<?php

namespace api\components;

use api\models\ApiCurrencies;
use Yii;

class ApiCurrenciesCom {

    public $decimal_places, $format, $format_id;

    public function __construct() {
        $this->format = array();
        $this->format_id = array();
        $this->decimal_places = null;

        $c_data = ApiCurrencies::find()->orderBy('title, code')->all();
        if (!empty($c_data)) {
            foreach ($c_data as $_cur) {
                // full currencies format
                $this->format[$_cur->code] = [
                    'symbol_left' => $_cur->symbol_left,
                    'symbol_right' => $_cur->symbol_right,
                    'decimal_point' => $_cur->decimal_point,
                    'thousands_point' => $_cur->thousands_point,
                    'decimal_places' => $_cur->decimal_places,
                    'value' => $_cur->value,
                    'buy_value' => $_cur->buy_value,
                    'sell_value' => $_cur->sell_value,
                ];
                $this->format_id[$_cur->currencies_id] = $_cur->code;
            }
        }
    }

    public function format($number, $calculate_cur_value = true, $cur_type = '', $cur_value = '', $type = 'sell') {
        $format_string = '';

        if (!empty($cur_type) && isset($this->format[$cur_type])) {
            if ($calculate_cur_value == true) {
                $_eur_cur = array('DEM', 'BEF', 'LUF', 'ESP', 'FRF', 'IEP', 'ITL', 'NLG', 'ATS', 'PTE', 'FIM', 'GRD');

                if ($cur_value != '') {
                    $rate = $cur_value;
                } else {
                    $rate = (($type != '') ? $this->format[$cur_type][$type . '_value'] : $this->format[$cur_type]['value']);
                }

                if (is_null($this->decimal_places)) {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number * $rate, $this->format[$cur_type]['decimal_places'], '.', ''), $this->format[$cur_type]['decimal_places'], $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                } else {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number * $rate, $this->decimal_places, '.', ''), $this->decimal_places, $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                }

                if ((Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"] == 'EUR') && in_array($cur_type, $_eur_cur)) {
                    $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
                }
            } else {
                if (is_null($this->decimal_places)) {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number, $this->format[$cur_type]['decimal_places'], '.', ''), $this->format[$cur_type]['decimal_places'], $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                } else {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number, $this->decimal_places, '.', ''), $this->decimal_places, $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                }
            }
        } else {
            $format_string = $number;
        }

        return $format_string;
    }

}