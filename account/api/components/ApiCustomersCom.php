<?php

namespace api\components;

use api\models\ApiAddressBook;
use api\models\ApiCustomers;
use api\models\ApiCustomersGroups;
use api\models\ApiInstantMessageAccounts;
use api\models\ApiZones;
use Yii;

class ApiCustomersCom {

    private static $form = array(
        "billing",
        "seller_register"
    );
    private static $user_info = array(
        "customers_email_address",
        "customers_firstname",
        "customers_lastname",
        "customers_telephone",
        "customers_country_dialing_code_id",
        "customers_fax",
        "customers_groups_id",
        "customers_default_address_id"
    );
    private static $user_addr = array(
        "entry_street_address",
        "entry_suburb",
        "entry_city",
        "entry_state",
        "entry_postcode",
        "entry_country_id",
        "entry_zone_id"
    );

    public static function encryptPassword($plain) {
        $password = '';

        for ($i = 0; $i < 10; $i++) {
            $password .= ApiGeneralCom::cpnRand();
        }

        $salt = substr(md5($password), 0, 2);
        $password = md5($salt . $plain) . ':' . $salt;

        return $password;
    }

    public static function form($customer_id, $country, $form) {
        $result = array();

        if (in_array($form, self::$form)) {
            $dialing_code_id = 0;
            $mobile_phone_number = '';
            $mobile_section_parameters = '';
            $first_name = '';
            $last_name = '';
            $customers_default_address_id = 0;

            $billing_address1 = '';
            $billing_address2 = '';
            $billing_city = '';
            $billing_state = '';
            $billing_zone_id = 0;
            $billing_zip = '';
            $billing_country = 0;

            $cust_arr = self::profile($customer_id);

            if (isset($cust_arr['customer'])) {
                $dialing_code_id = $cust_arr['customer']['customers_country_dialing_code_id'];
                $mobile_phone_number = $cust_arr['customer']['customers_telephone'];
                $first_name = $cust_arr['customer']['customers_firstname'];
                $last_name = $cust_arr['customer']['customers_lastname'];
                $customers_default_address_id = $cust_arr['customer']['customers_default_address_id'];

                if ($mobile_phone_number === '****') {
                    $mobile_phone_number = '';
                } else {
                    $mobile_section_parameters = 'disabled';
                }
            }

            if (isset($cust_arr['address_book'])) {
                $billing_address1 = $cust_arr['address_book']['entry_street_address'];
                $billing_address2 = $cust_arr['address_book']['entry_suburb'];
                $billing_city = $cust_arr['address_book']['entry_city'];
                $billing_zip = $cust_arr['address_book']['entry_postcode'];
                $billing_country = !empty($cust_arr['address_book']['entry_country_id']) ? $cust_arr['address_book']['entry_country_id'] : $country;
                $billing_state = $cust_arr['address_book']['entry_state'];
                $billing_zone_id = $cust_arr['address_book']['entry_zone_id'];
            }

            $total_state = ApiZones::find()->where(['zone_country_id' => $billing_country])->count();
            if ($total_state > 0) {
                $state_element = array('type' => 'select', 'name' => 'billing_state', 'value' => $billing_zone_id, 'options' => array('id' => "billing_state", 'class' => "ezInputField"));
            } else {
                $state_element = array('type' => 'text', 'name' => 'billing_state', 'value' => $billing_state);
            }

            switch ($form) {
                case "billing":
                    $result = array(
                        'tag' => array(
                            'name' => array(
                                'label' => Yii::t('apiModule.customer', 'TEXT_NAME') . '<span class="required"> * </span>',
                                'group' => array(
                                    array('type' => 'text', 'name' => 'first_name', 'value' => $first_name,  'options' => array('disabled' => true)),
                                    array('type' => 'text', 'name' => 'last_name', 'value' => $last_name,  'options' => array('disabled' => true))
                                )
                            ),
                            'billing_address1' => array('type' => 'text', 'name' => 'billing_address1', 'value' => $billing_address1, 'label' => Yii::t('apiModule.customer', 'TEXT_BILLING_ADDRESS') . '<span class="required"> * </span>'),
                            'billing_address2' => array('type' => 'text', 'name' => 'billing_address2', 'value' => $billing_address2, 'label' => Yii::t('apiModule.customer', 'TEXT_BILLING_ADDRESS') . '<span class="required"> * </span>'),
                            'billing_city' => array('type' => 'text', 'name' => 'billing_city', 'value' => $billing_city, 'label' => Yii::t('apiModule.customer', 'TEXT_CITY') . '<span class="required"> * </span>'),
                            'billing_country' => array(
                                'type' => 'select',
                                'name' => 'billing_country',
                                'value' => $billing_country,
                                'label' => Yii::t('apiModule.customer', 'TEXT_COUNTRY') . '<span class="required"> * </span>',
                                'options' => array(
                                    'onchange' => 'refreshStateList(this, "billing_state", true)',
                                    'disabled' => true,
                                ),
                            ),
                            'billing_state' => array_merge($state_element, array('label' => Yii::t('apiModule.customer', 'TEXT_STATE') . '<span class="required"> * </span>')),
                            'billing_zip' => array(
                                'type' => 'text',
                                'name' => 'billing_zip',
                                'value' => $billing_zip,
                                'label' => Yii::t('apiModule.customer', 'TEXT_ZIP') . '<span class="required"> * </span>',
                            ),
                            'dialing_code_id' => array('type' => 'select', 'name' => 'dialing_code_id', 'value' => $dialing_code_id, 'label' => Yii::t('apiModule.customer', 'TEXT_COUNTRY_DIALING_CODE_ID'), 'options' => array('disabled' => $mobile_section_parameters)),
                            'mobile_phone_number' => array('type' => 'text', 'name' => 'mobile_phone_number', 'value' => $mobile_phone_number, 'label' => Yii::t('apiModule.customer', 'ENTRY_TELEPHONE_NUMBER'), 'options' => array('disabled' => $mobile_section_parameters)),
                        ),
                        'data' => array(
                            'billing_country' => $billing_country,
                            'cid' => $customer_id,
                            //TODO Deprecated, may remove soon
                            'update_phone_desc' => Yii::t('verifyPhone', 'MSG_ENSURE_PHONE_NUMBER_WITH_EDIT_LINK'),
                        )
                    );
                    break;

                case "seller_register":
                    $result = array(
                        'tag' => array(
                            'dialing_code_id' => array('type' => 'select', 'name' => 'dialing_code_id', 'value' => $dialing_code_id, 'label' => Yii::t('apiModule.customer', 'TEXT_COUNTRY_DIALING_CODE_ID') . '<span class="required"> * </span>', 'options' => array('disabled' => $mobile_section_parameters)),
                            'mobile_phone_number' => array('type' => 'text', 'name' => 'mobile_phone_number', 'value' => $mobile_phone_number, 'label' => Yii::t('apiModule.customer', 'ENTRY_TELEPHONE_NUMBER') . '<span class="required"> * </span>', 'options' => array('disabled' => $mobile_section_parameters)),
                            'instant_messenger_list' => array(
                                'type' => 'select',
                                'name' => 'instant_messenger_list',
                                'value' => '',
                                'label' => Yii::t('apiModule.customer', 'TEXT_INSTANT_MESSENGER') . '<span class="required"> * </span>',
                                'options' => array(
                                    'onchange' => 'js:addIm(this.value)'
                                ),
                            ),
                            'instant_messenger' => array(
                                'type' => 'im',
                                'name' => 'instant_messenger',
                                'value' => $cust_arr['instant_message'],
                                'label' => ''
                            ),
                        ),
                        'data' => array(
                            'cid' => $customer_id,
                            //TODO Deprecated, may remove soon
                            'update_phone_desc' => \yii\helpers\Html::a(Yii::t('profile', 'LINK_CHANGE_PHONE_NUMBER'), \Yii::$app->params['SECURE_OUB_MYACCOUNT'] . '/profile/security#change-phone', array('id' => 'link_edit_phone_number', 'target' => '_blank', 'class' => 'link_option'))
                        )
                    );
                    break;
            }
        }

        return $result;
    }

    public static function profile($cid) {
        $result = array();

        $m_ac = ApiCustomers::findOne(['customers_id' => $cid]);
        if (isset($m_ac->customers_id)) {
            $result["customer"] = array();
            $result["address_book"] = array();
            $result["instant_message"] = array();

            foreach (self::$user_info as $key) {
                switch ($key) {
                    case "customers_telephone":
                        $result["customer"][$key] = substr($m_ac->$key, 0, -4) . '****';
                        $result["customer"]["customers_telephone_raw"] = $m_ac->$key;
                        break;

                    case "customers_groups_id":
                        $result["customer"][$key] = $m_ac->$key;
                        $m_acg = ApiCustomersGroups::findOne(['customers_groups_id' => $m_ac->$key]);
                        if (isset($m_acg->customers_groups_id)) {
                            $result["customer"]["customers_groups_name"] = $m_acg->customers_groups_name;
                        }
                        break;

                    default:
                        $result["customer"][$key] = $m_ac->$key;
                        break;
                }
            }

            // address book
            if ($m_ac->customers_default_address_id) {
                $m_aab = ApiAddressBook::findOne(['address_book_id' => $m_ac->customers_default_address_id]);
                if (isset($m_aab->address_book_id)) {

                    foreach (self::$user_addr as $key) {
                        switch ($key) {
                            case "entry_zone_id":
                                $result["address_book"][$key] = $m_aab->$key;
                                $result["address_book"]["entry_state"] = ($m_aab->$key ? $m_aab->$key : $m_aab->entry_state);
                                break;

                            default:
                                $result["address_book"][$key] = ApiGeneralCom::removeInvalidChars($m_aab->$key);
                                break;
                        }
                    }
                    $result['address_book']['entry_state_actual'] = $m_aab->entry_state;
                    if (empty($result['address_book']['entry_state_actual']) && !empty($result['address_book']['entry_zone_id'])) {
                        $m_zone = ApiZones::find()->where(['zone_id' => $result['address_book']['entry_zone_id']])->one();
                        if ($m_zone) {
                            $result['address_book']['entry_state_actual'] = $m_zone->zone_name;
                        }
                    }
                }
            }

            // instant message
            $m_ima = ApiInstantMessageAccounts::find()->joinWith('instantMessageType')->where(["customer_id" => $m_ac->customers_id])->orderBy('instant_message_accounts_id')->all();
            if (count($m_ima)) {
                foreach ($m_ima as $m_data) {
                    $result["instant_message"][] = array(
                        'account_id' => $m_data->instant_message_accounts_id,
                        'type_id' => $m_data->instant_message_type_id,
                        'user_id' => $m_data->instant_message_userid,
                        'name' => isset($m_data->instantMessageType->instant_message_type_name) ? $m_data->instantMessageType->instant_message_type_name : '',
                    );
                }
            }
        }

        return $result;
    }

}
