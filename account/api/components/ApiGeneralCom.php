<?php

namespace api\components;

use common\components\EmailComponent;
use common\components\GeneralComponent;
use Exception;
use Yii;
use yii\helpers\Json;

class ApiGeneralCom {

    public $purifier;

    public function _isSerialize($data) {
        return (@unserialize($data) != NULL) ? true : false;
    }

    public function _isJson($data) {
        try {
            return (Json::decode($data) != NULL);
        } catch (Exception $ex) {
            return false;
        }
    }

    public function _purify($data) {
        return GeneralComponent::purify($data);
    }

    public function _readInput() {
        $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : $_GET;

        if (isset($_SERVER['CONTENT_TYPE'])) {
            if ($_SERVER['CONTENT_TYPE'] == 'application/json') {
                $_data = file_get_contents("php://input");
            } else {
                // multipar/form-data with boundry
                $_data = $_POST + $_FILES;
            }
        }

        if (!empty($_data)) {
            if ($this->_isJson($_data)) {
                $_data = Json::decode($_data);
            } else if ($this->_isSerialize($_data)) {
                $_data = unserialize($_data);
            } else if (is_object($_data)) {
                $_data = (array) $_data;
            }
        }

        return $this->_purify($_data);
    }

    public function _sendResponse($result) {
        header("Cache-Control: no-store");
        header("Access-Control-Allow-Origin: *");
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        Yii::$app->response->data = $this->removeInvalidCharsFromArray($result);
        Yii::$app->end();
    }

    public function _validateSignatureWebpush($actionId, $input) {
        switch (strtolower($actionId)) {
            case 'view':
            case 'update':
            case 'delete':
                $signParam = 'id';
                break;
            default:
                $signParam = 'time';
                break;
        }
           
        if (isset($input[$signParam]) && isset($input['merchant']) && isset($input['signature'])) {
            if (isset(Yii::$app->params['signature'][$input['merchant']])) {
                if ($input['signature'] == md5($input['merchant'] . $input[$signParam] . "|" . Yii::$app->params['signature'][$input['merchant']])) {
                    return true;
                }
            }
        }

        $this->_errorReport('Invalid signature');
        return false;
    }

    public function _validateSignature($input) {
        if (isset($input['cid']) && isset($input['merchant']) && isset($input['signature'])) {
            if (isset(Yii::$app->params['signature'][$input['merchant']]) && ($input['signature'] == md5($input['cid'] . "|" . Yii::$app->params['signature'][$input['merchant']]))) {
                return true;
            }
        }

        $this->_errorReport('Invalid signature');
        return false;
    }

    public function _errorReport($error) {
        $message = "API Response : " . $error . "<br />";
        $message .= "Request URI : " . getenv('REQUEST_URI') . "<br />";
        $message .= "Request Variables : ";

        $_data = $this->_readInput();
        if (!empty($_data)) {
            if (is_array($_data)) {
                foreach ($_data as $key => $val) {
                    switch ($key) {
                        case "password":
                        case "signature":
                            $_data[$key] = "- confidential -";
                            break;
                    }
                }
            }
            ob_start();
            print "<pre>";
            var_dump($_data);
            print "</pre>";
            $message .= ob_get_contents();
            ob_clean();
        } else {
            $message .= "- empty -";
        }

        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: ' . Yii::$app->name . ' REPORTER <<EMAIL>>' . "\r\n";
        $subject = Yii::$app->name . ' API Error from ' . (!empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'unknown') . ' - ' . date("F j, Y H:i");

        EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $message, $headers);
    }

    public static function createRandomValue($length, $type = 'mixed') {
        if (($type != 'mixed') && ($type != 'chars') && ($type != 'digits'))
            return false;

        $rand_value = '';
        while (strlen($rand_value) < $length) {
            if ($type == 'digits') {
                $char = self::cpnRand(0, 9);
            } else {
                $char = chr(self::cpnRand(0, 255));
            }

            if ($type == 'mixed') {
                if (preg_match('/^[a-z0-9]$/i', $char))
                    $rand_value .= $char;
            } else if ($type == 'chars') {
                if (preg_match('/^[a-z]$/i', $char))
                    $rand_value .= $char;
            } else if ($type == 'digits') {
                if (preg_match('/^[0-9]$/', $char))
                    $rand_value .= $char;
            }
        }

        return $rand_value;
    }

    public static function cpnRand($min = null, $max = null) {
        static $seeded;

        if (!isset($seeded)) {
            mt_srand((double) microtime() * 1000000);
            $seeded = true;
        }

        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                return $min;
            } else {
                return mt_rand($min, $max);
            }
        } else {
            return mt_rand();
        }
    }

    public static function encrypt($string, $key) {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result.=$char;
        }

        return base64_encode($result);
    }

    public static function removeInvalidChars($text) {
        $regex = '/( [\x00-\x7F] | [\xC0-\xDF][\x80-\xBF] | [\xE0-\xEF][\x80-\xBF]{2} | [\xF0-\xF7][\x80-\xBF]{3} ) | ./x';
        return preg_replace($regex, '$1', $text);
    }

    public static function removeInvalidCharsFromArray($array) {
        foreach($array as &$data) {
            if (is_array($data)) {
                $data = self::removeInvalidCharsFromArray($data);
            } else if (is_string($data)) {
                $data = self::removeInvalidChars($data);
            }
        }
        return $array;
    }

}
