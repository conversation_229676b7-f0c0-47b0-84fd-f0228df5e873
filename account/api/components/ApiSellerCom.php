<?php

namespace api\components;

use common\components\LogFilesComponent;
use common\components\PhoneVerificationComponent;
use common\models\AddressBook;
use common\models\Configuration;
use common\models\Customers;
use common\models\CustomersInfo;
use common\models\InstantMessageAccounts;
use common\models\InstantMessageType;
use Yii;

class ApiSellerCom {

    public static function editAccount($customer_id, $data) {
        $classLogFilesComponent = new LogFilesComponent($customer_id);

        $modelAddressBook = new AddressBook();
        $modelCustomers = new Customers();
        $modelCustomersInfo = new CustomersInfo();

        $error = FALSE;
        $error_array = array();
        $instant_messegner_data = isset($data['newListValue']) ? $data['newListValue'] : '';
        $dialing_code_id = isset($data['dialing_code_id']) ? (int) $data['dialing_code_id'] : 0;
        $mobile_phone_number = isset($data['mobile_phone_number']) ? $data['mobile_phone_number'] : '';

        $billing_zone_id = 0;
        $update_mobile_number = FALSE;
        $customers_default_address_id = 0;
        $allCustomersInfoChangesMade = '';

        $oldLogInfo = $modelCustomers->getCustomerCurrentInfo($customer_id);

        if (!is_null($oldLogInfo)) {
            $customers_default_address_id = $oldLogInfo['customers_default_address_id'];
            $allCustomersInfoChangesMade = $oldLogInfo['customers_info_changes_made'];

            if (empty($oldLogInfo['customers_telephone'])) {
                $update_mobile_number = TRUE;
            } else if ($dialing_code_id === 0) {
                $dialing_code_id = $oldLogInfo['customers_country_dialing_code_id'];
                $mobile_phone_number = $oldLogInfo['customers_telephone'];
            } else {
                $update_mobile_number = TRUE;
            }
        }

        //check instant messenger
        $IMarray = array();
        if (empty($instant_messegner_data)) {
            $error = TRUE;
            $error_array['instant_messenger_list'][] = Yii::t('profile', 'ERROR_INSTANT_MESSENGER');
        } else {
            $totalIm = 0;

            $IMTypeData = InstantMessageType::find()->all();
            $IMTypeList = \yii\helpers\ArrayHelper::map($IMTypeData, 'instant_message_type_id', 'instant_message_type_id');

            foreach ($instant_messegner_data as $imValueKey => $imValue) {
                if ($imValueKey == "new") {
                    foreach ($imValue as $imValueNewKey => $imValueNew) {
                        foreach ($imValueNew as $imValueNewValueKey => $imValueNewValue) {
                            $imValueNewValue_p = trim($imValueNewValue);
                            $imValueNewValueKey = (int) $imValueNewValueKey;
                            if ($imValueNewValue_p && $imValueNewValueKey) {
                                if (in_array($imValueNewValueKey, $IMTypeList)) {
                                    $IMarray['new'][$imValueNewKey][$imValueNewValueKey] = $imValueNewValue_p;
                                    $totalIm++;
                                } else {
                                    $error = TRUE;
                                    $error_array['instant_messenger'][] = Yii::t('profile', 'ERROR_INSTANT_MESSENGER_INVALID_TYPE');
                                }
                            } else {
                                $error = TRUE;
                                $error_array['instant_messenger'][] = Yii::t('profile', 'ERROR_INSTANT_MESSENGER_VALUE_EMPTY');
                                break;
                            }
                        }
                        if ($error === TRUE) {
                            break;
                        }
                    }
                } else {
                    $newIMValue = trim($imValue);
                    $imValueKey = (int) $imValueKey;
                    if ($newIMValue && $imValueKey) {
                        $IMarray[$imValueKey] = $newIMValue;
                        $totalIm++;
                    } else {
                        $error = TRUE;
                        $error_array['instant_messenger'][] = Yii::t('profile', 'ERROR_INSTANT_MESSENGER_VALUE_EMPTY');
                        break;
                    }
                }
            }
            if ($error !== TRUE) {
                $maxIm = Configuration::model()->getConfigValue('ENTRY_IM_ACCOUNT_MAX_ENTRIES');
                if ($totalIm > $maxIm) {
                    $error_array['instant_messenger_list'][] = Yii::t('profile', 'ERROR_INSTANT_MESSENGER_MAXED', array('SYS_MIN_LENGTH' => $maxIm));
                }
            }
        }
        //end check instant messenger

        // customer contact number is empty
        if ($update_mobile_number === TRUE) {
            $phone_result = PhoneVerificationComponent::verifyNumberDialCode($dialing_code_id, $mobile_phone_number, $customer_id);
            if ($phone_result["status"]) {
                $mobile_phone_number = $phone_result["result"];
            } else {
                $error = TRUE;
                if ($phone_result["error"]["phone"]) {
                    $error_array['mobile_phone_number'][] = $phone_result["error"]["phone"];
                } else {
                    $error_array['dialing_code_id'][] = $phone_result["error"]["dial"];
                }
            }
        }

        if ($error !== TRUE) {
            // Update Profile
            if ($update_mobile_number === TRUE) {
                $sql_data_array = array(
                    "customers_country_dialing_code_id" => $dialing_code_id,
                    "customers_telephone" => $mobile_phone_number,
                );
                $modelCustomers->updateCustomerInfo($sql_data_array, $customer_id);
            }
            
            $modelCustomersInfo->updateLastModify($customer_id);
            $newLogInfo = $modelCustomers->getCustomerCurrentInfo($customer_id);
            $customerChangesArray = $classLogFilesComponent->detectChanges($oldLogInfo, $newLogInfo);
            $customerChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerChangesArray);

            $allCustomersInfoChangesMade = $classLogFilesComponent->contructChangesString($customerChangesArray, $allCustomersInfoChangesMade);

            if (count($customerChangesFormattedArray)) {
                $changesStr = 'Changes made:' . "\n";

                for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                    if (count($customerChangesFormattedArray[$i])) {
                        foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $changesStr .= $res['text'] . "\n";
                            } else {
                                $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }

                $classLogFilesComponent->insertCustomerHistoryLog($newLogInfo['customers_email_address'], $changesStr);
            }

            $modelCustomersInfo->updateChangeMade($allCustomersInfoChangesMade);

            //update instant messenger
            $instant_messenger_obj = InstantMessageAccounts::findAll(['customer_id' => $customer_id]);
            if (!is_null($instant_messenger_obj)) {
                $return_array = array();
                foreach ($instant_messenger_obj as $im_objKey => $im_objValue) {
                    if (isset($IMarray[$im_objValue->instant_message_accounts_id])) {
                        //update
                        $updateData = array(
                            'instant_message_userid' => $IMarray[$im_objValue->instant_message_accounts_id]
                        );
                        InstantMessageAccounts::updateAll($updateData, ['instant_message_accounts_id' => $im_objValue->instant_message_accounts_id]);
                        unset($IMarray[$im_objValue->instant_message_accounts_id]);
                    } else {
                        if (isset($IMarray['new']) && $IMarray['new']) {
                            foreach ($IMarray['new'] as $IMarrayNewKey => $IMarrayNewValue) {
                                foreach ($IMarrayNewValue as $imValueNewValueKey1 => $imValueNewValue1) {
                                    $updateData = array(
                                        'instant_message_type_id' => $imValueNewValueKey1,
                                        'instant_message_userid' => $imValueNewValue1,
                                        'instant_message_remarks' => ''
                                    );
                                    InstantMessageAccounts::updateAll($updateData, ['instant_message_accounts_id' => $im_objValue->instant_message_accounts_id]);
                                }
                                unset($IMarray['new'][$IMarrayNewKey]);
                                break;
                            }
                        } else {
                            //delete
                            $im_objValue->delete();
                        }
                    }
                }
            }

            if (isset($IMarray['new'])) {
                //add new
                foreach ($IMarray['new'] as $IMarrayNewKey => $IMarrayNewValue) {
                    foreach ($IMarrayNewValue as $imValueNewValueKey1 => $imValueNewValue1) {
                        $insertData = array(
                            'customer_id' => $customer_id,
                            'instant_message_type_id' => $imValueNewValueKey1,
                            'instant_message_userid' => $imValueNewValue1
                        );
                        InstantMessageAccounts::model()->saveNewRecord($insertData);
                    }
                }
            }
            //end update instant messenger

            $return_bool = TRUE;
            $customer_row = Customers::findOne(['customers_id' => $customer_id]);

            if (!is_null($customer_row)) {
                $dial_country_id = $customer_row->customers_country_dialing_code_id;
                $mobile_phone_number = $customer_row->customers_telephone;
                $customers_default_address_id = $customer_row->customers_default_address_id;

                if (empty($dial_country_id) || empty($customer_row->customers_telephone)) {
                    $return_bool = 1;
                }
            } else {
                $return_bool = -1;
            }

            $error_array = $return_bool;
        }

        return array(
            'error' => $error,
            'error_array' => $error_array,
            'customers_country_dialing_code_id' => $dialing_code_id,
            'customers_telephone' => $mobile_phone_number
        );
    }

}