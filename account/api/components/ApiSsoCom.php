<?php

namespace api\components;

use api\models\ApiUserToken;
use common\components\GeneralComponent;

class ApiSsoCom {

    public static $otp_key_api_register = "api_register";
    public static $otp_key_device = "device_pin";
    public static $otp_key_password = "reset_password";

    public static function secureCodeDelete($otp_key, $cid = "") {
        ApiUserToken::deleteAll("token_type = :key AND expiry_date < :time", [":key" => $otp_key, ":time" => date("Y-m-d H:i:s")]);

        if (!empty($cid)) {
            ApiUserToken::deleteAll(["user_id" => $cid, "token_key" => $otp_key]);
        }
    }

    public static function secureCodeRead($otp_key, $cid) {
        $result = array();

        $m_ut = ApiUserToken::findOne(["user_id" => $cid, "token_type" => $otp_key]);
        if (isset($m_ut->user_id)) {
            switch ($otp_key) {
                case self::$otp_key_device:
                    $m_ut->expiry_date = date("Y-m-d H:i:s", time() + (24 * 60 * 60));    // extend 24hr
                    $m_ut->save();
                    break;
            }

            $result = array(
                "otp_pin" => $m_ut->token_value,
                "create_date" => $m_ut->created_date,
                "expiry_date" => $m_ut->expiry_date
            );
        } else {
            switch ($otp_key) {
                case self::$otp_key_device:
                    self::secureCodeRequest($otp_key, $cid, (24 * 60 * 60));    // valid 24hr
                    $m_ut = ApiUserToken::findOne(["user_id" => $cid, "token_type" => $otp_key]);
                    if (isset($m_ut->user_id)) {
                        $result = array(
                            "otp_pin" => $m_ut->token_value,
                            "create_date" => $m_ut->created_date,
                            "expiry_date" => $m_ut->expiry_date
                        );
                    }
                    break;
            }
        }

        return $result;
    }

    public static function secureCodeRequest($otp_key, $cid, $lifetime = 0) {
        self::secureCodeDelete($otp_key);

        $m_co = ApiUserToken::findOne(["user_id" => $cid, "token_type" => $otp_key]);
        if (!isset($m_co->user_id)) {
            $created = time();

            switch ($otp_key) {
                case self::$otp_key_api_register:
                    $otp_pin = GeneralComponent::createRandomValue(8);
                    $expiry = $created + ($lifetime ? $lifetime : 10);    // valid 10sec
                    break;

                case self::$otp_key_password:
                    $otp_pin = ApiGeneralCom::encrypt($cid, $created);
                    $expiry = $created + ($lifetime ? $lifetime : (24 * 60 * 60)); // valid 24hr
                    break;

                default:
                    $otp_pin = ApiGeneralCom::createRandomValue(6, 'digits');
                    $expiry = $created + ($lifetime ? $lifetime : (15 * 60));    // valid 15min
                    break;
            }

            $m_attr = array(
                'user_id' => $cid,
                'token_type' => $otp_key,
                'token_value' => $otp_pin,
                'created_date' => date("Y-m-d H:i:s", $created),
                'expiry_date' => date("Y-m-d H:i:s", $expiry)
            );
            ApiUserToken::model()->saveNewRecord($m_attr);
        } else {
            $otp_pin = $m_co->token_value;
        }

        return $otp_pin;
    }
    
    public static function disableTwoFactorAuthentication($cid) {
        \common\components\TwoFactorAuth::removeUserTwoFactorAuth($cid);
    }

    public static function doesCustomerHaveTwoFactorAuthentication($cid) {
        return \common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($cid);
    }

    public static function validateTwoFactorToken($cid, $token) {
        $secret = \common\components\TwoFactorAuth::getUserTwoFactorAuthSecret($cid);
        if (empty($secret)) {
            return [
                'error' => 1,
                'text' => 'Invalid Customer ID',
            ];
        }
        if (\common\components\TwoFactorAuth::getInstance()->verifyCode($secret, $token)) {
            return [
                'error' => 0,
                'text' => '',
            ];
        } else {
            return [
                'error' => 2,
                'text' => \Yii::t("general", "ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH"),
            ];
        }
    }
}