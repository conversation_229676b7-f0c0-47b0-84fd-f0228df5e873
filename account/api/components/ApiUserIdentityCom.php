<?php

namespace api\components;

use api\models\ApiAddressBook;
use api\models\ApiCustomers;
use api\models\ApiCustomersInfo;
use Yii;
use yii\db\Expression;

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class ApiUserIdentityCom {

    public static function register($_input = array()) {
        /*
         * email
         * passwd
         * fname
         * lname
         * news
         * ip
         * ip_country_iso2
         */
        $result = false;
        $mail = strtolower($_input["email"]);

        if (!ApiCustomers::find()->where(['customers_email_address' => $mail])->exists()) {
            $fname = (isset($_input['fname']) ? $_input['fname'] : substr($_input["email"], 0, strpos($_input["email"], "@")));
            $lname = (isset($_input['lname']) ? $_input['lname'] : $fname);
            $news = (isset($_input['news']) ? 17 : "");

            $m_attr = array(
                'customers_email_address' => $mail,
                'customers_firstname' => $fname,
                'customers_lastname' => $lname,
                'customers_password' => ApiCustomersCom::encryptPassword($_input['passwd']),
                'customers_newsletter' => $news,
            );
            $cid = ApiCustomers::model()->saveNewRecord($m_attr, true);
            unset($m_attr);

            if ($cid) {
                $m_attr = array(
                    'customers_id' => $cid,
                    'entry_firstname' => $fname,
                    'entry_lastname' => $lname
                );
                $address_id = ApiAddressBook::model()->saveNewRecord($m_attr, true);
                unset($m_attr);

                $m_attr = array('customers_default_address_id' => $address_id);
                ApiCustomers::updateAll($m_attr, ['customers_id' => $cid]);
                unset($m_attr);

                $m_attr = array(
                    'customers_info_id' => $cid,
                    'customers_info_date_account_created' => new Expression('NOW()'),
                    'customers_info_account_created_ip' => $_input["ip"],
                    'account_created_country' => (isset($_input["ip_country_iso2"]) ? $_input["ip_country_iso2"] : ""),
                    'customers_info_account_created_from' => 0,
                    'account_created_site' => (isset($_input["service"]) && !empty($_input["service"]) ? $_input["service"] : Yii::$app->name),
                    'customer_info_selected_language_id' => 1   // en
                );
                ApiCustomersInfo::model()->saveNewRecord($m_attr);
                unset($m_attr);

                $result = true;
            }
        }

        return $result;
    }

}