<?php

namespace api\components\webpush;

use api\models\ApiWebpushToken;
use api\models\ApiWebpushTopic;
use api\models\ApiWebpushUser;
use api\models\ApiWebpushClient;
use common\components\CurlComponent;
use Yii;
use yii\helpers\BaseJson;
use yii\helpers\ArrayHelper;

class ApiWebpushGeneralCom
{
	public $defaultTopic;

	public function __construct($merchantId)
    {
        if ($topic = ApiWebpushTopic::findOne(['topic_default' => 1, 'client_id' => $merchantId])) {
            $this->defaultTopic = $topic->code;
        } else {
            $this->defaultTopic = 'default';
        }
    }

    protected function setUserSubscription($userId, $merchantId, $topic, $action = 'merge')
    {
        // Set webpush user table
        if ($user = ApiWebpushUser::find()->where(['id' => $userId, 'client_id' => $merchantId])->one()) {
            $topicSubscriptions = BaseJson::decode($user->topic_subscriptions);
            $topicSubscriptions = array_unique(ArrayHelper::$action($topicSubscriptions, $topic));

            $user->topic_subscriptions = BaseJson::encode($topicSubscriptions);
            $user->updated_at = time();
            $user->update();

            return $topicSubscriptions;
        } else {
            // remove action does not need to add user
            if ($action == 'merge') {
                $user = new ApiWebpushUser;
                $user->id = $userId;
                $user->client_id = $merchantId;
                $user->topic_subscriptions = BaseJson::encode($topic);
                $user->created_at = time();
                $user->updated_at = time();
                $user->save();
            }
            // return false for new user
            return false;
        }
    }

	protected function setTokenTopic($topic, $token, $merchantId)
    {
        $api_url = Yii::$app->params['FIREBASE']['TOPIC_URL'] . '/' . $token . '/rel/topics/' . $topic;
        $merchant = ApiWebpushClient::find()->where(['id' => $merchantId])->one();

        $curl_obj = new CurlComponent();
        
        $curl_obj->request_headers = [
            'Authorization' => 'key='.$merchant->server_key,
        ];

        $data =[];
        
        $response = $curl_obj->curlPost($api_url, $data);

        $this->tokenCount($topic, $merchantId, 'add');
    }

    protected function unsetTokenTopic($topic, $token, $merchantId)
    {
        $api_url = Yii::$app->params['FIREBASE']['TOPIC_URL'] . '/' . $token . '/rel/topics/' . $topic;
        $merchant = ApiWebpushClient::find()->where(['id' => $merchantId])->one();

        $curl_obj = new CurlComponent();
        
        $curl_obj->request_headers = [
            'Authorization' => 'key='.$merchant->server_key,
        ];
        
        $data =[];
        
        $response = $curl_obj->curlDelete($api_url, $data);

        $this->tokenCount($topic, $merchantId, 'minus');
    }

    protected function tokenCount($topicCode, $merchantId, $operation)
    {
        if ($topic = ApiWebpushTopic::find()->where(['code' => $topicCode, 'client_id' => $merchantId])->one()) {
            switch ($operation) {
                case 'minus':
                    $topic->token_counts = $topic->token_counts - 1;
                    break;
                default:
                    $topic->token_counts = $topic->token_counts + 1;
                    break;
            }
            $topic->update();
        }
    }

    protected function resetTokenDefault($input, $userId)
    {
        $user = ApiWebpushUser::findOne($userId);
        if ($user) {
            $topicSubscriptions = BaseJson::decode($user->topic_subscriptions);
            foreach ($topicSubscriptions as $topic) {
                $this->unsetTokenTopic($topic, $input['token'], $input['merchant']);
            }
        }
    }

    protected function validateWebpushToken($token, $merchantId)
    {
        $api_url = Yii::$app->params['FIREBASE']['PUSH_URL'];
        $merchant = ApiWebpushClient::find()->where(['id' => $merchantId])->one();

        $curl_obj = new CurlComponent();
        
        $curl_obj->request_headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'key='.$merchant->server_key,
        ];

        $data = [
            'registration_ids' => [$token],
            'dry_run' => true,
        ];

        $data = BaseJson::encode($data);
        $response = $curl_obj->curlPost($api_url, $data);

        $result = BaseJson::decode($response);
        if (isset($result['results'][0]['error'])) {
            return false;
        }
        return true;
    }
}