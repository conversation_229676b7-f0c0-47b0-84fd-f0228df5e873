<?php

namespace api\components\webpush;

use api\models\ApiWebpushNotification;
use api\models\ApiWebpushSchedule;
use api\models\ApiWebpushApiLog;
use api\models\ApiWebpushTopic;
use api\models\ApiWebpushClient;
use common\components\CurlComponent;
use Aws\S3\Exception\S3Exception;
use Yii;
use yii\helpers\BaseJson;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;

class ApiWebpushNotificationCom extends \api\components\webpush\ApiWebpushGeneralCom
{
    public function __construct($merchantId)
    {
        parent::__construct($merchantId);
    }
    
    protected function setSchedule($pushId, $scheduleAt)
    {
        $schedule = new ApiWebpushSchedule;
        $schedule->push_id = $pushId;
        $schedule->schedule_at = $scheduleAt;
        $schedule->created_at = time();
        $schedule->save();
    }

    protected function updateSchedule($pushId, $data)
    {
        $schedule = ApiWebpushSchedule::find()->where(['push_id' => $pushId])->one();
        foreach ($data as $field => $value) {
            if ($schedule->hasAttribute($field)) {
                if (!isset($value)) {
                    unset($schedule->$field);
                } else {
                    $schedule->setAttribute($field, $value);
                }
            }
        }
        $schedule->update();
    }

    protected function deleteSchedule($pushId)
    {
        $schedule = ApiWebpushSchedule::find()->where(['push_id' => $pushId])->one();
        if ($schedule) {
            $schedule->delete();
        }
    }

    protected function sendNotification($pushId)
    {
        $api_url = Yii::$app->params['FIREBASE']['PUSH_URL'];
        $notification = ApiWebpushNotification::find()->where(['id' => $pushId])->one();
        $merchant = ApiWebpushClient::find()->where(['id' => $notification->client_id])->one();

        $curl_obj = new CurlComponent();
        
        $curl_obj->request_headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'key='.$merchant->server_key,
        ];

        $data = [
            'data' => [
                'notification' => [
                    'title' => $notification->title,
                    'body' => $notification->message,
                    'icon' => (isset($notification->icon) && !empty($notification->icon)) ? $this->getImageUrlFromS3($notification->icon) : '',
                    'image' => (isset($notification->image) && !empty($notification->image)) ? $this->getImageUrlFromS3($notification->image) : '',
                    'interaction' => ($notification->click_action === 0) ? false : true,
                    'click_action' => ($notification->url) ? $notification->url : 'https://www.offgamers.com/',
                ],
            ],
            'to' => ($notification->type == 0) ? '/topics/'. $notification->recipient : $notification->recipient,
        ];

        $data = BaseJson::encode($data);

        $apiLogObj = new ApiWebpushApiLog;
        $apiLogObj->saveApiLog($pushId, $merchant->id, $api_url, $data);
        
        $response = $curl_obj->curlPost($api_url, $data);

        $apiLogObj->updateApiLog($response);

        $result = BaseJson::decode($response);
        if (isset($result['results']['error'])) {
            return false;
        }
        return true;
    }

    public function getNotificationList($input)
    {
        $notificationArray = [];

        $startAt = $input['page_size'] * ($input['page'] - 1);

        $notificationQuery = ApiWebpushNotification::find()->where(['client_id' => $input['merchant']]);

        // Filter Options
        if (isset($input['title']) && !empty($input['title'])) { $notificationQuery->andWhere(['LIKE', 'title', $input['title']]); }
        if (isset($input['message']) && !empty($input['message'])) { $notificationQuery->andWhere(['LIKE', 'message', $input['message']]); }
        if (isset($input['send_status']) && is_numeric($input['send_status'])) { $notificationQuery->andWhere(['LIKE', 'send_status', $input['send_status']]); }

        if (isset($input['created_at']) && !empty($input['created_at'])) {
            $beginOfDay = strtotime("midnight", $input['created_at']);
            $endOfDay = strtotime("tomorrow", $beginOfDay) - 1;

            $notificationQuery->andWhere(['>=', 'created_at', $beginOfDay]);
            $notificationQuery->andWhere(['<=', 'created_at', $endOfDay]);
        }

        $notificationCount = $notificationQuery->count();

        $notificationQuery->orderBy(['created_at' => SORT_DESC])
            ->limit($input['page_size'])
            ->offset($startAt);

        $notificationList = $notificationQuery->all();

        return $notificationArray = [
            'total_notification' => $notificationCount,
            'page_size' => $input['page_size'],
            'page' => $input['page'],
            'notification' => $notificationList
        ];
    }

    public function getNotification($id)
    {
        $data = ApiWebpushNotification::find()->where(['id' => $id])->one();

        if ($data) {
            // Check icon data
            $data['icon'] = (isset($data['icon']) && !empty($data['icon'])) ? $this->getImageUrlFromS3($data['icon']) : '';
            // Check image data
            $data['image'] = (isset($data['image']) && !empty($data['image'])) ? $this->getImageUrlFromS3($data['image']) : '';

            return $data;
        } else {
            return [
                'error' => 1,
                'error_message' => 'Notification Not Found',
            ];
        }
    }

    public function saveNotification($input)
    {
        if (ApiWebpushTopic::find()->where(['code' => $input['recipient'], 'client_id' => $input['merchant']])->one()) {
            $time = time();

            $notification = new ApiWebpushNotification;
            $notification->type = $input['type'];
            $notification->recipient = $input['recipient'];
            $notification->title = $input['title'];
            $notification->message = $input['message'];
            $notification->url = $input['url'];
            $notification->click_action = $input['click_action'];
            $notification->client_id = $input['merchant'];
            $notification->created_at = $time;
            $notification->updated_at = $time;
            $notification->schedule_at = $input['schedule_at'];
            $notification->created_by = $input['created_by'];
            
            if ($notification->save()) {
                // Check for icon upload
                if (isset($input['icon']) && $input['icon']) {
                    $pathIcon = $input['icon']['name'];
                    $extIcon = pathinfo($pathIcon, PATHINFO_EXTENSION);
                    # Upload to S3
                    $nameIcon = '/webpush/icon/' . $notification->id . '_' . $time . '.' . $extIcon;
                    $uploadIcon = $this->uploadImageToS3($nameIcon, ['tempName' => $input['icon']['tmp_name'], 'type' => $input['icon']['type']]);

                    if ($uploadIcon['status']) {
                        $notification->icon = $nameIcon;
                        $notification->save();
                    } else {
                        $notification->icon = Yii::$app->params['API_MERCHANT_INFO'][$input['merchant']]['WEBPUSH_ICON'];
                        $notification->save();
                    }
                } else {
                    $notification->icon = Yii::$app->params['API_MERCHANT_INFO'][$input['merchant']]['WEBPUSH_ICON'];
                    $notification->save();
                }
                // Check for image upload
                if (isset($input['image']) && $input['image']) {
                    $pathImage = $input['image']['name'];
                    $extImage = pathinfo($pathImage, PATHINFO_EXTENSION);
                    // Upload to S3
                    $nameImage = '/webpush/image/' . $notification->id . '_' . $time . '.' . $extImage;
                    $uploadImage = $this->uploadImageToS3($nameImage, ['tempName' => $input['image']['tmp_name'], 'type' => $input['image']['type']]);

                    if ($uploadImage['status']) {
                        $notification->image = $nameImage;
                        $notification->save();
                    }
                }
            } else {
                return [
                    'error' => 3,
                    'error_message' => 'Notification Failed to Save',
                ];
            }

            // set schedule
            if (!empty($input['schedule_at']) && $input['schedule_at'] > $time) {
                $this->setSchedule($notification->id, $input['schedule_at']);
            }

            return $notification->id;
        } else {
            return [
                'error' => 2,
                'error_message' => 'Recipient Not Exist',
            ];
        }
    }

    public function updateNotification($input)
    {
        $notification = ApiWebpushNotification::findOne($input['id']);
        $time = time();
        if ($notification) {
            // checking if notification has been sent
            if ($notification->send_status == 1) {
                return [
                    'error' => 6,
                    'error_message' => 'Notification Failed to Update - Notification Has Been Sent',
                ];
            }
            
            // Check for notification status update onyl
            if (isset($input['notification_status']) && $input['notification_status'] == 1) {
                $notification->notification_status = $input['notification_status'];
            } else {
                $notification->recipient = $input['recipient'];
                $notification->title = $input['title'];
                $notification->message = $input['message'];
                $notification->url = $input['url'];
                $notification->click_action = $input['click_action'];
                $notification->notification_status = 0;

                // Check & update schedule if exist?
                if (!empty($input['schedule_at']) && $input['schedule_at'] > $time && empty($notification->schedule_at)) {
                    $notification->schedule_at = $input['schedule_at'];
                    $this->setSchedule($notification->id, $input['schedule_at']);
                } elseif (!empty($input['schedule_at']) && $input['schedule_at'] > $time) {
                    $notification->schedule_at = $input['schedule_at'];
                    $this->updateSchedule($notification->id, ['schedule_at' => $input['schedule_at'], 'updated_at' => $time]);
                } else {
                    $notification->schedule_at = NULL;
                    $this->deleteSchedule($notification->id);
                }
            }

            $notification->updated_at = $time;
            $notification->created_by = $input['created_by'];

            if ($notification->update()) {
                // Check for notification status update onyl skip this
                if (!isset($input['notification_status'])) {
                    // Check update for icon
                    if (isset($input['icon']) && $input['icon']) {
                        $pathIcon = $input['icon']['name'];
                        $extIcon = pathinfo($pathIcon, PATHINFO_EXTENSION);
                        $oldFileIcon = $notification->icon;
                        # Upload to S3
                        $nameIcon = '/webpush/icon/' . $notification->id . '_' . $time . '.' . $extIcon;
                        $uploadIcon = $this->uploadImageToS3($nameIcon, ['tempName' => $input['icon']['tmp_name'], 'type' => $input['icon']['type']]);

                        if ($uploadIcon['status']) {
                            # Delete old image                               
                            $this->deleteImageFromS3($oldFileIcon);
                            $notification->icon = $nameIcon;
                            $notification->save();
                        } else {
                            $notification->icon = Yii::$app->params['API_MERCHANT_INFO'][$input['merchant']]['WEBPUSH_ICON'];
                            $notification->save();
                        }
                    } else {
                        $notification->icon = Yii::$app->params['API_MERCHANT_INFO'][$input['merchant']]['WEBPUSH_ICON'];
                        $notification->save();
                    }
                    // Check update for image
                    if (isset($input['image']) && $input['image']) {
                        $pathImage = $input['image']['name'];
                        $extImage = pathinfo($pathImage, PATHINFO_EXTENSION);
                        $oldFileImage = $notification->icon;
                        // Upload to S3
                        $nameImage = '/webpush/image/' . $notification->id . '_' . $time . '.' . $extImage;
                        $uploadImage = $this->uploadImageToS3($nameImage, ['tempName' => $input['image']['tmp_name'], 'type' => $input['image']['type']]);

                        if ($uploadImage['status']) {
                            // Delete old image
                            $this->deleteImageFromS3($oldFileImage);
                            $notification->image = $nameImage;
                            $notification->save();
                        }
                    }
                }

                // check schedule exist?
                if (!empty($notification->schedule_at) && $notification->schedule_at < $time && $notification->notification_status == 1) {
                    // Send notification
                    if ($this->sendNotification($notification->id)) {
                        // Delete schedule
                        $this->deleteSchedule($notification->id);
                        $notification->send_status = 1;
                    } else {
                        $notification->send_status = 2;
                    }
                    $notification->executed_at = $time;
                    
                } elseif (empty($notification->schedule_at) && $notification->notification_status == 1) {
                    if ($this->sendNotification($notification->id)) {
                        $notification->send_status = 1;   
                    } else {
                        $notification->send_status = 2;
                    }
                    $notification->executed_at = $time;
                }
                
                $notification->update();

                return $notification->id;
            } else {
                return [
                    'error' => 4,
                    'error_message' => 'Notification Failed to Update',
                ];
            }
        } else {
            return [
                'error' => 1,
                'error_message' => 'Notification Not Found',
            ];
        }
    }

    public function deleteNotification($input)
    {
        $notification = ApiWebpushNotification::findOne($input['id']);

        if ($notification) {
            // checking if notification has been sent
            if ($notification->send_status == 1) {
                return [
                    'error' => 6,
                    'error_message' => 'Notification Has Been Sent',
                ];
            }

            $oldFileIcon = $notification->icon;
            $oldFileImage = $notification->image;

            if ($notification->delete()) {
                $this->deleteImageFromS3($oldFileIcon);
                $this->deleteImageFromS3($oldFileImage);
                $this->deleteSchedule($input['id']);
                return true;
            } else {
                return [
                    'error' => 5,
                    'error_message' => 'Notification Failed to Delete',
                ];
            }
        } else {
            return [
                'error' => 1,
                'error_message' => 'Notification Not Found',
            ];
        }
    }

    private function uploadImageToS3($name, $resource)
    {
        $result['status'] = false;

        try {
            $s3 = Yii::$app->aws->getS3('BUCKET_STATIC');
            $cacheTime = '2592000';
            $options = ['ContentType' => $resource['type']];       
            $uploadResult = $s3->saveFile($name, $resource['tempName'], 'public-read', $cacheTime, $options);
            $result['status'] = true;
            $result['objectUrl'] = $uploadResult;
        } catch (S3Exception $e) {
            echo $e . "\nThere was an error uploading the file.\n";
        }

        return $result;
    }

    private function deleteImageFromS3($name)
    {
        $result['status'] = false;
        
        try {
            $s3 = Yii::$app->aws->getS3('BUCKET_STATIC');
            $objects[] = array('Key' => $name);
            $deleteResult = $s3->deleteObjects($objects);
            $result['status'] = true;
            $result['data'] = $deleteResult;
        } catch (S3Exception $e) {
            echo $e . "\nThere was an error deleting the file.\n";
        }

        return $result;
    }

    private function getImageUrlFromS3($name)
    {
        $result = '';

        if (mb_substr($name, 0, 4) == 'http') {
            return $name;
        } else {
            try {
                // Send full url to reponse
                $s3 = Yii::$app->aws->getS3('BUCKET_STATIC');
                $result = $s3->getContentUrl($name);
            } catch (S3Exception $e) {
                echo $e . "\nThere was an error getting the file url.\n";
            }
        }

        return $result;
    }

}