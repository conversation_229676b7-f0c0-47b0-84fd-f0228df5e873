<?php

namespace api\components\webpush;

use api\models\ApiWebpushToken;
use api\models\ApiWebpushTopic;
use api\models\ApiWebpushUser;
use api\models\ApiWebpushClient;
use common\components\CurlComponent;
use Yii;
use yii\helpers\BaseJson;
use yii\helpers\ArrayHelper;

class ApiWebpushTokenCom extends \api\components\webpush\ApiWebpushGeneralCom
{
    public function __construct($merchantId)
    {
        parent::__construct($merchantId);
    }

    public function getTokenList($input)
    {
        $tokenArray = [];

        $startAt = $input['page_size'] * ($input['page'] - 1);

        $tokenQuery = ApiWebpushToken::find()->where(['client_id' => $input['merchant']]);
        
        $tokenCount = $tokenQuery->count();

        $tokenQuery->orderBy(['id' => SORT_ASC])->limit($input['page_size'])->offset($startAt);
        $tokenList = $tokenQuery->all();

        return $tokenArray = [
            'total_token' => $tokenCount,
            'page_size' => $input['page_size'],
            'page' => $input['page'],
            'token' => $tokenList
        ];
    }

    public function getToken($id)
    {
        return ApiWebpushToken::find()->where(['id' => $id])->one();
    }

    public function saveToken($input)
    {
        // velidate code
        if ($token = ApiWebpushToken::find()->where(['id' => $input['token'], 'client_id' => $input['merchant']])->one()) {
            if (!empty($input['user_id']) && $input['user_id'] == $token->user_id) {
                return array(
                    'error' => 'Token already exist.',
                );
            } elseif (empty($input['user_id'])) {
                // reset all topic attach to the token to default if any
                $this->resetTokenDefault($input, $token->user_id);
                // Set token to general topic
                $this->setTokenTopic($this->defaultTopic, $input['token'], $input['merchant']);
                
                $token->user_id = Null;
                $token->updated_at = time();
                $token->update();
            } else {
                // reset all topic attach to the token to default if any
                $this->resetTokenDefault($input, $token->user_id);
                // set topic for new user or update it
                $currentTopics = $this->setUserSubscription($input['user_id'], $input['merchant'], [$this->defaultTopic]);
                if ($currentTopics) {
                    foreach ($currentTopics as $topic) {
                        $this->setTokenTopic($topic, $input['token'], $input['merchant']);
                    }
                } else {
                    $this->setTokenTopic($this->defaultTopic, $input['token'], $input['merchant']);
                }

                $token->user_id = $input['user_id'];
                $token->updated_at = time();
                $token->update();
            }
        } else {
            // Validate FCM token with firebase
            if ($this->validateWebpushToken($input['token'], $input['merchant'])) {
                $token = new ApiWebpushToken;
                $token->id = $input['token'];
                $token->user_id = empty($input['user_id']) ? NULL : $input['user_id'];
                $token->client_id = $input['merchant'];
                $token->created_at = time();
                $token->updated_at = time();
                $token->save();

                // Set token to general topic
                $this->setTokenTopic($this->defaultTopic, $input['token'], $input['merchant']);

                // Set user topic subscriptions
                if (!empty($input['user_id'])) {
                    $this->setUserSubscription($input['user_id'], $input['merchant'], [$this->defaultTopic]);
                }

                return $token->id;
            } else {
                return array(
                    'error' => 'Token Error.',
                );
            }
        }
    }

    public function updateToken($input)
    {
        $token = ApiWebpushToken::findOne(['id' => $input['id'], 'client_id' => $input['merchant']]);

        // reset all topic attach to the token to default if any
        $this->resetTokenDefault($input, $token->user_id);
        // set topic for new user or update it
        $currentTopics = $this->setUserSubscription($input['customer_id'], $input['merchant'], [$this->defaultTopic]);
        if ($currentTopics) {
            foreach ($currentTopics as $topic) {
                $this->setTokenTopic($topic, $input['id'], $input['merchant']);
            }
        } else {
            $this->setTokenTopic($this->defaultTopic, $token->id, $input['merchant']);
        }
        
        $token->user_id = $input['customer_id'];
        $token->updated_at = time();

        if ($token->update()) {
            return $token->id;
        } else {
            return false;
        }
    }

    public function deleteToken($input)
    {
        $token = ApiWebpushToken::findOne(['id' => $input['id'], 'client_id' => $input['merchant']]);

        if (!empty($token->user_id)) {
            $user = ApiWebpushUser::findOne($token->user_id);
            $topicSubscriptions = BaseJson::decode($user->topic_subscriptions);

            foreach ($topicSubscriptions as $topic) {
                $this->unsetTokenTopic($topic, $input['id'], $input['merchant']);
            }
        } else {
            $this->unsetTokenTopic($this->defaultTopic, $input['id'], $input['merchant']);
        }

        if ($token->delete()) {
            return true;
        } else {
            return false;
        }
    }
}
