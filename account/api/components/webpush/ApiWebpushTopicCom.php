<?php

namespace api\components\webpush;

use api\models\ApiWebpushTopic;
use api\models\ApiWebpushToken;
use api\models\ApiWebpushUser;
use api\models\ApiWebpushCronTopicRefresh;
use api\models\ApiAddressBook;
use api\models\ApiCountries;
use Yii;
use yii\helpers\BaseJson;
use yii\helpers\ArrayHelper;

class ApiWebpushTopicCom extends \api\components\webpush\ApiWebpushGeneralCom
{
    public function __construct($merchantId)
    {
        parent::__construct($merchantId);
    }

    private function camelCase($str, array $noStrip = [])
    {
        // non-alpha and non-numeric characters become spaces
        $str = preg_replace('/[^a-z0-9' . implode("", $noStrip) . ']+/i', ' ', $str);
        $str = trim($str);
        // uppercase the first character of each word
        $str = ucwords($str);
        $str = str_replace(" ", "", $str);
        $str = lcfirst($str);

        return $str;
    }

    private function setDefaultTopic($id, $code, $merchantId)
    {
        $prevTopicDefault = ApiWebpushTopic::find()
            ->where(['topic_default' => 1])
            ->andWhere(['!=', 'id', $id])
            ->andWhere(['=', 'client_id', $merchantId])
            ->one();

        if ($prevTopicDefault) {
            // get all user under this client
            if ($users = ApiWebpushUser::find()->select('id')->where(['client_id' => $merchantId])->asArray()->all()) {
                foreach ($users as $user) {
                    $this->updateUserSubscriptions($user['id'], $prevTopicDefault->code, $code);
                }
            }

            // get all token
            if ($tokens = ApiWebpushToken::find()->select('id')->where(['client_id' => $merchantId])->asArray()->all()) {
                foreach ($tokens as $token) {
                    $this->unsetTokenTopic($prevTopicDefault->code, $token['id'], $merchantId);
                    $this->setTokenTopic($code, $token['id'], $merchantId);
                }
            }
        }

        ApiWebpushTopic::updateAll(['topic_default' => 0], 'id != ' . $id . ' AND client_id = "' . $merchantId . '"');
    }

    public function getTopicList($input)
    {
        $topicArray = [];

        $topicQuery = ApiWebpushTopic::find();

        if (isset($input['topic_code']) && $input['topic_code']) {
            $topicQuery->select(['code']);
            $topicQuery->where(['client_id' => $input['merchant']]);
            $topicQuery->orderBy(['topic_default' => SORT_DESC]);
            $topicList = $topicQuery->all();

            return $topicArray = [
                'topic' => $topicList
            ];
        } else {
            $startAt = $input['page_size'] * ($input['page'] - 1);
            $topicQuery->where(['client_id' => $input['merchant']]);
            $topicCount = $topicQuery->count();
            $topicQuery->orderBy(['id' => SORT_ASC])->limit($input['page_size'])->offset($startAt);
            $topicList = $topicQuery->all();

            return $topicArray = [
                'total_topic' => $topicCount,
                'page_size' => $input['page_size'],
                'page' => $input['page'],
                'topic' => $topicList
            ];
        }
    }

    public function getTopic($id)
    {
        $data = ApiWebpushTopic::find()->where(['id' => $id])->one();
        
        return ($data) ? $data : ['error' => 1, 'error_message' => 'Topic Not Found'];
    }

    public function saveTopic($input)
    {
        // velidate code
        if (ApiWebpushTopic::find()->where(['code' => $this->camelCase($input['title']), 'client_id' => $input['merchant']])->one()) {
            return [
                'error' => 2,
                'error_message' => 'Topic Already Exist',
            ];
        } else {
            $topic = new ApiWebpushTopic;
            $topic->type = $input['type'];
            $topic->title = $input['title'];
            $topic->description = $input['description'];
            $topic->topic_default = $input['topic_default'];
            $topic->code = $this->camelCase($input['title']);
            $topic->client_id = $input['merchant'];
            $topic->created_at = time();
            $topic->updated_at = time();
            $topic->created_by = $input['created_by'];
            $topic->token_counts = 0;
            $topic->topic_filter = $input['topic_filter'];

            if ($topic->save()) {
                // remove default topic from previous setting if default ON
                if ($input['topic_default'] == 1) {
                    $this->setDefaultTopic($topic->id, $topic->code, $input['merchant']);
                }

                return $topic->id;
            } else {
                return [
                    'error' => 3,
                    'error_message' => 'Topic Failed to Save',
                ];
            }
        }
    }

    public function updateTopic($input)
    {
        $topic = ApiWebpushTopic::findOne($input['id']);
        $time = time();
        if ($topic) {
            // Check for topic status update onyl
            if (isset($input['refresh_status']) && $input['refresh_status'] == 1) {
                // Get all user in webpush db
                $cronjob = (isset($input['cronjob']) && $input['cronjob'] == 1) ? true : false;
                $startId = (isset($input['start_id']) && !empty($input['start_id'])) ?  $input['start_id'] : 0;
                
                $users = ApiWebpushUser::find()
                    ->select('id')
                    ->andWhere(['client_id' => $input['merchant']])
                    ->andWhere(['>=', 'id', $startId])
                    ->orderBy(['id' => SORT_ASC])
                    ->limit(Yii::$app->params['FIREBASE']['CRON_TOPIC_REFRESH_LIMIT'])
                    ->asArray()
                    ->all();
                
                foreach ($users as $user) {
                    // Generate subscriptions based on topic filtering
                    $this->updateTopicFilterSubscriptions($topic, $input['merchant'], $user, $cronjob);
                }

                reset($users);
                $lastUser = end($users);

                if (!$cronjob) {
                    if (ApiWebpushCronTopicRefresh::find()->where(['topic_id' => $input['id']])->one()) {
                        // Do nothing
                    } else {
                        // Insert into cron table
                        $cronTopic = new ApiWebpushCronTopicRefresh;
                        $cronTopic->client_id = $input['merchant'];
                        $cronTopic->topic_id = $input['id'];
                        $cronTopic->new_start_id = $lastUser['id'] + 1;
                        $cronTopic->cron_last_process_date = 0;
                        $cronTopic->cron_last_process_count = 0;
                        $cronTopic->cron_process_track_in_action = 0;
                        $cronTopic->cron_process_track_failed_attempt = 0;
                        $cronTopic->created_at = $time;
                        $cronTopic->created_by = $input['created_by'];
                        $cronTopic->save();

                        // Update topic refresh status
                        $topic->refresh = 1;
                        $topic->update();
                    }
                } else {
                    $cronTipicObj = ApiWebpushCronTopicRefresh::find()->where(['topic_id' => $input['id']])->one();
                    $cronTipicObj->new_start_id = $lastUser['id'] + 1;
                    $cronTipicObj->cron_last_process_count = Yii::$app->params['FIREBASE']['CRON_TOPIC_REFRESH_LIMIT'];
                    $cronTipicObj->update();
                }
            } else {
                $topic->description = $input['description'];
                $topic->topic_default = $input['topic_default'];
                $topic->topic_filter = $input['topic_filter'];
            }

            $topic->updated_at = $time;
            $topic->created_by = $input['created_by'];

            if ($topic->update()) {
                // remove default topic from previous setting if default ON
                if (isset($input['topic_default']) && $input['topic_default'] == 1) {
                    $this->setDefaultTopic($topic->id, $topic->code, $input['merchant']);
                }
                return $topic->id;
            } else {
                return [
                    'error' => 4,
                    'error_message' => 'Topic Failed to Update',
                ];
            }
        } else {
            return [
                'error' => 1,
                'error_message' => 'Topic Not Found',
            ];
        }
    }

    public function deleteTopic($input)
    {
        $topic = ApiWebpushTopic::findOne($input['id']);
        // check for default 1:default, 0:other
        if ($topic->topic_default == 1) {
            return [
                'error' => 6,
                'error_message' => 'Topic Failed to Delete',
            ];
        } else {
            $oldTopicCode = $topic->code;
            if ($topic->delete()) {
                // get all user under this client
                if ($users = ApiWebpushUser::find()->select('id')->where(['client_id' => $input['merchant']])->asArray()->all()) {
                    foreach ($users as $user) {
                        $this->updateUserSubscriptions($user['id'], $oldTopicCode);
                        // get all token
                        if ($tokens = ApiWebpushToken::find()->select('id')->where(['client_id' => $input['merchant'], 'user_id' => $user['id']])->asArray()->all()) {
                            foreach ($tokens as $token) {
                                $this->unsetTokenTopic($oldTopicCode, $token['id'], $input['merchant']);
                            }
                        }
                    }
                }
                return true;
            } else {
                return [
                    'error' => 5,
                    'error_message' => 'Topic Failed to Delete',
                ];
            }
        }
    }

    private function updateTopicFilterSubscriptions($topicObj, $merchantId, $user, $cronjob = false)
    {
        $filters = BaseJson::decode($topicObj->topic_filter);
        
        if (!$cronjob) {
            // reset topic token counts
            $topicObj->token_counts = 0;
            $topicObj->update();
        }

        switch ($filters['topic_filter_type']) {
            case '1': // Geolocation
                $geoFilter = $filters['topic_filter_geolocation'];
                // Check for address
                $c = Yii::$app->db;
                $c_sql = "SELECT ab.customers_id, ab.entry_country_id, c.countries_iso_code_2
                        FROM " . ApiAddressBook::tableName() . " AS ab
                        LEFT JOIN " . ApiCountries::tableName() . " AS c
                            ON c.countries_id = ab.entry_country_id
                        WHERE ab.customers_id = " . (int) $user['id'] . "
                        LIMIT 1";
                $c_res = $c->createCommand($c_sql)->query();
                if (($row = $c_res->read()) !== false) {
                    if (isset($row['countries_iso_code_2']) && !empty($row['countries_iso_code_2'])) {
                        // Check if fit the geolocation filter
                        if (in_array($row['countries_iso_code_2'], $geoFilter)) {
                            // Set topic subscriptions to user
                            $this->setUserSubscription($user['id'], $merchantId, [$topicObj->code]);
                            // get all token
                            if ($tokens = ApiWebpushToken::find()->select('id')->where(['client_id' => $merchantId, 'user_id' => $user['id']])->asArray()->all()) {
                                // set token to topic
                                foreach ($tokens as $token) {
                                    $this->setTokenTopic($topicObj->code, $token['id'], $merchantId);
                                }
                            }
                        } else {
                            $this->updateUserSubscriptions($user['id'], $topicObj->code);
                        }
                    }
                }
                break;
        }
    }

    private function updateUserSubscriptions($userId, $oldTopicCode, $defaultCode = '')
    {
        $userObj = ApiWebpushUser::findOne($userId);
        $topicSubscriptions = BaseJson::decode($userObj->topic_subscriptions);
        // remove prev default topic from user data
        foreach ($topicSubscriptions as $key => $value) {
            if ($value == $oldTopicCode) {
                unset($topicSubscriptions[$key]);
            }
        }
        // update user with new default subscriptions
        if (isset($defaultCode) && $defaultCode) {
            $topicSubscriptions = array_unique(ArrayHelper::merge($topicSubscriptions, [$defaultCode]));
        }
        $userObj->topic_subscriptions = BaseJson::encode($topicSubscriptions);
        $userObj->updated_at = time();
        $userObj->update();
    }
}
