<?php
$params = \yii\helpers\ArrayHelper::merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-encoded.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-encoded.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-api',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'api\controllers',
    'bootstrap' => ['log'],
    'modules' => [],
    'components' => [
        'request' => [
            'enableCsrfValidation' => false,
        ],
        'response' => [
            'format' => \yii\web\Response::FORMAT_JSON,
        ],
        'user' => [
            'identityClass' => 'api\models\DummyUser',
        ],
        'errorHandler' => [
            'class' => 'common\components\WebErrorHandler',
            'errorAction' => 'site/error',
        ],
        'urlManager' => [
            'rules' => [
                //Yii1: To map api/controller/action legacy calling method
                'api/<controller>/<action>' => '<controller>/<action>',
                ['class' => 'yii\rest\UrlRule', 'controller' => 'webpush/client'],
                ['class' => 'yii\rest\UrlRule', 'controller' => 'webpush/token', 'tokens' => ['{id}' => '<id:.+>']],
                ['class' => 'yii\rest\UrlRule', 'controller' => 'webpush/topic'],
                ['class' => 'yii\rest\UrlRule', 'controller' => 'webpush/notification'],
            ],
        ],
    ],
    'params' => $params,
];
