<?php

namespace api\controllers;

use api\components\ApiCurrenciesCom;
use api\components\ApiGeneralCom;
use api\models\ApiCouponGvCustomer;
use api\models\ApiCustomers;
use api\models\ApiStoreAccountBalance;
use api\models\ApiStorePoints;
use common\components\CamelCaseActionController;
use Yii;

class AccountController extends CamelCaseActionController {
    /*
     * code
     * 200 : success
     * 404 : not found
     */

    public $input = array();
    public $result = array();
    public $status = false;
    public $code = 404;
    public $error;

    public function beforeAction($action) {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if ($api_obj->_validateSignature($this->input)) {
            return true;
        } else {
            $this->error = 'Invalid signature';
        }
        Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error
        ]);
        return false;
    }

    public function afterAction($action, $result) {
        $result = parent::afterAction($action, $result);
        return [
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error,
        ];
    }

    public function actionBalance() {
        if (isset($this->input['request']) && isset($this->input['cid'])) {
            $m_cust = ApiCustomers::findOne(['customers_id' => $this->input['cid']]);
            if (isset($m_cust->customers_id)) {
                $cur_obj = new ApiCurrenciesCom();
                
                switch ($this->input['request']) {
                    case 'seller_credit':
                        $this->status = true;

                        $m_sab = ApiStoreAccountBalance::findAll(['user_id' => $this->input['cid'], 'user_role' => 'customers']);
                        if (count($m_sab)) {
                            $this->code = 200;
                            foreach ($m_sab as $_idx => $_sab) {
                                $amt = (floor($_sab->store_account_balance_amount * 100) / 100);
                                
                                $this->result[] = array(
                                    'currency' => $_sab->store_account_balance_currency,
                                    'amount' => $amt,
                                    'display' => $cur_obj->format($amt, false, $_sab->store_account_balance_currency)
                                );
                            }
                        }
                        break;

                    case 'store_credit':
                        $this->status = true;

                        $m_cgc = ApiCouponGvCustomer::findOne(['customer_id' => $this->input['cid']]);
                        if (isset($m_cgc->customer_id)) {
                            $this->code = 200;
                            
                            $amt = (($m_cgc->sc_reversible_amount + $m_cgc->sc_irreversible_amount) - ($m_cgc->sc_reversible_reserve_amount + $m_cgc->sc_irreversible_reserve_amount));
                            $cur_code = (isset($cur_obj->format_id[$m_cgc->sc_currency_id]) ? $cur_obj->format_id[$m_cgc->sc_currency_id] : "");
                            
                            $this->result[] = array(
                                'currency' => $cur_code,
                                'amount' => $amt,
                                'display' => $cur_obj->format($amt, false, $cur_code)
                            );
                        }
                        break;

                    case 'token':
                        $this->status = true;

                        $m_sp = ApiStorePoints::findOne(['customers_id' => $this->input['cid']]);
                        if (isset($m_sp->customers_id)) {
                            $this->code = 200;
                            $this->result[] = array(
                                'currency' => '',
                                'amount' => $m_sp->sp_amount,
                                'display' => $m_sp->sp_amount
                            );
                        }
                        break;
                }

                if (!$this->status) {
                    $this->error = 'Invalid request';
                } else if ($this->status && empty($this->result)) {
                    $this->result[] = array(
                        'currency' => '',
                        'amount' => 0
                    );
                }
            } else {
                $this->error = 'Invalid Customer ID';
            }
        } else {
            $this->error = 'Invalid request';
        }
    }

}