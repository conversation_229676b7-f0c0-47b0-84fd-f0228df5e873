<?php

namespace api\controllers;

class LogController extends \yii\web\Controller {
    
    protected $token_validity = 5 * 24 * 60 * 60; //5 days

    public function actionView($id = null, $timestamp = null, $token = null) {
        if (empty($id) || empty($timestamp || empty($token))) {
            throw new \yii\web\NotFoundHttpException();
        } else if (\common\components\Reporter::generateToken($id, $timestamp) !== strtolower($token)) {
            throw new \yii\web\NotFoundHttpException();
        } else if (time() > $timestamp + $this->token_validity || time() < $timestamp) {
            throw new \yii\web\ForbiddenHttpException('Timestamp expired');
        }
        $log = \common\models\GenericEvent::findOne($id);
        if (!isset($log)) {
            throw new \yii\web\NotFoundHttpException('Log not found');
        }
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $attr = $log->attributes;
        $attr['env_dump'] = json_decode($attr['env_dump'], true);
        return $attr;
    }

}