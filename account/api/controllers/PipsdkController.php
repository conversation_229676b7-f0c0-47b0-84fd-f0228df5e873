<?php

namespace api\controllers;

use Yii;
use common\models\CustomerPipwaveVerificationDocMetaModel;
use common\models\CustomerPipwaveVerificationDocModel;
use common\models\CustomerPipwaveVerificationMetaModel;
use common\models\CustomerPipwaveVerificationModel;
use common\models\CustomersVerificationDocument;
use common\models\CustomerPipwaveVerificationToken;
use common\models\CustomersRemarksHistory;
use common\models\CustomersVerificationDocumentLog;
use dpodium\pipwave\sdk\models\ApiForm;


//use frontend\components\Controller;
//
class PipsdkController extends \yii\web\Controller
{

    public function actionNotification()
    {
        if (Yii::$app->request->isPost) {
            try {
                $connection = \Yii::$app->db;
                $transaction = $connection->beginTransaction();
                $raw_data = Yii::$app->request->getRawBody();

                $data = json_decode($raw_data);
                $is_signature_valid = $this->validateSignature($data);
                if (!$is_signature_valid) {
                    Yii::$app->reporter->reportToAdminViaSlack("Invalid Signature", $data);
                    return;
                }
                $session_token = (isset($data->session_token)) ? $data->session_token : null;
                $pd_id = (isset($data->pd_id)) ? $data->pd_id : null;
                $verification_type = (isset($data->verification_type)) ? $data->verification_type : null;
                $attempts_num = (isset($data->attempts_num)) ? $data->attempts_num : null;
                $is_file_exist = CustomerPipwaveVerificationModel::find()->where(["session_token" => $session_token, "pd_id" => $pd_id, "verification_type" => $verification_type, "attempts_num" => $attempts_num])->exists();

                // Save Verification Main
                \Yii::$app->cache->delete($data->extra_param1);
                $verification = new CustomerPipwaveVerificationModel();
                $verification->session_token = (isset($data->session_token)) ? $data->session_token : null;
                $verification->verification_type = (isset($data->verification_type)) ? $data->verification_type : null;
                $verification->customer_id = (isset($data->user_id) && is_numeric($data->user_id)) ? (integer)$data->user_id : null;
                $verification->mode = (isset($data->mode)) ? $data->mode : null;
                $verification->attempts_num = (isset($data->attempts_num)) ? $data->attempts_num : null;
                $verification->ip_address = (isset($data->ip_address)) ? $data->ip_address : null;
                $verification->verification_status = (isset($data->verification_status)) ? $data->verification_status : null;
                $verification->approval_status = (isset($data->approval_status)) ? $data->approval_status : null;
                $verification->approval_status_updated_by = (isset($data->approval_status_updated_by)) ? $data->approval_status_updated_by : null;
                $verification->pd_id = (isset($data->pd_id)) ? $data->pd_id : null;
                $verification->user_agent = (isset($data->user_agent)) ? $data->user_agent : null;
                $verification->document_set_scoring = (isset($data->document_set_scoring)) ? $data->document_set_scoring : null;
                $verification->api_key = (isset($data->api_key)) ? $data->api_key : null;
                $verification->started_at = (isset($data->started_at)) ? $data->started_at : null;
                $verification->completed_at = (isset($data->completed_at)) ? $data->completed_at : null;
                $verification->timestamp = (isset($data->timestamp)) ? $data->timestamp : null;
                $verification->signature = (isset($data->signature)) ? $data->signature : null;
                $verification->extra_param1 = (isset($data->extra_param1)) ? $data->extra_param1 : null;
                $verification->extra_param2 = (isset($data->extra_param2)) ? $data->extra_param2 : null;
                $verification->extra_param3 = (isset($data->extra_param3)) ? $data->extra_param3 : null;
                $verification->face_id = (isset($data->analysis_result_meta->face_id)) ? $data->analysis_result_meta->face_id : "";

                if ($verification->save()) {
                    $this->saveMeta($verification->verification_id, $data->analysis_result_meta, $raw_data, $verification->customer_id);
                    $this->saveDocuments($verification->verification_id, $data->documents);
                    $this->updateCustomerVerificationDoc($data);

                    $customer_token_model = CustomerPipwaveVerificationToken::find()->where(['token' => $data->session_token])->one();
                    if ($customer_token_model) {
                        $customer_token_model->updateStatus($data->verification_status);
                    }

                    if ($is_file_exist == false) {
                        //save file only if unique document is
                        $this->uploadDocuments($data);
                    }

                    $transaction->commit();
                    echo "OK";
                    Yii::$app->end();
                } else {
                    $transaction->rollBack();
                    \Yii::$app->reporter->reportToAdminViaSlack("Verification Save Failed", $verification->errors);
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                Yii::$app->reporter->reportToAdminViaSlack("PIPWAVE SDK Shasso Notification", $e->getMessage());
                throw $e;
            } catch (\Throwable $e) {
                $transaction->rollBack();
                Yii::$app->reporter->reportToAdminViaSlack("PIPWAVE SDK Shasso Notification", $e->getMessage());
                throw $e;
            }
        }
    }

    public function uploadDocuments($data)
    {
        if (!in_array($data->verification_type, ['identification', 'credit-card', 'selfie'])) {
            return;
        }

        $key_arr = [
            'identification' => '003',
            'credit-card' => '004',
            'selfie' => '001'
        ];

        $_key = $key_arr[$data->verification_type];
        $m_cvd = CustomersVerificationDocument::findOne([
            'customers_id' => $data->user_id,
        ]);


        if (!empty($data->documents)) {
            $upload_fail = array();
            $upload_success = array();
            $c = 0;
            foreach ($data->documents as $document) {
                $c++;
                $file_name = null;
                $file_url = null;
                $file_size = null;

                if ($document->full_size > 0) {
                    $file_name = $document->full_filename;
                    $file_url = $document->full_url;
                    $file_size = $document->full_size;
                }

                if ($file_url) {
                    $file_locked = str_replace("doc_", "", $_key);
                    $m_files = "files_" . $file_locked;
                    $m_files_locked = "files_" . $file_locked . "_locked";

                    /*
                     * prefix
                     * selfie sf_
                     * credit_card cc_
                     * identification id_
                     * *?
                     */

                    $tag = [
                        'selfie' => '_sf_',
                        'credit-card' => '_cc_',
                        'identification' => '_id_'
                    ];

                    if (array_key_exists($document->document_type, $tag)) {
                        $tag_text = $tag[$document->document_type];
                    } else {
                        $tag_text = '_';
                    }
                    if ($data->api_key == \Yii::$app->params['pwapi.og_api_key']) {
                        $tag_text .= 'og_';
                    }

                    $_val["type"] = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                    $dest_filename = $data->user_id . "_" . $file_locked . "_" . date("YmdHis") . $tag_text . $c . "." . $_val["type"];
                    $tmp_filename = $data->user_id . "_" . $file_locked . "_" . date("YmdHis") . $tag_text . $c;


                    $ch = curl_init($file_url);
                    $fp = fopen(Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]) . $tmp_filename, 'wb');
                    curl_setopt($ch, CURLOPT_FILE, $fp);
                    curl_setopt($ch, CURLOPT_HEADER, 0);
                    curl_exec($ch);
                    curl_close($ch);
                    fclose($fp);

                    $file = [
                        "name" => $dest_filename,
                        "type" => " image/jpeg",
                        "tmp_name" => Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]) . $tmp_filename,
                        "size" => $file_size
                    ];

                    $up_obj = new \frontend\components\UploadCom($_key);
                    $up_obj->set_encryption(Yii::$app->params["VERIFICATION_DOC"]["ENCRYPT_FILE"]);
                    $up_obj->set_extensions(Yii::$app->params["VERIFICATION_DOC"]["FILE_TYPE"]);
                    $up_obj->set_maxsize(Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"] * 1024);

                    $up_obj->set_bucket(Yii::$app->params["VERIFICATION_DOC"]["S3_BUCKET"]);
                    $up_obj->set_destination(Yii::$app->params["VERIFICATION_DOC"]["S3_DESTINATION"] . $file_locked . '/' . date('Ym') . '/' . $data->user_id . '/');
                    $up_obj->set_filename(date("YmdHis") . $tag_text . $c . "." . $_val["type"]);

                    $up_obj->set_upload_destination(Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]));
                    $up_obj->set_upload_destination_filename($dest_filename);

                    if ($up_obj->save($file)) {
                        unlink(Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]) . $tmp_filename);
                        $upload_success[$file_locked] = Yii::t("verificationSubmission", "TEXT_VERIFICATION_TYPE_" . $file_locked);

                        $m_cvd->$m_files = $up_obj->filename;
//                        $m_cvd->$m_files_locked = 1;
                        $m_cvd->save();

                        $m_attr = [
                            'log_users_id' => $data->user_id,
                            'log_users_type' => 'customers',
                            'log_customers_id' => $data->user_id,
                            'log_docs_id' => $file_locked,
                            'log_IP' => $data->ip_address,
                            'log_datetime' => date("Y-m-d H:i:s"),
                            'log_filename' => $up_obj->filename,
                            'log_action' => 'UPLOAD'
                        ];
                        CustomersVerificationDocumentLog::model()->saveNewRecord($m_attr);
                        unset($m_attr);
                    } else {
                        $output_message = $up_obj->get_output_messages();

                        if (isset($output_message[0]["message"]) && !in_array($output_message[0]["message"], $upload_fail)) {
                            $upload_fail[$file_locked] = $output_message[0]["message"];
                        }
                    }
                    unset($up_obj);
                }
            }
            \common\components\CustomersInfoVerificationComponent::fileUploadNotification($data->user_id);
        }
    }

    public function updateCustomerVerificationDoc($data)
    {
        $types = [
            'identification' => '003',
            'credit-card' => '004',
            'selfie' => '001'
        ];
        $status = [
            10 => 1,  // sucess
            5 => 4, // failed
            8 => 3, // Review
            2 => 4 // Error
        ];

//        if (!in_array($data->verification_type, $types)) {
//            //invalid verification type
//            return;
//        }


        $file = $types[$data->verification_type];
        $lock_field = 'files_' . $file . '_locked';
        $status_field = 'files_' . $file . '_status';
        $file_field = 'files_' . $file;

        $doc = CustomersVerificationDocument::find()->where(['customers_id' => $data->user_id])->one();


        if ($doc) {
            if (in_array($data->approval_status, [10, 5])) {
                $doc->$status_field = $status[$data->approval_status];
                $doc->$lock_field = (in_array($data->approval_status, [10, 8, 5])) ? 1 : 0; //            $doc->$file_field = '';
            } else {
                $doc->$status_field = $status[$data->verification_status];
                $doc->$lock_field = (in_array($data->verification_status, [10, 8, 5])) ? 1 : 0; //            $doc->$file_field = '';
            }

            if (!$doc->save()) {
                Yii::$app->reporter->reportToAdminViaSlack("updateCustomerVerificationDoc save error ", $doc->errors);
            }
        }
        return;
    }

    public function saveMeta($verification_id, $verification_meta, $raw_data, $customer_id)
    {
        $meta_type = 'full_notification';
        $v_meta = new CustomerPipwaveVerificationMetaModel();
        $v_meta->verification_id = $verification_id;
        $v_meta->meta_type = $meta_type;
        $v_meta->meta_label = $meta_type;
        $v_meta->meta_value = $raw_data;
        $v_meta->save();

        $meta = (array)$verification_meta;
        if (!empty($meta)) {
            $meta_type = 'raw_meta';
            $v_meta = new CustomerPipwaveVerificationMetaModel();
            $v_meta->verification_id = $verification_id;
            $v_meta->meta_type = $meta_type;
            $v_meta->meta_label = $meta_type;
            $v_meta->meta_value = json_encode($meta);
            $v_meta->save();

            foreach ($meta as $k => $v) {
                if ($k == 'face_matches') {
                    if (!empty($v->list)) {
                        $c = 1;
                        foreach ($v->list as $match_id) {
                            $v_meta = new CustomerPipwaveVerificationMetaModel();
                            $v_meta->verification_id = $verification_id;
                            $v_meta->meta_type = $k;
                            $v_meta->meta_label = $k . '_' . $c;
                            $v_meta->meta_value = $match_id;
                            $v_meta->save();
                            $c++;
                        }
                        $total_share_user = array_merge([$customer_id], $v->list);
                        sort($total_share_user);
                        foreach ($total_share_user as $other_share_id) {
                            $m_crh = new CustomersRemarksHistory();
                            $m_crh->customers_id = $other_share_id;
                            $m_crh->date_remarks_added = date('Y-m-d H:i:s');
                            $exclude_own_total_share_user = array_diff($total_share_user, [$other_share_id]);
                            $m_crh->remarks = 'Selfie shared with users ' . implode(', ', $exclude_own_total_share_user);
                            $m_crh->remarks_added_by = 'System';
                            $m_crh->save();
                        }
                    }
                    // save fraud level
                    if (!empty($v->fraud_level)) {
                        $fraud_level = $v->fraud_level;
                        $v_meta = new CustomerPipwaveVerificationMetaModel();
                        $v_meta->verification_id = $verification_id;
                        $v_meta->meta_type = "fraud_level";
                        $v_meta->meta_label = "fraud_level";
                        $v_meta->meta_value = $fraud_level;
                        $v_meta->save();
                    }
                } else {
                    $v_meta = new CustomerPipwaveVerificationMetaModel();
                    $v_meta->verification_id = $verification_id;
                    $v_meta->meta_type = $k;
                    $v_meta->meta_label = $k;
                    $v_meta->meta_value = (is_string($v)) ? $v : json_encode($v);
                    $v_meta->save();
                }
            }
        }
    }

    public function saveDocuments($verification_id, $documents)
    {
        if (!empty($documents)) {
            foreach ($documents as $document) {
                $doc = new CustomerPipwaveVerificationDocModel();
                $doc->verification_id = $verification_id;
                $doc->document_type = $document->document_type;
                $doc->subject_filename = $document->subject_filename;
                $doc->subject_size = $document->subject_size;
                $doc->subject_url = $document->subject_url;
                $doc->full_filename = $document->full_filename;
                $doc->full_size = $document->full_size;
                $doc->full_url = $document->full_url;
                $doc->document_scoring = $document->document_scoring;
                $doc->result = $document->result;

                if ($doc->save()) {
                    $this->saveDocMeta($doc->document_id, $document->analysis_result_meta);
                } else {
                    \Yii::$app->reporter->reportToAdminViaSlack('File upload save failed', $doc);
                }
            }
        }
    }

    public function saveDocMeta($document_id, $doc_meta)
    {
        $DocMeta = new CustomerPipwaveVerificationDocMetaModel;
        if (!empty($doc_meta)) {
            $meta_type = 'raw_meta';
            $raw_meta[] = [
                "verification_meta_id" => "",
                "document_id" => $document_id,
                "meta_type" => $meta_type,
                "meta_label" => $meta_type,
                "meta_value" => json_encode($doc_meta),
            ];
            Yii::$app->db->createCommand()->batchInsert(CustomerPipwaveVerificationDocMetaModel::tableName(), $DocMeta->attributes(), $raw_meta)->execute();
        }
        if (isset($doc_meta->detected_labels)) {
            $meta_type = 'detected_labels';
            $detected_labels = [];
            foreach ($doc_meta->detected_labels as $k => $v) {
                $detected_labels[] = [
                    "verification_meta_id" => "",
                    "document_id" => $document_id,
                    "meta_type" => $meta_type,
                    "meta_label" => $k,
                    "meta_value" => $v
                ];
            }
            Yii::$app->db->createCommand()->batchInsert(CustomerPipwaveVerificationDocMetaModel::tableName(), $DocMeta->attributes(), $detected_labels)->execute();
        }

        if (isset($doc_meta->detected_label)) {
            $meta_type = 'detected_labels';
            $detected_label = [];
            foreach ($doc_meta->detected_label as $k => $v) {
                $detected_label[] = [
                    "verification_meta_id" => "",
                    "document_id" => $document_id,
                    "meta_type" => $meta_type,
                    "meta_label" => $k,
                    "meta_value" => $v
                ];
            }
            Yii::$app->db->createCommand()->batchInsert(CustomerPipwaveVerificationDocMetaModel::tableName(), $DocMeta->attributes(), $detected_label)->execute();
        }

        if (isset($doc_meta->detected_text)) {
            $meta_type = 'detected_text';
            $detected_text = [];
            foreach ($doc_meta->detected_text as $k => $v) {
                $detected_text[] = [
                    "verification_meta_id" => "",
                    "document_id" => $document_id,
                    "meta_type" => $meta_type,
                    "meta_label" => $k,
                    "meta_value" => $v
                ];
            }
            Yii::$app->db->createCommand()->batchInsert(CustomerPipwaveVerificationDocMetaModel::tableName(), $DocMeta->attributes(), $detected_text)->execute();
        }

        if (isset($doc_meta->match_text)) {
            $match_text = (array)$doc_meta->match_text;

            if (!empty($match_text)) {
                foreach ($match_text as $mk => $mv) {
                    $meta_type = 'match_text' . $mk;
                    $DocMeta = new CustomerPipwaveVerificationDocMetaModel();
                    $DocMeta->document_id = $document_id;
                    $DocMeta->meta_type = $meta_type;
                    $DocMeta->meta_label = $mv->text;
                    $DocMeta->meta_value = '' . $mv->score;
                }
            }
        }

        if (isset($doc_meta->detected_labels_full_img)) {
            $meta_type = 'detected_labels_full_img';
            $detected_labels = [];
            foreach ($doc_meta->detected_labels_full_img as $k => $v) {
                $detected_labels[] = [
                    "verification_meta_id" => "",
                    "document_id" => $document_id,
                    "meta_type" => $meta_type,
                    "meta_label" => $k,
                    "meta_value" => $v
                ];
            }
            Yii::$app->db->createCommand()->batchInsert(CustomerPipwaveVerificationDocMetaModel::tableName(), $DocMeta->attributes(), $detected_labels)->execute();
        }
    }

    public function saveDocMeta2($document_id, $doc_meta)
    {
        if (isset($doc_meta->detected_labels)) {
            $meta_type = 'detected_labels';
            foreach ($doc_meta->detected_labels as $k => $v) {
                $DocMeta = new CustomerPipwaveVerificationDocMetaModel;
                $DocMeta->document_id = $document_id;
                $DocMeta->meta_type = $meta_type;
                $DocMeta->meta_label = $k;
                $DocMeta->meta_value = $v;
                $DocMeta->save();
            }
        }

        if (isset($doc_meta->detected_text)) {
            $meta_type = 'detected_text';
            foreach ($doc_meta->detected_text as $k => $v) {
                $DocMeta = new CustomerPipwaveVerificationDocMetaModel;
                $DocMeta->document_id = $document_id;
                $DocMeta->meta_type = $meta_type;
                $DocMeta->meta_label = $k;
                $DocMeta->meta_value = $v;
                $DocMeta->save();
            }
        }
    }

    public function validateSignature($data)
    {
        $array = [
            'api_key' => $data->api_key,
            'api_secret' => $this->getApiSecret($data->api_key),
            'user_id' => $data->user_id,
            'pd_id' => $data->pd_id,
            'session_token' => $data->session_token,
            'verification_type' => $data->verification_type,
            'mode' => $data->mode,
            'attempts_num' => $data->attempts_num,
            'verification_status' => $data->verification_status,
            'document_set_scoring' => $data->document_set_scoring,
            'timestamp' => $data->timestamp,

        ];

        $signature = ApiForm::genSignature($array);
        return ($signature == $data->signature);
    }

    public function getApiSecret($data_api_key)
    {
        $brand_list = \Yii::$app->params['brand_list'];
        foreach ($brand_list as $brand) {
            $key = \Yii::$app->params['pwapi.' . $brand . '_api_key'];
            if ($key == $data_api_key) {
                return \Yii::$app->params['pwapi.' . $brand . '_api_secret'];
            }
        }
    }
}
