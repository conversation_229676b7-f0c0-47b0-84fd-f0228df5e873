<?php

namespace api\controllers;

use api\components\ApiCustomersCom;
use api\components\ApiGeneralCom;
use api\components\ApiSellerCom;
use api\models\ApiAddressBook;
use api\models\ApiCountries;
use api\models\ApiCustomers;
use api\models\ApiCustomersInfo;
use api\models\ApiCustomersInfoVerification;
use common\components\CamelCaseActionController;
use common\components\CustomersCom;
use common\components\CustomerSecurityComponent;
use common\components\CustomersInfoVerificationComponent;
use common\components\GeneralComponent;
use common\components\Password;
use common\components\PhoneVerificationComponent;
use common\models\Customers;
use common\models\CustomersInfoVerification;
use common\models\CustomersSetting;
use Yii;

class ProfileController extends CamelCaseActionController {

    public $input = array();
    public $result = array();
    public $status = false;
    public $code = 404;
    public $error;

    public function beforeAction($action) {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if ($api_obj->_validateSignature($this->input)) {
            Yii::$app->language = isset($this->input['extra']['language']) ? $this->input['extra']['language'] : 'en';
            return true;
        } else {
            $this->error = 'Invalid signature';
        }
        Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error
        ]);
        return false;
    }

    public function afterAction($action, $result) {
        $result = parent::afterAction($action, $result);
        $api_obj = new ApiGeneralCom();
        return $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error,
        ]);
    }

    public function actionUserInfo() {
        $c_req = isset($this->input['request']) ? $this->input['request'] : "signup_date";
        $_req = explode(',', $c_req);
        $_map = array(
            'signup_date' => 'customers_info_date_account_created',
            'email' => 'customers_email_address',
            'firstname' => 'customers_firstname',
            'lastname' => 'customers_lastname',
            'address_id' => 'customers_default_address_id',
            'group_id' => 'customers_groups_id',
            'group_name' => 'customers_groups_name',
            'discount' => 'customers_discount',
            'phone_verified' => 'info_verified',
            'phone' => 'customers_telephone',
            'phone_dial_code' => 'countries_international_dialing_code',
            'phone_country_code' => 'countries_iso_code_2',
        );

        $_search = array_intersect_key($_map, array_flip($_req));
        if (!empty($_search)) {
            $_search_tbl = array();
            $_join_tbl = array();
            foreach ($_search as $key => $field) {
                $this->result[$key] = '';
                switch ($field) {
                    case 'customers_email_address':
                    case 'customers_firstname':
                    case 'customers_lastname':
                    case 'customers_default_address_id':
                    case 'customers_groups_id':
                    case 'customers_telephone':
                    case 'customers_discount':
                        $_search_tbl['customers'][$key] = $field;
                        break;
                    
                    case 'customers_info_date_account_created':
                        $_search_tbl['customers_info'][$key] = $field;
                        break;

                    case 'customers_groups_name':
                        $_join_tbl['customers'][$key] = $field;
                        $customers['customers_groups cg'] = ['select' => 'customers_groups_name', 'on' => 'cg.customers_groups_id = c.customers_groups_id'];
                        break;
                    
                    case 'countries_international_dialing_code':
                    case 'countries_iso_code_2':
                        $_join_tbl['customers'][$key] = $field;
                        $customers['countries dcc'] = ['select' => 'countries_international_dialing_code,countries_iso_code_2', 'on' => 'dcc.countries_id = c.customers_country_dialing_code_id'];
                        break;
                    
                    case 'info_verified':
                        $_join_tbl['customers'][$key] = $field;
                        $customers['countries dcc'] = ['on' => 'dcc.countries_id = c.customers_country_dialing_code_id'];
                        $customers['customers_info_verification iv'] = ['select' => 'info_verified', 'on' => 'iv.customers_id = c.customers_id AND iv.info_verification_type = \'telephone\' AND iv.customers_info_value = CONCAT(dcc.countries_international_dialing_code, c.customers_telephone)'];
                        break;
                }
            }
            
            if (isset($_join_tbl['customers']) && !isset($_search_tbl['customers'])) {
                //Select empty from customers if nothing is selected.
                $_search_tbl['customers'] = array();
            }

            if (!empty($_search_tbl)) {
                foreach ($_search_tbl as $tbl => $val) {
                    $primary = '';
                    switch ($tbl) {
                        case 'customers':
                            if (isset($$tbl)) {
                                $query = ApiCustomers::find()->alias('c');
                                $select = ['c.*'];
                                foreach($$tbl as $table => $details) {
                                    $query->join('LEFT JOIN', $table, $details['on']);
                                    if (isset($details['select'])) {
                                        $select = array_merge($select, explode(',', $details['select']));
                                    }
                                }
                                $m_data = $query->select($select)->where(['c.customers_id' => $this->input['cid']])->asArray()->one();
                            } else {
                                $m_data = ApiCustomers::find()->where(['customers_id' => $this->input['cid']])->asArray()->one();
                            }
                            $primary = 'customers_id';
                            break;

                        case 'customers_info':
                            $m_data = ApiCustomersInfo::find()->where(['customers_info_id' => $this->input['cid']])->asArray()->one();
                            $primary = 'customers_info_id';
                            break;
                    }

                    if (isset($m_data[$primary])) {
                        foreach ($_search_tbl[$tbl] as $key => $field) {
                            if (isset($m_data[$field])) {
                                //TODO encode string to utf8
                                $this->result[$key] = $m_data[$field];
                            }
                        }

                        if (isset($_join_tbl[$tbl]) && isset($$tbl)) {
                            foreach ($_join_tbl[$tbl] as $key => $field) {
                                foreach ($$tbl as $_key => $_val) {
                                    if (isset($m_data[$field])) {
                                        //TODO encode string to utf8
                                        $this->result[$key] = $m_data[$field];
                                    }
                                }
                            }
                        }
                    }

                    if (!empty($this->result)) {
                        $this->code = 200;
                        $this->status = true;
                    } else {
                        $this->error = 'Result not found';
                    }
                }
            }
        } else {
            $this->error = 'Request key mismatch';
        }
    }

    public function actionGetCustomerData() {
        $ret_addr = isset($this->input['extra']['address_book_flag']) ? (int) $this->input['extra']['address_book_flag'] : 0;
        $this->result = ApiCustomersCom::profile($this->input['cid'], $ret_addr);

        if (!empty($this->result)) {
            $this->code = 200;
            $this->status = true;
        } else {
            $this->error = 'Invalid Customer ID';
        }
    }

    public function actionIsBillingInfoComplete() {
        $return_bool = TRUE;
        $customer_id = $this->input['cid'];

        if ($customer_id !== 0) {
            $m_ac = ApiCustomers::findOne(['customers_id' => $customer_id]);
            if (isset($m_ac->customers_id)) {
                if (empty($m_ac->customers_telephone) || empty($m_ac->customers_firstname)) {
                    $return_bool = 1;
                }
            } else {
                $return_bool = -1;
            }

            if ($return_bool === TRUE) {
                $m_aab = ApiAddressBook::findOne(['address_book_id' => $m_ac->customers_default_address_id]);
                if (isset($m_aab->address_book_id)) {
                    if (!trim($m_aab->entry_street_address) || empty($m_aab->entry_country_id) || !trim($m_aab->entry_city) || !trim($m_aab->entry_postcode) || (!trim($m_aab->entry_state) && $m_aab->entry_zone_id == 0)) {
                        $return_bool = 2;
                    }
                } else {
                    $return_bool = -2;
                }
            }

            if ($return_bool === TRUE) {
                $m_actry = ApiCountries::findOne(['countries_id' => $m_ac->customers_country_dialing_code_id]);
                if (isset($m_actry->countries_id)) {
                    $phone = $m_actry->countries_international_dialing_code . $m_ac->customers_telephone;
                    if (!ApiCustomersInfoVerification::find()->where([
                        'customers_info_value' => $phone,
                        'info_verification_type' => 'telephone',
                        'customers_id' => $customer_id,
                        'info_verified' => '1',
                    ])->exists()) {
                        $return_bool = 3;
                    }
                }
            }
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_bool;
    }

    public function actionGetConfirmBillingInfoContent() {
        $customer_id = $this->input['cid'];
        $country = isset($this->input['extra']['country']) ? $this->input['extra']['country'] : 0;

        if ($customer_id) {
            $this->result = ApiCustomersCom::form($customer_id, $country, "billing");
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
    }

    public function actionUpdateConfirmBillingInfo() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $data = isset($this->input['extra']) ? $this->input['extra'] : array();

        if ($customer_id !== 0) {
            $return_array = CustomersCom::editAccount($customer_id, $data);

            if ($return_array['error'] !== TRUE) {
                $m_actry = ApiCountries::findOne(['countries_id' => $return_array['customers_country_dialing_code_id']]);
                $countryDialCode = (isset($m_actry->countries_id) ? $m_actry->countries_international_dialing_code : false);

                $phone_verification_obj = new PhoneVerificationComponent();
                if ($phone_verification_obj->isMobileNumExist($return_array['customers_telephone'], $return_array['customers_country_dialing_code_id'], $customer_id, $countryDialCode)) {
                    $return_array = array(
                        'error' => TRUE,
                        'error_array' => array('* ' . Yii::t('verifyPhone', 'ENTRY_CONTACT_NUMBER_EXIST_ERROR'))
                    );
                }
                unset($phone_verification_obj);
            }
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }

    public function actionCheckInfoVerified() {
        $_status = false;
        $_token = '';

        $c_cid = $this->input['cid'];
        $c_info_val = isset($this->input['extra']['info_value']) ? $this->input['extra']['info_value'] : 0;
        $c_req_token = isset($this->input['extra']['req_token']) ? $this->input['extra']['req_token'] : 0;
        $c_type = isset($this->input['extra']['type']) ? $this->input['extra']['type'] : 'email';

        if (!empty($c_cid)) {
            $m_civ = ApiCustomersInfoVerification::model()->findOne(['customers_id' => $c_cid, 'customers_info_value' => $c_info_val, 'info_verified' => '1', 'info_verification_type' => $c_type]);
            $_status = (isset($m_civ->customers_id) ? true : false);

            if (!$_status && $c_req_token) {
                if ($c_type == 'email') {
                    $_token = GeneralComponent::setRandomEmailSerial($c_info_val, $c_cid);
                }
            }
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = array(
            'status' => $_status,
            'token' => $_token
        );
    }

    public function actionValidatePassword() {
        $result = 'fail';
        $customer_id = $this->input['cid'];
        $password = isset($this->input['extra']['password']) ? $this->input['extra']['password'] : "";

        if (!empty($customer_id) && !empty($password)) {
            $m_ac = ApiCustomers::findOne(['customers_id' => $customer_id]);
            if ($m_ac->customers_id) {
                if (Password::validatePassword($password, $m_ac->customers_password)) {
                    $result = 'success';
                }
            }
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $result;
    }

    public function actionRequestSecurityToken() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $requestUrl = isset($this->input['request_uri']) ? $this->input['request_uri'] : null;

        if ($customer_id !== 0) {
            $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'security_token_request', $customer_id, $this->getCustomersIP());
            $return_array = $customer_security_obj->requestSecurityToken($requestUrl);
            unset($customer_security_obj);
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }

    public function actionRequestPhoneVerifyingToken() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $requestUrl = isset($this->input['request_uri']) ? $this->input['request_uri'] : null;
        
        if ($customer_id !== 0) {
            $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'verify_phone_request', $customer_id, $this->getCustomersIP());
            $return_array = $customer_security_obj->requestSecurityToken($requestUrl);
            $return_array['header'] = Yii::t('smsToken', 'TITLE_SECURITY_TOKEN');
            $return_array['tooltips_title'] = Yii::t('tooltips', 'MSG_PHONE_WARNING');
            $return_array['answer_rel'] = Yii::t('smsToken', 'TEXT_ENTER_6_SERUCITY_TOKEN');
            //TODO Deprecated, may remove soon
            $return_array['update_phone_desc'] = Yii::t('verifyPhone', 'TEXT_CHANGE_PHONE_NUMBER');

            unset($customer_security_obj);
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }

    public function actionRequestSecurityTokenResend() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $requestUrl = isset($this->input['request_uri']) ? $this->input['request_uri'] : null;

        if ($customer_id !== 0) {
            $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'security_token_request', $customer_id, $this->getCustomersIP());
            $return_array = $customer_security_obj->requestSecurityTokenResend($requestUrl);
            unset($customer_security_obj);
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }

    public function actionRequestPhoneVerifyingTokenResend() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $requestUrl = isset($this->input['request_uri']) ? $this->input['request_uri'] : null;
        
        if ($customer_id !== 0) {
            $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'verify_phone_request', $customer_id, $this->getCustomersIP());
            $return_array = $customer_security_obj->requestSecurityTokenResend($requestUrl);
            $return_array['header'] = Yii::t('smsToken', 'TITLE_SECURITY_TOKEN');
            $return_array['tooltips_title'] = Yii::t('tooltips', 'MSG_PHONE_WARNING');
            $return_array['answer_rel'] = Yii::t('smsToken', 'TEXT_ENTER_6_SERUCITY_TOKEN');
            //TODO Deprecated, may remove soon
            $return_array['update_phone_desc'] = Yii::t('verifyPhone', 'TEXT_CHANGE_PHONE_NUMBER');

            unset($customer_security_obj);
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }

    public function actionValidateSecurityToken() {
        $error = TRUE;
        $error_message = '';
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $token = isset($this->input['extra']['token']) ? $this->input['extra']['token'] : "";

        if ($customer_id !== 0 && !empty($token)) {
            $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'security_token_request', $customer_id, $this->getCustomersIP());
            $return_array = $customer_security_obj->validateCustomerSecurityAnswer($token);

            if ($return_array['error'] == 0) {
                $error = FALSE;
            } else {
                $error_message = $return_array['text'];
            }
            unset($customer_security_obj);
        } else {
            $this->error = 'Invalid Customer ID';
        }


        $this->code = 200;
        $this->status = true;
        $this->result = array(
            'error' => $error,
            'error_message' => $error_message,
        );
    }

    public function actionValidatePhoneVerifyingToken() {
        $error = TRUE;
        $error_message = '';
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $token = isset($this->input['extra']['token']) ? $this->input['extra']['token'] : "";

        if ($customer_id !== 0 && !empty($token)) {
            $phone_verification_obj = new PhoneVerificationComponent();
            $customers_info_obj = new CustomersInfoVerificationComponent();
            $phoneInfo = $customers_info_obj->formatTelephone($customer_id);

            if ($phone_verification_obj->isMobileNumExist($phoneInfo['telephone_number'], $phoneInfo['country_id'], $customer_id, $phoneInfo['country_international_dialing_code'])) {
                $error_message = '* ' . Yii::t('verifyPhone', 'ENTRY_CONTACT_NUMBER_EXIST_ERROR');
            } else {
                $customer_security_obj = new CustomerSecurityComponent($merchant_id, 'verify_phone_request', $customer_id, $this->getCustomersIP(), $phoneInfo['telephone_number'], $phoneInfo['country_id']);
                $return_array = $customer_security_obj->validateCustomerSecurityAnswer($token);

                if ($return_array['error'] == 0) {
                    $customers_info_verification_obj = new CustomersInfoVerification();
                    $customers_info_verification_obj->updatePhoneVerifyInfo($customer_id, $phoneInfo['country_international_dialing_code'] . $phoneInfo['telephone_number'], '', 1);
                    unset($customers_info_verification_obj);

                    $error = FALSE;
                } else {
                    $error_message = '* ' . $return_array['text'];
                }

                unset($customer_security_obj);
            }

            unset($phone_verification_obj, $customers_info_obj, $phoneInfo);
        } else {
            $this->error = 'Invalid Customer ID';
        }

        $this->code = 200;
        $this->status = true;
        $this->result = array(
            'error' => $error,
            'error_message' => $error_message
        );
    }
    
    public function actionValidate2faToken() {
        $error = TRUE;
        $error_message = '';
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $token = isset($this->input['extra']['token']) ? $this->input['extra']['token'] : "";
        $tfa_enabled = false;

        if ($customer_id !== 0 && !empty($token)) {
            $tfa_enabled = \api\components\ApiSsoCom::doesCustomerHaveTwoFactorAuthentication($customer_id);
            if ($tfa_enabled) {
                $return_array = \api\components\ApiSsoCom::validateTwoFactorToken($customer_id, $token);

                if ($return_array['error'] == 0) {
                    $error = FALSE;
                } else {
                    $error_message = $this->error = $return_array['text'];
                }
            } else {
                $this->error = '2FA not enabled';
            }
        } else {
            $this->error = 'Invalid Customer ID';
        }


        $this->code = 200;
        $this->status = true;
        $this->result = array(
            'tfa_enabled' => $tfa_enabled,
            'error' => $error,
            'error_message' => $error_message,
        );
    }
    
    public function actionGenerateEmailSecurityToken() {
        $lastDigit = isset($this->input['extra']['lastDigit']) ? $this->input['extra']['lastDigit'] : "";
        $customer_id = $this->input['cid'];
        $merchant_id = $this->input['merchant'];
        $requestUrl = isset($this->input['request_uri']) ? $this->input['request_uri'] : null;
        $status = 0;
        $message = '';
        $token = '';
        
        $resend_email_attempt_type = 'generate_email_security_token';
        
        $modelCustomersSetting = CustomersSetting::model();
        $tokenVerifyAttempt = $modelCustomersSetting->getTokenVerifyAttempt($customer_id, 0, $resend_email_attempt_type);
        $tries = 0;
        if (isset($tokenVerifyAttempt->same_day_request) && $tokenVerifyAttempt->same_day_request) {
            $tries = $tokenVerifyAttempt->customers_setting_value;
        }
        
        if ($tries > 3) {
            $requestResult = array(
                'res_code' => 2,
            );
        } else {
            $classCustomerSecurityComponent = new CustomerSecurityComponent($merchant_id, 'security_token_request', $customer_id, $this->getCustomersIP());
            $requestResult = $classCustomerSecurityComponent->requestSecurityTokenAgainForEmail($lastDigit);
        }

        if ($requestResult['res_code'] == 1) {
            $token = $requestResult['token'];
            $message = Yii::t('smsToken', 'TEXT_SUCCESSFULLY_SEND_MAIL');
        } else if ($requestResult['res_code'] == 0) {
            if (!isset($tokenVerifyAttempt)) {
                $tokenData = array(
                    'customers_id' => $customer_id,
                    'customers_setting_key' => $resend_email_attempt_type,
                    'customers_setting_value' => '1',
                    'created_datetime' => date("Y-m-d H:i:s"),
                    'updated_datetime' => date("Y-m-d H:i:s")
                );
                $modelCustomersSetting->saveCustomerSetting($tokenData);
            }
            else if ($tokenVerifyAttempt->same_day_request) {
                $tokenData = array(
                    'customers_setting_value' => $tries + 1,
                    'updated_datetime' => date("Y-m-d H:i:s")
                );
                $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $customer_id, $resend_email_attempt_type);
            } else {
                $tokenData = array(
                    'customers_setting_value' => '1',
                    'updated_datetime' => date("Y-m-d H:i:s"),
                    'created_datetime' => date("Y-m-d H:i:s")
                );
                $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $customer_id, $resend_email_attempt_type);
            }
            $message = Yii::t('smsToken', 'ERROR_INVALID_LAST_DIGIT');
        } else {
            $message = Yii::t('smsToken', 'ERROR_TRIED_TOO_MANY_TIMES');
        }

        $this->code = 200;
        $this->status = true;
        $this->result = array(
            'status' => $requestResult['res_code'],
            'message' => $message,
            'token' => $token,
        );
    }

    public function actionGetSellerRegisterFormContent() {
        $country = isset($this->input['extra']['country']) ? $this->input['extra']['country'] : '';

        $this->result = ApiCustomersCom::form($this->input['cid'], $country, "seller_register");
        $this->code = 200;
        $this->status = true;
    }

    public function actionVerifySellerInfo() {
        $data = isset($this->input['extra']) ? $this->input['extra'] : array();

        $this->result = ApiSellerCom::editAccount($this->input['cid'], $data);
        $this->code = 200;
        $this->status = true;
    }

    public function actionIsCustomerNameLocked() {
        $customers_info = Customers::model()->getCustomerCurrentInfo($this->input['cid']);
        $customers_info['name_locked'] = CustomersCom::isCustomerNameLocked($this->input['cid']);
        $this->result = $customers_info;
        $this->code = 200;
        $this->status = true;
    }

    public function actionUpdateCustomerName() {
        $return_array = array();
        $customer_id = $this->input['cid'];
        $data = isset($this->input['extra']) ? $this->input['extra'] : array();
        if (CustomersCom::isCustomerNameLocked($customer_id)) {
            $this->error = 'Customer name is locked';
        } else {
            $return_array = CustomersCom::updateCustomerName($customer_id, $data);
        }

        $this->code = 200;
        $this->status = true;
        $this->result = $return_array;
    }
    
    public function actionLockCustomerName() {
        $customer_id = $this->input['cid'];
        CustomersCom::lockCustomerName($customer_id);
        
        $this->code = 200;
        $this->status = true;
    }

    protected function getCustomersIP() {
        if (!empty($this->input['cust_ip_address'])) {
            return $this->input['cust_ip_address'];
        }
        $user_last_login = \api\models\ApiUserLastLogin::findOne([
            'user_id' => $this->input['cid'],
        ]);
        if ($user_last_login) {
            return $user_last_login->login_ip;
        }
        return null;
    }

}
