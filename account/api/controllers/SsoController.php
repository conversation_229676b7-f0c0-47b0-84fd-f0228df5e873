<?php

namespace api\controllers;

use api\components\ApiGeneralCom;
use api\components\ApiSsoCom;
use api\components\ApiUserIdentityCom;
use api\models\ApiClient;
use api\models\ApiCustomers;
use api\models\ApiShassoYiiSession;
use api\models\ApiSsoToken;
use common\components\CamelCaseActionController;
use common\components\CurlComponent;
use common\models\Customers;
use common\models\CustomersSetting;
use Yii;
use yii\db\Expression;

class SsoController extends CamelCaseActionController {

    public $input = array();
    public $result = array();
    public $status = false;
    public $error;

    public function beforeAction($action) {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if (self::_validateSignature($action->id)) {
            return true;
        }
        \Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ]);
        return false;
    }

    public function afterAction($action, $result) {
        $result = parent::afterAction($action, $result);
        return [
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error,
        ];
    }

    private function _validateSignature($action_id) {
        switch (strtolower($action_id)) {
            case "kickuser":
            case "kick-user":
            case "onlinestatus":
            case "online-status":
            case "securecode":
            case "secure-code":
            case "disable-2fa":
            case "has-2fa":
            case "change-mobile-country":
                $api_obj = new ApiGeneralCom();
                if ($api_obj->_validateSignature($this->input)) {
                    return true;
                }
                break;

            case "xpress":
                if (isset($this->input['service']) && isset($this->input['email']) && isset($this->input['signature'])) {
                    $m_ac = ApiClient::findOne(['client_id' => $this->input['service']]);
                    if (isset($m_ac->client_id) && ($this->input['signature'] == md5($m_ac->client_id . $this->input["email"] . $m_ac->client_secret))) {
                        return true;
                    }
                }
                break;

            default:
                if (isset($this->input['service']) && isset($this->input['S3ID']) && isset($this->input['signature'])) {
                    $m_ac = ApiClient::findOne(['client_id' => $this->input['service']]);
                    if (isset($m_ac->client_id) && ($this->input['signature'] == md5($m_ac->client_id . $this->input['S3ID'] . $m_ac->client_secret))) {
                        return true;
                    }
                }
                break;
        }

        $this->error = 'Invalid signature';
        return false;
    }

    private function _validateToken() {
        // delete every 15min
        if ((date("i") % 15) == 0) {
            ApiSsoToken::deleteAll('expiry < ' . new Expression('NOW()'));
        }

        if (isset($this->input['S3ID'])) {
            if (ApiSsoToken::find()->where([
                'sso_token' => $this->input['S3ID'],
                'client_id' => $this->input['service'],
            ])->exists()) {
                return true;
            }
        }

        $this->error = 'Invalid token';
        return false;
    }

    public function actionKickUser() {
        \common\components\SSOCom::kickUser($this->input["cid"]);
        
        $this->status = true;
    }

    public function actionOnlineStatus() {
        if (ApiShassoYiiSession::doesUserExistInSession($this->input["cid"])) {
            $this->status = true;
        }
    }

    public function actionRequestUid() {
        if (self::_validateToken()) {
            $m_sst = ApiSsoToken::findOne(['sso_token' => $this->input['S3ID'], 'client_id' => $this->input['service']]);
            if (isset($m_sst->sso_token)) {
                ApiSsoToken::deleteAll(['sso_token' => $this->input['S3ID']]);

                $this->status = true;
                $this->result[] = array(
                    'user_id' => $m_sst->user_id,
                    'avatar' => "", // hybridauth avatar
                    'login_method' => $m_sst->login_method
                );
            } else {
                $this->error = 'Invalid token';
            }
        }
    }

    public function actionSecureCode() {
        if (isset($this->input['action']) && isset($this->input['otp_key']) && isset($this->input['cid'])) {
            switch ($this->input['action']) {
                case "read":
                    $result = ApiSsoCom::secureCodeRead($this->input['otp_key'], $this->input['cid']);

                    if (!empty($result)) {
                        $this->status = true;
                        $this->result[] = $result;
                    } else {
                        $this->error = "No record found";
                    }
                    break;

                default:
                    $this->error = "Invalid action";
                    break;
            }
        } else {
            $this->error = "Mandatory data missing";
        }
    }

    public function actionValidateToken() {
        $this->status = self::_validateToken();
    }

    public function actionXpress() {
        /*
         * support normal login / sign-up ONLY, avoid different sns info received among apps
         * e.g. FB Apps-Scoped User ID
         */
        if (isset($this->input['action']) && isset($this->input['email']) && isset($this->input['passwd'])) {
            switch ($this->input['action']) {
                case "register":
                    if (ApiUserIdentityCom::register($this->input)) {
                        $m_cust = ApiCustomers::findOne(['customers_email_address' => $this->input['email'], 'customers_status' => 1]);
                        if (isset($m_cust->customers_id)) {
                            $this->status = true;
                            $this->result[] = array(
                                "user_id" => $m_cust->customers_id,
                                "S3AT" => ApiSsoCom::secureCodeRequest(ApiSsoCom::$otp_key_api_register, $m_cust->customers_id) // API Token
                            );
                        }
                    } else {
                        $this->error = "Fail to create account";
                    }
                    break;

                default:
                    $this->error = "Invalid action";
                    break;
            }
        } else {
            $this->error = "Mandatory data missing";
        }
    }

    public function actionDisable2fa() {
        if (isset($this->input['cid'])) {
            ApiSsoCom::disableTwoFactorAuthentication($this->input["cid"]);
            $this->status = true;
        } else {
            $this->error = "Mandatory data missing";
        }
    }

    public function actionHas2fa() {
        if (isset($this->input['cid'])) {
            $this->status = true;
            $this->result = [
                'tfa_enabled' => ApiSsoCom::doesCustomerHaveTwoFactorAuthentication($this->input["cid"]),
            ];
        } else {
            $this->error = "Mandatory data missing";
        }
    }

    public function actionChangeMobileCountry() {
        if (isset($this->input['cid'])) {
            $m_cust = Customers::findOne(['customers_id' => $this->input['cid']]);
            if ($m_cust) {
                \common\components\SSOCom::clientLocalizationInvalidate($m_cust);
            }
        }
    }
}