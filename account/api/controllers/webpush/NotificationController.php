<?php

namespace api\controllers\webpush;

use api\components\ApiGeneralCom;
use api\components\webpush\ApiWebpushNotificationCom;
use common\components\CamelCaseActionController;
use common\components\AwsClient;
use Yii;
use yii\helpers\BaseJson;

class NotificationController extends CamelCaseActionController
{
    public $input = array();
    public $result = array();
    public $status = false;
    public $error;

    public function beforeAction($action)
    {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if ($api_obj->_validateSignatureWebpush($action->id, $this->input)) {
            return true;
        } else {
            $this->error = 'Invalid signature';
        }

        Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ]);

        return false;
    }

    public function afterAction($action, $result)
    {
        $api_obj = new ApiGeneralCom();

        $api_obj->_sendResponse(array(
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ));

        return true;
    }

    public function actionIndex()
    {
        $objNotification = new ApiWebpushNotificationCom($this->input['merchant']);
        $data = $objNotification->getNotificationList($this->input);

        $this->status = true;
        $this->result = $data;
    }

    public function actionView()
    {
        $objNotification = new ApiWebpushNotificationCom($this->input['merchant']);
        $notificationArray = $objNotification->getNotification($this->input['id']);

        $this->returnResponse($notificationArray);
    }

    public function actionCreate()
    {
        $objNotification = new ApiWebpushNotificationCom($this->input['merchant']);
        // Check update or create
        if (isset($this->input['id']) && !empty($this->input['id'])) {
            // Update process
            $notificationArray = $objNotification->updateNotification($this->input);
        } else {
            // Create new process
            $notificationArray = $objNotification->saveNotification($this->input);
        }

        $this->returnResponse($notificationArray);
    }

    public function actionUpdate()
    {
        $objNotification = new ApiWebpushNotificationCom($this->input['merchant']);
        $notificationArray = $objNotification->updateNotification($this->input);

        $this->returnResponse($notificationArray);
    }

    public function actionDelete()
    {
        $objNotification = new ApiWebpushNotificationCom($this->input['merchant']);
        $notificationArray = $objNotification->deleteNotification($this->input);

        $this->returnResponse($notificationArray);
    }

    private function returnResponse($responseArray)
    {
        if (isset($responseArray['error']) && $responseArray['error']) {
            $this->status = false;
            $this->error = $responseArray;
        } else {
            $this->status = true;
            $this->result = $responseArray;
        }
    }
}
