<?php

namespace api\controllers\webpush;

use api\components\ApiGeneralCom;
use api\components\webpush\ApiWebpushTokenCom;
use common\components\CamelCaseActionController;
use Yii;
use yii\helpers\BaseJson;

class TokenController extends CamelCaseActionController
{
    public $input = array();
    public $result = array();
    public $status = false;
    public $error;

    public function beforeAction($action)
    {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if ($api_obj->_validateSignatureWebpush($action->id, $this->input)) {
            return true;
        } else {
            $this->error = 'Invalid signature';
        }

        Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ]);
        
        return false;
    }

    public function afterAction($action, $result)
    {
        $api_obj = new ApiGeneralCom();

        $api_obj->_sendResponse(array(
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ));

        return true;
    }

    public function actionIndex()
    {
        $objToken = new ApiWebpushTokenCom($this->input['merchant']);
        $data = $objToken->getTokenList($this->input);

        $this->status = true;
        $this->result = $data;
    }

    public function actionView()
    {
        $objToken = new ApiWebpushTokenCom($this->input['merchant']);
        $data = $objToken->getToken($this->input['id']);
        
        $this->status = true;
        $this->result = $data;
    }

    public function actionCreate()
    {
        $objToken = new ApiWebpushTokenCom($this->input['merchant']);
        $tokenArray = $objToken->saveToken($this->input);

        if (!isset($tokenArray['error'])) {
            $this->status = true;
            $this->result = $tokenArray;
        } else {
            $this->error = $tokenArray['error'];
            return false;
        }
    }

    public function actionUpdate()
    {
        $objToken = new ApiWebpushTokenCom($this->input['merchant']);
        $tokenArray = $objToken->updateToken($this->input);

        if ($tokenArray) {
            $this->status = true;
            $this->result = $tokenArray;
        } else {
            $this->error = BaseJson::encode($this->input);
            return false;
        }
    }

    public function actionDelete()
    {
        $objToken = new ApiWebpushTokenCom($this->input['merchant']);
        $tokenArray = $objToken->deleteToken($this->input);

        if ($tokenArray) {
            $this->status = true;
            $this->result = $tokenArray;
        } else {
            $this->error = BaseJson::encode($this->input);
            return false;
        }
    }
}
