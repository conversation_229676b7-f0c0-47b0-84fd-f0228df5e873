<?php

namespace api\controllers\webpush;

use api\components\ApiGeneralCom;
use api\components\webpush\ApiWebpushTopicCom;
use common\components\CamelCaseActionController;
use Yii;
use yii\helpers\BaseJson;

class TopicController extends CamelCaseActionController
{
    public $input = array();
    public $result = array();
    public $status = false;
    public $error;

    public function beforeAction($action)
    {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        if ($api_obj->_validateSignatureWebpush($action->id, $this->input)) {
            return true;
        } else {
            $this->error = 'Invalid signature';
        }

        Yii::$app->response->data = $api_obj->removeInvalidCharsFromArray([
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ]);
        
        return false;
    }

    public function afterAction($action, $result)
    {
        $api_obj = new ApiGeneralCom();

        $api_obj->_sendResponse(array(
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ));

        return true;
    }

    public function actionIndex()
    {
        $objTopic = new ApiWebpushTopicCom($this->input['merchant']);
        $data = $objTopic->getTopicList($this->input);

        $this->status = true;
        $this->result = $data;
    }

    public function actionView()
    {
        $objTopic = new ApiWebpushTopicCom($this->input['merchant']);
        $topicArray = $objTopic->getTopic($this->input['id']);

        $this->returnResponse($topicArray);
    }

    public function actionCreate()
    {
        $objTopic = new ApiWebpushTopicCom($this->input['merchant']);
        $topicArray = $objTopic->saveTopic($this->input);

        $this->returnResponse($topicArray);
    }

    public function actionUpdate()
    {
        $objTopic = new ApiWebpushTopicCom($this->input['merchant']);
        $topicArray = $objTopic->updateTopic($this->input);

        $this->returnResponse($topicArray);
    }

    public function actionDelete()
    {
        $objTopic = new ApiWebpushTopicCom($this->input['merchant']);
        $topicArray = $objTopic->deleteTopic($this->input);

        $this->returnResponse($topicArray);
    }

    private function returnResponse($responseArray)
    {
        if (isset($responseArray['error']) && $responseArray['error']) {
            $this->status = false;
            $this->error = $responseArray;
        } else {
            $this->status = true;
            $this->result = $responseArray;
        }
    }
}
