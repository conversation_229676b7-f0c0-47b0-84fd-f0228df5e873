<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "client".
 *
 * The followings are the available columns in table 'client':
 * @property string $client_id
 * @property string $client_name
 * @property string $client_secret
 * @property string $redirect_url
 * @property string $set_token_url
 * @property string $delete_token_url
 * @property string $kick_user_url
 * @property string $api_url
 */
class ApiClient extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%client}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['client_id', 'client_name', 'client_secret'], 'required'],
            [['client_id', 'client_name', 'client_secret'], 'string', 'max' => 40],
            [['redirect_url', 'set_token_url', 'delete_token_url', 'kick_user_url','api_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'client_id' => 'Client',
            'client_name' => 'Client Name',
            'client_secret' => 'Client Secret',
            'redirect_url' => 'Redirect Url',
            'set_token_url' => 'Set Token Url',
            'delete_token_url' => 'Delete Token Url',
            'kick_user_url' => 'Kick User Url',
            'api_url' => 'Api Url',
        ];
    }
    
}