<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "countries".
 *
 * The followings are the available columns in table 'countries':
 * @property integer $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property integer $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property integer $address_format_id
 * @property integer $maxmind_support
 * @property string $aft_risk_type
 * @property integer $countries_display
 * @property integer $telesign_support
 */
class ApiCountries extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%countries}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['countries_currencies_id', 'address_format_id', 'maxmind_support', 'countries_display', 'telesign_support'], 'integer'],
            [['countries_name', 'countries_website_domain'], 'string', 'max' => 64],
            [['countries_iso_code_2'], 'string', 'max' => 2],
            [['countries_iso_code_3'], 'string', 'max' => 3],
            [['countries_international_dialing_code'], 'string', 'max' => 5],
            [['aft_risk_type'], 'string', 'max' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'countries_id' => 'Countries',
            'countries_name' => 'Countries Name',
            'countries_iso_code_2' => 'Countries Iso Code 2',
            'countries_iso_code_3' => 'Countries Iso Code 3',
            'countries_currencies_id' => 'Countries Currencies',
            'countries_international_dialing_code' => 'Countries International Dialing Code',
            'countries_website_domain' => 'Countries Website Domain',
            'address_format_id' => 'Address Format',
            'maxmind_support' => 'Maxmind Support',
            'aft_risk_type' => 'Aft Risk Type',
            'countries_display' => 'Countries Display',
            'telesign_support' => 'Telesign Support',
        ];
    }
    
}
