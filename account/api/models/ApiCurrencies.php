<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "currencies".
 *
 * The followings are the available columns in table 'currencies':
 * @property integer $currencies_id
 * @property string $title
 * @property string $code
 * @property string $symbol_left
 * @property string $symbol_right
 * @property string $decimal_point
 * @property string $thousands_point
 * @property string $decimal_places
 * @property string $value
 * @property string $buy_value
 * @property string $buy_value_adjust
 * @property string $sell_value
 * @property string $sell_value_adjust
 * @property integer $currencies_live_update
 * @property string $currencies_used_for
 * @property string $last_updated
 */
class ApiCurrencies extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%currencies}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['currencies_live_update'], 'integer'],
            [['value', 'buy_value', 'sell_value'], 'string', 'max' => 13],
            [['buy_value_adjust', 'sell_value_adjust'], 'string', 'max' => 7],
            [['last_updated'], 'safe'],
            [['title', 'currencies_used_for'], 'string', 'max' => 32],
            [['code'], 'string', 'max' => 3],
            [['symbol_left', 'symbol_right'], 'string', 'max' => 12],
            [['decimal_point', 'thousands_point', 'decimal_places'], 'string', 'max' => 1],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'currencies_id' => 'Currencies',
            'title' => 'Title',
            'code' => 'Code',
            'symbol_left' => 'Symbol Left',
            'symbol_right' => 'Symbol Right',
            'decimal_point' => 'Decimal Point',
            'thousands_point' => 'Thousands Point',
            'decimal_places' => 'Decimal Places',
            'value' => 'Value',
            'buy_value' => 'Buy Value',
            'buy_value_adjust' => 'Buy Value Adjust',
            'sell_value' => 'Sell Value',
            'sell_value_adjust' => 'Sell Value Adjust',
            'currencies_live_update' => 'Currencies Live Update',
            'currencies_used_for' => 'Currencies Used For',
            'last_updated' => 'Last Updated',
        ];
    }
}