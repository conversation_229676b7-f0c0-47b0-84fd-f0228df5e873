<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "customers".
 *
 * The followings are the available columns in table 'customers':
 * @property integer $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_dob
 * @property integer $account_activated
 * @property string $customers_email_address
 * @property integer $email_verified
 * @property string $serial_number
 * @property integer $customers_default_address_id
 * @property integer $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string $customers_newsletter
 * @property string $customers_group_name
 * @property integer $customers_group_id
 * @property string $customers_discount
 * @property integer $customers_groups_id
 * @property string $customers_aft_groups_id
 * @property integer $customers_status
 * @property string $customers_flag
 * @property integer $customers_phone_verified
 * @property string $customers_phone_verified_by
 * @property string $customers_phone_verified_datetime
 * @property integer $affiliate_ref_id
 * @property integer $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property string $customers_reserve_amount
 * @property string $customers_security_start_time
 * @property integer $customers_disable_withdrawal
 */
class ApiCustomers extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_dob', 'customers_phone_verified_datetime', 'customers_security_start_time'], 'safe'],
            [['account_activated', 'email_verified', 'customers_default_address_id', 'customers_country_dialing_code_id', 'customers_group_id', 'customers_groups_id', 'customers_status', 'customers_phone_verified', 'affiliate_ref_id', 'ref_id', 'customers_disable_withdrawal'], 'integer'],
            [['customers_discount'], 'string', 'max' => 8],
            [['customers_reserve_amount'], 'string', 'max' => 15],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname', 'customers_telephone', 'customers_mobile', 'customers_fax', 'customers_login_sites'], 'string', 'max' => 32],
            [['customers_email_address', 'customers_msn', 'customers_yahoo'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_qq', 'customers_icq'], 'string', 'max' => 20],
            [['customers_password', 'customers_pin_number'], 'string', 'max' => 40],
            [['customers_newsletter', 'customers_phone_verified_by', 'customers_merged_profile'], 'string', 'max' => 255],
            [['customers_group_name'], 'string', 'max' => 27],
            [['customers_flag'], 'string', 'max' => 10],
            [['customers_aft_groups_id'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_gender' => 'Customers Gender',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_dob' => 'Customers Dob',
            'account_activated' => 'Account Activated',
            'customers_email_address' => 'Customers Email Address',
            'email_verified' => 'Email Verified',
            'serial_number' => 'Serial Number',
            'customers_default_address_id' => 'Customers Default Address',
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'customers_mobile' => 'Customers Mobile',
            'customers_fax' => 'Customers Fax',
            'customers_msn' => 'Customers Msn',
            'customers_qq' => 'Customers Qq',
            'customers_yahoo' => 'Customers Yahoo',
            'customers_icq' => 'Customers Icq',
            'customers_password' => 'Customers Password',
            'customers_pin_number' => 'Customers Pin Number',
            'customers_newsletter' => 'Customers Newsletter',
            'customers_group_name' => 'Customers Group Name',
            'customers_group_id' => 'Customers Group',
            'customers_discount' => 'Customers Discount',
            'customers_groups_id' => 'Customers Groups',
            'customers_aft_groups_id' => 'Customers Aft Groups',
            'customers_status' => 'Customers Status',
            'customers_flag' => 'Customers Flag',
            'customers_phone_verified' => 'Customers Phone Verified',
            'customers_phone_verified_by' => 'Customers Phone Verified By',
            'customers_phone_verified_datetime' => 'Customers Phone Verified Datetime',
            'affiliate_ref_id' => 'Affiliate Ref',
            'ref_id' => 'Ref',
            'customers_merged_profile' => 'Customers Merged Profile',
            'customers_login_sites' => 'Customers Login Sites',
            'customers_reserve_amount' => 'Customers Reserve Amount',
            'customers_security_start_time' => 'Customers Security Start Time',
            'customers_disable_withdrawal' => 'Customers Disable Withdrawal',
        ];
    }
    
}