<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "customers_groups".
 *
 * The followings are the available columns in table 'customers_groups':
 * @property integer $customers_groups_id
 * @property string $customers_groups_name
 * @property string $customers_groups_legend_color
 * @property string $customers_groups_payment_methods
 * @property string $customers_groups_extra_sc
 * @property integer $sort_order
 */
class ApiCustomersGroups extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_groups}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_groups_payment_methods'], 'required'],
            [['customers_groups_extra_sc'], 'string', 'max' => 8],
            [['sort_order'], 'integer'],
            [['customers_groups_name'], 'string', 'max' => 32],
            [['customers_groups_legend_color'], 'string', 'max' => 7],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_groups_id' => 'Customers Groups',
            'customers_groups_name' => 'Customers Groups Name',
            'customers_groups_legend_color' => 'Customers Groups Legend Color',
            'customers_groups_payment_methods' => 'Customers Groups Payment Methods',
            'customers_groups_extra_sc' => 'Customers Groups Extra Sc',
            'sort_order' => 'Sort Order',
        ];
    }
    
}