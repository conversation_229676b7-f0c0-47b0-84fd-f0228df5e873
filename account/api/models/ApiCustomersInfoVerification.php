<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "customers_info_verification".
 *
 * The followings are the available columns in table 'customers_info_verification':
 * @property integer $customers_id
 * @property string $customers_info_value
 * @property string $serial_number
 * @property integer $verify_try_turns
 * @property integer $info_verified
 * @property string $info_verification_type
 * @property string $customers_info_verification_mode
 * @property string $customers_info_verification_date
 * @property string $call_language
 */
class ApiCustomersInfoVerification extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_info_verification}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'verify_try_turns', 'info_verified'], 'integer'],
            [['customers_info_verification_date'], 'safe'],
            [['customers_info_value'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_info_verification_mode'], 'string', 'max' => 1],
            [['info_verification_type'], 'string', 'max' => 32],
            [['call_language'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_info_value' => 'Customers Info Value',
            'serial_number' => 'Serial Number',
            'verify_try_turns' => 'Verify Try Turns',
            'info_verified' => 'Info Verified',
            'info_verification_type' => 'Info Verification Type',
            'customers_info_verification_mode' => 'Customers Info Verification Mode',
            'customers_info_verification_date' => 'Customers Info Verification Date',
            'call_language' => 'Call Language',
        ];
    }
    
}