<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "instant_message_accounts".
 *
 * The followings are the available columns in table 'instant_message_accounts':
 * @property integer $instant_message_accounts_id
 * @property integer $customer_id
 * @property integer $instant_message_type_id
 * @property string $instant_message_userid
 * @property string $instant_message_remarks
 * 
 * @property ApiInstantMessageType $instant_message_type
 */
class ApiInstantMessageAccounts extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%instant_message_accounts}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'instant_message_type_id'], 'integer'],
            [['instant_message_userid', 'instant_message_remarks'], 'string', 'max' => 96],
            [['instant_message_type_id', 'instant_message_userid'], 'required'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'instant_message_accounts_id' => 'Instant Message Accounts',
            'customer_id' => 'Customer',
            'instant_message_type_id' => 'Instant Message Type',
            'instant_message_userid' => 'Instant Message Userid',
            'instant_message_remarks' => 'Instant Message Remarks',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getInstantMessageType() {
        return $this->hasOne(ApiInstantMessageType::className(), ['instant_message_type_id' => 'instant_message_type_id']);
    }
}
