<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "instant_message_type".
 *
 * The followings are the available columns in table 'instant_message_type':
 * @property integer $instant_message_type_id
 * @property string $instant_message_type_name
 * @property string $instant_message_type_description
 * @property integer $instant_message_type_order
 */
class ApiInstantMessageType extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%instant_message_type}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['instant_message_type_order'], 'integer'],
            [['instant_message_type_name'], 'string', 'max' => 96],
            [['instant_message_type_description'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'instant_message_type_id' => 'Instant Message Type',
            'instant_message_type_name' => 'Instant Message Type Name',
            'instant_message_type_description' => 'Instant Message Type Description',
            'instant_message_type_order' => 'Instant Message Type Order',
        ];
    }
    
}
