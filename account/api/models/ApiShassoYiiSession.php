<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "shasso_yii_session".
 *
 * The followings are the available columns in table 'shasso_yii_session':
 * @property string $id
 * @property string $user_id
 * @property integer $expire
 * @property string $data
 */
class ApiShassoYiiSession extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%shasso_yii_session}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['expire'], 'integer'],
            [['data'], 'string'],
            [['id'], 'string', 'max' => 32],
            [['user_id'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User',
            'expire' => 'Expire',
            'data' => 'Data',
        ];
    }

    public static function doesUserExistInSession($user_id) {
        if (\Yii::$app->params['session.provider'] == 'mysql') {
            return ApiShassoYiiSession::find()->where(['user_id' => $user_id])->exists();
        }
    }
}