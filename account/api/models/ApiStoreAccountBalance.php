<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "store_account_balance".
 *
 * The followings are the available columns in table 'store_account_balance':
 * @property integer $user_id
 * @property string $user_role
 * @property string $store_account_balance_currency
 * @property string $store_account_balance_amount
 * @property string $store_account_reserve_amount
 * @property string $store_account_po_wsc
 * @property string $store_account_credit_note_amount
 * @property string $store_account_last_modified
 */
class ApiStoreAccountBalance extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_account_balance}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id'], 'integer'],
            [['store_account_last_modified'], 'safe'],
            [['user_role'], 'string', 'max' => 16],
            [['store_account_balance_currency'], 'string', 'max' => 3],
            [['store_account_balance_amount', 'store_account_reserve_amount', 'store_account_po_wsc', 'store_account_credit_note_amount'], 'string', 'max' => 15],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User',
            'user_role' => 'User Role',
            'store_account_balance_currency' => 'Store Account Balance Currency',
            'store_account_balance_amount' => 'Store Account Balance Amount',
            'store_account_reserve_amount' => 'Store Account Reserve Amount',
            'store_account_po_wsc' => 'Store Account Po Wsc',
            'store_account_credit_note_amount' => 'Store Account Credit Note Amount',
            'store_account_last_modified' => 'Store Account Last Modified',
        ];
    }
    
}