<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "store_points".
 *
 * The followings are the available columns in table 'store_points':
 * @property integer $customers_id
 * @property string $sp_amount
 * @property string $sp_last_modified
 */
class ApiStorePoints extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_points}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id'], 'integer'],
            [['sp_amount'], 'string', 'max' => 15],
            [['sp_last_modified'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'sp_amount' => 'Sp Amount',
            'sp_last_modified' => 'Sp Last Modified',
        ];
    }
    
}