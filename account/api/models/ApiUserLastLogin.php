<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "{{%user_last_login}}".
 *
 * @property int $user_id
 * @property string $login_date
 * @property string $login_ip
 * @property string $login_ip_iso2
 */
class ApiUserLastLogin extends CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user_last_login}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'login_date', 'login_ip', 'login_ip_iso2'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['login_ip'], 'string', 'max' => 128],
            [['login_ip_iso2'], 'string', 'max' => 2],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User',
            'login_date' => 'Login Date',
            'login_ip' => 'Login Ip',
            'login_ip_iso2' => 'Login Ip Iso2',
        ];
    }
}
