<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "user_token".
 *
 * The followings are the available columns in table 'user_token':
 * @property string $user_id
 * @property string $token_type
 * @property string $token_value
 * @property string $created_date
 * @property string $expiry_date
 */
class ApiUserToken extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user_token}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'token_type', 'token_value'], 'required'],
            [['user_id'], 'integer'],
            [['created_date', 'expiry_date'], 'safe'],
            [['token_type', 'token_value'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User',
            'token_type' => 'Token Type',
            'token_value' => 'Token Value',
            'created_date' => 'Created Date',
            'expiry_date' => 'Expiry Date',
        ];
    }

}