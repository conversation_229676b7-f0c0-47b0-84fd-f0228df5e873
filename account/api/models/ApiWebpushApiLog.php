<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "api_log".
 *
 * The followings are the available columns in table 'api_log':
 * @property integer $id
 * @property integer $push_id
 * @property string $client_id
 * @property string $api_endpoint
 * @property string $request
 * @property string $response
 * @property string $created_at
 * @property string $updated_at
 */
class ApiWebpushApiLog extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%api_log}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpushmb4');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['push_id', 'client_id', 'api_endpoint', 'request'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['push_id'], 'integer'],
            [['client_id'], 'string', 'max' => 40],
            [['api_endpoint'], 'string', 'max' => 255],
            [['request', 'response'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'API Log Id',
            'push_id' => 'Push Id',
            'client_id' => 'Client Id',
            'api_endpoint' => 'API Endpoint',
            'request' => 'API Log Request',
            'response' => 'API Log Response',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
        ];
    }

    public function saveApiLog($pushId, $merchant, $apiEndpoint, $request)
    {
        $this->push_id = $pushId;
        $this->client_id = $merchant;
        $this->api_endpoint = $apiEndpoint;
        $this->request = $request;
        $this->created_at = time();
        $this->save();
    }

    public function updateApiLog($response)
    {
        $this->response = $response;
        $this->updated_at = time();
        $this->update();
    }
    
}