<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "user".
 *
 * The followings are the available columns in table 'user':
 * @property string $id
 * @property string $secret
 * @property string $server_key
 * @property string $sender_id
 * @property integer $status
 * @property string $created_at
 * @property string $created_by
 */
class ApiWebpushClient extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%client}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'secret'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['id'], 'string', 'max' => 40],
            [['secret'], 'string', 'max' => 64],
            [['server_key', 'sender_id', 'created_by'], 'string'],
            [['status'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Client Id',
            'secret' => 'Client Secret',
            'server_key' => 'Firebase Server Key',
            'sender_id' => 'Firebase Sender Id',
            'status' => 'Client Webpush Status',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
            'created_by' => 'Created By',
        ];
    }
    
}