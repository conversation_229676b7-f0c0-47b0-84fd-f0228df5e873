<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "cron_topic_refresh".
 *
 * The followings are the available columns in table 'cron_topic_refresh':
 * @property string $id
 * @property integer $cron_process_track_in_action
 * @property integer $cron_process_track_failed_attempt
 * @property integer $cron_process_track_start_date
 * @property string $cron_process_track_filename
 */
class ApiWebpushCronProcessTrack extends CoreModel {

    public $overdue_process;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%cron_process_track}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['cron_process_track_filename'], 'required'],
            [['cron_process_track_start_date'], 'safe'],
            [['cron_process_track_in_action', 'cron_process_track_failed_attempt'], 'integer'],
            [['cron_process_track_filename'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Cron Topic Refresh Id',
            'cron_process_track_in_action' => 'Cron Process Track in Action',
            'cron_process_track_failed_attempt' => 'Cron Process Track Failed Attempt',
            'cron_process_track_start_date' => 'Cron Process Track Start Date',
            'cron_process_track_filename' => 'Cron Process Track Filename',
        ];
    }
    
}