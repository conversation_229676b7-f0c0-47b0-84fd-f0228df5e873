<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "cron_topic_refresh".
 *
 * The followings are the available columns in table 'cron_topic_refresh':
 * @property string $id
 * @property string $client_id
 * @property integer $topic_id
 * @property integer $new_start_id
 * @property integer $cron_last_process_date
 * @property integer $cron_last_process_count
 * @property integer $cron_process_track_in_action
 * @property integer $cron_process_track_failed_attempt
 * @property integer $created_at
 * @property string $created_by
 */
class ApiWebpushCronTopicRefresh extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%cron_topic_refresh}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['client_id', 'topic_id', 'new_start_id'], 'required'],
            [['cron_last_process_date', 'created_at'], 'safe'],
            [['cron_process_track_in_action', 'cron_process_track_failed_attempt'], 'integer'],
            [['topic_id', 'new_start_id', 'cron_last_process_count', 'cron_process_track_in_action', 'cron_process_track_failed_attempt'], 'integer'],
            [['created_by'], 'string', 'max' => 255],
            [['client_id'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Cron Topic Refresh Id',
            'client_id' => 'Client Id',
            'topic_id' =>'Topic Id',
            'new_start_id' => 'New Start Id',
            'cron_last_process_date' => 'Cron Last Process Date',
            'cron_last_process_count' => 'Cron Last Process Count',
            'cron_process_track_in_action' => 'Cron Process Track in Action',
            'cron_process_track_failed_attempt' => 'Cron Process Track Failed Attempt',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
        ];
    }
    
}