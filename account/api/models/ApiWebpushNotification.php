<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "notification".
 *
 * The followings are the available columns in table 'notification':
 * @property integer $id
 * @property integer $type
 * @property string $recipient
 * @property string $title
 * @property string $message
 * @property string $icon
 * @property string $url
 * @property integer $click_action
 * @property string $image
 * @property integer $notification_status
 * @property integer $send_status
 * @property string $client_id
 * @property string $created_at
 * @property string $updated_at
 * @property string $executed_at
 * @property string $schedule_at
 * @property string $created_by
 */
class ApiWebpushNotification extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%notification}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpushmb4');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['type', 'recipient', 'title', 'message', 'client_id', 'created_by'], 'required'],
            [['type', 'click_action', 'notification_status', 'send_status'], 'integer'],
            [['created_at', 'updated_at', 'executed_at', 'schedule_at'], 'safe'],
            [['recipient', 'title', 'message', 'icon', 'url', 'image', 'created_by'], 'string', 'max' => 255],
            [['client_id'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Notification Id',
            'type' => 'Notification Type',
            'recipient' => 'Notification Recipient',
            'title' => 'Notification Title',
            'message' => 'Notification Message',
            'icon' => 'Notification Icon',
            'url' => 'Notification Url',
            'click_action' => 'Notification Click Action',
            'image' => 'Notification Image',
            'notification_status' => 'Notification Status',
            'send_status' => 'Notification Send Status',
            'client_id' => 'Client Id',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
            'executed_at' => 'Executed Date',
            'schedule_at' => 'Schedule Date',
            'created_at' => 'Created By',
        ];
    }
    
    public function getApiWebpushSchedule()
    {
        return $this->hasOne(ApiWebpushSchedule::className(), ['push_id' => 'id']);
    }
}