<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "schedule".
 *
 * The followings are the available columns in table 'schedule':
 * @property integer $push_id
 * @property integer $push_status
 * @property string $schedule_at
 * @property string $created_at
 * @property string $updated_at
 * @property string $executed_at
 */
class ApiWebpushSchedule extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%schedule}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['push_id', 'schedule_at'], 'required'],
            [['created_at', 'updated_at', 'executed_at'], 'safe'],
            [['push_id', 'push_status'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'push_id' => 'Push Id',
            'push_status' => 'Push Status',
            'schedule_at' => 'Schedule Date',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
            'executed_at' => 'Executed Date',
        ];
    }

    public function getApiWebpushNotification()
    {
        return $this->hasOne(ApiWebpushNotification::className(), ['id' => 'push_id']);
    }
    
}