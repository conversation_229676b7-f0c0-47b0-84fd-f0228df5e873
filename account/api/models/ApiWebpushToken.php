<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "token".
 *
 * The followings are the available columns in table 'token':
 * @property string $id
 * @property integer $user_id
 * @property string $client_id
 * @property string $created_at
 * @property string $updated_at
 */
class ApiWebpushToken extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%token}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'client_id'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['id'], 'string', 'max' => 255],
            [['user_id'], 'integer'],
            [['client_id'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Token',
            'user_id' => 'User Id',
            'client_id' => 'Client Id',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
        ];
    }
    
}