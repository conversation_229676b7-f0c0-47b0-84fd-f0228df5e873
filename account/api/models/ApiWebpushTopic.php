<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "topic".
 *
 * The followings are the available columns in table 'topic':
 * @property integer $id
 * @property integer $type
 * @property string $title
 * @property string $description
 * @property string $code
 * @property string $topic_default
 * @property string $client_id
 * @property string $created_at
 * @property string $updated_at
 * @property string $created_by
 * @property integer $token_counts
 * @property string $topic_filter
 * @property integer $refresh
 */
class ApiWebpushTopic extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%topic}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['type', 'title', 'code', 'client_id'], 'required'],
            [['code'], 'unique'],
            [['type', 'token_counts', 'topic_default', 'refresh'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['title'], 'string', 'max' => 125],
            [['description', 'created_by'], 'string', 'max' => 255],
            [['code'], 'string', 'max' => 32],
            [['client_id'], 'string', 'max' => 40],
            [['topic_filter'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Topic',
            'type' => 'Topic Type',
            'title' => 'Topic Title',
            'description' => 'Topic Description',
            'code' => 'Topic Code',
            'topic_default' => 'Topic Default',
            'client_id' => 'Client Id',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
            'created_at' => 'Created By',
            'token_counts' => 'Topic Token Counts',
            'topic_filter' => 'Topic Filter',
            'refresh' => 'Topic Refresh Status',
        ];
    }
    
}