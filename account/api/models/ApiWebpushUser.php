<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "user".
 *
 * The followings are the available columns in table 'user':
 * @property integer $id
 * @property string $client_id
 * @property string $topic_subscriptions
 * @property string $created_at
 * @property string $updated_at
 */
class ApiWebpushUser extends CoreModel {

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbwebpush');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'client_id'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['id'], 'integer'],
            [['client_id'], 'string', 'max' => 40],
            [['topic_subscriptions'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'User Id',
            'client_id' => 'Client Id',
            'topic_subscriptions' => 'Topic Subscriptions',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
        ];
    }
    
}