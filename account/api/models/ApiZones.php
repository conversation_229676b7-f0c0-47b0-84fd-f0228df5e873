<?php

namespace api\models;

use Yii;
use common\components\CoreModel;

/**
 * This is the model class for table "zones".
 *
 * The followings are the available columns in table 'zones':
 * @property integer $zone_id
 * @property integer $zone_country_id
 * @property string $zone_code
 * @property string $zone_name
 */
class ApiZones extends CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%zones}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['zone_country_id'], 'integer'],
            [['zone_code'], 'string', 'max' => 32],
            [['zone_name'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'zone_id' => 'Zone',
            'zone_country_id' => 'Zone Country',
            'zone_code' => 'Zone Code',
            'zone_name' => 'Zone Name',
        ];
    }
    
}
