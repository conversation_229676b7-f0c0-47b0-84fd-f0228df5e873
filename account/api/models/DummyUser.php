<?php

namespace api\models;

use Yii;
use yii\db\ActiveRecord;
use yii\web\IdentityInterface;

class DummyUser extends ActiveRecord implements IdentityInterface {

    public function getAuthKey() {
        return null;
    }

    public function getId() {
        return null;
    }

    public function validateAuthKey($authKey) {
        return null;
    }

    public static function findIdentity($id) {
        return null;
    }

    public static function findIdentityByAccessToken($token, $type = null) {
        return null;
    }

}
