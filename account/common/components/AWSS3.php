<?php

namespace common\components;

use \Yii;
use yii\base\InvalidConfigException;

//http://docs.aws.amazon.com/aws-sdk-php/v3/api/api-s3-2006-03-01.html

class AWSS3 {

    const S3_ACL_PRIVATE = 'private';
    const S3_ACL_PUBLIC_READ = 'public-read';
    const S3_ACL_PUBLIC_READ_WRITE = 'public-read-write';
    const S3_ACL_AUTH_READ = 'authenticated-read';
    const S3_ACL_BUCKET_OWNER_READ = 'bucket-owner-read';
    const S3_ACL_BUCKET_OWNER_FULL = 'bucket-owner-full-control';

    /**
     * @var \common\components\AWS
     */
    private $_aws;

    /**
     * @var \Aws\S3\S3Client
     */
    private $_instance;

    /**
     * @var string
     */
    private $bucket_key;

    /**
     * @var string
     */
    private $acl = self::S3_ACL_PRIVATE;

    /**
     * @var string
     */
    private $bucket_tag;

    /**
     * @var string
     */
    private $prefix_path = '';
    
    /**
     * @var string
     */
    private $storage = '';
    

    public function __construct(\common\components\AWS $aws, $bucket_tag, $config = false) {
        if (empty($bucket_tag)) {
            throw new InvalidConfigException("S3 bucket tag cannot be empty!");
        }
        if (!isset($aws)) {
            $aws = Yii::$app->aws;
            if ($config === false && isset($aws->s3[$bucket_tag])) {
                $config = $aws->s3[$bucket_tag];
            }
        }
        if ($config === false) {
            $config = [];
        }
        $this->_aws = $aws;
        if (empty($config['bucket_key'])) {
            throw new InvalidConfigException("S3 bucket key cannot be empty for '" . $bucket_tag . "'!");
        }
        $this->bucket_tag = $bucket_tag;
        $this->bucket_key = $config['bucket_key'];
        $this->acl = !empty($config['acl']) ? $config['acl'] : $this->acl;
        $this->prefix_path = !empty($config['prefix_path']) ? trim($config['prefix_path'], '/') . '/' : $this->prefix_path;
        $extra_args = [];
        if (!empty($config['region'])) {
            $extra_args['region'] = $config['region'];
        }
        if (Yii::$app->params['USE_PROXY'] && !empty(Yii::$app->params['PROXY_HOST'])) {
            $extra_args['http'] = [
                'proxy' => Yii::$app->params['PROXY_HOST'] . ':' . Yii::$app->params['PROXY_PORT'],
            ];
        }
        if (!empty($config['key'])) {
            $extra_args['credentials']['key'] = $config['key'];
        }
        if (!empty($config['secret'])) {
            $extra_args['credentials']['secret'] = $config['secret'];
        }
        $this->_instance = $aws->getSdk()->createS3($extra_args);
    }

    /**
     * @return \Aws\S3\S3Client the instance
     */
    public function getInstance() {
        return $this->_instance;
    }

    /**
     * @param string $path AmazonS3 path
     * @param string $filename Target filename
     * @param string $acl - private|public-read|public-read-write|authenticated-read|bucket-owner-read|bucket-owner-full-control
     * @param int $cache_duration Cache duration in seconds, null means no cache
     * 
     * @return boolean|string filename if successful, false if failed
     */
    public function saveFile($path, $filename, $acl = false, $cache_duration = null, $options = []) {
        if (!$this->_instance) {
            return;
        }
        $acl = $this->parseAcl($acl);
        if (file_exists($filename)) {
            $res = fopen($filename, 'r');
            return $this->putObject($path, $res, $acl, $cache_duration, $options);
        } else {
            $this->reportError(__FUNCTION__, "File not found: " . $filename);
        }
        return false;
    }

    /**
     * @param string $path AmazonS3 path
     * @param string $content Content
     * @param string $acl private|public-read|public-read-write|authenticated-read|bucket-owner-read|bucket-owner-full-control
     * @param int $cache_duration Cache duration in seconds, null means no cache
     * 
     * @return string|boolean content url if successful, false if failed
     */
    public function saveContent($path, $content, $acl = false, $cache_duration = null, $options = []) {
        if (!$this->_instance) {
            return;
        }
        $acl = $this->parseAcl($acl);
        return $this->putObject($path, $content, $acl, $cache_duration, $options);
    }

    protected function putObject($path, $content, $acl, $cache_duration, $options = []) {
        $path = $this->parsePath($path);
        $return = false;
        try {
            $params = $options;
            if (!isset($cache_duration)) {
                $params['CacheControl'] = 'max-age=0';
            } else if ($cache_duration > 0) {
                $params['CacheControl'] = 'max-age=' . $cache_duration;
            } else {
                $params['CacheControl'] = 'no-store';
            }
            if (!empty($params['storage'])) {
                $params['StorageClass'] = $params['storage'];
            } else if (!empty($this->storage)) {
                $params['StorageClass'] = $this->storage;
            }
            $result = $this->_instance->upload($this->bucket_key, $path, $content, $acl, [
                'params' => $params,
            ]);
            if (!isset($result->get('@metadata')['statusCode']) || $result->get('@metadata')['statusCode'] != 200) {
                $this->reportError(__FUNCTION__, "Return status: " . var_export($result, true));
            } else {
                $return = $result->get('ObjectURL');
            }
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return $return;
    }

    /**
     * @param string $path AmazonS3 path
     * 
     * @return boolean if the content exists
     */
    public function doesContentExist($path) {
        if (!$this->_instance) {
            return false;
        }
        $path = $this->parsePath($path);
        return $this->doesObjectExist($path);
    }

    protected function doesObjectExist($path) {
        try {
            return $this->_instance->doesObjectExist($this->bucket_key, $path);
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return false;
    }

    /**
     * @param string $path AmazonS3 path
     * 
     * @return \Aws\Result|boolean result meta data if object exists, false if object not exists
     */
    public function getContentMeta($path) {
        if (!$this->_instance) {
            return false;
        }
        $path = $this->parsePath($path);
        if ($this->doesObjectExist($path)) {
            try {
                return $this->_instance->headObject([
                            'Bucket' => $this->bucket_key,
                            'Key' => $path,
                ]);
            } catch (\Exception $e) {
                $this->reportError(__FUNCTION__, "Unknown Exception", $e);
            }
        } else {
            return false;
        }
    }

    /**
     * @param string $path AmazonS3 path
     * @param boolean $getFromServer should get from server | default: false
     * @param boolean $expirytime when the url should expire in seconds | default : 86400 (1 day)
     * 
     * @return string|boolean url if successful, false if failed
     */
    public function getContentUrl($path, $getFromServer = false, $expirytime = 86400) {
        $url = "";
        if (!$this->_instance) {
            return $url;
        }
        $path = $this->parsePath($path);
        if ($this->doesObjectExist($path)) {
            if ($getFromServer) {
                $url = $this->createPresignedRequestUrl($path, $expirytime);
            } else {
                try {
                    $url = $this->_instance->getObjectUrl($this->bucket_key, $path);
                } catch (\Exception $e) {
                    $this->reportError(__FUNCTION__, "Unknown Exception", $e);
                }
            }
        }
        return $url;
    }

    protected function createPresignedRequestUrl($parsed_path, $expirytime) {
        $url = "";
        $command = $this->_instance->getCommand('GetObject', [
            'Bucket' => $this->bucket_key,
            'Key' => $parsed_path,
        ]);
        try {
            $request = $this->_instance->createPresignedRequest($command, '+' . $expirytime . ' seconds');
            $url = $request->getUri()->__toString();
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return $url;
    }
    
    public function getContent($path) {
        $parsed_path = $this->parsePath($path);
        try {
            return $this->_instance->getObject([
                'Bucket' => $this->bucket,
                'Key' => $parsed_path,
            ])->get('Body');
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return null;
    }

    /**
     * 
     * @param string $path Amazon S3 path to save the copy to
     * @param string $source_path Amazon S3 path of the source content to copy from
     * @param string $acl private|public-read|public-read-write|authenticated-read|bucket-owner-read|bucket-owner-full-control
     * @param string $source_bucket_tag the source bucket tag, if any, otherwise current bucket is used
     * 
     * @return string|boolean content url if successful, false if failed
     */
    public function copyContent($path, $source_path, $acl = false, $source_bucket_tag = false) {
        if (!$this->_instance) {
            return false;
        }
        if ($source_bucket_tag === false) {
            $source_bucket_tag = $this->bucket_tag;
        }
        $source_instance = $this->_aws->getS3($source_bucket_tag);
        $acl = $this->parseAcl($acl);
        $path = $this->$this->parsePath($path);
        $source_path = $source_instance->parsePath($source_path);
        $params = [
            'Bucket' => $this->bucket_key,
            'Key' => $path,
            'ACL' => $acl,
            'CopySource' => urlencode($source_bucket_config['bucket_key'] . '/' . $source_path),
        ];
        if (!empty($params['storage'])) {
            $params['StorageClass'] = $params['storage'];
        } else if (!empty($this->storage)) {
            $params['StorageClass'] = $this->storage;
        }
        try {
            $result = $this->_instance->copyObject($params);
            if (!isset($result->get('@metadata')['statusCode']) || $result->get('@metadata')['statusCode'] != 200) {
                $this->reportError(__FUNCTION__, "Return status: " . var_export($result, true));
                $return = false;
            } else {
                $return = $result->get('ObjectURL');
            }
            return $return;
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return false;
    }
    
    public function directCopyContent($path, $source_path_with_bucket, $acl = false) {
        if (!$this->_instance) {
            return false;
        }
        $acl = $this->parseAcl($acl);
        $path = $this->parsePath($path);
        $params = [
            'Bucket' => $this->bucket_key,
            'Key' => $path,
            'ACL' => $acl,
                'CopySource' => urlencode($source_path_with_bucket),
        ];
        if (!empty($params['storage'])) {
            $params['StorageClass'] = $params['storage'];
        } else if (!empty($this->storage)) {
            $params['StorageClass'] = $this->storage;
        }
        try {
            $result = $this->_instance->copyObject([
                'Bucket' => $this->bucket_key,
                'Key' => $path,
                'ACL' => $acl,
                'CopySource' => urlencode($source_path_with_bucket),
            ]);
            if (!isset($result->get('@metadata')['statusCode']) || $result->get('@metadata')['statusCode'] != 200) {
                $this->reportError(__FUNCTION__, "Return status: " . var_export($result, true));
                $return = false;
            } else {
                $return = $result->get('ObjectURL');
            }
            return $return;
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return false;
    }

    /**
     * 
     * @param string $path Amazon S3 path to save the copy to
     * @param string $source_path Amazon S3 path of the source content to copy from
     * @param string $acl private|public-read|public-read-write|authenticated-read|bucket-owner-read|bucket-owner-full-control
     * @param string $source_bucket_tag the source bucket tag, if any, otherwise current bucket is used
     * 
     * @return string|boolean content url if successful, false if failed
     */
    public function moveContent($path, $source_path, $acl = false, $source_bucket_tag = false) {
        if (!$this->_instance) {
            return false;
        }
        $result = $this->copyContent($path, $source_path, $acl, $source_bucket_tag);
        if ($result !== false) {
            $this->_aws->getS3($source_bucket_tag)->deleteContent($source_path);
        }
        return $result;
    }

    /**
     * @param string $path AmazonS3 path
     * 
     * @return boolean if the file is found for deletion
     */
    public function deleteContent($path) {
        if (!$this->_instance) {
            return false;
        }
        $path = $this->parsePath($path);
        if ($this->doesObjectExist($path)) {
            try {
                $this->_instance->deleteObject([
                    'Bucket' => $this->bucket_key,
                    'Key' => $path,
                ]);
                return true;
            } catch (\Exception $e) {
                $this->reportError(__FUNCTION__, "Unknown Exception", $e);
            }
        }
        return false;
    }

    public function deleteObjects($objects) {
        if (!$this->_instance) {
            return false;
        }
        $deleteResult = $this->_instance->deleteObjects([
           'Bucket' => $this->bucket_key,
           'Delete' => ['Objects' => $objects],
        ]);
        return $deleteResult;
    }

    /**
     * @param string $path AmazonS3 path
     * 
     * @return boolean if the file is found for deletion
     */
    public function deleteFolder($path) {
        if (!$this->_instance) {
            return false;
        }
        $path = $this->parsePath($path);
        
        try {
            $result = $this->_instance->listObjectsV2([
                'Bucket' => $this->bucket_key,
                'Prefix' => $path,
            ]);
            if (!isset($result->get('@metadata')['statusCode']) || $result->get('@metadata')['statusCode'] != 200) {
                $this->reportError(__FUNCTION__, "Return status: " . var_export($result, true));
                return false;
            }
            foreach ($result->get("Contents") as $content) {
                $this->_instance->deleteObject([
                    'Bucket' => $this->bucket_key,
                    'Key' => $content['Key'],
                ]);
            }
            return true;
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
        return false;
    }

    protected function parseAcl($acl) {
        if ($acl === false) {
            return $this->acl;
        } else if (!in_array($acl, [self::S3_ACL_PRIVATE, self::S3_ACL_PUBLIC_READ, self::S3_ACL_PUBLIC_READ_WRITE, self::S3_ACL_AUTH_READ, self::S3_ACL_BUCKET_OWNER_READ, self::S3_ACL_BUCKET_OWNER_FULL])) {
            throw new InvalidConfigException("S3 no such ACL : " . $acl);
        } else {
            return $acl;
        }
    }

    protected function parsePath($path) {
        $path = trim(trim($path), '/');
        return $this->prefix_path . $path;
    }

    protected function reportError($functionName, $message, $exception = null) {
        $this->_aws->reportError(get_called_class(), $functionName, "Bucket tag: " . $this->bucket_tag . " | " . $message, $exception);
    }

    public function getBucketKey() {
        return $this->bucket_key;
    }

    public function getAcl() {
        return $this->parseAcl(false);
    }
}
