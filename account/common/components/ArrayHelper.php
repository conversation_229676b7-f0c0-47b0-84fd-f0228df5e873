<?php

namespace common\components;

use \yii\helpers\ArrayHelper as CoreArrayHelper;

class ArrayHelper extends CoreArrayHelper
{
    /**
     * Get a subset of the items from the given array.
     *
     * @param  array  $array
     * @param  array|string  $keys
     * @return array
     */
    public static function selectByKeys($array, $keys)
    {
        return array_intersect_key($array, array_flip((array) $keys));
    }
}