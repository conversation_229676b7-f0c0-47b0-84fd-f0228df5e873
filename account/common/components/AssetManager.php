<?php

/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace common\components;

use Yii;
use yii\base\InvalidConfigException;
use yii\base\InvalidParamException;
use yii\helpers\FileHelper;
use yii\helpers\StringHelper;

use \Aws\CacheInterface;
use \Aws\S3\S3ClientInterface;

class AssetManager extends \yii\web\AssetManager {

    public $s3BucketTag;

    /**
     * 'components' => [
     *      'assetManager' => [
     *          'class' => 'backend\components\AssetManager',
     *          'basePath' => 's3://testonly/pipwave/assets',
     *          'baseUrl' => 'http://testonly.s3.amazonaws.com/pipwave/assets'
     *      ]
     * ]
     * 
     * @throws InvalidConfigException
     */
    public function init() {
        if (!empty($this->s3BucketTag)) {
            //S3 Streamwrapper, for FileManager support with S3
            $s3 = Yii::$app->aws->getS3($this->s3BucketTag);
            StreamWrapper::register($s3->getInstance());
        }
        
        $basePath = $this->basePath;
        if (!is_dir(\Yii::getAlias($basePath))) {
            mkdir(\Yii::getAlias($basePath));
        }
        
        // Init verifies basePath, but it ends the verification chain with:
        //   $this->basePath = realpath($this->basePath);
        // which is not supported by s3 streamwrapper. Because of this we will get alias again and execute realPath if $this->basePath is false
        $basePath = $this->basePath;
        parent::init();
        if ($this->basePath === false) {
            $this->basePath = $basePath;
        }
    }

    public function getBundle($name, $publish = true) {
        if ($publish && isset($this->bundles[$name]) && is_array($this->bundles[$name])) {
            if (!isset($this->bundles[$name]['sourcePath']) && !isset($this->bundles[$name]['basePath']) && !isset($this->bundles[$name]['baseUrl'])) {
                $this->bundles[$name]['basePath'] = $this->basePath;
                $this->bundles[$name]['baseUrl'] = $this->baseUrl;
            }
        }
        return parent::getBundle($name, $publish);
    }

    /**
     * Publishes a file to S3.
     * @param string $src the asset file to be published
     * @return array the path and the URL that the asset is published as.
     * @throws InvalidParamException if the asset to be published does not exist.
     */
    protected function publishFile($src) {
        if ($val = $this->publishCheck($src)) {
            return $val;
        }
        $dir = $this->hash($src);
        $fileName = basename($src);
        $dstDir = $this->basePath . DIRECTORY_SEPARATOR . $dir;
        $dstFile = $dstDir . DIRECTORY_SEPARATOR . $fileName;

        if ($this->linkAssets) {
            if (!is_file($dstFile)) {
                symlink($src, $dstFile);
            }
        } elseif (@filemtime($dstFile) < @filemtime($src)) {
            $context = stream_context_create([
                's3' => [
                    'ACL' => 'public-read',
                    'CacheControl' => 'max-age=2592000',
                ],
            ]);
            copy($src, $dstFile, $context);
        }
        return $this->publishCheck($src, [$dstFile, $this->baseUrl . "/$dir/$fileName"]);
    }

    /**
     * Publishes a directory to S3.
     * @param string $src the asset directory to be published
     * @param array $options the options to be applied when publishing a directory.
     * The following options are supported:
     *
     * - only: array, list of patterns that the file paths should match if they want to be copied.
     * - except: array, list of patterns that the files or directories should match if they want to be excluded from being copied.
     * - caseSensitive: boolean, whether patterns specified at "only" or "except" should be case sensitive. Defaults to true.
     * - beforeCopy: callback, a PHP callback that is called before copying each sub-directory or file.
     *   This overrides [[beforeCopy]] if set.
     * - afterCopy: callback, a PHP callback that is called after a sub-directory or file is successfully copied.
     *   This overrides [[afterCopy]] if set.
     * - forceCopy: boolean, whether the directory being published should be copied even if
     *   it is found in the target directory. This option is used only when publishing a directory.
     *   This overrides [[forceCopy]] if set.
     *
     * @return array the path directory and the URL that the asset is published as.
     * @throws InvalidParamException if the asset to be published does not exist.
     */
    protected function publishDirectory($src, $options) {
        if ($val = $this->publishCheck($src)) {
            return $val;
        }
        $dir = $this->hash($src);
        $dstDir = $this->basePath . DIRECTORY_SEPARATOR . $dir;

        if ($this->linkAssets) {
            if (!is_dir($dstDir)) {
                symlink($src, $dstDir);
            }
        } elseif (!empty($options['forceCopy']) || ($this->forceCopy && !isset($options['forceCopy'])) || !is_dir($dstDir)) {
            $opts = array_merge($options, ['dirMode' => $this->dirMode, 'fileMode' => $this->fileMode]);
            if (!isset($opts['beforeCopy'])) {
                if ($this->beforeCopy !== null) {
                    $opts['beforeCopy'] = $this->beforeCopy;
                } else {
                    $opts['beforeCopy'] = function ($from, $to) {
                        return strncmp(basename($from), '.', 1) !== 0;
                    };
                }
            }
            if (!isset($opts['afterCopy']) && $this->afterCopy !== null) {
                $opts['afterCopy'] = $this->afterCopy;
            }
            static::_copyDirectory($src, $dstDir, $opts);
        }
        return $this->publishCheck($src, [$dstDir, $this->baseUrl . '/' . $dir]);
    }

    /**
     * Copies a whole directory as another one in S3
     * Duplicate from FileHelper::copyDirectory($src, $dst, $options = [])
     * @param type $src the source directory
     * @param type $dst the destination directory
     * @param type $options reference FileHelper::copyDirectory
     * @throws InvalidParamException
     */
    public static function _copyDirectory($src, $dst, $options = []) {
        if (!is_dir($dst)) {
            mkdir($dst, isset($options['dirMode']) ? $options['dirMode'] : 0775, true);
        }
        $handle = opendir($src);
        if ($handle === false) {
            throw new InvalidParamException("Unable to open directory: $src");
        }

        if (!isset($options['basePath'])) {
            // this should be done only once
            $options['basePath'] = realpath($src);
            $options = static::normalizeOptions($options);
        }
        while (($file = readdir($handle)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            $from = $src . DIRECTORY_SEPARATOR . $file;
            $to = $dst . DIRECTORY_SEPARATOR . $file;

            if (FileHelper::filterPath($from, $options)) {
                if (isset($options['beforeCopy']) && !call_user_func($options['beforeCopy'], $from, $to)) {
                    continue;
                }
                if (is_file($from)) {
                    $context = stream_context_create([
                        's3' => [
                            'ACL' => 'public-read',
                            'CacheControl' => 'max-age=2592000',
                        ],
                    ]);
                    copy($from, $to, $context);
                } else {
                    static::_copyDirectory($from, $to, $options);
                }
                if (isset($options['afterCopy'])) {
                    call_user_func($options['afterCopy'], $from, $to);
                }
            }
        }
        closedir($handle);
    }

    /**
     * Duplicate from FileHelper::normalizeOptions(array $options)
     * @param array $options
     * @return type
     */    
    private static function normalizeOptions(array $options) {
        if (!array_key_exists('caseSensitive', $options)) {
            $options['caseSensitive'] = true;
        }
        if (isset($options['except'])) {
            foreach ($options['except'] as $key => $value) {
                if (is_string($value)) {
                    $options['except'][$key] = static::parseExcludePattern($value, $options['caseSensitive']);
                }
            }
        }
        if (isset($options['only'])) {
            foreach ($options['only'] as $key => $value) {
                if (is_string($value)) {
                    $options['only'][$key] = static::parseExcludePattern($value, $options['caseSensitive']);
                }
            }
        }
        return $options;
    }
    
    /**
     * Duplicate from FileHelper::parseExcludePattern($pattern, $caseSensitive)
     * @param string $pattern
     * @param boolean $caseSensitive
     * @throws \yii\base\InvalidParamException
     * @return array with keys: (string) pattern, (int) flags, (int|boolean) firstWildcard
     */
    private static function parseExcludePattern($pattern, $caseSensitive)
    {
        if (!is_string($pattern)) {
            throw new InvalidParamException('Exclude/include pattern must be a string.');
        }

        $result = [
            'pattern' => $pattern,
            'flags' => 0,
            'firstWildcard' => false,
        ];

        if (!$caseSensitive) {
            $result['flags'] |= FileHelper::PATTERN_CASE_INSENSITIVE;
        }

        if (!isset($pattern[0])) {
            return $result;
        }

        if ($pattern[0] == '!') {
            $result['flags'] |= FileHelper::PATTERN_NEGATIVE;
            $pattern = StringHelper::byteSubstr($pattern, 1, StringHelper::byteLength($pattern));
        }
        if (StringHelper::byteLength($pattern) && StringHelper::byteSubstr($pattern, -1, 1) == '/') {
            $pattern = StringHelper::byteSubstr($pattern, 0, -1);
            $result['flags'] |= FileHelper::PATTERN_MUSTBEDIR;
        }
        if (strpos($pattern, '/') === false) {
            $result['flags'] |= FileHelper::PATTERN_NODIR;
        }
        $result['firstWildcard'] = static::firstWildcardInPattern($pattern);
        if ($pattern[0] == '*' && static::firstWildcardInPattern(StringHelper::byteSubstr($pattern, 1, StringHelper::byteLength($pattern))) === false) {
            $result['flags'] |= FileHelper::PATTERN_ENDSWITH;
        }
        $result['pattern'] = $pattern;

        return $result;
    }

    /**
     * Duplicate from FileHelper::firstWildcardInPattern($pattern)
     * @param string $pattern the pattern to search in
     * @return integer|boolean position of first wildcard character or false if not found
     */
    private static function firstWildcardInPattern($pattern)
    {
        $wildcards = ['*', '?', '[', '\\'];
        $wildcardSearch = function ($r, $c) use ($pattern) {
            $p = strpos($pattern, $c);

            return $r === false ? $p : ($p === false ? $r : min($r, $p));
        };

        return array_reduce($wildcards, $wildcardSearch, false);
    }

    protected function publishCheck($src, $returns = null) {
        if ($this->basePath == \Yii::getAlias('@webroot/assets')) {
            if (!empty($returns)) {
                return $returns;
            } else {
                return false;
            }
        }
        $cache_key = 'Performance/' . \Yii::$app->id . '/publish_check/' . md5($this->basePath . $src) . '/boolean';
        if (isset($returns)) {
            \Yii::$app->cache->set($cache_key, $returns, 604800);
            return $returns;
        } else {
            return \Yii::$app->cache->get($cache_key);
        }
    }
    
}

class StreamWrapper extends \Aws\S3\StreamWrapper {
    /**
     * Register the 's3://' stream wrapper, copied from aws-sdk-php/src/S3/StreamWrapper
     *
     * @param S3ClientInterface $client   Client to use with the stream wrapper
     * @param string            $protocol Protocol to register as.
     * @param CacheInterface    $cache    Default cache for the protocol.
     */
    public static function register(S3ClientInterface $client, $protocol = 's3', CacheInterface $cache = null) {
        if (in_array($protocol, stream_get_wrappers())) {
            stream_wrapper_unregister($protocol);
        }

        // Set the client passed in as the default stream context client
        // Do not register stream wrapper with STREAM_IS_URL
        stream_wrapper_register($protocol, get_called_class());
        $default = stream_context_get_options(stream_context_get_default());
        $default[$protocol]['client'] = $client;

        if ($cache) {
            $default[$protocol]['cache'] = $cache;
        } elseif (!isset($default[$protocol]['cache'])) {
            // Set a default cache adapter.
            $default[$protocol]['cache'] = new \Aws\LruArrayCache();
        }

        stream_context_set_default($default);
    }
}