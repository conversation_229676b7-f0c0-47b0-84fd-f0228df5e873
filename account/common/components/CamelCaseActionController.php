<?php

namespace common\components;

use yii\base\InlineAction;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use yii\helpers\Inflector;
use yii\web\Controller;

//Yii1: Support camelCase url callings

abstract class CamelCaseActionController extends Controller {
    
    public function init() {
        parent::init();
        //Convert CamelCase controllers to id-dash format to find correct view ala yii2 method
        $this->id = Inflector::camel2id($this->id);
    }
        
    public function createAction($id) {
        if (!empty($id)) {
            //Convert camelCase action to hash-format
            $id = Inflector::camel2id($id);
        }
        return parent::createAction($id);
    }
    
}