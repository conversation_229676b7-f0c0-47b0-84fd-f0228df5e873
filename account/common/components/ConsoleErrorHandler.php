<?php

namespace common\components;

class ConsoleErrorHandler extends \yii\console\ErrorHandler {
    
    protected function renderException($exception) {
        // Auto-report to admin on errors
        $db = \Yii::$app->getDb();
        $txn = $db->getTransaction();
        if ($txn) {
            //Rollback first in order to allow logs to enter db
            $txn->rollBack();
        }
        \Yii::$app->reporter->reportToAdminViaSlack('Unexpected ' . \Yii::$app->id . ' portal error', $exception);
        return parent::renderException($exception);
    }
}
