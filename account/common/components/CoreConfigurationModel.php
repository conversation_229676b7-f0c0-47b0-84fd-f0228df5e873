<?php

namespace common\components;

use \Yii;

class CoreConfigurationModel extends CoreModel {

    public function getConfigValue($key, $realtime = false) {
        $cache_key = static::tableName() . '/key/' . $key;
        $result = ($realtime ? false : Yii::$app->cache->get($cache_key));

        if ($result === false) {
            $m_data = $this->findOne(['configuration_key' => $key]);
            if (isset($m_data)) {
                $result = (isset($m_data->configuration_value) ? $m_data->configuration_value : false);
                Yii::$app->cache->set($cache_key, $result, 86400);
            }
        }

        return $result;
    }

}