<?php

namespace common\components;

use Yii;
use \yii\db\ActiveRecord;

class CoreModel extends ActiveRecord {
    
    public static function model() {
        return new static();
    }

    public function insertOrUpdate($data, $returnPk = false) {
        $primaryKey = $this->saveInit($data);

        $status = $this->upsert(false);

        if (!$status) {
            \Yii::$app->reporter->reportToAdminViaSlack('saveNewRecord failed', json_encode([
                'class' => static::className(),
                'attributes' => $this->attributes,
                'error' => $this->getErrors(),
            ]));
        }

        if ($returnPk) {
            return is_array($primaryKey) ? $this : $this->$primaryKey;
        }

        return $status;
    }

    public function saveNewRecord($data, $returnPk = false) {
        $primaryKey = $this->saveInit($data);

        $this->setIsNewRecord(true);

        //Ported from yii1, insert does not run validation
        $status = $this->insert(false);

        if (!$status) {
            \Yii::$app->reporter->reportToAdminViaSlack('saveNewRecord failed', json_encode([
                'class' => static::className(),
                'attributes' => $this->attributes,
                'error' => $this->getErrors(),
            ]));
        }
        
        if ($returnPk) {
            return is_array($primaryKey) ? $this : $this->$primaryKey;
        }

        return $status;
    }

    protected function upsert($runValidation = true, $attributes = null) {
        if ($runValidation && !$this->validate($attributes)) {
            Yii::info('Model not inserted due to validation error.', __METHOD__);
            return false;
        }

        $connection = static::getDb();
        $command = $connection->createCommand();

        //remove primary key from attributes
        $update_attributes = array_diff_key($this->getAttributes(), array_flip($this->getPrimaryKeys()));

        try {
            $command->upsert(static::tableName(), $this->getAttributes(), $update_attributes);
            $result = $command->execute();

            return $result;
        } catch (\Exception $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    protected function getPrimaryKeys() {
        $primaryKey = $this->getTableSchema()->primaryKey;

        if(is_array($primaryKey)) {
            return $primaryKey;
        }

        return [$primaryKey];
    }

    protected function saveInit($data) {
        $primaryKey = $this->getTableSchema()->primaryKey;

        if (is_array($primaryKey) && count($primaryKey) === 1) {
            $primaryKey = array_shift($primaryKey);
        }
        if (is_array($primaryKey)) {
            foreach($primaryKey as $key) {
                unset($this->$key);
            }
        } else {
            unset($this->$primaryKey);
        }

        foreach ($data as $field => $value) {
            //Checks whether this AR has the named attribute
            if ($this->hasAttribute($field)) {
                if (!isset($value)) {
                    unset($this->$field);
                } else {
                    $this->setAttribute($field, $value);
                }
            }
        }

        return $primaryKey;
    }

}