<?php

namespace common\components;

use Yii;

class CurlComponent {

    public $connect_via_proxy;
    public $request_headers;
    public $curl_info;
    private $error_array = array();

    function __construct() {
        $this->connect_via_proxy = Yii::$app->params['USE_PROXY'];
    }

    function curlGet($url, $data = '', $filename = '') {
        $getfields = '';
        if (!empty($data)) {
            if (is_array($data)) {
                while (list($key, $value) = each($data)) {
                    $getfields .= $key . '=' . urlencode($value) . '&';
                }
            } else {
                $getfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        $ch = curl_init($url . '?' . $getfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->connect_via_proxy ? false : 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        // Process custom headers
        if (isset($this->request_headers) && count($this->request_headers)) {
            $temp_headers = array();

            if (!array_key_exists('Expect', $this->request_headers)) {
                $this->request_headers['Expect'] = '';
            }

            foreach ($this->request_headers as $k => $v) {
                $temp_headers[] = $k . ': ' . $v;
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $temp_headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        }

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $response = curl_exec($ch);

        if ($curl_errno = curl_errno($ch)) {
            $this->errorReport($curl_errno, curl_error($ch));
        }
        $this->curl_info = curl_getinfo($ch);

        curl_close($ch);

        return $response;
    }

    function curlGetBody($url, $data, $filename = '', $uploadFile = false) {
        $ch = curl_init($url);
        $postfields = '';

        #for upload file, post field need send in array
        if ($uploadFile) {
            $postfields = $data;
        } else {
            if (is_array($data)) {
                $postfields = http_build_query($data, null, '&');
            } else {
                $postfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 0);
        if (!empty($filename)) {
            $fp = fopen($filename, 'w+');
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->connect_via_proxy ? false : 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);

        // Process custom headers
        if (isset($this->request_headers) && count($this->request_headers)) {
            $temp_headers = array();

            if (!array_key_exists('Expect', $this->request_headers)) {
                $this->request_headers['Expect'] = '';
            }

            foreach ($this->request_headers as $k => $v) {
                $temp_headers[] = $k . ': ' . $v;
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $temp_headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $response = curl_exec($ch);

        if ($curl_errno = curl_errno($ch)) {
            $this->errorReport($curl_errno, curl_error($ch));
        }

        $this->curl_info = curl_getinfo($ch);

        curl_close($ch);
        if (!empty($filename)) {
            fclose($fp);
        }

        return $response;
    }

    function curlPost($url, $data, $filename = '', $uploadFile = false) {
        $ch = curl_init($url);
        $postfields = '';

        #for upload file, post field need send in array
        if ($uploadFile) {
            $postfields = $data;
        } else {
            if (is_array($data)) {
                $postfields = http_build_query($data, null, '&');
            } else {
                $postfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 0);
        if (!empty($filename)) {
            $fp = fopen($filename, 'w+');
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->connect_via_proxy ? false : 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);

        // Process custom headers
        if (isset($this->request_headers) && count($this->request_headers)) {
            $temp_headers = array();

            if (!array_key_exists('Expect', $this->request_headers)) {
                $this->request_headers['Expect'] = '';
            }

            foreach ($this->request_headers as $k => $v) {
                $temp_headers[] = $k . ': ' . $v;
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $temp_headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $response = curl_exec($ch);
        if ($curl_errno = curl_errno($ch)) {
            $this->errorReport($curl_errno, curl_error($ch));
        }

        $this->curl_info = curl_getinfo($ch);

        curl_close($ch);
        if (!empty($filename)) {
            fclose($fp);
        }

        return $response;
    }

    function curlDelete($url, $data, $filename = '', $uploadFile = false) {
        $ch = curl_init($url);
        $postfields = '';

        #for upload file, post field need send in array
        if ($uploadFile) {
            $postfields = $data;
        } else {
            if (is_array($data)) {
                $postfields = http_build_query($data, null, '&');
            } else {
                $postfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 0);
        if (!empty($filename)) {
            $fp = fopen($filename, 'w+');
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->connect_via_proxy ? false : 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        
        // Process custom headers
        if (isset($this->request_headers) && count($this->request_headers)) {
            $temp_headers = array();

            if (!array_key_exists('Expect', $this->request_headers)) {
                $this->request_headers['Expect'] = '';
            }

            foreach ($this->request_headers as $k => $v) {
                $temp_headers[] = $k . ': ' . $v;
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $temp_headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $response = curl_exec($ch);

        if ($curl_errno = curl_errno($ch)) {
            $this->errorReport($curl_errno, curl_error($ch));
        }

        $this->curl_info = curl_getinfo($ch);

        curl_close($ch);
        if (!empty($filename)) {
            fclose($fp);
        }

        return $response;
    }

    public function getError() {
        return $this->error_array;
    }

    private function errorReport($errorCode, $errorMessage = '', $extra = array()) {
        $this->error_array = array(
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'extra_info' => $extra
        );
    }

}