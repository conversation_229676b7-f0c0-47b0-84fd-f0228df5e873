<?php

namespace common\components;

use common\models\Currencies;
use Yii;

/*
 * $Id: CurrenciesComponent.php,v 1.13.2.1 2015/04/28 12:40:15 darren.ng Exp $
 *
 * Developer: Ching Yen
 *
 */

class CurrenciesComponent {

    public $currencies, $decimal_places, $internal_currencies, $rebate_point, $rebate_point_formula, $rebate_point_extra, $format, $format_id;

    public function __construct() {
        $this->format = [];
        $this->format_id = [];
        $this->currencies = [];
        $this->decimal_places = null;
        $this->internal_currencies = [];
        $this->rebate_point = 0;
        $this->rebate_point_extra = 0;
        $this->rebate_point_formula = '';

        $c_data = Currencies::find()->orderBy('title, code')->all();
        if (!empty($c_data)) {
            foreach ($c_data as $_cur) {
                $_used_mode = explode(',', $_cur->currencies_used_for);

                if (!empty($_used_mode) && in_array('SELL', $_used_mode)) {
                    $this->currencies[$_cur->code] = [
                        'currencies_id' => $_cur->currencies_id,
                        'title' => $_cur->title,
                        'symbol_left' => $_cur->symbol_left,
                        'symbol_right' => $_cur->symbol_right,
                        'decimal_point' => $_cur->decimal_point,
                        'thousands_point' => $_cur->thousands_point,
                        'decimal_places' => $_cur->decimal_places,
                        'value' => $_cur->value,
                        'buy_value' => $_cur->buy_value,
                        'sell_value' => $_cur->sell_value,
                    ];
                    $this->internal_currencies[$_cur->currencies_id] = $_cur->code;
                }

                // full currencies format
                $this->format[$_cur->code] = [
                    'symbol_left' => $_cur->symbol_left,
                    'symbol_right' => $_cur->symbol_right,
                    'decimal_point' => $_cur->decimal_point,
                    'thousands_point' => $_cur->thousands_point,
                    'decimal_places' => $_cur->decimal_places,
                    'value' => $_cur->value,
                    'buy_value' => $_cur->buy_value,
                    'sell_value' => $_cur->sell_value,
                ];
                $this->format_id[$_cur->currencies_id] = $_cur->code;
            }
        }
    }

    public function format($number, $calculate_cur_value = true, $cur_type = '', $cur_value = '', $type = 'sell') {
        $format_string = '';

        if ($cur_type == '') {
            $cur_type = Yii::$app->session['cur_code'];
        }

        if (isset($this->format[$cur_type])) {
            if ($calculate_cur_value == true) {
                $_eur_cur = ['DEM', 'BEF', 'LUF', 'ESP', 'FRF', 'IEP', 'ITL', 'NLG', 'ATS', 'PTE', 'FIM', 'GRD'];

                if ($cur_value != '') {
                    $rate = $cur_value;
                } else {
                    $rate = (($type != '') ? $this->format[$cur_type][$type . '_value'] : $this->format[$cur_type]['value']);
                }

                if (is_null($this->decimal_places)) {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number * $rate, $this->format[$cur_type]['decimal_places'], '.', ''), $this->format[$cur_type]['decimal_places'], $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                } else {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number * $rate, $this->decimal_places, '.', ''), $this->decimal_places, $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                }

                if ((Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"] == 'EUR') && in_array($cur_type, $_eur_cur)) {
                    $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
                }
            } else {
                if (is_null($this->decimal_places)) {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number, $this->format[$cur_type]['decimal_places'], '.', ''), $this->format[$cur_type]['decimal_places'], $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                } else {
                    $format_string = $this->format[$cur_type]['symbol_left'] . number_format(number_format($number, $this->decimal_places, '.', ''), $this->decimal_places, $this->format[$cur_type]['decimal_point'], $this->format[$cur_type]['thousands_point']) . $this->format[$cur_type]['symbol_right'];
                }
            }
        } else {
            $format_string = $number;
        }

        return $format_string;
    }

    public function formatRoundDown($number, $calculate_currency_value = true, $currency_type = '', $currency_value = '', $type = 'sell') {
        $_def_cur = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];

        if ($currency_type == '') {
            $currency_type = $_def_cur;
        }

        if ($calculate_currency_value) {
            $rate = ($currency_value) ? $currency_value : $this->format[$currency_type]['value'];

            // added by subrat
            if (empty($this->decimal_places)) {
                $format_string = $this->format[$currency_type]['symbol_left'] . number_format($this->_currencyRoundDown($number * $rate, pow(10, (int) $this->format[$currency_type]['decimal_places'])), $this->format[$currency_type]['decimal_places'], $this->format[$currency_type]['decimal_point'], $this->format[$currency_type]['thousands_point']) . $this->format[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->format[$currency_type]['symbol_left'] . number_format($this->_currencyRoundDown($number * $rate, pow(10, (int) $this->format[$currency_type]['decimal_places'])), $this->decimal_places, $this->format[$currency_type]['decimal_point'], $this->format[$currency_type]['thousands_point']) . $this->format[$currency_type]['symbol_right'];
            }

            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if (($_def_cur == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->formatRoundDown($number, true, 'EUR', '', $type) . ']</small>';
            }
        } else {
            if (empty($this->decimal_places))
                $format_string = $this->format[$currency_type]['symbol_left'] . number_format($this->_currencyRoundDown($number, pow(10, (int) $this->format[$currency_type]['decimal_places'])), $this->format[$currency_type]['decimal_places'], $this->format[$currency_type]['decimal_point'], $this->format[$currency_type]['thousands_point']) . $this->format[$currency_type]['symbol_right'];
            else
                $format_string = $this->format[$currency_type]['symbol_left'] . number_format($this->_currencyRoundDown($number, pow(10, (int) $this->decimal_places)), $this->decimal_places, $this->format[$currency_type]['decimal_point'], $this->format[$currency_type]['thousands_point']) . $this->format[$currency_type]['symbol_right'];
        }
        return $format_string;
    }

    public function _currencyRoundDown($val, $rounder) {
        if ($rounder != 0) {
            return floor($val * $rounder) / $rounder;
        } else {
            return $val;
        }
    }

    public function applyCurrencyExchange($number, $currency_type = '', $currency_value = '', $type = 'sell') {
        if ($currency_type == '') {
            $currency_type = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        $rate = ($currency_value) ? $currency_value : $this->getValue($currency_type, $type);
        $value = $number * $rate;

        if (isset($this->format[$currency_type]['decimal_places'])) {
            return number_format($value, $this->format[$currency_type]['decimal_places'], '.', '');
        } else {
            return $value;
        }
    }

    public function advanceCurrencyConversion($number, $from_cur_type = '', $to_cur_type = '', $round = true, $type = 'buy') {
        if ($from_cur_type == '') {
            $from_cur_type = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if ($to_cur_type == '') {
            $to_cur_type = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        $rate = $this->advanceCurrencyConversionRate($from_cur_type, $to_cur_type, $type);

        if ($round) {
            return number_format($number * $rate, is_null($this->decimal_places) ? $this->format[$to_cur_type]['decimal_places'] : $this->decimal_places, '.', '');
        } else {
            return $number * $rate;
        }
    }

    function getValue($code, $type = 'sell') {
        if ($type != '') {
            return (isset($this->currencies[$code]) ? $this->currencies[$code][$type . '_value'] : (isset($this->format[$code]) ? $this->format[$code][$type . '_value'] : 0));
        } else {
            return (isset($this->currencies[$code]) ? $this->currencies[$code]['value'] : (isset($this->format[$code]) ? $this->format[$code]['value'] : 0));
        }
    }

    function advanceCurrencyConversionRate($from_cur_type = '', $to_cur_type = '', $type = 'sell') {
        if ($from_cur_type == '') {
            $from_cur_type = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if ($to_cur_type == '') {
            $to_cur_type = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if ($from_cur_type == $to_cur_type) {
            return number_format(1, 8, '.', '');
        }

        if ($type == 'sell') {
            if ($to_cur_type == Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"]) {
                $rate = ($this->format[$from_cur_type]['sell_value'] > 0) ? bcdiv($this->format[$to_cur_type]['buy_value'], $this->format[$from_cur_type]['sell_value'], 8) : 0;
            } else {
                $rate = ($this->format[$from_cur_type]['sell_value'] > 0) ? bcdiv($this->format[$to_cur_type]['buy_value'], $this->format[$from_cur_type]['value'], 8) : 0;
            }
        } else {
            if ($to_cur_type == Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"]) {
                $rate = ($this->format[$from_cur_type]['sell_value'] > 0) ? bcdiv($this->format[$to_cur_type]['sell_value'], $this->format[$from_cur_type]['buy_value'], 8) : 0;
            } else {
                $rate = ($this->format[$from_cur_type]['sell_value'] > 0) ? bcdiv($this->format[$to_cur_type]['sell_value'], $this->format[$from_cur_type]['value'], 8) : 0;
            }
        }

        return number_format($rate, 8, '.', '');
    }

    function set_decimal_places($decimal_place) {
        $decimal_place = (int) $decimal_place;

        if ($decimal_place <= 0)
            $this->decimal_places = null;
        else
            $this->decimal_places = $decimal_place;
    }

}