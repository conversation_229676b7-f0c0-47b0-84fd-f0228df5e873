<?php

namespace common\components;

use common\components\phone_verification_providers\AbstractPhoneVerificationProvider;
use common\models\Configuration;
use common\models\Countries;
use common\models\Customers;
use common\models\CustomersInfoVerification;
use common\models\CustomersOtp;
use common\models\CustomersSetting;
use common\models\MaxmindTelephoneIdentification;
use Yii;
use yii\web\NotFoundHttpException;

class CustomerSecurityComponent {

    private $merchant_id;
    private $customers_id;
    private $ip_address;
    private $request_type;
    private $customerCompletePhoneInfoArray;
    private $send_sms;
    private $default_service;
    private $max_token_validation_attempts = 10;

    /** @var AbstractPhoneVerificationProvider $service */
    private $service;

    function __construct($merchant_id, $request_type, $customerId = 0, $ip_address = null, $phone = null, $country = null) {
        $this->send_sms = (Configuration::model()->getConfigValue('SMS_MODULE_MODE') == 'Production' ? true : false);
        $this->default_service = Yii::$app->params['SMS_DEFAULT_SERVICE'];
        $this->merchant_id = $merchant_id;
        $this->customers_id = $customerId;
        $this->request_type = $request_type;
        $this->ip_address = empty($ip_address) ? GeneralComponent::getIPAddress() : $ip_address;
        $this->initialize($phone, $country);
    }

    private function initialize($phone, $country) {
        if (!in_array($this->request_type, array('security_token_request', 'verify_phone_request', 'one_time_pin'))) {
            throw new NotFoundHttpException('The specified page cannot be found.');
        }
        $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();

        if (!empty($this->customers_id) && (empty($phone) || empty($country))) {
            $field = array('customers_telephone');
            $cond = array('customers_id' => (int) $this->customers_id);
            $customerPhone = Customers::model()->getCustomerByField($field, $cond);
            if ($customerPhone['customers_telephone'] != '') {
                $this->customerCompletePhoneInfoArray = $classCustomersInfoVerificationComponent->formatTelephone($this->customers_id);
            }
        }
        if (!empty($phone) && !empty($country)) {
            $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($phone, $country);
            $country_info = Countries::model()->getDialingInfo($country);
            if (isset($country)) {
                $this->customerCompletePhoneInfoArray = array(
                    'country_international_dialing_code' => $country_info['countries_international_dialing_code'],
                    'telephone_number' => $parse_result->national_number,
                    'country_id' => $country,
                    'country_name' => $country_info['countries_name'],
                    'is_country_match' => $parse_result->is_country_match,
                    'is_valid_number' => $parse_result->is_valid_number,
                );
            }
        }
        $this->decidePhoneVerificationService();
    }

    private function decidePhoneVerificationService() {
        $service = "";
        if (isset($this->customerCompletePhoneInfoArray['country_international_dialing_code'])) {
            // If any country-based service selection, do it here!
            $country_dial_code = $this->customerCompletePhoneInfoArray['country_international_dialing_code'];
            if (in_array($country_dial_code, array('1', '52'))) {   // US, Canada and Mexico
                $service = $this->default_service; //'clickatell';
            } else if ($this->request_type != 'verify_phone_request' && $this->request_type != 'one_time_pin' && in_array($country_dial_code, array('62'))) {    // Indonesia
                $service = $this->default_service; //'mobileace';
            } else {
                $service = $this->default_service;
            }
        } else {
            $service = $this->default_service;
        }

        if ($this->send_sms) {
            $clazz = ucfirst($service) . 'PhoneVerificationProvider';
        } else {
            $clazz = 'TestPhoneVerificationProvider';
        }
        $clazz = '\\common\\components\\phone_verification_providers\\' . $clazz;
        $this->service = new $clazz($this->merchant_id, $service);
    }

    public function getCustomerPhoneInfo() {
        return $this->customerCompletePhoneInfoArray;
    }
    
    public function requestSecurityToken($requestUrl = null) {
        $responseResult = array(
            'res_code' => 0,
            'res_text' => '',
            'seconds_till_resend' => -1,
        );
        $mobileNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];
        $tokenVerifyAttempt = CustomersSetting::model()->getTokenVerifyAttempt($this->customers_id, $this->service->getResendFrequency(), $this->request_type);
        $otp = CustomersOtp::model()->getCustomerSecurityToken($this->customers_id, $this->request_type, $this->service->getTokenValidityPeriod(), $mobileNumber);
        $responseResult['res_code'] = $this->verifyTokenAttempt(FALSE, $this->request_type, $tokenVerifyAttempt, $otp);

        if ($responseResult['res_code'] === FALSE) {
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_FAIL_MSG');
        } else if ($responseResult['res_code'] == '2' || $responseResult['res_code'] == '3') {
            $responseResult['res_code'] = '2';
            $responseResult['seconds_till_resend'] = $tokenVerifyAttempt->seconds_till_resend;
            $responseResult['res_text'] = sprintf(Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_REUSE_MSG'), substr($mobileNumber, 0, -4) . str_repeat('X', 4));
            if (!$this->send_sms) {
                if (isset($otp->customers_otp_digit)) {
                    $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $otp->customers_otp_digit;
                }
            }
        } else if ($responseResult['res_code'] == '1') {
            $this->sendToken($requestUrl, $tokenVerifyAttempt, $otp, $responseResult);
        }
        return $responseResult;
    }

    public function requestSecurityTokenResend($requestUrl = null) {
        $responseResult = array(
            'res_code' => 0,
            'res_text' => '',
            'seconds_till_resend' => -1,
        );
        $mobileNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];
        $tokenVerifyAttempt = CustomersSetting::model()->getTokenVerifyAttempt($this->customers_id, $this->service->getResendFrequency(), $this->request_type);
        $otp = CustomersOtp::model()->getCustomerSecurityToken($this->customers_id, $this->request_type, $this->service->getTokenValidityPeriod(), $mobileNumber);
        $responseResult['res_code'] = $this->verifyTokenAttempt(FALSE, $this->request_type, $tokenVerifyAttempt, $otp);

        if ($responseResult['res_code'] === FALSE) {
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_FAIL_MSG');
        } else if ($responseResult['res_code'] == '3') {
            $responseResult['res_code'] = '1';
            $this->resendtoken($requestUrl, $tokenVerifyAttempt, $otp, $responseResult);
        } else if ($responseResult['res_code'] == '2') {
            $responseResult['seconds_till_resend'] = $tokenVerifyAttempt->seconds_till_resend;
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_PLEASE_WAIT_FOR_X_SECOND', array(
                        'second' => $this->service->getResendFrequency(),
            ));
        } else if ($responseResult['res_code'] == '1') {
            $this->sendToken($requestUrl, $tokenVerifyAttempt, $otp, $responseResult);
        }
        return $responseResult;
    }

    private function sendToken($requestUrl, $tokenVerifyAttempt, $otp, &$responseResult) {
        $mobileNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];

        if ($this->verifyTokenRequest($tokenVerifyAttempt, $otp, $responseResult)) {
            $result = $this->service->sendToken($this->customers_id, $this->ip_address, $this->request_type, $this->customerCompletePhoneInfoArray, $requestUrl);
            switch ($result['status']) {
                case AbstractPhoneVerificationProvider::RESULT_OK:
                    $responseResult['res_text'] = sprintf(Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_SUCCESS_MSG'), substr($mobileNumber, 0, -4) . str_repeat('X', 4));
                    $responseResult['seconds_till_resend'] = $this->service->getResendFrequency();
                    if (!$this->send_sms) {
                        $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $result['token'];
                    } else if (!empty($result['test_token'])) {
                        $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $result['test_token'];
                    }
                    break;
                case AbstractPhoneVerificationProvider::RESULT_NOT_MOBILE:
                    $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_INVALID_MOBILE');
                    $responseResult['res_code'] = FALSE;
                    break;
                default:
                    $responseResult['res_text'] = Yii::t('smsToken', 'ERROR_REQUEST_TOKEN_PROVIDER_ERROR');
                    $responseResult['res_code'] = FALSE;
            }
        }
    }

    private function resendtoken($requestUrl, $tokenVerifyAttempt, $otp, &$responseResult) {
        $mobileNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];

        if ($this->verifyTokenRequest($tokenVerifyAttempt, $otp, $responseResult)) {
            $result = $this->service->resendToken($this->customers_id, $this->ip_address, $this->request_type, $this->customerCompletePhoneInfoArray, $otp, $requestUrl);
            switch ($result['status']) {
                case AbstractPhoneVerificationProvider::RESULT_OK:
                    $responseResult['res_text'] = sprintf(Yii::t('smsToken', 'TEXT_RESEND_TOKEN_SUCCESS_MSG'), substr($mobileNumber, 0, -4) . str_repeat('X', 4));
                    $responseResult['seconds_till_resend'] = $this->service->getResendFrequency();
                    if (!$this->send_sms) {
                        $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $result['token'];
                    } else if (!empty($result['test_token'])) {
                        $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $result['test_token'];
                    }
                    break;
                default:
                    $responseResult['res_text'] = Yii::t('smsToken', 'ERROR_REQUEST_TOKEN_PROVIDER_ERROR');
                    $responseResult['res_code'] = FALSE;
            }
        }
    }

    private function verifyTokenRequest($tokenVerifyAttempt, $otp, &$responseResult) {
        $responseResult['res_code'] = $this->verifyPhoneNumber();
        if ($responseResult['res_code'] == -2) {
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_INVALID_MOBILE');
        } else if ($responseResult['res_code'] == -1) {
            $responseResult['res_text'] = "Fail to identify phone number as mobile number.";
        } else {
            $this->verifyTokenAttempt(TRUE, $this->request_type, $tokenVerifyAttempt, $otp);
            return true;
        }
        return false;
    }

    private function verifyPhoneNumber() {
        $classPhoneVerificationComponent = new PhoneVerificationComponent();
        $completeTelephoneNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];
        if (!isset($this->customerCompletePhoneInfoArray['telephone_number']) || empty($this->customerCompletePhoneInfoArray['telephone_number']) || strlen($completeTelephoneNumber) <= 8) {
            // invalid phone format
            return -2;
        } else if ($this->service->shouldVerifyPhone() && !in_array($classPhoneVerificationComponent->getPhoneType($this->customers_id, '', $this->customerCompletePhoneInfoArray['country_international_dialing_code'], $this->customerCompletePhoneInfoArray['telephone_number']), array(2, 3))) {
            // invalid phone number type
            return -1;
        } else {
            $phoneExists = CustomersInfoVerification::model()->searchRecord($this->customers_id, $completeTelephoneNumber, CustomersInfoVerification::$type2);
            if (empty($phoneExists)) {
                $infoVerificationArray = array('customers_id' => $this->customers_id,
                    'customers_info_value' => $completeTelephoneNumber,
                    'serial_number' => '',
                    'verify_try_turns' => 0,
                    'info_verified' => 0,
                    'info_verification_type' => 'telephone',
                    'customers_info_verification_mode' => 'A',
                );

                CustomersInfoVerification::model()->saveCustomerInfoVerification($infoVerificationArray);
            }

            return 1;
        }
    }

    private function verifyTokenAttempt($update, $tokenType, $tokenVerifyAttempt, $otp) {
        $modelCustomersSetting = new CustomersSetting();
        $returnInt = FALSE;    // 1 = new request, 2 = reuse, 3 = resend allowed

        if (isset($tokenVerifyAttempt->customers_setting_value)) {
            $currentRequestCnt = (int) $tokenVerifyAttempt->customers_setting_value;
            if ($tokenVerifyAttempt->same_day_request == '1') {
                if (!empty($otp->customers_otp_digit) && $otp->active_token) {    // Reuse
                    if ($tokenVerifyAttempt->seconds_till_resend <= 0) {    //Allow resend
                        if ($currentRequestCnt >= $this->service->getMaxRequestPerDay()) {
                            $returnInt = 2;
                        } else {
                            $returnInt = 3;
                            $currentRequestCnt += 1;
                        }
                    } else {
                        $returnInt = 2;
                    }
                    if ($update) {
                        $tokenData = array(
                            'customers_setting_value' => $currentRequestCnt,
                            'updated_datetime' => date("Y-m-d H:i:s")
                        );

                        $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $this->customers_id, $tokenType);

                        if ($currentRequestCnt == $this->service->getAlertOnRequestPerDay()) {
                            $this->sendAlertEmailDueToTooManyRequests();
                        }
                    }
                } else {
                    if ($currentRequestCnt >= $this->service->getMaxRequestPerDay()) {
                        $returnInt = FALSE;
                    } else {
                        $returnInt = 1;
                        $currentRequestCnt += 1;

                        if ($update) {
                            $tokenData = array(
                                'customers_setting_value' => $currentRequestCnt,
                                'updated_datetime' => date("Y-m-d H:i:s")
                            );

                            $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $this->customers_id, $tokenType);

                            if ($currentRequestCnt == $this->service->getAlertOnRequestPerDay()) {
                                $this->sendAlertEmailDueToTooManyRequests();
                            }
                        }
                    }
                }
            } else {
                $returnInt = 1;

                if ($update) {
                    $tokenData = array(
                        'customers_setting_value' => '1',
                        'updated_datetime' => date("Y-m-d H:i:s"),
                        'created_datetime' => date("Y-m-d H:i:s")
                    );

                    $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $this->customers_id, $tokenType);
                }
            }
        } else {
            $returnInt = 1;
            if ($update) {
                $tokenData = array(
                    'customers_id' => $this->customers_id,
                    'customers_setting_key' => $tokenType,
                    'customers_setting_value' => '1',
                    'created_datetime' => date("Y-m-d H:i:s"),
                    'updated_datetime' => date("Y-m-d H:i:s")
                );

                $modelCustomersSetting->saveCustomerSetting($tokenData);
            }
        }
        return $returnInt;
    }

    public function validateCustomerSecurityAnswer($answer = '', $removeToken = true) {
        $result = $this->service->validateTokenAnswer($this->customers_id, $this->ip_address, $answer, $this->request_type, $removeToken, $this->customerCompletePhoneInfoArray);
        if ($result['error'] == 1) {
            $attempts_token_type = $this->request_type . '_attempts';
            $modelCustomersSetting = CustomersSetting::model()->getTokenVerifyAttempt($this->customers_id, 0, $attempts_token_type);
            $errors = 1;
            if (isset($modelCustomersSetting->customers_setting_value)) {
                $errors = $modelCustomersSetting->customers_setting_value + 1;
            }

            if ($errors > $this->max_token_validation_attempts) {
                $this->service->invalidateToken($this->customers_id, $this->request_type);
                $result['error'] = 2;
                $result['text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_INVALID_CODE_TOO_MANY_ATTEMPTS');
                $tokenData = array(
                    'customers_id' => $this->customers_id,
                    'customers_setting_key' => $attempts_token_type,
                    'customers_setting_value' => 0,
                    'updated_datetime' => date("Y-m-d H:i:s"),
                    'created_datetime' => date("Y-m-d H:i:s"),
                );
                $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $this->customers_id, $attempts_token_type);
            } else {
                $tokenData = array(
                    'customers_id' => $this->customers_id,
                    'customers_setting_key' => $attempts_token_type,
                    'customers_setting_value' => $errors,
                    'updated_datetime' => date("Y-m-d H:i:s"),
                    'created_datetime' => date("Y-m-d H:i:s"),
                );
                if (isset($modelCustomersSetting)) {
                    $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $this->customers_id, $attempts_token_type);
                } else {
                    CustomersSetting::model()->saveCustomerSetting($tokenData);
                }
                $result['text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_INVALID_CODE_MSG2');
            }
        } else {
            if (!$this->service->shouldVerifyPhone() && (empty($result['do_not_auto_verify_phone']) || $result['do_not_auto_verify_phone'] === false)) {
                //Since this service did not verify phone earlier, that means we use our sms token to assume that the phone is valid and mobile.
                //Thus, we add a data to the database to signify this.
                //This flow only applies to services which supports mobile type verification as part of their verification process
                $modelMaxmindTelephoneIdentification = new MaxmindTelephoneIdentification();
                if (!$modelMaxmindTelephoneIdentification->customerPhoneExists($this->customers_id, $this->customerCompletePhoneInfoArray['country_international_dialing_code'], $this->customerCompletePhoneInfoArray['telephone_number'])) {
                    $telesignPhoneData = array(
                        'customers_id' => $this->customers_id,
                        'customers_country_international_dialing_code' => $this->customerCompletePhoneInfoArray['country_international_dialing_code'],
                        'customers_telephone' => $this->customerCompletePhoneInfoArray['telephone_number'],
                        'maxmind_telephone_identification_id' => '+' . $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'],
                        'requested_date' => date("Y-m-d H:i:s"),
                        'provider' => $this->service->getServiceName(),
                        'customers_telephone_type' => 2, //2 = mobile
                        'city' => 'NULL',
                        'state' => 'NULL',
                        'postcode' => 'NULL',
                        'countries_name' => 'NULL',
                        'latitude' => 'NULL',
                        'longitude' => 'NULL',
                    );
                    $modelMaxmindTelephoneIdentification->saveMaxmindTelephoneIdentification($telesignPhoneData);
                }
            }
            $result['text'] = '';
        }
        return $result;
    }

    public function requestSecurityTokenAgainForEmail($lastDigit) {
        $requestStatus = 0;
        $responseResult = array(
            'res_code' => 0,
            'token' => '',
        );

        if (strlen($lastDigit) != 4) {
            $requestStatus = 0;
        } else {
            $customerInfo = Customers::model()->getCustomerCurrentInfo($this->customers_id);
            if (empty($customerInfo) || $lastDigit != substr($customerInfo['customers_telephone'], -4)) {
                $requestStatus = 0;
            } else {
                $tokenVal = $this->service->regenerateTokenForEmail($this->customers_id, $this->request_type, $this->customerCompletePhoneInfoArray);
                $requestStatus = 1;
                $responseResult['token'] = $tokenVal;
            }
        }
        $responseResult['res_code'] = $requestStatus;
        return $responseResult;
    }

    private function sendAlertEmailDueToTooManyRequests() {
        $message = "Customer requested token verification many times";
        $message .= "\n\nCustomer ID: " . $this->customers_id;
        $message .= "\nVerification Type: " . $this->request_type;
        $message .= "\nCurrent Counter: " . $this->service->getAlertOnRequestPerDay();
        $this->sendAlertEmailToCs($message);
    }

    private function sendAlertEmailToCs($message) {
        $serverName = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-';
        $subject = 'SMS token failed attempt from ' . $serverName;
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: ' . Yii::$app->name . ' REPORTER <<EMAIL>>' . "\r\n";
        $classEmail = new EmailComponent();
        if (isset(Yii::$app->params["API_MERCHANT_INFO"][$this->merchant_id])) {
            $email_add = Yii::$app->params["API_MERCHANT_INFO"][$this->merchant_id]['CS_EMAIL'];
        } else {
            $email_add = Yii::$app->params["GENERAL_CONFIG"]["CS_RECIPIENT"];
        }
        $classEmail->sendInternalMail($email_add, $subject, $message, $headers);
    }
}
