<?php

namespace common\components;

use common\models\AddressBook;
use common\models\Configuration;
use common\models\Countries;
use common\models\Customers;
use common\models\CustomersDocumentIdVerification;
use common\models\CustomersInfo;
use common\models\SnsConnection;
use common\models\StorePoints;
use common\models\Zones;
use Yii;
use yii\base\Component;

class CustomersCom extends Component {

    public static function getConnection($customer_id) {
        $fb_id = 0;
        $res = SnsConnection::findOne(['provider' => 'Facebook', 'customers_id' => $customer_id]);
        if (isset($res->provider_uid))
            $fb_id = $res->provider_uid;

        return $fb_id;
    }

    public static function editAccount($customer_id, $data) {
        $classLogFilesComponent = new LogFilesComponent($customer_id);
        $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
        $classPhoneVerificationComponent = new PhoneVerificationComponent();
        
        $oldLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);
        $customers_default_address_id = $oldLogInfo['customers_default_address_id'];
        $oldAddress = AddressBook::model()->getAddressInfo($customers_default_address_id, $customer_id);

        $error = FALSE;
        $error_array = array();
        $billing_address1 = isset($data['billing_address1']) ? trim($data['billing_address1']) : '';
        $billing_address2 = isset($data['billing_address2']) ? trim($data['billing_address2']) : '';
        $billing_city = isset($data['billing_city']) ? trim($data['billing_city']) : '';
        $billing_state = isset($data['billing_state']) ? trim($data['billing_state']) : NULL;
        $billing_zip = isset($data['billing_zip']) ? trim($data['billing_zip']) : '';
        //billing country cannot update
        $billing_country = isset($oldAddress['entry_country_id']) ? (int) $oldAddress['entry_country_id'] : 0;
        $dialing_code_id = isset($data['dialing_code_id']) ? (int) $data['dialing_code_id'] : 0;
        $mobile_phone_number = isset($data['mobile_phone_number']) ? $data['mobile_phone_number'] : '';

        $billing_zone_id = 0;
        $update_mobile_number = FALSE;
        $allCustomersInfoChangesMade = '';

        $minAdressLength = Configuration::model()->getConfigValue('ENTRY_STREET_ADDRESS_MIN_LENGTH');
        $minCityLength = Configuration::model()->getConfigValue('ENTRY_CITY_MIN_LENGTH');
        $minPostcodeLength = Configuration::model()->getConfigValue('ENTRY_POSTCODE_MIN_LENGTH');

        if (!is_null($oldLogInfo)) {
            $allCustomersInfoChangesMade = $oldLogInfo['customers_info_changes_made'];

            if (empty($oldLogInfo['customers_telephone'])) {
                $update_mobile_number = TRUE;
            } else if ($dialing_code_id === 0) {
                $dialing_code_id = $oldLogInfo['customers_country_dialing_code_id'];
                $mobile_phone_number = $oldLogInfo['customers_telephone'];
            } else {
                $update_mobile_number = TRUE;
            }
        }

        if (empty($billing_address1) || ($billing_address1 != '' && strlen($billing_address1) < $minAdressLength) || ($billing_address2 != '' && strlen($billing_address2) < $minAdressLength)) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_ADDRESS', array('SYS_MIN_LENGTH' => $minAdressLength));
        }

        if (empty($billing_zip) || ($billing_zip != '' && strlen($billing_zip) < $minPostcodeLength)) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_ERR_POSTCODE', array('SYS_MIN_LENGTH' => $minPostcodeLength));
        }

        if (empty($billing_city) || ($billing_city != '' && strlen($billing_city) < $minCityLength)) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_CITY', array('SYS_MIN_LENGTH' => $minCityLength));
        }

        // customer contact number is empty
        if ($update_mobile_number === TRUE) {
            if ($mobile_phone_number != '' && $dialing_code_id != 0) {
                if (is_numeric($mobile_phone_number)) {
                    $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($mobile_phone_number, $dialing_code_id);
                    if (!$parse_result->is_valid_number) {
                        $error = TRUE;
                        $error_array[] = Yii::t('smsToken', 'TEXT_INVALID_MOBILE');
                    } else if (!$parse_result->is_country_match) {
                        $error = TRUE;
                        $error_array[] = Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY');
                    } else {
                        $mobile_phone_number = $parse_result->national_number;
                        if ($classPhoneVerificationComponent->isMobileNumExist($mobile_phone_number, $dialing_code_id, $customer_id, '')) {
                            $error = TRUE;
                            $error_array[] = Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED_NOMAIL');
                        }
                    }
                } else {
                    $error = TRUE;
                    $error_array[] = Yii::t('general', 'ERROR_NUMERICAL', array('SYS_FIELD' => Yii::t('profile', 'ENTRY_MOBILE_PHONE')));
                }
            } else {
                $error = TRUE;

                if ($mobile_phone_number == '') {
                    $error_array[] = Yii::t('profile', 'MSG_ENTRY_CONTACT_NUMBER_ERROR', array('SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH')));
                }

                if ($dialing_code_id == 0) {
                    $error_array[] = Yii::t('profile', 'MSG_ENTRY_COUNTRY_ERROR');
                }
            }
        }

        if (empty($billing_country) || ($billing_country != '' && !is_numeric($billing_country))) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_ENTRY_COUNTRY_ERROR');
        } else {
            $entry_state_has_zones = Zones::model()->getTotalState($billing_country);

            if ($entry_state_has_zones > 0) {
                $stateExists = Zones::model()->stateExists($billing_country, $billing_state);

                if ($stateExists) {
                    $billing_zone_id = $billing_state;
                    $billing_state = '';
                } else {
                    $error = TRUE;
                    $error_array[] = Yii::t('profile', 'MSG_STATE_ID_ERROR');
                }
            } else {
                $minStateLength = Configuration::model()->getConfigValue('ENTRY_STATE_MIN_LENGTH');
                
                if (empty($billing_state) || ($billing_state != '' && strlen($billing_state) < $minStateLength)) {
                    $error = TRUE;
                    $error_array[] = Yii::t('profile', 'MSG_STATE_ERROR', array('SYS_MIN_LENGTH' => $minStateLength));
                }
            }
        }

        if ($error !== TRUE) {
            $country_id_row = Countries::model()->getDialingInfo($dialing_code_id);
            if (isset($country_id_row->countries_id)) {
                $error = TRUE;
                $error_array[] = Yii::t('profile', 'MSG_ENTRY_COUNTRY_CODE_ERROR', array("SYS_COUNTRY_CODE" => Yii::t('apiModule.customer', 'TEXT_COUNTRY_DIALING_CODE_ID'), "SYS_CHANGE_MOBILE_NUMBER" => Yii::t('profile', 'LINK_CHANGE_PHONE_NUMBER')));
            }
        }

        if ($error !== TRUE) {
            $customerChangesArray = array();
            $customerChangesFormattedArray = array();
            $newLogInfo = null;

            if ($update_mobile_number === TRUE) {
                $sql_data_array = array(
                    'customers_country_dialing_code_id' => $dialing_code_id,
                    'customers_telephone' => $mobile_phone_number,
                );
                
                Customers::model()->updateCustomerInfo($sql_data_array, $customer_id);
                CustomersInfo::model()->updateLastModify($customer_id);
                $newLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);
                $customerChangesArray = $classLogFilesComponent->detectChanges($oldLogInfo, $newLogInfo);
                $customerChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerChangesArray);
            } else {
                $newLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);
            }

            $sql_address_data_array = array(
                'entry_street_address' => $billing_address1,
                'entry_suburb' => $billing_address2,
                'entry_zone_id' => $billing_zone_id,
                'entry_state' => $billing_state,
                'entry_postcode' => $billing_zip,
                'entry_city' => $billing_city,
                // 'entry_country_id' => $billing_country
            );

            $oldAddress = AddressBook::model()->getAddressInfo($customers_default_address_id, $customer_id);

            if ($oldAddress) {
                $customers_default_address_id = $oldAddress->address_book_id;
                AddressBook::model()->updateCustomerInfo($sql_address_data_array, $customers_default_address_id, $customer_id);
            }

            $oldAddressInfo['entry_street_address'] = $oldAddress['entry_street_address'];
            $oldAddressInfo['entry_suburb'] = $oldAddress['entry_suburb'];
            $oldAddressInfo['entry_postcode'] = $oldAddress['entry_postcode'];
            $oldAddressInfo['entry_city'] = $oldAddress['entry_city'];
            $oldAddressInfo['entry_state'] = $oldAddress['entry_state'];
            // $oldAddressInfo['entry_country_id'] = $oldAddress['entry_country_id'];
            $oldAddressInfo['entry_zone_id'] = $oldAddress['entry_zone_id'];

            if ((int) $oldAddress["entry_zone_id"] > 0) {
                if ($oldAddress['entry_zone_id'] > 0) {
                    $oldAddressInfo['entry_state'] = Zones::model()->getStateName($oldAddress['entry_country_id'], $oldAddress['entry_zone_id']);
                }
            }

            $newAddress = AddressBook::model()->getAddressInfo($customers_default_address_id, $customer_id);
            $newAddressInfo['entry_street_address'] = $newAddress['entry_street_address'];
            $newAddressInfo['entry_suburb'] = $newAddress['entry_suburb'];
            $newAddressInfo['entry_postcode'] = $newAddress['entry_postcode'];
            $newAddressInfo['entry_city'] = $newAddress['entry_city'];
            $newAddressInfo['entry_state'] = $newAddress['entry_state'];
            // $newAddressInfo['entry_country_id'] = $newAddress['entry_country_id'];
            $newAddressInfo['entry_zone_id'] = $newAddress['entry_zone_id'];

            if ((int) $newAddress["entry_zone_id"] > 0) {
                $newAddressInfo['entry_state'] = Zones::model()->getStateName($oldAddress['entry_country_id'], $newAddressInfo['entry_zone_id']);
            }

            $customerAddressChangesArray = $classLogFilesComponent->detectChanges($oldAddressInfo, $newAddressInfo);
            $customerAddressChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerAddressChangesArray);

            $customerChangesArray = array_merge($customerChangesArray, $customerAddressChangesArray);
            $customerChangesFormattedArray = array_merge($customerChangesFormattedArray, $customerAddressChangesFormattedArray);

            $allCustomersInfoChangesMade = $classLogFilesComponent->contructChangesString($customerChangesArray, $allCustomersInfoChangesMade);

            if (count($customerChangesFormattedArray)) {
                $changesStr = 'Changes made:' . "\n";

                for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                    if (count($customerChangesFormattedArray[$i])) {
                        foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $changesStr .= $res['text'] . "\n";
                            } else {
                                $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }

                $classLogFilesComponent->insertCustomerHistoryLog($newLogInfo['customers_email_address'], $changesStr);
            }

            CustomersInfo::model()->updateChangeMade($allCustomersInfoChangesMade);
        }

        return array(
            'error' => $error,
            'error_array' => $error_array,
            'customers_country_dialing_code_id' => $dialing_code_id,
            'customers_telephone' => $mobile_phone_number
        );
    }

    public static function account() {
        $rewardPoint = number_format(StorePoints::model()->getCustomersStorePoint(), 0, '.', ',');

        return array('rewardPoint' => $rewardPoint);
    }

    public static function isCustomerNameLocked($customer_id) {
        $cust_verify = CustomersDocumentIdVerification::findOne(['customers_id' => $customer_id]);
        return $cust_verify && $cust_verify->name_lock > time();
    }

    public static function updateCustomerName($customer_id, $data) {
        $classLogFilesComponent = new LogFilesComponent($customer_id);

        $error = FALSE;
        $error_array = array();
        $first_name = isset($data['first_name']) ? trim($data['first_name']) : '';
        $last_name = isset($data['last_name']) ? trim($data['last_name']) : '';

        $customers_default_address_id = 0;
        $allCustomersInfoChangesMade = '';

        $oldLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);

        if (!is_null($oldLogInfo)) {
            $customers_default_address_id = $oldLogInfo['customers_default_address_id'];
            $allCustomersInfoChangesMade = $oldLogInfo['customers_info_changes_made'];
        }

        $minFirstName = Configuration::model()->getConfigValue('ENTRY_FIRST_NAME_MIN_LENGTH');
        $minLastName = Configuration::model()->getConfigValue('ENTRY_LAST_NAME_MIN_LENGTH');

        $oldLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);

        if (strlen($first_name) < $minFirstName) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_ERR_MINIMUM_FIRSTNAME', array('SYS_MIN_LENGTH' => $minFirstName));
        }

        if (!empty($last_name) && strlen($last_name) < $minLastName) {
            $error = TRUE;
            $error_array[] = Yii::t('profile', 'MSG_ERR_MINIMUM_LASTNAME', array('SYS_MIN_LENGTH' => $minLastName));
        }

        if ($error !== TRUE) {
            // Update Name
            $sql_data_array = array(
                'customers_firstname' => $first_name,
                'customers_lastname' => $last_name
            );

            Customers::model()->updateCustomerInfo($sql_data_array, $customer_id);
            CustomersInfo::model()->updateLastModify($customer_id);
            $newLogInfo = Customers::model()->getCustomerCurrentInfo($customer_id);
            $customerChangesArray = $classLogFilesComponent->detectChanges($oldLogInfo, $newLogInfo);

            $customerChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerChangesArray);

            $sql_address_data_array = array(
                'entry_firstname' => $first_name,
                'entry_lastname' => $last_name,
            );

            $oldAddress = AddressBook::model()->getAddressInfo($customers_default_address_id, $customer_id);

            if ($oldAddress) {
                $customers_default_address_id = $oldAddress->address_book_id;
                AddressBook::model()->updateCustomerInfo($sql_address_data_array, $customers_default_address_id, $customer_id);
            }

            $oldAddressInfo['entry_street_address'] = $oldAddress['entry_street_address'];
            $oldAddressInfo['entry_suburb'] = $oldAddress['entry_suburb'];
            $oldAddressInfo['entry_postcode'] = $oldAddress['entry_postcode'];
            $oldAddressInfo['entry_city'] = $oldAddress['entry_city'];
            $oldAddressInfo['entry_state'] = $oldAddress['entry_state'];
            $oldAddressInfo['entry_country_id'] = $oldAddress['entry_country_id'];
            $oldAddressInfo['entry_zone_id'] = $oldAddress['entry_zone_id'];

            if ((int) $oldAddress["entry_zone_id"] > 0) {
                if ($oldAddress['entry_zone_id'] > 0) {
                    $oldAddressInfo['entry_state'] = Zones::model()->getStateName($oldAddress['entry_country_id'], $oldAddress['entry_zone_id']);
                }
            }

            $newAddress = AddressBook::model()->getAddressInfo($customers_default_address_id, $customer_id);
            $newAddressInfo['entry_street_address'] = $oldAddress['entry_street_address'];
            $newAddressInfo['entry_suburb'] = $oldAddress['entry_suburb'];
            $newAddressInfo['entry_postcode'] = $oldAddress['entry_postcode'];
            $newAddressInfo['entry_city'] = $oldAddress['entry_city'];
            $newAddressInfo['entry_state'] = $oldAddress['entry_state'];
            $newAddressInfo['entry_country_id'] = $oldAddress['entry_country_id'];
            $newAddressInfo['entry_zone_id'] = $oldAddress['entry_zone_id'];

            $customerAddressChangesArray = $classLogFilesComponent->detectChanges($oldAddressInfo, $newAddressInfo);
            $customerAddressChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerAddressChangesArray);

            $customerChangesArray = array_merge($customerChangesArray, $customerAddressChangesArray);
            $customerChangesFormattedArray = array_merge($customerChangesFormattedArray, $customerAddressChangesFormattedArray);

            $allCustomersInfoChangesMade = $classLogFilesComponent->contructChangesString($customerChangesArray, $allCustomersInfoChangesMade);

            if (count($customerChangesFormattedArray)) {
                $changesStr = 'Changes made:' . "\n";

                for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                    if (count($customerChangesFormattedArray[$i])) {
                        foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $changesStr .= $res['text'] . "\n";
                            } else {
                                $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }

                $classLogFilesComponent->insertCustomerHistoryLog($newLogInfo['customers_email_address'], $changesStr);
            }

            CustomersInfo::model()->updateChangeMade($allCustomersInfoChangesMade);
        }

        return array(
            'error' => $error,
            'error_array' => $error_array,
        );
    }

    public static function lockCustomerName($customer_id) {
        $cust_verify = CustomersDocumentIdVerification::findOne(['customers_id' => $customer_id]);
        if (!$cust_verify) {
            $cust_verify = new CustomersDocumentIdVerification([
                'customers_id' => $customer_id,
                'customers_firstname' => '',
                'customers_lastname' => '',
                'customers_edit_firstname' => '',
                'customers_edit_lastname' => '',
                'name_lock' => time(),
                'verify_status' => 1,
                'waiting_for_document' => 0,
                'isAuto' => 1,
                'updated_at' => time(),
            ]);
        }
        $cust_verify->name_lock = time() + (60 * 24 * 60 * 60);
        if (!$cust_verify->save(false)) {
            \Yii::$app->reporter->reportToAdminViaSlack('Customer name lock failed', $cust_verify);
        }

        $classLogFilesComponent = new LogFilesComponent($customer_id);
        $classLogFilesComponent->insertCustomerHistoryLog("system", "Customer name change locked");
    }
}