<?php

namespace common\components;

use common\models\AddressBook;
use common\models\Countries;
use common\models\Customers;
use Yii;

class CustomersInfoVerificationComponent {

    public function parseTelephone($telephone, $ctry_id) {
        $returnResult = new PhoneParseResult();
        $returnResult->is_valid_number = false;
        $returnResult->is_country_match = false;
        $returnResult->region_code = null;
        $returnResult->national_number = $telephone;
        $returnResult->dialing_code = null;
        
        $country = Countries::getCountryDetails($ctry_id);
        if (!$country) {
            return $returnResult;
        }
        $returnResult->region_code = $country['countries_iso_code_2'];
        $returnResult->dialing_code = $country['countries_international_dialing_code'];
        
        $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        try {
            $parseResult = $phoneUtil->parse('+' . preg_replace('/[\D]/', '', $returnResult->dialing_code . $telephone));
        } catch (\libphonenumber\NumberParseException $ex) {
            return $returnResult;
        }
        
        $returnResult->is_valid_number = $phoneUtil->isValidNumber($parseResult);
        $returnResult->region_code = $phoneUtil->getRegionCodeForNumber($parseResult);
        $returnResult->is_country_match = $returnResult->region_code == $country['countries_iso_code_2'];
        $returnResult->dialing_code = '' . $parseResult->getCountryCode();
        $e164 = $phoneUtil->format($parseResult, \libphonenumber\PhoneNumberFormat::E164);
        if ($e164 && strpos($e164, '+' . $returnResult->dialing_code) === 0) {
            $returnResult->national_number = str_replace('+' . $returnResult->dialing_code, '', $e164);
        }
        return $returnResult;
    }

    public function formatTelephone($customer_id = 0) {
        $customer_id = $customer_id > 0 ? $customer_id : Yii::$app->user->id;

        $modelCustomers = new Customers();
        $modelAddressBook = new AddressBook();
        $modelCountries = new Countries();
        $customerTelephone = array();

        $phoneInfo = $modelCustomers->getPhoneInfo($customer_id);
        if (!empty($phoneInfo)) {
            $telephoneNotStandardFormat = $phoneInfo['customers_telephone'];
            $telephoneNotStandardFormat = preg_replace('/[^\d]/', '', $telephoneNotStandardFormat);
            $countryDialingCodeId = $phoneInfo['customers_country_dialing_code_id'];
            $customerDefaultAddressId = $phoneInfo['customers_default_address_id'];

            if (empty($countryDialingCodeId)) {
                $countryDialingCodeId = $modelAddressBook->getEntryCountryId($customerDefaultAddressId, $customer_id);
                if (isset($countryDialingCodeId)) {
                    $modelCustomers->updateCountryDialingCodeId($countryDialingCodeId, $customer_id);
                }
            }
            if (!empty($countryDialingCodeId)) {
                $countryInfo = $modelCountries->getDialingInfo($countryDialingCodeId);
                $countryInternationalDialingCode = $countryInfo['countries_international_dialing_code'];
                $countryName = $countryInfo['countries_name'];

                $parse_result = $this->parseTelephone($telephoneNotStandardFormat, $countryDialingCodeId);

                $customerTelephone = ['country_id' => $countryDialingCodeId, 'country_name' => $countryName, 'country_international_dialing_code' => $countryInternationalDialingCode, 'telephone_number' => $parse_result->national_number, 'is_country_match' => $parse_result->is_country_match, 'is_valid_number' => $parse_result->is_valid_number];
            }
        }
        
        return $customerTelephone;
    }

    function fourDigitCodeGenerate() {
        $code = rand(1000, 9999);

        return $code;
    }

    public static function fileUploadNotification($user_id = null) {
        if (!$user_id) {
            $user_id = Yii::$app->user->id;
        }
        MerchantNotification::notifyOnCustomerFileUploaded($user_id);
    }
}

class PhoneParseResult {
    public $is_valid_number;
    public $is_country_match;
    public $region_code;
    public $national_number;
    public $dialing_code;
}