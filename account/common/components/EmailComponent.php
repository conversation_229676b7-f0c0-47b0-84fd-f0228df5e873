<?php

namespace common\components;

use common\models\Configuration;
use Yii;
use yii\base\Component;

class EmailComponent extends Component {

    public $html;
    public $text;
    public $output;
    public $html_text;
    public $html_images;
    public $image_types;
    public $build_params;
    public $attachments;
    public $headers;
    public $lf;
    public $emailLineFeed, $emailUseHtml, $emailTransport;

    public function init() {
        parent::init();
        $this->emailLineFeed = Configuration::model()->getConfigValue('EMAIL_LINEFEED');
        $this->emailUseHtml = Configuration::model()->getConfigValue('EMAIL_USE_HTML');
        $this->emailTransport = Configuration::model()->getConfigValue('EMAIL_TRANSPORT');
    }

    function initEmail($headers = '') {
        if ($headers == '')
            $headers = array();

        $this->html_images = array();
        $this->headers = array();

        if ($this->emailLineFeed == 'CRLF') {
            $this->lf = "\r\n";
        } else {
            $this->lf = "\n";
        }

        /**
         * If you want the auto load functionality
         * to find other mime-image/file types, add the
         * extension and content type here.
         */
        $this->image_types = array('gif' => 'image/gif',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpe' => 'image/jpeg',
            'bmp' => 'image/bmp',
            'png' => 'image/png',
            'tif' => 'image/tiff',
            'tiff' => 'image/tiff',
            'swf' => 'application/x-shockwave-flash');

        $this->build_params['html_encoding'] = 'quoted-printable';
        $this->build_params['text_encoding'] = '7bit';
        $this->build_params['html_charset'] = 'UTF-8'; //constant('CHARSET');
        $this->build_params['text_charset'] = 'UTF-8'; //constant('CHARSET');
        $this->build_params['text_wrap'] = 998;

        /**
         * Make sure the MIME version header is first.
         */
        $this->headers[] = 'MIME-Version: 1.0';

        reset($headers);

        while (list(, $value) = each($headers)) {
            if (!empty($value)) {
                $this->headers[] = $value;
            }
        }
    }

    function setRenderEmailhtml($view, $params = []) {
        $v = Yii::createObject([
                    'class' => \yii\web\View::className(),
        ]);
        $content_html = $v->render('@common/mail/' . $view, $params);
        $content_html = $v->render('@common/mail/layout/html', [
            'content_html' => $content_html,
        ]);
        return $content_html;
    }

    function setRenderEmailText($view, $params = []) {//text layout
        $v = Yii::createObject([
                    'class' => \yii\web\View::className(),
        ]);
        $content_text = $v->render('@common/mail/' . $view, $params);
        $content_text = $v->render('@common/mail/layout/text', [
            'content_text' => $content_text,
        ]);
        return $content_text;
    }

    function sendMail($toName, $toEmailAddress, $emailSubject, $emailArr, $fromEmailName, $fromEmailAddress, $bccEmailAddress = '') {
        if (Configuration::model()->getConfigValue('SEND_EMAILS') != 'true')
            return false;

        $sentStatus = false;
        $extraHeader = '';

        $letter = array();
        $letter['message']['subject'] = $emailSubject;
        $letter['message']['body'] = $emailArr;
        $letter['envelope']['to'] = array('name' => $toName, 'address' => $toEmailAddress);
        $letter['envelope']['from'] = array('name' => $fromEmailName, 'address' => $fromEmailAddress);
        $letter['envelope']['bcc'] = $bccEmailAddress;

        $aws_obj = new OgmAmazonWs();
        if ($aws_obj->send_mail_by_ses_controller($letter)) {
            $sentStatus = $aws_obj->send_mail_by_ses($letter);
        }
        //Do not send email via X-Mailer anymore - if SES fails, we skip email sending
        //Ref: Email: Fwd: Your AWS Abuse Report [13990641217] [AWS ID 305050326984]
        return;

        if (!$sentStatus) {
            // Instantiate a new mail object
            $this->initEmail(array('X-Mailer: php', 'Reply-To: ' . $fromEmailAddress));

            // Build the text version
            if (isset($emailArr['content_html'])) {
                $text = strip_tags($emailArr["content_text"]);
                if ($this->emailUseHtml == 'true') {
                    $this->addHtml($emailArr["content_html"], $text, false); //if both text either one will be only text and html
                } else {
                    $this->addText($text);
                }
            } else {
                $text = strip_tags($emailArr);
                if ($this->emailUseHtml == 'true') {
                    $this->addHtml($emailArr, $text);
                } else {
                    $this->addText($text);
                }
            }

            if ($bccEmailAddress)
                $extraHeader .= "Bcc: $" . $bccEmailAddress . "\r\n";

            // Send message
            $this->buildMessage();
            $this->send($toName, $toEmailAddress, $fromEmailName, $fromEmailAddress, $emailSubject, $extraHeader);
        }

        unset($aws_obj, $letter);
    }

    function addHtml($html, $text = NULL, $convert_lf = true) {
        if ($convert_lf) {
            $this->html = GeneralComponent::convertLinefeeds(array("\r\n", "\n", "\r"), '<br>', $html);
        } else {
            $this->html = $html;
        }
        $this->html_text = GeneralComponent::convertLinefeeds(array("\r\n", "\n", "\r"), $this->lf, $text);
    }

    function addText($text = '') {
        $this->text = GeneralComponent::convertLinefeeds(array("\r\n", "\n", "\r"), $this->lf, $text);
    }

    function add_text_part(&$obj, $text) {
        $params['content_type'] = 'text/plain';
        $params['encoding'] = $this->build_params['text_encoding'];
        $params['charset'] = $this->build_params['text_charset'];

        if (is_object($obj)) {
            return $obj->addSubpart($text, $params);
        } else {
            return new MimeComponent($text, $params);
        }
    }

    function add_mixed_part() {
        $params['content_type'] = 'multipart/mixed';

        return new MimeComponent('', $params);
    }

    function add_html_part(&$obj) {
        $params['content_type'] = 'text/html';
        $params['encoding'] = $this->build_params['html_encoding'];
        $params['charset'] = $this->build_params['html_charset'];

        if (is_object($obj)) {
            return $obj->addSubpart($this->html, $params);
        } else {
            return new MimeComponent($this->html, $params);
        }
    }

    function add_alternative_part(&$obj) {
        $params['content_type'] = 'multipart/alternative';

        if (is_object($obj)) {
            return $obj->addSubpart('', $params);
        } else {
            return new MimeComponent('', $params);
        }
    }

    function add_related_part(&$obj) {
        $params['content_type'] = 'multipart/related';

        if (is_object($obj)) {
            return $obj->addSubpart('', $params);
        } else {
            return new MimeComponent('', $params);
        }
    }

    function add_html_image_part(&$obj, $value) {
        $params['content_type'] = $value['c_type'];
        $params['encoding'] = 'base64';
        $params['disposition'] = 'inline';
        $params['dfilename'] = $value['name'];
        $params['cid'] = $value['cid'];

        $obj->addSubpart($value['body'], $params);
    }

    function add_attachment_part(&$obj, $value) {
        $params['content_type'] = $value['c_type'];
        $params['encoding'] = $value['encoding'];
        $params['disposition'] = 'attachment';
        $params['dfilename'] = $value['name'];

        $obj->addSubpart($value['body'], $params);
    }

    function buildMessage($params = '') {
        if ($params == '')
            $params = array();

        if (count($params) > 0) {
            reset($params);
            while (list($key, $value) = each($params)) {
                $this->build_params[$key] = $value;
            }
        }

        if (!empty($this->html_images)) {
            reset($this->html_images);
            while (list(, $value) = each($this->html_images)) {
                $this->html = str_replace($value['name'], 'cid:' . $value['cid'], $this->html);
            }
        }

        $null = NULL;
        $attachments = (!empty($this->attachments) ? true : false);
        $html_images = (!empty($this->html_images) ? true : false);
        $html = (!empty($this->html) ? true : false);
        $text = (!empty($this->text) ? true : false);

        switch (true) {
            case (($text == true) && ($attachments == false)):
                /* HPDL PHP3 */
                $message = $this->add_text_part($null, $this->text);
                break;
            case (($text == false) && ($attachments == true) && ($html == false)):
                /* HPDL PHP3 */
                $message = $this->add_mixed_part();

                for ($i = 0; $i < count($this->attachments); $i++) {
                    $this->add_attachment_part($message, $this->attachments[$i]);
                }
                break;
            case (($text == true) && ($attachments == true)):
                /* HPDL PHP3 */
                $message = $this->add_mixed_part();
                $this->add_text_part($message, $this->text);

                for ($i = 0; $i < count($this->attachments); $i++) {
                    $this->add_attachment_part($message, $this->attachments[$i]);
                }
                break;
            case (($html == true) && ($attachments == false) && ($html_images == false)):
                if (!empty($this->html_text)) {
                    /* HPDL PHP3 */
                    $message = $this->add_alternative_part($null);
                    $this->add_text_part($message, $this->html_text);
                    $this->add_html_part($message);
                } else {
                    /* HPDL PHP3 */
                    $message = $this->add_html_part($null);
                }
                break;
            case (($html == true) && ($attachments == false) && ($html_images == true)):
                if (!empty($this->html_text)) {
                    /* HPDL PHP3 */
                    $message = $this->add_alternative_part($null);
                    $this->add_text_part($message, $this->html_text);
                    /* HPDL PHP3 */
                    $related = $this->add_related_part($message);
                } else {
                    /* HPDL PHP3 */
                    $message = $this->add_related_part($null);
                    $related = $message;
                }
                $this->add_html_part($related);

                for ($i = 0; $i < count($this->html_images); $i++) {
                    $this->add_html_image_part($related, $this->html_images[$i]);
                }
                break;
            case (($html == true) && ($attachments == true) && ($html_images == false)):
                /* HPDL PHP3 */
                $message = $this->add_mixed_part();
                if (!empty($this->html_text)) {
                    /* HPDL PHP3 */
                    $alt = $this->add_alternative_part($message);
                    $this->add_text_part($alt, $this->html_text);
                    $this->add_html_part($alt);
                } else {
                    $this->add_html_part($message);
                }

                for ($i = 0; $i < count($this->attachments); $i++) {
                    $this->add_attachment_part($message, $this->attachments[$i]);
                }
                break;
            case (($html == true) && ($attachments == true) && ($html_images == true)):
                /* HPDL PHP3 */
                $message = $this->add_mixed_part();

                if (GeneralComponent::notNull($this->html_text)) {
                    /* HPDL PHP3 */
                    $alt = $this->add_alternative_part($message);
                    $this->add_text_part($alt, $this->html_text);
                    /* HPDL PHP3 */
                    $rel = $this->add_related_part($alt);
                } else {
                    /* HPDL PHP3 */
                    $rel = $this->add_related_part($message);
                }
                $this->add_html_part($rel);

                for ($i = 0; $i < count($this->html_images); $i++) {
                    $this->add_html_image_part($rel, $this->html_images[$i]);
                }

                for ($i = 0; $i < count($this->attachments); $i++) {
                    $this->add_attachment_part($message, $this->attachments[$i]);
                }
                break;
        }

        if ((isset($message)) && (is_object($message))) {
            $output = $message->encode();
            $this->output = $output['body'];

            reset($output['headers']);
            while (list($key, $value) = each($output['headers'])) {
                $headers[] = $key . ': ' . $value;
            }

            $this->headers = array_merge($this->headers, $headers);

            return true;
        } else {
            return false;
        }
    }

    function send($toName, $toAddr, $fromName, $fromAddr, $subject = '', $headers = '') {
        $to = (($toName != '') ? '"' . $toName . '" <' . $toAddr . '>' : $toAddr);
        $from = (($fromName != '') ? '"' . $fromName . '" <' . $fromAddr . '>' : $fromAddr);

        if (is_string($headers)) {
            $headers = explode($this->lf, trim($headers));
        }

        for ($i = 0; $i < count($headers); $i++) {
            if (is_array($headers[$i])) {
                for ($j = 0; $j < count($headers[$i]); $j++) {
                    if ($headers[$i][$j] != '') {
                        $xtraHeaders[] = $headers[$i][$j];
                    }
                }
            }

            if ($headers[$i] != '') {
                $xtraHeaders[] = $headers[$i];
            }
        }

        if (!isset($xtraHeaders)) {
            $xtraHeaders = array();
        }
        if ($this->emailTransport == 'smtp') {
            return mail($toAddr, $subject, $this->output, 'From: ' . $from . $this->lf . 'To: ' . $to . $this->lf . implode($this->lf, $this->headers) . $this->lf . implode($this->lf, $xtraHeaders));
        } else {
            return mail($to, $subject, $this->output, 'From: ' . $from . $this->lf . implode($this->lf, $this->headers) . $this->lf . implode($this->lf, $xtraHeaders));
        }
    }

    public static function sendInternalMail($mailto, $subject = '', $message = '', $header = '') {
        \Yii::$app->reporter->reportToAdminViaSlack($subject, $message);
    }

}
