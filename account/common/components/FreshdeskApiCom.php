<?php

namespace common\components;

use common\models\Countries;
use common\models\Customers;
use common\models\CustomersSetting;
use api\components\ApiGeneralCom;
use common\components\EmailComponent;
use common\models\CustomersInfoVerification;
use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;
use yii\helpers\Url;

abstract class FreshdeskApiCom extends Component
{

    //$non_required_field  please refer freshdesk for non required field https://developers.freshdesk.com/api/#create_ticket

    const FD_EMAIL_VERIFICATION = "fd_email_verification";

    const CONFIG_TAG_TICKET_OG = 'TICKET_OG';

    const CONFIG_TAG_SSO_OG = 'SSO_OG';

    public $config_tag;
    public $enabled;
    public $api_key;
    public $api_password;
    public $api_domain;
    public $redirect_uri;

    protected $api_retries = 2;

    /**
     * @param string $config_tag
     * @return object
     */
    public static function get($config_tag, $client_id = '')
    {
        $config_tag = strtoupper($config_tag);
        if ($config_tag == self::CONFIG_TAG_TICKET_OG) {
            if (empty(\Yii::$app->params['FRESHDESK_API'][$config_tag])) {
                throw new InvalidConfigException("FRESHDESK_API[$config_tag] not set up");
            }
            $config = \Yii::$app->params['FRESHDESK_API'][$config_tag];
        } else {
            if (empty(\Yii::$app->params['FRESHDESK_API'][$config_tag][$client_id])) {
                throw new InvalidConfigException("FRESHDESK_API[$config_tag][$client_id] not set up");
            }
            $config = \Yii::$app->params['FRESHDESK_API'][$config_tag][$client_id];
        }
        $config['config_tag'] = $config_tag;
        return \Yii::createObject($config);
    }

    public function init()
    {
        parent::init();
        $this->enabled = isset($this->enabled) && $this->enabled;
        if ($this->enabled) {
            if (empty($this->api_key)) {
                throw new InvalidConfigException("FRESHDESK_API[$this->config_tag] does not have API_KEY");
            }
            if (empty($this->api_password)) {
                throw new InvalidConfigException("FRESHDESK_API[$this->config_tag] does not have PASSWORD");
            }
            if (empty($this->api_domain)) {
                throw new InvalidConfigException("FRESHDESK_API[$this->config_tag] does not have DOMAIN");
            }
        }
    }

    public function apiListUser($email_address, $retry = 0)
    {
        $params = [
            'email' => $email_address,
        ];
        $api_response = $this->callGetApi('contacts', $params);
        $info = isset($api_response['info']) ? $api_response['info'] : [];
        $response = isset($api_response['response']) ? $api_response['response'] : '';
        $exception = isset($api_response['exception']) ? $api_response['exception'] : null;

        if (isset($info['http_code']) && $info['http_code'] == 200) {
            return json_decode($response, true);
        } elseif (!$this->enabled) {
            return []; //Default value if Freshdesk API is not enabled
        } elseif ($retry < $this->api_retries) {
            $retry++;
            return $this->apiListUser($email_address, $retry);
        } else {
            if (!empty($exception)) {
                $content = $exception;
            } else {
                $content = [
                    'info' => $info,
                    'response' => $response,
                    'params' => $params,
                ];
            }
            $response_array = json_decode($response, true);

            if (isset($response_array['errors']) && $response_array['errors'][0]["code"] == "invalid_value") {
                return null;
            } else {
                $customer = Customers::find()->where(["customers_email_address" => $email_address])->one();
                $cid = (isset($customer->customers_id)) ? $customer->customers_id : $email_address;
                Yii::$app->reporter->reportToAdminViaSlack('Freskdesk list user API failed for customer ' . $cid, $content);
            }
        }
        return null;
    }

    public function apiCreateUser($params, $retry = 0)
    {
        if (!empty($params['unique_external_id'])) {
            $params['unique_external_id'] = static::generateUniqueExternalCid($params['unique_external_id']);
        }
        $api_response = $this->callPostApi('contacts', $params);
        $info = isset($api_response['info']) ? $api_response['info'] : [];
        $response = isset($api_response['response']) ? $api_response['response'] : '';
        $exception = isset($api_response['exception']) ? $api_response['exception'] : null;

        if (isset($info['http_code']) && $info['http_code'] == 201) {
            return json_decode($response, true);
        } elseif (!$this->enabled) {
            return null; //Default value if Freshdesk API is not enabled
        } elseif ($retry < $this->api_retries) {
            $retry++;
            return $this->apiCreateUser($params, $retry);
        } else {
            $cid = null;
            if (isset($params['unique_external_id'])) {
                $cid = $params['unique_external_id'];
            } elseif (isset($params['email'])) {
                $cid = $params['email'];
            }
            if (!empty($exception)) {
                $content = $exception;
            } else {
                $content = [
                    'info' => $info,
                    'response' => $response,
                    'params' => $params,
                ];
            }
            Yii::$app->reporter->reportToAdminViaSlack('Freskdesk create user API failed for customer ' . $cid, $content);
        }
    }

    public function apiUpdateUser($fd_id, $params, $retry = 0)
    {
        if (!empty($params['unique_external_id'])) {
            $params['unique_external_id'] = static::generateUniqueExternalCid($params['unique_external_id']);
        }
        $api_response = $this->callPostApi('contacts/' . $fd_id, $params, 'PUT');
        $info = isset($api_response['info']) ? $api_response['info'] : [];
        $response = isset($api_response['response']) ? $api_response['response'] : '';
        $exception = isset($api_response['exception']) ? $api_response['exception'] : null;

        if (isset($info['http_code']) && $info['http_code'] == 200) {
            return json_decode($response, true);
        } elseif (!$this->enabled) {
            return null; //Default value if Freshdesk API is not enabled
        } elseif ($retry < $this->api_retries) {
            $retry++;
            return $this->apiUpdateUser($fd_id, $params, $retry);
        } else {
            $cid = null;
            if (isset($params['unique_external_id'])) {
                $cid = $params['unique_external_id'];
            } elseif (isset($params['email'])) {
                $cid = $params['email'];
            }
            if (!empty($exception)) {
                $content = $exception;
            } else {
                $content = [
                    'info' => $info,
                    'response' => $response,
                    'params' => $params,
                ];
            }
            Yii::$app->reporter->reportToAdminViaSlack('Freskdesk update user API failed for customer ' . $cid, $content);
        }
    }

    protected function callPostApi($endpoint, $params, $method = 'POST')
    {
        if (!$this->enabled) {
            Yii::$app->reporter->reportToAdminViaSlack('Freshdesk calling POST API ' . $endpoint, $params);
            return;
        }
        $post_data = json_encode($params);
        $url = "https://$this->api_domain.freshdesk.com/api/v2/" . $endpoint;
        $header[] = "Content-type: application/json";
        $ch = curl_init($url);
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
        } else {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "$this->api_key:$this->api_password");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        try {
            $server_output = curl_exec($ch);
            $info = curl_getinfo($ch);
            $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($server_output, 0, $header_size);
            $response = substr($server_output, $header_size);
            curl_close($ch);
            return [
                'info' => $info,
                'response' => $response,
            ];
        } catch (\Exception $ex) {
            return [
                'exception' => $ex,
            ];
        }
    }

    protected function callGetApi($endpoint, $params)
    {
        if (!$this->enabled) {
            Yii::$app->reporter->reportToAdminViaSlack('Freshdesk calling GET API ' . $endpoint, $params);
            return;
        }
        $url = "https://$this->api_domain.freshdesk.com/api/v2/" . $endpoint;
        if ($params) {
            $url .= "?" . http_build_query($params);
        }
        $header[] = "Content-type: application/json";
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_USERNAME, $this->api_key);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        try {
            $server_output = curl_exec($ch);
            $info = curl_getinfo($ch);
            $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($server_output, 0, $header_size);
            $response = substr($server_output, $header_size);
            curl_close($ch);
            return [
                'info' => $info,
                'response' => $response,
            ];
        } catch (\Exception $ex) {
            return [
                'exception' => $ex,
            ];
        }
    }

    public static function flagUserFdVerification($cid, $new_email_address)
    {
        $user_fd_setting = CustomersSetting::find()->where(["customers_id" => $cid, "customers_setting_key" => FreshdeskApiCom::FD_EMAIL_VERIFICATION, "customers_setting_value" => '1'])->one();
        if ($user_fd_setting) {
            $user_fd_setting->updated_datetime = date("Y-m-d H:i:s");
            $user_fd_setting->save(true, ['updated_datetime']);
        } else {
            $modelCustomersSetting = CustomersSetting::model();
            $tokenData = array(
                'customers_id' => $cid,
                'customers_setting_key' => self::FD_EMAIL_VERIFICATION,
                'customers_setting_value' => '1',
                'created_datetime' => date("Y-m-d H:i:s"),
                'updated_datetime' => date("Y-m-d H:i:s")
            );
            $modelCustomersSetting->saveCustomerSetting($tokenData);
        }

        // Send Email for SignUpEmail Verification
        $emailExist = CustomersInfoVerification::model()->searchRecord($cid, $new_email_address, CustomersInfoVerification::$type1);
        $serialNumber = ApiGeneralCom::createRandomValue(12);
        if (empty($emailExist)) {
            $infoVerificationArray = array(
                'customers_id' => $cid,
                'customers_info_value' => $new_email_address,
                'serial_number' => $serialNumber,
                'verify_try_turns' => 0,
                'info_verified' => 0,
                'info_verification_type' => CustomersInfoVerification::$type1,
                'customers_info_verification_mode' => 'A',
            );

            CustomersInfoVerification::model()->saveCustomerInfoVerification($infoVerificationArray);
        } else {
            //fetch the customer_verification for serial number
            $customer_info_verification = CustomersInfoVerification::find()->where(
                [
                    "customers_id" => $cid,
                    "customers_info_value" => $new_email_address,
                    "info_verification_type" => CustomersInfoVerification::$type1
                ]
            )->one();

            $customer_info_verification->serial_number = $serialNumber;
            $customer_info_verification->info_verified = 0;
            $customer_info_verification->customers_info_verification_date = 0;
            $customer_info_verification->save(true, ['serial_number', 'info_verified', 'customers_info_verification_date']);
        }

        $action = "verify";
        $verification_url = Url::base(true) . Url::toRoute([
                '/verify-email',
                'action' => $action,
                'serialNumber' => $serialNumber,
                'email' => $new_email_address,

            ]);

        $mail_obj = new EmailComponent();
        $mail_obj->sendMail(
            $new_email_address,
            $new_email_address,
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'VERIFY_SIGNUP_EMAIL_SUBJECT'),
            Yii::t('profile', 'VERIFY_SIGNUP_EMAIL_BODY', [
                'FIRSTNAME' => $new_email_address,
                'SIGNUP_EMAIL' => $new_email_address,
                'EMAIL_SIGNUP_URL' => $verification_url,
                'EMAIL_SIGNATURE' => Yii::t("dev", "EMAIL_SIGNATURE")
            ]),
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
        );
    }

    public abstract function createTicketForMobileChangeApproval($user_id, $new_country, $wipe_credits);

    protected function _createTicketForMobileChangeApproval($user_id, $new_country, $wipe_credits, $cf, $group_id, $ticket_type, $tags)
    {
        $subject = sprintf('[OV][OG] - Buyer ID %s Request to change mobile country', $user_id);
        $description = sprintf('This user (#%s) requests to change the mobile country to %s. Proof of address(POA) document is submitted to verify against %s country.', $user_id, $new_country, $new_country);
        if ($wipe_credits) {
            $description .= "\n\nPlease be reminded that any unused G2G Point, G2G Store Credit or Seller Credit will be written off when request is approved.";
        }
        $this->createTicket($subject, $description, $group_id, $ticket_type, $cf, [
            'tags' => $tags,
            'customers_id' => $user_id,
        ]);
    }

    public abstract function createTicketForEmailVerificationCompleted($customers_id);

    protected function _createTicketForEmailVerificationCompleted($customers_id, $cf, $group_id, $ticket_type, $tags)
    {
        $subject = sprintf('[OV][OG] - Buyer ID %s has just verified the email address.', $customers_id);
        $description = 'Thank you for your verification, your account is currently verified and the process may take up to 24 hours.<br>
        <br>
        Thank you for your patience.';

        $this->createTicket($subject, $description, $group_id, $ticket_type, $cf, [
            'tags' => $tags,
            'customers_id' => $customers_id,
        ]);
    }

    public abstract function createTicketForKYCCompleted($customers_id);

    protected function _createTicketForKYCCompleted($customers_id, $cf, $group_id, $ticket_type, $tags)
    {
        $subject = sprintf('[EKYC][OG] - Buyer ID %s has just submitted eKYC documents.', $customers_id);
        $description = 'Thank you for the submission. Your documents are currently being verified and the process may take up to 24 hours<br>
        <br>
        Thank you for your patience.';
        //See if can add note?
        $this->createTicket($subject, $description, $group_id, $ticket_type, $cf, [
            'tags' => $tags,
            'customers_id' => $customers_id,
        ]);
    }

    protected function createTicket($subject, $description, $group_id, $ticket_type, $cf, $configs = [], $retry = 0)
    {
        $tags = isset($configs['tags']) ? $configs['tags'] : [];
        $status = isset($configs['status']) ? $configs['status'] : 3;
        $priority = isset($configs['priority']) ? $configs['priority'] : 3;
        $cid = isset($configs['customers_id']) ? $configs['customers_id'] : null;
        $non_required_field = isset($configs['non_required_field']) ? $configs['non_required_field'] : [];

        $custom_fields = [];
        foreach ($cf as $key => $val) {
            $custom_fields[$key] = (string)$val;
        }
        $params = [
            'description' => $description,
            'unique_external_id' => ($cid ? static::generateUniqueExternalCid($cid) : 'Shasso system'),
            'subject' => \Yii::$app->params['FRESHDESK_SUBJECT_PREFIX'] . $subject,
            'status' => $status,
            'priority' => $priority,
            'source' => 2,
            'group_id' => $group_id,
        ];
        if (!empty($cid)) {
            $m_customer = Customers::findOne(['customers_id' => $cid]);
            $m_country_dialing_code = Countries::findOne(['countries_international_dialing_code' => $m_customer->customers_country_dialing_code_id]);
            $country_dialing_code = isset($m_country_dialing_code) ? $m_country_dialing_code->countries_international_dialing_code : '';
            $params['name'] = $m_customer->customers_firstname . ' ' . $m_customer->customers_lastname;
            $params['email'] = $m_customer->customers_email_address;
            $params['phone'] = $country_dialing_code . $m_customer->customers_telephone;
        }
        if (!empty($tags)) {
            $params['tags'] = $tags;
        }
        if (!empty($custom_fields)) {
            $params['custom_fields'] = $custom_fields;
        }
        $params['type'] = 'AFT related';
        $full_set_params = array_merge($params, $non_required_field);
        $api_response = $this->callPostApi('tickets', $full_set_params);
        $info = isset($api_response['info']) ? $api_response['info'] : [];
        $response = isset($api_response['response']) ? $api_response['response'] : [];
        $exception = isset($api_response['exception']) ? $api_response['exception'] : null;

        if (isset($info['http_code']) && $info['http_code'] == 201) {
            return json_decode($response, true);
        } elseif ($retry < $this->api_retries) {
            $retry++;
            return $this->createTicket($subject, $description, $group_id, $ticket_type, $cf, $configs, $retry);
        } else {
            if (!empty($exception)) {
                $content = $exception;
            } else {
                $content = [
                    'info' => $info,
                    'response' => $response,
                    'params' => $full_set_params,
                ];
            }
            Yii::$app->reporter->reportToAdminViaSlack('Freskdesk create ticket API failed for customer ' . $cid, $content);
        }
    }

    public static function generateUniqueExternalCid($cid)
    {
        return \Yii::$app->params['FRESHDESK_EXTERNAL_ID_PREFIX'] . $cid;
    }

}