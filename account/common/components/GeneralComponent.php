<?php

namespace common\components;

use common\models\CustomersInfoVerification;
use common\models\UserFlags;
use Yii;
use yii\helpers\HtmlPurifier;

class GeneralComponent
{

    public static function getIPAddress()
    {
        if ($ip = self::getAkamaiIp()) {
            return $ip;
        }

        $ip = ArrayHelper::getValue($_SERVER, 'HTTP_X_FORWARDED_FOR');

        if (!$ip) {
            return ArrayHelper::getValue($_SERVER, 'REMOTE_ADDR');
        }

        $ip_array = explode(',', $ip);

        foreach ($ip_array as $ip) {
            $ip = trim($ip);

            if (self::validateIp($ip)) {
                return $ip;
            }
        }

        return null;
    }

    private static function validateIp($ip)
    {
        if (!$ip) {
            return false;
        }

        $validator = new ReserveIpValidator();

        if (!$validator->validate($ip)) {
            return false;
        }

        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE | FILTER_FLAG_NO_PRIV_RANGE) === false) {
            return false;
        }

        return true;
    }

    private static function getAkamaiIp()
    {
        $ip = ArrayHelper::getValue($_SERVER, 'HTTP_TRUE_CLIENT_IP');

        if (!$ip) {
            return null;
        }

        return self::validateIp($ip) ? $ip : null;
    }

    public static function generateRandom($min = null, $max = null)
    {
        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                return $min;
            } else {
                return mt_rand($min, $max);
            }
        } else {
            return mt_rand();
        }
    }

    public static function convertLinefeeds($from, $to, $string)
    {
        if ((PHP_VERSION < "4.0.5") && is_array($from)) {
            return preg_replace('/(' . implode('|', $from) . ')/', $to, $string);
        } else {
            return str_replace($from, $to, $string);
        }
    }

    public static function notNull($value)
    {
        if (is_array($value)) {
            if (sizeof($value) > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            if (($value != '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
                return true;
            } else {
                return false;
            }
        }
    }

    public static function parseEmailString($emailString)
    {
        $emailArray = !empty($emailString) ? explode(',', $emailString) : array();
        $emailPattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
        $emailReceivers = array();

        for ($receiverCnt = 0; $receiverCnt < count($emailArray); $receiverCnt++) {
            if (preg_match($emailPattern, $emailArray[$receiverCnt], $regs)) {
                $receiverName = trim($regs[1]);
                $receiverEmail = trim($regs[2]);

                $emailReceivers[] = array('name' => $receiverName, 'email' => $receiverEmail);
            }
        }

        return $emailReceivers;
    }

    public static function removeInvalidChars($text)
    {
        $regex = '/( [\x00-\x7F] | [\xC0-\xDF][\x80-\xBF] | [\xE0-\xEF][\x80-\xBF]{2} | [\xF0-\xF7][\x80-\xBF]{3} ) | ./x';
        return preg_replace($regex, '$1', $text);
    }

    public static function arrayDiff($array1, $array2)
    {
        $result = array();
        foreach ($array1 as $key => $val) {
            if ((isset($array2[$key]) && ($array1[$key] != $array2[$key])) || !isset($array2[$key])) {
                $result[$key] = $val;
            }
        }
        return $result;
    }

    public static function parseURLGetParam($params)
    {
        $result = array();

        # data from `Site` Index View $_POST['params'] ( `p` parameter in URL )
        if (!empty($params)) {
            if (is_array($params)) {
                foreach ($params as $_key => $_val) {
                    if (is_array($_val)) {
                        foreach ($_val as $_key1 => $_val1) {
                            $result[$_key][$_key1] = HtmlPurifier::process($_val1);
                        }
                    } else {
                        $result[$_key] = HtmlPurifier::process($_val);
                    }
                }
            } else {
                $_tmp = explode('&', $params);
                foreach ($_tmp as $_tmp_val) {
                    $_var = explode('=', $_tmp_val);
                    if (count($_var) == 2) {
                        $result[$_var[0]] = HtmlPurifier::process($_var[1]);
                    }
                }
            }
        }

        return $result;
    }

    public static function encrypt($string, $key)
    {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result .= $char;
        }

        return base64_encode($result);
    }

    public static function decrypt($string, $key)
    {
        $result = '';
        $string = base64_decode($string);

        if ($key != '') {
            for ($i = 0; $i < strlen($string); $i++) {
                $char = substr($string, $i, 1);
                $keychar = substr($key, ($i % strlen($key)) - 1, 1);
                $char = chr(ord($char) - ord($keychar));
                $result .= $char;
            }
        }

        return $result;
    }

    public static function cpnRand($min = null, $max = null)
    {
        static $seeded;

        if (!isset($seeded)) {
            mt_srand((double)microtime() * 1000000);
            $seeded = true;
        }

        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                return $min;
            } else {
                return mt_rand($min, $max);
            }
        } else {
            return mt_rand();
        }
    }

    public static function setRandomEmailSerial($email, $cid = '')
    {
        $cid = (empty($cid) && isset(Yii::$app->user->id) ? Yii::$app->user->id : $cid);
        $serialNumber = '';

        if (!empty($cid)) {
            $again = true;
            while ($again) {
                $time = strtotime('now');
                $randomKey = rand(1, 99999);
                $value = md5($email . $time . $randomKey);
                $serialNumber = substr($value, 0, 12);

                $m_civ1 = CustomersInfoVerification::findOne(['serial_number' => $serialNumber]);
                if (!count($m_civ1)) {
                    $again = false;

                    $m_civ2 = CustomersInfoVerification::findOne(['customers_id' => $cid, 'info_verification_type' => 'email', 'customers_info_value' => $email]);
                    if (count($m_civ2)) {
                        $m_civ2->serial_number = $serialNumber;
                        $m_civ2->save();
                    } else {
                        $m_attr = array(
                            'customers_id' => $cid,
                            'customers_info_value' => $email,
                            'serial_number' => $serialNumber,
                            'info_verified' => '0',
                            'info_verification_type' => 'email'
                        );
                        CustomersInfoVerification::model()->saveNewRecord($m_attr);
                        unset($m_attr);
                    }
                }
            }
        }

        return $serialNumber;
    }

    public static function dateShort($rawDate)
    {
        if (($rawDate == '0000-00-00 00:00:00') || empty($rawDate)) {
            return false;
        }

        $year = substr($rawDate, 0, 4);
        $month = (int)substr($rawDate, 5, 2);
        $day = (int)substr($rawDate, 8, 2);
        $hour = (int)substr($rawDate, 11, 2);
        $minute = (int)substr($rawDate, 14, 2);
        $second = (int)substr($rawDate, 17, 2);

        if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
            return date(Yii::t('myStoreOrders', 'DATE_FORMAT'), mktime($hour, $minute, $second, $month, $day, $year));
        } else {
            return preg_replace('/2037$/', $year, date(Yii::t('myStoreOrders', 'DATE_FORMAT'), mktime($hour, $minute, $second, $month, $day, 2037)));
        }
    }

    public static function getRequestURI()
    {
        $result = "";

        if (isset($_SERVER['REQUEST_URI'])) {
            $url = parse_url($_SERVER['REQUEST_URI']);
            if (isset($url['query'])) {
                $result = $url['query'];
            }
        }

        return $result;
    }

    // mbstring module related
    public static function mbConvertEncoding($str, $toEncoding, $fromEncoding)
    {
        $doEncoding = true;
        if (extension_loaded('mbstring')) {
            $toEncoding = strtoupper($toEncoding);
            $fromEncoding = strtoupper($fromEncoding);

            if (!empty($fromEncoding)) {
                if (mb_detect_encoding($str) != $fromEncoding) {
                    $doEncoding = false;
                }
            } else {
                $fromEncoding = mb_detect_encoding($str);
            }
            if ($doEncoding) {
                $str = mb_convert_encoding($str, $toEncoding, $fromEncoding);
            }
        }

        return $str;
    }

    public static function getUserFlags()
    {
        $modelUserFlags = new UserFlags();
        $userFlagsArray = array();
        $userFlags = $modelUserFlags->getUserFlags();
        foreach ($userFlags as $flagInfo) {
            $userFlagsArray[$flagInfo->user_flags_id] = $flagInfo;
        }

        return $userFlagsArray;
    }

    // Encrypt Data
    public static function encryptData($theData)
    {
        $length = strlen($theData);
        $length = sprintf("%020d", $length);

        $theData = mcrypt_encrypt(Yii::$app->params['SECURE_CIPHER'], Yii::$app->params['SECURE_KEY'], $theData, MCRYPT_MODE_CBC, Yii::$app->params['SECURE_KEY_IV']);
        $theData = base64_encode($theData);
        $theData = $length . $theData;
        return ($theData);
    }

    // Decrypt Data
    public static function decryptData($theData)
    {
        if ($theData) {
            $length = substr($theData, 0, 20);
            $theData = substr($theData, 20);
            $length = intval($length);

            $theData = base64_decode($theData);
            $theData = mcrypt_decrypt(Yii::$app->params['SECURE_CIPHER'], Yii::$app->params['SECURE_KEY'], $theData, MCRYPT_MODE_CBC, Yii::$app->params['SECURE_KEY_IV']);
            $theData = substr($theData, 0, $length);
            $theData = base64_decode($theData);
        }
        return $theData;
    }

    public static function createRandomValue($length, $type = 'mixed')
    {
        if (($type != 'mixed') && ($type != 'chars') && ($type != 'digits')) {
            return false;
        }

        $rand_value = '';
        while (strlen($rand_value) < $length) {
            if ($type == 'digits') {
                $char = self::cpnRand(0, 9);
            } else {
                $char = chr(self::cpnRand(0, 255));
            }

            if ($type == 'mixed') {
                if (preg_match('/^[a-z0-9]$/i', $char)) {
                    $rand_value .= $char;
                }
            } else {
                if ($type == 'chars') {
                    if (preg_match('/^[a-z]$/i', $char)) {
                        $rand_value .= $char;
                    }
                } else {
                    if ($type == 'digits') {
                        if (preg_match('/^[0-9]$/', $char)) {
                            $rand_value .= $char;
                        }
                    }
                }
            }
        }

        return $rand_value;
    }

    public static function purify($data)
    {
        if (is_array($data)) {
            $data = array_map([get_called_class(), 'purify'], $data);
        } else {
            $data = strip_tags(HtmlPurifier::process($data));
        }

        return $data;
    }

    public static function createEmailMask($email)
    {
        $em = explode("@", $email);
        $name = implode(array_slice($em, 0, count($em) - 1), '@');
        $emailmasked = substr($name, 0, 2) . str_repeat('*', 5) . "@" . end($em);

        return $emailmasked;
    }

    public static function generateRandomString($length = 32)
    {
        $bytes = \Yii::$app->security->generateRandomKey($length);

        return strtr(substr(base64_encode($bytes), 0, $length), '+/', '01');
    }

    public static function parseUserAgent($ua)
    {
        $os = 'Unknown';
        $browser = 'Unknown';
        $parse_result = \UAParser\Parser::create()->parse($ua);
        if (!empty($parse_result->os->family)) {
            $os = $parse_result->os->family;
        }
        if (!empty($parse_result->ua->family)) {
            $browser = $parse_result->ua->family;
        }
        return [
            'os' => $os,
            'browser' => $browser,
        ];
    }

    public static function checkGoNativeVersionSupport($ua, $target)
    {
        $version = self::parseGoNativeVersion($ua);
        $target_version = explode('.', $target);
        $current_version = explode('.', $version);
        if (!empty($version) && isset($current_version[2]) && isset($target_version[2])) {
            if($current_version[0] > $target_version[0]){
                return true;
            }
            elseif($current_version[0] == $target_version[0] && $current_version[1] > $target_version[1]){
                return true;
            }
            elseif($current_version[0] == $target_version[0] && $current_version[1] == $target_version[1] && $current_version[2] >= $target_version[2]){
                return true;
            }
        }
        return false;
    }

    public static function parseGoNativeVersion($ua)
    {
        $matches = array();
        if (strpos($ua, 'gonative') !== false) {
            $pattern = "/(?<=GonativeUA\/)[0-9\.]+/i";
            if(preg_match($pattern, $ua, $matches)){
                return $matches[0];
            }
        }
        return false;
    }
}
