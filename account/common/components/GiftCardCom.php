<?php

namespace common\components;

use yii\base\Component;
use Yii;

class GiftCardCom {

    public static function product() {
        $result = array();

        if (isset(Yii::$app->params["GIFT_CARD"]) && isset(Yii::$app->params["GIFT_CARD"]["PRODUCT"])) {
            $result = Yii::$app->params["GIFT_CARD"]["PRODUCT"];
        }

        return $result;
    }

    /**
     * @param string $key
     * @return \common\components\gift_cards\AbstractGiftCard
     */
    public static function initProduct($key) {
        if (isset(Yii::$app->params["GIFT_CARD"]["PRODUCT"][$key]) && isset(Yii::$app->params["GIFT_CARD"]["PRODUCT"][$key])) {
            return \Yii::createObject(array_merge(['code' => $key], Yii::$app->params["GIFT_CARD"]["PRODUCT"][$key]));
        }
        return null;
    }
}