<?php

namespace common\components;

use common\models\Countries;
use common\models\Customers;
use common\models\CustomersGroups;
use common\models\CustomersRemarksHistory;
use common\models\LogTable;
use common\models\OrdersLogTable;
use Yii;
use yii\db\Expression;

class LogFilesComponent {

    var $identity;
    var $table = 'log_table';

    public function __construct($identity) {
        $this->identity = $identity;
    }

    function insertLog($prodId, $fieldName, $fromVal = '', $toVal = '', $adminMsg = '', $userMsg = '') {
        $modelLogTable = new LogTable();

        $logIp = GeneralComponent::getIpAddress();

        $logTableInfoArray = array('log_admin_id' => $this->identity, 'log_ip' => $logIp, 'log_time' => 'NOW()', 'log_products_id' => $prodId,
            'log_system_messages' => $adminMsg, 'log_user_messages' => $userMsg, 'log_field_name' => $fieldName,
            'log_from_value' => $fromVal, 'log_to_value' => $toVal);
        $modelLogTable->saveLogInfo($logTableInfoArray);
    }

    function insertOrdersLog($orderId, $ordersLogSystemMsg = '', $ordersLogFilename = '') {
        $ordersLogAdminId = $this->identity;

        if (is_numeric($this->identity)) {
            $cust_data = Customers::findOne(['customers_id' => $ordersLogAdminId]);
            if (isset($cust_data)) {
                $ordersLogAdminId = $cust_data->customers_email_address;
            }
        }

        $logTableArr = array(
            'orders_log_admin_id' => $ordersLogAdminId,
            'orders_log_ip' => GeneralComponent::getIPAddress(),
            'orders_log_time' => new Expression('NOW()'),
            'orders_log_orders_id' => $orderId,
            'orders_log_system_messages' => $ordersLogSystemMsg,
            'orders_log_filename' => $ordersLogFilename
        );
        OrdersLogTable::model()->insertLogTable($logTableArr);
    }

    function detectChanges($oldData, $newData) {
        $changesArray = array();
        if (count($oldData) && is_array($oldData) && count($newData) && is_array($newData)) {
            foreach ($oldData as $key => $value) {
                if (strcmp($newData[$key], $value) !== 0) {
                    $changesArray[$key] = array('from' => $value, 'to' => $newData[$key]);
                }
            }
        }
        return $changesArray;
    }

    function constructLogMessage($changesArray) {
        $messageStr = array();
        if (count($changesArray)) {
            foreach ($changesArray as $key => $changes) {
                $readableArray = $this->getReadableLogInput($key, $changes['from'], $changes['to']);
                if (count($readableArray)) {
                    $messageStr[] = $readableArray;
                }
            }
        }

        return $messageStr;
    }

    function getReadableLogInput($fieldName, $oldVal, $newVal) {
        $modelCustomersGroups = new CustomersGroups();
        $modelCountries = new Countries();
        $plainResult = false;

        $result = array();
        $oldVal = trim($oldVal);
        $newVal = trim($newVal);
        switch ($fieldName) {
            case 'customers_flag':
                $oldString = $newString = '';
                $userFlagsArray = GeneralComponent::getUserFlags();

                $changesArray = array();
                $oldArray = !empty($oldVal) ? explode(',', $oldVal) : array();
                $newArray = !empty($newVal) ? explode(',', $newVal) : array();

                $flaggedArray = arrayDiff($newArray, $oldArray);
                $unflaggedArray = arrayDiff($oldArray, $newArray);

                if (count($flaggedArray)) { // From Off->On
                    foreach ($flaggedArray as $flagId) {
                        $flagLabel = $userFlagsArray[$flagId]['user_flags_name'] . (strpos($userFlagsArray[$flagId]['user_flags_name'], str_replace(':', '', Yii::t('myStoreOrders', 'ENTRY_CUSTOMERS_FLAG'))) !== FALSE ? ':' : ' ' . Yii::t('myStoreOrders', 'ENTRY_CUSTOMERS_FLAG'));

                        $changesArray[$flagId] = '<span class="' . $userFlagsArray[$flagId]['user_flags_css_style'] . '">' . $flagLabel .
                                '</span> Off --> On' . (isset($userFlagsArray[$flagId]['user_flags_description']) ? ' (<span class="redIndicator">' .
                                        $userFlagsArray[$flagId]['user_flags_description'] . '</span>)' : '');
                    }
                }

                if (count($unflaggedArray)) { // From On->Off
                    foreach ($unflaggedArray as $flagId) {
                        $flagLabel = $userFlagsArray[$flagId]['user_flags_name'] . (strpos($userFlagsArray[$flagId]['user_flags_name'], str_replace(':', '', Yii::t('myStoreOrders', 'ENTRY_CUSTOMERS_FLAG'))) !== FALSE ? ':' : ' ' . Yii::t('myStoreOrders', 'ENTRY_CUSTOMERS_FLAG'));
                        $changesArray[$flagId] = '<span class="' . $userFlagsArray[$flagId]['user_flags_css_style'] . '">' . $flagLabel . '</span> On --> Off';
                    }
                }

                ksort($changesArray);
                reset($changesArray);

                $text = implode("\n", $changesArray);
                $plainResult = true;
                break;
            case 'customers_groups_id':
                if ((int) $oldVal > 0) {
                    $customersGroupsName = $modelCustomersGroups->getCustomersGroupsName($oldVal);
                    if (!empty($customersGroupsName)) {
                        $oldString = $customersGroupsName;
                    } else {
                        $oldString = "Customer group not found!";
                    }
                }

                if ((int) $newVal > 0) {
                    $customersGroupsName = $modelCustomersGroups->getCustomersGroupsName($newVal);
                    if (!empty($customersGroupsName)) {
                        $newString = $customersGroupsName;
                    } else {
                        $newString = "Customer group not found!";
                    }
                }

                $text = 'Group';
                break;
            case 'customers_gender':
                if (!empty($oldVal)) {
                    $oldString = ($oldVal == 'm') ? 'Male' : 'Female';
                } else {
                    $oldString = 'EMPTY';
                }

                $newString = ($newVal == 'm') ? 'Male' : 'Female';
                $text = 'Gender';
                break;
            case 'customers_dob':
                if (!empty($oldVal)) {
                    $oldString = GeneralComponent::dateShort($oldVal);
                } else {
                    $oldString = 'EMPTY';
                }

                $newString = GeneralComponent::dateShort($newVal);
                $text = 'Date of Birth';
                break;
            case 'customers_phone_verified':
                $oldString = ((int) $oldVal == '1') ? 'Verify' : 'Unverify';
                $newString = ((int) $newVal == '1') ? 'Verify' : 'Unverify';
                $text = 'Phone Verification';
                break;
            case 'entry_country_id':
                $country = $modelCountries->getCountryName($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = $modelCountries->getCountryName($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Country';
                break;
            case 'entry_zone_id':
                return;
                break;
            case 'customers_country_dialing_code_id':
                $country = $modelCountries->getCountryName($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = $modelCountries->getCountryName($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Location';
                break;
            case 'custom_products_code_viewed':
                $oldString = ((int) $oldVal == '1') ? 'Viewed' : 'Not Viewed';
                $newString = ((int) $newVal == '1') ? 'Viewed' : 'Not Viewed';
                $text = 'CD Key View Status';
                break;
            case 'custom_products_code_status_id':
                $codeStatusArray = array('-1' => 'Disabled',
                    '-2' => 'On Hold',
                    '0' => 'Sold',
                    '1' => 'Actual');
                $oldString = $codeStatusArray[(int) $oldVal];
                $newString = $codeStatusArray[(int) $newVal];
                $text = 'CD Key Status';
                break;
            default:
                $displayLabel = array(
                    'customers_firstname' => 'First Name', 
                    'customers_lastname' => 'Last Name', 
                    'customers_email_address' => 'E-Mail Address',
                    'customers_telephone' => 'Telephone Number', 
                    'customers_fax' => 'Fax Number', 
                    'customers_discount' => 'Customer Discount Rate',
                    'customers_newsletter' => 'Newsletter',
                    'customers_phone_verified' => 'Phone Verification', 
                    'customers_phone_verified_datetime' => 'Phone Verification Date',
                    'entry_street_address' => 'Street Address', 
                    'entry_postcode' => 'Post Code', 
                    'entry_city' => 'City', 
                    'entry_company' => 'Company',
                    'entry_suburb' => 'Suburb', 
                    'entry_state' => 'State', 
                    'char_wow_account' => 'WOW Account Name', 
                    'char_account_name' => 'Account Name', 
                    'char_account_pwd' => 'Account Password', 
                    'char_name' => 'Character Name',
                    'delivery_mode' => 'Delivery Mode'
                );
                $oldString = (trim($oldVal) != '') ? $oldVal : "EMPTY";
                $newString = (trim($newVal) != '') ? $newVal : "EMPTY";
                $text = isset($displayLabel[$fieldName]) ? $displayLabel[$fieldName] : $fieldName;
                break;
        }
        $result[$fieldName] = array('text' => $text, 'from' => $oldString, 'to' => $newString, 'plain_result' => ($plainResult ? '1' : '0'));
        return $result;
    }

    function insertCustomerHistoryLog($mail, $remark) {
        $m_attr = array(
            "customers_id" => $this->identity,
            "date_remarks_added" => new Expression('NOW()'),
            "remarks" => $remark,
            "remarks_added_by" => $mail
        );
        CustomersRemarksHistory::model()->saveNewRecord($m_attr);
        unset($m_attr);
    }

    function contructChangesString($customerChangesArray, $allCustomersInfoChangesMade) {
        if (gettype($customerChangesArray) == "array" && sizeof($customerChangesArray) > 0) {
            foreach ($customerChangesArray as $field => $res) {
                if (!empty($allCustomersInfoChangesMade)) {
                    if (!preg_match('/(##)?(' . $field . ')(##)?/', $allCustomersInfoChangesMade)) {
                        $allCustomersInfoChangesMade .= "##" . $field;
                    }
                } else {
                    $allCustomersInfoChangesMade .= $field;
                }
            }
        }

        while (strstr($allCustomersInfoChangesMade, '####')) {
            $allCustomersInfoChangesMade = str_replace('####', '##', $allCustomersInfoChangesMade);
        }

        if (preg_match('/^##/', $allCustomersInfoChangesMade)) {
            $allCustomersInfoChangesMade = substr($allCustomersInfoChangesMade, 2);
        }

        if (preg_match('/##$/', $allCustomersInfoChangesMade)) {
            $allCustomersInfoChangesMade = substr($allCustomersInfoChangesMade, 0, -2);
        }

        return $allCustomersInfoChangesMade;
    }

}
