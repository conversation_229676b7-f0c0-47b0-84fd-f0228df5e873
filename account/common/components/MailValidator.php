<?php

namespace common\components;

use Yii;
use yii\base\Component;
use yii\helpers\Json;
use common\models\LogSesBounce;
use common\models\EmailValidation;
use common\components\CurlComponent;

class MailValidator extends Component
{
    public $profile = 'pipwave';
    public $error_msg = '';

    public function checkEmail($email)
    {
        if (!empty($email)) {
            $purify_email = LogSesBounce::emailPurify($email);
            if ((new LogSesBounce)->emailExists($purify_email) === false) {
                $result = $this->validateEmail($purify_email);
                if ($result == 1 || $result == -1) {
                    return true;
                }
            }
        }
        return false;
    }

    public function validateEmail($email)
    {
        // bypass punycode encoding
        $result = $this->validateExistingRecord($email);
        if ($result == 1) {
            return true;
        } elseif ($result == -1) {
            switch ($this->profile) {
                case 'maillayer':
                    $this->mailLayerValidation($email);
                    break;

                case 'pipwave':
                    $service_id = Yii::$app->name;
                    if (isset($_GET['service'])) {
                        $service_id = \common\components\GeneralComponent::purify($_GET['service']);
                    }
                    $pipwave_api = new PipwaveApiCom($service_id);
                    $response = $pipwave_api->verifyEmail($email);
                    if(!empty($response['error_message'])){
                        $this->error_msg = $response['error_message'];
                    }
                    break;
            }
            return $this->validateExistingRecord($email);
        }
        return false;
    }

    private function validateExistingRecord($email)
    {
        $model = EmailValidation::find()->where(['email' => $email])->one();
        if ($model) {
            if ($model->mx_found == 1) {
                return 1;
            }
            return 0;
        }
        return -1;
    }

    private function mailLayerValidation($email)
    {
        $access_key = (Yii::$app->params['mailboxlayer.key'] ? Yii::$app->params['mailboxlayer.key'] : '');
        if (empty($access_key)) {
            return;
        }
        $url = 'https://apilayer.net/api/check';
        $params = array(
            'access_key' => $access_key,
            'email' => $email,
            'smtp' => 1,
            'format' => 1
        );

        try {
            $curl_obj = new CurlComponent();
            $result = $curl_obj->curlGet($url, $params);
            $data = Json::decode($result);

            if (isset($data['email']) && isset($data['mx_found']) && isset($data['smtp_check']) && isset($data['score'])) {
                $this->saveValidationRecord([
                    'email' => $email,
                    'profile' => 'mailboxlayer',
                    'mx_found' => ($data['mx_found'] == true ? 1 : 0),
                    'smtp_check' => ($data['smtp_check'] == true ? 1 : 0),
                    'score' => ($data['score'] * 100 ? $data['score'] * 100 : 0),
                    'raw' => Json::encode($data),
                ]);
            } else {
                // DO NOTHING
            }
        } catch (\Exception $e) {
            // DO NOTHING
        }
    }

    private function saveValidationRecord($data)
    {
        $model = new EmailValidation();
        $model->load($data, '');
        $model->trigger($model::EVENT_BEFORE_INSERT);
        $model->insertOrUpdate($model->getAttributes());
    }

}
