<?php

namespace common\components;

use dpodium\yii2\geoip\components\CGeoIP;
use Yii;

class MaxmindGeoIP extends \yii\base\Component
{

    public $countryDbPath = '';

    public function countryCodeByIP($ip = null)
    {
        $cache = Yii::$app->cache;

        if (!$ip) {
            $ip = GeneralComponent::getIPAddress();
        }

        $countryCode = $cache->get('ip_country_' . $ip);

        if($countryCode) {
            return $countryCode;
        }

        $module = new CGeoIP([
            'countryDbPath' => $this->countryDbPath
        ]);

        $module->support_ipv6 = true;
        $result = $module->lookupCountryCode($ip);

        if (empty($result) && $ex = $module->getPreviousException()) {
            Yii::$app->reporter->reportToAdminViaSlack('GeoIP IP query error - ' . $ip, $ex);
            $result = false;
        }

        $result =  (empty($result) ? 'US' : $result);

        $cache->set('ip_country_' . $ip, $result);

        return $result;
    }
}