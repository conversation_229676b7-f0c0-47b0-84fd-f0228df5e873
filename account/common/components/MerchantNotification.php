<?php

namespace common\components;

use common\models\Orders;
use Yii;

class MerchantNotification
{

    public static function notifyOnCustomerEmailVerifiedSuccessfully($customers_id, $email)
    {
        $curl_obj = new CurlComponent();
        $curl_obj->connect_via_proxy = false;
        $c_data = ['customer_id' => $customers_id, 'email' => $email];
        $curl_obj->curlPost(Yii::$app->params['OG_URL'] . '/checkout/API/PaymentEmailVerification', $c_data);

        //New method - use Freshdesk Ticket, split to different merchants
        FreshdeskApiCom::get(FreshdeskApiCom::CONFIG_TAG_TICKET_OG)->createTicketForEmailVerificationCompleted($customers_id);
    }

    public static function notifyOnCustomerFileUploaded($customers_id)
    {
        FreshdeskApiCom::get(FreshdeskApiCom::CONFIG_TAG_TICKET_OG)->createTicketForKYCCompleted($customers_id);
    }

}
