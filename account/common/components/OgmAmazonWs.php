<?php

namespace common\components;

use common\models\Configuration;
use common\models\LogSesBounce;
use Exception;
use Yii;
use yii\helpers\FileHelper;

class OgmAmazonWs {

    public $aws_bucket, $aws_bucket_key;
    public $aws_filepath;
    public $aws_filename;
    public $aws_file, $aws_file_content, $aws_file_content_type;

    /**
     * @var AWSS3
     */
    public $s3;

    /**
     * @var AWSSes
     */
    public $ses;
    public static $aws_s3_enabled = 'false'; // Needed to transfer to AWS, true: yes
    public static $aws_ses_enabled = 'false'; // Needed to transfer to AWS, true: yes
    private $ses_send_quota_array = array();
    private $letter_to, $letter_from, $letter_source, $letter_destination = null, $letter_option = null, $letter_compiled_message = null, $letter_replyto = null;

    public function __construct($config = null) {
        if (!empty($config)) {
            // use external config setting
        } else {
            $config = array(
                'aws_s3_enabled' => Configuration::model()->getConfigValue('AWS_S3_ENABLED'),
                'aws_ses_enabled' => Configuration::model()->getConfigValue('AWS_SES_ENABLED'),
            );
        }

        $this->set_aws_api_config($config);
        $this->ses = Yii::$app->aws->getSes();
    }

    private function load_memcache() {
        return Yii::$app->cache;
    }

    private function set_aws_api_config($config) {
        if (!empty($config)) {
            // One shot initialize
            if ($config['aws_s3_enabled']) {
                self::$aws_s3_enabled = $config['aws_s3_enabled'];
            }

            if ($config['aws_ses_enabled']) {
                self::$aws_ses_enabled = $config['aws_ses_enabled'];
            }
        }
    }

    public function set_bucket_key($bucket_key) {
        if (!empty($bucket_key)) {
            $this->aws_bucket_key = $bucket_key;
            if (!empty(Yii::$app->aws->s3[$bucket_key])) {
                $this->s3 = Yii::$app->aws->getS3($bucket_key);
                $this->aws_bucket = $this->s3->getBucketKey();
            } else {
                $this->s3 = null;
                $this->aws_bucket = null;
            }
        }
    }

    public function set_file($file) {
        $this->aws_file = $file;
    }

    public function set_file_content($file_content) {
        $this->aws_file_content = $file_content;
    }

    /*
     * 	contentType - _string_ (Optional)
     */

    public function set_file_content_type($file_content_type = null) {
        if (!empty($file_content_type)) {
            $this->aws_file_content_type = $file_content_type;
        } else if (!empty($this->aws_filename)) {
            $mime_type = FileHelper::getMimeTypeByExtension($this->aws_filename);
            $this->aws_file_content_type = $mime_type;
        }
    }

    public function set_filename($filename) {
        if (!is_null($filename)) {
            $this->aws_filename = $filename;
        }
    }

    public function set_filepath($filepath) {
        if (!empty($filepath)) {
            $this->aws_filepath = $filepath;
        }
    }

    private function get_aws_bucket($bucket_key = null) {
        $this->set_bucket_key($bucket_key);
        return $this->aws_bucket;
    }

    private function get_aws_filename($filename = null, $filepath = null) {
        $this->set_filename($filename);
        $this->set_filepath($filepath);
        return $this->aws_filepath . $this->aws_filename;
    }

    public static function is_aws_s3_enabled() {
        return strtolower(self::$aws_s3_enabled) == 'true';
    }

    public function is_s3_bucket_config_enabled() {
        return !empty($this->s3);
    }

    private function get_opts_array() {
        $return_array = array();

        if (isset($this->aws_file['tmp_name'])) {
            $return_array['fileUpload'] = $this->aws_file['tmp_name'];
        } else if (!empty($this->aws_file_content)) {
            $return_array['body'] = $this->aws_file_content;
        }

        if (!empty($this->aws_file_content_type)) {
            $return_array['ContentType'] = $this->aws_file_content_type;
        }

        return $return_array;
    }

    public function is_image_exists($filename = null, $filepath = null, $bucket = null, $cache = true) {
        $memcache_obj = $this->load_memcache();
        $return_status = false;

        if (self::is_aws_s3_enabled()) {
            $bucket = $this->get_aws_bucket($bucket);
            $file_name = $this->get_aws_filename($filename, $filepath);

            if (!empty($file_name) && isset($bucket)) {
                $cache_key = $bucket . '/' . $file_name . '/boolean';
                $cache_result = $memcache_obj->get($cache_key);

                if ($cache_result !== FALSE) {
                    $return_status = $cache_result;
                } else {
                    try {
                        if ($this->s3->doesContentExist($file_name) === true) {
                            $return_status = true;
                        }
                    } catch (Exception $e) {
                        $this->report_error("Method [is_image_exists] : " . $e->getMessage());
                    }

                    if ($cache) {
                        $memcache_obj->set($cache_key, $return_status, 2592000); // Cache for 30 days
                    }
                }

                unset($cache_key, $cache_result);
            }

            unset($bucket, $file_name);
        }

        return $return_status;
    }

    public function get_file($filename = null, $filepath = null, $bucket = null, $cache = false) { // Get Object using API
        $memcache_obj = $this->load_memcache();
        $return_string = '';

        if (self::is_aws_s3_enabled()) {
            $bucket = $this->get_aws_bucket($bucket);
            $file_name = $this->get_aws_filename($filename, $filepath);

            if (!empty($file_name) && isset($bucket)) {
                $cache_key = $bucket . '/' . $file_name;
                $cache_result = $memcache_obj->get($cache_key);

                if ($cache_result !== FALSE) {
                    ;
                } else {
                    try {
                        $return_string = $this->s3->getContent($file_name);
                    } catch (Exception $e) {
                        $this->report_error("Method [get_file][" . $this->aws_bucket_key . "] : " . $e->getMessage());
                    }

                    if ($cache) {
                        $memcache_obj->set($cache_key, $return_string, $cache);
                    }
                }

                unset($cache_key, $cache_result);
            }

            unset($bucket, $file_name);
        }

        return $return_string;
    }

    public function save_file() { // Move the file to Amazon S3 by API
        $return_status = false;

        if (self::is_aws_s3_enabled() && $this->is_s3_bucket_config_enabled()) {
            $this->set_file_content_type();

            $bucket = $this->get_aws_bucket();
            $file_name = $this->get_aws_filename();
            $opt_array = $this->get_opts_array();

            if (!empty($file_name) && isset($bucket)) {
                try {
                    if (isset($opt_array['fileUpload'])) {
                        $file = $opt_array['fileUpload'];
                        unset($opt_array['fileUpload'], $opt_array['body']);
                        $result = $this->s3->saveFile($file_name, $file, false, null, $opt_array);
                    } else if (isset($opt_array['body'])) {
                        $content = $opt_array['body'];
                        unset($opt_array['fileUpload'], $opt_array['body']);
                        $result = $this->s3->saveContent($file_name, $content, false, null, $opt_array);
                    } else {
                        throw new Exception('Unrecognized save_file call');
                    }

                    if (!$result) {
                        $this->report_error("Method [save_file][" . $this->aws_bucket_key . "][" . $return_status . "]");
                        $return_status = false;
                    } else {
                        $this->clear_memcache();
                        $return_status = true;
                    }

                    unset($result);
                } catch (Exception $e) {
                    $this->report_error("Method [save_file] : " . $e->getMessage());
                }
            }

            unset($bucket, $file_name, $opt_array);
        }

        return $return_status;
    }

    private function clear_memcache($filename = null, $filepath = null, $bucket = null) {
        $memcache_obj = $this->load_memcache();

        $bucket = $this->get_aws_bucket($bucket);
        $file_name = $this->get_aws_filename($filename, $filepath);

        $memcache_obj->delete($bucket . '/' . $file_name, 0);
        $memcache_obj->delete($bucket . '/' . $file_name . '/boolean', 0);

        unset($bucket, $file_name);
    }

// ------------------------------------------------------------------------------------------------------------------------------------------------- SES
    /*
     * $letter['envelope']['to'] : (Required)
     * $letter['envelope']['to']['name'] : (Required)
     * $letter['envelope']['from'] : (Required)
     * $letter['envelope']['from']['name'] : (Required)
     * $letter['envelope']['cc'] : (Optional)
     * $letter['envelope']['bcc'] : (Optional)
     * $letter['envelope']['replyto'] : (Optional)
     * $letter['message']['subject'] : (Required)
     * $letter['message']['body'] : (Required)
     */
    public function set_ses_letter_info($letter) {
        $emailLinefeed = Configuration::model()->getConfigValue('EMAIL_LINEFEED');
        if (!empty($letter)) {
            if ($emailLinefeed == 'CRLF') {
                $lf = "\r\n";
            } else {
                $lf = "\n";
            }
            $this->letter_from = $letter['envelope']['from']['address'];
            $this->letter_to = $letter['envelope']['to']['address'];
            $this->letter_source = (!empty($letter['envelope']['from']['name']) ? '"' . $letter['envelope']['from']['name'] . '" <' . $this->letter_from . '>' : $this->letter_from);

            $this->letter_destination = array(
                'ToAddresses' => array($this->letter_to)
            );

            if (isset($letter['envelope']['cc']) && !empty($letter['envelope']['cc'])) {
                $this->letter_destination['CcAddresses'] = array($letter['envelope']['cc']);
            }

            if (isset($letter['envelope']['bcc']) && !empty($letter['envelope']['bcc'])) {
                $this->letter_destination['BccAddresses'] = array($letter['envelope']['bcc']);
            }

            if (isset($letter['envelope']['replyto'])) {
                $this->set_ses_reply_to_email($letter['envelope']['replyto']);
            } else {
                $this->set_ses_reply_to_email($this->letter_from);
            }

            $this->letter_option = array('ReplyToAddresses' => $this->letter_replyto);

            $this->letter_compiled_message = array();
            $this->letter_compiled_message['Subject']['Data'] = $letter['message']['subject'];
            if (isset($letter['message']['body']['content_html'])) {
                $this->letter_compiled_message['Body']['Html']['Data'] = $letter['message']['body']['content_html'];
                $this->letter_compiled_message['Body']['Text']['Data'] = GeneralComponent::convertLinefeeds(array("\r\n", "\n", "\r"), '<br>', $letter['message']['body']['content_text']);
            } else {
                $this->letter_compiled_message['Body']['Html']['Data'] = GeneralComponent::convertLinefeeds(array("\r\n", "\n", "\r"), '<br>', $letter['message']['body']);
            }
        }
    }

    public function set_ses_reply_to_email($replyto_address) {
        if (!empty($replyto_address)) {
            $this->letter_replyto[] = $replyto_address;
        }
    }

    public function set_ses_status_locked() {
        $memcache_obj = $this->load_memcache();
        $mm_status = $this->send_mail_mc_locked_status();

        if ($mm_status['status'] != 'LOCKED') {
            $get_remaining_time = 3600; //(int)$mm_status['expired_ts'] - time();

            $mc_array = array('expired_ts' => $mm_status['expired_ts'], 'duration' => $get_remaining_time, 'status' => 'LOCKED');
            $memcache_obj->set($mm_status['key'], $mc_array, $get_remaining_time);
            $this->report_error("Status [LOCKED][DURATION] : " . $get_remaining_time);
            unset($mc_array);
        } else {
            // Memcache has no record or has been locked.
        }

        unset($mm_status);
    }

    public function get_ses_send_quota() {
        $return_array = array();

        if (!empty($this->ses_send_quota_array)) {
            $return_array = $this->ses_send_quota_array;
        } else {
            $api_return = array();

            try {
                $api_return = $this->ses->getInstance()->getSendQuota();
                $api_return = (array) $api_return->body;
            } catch (Exception $e) {
                $this->report_error("Method [get_ses_send_quota] : " . $e->getMessage());
            }

            if (!empty($api_return)) {
                $return_array['max'] = (int) $api_return->get('Max24HourSend');
                $return_array['sent'] = (int) $api_return->get('SentLast24Hours');
                $return_array['rate'] = (int) $api_return->get('MaxSendRate'); // How many email sent per second
                $this->ses_send_quota_array = $return_array;
            }

            unset($api_return);
        }

        return $return_array;
    }

    /*
     * 	OUTPUT
     * return 0  : Checked SES and Found Sent records but cannot get last 24 first attempt timestamp
     * return -1 : Checked SES and Found NO send records
     * return >0 : Checked SES and Found Sent records and Got the last 24 first attempt timestamp
     */

    public function get_timestamp_for_first24_attempts() {
        $data_array = array();
        $return_int = 0;

        try {
            $statistics_obj = $this->ses->getInstance()->getSendStatistics();
            $data_array = (array) $statistics_obj->body->get('SendDataPoints');
        } catch (Exception $e) {
            $this->report_error("Method [get_timestamp_for_first24_attempts] : " . $e->getMessage());
        }

        if (!empty($data_array)) {
            $sent_quota = $this->get_ses_send_quota();

            if (isset($sent_quota['sent'])) { // When sent quota has value
                $ttl_sent = $sent_quota['sent'];

                if ($ttl_sent > 0) {
                    $stt_array = array();
                    $estimate_range = time() - 86400; // Estimate Passed 24 hours from now

                    foreach ($data_array as $counter => $rdata) {
                        $arr_key = strtotime($rdata->Timestamp);
                        if ($arr_key > $estimate_range) {
                            $stt_array[$arr_key]['da'] = (int) $rdata->DeliveryAttempts;
                        }
                    }

                    krsort($stt_array);

                    foreach ($stt_array as $ts => $stt) {
                        $ttl_sent -= $stt['da'];
                        if ($ttl_sent <= 0) {
                            $return_int = (int) $ts;
                            break;
                        }
                    }

                    unset($stt_array);
                } else {
                    $return_int = -1;
                }
            }
            unset($sent_quota);
        }

        unset($statistics_obj, $data_array);
        return $return_int;
    }

    public function get_expired_timestamp() {
        $return_int = 0;
        $last_attempts_ts = $this->get_timestamp_for_first24_attempts();

        switch ($last_attempts_ts) {
            case 0 :
                // Check records again after 1 hour
                $expired_timestamp = time() + 3600; // +1hour interval = 3600 seconds
                break;
            case -1 :
                $expired_timestamp = time() + 86400; // +24hours = 86400 seconds
                break;
            default :
                $expired_timestamp = $last_attempts_ts + 86400; // +24hours = 86400 seconds
                break;
        }

        if ($expired_timestamp > time()) {
            $return_int = (int) $expired_timestamp;
        }

        return $return_int;
    }

    public static function is_aws_ses_enabled() {
        return strtolower(self::$aws_ses_enabled) == 'true';
    }

    private function is_filtered_allow_subject() {
        $return_bool = true;
        $exclude_fromaddress_pattern_array = array('<EMAIL>');
        $exclude_toaddress_pattern_array = array('<EMAIL>');
        $exclude_toaddress_domain_pattern_array = array();
        $exclude_subject_pattern_array = array('[cronjob]', 'DEBUG E-MAIL', ' Before Process E-MAIL', 'Top-up Report', 'Top-up Notification', 'Direct Top-up',
            'Paid amount less than total need to pay',
            'Bibit Payment Method Match Failed',
            'SC search empty query',
            '[URGENT] checkout amount not tally with order products',
            'Turn off publisher due to data mismatched',
            'MayBank Before Process curl E-MAIL',
            'Cron update order status',
            'KuaiQian CallBack',
            'KuaiQian Process',
            'KuaiQian Before Process3',
            'MayBank CallBack E-MAIL',
            'Mobile Money DEBUG',
            'Missing private/public key',
            'Admin Account Updated',
            'New Admin Member',
            'Manual Stock Addition Notification',
            'Manual Stock Deduction Notification',
            'Discount Coupons Request Approved',
            'Discount Coupons Request Cancelled',
            'Inactive Admin Members',
            'Low Stock Warning:',
            'Cancellation of Redeemed Gift Voucher',
            'Receiving Payment Method Changes Notification',
            'Low Stock Warning:');

        if (!empty($this->letter_to)) {
            $return_bool = $this->check_not_bounce_address(trim($this->letter_to));
        } else {
            $return_bool = false; // Just in case ToAddress is missing
        }

        if ($return_bool) {
            if (!empty($this->letter_from)) {
                if (in_array(trim($this->letter_from), $exclude_fromaddress_pattern_array)) { // case-insensitive
                    $return_bool = false;
                }
            } else {
                $return_bool = false; // Just in case FromAddress is missing
            }
        }

        if ($return_bool) {
            if (in_array(trim($this->letter_to), $exclude_toaddress_pattern_array)) { // case-insensitive
                $return_bool = false;
            } else if (in_array(trim(substr(strrchr($this->letter_to, "@"), 1)), $exclude_toaddress_domain_pattern_array)) { // case-insensitive
                $return_bool = false;
            }
        }

        if ($return_bool && isset($this->letter_compiled_message['Subject']['Data'])) {
            foreach ($exclude_subject_pattern_array as $str) {
                if (stripos($this->letter_compiled_message['Subject']['Data'], $str) !== FALSE) { // case-insensitive
                    $return_bool = false;
                    break;
                }
            }
        }

        unset($exclude_toaddress_pattern_array, $exclude_fromaddress_pattern_array, $exclude_subject_pattern_array);

        return $return_bool;
    }

    public function send_mail_by_ses($letter = null) {
        $return_bool = false;

        if (self::is_aws_ses_enabled()) {
            $mm_status = $this->send_mail_mc_locked_status();

            if ($mm_status['status'] != 'LOCKED') { // Allow to send if status is ''
                $this->set_ses_letter_info($letter);

                try {
                    $mail_params = [
                        'Source' => $this->letter_source,
                        'Destination' => $this->letter_destination,
                        'Message' => $this->letter_compiled_message,
                    ];
                    if (isset($this->letter_option['ReplyToAddresses'])) {
                        $mail_params['ReplyToAddresses'] = $this->letter_option['ReplyToAddresses'];
                    }
                    $return_obj = $this->ses->getInstance()->sendEmail($mail_params);
                    if (!isset($return_obj->get('@metadata')['statusCode']) || $return_obj->get('@metadata')['statusCode'] != 200) {
                        $this->report_error("STATUS[FAILED:UNKNOWN CODE] : " . http_build_query((array) $return_obj->get('@metadata')['statusCode'], null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . http_build_query((array) $this->letter_destination['ToAddresses']));
                    } else {
                        $return_bool = true;
                    }
                    unset($return_obj);
                } catch (\Aws\Exception\AwsException $e) {
                    if (strtolower($e->getAwsErrorCode()) == 'throttling') {
                        $this->set_ses_status_locked();
                        $this->report_error("STATUS[FAILED:THROTTLING] : " . http_build_query((array) $e->getAwsErrorMessage(), null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . http_build_query((array) $this->letter_destination['ToAddresses']));
                    } else {
                        $this->report_error("STATUS[FAILED:KNOWN CODE] : " . http_build_query((array) $e->getAwsErrorMessage(), null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . http_build_query((array) $this->letter_destination['ToAddresses']));
                    }
                } catch (Exception $e) {
                    $this->report_error("STATUS[FAILED] : " . $e->getMessage());
                }
            }

            unset($mm_status);
        }

        return $return_bool;
    }

    public function send_mail_by_ses_controller($letter = null) {
        $return_bool = false;
        
        if (self::is_aws_ses_enabled()) {
            $this->set_ses_letter_info($letter);

            if ($this->is_filtered_allow_subject()) {
                $mm_status = $this->send_mail_mc_locked_status();

                if (!empty($mm_status['status'])) {
                    if ($mm_status['status'] != 'LOCKED') { // Status return 'LOCKED'
                        $return_bool = true;
                    }
                } else {
                    $return_bool = true;
                }

                unset($mm_status);
            }
        }

        return $return_bool;
    }

    /*
     * 	OUTPUT
     * status : ''         : Memcache has no record
     * status : 'LOCKED'   : Memcache has recorded Locked when received ThrottlingException from SES response
     * status : 'UNLOCKED' : Memcache has recorded Unlocked when newly created.
     */

    private function send_mail_mc_locked_status() {
        $memcache_obj = $this->load_memcache();
        $return_array = array();
        $cache_key = 'ogm_amazon_ws/send_mail_controller/array';
        $cache_result = $memcache_obj->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $return_array['status'] = '';
        }

        $return_array['key'] = $cache_key;

        return $return_array;
    }

    public function verify_email_address($email_address) {
        try {
            $this->ses->getInstance()->verifyEmailAddress(['EmailAddress' => $email_address]);
            $this->report_error("Method [verify_email_address][DONE] : " . $email_address);
        } catch (Exception $e) {
            $this->report_error("Method [verify_email_address] : " . $e->getMessage());
        }
    }

    public function check_not_bounce_address($emailAddress) {
        $returnBool = true;
        $modelLogSesBounce = new LogSesBounce();

        $emailExists = $modelLogSesBounce->emailExists($emailAddress);
        if ($emailExists) {
            $returnBool = false;
        }

        return $returnBool;
    }

    private function report_error($message) {
        \Yii::$app->reporter->reportToAdminViaSlack('OgmAmazonWs error', $message);
    }

    public function __destruct() {
        unset($this->s3, $this->ses);
    }

}
