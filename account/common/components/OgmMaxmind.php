<?php

namespace common\components;

use common\models\Configuration;
use CreditCardFraudDetection;
use Yii;

require_once(Yii::getAlias('@common/extensions/anti_fraud/maxmind/classes/CreditCardFraudDetection.php'));
require_once(Yii::getAlias('@common/extensions/anti_fraud/maxmind/classes/TelephoneVerification.php'));

class OgmMaxmind extends PhoneTypeProvider {

    const NOT_CHECK_KEYWORD = 'Not Check';
    const OLD_VERSION_API = 'PHP/1.4';

    private static $phone_identification_supported_country_array = array('Canada', 'United States');
    private $proxy_mode = FALSE;
    private $maxmind_country_conversion, $api_url, $api_check_field, $countryCode;

    public function __construct() {
        $this->maxmind_country_conversion = array(
            'Russia' => 'Russian Federation',
            'Slovakia' => 'Slovakia (Slovak Republic)',
            'Viet Nam' => 'Vietnam',
            'South Korea' => 'Korea, Republic of',
            'Brunei' => 'Brunei Darussalam'
        );

        $this->maxmind_license_key = Configuration::model()->getConfigValue('MAXMIND_LICENSE_KEY');
        $this->proxy_mode = Yii::$app->params['USE_PROXY'];
    }

    private function setRequestUrl($url) {
        $this->api_url = $url;
    }

    private function setCheckField($field) {
        $this->api_check_field = $field;
    }

    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }

    public function getCountryCode() {
        return $this->countryCode;
    }

    private function prepareRequest($input_data, $version = 'latest') {
        $return_array = array();

        $ccfs = new CreditCardFraudDetection();

        switch ($version) {
            case self::OLD_VERSION_API:
                $check_field = $this->api_check_field;
                $api_url = $this->api_url;
                $allowed_field_array = array('l' => 1, 'phone' => 1);

                $input_data["l"] = $this->maxmind_license_key;
                break;
            default:
                $check_field = $ccfs->check_field;
                $api_url = $ccfs->url;
                $allowed_field_array = array();

                $input_data["license_key"] = $this->maxmind_license_key;
                break;
        }

        $ccfs->set_url($api_url);
        $ccfs->set_is_proxy($this->proxy_mode);
        $ccfs->set_allowed_fields($allowed_field_array);
        $ccfs->input($input_data);

//		$ccfs->isSecure = 0;                    // If you want to disable Secure HTTPS or don't have Curl and OpenSSL installed
        $ccfs->timeout = 15;                    // set the timeout to be five seconds
        $ccfs->query();

        // then we get the result from the server
        if ($h = $ccfs->output()) {
            // then finally we print out the result
            $outputkeys = array_keys($h);
            $numoutputkeys = count($h);

            for ($i = 0; $i < $numoutputkeys; $i++) {
                if ($key = $outputkeys[$i]) {
                    $return_array[$key] = $h[$key];
                }
            }
        }

        return $return_array;
    }

    public function telephoneTypeRequest($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $return_array = FALSE;
        $customers_telephone = preg_replace('/[^\d]/', '', trim($customers_telephone));

        $this->setCountryCode($customers_country_international_dialing_code);
        $this->setCheckField('refid');

        if (in_array(trim($customers_telephone_country), self::$phone_identification_supported_country_array)) {
            $this->setRequestUrl("app/phonetype_http");
            $h = array("phone" => $customers_telephone);
        } else {
            $this->setRequestUrl("app/phone_id_http");
            $h = array("phone" => '+' . $this->getCountryCode() . $customers_telephone);
        }

        if ($maxmind_telephone_info = $this->prepareRequest($h, self::OLD_VERSION_API)) {
            $return_array = array(
                'TYPEOFPHONE' => $maxmind_telephone_info["phoneType"],
                'CITY' => isset($maxmind_telephone_info["city"]) ? $maxmind_telephone_info["city"] : '',
                'STATE' => isset($maxmind_telephone_info["state"]) ? $maxmind_telephone_info["state"] : '',
                'ZIP' => isset($maxmind_telephone_info["zip"]) ? $maxmind_telephone_info["zip"] : '',
                'COUNTRYNAME' => isset($maxmind_telephone_info["countryname"]) ? $maxmind_telephone_info["countryname"] : '',
                'LATITUDE' => isset($maxmind_telephone_info["latitude"]) ? $maxmind_telephone_info["latitude"] : '',
                'LONGITUDE' => isset($maxmind_telephone_info["longitude"]) ? $maxmind_telephone_info["longitude"] : '',
                'error' => $maxmind_telephone_info["err"],
                'REFERENCEID' => $maxmind_telephone_info["refid"]
            );
        }

        return $return_array;
    }

}
