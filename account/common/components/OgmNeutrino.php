<?php

namespace common\components;

use common\components\CustomersInfoVerificationComponent;
use Exception;
use stdClass;
use Yii;

class OgmNeutrino extends PhoneTypeProvider {

    public $provider_name = 'neutrino';
    
    private $user_id;
    
    private $signature;
    
    private $url;
    
    private $curl_obj,
            $phone_type = array(
                'mobile' => 2,
                'fixed-line' => 1,
                'toll-free' => 4,
                'voip' => 5,
                'premium-rate' => 6,
                'hlr-mobile' => 3,
                'hlr-fixed-line' => 11,
                'hlr-toll-free' => 14,
                'hlr-voip' => 15,
                'hlr-premium-rate' => 16,
                'hlr-invalid-mobile' => 17,
                'hlr-missing' => 18,
                'hlr-unknown' => 19,
                'unknown' => 10
    );

    public function __construct() {
        $this->curl_obj = new CurlComponent();
        $this->user_id = Yii::$app->params['antifraud.neutrino.api.user_id'];
        $this->signature = Yii::$app->params['antifraud.neutrino.api.signature'];
        $this->url = Yii::$app->params['antifraud.neutrino.api.url'];
    }
    
    public function setUserId($user_id) {
        $this->user_id = $user_id;
    }

    public function setSignature($signature) {
        $this->signature = $signature;
    }

    public function setUrl($url) {
        $this->url = $url;
    }

    private function isJson($params) {
        return json_decode($params) instanceof stdClass;
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }

    public function getCountryCode() {
        return $this->countryCode;
    }

    public function getPhoneTypeId($phone_type) {
        return isset($this->phone_type[$phone_type]) ? $this->phone_type[$phone_type] : 10;
    }

    private function prepareRequest($action, $request_data, $total_try = 0) {
        $return_array = array();

        $request_url = $this->url . '/' . $action;

        $request_data['user-id'] = $this->user_id;
        $request_data['api-key'] = $this->signature;

        try {
            if ($total_try < 3) {
                $response = $this->curl_obj->curlGet($request_url, $request_data);
                if ($this->isJson($response)) {
                    $return_array = json_decode($response, TRUE);
                } else {
                    $curl_err = $this->curl_obj->getError();

                    if (isset($curl_err['error_code']) && in_array($curl_err['error_code'], array(35))) {
                        $this->reportError(array('request' => array('action' => $action, 'data' => $request_data['number']), 'response' => $response, 'curl_error' => $this->curl_obj->getError(), 'try' => $total_try), "Method [prepare_request]");
                        $return_array = $this->prepare_request($action, $request_data, ++$total_try);
                    } else {
                        throw new Exception('NO_RESPONSE');
                    }
                }
            }
        } catch (Exception $e) {
            unset($request_data['api-key']);
            $this->reportError(array('request' => array('action' => $action, 'data' => $request_data), 'response' => $response, 'curl_error' => $this->curl_obj->getError(), 'error' => $e->getMessage()), "Method [prepare_request]");
        }

        unset($request_data);

        return $return_array;
    }

    public function telephoneTypeRequest($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $phone_type = '';
        $err = '';
        $this->setCountryCode($customers_country_international_dialing_code);

        $civ_obj = new CustomersInfoVerificationComponent();
        $civ = $civ_obj->parseTelephone($customers_telephone, $this->getCountryCode());
        if (isset($civ->national_number)) {
            $phone = '+' . $this->getCountryCode() . $civ->national_number;
        } else {
            $phone = '+' . $this->getCountryCode() . $this->sanitizePhoneNumber($customers_telephone);
        }
        
        $hlr_lookup_array = array();
        $phone_validate_array = $this->queryPhoneValidate($phone);

        if ($phone_validate_array !== array()) {
            if (!isset($phone_validate_array['api-error-msg'])) {
                $phone_type = isset($phone_validate_array['type']) ? $phone_validate_array['type'] : '';

                if ($phone_type == 'unknown') {
                    $hlr_lookup_array = $this->queryHLRLookup($phone);

                    if ($hlr_lookup_array !== array()) {
                        if (!isset($hlr_lookup_array['api-error-msg'])) {
                            $phone_type = isset($hlr_lookup_array['number-type']) ? $hlr_lookup_array['number-type'] : 'hlr-missing';
                            $mcc = isset($hlr_lookup_array['mcc']) ? $hlr_lookup_array['mcc'] : '';
                            $mnc = isset($hlr_lookup_array['mnc']) ? $hlr_lookup_array['mnc'] : '';

                            if ($phone_type == 'mobile') {
                                if (($mcc == '') || ($mnc == '')) {
                                    $phone_type = 'invalid-mobile';
                                }
                            }

                            if ($phone_type != 'hlr-missing') {
                                $phone_type = 'hlr-' . $phone_type;
                            }
                        } else {
                            $err = 'queryPhoneValidate::' . $hlr_lookup_array['api-error-msg'];
                        }
                    } else {
                        $err = 'queryHLRLookup::NO_RESPONSE';
                    }
                }
            } else {
                $err = 'queryPhoneValidate::' . $phone_validate_array['api-error-msg'];
            }
        } else {
            $err = 'queryPhoneValidate::NO_RESPONSE';
        }

        return array(
            'response_pv' => $phone_validate_array,
            'response_hlr' => $hlr_lookup_array,
            'TYPEOFPHONE' => $this->getPhoneTypeId($phone_type),
            'CITY' => '',
            'STATE' => '',
            'ZIP' => '',
            'COUNTRYNAME' => isset($phone_validate_array["location"]) ? $phone_validate_array["location"] : '',
            'LATITUDE' => '',
            'LONGITUDE' => '',
            'REFERENCEID' => $phone,
            'error' => $err
        );
    }

    private function queryHLRLookup($phone_number) {
        $request_action = 'hlr-lookup';
        $request_array = array(
            'number' => $phone_number
        );

        return $this->prepareRequest($request_action, $request_array);
    }

    private function queryPhoneValidate($phone_number) {
        $request_action = 'phone-validate';
        $request_array = array(
            'number' => $phone_number
        );

        return $this->prepareRequest($request_action, $request_array);
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = 'Neutrino API Error - ' . date("F j, Y H:i");
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: B2C REPORTER <<EMAIL>>' . "\r\n";

        EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $message, $headers);
    }

}
