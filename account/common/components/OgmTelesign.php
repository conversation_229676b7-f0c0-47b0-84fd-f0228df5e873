<?php

namespace common\components;

require_once(\Yii::getAlias('@common/extensions/anti_fraud/telesign/classes/Telesign.php'));

use common\models\Configuration;
use Telesign;

class OgmTelesign extends PhoneTypeProvider {
    
    private $service;
    
    function __construct() {
        $this->service = new Telesign();
        $this->service->setCustomerID(Configuration::model()->getConfigValue('TELESIGN_CUSTOMER_ID'));
        $this->service->setAuthenticationID(Configuration::model()->getConfigValue('TELESIGN_AUTHENTICATION_ID'));
    }

    public function getCountryCode() {
        return $this->service->getCountryCode();
    }

    public function setCountryCode($code) {
        return $this->service->setCountryCode($code);
    }

    public function telephoneTypeRequest($country, $phoneNo) {
        $this->service->requestPhoneID($phoneNo);
        return $this->service->phoneinfo;
    }

}
