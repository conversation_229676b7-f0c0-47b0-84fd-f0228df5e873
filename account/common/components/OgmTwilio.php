<?php

namespace common\components;

use common\models\Configuration;
use common\components\CustomersInfoVerificationComponent;
use dpodium\yii2\Twilio\TwilioManager;
use Exception;
use Yii;

class OgmTwilio extends PhoneTypeProvider {

    public $provider_name = 'twilio';

    protected $number_type = array(
        'mobile' => 2,
        'voip' => 5,
        'landline' => 1,
        'unknown' => 10,
    );

    protected $sid;

    protected $token;

    public function __construct($config = null) {
        $this->sid = Configuration::model()->getConfigValue('ANTIFRAUD_TWILIO_SID');
        $this->token = Configuration::model()->getConfigValue('ANTIFRAUD_TWILIO_TOKEN');
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }

    public function getCountryCode() {
        return $this->countryCode;
    }

    public function telephoneTypeRequest($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $phone_type = $this->number_type['unknown'];
        $err = '';
        $this->setCountryCode($customers_country_international_dialing_code);

        $civ_obj = new CustomersInfoVerificationComponent();
        $civ = $civ_obj->parseTelephone($customers_telephone, $this->getCountryCode());
        if (isset($civ->national_number)) {
            $phone = '+' . $this->getCountryCode() . $civ->national_number;
        } else {
            $phone = '+' . $this->getCountryCode() . $this->sanitizePhoneNumber($customers_telephone);
        }

        try{
            $twilio = new TwilioManager();
            $twilio->config = [
                'sid' => $this->sid,
                'token' => $this->token,
            ];
            if (Yii::$app->params['USE_PROXY']) {
                $twilio->proxy = [
                    'host' => Yii::$app->params['PROXY_HOST'],
                    'port' => Yii::$app->params['PROXY_PORT'],
                ];
            }
            $result = $twilio->lookup($phone, null, 'carrier');
            $phone_type = $result['carrier']['type'];
            $phone_type = isset($this->number_type[$phone_type]) ? $this->number_type[$phone_type] : $this->number_type['unknown'];
        } catch (Exception $ex) {
            $this->reportError($ex->getMessage());
            $err = $ex->getMessage();
            \Yii::$app->reporter->reportToAdminViaSlack("OgmTwilio - telephoneTypeRequest", json_encode($err));
        }
        return array(
            'TYPEOFPHONE' => $phone_type,
            'CITY' => '',
            'STATE' => '',
            'ZIP' => '',
            'COUNTRYNAME' => '',
            'LATITUDE' => '',
            'LONGITUDE' => '',
            'REFERENCEID' => $phone,
            'error' => $err
        );
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = 'Twilio API Error - ' . date("F j, Y H:i");
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: B2C REPORTER <<EMAIL>>' . "\r\n";

        EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $response_data, $headers);
    }

}
?>