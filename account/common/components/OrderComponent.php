<?php
namespace common\components;

use Yii;
use yii\base\Component; 
use common\models\Orders;
use common\models\OrdersTotal;
use common\models\OrdersStatus;
use common\models\OrdersProducts;
use common\models\OrdersProductsExtraInfo;
use common\models\Customers;
use common\models\ProductsGroups;
use common\models\OrdersProductsAttributes;
use common\models\OrdersCustomProducts;
use common\models\CustomProductsCode;
use common\models\PaymentExtraInfo;
use common\models\C2cConfiguration;
use common\models\Configuration;
use common\models\PaymentMethods;
/*
 * $Id: OrderComponent.php,v 1.12.2.4 2015/11/05 10:32:35 darren.ng Exp $
 *
 * Developer: Ching Yen
 *
 */

class OrderComponent extends Component {
    const LL_FLAG = 'Flag LL';
    const PAYPAL_EMAIL_FLAG = 'Paypal Email';
    
    public $customer_id;
    public $currency;
    public $info, $totals, $products, $customer, $delivery, $billing, $payment;
    public $order_id;
    public $compensate_products;
    public $order_offline_payment_methods_type_array = array('7', '8', '9');
    public $order_tag_info = array();

    public function __construct($order_id = '', $is_sc = false) {
        $this->info = array();
        $this->totals = array();
        $this->products = array();
        $this->customer = array();
        $this->delivery = array();
        $this->billing = array();
        $this->compensate_products = array();

        if ($order_id != '') {
            $this->query($order_id);
            $this->order_id = $order_id;
        }
    }

    public function query($order_id) {
        $shipping_method = array();
        $order_total = array();

        $modelOrders = new Orders();
        $modelOrdersTotal = new OrdersTotal();
        $modelOrdersStatus = new OrdersStatus();
        $modelOrdersProducts = new OrdersProducts();
        $modelOrdersProductsExtraInfo = new OrdersProductsExtraInfo();
        $modelCustomers = new Customers();
        $modelProductsGroups = new ProductsGroups();
        $modelOrdersProductsAttributes = new OrdersProductsAttributes();
        $orderInfo = $modelOrders->getOrderInfo($order_id);

        if (isset($orderInfo)) {
            $ordersTotalInfo = $modelOrdersTotal->getOrderTotalInfo($order_id);

            foreach ($ordersTotalInfo as $otInfo) {
                $this->totals[] = [
                    'title' => $otInfo->title,
                    'text' => $otInfo->text,
                    'value' => $otInfo->value,
                    'class' => $otInfo->class,
                ];

                if ($otInfo->class == 'ot_total') {
                    $order_total = $otInfo;
                } else if ($otInfo->class == 'ot_shipping') {
                    $shipping_method = $otInfo;
                }
            }

            $orderStatus = $modelOrdersStatus->getOrderStatus($orderInfo->orders_status);

            $this->info = [
                'currency' => $orderInfo->currency,
                'currency_value' => $orderInfo->currency_value,
                'payment_method' => $orderInfo->payment_method,
                'payment_methods_id' => $orderInfo->payment_methods_id,
                'payment_methods_parent_id' => $orderInfo->payment_methods_parent_id,
                'cc_type' => $orderInfo->cc_type,
                'cc_owner' => $orderInfo->cc_owner,
                'cc_number' => $orderInfo->cc_number,
                'cc_expires' => $orderInfo->cc_expires,
                'date_purchased' => $orderInfo->date_purchased,
                'pm_2CO_cc_owner_firstname' => $orderInfo->pm_2CO_cc_owner_firstname,
                'pm_2CO_cc_owner_lastname' => $orderInfo->pm_2CO_cc_owner_lastname,

                //begin PayPal_Shopping_Cart_IPN
//                'orders_status_id' => $orderInfo->orders_status,
                'shipping_cost' => isset($shipping_method->value) ? $shipping_method->value : '',
                'total_value' => isset($order_total->value) ? $order_total->value : '',

                //end PayPal_Shopping_Cart_IPN
                'orders_status_id' => $orderInfo->orders_status,
                'orders_status' => isset($orderStatus) ? $orderStatus : '',
                'paypal_ipn_id' => $orderInfo->paypal_ipn_id,
                'remote_addr' => $orderInfo->remote_addr,
                'last_modified' => $orderInfo->last_modified,
                'total' => isset($order_total->text) ? strip_tags($order_total->text) : '',
                'shipping_method' => (isset($shipping_method->title) ? ((substr($shipping_method->title, -1) == ':') ? substr(strip_tags($shipping_method->title), 0, -1) : strip_tags($shipping_method->title)) : ''),
                'orders_aft_executed' => $orderInfo->orders_aft_executed,
                'orders_tag_ids' => $orderInfo->orders_tag_ids,
            ];

            $this->customer = [
                'id' => $orderInfo->customers_id,
                'name' => $orderInfo->customers_name,
                'company' => $orderInfo->customers_company,
                'street_address' => $orderInfo->customers_street_address,
                'suburb' => $orderInfo->customers_suburb,
                'city' => $orderInfo->customers_city,
                'postcode' => $orderInfo->customers_postcode,
                'state' => $orderInfo->customers_state,
                'country' => $orderInfo->customers_country,
                'telephone_country' => (!empty($orderInfo->customers_telephone_country) ? $orderInfo->customers_telephone_country : isset($orderInfo->countries['countries_name']) ? $orderInfo->countries['countries_name'] : null),
                'order_country_code' => (!empty($orderInfo->customers_country_international_dialing_code) ? $orderInfo->customers_country_international_dialing_code : isset($orderInfo->countries['countries_international_dialing_code']) ? $orderInfo->countries['countries_international_dialing_code'] : null),
                'format_id' => $orderInfo->customers_address_format_id,
                'telephone' => $orderInfo->customers_telephone,
                'email_address' => $orderInfo->customers_email_address,
                'customers_groups_id' => $orderInfo->customers_groups_id
            ];

            $this->delivery = [
                'name' => $orderInfo->delivery_name,
                'company' => $orderInfo->delivery_company,
                'street_address' => $orderInfo->delivery_street_address,
                'suburb' => $orderInfo->delivery_suburb,
                'city' => $orderInfo->delivery_city,
                'postcode' => $orderInfo->delivery_postcode,
                'state' => $orderInfo->delivery_state,
                'country' => $orderInfo->delivery_country,
                'format_id' => $orderInfo->delivery_address_format_id
            ];

            if (empty($this->delivery['name']) && empty($this->delivery['street_address'])) {
                $this->delivery = false;
            }

            $this->billing = [
                'name' => $orderInfo->billing_name,
                'company' => $orderInfo->billing_company,
                'street_address' => $orderInfo->billing_street_address,
                'suburb' => $orderInfo->billing_suburb,
                'city' => $orderInfo->billing_city,
                'postcode' => $orderInfo->billing_postcode,
                'state' => $orderInfo->billing_state,
                'country' => $orderInfo->billing_country,
                'format_id' => $orderInfo->billing_address_format_id
            ];

            // TODO: Split actual purchase and compensated
            $index = 0;
            $orderProductInfo = $modelOrdersProducts->getOrderProductDetails($order_id);

            foreach ($orderProductInfo as $opInfo) {
                $this->products[$index] = [
                    'qty' => $opInfo->products_quantity,
                    'delivered_qty' => $opInfo->products_delivered_quantity,
                    'id' => $opInfo->products_id,

                    //begin PayPal_Shopping_Cart_IPN
                    'orders_products_id' => $opInfo->orders_products_id,

                    //end PayPal_Shopping_Cart_IPN
                    'custom_products_type_id' => $opInfo->custom_products_type_id,
                    'name' => $opInfo->products_name,
                    'model' => $opInfo->products_model,
                    'tax' => $opInfo->products_tax,
                    'price' => $opInfo->products_price,
                    'final_price' => $opInfo->final_price,
                    'pre_order' => $opInfo->products_pre_order,
                    'products_categories_id' => $opInfo->products_categories_id,
                    'is_compensate' => $opInfo->orders_products_is_compensate
                ];

                if ($opInfo->custom_products_type_id == '2') {
                    $deliveryMode = $modelOrdersProductsExtraInfo->getDeliveryMode($opInfo->orders_products_id);

                    if ($deliveryMode != '') {
                        $this->products[$index]['custom_content']['delivery_mode'] = $deliveryMode;
                    }
                }

                if ($cdkey_info = $this->getCdkeyIdentifier($opInfo->products_id, $opInfo->orders_products_id)) {
                    $this->products[$index]['cdkey_info'] = $cdkey_info;
                }

                $subProduct = $modelOrdersProducts->getSubProductInfo($order_id, $opInfo->orders_products_id, $orderInfo->date_purchased);
                $sub_index = 0;

                foreach ($subProduct as $subProductInfo) {
                    $custom_content_array = array();
                    $deliveryMode = $modelOrdersProductsExtraInfo->getDeliveryMode($subProductInfo->orders_products_id);

                    if ($deliveryMode != '') {
                        $custom_content_array['delivery_mode'] = $deliveryMode;
                        $this->products[$index]['custom_content']['delivery_mode'] = $deliveryMode;
                    }

                    $this->products[$index]['package'][$sub_index] = array(
                        'qty' => $subProductInfo->products_quantity,
                        'id' => $subProductInfo->products_id,
                        'orders_products_id' => $subProductInfo->orders_products_id,
                        'name' => $subProductInfo->products_name,
                        'model' => $subProductInfo->products_model,
                        'tax' => $subProductInfo->products_tax,
                        'price' => $subProductInfo->products_price,
                        'final_price' => $subProductInfo->final_price,
                        'products_categories_id' => $subProductInfo->products_categories_id,
                        'delivered_qty' => $subProductInfo->products_delivered_quantity,
                        'purchase_eta' => $subProductInfo->purchase_eta,
                        'org_purchase_eta' => $subProductInfo->orders_products_purchase_eta,
                        'custom_content' => $custom_content_array,
                    );

                    $this->products[$index]['package'][$sub_index]['qty_info'] = array(
                        'delivered_quantity' => $subProductInfo->products_good_delivered_quantity,
                        'canceled_quantity' => $subProductInfo->products_canceled_quantity,
                        'reversed_quantity' => $subProductInfo->products_reversed_quantity
                    );

                    if ($cdkey_info = $this->getCdkeyIdentifier($subProductInfo->products_id, $subProductInfo->orders_products_id)) {
                        $this->products[$index]['package'][$sub_index]['cdkey_info'] = $cdkey_info;
                    }

                    $sub_index++;
                }

                $customerGroupId = $modelCustomers->getCustomerGroupId();

                if ($customerGroupId != '0') {
                    $ordersCustomersPrice = $modelProductsGroups->getCustomerGroupPrice($customerGroupId, $this->products[$index]['id']);

                    if ($ordersCustomersPrice != '') {

                        $this->products[$index] = array(
                            'price' => $ordersCustomersPrice,
                            'final_price' => $ordersCustomersPrice
                        );
                    }
                }

                // ********** End Separate Price per Customer Mod **************
                $subindex = 0;
                $productAttribute = $modelOrdersProductsAttributes->getProductAttributeInfo($order_id, $opInfo['orders_products_id']);

                foreach ($productAttribute as $attributes) {
                    $this->products[$index]['attributes'][$subindex] = array(//begin PayPal_Shopping_Cart_IPN
                        'option_id' => $attributes->products_options_id,
                        'value_id' => $attributes->products_options_values_id,

                        //end PayPal_Shopping_Cart_IPN
                        'option' => $attributes->products_options,
                        'value' => $attributes->products_options_values,
                        'prefix' => $attributes->price_prefix,
                        'price' => $attributes->options_values_price
                    );
                    $subindex++;
                }

                $this->info['tax_groups']["{$this->products[$index]['tax']}"] = '1';
                $index++;
            }
        }
    }
    
    public function getCdkeyIdentifier($productId, $ordersProductId) {
        $modelOrdersCustomProducts = new OrdersCustomProducts();
        $modelCustomProductsCode = new CustomProductsCode();

        $ids_arr = array();
        $cdkey_identifier_arr = array();
        $cdKeyInfo = $modelOrdersCustomProducts->getCdkeyInfo($productId, $ordersProductId);

        foreach ($cdKeyInfo as $cdKey) {
            $ids_arr = array_merge($ids_arr, explode(",", $cdKey->orders_custom_products_value));
        }

        if (count($ids_arr) > 0) {

            $productCode = $modelCustomProductsCode->getProductCodeInfo($ids_arr);

            foreach ($productCode as $codeInfo) {
                $cdkey_identifier_arr[$codeInfo->custom_products_code_id] = array(
                    'key_identifier' => $codeInfo->custom_products_code_id,
                    'file_name' => $productId . '_' . $codeInfo->custom_products_code_id,
                    'file_type' => !empty($codeInfo->file_type) ? $codeInfo->file_type : 'jpg'
                );
            }
        }

        return $cdkey_identifier_arr;
    }

    public function getPaymentMethodTypeId($payment_method_id = null) {
        $return_int = 0;
        $pm_id = $payment_method_id != null ? $payment_method_id : (isset($this->info['payment_methods_id']) ? $this->info['payment_methods_id'] : 0);
        
        if ($pm_id) {
            if ($result = PaymentMethods::model()->getPaymentMethodTypeId($pm_id)) {
                $return_int = $result;
            }
        }
        
        return $return_int;
    }

}
