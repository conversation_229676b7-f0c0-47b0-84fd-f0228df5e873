<?php

namespace common\components;

use Yii;

class Pagination {

    function paginate($totalList, $limit, $page, $paging, $controller) {
        $pagination = '';
        $totalPage = ceil($totalList / $limit);
        $firstPg = '';
        $lastPg = '';
        $prevPg = '';
        $nextPg = '';
        $goPg = '';
        $mid = ($paging + 1) / 2;
        $range = ($paging - 1) / 2;
        if ($totalList != '0') {
            if ($totalList > $limit) {

                if ($page != '1') {
                    $firstPg = '<div style="float:right;" class="main_btn gray_short_btn"><a href="javascript:viewPage(\'1\',\'' . $controller . '\')"><font>' . Yii::t('general', 'LINK_FIRST_PAGE') . '</font>
					<span></span></a></div>';
                    $prevPg = '<div style="float:right;" class="main_btn gray_short_btn"><a href="javascript:viewPage(\'' . ($page - 1) . '\',\'' . $controller . '\')"><font><<</font>
					<span></span></a></div>';
                }

                if ($totalPage <= $paging) {
                    for ($i = 1, $max = $totalPage + 1; $i < $max; $i++) {
                        if ($i != $page) {
                            $goPg = '<div class="main_btn gray_short_btn" style="float:right;"><a href="javascript:viewPage(\'' . $i . '\',\'' . $controller . '\')"><font>' . $i . '</font>
							<span></span></a></div>' . $goPg;
                        } else {
                            $goPg = '<div style="float:right"><span class="hds3 lfloat" style="display: inline-block;padding: 6px 10px;vertical-align: top;">' . $i . '</span></div>' . $goPg;
                        }
                    }
                } else {
                    if ($totalPage >= $page + ($paging - $range)) {

                        if (($page - $range) >= 1) {
                            for ($i = ($page - $range), $max = $page + $range + 1; $i < $max; $i++) {
                                if ($i != $page) {
                                    $goPg = '<div class="main_btn gray_short_btn" style="float:right;"><a href="javascript:viewPage(\'' . $i . '\',\'' . $controller . '\')"><font>' . $i . '</font>
									<span></span></a></div>' . $goPg;
                                } else {
                                    $goPg = '<div style="float:right"><span class="hds3 lfloat" style="display: inline-block;padding: 6px 10px;vertical-align: top;">' . $i . '</span></div>' . $goPg;
                                }
                            }
                        } else {
                            for ($i = 1, $max = $paging + 1; $i < $max; $i++) {
                                if ($i != $page) {
                                    $goPg = '<div class="main_btn gray_short_btn" style="float:right;"><a href="javascript:viewPage(\'' . $i . '\',\'' . $controller . '\')"><font>' . $i . '</font>
									<span></span></a></div>' . $goPg;
                                } else {
                                    $goPg = '<div style="float:right"><span class="hds3 lfloat" style="display: inline-block;padding: 6px 10px;vertical-align: top;">' . $i . '</span></div>' . $goPg;
                                }
                            }
                        }
                    } else {
                        for ($i = ($totalPage - ($paging - 1)), $max = $totalPage + 1; $i < $max; $i++) {
                            if ($i != $page) {
                                $goPg = '<div class="main_btn gray_short_btn" style="float:right;"><a href="javascript:viewPage(\'' . $i . '\',\'' . $controller . '\')"><font>' . $i . '</font>
								<span></span></a></div>' . $goPg;
                            } else {
                                $goPg = '<div style="float:right"><span class="hds3 lfloat" style="display: inline-block;padding: 6px 10px;vertical-align: top;">' . $i . '</span></div>' . $goPg;
                            }
                        }
                    }
                }
            }

            if ($page != $totalPage) {
                $lastPg = '<div style="float:right;" class="main_btn gray_short_btn"><a href="javascript:viewPage(\'' . $totalPage . '\',\'' . $controller . '\')"><font>' . Yii::t('general', 'LINK_LAST_PAGE') . '</font>
					<span></span></a></div>';
                $nextPg = '<div style="float:right;" class="main_btn gray_short_btn"><a href="javascript:viewPage(\'' . ($page + 1) . '\',\'' . $controller . '\')"><font>>></font>
					<span></span></a></div>';
            }

            $pagination = $lastPg . $nextPg . $goPg . $prevPg . $firstPg;
        }
        return $pagination;
    }

}