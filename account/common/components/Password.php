<?php

namespace common\components;

class Password {

    public static function validatePassword($plain, $encrypted) {
        if ((isset($plain) && $plain != '') && (isset($encrypted) && $encrypted != '')) {
            // split apart the hash / salt
            $stack = explode(':', $encrypted);

            if (sizeof($stack) != 2)
                return false;

            $plain = GeneralComponent::purify($plain);

            if (md5($stack[1] . $plain) == $stack[0]) {
                return true;
            }
        }

        return false;
    }

    // This function makes a new password from a plaintext password. 
    public static function encryptPassword($plain) {
        $password = '';

        for ($i = 0; $i < 10; $i++) {
            $password .= \common\components\GeneralComponent::cpnRand();
        }

        $salt = substr(md5($password), 0, 2);
        $password = md5($salt . $plain) . ':' . $salt;

        return $password;
    }

}