<?php
namespace common\components;

use common\models\PaymentMethods;

class PaymentModuleInfoComponent {

    public static function getSystemDefinedPaymentAccountSelection() {
        $_result = array();

        $c = (new \yii\db\Query())
                ->select(['t.payment_methods_id', 't.payment_methods_send_mode_name', 't.payment_methods_send_available_sites'])
                ->from('payment_methods t')
                ->join('LEFT JOIN', 'payment_methods_types',  'payment_methods_types.payment_methods_types_id = t.payment_methods_types_id')
                ->where('payment_methods_types.payment_methods_types_mode = "SEND"')
                ->andWhere('payment_methods_types.payment_methods_types_system_define = "1"')->all();
    
        if (!empty($c)) {
            foreach ($c as $_data) {
                if (strstr($_data['payment_methods_send_available_sites'], '0') !== false) {
                    $_result[$_data['payment_methods_id']] = $_data['payment_methods_send_mode_name'];
                }
            }
        }

        unset($c);
        return $_result;
    }

}
