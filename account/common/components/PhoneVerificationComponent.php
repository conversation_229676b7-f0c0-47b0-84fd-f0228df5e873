<?php

namespace common\components;

use common\models\Configuration;
use common\models\Countries;
use common\models\Customers;
use common\models\CustomersInfoVerification;
use common\models\CustomersSetting;
use common\models\MaxmindTelephoneIdentification;
use Exception;
use Yii;

class PhoneVerificationComponent {

    public $service_provider, $service, $service2;
    private $max_request = 3;

    public function __construct() {
        $this->service_provider = Configuration::model()->getConfigValue('TELEPHONE_VERIFICATION_SERVICES');
        if ($this->isTelesignEnabled()) {
            $this->service = new OgmTelesign();
        } else if ($this->isMaxmindEnabled()) {
            $this->service = new OgmMaxmind();
        } else if ($this->isNeutrinoEnabled()) {
            $this->service = new OgmNeutrino();
        } else if ($this->isTwilioEnabled()) {
            $this->service = new OgmTwilio();
        }
    }

    public function isTelesignEnabled() {
        return $this->service_provider == 'telesign'; //Configuration::model()->getConfigValue('TELEPHONE_VERIFICATION_SERVICES');
    }

    public function isMaxmindEnabled() {
        return ($this->service_provider == 'maxmind' && Configuration::model()->getConfigValue('MAXMIND_PHONE_VERIFICATION_ENABLED'));
    }

    public function isNeutrinoEnabled() {
        return ($this->service_provider == 'neutrino');
    }

    public function isTwilioEnabled() {
        return ($this->service_provider == 'twilio');
    }

    public function setCountryCode($countryCode) {
        $this->service->setCountryCode($countryCode);
    }

    private function verificationLimitByCustomer($customerId) {
        $modelCustomersSetting = new CustomersSetting();
        $returnInt = FALSE;

        if ($customerId) {
            $phoneVerifyAttempt = $modelCustomersSetting->getPhoneVerifyAttempt($customerId);

            if (isset($phoneVerifyAttempt->customers_setting_value) && $phoneVerifyAttempt->customers_setting_value != '') {
                if ($phoneVerifyAttempt->request_active == '1') {
                    $returnInt = $phoneVerifyAttempt->customers_setting_value;

                    if ($returnInt >= $this->max_request) {
                        $returnInt = FALSE;
                    } else {
                        // Update counter + 1
                        $returnInt = $modelCustomersSetting->updatePhoneVerifyAttempt($customerId);
                    }
                } else {
                    // reset counter = 1
                    $returnInt = $modelCustomersSetting->updatePhoneVerifyAttempt($customerId, $reset = TRUE);
                }
            } else {
                $returnInt = 1;

                $phoneVerifyData = array(
                    'customers_id' => $customerId,
                    'customers_setting_key' => 'phone_verification_attempt',
                    'customers_setting_value' => $returnInt,
                    'created_datetime' => date('Y-m-d H:i:s'),
                    'updated_datetime' => date('Y-m-d H:i:s')
                );

                if (!$modelCustomersSetting->saveCustomerSetting($phoneVerifyData)) {
                    $returnInt = FALSE;
                }
            }
        }

        return $returnInt !== FALSE ? $returnInt <= $this->max_request : $returnInt;
    }

    public static function verifyNumberDialCode($ctry_id, $phone, $customer_id = null) {
        $result = array(
            "status" => false,
            "result" => "",
            "error" => array(
                "dial" => "",
                "phone" => ""
            )
        );
        
        $customer_id = $customer_id ? $customer_id : Yii::$app->user->id;

        if (($phone != "") && ($ctry_id != "")) {
            $m_ctry = Countries::findOne(['countries_id' => $ctry_id]);
            if (isset($m_ctry->countries_id)) {
                $cust_info_obj = new CustomersInfoVerificationComponent();
                $_phone_number = $cust_info_obj->parseTelephone($phone, $ctry_id);
                if (!empty($_phone_number)) {
                    $phone_obj = new PhoneVerificationComponent();
                    if ($phone_obj->isMobileNumExist($_phone_number, $ctry_id, $customer_id, '')) {
                        $result["error"]["phone"] = Yii::t("smsToken", "TEXT_MOBILE_IS_IN_USED_NOMAIL");
                    } else {
                        $_phone_min_length = Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH');
                        if (strlen($_phone_number) < $_phone_min_length) {
                            $result["error"]["phone"] = Yii::t("profile", "MSG_ENTRY_CONTACT_NUMBER_ERROR", array("SYS_MIN_LENGTH" => $_phone_min_length));
                        } else {
                            $result["status"] = true;
                            $result["result"] = $_phone_number;
                        }
                    }
                } else {
                    $result["error"]["phone"] = Yii::t("profile", "ERROR_EMPTY_MOBILE_NUMBER");
                }
            } else {
                $result["error"]["dial"] = Yii::t("profile", "ERROR_EMPTY_DIAL_CODE");
            }
        } else if (empty($ctry_id) || ($phone == "")) {
            if ($phone == "") {
                $result["error"]["phone"] = Yii::t("profile", "ERROR_EMPTY_MOBILE_NUMBER");
            } else {
                $result["error"]["dial"] = Yii::t("profile", "ERROR_EMPTY_DIAL_CODE");
            }
        } else {
            $result["error"]["phone"] = Yii::t("profile", "ERROR_EMPTY_MOBILE_NUMBER");
        }

        return $result;
    }

    private function getPhoneInfo($country, $countryCode, $phoneNo) {
        $return_array = FALSE;

        $this->setCountryCode($countryCode);
        $return_array = $this->service->telephoneTypeRequest($country, $phoneNo);

        return $return_array;
    }

    public function getCountryCode() {
        return $this->service->getCountryCode();
    }

    public function requestPhoneID($customerId, $country, $countryCode, $phoneNo) {
        $requestStatus = FALSE;

        if ($this->verificationLimitByCustomer($customerId) !== FALSE) {
            $phoneInfo = $this->getPhoneInfo($country, $countryCode, $phoneNo);

            if ($phoneInfo !== FALSE) {
                $modelMaxmindTelephoneIdentification = new MaxmindTelephoneIdentification();

                $telesignPhoneData = array(
                    'customers_id' => $customerId,
                    'customers_country_international_dialing_code' => $this->getCountryCode(),
                    'customers_telephone' => $phoneNo,
                    'maxmind_telephone_identification_id' => isset($phoneInfo["REFERENCEID"]) ? $phoneInfo["REFERENCEID"] : '',
                    'requested_date' => date("Y-m-d H:i:s"),
                    'provider' => $this->service_provider
                );

                if (isset($phoneInfo['APIERROR^CODE']) && $phoneInfo['APIERROR^CODE'] != '0') {
                    $telesignPhoneData['error'] = $phoneInfo["APIERROR^CODE"] . '-' . $phoneInfo["APIERROR^MESSAGE"];
                    $this->reportError("Method [" . $this->service_provider . ": requestPhoneID]<br><br>Error Code: " . $phoneInfo['APIERROR^CODE'] . "<br>Error Message: " . $phoneInfo['APIERROR^MESSAGE'] . "<br>Customer ID: " . $customerId);
                } else if (isset($phoneInfo['error']) && !empty($phoneInfo['error'])) {
                    $telesignPhoneData['error'] = $phoneInfo['error'];
                    $this->reportError("Method [" . $this->service_provider . ": requestPhoneID]<br><br>Error : " . $phoneInfo['error'] . "<br>Customer ID: " . $customerId);
                } else {
                    $requestStatus = TRUE;
                    $telesignPhoneData['customers_telephone_type'] = isset($phoneInfo["TYPEOFPHONE"]) ? (int) $phoneInfo["TYPEOFPHONE"] : 0;
                    $telesignPhoneData['city'] = isset($phoneInfo["CITY"]) ? ($phoneInfo["CITY"] ? $phoneInfo["CITY"] : 'NULL') : 'NULL';
                    $telesignPhoneData['state'] = isset($phoneInfo["STATE"]) ? ($phoneInfo["STATE"] ? $phoneInfo["STATE"] : 'NULL') : 'NULL';
                    $telesignPhoneData['postcode'] = isset($phoneInfo["ZIP"]) ? ($phoneInfo["ZIP"] ? $phoneInfo["ZIP"] : 'NULL') : 'NULL';
                    $telesignPhoneData['countries_name'] = isset($phoneInfo["COUNTRYNAME"]) ? ($phoneInfo["COUNTRYNAME"] ? $phoneInfo["COUNTRYNAME"] : 'NULL') : 'NULL';
                    $telesignPhoneData['latitude'] = isset($phoneInfo["LATITUDE"]) ? ($phoneInfo["LATITUDE"] ? (float) $phoneInfo["LATITUDE"] : 'NULL') : 'NULL';
                    $telesignPhoneData['longitude'] = isset($phoneInfo["LONGITUDE"]) ? ($phoneInfo["LONGITUDE"] ? (float) $phoneInfo["LONGITUDE"] : 'NULL') : 'NULL';
                }

                if ($modelMaxmindTelephoneIdentification->customerPhoneExists($customerId, $this->getCountryCode(), $phoneNo)) {
                    $modelMaxmindTelephoneIdentification->updateMaxmindTelephoneIdentification($telesignPhoneData, $customerId, $this->getCountryCode(), $phoneNo);
                } else {
                    $modelMaxmindTelephoneIdentification->saveMaxmindTelephoneIdentification($telesignPhoneData);
                }

                unset($telesignPhoneData);
            }
        }

        return $requestStatus;
    }

    public function getPhoneType($customerId, $country, $customerCountryCode, $customerTelephone) {
        $returnInt = null;

        $modelMaxmindTelephoneIdentification = new MaxmindTelephoneIdentification();
        $phoneProviderInfo = $modelMaxmindTelephoneIdentification->getPhoneProviderInfo($customerId, $customerCountryCode, $customerTelephone);

        if (isset($phoneProviderInfo)) {
            if ($phoneProviderInfo->provider == 'telesign') {
                $returnInt = $phoneProviderInfo->customers_telephone_type - 300; // Telesign code start with 301 and its 301 = maxmind's 1
            } else {
                $returnInt = $phoneProviderInfo->customers_telephone_type;
            }
        } else {
            if ($this->requestPhoneID($customerId, $country, $customerCountryCode, $customerTelephone)) {
                $phoneProviderInfo = $modelMaxmindTelephoneIdentification->getPhoneProviderInfo($customerId, $customerCountryCode, $customerTelephone);

                if (isset($phoneProviderInfo)) {
                    if ($phoneProviderInfo->provider == 'telesign') {
                        $returnInt = $phoneProviderInfo->customers_telephone_type - 300; // Telesign code start with 301 and its 301 = maxmind's 1
                    } else {
                        $returnInt = $phoneProviderInfo->customers_telephone_type;
                    }
                }
            }
        }
        
        return $returnInt;
    }

    public function isMobileNumExist($phoneNo, $countryDialId, $customerId = 0, $countryDialCode = '') {
        $modelCountries = new Countries();
        $modelCustomers = new Customers();
        $modelCustomersInfoVerification = new CustomersInfoVerification();

        $returnBool = FALSE;
        $dupCustomerId = array();
        if (empty($countryDialCode) && !empty($countryDialId)) {
            $countryDialCode = $modelCountries->getCountryDialingCodeById($countryDialId);
        } else if (empty($countryDialId) && !empty($countryDialCode)) {
            $countryDialId = $modelCountries->getCountryIdByDialingCode($countryDialCode);
        }

        $duplicateId = $modelCustomers->getDuplicatePhoneId($countryDialCode, $phoneNo, $customerId);
        foreach ($duplicateId as $duplicateInfo) {
            $dupCustomerId[] = $duplicateInfo->customers_id;
        }
        $completePhone = $countryDialCode . $phoneNo;
        if (count($dupCustomerId)) {
            for ($cnt = 0, $max = count($dupCustomerId); $cnt < $max; $cnt++) {
                $phoneIsVerify = $modelCustomersInfoVerification->phoneIsVerify($dupCustomerId[$cnt], $completePhone);
                if ($phoneIsVerify) {
                    $returnBool = TRUE;
                    break;
                }
            }
        }

        return $returnBool;
    }

    public function existMobileNumId($phoneNo, $countryDialId, $customerId = 0, $countryDialCode = '')
    {
        $modelCountries = new Countries();
        $modelCustomers = new Customers();
        $modelCustomersInfoVerification = new CustomersInfoVerification();

        $return = NULL;
        $dupCustomerId = array();
        if (empty($countryDialCode) && !empty($countryDialId)) {
            $countryDialCode = $modelCountries->getCountryDialingCodeById($countryDialId);
        } else if (empty($countryDialId) && !empty($countryDialCode)) {
            $countryDialId = $modelCountries->getCountryIdByDialingCode($countryDialCode);
        }

        $duplicateId = $modelCustomers->getDuplicatePhoneId($countryDialCode, $phoneNo, $customerId);
        foreach ($duplicateId as $duplicateInfo) {
            $dupCustomerId[] = $duplicateInfo->customers_id;
        }
        $completePhone = $countryDialCode . $phoneNo;
        if (count($dupCustomerId)) {
            for ($cnt = 0, $max = count($dupCustomerId); $cnt < $max; $cnt++) {
                $phoneIsVerify = $modelCustomersInfoVerification->phoneIsVerify($dupCustomerId[$cnt], $completePhone);
                if ($phoneIsVerify) {
                    $return = $dupCustomerId[$cnt];
                    break;
                }
            }
        }

        return $return;
    }

    public function reportError($message, $extend_subject = '') {
        $serverName = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-';
        $subject = 'Phone verification Reporting' . $extend_subject . ' from ' . $serverName;
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: ' . Yii::$app->name . ' REPORTER <<EMAIL>>' . "\r\n";

        EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $message, $headers);
    }

}
