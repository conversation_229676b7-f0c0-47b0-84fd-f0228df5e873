<?php

namespace common\components;

use common\models\EmailValidation;
use Yii;
use yii\helpers\Json;

class PipwaveApiCom
{

    private $api_key;
    private $api_secret;
    private $merchant_id;

    function __construct($merchant_id)
    {
        $this->merchant_id = $merchant_id;

        $config = \Yii::$app->params['pipwave.config'];
        $this->api_key = $config['api_key'];
        $this->api_secret = $config['api_secret'];
    }

    public function verifyEmail($email_address, $user_id = null, $ip_address = null)
    {
        $ip_address = !empty($ip_address) ? $ip_address : GeneralComponent::getIPAddress();
        $url = \Yii::$app->params['pipwave.api_url'] . 'email';
        $params = [
            "action" => "validate-email",
            "user_id" => isset($user_id) ? ('' . $user_id) : $user_id,
            "email_address" => $email_address,
            "signup_ip_address" => "",
            "session_ip_address" => $ip_address,
            "api_key" => $this->api_key,
            "timestamp" => time()
        ];

        $signature_param = [
            "action" => "validate-email",
            "email_address" => $email_address,
            "api_key" => $this->api_key,
            "timestamp" => time()
        ];

        $header = [
            "Content-Type" => "application/json",
            "x-api-key" => $this->api_key
        ];

        $signature = $this->generateSignature($signature_param);
        $params['signature'] = $signature;
        $api_response = $this->curl($url, $params, $header);
        $is_email_verified = (isset($api_response['result']['action']) && $api_response['result']['action'] == "allow");

        $error_message = "";

        if (!isset($api_response['result']['action'])){
            \Yii::$app->reporter->reportToAdminViaSlack('Email verification API failed', [
                'params' => $params,
                'api_response' => $api_response,
            ]);
            $error_message = \Yii::t('verifyEmail', 'INVALID_EMAIL');
        }
        else{
            $this->saveValidationRecord([
                'email' => $email_address,
                'profile' => 'pipwave',
                'mx_found' => ($is_email_verified ? 1 : 0),
                'smtp_check' => ($is_email_verified? 1 : 0),
                'score' => ($is_email_verified ? 100 : 0),
                'raw' => Json::encode($api_response),
            ]);
        }

        if (isset($api_response['result']['action_reason'])) {
            switch ($api_response['result']['action_reason']) {
                case "mailbox-full": {
                        $error_message = \Yii::t('verifyEmail', 'TRY_AGAIN_MAILBOX_FULL');
                        break;
                    }
                case "typo": {
                        $error_message = \Yii::t('verifyEmail', 'TRY_AGAIN_TYPO');
                        break;
                    }
                case "invalid-email": {
                        $error_message = \Yii::t('verifyEmail', 'INVALID_EMAIL');
                        break;
                    }
            }
        }

        return [
            'is_verified' => $is_email_verified,
            'error_message' => $error_message
        ];
    }

    public function generateSignature($params)
    {
        $params['api_secret'] = $this->api_secret;
        ksort($params);
        $s = "";
        foreach ($params as $key => $value) {
            $s .= $key . ':' . $value;
        }
        return sha1($s);
    }
    
    protected function curl($api, $data, $header = null)
    {
        $curlObj = new CurlComponent();
        if (!empty($header)) {
            $curlObj->request_headers = $header;
        }
        $curlObj->connect_via_proxy = false;
        $result =  $curlObj->curlPost($api, json_encode($data));
        if (empty($result)) {
            return [];
        }

        return json_decode($result, true);
    }

    private function saveValidationRecord($data)
    {
        $model = new EmailValidation();
        $model->load($data, '');
        $model->trigger($model::EVENT_BEFORE_INSERT);
        $model->insertOrUpdate($model->getAttributes());
    }
}
