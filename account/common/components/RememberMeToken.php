<?php

namespace common\components;

use common\models\ShassoRememberMeTokenUpdateLog;

class RememberMeToken
{

    const REMEMBER_ME_EXPIRY_DAYS = 90;

    private static $instance;

    /**
     * @return static
     */
    public static function get()
    {
        if (!static::$instance) {
            static::$instance = new static();
        }
        return static::$instance;
    }

    public function isTokenValid($m_cust, $uc_token, $login_ip, $login_country, $login_user_agent, $login_ua_parse)
    {
        //New RM token format
        if (strpos($uc_token, '.') !== false) {
            list($salt, $token) = explode('.', $uc_token);
            if (md5($m_cust->customers_email_address . ':' . $salt . ':' . $m_cust->customers_password) != $token) {
                return false;
            }
            if ($this->isInTableByCustomersIdAndToken($m_cust->customers_id, $uc_token, $login_ip, $login_country, $login_user_agent, $login_ua_parse)) {
                //Pre-generate token
                static::generateNewUcToken($m_cust, $uc_token);
                return true;
            }
        }
        return false;
    }

    protected static function generateNewUcToken($m_cust, $old_token)
    {
        //cached token if available, old_token change to new_token. this is to reduce the possibility of multiple tokens generated when there is parallel requests coming in
        $new_uc_token = $cache_key = null;
        if ($old_token) {
            $cache_key = get_called_class() . '/generated-tokens/' . $old_token;
            $new_uc_token = \Yii::$app->cache->get($cache_key);
        }
        if (!$new_uc_token) {
            $salt = uniqid();
            $new_uc_token = $salt . '.' . md5($m_cust->customers_email_address . ':' . $salt . ':' . $m_cust->customers_password);
            //set cache for old_token change to new_token 1 mins
            if ($cache_key) {
                \Yii::$app->cache->set($cache_key, $new_uc_token, 60);
            }
        }
        return $new_uc_token;
    }

    public function generateToken($m_cust, $uc_token, $ip_address, $ip_country, $user_agent, $ua_parse_result)
    {
        //Generate new token every time
        //Grab pre-generated token or generate new one
        $new_uc_token = static::generateNewUcToken($m_cust, $uc_token);

        $now = time();
        $data = [
            'customers_id' => $m_cust->customers_id,
            'token' => $new_uc_token,
            'ip_address' => $ip_address,
            'ip_country' => $ip_country,
            'user_agent' => $user_agent,
            'parsed_ua_os' => $ua_parse_result['os'],
            'parsed_ua_browser' => $ua_parse_result['browser'],
            'expire' => $now + (60 * 60 * 24 * static::REMEMBER_ME_EXPIRY_DAYS),
        ];

        $this->setTokenData($data, $uc_token);
        return $new_uc_token;
    }

    public function deleteTokenByToken($customers_id, $uc_token)
    {
        \common\models\RememberMeToken::deleteAll(['customers_id' => $customers_id, 'token' => $uc_token]);
        return true;
    }

    public function deleteTokenByUser($customers_id, $except_uc_token = null)
    {
        $conditions = ['AND', ['customers_id' => $customers_id]];
        if ($except_uc_token) {
            $conditions[] = ['!=', 'token', $except_uc_token];
        }
        \common\models\RememberMeToken::deleteAll($conditions);
        return true;
    }

    protected function isInTableByCustomersIdAndToken($customers_id, $token, $login_ip, $login_country, $login_user_agent, $login_ua_parse)
    {
        try {
            $valid = false;
            $r = \common\models\RememberMeToken::findOne([
                'customers_id' => $customers_id,
                'token' => $token
            ]);
            if ($r) {
                $rm_ip_country = $r->login_country;
                $rm_user_agent = $r->user_agent;
                $item = $r->getAttributes(null, ['expire']);
                if (empty($r->parsed_ua_os) || empty($r->parsed_ua_browser)) {
                    $rm_ua_result = GeneralComponent::parseUserAgent($rm_user_agent);
                    $item['parsed_ua_os'] = $rm_ua_result['os'];
                    $item['parsed_ua_browser'] = $rm_ua_result['browser'];
                } else {
                    $rm_ua_result['os'] = $r->parsed_ua_os;
                    $rm_ua_result['browser'] = $r->parsed_ua_browser;
                }
                if ($rm_ip_country == $login_country && $login_ua_parse['os'] == $rm_ua_result['os'] && $login_ua_parse['browser'] == $rm_ua_result['browser']) {
                    $valid = true;
                }
                if (!$valid) {
                    $current_data = [
                        'customers_id' => $customers_id,
                        'token_id' => !empty($item['token_id']) ? $item['token_id'] : $token,
                        'login_ip' => $login_ip,
                        'login_country' => $login_country,
                        'user_agent' => $login_user_agent,
                        'parsed_ua_os' => $login_ua_parse['os'],
                        'parsed_ua_browser' => $login_ua_parse['browser'],
                    ];
                    ShassoRememberMeTokenUpdateLog::log('delete', $item, $current_data);
                    $this->deleteTokenByToken($customers_id, $token);
                }
            }
            return $valid;
        } catch (\Exception $ex) {
            \Yii::$app->reporter->reportToAdminViaSlack('RM Ddb failed during RM check', $ex);
            return false;
        }
    }

    protected function setTokenData($data, $original_uc_token)
    {
        $original_data = [];
        if ($original_uc_token) {
            try {
                $ar = \common\models\RememberMeToken::findOne(['customers_id' => $data['customers_id'], 'token' => $original_uc_token]);
                if ($ar) {
                    $data['token_id'] = $ar->token_id;
                    $original_data = $ar->getAttributes(null, ['expire']);
                    $ar->delete();
                }
                $this->insertTokenData($data, $original_data);
            } catch (\Exception $ex) {
                \Yii::$app->reporter->reportToAdminViaSlack('RM Ddb failed during delete original token data', $ex);
            }
        } else {
            $this->insertTokenData($data, $original_data);
        }
    }

    protected function insertTokenData($data, $original_data)
    {
        try {
            $data['token_id'] = !empty($data['token_id']) ? $data['token_id'] : GeneralComponent::generateRandomString();
            $item = [
                'customers_id' => $data['customers_id'],
                'token_id' => (string)$data['token_id'],
                'token' => (string)$data['token'],
                'login_ip' => (string)$data['ip_address'],
                'login_country' => (string)$data['ip_country'],
                'user_agent' => (string)$data['user_agent'],
                'parsed_ua_os' => (string)$data['parsed_ua_os'],
                'parsed_ua_browser' => (string)$data['parsed_ua_browser'],
                'expire' => $data['expire'],
            ];
            $ar = new \common\models\RememberMeToken;
            $ar->load($item, '');
            $ar->save();
            ShassoRememberMeTokenUpdateLog::log('update', $original_data, $item);
        } catch (\Exception $ex) {
            \Yii::$app->reporter->reportToAdminViaSlack('RM Ddb failed during insert token data', $ex);
        }
    }
}