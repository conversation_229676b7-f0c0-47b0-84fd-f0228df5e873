<?php

namespace common\components;

use \Yii;
use \common\models\GenericEvent;

class Reporter extends \yii\base\Component {
    
    const TYPE_EXCEPTION = GenericEvent::TYPE_EXCEPTION,
            TYPE_NOTIFICATION = GenericEvent::TYPE_NOTIFICATION,
            TYPE_DEBUG = GenericEvent::TYPE_DEBUG;
    
    public static function generateToken($log_id, $timestamp) {
        return strtolower(sha1($log_id.\Yii::$app->params['slack.log.generic.secret'].$timestamp));
    }

    public function reportToAdminViaSlack($subject, $message, $type = self::TYPE_EXCEPTION, $slack_channel = null, $tags = '') {
        $event = $this->addEvent($subject, $message, $type, $tags, [GenericEvent::VISIBLE_TO_ADMIN]);
        $now = time();
        \Yii::$app->slack->send($subject . "\nPlease click <" . sprintf(Yii::$app->params['slack.log.generic.url'], $event->id, $now, static::generateToken($event->id, $now)) . "|here> for more details.", $slack_channel);
    }
    
    public function addEvent($subject, $message = '', $type = self::TYPE_NOTIFICATION, $tags = '', $visible_to = null) {
        if (!isset($visible_to)) {
            $visible_to = [GenericEvent::VISIBLE_TO_ADMIN, GenericEvent::VISIBLE_TO_CS];
        }
        $event = GenericEvent::createEvent($subject, $message, $type, $tags);
        foreach($visible_to as $who) {
            $event->addVisibleTo($who);
        }
        $cb = function($e = null) use ($event) {
            if (isset($e->data['hook'])) {
                \Yii::$app->db->off(\yii\db\Connection::EVENT_ROLLBACK_TRANSACTION, $e->data['hook']);
                \Yii::$app->db->off(\yii\db\Connection::EVENT_COMMIT_TRANSACTION, $e->data['hook']);
            }
            if (!$event->save()) {
                \Yii::$app->slack->send("Unable to save generic event - " . json_encode($event->getErrors()) . "<br><br>" . json_encode($event->attributes));
            }
        };
        if (\Yii::$app->db->transaction) {
            \Yii::$app->db->on(\yii\db\Connection::EVENT_ROLLBACK_TRANSACTION, $cb, [
                'hook' => $cb,
                'event' => $event,
            ]);
            \Yii::$app->db->on(\yii\db\Connection::EVENT_COMMIT_TRANSACTION, $cb, [
                'hook' => $cb,
                'event' => $event,
            ]);
        } else {
            $cb();
        }
        return $event;
    }
}