<?php

namespace common\components;

use Yii;
use yii\validators\IpValidator;

class ReserveIpValidator extends IpValidator
{
    public $networks = [
        '*' => ['any'],
        'any' => ['0.0.0.0/0', '::/0'],
        'private' => ['10.0.0.0/8', '**********/12', '***********/16', '**********/10', 'fd00::/8'],
        'multicast' => ['*********/4', 'ff00::/8'],
        'linklocal' => ['***********/16', 'fe80::/10'],
        'localhost' => ['*********/8', '::1'],
        'documentation' => ['*********/24', '************/24', '***********/24', '2001:db8::/32'],
        'system' => ['multicast', 'linklocal', 'localhost', 'documentation'],
    ];

    public function __construct($config = [])
    {
        $this->ranges = ['!private', 'any'];

        parent::__construct($config);
    }
}