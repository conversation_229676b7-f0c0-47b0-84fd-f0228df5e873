<?php

namespace common\components;

use common\models\Customers;
use common\models\CustomersLoginIpHistory;
use common\models\ShassoClient;
use common\models\ShassoClientExtraInfo;
use common\models\ShassoSsoToken;
use common\models\ShassoUserToken;
use common\models\UserLastLogin;
use common\models\ShassoYiiSession;
use Yii;
use yii\base\Component;
use yii\web\Cookie;

class SSOCom extends Component {

    public static $otp_key_api_register = "api_register";
    public static $otp_key_device = "device_pin";
    public static $otp_key_password = "reset_password";
    public static $otp_key_sns_detach = "sns_detach";

    public static function clientExtraInfo($client_id, $type) {
        $result = array();

        $m_scei = ShassoClientExtraInfo::findOne(['client_id' => $client_id, 'extra_key' => $type]);
        if (isset($m_scei->extra_value)) {
            switch ($m_scei->extra_value) {
                case "hash_hmac":
                    //For third party service hashing and query building
                    $m_sc = ShassoClient::findOne(['client_id' => $client_id]);
                    if (isset($m_sc->client_id)) {
                        $email = Yii::$app->user->email;
                        $name = Yii::$app->user->firstname;
                        $utctime = time();
                        //Name, Shared secret key, Email address, and Timestamp
                        $hash = hash_hmac('md5', $name . $m_sc->client_secret . $email . $utctime, $m_sc->client_secret);

                        $result = array(
                            'name' => $name,
                            'email' => $email,
                            'timestamp' => $utctime,
                            'id' => Yii::$app->user->id,
                            'hash' => $hash
                        );
                    }
                    break;
            }
        }

        return $result;
    }

    public static function clientLocalizationInvalidate($customer) {
        $result = array();
        $country_code_ISO = \common\models\Countries::findOne(['countries_id' => $customer->customers_country_dialing_code_id]);
        $m_sc_list = ShassoClient::find()->where([
            'and',
            ['IS NOT', 'api_url', null],
            ['!=', 'api_url', '']
        ])->all();
        foreach ($m_sc_list as $m_sc) {
            if (isset($m_sc->client_id)) {
                $customer_id = $customer->customers_id;
                $merchant_id = $m_sc->client_id;
                $utctime = time();
                $dialing_code = $country_code_ISO->countries_international_dialing_code; //60
                $country_code = $country_code_ISO->countries_iso_code_2; //MY
                $hash = md5($customer_id . '|' . $utctime . '|' . $dialing_code . '|' . $country_code . '|' . $m_sc->client_secret);
                $data = array(
                    'event' => 'phone-country-updated',
                    'cid' => $customer_id,
                    'merchant' => $merchant_id,
                    'timestamp' => $utctime,
                    'hash' => $hash,
                    'dialing_code' => $dialing_code,
                    'phone_country_code' => $country_code,
                );

                //CURL to $m_sc->api_url
                $curlObj = new CurlComponent();
                $result = $curlObj->curlGet($m_sc->api_url, $data);
                if (!empty($curlObj->getError())) {
                    Yii::$app->reporter->reportToAdminViaSlack('[Method] phoneCountryUpdated', [
                        'data' => $data,
                        'error' => $curlObj->getError(),
                    ]);
                }
                
            }
        }
    }

    public static function clientURLQuery() {
        /*
         * 3rd party query mandatory structure
         * service  : g2g, ogm, gmz
         * origin   : full path match with db
         */
        $query = '';

        if (isset($_GET['service']) && !empty($_GET['service']) && isset($_GET['origin']) && !empty($_GET['origin'])) {
            $origin = urldecode(urldecode($_GET['origin']));  // iframe browser auto encode + apps encode

            if (self::clientURLMatch($_GET['service'], $origin)) {
                $url = parse_url($_SERVER['REQUEST_URI']);
                $query = $url['query'];
            }
        }

        return $query;
    }

    public static function clientURLSetSSOToken() {
        $req_arr = array();
        $result = array();

        // 3rd party portal URL query
        $req_uri = self::clientURLQuery();
        parse_str($req_uri, $req_arr);

        // send 3rd remember me
        if (isset($_COOKIE['ogm']['un']) && isset($_COOKIE['ogm']['uc'])) {
            $result["S3RM"] = 1;
        }

        $expiry = date("Y-m-d H:i:s", time() + (24 * 60 * 60));

        if (isset($req_arr['service'])) {
            $m_client = ShassoClient::find()->where("client_id = :client OR set_token_url <> ''", array(":client" => $req_arr['service']))->all();
        } else {
            $m_client = ShassoClient::find()->where("set_token_url <> ''")->all();
        }
        if (!empty($m_client)) {
            foreach ($m_client as $_data) {
                $m_sst = ShassoSsoToken::findOne(['sess_id' => Yii::$app->session->getId(), 'client_id' => $_data->client_id, 'user_id' => Yii::$app->user->id]);
                if (isset($m_sst->sso_token)) {
                    $token = $m_sst->sso_token;
                } else {
                    $token = self::createSSOToken($_data->client_id . Yii::$app->user->id);

                    // login method mapping
                    $login_method = Yii::$app->user->login_method;
                    switch ($login_method) {
                        case self::$otp_key_api_register:
                        case self::$otp_key_device:
                        case "register":
                            $login_method = "normal";
                            break;

                        case "sns":
                            if (isset(Yii::$app->user->sns)) {
                                $login_method = Yii::$app->user->sns["login_method"];
                            }
                            break;
                    }

                    $c_attr = array(
                        'sso_token' => $token,
                        'sess_id' => Yii::$app->session->id,
                        'client_id' => $_data->client_id,
                        'user_id' => Yii::$app->user->id,
                        'login_method' => ucwords(str_replace("_", " ", $login_method)),
                        'expiry' => $expiry
                    );
                    ShassoSsoToken::model()->saveNewRecord($c_attr);
                    unset($c_attr);
                }

                $_info = self::clientExtraInfo($_data->client_id, "redirect_url_hash");
                if (isset($req_arr['service']) && ($req_arr['service'] == $_data->client_id)) {
                    $result["S3ID"] = $token;
                    $result["q"] = $_info;
                } else {
                    $_info["S3ID"] = $token;

                    $set_token_url = parse_url($_data->set_token_url);
                    if (isset($set_token_url['query'])) {
                        $set_token_q = array();
                        parse_str($set_token_url['query'], $set_token_q);
                        $_info = array_merge($_info, $set_token_q);
                    }

                    if (isset($result["S3RM"])) {
                        $_info["S3RM"] = $result["S3RM"];
                    }

                    if (strpos($_data->set_token_url, "?")) {
                        $set_token_url = substr($_data->set_token_url, 0, strpos($_data->set_token_url, "?"));
                    } else {
                        $set_token_url = $_data->set_token_url;
                    }

                    $result['portal'][] = array(
                        'url' => $set_token_url . "?" . http_build_query($_info)
                    );
                }
            }
        }

        return $result;
    }

    public static function createSSOToken($cid) {
        $salt = '';

        # salt
        for ($i = 0; $i < 10; $i++) {
            $salt .= GeneralComponent::generateRandom();
        }
        $salt = substr(md5($salt), 0, 2);

        $sso_token = md5($salt . date('YmdHis') . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY'] . $cid);

        return $sso_token;
    }

    public static function clientURLMatch($id, $uri) {
        $m_client = ShassoClient::findOne([
                    'client_id' => $id,
        ]);
        if (isset($m_client->redirect_url)) {
            $origin = explode(",", $m_client->redirect_url);
            if (in_array($uri, $origin)) {
                return true;
            }
        }

        return false;
    }

    public static function secureCodeCreate($otp_key, $key) {
        $status = false;
        $result = array();
        $error = Yii::t('sso', 'ERROR_DEVICE_INVALID_CODE');

        if (isset($key["cid"])) {
            switch ($otp_key) {
                case self::$otp_key_device:
                    /*
                     * code : time.md5(time.cid.key)
                     */
                    $cookval = "";
                    if (isset(Yii::$app->request->cookies['sa']->value['dc'])) {
                        $cookval = Yii::$app->request->cookies['sa']->value['dc'];
                        $status = self::_devicePinMatch($key["cid"]);
                    }

                    if (!$status) {
                        $status = true;
                        $time = time();
                        $userval = $time . "." . md5($time . $key["cid"] . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY']);

                        $cookval = (empty($cookval) ? "" : $cookval . "|") . $userval;
                    }

                    Yii::$app->response->cookies->add(new Cookie([
                        'name' => 'sa[dc]',
                        'value' => $cookval,
                        'expire' => time() + Yii::$app->params['DEVICE_TOKEN_COOKIE_DURATION'],
                        'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                    ]));
                    break;

                case self::$otp_key_password:
                    if ($key["passwd"] == $key["cfm_passwd"]) {
                        $cid = GeneralComponent::decrypt($key["code"], strtotime($key["req_date"]));
                        $m_cust = Customers::findOne([
                                    'customers_id' => $key["cid"]
                        ]);

                        if (isset($m_cust->customers_id) && ($m_cust->customers_id == $cid)) {
                            $result = array("cid" => $cid);
                            Customers::model()->updatePassword(Password::encryptPassword($key["passwd"]), $m_cust->customers_id);

                            $mail_obj = new EmailComponent();
                            $mail_obj->sendMail($m_cust->customers_firstname . ' ' . $m_cust->customers_lastname, $m_cust->customers_email_address, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT'), Yii::t('profile', 'EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE', array('SYS_EMAIL_SIGNATURE' => Yii::t("dev", "EMAIL_SIGNATURE"))), Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);

                            $status = true;
                            self::secureCodeDelete($otp_key, $key["cid"]);
                        }
                    } else {
                        $error = Yii::t('sso', 'ERROR_PASSWORD_MATCH');
                    }
                    break;
            }
        }

        return array(
            "status" => $status,
            "result" => $result,
            "error" => $error
        );
    }

    public static function secureCodeDelete($otp_key, $cid = "") {
        ShassoUserToken::deleteAll("token_type = :key AND expiry_date < :time", [":key" => $otp_key, ":time" => date("Y-m-d H:i:s")]);

        if (!empty($cid)) {
            ShassoUserToken::deleteAll(["user_id" => $cid, "token_type" => $otp_key]);
        }
    }

    public static function secureCodeMatch($otp_key, $key) {
        /*
         * key
         * code : otp pin
         * cid  : customer id
         */
        $status = false;
        $result = array();
        $error = "";

        self::secureCodeDelete($otp_key);

        if (isset($key["code"])) {
            if ($otp_key == self::$otp_key_device && $secret = \common\components\TwoFactorAuth::getUserTwoFactorAuthSecret($key["cid"])) {
                //Verify via 2FA method
                if (\common\components\TwoFactorAuth::getInstance()->verifyCode($secret, $key["code"])) {
                    $status = true;
                    $result = ["cid" => $key["cid"]];
                } else {
                    $error = Yii::t("general", "ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH");
                }
            } else {
                $m_cotp = ShassoUserToken::findOne(["token_type" => $otp_key, "token_value" => $key["code"]]);
                if (isset($m_cotp->user_id)) {
                    switch ($otp_key) {
                        case self::$otp_key_api_register:
                        case self::$otp_key_sns_detach:
                            if (isset($key["cid"]) && ($key["cid"] == $m_cotp->user_id)) {
                                self::secureCodeDelete($otp_key, $m_cotp->user_id);

                                $status = true;
                                $result = ["cid" => $m_cotp->user_id];
                            }
                            break;

                        case self::$otp_key_device:
                            if (isset($key["cid"]) && ($key["cid"] == $m_cotp->user_id)) {
                                self::secureCodeCreate($otp_key, ["cid" => $m_cotp->user_id]);
                                $status = true;
                                $result = ["cid" => $m_cotp->user_id];

                                if (strtotime($m_cotp->expiry_date) > strtotime("+15 minutes")) {
                                    $m_cotp->expiry_date = date("Y-m-d H:i:s", time() + (15 * 60));
                                    $m_cotp->save();
                                }
                            }
                            break;

                        case self::$otp_key_password:
                            $key["cid"] = $m_cotp->user_id;
                            $key["req_date"] = $m_cotp->created_date;
                            $ret = self::secureCodeCreate($otp_key, $key);
                            extract($ret);
                            break;
                    }
                } else {
                    switch ($otp_key) {
                        case self::$otp_key_device:
                            $error = Yii::t("sso", "ERROR_DEVICE_INVALID_CODE");
                            break;

                        case self::$otp_key_password:
                            $error = Yii::t("sso", "ERROR_INVALID_RECOVER_CODE");
                            break;
                    }
                }
            }
        } else if (isset($key["cid"])) {
            switch ($otp_key) {
                case self::$otp_key_device:
                    //If 2FA enabled, device pin is false everytime so that 2FA security is enforced
                    //Else, device pin security is enforced: only enforce device pin if it is NOT SNS
                    $status = !\common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($key["cid"]) && ((isset($key['skip_device_pin']) && $key['skip_device_pin']) || self::_devicePinMatch($key["cid"]));

                    // device cookie not found
                    if (!$status) {
                        $m_ull = UserLastLogin::findOne([
                                    'user_id' => $key["cid"]
                        ]);
                        if (!isset($m_ull->user_id)) {
                            $m_clih = CustomersLoginIpHistory::find()->where([
                                        'customers_id' => $key["cid"],
                                    ])->orderBy('customers_login_date DESC')->one();
                            if (isset($m_clih->customers_id) && (Yii::$app->geoip->countryCodeByIP($m_clih->customers_login_ip) == Yii::$app->geoip->countryCodeByIP())) {
                                $status = true;
                            }
                        }
                    }
                    break;
            }
        }

        return array(
            "status" => $status,
            "result" => $result,
            "error" => $error
        );
    }

    public static function secureCodeRequest($otp_key, $cid, $auto_renew = true) {
        self::secureCodeDelete($otp_key);

        if ($otp_key == self::$otp_key_device && \common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($cid)) {
            //No need to generate code if device pin and 2FA is enabled, we use 2FA app to generate the pin
            return null;
        }

        $otp_pin = null;

        $m_ut = ShassoUserToken::findOne([
                    "user_id" => $cid, "token_type" => $otp_key
        ]);
        if (!isset($m_ut->user_id)) {
            if ($auto_renew) {
                $created = time();

                switch ($otp_key) {
                    case self::$otp_key_api_register:
                        $otp_pin = GeneralComponent::createRandomValue(8);
                        $expiry = $created + 10;    // valid 10sec
                        break;

                    case self::$otp_key_password:
                        $otp_pin = GeneralComponent::encrypt($cid, $created);
                        $expiry = $created + (24 * 60 * 60); // valid 24hr
                        break;

                    case self::$otp_key_sns_detach:
                        $otp_pin = GeneralComponent::createRandomValue(8);
                        $expiry = $created + (15 * 60);    // valid 15min
                        break;

                    default:
                        $otp_pin = GeneralComponent::createRandomValue(6, 'digits');
                        $expiry = $created + (10 * 60);    // valid 10min
                        break;
                }

                $m_attr = array(
                    'user_id' => $cid,
                    'token_type' => $otp_key,
                    'token_value' => $otp_pin,
                    'created_date' => date("Y-m-d H:i:s", $created),
                    'expiry_date' => date("Y-m-d H:i:s", $expiry)
                );
                ShassoUserToken::model()->saveNewRecord($m_attr);
            }
        } else {
            // Do not extend device pin
            // switch ($otp_key) {
            //     case self::$otp_key_device:
            //         $m_ut->expiry_date = date("Y-m-d H:i:s", time() + (15 * 60));    // valid 15min
            //         $m_ut->save();
            //         break;
            // }

            $otp_pin = $m_ut->token_value;
        }

        return $otp_pin;
    }

    public static function _devicePinMatch($cid) {
        /*
         * code : time.md5(time.cid.key)
         */
        $status = false;

        if (isset(Yii::$app->request->cookies['sa']->value['dc'])) {
            $cookval = explode("|", Yii::$app->request->cookies['sa']->value['dc']);
            for ($i = 0, $cnt = count($cookval); $cnt > $i; $i++) {
                $key = explode(".", $cookval[$i]);
                if (isset($key[1]) && ($key[1] == md5($key[0] . $cid . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY']))) {
                    $status = true;
                    break;
                }
            }
        }

        return $status;
    }
    
    public static function kickUser($user_id, $except_session_id = null, $except_uc_token = null){
        ShassoYiiSession::deleteAllSessionsForUser($user_id, $except_session_id);
        ShassoSsoToken::deleteAll(["user_id" => $user_id]);
        RememberMeToken::get()->deleteTokenByUser($user_id, $except_uc_token);

        $m_client = ShassoClient::find()->where("kick_user_url <> ''")->all();
        if (count($m_client)) {
            $curl_obj = new CurlComponent();
            foreach ($m_client as $_num => $_data) {
                $c_attr = array(
                    "signature" => md5($user_id . "|" . $_data->client_secret),
                    "user_id" => $user_id
                );
                $curl_obj->curlPost($_data->kick_user_url, $c_attr);
            }
        }
        $c_setting = new \common\models\CustomersSetting();
        $c_setting->setCustomerSetting($user_id, \common\models\CustomersSetting::SESSION_ACTIVE_TIME, time());
    }

}
