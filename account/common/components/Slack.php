<?php

namespace common\components;

use Mak<PERSON>\Slack\Client;
use Yii;

class Slack extends \yii\base\Component{
    protected $service;
    
    public $id;
    
    public $url;
    
    public $username;
    
    public $icon;
    
    public $proxy;
    
    public function init() {
        parent::init();
        if (!empty($this->id) && !empty($this->url)) {
            $url = $this->url . $this->id;
        }
        
        if (isset($url) && !empty($this->username)) {
            $slackParams = [
                'username' => $this->username,
                'link_names' => true
            ];
            if (!empty($this->icon)) {
                $slackParams['icon'] = $this->icon;
            }
            // If proxy exists, create default Guzzle with Proxy setting
            $guzzle = empty($this->proxy) ? null : new \GuzzleHttp\Client([
                'proxy' => $this->proxy,
            ]);
            $this->service = new Client($url, $slackParams, $guzzle);
        }
    }
    
    public function send($message, $toChannel = null, $retries = 0) {
        if (isset($this->service)) {
            if (empty($toChannel)) {
                if (empty(Yii::$app->params['slack.channel'])) {
                    return;
                }
                $toChannel = Yii::$app->params['slack.channel'];
            }
            if (strpos($toChannel, "#") === 0) {
                $toChannel = substr($toChannel, 1);
            }
            try {
                $this->service->to("#" . $toChannel)->send($message);
            } catch (\Exception $ex) {
                if ($retries < 3) {
                    $this->send($message, $toChannel, $retries + 1);
                } else {
                    $ex = new \Exception($ex->getMessage() . "\n\nPrevious message: " . $message, $ex->getCode(), $ex);
                    \Yii::error("Slack send failed\n\nException: " . $ex->getMessage() . "\n\nMessage: " . $message);
                }
            }
        }
    }
    
    public function dump($toChannel = null) {
        $args = func_get_args();
        if (count($args) <= 1) {
            return;
        }
        $s = [];
        array_shift($args);
        foreach($args as $arg) {
            if (is_array($arg)) {
                $s[] = json_encode($arg);
            } else if (is_string($arg)) {
                $s[] = $arg;
            } else {
                $s[] = @var_export($arg, true);
            }
        }
        $this->send(implode("\n", $s), $toChannel);
    }
}
