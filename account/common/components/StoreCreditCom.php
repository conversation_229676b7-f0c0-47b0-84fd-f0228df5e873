<?php

namespace common\components;

use common\models\Configuration;
use common\models\CouponGvCustomer;
use common\models\Customers;
use common\models\GiftCardRedemption;
use common\models\Orders;
use common\models\OrdersExtraInfo;
use common\models\OrdersProducts;
use common\models\ShassoClient;
use common\models\ShassoClientExtraInfo;
use common\models\StoreCreditConversionHistory;
use common\models\StoreCreditHistory;
use common\models\StorePayments;
use Yii;
use yii\helpers\Html;
use yii\base\Component;
use yii\db\Expression;
use common\components\PaymentModuleInfoComponent;

class StoreCreditCom extends Component {

    public static $stmt_month = 3;
    public static $stmt_filt_month = 12;
    private $topup_diff_range = 20;

    const PRODUCT_TYPE_ID_STORE_CREDIT = 3;

    public static function accountCurrency($selected_currency = null) {
        $result = array(
            'status' => false,
            'result' => [
                'cur_code' => '',
                'sc_set' => false,
                'cur_symbol_left' => '',
                'cur_symbol_right' => '',
                'cur_title' => '',
            ],
        );

        $cur_obj = new CurrenciesComponent();
        $m_cgc = CouponGvCustomer::findOne(Yii::$app->user->id);
        if (isset($m_cgc->customer_id)) {
            if (isset($cur_obj->internal_currencies[$m_cgc->sc_currency_id])) {
                $result["status"] = true;
                $result["result"] = array(
                    "cur_id" => $m_cgc->sc_currency_id,
                    "cur_code" => $cur_obj->internal_currencies[$m_cgc->sc_currency_id]
                );
            } else {
                $result["result"] = array(
                    "cur_id" => $m_cgc->sc_currency_id,
                    "cur_code" => $cur_obj->format_id[$m_cgc->sc_currency_id]
                );
            }
            $result['result']['sc_set'] = true;
        } else {
            $cur_code = isset($selected_currency, $cur_obj->currencies[$selected_currency]) ? $selected_currency : (isset($cur_obj->currencies[Yii::$app->session['cur_code']]) ? Yii::$app->session['cur_code'] : Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"]);
            $result["status"] = true;
            $result["result"] = array(
                "cur_id" => $cur_obj->currencies[$cur_code]['currencies_id'],
                "cur_code" => $cur_code
            );
            $result['result']['sc_set'] = false;
        }

        $cur_code = $result['result']['cur_code'];
        if (!empty($cur_code) && isset($cur_obj->currencies[$cur_code])) {
            $result['result']["cur_symbol_left"] = $cur_obj->currencies[$cur_code]['symbol_left'];
            $result['result']["cur_symbol_right"] = $cur_obj->currencies[$cur_code]['symbol_right'];
            $result['result']["cur_title"] = $cur_obj->currencies[$cur_code]['title'];
        }

        return $result;
    }

    public function getCurrentCreditsBalance() {
        $modelCouponGvCustomer = new CouponGvCustomer();
        $scInfo = $modelCouponGvCustomer->getScInfo();
        $creditAccounts = array();
        if (isset($scInfo->sc_reversible_amount) && $scInfo->sc_reversible_amount != '') {
            $creditAccounts = array(
                'sc_reverse' => $scInfo->sc_reversible_amount,
                'sc_reverse_reserve_amt' => $scInfo->sc_reversible_reserve_amount,
                'sc_irreverse' => $scInfo->sc_irreversible_amount,
                'sc_irreversible_reserve_amt' => $scInfo->sc_irreversible_reserve_amount,
                'sc_currency_id' => $scInfo->sc_currency_id
            );
        }

        return $creditAccounts;
    }

    public function setStoreCreditBalance($updateInfo) {
        $modelCouponGvCustomer = new CouponGvCustomer();
        $scCurrencyId = $modelCouponGvCustomer->getScCurrency();
        $scInfo = $modelCouponGvCustomer->getScInfo();
        $cur_obj = new CurrenciesComponent();
        $updateArr = array();

        if (isset($scInfo)) {
            for ($i = 0, $max = count($updateInfo); $i < $max; $i++) {
                if (($updateInfo[$i]['field_name'] == 'sc_reversible_amount' || $updateInfo[$i]['field_name'] == 'sc_irreversible_amount')) {
                    $updateInfo[$i]['operator'] = trim($updateInfo[$i]['operator']);
                    //if found different currency type
                    if ($scCurrencyId != $updateInfo[$i]['currency_id']) {
                        $updateInfo[$i]['value'] = $cur_obj->advanceCurrencyConversion($updateInfo[$i]['value'], $cur_obj->internal_currencies[$updateInfo[$i]['currency_id']], $cur_obj->internal_currencies[$scCurrencyId], true, 'buy');
                    }

                    switch ($updateInfo[$i]['operator']) {
                        case '+':
                            $updateArr[$updateInfo[$i]['field_name']] = $scInfo->$updateInfo[$i]['field_name'] + $updateInfo[$i]['value'];
                            break;
                        case '-':
                            $updateArr[$updateInfo[$i]['field_name']] = $scInfo->$updateInfo[$i]['field_name'] - $updateInfo[$i]['value'];
                            break;
                        case '=':
                            $updateArr[$updateInfo[$i]['field_name']] = $updateInfo[$i]['value'];
                            break;
                        default:
                            break;
                    }
                } else if ($updateInfo[$i]['field_name'] == 'sc_currency_id') {
                    $updateInfo[$i]['operator'] = trim($updateInfo[$i]['operator']);
                    switch ($updateInfo[$i]['operator']) {
                        case '=':
                            $updateArr[$updateInfo[$i]['field_name']] = $updateInfo[$i]['value'];
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        if (count($updateArr)) {
            $updateArr['sc_last_modified'] = date("Y-m-d H:i:s");
            $modelCouponGvCustomer->scUpdateInfo($updateArr);

            $creditAccounts = $this->getCurrentCreditsBalance();
            return $creditAccounts;
        }
    }

    public function scMiscellaneousDeductAmount($transArr, $actionMsg = '', $actionDesc = '') {
        $cur_obj = new CurrenciesComponent();
        $CreditAccountsType = array('R' => 'Reversible', 'NR' => 'Non-Reversible');

        // get default currency id
        if (!isset($transArr['currency_id'])) {
            $transArr['currency_id'] = array_search(Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"], $cur_obj->internal_currencies);
        }

        if (isset($CreditAccountsType[$transArr['sc_type']])) {
            if ($this->checkCreditsAccountExists($transArr['currency_id'])) {
                $deductAmount = trim($transArr['deduct_amount']);
                if (is_numeric($deductAmount) && $deductAmount >= 0) {
                    $updateInfo = array(array('field_name' => $transArr['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                            'operator' => '-',
                            'value' => $deductAmount,
                            'currency_id' => $transArr['currency_id'])
                    );
                    $newScBalance = $this->setStoreCreditBalance($updateInfo);
                    $scBalanceHistoryDataArray = [
                        'customer_id' => Yii::$app->user->id,
                        'store_credit_account_type' => $transArr['sc_type'],
                        'store_credit_history_date' => date("Y-m-d H:i:s"),
                        'store_credit_history_currency_id' => (int) $newScBalance['sc_currency_id'],
                        'store_credit_history_debit_amount' => (double) $deductAmount,
                        'store_credit_history_credit_amount' => 'NULL',
                        'store_credit_history_r_after_balance' => (double) $newScBalance['sc_reverse'],
                        'store_credit_history_nr_after_balance' => (double) $newScBalance['sc_irreverse'],
                        'store_credit_history_trans_type' => $transArr['type'],
                        'store_credit_history_trans_id' => $transArr['id'],
                        'store_credit_activity_type' => $transArr['act_type'],
                        'store_credit_history_activity_title' => $actionMsg,
                        'store_credit_history_activity_desc' => $actionDesc,
                        'store_credit_history_activity_desc_show' => (int) $transArr['show_desc'],
                        'store_credit_history_added_by' => $transArr['added_by'],
                        'store_credit_history_added_by_role' => $transArr['added_by_role']
                    ];

                    $m_sch = new StoreCreditHistory();
                    $historyId = $m_sch->saveInfo($scBalanceHistoryDataArray);
                    $returnResult = array('sc_type' => $transArr['sc_type'], 'sc_trans_id' => $historyId, 'sc_amount' => (double) $deductAmount, 'sc_currency' => $cur_obj->internal_currencies[$newScBalance['sc_currency_id']]);

                    return $returnResult;
                }
            }
        }

        return false;
    }

    public function scMiscellaneousAddAmount($transArr, $actionMsg = '', $actionDesc = '') {
        $cur_obj = new CurrenciesComponent();
        $CreditAccountsType = array('R' => 'Reversible', 'NR' => 'Non-Reversible');

        // get default currency id
        if (!isset($transArr['currency_id'])) {
            $transArr['currency_id'] = array_search(Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"], $cur_obj->internal_currencies);
        }

        if (isset($CreditAccountsType[$transArr['sc_type']])) {
            if ($this->checkCreditsAccountExists($transArr["currency_id"])) {
                $addAmount = trim($transArr['add_amount']);
                if (is_numeric($addAmount) && $addAmount >= 0) {
                    $updateInfo = array(array('field_name' => $transArr['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                            'operator' => '+',
                            'value' => $addAmount,
                            'currency_id' => $transArr['currency_id']),
                        array('field_name' => 'sc_currency_id',
                            'operator' => '=',
                            'value' => $transArr['currency_id'])
                    );
                    $newScBalance = $this->setStoreCreditBalance($updateInfo);
                    $scBalanceHistoryDataArray = [
                        'customer_id' => Yii::$app->user->id,
                        'store_credit_account_type' => $transArr['sc_type'],
                        'store_credit_history_date' => date("Y-m-d H:i:s"),
                        'store_credit_history_currency_id' => (int) $newScBalance['sc_currency_id'],
                        'store_credit_history_debit_amount' => 'NULL',
                        'store_credit_history_credit_amount' => (double) $addAmount,
                        'store_credit_history_r_after_balance' => (double) $newScBalance['sc_reverse'],
                        'store_credit_history_nr_after_balance' => (double) $newScBalance['sc_irreverse'],
                        'store_credit_history_trans_type' => $transArr['type'],
                        'store_credit_history_trans_id' => $transArr['id'],
                        'store_credit_activity_type' => $transArr['act_type'],
                        'store_credit_history_activity_title' => $actionMsg,
                        'store_credit_history_activity_desc' => $actionDesc,
                        'store_credit_history_activity_desc_show' => (int) $transArr['show_desc'],
                        'store_credit_history_added_by' => $transArr['added_by'],
                        'store_credit_history_added_by_role' => $transArr['added_by_role']
                    ];

                    $m_sch = new StoreCreditHistory();
                    $historyId = $m_sch->saveInfo($scBalanceHistoryDataArray);
                    $returnResult = [
                        'sc_type' => $transArr['sc_type'],
                        'sc_trans_id' => $historyId,
                        'sc_amount' => (double) $addAmount,
                        'sc_currency' => $cur_obj->internal_currencies[$newScBalance['sc_currency_id']],
                        'sc_currency_id' => $newScBalance['sc_currency_id']
                    ];

                    return $returnResult;
                }
            }
        }

        return false;
    }

    public function checkCreditsAccountExists($new_cur_id = 0) {
        $m_cgc = CouponGvCustomer::findOne([
                    'customer_id' => Yii::$app->user->id,
        ]);
        if (isset($m_cgc->customer_id)) {
            return true;
        } else {
            $m_cust = Customers::findOne([
                        'customers_id' => Yii::$app->user->id
            ]);
            if (isset($m_cust->customers_id)) {
                $scData = array(
                    'customer_id' => Yii::$app->user->id,
                    'amount' => 0,
                    'sc_reversible_amount' => 0,
                    'sc_reversible_reserve_amount' => 0,
                    'sc_irreversible_amount' => 0,
                    'sc_irreversible_reserve_amount' => 0,
                    'sc_last_modified' => new Expression('NOW()'),
                );

                if ($new_cur_id) {
                    $scData['sc_currency_id'] = $new_cur_id;
                }

                CouponGvCustomer::model()->saveNewRecord($scData);
                return true;
            } else {
                return false;
            }
        }
    }

    public static function statement($page, $limit, $filt, $date) {
        /*
         * X : Cancel
         * C : Compensate
         * MI: Manual Addition
         * MR: Manual Deduction
         * P : Purchase
         * R : Refund
         * B : Bonus
         * D : Redeem
         * S : Store Credit Top Up
         * XS: Extra Store Credit
         */

        $m_data = array();
        $result = array();
        $_cond = "";

        //Date filter
        if (empty($date)) {
            $_cond = " AND (store_credit_history_date >= DATE_SUB(NOW(), INTERVAL " . self::$stmt_month . " MONTH)) ";
        } else {
            $date_start = $date;
            $date_end = mktime(0, 0, 0, date('n', $date) + 1, 1, date('Y', $date));
            $_cond = " AND (store_credit_history_date >= '" . date("Y-m-d H:i:s", $date_start) . "' AND store_credit_history_date < '" . date("Y-m-d H:i:s", $date_end) . "')";
        }

        if (!empty($filt)) {
            switch ($filt) {
                case "V":
                    $_cond = " AND store_credit_activity_type = '" . $filt . "' ";
                    break;

                case "P":
                    $_cond = " AND store_credit_activity_type = 'P' AND store_credit_history_trans_type = 'C' AND store_credit_history_added_by_role = 'customers'";
                    break;
            }
        }

        $c = Yii::$app->db;
        $c_sql = "SELECT store_credit_history_id, store_credit_history_date, store_credit_history_currency_id,
                        SUM(store_credit_history_debit_amount) AS store_credit_history_debit_amount,
                        SUM(store_credit_history_credit_amount) AS store_credit_history_credit_amount,
                        SUM(IF(store_credit_account_type='R', store_credit_history_r_after_balance, 0)) AS store_credit_history_r_after_balance,
                        SUM(IF(store_credit_account_type='NR', store_credit_history_nr_after_balance, 0)) AS store_credit_history_nr_after_balance,
                        store_credit_activity_type, store_credit_history_trans_id, store_credit_history_activity_desc,
                        store_credit_history_activity_title
                    FROM " . StoreCreditHistory::tableName() . "
                    WHERE customer_id = " . Yii::$app->user->id . "
                        AND (store_credit_activity_type != 'X' AND store_credit_activity_type != 'XS' AND store_credit_account_type != 'RP')
                        " . $_cond . "
                    GROUP BY store_credit_history_date, store_credit_activity_type, store_credit_history_currency_id
                    ORDER BY store_credit_history_id DESC";
        $c_res = $c->createCommand($c_sql)->query();
        while (($row = $c_res->read()) !== false) {
            $m_data[] = array(
                'schid' => $row["store_credit_history_id"],
                'date' => $row["store_credit_history_date"],
                'trans_id' => $row["store_credit_history_trans_id"],
                'cur_id' => $row["store_credit_history_currency_id"],
                'type' => $row["store_credit_activity_type"],
                'debit' => $row["store_credit_history_debit_amount"] + $row["store_credit_history_credit_amount"],
                'credit' => $row["store_credit_history_credit_amount"] + $row["store_credit_history_debit_amount"],
                'balance' => $row["store_credit_history_r_after_balance"] + $row["store_credit_history_nr_after_balance"],
                'activity' => $row["store_credit_history_activity_title"]
            );
        }

        $total_result = count($m_data);
        if ($total_result > 0) {
            $_res = array();
            $result = array();

            $cur_obj = new CurrenciesComponent();

            foreach ($m_data as &$data) {
                $data['sorting_date'] = $data['date'] . ' ' . str_pad($data['schid'], 20, '0', STR_PAD_LEFT);
            }
            unset($data);

            $_usort_obj = new UsortComponent('sorting_date');
            $_usort_data = $_usort_obj->usort_func($m_data, 'desc');

            # filter data display per page
            if (($limit == '') || ($limit >= $total_result)) {
                $_res = $_usort_data;
            } else {
                $_count = ($page - 1);
                $_start = ($_count * $limit);
                $_end = (($_count + 1) * $limit);

                for ($i = $_start; $_end > $i; $i++) {
                    if (isset($_usort_data[$i])) {
                        $_res[] = $_usort_data[$i];
                    } else {
                        break;
                    }
                }
            }

            $prev_v = 0;

            for ($i = 0, $cnt = count($_res); $cnt > $i; $i++) {
                $cur_code = (!empty($_res[$i]['cur_id']) ? $cur_obj->format_id[$_res[$i]['cur_id']] : "");

                $result[$i] = array(
                    "date" => $_res[$i]['date'],
                    "trans_id" => $_res[$i]['trans_id'],
                    "cur_id" => $_res[$i]['cur_id'],
                    "type" => $_res[$i]["type"],
                    "desc" => Yii::t("storeCredit", "TEXT_STATEMENT_TYPE_" . $_res[$i]["type"]),
                    "debit" => (($_res[$i]['debit'] != "") ? $cur_obj->format($_res[$i]['debit'], false, $cur_code) : ""),
                    "credit" => (($_res[$i]['credit'] != "") ? $cur_obj->format($_res[$i]['credit'], false, $cur_code) : ""),
                    "balance" => (($_res[$i]['balance'] != "") ? $cur_obj->format($_res[$i]['balance'], false, $cur_code) : "")
                );

                switch ($_res[$i]['type']) {
                    case 'D':
                        if (!empty($_res[$i]["trans_id"])) {
                            $result[$i]["desc"] .= " ( " . Html::a("#" . $_res[$i]["trans_id"], Yii::$app->urlManager->createAbsoluteUrl("op/statement")) . " )";
                        }
                        break;

                    case "P":
                    case "R":
                        if (!empty($_res[$i]["trans_id"])) {
                            $site = "";
                            $link = Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'];

                            $m_oei = OrdersExtraInfo::findOne(["orders_id" => $_res[$i]["trans_id"], "orders_extra_info_key" => "site_id"]);
                            if (isset($m_oei->orders_extra_info_value)) {
                                $m_scei = ShassoClientExtraInfo::findOne(["extra_key" => "site_id", "extra_value" => $m_oei->orders_extra_info_value]);
                                if (isset($m_scei->client_id)) {
                                    $m_sc = ShassoClient::findOne($m_scei->client_id);
                                    if (isset($m_sc->client_id)) {
                                        $site = $m_sc->client_name . " ";
                                    }
                                }
                            }
                            $result[$i]["desc"] .= " ( " . $site . Html::a("#" . $_res[$i]["trans_id"], sprintf($link, $_res[$i]["trans_id"])) . " )";
                        }
                        break;

                    case "S":
                        if (!empty($_res[$i]["trans_id"])) {
                            $result[$i]["desc"] .= " ( " . Html::a("#" . $_res[$i]["trans_id"], sprintf(Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'], $_res[$i]["trans_id"])) . " )";
                        }
                        break;

                    case "V":
                        if (!empty($_res[$i]['schid'])) {
                            if ($prev_v % 2 == 0) {
                                //findOne or find
                                $m_sch = StoreCreditHistory::find("customer_id = :cid AND store_credit_history_id < :schid AND store_credit_account_type = 'R' AND store_credit_activity_type = 'V' ORDER BY store_credit_history_id DESC", array(":cid" => Yii::$app->user->id, ":schid" => $_res[$i]['schid']));
                                if (isset($m_sch->store_credit_history_id)) {
                                    $result[$i]["desc"] .= Yii::t("storeCredit", "TEXT_SC_CONVERT_FROM_TO", array("{SYS_FROM_CUR}" => $cur_obj->format_id[$m_sch->store_credit_history_currency_id], "{SYS_TO_CUR}" => $cur_code));
                                }
                            } else {
                                $m_sch = StoreCreditHistory::find("customer_id = :cid AND store_credit_history_id > :schid AND store_credit_account_type = 'R' AND store_credit_activity_type = 'V' ORDER BY store_credit_history_id ASC", array(":cid" => Yii::$app->user->id, ":schid" => $_res[$i]['schid']));
                                if (isset($m_sch->store_credit_history_id)) {
                                    $result[$i]["desc"] .= Yii::t("storeCredit", "TEXT_SC_CONVERT_FROM_TO", array("{SYS_FROM_CUR}" => $cur_code, "{SYS_TO_CUR}" => $cur_obj->format_id[$m_sch->store_credit_history_currency_id]));
                                }
                            }
                            $prev_v ++;
                        }
                        break;

                    default:
                        if (!empty($_res[$i]['activity'])) {
                            $result[$i]["desc"] = $_res[$i]['activity'];
                        }
                        break;
                }
            }
        }
        $balance = StoreCreditCom::storeCreditBalance(true);
//              
        return array("result" => $result, "total" => $total_result, "balance" => $balance);
    }

    public static function history($page, $limit) {
        $m_data = array();
        $result = array();
        $_cond = "";

        $c = Yii::$app->db;
        $c_sql = "  SELECT o.date_purchased, o.orders_id, os.orders_status_name, op.products_tax, op.products_price, op.products_quantity, o.currency
                    FROM " . Orders::tableName() . " AS o
                    INNER JOIN " . OrdersProducts::tableName() . " AS op
                        ON op.orders_id = o.orders_id
                            AND op.custom_products_type_id = " . self::PRODUCT_TYPE_ID_STORE_CREDIT . "
                    LEFT JOIN orders_status os ON o.orders_status = os.orders_status_id 
                    WHERE o.customers_id = " . Yii::$app->user->id . "
                        AND o.orders_status != 5
                        AND o.date_purchased >= DATE_SUB(CURDATE(), INTERVAL " . self::$stmt_month . " MONTH)";
        $c_res = $c->createCommand($c_sql)->query();
        while (($row = $c_res->read()) !== false) {
            $m_data[] = [
                'schid' => "",
                'date' => $row["date_purchased"],
                'trans_id' => $row["orders_id"],
                'cur_id' => $row['currency'],
                'type' => "S",
                'activity' => Yii::t("storeCredit", "TEXT_STATEMENT_TYPE_S"),
                'debit' => "",
                'credit' => "",
                'balance' => $row["products_quantity"],
                'activity' => "",
                'status' => $row["orders_status_name"]
            ];
        }

        $total_result = count($m_data);
        if ($total_result > 0) {
            $_res = array();
            $result = array();

            $cur_obj = new CurrenciesComponent();

            foreach ($m_data as &$data) {
                $data['sorting_date'] = $data['date'] . ' ' . str_pad($data['schid'], 20, '0', STR_PAD_LEFT);
            }
            unset($data);

            $_usort_obj = new UsortComponent('sorting_date');
            $_usort_data = $_usort_obj->usort_func($m_data, 'desc');

            # filter data display per page
            if (($limit == '') || ($limit >= $total_result)) {
                $_res = $_usort_data;
            } else {
                $_count = ($page - 1);
                $_start = ($_count * $limit);
                $_end = (($_count + 1) * $limit);

                for ($i = $_start; $_end > $i; $i++) {
                    if (isset($_usort_data[$i])) {
                        $_res[] = $_usort_data[$i];
                    } else {
                        break;
                    }
                }
            }

            $prev_v = 0;

            for ($i = 0, $cnt = count($_res); $cnt > $i; $i++) {
                $result[$i] = array(
                    "date" => $_res[$i]['date'],
                    "trans_id" => $_res[$i]['trans_id'],
                    "cur_id" => $_res[$i]['cur_id'],
                    "type" => $_res[$i]["type"],
                    "desc" => Yii::t("storeCredit", "TEXT_STATEMENT_TYPE_" . $_res[$i]["type"]),
                    "debit" => (($_res[$i]['debit'] != "") ? $cur_obj->format($_res[$i]['debit'], false, $_res[$i]['cur_id']) : ""),
                    "credit" => (($_res[$i]['credit'] != "") ? $cur_obj->format($_res[$i]['credit'], false, $_res[$i]['cur_id']) : ""),
                    "balance" => (($_res[$i]['balance'] != "") ? $cur_obj->format($_res[$i]['balance'], false, $_res[$i]['cur_id']) : ""),
                    "status" => $_res[$i]['status']
                );
                if (!empty($_res[$i]["trans_id"])) {
                    $result[$i]["desc"] .= " ( " . Html::a("#" . $_res[$i]["trans_id"], sprintf(Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'], $_res[$i]["trans_id"])) . " )";
                }
            }
        }
        return array("result" => $result, "total" => $total_result);
    }

    function getStoreCreditConversion($scAmount = 0, $scFromCurrency, $scToCurrency, $type = 'sell') {
        $cur_obj = new CurrenciesComponent();
        if ($scFromCurrency == '') {
            $scFromCurrency = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if ($scToCurrency == '') {
            $scToCurrency = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if (trim($scFromCurrency) == trim($scToCurrency))
            return $scAmount;

        $returnValue = $cur_obj->advanceCurrencyConversion($scAmount, $scFromCurrency, $scToCurrency, true, $type);

        return $returnValue;
    }

    public function redeemGiftCard($input) {
        $cur_obj = new CurrenciesComponent();
        $def_cur = Configuration::model()->getConfigValue('DEFAULT_CURRENCY');

        $trans_array = [
            'type' => '',
            'id' => '',
            'act_type' => 'GC',
            'user_id' => Yii::$app->user->id,
            'add_amount' => $input["redeem_deno"],
            'sc_type' => 'NR',
            'show_desc' => '0',
            'added_by' => Yii::$app->user->email,
            'added_by_role' => 'customers',
            'currency_id' => $input["redeem_cur_id"]
        ];

        $sc_topup_precheck_total_sc = StoreCreditCom::storeCreditBalance(false);
        if ($input["redeem_cur_code"] != $def_cur) {
            $sc_topup_precheck_value = $cur_obj->advanceCurrencyConversion($sc_topup_precheck_total_sc, $input["redeem_cur_code"], $def_cur, true, 'sell');
            $gc_value_to_USD = $cur_obj->advanceCurrencyConversion($input["redeem_deno"], $input["redeem_cur_code"], $def_cur, true, 'sell');
        } else {
            $sc_topup_precheck_value = $sc_topup_precheck_total_sc;
            $gc_value_to_USD = $input["redeem_deno"];
        }

        $sc_top_up_info = $this->scMiscellaneousAddAmount($trans_array, 'Gift Card Redemption');
        $sc_topup_postcheck_value_array = $this->getCurrentCreditsBalance();
        $sc_postcheck_currency_code = $cur_obj->internal_currencies[$sc_topup_postcheck_value_array['sc_currency_id']];
        $sc_postcheck_total_sc = ($sc_topup_postcheck_value_array['sc_reverse'] + $sc_topup_postcheck_value_array['sc_irreverse']) - ($sc_topup_postcheck_value_array['sc_reverse_reserve_amt'] + $sc_topup_postcheck_value_array['sc_irreversible_reserve_amt']);

        if ($sc_postcheck_currency_code != $def_cur) {
            $sc_topup_postcheck_value = $cur_obj->advanceCurrencyConversion($sc_postcheck_total_sc, $sc_postcheck_currency_code, $def_cur, true, 'sell');
        } else {
            $sc_topup_postcheck_value = $sc_postcheck_total_sc;
        }

        $added_sc_in_USD = $sc_topup_postcheck_value - $sc_topup_precheck_value;
        if (abs($added_sc_in_USD - $gc_value_to_USD) > $this->topup_diff_range) {
            $mail_content = '   Gift Card Pin Number : ' . $input["pin"] . ' 
                                Gift Card Serial Number : ' . $input["serial"] . ' 
                                Gift Card Value : ' . $input["redeem_cur_code"] . ' ' . $input["redeem_deno"] . '
                                Customer ID :' . Yii::$app->user->id . '
                                DateTime : ' . time();

            EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], "GiftCard TopUp Suspicious Value", $mail_content, []);
        }

        if (!empty($sc_top_up_info)) {
            $gc_redeem_track_array = [
                'customers_id' => Yii::$app->user->id,
                'transaction_id' => $sc_top_up_info['sc_trans_id'],
                'serial_number' => $input["serial"],
                'pin_number' => $input["pin"],
                'gift_card_deno' => $input["orig_deno"],
                'gift_card_currency_id' => $input["orig_cur_id"],
                'gift_card_currency_code' => $input["orig_cur_code"],
                'gift_card_redeem_amount' => $input["orig_deno"],
                'transaction_type' => 'SC',
                'redeem_date' => date('Y-m-d H:i:s'),
                'redeem_ip' => GeneralComponent::getIPAddress(),
                'issued_amount' => $sc_top_up_info['sc_amount'],
                'issued_currency_id' => $sc_top_up_info['sc_currency_id'],
                'issued_currency_code' => $sc_top_up_info['sc_currency']
            ];
            GiftCardRedemption::model()->saveNewRecord($gc_redeem_track_array);
        }
    }

    public function getMinimumStoreCredit($toCurrency, $minValue = 10, $minCurrency = 'USD', $roundToNearest = 10) {

        if ($toCurrency == $minCurrency) {
            $newMinValue = $minValue;
        } else {

            $newMinValue = $this->getStoreCreditConversion($minValue, $minCurrency, $toCurrency, 'buy');
        }

        $newMinValue = ceil($newMinValue / $roundToNearest) * $roundToNearest;

        return $newMinValue;
    }

    public function getMaximumStoreCredit($toCurrency, $maxValue = 2000, $maxCurrency = 'USD', $roundToNearest = 10) {

        if ($toCurrency == $maxCurrency) {
            $newMaxValue = $maxValue;
        } else {

            $newMaxValue = $this->getStoreCreditConversion($maxValue, $maxCurrency, $toCurrency, 'sell');
        }

        $newMaxValue = floor($newMaxValue / $roundToNearest) * $roundToNearest;

        return $newMaxValue;
    }

    public static function storeCreditBalance($curFormat = false) {
        $result = 0;

        $c_data = CouponGvCustomer::findOne([
                    'customer_id' => Yii::$app->user->id,
        ]);
        if (isset($c_data->customer_id)) {
            $result = (($c_data->sc_reversible_amount + $c_data->sc_irreversible_amount) - ($c_data->sc_reversible_reserve_amount + $c_data->sc_irreversible_reserve_amount));

            if ($curFormat) {
                $cur_obj = new CurrenciesComponent();
                if (isset($cur_obj->format_id[$c_data->sc_currency_id])) {
                    $result = $cur_obj->format($result, false, $cur_obj->format_id[$c_data->sc_currency_id]);
                }
            }
        }

        return $result;
    }

    public static function storeCreditWithdrawal() {
        # verify CO Pending / Processing SC Payment
        $f_error = false;
        $sc_type_array = array();

        $system_defined_send_pm_array = PaymentModuleInfoComponent::getSystemDefinedPaymentAccountSelection();
        foreach ($system_defined_send_pm_array as $_id => $_data) {
            $sc_type_array[] = (int) $_id;
        }

        if (!empty($sc_type_array)) {
            $c = StorePayments::find();
            $c_data = $c->select('t.store_payments_id')
                            ->alias('t')
                            ->where('t.user_id = ' . Yii::$app->user->id)
                            ->andWhere('t.user_role = "customers"')
                            ->andWhere('t.store_payments_status IN (1, 2)')
                            ->andWhere('t.store_payments_methods_id IN ("' . implode('","', $sc_type_array) . '")')->one();
            if (!empty($c_data)) {
                $f_error = true;
            }
        }

        return $f_error;
    }

    public function StoreCreditConversion($curr_code) {
        /*
         * error code
         * 0 : success
         * 1 : invalid currency
         * 2 : invalid sc currency
         * 3 : NRSC / RSC -ve value
         * 4 : convert-to and convert-from are same
         * 5 : no sc record
         * 6 : not allow convert due to pending disbursement request
         */
        $_error = 0;
        $_conv_result = [
            'new_bal' => 0,
            'from_cur' => '',
            'to_cur' => '',
        ];
        $_message = '';

        $cur_obj = new CurrenciesComponent();

        if (empty($curr_code) || !isset($cur_obj->currencies[$curr_code])) {
            $_error = 1;
        } else {
            $curr_id = array_search($curr_code, $cur_obj->internal_currencies);
        }

        $store_credit_conversion_history_date = date("Y-m-d H:i:s");
        $new_store_credit_row = array(
            'sc_reverse' => 0,
            'sc_reverse_reserve_amt' => 0,
            'sc_irreverse' => 0,
            'sc_irreversible_reserve_amt' => 0
        );

        // get customers info
        $customer_row = $this->getCustomerInfo();

        // first get all store credit amounts
        $store_credit_row = $this->getCurrentCreditsBalance();

        if (!empty($store_credit_row)) {
            // check CONVERT FROM currency still active in master currencies
            $from_currency_code = (isset($cur_obj->internal_currencies[$store_credit_row['sc_currency_id']]) ? $cur_obj->internal_currencies[$store_credit_row['sc_currency_id']] : false);

            if (!$from_currency_code) {
                $_error = 2;
            }

            $reversible_balance = $store_credit_row['sc_reverse'] - $store_credit_row['sc_reverse_reserve_amt'];
            $irreversible_balance = $store_credit_row['sc_irreverse'] - $store_credit_row['sc_irreversible_reserve_amt'];
            if (isset($cur_obj->format[$from_currency_code])) {
                $reversible_balance = number_format($reversible_balance, $cur_obj->format[$from_currency_code]['decimal_places'], '.', '');
                $irreversible_balance = number_format($irreversible_balance, $cur_obj->format[$from_currency_code]['decimal_places'], '.', '');
            }

            // We allow ~ -0 to pass conversion, except for below scenarios:
            if ($reversible_balance < 0 || $irreversible_balance < 0 || ($store_credit_row['sc_reverse'] - $store_credit_row['sc_reverse_reserve_amt'] < 0 && $store_credit_row['sc_reverse_reserve_amt'] > 0) || ($store_credit_row['sc_irreverse'] - $store_credit_row['sc_irreversible_reserve_amt'] < 0 && $store_credit_row['sc_irreversible_reserve_amt'] > 0)) {
                // If balance is way lesser than 0 (eg: -0.01), do not pass conversion
                // Or if balance ~ -0 but there is reserved amount, also do not pass conversion
                $_error = 3;
            }

            if (!$_error) {
                if ($curr_code != $from_currency_code) {
                    if ($store_credit_row['sc_reverse'] + $store_credit_row['sc_reverse_reserve_amt'] < 0) {
                        // -0.01 < balance < 0, then we try to re-balance back to 0
                        $trans_array = [
                            'sc_type' => 'R',
                            'added_by' => $customer_row['customers_email_address'],
                            'added_by_role' => 'customers',
                            'type' => '',
                            'id' => '',
                            'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                            'add_amount' => -($store_credit_row['sc_reverse'] + $store_credit_row['sc_reverse_reserve_amt']),
                            'show_desc' => 0,
                            'currency_id' => $store_credit_row['sc_currency_id']
                        ];
                        $this->scMiscellaneousAddAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                        unset($trans_array);
                        $new_store_credit_row['sc_reverse'] = 0;
                        $new_store_credit_row['sc_reverse_reserve_amt'] = 0;
                    } else {
                        // Create store credit statement records for RSC and NRSC before conversion to deduct the to-be-converted-amount
                        $trans_array = [
                            'sc_type' => 'R',
                            'added_by' => $customer_row['customers_email_address'],
                            'added_by_role' => 'customers',
                            'type' => '',
                            'id' => '',
                            'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                            'deduct_amount' => ($store_credit_row['sc_reverse'] + $store_credit_row['sc_reverse_reserve_amt']),
                            'show_desc' => 0,
                            'currency_id' => $store_credit_row['sc_currency_id']
                        ];
                        $this->scMiscellaneousDeductAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                        unset($trans_array);
                        $new_store_credit_row['sc_reverse'] = $cur_obj->advanceCurrencyConversion($store_credit_row['sc_reverse'], $from_currency_code, $curr_code, true, 'sell');
                        $new_store_credit_row['sc_reverse_reserve_amt'] = $cur_obj->advanceCurrencyConversion($store_credit_row['sc_reverse_reserve_amt'], $from_currency_code, $curr_code, true, 'sell');
                    }

                    if ($store_credit_row['sc_irreverse'] + $store_credit_row['sc_irreversible_reserve_amt'] < 0) {
                        // -0.01 < balance < 0, then we try to re-balance back to 0
                        $trans_array = [
                            'sc_type' => 'NR',
                            'added_by' => $customer_row['customers_email_address'],
                            'added_by_role' => 'customers',
                            'type' => '',
                            'id' => '',
                            'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                            'add_amount' => -($store_credit_row['sc_irreverse'] + $store_credit_row['sc_irreversible_reserve_amt']),
                            'show_desc' => 0,
                            'currency_id' => $store_credit_row['sc_currency_id']
                        ];
                        $this->scMiscellaneousAddAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                        unset($trans_array);
                        $new_store_credit_row['sc_irreverse'] = 0;
                        $new_store_credit_row['sc_irreversible_reserve_amt'] = 0;
                    } else {
                        // Create store credit statement records for RSC and NRSC before conversion to deduct the to-be-converted-amount
                        $trans_array = [
                            'sc_type' => 'NR',
                            'added_by' => $customer_row['customers_email_address'],
                            'added_by_role' => 'customers',
                            'type' => '',
                            'id' => '',
                            'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                            'deduct_amount' => ($store_credit_row['sc_irreverse'] + $store_credit_row['sc_irreversible_reserve_amt']),
                            'show_desc' => 0,
                            'currency_id' => $store_credit_row['sc_currency_id']
                        ];
                        $this->scMiscellaneousDeductAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                        unset($trans_array);
                        $new_store_credit_row['sc_irreverse'] = $cur_obj->advanceCurrencyConversion($store_credit_row['sc_irreverse'], $from_currency_code, $curr_code, true, 'sell');
                        $new_store_credit_row['sc_irreversible_reserve_amt'] = $cur_obj->advanceCurrencyConversion($store_credit_row['sc_irreversible_reserve_amt'], $from_currency_code, $curr_code, true, 'sell');
                    }

                    $exch_rate = $cur_obj->advanceCurrencyConversionRate($from_currency_code, $curr_code, 'sell');

                    $newCurrencyArr = [
                        'sc_currency_id' => $curr_id,
                        'sc_conversion_date' => $store_credit_conversion_history_date
                    ];
                    CouponGvCustomer::model()->scUpdateInfo($newCurrencyArr);

                    // Create store credit statement records for RSC and NRSC after conversion by adding the converted-amount
                    $trans_array = [
                        'sc_type' => 'R',
                        'added_by' => $customer_row['customers_email_address'],
                        'added_by_role' => 'customers',
                        'type' => '',
                        'id' => '',
                        'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                        'add_amount' => ($new_store_credit_row['sc_reverse'] + $new_store_credit_row['sc_reverse_reserve_amt']),
                        'show_desc' => 0,
                        'currency_id' => $curr_id
                    ];
                    $this->scMiscellaneousAddAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                    unset($trans_array);

                    $trans_array = [
                        'sc_type' => 'NR',
                        'added_by' => $customer_row['customers_email_address'],
                        'added_by_role' => 'customers',
                        'type' => '',
                        'id' => '',
                        'act_type' => Yii::t('storeCredit', 'TEXT_SC_ACTIVITY_TYPE_CONVERT'),
                        'add_amount' => ($new_store_credit_row['sc_irreverse'] + $new_store_credit_row['sc_irreversible_reserve_amt']),
                        'show_desc' => 0,
                        'currency_id' => $curr_id
                    ];
                    $this->scMiscellaneousAddAmount($trans_array, Yii::t('storeCredit', 'TEXT_SC_CONVERT'));
                    unset($trans_array);

                    // add RSC history records into conversion history table
                    $sc_conversion_history_data_array = ['customer_id' => Yii::$app->user->id,
                        'store_credit_conversion_history_date' => $store_credit_conversion_history_date,
                        'store_credit_account_type' => 'RSC',
                        'store_credit_conversion_from_currency_id' => $customer_row['sc_currency_id'],
                        'store_credit_conversion_from_amount' => number_format($store_credit_row['sc_reverse'], $cur_obj->currencies[$from_currency_code]['decimal_places'], $cur_obj->currencies[$from_currency_code]['decimal_point'], ''),
                        'store_credit_conversion_from_reserve_amount' => number_format($store_credit_row['sc_reverse_reserve_amt'], $cur_obj->currencies[$from_currency_code]['decimal_places'], $cur_obj->currencies[$from_currency_code]['decimal_point'], ''),
                        'store_credit_conversion_to_currency_id' => $curr_id,
                        'store_credit_conversion_to_amount' => number_format($new_store_credit_row['sc_reverse'], $cur_obj->currencies[$curr_code]['decimal_places'], $cur_obj->currencies[$curr_code]['decimal_point'], ''),
                        'store_credit_conversion_to_reserve_amount' => number_format($new_store_credit_row['sc_reverse_reserve_amt'], $cur_obj->currencies[$curr_code]['decimal_places'], $cur_obj->currencies[$curr_code]['decimal_point'], ''),
                        'store_credit_conversion_currency_values' => $exch_rate
                    ];
                    StoreCreditConversionHistory::model()->saveInfo($sc_conversion_history_data_array);
                    unset($sc_conversion_history_data_array);

                    // add NRSC history records into conversion history table
                    $sc_conversion_history_data_array = ['customer_id' => Yii::$app->user->id,
                        'store_credit_conversion_history_date' => $store_credit_conversion_history_date,
                        'store_credit_account_type' => 'NRSC',
                        'store_credit_conversion_from_currency_id' => $customer_row['sc_currency_id'],
                        'store_credit_conversion_from_amount' => number_format($store_credit_row['sc_irreverse'], $cur_obj->currencies[$from_currency_code]['decimal_places'], $cur_obj->currencies[$from_currency_code]['decimal_point'], ''),
                        'store_credit_conversion_from_reserve_amount' => number_format($store_credit_row['sc_irreversible_reserve_amt'], $cur_obj->currencies[$from_currency_code]['decimal_places'], $cur_obj->currencies[$from_currency_code]['decimal_point'], ''),
                        'store_credit_conversion_to_currency_id' => $curr_id,
                        'store_credit_conversion_to_amount' => number_format($new_store_credit_row['sc_irreverse'], $cur_obj->currencies[$curr_code]['decimal_places'], $cur_obj->currencies[$curr_code]['decimal_point'], ''),
                        'store_credit_conversion_to_reserve_amount' => number_format($new_store_credit_row['sc_irreversible_reserve_amt'], $cur_obj->currencies[$curr_code]['decimal_places'], $cur_obj->currencies[$curr_code]['decimal_point'], ''),
                        'store_credit_conversion_currency_values' => $exch_rate
                    ];
                    StoreCreditConversionHistory::model()->saveInfo($sc_conversion_history_data_array);
                    unset($sc_conversion_history_data_array);

                    $_to_cur = $cur_obj->format(($new_store_credit_row['sc_reverse'] + $new_store_credit_row['sc_reverse_reserve_amt'] + $new_store_credit_row['sc_irreverse'] + $new_store_credit_row['sc_irreversible_reserve_amt']), false, $cur_obj->internal_currencies[$curr_id]);

                    $_conv_result = [
                        'new_bal' => $_to_cur,
                        'from_cur' => $from_currency_code,
                        'to_cur' => $curr_code,
                    ];
                } else {
                    $_error = 4;
                }
            }
        } else {
            $_error = 5;
        }

        $_result = array(
            'error' => $_error,
            'conv_result' => $_conv_result
        );
        return $_result;
    }

    public function getCustomerInfo() {
        $_result = array();

        $c1 = CouponGvCustomer::findOne([
                    'customer_id' => Yii::$app->user->id,
        ]);
        if (!empty($c1)) {
            $_result['sc_currency_id'] = $c1->sc_currency_id;
            $_result['sc_conversion_date'] = $c1->sc_conversion_date;

            $c2 = Customers::findOne([
                        'customers_id' => Yii::$app->user->id,
            ]);
            if (!empty($c2)) {
                $_result['customer_id'] = $c2->customers_id;
                $_result['customers_email_address'] = $c2->customers_email_address;
            }
        }

        return $_result;
    }

    public static function getBlockedCountry($ipAddress) {
        $country = \Yii::$app->geoip->countryCodeByIP($ipAddress);
        if (!isset(\Yii::$app->params['BLOCKED_COUNTRY'])) {
            return false;
        }
        return in_array($country, \Yii::$app->params['BLOCKED_COUNTRY']);
    }

}
