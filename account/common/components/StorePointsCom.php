<?php

namespace common\components;

use common\components\CurrenciesComponent;
use common\models\CouponGvCustomer;
use common\models\StorePoints;
use common\models\Customers;
use common\models\Countries;
use common\models\StorePointsRedeem;
use common\models\StorePointsRedeemHistory;
use common\models\StorePointsHistory;
use Yii;
use yii\base\Component;
use common\models\Orders;
use common\components\UsortComponent;
use common\models\OrdersExtraInfo;
use common\models\OrdersProducts;

class StorePointsCom extends Component
{

    public static $modulus = 100;
    public static $sp_history_activity_type = array("B", "D", "MI", "MR", "P", "PD");
    public static $role_admin = "admin";
    public static $role_user = "customer";
    public static $role_system = "system";
    public static $stmt_month = 3;
    public static $stmt_filt_month = 12;

    public static function redeemStatus($token_amt, $curr_code = null)
    {
        $result = array(
            "currency_code" => "",
            "currency_rate" => 0,
            "token_before_redeem" => $token_amt,
            "token_after_redeem" => 0,
            "token_redeem" => 0,
            "store_credit_redeem" => 0,
            "store_credit_redeem_format" => "",
            "store_credit_redeem_display" => ""
        );
        $cur_obj = new CurrenciesComponent();

        $sc_currency = static::getCustomerSCCurrency(Yii::$app->user->id);
        if (isset($sc_currency)) {
            $result["currency_code"] = $sc_currency;
        } else {
            if (isset($curr_code) && $cur_obj->currencies[$curr_code]) {
                $result["currency_code"] = $curr_code;
            } else {
                $result["currency_code"] = Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
            }
        }

        if (!empty($result["currency_code"])) {
            $result["token_after_redeem"] = $token_amt % self::$modulus;
            $result["token_redeem"] = $token_amt - $result["token_after_redeem"];
            $result["token_redeem_display"] = number_format($result["token_redeem"], 0, '', ',');

            $result["currency_rate"] = $cur_obj->advanceCurrencyConversionRate(Yii::$app->params["REG_CONFIG"]["DEF_CURRENCY_CODE"], $result["currency_code"], 'sell');
            $result["store_credit_redeem"] = ($result["token_redeem"] / 10000) * $result["currency_rate"];
            $result["store_credit_redeem_format"] = number_format($result["store_credit_redeem"], 2, '.', ',');
            $result["store_credit_redeem_display"] = $cur_obj->format($result["store_credit_redeem"], false, $result["currency_code"]);
        }

        return $result;
    }

    public static function redeemToken($_input = array())
    {
        if (!empty($_input["currency_code"]) && !empty($_input["store_credit_redeem_format"])) {
            if (StorePoints::model()->updateStorePoint($_input["token_before_redeem"], $_input["token_redeem"])) {
                $m_cust = Customers::findOne(Yii::$app->user->id);
                if (isset($m_cust->customers_id)) {
                    $m_ctry = Countries::findOne($m_cust->customers_country_dialing_code_id);
                    $m_attr = array(
                        "user_id" => Yii::$app->user->id,
                        "user_role" => self::$role_user,
                        "user_firstname" => $m_cust->customers_firstname,
                        "user_lastname" => $m_cust->customers_lastname,
                        "user_email_address" => $m_cust->customers_email_address,
                        "user_country_international_dialing_code" => (isset($m_ctry->countries_international_dialing_code) ? $m_ctry->countries_international_dialing_code : ""),
                        "user_telephone" => $m_cust->customers_telephone,
                        "user_mobile" => $m_cust->customers_mobile,
                        "store_points_redeem_date" => new \yii\db\Expression('NOW()'),
                        "store_points_redeem_status" => 1,
                        "store_points_redeem_amount" => $_input["token_redeem"],
                        "store_points_request_currency" => $_input["currency_code"],
                        "store_points_request_currency_amount" => $_input["store_credit_redeem"],
                        "store_points_paid_currency" => $_input["currency_code"],
                        "store_points_exchange_rate" => $_input["currency_rate"],
                        "store_points_redeem_last_modified" => new \yii\db\Expression('NOW()')
                    );
                    $m_spr_id = StorePointsRedeem::model()->saveNewRecord($m_attr, true);
                    unset($m_attr);

                    if (!empty($m_spr_id)) {
                        $comment = Yii::t('wor', 'TEXT_REDEEM_COMMENT', [
                            'SYS_REDEEM_POINTS' => $_input["token_redeem_display"],
                            'SYS_REDEEM_VALUE' => $_input["store_credit_redeem_display"],
                            'SYS_REDEEM_KEY' => $m_spr_id
                        ]);

                        $m_attr = array(
                            "store_points_redeem_id" => $m_spr_id,
                            "store_points_redeem_status" => 1,
                            "date_added" => new \yii\db\Expression('NOW()'),
                            "payee_notified" => 1,
                            "comments" => $comment,
                            "changed_by" => Yii::$app->user->email,
                            "changed_by_role" => self::$role_user
                        );
                        StorePointsRedeemHistory::model()->saveNewRecord($m_attr);
                        unset($m_attr);

                        $m_attr = array(
                            "customer_id" => Yii::$app->user->id,
                            "store_points_history_date" => new \yii\db\Expression('NOW()'),
                            "store_points_history_debit_amount" => $_input['token_redeem'],
                            "store_points_history_after_balance" => $_input['token_after_redeem'],
                            "store_points_history_trans_type" => "C",
                            "store_points_history_trans_id" => $m_spr_id,
                            "store_points_history_activity_type" => "D",
                            "store_points_history_activity_title" => $m_spr_id, //"Redemption " .
                            "store_points_history_activity_desc" => $comment,
                            "store_points_history_activity_desc_show" => 1,
                            "store_points_history_added_by" => Yii::$app->user->email,
                            "store_points_history_added_by_role" => self::$role_user
                        );
                        StorePointsHistory::model()->saveNewRecord($m_attr);
                        return $m_spr_id;
                    }
                }
            } else {
                $m_spr_id = StorePointsHistory::find()
                    ->select('store_points_history_trans_id')
                    ->where([
                        'customer_id' => Yii::$app->user->id,
                        'store_points_history_debit_amount' => $_input["token_redeem"],
                    ])
                    ->andWhere(['>', 'store_points_history_date', new \yii\db\Expression('DATE_SUB(NOW(), INTERVAL 5 MINUTE)')])
                    ->orderBy(['store_points_history_trans_id' => SORT_DESC])
                    ->scalar();
                if ($m_spr_id) {
                    return $m_spr_id;
                }
            }
        }

        return false;
    }

    public static function statement($page, $limit, $filt, $date)
    {
        /*
         * B : Bonus
         * C : Compensate
         * D : Redeem
         * MI: Manual Addition
         * MR: Manual Deduction
         * P : Purchase
         * PD: Order Rollback
         * R : Refund
         * X : Cancellation
         */

        $m_data = array();
        $result = array();
        $_cond = '';

        //Date filter
        if (empty($date)) {
            $_cond = " AND (store_points_history_date >= DATE_SUB(NOW(), INTERVAL " . self::$stmt_month . " MONTH)) ";
        } else {
            $date_start = $date;
            $date_end = mktime(0, 0, 0, date('n', $date) + 1, 1, date('Y', $date));
            $_cond = " AND (store_points_history_date >= '" . date("Y-m-d H:i:s", $date_start) . "' AND store_points_history_date < '" . date("Y-m-d H:i:s", $date_end) . "')";
        }

        $c_sql = "  SELECT store_points_history_date, store_points_history_debit_amount, store_points_history_credit_amount, store_points_history_after_balance, 
                        store_points_history_trans_id, store_points_history_activity_type, store_points_history_activity_desc 
                    FROM " . StorePointsHistory::tableName() . " 
                    WHERE customer_id = " . Yii::$app->user->id . " 
                        AND store_points_history_activity_desc_show = 1 
                        " . $_cond;
        $c_res = \Yii::$app->db->createCommand($c_sql)->query();
        while (($row = $c_res->read()) !== false) {
            $m_data[] = array(
                'date' => $row["store_points_history_date"],
                'debit' => $row["store_points_history_debit_amount"],
                'credit' => $row["store_points_history_credit_amount"],
                'balance' => $row["store_points_history_after_balance"],
                'trans_id' => $row["store_points_history_trans_id"],
                'type' => $row["store_points_history_activity_type"],
                'activity' => $row["store_points_history_activity_desc"]
            );
        }

        $total_result = count($m_data);
        if ($total_result > 0) {
            $_res = array();

            $_usort_obj = new UsortComponent('date');
            $_usort_data = $_usort_obj->usort_func($m_data, 'desc');

            foreach ($_usort_data as $_num => $_val) {
                $_data = array(
                    "date" => $_val['date'],
                    "trans_id" => $_val['trans_id'],
                    "type" => $_val["type"],
                    "desc" => Yii::t("wor", "TEXT_STATEMENT_TYPE_" . $_val["type"]),
                    "debit" => number_format($_val['debit'], 0, "", ","),
                    "credit" => number_format($_val['credit'], 0, "", ","),
                    "balance" => number_format($_val['balance'], 0, "", ","),
                    "status" => "",
                    "activity" => $_val["activity"]
                );

                switch ($_val['type']) {
                    case "B":
                    case "MI":
                    case "MR":
                        $_data["status"] = Yii::t('wor', 'TEXT_STATEMENT_STATUS_COMPLETE');
                        break;

                    case "P":
                    case "PD":
                        if (!empty($_data["trans_id"])) {
                            $m_oei = OrdersExtraInfo::findOne(['orders_id' => $_data["trans_id"], 'orders_extra_info_key' => 'site_id']);
                            if (isset($m_oei->orders_extra_info_value) && ($m_oei->orders_extra_info_value != 5)) {
                                $_data["desc"] = Yii::t("dev", "TEXT_WOR_SITE_ID_0");
                                $_data["site"] = 0;
                            }
                        }
                        $_data["status"] = Yii::t('wor', 'TEXT_STATEMENT_STATUS_COMPLETE');
                        break;

                    case "D":
                        if (!empty($_data["trans_id"])) {
                            $m_spr = StorePointsRedeem::findOne($_data["trans_id"]);
                            if (isset($m_spr->store_points_redeem_id)) {
                                switch ($m_spr->store_points_redeem_status) {
                                    case 1:
                                        $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_PENDING");
                                        break;

                                    case 2:
                                        $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_PROCESSING");
                                        break;

                                    case 3:
                                        $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_COMPLETE");
                                        break;

                                    case 4:
                                        $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_CANCEL");
                                        break;
                                }
                            }
                        }
                        break;
                }

                $_res[] = $_data;
                unset($_data);
            }

            # filter data display per page
            if (($limit == '') || ($limit >= $total_result)) {
                $result = $_res;
            } else {
                $_count = ($page - 1);
                $_start = ($_count * $limit);
                $_end = (($_count + 1) * $limit);

                for ($i = $_start; $_end > $i; $i++) {
                    if (isset($_res[$i])) {
                        $result[] = $_res[$i];
                    } else {
                        break;
                    }
                }
            }
        }

        return array("result" => $result, "total" => $total_result);
    }

    public static function history($page, $limit)
    {
        /*
         * B : Bonus
         * C : Compensate
         * D : Redeem
         * MI: Manual Addition
         * MR: Manual Deduction
         * P : Purchase
         * PD: Order Rollback
         * R : Refund
         * X : Cancellation
         */

        $m_data = array();
        $result = array();

        $c_sql = "  SELECT store_points_history_date, store_points_history_debit_amount, store_points_history_credit_amount, store_points_history_after_balance, 
                        store_points_history_trans_id, store_points_history_activity_type, store_points_history_activity_desc 
                    FROM " . StorePointsHistory::tableName() . " 
                    WHERE customer_id = " . Yii::$app->user->id . " 
                        AND store_points_history_activity_desc_show = 1 
                        AND store_points_history_activity_type = 'D'
                        AND store_points_history_date >= DATE_SUB(CURDATE(), INTERVAL " . self::$stmt_month . " MONTH)";
        $c_res = \Yii::$app->db->createCommand($c_sql)->query();
        while (($row = $c_res->read()) !== false) {
            $m_data[] = array(
                'date' => $row["store_points_history_date"],
                'debit' => $row["store_points_history_debit_amount"],
                'credit' => $row["store_points_history_credit_amount"],
                'balance' => $row["store_points_history_after_balance"],
                'trans_id' => $row["store_points_history_trans_id"],
                'type' => $row["store_points_history_activity_type"],
                'activity' => $row["store_points_history_activity_desc"]
            );
        }

        $total_result = count($m_data);
        if ($total_result > 0) {
            $_res = array();

            $_usort_obj = new UsortComponent('date');
            $_usort_data = $_usort_obj->usort_func($m_data, 'desc');

            $cur_obj = new CurrenciesComponent();

            foreach ($_usort_data as $_num => $_val) {
                $_data = array(
                    "date" => $_val['date'],
                    "trans_id" => $_val['trans_id'],
                    "type" => $_val["type"],
                    "desc" => Yii::t("wor", "TEXT_STATEMENT_TYPE_" . $_val["type"]),
                    "debit" => number_format($_val['debit'], 0, "", ","),
                    "credit" => number_format($_val['credit'], 0, "", ","),
                    "balance" => number_format($_val['balance'], 0, "", ","),
                    'amount' => "",
                    "status" => "",
                    "activity" => $_val["activity"]
                );

                if (!empty($_data["trans_id"])) {
                    $m_spr = StorePointsRedeem::findOne($_data["trans_id"]);
                    if (isset($m_spr->store_points_redeem_id)) {
                        $_data['amount'] = $cur_obj->format($m_spr->store_points_request_currency_amount, false, $m_spr->store_points_request_currency);
                        switch ($m_spr->store_points_redeem_status) {
                            case 1:
                                $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_PENDING");
                                break;

                            case 2:
                                $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_PROCESSING");
                                break;

                            case 3:
                                $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_COMPLETE");
                                break;

                            case 4:
                                $_data["status"] = Yii::t("wor", "TEXT_STATEMENT_STATUS_CANCEL");
                                break;
                        }
                    }
                }

                $_res[] = $_data;
                unset($_data);
            }

            # filter data display per page
            if (($limit == '') || ($limit >= $total_result)) {
                $result = $_res;
            } else {
                $_count = ($page - 1);
                $_start = ($_count * $limit);
                $_end = (($_count + 1) * $limit);

                for ($i = $_start; $_end > $i; $i++) {
                    if (isset($_res[$i])) {
                        $result[] = $_res[$i];
                    } else {
                        break;
                    }
                }
            }
        }

        return array("result" => $result, "total" => $total_result);
    }

    public static function getCustomerSCCurrency($customers_id)
    {
        if (empty(\Yii::$app->params['OG_MICROSERVICE']['URL'])) {
            return null;
        }
        $time = time();

        $url = \Yii::$app->params['OG_MICROSERVICE']['URL'];
        $data = [
            'source' => \Yii::$app->params['OG_MICROSERVICE']['KEY'],
            'signature' => md5(\Yii::$app->params['OG_MICROSERVICE']['KEY'] . $time . '|' . \Yii::$app->params['OG_MICROSERVICE']['SECRET']),
            'time' => $time,
            'checking_type' => 'BALANCE',
        ];
        $curl = new CurlComponent();
        $curl->request_headers = [
            'Content-Type' => 'application/json',
        ];

        try {
            $response = $curl->curlGetBody($url . '/store-credits/' . $customers_id, json_encode($data));
            $result = json_decode($response, true);
        } catch (\Exception $ex) {
            \Yii::$app->reporter->reportToAdminViaSlack('OG Microservice - CURL error', $ex);
            return null;
        }
        if ($err = $curl->getError()) {
            \Yii::$app->reporter->reportToAdminViaSlack('OG Microservice - CURL error', $err);
            return null;
        }
        if (!$result || !isset($result['status'])) {
            \Yii::$app->reporter->reportToAdminViaSlack('OG Microservice - CURL unexpected response', $response);
            return null;
        }
        if ($result['status'] !== true) {
            if (isset($result['error']['code']) && $result['error']['code'] == 14) {
                return null;
            }
            \Yii::$app->reporter->reportToAdminViaSlack('OG Microservice - CURL API call failed', $response);
            return null;
        }
        if (!empty($result['result']['Items']['currency'])) {
            return $result['result']['Items']['currency'];
        }
        return null;
    }

}
