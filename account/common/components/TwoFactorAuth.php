<?php

namespace common\components;

use common\models\CustomersStaticInfo;
use Endroid\QrCode\QrCode;
use RobThree\Auth\Providers\Qr\IQRCodeProvider;
use Yii;

class TwoFactorAuth extends \RobThree\Auth\TwoFactorAuth {
    
    protected static $instance;
    
    /**
     * @return TwoFactorAuth
     */
    public static function getInstance() {
        if (!static::$instance) {
            static::$instance = new static(Yii::$app->name, 6, 30, 'sha1', new QrProvider());
        }
        return static::$instance;
    }
    
    public static function getUserTwoFactorAuthSecret($customer_id) {
        $info = CustomersStaticInfo::getInfo(CustomersStaticInfo::KEY_TWO_FACTOR_SECRET, $customer_id);
        return !empty($info->value) ? $info->value : null;
    }
    
    public static function isUserTwoFactorAuthEnabled($customer_id) {
        $cache_key = static::getIsEnabledCacheKey($customer_id);
        $value = \Yii::$app->cache->get($cache_key);
        if ($value === false) {
            $value = !empty(static::getUserTwoFactorAuthSecret($customer_id)) ? 'yes' : 'no';
            \Yii::$app->cache->set($cache_key, $value, 86400);
        }
        return $value == 'yes';
    }
    
    public static function removeUserTwoFactorAuth($customer_id) {
        CustomersStaticInfo::deleteAll([
            'info_key' => CustomersStaticInfo::KEY_TWO_FACTOR_SECRET,
            'customers_id' => $customer_id,
        ]);
        $cache_key = static::getIsEnabledCacheKey($customer_id);
        \Yii::$app->cache->delete($cache_key);
    }
    
    public static function setUserTwoFactorAuth($customer_id, $secret) {
        CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_TWO_FACTOR_SECRET, $secret, $customer_id);
        $cache_key = static::getIsEnabledCacheKey($customer_id);
        \Yii::$app->cache->delete($cache_key);
        return $secret;
    }
    
    public function getQRCodeImageAsDataUri($label, $secret, $size = 200) {
        return parent::getQRCodeImageAsDataUri(Yii::$app->name . ' - ' . $label, $secret, $size);
    }
    
    protected static function getIsEnabledCacheKey($customer_id) {
        return "security/$customer_id/twoFactorAuth/isEnabled/boolean";
    }
}

class QrProvider implements IQRCodeProvider {
    
    protected $module_qr;
    
    public function __construct() {
        $this->module_qr = new QrCode();
        
        $this->module_qr
            ->setPadding(5)
            ->setErrorCorrection('high')
            ->setImageType(QrCode::IMAGE_TYPE_PNG);
    }
    
    public function getQRCodeImage($qrtext, $size) {
        $this->module_qr
            ->setText($qrtext)
            ->setSize($size);
        $qr = $this->module_qr->getImage();
        $raw = static::getPngRaw($qr);
        imagedestroy($qr);
        return $raw;
    }
    
    public function getMimeType() {
        return $this->module_qr->getContentType();
    }
    
    protected static function getPngRaw($png) {
        ob_start();
        imagepng($png);
        $raw = ob_get_contents();
        ob_end_clean();
        return $raw;
    }
}
