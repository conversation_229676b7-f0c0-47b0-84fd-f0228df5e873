<?php
namespace common\components;

use yii\base\Component;

class UsortComponent extends Component {

    private $key;
    private $sort_order;

    public function __construct($key) {
        $this->key = $key;
    }

    public function usort_func($data, $sort = 'asc') {
        $this->sort_order = $sort;
        usort($data, array($this, 'usort_func_sort'));

        return $data;
    }

    public function usort_func_sort($a, $b) {
        
        $_a = strtolower($a[$this->key]);
        $_b = strtolower($b[$this->key]);

        if ($_a == $_b) {
            return 0;
        }

        if ($this->sort_order == 'asc') {
            return (($_a > $_b) ? 1 : -1);
        } else if ($this->sort_order == 'desc') {
            return (($_a < $_b) ? 1 : -1);
        }
    }

}