<?php

namespace common\components;

class WebErrorHandler extends \yii\web\ErrorHandler {
    
    protected function renderException($exception) {
        // Auto-report to admin on errors
        $db = \Yii::$app->getDb();
        $txn = $db->getTransaction();
        if ($txn) {
            //Rollback first in order to allow logs to enter db
            $txn->rollBack();
        }
        if (!isset($exception->statusCode) || !preg_match('/4../', $exception->statusCode)) {
            \Yii::$app->reporter->reportToAdminViaSlack('Unexpected ' . \Yii::$app->id . ' portal error - ' . ((!isset(\Yii::$app->user->isGuest) || \Yii::$app->user->isGuest) ? 'Guest' : \Yii::$app->user->id), $exception);
        }
        return parent::renderException($exception);
    }
}
