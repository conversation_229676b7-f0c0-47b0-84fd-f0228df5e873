<?php

namespace common\components\freshdesk_merchant;

use common\components\FreshdeskApiCom;

class FreshdeskOGApiCom extends FreshdeskApiCom {
    
    public function createTicketForMobileChangeApproval($user_id, $new_country, $wipe_credit) {
        $cf = [
            "cf_product_type" => "Others or non-product related",
            "cf_inquiring_as_agent" => "Buyer",
            "cf_inquiry_type881587" => "AFT & Payment Related",
            "cf_issue_type" => "Order Verification"
        ];
        $group_id = 5000259081; //AFT
        $ticket_type = null;
        $tags = [];
        return $this->_createTicketForMobileChangeApproval($user_id, $new_country, $wipe_credit, $cf, $group_id, $ticket_type, $tags);
    }

    public function createTicketForEmailVerificationCompleted($customers_id) {
        $cf = [
            "cf_product_type" => "Others or non-product related",
            "cf_inquiring_as_agent" => "Buyer",
            "cf_inquiry_type881587" => "AFT & Payment Related",
            "cf_issue_type" => "Selfie / EKYC"
        ];
        $group_id = 5000259081; //AFT
        $ticket_type = null;
        $tags = [];
        return $this->_createTicketForEmailVerificationCompleted($customers_id, $cf, $group_id, $ticket_type, $tags);
    }

    public function createTicketForKYCCompleted($customers_id) {
        $cf = [
            "cf_product_type" => "Others or non-product related",
            "cf_inquiring_as_agent" => "Buyer",
            "cf_inquiry_type881587" => "AFT & Payment Related",
            "cf_issue_type" => "Selfie / EKYC"
        ];
        $group_id = 5000259081; //AFT
        $ticket_type = null;
        $tags = [];
        return $this->_createTicketForKYCCompleted($customers_id, $cf, $group_id, $ticket_type, $tags);
    }
}