<?php

namespace common\components\gift_cards;

use yii\base\Component;

abstract class AbstractGiftCard extends Component {
    
    const ERROR_REDEEM_FAIL = 1,
            ERROR_CARD_DEACTIVATED = 2,
            ERROR_CARD_REDEEMED = 3,
            ERROR_INVALID_CURRENCY = 4,
            ERROR_AUTH = 5,
            ERROR_PROVIDER_MESSAGE = 99;
    
    public $code;
    
    public $name;
    
    public $purchase_link;
    
    public $allowable_group;
    
    public function redeem($input) {
        return $this->actualRedeem($input['rd_serial'], $input['rd_pin']);
    }
    
    public function validatePin($input) {
        return $this->actualValidatePin($input['rd_serial'], $input['rd_pin']);
    }
    
    abstract public function actualRedeem($serial, $pin);
    
    abstract public function actualValidatePin($serial, $pin);
}
