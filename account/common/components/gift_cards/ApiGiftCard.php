<?php

namespace common\components\gift_cards;

use yii\base\InvalidConfigException;

abstract class ApiGiftCard extends AbstractGiftCard {
    
    public $api;
    
    public $api_url = "";
    public $api_merchant = "";
    public $api_secret = "";

    public function init() {
        parent::init();
        
        if (empty($this->api['url']) || empty($this->api['merchant']) || empty($this->api['secret'])) {
            throw new InvalidConfigException('api.url, api.merchant and api.secret is required');
        }
        
        $this->api_url = $this->api['url'];
        $this->api_merchant = $this->api['merchant'];
        $this->api_secret = $this->api['secret'];
    }
}
