<?php

namespace common\components\gift_cards;

use common\components\CurlComponent;
use common\components\CurrenciesComponent;
use yii\helpers\Json;

class OffgamersGiftCard extends ApiGiftCard {
    
    public function actualRedeem($serial, $pin) {
        $resp = $this->callApi('api_redeem.php', 'redeem', $serial, $pin);
        if ($resp['status'] === true) {
            if (isset($resp['result']["success"])) {
                $result["status"] = true;
            } else {
                $stat = $this->parseResponse($resp['result']);
                $resp['status'] = false;
                $resp["error"] = $stat["error"];
            }
        }
        return $resp;
    }

    public function actualValidatePin($serial, $pin) {
        $resp = $this->callApi('api_get_balance.php', 'getPinInfo', $serial, $pin);
        if ($resp['status'] === true) {
            $stat = $this->parseResponse($resp['result']);
            if ($stat["status"] !== true) {
                $resp['status'] = $stat['status'];
                $resp["error"] = $stat["error"];
            }
        }
        return $resp;
    }
    
    protected function callApi($url, $scope, $serial, $pin) {
        $result = array(
            "status" => false,
            "result" => array(),
            "error" => ""
        );
        
        $auth = $this->authenticate($scope);
        if (isset($auth['token']) && !empty($auth['token'])) {
            $api_url = $this->api_url . '/' . $url;
            $data = array(
                'pin' => $pin,
                'serial' => $serial,
                'token' => $auth['token']
            );

            $curl_obj = new CurlComponent();
            $response = $curl_obj->curlPost($api_url, $data);
            $resp = Json::decode($response);
            $result['status'] = true;
            $result['result'] = $resp;
        } else {
            $result['status'] = static::ERROR_AUTH;
        }
        return $result;
    }

    protected function authenticate($scope) {
        $url = $this->api_url . '/api_auth.php';
        $hash = time();
        $auth = md5($this->api_merchant . $this->api_secret . $hash);
        $data = array(
            'merchant_code' => $this->api_merchant,
            'auth' => $auth,
            'hash' => $hash,
            'scope' => $scope,
        );
        $curl_obj = new CurlComponent();
        $result = $curl_obj->curlPost($url, $data);

        return Json::decode($result);
    }

    protected function parseResponse($resp = array()) {
        $result = array(
            "status" => false,
            "error" => "",
        );
        if (empty($resp)) {
            $result['status'] = static::ERROR_REDEEM_FAIL;
        } else if (isset($resp['success'])) {
            $result["status"] = true;
        } else if (!isset($resp['status']) && empty($resp["error"])) {
            $result['status'] = static::ERROR_REDEEM_FAIL;
        } else if (isset($resp["error"]) && $resp["error"]) {
            $result['status'] = static::ERROR_PROVIDER_MESSAGE;
            $result["error"] = $resp["error"];
        } else if ($resp["status"] == 0) {
            $result['status'] = static::ERROR_CARD_DEACTIVATED;
        } else if ($resp["redeem"] == 1) {
            $result['status'] = static::ERROR_CARD_REDEEMED;
        } else if (isset($resp['start_date']) && isset($resp['end_date'])) {
            $cur_obj = new CurrenciesComponent();

            $start_date = strtotime($resp['start_date'] . ' 00:00:00');
            $end_date = strtotime($resp['end_date'] . ' 23:59:59');
            if (time() < $start_date || time() > $end_date) {
                $result['status'] = static::ERROR_CARD_DEACTIVATED;
            } else if (!$cur_obj->currencies[$resp['currency']]) {
                $result["status"] = static::ERROR_INVALID_CURRENCY;
            } else {
                $result["status"] = true;
            }
        } else {
            $result["status"] = true;
        }

        return $result;
    }

}