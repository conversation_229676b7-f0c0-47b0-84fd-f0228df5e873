<?php

namespace common\components\phone_verification_providers;

use common\components\GeneralComponent;
use common\models\CustomersOtp;
use common\models\CustomersSetting;
use Yii;

abstract class AbstractPhoneVerificationProvider {

    protected $merchant_id;
    protected $serviceName;
    
    protected $maxRequestPerDay = 10;
    protected $alertOnRequestPerDay = 5;
    protected $tokenValidityPeriod = 10; //In minutes
    protected $resendFrequency = 30; //In seconds
    protected $shouldVerifyPhone = true;

    const RESULT_OK = 'success',
            RESULT_ERROR = 'error',
            RESULT_NOT_MOBILE = 'not-mobile';

    function __construct($merchant_id, $serviceName = '') {
        $this->merchant_id = $merchant_id;
        $this->serviceName = $serviceName;
    }

    public function getServiceName() {
        return $this->serviceName;
    }

    public function getAlertOnRequestPerDay() {
        return $this->alertOnRequestPerDay;
    }

    public function getMaxRequestPerDay() {
        return $this->maxRequestPerDay;
    }

    public function shouldVerifyPhone() {
        return $this->shouldVerifyPhone;
    }

    /**
     * @return int the validity of the token, in minutes
     */
    public function getTokenValidityPeriod() {
        return $this->tokenValidityPeriod;
    }

    /**
     * @return int the resend frequency allowed, in seconds
     */
    public function getResendFrequency() {
        return $this->resendFrequency;
    }

    public abstract function sendToken($customer_id, $ip_address, $request_type, $phone_info, $request_url);

    public abstract function resendToken($customer_id, $ip_address, $request_type, $phone_info, $otp, $request_url);

    public abstract function validateTokenAnswer($customer_id, $ip_address, $answer, $request_type, $removeToken, $phone_info);

    public function invalidateToken($customer_id, $request_type) {
        $this->removeCustomerSecurityToken($request_type, $customer_id);
    }

    /**
     * @return token
     */
    public function regenerateTokenForEmail($customer_id, $request_type, $phone_info) {
        $offsetDate = 1 * 24 * 3600; //1 day offset

        $tokenVal = $this->generateToken(6);
        $this->updateCustomerSecurityToken($customer_id, $tokenVal, $request_type, $phone_info, $offsetDate);

        return $tokenVal;
    }

    protected function generateToken($no_of_pin) {
        $codeLength = 0;
        $pin = '';

        while ($codeLength < $no_of_pin) {
            $pin .= GeneralComponent::cpnRand(0, 9);
            $codeLength++;
        }

        return $pin;
    }

    protected function updateCustomerSecurityToken($customer_id, $token, $request_type, $phone_info, $date_offset = 0) {
        $modelCustomersOtp = new CustomersOtp();
        $tokenData = array('customers_id' => $customer_id,
            'customers_otp_type' => $request_type,
            'customers_otp_digit' => $token,
            'customers_match_data' => (isset($phone_info['country_international_dialing_code']) ? $phone_info['country_international_dialing_code'] : '') . (isset($phone_info['telephone_number']) ? $phone_info['telephone_number'] : ''),
        );

        $customerExists = $modelCustomersOtp->customerExists($customer_id, $request_type);

        if ($customerExists) {
            if (isset($date_offset)) {
                $tokenData += array(
                    'customers_otp_request_date' => date('Y-m-d H:i:s', time() + $date_offset),
                );
            }
            $modelCustomersOtp->updateCustomerOpt($tokenData, $customer_id, $request_type);
        } else {
            $tokenData += array(
                'customers_otp_request_date' => date('Y-m-d H:i:s', time() + (isset($date_offset) ? $date_offset : 0)),
            );
            $modelCustomersOtp->saveCustomerOpt($tokenData);
        }

        $attempts_token_type = $request_type . '_attempts';
        $modelCustomersSetting = CustomersSetting::model()->getTokenVerifyAttempt($customer_id, 0, $attempts_token_type);
        if (isset($modelCustomersSetting)) {
            $tokenData = array(
                'customers_id' => $customer_id,
                'customers_setting_key' => $attempts_token_type,
                'customers_setting_value' => 0,
                'updated_datetime' => date("Y-m-d H:i:s"),
                'created_datetime' => date("Y-m-d H:i:s"),
            );
            $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $customer_id, $attempts_token_type);
        }
    }

    /*
     * ---- Utility functions for subclasses
     */

    protected function getCustomerSecurityToken($request_type, $customer_id, $phone_info) {
        $correctToken = FALSE;

        $customerSecurityToken = CustomersOtp::model()->getCustomerSecurityToken($customer_id, $request_type, $this->tokenValidityPeriod, $phone_info === false ? $phone_info : ((isset($phone_info['country_international_dialing_code']) ? $phone_info['country_international_dialing_code'] : '') . (isset($phone_info['telephone_number']) ? $phone_info['telephone_number'] : '')));
        if (isset($customerSecurityToken)) {
            if ($customerSecurityToken->active_token == '1') {
                $correctToken = $customerSecurityToken->customers_otp_digit;
            } else {
                $correctToken = 0;
            }
        }

        return $correctToken;
    }

    protected function getSmsPurpose($request_type) {
        if ($request_type == 'one_time_pin') {
            return 'one_time_pin';
        } else if ($request_type == 'verify_phone_request') {
            return 'verify_phone';
        } else {
            return 'security_token';
        }
    }

    protected function removeCustomerSecurityToken($request_type, $customer_id) {
        CustomersOtp::model()->updateCustomerOpt(array(
            'customers_otp_digit' => '',
                ), $customer_id, $request_type);
        CustomersSetting::model()->updateTokenVerifyAttempt(array(
            'updated_datetime' => 0,
                ), $customer_id, $request_type);
    }

    protected function getMerchantName() {
        if (isset(Yii::$app->params['API_MERCHANT_INFO'][$this->merchant_id])) {
            return Yii::$app->params['API_MERCHANT_INFO'][$this->merchant_id]['STORE_OWNER_NAME'];
        } else {
            return Yii::$app->name;
        }
    }

}
