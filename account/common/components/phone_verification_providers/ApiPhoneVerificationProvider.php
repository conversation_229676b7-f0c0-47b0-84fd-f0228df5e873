<?php

namespace common\components\phone_verification_providers;

use common\models\LogPhoneVerificationApiCall;
use common\models\SmsReport;

abstract class ApiPhoneVerificationProvider extends AbstractPhoneVerificationProvider {
    
    protected abstract function requestApiSendToken($customer_id, $ip_address, $phone_info, $request_type);

    protected abstract function requestApiResendToken($customer_id, $ip_address, $phone_info, $key, $request_type);

    protected abstract function requestApiVerifyToken($customers_id, $ip_address, $phone_info, $key, $answer, $request_type);
    
    protected abstract function requestApiCancelToken($customers_id, $key, $request_type);
    
    public function sendToken($customer_id, $ip_address, $request_type, $phone_info, $request_url) {
        $modelSmsReport = SmsReport::model();
        
        $smsReportData = array(
            'customers_id' => $customer_id,
            'sms_request_type' => $this->getSmsPurpose($request_type),
            'sms_request_phone_number' => $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'],
            'sms_request_phone_country_id' => $phone_info['country_id'],
            'sms_request_page' => $request_url ? $request_url : (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''),
            'sms_provider' => $this->serviceName,
        );
        $modelSmsReport->saveSmsReport($smsReportData);
        
        $result = $this->requestApiSendToken($customer_id, $ip_address, $phone_info, $request_type);
        if (!empty($result['token'])) {
            $this->updateCustomerSecurityToken($customer_id, $result['token'], $request_type, $phone_info);
            $this->logRequest($customer_id, 'sendToken', $request_type, $result['token'], array(
                'merchant_id' => $this->merchant_id,
                'phone_info' => $phone_info,
                'test_token' => isset($result['test_token']) ? $result['test_token'] : null,
            ));
            $modelSmsReport->updateSmsReport($smsReportData);
        }
        return $result;
    }
    
    public function resendToken($customer_id, $ip_address, $request_type, $phone_info, $otp, $request_url) {
        // Get old token, if expired or not found, follow back original flow instead
        if (!isset($otp) || !$otp->active_token || strlen($otp->customers_otp_digit) == 6) {
            return $this->sendToken($customer_id, $ip_address, $request_type, $phone_info, $request_url);
        }
        $modelSmsReport = SmsReport::model();
        
        $smsReportData = array(
            'customers_id' => $customer_id,
            'sms_request_type' => $this->getSmsPurpose($request_type),
            'sms_request_phone_number' => $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'],
            'sms_request_phone_country_id' => $phone_info['country_id'],
            'sms_request_page' => $request_url ? $request_url : (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''),
            'sms_provider' => $this->serviceName,
        );
        $modelSmsReport->saveSmsReport($smsReportData);
        
        $result = $this->requestApiResendToken($customer_id, $ip_address, $phone_info, $otp->customers_otp_digit, $request_type);
        if (!empty($result['token'])) {
            if ($result['token'] != $otp->customers_otp_digit) {
                $this->updateCustomerSecurityToken($customer_id, $result['token'], $request_type, $phone_info);
            }
            $this->logRequest($customer_id, 'resendToken', $request_type, $result['token'], array(
                'merchant_id' => $this->merchant_id,
                'phone_info' => $phone_info,
                'test_token' => isset($result['test_token']) ? $result['test_token'] : null,
            ));
            $modelSmsReport->updateSmsReport($smsReportData);
        }
        return $result;
    }

    public function validateTokenAnswer($customer_id, $ip_address, $answer, $request_type, $removeToken, $phone_info) {
        $return = array(
            'error' => 1,
        );
        $error = 1;
        $answer = !empty($answer) ? strip_tags($answer) : '';
        $verify_with_api = false;
        
        $key = $this->getCustomerSecurityToken($request_type, $customer_id, $phone_info);
        if (!empty($key)) {
            if (strlen($key) == 6 && $key == $answer) {
                $error = 0;
            } else if (strlen($key) != 6 && $this->requestApiVerifyToken($customer_id, $ip_address, $phone_info, $key, $answer, $request_type)) {
                $error = 0;
                $verify_with_api = true;
            }
        }
        $this->logRequest($customer_id, 'validateTokenAnswer', $request_type, $key, array(
            'answer' => $answer,
            'remove' => $removeToken,
            'phone_info' => $phone_info,
            'success' => $error == 0,
        ));
        if ($error == 0) {
            if ($removeToken) {
                $this->removeCustomerSecurityToken($request_type, $customer_id);
            } else {
                // Update token to correct answer so that in case process fails, this token is still valid and we do not have to re-send a request to API provider
                $this->updateCustomerSecurityToken($customer_id, $answer, $request_type, $phone_info, null);
            }
            $return['error'] = 0;
            if (!$verify_with_api && !$this->shouldVerifyPhone) {
                $return['do_not_auto_verify_phone'] = true;
            }
        }
        
        return $return;
    }
    
    public function regenerateTokenForEmail($customer_id, $request_type, $phone_info) {
        $this->invalidateApiToken($customer_id, $request_type);
        $token = parent::regenerateTokenForEmail($customer_id, $request_type, $phone_info);
        $this->logRequest($customer_id, 'regenerateTokenForEmail', $request_type, $token, [
            'phone_info' => $phone_info,
        ]);
        return $token;
    }
    
    public function invalidateToken($customer_id, $request_type) {
        $this->invalidateApiToken($customer_id, $request_type);
        parent::invalidateToken($customer_id, $request_type);
    }
    
    protected function invalidateApiToken($customer_id, $request_type) {
        $key = $this->getCustomerSecurityToken($request_type, $customer_id, false);
        if (!empty($key)){
            $this->requestApiCancelToken($customer_id, $key, $request_type);
        }
    }
    
    protected function logRequest($customer_id, $action, $request_type, $token, $extras = array()) {
        $data = array(
            'provider' => $this->serviceName,
            'request_type' => $request_type,
            'customers_id' => $customer_id,
            'action' => $action,
            'token' => $token,
            'extra_data' => json_encode($extras),
            'created_date' => date("Y-m-d H:i:s"),
        );
        $model = new LogPhoneVerificationApiCall();
        $model->attributes = $data;
        $model->save();
    }
}
