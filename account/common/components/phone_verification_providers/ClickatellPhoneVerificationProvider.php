<?php

namespace common\components\phone_verification_providers;

use common\components\CurlComponent;

class ClickatellPhoneVerificationProvider extends SmsPhoneVerificationProvider {

    private $send_url;
    private $api_credential;
    private $unicode_msg;

    function __construct($serviceName) {
        parent::__construct($serviceName);
        $this->send_url = "http://api.clickatell.com/http/sendmsg";
        $this->api_credential = \Yii::$app->params['sms.clickatell.config'];
        $this->unicode_msg = FALSE;
    }

    public function sendSms($customer_id, $ip_address, $phone_info, $message, $purpose) {
        $mobile_no = $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'];
        
        $account = 'global';
        if (strpos($mobile_no, '1') === 0 || strpos($mobile_no, '52') === 0) {  // Use US Long Number Account for US, Canada and Mexico
            $account = 'us';
        }

        $post_array = array('api_id' => $this->api_credential[$account]['api_id'],
            'user' => $this->api_credential[$account]['send_id'],
            'password' => $this->api_credential[$account]['send_pwd'],
            'text' => $message,
            'to' => $mobile_no
        );

        if (isset($this->api_credential[$account]['extra']) && is_array($this->api_credential[$account]['extra'])) {
            $post_array = array_merge($post_array, $this->api_credential[$account]['extra']);
        }

        if ($this->unicode_msg === TRUE) {
            $post_array['unicode'] = '1';
        }

        $curl_obj = new CurlComponent();
        $result = $curl_obj->curlPost($this->send_url, $post_array);
        return true;
    }
}
