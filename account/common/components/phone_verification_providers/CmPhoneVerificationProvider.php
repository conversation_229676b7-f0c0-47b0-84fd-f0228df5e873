<?php

namespace common\components\phone_verification_providers;

use common\components\CurlComponent;
use common\models\Configuration;
use SimpleXMLElement;
use Yii;

class CmPhoneVerificationProvider extends SmsPhoneVerificationProvider {

    private $send_url;
    private $product_token;
    private $unicode_msg;

    function __construct($serviceName) {
        parent::__construct($serviceName);
        $this->send_url = "https://sgw01.cm.nl/gateway.ashx";
        $this->product_token = Configuration::model()->getConfigValue('SMS_CM_PRODUCT_TOKEN');
        $this->unicode_msg = false;
    }

    public function sendSms($customer_id, $ip_address, $phone_info, $message, $purpose) {
        $mobile_no = $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'];

        $xml = $this->buildMessageXml($mobile_no, $message);
        $curl_obj = new CurlComponent();
        $result = $curl_obj->curlPost($this->send_url, $xml);
        return true;
    }

    private function buildMessageXml($recipient, $message) {
        if (strpos($recipient, '+') !== 0) {
            $recipient = '+' . $recipient;
        }
        $xml = new SimpleXMLElement('<MESSAGES/>');

        $authentication = $xml->addChild('AUTHENTICATION');
        $authentication->addChild('PRODUCTTOKEN', $this->product_token);

        $msg = $xml->addChild('MSG');
        $msg->addChild('FROM', Yii::$app->name);
        $msg->addChild('TO', $recipient);
        if ($this->unicode_msg) {
            $msg->addChild('DCS', 8);
        }
        $len = mb_strlen($message);
        if (!$this->unicode_msg && $len >= 160) {
            $msg->addChild('MINIMUMNUMBEROFMESSAGEPARTS', 1);
            $msg->addChild('MAXIMUMNUMBEROFMESSAGEPARTS', ceil($len / 153));
        } else if ($this->unicode_msg && $len >= 70) {
            $msg->addChild('MINIMUMNUMBEROFMESSAGEPARTS', 1);
            $msg->addChild('MAXIMUMNUMBEROFMESSAGEPARTS', ceil($len / 70));
        }
        $msg->addChild('BODY', $message);

        return $xml->asXML();
    }

}
