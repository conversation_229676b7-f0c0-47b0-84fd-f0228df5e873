<?php

namespace common\components\phone_verification_providers;

use common\components\CurlComponent;
use common\models\Configuration;

class MobileacePhoneVerificationProvider extends SmsPhoneVerificationProvider {

    private $send_url;
    private $send_id;
    private $send_pwd;
    private $msg_type;

    function __construct($serviceName) {
        parent::__construct($serviceName);
        $this->send_url = "http://**************/bulksms/smsblast.asp";
        $this->send_id = Configuration::model()->getConfigValue('SMS_MOBILE_ACE_USERNAME');
        $this->send_pwd = Configuration::model()->getConfigValue('SMS_MOBILE_ACE_PASSWORD');
        $this->msg_type = '0';
    }

    public function sendSms($customer_id, $ip_address, $phone_info, $message, $purpose) {
        $mobile_no = $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'];

        $post_array = array(
            'user' => $this->send_id,
            'pass' => $this->send_pwd,
            'type' => $this->msg_type,
            'to' => $mobile_no,
            'text' => $message
        );

        $curl_obj = new CurlComponent();
        $result = $curl_obj->curlPost($this->send_url, $post_array);
        return true;
    }

}
