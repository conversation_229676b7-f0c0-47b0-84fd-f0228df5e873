<?php

namespace common\components\phone_verification_providers;

use common\components\CurlComponent;
use common\models\Configuration;
use Yii;

class NexmoPhoneVerificationProvider extends ApiPhoneVerificationProvider {

    private $baseUrl;
    private $key;
    private $secret;

    protected $shouldVerifyPhone = false;

    function __construct($merchant_id, $serviceName = '') {
        parent::__construct($merchant_id, $serviceName);
        $this->key = Configuration::model()->getConfigValue('SMS_NEXMO_API_KEY');
        $this->secret = Configuration::model()->getConfigValue('SMS_NEXMO_API_SECRET');
        $this->baseUrl = 'https://api.nexmo.com/';
    }

    protected function requestApiSendToken($customer_id, $ip_address, $phone_info, $request_type) {
        $merchant_name = $this->getMerchantName();
        $mobile_no = $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'];
        $response = $this->callApi('verify', array(
            'number' => $mobile_no,
            'brand' => $merchant_name,
            'code_length' => 6,
            'require_type' => 'Mobile',
            'avoid_voice_call' => 'false',
            'pin_expiry' => $this->getTokenValidityPeriod() * 60,
            'next_event_wait' => $this->getTokenValidityPeriod() * 60 / 5,
        ));
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            $status = $json['status'];
            if ($status == 0 && !empty($json['request_id'])) {
                return array(
                    'status' => static::RESULT_OK,
                    'token' => $json['request_id'],
                );
            } else if ($status == 7) {
                //Blacklisted Number...
            } else if ($status == 10 || $status == 1) {
                //Throttle...
                return array(
                    'status' => static::RESULT_ERROR,
                );
            } else if ($status == 18 || $status == 15 || ($status == 3 && $json['error_text'] == "Invalid value for param: number")) {
                //Not in require_type validation or not in supported network
                return array(
                    'status' => static::RESULT_NOT_MOBILE,
                );
            }
        }
        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('Nexmo api send token failed', json_encode([
            $merchant_name, $customer_id, $phone_info, $request_type
        ]) . ' - ' . $response);
        return array(
            'status' => static::RESULT_ERROR,
        );
    }

    protected function requestApiResendToken($customer_id, $ip_address, $phone_info, $key, $request_type) {
        $merchant_name = $this->getMerchantName();
        $response = $this->callApi('verify/control', array(
            'request_id' => $key,
            'cmd' => 'trigger_next_event',
        ));
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            switch ($json['status']) {
                //Good status
                case '0':
                // We resent as much as we can! Lets hope the SMS eventually arrives...
                case '19':
                    return array(
                        'status' => static::RESULT_OK,
                        'token' => $key,
                    );
                default:
            }
        }

        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('Nexmo api resend token failed', json_encode([
            $merchant_name, $customer_id, $phone_info, $key, $request_type
        ]) . ' - ' . $response);
        return array(
            'status' => static::RESULT_ERROR,
        );
    }

    protected function requestApiVerifyToken($customers_id, $ip_address, $phone_info, $key, $answer, $request_type) {
        $response = $this->callApi('verify/check', array(
            'request_id' => $key,
            'code' => $answer,
        ));
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            switch ($json['status']) {
                case '0':
                    return true;
                case '16':
                // Wrong code!
                case '17':
                    // Wrong code too many times!
                    return false;
                default:
            }
        }

        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('Nexmo api verify token failed', json_encode([
            $this->getMerchantName(), $customers_id, $key, $answer, $request_type
        ]) . ' - ' . $response);
        return false;
    }

    protected function requestApiCancelToken($customers_id, $key, $request_type) {
        $response = $this->callApi('verify/control', array(
            'request_id' => $key,
            'cmd' => 'cancel',
        ));
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            switch ($json['status']) {
                case '0':
                    return;
                case '19':
                // Nexmo doesn't allow to cancel this anymore...
                default:
            }
        }

        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('Nexmo api cancel token failed', json_encode([
            $this->getMerchantName(), $customers_id, $key, $request_type
        ]) . ' - ' . $response);
    }

    protected function callApi($action, $data) {
        $url = $this->baseUrl . $action . '/json';
        $params = array(
            'api_key' => $this->key,
            'api_secret' => $this->secret
                ) + $data;

        $curl_obj = new CurlComponent();
        return $curl_obj->curlPost($url, $params);
    }
}
