<?php

namespace common\components\phone_verification_providers;

use common\components\CurlComponent;
use common\models\Configuration;
use Yii;

class PipwavePhoneVerificationProvider extends ApiPhoneVerificationProvider {

    private $url;
    private $api_key;
    private $api_secret;

    protected $shouldVerifyPhone = false;

    function __construct($merchant_id, $serviceName = '') {
        parent::__construct($merchant_id, $serviceName);
        $this->url = \Yii::$app->params['pipwave.api_url'] . 'one-time-pin';
        $config = \Yii::$app->params['pipwave.config'];
        $this->api_key = $config['api_key'];
        $this->api_secret = $config['api_secret'];
    }

    protected function requestApiSendToken($customer_id, $ip_address, $phone_info, $request_type) {
        $response = $this->callApi([
            'action' => 'send-token',
            'user_id' => '' . $customer_id,
            'contact_no_country_dialing_code' => '' . $phone_info['country_international_dialing_code'],
            'contact_no' => '' . $phone_info['telephone_number'],
            'ip_address' => $ip_address,
        ], ['user_id', 'contact_no_country_dialing_code', 'contact_no']);
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            $status = $json['status'];
            if ($status == '200' && !empty($json['reference'])) {
                $return = array(
                    'status' => static::RESULT_OK,
                    'token' => $json['reference'],
                );
                if (!empty($json['test_token'])) {
                    $return['test_token'] = $json['test_token'];
                }
                return $return;
            } else{
                switch ($status) {
                    case '5001':
                        //Not in require_type validation or not in supported network
                        return array(
                            'status' => static::RESULT_NOT_MOBILE,
                        );
                    case '5002':
                        //Throttle due to too frequent resend button being clicked...
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5003':
                        //Concurrent verification
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5004':
                        //Throttle due to too frequent send token request...
                        $this->reportToAdmin('throttle', $customer_id, $phone_info);
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5005':
                        //too many resend request
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5006':
                        //Blacklisted number
                        $this->reportToAdmin('blacklist', $customer_id, $phone_info);
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5021':
                        \Yii::$app->reporter->reportToAdminViaSlack('Insufficient credit to call phone verification API', '');
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5023':
                        \Yii::$app->reporter->reportToAdminViaSlack('Phone verification service off at Merchant portal', '');
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                    case '5022':
                        \Yii::$app->reporter->reportToAdminViaSlack('Phone verification service off at pipwave level', '');
                        return array(
                            'status' => static::RESULT_ERROR,
                        );
                }
            }
        }
        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('pipwave api send token failed', json_encode([
            $this->getMerchantName(), $customer_id, $phone_info, $request_type
        ]) . ' - ' . $response);
        return array(
            'status' => static::RESULT_ERROR,
        );
    }

    protected function requestApiResendToken($customer_id, $ip_address, $phone_info, $key, $request_type) {
        return $this->requestApiSendToken($customer_id, $ip_address, $phone_info, $request_type);
    }

    protected function requestApiVerifyToken($customer_id, $ip_address, $phone_info, $key, $answer, $request_type) {
        $response = $this->callApi([
            'action' => 'verify-token-answer',
            'user_id' => '' . $customer_id,
            'contact_no_country_dialing_code' => '' . $phone_info['country_international_dialing_code'],
            'contact_no' => '' . $phone_info['telephone_number'],
            'ip_address' => $ip_address,
            'reference' => $key,
            'answer' => $answer,
        ], ['user_id', 'reference', 'contact_no_country_dialing_code', 'contact_no', 'answer']);
        $json = json_decode($response, true);
        if (isset($json['status'])) {
            switch ($json['status']) {
                case '200':
                    return true;
                case '5051':
                    //Expired
                case '5052':
                    // Wrong code
                    return false;
                default:
            }
        }

        // If arrive here, that means error!
        \Yii::$app->reporter->reportToAdminViaSlack('pipwave api verify token failed', json_encode([
            $this->getMerchantName(), $customer_id, $key, $answer, $request_type
        ]) . ' - ' . $response);
        return false;
    }

    protected function requestApiCancelToken($customers_id, $key, $request_type) {
        //Not implemented
    }

    protected function callApi($data, $signature_data) {
        $data = array_merge($data, [
            'api_key' => $this->api_key,
            'timestamp' => time(),
        ]);
        $data['signature'] = $this->generate_signature($data, array_merge($signature_data, ['timestamp', 'action', 'api_key', 'api_secret']));
        
        $curl_obj = new CurlComponent();
        $curl_obj->connect_via_proxy = false;
        $curl_obj->request_headers = [
            'x-api-key' => $this->api_key,
        ];
        return $curl_obj->curlPost($this->url, json_encode($data), '', false);
    }
    
    protected function generate_signature($array, $signature_data) {
        $array['api_secret'] = $this->api_secret;
        ksort($array);
        $s = "";
        foreach($array as $key => $value) {
            if (in_array($key, $signature_data)) {
                $s .= $key . ':' . $value;
            }
        }
        return sha1($s);
    }
    
    protected function reportToAdmin($type, $customer_id, $phone_info) {
        $cache_key = 'pipwavePhoneVerificationProvider/customer/' . $this->merchant_id . '/' .$customer_id.'/' . $type . '/alert';
        $result = \Yii::$app->cache->get($cache_key);
        if ($result === false) {
            if ($type == 'throttle') {
                $message = 'Throttle request. Merchant: %s, User: %s, Phone: %s %s';
            } else {
                $message = 'Number blacklisted for verification. Merchant: %s, User: %s, Phone: %s %s';
            }
            \Yii::$app->reporter->reportToAdminViaSlack(sprintf($message, $this->merchant_id, $customer_id, $phone_info['country_international_dialing_code'], $phone_info['telephone_number']), '');
            \Yii::$app->cache->set($cache_key, '1', 30 * 60); //Temp cache so that our slack does not get bombarded
        }
    }
}
