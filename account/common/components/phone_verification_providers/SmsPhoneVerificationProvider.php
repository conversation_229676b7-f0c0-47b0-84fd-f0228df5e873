<?php

namespace common\components\phone_verification_providers;

use common\models\SmsReport;

abstract class SmsPhoneVerificationProvider extends AbstractPhoneVerificationProvider {
    
    protected $maxRequestPerDay = 3;
    protected $alertOnRequestPerDay = 3;
    
    public abstract function sendSms($customer_id, $ip_address, $phone_info, $message, $request_type);
    
    public function sendToken($customer_id, $ip_address, $request_type, $phone_info, $request_url) {
        // Generate token and insert into OTP table for verification purpose
        $tokenVal = $this->generateToken(6);
        return $this->sendTokenProcess($customer_id, $ip_address, $tokenVal, $request_type, $phone_info, $request_url);
    }
    
    public function resendToken($customer_id, $ip_address, $request_type, $phone_info, $otp, $request_url) {
        // Get old token, if expired or not found, follow back original flow instead
        if (!isset($otp) || !$otp->active_token) {
            return $this->sendToken($customer_id, $ip_address, $request_type, $phone_info, $request_url);
        } else {
            return $this->sendTokenProcess($customer_id, $ip_address, $otp->customers_otp_digit, $request_type, $phone_info, $request_url);
        }
    }
    
    protected function sendTokenProcess($customer_id, $ip_address, $token, $request_type, $phone_info, $request_url) {
        $modelSmsReport = SmsReport::model();
        
        $smsReportData = array(
            'customers_id' => $customer_id,
            'sms_request_type' => $this->getSmsPurpose($request_type),
            'sms_request_phone_number' => $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'],
            'sms_request_phone_country_id' => $phone_info['country_id'],
            'sms_request_page' => $request_url ? $request_url : (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''),
            'sms_provider' => $this->serviceName,
        );
        $modelSmsReport->saveSmsReport($smsReportData);
        
        $message = 'From ' . $this->getMerchantName() . '. Your security token: ' . $token . '. Validity 10 minutes from the time you receive this SMS. TQ';
        
        if ($this->sendSms($customer_id, $ip_address, $phone_info, $message, $request_type)) {
            $modelSmsReport->updateSmsReport($smsReportData);
            $this->updateCustomerSecurityToken($customer_id, $token, $request_type, $phone_info);
            return array(
                'status' => static::RESULT_OK,
                'token' => $token,
            );
        }
        return array(
            'status' => static::RESULT_ERROR,
        );
    }
    
    public function validateTokenAnswer($customer_id, $ip_address, $answer, $request_type, $removeToken, $phone_info) {
        $error = 1;
        $answer = !empty($answer) ? strip_tags($answer) : '';

        $challengeToken = $this->getCustomerSecurityToken($request_type, $customer_id, $phone_info);

        if ($challengeToken !== FALSE && $challengeToken != 0 && $answer == $challengeToken) {
            $error = 0;
            if ($removeToken) {
                $this->removeCustomerSecurityToken($request_type, $customer_id);
            }
        }
        
        return array('error' => $error);
    }
}