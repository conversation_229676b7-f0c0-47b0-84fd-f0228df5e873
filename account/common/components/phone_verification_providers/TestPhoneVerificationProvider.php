<?php

namespace common\components\phone_verification_providers;

class TestPhoneVerificationProvider extends SmsPhoneVerificationProvider {
    
    public function sendTokenProcess($customer_id, $ip_address, $token, $request_type, $phone_info, $request_url) {
        $test_request_url = '(Test) ' . ($request_url ? $request_url : (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''));
        return parent::sendTokenProcess($customer_id,$ip_address, $token, $request_type, $phone_info, $test_request_url);
    }
    
    public function sendSms($customer_id, $ip_address, $phone_info, $message, $purpose) {
        return true;
    }
}