<?php
Yii::set<PERSON><PERSON><PERSON>('@common', dirname(__DIR__));
Yii::set<PERSON><PERSON><PERSON>('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::set<PERSON>lias('@api', dirname(dirname(__DIR__)) . '/api');
Yii::set<PERSON>lia<PERSON>('@console', dirname(dirname(__DIR__)) . '/console');

//Yii1: Overwrite string validator to allow string and number to be validated as per yii1
\yii\validators\Validator::$builtInValidators['string'] = '\common\validators\StringValidator';