<?php

$params = \yii\helpers\ArrayHelper::merge(
        require(__DIR__ . '/params.php'),
        require(__DIR__ . '/params-encoded.php'),
        require(__DIR__ . '/params-local.php')
);

return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'language' => 'en',
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'components' => [
        'jwt' => [
            'class' => 'sizeg\jwt\Jwt',
        ],
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'latin1',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800,
        ],
        'dbshasso' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['dbshasso.dsn'],
            'username' => $params['dbshasso.username'],
            'password' => $params['dbshasso.password'],
            'charset' => 'utf8',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800,
        ],
        'dbwebpush' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['dbwebpush.dsn'],
            'username' => $params['dbwebpush.username'],
            'password' => $params['dbwebpush.password'],
            'charset' => 'utf8',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800,
        ],
        'dbwebpushmb4' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['dbwebpush.dsn'],
            'username' => $params['dbwebpush.username'],
            'password' => $params['dbwebpush.password'],
            'charset' => 'utf8mb4',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800,
        ],
        'db.og' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.og.dsn'],
            'username' => $params['db.og.username'],
            'password' => $params['db.og.password'],
            'charset' => 'utf8mb4',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800,
        ],
        'readSession' => [
            'keyPrefix' => 'auth-session/',
            'class' => '\frontend\components\RedisSession',
            'name' => 'OGMAUTHV1',
        ],
        'cache' => [
            'class' => 'yii\caching\MemCache',
            'keyPrefix' => "shasso/",
            'useMemcached' => true,
            'servers' => [
            ],
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@common/messages',
                ],
            ],
        ],
        // extension
        'geoip' => [
            'class' => 'common\components\MaxmindGeoIP',
            'countryDbPath' => '@common/components/db/GeoLite2-Country.mmdb'
        ],
        'reporter' => [
            'class' => 'common\components\Reporter',
        ],
        'slack' => [
            'class' => 'common\components\Slack',
            'id' => $params['slack.id'],
            'url' => $params['slack.url'],
            'username' => $params['slack.username'],
            'icon' => $params['slack.icon_url'],
            'proxy' => $params['USE_PROXY'] && !empty($params['PROXY_HOST']) ? $params['PROXY_HOST'] . ':' . $params['PROXY_PORT'] : '',
        ],
    ],
];
