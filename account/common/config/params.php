<?php
return [
    'supportEmail' => '<EMAIL>',
    'user.passwordResetTokenExpire' => 3600,
    # Slack configuration
    'slack.url' => 'https://hooks.slack.com/services/',
    // Default Slack Bot Username
    'slack.username' => 'Shasso',
    // Must be square!
    'slack.icon_url' => '',
    //
    # Currency denominations
    'currency_denominations' => require('currency-denominations.php'),
    //
    # General configs
    "SITE_ID" => 0,
    'B2C_SITE_ID' => 0,
    'C2C_SITE_ID' => 5,
    'C2C_DISPLAY_IM' => '4, 6, 8, 9, 10, 11, 12',
    'SHASSO_LOGO'=>'https://static.shasso.com/email-assets/',
    //
    # Memcache
    'MEMCACHE_PREFIX' => 'shasso/',
    //
    # Image
    'SECURE_CIPHER' => 'rijndael-128',
    //
    # cookie domain
    'DEVICE_TOKEN_COOKIE_DURATION' => 60 * 60 * 24 * 180,
    //
    # pagination
    "GENERAL_CONFIG" => [
        "PAGER_ITEM_PER_PAGE" => 20, // pagination
        "PAGER_MAX_BUTTON" => 5, // pagination
    ],
    //
    # reCaptcha
    "RECAPTCHA_CONFIG" => [
        'RECAPTCHA_API_SERVER' => 'http://www.google.com/recaptcha/api/siteverify',
        'RECAPTCHA_API_SECURE_SERVER' => 'https://www.google.com/recaptcha/api/siteverify',
    ],
    //
    # Geetest
    "GEETEST_CONFIG" => [
        'GEETEST_API_SERVER' => 'http://api.geetest.com',
        'GEETEST_API_SECURE_SERVER' => 'https://api.geetest.com',
    ],
    //
    # regional
    "REG_CONFIG" => [
        "DEF_COUNTRY_ID" => 223,
        "DEF_LANGUAGE_ID" => 1,
        "DEF_COUNTRY_CODE" => "MY",
        "DEF_CURRENCY_CODE" => "USD",
        "DEF_LANGUAGE_CODE" => "en",
        "DEF_LANGUAGE_NAME" => "English",
    ],
    //
    # single sign-on
    "SSO_CONFIG" => [
        "FORGET_EMAIL_TRIAL" => 3,
        "FORGET_EMAIL_LIFETIME" => 86400, // 24 hr
        "LANG_SUPPORT" => ['en', 'id', 'zh-CN', 'zh-TW'],
        "LOGIN_SAME_IP" => 20, //Cache-powered IP check for unsuccessful login, hit this number, lock out for captcha
        "LOGIN_SAME_IP_LIFETIME" => 1800, //Cache lasts for 3 minutes
        "LOGIN_TRIAL" => 3,
        "SIGNUP_INLINE_CAPTCHA" => false,
        "RECLAIM_EMAIL_TRIAL" => 3,
    ],
    //
    # verification submission form
    "VERIFICATION_DOC" => [
        "ENCRYPT_FILE" => true,
        "FILE_MAXSIZE" => 8192, // KB
        "FILE_TYPE" => ["jpg", "jpeg", "gif", "png","pdf"],
        "S3_BUCKET" => "BUCKET_UPLOAD",
        "S3_DESTINATION" => "user/verification/",
        "UPLOAD_DESTINATION" => "@runtime/uploads/", // leave empty if not support local upload
    ],
    //
    "GIFT_CARD" => [
        "TRIAL" => 3,
        "PRODUCT" => [
            "ogc" => [
                "name" => "OffGamers Gift Card",
                "class" => "\common\components\gift_cards\OffgamersGiftCard",
                "purchase_link" => "https://www.offgamers.com/search/index.htm?keyword=ogc",
            ],
        ],
    ],
    'API_MERCHANT_INFO' => [
        'ogm' => [
            'STORE_OWNER_NAME' => 'OffGamers',
        ],
    ],
    'SMS_DEFAULT_SERVICE' => 'pipwave', //nexmo, cm, clickatell, mobileace, pipwave
    'BLOCKED_COUNTRY' => ['IR','CU','KP','CG','LY','SO','YE','SD'],// blocked country list
    'EMAIL_CHANGE_PER_DAY' => 3,
    'support_url' => 'https://helpdesk.offgamers.com/support/solutions',
    'pre_login_support_url' => 'https://helpdesk.offgamers.com/support/solutions',
    'brand_list' => [
        'og'=>'og',
    ],
    # Session config
    'session.provider' => 'mysql',
    //
    # Country clear credits
    'country_clear_credits' => ['MY', 'ID'],
    //
    # Freshdesk API
    'FRESHDESK_SUBJECT_PREFIX' => '',
    'FRESHDESK_EXTERNAL_ID_PREFIX' => '',
    "FD_MAIL_RESEND_TIME" => 60*60*3,
    'FRESHDESK_API' => [
        'TICKET_OG' => [
            'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
            'enabled' => true,
        ],
    ],
    //
    # Email country change
    'email_country_change.limit_timeframe' => '60 DAY', //MYSQL syntax, see DATE_SUB function
    //
];