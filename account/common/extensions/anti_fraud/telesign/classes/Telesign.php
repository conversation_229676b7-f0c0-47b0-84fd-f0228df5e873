<?php

class Telesign {

    private $custID;
    private $authID;
    var $delayTime = 0;
    var $redialCount = 1;
    private $uri = "https://api.telesign.com/1.x/soap.asmx";
    public $countryCode;
    private $countryMapper;
    public $phoneNumber;
    public $randCode;
    public $phoneNum;
    public $extContent = null;
    public $extType = null;
    private $pinLength = 4;
    public $error;
    public $callinfo = array();
    public $phoneinfo = array();
    public $ret;
    private $curTag;
    private $xml_parser;
    private $caret_pos;
    public $flag;
    public $debugging = false;
    public $validateRequestSource = false;
    public $errors = array();
    public $language = null;
    public $messageText = null;
    public $mailAdress;
    public static $suportedLanguages = array('arabic' => 'Arabic',
        'bulgarian' => 'Bulgarian',
        'cantonese' => 'Cantonese',
        'catalan' => 'Catalan',
        'chinese' => 'Chinese',
        'croatian' => 'Croatian',
        'czech' => 'Czech',
        'danish' => 'Danish',
        'dutch' => 'Dutch',
        'egyptian' => 'Egyptian',
        'english' => 'English (American)',
        'australian' => 'English (Australian)',
        'englishuk' => 'English (UK)',
        'estonian' => 'Estonian',
        'filipino' => 'Filipino',
        'finnish' => 'Finnish',
        'canadianfrench' => 'French (Canadian)',
        'french' => 'French',
        'german' => 'German',
        'greek' => 'Greek',
        'hebrew' => 'Hebrew',
        'hindi' => 'Hindi',
        'hungarian' => 'Hungarian',
        'icelandic' => 'Icelandic',
        'indonesian' => 'Indonesian',
        'italian' => 'Italian',
        'japanese' => 'Japanese',
        'korean' => 'Korean',
        'latvian' => 'Latvian',
        'lingala' => 'Lingala',
        'lithuanian' => 'Lithuanian',
        'norwegian' => 'Norwegian',
        'polish' => 'Polish',
        'portuguese' => 'Portuguese (Brazilian)',
        'portugueseeu' => 'Portuguese (EU)',
        'romanian' => 'Romanian',
        'russian' => 'Russian',
        'slovak' => 'Slovak',
        'spanisheu' => 'Spanish (EU)',
        'spanish' => 'Spanish (Latin American)',
        'swedish' => 'Swedish',
        'thai' => 'Thai',
        'turkish' => 'Turkish',
        'ukrainian' => 'Ukranian',
        'vietnamese' => 'Vietnamese'
    );

    /**
     * Constructor of Telesign Class. Unsets session debuging vars.
     *
     * @param void
     * @return void
     */
    function Telesign() {
        if (isset($_SESSION['ts-class']['lastRequest'])) {
            unset($_SESSION['ts-class']['lastRequest']);
        }
        if (isset($_SESSION['ts-class']['lastResponse'])) {
            unset($_SESSION['ts-class']['lastResponse']);
        }
    }

    /**
     * Switch debugging on/off. In debug mode XML requests and responses will be printed.
     *
     * @param boolean $on
     * @return void
     */
    function setDebugging($on) {
        $this->debugging = $on;
    }

    /**
     * Switch validating request source on/off
     *
     * @param boolean $on
     * @return void
     */
    function validateRequestSource($on) {
        $this->validateRequestSource = $on;
    }

    /**
     * Returns error messages for errors which occurred during the request.
     *
     * @return array list of error messages
     */
    function getErrors() {
        $errors = $this->errors;
        if (isset($this->callinfo['ErrorMessage']))
            array_push($errors, array('message' => $this->callinfo['ErrorMessage'], 'code' => $this->callinfo['ErrorCode']));
        return $errors;
    }

    /**
     * Check for specyfic error which occurred during the request.
     * @param integer $errorID 
     * @return bool
     */
    function hasError($errorID) {
        // -20001 invalid CustomerID
        // -20001 invalid AuthID
        // -10001 invalid country code
        // -10001 invalid phone number
        // -25 County mapper could not be loaded 
        // -30 Country mapper not registerd 
        // -35 Failed initializing cURL handle
        // -40 Invalid request source
        // -45 Requests limit exceeded
        // 
        // telesign errors from -10001 to -90001
        // -10001 API Error, invalid Contry Code / PhoneNumber
        // -20001 API Error, invalid Customer / Auth ID 

        $errors = $this->errors;
        foreach ($errors as $k) {
            if ($k['code'] == $errorID)
                return true;
        }
        return false;
    }

    /**
     * Return list of known passible errors.
     *
     * @return array
     */
    function getPassibleErrors() {
        $passibleErrors = array(-20001, // CustomerID/AuthentificationID does not exist
            -30001, // Account suspended
            -40001, // 
            -60001, // 
            -70001, // 
            -80001, // System unavailable; try again later
            -90001,); // System unavailable; try again later
        return $passibleErrors;
    }

    /**
     * Return true, when error code doesn't apear in passibleErrors array
     *
     * @return bool
     */
    function hasOtherErrors() {
        $passibleErrors = $this->getPassibleErrors();
        $errors = $this->getErrors();
        $flag = true;
        foreach ($errors as $k => $v) {
            if (in_array($v['code'], $passibleErrors)) {
                $flag = false;
            }
        }
        return $flag;
    }

    /**
     * Clear errors for subsequent verification requests. This needs to be called before
     * requesting a subsequent verification.
     *
     * @return void
     */
    function clearErrors() {
        $this->errors = array();
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    function setCountryCode($code) {
        $this->countryCode = $this->sanitizePhoneNumber($code);
    }

    /**
     * Set the language of phone call
     *
     * @param string $language
     * @return bool
     */
    function setLanguage($language) {
        $language = strtolower($language);

        if (array_key_exists($language, self::$suportedLanguages)) {
            $this->language = self::$suportedLanguages[$language];
            return true;
        } else {
            $this->language = ''; // (default english)
            return false;
        }
    }

    /**
     * Return the language if is available in other case return 'english'
     *
     * @param string $language
     * @return string
     */
    function getLanguage() {
        if (isset($this->language)) {
            return $this->language;
        } else {
            return '';  //(default english) 
        }
    }

    /**
     * Set the text message where %s is pin code
     *
     * @param string $message, $code=''
     * @return void
     */
    function setMessageText($message, $code='') {
        $message = str_replace(array('%s', ']]>'), array($code, ''), $message);
        $message = substr($message, 0, 159);
        $this->messageText = '<![CDATA[' . $message . ']]>';
    }

    /**
     * Get sms text
     *
     * @param void
     * @return string
     */
    function getMessageText() {
        return $this->messageText;
    }

    /**
     * Return the country code actually used in the request. Might be different to
     * what was initially set by the user due to sanitization.
     *
     * @return integer sanitized country code
     */
    function getCountryCode() {
        return $this->countryCode;
    }

    /**
     * Return the phone number actually used in the request. Might be different to
     * what was initially set by the user due to sanitization.
     *
     * @return string sanitized phone number
     */
    function getPhoneNumber() {
        return $this->phoneNumber;
    }

    /**
     * Register a country mapper class that will resolve country ids to country codes.
     *
     * @param string $name
     * @return boolean
     */
    function setCountryMapper($name) {
        // Validate file name
        if (!preg_match("/[a-z0-9_-]/i", $name)) {
            return false;
        }

        $fileName = dirname(__FILE__) . '/CountryMapper/' . $name . ".php";
        if (file_exists($fileName)) {
            include $fileName;
            $this->countryMapper = 'Telesign_CountryMapper_' . $name;
            return true;
        } else {
            //$this->errors[] = "County mapper could not be loaded";
            array_push($this->errors, array('message' => 'County mapper could not be loaded', 'code' => -25));
            return false;
        }
    }

    /**
     * Set the country code utilizing a country mapper
     *
     * @param string $countryId
     * @return boolean
     */
    function setCountryId($countryId) {
        if (!$this->countryMapper) {
            //$this->errors[] = "Country mapper not registerd";
            array_push($this->errors, array('message' => 'Country mapper not registerd', 'code' => -30));
            return false;
        }

        $cm = new $this->countryMapper;
        $this->countryCode = $cm->getCountryCode($countryId);
    }

    /**
     * Set the Customer ID
     *
     * @param string $CustomerID
     * @return void
     */
    function setCustomerID($CustomerID) {
        $this->custID = $CustomerID;
    }

    /**
     * Set the Authentication ID
     *
     * @param string $AuthenticationID
     * @return void
     */
    function setAuthenticationID($AuthenticationID) {
        $this->authID = $AuthenticationID;
    }

    /**
     * Configure the number of digits in the PIN
     *
     * @param integer $pinL
     * @return void
     */
    function setPinLength($pinLength) {
        $this->pinLength = $pinLength;
    }

    /**
     * Define a custom PIN
     *
     * @param string $pin
     * @return void
     */
    function setPin($pin) {
        $this->randCode = $pin;
        $_SESSION['pin'] = $pin;
    }

    /**
     * Set the delay in seconds before the call is initiated
     *
     * @param integer $delay
     */
    function setDelay($delay) {
        $this->delayTime = $delay;
    }

    /**
     * Set the number of times to try to redial in case of unanswered / disconnected calls
     *
     * @param integer $redialCount
     * @return void
     */
    function setRedialCount($redialCount) {
        $this->redialCount = $redialCount;
    }

    /**
     * Compare PIN submitted by user with the one originally sent out
     *
     * @param string $subPin
     * @return boolean pin comparison result
     */
    function checkPin($subPin) {
        return isset($_SESSION['pin']) ? $subPin == $_SESSION['pin'] : false;
    }

    /**
     * Generate a random PIN based on specified length
     *
     * @return void
     */
    function generatePin() {
        $low = $this->pinLength == 1 ? 0 : pow(10, $this->pinLength - 1);
        $high = pow(10, $this->pinLength) - 1;
        $this->randCode = rand($low, $high);
        $_SESSION['pin'] = $this->randCode;
    }

    /**
     * Return PIN if available
     *
     * @return string pin number
     */
    function getPin() {
        return isset($_SESSION['pin']) ? $_SESSION['pin'] : false;
    }

    /**
     * Check if phone number is a high risk level number
     *
     * @param string $phoneNum optional phone number
     * @param string $countryCode optional country code
     * @return boolean
     */
    function isVOIP($phoneNum = null, $countryCode = null) {
        $allowed = array(301, 302, 310);

        if (!empty($phoneNum)) {
            $this->requestPhoneID($phoneNum, $countryCode);
        }

        if (isset($this->phoneinfo['TYPEOFPHONE']) && !in_array($this->phoneinfo['TYPEOFPHONE'], $allowed)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Request information about a phone number - proxy for ::request()
     *
     * @param string $phoneNum phone number
     * @param string $countryCode optional country code
     * @return boolean request status
     */
    function requestPhoneID($phoneNum, $countryCode = null) {
        return $this->request($phoneNum, $countryCode, 'phoneid');
    }

    /**
     * Request phone verfication - proxy for ::request()
     *
     * @param string $phoneNum phone number
     * @param string $countryCode optional country code
     * @return boolean request status
     */
    function requestCall($phoneNum, $countryCode = null) {
        return $this->request($phoneNum, $countryCode, 'call');
    }

    /**
     * Request sms verification - proxy for ::request()
     *
     * @param string $phoneNum phone number
     * @param string $countryCode optional country code
     * @return boolean request status
     */
    function requestSms($phoneNum, $countryCode = null) {
        return $this->request($phoneNum, $countryCode, 'sms');
    }

    /**
     * Request phone or sms verification
     *
     * @param string $phoneNum phone number
     * @param string $countryCode optional country code
     * @param string $type call / sms
     * @return boolean request status
     */
    function request($phoneNum, $countryCode = null, $type) {
        $this->phoneNumber = $this->sanitizePhoneNumber($phoneNum);

        if (empty($this->randCode)) {
            $this->generatePin();
        }

        if (!empty($countryCode)) {
            $this->countryCode = $this->sanitizePhoneNumber($countryCode);
        }

        $this->validateParameters($this->phoneNumber, $this->countryCode, $type);

        if ($this->validateRequestSource) {
            $parse_url = parse_url($_SERVER['HTTP_REFERER']);
            if (!empty($_SERVER['HTTP_REFERER']) && ($_SERVER['HTTP_HOST'] != $parse_url['host'])) {
                array_push($this->errors, array('message' => 'Invalid request source', 'code' => -40));
            }
        }

        // Evacuate in case of errors
        if (!empty($this->errors)) {
            return false;
        }

        if ($type == 'call') {

            $this->flag = "call";
            $this->ret = $this->apiRequestCALL($this->uri, $this->custID, $this->authID, $this->countryCode, $this->phoneNumber, $this->randCode, $this->delayTime, $this->redialCount, $this->extContent, $this->extType);
        } elseif ($type == 'sms') {

            $this->flag = "sms";

            $this->ret = $this->apiRequestSMS($this->uri, $this->custID, $this->authID, $this->countryCode, $this->phoneNumber, $this->randCode);
        } elseif ($type == 'phoneid') {

            $this->flag = 'phoneid';
            $this->ret = $this->apiRequestPhoneID($this->uri, $this->custID, $this->authID, $this->countryCode, $this->phoneNumber);
        }

        if ($this->debugging) {
            $this->echoXml($this->ret);
        }

        $this->parseResponseXml();

        // Return request status
        return empty($this->errors);
    }

    /**
     * Send the call request to Telesign through SOAP using cUrl
     *
     * @param string $uri
     * @param string $custID
     * @param string $authID
     * @param integer $countryCode
     * @param integer $phoneNum
     * @param string $randCode
     * @param integer $delayTime
     * @param integer $redialCount
     * @param integer $extContent
     * @param string $extType
     * @return mixed response string or false on failure
     */
    function apiRequestCALL($uri, $custID, $authID, $countryCode, $phoneNum, $randCode, $delayTime, $redialCount, $extContent, $extType) {
        $ch = curl_init();
        if (!$ch) {
            //$this->errors[] = "Failed initializing cURL handle";
            array_push($this->errors, array('message' => 'Failed initializing cURL handle', 'code' => -30));
            return false;
        }

        //STORE THE SOAP IN THE STRPOST VARIABLE
        $strPost = '<?xml version="1.0" encoding="utf-8"?>';
        $strPost .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        $strPost .= 'xmlns:xsd="http://www.w3.org/2001/XMLSchema" ';
        $strPost .= 'xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
        $strPost .= '<soap:Body>';
        $strPost .= '<RequestCALL xmlns="https://www.telesign.com/api/">';
        $strPost .= '<CustomerID>' . $custID . '</CustomerID>';
        $strPost .= '<AuthenticationID>' . $authID . '</AuthenticationID>';
        $strPost .= '<CountryCode>' . $countryCode . '</CountryCode>';
        $strPost .= '<PhoneNumber>' . $phoneNum . '</PhoneNumber>';
        $strPost .= '<Message>' . $this->getLanguage() . '</Message>';
        $strPost .= '<VerificationCode>' . $randCode . '</VerificationCode>';
        $strPost .= '<DelayTime>' . $delayTime . '</DelayTime>';
        $strPost .= '<RedialCount>' . $redialCount . '</RedialCount>';
        $strPost .= '<ExtensionContent>' . $extContent . '</ExtensionContent>';
        $strPost .= '<ExtensionType>' . $extType . '</ExtensionType>';
        $strPost .= '</RequestCALL>';
        $strPost .= '</soap:Body>';
        $strPost .= '</soap:Envelope>';
        $_SESSION['ts-class']['lastRequest'] = $strPost;

        if ($this->debugging) {
            $this->echoXml($strPost);
        }

        //STORE THE HEADER INFORMATION IN THE HEADER ARRAY
        $header[] = 'Content-type: text/xml; charset=utf-8';
        $header[] = 'SOAPAction: https://www.telesign.com/api/RequestCALL';

        //SET THE PHP CURL OPTIONS, INCLUDING THE HEADER AND THE VARIABLE TO POST
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $strPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        if (Yii::$app->params['USE_PROXY']) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $ret = curl_exec($ch);
        curl_close($ch);
        $_SESSION['ts-class']['lastResponse'] = $ret;

        return $ret;
    }

    /**
     * Send the sms request to Telesign through SOAP using cUrl
     *
     * @param string $uri
     * @param string $custID
     * @param string $authID
     * @param integer $countryCode
     * @param integer $phoneNum
     * @param string $randCode
     * @return mixed response string or false on failure
     */
    function apiRequestSMS($uri, $custID, $authID, $countryCode, $phoneNum, $randCode) {
        $ch = curl_init();
        if (!$ch) {
            //$this->errors[] = "Failed initializing cURL handle";
            array_push($this->errors, array('message' => 'Failed initializing cURL handle', 'code' => -35));
            return false;
        }

        //STORE THE SOAP IN THE STRPOST VARIABLE
        $strPost = "";
        $strPost .= '<?xml version="1.0" encoding="utf-8"?>';
        $strPost .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
        $strPost .= '<soap:Body>';
        $strPost .= '<RequestSMS xmlns="https://www.telesign.com/api/">';
        $strPost .= '<CustomerID>' . $custID . '</CustomerID>';
        $strPost .= '<AuthenticationID>' . $authID . '</AuthenticationID>';
        $strPost .= '<CountryCode>' . $countryCode . '</CountryCode>';
        $strPost .= '<PhoneNumber>' . $phoneNum . '</PhoneNumber>';
        $strPost .= '<Message>' . $this->getMessageText() . '</Message>';
        $strPost .= '<VerificationCode>' . $randCode . '</VerificationCode>';
        $strPost .= '</RequestSMS>';
        $strPost .= '</soap:Body>';
        $strPost .= '</soap:Envelope>';
        $_SESSION['ts-class']['lastRequest'] = $strPost;

        if ($this->debugging) {
            $this->echoXml($strPost);
        }

        //STORE THE HEADER INFORMATION IN THE HEADER ARRAY
        $header[] = 'Content-type: text/xml; charset=utf-8';
        $header[] = 'SOAPAction: https://www.telesign.com/api/RequestSMS';

        //SET THE PHP CURL OPTIONS, INCLUDING THE HEADER AND THE VARIABLE TO POST
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $strPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        if (Yii::$app->params['USE_PROXY']) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $ret = curl_exec($ch);
        curl_close($ch);
        $_SESSION['ts-class']['lastResponse'] = $ret;

        return $ret;
    }

    /**
     * Send the phoneid request to Telesign through SOAP using cUrl
     *
     * @param string $uri
     * @param string $custID
     * @param string $authID
     * @param integer $countryCode
     * @param integer $phoneNum
     * @return mixed response string or false on failure
     */
    function apiRequestPhoneID($uri, $custID, $authID, $countryCode, $phoneNum) {
        $ch = curl_init();
        if (!$ch) {
            $this->errors[] = "Failed initializing cURL handle";
            return false;
        }

        //STORE THE SOAP IN THE STRPOST VARIABLE
        $strPost = '<?xml version="1.0" encoding="utf-8"?>';
        $strPost .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        $strPost .= 'xmlns:xsd="http://www.w3.org/2001/XMLSchema" ';
        $strPost .= 'xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
        $strPost .= '<soap:Body>';
        $strPost .= '<RequestPhoneID xmlns="https://www.telesign.com/api/">';
        $strPost .= '<CustomerID>' . $custID . '</CustomerID>';
        $strPost .= '<AuthenticationID>' . $authID . '</AuthenticationID>';
        $strPost .= '<CountryCode>' . $countryCode . '</CountryCode>';
        $strPost .= '<PhoneNumber>' . $phoneNum . '</PhoneNumber>';
        $strPost .= '</RequestPhoneID>';
        $strPost .= '</soap:Body>';
        $strPost .= '</soap:Envelope>';
        $_SESSION['ts']['lastRequest'] = $strPost;

        if ($this->debugging) {
            $this->echoXml($strPost);
        }

        //STORE THE HEADER INFORMATION IN THE HEADER ARRAY
        $header[] = 'Content-type: text/xml; charset=utf-8';
        $header[] = 'SOAPAction: https://www.telesign.com/api/RequestPhoneID';

        //SET THE PHP CURL OPTIONS, INCLUDING THE HEADER AND THE VARIABLE TO POST
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $strPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        if (Yii::$app->params['USE_PROXY']) {
            curl_setopt($ch, CURLOPT_PROXY, Yii::$app->params['PROXY_HOST']);
            curl_setopt($ch, CURLOPT_PROXYPORT, Yii::$app->params['PROXY_PORT']);
        }

        $ret = curl_exec($ch);
        curl_close($ch);
        $_SESSION['ts']['lastResponse'] = $ret;

        return $ret;
    }

    /**
     * Validate request parameters
     *
     * @param string $phoneNo sanitized phone number
     * @param string $countryCode country code
     */
    function validateParameters($phoneNo, $countryCode) {
        if (empty($this->custID) || strlen($this->custID) != 36) {
            array_push($this->errors, array('message' => 'Invalid Customer ID', 'code' => -20001));
        }

        if (empty($this->authID) || strlen($this->authID) != 36) {
            array_push($this->errors, array('message' => 'Invalid Authentication ID', 'code' => -20001));
        }

        if (empty($countryCode)) {
            array_push($this->errors, array('message' => 'Invalid country code', 'code' => -10001));
        }

        if (strlen($phoneNo) < 5) {
            array_push($this->errors, array('message' => 'Invalid phone number', 'code' => -10001));
        }
    }

    /**
     * Sanitizes a phone number trimming it & stripping it of all whitespace, parenthesis,
     * plus and minus characters.
     *
     * @param string $phoneNumber phone number without country code
     * @return string valid phone number
     */
    function sanitizePhoneNumber($phoneNumber) {
        // remove whitespaces, dashes and brackets
        $phoneNumber = trim($phoneNumber);
        $pattern = array("/-/", "/\+/", "/\s/", "/\(/", "/\)/");
        $replace = '';
        $phoneNumber = preg_replace($pattern, $replace, $phoneNumber);

        // trim zeros from the begining
        $phoneNumber = ltrim($phoneNumber, '0');

        return $phoneNumber;
    }

    /*
     * XML Parser functions
     */

    function startElement($parser, $name, $attrs) {
        $this->curTag .= "^$name";
        //var_dump($this->curTag);
    }

    function endElement($parser, $name) {
        $this->caret_pos = strrpos($this->curTag, '^');
        $this->curTag = substr($this->curTag, 0, $this->caret_pos);
    }

    function characterData($parser, $data) {
        if ($this->flag == "call") {
            $elements = "^SOAP:ENVELOPE^SOAP:BODY^REQUESTCALLRESPONSE^REQUESTCALLRESULT";
            $type = 1;
        } elseif ($this->flag == "sms") {
            $elements = "^SOAP:ENVELOPE^SOAP:BODY^REQUESTSMSRESPONSE^REQUESTSMSRESULT";
            $type = 1;
        } elseif ($this->flag == "phoneid") {
            $elements = "\^SOAP:ENVELOPE\^SOAP:BODY\^REQUESTPHONEIDRESPONSE\^REQUESTPHONEIDRESULT";
            $type = 2;
        }

        if (isset($type) && $type == 1) {
            $refIDKey = $elements . "^REFERENCEID";
            $errorCodeKey = $elements . "^APIERROR^CODE";
            $errorMessageKey = $elements . "^APIERROR^MESSAGE";

            if ($this->curTag == $refIDKey) {
                $this->callinfo['ReferenceID'] = $data;
            } elseif ($this->curTag == $errorCodeKey) {
                $this->callinfo['ErrorCode'] = $data;
            } elseif ($this->curTag == $errorMessageKey) {
                $this->callinfo['ErrorMessage'] = $data;
                array_push($this->errors, array('message' => 'API error ' . $this->callinfo['ErrorMessage'], 'code' => $this->callinfo['ErrorCode']));
            }
        } elseif (isset($type) && $type == 2) {

            if (preg_match("/^$elements\^(\w+\^?\w+)$/", $this->curTag, $matches) > 0) {
                $this->phoneinfo[$matches[1]] = $data;
            }
        }
    }

    /**
     * Return request's reference id for any follow up operations (status check)
     *
     * @return string reference id
     */
    function getReferenceID() {
        return isset($this->callinfo['ReferenceID']) ? $this->callinfo['ReferenceID'] : null;
    }

    function parseResponseXml() {
        $this->xml_parser = xml_parser_create("UTF-8");
        xml_set_element_handler($this->xml_parser, array(&$this, 'startElement'), array(&$this, 'endElement'));
        xml_set_character_data_handler($this->xml_parser, array(&$this, 'characterData'));
        if (!xml_parse($this->xml_parser, $this->ret)) {
            array_push($this->errors, array('message' => 'XML error: ', 'code' => xml_error_string(xml_get_error_code($this->xml_parser))));
        }
        xml_parser_free($this->xml_parser);
    }

    /**
     * Format XML for printing in debug mode
     *
     * @param string $xml xml content
     * @return string nicely-formatted XML
     */
    function formatXmlString($xml) {
        // add marker linefeeds to aid the pretty-tokeniser (adds a linefeed between all tag-end boundaries)
        $xml = preg_replace('/(>)(<)(\/*)/', "$1\n$2$3", $xml);

        // remove lines between an empty tag
        $xml = preg_replace("|<([^>/ ]+)(\s([^>]+))?>\n</\\1>|", "<$1$2></$1>", $xml);

        // now indent the tags
        $token = strtok($xml, "\n");
        $result = ''; // holds formatted version as it is built
        $pad = 0; // initial indent
        $matches = array(); // returns from preg_matches()
        // scan each line and adjust indent based on opening/closing tags
        while ($token !== false) {
            // test for the various tag states
            // 1. open and closing tags on same line - no change
            if (preg_match('/.+<\/\w[^>]*>$/', $token, $matches)) {
                $indent = 0;
                // 2. closing tag - outdent now
            } elseif (preg_match('/^<\/\w/', $token, $matches)) {
                $pad--;
                // 3. opening tag - don't pad this one, only subsequent tags
            } elseif (preg_match('/^<\w[^>]*[^\/]>.*$/', $token, $matches)) {
                $indent = 1;
                // 4. no indentation needed
            } else {
                $indent = 0;
            }

            // pad the line with the required number of leading spaces
            $line = str_pad($token, strlen($token) + $pad * 4, ' ', STR_PAD_LEFT);
            $result .= $line . "\n"; // add to the cumulative result, with linefeed
            $token = strtok("\n"); // get the next token
            $pad += $indent; // update the pad size for subsequent lines
        }

        return $result;
    }

    /**
     * Decorator for formatXmlString which adds fancy border and div style.
     * Outputs html-formatted xml.
     *
     * @param string $xml xml content
     * @return void
     */
    function echoXml($xml, $return = false) {
        $str = '<pre style="border: 2px solid gray; background-color: RGB(240,240,240); padding: 3px; overflow: auto; font-family: lucida console; font-size: 8pt;">';
        $str .= htmlentities($this->formatXmlString($xml));
        $str .= '</pre>';

        if (!$return) {
            echo $str;
        } else {
            return $str;
        }
    }

    function getFormattedRequest() {
        if (isset($_SESSION['ts-class']['lastRequest'])) {
            return $this->echoXml($_SESSION['ts-class']['lastRequest'], true);
        } else {
            return false;
        }
    }

    function getFormattedResponse() {
        if (isset($_SESSION['ts-class']['lastResponse'])) {
            return $this->echoXml($_SESSION['ts-class']['lastResponse'], true);
        } else {
            return false;
        }
    }

    function setEmailRecipient($adress) {
        $emailRegex = "/^([_a-z0-9+-]+)(\.[_a-z0-9+-]+)*@([a-z0-9-]+)(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i";
        if (preg_match($emailRegex, $adress)) {
            return $this->mailAdress = $adress;
        } else {
            return false;
        }
    }

    function sendError($content) {
        $exp = '';
        foreach ($content as $a) {

            $exp .="\n" . $a['message'] . ', error code: ' . $a['code'];
        }

        if ($this->getFormattedRequest()) {
            $response = $this->getFormattedResponse();
            $request = $this->getFormattedRequest();
        } else {
            $pre = '<pre style="border: 2px solid gray; background-color: RGB(240,240,240); padding: 3px; overflow: auto; font-family: lucida console; font-size: 8pt;">';
            $response = $pre . "Response not available</pre>";
            $request = $pre . "Request not available</pre>";
        }

        $htmlContent = '<html><body>' . nl2br($exp) . '<p>' . $request
                . '<p>' . $response . '</body></html>';

        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=utf-8' . "\r\n";

        $emailRegex = "/^([_a-z0-9+-]+)(\.[_a-z0-9+-]+)*@([a-z0-9-]+)(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i";
        if (isset($this->mailAdress)) {
            return mail($this->mailAdress, "TeleSign Error", $htmlContent, $headers);
        }

        return false;
    }

    /**
     * Calls an external function passed as a string/array.
     * String should be a name of a function.
     * Array should have two values - either an object and a name of its method or a name of a class and a name of its static method.  
     *
     * @param mixed $callbackFunction
     * @param array $params
     * @return mixed
     */
    function callFunction($callbackFunction, $params = array()) {
        if (is_array($callbackFunction)) {
            if (is_object($callbackFunction[0])) {
                return $callbackFunction[0]->$callbackFunction[1]($params);
            } else {
                define($callbackFunction[0], CLASSNAME);
                return CLASSNAME::$callbackFunction[1]($params);
            }
        } else {
            return $callbackFunction($params);
        }
    }

    /**
     * Calls an external function to log information about the request
     *
     * @param mixed $callbackFunction
     * @return void
     */
    function setLogging($callbackFunction) {
        $params = array('ip' => $_SERVER['REMOTE_ADDR'], 'date' => date('Y-m-d H:i:s'), 'phoneNumber' => $this->countryCode . $this->phoneNumber);
        $this->callFunction($callbackFunction, $params);
    }

    /**
     * Sets the limit of requests originating from one IP during a period of time (in hours)
     * 
     * @param int $limit
     * @param int $hours
     * @param mixed $callbackFunction
     * @return void
     */
    function setRequestLimit($limit, $hours, $callbackFunction) {
        $params = array('ip' => $_SERVER['REMOTE_ADDR'], 'hours' => $hours);
        $requests = $this->callFunction($callbackFunction, $params);
        if ($requests > $limit) {
            array_push($this->errors, array('message' => 'Requests limit exceeded', 'code' => -45));
        }
    }

}