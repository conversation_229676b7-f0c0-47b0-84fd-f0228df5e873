<?php

use yii\helpers\Html;
?>
<title><?php echo Yii::t('general', 'How to recover your password'); ?></title>

<?php echo Yii::t('sso', 'Dear {cname} ,', ['cname' => $cname]); ?>

<br><br><?php echo Yii::t('sso', 'Forgot password? Kindly click the button below to reset password.'); ?>

<br><br>
<?php echo Html::a(Yii::t('sso', 'RESET PASSWORD'), $link, ['style' => ['display' => 'inline-block', 'color' => '#FFFFFF', 'background-color' => '#6ec8c7',
        'font-size' => '12px', 'font-weight' => '500', 'text-decoration' => 'none', 'background-color' => '#6ec8c7', 'border-radius' => '30px', 'padding' => '10px 50px', 'cursor' => 'pointer', 'margin-bottom' => '10px']]);
?>
<br>
<?php echo Yii::t('sso', 'or use this link : '); ?><?php echo Html::a($link, $link, ['style' => ['color' => '#6ec8c7', 'text-decoration' => 'none']]); ?> 

<br><br><br><?php echo Yii::t('sso', 'If you have received this email by error, it is likely that another user entered your email address by mistake while trying to reset a password. ');?>
<?php echo Yii::t('sso','If you did not initiate the request, you can safely disregard this email.'); ?>

<br><br><br><?php echo Yii::t('general', 'Best regards,'); ?><br>
<?php echo Yii::t('general', 'OffGamers.com'); ?>
        

