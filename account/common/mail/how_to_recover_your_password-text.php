<?php
use yii\helpers\Html; 

echo Yii::t('general', 'Dear {cname} ,', ['cname' => $cname]);

echo "\r\n";

echo Yii::t('sso','Forgot password? Kindly click the button below to reset password.');

echo Html::a(Yii::t('sso', 'RESET PASSWORD'), $link ,['style'=>['display'=> 'inline-block','color'=> '#FFFFFF','background-color'=> '#6ec8c7',
                'font-size'=> '12px','font-weight'=> '500','text-decoration' => 'none','background-color'=> '#6ec8c7','border-radius'=> '30px','padding'=> '10px 50px','cursor'=> 'pointer','margin-bottom'=> '10px']]);

echo "\r\n"; 

echo Yii::t('sso','or use this link : ');

echo Html::a($link, $link,['style'=>['color'=> '#6ec8c7','text-decoration' => 'none']]);

echo "\r\n";

echo Yii::t('sso','If you have received this email by error, it is likely that another user entered your email address by mistake while trying to reset a password. ');

echo Yii::t('sso','If you did not initiate the request, you can safely disregard this email.');

echo "\r\n";

echo Yii::t('general', 'Best regards,');

echo "\r\n";

echo Yii::t('general', 'OffGamers.com'); 