<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this \yii\web\View view component instance */
/* @var $message \yii\mail\MessageInterface the message being composed */
/* @var $content string main view render result */
?>
<?php $this->beginPage() ?>
<!DOCTYPE html PUBLIC>
<html lang="<?= Yii::$app->language ?>">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= Yii::$app->charset ?>" />
        <title><?= Html::encode($this->title) ?></title>
        <?php $this->head() ?>
    </head>
    <body style="font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif; font-weight: 300; background-color: #eaeaea; line-height: 22px;">
        <br>
        <?php $this->beginBody() ?>
        <table style="table-layout: fixed; max-width: 600px; font-size: 14px; color: #666666; line-height: 1.5; background-color: #ffffff; padding-top: 50px; padding-bottom: 50px;  padding-left: 30px; padding-right: 30px; border-top-left-radius: 6px; border-top-right-radius: 6px;" width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td style="padding-bottom: 50px;">
                    <img src="https://static.offgamers.com/email-assets/og-logo-2023.png" height="45px" alt="OffGamers | shared single sign-on" title="OffGamers | shared single sign-on"/>

                </td>
            </tr>
            <tr>
                <td>
                    <?php
                    echo $content_html;
                    ?>
                </td>
            </tr>
        </table>
        <!--FOOTER-->
        <table style="table-layout: fixed; max-width: 600px; padding: 30px; font-size: 12px; color: #999999; line-height: 1.5; background-color: #f9f9f9; border-top: 1px solid #e8e8e8; border-bottom-left-radius: 6px; border-bottom-right-radius: 6px;" width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td style="padding-bottom: 15px;">
                    <?php echo Yii::t('email', 'You received this mandatory email service announcement to update you about important changes to your OffGamers account. '); ?><?php echo Yii::t('email', 'For information on OffGamers\'s privacy policy, visit '); ?><?php echo Html::a(Yii::t('email', 'privacy policy'), Yii::$app->params['OG_URL'] .'/privacy-policy', ['style' => ['color' => '#6ec8c7','text-decoration'=> 'none']]); ?>.
                    <br><?php echo Yii::t('email', 'This email message was auto-generated. Please do not reply.'); ?>
                </td>
            </tr>

            <tr>
                <td style="padding-top: 15px; border-top: 1px solid #e3e3e3;">
                    <?php echo Yii::t('email', '&copy; {year} ', ['year' => date('Y')]); ?><?php echo Html::a(Yii::t('general', 'OffGamers.com'), 'https://account.offgamers.com', ['style' => ['color' => '#6ec8c7','text-decoration'=> 'none']]); ?>.
                    <?php echo Yii::t('email', 'All right reserved.'); ?>
                </td>
            </tr>
        </table>
        <?php $this->endBody() ?>
        <br>
    </body>
</html>
<?php $this->endPage() ?>