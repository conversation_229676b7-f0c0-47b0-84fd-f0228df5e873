<?php

use yii\helpers\Html;
?>
<title><?php echo Yii::t('smsToken', 'Security Token Has Been Reset'); ?></title>

<?php echo Yii::t('general', 'Dear {cname} ,', ['cname' => $cname]); ?>

<br><br><?php echo Yii::t('smsToken', 'We have reset your Security Token and this token is valid for {HOURS} hours:', ['HOURS' => 24]); ?>
<br><span style="color: #2b2b36; font-weight: 500; font-size: 24px;"><?php echo $tokenValue; ?></span>

<br><br><?php echo Yii::t('general', 'If you did not perform this action, please '); ?><?php echo Html::a(Yii::t('general', 'contact us '), 'https://helpdesk.offgamers.com/support/solutions', ['style' => ['color' => '#6ec8c7', 'text-decoration' => 'none']]); ?>
<?php echo Yii::t('general', 'immediately to prevent your account from being compromised.'); ?>

<br><br><br><?php echo Yii::t('general', 'Best regards,'); ?><br><?php echo Yii::t('smsToken', 'OffGamers.com'); ?>
        