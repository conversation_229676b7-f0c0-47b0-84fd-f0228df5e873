<?php

use yii\helpers\Html;
?>
<?php $this->beginPage() ?>
<!DOCTYPE html PUBLIC>
<html lang="<?= Yii::$app->language ?>">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= Yii::$app->charset ?>" />
        <title><?= Html::encode($this->title) ?></title>
        <?php $this->head() ?>
    </head>
<body style="margin: 0; background-color: #e8e8e8; font-family: 'Helvetica Neue', Helvetica, Arial, 'sans-serif';">
	<br>
        <?php $this->beginBody() ?>
	<!-- HEAD -->
	<table style="table-layout: fixed; max-width: 600px; padding: 30px; background-color: #2b2b36; border-top-left-radius: 6px; border-top-right-radius: 6px;" width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td style="padding-top: 30px;" align="center">
                    <!-- LOGO -->
                    <img src="https://static.offgamers.com/email-assets/og-logo-white-2023.png" height="45px" alt="OffGamers | shared single sign-on" title="OffGamers | shared single sign-on">
                </td>
            </tr>
            <tr>
                <td style="padding-top: 30px; padding-bottom: 30px;" align="center">
                    <span style="font-size: 20px; font-weight: 500; color: #ffffff; line-height: 1.2;"><?php echo Yii::t('sso', 'WELCOME TO OFFGAMERS!'); ?></span>
                </td>
            </tr>
	</table>
	
	<!-- CONTENT -->	
	<table style="table-layout: fixed; max-width: 600px; font-size: 14px; color: #666666; line-height: 1.5; background-color: #ffffff; padding-top: 30px; padding-bottom: 30px; padding-left: 30px; padding-right: 30px;" width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
		<tr>
			<td>
                            <?php echo Yii::t('sso', 'Hello {cname},', ['cname' => $cname]); ?>
                            <br><br>
                            <?php echo Yii::t('sso', 'Thank you for signing up. Your OffGamers account allows you to access OffGamers\'s network of websites. '); ?>
                            <?php echo Yii::t('sso', 'One username and password is all you need to access '); ?>
                            <?php echo Html::a('OffGamers.com', 'https://www.offgamers.com', ['style' => ['color' => '#6ec8c7', 'text-decoration' => 'none']]); ?>

                            <br><br><?php echo Yii::t('sso', 'Please do secure your OffGamers\'s account details and password. '); ?>
                            <?php echo Yii::t('sso', 'It is best not to share your account details, username, or password to anyone.'); ?>
			</td>
		</tr>

		<tr><!-- SECURITY -->
			<td style="padding-top: 20px; padding-bottom: 20px;">
				<table style="max-width: 540px; background-color: #f9f9f9; border: 1px solid #e8e8e8; font-size: 12px; color: #666666; line-height: 1.5; padding: 20px;" cellpadding="0" cellspacing="0" border="0" align="center" width="100%">
                                    <tr>
                                        <td>
                                            <span style="font-size: 14px; font-weight: 500; color: #2b2b36;"><?php echo Yii::t('sso', 'Secure Your Account'); ?></span>
                                            <br><?php echo Yii::t('sso', 'Turn on '); ?><?php echo Html::a('two-factor authentication (2FA)', 'https://account.offgamers.com/site/page?view=2fa', ['style' => ['color' => '#6ec8c7', 'text-decoration' => 'none']]); ?>
                                            <?php echo Yii::t('sso', ' for an added layer of security. You will need to sign in with your phone to login to OffGamers. '); ?>
                                            <?php echo Yii::t('sso', 'If your account password is compromised or stolen, only you can sign in to your account with your mobile.'); ?>
                                            <br><br>
                                            <?php
                                            echo Html::a(Yii::t('sso', 'ACTIVATE NOW!'), 'https://account.offgamers.com/profile/security#change-2fa', ['style' => ['display' => 'inline-block', 'color' => '#FFFFFF', 'background-color' => '#6ec8c7', 'font-size' => '12px', 'font-weight' => '500',
                                                    'text-decoration' => 'none', 'border-radius' => '30px', 'padding' => '10px 50px', 'cursor' => 'pointer', 'margin-bottom' => '10px']]);
                                            ?>
                                        </td>
                                    </tr>
                                </table>
			</td>
		</tr>

		<tr>
			<td>
				<?php echo Yii::t('sso', 'OffGamers Points'); ?>
                                <?php echo Yii::t('sso', ' is given upon sucessful transaction(s) made for any product listed on OffGamers\'s network of websites. Regular members will receive a standard amount of points based on the product(s) purchased.'); ?>
				<br><br><br><?php echo Yii::t('sso', 'Thank you for choosing us,'); ?><br><?php echo Yii::t('general', 'OffGamers.com'); ?>
			</td>
		</tr>
	</table>
	
	<!--FOOTER-->
        <table style="table-layout: fixed; max-width: 600px; padding: 30px; font-size: 12px; color: #999999; line-height: 1.5; background-color: #f9f9f9; border-top: 1px solid #e8e8e8; border-bottom-left-radius: 6px; border-bottom-right-radius: 6px;" width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td style="padding-bottom: 15px;">
                    <?php echo Yii::t('email', 'You received this mandatory email service announcement to update you about important changes to your OffGamers account. '); ?><?php echo Yii::t('email', 'For information on OffGamers\'s privacy policy, visit '); ?><?php echo Html::a(Yii::t('email', 'privacy policy'), Yii::$app->params['OG_URL'] .'/privacy-policy', ['style' => ['color' => '#6ec8c7','text-decoration'=> 'none']]); ?>.
                    <br><?php echo Yii::t('email', 'This email message was auto-generated. Please do not reply.'); ?>
                </td>
            </tr>

            <tr>
                <td style="padding-top: 15px; border-top: 1px solid #e3e3e3;">
                    <?php echo Yii::t('email', '&copy; {year} ', ['year' => date('Y')]); ?><?php echo Html::a(Yii::t('general', 'OffGamers.com'), \yii\helpers\Url::to('/'), ['style' => ['color' => '#6ec8c7','text-decoration'=> 'none']]); ?>.
                    <?php echo Yii::t('email', 'All right reserved.'); ?>
                </td>
            </tr>
        </table>
        <?php $this->endBody() ?>
        <br>
    </body>
</html>
<?php $this->endPage() ?>
