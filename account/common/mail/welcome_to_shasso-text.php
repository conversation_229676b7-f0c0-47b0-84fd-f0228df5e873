<?php

use yii\helpers\Html;

echo Yii::t('sso', 'Hello {cname},', ['cname' => $cname]);

echo "\r\n";

echo Yii::t('sso', 'Thank you for signing up. Your OffGamers account allows you to access OffGamers\'s network of websites. ');

echo Yii::t('sso', 'One username and password is all you need to access ');

echo Html::a('OffGamers.com ', 'https://www.offgamers.com',['style'=>['color'=> '#6ec8c7','text-decoration' => 'none']]);
echo "\r\n";

echo Yii::t('sso', 'Please do secure your OffGamers\'s account details and password. ');

echo Yii::t('sso','It is best not to share your account details, username, or password to anyone.');

echo "\r\n";

echo Yii::t('sso', 'Secure Your Account');

echo "\r\n";

echo Yii::t('sso', 'Turn on ');

echo Html::a(Yii::t('sso', 'two-factor authentication (2FA)'), 'https://account.offgamers.com/site/page?view=2fa',['style'=>['color'=> '#6ec8c7','text-decoration' => 'none']]);

echo Yii::t('sso', ' for an added layer of security. You will need to sign in with your phone to login to OffGamers. ');

echo Yii::t('sso','If your account password is compromised or stolen, only you can sign in to your account with your mobile.');

echo "\r\n";

echo Html::a(Yii::t('sso', 'ACTIVATE NOW!'), 'https://account.offgamers.com/profile/security#change-2fa',['style'=>['display'=> 'inline-block','color' => '#FFFFFF','background-color'=> '#6ec8c7','font-size' => '12px','font-weight'=> '500',
    'text-decoration' => 'none','border-radius'=> '30px','padding' => '10px 50px','cursor'=> 'pointer','margin-bottom' => '10px']]);

echo "\r\n";

echo Yii::t('sso', 'OffGamers Points');

echo Yii::t('sso', ' is given upon sucessful transaction(s) made for any product listed on OffGamers\'s network of websites. Regular members will receive a standard amount of points based on the product(s) purchased.');

echo "\r\n";

echo Yii::t('sso', 'Thank you for choosing us,');

echo "\r\n";

echo Yii::t('general', 'OffGamers.com');
