<?php

return [
    'BTN_ACTIVATE' => 'Activate',
    'BTN_DEACTIVATE' => 'Deactivate',
    'BTN_SAVE' => 'Save',
    'BTN_SAVE_CHANGED' => 'Save',
    'BTN_CANCEL' =>'cancel',
    'BTN_CONTINUE' => 'continue',
    'BTN_SUBMIT' => 'Submit',
    'BTN_SUBMITTING' => 'Submitting...',
    'BTN_UPLOAD' => 'Upload',
    'BTN_OK' => 'OK',
    'BTN_NEXT' => 'Next',
    'SELECT_FILE' => 'select file',
    'COMBOBOX_STATE' => 'State',
    'EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE' => 'Dear Valued Customer,<br /><br />This is to inform you that your password has been changed.<br /><br />Please <a href="https://helpdesk.offgamers.com/support/solutions">contact us</a> if you think someones has access to the email address associated with your OffGamers account. Kindly change the password for your email as well.<br /><br />{SYS_EMAIL_SIGNATURE}',
    'EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT' => 'Password Changed',
    'EMAIL_PROFILE_CHANGE_EMAIL' => 'You have change to a new email.<br /><br />Previous Email: {SYS_USER_PREVIOUS_MAIL} <br />New Email: {SYS_USER_NEW_MAIL} <br />Date: {SYS_DATE} <br /><br /><br />Please <a href=\'https://helpdesk.offgamers.com/support/solutions\'>contact us</a> if you believe that this change was not intended.<br /><br />{SYS_EMAIL_SIGNATURE}',
    'EMAIL_PROFILE_CHANGE_EMAIL_SUBJECT' => 'OffGamers Email Change Request',
    'ENTRY_2FA_CODE' => '6-Digit Verification Code',
    'ENTRY_ADDRESS' => 'Address',
    'ENTRY_CITY' => 'City',
    'ENTRY_CONFIRM_EMAIL' => 'Confirm email',
    'ENTRY_CONFIRM_PASSWORD' => 'Confirm new password',
    'ENTRY_COUNTRY' => 'Country/Region',
    'ENTRY_CURRENT_PASSWORD' => 'Current password',
    'ENTRY_DOB' => 'Date of birth',
    'ENTRY_EMAIL' => 'Email',
    'ENTRY_GENDER' => 'Gender',
    'ENTRY_MOBILE_PHONE' => 'Mobile Number',
    'ENTRY_NAME' => 'Name',
    'ENTRY_NEWSLETTER' => 'Customers Newsletter',
    'ENTRY_NEW_EMAIL' => 'New email',
    'ENTRY_NEW_PASSWORD' => 'New password',
    'ENTRY_NEW_PHONE_NUMBER' => 'New mobile number',
    'ENTRY_SETUP_2FA_CONFIRM_PASSWORD' => 'Confirm password to start setup Two-Factor Authentication',
    'ENTRY_STATE' => 'State',
    'ENTRY_TWO_FACTOR_AUTH' => 'Two-Factor Authentication',
    'ENTRY_ZIP' => 'Zip code',
    'ERROR_EMPTY_DIAL_CODE' => 'Please select country/region code',
    'ERROR_EMPTY_MOBILE_NUMBER' => 'Please enter mobile number',
    'ERROR_INCORRECT_LOGIN_PASSWORD' => 'Password is incorrect',
    'ERROR_INSTANT_MESSENGER' => 'Please select at least one instant messenger',
    'ERROR_INSTANT_MESSENGER_INVALID_TYPE' => 'Invalid instant messenger type',
    'ERROR_INSTANT_MESSENGER_MAXED' => 'You are only allowed to have a maximum of {SYS_MIN_LENGTH} instant messenger in the account',
    'ERROR_INSTANT_MESSENGER_VALUE_EMPTY' => 'Instant messenger cannot be empty',
    'ERROR_SAME_EMAIL' => 'Please enter a different email address',
    'ERROR_INVALID_EMAIL' => "Invalid Email ({REASON})",
    'LINK_CHANGE_PHONE_NUMBER' => 'Change mobile number',
    'LINK_FORGET_PASSWORD' => 'Forgot your password?',
    'LINK_SETUP_TWO_FACTOR_AUTH' => 'How to setup Two-Factor Authentication?',
    'MSG_ADDRESS' => 'Your Street Address must contain a minimum of {SYS_MIN_LENGTH} characters.',
    'MSG_APP_AUTHY' => 'Authy',
    'MSG_APP_DUO_MOBILE' => 'Duo Mobile',
    'MSG_APP_GOOGLE_AUTH' => 'Google Authenticator',
    'MSG_APP_WINDOWS_PHONE_AUTH' => 'Windows Phone Authenticator',
    'MSG_CITY' => 'Your City must contain a minimum of {SYS_MIN_LENGTH} characters.',
    'MSG_CONFIGURE_TWO_FACTOR_DESC' => 'Once configured, you are required to enter a code created by the {APP_GOOGLE_AUTH}, {APP_DUO_MOBILE}, {APP_AUTHY}, or {APP_WINDOWS_PHONE_AUTH} apps in order to sign into your OffGamers account. {SYS_LEARN_MORE_LINK} about two-factor authentication from our help page.',
    'MSG_CONFIGURE_TWO_FACTOR_DESC_LEARN_MORE' => 'Learn more',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_1' => '1) Get the app',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_1_DESC' => 'Download and install the {APP_GOOGLE_AUTH}, {APP_DUO_MOBILE}, {APP_AUTHY}, or {APP_WINDOWS_PHONE_AUTH} app for your phone or tablet.',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_2' => '2) Scan this QR code',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_2_DESC' => 'Open the authentication app and scan the image on the left, using your phone\'s camera.',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_2_DESC_2' => 'Unable to scan QR code? You can manually enter the code as well: {SYS_CODE}',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_3' => '3) Enter verification code',
    'MSG_CONFIGURE_TWO_FACTOR_STEP_3_DESC' => 'After scanning the QR Code above, please enter the 6-digit verification code generated by the app.',
    'MSG_EMAIL_CHANGE_SUCCESS' => 'Email updated.',
    'MSG_ENTRY_CONTACT_NUMBER_ERROR' => 'Your mobile phone number must be at least {SYS_MIN_LENGTH} number.',
    'MSG_ENTRY_COUNTRY_CODE_ERROR' => 'You must select a country/region from the {SYS_COUNTRY_CODE} pull down menu or {SYS_CHANGE_MOBILE_NUMBER}',
    'MSG_ENTRY_COUNTRY_ERROR' => 'You must select a country/region from the Country/Region pull down menu.',
    'MSG_ERR_BIRTHDAY' => 'Your birthdate does not appear to be valid.',
    'MSG_ERR_MINIMUM_FIRSTNAME' => 'First name must be at least {SYS_MIN_LENGTH} character long.',
    'MSG_ERR_MINIMUM_LASTNAME' => 'Your Last Name must contain a minimum of {SYS_MIN_LENGTH} characters.',
    'MSG_ERR_POSTCODE' => 'Your Post Code must contain a minimum of {SYS_MIN_LENGTH} characters.',
    'MSG_EXCEED_IM_LIMIT' => 'You are only allowed to have a maximum of {SYS_MIN_LENGTH} instant messenger in the account',
    'MSG_FROM_ERROR' => 'Errors have occured during the process of your form.',
    'MSG_KYC_CHANGE_COUNTRY_ERROR' => 'You are not allowed to change phone number, please contact us for assistance.',
    'MSG_INCORRECT_CURRENT_PASSWORD' => 'Current password is incorrect.',
    'MSG_MOBILE_PHONE_CHANGE_SUCCESS' => 'Mobile number has been updated.',
    'MSG_PASSWORD_CHANGE_SUCCESS' => 'Password updated.',
    'MSG_PROFILE_UPDATE' => 'Profile information updated.',
    'MSG_SECURITY_TOKEN' => 'Please key in security token code',
    'MSG_LAST_FOUR_DIGIT' => 'The last 4 digits do not match with our records',
    'MSG_LAST_FOUR_DIGIT_LIMIT' => 'You\'ve reached the daily limit for confirming the last 4 digits mobile number.',
    'MSG_SELLER_CHANGE_EMAIL_EXCEED_LIMIT' => 'You have reached your change limit this month.',
    'MSG_SELLER_CHANGE_EMAIL_ONCE' => 'As a seller, you can only change email once a month.',
    'MSG_USER_CHANGE_EMAIL_LIMIT' => 'You can only change email up to 3 times a day.',
    'MSG_STATE_ERROR' => 'Your State must contain a minimum of {SYS_MIN_LENGTH} characters.',
    'MSG_STATE_ID_ERROR' => 'Please select your state.',
    'MSG_TWO_FACTOR_DISABLED' => '2-factor authentication disabled successfully.',
    'MSG_TWO_FACTOR_ENABLED' => '2-factor authentication enabled successfully.',
    'PLACEHOLDER_ADDRESS1' => 'Address 1',
    'PLACEHOLDER_ADDRESS2' => 'Address 2',
    'PLACEHOLDER_FIRST_NAME' => 'First Name',
    'PLACEHOLDER_LAST_NAME' => 'Last Name',
    'RADIO_FEMALE' => 'Female',
    'RADIO_MALE' => 'Male',
    'TEXT_EMAIL_DESC_1' => 'Your e-mail address is {email}.',
    'TEXT_EMAIL_DESC_2' => 'Use to login, reset password, manage newsletter and notifications.',
    'TEXT_MOBILE_PHONE_DESC_1' => 'Your current mobile phone number is {phone}',
    'TEXT_MOBILE_PHONE_DESC_1b' => 'You have yet to add any mobile phone number.',
    'TEXT_MOBILE_PHONE_DESC_2' => 'We need your mobile phone number for some security changes.',
    'TEXT_PASSWORD_DESC_1' => 'Do not use the same password as your e-mail account.',
    'TEXT_PASSWORD_DESC_2' => 'Changing password will eliminate all existing login sessions that was previously saved in this browser.',
    'TEXT_TFA_STATUS_ACTIVE' => 'active',
    'TEXT_TFA_STATUS_INACTIVE' => 'inactive',
    'TEXT_TWO_FACTOR_AUTH_CHECKING' => 'Enter the 2FA verification code below to change your mobile number.',
    'TEXT_TWO_FACTOR_AUTH_DESC' => 'Secure your account with Two-Factor Authentication.',
    'TEXT_TWO_FACTOR_AUTH_DISABLE' => 'To deactivate Two-Factor authentication, you will need to enter the 2FA verification code below.',
    'TEXT_TWO_FACTOR_AUTH_ENABLE' => 'Protect your OffGamers account with an extra layer of security by requiring access to your mobile phone. Once configured, you will be required to enter both your password and an authentication code from your mobile in order to sign into your OffGamers account. {SYS_LINK_LEARN}.',
    'TEXT_TWO_FACTOR_AUTH_STATUS' => 'Two-Factor authentication is {STATUS}.',
    'TITLE_BILLING_ADDRESS' => 'Billing Address',
    'TITLE_CONFIGURE_TWO_FACTOR' => 'Configure Two-Factor Authentication',
    'TITLE_PERSONAL_INFO' => 'Personal',
    'TITLE_PROFILE' => 'ACCOUNT DETAILS',
    'TITLE_SECURITY_INFO' => 'Security',
    'TEXT_CONFIRM_LAST_4_NUM' => 'Confirm last 4 digits of {phone}',
    'TEXT_FILE_SIZE' => 'Max size is {FILE_MAXSIZE}MB.',
    'TEXT_FILE_TYPE' => 'png, gif, jpg and pdf format only.',
    'TEXT_FORM_INVALID' => 'Invalid Form',
    //Einvoice
    'TITLE_EINVOICE_TYPE' => 'E-Invoice Type',
    'TEXT_EINVOICE_TYPE_DESC' => 'Cannot be edited after saving.',
    'EINVOICE_TYPE_PERSONAL' => 'Personal',
    'EINVOICE_TYPE_BUSINESS' => 'Business',
    'TITLE_COMPANY_DETAILS' => 'Company Details',
    'TITLE_COMPANY_ADDRESS' => 'Company Address',
    'ENTRY_COMPANY_NAME' => 'Company name',
    'ENTRY_COMPANY_REGISTRATION_NUMBER' => 'Company registration number',
    'ENTRY_TAX_IDENTIFICATION_NUMBER' => 'Tax identification number (TIN)',
    'MSG_ENTRY_TAX_IDENTIFICATION_NUMBER' => 'A number issued by tax authorities for tax reporting and compliance purposes.',
    'MSG_ENTRY_COMPANY_REGISTRATION_NUMBER' => 'A unique identifier assigned to your company upon registration with the authorities.',
    'MSG_ENTRY_FULL_NAME' => 'Please enter your full name as it appears on your identity document.',
    "ENTRY_NID" => 'National identity number',
    'ERROR_INVALID_NID' => 'Invalid national identity number',

    //Email
    'EMAIL_RECLAIM_DESC_1' => 'The mobile number you are trying to use has been registered to a OffGamers account previously. ',
    'EMAIL_RECLAIM_DESC_2' => 'If you wish to use the OffGamers account with a new email address, please reclaim your mobile number by entering this email when prompted by the system: {c_email}.',
    'EMAIL_NUMBER_BEEN_USED_DESC_1' => 'The mobile number you are trying to use has been registered to a OffGamers account with this email address.',
    'EMAIL_NUMBER_BEEN_USED_DESC_2' => 'Please login to your account using this email address: {c_email}',
    //Changing phone country
    'TITLE_CHANGE_PHONE_COUNTRY' => 'Changing Phone Country/Region',
    'DETAILS_CHANGE_PHONE_COUNTRY_QUOTA' => 'Please ensure you have completed all the following before submitting the request.',
    'DETAILS_CHANGE_PHONE_COUNTRY5' => 'Upload latest proof of address (POA) if your eKYC documents have verified previously.',
    'AGREE_CHANGE_PHONE_COUNTRY' => 'By clicking "CONTINUE", I acknowledge that I have read and understood the terms stated above and wish to proceed with my request.',
    'DETAILS_CHANGE_PHONE_COUNTRY7' => 'To change your phone country/region, please fill in the information below and upload the Proof of Address (POA).',
    'TITLE_UPLOAD_PROOF' => 'Upload Proof Of Address',
    'MSG_CHANGES_NOT_ALLOWED' => 'You can only request change mobile country/region once every 60 days',
    'TITLE_PHONE_COUNTRY_CHANGE_SUBMITED' => 'REQUEST SUBMITTED',
    'DETAILS_PHONE_CHANGE_SUBMITED' => 'The process may take 72 hours and we will update you via email at {EMAIL_ADDRESS}',
    'ERROR_PLEASE_AGREE' => 'Please click agree to the terms to continue.',
    'ERROR_PLEASE_UPLOAD_POA' => 'Please select a Proof of Address to upload.',
    'ERROR_MOBILE_COUNTRY_CHANGE_QUOTA_REACHED' => 'You can only request to change phone country/region once every 60 days.',
    'ERROR_MOBILE_COUNTRY_CHANGE_REJECTED_QUOTA_REACHED' => 'Your request to change phone country/region has been rejected. You can only request change phone country/region once every 60 days.',
    'DETAILS_INSERT_NEW_NUMBER' => 'Enter your new mobile phone number.',
    'DETAILS_CHANGE_PHONE_COUNTRY8' => 'You can only change your phone country/region once every 60 days',
    'VERIFY_NEW_EMAIL' =>'An email has been sent to {EMAIL_ADDRESS} at {LAST_SENT_TIME}, Please verify your new email address',
    'VERIFY_NEW_EMAIL_SUBJECT'=> "OffGamers Change Email Verification",
    'VERIFY_SIGNUP_EMAIL_SUBJECT'=> "OffGamers Signup Email Verification",
    'VERIFY_NEW_EMAIL_BODY' => "<p>Dear {FIRSTNAME},</p>
    <p>You have recently requested to change the email address used to log in to OffGamers to {NEW_EMAIL_ADDRESS}</p>
    <p>Please verify this e-mail account by clicking on the following link or copy and paste the link to your browser. This process will verify that you are the owner of this e-mail address and to protect OffGamers account owners from unauthorised access.</p>
    <br><a href=\"{EMAIL_CHANGE_URL}\" target=\"_blank\">{EMAIL_CHANGE_URL}</a><br>
    <p>If you did not initiate this request, please <a href=\"https://helpdesk.offgamers.com/support/solutions\" target=\"_blank\">contact us</a> immediately.</p>
    {EMAIL_SIGNATURE}
    ",
    'VERIFY_SIGNUP_EMAIL_BODY'=> "<p>Dear {FIRSTNAME}</p>
    <p>Welcome and thank you for registering at OffGamers with your email address  {SIGNUP_EMAIL}</p>
    <p>Please verify this e-mail account by clicking on the following link or copy and paste the link to your browser. This process will verify that you are the owner of this e-mail address and to protect OffGamers account owners from unauthorised access.</p>
    <br><a href=\"{EMAIL_SIGNUP_URL}\" target=\"_blank\">{EMAIL_SIGNUP_URL}</a><br>
    <p>If you did not create an account, no further action is required.</p>
    {EMAIL_SIGNATURE}
    ",
    "TEXT_DELETE_ACCOUNT" => "Delete account",
    "TEXT_LEARN_MORE" => "Learn more",
    "BTN_REQUEST" => "Request",
];
