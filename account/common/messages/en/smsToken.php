<?php

return [
    'BTN_EMAIL_SECURITY_TOKEN' => 'Email token',
    'BTN_REQUEST_SECURITY_TOKEN' => 'Request token',
    'ERROR_INVALID_LAST_DIGIT' => 'Invalid last 4 digit phone number.',
    'ERROR_REQUEST_TOKEN_PROVIDER_ERROR' => 'There was an error sending the SMS. Kindly wait for a moment and try again or contact us.',
    'ERROR_TRIED_TOO_MANY_TIMES' => 'You have attempted to verify the number too many times. Please wait for 24 hours before re-attempting, or click on "Need help" link below',
    'ERROR_PLEASE_REQUEST_TOKEN' => 'Please request and enter the one-time password (OTP) sent to you via SMS.',
    'LINK_LOST_PHONE' => 'Lost or invalid mobile phone?',
    'LINK_NEED_HELP' => 'Need help?',
    'LINK_RESEND' => 'Resend',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => 'Enter the 6 digits security token',
    'TEXT_ENTER_LAST_4_DIGIT' => 'Enter the last 4 digits mobile phone number to receive security token in your registered e-mail.',
    'TEXT_INVALID_MOBILE' => 'Invalid mobile number. Please use new mobile number.',
    'TEXT_MOBILE_IS_IN_USED' => 'The mobile number is already in use in another account. An email has been sent to {c_email}.',
    'TEXT_MOBILE_IS_IN_USED_NOMAIL' => 'The mobile number is already in use in another account.',
    'TEXT_PLEASE_SETUP_MOBILE' => 'The mobile number you had provide is in used or invalid. Please key in another mobile number. Click <a class="fBlue3 f11" href="{SYS_PROFILE_LINK}">here</a>',
    'TEXT_PLEASE_WAIT_FOR_X_SECOND' => 'Please wait for {second} seconds before requesting to resend again.',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => 'You have exceeded the allowable limit for security tokens. Please try again after 24 hours.',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_MSG2' => 'Your security token is invalid. Please try again.',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_TOO_MANY_ATTEMPTS' => 'You have attempted to validate token too many times. Please request a new security token and try again.',
    'TEXT_REQUEST_TOKEN_REUSE_MSG' => 'Enter the security token previously sent to: %s.',
    'TEXT_REQUEST_TOKEN_SUCCESS_MSG' => 'A text message with your security token has been sent to: %s.',
    'TEXT_RESEND_TOKEN_SUCCESS_MSG' => 'A text message with your security token has been sent to: %s. Please try to restart your mobile device if you do not receive the token.',
    'TEXT_RESEND_IN_X_SEC' => 'Resend in {0} sec...',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => 'Your new Security Token is %s. This token will expire in %s hours from the time you receive this email.',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => 'An e-mail with security token has been sent to: %s',
    'TEXT_TESTING_MODE' => 'Testing Mode: Security Token',
    'TEXT_INVALID_MOBILE_COUNTRY' => 'Invalid mobile number country/region',
    'TITLE_SECURITY_TOKEN' => 'SECURITY TOKEN',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => 'Your new Security Token is {SYS_SECURITY_TOKEN}',
];
