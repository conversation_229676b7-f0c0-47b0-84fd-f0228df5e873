<?php

return [
    'BTN_CONNECT' => 'Connect',
    'BTN_CONNECT_WITH_SNS' => 'Connect with {SYS_PROVIDER}',
    'BTN_DISCONNECT' => 'Disconnect',
    'ERROR_MSG_ACCCOUNT_CONNECTED' => 'This OffGamers account is connected with {SYS_PROVIDER} email.',
    'ERROR_MSG_SN_CONNECTED' => 'This {SYS_PROVIDER} account is connected to another OffGamers account.',
    'MSG_SOCIAL_CONNECT_SUCCESSFUL' => 'Connection enabled successfully!',
    'MSG_SOCIAL_DISCONNECT_SUCCESSFUL' => 'Connection disabled successfully!',
    'TEXT_CONFIRM_PASSWORD' => 'Confirm Password to proceed',
    'TEXT_CONNECTION_STATUS' => 'Your OffGamers account is {STATUS} to your {SYS_PROVIDER}',
    'TEXT_CONNECTION_STATUS_CONNECTED' => 'connected',
    'TEXT_CONNECTION_STATUS_NOT_CONNECTED' => 'not connected',
    'TEXT_SNS_NOT_AVAILABLE' => 'Sorry, no social networks are supported',
    'TITLE_SOCIAL_CONNECT' => 'SOCIAL CONNECT',
];
