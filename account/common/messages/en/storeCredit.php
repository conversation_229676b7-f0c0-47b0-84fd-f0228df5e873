<?php

return [
    'TEXT_STATEMENT_TYPE_B' => 'Bonus',
    'TEXT_STATEMENT_TYPE_C' => 'Compensate',
    'TEXT_STATEMENT_TYPE_D' => 'Redeem',
    'TEXT_STATEMENT_TYPE_MI' => 'Manual Addition',
    'TEXT_STATEMENT_TYPE_MR' => 'Manual Deduction',
    'TEXT_STATEMENT_TYPE_P' => 'Purchase',
    'TEXT_STATEMENT_TYPE_R' => 'Refund',
    'TEXT_STATEMENT_TYPE_V' => 'Store Credit Conversion',
    'TEXT_STATEMENT_TYPE_X' => 'Cancel',
    'TEXT_STATEMENT_TYPE_XS' => 'Extra Store Credit',
    'BTN_CONVERT_NOW' => 'Convert Now',
    'BTN_OTHER' => 'Other',
    'BTN_REDEEM_NOW' => 'Redeem now',
    'BTN_TOPUP_NOW' => 'Top up now',
    'BTN_VIEW_ORDER_DETAIL' => 'View order details',
    'DESCRIPTION_BLOCKED_COUNTRY'=> 'Your IP address is blocked from accessing this service caused by restrictive policy and our compliance to government sanctions on Iran, Cuba, North Korea, Congo, Libya, Somalia, Yemen and Sudan.',
    'DESCRIPTION_CONTACT_US' => ' If you think this is a mistake, please <a href=\'https://helpdesk.offgamers.com/support/tickets/new\'>contact us</a>.',
    'ENTRY_CONVERT_TO' => 'Convert to',
    'ENTRY_MY_CREDIT_BALANCE' => 'Store Credits balance',
    'ERROR_INVALID_STORE_CREDIT_CURRENCY' => 'Sorry, your Store Credit currency is no longer supported by OffGamers.<br />Kindly email <a href="mailto:<EMAIL>"><EMAIL></a> to indicate your new preferred default currency.',
    'ERROR_MESSAGE_EMPTY_CURRENCY' => 'Please select a valid currency.',
    'ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND' => 'Sorry, you are currently not allowed to convert your store credit currency because you have a pending disbursement request. Please check back in 24 hours.',
    'ERROR_NO_REDEEM_GC_ALLOW_PENDING_PAYMENTS_FOUND' => 'Sorry, you are currently not allowed to redeem your gift card because you have a pending disbursement request. Please check back in 24 hours.',
    'ERROR_NO_TOPUP_ALLOW_PENDING_PAYMENTS_FOUND' => 'Sorry, you are currently not allowed to topup your store credit because you have a pending disbursement request. Please check back in 24 hours.',
    'ERROR_REDEEM_CUSTOMERS_GROUPS_NOT_ALLOW' => 'Your member status is not allow to redeem gift card. Please contact <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.',
    'ERROR_REDEEM_FAIL' => 'Redeem failed. Try again or contact us.',
    'ERROR_REDEEM_FAIL_DEACTIVATED' => 'This code is already in used. If you have bought this code, please contact us.',
    'ERROR_REDEEM_FAIL_INVALID_CURRENCY' => 'Apologize, Gift Card\'s currency: AED, SAR, KWD, PHP, JPY and HKD are no longer supported in our store.',
    'ERROR_REDEEM_FAIL_REDEEMED' => 'This code is already in used. If you have bought this code, please contact us.',
    'ERROR_SC_CONVERSION' => 'Sorry, you are currently not allowed to convert your store credit currency. Kindly email <a href="mailto:<EMAIL>"><EMAIL></a>.',
    'ERROR_SC_NOT_AVAILABLE' => 'Sorry, Top-Up Store Credit Service is temporarily not available, please try again later',
    'ERROR_STORE_CREDIT_CONTACT_ADMIN' => 'There is a problem with your Store Credit account. Please <a href=\'https://helpdesk.offgamers.com/support/tickets/new\'>contact us</a> for assistance.',
    'ERR_INVALID_STORE_CREDIT_CURRENCY_SELECTION' => 'Please select a valid currency.',
    'ERR_MSG_ENTER_INVALID_SC_AMOUNT' => 'Please enter a valid store credits amount.',
    'HEADER_CONVERT_SUCCESSFUL' => 'Convert successful!',
    'HEADER_REDEEM_SUCCESS' => 'Redeem Successful!',
    'HEADER_TOP_UP_SUCCESS' => 'Top Up Successful!',
    'LABEL_STATEMENT_DATE' => '{SYS_DATE} Statement',
    'LINK_HOW_TO_REDEEM' => 'How to Redeem {SYS_NAME}?',
    'LINK_KB_REDEEM_GC' => 'https://helpdesk.offgamers.com/support/solutions/articles/**********-offgamers-gift-card-buying-and-redeem-guide',
    'LINK_KB_TOPUP' => 'https://helpdesk.offgamers.com/support/solutions/articles/**********-types-of-alternate-payment-options',
    'LINK_LEARN' => 'How to top up Store Credits? <i class="fa fa-external-link-square"></i>',
    'LINK_TOPUP_STORE_CREDITS' => 'Top up Store Credits',
    'LINK_VIEW_STATEMENT' => 'View Statement',
    'TEXT_CONVERT_NO_SC' => 'You do not have any store credits.',
    'TEXT_GIFT_CARD_VALUE' => 'Gift card value',
    'TEXT_MINIMUM_PURCHASE_OF_SC' => 'Minimum top up of Store Credit for',
    'TEXT_MAXIMUM_PURCHASE_OF_SC' => 'Maximum top up of Store Credit for',
    'TEXT_NO_HISTORY' => 'No history found in the past {SYS_NUM_MONTHS} months. Get credits <a href="{SYS_LINK}">here</a>',
    'TEXT_NO_STATEMENT' => 'No statement found on {SYS_DATE}. <a href="{SYS_LINK}">Get credits</a>',
    'TEXT_OGC_DESC' => '{SYS_NAME} solution is designed to replace the traditional gift certificates and offers excellent conveniences to all users. The ease of providing gifts to your loved ones, friends and colleagues, could be achieved right from your fingertips. With direct online ordering, redeeming gifts online would be an ideal way for everyone. {SYS_KB_LINK}',
    'TEXT_ORDER_ID' => 'Your order number is',
    'TEXT_PIN_CODE' => 'PIN Code',
    'TEXT_PREVIOUS_BALANCE' => 'Previous Store Credits balance',
    'TEXT_SC_ACTIVITY_TYPE_CONVERT' => 'V',
    'TEXT_SC_CONVERT' => 'Store Credit Currency Conversion',
    'TEXT_SC_CONVERT_FROM_TO' => ' ( From {SYS_FROM_CUR} To {SYS_TO_CUR} )',
    'TEXT_SERIAL_NUMBER' => 'Serial No.',
    'TEXT_STATEMENT_TYPE_S' => 'Store Credit Top Up',
    'TEXT_STORE_CREDIT' => 'Store Credits',
    'TEXT_STORE_CREDIT_VALUE' => 'Store Credit value',
    'TEXT_TOPUP_INFO' => 'Use Store Credit(SC) as an alternative payment method to using credit cards when making purchases on OffGamers\'s network of websites. With a readily available line of credit in your account in the form of store credits, purchases and pre-order can be made quicker with minimal hassle. The Store Credits will be available in your account instantly after top up. {SYS_LINK_LEARN}.',
    'TEXT_TOP_UP_AMOUNT' => 'Top Up Amount',
    'TEXT_TOP_UP_CURRENCY' => 'Select your currency',
    'TEXT_VALUE_AFTER_CONVERSION' => 'Value after conversion',
    'TITLE_AMOUNT' => 'Amount',
    'TITLE_BALANCE' => 'Balance',
    'TITLE_CONVERT_STORE_CREDITS' => 'CONVERT STORE CREDITS',
    'TITLE_DATE' => 'Date',
    'TITLE_DESCRIPTION' => 'Description',
    'TITLE_REDEEM_GIFT_CARD' => 'REDEEM GIFT CARD',
    'TITLE_STATUS' => 'Status',
    'TITLE_STORE_CREDITS_STATEMENT' => 'STORE CREDITS STATEMENT',
    'TITLE_STORE_CREDIT_BALANCE' => 'Store Credit Balance',
    'TITLE_TOPUP_HISTORY' => 'TOP UP ACTIVITY',
    'TITLE_AMOUNT_REDEMPTION' => 'Redeem amount',
    'TEXT_CONFIRM' => 'CONFIRM',
];
