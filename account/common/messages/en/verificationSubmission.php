<?php

$text_upload_limit = 'Allowed formats: JPG, GIF, or PNG, Maximum {SYS_FILE_SIZE}kb size limit.';

$entry_001_header = 'Scan and take a selfie';
$entry_001_desc = "Do not wear sunglasses, hat, or accessories. Background must be light and neutral. Please ensure that the image is clear, no glare, and your face can be seen clearly. ";
$entry_002_header = 'Upload: Utility bill that matches your billing information.';
$entry_003_header = 'Electronic Know Your Customer (eKYC)';
$entry_003_instruction1 = 'Please prepare your Government Issued ID, Driving License, or Passport for the verification process. We will need your selfie too to perform a match with your ID.';
$entry_003_instruction2 = 'Information on your document must be readable.';
$entry_003_instruction3 = 'Expired document will not be accepted.';
$entry_003_instruction4 = 'Must match the billing address of your credit card or paypal.';
$entry_003b_header = 'Upload 2: Selfie photo of you holding your identity document.';
$entry_003b_instruction1 = 'Look straight at the camera.';
$entry_003b_instruction2 = 'Your face must be clearly visible. Do not wear sunglasses, hat, etc.';
$entry_003b_instruction3 = 'Background color must be light and neutral.';
$entry_004_header = 'Take the front Credit Card photo';
$entry_004_instruction1 = 'Your credit card must be valid. Please show the first 6 and last 4 digits of your credit card and blank out the middle digits. Please ensure that the image is clear, no glare, and the wording must be readable.';
$entry_005_header = 'Upload: Image of Virtual Card / Mobile Payment App.';
$entry_005_instruction1 = 'Take a screenshot of your virtual card or mobile payment app. (For security reasons, please blank out the CVV, if any).';

return [
    'ENTRY_001' => '<b>' . $entry_001_header . '</b>',
    'ENTRY_002' => '<b>' . $entry_002_header . '</b>',
    'ENTRY_003' => '<b>' . $entry_003_header . '</b><br>',
    'ENTRY_003b' => '<b>' . $entry_003b_header . '</b>',
    'ENTRY_004' => '<b>' . $entry_004_header . '</b>',
    'ENTRY_005' => '<b>' . $entry_005_header . '</b>',
    'INST_001' => '<br>' . $entry_001_desc . '</br>',
    'INST_002' => '<ol class="list-no-margin"><li>' . $text_upload_limit . '</li></ol>',
    'INST_003' => 'Please prepare your Government Issued ID, Driving License, or Passport for the verification process. We will need your selfie too to perform a match with your ID.',
    'INST_003b' => '<ol class="list-no-margin"><li>' . $entry_003b_instruction1 . '</li><li>' . $entry_003b_instruction2 . '</li><li>' . $entry_003b_instruction3 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'INST_004' => '<br>' . $entry_004_instruction1 . '</br>',
    'INST_005' => '<ol class="list-no-margin"><li>' . $entry_005_instruction1 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'TEXT_VERIFICATION_TYPE_001' => 'Purchase authorization form',
    'TEXT_VERIFICATION_TYPE_002' => 'Utility bill',
    'TEXT_VERIFICATION_TYPE_003' => 'Photo Identification',
    'TEXT_VERIFICATION_TYPE_004' => 'Front of credit card',
    'TEXT_VERIFICATION_TYPE_005' => 'Image of Virtual Card / Mobile Payment App',
    'EMAIL_FUN_CONTENT' => 'Verification form successfully uploaded from customer, please review.',
    'EMAIL_FUN_SUBJECT' => 'Verification Form Uploaded From Customer <{SYS_CUSTOMER_ID}>',
    'SUCCESS_FILE_SAVED_SUCCESSFULLY' => '{SYS_FILENAME} upload saved successfully.',
    'TEXT_TAB_SUB_TITLE' => 'This is an electronic Know Your Customer (eKYC) process to help us confirm your identify and protect your account from unauthorised transactions.<br><br>Due to the recent changes in security policies by the authorities and our interest to adhere to the anti-money laundering (AML) laws, this eKYC measure is in place to ensure compliance with the new requirements. AML regulations require companies to complete due-diligence procedures to ensure they are not aiding in money laundering activities or financial crimes. Verification is necessary so we can minimise fraud and also to prevent the creation and use of fraudulent accounts.',
//    'TITLE_VERIFICATION_SUBMISSION_FORM' => 'VERIFICATION SUBMISSION FORM',
    'TITLE_VERIFICATION_SUBMISSION_FORM' => 'CONFIRM YOUR IDENTITY WITH ',
    'FAIL_BOTH_FILES_MUST_BE_UPLOADED' => 'You must upload both files to continue',
    'READ_GUIDE' => 'read guide',
    'CLOSE_GUIDE'=>'close guide',

    'IMAGE_SELECTED' => 'Image Selected',
    'Review in 48 hours' => 'Review in 48 hours.',
    'Failed' => 'Failed',
    'Error' => 'Error',
    'NO_DOC' => 'You are not required to submit any document at the moment.',
    'REVIEW_NOTE' => '<a href="{ORDER_LIST}" target="_blank">Check order</a> for updates or <a href= {CONTACT_TO} target="_blank">contact support</a>  if review is taking too long'
];
