<?php

return [
    'BTN_EMAIL_SECURITY_TOKEN' => 'Email token',
    'BTN_REQUEST_SECURITY_TOKEN' => 'Permintaan Token',
    'ERROR_INVALID_LAST_DIGIT' => '4 digit terakhir nomor telepon tidak valid.',
    'ERROR_REQUEST_TOKEN_PROVIDER_ERROR' => 'Ada kesalahan saat mengirim SMS. Mohon tunggu sebentar dan coba lagi atau hubungi kami.',
    'ERROR_TRIED_TOO_MANY_TIMES' => 'Anda telah berusaha untuk memverifikasi nomor terlalu banyak kali. Silahkan tunggu 24 jam sebelum kembali mencoba-atau klik pada "Butuh bantuan" link di bawah.',
    'ERROR_PLEASE_REQUEST_TOKEN' => 'Silakan meminta dan masukkan kata sandi sekali-pakai (OTP) yang dikirimkan kepada Anda melalui SMS.',
    'LIN<PERSON>_LOST_PHONE' => 'Hilang atau ponsel tidak valid?',
    'LINK_NEED_HELP' => 'Butuh bantuan?',
    'LINK_RESEND' => 'Kirim ulang',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => 'Masukkan token keamanan yang berisi 6 digit',
    'TEXT_ENTER_LAST_4_DIGIT' => 'Masukkan terakhir 4 digit nomor ponsel untuk menerima token keamanan dalam e-mail yang terdaftar.',
    'TEXT_INVALID_MOBILE' => 'Nomor ponsel tidak valid. Silakan gunakan nomor ponsel baru.',
    'TEXT_MOBILE_IS_IN_USED' => 'Nomor ponsel sudah digunakan di akun lain. Email telah dikirim ke {c_email}.',
    'TEXT_MOBILE_IS_IN_USED_NOMAIL' => 'Nomor ponsel sudah digunakan di akun lain.',
    'TEXT_PLEASE_SETUP_MOBILE' => 'Nomor ponsel yang anda berikan dalam digunakan atau tidak valid. Harap masukkan nomor ponsel lain. Klik <a class="fBlue3 f11" href="{SYS_PROFILE_LINK}"> di sini</a>',
    'TEXT_PLEASE_WAIT_FOR_X_SECOND' => 'Silahkan tunggu {second} seconds sebelum meminta untuk mengirim ulang lagi.',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => 'Anda telah melebihi batas yang diijinkan untuk token keamanan. Silakan coba lagi setelah 24 jam.',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_MSG2' => 'Token keamanan anda tidak valid. Silakan coba lagi.',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_TOO_MANY_ATTEMPTS' => 'Anda telah berusaha untuk memvalidasi token keamanan terlalu banyak kali. Silakan meminta token keamanan baru dan coba lagi.',
    'TEXT_REQUEST_TOKEN_REUSE_MSG' => 'Masukkan token keamanan yang sebelumnya dikirim ke: %s',
    'TEXT_REQUEST_TOKEN_SUCCESS_MSG' => 'Pesan teks dengan token keamanan anda telah dikirim ke: %s.',
    'TEXT_RESEND_TOKEN_SUCCESS_MSG' => 'Pesan teks dengan token keamanan anda telah dikirim ke: %s. Mulai ulang perangkat seluler Anda jika Anda tidak menerima token.',
    'TEXT_RESEND_IN_X_SEC' => 'Kirim ulang di {0} sec ...',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => 'Token keamanan baru anda adalah %s.Token ini akan berakhir pada %s dari saat anda menerima email ini.',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => 'Email dengan token keamanan telah dikirim ke: %s',
    'TEXT_TESTING_MODE' => 'Mode Pengujian: Token Keamanan',
    'TITLE_SECURITY_TOKEN' => 'TOKEN KEAMANAN',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => 'Token keamanan baru Anda adalah {SYS_SECURITY_TOKEN}',
    'TEXT_INVALID_MOBILE_COUNTRY' => 'Nomor ponsel negara/wilayah tidak valid',
    //------ mail translation ------\\
    //security token reset
    'Security Token Has Been Reset' => 'Token Keamanan Telah Diatur Ulang',
    'We have reset your Security Token and this token is valid for {HOURS} hours:' => 'Kami telah mengatur ulang Token Keamanan Anda dan token ini berlaku selama {HOURS} jam:',
    
    
    
];
