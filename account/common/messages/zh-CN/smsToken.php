<?php

return [
    'BTN_EMAIL_SECURITY_TOKEN' => '电邮验证码',
    'BTN_REQUEST_SECURITY_TOKEN' => '要求验证码',
    'ERROR_INVALID_LAST_DIGIT' => '号码后4位数字不正确。',
    'ERROR_REQUEST_TOKEN_PROVIDER_ERROR' => '短信发送错误。请稍等片刻后再试一次或联系我们。',
    'ERROR_TRIED_TOO_MANY_TIMES' => '您试图验证已到达限定次数。请在24小时后再重新尝试，或点击下面的“需要帮助”链接。',
    'ERROR_PLEASE_REQUEST_TOKEN' => '请索取并输入通过手机短信发送给您的一次性密码 (OTP)。',
    'LINK_LOST_PHONE' => '丢失或手机无效?',
    'LINK_NEED_HELP' => '需要帮忙？',
    'LINK_RESEND' => '重新发送',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '请输入6位数字验证码',
    'TEXT_ENTER_LAST_4_DIGIT' => '输入手机号码的最后4位数字,验证码将发送至您的注册电邮。',
    'TEXT_INVALID_MOBILE' => '无效手机号码，请使用其他手机号码。',
    'TEXT_MOBILE_IS_IN_USED' => '该手机号码已在另一个帐户中使用。电子邮件通知已发送至 {c_email}',
    'TEXT_MOBILE_IS_IN_USED_NOMAIL' => '该手机号码已在另一个帐户中使用。',
    'TEXT_PLEASE_SETUP_MOBILE' => '您提供的手机号码已使用或不存在。请提供另一个手机号码。 点击 <a class="fBlue3 f11" href="{SYS_PROFILE_LINK}">这里</a>',
    'TEXT_PLEASE_WAIT_FOR_X_SECOND' => '请稍候{second}秒後再重新发送。',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => '您的验证码使用次数已超过最大限制，请24小时后重试。',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_MSG2' => '验证码不正确，请重新输入。',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_TOO_MANY_ATTEMPTS' => '您试图确认验证码次数过多。请重新申请新的验证码。',
    'TEXT_REQUEST_TOKEN_REUSE_MSG' => '输入先前发送到%s 的验证码',
    'TEXT_REQUEST_TOKEN_SUCCESS_MSG' => '您的验证码信息已发送到：%s。',
    'TEXT_RESEND_TOKEN_SUCCESS_MSG' => '您的验证码信息已发送到：%s。如果没有收到验证码，请重启您的移动设备。',
    'TEXT_RESEND_IN_X_SEC' => '重新发送{0}秒...',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '您的新安全码是%s。 这安全码会在您收到这邮件的24小时过期。',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => '验证码已经发送至您的注册电邮：％s',
    'TEXT_TESTING_MODE' => '测试模式：验证码',
    'TITLE_SECURITY_TOKEN' => '验证码',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => '您的新验证码是 {SYS_SECURITY_TOKEN}',
    'TEXT_INVALID_MOBILE_COUNTRY' => '无效手机号码国家/地区',
    //------ mail translation ------\\
    //security token reset
    'Security Token Has Been Reset' => '密保已重置',
    'We have reset your Security Token and this token is valid for {HOURS} hours:' => '我们已重置您的密保，此密保有效期为{HOURS}小时：',
    
];
