<?php

return [
    'TEXT_STATEMENT_TYPE_B' => '奖金',
    'TEXT_STATEMENT_TYPE_C' => '赔偿',
    'TEXT_STATEMENT_TYPE_D' => '兑换',
    'TEXT_STATEMENT_TYPE_MI' => '手动添加',
    'TEXT_STATEMENT_TYPE_MR' => '手动扣除',
    'TEXT_STATEMENT_TYPE_P' => '购买',
    'TEXT_STATEMENT_TYPE_R' => '退还',
    'TEXT_STATEMENT_TYPE_V' => '购物代金券转换',
    'TEXT_STATEMENT_TYPE_X' => '取消',
    'TEXT_STATEMENT_TYPE_XS' => '额外购物代金券',
    'BTN_CONVERT_NOW' => '转换',
    'BTN_OTHER' => '其它',
    'BTN_REDEEM_NOW' => '立刻兑换',
    'BTN_TOPUP_NOW' => '马上充值',
    'BTN_VIEW_ORDER_DETAIL' => '订单信息',
    'DESCRIPTION_BLOCKED_COUNTRY'=> '您的IP地址无法访问此服务，就因为限制性政策和我们遵守政府对伊朗、古巴、朝鲜、刚果、利比亚、索马里、也门和苏丹的制裁。',
    'DESCRIPTION_CONTACT_US' => '如果您觉得这是一个错误，请 <a href=\'https://helpdesk.offgamers.com/support/tickets/new\'>联系我们</a>。',
    'ENTRY_CONVERT_TO' => '将货币转换为',
    'ENTRY_MY_CREDIT_BALANCE' => '购物代金券余额',
    'ERROR_INVALID_STORE_CREDIT_CURRENCY' => '抱歉，OffGamers已不再支持您所选的购物代金券货币币种。<br />请发电子邮件至<a href="mailto:<EMAIL>"><EMAIL></a>，提交新的首选默认货币币种。',
    'ERROR_MESSAGE_EMPTY_CURRENCY' => '请选择一个有效的货币。',
    'ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND' => '抱歉，您有一项等待支出的付款申请，所以暂时无法转换您的购物代金券币种。请于24小时后重试。',
    'ERROR_NO_REDEEM_GC_ALLOW_PENDING_PAYMENTS_FOUND' => '抱歉，您有一项等待支出的付款申请，所以暂时无法兑换礼品卡。请于24小时后重试。',
    'ERROR_NO_TOPUP_ALLOW_PENDING_PAYMENTS_FOUND' => '抱歉，您有一项等待支出的付款申请，所以暂时无法充值购物代金券。请于24小时后重试。',
    'ERROR_REDEEM_CUSTOMERS_GROUPS_NOT_ALLOW' => '您的会员资格不支持兑换礼品卡。请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。',
    'ERROR_REDEEM_FAIL' => '兑换失败。再次尝试或与联络我们。',
    'ERROR_REDEEM_FAIL_DEACTIVATED' => '此代码已被使用。如您已购买这个代码，请与我们联系。',
    'ERROR_REDEEM_FAIL_INVALID_CURRENCY' => '抱歉，我们不再支援以下的礼品卡货币：AED，SAR，KWD，PHP，JPY和HKD。',
    'ERROR_REDEEM_FAIL_REDEEMED' => '此代码已被使用。如您已购买这个代码，请与我们联系。',
    'ERROR_SC_CONVERSION' => '您无法转换您的购物代金券币种。请发电子邮件至<a href="mailto:<EMAIL>"><EMAIL></a>。',
    'ERROR_SC_NOT_AVAILABLE' => '抱歉，充值购物代金券服务暂时无法使用，请稍后再试',
    'ERROR_STORE_CREDIT_CONTACT_ADMIN' => '您的购物代金券账户出现问题。请<a href=\'https://helpdesk.offgamers.com/support/tickets/new\'>联系我们</a>寻求帮助。',
    'ERR_INVALID_STORE_CREDIT_CURRENCY_SELECTION' => '请选择一个有效的货币。',
    'ERR_MSG_ENTER_INVALID_SC_AMOUNT' => '您输入的购物代金券金额无效。',
    'HEADER_CONVERT_SUCCESSFUL' => '转换成功！',
    'HEADER_REDEEM_SUCCESS' => '兑换成功!',
    'HEADER_TOP_UP_SUCCESS' => '充值成功!',
    'LABEL_STATEMENT_DATE' => '{SYS_DATE}报表',
    'LINK_HOW_TO_REDEEM' => '如何兑换 {SYS_NAME}？',
    'LINK_KB_REDEEM_GC' => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884266-types-of-alternate-payment-options',
    'LINK_KB_TOPUP' => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884266-types-of-alternate-payment-options',
    'LINK_LEARN' => '了解如何充值在这里? <i class="fa fa-external-link-square"></i>',
    'LINK_TOPUP_STORE_CREDITS' => '充值购物代金券',
    'LINK_VIEW_STATEMENT' => '查看账单',
    'TEXT_CONVERT_NO_SC' => '您没有购物代金券。',
    'TEXT_GIFT_CARD_VALUE' => '礼品卡价值',
    'TEXT_MINIMUM_PURCHASE_OF_SC' => '最低购物代金券充值为',
    'TEXT_MAXIMUM_PURCHASE_OF_SC' => '最大购物代金券充值为',
    'TEXT_NO_HISTORY' => '在过去的 {SYS_NUM_MONTHS} 月中查询不到历史记录。获取代金券，<a href="{SYS_LINK}">请点击</a>。',
    'TEXT_NO_STATEMENT' => '没有任何报表于 {SYS_DATE}。 <a href="{SYS_LINK}">充值购物代金券</a>。',
    'TEXT_OGC_DESC' => '{SYS_NAME} 方案旨在取代传统礼券，为客户提供更好的便利。只需指尖轻轻一点，即可送礼物给爱人、朋友或同事。直接在线下单，兑换礼品对每个人来说都是理想的选择。',
    'TEXT_ORDER_ID' => '订单号码',
    'TEXT_PIN_CODE' => 'PIN码',
    'TEXT_PREVIOUS_BALANCE' => '以往购物代金券结余',
    'TEXT_SC_ACTIVITY_TYPE_CONVERT' => 'V',
    'TEXT_SC_CONVERT' => '购物代金券货币转换',
    'TEXT_SC_CONVERT_FROM_TO' => ' ( 从 {SYS_FROM_CUR} 到  {SYS_TO_CUR} )',
    'TEXT_SERIAL_NUMBER' => '编号',
    'TEXT_STATEMENT_TYPE_S' => '充值购物代金券',
    'TEXT_STORE_CREDIT' => '购物代金券',
    'TEXT_STORE_CREDIT_VALUE' => '购物代金券价值',
    'TEXT_TOPUP_INFO' => '使用购物代金券（SC）在OffGamers购物，作为替代现金或信用卡的付款方式。您的帐户中的现有的购物代金券通道，可以减少麻烦更快购买和预购。充值后账户中的购物代金券就可以使用。 {SYS_LINK_LEARN}',
    'TEXT_TOP_UP_AMOUNT' => '储值数量',
    'TEXT_TOP_UP_CURRENCY' => '选择货币',
    'TEXT_VALUE_AFTER_CONVERSION' => '转换后的价值',
    'TITLE_AMOUNT' => '数量',
    'TITLE_BALANCE' => '余额',
    'TITLE_CONVERT_STORE_CREDITS' => '转换购物代金券',
    'TITLE_DATE' => '日期',
    'TITLE_DESCRIPTION' => '描述',
    'TITLE_REDEEM_GIFT_CARD' => '兑换礼品卡',
    'TITLE_STATUS' => '状态',
    'TITLE_STORE_CREDITS_STATEMENT' => '购物代金券账单',
    'TITLE_STORE_CREDIT_BALANCE' => '购物代金券余额',
    'TITLE_TOPUP_HISTORY' => '储值活动',
    'TITLE_AMOUNT_REDEMPTION' => '兑换数量',
    'TEXT_CONFIRM' => '确认',
];
