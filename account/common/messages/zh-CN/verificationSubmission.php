<?php

$text_upload_limit = '允许的格式：JPG，GIF或PNG, 大小限制为{SYS_FILE_SIZE}kb。';

$entry_001_header = '面部自拍';
$entry_001_desc = '请勿佩戴墨镜，帽子或配饰。 背景必须是浅色或中性色。 请确保图像清晰，无反光，并且可以清晰看到您的脸部。';
$entry_002_header = '上传：与您的账单信息相符的水电费账单。';
$entry_003_header = '身份验证（eKYC）';
$entry_003_instruction1 = '请准备您由政府颁发的有效身份证、驾驶证或护照以进行验证。 还需要您的自拍照才能与您的证件进行匹配。';
$entry_003_instruction2 = '必须清晰显示文件上的信息。';
$entry_003_instruction3 = '不接受过期文件。';
$entry_003_instruction4 = '必須與您的信用卡或PayPal的帳單郵寄地址一致。';
$entry_003b_header = '上传2：持有身份证件的自拍照片。';
$entry_003b_instruction1 = '须直视镜头。';
$entry_003b_instruction2 = '您的样貌必须清晰可见。不要配戴太阳眼镜，帽子等。';
$entry_003b_instruction3 = '背景颜色必须是浅色和中性颜色。';
$entry_004_header = '扫描信用卡/借记卡的正面';
$entry_004_instruction1 = '您的信用卡必须在有效期内。 请显示您信用卡的前6位和后4位数字，中间数字可做遮挡。 请确保图像清晰，无反光，并且卡面内容必须可读。';
$entry_005_header = '上传：虚拟卡/移动支付应用程序图片。';
$entry_005_instruction1 = '截取您的虚拟卡或移动支付应用程序的屏幕截图。 （出于安全原因，请掩盖CVV）。';

return [
     'ENTRY_001' => '<b>' . $entry_001_header . '</b>',
    'ENTRY_002' => '<b>' . $entry_002_header . '</b>',
    'ENTRY_003' => '<b>' . $entry_003_header . '</b><br>',
    'ENTRY_003b' => '<b>' . $entry_003b_header . '</b>',
    'ENTRY_004' => '<b>' . $entry_004_header . '</b>',
    'ENTRY_005' => '<b>' . $entry_005_header . '</b>',
    'INST_001' => '<br>' . $entry_001_desc . '</br>',
    'INST_002' => '<ol class="list-no-margin"><li>' . $text_upload_limit . '</li></ol>',
    'INST_003' => '请准备好您的政府签发的身份证、执照或护照以便扫描。 <br> 我们将扫描您的自拍照，并使用面部识别技术匹配您的身份 ',
    'INST_003b' => '<ol class="list-no-margin"><li>' . $entry_003b_instruction1 . '</li><li>' . $entry_003b_instruction2 . '</li><li>' . $entry_003b_instruction3 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'INST_004' => '<br>' . $entry_004_instruction1 . '</br>',
    'INST_005' => '<ol class="list-no-margin"><li>' . $entry_005_instruction1 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'TEXT_VERIFICATION_TYPE_001' => '购买登记表格',
    'TEXT_VERIFICATION_TYPE_002' => '水电费账单',
    'TEXT_VERIFICATION_TYPE_003' => '带照片的有效证件',
    'TEXT_VERIFICATION_TYPE_004' => '信用卡正面',
    'TEXT_VERIFICATION_TYPE_005' => '虚拟卡/移动支付应用程序图片',
    'EMAIL_FUN_CONTENT' => 'Verification form successfully uploaded from customer, please review.',
    'EMAIL_FUN_SUBJECT' => 'Verification Form Uploaded From Customer <{SYS_CUSTOMER_ID}>',
    'SUCCESS_FILE_SAVED_SUCCESSFULLY' => '上传文件{SYS_FILENAME}已存盘。',
    'TEXT_TAB_SUB_TITLE' => '这是一个电子化认识你的客户（eKYC）程序，可帮助我们确认您的身份并保护您的帐户免遭未经授权的交易。<br><br>由于当局最近对安全政策进行了更改，并且我们有意遵守反洗钱（AML）法律，因此，该eKYC措施旨在确保符合新要求。《反洗钱条例》要求公司完成尽职调查程序，以确保它们不会协助洗钱活动或金融犯罪。核实是必要的，这样我们才能最大程度地减少欺诈，并防止创建和使用欺诈性帐户。',
    'TITLE_VERIFICATION_SUBMISSION_FORM' => '确认您的身份',
    'FAIL_BOTH_FILES_MUST_BE_UPLOADED' => '您必須上傳兩個文件才能繼續',
    'TITLE_VERIFICATION_SUBMISSION_FORM' => '验证',
    'READ_GUIDE' => '阅读指南',
    'CLOSE_GUIDE'=>'关闭指南',
    'IMAGE_SELECTED' =>    '选定图像',
    'Review in 48 hours' =>    '48小时内审查',
    'Failed' =>    '失败',
    'Error' =>     '错误',
    'NO_DOC' => '目前不需要您提交任何文档',
    'REVIEW_NOTE' => '如果审核时间过长，请<a href="{ORDER_LIST}" target="_blank">检看订单</a> 最新状态或 <a href= {CONTACT_TO} target="_blank">联系客服</a>'
];
