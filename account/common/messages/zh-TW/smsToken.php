<?php

return [
    'BTN_EMAIL_SECURITY_TOKEN' => '電郵驗證碼',
    'BTN_REQUEST_SECURITY_TOKEN' => '要求驗證碼',
    'ERROR_INVALID_LAST_DIGIT' => '號碼後4位數字不正確。',
    'ERROR_REQUEST_TOKEN_PROVIDER_ERROR' => '短信發送錯誤。請稍等片刻後再試一次或聯繫我們。',
    'ERROR_TRIED_TOO_MANY_TIMES' => '您試圖驗證已到達限定次數。請在24小時後再重新嘗試，或點擊下面的“需要幫助”鏈接。',
    'ERROR_PLEASE_REQUEST_TOKEN' => '请索取并输入通过手机短信发送给您的一次性密码 (OTP)。',
    'LINK_LOST_PHONE' => '丟失或手機無效?',
    'LINK_NEED_HELP' => '需要幫忙？',
    'LINK_RESEND' => '重新發送',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '請輸入6位數字驗證碼。',
    'TEXT_ENTER_LAST_4_DIGIT' => '輸入手機號碼的最後4位數字，驗證碼將發送至您的註冊電郵。',
    'TEXT_INVALID_MOBILE' => '无效手机号码，请使用其他手机号码。',
    'TEXT_MOBILE_IS_IN_USED' => '此手机号码已被占用，请使用其他手机号码。',
    'TEXT_PLEASE_SETUP_MOBILE' => '您提供的手機號碼已使用或不存在。請提供另一個手機號碼。點擊 <a class="fBlue3 f11" href="{SYS_PROFILE_LINK}">這裡</a>',
    'TEXT_PLEASE_WAIT_FOR_X_SECOND' => '請稍候{second}秒後再重新發送。',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => '您的驗證碼使用次數已超過最大限制，請24小時後重試。',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_MSG2' => '驗證碼不正確，請重新輸入。',
    'TEXT_REQUEST_TOKEN_INVALID_CODE_TOO_MANY_ATTEMPTS' => '您試圖驗證驗證碼次數過多。請重新申請新的驗證碼。',
    'TEXT_REQUEST_TOKEN_REUSE_MSG' => '輸入先前發送到%s 的驗證碼',
    'TEXT_REQUEST_TOKEN_SUCCESS_MSG' => '您的驗證碼信息已發送到：%s。',
    'TEXT_RESEND_TOKEN_SUCCESS_MSG' => '您的驗證碼信息已發送到：%s。如果没有收到验证码，请重启您的移动设备。',
    'TEXT_RESEND_IN_X_SEC' => '重新發送{0}秒...',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '您的新安全碼是%s。這安全碼會在您收到這郵件的24小時過期。',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => '驗證碼已經發送至您的註冊電郵：％s',
    'TEXT_TESTING_MODE' => '測試模式：驗證碼',
    'TEXT_INVALID_MOBILE_COUNTRY' => '無效手機號碼国家/地区',
    'TITLE_SECURITY_TOKEN' => '驗證碼',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => '您的新验证码是 {SYS_SECURITY_TOKEN}',
];
