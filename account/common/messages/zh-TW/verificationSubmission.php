<?php

$text_upload_limit = '允許的格式：JPG，GIF或PNG, 大小限制為{SYS_FILE_SIZE}。';

$entry_001_header = '上傳：購買登記表格。';
$entry_002_header = '上傳：與您的賬單信息相符的水電費賬單。';
$entry_003_header = '上傳1：身份証件或護照的照片。';
$entry_003_instruction1 = '隻接受政府頒發的身份証或護照。';
$entry_003_instruction2 = '必須清晰顯示文件上的信息。';
$entry_003_instruction3 = '不接受過期文件。';
$entry_003_instruction4 = '必须与您的信用卡或PayPal的帐单邮寄地址一致。';
$entry_003b_header = '上傳2：持有身份証件的自拍照片。';
$entry_003b_instruction1 = '須直視鏡頭。';
$entry_003b_instruction2 = '您的樣貌必須清晰可見。不要配戴太陽眼鏡，帽子等。';
$entry_003b_instruction3 = '背景顏色必須是淺色和中性顏色。';
$entry_004_header = '上傳：信用卡正面。';
$entry_004_instruction1 = '显示信用卡正面前6位数字和后4位数字的复印件 <a href="http://kb.offgamers.com/zhcn/?p=141" target="_blank">点击这里查看验证指南</a>';
$entry_005_header = '上傳：信用卡背面。';
$entry_005_instruction1 = '显示信用卡背面签名的复印件。（为安全起见，请掩盖CVV和信用卡背面的印刷数字。）<a href="http://kb.offgamers.com/zhcn/?p=141" target="_blank">点击这里查看验证指南</a>';

return [
    'ENTRY_001' => '<b>' . $entry_001_header . '</b><ol class="list-no-margin"><li>' . $text_upload_limit . '</li></ol>',
    'ENTRY_002' => '<b>' . $entry_002_header . '</b><ol class="list-no-margin"><li>' . $text_upload_limit . '</li></ol>',
    'ENTRY_003' => '<b>' . $entry_003_header . '</b><ol class="list-no-margin"><li>' . $entry_003_instruction1 . '</li><li>' . $entry_003_instruction2 . '</li><li>' . $entry_003_instruction3 . '</li><li>' . $entry_003_instruction4 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'ENTRY_003b' => '<b>' . $entry_003b_header . '</b><ol class="list-no-margin"><li>' . $entry_003b_instruction1 . '</li><li>' . $entry_003b_instruction2 . '</li><li>' . $entry_003b_instruction3 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'ENTRY_004' => '<b>' . $entry_004_header . '</b><ol class="list-no-margin"><li>' . $entry_004_instruction1 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'ENTRY_005' => '<b>' . $entry_005_header . '</b><ol class="list-no-margin"><li>' . $entry_005_instruction1 . '</li><li>' . $text_upload_limit . '</li></ol>',
    'TEXT_VERIFICATION_TYPE_001' => '購買登記表格',
    'TEXT_VERIFICATION_TYPE_002' => '水電費賬單',
    'TEXT_VERIFICATION_TYPE_003' => '帶照片的有效證件',
    'TEXT_VERIFICATION_TYPE_004' => '信用卡正面',
    'TEXT_VERIFICATION_TYPE_005' => '信用卡背面',
    'EMAIL_FUN_CONTENT' => 'Verification form successfully uploaded from customer, please review.',
    'EMAIL_FUN_SUBJECT' => 'Verification Form Uploaded From Customer <{SYS_CUSTOMER_ID}>',
    'SUCCESS_FILE_SAVED_SUCCESSFULLY' => '上載文件{SYS_FILENAME}已存盤。',
    'TEXT_TAB_SUB_TITLE' => '為確保身份驗證過程安全順暢，請確保您所提交的所有文件都正確無誤。<br><br>由于当局最近对安全政策进行了更改，并且我们有意遵守反洗钱（AML）法律，因此，该eKYC措施旨在确保符合新要求。《反洗钱条例》要求公司完成尽职调查程序，以确保它们不会协助洗钱活动或金融犯罪。核实是必要的，这样我们才能最大程度地减少欺诈，并防止创建和使用欺诈性帐户。',
    'TITLE_VERIFICATION_SUBMISSION_FORM' => '身份驗證提交表',
    'FAIL_BOTH_FILES_MUST_BE_UPLOADED' => '您必须上传两个文件才能继续',
];
