<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%address_book}}".
 *
 * @property int $address_book_id
 * @property int $customers_id
 * @property string $entry_gender
 * @property string $entry_company
 * @property string $entry_firstname
 * @property string $entry_lastname
 * @property string $entry_street_address
 * @property string $entry_suburb
 * @property string $entry_postcode
 * @property string $entry_city
 * @property string $entry_state
 * @property int $entry_country_id
 * @property int $entry_zone_id
 */
class AddressBook extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%address_book}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'entry_country_id', 'entry_zone_id'], 'integer'],
            [['entry_gender'], 'string', 'max' => 1],
            [['entry_company', 'entry_firstname', 'entry_lastname', 'entry_suburb', 'entry_city', 'entry_state'], 'string', 'max' => 32],
            [['entry_street_address'], 'string', 'max' => 64],
            [['entry_postcode'], 'string', 'max' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'address_book_id' => 'Address Book',
            'customers_id' => 'Customers',
            'entry_gender' => 'Entry Gender',
            'entry_company' => 'Entry Company',
            'entry_firstname' => 'Entry Firstname',
            'entry_lastname' => 'Entry Lastname',
            'entry_street_address' => 'Entry Street Address',
            'entry_suburb' => 'Entry Suburb',
            'entry_postcode' => 'Entry Postcode',
            'entry_city' => 'Entry City',
            'entry_state' => 'Entry State',
            'entry_country_id' => 'Entry Country',
            'entry_zone_id' => 'Entry Zone',
        ];
    }

    public function getAddressInfo($addressBookId = 0, $customerId = 0) {
        $customerData = '';
        $addNewAddressBookData = false;
        //customerId might use by API purpose.
        $customerId = $customerId == 0 ? Yii::$app->user->id : $customerId;

        if (!$addressBookId) {
            $customerData = Customers::findOne([
                'customers_id' => $customerId
            ]);
            if ($customerData) {
                $addressBookId = $customerData->customers_default_address_id;
            }
        }

        $addressBookData = static::findOne([
            'address_book_id' => $addressBookId,
            'customers_id' => $customerId
        ]);
        if (!$addressBookData) {
            $addNewAddressBookData = true;
        }

        if ($addNewAddressBookData === true) {
            if (!$customerData) {
                $customerData = Customers::findOne([
                    'customers_id' => $customerId
                ]);
            }

            if ($customerData) {
                $saveData = array(
                    'customers_id' => $customerId,
                    'entry_gender' => $customerData->customers_gender,
                    'entry_firstname' => $customerData->customers_firstname,
                    'entry_lastname' => $customerData->customers_lastname
                );

                if ($this->saveNewRecord($saveData)) {
                    $customerData->customers_default_address_id = $this->address_book_id;
                    $customerData->update(true, ['customers_default_address_id']);
                    $addressBookData = $this;
                }
            }
        }
        return $addressBookData;
    }

    public function getEntryCountryId($addressId, $customer_id = 0) {
        $customer_id = $customer_id == 0 ? Yii::$app->user->id : $customer_id;
        
        $result = static::find()->select('entry_country_id')->where([
            'address_book_id' => $addressId,
            'customers_id' => $customer_id,
        ])->one();

        return isset($result->entry_country_id) ? $result->entry_country_id : null;
    }

    public function getUserAddress($addr_id, $key = array()) {
        return AddressBook::find()->select($key ? $key : "*")->where([
            'address_book_id' => (int) $addr_id,
        ])->asArray()->one();
    }

    public function updateCustomerInfo($customerInfo, $addressBookId, $customerId = 0) {
        //customerId might use by API purpose.
        $customerId = $customerId == 0 ? Yii::$app->user->id : $customerId;

        $attributes = $customerInfo;
        static::updateAll($attributes, [
            'customers_id' => $customerId,
            'address_book_id' => $addressBookId,
        ]);
    }

}
