<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "analysis_credit_card_info".
 *
 * @property integer $orders_id
 * @property string $fomatted_pan
 * @property string $bin_number
 * @property string $card_summary
 * @property string $expiry
 * @property string $three_d_offered
 * @property string $three_d_result
 * @property string $issuer_country
 * @property string $created_at
 */
class AnalysisCreditCardInfo extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'analysis_credit_card_info';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id', 'created_at'], 'required'],
            [['orders_id'], 'integer'],
            [['created_at'], 'safe'],
            [['fomatted_pan'], 'string', 'max' => 32],
            [['bin_number'], 'string', 'max' => 6],
            [['card_summary'], 'string', 'max' => 4],
            [['expiry'], 'string', 'max' => 7],
            [['three_d_offered'], 'string', 'max' => 5],
            [['three_d_result'], 'string', 'max' => 11],
            [['issuer_country'], 'string', 'max' => 2],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => Yii::t('analysis_credit_card_info', 'Orders ID'),
            'fomatted_pan' => Yii::t('analysis_credit_card_info', 'Fomatted Pan'),
            'bin_number' => Yii::t('analysis_credit_card_info', 'Bin Number'),
            'card_summary' => Yii::t('analysis_credit_card_info', 'Card Summary'),
            'expiry' => Yii::t('analysis_credit_card_info', 'Expiry'),
            'three_d_offered' => Yii::t('analysis_credit_card_info', 'Three D Offered'),
            'three_d_result' => Yii::t('analysis_credit_card_info', 'Three D Result'),
            'issuer_country' => Yii::t('analysis_credit_card_info', 'Issuer Country'),
            'created_at' => Yii::t('analysis_credit_card_info', 'Created At'),
        ];
    }
}
