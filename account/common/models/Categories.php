<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%categories}}".
 *
 * @property int $categories_id
 * @property int $parent_id
 * @property string $categories_parent_path
 * @property int $sort_order
 * @property string $date_added
 * @property string $last_modified
 * @property int $categories_pinned
 * @property int $categories_status
 * @property int $c2c_categories_status
 * @property string $categories_url_alias
 * @property int $categories_auto_seo
 * @property string $categories_auto_seo_type
 * @property int $products_count
 * @property int $custom_products_type_id
 * @property int $custom_products_type_child_id
 * @property int $categories_types_groups_id
 * @property int $categories_buyback_main_cat
 */
class Categories extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%categories}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['parent_id', 'sort_order', 'categories_pinned', 'categories_status', 'c2c_categories_status', 'categories_auto_seo', 'custom_products_type_id', 'custom_products_type_child_id', 'categories_types_groups_id', 'categories_buyback_main_cat'], 'integer'],
            [['date_added', 'last_modified'], 'safe'],
            [['categories_parent_path', 'categories_url_alias'], 'string', 'max' => 255],
            [['categories_pinned', 'categories_status', 'c2c_categories_status', 'categories_auto_seo', 'categories_buyback_main_cat'], 'string', 'max' => 1],
            [['categories_auto_seo_type'], 'string', 'max' => 64],
            [['products_count'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories',
            'parent_id' => 'Parent',
            'categories_parent_path' => 'Categories Parent Path',
            'sort_order' => 'Sort Order',
            'date_added' => 'Date Added',
            'last_modified' => 'Last Modified',
            'categories_pinned' => 'Categories Pinned',
            'categories_status' => 'Categories Status',
            'c2c_categories_status' => 'C2c Categories Status',
            'categories_url_alias' => 'Categories Url Alias',
            'categories_auto_seo' => 'Categories Auto Seo',
            'categories_auto_seo_type' => 'Categories Auto Seo Type',
            'products_count' => 'Products Count',
            'custom_products_type_id' => 'Custom Products Type',
            'custom_products_type_child_id' => 'Custom Products Type Child',
            'categories_types_groups_id' => 'Categories Types Groups',
            'categories_buyback_main_cat' => 'Categories Buyback Main Cat',
        ];
    }
    
    public function getParentId($categoryId) {
        $result = static::find()->select('parent_id')->where([
            'categories_id' => $categoryId,
        ])->andWhere(['<>', 'parent_id', '0'])->one();
        return isset($result->parent_id) ? $result->parent_id : null;
    }

    public function storeCreditProductID($grpid) {
        $result = false;
        
        $c = Yii::$app->db;
        $sql = "SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
                FROM " . Categories::model()->tableName() . " AS c 
                INNER JOIN " . CategoriesGroups::model()->tableName() . " AS cg 
                    ON (c.categories_id = cg.categories_id) 
                WHERE c.categories_status = 1 
                    AND c.custom_products_type_id = '3'
                    AND ((cg.groups_id = '" . (int) $grpid . "') OR (cg.groups_id = 0)) 
                ORDER BY c.categories_id";
        $res = $c->createCommand($sql)->query();
        if (($row = $res->read()) !== false) {
            if ($row["parent_id"] > 0) {
                $cPath = substr($row["categories_parent_path"], 1) . $row["categories_id"];
            } else {
                $cPath = $row["categories_id"];
            }

            $cat = explode("_", $cPath);
            $prod_sql = "   SELECT DISTINCT(p.products_id)
                            FROM " . Products::model()->tableName() . " AS p
                            INNER JOIN " . ProductsToCategories::model()->tableName() . " AS ptc
                                ON (p.products_id = ptc.products_id)
                            INNER JOIN " . CategoriesGroups::model()->tableName() . " AS cg
                                ON (cg.categories_id = ptc.categories_id)
                            INNER JOIN " . Categories::model()->tableName() . " AS c
                                ON (c.categories_id = ptc.categories_id AND c.categories_status = 1)
                            WHERE ptc.categories_id IN ('" . implode("', '", $cat) . "')
                                AND p.custom_products_type_id = 3
                                AND p.products_status = 1
                                AND p.products_display = 1
                                AND ((cg.groups_id = '" . (int) $grpid . "') OR (cg.groups_id = 0))
                            ORDER BY p.products_sort_order";
            $prod_res = $c->createCommand($prod_sql)->query();
            if (($prod_row = $prod_res->read()) !== false) {
                $result = $prod_row["products_id"];
            }
        }

        return $result;
    }

}
