<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%categories_groups}}".
 *
 * @property int $linkid
 * @property int $categories_id
 * @property int $groups_id
 */
class CategoriesGroups extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%categories_groups}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['categories_id', 'groups_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'linkid' => 'Linkid',
            'categories_id' => 'Categories',
            'groups_id' => 'Groups',
        ];
    }
}
