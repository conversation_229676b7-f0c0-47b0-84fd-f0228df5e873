<?php

namespace common\models;

use Yii;
use \common\components\CoreConfigurationModel;


/**
 * This is the model class for table "{{%configuration}}".
 *
 * @property int $configuration_id
 * @property string $configuration_title
 * @property string $configuration_key
 * @property string $configuration_value
 * @property string $configuration_description
 * @property int $configuration_group_id
 * @property int $sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property string $use_function
 * @property string $set_function
 */
class Configuration extends \common\components\CoreConfigurationModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%configuration}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['configuration_value'], 'required'],
            [['configuration_group_id', 'sort_order'], 'integer'],
            [['last_modified', 'date_added', 'set_function'], 'safe'],
            [['configuration_title', 'configuration_key'], 'string', 'max' => 64],
            [['configuration_description', 'use_function'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'configuration_id' => 'Configuration',
            'configuration_title' => 'Configuration Title',
            'configuration_key' => 'Configuration Key',
            'configuration_value' => 'Configuration Value',
            'configuration_description' => 'Configuration Description',
            'configuration_group_id' => 'Configuration Group',
            'sort_order' => 'Sort Order',
            'last_modified' => 'Last Modified',
            'date_added' => 'Date Added',
            'use_function' => 'Use Function',
            'set_function' => 'Set Function',
        ];
    }
}
