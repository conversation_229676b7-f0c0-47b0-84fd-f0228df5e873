<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%countries}}".
 *
 * @property int $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property int $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property int $address_format_id
 * @property int $maxmind_support
 * @property string $aft_risk_type
 * @property int $countries_display
 * @property int $telesign_support
 */
class Countries extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%countries}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['countries_currencies_id', 'address_format_id'], 'integer'],
            [['countries_name', 'countries_website_domain'], 'string', 'max' => 64],
            [['countries_iso_code_2'], 'string', 'max' => 2],
            [['countries_iso_code_3'], 'string', 'max' => 3],
            [['countries_international_dialing_code'], 'string', 'max' => 5],
            [['maxmind_support', 'countries_display', 'telesign_support'], 'string', 'max' => 1],
            [['aft_risk_type'], 'string', 'max' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'countries_id' => 'Countries',
            'countries_name' => 'Countries Name',
            'countries_iso_code_2' => 'Countries Iso Code 2',
            'countries_iso_code_3' => 'Countries Iso Code 3',
            'countries_currencies_id' => 'Countries Currencies',
            'countries_international_dialing_code' => 'Countries International Dialing Code',
            'countries_website_domain' => 'Countries Website Domain',
            'address_format_id' => 'Address Format',
            'maxmind_support' => 'Maxmind Support',
            'aft_risk_type' => 'Aft Risk Type',
            'countries_display' => 'Countries Display',
        ];
    }
    
    public function getAllCountries($include_hidden = false) {
        $cacheKey = Yii::$app->params['MEMCACHE_PREFIX'] . Countries::model()->tableName() . ($include_hidden ? '/all' : '/display') . '/array';
        $country = Yii::$app->cache->get($cacheKey);

        if ($country === false) {
            $q = static::find()->select('countries_id, countries_name, countries_iso_code_2, countries_international_dialing_code')->asArray()->orderBy('countries_name');
            if (!$include_hidden) {
                $q->where([
                    'countries_display' => 1,
                ]);
            }
            $country = $q->indexBy('countries_id')->all();
            
            Yii::$app->cache->set($cacheKey, $country, 86400);
        }

        return $country;
    }
    
    public static function getCountryDetails($country_id) {
        $cache_key = static::tableName() . "/$country_id/array";
        $result = \Yii::$app->cache->get($cache_key);
        if ($result === false) {
            $result = static::find()->where([
                'countries_id' => $country_id,
            ])->asArray()->one();
            if ($result) {
                \Yii::$app->cache->set($cache_key, $result, 86400);
            }
        }
        return $result;
    }

    public function getCountryName($countryId) {
        return static::find()->select('countries_name')->where([
            'countries_id' => $countryId,
        ])->one();
    }

    public function getDialingInfo($countryId) {
        return static::find()->select('countries_name, countries_international_dialing_code')->where([
            'countries_id' => $countryId,
        ])->one();
    }

    public function getCountryIdByDialingCode($dialingCode) {
        $result = static::find()->select('countries_id')->where([
            'countries_international_dialing_code' => $dialingCode,
        ])->one();
        return isset($result->countries_id) ? $result->countries_id : null;
    }

    public function getCountryDialingCodeById($countryId) {
        $result = static::find()->select('countries_international_dialing_code')->where([
            'countries_id' => $countryId,
        ])->one();
        return isset($result->countries_international_dialing_code) ? $result->countries_international_dialing_code : null;
    }

    public static function getCountryIsoCodeByCountryId($country_id) {
        $country_detail = static::getCountryDetails($country_id);
        return !empty($country_detail['countries_iso_code_2']) ? $country_detail['countries_iso_code_2'] : null;
    }
}
