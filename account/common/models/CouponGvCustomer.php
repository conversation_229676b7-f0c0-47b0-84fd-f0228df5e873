<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%coupon_gv_customer}}".
 *
 * @property int $customer_id
 * @property string $amount
 * @property string $sc_reversible_amount
 * @property string $sc_reversible_reserve_amount
 * @property string $sc_irreversible_amount
 * @property string $sc_last_modified
 * @property string $sc_irreversible_reserve_amount
 * @property int $sc_currency_id
 * @property string $sc_conversion_date
 */
class CouponGvCustomer extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%coupon_gv_customer}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'sc_currency_id'], 'integer'],
            [['amount'], 'string', 'max' => 8],
            [['sc_reversible_amount', 'sc_reversible_reserve_amount', 'sc_irreversible_amount', 'sc_irreversible_reserve_amount'], 'string', 'max' => 15],
            [['sc_last_modified', 'sc_conversion_date'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customer_id' => 'Customer',
            'amount' => 'Amount',
            'sc_reversible_amount' => 'Sc Reversible Amount',
            'sc_reversible_reserve_amount' => 'Sc Reversible Reserve Amount',
            'sc_irreversible_amount' => 'Sc Irreversible Amount',
            'sc_last_modified' => 'Sc Last Modified',
            'sc_irreversible_reserve_amount' => 'Sc Irreversible Reserve Amount',
            'sc_currency_id' => 'Sc Currency',
            'sc_conversion_date' => 'Sc Conversion Date',
        ];
    }
    
    public function getScCurrency() {
        $m_data = static::findOne([
            'customer_id' => Yii::$app->user->id,
        ]);
        return (isset($m_data->sc_currency_id) ? $m_data->sc_currency_id : false);
    }

    public function getScInfo() {
        return static::find()->select('sc_reversible_amount,sc_reversible_reserve_amount,sc_irreversible_amount,sc_irreversible_reserve_amount,sc_currency_id')->where([
            'customer_id' => Yii::$app->user->id,
        ])->one();
    }

    public function scUpdateInfo($updateArr) {
        static::updateAll($updateArr, [
            'customer_id' => Yii::$app->user->id,
        ]);
    }

}
