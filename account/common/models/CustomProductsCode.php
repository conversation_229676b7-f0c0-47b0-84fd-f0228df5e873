<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%custom_products_code}}".
 *
 * @property int $custom_products_code_id
 * @property int $products_id
 * @property int $orders_products_id
 * @property int $status_id
 * @property string $file_name
 * @property string $file_type
 * @property string $code_date_added
 * @property string $code_date_modified
 * @property string $code_uploaded_by
 * @property string $remarks
 * @property int $custom_products_code_viewed
 * @property int $purchase_orders_id
 * @property int $to_s3
 */
class CustomProductsCode extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%custom_products_code}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['products_id', 'orders_products_id', 'status_id', 'custom_products_code_viewed'], 'integer'],
            [['code_date_added', 'code_date_modified'], 'safe'],
            [['remarks'], 'required'],
            [['file_name'], 'string', 'max' => 50],
            [['file_type'], 'string', 'max' => 5],
            [['code_uploaded_by'], 'string', 'max' => 65],
            [['purchase_orders_id'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'custom_products_code_id' => 'Custom Products Code',
            'products_id' => 'Products',
            'orders_products_id' => 'Orders Products',
            'status_id' => 'Status',
            'file_name' => 'File Name',
            'file_type' => 'File Type',
            'code_date_added' => 'Code Date Added',
            'code_date_modified' => 'Code Date Modified',
            'code_uploaded_by' => 'Code Uploaded By',
            'remarks' => 'Remarks',
            'custom_products_code_viewed' => 'Custom Products Code Viewed',
            'purchase_orders_id' => 'Purchase Orders',
        ];
    }
    
    public function getProductCodeInfo($assignedCdkeyArr) {
        $idlist = array();
        foreach($assignedCdkeyArr as $k => $v) {
            $idlist[] = (int) trim($v);
        }
        return static::find()->select('custom_products_code_id, file_name, file_type, orders_products_id')->where([
            'custom_products_code_id' => $idlist,
        ])->orderBy('code_date_added asc, file_name asc')->all();
    }

    public function getProductCode($cpId) {
        return static::find()->select('file_type, custom_products_code_id, file_name, orders_products_id, custom_products_code_viewed, to_s3, products_id, code_date_added')->where([
            'custom_products_code_id' => $cpId,
        ])->one();
    }

    public function updateData($attribute, $cpId) {
        static::updateAll($attribute, [
            'custom_products_code_id' => $cpId,
        ]);
    }

}
