<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_document_id_verification}}".
 *
 * @property int $id
 * @property int $customer_id
 * @property string $info_key
 * @property boolean $locked
 * @property int $updated_at
 * 
 * @property Customers $customers
 */

class CustomerInfoLock extends \common\components\CoreModel{
const DOB_LOCK_KEY = 'customers.customers_dob';

    public function behaviors() {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::className(),
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ]
            ],
        ];
    }
    
    public static function tableName() {
        
         return '{{%customer_info_lock}}';
    }
    
    public function rules() {
        
        return [
            [['customer_id'], 'integer'],
            [['info_key'], 'string', 'max' => 256],
            [['locked'],'boolean'],
            [['locked'],'default', 'value' => '0' ]
        ];
    }
    
    public function attributeLabels() {
        return [
            'customer_id' => 'Customers',
            'info_key' => 'Info Key',
            'locked' => 'Lock Status',
            'updated_at' => 'Updated at'
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomers() {
        return $this->hasOne(Customers::className(), ['customers_id' => 'customer_id']);
    }
    
}