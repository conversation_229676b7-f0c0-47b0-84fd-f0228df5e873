<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customer_pipwave_verification_doc_meta".
 *
 * @property integer $document_meta_id
 * @property integer $document_id
 * @property string $meta_type
 * @property string $meta_label
 * @property string $meta_value
 */
class CustomerPipwaveVerificationDocMetaModel extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'customer_pipwave_verification_doc_meta';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['document_id'], 'integer'],
            [['meta_type', 'meta_label'], 'string', 'max' => 100],
            [['meta_value'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'document_meta_id' => Yii::t('pipwave_verification', 'Document Meta ID'),
            'document_id' => Yii::t('pipwave_verification', 'Document ID'),
            'meta_type' => Yii::t('pipwave_verification', 'Meta Type'),
            'meta_label' => Yii::t('pipwave_verification', 'Meta Label'),
            'meta_value' => Yii::t('pipwave_verification', 'Meta Value'),
        ];
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocMetaQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new CustomerPipwaveVerificationDocMetaQuery(get_called_class());
    }
}
