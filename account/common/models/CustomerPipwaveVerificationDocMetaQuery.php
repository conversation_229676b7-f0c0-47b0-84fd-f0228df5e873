<?php

namespace common\models;

/**
 * This is the ActiveQuery class for [[CustomerPipwaveVerificationDocMetaModel]].
 *
 * @see CustomerPipwaveVerificationDocMetaModel
 */
class CustomerPipwaveVerificationDocMetaQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocMetaModel[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocMetaModel|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
