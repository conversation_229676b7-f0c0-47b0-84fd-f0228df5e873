<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customer_pipwave_verification_doc".
 *
 * @property integer $document_id
 * @property integer $verification_id
 * @property string $document_type
 * @property string $subject_filename
 * @property integer $subject_size
 * @property string $subject_url
 * @property string $full_filename
 * @property string $full_size
 * @property string $full_url
 * @property string $document_scoring
 * @property integer $result
 */
class CustomerPipwaveVerificationDocModel extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'customer_pipwave_verification_doc';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['verification_id', 'subject_size', 'result'], 'integer'],
            [['document_scoring'], 'number'],
            [['document_type'], 'string', 'max' => 30],
            [['subject_filename',  'full_filename', 'full_size' ], 'string', 'max' => 250],
            [ [ 'full_url', 'subject_url' ], 'string'  ]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'document_id' => Yii::t('pipwave_verification', 'Document ID'),
            'verification_id' => Yii::t('pipwave_verification', 'Verification ID'),
            'document_type' => Yii::t('pipwave_verification', 'Document Type'),
            'subject_filename' => Yii::t('pipwave_verification', 'Subject Filename'),
            'subject_size' => Yii::t('pipwave_verification', 'Subject Size'),
            'subject_url' => Yii::t('pipwave_verification', 'Subject Url'),
            'full_filename' => Yii::t('pipwave_verification', 'Full Filename'),
            'full_size' => Yii::t('pipwave_verification', 'Full Size'),
            'full_url' => Yii::t('pipwave_verification', 'Full Url'),
            'document_scoring' => Yii::t('pipwave_verification', 'Document Scoring'),
            'result' => Yii::t('pipwave_verification', 'Result'),
        ];
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new CustomerPipwaveVerificationDocQuery(get_called_class());
    }

    public function getDocMeta(){
        return $this->hasMany(common\models\CustomerPipwaveVerificationDocMetaModel::className(), [ 'document_id'=>'document_id' ] );
    }
}
