<?php

namespace common\models;

/**
 * This is the ActiveQuery class for [[CustomerPipwaveVerificationDocModel]].
 *
 * @see CustomerPipwaveVerificationDocModel
 */
class CustomerPipwaveVerificationDocQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocModel[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationDocModel|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
