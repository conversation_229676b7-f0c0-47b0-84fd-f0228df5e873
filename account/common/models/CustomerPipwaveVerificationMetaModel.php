<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customer_pipwave_verification_meta".
 *
 * @property integer $verification_meta_id
 * @property integer $verification_id
 * @property string $meta_type
 * @property string $meta_label
 * @property string $meta_value
 */
class CustomerPipwaveVerificationMetaModel extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'customer_pipwave_verification_meta';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['verification_id'], 'integer'],
            [['meta_type', 'meta_label'], 'string', 'max' => 100],
            [['meta_value'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'verification_meta_id' => Yii::t('pipwave_verification', 'Verification Meta ID'),
            'verification_id' => Yii::t('pipwave_verification', 'Verification ID'),
            'meta_type' => Yii::t('pipwave_verification', 'Meta Type'),
            'meta_label' => Yii::t('pipwave_verification', 'Meta Label'),
            'meta_value' => Yii::t('pipwave_verification', 'Meta Value'),
        ];
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationMetaQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new CustomerPipwaveVerificationMetaQuery(get_called_class());
    }
}
