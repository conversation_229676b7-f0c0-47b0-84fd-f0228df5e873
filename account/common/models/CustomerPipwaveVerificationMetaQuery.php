<?php

namespace common\models;

/**
 * This is the ActiveQuery class for [[CustomerPipwaveVerificationMetaModel]].
 *
 * @see CustomerPipwaveVerificationMetaModel
 */
class CustomerPipwaveVerificationMetaQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationMetaModel[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationMetaModel|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
