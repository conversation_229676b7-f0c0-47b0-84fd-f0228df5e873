<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customer_pipwave_verification".
 *
 * @property integer $verification_id
 * @property string $session_token
 * @property string $verification_type
 * @property integer $customer_id
 * @property integer $attempts_num
 * @property string $ip_address
 * @property string $mode
 * @property string $user_agent
 * @property integer $verification_status
 * @property string $approval_status
 * @property string $approval_status_updated_by
 * @property string $pd_id
 * @property string $document_set_scoring
 * @property string $started_at
 * @property string $completed_at
 * @property string $api_key
 * @property string $timestamp
 * @property string $signature
 * @property string $face_id
 * @property string $extra_param1
 * @property string $extra_param2
 * @property string $extra_param3
 */
class CustomerPipwaveVerificationModel extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'customer_pipwave_verification';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'attempts_num', 'verification_status'], 'integer'],
            [['document_set_scoring'], 'number'],
            [['started_at', 'completed_at', 'timestamp'], 'safe'],
            [['extra_param1', 'extra_param2', 'extra_param3'], 'string'],
            [['session_token'], 'string', 'max' => 64],
            [['verification_type','approval_status', 'approval_status_updated_by', 'pd_id', 'api_key', 'signature', 'face_id'], 'string', 'max' => 50],
            [['ip_address','mode'], 'string', 'max' => 128],
            [['user_agent'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'verification_id' => Yii::t('pipwave_verification', 'Verification ID'),
            'session_token' => Yii::t('pipwave_verification', 'Session Token'),
            'verification_type' => Yii::t('pipwave_verification', 'Verification Type'),
            'customer_id' => Yii::t('pipwave_verification', 'Customer ID'),
            'attempts_num' => Yii::t('pipwave_verification', 'Attempts Num'),
            'ip_address' => Yii::t('pipwave_verification', 'Ip Address'),
            'mode' => Yii::t('pipwave_verification', 'Model'),
            'user_agent' => Yii::t('pipwave_verification', 'User Agent'),
            'verification_status' => Yii::t('pipwave_verification', 'Verification Status'),
            'approval_status' => Yii::t('pipwave_verification', 'Appproval Status'),
            "approval_status_updated_by" => Yii::t('pipwave_verification', 'Appproval Status Updated By'),
            'pd_id' => Yii::t('pipwave_verification', 'Pd ID'),
            'document_set_scoring' => Yii::t('pipwave_verification', 'Document Set Scoring'),
            'started_at' => Yii::t('pipwave_verification', 'Started At'),
            'completed_at' => Yii::t('pipwave_verification', 'Completed At'),
            'api_key' => Yii::t('pipwave_verification', 'Api Key'),
            'timestamp' => Yii::t('pipwave_verification', 'Timestamp'),
            'signature' => Yii::t('pipwave_verification', 'Signature'),
            'face_id' => Yii::t('pipwave_verification', 'Face ID'),
            'extra_param1' => Yii::t('pipwave_verification', 'Extra Param1'),
            'extra_param2' => Yii::t('pipwave_verification', 'Extra Param2'),
            'extra_param3' => Yii::t('pipwave_verification', 'Extra Param3'),
        ];
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new CustomerPipwaveVerificationQuery(get_called_class());
    }

    public function getMeta(){
        return $this->hasMany(common\models\CustomerPipwaveVerificationMetaModel::className(), [ 'verification_id'=>'verification_id' ] );
    }

    public  function getDoc(){
        return $this->hasMany(common\models\CustomerPipwaveVerificationDocModel::className(), [ 'verification_id'=>'verification_id' ] );
    }
}
