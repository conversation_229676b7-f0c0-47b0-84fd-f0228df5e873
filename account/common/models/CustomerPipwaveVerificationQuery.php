<?php

namespace common\models;

/**
 * This is the ActiveQuery class for [[CustomerPipwaveVerificationModel]].
 *
 * @see CustomerPipwaveVerificationModel
 */
class CustomerPipwaveVerificationQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationModel[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return CustomerPipwaveVerificationModel|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
