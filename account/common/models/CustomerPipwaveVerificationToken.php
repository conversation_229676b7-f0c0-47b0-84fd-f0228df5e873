<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customer_pipwave_verification_token".
 *
 * @property integer $token_id
 * @property string $token
 * @property integer $customer_id
 * @property integer $result
 * @property integer $created_at
 * @property integer $updated_at
 * @property integer $expire_at
 */
class CustomerPipwaveVerificationToken extends \yii\db\ActiveRecord
{
    const SUCCESS_STATUS_LABEL = 'Review in 15 minutes';
    const FAILED_STATUS_LABEL = 'Review in 15 minutes';
    const REVIEW_STATUS_LABEL = 'Review in 15 minutes';
    const ERROR_STATUS_LABEL = 'Error';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'customer_pipwave_verification_token';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'result', 'created_at', 'updated_at', 'expire_at'], 'integer'],
            [['token'], 'string', 'max' => 100],
            [['token'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'token_id' => Yii::t('analysis_credit_card_info', 'Token ID'),
            'token' => Yii::t('analysis_credit_card_info', 'Token'),
            'customer_id' => Yii::t('analysis_credit_card_info', 'Customer ID'),
            'result' => Yii::t('analysis_credit_card_info', 'Result'),
            'created_at' => Yii::t('analysis_credit_card_info', 'Created At'),
            'updated_at' => Yii::t('analysis_credit_card_info', 'Updated At'),
            'expire_at' => Yii::t('analysis_credit_card_info', 'Expire At'),
        ];
    }

    public function updateStatus($status)
    {
        $this->result = $status;
        $this->updated_at = time();
        $this->save();
    }

    public function statusLabel()
    {
        $status_arr = array(
            10 => Yii::t('verificationSubmission', $this::SUCCESS_STATUS_LABEL),
            5 => Yii::t('verificationSubmission', $this::FAILED_STATUS_LABEL),
            8 => Yii::t('verificationSubmission', $this::REVIEW_STATUS_LABEL),
            2 => Yii::t('verificationSubmission', $this::ERROR_STATUS_LABEL),
        );

        if (array_key_exists($this->result, $status_arr)) {
            return [
                'status' => $this->result,
                'label' => $status_arr[$this->result]
            ];
        } else {
            Yii::$app->reporter->reportToAdminViaSlack("PIPWAVE SDK OffGamers Authentication Notification", "CustomerPipwaveVerificationToken status out of range: "
                . "token: " . $this->token
                . "Result: " . $this->result);
            return false;
        }
    }
}
