<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers}}".
 *
 * @property int $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_dob
 * @property int $account_activated
 * @property string $customers_email_address
 * @property int $email_verified
 * @property string $serial_number
 * @property int $customers_default_address_id
 * @property int $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string $customers_newsletter
 * @property string $customers_group_name
 * @property int $customers_group_id
 * @property string $customers_discount
 * @property int $customers_groups_id
 * @property int $customers_aft_groups_id
 * @property int $customers_status
 * @property string $customers_flag
 * @property int $customers_phone_verified
 * @property string $customers_phone_verified_by
 * @property string $customers_phone_verified_datetime
 * @property int $affiliate_ref_id
 * @property int $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property string $customers_reserve_amount
 * @property string $customers_security_start_time
 * @property int $customers_disable_withdrawal
 * @property string $customers_einvoice_type
 * @property string $customers_national_id_number
 * @property string $customers_company_name
 * @property string $customers_company_registration_number
 * @property string $customers_tax_identification_number
 * @property int $customers_einvoice_requested
 * @property Countries $countries
 * @property CustomersInfo $customers_info
 */
class Customers extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_dob', 'customers_phone_verified_datetime', 'customers_security_start_time'], 'safe'],
            [['account_activated', 'email_verified', 'customers_default_address_id', 'customers_country_dialing_code_id', 'customers_group_id', 'customers_groups_id', 'customers_status', 'customers_phone_verified', 'affiliate_ref_id', 'ref_id', 'customers_disable_withdrawal'], 'integer'],
            [['customers_discount'], 'string', 'max' => 8],
            [['customers_reserve_amount'], 'string', 'max' => 15],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname', 'customers_telephone', 'customers_mobile', 'customers_fax', 'customers_login_sites'], 'string', 'max' => 32],
            [['customers_email_address', 'customers_msn', 'customers_yahoo'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_qq', 'customers_icq'], 'string', 'max' => 20],
            [['customers_password', 'customers_pin_number'], 'string', 'max' => 40],
            [['customers_newsletter', 'customers_phone_verified_by', 'customers_merged_profile'], 'string', 'max' => 255],
            [['customers_group_name'], 'string', 'max' => 27],
            [['customers_flag'], 'string', 'max' => 10],
            [['customers_aft_groups_id'], 'string', 'max' => 11],
            [['customers_einvoice_requested'], 'integer', 'max' => 1],
            [['customers_einvoice_type', 'customers_company_registration_number', 'customers_tax_identification_number'], 'string', 'max' => 100],
            [['customers_national_id_number'], 'string', 'max' => 12],
            [['customers_company_name'], 'string', 'max' => 255],
            [['customers_tax_country_code'], 'default', 'value' => 'MY'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_gender' => 'Customers Gender',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_dob' => 'Customers Dob',
            'account_activated' => 'Account Activated',
            'customers_email_address' => 'Customers Email Address',
            'email_verified' => 'Email Verified',
            'serial_number' => 'Serial Number',
            'customers_default_address_id' => 'Customers Default Address',
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'customers_mobile' => 'Customers Mobile',
            'customers_fax' => 'Customers Fax',
            'customers_msn' => 'Customers Msn',
            'customers_qq' => 'Customers Qq',
            'customers_yahoo' => 'Customers Yahoo',
            'customers_icq' => 'Customers Icq',
            'customers_password' => 'Customers Password',
            'customers_pin_number' => 'Customers Pin Number',
            'customers_newsletter' => Yii::t('profile', 'ENTRY_NEWSLETTER'),
            'customers_group_name' => 'Customers Group Name',
            'customers_group_id' => 'Customers Group',
            'customers_discount' => 'Customers Discount',
            'customers_groups_id' => 'Customers Groups',
            'customers_aft_groups_id' => 'Customers Aft Groups',
            'customers_status' => 'Customers Status',
            'customers_flag' => 'Customers Flag',
            'customers_phone_verified' => 'Customers Phone Verified',
            'customers_phone_verified_by' => 'Customers Phone Verified By',
            'customers_phone_verified_datetime' => 'Customers Phone Verified Datetime',
            'affiliate_ref_id' => 'Affiliate Ref',
            'ref_id' => 'Ref',
            'customers_merged_profile' => 'Customers Merged Profile',
            'customers_login_sites' => 'Customers Login Sites',
            'customers_reserve_amount' => 'Customers Reserve Amount',
            'customers_security_start_time' => 'Customers Security Start Time',
            'customers_disable_withdrawal' => 'Customers Disable Withdrawal',
            'customers_einvoice_requested' => 'Customers E-Invoice Requested',
            'customers_einvoice_type' => 'Customers E-Invoice Type',
            'customers_national_id_number' => 'Customers National ID Number',
            'customers_company_name' => 'Customers Company Name',
            'customers_company_registration_number' => 'Customers Company Registration Number',
            'customers_tax_identification_number' => 'Customers Tax Identification Number',
            'customers_tax_country_code' => 'Customers Tax Country Code',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCountries() {
        return $this->hasOne(Countries::className(), ['countries_id' => 'customers_country_dialing_code_id']);
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomersInfo() {
        return $this->hasOne(CustomersInfo::className(), ['customers_info_id' => 'customers_id']);
    }
    
    public function checkDate($attribute, $params) {
        if (!isset($params['message'])) {
            $params['message'] = 'Invalid Date';
        }
        if (!$this->hasErrors($attribute)) {
            if ($this->{$attribute} != "") {
                $splitDateTime = explode(" ", $this->{$attribute});
                $splitDate = explode("-", $splitDateTime[0]);
                $dobMonth = (isset($splitDate[1]) ? (int) $splitDate[1] : 0);
                $dobYear = (isset($splitDate[0]) ? (int) $splitDate[0] : 0);
                $dobDay = (isset($splitDate[2]) ? (int) $splitDate[2] : 0);

                if ($dobDay != 0 || $dobMonth != 0 || $dobYear != 0) {
                    if (!checkdate($dobMonth, $dobDay, $dobYear))
                        $this->addError($attribute, $params['message']);
                }
            }
        }
    }

    public function getCustomerCurrentInfo($customer_id = 0) {
        $customer_id = $customer_id > 0 ? $customer_id : Yii::$app->user->id;
        
        $result = static::findOne([
            'customers_id' => $customer_id,
        ]);

        $customerInfo['customers_firstname'] = $result->customers_firstname;
        $customerInfo['customers_lastname'] = $result->customers_lastname;
        $customerInfo['customers_gender'] = $result->customers_gender;
        $customerInfo['customers_email_address'] = $result->customers_email_address;
        $customerInfo['customers_country_dialing_code_id'] = $result->customers_country_dialing_code_id;
        $customerInfo['customers_telephone'] = $result->customers_telephone;
        $customerInfo['customers_fax'] = $result->customers_fax;
        $customerInfo['customers_dob'] = $result->customers_dob;
        $customerInfo['customers_login_sites'] = $result->customers_login_sites;
        $customerInfo['customers_default_address_id'] = $result->customers_default_address_id;
        $customerInfo['customers_info_changes_made'] = isset($result->customersInfo->customers_info_changes_made) ? $result->customersInfo->customers_info_changes_made : '';

        return $customerInfo;
    }

    public function getCustomerFullPhone() {
        $c_data = static::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);
        if (isset($c_data->customers_id, $c_data->countries->countries_international_dialing_code)) {
            return $c_data->countries->countries_international_dialing_code . $c_data->customers_telephone;
        }
        return false;
    }

    public function customerPassword() {
        $c_data = static::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);
        if (isset($c_data->customers_id)) {
            return $c_data->customers_password;
        }

        return false;
    }

    public function updatePassword($cryptedPassword, $cid = '') {
        static::updateAll([
            'customers_password' => $cryptedPassword
        ], [
            'customers_id' => $cid != '' ? $cid : Yii::$app->user->id,
        ]);
    }

    public function getPhoneInfo($customer_id = 0) {
        $customer_id = $customer_id > 0 ? $customer_id : Yii::$app->user->id;
        $customerPhone = array();

        $c_data = static::findOne([
            'customers_id' => $customer_id,
        ]);
        if (isset($c_data->customers_id)) {
            $customerPhone = array(
                "customers_country_dialing_code_id" => $c_data->customers_country_dialing_code_id,
                "customers_telephone" => $c_data->customers_telephone,
                "customers_default_address_id" => $c_data->customers_default_address_id
            );
        }
        
        return $customerPhone;
    }

    public function updateCustomerPhone($countryCode, $phoneNumber) {
        static::updateAll([
            'customers_country_dialing_code_id' => $countryCode,
            'customers_telephone' => $phoneNumber,
        ], [
            'customers_id' => Yii::$app->user->id,
        ]);
    }

    public function updateCountryDialingCodeId($countryDialingCodeId, $customer_id) {
        $customer_id = $customer_id > 0 ? $customer_id : Yii::$app->user->id;
        static::updateAll([
            'customers_country_dialing_code_id' => $countryDialingCodeId,
        ], [
            'customers_id' => $customer_id,
        ]);
    }

    public function updateCustomerInfo($m_attr, $customer_id = 0) {
        $customer_id = $customer_id > 0 ? $customer_id : Yii::$app->user->id;
        static::updateAll($m_attr, [
            'customers_id' => $customer_id,
        ]);
    }

    public function getDuplicatePhoneId($countryDialCode, $phoneNo, $customerId) {
        return static::find()->select('customers_id')->where([
            'customers_country_dialing_code_id' => Countries::find()->select('countries_id')->where([
                'countries_international_dialing_code' => $countryDialCode,
            ]),
            'customers_telephone' => $phoneNo,
        ])->andWhere(['!=', 'customers_id', $customerId])->all();
    }

    public function getCustomerGroupId() {
        $c_data = static::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);
        if (isset($c_data->customers_id)) {
            return $c_data->customers_groups_id;
        }
        return false;
    }

    public function getCustomerIdByEmail($email) {
        $c_data = static::findOne([
            'customers_email_address' => $email,
        ]);
        $customer_info = array();
        if (isset($c_data->customers_id)) {
            $customer_info = array(
                "customers_id" => $c_data->customers_id,
                "customers_firstname" => $c_data->customers_firstname,
                "customers_lastname" => $c_data->customers_lastname,
                "customers_gender" => $c_data->customers_gender
            );
        }
        return $customer_info;
    }

    public static function getCustomerByField($field, $cond) {
        return static::find()->select($field)->where($cond)->asArray()->one();
    }

}
