<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_document_id_verification}}".
 *
 * @property int $id
 * @property int $customers_id
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_edit_firstname
 * @property string $customers_edit_lastname
 * @property int $name_lock
 * @property int $verify_status
 * @property boolean $waiting_for_document
 * @property boolean $isAuto
 * @property int $updated_at
 * 
 * @property Customers $customers
 */
class CustomersDocumentIdVerification extends \common\components\CoreModel{
    
    const STATUS_APPROVE = '1';
    const STATUS_REVIEW = '0';
    const STATUS_DECLINE = '2';


    public function behaviors() {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::className(),
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ]
            ],
        ];
    }
    
    public static function tableName() {
        
         return '{{%customers_document_id_verification}}';
    }
    
    public function rules() {
        
        return [
            [['customers_edit_firstname', 'customers_edit_lastname'],'required', 'message'=> 'This {attribute} cannot be empty'],
            [['customers_edit_firstname', 'customers_edit_lastname', 'customers_id', 'name_lock', 'verify_status', 'waiting_for_document'], 'required'],
            [['customers_id','name_lock','verify_status','updated_at'], 'integer'],
            [['waiting_for_document'],'boolean'],
            [['customers_firstname', 'customers_lastname', 'customers_edit_firstname', 'customers_edit_lastname'], 'string', 'max' => 32],
            [['verify_status'],'default', 'value' => '0' ]
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_id' => 'Customers',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_edit_firstname' => 'Customers Firstname',
            'customers_edit_lastname' => 'Customers Lastname',
            'name_lock' => 'Name Lock',
            'verify_status' => 'Verify Status',
            'waiting_for_document' => 'Waiting for document',
            'updated_at' => 'Updated at'
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomers() {
        return $this->hasOne(Customers::className(), ['customers_id' => 'customers_id']);
    }
    
    
}
