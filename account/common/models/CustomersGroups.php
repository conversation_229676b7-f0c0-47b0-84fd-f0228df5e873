<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_groups}}".
 *
 * @property int $customers_groups_id
 * @property string $customers_groups_name
 * @property string $customers_groups_legend_color
 * @property string $customers_groups_payment_methods
 * @property string $customers_groups_extra_sc In Percentage
 * @property int $sort_order
 */
class CustomersGroups extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_groups}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_groups_payment_methods'], 'required'],
            [['customers_groups_extra_sc'], 'string', 'max' => 8],
            [['sort_order'], 'integer'],
            [['customers_groups_name'], 'string', 'max' => 32],
            [['customers_groups_legend_color'], 'string', 'max' => 7],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_groups_id' => 'Customers Groups',
            'customers_groups_name' => 'Customers Groups Name',
            'customers_groups_legend_color' => 'Customers Groups Legend Color',
            'customers_groups_payment_methods' => 'Customers Groups Payment Methods',
            'customers_groups_extra_sc' => 'Customers Groups Extra Sc',
            'sort_order' => 'Sort Order',
        ];
    }
    
    public function getCustomersGroupsName($cgid) {
        $result = false;

        $m_data = static::findOne([
            'customers_groups_id' => $cgid,
        ]);
        if (isset($m_data->customers_groups_name)) {
            $result = $m_data->customers_groups_name;
        }

        return $result;
    }

}
