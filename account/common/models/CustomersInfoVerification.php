<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_info_verification}}".
 *
 * @property int $customers_id
 * @property string $customers_info_value
 * @property string $serial_number
 * @property int $verify_try_turns
 * @property int $info_verified
 * @property string $info_verification_type
 * @property string $customers_info_verification_mode
 * @property string $customers_info_verification_date
 * @property string $call_language
 */
class CustomersInfoVerification extends \common\components\CoreModel
{
    public static $type1 = "email";
    public static $type2 = "telephone";
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_info_verification}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'verify_try_turns', 'info_verified'], 'integer'],
            [['customers_info_verification_date'], 'safe'],
            [['customers_info_value'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_info_verification_mode'], 'string', 'max' => 1],
            [['info_verification_type'], 'string', 'max' => 32],
            [['call_language'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_info_value' => 'Customers Info Value',
            'serial_number' => 'Serial Number',
            'verify_try_turns' => 'Verify Try Turns',
            'info_verified' => 'Info Verified',
            'info_verification_type' => 'Info Verification Type',
            'customers_info_verification_mode' => 'Customers Info Verification Mode',
            'customers_info_verification_date' => 'Customers Info Verification Date',
            'call_language' => 'Call Language',
        ];
    }
    
    public function searchRecord($uid, $val, $type = "") {
        return static::find()->where([
            'customers_id' => $uid,
            'customers_info_value' => $val,
            'info_verification_type' => (empty($type) ? self::$type1 : $type),
        ])->exists();
    }

    public function saveCustomerInfoVerification($infoVerification) {
        $this->setIsNewRecord(true);
        $this->attributes = $infoVerification;
        $this->save();
    }

    public function updatePhoneVerifyInfo($customerId, $phoneNo, $serialNumber = '', $infoVerified = 0, $tryTurns = '1', $verificationType = 'telephone') {
        $verifyInfo = array(
            'customers_id' => $customerId,
            'customers_info_value' => $phoneNo,
            'serial_number' => $serialNumber,
            'verify_try_turns' => $tryTurns,
            'info_verified' => $infoVerified,
            'info_verification_type' => $verificationType,
            'customers_info_verification_date' => new \yii\db\Expression('NOW()')
        );
        
        if (CustomersInfoVerification::model()->searchRecord($customerId, $phoneNo, CustomersInfoVerification::$type2) == 0) {
            $this->saveNewRecord($verifyInfo);
        } else {
            static::updateAll($verifyInfo, [
                'customers_id' => $customerId,
                'customers_info_value' => $phoneNo,
                'info_verification_type' => $verificationType,
            ]);
        }
    }

    public function phoneIsVerify($customer, $phone) {
        return static::find()->where([
            'customers_info_value' => $phone,
            'info_verification_type' => 'telephone',
            'customers_id' => $customer,
            'info_verified' => '1',
        ])->exists();
    }
    
    public static function getCustomersInfoVerify() {
        $cache_key = self::tableName() . \Yii::$app->user->id;
        $result = Yii::$app->cache->get($cache_key);

        if (!$result) {
            $model = \common\models\Customers::findOne(['customers_id' => \Yii::$app->user->id]);
            $result = static::find()->where(['customers_id' => \Yii::$app->user->id, 'customers_info_value' => $model->customers_country_dialing_code_id . $model->customers_telephone, 'info_verified' => 1, 'info_verification_type' => static::$type2])->asArray()->one();
            Yii::$app->cache->set($cache_key, $result, 24 * 60 * 60);
        }

        return $result;
    }
    
    
}
