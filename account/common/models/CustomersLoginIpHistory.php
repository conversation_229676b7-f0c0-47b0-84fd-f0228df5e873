<?php

namespace common\models;

use common\components\CoreModel;

/**
 * This is the model class for table "{{%customers_login_ip_history}}".
 *
 * @property int $customers_id
 * @property string $customers_login_date
 * @property string $customers_login_ip
 * @property string $customers_login_ua_info
 * @property string $login_method
 */
class CustomersLoginIpHistory extends CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_login_ip_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_login_date'], 'required'],
            [['customers_id'], 'integer'],
            [['customers_login_date'], 'safe'],
            [['customers_login_ip'], 'string', 'max' => 128],
            [['login_method'], 'string', 'max' => 12],
            [['customers_login_ua_info'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_login_date' => 'Customers Login Date',
            'customers_login_ip' => 'Customers Login Ip',
            'customers_login_ua_info' => 'Customers Login Ua Info',
            'login_method' => 'Login Method',
        ];
    }
}
