<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_mobile_change_approval}}".
 *
 * @property int $customers_id
 * @property string $street_address
 * @property string $suburb
 * @property string $postcode
 * @property string $city
 * @property string $state
 * @property string $telephone
 * @property string $approved_status_updated_by
 * @property integer $approval_status_datetime
 * @property int $approval_status
 * @property int $dialing_code_id
 * @property int $country_id
 * @property int $zone_id
 * @property integer $created_datetime
 * @property integer $updated_datetime
 */
class CustomersMobileChangeApproval extends \common\components\CoreModel
{
    const APPROVAL_PENDING = '1',
        APPROVAL_APPROVED = '2',
        APPROVAL_REJECTED = '3';
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_mobile_change_approval}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'approval_status', 'dialing_code_id','country_id','zone_id'], 'integer'],
            [['suburb', 'city', 'state', 'telephone'], 'string', 'max' => 32],
            [['street_address'], 'string', 'max' => 64],
            [['postcode'], 'string', 'max' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'approval_status' => 'Approval status',
            'dialing_code_id' => 'Dialing Code ID',
            'country_id' => 'Country ID',
            'zone_id' => 'Zone ID',
            'suburb' => 'Suburb',
            'city' => 'City',
            'state' => 'State',
            'telephone' => 'Telephone',
            'street_address' => 'Street Address',
            'postcode' => 'Postcode',
            'created_datetime' => 'Created Datetime',
            'updated_datetime' => 'Updated Datetime',
            'aprroved_status_datetime' => 'Approved Datetime',
            'approved_status_updated_by' => 'Approved Status Updated By',
        ];
    }
}
