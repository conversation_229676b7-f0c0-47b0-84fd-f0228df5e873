<?php

namespace common\models;

use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "{{%customers_otp}}".
 *
 * @property int $customers_id
 * @property string $customers_otp_type
 * @property string $customers_otp_digit
 * @property string $customers_match_data
 * @property string $customers_otp_request_date
 */
class CustomersOtp extends \common\components\CoreModel
{
    public $active_token;
    
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_otp}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_otp_type'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['customers_match_data'], 'string', 'max' => 255],
            [['customers_otp_request_date'], 'safe'],
            [['customers_otp_type', 'customers_otp_digit'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_otp_type' => 'Customers Otp Type',
            'customers_otp_digit' => 'Customers Otp Digit',
            'customers_match_data' => 'Customers Match Data',
            'customers_otp_request_date' => 'Customers Otp Request Date',
        ];
    }
    
    public function customerExists($customerId, $requestType) {
        return static::find()->where([
            'customers_id' => $customerId,
            'customers_otp_type' => $requestType,
        ])->exists();
    }

    public function updateCustomerOpt($attribute, $customerId, $requestType) {
        static::updateAll($attribute, [
            'customers_id' => $customerId,
            'customers_otp_type' => $requestType,
        ]);
    }

    public function saveCustomerOpt($customerOptData) {
        $this->setIsNewRecord(true);
        $this->attributes = $customerOptData;
        $this->save();
    }

    public function getCustomerSecurityToken($customerId, $requestType, $tokenValidityPeriod, $match_data) {
        $data = static::find()->select([
            'customers_otp_digit', new Expression(
            'customers_otp_request_date > DATE_SUB(NOW(), INTERVAL ' . (int) $tokenValidityPeriod . ' MINUTE) AS active_token'),
            'customers_match_data'
        ])->where([
            'customers_id' => $customerId,
            'customers_otp_type' => $requestType,
        ])->one();
        if ($match_data !== false && !empty($data->customers_match_data) && $data->customers_match_data != $match_data) {
            //Not matching already, so we just invalidate the otp
            $data->updateCustomerOpt([
                'customers_otp_digit' => '',
            ], $customerId, $requestType);
            return null;
        }
        return $data;
    }

}
