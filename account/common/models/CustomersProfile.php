<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_profile}}".
 * @property int $customers_id
 * @property string $field_key
 * @property string $value
 * @property dateTime $updated_at
 */
class CustomersProfile extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_profile}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'field_key', 'value'], 'required'],
            [['customers_id'], 'integer'],
            [['field_key'], 'string', 'max' => 32],
            [['value'], 'string', 'max' => 255],
            [['updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'field_key' => 'Field Key',
            'value' => 'Value',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Get all profile info for a customer
     * @param int $customer_id
     * @return array
     */
    public static function getCustomerProfileInfo($customer_id)
    {
        $profiles = static::find()->select('field_key, value')->where([
            'customers_id' => $customer_id,
        ])->asArray()->all();

        $results = [];
        foreach ($profiles as $profile) {
            $results[$profile['field_key']] = $profile['value'];
        }
        
        return $results;
    }

    /**
     * Set or update a profile field value
     * @param int $customer_id
     * @param string $field_key
     * @param string $value
     * @return bool
     */
    public static function setCustomerProfileValue($customer_id, $field_key, $value)
    {
        // Find or initialize the profile
        $profile = static::findOne(['customers_id' => $customer_id, 'field_key' => $field_key]);
        if (!$profile) {
            $profile = new self();
            $profile->customers_id = $customer_id;
            $profile->field_key = $field_key;
        }

        // Set the value and updated_at
        $profile->value = $value;
        $profile->updated_at = date('Y-m-d H:i:s');

        // Save the profile
        if (!$profile->save()) {
            throw new \Exception(json_encode($profile->getErrors()));
        }

        return true;
    }

    /**
     * Delete a profile field
     * @param int $customer_id
     * @param string $field_key
     * @return int
     */
    public static function deleteCustomerProfileValue($customer_id, $field_key)
    {
        return static::deleteAll([
            'customers_id' => $customer_id,
            'field_key' => $field_key,
        ]);
    }
}
