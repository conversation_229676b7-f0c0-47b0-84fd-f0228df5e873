<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_remarks_history}}".
 *
 * @property int $customers_remarks_history_id
 * @property int $customers_id
 * @property string $date_remarks_added
 * @property string $remarks
 * @property string $remarks_added_by
 */
class CustomersRemarksHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_remarks_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id'], 'integer'],
            [['date_remarks_added', 'remarks'], 'safe'],
            [['remarks_added_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_remarks_history_id' => 'Customers Remarks History',
            'customers_id' => 'Customers',
            'date_remarks_added' => 'Date Remarks Added',
            'remarks' => 'Remarks',
            'remarks_added_by' => 'Remarks Added By',
        ];
    }
    
}
