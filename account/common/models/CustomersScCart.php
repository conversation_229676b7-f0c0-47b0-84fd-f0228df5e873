<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_sc_cart}}".
 *
 * @property int $customers_id
 * @property int $products_id
 * @property int $customers_sc_cart_quantity
 * @property string $customers_sc_cart_date_added
 * @property string $customers_sc_cart_currency
 * @property string $customers_sc_cart_last_message
 */
class CustomersScCart extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_sc_cart}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'products_id', 'customers_sc_cart_quantity'], 'integer'],
            [['customers_sc_cart_date_added'], 'safe'],
            [['customers_sc_cart_currency'], 'string', 'max' => 3],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'products_id' => 'Products',
            'customers_sc_cart_quantity' => 'Customers Sc Cart Quantity',
            'customers_sc_cart_date_added' => 'Customers Sc Cart Date Added',
            'customers_sc_cart_currency' => 'Customers Sc Currency',
            'customers_sc_cart_last_message' => 'Customers Sc Last Message',
        ];
    }
    
    public function removeCart() {
        static::deleteAll([
            'customers_id' => Yii::$app->user->id,
        ]);
    }

}
