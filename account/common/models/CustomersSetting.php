<?php

namespace common\models;

use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "{{%customers_setting}}".
 *
 * @property int $customers_id
 * @property string $customers_setting_key
 * @property string $customers_setting_value
 * @property string $created_datetime
 * @property string $updated_datetime
 */
class CustomersSetting extends \common\components\CoreModel
{
    var $request_active;
    var $same_day_request;
    var $active_request;
    var $seconds_till_resend;


    const SETUP_ACCOUT_KEY = 'setup_account';
    const FOUR_DIGIT_KEY = 'last_four_digit_attempts';
    const SESSION_ACTIVE_TIME = 'session_active_time';
    const DEVICE_PIN_FAILED_EMAIL_COUNT = 'device_pin_failed_email_count';
    const CHANGE_PHONE_COUNTRY = 'change_phone_country';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_setting}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_setting_key'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['customers_setting_value', 'created_datetime', 'updated_datetime'], 'safe'],
            [['customers_setting_key'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_setting_key' => 'Customers Setting Key',
            'customers_setting_value' => 'Customers Setting Value',
            'updated_datetime' => 'Updated Datetime',
        ];
    }

    public function getPhoneVerifyAttempt($customerId)
    {
        return static::find()->select(['customers_setting_value', new Expression('updated_datetime > DATE_SUB(curdate(), INTERVAL 1 DAY) AS request_active')])->where([
            'customers_id' => $customerId,
            'customers_setting_key' => 'phone_verification_attempt',
        ])->one();
    }

    public function updatePhoneVerifyAttempt($customer_id, $reset_flag = false)
    {
        $dataset = static::find()->where([
            'customers_id' => $customer_id,
            'customers_setting_key' => 'phone_verification_attempt',
        ])->one();

        if (isset($dataset)) {
            $dataset->updated_datetime = date('Y-m-d H:i:s');

            if ($reset_flag) {
                $dataset->customers_setting_value = 1;
            } else {
                $dataset->customers_setting_value += 1;
            }

            if ($dataset->save()) {
                $return_int = $dataset->customers_setting_value;
            }
        }
        return $return_int;
    }

    public function getTokenVerifyAttempt($customerId, $resendPeriod, $tokenType)
    {
        $result = static::find()->select(['customers_setting_value', new Expression('created_datetime > DATE_SUB(NOW(), INTERVAL 24 HOUR) AS same_day_request'), new Expression('TIMESTAMPDIFF(SECOND, DATE_SUB(NOW(), INTERVAL ' . (int)$resendPeriod . ' SECOND), updated_datetime) as seconds_till_resend')])->where([
            'customers_id' => $customerId,
            'customers_setting_key' => $tokenType,
        ])->one();
        if (isset($result->seconds_till_resend) && $result->seconds_till_resend < 0) {
            $result->seconds_till_resend = 0;
        }
        return $result;
    }

    public function updateTokenVerifyAttempt($attribute, $customerId, $requestType)
    {
        static::updateAll($attribute, [
            'customers_setting_key' => $requestType,
            'customers_id' => $customerId,
        ]);
    }

    public function saveCustomerSetting($tokenData)
    {
        $model = static::find()->where([
            'customers_id' => $tokenData['customers_id'],
            'customers_setting_key' => $tokenData['customers_setting_key'],
        ])->one();

        if ($model) {
            static::updateAll($tokenData, [
                'customers_id' => $tokenData['customers_id'],
                'customers_setting_key' => $tokenData['customers_setting_key'],
            ]);
        } else {
            $this->setIsNewRecord(true);
            $this->attributes = $tokenData;
            $this->save();
        }
    }

    public function setCustomerSetting($customer_id, $key, $value)
    {
        $active_time_setting = $this->findOne([
            "customers_id" => $customer_id,
            "customers_setting_key" => $key
        ]);
        if ($active_time_setting) {
            $c_setting_data = [
                'customers_setting_value' => $value,
                'updated_datetime' => date("Y-m-d h:i:s")
            ];
            $active_time_setting->setAttributes($c_setting_data);
            $active_time_setting->save();
        } else {
            $c_setting_data = [
                'customers_id' => $customer_id,
                'customers_setting_value' => $value,
                'updated_datetime' => date("Y-m-d h:i:s"),
                'customers_setting_key' => $key,
                'created_datetime' => date("Y-m-d h:i:s"),
            ];
            $this->setAttributes($c_setting_data);
            $this->save();
        }
    }

    public static function getCustomersSetting($key)
    {
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . \Yii::$app->user->id . '/' . $key . '/value';
        $result = Yii::$app->cache->get($cache_key);

        if ($result === false) {
            $setting = static::find()->where(['customers_id' => \Yii::$app->user->id, 'customers_setting_key' => $key])->asArray()->one();
            $result = $setting['customers_setting_value'];
            Yii::$app->cache->set($cache_key, $result, 24 * 60 * 60);
        }

        return $result;
    }

    public function removeUpdateCustSetting()
    {
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . $this->customers_id . '/' . $this->customers_setting_key . '/value';
        Yii::$app->cache->delete($cache_key);
    }

    public function saveValue($value)
    {
        $this->customers_setting_value = $value;
        $this->updated_datetime = new \yii\db\Expression('NOW()');
        $this->save();
    }
}
