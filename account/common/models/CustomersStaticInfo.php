<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_static_info}}".
 *
 * @property int $customers_id
 * @property string $info_key
 * @property string $value
 * @property string $created_date
 * @property string $updated_date
 */
class CustomersStaticInfo extends \common\components\CoreModel
{    
    const KEY_EMAIL_CHANGES_COUNTER = 'email_changes_counter', //MONTHLY
            KEY_EMAIL_CHANGES_COUNTER_DAILY = 'email_changes_counter_daily',
            KEY_TWO_FACTOR_SECRET = 'totp_secret';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_static_info}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'info_key'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['info_key'], 'string', 'max' => 64],
            [['value'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'info_key' => 'Info key',
            'value' => 'Info value',
            'created_date' => 'Created date',
            'updated_date' => 'Updated date',
        ];
    }
    
    public static function saveInfo($key, $value, $customers_id = null) {
        $customers_id = $customers_id ?: Yii::$app->user->id;
        $model = static::find()->where([
            'customers_id' => $customers_id,
            'info_key' => $key,
        ])->one();
        if (!$model) {
            $model = new static();
            $model->customers_id = $customers_id;
            $model->info_key = $key;
            $model->created_date = new \yii\db\Expression('NOW()');
        }
        $model->saveValue($value);
    }
    
    public function saveValue($value) {
        $this->value = $value;
        $this->updated_date = new \yii\db\Expression('NOW()');
        $this->save();
    }
    
    public static function getInfo($key, $customers_id = null) {
        $customers_id = $customers_id ?: Yii::$app->user->id;
        return static::find()->where([
            'customers_id' => $customers_id,
            'info_key' => $key,
        ])->one();
    }
}
