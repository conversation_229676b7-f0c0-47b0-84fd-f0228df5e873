<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_verification_document}}".
 *
 * @property integer $customers_id
 * @property string $files_001
 * @property integer $files_001_locked
 * @property string $files_002
 * @property integer $files_002_locked
 * @property string $files_003
 * @property integer $files_003_locked
 * @property string $files_004
 * @property integer $files_004_locked
 * @property string $files_005
 * @property integer $files_005_locked
 */
class CustomersVerificationDocument extends \common\components\CoreModel
{

//    const STATUS_APPROVED = "Approved";
//    const STATUS_PENDING = "Review";
//    const STATUS_DENIED = "Denied";
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_verification_document}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'files_001_locked', 'files_002_locked', 'files_003_locked', 'files_004_locked', 'files_005_locked'], 'integer'],
            [['files_001', 'files_002', 'files_003', 'files_004', 'files_005'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'files_001' => 'Files 001',
            'files_001_locked' => 'Files 001 Locked',
            'files_001_status' => 'Files 001 Status',
            'files_002' => 'Files 002',
            'files_002_locked' => 'Files 002 Locked',
            'files_002_status' => 'Files 002 Status',
            'files_003' => 'Files 003',
            'files_003_locked' => 'Files 003 Locked',
            'files_003_status' => 'Files 003 Status',
            'files_004' => 'Files 004',
            'files_004_locked' => 'Files 004 Locked',
            'files_004_status' => 'Files 004 Status',
            'files_005' => 'Files 005',
            'files_005_locked' => 'Files 005 Locked',
            'files_005_status' => 'Files 005 Status',
        ];
    }

    public function requireVerification() {
        $auto_check_file_id = $auto_check_file_lock = $auto_check_file_status = null;
        //G2G SLS 20220325 discussion with AFT - temporarily disable this flow
        // $auto_check = \Yii::$app->request->get('type');
        if (!empty($auto_check)) {
            $auto_check_types = explode(',', $auto_check);
            foreach ($auto_check_types as $type) {
                $type = trim($type);
                switch ($type) {
                    case 'ekyc':
                        $auto_check_file_id['003'] = '003';
                        break;
                    case 'selfie':
                        $auto_check_file_id['001'] = '001';
                        break;
                    case 'cc':
                        $auto_check_file_id['004'] = '004';
                        break;
                    case 'virtual':
                        $auto_check_file_id['005'] = '005';
                        break;
                    default:
                }
            }
        }
        if ($auto_check_file_id) {
            foreach ($auto_check_file_id as $doc_id) {
                $auto_check_file_status[$doc_id] = "files_${doc_id}_status";
                $auto_check_file_lock[$doc_id] = "files_${doc_id}_locked";
            }
        }
        $m_data = static::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);
        if (isset($m_data->customers_id)) {
            if (!empty($auto_check_file_id)) {
                //G2G SLS special flow - unlock if ekyc query string is present if file is pending and locked
                $to_update = false;
                foreach ($auto_check_file_id as $doc_id) {
                    $file_status = $auto_check_file_status[$doc_id];
                    $file_lock = $auto_check_file_lock[$doc_id];
                    if ($m_data->$file_status == '0' && $m_data->$file_lock == '1') {
                        $m_data->$file_lock = 0;
                        $to_update = true;
                    }
                }
                if ($to_update && !$m_data->save()) {
                    \Yii::$app->reporter->reportToAdminViaSlack('CustomersVerificationDocument auto-unlock save failed', $m_data);
                }
            }
            $result = $m_data->files_001_locked + $m_data->files_002_locked + $m_data->files_003_locked + $m_data->files_004_locked + $m_data->files_005_locked;
            if (!empty(Yii::$app->params['tmp.ekyc.allow_upload']) && in_array($m_data->customers_id, Yii::$app->params['tmp.ekyc.allow_upload'])) {
                $m_cvd_log_latest = CustomersVerificationDocumentLog::find()->select(['UNIX_TIMESTAMP(log_datetime) AS log_datetime'])->where([
                    'log_customers_id' => $m_data->customers_id,
                    'log_docs_id' => '003',
                    'log_action' => 'UPLOAD',
                ])->orderBy('log_id DESC')->asArray()->one();
                if (!empty(Yii::$app->params['tmp.ekyc.allow_upload_before']) && (empty($m_cvd_log_latest['log_datetime']) || $m_cvd_log_latest['log_datetime'] < Yii::$app->params['tmp.ekyc.allow_upload_before'])) {
                    $result -= 1;
                }
            }
            if ($result == 5) {
                return false;
            }
        } else if (!empty($auto_check_file_id)) {
            //G2G SLS special flow - unlock if ekyc query string is present if document row is not present
            $m_data = new static([
                'customers_id' => Yii::$app->user->id,
            ]);
            foreach ($auto_check_file_id as $doc_id) {
                $file_lock = $auto_check_file_lock[$doc_id];
                $m_data->$file_lock = 0;
            }
            if (!$m_data->save()) {
                \Yii::$app->reporter->reportToAdminViaSlack('CustomersVerificationDocument auto-unlock save failed', $m_data);
            }
            return true;
        } else {
            return false;
        }

        return true;
    }

    public function getLockInfo() {
        return static::find()->select('files_001_locked, files_002_locked, files_003_locked, files_004_locked, files_005_locked, files_003')->where([
            'customers_id' => Yii::$app->user->id,
        ])->one();
    }

    public function getStatusInfo() {
        return static::find()->select('files_001_status, files_002_status, files_003_status, files_004_status, files_005_locked, files_005_status')->where([
            'customers_id' => Yii::$app->user->id,
        ])->one();
    }

    public function showStatusLabel($status){
        $status_array = [
                0=>"Pending",
                1=> "Review in 48 hours",
                2=>"Denied",
                3=>"Review in 48 hours",
                4 => "Failed"
            ];
        return $status_array[$status];

    }

}
