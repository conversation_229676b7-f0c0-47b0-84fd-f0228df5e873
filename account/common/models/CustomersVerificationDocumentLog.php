<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%customers_verification_document_log}}".
 *
 * @property integer $log_id
 * @property string $log_users_id
 * @property string $log_users_type
 * @property integer $log_customers_id
 * @property string $log_docs_id
 * @property string $log_IP
 * @property string $log_datetime
 * @property string $log_filename
 * @property string $log_action
 */
class CustomersVerificationDocumentLog extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%customers_verification_document_log}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['log_customers_id'], 'integer'],
            [['log_datetime'], 'safe'],
            [['log_users_id'], 'string', 'max' => 255],
            [['log_users_type'], 'string', 'max' => 16],
            [['log_docs_id'], 'string', 'max' => 3],
            [['log_IP'], 'string', 'max' => 128],
            [['log_filename'], 'string', 'max' => 32],
            [['log_action'], 'string', 'max' => 20],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'log_id' => 'Log',
            'log_users_id' => 'Log Users',
            'log_users_type' => 'Log Users Type',
            'log_customers_id' => 'Log Customers',
            'log_docs_id' => 'Log Docs',
            'log_IP' => 'Log Ip',
            'log_datetime' => 'Log Datetime',
            'log_filename' => 'Log Filename',
            'log_action' => 'Log Action',
        ];
    }
}
