<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\helpers\BaseJson;
use common\models\Zones;


/**
 * This is the model class for table "einvoice_profile_update_queue".
 *
 * @property int $id
 * @property int $customers_id
 * @property string|null $user_info
 * @property bool $is_sqs_sent
 * @property string $created_at
 * @property string $updated_at
 */
class EinvoiceProfileUpdateQueue extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'einvoice_profile_update_queue';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors() {
        return [
            [
                'class' => TimestampBehavior::className(),
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => new Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id'], 'required'],
            [['customers_id'], 'integer'],
            [['user_info'], 'string'],
            [['is_sqs_sent'], 'boolean'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'user_info' => 'User Info',
            'is_sqs_sent' => 'Is Sqs Sent',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Create or update einvoice profile queue record
     * 
     * @param int $customers_id
     * @param array $customerData Customer model data
     * @param array $addressData Address model data
     * @param array $customerProfile Customer profile attributes
     * @return bool
     */
    public static function upsertEinvoiceProfile($customers_id, $customerData, $addressData, $countryData)
    {
        $payload = self::buildPayload($customers_id, $customerData, $addressData, $countryData);

        $queue = self::findOne(['customers_id' => $customers_id]);
        if(!$queue){
            $queue = new self();
            $queue->customers_id = $customers_id;
        }

        $queue->user_info = BaseJson::encode($payload);
        $queue->is_sqs_sent = false;
        return $queue->save();
    }

    /**
     * Build payload for einvoice profile update
     * 
     * @param int $customers_id
     * @param array $customerData Customer model data
     * @param array $addressData Address model data
     * @param array $customerProfile Customer profile attributes
     * @return array
     */
    private static function buildPayload($customers_id, $customer, $address, $country)
    {
        $customers_id = (string)$customers_id;
        $firstname = isset($customer->customers_firstname) ? $customer->customers_firstname : "";
        $lastname = isset($customer->customers_lastname) ? $customer->customers_lastname : "";
        $email = isset($customer->customers_email_address) ? $customer->customers_email_address : "";
        $username = $firstname . " " . $lastname;
        $state = isset($address->entry_state) ? $address->entry_state : "";
        if(empty($state)){
            $m_zone = Zones::findOne(['zone_id' => $address->entry_zone_id]);
            if (isset($m_zone->zone_name)) {
                $state = $m_zone->zone_name;
            }
        }

        return [
            "customers_id" => $customers_id,
            "role" => "Buyer",
            "user_info" => [
                "buyer_id" => $customers_id,
                "email" => $email,
                "first_name" => $firstname,
                "last_name" => $lastname,
                "username" => $username,
                "contact_no" => isset($customer->customers_telephone) ? $customer->customers_telephone : "",
                "contact_no_country_iso2" => isset($country->countries_international_dialing_code) ? $country->countries_international_dialing_code : "60",
                "contact_no_country_code" => isset($country->countries_iso_code_2) ? $country->countries_iso_code_2 : "MY",
                "national_id_number" => isset($customer->identity_number) ? $customer->identity_number : "",
                "company_name" => isset($customer->company_name) ? $customer->company_name : "",
                "tax_number" => isset($customer->tax_reg_number) ? $customer->tax_reg_number : "",
                "tax_country" => isset($customer->tax_country_code) ? $customer->tax_country_code : "",
                "registration_number" => isset($customer->company_reg_number) ? $customer->company_reg_number : "",
                "sst_number" => "",
                "msic_code" => "",
                "billing_info" => [
                    'zip' => isset($address->entry_postcode) ? $address->entry_postcode : "",
                    'country' => isset($country->countries_name) ? $country->countries_name : "Malaysia",
                    'address_2' => isset($address->entry_suburb) ? $address->entry_suburb : "",
                    'city' => isset($address->entry_city) ? $address->entry_city : "",
                    'address_1' => isset($address->entry_street_address) ? $address->entry_street_address : "",
                    'name' => $firstname . ' ' . $lastname,
                    'country_name' => isset($country->countries_name) ? $country->countries_name : "Malaysia",
                    'state' => $state,
                    'country_iso2' => isset($country->countries_iso_code_2) ? $country->countries_iso_code_2 : "MY",
                ],
            ],
        ];
    }
}