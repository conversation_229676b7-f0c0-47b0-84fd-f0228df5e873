<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "email_validation".
 *
 * @property int $id
 * @property string $email
 * @property string $profile
 * @property int $mx_found
 * @property int $smtp_check
 * @property int $score
 * @property string|null $raw
 * @property int|null $created_at
 * @property int|null $updated_at
 */
class EmailValidation extends \common\components\CoreModel
{

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%email_validation}}';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db.og');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['email', 'profile', 'mx_found', 'smtp_check', 'score'], 'required'],
            [['mx_found', 'smtp_check', 'score', 'created_at', 'updated_at'], 'integer'],
            [['raw'], 'string'],
            [['email'], 'string', 'max' => 768],
            [['profile'], 'string', 'max' => 255],
            [['email'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'email' => 'Email',
            'profile' => 'Profile',
            'mx_found' => 'Mx Found',
            'smtp_check' => 'Smtp Check',
            'score' => 'Score',
            'raw' => 'Raw',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

}
