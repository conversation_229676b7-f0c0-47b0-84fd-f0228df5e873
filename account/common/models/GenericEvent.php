<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%generic_event}}".
 *
 * @property string $id
 * @property integer $type
 * @property integer $visible_to
 * @property integer $updated_at
 * @property integer $created_at
 * @property string $app
 * @property string $script
 * @property integer $line
 * @property string $title
 * @property string $message
 * @property string $env_dump
 * @property string $tags
 */
class GenericEvent extends \yii\db\ActiveRecord {
    
    //bit-space for visible-to
    const VISIBLE_TO_ADMIN = 1;
    //const VISIBLE_TO_PERSON_2 = 2;
    //const VISIBLE_TO_PERSON_3 = 4;
    //const VISIBLE_TO_PERSON_4 = 8;
    
    const TYPE_EXCEPTION = 1,
            TYPE_NOTIFICATION = 2,
            TYPE_DEBUG = 3;
    
    public function behaviors() {
        return [
            [
                'class' => TimestampBehavior::className(),
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ]
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    public static function tableName() {
        return '{{%generic_event}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules() {
        return [
            [['type', 'visible_to'], 'required'],
            [['type', 'visible_to', 'updated_at', 'created_at', 'line'], 'integer'],
            [['message', 'env_dump'], 'string'],
            [['app'], 'string', 'max' => 16],
            [['script', 'title'], 'string', 'max' => 255],
            [['type'], 'in', 'range' => array_keys(static::getTypes())],
            [['tags'], 'each', 'rule' => ['string', 'max' => 50]],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels() {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'visible_to' => 'Visible To',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
            'app' => 'App',
            'script' => 'Script',
            'line' => 'Line',
            'title' => 'Title',
            'message' => 'Message',
            'env_dump' => 'Env Dump',
            'tags' => 'Tags',
        ];
    }

    public static function getTypes() {
        return [
            static::TYPE_EXCEPTION => 'Exception',
            static::TYPE_NOTIFICATION => 'Notification',
            static::TYPE_DEBUG => 'Debug',
        ];
    }
    
    public function getVisibleTos() {
        return [
            static::VISIBLE_TO_ADMIN,
            static::VISIBLE_TO_MERCHANT,
            static::VISIBLE_TO_BUYER,
            static::VISIBLE_TO_CS
        ];
    }
    
    public function addTag($tag) {
        if (!is_array($this->tags)) {
            $this->tags = explode(',', $this->tags);
        }
        $this->tags[] = $tag;
        return $this;
    }
    
    public function visibleToNone() {
        $this->visible_to = 0;
        return $this;
    }
    
    public function visibleToAll() {
        $this->visible_to = static::VISIBLE_TO_ADMIN;
        return $this;
    }
    
    public function addVisibleTo($who) {
        if (!isset($this->visible_to)) {
            $this->visible_to = 0;
        }
        $this->visible_to |= $who;
        return $this;
    }
    
    public function isVisibleTo($who) {
        return ($this->visible_to & $who) > 0;
    }

    /**
     * 
     * @param string $title
     * @param string $message
     * @param integer $type
     * @param string $tags
     * @return \common\models\GenericEvent
     */
    public static function createEvent($title, $message, $type, $tags = []) {
        $model = new static([
            'title' => substr($title, 0, 255),
            'type' => $type,
            'app' => \Yii::$app->id,
        ]);
        
        $call_trace = [];
        if (is_string($message)){
            //Do nothing, just perfect
        } else if ($message instanceof \Exception) {
            $model->script = $message->getFile();
            $model->line = $message->getLine();
            $call_trace = $message->getTrace();
            $message = $message->getCode() . ' - ' . $message->getMessage();
        } else if ($message instanceof ActiveRecord) {
            $message = "Attributes: " . json_encode($message->attributes) . "\n\nErrors: " . json_encode($message->getErrors());
        } else {
            $message = json_encode($message);
        }
        if (!is_array($tags)) {
            $tags = explode(',', $tags);
        }
        $model->tags = $tags;
        $model->message = $title . ' : ' .$message;
        $model->setScriptAndLine($call_trace);
        return $model->addVisibleTo(static::VISIBLE_TO_ADMIN);
    }
    
    public function setScriptAndLine($bt = []) {
        if (empty($bt)) {
            $bt = debug_backtrace();
        }
        $index = 0;
        $len = count($bt);
        $call_stack = [];
        while($index < $len) {
            //Trace backtrace in order to find the soonest custom code execution
            $btr = $bt[$index];
            $index ++;
            if (!isset($btr['file'])
                    //Ignore GenericEvent scripts
                    || strpos($btr['file'], 'models/GenericEvent') !== false
                    //Ignore Reporter scripts
                    || strpos($btr['file'], 'components/Reporter') !== false
                    //Ignore vendor files except dpodium scripts
                    || preg_match('/vendor\/(?!dpodium)/', $btr['file'])) {
                continue;
            }
            if (empty($this->script)) {
                $this->script = $btr['file'];
                $this->line = !isset($btr['line']) ? 0 : $btr['line'];
            }
            unset($btr['args']);
            unset($btr['object']);
            $call_stack[] = $btr;
        }
        $env_dump = $_SERVER;
        $env_dump['__call_stack'] = $call_stack;
        $this->env_dump = json_encode($env_dump);
    }
    
    public function afterFind() {
        $this->tags = explode(',', $this->tags);
    }
    
    public function beforeSave($insert) {
        $return = parent::beforeSave($insert);
        if ($return) {
            if (is_array($this->tags)) {
                $this->tags = implode(',', $this->tags);
            }
        }
        return $return;
    }
    
    public function afterSave($insert, $changedAttributes) {
        $return = parent::afterSave($insert, $changedAttributes);
        if (!is_array($this->tags)) {
            $this->tags = explode(',', $this->tags);
        }
        return $return;
    }
}
