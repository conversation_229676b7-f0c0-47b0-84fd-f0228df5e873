<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%gift_card_redemption}}".
 *
 * @property int $gift_card_redemption_id
 * @property int $customers_id
 * @property int $transaction_id
 * @property string $serial_number
 * @property string $pin_number
 * @property string $gift_card_deno
 * @property string $gift_card_redeem_amount
 * @property int $gift_card_currency_id
 * @property string $gift_card_currency_code
 * @property string $transaction_type
 * @property string $redeem_date
 * @property string $redeem_ip
 * @property string $issued_amount
 * @property int $issued_currency_id
 * @property string $issued_currency_code
 */
class GiftCardRedemption extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%gift_card_redemption}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'transaction_id', 'serial_number', 'pin_number', 'gift_card_redeem_amount', 'gift_card_currency_code', 'redeem_date', 'issued_amount', 'issued_currency_code'], 'required'],
            [['customers_id', 'transaction_id', 'gift_card_currency_id', 'issued_currency_id'], 'string', 'max' => 11],
            [['gift_card_deno', 'gift_card_redeem_amount', 'issued_amount'], 'string', 'max' => 15],
            [['serial_number'], 'string', 'max' => 18],
            [['pin_number'], 'string', 'max' => 16],
            [['gift_card_currency_code', 'issued_currency_code'], 'string', 'max' => 3],
            [['transaction_type'], 'string', 'max' => 10],
            [['redeem_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'gift_card_redemption_id' => 'Gift Card Redemption',
            'customers_id' => 'Customers',
            'transaction_id' => 'Transaction',
            'serial_number' => 'Serial Number',
            'pin_number' => 'Pin Number',
            'gift_card_deno' => 'Gift Card Deno',
            'gift_card_redeem_amount' => 'Gift Card Redeem Amount',
            'gift_card_currency_id' => 'Gift Card Currency',
            'gift_card_currency_code' => 'Gift Card Currency Code',
            'transaction_type' => 'Transaction Type',
            'redeem_date' => 'Redeem Date',
            'redeem_ip' => 'Redeem Ip',
            'issued_amount' => 'Issued Amount',
            'issued_currency_id' => 'Issued Currency',
            'issued_currency_code' => 'Issued Currency Code',
        ];
    }
}
