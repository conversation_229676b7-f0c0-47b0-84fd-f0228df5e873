<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%instant_message_accounts}}".
 *
 * @property int $instant_message_accounts_id
 * @property int $customer_id
 * @property int $instant_message_type_id
 * @property string $instant_message_userid
 * @property string $instant_message_remarks
 */
class InstantMessageAccounts extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%instant_message_accounts}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'instant_message_type_id'], 'integer'],
            [['instant_message_userid', 'instant_message_remarks'], 'string', 'max' => 96],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'instant_message_accounts_id' => 'Instant Message Accounts',
            'customer_id' => 'Customer',
            'instant_message_type_id' => 'Instant Message Type',
            'instant_message_userid' => 'Instant Message Userid',
            'instant_message_remarks' => 'Instant Message Remarks',
        ];
    }
    
    public function insert_instant_message($im_tid, $im_val) {
        $m_attr = array(
            'customer_id' => Yii::$app->user->id,
            'instant_message_type_id' => $im_tid,
            'instant_message_userid' => $im_val
        );
        static::model()->saveNewRecord($m_attr);
    }

}
