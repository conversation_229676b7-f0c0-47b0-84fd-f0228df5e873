<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%instant_message_type}}".
 *
 * @property int $instant_message_type_id
 * @property string $instant_message_type_name
 * @property string $instant_message_type_description
 * @property int $instant_message_type_order
 */
class InstantMessageType extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%instant_message_type}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['instant_message_type_order'], 'integer'],
            [['instant_message_type_name'], 'string', 'max' => 96],
            [['instant_message_type_description'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'instant_message_type_id' => 'Instant Message Type',
            'instant_message_type_name' => 'Instant Message Type Name',
            'instant_message_type_description' => 'Instant Message Type Description',
            'instant_message_type_order' => 'Instant Message Type Order',
        ];
    }
    
    public function getValidIM() {
        return static::find()->where('instant_message_type_id IN (' . Yii::$app->params['C2C_DISPLAY_IM'] . ')')
                ->orderBy('instant_message_type_order, instant_message_type_name')->all();
    }

}
