<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%languages}}".
 *
 * @property int $languages_id
 * @property string $name
 * @property string $code
 * @property string $image
 * @property string $directory
 * @property string $maxmind_phone_language
 * @property int $sort_order
 */
class Languages extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%languages}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sort_order'], 'integer'],
            [['name', 'image'], 'string', 'max' => 64],
            [['code'], 'string', 'max' => 5],
            [['directory'], 'string', 'max' => 32],
            [['maxmind_phone_language'], 'string', 'max' => 16],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'languages_id' => 'Languages',
            'name' => 'Name',
            'code' => 'Code',
            'image' => 'Image',
            'directory' => 'Directory',
            'maxmind_phone_language' => 'Maxmind Phone Language',
            'sort_order' => 'Sort Order',
        ];
    }
}
