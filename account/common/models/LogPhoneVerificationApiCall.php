<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%log_phone_verification_api_call}}".
 *
 * @property int $id
 * @property string $provider
 * @property string $request_type
 * @property int $customers_id
 * @property string $action
 * @property string $token
 * @property string $extra_data
 * @property string $created_date
 */
class LogPhoneVerificationApiCall extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%log_phone_verification_api_call}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['provider', 'request_type', 'customers_id', 'action', 'token', 'created_date'], 'required'],
            [['customers_id'], 'integer'],
            [['extra_data'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Id',
            'provider' => 'Provider',
            'request_type' => 'Request Type',
            'customers_id' => 'Customers Id',
            'action' => 'Action',
            'token' => 'Token',
            'extra_data' => 'Extra Data',
            'created_date' => 'Created Date',
        ];
    }
}
