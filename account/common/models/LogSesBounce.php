<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%log_ses_bounce}}".
 *
 * @property string $email
 * @property string $error_string
 * @property string $created_datetime
 */
class LogSesBounce extends \common\components\CoreModel
{

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%log_ses_bounce}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['email', 'created_datetime'], 'required'],
            [['error_string'], 'string'],
            [['email'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'email' => 'Email',
            'error_string' => 'Error String',
            'created_datetime' => 'Created Datetime',
        ];
    }

    public function emailExists($email)
    {
        $str = explode(" ", $email);
        $email = trim(array_pop($str), "<>");

        return static::find()->where([
                    'email' => $email,
                ])->exists();
    }

    public function saveSesBounce($sesBounce)
    {
        $str = explode(" ", $sesBounce['email']);
        $sesBounce['email'] = trim(array_pop($str), "<>");

        if (!$this->emailExists($sesBounce['email'])) {
            $this->setIsNewRecord(true);
            unset($this->email);
            $this->attributes = $sesBounce;
            $this->save();
        }
    }

    public static function emailPurify($email)
    {
        $str = explode(" ", $email);
        return trim(array_pop($str), "<> ");
    }

}
