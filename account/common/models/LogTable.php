<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%log_table}}".
 *
 * @property int $log_id
 * @property string $log_admin_id
 * @property string $log_ip
 * @property string $log_time
 * @property string $log_products_id
 * @property string $log_system_messages
 * @property string $log_user_messages
 * @property string $log_field_name
 * @property string $log_from_value
 * @property string $log_to_value
 */
class LogTable extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%log_table}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['log_time'], 'safe'],
            [['log_system_messages', 'log_user_messages'], 'required'],
            [['log_admin_id', 'log_products_id', 'log_field_name', 'log_from_value', 'log_to_value'], 'string', 'max' => 255],
            [['log_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'log_id' => 'Log',
            'log_admin_id' => 'Log Admin',
            'log_ip' => 'Log Ip',
            'log_time' => 'Log Time',
            'log_products_id' => 'Log Products',
            'log_system_messages' => 'Log System Messages',
            'log_user_messages' => 'Log User Messages',
            'log_field_name' => 'Log Field Name',
            'log_from_value' => 'Log From Value',
            'log_to_value' => 'Log To Value',
        ];
    }
    
    public function saveLogInfo($logInfo) {
        $this->log_admin_id = $logInfo['log_admin_id'];
        $this->log_ip = $logInfo['log_ip'];
        $this->log_time = $logInfo['log_time'];
        $this->log_products_id = $logInfo['log_products_id'];
        $this->log_system_messages = $logInfo['log_system_messages'];
        $this->log_user_messages = $logInfo['log_user_messages'];
        $this->log_field_name = $logInfo['log_field_name'];
        $this->log_from_value = $logInfo['log_from_value'];
        $this->log_to_value = $logInfo['log_to_value'];
        $this->save();
    }

}
