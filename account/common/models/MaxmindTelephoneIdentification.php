<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%maxmind_telephone_identification}}".
 *
 * @property int $customers_id
 * @property int $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property int $customers_telephone_type
 * @property string $city
 * @property string $state
 * @property string $postcode
 * @property string $countries_name
 * @property string $latitude
 * @property string $longitude
 * @property string $error
 * @property string $provider
 * @property string $maxmind_telephone_identification_id
 * @property string $requested_date
 */
class MaxmindTelephoneIdentification extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%maxmind_telephone_identification}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_country_international_dialing_code', 'customers_telephone_type'], 'integer'],
            [['requested_date'], 'safe'],
            [['customers_telephone', 'city', 'state'], 'string', 'max' => 32],
            [['postcode', 'latitude', 'longitude'], 'string', 'max' => 10],
            [['countries_name', 'error', 'maxmind_telephone_identification_id'], 'string', 'max' => 64],
            [['provider'], 'string', 'max' => 8],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'customers_country_international_dialing_code' => 'Customers Country International Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'customers_telephone_type' => 'Customers Telephone Type',
            'city' => 'City',
            'state' => 'State',
            'postcode' => 'Postcode',
            'countries_name' => 'Countries Name',
            'latitude' => 'Latitude',
            'longitude' => 'Longitude',
            'error' => 'Error',
            'provider' => 'Provider',
            'maxmind_telephone_identification_id' => 'Maxmind Telephone Identification',
            'requested_date' => 'Requested Date',
        ];
    }
    
    public function getPhoneProviderInfo($customerId, $countryCode, $phoneNo) {
        return static::find()->select('customers_telephone_type, provider')->where([
            'customers_country_international_dialing_code' => $countryCode,
            'customers_telephone' => $phoneNo,
            'customers_id' => $customerId,
        ])->one();
    }

    public function customerPhoneExists($customerId, $dialingCode, $phoneNo) {
        return static::find()->where([
            'customers_id' => $customerId,
            'customers_country_international_dialing_code' => $dialingCode,
            'customers_telephone' => $phoneNo,
        ])->exists();
    }

    public function saveMaxmindTelephoneIdentification($phoneInfo) {
        $this->setIsNewRecord(true);
        $this->attributes = $phoneInfo;
        $this->save();
    }

    public function updateMaxmindTelephoneIdentification($attribute, $customerId, $dialingCode, $phoneNo) {
        static::updateAll($attribute, [
            'customers_id' => $customerId,
            'customers_country_international_dialing_code' => $dialingCode,
            'customers_telephone' => $phoneNo,
        ]);
    }

}
