<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_custom_products}}".
 *
 * @property int $orders_custom_products_id
 * @property int $products_id
 * @property int $orders_products_id
 * @property string $orders_custom_products_key
 * @property string $orders_custom_products_value
 * @property int $orders_custom_products_number
 */
class OrdersCustomProducts extends \common\components\CoreModel
{
    var $task_info, $bracket_info;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_custom_products}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['products_id', 'orders_products_id', 'orders_custom_products_number'], 'integer'],
            [['orders_custom_products_value'], 'required'],
            [['orders_custom_products_key'], 'string', 'max' => 100],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_custom_products_id' => 'Orders Custom Products',
            'products_id' => 'Products',
            'orders_products_id' => 'Orders Products',
            'orders_custom_products_key' => 'Orders Custom Products Key',
            'orders_custom_products_value' => 'Orders Custom Products Value',
            'orders_custom_products_number' => 'Orders Custom Products Number',
        ];
    }
    
    public function getCdkeyInfo($productId, $ordersProductId) {
        return static::find()->select('orders_custom_products_value')->where([
            'products_id' => $productId,
            'orders_products_id' => $ordersProductId,
            'orders_custom_products_key' => 'cd_key_id',
        ])->all();
    }
}
