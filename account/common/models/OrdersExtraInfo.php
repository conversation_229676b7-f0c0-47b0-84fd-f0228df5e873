<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_extra_info}}".
 *
 * @property int $orders_id
 * @property string $orders_extra_info_key
 * @property string $orders_extra_info_value
 */
class OrdersExtraInfo extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_extra_info}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id', 'orders_extra_info_key', 'orders_extra_info_value'], 'required'],
            [['orders_id'], 'string', 'max' => 11],
            [['orders_extra_info_key', 'orders_extra_info_value'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders',
            'orders_extra_info_key' => 'Orders Extra Info Key',
            'orders_extra_info_value' => 'Orders Extra Info Value',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getO() {
        return $this->hasOne(Orders::className(), ['orders_id' => 'orders_id'])->alias('o');
    }
    
    public function getPaymentImage($oderId, $imageId) {
        static::find()->alias('m')->joinWith(['o'], false)->where([
            'm.orders_extra_info_key' => $imageId,
            'm.orders_id' => $oderId,
            'o.customers_id' => Yii::$app->user->id,
        ])->one();
    }

}
