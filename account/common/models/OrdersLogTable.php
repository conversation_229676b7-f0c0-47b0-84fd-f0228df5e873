<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_log_table}}".
 *
 * @property integer $orders_log_id
 * @property string $orders_log_admin_id
 * @property string $orders_log_ip
 * @property string $orders_log_time
 * @property integer $orders_log_orders_id
 * @property string $orders_log_system_messages
 * @property string $orders_log_filename
 */
class OrdersLogTable extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_log_table}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_log_time'], 'safe'],
            [['orders_log_orders_id'], 'integer'],
            [['orders_log_system_messages'], 'required'],
            [['orders_log_admin_id', 'orders_log_filename'], 'string', 'max' => 255],
            [['orders_log_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_log_id' => 'Orders Log',
            'orders_log_admin_id' => 'Orders Log Admin',
            'orders_log_ip' => 'Orders Log Ip',
            'orders_log_time' => 'Orders Log Time',
            'orders_log_orders_id' => 'Orders Log Orders',
            'orders_log_system_messages' => 'Orders Log System Messages',
            'orders_log_filename' => 'Orders Log Filename',
        ];
    }
    
    public function insertLogTable($dataArr) {
        $this->orders_log_admin_id = $dataArr['orders_log_admin_id'];
        $this->orders_log_ip = $dataArr['orders_log_ip'];
        $this->orders_log_time = $dataArr['orders_log_time'];
        $this->orders_log_orders_id = $dataArr['orders_log_orders_id'];
        $this->orders_log_system_messages = $dataArr['orders_log_system_messages'];
        $this->orders_log_filename = $dataArr['orders_log_filename'];
        $this->save();
    }

}
