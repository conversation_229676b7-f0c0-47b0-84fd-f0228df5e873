<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_products}}".
 *
 * @property int $orders_products_id
 * @property int $orders_id
 * @property int $products_id
 * @property string $products_model
 * @property string $products_name
 * @property string $orders_products_store_price
 * @property string $products_price
 * @property string $final_price
 * @property int $op_rebate
 * @property int $op_rebate_delivered
 * @property string $products_tax
 * @property int $products_quantity
 * @property string $products_delivered_quantity
 * @property string $products_good_delivered_quantity
 * @property string $products_good_delivered_price
 * @property string $products_canceled_quantity
 * @property string $products_canceled_price
 * @property string $products_reversed_quantity
 * @property string $products_reversed_price
 * @property int $products_bundle_id
 * @property int $parent_orders_products_id
 * @property int $products_pre_order
 * @property int $custom_products_type_id
 * @property int $orders_products_is_compensate
 * @property int $orders_products_purchase_eta
 * @property int $products_categories_id
 */
class OrdersProducts extends \common\components\CoreModel
{
    var $purchase_eta;
    var $quantity_undeliver;
    var $order_unit_price;
    var $amount_delivered;
    var $amount_refunded;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_products}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id', 'products_id', 'op_rebate', 'op_rebate_delivered', 'products_quantity', 'products_bundle_id', 'parent_orders_products_id', 'products_pre_order', 'custom_products_type_id', 'orders_products_is_compensate', 'orders_products_purchase_eta', 'products_categories_id'], 'integer'],
            [['products_delivered_quantity', 'products_good_delivered_quantity', 'products_good_delivered_price', 'products_canceled_quantity', 'products_canceled_price', 'products_reversed_quantity', 'products_reversed_price'], 'string', 'max' => 15],
            [['products_model'], 'string', 'max' => 12],
            [['products_name'], 'string', 'max' => 255],
            [['orders_products_store_price', 'products_price', 'final_price'], 'string', 'max' => 19],
            [['products_tax'], 'string', 'max' => 7],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products',
            'orders_id' => 'Orders',
            'products_id' => 'Products',
            'products_model' => 'Products Model',
            'products_name' => 'Products Name',
            'orders_products_store_price' => 'Orders Products Store Price',
            'products_price' => 'Products Price',
            'final_price' => 'Final Price',
            'op_rebate' => 'Op Rebate',
            'op_rebate_delivered' => 'Op Rebate Delivered',
            'products_tax' => 'Products Tax',
            'products_quantity' => 'Products Quantity',
            'products_delivered_quantity' => 'Products Delivered Quantity',
            'products_good_delivered_quantity' => 'Products Good Delivered Quantity',
            'products_good_delivered_price' => 'Products Good Delivered Price',
            'products_canceled_quantity' => 'Products Canceled Quantity',
            'products_canceled_price' => 'Products Canceled Price',
            'products_reversed_quantity' => 'Products Reversed Quantity',
            'products_reversed_price' => 'Products Reversed Price',
            'products_bundle_id' => 'Products Bundle',
            'parent_orders_products_id' => 'Parent Orders Products',
            'products_pre_order' => 'Products Pre Order',
            'custom_products_type_id' => 'Custom Products Type',
            'orders_products_is_compensate' => 'Orders Products Is Compensate',
            'orders_products_purchase_eta' => 'Orders Products Purchase Eta',
            'products_categories_id' => 'Products Categories',
        ];
    }
    
    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    public function getOrderProductDetails($orderId) {
        return static::find()->select('orders_products_id, custom_products_type_id, products_id, products_name, products_model, products_price,
            products_tax, products_quantity, products_delivered_quantity, final_price, products_pre_order,
            products_categories_id, orders_products_is_compensate')->where([
                'orders_id' => $orderId,
                'products_bundle_id' => '0',
            ])->all();
    }

    public function getSubProductInfo($orderId, $orderProductId, $datePurchase) {
        return static::find()->select(['orders_products_id', 'products_id', 'products_name', 'products_model', 'products_price', 'products_tax', 'products_quantity',
            'final_price', 'products_categories_id', 'products_delivered_quantity', 'products_categories_id', 'orders_products_purchase_eta',
            'products_good_delivered_quantity', 'products_canceled_quantity', 'products_reversed_quantity',
            'IF(orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
            NULL,
            (TO_DAYS(DATE_ADD(:datePurchase, INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(:datePurchase, INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
            ) AS purchase_eta'])->where([
                'orders_id' => $orderId,
                'parent_orders_products_id' => $orderProductId,
            ], [
                'datePurchase' => $datePurchase,
            ])->all();
    }

}
