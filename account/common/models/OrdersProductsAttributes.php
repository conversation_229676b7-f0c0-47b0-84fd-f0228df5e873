<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_products_attributes}}".
 *
 * @property int $orders_products_attributes_id
 * @property int $orders_id
 * @property int $orders_products_id
 * @property string $products_options
 * @property string $products_options_values
 * @property string $options_values_price
 * @property string $price_prefix
 * @property int $products_options_id
 * @property int $products_options_values_id
 */
class OrdersProductsAttributes extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_products_attributes}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'products_options_id', 'products_options_values_id'], 'integer'],
            [['options_values_price'], 'string', 'max' => 15],
            [['products_options', 'products_options_values'], 'string', 'max' => 32],
            [['price_prefix'], 'string', 'max' => 1],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_products_attributes_id' => 'Orders Products Attributes',
            'orders_id' => 'Orders',
            'orders_products_id' => 'Orders Products',
            'products_options' => 'Products Options',
            'products_options_values' => 'Products Options Values',
            'options_values_price' => 'Options Values Price',
            'price_prefix' => 'Price Prefix',
            'products_options_id' => 'Products Options',
            'products_options_values_id' => 'Products Options Values',
        ];
    }
    
    public function getProductAttributeInfo($orderId, $orderProductId) {
        return static::find()->select('products_options_id, products_options_values_id, products_options, products_options_values, options_values_price, price_prefix')->where([
            'orders_id' => $orderId,
            'orders_products_id' => $orderProductId,
        ])->all();
    }

}
