<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_products_extra_info}}".
 *
 * @property int $orders_products_id
 * @property string $orders_products_extra_info_key
 * @property string $orders_products_extra_info_value
 */
class OrdersProductsExtraInfo extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_products_extra_info}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_products_extra_info_value'], 'required'],
            [['orders_products_id'], 'integer'],
            [['orders_products_extra_info_key'], 'string', 'max' => 100],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products',
            'orders_products_extra_info_key' => 'Orders Products Extra Info Key',
            'orders_products_extra_info_value' => 'Orders Products Extra Info Value',
        ];
    }
    
    public function getDeliveryMode($orderProductId) {
        $result = static::find()->select('orders_products_extra_info_value')->where([
            'orders_products_id' => $orderProductId,
            'orders_products_extra_info_key' => 'delivery_mode',
        ])->one();
        return isset($result->orders_products_extra_info_value) ? $result->orders_products_extra_info_value : '';
    }

}
