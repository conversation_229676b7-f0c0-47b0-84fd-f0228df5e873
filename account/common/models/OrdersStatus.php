<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_status}}".
 *
 * @property int $orders_status_id
 * @property int $language_id
 * @property string $orders_status_name
 * @property int $orders_status_sort_order
 */
class OrdersStatus extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_status}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_status_id', 'language_id', 'orders_status_sort_order'], 'integer'],
            [['orders_status_name'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_status_id' => 'Orders Status',
            'language_id' => 'Language',
            'orders_status_name' => 'Orders Status Name',
            'orders_status_sort_order' => 'Orders Status Sort Order',
        ];
    }
    
    public function getOrderStatus($id) {
        $c = Yii::$app->db;
        $c_sql = "SELECT orders_status_name FROM " . $this->tableName() . " 
                WHERE orders_status_id = " . (int) $id . " 
                    AND (IF (language_id = " . Yii::t("dev", "LANG_CODE_TO_ID") . ", 1, 
                        IF(( SELECT COUNT(orders_status_id) > 0 FROM " . $this->tableName() . " 
                            WHERE orders_status_id = " . (int) $id . " 
                                AND language_id = " . Yii::t("dev", "LANG_CODE_TO_ID") . "), 0, language_id = " . Yii::$app->params["REG_CONFIG"]["DEF_LANGUAGE_ID"] . ")))";
        $c_res = $c->createCommand($c_sql)->query()->read();

        return isset($c_res["orders_status_name"]) ? $c_res["orders_status_name"] : null;
    }

}
