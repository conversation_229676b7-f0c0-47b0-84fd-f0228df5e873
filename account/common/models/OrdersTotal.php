<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%orders_total}}".
 *
 * @property int $orders_total_id
 * @property int $orders_id
 * @property string $title
 * @property string $text
 * @property string $value
 * @property string $class
 * @property int $sort_order
 */
class OrdersTotal extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%orders_total}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id', 'sort_order'], 'integer'],
            [['value'], 'string', 'max' => 15],
            [['title', 'text'], 'string', 'max' => 255],
            [['class'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_total_id' => 'Orders Total',
            'orders_id' => 'Orders',
            'title' => 'Title',
            'text' => 'Text',
            'value' => 'Value',
            'class' => 'Class',
            'sort_order' => 'Sort Order',
        ];
    }
    
    public function getOrderTotalInfo($orderId) {
        return static::find()->select('title, text, class, value')->where([
            'orders_id' => $orderId,
        ])->orderBy('sort_order')->all();
    }

}
