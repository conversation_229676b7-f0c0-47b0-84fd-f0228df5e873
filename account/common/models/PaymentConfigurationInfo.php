<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_configuration_info}}".
 *
 * @property int $payment_configuration_info_id
 * @property int $payment_methods_id
 * @property string $payment_configuration_info_title
 * @property string $payment_configuration_info_key
 * @property string $payment_configuration_info_description
 * @property int $payment_configuration_info_sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property string $use_function
 * @property string $set_function
 * 
 * @property PaymentConfigurationInfoDescription $pcid
 */
class PaymentConfigurationInfo extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_configuration_info}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['payment_methods_id', 'payment_configuration_info_sort_order'], 'integer'],
            [['last_modified', 'date_added'], 'safe'],
            [['payment_configuration_info_title', 'payment_configuration_info_key'], 'string', 'max' => 64],
            [['payment_configuration_info_description', 'use_function', 'set_function'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'payment_configuration_info_id' => 'Payment Configuration Info',
            'payment_methods_id' => 'Payment Methods',
            'payment_configuration_info_title' => 'Payment Configuration Info Title',
            'payment_configuration_info_key' => 'Payment Configuration Info Key',
            'payment_configuration_info_description' => 'Payment Configuration Info Description',
            'payment_configuration_info_sort_order' => 'Payment Configuration Info Sort Order',
            'last_modified' => 'Last Modified',
            'date_added' => 'Date Added',
            'use_function' => 'Use Function',
            'set_function' => 'Set Function',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPcid() {
        return $this->hasOne(PaymentConfigurationInfoDescription::className(), ['payment_configuration_info_id' => 'payment_configuration_info_id'])->alias('pcid');
    }

}
