<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_configuration_info_description}}".
 *
 * @property int $payment_configuration_info_id
 * @property int $languages_id
 * @property string $payment_configuration_info_value
 * 
 * @property PaymentConfigurationInfo $payment_configuration_info
 */
class PaymentConfigurationInfoDescription extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_configuration_info_description}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['payment_configuration_info_value'], 'required'],
            [['payment_configuration_info_id', 'languages_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'payment_configuration_info_id' => 'Payment Configuration Info',
            'languages_id' => 'Languages',
            'payment_configuration_info_value' => 'Payment Configuration Info Value',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentConfigurationInfo() {
        return $this->hasOne(PaymentConfigurationInfo::className(), ['payment_configuration_info_id' => 'payment_configuration_info_id']);
    }

}
