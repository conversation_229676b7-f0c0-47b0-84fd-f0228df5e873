<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_extra_info}}".
 *
 * @property int $orders_id
 * @property string $transaction_id
 * @property int $transaction_status
 * @property string $transaction_amount
 * @property string $transaction_currency
 * @property string $transaction_text_amount
 * @property string $credit_card_type
 * @property string $credit_card_owner
 * @property string $cardholder_aut_result
 * @property string $email_address
 * @property string $billing_address
 * @property string $country
 * @property string $country_code
 * @property string $ip_address
 * @property string $tel
 * @property string $fax
 * @property string $check_result
 * @property string $alert_message
 * @property string $authorisation_mode
 * @property string $authorisation_result
 * @property string $transaction_net_amount
 */
class PaymentExtraInfo extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_extra_info}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id', 'transaction_status'], 'integer'],
            [['authorisation_result'], 'safe'],
            [['transaction_id', 'transaction_amount', 'country_code'], 'string', 'max' => 15],
            [['authorisation_mode'], 'string', 'max' => 1],
            [['transaction_currency'], 'string', 'max' => 10],
            [['transaction_text_amount', 'tel', 'fax'], 'string', 'max' => 32],
            [['credit_card_type', 'cardholder_aut_result', 'ip_address'], 'string', 'max' => 64],
            [['credit_card_owner', 'email_address', 'billing_address', 'country', 'check_result', 'alert_message'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders',
            'transaction_id' => 'Transaction',
            'transaction_status' => 'Transaction Status',
            'transaction_amount' => 'Transaction Amount',
            'transaction_currency' => 'Transaction Currency',
            'transaction_text_amount' => 'Transaction Text Amount',
            'credit_card_type' => 'Credit Card Type',
            'credit_card_owner' => 'Credit Card Owner',
            'cardholder_aut_result' => 'Cardholder Aut Result',
            'email_address' => 'Email Address',
            'billing_address' => 'Billing Address',
            'country' => 'Country',
            'country_code' => 'Country Code',
            'ip_address' => 'Ip Address',
            'tel' => 'Tel',
            'fax' => 'Fax',
            'check_result' => 'Check Result',
            'alert_message' => 'Alert Message',
            'authorisation_mode' => 'Authorisation Mode',
            'authorisation_result' => 'Payment Info', // to display at offline payment popup
        ];
    }
}
