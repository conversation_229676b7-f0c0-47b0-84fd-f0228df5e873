<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_methods}}".
 *
 * @property int $payment_methods_id
 * @property string $payment_methods_code
 * @property string $payment_methods_title
 * @property int $payment_methods_send_status
 * @property int $payment_methods_send_status_mode
 * @property string $payment_methods_send_mode_name
 * @property string $payment_methods_send_required_info
 * @property string $payment_methods_send_currency
 * @property int $payment_methods_estimated_receive_period
 * @property int $payment_methods_send_mass_payment
 * @property string $payment_methods_send_available_sites
 * @property int $payment_methods_receive_status
 * @property int $payment_methods_receive_status_mode
 * @property int $payment_methods_receive_featured_status
 * @property int $payment_methods_sort_order
 * @property string $payment_methods_admin_groups_id
 * @property int $payment_methods_types_id
 * @property int $payment_methods_parent_id
 * @property string $payment_methods_legend_color
 * @property string $payment_methods_filename
 * @property string $payment_methods_logo
 * @property string $date_added
 * @property string $last_modified
 * 
 * @property PaymentMethodsTypes $payment_methods_types
 */
class PaymentMethods extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_methods}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['payment_methods_admin_groups_id'], 'required'],
            [['payment_methods_send_required_info', 'date_added', 'last_modified'], 'safe'],
            [['payment_methods_code'], 'string', 'max' => 20],
            [['payment_methods_title', 'payment_methods_send_mode_name', 'payment_methods_filename', 'payment_methods_logo'], 'string', 'max' => 255],
            [['payment_methods_send_status', 'payment_methods_send_status_mode', 'payment_methods_send_mass_payment', 'payment_methods_receive_status', 'payment_methods_receive_status_mode', 'payment_methods_sort_order', 'payment_methods_types_id', 'payment_methods_parent_id'], 'integer'],
            [['payment_methods_send_currency'], 'string', 'max' => 3],
            [['payment_methods_send_available_sites'], 'string', 'max' => 12],
            [['payment_methods_estimated_receive_period'], 'string', 'max' => 11],
            [['payment_methods_legend_color'], 'string', 'max' => 7],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'payment_methods_id' => 'Payment Methods',
            'payment_methods_code' => 'Payment Methods Code',
            'payment_methods_title' => 'Payment Methods Title',
            'payment_methods_send_status' => 'Payment Methods Send Status',
            'payment_methods_send_status_mode' => 'Payment Methods Send Status Mode',
            'payment_methods_send_mode_name' => 'Payment Methods Send Mode Name',
            'payment_methods_send_required_info' => 'Payment Methods Send Required Info',
            'payment_methods_send_currency' => 'Payment Methods Send Currency',
            'payment_methods_estimated_receive_period' => 'Payment Methods Estimated Receive Period',
            'payment_methods_send_mass_payment' => 'Payment Methods Send Mass Payment',
            'payment_methods_send_available_sites' => 'Payment Methods Send Available Sites',
            'payment_methods_receive_status' => 'Payment Methods Receive Status',
            'payment_methods_receive_status_mode' => 'Payment Methods Receive Status Mode',
            'payment_methods_sort_order' => 'Payment Methods Sort Order',
            'payment_methods_admin_groups_id' => 'Payment Methods Admin Groups',
            'payment_methods_types_id' => 'Payment Methods Types',
            'payment_methods_parent_id' => 'Payment Methods Parent',
            'payment_methods_legend_color' => 'Payment Methods Legend Color',
            'payment_methods_filename' => 'Payment Methods Filename',
            'payment_methods_logo' => 'Payment Methods Logo',
            'date_added' => 'Date Added',
            'last_modified' => 'Last Modified',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentMethodsTypes() {
        return $this->hasOne(PaymentMethodsTypes::className(), ['payment_methods_types_id' => 'payment_methods_types_id']);
    }
    
    public function getPaymentMethodTypeId($pmId) {
        $result = static::find()->select('payment_methods_types_id')->where([
            'payment_methods_id' => $pmId,
        ])->one();
        return isset($result->payment_methods_types_id) ? $result->payment_methods_types_id : null;
    }

}
