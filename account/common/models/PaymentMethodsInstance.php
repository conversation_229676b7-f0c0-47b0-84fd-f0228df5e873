<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_methods_instance}}".
 *
 * @property int $payment_methods_instance_id
 * @property int $payment_methods_id
 * @property string $currency_code
 * @property int $payment_methods_instance_default
 * @property int $payment_methods_instance_follow_default
 */
class PaymentMethodsInstance extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_methods_instance}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['payment_methods_instance_id', 'payment_methods_id'], 'required'],
            [['payment_methods_instance_id', 'payment_methods_id'], 'integer'],
            [['currency_code'], 'string', 'max' => 3],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'payment_methods_instance_id' => 'Payment Methods Instance ID',
            'payment_methods_id' => 'Payment Methods ID',
            'currency_code' => 'Currency Code',
            'payment_methods_instance_default' => 'Payment Methods Instance Default',
            'payment_methods_instance_follow_default' => 'Payment Methods Instance Follow Default',
        ];
    }
}
