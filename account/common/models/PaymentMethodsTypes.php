<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%payment_methods_types}}".
 *
 * @property int $payment_methods_types_id
 * @property string $payment_methods_types_name
 * @property string $payment_methods_types_mode
 * @property int $payment_methods_types_system_define
 * @property string $payment_methods_types_sites
 * @property int $payment_methods_types_sort_order
 */
class PaymentMethodsTypes extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%payment_methods_types}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['payment_methods_types_system_define', 'payment_methods_types_sort_order'], 'integer'],
            [['payment_methods_types_name'], 'string', 'max' => 255],
            [['payment_methods_types_mode'], 'string', 'max' => 7],
            [['payment_methods_types_sites'], 'string', 'max' => 16],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'payment_methods_types_id' => 'Payment Methods Types',
            'payment_methods_types_name' => 'Payment Methods Types Name',
            'payment_methods_types_mode' => 'Payment Methods Types Mode',
            'payment_methods_types_system_define' => 'Payment Methods Types System Define',
            'payment_methods_types_sites' => 'Payment Methods Types Sites',
            'payment_methods_types_sort_order' => 'Payment Methods Types Sort Order',
        ];
    }
}
