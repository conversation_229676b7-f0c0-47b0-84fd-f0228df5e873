<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%products_groups}}".
 *
 * @property int $customers_group_id
 * @property string $customers_group_price
 * @property int $products_id
 * @property string $products_price
 */
class ProductsGroups extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%products_groups}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_group_id', 'products_id'], 'integer'],
            [['customers_group_price', 'products_price'], 'string', 'max' => 15],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_group_id' => 'Customers Group ID',
            'customers_group_price' => 'Customers Group Price',
            'products_id' => 'Products ID',
            'products_price' => 'Products Price',
        ];
    }
    
    public function getCustomerGroupPrice($customerGroupId, $productId) {
        $result = static::find()->select('customers_group_price')->where([
            'customers_group_id' => $customerGroupId,
            'products_id' => $productId,
        ])->one();
        return isset($result->customers_group_price) ? $result->customers_group_price : '';
    }

}
