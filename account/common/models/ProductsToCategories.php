<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%products_to_categories}}".
 *
 * @property int $products_id
 * @property int $categories_id
 * @property int $products_is_link
 * 
 * @property Products $products
 * @property Categories $cat
 */
class ProductsToCategories extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%products_to_categories}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['products_id', 'categories_id', 'products_is_link'], 'required'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products',
            'categories_id' => 'Categories',
            'products_is_link' => 'Products Is Link',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducts() {
        return $this->hasOne(Products::className(), ['products_id' => 'products_id']);
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCat() {
        return $this->hasOne(Categories::className(), ['categories_id' => 'categories_id'])->onCondition('cat.categories_status = 1')->alias('cat');
    }
    
}
