<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "remember_me_token".
 *
 * @property string $id
 * @property integer $customers_id
 * @property string $token
 * @property string $token_id
 * @property string $login_ip
 * @property string $login_country
 * @property string $user_agent
 * @property string $parsed_ua_os
 * @property string $parsed_ua_browser
 * @property integer $expire
 */
class RememberMeToken extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'remember_me_token';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id'], 'required'],
            [['customers_id', 'expire'], 'integer'],
            [['token', 'token_id', 'user_agent', 'parsed_ua_os', 'parsed_ua_browser'], 'string', 'max' => 255],
            [['login_ip'], 'string', 'max' => 128],
            [['login_country'], 'string', 'max' => 2],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'token' => 'Token',
            'token_id' => 'Token ID',
            'login_ip' => 'Login Ip',
            'login_country' => 'Login Country',
            'user_agent' => 'User Agent',
            'parsed_ua_os' => 'Parsed Ua Os',
            'parsed_ua_browser' => 'Parsed Ua Browser',
            'expire' => 'Expire',
        ];
    }
}
