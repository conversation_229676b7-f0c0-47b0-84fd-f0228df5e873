<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%client_extra_info}}".
 *
 * @property string $client_id
 * @property string $extra_key
 * @property string $extra_value
 */
class ShassoClientExtraInfo extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%client_extra_info}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['client_id', 'extra_key', 'extra_value'], 'required'],
            [['client_id', 'extra_key'], 'string', 'max' => 40],
            [['extra_value'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'client_id' => 'Client',
            'extra_key' => 'Extra Key',
            'extra_value' => 'Extra Value',
        ];
    }
}
