<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%remember_me_token_update_log}}".
 *
 * @property int $id
 * @property int $customers_id
 * @property string $token_id
 * @property string $login_ip
 * @property string $login_country
 * @property string $user_agent
 * @property string $parsed_ua_os
 * @property string $parsed_ua_browser
 * @property string $prev_login_ip
 * @property string $prev_login_country
 * @property string $prev_user_agent
 * @property string $prev_parsed_ua_os
 * @property string $prev_parsed_ua_browser
 * @property string $type
 * @property string $created_date
 */
class ShassoRememberMeTokenUpdateLog extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%remember_me_token_update_log}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'token_id'], 'required'],
            [['customers_id'], 'integer'],
            [['created_date'], 'safe'],
            [['token_id', 'login_ip', 'login_country', 'user_agent', 'parsed_ua_os', 'parsed_ua_browser', 'prev_login_ip', 'prev_login_country', 'prev_user_agent', 'prev_parsed_ua_os', 'prev_parsed_ua_browser', 'type'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'token_id' => 'Token ID',
            'login_ip' => 'Login IP',
            'login_country' => 'Login Country',
            'user_agent' => 'User Agent',
            'parsed_ua_os' => 'Parsed UA OS',
            'parsed_ua_browser' => 'Parsed UA Browser',
            'prev_login_ip' => 'Prev Login IP',
            'prev_login_country' => 'Prev Login Country',
            'prev_user_agent' => 'Prev User Agent',
            'prev_parsed_ua_os' => 'Prev Parsed UA OS',
            'prev_parsed_ua_browser' => 'Prev Parsed UA Browser',
            'type' => 'Type',
            'created_date' => 'Created Date',
        ];
    }

    public static function log($type, $prev_data, $current_data) {
        if (!$prev_data) {
            return;
        }
        $save_data = [
            'customers_id' => $current_data['customers_id'],
            'token_id' => $current_data['token_id'],
            'login_ip' => $current_data['login_ip'],
            'login_country' => $current_data['login_country'],
            'user_agent' => $current_data['user_agent'],
            'parsed_ua_os' => $current_data['parsed_ua_os'],
            'parsed_ua_browser' => $current_data['parsed_ua_browser'],
            //$prev_data is ALWAYS in DDB format!
            'prev_login_ip' => $prev_data['login_ip'],
            'prev_login_country' => $prev_data['login_country'],
            'prev_user_agent' => $prev_data['user_agent'],
            'type' => $type,
            'created_date' => new \yii\db\Expression("NOW()"),
        ];
        if (empty($prev_data['parsed_ua_os']) || empty($prev_data['parsed_ua_browser'])) {
            $parse_ua_result = \common\components\GeneralComponent::parseUserAgent($save_data['prev_user_agent']);
            $save_data['prev_parsed_ua_os'] = $parse_ua_result['os'];
            $save_data['prev_parsed_ua_browser'] = $parse_ua_result['browser'];
        } else {
            $save_data['prev_parsed_ua_os'] = $prev_data['parsed_ua_os'];
            $save_data['prev_parsed_ua_browser'] = $prev_data['parsed_ua_browser'];
        }
        if ($type == 'update') {
            if ($save_data['login_ip'] == $save_data['prev_login_ip']
                && $save_data['login_country'] == $save_data['prev_login_country']
                && $save_data['user_agent'] == $save_data['prev_user_agent']
                && $save_data['parsed_ua_os'] == $save_data['prev_parsed_ua_os']
                && $save_data['parsed_ua_browser'] == $save_data['prev_parsed_ua_browser']
                ) {
                    //No difference in data update, no need to log
                    //return;
            }
        }
        $model = new static($save_data);
        if (!$model->save()) {
            \Yii::$app->reporter->reportToAdminViaSlack('remember_me_token_update_log save failed', $model);
        }
    }
}
