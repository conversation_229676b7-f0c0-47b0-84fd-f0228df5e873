<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace common\models;

use Yii;

/**
 * Description of ShassoSplitStoreCredit
 *
 * <AUTHOR>
 */
class ShassoSplitStoreCredit extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */


    public static function tableName() {
        return '{{%store_credit_split}}';
    }

    public function rules() {
        return [
            [ [ 'user_id' ], 'required', 'message' => Yii::t('rules', 'ERROR_EMPTY_FIELD') ],
            [ [ 'og_perc', 'g2g_perc','og_wor', 'g2g_wor' ], 'integer', 'min' => 0, 'max' => 100 ],
            [['confirm'], 'safe']
        ];
    }

    public function attributeLabels() {
        return [
            'og_perc' => Yii::t('forms', 'OG Credit Percentage'),
            'g2g_perc' => Yii::t('forms', 'G2G Credit Percentage'),
            'og_wor' => Yii::t('forms', 'OffGamers Points Percentage'),
            'g2g_wor' => Yii::t('forms', 'G2G Points Percentage'),
            'user_id' => Yii::t('forms', 'Customer ID'),
            'confirm' => Yii::t('forms', 'Setting Confirmed')
        ];
    }

    public function getByUserid($user_id) {
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . Yii::$app->user->id . '/value';
        $cache_duration = 60 * 60 * 24 * 180;
        if (Yii::$app->cache->exists($cache_key)) {
            return Yii::$app->cache->get($cache_key);
        } else {
            $split_model = $this->findOne(['user_id' => Yii::$app->user->id]);
            Yii::$app->cache->set($cache_key, $split_model, $cache_duration);
            return $split_model;
        }
    }

    public function afterSave($insert, $changedAttributes) {

        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . \Yii::$app->user->id . '/value';
        $cache_duration = 60 * 60 * 24 * 180;
        $setting_model = $this->findOne(['user_id' => $this->user_id]);
        Yii::$app->cache->set($cache_key, $setting_model, $cache_duration);
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete() {
        parent::afterDelete();
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . \Yii::$app->user->id . '/value';
        Yii::$app->cache->delete($cache_key);
    }

}
