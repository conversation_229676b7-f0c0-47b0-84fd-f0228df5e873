<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%sso_token}}".
 *
 * @property string $sso_token
 * @property string $sess_id
 * @property string $client_id
 * @property int $user_id
 * @property int $login_method
 * @property string $expiry
 */
class ShassoSsoToken extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%sso_token}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sso_token', 'sess_id', 'client_id', 'user_id', 'login_method'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['login_method'], 'string', 'max' => 20],
            [['expiry'], 'safe'],
            [['sso_token'], 'string', 'max' => 36],
            [['sess_id'], 'string', 'max' => 32],
            [['client_id'], 'string', 'max' => 40],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'sso_token' => 'Sso Token',
            'sess_id' => 'Sess',
            'client_id' => 'Client',
            'user_id' => 'User',
            'login_method' => 'Login Method',
            'expiry' => 'Expiry',
        ];
    }
    
    public function getOnlineStatus($uid) {
        return static::find()->where([
            'user_id' => $uid,
        ])->exists();
    }
}
