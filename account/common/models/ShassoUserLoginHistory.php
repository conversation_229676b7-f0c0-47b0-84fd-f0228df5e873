<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%user_login_history}}".
 *
 * @property int $user_id
 * @property string $login_date
 * @property string $login_ip
 * @property string $login_ua_info
 * @property string $login_method
 */
class ShassoUserLoginHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user_login_history}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'login_date', 'login_ip', 'login_ua_info', 'login_method'], 'required'],
            [['user_id'], 'integer'],
            [['login_date'], 'safe'],
            [['login_ip'], 'string', 'max' => 128],
            [['login_ua_info'], 'string', 'max' => 255],
            [['login_method'], 'string', 'max' => 12],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User',
            'login_date' => 'Login Date',
            'login_ip' => 'Login Ip',
            'login_ua_info' => 'Login Ua Info',
            'login_method' => 'Login Method',
        ];
    }
}
