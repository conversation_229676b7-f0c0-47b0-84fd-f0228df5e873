<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%user_preference}}".
 *
 * @property int $user_id
 * @property string $preference_key
 * @property string $value
 */
class ShassoUserPreference extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user_preference}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'preference_key', 'value'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['preference_key'], 'string', 'max' => 32],
            [['value'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User',
            'preference_key' => 'Preference Key',
            'value' => 'Value',
        ];
    }
    
    public function updateUserPreference($uid, $key, $val) {
        $m_attr = [
            "user_id" => $uid,
            "preference_key" => $key,
            "value" => $val
        ];
        
        $m_data = static::findOne(['user_id' => $uid, 'preference_key' => $key]);
        if (isset($m_data->user_id)) {
            static::updateAll($m_attr, ['user_id' => $uid, 'preference_key' => $key]);
        } else {
            $this->saveNewRecord($m_attr);
        }
    }
}
