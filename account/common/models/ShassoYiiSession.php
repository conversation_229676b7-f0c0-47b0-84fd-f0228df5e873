<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%shasso_yii_session}}".
 *
 * @property string $id
 * @property int $user_id
 * @property int $expire
 * @property resource $data
 */
class ShassoYiiSession extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%shasso_yii_session}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['expire'], 'integer'],
            [['data'], 'string'],
            [['id'], 'string', 'max' => 32],
            [['user_id'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User',
            'expire' => 'Expire',
            'data' => 'Data',
        ];
    }

    public static function deleteAllSessionsForUser($user_id, $except_session_id = null) {
        Yii::$app->readSession->destroyUserSessions($user_id, $except_session_id);
        return true;
    }
}
