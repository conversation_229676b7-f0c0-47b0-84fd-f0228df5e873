<?php

namespace common\models;

/**
 * This is the model class for table "{{%sms_report}}".
 *
 * @property int $customers_id
 * @property string $sms_request_date
 * @property string $sms_request_type
 * @property string $sms_request_phone_number
 * @property int $sms_request_phone_country_id
 * @property string $sms_request_page
 * @property string $sms_provider
 * @property string $sms_send_date
 */
class SmsReport extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%sms_report}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'sms_request_phone_country_id'], 'string', 'max' => 11],
            [['sms_request_type'], 'string', 'max' => 16],
            [['sms_request_phone_number'], 'string', 'max' => 32],
            [['sms_request_page'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'sms_request_date' => 'Sms Request Date',
            'sms_request_type' => 'Sms Request Type',
            'sms_request_phone_number' => 'Sms Request Phone Number',
            'sms_request_phone_country_id' => 'Sms Request Phone Country',
            'sms_request_page' => 'Sms Request Page',
            'sms_provider' => 'Sms Provider',
            'sms_send_date' => 'Sms Send Date',
        ];
    }
    
    public function saveSmsReport($smsReportData) {
        $this->customers_id = $smsReportData['customers_id'];
        $this->sms_request_date = date('Y-m-d H:i:s');
        $this->sms_request_type = $smsReportData['sms_request_type'];
        $this->sms_request_phone_number = $smsReportData['sms_request_phone_number'];
        $this->sms_request_phone_country_id = $smsReportData['sms_request_phone_country_id'];
        $this->sms_request_page = $smsReportData['sms_request_page'];
        $this->sms_provider = $smsReportData['sms_provider'];
        $this->sms_send_date = isset($smsReportData['sms_is_sent']) ? $smsReportData['sms_is_sent'] : false;

        $this->insertOrUpdate($this->getAttributes([
            'customers_id',
            'sms_request_date',
            'sms_request_type',
            'sms_request_phone_number',
            'sms_request_phone_country_id',
            'sms_request_page',
            'sms_provider',
            'sms_send_date'
        ]));
    }
    
    public function updateSmsReport($smsReportData) {
        $this->setIsNewRecord(false);
        $this->sms_send_date = date('Y-m-d H:i:s');
        $this->save();
    }
}
