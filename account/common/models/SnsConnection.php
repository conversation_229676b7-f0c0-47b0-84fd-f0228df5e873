<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%sns_connection}}".
 *
 * @property int $customers_id
 * @property string $provider
 * @property string $provider_uid
 * @property string $username
 * @property string $created_date
 */
class SnsConnection extends \common\components\CoreModel
{
    public static $sns_force_xpress_form = array("Facebook");

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%sns_connection}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'provider', 'provider_uid'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['created_date'], 'safe'],
            [['provider'], 'string', 'max' => 20],
            [['provider_uid'], 'string', 'max' => 32],
            [['username'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'provider' => 'Provider',
            'provider_uid' => 'Provider Uid',
            'username' => 'Username',
            'created_date' => 'Created Date',
        ];
    }
    
    public function createConnection($cid, $provider, $uid, $uname) {
        $m_sc = static::findOne(["customers_id" => $cid, "provider" => $provider, "provider_uid" => $uid]);
        if (!isset($m_sc->customers_id)) {
            $m_attr = array(
                'customers_id' => $cid,
                'provider' => $provider,
                'provider_uid' => $uid,
                'username' => $uname
            );
            $this->saveNewRecord($m_attr);
            unset($m_attr);
        }
    }

    public function getConnections($cid) {
        $result = array();
        $c_data = static::findAll(["customers_id" => $cid]);
        if (!empty($c_data)) {
            foreach ($c_data as $_data) {
                $result[$_data->provider] = $_data->provider_uid;
            }
        }
        return $result;
    }

    public function removeConnection($uid, $provider) {
        static::deleteAll(["customers_id" => $uid, "provider" => $provider]);
    }

}
