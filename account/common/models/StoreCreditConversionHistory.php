<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_credit_conversion_history}}".
 *
 * @property int $store_credit_conversion_history_id
 * @property int $customer_id
 * @property string $store_credit_conversion_history_date
 * @property string $store_credit_account_type
 * @property int $store_credit_conversion_from_currency_id
 * @property string $store_credit_conversion_from_amount
 * @property string $store_credit_conversion_from_reserve_amount
 * @property int $store_credit_conversion_to_currency_id
 * @property string $store_credit_conversion_to_amount
 * @property string $store_credit_conversion_to_reserve_amount
 * @property string $store_credit_conversion_currency_values
 */
class StoreCreditConversionHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_credit_conversion_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'store_credit_conversion_from_currency_id', 'store_credit_conversion_to_currency_id'], 'integer'],
            [['store_credit_conversion_history_date'], 'safe'],
            [['store_credit_conversion_from_amount', 'store_credit_conversion_from_reserve_amount', 'store_credit_conversion_to_amount', 'store_credit_conversion_to_reserve_amount'], 'string', 'max' => 15],
            [['store_credit_account_type'], 'string', 'max' => 4],
            [['store_credit_conversion_currency_values'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_credit_conversion_history_id' => 'Store Credit Conversion History',
            'customer_id' => 'Customer',
            'store_credit_conversion_history_date' => 'Store Credit Conversion History Date',
            'store_credit_account_type' => 'Store Credit Account Type',
            'store_credit_conversion_from_currency_id' => 'Store Credit Conversion From Currency',
            'store_credit_conversion_from_amount' => 'Store Credit Conversion From Amount',
            'store_credit_conversion_from_reserve_amount' => 'Store Credit Conversion From Reserve Amount',
            'store_credit_conversion_to_currency_id' => 'Store Credit Conversion To Currency',
            'store_credit_conversion_to_amount' => 'Store Credit Conversion To Amount',
            'store_credit_conversion_to_reserve_amount' => 'Store Credit Conversion To Reserve Amount',
            'store_credit_conversion_currency_values' => 'Store Credit Conversion Currency Values',
        ];
    }
    
    public function saveInfo($scDataArr) {
        $this->setIsNewRecord(true);
        unset($this->store_credit_conversion_history_id);
        foreach ($scDataArr as $key => $val) {
            $this->$key = $val;
        }
        $this->save();
    }

}
