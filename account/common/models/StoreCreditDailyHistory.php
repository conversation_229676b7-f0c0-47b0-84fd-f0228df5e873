<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_credit_daily_history}}".
 *
 * @property string $store_credit_daily_history_date
 * @property string $store_credit_daily_history_currency
 * @property string $store_credit_daily_history_credit_type
 * @property string $store_credit_daily_history_amount
 * @property string $store_credit_daily_history_reserved_amount
 * @property string $user_id
 * @property string $user_role
 */
class StoreCreditDailyHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_credit_daily_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['store_credit_daily_history_amount', 'store_credit_daily_history_reserved_amount'], 'string', 'max' => 15],
            [['store_credit_daily_history_currency'], 'string', 'max' => 3],
            [['store_credit_daily_history_credit_type'], 'string', 'max' => 4],
            [['user_id'], 'string', 'max' => 32],
            [['user_role'], 'string', 'max' => 16],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_credit_daily_history_date' => 'Store Credit Daily History Date',
            'store_credit_daily_history_currency' => 'Store Credit Daily History Currency',
            'store_credit_daily_history_credit_type' => 'Store Credit Daily History Credit Type',
            'store_credit_daily_history_amount' => 'Store Credit Daily History Amount',
            'store_credit_daily_history_reserved_amount' => 'Store Credit Daily History Reserved Amount',
            'user_id' => 'User',
            'user_role' => 'User Role',
        ];
    }
    
}
