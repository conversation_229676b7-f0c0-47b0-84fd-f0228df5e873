<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_credit_history}}".
 *
 * @property int $store_credit_history_id
 * @property int $customer_id
 * @property string $store_credit_account_type
 * @property string $store_credit_history_date
 * @property int $store_credit_transaction_reserved
 * @property int $store_credit_history_currency_id
 * @property string $store_credit_history_debit_amount
 * @property string $store_credit_history_credit_amount
 * @property string $store_credit_history_r_after_balance
 * @property string $store_credit_history_nr_after_balance
 * @property string $store_credit_history_trans_type
 * @property string $store_credit_history_trans_id
 * @property string $store_credit_activity_type
 * @property string $store_credit_history_activity_title
 * @property string $store_credit_history_activity_desc
 * @property int $store_credit_history_activity_desc_show
 * @property string $store_credit_history_added_by
 * @property string $store_credit_history_added_by_role
 * @property string $store_credit_history_admin_messages
 */
class StoreCreditHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_credit_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'store_credit_transaction_reserved', 'store_credit_history_currency_id', 'store_credit_history_activity_desc_show'], 'integer'],
            [['store_credit_history_date'], 'safe'],
            [['store_credit_history_debit_amount', 'store_credit_history_credit_amount', 'store_credit_history_r_after_balance', 'store_credit_history_nr_after_balance'], 'string', 'max' => 15],
            [['store_credit_account_type'], 'string', 'max' => 3],
            [['store_credit_history_trans_id'], 'string', 'max' => 255],
            [['store_credit_activity_type'], 'string', 'max' => 2],
            [['store_credit_history_activity_title', 'store_credit_history_added_by'], 'string', 'max' => 128],
            [['store_credit_history_added_by_role'], 'string', 'max' => 16],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_credit_history_id' => 'Store Credit History',
            'customer_id' => 'Customer',
            'store_credit_account_type' => 'Store Credit Account Type',
            'store_credit_history_date' => 'Store Credit History Date',
            'store_credit_transaction_reserved' => 'Store Credit Transaction Reserved',
            'store_credit_history_currency_id' => 'Store Credit History Currency',
            'store_credit_history_debit_amount' => 'Store Credit History Debit Amount',
            'store_credit_history_credit_amount' => 'Store Credit History Credit Amount',
            'store_credit_history_r_after_balance' => 'Store Credit History R After Balance',
            'store_credit_history_nr_after_balance' => 'Store Credit History Nr After Balance',
            'store_credit_history_trans_type' => 'Store Credit History Trans Type',
            'store_credit_history_trans_id' => 'Store Credit History Trans',
            'store_credit_activity_type' => 'Store Credit Activity Type',
            'store_credit_history_activity_title' => 'Store Credit History Activity Title',
            'store_credit_history_activity_desc' => 'Store Credit History Activity Desc',
            'store_credit_history_activity_desc_show' => 'Store Credit History Activity Desc Show',
            'store_credit_history_added_by' => 'Store Credit History Added By',
            'store_credit_history_added_by_role' => 'Store Credit History Added By Role',
            'store_credit_history_admin_messages' => 'Store Credit History Admin Messages',
        ];
    }
    
    public function saveInfo($historyInfo) {
        $this->customer_id = $historyInfo['customer_id'];
        $this->store_credit_account_type = $historyInfo['store_credit_account_type'];
        $this->store_credit_history_date = $historyInfo['store_credit_history_date'];
        $this->store_credit_history_currency_id = $historyInfo['store_credit_history_currency_id'];
        $this->store_credit_history_debit_amount = $historyInfo['store_credit_history_debit_amount'];
        $this->store_credit_history_credit_amount = $historyInfo['store_credit_history_credit_amount'];
        $this->store_credit_history_r_after_balance = $historyInfo['store_credit_history_r_after_balance'];
        $this->store_credit_history_nr_after_balance = $historyInfo['store_credit_history_nr_after_balance'];
        $this->store_credit_history_trans_type = $historyInfo['store_credit_history_trans_type'];
        $this->store_credit_history_trans_id = $historyInfo['store_credit_history_trans_id'];
        $this->store_credit_activity_type = $historyInfo['store_credit_activity_type'];
        $this->store_credit_history_activity_title = $historyInfo['store_credit_history_activity_title'];
        $this->store_credit_history_activity_desc = $historyInfo['store_credit_history_activity_desc'];
        $this->store_credit_history_activity_desc_show = $historyInfo['store_credit_history_activity_desc_show'];
        $this->store_credit_history_added_by = $historyInfo['store_credit_history_added_by'];
        $this->store_credit_history_added_by_role = $historyInfo['store_credit_history_added_by_role'];
        $this->save();

        $latestKey = $this->primaryKey;
        return $latestKey;
    }

}
