<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_payments}}".
 *
 * @property int $store_payments_id
 * @property int $user_id
 * @property string $user_role
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $user_country_international_dialing_code
 * @property string $user_telephone
 * @property string $user_mobile
 * @property string $store_payments_date
 * @property int $store_payments_status
 * @property string $store_payments_request_currency
 * @property string $store_payments_request_amount
 * @property string $store_payments_fees
 * @property string $store_payments_after_fees_amount
 * @property string $store_payments_paid_currency
 * @property string $store_payments_paid_currency_value
 * @property string $store_payments_paid_amount
 * @property string $store_payments_reference
 * @property int $store_payments_methods_id
 * @property string $store_payments_methods_name
 * @property int $store_payment_account_book_id
 * @property string $user_payment_methods_alias
 * @property string $store_payments_fees_calculation
 * @property string $store_payments_last_modified
 * @property int $store_payments_read_mode
 * @property int $store_payments_lock
 */
class StorePayments extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_payments}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'store_payments_status', 'store_payments_methods_id', 'store_payment_account_book_id', 'store_payments_read_mode', 'store_payments_lock'], 'integer'],
            [['store_payments_date', 'store_payments_fees_calculation', 'store_payments_last_modified'], 'safe'],
            [['store_payments_request_amount', 'store_payments_fees', 'store_payments_after_fees_amount', 'store_payments_paid_amount'], 'string', 'max' => 15],
            [['user_role'], 'string', 'max' => 16],
            [['user_firstname', 'user_lastname', 'user_telephone', 'user_mobile', 'store_payments_reference', 'user_payment_methods_alias'], 'string', 'max' => 32],
            [['user_email_address'], 'string', 'max' => 96],
            [['user_country_international_dialing_code'], 'string', 'max' => 5],
            [['store_payments_request_currency', 'store_payments_paid_currency'], 'string', 'max' => 3],
            [['store_payments_methods_name'], 'string', 'max' => 255],
            [['store_payments_paid_currency_value'], 'string', 'max' => 14],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_payments_id' => 'Store Payments',
            'user_id' => 'User',
            'user_role' => 'User Role',
            'user_firstname' => 'User Firstname',
            'user_lastname' => 'User Lastname',
            'user_email_address' => 'User Email Address',
            'user_country_international_dialing_code' => 'User Country International Dialing Code',
            'user_telephone' => 'User Telephone',
            'user_mobile' => 'User Mobile',
            'store_payments_date' => 'Store Payments Date',
            'store_payments_status' => 'Store Payments Status',
            'store_payments_request_currency' => 'Store Payments Request Currency',
            'store_payments_request_amount' => 'Store Payments Request Amount',
            'store_payments_fees' => 'Store Payments Fees',
            'store_payments_after_fees_amount' => 'Store Payments After Fees Amount',
            'store_payments_paid_currency' => 'Store Payments Paid Currency',
            'store_payments_paid_currency_value' => 'Store Payments Paid Currency Value',
            'store_payments_paid_amount' => 'Store Payments Paid Amount',
            'store_payments_reference' => 'Store Payments Reference',
            'store_payments_methods_id' => 'Store Payments Methods',
            'store_payments_methods_name' => 'Store Payments Methods Name',
            'store_payment_account_book_id' => 'Store Payment Account Book',
            'user_payment_methods_alias' => 'User Payment Methods Alias',
            'store_payments_fees_calculation' => 'Store Payments Fees Calculation',
            'store_payments_last_modified' => 'Store Payments Last Modified',
            'store_payments_read_mode' => 'Store Payments Read Mode',
            'store_payments_lock' => 'Store Payments Lock',
        ];
    }
}
