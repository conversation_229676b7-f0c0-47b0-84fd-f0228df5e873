<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_points}}".
 *
 * @property int $customers_id
 * @property string $sp_amount
 * @property string $sp_last_modified
 */
class StorePoints extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_points}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id'], 'integer'],
            [['sp_amount'], 'string', 'max' => 15],
            [['sp_last_modified'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers',
            'sp_amount' => 'Sp Amount',
            'sp_last_modified' => 'Sp Last Modified',
        ];
    }
    
    public function updateStorePoint($before_redeem, $storePoint) {
        Yii::$app->db->createCommand('LOCK TABLE ' . StorePoints::tableName() . ' WRITE;')->execute();

        $update_status = static::updateAll([
            'sp_amount' => new \yii\db\Expression("sp_amount - :storePoint", [
                ':storePoint' => $storePoint,
            ]),
        ], [
            'customers_id' => Yii::$app->user->id,
            'sp_amount' => $before_redeem
        ]);
        
        Yii::$app->db->createCommand('UNLOCK TABLES;')->execute();

        return $update_status;
    }

    public function getCustomersStorePoint() {
        if (isset(Yii::$app->user->id)) {
            $c_data = static::findOne([
                'customers_id' => Yii::$app->user->id,
            ]);
            return (isset($c_data->sp_amount) ? $c_data->sp_amount : 0);
        }

        return 0;
    }

}
