<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_points_redeem}}".
 *
 * @property int $store_points_redeem_id
 * @property int $user_id
 * @property string $user_role
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $user_country_international_dialing_code
 * @property string $user_telephone
 * @property string $user_mobile
 * @property string $store_points_redeem_date
 * @property int $store_points_redeem_status
 * @property string $store_points_redeem_amount
 * @property string $store_points_request_currency
 * @property string $store_points_request_currency_amount
 * @property string $store_points_paid_amount
 * @property string $store_points_paid_currency
 * @property string $store_points_paid_currency_amount
 * @property string $store_points_exchange_rate
 * @property string $store_points_redeem_reference
 * @property string $store_points_redeem_last_modified
 * @property int $store_points_read_mode
 */
class StorePointsRedeem extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_points_redeem}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'store_points_redeem_status', 'store_points_read_mode'], 'integer'],
            [['store_points_redeem_date', 'store_points_redeem_last_modified'], 'safe'],
            [['store_points_redeem_amount', 'store_points_request_currency_amount', 'store_points_paid_amount', 'store_points_paid_currency_amount'], 'string', 'max' => 15],
            [['user_role'], 'string', 'max' => 16],
            [['user_firstname', 'user_lastname', 'user_telephone', 'user_mobile', 'store_points_redeem_reference'], 'string', 'max' => 32],
            [['user_email_address'], 'string', 'max' => 96],
            [['user_country_international_dialing_code'], 'string', 'max' => 5],
            [['store_points_request_currency', 'store_points_paid_currency'], 'string', 'max' => 3],
            [['store_points_exchange_rate'], 'string', 'max' => 14],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_points_redeem_id' => 'Store Points Redeem',
            'user_id' => 'User',
            'user_role' => 'User Role',
            'user_firstname' => 'User Firstname',
            'user_lastname' => 'User Lastname',
            'user_email_address' => 'User Email Address',
            'user_country_international_dialing_code' => 'User Country International Dialing Code',
            'user_telephone' => 'User Telephone',
            'user_mobile' => 'User Mobile',
            'store_points_redeem_date' => 'Store Points Redeem Date',
            'store_points_redeem_status' => 'Store Points Redeem Status',
            'store_points_redeem_amount' => 'Store Points Redeem Amount',
            'store_points_request_currency' => 'Store Points Request Currency',
            'store_points_request_currency_amount' => 'Store Points Request Currency Amount',
            'store_points_paid_amount' => 'Store Points Paid Amount',
            'store_points_paid_currency' => 'Store Points Paid Currency',
            'store_points_paid_currency_amount' => 'Store Points Paid Currency Amount',
            'store_points_exchange_rate' => 'Store Points Exchange Rate',
            'store_points_redeem_reference' => 'Store Points Redeem Reference',
            'store_points_redeem_last_modified' => 'Store Points Redeem Last Modified',
            'store_points_read_mode' => 'Store Points Read Mode',
        ];
    }
}
