<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%store_points_redeem_history}}".
 *
 * @property int $store_points_redeem_history_id
 * @property int $store_points_redeem_id
 * @property int $store_points_redeem_status
 * @property string $date_added
 * @property int $payee_notified
 * @property string $comments
 * @property string $changed_by
 * @property string $changed_by_role
 */
class StorePointsRedeemHistory extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%store_points_redeem_history}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['store_points_redeem_id', 'store_points_redeem_status', 'payee_notified'], 'integer'],
            [['date_added', 'comments'], 'safe'],
            [['changed_by'], 'string', 'max' => 128],
            [['changed_by_role'], 'string', 'max' => 16],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'store_points_redeem_history_id' => 'Store Points Redeem History',
            'store_points_redeem_id' => 'Store Points Redeem',
            'store_points_redeem_status' => 'Store Points Redeem Status',
            'date_added' => 'Date Added',
            'payee_notified' => 'Payee Notified',
            'comments' => 'Comments',
            'changed_by' => 'Changed By',
            'changed_by_role' => 'Changed By Role',
        ];
    }
}
