<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%user_flags}}".
 *
 * @property int $user_flags_id
 * @property string $user_flags_name
 * @property string $user_flags_description
 * @property string $user_flags_alias
 * @property int $user_flags_status
 * @property string $user_flags_css_style
 * @property string $user_flags_on_notification
 * @property string $user_flags_off_notification
 * @property string $user_flags_admin_groups_id
 */
class UserFlags extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%user_flags}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_flags_admin_groups_id'], 'required'],
            [['user_flags_name'], 'string', 'max' => 128],
            [['user_flags_description', 'user_flags_css_style', 'user_flags_on_notification', 'user_flags_off_notification'], 'string', 'max' => 255],
            [['user_flags_alias'], 'string', 'max' => 12],
            [['user_flags_status'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_flags_id' => 'User Flags',
            'user_flags_name' => 'User Flags Name',
            'user_flags_description' => 'User Flags Description',
            'user_flags_alias' => 'User Flags Alias',
            'user_flags_status' => 'User Flags Status',
            'user_flags_css_style' => 'User Flags Css Style',
            'user_flags_on_notification' => 'User Flags On Notification',
            'user_flags_off_notification' => 'User Flags Off Notification',
            'user_flags_admin_groups_id' => 'User Flags Admin Groups',
        ];
    }
    
    public function getUserFlags() {
        return static::find()->select('user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_css_style')->where([
            'user_flags_status' => '1',
        ])->orderBy('user_flags_id')->all();
    }

}
