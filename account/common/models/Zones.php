<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "{{%zones}}".
 *
 * @property int $zone_id
 * @property int $zone_country_id
 * @property string $zone_code
 * @property string $zone_name
 */
class Zones extends \common\components\CoreModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%zones}}';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['zone_country_id'], 'integer'],
            [['zone_code'], 'string', 'max' => 32],
            [['zone_name'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'zone_id' => 'Zone',
            'zone_country_id' => 'Zone Country',
            'zone_code' => 'Zone Code',
            'zone_name' => 'Zone Name',
        ];
    }
    
    public function getStateList($countryId) {
        return static::find()->where([
            'zone_country_id' => $countryId,
        ])->all();
    }

    public function getStateName($ctry_id, $state_id) {
        $m_zone = static::find()->where([
            'zone_id' => $state_id,
            'zone_country_id' => $ctry_id,
        ])->one();
        return isset($m_zone) && !empty($m_zone->zone_name) ? $m_zone->zone_name : null;
    }

    public function getTotalState($countryId) {
        return static::find()->where([
            'zone_country_id' => $countryId,
        ])->count();
    }

    public function stateExists($ctry_id, $state_id) {
        return static::find()->where([
            'zone_id' => $state_id,
            'zone_country_id' => $ctry_id,
        ])->exists();
    }

}
