<?php

namespace common\validators;

class StringValidator extends \yii\validators\StringValidator {
    
    public $strict = false;
    
    public function validateAttribute($model, $attribute) {
        $converted = null;
        if (isset($model->$attribute) && (is_integer($model->$attribute) || is_float($model->$attribute)) && !$this->strict) {
            $converted = $model->$attribute;
            $model->$attribute .= '';
        }
        
        parent::validateAttribute($model, $attribute);
        
        if (isset($converted)) {
            $model->$attribute = $converted;
        }
    }
    
    public function validateValue($value) {
        if (isset($value) && is_integer($value) && !$this->strict) {
            $value .= '';
        }
        return parent::validateValue($value);
    }
}