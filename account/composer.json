{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "dev", "require": {"php": ">=5.6.0", "yiisoft/yii2": "********", "maknz/slack": "1.7.*", "aws/aws-sdk-php": "3.*", "aws/aws-php-sns-message-validator": "1.*", "fortawesome/font-awesome": "*", "giggsey/libphonenumber-for-php": "8.13.19", "dpodium/yii2-twilio": "*", "bower-asset/toastr": "*", "robthree/twofactorauth": "1.6.*", "endroid/qrcode": "1.9.*", "conquer/select2": "*", "yiisoft/yii2-jui": "~2.0.0", "dpodium/yii2-geoip": "~2.1.0", "dpodium/pipwave-php-sdk": "*", "captcha-com/captcha": "4.2.*", "sizeg/yii2-jwt": "^1.1", "ua-parser/uap-php": "*", "yiisoft/yii2-redis": "^2.0", "yiisoft/yii2-bootstrap": "~2.0.0", "ext-json": "*"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0", "yiisoft/yii2-gii": "~2.0.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/base": "^2.2.3", "codeception/verify": "~0.3.1"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}