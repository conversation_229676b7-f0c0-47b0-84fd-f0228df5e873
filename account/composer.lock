{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3d10717c8543fea27c9ca3b6998d277b", "packages": [{"name": "aws/aws-php-sns-message-validator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/aws/aws-php-sns-message-validator.git", "reference": "7b8eee4a56fe27d70395f1d87cc35a0651889d9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-php-sns-message-validator/zipball/7b8eee4a56fe27d70395f1d87cc35a0651889d9f", "reference": "7b8eee4a56fe27d70395f1d87cc35a0651889d9f", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=5.4", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/psr7": "^1.4", "phpunit/phpunit": "^4.0", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "autoload": {"psr-4": {"Aws\\Sns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "Amazon SNS message validation for PHP", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["SNS", "amazon", "aws", "cloud", "message", "sdk", "webhooks"], "time": "2017-09-27T23:05:21+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.54.4", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "6642a13df7ddcccf19e66c744c5bfae5b61e9e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/6642a13df7ddcccf19e66c744c5bfae5b61e9e85", "reference": "6642a13df7ddcccf19e66c744c5bfae5b61e9e85", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "guzzlehttp/guzzle": "^5.3.1|^6.2.1", "guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "^1.4.1", "mtdowling/jmespath.php": "~2.2", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "nette/neon": "^2.3", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2018-04-10T22:11:31+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.3.7", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "0b9c4a4007c44201dce9a6cc1a38407005c26c86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/0b9c4a4007c44201dce9a6cc1a38407005c26c86", "reference": "0b9c4a4007c44201dce9a6cc1a38407005c26c86"}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/inputmask", "version": "3.3.11", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/5e670ad62f50c738388d4dcec78d2888505ad77b", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "2.2.4", "source": {"type": "git", "url": "**************:jquery/jquery-dist.git", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/c0185ab7c75aab88762c5aae780b9d83b80eda72", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/jquery-ui", "version": "1.12.1", "source": {"type": "git", "url": "**************:components/jqueryui.git", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/jqueryui/zipball/44ecf3794cc56b65954cc19737234a3119d036cc", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "require": {"bower-asset/jquery": ">=1.6"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v1.3.2", "source": {"type": "git", "url": "**************:bestiejs/punycode.js.git", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bestiejs/punycode.js/zipball/38c8d3131a82567bfef18da09f7f4db68c84f8a3", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "type": "bower-asset"}, {"name": "bower-asset/select2", "version": "4.0.5", "source": {"type": "git", "url": "**************:ivaynberg/select2.git", "reference": "ebf10c93db7d6d7a0d1330119d4c6f32cbd231d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ivaynberg/select2/zipball/ebf10c93db7d6d7a0d1330119d4c6f32cbd231d7", "reference": "ebf10c93db7d6d7a0d1330119d4c6f32cbd231d7"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/toastr", "version": "2.1.3", "source": {"type": "git", "url": "**************:johnpapa/toastr-bower.git", "reference": "d6fef68471aa1836bc44ba1a41a90a96548b784f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpapa/toastr-bower/zipball/d6fef68471aa1836bc44ba1a41a90a96548b784f", "reference": "d6fef68471aa1836bc44ba1a41a90a96548b784f"}, "require": {"bower-asset/jquery": ">=1.6.3,<3"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/yii2-pjax", "version": "*******", "source": {"type": "git", "url": "**************:yiisoft/jquery-pjax.git", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/aef7b953107264f00234902a3880eb50dafc48be", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "captcha-com/captcha", "version": "4.2.5", "source": {"type": "git", "url": "https://git.captcha.com/botdetect-php-captcha-library.git", "reference": "4b8b642509bfc63d2f295dccf1c1b4d0e2266a15"}, "require": {"php": ">=5.3.0"}, "suggest": {"captcha-com/cakephp-captcha": "CakePHP Captcha Plugin -- BotDetect PHP CAPTCHA generator integration for the CakePHP framework.", "captcha-com/laravel-captcha": "Laravel Captcha Package -- BotDetect PHP CAPTCHA generator integration for the Laravel framework.", "captcha-com/symfony-captcha-bundle": "Symfony Captcha Bundle -- BotDetect PHP CAPTCHA generator integration for the Symfony framework."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["proprietary"], "authors": [{"name": "Captcha Support", "email": "<EMAIL>", "homepage": "https://captcha.com/php-captcha.html"}], "description": "Captcha Generator -- BotDetect PHP CAPTCHA Free composer package.", "homepage": "https://captcha.com/php-captcha.html", "keywords": ["<PERSON><PERSON>a", "captcha generator", "captcha library", "php captcha"], "time": "2019-07-22T16:36:53+00:00"}, {"name": "cebe/markdown", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "25b28bae8a6f185b5030673af77b32e1163d5c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/25b28bae8a6f185b5030673af77b32e1163d5c6e", "reference": "25b28bae8a6f185b5030673af77b32e1163d5c6e", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2017-07-16T21:13:23+00:00"}, {"name": "composer/ca-bundle", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d2c0a83b7533d6912e8d516756ebd34f893e9169"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d2c0a83b7533d6912e8d516756ebd34f893e9169", "reference": "d2c0a83b7533d6912e8d516756ebd34f893e9169", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2018-03-29T19:57:20+00:00"}, {"name": "conquer/helpers", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/borodulin/yii2-helpers.git", "reference": "0b6a1b5a6c18f5b1b4fba3cd279eaec2edf6654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/borodulin/yii2-helpers/zipball/0b6a1b5a6c18f5b1b4fba3cd279eaec2edf6654c", "reference": "0b6a1b5a6c18f5b1b4fba3cd279eaec2edf6654c", "shasum": ""}, "require": {"php": ">=5.4.0", "yiisoft/yii2": ">=2.0.6"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.12.x-dev"}}, "autoload": {"psr-4": {"conquer\\helpers\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yii2 helpers", "homepage": "https://github.com/borodulin/yii2-helpers", "keywords": ["extension", "helpers", "widget", "yii2"], "time": "2017-10-27T11:59:47+00:00"}, {"name": "conquer/select2", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/borodulin/yii2-select2.git", "reference": "410c1ae9d0de70798c15916a702198a2400ae1ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/borodulin/yii2-select2/zipball/410c1ae9d0de70798c15916a702198a2400ae1ed", "reference": "410c1ae9d0de70798c15916a702198a2400ae1ed", "shasum": ""}, "require": {"bower-asset/select2": ">=4.0.0", "conquer/helpers": ">=2.0.7", "php": ">=5.4.0", "yiisoft/yii2": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.3.x-dev", "dev-release": "1.4.4.x-dev"}}, "autoload": {"psr-4": {"conquer\\select2\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yii2 Select2 widget", "homepage": "https://github.com/borodulin/yii2-select2", "keywords": ["extension", "select2", "widget", "yii2"], "time": "2017-01-10T19:21:40+00:00"}, {"name": "dpodium/pipwave-php-sdk", "version": "1.0", "source": {"type": "git", "url": "https://github.com/dpodium/pipwave-php-sdk.git", "reference": "50f224d53f2341d98b01c2122d49228788d8c6e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dpodium/pipwave-php-sdk/zipball/50f224d53f2341d98b01c2122d49228788d8c6e2", "reference": "50f224d53f2341d98b01c2122d49228788d8c6e2", "shasum": ""}, "require": {"yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "autoload": {"psr-4": {"dpodium\\pipwave\\sdk\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.dpodium.com"}], "description": "Module for generating Pipwave SDK session", "keywords": ["dpodium", "pipwave", "sdk", "yii2"], "time": "2019-09-20T05:05:02+00:00"}, {"name": "dpodium/yii2-geoip", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/dpodium/yii2-geoip.git", "reference": "68d433fe799735fcf3a33be2c2f5596a798bda11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dpodium/yii2-geoip/zipball/68d433fe799735fcf3a33be2c2f5596a798bda11", "reference": "68d433fe799735fcf3a33be2c2f5596a798bda11", "shasum": ""}, "require": {"geoip2/geoip2": "~2.0", "yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "autoload": {"psr-4": {"dpodium\\yii2\\geoip\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sionghuat Chng", "email": "<EMAIL>", "homepage": "http://www.dpodium.com"}], "description": "Yii2 Component to allow for easy usage of the MaxMind Free dbs.", "keywords": ["extension", "geoip", "maxmind", "yii2"], "time": "2022-04-04T00:44:53+00:00"}, {"name": "dpodium/yii2-twilio", "version": "1.5", "source": {"type": "git", "url": "https://github.com/dpodium/yii2-twilio.git", "reference": "7b4bc75973ee82949e1b5dd968c31a1d71ace089"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dpodium/yii2-twilio/zipball/7b4bc75973ee82949e1b5dd968c31a1d71ace089", "reference": "7b4bc75973ee82949e1b5dd968c31a1d71ace089", "shasum": ""}, "require": {"twilio/sdk": "~5.0", "yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "autoload": {"psr-4": {"dpodium\\yii2\\Twilio\\": "components/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD"], "authors": [{"name": "Sionghuat Chng", "email": "<EMAIL>", "homepage": "http://www.dpodium.com"}], "description": "Yii2 twilio is a wrapper for the Twilio PHP library. Allow yii2 application to use twilio services.", "keywords": ["sms", "twi<PERSON>", "yii2"], "time": "2017-08-10T00:56:22+00:00"}, {"name": "endroid/qrcode", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "c9644bec2a9cc9318e98d1437de3c628dcd1ef93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/c9644bec2a9cc9318e98d1437de3c628dcd1ef93", "reference": "c9644bec2a9cc9318e98d1437de3c628dcd1ef93", "shasum": ""}, "require": {"ext-gd": "*", "php": ">=5.4", "symfony/options-resolver": "^2.3|^3.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0", "sensio/framework-extra-bundle": "^3.0", "symfony/browser-kit": "^2.3|^3.0", "symfony/framework-bundle": "^2.3|^3.0", "symfony/http-kernel": "^2.3|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://endroid.nl/"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/QrCode", "keywords": ["bundle", "code", "endroid", "qr", "qrcode", "symfony"], "abandoned": "endroid/qr-code", "time": "2017-04-08T09:13:59+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.10.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "d85d39da4576a6934b72480be6978fb10c860021"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/d85d39da4576a6934b72480be6978fb10c860021", "reference": "d85d39da4576a6934b72480be6978fb10c860021", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "^1.1"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2018-02-23T01:58:20+00:00"}, {"name": "fortawesome/font-awesome", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/a8386aae19e200ddb0f6845b5feeee5eb7013687", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687", "shasum": ""}, "require-dev": {"jekyll": "1.0.2", "lessc": "1.4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.6.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OFL-1.1", "MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://twitter.com/davegandy", "role": "Developer"}], "description": "The iconic font and CSS framework", "homepage": "http://fontawesome.io/", "keywords": ["FontAwesome", "awesome", "bootstrap", "font", "icon"], "time": "2016-10-24T15:52:54+00:00"}, {"name": "geoip2/geoip2", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/maxmind/GeoIP2-php.git", "reference": "a807fbf65212eef5d8d2db1a1b31082b53633d77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/GeoIP2-php/zipball/a807fbf65212eef5d8d2db1a1b31082b53633d77", "reference": "a807fbf65212eef5d8d2db1a1b31082b53633d77", "shasum": ""}, "require": {"maxmind-db/reader": "~1.0", "maxmind/web-service-common": "~0.5", "php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2018-04-10T15:32:59+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.19", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "7b60d1264ba806e68fb99b06e73e2ed07815689e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/7b60d1264ba806e68fb99b06e73e2ed07815689e", "reference": "7b60d1264ba806e68fb99b06e73e2ed07815689e", "shasum": ""}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2023-08-22T13:59:44+00:00"}, {"name": "giggsey/locale", "version": "1.9", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "b07f1eace8072ccc61445ad8fbd493ff9d783043"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/b07f1eace8072ccc61445ad8fbd493ff9d783043", "reference": "b07f1eace8072ccc61445ad8fbd493ff9d783043", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "~2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "phpunit/phpunit": "^4.8|^5.0", "symfony/console": "^2.8|^3.0|^4.0", "symfony/filesystem": "^2.8|^3.0|^4.0", "symfony/finder": "^2.8|^3.0|^4.0", "symfony/process": "^2.8|^3.0|^4.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "time": "2020-07-07T11:16:24+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "68d0ea14d5a3f42a20e87632a5f84931e2709c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/68d0ea14d5a3f42a20e87632a5f84931e2709c90", "reference": "68d0ea14d5a3f42a20e87632a5f84931e2709c90", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-03-26T16:33:04+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"], "time": "2017-03-20T17:10:46+00:00"}, {"name": "lcobucci/jwt", "version": "3.2.5", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "82be04b4753f8b7693b62852b7eab30f97524f9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/82be04b4753f8b7693b62852b7eab30f97524f9b", "reference": "82be04b4753f8b7693b62852b7eab30f97524f9b", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=5.5"}, "require-dev": {"mdanter/ecc": "~0.3.1", "mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "~4.5", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"mdanter/ecc": "Required to use Elliptic Curves based algorithms."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "time": "2018-11-11T12:22:26+00:00"}, {"name": "maknz/slack", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/maknz/slack.git", "reference": "7f21fefc70c76b304adc1b3a780c8740dfcfb595"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maknz/slack/zipball/7f21fefc70c76b304adc1b3a780c8740dfcfb595", "reference": "7f21fefc70c76b304adc1b3a780c8740dfcfb595", "shasum": ""}, "require": {"ext-mbstring": "*", "guzzlehttp/guzzle": "~6.0|~5.0|~4.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "4.2.*"}, "suggest": {"illuminate/support": "Required for Laravel support"}, "type": "library", "autoload": {"psr-4": {"Maknz\\Slack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "maknz", "email": "<EMAIL>"}], "description": "A simple PHP package for sending messages to Slack, with a focus on ease of use and elegant syntax. Includes Laravel support out of the box.", "keywords": ["laravel", "slack"], "time": "2015-06-03T03:35:16+00:00"}, {"name": "maxmind-db/reader", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "e042b4f8a2dff41e19019faf16427178b07fbd58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/e042b4f8a2dff41e19019faf16427178b07fbd58", "reference": "e042b4f8a2dff41e19019faf16427178b07fbd58", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "4.* || 5.*", "satooshi/php-coveralls": "1.0.*", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2018-02-21T21:23:33+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.5.0", "source": {"type": "git", "url": "https://github.com/maxmind/web-service-common-php.git", "reference": "61a9836fa3bb1743ab89752bae5005d71e78c73b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/web-service-common-php/zipball/61a9836fa3bb1743ab89752bae5005d71e78c73b", "reference": "61a9836fa3bb1743ab89752bae5005d71e78c73b", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "time": "2018-02-12T22:31:54+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2016-12-03T22:08:25+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "rob<PERSON><PERSON>/twofactor<PERSON>h", "version": "1.6.5", "source": {"type": "git", "url": "https://github.com/RobThree/TwoFactorAuth.git", "reference": "f5f58a4c62d0336a0e6175856894a51f3565dad2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobThree/TwoFactorAuth/zipball/f5f58a4c62d0336a0e6175856894a51f3565dad2", "reference": "f5f58a4c62d0336a0e6175856894a51f3565dad2", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "@stable"}, "type": "library", "autoload": {"psr-4": {"RobThree\\Auth\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://robiii.me", "role": "Developer"}], "description": "Two Factor Authentication", "homepage": "https://github.com/RobThree/TwoFactorAuth", "keywords": ["Authentication", "MFA", "Multi Factor Authentication", "Two Factor Authentication", "authenticator", "authy", "php", "tfa"], "time": "2018-06-09T10:09:59+00:00"}, {"name": "sizeg/yii2-jwt", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/sizeg/yii2-jwt.git", "reference": "22100b1313a8ee281525fa63d91d5ef12c06d959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sizeg/yii2-jwt/zipball/22100b1313a8ee281525fa63d91d5ef12c06d959", "reference": "22100b1313a8ee281525fa63d91d5ef12c06d959", "shasum": ""}, "require": {"lcobucci/jwt": "~3.2.0", "php": ">=5.5.0", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "type": "yii2-extension", "autoload": {"psr-4": {"sizeg\\jwt\\": ""}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sizeg.tk"}], "description": "JWT based on Icobucci", "keywords": ["jwt", "yii 2", "yii2"], "time": "2019-03-20T06:36:38+00:00"}, {"name": "symfony/options-resolver", "version": "v3.4.11", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "f3109a6aedd20e35c3a33190e932c2b063b7b50e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/f3109a6aedd20e35c3a33190e932c2b063b7b50e", "reference": "f3109a6aedd20e35c3a33190e932c2b063b7b50e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2018-01-11T07:56:07+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "b5f7b932ee6fa802fc792eabd77c4c88084517ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/b5f7b932ee6fa802fc792eabd77c4c88084517ce", "reference": "b5f7b932ee6fa802fc792eabd77c4c88084517ce", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "twilio/sdk", "version": "5.17.1", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "ae3477ccf88a0efb5bbe8928274c4a2046aec0c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/ae3477ccf88a0efb5bbe8928274c4a2046aec0c1", "reference": "ae3477ccf88a0efb5bbe8928274c4a2046aec0c1", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"apigen/apigen": "^4.1", "phpunit/phpunit": "4.5.*"}, "type": "library", "autoload": {"psr-4": {"Twilio\\": "<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "http://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"], "time": "2018-04-20T23:40:08+00:00"}, {"name": "ua-parser/uap-php", "version": "v3.9.7", "source": {"type": "git", "url": "https://github.com/ua-parser/uap-php.git", "reference": "7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ua-parser/uap-php/zipball/7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a", "reference": "7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a", "shasum": ""}, "require": {"composer/ca-bundle": "^1.1", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "<8", "symfony/console": "^2.0 || ^3.0 || ^4.0", "symfony/filesystem": "^2.0 || ^3.0 || ^4.0", "symfony/finder": "^2.0 || ^3.0 || ^4.0", "symfony/yaml": "^2.0 || ^3.0 || ^4.0"}, "suggest": {"symfony/console": "Required for CLI usage - ^2.0 || ^3.0 || ^4.0", "symfony/filesystem": "Required for CLI usage - 2.0 || ^3.0 || ^4.0", "symfony/finder": "Required for CLI usage - ^2.0 || ^3.0 || ^4.0", "symfony/yaml": "Required for CLI usage - ^4.0 || ^5.0"}, "bin": ["bin/uaparser"], "type": "library", "autoload": {"psr-4": {"UAParser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A multi-language port of Browserscope's user agent parser.", "time": "2020-02-21T09:54:14+00:00"}, {"name": "yiisoft/yii2", "version": "********", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "ef74f3783e964cea477f06e6d6ce209df1f37881"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/ef74f3783e964cea477f06e6d6ce209df1f37881", "reference": "ef74f3783e964cea477f06e6d6ce209df1f37881", "shasum": ""}, "require": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "http://www.yiiframework.com/", "keywords": ["framework", "yii2"], "time": "2018-03-13T14:15:01+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/83d144f4089adaa7064ad60dc4c1436daa2eb30e", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.4.* | 3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": "~2.0.6"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\bootstrap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap", "type": "tidelift"}], "time": "2021-08-09T20:54:06+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-jui", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-jui.git", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-jui/zipball/ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "shasum": ""}, "require": {"bower-asset/jquery-ui": "~1.12.1", "yiisoft/yii2": "~2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\jui\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Jquery UI extension for the Yii framework", "keywords": ["jQuery <PERSON>", "yii2"], "time": "2017-11-25T15:32:29+00:00"}, {"name": "yiisoft/yii2-redis", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-redis.git", "reference": "5dc55d5187923219e9db86d149d56acf1f5a6ee8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-redis/zipball/5dc55d5187923219e9db86d149d56acf1f5a6ee8", "reference": "5dc55d5187923219e9db86d149d56acf1f5a6ee8", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.11"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\redis\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON>is Cache, Session and ActiveRecord for the Yii framework", "keywords": ["active-record", "cache", "redis", "session", "yii2"], "time": "2017-04-05T13:42:11+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.5.1", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "74ac03d52c5e23ad8abd5c5cce4ab0e8dc1b530a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/74ac03d52c5e23ad8abd5c5cce4ab0e8dc1b530a", "reference": "74ac03d52c5e23ad8abd5c5cce4ab0e8dc1b530a", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "~4.5|~5", "symfony/phpunit-bridge": "~2.7|~3", "symfony/yaml": "~2.3|~3"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "time": "2017-08-30T11:04:43+00:00"}, {"name": "bower-asset/typeahead.js", "version": "v0.11.1", "source": {"type": "git", "url": "**************:twitter/typeahead.js.git", "reference": "588440f66559714280628a4f9799f0c4eb880a4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twitter/typeahead.js/zipball/588440f66559714280628a4f9799f0c4eb880a4a", "reference": "588440f66559714280628a4f9799f0c4eb880a4a"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset"}, {"name": "codeception/base", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/Codeception/base.git", "reference": "fb07b713f2a97cdf0c5c81c93fa26a0785eb44a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/base/zipball/fb07b713f2a97cdf0c5c81c93fa26a0785eb44a6", "reference": "fb07b713f2a97cdf0c5c81c93fa26a0785eb44a6", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/phpunit-wrapper": "^6.0.9|^7.0.6", "codeception/stub": "^1.0", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "~1.0", "php": ">=5.4.0 <8.0", "symfony/browser-kit": ">=2.7 <5.0", "symfony/console": ">=2.7 <5.0", "symfony/css-selector": ">=2.7 <5.0", "symfony/dom-crawler": ">=2.7 <5.0", "symfony/event-dispatcher": ">=2.7 <5.0", "symfony/finder": ">=2.7 <5.0", "symfony/yaml": ">=2.7 <5.0"}, "require-dev": {"codeception/specify": "~0.3", "facebook/graph-sdk": "~5.3", "flow/jsonpath": "~0.2", "monolog/monolog": "~1.8", "pda/pheanstalk": "~3.0", "php-amqplib/php-amqplib": "~2.4", "predis/predis": "^1.0", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <5.0", "vlucas/phpdotenv": "^2.4.0"}, "suggest": {"aws/aws-sdk-php": "For using AWS Auth in REST module and Queue module", "codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests", "codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "flow/jsonpath": "For using JSONPath in REST module", "league/factory-muffin": "For DataFactory module", "league/factory-muffin-faker": "For Faker support in DataFactory module", "phpseclib/phpseclib": "for SFTP option in FTP Module", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Codeception\\": "src\\Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "http://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "abandoned": true, "time": "2018-03-31T22:36:34+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "6.0.9", "source": {"type": "git", "url": "https://github.com/Codeception/phpunit-wrapper.git", "reference": "450f1cfc5f49539c421061e64338f5edb8baad6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/phpunit-wrapper/zipball/450f1cfc5f49539c421061e64338f5edb8baad6a", "reference": "450f1cfc5f49539c421061e64338f5edb8baad6a", "shasum": ""}, "require": {"phpunit/php-code-coverage": ">=2.2.4 <6.0", "phpunit/phpunit": ">=4.8.28 <5.0.0 || >=5.6.3 <7.0", "sebastian/comparator": ">1.1 <3.0", "sebastian/diff": ">=1.4 <4.0"}, "replace": {"codeception/phpunit-wrapper": "*"}, "require-dev": {"codeception/specify": "*", "vlucas/phpdotenv": "^2.4"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src\\"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHPUnit classes used by Codeception", "time": "2018-03-31T18:50:01+00:00"}, {"name": "codeception/stub", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "95fb7a36b81890dd2e5163e7ab31310df6f1bb99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/95fb7a36b81890dd2e5163e7ab31310df6f1bb99", "reference": "95fb7a36b81890dd2e5163e7ab31310df6f1bb99", "shasum": ""}, "require": {"phpunit/phpunit-mock-objects": ">2.3 <7.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <8.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "time": "2018-02-18T13:56:56+00:00"}, {"name": "codeception/verify", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "shasum": ""}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"files": ["src/Codeception/function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD assertion library for PHPUnit", "time": "2017-01-09T10:58:51+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14T21:17:01+00:00"}, {"name": "fzaninotto/faker", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "squizlabs/php_codesniffer": "^1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "time": "2017-08-15T16:48:10+00:00"}, {"name": "myclabs/deep-copy", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2017-10-19T19:58:43+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bf329f6c1aadea3299f08ee804682b7c45b326a2", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-11-10T14:09:06+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-07-14T14:27:02+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/0464787bfa7cd13576c5a1e318709768798bec6a", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "time": "2016-04-07T12:29:16+00:00"}, {"name": "phpspec/prophecy", "version": "1.7.5", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "dfd6be44111a7c41c2e884a336cc4f461b3b2401"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/dfd6be44111a7c41c2e884a336cc4f461b3b2401", "reference": "dfd6be44111a7c41c2e884a336cc4f461b3b2401", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2018-02-19T10:16:54+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/1ce90ba27c42e4e44e6d8458241466380b51fa16", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2017-12-04T08:55:13+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2018-02-01T05:50:59+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2017-06-30T09:13:00+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "840bb6f0d5b3701fd768b68adf7193c2d0f98f79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/840bb6f0d5b3701fd768b68adf7193c2d0f98f79", "reference": "840bb6f0d5b3701fd768b68adf7193c2d0f98f79", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/dom-crawler": "~2.8|~3.0|~4.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2018-03-19T22:32:39+00:00"}, {"name": "symfony/console", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf", "reference": "d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2018-04-03T05:22:50+00:00"}, {"name": "symfony/css-selector", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "519a80d7c1d95c6cc0b67f686d15fe27c6910de0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/519a80d7c1d95c6cc0b67f686d15fe27c6910de0", "reference": "519a80d7c1d95c6cc0b67f686d15fe27c6910de0", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2018-03-19T22:32:39+00:00"}, {"name": "symfony/debug", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "9cf7c2271cfb89ef9727db1b740ca77be57bf9d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/9cf7c2271cfb89ef9727db1b740ca77be57bf9d7", "reference": "9cf7c2271cfb89ef9727db1b740ca77be57bf9d7", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "abandoned": "symfony/error-handler", "time": "2018-04-03T05:22:50+00:00"}, {"name": "symfony/dom-crawler", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "1a4cffeb059226ff6bee9f48acb388faf674afff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/1a4cffeb059226ff6bee9f48acb388faf674afff", "reference": "1a4cffeb059226ff6bee9f48acb388faf674afff", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2018-03-19T22:32:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "fdd5abcebd1061ec647089c6c41a07ed60af09f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fdd5abcebd1061ec647089c6c41a07ed60af09f8", "reference": "fdd5abcebd1061ec647089c6c41a07ed60af09f8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2018-04-06T07:35:25+00:00"}, {"name": "symfony/finder", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "bd14efe8b1fabc4de82bf50dce62f05f9a102433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/bd14efe8b1fabc4de82bf50dce62f05f9a102433", "reference": "bd14efe8b1fabc4de82bf50dce62f05f9a102433", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2018-04-04T05:07:11+00:00"}, {"name": "symfony/yaml", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "a42f9da85c7c38d59f5e53f076fe81a091f894d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a42f9da85c7c38d59f5e53f076fe81a091f894d0", "reference": "a42f9da85c7c38d59f5e53f076fe81a091f894d0", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2018-04-03T05:14:20+00:00"}, {"name": "webmozart/assert", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "0df1908962e7a3071564e857d86874dad1ef204a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/0df1908962e7a3071564e857d86874dad1ef204a", "reference": "0df1908962e7a3071564e857d86874dad1ef204a", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2018-01-29T19:49:41+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.0.12", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "93082f46d3568b4431a26f264e0d16a12c42bd50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/93082f46d3568b4431a26f264e0d16a12c42bd50", "reference": "93082f46d3568b4431a26f264e0d16a12c42bd50", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.11", "yiisoft/yii2-bootstrap": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\debug\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2017-10-09T20:30:01+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "3df62b1dcb272a8413f9c6e532c9d73f325ccde1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/3df62b1dcb272a8413f9c6e532c9d73f325ccde1", "reference": "3df62b1dcb272a8413f9c6e532c9d73f325ccde1", "shasum": ""}, "require": {"fzaninotto/faker": "~1.4", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\faker\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "time": "2018-02-19T20:27:10+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/1bd6df6804ca077ec022587905a0d43eb286f507", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507", "shasum": ""}, "require": {"bower-asset/typeahead.js": "0.10.* | ~0.11.0", "phpspec/php-diff": ">=1.0.2", "yiisoft/yii2": ">=2.0.4", "yiisoft/yii2-bootstrap": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"yii\\gii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "gii", "yii2"], "time": "2016-03-18T14:09:46+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.6.0", "ext-json": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}