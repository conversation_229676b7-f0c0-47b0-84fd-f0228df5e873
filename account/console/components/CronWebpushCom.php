<?php

namespace console\components;

use api\models\ApiWebpushSchedule;
use api\models\ApiWebpushNotification;
use api\models\ApiWebpushToken;
use api\models\ApiWebpushTopic;
use api\models\ApiWebpushClient;
use api\models\ApiWebpushCronTopicRefresh;
use api\models\ApiWebpushUser;
use api\components\webpush\ApiWebpushNotificationCom;
use api\components\webpush\ApiWebpushTopicCom;
use common\components\CurlComponent;
use Yii;

class CronWebpushCom extends ApiWebpushNotificationCom
{
    public function __construct($merchantId)
    {
        parent::__construct($merchantId);
    }
    
    public function processWebpush()
    {
        $pushIdArray = [];
        // Check if schedule exist
        $schedule = ApiWebpushSchedule::find()
            ->joinWith('apiWebpushNotification', false)
            ->where(['<=', 'schedule.schedule_at', time()])
            ->andWhere(['schedule.push_status' => 0])
            ->andWhere(['notification.notification_status' => 1])
            ->limit(Yii::$app->params['FIREBASE']['CRON_LIMIT'])
            ->all();
        
        foreach ($schedule as $scheduleInfo) {
            $this->updateSchedule($scheduleInfo->push_id, ['push_status' => 1, 'executed_at' => time()]);
            $pushIdArray[] = $scheduleInfo->push_id;
        }

        foreach ($pushIdArray as $pushId) {
            // Get push info
            $notification = ApiWebpushNotification::findOne($pushId);
            // Send notification
            if ($this->sendNotification($notification->id)) {
                // Delete schedule
                $this->deleteSchedule($notification->id);
                $notification->send_status = 1;
            } else {
                $notification->send_status = 2;
            }
            $notification->executed_at = time();
            $notification->update();
        }
    }

    public function cleanupWebpush()
    {
        $pushTokenArray = [];
        // Get all token without user_id & < 6 months
        $date = strtotime(date('Y-m-d').' -6 months');
        $tokens = ApiWebpushToken::find()
            ->where(['IS', 'user_id', NULL])
            ->andWhere(['<=', 'created_at', $date])
            ->orderBy(['created_at' => SORT_ASC])
            ->limit(Yii::$app->params['FIREBASE']['CRON_LIMIT'])
            ->all();

        foreach ($tokens as $tokenInfo) {
            $this->unsetTokenTopic($this->defaultTopic, $tokenInfo->id, $tokenInfo->client_id);
            $tokenInfo->delete();
        }

        unset($tokens);

        // validate expired
        $tokens = ApiWebpushToken::find()
            ->where(['<=', 'created_at', $date])
            ->orderBy(['created_at' => SORT_ASC])
            ->limit(Yii::$app->params['FIREBASE']['CRON_LIMIT'])
            ->all();

        foreach ($tokens as $tokenInfo) {
            if (!$this->validateWebpushToken($tokenInfo->id, $tokenInfo->client_id)) {
                $this->unsetTokenTopic($this->defaultTopic, $tokenInfo->id, $tokenInfo->client_id);
                $tokenInfo->delete();
            }
        }
    }

    public function refreshTopic()
    {
        // Check for cron list
        if ($cronTopic = ApiWebpushCronTopicRefresh::find()->where(['cron_process_track_in_action' => 0])->orderBy(['created_at' => SORT_ASC])->one()) {
            // set cron track in action
            $cronTopic->cron_process_track_in_action = 1;
            $cronTopic->update();

            // Check for end of records
            $userLastId = ApiWebpushUser::find()->orderBy(['id' => SORT_DESC])->one();
            if (($cronTopic->new_start_id - 1) != $userLastId->id) {
                $objTopic = new ApiWebpushTopicCom($cronTopic->client_id);
                $topicArray = $objTopic->updateTopic([
                    'id' => $cronTopic->topic_id,
                    'start_id' => $cronTopic->new_start_id,
                    'refresh_status' => 1,
                    'merchant' => $cronTopic->client_id,
                    'created_by' => 'System',
                    'cronjob' => 1
                ]);
            } else {
                // delete cron task
                $cronTopic->delete();
                // Update refresh status
                $topic = ApiWebpushTopic::findOne($cronTopic->topic_id);
                $topic->refresh = 0;
                $topic->update();
            }

            // Update cront track to finished
            $cronTopic->cron_process_track_in_action = 0;
            $cronTopic->cron_last_process_date = time();
            $cronTopic->update();
        }
    }
}
