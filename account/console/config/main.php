<?php
$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-encoded.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-encoded.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'console\controllers',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'controllerMap' => [
        'fixture' => [
            'class' => 'yii\console\controllers\FixtureController',
            'namespace' => 'common\fixtures',
        ],
        'migrate' => [
            //Offgamers db
            'class' => 'console\controllers\MigrateController',
            'migrationTable' => 'myacc_version_upgrade',
        ],
        'migrate_s' => [
            //Shasso db
            'class' => 'console\controllers\MigrateController',
            'migrationTable' => 'version_upgrade',
            'db' => 'dbshasso',
            'migrationPath' => '@app/migrations/shasso',
        ],
        'migrate_w' => [
            //Webpush db
            'class' => 'console\controllers\MigrateController',
            'db' => 'dbwebpush',
            'migrationPath' => '@app/migrations/webpush',
        ],
    ],
    'components' => [
        'log' => [
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'errorHandler' => [
            'class' => 'common\components\ConsoleErrorHandler',
        ],
    ],
    'params' => $params,
];
