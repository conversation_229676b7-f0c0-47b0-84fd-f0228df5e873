<?php
namespace console\controllers;

use Yii;
use yii\helpers\Url;
use yii\console\Controller;
use api\models\ApiWebpushClient;
use api\models\ApiWebpushCronProcessTrack;
use common\models\CustomersMobileChangeApproval;
use common\models\CustomersRemarksHistory;
use console\components\CronWebpushCom;
use common\models\EinvoiceProfileUpdateQueue;

/**
 * Test controller
 */
class CronController extends Controller
{
    private function cronWebpushProcessCheck($filename)
    {
        $cronProcess = ApiWebpushCronProcessTrack::find()
            ->select(['id', 'cron_process_track_in_action', 'cron_process_track_start_date', 'cron_process_track_failed_attempt',
                'cron_process_track_filename', 'FROM_UNIXTIME(cron_process_track_start_date) < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process'])
            ->where('cron_process_track_filename=:cron_process_track_filename', [':cron_process_track_filename' => $filename])
            ->one();
        return $cronProcess;
    }

    private function cronWebpushFailedAttempt($processId)
    {
        $trackObj = ApiWebpushCronProcessTrack::findOne($processId);
        $trackObj->cron_process_track_failed_attempt = $trackObj->cron_process_track_failed_attempt + 1;
        $trackObj->cron_process_track_start_date = time();
        $trackObj->save();

        if ($trackObj->cron_process_track_failed_attempt > 3) {
            $trackObj->cron_process_track_in_action = 0;
            $trackObj->cron_process_track_failed_attempt = 0;
            $trackObj->save();
            unset($trackObj);
        }
    }
    
    // Cron Webpush
    public function actionWebpushSchedule()
    {
        $error = false;

        $cronProcess = $this->cronWebpushProcessCheck('WebpushSchedule');

        if ($cronProcess->cron_process_track_in_action == '0') {
            // Start cron process by locking the cron_process_track
            if (ApiWebpushCronProcessTrack::updateAll(['cron_process_track_in_action' => 1, 'cron_process_track_start_date' => time(), 'cron_process_track_failed_attempt' => 0], ['id' => $cronProcess->id, 'cron_process_track_in_action' => 0])) {
                $clients = ApiWebpushClient::find()->all();

                foreach ($clients as $client) {
                    $cronWebpush = new CronWebpushCom($client->id);
                    $cronWebpush->processWebpush();
                    $cronWebpush->cleanupWebpush();
                    unset($cronWebpush);
                }

                // End cron process by un-locking the cron_process_track
                $trackObj = ApiWebpushCronProcessTrack::findOne($cronProcess->id);
                $trackObj->cron_process_track_in_action = 0;
                $trackObj->save();
                unset($trackObj);
            } else {
                $error = true;
            }
        } else {
            $error = true;
        }

        if ($error) {
            if ($cronProcess->overdue_process == '1') {
                $this->cronWebpushFailedAttempt($cronProcess->id);
            }
        }
    }

    public function actionRefreshTopic()
    {
        $error = false;
        
        $cronProcess = $this->cronWebpushProcessCheck('RefreshTopic');

        if ($cronProcess->cron_process_track_in_action == '0') {
            // Start cron process by locking the cron_process_track
            if (ApiWebpushCronProcessTrack::updateAll(['cron_process_track_in_action' => 1, 'cron_process_track_start_date' => time(), 'cron_process_track_failed_attempt' => 0], ['id' => $cronProcess->id, 'cron_process_track_in_action' => 0])) {
                $clients = ApiWebpushClient::find()->all();

                foreach ($clients as $client) {
                    $cronWebpush = new CronWebpushCom($client->id);
                    $cronWebpush->refreshTopic();
                    unset($cronWebpush);
                }

                // End cron process by un-locking the cron_process_track
                $trackObj = ApiWebpushCronProcessTrack::findOne($cronProcess->id);
                $trackObj->cron_process_track_in_action = 0;
                $trackObj->save();
                unset($trackObj);
            } else {
                $error = true;
            }
        } else {
            $error = true;
        }

        if ($error) {
            if ($cronProcess->overdue_process == '1') {
                $this->cronWebpushFailedAttempt($cronProcess->id);
            }
        }
    }

    /**
     * actionDenyMobileChange
     * Deny a mobioe change request after 5 days
     * @return void
     */
    public function actionDenyMobileChange(){

        $deny_list = CustomersMobileChangeApproval::find()->where(['approval_status'=>1])->andWhere('updated_datetime < CURDATE() - INTERVAL 5 DAY')->asArray()->all();

        if(!empty($deny_list)){
            foreach($deny_list as $denied_approval){
                //1. Add remark to the customer's remark history
                $customer_remarks = new CustomersRemarksHistory();
                $customer_remarks->customers_id = $denied_approval['customers_id'];
                $customer_remarks->date_remarks_added = new \yii\db\Expression('NOW()');
                $customer_remarks->remarks = "Customer Mobile Change Rejected after 5 days by system";
                $customer_remarks->remarks_added_by = "system";
                if(!$customer_remarks->save()){
                    Yii::$app->reporter->reportToAdminViaSlack("Auto Deny Mobile Add Remarks Failed", $customer_remarks);
                    // on fail skip this row and continue to next to be rerun on next cron cycle
                    continue;
                }

                //2. Update the approval table's status and approver details
                $denied_approval['approval_status'] = CustomersMobileChangeApproval::APPROVAL_REJECTED;
                $denied_approval['updated_datetime'] = new \yii\db\Expression('NOW()');;
                $denied_approval['approval_status_datetime'] = new \yii\db\Expression('NOW()');
                $denied_approval['approved_status_updated_by'] = 'system';
                if(!CustomersMobileChangeApproval::updateAll($denied_approval,['customers_id'=>$denied_approval['customers_id']])){
                    Yii::$app->reporter->reportToAdminViaSlack("Auto Deny Mobile Change Save Failed", $denied_approval);
                }

            }

        }
    }

    public function actionClearEinvoiceProfileUpdateQueue(){
        $queueItems = EinvoiceProfileUpdateQueue::find()
            ->where(['is_sqs_sent' => false])
            ->orderBy(['created_at' => SORT_ASC])
            ->limit(100)
            ->all();

        if (empty($queueItems)) {
            echo "No items in einvoice profile update queue to process.\n";
            return;
        }

        foreach ($queueItems as $item) {
            try {
                $sqs = Yii::$app->aws->getSQS('EINVOICE_PROFILE_UPDATE_QUEUE');
                $sqs->pushMessage($item->user_info);

                $item->is_sqs_sent = true;
                $item->save(false, ['is_sqs_sent', 'updated_at']);

            } catch (\Exception $e) {
                Yii::error("Failed to send message to SQS for queue item ID: {$item->id}. Error: " . $e->getMessage(), 'cron_einvoice_queue');
                continue;
            }
        }
    }
}
