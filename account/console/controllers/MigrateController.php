<?php

namespace console\controllers;

class MigrateController extends \yii\console\controllers\MigrateController {
    
    public function migrateUp($class) {
        if (parent::migrateUp($class)) {
            $this->db->getSchema()->refresh();
            return true;
        }
        return false;
    }
    
    public function migrateDown($class) {
        if (parent::migrateDown($class)) {
            $this->db->getSchema()->refresh();
            return true;
        }
        return false;
    }
}
