<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m131129_031807_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            $_sql = "UPDATE myacc_link SET display_status = 0, last_modified = NOW() WHERE menu_id = 19";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ SUCCESS ] 'Favorite Server' menu option deactivated \n\n";
            } else {
                echo "\n[ FAIL ] query fail \n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        $c = $this->getDb();
        try {
            $_sql = "UPDATE myacc_link SET display_status = 1, last_modified = NOW() WHERE menu_id = 19";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ SUCCESS ] 'Favorite Server' menu option activated \n\n";
            } else {
                echo "\n[ FAIL ] query fail \n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

