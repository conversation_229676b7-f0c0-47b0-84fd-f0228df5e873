<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m131204_103556_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            /* -- add batch_id column for sso_token -- */
            $_sql = "ALTER TABLE sso_token ADD batch_id VARCHAR( 32 ) NULL DEFAULT NULL AFTER customers_id ;";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ SUCCESS ] column 'batch_id' added to the table 'sso_token'\n\n";
            } else {
                echo "\n[ FAIL ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        $c = $this->getDb();
        try {
            /* -- drop batch_id column for sso_token -- */
            $_sql = "ALTER TABLE sso_token DROP batch_id";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ SUCCESS ] column 'batch_id' droped from the table 'sso_token'\n\n";
            } else {
                echo "\n[ FAIL ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

