<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140128_063712_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            /* -- update display_status column for verify phone number menu -- */
            $_sql = "UPDATE myacc_link SET display_status = 0 WHERE controller_name = 'verifyPhone/index';";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ UPDATE ] column 'display_status' updated\n\n";
            } else {
                echo "\n[ UPDATE- ERROR ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version upgrade\n";
    }

    public function down() {
        $c = $this->getDb();
        try {
            $_sql = "UPDATE myacc_link SET display_status = 1 WHERE controller_name = 'verifyPhone/index';";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ UPDATE ] column 'display_status' updated\n\n";
            } else {
                echo "\n[ UPDATE- ERROR ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version downgrade\n";
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}