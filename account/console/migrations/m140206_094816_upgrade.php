<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140206_094816_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            /* -- create index for customers_id and batch_id column -- */
            $_sql = "ALTER TABLE sso_token ADD INDEX  idx_customers_batch (customers_id, batch_id);";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ UPDATE ] INDEX added for columns 'customers_id' and 'batch_id' for sso_token\n\n";
            } else {
                echo "\n[ UPDATE- ERROR ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version upgrade\n";
    }

    public function down() {
        $c = $this->getDb();
        try {
            $_sql = "ALTER TABLE sso_token DROP INDEX idx_customers_batch;";
            if ($c->createCommand($_sql)->execute()) {
                echo "\n[ UPDATE ] droped INDEX for columns 'customers_id' and 'batch_id' from sso_token table\n\n";
            } else {
                echo "\n[ UPDATE- ERROR ] query fail\n\n";
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version downgrade\n";
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

