<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140502_072528_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            /* -- alter table configuration -- */
            $_sql = "UPDATE  `configuration` SET
                    `set_function` =  'tep_cfg_select_option(array(''maxmind'', ''telesign'',''neutrino''),'
                    WHERE `configuration_key` ='TELEPHONE_VERIFICATION_SERVICES';";
            $c->createCommand($_sql)->execute();
            echo "\n[ UPDATE ] alter table 'configuration' added neutrino option \n\n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version upgrade\n";
    }

    public function down() {
        $c = $this->getDb();
        try {
            $_sql = "UPDATE  `configuration` SET
                    `set_function` =  'tep_cfg_select_option(array(''maxmind'', ''telesign''),'
                    WHERE `configuration_key` ='TELEPHONE_VERIFICATION_SERVICES';";
            $c->createCommand($_sql)->execute();
            echo "\n[ UPDATE ] retore table 'configuration' restored back to maxmind \n\n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version downgrade\n";
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

