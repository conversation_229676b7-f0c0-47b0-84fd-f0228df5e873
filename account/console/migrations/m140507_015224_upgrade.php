<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140507_015224_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            $_sql = "CREATE TABLE IF NOT EXISTS sns_connection (
                      customers_id int(11) unsigned NOT NULL,
                      provider varchar(20) NOT NULL,
                      provider_uid varchar(32) NOT NULL,
                      PRIMARY KEY (customers_id,provider)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
            $c->createCommand($_sql)->execute();
            echo "\n[ UPDATE ] created table 'sns_connection'\n\n";

            $query = new yii\db\Query();
            $row = $query->select('max(sort_order) as max_sort_order')->from('myacc_link')->one();
            $_col = array(
                'parent_id' => '0',
                'sort_order' => $row['max_sort_order'] + 1,
                'controller_name' => 'social/connect',
                'date_added' => new \yii\db\Expression('NOW()'),
                'last_modified' => new \yii\db\Expression('NOW()'),
                'display_status' => 1,
                'menu_group' => 1,
            );
            $c->createCommand()->insert('myacc_link', $_col);
            echo "\n[ UPDATE ] inserted new menu 'social/connect' successfully \n\n";

            $last_id = $c->getLastInsertID();
            $_col = array(
                'menu_bar_id' => $last_id,
                'language_id' => '1',
                'menu_name' => 'Social Networks Setting'
            );
            $c->createCommand()->insert('myacc_link_description', $_col);
            echo "\n[ UPDATE ] inserted description 'english' for new menu\n\n";

            // inserted description for language 'zh-CN'
            $_col = array(
                'menu_bar_id' => $last_id,
                'language_id' => '2',
                'menu_name' => '社交网络设置'
            );
            $c->createCommand()->insert('myacc_link_description', $_col);

            // inserted description for language 'zh-TW'
            $_col = array(
                'menu_bar_id' => $last_id,
                'language_id' => '3',
                'menu_name' => '社交網絡設置'
            );
            $c->createCommand()->insert('myacc_link_description', $_col);

            // inserted description for language 'id'
            $_col = array(
                'menu_bar_id' => $last_id,
                'language_id' => '4',
                'menu_name' => 'Social Networks Setting'
            );
            $c->createCommand()->insert('myacc_link_description', $_col);

            $_col = array(
                'display_status' => '0'
            );
            $c->createCommand()->update('myacc_link', $_col, 'controller_name="facebook/index"');
            echo "\n[ UPDATE ] hidden menu 'facebook/index' successfully \n\n";

        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version upgrade\n";
    }

    public function down() {
        $c = $this->getDb();
        try {
            $_sql = "Drop TABLE sns_connection";
            $c->createCommand($_sql)->execute();
            echo "\n[ UPDATE ] droped table 'sns_connection'\n\n";

            $query = new yii\db\Query();
            $row = $query->select('menu_id')->from('myacc_link')->where('controller_name="social/connect"')->one();
            $c->createCommand()->delete('myacc_link', 'menu_id=:menu_id', array('menu_id' => $row['menu_id']));
            $c->createCommand()->delete('myacc_link_description', 'menu_bar_id=:menu_id', array('menu_id' => $row['menu_id']));
            echo "\n[ UPDATE ] droped menu successfully\n\n";

            $_col = array(
                'display_status' => '1'
            );
            $c->createCommand()->update('myacc_link', $_col, 'controller_name="facebook/index"');
            echo "\n[ UPDATE ] display menu 'facebook/index' successfully \n\n";

        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version downgrade\n";
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

