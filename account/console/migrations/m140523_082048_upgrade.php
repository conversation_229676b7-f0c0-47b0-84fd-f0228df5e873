<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140523_082048_upgrade extends Migration {

    public function up() {
        $c = $this->getDb();
        try {
            $query = new yii\db\Query();
            $fbConnections = $query->select('customers_id, customers_fb_uid')->from('customers_connection')->all();
            foreach ($fbConnections as $row) {
                $_sql = "INSERT IGNORE INTO sns_connection (customers_id, provider, provider_uid) VALUES ('" . $row['customers_id'] . "','Facebook','" . $row['customers_fb_uid'] . "' )";
                $c->createCommand($_sql)->execute();
            }

        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version upgrade\n";
    }

    public function down() {
        $c = $this->getDb();
        try {
            $query = new yii\db\Query();
            $fbConnections = $query->select('customers_id, provider_uid')->from('sns_connection')->where('provider="Facebook"')->all();
            foreach ($fbConnections as $row) {
                $_sql = "INSERT IGNORE INTO customers_connection (customers_id, customers_fb_uid) VALUES ('" . $row['customers_id'] . "', '" . $row['provider_uid'] . "' )";
                $c->createCommand($_sql)->execute();
            }

            $this->delete('sns_connection', 'provider="Facebook"');
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        echo "End of Version downgrade\n";
    }

    /*
      // Use safeUp/safeDown to do migration with transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}

