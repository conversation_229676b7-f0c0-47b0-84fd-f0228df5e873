<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140811_081642_upgrade extends Migration {

    public function up() {
        try {
            $this->dropIndex('PRIMARY', 'sns_connection');

            $c = $this->getDb();
            $_sql = "ALTER TABLE sns_connection ADD PRIMARY KEY (provider, provider_uid)";
            $c->createCommand($_sql)->execute();
            echo "    > " . $_sql . "\n";

            echo "End of Version upgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        try {
            $this->dropIndex('PRIMARY', 'sns_connection');
            
            $c = $this->getDb();
            $_sql = "ALTER TABLE sns_connection ADD PRIMARY KEY (customers_id, provider)";
            $c->createCommand($_sql)->execute();
            echo "    > " . $_sql . "\n";

            echo "End of Version downgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

}