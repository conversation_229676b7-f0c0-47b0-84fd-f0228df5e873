<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m140825_081142_upgrade extends Migration {

    public function up() {
        try {
            $this->createIndex('idx_customer_provider', 'sns_connection', 'customers_id, provider');

            echo "End of Version downgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        try {
            $this->dropIndex('idx_customer_provider', 'sns_connection');

            echo "End of Version downgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

}