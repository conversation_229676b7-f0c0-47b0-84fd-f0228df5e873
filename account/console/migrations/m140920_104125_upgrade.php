<?php

use yii\db\Schema;
use yii\db\Migration;

class m140920_104125_upgrade extends Migration {

    public function up() {
        $this->addColumn("customers_info", "account_created_country", "char(2) NULL AFTER customers_info_account_created_ip");
        $this->addColumn("customers_info", "account_created_site", "varchar(32) NULL AFTER customers_info_account_created_from");
        $this->addColumn("customers_login_ip_history", "login_method", "varchar(20) NULL");

        // move Seller Payment from general section to g2g marketplace section
        $this->update("myacc_link", array("menu_group" => 3), "menu_id = 7");
        $this->update("myacc_link", array("menu_group" => 3), "menu_id = 23");
        $this->update("myacc_link", array("menu_group" => 3), "menu_id = 27");

        // hide Verify Email Address
        $this->update("myacc_link", array("display_status" => 0), "menu_id = 24");

        // relocate option position
        $this->update("myacc_link", array("sort_order" => 3), "menu_id = 28");  // SNS
        $this->update("myacc_link", array("sort_order" => 16), "menu_id = 3");  // WOR
        $this->update("myacc_link", array("sort_order" => 2), "menu_id = 9");  // store credit convert
        $this->update("myacc_link", array("sort_order" => 3), "menu_id = 6");  // store credit statement

        // update description
        $this->update("myacc_link_description", array("menu_name" => "Manage Profile"), "menu_bar_id = 2 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Social Connect"), "menu_bar_id = 28 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Store Credits Statement"), "menu_bar_id = 6 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Buy Orders"), "menu_bar_id = 16 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Buy Orders"), "menu_bar_id = 18 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Customer Orders"), "menu_bar_id = 20 AND language_id = 1");
    }

    public function down() {
        $this->dropColumn("customers_info", "account_created_country");
        $this->dropColumn("customers_info", "account_created_site");
        $this->dropColumn("customers_login_ip_history", "login_method");

        // move Seller Payment from g2g marketplace section to general section
        $this->update("myacc_link", array("menu_group" => 1), "menu_id = 7");
        $this->update("myacc_link", array("menu_group" => 1), "menu_id = 23");
        $this->update("myacc_link", array("menu_group" => 1), "menu_id = 27");

        // show Verify Email Address
        $this->update("myacc_link", array("display_status" => 1), "menu_id = 24");

        // relocate option position
        $this->update("myacc_link", array("sort_order" => 16), "menu_id = 28");  // SNS
        $this->update("myacc_link", array("sort_order" => 3), "menu_id = 3");  // WOR
        $this->update("myacc_link", array("sort_order" => 3), "menu_id = 9");  // store credit convert
        $this->update("myacc_link", array("sort_order" => 2), "menu_id = 6");  // store credit statement
        
        // update description
        $this->update("myacc_link_description", array("menu_name" => "Profile"), "menu_bar_id = 2 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Social Networks Setting"), "menu_bar_id = 28 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Store Credits History"), "menu_bar_id = 6 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Orders"), "menu_bar_id = 16 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Orders"), "menu_bar_id = 18 AND language_id = 1");
        $this->update("myacc_link_description", array("menu_name" => "Sell Orders"), "menu_bar_id = 20 AND language_id = 1");
    }

}