<?php

use yii\db\Schema;
use yii\db\Migration;

class m150727_065646_upgrade extends Migration {

    // Use safeUp/safeDown to do migration with transaction
    public function safeUp() {
        $this->insert('configuration', [
            'configuration_title' => 'CM Product Token',
            'configuration_key' => 'SMS_CM_PRODUCT_TOKEN',
            'configuration_value' => '',
            'configuration_description' => 'CM SMS Product Token',
            'configuration_group_id' => 914,
            'sort_order' => 20,
            'use_function' => null,
            'set_function' => null,
        ]);
    }

    public function safeDown() {
        $this->delete('configuration', 'configuration_key = "SMS_CM_PRODUCT_TOKEN" ');
    }
}
