<?php

use yii\db\Schema;
use yii\db\Migration;

class m160119_100911_upgrade extends Migration {

    public function up() {
        $this->insert('configuration', [
            'configuration_title' => 'Nexmo API Key',
            'configuration_key' => 'SMS_NEXMO_API_KEY',
            'configuration_value' => '',
            'configuration_description' => 'Nexmo Verify API Key',
            'configuration_group_id' => 914,
            'sort_order' => 25,
            'use_function' => null,
            'set_function' => null,
        ]);
        
        $this->insert('configuration', [
            'configuration_title' => 'Nexmo API Secret',
            'configuration_key' => 'SMS_NEXMO_API_SECRET',
            'configuration_value' => '',
            'configuration_description' => 'Nexmo Verify API Secret',
            'configuration_group_id' => 914,
            'sort_order' => 30,
            'use_function' => null,
            'set_function' => null,
        ]);
        
        $this->alterColumn('customers_otp', 'customers_otp_digit', 'varchar(32) NULL');
    }

    public function down() {
        $this->delete('configuration', 'configuration_key = "SMS_NEXMO_API_KEY" ');
        $this->delete('configuration', 'configuration_key = "SMS_NEXMO_API_SECRET" ');
        $this->alterColumn('customers_otp', 'customers_otp_digit', 'varchar(8) NULL');
    }
}
