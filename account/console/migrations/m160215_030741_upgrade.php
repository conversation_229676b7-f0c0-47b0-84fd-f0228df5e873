<?php

use yii\db\Schema;
use yii\db\Migration;

class m160215_030741_upgrade extends Migration {

    public function up() {
        $this->createTable('log_phone_verification_api_call', array(
            'id' => 'pk',
            'provider' => 'varchar(20) NOT NULL',
            'request_type' => 'varchar(32) NOT NULL',
            'customers_id' => 'int(11) NOT NULL',
            'action' => 'varchar(128) NOT NULL',
            'token' => 'varchar(32) NOT NULL',
            'extra_data' => 'varchar(255) NULL',
            'created_date' => 'DATETIME NOT NULL'
        ), 'ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci');
    }

    public function down() {
        $this->dropTable('log_phone_verification_api_call');
    }
}
