<?php

use yii\db\Schema;
use yii\db\Migration;

class m160412_065533_upgrade extends Migration {

    public function safeUp() {
        $this->insert('configuration', [
            'configuration_title' => 'Twilio API SID',
            'configuration_key' => 'ANTIFRAUD_TWILIO_SID',
            'configuration_value' => '',
            'configuration_description' => 'Twilio API SID',
            'configuration_group_id' => 910,
            'sort_order' => 6100,
            'use_function' => null,
            'set_function' => null,
        ]);

        $this->insert('configuration', [
            'configuration_title' => 'Twilio API Token',
            'configuration_key' => 'ANTIFRAUD_TWILIO_TOKEN',
            'configuration_value' => '',
            'configuration_description' => 'Twilio API Token',
            'configuration_group_id' => 910,
            'sort_order' => 6200,
            'use_function' => null,
            'set_function' => null,
        ]);
        
        $this->update('configuration', [
            'set_function' => "tep_cfg_select_option(array('maxmind', 'telesign','neutrino', 'twilio'),",
        ], 'configuration_key = "TELEPHONE_VERIFICATION_SERVICES"');
    }

    public function safeDown() {
        $this->delete('configuration', 'configuration_key = "ANTIFRAUD_TWILIO_SID"');
        $this->delete('configuration', 'configuration_key = "ANTIFRAUD_TWILIO_TOKEN"');
        
        $this->update('configuration', [
            'set_function' => "tep_cfg_select_option(array('maxmind', 'telesign','neutrino'),",
        ], 'configuration_key = "TELEPHONE_VERIFICATION_SERVICES"');
        
        $this->update('configuration', [
            'configuration_value' => 'neutrino',
        ], 'configuration_key = "TELEPHONE_VERIFICATION_SERVICES" AND configuration_value = "twilio"');
    }
}
