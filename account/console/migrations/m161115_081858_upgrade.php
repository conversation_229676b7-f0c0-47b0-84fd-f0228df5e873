<?php

use yii\db\Schema;
use yii\db\Migration;
use \Exception;

class m161115_081858_upgrade extends Migration {

    public function up() {

        try {
            // PC template
            $c = $this->getDb();
            $m_attr = array(1 => array('account_id' => array('entry' => 'ENTRY_BUYBACK_ACCOUNT_ID', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'password' => array('entry' => 'ENTRY_BUYBACK_PASSWORD', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',),), 2 => array('first_name' => array('entry' => 'ENTRY_BUYBACK_FIRST_NAME', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'last_name' => array('entry' => 'ENTRY_BUYBACK_LAST_NAME', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'account_country' => array('entry' => 'ENTRY_BUYBACK_ACCOUNT_COUNTRY', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'date_of_birth' => array('entry' => 'ENTRY_BUYBACK_DOB', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'email_account' => array('entry' => 'ENTRY_BUYBACK_EMAIL_ACCOUNT', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'email_password' => array('entry' => 'ENTRY_BUYBACK_EMAIL_PASSWORD', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',),), 3 => array('secret_question' => array('entry' => 'ENTRY_BUYBACK_SECRET_QUESTION', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'secret_answer' => array('entry' => 'ENTRY_BUYBACK_SECRET_ANSWER', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',),), 99 => array('additional_note' => array('entry' => 'ENTRY_BUYBACK_ADDITIONAL_NOTE', 'validation' => 'max_char:240', 'li_width' => '700', 'type' => 'text', 'size' => '73', 'maxlength' => '240')));
            $this->insert('products_hla_template', [
                'hla_template_name' => 'pc',
                'hla_template_data' => urlencode(serialize($m_attr)),
                'hla_template_sort_order' => 1,
                'hla_template_status' => 1
            ]);
            $query = new yii\db\Query();
            $m_data = $query->select('hla_template_id')->from('products_hla_template')->all();
            foreach ($m_data as $row) {
                $this->update('products_templates', array('products_templates_id' => $row["hla_template_id"]));
            }

            // Mobile template
            $m_attr = array(1 => array('account_id' => array('entry' => 'ENTRY_BUYBACK_ACCOUNT_ID', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'password' => array('entry' => 'ENTRY_BUYBACK_PASSWORD', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',),), 2 => array('first_name' => array('entry' => 'ENTRY_BUYBACK_FIRST_NAME', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'last_name' => array('entry' => 'ENTRY_BUYBACK_LAST_NAME', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'account_country' => array('entry' => 'ENTRY_BUYBACK_ACCOUNT_COUNTRY', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'date_of_birth' => array('entry' => 'ENTRY_BUYBACK_DOB', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',),), 3 => array('secret_question' => array('entry' => 'ENTRY_BUYBACK_SECRET_QUESTION', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'secret_answer' => array('entry' => 'ENTRY_BUYBACK_SECRET_ANSWER', 'validation' => 'mandatory', 'li_width' => '300', 'type' => 'text',), 'secret_question_2' => array('entry' => 'ENTRY_BUYBACK_SECRET_QUESTION_2', 'validation' => '', 'li_width' => '300', 'type' => 'text',), 'secret_answer_2' => array('entry' => 'ENTRY_BUYBACK_SECRET_ANSWER_2', 'validation' => '', 'li_width' => '300', 'type' => 'text',), 'secret_question_3' => array('entry' => 'ENTRY_BUYBACK_SECRET_QUESTION_3', 'validation' => '', 'li_width' => '300', 'type' => 'text',), 'secret_answer_3' => array('entry' => 'ENTRY_BUYBACK_SECRET_ANSWER_3', 'validation' => '', 'li_width' => '300', 'type' => 'text',),), 99 => array('additional_note' => array('entry' => 'ENTRY_BUYBACK_ADDITIONAL_NOTE', 'validation' => 'max_char:240', 'li_width' => '700', 'type' => 'text', 'size' => '73', 'maxlength' => '240')));
            $this->insert('products_hla_template', [
                'hla_template_name' => 'mobile',
                'hla_template_data' => urlencode(serialize($m_attr)),
                'hla_template_sort_order' => 5,
                'hla_template_status' => 1
            ]);
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        echo "m161115_081858_upgrade does not support migration down.\n";
        return true;
    }

}
