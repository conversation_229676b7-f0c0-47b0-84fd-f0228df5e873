<?php

use yii\db\Migration;

class m180611_071133_upgrade extends Migration
{
    public function up()
    {
        foreach($this->imList() as $id => $data) {
            $this->insert('{{%instant_message_type}}', $data);
        }
        $this->update('{{%instant_message_type}}', [
            'instant_message_type_name' => 'Hangout',
        ], [
            'instant_message_type_id' => 4
        ]);
    }

    public function down()
    {
        $this->delete('{{%instant_message_type}}', [
            'instant_message_type_id' => array_keys($this->imList()),
        ]);
        $this->update('{{%instant_message_type}}', [
            'instant_message_type_name' => 'Google Talk',
        ], [
            'instant_message_type_id' => 4
        ]);
    }
    
    protected function imList() {
        return [
            '8' => [
                'instant_message_type_id' => '8',
                'instant_message_type_name' => 'Wechat',
                'instant_message_type_description' => 'Wechat Messenger',
                'instant_message_type_order' => '8',
            ],
            '9' => [
                'instant_message_type_id' => '9',
                'instant_message_type_name' => 'Line',
                'instant_message_type_description' => 'Line Messenger',
                'instant_message_type_order' => '9',
            ],
            '10' => [
                'instant_message_type_id' => '10',
                'instant_message_type_name' => 'Facebook Messenger',
                'instant_message_type_description' => 'Facebook Messenger',
                'instant_message_type_order' => '10',
            ],
            '11' => [
                'instant_message_type_id' => '11',
                'instant_message_type_name' => 'Whatsapp',
                'instant_message_type_description' => 'Whatsapp Messenger',
                'instant_message_type_order' => '11',
            ],
            '12' => [
                'instant_message_type_id' => '12',
                'instant_message_type_name' => 'Others',
                'instant_message_type_description' => 'Others',
                'instant_message_type_order' => '12',
            ],
        ];
    }
}
