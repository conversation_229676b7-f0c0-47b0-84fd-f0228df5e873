<?php

use yii\db\Migration;

class m190301_080453_create_store_credit_split extends Migration
{
    public function up() {
        if ( Yii::$app->db->schema->getTableSchema('{{%store_credit_split}}') === null ) {
            $tableOptions = null;
            if ( $this->db->driverName === 'mysql' ) {
                // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
                $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
            }
            $this->createTable('{{%store_credit_split}}', [
                'id'         => $this->primaryKey(),
                'user_id'  => $this->integer()->notNull()->unique(),
                'og_perc'    => $this->integer()->notNull(),
                'g2g_perc'   => $this->integer()->notNull(),
                'created_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP',
                'updated_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                    ], $tableOptions);

            echo "store_credit_split table created successfully\n";
        }
        
    }

    public function down()
    {
        $this->dropTable('{{%store_credit_split}}');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
