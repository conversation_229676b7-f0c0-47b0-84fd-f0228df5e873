<?php

use yii\db\Migration;

class m190326_023860_alter_store_credit_split_add_confirm extends Migration {

    public function up() {
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->addColumn('{{%store_credit_split}}', 'confirm', $this->integer());
        echo "confirm columns created successfully\n";
    }

    public function down() {
        $this->dropColumn('{{%store_credit_split}}', 'confirm');
    }

    /*
      // Use safeUp/safeDown to run migration code within a transaction
      public function safeUp()
      {
      $this->dropColumn('{{%store_credit_split}}', 'confirm')
      }

      }

      public function safeDown()
      {
      $this->dropColumn('{{%store_credit_split}}', 'confirm')
      }
     */
}
