<?php

use yii\db\Migration;

class m190702_025800_altere_store_credit_split_add_migrate_flags extends Migration
{
    public function up()
    {
    if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->addColumn('{{%store_credit_split}}', 'migrate_wor', $this->getDb()->getSchema()->createColumnSchemaBuilder('tinyint', 0)->defaultValue(0));
        $this->addColumn('{{%store_credit_split}}', 'migrate_sc', $this->getDb()->getSchema()->createColumnSchemaBuilder('tinyint', 0)->defaultValue(0));
        echo "confirm columns created successfully\n";
    }

    public function down()
    {
        echo "m190702_025800_altere_store_credit_split_add_migrate_flags cannot be reverted.\n";
        $this->dropColumn('{{%store_credit_split}}', 'migrate_wor');
        $this->dropColumn('{{%store_credit_split}}', 'migrate_sc');

        return false;
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
