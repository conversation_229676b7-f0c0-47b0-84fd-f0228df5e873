<?php

use yii\db\Migration;

class m190822_012923_mexico_num_formatting extends Migration {

    public function safeUp() {
        $rows = $this->getDb()->createCommand("SELECT a.customers_id as customer_a, a.customers_info_value as value_a, b.customers_id as customer_b, b.customers_info_value as value_b FROM customers_info_verification a INNER JOIN customers_info_verification b ON a.info_verification_type = 'telephone' AND b.info_verification_type = 'telephone' AND b.customers_info_value = CONCAT(SUBSTR(a.customers_info_value, 1, 2), SUBSTR(a.customers_info_value, 4)) WHERE a.customers_info_value LIKE '521%'")->queryAll();
        $customers_id = [];
        foreach ($rows as $row) {
            $customers_id[$row['customer_a']] = $row['customer_a'];
            $customers_id[$row['customer_b']] = $row['customer_b'];
            $this->getDb()->createCommand("DELETE FROM customers_info_verification WHERE customers_id = " . $row['customer_a'] . " AND customers_info_value = '" . $row['value_a'] . "' AND info_verification_type = 'telephone'")->execute();
            $this->getDb()->createCommand("DELETE FROM customers_info_verification WHERE customers_id = " . $row['customer_b'] . " AND customers_info_value = '" . $row['value_b'] . "' AND info_verification_type = 'telephone'")->execute();
        }
        
        if (!empty($customers_id)) {
            $this->delete('{{%customers_setting}}', [
                'customers_id' => array_values($customers_id),
                'customers_setting_key' => 'setup_account',
            ]);
        }
        
        foreach ([
            "UPDATE customers_info_verification SET customers_info_value = CONCAT(SUBSTR(customers_info_value, 1, 2), SUBSTR(customers_info_value, 4)) WHERE info_verification_type = 'telephone' AND customers_info_value LIKE '521%';",
            "UPDATE customers SET customers_telephone = SUBSTR(customers_telephone, 2)WHERE customers_country_dialing_code_id = 138 AND customers_telephone LIKE '1%' AND SUBSTR(customers_telephone, 2) NOT IN (SELECT customers_telephone FROM (SELECT customers_telephone FROM customers WHERE customers_country_dialing_code_id = 138) tmp);",
        ] as $i => $sql) {
            $c = $this->getDb()->createCommand($sql)->execute();
            echo "SQL " . ($i + 1) . " - " . $c . " records affected\n";
        }
    }

    public function safeDown() {
        echo "m190822_012923_mexico_num_formatting cannot be reverted.\n";

        return false;
    }

}
