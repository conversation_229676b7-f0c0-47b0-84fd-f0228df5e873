<?php

use yii\db\Migration;

class m190827_023101_customer_pipwave_verification_create extends Migration
{
    public function up()
    {
        $tableOptions = null;
        $tableName = '{{%customer_pipwave_verification}}';
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $columns = [
            "verification_id" =>$this->bigPrimaryKey(),
            "session_token" =>$this->string(64),
            "verification_type" =>$this->string(50),
            "customer_id" =>$this->integer(),
            "attempts_num" =>$this->smallInteger(),
            "ip_address" =>$this->string(20),
            "mode" =>$this->string(20),
            "user_agent" =>$this->string(),
            "verification_status" =>$this->smallInteger(),
            "pd_id" =>$this->string(50),
            "document_set_scoring" =>$this->decimal(),
            "started_at" =>$this->integer(),
            "completed_at" =>$this->integer(),
            "api_key" =>$this->string(50),
            "timestamp" =>$this->integer(),
            "signature" =>$this->string(50),
            "face_id" =>$this->string(50),
            "extra_param1" =>$this->text(),
            "extra_param2" =>$this->text(),
            "extra_param3" =>$this->text(),
        ];

        $this->createTable($tableName, $columns, $tableOptions);
        $this->createIndex($tableName.'.customer_id',$tableName,'customer_id',0);

    }

    public function down()
    {
        $this->dropTable('{{%customer_pipwave_verification}}');
        echo "m190827_023101_customer_pipwave_verification_create  reverted.\n";

    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
