<?php

use yii\db\Migration;

class m190827_023155_customer_pipwave_verification_doc_create extends Migration
{
    public function up()
    {
        $tableOptions = null;
        $tableName ='{{%customer_pipwave_verification_doc}}';
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $columns = [
            "document_id" => $this->bigPrimaryKey(),
            "verification_id" => $this->bigInteger(),
            "document_type" => $this->string(30),
            "subject_filename" => $this->string(250),
            "subject_size" => $this->integer(),
            "subject_url" => $this->text(),
            "full_filename" =>$this->string(250),
            "full_size" => $this->string(250),
            "full_url" => $this->text(),
            "document_scoring" => $this->decimal(),
            "result" => $this->smallInteger(),
        ];

        $this->createTable($tableName, $columns, $tableOptions);
        $this->createIndex($tableName.'.verification_id',$tableName,'verification_id',0);
    }

    public function down()
    {
        $this->dropTable('{{%customer_pipwave_verification_doc}}');
        echo "m190827_023155_customer_pipwave_verification_doc_create cannot be reverted.\n";
        return false;
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
