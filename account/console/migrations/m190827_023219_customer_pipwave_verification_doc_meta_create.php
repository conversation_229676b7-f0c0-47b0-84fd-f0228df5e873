<?php

use yii\db\Migration;

class m190827_023219_customer_pipwave_verification_doc_meta_create extends Migration
{
    public function up()
    {
        $tableOptions = null;
        $tableName = '{{%customer_pipwave_verification_doc_meta}}';
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $columns = [
                "document_meta_id" =>$this->bigPrimaryKey(),
                "document_id" => $this->bigInteger(),
                "meta_type" =>$this->string(100),
                "meta_label" =>$this->string(100),
                "meta_value" =>$this->text()
         ];

        $this->createTable($tableName, $columns, $tableOptions);
        $this->createIndex($tableName.'.document_id',$tableName,'document_id',0);
        $this->createIndex($tableName.'.meta_type',$tableName,'meta_type',0);

    }

    public function down()
    {
        $this->dropTable('{{%customer_pipwave_verification_doc_meta}}');
        echo "m190827_023219_customer_pipwave_verification_doc_meta_create  reverted.\n";

        return false;
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
