<?php

use yii\db\Migration;

class m190828_060748_customer_pipwave_verification_token_create extends Migration {

    public function up() {
        $tableOptions = null;
        $tableName = '{{%customer_pipwave_verification_token}}';
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $columns = [
            "token_id" => $this->bigPrimaryKey(),
            "token" => $this->string(100),
            "customer_id" => $this->integer(),
            "result" => $this->smallInteger(1),
            "created_at" => $this->integer(),
            "updated_at"=> $this->integer(),
            "expire_at"=> $this->integer()
        ];
        
        $this->createTable($tableName, $columns, $tableOptions);
        $this->createIndex($tableName.'.unique_token',$tableName, 'token', 1);
        $this->createIndex($tableName.'.customer_id',$tableName, 'customer_id', 0);
        $this->createIndex($tableName.'.expire_at',$tableName, 'expire_at', 0);
    }

    public function down() {
        echo "m190828_060748_customer_pipwave_verification_token_create cannot be reverted.\n";
        return false;
    }

    /*
      // Use safeUp/safeDown to run migration code within a transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}
