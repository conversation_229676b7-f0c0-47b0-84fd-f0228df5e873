<?php

use yii\db\Migration;

class m190905_021209_store_credit_spit_add_wor extends Migration
{
    public function up() {
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->addColumn('{{%store_credit_split}}', 'g2g_wor', '  INTEGER AFTER g2g_perc');
        $this->addColumn('{{%store_credit_split}}', 'og_wor', '  INTEGER AFTER g2g_perc');

        echo "confirm columns created successfully\n";
    }

    public function down()
    {
        $this->dropColumn('{{%store_credit_split}}', 'g2g_wor');
        $this->dropColumn('{{%store_credit_split}}', 'og_wor');
        echo "m190905_021209_store_credit_spit_add_wor reverted.\n";

        
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
