<?php

use yii\db\Migration;

class m190925_072708_customers_document_id_verification extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }
        
        $this->createTable('{{%customers_document_id_verification}}', [
            'id' => $this->bigPrimaryKey(),
            'customers_id' => $this->integer()->notNull(),
            'customers_firstname' => $this->string(32)->notNull(),
            'customers_lastname' => $this->string(32)->notNull(),
            'customers_edit_firstname' => $this->string(32)->notNull(),
            'customers_edit_lastname' => $this->string(32)->notNull(),
            'name_lock' => $this->boolean()->notNull(),
            'name_verify' => $this->boolean()->notNull(),
            'waiting_for_document' => $this->boolean()->notNull(),
                ], $tableOptions);
        
        $this->createIndex('idx__customers_id', '{{%customers_document_id_verification}}', ['customers_id']);
        $this->addForeignKey('fk__cdiv__customers_id', '{{%customers_document_id_verification}}', 'customers_id', '{{%customers}}', 'customers_id', 'CASCADE', 'CASCADE');


    }

    public function down()
    {
        $this->dropTable('{{%customers_document_id_verification}}');
    }
}
