<?php

use yii\db\Migration;

class m190926_075035_fix_nulled_wor_split extends Migration
{
    public function up()
    {
        $wor_default =  [ 'og_wor'=>100, 'g2g_wor' =>0 ];
        \common\models\ShassoSplitStoreCredit::updateAll($wor_default,[ 'og_wor'=>null, 'g2g_wor' =>null ]   );
    }

    public function down()
    {
        echo "m190926_075035_fix_nulled_wor_split cannot be reverted.\n";



    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}