<?php

use yii\db\Migration;

class m190930_090306_customers_name_verification_log extends Migration
{
   public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }
        
        $this->createTable('{{%customers_name_verification_log}}', [
            'id' => $this->bigPrimaryKey(),
            'verification_id' => $this->bigInteger()->notNull(),
            'customers_id' => $this->integer()->notNull(),
            'customers_firstname' => $this->string(32)->notNull(),
            'customers_lastname' => $this->string(32)->notNull(),
            'customers_pd_id' => $this->string(32)->notNull(),
            'customers_document_score' => $this->string(10)->notNull(),
            'verify_status' => $this->string(10)->notNull(),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer(),
                ], $tableOptions);
        
        $this->createIndex('idx__customers_id_customers_pd_id', '{{%customers_name_verification_log}}', ['customers_id','customers_pd_id']);
        $this->addForeignKey('fk__cnvl__customers_id', '{{%customers_name_verification_log}}', 'customers_id', '{{%customers}}', 'customers_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk__cnvl__verification_id', '{{%customers_name_verification_log}}', 'verification_id', '{{%customer_pipwave_verification}}', 'verification_id', 'CASCADE', 'CASCADE');

    }

    public function down()
    {
        $this->dropTable('{{%customers_name_verification_log}}');
    }
}
