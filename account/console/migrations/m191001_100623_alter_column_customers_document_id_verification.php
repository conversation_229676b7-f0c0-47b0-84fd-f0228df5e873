<?php

use yii\db\Migration;

class m191001_100623_alter_column_customers_document_id_verification extends Migration
{
    public function up()
    {
        $this->alterColumn('{{%customers_document_id_verification}}', 'name_lock', $this->integer()->notNull());
        
        $this->dropColumn('{{%customers_document_id_verification}}', 'name_verify');
        
        $this->addColumn('{{%customers_document_id_verification}}', 'verify_status', $this->boolean()->notNull());
        $this->addColumn('{{%customers_document_id_verification}}', 'isAuto', $this->boolean()->notNull());
        $this->addColumn('{{%customers_document_id_verification}}', 'updated_at', $this->integer()->notNull());
    }

    public function down()
    {
        $this->alterColumn('{{%customers_document_id_verification}}', 'name_lock', $this->boolean()->notNull());
        
        $this->dropColumn('{{%customers_document_id_verification}}', 'isAuto');
        $this->dropColumn('{{%customers_document_id_verification}}', 'verify_status');
        $this->dropColumn('{{%customers_document_id_verification}}', 'updated_at');
        
        $this->addColumn('{{%customers_document_id_verification}}', 'name_verify', $this->boolean()->notNull());
    }
}
