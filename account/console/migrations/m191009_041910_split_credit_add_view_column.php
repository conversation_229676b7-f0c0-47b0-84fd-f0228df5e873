<?php

use yii\db\Migration;

class m191009_041910_split_credit_add_view_column extends Migration
{
    public function up()
    {
        $this->addColumn('{{%store_credit_split}}', 'has_viewed', $this->boolean()->notNull()->defaultValue(0)->after('updated_at'));
        $this->update('{{%store_credit_split}}', [
            'has_viewed' => 1,
        ], [
            'confirm' => 1,
        ]);
    }

    public function down()
    {
        $this->dropColumn('{{%store_credit_split}}', 'has_viewed');
    }

}
