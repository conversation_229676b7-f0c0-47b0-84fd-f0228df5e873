<?php

use yii\db\Migration;

class m191211_025633_alter_customer_pipwave_verification_add_approval_status extends Migration
{
    public function up()
    {
        $tableName = '{{%customer_pipwave_verification}}';
        if ($this->db->driverName === 'mysql') {
                // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
                $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
            }

            $this->addColumn($tableName, 'approval_status', $this->getDb()->getSchema()->createColumnSchemaBuilder('tinyint', 0)->defaultValue(0));
            echo "confirm columns created successfully\n";
        }



    public function down()
    {
        $tableName = '{{%customer_pipwave_verification}}';
        $this->dropColumn($tableName, 'approval_status');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
