<?php

use yii\db\Migration;

class m191227_021232_alter_column_customer_firstname_lastname extends Migration
{
    public function up()
    {
        $this->alterColumn('{{%customers_document_id_verification}}', 'verify_status', $this->integer()->notNull());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_edit_firstname', $this->string()->null());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_edit_lastname', $this->string()->null());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_firstname', $this->string()->null());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_lastname', $this->string()->null());
    }

    public function down()
    {
        $this->alterColumn('{{%customers_document_id_verification}}', 'verify_status', $this->boolean()->notNull());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_edit_firstname', $this->string()->notNull());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_edit_lastname', $this->string()->notNull());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_firstname', $this->string()->notNull());
        $this->alterColumn('{{%customers_document_id_verification}}', 'customers_lastname', $this->string()->notNull());
        
    }
}
