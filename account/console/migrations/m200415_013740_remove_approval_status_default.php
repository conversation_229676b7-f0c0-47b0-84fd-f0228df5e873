<?php

use yii\db\Migration;

class m200415_013740_remove_approval_status_default extends Migration
{
    public function up()
    {
        $this->alterColumn('{{%customer_pipwave_verification}}', 'approval_status', $this->integer());
        $this->update('{{%customer_pipwave_verification}}',['approval_status'=>null],['approval_status'=>0]);
    }

    public function down()
    {
        echo "m200415_013740_remove_approval_status_default cannot be reverted.\n";

        return false;
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
