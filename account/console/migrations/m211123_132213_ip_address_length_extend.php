<?php

use yii\db\Migration;

class m211123_132213_ip_address_length_extend extends Migration
{
    public function up()
    {
        $sql_list = [
            'ALTER TABLE `customer_pipwave_verification` CHANGE `ip_address` `ip_address` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;',
        ];
        foreach ($sql_list as $sql) {
            echo $sql . "\n";
            \Yii::$app->getDb()->createCommand($sql)->execute();
        }
    }

    public function down()
    {
        $sql_list = [
            'ALTER TABLE `customer_pipwave_verification` CHANGE `ip_address` `ip_address` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;',
        ];
        foreach ($sql_list as $sql) {
            echo $sql . "\n";
            \Yii::$app->getDb()->createCommand($sql)->execute();
        }
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
