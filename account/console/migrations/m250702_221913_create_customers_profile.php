<?php

use yii\db\Migration;

/**
 * Class m250702_221913_create_customers_profile
 */
class m250702_221913_create_customers_profile extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('customers_profile', [
            'customers_id' => $this->integer()->unsigned()->notNull(),
            'field_key' => $this->string(32)->notNull(),
            'value' => $this->string(255)->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $tableOptions);

        $this->addPrimaryKey('pk_customers_profile', 'customers_profile', ['customers_id', 'field_key']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('customers_profile');
    }
}