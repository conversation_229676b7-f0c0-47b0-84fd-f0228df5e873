<?php

use yii\db\Migration;

/**
 * Class m250704_204142_create_einvoice_profile_update_queue_table
 */
class m250704_204142_create_einvoice_profile_update_queue_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('einvoice_profile_update_queue', [
            'id' => $this->primaryKey()->unsigned(),
            'customers_id' => $this->integer()->notNull(),
            'user_info' => $this->text(),
            'is_sqs_sent' => $this->boolean()->notNull()->defaultValue(false),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_customers_id', 'einvoice_profile_update_queue', 'customers_id');
        $this->createIndex('idx_is_sqs_sent', 'einvoice_profile_update_queue', 'is_sqs_sent');
        $this->createIndex('idx_created_at', 'einvoice_profile_update_queue', 'created_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx_customers_id', 'einvoice_profile_update_queue');
        $this->dropIndex('idx_is_sqs_sent', 'einvoice_profile_update_queue');
        $this->dropIndex('idx_created_at', 'einvoice_profile_update_queue');
        $this->dropTable('einvoice_profile_update_queue');
    }
}