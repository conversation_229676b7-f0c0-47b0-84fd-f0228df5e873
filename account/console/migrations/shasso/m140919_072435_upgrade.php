<?php

use yii\db\Schema;
use yii\db\Migration;

class m140919_072435_upgrade extends Migration {
    /*
     * NOTE : insert record sample provided
     */

    public function up() {
        $tbl_opt = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        // create table
        $this->createTable("client", array(
            "client_id" => "varchar(32) NOT NULL",
            "client_name" => "varchar(64) NOT NULL",
            "client_secret" => "varchar(32) NOT NULL",
            "redirect_url" => "varchar(255) DEFAULT NULL COMMENT 'origin'",
            "set_token_url" => "varchar(255) DEFAULT NULL",
            "delete_token_url" => "varchar(255) DEFAULT NULL",
            "PRIMARY KEY (client_id)",
            "KEY index_client_access (client_id, client_secret)"), $tbl_opt);

        $this->createTable("client_extra_info", array(
            "client_id" => "varchar(32) NOT NULL",
            "extra_key" => "varchar(32) NOT NULL",
            "extra_value" => "varchar(255) NOT NULL",
            "PRIMARY KEY (client_id, extra_key)"), $tbl_opt);

        $this->createTable("sso_token", array(
            "sso_token" => "varchar(36) NOT NULL",
            "sess_id" => "varchar(32) NOT NULL",
            "client_id" => "varchar(32) NOT NULL",
            "user_id" => "int(11) unsigned NOT NULL",
            "login_method" => "varchar(20) NOT NULL",
            "expiry" => "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "PRIMARY KEY (sso_token, sess_id)",
            "KEY index_sso_token (sso_token, client_id)",
            "KEY index_user_online_status (user_id, expiry)"), $tbl_opt);

        $this->createTable("user_token", array(
            "user_id" => "int(11) unsigned NOT NULL",
            "token_type" => "varchar(32) NOT NULL",
            "token_value" => "varchar(32) NOT NULL",
            "created_date" => "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "expiry_date" => "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "PRIMARY KEY (user_id, token_type)",
            "KEY index_token_type (token_type, expiry_date)",
            "KEY index_expiry_date (expiry_date)"), $tbl_opt);

        $this->createTable("user_preference", array(
            "user_id" => "int(11) unsigned NOT NULL",
            "preference_key" => "varchar(32) NOT NULL",
            "value" => "varchar(128) NOT NULL",
            "PRIMARY KEY (user_id, preference_key)"), $tbl_opt);

    }

    public function safeDown() {
        $this->dropTable("client");
        $this->dropTable("client_extra_info");
        $this->dropTable("sso_token");
        $this->dropTable("user_preference");
        $this->dropTable("user_token");
    }

}