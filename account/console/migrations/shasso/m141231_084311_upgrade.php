<?php

use yii\db\Schema;
use yii\db\Migration;

class m141231_084311_upgrade extends Migration {
    
    public function up() {
        $this->addColumn("client", "kick_user_url", "VARCHAR(255) NULL");
        
        // create table
        $this->createTable("user_login_history", array(
            "user_id" => "int(11) unsigned NOT NULL",
            "login_date" => "datetime NOT NULL",
            "login_ip" => "varchar(20) NOT NULL",
            "login_ua_info" => "varchar(255) NOT NULL",
            "login_method" => "varchar(12) NOT NULL",
            "PRIMARY KEY (user_id, login_date)",
            "KEY index_login_date_and_ip (login_date, login_ip)"), "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB");
    }

    public function down() {
        $this->dropColumn("client", "kick_user_url");
        $this->dropTable("user_login_history");
    }
}