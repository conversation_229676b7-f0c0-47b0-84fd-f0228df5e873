<?php

use yii\db\Schema;
use yii\db\Migration;

class m150109_080314_upgrade extends Migration {

    public function up() {
        $this->dropIndex("PRIMARY", "user_login_history");
        $this->createTable("user_last_login", array(
            "user_id" => "int(11) unsigned NOT NULL",
            "login_date" => "datetime NOT NULL",
            "login_ip" => "varchar(20) NOT NULL",
            "login_ip_iso2" => "varchar(2) NOT NULL",
            "PRIMARY KEY (user_id)"), "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB");
    }

    public function down() {
        $c = $this->getDb();
        $_sql = "ALTER TABLE user_login_history ADD PRIMARY KEY (user_id, login_date)";
        $c->createCommand($_sql)->execute();
        echo "    > " . $_sql . "\n";
        
        $this->dropTable("user_last_login");
    }

}