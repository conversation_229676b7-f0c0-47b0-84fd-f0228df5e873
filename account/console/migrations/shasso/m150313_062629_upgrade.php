<?php

use yii\db\Schema;
use yii\db\Migration;

class m150313_062629_upgrade extends Migration {

    public function up() {
        try {
            $this->createIndex('index_expiry', 'sso_token', 'expiry');
            $this->createIndex('index_session_id', 'sso_token', 'sess_id');
            $this->dropIndex('index_user_online_status', 'sso_token');

            echo "End of Version downgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function down() {
        try {
            $this->createIndex('index_user_online_status', 'sso_token', 'user_id, expiry');
            $this->dropIndex('index_expiry', 'sso_token');
            $this->dropIndex('index_session_id', 'sso_token');

            echo "End of Version downgrade. \n";
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

}