<?php

use yii\db\Schema;
use yii\db\Migration;

class m170220_082859_upgrade extends Migration {

    public function up() {
        $tbl_opt = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        // create table
        $this->createTable("customers_static_info", array(
            "customers_id" => "int(11) NOT NULL",
            "info_key" => "varchar(64) NOT NULL",
            "value" => "varchar(255) NOT NULL",
            "created_date" => "datetime NOT NULL",
            "updated_date" => "datetime NOT NULL",
            "PRIMARY KEY (customers_id, info_key)"), $tbl_opt);
    }

    public function down() {
        $this->dropTable('customers_static_info');
    }

}
