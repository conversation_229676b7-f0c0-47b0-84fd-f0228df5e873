<?php

use yii\db\Migration;
use yii\db\Schema;

class m180430_062752_generic_events extends Migration {

    public function up() {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }
        $this->createTable('{{%generic_event}}', [
            'id' => $this->bigPrimaryKey()->unsigned(),
            'type' => Schema::TYPE_SMALLINT . ' NOT NULL COMMENT "Type of event, 1=Exception, 2=Notification, 3=Debug"',
            'visible_to' => Schema::TYPE_SMALLINT . ' NOT NULL COMMENT "Who can see this, in bit-switch format"',
            'updated_at' => Schema::TYPE_INTEGER . ' NOT NULL',
            'created_at' => Schema::TYPE_INTEGER . ' NOT NULL',
            'app' => Schema::TYPE_STRING . '(16) NULL',
            'script' => Schema::TYPE_STRING . '(255) NULL',
            'line' => Schema::TYPE_INTEGER . ' NULL',
            'title' => Schema::TYPE_STRING . '(255) NULL COMMENT "Title/summary of the event"',
            'message' => Schema::TYPE_TEXT . ' NULL COMMENT "Content of the event"',
            'env_dump' => Schema::TYPE_TEXT . ' NULL COMMENT "JSON-encoded server environmental dump"',
            'tags' => Schema::TYPE_STRING . '(255) NULL',
                ], $tableOptions);
    }

    public function down() {
        $this->dropTable('{{%generic_event}}');
    }

}
