<?php

use yii\db\Migration;

class m190405_022751_reformat_customer_phone_after_setup extends Migration
{
    public function up()
    {
        $remark_prefix = 'Formatting bugfix by system';
        
        $results = (new \yii\db\Query())->distinct('customers_id')->from('{{%customers_setting}}')->where([
            'and',
            ['>', 'customers_setting_value', 0],
            ['=', 'customers_setting_key', 'setup_account'],
        ])->column();
        if (!$results) {
            return true;
        }
        $customers_list = (new \yii\db\Query())->select(['customers_id', 'customers_country_dialing_code_id', 'customers_telephone'])
                ->from('{{%customers}}')
                ->where([
            'customers_id' => $results,
        ])->all();
        $classCustomersInfoVerificationComponent = new \common\components\CustomersInfoVerificationComponent();
        foreach($customers_list as $customer) {
            $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($customer['customers_telephone'], $customer['customers_country_dialing_code_id']);
            $phoneNumber = $parse_result->national_number;
            if ($phoneNumber == $customer['customers_telephone']) {
                continue;
            }
            
            $cust_data = array(
                'customers_telephone' => $phoneNumber,
            );
            $cust_orig = array_intersect_key($customer, $cust_data);
            \common\models\Customers::updateAll($cust_data, ['customers_id' => $customer['customers_id']]);
            \common\models\CustomersInfo::model()->updateLastModify($customer['customers_id']);
            
            $_diff = \common\components\GeneralComponent::arrayDiff($cust_orig, $cust_data);
            if ($_diff) {
                $log_obj = new \common\components\LogFilesComponent($customer['customers_id']);
                $mod = $log_obj->detectChanges($_diff, $cust_data);
                $_log = $log_obj->constructLogMessage($mod);

                $change_log = $log_obj->contructChangesString($mod, "");
                \common\models\CustomersInfo::model()->updateChangeMade($change_log, $customer['customers_id']);

                $change_str = $remark_prefix . ":\n";
                for ($i = 0, $cnt = count($_log); $cnt > $i; $i++) {
                    if (is_array($_log[$i])) {
                        foreach ($_log[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $change_str .= $res['text'] . "\n";
                            } else {
                                $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }
                $log_obj->insertCustomerHistoryLog('system', $change_str);
            }
        }
    }

    public function down()
    {
        echo "Nothing to revert";
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
