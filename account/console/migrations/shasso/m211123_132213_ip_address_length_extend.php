<?php

use yii\db\Migration;

class m211123_132213_ip_address_length_extend extends Migration
{
    public function up()
    {
        $sql_list = [
            'ALTER TABLE `user_last_login` CHANGE `login_ip` `login_ip` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL;',
        ];
        foreach ($sql_list as $sql) {
            echo $sql . "\n";
            $this->getDb()->createCommand($sql)->execute();
        }
    }

    public function down()
    {
        $sql_list = [
            'ALTER TABLE `user_last_login` <PERSON>AN<PERSON> `login_ip` `login_ip` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL;',
        ];
        foreach ($sql_list as $sql) {
            echo $sql . "\n";
            $this->getDb()->createCommand($sql)->execute();
        }
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
