<?php

use yii\db\Migration;

class m220113_101722_remember_me_token_log extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }
        $this->createTable('{{%remember_me_token_update_log}}', [
            'id' => $this->bigPrimaryKey()->unsigned(),
            'customers_id' => $this->integer()->notNull(),
            'token_id' => $this->string(255),
            'login_ip' => $this->string(128),
            'login_country' => $this->string(2),
            'user_agent' => $this->string(255),
            'parsed_ua_os' => $this->string(255),
            'parsed_ua_browser' => $this->string(255),
            'prev_login_ip' => $this->string(128),
            'prev_login_country' => $this->string(2),
            'prev_user_agent' => $this->string(255),
            'prev_parsed_ua_os' => $this->string(255),
            'prev_parsed_ua_browser' => $this->string(255),
            'type' => $this->string(16),
            'created_date' => $this->dateTime(),
                ], $tableOptions);
        
        $this->createIndex('idx__customers_id__created_date', '{{%remember_me_token_update_log}}', ['customers_id', 'created_date']);
        $this->createIndex('idx__created_date__type', '{{%remember_me_token_update_log}}', ['created_date', 'type']);
    }

    public function down()
    {
        $this->dropTable('{{%remember_me_token_update_log}}');
    }
}
