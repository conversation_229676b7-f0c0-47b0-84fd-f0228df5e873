<?php

use yii\db\Migration;

class m220404_225749_remember_me_token extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }
        $this->createTable('{{%remember_me_token}}', [
            'id' => $this->bigPrimaryKey()->unsigned(),
            'customers_id' => $this->integer()->notNull(),
            'token' => $this->string(255),
            'token_id' => $this->string(255),
            'login_ip' => $this->string(128),
            'login_country' => $this->string(2),
            'user_agent' => $this->string(255),
            'parsed_ua_os' => $this->string(255),
            'parsed_ua_browser' => $this->string(255),
            'expire' => $this->bigInteger(),
        ], $tableOptions);

        $this->createIndex('idx__customers_id', '{{%remember_me_token}}', ['customers_id']);
        $this->createIndex('idx__token_id', '{{%remember_me_token}}', ['customers_id']);
        $this->createIndex('idx__token', '{{%remember_me_token}}', ['customers_id']);
    }

    public function down()
    {
        $this->dropTable('{{%remember_me_token}}');
    }
}
