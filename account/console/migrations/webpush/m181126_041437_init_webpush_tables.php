<?php

use yii\db\Migration;

class m181126_041437_init_webpush_tables extends Migration
{
    public function up()
    {
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";

        if (!in_array('user', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%user}}', [
                    'id' => 'bigint(20) unsigned NOT NULL',
                    'client_id' => 'varchar(40) NOT NULL',
                    'topic_subscriptions' => 'text NULL',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_client_id', 'user', 'client_id');
            }
        }

        if (!in_array('client', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%client}}', [
                    'id' => 'varchar(40) NOT NULL',
                    'server_key' => 'varchar(255) NULL',
                    'sender_id' => 'varchar(255) NULL',
                    'status' => 'smallint(1) NOT NULL COMMENT \'Client Status, 0=Not-Active 1=Active\'',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'created_by' => 'varchar(255) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
            }
        }

        if (!in_array('token', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%token}}', [
                    'id' => 'varchar(255) NOT NULL',
                    'client_id' => 'varchar(40) NOT NULL',
                    'user_id' => 'bigint(20) unsigned NULL',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_user_id', 'token', 'user_id');
                $this->createIndex('idx_client_id', 'token', 'client_id');
            }
        }

        if (!in_array('topic', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%topic}}', [
                    'id' => 'bigint(20) NOT NULL AUTO_INCREMENT',
                    'client_id' => 'varchar(40) NOT NULL',
                    'type' => 'smallint(1) NOT NULL COMMENT \'0=Auto Generated 1=Manual Created\'',
                    'title' => 'varchar(125) NOT NULL',
                    'description' => 'varchar(255) NULL',
                    'code' => 'varchar(32) NOT NULL UNIQUE',
                    'topic_default' => 'smallint(1) NOT NULL COMMENT \'0=Other 1=Default\'',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'created_by' => 'varchar(255) NULL',
                    'token_counts' => 'int(11) unsigned NOT NULL DEFAULT \'0\'',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_client_id', 'topic', 'client_id');
            }
        }

        if (!in_array('notification', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%notification}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'client_id' => 'varchar(40) NOT NULL',
                    'type' => 'smallint(1) NOT NULL DEFAULT \'0\' COMMENT \'0=topic, 1=token\'',
                    'recipient' => 'varchar(225) NOT NULL',
                    'title' => 'varchar(255) NOT NULL',
                    'message' => 'varchar(255) NOT NULL',
                    'icon' => 'varchar(255) NULL',
                    'url' => 'varchar(255) NULL',
                    'click_action' => 'smallint(1) NOT NULL DEFAULT \'0\' COMMENT \'0=Auto hide, 1=Customer must click\'',
                    'image' => 'varchar(255) NULL',
                    'notification_status' => 'smallint(1) NOT NULL DEFAULT \'0\' COMMENT \'Notification Status 0=pending, 1=active\'',
                    'send_status' => 'smallint(1) NOT NULL DEFAULT \'0\' COMMENT \'Send Status 0=pending, 1=success, 2=failed\'',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'executed_at' => 'int(11) NULL',
                    'schedule_at' => 'int(11) NULL',
                    'created_by' => 'varchar(255) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_client_id', 'notification', 'client_id');
            }
        }

        if (!in_array('schedule', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%schedule}}', [
                    'push_id' => 'bigint(20) unsigned NOT NULL',
                    'push_status' => 'smallint(1) NOT NULL DEFAULT \'0\' COMMENT \'Send Status 0=pending, 1=processing\'',
                    'schedule_at' => 'int(11) NOT NULL',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'executed_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`push_id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_schedule_at', 'schedule', 'schedule_at');
            }
        }

        if (!in_array('api_log', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%api_log}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'push_id' => 'bigint(20) unsigned NOT NULL',
                    'client_id' => 'varchar(40) NOT NULL',
                    'api_endpoint' => 'varchar(255) NOT NULL',
                    'request' => 'text NULL',
                    'response' => 'text NULL',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_push_id', 'api_log', 'push_id');
                $this->createIndex('idx_client_id', 'api_log', 'client_id');
            }
        }

        // Insert OG client info from shasso to webpush db
        $time = time();
        $this->insert('{{%client}}', [
            'id' => 'ogm',
            'server_key' => 'AAAADO7z9d8:APA91bERIwFaI0KUhL-8yzmNT1kUdZI3IkGua11kdApLi0cOXcXj8NHuBvJ0vB2kfSNEP0ujqusgSrkIgwahRObt9hYgSA-dcVSm4l2kgJ0yrZlcLvj4L1_5LZ2_IdZHX9Fa_nvSYNTB',
            'sender_id' => '55548573151',
            'status' => 1,
            'created_at' => $time,
            'updated_at' => $time,
            'created_by' => 'system',
        ]);
    }

    public function down()
    {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `user`');
        $this->execute('DROP TABLE IF EXISTS `client`');
        $this->execute('DROP TABLE IF EXISTS `token`');
        $this->execute('DROP TABLE IF EXISTS `topic`');
        $this->execute('DROP TABLE IF EXISTS `notification`');
        $this->execute('DROP TABLE IF EXISTS `schedule`');
        $this->execute('DROP TABLE IF EXISTS `api_log`');
        $this->execute('SET foreign_key_checks = 1;');

        echo "m181126_041437_init_webpush_tables reverted.\n";
    }
}
