<?php

use yii\db\Migration;

class m190716_045832_upgrade_db extends Migration
{
    public function up()
    {
        $c = $this->getDb();
        // Alter database charset & collate to utf8mb4
        $_sql = "ALTER TABLE api_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;";
        $_sql .= "ALTER TABLE notification CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;";
        
        if ($c->createCommand($_sql)->execute()) {
            echo "\n[ SUCCESS ] webpush tables altered to utf8mb4'\n\n";
        } else {
            echo "\n[ FAIL ] query fail\n\n";
        }

        // add new column to store filter options for topic/segment
        $this->addColumn("topic", "topic_filter", "text");
    }

    public function down()
    {
        $c = $this->getDb();
        // Alter database charset & collate to utf8mb4
        $_sql = "ALTER TABLE api_log CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_ci;";
        $_sql .= "ALTER TABLE notification CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_ci;";
        
        if ($c->createCommand($_sql)->execute()) {
            echo "\n[ SUCCESS ] webpush tables altered to utf8'\n\n";
        } else {
            echo "\n[ FAIL ] query fail\n\n";
        }

        // drop column to store filter options for topic/segment
        $this->dropColumn("topic", "topic_filter");

        echo "m190716_045832_upgrade_db reverted.\n";
    }
}