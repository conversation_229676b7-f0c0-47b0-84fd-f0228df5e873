<?php

use yii\db\Migration;

class m200428_015455_webpush_segment_cron extends Migration
{
    public function up()
    {
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";

        if (!in_array('cron_topic_refresh', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_topic_refresh}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'client_id' => 'varchar(40) NOT NULL',
                    'topic_id' => 'bigint(20) NOT NULL',
                    'new_start_id'=> 'bigint(20) NOT NULL',
                    'cron_last_process_date' => 'int(11) NULL',
                    'cron_last_process_count' => 'int(11) NULL',
                    'cron_process_track_in_action' => 'tinyint(4)',
                    'cron_process_track_failed_attempt' => 'int(11) NULL',
                    'created_at' => 'int(11) NULL',
                    'created_by' => 'varchar(255) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_topic_id', 'cron_topic_refresh', 'topic_id');
            }
        }

        $this->addColumn('topic', 'refresh', 'tinyint(4)');
    }

    public function down()
    {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `cron_topic_refresh`');
        
        // drop column to store filter options for topic/segment
        $this->dropColumn('topic', 'refresh');

        $this->execute('SET foreign_key_checks = 1;');

        echo "m200428_015455_webpush_segment_cron reverted.\n";
    }
}
