<?php

use yii\db\Migration;

class m210416_064410_webpush_cron_track extends Migration
{
    public function up()
    {
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8mb4 COLLATE utf8mb4_bin ENGINE=InnoDB";

        if (!in_array('cron_process_track', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_process_track}}', [
                    'id' => $this->primaryKey(),
                    'cron_process_track_in_action' => 'tinyint(4)',
                    'cron_process_track_failed_attempt' => $this->integer()->notNull(),
                    'cron_process_track_start_date' => $this->integer()->unsigned()->notNull(),
                    'cron_process_track_filename' => $this->string(255),
                ], $tableOptions_mysql);
            }

            $this->insert('{{%cron_process_track}}', ['id' => '1', 'cron_process_track_in_action' => '0', 'cron_process_track_start_date' => time(), 'cron_process_track_failed_attempt' => '0', 'cron_process_track_filename' => 'WebpushSchedule']);
            $this->insert('{{%cron_process_track}}', ['id' => '2', 'cron_process_track_in_action' => '0', 'cron_process_track_start_date' => time(), 'cron_process_track_failed_attempt' => '0', 'cron_process_track_filename' => 'RefreshTopic']);
        }
    }

    public function down()
    {
        $this->execute('DROP TABLE IF EXISTS `cron_process_track`');

        echo "m210416_064410_webpush_cron_track reverted.\n";
    }
}
