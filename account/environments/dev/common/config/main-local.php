<?php
return [
    'aliases' => [
        '@freshdeskPrivateKey' => dirname(__FILE__) . '/freshdeskprivate.key',
    ],
    'components' => [
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['warning', 'error'],
                ],
            ],
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            'useFileTransport' => true,
        ],
        'cache' => [
            'keyPrefix' => "shasso-staging/",
            'servers' => [
                [
                    'host' => 'localhost',
                    'port' => 11211,
                ],
            ],
        ],
        'redis' => [
            'class' => 'yii\redis\Connection',
            'hostname' => 'localhost',
            'port' => 6379,
            'database' => 0,
        ],
        'geoip' => [
            'countryDbPath' => '@common/components/db/GeoLite2-Country.mmdb'
        ],
        'aws' => [
            'class' => '\common\components\AWS',
            'key' => '',
            'secret' => '',
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                // Bucket tag used by AssetManager
                'BUCKET_STATIC' => [
                    'bucket_key' => 'dev-s3-static-shasso',
                    'acl' => 'public-read',
                    'prefix_path' => 'account/app-assets',
                    'storage' => 'STANDARD',
                ],
                // Bucket tag used by UploadCom
                'BUCKET_UPLOAD' => [
                    'bucket_key' => 'dev-s3-upload',
                    'acl' => 'public-read',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
            ],
            'ses' => [],
            'sqs' => [
                'EINVOICE_PROFILE_UPDATE_QUEUE' => [
                    'queue_url' => '',
                    'region' => '',
                    'key' => '',
                    'secret' => '',
                ]
            ],
        ],
    ],
];
