<?php
return [
    # DB credentials
    'db.dsn' => '',
    'db.username' => '',
    'db.password' => '',
    'dbshasso.dsn' => '',
    'dbshasso.username' => '',
    'dbshasso.password' => '',
    'dbwebpush.dsn' => '',
    'dbwebpush.username' => '',
    'dbwebpush.password' => '',
    //
    'db.og.dsn' => '',
    'db.og.username' => '',
    'db.og.password' => '',
    //
    # Anti-fraud Neutrino configs
    'antifraud.neutrino.api.user_id' => 'OGM',
    'antifraud.neutrino.api.signature' => '5LueS4Ba2KNmHQsXKDW4iHnGqhii6NgG',
    //
    # Slack configuration
    // Default Slack App Incoming Hook ID
    'slack.id' => '',
    'slack.log.generic.secret' => '474fBKnxToGHPPZX',
    // pipwave Slack App Incoming Hook ID
    'slack.pw.id' => '',
    'slack.ekyc.id' => '',
    //
    # Sms provider configs
    'sms.clickatell.config' => [
        'global' => [
            'api_id' => '3384472',
            'send_id' => 'offgamers',
            'send_pwd' => 'eCRDEYVSeOcIDF',
        ],
        'us' => [
            'api_id' => '3400975',
            'send_id' => 'us.offgamers',
            'send_pwd' => 'pEo1tr6o2ra0',
            'extra' => [
                'from' => '17752374328',
                'mo' => '1',
            ],
        ],
    ],
    'pipwave.config' => [
        'api_key' => '',
        'api_secret' => '',
    ],
    //
    # Image
    'SECURE_KEY' => '2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8',
    'SECURE_KEY_IV' => '9S?C3B4WMQW6XM8P',
    //
    # reCaptcha
    "RECAPTCHA_CONFIG" => [
        'PUBLIC_KEY' => '6LfwC1kUAAAAAEx0g1pWYnuMI1QQ9yAPcVQYVINz',
        'PRIVATE_KEY' => '6LfwC1kUAAAAAG3iotdGFeTvutDCP8jZNOwhWPFU',
    ],
    "RECAPTCHA_V3_CONFIG" => [
        'PUBLIC_KEY' => '6LeCrasUAAAAAGO6OxA8Tb43lgujo3uovUFHLGTy',
        'PRIVATE_KEY' => '6LeCrasUAAAAANaXC1CBcxv_tJj6DoYxFadWBITU',
    ],
    //
    # Geetest
    "GEETEST_CONFIG" => [
        'PUBLIC_KEY' => '2405fdb536991cf25783224d85c36d11',
        'PRIVATE_KEY' => '2e81537251ef980ae6d3557e5f4bd86a',
    ],
    //
    # single sign-on
    "SSO_CONFIG" => [
        "SSO_SECRET_KEY" => "2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8",
    ],
    //
    "GIFT_CARD" => [
        "PRODUCT" => [
            "ogc" => [
                "api" => [
                    "merchant" => "OGM",
                    "secret" => "TESTOGM"
                ],
            ],
        ],
    ],

    # OG Microservice
    'OG_MICROSERVICE' => [
        'URL' => 'https://staging-ms-storecredit.offgamers.com',
        'KEY' => '',
        'SECRET' => '',
    ],
    //
    # pipwave configurations
    'pwapi.og_api_key' => '',
    'pwapi.og_api_secret' => '',
    //
    # Freshdesk configurations  
    'FRESHDESK_API' => [
        'TICKET_OG' => [
            'api_key' => '',
            'api_password' => '',
            'api_domain' => '',
        ],
        'SSO_OG' => [
            'support-offgamers.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],
            'offgamerssdnbhdassist.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],
            'cnsupport-offgamers.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],

        ],
    ],
    //
    'mailboxlayer.key' => '',
];