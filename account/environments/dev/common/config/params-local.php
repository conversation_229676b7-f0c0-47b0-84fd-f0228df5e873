<?php
return [
    # Proxy
    'USE_PROXY' => false,
    'PROXY_HOST' => 'my-proxy.offgamers.lan',
    'PROXY_PORT' => '3128',
    //
    # Anti-fraud Neutrino configs
    'antifraud.neutrino.api.url' => 'https://neutrinoapi.com',
    //
    # Slack configuration
    'slack.channel' => '#shasso-staging-log',
    'slack.log.generic.url' => 'https://staging-api.shasso.com/log/view?id=%s&timestamp=%s&token=%s',
    //
    # URLs
    'REGION_STATIC_DOMAIN' => 'http://staging.shasso.com/data/localization',
    'REGION_SECURE_STATIC_DOMAIN' => 'https://staging.shasso.com/data/localization',
    'SECURE_OUB_MYACCOUNT' => 'https://staging.shasso.com',
    //
    # OG
    'OG_URL' => 'https://staging.offgamers.com',
    'OG_ORDER_DETAIL_PAGE_URL' => 'https://staging.offgamers.com/account/purchase/order/%s',
    'OG_CREW_CUSTOMER_ORDERS_URL' => 'https://crew.offgamers.biz/orders.php?cID=%s&status=7',
    'OG_CREW_CUSTOMER_PROFILE_URL' => 'https://crew.offgamers.biz/customers.php?page=1&cID=%s&action=edit',
    'OG_CREW_ORDER_URL' => 'https://crew.offgamers.biz/orders.php?oID=%s&action=edit',
    //
    # cookie domain
    'COOKIE_DOMAIN' => '.staging.shasso.com',
    //
    # pagination
    "GENERAL_CONFIG" => [
        "DEBUG_RECEIPIENT" => "<EMAIL>", // alert to DEV
        "CS_RECIPIENT" => "<EMAIL>", // alert to CS
        "DOWN_FOR_MAINTENANCE" => false, // maintenance mode
    ],
    //
    # shasso mail to customer
    "SHASSO_MAIL_CONFIG" => [
        "SUBJECT_PREFIX" => "[OffGamers Authentication staging] ",
        "SENDER_NAME" => "OffGamers Authentication",
        "SENDER_EMAIL_ADDR" => "<EMAIL>"
    ],
    'EMAIL_SUBJECT_PREFIX' => "[OffGamers Authentication staging error] ",
    //
    "GIFT_CARD" => [
        "PRODUCT" => [
            "ogc" => [
                //"allowable_group" => "2,12,3,4,5,15",
                "api" => [
                    "url" => "https://api.offgamers.biz/ws/pin",
                ],
            ],
        ],
    ],
    'GTM_ID' => '',
    'API_MERCHANT_INFO' => [
        'ogm' => [
            'CS_EMAIL' => '<EMAIL>',
            'WEBPUSH_ICON' => 'https://d130xiciw9h9wz.cloudfront.net/banners/2/ogm%20logo%20icon_192x192px-20181210-022127.png', // HTTPS only
        ],
    ],
    'FIREBASE' => [
        'TOPIC_URL' => 'https://iid.googleapis.com/iid/v1',
        'PUSH_URL' => 'https://fcm.googleapis.com/fcm/send',
        'CRON_LIMIT' => 10,
        'CRON_TOPIC_REFRESH_LIMIT' => 1000,
    ],
    'pipwave.api_url' => 'https://staging-api.pipwave.com/',

    //
    # pipwave configurations
    'shassoapi.sdk_noti' => 'https://staging-api.shasso.com/pipsdk/notification',
    'pwapi.doc_url' => 'https://staging-api.pipwave.com/doc',
    'pwapi.sdk_url' => 'https://staging-static.pipwave.com/sdk/pipwaveDoc/v1/app.js',
    //
    //merchant order list url
    'og_order_list' => 'https://dev-www.offgamers.com/account/purchase',
    //merchant support url
    'og_support_url' => 'https://helpdesk.offgamers.com/support/tickets/new',
    //pipwave merchant analytic
    'pipwave_merchant_analytic' => 'https://staging-merchant.pipwave.com/reports/analytic/user/%s',
    # Paypal Login Info
    "paypal.login.login_url" => "https://www.sandbox.paypal.com/connect?flowEntry=static&client_id=%s&scope=%s&redirect_uri=%s",
    "paypal.login.accesstoken_api_url" => "https://api.sandbox.paypal.com/v1/oauth2/token",
    "paypal.login.userinfo_api_url" => "https://api.sandbox.paypal.com/v1/identity/oauth2/userinfo",
];
