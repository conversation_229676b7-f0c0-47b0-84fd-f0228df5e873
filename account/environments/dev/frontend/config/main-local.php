<?php

$config = [
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '',
        ],
        'assetManager' => [
            /* S3 config
            's3BucketTag' => 'BUCKET_STATIC',
            'appendTimestamp' => true,
            'basePath' => 's3://s3-static.shasso.biz/app-assets/' . $params['asset.path'],
            'baseUrl' => 'https://staging-static.shasso.com/app-assets/' . $params['asset.path'],
            'hashCallback' => function ($path) {
               return hash('md4', $path);
            }, //*/
            //* Local config
            'basePath' => '@webroot/assets',
            'baseUrl' => '@web/assets', //*/
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['warning', 'error'],
                ],
            ],
        ],
    ],
];

if (!YII_ENV_TEST) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
