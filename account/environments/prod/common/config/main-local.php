<?php
return [
    'aliases' => [
        '@freshdeskPrivateKey' => dirname(__FILE__) . '/freshdeskprivate.key',
    ],
    'components' => [
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'categories' => ['Exception', 'yii\web\HttpException:500'],
                    'levels' => ['error'],
                ],
            ],
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
        ],
        'cache' => [
            'servers' => [
                [
                    'host' => 'localhost',
                    'port' => 11211,
                ],
            ],
        ],
        'redis' => [
            'class' => 'yii\redis\Connection',
            'hostname' => 'localhost',
            'port' => 6379,
            'database' => 0,
        ],
        'geoip' => [
            'countryDbPath' => '@common/components/db/GeoLite2-Country.mmdb'
        ],
        'aws' => [
            'class' => '\common\components\AWS',
            'key' => '',
            'secret' => '',
            'version' => 'latest',
            'region' => '',
            's3' => [
                // Bucket tag used by AssetManager
                'BUCKET_STATIC' => [
                    'region' => 'us-east-1', // Remove line to use default AWS region
                    'bucket_key' => 's3-static.shasso.com',
                    'acl' => 'public-read',
                    'prefix_path' => 'app-assets',
                    'storage' => 'REDUCED_REDUNDANCY', //STANDARD|REDUCED_REDUNDANCY|STANDARD_IA|ONEZONE_IA
                ],
                // Bucket tag used by UploadCom
                'BUCKET_UPLOAD' => [
                    'region' => 'us-east-1',
                    'bucket_key' => 'BUCKET_UPLOAD',
                    'acl' => 'public-read',
                    'prefix_path' => '',
                    'endpoint' => 'http://localhost:32794',
                    'storage' => 'STANDARD', //STANDARD|REDUCED_REDUNDANCY|STANDARD_IA|ONEZONE_IA
                ],
            ],
            'ses' => [],
            'sqs' => [
                'EINVOICE_PROFILE_UPDATE_QUEUE' => [
                    'queue_url' => '',
                    'region' => '',
                    'key' => '',
                    'secret' => '',
                ]
            ],
        ],
    ],
];
