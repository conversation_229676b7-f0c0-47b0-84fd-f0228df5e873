<?php
return [
    # DB credentials
    'db.dsn' => '',
    'db.username' => '',
    'db.password' => '',
    'dbshasso.dsn' => '',
    'dbshasso.username' => '',
    'dbshasso.password' => '',
    'dbwebpush.dsn' => '',
    'dbwebpush.username' => '',
    'dbwebpush.password' => '',
    //
    'db.og.dsn' => '',
    'db.og.username' => '',
    'db.og.password' => '',
    //
    # Anti-fraud Neutrino configs
    'antifraud.neutrino.api.user_id' => 'OGM',
    'antifraud.neutrino.api.signature' => '5LueS4Ba2KNmHQsXKDW4iHnGqhii6NgG',
    //
    # Slack configuration
    // Default Slack App Incoming Hook ID
    'slack.id' => '',
    'slack.log.generic.secret' => 'NC5SAGbFcuxeXy67',
    // pipwave Slack App Incoming Hook ID
    'slack.pw.id' => '',
    'slack.ekyc.id' => '',
    //
    # Sms provider configs
    'sms.clickatell.config' => [
        'global' => [
            'api_id' => '3384472',
            'send_id' => 'offgamers',
            'send_pwd' => 'eCRDEYVSeOcIDF',
        ],
        'us' => [
            'api_id' => '3400975',
            'send_id' => 'us.offgamers',
            'send_pwd' => 'pEo1tr6o2ra0',
            'extra' => [
                'from' => '17752374328',
                'mo' => '1',
            ],
        ],
    ],
    'pipwave.config' => [
        'api_key' => '',
        'api_secret' => '',
    ],
    //
    # Image
    'SECURE_KEY' => '',
    'SECURE_KEY_IV' => '',
    //
    # reCaptcha
    "RECAPTCHA_CONFIG" => [
        'PUBLIC_KEY' => '',
        'PRIVATE_KEY' => '',
    ],
    "RECAPTCHA_V3_CONFIG" => [
        'PUBLIC_KEY' => '',
        'PRIVATE_KEY' => '',
    ],
    //
    # Geetest
    "GEETEST_CONFIG" => [
        'PUBLIC_KEY' => '',
        'PRIVATE_KEY' => '',
    ],
    //
    # single sign-on
    "SSO_CONFIG" => [
        "SSO_SECRET_KEY" => "",
    ],
    //
    "GIFT_CARD" => [
        "PRODUCT" => [
            "ogc" => [
                "api" => [
                    "merchant" => "",
                    "secret" => ""
                ],
            ],
        ],
    ],
    # OG Microservice
    'OG_MICROSERVICE' => [
        'URL' => 'https://ms-storecredit.offgamers.com',
        'KEY' => '',
        'SECRET' => '',
    ],

    //
    # pipwave configurations
    'pwapi.og_api_key' => '',
    'pwapi.og_api_secret' => '',
    //
    # Freshdesk configurations
    'FRESHDESK_API' => [
        'TICKET_OG' => [
            'api_key' => '',
            'api_password' => '',
            'api_domain' => '',
        ],
        'SSO_OG' => [
            'support-offgamers.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],
            'offgamerssdnbhdassist.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],
            'cnsupport-offgamers.freshdesk.com' => [
                'class' => '\common\components\freshdesk_merchant\FreshdeskOGApiCom',
                'enabled' => true,
                'api_key' => '',
                'api_password' => '',
                'api_domain' => '',
            ],

        ],
    ],
    //
    'mailboxlayer.key' => '',
];