<?php
return [
    # Proxy
    'USE_PROXY' => false,
    'PROXY_HOST' => 'my-proxy.offgamers.lan',
    'PROXY_PORT' => '3128',
    //
    # Anti-fraud Neutrino configs
    'antifraud.neutrino.api.url' => 'https://neutrinoapi.com',
    //
    # Slack configuration
    'slack.channel' => '#shasso-dev-log',
    'slack.log.generic.url' => 'https://api2.shasso.com/log/view?id=%s&timestamp=%s&token=%s',
    //
    # URLs
    'REGION_STATIC_DOMAIN' => 'http://account.offgamers.com/themes/myaccount_v2/data/localization',
    'REGION_SECURE_STATIC_DOMAIN' => 'https://account.offgamers.com/themes/myaccount_v2/data/localization',
    'SECURE_OUB_MYACCOUNT' => 'https://account.offgamers.com',
    //
    # OG
    'OG_URL' => 'http://www.offgamers.com',
    'OG_ORDER_DETAIL_PAGE_URL' => 'https://www.offgamers.com/account/purchase/order/%s',
    'OG_CREW_CUSTOMER_ORDERS_URL' => 'https://crew.offgamers.com/orders.php?cID=%s&status=7',
    'OG_CREW_CUSTOMER_PROFILE_URL' => 'https://crew.offgamers.com/customers.php?page=1&cID=%s&action=edit',
    'OG_CREW_ORDER_URL' => 'https://crew.offgamers.com/orders.php?oID=%s&action=edit',
    //
    # cookie domain
    'COOKIE_DOMAIN' => '.shasso.com',
    //
    # pagination
    "GENERAL_CONFIG" => [
        "DEBUG_RECEIPIENT" => "<EMAIL>", // alert to DEV
        "CS_RECIPIENT" => "<EMAIL>", // alert to CS
        "DOWN_FOR_MAINTENANCE" => false, // maintenance mode
    ],
    //
    # shasso mail to customer
    "SHASSO_MAIL_CONFIG" => [
        "SUBJECT_PREFIX" => "[OffGamers Authentication] ",
        "SENDER_NAME" => "OffGamers Authentication",
        "SENDER_EMAIL_ADDR" => "<EMAIL>"
    ],
    'EMAIL_SUBJECT_PREFIX' => "[OffGamers Authentication error] ",
    //
    "GIFT_CARD" => [
        "PRODUCT" => [
            "ogc" => [
                //"allowable_group" => "2,12,3,4,5,15",
                "api" => [
                    "url" => "",
                ],
            ],
        ],
    ],
    'GTM_ID' => '',
    'API_MERCHANT_INFO' => [
        'ogm' => [
            'CS_EMAIL' => '<EMAIL>',
            'WEBPUSH_ICON' => 'https://d130xiciw9h9wz.cloudfront.net/banners/2/ogm%20logo%20icon_192x192px-20181210-022127.png', // HTTPS only
        ],
    ],
    'FIREBASE' => [
        'TOPIC_URL' => 'https://iid.googleapis.com/iid/v1',
        'PUSH_URL' => 'https://fcm.googleapis.com/fcm/send',
        'CRON_LIMIT' => 10,
        'CRON_TOPIC_REFRESH_LIMIT' => 1000,
    ],
    'pipwave.api_url' => 'https://api.pipwave.com/',

    //
    # pipwave configurations
    'shassoapi.sdk_noti' => 'https://api.shasso.com/pipsdk/notification',
    'pwapi.doc_url' => 'https://api.pipwave.com/doc',
    'pwapi.sdk_url' => 'https://static.pipwave.com/sdk/pipwaveDoc/v1/app.js',
    //
    # Session config
    'session.provider' => 'ddb',

    //merchant order list url
    'og_order_list' => 'https://www.offgamers.com/account/purchase',
    //merchant support url
    'og_support_url' => 'https://helpdesk.offgamers.com/support/tickets/new',
    //pipwave merchant analytic
    'pipwave_merchant_analytic' => 'https://merchant.pipwave.com/reports/analytic/user/%s',
    # Paypal Login Info
    "paypal.login.login_url" => 'https://www.paypal.com/connect?flowEntry=static&client_id=%s&scope=%s&redirect_uri=%s',
    "paypal.login.accesstoken_api_url" => 'https://api.paypal.com/v1/oauth2/token',
    "paypal.login.userinfo_api_url" => 'https://api.paypal.com/v1/identity/oauth2/userinfo',

];
