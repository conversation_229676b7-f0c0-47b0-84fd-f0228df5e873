<?php
return [
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '',
        ],
        'assetManager' => [
            's3BucketTag' => 'BUCKET_STATIC',
            'appendTimestamp' => true,
            'basePath' => 's3://dev-s3-static-shasso/account/app-assets/' . $params['asset.path'],
            'baseUrl' => 'https://dev-static.offgamers.com/account/app-assets/' . $params['asset.path'],
            'hashCallback' => function ($path) {
               return hash('md4', $path);
            },
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error'],
                    'categories' => [
                        'yii\web\HttpException:500',
                    ],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error'],
                    'except' => [
                        'yii\web\HttpException:*',
                    ],
                ],
            ],
        ],
    ],
];
