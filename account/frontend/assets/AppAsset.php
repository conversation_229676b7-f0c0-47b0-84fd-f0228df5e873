<?php

namespace frontend\assets;

use yii\web\AssetBundle;

/**
 * Main frontend application asset bundle.
 */
class AppAsset extends AssetBundle
{
    public $sourcePath = '@media';
    public $css = [
        'css/main.css',
        'css/layout.css',
        'css/footer-og.css',
    ];
    public $js = [
        'javascript/main.js',
        'javascript/head.min.js',
        'javascript/activefield.tokeninput.js',
    ];
    public $depends = [
        'yii\web\YiiAsset',
        'frontend\assets\FontAwesomeAsset',
        'frontend\assets\ToastrAsset',
        'frontend\assets\FjallaOneFontAsset',
        'frontend\assets\CaptchaAsset',
    ];
    public $publishOptions = [
        'forceCopy' => YII_ENV == 'dev' ? true : false,
    ];
    
    public static function getUrl($path = '', $appendTimestamp = true) {
        if (empty($path)) {
            $appendTimestamp = false;
        }
        $bundle = \Yii::$app->assetManager->getBundle(static::className());
        $original_append_timestamp_setting = null;
        if (!$appendTimestamp) {
            $original_append_timestamp_setting = \Yii::$app->assetManager->appendTimestamp;
            \Yii::$app->assetManager->appendTimestamp = false;
        }
        $return_url = \Yii::$app->assetManager->getAssetUrl($bundle, $path);
        if ($appendTimestamp) {
            \Yii::$app->assetManager->appendTimestamp = \Yii::$app->assetManager->appendTimestamp;
        }
        return $return_url;
    }
}
