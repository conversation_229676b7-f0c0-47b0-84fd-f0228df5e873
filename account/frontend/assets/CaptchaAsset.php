<?php

namespace frontend\assets;

use yii\web\AssetBundle;

/**
 * Main frontend application asset bundle.
 */
class CaptchaAsset extends AssetBundle
{
    public $depends = [];
    
    public function init() {
        parent::init();
        if (\frontend\components\Captcha::getCaptchaProvider() == 'geetest') {
            $this->depends = [
                'frontend\assets\GeetestAsset',
            ];
        } else if (\frontend\components\Captcha::getCaptchaProvider() == 'captchacom') {
            $this->depends = [
                'frontend\assets\CaptchaComAsset',
            ];
        } else {
            $this->depends = [
                'frontend\assets\RecaptchaAsset',
            ];
        }
    }
}
