<?php

namespace frontend\assets;

use yii\web\AssetBundle;

class RecaptchaAsset extends AssetBundle {

    public $baseUrl = '@web';

    public function init() {
        parent::init();
        $v3_api_key = \frontend\components\GoogleRecaptcha::getV3PublicKey();
        if ($v3_api_key) {
            $this->js = [
                '//www.recaptcha.net/recaptcha/api.js?render=' . $v3_api_key,
            ];
        } else {
            $this->js = [
                '//www.recaptcha.net/recaptcha/api.js',
            ];
        }
    }

}
