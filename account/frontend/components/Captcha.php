<?php

namespace frontend\components;

use common\components\GeneralComponent;
use Yii;
use yii\helpers\Url;

class Captcha {

    private $captcha_obj;
    private $captcha_note;

    public function __construct($prefer_invisible = false) {
        $provider = static::getCaptchaProvider();
        if ($provider == 'geetest') {
            $this->captcha_obj = new captcha_providers\Geetest($prefer_invisible);
            $this->captcha_note = Yii::t('site', 'TEXT_GEETEST_NOTE');
        } else if ($provider == 'captchacom') {
            $this->captcha_obj = new captcha_providers\CaptchaCom($prefer_invisible);
            $this->captcha_note = Yii::t('site', 'TEXT_CAPTCHACOM_NOTE');
        } else {
            //Default - Recaptcha if none defined 
            $this->captcha_obj = new captcha_providers\Recaptcha($prefer_invisible);
            $this->captcha_note = !$prefer_invisible ? Yii::t('site', 'TEXT_RECAPTCHA_NOTE') : '';
        }
    }
    
    public static function getCaptchaProvider() {
        if (!isset(Yii::$app->session['captcha_provider'])) {
            $ip = GeneralComponent::getIPAddress();
            $countries_iso_code_2 = Yii::$app->geoip->countryCodeByIP($ip);
            //If user comes from region not supported by Recaptcha (eg: China)
            if (GoogleRecaptcha::isRegionSupported()) {
                $provider = 'recaptcha';
            } else {
                $provider = 'captchacom';
            }
            Yii::$app->session['captcha_provider'] = $provider;
        } else {
            $provider = Yii::$app->session['captcha_provider'];
        }
        return $provider;
    }

    public function load_html() {
        return array(
            'html' => $this->captcha_obj->load_html(),
            'js' => $this->captcha_obj->load_js(),
            'note' => $this->captcha_note,
        );
    }
    
    public function verify_response_raw($response) {
        return $this->captcha_obj->verify_response($response);
    }

    public function verify_response($response) {
        if ($this->verify_response_raw($response)) {
            unset(Yii::$app->session['need_captcha']);
            unset(Yii::$app->session['captcha_provider']);
            if (isset(Yii::$app->session['cc_clear_ip_ban'])) {
                $ip = GeneralComponent::getIPAddress();
                $cache_key = Yii::$app->session['cc_clear_ip_ban'] . $ip;
                Yii::$app->cache->delete($cache_key);
                unset(Yii::$app->session['cc_clear_ip_ban']);
            }

            $origin = (isset(Yii::$app->session['return_url']) ? Yii::$app->session['return_url'] : Url::to('/', true));
            unset(Yii::$app->session['return_url']);
            return $origin;
        } else {
            return false;
        }
    }

    /**
     * Check if after current invalid response, would captcha be necessary to be
     * displayed
     * 
     * @param string  $captchaType The type of checking to perform
     * @param array   $opts The options/params the captchaType takes in
     * 
     * ~ simpleReg<br>
     *   - Check if the user submitted form twice with different values quickly<br>
     *   - Options:<br>
     *     : value1 = First value to compare<br>
     *     : value2 = Second value to compare<br>
     *     : returnUrl = Where should user be redirected<br>
     *     : redirect = Should auto-redirect the user?<br>
     * ~ sessionCheck<br>
     *   - Check if the given session key has more than x attempts<br>
     *   - Options:<br>
     *     : sessionKey = Which session key to store number of attempts in<br>
     *     : maxTries = How many tries before captcha is activated<br>
     *     : returnUrl = Where should user be redirected<br>
     *     : redirect = Should auto-redirect the user?<br>
     * ~ ipCheck<br>
     *   - Check if the user's IP address has more than x attempts<br>
     *   - Options:<br>
     *     : cacheLifetime = Minutes lifetime of the cache for this IP<br>
     *     : cacheKeyPrefix = The prefix to append this IP to for caching<br>
     *     : maxTries = How many tries before captcha is activated<br>
     *     : checkOnly = Is check mode?<br>
     *     : returnUrl = Where should user be redirected<br>
     *     : redirect = Should auto-redirect the user?<br>
     *
     * @return boolean if the captcha has to be activated
     */
    public static function checkNeedCaptcha($captchaType, $opts = array()) {
        $requireCaptcha = false;
        switch ($captchaType) {
            case "simpleReg":
                if (isset(Yii::$app->session['need_captcha'])) {
                    $requireCaptcha = true;
                } else {
                    $thisRequireCaptcha = false;
                    $value1 = isset($opts['value1']) ? $opts['value1'] : null;
                    $value2 = isset($opts['value2']) ? $opts['value2'] : null;
                    if (isset(Yii::$app->session['sgstime'])) {
                        $startTime = Yii::$app->session['sgstime'];
                        $diff = time() - $startTime;
                        //check submit form before 5 secs and value has changed
                        if ($diff < 5 && isset($value1) && isset($value2) && ($value1 != $value2)) {
                            $thisRequireCaptcha = true;
                        }
                    } else {
                        // Cookie not exists probably is Bot
                        $thisRequireCaptcha = true;
                    }
                    if ($thisRequireCaptcha) {
                        $requireCaptcha = true;
                        Yii::$app->session['need_captcha'] = true;
                    }
                }
                break;
            case "sessionCheck":
                if (isset(Yii::$app->session['need_captcha'])) {
                    $requireCaptcha = true;
                } else {
                    $thisRequireCaptcha = false;
                    $sessionKey = isset($opts['sessionKey']) ? $opts['sessionKey'] : 'cc_trial';
                    $maxTries = isset($opts['maxTries']) ? $opts['maxTries'] : 3;
                    Yii::$app->session[$sessionKey] = (isset(Yii::$app->session[$sessionKey]) ? Yii::$app->session[$sessionKey] + 1 : 1);
                    $thisRequireCaptcha = Yii::$app->session[$sessionKey] >= $maxTries;
                    if ($thisRequireCaptcha) {
                        $requireCaptcha = true;
                        unset(Yii::$app->session[$sessionKey]);
                        Yii::$app->session['need_captcha'] = true;
                    }
                }
                break;
            case "ipCheck":
                $thisRequireCaptcha = false;
                $checkOnly = isset($opts['checkOnly']) ? $opts['checkOnly'] : false;
                $cacheLifetime = isset($opts['cacheLifetime']) ? $opts['cacheLifetime'] : \Yii::$app->params['SSO_CONFIG']['LOGIN_SAME_IP_LIFETIME'];
                $cacheKeyPrefix = isset($opts['cacheKeyPrefix']) ? $opts['cacheKeyPrefix'] : 'pvm_';
                $maxTries = isset($opts['maxTries']) ? $opts['maxTries'] : 5;
                $ip = GeneralComponent::getIPAddress();
                $cacheKey = $cacheKeyPrefix . $ip;
                $ipTrial = (int) Yii::$app->cache->get($cacheKey);
                if (!$checkOnly) {
                    $ipTrial ++;
                    Yii::$app->cache->set($cacheKey, $ipTrial, $cacheLifetime);
                }
                $thisRequireCaptcha = $ipTrial >= $maxTries;
                if ($thisRequireCaptcha) {
                    $requireCaptcha = true;
                    Yii::$app->session['cc_clear_ip_ban'] = $cacheKeyPrefix;
                }
                break;
            default:
            //Nothing
        }

        if ($requireCaptcha) {
            $returnUrl = isset($opts['returnUrl']) ? $opts['returnUrl'] : '';
            $redirect = !isset($opts['redirect']) || $opts['redirect'];
            if (!empty($returnUrl)) {
                Yii::$app->session['return_url'] = $returnUrl;
            }
            if ($redirect) {
                Yii::$app->response->redirect(['site/captcha']);
                Yii::$app->end();
            } else {
                return true;
            }
        }
        return false;
    }

}

abstract class CaptchaProvider {
    
    public abstract function load_html();
    
    public abstract function load_js();
    
    public abstract function verify_response($response);
}