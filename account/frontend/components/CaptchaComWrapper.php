<?php

namespace frontend\components {

    //Class implementation is a modified copy of `@vendor/captcha-com/captcha/botdetect-captcha-lib/botdetect.php`
    
    class CaptchaComWrapper extends \yii\base\Component {

        public function getCaptchaCom($clear_instances = false) {
            if ($clear_instances) {
                unset($_SESSION['BDC_pc_Default'], $_SESSION['BDC_CodeCollection_Default']);
            }
            $this->initCaptchaCom();
            $captcha = new \Captcha('Default');
            $captcha->SoundEnabled = false;
            $captcha->UserInputID = 'CaptchaCode';
            //Manually generate include script, otherwise there's race condition which fails the first load
            $captcha->AddScriptInclude = false;
            return $captcha;
        }
        
        public function serve() {
            $this->initCaptchaCom(false);
            //\Yii::$app->session['BDC_pc_Default']
            //Ignore warnings because... in vendor script got session_start...
            @require_once(BDC_INCLUDE_PATH . 'CaptchaHandler.php');
        }

        protected function initCaptchaCom($web = true) {
            if (defined('BDC_INCLUDE_PATH')) {
                return;
            }

            // 1. define BotDetect paths
            // physical path to Captcha library files (the BotDetect folder)
            $BDC_Include_Path = \Yii::getAlias('@vendor/captcha-com/captcha/botdetect-captcha-lib/botdetect/');

            // BotDetect Url prefix (base Url of the BotDetect public resources)
            $bundle = \Yii::$app->assetManager->getBundle(\frontend\assets\CaptchaComAsset::className());
            /* @var $bundle \yii\web\AssetBundle */
            if ($web) {
                $BDC_Url_Root = \yii\helpers\Url::to(\Yii::$app->assetManager->getAssetUrl($bundle, ''), true);
            } else {
                $BDC_Url_Root = \Yii::getAlias('@vendor/captcha-com/captcha/botdetect-captcha-lib/botdetect/public/');
            }

            // physical path to the folder with the (optional!) config override file
            $BDC_Config_Override_Path = __DIR__;

            // normalize paths
            // clean-up path specifications
            $BDC_Include_Path = BDC_NormalizePath($BDC_Include_Path);
            $BDC_Url_Root = BDC_NormalizePath($BDC_Url_Root);
            $BDC_Config_Override_Path = BDC_NormalizePath($BDC_Config_Override_Path);

            define('BDC_INCLUDE_PATH', $BDC_Include_Path);
            define('BDC_URL_ROOT', $BDC_Url_Root);
            define('BDC_CONFIG_OVERRIDE_PATH', $BDC_Config_Override_Path);

            // 2. include required base class declarations
            require_once(BDC_INCLUDE_PATH . 'CaptchaIncludes.php');

            // 3. include BotDetect configuration
            // a) mandatory global config, located in lib path
            require_once(BDC_INCLUDE_PATH . 'CaptchaConfigDefaults.php');

            // 4. determine is this file included in a form/class, or requested directly
            require_once(BDC_INCLUDE_PATH . 'CaptchaClass.php');
        }
    }
}

namespace {

    function BDC_NormalizePath($p_Path) {
        // replace backslashes with forward slashes
        $canonical = str_replace('\\', '/', $p_Path);
        // ensure ending slash
        $canonical = rtrim($canonical, '/');
        $canonical .= '/';
        return $canonical;
    }

    // b) optional config override
    function BDC_ApplyUserConfigOverride($CaptchaConfig, $CurrentCaptchaId) {
        $BotDetect = clone $CaptchaConfig;
        $BDC_ConfigOverridePath = BDC_CONFIG_OVERRIDE_PATH . 'CaptchaConfig.php';
        CaptchaConfiguration::ProcessGlobalDeclarations($BotDetect);
        //Manually overwrite any configurations here
        $BotDetect->HandlerUrl = \yii\helpers\Url::to(['captcha-com/index']);
        return $BotDetect;
    }

}