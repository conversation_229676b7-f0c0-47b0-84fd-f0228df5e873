<?php

namespace frontend\components;

use Yii;

class Chat
{

    public function __construct()
    {

    }

    public function getLiveChatContent($live_chat_string)
    {
        $params = [
            'token' => '6e44e18e-ce41-43c8-9dc0-0ed1cc68c6b4',
            'host' => 'https://wchat.freshchat.com',
            "config" => [
                "headerProperty" => [
                    "hideChatButton" => true
                ]
            ]
        ];
        $user_info = [];

        if (isset(Yii::$app->session['id']) && !empty(Yii::$app->session['id'])) {
            $user_info = [
                'externalId' => Yii::$app->session['id'],
                'firstName' => Yii::$app->session['firstname'],
                'lastName' => Yii::$app->session['lastname'],
                'email' => Yii::$app->session['email'],
                'customerGroupId' => Yii::$app->session['customers_groups_id'],
                'customerGroupName' => Yii::$app->session['ogm_group_name']
            ];
        }

        $live_chat_string = str_replace(
                "LIVE_CHAT_PARAMS", json_encode(array_merge($params, $user_info)), $live_chat_string
        );


        return $live_chat_string;
    }

    public function getFreshworksContent($fresh_works_string)
    {
        $user_info = [];
        if (Yii::$app->user->id) {
            $user_info = [
                'name' => Yii::$app->session['firstname'] . ' ' . Yii::$app->session['lastname'],
                'email' => Yii::$app->session['email'],
            ];
        }
        $fresh_works_string = str_replace("FRESH_WORKS_PARAMS", json_encode($user_info), $fresh_works_string);
        return $fresh_works_string;
    }

}
