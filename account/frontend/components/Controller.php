<?php

namespace frontend\components;

use Yii;
use yii\helpers\Url;

/**
 * Default Controller
 */
abstract class Controller extends \common\components\CamelCaseActionController {

    public $menu;

    /**
     * @inheritdoc
     * @ : authenticated user
     * ? : anonymous user
     * * : any user
     */
    public function behaviors() {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                        [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ]);
    }

    public function init() {
        parent::init();

        if (Yii::$app->params["GENERAL_CONFIG"]["DOWN_FOR_MAINTENANCE"] && !isset(Yii::$app->controller->module->id)) {
            if (Yii::$app->urlManager->parseUrl(Yii::$app->request) != 'site/maintenance') {
                Yii::$app->controller->redirect(array('site/maintenance'));
                Yii::$app->end();
            }
        }

        header('P3P:CP="This is not a P3P policy! See http://www.offgamers.com/privacy-policy-i-4.ogm for more info"');

        if (Yii::$app->request->isSecureConnection) {
            $recaptcha_config = Yii::$app->params['RECAPTCHA_CONFIG'];
            $recaptcha_config['RECAPTCHA_API_SERVER'] = Yii::$app->params['RECAPTCHA_CONFIG']['RECAPTCHA_API_SECURE_SERVER'];
            Yii::$app->params['RECAPTCHA_CONFIG'] = $recaptcha_config;
            Yii::$app->params['REGION_STATIC_DOMAIN'] = Yii::$app->params['REGION_SECURE_STATIC_DOMAIN'];
        }
        // set OGMAUTHV1 cookie  
        if (strpos(\Yii::$app->request->userAgent, 'ELB-HealthChecker') === false) { // if ELB healthcheck instance, no need to initialize regionalcom
            # regional setting
            RegionalCom::initial();
        }

        # update website global language
        if (isset(Yii::$app->session['lang_code'])) {
            Yii::$app->language = Yii::$app->session['lang_code'];
        }

        # check cache if current ip is ban, only guest users will get checked
        if (Yii::$app->user->isGuest) {
            $request = Yii::$app->urlManager->parseRequest(Yii::$app->request);
            if (array_shift($request) != 'site/captcha') {
                Captcha::checkNeedCaptcha('ipCheck', array(
                    'checkOnly' => true,
                    'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_SAME_IP'],
                ));
            }
        }
    }

    public function beforeAction($action) {
        if (parent::beforeAction($action)) {
            if (Yii::$app->user->isGuest) {
                return true;
            }

            return true;

            if ($this->id != 'sms-token' && $this->id != 'captcha-com' && !($this->id == 'sso' && $this->action->id == 'logout')) {
                $result = \common\models\CustomersSetting::getCustomersSetting(\common\models\CustomersSetting::SETUP_ACCOUT_KEY); // get from customers_setting instead

                if (!($this->id == 'sso' && ($this->action->id == 'setup' || $this->action->id == 'set-customers-setup' || $this->action->id == 'reclaim-mobile' || $this->action->id =='get-reclaim-mobile' ))) {
                    if (!$result) {
                        //User have not setup account, force user to setup
                        $f_query = \common\components\GeneralComponent::getRequestURI();

                        $this->redirect(Url::to(['sso/setup'], true) . '?' . $f_query);
                        \Yii::$app->end();
                    }

                } else {
                    if ($result) {
                        //User already setup account, cannot setup again
                        $f_query = \common\components\GeneralComponent::getRequestURI();
                        $this->redirect(Url::to(['sso/login-all'], true) .'?'. $f_query);
                        \Yii::$app->end();
                    }
                }
            }

            return true; // or false if needed
        } else {
            return false;
        }
    }

    protected function performAjaxValidation($model, $formId) {
        if ($model && $formId) {
            if (isset($_POST['ajax']) && $_POST['ajax'] === $formId) {
                echo \frontend\widgets\ActiveForm::validate($model);
                Yii::$app->end();
            }
        }
    }

}
