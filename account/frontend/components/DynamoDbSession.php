<?php
/**
 * <AUTHOR> K <vk<PERSON><PERSON><PERSON>@gmail.com>  
 * @package yii2-dynamodbsession
 * @version 1.0
 * @Support Version - Yii-2.0 >=
 * 
 * https://github.com/vkbala/yii2-dynamodb-session/blob/master/DynamoDbSession.php
 */

namespace frontend\components;

use Yii;
use yii\base\ErrorHandler;
use Aws\DynamoDb\DynamoDbClient;
use yii\web\ServerErrorHttpException;

class DynamoDbSession extends \yii\web\Session
{
    /**
     * @var DynamoDbClient
     */
    public $dynamoDb;

    public $params;

    public $sessionTable = 'sessions';

    /**
     * @var string DynamoDB table name
     */
    private $tableName = 'sessions';

    /**
     * @var string id key name
     */
    public $idColumn = 'id';

    /**
     * @var string user key name
     */
    public $userColumn = 'user_id';

    /**
     * @var string data key name
     */
    public $dataColumn="data";

    /**
     * @var string expire key name
     */
    public $expireColumn="expire";

    /**
     * @var string API retries
     */
    public $retries = 3;

    /**
     * @var string API connect timeout in seconds
     */
    public $connect_timeout = 3;

    /**
     * Initializes the Session component.
     * This method will initialize the [[db]] property to make sure it refers to a valid DynamoDB connection.
     * @throws InvalidConfigException if [[db]] is invalid.
     */
    public function init() {
        $this->dynamoDb = new \Aws\DynamoDb\DynamoDbClient([
            'version' => '2012-08-10',
            'region' => $this->params['region'],
            'credentials' => [
                'key' => $this->params['key'],
                'secret' => $this->params['secret'],
            ],
            'retries' => $this->retries,
            'http' => [
                'connect_timeout' => $this->connect_timeout
            ],
            'scheme' => 'http' //better performance for EC2 call DynamoDB
        ]);

        $this->tableName = $this->sessionTable;

        parent::init();        
    }

    /**
     * Returns a value indicating whether to use custom session storage.
     * This method overrides the parent implementation and always returns true.
     * @return boolean whether to use custom storage.
     */
    public function getUseCustomStorage()
    {
        return true;
    }  
    
    /**
     * Updates the current session ID with a newly generated one.
     * Please refer to <http://php.net/session_regenerate_id> for more details.
     * @param boolean $deleteOldSession Whether to delete the old associated session file or not.
     */
    public function regenerateID($deleteOldSession = false)
    {
        $oldId = session_id();

        parent::regenerateID(false);
        $newId = session_id();
        
        $row = $this->getData($oldId);
        try {
            if (!is_null($row)) {
                if ($deleteOldSession) { // Delete + Put = Update
                    $this->dynamoDb->deleteItem(array(
                        'TableName' => $this->tableName,
                        'Key' => array(
                            $this->idColumn => array('S' => (string) $oldId),
                        ),
                    ));
                    $this->dynamoDb->putItem(array(
                        'TableName' => $this->tableName,
                        'Item' => array(
                            $this->idColumn => array('S' => (string) $newId),
                            $this->dataColumn => $row[$this->dataColumn],
                            $this->expireColumn => $row[$this->expireColumn],
                        ),
                    ));
                } else {
                    $row[$this->idColumn] = array('S' => (string) $newId);
                    $this->dynamoDb->putItem(array(
                        'TableName' => $this->tableName,
                        'Item' => $row,
                    ));
                }
            } else {
                $this->dynamoDb->putItem(array(
                    'TableName' => $this->tableName,
                    'Item' => array(
                        $this->idColumn => array('S' => (string) $newId),
                        $this->expireColumn => array('N' =>  (string) $this->getExpireTime()),
                    ),
                ));
            }
        } catch (\Exception $ex) {
            \Yii::$app->reporter->reportToAdminViaSlack('Ddb Session regenerateID failed', $ex);
            throw new ServerErrorHttpException('Please try again later');
        }
    } 

    /**
     * Session read handler.
     * Do not call this method directly.
     * @param string $id session ID
     * @return string the session data
     */
    public function readSession($id)
    {
        $row = $this->getData($id);
        // return is_null($row) ? '' : $row[$this->dataColumn]['S'];
        if (!is_null($row)) {
            $expiry = $row[$this->expireColumn]['N'];
            if ($expiry > time()) {
                $data = $row[$this->dataColumn]['S'];
                return substr($data, 0, 6) === 'base64' ? base64_decode(substr($data, 6)) : $data;
            }
        }
        return '';
    }

    /**
     * Session write handler.
     * Do not call this method directly.
     * @param string $id session ID
     * @param string $data session data
     * @return boolean whether session write is successful
     */
    public function writeSession($id, $data)
    {
        $data = 'base64'.base64_encode($data);
        try {
            $sessionItem = array(
                $this->idColumn => array('S' => (string) $id),
                $this->dataColumn => array('S' => (string) $data),
                $this->expireColumn => array('N' => (string) $this->getExpireTime()),
            );
            if (\Yii::$app->user->id) {
                $sessionItem[$this->userColumn] = array('S' => (string) \Yii::$app->user->id);
            }

            $this->dynamoDb->putItem(array(
                'TableName' => $this->tableName,
                'Item' => $sessionItem
            ));
        } catch (\Exception $e) {
            $exception = ErrorHandler::convertExceptionToString($e);
            // its too late to use Yii logging here
            error_log($exception);
            echo "Please try again later";

            return false;
        }            
        return true; //@todo check return from put
    }  

    /**
     * Session destroy handler.
     * Do not call this method directly.
     * @param string $id session ID
     * @return boolean whether session is destroyed successfully
     */
    public function destroySession($id)
    {
        try {
            $this->dynamoDb->deleteItem(array(
                'TableName' => $this->tableName,
                'Key' => array(
                    $this->idColumn => array('S' => (string) $id),
                ),
            ));
        } catch (\Exception $ex) {
            \Yii::$app->reporter->reportToAdminViaSlack('Ddb Session destroySession failed', $ex);
            throw new ServerErrorHttpException('Please try again later');
        }
        
        return true; //@todo check return from put
    }

    /**
     * Session GC (garbage collection) handler.
     * Do not call this method directly.
     * @param integer $maxLifetime The number of seconds after which data will be seen as 'garbage' and cleaned up.
     * @return boolean whether session is GCed successfully
     */
    public function gcSession($maxLifetime)
    {
        //@TODO
        return true; //@todo check return from put
    }          

    public function getData($id, $retry = 0)
    {
        try {
            $r = $this->dynamoDb->getItem(array(
                'ConsistentRead' => true,
                'TableName' => $this->tableName,
                'Key' => array(
                    $this->idColumn => array('S' => (string) $id),
                ),
            ));
        } catch (\Exception $ex) {
            if ($retry > 0) {
                \Yii::$app->reporter->reportToAdminViaSlack('Ddb Session getData failed', $ex);
                throw new ServerErrorHttpException('Please try again later');
            }
            return $this->getData($id, $retry + 1);
        }

        return $r['Item'];
    }

    protected function getExpireTime()
    {
        return time() + $this->getTimeout();
    }
}
