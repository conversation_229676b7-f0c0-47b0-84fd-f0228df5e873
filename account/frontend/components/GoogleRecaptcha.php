<?php

namespace frontend\components;

class GoogleRecaptcha {
    
    public static function verifyToken($token, $private_key = null) {
        if (!$private_key) {
            $private_key = static::getV3PrivateKey();
        }
        $curl = new \common\components\CurlComponent();
        $postData = [
            'secret' => $private_key,
            'response' => $token
        ];
        $url = \Yii::$app->params['RECAPTCHA_CONFIG']['RECAPTCHA_API_SECURE_SERVER'];
        $response = $curl->curlPost($url, $postData);
        return json_decode($response, true);
    }
    
    public static function getV3PublicKey() {
        return !empty(\Yii::$app->params['RECAPTCHA_V3_CONFIG']['PUBLIC_KEY']) ? \Yii::$app->params['RECAPTCHA_V3_CONFIG']['PUBLIC_KEY'] : null;
    }
    
    public static function getV3PrivateKey() {
        return !empty(\Yii::$app->params['RECAPTCHA_V3_CONFIG']['PRIVATE_KEY']) ? \Yii::$app->params['RECAPTCHA_V3_CONFIG']['PRIVATE_KEY'] : null;
    }
    
    public static function isRegionSupported() {
        return true; //All goes to Recaptcha, even CN
        $countries_iso_code_2 = \Yii::$app->geoip->countryCodeByIP();
        return !in_array($countries_iso_code_2, array('CN'));
    }
}
