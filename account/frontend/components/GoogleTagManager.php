<?php

namespace frontend\components;

use Yii;
use yii\web\View;

class GoogleTagManager
{
    public $app_id;

    public function __construct()
    {
        $this->app_id = Yii::$app->params['GTM_ID'];
    }

    public function webSDK()
    {
        if ($this->app_id) {
            echo <<<HTML
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','$this->app_id');</script>
HTML;
        }
    }

    public function bodySDK()
    {
        if ($this->app_id) {
            echo <<<HTML
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=$this->app_id" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
HTML;
        }
    }

    public function login()
    {
        if ($this->app_id) {
            switch (Yii::$app->session->get("login_from")) {
                case "register":
                    $event = "sign_up";
                    break;
                case "login":
                    $event = "login";
                    break;
                default:
                    $event = "";
                    break;
            }

            $method = Yii::$app->session->get("login_method");
            if (($method == "sns") && isset($_SESSION["sns"]["login_method"])) {
                $method = $_SESSION["sns"]["login_method"];
            }

            if ($event && $method) {
                $js = <<<JS
                dataLayer.push({
                  event: "$event",
                  method: "$method"
                });
JS;

                Yii::$app->getView()->registerJs($js, View::POS_READY);
            }
        }
    }
}