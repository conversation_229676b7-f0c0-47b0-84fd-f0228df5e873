<?php

namespace frontend\components;

use common\components\EmailComponent;
use Exception;
use \Hybrid_Auth;
use \Hybrid_Endpoint;
use Yii;

/**
 * Implementation of HybridAuth (http://hybridauth.sourceforge.net/) into Yii.
 */
class HybridAuth extends \yii\base\Component {

    public $base_url;
    public $error_url;
    public $bindingDisplay;
    public $providers;
    public $debug_mode;
    public $debug_file;
    private $_hybridAuth;
    private $_libPath;
    private $sessionKeys = array('ha_provider', 'ha_uid', 'ha_photo', 'ha_email', 'ha_firstName', 'ha_lastName', 'ha_profileURL');
    public $email_notification_error_codes = array(1, 2, 4);
    public $exclude_countries_providers = array('44' => array('Facebook', 'Google', 'Twitter'));
    public $error;
    public $proxy;

    public function init() {
        parent::init();
        $this->_libPath = Yii::getAlias('@app/extensions/hybridauth');
        require_once($this->_libPath . '/Hybrid/Auth.php');

        if(!isset($_SESSION['state_token'])) {
            $_SESSION['state_token'] = md5(session_id());
        }

        foreach($this->providers as &$config) {
            foreach($config as $key => $val) {
                $lowkey = strtolower($key);
                if (strpos($lowkey, 'uri') !== false || strpos($lowkey, 'url') !== false) {
                    $config[$key] = \yii\helpers\Url::to($val, true);
                }
                // Set State Token & User Agent for Google
                elseif($key == 'state'){
                    $config[$key] = $_SESSION['state_token'].':'.(!isset($_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'],'gonative') === false ? 'normal' : 'gonative');
                }
            }
        }
    }

    public function endPoint() {
        require_once($this->_libPath . '/Hybrid/Endpoint.php');
        $this->getHybridAuth();
        Hybrid_Endpoint::process();
    }

    public function getConfig() {
        $config = array(
            'base_url' => \yii\helpers\Url::to($this->base_url, true), // URL for Hybrid_Auth callback
            'error_url' => \yii\helpers\Url::to($this->error_url, true), // URL for error cases
            'providers' => $this->providers,
            'debug_mode' => $this->debug_mode,
            'debug_file' => Yii::getAlias($this->debug_file),
            'hauth' => $this,
        );
        if (!empty($this->proxy))
            $config['proxy'] = $this->proxy;

        return $config;
    }

    public function isAllowedProvider($provider) {
        if (isset($this->providers[$provider]['enabled'])) {
            return $this->providers[$provider]['enabled'];
        }
        return false;
    }

    public function getAllowedProviders() {
        $providers = array();
        if (isset($this->providers)) {
            foreach ($this->providers as $provider => $config) {
                if (isset($config['enabled']) && $config['enabled'] && isset($config['display']) && $config['display']) {
                    if (!(isset($this->exclude_countries_providers[Yii::$app->session["ctry_id"]]) && in_array($provider, $this->exclude_countries_providers[Yii::$app->session["ctry_id"]]))) {
                        $providers[$provider] = $config;
                    }
                }
            }
        }
        return $providers;
    }

    public function getSession($key) {
        if (in_array($key, $this->sessionKeys)) {
            if (isset(Yii::$app->session[$key]))
                return Yii::$app->session[$key];
        }else {
            die('The session key:"' . $key . '" is not allowed by extension');
        }
    }

    public function setSession($key, $value) {
        if (in_array($key, $this->sessionKeys)) {
            Yii::$app->session[$key] = $value;
        } else {
            die('The session key:"' . $key . '" is not allowed by extension');
        }
    }

    public function unsetSession($key) {
        if (in_array($key, $this->sessionKeys)) {
            unset(Yii::$app->session[$key]);
        } else {
            die('The session key:"' . $key . '" is not allowed by extension');
        }
    }

    public function clearSession() {
        foreach ($this->sessionKeys as $key) {
            $this->unsetSession($key);
        }
    }

    public function clearHybridAuthError() {
        if (isset($this->providers)) {
            foreach ($this->providers as $provider => $config) {
                $id = strtolower($provider);
                Hybrid_Auth::storage()->delete("hauth_session.{$id}.error.status");
                Hybrid_Auth::storage()->delete("hauth_session.{$id}.error.message");
            }
        }
    }

    /**
     * Hybrid Auth functions
     */
    public function getHybridAuth() {
        if ($this->_hybridAuth === null) {
            return $this->_hybridAuth = new Hybrid_Auth($this->getConfig());
        }
        return $this->_hybridAuth;
    }

    public function getHybridAuthError() {
        $result = array();
        try {
            $message = json_decode($this->getHybridAuth()->getSessionData(), true);
        } catch (\Exception $ex) {
            return $this->errorMessage($ex);
        }
        if ($message) {
            foreach ($message as $key => $val) {
                if (preg_match("/\.error\.message/", $key)) {
                    $result[] = trim($val, '"');
                }
            }
        }
        return $result;
    }

    public function login() {
        try {
            if (isset($_GET['ha_provider']) && $_GET['ha_provider'] != "") {
                $provider = $_GET['ha_provider'];
            } else {
                throw new Exception('Provider is not available in the GET parameter', 101);
            }

            if (isset($this->providers) && isset($this->providers[$provider]) && $this->isAllowedProvider($provider)) {
                $adapter = $this->getHybridAuth()->authenticate($provider);
                $userProfile = $adapter->getUserProfile();
                if ($userProfile) {
                    $this->setSession('ha_provider', $provider);
                    $this->setSession('ha_uid', (string) $userProfile->identifier);
                    $this->setSession('ha_photo', $userProfile->photoURL);
                    $this->setSession('ha_email', $userProfile->email);
                    $this->setSession('ha_firstName', $userProfile->firstName);
                    $this->setSession('ha_lastName', $userProfile->lastName);
                    $this->setSession('ha_profileURL', $userProfile->profileURL);
                }

                if (isset($this->providers)) {
                    foreach ($this->providers as $_provider => $_config) {
                        if (($_provider != $provider) && $this->isAllowedProvider($_provider)) {
                            $adapter = $this->getHybridAuth()->getAdapter($_provider);
                            $adapter->logout();
                        }
                    }
                }
                return true;
            }
        } catch (Exception $e) {
            $this->errorMessage($e);
            return false;
        }
    }

    public function logout($provider = "") {
        try {
            if (isset($_GET['ha_provider']) && $_GET['ha_provider'] != "") {
                $provider = $_GET['ha_provider'];
            }

            if ($provider != "" && $this->isAllowedProvider($provider)) {
                if ($provider == $this->getSession('ha_provider')) {
                    foreach ($this->sessionKeys as $key) {
                        $this->unsetSession($key);
                    }
                }
                $adapter = $this->getHybridAuth()->getAdapter($provider);
                $adapter->logout();
            } else {
                $this->clearSession();
            }

            return true;
        } catch (Exception $e) {
            $this->errorMessage($e);
            return false;
        }
    }

    public function errorMessage($e) {
        $orgError = '';
        switch ($e->getCode()) {
            case 0 : $orgError = "Unspecified error.";
                break;
            case 1 : $orgError = "System error."; # Hybriauth configuration error
                break;
            case 2 : $orgError = "Provider not properly configured.";
                break;
            case 3 : $orgError = "Unknown or disabled provider.";
                break;
            case 4 : $orgError = "Missing provider application credentials.";
                break;
            case 5 : $orgError = "Authentication failed. The user has canceled the authentication.";
                break;
            case 6 : $orgError = "User profile request failed. Most likely the user is not connected to the provider and he should to authenticate again.";
                break;
            case 7 : $orgError = "User not connected to the provider.";
                break;
            case 8 : $orgError = "Provider does not support this feature.";
                break;
            case 101 : $orgError = "Provider missing";
                break;
            case 102 : $orgError = "Identifier missing";
                break;
            default:
                $orgError = "Failed to connect. Please try again.";
        }

        if (in_array($e->getCode(), $this->email_notification_error_codes)) {
            $error = $orgError;
            $error .= "<br /><br /><b>Original error message:</b> " . $e->getMessage();
            $error .= "<hr /><pre>Trace:<br />" . $e->getTraceAsString() . "</pre>";

            $message = "<pre>";
            $message .= "<br>================================================Error================================================<br>";
            $message .= $error;
            $message .= "<br>========================================================================================================<br>";
            $message .= "<br>================================================_REQUEST================================================<br>";
            unset($_REQUEST['Customers']['customers_password']);
            $message .= print_r($_REQUEST, true);
            $message .= "<br>========================================================================================================<br>";
            $message .= "<br>================================================_SERVER================================================<br>";
            $message .= print_r($_SERVER, true);
            $message .= "<br>========================================================================================================<br>";

            $subject = 'HybridAuth Error - ' . date("F j, Y H:i");
            $headers = 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
            $headers .= 'From: ' . Yii::$app->name . ' REPORTER <<EMAIL>>' . "\r\n";
            EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $message, $headers);
            
            \Yii::$app->reporter->reportToAdminViaSlack("Hybridauth failed", $e);
        }

        $this->error = '(#' . $e->getCode() . ') ' . $orgError;
        $this->getHybridAuth()->logoutAllProviders();
    }

}