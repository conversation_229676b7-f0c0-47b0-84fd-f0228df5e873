<?php

namespace frontend\components;

use common\components\CurlComponent;
use common\models\ShassoUserPreference;
use Yii;
use yii\helpers\Json;
use yii\web\Cookie;

class RegionalCom {

    public static function initial() {
        $retry = false;

        $ctry_code = (isset(Yii::$app->request->cookies['reg']->value['ctry']) && isset(Yii::$app->session["ctry_code"]) ? true : false);
        $cur_code = (isset(Yii::$app->request->cookies['reg']->value['cur']) && isset(Yii::$app->session["cur_code"]) ? true : false);
        $lang_code = (isset(Yii::$app->request->cookies['reg']->value['lang']) && isset(Yii::$app->session["lang_code"]) ? true : false);

        if (!$ctry_code || !$cur_code || !$lang_code) {
            $retry = 1;
        }
        
        while ($retry) {
            switch ($retry) {
                case 1:
                    $ctry_code = (isset(Yii::$app->request->cookies['reg']->value['ctry']) ? Yii::$app->request->cookies['reg']->value['ctry'] : (isset(Yii::$app->session["ctry_code"]) ? Yii::$app->session["ctry_code"] : ""));
                    $cur_code = (isset(Yii::$app->request->cookies['reg']->value['cur']) ? Yii::$app->request->cookies['reg']->value['cur'] : (isset(Yii::$app->session["cur_code"]) ? Yii::$app->session["cur_code"] : ""));
                    $lang_code = (isset(Yii::$app->request->cookies['reg']->value['lang']) ? Yii::$app->request->cookies['reg']->value['lang'] : (isset(Yii::$app->session["lang_code"]) ? Yii::$app->session["lang_code"] : ""));
                    $retry++;
                    break;

                case 2:
                    $ctry_code = Yii::$app->geoip->countryCodeByIP();
                    if (empty($ctry_code)) {
                        $ctry_code = Yii::$app->params["REG_CONFIG"]["DEF_COUNTRY_CODE"];
                    }
                    $retry++;
                    break;

                default:
                    $ctry_code = Yii::$app->params['REG_CONFIG']['DEF_COUNTRY_CODE'];
                    $retry = false;
                    break;
            }

            if (!empty($ctry_code)) {
                $cookieDataArray = array(
                    'reg_ctry' => $ctry_code,
                    'reg_cur' => $cur_code,
                    'reg_lang' => $lang_code
                );
                $result = self::_validateRegionalData($cookieDataArray);
                if (!empty($result) && $result["ctry_id"] && $result["ctry_code"] && $result["cur_code"] && $result["lang_code"]) {
                    self::updateCookie($result);
                    self::updateSession($result);
                    self::updateDb();
                    $retry = false;
                }
            }
        }
    }

    public static function updateSession($_input) {
        Yii::$app->language = $_input["lang_code"];

        Yii::$app->session["ctry_id"] = $_input["ctry_id"];        // 129
        Yii::$app->session["ctry_code"] = $_input["ctry_code"];    // MY
        Yii::$app->session['cur_code'] = $_input["cur_code"];      // USD
        Yii::$app->session['lang_code'] = $_input["lang_code"];    // en
        Yii::$app->session['lang_name'] = $_input["lang_name"];    // en
    }

    public static function updateCookie($_input) {
        $cookieExpiry = time() + 60 * 60 * 24 * 365;

        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'reg[ctry]',
            'value' => $_input["ctry_code"],
            'expire' => $cookieExpiry,
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'reg[cur]',
            'value' => $_input["cur_code"],
            'expire' => $cookieExpiry,
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'reg[lang]',
            'value' => $_input["lang_code"],
            'expire' => $cookieExpiry,
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
    }

    public static function updateDB() {
        if (isset(Yii::$app->user->id)) {
            $m_attr = array(
                'country' => Yii::$app->session["ctry_code"],
                'currency' => Yii::$app->session['cur_code'],
                'language' => Yii::$app->session['lang_code']
            );
            foreach ($m_attr as $key => $val) {
                ShassoUserPreference::model()->updateUserPreference(Yii::$app->user->id, $key, $val);
            }
            unset($m_attr);
        }
    }

    private static function _getDefaultLanguage($postDataArray, $regionJsonData) {
        $lang_code = $lang_name = "";
        
        if (!empty($regionJsonData)) {
            if (isset($regionJsonData[$postDataArray['reg_ctry']]['language']['def']) && !empty($regionJsonData[$postDataArray['reg_ctry']]['language']['def'])) {
                $lang_code = $regionJsonData[$postDataArray['reg_ctry']]['language']['def'];
            } else if (isset($postDataArray['reg_lang']) && isset($regionJsonData['default']['language']['val'][$postDataArray['reg_lang']])) {
                $lang_code = $postDataArray['reg_lang'];
            }
        }

        if (empty($lang_code)) {
            $lang_code = Yii::$app->params['REG_CONFIG']['DEF_LANGUAGE_CODE'];
            $lang_name = Yii::$app->params['REG_CONFIG']['DEF_LANGUAGE_NAME'];
        } else {
            $lang_name = $regionJsonData['refer']['language'][$lang_code];
        }
        return array(
            'lang_code' => $lang_code,
            'lang_name' => $lang_name
        );
    }

    private static function _getDefaultCurrency($postDataArray, $regionJsonData) {
        $currency = "";

        if (!empty($regionJsonData)) {
            if (isset($regionJsonData[$postDataArray['reg_ctry']]['currency']['def']) && !empty($regionJsonData[$postDataArray['reg_ctry']]['currency']['def'])) {
                $currency = $regionJsonData[$postDataArray['reg_ctry']]['currency']['def'];
            } else if (isset($postDataArray['reg_cur']) && isset($regionJsonData['default']['currency']['val'][$postDataArray['reg_cur']])) {
                $currency = $postDataArray['reg_cur'];
            }
        }

        if (empty($currency)) {
            $currency = Yii::$app->params['REG_CONFIG']['DEF_CURRENCY_CODE'];
        }
        return $currency;
    }

    public static function _validateRegionalData($postDataArray) {
        $regionalSettingData = array();
        $curl_obj = new CurlComponent();

        $countryCurlResult = $curl_obj->curlGet(Yii::$app->params['REGION_STATIC_DOMAIN'] . '/country.json');
        $countryJsonData = Json::decode(preg_replace('/.+?({.+}).+/', '$1', $countryCurlResult));

        $regionCurlResult = $curl_obj->curlGet(Yii::$app->params['REGION_STATIC_DOMAIN'] . '/region.json');
        $regionJsonData = Json::decode(preg_replace('/.+?({.+}).+/', '$1', $regionCurlResult));

        // Check POST country code is found in country json
        if (!empty($countryJsonData) && !empty($regionJsonData)) {
            if (!isset($postDataArray['reg_ctry']) && isset($postDataArray['reg_ctry_id'])) {
                foreach($countryJsonData as $ctry_code => $country) {
                    if ($postDataArray['reg_ctry_id'] == $country['id']) {
                        $postDataArray['reg_ctry'] = $ctry_code;
                        break;
                    }
                }
            }
            if (isset($countryJsonData[$postDataArray['reg_ctry']])) {
                $regionalSettingData['ctry_id'] = $countryJsonData[$postDataArray['reg_ctry']]['id'];
                $regionalSettingData['ctry_code'] = $postDataArray['reg_ctry'];

                // Check POST language id is valid
                if (isset($postDataArray['reg_lang']) && isset($regionJsonData[$postDataArray['reg_ctry']]['language']['val'][$postDataArray['reg_lang']])) {
                    $regionalSettingData['lang_code'] = $postDataArray['reg_lang'];
                    $regionalSettingData['lang_name'] = $regionJsonData['refer']['language'][$regionalSettingData['lang_code']];
                } else {
                    $language = self::_getDefaultLanguage($postDataArray, $regionJsonData);
                    $regionalSettingData['lang_code'] = $language['lang_code'];
                    $regionalSettingData['lang_name'] = $language['lang_name'];
                }

                // Check POST currency is valid
                if (isset($postDataArray['reg_cur']) && isset($regionJsonData[$postDataArray['reg_ctry']]['currency']['val'][$postDataArray['reg_cur']])) {
                    $regionalSettingData['cur_code'] = $postDataArray['reg_cur'];
                } else {
                    $regionalSettingData['cur_code'] = self::_getDefaultCurrency($postDataArray, $regionJsonData);
                }
            }
        }

        if (empty($regionalSettingData)) {
            // When saving Country from geoip not found in json, set to config defautlt
            $ctry_code = Yii::$app->params['REG_CONFIG']['DEF_COUNTRY_CODE'];
            $ctry_id = Yii::$app->params['REG_CONFIG']['DEF_COUNTRY_ID'];

            // get def language and currency based on country code
            $language = self::_getDefaultLanguage(array('reg_ctry' => $ctry_code), $regionJsonData);
            $currency = self::_getDefaultCurrency(array('reg_ctry' => $ctry_code), $regionJsonData);

            $regionalSettingData = array(
                "ctry_id" => $ctry_id,
                "ctry_code" => $ctry_code,
                "cur_code" => $currency,
                "lang_code" => $language['lang_code'],
                "lang_name" => $language['lang_name'],
            );
        }

        return $regionalSettingData;
    }

    public static function getCurrenciesForCountry($ctry_code = null, $regionJsonData = null) {
        if (!$ctry_code) {
            $ctry_code = Yii::$app->request->cookies['reg']->value['ctry'];
        }
        if (!$regionJsonData) {
            $curl_obj = new CurlComponent();
            $regionCurlResult = $curl_obj->curlGet(Yii::$app->params['REGION_STATIC_DOMAIN'] . '/region.json');
            $regionJsonData = Json::decode(preg_replace('/.+?({.+}).+/', '$1', $regionCurlResult));
        }
        $currencies = [];
        $regionalData = !isset($regionJsonData[$ctry_code]['currency']) ? $regionJsonData['default']['currency'] : $regionJsonData[$ctry_code]['currency'];
        foreach($regionalData['val'] as $cur_code => $empty_string) {
            if (isset($regionJsonData['refer']['currency'][$cur_code])) {
                $currencies[$cur_code] = $regionJsonData['refer']['currency'][$cur_code];
            }
        }
        return $currencies;
    }
}