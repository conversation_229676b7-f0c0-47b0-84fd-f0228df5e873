<?php

namespace frontend\components;

class TestGiftCard extends \common\components\gift_cards\AbstractGiftCard {
    
    public function actualRedeem($serial, $pin) {
        //Fail-safe
        if (YII_ENV != 'dev') {
            return [
                'status' => static::ERROR_REDEEM_FAIL,
                'result' => [],
                'error' => '',
            ];
        }
        if (strlen($serial) == 3 && is_numeric($pin * 1)) {
            return [
                'status' => true,
                'result' => [
                    'deno' => $pin * 1,
                    'currency' => $serial,
                ],
                'error' => '',
            ];
        } else {
            return [
                'status' => static::ERROR_REDEEM_FAIL,
                'result' => [],
                'error' => '',
            ];
        }
    }
    
    public function actualValidatePin($serial, $pin) {
        if (strlen($serial) == 3 && is_numeric($pin * 1)) {
            return [
                'status' => true,
                'result' => [
                    'deno' => $pin * 1,
                    'currency' => $serial,
                ],
                'error' => '',
            ];
        } else {
            return [
                'status' => static::ERROR_REDEEM_FAIL,
                'result' => [],
                'error' => '',
            ];
        }
    }
}
