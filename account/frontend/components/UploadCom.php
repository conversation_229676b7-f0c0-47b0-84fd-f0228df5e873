<?php

namespace frontend\components;

use common\components\GeneralComponent;
use common\components\OgmAmazonWs;
use Yii;

class UploadCom {

    public $onlyAllowedType = true;
    public $permissions = "0775";
    public $encrypt_file = false;
    var $file, $filename, $destination, $extensions, $imgsize, $maxsize, $s3_bucket, $output_messages, $upload_destination, $upload_destination_filename;

    public function __construct($file = '') {
        $this->set_file($file);
    }

    public function parse($file=null) {

        //check for file error first
        if (isset($_FILES[$this->file])) {
            if ((isset($_FILES[$this->file]['error']) && $_FILES[$this->file]['error'] != 0) || is_array($_FILES[$this->file]['error'])) {
                \Yii::$app->reporter->reportToAdminViaSlack($this->file, $_FILES);
                $this->set_output_messages(json_encode($_FILES[$this->file]['error']), 'error');
                return false;
            }
        }

        if($file == null ){

            if (isset($_FILES[$this->file])) {
            $file = array(
                'name' => $_FILES[$this->file]['name'],
                'type' => $_FILES[$this->file]['type'],
                'size' => $_FILES[$this->file]['size'],
                'tmp_name' => $_FILES[$this->file]['tmp_name']
                );
            }

            if(!is_uploaded_file($file['tmp_name'])){
                $this->set_output_messages(Yii::t('general', 'WARNING_NO_FILE_UPLOADED'), 'warning');
                return false;
            }

        }

        if (!empty($file['tmp_name']) && ($file['tmp_name'] != 'none') ) {
            if ($file["size"] > 0) {
                if (sizeof($this->extensions) > 0) {
                    $file_type = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
                    //  get file mime for most type instead of just image
                    $finfo = finfo_open(FILEINFO_MIME_TYPE);
                    $file_mime = finfo_file($finfo,$file['tmp_name']);
                    finfo_close($finfo);

                    if (!in_array($file_type, $this->extensions)) {
                        $this->set_output_messages(Yii::t('general', 'ERROR_FILETYPE_NOT_ALLOWED'), 'error');
                        return false;
                    } else if ($this->onlyAllowedType && strpos($file_mime, 'exe') == true) {
                        $this->set_output_messages(Yii::t('general', 'ERROR_FILETYPE_NOT_ALLOWED'), 'error');
                        return false;
                    } else if (!empty($this->maxsize)) {
                        if ($file["size"] > $this->maxsize) {
                            $this->set_output_messages(Yii::t('general', 'ERROR_FILESIZE_EXCEED'), 'error');
                            return false;
                        }
                    }
                    unset($file_mime);
                }

                if ($this->encrypt_file) {
                    $this->encryption($file);
                }
                return true;
            } else {
                $this->set_output_messages(Yii::t('general', 'ERROR_NO_UPLOAD_FILE'), 'error');
                return false;
            }
        } else {
            $this->set_output_messages(Yii::t('general', 'WARNING_NO_FILE_UPLOADED'), 'warning');
            return false;
        }
    }

    public function save($file=null) {
        if (!empty($this->file) && !empty($this->destination)) {
            if ($this->parse($file) == true) {
                $aws_obj = new OgmAmazonWs();
                if ($aws_obj->is_aws_s3_enabled()) {
                    $aws_obj->set_bucket_key($this->s3_bucket);

                    $tmp_name = ($file == null) ? $_FILES[$this->file]['tmp_name'] : $file['tmp_name'];
                    $aws_obj->set_file(array('tmp_name' => $tmp_name));
                    $aws_obj->set_filename($this->filename);
                    $aws_obj->set_filepath($this->destination);

                    if ($aws_obj->save_file()) {
                        return true;
                    } else {
                        $this->set_output_messages(Yii::t("general", "ERROR_FILE_NOT_SAVED"), 'error');
                    }
                } else {
                    if (!empty($this->upload_destination)) {
                        $this->set_destination($this->upload_destination);
                        $this->set_filename($this->upload_destination_filename);
                    }

                    if ($this->check_destination()) {
                        if (file_exists($this->destination . $this->filename)) {
                            @unlink($this->destination . $this->filename);
                        }

                        if (move_uploaded_file($_FILES[$this->file]['tmp_name'], $this->destination . $this->filename)) {
                            @chmod($this->destination . $this->filename, $this->permissions);
                            return true;
                        } else {
                            $this->set_output_messages(Yii::t("general", "ERROR_FILE_NOT_SAVED"), 'error');
                        }
                    }
                }
            }
        }

        return false;
    }

    public function encryption($file) {
        $filePath = $file['tmp_name'];
        $fp = fopen($file['tmp_name'], 'r') or die("can't open file");
        $file_content = fread($fp, filesize($file['tmp_name']));
        fclose($fp);

        $file_content = GeneralComponent::encryptData(base64_encode($file_content));

        $fp = fopen($file['tmp_name'], 'w') or die("can't open file");
        fwrite($fp, $file_content);
        fclose($fp);
    }

    public function set_bucket($s3_bucket) {
        $this->s3_bucket = $s3_bucket;
    }

    public function set_encryption($encrypt_file) {
        $this->encrypt_file = $encrypt_file;
    }

    public function set_file($file) {
        $this->file = $file;
    }

    public function set_imgsize($imgsize) {
        $this->imgsize = $imgsize;
    }

    public function set_maxsize($maxsize) {
        $this->maxsize = $maxsize;
    }

    public function set_destination($destination) {
        $this->destination = $destination;
    }

    public function set_upload_destination($destination) {
        $this->upload_destination = $destination;
    }

    public function set_upload_destination_filename($filename) {
        $this->upload_destination_filename = $filename;
    }

    public function set_permissions($permissions) {
        $this->permissions = octdec($permissions);
    }

    public function set_filename($filename) {
        $this->filename = $filename;
    }

    public function set_extensions($extensions) {
        if (!empty($extensions)) {
            if (is_array($extensions)) {
                $this->extensions = $extensions;
            } else {
                $this->extensions = array($extensions);
            }
        } else {
            $this->extensions = array();
        }

        foreach ($this->extensions as $ext) {
            if (!in_array($ext, array('jpg', 'jpeg', 'gif', 'png','pdf')))
                $this->onlyAllowedType = false;
        }
    }

    public function check_destination() {
        if (!is_writeable($this->destination)) {
            if (is_dir($this->destination)) {
                $this->set_output_messages(Yii::t('general', 'ERROR_DESTINATION_NOT_WRITEABLE'), 'error');
            } else {
                $this->set_output_messages(Yii::t('general', 'ERROR_DESTINATION_DOES_NOT_EXIST'), 'error');
            }
            return false;
        } else {
            return true;
        }
    }

    public function set_output_messages($message, $type) {
        $this->output_messages[] = array('message' => $message, 'type' => $type);
    }

    public function get_output_messages() {
        return $this->output_messages;
    }

}