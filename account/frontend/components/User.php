<?php

namespace frontend\components;

class User extends \yii\web\User {
    
    public function __get($name) {
        try {
            return parent::__get($name);
        } catch (\yii\base\UnknownPropertyException $ex) {
            if ($this->getIdentity() !== null && isset(\Yii::$app->session[$name])) {
                return \Yii::$app->session[$name];
            } else {
                throw $ex;
            }
        }
    }
    
    public function __set($name, $value) {
        try {
            parent::__set($name, $value);
        } catch (\yii\base\UnknownPropertyException $ex) {
            if ($this->getIdentity() !== null && isset(\Yii::$app->session[$name])) {
                \Yii::$app->session[$name] = $value;
            } else {
                throw $ex;
            }
        }
    }
    
    public function __isset($name) {
        if (!parent::__isset($name)) {
            return $this->getIdentity() !== null && isset(\Yii::$app->session[$name]);
        }
        return true;
    }
    
    public function __unset($name) {
        try {
            parent::__unset($name);
        } catch (\yii\base\InvalidCallException $ex) {
            if ($this->getIdentity() !== null) {
                \Yii::$app->session->remove($name);
            } else {
                throw $ex;
            }
        }
    }
}