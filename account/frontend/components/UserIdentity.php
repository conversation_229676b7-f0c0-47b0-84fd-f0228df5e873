<?php

namespace frontend\components;

use frontend\components\Captcha;
use frontend\components\RegionalCom;
use common\components\GeneralComponent;
use common\components\Password;
use common\components\RememberMeToken;
use common\components\SSOCom;
use common\models\AddressBook;
use common\models\Customers;
use common\models\CustomersGroups;
use common\models\CustomersInfo;
use common\models\CustomersLoginIpHistory;
use common\models\ShassoSsoToken;
use common\models\ShassoUserLoginHistory;
use common\models\SnsConnection;
use common\models\UserLastLogin;
use Yii;
use yii\base\NotSupportedException;
use yii\db\Expression;
use yii\helpers\Url;
use yii\web\Cookie;
use yii\web\IdentityInterface;
use common\components\FreshdeskApiCom;

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class UserIdentity implements IdentityInterface {
    
    /**
     * @var Customers
     */
    protected $customers;

    public $username, $password, $login_method;

    public function __construct($username = '', $passwd = '') {
        $this->username = $username;
        $this->password = $passwd;
    }
    
    public function __get($name) {
        if (isset($this->$name)) {
            return $this->$name;
        } else if (isset(Yii::$app->session[$name])) {
            return Yii::$app->session[$name];
        } else {
            return null;
        }
    }

    /**
     * @inheritdoc
     */
    public static function findIdentity($id) {
        $customer = Customers::findOne([
            'customers_id' => $id,
        ]);
        if (isset($customer)) {
            $identity = new static();
            $identity->customers = $customer;
            return $identity;
        }
        return null;
    }

    /**
     * @inheritdoc
     */
    public static function findIdentityByAccessToken($token, $type = null) {
        throw new NotSupportedException('"findIdentityByAccessToken" is not implemented.');
    }

    /**
     * @inheritdoc
     */
    public function getId() {
        return $this->customers ? $this->customers->customers_id : null;
    }

    /**
     * @inheritdoc
     */
    public function getAuthKey() {
        throw new NotSupportedException('"getAuthKey" is not implemented.');
    }

    /**
     * @inheritdoc
     */
    public function validateAuthKey($authKey) {
        throw new NotSupportedException('"validateAuthKey" is not implemented.');
    }

    /**
     * Authenticates a user.
     * The example implementation makes sure if the username and password
     * are both 'demo'.
     * In practical applications, this should be changed to authenticate
     * against some persistent user identity storage (e.g. database).
     * @return boolean whether authentication succeeds.
     */
    public function authenticate() {
        /* error code
         * 0 : success
         * 1 : fail, invalid email or password
         * 2 : fail, invalid recaptcha
         * 3 : fail, empty recaptcha input
         * 4 : fail, device pin required
         * 5 : fail, inactive account status
         *
         * login method
         * - normal
         * - register
         * - remember_me
         * - api_register ( api from permitted service )
         * - device ( device pin )
         * - sns ( facebook / provider )
         */
        $_error = true;
        $this->errorCode = '';
        $m_cust = null;

        # Authentication
        $ogm_username = (isset($_COOKIE['ogm']['un']) ? $_COOKIE['ogm']['un'] : '');
        $ogm_passwd = (isset($_COOKIE['ogm']['uc']) ? $_COOKIE['ogm']['uc'] : '');

        switch ($this->login_method) {
            case "normal":
            case "register":
                if (($this->username != '') && ($this->password != '')) {
                    $m_cust = Customers::findOne(['customers_email_address' => $this->username]);
                    if (isset($m_cust->customers_id)) {
                        if (Password::validatePassword($this->password, $m_cust->customers_password)) {
                            $_error = false;
                        }
                    }
                }
                break;

            case "sns":
                $ha_provider = Yii::$app->hybridauth->getSession('ha_provider');
                $ha_uid = Yii::$app->hybridauth->getSession('ha_uid');

                if ($ha_provider && $ha_uid) {
                    $m_customer = SnsConnection::findOne(['provider' => $ha_provider, 'provider_uid' => $ha_uid]);
                    if (isset($m_customer->customers_id)) {
                        $m_cust = Customers::findOne(['customers_id' => $m_customer->customers_id]);
                        if (isset($m_cust)) {
                            $this->username = $m_cust->customers_email_address;
                            $_error = false;
                        }
                    }
                }
                break;

            case SSOCom::$otp_key_api_register:
            case SSOCom::$otp_key_device:
                $m_cust = Customers::findOne([
                    'customers_id' => $this->username,
                ]);
                if (isset($m_cust->customers_id)) {
                    $this->username = $m_cust->customers_email_address;
                    $_error = false;
                }
                break;
        }

        # Login IP History
        $login_ip = GeneralComponent::getIPAddress();
        $login_country = Yii::$app->geoip->countryCodeByIP($login_ip);
        $login_user_agent = (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT'));
        $login_ua_parse = GeneralComponent::parseUserAgent($login_user_agent);
        
        // Remember Me
        if (!$m_cust && ($ogm_username != '') && ($ogm_passwd != '')) {
            $this->login_method = 'remember_me';

            $m_cust = Customers::findOne(['customers_email_address' => $ogm_username]);
            if (isset($m_cust->customers_id)) {
                if (RememberMeToken::get()->isTokenValid($m_cust, $ogm_passwd, $login_ip, $login_country, $login_user_agent, $login_ua_parse)) {
                    $_error = false;
                }
            }

            if ($_error) {
                Yii::$app->response->cookies->remove(new Cookie([
                    'name' => 'ogm[un]',
                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                ]));
                Yii::$app->response->cookies->remove(new Cookie([
                    'name' => 'ogm[uc]',
                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                ]));
            }
        }

        // Account Inactive
        if (!$_error && isset($m_cust->customers_id) && $m_cust->customers_status != '1') {
            $_error = true;
            $this->errorCode = 5;
        }

        // Device Pin Request
        if (($this->login_method == "normal" || $this->login_method == "sns") && !$_error) {
            $r_scm = SSOCom::secureCodeMatch(SSOCom::$otp_key_device, ["cid" => $m_cust->customers_id, 'skip_device_pin' => $this->login_method == "sns"]);
            if (!$r_scm["status"]) {
                $_error = true;
                Yii::$app->session['device_pin_login_method'] = $this->login_method;
                $this->errorCode = 4;
            }
        }

        //Login successful
        
        if (!$_error && isset($m_cust->customers_id) && (Yii::$app->user->isGuest || (isset(Yii::$app->user->id) && (Yii::$app->user->id != $m_cust->customers_id)))) {
            if ($this->login_method == SSOCom::$otp_key_device && Yii::$app->session['device_pin_login_method'] == 'sns') {
                $this->login_method = Yii::$app->session['device_pin_login_method'];
            }
            Yii::$app->session->remove('device_pin_login_method');
            
            SSOCom::secureCodeCreate(SSOCom::$otp_key_device, array("cid" => $m_cust->customers_id));

            $this->errorCode = 0;
            $_num_logon = 1;

            $this->username = $m_cust->customers_email_address;
            $this->password = '***';
            
            Yii::$app->session['id'] = $m_cust->customers_id;
            Yii::$app->session['email'] = $m_cust->customers_email_address;
            Yii::$app->session['firstname'] = $m_cust->customers_firstname;
            Yii::$app->session['lastname'] = $m_cust->customers_lastname;

            Yii::$app->session['ogm_group_name'] = CustomersGroups::model()->getCustomersGroupsName($m_cust->customers_groups_id);
            Yii::$app->session['login_method'] = $this->login_method;

            if ($this->login_method == 'sns') {
                $sns['login_method'] = Yii::$app->hybridauth->getSession('ha_provider');
                $sns['photo'] = Yii::$app->hybridauth->getSession('ha_photo');
                Yii::$app->session['sns'] = $sns;
            }

            Yii::$app->session['customers_groups_id'] = $m_cust->customers_groups_id;
            Yii::$app->session['customers_default_address_id'] = $m_cust->customers_default_address_id;

            # Last Login
            $c_attr = array(
                'customers_info_date_of_last_logon' => new Expression('NOW()'),
                'customers_info_number_of_logons' => $_num_logon,
                'customer_info_selected_country' => (isset(Yii::$app->session["ctry_id"]) ? Yii::$app->session["ctry_id"] : 0)
            );

            $m_ci = CustomersInfo::findOne([
                'customers_info_id' => $m_cust->customers_id
            ]);
            if (isset($m_ci->customers_info_id)) {
                $_num_logon = $m_ci->customers_info_number_of_logons + 1;

                # no login for 90days, set as dormant account
                if ($m_ci->customers_info_number_of_logons > 0) {
                    $_diff_day = (time() - strtotime($m_ci->customers_info_date_of_last_logon)) / 86400;
                    if ($_diff_day > 90) {
                        $c_attr["customer_info_account_dormant"] = 1;
                    }
                }
            }
            CustomersInfo::updateAll($c_attr, [
                'customers_info_id' => $m_cust->customers_id
            ]);
            unset($c_attr);

            $c_attr = array(
                'customers_id' => $m_cust->customers_id,
                'customers_login_date' => new Expression('NOW()'),
                'customers_login_ip' => $login_ip,
                'customers_login_ua_info' => $login_user_agent,
                'login_method' => ($this->login_method == 'sns') ? $sns['login_method'] : $this->login_method
            );
            try {
                CustomersLoginIpHistory::model()->saveNewRecord($c_attr);
            } catch (\Exception $ex) {
                //TODO silent error first for now
                //\Yii::$app->reporter->reportToAdminViaSlack('Customer ip login insert error', $ex->getMessage());
            }
            unset($c_attr);

            //Phased out, seemingly unused
            // $c_attr = array(
            //     "user_id" => $m_cust->customers_id,
            //     "login_date" => new Expression('NOW()'),
            //     "login_ip" => $login_ip,
            //     "login_ua_info" => $login_user_agent,
            //     "login_method" => ($this->login_method == 'sns') ? $sns['login_method'] : $this->login_method
            // );
            // ShassoUserLoginHistory::model()->saveNewRecord($c_attr);
            // unset($c_attr);

            $c_attr = array(
                "user_id" => $m_cust->customers_id,
                "login_date" => new Expression('NOW()'),
                "login_ip" => $login_ip,
                "login_ip_iso2" => $login_country
            );

            UserLastLogin::model()->insertOrUpdate($c_attr);

            unset($c_attr);

            # remember me
            if ((isset($_POST['remember_me']) && $_POST['remember_me']) || isset(Yii::$app->request->cookies['S3RM']) || ($this->login_method == 'remember_me')) {
                Yii::$app->response->cookies->remove(new Cookie([
                    'name' => 'S3RM',
                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                ]));

                $ogm_passwd = RememberMeToken::get()->generateToken($m_cust, $ogm_passwd, $login_ip, $login_country, $login_user_agent, $login_ua_parse);
                
                $cookieLifeTimeDays = time() + 60 * 60 * 24 * RememberMeToken::REMEMBER_ME_EXPIRY_DAYS;

                Yii::$app->response->cookies->add(new Cookie([
                    'name' => 'ogm[un]',
                    'value' => $m_cust->customers_email_address,
                    'expire' => $cookieLifeTimeDays,
                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                ]));
                Yii::$app->response->cookies->add(new Cookie([
                    'name' => 'ogm[uc]',
                    'value' => $ogm_passwd,
                    'expire' => $cookieLifeTimeDays,
                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                ]));
            }

            self::login_trial_unset();

            Yii::$app->session["uc_sync"] = false;
            
            if (Yii::$app->user->isGuest) {
                $this->customers = $m_cust;
                Yii::$app->user->login($this);
            } else {
                Yii::$app->user->logout(false);
                $this->customers = $m_cust;
                Yii::$app->user->login($this);
            }

            RegionalCom::updateDB();
        } else {
            if ($this->errorCode == '') {
                $this->errorCode = 1;
            }
        }
    }

    public function register($_input = array()) {
        $result = false;

        if (!Customers::find()->where(['customers_email_address' => strtolower($this->username)])->exists()) {
            $fname = (isset($_input['fname']) ? $_input['fname'] : substr($this->username, 0, strpos($this->username, "@")));
            $lname = (isset($_input['lname']) ? $_input['lname'] : "");
            $news = (isset($_input['news']) ? 17 : "");

            $m_attr = array(
                'customers_email_address' => strtolower($this->username),
                'customers_firstname' => $fname,
                'customers_lastname' => $lname,
                'customers_password' => Password::encryptPassword($this->password),
                'customers_newsletter' => $news,
            );
            $cid = Customers::model()->saveNewRecord($m_attr, true);
            unset($m_attr);
            
            if ($cid) {
                $m_attr = array(
                    'customers_id' => $cid,
                    'entry_firstname' => $fname,
                    'entry_lastname' => $lname
                );
                $addressId = AddressBook::model()->saveNewRecord($m_attr, true);
                unset($m_attr);

                $m_attr = array('customers_default_address_id' => $addressId);
                Customers::updateAll($m_attr, [
                    'customers_id' => $cid,
                ]);
                unset($m_attr);

                // 3rd party portal URL query
                $f_data = array();
                $req_uri = SSOCom::clientURLQuery();
                parse_str($req_uri, $f_data);

                $signup_ip = GeneralComponent::getIPAddress();
                $signup_ctry = Yii::$app->geoip->countryCodeByIP($signup_ip);

                $m_attr = array(
                    'customers_info_id' => $cid,
                    'customers_info_date_account_created' => new Expression('NOW()'),
                    'customers_info_account_created_ip' => $signup_ip,
                    'account_created_country' => ($signup_ctry ? $signup_ctry : ""),
                    'customers_info_account_created_from' => Yii::$app->params["SITE_ID"],
                    'account_created_site' => (isset($f_data["service"]) && !empty($f_data["service"]) ? $f_data["service"] : Yii::$app->name),
                    'customer_info_selected_language_id' => Yii::t("dev", "LANG_CODE_TO_ID")
                );
                CustomersInfo::model()->saveNewRecord($m_attr);
                unset($m_attr);

                self::login_trial_unset();

                $result = true;
            }
        }

        return $result;
    }

    public function logout() {
        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'S3ID',
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
        RememberMeToken::get()->deleteTokenByToken(\Yii::$app->user->id, (isset($_COOKIE['ogm']['uc']) ? $_COOKIE['ogm']['uc'] : ''));
        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'ogm[un]',
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'ogm[uc]',
            'domain' => Yii::$app->params['COOKIE_DOMAIN'],
        ]));
        
        // delete every 15min
        if ((date("i") % 15) == 0) {
            ShassoSsoToken::deleteAll('expiry < ' . new Expression('NOW()'));
        }
        ShassoSsoToken::deleteAll(['sess_id' => Yii::$app->session->getId()]);

        unset(Yii::$app->session['customers_groups_id']);
    }

    public static function login_trial() {
        $req_uri = GeneralComponent::getRequestURI();
        $returnUrl = Url::to(['sso/login'], true) . (!empty($req_uri) ? "?" . $req_uri : "");
        Captcha::checkNeedCaptcha('sessionCheck', array(
            'returnUrl' => $returnUrl,
            'sessionKey' => 'login_trial',
            'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
        ));
        Captcha::checkNeedCaptcha('ipCheck', array(
            'returnUrl' => $returnUrl,
            'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_SAME_IP'],
        ));
    }

    public static function login_trial_unset() {
        unset(Yii::$app->session['login_trial']);
        unset(Yii::$app->session['sgid']);
        unset(Yii::$app->session['sgstime']);
    }

    public static function statusAction($code, $_input = array()) {
        switch ($code) {
            case 0:
                $req_arr = array();

                // 3rd party portal URL query
                $req_uri = SSOCom::clientURLQuery();
                parse_str($req_uri, $req_arr);

                if (isset($req_arr['origin'])) {
                    Yii::$app->controller->redirect(Url::to(['sso/login-all'], true) . (!empty($req_uri) ? "?" : "") . $req_uri);
                    Yii::$app->end();
                } else {
                    if (isset($_input["login_method"]) && ($_input["login_method"] == "signup")) {
                        Yii::$app->session->setFlash('success', Yii::t('sso', 'TEXT_SIGNUP_WELCOME', array('SYS_FIRSTNAME' => Yii::$app->user->firstname)));
                        Yii::$app->controller->redirect(Url::to(['overview/index'], true));
                        Yii::$app->end();
                    }
                }

                Yii::$app->controller->redirect(Url::to(Yii::$app->homeUrl[0], true));
                Yii::$app->end();
                break;

            case 1:
                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_LOGIN_1"));
                break;

            case 5:
                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_LOGIN_5"));
                break;

            case 6:
                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_SIGNUP"));
                break;

            case 101:   // sns bind with other account
                $link = "";
                if (isset($_input["uid"]) && !empty($_input["uid"]) && isset($_input["sns"])) {
                    $link = Yii::t("sso", "ERROR_XPRESS_SNS_DETACH", ["SYS_SNS_DETACH_LINK" => \yii\helpers\Html::a(Yii::t('general', 'TEXT_HERE'), ["sso/xpress", "ha_provider" => $_input["sns"], "snsdc" => SSOCom::secureCodeRequest(SSOCom::$otp_key_sns_detach, $_input["uid"])])]);
                }
                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_XPRESS_SNS_CONNECTED", ["SYS_SITE_NAME" => Yii::$app->name, "SYS_PROVIDER" => (!empty($_input["sns"]) ? ucwords($_input["sns"]) : "")]) . $link);
                break;

            case 102:   // account bind with other sns
                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_XPRESS_ACCOUNT_CONNECTED", ["SYS_SITE_NAME" => Yii::$app->name, "SYS_PROVIDER" => (!empty($_input["sns"]) ? ucwords($_input["sns"]) : "")]));
                break;
        }
    }

}
