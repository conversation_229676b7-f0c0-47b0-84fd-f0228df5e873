<?php

namespace frontend\components\captcha_providers;

class CaptchaCom extends \frontend\components\CaptchaProvider {

    protected $captcha_com_wrapper;

    public function __construct($prefer_invisible = null) {
        $this->captcha_com_wrapper = new \frontend\components\CaptchaComWrapper();
    }

    public function load_html() {
        $captcha_com = $this->captcha_com_wrapper->getCaptchaCom(true);
        \frontend\assets\CaptchaComAsset::register(\Yii::$app->controller->getView());
        $html = \yii\helpers\Html::beginTag('div', [
            'style' => 'width: 280px; margin: 0px auto;',
        ]);
        $html .= $captcha_com->Html();
        $html .= \yii\helpers\Html::endTag('div');
        $html .= <<<HTML
<div class="validationDiv">
    <input name="CaptchaCode" type="text" id="CaptchaCode" value="" />
</div>
HTML;
        return $html;
    }

    public function load_js() {
        $captcha_com = $this->captcha_com_wrapper->getCaptchaCom();
        return \BDC_CaptchaScriptsHelper::GetInitScriptMarkup($captcha_com, $captcha_com->InstanceId);
    }

    public function verify_response_raw($response) {
        $captcha_com = $this->captcha_com_wrapper->getCaptchaCom();
        return $captcha_com->Validate();
    }

    public function verify_response($response) {
        return $this->verify_response_raw($response);
    }

}