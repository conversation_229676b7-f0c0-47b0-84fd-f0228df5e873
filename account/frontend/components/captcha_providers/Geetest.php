<?php

namespace frontend\components\captcha_providers;

use common\components\CurlComponent;
use Yii;

/**
 * The reCAPTCHA alternative from China
 * http://www.geetest.com/
 */
class Geetest extends \frontend\components\CaptchaProvider {

    private $GT_SDK_VERSION  = 'php_2.15.4.2.2';
    
    private $id;
    
    private $challenge;

    public function __construct($prefer_invisible = null) {
        \frontend\assets\GeetestAsset::register(\Yii::$app->controller->getView());
    }

    public function load_html() {
        $this->challenge = $this->getChallenge();
        if (strlen($this->challenge) != 32) {
            return '';
        }
        $this->id = \common\components\GeneralComponent::generateRandomString(6);
        return "<div id='geetest_$this->id'></div>";
    }
    
    public function load_js() {
        if (strlen($this->challenge) != 32) {
            return '';
        }
        $params = json_encode([
            "gt" => Yii::$app->params['GEETEST_CONFIG']['PUBLIC_KEY'],
            "challenge" => $this->challenge,
            "product" => 'embed',
            "float" => 'right',
        ]);
        return <<<JS
    initGeetest($params, function(captchaObj) {
        captchaObj.appendTo("#geetest_$this->id");
    });
JS;
    }

    public function verify_response($response) {
        if (empty($response['geetest_challenge']) || empty($response['geetest_validate']) || empty($response['geetest_seccode'])) {
            return false;
        }
        $challenge = $response['geetest_challenge'];
        $validate = $response['geetest_validate'];
        $seccode = $response['geetest_seccode'];
        if (strlen($validate) != 32) {
            return false;
        }
        if (md5(Yii::$app->params['GEETEST_CONFIG']['PRIVATE_KEY'] . 'geetest' . $challenge) != $validate) {
            return false;
        }
        $postData = array(
            "seccode" => $seccode,
            "sdk" => $this->GT_SDK_VERSION,
        );
        $url = Yii::$app->params['GEETEST_CONFIG']['GEETEST_API_SECURE_SERVER'] . "/validate.php";
        $curl = new CurlComponent();
        $resp = $curl->curlPost($url, $postData);
        if (strlen($resp) > 0 && $resp == md5($seccode)) {
            return true;
        } else {
            return false;
        }
    }

    private function getChallenge() {
        $params = array(
            'gt' => Yii::$app->params['GEETEST_CONFIG']['PUBLIC_KEY'],
        );
        $url = Yii::$app->params['GEETEST_CONFIG']['GEETEST_API_SECURE_SERVER'] . "/register.php";
        $curl = new CurlComponent();
        $response = $curl->curlGet($url, $params);
        return $response;
    }

}
