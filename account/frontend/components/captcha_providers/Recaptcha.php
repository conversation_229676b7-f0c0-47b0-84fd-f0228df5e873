<?php

namespace frontend\components\captcha_providers;

use common\components\CurlComponent;
use Yii;

/**
 * The reCAPTCHA server URL's
 */
class Recaptcha extends \frontend\components\CaptchaProvider {
    
    protected $prefer_invisible;
    
    public function __construct($prefer_invisible = null) {
        $this->prefer_invisible = $prefer_invisible;
        \frontend\assets\RecaptchaAsset::register(\Yii::$app->controller->getView());
    }

    public function load_html() {
        $return_html = '';
        if ($this->prefer_invisible) {
            $return_html .= "<div class='g-recaptcha g-recaptcha-v2' data-size='invisible' data-callback='grecaptcha_callback' ></div>";
            $js = <<<JS
    function grecaptcha_callback() {
        var form = jQuery('.g-recaptcha-v2').closest('form');
        form.off('beforeSubmit.grecaptcha').on('beforeSubmit.prevent_double_submit', function() {
            if (jQuery(this).data('_is_submitting')) {
                return false;
            }
            jQuery(this).data('_is_submitting', true);
            return true;
        }).submit();
    }
JS;
            $return_html .= \yii\helpers\Html::script($js);
        } else {
            $return_html .= "<div class='g-recaptcha g-recaptcha-v2' style='width:300px;'></div>";
        }
        return $return_html;
    }
    
    public function load_js() {
        $api_key = Yii::$app->params['RECAPTCHA_CONFIG']['PUBLIC_KEY'];
        $js = <<<JS
            grecaptcha.ready(function() {
                jQuery('.g-recaptcha-v2').each(function() {
                    var widget_id = grecaptcha.render(this, {
                        'sitekey' : '$api_key'
                    });
                    jQuery(this).closest('form').data('g-recaptcha-id', widget_id);
                });
            });
JS;
        if ($this->prefer_invisible) {
            $js .= <<<JS
    var form = jQuery('.g-recaptcha-v2').closest('form');
    form.on('beforeSubmit.grecaptcha', function() {
        grecaptcha.execute(form.data('g-recaptcha-id'));
        return false;
    });
JS;
        }
        return $js;
    }
    
    public function verify_response_raw($response) {
        if (empty($response['g-recaptcha-response'])) {
            return false;
        }
        return \frontend\components\GoogleRecaptcha::verifyToken($response['g-recaptcha-response'], Yii::$app->params['RECAPTCHA_CONFIG']['PRIVATE_KEY']);
    }
    
    public function verify_response($response) {
        $reply = $this->verify_response_raw($response);
        return isset($reply["success"]) && $reply["success"];
    }
}
