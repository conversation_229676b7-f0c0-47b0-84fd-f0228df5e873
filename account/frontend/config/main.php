<?php

$params = \yii\helpers\ArrayHelper::merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-encoded.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require(__DIR__ . '/params-encoded.php'),
    require __DIR__ . '/params-local.php',
    require __DIR__ . '/../modules/sso/config/params.php'
);

return [
    'modules' => [
        'sso' => [
            'class'  => 'app\modules\sso\Module',
            'layout' => 'main',
        ],
        // Other modules here...
    ],
    'id' => 'app-frontend',
    'name' => 'OffGamers Authentication',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'frontend\controllers',
    'controllerMap' => [
        //Yii1: Map all camel case controllers here for legacy support (and map actions too)
        'smsToken' => 'frontend\controllers\SmsTokenController',
        'storeCredit' => 'frontend\controllers\StoreCreditController',
        'userBar' => 'frontend\controllers\UserBarController',
        'verifyEmail' => 'frontend\controllers\VerifyEmailController',
        'verificationForm' => 'frontend\controllers\VerificationFormController',
    ],
    'components' => [
        'request' => [
            'enableCookieValidation' => false,
            'enableCsrfValidation' => true,
            'enableCsrfCookie' => false,
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ]
        ],
        'user' => [
            'class' => 'frontend\components\User',
            'identityClass' => 'frontend\components\UserIdentity',
            'loginUrl' => ['sso/login'],
        ],
        'assetManager' => [
            'class' => '\common\components\AssetManager',
        ],
        'session' => [
            'keyPrefix' => 'auth-session/',
            'class' => '\frontend\components\RedisSession',
            'name' => 'OGMAUTHV1',
        ],
        'errorHandler' => [
            'class' => 'common\components\WebErrorHandler',
            'errorAction' => 'site/error',
        ],
        'urlManager' => [
            'rules' => [
                'sso/<action>' => 'sso/sso/<action>',
                'sso/sns/response/<hauth_done:\w+>' => 'sso/sns/response',

                //To map Facebook callback SNS; must be full path configured in app starting php-sdk-v5
                [
                    'pattern' => 'privacy-policy',
                    'route' => 'site/page',
                    'defaults' => ['view' => 'policy'],
                ],
                [
                    'pattern' => 'terms-of-service',
                    'route' => 'site/page',
                    'defaults' => ['view' => 'tos'],
                ],
                'op' => 'wor',
                'op/<action:[\w-]+>' => 'wor/<action>',
                '<controller:[\w-]+>/<id:\d+>' => '<controller>/view',
                '<controller:[\w-]+>/<action:[\w-]+>/<id:\d+>' => '<controller>/<action>',
                '<controller:[\w-]+>/<action:[\w-]+>' => '<controller>/<action>',
            ],
        ],
        'hybridauth' => [
            'class' => 'app\modules\sso\components\HybridAuth',
            'base_url' => 'sso/sns/response',
            'error_url' => 'sso/login',
            'proxy' => $params['USE_PROXY'] && !empty($params['PROXY_HOST']) ? ($params['PROXY_HOST'] . ':' . $params['PROXY_PORT']) : '',
            'providers' => [
                'Joybit' => [
                    'enabled' => true,
                    'display' => false,
                    'keys' => [
                        'id' => $params['hybridauth.joybit.keys.id'],
                        'secret' => $params['hybridauth.joybit.keys.secret']
                    ],
                    'host' => 'shasso.com',
                    'call_url' => 'http://user.joybit.com/oauth/auth_login',
                    'provider_timeout' => '30',
                    // sec, Joybit request timeout, mandatory
                    'request_timeout' => '180'
                    // sec, time frame to hold Joybit valid connection, leave blank to not timeout
                ],
                'Facebook' => [
                    'enabled' => true,
                    'display' => true,
                    'keys' => [
                        'id' => $params['hybridauth.facebook.keys.id'],
                        'secret' => $params['hybridauth.facebook.keys.secret']
                    ],
                    'scope' => ['email'],
                    'redirect_uri' => 'sso/sns/response/Facebook',
                ],
                'Google' => [
                    'enabled' => true,
                    'display' => true,
                    'keys' => [
                        'id' => $params['hybridauth.google.keys.id'],
                        'secret' => $params['hybridauth.google.keys.secret']
                    ],
                    'scope' => 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
                    'access_type' => 'online',
                    'width' => '600',
                    'height' => '650',
                    'state' => '',
                    'redirect_uri' => 'sso/sns/response/Google',
                ],
                'Twitter' => [
                    'enabled' => true,
                    'display' => true,
                    'keys' => [
                        'key' => $params['hybridauth.twitter.keys.id'],
                        'secret' => $params['hybridauth.twitter.keys.secret']
                    ],
                    'redirect_uri' => 'sso/sns/response/Twitter',
                ],
                'PayPal' => [
                    'enabled' => true,
                    'display' => true,
                    'login_url' => $params["paypal.login.login_url"],
                    "accesstoken_api_url" => $params["paypal.login.accesstoken_api_url"],
                    "userinfo_api_url" => $params["paypal.login.userinfo_api_url"],
                    'scope' => "profile email https://uri.paypal.com/services/paypalattributes",
                    'keys' => [
                        'id' => $params['hybridauth.paypal.keys.id'],
                        'secret' => $params['hybridauth.paypal.keys.secret']
                    ],
                    'redirect_uri' => 'sso/sns/response/PayPal',
                ]
            ],
            'debug_mode' => false,
            'debug_file' => '@runtime/logs/hybrid.log'
        ],
    ],
    'aliases' => [
        '@media' => '@app/media',
    ],
    'params' => $params,
];
