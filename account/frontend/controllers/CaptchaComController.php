<?php

namespace frontend\controllers;

class CaptchaComController extends \frontend\components\Controller {
    
    public $enableCsrfValidation = false;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['index'],
                        'allow' => true,
                        'roles' => ['*', '@', '?'],
                    ],
                ],
            ],
        ]);
    }
    
    public function actionIndex() {
        $wrapper = new \frontend\components\CaptchaComWrapper();
        $this->captcha_com = $wrapper->serve();
    }

}
