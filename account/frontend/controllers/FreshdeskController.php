<?php

namespace frontend\controllers;

use common\components\FreshdeskApiCom;
use common\models\Countries;
use common\models\Customers;
use common\models\CustomersSetting;
use common\models\ShassoClientExtraInfo;
use frontend\components\Controller;
use <PERSON>cobucci\JWT\Signer\Keychain;
use <PERSON><PERSON>bu<PERSON>\JWT\Signer\Rsa\Sha256;
use Yii;
use yii\filters\AccessControl;
use yii\helpers\Url;
use yii\web\HttpException;

// just to make our life simpler

// you can use Lcobucci\JWT\Signer\Ecdsa\Sha256 if you're using ECDSA keys

class FreshdeskController extends Controller
{
    private static $error_url = "/sso/login";

    public $enableCsrfValidation = false;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['login', 'logout'],
                        'allow' => true,
                        'roles' => ['*', '@', '?'],
                    ],
                ],
            ],
        ]);
    }

    public function actionLogin()
    {
        $session = Yii::$app->session;
        $request = Yii::$app->request;

        $request_keys = [
            'response_type',
            'client_id',
            'scope',
            'state',
            'redirect_uri',
            'registration_id',
            'nonce',
            'action',
            'origin'
        ];

        foreach ($request_keys as $key) {
            $session->set($key, $request->get($key));
        }

        $session->set('action','freshdesk-login');
        $session->set('nextUrl', $session->get('redirect_uri'));

        if ($this->verifyFreshdeskRedirectUrl() == false) {
            // change code here
            $this->redirect(self::$error_url);
            return;
        }
        // check if the user is login
        if (Yii::$app->user->getIsGuest()) {
            $params = [
                'sso/login',
                "action" => $session->get('action'),
                "nextUrl" => $session->get('redirect_uri'),
                "service" => $session->get('client_id'),
                "hl" => null,
                "state" => $session->get('state'),
                "origin" => $session->get('origin'),
                "nonce" => $session->get('nonce'),
            ];
            $url = Url::toRoute($params);
            $this->redirect($url);
        } else {
            $user = Customers::find()->where(['customers_id' => Yii::$app->user->id])->one();
            $this->freshdeskRedirect($user);
        }

    }

    public static function freshdeskRedirect($user)
    {

        $phone_country = Countries::find()->where(['countries_id' => $user['customers_country_dialing_code_id']])->one();
        $country_code = (!empty($phone_country['countries_international_dialing_code'])) ? '+' . $phone_country['countries_international_dialing_code'] . ' ' : '0';

        //generate the jwt token
        $signer = new Sha256();
        $keychain = new Keychain();

        // sub, email, iat, nonce, given_name, family_name, phone_number
        $jwt_token = Yii::$app->jwt->getBuilder()
            ->setSubject("OG Freshdesk SSO")
            ->set('sub', FreshdeskApiCom::generateUniqueExternalCid($user['customers_id']))
            ->set('email', $user['customers_email_address'])
            ->set('iat', time())
            ->set('nonce', $_SESSION['nonce'])
            ->set('given_name', $user['customers_firstname'])
            ->set('family_name', $user['customers_lastname'])
            ->set('phone_number', $country_code . $user['customers_telephone'])
            ->sign($signer, $keychain->getPrivateKey('file://' . \Yii::getAlias('@freshdeskPrivateKey'))) // creates a signature using your private key
            ->getToken(); // Retrieves the generated token

        $url = $_SESSION['nextUrl'] . '?state=' . $_SESSION['state'] . '&id_token=' . $jwt_token;

        //Check Customers setting if email is tagged with fd_verification email
        $user_fd_setting = CustomersSetting::find()->where(["customers_id" => $user['customers_id'], "customers_setting_key" => FreshdeskApiCom::FD_EMAIL_VERIFICATION, "customers_setting_value" => '1'])->one();
        if ($user_fd_setting) {
            $last_sent = strtotime($user_fd_setting->updated_datetime);
            $resend_time = Yii::$app->params['FD_MAIL_RESEND_TIME'];
            $elapse = time() - $last_sent;
            if ($elapse >= $resend_time) {
                //resend email
                FreshdeskApiCom::flagUserFdVerification($user_fd_setting->customers_id, $user_fd_setting->customers_setting_value);

            }

            Yii::$app->session->setFlash('success', Yii::t('profile', 'VERIFY_NEW_EMAIL', ["EMAIL_ADDRESS" => $user['customers_email_address'], "LAST_SENT_TIME" => $user_fd_setting->updated_datetime]));
            return Yii::$app->controller->redirect(self::$error_url);

        }

        //Prepare params to push to FD API
        $address = \common\models\AddressBook::find()->where(["address_book_id" => $user['customers_default_address_id']])->one();
        if ($address) {
            $address_country = Countries::find()->where(['countries_id' => $address['entry_country_id']])->one();
            $complete_address = $address['entry_street_address'] . " " . $address['entry_suburb'] . " " . $address['entry_city'] . ", " . $address['entry_state'] . " " . $address['entry_postcode'] . " " . $address_country['countries_name'];
        } else {
            $complete_address = "";
        }
        $params = [
            "name" => $user['customers_firstname'] . " " . $user['customers_lastname'],
            "email" => $user['customers_email_address'],
            "phone" => $country_code . $user['customers_telephone'],
            "unique_external_id" => (string) $user['customers_id'],
            "address" => $complete_address,
        ];

        // Check if the email exist in FD
        $fd_com = FreshdeskApiCom::get(FreshdeskApiCom::CONFIG_TAG_SSO_OG, $_SESSION['client_id']);
        $fd_user = $fd_com->apiListUser($user['customers_email_address']);
        // If not exist Create the account
        if (empty($fd_user) && $fd_user !== NULL) {
            $result = $fd_com->apiCreateUser($params);
        } elseif ($fd_user === NULL) {
            Yii::$app->session->setFlash('danger', Yii::t('sso', 'ERROR_EMAIL_FORMAT'));
            return Yii::$app->controller->redirect(self::$error_url);
        } else {
            $result = $fd_com->apiUpdateUser($fd_user[0]['id'], $params);
        }
        //proceed to redirect
        return Yii::$app->controller->redirect($url);
    }

    public function verifyFreshdeskRedirectUrl()
    {
        $is_url_valid = false;
        $redirect_url = (isset($_SESSION['redirect_uri']) && !empty($_SESSION['redirect_uri'])) ? $_SESSION['redirect_uri'] : ((isset($_SESSION['nextUrl']) && !empty($_SESSION['nextUrl'])) ? $_SESSION['nextUrl'] : "");
        $client_id = (isset($_SESSION['client_id']) && !empty($_SESSION['client_id'])) ? $_SESSION['client_id'] : ((isset($_SESSION['service']) && !empty($_SESSION['service'])) ? $_SESSION['service'] : "");

        if (!empty($redirect_url) && !empty($client_id)) {
            $config = \Yii::$app->params['FRESHDESK_API'][FreshdeskApiCom::CONFIG_TAG_SSO_OG][$_SESSION['client_id']];
            // var_dump($client_extra_info->extra_value);

            if ($config && $redirect_url == $config['redirect_uri']) {
                $is_url_valid = true;
            } else {
                \Yii::$app->reporter->reportToAdminViaSlack("No Redirect url found", $_SESSION);
            }
        } else {
            $redirects = [
                'client_id' => $client_id,
                'client' => $redirect_url,
            ];
            \Yii::$app->reporter->reportToAdminViaSlack("Client Info not complete", $redirects);
        }

        return $is_url_valid;
    }
}