<?php

namespace frontend\controllers;

use common\components\StoreCreditCom;
use common\models\StorePoints;
use frontend\components\Controller;

class OverviewController extends Controller {

    public $layout = 'main-col1';
    
    public function actionIndex() {
        $this->menu = 'LINK_OVERVIEW';

        $f_data = array(
            "f_store_credit" => StoreCreditCom::storeCreditBalance(true),
            "f_wor" => number_format(StorePoints::model()->getCustomersStorePoint(), 0, '.', ','),
            'f_tfa_enabled' => \common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled(\Yii::$app->user->id),
        );
        return $this->render('index', $f_data);
    }

}