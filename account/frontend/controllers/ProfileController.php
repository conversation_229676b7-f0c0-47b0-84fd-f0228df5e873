<?php

namespace frontend\controllers;

use app\modules\sso\components\Helper;
use common\components\CustomerSecurityComponent;
use common\components\CustomersInfoVerificationComponent;
use common\components\EmailComponent;
use common\components\FreshdeskApiCom;
use common\components\GeneralComponent;
use common\components\LogFilesComponent;
use common\components\Password;
use common\components\PhoneVerificationComponent;
use common\models\AddressBook;
use common\models\Configuration;
use common\models\Countries;
use common\models\CustomerInfoLock;
use common\models\Customers;
use common\models\CustomersInfo;
use common\models\CustomersProfile;
use common\models\CustomersSetting;
use common\models\CustomersInfoVerification;
use common\models\CustomersStaticInfo;
use common\models\InstantMessageAccounts;
use common\models\InstantMessageType;
use common\models\Zones;
use frontend\components\Captcha;
use frontend\components\Controller;
use frontend\models\AddressForm;
use frontend\models\UpdateTwoFaForm;
use frontend\models\ProfileForm;
use frontend\models\UpdateEmailForm;
use frontend\models\UpdatePasswordForm;
use frontend\models\UpdatePhoneForm;
use frontend\models\UpdatePhoneFormLastFourDigit;
use common\models\CustomersDocumentIdVerification;
use common\models\CustomersMobileChangeApproval;
use common\models\CustomersVerificationDocument;
use common\models\CustomersVerificationDocumentLog;
use common\models\EinvoiceProfileUpdateQueue;
use frontend\models\CustomersMobileChangeApprovalForm;
use Yii;
use yii\base\InvalidCallException;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\ForbiddenHttpException;
use Exception;
use api\components\ApiGeneralCom;
use common\components\PipwaveApiCom;
use common\components\MerchantNotification;

class ProfileController extends Controller
{

    public $layout = 'main-col2';

    public $menu = 'LINK_PROFILE';

    const SESSION_KEY_LINK_TWO_FA = 'profile/enable-2fa/unlock',
        SESSION_KEY_TWO_FA_TMP_SECRET = 'profile/enable-2fa/secret';

    public function actionIndex()
    {
        $modelCountries = new Countries();
        $m_cust = Customers::findOne(['customers_id' => Yii::$app->user->id]);
        $cust_profile = CustomersProfile::getCustomerProfileInfo(Yii::$app->user->id);
        $f_cust = ProfileForm::fromModel($m_cust, $cust_profile);
        $m_addr = AddressBook::model()->getAddressInfo($m_cust->customers_default_address_id, $m_cust->customers_id);
        $f_addr = AddressForm::fromModel($m_addr);
        $f_data["config"]["ENTRY_IM_ACCOUNT_MAX_ENTRIES"] = Configuration::model()->getConfigValue('ENTRY_IM_ACCOUNT_MAX_ENTRIES');
        $f_data['imList'] = ArrayHelper::map(InstantMessageType::model()->getValidIM(), 'instant_message_type_id', 'instant_message_type_name');

        $_POST = GeneralComponent::purify($_POST);

        $cust_verify = CustomersDocumentIdVerification::findOne(['customers_id' => Yii::$app->user->id]);
        if (isset($cust_verify) && !empty($cust_verify->name_lock)) {
            //name locked, deny name change
            unset($_POST[$f_cust->formName()]['customers_firstname'], $_POST[$f_cust->formName()]['customers_lastname']);
        }

        $info_lock = $this->getCustomerInfoLock(CustomerInfoLock::DOB_LOCK_KEY);
        if (isset($info_lock) && ($info_lock->info_key == CustomerInfoLock::DOB_LOCK_KEY) && ($info_lock->locked == true)) {
            //name locked, deny name change
            unset($_POST[$f_cust->formName()]['customers_dob'], $_POST[$f_cust->formName()]['customers_dob']);
        }

        $phone_country = Countries::find()->where(['countries_id' => $m_cust->customers_country_dialing_code_id])->one();
        $isMyBuyer = !empty($phone_country['countries_international_dialing_code']) && $phone_country['countries_international_dialing_code'] == "60";
        $f_cust->tax_country_code = $isMyBuyer? "MY" : "";

        if (Yii::$app->getRequest()->getIsPost() && $f_cust->load($_POST) && $f_addr->load($_POST)) {
            if (!isset($f_addr->entry_zone_id)) {
                $f_addr->entry_zone_id = '';
            }

            if (isset($_POST[$f_cust->formName()]['customers_dob'])) {
                ; //Auto-load
            } else {
                if (empty($f_cust->dobYear) && empty($f_cust->dobMonth) && empty($f_cust->dobDay)) {
                    $f_cust->customers_dob = null;
                } else {
                    $f_cust->customers_dob = $f_cust->dobYear . "-" . $f_cust->dobMonth . "-" . $f_cust->dobDay . " 00:00:00";
                }
            }

            $f_cust->identity_number = $f_cust->nric_1 . $f_cust->nric_2 . $f_cust->nric_3;
            
            if ($f_cust->validate() && $f_addr->validate()) {
                $_error = false;

                // instant message
                $_input_im = array();
                foreach ($_POST as $key => $val) {
                    if (!empty($val) && preg_match("/^im_/", $key)) {
                        $_key = explode("_", $key);

                        if (isset($_key[1]) && array_key_exists($_key[1], $f_data['imList'])) {
                            $_input_im[] = array(
                                "tid" => $_key[1],
                                "val" => $val
                            );
                        }
                    }
                }

                if (count($_input_im) > $f_data["config"]["ENTRY_IM_ACCOUNT_MAX_ENTRIES"]) {
                    $_error = true;
                    $f_cust->addError('imList', Yii::t('profile', 'MSG_EXCEED_IM_LIMIT', array('SYS_MIN_LENGTH' => $f_data["config"]["ENTRY_IM_ACCOUNT_MAX_ENTRIES"])));
                }

                if (!$_error) {
                    $cust_log = $cust_mod = $addr_log = $addr_mod = array();

                    // profile
                    $cust_attr = array(
                        "customers_firstname" => $f_cust->customers_firstname,
                        "customers_lastname" => $f_cust->customers_lastname,
                        "customers_gender" => $f_cust->customers_gender,
                        "customers_dob" => !is_null($f_cust->customers_dob) ? $f_cust->customers_dob : null,
                    );

                    $cust_orig = Customers::find()->select(array_keys($cust_attr))->where(['customers_id' => Yii::$app->user->id])->one();
                    $cust_orig = array_intersect_key($cust_orig->attributes, $cust_attr);

                    Yii::$app->user->firstname = $f_cust->customers_firstname;
                    Yii::$app->user->lastname = $f_cust->customers_lastname;
                    Customers::updateAll($cust_attr, ['customers_id' => Yii::$app->user->id]);
                    CustomersInfo::model()->updateLastModify();

                    // user profile change log
                    $_diff = GeneralComponent::arrayDiff($cust_orig, $cust_attr);
                    if ($_diff) {
                        $log_obj = new LogFilesComponent(Yii::$app->user->id);
                        $cust_mod = $log_obj->detectChanges($_diff, $cust_attr);
                        $cust_log = $log_obj->constructLogMessage($cust_mod);
                    }

                    $einvoice_enabled = false;
                    // Einvoice profile
                    if ($isMyBuyer) {
                        if($f_cust->einvoice_type == 'business' || ($f_cust->einvoice_type == 'personal' && !empty($f_cust->identity_number))){
                            $cust_profile_attr = ['einvoice_type' => $f_cust->einvoice_type];

                            if ($f_cust->einvoice_type == 'business') {
                                $cust_profile_attr['company_name'] = $f_cust->company_name;
                                $cust_profile_attr['company_reg_number'] = $f_cust->company_reg_number;
                                $cust_profile_attr['tax_reg_number'] = $f_cust->tax_reg_number;
                                $cust_profile_attr['tax_country_code'] = $f_cust->tax_country_code;
                            }

                            if (!empty($f_cust->identity_number)) {
                                $cust_profile_attr['identity_number'] = $f_cust->identity_number;
                            }

                            if (!empty($cust_profile)) {
                                $profile_diff = GeneralComponent::arrayDiff($cust_profile, $cust_profile_attr);
                            } else{
                                $einvoice_enabled = true;
                                $profile_diff = $cust_profile_attr;
                            }
                            
                            if ($profile_diff){
                                foreach ($profile_diff as $key => $old_value) {
                                    if (!array_key_exists($key, $cust_profile)) {
                                        $cust_profile[$key] = "";
                                    }
                                    CustomersProfile::setCustomerProfileValue(Yii::$app->user->id, $key, $cust_profile_attr[$key]);
                                }

                                $log_obj = new LogFilesComponent(Yii::$app->user->id);
                                $profile_mod = $log_obj->detectChanges($cust_profile, $cust_profile_attr);
                                $profile_log = $log_obj->constructLogMessage($profile_mod);

                                // Add profile changes to the main log arrays
                                $cust_mod = array_merge($cust_mod, $profile_mod);
                                $cust_log = array_merge($cust_log, $profile_log);

                                // Create or update einvoice profile queue
                                EinvoiceProfileUpdateQueue::upsertEinvoiceProfile(
                                    Yii::$app->user->id,
                                    $f_cust,
                                    $f_addr,
                                    $phone_country
                                );
                            }
                        }
                    }

                    // user address
                    if (!empty($f_addr->entry_zone_id)) {
                        if (!Zones::find()->where([
                            'zone_id' => $f_addr->entry_zone_id,
                            'zone_country_id' => $f_addr->entry_country_id,
                        ])->exists()) {
                            $f_addr->entry_zone_id = "";
                        } else {
                            $f_addr->entry_state = "";
                        }
                    }

                    $addr_attr = array(
                        "entry_gender" => $f_cust->customers_gender,
                        "entry_firstname" => $f_cust->customers_firstname,
                        "entry_lastname" => $f_cust->customers_lastname,
                        "entry_street_address" => $f_addr->entry_street_address,
                        "entry_suburb" => $f_addr->entry_suburb,
                        "entry_postcode" => $f_addr->entry_postcode,
                        "entry_city" => $f_addr->entry_city,
                        "entry_state" => $f_addr->entry_state,
                        // "entry_country_id" => $f_addr->entry_country_id,
                        "entry_zone_id" => $f_addr->entry_zone_id
                    );
                    $addr_orig = AddressBook::model()->getUserAddress($m_cust->customers_default_address_id, array_keys($addr_attr));
                    AddressBook::updateAll($addr_attr, ['address_book_id' => $m_cust->customers_default_address_id]);

                    // user address change log
                    unset($addr_attr["entry_gender"], $addr_orig["entry_gender"], $addr_attr["entry_firstname"], $addr_orig["entry_firstname"], $addr_attr["entry_lastname"], $addr_orig["entry_lastname"]);
                    $_diff = GeneralComponent::arrayDiff($addr_orig, $addr_attr);
                    if ($_diff) {
                        if (isset($_diff["entry_country_id"]) && empty($_diff["entry_country_id"])) {
                            unset($_diff["entry_country_id"]);
                        }
                        if (isset($_diff["entry_zone_id"])) {
                            if (empty($_diff["entry_zone_id"])) {
                                unset($_diff["entry_zone_id"]);
                            } else {
                                $m_zone = Zones::findOne(['zone_id' => $_diff["entry_zone_id"]]);
                                if (isset($m_zone->zone_name)) {
                                    $_diff["entry_state"] = $m_zone->zone_name;
                                }
                                $m_zone = Zones::findOne(['zone_id' => $addr_attr["entry_zone_id"]]);
                                if (isset($m_zone->zone_name)) {
                                    $addr_attr["entry_state"] = $m_zone->zone_name;
                                }
                            }
                        }
                        $log_obj = new LogFilesComponent(Yii::$app->user->id);
                        $addr_mod = $log_obj->detectChanges($_diff, $addr_attr);
                        $addr_log = $log_obj->constructLogMessage($addr_mod);
                    }

                    $_mod = array_merge($cust_mod, $addr_mod);
                    $_log = array_merge($cust_log, $addr_log);
                    if (!empty($_log)) {
                        $log_obj = new LogFilesComponent(Yii::$app->user->id);
                        $change_log = $log_obj->contructChangesString($_mod, "");
                        CustomersInfo::model()->updateChangeMade($change_log);

                        $change_str = $einvoice_enabled ? "Einvoice enabled:\n" : "Changes made:\n";
                        for ($i = 0, $cnt = count($_log); $cnt > $i; $i++) {
                            if (is_array($_log[$i])) {
                                foreach ($_log[$i] as $field => $res) {
                                    if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                        $change_str .= $res['text'] . "\n";
                                    } else {
                                        $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                    }
                                }
                            }
                        }
                        $log_obj->insertCustomerHistoryLog($m_cust->customers_email_address, $change_str);
                    }

                    // instant message
                    InstantMessageAccounts::deleteAll(['customer_id' => Yii::$app->user->id]);
                    if ($_input_im) {
                        for ($i = 0, $cnt = count($_input_im); $cnt > $i; $i++) {
                            InstantMessageAccounts::model()->insert_instant_message($_input_im[$i]["tid"], $_input_im[$i]["val"]);
                        }
                    }

                    Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_PROFILE_UPDATE'));
                    return $this->redirect(['profile/index']);
                }
            }
            
            Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_FROM_ERROR'));
        }

        // date of birth
        foreach (range(1, 31) as $i) {
            $f_data["opt"]["dob_day"][$i] = $i;
        }
        foreach (range(1, 12) as $i) {
            $f_data["opt"]["dob_mth"][$i] = date('F', mktime(0, 0, 0, $i, 1, 0));
        }
        $curYear = date("Y");
        for ($i = ($curYear - 100), $max = $curYear + 1; $i < $max; $i++) {
            $f_data["opt"]["dob_yr"][$i] = $i;
        }

        // instant message
        $f_data["cur_im"] = array();
        if (isset($_input_im)) {
            if (!empty($_input_im)) {
                for ($i = 0, $cnt = count($_input_im); $cnt > $i; $i++) {
                    if (array_key_exists($_input_im[$i]["tid"], $f_data['imList'])) {
                        $f_data["cur_im"][] = array(
                            "tid" => $_input_im[$i]["tid"],
                            "type" => str_replace(" ", "_", strtolower($f_data['imList'][$_input_im[$i]["tid"]])),
                            "val" => $_input_im[$i]["val"]
                        );
                    }
                }
            }
        } else {
            $m_ima = InstantMessageAccounts::find()->where(["customer_id" => Yii::$app->user->id])->orderBy('instant_message_accounts_id')->all();
            if (count($m_ima)) {
                foreach ($m_ima as $m_num => $m_data) {
                    if (array_key_exists($m_data->instant_message_type_id, $f_data['imList'])) {
                        $f_data["cur_im"][] = array(
                            "tid" => $m_data->instant_message_type_id,
                            "type" => str_replace(" ", "_", strtolower($f_data['imList'][$m_data->instant_message_type_id])),
                            "val" => $m_data->instant_message_userid
                        );
                    }
                }
            }
        }

        $f_cust->customers_firstname = GeneralComponent::removeInvalidChars($f_cust->customers_firstname);
        $f_cust->customers_lastname = GeneralComponent::removeInvalidChars($f_cust->customers_lastname);
        
        $f_data['modelCustomers'] = $f_cust;
        $f_data['modelAddressBook'] = $f_addr;
        $f_data['allCountry'] = $modelCountries->getAllCountries(false);
        $f_data['cust_verify'] = $cust_verify;
        $f_data['info_lock'] = $info_lock;
        $f_data['isMyBuyer'] = $isMyBuyer;
        $f_data['hasEnabledEinvoice'] = isset($cust_profile['einvoice_type']) && !empty($cust_profile['einvoice_type']) ? true : false;

        return $this->render('index', $f_data);
    }

    public function actionSecurity()
    {
        $m_cust = Customers::findOne(['customers_id' => Yii::$app->user->id]);

        $cust_phone = '';

        if (isset($m_cust->customers_telephone) && $m_cust->customers_telephone != "") {
            $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
            $phoneInfo = $classCustomersInfoVerificationComponent->formatTelephone();
            $phone = substr($phoneInfo['telephone_number'], 0, -4) . '****';
            $cust_phone = '+' . $phoneInfo['country_international_dialing_code'] . ' ' . $phone;
        }
        $cust_email = $m_cust->customers_email_address;

        $email_changes_counter_monthly = $email_changes_counter_daily = 0;
        $time = time();
        $email_changes_model = CustomersStaticInfo::getInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER);
        if ($email_changes_model) {
            if (strtotime($email_changes_model->updated_date) < mktime(0, 0, 0, date('n', $time), 1, date('Y', $time))) {
                $email_changes_model->saveValue(0);
            } else {
                $email_changes_counter_monthly = $email_changes_model->value;
            }
        }
        $email_changes_model = CustomersStaticInfo::getInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER_DAILY);
        if ($email_changes_model) {
            if (strtotime($email_changes_model->updated_date) < mktime(0, 0, 0, date('n', $time), date('d', $time), date('Y', $time))) {
                $email_changes_model->saveValue(0);
            } else {
                $email_changes_counter_daily = $email_changes_model->value;
            }
        }

        $email_change_allowed = $email_changes_counter_daily < Yii::$app->params['EMAIL_CHANGE_PER_DAY'];

        $redirect = null;

        $update_password_form = new UpdatePasswordForm();
        $update_email_form = new UpdateEmailForm([
            'is_seller' => false,
            'email_change_allowed' => $email_change_allowed,
            'email_changes_counter' => $email_changes_counter_monthly,
            'email_changes_counter_daily' => $email_changes_counter_daily,
        ]);
        $update_phone_form = new UpdatePhoneFormLastFourDigit([
            'cust_phone' => $m_cust->customers_telephone,
        ]);
        $update_phone_form_two = new UpdatePhoneForm([
            'cust_phone' => $cust_phone,
            'customers_country_dialing_code_id' => $m_cust->customers_country_dialing_code_id,
        ]);
        $update_twofa_form = new UpdateTwoFaForm([
            'twofa_enabled' => \common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled(Yii::$app->user->id),
        ]);

        $m_cust = Customers::findOne(['customers_id' => Yii::$app->user->id]);
        $update_change_country = CustomersMobileChangeApprovalForm::fromModel($m_cust);
        $update_change_country->scenario = 'poa_required';

        $m_customer_mobile_change_approval = CustomersMobileChangeApproval::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);
        $has_pending_customer_approval = isset($m_customer_mobile_change_approval) && $m_customer_mobile_change_approval->approval_status == CustomersMobileChangeApprovalForm::STATUS_PENDING;

        if (\Yii::$app->request->isPost && ($action = \Yii::$app->request->post('type'))) {
            if (isset(Yii::$app->session['need_captcha'])) {
                return $this->redirect(['site/captcha']);
            }
            $_POST = GeneralComponent::purify($_POST);
            $action_map = [
                'change-password' => [
                    'form' => $update_password_form,
                    'function' => 'changePassword',
                ],
                'change-email' => [
                    'form' => $update_email_form,
                    'function' => 'changeEmail',
                ],
                'change-phone' => [
                    'form' => $update_phone_form_two,
                    'function' => 'changePhone',
                ],
                'change-2fa' => [
                    'form' => $update_twofa_form,
                    'function' => 'changeTwoFa'
                ]
            ];
            if ($action_map[$action]) {
                $form = $action_map[$action]['form'];
                $function = $action_map[$action]['function'];
                if ($form->load($_POST) && $form->validate()) {
                    $redirect = $this->$function($form);
                }
            }
        }

        if (!empty($redirect)) {
            return $redirect;
        }

        $this->menu = 'LINK_SECURITY';

        return $this->render('security', [
            'cust_phone' => $cust_phone,
            'cust_email' => $cust_email,
            'email_form_param' => [
                'model' => $update_email_form,
                'is_seller' => false,
                'change_allowed' => $email_change_allowed,
            ],
            'password_form_param' => [
                'model' => $update_password_form,
            ],
            'phone_form_param' => [
                'model' => $update_phone_form,
                'model2' => $update_phone_form_two,
                'model3' => $update_twofa_form,
                'cust_phone' => $cust_phone,
                'allCountry' => Countries::model()->getAllCountries(false),
                'address' => $update_change_country,
                'email_address' => $cust_email,
                'has_pending_customer_approval' => $has_pending_customer_approval,
            ],
            'twofa_form_param' => [
                'model' => $update_twofa_form,
                'is_enabled' => $update_twofa_form->twofa_enabled,
            ],
        ]);
    }

    /**
     * @param UpdatePasswordForm $form
     */
    protected function changePassword($form)
    {
        $userPassword = Customers::model()->customerPassword();
        if (Password::validatePassword($form->old_password, $userPassword)) {
            unset(Yii::$app->session['cc_changepwd']);
            Customers::model()->updatePassword(Password::encryptPassword($form->customers_password));
            $customerInfo = Customers::model()->getCustomerIdByEmail(Yii::$app->user->email);
            //email password changed
            $emailLayout = new EmailComponent();
            $f_data = array(
                'cname' => $customerInfo['customers_firstname'],
                'email' => Yii::$app->user->email
            );
            $mail_txt = [
                "content_html" => $emailLayout->setRenderEmailhtml('password_changed-html', $f_data),
                "content_text" => $emailLayout->setRenderEmailText('password_changed-text', $f_data),
            ];

            if (!empty($customerInfo)) {
                $classEmail = new EmailComponent();
                $classEmail->sendMail(
                    $customerInfo['customers_firstname'] . ' ' . $customerInfo['customers_lastname'],
                    Yii::$app->user->email,
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT'),
                    $mail_txt,
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
                );
            }
            Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_PASSWORD_CHANGE_SUCCESS') . ' ' . date('d M Y H:i:s'));

            \common\components\SSOCom::kickUser(\Yii::$app->user->id, \Yii::$app->session->id, (isset($_COOKIE['ogm']['uc']) ? $_COOKIE['ogm']['uc'] : ''));

            \Yii::$app->user->returnUrl = 'profile/security';
            return $this->redirect(['sso/login-all']); //Relogin all portals for this browser
        } else {
            Captcha::checkNeedCaptcha('sessionCheck', array(
                'returnUrl' => Url::to(['profile/security']),
                'sessionKey' => 'cc_changepwd',
                'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
            ));
            $form->addError('old_password', Yii::t('profile', 'MSG_INCORRECT_CURRENT_PASSWORD'));
        }
    }

    /**
     * @param UpdateEmailForm $form
     */
    protected function changeEmail($form)
    {
        $allow = $form->email_change_allowed;

        if (!$allow) {
            //Change not allowed, over quota
            Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_SELLER_CHANGE_EMAIL_EXCEED_LIMIT'));
            return;
        }
        $userPassword = Customers::model()->customerPassword();
        $allow = Password::validatePassword($form->customers_password, $userPassword);
        $classCustomerSecurityComponent = new CustomerSecurityComponent(Yii::$app->name, 'one_time_pin', Yii::$app->user->id);

        if (!$allow) {
            $form->addError('customers_password', Yii::t('profile', 'ERROR_INCORRECT_LOGIN_PASSWORD'));
        }

        $mail_validator =  new \common\components\MailValidator();
        $valid_email = $mail_validator->checkEmail($form->customers_email_address);

        if (!$valid_email) {
            Yii::$app->session->setFlash('danger', \Yii::t('verifyEmail', 'INVALID_EMAIL'));
            return;
        }

        if ($allow) {
            unset(Yii::$app->session['cc_changeemail']);
            $cust_attr = array(
                "customers_email_address" => $form->customers_email_address,
            );
            if (Yii::$app->user->email != $form->customers_email_address) {
                if ($form->token_answer) {
                    $validateAnswer = $classCustomerSecurityComponent->validateCustomerSecurityAnswer($form->token_answer);
                }

                $cust_attr['email_verified'] = CustomersInfoVerification::model()->searchRecord(Yii::$app->user->id, $form->customers_email_address, CustomersInfoVerification::$type1);

                //change email address
                $mail_obj = new EmailComponent();
                $mail_greet = Yii::t("dev", "EMAIL_GREETING", array("SYS_USERNAME" => Yii::$app->user->firstname));
                $mail_obj->sendMail(
                    Yii::$app->user->firstname,
                    Yii::$app->user->email,
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'EMAIL_PROFILE_CHANGE_EMAIL_SUBJECT'),
                    $mail_greet . Yii::t('profile', 'EMAIL_PROFILE_CHANGE_EMAIL', [
                        'SYS_USER_PREVIOUS_MAIL' => Yii::$app->user->email,
                        'SYS_USER_NEW_MAIL' => $form->customers_email_address,
                        'SYS_DATE' => date('Y-m-d H:i'),
                        'SYS_EMAIL_SIGNATURE' => Yii::t("dev", "EMAIL_SIGNATURE")
                    ]),
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
                );

                // insert verification record
                if (!$cust_attr["email_verified"]) {
                    $m_attr = [
                        'verify_try_turns' => 1,
                        'info_verified' => 1,
                        'customers_info_verification_mode' => 'A',
                        'customers_info_verification_date' => Helper::now()
                    ];

                    $custInfo = CustomersInfoVerification::findOne([
                        "customers_id" => Yii::$app->user->id,
                        "customers_info_value" => $form->customers_email_address,
                        "info_verification_type" => CustomersInfoVerification::$type1,
                    ]);

                    if(!$custInfo) {
                        $custInfo = new CustomersInfoVerification();
                        $custInfo->attributes = [
                            "customers_id" => Yii::$app->user->id,
                            "customers_info_value" => $form->customers_email_address,
                            "info_verification_type" => CustomersInfoVerification::$type1,
                        ];
                        $custInfo->save();
                    }

                    $custInfo->attributes = $m_attr;
                    $custInfo->save();
                    unset($m_attr);
                }
                unset($cust_attr["email_verified"]);
                $cust_orig = Customers::find()->select(array_keys($cust_attr))->where(['customers_id' => Yii::$app->user->id])->one();
                $cust_orig = array_intersect_key($cust_orig->attributes, $cust_attr);

                Yii::$app->user->email = $form->customers_email_address;
                Customers::model()->updateAll($cust_attr, ['customers_id' => Yii::$app->user->id]);
                CustomersInfo::model()->updateLastModify();

                $_diff = GeneralComponent::arrayDiff($cust_orig, $cust_attr);
                if ($_diff) {
                    $log_obj = new LogFilesComponent(Yii::$app->user->id);
                    $cust_mod = $log_obj->detectChanges($_diff, $cust_attr);
                    $cust_log = $log_obj->constructLogMessage($cust_mod);
                }
                if (!empty($cust_log)) {
                    $log_obj = new LogFilesComponent(Yii::$app->user->id);
                    $change_log = $log_obj->contructChangesString($cust_mod, "");
                    CustomersInfo::model()->updateChangeMade($change_log);

                    $change_str = "Changes made:\n";
                    for ($i = 0, $cnt = count($cust_log); $cnt > $i; $i++) {
                        if (is_array($cust_log[$i])) {
                            foreach ($cust_log[$i] as $field => $res) {
                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                    $change_str .= $res['text'] . "\n";
                                } else {
                                    $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                }
                            }
                        }
                    }
                    $log_obj->insertCustomerHistoryLog($form->customers_email_address, $change_str);

                    CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER, $form->email_changes_counter + 1);
                    CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER_DAILY, $form->email_changes_counter_daily + 1);
                }
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('profile', 'ERROR_SAME_EMAIL'));
                return;
            }

            Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_EMAIL_CHANGE_SUCCESS') . ' ' . date('d M Y H:i:s'));
            return $this->redirect(['profile/security', '#' => '']);
        } else {
            Captcha::checkNeedCaptcha('sessionCheck', array(
                'returnUrl' => Url::to(['profile/security']),
                'sessionKey' => 'cc_changeemail',
                'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
            ));
        }
    }

    //ajax for compare number
    public function actionValidateFourDigit()
    {
        $cust_static = \common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled(\Yii::$app->user->id);
        if ($cust_static) {
            throw new \yii\web\NotFoundHttpException('The requested page does not exist.');
        }

        $cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
        $cust_phone_last_num = substr($cust->customers_telephone, -4);
        $allow = $this->checkAttempt($cust->customers_id);
        if ($allow->customers_setting_value <= 5) {
            if (\Yii::$app->request->post('last_num') == $cust_phone_last_num) {
                // set phone num to session and return json status
                $session = \Yii::$app->session;
                $session['unlock_change_number'] = true;
                return json_encode([
                    'status' => 'success'
                ]);
            } else {
                //return status,message
                $allow->save();
                return json_encode([
                    'status' => 'failed',
                    'message' => Yii::t('profile', 'MSG_LAST_FOUR_DIGIT'),
                ]);
            }
        } else {
            return json_encode([
                'status' => 'failed',
                'message' => Yii::t('profile', 'MSG_LAST_FOUR_DIGIT_LIMIT'),
            ]);
        }
    }

    public function actionCheckChangeMobileCountry()
    {
        try {
            $custSetting = CustomersSetting::find()->select(['customers_setting_value', new Expression('updated_datetime < DATE_SUB(NOW(), INTERVAL ' . \Yii::$app->params['email_country_change.limit_timeframe'] . ') AS stale_attempt')]
            )->where([
                'customers_id' => \Yii::$app->user->id,
                'customers_setting_key' => \common\models\CustomersSetting::CHANGE_PHONE_COUNTRY,
            ])->asArray()->one();

            if (!empty($custSetting) && !$custSetting['stale_attempt']) {
                $m_customer_mobile_change_approval = CustomersMobileChangeApproval::findOne([
                    'customers_id' => Yii::$app->user->id,
                ]);
                if (isset($m_customer_mobile_change_approval) && $m_customer_mobile_change_approval->approval_status == CustomersMobileChangeApproval::APPROVAL_REJECTED) {
                    return json_encode([
                        'status' => 'fail',
                        'message' => \Yii::t('profile', 'ERROR_MOBILE_COUNTRY_CHANGE_REJECTED_QUOTA_REACHED'),
                    ]);
                } else {
                    return json_encode([
                        'status' => 'fail',
                        'message' => \Yii::t('profile', 'ERROR_MOBILE_COUNTRY_CHANGE_QUOTA_REACHED'),
                    ]);
                }
            }

            $new_country_details = Countries::getCountryDetails(\Yii::$app->request->post('new_country_id'));
            if (empty($new_country_details)) {
                throw new InvalidCallException();
            }

            $kyc_status = CustomersVerificationDocument::find()->select(['files_003_status'])->where([
                'customers_id' => \Yii::$app->user->id,
            ])->scalar();
            $user_kyc_verified = !empty($kyc_status) && $kyc_status == 1;

            $messages = [
                Yii::t('profile', 'DETAILS_CHANGE_PHONE_COUNTRY8'),
            ];

            if ($user_kyc_verified) {
                $messages[] = Yii::t('profile', 'DETAILS_CHANGE_PHONE_COUNTRY5');
            }
            $message = '';
            if ($messages) {
                $message = Html::beginTag("p", ['class' => 'content-mobileChange']);
                $c = 1;
                foreach ($messages as $m) {
                    $message .= $c . ') ' . $m . "<br/>\n";
                    $c++;
                }
                $message .= Html::endTag("p");
            }
            return json_encode([
                'status' => 'success',
                'skip_message' => empty($messages),
                'skip_poa' => !$user_kyc_verified,
                'message' => $message,
                'country_iso2' => $new_country_details['countries_iso_code_2'],
                'country_name' => $new_country_details['countries_name'],
                'country_dialing_code' => $new_country_details['countries_international_dialing_code'],
            ]);
        } catch (Exception $e) {
            return json_encode([
                'status' => 'fail',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function actionChangeMobileCountry()
    {
        if (!\Yii::$app->request->isPost) {
            throw new InvalidCallException();
        }
        if (!\Yii::$app->session->get('unlock_change_number')) {
            \Yii::$app->reporter->reportToAdminViaSlack('Missing unlock_change_number session ' . Yii::$app->user->id, 'Body : ' . json_encode($_POST));
            throw new ForbiddenHttpException();
        }
        $custSetting = CustomersSetting::find()->select(['customers_setting_value', new Expression('updated_datetime < DATE_SUB(NOW(), INTERVAL ' . \Yii::$app->params['email_country_change.limit_timeframe'] . ') AS stale_attempt')])->where(
            [
                'customers_id' => \Yii::$app->user->id,
                'customers_setting_key' => \common\models\CustomersSetting::CHANGE_PHONE_COUNTRY,
            ]
        )->asArray()->one();

        if (!empty($custSetting) && !$custSetting['stale_attempt']) {
            throw new ForbiddenHttpException();
        }
        $cust = Customers::findOne(['customers_id' => Yii::$app->user->id]);
        $kyc_status = CustomersVerificationDocument::find()->select(['files_003_status'])->where([
            'customers_id' => \Yii::$app->user->id,
        ])->scalar();
        $user_kyc_verified = !empty($kyc_status) && $kyc_status == 1;

        $form = CustomersMobileChangeApprovalForm::fromModel($cust);
        if ($user_kyc_verified) {
            $form->scenario = 'poa_required';
        }
        if ($form->load($_POST) && $form->validate()) {
            $classCustomerSecurityComponent = new CustomerSecurityComponent(Yii::$app->name, 'verify_phone_request', Yii::$app->user->id, null, $form->telephone, $form->dialing_code_id);
            $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
            if (isset($_POST['tokenAnswer']) && $_POST['tokenAnswer'] != '') {
                $answer = $_POST['tokenAnswer'];
                $validateAnswer = $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer, false);

                if ($validateAnswer['error'] === 0) {
                    $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($form->telephone, $form->dialing_code_id);
                    if (!$parse_result->is_valid_number) {
                        return json_encode([
                            'status' => 'failed',
                            'message' => Yii::t('smsToken', 'MSG_PHONE_ERROR'),
                        ]);
                    } else {
                        if (!$parse_result->is_country_match) {
                            return json_encode([
                                'status' => 'failed',
                                'message' => Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY'),
                            ]);
                        } else {
                            if ($form->dialing_code_id == $cust->customers_country_dialing_code_id) {
                                \Yii::$app->reporter->reportToAdminViaSlack('Customer country code id same with updated country id' . Yii::$app->user->id, 'Body : ' . json_encode($_POST));
                                Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_KYC_CHANGE_COUNTRY_ERROR'));
                                return json_encode([
                                    'status' => 'failed',
                                    'refresh' => true,
                                ]);
                            } else {
                                if ($form->scenario == 'poa_required' && (($form->dialing_code_id != $form->country_id) || empty($_FILES))) {
                                    \Yii::$app->reporter->reportToAdminViaSlack('Update Phone Country no file submitted' . Yii::$app->user->id, 'Body : ' . json_encode($_POST));
                                    Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_KYC_CHANGE_COUNTRY_ERROR'));
                                    return json_encode([
                                        'status' => 'failed',
                                        'refresh' => true,
                                    ]);
                                } else {
                                    $classPhoneVerificationComponent = new PhoneVerificationComponent();
                                    $modelCustomersInfo = new CustomersInfo();
                                    $classLogFilesComponent = new LogFilesComponent(Yii::$app->user->id);
                                    $phoneNumber = $parse_result->national_number;
                                    $countryDialId = $parse_result->dialing_code;
                                    if (!$classPhoneVerificationComponent->isMobileNumExist($phoneNumber, $form->dialing_code_id, Yii::$app->user->id, $countryDialId)) {
                                        //We are now using type `verify_phone_request`, so we must add data to verified list. This phone now binds to this user.
                                        $customers_info_verification_obj = new CustomersInfoVerification();
                                        $customers_info_verification_obj->updatePhoneVerifyInfo(Yii::$app->user->id, $countryDialId . $phoneNumber, '', 1);

                                        if ($user_kyc_verified) {
                                            $upload_poa_result = $this->uploadPOA();
                                            if ($upload_poa_result === true) {
                                                //Called again to finally clear off the OTP token
                                                $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer);
                                                $c_mca = CustomersMobileChangeApproval::findOne(['customers_id' => Yii::$app->user->id]);

                                                if (!isset($c_mca)) {
                                                    $update_change_country = new CustomersMobileChangeApproval();
                                                    $update_change_country->customers_id = Yii::$app->user->id;
                                                    $update_change_country->approval_status = CustomersMobileChangeApprovalForm::STATUS_PENDING;
                                                    $update_change_country->dialing_code_id = $form->dialing_code_id;
                                                    $update_change_country->country_id = $form->country_id;
                                                    $update_change_country->zone_id = $form->zone_id;
                                                    $update_change_country->suburb = $form->suburb;
                                                    $update_change_country->city = $form->city;
                                                    $update_change_country->state = $form->state;
                                                    $update_change_country->telephone = $phoneNumber;
                                                    $update_change_country->street_address = $form->street_address;
                                                    $update_change_country->postcode = $form->postcode;
                                                    $update_change_country->approval_status_datetime = null;
                                                    $update_change_country->created_datetime = date("Y-m-d H:i:s");
                                                    $update_change_country->updated_datetime = date("Y-m-d H:i:s");
                                                    if (!$update_change_country->save()) {
                                                        \Yii::$app->reporter->reportToAdminViaSlack('customers-mobile-change-approval save failed', $update_change_country);
                                                    }
                                                } else {
                                                    CustomersMobileChangeApproval::updateAll([
                                                        'approval_status' => CustomersMobileChangeApprovalForm::STATUS_PENDING,
                                                        'dialing_code_id' => $form->dialing_code_id,
                                                        'country_id' => $form->country_id,
                                                        'zone_id' => $form->zone_id,
                                                        'suburb' => $form->suburb,
                                                        'city' => $form->city,
                                                        'state' => $form->state,
                                                        'telephone' => $phoneNumber,
                                                        'street_address' => $form->street_address,
                                                        'postcode' => $form->postcode,
                                                        'approval_status_datetime' => null,
                                                        'updated_datetime' => date("Y-m-d H:i:s"),
                                                    ], [
                                                        'customers_id' => Yii::$app->user->id,
                                                    ]);
                                                }
                                            } else {
                                                if ($upload_poa_result == null) {
                                                    return json_encode([
                                                        'status' => 'failed',
                                                        'message' => Yii::t('general', 'ERROR_FILESIZE_EXCEED'),
                                                        'upload_issue' => true,
                                                    ]);
                                                } else {
                                                    return json_encode([
                                                        'status' => 'failed',
                                                        'message' => $upload_poa_result,
                                                        'upload_issue' => true,
                                                    ]);
                                                }
                                            }
                                        } else {
                                            //Called again to finally clear off the OTP token
                                            $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer);
                                            $fieldArr = ['customers_country_dialing_code_id', 'customers_telephone'];
                                            $conditionArr = ['customers_id' => Yii::$app->user->id];
                                            $customerInfo = Customers::model()->getCustomerByField($fieldArr, $conditionArr);
                                            if (isset($customerInfo)) {
                                                $cust_log = $cust_mod = $addr_log = $addr_mod = array();

                                                $addr_attr = array(
                                                    "entry_street_address" => '',
                                                    "entry_suburb" => '',
                                                    "entry_postcode" => '',
                                                    "entry_city" => '',
                                                    "entry_state" => '',
                                                    "entry_country_id" => $form->dialing_code_id,
                                                    "entry_zone_id" => null,
                                                );
                                                $addr_orig = AddressBook::model()->getUserAddress($cust->customers_default_address_id, array_keys($addr_attr));

                                                $oldRecord['customers_country_dialing_code_id'] = $customerInfo['customers_country_dialing_code_id'];
                                                $oldRecord['customers_telephone'] = $customerInfo['customers_telephone'];

                                                Customers::model()->updateCustomerPhone($form->dialing_code_id, $phoneNumber);
                                                AddressBook::updateAll($addr_attr, ['address_book_id' => $cust->customers_default_address_id]);
                                                $modelCustomersInfo->updateLastModify();
                                                $customerInfoNew = Customers::model()->getCustomerByField($fieldArr, $conditionArr);
                                                $newRecord['customers_country_dialing_code_id'] = $customerInfoNew['customers_country_dialing_code_id'];
                                                $newRecord['customers_telephone'] = $customerInfoNew['customers_telephone'];

                                                $_diff = GeneralComponent::arrayDiff($addr_orig, $addr_attr);
                                                if ($_diff) {
                                                    if (isset($_diff["entry_country_id"]) && empty($_diff["entry_country_id"])) {
                                                        unset($_diff["entry_country_id"]);
                                                    }
                                                    if (isset($_diff["entry_zone_id"])) {
                                                        if (empty($_diff["entry_zone_id"])) {
                                                            unset($_diff["entry_zone_id"]);
                                                        } else {
                                                            $m_zone = Zones::findOne(['zone_id' => $_diff["entry_zone_id"]]);
                                                            if (isset($m_zone->zone_name)) {
                                                                $_diff["entry_state"] = $m_zone->zone_name;
                                                            }
                                                            $m_zone = Zones::findOne(['zone_id' => $addr_attr["entry_zone_id"]]);
                                                            if (isset($m_zone->zone_name)) {
                                                                $addr_attr["entry_state"] = $m_zone->zone_name;
                                                            }
                                                        }
                                                    }
                                                    $addr_mod = $classLogFilesComponent->detectChanges($_diff, $addr_attr);
                                                    $addr_log = $classLogFilesComponent->constructLogMessage($addr_mod);
                                                }
                                                $_diff = GeneralComponent::arrayDiff($oldRecord, $newRecord);
                                                if ($_diff) {
                                                    $cust_mod = $classLogFilesComponent->detectChanges($_diff, $newRecord);
                                                    $cust_log = $classLogFilesComponent->constructLogMessage($cust_mod);
                                                }

                                                $_mod = array_merge($cust_mod, $addr_mod);
                                                $_log = array_merge($cust_log, $addr_log);
                                                if (!empty($_log)) {
                                                    $log_obj = new LogFilesComponent(Yii::$app->user->id);
                                                    $change_log = $log_obj->contructChangesString($_mod, "");
                                                    CustomersInfo::model()->updateChangeMade($change_log);

                                                    $change_str = "Mobile country changes made:\n";
                                                    for ($i = 0, $cnt = count($_log); $cnt > $i; $i++) {
                                                        if (is_array($_log[$i])) {
                                                            foreach ($_log[$i] as $field => $res) {
                                                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                                                    $change_str .= $res['text'] . "\n";
                                                                } else {
                                                                    $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                                                }
                                                            }
                                                        }
                                                    }
                                                    $log_obj->insertCustomerHistoryLog($cust->customers_email_address, $change_str);
                                                }

                                                $m_customer = Customers::findOne(['customers_id' => Yii::$app->user->id]);
                                                if ($oldRecord['customers_country_dialing_code_id'] != $m_customer->customers_country_dialing_code_id) {
                                                    \common\components\SSOCom::clientLocalizationInvalidate($m_customer);
                                                }
                                            }
                                        }
                                        $m_cust_setting = new CustomersSetting();
                                        $m_cust_setting->setCustomerSetting(\Yii::$app->user->id, CustomersSetting::CHANGE_PHONE_COUNTRY, '1');

                                        // 3rd party portal url
                                        $req_uri = \common\components\SSOCom::clientURLQuery();
                                        $fq_arr = array();
                                        parse_str($req_uri, $fq_arr);

                                        if (isset($fq_arr['origin']) && !empty($fq_arr['origin'])) {
                                            $f_data['fq_arr'] = $fq_arr;
                                        }
                                        $fq_arr = isset($f_data['fq_arr']) ? $f_data['fq_arr'] : null;

                                        if (isset($fq_arr['origin']) && !empty($fq_arr['origin'])) {
                                            $url = urldecode(urldecode($fq_arr['origin'])) . "?" . http_build_query($fq_arr) . '#';
                                            return json_encode([
                                                'status' => 'success',
                                                'next_url' => $url,
                                            ]);
                                        } else {
                                            return json_encode([
                                                'status' => 'success',
                                                'next_url' => 'refresh',
                                            ]);
                                        }
                                    } else {
                                        // Send Email
                                        $nun_owner_id = $classPhoneVerificationComponent->existMobileNumId($phoneNumber, $form->dialing_code_id, Yii::$app->user->id, $countryDialId);
                                        if ($nun_owner_id != null) {
                                            $mail_obj = new EmailComponent();
                                            $m_cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
                                            $orig_cust = Customers::findOne(['customers_id' => $nun_owner_id]);
                                            $mail_title = Yii::t('sso', 'Mobile Number Has Been Used');
                                            $f_data = array(
                                                'cname' => $m_cust->customers_firstname,
                                                'c_email' => $m_cust->customers_email_address,
                                            );
                                            $mail_txt = [
                                                "content_html" => $mail_obj->setRenderEmailhtml('mobile_number_been_used-html', $f_data),
                                                "content_text" => $mail_obj->setRenderEmailText('mobile_number_been_used-text', $f_data),
                                            ];

                                            if (!empty($mail_txt)) {
                                                $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                                                $mail_obj->sendMail(
                                                    $orig_cust->customers_firstname,
                                                    $orig_cust->customers_email_address,
                                                    $mail_sub,
                                                    $mail_txt,
                                                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
                                                    Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
                                                );
                                            }
                                        }

                                        $email = GeneralComponent::createEmailMask($orig_cust->customers_email_address);
                                        return json_encode([
                                            'status' => 'failed',
                                            'message' => Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED', ['c_email' => $email]),
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    Captcha::checkNeedCaptcha('sessionCheck', array(
                        'returnUrl' => Url::to(['profile/security']),
                        'sessionKey' => 'cc_changephone',
                        'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
                    ));

                    return json_encode([
                        'status' => 'failed',
                        'message' => $validateAnswer['text'],
                    ]);
                }
            } else {
                return json_encode([
                    'status' => 'failed',
                    'message' => Yii::t('profile', 'MSG_SECURITY_TOKEN'),
                ]);
            }
        } else {
            if (!empty($form->getErrors())) {
                return json_encode([
                    'status' => 'failed',
                    'message' => $form->getErrors(),
                ]);
            } else {
                return json_encode([
                    'status' => 'failed',
                    'message' => Yii::t('general', 'ERROR_FILESIZE_EXCEED'),
                ]);
            }
        }
    }

    private function checkAttempt($id)
    {
        $custSetting = \common\models\CustomersSetting::findOne(['customers_id' => $id, 'customers_setting_key' => \common\models\CustomersSetting::FOUR_DIGIT_KEY]);

        if (!empty($custSetting)) {
            //clear the attempt to 0 after 24hours
            $time = time();
            if (strtotime($custSetting->updated_datetime) < mktime(0, 0, 0, date('n', $time), date('j', $time) - 1, date('Y', $time))) {
                $custSetting->saveValue(0);
            }
        } else {
            $custSetting = new \common\models\CustomersSetting();
            $custSetting->customers_id = $id;
            $custSetting->customers_setting_key = \common\models\CustomersSetting::FOUR_DIGIT_KEY;
            $custSetting->customers_setting_value = 0;
            $custSetting->created_datetime = date("Y-m-d H:i:s");
        }

        $custSetting->customers_setting_value = $custSetting->customers_setting_value + 1;
        $custSetting->updated_datetime = date("Y-m-d H:i:s");
        return $custSetting;
    }

    /**
     * @param UpdatePhoneForm $form
     */
    protected function changePhone($form)
    {
        $cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
        $cust_phone_last_num = substr($cust->customers_telephone, -4);
        // check last four digit
        //change to change mobile lock 
        if (\Yii::$app->session->get('unlock_change_number')) {
            $classCustomerSecurityComponent = new CustomerSecurityComponent(Yii::$app->name, 'verify_phone_request', Yii::$app->user->id, null, $form->customers_telephone, $form->customers_country_dialing_code_id);
            $classPhoneVerificationComponent = new PhoneVerificationComponent();
            $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
            $modelCustomersInfo = new CustomersInfo();
            $classLogFilesComponent = new LogFilesComponent(Yii::$app->user->id);
            if (isset($_POST['tokenAnswer']) && $_POST['tokenAnswer'] != '') {
                $answer = $_POST['tokenAnswer'];
                $form->token_answer = $answer;
                $validateAnswer = $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer, false);
                if ($validateAnswer['error'] === 0) {
                    $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($form->customers_telephone, $form->customers_country_dialing_code_id);
                    if (!$parse_result->is_valid_number) {
                        $form->addError('token_answer', Yii::t('smsToken', 'MSG_PHONE_ERROR'));
                        Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_FROM_ERROR'));
                    } else {
                        if (!$parse_result->is_country_match) {
                            $form->addError('token_answer', Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY'));
                            Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_FROM_ERROR'));
                        } else {
                            $phoneNumber = $parse_result->national_number;
                            $countryDialId = $parse_result->dialing_code;
                            if (!$classPhoneVerificationComponent->isMobileNumExist($phoneNumber, $form->customers_country_dialing_code_id, Yii::$app->user->id, $countryDialId)) {
                                //Called again to finally clear off the OTP token
                                $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer);
                                $fieldArr = ['customers_country_dialing_code_id', 'customers_telephone', 'customers_email_address'];
                                $conditionArr = ['customers_id' => Yii::$app->user->id];
                                $customerInfo = Customers::model()->getCustomerByField($fieldArr, $conditionArr);
                                if (isset($customerInfo)) {
                                    $oldRecord['customers_country_dialing_code_id'] = $customerInfo['customers_country_dialing_code_id'];
                                    $oldRecord['customers_telephone'] = $customerInfo['customers_telephone'];

                                    Customers::model()->updateCustomerPhone($form->customers_country_dialing_code_id, $phoneNumber);
                                    $modelCustomersInfo->updateLastModify();

                                    $customerInfo = Customers::model()->getCustomerByField($fieldArr, $conditionArr);
                                    $newRecord['customers_country_dialing_code_id'] = $customerInfo['customers_country_dialing_code_id'];
                                    $newRecord['customers_telephone'] = $customerInfo['customers_telephone'];

                                    $customerChangesArray = $classLogFilesComponent->detectChanges($oldRecord, $newRecord);
                                    $customerChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerChangesArray);

                                    if (count($customerChangesFormattedArray)) {
                                        $changesStr = 'Changes made:' . "\n";

                                        for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                                            if (count($customerChangesFormattedArray[$i])) {
                                                foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                                                    if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                                        $changesStr .= $res['text'] . "\n";
                                                    } else {
                                                        $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                                    }
                                                }
                                            }
                                        }

                                        $classLogFilesComponent->insertCustomerHistoryLog($customerInfo['customers_email_address'], $changesStr);
                                    }

                                    //We are now using type `verify_phone_request`, so we must add data to verified list. This phone now binds to this user.
                                    $customers_info_verification_obj = new CustomersInfoVerification();
                                    $customers_info_verification_obj->updatePhoneVerifyInfo(Yii::$app->user->id, $countryDialId . $phoneNumber, '', 1);
                                    // check dialing_code same with coutry_code
                                    $m_customer = Customers::findOne(['customers_id' => Yii::$app->user->id]);

                                    if ($oldRecord['customers_country_dialing_code_id'] != $m_customer->customers_country_dialing_code_id) {
                                        \common\components\SSOCom::clientLocalizationInvalidate($m_customer);
                                    }
                                    // 3rd party portal url
                                    $req_uri = \common\components\SSOCom::clientURLQuery();
                                    $fq_arr = array();
                                    parse_str($req_uri, $fq_arr);

                                    if (isset($fq_arr['origin']) && !empty($fq_arr['origin'])) {
                                        $f_data['fq_arr'] = $fq_arr;
                                    }
                                    $fq_arr = isset($f_data['fq_arr']) ? $f_data['fq_arr'] : null;

                                    if (isset($fq_arr['origin']) && !empty($fq_arr['origin'])) {
                                        $url = urldecode(urldecode($fq_arr['origin'])) . "?" . http_build_query($fq_arr) . '#';
                                        return $this->redirect($url);
                                    } else {
                                        Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_MOBILE_PHONE_CHANGE_SUCCESS') . ' ' . date('d M Y H:i:s'));
                                        return $this->redirect(['profile/security', '#' => '']);
                                    }
                                }
                            } else {
                                // Send Email
                                $nun_owner_id = $classPhoneVerificationComponent->existMobileNumId($phoneNumber, $form->customers_country_dialing_code_id, Yii::$app->user->id, $countryDialId);
                                if ($nun_owner_id != null) {
                                    $mail_obj = new EmailComponent();
                                    $m_cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
                                    $orig_cust = Customers::findOne(['customers_id' => $nun_owner_id]);
                                    $mail_title = Yii::t('sso', 'Mobile Number Has Been Used');
                                    $f_data = array(
                                        'cname' => $m_cust->customers_firstname,
                                        'c_email' => $m_cust->customers_email_address,
                                    );
                                    $mail_txt = [
                                        "content_html" => $mail_obj->setRenderEmailhtml('mobile_number_been_used-html', $f_data),
                                        "content_text" => $mail_obj->setRenderEmailText('mobile_number_been_used-text', $f_data),
                                    ];

                                    if (!empty($mail_txt)) {
                                        $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                                        $mail_obj->sendMail(
                                            $orig_cust->customers_firstname,
                                            $orig_cust->customers_email_address,
                                            $mail_sub,
                                            $mail_txt,
                                            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
                                            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
                                        );
                                    }
                                }

                                $email = GeneralComponent::createEmailMask($orig_cust->customers_email_address);
                                $form->last_four_passed = true;
                                $form->addError('token_answer', Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED', ['c_email' => $email]));
                                Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_FROM_ERROR'));
                            }
                        }
                    }
                } else {
                    Captcha::checkNeedCaptcha('sessionCheck', array(
                        'returnUrl' => Url::to(['profile/security']),
                        'sessionKey' => 'cc_changephone',
                        'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
                    ));
                    $form->last_four_passed = true;
                    $form->addError('token_answer', $validateAnswer['text']);
                }
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('profile', 'MSG_SECURITY_TOKEN'));
            }
        }
    }

    /**
     * @param UpdateTwoFaForm $form
     */
    protected function changeTwoFa($form)
    {
        if ($form->validate()) {
            unset(Yii::$app->session['cc_changeemail']);
            if ($form->twofa_enabled) {
                \common\components\TwoFactorAuth::removeUserTwoFactorAuth(Yii::$app->user->id);
                //deactivated
                $mail_obj = new EmailComponent();
                $mail_title = Yii::t('profile', 'Two-factor authentication de-activated');
                $m_cust = Customers::findOne(['customers_email_address' => Yii::$app->user->email, 'customers_status' => 1]);

                $f_data = array(
                    'cname' => $m_cust->customers_firstname,
                );
                $mail_txt = [
                    "content_html" => $mail_obj->setRenderEmailhtml('2fa_deactivated-html', $f_data),
                    "content_text" => $mail_obj->setRenderEmailText('2fa_deactivated-text', $f_data),
                ];

                if (!empty($mail_txt)) {
                    $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                    $mail_obj->sendMail($m_cust->customers_firstname, Yii::$app->user->email, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
                }

                Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_TWO_FACTOR_DISABLED') . ' ' . date('d M Y H:i:s'));
                return $this->redirect(['profile/security', '#' => '']);
            } else {
                Yii::$app->session->set(static::SESSION_KEY_LINK_TWO_FA, time() + (15 * 60)); //Unlock two FA for 15 minutes
                Yii::$app->session->set(static::SESSION_KEY_TWO_FA_TMP_SECRET, \common\components\TwoFactorAuth::getInstance()->createSecret()); //With this secret key
                return $this->redirect(['profile/link-twofa']);
            }
        } else {
            Captcha::checkNeedCaptcha('sessionCheck', array(
                'returnUrl' => Url::to(['profile/security']),
                'sessionKey' => 'cc_changeemail',
                'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
            ));
        }
    }

    public function actionLinkTwofa()
    {
        $unlock = Yii::$app->session->get(static::SESSION_KEY_LINK_TWO_FA);
        $secret = Yii::$app->session->get(static::SESSION_KEY_TWO_FA_TMP_SECRET);
        if (!isset($unlock, $secret) || $unlock < time()) {
            Yii::$app->session->remove(static::SESSION_KEY_LINK_TWO_FA);
            Yii::$app->session->remove(static::SESSION_KEY_TWO_FA_TMP_SECRET);
            return $this->redirect(['profile/security', '#' => '']);
        }

        $two_fa = \common\components\TwoFactorAuth::getInstance();

        if (Yii::$app->request->isPost) {
            if (($code = Yii::$app->request->post('code')) && $two_fa->verifyCode($secret, $code)) {
                \common\components\TwoFactorAuth::setUserTwoFactorAuth(\Yii::$app->user->id, $secret);
                Yii::$app->session->remove(static::SESSION_KEY_LINK_TWO_FA);
                Yii::$app->session->remove(static::SESSION_KEY_TWO_FA_TMP_SECRET);
                //activated email
                $mail_obj = new EmailComponent();
                $mail_title = Yii::t('profile', 'Two-factor authentication activated');
                $m_cust = Customers::findOne(['customers_email_address' => Yii::$app->user->email, 'customers_status' => 1]);

                $f_data = array(
                    'cname' => $m_cust->customers_firstname,
                );
                $mail_txt = [
                    "content_html" => $mail_obj->setRenderEmailhtml('2fa_activated-html', $f_data),
                    "content_text" => $mail_obj->setRenderEmailText('2fa_activated-text', $f_data),
                ];

                if (!empty($mail_txt)) {
                    $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                    $mail_obj->sendMail($m_cust->customers_firstname, Yii::$app->user->email, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
                }

                Yii::$app->session->setFlash('success', Yii::t('profile', 'MSG_TWO_FACTOR_ENABLED') . ' ' . date('d M Y H:i:s'));
                return $this->redirect(['profile/security', '#' => '']);
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('general', 'ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH'));
            }
        }

        $qrcode = $two_fa->getQRCodeImageAsDataUri(Yii::$app->user->email, $secret);
        $plaincode = $secret;

        $this->menu = 'LINK_SECURITY';

        return $this->render('2fa', ['qrcode' => $qrcode, 'plaincode' => $plaincode]);
    }

    public function actionChangePhoneTwofa()
    {
        //checking on 2fa token
        $secret = \common\components\TwoFactorAuth::getUserTwoFactorAuthSecret(\Yii::$app->user->id);

        $two_fa = \common\components\TwoFactorAuth::getInstance();
        if (Yii::$app->request->isPost) {
            $code = Yii::$app->request->post('code');
            if ($code && $two_fa->verifyCode($secret, $code)) {
                $session = \Yii::$app->session;
                $session['unlock_change_number'] = true;
                return json_encode([
                    'status' => 'success'
                ]);
            } else {
                return json_encode([
                    'status' => 'failed',
                    'message' => Yii::t('general', 'ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH'),
                    'code' => $code,
                ]);
            }
        }
    }

    public function actionLoadState()
    {
        $modelZones = new Zones();

        $stateInfo = array();
        $countryId = '0';

        $_POST = GeneralComponent::purify($_POST);

        if (isset($_POST['country'])) {
            $countryId = (int)$_POST['country'];
        }

        $stateList = $modelZones->getStateList($countryId);
        foreach ($stateList as $state) {
            $stateInfo[$state->zone_id] = utf8_encode($state->zone_name);
        }

        if (count($stateInfo) != 0) {
            $content = Html::dropDownList('state', '', $stateInfo, ['id' => 'state', 'prompt' => 'State', 'style' => 'width:192px;']);
            $type = '1';
        } else {
            $content = Html::textInput('state', '', ['id' => 'state', 'placeholder' => 'State', 'style' => 'width:177px;']);
            $type = '0';
        }
        $content = iconv("UTF-8", "UTF-8//IGNORE", $content);
        echo Json::encode(array(
            'content' => $content,
            'type' => $type,
        ));
    }

    protected function getCustomerInfoLock($info_key)
    {
        $lock_info = CustomerInfoLock::findOne(['customer_id' => Yii::$app->user->id, 'info_key' => $info_key]);
        if (isset($lock_info) || !empty($lock_info->info_lock)) {
            return $lock_info;
        }
        return null;
    }

    protected function uploadPOA()
    {
        $m_cvd = CustomersVerificationDocument::findOne([
            'customers_id' => Yii::$app->user->id,
        ]);

        foreach ($_FILES as $_key => $_val) {
            $file_locked = str_replace("doc_", "", $_key);
            $m_files = "files_" . $file_locked;
            $m_files_locked = "files_" . $file_locked . "_locked";
            $m_files_status = "files_" . $file_locked . "_status";

            if ($_val["size"] > 0) {
                $_val["type"] = strtolower(pathinfo($_val["name"], PATHINFO_EXTENSION));

                $up_obj = new \frontend\components\UploadCom($_key);
                $up_obj->set_encryption(Yii::$app->params["VERIFICATION_DOC"]["ENCRYPT_FILE"]);
                $up_obj->set_extensions(Yii::$app->params["VERIFICATION_DOC"]["FILE_TYPE"]);
                $up_obj->set_maxsize(Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"] * 1024);

                $up_obj->set_bucket(Yii::$app->params["VERIFICATION_DOC"]["S3_BUCKET"]);
                $up_obj->set_destination(Yii::$app->params["VERIFICATION_DOC"]["S3_DESTINATION"] . $file_locked . '/' . date('Ym') . '/' . Yii::$app->user->id . '/');
                $up_obj->set_filename(date("YmdHis") . "." . $_val["type"]);

                $up_obj->set_upload_destination(Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]));
                $up_obj->set_upload_destination_filename(Yii::$app->user->id . "_" . $file_locked . "_" . date("YmdHis") . "." . $_val["type"]);

                if ($up_obj->save()) {
                    $upload_success[$file_locked] = Yii::t("verificationSubmission", "TEXT_VERIFICATION_TYPE_" . $file_locked);

                    $m_cvd->$m_files = $up_obj->filename;
                    $m_cvd->$m_files_locked = 1;
                    $m_cvd->$m_files_status = 0;
                    $m_cvd->save();

                    $m_attr = [
                        'log_users_id' => Yii::$app->user->id,
                        'log_users_type' => 'customers',
                        'log_customers_id' => Yii::$app->user->id,
                        'log_docs_id' => $file_locked,
                        'log_IP' => GeneralComponent::getIpAddress(),
                        'log_datetime' => date("Y-m-d H:i:s"),
                        'log_filename' => $up_obj->filename,
                        'log_action' => 'UPLOAD'
                    ];
                    CustomersVerificationDocumentLog::model()->saveNewRecord($m_attr);
                    unset($m_attr);
                    return true;
                } else {
                    $output_message = $up_obj->get_output_messages();
                    if (isset($output_message[0]["message"])) {
                        return $output_message[0]["message"];
                    } else {
                        return Yii::t('profile', 'MSG_FROM_ERROR');
                    }
                }
                unset($up_obj);
            }
        }
    }

    protected function sendEmailChangeVerification($cid, $email)
    {
        //change email address
        $action = "verify";


        $emailExist = CustomersInfoVerification::model()->searchRecord($cid, $email, CustomersInfoVerification::$type1);
        $serialNumber = ApiGeneralCom::createRandomValue(12);
        if (empty($emailExist)) {
            $infoVerificationArray = array(
                'customers_id' => $cid,
                'customers_info_value' => $email,
                'serial_number' => $serialNumber,
                'verify_try_turns' => 0,
                'info_verified' => 0,
                'info_verification_type' => CustomersInfoVerification::$type1,
                'customers_info_verification_mode' => 'A',
            );

            CustomersInfoVerification::model()->saveCustomerInfoVerification($infoVerificationArray);
        } else {
            $customer_info_verification = CustomersInfoVerification::find()->where([
                    "customers_id" => $cid,
                    "customers_info_value" => $email,
                    "info_verification_type" => CustomersInfoVerification::$type1
                ]
            )->one();
            $customer_info_verification->serial_number = $serialNumber;
            $customer_info_verification->info_verified = 0;
            $customer_info_verification->customers_info_verification_date = 0;
            $customer_info_verification->save(true, ['serial_number', 'info_verified', 'customers_info_verification_date']);
        }


        $verification_url = Url::base(true) . Url::toRoute([
                'verify-email/change-email',
                'action' => $action,
                'serialNumber' => $serialNumber,
                'email' => $email,
                'signature' => md5($action . $email . $serialNumber . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY'])
            ]);

        $mail_obj = new EmailComponent();
        $mail_obj->sendMail(
            Yii::$app->user->firstname,
            $email,
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'VERIFY_NEW_EMAIL_SUBJECT'),
            Yii::t('profile', 'VERIFY_NEW_EMAIL_BODY', [
                'FIRSTNAME' => Yii::$app->user->firstname,
                'NEW_EMAIL_ADDRESS' => $email,
                'EMAIL_CHANGE_URL' => $verification_url,
                'EMAIL_SIGNATURE' => Yii::t("dev", "EMAIL_SIGNATURE")
            ]),
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"],
            Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]
        );
    }
}
