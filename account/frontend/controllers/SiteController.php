<?php
namespace frontend\controllers;

use frontend\components\Captcha;
use common\components\GeneralComponent;
use frontend\components\Controller;
use Yii;
use yii\filters\AccessControl;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\ViewAction;

class SiteController extends Controller
{
    public $enableCsrfValidation = false;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['index', 'page', 'captcha', 'maintenance', 'switch-to-old'],
                        'allow' => true,
                        'roles' => ['*'],
                    ],
                    [
                        'actions' => ['get-menu-id'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['?'],
                        'denyCallback' => [$this, '_redirectIndex'],
                    ]
                ],
            ],
        ]);
    }

    public function actions() {
        return array_merge(parent::actions(), [
            'page' => [
                'class' => ViewAction::className(),
            ],
        ]);
    }

    public function beforeAction($action) {
        if ($action->id == "page") {
            Yii::$app->layout = "page";
        }
        return true;
    }

    public function _redirectIndex() {
        if (Yii::$app->request->isAjax) {
            return Json::encode(array(
                'status' => 0,
            ));
        } elseif (isset($_GET['atab'])) {
            if (isset($_GET['origin']) && $_GET['origin'] != '') {
                return '<script type="text/javascript">window.onload = function(){top.location.href="' . $_GET['origin'] . '";}</script>';
            }
            return;
        } else {
            Yii::$app->user->setReturnUrl(Yii::$app->request->getUrl());
            return $this->redirect(['sso/index']);
        }
    }

    /**
     * This is the default 'index' action that is invoked
     * when an action is not explicitly requested by users.
     */

    public function actionIndex() {
        $getController = isset($_GET['c']) ? $_GET['c'] : '';
        $getAction = isset($_GET['a']) ? $_GET['a'] : '';

        if ($getController == 'verifyEmail' && $getAction == 'index' && isset($_REQUEST['p'])) {
            $params = GeneralComponent::parseURLGetParam($_REQUEST['p']);
            return $this->redirect(array_merge(['verify-email/index'], $params));
        }

        if (Yii::$app->user->isGuest) {
            //When redirecting user to login page, set the return url so that user can come back when they login!
            Yii::$app->user->setReturnUrl(Yii::$app->request->getUrl());
            return $this->redirect(array_merge([Yii::$app->user->loginUrl[0]], (isset($_GET['origin']) ? ['origin' => $_GET['origin']] : [])));
        } else {
            if (!isset($_REQUEST['c'])) {
                echo Html::script("top.location.href='" . Url::to(['overview/index']) . "';");
                return;
            }

            return $this->render('index');
        }
    }

    public function actionError() {
        if ($error = Yii::$app->errorHandler->exception) {
            if (Yii::$app->request->isAjax) {
                return $error->getMessage();
            } else {
                $this->layout = 'main-col1';
                $type = ($error instanceof \yii\web\HttpException && $error->statusCode >= 400 && $error->statusCode < 500) ? '404' : '500';
                return $this->render('error404', ['type' => $type, 'f_message' => $error->getMessage()]);
            }
        } else {
            $this->layout = 'main-col1';
            return $this->render('error404', ['type' => '500', 'f_message' => ""]);
        }
    }

    public function actionCaptcha() {
        $f_data = array();

        $captcha_com = new Captcha();

        if (Yii::$app->getRequest()->getIsPost()) {
            $f_data['status'] = 2;
            $return_url = $captcha_com->verify_response($_POST);
            if ($return_url !== false) {
                return $this->redirect($return_url);
            } else {
                \Yii::$app->session->setFlash('danger', Yii::t("site", "ERROR_RECAPTCHA_INVALID_CODE"));
            }
        }

        $captcha = $captcha_com->load_html();

        $f_data['captcha'] = $captcha;

        Yii::$app->layout = 'card';
        return $this->render('captcha', $f_data);
    }

    public function actionMaintenance() {
        if (true || Yii::$app->params["GENERAL_CONFIG"]["DOWN_FOR_MAINTENANCE"]) {
            Yii::$app->layout = 'card';
            return $this->render('maintenance');
        } else {
            return $this->redirect(['/']);
        }
    }
}
