<?php

namespace frontend\controllers;

use common\components\CustomerSecurityComponent;
use common\components\EmailComponent;
use common\models\Customers;
use common\models\CustomersOtp;
use common\models\CustomersSetting;
use frontend\components\Captcha;
use frontend\components\Controller;
use Yii;
use yii\helpers\Url;
use yii\web\Response;

class SmsTokenController extends Controller {
    
    const DATA_ORIGIN_DATABASE = 'database',
            DATA_ORIGIN_INPUT = 'input';
    
    const ERROR_NEED_CAPTCHA = 'need-captcha';
    
    protected $data_origin;
    
    public function init() {
        parent::init();
        $this->data_origin = static::DATA_ORIGIN_DATABASE;
    }

    public function beforeAction($action) {
        if (parent::beforeAction($action)) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            return true;
        }
        return false;
    }

    public function actionRequestSmsToken() {
        $seconds_till_resend = -1;

        $resCode = 0;
        $resText = '';
        $error = false;

        $validate_result = $this->validateForRequestSmsToken();
        if (!empty($validate_result['error'])) {
            switch ($validate_result['error']) {
                case static::ERROR_NEED_CAPTCHA:
                    $captcha_com = new Captcha(false);
                    $captcha_load = $captcha_com->load_html();
                    return [
                        'resCode' => -1,
                        'captcha_html' => $captcha_load['html'],
                        'captcha_js' => $captcha_load['js'],
                        'captcha_note' => $captcha_load['note'],
                    ];
            }
            return [
                'resCode' => $resCode,
                'message' => $resText,
                'secondsTillResend' => $seconds_till_resend,
            ];
        } else if (empty($this->data_origin)) {
            $error = true;
        } else {
            $classCustomerSecurityComponent = $this->initCustomerSecurityComForRequestSmsToken($validate_result);
            if ($classCustomerSecurityComponent) {
                $phone_info = $classCustomerSecurityComponent->getCustomerPhoneInfo();
                if (empty($phone_info)) {
                    $error = true;
                } else if (!$phone_info['is_valid_number']) {
                    $error = true;
                } else if (!$phone_info['is_country_match']) {
                    $error = true;
                    $resText = Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY');
                } else {
                    $requestResult = $classCustomerSecurityComponent->requestSecurityToken();
                    $requestResult['res_code'] == FALSE ? 0 : $requestResult['res_code'];
                    $resCode = ((int) $requestResult['res_code'] > 0 ? $requestResult['res_code'] : 0);
                    $resText = ((int) $requestResult['res_code'] < 0 ? str_replace(array('.', '��'), '', $requestResult['res_text']) : $requestResult['res_text']);
                    $seconds_till_resend = $requestResult['seconds_till_resend'];
                }
            } else {
                $error = true;
            }
        }
        
        if ($error && empty($resText)) {
            if ($this->data_origin == static::DATA_ORIGIN_INPUT) {
                $resText = Yii::t('smsToken', 'TEXT_INVALID_MOBILE');
            } else {
                $resText = Yii::t('smsToken', 'TEXT_PLEASE_SETUP_MOBILE', ['SYS_PROFILE_LINK' => Url::to(['profile/security', '#' => 'change-phone'])]);
            }
        }

        return [
            'resCode' => $resCode,
            'message' => $resText,
            'secondsTillResend' => $seconds_till_resend,
        ];
    }

    public function actionRequestSmsTokenResend() {
        $seconds_till_resend = -1;
        
        $resCode = 0;
        $resText = '';
        $error = false;

        $validate_result = $this->validateForRequestSmsToken();
        if (!empty($validate_result['error'])) {
            switch ($validate_result['error']) {
                case static::ERROR_NEED_CAPTCHA:
                    $captcha_com = new Captcha(false);
                    $captcha_load = $captcha_com->load_html();
                    return [
                        'resCode' => -1,
                        'captcha_html' => $captcha_load['html'],
                        'captcha_js' => $captcha_load['js'],
                        'captcha_note' => $captcha_load['note'],
                    ];
            }
            return [
                'resCode' => $resCode,
                'message' => $resText,
                'secondsTillResend' => $seconds_till_resend,
            ];
        } else if (empty($this->data_origin)) {
            $error = true;
        } else {
            $classCustomerSecurityComponent = $this->initCustomerSecurityComForRequestSmsToken($validate_result);
            if ($classCustomerSecurityComponent) {
                $phone_info = $classCustomerSecurityComponent->getCustomerPhoneInfo();
                if (empty($phone_info)) {
                    $error = true;
                } else if (!$phone_info['is_valid_number']) {
                    $error = true;
                } else if (!$phone_info['is_country_match']) {
                    $error = true;
                    $resText = Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY');
                } else {
                    $requestResult = $classCustomerSecurityComponent->requestSecurityTokenResend();
                    $requestResult['res_code'] == FALSE ? 0 : $requestResult['res_code'];
                    $resCode = ((int) $requestResult['res_code'] > 0 ? $requestResult['res_code'] : 0);
                    $resText = ((int) $requestResult['res_code'] < 0 ? str_replace(array('.', '��'), '', $requestResult['res_text']) : $requestResult['res_text']);
                    $seconds_till_resend = $requestResult['seconds_till_resend'];
                }
            } else {
                $error = true;
            }
        }
        
        if ($error && empty($resText)) {
            if ($this->data_origin == static::DATA_ORIGIN_INPUT) {
                $resText = Yii::t('smsToken', 'TEXT_INVALID_MOBILE');
            } else {
                $resText = Yii::t('smsToken', 'TEXT_PLEASE_SETUP_MOBILE', ['SYS_PROFILE_LINK' => Url::to(['profile/security', '#' => 'change-phone'])]);
            }
        }

        return [
            'resCode' => $resCode,
            'message' => $resText,
            'secondsTillResend' => $seconds_till_resend,
        ];
    }
    
    protected function validateForRequestSmsToken() {
        if (\frontend\widgets\GoogleRecaptchaV3::shouldImplementAmbientCaptcha()) {
            $captcha_response = \frontend\components\GoogleRecaptcha::verifyToken(\Yii::$app->request->post('g-recaptcha-response-v3'));
            //TODO parse ambient captcha result
            //validate $captcha_response['action']
            //check $captcha_response['score']
        }
        
        $phone = $country = null;
        if (isset($_POST['country']) && isset($_POST['phone'])) {
            $country = \common\components\GeneralComponent::purify($_POST['country']);
            $phone = \common\components\GeneralComponent::purify($_POST['phone']);
            if (empty($country) || empty($phone)) {
                return null;
            }
            $country_detail = \common\models\Countries::getCountryDetails($country);
            if (!$country_detail) {
                return null;
            }
            if ($this->checkCaptchaNeeded($country_detail)) {
                return [
                    'error' => static::ERROR_NEED_CAPTCHA,
                ];
            }
        }
        if (empty($phone)) {
            $phone = Customers::model()->getCustomerFullPhone();
            if (!empty($phone)) {
                $this->data_origin = static::DATA_ORIGIN_DATABASE;
            }
        } else {
            $this->data_origin = static::DATA_ORIGIN_INPUT;
        }
        return [
            'phone' => $phone,
            'country' => $country,
        ];
    }
    
    protected function checkCaptchaNeeded($country_detail) {
        if ($country_detail['countries_iso_code_2'] == \Yii::$app->geoip->countryCodeByIP()) {
            return false;
        }
        $captcha_com = new Captcha();
        if ($captcha_com->verify_response_raw(\Yii::$app->request->post())) {
            return false;
        }
        return true;
    }

    private function initCustomerSecurityComForRequestSmsToken($phone_info) {
        $requestType = '';
        $service_id = Yii::$app->name;
        
        if (isset($_POST['requestType'])) {
            $requestType = \common\components\GeneralComponent::purify($_POST['requestType']);
        }
        
        if (isset($_POST['service_id'])) {
            $service_id = \common\components\GeneralComponent::purify($_POST['service_id']);
        }
        
        if ($this->data_origin == static::DATA_ORIGIN_DATABASE) {
            return new CustomerSecurityComponent($service_id, $requestType, Yii::$app->user->id);
        } else {
            return new CustomerSecurityComponent($service_id, $requestType, Yii::$app->user->id, null, $phone_info['phone'], $phone_info['country']);
        }
    }

    public function requestSecurityToken($requestUrl = null) {
        $responseResult = array(
            'res_code' => 0,
            'res_text' => '',
            'seconds_till_resend' => -1,
        );
        $mobileNumber = $this->customerCompletePhoneInfoArray['country_international_dialing_code'] . $this->customerCompletePhoneInfoArray['telephone_number'];
        $tokenVerifyAttempt = CustomersSetting::model()->getTokenVerifyAttempt($this->customers_id, $this->service->getResendFrequency(), $this->request_type);
        $otp = CustomersOtp::model()->getCustomerSecurityToken($this->customers_id, $this->request_type, $this->service->getTokenValidityPeriod(), $mobileNumber);
        $responseResult['res_code'] = $this->verifyTokenAttempt(FALSE, $this->request_type, $tokenVerifyAttempt, $otp);

        if ($responseResult['res_code'] === FALSE) {
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_FAIL_MSG');
        } else if ($responseResult['res_code'] == '2' || $responseResult['res_code'] == '3') {
            $responseResult['res_code'] = '2';
            $responseResult['seconds_till_resend'] = $tokenVerifyAttempt->seconds_till_resend;
            $responseResult['res_text'] = sprintf(Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_REUSE_MSG'), substr($mobileNumber, 0, -4) . str_repeat('X', 4));
            if (!$this->send_sms) {
                if (isset($otp->customers_otp_digit)) {
                    $responseResult['res_text'] .= '<br>' . Yii::t('smsToken', 'TEXT_TESTING_MODE') . ': ' . $otp->customers_otp_digit;
                }
            }
        } else if ($responseResult['res_code'] == '1') {
            $this->sendToken($requestUrl, $tokenVerifyAttempt, $otp, $responseResult);
        }
        return $responseResult;
    }

    public function actionEmailToken() {
        return [
            'content' => iconv("UTF-8", "UTF-8//IGNORE", $this->renderPartial('emailToken')),
            'title' => Yii::t('smsToken', 'TITLE_SECURITY_TOKEN'),
        ];
    }

    public function actionResendEmailToken() {
        $lastDigit = 0;
        $message = '';
        $status = 0;

        if (isset($_POST['lastDigit'])) {
            $lastDigit = $_POST['lastDigit'];
        }
        if (isset(Yii::$app->session['need_captcha'])) {
            $status = 3;
            $message = Url::to(['site/captcha'], true);
        }

        if ($status == 0) {
            $classCustomerSecurityComponent = new CustomerSecurityComponent(Yii::$app->name, 'security_token_request', Yii::$app->user->id);
            $classEmail = new EmailComponent();
            $requestResult = $classCustomerSecurityComponent->requestSecurityTokenAgainForEmail($lastDigit);
            $status = $requestResult['res_code'];

            if ($status == 1) {
                $customerInfo = Customers::findOne(['customers_id' => Yii::$app->user->id]);
                if (isset($customerInfo->customers_id)) {
                    $tokenVal = $requestResult['token'];
                    $title = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('smsToken','Security Token Has Been Reset');
                    
                    $f_data = array(
                        'cname' => $customerInfo->customers_firstname,
                        'tokenValue' => $tokenVal
                    );
                    $mail_txt = [
                        "content_html" => $classEmail->setRenderEmailhtml('security_token_has_been_reset-html', $f_data),
                        "content_text" => $classEmail->setRenderEmailText('security_token_has_been_reset-text', $f_data),
                    ];
                    
                    $classEmail->sendMail($customerInfo->customers_firstname . ' ' . $customerInfo->customers_lastname, $customerInfo->customers_email_address, $title, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);

                    $requestResult['res_text'] = sprintf(Yii::t('smsToken', 'TEXT_SUCCESSFULLY_SEND_MAIL'), $customerInfo->customers_email_address);
                } else {
                    $requestResult['res_text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_FAIL_MSG');
                    $status = 2;
                }
            }

            if ($status == 0) {
                if (Captcha::checkNeedCaptcha('sessionCheck', array(
                            'returnUrl' => Url::to(["/site/index"], true),
                            'redirect' => false,
                            'sessionKey' => 'cc_email_token_check',
                            'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
                        ))) {
                    $status = 3;
                    $message = Url::to(['site/captcha'], true);
                }
                if ($status == 0) {
                    $message = Yii::t('smsToken', 'ERROR_INVALID_LAST_DIGIT');
                }
            } else {
                $message = $requestResult["res_text"];
            }
        }

        return [
            'status' => $status,
            'message' => $message,
        ];
    }

}
