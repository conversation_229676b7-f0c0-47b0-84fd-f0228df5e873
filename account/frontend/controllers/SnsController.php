<?php

namespace frontend\controllers;

use Aws\Sns\Exception\InvalidSnsMessageException;
use Aws\Sns\Message;
use Aws\Sns\MessageValidator;
use common\components\EmailComponent;
use common\models\LogSesBounce;
use frontend\components\Controller;
use RuntimeException;
use Yii;
use yii\filters\AccessControl;

class SnsController extends Controller {

    public $enableCsrfValidation = false;
    
    /**
     * @inheritdoc
     */
    public function behaviors() {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['*', '?'],
                    ],
                ],
            ],
        ]);
    }
    
    public function actionIndex() {
        try {
            $message = Message::fromRawPostData();
            $validator = new MessageValidator();
            $validator->validate($message);
            $this->handleSnsMessage($message);
        } catch (InvalidSnsMessageException $e) {
            //$this->sendDebugMail('SNS Message Validation Error', $e->__toString());
            $this->redirect('', true, 404);
        } catch (RuntimeException $e) {
            //$this->sendDebugMail('SNS Message Validation Error', $e->__toString());
            $this->redirect('', true, 404);
        }
    }

    protected function handleSnsMessage(Message $message) {
        switch ($message['Type']) {
            case 'SubscriptionConfirmation':
                //Auto-confirm subscription
                file_get_contents($message['SubscribeURL']);
                break;
            case 'Notification':
                $this->handleSnsNotification($message['Message']);
            default:
            //Ignore other Sns
        }
    }

    protected function handleSnsNotification($message) {
        $content = json_decode($message, true);
        switch ($content['notificationType']) {
            case 'Bounce':
                $this->handleBounceSesNotification($content);
            default:
            //Ignore other notifications
        }
    }

    //Ref: http://docs.aws.amazon.com/ses/latest/DeveloperGuide/notification-contents.html
    protected function handleBounceSesNotification($content) {
        $bounce_due_to_content = false;
        $bounce = $content['bounce'];
        $email = $content['mail'];
        
        $bounce_type = $bounce['bounceType'];
        $bounce_subtype = $bounce['bounceSubType'];
        $bounce_id = $bounce['feedbackId'];
        $message_id = $email['messageId'];
        $bounce_emails = $bounce['bouncedRecipients'];
        
        $message = "Bounce Type : " . $bounce_type . '/' . $bounce_subtype
                . "<br>Message ID : " . $message_id
                . "<br>Bounce ID : " . $bounce_id;
        
        if ($bounce_type == 'Transient' && in_array($bounce_subtype, [ 'MessageTooLarge', 'ContentRejected', 'AttachmentRejected' ])) {
            $bounce_due_to_content = true;
        }
        
        if ($bounce_due_to_content) {
            $this->alertBounceDueToContent($message, $email['destination'], $bounce_emails);
        } else {
            $this->recordBounce($message, $email['source'], $bounce_emails);
        }
    }
    
    protected function alertBounceDueToContent($message, $original_list, $bounce_emails) {
        $bounce_list = [];
        foreach ($bounce_emails as $detail) {
            $bounce_list[] = $detail['emailAddress'];
        }
        $message .= "<br>Bounce Destination : " . implode(", ", $bounce_list)
            . "<br>Original Destination : " . implode(", ", $original_list);
        
        $this->sendDebugMail('AWS SES Email bounce due to content', $message);
    }
    
    protected function recordBounce($message, $sender, $bounce_emails) {
        foreach ($bounce_emails as $detail) {
            $model = new LogSesBounce();
            $model->saveSesBounce([
                'email' => $detail['emailAddress'],
                'error_string' => $message . "<br>Diagnostic Code: " . (isset($detail['diagnosticCode']) ? $detail['diagnosticCode'] : ""),
                'created_datetime' => date("Y-m-d H:i:s")
            ]);
        }
    }
    
    protected function sendDebugMail($subject, $message) {
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: ' . Yii::$app->name . ' REPORTER <<EMAIL>>' . "\r\n";
        EmailComponent::sendInternalMail(Yii::$app->params["GENERAL_CONFIG"]["DEBUG_RECEIPIENT"], $subject, $message, $headers);
    }
}
