<?php

namespace frontend\controllers;

use common\components\Password;
use common\models\Customers;
use common\models\SnsConnection;
use Exception;
use frontend\components\Captcha;
use frontend\components\Controller;
use Yii;
use yii\filters\AccessControl;
use yii\helpers\Url;

/**
 * class SocialController
 * Used to control login system between social network and portal
 */
class SocialController extends Controller {

    public $layout = 'main-col2';
    public $menu = 'LINK_SOCIAL';

    /**
     * {@inheritdoc}
     */

    public function beforeAction($action){
        Yii::$app->hybridauth->init();
        return parent::beforeAction($action);
    }

    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['connect', 'link', 'unlink', 'confirm-link', 'confirm-unlink'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'actions' => ['callback'],
                        'allow' => true,
                        'roles' => ['?', '@'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['*'],
                        'denyCallback' => [$this, '_redirectIndex'],
                    ]
                ],
            ],
        ]);
    }

    public function _redirectIndex() {
        if (Yii::$app->request->isAjaxRequest) {
            echo \yii\helpers\Json::encode([
                'status' => 0,
            ]);
        } else {
            if (isset($_GET['origin'])) {
                $_GET['origin'] = urldecode($_GET['origin']);
                $redirect = (!preg_match("~^(?:f|ht)tps?://~i", $_GET['origin']) ? 'http://' . $_GET['origin'] : $_GET['origin']);
            } else if (isset(Yii::$app->session['origin'])) {
                $redirect = Yii::$app->session['origin'];
                unset(Yii::$app->session['origin']);
            } else {
                Yii::$app->user->setReturnUrl(Yii::$app->request->getUrl());
                $redirect = Url::to(['/sso/index'], true);
            }
            return $this->redirect($redirect);
        }
    }

    public function actionConnect() {
        return $this->renderConnect();
    }

    public function renderConnect() {
        unset(Yii::$app->session['social/confirmationDone']);
        return $this->render('connect', [
            'model' => new \frontend\models\PasswordConfirmationForm(),
        ]);
    }

    public function actionConfirmUnlink() {
        $provider = isset($_POST['ha_provider']) ? $_POST['ha_provider'] : '';
        $form = new \frontend\models\PasswordConfirmationForm();
        if (isset(Yii::$app->session['need_captcha'])) {
            return $this->redirect(['site/captcha']);
        } else if (!empty($provider) && $form->load(\Yii::$app->request->post())) {
            if ($form->validate()) {
                unset(Yii::$app->session['cc_social_unlink']);
                if (isset(Yii::$app->user->sns) && isset(Yii::$app->user->sns['login_method']) && $provider == Yii::$app->user->sns['login_method']) {
                    unset(Yii::$app->user->sns);
                }
                Yii::$app->hybridauth->logout($provider);
                SnsConnection::model()->removeConnection(Yii::$app->user->id, $provider);
                Yii::$app->session->setFlash('success', Yii::t('socialConnect', 'MSG_SOCIAL_DISCONNECT_SUCCESSFUL') . ' ' . date('d M Y H:i:s'));
                return $this->redirect(['social/connect']);
            } else {
                Captcha::checkNeedCaptcha('sessionCheck', array(
                    'returnUrl' => Url::to(['social/connect']),
                    'sessionKey' => 'cc_social_unlink',
                    'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
                ));
                Yii::$app->session->setFlash('error', $form->getFirstError('password'));
                return $this->redirect(['social/connect', '#' => 'social-' . strtolower($provider) . '-enabled']);
            }
        } else {
            return $this->redirect(['social/connect', '#' => 'social-' . strtolower($provider) . '-enabled']);
        }
    }

    public function actionConfirmLink() {
        $provider = isset($_GET['ha_provider']) ? $_GET['ha_provider'] : '';
        $form = new \frontend\models\PasswordConfirmationForm();
        if (isset(Yii::$app->session['need_captcha'])) {
            return $this->redirect(['site/captcha']);
        } else if (!empty($provider) && $form->load(\Yii::$app->request->post())) {
            if ($form->validate()) {
                unset(Yii::$app->session['cc_social_link']);
                Yii::$app->session['social/confirmationDone'] = true;
                return $this->_confirmLink();
            } else {
                Captcha::checkNeedCaptcha('sessionCheck', array(
                    'returnUrl' => Url::to(['social/connect']),
                    'sessionKey' => 'cc_social_link',
                    'maxTries' => Yii::$app->params['SSO_CONFIG']['LOGIN_TRIAL'],
                ));
                Yii::$app->session->setFlash('error', $form->getFirstError('password'));
                return $this->redirect(['social/connect', '#' => 'social-' . strtolower($provider) . '-disabled']);
            }
        } else if (isset(Yii::$app->session['social/confirmationDone'])) {
            unset(Yii::$app->session['social/confirmationDone']);
            return $this->_confirmLink();
        } else {
            return $this->redirect(['social/connect', '#' => 'social-' . strtolower($provider) . '-disabled']);
        }
    }

    private function _confirmLink() {
        if (Yii::$app->hybridauth->login()) {
            $ha_provider = Yii::$app->hybridauth->getSession('ha_provider');
            $provider_uid = Yii::$app->hybridauth->getSession('ha_uid');
            $ha_uname = Yii::$app->hybridauth->getSession('ha_email');

            if (isset($provider_uid)) {
                $customerConnections = SnsConnection::model()->getConnections(Yii::$app->user->id);
                $m_customer = SnsConnection::findOne(['provider' => $ha_provider, 'provider_uid' => $provider_uid]);
                if (is_array($customerConnections) && array_key_exists($ha_provider, $customerConnections)) {
                    if ($customerConnections[$ha_provider] == $provider_uid) {
                        //user is already connected to this provider
                    } else {
                        Yii::$app->session->setFlash('error', Yii::t('socialConnect', 'ERROR_MSG_ACCCOUNT_CONNECTED', array('SYS_PROVIDER' => $ha_provider)));
                        Yii::$app->hybridauth->logout($ha_provider);
                    }
                } else if (isset($m_customer->customers_id)) {
                    if ($m_customer->customers_id == Yii::$app->user->id) {
                        //user is already connected to this provider
                    } else {
                        Yii::$app->session->setFlash('error', Yii::t('socialConnect', 'ERROR_MSG_SN_CONNECTED', array('SYS_PROVIDER' => $ha_provider)));
                        Yii::$app->hybridauth->logout($ha_provider);
                    }
                } else {
                    SnsConnection::model()->createConnection(Yii::$app->user->id, $ha_provider, $provider_uid, $ha_uname);
                    Yii::$app->session->setFlash('success', Yii::t('socialConnect', 'MSG_SOCIAL_CONNECT_SUCCESSFUL') . ' ' . date('d M Y H:i:s'));
                }
            }
        } elseif (Yii::$app->hybridauth->error) {
            Yii::$app->session->setFlash('error', Yii::$app->hybridauth->error);
        }
        return $this->redirect(['social/connect']);
    }

    public function actionCallback()
    {
        $state_token = '';
        if (isset($_GET['hauth_done'])) {
            if (isset($_SESSION['state_token']) && isset($_GET['state']) && ((strtolower($_GET['hauth_done']) === 'google') || strtolower($_GET['hauth_done']) === 'facebook')) {
                $agent = (!isset($_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'], 'gonative') === false ? 'normal' : 'gonative');
                list($state_token, $mode) = explode(':', $_GET['state']);
                if ($mode == 'gonative' && $agent === 'normal' && $state_token !== $_SESSION['state_token'] && !empty(Yii::$app->params['gonative.custom.url.scheme'])) {
                    $url = Yii::$app->params['gonative.custom.url.scheme'] . '.' . Url::current([], true);
                    $this->layout = 'card';
                    return $this->render('gonative', ['url' => $url]);
                }
            }
            $_REQUEST['hauth_done'] = $_GET['hauth_done'];
        }
        try {
            // Prevent SSO Sign-in when state token mismatch
            if (isset($_GET['hauth_done']) && strtolower($_GET['hauth_done']) === 'google' && ($state_token !== $_SESSION['state_token'])) {
                return $this->redirect(['sso/login']);
            }
            Yii::$app->hybridauth->endPoint();
        } catch (Exception $e) {
            Yii::$app->hybridauth->errorMessage($e);
            Yii::$app->session->setFlash('error', Yii::$app->hybridauth->error);
            return $this->redirect(['sso/login']);
        }
    }

}
