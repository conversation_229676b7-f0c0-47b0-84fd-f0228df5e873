<?php

namespace frontend\controllers;

use common\components\CustomersInfoVerificationComponent;
use common\components\EmailComponent;
use common\components\GeneralComponent;
use common\components\Password;
use common\components\SSOCom;
use common\models\Configuration;
use common\models\Countries;
use common\models\Customers;
use common\models\CustomersSetting;
use common\models\CustomersInfoVerification;
use common\models\ShassoClient;
use common\models\SnsConnection;
use common\components\CustomerSecurityComponent;
use common\components\PhoneVerificationComponent;
use common\components\LogFilesComponent;
use common\models\AddressBook;
use frontend\components\Captcha;
use frontend\components\Controller;
use frontend\components\UserIdentity;
use frontend\models\ForgetForm;
use frontend\models\LoginForm;
use frontend\models\ResetPasswordForm;
use frontend\models\XpressSignupForm;
use frontend\models\UpdatePhoneForm;
use common\models\CustomersInfo;
use common\models\Zones;
use frontend\models\ReclaimMobileForm;
use Yii;
use yii\base\Model;
use yii\filters\AccessControl;
use yii\helpers\Url;
use yii\web\Cookie;

class SsoController extends Controller
{

    public $layout = 'card';

    const PHONE_SHARE_RECLAIM = 'reclaim',
        PHONE_SHARE_NO_RECLAIM = 'no-reclaim';

    /**
     * @inheritdoc
     * @ : authenticated user
     * ? : anonymous user
     * * : any user
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['login-all', 'sign-up-success', 'setup', 'set-customers-setup', 'reclaim-mobile', 'get-reclaim-mobile'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'actions' => ['device', 'login', 'sign-up'],
                        'allow' => true,
                        'roles' => ['?'],
                    ],
                    [
                        'actions' => ['forget-reset', 'index', 'logout', 'xpress', 'forget'],
                        'allow' => true,
                        'roles' => ['@', '?'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['@'],
                        'denyCallback' => [$this, '_redirectLoginAll'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['?'],
                        'denyCallback' => [$this, '_redirectLogin'],
                    ]
                ],
            ],
        ]);
    }

    public function _redirectLoginAll()
    {
        return $this->redirect(['sso/login-all']);
    }

    public function _redirectLogin()
    {
        return $this->redirect(['sso/login']);
    }

    public function beforeAction($action)
    {
        // remember-me
        $auto_login_action = array('index', 'login', 'signup', 'setup', 'set-customers-setup');
        if (Yii::$app->user->isGuest && in_array(strtolower($action->id), $auto_login_action) && isset($_COOKIE['ogm']['un']) && isset($_COOKIE['ogm']['uc'])) {
            $this->_authenticate();
        }

        if (isset($_SERVER['REQUEST_METHOD'])) {
            if (isset($_POST) && !empty($_POST)) {
                $_POST = GeneralComponent::purify($_POST);
            }

            if (isset($_GET) && !empty($_GET)) {
                $_GET = GeneralComponent::purify($_GET);
            }
        }

        if (isset($_GET['hl']) && in_array($_GET['hl'], Yii::$app->params["SSO_CONFIG"]["LANG_SUPPORT"])) {
            Yii::$app->language = $_GET['hl'];
        }

        return parent::beforeAction($action);
    }

    public function afterAction($action, $result)
    {
        Yii::$app->language = Yii::$app->session['lang_code'];
        return parent::afterAction($action, $result);
    }

    public function actionIndex()
    {
        $req_arr = array();
        $url = "sso/login";

        // 3rd party portal URL query
        $req_uri = SSOCom::clientURLQuery();
        parse_str($req_uri, $req_arr);

        if (!empty($req_arr) && isset($req_arr["action"])) {
            $action = strtolower($req_arr["action"]);
            switch ($action) {
                case "login":
                    if (isset($req_arr["sns"])) {
                        $sns = str_replace("+", "", ucwords($req_arr["sns"]));
                        if (Yii::$app->hybridauth->isAllowedProvider($sns)) {
                            $url = "sso/xpress";
                            $req_arr["ha_provider"] = $sns;
                        }
                    } else if (Yii::$app->user->isGuest) {
                        $url = "sso/login";
                    } else {
                        $url = "sso/loginAll";
                        if (isset($req_arr['origin'])) {
                            $req_arr['partner-login'] = true;
                        }
                    }
                    break;

                case "signup":
                    $url = "sso/signUp";
                    break;

                case "logout":
                    $url = "sso/logout";
                    break;
            }
        }
        return $this->redirect(Url::to([$url], true) . (empty($req_uri) ? "" : "?" . http_build_query($req_arr)));
    }

    public function actionLogin()
    {
        $f_data = array();

        $model = new LoginForm();

        $_SESSION['action'] = isset($_REQUEST['action']) ? $_REQUEST['action'] : "";
        $_SESSION['nextUrl'] = isset($_REQUEST['nextUrl']) ? $_REQUEST['nextUrl'] : "";
        $_SESSION['service'] = isset($_REQUEST['service']) ? $_REQUEST['service'] : "";
        $_SESSION['state'] = isset($_REQUEST['state']) ? $_REQUEST['state'] : "";
        $_SESSION['origin'] = isset($_REQUEST['origin']) ? $_REQUEST['origin'] : "";
        $_SESSION['freshdesk_domain'] = isset($_REQUEST['freshdesk_domain']) ? $_REQUEST['freshdesk_domain'] : "";
        $_SESSION['nonce'] = isset($_REQUEST['nonce']) ? $_REQUEST['nonce'] : "";

        $f_data['is_mode_launcher'] = $this->isModeLauncher();

        if (Yii::$app->getRequest()->getIsPost()) {
            // normal login
            UserIdentity::login_trial();

            if ($model->load($_POST) && $model->validate()) {
                // Yii login
                $_status = $this->_authenticate($model->customers_email_address, $model->customers_password, "normal");
                if ($_status) {
                    $f_data['mail'] = $model->customers_email_address;
                    Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_LOGIN_" . $_status));
                }
            }
        } else if (isset($_GET["S3AT"])) {
            $req_arr = array();

            // 3rd party portal URL query
            $req_uri = SSOCom::clientURLQuery();
            parse_str($req_uri, $req_arr);

            if (isset($req_arr['origin']) && !empty($req_arr['origin'])) {
                $r_scm = SSOCom::secureCodeMatch(SSOCom::$otp_key_api_register, array("code" => $_GET["S3AT"], "cid" => $_GET["uid"]));
                if (isset($r_scm["result"]["cid"])) {
                    if ($this->_authenticate($r_scm["result"]["cid"], "", SSOCom::$otp_key_api_register)) {
                        return $this->redirect(Url::to(['sso/login'], true) . $req_uri);
                    }
                }
            }
        }

        // 3rd party portal URL query
        $f_data['f_query'] = GeneralComponent::getRequestURI();
        $req_uri = (empty($f_data['f_query']) ? "" : "?") . $f_data['f_query'];

        if (isset(Yii::$app->session['need_captcha'])) {
            Yii::$app->session['return_url'] = Url::to(['sso/login'], true) . $req_uri;
            return $this->redirect(Url::to(['site/captcha'], true) . $req_uri);
        } else {

            $f_data['model'] = $model;

            // social network
            $f_data['sns'] = count(Yii::$app->hybridauth->getAllowedProviders());

            if ($f_data["sns"]) {
                $sns_error = Yii::$app->hybridauth->getHybridAuthError();
                if ($sns_error) {
                    foreach ($sns_error as $error) {
                        Yii::$app->session->setFlash('danger', $error);
                    }
                    Yii::$app->hybridauth->clearHybridAuthError();
                }
            }

            // pre-fill email address
            if (isset($_GET['email']) && !isset($f_data['mail'])) {
                $f_data['mail'] = $_GET['email'];
            }

            return $this->render('login', $f_data);
        }
    }

    public function actionLoginAll()
    {
        $req_arr = array();

        // 3rd party portal URL query
        $req_uri = SSOCom::clientURLQuery();
        parse_str($req_uri, $req_arr);

        // 3rd party plant cookie
        $_data = SSOCom::clientURLSetSSOToken();
        $is_remember_me = (isset($req_arr["partner-login"]) && $req_arr["partner-login"]) || Yii::$app->user->login_method == 'remember_me';

        if (isset($_SESSION['action']) && $_SESSION['action'] == 'freshdesk-login') {
            $user = Customers::find()->where(['customers_id' => Yii::$app->user->id])->one();
            FreshdeskController::freshdeskRedirect($user);
        }

        if (isset($_data["S3ID"])) {
            $req_arr["S3ID"] = $_data["S3ID"];
        }
        if (isset($_data["q"]) && !empty($_data["q"])) {
            $req_arr = array_merge($req_arr, $_data["q"]);
        }
        if (isset($_data["S3RM"])) {
            $req_arr["S3RM"] = $_data["S3RM"];
        }

        // 3rd party <image> plant cookie
        if (isset($_data["portal"]) && !empty($_data["portal"])) {
            $f_data["portal"] = $_data["portal"];
        }

        if (isset($req_arr['origin']) && !empty($req_arr['origin'])) {
            $f_data['origin'] = urldecode(urldecode($req_arr['origin'])) . "?" . http_build_query($req_arr);
            //If login request come from partner website, immediately redirect user back to partner to minimize wait time
            if ($is_remember_me) {
                return $this->redirect($f_data['origin']);
            }
        } else {
            if (preg_match("~^(?:f|ht)tps?://~i", Yii::$app->user->returnUrl)) {
                if (preg_match("/sso/i", Yii::$app->user->returnUrl)) {
                    $f_data['origin'] = Url::to(Yii::$app->homeUrl[0], true);
                } else {
                    $f_data['origin'] = Yii::$app->user->returnUrl;
                }
            } else {
                $f_data['origin'] = Url::to(Yii::$app->user->returnUrl, true);
            }
        }

        $this->layout = 'min';
        return $this->render('cookieAll', $f_data);
    }

    public function actionSetup()
    {
        $model = Customers::findOne(['customers_id' => Yii::$app->user->id]);
        // check phone num not empty
        $custInfoVer = '';
        if (!empty($model->customers_telephone)) {
            $countryDialId = Countries::model()->getCountryDialingCodeById($model->customers_country_dialing_code_id);
            $custInfoVer = CustomersInfoVerification::findOne(['customers_id' => Yii::$app->user->id, 'customers_info_value' => $countryDialId . $model->customers_telephone, 'info_verified' => 1, 'info_verification_type' => CustomersInfoVerification::$type2]);
        }

        return $this->renderSetup(new \frontend\models\CustomersSetupForm([
            'customers_country_dialing_code_id' => Yii::$app->session["ctry_id"],
            'require_mobile' => empty($custInfoVer),
        ]), $custInfoVer);
    }

    private function renderSetup($model, $cust_info)
    {
        $params = [
            'f_query' => GeneralComponent::getRequestURI(),
            'model' => $model,
            'allCountry' => Countries::model()->getAllCountries(false),
            //$modelCountries->getAllCountries(true),
            'token' => new \frontend\models\UpdatePhoneForm(),
            'info_verified' => $cust_info,
        ];

        return $this->render('setup', $params);
    }

    public function actionSetCustomersSetup()
    {
        $fq_arr = array();
        $f_query = GeneralComponent::getRequestURI();
        if (!\Yii::$app->request->isPost) {
            return $this->redirect(Url::to(['sso/setup'], true) . '?' . $f_query);
        }
        //Phone verified already?
        $m_cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
        $countryDialId = Countries::model()->getCountryDialingCodeById($m_cust->customers_country_dialing_code_id);
        $custInfoVer = CustomersInfoVerification::findOne(['customers_id' => \Yii::$app->user->id, 'customers_info_value' => $countryDialId . $m_cust['customers_telephone'], 'info_verified' => 1, 'info_verification_type' => CustomersInfoVerification::$type2]);

        $f_cust = new \frontend\models\CustomersSetupForm([
            'require_mobile' => empty($custInfoVer),
        ]);

        if (!$f_cust->load(\Yii::$app->request->post()) || !$f_cust->validate()) {
            return $this->renderSetup($f_cust, $custInfoVer);
        }

        if (empty($custInfoVer)) {
            //Check token answer
            $update_phone_form = new UpdatePhoneForm([
                'customers_telephone' => $f_cust->customers_telephone,
                'customers_country_dialing_code_id' => $f_cust->customers_country_dialing_code_id,
            ]);
            $_POST = GeneralComponent::purify($_POST);
            //get service_id before validate answer

            if (isset($f_query) && !empty($f_query)) {
                $tmp_arr = array();
                parse_str($f_query, $tmp_arr);
                $f_query = '?' . $f_query;

                // 3rd party portal URL query
                if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
                    $origin = urldecode(urldecode($tmp_arr["origin"]));
                    if (\common\components\SSOCom::clientURLMatch($tmp_arr["service"], $origin)) {
                        $fq_arr = $tmp_arr;
                    }
                }
            }
            $service_id = isset($tmp_arr["service"]) ? $tmp_arr["service"] : NULL;

            $classCustomerSecurityComponent = new CustomerSecurityComponent($service_id, 'verify_phone_request', Yii::$app->user->id, null, $update_phone_form->customers_telephone, $update_phone_form->customers_country_dialing_code_id);
            $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
            $classPhoneVerificationComponent = new PhoneVerificationComponent();
            $modelCustomersInfo = new CustomersInfo();
            //validate tokenAnswer
            if (isset($_POST['tokenAnswer']) && $_POST['tokenAnswer'] != '') {
                $cust_log = $cust_mod = array();

                $answer = $_POST['tokenAnswer'];
                $update_phone_form->token_answer = $answer;
                $validateAnswer = $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer, false);
                if ($validateAnswer['error'] === 0) {
                    $parse_result = $classCustomersInfoVerificationComponent->parseTelephone($update_phone_form->customers_telephone, $update_phone_form->customers_country_dialing_code_id);
                    if (!$parse_result->is_valid_number) {
                        Yii::$app->session->setFlash('danger', Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED_NOMAIL'));
                    } else if (!$parse_result->is_country_match) {
                        Yii::$app->session->setFlash('danger', Yii::t('smsToken', 'TEXT_INVALID_MOBILE_COUNTRY'));
                    } else {
                        $phoneNumber = $parse_result->national_number;
                        $countryDialId = $parse_result->dialing_code;
                        if (!$classPhoneVerificationComponent->isMobileNumExist($phoneNumber, $update_phone_form->customers_country_dialing_code_id, Yii::$app->user->id, $countryDialId)) {

                            $classCustomerSecurityComponent->validateCustomerSecurityAnswer($answer);
                            //We are now using type `verify_phone_request`, so we must add data to verified list. This phone now binds to this user.
                            $customers_info_verification_obj = new CustomersInfoVerification();
                            $customers_info_verification_obj->updatePhoneVerifyInfo(Yii::$app->user->id, $countryDialId . $phoneNumber, '', 1);

                            //Update customers information
                            $model = Customers::findOne(['customers_id' => $this->user->id]);
                            $cust_data = array(
                                'customers_firstname' => $f_cust->customers_firstname,
                                'customers_lastname' => $f_cust->customers_lastname,
                                'customers_telephone' => $phoneNumber,
                                'customers_country_dialing_code_id' => $f_cust->customers_country_dialing_code_id,
                            );
                            $log_cust_data = $cust_data;
                            $cust_orig = Customers::find()->select(array_keys($cust_data))->where(['customers_id' => Yii::$app->user->id])->one();
                            $cust_orig = array_intersect_key($cust_orig->attributes, $cust_data);

                            $addr_data = AddressBook::model()->getAddressInfo($m_cust->customers_default_address_id, $m_cust->customers_id);
                            if ($addr_data->entry_country_id != $f_cust->customers_country_dialing_code_id) {
                                $addr_attr = array(
                                    "entry_street_address" => '',
                                    "entry_suburb" => '',
                                    "entry_postcode" => '',
                                    "entry_city" => '',
                                    "entry_state" => '',
                                    "entry_country_id" => $f_cust->customers_country_dialing_code_id,
                                    "entry_zone_id" => null,
                                );
                                $addr_orig = AddressBook::model()->getUserAddress($m_cust->customers_default_address_id, array_keys($addr_attr));
                                AddressBook::updateAll($addr_attr, ['address_book_id' => $m_cust->customers_default_address_id]);

                                if (isset($addr_orig['entry_zone_id'])) {
                                    if (empty($addr_orig['entry_zone_id'])) {
                                        unset($addr_orig['entry_zone_id']);
                                    } else {
                                        $zone_name = Zones::model()->getStateName($addr_orig['entry_country_id'], $addr_orig['entry_zone_id']);
                                        if (isset($zone_name)) {
                                            $addr_orig["entry_state"] = $zone_name;
                                        }
                                    }
                                }
                                unset($addr_attr['entry_zone_id']);
                                $cust_orig = array_merge($cust_orig, $addr_orig);
                                $log_cust_data = array_merge($log_cust_data, $addr_attr);
                            }

                            Yii::$app->user->firstname = $f_cust->customers_firstname;
                            Yii::$app->user->lastname = $f_cust->customers_lastname;
                            Customers::updateAll($cust_data, ['customers_id' => Yii::$app->user->id]);
                            CustomersInfo::model()->updateLastModify();

                            // customers data change log
                            $this->_dataLog($cust_orig, $log_cust_data, $m_cust->customers_email_address);
                            $this->_setCustomerSetting(Yii::$app->user->id);

                            //TODO Iera - set shasso.user_preference table (prefernce_key = 'country', value = phone_country_id) and set session key
                            Yii::$app->session->setFlash('success', Yii::t('sso', 'SETUP_SUCCESSFUL'));
                            return $this->redirect(Url::to(['sso/login-all'], true) . '?' . $f_query);
                        } else {
                            //session for $f_cust
                            $session = Yii::$app->session;
                            $session['customers_firstname'] = $f_cust->customers_firstname;
                            $session['customers_lastname'] = $f_cust->customers_lastname;
                            $session['customers_telephone'] = $phoneNumber;
                            $session['customers_country_dialing_code_id'] = $f_cust->customers_country_dialing_code_id;
                            //                            //old data already setup
                            //query new from customer and customers_info_verification
                            $dataCus = Customers::find()
                                ->join('LEFT JOIN', CustomersInfoVerification::tableName(), '{{%customers}}.customers_id = {{%customers_info_verification}}.customers_id')
                                ->where([
                                    'and',
                                    ['=', '{{%customers}}.customers_telephone', $phoneNumber],
                                    ['=', '{{%customers_info_verification}}.customers_info_value', $countryDialId . $phoneNumber],
                                    ['=', '{{%customers_info_verification}}.info_verification_type', CustomersInfoVerification::$type2],
                                    ['=', '{{%customers_info_verification}}.info_verified', 1],
                                ])->asArray()->all();
                            foreach ($dataCus as $datas) {
                                $type = null;

                                $m_cust = Customers::findOne(['customers_id' => $datas['customers_id']]);

                                if (\common\models\CustomersSetting::find()->where(['and', ['=', 'customers_id', $datas['customers_id']], ['=', 'customers_setting_key', \common\models\CustomersSetting::SETUP_ACCOUT_KEY]])->one()) {
                                    $type = static::PHONE_SHARE_NO_RECLAIM;
                                } else if ($m_cust['customers_status'] != 1) {
                                    $type = static::PHONE_SHARE_NO_RECLAIM;
                                } else {
                                    $type = static::PHONE_SHARE_RECLAIM;
                                }

                                $mail_obj = new EmailComponent();
                                $f_data = array(
                                    'cname' => $m_cust->customers_firstname,
                                    'c_email' => $m_cust->customers_email_address,
                                );

                                if ($type == static::PHONE_SHARE_RECLAIM) {
                                    //activated email
                                    $mail_title = Yii::t('sso', 'Reclaim Mobile Number');

                                    $mail_txt = [
                                        "content_html" => $mail_obj->setRenderEmailhtml('warning_reclaim_mobile-html', $f_data),
                                        "content_text" => $mail_obj->setRenderEmailText('warning_reclaim_mobile-text', $f_data),
                                    ];

                                    if (!empty($mail_txt)) {
                                        $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                                        $mail_obj->sendMail($m_cust->customers_firstname, $m_cust->customers_email_address, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
                                    }
                                    //cache put to session customer_id
                                    $session['existed_cust_id'] = $datas['customers_id'];
                                    return $this->redirect(Url::to(['sso/reclaim-mobile'], true) . '?' . $f_query);
                                } else if ($type == static::PHONE_SHARE_NO_RECLAIM) {
                                    //activated email

                                    $mail_title = Yii::t('sso', 'Mobile Number Has Been Used');

                                    $f_data = array(
                                        'cname' => $m_cust->customers_firstname,
                                        'c_email' => $m_cust->customers_email_address,
                                    );
                                    $mail_txt = [
                                        "content_html" => $mail_obj->setRenderEmailhtml('mobile_number_been_used-html', $f_data),
                                        "content_text" => $mail_obj->setRenderEmailText('mobile_number_been_used-text', $f_data),
                                    ];

                                    if (!empty($mail_txt)) {
                                        $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;
                                        $mail_obj->sendMail($m_cust->customers_firstname, $m_cust->customers_email_address, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
                                    }

                                    $email = GeneralComponent::createEmailMask($f_data['c_email']);
                                    Yii::$app->session->setFlash('danger', Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED', ['c_email' => $email]));
                                    return $this->renderSetup($f_cust, $custInfoVer);
                                }
                            }
                        }
                    }
                } else {
                    Yii::$app->session->setFlash('danger', $validateAnswer['text']);
                    return $this->renderSetup($f_cust, $custInfoVer);
                }
            }
        } else {
            $model = Customers::findOne(['customers_id' => $this->user->id]);
            $cust_data = array(
                'customers_firstname' => $f_cust->customers_firstname,
                'customers_lastname' => $f_cust->customers_lastname,
            );
            $cust_orig = Customers::find()->select(array_keys($cust_data))->where(['customers_id' => Yii::$app->user->id])->one();
            $cust_orig = array_intersect_key($cust_orig->attributes, $cust_data);

            Yii::$app->user->firstname = $f_cust->customers_firstname;
            Yii::$app->user->lastname = $f_cust->customers_lastname;
            Customers::updateAll($cust_data, ['customers_id' => Yii::$app->user->id]);
            \common\models\CustomersInfo::model()->updateLastModify();

            $this->_dataLog($cust_orig, $cust_data, $m_cust->customers_email_address);
            $this->_setCustomerSetting(Yii::$app->user->id);
            Yii::$app->session->setFlash('success', Yii::t('sso', 'SETUP_SUCCESSFUL'));
            return $this->redirect(Url::to(['sso/login-all'], true) . '?' . $f_query);
        }
        return $this->redirect(Url::to(['sso/login-all'], true) . '?' . $f_query);
    }

    public function actionReclaimMobile()
    {

        $_cust = Customers::findOne(['customers_id' => \Yii::$app->session->get('existed_cust_id')]);
        return $this->renderReclaimMobile($_cust, new ReclaimMobileForm());
    }

    private function renderReclaimMobile($m_cust, $model)
    {
        //get the email address input
        $email = GeneralComponent::createEmailMask($m_cust->customers_email_address);

        $params = [
            'f_query' => GeneralComponent::getRequestURI(),
            'model' => $model,
            'email' => $email,
        ];

        return $this->render('_retrive-email', $params);
    }

    public function actionGetReclaimMobile()
    {
        if (isset(Yii::$app->session['need_captcha'])) {
            return $this->redirect(Url::to(['site/captcha'], true));
        }
        $fq_arr = array();
        $f_query = GeneralComponent::getRequestURI();
        if (isset($f_query) && !empty($f_query)) {
            $tmp_arr = array();
            parse_str($f_query, $tmp_arr);
            $f_query = '?' . $f_query;

            // 3rd party portal URL query
            if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
                $origin = urldecode(urldecode($tmp_arr["origin"]));
                if (\common\components\SSOCom::clientURLMatch($tmp_arr["service"], $origin)) {
                    $fq_arr = $tmp_arr;
                }
            }
        }

        if (!\Yii::$app->request->post()) {
            return $this->redirect(Url::to(['sso/setup'], true) . '?' . $f_query);
        }
        // check xxx email true not false
        $f_email = new ReclaimMobileForm();
        $m_cust = Customers::findOne(['customers_id' => \Yii::$app->user->id]);
        $classCustomersInfoVerificationComponent = new CustomersInfoVerificationComponent();
        $parse_result = $classCustomersInfoVerificationComponent->parseTelephone(\Yii::$app->session->get('customers_telephone'), \Yii::$app->session->get('customers_country_dialing_code_id'));
        $phoneNumber = $parse_result->national_number;
        $countryDialId = $parse_result->dialing_code;
        //get old email
        $oldAccData = Customers::findOne(['customers_id' => \Yii::$app->session->get('existed_cust_id')]);
        if (!$oldAccData) {
            return $this->renderReclaimMobile($oldAccData, $f_email);
        }

        if (!$f_email->load(\Yii::$app->request->post()) || !$f_email->validate()) {
            return $this->renderReclaimMobile($oldAccData, $f_email);
        }

        $result = $this->validateEmail($oldAccData, $f_email->customers_email_address);
        $custSet = \common\models\CustomersSetting::findOne([
            'customers_id' => \Yii::$app->session->get('existed_cust_id'),
            'customers_setting_key' => \common\models\CustomersSetting::SETUP_ACCOUT_KEY,
            'customers_setting_value' => 1
        ]);
        $classPhoneVerificationComponent = new PhoneVerificationComponent();

        //find mobile num and delete
        if ($result) {
            //check if other who use same num
            if (!$custSet) {
                if ($classPhoneVerificationComponent->isMobileNumExist($phoneNumber, \Yii::$app->session->get('customers_country_dialing_code_id'), Yii::$app->user->id, $countryDialId)) {
                    //update phone num from old acc verified = 0
                    $cust_info_old_ver_obj = new CustomersInfoVerification();
                    $cust_info_old_ver_obj->updatePhoneVerifyInfo($oldAccData->customers_id, $countryDialId . $phoneNumber, '', 0);
                    CustomersInfo::model()->updateLastModify($oldAccData->customers_id);

                    //new account
                    $cust_info_new_ver_obj = new CustomersInfoVerification();
                    $cust_info_new_ver_obj->updatePhoneVerifyInfo(Yii::$app->user->id, $countryDialId . $phoneNumber, '', 1);

                    // save phone num into new acc
                    $cust_data = array(
                        'customers_firstname' => \Yii::$app->session->get('customers_firstname'),
                        'customers_lastname' => \Yii::$app->session->get('customers_lastname'),
                        'customers_telephone' => $phoneNumber,
                        'customers_country_dialing_code_id' => \Yii::$app->session->get('customers_country_dialing_code_id'),
                    );
                    $log_cust_data = $cust_data;
                    $cust_orig = Customers::find()->select(array_keys($cust_data))->where(['customers_id' => Yii::$app->user->id])->one();
                    $cust_orig = array_intersect_key($cust_orig->attributes, $cust_data);

                    $addr_data = AddressBook::model()->getAddressInfo($m_cust->customers_default_address_id, $m_cust->customers_id);
                    if ($addr_data->entry_country_id != \Yii::$app->session->get('customers_country_dialing_code_id')) {
                        $addr_attr = array(
                            "entry_street_address" => '',
                            "entry_suburb" => '',
                            "entry_postcode" => '',
                            "entry_city" => '',
                            "entry_state" => '',
                            "entry_country_id" => \Yii::$app->session->get('customers_country_dialing_code_id'),
                            "entry_zone_id" => null,
                        );
                        $addr_orig = AddressBook::model()->getUserAddress($m_cust->customers_default_address_id, array_keys($addr_attr));
                        AddressBook::updateAll($addr_attr, ['address_book_id' => $m_cust->customers_default_address_id]);

                        if (isset($addr_orig['entry_zone_id'])) {
                            if (empty($addr_orig['entry_zone_id'])) {
                                unset($addr_orig['entry_zone_id']);
                            } else {
                                $zone_name = Zones::model()->getStateName($addr_orig['entry_country_id'], $addr_orig['entry_zone_id']);
                                if (isset($zone_name)) {
                                    $addr_orig["entry_state"] = $zone_name;
                                }
                            }
                        }
                        unset($addr_attr['entry_zone_id']);
                        $cust_orig = array_merge($cust_orig, $addr_orig);
                        $log_cust_data = array_merge($log_cust_data, $addr_attr);
                    }

                    Yii::$app->user->firstname = \Yii::$app->session->get('customers_firstname');
                    Yii::$app->user->lastname = \Yii::$app->session->get('customers_lastname');
                    Customers::updateAll($cust_data, ['customers_id' => Yii::$app->user->id]);
                    CustomersInfo::model()->updateLastModify();

                    $this->_dataLog($cust_orig, $log_cust_data, $m_cust->customers_email_address);
                    $this->_setCustomerSetting(Yii::$app->user->id);

                    \Yii::$app->session->remove('existed_cust_id');
                    \Yii::$app->session->remove('customers_firstname');
                    \Yii::$app->session->remove('customers_lastname');
                    \Yii::$app->session->remove('customers_telephone');
                    \Yii::$app->session->remove('customers_country_dialing_code_id');

                    Yii::$app->session->setFlash('success', Yii::t('sso', 'SETUP_SUCCESSFUL'));
                    return $this->redirect(Url::to(['sso/login-all'], true) . '?' . $f_query);
                }
            } else {
                $custInfoVer = CustomersInfoVerification::findOne(['customers_id' => Yii::$app->user->id, 'customers_info_value' => $countryDialId . $m_cust->customers_telephone, 'info_verified' => 1, 'info_verification_type' => CustomersInfoVerification::$type2]);
                $model = new \frontend\models\CustomersSetupForm([
                    'customers_country_dialing_code_id' => Yii::$app->session["ctry_id"],
                    'require_mobile' => empty($custInfoVer),
                ]);
                Yii::$app->session->setFlash('danger', Yii::t('smsToken', 'TEXT_MOBILE_IS_IN_USED_NOMAIL'));
                return $this->renderSetup($model, $custInfoVer);
            }
        }
        Yii::$app->session->setFlash('danger', Yii::t('sso', 'EMAIL_NOT_MATCH'));
        Captcha::checkNeedCaptcha('sessionCheck', array(
            'returnUrl' => Url::to(['sso/reclaim-mobile'], true) . '?' . $f_query,
            'sessionKey' => 'reclaim_mobile',
            'maxTries' => Yii::$app->params['SSO_CONFIG']['RECLAIM_EMAIL_TRIAL'],
        )
        );
        return $this->renderReclaimMobile($oldAccData, $f_email);
    }

    private function validateEmail($oldAccData, $postEmail)
    {
        //validate the right email
        if ($oldAccData->customers_email_address == $postEmail) {
            return true;
        }
        return false;
    }

    public function actionLogout()
    {
        $f_data = array();
        $req_arr = array();

        // 3rd party portal URL query
        $req_uri = SSOCom::clientURLQuery();
        parse_str($req_uri, $req_arr);

        if (isset($req_arr['origin']) && !empty($req_arr['origin'])) {
            $f_data['origin'] = urldecode(urldecode($req_arr['origin'])) . "?" . http_build_query($req_arr);
        } else {
            if (isset($_GET["host_url"])) {
                // freshdesk auto append param
                $f_data['origin'] = (!preg_match("~^(?:f|ht)tps?://~i", $_GET["host_url"]) ? "http://" : "") . $_GET["host_url"];
            } else {
                $f_data['origin'] = Url::to(Yii::$app->user->loginUrl, true);
            }
        }

        if (Yii::$app->user->isGuest) {
            $identity = new UserIdentity();
            $identity->logout();
            Yii::$app->hybridauth->logout();
            Yii::$app->user->logout();

            return $this->redirect($f_data['origin']);
        } else {
            $m_client = ShassoClient::find()->where("delete_token_url <> ''")->all();
            foreach ($m_client as $_num => $_data) {
                if (isset($_data->client_id)) {
                    if (isset($req_arr['service']) && ($req_arr['service'] == $_data->client_id)) {
                        ;
                    } else {
                        $f_data['portal'][] = array(
                            'url' => $_data->delete_token_url
                        );
                    }
                }
            }

            $identity = new UserIdentity();
            $identity->logout();
            Yii::$app->hybridauth->logout();
            Yii::$app->user->logout();

            $this->layout = 'min';
            return $this->render('cookieAll', $f_data);
        }
    }

    public function actionSignUp()
    {
        $f_data = array();
        $model = new LoginForm();

        $f_data['f_query'] = GeneralComponent::getRequestURI();
        $req_uri = (empty($f_data['f_query']) ? "" : "?") . $f_data['f_query'];
        $f_data['model'] = $model;

        $captcha_com = Yii::$app->params['SSO_CONFIG']['SIGNUP_INLINE_CAPTCHA'] ? new Captcha(true) : null;

        if (isset(Yii::$app->session['need_captcha'])) {
            return $this->redirect(['site/captcha']);
        } else if (Yii::$app->getRequest()->getIsPost() && $model->load($_POST) && isset(Yii::$app->session['sgstime'])) {
            $_status = true;

            if (isset($captcha_com)) {
                if (!$captcha_com->verify_response_raw($_POST)) {
                    Yii::$app->session->setFlash('danger', Yii::t("site", "ERROR_RECAPTCHA_INVALID_CODE"));
                    return $this->renderSignupForm($f_data, $captcha_com);
                }
            } else if (!empty($_POST['prev'])) {
                //If inline captcha is enabled, we do not need to check for simplereg captcha
                $prev_mail = GeneralComponent::decrypt($_POST['prev'], Yii::$app->session['sgstime']);
                Captcha::checkNeedCaptcha('simpleReg', array(
                    'returnUrl' => Url::to(['sso/sign-up'], true) . $req_uri,
                    'value1' => $prev_mail,
                    'value2' => $model->customers_email_address,
                )
                );
            }

            $mail_validator =  new \common\components\MailValidator();

            $valid_email = $mail_validator->checkEmail($model->customers_email_address);

            if ($valid_email == 1 || $valid_email == -1) {
                if ($model->validate()) {
                    $_input['news'] = (isset($_POST['newsletter']) ? $_POST['newsletter'] : '');
                    $obj_uid = new UserIdentity($model->customers_email_address, $model->customers_password);
                    if ($obj_uid->register($_input)) {
                        $m_cust = Customers::findOne(['customers_email_address' => $model->customers_email_address]);
                        $mail_obj = new EmailComponent();
                        // welcome mail
                        $v = Yii::createObject([
                            'class' => \yii\web\View::className(),
                        ]);

                        $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('sso', 'WELCOME TO OFFGAMERS!');
                        $f_data = array(
                            'title' => $mail_sub,
                            'cname' => $m_cust->customers_firstname,
                        );
                        $mail_txt = [
                            "content_html" => $v->render('@common/mail/welcome_to_shasso-html', $f_data),
                            "content_text" => $v->render('@common/mail/welcome_to_shasso-text', $f_data),
                        ];

                        $mail_obj->sendMail($m_cust->customers_firstname, $model->customers_email_address, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);

                        $obj_uid->login_method = "register";
                        $obj_uid->authenticate();
                        $_status = (int) $obj_uid->errorCode;
                    }
                }

                if ($_status) {
                    Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_SIGNUP"));
                } else {
                    $req_arr = array();

                    // 3rd party portal URL query
                    $req_uri = SSOCom::clientURLQuery();
                    parse_str($req_uri, $req_arr);

                    if (isset($req_arr['origin'])) {
                        // return newsletter subscription status to 3rd party portal
                        if ($_input['news']) {
                            $req_arr['S3NEWS'] = 1;
                        }

                        return $this->redirect(Url::to(['sso/login-all'], true) . (!empty($req_uri) ? "?" : "") . http_build_query($req_arr));
                    } else {
                        Yii::$app->session->setFlash('success', Yii::t('sso', 'TEXT_SIGNUP_WELCOME', array('SYS_FIRSTNAME' => Yii::$app->user->firstname)));
                        return $this->redirect(Url::to(['overview/index'], true) . (!empty($req_uri) ? "?" : "") . $req_uri);
                    }
                }
            } else {
                if (!empty($mail_validator->error_msg)) {
                    $model->addError('customers_email_address', $mail_validator->error_msg);
                }
                else{
                    $model->addError('customers_email_address', \Yii::t('verifyEmail', 'INVALID_EMAIL'));
                }
            }
        }

        return $this->renderSignupForm($f_data, $captcha_com);
    }

    protected function renderSignupForm($f_data, $captcha_com)
    {
        Yii::$app->session['sgstime'] = time();
        if (Yii::$app->getRequest()->getIsPost() && !empty($f_data['model']->customers_email_address)) {
            $f_data['prev_mail'] = GeneralComponent::encrypt($f_data['model']->customers_email_address, Yii::$app->session['sgstime']);
        }

        // social network
        $f_data['sns'] = count(Yii::$app->hybridauth->getAllowedProviders());

        // pre-fill email address
        if (isset($_GET['email']) && !isset($f_data['mail'])) {
            $f_data['mail'] = $_GET['email'];
        }

        $f_data['is_mode_launcher'] = $this->isModeLauncher();
        $f_data['captcha'] = isset($captcha_com) ? $captcha_com->load_html() : null;

        return $this->render('signUp', $f_data);
    }

    public function actionDevice()
    {
        // 3rd party portal URL query
        $f_data['f_query'] = GeneralComponent::getRequestURI();
        $req_uri = (empty($f_data['f_query']) ? "" : "?") . $f_data['f_query'];

        if ((Yii::$app->getRequest()->getIsPost() && isset(Yii::$app->session['sgid']) && isset($_POST[Yii::$app->session['sgid']])) || isset(Yii::$app->session["device_val"])) {
            $mail = "";
            if (isset(Yii::$app->session["device_val"])) {
                parse_str(Yii::$app->session["device_val"], $f_data);
                unset(Yii::$app->session["device_val"]);

                $mail = (isset($f_data["mail"]) ? $f_data["mail"] : "");
            } else {
                $mail = $_POST[Yii::$app->session['sgid']];

                list($time, $sgid_val) = explode("/", Yii::$app->session['sgid']);
                $sgid = $time . "/" . md5($time . $mail . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY']);
                if ($sgid != Yii::$app->session['sgid']) {
                    $mail = "";
                }
            }
            $f_data['status'] = (isset($f_data['no_pin']) && $f_data['no_pin']) ? false : true;

            if (!empty($mail)) {
                $f_data["mail"] = $mail;
                $m_cust = Customers::findOne(['customers_email_address' => $mail, 'customers_status' => 1]);

                if (Yii::$app->session["device_trial"] >= Yii::$app->params["SSO_CONFIG"]["FORGET_EMAIL_TRIAL"]) {
                    ;
                } else if (isset($_POST['pin'])) {
                    $m_data["code"] = $_POST['pin'];
                    if (isset($m_cust->customers_id)) {
                        $m_data["cid"] = $m_cust->customers_id;
                    }

                    $r_scm = SSOCom::secureCodeMatch(SSOCom::$otp_key_device, $m_data);
                    if (isset($r_scm["result"]["cid"])) {
                        $this->unsetDevicePinAttempt($m_cust->customers_id);
                        if ($this->_authenticate($r_scm["result"]["cid"], "", SSOCom::$otp_key_device)) {
                            if (isset($r_scm["error"])) {
                                Yii::$app->session->setFlash('danger', $r_scm["error"]);
                            }
                            return $this->redirect(Url::to(['sso/login'], true) . $req_uri);
                        }
                    } else {
                        Yii::$app->session["device_trial"] += 1;
                        //If this is last try, we send email to customer to warn them!
                        if (Yii::$app->session["device_trial"] == (Yii::$app->params["SSO_CONFIG"]["FORGET_EMAIL_TRIAL"])) {
                            if (isset($m_cust->customers_id)) {
                                $this->_devicePin($mail, $m_cust->customers_id, implode(" ", [$m_cust->customers_firstname, $m_cust->customers_lastname]), $f_data['f_query'], 2);
                            }
                        }
                        Yii::$app->session->setFlash('danger', (!empty($r_scm["error"]) ? $r_scm["error"] : Yii::t("sso", "ERROR_DEVICE_INVALID_CODE")));
                    }
                }
                $time = time();
                Yii::$app->session['sgid'] = $time . "/" . md5($time . $mail . Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY']);
                $f_data['token'] = Yii::$app->session['sgid'];
                if (Yii::$app->session["device_trial"] >= Yii::$app->params["SSO_CONFIG"]["FORGET_EMAIL_TRIAL"]) {
                    $f_data['status'] = false;
                }
                if (\common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($m_cust->customers_id)) {
                    return $this->render('2fa', $f_data);
                }
                return $this->render('device', $f_data);
            }
        }

        return $this->redirect(Yii::$app->user->loginUrl);
    }

    public function actionForget()
    {
        /*
         * exclude from 3rd party portal redirect
         */
        $phone_owner = "";
        $logged_in = !Yii::$app->user->isGuest;
        $model_passwd = new ForgetForm([
            'scenario' => 'forget_password',
        ]);
        $model_mail = new ForgetForm([
            'scenario' => 'forget_email',
            'dial_ctry_code' => Yii::$app->session["ctry_id"],
        ]);

        if (Yii::$app->getRequest()->getIsPost()) {
            if (isset($_POST["submit_passwd"])) {
                if ($model_passwd->load($_POST) && $model_passwd->validate()) {
                    $m_cust = Customers::findOne(['customers_email_address' => $model_passwd->email]);
                    if (isset($m_cust->customers_id)) {
                        $this->_passwordPin($m_cust->customers_id, $m_cust->customers_email_address, $m_cust->customers_firstname, $m_cust->customers_lastname);
                    }

                    Yii::$app->session->setFlash('success', Yii::t("sso", "TEXT_RESET_SUCCESS"));
                    return $this->redirect(($logged_in) ? ['profile/security'] : Url::to(['sso/login'], true) . '?' . GeneralComponent::getRequestURI());
                }
            }

            if (!$logged_in && isset($_POST["submit_mail"])) {
                if ($model_mail->load($_POST) && $model_mail->validate()) {
                    $m_ctry = Countries::findOne([
                        'countries_id' => $model_mail->dial_ctry_code,
                    ]);

                    if (isset($m_ctry->countries_id)) {
                        $civ_obj = new CustomersInfoVerificationComponent();
                        $parse_result = $civ_obj->parseTelephone($model_mail->phone, $m_ctry->countries_id);
                        $phone_number = $parse_result->national_number;

                        $m_cust = Customers::findAll([
                            'customers_country_dialing_code_id' => $m_ctry->countries_id,
                            'customers_telephone' => $phone_number,
                        ]);
                        if (!empty($m_cust)) {
                            foreach ($m_cust as $_num => $_data) {
                                if (
                                    isset($_data->customers_id) && CustomersInfoVerification::find()->where([
                                        'customers_id' => $_data->customers_id,
                                        'customers_info_value' => $m_ctry->countries_international_dialing_code . $phone_number,
                                        'info_verification_type' => 'telephone',
                                        'info_verified' => '1',
                                    ])->exists()
                                ) {
                                    $phone_owner = GeneralComponent::createEmailMask($_data->customers_email_address);
                                    break;
                                }
                            }
                        }

                        if (empty($phone_owner)) {
                            Yii::$app->session['fmail_trial'] = (isset(Yii::$app->session['fmail_trial']) ? Yii::$app->session['fmail_trial'] + 1 : 0);

                            if (isset(Yii::$app->session['fmail_trial']) && (Yii::$app->session['fmail_trial'] >= Yii::$app->params["SSO_CONFIG"]["FORGET_EMAIL_TRIAL"]) && !isset(Yii::$app->request->cookies['safm'])) {
                                \Yii::$app->response->cookies->add(new Cookie([
                                    'name' => 'safm',
                                    'value' => true,
                                    'expire' => time() + Yii::$app->params["SSO_CONFIG"]["FORGET_EMAIL_LIFETIME"],
                                    'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                                ]));
                            } else {
                                Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_FORGOT_EMAIL_ADDRESS"));
                            }
                        }
                    }
                }
            }
        }

        $referer = Yii::$app->getRequest()->getReferrer();
        //To return user back to their request, ensure first that it is from the
        //same base URL. Otherwise, just return to overview or login page
        if ($logged_in && strpos($referer, Yii::$app->request->getHostInfo()) === 0) {
            $returnUrl = $referer;
        } else {
            $returnUrl = Url::to(['sso/login'], true) . '?' . GeneralComponent::getRequestURI();
        }

        $f_data = array(
            "model_mail" => $model_mail,
            "model_passwd" => $model_passwd,
            "ctry" => Countries::model()->getAllCountries(false),
            "phone_owner" => $phone_owner,
            "logged_in" => $logged_in,
            "returnUrl" => $returnUrl,
        );

        return $this->render('forget', $f_data);
    }

    public function actionForgetReset()
    {
        /*
         * exclude from 3rd party portal redirect
         */
        if (isset($_GET['c']) || isset($_POST['code'])) {
            $model = new ResetPasswordForm();

            $f_data = array(
                'model' => $model,
                'code' => (isset($_GET['c']) ? $_GET['c'] : $_POST['code'])
            );

            if (isset($_POST['code']) && $model->load($_POST) && $model->validate()) {
                $_input = array(
                    "code" => $_POST['code'],
                    "passwd" => $model->customers_password,
                    "cfm_passwd" => $model->confirm_password,
                );
                $r_scm = SSOCom::secureCodeMatch(SSOCom::$otp_key_password, $_input);
                $f_data = array_merge($f_data, $r_scm);
                if ($r_scm['status']) {
                    if (!Yii::$app->user->isGuest && isset($r_scm["result"]["cid"]) && ($r_scm["result"]["cid"] != Yii::$app->user->id)) {
                        $identity = new UserIdentity();
                        $identity->logout();
                        Yii::$app->hybridauth->logout();
                        Yii::$app->user->logout();
                    }

                    //kick user
                    \common\components\SSOCom::kickUser($r_scm["result"]["cid"], \Yii::$app->session->id, (isset($_COOKIE['ogm']['uc']) ? $_COOKIE['ogm']['uc'] : ''));
                    return $this->redirect(Yii::$app->user->loginUrl);
                } else if (!$r_scm["status"] && !empty($r_scm["error"])) {
                    Yii::$app->session->setFlash('danger', $r_scm["error"]);
                }
            }

            return $this->render('forgetReset', $f_data);
        } else {
            return $this->redirect(Yii::$app->user->loginUrl);
        }
    }

    public function actionXpress()
    {
        if ($this->isModeLauncher()) {
            Yii::$app->hybridauth->base_url .= '?mode=launcher';
        }
        if (Yii::$app->getRequest()->getIsPost() && isset($_POST['remember_me']) && $_POST['remember_me']) {
            Yii::$app->response->cookies->add(new Cookie([
                'name' => 'S3RM',
                'value' => 1,
                'expire' => time() + (60 * 15),
                'domain' => Yii::$app->params['COOKIE_DOMAIN'],
            ]));
            return $this->redirect(Yii::$app->request->url);
        }
        if (Yii::$app->hybridauth->login()) {
            $login_method = "sns";
            $status = 1;

            $sns = Yii::$app->hybridauth->getSession('ha_provider');
            $sns_uid = Yii::$app->hybridauth->getSession('ha_uid');
            $sns_uname = Yii::$app->hybridauth->getSession('ha_email');
            $mail = Yii::$app->hybridauth->getSession('ha_email');
            $fname = Yii::$app->hybridauth->getSession('ha_firstName');
            $lname = Yii::$app->hybridauth->getSession('ha_lastName');

            if (!empty($sns_uid)) {
                $m_sc = SnsConnection::findOne([
                    "provider" => $sns,
                    "provider_uid" => $sns_uid
                ]);

                if (isset($m_sc->customers_id)) {
                    // current sns-bind user
                    if (Yii::$app->user->isGuest || (!Yii::$app->user->isGuest && (Yii::$app->user->id != $m_sc->customers_id))) {
                        $identity = new UserIdentity();
                        $identity->logout();
                        Yii::$app->user->logout(false);

                        $status = $this->_authenticate("", "", "sns");
                    } else {
                        $status = 0;
                    }
                } else if (!empty($mail) && !in_array($sns, SnsConnection::$sns_force_xpress_form)) {

                    // auto bind or create account
                    $m_cust = Customers::findOne(['customers_email_address' => $mail]);

                    if (!isset($m_cust->customers_id)) {
                        // register
                        $_input = array();
                        if (!empty($fname)) {
                            $_input['fname'] = $fname;
                        }
                        if (!empty($lname)) {
                            $_input['lname'] = $lname;
                        }
                        $status = $this->_xpressSignUp($mail, $sns, $sns_uid, $sns_uname, $_input);
                    } else {
                        if (isset($m_cust->customers_id) && $m_cust->customers_status) {
                            if (isset($_GET["snsdc"])) {
                                $r_scm = SSOCom::secureCodeMatch(SSOCom::$otp_key_sns_detach, array("cid" => $m_cust->customers_id, "code" => $_GET["snsdc"]));
                                if ($r_scm["status"]) {
                                    SnsConnection::model()->removeConnection($m_cust->customers_id, $sns);
                                }
                            }
                            $status = $this->_xpressBind($m_cust->customers_id, $sns, $sns_uid, $sns_uname);
                        } else {
                            $status = 5;
                        }
                    }
                } else {
                    // xpress page
                    $f_data = array(
                        'sns' => $sns,
                        'photo' => Yii::$app->hybridauth->getSession('ha_photo'),
                        'mail' => $mail,
                        'fname' => $fname,
                        'lname' => $lname,
                        'f_query' => GeneralComponent::getRequestURI(),
                    );

                    if (Yii::$app->getRequest()->getIsPost() && isset($_POST["login_method"])) {

                        $f_data["login_method"] = $_POST["login_method"];
                        /* @var $model Model */
                        $model = $_POST['login_method'] == 'login' ? new LoginForm() : new XpressSignupForm();

                        if ($model->load($_POST) && $model->validate()) {
                            UserIdentity::login_trial();

                            $m_cust = Customers::findOne(array('customers_email_address' => $model->customers_email_address));
                            $login_method = $_POST["login_method"];

                            switch ($_POST['login_method']) {
                                case 'login': // bind account
                                    if (isset($m_cust->customers_id) && $m_cust->customers_status) {
                                        if (Password::validatePassword($model->customers_password, $m_cust->customers_password)) {
                                            $status = $this->_xpressBind($m_cust->customers_id, $sns, $sns_uid, $sns_uname);
                                        } else {
                                            $status = 1;
                                        }
                                    }
                                    break;

                                case 'signup': // register
                                    if (!isset($m_cust->customers_id)) {
                                        $_input = array(
                                            'fname' => $model->customers_firstname,
                                            'lname' => $model->customers_lastname,
                                            'passwd' => $model->customers_password,
                                        );
                                        $status = $this->_xpressSignUp($model->customers_email_address, $sns, $sns_uid, $sns_uname, $_input);
                                    } else {
                                        $status = 6;
                                    }
                                    break;
                            }

                            $_data = array(
                                "sns" => $sns,
                                "login_method" => $login_method
                            );
                            UserIdentity::statusAction($status, $_data);
                        }

                        $f_data["model"] = $model;
                        switch ($_POST['login_method']) {
                            case 'login':
                                return $this->render('xpressLogin', $f_data);

                            case 'signup':
                                return $this->render('xpressSignUp', $f_data);
                        }
                    }

                    return $this->render('xpress', $f_data);
                }

                $_data = array(
                    "sns" => $sns,
                    "login_method" => $login_method,
                    "uid" => (isset($m_cust->customers_id) ? $m_cust->customers_id : "")
                );
                UserIdentity::statusAction($status, $_data);
            }
        }

        if (isset($_GET["ha_provider"])) {
            UserIdentity::login_trial();
        }

        if (Yii::$app->hybridauth->error) {
            return $this->render('/site/error404', ['type' => '500', 'f_message' => Yii::$app->hybridauth->error]);
        } else {
            return $this->redirect(Yii::$app->user->loginUrl);
        }
    }

    private function _setCustomerSetting($id)
    {
        //Set customers_setting
        $custSetting = \common\models\CustomersSetting::findOne(['customers_id' => $id, 'customers_setting_key' => \common\models\CustomersSetting::SETUP_ACCOUT_KEY]);
        if (!empty($custSetting)) {
            $custSetting->customers_setting_value = $custSetting->customers_setting_value + 1;
            $custSetting->updated_datetime = date("Y-m-d H:i:s");
            if ($custSetting->validate() && $custSetting->save()) {
                $custSetting->removeUpdateCustSetting();
                return \common\models\CustomersInfo::model()->updateLastModify();
            }
        } else {
            $custSetting = new \common\models\CustomersSetting();
            $custSetting->customers_id = $this->user->id;
            $custSetting->customers_setting_key = \common\models\CustomersSetting::SETUP_ACCOUT_KEY;
            $custSetting->customers_setting_value = 1;
            $custSetting->created_datetime = date("Y-m-d H:i:s");
            $custSetting->updated_datetime = date("Y-m-d H:i:s");
            $custSetting->save();
            $custSetting->removeUpdateCustSetting();
            return \common\models\CustomersInfo::model()->updateLastModify();
        }
    }

    private function _dataLog($orig, $data, $email)
    {
        // customers data change log
        $_diff = GeneralComponent::arrayDiff($orig, $data);
        if ($_diff) {
            $log_obj = new LogFilesComponent(Yii::$app->user->id);
            $mod = $log_obj->detectChanges($_diff, $data);
            $_log = $log_obj->constructLogMessage($mod);

            $change_log = $log_obj->contructChangesString($mod, "");
            CustomersInfo::model()->updateChangeMade($change_log);

            $change_str = "Changes setup made:\n";
            for ($i = 0, $cnt = count($_log); $cnt > $i; $i++) {
                if (is_array($_log[$i])) {
                    foreach ($_log[$i] as $field => $res) {
                        if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                            $change_str .= $res['text'] . "\n";
                        } else {
                            $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                        }
                    }
                }
            }
            return $log_obj->insertCustomerHistoryLog($email, $change_str);
        }
    }

    private function _authenticate($mail = "", $passwd = "", $method = "normal")
    {
        $obj_uid = new UserIdentity($mail, $passwd);
        $obj_uid->login_method = $method;
        $obj_uid->authenticate();

        // 3rd party portal URL query
        $req_uri = GeneralComponent::getRequestURI();

        $status = (int) $obj_uid->errorCode;
        switch ($status) {
            case 0: // success
                $this->redirect(Url::to(['sso/login-all']) . (!empty($req_uri) ? "?" : "") . $req_uri);
                Yii::$app->end();
                break;

            case 4: // device pin
                $f_data['f_query'] = $req_uri;

                $m_cust = Customers::findOne(['customers_email_address' => $obj_uid->username, 'customers_status' => 1]);

                if (isset($m_cust->customers_id)) {
                    $device_pin = $this->_devicePin($m_cust->customers_email_address, $m_cust->customers_id, implode(" ", array($m_cust->customers_firstname, $m_cust->customers_lastname)), $req_uri);
                    if ($device_pin && $device_pin['limit_hit']) {
                        if (empty($device_pin['device_pin'])) {
                            $f_data['no_pin'] = 1;
                        } else {
                            Yii::$app->session->setFlash('danger', Yii::t("sso", "ERROR_DEVICE_PIN_EMAIL_LIMIT_HIT"));
                        }
                    }
                    $f_data['mail'] = $m_cust->customers_email_address;
                }

                // user has ticked `remember-me` during login
                if (Yii::$app->getRequest()->getIsPost() && isset($_POST["remember_me"])) {
                    Yii::$app->response->cookies->add(new Cookie([
                        'name' => 'S3RM',
                        'value' => 1,
                        'expire' => time() + (60 * 15),
                        'domain' => Yii::$app->params['COOKIE_DOMAIN'],
                    ]));
                }
                Yii::$app->session["device_val"] = http_build_query($f_data);
                Yii::$app->session["device_trial"] = 0;
                $this->redirect(Url::to(['sso/device']) . (!empty($req_uri) ? "?" : "") . $req_uri);
                Yii::$app->end();
                break;
        }

        return $status;
    }

    private function _devicePin($mail, $cid, $cname, $f_query, $type = 1)
    {
        /*
         * $type
         * 1 : new device pin
         * 2 : max trial warning
         */
        $mail_title = "";
        $mail_txt = "";
        $return = null;

        $support_url = \Yii::$app->params['support_url'];
        if (isset($f_query) && !empty($f_query)) {
            parse_str($f_query, $tmp_arr);
            // 3rd party portal URL query
            if (isset($tmp_arr["service"])) {
                if (isset(\Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$tmp_arr["service"]]['support_url'])) {
                    $support_url = \Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$tmp_arr["service"]]['support_url'];
                }
            }
        }

        switch ($type) {
            case 1: // new device pin
                $limit_hit = !\common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($cid) && $this->checkIsDevicePinAttemptOverLimit($cid);
                if ($limit_hit) {
                    $device_pin = SSOCom::secureCodeRequest(SSOCom::$otp_key_device, $cid, false);
                } else {
                    $device_pin = SSOCom::secureCodeRequest(SSOCom::$otp_key_device, $cid);

                    if (!isset($device_pin)) {
                        //No need to send email if no device pin, in the case of 2FA, for example
                        return;
                    }

                    $mail_title = Yii::t('sso', 'A new device accessed your OffGamers account - Device PIN:{DEVICE_PIN}', ['DEVICE_PIN' => $device_pin]);

                    $emailLayout = new EmailComponent();
                    $f_data = array(
                        "title" => $mail_title,
                        'cname' => $cname,
                        'device_pin' => $device_pin,
                        'support_url' => $support_url,
                    );
                    $mail_txt = [
                        "content_html" => $emailLayout->setRenderEmailhtml('new_device-html', $f_data),
                        "content_text" => $emailLayout->setRenderEmailText('new_device-text', $f_data),
                    ];
                }
                $return = [
                    'limit_hit' => $limit_hit,
                    'device_pin' => $device_pin,
                ];
                break;

            case 2: // max trial warning
                //Do not send email if it is Device PIN method (not 2FA), because Device PIN method user is already receiving Device PIN emails.
                if (\common\components\TwoFactorAuth::isUserTwoFactorAuthEnabled($cid) && !$this->checkIsFailedDevicePinEmailOverLimit($cid)) {
                    $mail_title = Yii::t('sso', 'New device attempt failed');
                    $emailLayout = new EmailComponent();
                    $f_data = array(
                        "title" => $mail_title,
                        'cname' => $cname,
                        'email' => $mail,
                        'support_url' => $support_url,
                    );
                    $mail_txt = [
                        "content_html" => $emailLayout->setRenderEmailhtml('new_device_failed-html', $f_data),
                        "content_text" => $emailLayout->setRenderEmailText('new_device_failed-text', $f_data),
                    ];
                }
                break;
        }

        if (!empty($mail_txt)) {
            $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . $mail_title;

            $mail_obj = new EmailComponent();
            $mail_obj->sendMail($cname, $mail, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
        }
        return $return;
    }

    private function _passwordPin($cid, $mail, $fname, $lname)
    {
        $status = false;
        $recover_key = SSOCom::secureCodeRequest(SSOCom::$otp_key_password, $cid);
        if (!empty($recover_key)) {
            $status = true;
            $mail_obj = new EmailComponent();
            // recover mail
            $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('sso', 'EMAIL_RESET_PASSWORD_TITLE');
            $f_data = array(
                'title' => $mail_sub,
                'cname' => $fname,
                'link' => Url::to(['sso/forget-reset', 'c' => $recover_key], true),
            );
            $mail_txt = [
                "content_html" => $mail_obj->setRenderEmailhtml('how_to_recover_your_password-html', $f_data),
                "content_text" => $mail_obj->setRenderEmailText('how_to_recover_your_password-text', $f_data),
            ];

            $mail_obj->sendMail(implode(' ', array($fname, $lname)), $mail, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
        }

        return $status;
    }

    private function _xpressBind($uid, $sns, $sns_uid, $sns_uname)
    {
        /*
         * error code
         * 0 : success
         * 101 : already bind with other sns-account
         */
        $status = 1;

        if (
            SnsConnection::find()->where([
                'customers_id' => $uid,
                'provider' => $sns
            ])->exists()
        ) {
            $status = 101;
        } else {
            // user account not bind yet
            SnsConnection::model()->createConnection($uid, $sns, $sns_uid, $sns_uname);

            if (!Yii::$app->user->isGuest) {
                $identity = new UserIdentity();
                $identity->logout();
                Yii::$app->user->logout(false);
            }
            $status = $this->_authenticate("", "", "sns");
        }

        return $status;
    }

    private function _xpressSignUp($mail, $sns, $sns_uid, $sns_uname, $_input = array())
    {
        /*
         * $_input = array('fname', 'lname');
         */
        $status = 1;

        $passwd = !empty($_input['passwd']) ? $_input['passwd'] : GeneralComponent::createRandomValue(Configuration::model()->getConfigValue('ENTRY_PASSWORD_MIN_LENGTH'));
        $obj_uid = new UserIdentity($mail, $passwd);
        if ($obj_uid->register($_input)) {
            $m_cust = Customers::findOne(['customers_email_address' => $mail, 'customers_status' => 1]);
            if (isset($m_cust->customers_id)) {
                if (empty($_input['passwd'])) {
                    $recover_key = SSOCom::secureCodeRequest(SSOCom::$otp_key_password, $m_cust->customers_id);
                    if (!empty($recover_key)) {
                        $passwd_link = Url::to(['sso/forget-reset', 'c' => $recover_key], true);
                    } else {
                        $passwd_link = Url::to(['sso/forget'], true);
                    }

                    $mail_obj = new EmailComponent();
                    $mail_sub = Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('sso', 'EMAIL_XPRESS_SIGNUP_TITLE', array('SYS_SITE_NAME' => Yii::$app->name));
                    $mail_txt = Yii::t('sso', 'EMAIL_XPRESS_SIGNUP_TEXT', array("SYS_CUSTOMER_NAME" => $m_cust->customers_firstname, "SYS_SITE_NAME" => Yii::$app->name, "SYS_CUSTOMER_EMAIL" => $m_cust->customers_email_address, "SYS_RESET_PASSWORD_LINK" => \yii\helpers\Html::a(Yii::t('general', 'TEXT_HERE'), $passwd_link)));
                    $mail_obj->sendMail($m_cust->customers_firstname, $m_cust->customers_email_address, $mail_sub, $mail_txt, Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);
                }
                SnsConnection::model()->createConnection($m_cust->customers_id, $sns, $sns_uid, $sns_uname);

                $obj_uid->login_method = "sns";
                $obj_uid->authenticate();
                $status = (int) $obj_uid->errorCode;
            }
        }

        return $status;
    }

    protected function isModeLauncher()
    {
        return isset($_GET['mode']) && $_GET['mode'] == 'launcher';
    }

    private function checkIsDevicePinAttemptOverLimit($id)
    {
        $custSetting = \common\models\CustomersSetting::findOne(['customers_id' => $id, 'customers_setting_key' => SSOCom::$otp_key_device]);

        if (!empty($custSetting)) {
            //clear the attempt to 0 after 2hours
            $time = time();
            if (strtotime($custSetting->updated_datetime) < mktime(date('G', $time) - 2, 0, 0, date('n', $time), date('j', $time), date('Y', $time))) {
                $custSetting->customers_setting_value = 0;
                $custSetting->updated_datetime = date("Y-m-d H:i:s");
            }
        } else {
            $custSetting = new \common\models\CustomersSetting();
            $custSetting->customers_id = $id;
            $custSetting->customers_setting_key = SSOCom::$otp_key_device;
            $custSetting->customers_setting_value = 0;
            $custSetting->created_datetime = date("Y-m-d H:i:s");
            $custSetting->updated_datetime = date("Y-m-d H:i:s");
        }

        $custSetting->customers_setting_value = $custSetting->customers_setting_value + 1;
        if ($custSetting->customers_setting_value > 2) {
            return true;
        }
        $custSetting->save();
        return false;
    }

    private function unsetDevicePinAttempt($id)
    {
        $custSetting = \common\models\CustomersSetting::findOne(['customers_id' => $id, 'customers_setting_key' => SSOCom::$otp_key_device]);
        if ($custSetting) {
            $custSetting->saveValue(0);
        }
    }

    private function checkIsFailedDevicePinEmailOverLimit($id)
    {
        $custSetting = \common\models\CustomersSetting::findOne(['customers_id' => $id, 'customers_setting_key' => CustomersSetting::DEVICE_PIN_FAILED_EMAIL_COUNT]);

        if (!empty($custSetting)) {
            //clear the attempt to 0 per day
            $time = time();
            if (strtotime($custSetting->updated_datetime) < mktime(0, 0, 0, date('n', $time), date('j', $time) - 1, date('Y', $time))) {
                $custSetting->customers_setting_value = 0;
                $custSetting->updated_datetime = date("Y-m-d H:i:s");
            }
        } else {
            $custSetting = new \common\models\CustomersSetting();
            $custSetting->customers_id = $id;
            $custSetting->customers_setting_key = CustomersSetting::DEVICE_PIN_FAILED_EMAIL_COUNT;
            $custSetting->customers_setting_value = 0;
            $custSetting->created_datetime = date("Y-m-d H:i:s");
            $custSetting->updated_datetime = date("Y-m-d H:i:s");
        }

        $custSetting->customers_setting_value = $custSetting->customers_setting_value + 1;
        if ($custSetting->customers_setting_value > 1) {
            return true;
        }
        $custSetting->save();
        return false;
    }

}