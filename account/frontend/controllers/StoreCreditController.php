<?php

namespace frontend\controllers;

use common\components\CurrenciesComponent;
use common\components\GeneralComponent;
use common\components\GiftCardCom;
use common\components\OrderComponent;
use common\components\SSOCom;
use common\components\StoreCreditCom;
use common\models\Categories;
use common\models\CouponGvCustomer;
use common\models\Customers;
use common\models\CustomersScCart;
use common\models\Orders;
use common\models\OrdersProducts;
use frontend\components\Captcha;
use frontend\components\RegionalCom;
use Yii;
use yii\base\ErrorException;
use yii\db\Expression;
use frontend\components\Controller;
use yii\helpers\Url;

class StoreCreditController extends Controller {

    public $layout = 'main-col2';

    public $menu = 'LINK_SC_TOPUP';

    const SESSION_KEY_CONVERSION_RESULT = 'store-credit/convert-success/conv_result',
            SESSION_KEY_GC_REDEEM_RESULT = 'store-credit/redeem-gift-card-success/redeem_result';

    public function beforeAction($action) {
        if (isset($_SERVER['REQUEST_METHOD'])) {
            if (isset($_POST) && !empty($_POST)) {
                $_POST = GeneralComponent::purify($_POST);
            }

            if (isset($_GET) && !empty($_GET)) {
                $_GET = GeneralComponent::purify($_GET);
            }
        }

        return parent::beforeAction($action);
    }

    public function actionIndex() {
        $this->menu = 'LINK_GROUP_SC';

        return $this->render('storeCredit');
    }

    public function actionStatement() {
        /*
         * X : Cancel
         * C : Compensate
         * MI: Manual Addition
         * MR: Manual Deduction
         * P : Purchase
         * R : Refund
         * B : Bonus
         * D : Redeem
         * S : Store Credit Top Up
         * XS: Extra Store Credit
         */

        $dates = [];
        $now = time();
        $mth = date('n', $now);
        $yr = date('Y', $now);
        for($i = 0; $i < StoreCreditCom::$stmt_filt_month; $i ++) {
            $time = mktime(0, 0, 0, $mth - $i, 1, $yr);
            $dates[$time] = $i == 0 ? \Yii::t('wor', 'LABEL_STATEMENT_THIS_MONTH') : \Yii::t('storeCredit', 'LABEL_STATEMENT_DATE', [
                'SYS_DATE' => date('M Y', $time)
            ]);
        }
        reset($dates);
        $date = (isset($_GET['date']) && isset($dates[$_GET['date']])) ? $_GET['date'] : key($dates);
        $page = (isset($_GET["page"]) ? (int) $_GET["page"] : 1);
        $filt = (isset($_POST["filt"]) ? $_POST["filt"] : (isset($_GET["filt"]) ? $_GET["filt"] : ""));
        $result = StoreCreditCom::statement($page, Yii::$app->params["GENERAL_CONFIG"]["PAGER_ITEM_PER_PAGE"], $filt, $date);
        $f_data = array(
            "data" => $result["result"],
            "balance" =>$result["balance"],
            "dates" => $dates,
            "date" => $date,
            "pagination" => array(
                "current" => $page,
                "record" => count($result["result"]),
                "total" => $result["total"],
                "query" =>array("date" => $date)
            )
        );

        $this->menu = 'LINK_SC_STATEMENT';
        return $this->render('statement', $f_data);
    }

    public function actionHistory() {
        /*
         * X : Cancel
         * C : Compensate
         * MI: Manual Addition
         * MR: Manual Deduction
         * P : Purchase
         * R : Refund
         * B : Bonus
         * D : Redeem
         * S : Store Credit Top Up
         * XS: Extra Store Credit
         */

        $page = (isset($_GET["page"]) ? (int) $_GET["page"] : 1);
        $filt = (isset($_POST["filt"]) ? $_POST["filt"] : (isset($_GET["filt"]) ? $_GET["filt"] : ""));
        $result = StoreCreditCom::history($page, Yii::$app->params["GENERAL_CONFIG"]["PAGER_ITEM_PER_PAGE"], $filt);

        $f_data = array(
            "data" => $result["result"],
            "pagination" => array(
                "current" => $page,
                "record" => count($result["result"]),
                "total" => $result["total"],
                "query" => array("filt" => $filt)
            )
        );

        $this->menu = 'LINK_SC_HISTORY';
        return $this->render('history', $f_data);
    }

    protected function getAllowedCurrencies($sc_obj) {
        $cur_obj = new CurrenciesComponent();
        $currencies = RegionalCom::getCurrenciesForCountry();
        $array = array();
        foreach($currencies as $cur_code => $cur_name) {
            if (!isset($cur_obj->currencies[$cur_code])) {
                continue;
            }
            $_data = $cur_obj->currencies[$cur_code];
            $min_topup = $sc_obj->getMinimumStoreCredit($cur_code);
            $max_topup = $sc_obj->getMaximumStoreCredit($cur_code);
            $deno_list = [];
            foreach(!empty(Yii::$app->params['currency_denominations'][$cur_code]) ? Yii::$app->params['currency_denominations'][$cur_code] : [] as $deno) {
                if ($min_topup <= $deno && $max_topup >= $deno) {
                    $deno_list[] = $deno;
                }
            }

            $array[$cur_code] = array(
                'label' => $_data['title']  . " ( " . html_entity_decode($_data['symbol_left'], ENT_NOQUOTES, 'UTF-8') . " " . html_entity_decode($_data['symbol_right'], ENT_NOQUOTES, 'UTF-8') . " ) ",
                'name' => $_data['title'],
                'min_topup' => $min_topup,
                'max_topup' => $max_topup,
                'min_topup_display' => number_format($min_topup, 0),
                'max_topup_display' => number_format($max_topup, 0),
                'symbol_left' => $_data['symbol_left'],
                'symbol_right' => $_data['symbol_right'],
                'deno' => $deno_list,
            );
        }
        return $array;
    }
}
