<?php

namespace frontend\controllers;

use common\components\GeneralComponent;
use common\components\StoreCreditCom;
use common\models\StorePoints;
use frontend\components\RegionalCom;
use frontend\components\Controller;
use Yii;
use yii\filters\AccessControl;
use yii\helpers\Json;

class UserBarController extends Controller {

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'actions' => ['regional'],
                        'allow' => true,
                        'roles' => ['@', '?'],
                    ],
                    [
                        'actions' => ['user-menu'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['?'],
                    ]
                ],
            ],
        ]);
    }

    function actionRegional() {
        $this->layout = 'min';
        if (Yii::$app->getRequest()->getIsPost()) {
            $POST = GeneralComponent::purify($_POST);
            
            if (isset($POST['reg_lang'])) {
                if (!isset($POST['reg_ctry'])) {
                    $POST['reg_ctry_id'] = Yii::$app->session["ctry_id"];
                }
                if (!isset($POST['reg_cur'])) {
                    $POST['reg_cur'] = Yii::$app->session["cur_code"];
                }
                $result = RegionalCom::_validateRegionalData($POST);
                if (($result["ctry_id"] != Yii::$app->session["ctry_id"]) || ($result["cur_code"] != Yii::$app->session["cur_code"]) || ($result["lang_code"] != Yii::$app->session["lang_code"])) {
                    RegionalCom::updateCookie($result);
                    RegionalCom::updateSession($result);
                    RegionalCom::updateDB();
                }
            }
            if (isset($POST["returnUrl"]) && !empty($POST["returnUrl"])) {
                $returnUrl = \yii\helpers\Url::to([$POST["returnUrl"]], true);
            } else if (isset(Yii::$app->request->referrer)) {
                $returnUrl = Yii::$app->request->referrer;
            } else {
                $returnUrl = \yii\helpers\Url::to([Yii::$app->homeUrl[0]], true);
            }
            return $this->renderContent(\yii\helpers\Html::script("top.location.href = '" . \yii\helpers\Html::encode($returnUrl, true) . "';"));
        }
        return $this->renderContent(\yii\helpers\Html::script("top.location.href = '" . \yii\helpers\Url::to([Yii::$app->homeUrl[0]], true) . "';"));
    }

    function actionUserMenu() {
        echo Json::encode(array(
            "status" => false,
            "result" => array(
                "store_credit" => StoreCreditCom::storeCreditBalance(true),
                "wor" => number_format(StorePoints::model()->getCustomersStorePoint(), 0, '.', ',')
            ),
            "error" => ""
        ));
    }

}
