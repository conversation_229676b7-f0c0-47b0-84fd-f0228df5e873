<?php

namespace frontend\controllers;

use dpodium\pipwave\sdk\models\api\InitiateVerificationSessionForm;
use common\components\CustomersInfoVerificationComponent;
use common\components\GeneralComponent;
use common\models\CustomersVerificationDocument;
use common\models\CustomersVerificationDocumentLog;
use common\models\CustomerPipwaveVerificationToken;
use frontend\components\Controller;
use common\models\Customers;
use common\models\AnalysisCreditCardInfo;
use GuzzleHttp\Client;
use Yii;

class VerificationFormController extends Controller {

    public $layout = 'main-col2';
    public $menu = 'LINK_VERIFICATION_FORM';

    const PIPWAVE_VERIFICATION_CACHE_DURATION = 900;  //30 minutes result caching

    public function beforeAction($action) {
        if ($action->id == 'upload' && empty($_FILES)) {
            //Overloaded upload, so we disable CSRF to avoid CSRF error message
            $this->enableCsrfValidation = false;
        }
        return parent::beforeAction($action);
    }

    public function actionIndex() {

        return (Yii::$app->request->get("order_id")) ? $this->redirect(['verification-form/offgamers', 'order_id' => Yii::$app->request->get("offgamers")]) : $this->redirect(['verification-form/offgamers']);
    }

    public function actionOffgamers() {
        $f_data = $this->commonVerification();
        $f_data['brand'] = 'og';
        $f_data['brand_label'] = 'OffGamers';
        $f_data['order_id'] = Yii::$app->request->get("order_id");
        $f_data['api_key'] = \Yii::$app->params['pwapi.og_api_key'];
        $f_data['merchant'] = 'offgamers';
        return $this->render("index", $f_data);
    }

    public function commonVerification() {
        $f_data = [
            "require" => CustomersVerificationDocument::model()->requireVerification(),
            "files" => [],
        ];
        $f_data["show_submit"] = 0;

        if ($f_data["require"]) {
            $m_cvd = CustomersVerificationDocument::findOne([
                        'customers_id' => Yii::$app->user->id,
            ]);
            
            $display_order = ['003', '004', '001', '002', '005'];

            $files = [];
            $file_status = [];
            $status_code = [];
            foreach ($display_order as $key) {
                $lock_field = 'files_' . $key . '_locked';
                $file_lock_list[$key] = (isset($m_cvd->$lock_field) ? $m_cvd->$lock_field : 1);
                $status_field = 'files_' . $key . '_status';
                $file_status[$key] = $m_cvd->showStatusLabel($m_cvd->$status_field);
                $status_code[$key] = $m_cvd->$status_field;
            }
            if (!empty(Yii::$app->params['tmp.ekyc.allow_upload']) && in_array(Yii::$app->user->id, Yii::$app->params['tmp.ekyc.allow_upload'])) {
                $m_cvd_log_latest = CustomersVerificationDocumentLog::find()->select(['UNIX_TIMESTAMP(log_datetime) AS log_datetime'])->where([
                    'log_customers_id' => Yii::$app->user->id,
                    'log_docs_id' => '003',
                    'log_action' => 'UPLOAD',
                ])->orderBy('log_id DESC')->asArray()->one();
                if (!empty(Yii::$app->params['tmp.ekyc.allow_upload_before']) && (empty($m_cvd_log_latest['log_datetime']) || $m_cvd_log_latest['log_datetime'] < Yii::$app->params['tmp.ekyc.allow_upload_before'])) {
                    $status_code['003'] = 0;
                    $file_lock_list['003'] = 0;
                }
            }
            $f_data["files"] = $file_lock_list;
            $f_data["status"] = $file_status;
            $f_data['status_code'] = $status_code;
            $f_data["show_submit"] = (( $file_lock_list['002'] + $file_lock_list['005'] ) < 2 ) ? 1 : 0;
        } else {
            $flash = Yii::$app->session->getAllFlashes(false);
            if (!empty($flash)) {
                $f_data["files"] = array(
                    "003" => 1,
                    "004" => 1,
                    "001" => 1,
                    "002" => 1,
                    "005" => 1,
                );
            }
        }

        return $f_data;
    }

    public function actionGetVerificationToken() {
        if (Yii::$app->request->isPost) {
            $customer = Customers::findOne(\Yii::$app->user->id);
            $ver_type = Yii::$app->request->post("ver_type");
            $brand = Yii::$app->request->post("brand");
            $order_id = Yii::$app->request->post("order_id");
            $cache_key = 'xpipwave-verification/' . $ver_type . '/' . \Yii::$app->user->id . '/' . $brand . '/' . $order_id . '/session';
            $pipwave_session = \Yii::$app->cache->get($cache_key);

            $customer_document =  CustomersVerificationDocument::findOne(\Yii::$app->user->id)->getAttributes();



            switch ($ver_type) {
                case "selfie":
                    if ($customer_document['files_001_locked'] == 1) {
                         return json_encode(["error"=>Yii::t("verificationSubmission", "Document is locked. "). $ver_type ]);

                    }
                    break;
                case "identification":
                    if (!empty(Yii::$app->params['tmp.ekyc.allow_upload']) && in_array(Yii::$app->user->id, Yii::$app->params['tmp.ekyc.allow_upload'])) {
                        ;
                    } else if ($customer_document['files_003_locked'] == 1) {
                        return json_encode(["error"=>Yii::t("verificationSubmission", "Document is locked. "). $ver_type ]);
                    }
                    break;
                case "credit-card":
                    if ($customer_document['files_004_locked'] == 1) {
                        return json_encode(["error"=>Yii::t("verificationSubmission", "Document is locked. "). $ver_type ]);
                        return false;
                    }
                    break;
            }

            if ($pipwave_session) {
                echo $pipwave_session;
                return;
            }

            $cc_match = [];
            if (!empty($order_id)) {
                $credit_card = AnalysisCreditCardInfo::findOne($order_id);
                if ($credit_card) {
                    if (!empty($credit_card->bin_number)) {
                        $cc_match["match_text"]['credit-card-first6'] = $credit_card->bin_number;
                    }

                    if (!empty($credit_card->card_summary)) {
                        $cc_match["match_text"]['credit-card-last4'] = $credit_card->card_summary;
                    }
                }
            }

            $ver_types_arr = [
                "credit-card" => ["credit-card" => $cc_match], //match_text.credit-card-first6 match_text.credit-card-last4
                "identification" => ["identification" => ["match_text" => ["name" => $customer->customers_firstname . " " . $customer->customers_lastname]]],
                "selfie" => ["selfie" => []]
            ];

            $verification = $ver_types_arr[$ver_type];
            $allowed_mode = ['livefeed'];
            $api_model = new InitiateVerificationSessionForm([
//                    'merchant_id' => \Yii::$app->params['pwapi.merchant_id'],
                'api_key' => \Yii::$app->params['pwapi.' . $brand . '_api_key'],
                'api_secret' => \Yii::$app->params['pwapi.' . $brand . '_api_secret'],
                'action' => InitiateVerificationSessionForm::ACTION_CODE,
                'user_id' => \Yii::$app->user->id,
                'types_of_verification' => json_encode($verification),
                'allowed_mode' => implode(',', $allowed_mode),
                'notification_url' => \Yii::$app->params['shassoapi.sdk_noti'],
                'timestamp' => time(),
                'extra_param1' => $cache_key
            ]);
            $api_model->signature = $api_model->generateSignature();

//                $api = new \common\components\PipwaveAPI();
            $response = $this->sendVerificationDocApiCall($api_model, Yii::$app->params['pwapi.doc_url']);
            \Yii::$app->cache->set($cache_key, $response, self::PIPWAVE_VERIFICATION_CACHE_DURATION);

            $customer_token_model = new CustomerPipwaveVerificationToken();
            $customer_token_model->token = json_decode($response)->token;
            $customer_token_model->customer_id = \Yii::$app->user->id;
            $customer_token_model->result = 0;
            $time = time();
            $customer_token_model->created_at = $time;
            $customer_token_model->updated_at = $time;
            $customer_token_model->expire_at = $time + self::PIPWAVE_VERIFICATION_CACHE_DURATION;
            $customer_token_model->save();

            echo $response;
        }
    }

    public function actionVerificationCallback() {
        if (Yii::$app->request->isPost) {
            $token = Yii::$app->request->post("token");
            $status = Yii::$app->request->post("status");
            $customer_token_model = CustomerPipwaveVerificationToken::find()->where(['token' => $token])->one();
            if ($customer_token_model) {
                $customer_token_model->updateStatus($status);
                echo json_encode($customer_token_model->statusLabel());
            }
        }
    }

    public function sendVerificationDocApiCall($model, $url) {
        $url = $url ?: Yii::$app->params['pwapi.doc_url'];

        $arr = $model->attributes;
        unset($arr['api_secret']);
        //Remove variables necessary to generate signature
        $req_json = json_encode($arr);
//        $this->request = $req_json;

        $client = new Client();
        $response = $client->request('POST', $url, [
            'form_params' => $arr,
            'headers' => [
                'x-api-key' => $model->api_key,
            ]
        ]);

        return $response->getBody()->getContents();
    }

    public function actionUpload() {
        if (isset($_FILES) && !empty($_FILES)) {
            $upload_fail = array();
            $upload_success = array();

            $m_cvd = CustomersVerificationDocument::findOne([
                        'customers_id' => Yii::$app->user->id,
            ]);
            if (!isset($m_cvd->customers_id)) {
                CustomersVerificationDocument::model()->saveNewRecord(array("customers_id" => Yii::$app->user->id));
                $m_cvd = CustomersVerificationDocument::findOne([
                            'customers_id' => Yii::$app->user->id,
                ]);
            }

            if (isset($m_cvd->customers_id)) {
                if (!empty($_FILES['doc_003']['size']) || !empty($_FILES['doc_003b']['size'])) {
                    if (empty($_FILES['doc_003']['size']) || empty($_FILES['doc_003b']['size'])) {
                        $upload_fail['003'] = Yii::t("verificationSubmission", 'FAIL_BOTH_FILES_MUST_BE_UPLOADED');
                        unset($_FILES['doc_003']);
                    } else {
                        $this->merge003();
                    }
                    unset($_FILES['doc_003b']);
                }
                foreach ($_FILES as $_key => $_val) {
                    $file_locked = str_replace("doc_", "", $_key);
                    $m_files = "files_" . $file_locked;
                    $m_files_locked = "files_" . $file_locked . "_locked";

                    if ($_val["size"] > 0) {
                        $_val["type"] = strtolower(pathinfo($_val["name"], PATHINFO_EXTENSION));

                        $up_obj = new \frontend\components\UploadCom($_key);
                        $up_obj->set_encryption(Yii::$app->params["VERIFICATION_DOC"]["ENCRYPT_FILE"]);
                        $up_obj->set_extensions(Yii::$app->params["VERIFICATION_DOC"]["FILE_TYPE"]);
                        $up_obj->set_maxsize(Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"] * 1024);

                        $up_obj->set_bucket(Yii::$app->params["VERIFICATION_DOC"]["S3_BUCKET"]);
                        $up_obj->set_destination(Yii::$app->params["VERIFICATION_DOC"]["S3_DESTINATION"] . $file_locked . '/' . date('Ym') . '/' . Yii::$app->user->id . '/');
                        $up_obj->set_filename(date("YmdHis") . "." . $_val["type"]);

                        $up_obj->set_upload_destination(Yii::getAlias(Yii::$app->params["VERIFICATION_DOC"]["UPLOAD_DESTINATION"]));
                        $up_obj->set_upload_destination_filename(Yii::$app->user->id . "_" . $file_locked . "_" . date("YmdHis") . "." . $_val["type"]);

                        if ($up_obj->save()) {
                            $upload_success[$file_locked] = Yii::t("verificationSubmission", "TEXT_VERIFICATION_TYPE_" . $file_locked);

                            $m_cvd->$m_files = $up_obj->filename;
                            $m_cvd->$m_files_locked = 1;
                            $m_cvd->save();

                            $m_attr = [
                                'log_users_id' => Yii::$app->user->id,
                                'log_users_type' => 'customers',
                                'log_customers_id' => Yii::$app->user->id,
                                'log_docs_id' => $file_locked,
                                'log_IP' => GeneralComponent::getIpAddress(),
                                'log_datetime' => date("Y-m-d H:i:s"),
                                'log_filename' => $up_obj->filename,
                                'log_action' => 'UPLOAD'
                            ];
                            CustomersVerificationDocumentLog::model()->saveNewRecord($m_attr);
                            unset($m_attr);
                        } else {
                            $output_message = $up_obj->get_output_messages();
                            if (isset($output_message[0]["message"]) && !in_array($output_message[0]["message"], $upload_fail)) {
                                $upload_fail[$file_locked] = $output_message[0]["message"];
                            }
                        }
                        unset($up_obj);
                    }
                }
                unset($m_cvd);
            }

            if (!empty($upload_success)) {
                foreach ($upload_success as $file_locked => $filename) {
                    Yii::$app->session->setFlash('success-' . $file_locked, Yii::t("verificationSubmission", "SUCCESS_FILE_SAVED_SUCCESSFULLY", array("SYS_FILENAME" => $filename)));
                }
                CustomersInfoVerificationComponent::fileUploadNotification();
            }

            if (!empty($upload_fail)) {
                foreach ($upload_fail as $file_locked => $message) {
                    Yii::$app->session->setFlash('danger-'. $file_locked, $message);
                }
            }
        } else {
            Yii::$app->session->setFlash('danger', Yii::t('general', 'ERROR_FILESIZE_EXCEED'));
        }

        if (isset($_POST["task"]) && !empty($_POST["task"])) {
            if (($_POST["task"] == "upload") && !empty($upload_fail)) {
                foreach ($upload_fail as $num => $val) {
                    $return_message[] = '<div style="width:95%;padding:10px 0px;">' . $val . '</div>';
                }
                $return_message[] = '<div style="width:95%;height:20px;">&nbsp;</div>';
                Yii::$app->session['returnMsg'] = $return_message;
                unset($return_message);
            }

            echo \yii\helpers\Html::script("top.uploadComplete('" . (isset($_POST['verifyPhone']) ? $_POST['verifyPhone'] : "") . "');");
            Yii::$app->end();
        }
        return $this->goBack((!empty(Yii::$app->request->referrer) ? Yii::$app->request->referrer : null));
//        return $this->redirect($this->request->referrer);
    }

    protected function merge003() {
        $new_image = $this->mergeImages($_FILES['doc_003'], $_FILES['doc_003b']);
        imagejpeg($new_image, $_FILES['doc_003']['tmp_name'], 100); //save back to temp folder
        unset($_FILES['doc_003b']);
    }

    protected function mergeImages($image1, $image2) {
        $left = $this->createImageFromFile($image1);
        $right = $this->createImageFromFile($image2);
        list($left_width, $left_height) = getimagesize($image1['tmp_name']);
        list($right_width, $right_height) = getimagesize($image2['tmp_name']);

        // compute new width/height
        $new_width = $left_width + $right_width;
        $new_height = ($left_height > $right_height) ? $left_height : $right_height;

        // create new image and merge
        $new = imagecreatetruecolor($new_width, $new_height);
        imagecopy($new, $left, 0, 0, 0, 0, $left_width, $left_height);
        imagecopy($new, $right, $left_width + 1, 0, 0, 0, $right_width, $right_height);

        // save to file
        return $new;
    }

    protected function createImageFromFile($file) {
        $image = null;
        //Disable reporting because sometimes JPG exif issue will cause error
        $current_error_reporting = error_reporting();
        ini_set('gd.jpeg_ignore_warning', 1);
        error_reporting(E_ALL & ~E_NOTICE);
        $extension = (pathinfo($file['name'], PATHINFO_EXTENSION));
        switch (strtolower($extension)) {
            case 'jpeg':
            case 'jpg':
                $image = imagecreatefromjpeg($file['tmp_name']);
                break;
            case 'png':
                $image = imagecreatefrompng($file['tmp_name']);
                break;
            case 'gif':
                $image = imagecreatefromgif($file['tmp_name']);
                break;
        }
        error_reporting($current_error_reporting);
        return $image;
    }

}
