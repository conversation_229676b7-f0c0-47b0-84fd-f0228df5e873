<?php

namespace frontend\controllers;

use common\components\FreshdeskApiCom;
use common\components\GeneralComponent;
use common\models\CustomersInfoVerification;
use common\models\OrdersExtraInfo;
use frontend\components\Controller;
use common\components\LogFilesComponent;
use common\models\Customers;
use common\models\CustomersInfo;
use common\models\CustomersSetting;
use common\models\CustomersStaticInfo;
use common\components\EmailComponent;
use common\components\MerchantNotification;
use Yii;
use yii\db\Expression;
use yii\filters\AccessControl;

class VerifyEmailController extends Controller {

    /**
     * {@inheritdoc}
     */
    public function behaviors() {
        return array_merge(parent::behaviors(), [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['*', '@', '?'],
                    ],
                ],
            ],
        ]);
    }
    
    public function beforeAction($action) {
        if (isset($_SERVER['REQUEST_METHOD'])) {
            if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                $_POST = GeneralComponent::purify($_POST);
            } else if ($_SERVER['REQUEST_METHOD'] == 'GET') {
                $_GET = GeneralComponent::purify($_GET);
            }
        }

        return true;
    }


    public function actionChangeEmail() {
        $nextUrl='';
        $new_email = $_GET['email'];
        if ((isset($_GET['serialNumber']) && $_GET['serialNumber']) && (isset($_GET['email']) && $_GET['email'])) {
            $m_civ = CustomersInfoVerification::findOne(["customers_info_value" => $_GET['email'], "serial_number" => $_GET['serialNumber'], 'info_verification_type' => 'email']);
            if (isset($m_civ->customers_id)) {
                $sig_valid = ($_GET['signature'] == md5($_GET['action'].$_GET['email'].$_GET['serialNumber'].Yii::$app->params["SSO_CONFIG"]['SSO_SECRET_KEY'])) ? true : false;
                if ($sig_valid) {
                    $customers_id = $m_civ->customers_id;
                    $customer = Customers::find()->where(["customers_id"=>$customers_id])->one();
                    // update customers verification
                    $m_civ->serial_number = NULL;
                    $m_civ->info_verified = 1;
                    $m_civ->customers_info_verification_date = new Expression('NOW()');
                    $m_civ->save();

                    // Change User's Email
                    // get new data
                    $cust_attr = array(
                        "customers_email_address" => $new_email,
                    );

                    $cust_orig = Customers::find()->select(array_keys($cust_attr))->where(['customers_id' => $customer->customers_id])->one();
                    $cust_orig = array_intersect_key($cust_orig->attributes, $cust_attr);

                    //change email address mail
                    $mail_obj = new EmailComponent();
                    $mail_greet = Yii::t("dev", "EMAIL_GREETING", array("SYS_USERNAME" => $customer->customers_firstname));
                    $mail_obj->sendMail( $customer->customers_firstname, $_GET['email'], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SUBJECT_PREFIX"] . Yii::t('profile', 'EMAIL_PROFILE_CHANGE_EMAIL_SUBJECT'), $mail_greet . Yii::t('profile', 'EMAIL_PROFILE_CHANGE_EMAIL', [
                                'SYS_USER_PREVIOUS_MAIL' => $customer->customers_email_address,
                                'SYS_USER_NEW_MAIL' => $_GET['email'],
                                'SYS_DATE' => date('Y-m-d H:i'),
                                'SYS_EMAIL_SIGNATURE' => Yii::t("dev", "EMAIL_SIGNATURE")
                            ]), Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_NAME"], Yii::$app->params["SHASSO_MAIL_CONFIG"]["SENDER_EMAIL_ADDR"]);

                    // Actually update the email
                    Customers::model()->updateAll($cust_attr, ['customers_id' =>  $customer->customers_id]);
                    CustomersInfo::model()->updateLastModify();
                    Yii::$app->session->setFlash('success', Yii::t('verifyEmail', 'MSG_EMAIL_VERIFY_SUCCESS'));

                    // Log Changes
                    $_diff = GeneralComponent::arrayDiff($cust_orig, $cust_attr);
                    if ($_diff) {
                        $log_obj = new LogFilesComponent($customer->customers_id);
                        $cust_mod = $log_obj->detectChanges($_diff, $cust_attr);
                        $cust_log = $log_obj->constructLogMessage($cust_mod);
                    }
                    if (!empty($cust_log)) {
                        $log_obj = new LogFilesComponent($customer->customers_id);
                        $change_log = $log_obj->contructChangesString($cust_mod, "");
                        CustomersInfo::model()->updateChangeMade($change_log);

                        $change_str = "Changes made:\n";
                        for ($i = 0, $cnt = count($cust_log); $cnt > $i; $i++) {
                            if (is_array($cust_log[$i])) {
                                foreach ($cust_log[$i] as $field => $res) {
                                    if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                        $change_str .= $res['text'] . "\n";
                                    } else {
                                        $change_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                    }
                                }
                            }
                        }
                        $log_obj->insertCustomerHistoryLog($customer->customers_email_address, $change_str);
                        // Get the Counter some other way
                        $user_static_info = CustomersStaticInfo::getInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER,$customer->customers_id);
                        if($user_static_info){
                            CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER, (int)$user_static_info->value + 1);
                        }else{
                            CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER, 1);
                        }
                        $user_static_info = CustomersStaticInfo::getInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER_DAILY,$customer->customers_id);
                        if($user_static_info){
                            CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER_DAILY, (int)$user_static_info->value + 1);
                        }else{
                            CustomersStaticInfo::saveInfo(CustomersStaticInfo::KEY_EMAIL_CHANGES_COUNTER_DAILY, 1);
                        }

                    }

                    //check if user have fd_email_verification tag and remove it
                    $fd_flag = CustomersSetting::find()->where(["customers_id"=>$m_civ->customers_id,"customers_setting_key"=>FreshdeskApiCom::FD_EMAIL_VERIFICATION,"customers_setting_value"=>"1"])->one();
                    if($fd_flag){
                        //delete fd flag
                        $fd_flag->delete();
                    }
                }
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('verifyEmail', 'MSG_VERIFY_CODE_NOT_VALID'));
            }
        } else {
            Yii::$app->session->setFlash('danger', Yii::t('verifyEmail', 'MSG_VERIFY_CODE_NOT_VALID'));
        }

        Yii::$app->layout = 'page';
        return $this->render('index', ['nextUrl' => $nextUrl]);
    }

    public function actionIndex() {
        $nextUrl='';
        if ((isset($_GET['serialNumber']) && $_GET['serialNumber']) && (isset($_GET['email']) && $_GET['email'])) {
            $m_civ = CustomersInfoVerification::findOne(["customers_info_value" => $_GET['email'], "serial_number" => $_GET['serialNumber'], 'info_verified' => 0, 'info_verification_type' => 'email']);
            if (isset($m_civ->customers_id)) {
                $m_civ->serial_number = NULL;
                $m_civ->info_verified = 1;
                $m_civ->customers_info_verification_date = new Expression('NOW()');
                $m_civ->save();

                //check if user have fd_email_verification tag and remove it
                $fd_flag = CustomersSetting::find()->where(["customers_id"=>$m_civ->customers_id,"customers_setting_key"=>FreshdeskApiCom::FD_EMAIL_VERIFICATION,"customers_setting_value"=>"1"])->one();
                if($fd_flag){
                    //delete fd flag
                    $fd_flag->delete();
                }

                Yii::$app->session->setFlash('success', Yii::t('verifyEmail', 'MSG_EMAIL_VERIFY_SUCCESS'));

                MerchantNotification::notifyOnCustomerEmailVerifiedSuccessfully($m_civ->customers_id, $_GET['email']);

                if (isset($_GET['oid'])) {
                    $nextUrl = sprintf(Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'], $_GET['oid']);
                }
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('verifyEmail', 'MSG_VERIFY_CODE_NOT_VALID'));
            }
        } else {
            Yii::$app->session->setFlash('danger', Yii::t('verifyEmail', 'MSG_VERIFY_CODE_NOT_VALID'));
        }

        Yii::$app->layout = 'page';
        return $this->render('index', ['nextUrl' => $nextUrl]);
    }
}
