<?php

namespace frontend\controllers;

use common\components\GeneralComponent;
use common\components\StorePointsCom;
use common\models\Configuration;
use common\models\StorePoints;
use common\models\StorePointsRedeem;
use frontend\components\Controller;
use Yii;
use yii\web\Session;
use common\components\CurrenciesComponent;

class WorController extends Controller {

    public $layout = 'main-col2';
    public $menu = 'LINK_WOR';

    public $min_redemption;

    const SESSION_KEY_WOR_REDEEM_RESULT = '';

    public function init() {
        parent::init();
        $this->min_redemption = Configuration::model()->getConfigValue('OP_MIN_REDEMPTION');
    }

    public function beforeAction($action) {
        if (isset($_SERVER['REQUEST_METHOD'])) {
            if (isset($_POST) && !empty($_POST)) {
                $_POST = GeneralComponent::purify($_POST);
            }

            if (isset($_GET) && !empty($_GET)) {
                $_GET = GeneralComponent::purify($_GET);
            }
        }

        return parent::beforeAction($action);
    }

    public function actionIndex() {
        $user_id = Yii::$app->user->id;
        $storePoint = StorePoints::model()->getCustomersStorePoint(Yii::$app->user->id); //get sp_amount
        $currs = new CurrenciesComponent();
        $result = [];
        //list of currency
        foreach ($currs->currencies as $val => $curr) {
            $result['currencyList'][$val] = $curr['title'] . " ( " . html_entity_decode($curr['symbol_left'], ENT_NOQUOTES, 'UTF-8') . " " . html_entity_decode($curr['symbol_right'], ENT_NOQUOTES, 'UTF-8') . " ) ";
        }

        $sc_currency = StorePointsCom::getCustomerSCCurrency(\Yii::$app->user->id);

        //selcted option
        $sel_opt = '';
        if (isset($sc_currency)) {
            $sel_opt = $sc_currency;
        } else if (isset($_POST['reg_cur']) && isset($currs->currencies[$_POST['reg_cur']])){
            $sel_opt = $_POST['reg_cur'];
        } else {
            foreach ($result['currencyList'] as $val => $curr_code) {
                if (\Yii::$app->session['cur_code'] == $val) {
                    $sel_opt = \Yii::$app->session['cur_code'];
                }
            }
        }

        $redeem_curr = \common\models\StorePointsRedeem::find()->select('store_points_paid_currency')->where('user_id =' . Yii::$app->user->id)->one();

        $redeem_diff = ($this->min_redemption - $storePoint);

        if ($redeem_diff <= 0 && Yii::$app->request->isPost) {
            $r_spc = StorePointsCom::redeemStatus($storePoint, $sel_opt);
            if (!empty($r_spc["currency_code"])) {
                if ($r_spc["token_redeem"] > 0) {
                    $redemption_id = StorePointsCom::redeemToken($r_spc);
                    if ($redemption_id) {
                        \Yii::$app->session->set(static::SESSION_KEY_WOR_REDEEM_RESULT, array_merge($r_spc, [
                            'redeem_id' => 'RDN-' . $redemption_id,
                        ]));
                        return $this->redirect(['op/redeem-successful']);
                    }
                    return $this->redirect(['op/statement']);
                }
            } else {
                Yii::$app->session->setFlash('danger', Yii::t('wor', 'ERROR_INVALID_STORE_CREDIT_CURRENCY'));
            }
        }

        $country_curr = \common\models\Countries::find()
                        ->join('LEFT JOIN', 'currencies', 'countries.countries_currencies_id = currencies.currencies_id')
                        ->join('LEFT JOIN', 'customers_info', 'countries.countries_id =  customers_info.customer_info_selected_country')
                        ->where('customers_info.customers_info_id =' . Yii::$app->user->id)->one();
       //check either user have been redeem or not
        $f_data = array(
            'min_redeem' => $this->min_redemption,
            'min_redeem_display' => number_format($this->min_redemption, 0, '', ','),
            'token' => $storePoint,
            'token_display' => number_format($storePoint, 0, '', ','),
            'diff' => $redeem_diff,
            'diff_display' => number_format($redeem_diff, 0, '', ','),
            'store_credit' => (isset($r_spc['store_credit_redeem']) ? $r_spc['store_credit_redeem'] : 0),
            'store_credit_display' => (isset($r_spc['store_credit_redeem_display']) ? $r_spc['store_credit_redeem_display'] : 0),
            'curr_opt' => $result['currencyList'],
            'curr' => $sel_opt,
            'redeem_point' => $sc_currency,
        );

        return $this->render('index', $f_data);
    }

    public function actionStatement() {

        $dates = [];
        $now = time();
        $mth = date('n', $now);
        $yr = date('Y', $now);
        for($i = 0; $i < StorePointsCom::$stmt_filt_month; $i ++) {
            $time = mktime(0, 0, 0, $mth - $i, 1, $yr);
            $dates[$time] = $i == 0 ? \Yii::t('wor', 'LABEL_STATEMENT_THIS_MONTH') : \Yii::t('wor', 'LABEL_STATEMENT_DATE', [
                'SYS_DATE' => date('M Y', $time)
            ]);
        }
        reset($dates);
        $date = (isset($_GET['date']) && isset($dates[$_GET['date']])) ? $_GET['date'] : key($dates);
        $page = (isset($_GET['page']) ? (int) $_GET['page'] : 1);
        $filt = (isset($_POST['filt']) ? $_POST['filt'] : (isset($_GET["filt"]) ? $_GET['filt'] : ""));
        $result = StorePointsCom::statement($page, Yii::$app->params['GENERAL_CONFIG']['PAGER_ITEM_PER_PAGE'], $filt, $date);

        $storePoint = StorePoints::model()->getCustomersStorePoint(Yii::$app->user->id);

        $f_data = array(
            'f_data' => $result['result'],
            'token_display' => number_format($storePoint, 0, '', ','),
            "dates" => $dates,
            "date" => $date,
            'pagination' => array(
                'current' => $page,
                'record' => count($result['result']),
                'total' => $result['total'],
                'query' =>array('date' => $date)
            )
        );

        $this->menu = 'LINK_WOR_STATEMENT';
        return $this->render('statement', $f_data);
    }

    public function actionHistory() {

        $page = (isset($_GET['page']) ? (int) $_GET['page'] : 1);
        $filt = (isset($_POST['filt']) ? $_POST['filt'] : (isset($_GET["filt"]) ? $_GET['filt'] : ""));
        $result = StorePointsCom::history($page, Yii::$app->params['GENERAL_CONFIG']['PAGER_ITEM_PER_PAGE'], $filt);

        $storePoint = StorePoints::model()->getCustomersStorePoint(Yii::$app->user->id);

        $f_data = array(
            'f_data' => $result['result'],
            'token_display' => number_format($storePoint, 0, '', ','),
            'pagination' => array(
                'current' => $page,
                'record' => count($result['result']),
                'total' => $result['total'],
                'query' =>array('filt' => $filt)
            )
        );

        $this->menu = 'LINK_WOR_HISTORY';
        return $this->render('history', $f_data);
    }

    public function actionRedeemInto() {
        $result_redeem = array();

        if (isset($_POST['reg_cur']) && !empty($_POST['reg_cur'])) {
            $curr_obj = new CurrenciesComponent();
            foreach ($curr_obj->currencies as $val => $curr) {
                $result['currencyList'][$val] = $curr['title'] . " ( " . html_entity_decode($curr['symbol_left'], ENT_NOQUOTES, 'UTF-8') . " " . html_entity_decode($curr['symbol_right'], ENT_NOQUOTES, 'UTF-8') . " ) ";
            }

            if (isset($result['currencyList'][$_POST['reg_cur']])) {
                $curr_code = $_POST['reg_cur'];
                //current store point
                $storePoint = StorePoints::model()->getCustomersStorePoint(Yii::$app->user->id); //get sp_amount

                $redeem_diff = ($this->min_redemption - $storePoint);
                $redeem_amount = 0;

                if ($redeem_diff <= 0) {
                    $r_spc = StorePointsCom::redeemStatus($storePoint, $curr_code);
                    if (!empty($r_spc["currency_code"])) {
                        $redeem_amount = $r_spc['store_credit_redeem_display'];
                    } else {
                        Yii::$app->session->setFlash('danger', Yii::t('wor', 'ERROR_INVALID_STORE_CREDIT_CURRENCY'));
                    }
                } else {
                    $redeem_amount = $curr_obj->format(0, false, $curr_code);
                }

                $result_redeem = array(
                    "amount" => $redeem_amount,
                );
            }
        }
        echo json_encode(array(
            'result' => $result_redeem
        ));
    }

    public function actionRedeemSuccessful() {
        $redeem_result = \Yii::$app->session->get(static::SESSION_KEY_WOR_REDEEM_RESULT, []);
        \Yii::$app->session->remove(static::SESSION_KEY_WOR_REDEEM_RESULT);
        if (empty($redeem_result)) {
            return $this->redirect(['op/index']);
        }

        return $this->render('redeemSuccess', $redeem_result);
    }
}
