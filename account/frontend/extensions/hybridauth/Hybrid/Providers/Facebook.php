<?php

/* !
 * HybridAuth
 * http://hybridauth.sourceforge.net | http://github.com/hybridauth/hybridauth
 * (c) 2009-2012, HybridAuth authors | http://hybridauth.sourceforge.net/licenses.html 
 */

/**
 * Hybrid_Providers_Facebook provider adapter based on OAuth2 protocol
 * 
 * Hybrid_Providers_Facebook use the Facebook PHP SDK created by Facebook
 * 
 * http://hybridauth.sourceforge.net/userguide/IDProvider_info_Facebook.html
 */
require_once Hybrid_Auth::$config["path_libraries"] . "Facebook/autoload.php";

use Facebook\Facebook;
use Facebook\Exceptions\FacebookSDKException;

class Hybrid_Providers_Facebook extends Hybrid_Provider_Model {
    
    /* Permissions copied from old FacebookPermissions class */
    
    // Basic permissions
    const PUBLIC_PROFILE = 'public_profile';
    const USER_FRIENDS = 'user_friends';
    const EMAIL = 'email';
    // Extended Profile Properties
    const USER_ABOUT_ME = 'user_about_me';
    const USER_ACTIONS_BOOKS = 'user_actions.books';
    const USER_ACTIONS_FITNESS = 'user_actions.fitness';
    const USER_ACTIONS_MUSIC = 'user_actions.music';
    const USER_ACTIONS_NEWS = 'user_actions.news';
    const USER_ACTIONS_VIDEO = 'user_actions.video';
    const USER_ACTIVITIES = 'user_activities';
    const USER_BIRTHDAY = 'user_birthday';
    const USER_EDUCATION_HISTORY = 'user_education_history';
    const USER_EVENTS = 'user_events';
    const USER_GAMES_ACTIVITY = 'user_games_activity';
    const USER_GROUPS = 'user_groups';
    const USER_HOMETOWN = 'user_hometown';
    const USER_INTERESTS = 'user_interests';
    const USER_LIKES = 'user_likes';
    const USER_LOCATION = 'user_location';
    const USER_PHOTOS = 'user_photos';
    const USER_POSTS = 'user_posts';
    const USER_RELATIONSHIPS = 'user_relationships';
    const USER_RELATIONSHIP_DETAILS = 'user_relationship_details';
    const USER_RELIGION_POLITICS = 'user_religion_politics';
    const USER_STATUS = 'user_status';
    const USER_TAGGED_PLACES = 'user_tagged_places';
    const USER_VIDEOS = 'user_videos';
    const USER_WEBSITE = 'user_website';
    const USER_WORK_HISTORY = 'user_work_history';
    // Extended Permissions
    const READ_FRIENDLISTS = 'read_friendlists';
    const READ_INSIGHTS = 'read_insights';
    const READ_MAILBOX = 'read_mailbox';
    const READ_PAGE_MAILBOXES = 'read_page_mailboxes';
    const READ_STREAM = 'read_stream';
    const MANAGE_NOTIFICATIONS = 'manage_notifications';
    const MANAGE_PAGES = 'manage_pages';
    const PUBLISH_PAGES = 'publish_pages';
    const PUBLISH_ACTIONS = 'publish_actions';
    const RSVP_EVENT = 'rsvp_event';
    
    // default permissions, and alot of them. You can change them from the configuration by setting the scope to what you want/need
    public $scopeList = array(
        self::PUBLIC_PROFILE,
        self::EMAIL,
        self::USER_ABOUT_ME,
        self::USER_BIRTHDAY,
        self::USER_HOMETOWN,
        self::USER_WEBSITE,
        self::READ_STREAM,
        self::PUBLISH_ACTIONS,
        self::USER_FRIENDS,
    );
    
    /** @var \Facebook\Facebook */
    protected $facebook = null;

    /**
     * IDp wrappers initializer 
     */
    function initialize() {
        if (!$this->config["keys"]["id"] || !$this->config["keys"]["secret"]) {
            throw new Exception("Your application id and secret are required in order to connect to {$this->providerId}.", 4);
        }
        $facebook_curl = new FacebookCurl();
        if (isset(Hybrid_Auth::$config["proxy"])) {
            list($proxy_host, $proxy_port) = explode(':', Hybrid_Auth::$config["proxy"]);
            $facebook_curl->setProxy($proxy_host, $proxy_port);
        }
        
        $this->facebook = new Facebook([
            'app_id' => $this->config["keys"]["id"],
            'app_secret' => $this->config["keys"]["secret"],
            'default_graph_version' => 'v2.8',
            'http_client_handler' => new \Facebook\HttpClients\FacebookCurlHttpClient($facebook_curl),
        ]);
        
        if (isset($this->config['redirect_uri']) && !empty($this->config['redirect_uri'])) {
            $this->endpoint = $this->config['redirect_uri'];
        }
        if (isset($this->config['scope']) && !empty($this->config['scope'])) {
            $this->scope = array();
            foreach ($this->config['scope'] as $scope) {
                if (in_array($scope, $this->scopeList)) {
                    $this->scope[] = $scope;
                }
            }
        } else {
            $this->scope = $this->scopeList;
        }
    }

    /**
     * begin login step
     * 
     * simply call Facebook::require_login(). 
     */
    function loginBegin() {
        // redirect to facebook

        $helper = $this->facebook->getRedirectLoginHelper();
        $url = $helper->getLoginUrl($this->endpoint, $this->scope);
        Hybrid_Auth::redirect($url);
    }

    /**
     * finish login step 
     */
    function loginFinish() {
        $helper = $this->facebook->getRedirectLoginHelper();
        try {
            $accessToken = $helper->getAccessToken($this->endpoint);
            if ($accessToken) {
                $_SESSION['__FB_ACCESS_TOKEN'] = (string) $accessToken;
            }
        } catch (FacebookSDKException $ex) {
            unset($_SESSION['__FB_ACCESS_TOKEN']);
            throw new Exception("Authentication failed! {$this->providerId} returned an invalid user id.", 5);
        } catch (\Exception $ex) {
            unset($_SESSION['__FB_ACCESS_TOKEN']);
            throw new Exception("Authentication failed! {$this->providerId} returned an invalid user id.", 5);
        }
        $this->setUserConnected();
    }

    /**
     * logout
     */
    function logout() {
        unset($_SESSION['__FB_ACCESS_TOKEN']);
        parent::logout();
    }

    /**
     * load the user profile from the IDp api client
     */
    function getUserProfile() {
        if (!array_key_exists('__FB_ACCESS_TOKEN', $_SESSION)) {
            throw new Exception("User profile request failed! {$this->providerId} returned an invalid response.", 6);
        }
        $accessToken = $_SESSION['__FB_ACCESS_TOKEN'];
        try {
            $this->facebook->setDefaultAccessToken($accessToken);
            $data = $this->facebook->get('/me?fields=name,email,first_name,last_name,link,website,gender,hometown,about')->getDecodedBody();
        } catch (FacebookSDKException $e) {
            throw new Exception("User profile request failed! {$this->providerId} api returned an error: $e");
        }

        // if the provider identifier is not recived, we assume the auth has failed
        if (!isset($data["id"])) {
            throw new Exception("User profile request failed! {$this->providerId} api returned an invalid response.", 6);
        }
        
        # store the user profile.
        $this->user->profile->identifier = (array_key_exists('id', $data)) ? $data['id'] : "";
        $this->user->profile->displayName = (array_key_exists('name', $data)) ? $data['name'] : "";
        $this->user->profile->firstName = (array_key_exists('first_name', $data)) ? $data['first_name'] : "";
        $this->user->profile->lastName = (array_key_exists('last_name', $data)) ? $data['last_name'] : "";
        $this->user->profile->photoURL = "https://graph.facebook.com/" . $this->user->profile->identifier . "/picture?width=150&height=150";
        $this->user->profile->profileURL = (array_key_exists('link', $data)) ? $data['link'] : "";
        $this->user->profile->webSiteURL = (array_key_exists('website', $data)) ? $data['website'] : "";
        $this->user->profile->gender = (array_key_exists('gender', $data)) ? $data['gender'] : "";
        $this->user->profile->description = (array_key_exists('about', $data)) ? $data['about'] : "";
        $this->user->profile->email = (array_key_exists('email', $data)) ? $data['email'] : "";
        $this->user->profile->emailVerified = (array_key_exists('email', $data)) ? $data['email'] : "";
        $this->user->profile->region = (array_key_exists("hometown", $data)) && isset($data['hometown']['name']) ? $data['hometown']['name'] : "";

        if (array_key_exists('birthday', $data)) {
            list($birthday_month, $birthday_day, $birthday_year) = explode("/", $data['birthday']);

            $this->user->profile->birthDay = (int) $birthday_day;
            $this->user->profile->birthMonth = (int) $birthday_month;
            $this->user->profile->birthYear = (int) $birthday_year;
        }
        
        return $this->user->profile;
    }

    /**
     * load the user contacts
     */
    function getUserContacts() {
        if (!array_key_exists('__FB_ACCESS_TOKEN', $_SESSION)) {
            throw new Exception("User contacts request failed! {$this->providerId} returned an invalid response.");
        }
        if (!in_array(static::USER_FRIENDS, $this->scope)) {
            return array();
        }
        $accessToken = $_SESSION['__FB_ACCESS_TOKEN'];
        try {
            $this->facebook->setDefaultAccessToken($accessToken);
            $response = $this->facebook->get('/me/friends')->getDecodedBody();
        } catch (FacebookSDKException $e) {
            throw new Exception("User contacts request failed! {$this->providerId} returned an error: $e");
        }

        if (!$response || !count($response["data"])) {
            return ARRAY();
        }

        $contacts = ARRAY();

        foreach ($response["data"] as $item) {
            $uc = new Hybrid_User_Contact();

            $uc->identifier = (array_key_exists("id", $item)) ? $item["id"] : "";
            $uc->displayName = (array_key_exists("name", $item)) ? $item["name"] : "";
            $uc->profileURL = "https://www.facebook.com/profile.php?id=" . $uc->identifier;
            $uc->photoURL = "https://graph.facebook.com/" . $uc->identifier . "/picture?width=150&height=150";

            $contacts[] = $uc;
        }

        return $contacts;
    }

    /**
     * update user status
     */
    function setUserStatus($status) {
        if (!array_key_exists('__FB_ACCESS_TOKEN', $_SESSION)) {
            throw new Exception("Update user status failed! {$this->providerId} returned an invalid response.");
        }
        if (!in_array(static::PUBLISH_ACTIONS, $this->scope)) {
            return;
        }
        
        $parameters = array();

        if (is_array($status)) {
            $parameters = $status;
        } else {
            $parameters["message"] = $status;
        }
        
        $accessToken = $_SESSION['__FB_ACCESS_TOKEN'];
        try {
            $this->facebook->setDefaultAccessToken($accessToken);
            $this->facebook->post('/me/feed', $parameters);
        } catch (FacebookSDKException $e) {
            throw new Exception("Update user status failed! {$this->providerId} returned an error: $e");
        }
    }

    /**
     * load the user latest activity  
     *    - timeline : all the stream
     *    - me       : the user activity only  
     */
    function getUserActivity($stream) {
        if (!array_key_exists('__FB_ACCESS_TOKEN', $_SESSION)) {
            throw new Exception("User activity stream request failed! {$this->providerId} returned an invalid response.");
        }
        if (!in_array(static::READ_STREAM, $this->scope)) {
            return array();
        }
        $accessToken = $_SESSION['__FB_ACCESS_TOKEN'];
        try {
            $this->facebook->setDefaultAccessToken($accessToken);
            $response = $this->facebook->get('/me/' . ($stream == 'me' ? 'feed' : 'home'))->getDecodedBody();
        } catch (FacebookSDKException $e) {
            throw new Exception("User activity stream request failed! {$this->providerId} returned an error: $e");
        }

        if (!$response || !count($response['data'])) {
            return ARRAY();
        }

        $activities = ARRAY();

        foreach ($response['data'] as $item) {
            $ua = new Hybrid_User_Activity();

            $ua->id = (array_key_exists("id", $item)) ? $item["id"] : "";
            $ua->date = (array_key_exists("created_time", $item)) ? strtotime($item["created_time"]) : "";

            if ($item["type"] == "video") {
                $ua->text = (array_key_exists("link", $item)) ? $item["link"] : "";
            }

            if ($item["type"] == "link") {
                $ua->text = (array_key_exists("link", $item)) ? $item["link"] : "";
            }

            if (empty($ua->text) && isset($item["story"])) {
                $ua->text = (array_key_exists("link", $item)) ? $item["link"] : "";
            }

            if (empty($ua->text) && isset($item["message"])) {
                $ua->text = (array_key_exists("message", $item)) ? $item["message"] : "";
            }

            if (!empty($ua->text)) {
                $ua->user->identifier = (array_key_exists("id", $item["from"])) ? $item["from"]["id"] : "";
                $ua->user->displayName = (array_key_exists("name", $item["from"])) ? $item["from"]["name"] : "";
                $ua->user->profileURL = "https://www.facebook.com/profile.php?id=" . $ua->user->identifier;
                $ua->user->photoURL = "https://graph.facebook.com/" . $ua->user->identifier . "/picture?type=square";

                $activities[] = $ua;
            }
        }

        return $activities;
    }
    
}

class FacebookCurl extends \Facebook\HttpClients\FacebookCurl {
    
    protected $proxy_host;
    protected $proxy_port;
    
    public function init() {
        parent::init();
        $this->setopt(CURLOPT_PROXY, $this->proxy_host);
        $this->setopt(CURLOPT_PROXYPORT, $this->proxy_port);
    }
    
    public function setProxy($proxy_host, $proxy_port) {
        $this->proxy_host = $proxy_host;
        $this->proxy_port = $proxy_port;
    }
}