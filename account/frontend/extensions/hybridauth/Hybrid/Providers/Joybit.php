<?php

class Hybrid_Providers_Joybit extends Hybrid_Provider_Model {

    public function initialize() {
        if (!$this->config["keys"]["id"] || !$this->config["keys"]["secret"]) {
            $this->_unsetSession();

            throw new Exception("Your application key and secret are required in order to connect to {$this->providerId}.", 4);
        }

        if (!$this->config["provider_timeout"]) {
            $this->_unsetSession();

            throw new Exception("Your application mandatory configuration are required in order to connect to {$this->providerId}.", 4);
        }
    }

    public function getUserProfile() {
        if (isset($_SESSION[$this->providerId])) {
            $profile = json_decode($_SESSION[$this->providerId], true);

            // if user trigger Joybit again
            if (isset($this->params["hauth_return_to"])) {
                $url = parse_url($this->params["hauth_return_to"]);
                $req_uri = $url['query'];
                parse_str($req_uri, $req_arr);
                
                if (isset($req_arr["token"]) && ($profile["token"] != $req_arr["token"])) {
                    $this->loginBegin();
                }
            }

            if (isset($_SESSION[$this->providerId . ".ha_timeout"]) && (time() > $_SESSION[$this->providerId . ".ha_timeout"])) {
                $this->_unsetProfile();
                $this->_unsetSession();

                throw new Exception("{$this->providerId} Connection Timeout! Please try again.", 6);
            } else {
                $this->user->profile->identifier = $profile["user_id"];
                
                return $this->user->profile;
            }
        } else {
            $this->_unsetProfile();
            $this->_unsetSession();

            throw new Exception("User profile request failed! {$this->providerId} returned an invalid response.", 6);
        }
    }

    public function loginBegin() {
        $this->_unsetProfile();

        if (isset($this->params["hauth_return_to"])) {
            $url = parse_url($this->params["hauth_return_to"]);
            $req_uri = $url['query'];
            parse_str($req_uri, $req_arr);
            Hybrid_Auth::storage()->delete("hauth_session.{$this->providerId}.id_provider_params");

            if (isset($req_arr["request_time"]) && (($req_arr["request_time"] + $this->config["provider_timeout"]) > time())) {
                if (isset($req_arr["token"]) && isset($req_arr["user_id"]) && isset($req_arr["request_time"]) && isset($req_arr["token"])) {
                    $token = md5($this->config["keys"]["id"] . $req_arr["request_time"] . $req_arr["user_id"] . $this->config["keys"]["secret"]);
                    if ($req_arr["token"] == $token) {
                        // valid connection
                        $_SESSION[$this->providerId] = json_encode(array(
                            "user_id" => $req_arr["user_id"],
                            "token" => $req_arr["token"]
                        ));

                        if ($this->config["request_timeout"]) {
                            $_SESSION[$this->providerId . ".ha_timeout"] = time() + $this->config["request_timeout"];
                        }

                        $req_arr = $this->_removeSecret($req_arr);
                        $return_url = substr($this->params["hauth_return_to"], 0, strpos($this->params["hauth_return_to"], '?')) . "?" . http_build_query($req_arr);
                        Hybrid_Auth::storage()->set("hauth_session.{$this->providerId}.hauth_return_to", $return_url);

                        Hybrid_Auth::redirect($this->endpoint);
                        exit();
                    }
                }
            }
        }

        Hybrid_Auth::storage()->set("hauth_session.{$this->providerId}.error.status", 1);
        Hybrid_Auth::storage()->set("hauth_session.{$this->providerId}.error.message", "Authentication failed! {$this->providerId} returned an invalid user id.");

        $this->_unsetSession();
        Hybrid_Auth::storage()->delete("hauth_session.{$this->providerId}.hauth_return_to");
        Hybrid_Auth::storage()->delete("hauth_session.{$this->providerId}.hauth_endpoint");
        Hybrid_Auth::storage()->delete("hauth_session.{$this->providerId}.id_provider_params");

        throw new Exception("Authentication failed! {$this->providerId} returned an invalid user id.", 5);
    }

    public function loginFinish() {
        $this->setUserConnected();
    }

    public function logout() {
        $this->_unsetProfile();
        $this->_unsetSession();
    }

    public function setUserUnconnected() {
        Hybrid_Auth::storage()->set("hauth_session.{$this->providerId}.is_logged_in", 0);
    }

    private function _unsetProfile() {
        $this->setUserUnconnected();
    }

    private function _unsetSession() {
        if (isset($_SESSION[$this->providerId])) {
            unset($_SESSION[$this->providerId]);
        }
        if (isset($_SESSION[$this->providerId . ".ha_timeout"])) {
            unset($_SESSION[$this->providerId . ".ha_timeout"]);
        }
    }

    private function _removeSecret($params) {
        $secret = array(
            "client_id",
            "user_id",
            "request_time",
            "token"
        );

        foreach ($secret as $key) {
            if (isset($params[$key])) {
                unset($params[$key]);
            }
        }
        return $params;
    }

}

