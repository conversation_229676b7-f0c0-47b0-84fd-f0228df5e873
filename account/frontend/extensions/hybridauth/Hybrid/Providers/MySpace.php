<?php
/*!
* HybridAuth
* http://hybridauth.sourceforge.net | http://github.com/hybridauth/hybridauth
* (c) 2009-2012, HybridAuth authors | http://hybridauth.sourceforge.net/licenses.html 
*/

/**
 * Hybrid_Providers_MySpace provider adapter based on OAuth1 protocol
 * 
 * http://hybridauth.sourceforge.net/userguide/IDProvider_info_MySpace.html
 */
class Hybrid_Providers_MySpace extends Hybrid_Provider_Model_OAuth1
{
	/**
	* IDp wrappers initializer 
	*/
	function initialize() 
	{
		parent::initialize();

		// Provider api end-points
		$this->api->api_endpoint_url  = "http://api.myspace.com/v1/";
		$this->api->authorize_url     = "http://api.myspace.com/authorize";
		$this->api->request_token_url = "http://api.myspace.com/request_token";
		$this->api->access_token_url  = "http://api.myspace.com/access_token";
	}

	/**
	* get the connected uid from myspace api
	*/
	public function getCurrentUserId()
	{
		$response = $this->api->get( 'http://api.myspace.com/v1/user.json' );

		if ( ! isset( $response->userId ) ){
			throw new Exception( "User id request failed! {$this->providerId} returned an invalid response." );
		}

		return $response->userId;
	}

	/**
	* load the user profile from the IDp api client
	*/
	function getUserProfile()
	{
		$userId = $this->getCurrentUserId();

		$data = $this->api->get( 'http://api.myspace.com/v1/users/' . $userId . '/profile.json' );

		if ( ! is_object( $data ) ){
			throw new Exception( "User profile request failed! {$this->providerId} returned an invalid response.", 6 );
		}

		$this->user->profile->identifier  = $userId;
		$this->user->profile->displayName = isset($data->basicprofile->name) ? $data->basicprofile->name : '';
		$this->user->profile->description = isset($data->aboutme) ? $data->aboutme : '';
		$this->user->profile->gender      = isset($data->basicprofile->gender) ? $data->basicprofile->gender : '';
		$this->user->profile->photoURL    = isset($data->basicprofile->image) ? $data->basicprofile->image : '';
		$this->user->profile->profileURL  = isset($data->basicprofile->webUri) ? $data->basicprofile->webUri : '';
		$this->user->profile->age         = isset($data->age) ? $data->age : '';
		$this->user->profile->country     = isset($data->country) ? $data->country : '';
		$this->user->profile->region      = isset($data->region) ? $data->region : '';
		$this->user->profile->city        = isset($data->city) ? $data->city : '';
		$this->user->profile->zip         = isset($data->postalcode) ? $data->postalcode : '';

		return $this->user->profile;
	}

	/**
	* load the user contacts
	*/
	function getUserContacts()
	{
		$userId = $this->getCurrentUserId();

		$response = $this->api->get( "http://api.myspace.com/v1/users/" . $userId . "/friends.json" );

		if ( ! is_object( $response ) ){
			throw new Exception( "User profile request failed! {$this->providerId} returned an invalid response.", 6 );
		}

		$contacts = ARRAY();

		foreach( $response->Friends as $item ){ 
			$uc = new Hybrid_User_Contact();

			$uc->identifier   = isset($item->userId) ? $item->userId : 0;
			$uc->displayName  = isset($item->name) ? $item->name : '';
			$uc->profileURL   = isset($item->webUri) ? $item->webUri : '';
			$uc->photoURL     = isset($item->image) ? $item->image : '';
			$uc->description  = isset($item->status) ? $item->status : '';

			$contacts[] = $uc;
		}

		return $contacts;
 	}

	/**
	* update user status
	*/
	function setUserStatus( $status )
	{
	// crappy myspace... gonna see this asaic
		$userId = $this->getCurrentUserId();
		
		$parameters = array( 'status' => $status );

		$response = $this->api->api( "http://api.myspace.com/v1/users/" . $userId . "/status", 'PUT', $parameters ); 

		// check the last HTTP status code returned
		if ( $this->api->http_code != 200 )
		{
			throw new Exception( "Update user status failed! {$this->providerId} returned an error. " . $this->errorMessageByStatus( $this->api->http_code ) );
		}
 	}

	/**
	* load the user latest activity  
	*    - timeline : all the stream
	*    - me       : the user activity only  
	*/
	function getUserActivity( $stream )
	{
		$userId = $this->getCurrentUserId();

		if( $stream == "me" ){
			$response = $this->api->get( "http://api.myspace.com/v1/users/" . $userId . "/status.json" ); 
		}                                                          
		else{                                                      
			$response = $this->api->get( "http://api.myspace.com/v1/users/" . $userId . "/friends/status.json" );
		}

		if ( ! is_object( $response ) ){
			throw new Exception( "User profile request failed! {$this->providerId} returned an invalid response.", 6 );
		}

		$activities = ARRAY();

		if( $stream == "me" ){
			// todo
		}
		else{                                                      
			foreach( $response->FriendsStatus as $item ){
				$ua = new Hybrid_User_Activity();

				$ua->id                 = isset($item->statusId) ? $item->statusId : 0;
				$ua->date               = NULL; // to find out!!
				$ua->text               = isset($item->status) ? $item->status : '';

				$ua->user->identifier   = isset($item->user->userId) ? $item->user->userId : 0;
				$ua->user->displayName  = isset($item->user->name) ? $item->user->name : '';
				$ua->user->profileURL   = isset($item->user->uri) ? $item->user->uri : '';
				$ua->user->photoURL     = isset($item->user->image) ? $item->user->image : '';

				$activities[] = $ua;
			}
		} 

		return $activities;
 	}
}
