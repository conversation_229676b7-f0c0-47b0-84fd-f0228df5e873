<?php
/*!
* HybridAuth
* http://hybridauth.sourceforge.net | http://github.com/hybridauth/hybridauth
* (c) 2009-2012, HybridAuth authors | http://hybridauth.sourceforge.net/licenses.html 
*/

/**
 * Hybrid_Providers_OpenID provider adapter for any idp openid based 
 * 
 * http://hybridauth.sourceforge.net/userguide/IDProvider_info_OpenID.html
 */
class Hybrid_Providers_OpenID extends Hybrid_Provider_Model_OpenID
{
}
