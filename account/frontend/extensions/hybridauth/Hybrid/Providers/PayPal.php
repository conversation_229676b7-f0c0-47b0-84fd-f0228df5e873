<?php


use common\components\ArrayHelper;
use common\components\CurlComponent;

class Hybrid_Providers_PayPal extends Hybrid_Provider_Model
{
    public $login_url;
    public $accesstoken_api_url;
    public $client_id;
    public $secret;

    /**
     * IDp wrappers initializer
     */
    function initialize()
    {
        $this->login_url = $this->config["login_url"];
        $this->accesstoken_api_url = $this->config["accesstoken_api_url"];
        $this->userinfo_api_url = $this->config["userinfo_api_url"];
        $this->client_id = $this->config["keys"]["id"];
        $this->secret = $this->config["keys"]["secret"];
    }

    function loginBegin()
    {
        $redirectUri = ArrayHelper::getValue($this->config, 'redirect_uri');
        $login_url = $this->generateLoginLink(null, $redirectUri);
        Hybrid_Auth::redirect($login_url);
    }

    function loginFinish()
    {
        if (!isset($_GET['code'])) {
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal loginBegin failed, no code found in GET parameter: " . \Yii::$app->user->id . "\n\n" . $_GET, []);
            throw new Exception("LoginWithPaypal loginBegin failed, {$this->providerId} no code found in GET parameter.", 1);
            return;
        }
        try {
            $userinfo = $this->getUserDetailsFromCode($_GET['code']);
            if (!isset($userinfo)) {
                \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal loginBegin failed, no userinfo found from code info: " . \Yii::$app->user->id . "\n\n" . $userinfo, []);
                throw new Exception("LoginWithPaypal loginBegin failed, {$this->providerId} no userinfo found from code info.", 1);
                return;
            }
            $_SESSION['__PAYPAL_USER_INFO'] = $userinfo;
            $this->setUserConnected();
        } catch (\Exception $ex) {
            unset($_SESSION['code']);
            throw new Exception($ex->getMessage(), 5);
        }
    }

    /**
     * logout
     */
    function logout()
    {
        unset($_SESSION['__PAYPAL_USER_INFO']);
        parent::logout();
    }

    function getUserProfile()
    {
        if (!isset($_SESSION['__PAYPAL_USER_INFO'])) {
            throw new Exception("User profile request failed! {$this->providerId} returned an invalid response.", 6);
        }
        $data = $_SESSION['__PAYPAL_USER_INFO'];

        $this->user->profile->identifier = (isset($data['payer_id'])) ? $data['payer_id'] : "";
        $this->user->profile->displayName = (isset($data['name'])) ? $data['name'] : "";
        $split_name = $this->split_name($data['name']);
        $this->user->profile->firstName = (isset($split_name[0])) ? $split_name[0] : "";
        $this->user->profile->lastName = (isset($split_name[1])) ? $split_name[1] : "";
        if (isset($data["emails"])) {
            foreach ($data["emails"] as $email) {
                if ($email["primary"] == true) {
                    $this->user->profile->email = $email["value"];
                }
            }
        }
        return $this->user->profile;
    }

    function getUserContacts()
    {
    }

    #####################
    public function generateLoginLink($scope = null, $redirect_url = null)
    {
        $scope = $scope ?: "profile email https://uri.paypal.com/services/paypalattributes";
        $redirect_url = $redirect_url ?: $this->endpoint;

        return sprintf($this->login_url, $this->client_id, $scope, $redirect_url);
    }

    public function getUserDetailsFromCode($code)
    {
        $access_token = $this->getAccessTokenFromCode($code);
        if (!$access_token) {
            return null;
        }
        return $this->getUserInfo($access_token);
    }

    protected function getAccessTokenFromCode($code)
    {
        $url = $this->accesstoken_api_url;
        $post_fields = [
            'grant_type' => 'authorization_code',
            'code' => $code,
        ];

        $curlObj = new CurlComponent();
        $curlObj->request_headers = [
            'Authorization' => 'Basic ' . base64_encode("$this->client_id:$this->secret"),
        ];
        $response_raw = $curlObj->curlPost($url, $post_fields);

        $response = json_decode($response_raw, true);

        if (!$response) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getAccessTokenFromCode failed, unrecognized response: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else if (isset($response['error']) && $response['error']) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getAccessTokenFromCode failed with error: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else if (!isset($response['access_token'])) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getAccessTokenFromCode failed, no access_token found: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else {
            return $response['access_token'];
        }
    }

    protected function getUserInfo($access_token)
    {
        $url = $this->userinfo_api_url;
        $headers = [
            'Authorization' => 'Bearer ' . $access_token,
            'Content-Type' => 'application/json',
        ];
        $curlObj = new CurlComponent();
        $curlObj->request_headers = $headers;
        $response_raw = $curlObj->curlGet($url, ['schema' => 'paypalv1.1']);
        $response = json_decode($response_raw, true);
        if (!$response) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getUserInfo failed, unrecognized response: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else if (isset($response['error'])) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getUserInfo failed with error: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else if (empty($response['name'])) {
            unset($_SESSION['code']);
            \Yii::$app->reporter->reportToAdminViaSlack("LoginWithPaypal getUserInfo failed, no name found: " . \Yii::$app->user->id . "\n\n" . $response_raw, []);
            return null;
        } else {
            return $response;
        }
    }

    function split_name($name)
    {
        $name = trim($name);
        $last_name = (strpos($name, ' ') === false) ? '' : preg_replace('#.*\s([\w-]*)$#', '$1', $name);
        $first_name = trim(preg_replace('#' . preg_quote($last_name, '#') . '#', '', $name));
        return array($first_name, $last_name);
    }
}
