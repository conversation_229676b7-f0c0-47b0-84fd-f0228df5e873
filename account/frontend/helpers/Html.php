<?php

namespace frontend\helpers;

class Html extends \yii\helpers\Html {
    
    public static function activeTokenInput($model, $attribute, $options = array()) {
        $name = isset($options['name']) ? $options['name'] : static::getInputName($model, $attribute);
        $value = Html::getAttributeValue($model, $attribute);
        $html_options = \yii\helpers\ArrayHelper::getValue($options, 'htmlOptions', []);
        if (!array_key_exists('id', $html_options)) {
            $html_options['id'] = static::getInputId($model, $attribute);
        }
        return static::tokenInput($name, $value, $html_options);
    }
    
    public static function tokenInput($name, $value, $options = array()) {
        $id = \yii\helpers\ArrayHelper::remove($options, 'id');
        $length = \yii\helpers\ArrayHelper::remove($options, 'length', 6);
        $type = \yii\helpers\ArrayHelper::remove($options, 'type', 'div');
        $input_wrapper_options = \yii\helpers\ArrayHelper::remove($options, 'inputWrapperOptions', []);
        $input_options = \yii\helpers\ArrayHelper::remove($options, 'inputOptions', []);
        
        $options = array_merge([
            'class' => 'input-row-token',
            'data' => [
                'token-input' => $length,
            ],
        ], $options);
        $input_wrapper_options = array_merge([
            'type' => 'div',
            'class' => "input-row-$length-coll",
        ], $input_wrapper_options);
        $input_options = array_merge([
            'type'=> 'tel',
            'class' => 'token-single',
            'data-token-field' => '',
        ], $input_options);
        
        $html = "<$type" . static::renderTagAttributes($options) . '>';
        $val_split = str_split(substr($value, 0, $length));
        if (!isset($options['class'])) {
            $options['class'] = 'input-row-token';
        }
        for($i = 0; $i < $length; $i ++) {
            $val = isset($val_split[$i]) ? $val_split[$i] : '';
            $wrapper_type = \yii\helpers\ArrayHelper::remove($input_wrapper_options, 'type', 'div');
            $html .= "<$wrapper_type" . static::renderTagAttributes($input_wrapper_options) . '>';
            $html .= "<input" . static::renderTagAttributes(array_merge($input_options, ['value' => $val, 'maxlength' => $length - $i, 'autocomplete' => 'nope'])) . '>';
            $html .= "</$wrapper_type>";
        }
        $html .= static::hiddenInput($name, $value, [
            'id' => $id,
        ]);
        $html .= "</$type>";
        
        return $html;
    }
}