@import url("https://fonts.googleapis.com/css2?family=Roboto&family=Roboto+Flex:opsz,wght@8..144,400;8..144,500&display=swap");
/* Preferred box-sizing value */
*,
*::before,
*::after {
  box-sizing: border-box;
}

.og-footer {
  font-size: 14px;
  font-family: "Roboto flex", "Noto Sans", serif !important;
  background: #f5f5f8;
  color: #303030;
  padding: 48px 0;
}
.og-footer .container {
  max-width: 1310px;
  margin: 0 auto;
  padding: 0 15px;
}
.og-footer .divider {
  border-top: 1px solid #d2d2da;
}
.og-footer .collapsable {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.og-footer .collapsable-container {
  width: 100%;
}
.og-footer .collapsable-arrow svg.svg-base {
  transition: transform 0.2s linear;
  height: 16px;
  width: 16px;
  text-align: right;
}
.og-footer .active.collapsable {
  background: #e9e9ee;
}
.og-footer .active.collapsable .collapsable-arrow svg.svg-base {
  transform: rotate(180deg);
}
.og-footer .collapsable-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-in-out;
}
.og-footer .footer-list-container {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 1023px) {
  .og-footer .footer-list-container {
    flex-direction: column;
    display: none;
  }
}
.og-footer .footer-list-container-mobile {
  display: none;
}
@media (max-width: 1023px) {
  .og-footer .footer-list-container-mobile {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
.og-footer .footer-list-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  padding: 8px 16px;
  height: 48px;
}
@media (max-width: 1023px) {
  .og-footer .footer-list-title {
    cursor: pointer;
    transition: all 0.1s linear;
  }
}
@media (max-width: 1023px) {
  .og-footer .footer-list-title:hover {
    background: #e9e9ee;
  }
}
.og-footer .footer-list-item {
  color: #777;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  transition: all ease-in 0.1s;
  display: block;
  padding: 8px 16px;
  height: 48px;
}
.og-footer .footer-list-item:hover {
  color: #303030;
  text-decoration-line: underline;
  cursor: pointer;
}
.og-footer .footer-second-container {
  display: flex;
  gap: 24px;
}
@media (max-width: 768px) {
  .og-footer .footer-second-container {
    flex-direction: column;
    align-items: center;
  }
}
.og-footer .footer-second-container .flex-auto {
  flex: 0 0 auto;
}
.og-footer .footer-second-container .flex-grow {
  flex: 1;
}
.og-footer .footer-second-container .footer-icons-container {
  display: flex;
  gap: 24px;
}
.og-footer .footer-second-container .footer-icons-container img {
  max-height: 27px;
}
.og-footer .footer-second-container .copyright-content {
  color: rgba(0, 0, 0, 0.7);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}
@media (max-width: 768px) {
  .og-footer .footer-second-container .copyright-content {
    text-align: center;
  }
}
.og-footer .footer-second-container .social-items-container {
  display: flex;
  align-items: center;
  gap: 10px;
}
.og-footer .footer-second-container .social-items-container .social-buttons {
  background: #e9e9ee;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all ease-in 0.1s;
  cursor: pointer;
}
.og-footer .footer-second-container .social-items-container .social-buttons img {
  transform: translate(0px, 1.5px);
  max-height: 18px;
}
.og-footer .footer-second-container .social-items-container .social-buttons:hover {
  opacity: 0.6;
}
.og-footer .footer-third-container {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 1023px) {
  .og-footer .footer-third-container {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}
.og-footer .footer-third-container .third-icons-container {
  display: flex;
  gap: 30px;
}
.og-footer .footer-third-container .third-icons-container .security-list {
  transition: ease-in-out 0.25s;
  filter: grayscale(1);
  opacity: 0.5;
  max-height: 36px;
}
.og-footer .footer-third-container .third-icons-container .security-list:hover {
  transition: ease-in-out 0.25s;
  filter: grayscale(0);
  opacity: 1;
}
.og-footer .footer-third-container .language-button {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  min-width: 200px;
  cursor: pointer;
}
@media (max-width: 1023px) {
  .og-footer .footer-third-container .language-button {
    flex: 0 0 100%;
  }
}
.og-footer .footer-third-container .language-button .content {
  display: flex;
  gap: 8px;
  align-items: center;
}
.og-footer .footer-third-container .language-button .content .country-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  overflow: hidden;
}
.og-footer .footer-backtotop-icon {
  fill: #303030;
  transform: rotate(180deg);
  vertical-align: sub;
  width: 12px;
  height: 16px;
  margin-right: 5px;
}
.og-footer .footer-list-title.collapsable.active {
  color: unset;
}

@media (max-width: 1023px) {
  .full-width-mobile {
    width: 100%;
  }
}

.desktop-only {
  display: block;
}
@media (max-width: 1023px) {
  .desktop-only {
    display: none;
  }
}

.mobile-only {
  display: none;
}
@media (max-width: 1023px) {
  .mobile-only {
    display: block;
  }
}

.legacy__footer {
  background: #2f3dbb;
  padding-top: 30px;
  padding-bottom: 30px;
}

.legacy__footer__container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: white;
  gap: 24px;
}
@media (max-width: 599px) {
  .legacy__footer__container {
    flex-direction: column;
    gap: 20px;
  }
}
.legacy__footer__left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;
  gap: 5px;
}
@media (max-width: 599px) {
  .legacy__footer__left {
    align-items: center;
    text-align: center;
    gap: 20px;
  }
}
.legacy__footer__links {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
@media (max-width: 599px) {
  .legacy__footer__links {
    justify-content: center;
  }
}
.legacy__footer__links-item {
  text-wrap: nowrap;
  display: flex;
  align-items: center;
  gap: 5px;
}
.legacy__footer__links-item img {
  width: 20px;
  height: 20px;
}
@media (min-width: 1024px) {
  .legacy__footer__links-item-support-desktop {
    display: initial;
  }
}
@media (max-width: 1023.99px) {
  .legacy__footer__links-item-support-desktop {
    display: none;
  }
}
@media (min-width: 1024px) {
  .legacy__footer__links-item-support-mobile {
    display: none;
  }
}
@media (max-width: 1023.99px) {
  .legacy__footer__links-item-support-mobile {
    display: initial;
  }
}
.legacy__footer__links-item a {
  height: 20px;
  line-height: 20px;
  color: #fff;
  display: flex;
  align-items: center;
}
.legacy__footer__separator {
  margin: 0 10px;
}
.legacy__footer__right {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.legacy__footer__social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.legacy__footer__social-icon a {
  height: 20px;
  width: 20px;
  text-align: center;
}

.legacy__footer .container {
  padding: 0 20px;
  max-width: 1040px;
  margin: 0 auto;
}

/*# sourceMappingURL=footer-og.css.map */
