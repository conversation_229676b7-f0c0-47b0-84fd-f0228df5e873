body {
    background-color: #2b2b36;
}
body.bg-white {
    background-color: #ffffff;
}
/*SLIDER*/
input[type=range]::-webkit-slider-thumb {
-webkit-appearance:none;
height:17px;
width:7px;
border-radius:3px;
background:#fff;
cursor:pointer;
border:1px solid #666;
box-shadow:1px 1px 1px #999,0 0 1px #0d0d0d;
}

input[type="range"]::-ms-fill-lower,
input[type="range"]::-ms-fill-upper {
  background: transparent;
}

input[type="range"]::-ms-track {
  border-radius: 1px;
  height: 7px;
  border: 1px solid #7d7d7d;
  background: #7d7d7d;
  color: #7d7d7d;
}

input[type="range"]::-ms-thumb {
  -webkit-appearance:none;
height:30px;
width:7px;
border-radius:3px;
background:#fff;
cursor:pointer;
border:1px solid #666;
box-shadow:1px 1px 1px #999,0 0 1px #0d0d0d;
}

.slidecontainer {
background-color:#e5ebeb;
border-radius:5px;
border-left:#AAA solid 1px;
border-top:#AAA solid 1px;
border-right:#FFF solid 1px;
border-bottom:#FFF solid 1px;
/*padding:4px 2px;*/
padding: 0;
}

.slidermain {
/*margin-top:10px;*/
background-color:#f4f4f4;
padding:0;
/*margin-top:2px;*/
align:center;
margin-top: 5px;
}

.split_label{
    color: #666;
    font-size: 12px;
    margin-top: -10px;
}

input[type='range'] {
-webkit-appearance:none;
width:95%;
align:center;
background-color: #7d7d7d;
-webkit-border-radius: 5px;
-moz-border-radius: 5px;

border-left:#AAA solid 1px;
border-top:#AAA solid 1px;
border-right:#FFF solid 1px;
border-bottom:#FFF solid 1px; 
border-radius: 5px;
/*
background:#064280;
background:-moz-linear-gradient(left,#064280 0%,#bb1d15 100%);
background:-webkit-linear-gradient(left,#064280 0%,#bb1d15 100%);
background:linear-gradient(to right,#064280 0%,#bb1d15 100%);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#064280',endColorstr='#bb1d15',GradientType=1);
*/
color:#FFF;
font-size:2em;
height:10px;
cursor:pointer;
padding: 0;
}

.og-logo-brand{
height:35px;
width:35px;
background-size:35px 35px;
/*display:inline-block;*/
display:none;
vertical-align:top;
}

#og_value,#og_wor_value{
font-size:20px;
line-height:34px;
}

.sa-logo { height: 78px; width: 250px; display: inline-block; vertical-align: top; }
.sa-logo.sa-logo-brand { background: url("../images/logo/logo-ogm.png") no-repeat; }
.sa-logo.sa-logo-ogm { background: url("../images/logo/logo-ogm.png") no-repeat; }
.sa-logo.sa-logo-gmz { background: url("../images/logo/logo-gmz-color.png") no-repeat; }

.sa-logo.sa-logo-overview { width: 100%; max-width: 250px; }

.sa-logo.sa-logo-overview.sa-logo-ogm { background: url("../images/logo/logo-ogm-color.png") no-repeat; background-size: 100%; background-position: bottom; }
.sa-logo.sa-logo-overview.sa-logo-gmz { background: url("../images/logo/logo-gmz-color.png") no-repeat; background-size: 100%; background-position: bottom; }

.sa-logo-sm { background: url("../images/logo/logo-ogm.png") no-repeat; width: 250px; height: 78px; display: inline-block; vertical-align: top; }

/* PARTITION */
.partition {
    width: auto;
    margin: 50px 0;
    height: 0;
    border-top: 1px solid #ebebeb;
}
.partition-dashed {
    width: auto;
    margin: 50px 0;
    height: 0;
    border-top: 1px dashed #cccccc;
}
.partition-ball {
    position: relative;
    margin-left: 45%;
    top: -8px;
    text-align: center;
    width: 30px;
    font-size: 14px;
    font-weight: bold;
    color: #999999;
    background-color: #ffffff;
}
.partition-shadow {
    width: auto;
    height: 20px;
    border-bottom: 1px solid #ebebeb;
    box-shadow: 0 10px 10px 0 #f7f6f6;
}

.sticky {
    position: fixed;
    top: 0px;
    width: 100%;
    z-index: 1000;
}
.sticky + .headsection-dropdownsection + .content {
    padding-top: 70px;
}
.anchor-after-sticky {
    margin-top: -70px;
    padding-bottom: 70px;
    display: block;
}

.logo {
    text-align: center;
    padding: 20px 0 10px 0;
}
.headsection {
    display: block;
    width: 100%;
    height: 70px;
    font-size: 12px;
    background-color: #2b2b36;
    border-bottom: 1px solid #222222;
    box-shadow: 0 1px 20px 0 #222222;
}
.headsection-dropdownsection {
    position: relative;
}
.headsection-dropdown {
    position: fixed;
    top: 71px;
    right: 0px;
    z-index: 50;
}

.headsection-dropdown-content {
    padding: 10px 20px 30px 20px;
    background-color: #2b2b36;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    color: #ffffff;
    font-size: 14px;
}
#dropdown-region, #dropdown-profile {
    width: 320px;
    display: block;
}
#dropdown-profile {
    right: 60px;
}
#dropdown-profile a:not(.btn) {
    position: relative;
    display: block;
    padding: 20px;
    border-bottom: 1px solid #444444;
}
#dropdown-profile a:hover {
    padding-left: 23px;
}

.head-logo {
    display: block;
    position: relative;
    text-align: left;
    top: 0;
    left: 20px;
    width: 250px;
    height: 78px;
}
.head-region, .head-profile {
    position: absolute;
    text-align: center;
    top: 25px;
    height: 42px;
    width: 60px;
    font-size:20px;
    color: #fff;
    cursor: pointer;
    display: block;
}
.burger:hover, .head-region:hover, .head-profile:hover, .head-region.active, .head-profile.active {border-bottom: 4px solid #6ec8c7;}

.head-region {right: 0;}
.head-profile {right: 60px;}
.burger {left: 0;}
.burger {display: none;}

.content {
    background: #f6f7f9;
}
.content-inner {
    padding-left: 30px;
    margin-left: 200px;
}
.midsection {
    width: 80%;
    min-height: 500px;
    text-align: center;
    padding: 50px 0px;
    margin: 0px auto;
}
.midsection-small { /*for login and signup*/
    width: 460px;
    text-align: center;
    margin: auto;
}

/* SIDE MENU */
.sidenav-res {
    width: 0;
    height: 100%;
    position: relative;
    z-index: 1;
    top: 0;
    left: 0;
    overflow-x: hidden;
    padding-top: 10px;
    float: left;
    background: #2b2b36;
    border-radius: 6px;
    box-shadow: 2px 2px 10px #cccccc;
}
.sidenav-res .closebtn, .sidenav-res .avatar-profile {
    display: none;
}
@media screen and (min-width: 801px ) {
    .midsection .sidenav-res {
        width: 200px !important;
    }
}
@media screen and (max-width : 800px ){
    .sidenav-res {
        position: fixed;
        -webkit-transition: 0.5s;
        transition: 0.5s;
        z-index: 3000;
        overflow: hidden;
    }
    .sidenav-res .closebtn, .sidenav-res .avatar-profile {
        display: block;
    }
}
.sidenav-res a, .dropdown-btn {
    padding: 20px;
    font-size: 14px;
    display: block;
    text-decoration: none;
    color: #6ec8c7;
    opacity: 0.8;
    -webkit-transition: 0.2s;
    transition: 0.2s;
    text-align: left;
    cursor: pointer;
    border-bottom: 1px solid #444444;
}
.sidenav-res a:hover, .sidenav-res a.active, .dropdown-btn:hover {
    padding-left: 23px;
    opacity: 1;
}
.fa-angle-down, .fa-angle-up, .external-link-icon {
    float: right;
}
.dropdown-btn.active .fa-angle-down {
    display: none;
}
.dropdown-btn:not(.active) .fa-angle-up {
    display: none;
}
.sidenav-res a.active {
    color: #FFFFFF;
}
.sidenav-res .closebtn {
    position: absolute;
    top: -10px;
    right: 0;
    font-size: 36px;
    border: none;
}
.avatar-profile, .avatar-profile-fb {
    display: block;
    padding: 20px 20px 50px 20px;
    border-bottom: 1px solid #444444;
    text-align: left;
}
.avatar-profile-fb {
    border-bottom: 1px solid #ebebeb;
}
.avatar {
    position: absolute;
    padding: 5px;
    width: 60px;
    height: 60px;
    border: 1px solid #666;
    border-radius: 6px;
    box-shadow: 2px 2px 10px #000000;
    text-align: center;
    color: white;
}
.avatar-text {
    padding: 5px;
    font-size: 14px;
    line-height: 1.3;
    font-weight: bold;
    color: #ccc;
    margin: 20px 0px 0px 80px;
}
.avatar-text-small {
    font-size: 12px;
    font-weight: normal;
    color: #999;
}

.active {
    color: #fff;
}
.dropdown-container {
    max-height: 0;
    background-color: #000000;
    overflow: hidden;
    transition: 0.2s;
    -webkit-transition: 0.2s;
}
.dropdown-container.collapsed {
    max-height: 500px;
}
.dropdown-container a {
    padding: 15px 0 15px 20px;
    font-size: 12px;
    color: #FFFFFF;
}
.dropdown-container a:hover {
    padding-left: 23px;
}

/* CALCULATION */
ul.calculation {
    list-style: none;
    border: 0 none;
    font: inherit;
    margin: 0;
    padding: 0;
    vertical-align: middle;
    width: 100%;
    display: table;
    padding-top: 30px;
    padding-bottom: 30px;
    margin: 0;
    font-size: 14px;
}
ul.calculation li {
    margin: 0;
}

.cal-row {
    display: table-row;
}
.cal-row-label, .cal-row-value {
    display: table-cell;
    vertical-align: top;
    padding-bottom: 18px;
    position: relative;
    z-index: 0;
}
.cal-row-value {
    text-align: right;
}
.cal-row-label::after, .cal-row-value::after {
    content: '';
    position: absolute;
    width: 100%;
    top: 8px;
    height: 1px;
    left: 0;
    border-bottom: 1px dotted #999;
    z-index: -1;
}
.cal-row-label-text {
    display: inline-block;
    background: #fff;
    position: relative;
    padding-right: 10px;
    float: left;
}
.cal-row-label-text-small {
    font-size: 12px;
    color: #999999;
    line-height: 1.3;
}
.cal-row-value-text {
    display: inline-block;
    background: #fff;
    position: relative;
    padding-left: 10px;
}

/* PAGE */
.page-title, .page-description, .page-description-table {
    position: relative;
    text-align: left;
}
.page-description, .page-description-table {
    margin-bottom: 10px;
}
.page-description-highlight {
    font-size: 16px;
    font-weight: bold;
    color: #000;
}

/* PAGINATION */
.pagination {
    display: inline-block;
    padding: 30px;
}
.pagination a {
    color: #6ec8c7;
    font-size: 14px;
    float: left;
    padding: 12px 16px;
    text-decoration: none;
    border-top: 1px solid #6ec8c7;
    border-right: 1px solid #6ec8c7;
    border-bottom: 1px solid #6ec8c7;
    background-color: #FFFFFF;
}
.pagination a.active {
    background-color: #6ec8c7;
    color: #FFFFFF;
    border: 1px solid #6ec8c7;
}
.pagination a:hover:not(.active) {
    background-color: #6ec8c7;
    color: #FFFFFF;
}
.pagination a:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    border-right: 1px solid #6ec8c7;
    border-left: 1px solid #6ec8c7;
}
.pagination a:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

/* WHITE CURVE CORNER CARD */

.card-default {
    width: 80%;
    text-align: left;
    padding: 50px 0px 50px 0px;
    margin: 0px auto;
}
.card-small { /* for login and signup */
    width: auto;
    height: auto;
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px solid #ebebeb;
    margin-top: 10px;
    padding: 30px 30px 0 30px;
}
.card {
    position: relative;
    margin-bottom: 20px;
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px solid #ebebeb;
    box-shadow: 2px 2px 10px #cccccc;
    padding: 30px;
    overflow: hidden;
}

.card-overview {
    width: 90%;
    text-align: center;
    margin: 50px auto;
}
.card-overview-box {
    width: calc((75% - 50px)/2);
    display: inline-block;
    margin: 50px;
    vertical-align: top;
}
.card-overview-box .card-overview-box-title, .card-overview-box .card-overview-box-title h1{
    color: #ffffff;
}
.card-overview-partition {
    position:relative;
    display: inline-block;
    top: 50px;
    height: 165pt;
    border-right: 1px solid #666666;
}
.card-overview-middle {
    width: 100%;
    margin: auto;
    text-align: center;
    background-color: #6ec8c7;
}
.card-overview-box-description {
    width: auto;
    text-align: center;
}
.card-overview-middle-box {
    width: 75%;
    display: inline-block;
    margin: auto;
    padding-bottom: 50px;
}
.card-overview-box-title {
    font-size: 14px;
    padding: 10px;
}
.card-overview-box-title h1 {
    line-height: 30px;
}
.card-overview-bottom {
    width: 100%;
    margin: auto;
    background-color: #FFFFFF;
    text-align: center;
}
.card-overview-bottom-box {
    width: calc(85%/3);
    display: inline-block;
    margin: 50px 25px;
    padding: 0px 15px;
    vertical-align: top;
}
.card-overview-bottom-logo {width: 80%;}

.card-store-credit {
    width: calc(85%/3);
    display: inline-block;
    margin: 50px 15px;
    padding: 0px 25px;
    vertical-align: top;
}

.card-error-box {
    width: auto;
    margin: 20% 10% 5% 10%;
}

.card-title {
    display: block;
    margin-bottom: 10px;
    text-align: left;
}
.card-title-2 {
    display: inline-block;
    margin-bottom: 10px;
    text-align: left;
    float: left;
}
.card-title-icon {
    position: relative;
    float: left;
    top: -5px;
    margin-right: 10px;
    font-size: 36px;
}
.card-title-image {
    margin-right: 20px;
    float: left;
}
.card-description {
    display: inline-block;
    text-align: left;
    width: 100%;
}
.card-description-left {
    margin-bottom: 10px;
    text-align: left;
    width: 70%;
}
.card-security-token {
    display: block;
    border: 2px dotted #6ec8c7;
    border-radius: 4px;

    padding: 0 10px 30px 10px;
    margin: 50px 0 10px 0;
}
.card-security-title {
    position: relative;
    top: -30px;
    left: 20px;
    padding-left: 5px;
    padding-right: 25px;
    float: left;
    background-color: #ffffff;
}
.card-tab { /* for login and signup */
    font-family: 'Fjalla One',"sans-serif";
    font-size: 20px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 50px;
    text-align: left;
}
.card-tab a {
    position: relative;
    float: right;
    color: #6ec8c7;
    text-decoration: none;
    opacity: 0.8;
    -webkit-transition: 0.2s;
    transition: 0.2s;
}
.card-tab a:hover {
    opacity: 1;
}

/*TABLE*/
.table {
    display: table;
    width: 100%;
}
.table-head {
    display: table-header-group;
    background-color:#f2f3f6;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}
.row {
    display:table-row;
}
.row:hover {
    background-color: #f7f8fb;
}	
.row .column:nth-child(1) { /* First column in a row */
    border-left:none;
}
.row:last-child .column, .row:last-child .column-right, .row:last-child .column-hide { /* column in a last row */
    border-bottom:none;
}
.column, .column-table-head, .column-right, .column-hide {
    display:table-cell;
    padding:10px 20px;
    border-bottom:1px solid #ebebeb;
    line-height:1.8em;
    text-align: left;
}
.column-table-head {
    padding:20px 20px;
}
.column-right, .column-hide {
    text-align: right;
}

#card-table {
    position: relative;
    margin-bottom: 20px;
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px solid #ebebeb;
    box-shadow: 2px 2px 10px #cccccc;
    font-size: 14px;
    line-height: 1.3;
}
#card-table table {
    border-collapse: collapse;
    width: 100%;
}
#card-table th, #card-table td, #card-table .th-1, #card-table .th-2, #card-table .td-1, #card-table .td-2, #card-table .td-3-left {
    text-align: left;
}
#card-table .th-3, #card-table .th-4, #card-table .td-3, #card-table .td-4 {
    text-align: right;
}
#card-table th {
    padding: 20px 20px;
    font-size: 12px;
    background-color: #f2f3f6;
    text-transform: uppercase;
}
#card-table td {
    padding: 14px 20px;
    border-top: 1px solid #ebebeb;
}
.td-debit {
    color: #ff8a84;
}
#card-table tr:hover {
    background-color: #f7f8fb;
}

ol.list-no-margin {
    padding-left: 1em;
}

/* TOOLTIP */
.tooltip-icon {
    display: inline-block;
    position: relative;
    top: -11px;
    font-size: 20px;
}
.tooltip {
    cursor: pointer;
    color: #6ec8c7;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.tooltip .tooltip-text {
    width: 260px;
    background-color: #333333;
    font-family: Helvetica, Arial, "sans-serif";
    font-size: 12px;
    color: #fff;
    line-height: 1.3;
    text-align: left;
    border-radius: 6px;
    padding: 20px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -150px;
}
.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

/* EXPAND COLLAPSE */
.collapse-spot {
    position: relative;
    width: 100%;
    margin-top: 50px;
    z-index: 2;
}
.collapse-box {
    overflow: hidden;
    height: 0;
}
.collapse-content {
    padding-top: 5px;
}
.ajax-loading-area {
    padding: 30px;
}

/*INPUT ROW*/
.input-row {
    display: inline-block;
    height: 30px;
    width: auto;
}
.input-row-text {
    width: 100%;
    text-align: center;
}
.input-row-text-full, .input-row-error {
    position: relative;
    display: inline-block;
    width: 100%;
    float: left;
    padding-bottom: 30px;
    font-size: 12px;
    color: #999999;
    line-height: 1.3;
}
.input-row-text-right {
    position: relative;
    display: block;
    text-align: right;
    font-size: 14px;
    color: #999999;
    line-height: 1.3;
}
.input-row-error {
    width: auto;
    text-align: left;
    font-style: italic;
    color: #ff8a84;
    padding-bottom: 20px;
}
.input-row-2-col {
    width: calc(90%/2);
    display: inline-block;
}
.input-row-login {
    width: calc(98%/2);
    display: inline-block;
}
.input-row-2-coll {
    display: inline-block;
    width: calc((100% - 1em)/2);
}
.input-row-2-coll-right {
    position: relative;
    margin-left: 10px;
    bottom: -1px;
}
.input-row-2-coll-left {
    position: relative;
    margin-right: 10px;
    top: 1px;
}
.input-row-3-coll-storecredits {
    display: inline-block;
    width: calc(98%/3);
    margin-top: 10px;
}

.input-row-4-coll {
    padding-right: 5px;
    position: relative;
    display: inline-block;
    width: calc((100% - 30px)/6);
    margin-left: 5px;
}
.input-row-4-coll:first-child {
    margin-left: 0px;
}
.input-row-4-coll-error {
    width: 60%;
    float: none;
    margin: 0px auto;
}
.input-row-6-coll {
    position: relative;
    display: inline-block;
    width: calc((100% - 30px)/6);
    margin-left: 5px;
}
.input-row-6-coll:first-child {
    margin-left: 0px;
}
.input-row-6-coll-error {
    width: 60%;
    float: none;
    margin: 0px auto;
}
.input-row-token {
    position: relative;
    display: block;
    width: 60%;
    max-width: 350px;
    text-align: center;
    margin: 0px auto;
}
.input-row-im {
    display: inline-block;
    width: 100%;
    border-bottom: 1px dashed #cccccc;
}
.input-row-im-left {
    position: relative;
    float: left;
    width: 90%;
}
.input-row-im-icon {
    position: relative;
    float: right;
    top: 20px;
    margin-left: 5px;
}

/* for instant messenger */
.icon-remove a {
    font-size: 20px;
    color: #ff5d55;
    opacity: 0.8;
    -webkit-transition: 0.2s;
    transition: 0.2s;
    cursor: pointer;
}
.icon-remove a:hover {
    opacity: 1;
}

/* loading icon */
.icon-loading {
    color: #6ec8c7;
}

.form { /*for login and signup*/
    margin-bottom: 30px;
    font-family: Helvetica, Arial, "sans-serif";
    font-size: 14px;
    color: #333333;
}
.form-normal {
    margin: 60px 15%;
    font-family: Helvetica, Arial, "sans-serif";
    font-size: 14px;
    color: #333333;
    text-align: left;
}
.form-normal.nomargin {
    margin: 60px 0;
}

label {
    margin-top: 20px;
    display: block;
    clear: both;
}
input[type=text], input[type=password], input[type=date], input[type=number], input[type=tel] {
    width: 100%;
    padding: 11px 20px;
    margin: 10px 0;
    box-sizing: border-box;
    border: 1px solid #ccc;
    -webkit-transition: 0.2s;
    transition: 0.2s;
    outline: none;
    background-color: #fff;
    color: #999;
    font-size: 14px;
    text-align: left;
    border-radius: 4px;
}
input::placeholder {
    color: #ddd;
}
input[type=text]:disabled::placeholder {
    color: #999;
}
input[type=text]:focus, input[type=password]:focus {
    border: 1px solid #6ec8c7;
    color: #6ec8c7;
}
.form-group.has-error input[type=text]:not(:focus), .form-group.has-error input[type=password]:not(:focus), .form-group.has-error input[type=date]:not(:focus) {
    border-color: #ff8a84;
    color: #ff8a84;
}
.form-group.has-error input[type=text]:not(:focus)::placeholder, .form-group.has-error input[type=password]:not(:focus)::placeholder, .form-group.has-error input[type=date]:not(:focus)::placeholder {
    color: #ff8a84;
}
input[type=text]:disabled, input[readonly] {
    color: #000;
    background-color: #f6f7f9;
    border: 1px solid #ccc;
    font-weight: bold;
}
input[type=text].im-hangout, input[type=text].im-skype, input[type=text].im-qq, input[type=text].im-line, input[type=text].im-wechat, input[type=text].im-facebook_messenger, input[type=text].im-whatsapp {
    background-image: url(../images/icon/im-gtalk.png);
    background-repeat: no-repeat;
    background-position: 10px 10px;
    padding-left: 40px;
    background-size: 20px 20px;
}
input[type=text].im-skype {
    background-image: url(../images/icon/im-skype.png);
}
input[type=text].im-qq {
    background-image: url(../images/icon/im-qq.png);
}
input[type=text].im-line {
    background-image: url(../images/icon/im-line.png);
}
input[type=text].im-wechat {
    background-image: url(../images/icon/im-wechat.png);
}
input[type=text].im-facebook_messenger {
    background-image: url(../images/icon/im-fb.png);
}
input[type=text].im-whatsapp {
    background-image: url(../images/icon/im-whatsapp.png);
}
input.token-single {
    padding: 8px 5px;
    margin: 5px 0;
    text-align: center;
}

.form-group .input-row-error {
    display: none;
}
.form-group.has-error .input-row-error {
    display: block;
}

/* CUSTOM CHECKBOX & RADIO BUTTON */
.custom-checkbox, .custom-radio {
    display: block;
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: left;
    line-height: 1.3;
}
/* Hide the browser's default checkbox */
.custom-checkbox input, .custom-radio input  {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}
.checkmark, .checkmark-radio {
    position: absolute;
    top: -2px;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.checkmark-radio {
    border-radius: 50%;
}
.custom-checkbox:hover input ~ .checkmark, .custom-radio:hover input ~ .checkmark-radio {
    border: 1px solid #6ec8c7;
}
.custom-checkbox input:checked ~ .checkmark, .custom-radio input:checked ~ .checkmark-radio {
    background-color: #6ec8c7;
    border: 1px solid #6ec8c7;
}
.checkmark:after, .checkmark-radio:after {
    content: "";
    position: absolute;
    display: none;
}
.custom-checkbox input:checked ~ .checkmark:after, .custom-radio input:checked ~ .checkmark-radio:after {
    display: block;
}
.custom-checkbox .checkmark:after {
    left: 6px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
.custom-radio .checkmark-radio:after {
    left: 5px;
    top: 5px;
    width: 10px;
    height: 10px;
    background-color: #fff;
    border-radius: 50%;
}

/*BUTTON POSITION*/
.btn-spot, .btn-spot-special {
    display: inline-block;
    width: 100%;
    margin-top: 0px;
    text-align: center;
}
.btn-spot-2col {
    display: inline-block;
    width: calc(60%/3);
    margin-bottom: 20px;
}
.btn-spot-right {
    display: inline-block;
    position: relative;
    top: -60px;
    float: right;
}
.btn-spot-right-expand {
    display: inline-block;
    position: absolute;
    top: 80px;
    right: 30px;
}
.btn-spot-special {
    margin-top: 10px;
}
.btn-spot-special a {
    display: block;
}

/*BUTTON STYLE*/
a.btn {
    display: inline-block;
    box-sizing: border-box;
}
button {
    opacity: 0.8;
    -webkit-transition: 0.2s;
    transition: 0.2s;
}
button:hover {
    opacity:1;
}
button:focus {
    outline: 0;
}
.btn-submit, .btn-default, .btn-default-full, .btn-default-mid, .btn-submit-full, .btn-logout {
    font-weight: bold;
    font-size: 12px;
    padding: 15px 20px;
    border-radius: 50px;
    cursor: pointer;
    min-width: 120px;
    text-transform: uppercase;
}
.btn-submit, .btn-submit-full, a.btn-submit, a.btn-submit-full {
    background-color: #6ec8c7;
    color: #FFFFFF;
    border: 1px solid #6ec8c7;
}
.btn-submit-full, .btn-default-full {
    width: 100%;
}
.btn-logout, a.btn-logout {
    background-color: #ff5d55;
    color: #FFFFFF;
    border: 1px solid #ff5d55;
}
.btn-default, .btn-default-full {
    background-color: #FFFFFF;
    color: #6ec8c7;
    border: 1px solid #6ec8c7;
}
.btn-default-mid {
    position: relative;
    top: -25px;
    background-color: #FFFFFF;
    color: #6ec8c7;
    border: 1px solid #6ec8c7;
}
.btn-default-mid-small {
    position: relative;
    top: -25px;
    background-color: #FFFFFF;
    color: #6ec8c7;
    border: 1px solid #6ec8c7;
    font-size: 20px;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 50px;
}
.btn-inactive {
    font-weight: bold;
    font-size: 12px;
    padding: 15px 20px;
    border-radius: 50px;
    min-width: 120px;
    background-color: #f6f7f9;
    color: #cccccc;
    border: 1px solid #cccccc;
}
.btn-expand-collapse-hide-collapse.collapsed {
    display:none;
}
.btn-facebook, .btn-twitter, .btn-google, .btn-paypal {
    background-color: #3b5998;
    border: 1px solid #3b5998;
    color: #FFFFFF;
    font-weight: normal;
    width: 100%;
    font-size: 14px;
    padding: 11px 20px;
    border-radius: 50px;
    cursor: pointer;
}
.btn-twitter {
    background-color: #0084b4;
    border: 1px solid #0084b4;
}
.btn-paypal {
    background-color: #1083d5;
    border: 1px solid #1083d5;
}
.btn-google {
    background-color: #d34836;
    border: 1px solid #d34836;
}
.btn-storecredits, .btn-storecredits-selected {
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC;
    color: #999999;
    font-weight: normal;
    width: 95%;
    font-size: 14px;
    padding: 11px 20px;
    border-radius: 4px;
    box-shadow: 1px 1px 5px #cccccc;
    cursor: pointer;
}
.btn-storecredits-selected {
    background-color: #6ec8c7;
    border: 1px solid #6ec8c7;
    color: #FFFFFF;
    box-shadow: none;
}

/* for login and signup */
.icon-socialconnect {
    position: relative;
    float: left;
    font-size: 16px;
}
/* for successful */
.icon-success {
    font-size: 80px;
    color: #72bb53;
}

.einvoice-type-wrapper {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
}

.einvoice-type-title {
    text-align: left;
}

.einvoice-type-toggle {
    width: 40%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.einvoice-toggle-container {
    display: flex;
    width: 200px;
    height: 50px;
    background: #e0e0e0;
    border-radius: 25px;
    overflow: hidden;
    position: relative;
    padding: 2px;
}

.einvoice-toggle-button {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    transition: color 0.3s;
}

.einvoice-toggle-slider {
    position: absolute;
    width: 48%;
    height: 90%;
    background: #fff;
    border-radius: 25px;
    transition: transform 0.3s ease;
    z-index: 0;
    padding: 1px;
}

#personal.active~.einvoice-toggle-slider {
    transform: translateX(0);
}

#business.active~.einvoice-toggle-slider {
    transform: translateX(100%);
}

.einvoice-toggle-button.active {
    color: #3BB3FF;
}

.einvoice-toggle-button:not(.active) {
    color: #000;
}

.nid-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    width: 100%;
}

.nid-input-group .form-group {
    flex: 1;
    min-width: 0;
    margin: 0;
}
.nid-separator {
    flex: 0 0 auto;
    font-weight: bold;
    color: #333;
    font-size: 16px;
    margin: 0 2px;
}

.nid-input-field {
    flex: 1;
    text-align: center !important;
    -moz-appearance: textfield !important; /* Firefox */
}

.nid-input-field::-webkit-inner-spin-button,
.nid-input-field::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.nid-input-field:focus {
    border: 1px solid #6ec8c7 !important;
    color: #6ec8c7 !important;
}


/* Modal */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 5; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.8); /* Black w/ opacity */
}
.modal-content {
    position: relative;
    margin: auto;
    padding: 0;
    width: 460px;
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px solid #ebebeb;
    -webkit-animation-name: animatetop;
    -webkit-animation-duration: 0.4s;
    animation-name: animatetop;
    animation-duration: 0.4s
}
.modal-header {padding: 2px 16px;}
.modal-body {padding: 16px;}
.modal-footer {padding: 2px 16px;}
.modal-header + .modal-body {padding-top: 2px;}
.modal-icon-close {
    position: relative;
    float: right;
    top: 8px;
    color: #6ec8c7;
    font-size: 36px;
    opacity: 0.8;
    -webkit-transition: 0.1s;
    transition: 0.1s;
    cursor: pointer;
}
.modal-icon-close:hover,
.modal-icon-close:focus {
    opacity: 1;
}

.page-description-table p {
    display: inline-block;
}

.page-description-right {
    width: 200px;
    position: absolute;
    right: 0px;
    top: -10px;
}

/* Add Animation */
@-webkit-keyframes animatetop {
    from {top:-300px; opacity:0} 
    to {top:0; opacity:1}
}

@keyframes animatetop {
    from {top:-300px; opacity:0}
    to {top:0; opacity:1}
}

@media screen and (max-width : 1024px ){
    .midsection {width: 90%;}
    .card-overview {width: 100%;}
    .card-overview-box {width: calc((75% - 50px)/2);}
}

@media screen and (max-width : 800px ){	
    .head-logo {left: 60px;}
    .head-logo-default {left: 20px;}
    .burger {
        position: absolute;
        text-align: center;
        top: 25px;
        height: 42px;
        width: 60px;
        font-size:20px;
        color: #fff;
        cursor: pointer;
        display: block;
    }

    .midsection .sidenav {display:none;}
    .card-overview-box {margin: 30px;}

    #card-table {	
        margin-left: 0px;
        max-width: 100%;
    }
    .content-inner {
        padding-left: 0px;
        margin-left: 0px;
    }
}
    
@media screen and (max-width : 460px ){
    #dropdown-region, #dropdown-profile {
        width: 100%;
    }
    #dropdown-profile {
        right: 0;
    }
    .mr {margin: 1px;}

    .midsection {
        width: 100%;
        margin: 0px auto;
        padding: 30px 0px;
    }
    .midsection-small {width: 100%;}

    .card-overview-box, .card-overview-bottom-box {
        width: 90%;
        display: block;
        margin: 20px;
        padding: 30px 0px 0px 0px;
    }
    .card-overview-bottom-box {
        padding-bottom: 30px;
        border-top: 1px solid #ebebeb;
    }
    .card-overview-bottom-logo {width: 60%;}
    .card-overview-partition {display: none;}	
    
    .card-error-box {margin: 30% 10% 5% 10%;}

    .card-title-image {
        float: none;
        text-align: center;
    }
    
    .page-title, .page-description-table {
        margin: 0 10px;
        max-width: 100%;
        text-align: center;
    }
    .page-description {
        display: none;
    }
    .pagination a {
        font-size: 14px;
        padding: 12px;
    }
    .calculation {
        width: 100%;
        margin: 0;
    }
    .tooltip .tooltip-text {
        width: 130px;
        left: 0;
        margin-left: -75px;
    }
    .card-small {
        border-radius: 0;
        border: none;
        margin-top: 0;
        padding-bottom: 5px;
    }
    .card, #card-table {
        border-radius: 0;
    }
    .card-description, .card-description-left {
        width: 100%;
    }
    .form-normal {
        margin: 15px 0;
    }
    .modal-content {
        width: 80%;
    }	
    .input-row-2-coll {
        width: 100%;
    }
    .input-row-2-coll-right {
        margin-left: 0;
    }
    .input-row-2-coll-left {
        margin-right: 0;
    }
    .input-row-3-coll-storecredits {
        width: calc(98%/2);
    }
    .input-row-token {
        width: 90%;
    }
    .input-row-6-coll-error {
        width: 90%;
    }
    .btn-spot-2col {
        width: 100%;
        margin: 5px 0;
    }
    .btn-spot-right {
        top: 0;
        float: none;
        width: 100%;
    }
    .btn-spot-right-expand {
        position: relative;
        top: 0;
        right: 0;
        width: 100%;
    }
    .btn-submit, .btn-default, .btn-logout, .btn-inactive {
        width: 100%;
    }
    #card-table .th-1, #card-table .th-4, #card-table .td-1, #card-table .td-4 {
        display: none;
    }
    .table,
    .row,
    .column,
    .column:after,
    .column-right:after
    { /* Converts a table, table row, table column and table column:before into a block element */
        display:block;
        font-size:12px;
    }
    .column, .column-right {
        padding: 0 20px;
    }
    .table,
    .row .column:last-child {
        border-bottom:none;
    }
    .table-head { /* Hides table head but not using display none */
        position:absolute;
        top:-1000em;
        left:-1000em;
    }
    .row {
        border-bottom: 1px solid #ebebeb;
        padding: 10px 0;
    }
    .row .column:last-child { /* last column of the row */
        border-right:none;
    }
    .column, .column-right { /* Column in the last row and column */
        border-bottom:none;
    }	
    .column:before, .column-right:before { /* prints the value of data-label attribute before the column data */
        font-weight:bold;
        content:" "attr(data-label)": ";	/* call the attribute value of data-label and adds a string // */
    }
    .column-hide {
        display: none;
    }
    .float-notice {
        width: 90%;
    }
    
    .page-description-right {
        width: 100%;
        display: block;
        position: static;
    }
}

.card-security-token button.btn-request-token .fa {
    font-size: 20px;
    vertical-align: middle;
    line-height: 12px;
}

/* Styling for main dropdown */
body .select2.select2-container--default {
    min-width: 100%;
    max-width: 100%;
    margin: 10px 0;
    font-size: 14px;
    text-align: left;
}
body .select2-container--default.select2-container--open:not(.select2) .select2-dropdown.select2-dropdown--above {
    margin-top: -6px;
}
body .select2-container--default .select2-selection--single {
    border: 1px solid #ccc;
    border-radius: 4px;
    height: 42px;
}
body .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 42px;
}
body .select2-container--default .select2-selection--single .select2-selection__rendered, body .select2-container--default .select2-results__option {
    color: #999;
    padding: 12px 20px;
    line-height: normal;
}
body .select2-container .selection .select2-hide-in-selected {
    display: none;
}
body .select2-container--default .select2-results__option {
    background-color: #fff;
}
body .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #6ec8c7;
    color: #fff;
}

body .ui-widget {
    font-family: inherit;
    font-size: 1em;
}

/* Styling for toastr */

#toast-container > div {
   padding: 20px 20px 20px 50px;
   font-size: 14px;
   line-height: 1.3em;
   opacity: 1;
}

.card-security-captcha {
    display: none;
}

.card-captcha {
    width: 300px;
    margin: 0px auto;
}

.wor-memo {
    background-color: #6ec8c7;
    padding: 20px;
    border-radius: 5px;
    color: #fff;
    width: 92%;
}

.mobile-change-modal-main{
    display: block;
}

.mobile-change-modal-main .content-mobileChange{
    text-align: left;
}

.mobile-change-modal-main .section_mobileChange{
    font-size: 14px;
    color: #333333;
    text-align: left;
}

.mobile-change-modal-main .category_mobileChange{
    margin-bottom: 15px;
}

.mobile-change-modal-main .section_bellow_mobileChange{
    display: flex;
    justify-content: center;
    padding: 20px;
    padding-bottom: 0 !important;
    border-top: 1px solid #ebebeb;
    margin-top: 10px;
    margin-right: -30px;
    margin-left: -30px;
}

.mobile-change-modal-main .sumbit_section_mobileChange-r{
    padding-right: 10px;
}

.mobile-change-modal-main .sumbit_section_mobileChange-l{
    padding-left: 10px;
}

.mobile-change-modal-main .sub_upload-title{
    padding-top: 15px;
}

.close-mobileNumChange{
    position: relative;
    bottom: -14px;
    padding-left: 34px;
    padding-right: 34px;
}

.modalDialog-mobileNumChange {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1050;
    opacity:0;
    -webkit-transition: opacity 100ms ease-in;
    -moz-transition: opacity 100ms ease-in;
    transition: opacity 100ms ease-in;
    pointer-events: none;
}

.modalDialog-mobileNumChange:target {
    opacity:1;
    pointer-events: auto;
}

.modalDialog-mobileNumChange.modalDialog-mobileNumChange-show {
    opacity:1;
    pointer-events: auto;
}

.modalDialog-mobileNumChange > div {
    transition: transform 200ms ease-out;
    transform: translate(0, 50%);
    position: relative;
    width: 70%;
    max-width: 500px;
    padding: 30px;
    border-radius: 5px;
    margin: auto;
    max-height: calc(100vh - 140px);
    min-height: inherit;
    flex-direction: column;
    overflow-y: scroll !important;
    -webkit-overflow-scrolling: touch;
    background: #fff;
}

.choose::-webkit-file-upload-button {
    /* color: white; */
    display: inline-block;
    /* background: #1CB6E0; */
    white-space: nowrap;
    background-color: #FFFFFF;
    color: #6ec8c7;
    border: 1px solid #6ec8c7;
    text-transform: uppercase;
    min-width: 120px;
    padding: 15px 20px;
    border-radius: 50px;
    font-weight: bold;
    font-size: 12px;
    cursor: pointer;
    margin-right: 10px;
  }

  .choose::-webkit-file-upload-button:hover{
    opacity: 1;
  }

  @media (max-width: 425px) {
    .modalDialog-mobileNumChange > div {
        width: -webkit-fill-available;
        width: -moz-available;
        height: 100%;
        margin: 0;
        min-height: calc(100vh - 60px);
        max-height: 100%;
        transform: none;
        left: 0;
    }
  }

  @media(max-width: 768px) {
    .remove-scrolling {
      height: 100%;
      overflow: hidden !important;
      }
  }

  .category_mobileChange.input[type="file"] {
    width: 90%;
  }

@media(max-width: 425px) {
    .mobile-change-modal-main .section_bellow_mobileChange{
        margin-bottom: 50px;
    }
}

@media(max-width: 425px) {
    .choose{
        width: 90%;
    }
}

.country-change-item-form{
    margin: 0 30px !important;
}

.country-change-content-form{
    margin: 0 30px 0 30px !important;
}

@media(max-width: 320px) {
    .country-change-item-form{
        margin: 0 !important;
    }
    .country-change-content-form{
        margin: 0 !important;
    }
}

@media(max-width: 425px) {
    .country-change-select-file{
        margin-right: 0;
        width: calc(100% - 50px);
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
    }
}

.country-change-select-file{
    margin: 5px 0 15px 0;
    width: fit-content;
    display: flex;
    justify-content: center;
}

.btn-submit.btn-submitting {
    background: #6ec8c7 !important;
    color: #ffffff !important;
    cursor: not-allowed;
}
.btn-submit.btn-submitting:hover  {
    cursor: not-allowed;
}
.btn-submit.btn-submitting {
    font-size: 0;
}
.btn-submit.btn-submitting::after{
    content: attr(data-submitting-txt);
    font-size: 12px;
}

.pending_approval-content{
    padding: 35px 0 15px 0px;
    font-size: 14px;
    color: #666666;
}
.dynamic-input-otp-form{
    transform: none !important; 
    margin: 50px auto !important;
}
@media(max-width: 425px) {
    .dynamic-input-otp-form{
        margin: 0 !important;
        
    }
}
.dynamic-mobile-change-content{
    margin: 50px auto !important;
    transform: none !important;
    max-height: calc(100vh - 150px)!important;
}
@media(max-width: 425px) {
    .dynamic-mobile-change-content{
        margin: 0 !important;
        
    }
}

.dynamic-mobile-otp-content{
    margin: 50px auto !important;
    transform: none !important;
    max-height: calc(100vh - 140px)!important;
}
@media(max-width: 425px) {
    .dynamic-mobile-otp-content{
        margin: 0 !important;

    }
}
@media(max-width: 425px) {
    .dynamic-submitted-form{
        padding-top: 50% !important;
    }
}

@media only screen and (max-width: 320px) {
    .g-recaptcha {
    transform:scale(0.77)!important;
    transform-origin:0 0;
    }
}

@media only screen and (max-width: 375px) {
    .g-recaptcha {
    transform:scale(0.9);
    transform-origin:0 0;
    }
}

@media only screen and (max-width: 540px) {
    .g-recaptcha {
    transform:scale(0.9);
    transform-origin:0 0;
    }

    .einvoice-type-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .einvoice-type-title {
        width: 100%;
    }

    .einvoice-type-toggle {
        width: 100%;
        justify-content: flex-start;
        margin-top: 10px;
    }
    }

@media(max-width: 425px) {
    .head-profile {
        right: 48px;
    }
    .head-logo {
        top: -3px;
    }
    .sa-logo-sm { background: url("../images/logo/logo-ogm.png") no-repeat; height: 72px; display: inline-block; vertical-align: top; background-size:contain; }
}

@media(max-width: 375px) {
    .head-profile {
        right: 48px;
    }
    .head-logo {
        left: 48px;
        top: -1px;
    }
    .sa-logo-sm { background: url("../images/logo/logo-ogm.png") no-repeat; height: 70px; display: inline-block; vertical-align: top; background-size:contain; }
}

@media(max-width: 320px) {
    .head-profile {
        right: 48px;
    }
    .head-logo {
        top: 10px;
    }
    .sa-logo-sm { background: url("../images/logo/logo-ogm.png") no-repeat; height: 48px; display: inline-block; vertical-align: top; background-size:contain; }
}