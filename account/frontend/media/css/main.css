body {
    transition: background-color .5s;
    font-family: Helvetica, <PERSON><PERSON>, "sans-serif";
    background-color: #f6f7f9;
    margin: auto;
    overflow-x: hidden;
}
h1 {
    font-family: 'Fjalla One',"sans-serif";
    color: #333333;
    font-size: 30px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0;
}
h2 {
    font-family: 'Fjalla One',"sans-serif";
    color: #333333;
    font-size: 20px;
    font-weight: bold;
    text-transform: uppercase;
}
p, li {
    font-family: Helvetica, Arial, "sans-serif";
    font-size: 14px;
    color: #666666;
    line-height: 1.3;
}
a:link, a:visited {
    text-decoration: none;
    color: #6ec8c7;
    opacity: 0.8;
    -webkit-transition: 0.1s;
    transition: 0.1s;
}
a:hover {
    opacity: 1;
}
footer {
    padding: 50px 0px;
    text-align: center;
}

/*-- misc --*/
.space-10 { height: 10px; }
.space-20 { height: 20px; }
.space-30 { height: 30px; }
.space-50 { height: 50px; }
.space-80 { height: 80px; }
.space-100 { height: 100px; }
.space-150 { height: 150px; }

.mg-10 { margin: 10px; }
.mr-10 { margin-right: 10px; }
.mt-05 { margin-top: 5px; }
.mt-10 { margin-top: 10px; }
.mb-00 {margin-bottom :0px;}

.padB00 { padding-bottom: 0px !important; }
.padL00 { padding-left: 0px !important; }
.padR00 { padding-right: 0px !important; }

.visible-xs { display: none !important; }

@media screen and (max-width : 460px ) {
    .hidden-xs { display: none !important; }
    .visible-xs { display: block !important; }
}

.clearfix::after {
    content: "";
    clear: both;
    display: table;
}

.hide {
    display: none;
}

.text-center {
    text-align: center;
    clear: both;
}
