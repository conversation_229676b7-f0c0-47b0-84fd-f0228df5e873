var token_input = {
    init: function(elem) {
        var _this = {
            div: elem,
            tokenEntryRunning: null,
            tokenEntry: function(ev) {
                if (_this.tokenEntryRunning != null) {
                    return;
                }
                var $this = $(ev.target);
                if (ev.type == 'keydown' && !_this._tokenEntryKeyDown(ev, $this)) {
                    ev.preventDefault();
                    return false;
                }
                //No need to process TAB key and ENTER key
                if (ev.keyCode != 9 && ev.keyCode != 13) {
                    _this.tokenEntryRunning = setTimeout(function () {
                        _this._tokenEntry($this, ev);
                        _this.tokenEntryRunning = null;
                    }, 100);
                }
            },
            _tokenEntryKeyDown: function (ev, $this) {
                if (ev.metaKey || ev.ctrlKey) {
                    //Allow function keys
                    return true;
                } else if (ev.shiftKey) {
                    //Do not allow shift key
                    return false;
                }
                switch (ev.which) {
                    //Backspace
                    case 8:
                        if ($this.val() != '') {
                            $this.val('');
                            _this.collectToken();
                            break;
                        } else {
                            if ($this.data('prev') != null) {
                                $this.data('prev').val('').focus();
                                _this.collectToken();
                            }
                        }
                        //arrow left
                    case 37:
                        if ($this.data('prev') != null) {
                            $this.data('prev').focus();
                        }
                        break;
                        //arrow right
                    case 39:
                        if ($this.data('next') != null) {
                            $this.data('next').focus();
                        }
                        break;
                        //tab
                    case 9:
                        //Enter
                    case 13:
                        //Android weird code
                    case 229:
                        return true;
                        //0 - 9
                    case 48:
                    case 49:
                    case 50:
                    case 51:
                    case 52:
                    case 53:
                    case 54:
                    case 55:
                    case 56:
                    case 57:
                    //Numpad 0 - 9
                    case 96:
                    case 97:
                    case 98:
                    case 99:
                    case 100:
                    case 101:
                    case 102:
                    case 103:
                    case 104:
                    case 105:
                        $this.val('');
                        return true;
                    default:
                }
                return false;
            },
            _tokenEntry: function ($this, ev) {
                var val = $this.val();
                if (val * 1 != val) {
                    $this.val('');
                    return;
                }
                var next = $this.data('next');
                if (val.length > 1 && next != null) {
                    $this.val(val.substring(0, 1));
                    var next_val = val.substring(1);
                    _this._tokenEntry(next.val(next_val).focus(), ev);
                } else if (val.length == 0) {
                    if ($this.data('prev') != null) {
                        $this.data('prev').focus();
                    }
                } else if (!ev.metaKey && !ev.ctrlKey && next != null) {
                    next.focus();
                }
                _this.collectToken();
            },
            collectToken: function() {
                var token = '';
                elem.find('[data-token-field]').each(function() {
                    token += this.value;
                });
                elem.children('input').val(token);
            },
        };
        
        var fields = elem.find('[data-token-field]');
        var len = fields.length;
        var prev = null;
        
        fields.each(function(index) {
            var $this = jQuery(this);
            if (prev != null) {
                prev.data('next', $this);
                $this.data('prev', prev);
            }
            $this.on('keydown.token_field', _this.tokenEntry);
            $this.on('paste.token_field', _this.tokenEntry);
            prev = $this;
        });
        
        return _this;
    },
};

jQuery('[data-token-input]').each(function() {
    token_input.init(jQuery(this));
});
