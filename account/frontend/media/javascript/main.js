var REGION_SETTING_FIRST_CLICK = 0;
var CTRY_JSON, REGION_JSON, REFER;

jQuery(document).ajaxSend(function (event, request, settings) {
    if (settings.type == "POST") {
        if (settings.data == null) {
            settings.data = {};
            settings.data['CSRF_TOKEN_NAME'] = CSRF_TOKEN;
        } else if (typeof settings.data.append == 'function') {
            settings.data.append(CSRF_TOKEN_NAME, CSRF_TOKEN);
        } else {
            if (settings.data.length > 0) {
                settings.data += "&";
            }
            settings.data += CSRF_TOKEN_NAME + "=" + CSRF_TOKEN;
        }
    }
});

userBar = {
    init: function (_ctry, _cur, _lang) {
        jQuery('#reg-setting').click(function () {
            if (REGION_SETTING_FIRST_CLICK === 0) {
                REGION_SETTING_FIRST_CLICK = 1;
                jQuery.ajax({
                    type: 'GET',
                    url: REGION_STATIC_DOMAIN + '/country.json',
                    contentType: "application/json",
                    jsonpCallback: 'country',
                    dataType: 'jsonp',
                    crossDomain: true,
                    success: function (ctry_data) {
                        CTRY_JSON = ctry_data;
                        jQuery.ajax({
                            type: 'GET',
                            url: REGION_STATIC_DOMAIN + '/region.json',
                            contentType: "application/json",
                            jsonpCallback: 'region',
                            dataType: 'jsonp',
                            crossDomain: true,
                            success: function (reg_data) {
                                REFER = reg_data.refer;
                                REGION_JSON = reg_data;
                                userBar.regCtry(_ctry);
                                userBar.regLangCur(_ctry, _cur, _lang);
                                REGION_SETTING_FIRST_CLICK = 2;
                            }
                        });
                    }
                });
            } else if (REGION_SETTING_FIRST_CLICK == 2) {
                //Region data loaded
                userBar.regCtry(_ctry);
                userBar.regLangCur(_ctry, _cur, _lang);
            }
        });

        jQuery('#reg_ctry').change(function () {
            userBar.regLangCur(jQuery('#reg_ctry').val(), jQuery('#reg_cur').val(), jQuery('#reg_lang').val());
        });
    },
    regCtry: function (_ctry) {
        jQuery('#reg_ctry option').remove();
        jQuery.each(CTRY_JSON, function (code, items) {
            var _sel = "";
            if (code == _ctry) {
                _sel = " selected";
            }
            jQuery('#reg_ctry').append('<option value="' + code + '"' + _sel + '>' + items.name + '</option>');
        });
    },
    regLangCur: function (_ctry, _cur, _lang) {
        if ((typeof (_ctry) !== undefined || _ctry != null) && (typeof (_cur) != undefined || _cur != null) && (typeof (_lang) != undefined || _lang != null)) {
            country = REGION_JSON[_ctry];
            // Invalid country, get default currency and language list
            if ((typeof (country) == undefined || country == null)) {
                country = REGION_JSON['default'];
            } else {
                //no currency set for country
                if (!('currency' in country)) {
                    country['currency'] = REGION_JSON['default']['currency'];
                }

                //no language set for country
                if (!('language' in country)) {
                    country['language'] = REGION_JSON['default']['language'];
                }
            }

            jQuery.each(country, function (type, typeItems) {
                switch (type) {
                    case "currency":
                        _opt = "#reg_cur";
                        _select = _cur;
                        break;
                    case "language":
                        _opt = "#reg_lang";
                        _select = _lang;
                        break;
                }

                userBar.setLangCurDropdown(_opt, typeItems, _select, type);
            });
        }
    },
    setLangCurDropdown: function (_opt, typeItems, _select, type) {
        var _sel_opt = false;
        var defaultSelected = REGION_JSON['default'][type]['def'];

        jQuery(_opt + ' option').remove();
        jQuery.each(typeItems, function (tkey, tval) {
            if (tkey == "val") {
                jQuery.each(tval, function (id, name) {
                    var _sel = "";
                    if (id == _select) {
                        _sel = " selected";
                        _sel_opt = true;
                    }
                    jQuery(_opt).append('<option value="' + id + '"' + _sel + '>' + REFER[type][id] + '</option>');
                });
            } else if (tkey == "def" && tval != "") {
                defaultSelected = tval;
            }
        });
        if ((_sel_opt == false) || (_select == "")) {
            if (typeItems["def"] != "") {
                jQuery(_opt).val(typeItems["def"]);
            }
        }
    }
}

function ctryOpt(_ctry_id, _ctry_field, _opt) {
    jQuery.ajax({
        type: 'GET',
        url: REGION_STATIC_DOMAIN + '/country.json',
        contentType: "application/json",
        jsonpCallback: 'country',
        dataType: 'jsonp',
        crossDomain: true,
        success: function (ctry_data) {
            jQuery.each(ctry_data, function (code, items) {
                for (i = 0; i < _ctry_id.length; i++) {
                    var _val = (_opt[i] == "idd") ? items.idd : items.name;
                    var _sel = "";
                    if (_ctry_id[i] != null && items.id == _ctry_id[i]) {
                        _sel = " selected";
                    }
                    jQuery('#' + _ctry_field[i]).append('<option value="' + items.id + '"' + _sel + '>' + _val + '</option>');
                }
            });
        }
    });
}

function stateOpt(ctry_id, sel, state_field, alt_field) {
    jQuery("#" + state_field + ' option').remove();
    jQuery("#" + state_field).closest('.form-group').addClass("hide").removeClass("show");
    jQuery("#" + alt_field).addClass("hide").removeClass("show");

    if (sel != null) {
        jQuery.ajax({
            type: 'GET',
            url: REGION_STATIC_DOMAIN + '/state.json',
            contentType: "application/json",
            jsonpCallback: 'state',
            dataType: 'jsonp',
            crossDomain: true,
            success: function (state_data) {
                if (state_data[ctry_id] != null) {
                    jQuery.each(state_data[ctry_id], function (sid, sname) {
                        var _sel = "";
                        if (sel != null && sid == sel) {
                            _sel = " selected";
                        }
                        jQuery('#' + state_field).append('<option value="' + sid + '"' + _sel + '>' + sname + '</option>');
                        jQuery('#' + state_field).closest('.form-group').addClass("show").removeClass("hide");
                    });
                } else {
                    jQuery('#' + alt_field).addClass("show").removeClass("hide");
                }
            }
        });
    } else {
        jQuery('#' + alt_field).addClass("show").removeClass("hide");
    }
}

// Card Tab Login Signup
function openCity(cityName) {
    var i;
    var x = document.getElementsByClassName("tabcontent");
    for (i = 0; i < x.length; i++) {
        x[i].style.display = "none";
    }
    document.getElementById(cityName).style.display = "block";
}

jQuery(function() {
    jQuery('.btn-expand-collapse').on('click.expand-collapse', function() {
        var $this = jQuery(this);
        $this.toggleClass("active");
        var target = $this.data('target');
        var content = document.getElementById(target);
        var $content = jQuery(content);
        var $btn = $this.data('btn');
        if ($btn == null) {
            $btn = $this;
        } else {
            $btn = jQuery('#' + $btn);
        }
        if ($content.height()) {
            if ($btn.data('expandlabel') != null) {
                $btn.html($btn.data('expandlabel'));
            }
            animateHide($content);
            
            $btn.removeClass("collapsed");
        } else {
            if ($btn.data('collapselabel') != null) {
                $btn.html($btn.data('collapselabel'));
            }
            
            animateShow($content);
            
            $btn.addClass("collapsed");
            collapseAll(content);
            if ($content.hasClass('auto-collapse')) {
                jQuery(document).off('click.expand-collapse.collapse-all');
                
                //Delay add click collapse listener
                setTimeout(function() {
                    jQuery(document).on('click.expand-collapse.collapse-all', collapseAllEventListener);
                }, 100);
            }
        }
    });
});

function collapseAllEventListener(e) {
    if (jQuery(e.target).closest('.auto-collapse').length == 0) {
        jQuery(document).off('click.expand-collapse.collapse-all');
        collapseAll();
    }
}

function collapseAll(elem) {
    var coll = document.getElementsByClassName("btn-expand-collapse");
    for (var j = 0; j < coll.length; j++) {
        //So that we only match 1 btn, not both
        var $btn = $(coll[j]);
        if ($btn.data('btn') != null) {
            continue;
        }
        var target = $btn.data('target');
        var content = document.getElementById(target);
        var $content = jQuery(content);
        //If elem provided, do not collapse elem
        //Or if element exists, collapse only elements with same group
        if (elem == content || (elem != null && jQuery(elem).data('group') != $content.data('group'))) {
            continue;
        } else if (elem == null && !$content.hasClass('auto-collapse')) {
            continue;
        }
        if ($btn.hasClass('collapsed')) {
            $btn.toggleClass("active");
            if ($btn.data('expandlabel') != null) {
                $btn.html($btn.data('expandlabel'));
            }
            $content.animate({
                'height': 0,
            }, 100);
            $btn.removeClass("collapsed");
        }
    }
}

// Tooltip - When the user clicks on div, open the popup
function tooltip(id) {
    var popup = document.getElementById(id);
    jQuery(popup).toggleClass("show");
}

jQuery(function() {
    // side menu
    var $dropdowns = jQuery('.dropdown-btn');
    $dropdowns.on('click.side_menu_dropdown', function(e) {
        var $this = jQuery(this);
        var $dropdownContent = $(this.nextElementSibling);
        if ($dropdownContent.hasClass('collapsed')) {
            $this.removeClass("active");
            $dropdownContent.removeClass("collapsed");
        } else {
            $this.addClass("active");
            $dropdownContent.addClass("collapsed");
            for (var j = 0; j < $dropdowns.length; j++) {
                if (this == $dropdowns[j]) {
                    continue;
                }
                var $dropdown = jQuery($dropdowns[j]);
                var $dropdownContent = $($dropdowns[j].nextElementSibling);
                if ($dropdownContent.hasClass('collapsed')) {
                    $dropdown.removeClass("active");
                    $dropdownContent.removeClass("collapsed");
                }
            }
        }
    });
});

function activateDropdownBtn($element) {
    $element.addClass('active');
    var container = $element.closest('.dropdown-container');
    if (container.length > 0) {
        container.prev('.dropdown-btn').click();
    }
}

// side menu responsive
function openNav() {
    document.getElementById("mySidenav").style.width = "100%";
    document.getElementById("mySidenav").style.overflow = 'auto';
}

function closeNav() {
    document.getElementById("mySidenav").style.width = null;
    document.getElementById("mySidenav").style.overflow = 'hidden';
}

var custom_modal = {
    init: function(elem) {
        var trigger = elem.data('trigger') == null ? null : jQuery(elem.data('trigger'));
        var _this = {
            target: elem,
            open: function() {
                _this.target.css('display', 'block');
                jQuery(document).on('click.custom_modal.close_modal', _this.documentClickListener);
                _this.target.find('[data-dismiss=modal]').on('click.custom_modal', function() {
                    _this.close();
                });
            },
            close: function() {
                _this.target.css('display', 'none');
                jQuery(document).off('click.custom_modal.close_modal');
            },
            documentClickListener: function(e) {
                if (jQuery(e.target).closest('.modal-content').length == 0) {
                    _this.close();
                }
            }
        };
        
        if (trigger != null) {
            trigger.on('click.custom_modal', function() {
                _this.open();
            });
        }
        
        return _this;
    },
    get: function(elem) {
        if (elem.data('custom-model') == null) {
            return custom_modal.init(elem);
        }
        return elem.data('custom-model');
    }
};

function animateShow($elem) {
    $elem.animate({
        'height': $elem.prop('scrollHeight'),
    }, 100, 'swing', function() {
        jQuery(this).height('auto');
    });
}

function animateHide($elem) {
    $elem.animate({
        'height': 0,
    }, 100);
}

jQuery(function() {
    jQuery('[data-modal=true]').each(function() {
        custom_modal.init(jQuery(this));
    });
});

jQuery(function() {
    if (/iP/i.test(navigator.userAgent)) {
        //iPhone/iPad only: add cursor:pointer to jQuery to allow jQuery(document).click to trigger
        //https://stackoverflow.com/questions/3705937/document-click-not-working-correctly-on-iphone-jquery
        /iP/i.test(navigator.userAgent) && jQuery('*').css('cursor', 'pointer');
        
        //Do not auto-focus search field because on mobile, auto-focusing search field brings up the virtual keyboard which is very bulky and confuses the user
        jQuery("select").on("select2:open", function(ev) {
            jQuery('.select2-search__field').prop('focus', false);
        });
    }
});

function onlyAlphabets(evt) {
    var e = evt
    if (window.event) { // IE
        var charCode = e.keyCode;
    } else if (e.which) { // Safari 4, Firefox 3.0.4
        var charCode = e.which;
    }
    if (charCode !== 32 && (charCode < 44 || charCode > 47) && (charCode < 65 || charCode > 122)) {
        return false;
    } else {
        return true;
    }
}

ambient_captcha = function() {
    
    var api_key = null;
    
    return {
        init: function(key) {
            api_key = key;
        },
        hasInit: function() {
            return api_key != null;
        },
        execute: function(action, callback) {
            grecaptcha.ready(function() {
                grecaptcha.execute(api_key, {action: action}).then(function(token) {
                    if (callback) {
                        callback(token);
                    }
                });
            });
        },
    }
}();

function validateYiiForm($form) {
    data = $form.data("yiiActiveForm");
    jQuery.each(data.attributes, function () {
        this.status = 3;
    });
    $form.yiiActiveForm("validate");
    var error_list = [];
    jQuery.each(data.attributes, function () {
        $target = jQuery(this.container);
        if ($target.hasClass('has-error')) {
            error_list.push($target);
        }
    });
    return error_list;
}

function openChat() {
    window.fcWidget.open();
    window.fcWidget.show();
}