jQuery('#imList').change(function () {
    jQuery("#im_model_error").addClass("hide").removeClass("show");

    if (jQuery(":input[name^='im_']").length < ENTRY_IM_ACCOUNT_MAX_ENTRIES) {
        jQuery("#im_list_error").addClass("hide").removeClass("show");

        _cnt = Date.now();
        var _name = "im_" + jQuery('#imList option:selected').val() + "_" + _cnt;
        var _opt = jQuery('#imList option:selected').text().toLowerCase().replace(" ", "_");

        jQuery("#row_sample_im").clone(true).attr("id", "row_im_" + _cnt).addClass("show").removeClass("hide").insertAfter('#row_sample_im');
        jQuery("#row_im_" + _cnt + " input").addClass('im-' + _opt);
        jQuery("#row_im_" + _cnt + " #sample_im").attr("id", _name).attr('placeholder', jQuery('#imList option:selected').text());
        jQuery("#" + _name).attr("name", _name);

        jQuery('#imList').val(null);
    } else {
        jQuery("#im_list_error").addClass("show").removeClass("hide");
        jQuery('#imList').val(null);
    }
});

function removeImRow(ele) {
    jQuery(ele).closest('span').remove();
    jQuery("#im_list_error").addClass("hide").removeClass("show");
    return false;
}

function validateFourDigit(object) {
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: VALIDATE_LAST_FOUR_DIGIT,
        data: {
            last_num: object
        },
        success: function (result) {
            if (result.status === "success") {
                if (jQuery("#last_four_digit").show()) {
                    jQuery("#change_phone").show();
                    jQuery("#last_four_digit").hide();
                } else {
                    jQuery("#change_phone").hide();
                    jQuery("#last_four_digit").show();
                }
            } else {
                toastr["error"](result.message);
            }
        },
    });
    return false;
}

var changeMobileCountryFlow = {
    next_url: null,

    checkChangeMobileCountry: function(original_country_id, e) {
        var new_country_id = jQuery(e.target).val();
        jQuery(e.target).val(original_country_id).trigger('change.select2');
        if (!new_country_id) {
            return;
        }

        jQuery('#dialog-mobile-change-loading').show();
        jQuery('#dialog-mobile-change-content').hide();
        jQuery('#dialog-mobile-change').addClass('modalDialog-mobileNumChange-show');
        disableScroll();

        jQuery.ajax({
            type: 'POST',
            dataType: 'json',
            url: CHECK_CHANGE_MOBILE_COUNTRY,
            data: {
                new_country_id: new_country_id,
            },
            success: function (result) {
                changeMobileCountryFlow.resetConfirmForm();
                changeMobileCountryFlow.resetBillingForm();
                changeMobileCountryFlow.resetOtpForm();
                if (result.status === "success") {
                    jQuery('#billing_address_country_name').val(result.country_name);
                    stateOpt(new_country_id, "", "customersmobilechangeapprovalform-zone_id", "customersmobilechangeapprovalform-state");
                    jQuery('#customersmobilechangeapprovalform-country_id').val(new_country_id);
                    jQuery('#mobile_number_country_dialing_code').val(result.country_name + ' (+' + result.country_dialing_code + ')');
                    jQuery('#customersmobilechangeapprovalform-dialing_code_id').val(new_country_id);
                    if (result.skip_message) {
                        jQuery('#dialog-mobile-change').removeClass('modalDialog-mobileNumChange-show');
                        jQuery('#dialog-phone-otp-form').addClass('modalDialog-mobileNumChange-show');

                    } else {
                        jQuery('#dialog-mobile-change-primary-message').html(result.message);
                        jQuery('#dialog-mobile-change-loading').hide();
                        jQuery('#dialog-mobile-change-content').show();
                        
                    }
                    jQuery('#dialog-mobile-change').data('skip-poa', result.skip_poa);
                } else if (result.status === "fail") {
                    jQuery('#dialog-mobile-change').removeClass('modalDialog-mobileNumChange-show');
                    toastr["error"](result.message);
                    enableScroll();
                }
            },
            error: function() {
                toastr['error'](MSG_FROM_ERROR);
            },
        });
        return false;
    },
    resetConfirmForm: function() {
        jQuery("#agree_change_phone_country").prop('checked', false);
    },
    resetBillingForm: function() {
        jQuery("#frm-change-country-billing")[0].reset();
        jQuery("input[name^=doc_002]").trigger('change');
    },
    resetOtpForm: function() {
        jQuery("#frm-change-country-phone")[0].reset();
        jQuery('#customersmobilechangeapprovalform-telephone').attr('readonly', false);
        jQuery('#frm-change-country-phone button[type=submit]').removeClass('btn-submitting');
    },
    confirmChangeCountry: function() {
        if (!jQuery('#agree_change_phone_country').is(':checked')) {
            toastr["error"](ERROR_PLEASE_AGREE);
            return false; 
        }
        jQuery('#dialog-mobile-change').removeClass('modalDialog-mobileNumChange-show');
        if (jQuery('#dialog-mobile-change').data('skip-poa')) {
            jQuery('#dialog-phone-otp-form').addClass('modalDialog-mobileNumChange-show');
        } else {
            jQuery('#dialog-billing-address-form').addClass('modalDialog-mobileNumChange-show');
        }
        return false;
    },
    confirmAddressAndPoa: function() {
        var errors = validateYiiForm(jQuery('#frm-change-country-billing'));
        if (errors.length > 0) {
            //cannot proceed
        } else if (!jQuery('input[name^=doc_002]').val()) {
            toastr['error'](ERROR_PLEASE_UPLOAD_POA);
        } else {
            jQuery('#dialog-billing-address-form').removeClass('modalDialog-mobileNumChange-show');
            jQuery('#dialog-phone-otp-form').addClass('modalDialog-mobileNumChange-show');
        }
        return false;
    },
    confirmOtp: function() {
        var $form = jQuery('#frm-change-country-phone');
        var errors = validateYiiForm($form);
        if (errors.length > 0) {
            //cannot proceed
        } else if ($form.find('.card-security-token').length && $form.find('.card-security-token').data('security_question').isTokenAnswerEmpty()) {
            toastr['error'](ERROR_PLEASE_REQUEST_TOKEN);
            //cannot proceed
        } else {
            changeMobileCountryFlow.submitAllDetails();
        }
        return false;
    },
    submitAllDetails: function(){
        if (jQuery('#frm-change-country-phone button[type=submit]').hasClass('btn-submitting')) {
            return;
        }
        jQuery('#frm-change-country-phone button[type=submit]').addClass('btn-submitting');
        var formData = new FormData();
        jQuery.each(jQuery('#frm-change-country-billing').serializeArray(), function(i, field) {
            formData.append(field.name, field.value);
        });
        jQuery.each(jQuery('#frm-change-country-phone').serializeArray(), function(i, field) {
            formData.append(field.name, field.value);
        });
        formData.append('doc_002', jQuery('input[name^=doc_002]')[0].files[0]);

        jQuery.ajax({
            type: 'POST',
            url: CHANGE_MOBILE_COUNTRY,
            data: formData,
            dataType: 'json',
            processData: false,  // tell jQuery not to process the data
            contentType: false,  // tell jQuery not to set contentType
            success: function (result) {
                if (result.status === "success") {
                    if (jQuery('#dialog-mobile-change').data('skip-poa')) {
                        jQuery('#dialog-change-submitted-successfully').find('#change_successful').show();
                        jQuery('#dialog-change-submitted-successfully').find('#approval_pending').hide();
                    } else {
                        jQuery('#dialog-change-submitted-successfully').find('#approval_pending').show();
                        jQuery('#dialog-change-submitted-successfully').find('#change_successful').hide();
                    }
                    jQuery('#dialog-phone-otp-form').removeClass('modalDialog-mobileNumChange-show');
                    jQuery('#dialog-change-submitted-successfully').addClass('modalDialog-mobileNumChange-show');
                    if (result.next_url) {
                        changeMobileCountryFlow.next_url = result.next_url;
                    }
                } else if (result.status == "failed") {
                    if (result.refresh) {
                        window.location.reload();
                    }
                    if (result.message) {
                        toastr['error'](result.message);
                    }
                    if (result.upload_issue) {
                        jQuery('#dialog-phone-otp-form').removeClass('modalDialog-mobileNumChange-show');
                        jQuery('#dialog-billing-address-form').addClass('modalDialog-mobileNumChange-show');
                        jQuery('input[name^=doc_002]').val(null);
                        jQuery("input[name^=doc_002]").trigger('change');
                    }
                    jQuery('#frm-change-country-phone button[type=submit]').removeClass('btn-submitting');
                }
            },
            error: function() {
                toastr['error'](MSG_FROM_ERROR);
                jQuery('#frm-change-country-phone button[type=submit]').removeClass('btn-submitting');
            },
        });
    
        return false;
    },
    endFlow: function(elem) {
        enableScroll();
        jQuery(elem).parents('.modalDialog-mobileNumChange').removeClass('modalDialog-mobileNumChange-show');
        if (changeMobileCountryFlow.next_url) {
            if (changeMobileCountryFlow.next_url == 'refresh') {
                window.location.reload();
            } else {
                window.location.href = changeMobileCountryFlow.next_url;
            }
            changeMobileCountryFlow.next_url = null;
        }
    },
};

jQuery('#customersmobilechangeapprovalform-telephone').change(function(){
    form_value = jQuery('#customersmobilechangeapprovalform-telephone').val();
    let isnum = /^\d+$/.test(form_value);
    if(isnum == false){
        jQuery('#customersmobilechangeapprovalform-telephone').val('');
    }
});

jQuery('#customersmobilechangeapprovalform-telephone').blur(function(){
    form_value = jQuery('#customersmobilechangeapprovalform-telephone').val();
    let isnum = /^\d+$/.test(form_value);
    if(isnum == false){
        jQuery('#customersmobilechangeapprovalform-telephone').val('');
    }
});

jQuery('#updatephoneform-customers_telephone').blur(function(){
    form_value = jQuery('#updatephoneform-customers_telephone').val();
    let isnum = /^\d+$/.test(form_value);
    if(isnum == false){
        jQuery('#updatephoneform-customers_telephone').val('');
    }
});

function changePhoneTwofa(object) {
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: VALIDATE_CHANGE_NUMBER_TWOFA,
        data: {
            code: object
        },
        success: function (result) {
            if (result.status === "success") {
                if (jQuery("#two_fa").show()) {
                    jQuery("#change_phone").show();
                    jQuery("#two_fa").hide();
                } else {
                    jQuery("#change_phone").hide();
                    jQuery("#two_fa").show();
                }
            } else {
                toastr["error"](result.message);
            }
        },
    });
    return false;
}

function disableScroll() {
    document.body.classList.add("remove-scrolling");
}

function enableScroll() {
    document.body.classList.remove("remove-scrolling");
}
