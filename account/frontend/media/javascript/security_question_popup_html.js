var security_question = {
    init: function(elem) {
        var _this = {
            div: elem,
            send_button_div: elem.find('.btn-send-token'),
            request_type: elem.data('request-type'),
            service_id: elem.data('service-id'),
            contact_no_dialing_code_id_field: null,
            contact_no_field: null,
            token_answer_field: null,
            triggerSmsToken: function(requestType) {
                var package = {
                    requestType: requestType,
                    service_id: _this.service_id,
                };
                var fn = function() {
                    _this.send_button_div.find('button').attr('disabled', true);
                    var fn2 = function(token) {
                        if (token) {
                            package['g-recaptcha-response-v3'] = token;
                        }
                        _this.div.find('.card-security-captcha').find('textarea,input').each(function() {
                            var $this = jQuery(this);
                            package[$this.attr('name')] = $this.val();
                        });
                        jQuery.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: SMS_TOKEN_URL,
                            data: package,
                            success: function (data) {
                                _this.div.find("#input-six-box").show();
                                _this.div.find("#note-memo").show();
                                _this.div.find("#request-timer").show;
                                
                                _this.onTriggerSmsToken(data, requestType);
                            },
                            error: function(jqXHR, status, error) {
                                if (jqXHR.status < 500) {
                                    //Session / csrf-related - refresh browser
                                    document.location.reload();
                                } else {
                                    toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                                }
                            },
                        });
                    }
                    if (typeof ambient_captcha !== 'undefined' && ambient_captcha.hasInit()) {
                        ambient_captcha.execute('sms_request', fn2);
                    } else {
                        fn2();
                    }
                };
                if (_this.contact_no_dialing_code_id_field && _this.contact_no_field) {
                    var form = _this.contact_no_dialing_code_id_field.closest('form');
                    form.on('afterValidateAttribute.trigger_sms_token', function(event, attribute, messages) {
                        form.off('afterValidateAttribute.trigger_sms_token');
                        if (messages.length) {
                            return;
                        }
                        package.country = _this.contact_no_dialing_code_id_field.val();
                        package.phone = _this.contact_no_field.val();
                        _this.contact_no_dialing_code_id_field.find('option:not(:checked)').prop('disabled', true);
                        _this.contact_no_field.prop('readonly', true);
                        fn();
                    });
                    form.yiiActiveForm('validateAttribute', _this.contact_no_field.attr('id'));
                } else {
                    fn();
                }
            },
            onTriggerSmsToken: function(data, requestType) {
                if (data.resCode == -1) {
                    _this.send_button_div.find('button').attr('disabled', null)
                    _this.div.find('.card-captcha').html(data['captcha_html']);
                    _this.div.find('.card-security-content').fadeOut('fast', function() {
                        _this.div.find('.card-security-captcha').fadeIn();
                        eval(data['captcha_js']);
                    });
                } else if (data.resCode != 0) {
                    if (_this.div.find('.card-security-content').is(':hidden')) {
                        _this.div.find('.card-security-captcha').fadeOut('fast', function() {
                            _this.div.find('.card-security-content').fadeIn();
                        });
                    }
                    _this.send_button_div.find('button').off('click.security_question');
                    toastr["success"](data.message);
                    _this.refreshResendButton(data.secondsTillResend, requestType);
                } else {
                    if (_this.div.find('.card-security-content').is(':hidden')) {
                        _this.div.find('.card-security-captcha').fadeOut('fast', function() {
                            _this.div.find('.card-security-content').fadeIn();
                        });
                    }
                    toastr["error"](data.message);
                    if (_this.contact_no_dialing_code_id_field && _this.contact_no_field) {
                        _this.contact_no_dialing_code_id_field.find('option').prop('disabled', null);
                        _this.contact_no_field.prop('readonly', null);
                        _this.send_button_div.find('button').attr('disabled', null);
                    }
                }
            },
            refreshResendButton: function(seconds, requestType) {
                if (seconds == null || isNaN(seconds)) {
                    return;
                }
                if (seconds > 0) {
                    if (!_this.send_button_div.find('button').hasClass('hide')) {
                        _this.send_button_div.find('button').addClass('hide');
                    }
                    _this.send_button_div.find('p').html(TEXT_RESEND_IN_X_SEC.replace("{0}", seconds));

                    window.setTimeout(function() {
                        _this.refreshResendButton(seconds - 1, requestType);
                    }, 1000);
                } else {
                    _this.send_button_div.find('p').html('');
                    _this.send_button_div.find('button').attr('disabled', null).removeClass('hide').on('click.security_question', function() {_this.triggerResendSmsToken(requestType);}).find('.btn-text').html(LINK_RESEND);
                }
            },
            triggerResendSmsToken: function(requestType) {
                var package = {
                    requestType: requestType,
                    service_id: _this.service_id,
                };
                var fn = function() {
                    _this.send_button_div.find('button').attr('disabled', true);
                    var fn2 = function(token) {
                        if (token) {
                            package['g-recaptcha-response-v3'] = token;
                        }
                        _this.div.find('.card-security-captcha').find('textarea,input').each(function() {
                            var $this = jQuery(this);
                            package[$this.attr('name')] = $this.val();
                        });
                        jQuery.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: SMS_RESEND_TOKEN_URL,
                            data: package,
                            success: function(data) {
                                _this.onTriggerSmsToken(data, requestType);
                            },
                            error: function(jqXHR, status, error) {
                                if (jqXHR.status < 500) {
                                    //Session / csrf-related - refresh browser
                                    document.location.reload();
                                } else {
                                    toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                                }
                            },
                        });
                    }
                    if (typeof ambient_captcha !== 'undefined' && ambient_captcha.hasInit()) {
                        ambient_captcha.execute('sms_resend', fn2);
                    } else {
                        fn2();
                    }
                }
                if (_this.contact_no_dialing_code_id_field && _this.contact_no_field) {
                    var form = _this.contact_no_dialing_code_id_field.closest('form');
                    form.on('afterValidateAttribute.trigger_sms_token', function(event, attribute, messages) {
                        form.off('afterValidateAttribute.trigger_sms_token');
                        if (messages.length) {
                            return;
                        }
                        package.country = _this.contact_no_dialing_code_id_field.val();
                        package.phone = _this.contact_no_field.val();
                        _this.contact_no_dialing_code_id_field.find('option:not(:checked)').prop('disabled', true);
                        _this.contact_no_field.prop('readonly', true);
                        fn();
                    });
                    form.yiiActiveForm('validateAttribute', _this.contact_no_field.attr('id'));
                } else {
                    fn();
                }
            },
            resendTokenView: function() {
                jQuery.ajax({
                    type: 'POST',
                    dataType: 'json',
                    url: VIEW_EMAIL_TOKEN,
                    success: function(data) {
                        jQuery('.custom-modal').html(data.content).find('button').on('click.security_question', function() {
                            _this.resendToken();
                        });
                        custom_modal.get(jQuery('.custom-modal')).open();
                    },
                    error: function(jqXHR, status, error) {
                        if (jqXHR.status < 500) {
                            //Session / csrf-related - refresh browser
                            document.location.reload();
                        } else {
                            toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                        }
                    },
                });
            },
            resendToken: function() {
                var lastDigit = jQuery('.custom-modal .form-group-last-4 input[type=text]').val();

                jQuery.ajax({
                    type: 'POST',
                    dataType: 'json',
                    url: RESEND_EMAIL_TOKEN,
                    data: {
                        'lastDigit': lastDigit,
                    },
                    success: function(data) {
                        if (data.status == 0) {
                            jQuery('.custom-modal .form-group-last-4').addClass('has-error').find('.help-block').html(data.message);
                        } else if (data.status == 1) {
                            toastr["success"](data.message);
                            custom_modal.get(jQuery('.custom-modal')).close();
                        } else if (data.status == 3) {
                            window.location.href = data.message;
                        } else {
                            toastr["error"](data.message);
                            custom_modal.get(jQuery('.custom-modal')).close();
                        }
                    },
                    error: function(jqXHR, status, error) {
                        if (jqXHR.status < 500) {
                            //Session / csrf-related - refresh browser
                            document.location.reload();
                        } else {
                            toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                        }
                    },
                });
            },
            isTokenAnswerEmpty: function() {
                return _this.token_answer_field.val().trim() == '';
            },
        };
        elem.data('security_question', _this);
        
        var field;
        if (field = elem.data('contact_no_dialing_code_id_field')) {
            _this.contact_no_dialing_code_id_field = jQuery('#' + field);
        }
        if (field = elem.data('contact_no_field')) {
            _this.contact_no_field = jQuery('#' + field);
        }
        if (field = elem.data('token-answer-field-name')) {
            _this.token_answer_field = _this.div.find('[name=' + field + ']');
        }
        _this.send_button_div.find('button').on('click.security_question', function() {
            _this.triggerSmsToken(_this.request_type);
        });
        
        _this.div.find('.link_resend_token').on('click.security_question', function() {
            _this.resendTokenView();
        });
        
        return _this;
    },
};

jQuery(function() {
    jQuery('[data-security-token=true]').each(function() {
        security_question.init(jQuery(this));
    });
});

function onlyNumbers(evt) {
    var e = evt
    if (window.event) { // IE
        var charCode = e.keyCode;
    } else if (e.which) { // Safari 4, Firefox 3.0.4
        var charCode = e.which;
    }
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    } else {
        return true;
    }
}
