var store_credit = {};

store_credit.topupInit = function(db, cur_code) {
    var _this = {
        init: function() {
            jQuery("#customcurcode").change(function() {
                _this.renderSelections(jQuery(this).val());
            });
            jQuery(".btn-storecredits").click(function() {
                _this.selectAmount(jQuery(this));
            });
            _this.renderSelections(cur_code);
        },
        renderSelections: function(select_code) {
            cur_code = select_code;
            var data = db[cur_code];
            var $topup_section = jQuery('#input-topup-amount');
            var $form = jQuery('#frm-topup');
            //Revert all selections first
            $topup_section.find('.input-row-3-coll-storecredits:not(.sc-custom)').remove();
            $topup_section.find('.btn-storecredits').removeClass('btn-storecredits-selected')
            //Set qty to empty first
            jQuery('#customqty').data('min_topup', data['min_topup']).data('max_topup', data['max_topup']).val('');
            
            //Render custom qty section
            jQuery('#min_topup_currency').html(data['name']);
            jQuery('#min_topup_amount').html(data['symbol_left'] + ' ' + data['min_topup_display'] + ' ' + data['symbol_right']);
            jQuery('#max_topup_currency').html(data['name']);
            jQuery('#max_topup_amount').html(data['symbol_left'] + ' ' + data['max_topup_display'] + ' ' + data['symbol_right']);
            
            //Hide custom amount
            jQuery("#custom-amount").height(0);
            
            //Reset errors
            $form.yiiActiveForm('resetForm');
            
            //Add all custom deno
            var attach_topup_value = function(data, value) {
                return jQuery('<div>')
                    .addClass('input-row-3-coll-storecredits')
                    .html(jQuery('<button>')
                        .attr('type', 'button')
                        .addClass('btn-storecredits')
                        .data('value', value)
                        .click(function() {
                            _this.selectAmount(jQuery(this));
                        })
                        .html(data['symbol_left'] + ' ' + value + ' ' + data['symbol_right'])
                    );
            };
            
            var topup_values = [];
            if (data['deno'] == null) {
                topup_values.push(data, data['min_topup']);
            } else {
                for(var index in data['deno']) {
                    topup_values.push(attach_topup_value(data, data['deno'][index]));
                }
            }
            $topup_section.find('.sc-custom').before(topup_values);
        },
        selectAmount: function($target) {
            if ($target.hasClass('btn-storecredits-selected')) {
                //No need double process
                return;
            }
            var val = $target.data('value');
            jQuery('#input-topup-amount .btn-storecredits').removeClass('btn-storecredits-selected');
            $target.addClass('btn-storecredits-selected');
            var $content = jQuery('#custom-amount');
            if (val == 0) {
                //Custom value
                val = jQuery('#customqty').data('min_topup');
                animateShow($content);
            } else {
                animateHide($content);
            }
            jQuery('#customqty').val(val);
        },
    };
    _this.init();
};

store_credit.convertCurrencyInit = function() {
    jQuery("select[name=currency]").change(function() {
        var content = document.getElementById('convert-detail');
        var $this = jQuery(this);
        if ($this.val() == '') {
            animateHide(jQuery(content));
            return;
        } else {
            jQuery('#convert-detail-result').addClass('hide');
            jQuery('#convert-in-progress').removeClass('hide');
            animateShow(jQuery(content));
        }
        jQuery.ajax({
            type: "post",
            dataType: 'json',
            url: $this.data('calculation-url'),
            data: {
                currency: $this.val(),
            },
            success: function(data) {
                if (data.f_error == false) {
                    if ((data.result.new_balance != undefined) && (data.result.from_cur_rate != undefined) && (data.result.to_cur_rate != undefined)) {
                        jQuery('#convert-detail-result-value').html(data.result.new_balance);
                        jQuery('#convert-detail-result-rate').html('<i class="fa fa-info-circle"></i> (' + data.result.from_cur_rate + ' = ' + data.result.to_cur_rate + ')');
                        jQuery('#convert-detail-result').removeClass('hide');
                        jQuery('#convert-in-progress').addClass('hide');
                    }
                } else {
                    toastr['error'](data.f_message);
                }
            },
            error: function(jqXHR, status, error) {
                if (jqXHR.status < 500) {
                    //Session / csrf-related - refresh browser
                    document.location.reload();
                } else {
                    toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                }
            },
        });
    });
};

store_credit.redeemGiftCardInit = function() {
    var _this = {
        submitting: false,
        $form: jQuery('#frm-redeem-gift-card'),
        init: function() {
            _this.$form.on('beforeSubmit', function(e) {
                if (_this.submitting) {
                    return false;
                }
                _this.submitting = true;

                var $clicked_button = _this.$form.find('button:submit[clicked=true]');
                if ($clicked_button.length > 0 && $clicked_button.val() == 'rd_next') {
                    return _this.validatePin($clicked_button);
                }
                return true;
            });
            _this.$form.find('button:submit').click(function() {
                var $this = $(this);
                $this.closest('form').find('button:submit').attr('clicked', null);
                $this.attr('clicked', 'true');
            });
            _this.$form.find('#btn-cancel-redeem').click(function() {
                _this.hideDetailArea(true);
            });
            _this.$form.find('#customcurcode').change(function() {
                _this.$form.find('#btn-validate-pin').click();
            });
        },
        validatePin: function($btn) {
            var rd_code = _this.$form.find('#redeemgiftcardform-rd_code').val();
            var rd_serial = _this.$form.find('#redeemgiftcardform-rd_serial').val();
            var rd_pin = _this.$form.find('#redeemgiftcardform-rd_pin').val();
            var rd_curr = _this.$form.find('[name=customcurcode]').val();
            
            _this.showDetailArea();
            
            jQuery.ajax({
                type: "POST",
                dataType: "json",
                url: $btn.data('validate-pin-url'),
                data: "rd_serial=" + rd_serial + "&rd_pin=" + rd_pin + "&rd_curr=" + rd_curr + "&action=rd_next&rd_code=" + rd_code,
                success: function(data) {
                    _this.submitting = false;
                    if (data.status) {
                        jQuery('#redeem-detail-result').removeClass('hide');
                        jQuery('#redeem-in-progress').addClass('hide');
                        
                        jQuery('#redeem-detail-result-gc-value').html(data.result.gc_value);
                        jQuery('#redeem-detail-result-rate').html(data.result.from_cur != data.result.to_cur ? '<i class="fa fa-info-circle"></i> (' + data.result.from_rate + ' = ' + data.result.to_rate + ')' : '');
                        jQuery('#redeem-detail-result-sc-value').html(data.result.topup_value);
                    } else {
                        _this.hideDetailArea(false);
                        if (data.link) {
                            window.top.location.href = data.link;
                        } else if (data.error) {
                            _this.$form.find('#redeemgiftcardform-rd_serial, #redeemgiftcardform-rd_pin').on('blur.custom', function() {
                                jQuery(this).off('blur.custom').closest('.form-group').find('.input-row-error').css('display', '');
                            }).closest('.form-group').removeClass('has-success').addClass('has-error').find('.input-row-error').css('display', 'none');
                            toastr['error'](data.error);
                        }

                    }
                },
                error: function(jqXHR, status, error) {
                    if (jqXHR.status < 500) {
                        //Session / csrf-related - refresh browser
                        document.location.reload();
                    } else {
                        toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                    }
                },
            });
            return false;
        },
        showDetailArea: function() {
            var $rd_serial = _this.$form.find('#redeemgiftcardform-rd_serial');
            var $rd_pin = _this.$form.find('#redeemgiftcardform-rd_pin');
            var $submit_btn = _this.$form.find('button[value=rd_next]');
            
            $rd_serial.attr('readonly', true);
            $rd_pin.attr('readonly', true);
            $submit_btn.addClass('hide');
            
            var content = document.getElementById('redeem-detail');
            jQuery('#redeem-detail-result').addClass('hide');
            jQuery('#redeem-in-progress').removeClass('hide');
            animateShow(jQuery('#redeem-detail'));
        },
        hideDetailArea: function(empty_val) {
            var $rd_serial = _this.$form.find('#redeemgiftcardform-rd_serial');
            var $rd_pin = _this.$form.find('#redeemgiftcardform-rd_pin');
            var $submit_btn = _this.$form.find('button[value=rd_next]');
            
            $rd_serial.attr('readonly', null);
            $rd_pin.attr('readonly', null);
            if (empty_val) {
                $rd_serial.val('');
                $rd_pin.val('');
            }
            $submit_btn.removeClass('hide');
            
            animateHide(jQuery('#redeem-detail'));
        },
    };
    _this.init();
};