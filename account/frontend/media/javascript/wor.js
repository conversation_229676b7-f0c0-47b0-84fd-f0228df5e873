var wor = {};
wor.init = function(cur_code, redeem_into_url) {
    wor.redeem_into_url = redeem_into_url;
    jQuery("select[name=reg_cur]").change(function(){
        wor.calculateRedeem(jQuery(this).val());
    });
    wor.calculateRedeem(cur_code);
}
wor.calculateRedeem = function(cur_code) {
    jQuery('#redeem-detail-result').addClass('hide');
    jQuery('#redeem-in-progress').removeClass('hide');
    animateShow(jQuery('#redeem-detail'));

    jQuery.ajax({
        type : 'post',
        datatype: 'json',
        url : wor.redeem_into_url,
        data:{
            reg_cur : cur_code,
        },
        success:function(data){
            var result = JSON.parse(data);
            if((result.result.amount != undefined)){
                jQuery('#redeem-detail-result').removeClass('hide');
                jQuery('#store-credit-display').html(result.result.amount);
            }
            else{
                toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
            }
            jQuery('#redeem-in-progress').addClass('hide');
        },
        error: function(jqXHR, status, error) {
            if (jqXHR.status < 500) {
                //Session / csrf-related - refresh browser
                document.location.reload();
            } else {
                jQuery('#redeem-in-progress').addClass('hide');
                toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
            }
        },
    });
}


//jQuery(document).ready(function(){
//    
//    jQuery("select[name=reg_cur]").change(function(){
//    });
//    jQuery("select[name=reg_cur]").change(); 
//});

function viewPage(page, controller) {
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: HOME_URL + '/' + controller,
        data: {
            'YII_CSRF_TOKEN':jQuery('input[name=YII_CSRF_TOKEN]').val(),
            'page': page
        },
        success: function(data) {
            jQuery("#redeemHistory").html(data.content);
        }
    });
}
