<?php

namespace frontend\models;

use common\models\AddressBook;
use common\models\Configuration;
use Yii;
use yii\base\Model;

class AddressForm extends Model
{
    public $entry_street_address, $entry_suburb,
            $entry_city, $entry_postcode, 
            $entry_state, $entry_zone_id,
            $entry_country_id;
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entry_street_address', 'entry_suburb'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_STREET_ADDRESS_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ADDRESS', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_STREET_ADDRESS_MIN_LENGTH')])],
            [['entry_city'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_CITY_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_CITY', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_CITY_MIN_LENGTH')])],
            [['entry_postcode'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_POSTCODE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ERR_POSTCODE', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_POSTCODE_MIN_LENGTH')])],
            [['entry_state'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_STATE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_STATE_ERROR', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_STATE_MIN_LENGTH')])],
            [['entry_suburb', 'entry_city', 'entry_state'], 'string', 'encoding' => 'latin1', 'max' => 32],
            [['entry_street_address'], 'string', 'encoding' => 'latin1', 'max' => 64],
            [['entry_postcode'], 'string', 'encoding' => 'latin1', 'max' => 10],
            [['entry_country_id', 'entry_zone_id'], 'integer'],
        ];
    }
    
    public function attributeLabels() {
        return [
            'entry_street_address' => 'Entry Street Address',
            'entry_suburb' => 'Entry Suburb',
            'entry_postcode' => 'Entry Postcode',
            'entry_city' => 'Entry City',
            'entry_state' => 'Entry State',
            'entry_country_id' => 'Entry Country',
        ];
    }
    
    /**
     * @param AddressBook $model
     * @return static
     */
    public static function fromModel($model) {
        if (!isset($model)) {
            return null;
        }
        $form = new static();
        foreach ($model->attributes as $attr => $value) {
            if ($form->hasProperty($attr)) {
                $form->$attr = $value;
            }
        }
        if (empty($form->entry_country_id)) {
            $form->entry_country_id = Yii::$app->session["ctry_id"];
        }
        return $form;
    }
}
