<?php

namespace frontend\models;

use common\models\AddressBook;
use common\models\Configuration;
use Yii;
use yii\base\Model;

class CustomersMobileChangeApprovalForm extends Model
{
    public $customers_id, $street_address,$suburb,
            $postcode, $city,$state, $country_id,$token_answer,
            $zone_id, $dialing_code_id, $telephone,$approval_status;
            // ,$aprroved_status_updated_by,$aprroved_status_datetime;
    const STATUS_PENDING = '1',
        STATUS_APPROVED = '2',
        STATUS_REJECTED = '3';
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['street_address', 'postcode', 'city'], 'required', 'on' => ['poa_required'], 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['telephone'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['street_address', 'suburb'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_STREET_ADDRESS_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ADDRESS', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_STREET_ADDRESS_MIN_LENGTH')])],
            [['city'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_CITY_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_CITY', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_CITY_MIN_LENGTH')])],
            [['postcode'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_POSTCODE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ERR_POSTCODE', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_POSTCODE_MIN_LENGTH')])],
            [['state'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_STATE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_STATE_ERROR', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_STATE_MIN_LENGTH')])],
            [['country_id', 'zone_id','approval_status'], 'integer'],
            [['dialing_code_id', 'telephone'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['telephone'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ENTRY_CONTACT_NUMBER_ERROR', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH')])],
            [['state'], 'required', 'when' => function($model) { return empty($model->zone_id); }, 'whenClient' => "function (attribute, value) {
                return jQuery('#" . strtolower($this->formName()) . "-zone_id').val() == null;
            }", 'on' => ['poa_required'], 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['zone_id'], 'required', 'when' => function($model) { return empty($model->state); }, 'whenClient' => "function (attribute, value) {
                return jQuery('#" . strtolower($this->formName()) . "-state').val() == '';
            }", 'on' => ['poa_required'], 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],       
            [['suburb', 'city', 'state'], 'string', 'encoding' => 'latin1', 'max' => 32],
            [['street_address'], 'string', 'encoding' => 'latin1', 'max' => 64],
            [['postcode'], 'string', 'encoding' => 'latin1', 'max' => 10],
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_id' => 'Customers',
            'approval_status' => 'Approval status',
            'dialing_code_id' => 'Dialing Code ID',
            'country_id' => 'Country ID',
            'zone_id' => 'Zone ID',
            'suburb' => 'Suburb',
            'city' => 'City',
            'state' => 'State',
            'telephone' => 'Telephone',
            'street_address' => 'Street Address',
            'postcode' => 'Postcode',
            'approval_status' => 'Approval Status',
            // 'created_datetime' => 'Created Datetime',
            // 'updated_datetime' => 'Updated Datetime',
            // 'aprroved_status_datetime' => 'Approved Datetime',
        ];
    }
    
    /**
     * @param CustomersMobileChangeApproval $model
     * @return static
     */
    public static function fromModel($model) {
        if (!isset($model)) {
            return null;
        }
        $form = new static();
        foreach ($model->attributes as $attr => $value) {
            if ($form->hasProperty($attr)) {
                $form->$attr = $value;
            }
        }
        if (empty($form->country_id)) {
            $form->country_id = Yii::$app->session["ctry_id"];
        }
        return $form;
    }
}
