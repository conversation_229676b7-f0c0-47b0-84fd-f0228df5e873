<?php

namespace frontend\models;

class CustomersSetupForm extends \yii\base\Model {
    
    public $customers_firstname;
    public $customers_lastname;
    public $customers_telephone;
    public $customers_country_dialing_code_id;
    
    public $require_mobile = false;
    
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_firstname'], 'required','message' => \Yii::t('sso', 'HINT_MESSAGE_FIRST_NAME')],
            [['customers_telephone'], 'required', 'when' => function() { return $this->require_mobile;}],
            [['customers_country_dialing_code_id'], 'integer'],
            [['customers_firstname', 'customers_lastname', 'customers_telephone'], 'string', 'max' => 32],
            
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_firstname' => \Yii::t('sso', 'ENTRY_FIRST_NAME'),
            'customers_lastname' => \Yii::t('sso', 'ENTRY_LAST_NAME'),
            'customers_telephone' => \Yii::t('sso', 'TEXT_MOBILE_NUMBER'),
        ];
    }

}