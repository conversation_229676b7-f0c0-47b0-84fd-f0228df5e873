<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class ForgetForm extends Model {

    public $email;
    public $dial_ctry_code, $phone;
    
    public function rules() {
        return [
            // forget password
            [['email'], 'email', 'message' => Yii::t('general', 'ERROR_EMAIL_FORMAT'), 'on' => ['forget_password']],
            [['email'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD'), 'on' => ['forget_password']],
            // forget username
            [['phone'], 'number', 'on' => ['forget_email']],
            [['dial_ctry_code', 'phone'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD'), 'on' => ['forget_email']],
        ];
    }

}
