<?php

namespace frontend\models;

use common\components\Password;
use common\models\Customers;
use Yii;
use yii\base\Model;

class PasswordConfirmationForm extends Model {
    
    public $password;
    
    public function rules() {
        return [
            [['password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['password'], 'validatePassword'],
        ];
    }

    public function validatePassword($attr) {
        if (!Password::validatePassword($this->$attr, Customers::model()->customerPassword())) {
            $this->addError($attr, Yii::t('general', 'ERROR_MSG_INCORRECT_LOGIN_PASSWORD'));
        }
    }
}
