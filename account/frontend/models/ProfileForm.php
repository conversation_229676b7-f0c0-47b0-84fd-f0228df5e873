<?php

namespace frontend\models;

use common\models\Configuration;
use common\models\Customers;
use Yii;
use yii\base\Model;

class ProfileForm extends Model
{
    public $phone;
    
    public $customers_email_address;
    public $customers_firstname, $customers_lastname, $customers_gender;
    public $customers_dob, $dobMonth, $dobYear, $dobDay;
    public $customers_country_dialing_code_id, $customers_telephone;

    public $einvoice_type, $identity_number, $nric_1, $nric_2, $nric_3;
    public $company_name, $company_reg_number, $tax_reg_number, $tax_country_code;
    
    //Holder for error message only
    //public $imList;
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_firstname'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['customers_firstname'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_FIRST_NAME_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ERR_MINIMUM_FIRSTNAME', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_FIRST_NAME_MIN_LENGTH')])],
            [['customers_lastname'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_LAST_NAME_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ERR_MINIMUM_LASTNAME', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_LAST_NAME_MIN_LENGTH')])],
            [['customers_country_dialing_code_id', 'customers_telephone'], 'integer'],
            [['customers_telephone'], 'string', 'min'  => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ENTRY_CONTACT_NUMBER_ERROR', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH')])],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname'], 'string', 'encoding' => 'latin1', 'max' => 32],
            [['customers_telephone'], 'string', 'max' => 32],
            [['dobYear', 'dobMonth', 'dobDay'], 'safe'],
            [['customers_dob'], 'checkDate', 'message' => Yii::t('profile', 'MSG_ERR_BIRTHDAY')],

            // Customer Profile for einvoice
            [['einvoice_type'], 'string', 'max' => 50],
            [['einvoice_type'], 'in', 'range' => ['personal', 'business']],
            [['company_name'], 'string', 'max' => 255],
            [['company_reg_number', 'tax_reg_number'], 'string', 'max' => 100],
            [['tax_country_code'], 'string', 'max' => 2],
            [['tax_country_code'], 'default', 'value' => 'MY'],
            
            // Business fields required when e-invoice type is business
            [['company_name', 'company_reg_number', 'tax_reg_number'], 'required', 'when' => function($model) {
                return $model->einvoice_type === 'business';
            }, 'whenClient' => "function (attribute, value) {
                return $('#einvoice-type-input').val() === 'business';
            }"],
            
            // NRIC validations
            [['nric_1'], 'string', 'max' => 6, 'min' => 6],
            [['nric_2'], 'string', 'max' => 2, 'min' => 2],
            [['nric_3'], 'string', 'max' => 4, 'min' => 4],
            [['nric_1', 'nric_2', 'nric_3'], 'match', 'pattern' => '/^\d+$/', 'message' => Yii::t('profile', 'ERROR_INVALID_NID')],
            [['identity_number'], 'validateMalaysianIC'],
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_gender' => 'Customers Gender',
            'dobDay' => 'dobDay',
            'dobYear' => 'dobYear',
            'dobMonth' => 'dobMonth',
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'einvoice_type' => 'E-Invoice Type',
            'identity_number' => 'National ID Number',
            'company_name' => 'Company Name',
            'company_reg_number' => 'Company Registration Number',
            'tax_reg_number' => 'Tax Identification Number',
            'tax_country_code' => 'Tax Country Code',
        ];
    }
    
    
    /**
     * Split NRIC into parts (from 12-character string to 3 parts)
     */
    public function splitNric()
    {
        if (!empty($this->identity_number) && strlen($this->identity_number) === 12) {
            $this->nric_1 = substr($this->identity_number, 0, 6);
            $this->nric_2 = substr($this->identity_number, 6, 2);
            $this->nric_3 = substr($this->identity_number, 8, 4);
        }
    }
    
    public function checkDate($attribute, $params) {
        if (!isset($params['message'])) {
            $params['message'] = 'Invalid Date';
        }
        if (!$this->hasErrors($attribute)) {
            if ($this->{$attribute} != "") {
                $splitDateTime = explode(" ", $this->{$attribute});
                $splitDate = explode("-", $splitDateTime[0]);
                $dobMonth = (isset($splitDate[1]) ? (int) $splitDate[1] : 0);
                $dobYear = (isset($splitDate[0]) ? (int) $splitDate[0] : 0);
                $dobDay = (isset($splitDate[2]) ? (int) $splitDate[2] : 0);

                if ($dobDay != 0 || $dobMonth != 0 || $dobYear != 0) {
                    if (!checkdate($dobMonth, $dobDay, $dobYear) || mktime(0, 0, 0, $dobMonth, $dobDay, $dobYear) > time())
                        $this->addError($attribute, $params['message']);
                }
            }
        }
    }
    
    /**
     * Validate Malaysian IC number
     */
    public function validateMalaysianIC($attribute)
    {
        if (!empty($this->{$attribute})) {
            $ic = $this->{$attribute};
            
            // Must be exactly 12 digits
            if (!preg_match('/^\d{12}$/', $ic)) {
                $this->addError($attribute, Yii::t('profile', 'ERROR_INVALID_NID'));
                return false;
            }
            
            $dob = substr($ic, 0, 6);
            $stateCode = substr($ic, 6, 2);
            
            // Check date of birth
            $year = (int)substr($dob, 0, 2);
            $month = (int)substr($dob, 2, 2);
            $day = (int)substr($dob, 4, 2);
            
            $currentYear = (int)date('Y');
            $fullYear = $year <= ($currentYear % 100) 
                ? $currentYear - ($currentYear % 100) + $year 
                : 1900 + $year;
            
            if (!checkdate($month, $day, $fullYear)) {
                $this->addError($attribute, Yii::t('profile', 'ERROR_INVALID_NID'));
                return false;
            }
            
            // Check if date is in the future
            if (mktime(0, 0, 0, $month, $day, $fullYear) > time()) {
                $this->addError($attribute, Yii::t('profile', 'ERROR_INVALID_NID'));
                return false;
            }
            
            // Check state code
            $validStates = [];
            // States 01-16
            for ($i = 1; $i <= 16; $i++) {
                $validStates[] = str_pad($i, 2, '0', STR_PAD_LEFT);
            }
            // States 60-99
            for ($i = 60; $i <= 99; $i++) {
                $validStates[] = (string)$i;
            }
            
            if (!in_array($stateCode, $validStates)) {
                $this->addError($attribute, Yii::t('profile', 'ERROR_INVALID_NID'));
                return false;
            }
        }

        return true;
    }

    /**
     * @param Customers $model
     * @return static
     */
    public static function fromModel($model, $profileInfo = []) {
        if (!isset($model)) {
            return null;
        }
        $form = new static();
        
        // Load basic customer data
        foreach ($model->attributes as $attr => $value) {
            if ($form->hasProperty($attr)) {
                $form->$attr = $value;
            }
        }
        
        if (!empty($form->customers_dob)) {
            $dob = strtotime($form->customers_dob);
            $form->dobDay = date("j", $dob);
            $form->dobMonth = date("n", $dob);
            $form->dobYear = date("Y", $dob);
            $form->customers_dob = sprintf('%s-%s-%s', $form->dobYear, str_pad($form->dobMonth, 2, '0', STR_PAD_LEFT), str_pad($form->dobDay, 2, '0', STR_PAD_LEFT));
        }
        
        foreach ($profileInfo as $key => $value) {
            $form->$key = isset($profileInfo[$key]) ? $value : null;
        }

        // Split NRIC if exists
        $form->splitNric();
        
        return $form;
    }
}
