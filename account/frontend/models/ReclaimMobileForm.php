<?php

namespace frontend\models;

use Yii;
use yii\base\Model;
class ReclaimMobileForm extends \yii\base\Model{
    public $customers_email_address;
    
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_email_address'], 'string', 'max' => 96],
            [['customers_email_address'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['customers_email_address'], 'email', 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_email_address' => \Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
        ];
    }
}
