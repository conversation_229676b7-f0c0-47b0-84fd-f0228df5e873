<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class RedeemGiftCardForm extends \yii\base\Model {
    
    public $rd_code;
    public $rd_serial;
    public $rd_pin;
    
    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['rd_code', 'rd_serial', 'rd_pin'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['rd_code', 'rd_serial', 'rd_pin'], 'string', 'max' => 255],
            [['rd_code'], 'in', 'range' => !empty(Yii::$app->params['GIFT_CARD']['PRODUCT']) ? array_keys(Yii::$app->params['GIFT_CARD']['PRODUCT']) : []],
        ];
    }

    public function attributeLabels() {
        return [
            'rd_code' => 'Redeem type',
            'rd_serial' => 'Serial num',
            'rd_pin' => 'Pin code',
        ];
    }

}
