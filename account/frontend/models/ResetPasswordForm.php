<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class ResetPasswordForm extends Model
{
    
    public $customers_password;
    public $confirm_password;
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_password', 'confirm_password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['confirm_password'], 'compare', 'compareAttribute' => 'customers_password'],
            [['customers_password'], '\frontend\validators\PasswordValidator'],
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_password' => Yii::t('sso', 'ENTRY_NEW_PASSWORD'),
            'confirm_password' => Yii::t('sso', 'ENTRY_NEW_PASSWORD_CONFIRM'),
        ];
    }
}
