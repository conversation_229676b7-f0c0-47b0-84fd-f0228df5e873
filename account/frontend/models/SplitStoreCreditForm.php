<?php

namespace frontend\models;

use Yii;
use common\models\Customers;
use common\components\Password;

/**
 * Description of SplitStoreCreditForm
 *
 * <AUTHOR>
 * @description Model for form splitting the current currency between OG and G2G
 * \yii\base\Model
 */
class SplitStoreCreditForm extends \yii\db\ActiveRecord
{

    public $og_perc  = 100;
    public $g2g_perc = 0;
    public $og_wor  = 100;
    public $g2g_wor = 0;
    public $password;

//    public $user_id;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */


    public static function tableName() {
        return '{{%store_credit_split}}';
    }



    public function rules() {
        return [
            [ [ 'password' ], 'required', 'message' => Yii::t('rules', 'Password is required') ],
            [ [ 'og_perc', 'g2g_perc','og_wor', 'g2g_wor' ], 'integer', 'min' => 0, 'max' => 100 ],
            [ [ 'og_perc' ], 'checkPercTotal', 'message' => Yii::t('rules', 'ERROR PECENTAGE VALUES ') ],
            [ [ 'og_wor' ], 'checkPercTotalWOR', 'message' => Yii::t('rules', 'ERROR PECENTAGE VALUES ') ],
            [['password'], 'validatePassword'],
            [['confirm'], 'safe']
        ];
    }

    public function attributeLabels() {
        return [
        'og_perc' => Yii::t('forms', 'Offgamers Store Credit'),
        'g2g_perc' => Yii::t('forms', 'G2G Store Credit'),
        'og_wor' => Yii::t('forms', 'Offgamers OP'),
        'g2g_wor' => Yii::t('forms', 'G2G  OP'),
        'password' => Yii::t('forms', 'Password'),
         'user_id' => Yii::t('forms', 'Customer ID'),
         'confirm' => Yii::t('forms', 'Setting Confirmed')
        ];
    }


    public function checkPercTotalWOR($attribute, $params) {

        if ( ($this->og_wor + $this->g2g_wor) != 100 ) {
            $this->addError('percentage', Yii::t('rules', 'Invalid Percentage Calculation '));
        }
    }

    public function checkPercTotal($attribute, $params) {

        if ( ($this->g2g_perc + $this->og_perc) != 100 ) {
            $this->addError('percentage', Yii::t('rules', 'Invalid Percentage Calculation '));
        }
    }

    public function validatePassword($attribute, $params) {
        $m_cust = Customers::findOne([ 'customers_id' => Yii::$app->user->id ]);
        if ( empty($m_cust) ) {
            $this->addError('Authentication', Yii::t('rules', 'No User'));
        }

        if ( isset($m_cust->customers_id) ) {
            if ( !Password::validatePassword($this->password, $m_cust->customers_password) ) {
                $this->addError('password', Yii::t('rules', 'Invalid Password.'));
            }
        }
    }

    public static function getByUserid($user_id) {
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . Yii::$app->user->id . '/v2/value';
        $cache_duration = 60 * 60 * 24 * 180;

        if (Yii::$app->cache->exists($cache_key)) {
            return Yii::$app->cache->get($cache_key);
        } else {
            $split_model = \frontend\models\SplitStoreCreditForm::findOne(['user_id' => Yii::$app->user->id]);
            if ($split_model) {
                Yii::$app->cache->set($cache_key, $split_model, $cache_duration);
            }
            return $split_model;
        }
    }

    public function afterSave($insert, $changedAttributes) {
        $boolean = parent::afterSave($insert, $changedAttributes);
        $this->clearCache();
        return $boolean;
    }

    public function afterDelete() {
        parent::afterDelete();
        $this->clearCache();
    }

    public function clearCache() {
        $cache_key = Yii::$app->params['MEMCACHE_PREFIX'] . self::tableName() . '/' . \Yii::$app->user->id . '/v2/value';
        Yii::$app->cache->delete($cache_key);
    }
}
