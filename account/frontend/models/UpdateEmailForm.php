<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class UpdateEmailForm extends Model {

    public $customers_email_address;
    public $confirm_email;
    public $customers_password;

    //Manual set
    public $is_seller;
    public $email_change_allowed;
    public $email_changes_counter;
    public $email_changes_counter_daily;
    
    //Error only
    public $token_answer;

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['customers_password', 'customers_email_address', 'confirm_email'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['confirm_email'], 'compare', 'compareAttribute' => 'customers_email_address'],
            [['customers_email_address'], 'email', 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
            [['customers_email_address'], 'unique', 'targetClass' => \common\models\Customers::className(), 'filter' => 'customers_id != ' . Yii::$app->user->id, 'message' => '"{value}" ' . Yii::t('sso', 'ERROR_EMAIL_EXIST')],
        ];
    }

    public function attributeLabels() {
        return [
            'customers_email_address' => 'New email',
            'confirm_email' => 'Confirm email',
            'customers_password' => 'Customers password',
        ];
    }

}
