<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class UpdatePasswordForm extends Model {

    public $old_password;
    public $customers_password;
    public $confirm_password;

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['old_password', 'customers_password', 'confirm_password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['confirm_password'], 'compare', 'compareAttribute' => 'customers_password'],
            [['customers_password'], '\frontend\validators\PasswordValidator'],
        ];
    }

    public function attributeLabels() {
        return [
            'old_password' => 'Current password',
            'customers_password' => 'New password',
            'confirm_password' => 'Confirm new password',
        ];
    }

}
