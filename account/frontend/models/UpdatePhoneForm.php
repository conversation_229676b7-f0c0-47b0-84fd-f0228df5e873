<?php

namespace frontend\models;

use common\models\Configuration;
use Yii;
use yii\base\Model;

class UpdatePhoneForm extends Model {
    public $customers_country_dialing_code_id;
    public $customers_telephone;
    
    //Manual set
    public $cust_phone;
    
    //Error only
    public $token_answer;
    
    public $last_four_passed = false;

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['customers_country_dialing_code_id', 'customers_telephone'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['customers_telephone'], 'string', 'min' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH'), 'tooShort' => Yii::t('profile', 'MSG_ENTRY_CONTACT_NUMBER_ERROR', ['SYS_MIN_LENGTH' => Configuration::model()->getConfigValue('ENTRY_TELEPHONE_MIN_LENGTH')])],
        ];
    }

    public function attributeLabels() {
        return [
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code',
            'customers_telephone' => 'Customers Telephone',
        ];
    }

}
