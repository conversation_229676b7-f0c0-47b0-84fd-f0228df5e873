<?php

namespace frontend\models;

use common\models\Configuration;
use Yii;
use yii\base\Model;

class UpdatePhoneFormLastFourDigit extends Model {
    
    public $cust_phone;
    public $last_four_num;
    
    public $last_four_passed = false;

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['last_four_num'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['last_four_num'], 'string'],
        ];
    }
    
    
    
}
