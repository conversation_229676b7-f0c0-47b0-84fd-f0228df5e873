<?php

namespace frontend\models;

use common\components\Password;
use common\models\Customers;
use Yii;
use yii\base\Model;

class UpdateTwoFaForm extends Model {
    
    public $password;
    
    //Manual set
    public $twofa_enabled;
    
    public function rules() {
        return [
            [['password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['password'], 'validatePassword', 'when' => function() { return !$this->twofa_enabled;}],
            [['password'], 'validateTwoFA', 'when' => function() { return $this->twofa_enabled;}],
        ];
    }

    public function validatePassword($attr) {
        if (!Password::validatePassword($this->$attr, Customers::model()->customerPassword())) {
            $this->addError($attr, Yii::t('general', 'ERROR_MSG_INCORRECT_LOGIN_PASSWORD'));
        }
    }
    
    public function validateTwoFA($attr) {
        if (!\common\components\TwoFactorAuth::getInstance()->verifyCode(\common\components\TwoFactorAuth::getUserTwoFactorAuthSecret(\Yii::$app->user->id), $this->$attr)) {
            $this->addError($attr, Yii::t('general', 'ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH'));
        }
    }
}
