<?php

namespace frontend\models;

use Yii;
use yii\base\Model;

class XpressSignupForm extends Model {
    
    public $customers_email_address;
    public $customers_firstname;
    public $customers_lastname;
    public $customers_password;
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_email_address'], 'email', 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
            [['customers_email_address', 'customers_password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['customers_firstname', 'customers_lastname'], 'string', 'max' => 32],
            [['customers_password'], '\frontend\validators\PasswordValidator'],
        ];
    }
    
    public function attributeLabels() {
        return [
            'customers_email_address' => 'Customers Email Address',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_password' => 'Customers Password',
        ];
    }
}