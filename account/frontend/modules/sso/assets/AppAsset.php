<?php

namespace app\modules\sso\assets;

use Yii;
use yii\web\AssetBundle;
use yii\web\View;

/**
 * Main frontend application asset bundle.
 */
class AppAsset extends AssetBundle
{
    public $sourcePath = '@app/modules/sso/media';
    public $css = [
        'css/style.css',
    ];
    public $js = [
        'js/index.js',
    ];
    public $jsOptions = [
        'position' => View::POS_BEGIN,
    ];
    public $depends = [];
    public $publishOptions = [
        'forceCopy' => YII_ENV == 'dev' ? true : false,
    ];

    public static function getUrl($path = '', $appendTimestamp = true)
    {
        if (empty($path)) {
            $appendTimestamp = false;
        }

        $bundle = Yii::$app->assetManager->getBundle(static::class);

        if (!$appendTimestamp) {
            Yii::$app->assetManager->appendTimestamp = false;
        }

        return Yii::$app->assetManager->getAssetUrl($bundle, $path);
    }
}
