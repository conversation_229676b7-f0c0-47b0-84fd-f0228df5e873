<?php

namespace app\modules\sso\components;

use app\modules\sso\models\Country;
use app\modules\sso\models\Customer;
use app\modules\sso\models\CustomerGroups;
use app\modules\sso\models\CustomerInfo;
use app\modules\sso\models\CustomerInfoVerification;
use app\modules\sso\models\CustomerLoginIpHistory;
use app\modules\sso\models\CustomerRemarksHistory;
use app\modules\sso\models\CustomerSetting;
use app\modules\sso\models\OtpManual;
use app\modules\sso\models\ShassoUserLastLogin;
use app\modules\sso\models\ShassoUserPreference;
use app\modules\sso\models\UserIdentity;
use common\components\ArrayHelper;
use Yii;
use yii\db\ActiveQuery;
use yii\web\Cookie;

class Auth
{
    public static function login(Customer $customer, $loginMethod = 'normal', $from = 'login')
    {
        $user = Yii::$app->user;

        if (!$user->isGuest) {
            $user->logout(false);
        }

        $userIdentity = new UserIdentity();
        $userIdentity->customer = $customer;
        $userIdentity->loginMethod = $loginMethod;

        $user->login($userIdentity);

        $userPreference = Helper::getUserPreference();

        self::setSession($customer, $loginMethod, $from);
        self::setCookies($customer);
        self::saveCustomerInfo($customer, $userPreference['country']);
        self::saveCustomerLoginIpHistory($customer, $loginMethod);
        self::saveCustomerLastLogin($customer);
    }

    public static function saveCustomerInfo(Customer $customer, Country $country = null)
    {
        $custInfo = CustomerInfo::findOne([
            'customers_info_id' => $customer->customers_id
        ]);

        $data = [
            'customers_info_date_of_last_logon' => Helper::now(),
            'customers_info_number_of_logons' => 1,
            'customer_info_selected_country' => $country ? $country->countries_id : 0
        ];

        $custInfo->attributes = $data;
        $custInfo->save();
    }

    public static function isManualOtp(Country $country, $hp, $status)
    {
        $fullHp = $country->countries_international_dialing_code . $hp;

        return OtpManual::find()->where([
            'country_id' => $country->countries_id,
            'hp' => $fullHp,
            'status' => $status
        ])->exists();
    }

    public static function setAccountAsDormant($custInfo)
    {
        if ($custInfo->customer_info_account_dormant) {
            return;
        }

        if ($custInfo->customers_info_number_of_logons <= 0) {
            return;
        }

        $_diff_day = (time() - strtotime($custInfo->customers_info_date_of_last_logon)) / 86400;

        if ($_diff_day <= 90) {
            return;
        }

        $custInfo->customer_info_account_dormant = 1;
        $custInfo->save();
    }

    public static function validateHp($type, $country, $hp)
    {
        if ($type != 'hp') {
            return true;
        }

        if (is_int($country)) {
            $country = Country::findOne([
                'countries_id' => $country
            ]);
        }

        if (!$country) {
            return false;
        }

        $code = $country->countries_international_dialing_code;

        return SMS::verifyPhoneNumber($code, $hp);
    }

    protected static function setCookies(Customer $customer)
    {
        $time = time();
        $params = Yii::$app->params['sso'];
        $loginKey = ArrayHelper::getValue($params, 'login.key');
        $userval = $time . "." . md5($time . $customer->customers_id . $loginKey);

        $cookval = (empty($cookval) ? "" : $cookval . "|") . $userval;

        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'sa[dc]',
            'value' => $cookval,
            'expire' => time() + ArrayHelper::getValue($params, 'cookies.lifetime'),
            'domain' => ArrayHelper::getValue($params, 'cookies.domain'),
        ]));
    }

    protected static function setSession(Customer $customer, $loginMethod, $from)
    {
        $custGroup = CustomerGroups::findOne([
            'customers_groups_id' => $customer->customers_groups_id
        ]);
        $session = Yii::$app->session;
        $hybridAuth = Yii::$app->hybridauth;
        $session->set('login_method', $loginMethod);
        $sessionData = [
            'login_from' => $from,
            'login_method' => $loginMethod,
            'id' => $customer->customers_id,
            'email' => $customer->customers_email_address,
            'firstname' => $customer->customers_firstname,
            'lastname' => $customer->customers_lastname,
            'customers_groups_id' => $customer->customers_groups_id,
            'customers_default_address_id' => $customer->customers_default_address_id,
            'ogm_group_name' => $custGroup ? $custGroup->customers_groups_name : ''
        ];

        if ($loginMethod == 'sns') {
            $sessionData['sns'] = [
                'login_method' => $hybridAuth->getSession('ha_provider'),
                'photo' => $hybridAuth->getSession('ha_photo')
            ];
        }

        foreach ($sessionData as $k => $v) {
            $session->set($k, $v);
        }
    }

    public static function encryptPassword($plain)
    {
        $password = '';

        for ($i = 0; $i < 10; $i++) {
            $password .= Helper::cpnRand();
        }

        $salt = substr(md5($password), 0, 2);

        return md5($salt . $plain) . ':' . $salt;
    }

    public static function validatePassword($plain, $encrypted)
    {
        if ((isset($plain) && $plain != '') && (isset($encrypted) && $encrypted != '')) {
            // split apart the hash / salt
            $stack = explode(':', $encrypted);

            if (sizeof($stack) != 2)
                return false;
            if (md5($stack[1] . $plain) == $stack[0]) {
                return true;
            }
        }

        return false;
    }

    public static function saveCustomerLoginIpHistory(Customer $customer, $loginMethod)
    {
        if ($loginMethod == 'sns') {
            $loginMethod = Yii::$app->hybridauth->getSession('ha_provider');
        }

        $data = [
            'customers_login_date' => Helper::now(),
            'customers_login_ip' => PVM::getIPAddress(),
            'customers_login_ua_info' => PVM::getUserAgent(),
            'login_method' => $loginMethod,
            'customers_id' => $customer->customers_id
        ];

        $custLoginIpHistory = new CustomerLoginIpHistory();
        $custLoginIpHistory->insertOrUpdate($data);
    }

    public static function getVerifiedCustomerByHp($countryId, $hp)
    {
        $customers = Customer::findAll([
            'customers_country_dialing_code_id' => $countryId,
            'customers_telephone' => $hp
        ]);

        foreach ($customers as $customer) {
            $verifyExist = CustomerInfoVerification::find()->where([
                'customers_id' => $customer->customers_id,
                'customers_info_value' => $customer->country->countries_international_dialing_code . $customer->customers_telephone,
                'info_verification_type' => 'telephone',
                'info_verified' => 1
            ])->exists();

            if ($verifyExist) {
                return $customer;
            }
        }

        return null;
    }

    public static function saveCustomerLastLogin(Customer $customer)
    {
        $data = [
            "user_id" => $customer->customers_id,
            "login_date" => Helper::now(),
            "login_ip" => PVM::getIPAddress(),
            "login_ip_iso2" => Yii::$app->geoip->countryCodeByIP(PVM::getIPAddress())
        ];

        ShassoUserLastLogin::model()->insertOrUpdate($data);
    }

    public static function setQuery()
    {
        $request = Yii::$app->request;
        $session = Yii::$app->session;

        if ($request->get('origin') || $request->get('provider')) {
            $session->set('query', $request->get());
        }
    }

    public static function reclaimIsOnCooldown(Country $country, $hp)
    {
        $fullHp = $country->countries_international_dialing_code . $hp;
        $params = Yii::$app->params['sso'];

        $custInfoVerify = CustomerInfoVerification::find()
            ->where([
                'info_verified' => 1,
                'customers_info_value' => $fullHp,
                'info_verification_type' => 'telephone',
            ])
            ->joinWith(['customer' => function (ActiveQuery $query) use ($country, $hp) {
                $query->where([
                    'customers_country_dialing_code_id' => $country->countries_id,
                    'customers_telephone' => $hp
                ]);
            }])
            ->orderBy('customers_info_verification_date DESC')
            ->one();

        if (!$custInfoVerify) {
            return false;
        }

        if (Helper::onCoolDown($custInfoVerify->customers_info_verification_date, $params['reclaim_mobile']['period'])) {
            return true;
        }

        return false;
    }

    public static function unverifiedHpFromAllAccount(Country $country, $hp, Customer $owner)
    {
        $customers = Customer::findAll([
            'customers_country_dialing_code_id' => $country->countries_id,
            'customers_telephone' => $hp
        ]);

        $updated = 0;
        $unverifiedCustomerIds = [];

        foreach ($customers as $customer) {
            $fullHp = $customer->country->countries_international_dialing_code . $customer->customers_telephone;

            $rowsUpdated = CustomerInfoVerification::updateAll([
                'info_verified' => 0
            ], [
                'info_verified' => 1,
                'customers_id' => $customer->customers_id,
                'customers_info_value' => $fullHp,
                'info_verification_type' => 'telephone',
            ]);

            if ($rowsUpdated) {
                $custRemarkHistory = new CustomerRemarksHistory();
                $custRemarkHistory->attributes = [
                    'customers_id' => $customer->customers_id,
                    'date_remarks_added' => Helper::now(),
                    'remarks' => 'Removed verify phone number because the number is reclaim by customer ID : ' . $owner->customers_id,
                    'remarks_added_by' => 'system'
                ];
                $custRemarkHistory->save();

                $unverifiedCustomerIds[] = $customer->customers_id;

                $updated = 1;
            }
        }

        if ($unverifiedCustomerIds) {
            Yii::$app->session->set('unverified_customer_ids', $unverifiedCustomerIds);
        }

        return $updated;
    }

    public static function saveCustomerInfoVerification(Customer $customer)
    {
        $exist = CustomerInfoVerification::findOne([
            'customers_id' => $customer->customers_id,
            'customers_info_value' => $customer->country->countries_international_dialing_code . $customer->customers_telephone,
            'info_verification_type' => 'telephone',
        ]);

        if ($exist) {
            $exist->info_verified = 1;
            $exist->customers_info_verification_date = Helper::now();
            $exist->customers_info_verification_mode = 'A';
            $exist->save();

            return;
        }

        $custInfoVerification = new CustomerInfoVerification();
        $data = [
            'customers_id' => $customer->customers_id,
            'customers_info_value' => $customer->country->countries_international_dialing_code . $customer->customers_telephone,
            'serial_number' => '',
            'verify_try_turns' => 1,
            'info_verified' => 1,
            'info_verification_type' => 'telephone',
            'customers_info_verification_mode' => 'A',
            'customers_info_verification_date' => Helper::now()
        ];

        $custInfoVerification->attributes = $data;
        $custInfoVerification->save();

    }

    public static function saveCustomerSetting(Customer $customer)
    {
        $exist = CustomerSetting::findOne([
            'customers_id' => $customer->customers_id,
            'customers_setting_key' => 'setup_account'
        ]);

        if ($exist) {
            return;
        }

        $custSetting = new CustomerSetting();
        $custSetting->customers_id = $customer->customers_id;
        $custSetting->customers_setting_key = 'setup_account';
        $custSetting->customers_setting_value = 1;
        $custSetting->created_datetime = Helper::now();
        $custSetting->updated_datetime = Helper::now();
        $custSetting->save();
    }

    public static function saveUserPreference(Customer $customer, $userPreference)
    {
        $data = [
            'country' => ArrayHelper::getValue($userPreference, 'country_code'),
            'currency' => ArrayHelper::getValue($userPreference, 'currency_code'),
            'language' => ArrayHelper::getValue($userPreference, 'language_code')
        ];

        foreach ($data as $k => $v) {
            $userPreference = ShassoUserPreference::findOne([
                'user_id' => $customer->customers_id,
                'preference_key' => $k
            ]);

            if (!$userPreference) {
                $userPreference = new ShassoUserPreference();
                $userPreference->user_id = $customer->customers_id;
                $userPreference->preference_key = $k;
            }

            $userPreference->value = $v;
            $userPreference->save();
        }
    }

    public static function maskEmail($email)
    {
        if (!$email) {
            return $email;
        }

        list($femail) = explode('@', $email);

        $times = strlen($femail) - 2;

        if ($times > 10) {
            $times = 10;
        }

        if ($times <= 0) {
            return $email;
        }

        return substr($femail, 0, 2)
            . str_repeat('*', $times)
            . '@'
            . substr($email, strpos($email, '@') + 1);
    }

    public static function maskHp($hp)
    {
        if (!$hp) {
            return $hp;
        }

        return substr($hp, 0, 2) . str_repeat('*', strlen($hp) - 4) . substr($hp, -2);
    }

    public static function isHpVerified(Customer $customer)
    {
        $fullHp = $customer->country->countries_international_dialing_code . $customer->customers_telephone;

        return CustomerInfoVerification::find()->where([
            'customers_id' => $customer->customers_id,
            'customers_info_value' => $fullHp,
            'info_verification_type' => 'telephone',
            'info_verified' => 1
        ])->exists();
    }

    public static function hpExist(Customer $customer)
    {
        return $customer->customers_country_dialing_code_id && $customer->customers_telephone;
    }
}