<?php

namespace app\modules\sso\components;

use app\modules\sso\components\providers\MailLayerEmail;
use app\modules\sso\components\providers\PipwaveEmail;
use app\modules\sso\models\Customer;
use app\modules\sso\models\CustomerSetting;
use app\modules\sso\models\EmailValidation;
use app\modules\sso\models\LogSesBounce;
use app\modules\sso\models\ShassoUserToken;
use common\components\ArrayHelper;
use common\components\EmailComponent;
use Yii;
use yii\helpers\Json;

class Email
{
    public static function requestOtp(Customer $customer, $type = 'device_pin')
    {
        $params = Yii::$app->params['sso'];

        $via = 'email';
        $key = $via . '_' . $type;

        $custId = $customer->customers_id;
        $custName = implode(" ", [
            $customer->customers_firstname,
            $customer->customers_lastname
        ]);

        $userToken = ShassoUserToken::findOne([
            "user_id" => $custId,
            "token_type" => $type
        ]);

        if (!$userToken) {
            $userToken = new ShassoUserToken();
            $userToken->attributes = [
                "user_id" => $custId,
                "token_type" => $type,
                'created_date' => Helper::now(),
            ];
        }

        if(COtp::requestOnCoolDown($userToken->created_date, $key)) {
            return [
                'status' => 400,
                'message' => 'Request too frequent, please try again at '
                    . $params['otp'][$key]['request_period']
                    . ' seconds later'
            ];
        }

        $expiry = Helper::now('+', $params['otp'][$key]['lifetime']);

        $data = [
            'token_value' => Helper::createRandomValue(6, 'digits'),
            'created_date' => Helper::now(),
            'expiry_date' => $expiry
        ];

        $userToken->attributes = $data;
        $userToken->save();

        $title = Yii::t('sso', 'A new device accessed your OffGamers account - Device PIN:{DEVICE_PIN}', [
            'DEVICE_PIN' => $userToken->token_value
        ]);

        $data = [
            "title" => $title,
            'cname' => $custName,
            'device_pin' => $userToken->token_value,
            'support_url' => $params['support_url'],
        ];

        self::send(
            $customer,
            $title,
            'new_device-html',
            'new_device-text',
            $data
        );

        return [
            'status' => 200,
            'message' => 'Success sent OTP'
        ];
    }

    public static function isRequestOnCoolDown(Customer $customer, $type = 'device_pin') {
        $via = 'email';
        $key = $via . '_' . $type;

        $custId = $customer->customers_id;

        $userToken = ShassoUserToken::findOne([
            "user_id" => $custId,
            "token_type" => $type
        ]);

        if (!$userToken) {
            $userToken = new ShassoUserToken();
            $userToken->attributes = [
                "user_id" => $custId,
                "token_type" => $type,
                'created_date' => Helper::now(),
            ];
        }

        return COtp::requestOnCoolDown($userToken->created_date, $key);
    }

    public static function verifyOtp(Customer $customer, $answer, $type = 'device_pin')
    {
        $key = 'email_' . $type;
        $PVMParam = ArrayHelper::getValue(Yii::$app->params, 'sso.otp.' . $key);

        if (!PVM::securityCheck($type, $PVMParam, 0)) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes',[
                'action' => 'support'
            ]);
        }

        $userToken = ShassoUserToken::findOne([
            'user_id' => $customer->customers_id,
            "token_type" => $type,
            "token_value" => $answer
        ]);

        if (!$userToken) {
            return [
                'status' => 422,
                'message' => Yii::t("sso", "ERROR_DEVICE_INVALID_CODE")
            ];
        }

        if(Helper::isExpired($userToken->expiry_date)) {
            return [
                'status' => 400,
                'message' => 'Code is expired, please request again'
            ];
        }

        $userToken->delete();
        PVM::clear($key);

        return [
            'status' => 200,
            'message' => 'Verified Success'
        ];
    }

    public static function deleteOtp($customer, $type = 'expired')
    {
        $params = Yii::$app->params['sso']['device_pin'];

        if ($type == 'all') {
            ShassoUserToken::deleteAll([
                "user_id" => $customer->customers_id,
                "token_type" => $params['key']
            ]);

            return;
        }

        ShassoUserToken::deleteAll("token_type = :key AND expiry_date < :time AND user_id = :user_id", [
            ":key" => $params['key'],
            ":time" => Helper::now(),
            ":user_id" => $customer->customers_id
        ]);
    }

    public static function validate($email)
    {
        $emailDomain = explode('@', $email);

        if (ArrayHelper::getValue($emailDomain,1) == 'fb.com') {
            return false;
        }

        $email = self::purify($email);

        $logSesBounce = LogSesBounce::findOne(['email' => $email,]);

        if ($logSesBounce) {
            return false;
        }

        $model = EmailValidation::findOne(['email' => $email]);

        if ($model) {
            if ($model->mx_found) {
                return [
                    'status' => 200,
                    'message' => 'Email verification success'
                ];
            }

            return [
                'status' => 400,
                'message' => Yii::t('verifyEmail', 'INVALID_EMAIL')
            ];
        }

        $profile = 'pipwave';
        $raw = [];

        $resp = PipwaveEmail::verify($email);
        $emailVerified = $resp['status'] == 200;
        $mxFound = $emailVerified ? 1 : 0;
        $smtpCheck = $emailVerified ? 1 : 0;
        $score = $emailVerified ? 100 : 0;
        $raw[$profile] = $resp;

        if ($resp['status'] == 500) {
            $profile = 'maillayer';
            $resp = MailLayerEmail::verify($email);

            $mxFound = ArrayHelper::getValue($resp, 'mx_found', 0);
            $smtpCheck = ArrayHelper::getValue($resp, 'smtp_check', 0);
            $score = ArrayHelper::getValue($resp, 'score', 0) * 100;
            $raw[$profile] = $resp;
        }

        $model = new EmailValidation();
        $model->insertOrUpdate([
            'email' => $email,
            'profile' => $profile,
            'mx_found' => $mxFound,
            'smtp_check' => $smtpCheck,
            'score' => $score,
            'raw' => Json::encode($raw),
            'created_at' => time(),
            'updated_at' => time()
        ]);

        return [
            'status' => $resp['status'],
            'message' => $resp['message']
        ];
    }

    public static function send(Customer $customer, $title, $htmlView, $textView, $data)
    {
        $params = Yii::$app->params['sso'];
        $emailLayout = new EmailComponent();
        $custName = implode(" ", [
            $customer->customers_firstname,
            $customer->customers_lastname
        ]);

        $txt = [
            "content_html" => $emailLayout->setRenderEmailhtml($htmlView, $data),
            "content_text" => $emailLayout->setRenderEmailText($textView, $data),
        ];

        $emailLayout->sendMail(
            $custName,
            $customer->customers_email_address,
            $title,
            $txt,
            ArrayHelper::getValue($params, 'mail.sender_name'),
            ArrayHelper::getValue($params, 'mail.sender_email')
        );
    }

    private static function purify($email)
    {
        $str = explode(" ", $email);
        return trim(array_pop($str), "<> ");
    }

    private static function saveRequestOtpLimit(Customer $customer, $type)
    {
        $custSetting = CustomerSetting::findOne([
            'customers_id' => $customer->customers_id,
            'customers_setting_key' => $type
        ]);

        if (!$custSetting) {
            $custSetting = new CustomerSetting();
            $custSetting->customers_id = $customer->customers_id;
            $custSetting->customers_setting_key = $type;
            $custSetting->customers_setting_value = 0;
            $custSetting->created_datetime = Helper::now();
            $custSetting->updated_datetime = Helper::now();
        }

        $custSetting->customers_setting_value = $custSetting->customers_setting_value + 1;
        $custSetting->save();
    }
}