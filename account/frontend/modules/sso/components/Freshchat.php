<?php

namespace app\modules\sso\components;

use Yii;

class Freshchat
{
    public function init($live_chat_string)
    {
        $yparams = Yii::$app->params['sso'];
        $ysession = Yii::$app->session;

        $params = [
            'host' => $yparams['providers']['freshchat']['url'],
            'token' => $yparams['providers']['freshchat']['key'],
            "config" => [
                "headerProperty" => [
                    "hideChatButton" => true
                ]
            ]
        ];
        $user_info = [];

        if ($ysession->get('id')) {
            $user_info = [
                'externalId' => $ysession->get('id'),
                'firstName' => $ysession->get('firstname'),
                'lastName' => $ysession->get('lastname'),
                'email' => $ysession->get('email'),
                'customerGroupId' => $ysession->get('customers_groups_id'),
                'customerGroupName' => $ysession->get('ogm_group_name')
            ];
        }

        $live_chat_string = str_replace(
                "LIVE_CHAT_PARAMS", json_encode(array_merge($params, $user_info)), $live_chat_string
        );

        return $live_chat_string;
    }

}
