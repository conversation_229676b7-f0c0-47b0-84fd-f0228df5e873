<?php

namespace app\modules\sso\components;

use app\modules\sso\components\providers\GoogleRecaptcha;
use common\components\ArrayHelper;
use UAParser\Parser;
use Yii;

class PVM
{
    public static function securityCheck($type, $opts, $requiredRecaptcha = 1, $recaptchaToken = null, $captcha_type = '')
    {
        if (!self::checkIp($type, $opts['ip'])) {
            return false;
        }

        if (!self::checkSession($type, $opts['session'])) {
            return false;
        }

        return self::checkRecaptcha($requiredRecaptcha, $recaptchaToken, $captcha_type);
    }

    public static function checkRecaptcha($requiredRecaptcha, $recaptchaToken, $type = 'invisible')
    {
        if (!$requiredRecaptcha) {
            return true;
        }

        if (self::isIpException()) {
            return true;
        }

        if (!$recaptchaToken) {
            return false;
        }

        if ($type == 'challenge') {
            $resp = GoogleRecaptcha::verifyChallenge($recaptchaToken);
        } else {
            $resp = GoogleRecaptcha::verify($recaptchaToken);
        }

        if ($resp['success']) {
            $session = Yii::$app->session;

            $session->set('recaptcha_challenge_success', 1);
        }

        return $resp['success'];
    }

    public static function checkIp($type, $opts)
    {
        $lifetime = ArrayHelper::getValue($opts, 'cache_lifetime');
        $keyPrefix = 'pvm_' . $type . '_';
        $maxTries = $opts['max_retries'];
        $ip = self::getIPAddress();
        $cacheKey = $keyPrefix . $ip;
        $ipTrial = Yii::$app->cache->get($cacheKey) ?: 1;

        if ($ipTrial > $maxTries) {
            return false;
        }

        $ipTrial++;

        Yii::$app->cache->set($cacheKey, $ipTrial, $lifetime);

        return true;
    }

    public static function checkSession($type, $opts)
    {
        $session = Yii::$app->session;

        $maxTries = ArrayHelper::getValue($opts, 'max_retries');
        $lifetime = ArrayHelper::getValue($opts, 'session_lifetime');

        $keyPrefix = 'pvm_' . $type . '_';
        $cacheKey = $keyPrefix . $session->getId();
        $triedCount = Yii::$app->cache->get($cacheKey) ?: 1;

        if ($triedCount > $maxTries) {
            return false;
        }

        $triedCount++;

        Yii::$app->cache->set($cacheKey, $triedCount, $lifetime);

        return true;
    }

    public static function checkCountry()
    {
        $params = Yii::$app->params['sso'];
        $type = 'country';
        $session = Yii::$app->session;

        $challengeSuccess = $session->get('recaptcha_challenge_success', 0);

        if ($challengeSuccess) {
            return true;
        }

        $countryCode = strtolower(Yii::$app->geoip->countryCodeByIP());

        $config = ArrayHelper::getValue($params, 'pvm.countries.' . $countryCode);

        if (!$config) {
            $config = ArrayHelper::getValue($params, 'pvm.countries.default');
        }

        if ($config['max_retries'] < 1) {
            return true;
        }

        $lifetime = ArrayHelper::getValue($config, 'cache_lifetime');
        $keyPrefix = 'pvm_' . $type . '_';
        $maxTries = $config['max_retries'];
        $cacheKey = $keyPrefix . $countryCode;
        $ipTrial = Yii::$app->cache->get($cacheKey) ?: 1;

        if (Yii::$app->cache->get('pvm_country_blocked_' . $countryCode)) {
            return false;
        }

        if ($ipTrial > $maxTries) {
            Yii::$app->cache->set('pvm_country_blocked_' . $countryCode, 1, ArrayHelper::getValue($params, 'pvm.block_duration'));

            Yii::$app->reporter->reportToAdminViaSlack('[PVM] Country ' . strtoupper($countryCode) . ' has been blocked by ' . ArrayHelper::getValue($params, 'pvm.block_duration') / 60 . ' minutes', '');
            return false;
        }

        if ($session->get('country_session')) {
            return true;
        }

        $ipTrial++;

        Yii::$app->cache->set($cacheKey, $ipTrial, $lifetime);
        $session->set('country_session', 1);

        return true;
    }

    public static function clear($type)
    {
        if (!is_array($type)) {
            self::clean($type);

            return;
        }

        foreach ($type as $t) {
            self::clean($t);
        }
    }

    public static function getIPAddress()
    {
        if ($ip = self::getAkamaiIp()) {
            return $ip;
        }

        $ip = ArrayHelper::getValue($_SERVER, 'HTTP_X_FORWARDED_FOR');

        if (!$ip) {
            return ArrayHelper::getValue($_SERVER, 'REMOTE_ADDR');
        }

        $ip_array = explode(',', $ip);

        foreach ($ip_array as $ip) {
            $ip = trim($ip);

            if (self::validateIp($ip)) {
                return $ip;
            }
        }

        return null;
    }

    public static function parseUserAgent($userAgent = null)
    {
        $userAgent = $userAgent ?: static::getUserAgent();
        $os = 'Unknown';
        $browser = 'Unknown';
        $parse_result = Parser::create()->parse($userAgent);
        if (!empty($parse_result->os->family)) {
            $os = $parse_result->os->family;
        }
        if (!empty($parse_result->ua->family)) {
            $browser = $parse_result->ua->family;
        }
        return [
            'os' => $os,
            'browser' => $browser,
        ];
    }

    public static function getUserAgent()
    {
        return ArrayHelper::getValue($_SERVER, 'HTTP_USER_AGENT', getenv('HTTP_USER_AGENT'));
    }

    private static function clean($type)
    {
        $keyPrefix = 'pvm_' . $type . '_';

        //Clear cache
        $ip = self::getIPAddress();
        $cacheKey = $keyPrefix . $ip;
        Yii::$app->cache->delete($cacheKey);

        //Clear session
        $session = Yii::$app->session;
        $sessionKey = $keyPrefix . $session->getId();
        Yii::$app->cache->delete($sessionKey);
    }

    private static function validateIp($ip)
    {
        if (!$ip) {
            return false;
        }

        $validator = new ReserveIpValidator();

        if (!$validator->validate($ip)) {
            return false;
        }

        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE | FILTER_FLAG_NO_PRIV_RANGE) === false) {
            return false;
        }

        return true;
    }

    private static function getAkamaiIp()
    {
        $ip = ArrayHelper::getValue($_SERVER, 'HTTP_TRUE_CLIENT_IP');

        if (!$ip) {
            return null;
        }

        return self::validateIp($ip) ? $ip : null;
    }

    public static function isIpException($ranges = null)
    {
        if (!$ranges) {
            $ranges = ['testim', 'office'];
        }

        $validator = new ReserveIpValidator();
        $validator->ranges = $ranges;

        return $validator->validate(self::getIPAddress());
    }
}