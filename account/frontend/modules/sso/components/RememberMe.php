<?php

namespace app\modules\sso\components;

use app\modules\sso\models\Customer;
use app\modules\sso\models\ShassoRememberMeToken;
use app\modules\sso\models\ShassoRememberMeTokenUpdateLog;
use common\components\ArrayHelper;
use Yii;
use yii\web\Cookie;

class RememberMe
{
    public static function login()
    {
        $cookies = Yii::$app->request->cookies->getValue('ogm');
        $username = ArrayHelper::getValue($cookies, 'un');
        $token = ArrayHelper::getValue($cookies, 'uc');

        if (!$username || !$token) {
            return false;
        }

        $customer = Customer::findOne(['customers_email_address' => $username]);

        if (!$customer) {
            return false;
        }

        if (!ShassoRememberMeToken::isValid($customer, $token)) {
            self::destroy($customer, $token);
            return false;
        } else {
            self::store($customer);
        }

        Auth::login($customer, 'remember_me');

        return Yii::$app->getResponse()->redirect('/sso/login-all');
    }

    public static function store(Customer $customer)
    {
        $params = Yii::$app->params['sso'];
        $geoIp = Yii::$app->geoip;

        $rememberMeCookies = Yii::$app->request->cookies->getValue('ogm');
        $oldToken = ArrayHelper::getValue($rememberMeCookies, 'uc');

        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'S3RM',
            'domain' => ArrayHelper::getValue($params, 'cookies.domain'),
        ]));

        $token = ShassoRememberMeToken::generate($customer, $oldToken);

        $data = [
            'customers_id' => $customer->customers_id,
            'token_id' => Helper::createRandomValue(32),
            'token' => $token,
            'login_ip' => PVM::getIPAddress(),
            'login_country' => $geoIp->countryCodeByIP(PVM::getIPAddress()),
            'user_agent' => PVM::getUserAgent(),
            'parsed_ua_os' => ArrayHelper::getValue(PVM::parseUserAgent(), 'os'),
            'parsed_ua_browser' => ArrayHelper::getValue(PVM::parseUserAgent(), 'browser'),
            'expire' => time() + ArrayHelper::getValue($params, 'cookies.lifetime'),
        ];

        $rememberMeToken = new ShassoRememberMeToken();
        $rememberMeToken->attributes = $data;
        $rememberMeToken->save();

        if ($oldToken) {
            $oldData = null;

            $oldRememberMeToken = ShassoRememberMeToken::findOne([
                'customers_id' => $customer->customers_id,
                'token' => $oldToken
            ]);

            if ($oldRememberMeToken) {
                $oldData = $oldRememberMeToken->getAttributes(null, ['except']);
                $oldRememberMeToken->delete();
            }

            self::saveTokenUpdatelog('update', $oldData, $data);
        }

        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'ogm[un]',
            'value' => $customer->customers_email_address,
            'expire' => time() + ArrayHelper::getValue($params, 'cookies.lifetime'),
            'domain' => ArrayHelper::getValue($params, 'cookies.domain'),
        ]));

        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'ogm[uc]',
            'value' => $token,
            'expire' => time() + ArrayHelper::getValue($params, 'cookies.lifetime'),
            'domain' => ArrayHelper::getValue($params, 'cookies.domain'),
        ]));
    }

    public static function destroy(Customer $customer, $token)
    {
        $params = Yii::$app->params['sso'];
        $geoIp = Yii::$app->geoip;

        $cookieDomain = ArrayHelper::getValue($params, 'cookies.domain');

        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'ogm[un]',
            'domain' => $cookieDomain,
        ]));

        Yii::$app->response->cookies->remove(new Cookie([
            'name' => 'ogm[uc]',
            'domain' => $cookieDomain,
        ]));

        $rmToken = ShassoRememberMeToken::findOne([
            'customers_id' => $customer->customers_id,
            'token' => $token
        ]);

        if (!$rmToken) {
            return;
        }

        $data = [
            'customers_id' => $customer->customers_id,
            'token_id' => $rmToken->token_id ?: $token,
            'login_ip' => PVM::getIPAddress(),
            'login_country' => $geoIp->countryCodeByIP(PVM::getIPAddress()),
            'user_agent' => PVM::getUserAgent(),
            'parsed_ua_os' => ArrayHelper::getValue(PVM::parseUserAgent(), 'os'),
            'parsed_ua_browser' => ArrayHelper::getValue(PVM::parseUserAgent(), 'browser'),
        ];

        $old = $rmToken->getAttributes(null, ['except']);
        self::saveTokenUpdatelog('delete', $old, $data);

        $rmToken->delete();
    }

    public static function saveTokenUpdatelog($type, $old, $new)
    {
        if (!$old) {
            return;
        }

        $oldParseUserAgent = PVM::parseUserAgent($old['user_agent']);

        $data = array_merge($new, [
            'prev_login_ip' => ArrayHelper::getValue($old, 'login_ip'),
            'prev_login_country' => ArrayHelper::getValue($old, 'login_country'),
            'prev_parsed_ua_os' => ArrayHelper::getValue($old, 'parsed_ua_os'),
            'prev_parsed_ua_browser' => ArrayHelper::getValue($old, 'parsed_ua_browser'),
            'type' => $type,
            'created_date' => Helper::now()
        ]);

        if (!ArrayHelper::getValue($data, 'prev_parsed_ua_os')) {
            $data['prev_parsed_ua_os'] = ArrayHelper::getValue($oldParseUserAgent, 'os');
        }

        if (!ArrayHelper::getValue($data, 'prev_parsed_ua_browser')) {
            $data['prev_parsed_ua_browser'] = ArrayHelper::getValue($oldParseUserAgent, 'browser');
        }

        $rememberMeTokenUpdateLog = new ShassoRememberMeTokenUpdateLog();
        $rememberMeTokenUpdateLog->attributes = $data;
        $rememberMeTokenUpdateLog->save();
    }
}