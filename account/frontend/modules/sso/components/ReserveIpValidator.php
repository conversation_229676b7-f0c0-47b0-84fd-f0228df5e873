<?php

namespace app\modules\sso\components;

use common\components\ArrayHelper;
use Yii;
use yii\validators\IpValidator;

class ReserveIpValidator extends IpValidator
{
    public $networks = [
        '*' => ['any'],
        'any' => ['0.0.0.0/0', '::/0'],
        'private' => ['10.0.0.0/8', '**********/12', '***********/16', 'fd00::/8'],
        'multicast' => ['*********/4', 'ff00::/8'],
        'linklocal' => ['***********/16', 'fe80::/10'],
        'localhost' => ['*********/8', '::1'],
        'documentation' => ['*********/24', '************/24', '***********/24', '2001:db8::/32'],
        'system' => ['multicast', 'linklocal', 'localhost', 'documentation'],
    ];

    public function __construct($config = [])
    {
        $reserveIpRange = [
            ArrayHelper::getValue(Yii::$app->params,'sso.pvm.reserve_ip_range')
        ];

        $this->networks['private'] = array_merge($reserveIpRange, $this->networks['private']);
        $this->networks['testim'] = ArrayHelper::getValue(Yii::$app->params['sso'], 'recaptcha.exception_ip.testim');
        $this->networks['office'] = ArrayHelper::getValue(Yii::$app->params['sso'], 'recaptcha.exception_ip.office');

        $this->ranges = ['!private', 'any'];

        parent::__construct($config);
    }
}