<?php

namespace app\modules\sso\components;

use app\modules\sso\components\providers\PipwaveOtp;
use app\modules\sso\models\Country;
use app\modules\sso\models\CustomerOtp;
use app\modules\sso\models\Otp;
use app\modules\sso\models\OtpManual;
use app\modules\sso\models\SmsReport;
use common\components\ArrayHelper;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;
use Yii;

class SMS
{
    public static function requestOtp($country, $hp, $type = 'device_pin')
    {
        $user = Yii::$app->user;
        $params = Yii::$app->params['sso'];
        $key = 'hp_' . $type;

        $period = $params['otp'][$key]['request_period'];

        $otp = self::getOtp($user, $country, $hp, $type);
        $userId = self::getUserId($user, $otp);
        $lastRequestDate = self::getLastRequestDate($user, $otp);

        if (COtp::requestOnCoolDown($lastRequestDate, $key)) {
            return [
                'status' => 400,
                'message' => 'Request too frequent, please try again at '
                    . $period
                    . ' seconds later'
            ];
        }

        $resp = PipwaveOtp::sendToken($country, $hp, $userId);

        if ($resp['status'] != 200) {
            return $resp;
        }

        if (ArrayHelper::getValue(Yii::$app->params, 'sso.otp.testing')) {
            Yii::$app->session->set('test-otp', ArrayHelper::getValue($resp, 'data.test_token'));
        }

        if ($user->isGuest) {
            $otp->updated_at = Helper::now();
            $otp->reference_token = $resp['data']['reference'];
        } else {
            $otp->customers_otp_digit = $resp['data']['reference'];
            $otp->customers_otp_request_date = Helper::now();

            $smsReport = new SmsReport();
            $smsReport->attributes = [
                'customers_id' => $userId,
                'sms_request_date' => Helper::now(),
                'sms_request_type' => $type,
                'sms_request_phone_number' => $country->countries_international_dialing_code . $hp,
                'sms_request_phone_country_id' => $country->countries_id,
                'sms_request_page' => ArrayHelper::getValue($_SERVER, 'HTTP_REFERER', ''),
                'sms_provider' => 'pipwave',
                'sms_send_date' => Helper::now()
            ];

            $smsReport->save();
        }

        $otp->save();

        return [
            'status' => 200,
            'message' => 'Success sent OTP'
        ];
    }

    public static function isRequestOnCoolDown($country, $hp, $type = 'device_pin')
    {
        $user = Yii::$app->user;
        $key = 'hp_' . $type;

        $otp = self::getOtp($user, $country, $hp, $type);
        $lastRequestDate = self::getLastRequestDate($user, $otp);

        return COtp::requestOnCoolDown($lastRequestDate, $key);
    }

    public static function verifyOtp(Country $country, $hp, $answer, $type = 'device_pin')
    {
        $user = Yii::$app->user;

        $key = 'hp_' . $type;
        $PVMParam = ArrayHelper::getValue(Yii::$app->params, 'sso.otp.' . $key);

        if (!PVM::securityCheck($type, $PVMParam, 0)) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        $otp = self::getOtp($user, $country, $hp, $type);
        $userId = self::getUserId($user, $otp);

        $resp = self::verifyByManualOtp($country, $hp, $answer, $type);

        if ($resp['status'] == 200) {
            PVM::clear($key);

            return $resp;
        }

        $resp = PipwaveOtp::verifyToken($country, $hp, $userId, $otp, $answer);

        if ($resp['status'] == 200) {
            PVM::clear($key);
        }

        return $resp;
    }

    protected static function getOtp($user, Country $country, $hp, $type)
    {
        $code = $country->countries_international_dialing_code;

        if ($user->isGuest) {
            $otp = Otp::findOne([
                'country_id' => $country->countries_id,
                'hp' => $code . $hp
            ]);

            if (!$otp) {
                $otp = new Otp();
                $otp->attributes = [
                    'country_id' => $country->countries_id,
                    'hp' => $code . $hp,
                ];
                $otp->save();
            }

            return $otp;
        }

        $otp = CustomerOtp::findOne([
            'customers_id' => $user->id,
            'customers_otp_type' => $type,
            'customers_match_data' => $code . $hp
        ]);

        if (!$otp) {
            $otp = new CustomerOtp();
            $otp->attributes = [
                'customers_id' => $user->id,
                'customers_otp_type' => $type,
                'customers_match_data' => $code . $hp,
                'customers_otp_request_date' => Helper::now()
            ];
        }

        return $otp;
    }

    private static function getUserId($user, $otp)
    {
        return $user->isGuest ? $otp->id : $user->id;
    }

    private static function getLastRequestDate($user, $otp)
    {
        return $user->isGuest ? $otp->updated_at : $otp->customers_otp_request_date;
    }

    public static function verifyPhoneNumber($code, $number)
    {
        $fullNumber = $code . $number;
        $phoneUtil = PhoneNumberUtil::getInstance();

        if (!$code || !$number || strlen($fullNumber) <= 8) {
            return false;
        }

        try {
            $parseResult = $phoneUtil->parse('+' . preg_replace('/[\D]/', '', $fullNumber));

            if (!$phoneUtil->isValidNumber($parseResult)) {
                return false;
            }
        } catch (NumberParseException $ex) {
            return false;
        }

        return true;
    }

    private static function requestVerifyHpOtp($country, $hp, $type)
    {

    }

    private static function verifyByManualOtp(Country $country, $hp, $answer, $type)
    {
        if ($type != 'verify_phone') {
            return [
                'status' => 400,
                'message' => 'Manual OTP only allowed for register'
            ];
        }

        $fullHp = $country->countries_international_dialing_code . $hp;

        $otpManual = OtpManual::findOne([
            'country_id' => $country->countries_id,
            'hp' => $fullHp,
            'status' => OtpManual::STATUS_NEW,
            'token' => $answer
        ]);

        if (!$otpManual) {
            return [
                'status' => 400,
                'message' => 'No manual otp found'
            ];
        }

        if (Helper::isExpired(date('Y-m-d H:i:s', $otpManual->expired_at))) {
            $otpManual->status = OtpManual::STATUS_EXPIRED;
            $otpManual->save();

            return [
                'status' => 400,
                'message' => 'Token is expired'
            ];
        }

        $otpManual->status = OtpManual::STATUS_USED;
        $otpManual->save();

        Yii::$app->session->set('manual_otp', 1);

        return [
            'status' => 200,
            'message' => 'Success verify token'
        ];
    }
}