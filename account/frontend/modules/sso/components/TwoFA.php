<?php

namespace app\modules\sso\components;

use app\modules\sso\models\Customer;
use app\modules\sso\models\ShassoCustomerStaticInfo;
use common\models\CustomersStaticInfo;
use RobThree\Auth\TwoFactorAuth;
use Yii;

class TwoFA
{
    public static function enabled(Customer $customer)
    {
        $custStaticInfo = ShassoCustomerStaticInfo::findOne([
            'customers_id' => $customer->customers_id,
            'info_key' => Yii::$app->params['sso']['2fa']['info_key'],
        ]);

        if ($custStaticInfo && $custStaticInfo->value) {
            return true;
        }

        return false;
    }

    public static function verify(Customer $customer, $answer)
    {
        $twoFactorAuth = new TwoFactorAuth();

        return $twoFactorAuth->verifyCode(self::secret($customer), $answer);
    }

    private static function secret(Customer $customer)
    {
        $custInfo = CustomersStaticInfo::findOne([
            'customers_id' => $customer->customers_id,
            'info_key' => Yii::$app->params['sso']['2fa']['info_key'],
        ]);

        return $custInfo ? $custInfo->value : null;
    }
}