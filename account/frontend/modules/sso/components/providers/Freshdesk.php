<?php

namespace app\modules\sso\components\providers;

use app\modules\sso\components\Helper;
use app\modules\sso\models\Customer;
use common\components\ArrayHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Keychain;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Rsa\Sha256;
use Yii;

class Freshdesk
{
    public static function redirect(Customer $customer, $query)
    {
        $validate = self::validate($query);

        if (!$validate) {
            return Yii::$app->controller->redirect('/sso/login');
        }

        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.freshdesk');

        $country = $customer->country;

        $countryCode = $country->countries_international_dialing_code
            ? '+' . $country->countries_international_dialing_code . ' '
            : '0';

        $address = $customer->daddress;
        $fullAddress = '';

        if ($address && $address->entry_street_address) {
            $addressCountry = $address->country;
            $fullAddress = $address->entry_street_address
                . " " . $address->entry_suburb
                . " " . $address->entry_city
                . ", " . $address->entry_state
                . " " . $address->entry_postcode
                . " " . $addressCountry->countries_name;
        }

        $data = [
            "name" => $customer->customers_firstname . " " . $customer->customers_lastname,
            "email" => $customer->customers_email_address,
            "phone" => $countryCode . $customer->customers_telephone,
            "unique_external_id" => ArrayHelper::getValue($params, 'id_prefix') . $customer->customers_id,
            "address" => $fullAddress,
        ];

        $user = self::getUser($customer);

        if (!$user) {
            self::addUser($customer, $data);
        } else {
            self::updateUser($customer, $user['id'], $data);
        }

        //proceed to redirect
        $token = self::createToken($query, $customer, $countryCode);
        $url = ArrayHelper::getValue($query, 'redirect_uri')
            . '?state=' . ArrayHelper::getValue($query, 'state')
            . '&id_token=' . $token;

        return Yii::$app->controller->redirect($url);
    }

    private static function createToken($query, Customer $customer, $countryCode)
    {
        $signer = new Sha256();
        $keychain = new Keychain();
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.freshdesk');

        return Yii::$app->jwt->getBuilder()
            ->setSubject("OG Freshdesk SSO")
            ->set('sub', ArrayHelper::getValue($params, 'id_prefix') . $customer->customers_id)
            ->set('email', $customer->customers_email_address)
            ->set('iat', time())
            ->set('nonce', ArrayHelper::getValue($query, 'nonce'))
            ->set('given_name', $customer->customers_firstname)
            ->set('family_name', $customer->customers_lastname)
            ->set('phone_number', $countryCode . $customer->customers_telephone)
            ->sign($signer, $keychain->getPrivateKey('file://' . Yii::getAlias('@freshdeskPrivateKey'))) // creates a signature using your private key
            ->getToken();
    }

    public static function getUser(Customer $customer)
    {
        $data = [
            'email' => $customer->customers_email_address,
        ];

        $response = self::api($customer, 'contacts', $data);

        return ArrayHelper::getValue($response, 0);
    }

    public static function addUser(Customer $customer, $data)
    {
        return self::api($customer, 'contacts', $data, 'post');
    }

    private static function updateUser(Customer $customer, $id, $data)
    {
        return self::api($customer, 'contacts/' . $id, $data, 'put');
    }

    protected static function api(Customer $customer, $url, $data = [], $method = 'get')
    {
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.freshdesk');
        $client = new Client();
        $url = ArrayHelper::getValue($params, 'url') . $url;
        try {
            if ($method == 'get') {
                $headers = [
                    'Content-Type' => 'application/json',
                ];

                $response = $client->get($url, [
                    'headers' => $headers,
                    'query' => $data,
                    'auth' => [ArrayHelper::getValue($params, 'key'), null],
                    'http_errors' => false
                ]);
            } else {
                $response = $client->request($method, $url, [
                    'json' => $data,
                    'auth' => [ArrayHelper::getValue($params, 'key'), null],
                    'http_errors' => false
                ]);
            }

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            $content = $e->getResponse() ? json_decode($e->getResponse()->getBody(), 1) : '';

            if($e->getCode() != 409) {
                Yii::$app->reporter->reportToAdminViaSlack('Freshdesk API failed for customer ' . $customer->customers_id, [
                    'status' => $e->getCode(),
                    'message' => $e->getMessage(),
                    'data' => $data,
                ]);
            }

            return $content;
        }
    }

    private static function validate($query)
    {
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.freshdesk');

        return Helper::contains($query['redirect_uri'], $params['redirect_url']);
    }
}