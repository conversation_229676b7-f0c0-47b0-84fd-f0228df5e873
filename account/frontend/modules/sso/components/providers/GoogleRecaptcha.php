<?php

namespace app\modules\sso\components\providers;

use common\components\ArrayHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Yii;

class GoogleRecaptcha
{
    public static function verify($token)
    {
        $client = new Client();
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.google.recaptcha');
        $data = [
            'secret' => $params['secret'],
            'response' => $token,
        ];
        $url = $params['url'];

        try {
            $response = $client->post($url, [
                'form_params' => $data,
                'http_errors' => false
            ]);

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            return json_decode($e->getResponse()->getBody(), 1);

        }
    }

    public static function verifyChallenge($token)
    {
        $client = new Client();
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.google.recaptcha_challenge');
        $data = [
            'secret' => $params['secret'],
            'response' => $token,
        ];
        $url = $params['url'];

        try {
            $response = $client->post($url, [
                'form_params' => $data,
                'http_errors' => false
            ]);

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            $content = $e->getResponse() ? json_decode($e->getResponse()->getBody(), 1) : '';

            return $content;

        }
    }
}