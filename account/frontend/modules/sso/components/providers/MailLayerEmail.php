<?php

namespace app\modules\sso\components\providers;

use app\modules\sso\components\Helper;
use common\components\ArrayHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Yii;

class MailLayerEmail
{
    public static function verify($email)
    {
        $data = [
            'email' => $email,
            'smtp' => 1,
            'format' => 1
        ];

        $resp = self::api($data);

        if (isset($resp['email']) && isset($resp['mx_found']) && isset($resp['smtp_check']) && isset($resp['score'])) {
            return [
                'status' => 200,
                'message' => 'Email verification success',
                'data' => $resp
            ];
        }

        return [
            'status' => 400,
            'message' => 'Email verification failed',
            'data' => $resp
        ];
    }

    public static function api($data)
    {
        $url = 'check';
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.mail_layer');
        $client = new Client();
        $data['access_key'] = ArrayHelper::getValue($params, 'key');

        $url = ArrayHelper::getValue($params, 'url') . $url;

        try {
            $response = $client->get($url, [
                'query' => $data,
                'http_errors' => false
            ]);

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            $content = $e->getResponse() ? json_decode($e->getResponse()->getBody(), 1) : '';

            Yii::$app->reporter->reportToAdminViaSlack('MailLayer Email failed', [
                'url' => $url,
                'status' => $e->getCode(),
                'message' => $e->getMessage(),
                'data' => $data,
                'content' => $content
            ]);

            return $content;
        }
    }

}