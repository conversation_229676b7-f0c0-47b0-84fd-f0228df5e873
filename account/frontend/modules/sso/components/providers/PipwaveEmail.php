<?php

namespace app\modules\sso\components\providers;

use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use common\components\ArrayHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Yii;

class PipwaveEmail
{
    public static function verify($email)
    {
        $reporter = Yii::$app->reporter;

        $data = [
            "action" => "validate-email",
            "user_id" => null,
            "email_address" => $email,
            "signup_ip_address" => "",
            "session_ip_address" => PVM::getIPAddress(),
        ];

        $resp = self::api($data, ['email_address']);

        $resultAction = ArrayHelper::getValue($resp, 'result.action');

        if (!$resultAction) {
            $reporter->reportToAdminViaSlack('Email verification API failed', [
                'params' => $data,
                'api_response' => $resp,
            ]);

            return [
                'status' => 500,
                'message' => Yii::t('verifyEmail', 'INVALID_EMAIL'),
                'data' => $resp
            ];
        }

        if ($resultAction == 'allow') {
            return [
                'status' => 200,
                'message' => 'Email validated',
                'data' => $resp
            ];
        }

        switch (ArrayHelper::getValue($resp, 'result.action_reason')) {
            case "mailbox-full" :
                return [
                    'status' => 200,
                    'message' => Yii::t('verifyEmail', 'TRY_AGAIN_MAILBOX_FULL'),
                    'data' => $resp
                ];
            case "typo" :
                return [
                    'status' => 400,
                    'message' => Yii::t('verifyEmail', 'TRY_AGAIN_TYPO'),
                    'data' => $resp
                ];
            default:
                return [
                    'status' => 400,
                    'message' => Yii::t('verifyEmail', 'INVALID_EMAIL'),
                    'data' => $resp
                ];

        }
    }

    public static function api($data, $signatureArray)
    {
        $url = 'email';
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.pipwave');
        $client = new Client();

        $data['api_key'] = ArrayHelper::getValue($params, 'key');
        $data['timestamp'] = time();

        $signatureArray = array_merge($signatureArray, [
            'timestamp',
            'action',
            'api_key',
            'api_secret'
        ]);

        $data['signature'] = self::signature($data, $signatureArray);
        $url = ArrayHelper::getValue($params, 'url') . $url;

        try {
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $data['api_key']
            ];

            $response = $client->post($url, [
                'headers' => $headers,
                'json' => $data,
                'http_errors' => false
            ]);

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            $content = $e->getResponse() ? json_decode($e->getResponse()->getBody(), 1) : '';

            Yii::$app->reporter->reportToAdminViaSlack('Pipwave Email failed', [
                'url' => $url,
                'status' => $e->getCode(),
                'message' => $e->getMessage(),
                'data' => $data,
                'content' => $content
            ]);

            return $content;
        }
    }

    protected static function signature($data, $signatureKeys)
    {
        $data['api_secret'] = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.pipwave.secret');
        ksort($data);
        $s = "";
        foreach ($data as $key => $value) {
            if (in_array($key, $signatureKeys)) {
                $s .= $key . ':' . $value;
            }
        }
        return sha1($s);
    }
}