<?php

namespace app\modules\sso\components\providers;

use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use app\modules\sso\models\Country;
use app\modules\sso\models\Otp;
use common\components\ArrayHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Yii;

class PipwaveOtp
{
    public static function sendToken(Country $country, $hp, $userId)
    {
        $user = Yii::$app->user;
        $reporter = Yii::$app->reporter;

        $data = [
            'action' => 'send-token',
            'user_id' => (string)$userId,
            'is_guest' => $user->isGuest,
            'ip_address' => PVM::getIPAddress(),
            'contact_no_country_dialing_code' => $country->countries_international_dialing_code,
            'contact_no' => $hp
        ];

        $resp = self::api($data, ['user_id', 'contact_no_country_dialing_code', 'contact_no']);

        if (isset($resp['status'])) {
            switch ($resp['status']) {
                case '200' :
                    return [
                        'status' => 200,
                        'message' => 'Success sent OTP',
                        'data' => $resp
                    ];

                //Invalid phone number
                case '5001' :
                    return [
                        'status' => 400,
                        'message' => 'Invalid phone number'
                    ];

                // Too frequent request
                case '5002':
                // Duplicate OTP request for the same mobile number by multiple users
                case '5003':
                // Request throttled
                case '5004':
                // Too many resend for this request
                case '5005':
                    return [
                        'status' => 400,
                        'message' => "[{$resp['status']}] " . 'Request too frequent, please try again later',
                        'data' => [
                            'action' => 'support'
                        ]
                    ];
                //Number is blacklisted
                case '5006':
                    return [
                        'status' => 400,
                        'message' => "[{$resp['status']}] " . $resp['message'],
                        'data' => [
                            'action' => 'support'
                        ]
                    ];
                    //Insufficient credit
                case '5021':
                    //Solution has been disabled, please contact the administrator
                case '5022':
                    //Merchant has disabled the solution, please enable it at Merchant Portal
                case '5023':
                default:
                    $reporter->reportToAdminViaSlack('Phone Verification Failed, Request : ' . json_encode($data) . ', Response : ' . json_encode($resp), '');
                    return [
                        'status' => 400,
                        'message' => "[{$resp['status']}" . 'Something went wrong, please try again later',
                        'data' => [
                            'action' => 'support'
                        ]
                    ];
            }
        } else {
            $reporter->reportToAdminViaSlack('Pipwave API Failed, Request : ' . json_encode($data) . ', Response : ' . json_encode($resp), '');
            return [
                'status' => 400,
                'message' => 'Something went wrong, please try again later',
                'data' => [
                    'action' => 'support'
                ]
            ];
        }
    }

    public static function verifyToken(Country $country, $hp, $userId, $otp, $answer)
    {
        $reporter = Yii::$app->reporter;
        $user = Yii::$app->user;

        $data = [
            'action' => 'verify-token-answer',
            'user_id' => (string)$userId,
            'is_guest' => $user->isGuest,
            'ip_address' => PVM::getIPAddress(),
            'reference' => self::getRefToken($user, $otp),
            'contact_no_country_dialing_code' => $country->countries_international_dialing_code,
            'contact_no' => $hp,
            'answer' => $answer
        ];

        if(!$data['reference']) {
            return [
                'status' => 400,
                'message' => 'OTP expired, please click "Resend OTP" to get the new OTP'
            ];
        }

        $resp = self::api($data, ['user_id', 'reference', 'contact_no_country_dialing_code', 'contact_no', 'answer']);

        if (!isset($resp['status'])) {
            $reporter->reportToAdminViaSlack('Pipwave Verify Token API Failed, Request : ' . json_encode($data) . ', Response : ' . json_encode($resp), '');
            return [
                'status' => 400,
                'message' => 'Something went wrong, please try again later',
                'data' => [
                    'action' => 'support'
                ]
            ];
        }

        switch ($resp['status']) {
            case '200' :
                return [
                    'status' => 200,
                    'message' => 'Verify success'
                ];
            //Token Expired
            case '5051' :
                return [
                    'status' => 400,
                    'message' => 'OTP expired, please click "Resend OTP" to get the new OTP'
                ];
            //Incorrect answer provided
            case '5052':
                return [
                    'status' => 422,
                    'message' => 'Wrong OTP entered'
                ];

            //Session has expired or reference not found, please try again
            default:
                $reporter->reportToAdminViaSlack('Phone Verify Token Failed, Request : ' . json_encode($data) . ', Response : ' . json_encode($resp), '');

                return [
                    'status' => 400,
                    'message' => "[{$resp['status']}" . 'Something went wrong, please try again later'
                ];
        }
    }

    public static function updateOtpUser(Country $country, $hp)
    {
        $user = Yii::$app->user;

        $code = $country->countries_international_dialing_code;

        if ($user->isGuest) {
            return;
        }

        $otp = Otp::findOne([
            'country_id' => $country->countries_id,
            'hp' => $code . $hp
        ]);

        if (!$otp) {
            return;
        }

        $data = [
            'action' => 'update-otp-user',
            'user_id' => (string)$user->id,
            'is_guest' => false,
            'ip_address' => PVM::getIPAddress(),
            'reference' => $otp->reference_token,
        ];

        self::api($data, ['user_id', 'reference']);
    }

    public static function api($data, $signatureArray)
    {
        $url = 'one-time-pin';
        $params = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.pipwave');
        $client = new Client();

        $data['api_key'] = ArrayHelper::getValue($params, 'key');
        $data['timestamp'] = time();

        $signatureArray = array_merge($signatureArray, [
            'timestamp',
            'action',
            'api_key',
            'api_secret'
        ]);

        $data['signature'] = self::signature($data, $signatureArray);
        $url = ArrayHelper::getValue($params, 'url') . $url;
        $headers = [
            'Content-Type' => 'application/json',
            "x-api-key" => $data['api_key']
        ];

        try {
            $response = $client->post($url, [
                'headers' => $headers,
                'json' => $data,
                'http_errors' => false
            ]);

            return json_decode($response->getBody(), 1);
        } catch (RequestException $e) {
            $content = $e->getResponse() ? json_decode($e->getResponse()->getBody(), 1) : '';

            Yii::$app->reporter->reportToAdminViaSlack('Pipwave API failed', [
                'url' => $url,
                'status' => $e->getCode(),
                'message' => $e->getMessage(),
                'data' => $data,
                'content' => $content
            ]);

            return $content;
        }
    }

    protected static function signature($data, $signatureKeys)
    {
        $data['api_secret'] = ArrayHelper::getValue(Yii::$app->params, 'sso.providers.pipwave.secret');
        ksort($data);
        $s = "";
        foreach ($data as $key => $value) {
            if (in_array($key, $signatureKeys)) {
                $s .= $key . ':' . $value;
            }
        }
        return sha1($s);
    }

    private static function getRefToken($user, $otp)
    {
        return $user->isGuest ? $otp->reference_token : $otp->customers_otp_digit;
    }

}