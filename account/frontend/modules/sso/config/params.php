<?php

use common\components\ArrayHelper;

$env = require __DIR__ . '/params-local.php';

return [
    "sso" => [
        'recaptcha' => [
            'exception_ip' => [
                'testim' => ArrayHelper::getValue($env, 'RECAPTCHA_EXCEPTION_IP_TESTIM'),
                'office' => ArrayHelper::getValue($env, 'RECAPTCHA_EXCEPTION_IP_OFFICE'),
            ]
        ],
        'home_url' => ArrayHelper::getValue($env, 'HOME_URL'),
        'support_url' => 'https://helpdesk.offgamers.com/support/solutions',
        'mail' => [
            "subject_prefix" => ArrayHelper::getValue($env, 'MAIL_SUBJECT_PREFIX'),
            "sender_name" => ArrayHelper::getValue($env, 'MAIL_SENDER_NAME'),
            "sender_email" => ArrayHelper::getValue($env, 'MAIL_SENDER_EMAIL')
        ],
        'pvm' => [
            'reserve_ip_range' => '**********/10',
            'block_duration' => ArrayHelper::getValue($env, 'PVM_BLOCK_DURATION'),
            'countries' => ArrayHelper::getValue($env, 'PVM_COUNTRIES')
        ],
        'register' => [
            'ip' => [
                'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
            ],
            'session' => [
                'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
            ],
        ],
        'forget' => [
            'lifetime' => 24 * 60 * 60, //1day
            'ip' => [
                'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
            ],
            'session' => [
                'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
            ],
        ],
        'login' => [
            'key' => ArrayHelper::getValue($env, 'LOGIN_KEY'),
            'ip' => [
                'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
            ],
            'session' => [
                'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
            ],
        ],
        '2fa' => [
            'info_key' => 'totp_secret',
            'ip' => [
                'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
            ],
            'session' => [
                'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
            ],
        ],
        'region' => [
            "country_id" => 223,
            "language_id" => 1,
            "country_code" => "MY",
            "currency_code" => "USD",
            "language_code" => "en",
            "language_name" => "English",
        ],
        'cookies' => [
            'domain' => ArrayHelper::getValue($env, 'COOKIE_DOMAIN'),
            'lifetime' => 60 * 60 * 24 * 90,
        ],
        'reclaim_mobile' => [
            'period' => ArrayHelper::getValue($env, 'RECLAIM_MOBILE_PERIOD'),
        ],
        'otp' => [
            'testing' => ArrayHelper::getValue($env, 'OTP_TESTING', false),
            'otp_bypass_customers_id' => ArrayHelper::getValue($env,'BYPASS_OTP_CUSTOMERS_ID', []),
            'hp_verify_phone' => [
                'request_period' => ArrayHelper::getValue($env, 'OTP_REQUEST_PERIOD'),
                'ip' => [
                    'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                    'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
                ],
                'session' => [
                    'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                    'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
                ],
            ],
            'hp_device_pin' => [
                'request_period' => ArrayHelper::getValue($env, 'OTP_REQUEST_PERIOD'),
                'ip' => [
                    'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                    'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
                ],
                'session' => [
                    'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                    'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
                ],
            ],
            'email_device_pin' => [
                'request_period' => ArrayHelper::getValue($env, 'OTP_REQUEST_PERIOD'),
                'lifetime' => ArrayHelper::getValue($env, 'OTP_EMAIL_DEVICE_PIN_LIFETIME'),
                'ip' => [
                    'max_retries' => ArrayHelper::getValue($env, 'IP_MAX_RETRIES'),
                    'cache_lifetime' => ArrayHelper::getValue($env, 'IP_CACHE_LIFETIME'),
                ],
                'session' => [
                    'max_retries' => ArrayHelper::getValue($env, 'SESSION_MAX_RETRIES'),
                    'session_lifetime' => ArrayHelper::getValue($env, 'SESSION_CACHE_LIFETIME'),
                ],
            ],
        ],
        'clients' => [
            ArrayHelper::getValue($env, 'CLIENT_STORE_KEY') => [
                'url' => ArrayHelper::getValue($env, 'CLIENT_STORE_URL'),
                'token_url' => ArrayHelper::getValue($env, 'CLIENT_STORE_TOKEN_URL')
            ]
        ],
        'providers' => [
            'google' => [
                'recaptcha' => [
                    'url' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_URL'),
                    'js_url' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_JS_URL'),
                    'key' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_KEY'),
                    'secret' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_SECRET'),
                ],
                'recaptcha_challenge' => [
                    'url' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_URL'),
                    'js_url' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_JS_URL'),
                    'key' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_CHALLENGE_KEY'),
                    'secret' => ArrayHelper::getValue($env, 'GOOGLE_RECAPTCHA_CHALLENGE_SECRET'),
                ]
            ],
            'freshchat' => [
                'url' => ArrayHelper::getValue($env, 'FRESHCHAT_URL'),
                'key' => ArrayHelper::getValue($env, 'FRESHCHAT_KEY'),
            ],
            'pipwave' => [
                'url' => ArrayHelper::getValue($env, 'PIPWAVE_URL'),
                'key' => ArrayHelper::getValue($env, 'PIPWAVE_KEY'),
                'secret' => ArrayHelper::getValue($env, 'PIPWAVE_SECRET'),
            ],
            'mail_layer' => [
                'url' => ArrayHelper::getValue($env, 'MAIL_LAYER_URL'),
                'key' => ArrayHelper::getValue($env, 'MAIL_LAYER_KEY'),
            ],
            'freshdesk' => [
                'id_prefix' => ArrayHelper::getValue($env, 'FRESHDESK_ID_PREFIX', ''),
                'url' => ArrayHelper::getValue($env, 'FRESHDESK_URL'),
                'key' => ArrayHelper::getValue($env, 'FRESHDESK_KEY'),
                'redirect_url' => ArrayHelper::getValue($env, 'FRESHDESK_REDIRECT_URL'),
            ],
        ]
    ],

];