<?php

namespace app\modules\sso\controllers;

use app\modules\sso\components\Helper;
use common\components\ArrayHelper;
use Yii;
use yii\web\Controller;

class BaseController extends Controller
{
    protected $request;
    protected $params;
    protected $cache;
    protected $geoIp;
    protected $session;
    protected $user;
    protected $type;
    protected $hybridAuth;

    public function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config);

        $this->request = Yii::$app->request;
        $this->params = Yii::$app->params['sso'];
        $this->cache = Yii::$app->cache;
        $this->geoIp = Yii::$app->geoip;
        $this->session = Yii::$app->session;
        $this->user = Yii::$app->user;
        $this->hybridAuth = Yii::$app->hybridauth;
    }

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        if (isset($_SERVER['REQUEST_METHOD'])) {
            if (isset($_POST) && !empty($_POST)) {
                $_POST = Helper::purify($_POST);
            }

            if (isset($_GET) && !empty($_GET)) {
                $_GET = Helper::purify($_GET);
            }
        }

        return parent::beforeAction($action);
    }

    public function actionData()
    {
        return Helper::resp(200, 'Data', $this->getData());
    }

    public function setData($data, $type = null, $key = null)
    {
        if (!$type) {
            $type = $this->type;
        }

        if ($key) {
            $data = array_merge($this->getData($type), [
                $key => $data
            ]);
        }

        $this->session->set($type . '-data', $data);
    }

    public function getData($type = null, $key = null)
    {
        if (!$type) {
            $type = $this->type;
        }

        $data = $this->session->get($type . '-data', []);

        if (!$key) {
            if (ArrayHelper::getValue(Yii::$app->params, 'sso.otp.testing') && $this->session->has('test-otp')) {
                $data['test_otp'] = $this->session->get('test-otp');
            }

            return $data;
        }

        if (is_array($key)) {
            foreach ($key as $k) {
                if (ArrayHelper::getValue($data, $k)) {
                    return ArrayHelper::getValue($data, $k);
                }
            }

            return null;
        }

        return ArrayHelper::getValue($data, $key);
    }

    public function destroyData($type = null)
    {
        if (!$type) {
            $type = $this->type;
        }

        $this->session->remove($type . '-data');
        $this->session->remove('test-otp');
    }
}