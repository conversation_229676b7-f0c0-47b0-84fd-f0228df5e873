<?php

namespace app\modules\sso\controllers;

use app\modules\sso\components\Auth;
use app\modules\sso\components\PVM;
use common\components\ArrayHelper;

class BaseFrontendController extends BaseController
{
    public function beforeAction($action)
    {
        $skip = [
            'recaptcha',
        ];

        if ($this->user->isGuest && !in_array($this->action->id, $skip) && !PVM::checkCountry()) {
            Auth::setQuery();

            return $this->redirect(['/sso/recaptcha', 'redirect_url' => $this->action->id]);
        }

        return parent::beforeAction($action);
    }
}