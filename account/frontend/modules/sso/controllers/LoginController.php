<?php

namespace app\modules\sso\controllers;

use yii\helpers\Url;

class LoginController extends BaseFrontendController
{
    protected $type = 'login';

    public function actionTwoFaVerify()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/login/2fa-verify');
    }

    public function actionOtpVerify()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/login/otp-verify');
    }
}