<?php

namespace app\modules\sso\controllers;

use app\modules\sso\components\Auth;
use app\modules\sso\components\SMS;
use app\modules\sso\models\Customer;
use app\modules\sso\models\OtpManual;
use app\modules\sso\models\SnsConnection;
use Exception;
use Yii;
use yii\helpers\Url;

class SnsController extends BaseFrontendController
{
    protected $loginMethod = 'sns';

    public function beforeAction($action)
    {
        $this->hybridAuth->init();
        return parent::beforeAction($action);
    }

    private function errorHandle()
    {
        if ($this->hybridAuth->error) {
            $this->session->setFlash('error', $this->hybridAuth->error);
        }

        return $this->redirect('/sso/login');
    }

    public function login(Customer $customer)
    {
        if (!Auth::hpExist($customer)) {
            $this->setSessionData($customer, 'login');

            return $this->redirect('/sso/setup');
        }

        if (!Auth::isHpVerified($customer)) {
            $this->setSessionData($customer, 'login');
            $this->setSessionData($customer, 'setup');

            $country = $customer->country;
            $hp = $customer->customers_telephone;

            if (!SMS::isRequestOnCoolDown($country, $hp, 'verify_phone')
                && !Auth::isManualOtp($country, $hp, OtpManual::STATUS_NEW)) {
                SMS::requestOtp($country, $hp, 'verify_phone');
            }

            return $this->redirect('/sso/setup/otp-verify');
        }

        Auth::setAccountAsDormant($customer->info);
        Auth::login($customer, $this->loginMethod);

        return $this->redirect('/sso/login-all');
    }

    public function actionRequest()
    {
        if (!Yii::$app->hybridauth->login()) {
            return $this->errorHandle();
        }

        $snsUid = Yii::$app->hybridauth->getSession('ha_uid');

        if (!$snsUid) {
            return $this->errorHandle();
        }

        $snsConnection = SnsConnection::findOne([
            "provider" => $this->hybridAuth->getSession('ha_provider'),
            "provider_uid" => $this->hybridAuth->getSession('ha_uid')
        ]);

        if ($snsConnection && $snsConnection->customer->customers_status != '1') {
            $snsConnection->delete();
            $snsConnection = null;
        }

        if ($snsConnection) {
            return $this->login($snsConnection->customer);
        }

        return $this->register();
    }

    public function actionResponse()
    {
        $state_token = '';

        if (isset($_GET['hauth_done'])) {
            if (isset($_SESSION['state_token']) && isset($_GET['state']) && ((strtolower($_GET['hauth_done']) === 'google') || strtolower($_GET['hauth_done']) === 'facebook')) {
                $agent = (!isset($_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'], 'gonative') === false ? 'normal' : 'gonative');
                list($state_token, $mode) = explode(':', $_GET['state']);
                if ($mode == 'gonative' && $agent === 'normal' && $state_token !== $_SESSION['state_token'] && !empty(Yii::$app->params['gonative.custom.url.scheme'])) {
                    $url = Yii::$app->params['gonative.custom.url.scheme'] . '.' . Url::current([], true);
                    return $this->render('@app/modules/sso/views/login/native', ['url' => $url]);
                }
            }
            $_REQUEST['hauth_done'] = $_GET['hauth_done'];
        }

        try {
            // Prevent SSO Sign-in when state token mismatch
            if (isset($_GET['hauth_done']) && strtolower($_GET['hauth_done']) === 'google' && ($state_token !== $_SESSION['state_token'])) {
                return $this->redirect('/sso/login');
            }

            Yii::$app->hybridauth->endPoint();
        } catch (Exception $e) {
            Yii::$app->hybridauth->errorMessage($e);
            Yii::$app->session->setFlash('error', Yii::$app->hybridauth->error);
            return $this->redirect('/sso/login');
        }
    }

    public function actionSignUp()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/register/sns/hp', ['provider' => $this->session->get('ha_provider')]);
    }

    public function actionOtpVerify()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/register/sns/otp-verify');
    }

    public function actionAccountCreate()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/register/sns/account-create');
    }

    private function register()
    {
        $this->setData([
            'type' => $this->loginMethod,
            'firstname' => $this->session->get('ha_firstName'),
            'lastname' => $this->session->get('ha_lastName'),
            'email' => $this->session->get('ha_email'),
        ], 'register');

        return $this->redirect('/sso/sns/sign-up');
    }

    private function setSessionData(Customer $customer, $type)
    {
        $hp = $customer->customers_telephone;
        $email = $customer->customers_email_address;

        $this->setData([
            'via' => 'sns',
            'customer' => $customer->toArray([
                'customers_id',
            ]),
            'country' => $customer->country->toArray([
                'countries_id',
                'countries_international_dialing_code'
            ]),
            'hp' => $hp,
            'hp_encoded' => Auth::maskHp($hp),
            'email_encoded' => Auth::maskEmail($email)
        ], $type);
    }
}