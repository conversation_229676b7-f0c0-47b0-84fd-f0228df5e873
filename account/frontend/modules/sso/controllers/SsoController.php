<?php

namespace app\modules\sso\controllers;

use app\modules\sso\components\Auth;
use app\modules\sso\components\Helper;
use app\modules\sso\components\providers\Freshdesk;
use app\modules\sso\components\RememberMe;
use app\modules\sso\models\Customer;
use app\modules\sso\models\ShassoSsoToken;
use common\components\ArrayHelper;
use common\components\SSOCom;
use common\models\ShassoClient;
use frontend\components\UserIdentity;
use Yii;
use yii\helpers\Url;

class SsoController extends BaseFrontendController
{
    public function render($view, $params = [])
    {
        if ($this->action->id == 'logout'
            || ($this->action->id == 'login-all' && $this->session->get('login_from') == 'login')) {
            $this->layout = 'min';
        }

        return parent::render($view, $params);
    }

    public function actionRecaptcha()
    {
        return $this->render('@app/modules/sso/views/recaptcha');
    }

    public function actionLogin()
    {
        if (!$this->user->isGuest) {
            if ($this->request->get('provider') == 'freshdesk') {
                $customer = Customer::findOne(['customers_id' => Yii::$app->user->id]);

                return Freshdesk::redirect($customer, $this->request->get());
            }

            return $this->redirect('/overview/index');
        }

        Auth::setQuery();


        RememberMe::login();

        return $this->render('@app/modules/sso/views/login/main');
    }

    public function actionSignUp()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        Auth::setQuery();

        return $this->render('@app/modules/sso/views/register/main');
    }

    public function actionSetup()
    {
        return $this->render('@app/modules/sso/views/login/setup');
    }

    public function actionOtpVerify()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/register/otp-verify');
    }

    public function actionAccountExist()
    {
        return $this->render('@app/modules/sso/views/register/account-exist');
    }

    public function actionAccountCreate()
    {
        if (!$this->user->isGuest) {
            return $this->redirect(Url::to(['/overview/index'], true));
        }

        return $this->render('@app/modules/sso/views/register/account-create');
    }

    public function actionForget()
    {
        return $this->render('@app/modules/sso/views/forget-password/main');
    }

    public function actionReset()
    {
        return $this->render('@app/modules/sso/views/forget-password/reset');
    }

    public function actionLoginAll()
    {
        $respData = [];
        $redirectUrl = Url::to(Yii::$app->homeUrl[0], true);
        $tokens = [];
        $token = null;
        $query = $this->session->get('query', $this->request->get());

        $provider = ArrayHelper::getValue($query, 'provider');
        $service = ArrayHelper::getValue($query, 'service');
        $origin = ArrayHelper::getValue($query, 'origin');

        if (!$this->request->isGet) {
            return $this->redirect('/sso/login');
        }

        if ($this->user->isGuest) {
            return $this->redirect('/sso/login');
        }

        if ($provider == 'freshdesk') {
            $customer = Customer::findOne(['customers_id' => Yii::$app->user->id]);
            return Freshdesk::redirect($customer, $query);
        }

        foreach ($this->params['clients'] as $id => $client) {
            $token = md5(
                Helper::createRandomValue(10)
                . date('YmdHis')
                . ArrayHelper::getValue($this->params, 'login.key')
                . $id
                . $this->user->id
            );

            $data = [
                'sso_token' => $token,
                'sess_id' => $this->session->id,
                'client_id' => $id,
                'user_id' => $this->user->id,
                'login_method' => ucwords(str_replace("_", " ", $this->user->login_method)),
                'expiry' => Helper::now('+', (24 * 60 * 60))
            ];

            $ssoToken = new ShassoSsoToken();
            $ssoToken->attributes = $data;
            $ssoToken->save();

            $respData[] = [
                'url' => $client['url'] . $client['token_url'] . "?" . http_build_query(['S3ID' => $token])
            ];

            $tokens[$client['url']] = $token;
        }

        if ($service && $origin) {
            $clientUrl = ArrayHelper::getValue($this->params, 'clients.' . $service . '.url');

            if ($clientUrl && $clientUrl == $origin) {
                $redirectUrl = $origin;
                $token = $tokens[$clientUrl];
            }
        } else {
            if (preg_match("~^(?:f|ht)tps?://~i", $this->user->returnUrl)) {
                if (!preg_match("/sso/i", $this->user->returnUrl)) {
                    $redirectUrl = Yii::$app->user->returnUrl;
                }
            } else {
                $redirectUrl = Url::to(Yii::$app->user->returnUrl, true);
            }
        }

        $rememberMeCookies = Yii::$app->request->cookies->getValue('ogm');

        if ($origin) {
            if (ArrayHelper::getValue($rememberMeCookies, 'un')
                && ArrayHelper::getValue($rememberMeCookies, 'uc')) {
                $query['S3RM'] = 1;
            }

            if ($token) {
                $query['S3ID'] = $token;
            }
        }

        $this->session->remove('query');

        foreach ($query as $k => $v) {
            if (is_array($v)) {
                $data = [
                    'GET' => $_GET,
                    'QUERY' => $this->session->get('query', $this->request->get()),
                    'PROCESSED_QUERY' => $query,
                    'AFFECTED_KEY' => $k,
                    'SESSION' => $_SESSION
                ];
                Yii::$app->reporter->reportToAdminViaSlack("Debug Script : Array Element on HTTP Query", json_encode($data));
                continue;
            }
            $query[$k] = html_entity_decode(urldecode($v));
        }

        if ($query) {
            $redirectUrl .= '?' . http_build_query($query);
        }

        if ($this->session->get('login_from') == 'register') {
            return $this->render('@app/modules/sso/views/register/success', [
                'portals' => $respData,
                'origin' => $redirectUrl
            ]);
        }

        return $this->render('@app/modules/sso/views/login/loading', [
            'portals' => $respData,
            'origin' => $redirectUrl
        ]);
    }

    //Deprecated
    public function actionLogout()
    {
        $f_data = array();
        $req_arr = array();

        // 3rd party portal URL query
        $req_uri = SSOCom::clientURLQuery();
        parse_str($req_uri, $req_arr);

        if (isset($req_arr['origin']) && !empty($req_arr['origin'])) {
            $f_data['origin'] = urldecode(urldecode($req_arr['origin'])) . "?" . http_build_query($req_arr);
        } else {
            if (isset($_GET["host_url"])) {
                // freshdesk auto append param
                $f_data['origin'] = (!preg_match("~^(?:f|ht)tps?://~i", $_GET["host_url"]) ? "http://" : "") . $_GET["host_url"];
            } else {
                $f_data['origin'] = Url::to(Yii::$app->user->loginUrl, true);
            }
        }

        if (Yii::$app->user->isGuest) {
            $identity = new UserIdentity();
            $identity->logout();
            Yii::$app->hybridauth->logout();
            Yii::$app->user->logout();

            return $this->redirect($f_data['origin']);
        } else {
            $m_client = ShassoClient::find()->where("delete_token_url <> ''")->all();
            foreach ($m_client as $_num => $_data) {
                if (isset($_data->client_id)) {
                    if (isset($req_arr['service']) && ($req_arr['service'] == $_data->client_id)) {
                    } else {
                        $f_data['portals'][] = array(
                            'url' => $_data->delete_token_url
                        );
                    }
                }
            }

            $identity = new UserIdentity();
            $identity->logout();
            Yii::$app->hybridauth->logout();
            Yii::$app->user->logout();

            return $this->render('@app/modules/sso/views/login/loading', $f_data);
        }
    }

    //Deprecated
    public function actionIndex()
    {
        $req_arr = array();
        $url = "sso/login";

        // 3rd party portal URL query
        $req_uri = SSOCom::clientURLQuery();
        parse_str($req_uri, $req_arr);

        Auth::setQuery();

        if (!empty($req_arr) && isset($req_arr["action"])) {
            $action = strtolower($req_arr["action"]);
            switch ($action) {
                case "login":
                    if (isset($req_arr["sns"])) {
                        $sns = str_replace("+", "", ucwords($req_arr["sns"]));
                        if (Yii::$app->hybridauth->isAllowedProvider($sns)) {
                            $url = "sso/xpress";
                            $req_arr["ha_provider"] = $sns;
                        }
                    } elseif (Yii::$app->user->isGuest) {
                        $url = "sso/login";
                    } else {
                        $url = "sso/login-all";
                        if (isset($req_arr['origin'])) {
                            $req_arr['partner-login'] = true;
                        }
                    }
                    break;

                case "signup":
                    $url = "sso/sign-up";
                    break;

                case "logout":
                    $url = "sso/logout";
                    break;
            }
        }
        return $this->redirect(Url::to([$url], true) . (empty($req_uri) ? "" : "?" . http_build_query($req_arr)));
    }
}