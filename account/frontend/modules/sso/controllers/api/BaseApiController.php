<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\controllers\BaseController;
use Yii;
use yii\web\Response;

class BaseApiController extends BaseController
{
    public function beforeAction($action)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        Yii::$app->response->on('beforeSend', function ($event) {
            $response = $event->sender;
            if ($response->statusCode >= 500 && $response->isSuccessful === false && $response->data !== null) {
                $response->statusCode = 400;
                $response->data = [
                    'status' => 400,
                    'message' => 'Something went wrong, please try again',
                    'data' => [
                        'action' => 'support'
                    ],
                ];
            }
        });

        return parent::beforeAction($action);
    }

    public function afterAction($action, $result)
    {
        return parent::afterAction($action, $result); // TODO: Change the autogenerated stub
    }
}