<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Helper;
use app\modules\sso\models\Country;

class CountryController extends BaseApiController
{
    public function actionIndex()
    {
        $countries = $this->cache->get('countries');

        if ($countries) {
            return Helper::resp(200, 'Country list', $countries);
        }

        $countries = [];

        $db_country = Country::find()
            ->select(['countries_id', 'countries_name', 'countries_iso_code_2', 'countries_international_dialing_code', 'countries_display'])
            ->orderBy(['countries_name' => SORT_ASC])
            ->asArray()
            ->all();

        foreach ($db_country as $index => $country) {
            $country['flag_index'] = $index;
            if($country['countries_display']){
                $countries[] = $country;
            }
        }

        $this->cache->set('countries', $countries);

        return Helper::resp(200, 'Country list', $countries);
    }

    public function actionCurrent()
    {
        $data = Helper::getUserPreference();

        return Helper::resp(200, 'Current country', $data['country']);
    }
}