<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Auth;
use app\modules\sso\components\Email;
use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use app\modules\sso\models\Customer;
use app\modules\sso\models\ShassoUserToken;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\ForgetForm;
use frontend\modules\sso\forms\ResetForm;
use Yii;
use yii\helpers\Url;

class ForgetController extends BaseApiController
{
    protected $type = 'forget';

    public function actionIndex()
    {
        $form = new ForgetForm();
        $form->attributes = $this->request->post();

        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request.');
        }

        $forgetParam = ArrayHelper::getValue($this->params, 'forget');

        $pvmVerify = PVM::securityCheck($this->type, $forgetParam, 1, $this->request->post('recaptcha_token'), 'challenge');

        if (!$pvmVerify) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        if (!Auth::validateHp(
            $this->request->post('type'),
            $this->request->post('country_id'),
            $this->request->post('username')
        )) {
            return Helper::resp(422, 'Validation failed', [
                'username' => ['Invalid mobile number']
            ]);
        }

        $customer = $this->getCustomer();

        if (!$customer) {
            //Prevent fraud user test email exist
            return Helper::resp(301, 'Success sent email', [
                'redirect_url' => 'forget/sent'
            ]);
        }

        if (!$this->verifyLoginInfo($customer)) {
            //Prevent fraud user test email exist
            return Helper::resp(301, 'Success sent email', [
                'redirect_url' => 'forget/sent'
            ]);
        }

        if ($customer->customers_status != '1') {
            return Helper::resp(301, 'Success sent email', [
                'redirect_url' => 'forget/sent'
            ]);
        }

        $token = $this->saveUserToken($customer);
        $custName = implode(" ", [
            $customer->customers_firstname,
            $customer->customers_lastname
        ]);

        $title = $this->params['mail']['subject_prefix']
            . Yii::t('sso', 'EMAIL_RESET_PASSWORD_TITLE');

        $data = [
            "title" => $title,
            'cname' => $custName,
            'link' => Url::to(['sso/reset', 'token' => $token], true),
        ];

        Email::send(
            $customer,
            $title,
            'how_to_recover_your_password-html',
            'how_to_recover_your_password-text',
            $data
        );

        return Helper::resp(301, 'Success sent email', [
            'redirect_url' => 'forget/sent'
        ]);
    }

    public function actionReset()
    {
        $form = new ResetForm();
        $form->attributes = $this->request->post();

        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request.');
        }

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        $userToken = ShassoUserToken::findOne([
            "token_value" => $this->request->post('token'),
            "token_type" => 'reset_password'
        ]);

        if (!$userToken) {
            return Helper::resp(301, 'The link is expired', [
                'redirect_url' => 'reset/invalid'
            ]);
        }

        if (Helper::isExpired($userToken->expiry_date)) {
            $userToken->delete();

            return Helper::resp(301, 'The link is expired', [
                'redirect_url' => 'reset/invalid'
            ]);
        }

        $customer = Customer::findOne([
            'customers_id' => $userToken->user_id
        ]);

        if (!$customer) {
            return Helper::resp(400, 'Something went wrong, please try to request again');
        }

        $pass = Helper::purify($this->request->post('password'));
        $pass = Auth::encryptPassword($pass);
        $customer->customers_password = $pass;
        $customer->save();

        $userToken->delete();

        PVM::clear($this->type);

        return Helper::resp(301, 'Success reset password', [
            'redirect_url' => 'reset/success'
        ]);
    }

    private function saveUserToken(Customer $customer)
    {
        $type = 'reset_password';
        $userToken = ShassoUserToken::findOne([
            "user_id" => $customer->customers_id,
            "token_type" => $type
        ]);

        $date = Helper::now();

        if (!$userToken) {
            $userToken = new ShassoUserToken();
            $userToken->attributes = [
                "user_id" => $customer->customers_id,
                "token_type" => $type,
                'created_date' => $date,
            ];
        }

        $token = Helper::encrypt($customer->customers_id, strtotime($date));

        $data = [
            'token_value' => $token,
            'expiry_date' => Helper::now('+', $this->params['forget']['lifetime'])
        ];

        $userToken->attributes = $data;
        $userToken->save();

        return $token;
    }

    protected function getCustomer()
    {
        if (!$this->user->isGuest) {
            return Customer::findOne([
                'customers_id' => $this->user->id,
            ]);
        }

        if ($this->request->post('type') == 'email') {
            return Customer::findOne(['customers_email_address' => $this->request->post('username')]);
        }

        return Auth::getVerifiedCustomerByHp($this->request->post('country_id'), $this->request->post('username'));
    }

    private function verifyLoginInfo(Customer $customer)
    {
        if ($this->user->isGuest) {
            return true;
        }

        if ($this->request->post('type') == 'email') {
            return $customer->customers_email_address == $this->request->post('username');
        }

        if ($customer->customers_country_dialing_code_id != $this->request->post('country_id')) {
            return false;
        }

        if ($customer->customers_telephone != $this->request->post('username')) {
            return false;
        }

        return true;
    }
}