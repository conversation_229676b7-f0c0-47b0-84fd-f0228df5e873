<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Auth;
use app\modules\sso\components\Email;
use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use app\modules\sso\components\RememberMe;
use app\modules\sso\components\SMS;
use app\modules\sso\components\TwoFA;
use app\modules\sso\models\Customer;
use app\modules\sso\models\CustomerLoginIpHistory;
use app\modules\sso\models\OtpManual;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\LoginForm;
use Yii;

class LoginController extends BaseApiController
{
    protected $type = 'login';

    public function actionIndex()
    {
        $form = new LoginForm();
        $form->attributes = $this->request->post();
        $loginParam = ArrayHelper::getValue($this->params, $this->type);

        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request.');
        }

        $pvmVerify = PVM::securityCheck($this->type, $loginParam, 1, $this->request->post('recaptcha_token'), 'challenge');

        if (!$pvmVerify) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        if (!Auth::validateHp(
            $this->request->post('type'),
            $this->request->post('country_id'),
            $this->request->post('username')
        )) {
            return Helper::resp(422, 'Validation failed', [
                'username' => ['Invalid mobile number']
            ]);
        }

        $customer = $this->getCustomer();

        $textType = $this->request->post('type') == 'email'
            ? $this->request->post('type')
            : 'mobile number';

        if (!$customer) {
            return Helper::resp(400, 'Wrong ' . $textType . ' or password');
        }

        if (!Auth::validatePassword(Helper::purify($this->request->post('password')), $customer->customers_password)) {
            return Helper::resp(400, 'Wrong ' . $textType . ' or password');
        }

        PVM::clear($this->type);

        if ($customer->customers_status != '1') {
            return Helper::resp(400, 'Account is deactivated', [
                'action' => 'support'
            ]);
        }

        if (!Auth::hpExist($customer)) {
            $this->setSessionData($customer);

            return Helper::resp(301, 'Enter new phone number', [
                'redirect_url' => 'setup'
            ]);
        }

        if (!Auth::isHpVerified($customer)) {
            $this->setSessionData($customer);
            $this->setSessionData($customer, 'setup');

            $country = $customer->country;
            $hp = $customer->customers_telephone;

            if (!SMS::isRequestOnCoolDown($country, $hp, 'verify_phone')
                && !Auth::isManualOtp($country, $hp, OtpManual::STATUS_NEW)) {
                SMS::requestOtp($country, $hp, 'verify_phone');
            }

            return Helper::resp(301, 'Verify phone number', [
                'redirect_url' => 'setup/otp-verify'
            ]);
        }

        if (TwoFA::enabled($customer)) {
            $this->setSessionData($customer);

            return Helper::resp(301, 'Required 2fa', [
                'customer' => $customer,
                'redirect_url' => 'login/two-fa-verify'
            ]);
        }

        if ($this->missMatchCookies($customer) || $this->ipIsDifferent($customer)) {
            if (!Email::isRequestOnCoolDown($customer)) {
                $resp = Email::requestOtp($customer);

                if ($resp['status'] != 200) {
                    return Helper::resp($resp['status'], $resp['message']);
                }
            }

            $this->setSessionData($customer);

            return Helper::resp(301, 'Required new device pin', [
                'customer' => $customer,
                'redirect_url' => 'login/otp-verify'
            ]);
        }

        $userPreference = Helper::getUserPreference();

        Auth::setAccountAsDormant($customer->info);
        Auth::saveUserPreference($customer, $userPreference);

        if ($this->request->post('remember_me')) {
            RememberMe::store($customer);
        }

        Auth::login($customer);

        return Helper::resp(301, 'Login success', [
            'redirect_url' => 'login-all'
        ]);
    }

    public function actionSession()
    {
        $via = $this->getData($this->type, 'via') ?: $this->type;
        $type = $this->request->post('type', $this->type);
        $customer = $this->getData($type, 'customer');
        $otpVerified = $this->getData($type, 'otp_verified');
        $rememberMe = $this->getData($type, 'remember_me');

        if (!$customer || !$otpVerified) {
            return Helper::resp(301, 'Customer not found', [
                'redirect_url' => 'login'
            ]);
        }

        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);

        $userPreference = Helper::getUserPreference();

        Auth::setAccountAsDormant($customer->info);
        Auth::saveUserPreference($customer, $userPreference);

        if ($rememberMe) {
            RememberMe::store($customer);
        }

        Auth::login($customer, $via);

        $this->destroyData($this->type);

        return Helper::resp(301, 'Login success', [
            'redirect_url' => 'login-all'
        ]);
    }

    protected function getCustomer()
    {
        if ($this->request->post('type') == 'email') {
            return Customer::findOne(['customers_email_address' => $this->request->post('username')]);
        }

        return Auth::getVerifiedCustomerByHp($this->request->post('country_id'), $this->request->post('username'));
    }

    private function setAccountAsDormant($custInfo)
    {
        if ($custInfo->customer_info_account_dormant) {
            return;
        }

        if ($custInfo->customers_info_number_of_logons <= 0) {
            return;
        }

        $_diff_day = (time() - strtotime($custInfo->customers_info_date_of_last_logon)) / 86400;

        if ($_diff_day <= 90) {
            return;
        }

        $custInfo->customer_info_account_dormant = 1;
        $custInfo->save();
    }

    private function missMatchCookies(Customer $customer)
    {
        if (!isset(Yii::$app->request->cookies['sa']->value['dc'])) {
            return true;
        }

        $cookval = explode("|", Yii::$app->request->cookies['sa']->value['dc']);
        $loginKey = ArrayHelper::getValue($this->params, 'login.key');

        for ($i = 0, $cnt = count($cookval); $cnt > $i; $i++) {
            $key = explode(".", $cookval[$i]);
            if (isset($key[1]) && ($key[1] == md5($key[0] . $customer->customers_id . $loginKey))) {
                return false;
            }
        }

        return true;
    }

    private function ipIsDifferent($customer)
    {
        $custLoginIpHistory = CustomerLoginIpHistory::find()->where([
            'customers_id' => $customer->customers_id,
        ])->orderBy('customers_login_date DESC')->one();

        if (!$custLoginIpHistory) {
            return true;
        }

        if (($this->geoIp->countryCodeByIP($custLoginIpHistory->customers_login_ip)
            != $this->geoIp->countryCodeByIP())) {
            return true;
        }

        return false;
    }

    private function setSessionData(Customer $customer, $type = null)
    {
        $type = $type ?: $this->type;
        $hp = $customer->customers_telephone;
        $email = $customer->customers_email_address;
        $data = [
            'customer' => $customer->toArray([
                'customers_id',
            ]),
            'remember_me' => $this->request->post('remember_me'),
            'country' => $customer->country->toArray([
                'countries_id',
                'countries_international_dialing_code'
            ]),
            'hp' => $hp,
            'hp_encoded' => Auth::maskHp($hp),
            'email_encoded' => Auth::maskEmail($email)
        ];

        $this->setData($data, $type);
    }
}