<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Email;
use app\modules\sso\components\Helper;
use app\modules\sso\components\SMS;
use app\modules\sso\components\TwoFA;
use app\modules\sso\models\Country;
use app\modules\sso\models\Customer;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\OtpForm;
use Yii;

class OtpController extends BaseApiController
{
    public function actionSend()
    {
        $via = $this->request->post('via', 'hp');

        if ($via == 'hp') {
            return $this->sendViaHp();
        }

        return $this->sendViaEmail();
    }

    public function actionSendInit()
    {
        $via = $this->request->post('via', 'hp');

        if ($via == 'hp') {
            return $this->sendViaHp(1);
        }

        return $this->sendViaEmail(1);
    }

    public function actionVerify()
    {
        $form = new OtpForm();
        $form->attributes = $this->request->post();

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        $via = $this->request->post('via', 'hp');
        $customer = $this->getData($form->type, 'customer');

        if (isset($customer['customers_id']) && in_array($customer['customers_id'], ArrayHelper::getValue(Yii::$app->params, 'sso.otp.otp_bypass_customers_id', []))) {
            $this->setData(1, $form->type, 'otp_verified');

            switch ($form->type) {
                case 'register':
                    $redirect = 'sso/sign-up';
                    break;
                case 'setup':
                    $redirect = 'sso/setup';
                    break;
                default:
                    $redirect = false;
                    break;
            }

            if ($redirect) {
                return Helper::resp(301, 'Success', [
                    'redirect_url' => $redirect
                ]);
            } else {
                return Helper::resp(200, 'Verified Success');
            }
        }

        if ($via == 'hp') {
            return $this->verifyViaHp();
        }

        return $this->verifyViaEmail();
    }

    private function sendViaHp($checkCooldown = 0)
    {
        $type = $this->request->post('type');
        $country = $this->getData($type, 'country');
        $hp = $this->getData($type, ['username', 'hp']);

        if (!$country || !$hp) {
            if ($type == 'register') {
                return Helper::resp(301, 'Phone number not found', [
                    'redirect_url' => 'sso/sign-up'
                ]);
            }

            return Helper::resp(400, 'Phone number not found');
        }

        $method = $type == 'login' ? 'device_pin' : 'verify_phone';

        if ($type == 'login') {
            $customer = $this->getData($type, 'customer');

            if (!$customer) {
                return Helper::resp(301, 'Customer not found', [
                    'redirect_url' => 'sso/login'
                ]);
            }
        }

        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        if ($checkCooldown && SMS::isRequestOnCoolDown($country, $hp, $method)) {
            return Helper::resp(200, 'Request is on cooldown');
        }

        $resp = SMS::requestOtp($country, $hp, $method);
        $data = ArrayHelper::getValue($resp, 'data', []);

        if (ArrayHelper::getValue(Yii::$app->params, 'sso.otp.testing') && $this->session->has('test-otp')) {
            $data['test_otp'] = $this->session->get('test-otp');
        }


        return Helper::resp($resp['status'], $resp['message'], $data);
    }

    private function sendViaEmail($checkCooldown = 0)
    {
        $type = $this->request->post('type');
        $customer = $this->getData($type, 'customer');

        if (!$customer) {
            if ($type == 'login') {
                return Helper::resp(301, 'Customer not found', [
                    'redirect_url' => 'sso/login'
                ]);
            }

            return Helper::resp(400, 'Customer not found');
        }

        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);

        if ($checkCooldown && Email::isRequestOnCoolDown($customer)) {
            return Helper::resp(200, 'Request is on cooldown');
        }

        $resp = Email::requestOtp($customer);

        $data = [];

        if (ArrayHelper::getValue(Yii::$app->params, 'sso.otp.testing') && $this->session->has('test-otp')) {
            $data['test_otp'] = $this->session->get('test-otp');
        }


        return Helper::resp($resp['status'], $resp['message'], $data);
    }

    private function verifyViaHp()
    {
        $type = $this->request->post('type');
        $country = $this->getData($type, 'country');
        $hp = $this->getData($type, ['username', 'hp']);

        if (!$country || !$hp) {
            if ($type == 'register') {
                return Helper::resp(301, 'Phone number not found', [
                    'redirect_url' => 'sign-up'
                ]);
            } elseif ($type == 'setup') {
                return Helper::resp(301, 'Phone number not found', [
                    'redirect_url' => 'setup'
                ]);
            }

            return Helper::resp(400, 'Phone number not found');
        }

        $usage = $type == 'login' ? 'device_pin' : 'verify_phone';
        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        $resp = SMS::verifyOtp($country, $hp, $this->request->post('answer'), $usage);

        if ($resp['status'] == 422) {
            return Helper::resp(422, 'Validation failed', [
                'answer' => [$resp['message']]
            ]);
        }

        if ($resp['status'] == 200) {
            $this->setData(1, $type, 'otp_verified');
        }

        if (isset($resp['data'])) {
            return Helper::resp($resp['status'], $resp['message'], $resp['data']);
        }

        return Helper::resp($resp['status'], $resp['message']);
    }

    private function verifyViaEmail()
    {
        $type = $this->request->post('type');
        $customer = $this->getData($type, 'customer');
        $answer = $this->request->post('answer');

        if (!$customer) {
            if ($type == 'login') {
                return Helper::resp(301, 'Customer not found', [
                    'redirect_url' => 'sso/login'
                ]);
            }

            return Helper::resp(400, 'Customer not found');
        }

        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);

        if (TwoFA::enabled($customer)) {
            if ($type == 'login') {
                return Helper::resp(301, 'Something went wrong, please try again', [
                    'redirect_url' => 'sso/login'
                ]);
            }

            return Helper::resp(400, 'Something went wrong, please try again');
        }

        $resp = Email::verifyOtp($customer, $answer);

        if ($resp['status'] == 422) {
            return Helper::resp(422, 'Validation failed', [
                'answer' => [$resp['message']]
            ]);
        }

        if ($resp['status'] == 200) {
            $this->setData(1, $type, 'otp_verified');
        }

        if (isset($resp['data'])) {
            return Helper::resp($resp['status'], $resp['message'], $resp['data']);
        }

        return Helper::resp($resp['status'], $resp['message']);
    }
}