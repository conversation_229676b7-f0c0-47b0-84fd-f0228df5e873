<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;

class RecaptchaController extends BaseApiController
{
    public function actionIndex()
    {
        $resp = PVM::checkRecaptcha(1, $this->request->post('recaptcha_token'), 'challenge');

        if (!$resp) {
            return Helper::resp(400, 'Wrong validation, please refresh and try again');
        }

        return Helper::resp(301, 'Success validate', [
            'redirect_url' => $this->request->post('redirect_url', 'login')
        ]);
    }
}