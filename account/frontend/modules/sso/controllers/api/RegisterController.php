<?php

namespace app\modules\sso\controllers\api;


use app\modules\sso\components\Auth;
use app\modules\sso\components\Email;
use app\modules\sso\components\Helper;
use app\modules\sso\components\providers\PipwaveOtp;
use app\modules\sso\components\PVM;
use app\modules\sso\components\SMS;
use app\modules\sso\models\AddressBook;
use app\modules\sso\models\Country;
use app\modules\sso\models\Customer;
use app\modules\sso\models\CustomerInfo;
use app\modules\sso\models\CustomerRemarksHistory;
use app\modules\sso\models\Otp;
use app\modules\sso\models\OtpManual;
use app\modules\sso\models\SnsConnection;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\CreateAccountForm;
use frontend\modules\sso\forms\RegisterForm;
use Yii;

class RegisterController extends BaseApiController
{
    protected $type = 'register';

    public function actionIndex()
    {
        $form = new RegisterForm();
        $form->attributes = $this->request->post();
        $registerParams = ArrayHelper::getValue($this->params, $this->type);

        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request.');
        }

        $captchaType = 'challenge';

        if ($this->session->get('ha_uid')) {
            $captchaType = 'invisible';
        }

        $pvmVerify = PVM::securityCheck($this->type, $registerParams, 1, $this->request->post('recaptcha_token'), $captchaType);

        if (!$pvmVerify) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        $country = Country::findOne([
            'countries_id' => $this->request->post('country_id')
        ]);

        if (!$country) {
            return Helper::resp(400, 'Invalid country code');
        }

        $hp = $this->request->post('username');

        if (!Auth::validateHp(
            'hp',
            $country,
            $this->request->post('username')
        )) {
            return Helper::resp(422, 'Validation failed', [
                'username' => ['Invalid mobile number']
            ]);
        }

        if (!SMS::isRequestOnCoolDown($country, $hp, 'verify_phone')
            && !Auth::isManualOtp($country, $hp, OtpManual::STATUS_NEW)) {
            SMS::requestOtp($country, $hp, 'verify_phone');
        }

        $type = $this->getData($this->type, 'type') ?: 'register';

        $sessionData = array_merge($this->getData(), $this->request->post(), [
            'type' => $type,
            'country' => $country->toArray([
                'countries_id',
                'countries_international_dialing_code'
            ]),
            'username_encoded' => Auth::maskHp($hp)
        ]);

        $redirectTo = 'otp-verify';

        if ($this->getData($this->type, 'type') == 'sns') {
            $redirectTo = 'sns/otp-verify';
        }

        //destroy whole register data and set again
        $this->destroyData($this->type);
        if(isset($sessionData["otp_verified"])){
            unset($sessionData["otp_verified"]);
        }
        $this->setData($sessionData);

        return Helper::resp(301, 'Success', [
            'redirect_url' => $redirectTo
        ]);
    }

    public function actionReclaim()
    {
        $country = $this->getData($this->type, 'country');
        $hp = $this->getData($this->type, 'username');

        if (!$country || !$hp) {
            return Helper::resp(301, 'Phone number not found', [
                'redirect_url' => 'sign-up'
            ]);
        }

        if (!$this->getData($this->type, 'otp_verified')) {
            return Helper::resp(301, 'OTP verify not found', [
                'redirect_url' => 'sign-up'
            ]);
        }

        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        if (Auth::reclaimIsOnCooldown($country, $hp)) {
            return Helper::resp(400, 'This mobile number cant be reclaim, please contact support for verification', [
                'action' => 'support'
            ]);
        }

        $this->setData(1, $this->type, 'reclaim_mobile');

        return Helper::resp(301, 'Reclaimed success', [
            'redirect_url' => $this->getRedirectUrl()
        ]);
    }

    public function actionCheckAccount()
    {
        $country = $this->getData($this->type, 'country');
        $hp = $this->getData($this->type, 'username');

        if (!$country || !$hp) {
            return Helper::resp(301, 'Phone number not found', [
                'redirect_url' => 'sign-up'
            ]);
        }

        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        $customer = Auth::getVerifiedCustomerByHp($country->countries_id, $hp);

        if ($customer) {
            $email = $customer->customers_email_address;

            $sessionData = $this->getData();
            $sessionData['email'] = $email;
            $sessionData['email_encoded'] = Auth::maskEmail($email);

            $this->setData($sessionData);

            return Helper::resp(301, 'Phone number exist', [
                'redirect_url' => 'account-exist'
            ]);
        }

        return Helper::resp(301, 'Create account', [
            'redirect_url' => $this->getRedirectUrl()
        ]);
    }

    public function actionCreateAccount()
    {
        $pvmVerify = PVM::checkRecaptcha(1, $this->request->post('recaptcha_token'));

        if (!$pvmVerify) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        $email = strtolower($this->request->post('email'));

        //Method Validate
        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request');
        }

        //Form Validate
        $form = new CreateAccountForm();
        $form->attributes = $this->request->post();

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        //Data Validate
        $country = $this->getData($this->type, 'country');
        $hp = $this->getData($this->type, 'username');
        $type = $this->getData($this->type, 'type');
        $otp_verified = $this->getData($this->type, 'otp_verified');

        if (!$otp_verified) {
            return Helper::resp(301, 'Validation failed', [
                'redirect_url' => 'sign-up'
            ]);
        }
        
        if (!$country || !$hp) {
            return Helper::resp(301, 'Phone number not found', [
                'redirect_url' => 'sign-up'
            ]);
        }

        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        //Email Validate
        $resp = Email::validate($email);

        if ($resp['status'] != 200) {
            return Helper::resp(422, 'Validation failed', [
                'email' => [$resp['message']]
            ]);
        }

        $customerExist = Customer::findOne([
            'customers_email_address' => $email
        ]);

        if ($customerExist) {
            return Helper::resp(422, 'Validation failed', [
                'email' => ['Email already used, please try another email']
            ]);
        }

        $customer = $this->saveCustomer($country, $hp, $email);

        $this->reclaimMobile($country, $hp, $customer);
        $this->saveCustomerAddressBook($customer);
        $this->saveCustomerInfo($customer);
        $this->sendWelcomeEmail($customer);
        $this->saveOtp($customer);
        $this->saveCustomerRemarkHistory($customer);

        Auth::saveCustomerSetting($customer);
        Auth::saveCustomerInfoVerification($customer);
        Auth::saveUserPreference($customer, Helper::getUserPreference());

        if ($type == 'sns') {
            $this->saveSnsConnection($customer);
        }

        Auth::login($customer, $type, 'register');
        PipwaveOtp::updateOtpUser($country, $hp);

        $this->destroyData($this->type);

        $this->session->remove('manual_otp');

        return Helper::resp(301, 'Register success', [
            'redirect_url' => 'login-all'
        ]);
    }

    private function saveCustomer(Country $country, $hp, $email)
    {
        $pass = Helper::purify($this->request->post('password'));
        $pass = Auth::encryptPassword($pass);

        $data = [
            'customers_email_address' => $email,
            'customers_firstname' => $this->request->post('firstname'),
            'customers_lastname' => $this->request->post('lastname'),
            'customers_password' => $pass,
            'customers_country_dialing_code_id' => $country->countries_id,
            'customers_telephone' => $hp
        ];

        $customer = new Customer();
        $customer->attributes = $data;
        $customer->save();

        return $customer;
    }

    private function saveCustomerAddressBook(Customer $customer)
    {
        $data = [
            'customers_id' => $customer->customers_id,
            'entry_firstname' => $this->request->post('firstname'),
            'entry_lastname' => $this->request->post('lastname'),
            'entry_country_id' => $customer->customers_country_dialing_code_id
        ];

        $addressBook = new AddressBook();
        $addressBook->attributes = $data;
        $addressBook->save();

        $customer->customers_default_address_id = $addressBook->address_book_id;
        $customer->save();
    }

    private function saveCustomerInfo(Customer $customer)
    {
        $ip = PVM::getIPAddress();
        $query = $this->session->get('query');

        $service = ArrayHelper::getValue($query, 'service', Yii::$app->name);
        $changeMade = '';

        $customerData = [
            'customers_email_address',
            'customers_firstname',
            'customers_lastname',
            'customers_password',
            'customers_country_dialing_code_id',
            'customers_telephone',
            'customers_default_address_id'
        ];

        foreach ($customerData as $k => $data) {
            if ($k) {
                $changeMade .= '##';
            }

            $changeMade .= $data;
        }

        $data = [
            'customer_info_selected_country' => $customer->customers_country_dialing_code_id,
            'customers_info_number_of_logons' => 1,
            'customers_info_date_of_last_logon' => Helper::now(),
            'customers_info_id' => $customer->customers_id,
            'customers_info_date_account_created' => Helper::now(),
            'customers_info_account_created_ip' => $ip,
            'account_created_country' => $this->geoIp->countryCodeByIP($ip),
            'account_created_site' => $service,
            'customer_info_selected_language_id' => Yii::t("dev", "LANG_CODE_TO_ID"),
            'customers_info_changes_made' => $changeMade
        ];

        $customerInfo = new CustomerInfo();
        $customerInfo->attributes = $data;
        $customerInfo->save();
    }

    private function sendWelcomeEmail(Customer $customer)
    {
        $custName = implode(" ", [
            $customer->customers_firstname,
            $customer->customers_lastname
        ]);

        $title = $this->params['mail']['subject_prefix'] . Yii::t('sso', 'WELCOME TO OFFGAMERS!');
        $data = [
            'title' => $title,
            'cname' => $custName,
        ];

        Email::send(
            $customer,
            $title,
            'welcome_to_shasso-html',
            'welcome_to_shasso-text',
            $data
        );
    }

    private function saveSnsConnection(Customer $customer)
    {
        $data = [
            'customers_id' => $customer->customers_id,
            'provider' => $this->hybridAuth->getSession('ha_provider'),
            'provider_uid' => $this->hybridAuth->getSession('ha_uid'),
            'username' => $this->hybridAuth->getSession('ha_email'),
            'created_date' => Helper::now()
        ];

        $snsConnection = new SnsConnection();
        $snsConnection->insertOrUpdate($data);
    }

    private function saveOtp(Customer $customer)
    {
        $otp = Otp::findOne([
            'country_id' => $customer->customers_country_dialing_code_id,
            'hp' => $customer->country->countries_international_dialing_code . $customer->customers_telephone
        ]);

        if (!$otp) {
            return;
        }

        $otp->customer_id = $customer->customers_id;
        $otp->save();
    }

    private function getRedirectUrl()
    {
        return $this->getData($this->type, 'type') == 'sns'
            ? 'sns/account-create'
            : 'account-create';
    }

    private function saveCustomerRemarkHistory(Customer $customer)
    {
        $country = $customer->country;

        $fullHp = $country->countries_international_dialing_code . $customer->customers_telephone;

        if (!Auth::isManualOtp($country, $customer->customers_telephone, OtpManual::STATUS_USED)
            && !$this->session->get('manual_otp')) {
            return;
        }

        $otpManuals = OtpManual::find()
            ->where([
                'country_id' => $country->countries_id,
                'hp' => $fullHp,
            ])
            ->orderBy(['id' => SORT_DESC])
            ->limit(10)
            ->all();

        $str = '';
        $updatedBy = $otpManuals[0]->updated_by;

        foreach ($otpManuals as $manual) {
            if (!$manual->remark) {
                continue;
            }

            $str .= date('Y-m-d H:i:s', $manual->created_at) . ' - ' . $manual->remark . "\n";
        }

        $custRemarkHistory = new CustomerRemarksHistory();
        $custRemarkHistory->attributes = [
            'customers_id' => $customer->customers_id,
            'date_remarks_added' => Helper::now(),
            'remarks' => 'Manual verify telephone number',
            'remarks_added_by' => 'system'
        ];
        $custRemarkHistory->save();

        if ($str) {
            $str = "Manual OTP Remark:\n" . $str;

            $custRemarkHistory = new CustomerRemarksHistory();
            $custRemarkHistory->attributes = [
                'customers_id' => $customer->customers_id,
                'date_remarks_added' => Helper::now(),
                'remarks' => $str,
                'remarks_added_by' => $updatedBy
            ];
            $custRemarkHistory->save();
        }
    }

    private function reclaimMobile(Country $country, $hp, Customer $customer)
    {
        if (!$this->getData($this->type, 'reclaim_mobile')) {
            return false;
        }

        $rowUpdated = Auth::unverifiedHpFromAllAccount($country, $hp, $customer);

        if ($rowUpdated) {
            $country = $customer->country;
            $fullHp = $country->countries_international_dialing_code . $customer->customers_telephone;

            $unverifiedCustomerIds = $this->session->get('unverified_customer_ids');
            $unverifiedCustomerIds = implode(', ', $unverifiedCustomerIds);

            $custRemarkHistory = new CustomerRemarksHistory();
            $custRemarkHistory->attributes = [
                'customers_id' => $customer->customers_id,
                'date_remarks_added' => Helper::now(),
                'remarks' => 'Reclaimed mobile number ' . $fullHp . ' from customer ID : ' . $unverifiedCustomerIds,
                'remarks_added_by' => 'system'
            ];

            $custRemarkHistory->save();
        }
    }
}