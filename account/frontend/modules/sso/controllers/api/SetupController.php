<?php

namespace app\modules\sso\controllers\api;

use app\modules\sso\components\Auth;
use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use app\modules\sso\components\SMS;
use app\modules\sso\models\Country;
use app\modules\sso\models\Customer;
use app\modules\sso\models\CustomerRemarksHistory;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\SetupForm;
use yii\db\ActiveQuery;

class SetupController extends BaseApiController
{
    protected $type = 'setup';

    public function actionIndex()
    {
        $form = new SetupForm();
        $form->attributes = $this->request->post();

        if (!$this->request->isPost) {
            return Helper::resp(400, 'Invalid request.');
        }

        $pvmVerify = PVM::checkRecaptcha(1, $this->request->post('recaptcha_token'));

        if (!$pvmVerify) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes', [
                'action' => 'support'
            ]);
        }

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        $country = Country::findOne([
            'countries_id' => $this->request->post('country_id')
        ]);

        if (!$country) {
            return Helper::resp(400, 'Invalid country code');
        }

        $hp = $this->request->post('username');

        if (!Auth::validateHp(
            'hp',
            $country,
            $hp
        )) {
            return Helper::resp(422, 'Validation failed', [
                'username' => ['Invalid mobile number']
            ]);
        }

        $resp = SMS::requestOtp($country, $hp, 'verify_phone');

        if ($resp['status'] != 200) {
            return Helper::resp($resp['status'], $resp['message']);
        }

        $this->setSessionData($country, $hp, 'login');
        $this->setSessionData($country, $hp, 'setup');

        return Helper::resp(301, 'Success', [
            'redirect_url' => 'setup/otp-verify'
        ]);
    }

    public function actionCheckAccount()
    {
        $customer = $this->getData($this->type, 'customer');
        $otpVerified = $this->getData($this->type, 'otp_verified');
        $country = $this->getData($this->type, 'country');
        $hp = $this->getData($this->type, 'hp');

        if (!$customer || !$otpVerified) {
            return Helper::resp(301, 'Customer not found', [
                'redirect_url' => 'login'
            ]);
        }

        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);
        $country = Country::findOne(['countries_id' => $country['countries_id']]);

        $customer->customers_country_dialing_code_id = $country->countries_id;
        $customer->customers_telephone = $hp;

        if ($this->isSomeoneUseCustomerHp($customer)) {
            return Helper::resp(301, 'Customer mobile number is exist in another account', [
                'redirect_url' => 'setup/account-exist'
            ]);
        }

        $customer->save();

        Auth::saveCustomerSetting($customer);
        Auth::saveCustomerInfoVerification($customer);

        $unverifiedCustomerIds = $this->session->get('unverified_customer_ids');

        if ($unverifiedCustomerIds) {
            $unverifiedCustomerIds = implode(', ', $unverifiedCustomerIds);

            $fullHp = $country->countries_international_dialing_code . $customer->customers_telephone;
            $custRemarkHistory = new CustomerRemarksHistory();
            $custRemarkHistory->attributes = [
                'customers_id' => $customer->customers_id,
                'date_remarks_added' => Helper::now(),
                'remarks' => 'Reclaimed mobile number ' . $fullHp . ' from customer ID : ' . $unverifiedCustomerIds,
                'remarks_added_by' => 'system'
            ];
            $custRemarkHistory->save();
        }

        return Helper::resp(200, 'Updated customer success');
    }

    public function actionReclaim()
    {
        $otpVerified = $this->getData($this->type, 'otp_verified');
        $country = $this->getData($this->type, 'country');
        $hp = $this->getData($this->type, 'hp');
        $customer = $this->getData($this->type, 'customer');


        if (!$country || !$hp) {
            return Helper::resp(301, 'Phone number not found', [
                'redirect_url' => 'login'
            ]);
        }

        if (!$otpVerified) {
            return Helper::resp(301, 'OTP verify not found', [
                'redirect_url' => 'login'
            ]);
        }

        $country = Country::findOne(['countries_id' => $country['countries_id']]);
        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);

        if ($this->isDisputeOrChargebackCustomer($country, $hp) || Auth::reclaimIsOnCooldown($country, $hp)) {
            return Helper::resp(400, 'This mobile number cant be reclaim, please contact support for verification', [
                'action' => 'support'
            ]);
        }

        Auth::unverifiedHpFromAllAccount($country, $hp, $customer);

        return Helper::resp(200, 'Success unbind mobile number');
    }

    private function isSomeoneUseCustomerHp(Customer $customer)
    {
        $otherCustomers = Customer::find()->where([
            'customers_country_dialing_code_id' => $customer->customers_country_dialing_code_id,
            'customers_telephone' => $customer->customers_telephone,
        ])->all();

        if (!$otherCustomers) {
            return false;
        }

        foreach ($otherCustomers as $otherCustomer) {
            if ($otherCustomer->customers_id == $customer->customers_id) {
                continue;
            }

            if (Auth::isHpVerified($otherCustomer)) {
                $data = $this->getData('login');
                $data['email_encoded'] = Auth::maskEmail($otherCustomer->customers_email_address);

                $this->setData($data, 'login');
                return true;
            }
        }

        return false;
    }

    private function setSessionData(Country $country, $hp, $type = null)
    {
        $type = $type ?: $this->type;

        $data = array_merge($this->getData($type), [
            'country' => $country->toArray([
                'countries_id',
                'countries_international_dialing_code'
            ]),
            'hp' => $hp,
            'hp_encoded' => Auth::maskHp($hp),
        ]);

        $this->setData($data, $type);
    }

    private function isDisputeOrChargebackCustomer(Country $country, $hp)
    {
        $disputeFlags = [2, 4];
        $fullHp = $country->countries_international_dialing_code . $hp;

        $customers = Customer::find()
            ->where([
                'customers_country_dialing_code_id' => $country->countries_id,
                'customers_telephone' => $hp,
            ])
            ->andWhere(['!=', 'customers_flag', ''])
            ->joinWith(['infoVerifications' => function (ActiveQuery $query) use ($fullHp) {
                $query->where([
                    'customers_info_value' => $fullHp,
                    'info_verification_type' => 'telephone',
                ]);
            }])
            ->all();

        foreach ($customers as $customer) {
            $flags = explode(',', $customer->customers_flag);

            foreach ($disputeFlags as $disputeFlag) {
                if (ArrayHelper::isIn($disputeFlag, $flags)) {
                    return true;
                }
            }
        }

        return false;
    }
}