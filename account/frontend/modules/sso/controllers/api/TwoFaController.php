<?php

namespace app\modules\sso\controllers\api;


use app\modules\sso\components\Helper;
use app\modules\sso\components\PVM;
use app\modules\sso\components\TwoFA;
use app\modules\sso\models\Customer;
use common\components\ArrayHelper;
use frontend\modules\sso\forms\OtpForm;
use Yii;

class TwoFaController extends BaseApiController
{
    protected $type = '2fa';

    public function actionVerify()
    {
        $form = new OtpForm();
        $form->attributes = $this->request->post();

        if (!$form->validate()) {
            return Helper::resp(422, 'Validation failed', $form->errors);
        }

        $type = $this->request->post('type');
        $customer = $this->getData($type, 'customer');
        $answer = $this->request->post('answer');

        $twoFaParams = ArrayHelper::getValue(Yii::$app->params, 'sso.' . $this->type);

        if (!PVM::securityCheck($this->type, $twoFaParams, 0)) {
            return Helper::resp(400, 'Too many request attempted, please try again after few minutes',[
                'action' => 'support'
            ]);
        }

        if (!$customer) {
            if ($type == 'login') {
                return Helper::resp(301, 'Customer not found', [
                    'redirect_url' => 'sso/login'
                ]);
            }

            return Helper::resp(400, 'Customer not found');
        }

        $customer = Customer::findOne(['customers_id' => $customer['customers_id']]);

        if (!TwoFA::enabled($customer)) {
            if ($type == 'login') {
                return Helper::resp(301, 'Something went wrong, please try again', [
                    'redirect_url' => 'sso/login'
                ]);
            }

            return Helper::resp(400, 'Something went wrong, please try again');
        }

        if (!TwoFA::verify($customer, $answer)) {
            return Helper::resp(422, 'Validation failed', [
                'answer' => [Yii::t("general", "ERROR_MSG_INCORRECT_TWO_FACTOR_AUTH")]
            ]);
        }

        $this->setData(1, $type, 'otp_verified');

        PVM::clear($this->type);

        return Helper::resp(200, 'Verified success');
    }
}