<?php

namespace frontend\modules\sso\forms;

use Yii;
use yii\base\Model;

class CreateAccountForm extends Model
{
    public $firstname;
    public $lastname;
    public $email;
    public $password;

    public function rules()
    {
        return [
            [['firstname', 'lastname'], 'string', 'encoding' => 'latin1', 'max' => 32],
            [['email'], 'email', 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
            [['firstname', 'lastname', 'email', 'password'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['password'], 'string', 'max' => 40],
            [['password'], 'string', 'min' => 6, 'message' => Yii::t('sso', 'ERROR_PASSWORD_MIN6')],
        ];
    }
}