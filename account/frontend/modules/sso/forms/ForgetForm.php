<?php

namespace frontend\modules\sso\forms;

use app\modules\sso\components\PVM;
use Yii;
use yii\base\Model;

class ForgetForm extends Model
{
    public $type;
    public $username;
    public $country_id;
    public $recaptcha_token;

    public function rules()
    {
        $required = ['type', 'username'];

        if(!PVM::isIpException()) {
            $required[] = 'recaptcha_token';
        }

        return [
            [['username'], 'email', 'when' => function ($model) {
                return $model->type == 'email';
            }, 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
            [['country_id'], 'required', 'when' => function ($model) {
                return $model->type == 'hp';
            }, 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['username'], 'match', 'pattern' => '/^[0-9]+$/', 'when' => function ($model) {
                return $model->type == 'hp';
            }, 'message' => 'Mobile number must be a number'],
            [$required, 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
        ];
    }
}
