<?php

namespace frontend\modules\sso\forms;

use app\modules\sso\components\PVM;
use Yii;
use yii\base\Model;

class LoginForm extends Model
{
    public $type;
    public $username;
    public $password;
    public $country_id;
    public $recaptcha_token;

    public function rules()
    {
        $required = ['type', 'username', 'password'];

        if (!PVM::isIpException()) {
            $required[] = 'recaptcha_token';
        }

        return [
            [['username'], 'email', 'when' => function ($model) {
                return $model->type == 'email';
            }, 'message' => Yii::t('sso', 'ERROR_EMAIL_FORMAT')],
            [['country_id'], 'required', 'when' => function ($model) {
                return $model->type == 'hp';
            }, 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['username'], 'match', 'pattern' => '/^[0-9]+$/', 'when' => function ($model) {
                return $model->type == 'hp';
            }, 'message' => 'Mobile number must be a number'],
            [$required, 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['password'], 'string', 'max' => 40],
            [['password'], 'string', 'min' => 6, 'message' => Yii::t('sso', 'ERROR_PASSWORD_MIN6')],
        ];
    }
}
