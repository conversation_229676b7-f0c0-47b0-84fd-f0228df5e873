<?php

namespace frontend\modules\sso\forms;

use Yii;
use yii\base\Model;

class OtpForm extends Model
{
    public $answer;
    public $type;
    public $via;

    public function rules()
    {
        return [
            [['type', 'answer'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['answer'], 'string', 'length' => 6, 'notEqual' => 'Pin should contain 6 digits'],
            [['answer'], 'match', 'pattern' => '/^[0-9]+$/', 'message' => 'Pin must be number']
        ];
    }
}
