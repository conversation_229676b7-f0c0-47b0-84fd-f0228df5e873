<?php

namespace frontend\modules\sso\forms;

use app\modules\sso\components\PVM;
use Yii;
use yii\base\Model;

class RegisterForm extends Model
{
    public $username;
    public $country_id;
    public $recaptcha_token;

    public function rules()
    {
        $required = ['country_id', 'username'];

        if (!PVM::isIpException()) {
            $required[] = 'recaptcha_token';
        }

        return [
            [$required, 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            ['username', 'match', 'pattern' => '/^[0-9]+$/', 'message' => 'Mobile number must be a number.']
        ];
    }
}