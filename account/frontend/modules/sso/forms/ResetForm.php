<?php

namespace frontend\modules\sso\forms;

use Yii;
use yii\base\Model;

class ResetForm extends Model
{
    public $password;
    public $token;
    public $password_confirm;

    public function rules()
    {
        return [
            [['token', 'password', 'password_confirm'], 'required', 'message' => Yii::t('general', 'ERROR_EMPTY_FIELD')],
            [['password'], 'string', 'max' => 40],
            [['password'], 'string', 'min' => 6, 'message' => Yii::t('sso', 'ERROR_PASSWORD_MIN6')],
            [['password_confirm'], 'compare', 'compareAttribute' => 'password', 'message' => 'Confirm password not match with password'],
        ];
    }
}
