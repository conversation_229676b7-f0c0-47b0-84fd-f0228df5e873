<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%address_book}}".
 *
 * @property int $address_book_id
 * @property int $customers_id
 * @property string $entry_gender
 * @property string $entry_company
 * @property string $entry_firstname
 * @property string $entry_lastname
 * @property string $entry_street_address
 * @property string $entry_suburb
 * @property string $entry_postcode
 * @property string $entry_city
 * @property string $entry_state
 * @property int $entry_country_id
 * @property int $entry_zone_id
 *
 * @property Country $country
 */
class AddressBook extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%address_book}}';
    }

    public function rules()
    {
        return [
            [['customers_id', 'entry_country_id', 'entry_zone_id'], 'integer'],
            [['entry_gender'], 'string', 'max' => 1],
            [['entry_company', 'entry_firstname', 'entry_lastname', 'entry_suburb', 'entry_city', 'entry_state'], 'string', 'max' => 32],
            [['entry_street_address'], 'string', 'max' => 64],
            [['entry_postcode'], 'string', 'max' => 10],
        ];
    }

    public function getCountry()
    {
        return $this->hasOne(Country::class, ['countries_id' => 'entry_country_id']);
    }
}
