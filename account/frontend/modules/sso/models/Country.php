<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%countries}}".
 *
 * @property int $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property int $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property int $address_format_id
 * @property int $maxmind_support
 * @property string $aft_risk_type
 * @property int $countries_display
 * @property int $telesign_support
 * @property Currency $currency
 */
class Country extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%countries}}';
    }

    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['currencies_id' => 'countries_currencies_id']);
    }
}
