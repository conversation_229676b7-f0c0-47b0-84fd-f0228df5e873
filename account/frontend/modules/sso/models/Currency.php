<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%currencies}}".
 *
 * @property int $currencies_id
 * @property string $title
 * @property string $code
 * @property string $symbol_left
 * @property string $symbol_right
 * @property string $decimal_point
 * @property string $thousands_point
 * @property string $decimal_places
 * @property string $value
 * @property string $buy_value
 * @property string $buy_value_adjust
 * @property string $sell_value
 * @property string $sell_value_adjust
 * @property int $currencies_live_update
 * @property string $currencies_used_for
 * @property string $last_updated
 */
class Currency extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%currencies}}';
    }
}
