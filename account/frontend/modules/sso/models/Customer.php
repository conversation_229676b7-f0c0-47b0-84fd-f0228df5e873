<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers}}".
 *
 * @property int $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_dob
 * @property int $account_activated
 * @property string $customers_email_address
 * @property int $email_verified
 * @property string $serial_number
 * @property int $customers_default_address_id
 * @property int $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string $customers_newsletter
 * @property string $customers_group_name
 * @property int $customers_group_id
 * @property string $customers_discount
 * @property int $customers_groups_id
 * @property int $customers_aft_groups_id
 * @property int $customers_status
 * @property string $customers_flag
 * @property int $customers_phone_verified
 * @property string $customers_phone_verified_by
 * @property string $customers_phone_verified_datetime
 * @property int $affiliate_ref_id
 * @property int $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property string $customers_reserve_amount
 * @property string $customers_security_start_time
 * @property int $customers_disable_withdrawal
 *
 * @property CustomerInfo $info
 * @property Country $country
 * @property AddressBook $daddress
 */
class Customer extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%customers}}';
    }

    public function rules()
    {
        return [
            [['customers_dob', 'customers_phone_verified_datetime', 'customers_security_start_time'], 'safe'],
            [['account_activated', 'email_verified', 'customers_default_address_id', 'customers_country_dialing_code_id', 'customers_group_id', 'customers_groups_id', 'customers_status', 'customers_phone_verified', 'affiliate_ref_id', 'ref_id', 'customers_disable_withdrawal'], 'integer'],
            [['customers_discount'], 'string', 'max' => 8],
            [['customers_reserve_amount'], 'string', 'max' => 15],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname', 'customers_telephone', 'customers_mobile', 'customers_fax', 'customers_login_sites'], 'string', 'max' => 32],
            [['customers_email_address', 'customers_msn', 'customers_yahoo'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_qq', 'customers_icq'], 'string', 'max' => 20],
            [['customers_password', 'customers_pin_number'], 'string', 'max' => 40],
            [['customers_newsletter', 'customers_phone_verified_by', 'customers_merged_profile'], 'string', 'max' => 255],
            [['customers_group_name'], 'string', 'max' => 27],
            [['customers_flag'], 'string', 'max' => 10],
            [['customers_aft_groups_id'], 'string', 'max' => 11],
        ];
    }

    public function getGroup()
    {
        return $this->hasOne(CustomerGroups::class, ['customers_groups_id' => 'customers_groups_id']);
    }

    public function getInfo()
    {
        return $this->hasOne(CustomerInfo::class, ['customers_info_id' => 'customers_id']);
    }

    public function getInfoVerifications()
    {
        return $this->hasMany(CustomerInfoVerification::class, ['customers_id' => 'customers_id']);
    }

    public function getCountry()
    {
        return $this->hasOne(Country::class, ['countries_id' => 'customers_country_dialing_code_id']);
    }

    public function getDaddress()
    {
        return $this->hasOne(AddressBook::class, ['address_book_id' => 'customers_default_address_id']);
    }
}