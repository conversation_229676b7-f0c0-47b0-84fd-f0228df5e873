<?php

namespace app\modules\sso\models;

/**
 * This is the model class for table "{{%customers_groups}}".
 *
 * @property int $customers_groups_id
 * @property string $customers_groups_name
 * @property string $customers_groups_legend_color
 * @property string $customers_groups_payment_methods
 * @property string $customers_groups_extra_sc In Percentage
 * @property int $sort_order
 */
class CustomerGroups extends BaseModel
{
    public static function tableName()
    {
        return '{{%customers_groups}}';
    }

    public function rules()
    {
        return [
            [['customers_groups_payment_methods'], 'required'],
            [['customers_groups_extra_sc'], 'string', 'max' => 8],
            [['sort_order'], 'integer'],
            [['customers_groups_name'], 'string', 'max' => 32],
            [['customers_groups_legend_color'], 'string', 'max' => 7],
        ];
    }
}
