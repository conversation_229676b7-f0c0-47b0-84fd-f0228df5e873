<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_info}}".
 *
 * @property int $customers_info_id
 * @property string $customers_info_date_of_last_logon
 * @property int $customer_info_account_dormant
 * @property int $customers_info_number_of_logons
 * @property string $customers_info_date_account_created
 * @property string $customers_info_account_created_ip
 * @property string $account_created_country
 * @property int $customers_info_account_created_from
 * @property string $account_created_site
 * @property string $customers_info_date_account_last_modified
 * @property string $customers_info_changes_made
 * @property int $global_product_notifications
 * @property int $customer_info_selected_country
 * @property int $customer_info_selected_language_id
 */
class CustomerInfo extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%customers_info}}';
    }

    public function rules()
    {
        return [
            [['customers_info_changes_made'], 'required'],
            [['customers_info_id', 'customer_info_account_dormant', 'customers_info_number_of_logons', 'customers_info_account_created_from', 'global_product_notifications', 'customer_info_selected_country', 'customer_info_selected_language_id'], 'integer'],
            [['customers_info_date_of_last_logon', 'customers_info_date_account_created', 'customers_info_date_account_last_modified'], 'safe'],
            [['customers_info_account_created_ip'], 'string', 'max' => 128],
            [['account_created_country'], 'string', 'max' => 2],
            [['account_created_site'], 'string', 'max' => 32],
        ];
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['customers_id' => 'customers_info_id']);
    }
}
