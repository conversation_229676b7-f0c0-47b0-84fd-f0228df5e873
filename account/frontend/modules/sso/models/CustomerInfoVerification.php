<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_info_verification}}".
 *
 * @property int $customers_id
 * @property string $customers_info_value
 * @property string $serial_number
 * @property int $verify_try_turns
 * @property int $info_verified
 * @property string $info_verification_type
 * @property string $customers_info_verification_mode
 * @property string $customers_info_verification_date
 * @property string $call_language
 */
class CustomerInfoVerification extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%customers_info_verification}}';
    }

    public function rules()
    {
        return [
            [['customers_id', 'verify_try_turns', 'info_verified'], 'integer'],
            [['customers_info_verification_date'], 'safe'],
            [['customers_info_value'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_info_verification_mode'], 'string', 'max' => 1],
            [['info_verification_type'], 'string', 'max' => 32],
            [['call_language'], 'string', 'max' => 40],
        ];
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['customers_id' => 'customers_id']);
    }
}
