<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_login_ip_history}}".
 *
 * @property int $customers_id
 * @property string $customers_login_date
 * @property string $customers_login_ip
 * @property string $customers_login_ua_info
 * @property string $login_method
 */
class CustomerLoginIpHistory extends BaseModel
{
    public static function tableName()
    {
        return '{{%customers_login_ip_history}}';
    }

    public function rules()
    {
        return [
            [['customers_id', 'customers_login_date'], 'required'],
            [['customers_id'], 'integer'],
            [['customers_login_date'], 'safe'],
            [['customers_login_ip'], 'string', 'max' => 128],
            [['login_method'], 'string', 'max' => 12],
            [['customers_login_ua_info'], 'string', 'max' => 255],
        ];
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['customers_id' => 'customers_id']);
    }
}
