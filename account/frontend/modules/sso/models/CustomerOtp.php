<?php

namespace app\modules\sso\models;

/**
 * This is the model class for table "{{%customers_otp}}".
 *
 * @property int $customers_id
 * @property string $customers_otp_type
 * @property string $customers_otp_digit
 * @property string $customers_match_data
 * @property string $customers_otp_request_date
 */
class CustomerOtp extends BaseModel
{
    public static function tableName()
    {
        return '{{%customers_otp}}';
    }

    public function rules()
    {
        return [
            [['customers_otp_type'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['customers_match_data'], 'string', 'max' => 255],
            [['customers_otp_request_date'], 'safe'],
            [['customers_otp_type', 'customers_otp_digit'], 'string', 'max' => 32],
        ];
    }
}
