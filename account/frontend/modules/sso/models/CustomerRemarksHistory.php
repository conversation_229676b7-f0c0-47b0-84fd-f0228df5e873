<?php

namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_remarks_history}}".
 *
 * @property int $customers_remarks_history_id
 * @property int $customers_id
 * @property string $date_remarks_added
 * @property string $remarks
 * @property string $remarks_added_by
 */
class CustomerRemarksHistory extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%customers_remarks_history}}';
    }

    public function rules()
    {
        return [
            [['customers_id'], 'integer'],
            [['date_remarks_added', 'remarks'], 'safe'],
            [['remarks_added_by'], 'string', 'max' => 255],
        ];
    }
}
