<?php

namespace app\modules\sso\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "email_validation".
 *
 * @property int $id
 * @property string $email
 * @property string $profile
 * @property int $mx_found
 * @property int $smtp_check
 * @property int $score
 * @property string|null $raw
 * @property int|null $created_at
 * @property int|null $updated_at
 */
class EmailValidation extends BaseModel
{
    public static function tableName()
    {
        return '{{%email_validation}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('db.og');
    }

    public function behaviors()
    {
        return [
            TimestampBehavior::class,
        ];
    }

    public function rules()
    {
        return [
            [['email', 'profile', 'mx_found', 'smtp_check', 'score'], 'required'],
            [['mx_found', 'smtp_check', 'score', 'created_at', 'updated_at'], 'integer'],
            [['raw'], 'string'],
            [['email'], 'string', 'max' => 768],
            [['profile'], 'string', 'max' => 255],
            [['email'], 'unique'],
        ];
    }
}
