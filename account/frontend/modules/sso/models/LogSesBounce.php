<?php


namespace app\modules\sso\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%log_ses_bounce}}".
 *
 * @property string $email
 * @property string $error_string
 * @property string $created_datetime
 */
class LogSesBounce extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%log_ses_bounce}}';
    }

    public function rules()
    {
        return [
            [['email', 'created_datetime'], 'required'],
            [['error_string'], 'string'],
            [['email'], 'string', 'max' => 255],
        ];
    }
}