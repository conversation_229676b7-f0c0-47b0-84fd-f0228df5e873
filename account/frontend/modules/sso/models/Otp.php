<?php

namespace app\modules\sso\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%otp}}".
 *
 * @property int $id
 * @property int $customer_id
 * @property int $country_id
 * @property string $hp
 * @property int $reference_token
 * @property string $created_at
 * @property string $updated_at
 * @property Country $country
 */
class Otp extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%otp}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('db.og');
    }

    public function rules()
    {
        return [
            [[
                'country_id',
                'customer_id',
                'hp',
                'reference_token',
                'created_at',
                'updated_at'
            ], 'safe'],
        ];
    }

    public function getCountry()
    {
        return $this->hasOne(Country::class, ['countries_id' => 'country_id']);
    }
}