<?php

namespace app\modules\sso\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%customers_static_info}}".
 *
 * @property int $customers_id
 * @property string $info_key
 * @property string $value
 * @property string $created_date
 * @property string $updated_date
 */
class ShassoCustomerStaticInfo extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%customers_static_info}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }
}
