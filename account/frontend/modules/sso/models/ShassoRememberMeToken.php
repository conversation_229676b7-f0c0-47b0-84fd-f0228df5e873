<?php

namespace app\modules\sso\models;

use app\modules\sso\components\PVM;
use common\components\ArrayHelper;
use Yii;

/**
 * This is the model class for table "remember_me_token".
 *
 * @property string $id
 * @property integer $customers_id
 * @property string $token
 * @property string $token_id
 * @property string $login_ip
 * @property string $login_country
 * @property string $user_agent
 * @property string $parsed_ua_os
 * @property string $parsed_ua_browser
 * @property integer $expire
 */
class ShassoRememberMeToken extends BaseModel
{
    public static function tableName()
    {
        return 'remember_me_token';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    public static function isValid(Customer $customer, $token)
    {
        if (!strpos($token, '.')) {
            return false;
        }

        list($salt, $oldSign) = explode('.', $token);
        $sign = self::getSignature($customer, $salt);

        if ($sign != $oldSign) {
            return false;
        }

        $rmToken = self::findOne([
            'customers_id' => $customer->customers_id,
            'token' => $token
        ]);

        if (!$rmToken) {
            return false;
        }

        if ($rmToken->login_country != Yii::$app->geoip->countryCodeByIP(PVM::getIPAddress())) {
            return false;
        }

        $parseUserAgent = PVM::parseUserAgent();

        if (!$rmToken->parsed_ua_os || $rmToken->parsed_ua_os
            != ArrayHelper::getValue($parseUserAgent, 'os')) {
            return false;
        }

        if (!$rmToken->parsed_ua_browser || $rmToken->parsed_ua_browser
            != ArrayHelper::getValue($parseUserAgent, 'browser')) {
            return false;
        }

        return true;
    }

    private static function getSignature($customer, $salt)
    {
        $salt = $salt ?: uniqid();

        return md5(
            $customer->customers_email_address
            . ':' . $salt
            . ':' . $customer->customers_password
        );
    }

    public static function generate(Customer $customer, $oldToken)
    {
        $cacheKey = get_called_class() . '/generated-tokens/' . $oldToken;
        $token = null;

        if (Yii::$app->cache->get($cacheKey)) {
            return Yii::$app->cache->get($cacheKey);
        }

        if (!$token) {
            $salt = uniqid();
            $sign = self::getSignature($customer, $salt);
            $token = $salt . '.' . $sign;

            Yii::$app->cache->set($cacheKey, $token, 60);
        }

        return $token;
    }

    public function rules()
    {
        return [
            [['customers_id'], 'required'],
            [['customers_id', 'expire'], 'integer'],
            [['token', 'token_id', 'user_agent', 'parsed_ua_os', 'parsed_ua_browser'], 'string', 'max' => 255],
            [['login_ip'], 'string', 'max' => 128],
            [['login_country'], 'string', 'max' => 2],
        ];
    }
}
