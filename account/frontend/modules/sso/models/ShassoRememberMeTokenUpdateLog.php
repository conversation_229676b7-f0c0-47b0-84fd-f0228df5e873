<?php

namespace app\modules\sso\models;

use Yii;

/**
 * This is the model class for table "{{%remember_me_token_update_log}}".
 *
 * @property int $id
 * @property int $customers_id
 * @property string $token_id
 * @property string $login_ip
 * @property string $login_country
 * @property string $user_agent
 * @property string $parsed_ua_os
 * @property string $parsed_ua_browser
 * @property string $prev_login_ip
 * @property string $prev_login_country
 * @property string $prev_user_agent
 * @property string $prev_parsed_ua_os
 * @property string $prev_parsed_ua_browser
 * @property string $type
 * @property string $created_date
 */
class ShassoRememberMeTokenUpdateLog extends BaseModel
{
    public static function tableName()
    {
        return '{{%remember_me_token_update_log}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customers_id', 'token_id'], 'required'],
            [['customers_id'], 'integer'],
            [['created_date'], 'safe'],
            [['token_id', 'login_ip', 'login_country', 'user_agent', 'parsed_ua_os', 'parsed_ua_browser', 'prev_login_ip', 'prev_login_country', 'prev_user_agent', 'prev_parsed_ua_os', 'prev_parsed_ua_browser', 'type'], 'string'],
        ];
    }
}
