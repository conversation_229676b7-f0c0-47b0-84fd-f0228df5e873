<?php

namespace app\modules\sso\models;

use Yii;

/**
 * This is the model class for table "{{%sso_token}}".
 *
 * @property string $sso_token
 * @property string $sess_id
 * @property string $client_id
 * @property int $user_id
 * @property int $login_method
 * @property string $expiry
 */
class ShassoSsoToken extends BaseModel
{
    public static function tableName()
    {
        return '{{%sso_token}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    public function rules()
    {
        return [
            [['sso_token', 'sess_id', 'client_id', 'user_id', 'login_method'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['login_method'], 'string', 'max' => 20],
            [['expiry'], 'safe'],
            [['sso_token'], 'string', 'max' => 36],
            [['sess_id'], 'string', 'max' => 32],
            [['client_id'], 'string', 'max' => 40],
        ];
    }
}
