<?php

namespace app\modules\sso\models;

use Yii;

/**
 * This is the model class for table "{{%user_last_login}}".
 *
 * @property int $user_id
 * @property string $login_date
 * @property string $login_ip
 * @property string $login_ip_iso2
 */
class ShassoUserLastLogin extends BaseModel
{
    public static function tableName()
    {
        return '{{%user_last_login}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    public function rules()
    {
        return [
            [['user_id', 'login_date', 'login_ip', 'login_ip_iso2'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['login_ip'], 'string', 'max' => 20],
            [['login_ip_iso2'], 'string', 'max' => 2],
        ];
    }
}
