<?php

namespace app\modules\sso\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%user_preference}}".
 *
 * @property int $user_id
 * @property string $preference_key
 * @property string $value
 */
class ShassoUserPreference extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%user_preference}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    public function rules()
    {
        return [
            [['user_id', 'preference_key', 'value'], 'required'],
            [['user_id'], 'string', 'max' => 11],
            [['preference_key'], 'string', 'max' => 32],
            [['value'], 'string', 'max' => 128],
        ];
    }
}
