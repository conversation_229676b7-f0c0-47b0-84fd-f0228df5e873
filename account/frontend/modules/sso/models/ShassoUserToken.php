<?php

namespace app\modules\sso\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%user_token}}".
 *
 * @property int $user_id
 * @property string $token_type
 * @property string $token_value
 * @property string $created_date
 * @property string $expiry_date
 */
class ShassoUserToken extends ActiveRecord
{
    public static function tableName()
    {
        return '{{%user_token}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbshasso');
    }

    public function rules()
    {
        return [
            [[
                'user_id',
                'token_type',
                'token_value',
                'created_date',
                'expiry_date',
            ], 'safe'],
        ];
    }
}
