<?php

namespace app\modules\sso\models;

/**
 * This is the model class for table "{{%sms_report}}".
 *
 * @property int $customers_id
 * @property string $sms_request_date
 * @property string $sms_request_type
 * @property string $sms_request_phone_number
 * @property int $sms_request_phone_country_id
 * @property string $sms_request_page
 * @property string $sms_provider
 * @property string $sms_send_date
 */
class SmsReport extends BaseModel
{
    public static function tableName()
    {
        return '{{%sms_report}}';
    }

    public function rules()
    {
        return [
            [['customers_id', 'sms_request_phone_country_id'], 'string', 'max' => 11],
            [['sms_request_type'], 'string', 'max' => 16],
            [['sms_request_phone_number'], 'string', 'max' => 32],
            [['sms_request_page'], 'string', 'max' => 128],
            [['sms_request_date', 'sms_provider', 'sms_send_date'], 'safe'],
        ];
    }
}
