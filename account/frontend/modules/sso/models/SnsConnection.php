<?php

namespace app\modules\sso\models;

/**
 * This is the model class for table "{{%sns_connection}}".
 *
 * @property int $customers_id
 * @property string $provider
 * @property string $provider_uid
 * @property string $username
 * @property string $created_date
 *
 * @property Customer $customer
 */
class SnsConnection extends BaseModel
{
    public static function tableName()
    {
        return '{{%sns_connection}}';
    }

    public function rules()
    {
        return [
            [['customers_id', 'provider', 'provider_uid'], 'required'],
            [['customers_id'], 'string', 'max' => 11],
            [['created_date'], 'safe'],
            [['provider'], 'string', 'max' => 20],
            [['provider_uid'], 'string', 'max' => 32],
            [['username'], 'string', 'max' => 255],
        ];
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['customers_id' => 'customers_id']);
    }

}
