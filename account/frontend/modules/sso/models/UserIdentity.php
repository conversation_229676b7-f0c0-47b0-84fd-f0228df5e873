<?php

namespace app\modules\sso\models;

use yii\base\NotSupportedException;
use yii\web\IdentityInterface;

class UserIdentity implements IdentityInterface
{
    public $customer;
    public $loginMethod;

    public static function tableName()
    {
        return '{{%customers}}';
    }

    public static function findIdentity($id)
    {
        $customer = static::findOne([
            'customers_id' => $id,
        ]);

        if ($customer) {
            $identity = new static();
            $identity->customers = $customer;
            return $identity;
        }

        return null;
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        throw new NotSupportedException('"findIdentityByAccessToken" is not implemented.');
    }

    public function getId()
    {
        return $this->customer ? $this->customer->customers_id : null;
    }

    public function validateAuthKey($authKey)
    {
        throw new NotSupportedException('"validateAuthKey" is not implemented.');
    }

    public function getAuthKey()
    {
        throw new NotSupportedException('"getAuthKey" is not implemented.');
    }
}