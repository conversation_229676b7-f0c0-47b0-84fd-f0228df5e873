<div class="flex flex-col <?php echo isset($margin) ? $margin : 'mb-7' ?> mobile-number-field" x-data="dropdown"
     x-show="tab === 'hp'">
    <label class="label">Mobile Number</label>

    <div class="flex" x-data="mobile_country_list">
        <div class="dropdown-list">
            <button class="dropdown base" type="button" @click="toggle()"
                    :class="{'invalid': !formData.country_id.valid}">
                <span class="flag" :class="selected.countries_iso_code_2"></span>
                <p>+<span x-text="selected.countries_international_dialing_code"></p>
                <i class="icon icon-arrow_drop_down"></i>
            </button>
            <div class="xlist" x-show="open" @click.away="close()">
                <div class="input-icon-group has-left-icon has-right-icon-btn">
                    <i class="icon icon-search left-icon"></i>
                    <input class="input base" x-model="keyword" @keyup="search()">
                    <button class="inline-icon-btn right-icon-btn text-on-surface" type="button"
                            x-show="keyword" @click="clear()">
                        <i class="icon icon-close"></i>
                    </button>
                </div>

                <ul>
                    <li class="xlist-item surface surface-state" x-show="!results.length">No result</li>
                    <template x-for="country in results" :key="index" x-show="results.length">
                        <li class="xlist-item surface surface-state" @click="select(country)"
                            x-show="country.countries_display">
                            <span class="flag" :class="country.countries_iso_code_2"></span>
                            <p class="mr-2 w-12">+<span x-text="country.countries_international_dialing_code"></span></p>
                            <p x-text="country.countries_name"></p>
                        </li>
                    </template>
                </ul>
            </div>
        </div>
        <input class="input rounded-l-none w-full base" type="tel"
               x-model="formData.username.value" :class="{'invalid': !formData.username.valid}">
    </div>

    <template x-for="error in formData.username.errors">
        <p class="message error" x-text="error"></p>
    </template>
</div>