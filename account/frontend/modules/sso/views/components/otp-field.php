<?php
$class = isset($class) ? $class : 'mb-7';
?>

<div class="text-center grid grid-cols-6 <?php echo $class; ?>">
    <template x-for="(v, k) in otp">
        <input type="tel" class="input base otp"
               :x-ref="`box${k}`" x-model="otp[k]" maxlength="1"
               :class="{'invalid': !formData.answer.valid}"
               @keydown="handleKeyDown(k, $event)" @paste="handlePaste(k, $event)">
    </template>

    <template x-for="error in formData.answer.errors">
        <p class="message error col-span-full text-left" x-text="error"></p>
    </template>
</div>