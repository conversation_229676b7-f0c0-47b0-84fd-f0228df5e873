<?php
    $class = isset($class) ? $class : 'mb-5';
?>
<?php if (!isset($confirm)){ ?>
    <div class="flex flex-col <?php echo $class; ?>" x-data="password">
        <label class="label" for="pass">Password</label>
        <div class="input-icon-group has-right-icon-btn">
            <input class="w-full input base" :type="type" id="pass"
                   x-model="formData.password.value" :class="{'invalid': !formData.password.valid}"/>
            <button class="inline-icon-btn right-icon-btn surface" type="button" @click="toggle()">
                <i class="icon" :class="icon_class"></i>
            </button>
        </div>

        <template x-for="error in formData.password.errors">
            <p class="message error" x-text="error"></p>
        </template>
    </div>
<?php } else { ?>
    <div class="flex flex-col <?php echo $class; ?>" x-data="password">
        <label class="label" for="pass-conf">Confirm Password</label>
        <div class="input-icon-group has-right-icon-btn">
            <input class="w-full input base" :type="type" id="pass-conf"
                   x-model="formData.password_confirm.value" :class="{'invalid': !formData.password_confirm.valid}"/>
            <button class="inline-icon-btn right-icon-btn surface" type="button" @click="toggle()">
                <i class="icon" :class="icon_class"></i>
            </button>
        </div>

        <template x-for="error in formData.password_confirm.errors">
            <p class="message error" x-text="error"></p>
        </template>
    </div>
<?php } ?>