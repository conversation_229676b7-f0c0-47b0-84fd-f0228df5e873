<div class="mb-6 flex justify-center">
    <button class="icon-btn xoutline mr-3 on-primary-state flex justify-center items-center"
            @click="hybridLogin('Facebook')">
        <i class="icon icon-Facebook"></i>
    </button>

    <button class="icon-btn xoutline mr-3 on-primary-state flex justify-center items-center"
            @click="hybridLogin('Twitter')">
        <i class="icon icon-Twitter"></i>
    </button>

    <button class="icon-btn xoutline mr-3 on-primary-state flex justify-center items-center"
            @click="hybridLogin('PayPal')">
        <i class="icon icon-Paypal"><span class="path1"></span><span class="path2"></span></i>
    </button>

    <button class="icon-btn xoutline on-primary-state flex justify-center items-center" @click="hybridLogin('Google')">
        <i class="icon icon-Google"><span class="path1"></span><span class="path2"></span><span
                    class="path3"></span><span class="path4"></span></i>
    </button>
</div>

<script>
    function hybridLogin(provider) {
        redirect('sns/request', {'ha_provider': provider})
    }
</script>