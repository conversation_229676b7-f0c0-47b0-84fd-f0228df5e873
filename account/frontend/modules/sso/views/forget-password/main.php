<?php

use app\modules\sso\assets\RecaptchaChallengeAsset;

RecaptchaChallengeAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        formData: {
            country_id: {value: 'email', valid: 1},
            username: {value: null, valid: 1},
            password: {value: null, valid: 1},
            remember_me: null,
            selected: null,
        }
    }));
</script>

<div class="auth-container" x-data="form">
    <div x-data="tabs">
        <div class="py-5 text-center semibold-1_5rem base">
            Forgot Password?
        </div>

        <?php echo $this->render('/components/email-hp-tab-bar'); ?>

        <div class="px-4 sm:px-12">
            <form @submit.prevent="submit" novalidate>
                <?php echo $this->render('/components/mobile-number-field') ?>

                <div class="flex flex-col mb-7" x-show="tab === 'email'">
                    <label class="label" for="email">Email</label>
                    <input class="w-full input base" type="email" id="email"
                           x-model="formData.username.value" :class="{'invalid': !formData.username.valid}"/>

                    <template x-for="error in formData.username.errors">
                        <p class="message error" x-text="error"></p>
                    </template>
                </div>

                <div class="g-recaptcha mx-auto mb-7" style="width: 304px"
                     :data-sitekey="RECAPTCHA_CHALLENGE_KEY"></div>

                <div class="flex mb-10 justify-center">
                    <button class="btn xoutline on-primary on-primary-state w-full mr-4" type="button"
                            @click="redirect('login')">Back
                    </button>

                    <?php echo $this->render('/components/primary-btn', ['text' => 'Continue', 'class' => 'w-full']) ?>
                </div>
            </form>
        </div>

        <div class="py-5 border-t surface regular-0_875rem text-center">
            <p class="mb-1">Forgot email and mobile number?</p>
            <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
        </div>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let form_data = new FormData(this.$event.target);
        let recaptcha_data = Object.fromEntries(form_data.entries());

        let data = {
            type: this.tab,
            username: this.formData.username.value
        };

        if (data.type === 'hp') {
            data.country_id = this.formData.selected.countries_id;
        }

        data.recaptcha_token = recaptcha_data['g-recaptcha-response'];

        if (!RECAPTCHA_DISABLED && data.recaptcha_token === '') {
            this.general['error'] = 'Please select the reCAPTCHA challenge to continue.';
            this.general['error_support'] = '';
            return false;
        }

        await api('forget/index', 'post', data, this.formData, this.general);

        grecaptcha.reset();
    }
</script>