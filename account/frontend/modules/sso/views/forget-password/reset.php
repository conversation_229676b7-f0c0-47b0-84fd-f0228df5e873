<script>
    Alpine.data('form', () => ({
        formData: {
            password: {value: null, valid: 1},
            password_confirm: {value: null, valid: 1},
            token: {value: null, valid: 1},
        }
    }));
</script>

<div class="auth-container px-4 sm:px-12" x-data="form">
    <div class="py-5 text-center semibold-1_5rem base">
        Reset Password
    </div>

    <form @submit.prevent="submit" novalidate>
        <?php echo $this->render('/components/password') ?>
        <?php echo $this->render('/components/password', ['confirm' => 1, 'class' => 'mb-7']) ?>

        <?php echo $this->render('/components/primary-btn', ['text' => 'Reset Password', 'class' => 'mb-10 w-full']) ?>
    </form>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let params = new URLSearchParams(window.location.search);

        let data = {
            token: params.get('token'),
            password: this.formData.password.value,
            password_confirm: this.formData.password_confirm.value,
        };

        await api('forget/reset', 'post', data, this.formData, this.general);
    }
</script>