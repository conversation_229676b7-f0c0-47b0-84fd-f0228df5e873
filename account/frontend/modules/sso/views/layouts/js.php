<?php

$errorMessage = is_string(Yii::$app->session->getFlash("error"))
    ? Yii::$app->session->getFlash("error") : "";

$errorAction = is_string(Yii::$app->session->getFlash("error-action"))
    ? Yii::$app->session->getFlash("error-action") : '';
?>
<script>
    Alpine.data('general', () => ({
        general: {
            error: '<?php echo $errorMessage; ?>',
            error_support: '<?php echo $errorAction; ?>',
            loading: false
        },
        clear() {
            this.general.error = '';
        }
    }));

    Alpine.data('pin', () => ({
        otp: [],

        init() {
            for (let i = 0; i < 6; i++) {
                this.otp[i] = '';
            }
        },
        handleKeyDown(index, event) {
            let refs = null;

            if (/^[A-Za-z]$/.test(event.key) && !((event.ctrlKey || event.metaKey) && event.key === 'v')) {
                event.preventDefault();
                return;
            }

            if (/[!#@)\[\]\\<>,.($%&'*+\-/=?^_`{|}~]/.test(event.key)) {
                event.preventDefault();
                return;
            }

            if (event.key >= '0' && event.key <= '9') {
                this.otp[index] = event.key;
                refs = document.querySelector(`[x-ref="box${index + 1}"]`);
                refs?.focus();
                event.preventDefault();
            } else if (event.key === 'Backspace') {
                if (!this.otp[index]) {
                    event.preventDefault();

                    if (index > 0) {
                        this.otp[index - 1] = '';

                        refs = document.querySelector(`[x-ref="box${index - 1}"]`);
                        refs?.focus();
                    }
                }
            }
        },
        handlePaste(index, event) {
            event.preventDefault();
            let refs = null;
            let text = event.clipboardData.getData('text/plain');

            if (text.length === 0 || isNaN(text)) return;

            let pastedValues = text.split('');
            let lastIndex = index;

            pastedValues.forEach((char, i) => {
                if (this.otp[index + i] !== undefined) {
                    this.otp[index + i] = char;
                    lastIndex = index + i;
                }
            })

            refs = document.querySelector(`[x-ref="box${lastIndex}"]`);
            refs?.focus();
        }
    }));

    Alpine.data('countdown', () => ({
        countdown: null,
        disabled: false,
        countdownInterval: null,
        init() {
            //Only start countdown when redirect from another page
            const navigationType = performance.getEntriesByType("navigation")[0].type;

            if (navigationType !== 'reload') {
                this.startCountdown();
            }
        },
        async initSendOtp() {
            this.general.error = '';

            let data = {
                type: this.formData.type.value,
            };

            if (this.tab) {
                data.via = this.tab;
            }

            this.disabled = true;

            const response = await api('otp/send-init', 'post', data, this.formData, this.general, 1);

            this.disabled = false;

            if (response.data.status === 200) {
                this.startCountdown()

                if (response.data.data['test_otp']) {
                    this.general.error = 'Testing OTP: ' + response.data.data['test_otp'];
                }
            }
        },
        async sendOtp() {
            if (this.countdown || this.disabled) {
                return;
            }

            this.general.error = '';

            let data = {
                type: this.formData.type.value,
            };

            if (this.tab) {
                data.via = this.tab;
            }

            this.disabled = true;

            const response = await api('otp/send', 'post', data, this.formData, this.general, 1);

            this.disabled = false;

            if (response.data.status === 200) {
                this.startCountdown()

                if (response.data.data['test_otp']) {
                    this.general.error = 'Testing OTP: ' + response.data.data['test_otp'];
                }
                return;
            }

            handleError(response, this.formData, this.general);

        },
        startCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }
            this.countdown = 60;

            this.countdownInterval = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    clearInterval(this.countdownInterval);
                    this.countdown = null;
                }
            }, 1000);
        }
    }))

    Alpine.data('tabs', () => ({
        tab: 'email',
        email_class: 'active',
        hp_class: '',

        toggle(type) {
            this.tab = type;
            this.email_class = this.tab === 'email' ? 'active' : '';
            this.hp_class = this.tab === 'hp' ? 'active' : '';

            Object.keys(this.formData).forEach(key => {
                if (this.formData[key]?.valid !== undefined && this.formData[key]?.errors !== undefined) {
                    this.formData[key].valid = 1;
                    this.formData[key].errors = [];
                }

                if (key === 'username') {
                    this.formData[key].value = null;
                }
            });

        },
    }))

    Alpine.data('dropdown', () => ({
        open: false,
        selected: {},

        toggle() {
            this.open = !this.open;
        },
        close() {
            this.open = false;
        },
        select(data) {
            this.selected = data;
            this.formData.selected = this.selected;
            this.close();
        }
    }))

    Alpine.data('mobile_country_list', () => ({
        countries: [],
        results: [],
        keyword: '',

        async init() {
            const response = await axios.get(AJAX_URL + 'country/index');
            const current_resp = await axios.get(AJAX_URL + 'country/current');
            this.countries = response.data.data;
            this.results = this.countries;

            this.countries.forEach((v, k) => {
                this.countries[k].countries_id = parseInt(v.countries_id);
                this.countries[k].countries_iso_code_2 = this.countries[k].countries_iso_code_2.toLowerCase();

                if (v.countries_id === current_resp.data.data.countries_id) {
                    this.selected = this.countries[k];
                    this.formData.selected = this.selected;
                }
            });
        },
        clear() {
            this.keyword = '';
            this.results = this.countries;
        },
        search() {
            let keyword = this.keyword.replace(/\+/g, '');
            keyword = keyword.toLowerCase();

            this.results = this.countries.filter(item => {
                return item.countries_name.toLowerCase().includes(keyword) ||
                    item.countries_international_dialing_code?.includes(keyword);
            });
        }
    }))

    Alpine.data('password', () => ({
        type: 'password',
        icon_class: 'icon-visibility',

        toggle() {
            this.type = this.type === 'password' ? 'text' : 'password';
            this.icon_class = this.type === 'text' ? 'icon-visibility_off' : 'icon-visibility';
        },
    }))

    Alpine.data('remember_me', () => ({
        button_class: 'text-primary on-primary-state',
        icon_class: 'icon-check_box',
        dremember_me: true,

        toggle() {
            this.dremember_me = !this.dremember_me;
            this.button_class = this.dremember_me ? 'text-primary on-primary-state' : 'text-on-surface surface-state';
            this.icon_class = this.dremember_me ? 'icon-check_box' : 'icon-check_box_outline_blank';

            this.formData.remember_me = this.dremember_me;
        },
    }))

    async function api(url, method, data, formData, general, stop = 0) {
        url = AJAX_URL + url;
        try {
            general.loading = true;

            let resp = method === 'get' ? await axios.get(url) : await axios.post(url, data);

            handleSuccess(resp);

            return resp;
        } catch (error) {
            general.loading = false;
            handleError(error, formData, general);
            return error.response;
        }
        finally {
            if(stop) {
                general.loading = false;
            }
        }
    }

    function clearError(formData, general) {
        Object.keys(formData).forEach(k => {
            if (formData[k]?.valid === 0) {
                formData[k].valid = 1;
                formData[k].errors = [];
            }
        });

        general.error = '';
    }

    function handleSuccess(response) {
        if (response.data.status === 301) {
            redirect(response.data.data.redirect_url);
        }
    }

    function handleError(response, formData, general) {
        response = response.response;

        let status = response.status;

        if (status === 422) {
            let errors = response.data.data;

            Object.keys(errors).forEach(k => {
                if (formData[k]) {
                    formData[k].valid = 0;
                    formData[k].errors = errors[k];
                }
            });

            return;
        }

        if (status === 400) {
            general.error = response.data.message;
            general.error_support = response.data.data?.action ? response.data.data.action : '';
        }
    }

    function redirect(url, data, delay, fullUrl) {

        if (!fullUrl) {
            url = '/sso/' + url;
        }

        if (typeof data === 'object') {
            url = url + '?' + objectToUrlParams(data)
        }

        delay = delay ? delay : 0;

        setTimeout(function () {
            window.location.href = url;
        }, delay);
    }

    function objectToUrlParams(data) {
        return Object.entries(data)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    }

    function openChat() {
        window.fcWidget.open();
        window.fcWidget.show();
    }

</script>