<?php

use app\modules\sso\assets\AppAsset;
use app\modules\sso\components\PVM;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $content string */

AppAsset::register($this);
$publish_url = AppAsset::getUrl();

?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">

<head>
    <?php
    $routeId = Yii::$app->controller->action->getUniqueId();
    if ($routeId == 'sso/sso/login' || $routeId == 'sso/sso/sign-up') {
    ?>
        <meta name="robots" content="noindex">
    <?php
    }
    ?>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript">
        let RECAPTCHA_KEY = '<?php echo Yii::$app->params['sso']['providers']['google']['recaptcha']['key'] ?>';
        let RECAPTCHA_CHALLENGE_KEY = '<?php echo Yii::$app->params['sso']['providers']['google']['recaptcha_challenge']['key'] ?>';
        let HOME_URL = '<?php echo Yii::$app->params['sso']['home_url'] ?>';
        let BASE_URL = '<?php echo Yii::$app->request->baseUrl; ?>';
        let STATIC_DOMAIN = '<?php echo $publish_url; ?>';
        let REGION_STATIC_DOMAIN = "<?php echo Yii::$app->params['REGION_STATIC_DOMAIN']; ?>";
        let CSRF_TOKEN_NAME = '<?php echo Yii::$app->request->csrfParam; ?>';
        let CSRF_TOKEN = '<?php echo Yii::$app->request->csrfToken; ?>';
        let AJAX_URL = '/sso/api/';
        let RECAPTCHA_DISABLED = '<?php echo PVM::isIpException(); ?>';
    </script>
    <?= Html::csrfMetaTags() ?>
    <title><?= Html::encode(Yii::$app->name) ?></title>
    <?php $this->head() ?>
    <?php if (!empty(Yii::$app->params['GTM_ID'])) { ?>
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    '//www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '<?php echo Yii::$app->params['GTM_ID']; ?>');
        </script>
        <!-- End Google Tag Manager -->
    <?php } ?>
</head>

<body>
    <!-- Google Tag Manager -->
    <noscript>
        <iframe src="//www.googletagmanager.com/ns.html?id=<?php echo Yii::$app->params['GTM_ID']; ?>" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager -->

    <?php $this->beginBody() ?>
    <?php echo $this->render('js') ?>

    <div class="flex flex-col min-h-screen" x-data="general">
        <?php echo $this->render('/components/general-error-message'); ?>
        <main class="flex-grow">
            <div class="py-5 flex justify-center max-w-md mx-auto">
                <a x-bind:href="HOME_URL">
                    <img class="sm:w-72 w-48" src="<?php echo $publish_url; ?>images/logo.png"
                        srcset="<?php echo $publish_url; ?>images/logo.png 1x, <?php echo $publish_url; ?>images/<EMAIL> 2x"
                        alt="logo">
                </a>
            </div>

            <?php echo $content; ?>
        </main>
        <footer class="text-[#AAABAE] text-center px-4 w-full regular-0_75rem mt-auto">
            <script>
                <?php
                echo (new \frontend\components\Chat)->getFreshworksContent(
                    'window.fwSettings={widget_id:5000000543},function(){var i;"function"!=typeof window.FreshworksWidget&&((i=function(){i.q.push(arguments)}).q=[],window.FreshworksWidget=i)}(),FreshworksWidget("hide","launcher"),FreshworksWidget("identify","ticketForm",FRESH_WORKS_PARAMS);'
                );
                ?>
            </script>
            <script src='https://widget.freshworks.com/widgets/5000000543.js'></script>
            <div class="pt-9">
                <a class="mr-4 semibold-0_75rem text-on-background cursor-pointer" @click="FreshworksWidget('open');">Support</a>
                <a class="mr-4 semibold-0_75rem text-on-background cursor-pointer" :href="HOME_URL + '/terms-of-service'" target="_blank">Terms & Conditions</a>
                <a class="semibold-0_75rem text-on-surface cursor-pointer" :href="HOME_URL + '/privacy-policy'" target="_blank">Privacy Policy</a>
            </div>
            <div class="py-5">
                <p>Copyright &copy <?php echo date('Y'); ?> OffGamers. All rights reserved.</p>
                <p>All product names are trademarks of their respective companies.</p>
            </div>
        </footer>
    </div>
    <?php $this->endBody() ?>
</body>

</html>
<?php $this->endPage() ?>