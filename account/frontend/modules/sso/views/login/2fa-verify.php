<script>
    Alpine.data('form', () => ({
        formData: {
            type: {value: 'login', valid: 1},
            answer: {value: '', valid: 1}
        }
    }));
</script>

<div class="auth-container" x-data="tabs">
    <div class="pt-5 text-center semibold-1_5rem mb-5 base">
        Two-Factor Authentication
    </div>

    <div class="w-[17.75rem] mx-auto mb-10" x-data="form">
        <div class="sys-message primary-container flex mb-3">
            <i class="mr-2 icon icon-info text-primary"></i>
            <div>
                <p class="mb-2">Enter the OffGamers Authentication code generated by your authentication app</p>
                <a class="a" :href="BASE_URL + '/site/page?view=2fa'" target="_blank">Learn More</a>
            </div>
        </div>

        <form @submit.prevent="submit" x-data="pin" autocomplete="off">
            <?php echo $this->render('/components/otp-field') ?>

            <div class="flex">
                <button class="btn xoutline on-primary on-primary-state w-[134px] mr-4" type="button"
                        @click="redirect('login')">Back
                </button>

                <?php echo $this->render('/components/primary-btn', ['text' => 'Continue', 'class' => 'w-[134px]']) ?>
            </div>
        </form>
    </div>

    <div class="py-5 text-center border-t surface regular-0_875rem">
        <p class="mb-1">Problem with 2FA?</p>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        this.formData.answer.value = '';

        this.otp.forEach((v) => {
            this.formData.answer.value += v;
        })
        let data = {
            via: this.tab,
            answer: this.formData.answer.value,
            type: this.formData.type.value
        };

        const response = await api('two-fa/verify', 'post', data, this.formData, this.general);

        if (response.data.status === 200) {
            await api('login/session', 'post', {}, this.formData, this.general);
        }
    }

</script>
