<script>
    Alpine.data('form', () => ({
        formData: {
            type: {value: 'setup', valid: 1},
            email_encoded: {value: null, valid: 1},
        },
        async init() {
            const response = await api('login/data', 'get', {}, this.formData, this.general, 1);
            if (response.data.data) {
                Object.keys(this.formData).forEach(k => {
                    if (response.data.data[k]) {
                        this.formData[k].value = response.data.data[k];
                    }
                });
            }
        }
    }));
</script>

<div class="auth-container px-4 sm:px-12" x-data="form">
    <div class="py-5 text-center semibold-1_5rem base">
        Login
    </div>

    <div class="mb-5 text-center regular-0_875rem text-on-surface">
        <p>The mobile number is registered to</p>
        <p class="semibold-0_875rem" x-text="formData.email_encoded.value"></p>
    </div>

    <button class="btn primary primary-state mb-4 w-full" @click="redirect('login')">Back to Login</button>

    <button class="btn xoutline on-primary on-primary-state mb-4 w-full" @click="redirect('sign-up')">Use New Mobile
        Number
    </button>

    <button class="btn xoutline on-primary on-primary-state mb-10 w-full" @click="reclaim(formData, general)"
            :disabled="general.loading">
        <i class="icon loading-icon icon-spinner" x-show="general.loading"></i>
        <span x-show="!general.loading">Reclaim Mobile Number</span>
    </button>
</div>

<script>
    async function reclaim(formData, general) {
        const response = await api('setup/reclaim', 'get', {}, formData, general);

        if (response.data.status === 200) {
            const response = await api('setup/check-account', 'get', {}, formData, general);

            if (response.data.status === 200) {
                let data = {
                    type: formData.type.value
                };

                await api('login/session', 'post', data, formData, general);
            }
        }
    }
</script>
