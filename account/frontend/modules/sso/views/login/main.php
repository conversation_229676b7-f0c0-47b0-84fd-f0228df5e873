<?php

use app\modules\sso\assets\RecaptchaChallengeAsset;

RecaptchaChallengeAsset::register($this);

?>

<script>
    Alpine.data('form', () => ({
        formData: {
            country_id: {value: 'email', valid: 1},
            username: {value: null, valid: 1},
            password: {value: null, valid: 1},
            remember_me: true,
            selected: null,
        }
    }));
</script>

<div class="auth-container" x-data="form">
    <div x-data="tabs">
        <div class="py-5 text-center semibold-1_5rem base">
            Login
        </div>

        <?php echo $this->render('/components/email-hp-tab-bar'); ?>

        <div class="px-4 sm:px-12">
            <form @submit.prevent="submit" novalidate>
                <?php echo $this->render('/components/mobile-number-field', ['margin' => 'mb-5']) ?>

                <div class="flex flex-col mb-5" x-show="tab === 'email'">
                    <label class="label" for="email">Email</label>
                    <input class="w-full input base" type="email" id="email"
                           x-model="formData.username.value" :class="{'invalid': !formData.username.valid}"/>

                    <template x-for="error in formData.username.errors">
                        <p class="message error" x-text="error"></p>
                    </template>
                </div>

                <?php echo $this->render('/components/password') ?>

                <div class="flex items-center place-content-between mb-7">
                    <div class="selection" x-data="remember_me">
                        <button class="icon-btn" type="button" :class="button_class" @click="toggle()">
                            <i class="icon" :class="icon_class"></i>
                        </button>
                        <p>Remember me</p>
                    </div>

                    <a class="a" @click="redirect('forget')">Forgot Password?</a>
                </div>
                <div class="g-recaptcha mx-auto mb-7" style="width: 304px"
                     :data-sitekey="RECAPTCHA_CHALLENGE_KEY"></div>
                <?php echo $this->render('/components/primary-btn', ['text' => 'Login', 'class' => 'mb-7 w-full']) ?>
            </form>

            <div class="flex mb-5 text-outline regular-0_75rem items-center">
                <span class="line"></span>
                <span class="mx-3">OR</span>
                <span class="line"></span>
            </div>

            <?php echo $this->render('/components/sns') ?>
        </div>


        <div class="py-5 flex justify-center regular-0_875rem border-t text-on-surface">
            <p class="mr-1">Not a member yet?</p>
            <a class="a" @click="redirect('sign-up')">Sign Up</a>
        </div>
    </div>

</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let form_data = new FormData(this.$event.target);
        let recaptcha_data = Object.fromEntries(form_data.entries());

        let data = {
            type: this.tab,
            username: this.formData.username.value,
            password: this.formData.password.value,
            remember_me: this.formData.remember_me,
        };

        if (data.type === 'hp') {
            data.country_id = this.formData.selected.countries_id;
        }

        data.recaptcha_token = recaptcha_data['g-recaptcha-response'];

        if (!RECAPTCHA_DISABLED && data.recaptcha_token === '') {
            this.general['error'] = 'Please select the reCAPTCHA challenge to continue.';
            this.general['error_support'] = '';
            return false;
        }

        await api('login/index', 'post', data, this.formData, this.general);

        grecaptcha.reset();
    }
</script>