<script>
    Alpine.data('form', () => ({
        formData: {
            type: {value: 'login', valid: 1},
            answer: {value: '', valid: 1},
            country: {value: null, valid: 1},
            hp_encoded: {value: null, valid: 1},
            email_encoded: {value: null, valid: 1},
        },
        async init() {
            const response = await api('login/data', 'get', {}, this.formData, this.general, 1);

            if (response.data.data) {
                Object.keys(this.formData).forEach(k => {
                    if (response.data.data[k] && k !== 'type') {
                        this.formData[k].value = response.data.data[k];
                    }
                });

                if (response.data.data['test_otp']) {
                    this.general.error = 'Testing OTP: ' + response.data.data['test_otp'];
                }
            }
        }
    }));

    Alpine.data('device_pin', () => ({
        tab: 'email',

        switchMethod(type) {
            this.tab = type;
        }
    }));
</script>

<div class="auth-container" x-data="device_pin">
    <div class="pt-5 text-center semibold-1_5rem mb-1 base">
        New Device Detected
    </div>

    <p class="regular-0_875rem text-unfocus text-center mb-5">
        Logged in from a new device?<br>
        Enter Device Pin to continue.
    </p>

    <div class="w-[17.75rem] mx-auto mb-10" x-data="form">
        <div class="sys-message primary-container flex mb-3">
            <i class="icon icon-info text-primary"></i>
            <div :class="{flex : tab === 'hp'}">
                <p class="mr-1">Device Pin sent to</p>
                <p class="semibold-0_875rem" x-show="tab === 'email'" x-text="formData.email_encoded.value"></p>
                <p class="semibold-0_875rem" x-show="tab === 'hp'">
                    (+<span x-text="formData.country.value?.countries_international_dialing_code"></span>)
                    <span x-text="formData.hp_encoded.value"></span>
                </p>
            </div>
        </div>

        <form @submit.prevent="submit" x-data="pin" autocomplete="off">
            <?php echo $this->render('/components/otp-field', ['class' => 'mb-3']) ?>

            <div class="flex justify-center mb-7 regular-0_875rem" x-data="countdown"
                 x-init="$watch('tab', () => initSendOtp())">
                <a class="a" @click="sendOtp()" x-show="!countdown">Resend Device Pin</a>
                <p class="text-unfocus" x-show="countdown">Resend in <span x-text="countdown"></span> seconds...</p>
            </div>

            <div class="flex">
                <button class="btn xoutline on-primary on-primary-state w-[134px] mr-4" type="button"
                        @click="redirect('login')">Back
                </button>

                <?php echo $this->render('/components/primary-btn', ['text' => 'Continue', 'class' => 'w-[134px]']) ?>
            </div>
        </form>
    </div>

    <div class="py-5 text-center border-t surface regular-0_875rem">
        <p class="mb-1">Try other options?</p>
        <a class="a" @click="tab = 'hp'" x-show="tab === 'email'">Send to SMS</a>
        <a class="a" @click="tab = 'email'" x-show="tab === 'hp'">Send to Email</a>
        <span class="text-unfocus mx-1">or</span>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        this.formData.answer.value = '';

        this.otp.forEach((v) => {
            this.formData.answer.value += v;
        })
        let data = {
            via: this.tab,
            answer: this.formData.answer.value,
            type: this.formData.type.value
        };

        const response = await api('otp/verify', 'post', data, this.formData, this.general);

        if (response.data.status === 200) {
            await api('login/session', 'post', {}, this.formData, this.general);
        }
    }

</script>