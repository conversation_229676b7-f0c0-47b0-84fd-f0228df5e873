<?php
use app\modules\sso\assets\RecaptchaAsset;
RecaptchaAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        tab: 'hp',
        formData: {
            country_id: {value: '', valid: 1},
            username: {value: null, valid: 1},
            selected: null
        }
    }));
</script>

<div class="auth-container" x-data="tabs">
    <div class="py-5 text-center semibold-1_5rem base">
        Login
    </div>

    <div class="px-4 sm:px-12" x-data="form">
        <p class="text-center regular-0_875rem text-unfocus mb-5">
            Please verify your mobile number
        </p>

        <form @submit.prevent="submit">
            <?php echo $this->render('/components/mobile-number-field') ?>

            <button class="btn primary primary-state mb-4 w-full" :disabled="general.loading">Continue</button>
        </form>
    </div>

    <div class="py-5 flex border-t surface regular-0_875rem justify-center">
        <p class="mr-1">Need help?</p>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let data = {
            username: this.formData.username.value,
            country_id: this.formData.selected.countries_id
        };

        data.recaptcha_token = await grecaptcha.execute(RECAPTCHA_KEY, {action: 'submit'});

        await api('setup/index', 'post', data, this.formData, this.general);
    }
</script>