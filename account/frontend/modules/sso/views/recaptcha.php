<?php

use app\modules\sso\assets\RecaptchaChallengeAsset;

RecaptchaChallengeAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        formData: {}
    }));
</script>

<div class="auth-container" x-data="form">
    <div class="py-5 text-center semibold-1_5rem base">
        Verify you are human
    </div>

    <div class="px-4 sm:px-12">
        <form @submit.prevent="submit" novalidate>
            <div class="g-recaptcha mx-auto mb-7" style="width: 304px" :data-sitekey="RECAPTCHA_CHALLENGE_KEY"></div>

            <?php echo $this->render('/components/primary-btn', ['text' => 'Submit', 'class' => 'mb-7 w-full']) ?>
        </form>
    </div>
</div>

<script>
    async function submit() {
        let form_data = new FormData(this.$event.target);
        let recaptcha_data = Object.fromEntries(form_data.entries());
        let data = {};
        let urlParams = new URLSearchParams(window.location.search);

        data.redirect_url = urlParams.get('redirect_url');
        data.recaptcha_token = recaptcha_data['g-recaptcha-response'];

        const response = await api('recaptcha/index', 'post', data, this.formData, this.general);

        handleError(response, this.formData, this.general);
    }
</script>