<?php

use app\modules\sso\assets\RecaptchaAsset;

RecaptchaAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        formData: {
            firstname: {value: '', valid: 1},
            lastname: {value: '', valid: 1},
            email: {value: '', valid: 1},
            password: {value: '', valid: 1},
        }
    }));
</script>

<div class="auth-container">
    <div class="pt-5 text-center semibold-1_5rem mb-3 base">
        Sign Up
    </div>

    <?php echo $this->render('/components/stepper-3', ['stepper' => ['', 'active']]); ?>

    <div class="my-10 px-4 sm:px-12" x-data="form">
        <p class="text-center regular-0_875rem text-unfocus mb-5">
            First name and last name cannot be changed after created.
        </p>

        <form @submit.prevent="submit" novalidate>
            <div class="flex flex-col mb-5">
                <label class="label" for="firstname">First Name</label>
                <input class="input base w-full" id="firstname"
                       x-model="formData.firstname.value" :class="{'invalid': !formData.firstname.valid}"/>

                <template x-for="error in formData.firstname.errors">
                    <p class="message error" x-text="error"></p>
                </template>
            </div>

            <div class="flex flex-col mb-5">
                <label class="label" for="lastname">Last Name</label>
                <input class="input base w-full" id="lastname"
                       x-model="formData.lastname.value" :class="{'invalid': !formData.lastname.valid}"/>

                <template x-for="error in formData.lastname.errors">
                    <p class="message error" x-text="error"></p>
                </template>
            </div>

            <div class="flex flex-col mb-5">
                <label class="label" for="email">Email</label>
                <input class="input base w-full" type="email" id="email"
                       x-model="formData.email.value" :class="{'invalid': !formData.email.valid}"/>

                <template x-for="error in formData.email.errors">
                    <p class="message error" x-text="error"></p>
                </template>
            </div>

            <?php echo $this->render('/components/password', ['class' => 'mb-7']) ?>

            <?php echo $this->render('/components/primary-btn', ['text' => 'Continue', 'class' => 'w-full']) ?>
        </form>
    </div>

    <div class="py-5 flex border-t surface regular-0_875rem justify-center">
        <p class="mr-1">Need help?</p>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let data = {
            firstname: this.formData.firstname.value,
            lastname: this.formData.lastname.value,
            email: this.formData.email.value,
            password: this.formData.password.value,
        };

        data.recaptcha_token = await grecaptcha.execute(RECAPTCHA_KEY, {action: 'submit'});

        await api('register/create-account', 'post', data, this.formData, this.general);
    }
</script>