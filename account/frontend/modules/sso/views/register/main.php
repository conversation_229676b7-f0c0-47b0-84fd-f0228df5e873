<?php

use app\modules\sso\assets\RecaptchaChallengeAsset;

RecaptchaChallengeAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        tab: 'hp',
        formData: {
            country_id: {value: '', valid: 1},
            username: {value: null, valid: 1},
            selected: null
        }
    }));
</script>

<div class="auth-container">
    <div class="py-5 text-center semibold-1_5rem base">
        Sign Up
    </div>

    <div class="px-4 sm:px-12" x-data="form">
        <form @submit.prevent="submit" novalidate>
            <?php echo $this->render('/components/mobile-number-field') ?>
            <div class="g-recaptcha mx-auto mb-7" style="width: 304px" :data-sitekey="RECAPTCHA_CHALLENGE_KEY"></div>

            <?php echo $this->render('/components/primary-btn', ['text' => 'Sign Up', 'class' => 'mb-1 w-full']) ?>
        </form>

        <div class="text-unfocus regular-0_75rem mb-7">
            By signing up, you agree to our
            <a class="a small" :href="HOME_URL + '/terms-of-service'" target="_blank">Terms of Service</a>
            and
            <a class="a small" :href="HOME_URL + '/privacy-policy'" target="_blank">Privacy Policy</a>
        </div>

        <div class="flex mb-5 text-outline regular-0_75rem items-center">
            <span class="line"></span>
            <span class="mx-3">OR</span>
            <span class="line"></span>
        </div>

        <?php echo $this->render('/components/sns') ?>
    </div>

    <div class="py-5 flex justify-center regular-0_875rem border-t text-on-surface">
        <p class="mr-1">Already a member?</p>
        <a class="a" @click="redirect('login')">Login</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);
        let form_data = new FormData(this.$event.target);
        let recaptcha_data = Object.fromEntries(form_data.entries());

        let data = {
            username: this.formData.username.value,
            country_id: this.formData.selected.countries_id
        };

        data.recaptcha_token = recaptcha_data['g-recaptcha-response'];

        if (!RECAPTCHA_DISABLED && data.recaptcha_token === '') {
            this.general['error'] = 'Please select the reCAPTCHA challenge to continue.';
            this.general['error_support'] = '';
            return false;
        }

        await api('register/index', 'post', data, this.formData, this.general);

        grecaptcha.reset();
    }
</script>

