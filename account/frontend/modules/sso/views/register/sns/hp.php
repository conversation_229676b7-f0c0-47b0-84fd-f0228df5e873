<?php
use app\modules\sso\assets\RecaptchaAsset;
RecaptchaAsset::register($this);
?>

<script>
    Alpine.data('form', () => ({
        tab: 'hp',
        formData: {
            country_id: {value: '', valid: 1},
            username: {value: null, valid: 1},
            selected: null
        }
    }));
</script>

<div class="auth-container">
    <div class="pt-5 text-center semibold-1_5rem mb-3 base">
        Sign Up Via <?php echo $provider; ?>
    </div>

    <?php echo $this->render('/components/stepper-4', ['stepper' => ['active']]); ?>

    <div class="my-10 px-4 sm:px-12" x-data="form">
        <form @submit.prevent="submit">
            <?php echo $this->render('/components/mobile-number-field') ?>

            <div class="flex mb-4">
                <button class="btn xoutline on-primary on-primary-state w-full mr-4" type="button"
                        @click="redirect('sign-up')">Back</button>
                <?php echo $this->render('/components/primary-btn', ['text' => 'Sign Up', 'class' => 'w-full']) ?>
            </div>
        </form>

        <div class="text-unfocus regular-0_75rem mb-7">
            By signing up, you agree to our
            <a class="a small" :href="HOME_URL + '/terms-of-service'" target="_blank">Terms of Service</a>
            and
            <a class="a small" :href="HOME_URL + '/privacy-policy'" target="_blank">Privacy Policy</a>
        </div>
    </div>

    <div class="py-5 flex border-t surface regular-0_875rem justify-center">
        <p class="mr-1">Need help?</p>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        let data = {
            username: this.formData.username.value,
            country_id: this.formData.selected.countries_id
        };

        data.recaptcha_token = await grecaptcha.execute(RECAPTCHA_KEY, {action: 'submit'});

        await api('register/index', 'post', data, this.formData, this.general);
    }
</script>