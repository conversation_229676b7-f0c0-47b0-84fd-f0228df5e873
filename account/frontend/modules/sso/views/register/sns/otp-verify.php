<script>
    Alpine.data('form', () => ({
        formData: {
            type: {value: 'register', valid: 1},
            answer: {value: '', valid: 1},
            country: {value: null, valid: 1},
            username_encoded: {value: null, valid: 1},
        },
        async init() {
            const response = await api('register/data', 'get', {}, this.formData, this.general, 1);

            if (response.data.data) {
                Object.keys(this.formData).forEach(k => {
                    if (response.data.data[k] && k !== 'type') {
                        this.formData[k].value = response.data.data[k];
                    }
                });

                if (response.data.data['test_otp']) {
                    this.general.error = 'Testing OTP: ' + response.data.data['test_otp'];
                }
            }
        }
    }));
</script>

<div class="auth-container">
    <div class="pt-5 text-center semibold-1_5rem mb-3 base">
        Sign Up
    </div>

    <?php echo $this->render('/components/stepper-4', ['stepper' => ['', 'active']]); ?>

    <div class="w-[17.75rem] mx-auto my-10" x-data="form">
        <div class="sys-message primary-container mb-3 flex items-center">
            <i class="icon icon-info text-primary"></i>
            <p>OTP sent to</p>
            <p class="semibold-0_875rem">
                (+<span x-text="formData.country.value?.countries_international_dialing_code"></span>)
                <span x-text="formData.username_encoded.value"></span>
            </p>
        </div>

        <form @submit.prevent="submit" novalidate x-data="pin" autocomplete="off">
            <?php echo $this->render('/components/otp-field', ['class' => 'mb-3']) ?>

            <div class="flex justify-center mb-7 regular-0_875rem" x-data="countdown">
                <a class="a" @click="sendOtp()" x-show="!countdown">Resend OTP</a>
                <p class="text-unfocus" x-show="countdown">Resend in <span x-text="countdown"></span> seconds...</p>
            </div>

            <div class="flex">
                <button class="btn xoutline on-primary on-primary-state w-[134px] mr-4" type="button"
                        @click="redirect('sns/sign-up')">Back
                </button>
                <?php echo $this->render('/components/primary-btn', ['text' => 'Continue', 'class' => 'w-[134px]']) ?>
            </div>
        </form>
    </div>

    <div class="py-5 flex border-t surface regular-0_875rem justify-center">
        <p class="mr-1">Need help?</p>
        <a class="a" @click="FreshworksWidget('open');">Contact Support</a>
    </div>
</div>

<script>
    async function submit() {
        clearError(this.formData, this.general);

        this.formData.answer.value = '';

        this.otp.forEach((v) => {
            this.formData.answer.value += v;
        })
        let data = {
            answer: this.formData.answer.value,
            type: this.formData.type.value
        };

        const response = await api('otp/verify', 'post', data, this.formData, this.general);

        if (response.data.status === 200) {
            await api('register/check-account', 'get', {}, this.formData, this.general);
        }
    }
</script>