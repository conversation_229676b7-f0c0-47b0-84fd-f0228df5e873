<?php
/* @var $this \yii\web\View */

$ctry_code = Yii::$app->session["ctry_code"];
$cur_code = Yii::$app->session["cur_code"];
$lang_code = Yii::$app->session["lang_code"];

$js = <<<JS
    userBar.init("$ctry_code", "$cur_code", "$lang_code");

    // user bar > user profile menu
    jQuery("#user-menu").click(function() {
        if (jQuery("#user-menu-wor").html() == "") {
            jQuery.ajax({
                type: "GET",
                dataType: "json",
                url: "/user-bar/user-menu",
                success: function(data) {
                    //jQuery("#user-menu-sc").html(data.result["store_credit"]);
                    jQuery("#user-menu-wor").html(data.result["wor"]);
//                    jQuery("#user-menu-sc-load").css("display", "none");
                    jQuery("#user-menu-wor-load").css("display", "none");
                    jQuery("#user-menu-wor-ico").css("display", "inline-block");
                },
                error: function(jqXHR, status, error) {
                    if (jqXHR.status < 500) {
                        //Session / csrf-related - refresh browser
                        document.location.reload();
                    } else {
                        toastr['error']('Unknown error has occured. Please retry again or contact the administrator');
                    }
                },
            });
        }
    });
JS;
$this->registerJs($js, \yii\web\View::POS_READY);
?>
<!-- parser URL query -->
<?php $f_query = \common\components\SSOCom::clientURLQuery(); ?>
<?php $f_query = (!empty($f_query) ? '?' . $f_query : ''); ?>

<div class="headsection sticky" id="sticky-header">
    <span class="burger" onclick="openNav()"><i class="fa fa-bars"></i></span>

    <a class="head-logo" href="<?php echo Yii::$app->params['OG_URL'] . $f_query; ?>">
        <div class="sa-logo-sm"></div>
    </a>

    <span class="head-profile btn-expand-collapse" id="user-menu" data-target="dropdown-profile">
        <?php
        if (isset(Yii::$app->user->sns) && isset(Yii::$app->user->sns['photo'])) {
            echo \yii\helpers\Html::img(Yii::$app->user->sns['photo'], [
                'width' => '18px',
            ]);
        } else {
            echo \yii\helpers\Html::tag('div', '', ['class' => 'fa fa-user']);
        }
        ?>
    </span>
    <span class="head-region btn-expand-collapse" id="reg-setting" data-target="dropdown-region"><i class="fa fa-globe"></i></span>
</div>
<div class="headsection-dropdownsection">

    <!-- PROFILE DROPDOWN -->

    <div class="headsection-dropdown collapse-box auto-collapse" data-group='header' id="dropdown-profile">
        <div class="headsection-dropdown-content">

            <div class="avatar-profile">
                <div class="avatar">
                    <?php
                    if (isset(Yii::$app->user->sns) && isset(Yii::$app->user->sns['photo'])) {
                        echo \yii\helpers\Html::img(Yii::$app->user->sns['photo'], [
                            'width' => '60px',
                        ]);
                    } else {
                        echo \yii\helpers\Html::tag('div', '', ['class' => 'fa fa-user', 'style' => ['font-size' => '60px']]);
                    }
                    ?></div>
                <div class="avatar-text"><?php echo Yii::$app->user->firstname; ?><br><span class="avatar-text-small"><?php echo Yii::t('userBar', 'TEXT_UID'); ?>: <?php echo Yii::$app->user->id; ?></span></div>
            </div>

            <a href="<?php echo \yii\helpers\Url::to(['profile/index']); ?>"><?php echo Yii::t('userBar', 'TEXT_MANAGE_PROFILE'); ?></a>
            <a href="<?php echo Yii::$app->params["OG_URL"]; ?>/account/store-credit/index"><?php echo Yii::t('userBar', 'TEXT_STORE_CREDIT'); ?></a>
            <a href="<?php echo \yii\helpers\Url::to(['op/index']); ?>"><?php echo Yii::t('userBar', 'TEXT_WOR'); ?>: <span id="user-menu-wor-ico" class="wor-icon wor-sml" style="display: none;"></span><span id="user-menu-wor"></span><i id="user-menu-wor-load" class="fa fa-cog fa-spin color-black"></i></a>

            <div class="clearfix">
                <div class="input-row"></div>
                <div class="btn-spot"><a class="btn btn-logout" href="<?php echo \yii\helpers\Url::to(['sso/logout']); ?>"><?php echo Yii::t('userBar', 'TEXT_LOGOUT'); ?> <i class="fa fa-sign-out"></i></a></div>
            </div>
        </div>
    </div>

    <!-- REGION DROPDOWN -->
    <div class="headsection-dropdown collapse-box auto-collapse" data-group='header' id="dropdown-region">
        <div class="headsection-dropdown-content">
            <?php echo \yii\helpers\Html::beginForm(['user-bar/regional']); ?>
            <?php echo yii\helpers\Html::hiddenInput("returnUrl", Yii::$app->controller->id . "/" . Yii::$app->controller->action->id); ?>
            <?php echo \conquer\select2\Select2Widget::widget([
                'name' => 'reg_lang',
                'options' => ['id' => 'reg_lang'],
                'bootstrap' => false,
                'settings' => ['minimumResultsForSearch' => 99999],
            ]); ?>
            <div class="clearfix">
                <div class="input-row"></div>
                <div class="btn-spot"><button type="submit" class="btn-submit"><?php echo Yii::t('userBar', 'TEXT_SAVE_CHANGES'); ?></button></div>
            </div>
            <?php echo \yii\helpers\Html::endForm(); ?>
        </div>
    </div>

</div>