<?php

use frontend\assets\AppAsset;
use frontend\components\GoogleTagManager;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $content string */

AppAsset::register($this);
$publish_url = AppAsset::getUrl();

$body_class = isset($body_class) ? $body_class : '';
$show_header = isset($show_header) ? $show_header : 'header';

echo frontend\widgets\GoogleRecaptchaV3::widget();
?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript">
        var HOME_URL = '<?php echo Url::to('/', true); ?>';
        var BASE_URL = '<?php echo Yii::$app->request->baseUrl; ?>';
        var STATIC_DOMAIN = '<?php echo $publish_url; ?>';
        var REGION_STATIC_DOMAIN = "<?php echo Yii::$app->params['REGION_STATIC_DOMAIN']; ?>";
        var CSRF_TOKEN_NAME = '<?php echo Yii::$app->request->csrfParam; ?>';
        var CSRF_TOKEN = '<?php echo Yii::$app->request->csrfToken; ?>';
    </script>
    <?= Html::csrfMetaTags() ?>
    <title><?= Html::encode(Yii::$app->name) ?></title>
    <?php $this->head() ?>
    <?php (new GoogleTagManager())->webSDK(); ?>
</head>
<body class="<?php echo $body_class; ?>">
<?php $this->beginBody() ?>
<?php (new GoogleTagManager())->bodySDK(); ?>
<?php
if ($show_header) {
    $this->beginContent(__DIR__ . '/' . $show_header . '.php');
    $this->endContent();
}
?>
<?php echo $content; ?>
<?php $this->beginContent(__DIR__ . '/footer.php'); ?>
<?php $this->endContent(); ?>
<?php
//Setup toastr
$js = <<<JS
    toastr.options = {
        "closeButton": true,
        "newestOnTop": true,
        "positionClass": "toast-bottom-right",
        "timeOut": "-1",
        "extendedTimeOut": "-1",
        "showMethod": "slideDown",
        "hideMethod": "slideUp",
        "hideDuration": 200,
    };
JS;
$this->registerJs($js, View::POS_READY);

$flash = Yii::$app->session->getAllFlashes();
if ($flash) {
    foreach ($flash as $_key => $_message) {
        if (strpos('danger', $_key) !== false) {
            $type = 'error';
        } else {
            if (strpos('success', $_key) !== false) {
                $type = 'success';
            } else {
                if (in_array($_key, ['error', 'info', 'warning', 'success'])) {
                    $type = $_key;
                } else {
                    $type = 'info';
                }
            }
        }
        if (!is_array($_message)) {
            $_message = [$_message];
        }
        $js = '';
        foreach ($_message as $message) {
            $message = json_encode($message);
            $js = $js . <<<JS
    toastr["$type"]($message);
JS;
        }
        $js = <<<JS
            setTimeout(function() {
                $js
            }, 200);
JS;
        $this->registerJs($js, View::POS_READY);
    }
}
?>
<div class="custom-modal modal" role="form"></div>
<?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>
