<?php
/* @var $this \yii\web\View */
?>
<!-- SIDE MENU RESPONSIVE -->
<div id="mySidenav" class="sidenav-res">
    <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>

    <div class="avatar-profile">
        <div class="avatar">
            <?php
            if (isset(Yii::$app->user->sns) && isset(Yii::$app->user->sns['photo'])) {
                echo \yii\helpers\Html::img(Yii::$app->user->sns['photo'], [
                    'width' => '60px',
                ]);
            } else {
                echo \yii\helpers\Html::tag('div', '', ['class' => 'fa fa-user', 'style' => ['font-size' => '60px']]);
            }
            ?></div>
        <div class="avatar-text"><?php echo Yii::$app->user->firstname; ?><br><span class="avatar-text-small"><?php echo Yii::t('userBar', 'TEXT_UID'); ?>: <?php echo Yii::$app->user->id; ?></span></div>
    </div>

    <a href="<?php echo Yii::$app->params["OG_URL"]; ?>/account/overview" id="LINK_OVERVIEW"><?php echo Yii::t('menu', 'LINK_OVERVIEW'); ?></a>
    
    <div class="dropdown-btn"><?php echo Yii::t('menu', 'LINK_GROUP_PROFILE'); ?><i class="fa fa-angle-down"></i><i class="fa fa-angle-up"></i></div>
    <div class="dropdown-container">
        <a href="<?php echo \yii\helpers\Url::to(['profile/index']); ?>" id="LINK_PROFILE"><?php echo Yii::t('menu', 'LINK_PROFILE'); ?></a>
        <a href="<?php echo \yii\helpers\Url::to(['profile/security']); ?>" id="LINK_SECURITY"><?php echo Yii::t('menu', 'LINK_SECURITY'); ?></a>
        <a href="<?php echo \yii\helpers\Url::to(['social/connect']); ?>" id="LINK_SOCIAL"><?php echo Yii::t('menu', 'LINK_SOCIAL'); ?></a>
    </div>
    <a href="<?php echo Yii::$app->params["OG_URL"]; ?>/account/purchase" id="LINK_OVERVIEW">View Orders</a>
    <a href="<?php echo Yii::$app->params["OG_URL"]; ?>/account/store-credit/index" id="LINK_GROUP_SC"><?php echo Yii::t('menu', 'LINK_GROUP_SC'); ?></a>
    
    <div class="dropdown-btn"><?php echo Yii::t('menu', 'LINK_GROUP_WOR'); ?><i class="fa fa-angle-down"></i><i class="fa fa-angle-up"></i></div>
    <div class="dropdown-container">
        <a href="<?php echo \yii\helpers\Url::to(['op/index']); ?>" id="LINK_WOR"><?php echo Yii::t('menu', 'LINK_WOR'); ?></a>
        <a href="<?php echo \yii\helpers\Url::to(['op/history']); ?>" id="LINK_WOR_HISTORY"><?php echo Yii::t('menu', 'LINK_WOR_HISTORY'); ?></a>
        <a href="<?php echo \yii\helpers\Url::to(['op/statement']); ?>" id="LINK_WOR_STATEMENT"><?php echo Yii::t('menu', 'LINK_WOR_STATEMENT'); ?></a>
    </div>
</div>
<?php if (isset($this->context->menu) && $this->context->menu != "") {
    $menu = $this->context->menu;
    $js = <<<JS
        if (jQuery('#$menu').length > 0) {
            activateDropdownBtn(jQuery('#$menu'));
        }
JS;
    $this->registerJs($js, \yii\web\View::POS_READY);
}