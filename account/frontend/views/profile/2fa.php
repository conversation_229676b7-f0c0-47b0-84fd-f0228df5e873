<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);

$publish_url = \frontend\assets\AppAsset::getUrl();

$css = <<<CSS
.tfa-token-input {
    margin-left: 215px;
}
.input-row-token {
    width: 90%;
}
@media screen and (max-width : 530px ){
    .tfa-token-input {
        margin-left: 0px;
    }
    .tfa-token-input:before {
        content: '';
        display: block;
        clear: both;
    }
}
CSS;
$this->registerCss($css);
?>

<div class="page-title">
    <h1><?php echo Yii::t('profile', 'TITLE_CONFIGURE_TWO_FACTOR'); ?></h1>
</div>

<div class="page-description">
    <p>
        <?php
        echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_DESC', [
            'APP_GOOGLE_AUTH' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_GOOGLE_AUTH'), 'https://m.google.com/authenticator', ['target' => '_blank']),
            'APP_DUO_MOBILE' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_DUO_MOBILE'), 'http://guide.duosecurity.com/', ['target' => '_blank']),
            'APP_AUTHY' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_AUTHY'), 'https://www.authy.com/', ['target' => '_blank']),
            'APP_WINDOWS_PHONE_AUTH' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_WINDOWS_PHONE_AUTH'), 'https://www.windowsphone.com/en-us/store/app/authenticator/e7994dbc-2336-4950-91ba-ca22d653759b', ['target' => '_blank']),
            'SYS_LEARN_MORE_LINK' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_DESC_LEARN_MORE') . ' ' . \frontend\helpers\Html::tag('i', '', ['class' => 'fa fa-external-link-square']), ['site/page', 'view' => '2fa'], ['target' => '_blank']),
        ]);
        ?>
    </p>
</div>

<div class="card">
    <div class="card-description">
        <div class="card-title-image"><img src="<?php echo $publish_url . 'images/2fa-app.png'; ?>" width="195px"></div>

        <h2><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_1'); ?></h2>
        <p>
            <?php
            echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_1_DESC', [
                'APP_GOOGLE_AUTH' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_GOOGLE_AUTH'), 'https://m.google.com/authenticator', ['target' => '_blank']),
                'APP_DUO_MOBILE' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_DUO_MOBILE'), 'http://guide.duosecurity.com/', ['target' => '_blank']),
                'APP_AUTHY' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_AUTHY'), 'https://www.authy.com/', ['target' => '_blank']),
                'APP_WINDOWS_PHONE_AUTH' => \frontend\helpers\Html::a(Yii::t('profile', 'MSG_APP_WINDOWS_PHONE_AUTH'), 'https://www.windowsphone.com/en-us/store/app/authenticator/e7994dbc-2336-4950-91ba-ca22d653759b', ['target' => '_blank']),
            ]);
            ?>
        </p>
    </div>

    <!-- Partition -->
    <div class="partition"></div>

    <div class="card-description">
        <div class="card-title-image">
            <?php echo \yii\helpers\Html::img($qrcode, ['class' => 'img-thumbnail', 'width' => '195px', 'height' => '195px']); ?>
        </div>

        <h2><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_2'); ?></h2>
        <p><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_2_DESC'); ?></p>
        <p><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_2_DESC_2', [
            'SYS_CODE' => \yii\helpers\Html::tag('span', $plaincode, ['style' => 'white-space: pre; text-decoration: underline;']),
        ]); ?></p>
    </div>

    <!-- Partition -->
    <div class="partition"></div>

    <div class="card-description">
        <div class="card-title-image"><img src="<?php echo $publish_url . 'images/2fa-pin.png'; ?>" width="195px"></div>
        <h2><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_3'); ?></h2>
        <p><?php echo Yii::t('profile', 'MSG_CONFIGURE_TWO_FACTOR_STEP_3_DESC'); ?></p>

        <?php
        $form = \frontend\widgets\ActiveForm::begin([
                    'options' => ['autocomplete' => 'off'],
        ]);
        ?>
        <div class="tfa-token-input">
            <?php echo \frontend\helpers\Html::tokenInput('code', '', ['maxlength' => 6]); ?>
        
            <div class="btn-spot-special">
                <button class="btn-submit" type="submit"><?php echo Yii::t('profile', 'BTN_ACTIVATE'); ?></button>
            </div>
        </div>
        <?php
        \frontend\widgets\ActiveForm::end();
        ?>
    </div>
</div>

