<?php
/* @var $this \yii\web\View */
?>

<?php
$form = \frontend\widgets\ActiveForm::begin([
    'action' => ['profile/security', '#' => 'change-2fa'],
    'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
]);
echo \frontend\helpers\Html::hiddenInput('type', 'change-2fa');
?>
<input type='password' style='display:none;'>
<?php
if ($is_enabled) {
?>

<p>
    <?php echo Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_DISABLE'); ?>
</p>

<div class="card-security-token">
    <div class="card-security-title">
        <h2><?php echo Yii::t('profile', 'ENTRY_2FA_CODE'); ?></h2>
    </div>
    
    <div class='clearfix'></div>
    <?php
    echo $form->field($model, 'password')->tokenInput();
    ?>
    
    <div class="btn-spot-special">
        <button type="submit" class="btn-logout"><?php echo Yii::t('profile', 'BTN_DEACTIVATE') ?></button>
    </div>
</div>

<?php
} else {
?>
<p>
    <?php echo Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_ENABLE', [
        'SYS_LINK_LEARN' =>\frontend\helpers\Html::a(Yii::t('profile', 'LINK_SETUP_TWO_FACTOR_AUTH') . ' ' . \frontend\helpers\Html::tag('i', '', ['class' => 'fa fa-external-link-square']), ['site/page', 'view' => '2fa'], ['target' => '_blank', 'style' => 'white-space: pre;'])
    ]); ?>
</p>

<label><?php echo Yii::t('profile', 'ENTRY_SETUP_2FA_CONFIRM_PASSWORD'); ?></label>
<?php
echo $form->field($model, 'password')->passwordInput();
?>
<div class="input-row-text-right">
    <?php echo \yii\helpers\Html::a(Yii::t('profile', 'LINK_FORGET_PASSWORD'), ['sso/forget']); ?>
</div>
<div class="input-row"></div>

<div class="text-center">
    <button type="submit" class="btn-submit"><?php echo Yii::t('general', 'BTN_NEXT'); ?></button>
</div>
<?php
}
?>
<?php
frontend\widgets\ActiveForm::end();