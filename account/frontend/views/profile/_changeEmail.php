<?php
/* @var $this \yii\web\View */

?>
<?php
echo \yii\helpers\Html::tag('p', Yii::t('profile', 'MSG_USER_CHANGE_EMAIL_LIMIT'));

if (!$change_allowed) {
    echo \yii\helpers\Html::tag('p', Yii::t('profile', 'MSG_SELLER_CHANGE_EMAIL_EXCEED_LIMIT'));
} else {
    $form = \frontend\widgets\ActiveForm::begin([
        'id' => 'change-email-form',
        'action' => ['profile/security', '#' => 'change-email'],
        'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
    ]);
    echo \frontend\helpers\Html::hiddenInput('type', 'change-email');
?>
    <input type='password' style='display:none;'>
    
    <label><?php echo Yii::t('profile', 'ENTRY_CURRENT_PASSWORD'); ?></label>
    <?php
    echo $form->field($model, 'customers_password')->passwordInput();
    ?>
    <div class="input-row-text-right">
        <?php echo \yii\helpers\Html::a(Yii::t('profile', 'LINK_FORGET_PASSWORD'), ['sso/forget']); ?>
    </div>
    <div class="input-row"></div>
    <label><?php echo Yii::t('profile', 'ENTRY_NEW_EMAIL'); ?></label>
    <?php
    echo $form->field($model, 'customers_email_address')->textInput();
    ?>
    <label><?php echo Yii::t('profile', 'ENTRY_CONFIRM_EMAIL'); ?></label>
    <?php
    echo $form->field($model, 'confirm_email')->textInput();
    ?>

    <?php if ($change_allowed) { ?>
        <div class="input-row"></div>
        <div class="text-center">
            <button type="submit" class="btn-submit"><?php echo Yii::t('profile', 'BTN_SAVE'); ?></button>
        </div>
    <?php } ?>
<?php
    frontend\widgets\ActiveForm::end();
}