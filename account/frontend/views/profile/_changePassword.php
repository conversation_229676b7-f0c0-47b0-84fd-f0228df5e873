<?php
/* @var $this \yii\web\View */
?>
<?php
$form = \frontend\widgets\ActiveForm::begin([
    'action' => ['profile/security', '#' => 'change-password'],
    'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
]);
echo \frontend\helpers\Html::hiddenInput('type', 'change-password');
?>
<input type='password' style='display:none;'>

<label><?php echo Yii::t('profile', 'ENTRY_CURRENT_PASSWORD'); ?></label>
<?php
echo $form->field($model, 'old_password')->passwordInput();
?>
<div class="input-row-text-right">
    <?php echo \yii\helpers\Html::a(Yii::t('profile', 'LINK_FORGET_PASSWORD'), ['sso/forget']); ?>
</div>
<div class="input-row"></div>

<label><?php echo Yii::t('profile', 'ENTRY_NEW_PASSWORD'); ?></label>
<?php
echo $form->field($model, 'customers_password')->passwordInput();
?>

<label><?php echo Yii::t('profile', 'ENTRY_CONFIRM_PASSWORD'); ?></label>
<?php
echo $form->field($model, 'confirm_password')->passwordInput();
?>

<div class="input-row"></div>
<div class="text-center">
    <button type="submit" class="btn-submit"><?php echo Yii::t('profile', 'BTN_SAVE'); ?></button>
</div>
<?php
\frontend\widgets\ActiveForm::end();
