<?php

use common\components\GeneralComponent;
use yii\helpers\Html;
/* @var $this \yii\web\View */
/* @var $model \frontend\models\UpdatePhoneForm */

\frontend\assets\FlagsAsset::register($this);

// 3rd party portal URL query
$req_uri = \common\components\GeneralComponent::getRequestURI();
$f_query = (empty($req_uri) ? "" : "?") . $req_uri;
//hide and show card
$option = ['id' => 'last_four_digit'];
$option2 = ['id' => 'change_phone','style'=>['display' => 'none']];
$option3 = ['id' => 'two_fa'];
if ($model2->last_four_passed == true) {
    Html::addCssStyle($option, ['display' => 'none'], true);
    Html::addCssStyle($option2, ['display' => 'block']);
    Html::addCssStyle($option3, ['display' => 'none'], true);
}

$image_selected_text =  Yii::t('verificationSubmission', 'IMAGE_SELECTED');
$js = <<<JS
        $("input[name^=doc_002]").change(function() {
            if ($("input[name^=doc_002]").val() != '') {
                $(this).parent().hide();
                $(this).parent().after('<p class="upload_status"><b>$image_selected_text</b></p>');
            } else {
                $(this).parent().show();
                $(this).parent().siblings(".upload_status").remove();
            }
        });
JS;
$this->registerJs($js);

$no_token_msg = json_encode(\Yii::t('smsToken', 'ERROR_PLEASE_REQUEST_TOKEN'));
$js = <<<JS
    jQuery('#change-phone-form').on('beforeSubmit', function(event) {
        if (jQuery('#change-phone-form .card-security-token').length && jQuery('#change-phone-form .card-security-token').data('security_question').isTokenAnswerEmpty()) {
            toastr['error']($no_token_msg);
            return false;
        }
        return true;
    });
JS;
$this->registerJs($js, \yii\web\View::POS_END);
?>
<script type='text/javascript'>
    var VALIDATE_LAST_FOUR_DIGIT = '<?php echo \yii\helpers\Url::to(['profile/validate-four-digit'], true) ?>';
    var VALIDATE_CHANGE_NUMBER_TWOFA = '<?php echo \yii\helpers\Url::to(['profile/change-phone-twofa'], true) ?>';
    var CHECK_CHANGE_MOBILE_COUNTRY = '<?php echo \yii\helpers\Url::to(['profile/check-change-mobile-country'], true) ?>';
    var CHANGE_MOBILE_COUNTRY = '<?php echo \yii\helpers\Url::to(['profile/change-mobile-country'], true) . $f_query; ?>';
    var ERROR_PLEASE_AGREE = '<?php echo \Yii::t("profile", "ERROR_PLEASE_AGREE"); ?>';
    var ERROR_PLEASE_UPLOAD_POA = '<?php echo \Yii::t("profile", "ERROR_PLEASE_UPLOAD_POA"); ?>';
    var MSG_FROM_ERROR = '<?php echo \Yii::t("profile", "MSG_KYC_CHANGE_COUNTRY_ERROR"); ?>';
    var ERROR_PLEASE_REQUEST_TOKEN = '<?php echo \Yii::t('smsToken', 'ERROR_PLEASE_REQUEST_TOKEN'); ?>';

    function imageExtensionValidate(i) {
        var validFileExtensions = [".jpg", ".jpeg", ".gif", ".png", ".pdf"];
        // var fileInput = document.getElementsById("file-input");
        var fileVal = i.value;
        // alert(i.value);
        if (fileVal.length > 0) {
            var blnValid = false;
            for (var j = 0; j < validFileExtensions.length; j++) {
                var sCurExtension = validFileExtensions[j];
                if (fileVal.substr(fileVal.length - sCurExtension.length, sCurExtension.length).toLowerCase() == sCurExtension.toLowerCase()) {
                    blnValid = true;
                    break;
                }
            }
            if (!blnValid) {
                alert("Sorry, the file you selected is not an allowed file type, allowed file type are: " + validFileExtensions.join(", "));
                oInput.value = "";
                return false;
            }
        }
    }
</script>
<?php
if ($has_pending_customer_approval) { ?>
    <div class="pending_approval-content">
        <?php echo Yii::t('profile', 'DETAILS_PHONE_CHANGE_SUBMITED', ['EMAIL_ADDRESS' => $email_address]);?>
    </div>
<?php } else if(!$model3->twofa_enabled) {
    $form = \frontend\widgets\ActiveForm::begin([
        'action' => \yii\helpers\Url::to(['profile/security'], true) . $f_query . '#confirm-four-digit',
        'options' => ['class' => 'form-normal'],
    ]);
    ?>
        <?php echo Html::beginTag('div', $option); ?>
            <div class="form-group">
                <div class="text-center">
                    <?php echo \Yii::t('profile', 'TEXT_CONFIRM_LAST_4_NUM', ['phone' => \yii\helpers\Html::tag('b', $cust_phone)]); ?>
                    <br>
                </div>

                <div class="space-20"></div>
                <?php
                    echo \frontend\helpers\Html::tokenInput(\yii\helpers\Html::getInputName($model, 'last_four_num'), $model->last_four_num, [
                        'length' => 4,
                        'id' => 'last_four_num'
                    ]);
                    ?>
                <div class="clearfix"></div>

            </div>
            <div class="clearfix"></div>
            <div class="input-row"></div>
            <div class="text-center">
                <button type="submit" class="btn-submit btn-next" onclick="return validateFourDigit(jQuery('#last_four_num').val());"><?php echo Yii::t('general', 'BTN_NEXT'); ?></button>
            </div>
        <?php echo Html::endTag('div'); ?>
    <?php \frontend\widgets\ActiveForm::end(); ?>
<?php } else { ?>
    <?php
    $form3 = \frontend\widgets\ActiveForm::begin([
        'action' => \yii\helpers\Url::to(['profile/security'], true) . $f_query . '#confirm-two-fa',
        'options' => ['class' => 'form-normal'],
    ]);
    ?>
        <?php echo Html::beginTag('div', $option3);?>
            <p>
                <?php echo Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_CHECKING'); ?>
            </p>
            <div class="card-security-token">
                <div class="card-security-title">
                    <h2><?php echo Yii::t('profile', 'ENTRY_2FA_CODE'); ?></h2>
                </div>

                <div class='clearfix'></div>
                <?php
                echo $form3->field($model3, 'password')->tokenInput();
                ?>

                <div class="btn-spot-special">
                    <button type="submit" class="btn-submit btn-next" onclick="return changePhoneTwofa(jQuery('#updatetwofaform-password').val());"><?php echo Yii::t('general', 'BTN_NEXT') ?></button>
                </div>
            </div>
        <?php echo Html::endTag('div');?>
    <?php \frontend\widgets\ActiveForm::end(); ?>
<?php } ?>
<?php
// new mobile number
$form2 = \frontend\widgets\ActiveForm::begin([
            'id' => 'change-phone-form',
            'action' => \yii\helpers\Url::to(['profile/security'], true) . $f_query . '#change-phone',
            'options' => ['class' => 'form-normal'],
        ]);
echo \frontend\helpers\Html::hiddenInput('type', 'change-phone');
?>
<?php echo Html::beginTag('div', $option2); ?>
    <input type="hidden" value="<?php $model->last_four_num; ?>" />
    <label><?php echo Yii::t('profile', 'ENTRY_NEW_PHONE_NUMBER'); ?></label>
    <div class="input-row-2-coll">
        <?php
        echo $form2->field($model2, 'customers_country_dialing_code_id')->widget(\conquer\select2\Select2Widget::className(), [
            'bootstrap' => false,
            'items' => \yii\helpers\ArrayHelper::map($allCountry, 'countries_id', function($array) {
                        return "<span class='flag flag-" . strtolower($array['countries_iso_code_2']) . "'></span> +$array[countries_international_dialing_code] <span class='select2-hide-in-selected'>$array[countries_name]</span>";
                    }),
            'settings' => [
                'prompt' => Yii::t('general', 'TEXT_PLEASE_SELECT'),
                'escapeMarkup' => new yii\web\JsExpression('function(text) { return text; }'),
            ],
            'events' => [
                'change' => 'function(e) {
                    return changeMobileCountryFlow.checkChangeMobileCountry("' . $model2->customers_country_dialing_code_id . '", e);
                }',
            ],
        ]);
        ?>
    </div>
    <div class="input-row-2-coll">
        <div class="input-row-2-coll-right">
            <?php
            echo $form2->field($model2, 'customers_telephone')->textInput([
                'placeholder' => Yii::t('sso', 'TEXT_MOBILE_NUMBER'),
                'autocomplete' => 'off',
                'onkeypress' => 'return onlyNumbers(event)',
            ]);
            ?>
        </div>
    </div>
    <?php
    echo $this->render('/sms-token/securityQuestionPopupHtml', [
        'requestType' => 'verify_phone_request',
        'tokenMsg' => $model2->getFirstError('token_answer'),
        'token' => $model2->token_answer,
        'contact_no_dialing_code_id_field' => 'updatephoneform-customers_country_dialing_code_id',
        'contact_no_field' => 'updatephoneform-customers_telephone',
    ]);
    ?>
    <div class="clearfix"></div>
    <div class="input-row"></div>
    <div class="text-center">
        <button type="submit" class="btn-submit"><?php echo Yii::t('profile', 'BTN_SAVE'); ?></button>
    </div>
<?php echo Html::endTag('div'); ?>
<?php
\frontend\widgets\ActiveForm::end();?>
<?php //popup display message?>
<div class="mobile-change-modal-main">
    <!--modals-->
    <div id="dialog-mobile-change" class="modalDialog-mobileNumChange">
        <div id="dialog-mobile-change-loading">
            <i class="fa fa-spin fa-refresh fa-2x fa-fw"></i>
        </div>
        <div id="dialog-mobile-change-content" class="dynamic-mobile-change-content">
        <div>
        <h2 class="content-mobileChange"><?php echo Yii::t('profile', 'TITLE_CHANGE_PHONE_COUNTRY'); ?></h2>
            <p class="content-mobileChange"> <?php echo Yii::t('profile', 'DETAILS_CHANGE_PHONE_COUNTRY_QUOTA');?></p>
            <div id="dialog-mobile-change-primary-message">
            </div>
            <label class="custom-checkbox">
            <p class="content-mobileChange"><?php echo Yii::t('profile', 'AGREE_CHANGE_PHONE_COUNTRY'); ?></p>
                <input id="agree_change_phone_country" type="checkbox"  name="agree_change_phone_country" style="margin-top: 2px;">
                <span class="checkmark"></span>
            </label>

            <div class="section_bellow_mobileChange">
                <div class="sumbit_section_mobileChange-r">
                    <a title="Close" class="close-mobileNumChange btn-expand-collapse btn-default" onclick="enableScroll();jQuery(this).parents('.modalDialog-mobileNumChange').removeClass('modalDialog-mobileNumChange-show');"><?php echo Yii::t('profile', 'BTN_CANCEL'); ?></a>
                </div>
                <div class="sumbit_section_mobileChange-l">
                    <button id="continue"type="submit" class="btn-submit" onclick="return changeMobileCountryFlow.confirmChangeCountry();"
                    ><?php echo Yii::t('profile', 'BTN_CONTINUE'); ?></button>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>

<div class="mobile-change-modal-main">
    <!--modals-->
    <?php
    $form4 = \frontend\widgets\ActiveForm::begin([
        'action' => \yii\helpers\Url::to(['profile/validate-country-dialing-code'], true) . $f_query . '#change-country',
        'options' => ['class' => 'form-normal'],
        'id' => 'frm-change-country-billing'
    ]);
    ?>
    <div id="dialog-billing-address-form" class="modalDialog-mobileNumChange">
        <div class = "dynamic-input-otp-form">
        <div class="form-normal content-mobileChange country-change-item-form">
            <h2><?php echo Yii::t('profile', 'TITLE_CHANGE_PHONE_COUNTRY'); ?></h2>
            <p class="content-mobileChange">
                <?php echo  Yii::t('profile', 'DETAILS_CHANGE_PHONE_COUNTRY7'); ?><br/>
            </p>
        </div>

            <div class="form-normal content-mobileChange country-change-content-form">
                <?php
                echo $form4->field($address, 'country_id')->hiddenInput()->label(false);
                ?>
                <label><?php echo Yii::t('profile', 'ENTRY_COUNTRY'); ?></label>
                <div class="form-group">
                    <input type="text" id="billing_address_country_name" disabled>
                </div>

                <label><?php echo Yii::t('profile', 'ENTRY_ADDRESS'); ?></label>
                <?php
                echo $form4->field($address, 'street_address')->textInput([
                    'placeholder' => Yii::t('profile', 'PLACEHOLDER_ADDRESS1'),
                ]);
                echo $form4->field($address, 'suburb')->textInput([
                    'placeholder' => Yii::t('profile', 'PLACEHOLDER_ADDRESS2'),
                ]);
                ?>
                <label><?php echo Yii::t('profile', 'ENTRY_CITY'); ?></label>
                <?php
                echo $form4->field($address, 'city')->textInput([
                    'placeholder' => Yii::t('profile', 'ENTRY_CITY'),
                ]);
                ?>
                <div class="clearfix"></div>
                <div class="input-row-2-coll">
                    <label><?php echo Yii::t('profile', 'ENTRY_STATE') . '/' . Yii::t('profile', 'ENTRY_ZIP'); ?></label>
                    <?php
                    echo $form4->field($address, 'zone_id', ['options' => ['class' => 'form-group hide']])->widget(\conquer\select2\Select2Widget::className(), [
                        'placeholder' => Yii::t('profile', 'COMBOBOX_STATE'),
                        'bootstrap' => false,
                    ]);
                    echo $form4->field($address, 'state')->textInput([
                        'placeholder' => Yii::t('profile', 'ENTRY_STATE'),
                        'class' => 'show',
                    ]);
                    ?>
                </div>
                <div class="input-row-2-coll">
                        <?php
                        echo $form4->field($address, 'postcode')->textInput([
                            'placeholder' => Yii::t('profile', 'ENTRY_ZIP'),
                        ]);
                        ?>
                </div>
                <div class="clearfix">
                    <h2><?php echo Yii::t('profile', 'TITLE_UPLOAD_PROOF'); ?></h2>
                    <p><i><?php echo Yii::t('profile', 'TEXT_FILE_TYPE'); ?> <?php echo Yii::t('profile', 'TEXT_FILE_SIZE',["FILE_MAXSIZE" => floor(Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"]/1024)]); ?></i></p>

                    <div class="input-row-2-coll">
                        <label class="btn btn-default custom-fileinput country-change-select-file">
                        <?php echo \frontend\helpers\Html::fileInput("doc_002", null, [
                            "style" => "display:none",
                            "accept" => "image/jpeg,image/gif,image/png,application/pdf,image/x-eps",
                            "id" => "fileinput",
                            "onchange" => "imageExtensionValidate(this)"
                            ]); ?>
                        <?php echo Yii::t('profile', 'SELECT_FILE') ?></label>
                    </div>
                    <div class="clearfix"></div>
                    <div class="section_bellow_mobileChange">
                            <div class="sumbit_section_mobileChange-r">
                                <a title="Close" class="close-mobileNumChange btn-expand-collapse btn-default" onclick="enableScroll();jQuery(this).parents('.modalDialog-mobileNumChange').removeClass('modalDialog-mobileNumChange-show');"><?php echo Yii::t('profile', 'BTN_CANCEL'); ?></a>
                            </div>
                            <div class="sumbit_section_mobileChange-l">
                                <button id="submitBilling" type="submit" class="btn-submit" onclick="return changeMobileCountryFlow.confirmAddressAndPoa();"><?php echo Yii::t('profile', 'BTN_CONTINUE'); ?></button>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php \frontend\widgets\ActiveForm::end();?>
</div>

<div class="mobile-change-modal-main">
    <!--modals-->
    <?php
        $form5 = \frontend\widgets\ActiveForm::begin([
            'action' => \yii\helpers\Url::to(['profile/security'], true) . $f_query . '#new-phone-number',
            'options' => ['class' => 'form-normal'],
            'id' => 'frm-change-country-phone'
        ]);
    ?>
    <div id="dialog-phone-otp-form" class="modalDialog-mobileNumChange">
        <div class="form-normal content-mobileChange dynamic-mobile-otp-content">
            <h2><?php echo Yii::t('profile', 'TITLE_CHANGE_PHONE_COUNTRY'); ?></h2>
            <?php
            echo $form5->field($address, 'dialing_code_id')->hiddenInput()->label(false);
            ?>
            <div class="input-row-2-coll-right">
                <label><?php echo Yii::t('profile', 'ENTRY_COUNTRY'); ?></label>
                <div class="form-group">
                    <input type="text" id="mobile_number_country_dialing_code" disabled>
                </div>
                <label><?php echo Yii::t('profile', 'ENTRY_NEW_PHONE_NUMBER'); ?></label>
                <?php
                echo $form5->field($address, 'telephone')->textInput([
                    'placeholder' => Yii::t('sso', 'TEXT_MOBILE_NUMBER'),
                    'autocomplete' => 'off',
                    'onkeypress' => 'return onlyNumbers(event)',
                    'pattern' => "\d*"
                ]);
                ?>
            </div>
            <?php
            echo $this->render('/sms-token/securityQuestionPopupHtml', [
                'requestType' => 'verify_phone_request',
                'tokenMsg' => '',
                'token' => '',
                'contact_no_dialing_code_id_field' => 'customersmobilechangeapprovalform-dialing_code_id',
                'contact_no_field' => 'customersmobilechangeapprovalform-telephone',
            ]);
            ?>
            <div class="section_bellow_mobileChange">
                <div class="sumbit_section_mobileChange-r">
                    <a title="Close" class="close-mobileNumChange btn-expand-collapse btn-default" onclick="enableScroll();jQuery(this).parents('.modalDialog-mobileNumChange').removeClass('modalDialog-mobileNumChange-show');"><?php echo Yii::t('profile', 'BTN_CANCEL'); ?></a>
                </div>
                <div class="sumbit_section_mobileChange-l">
                    <button id="saveinto" type="submit" class="btn-submit" data-submitting-txt="<?php echo Yii::t('profile', 'BTN_SUBMITTING'); ?>" data-submit-txt="<?php echo Yii::t('profile', 'BTN_SUBMIT'); ?>" onclick ="return changeMobileCountryFlow.confirmOtp()"><?php echo Yii::t('profile', 'BTN_SUBMIT'); ?></button>
                </div>
            </div>
        </div>
    </div>
    <?php \frontend\widgets\ActiveForm::end();?>
</div>
<div class="mobile-change-modal-main">
    <div id="dialog-change-submitted-successfully" class="modalDialog-mobileNumChange">
        <div class="dynamic-submitted-form">
            <h2 class="content-mobileChange"><?php echo Yii::t('profile', 'TITLE_PHONE_COUNTRY_CHANGE_SUBMITED'); ?></h2>
            <p id="approval_pending" class="content-mobileChange">
                <?php echo Yii::t('profile', 'DETAILS_PHONE_CHANGE_SUBMITED', ['EMAIL_ADDRESS' => $email_address]); ?>
            </p>
            <p class="content-mobileChange" id="change_successful" class="content-mobileChange">
                <?php echo Yii::t('profile', 'MSG_MOBILE_PHONE_CHANGE_SUCCESS'); ?>
            </p>
            <div class="section_bellow_mobileChange">
                <div style= "padding : 10px;">
                    <a title="Close" class="close-mobileNumChange btn-expand-collapse btn-submit" onclick="changeMobileCountryFlow.endFlow(this);"><?php echo Yii::t('profile', 'BTN_OK'); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>