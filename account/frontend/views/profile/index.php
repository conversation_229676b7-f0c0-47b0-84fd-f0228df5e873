<?php

use api\controllers\ProfileController;
use common\components\SSOCom;
use common\models\CustomerInfoLock;
use frontend\assets\FormAsset;
use frontend\assets\ProfileAsset;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use frontend\widgets\ActiveForm;

/* @var $this \yii\web\View */

FormAsset::register($this);
ProfileAsset::register($this);

$country_id_opt = $modelAddressBook->entry_country_id ? $modelAddressBook->entry_country_id : "null";
$zone_id_opt = $modelAddressBook->entry_zone_id ? $modelAddressBook->entry_zone_id : "''";

$js = <<<JS
    var ctry_id = ["$modelAddressBook->entry_country_id"];
    var ctry_field = ["addressform-entry_country_id"];
    var ctry_opt = ["name"];

    ctryOpt(ctry_id, ctry_field, ctry_opt);
    stateOpt($country_id_opt, $zone_id_opt, "addressform-entry_zone_id", "addressform-entry_state");

    jQuery('#addressform-entry_country_id').change(function() {
        stateOpt(jQuery('#addressform-entry_country_id').val(), "", "addressform-entry_zone_id", "addressform-entry_state");
    });

    $(document).ready(function() {
        const toggleButtons = $('.einvoice-toggle-button');
        const typeInput = $('#einvoice-type-input');
        const slider = $('.einvoice-toggle-slider');
        const businessSection = $('#business-section');
        const billingAddressTitle = $('#billing-address-title');

        // Initialize based on current value
        const currentType = typeInput.val();
        if (currentType === 'business') {
            businessSection.show();
            billingAddressTitle.text(billingAddressTitle.data('company-text'));
            slider.css('transform', 'translateX(100%)');
            
            // Make business fields required when business type is selected
            $('[data-required-when="business"]').prop('required', true);
        } else {
            // Remove required from business fields when personal type is selected
            $('[data-required-when="business"]').prop('required', false);
        }

        toggleButtons.on('click', function() {
            const type = this.id.replace('toggle-', '');
            typeInput.val(type);

            toggleButtons.removeClass('active');
            $(this).addClass('active');

            if (slider.length) {
                slider.css('transform', type === 'personal' ? 'translateX(0)' : 'translateX(100%)');
            }

            if (type === 'business') {
                businessSection.show();
                billingAddressTitle.text(billingAddressTitle.data('company-text'));
                
                // Make business fields required when business type is selected
                $('[data-required-when="business"]').prop('required', true);
            } else {
                businessSection.hide();
                billingAddressTitle.text(billingAddressTitle.data('billing-text'));
                
                // Remove required from business fields when personal type is selected
                $('[data-required-when="business"]').prop('required', false);
            }
        });

        const nidFields = $('.nid-input-field');
        nidFields.on('input', function() {
            $(this).val($(this).val().replace(/\D/g, ''));
            const maxLength = parseInt($(this).attr('maxlength'));
            if ($(this).val().length >= maxLength) {
                const nextField = nidFields.eq(nidFields.index(this) + 1);
                if (nextField.length) nextField.focus();
            }
        });

        nidFields.on('keydown', function(e) {
            if (e.key === 'Backspace' && $(this).val().length === 0) {
                const prevField = nidFields.eq(nidFields.index(this) - 1);
                if (prevField.length) prevField.focus();
            }
        });
    });
JS;
$this->registerJs($js, \yii\web\View::POS_READY);
?>
<script type="text/javascript">
    var ENTRY_IM_ACCOUNT_MAX_ENTRIES = '<?php echo $config["ENTRY_IM_ACCOUNT_MAX_ENTRIES"] ?>';

    function fd_request_delete_account() {
        FreshworksWidget('prefill', 'ticketForm', {
            "email": "<?php echo $modelCustomers->customers_email_address; ?>",
            "name": "<?php echo $modelCustomers->customers_firstname . ' ' . $modelCustomers->customers_lastname; ?>",
            "subject": "Request for Account Deletion",
            "description": "I would like to request for my account deletion.\n\nEmail Address: <?php echo $modelCustomers->customers_email_address; ?>",
            "group_types": "CS Senior Executive",
            "user_id": "<?php echo (string)Yii::$app->user->id; ?>",
            "custom_fields": {
                "cf_cid": "<?php echo (string)Yii::$app->user->id; ?>",
                "cf_offgamers_enquiry": "My Account",
                "cf_product_type": "Others or non-product related",
                "cf_issue296997": "I want to delete my account",
                "cf_issue_type_agent": "Remove account",
                "cf_inquiring_as_agent": "General Inquiries",
                "cf_inquiry_type881587": "User Account",
                "cf_issue_type": "Remove G2G\/OffGamers account"
            },
            "tags": "account deletion"
        });
        FreshworksWidget('disable', 'ticketForm', ['email', 'custom_fields.cf_cid', 'name', 'subject', 'description', 'custom_fields.cf_offgamers_enquiry', 'custom_fields.cf_product_type', 'custom_fields.cf_issue296997', 'custom_fields.cf_issue_type_agent', 'custom_fields.cf_inquiring_as_agent', 'custom_fields.cf_inquiry_type881587', 'custom_fields.cf_issue_type']);
        FreshworksWidget('open', 'ticketForm');
    }
</script>
<style type="text/css">
    @media (max-width: 600px) {
        .og-flex-direction-responsive {
            flex-direction: column;
        }
    }
</style>
<?php
if (\Yii::$app->session->hasFlash('success')) {
    echo $this->render("/sso/cookieAllImage", SSOCom::clientURLSetSSOToken());
}
?>

<div class="page-title">
    <h1><?php echo Yii::t('profile', 'TITLE_PROFILE'); ?></h1>
</div>

<div class="card">
    <?php
    $form = ActiveForm::begin();
    ?>

    <?php if ($isMyBuyer) { ?>
        <?php if (!$hasEnabledEinvoice) { ?>
            <!-- E-Invoice Type -->
            <div class="einvoice-type-wrapper">
                <div>
                    <h2 class="einvoice-type-title"><?php echo Yii::t('profile', 'TITLE_EINVOICE_TYPE'); ?></h2>
                    <div class="input-row-text-full" style="text-align:left"><?php echo Yii::t('profile', 'TEXT_EINVOICE_TYPE_DESC'); ?></div>
                </div>

                <div class="einvoice-type-toggle">
                    <div class="einvoice-toggle-container">
                        <div id="toggle-personal" class="einvoice-toggle-button <?php echo (empty($modelCustomers->einvoice_type) || $modelCustomers->einvoice_type === 'personal') ? 'active' : ''; ?>" value="personal"><?php echo Yii::t('profile', 'EINVOICE_TYPE_PERSONAL'); ?></div>
                        <div id="toggle-business" class="einvoice-toggle-button <?php echo $modelCustomers->einvoice_type === 'business' ? 'active' : ''; ?>" value="business"><?php echo Yii::t('profile', 'EINVOICE_TYPE_BUSINESS'); ?></div>
                        <div class="einvoice-toggle-slider"></div>
                    </div>
                    <?php echo $form->field($modelCustomers, 'einvoice_type')->hiddenInput(['id' => 'einvoice-type-input', 'value' => $modelCustomers->einvoice_type ?: 'personal'])->label(false); ?>
                </div>
            </div>

            <!-- Partition -->
            <div class="partition"></div>
        <?php } ?>

        <!-- Business Section -->
        <div id="business-section" style="display: <?php echo ($modelCustomers->einvoice_type === 'business') ? 'block' : 'none'; ?>;">
            <div class="card-tab">
                <h2><?php echo Yii::t('profile', 'TITLE_COMPANY_DETAILS'); ?></h2>
            </div>
            <div class="form-normal">
                <label><?php echo Yii::t('profile', 'ENTRY_COMPANY_NAME'); ?></label>
                <?php
                echo $form->field($modelCustomers, 'company_name')->textInput([
                    'required' => ($modelCustomers->einvoice_type === 'business'),
                    'data-required-when' => 'business',
                ]);
                ?>

                <label><?php echo Yii::t('profile', 'ENTRY_COMPANY_REGISTRATION_NUMBER'); ?></label>
                <?php
                echo $form->field($modelCustomers, 'company_reg_number')->textInput([
                    'required' => ($modelCustomers->einvoice_type === 'business'),
                    'data-required-when' => 'business',
                ]);
                ?>
                <div class="input-row-text-full">
                    <?php echo Yii::t('profile', 'MSG_ENTRY_COMPANY_REGISTRATION_NUMBER'); ?>
                </div>

                <label><?php echo Yii::t('profile', 'ENTRY_TAX_IDENTIFICATION_NUMBER'); ?></label>
                <div style="display: flex; align-items: flex-start; gap: 10px;">
                    <div class="input-row-2-coll" style="width: 20%;">
                        <?php
                            echo $form->field($modelCustomers, 'tax_country_code')->widget(\conquer\select2\Select2Widget::className(), [
                                'bootstrap' => false,
                                'items' => \yii\helpers\ArrayHelper::map($allCountry, 'countries_iso_code_2', function($array) {
                                    return "$array[countries_iso_code_2] <span class='select2-hide-in-selected'>$array[countries_iso_code_2]</span>";
                                }),
                                'settings' => [
                                    'escapeMarkup' => new yii\web\JsExpression('function(text) { return text; }'),
                                    'disabled' => true,
                                ]
                            ])->label(false);
                        ?>
                    </div>
                    <div class="input-row-2-coll" style="width: 80%;">
                        <?php
                        echo $form->field($modelCustomers, 'tax_reg_number')->textInput([
                            'required' => ($modelCustomers->einvoice_type === 'business'),
                            'data-required-when' => 'business',
                        ])->label(false);
                        ?>
                    </div>
                </div>
                <div class="input-row-text-full">
                    <?php echo Yii::t('profile', 'MSG_ENTRY_TAX_IDENTIFICATION_NUMBER'); ?>
                </div>
            </div>
            <!-- Partition -->
            <div class="partition"></div>
        </div>

    <?php } ?>
    <!-- Personal Info -->
    <div class="card-tab">
        <h2><?php echo Yii::t('profile', 'TITLE_PERSONAL_INFO'); ?></h2>
    </div>
    <div class="form-normal">
        <div id="customer-name">
            <?php if (!isset($cust_verify) || empty($cust_verify->name_lock)) { ?>
                <div class="input-row-2-coll">
                    <label><?php echo Yii::t('profile', 'ENTRY_NAME'); ?></label>
                    <?php
                    echo $form->field($modelCustomers, 'customers_firstname')->textInput([
                        'placeholder' => Yii::t('profile', 'PLACEHOLDER_FIRST_NAME'),
                    ]);
                    ?>
                </div>
                <div class="input-row-2-coll">
                    <div class="input-row-2-coll-right">
                        <?php
                        echo $form->field($modelCustomers, 'customers_lastname')->textInput([
                            'placeholder' => Yii::t('profile', 'PLACEHOLDER_LAST_NAME'),
                        ]);
                        ?>
                    </div>
                </div>
            <?php } else { ?>
                <div class="input-row-2-coll">
                    <label><?php echo Yii::t('profile', 'ENTRY_NAME'); ?></label>

                    <div class="space-10"></div>
                    <b><?php
                        echo $modelCustomers->customers_firstname;
                        ?>
                        <?php
                        echo $modelCustomers->customers_lastname;
                        ?></b>
                </div>
            <?php } ?>
            <?php if ($isMyBuyer) { ?>
                <div class="input-row-text-full">
                    <?php echo Yii::t('profile', 'MSG_ENTRY_FULL_NAME'); ?>
                </div>
            <?php } ?>
        </div>
        <label><?php echo Yii::t('profile', 'ENTRY_GENDER'); ?></label>
        <?php
        echo $form->field($modelCustomers, 'customers_gender')->radioList(['m' => Yii::t('profile', 'RADIO_MALE'), 'f' => Yii::t('profile', 'RADIO_FEMALE')]);
        ?>
        <?php if ((!isset($info_lock) && empty($info_lock)) || ($info_lock->info_key == CustomerInfoLock::DOB_LOCK_KEY) && ($info_lock->locked == false)) { ?>
            <label><?php echo Yii::t('profile', 'ENTRY_DOB'); ?></label>
            <?php
            echo $form->field($modelCustomers, 'customers_dob')->widget(\yii\jui\DatePicker::className(), [
                'dateFormat' => 'yyyy-MM-dd',
                'clientOptions' => [
                    'changeMonth' => true,
                    'changeYear' => true,
                    'yearRange' => (date("Y") - 100) . ":" . date("Y"),
                    'minDate' => "-100y",
                    'maxDate' => 0,
                ],
            ]);
            ?>
        <?php } else { ?>
            <div class="input-row-2-coll">
                <label><?php echo Yii::t('profile', 'ENTRY_DOB'); ?></label>
                <div class="space-10"></div>
                <b><?php
                    echo $modelCustomers->customers_dob;
                    ?>
                </b>
            </div>
        <?php } ?>
        <?php if ($isMyBuyer) { ?>
            <label><?php echo Yii::t('profile', 'ENTRY_NID'); ?></label>
            <?php
            $is_nic_required = false;
            if ($hasEnabledEinvoice && $modelCustomers->einvoice_type == 'personal' && isset($modelCustomers->identity_number) && !empty($modelCustomers->identity_number)) {
                $is_nic_required = true;
            }
            ?>
            <div class="full-width-row" style="width:100%">
                <div class="nid-input-group">
                    <?php echo $form->field($modelCustomers, 'nric_1')->textInput(['maxlength' => 6, 'placeholder' => 'XXXXXX', 'class' => 'nid-input-field', 'required' => $is_nic_required])->label(false) ?>
                    <span class="nid-separator">-</span>
                    <?php echo $form->field($modelCustomers, 'nric_2')->textInput(['maxlength' => 2, 'placeholder' => 'XX', 'class' => 'nid-input-field', 'required' => $is_nic_required])->label(false) ?>
                    <span class="nid-separator">-</span>
                    <?php echo $form->field($modelCustomers, 'nric_3')->textInput(['maxlength' => 4, 'placeholder' => 'XXXX', 'class' => 'nid-input-field', 'required' => $is_nic_required])->label(false) ?>
                </div>
            </div>
            <?php if ($modelCustomers->hasErrors('identity_number')) { ?>
                <div class="input-row-error" style="display:flex">
                    <i class="fa fa-exclamation-circle" style="margin-right: 5px;"></i><span class="help-block"><?php echo Html::error($modelCustomers, 'identity_number'); ?></span>
                </div>
            <?php } ?>
        <?php } ?>

        <label>Instant Messenger</label>
        <?php echo \conquer\select2\Select2Widget::widget([
            'name' => 'imList',
            'options' => [
                'id' => 'imList',
                'prompt' => Yii::t('general', 'COMBOBOX_SELECT'),
            ],
            'bootstrap' => false,
            'items' => $imList,
        ]); ?>
        <div id="im_list_error" class="hide">
            <div class="input-row-error">
                <i class="fa fa-exclamation-circle"></i> <span class="help-block"><?php echo Yii::t('profile', 'MSG_EXCEED_IM_LIMIT', array('SYS_MIN_LENGTH' => $config["ENTRY_IM_ACCOUNT_MAX_ENTRIES"])); ?></span>
            </div>
        </div>
        <div id="im_model_error">
            <?php if (!($error = Html::error($modelCustomers, 'imList'))) { ?>
                <div class="input-row-error">
                    <i class="fa fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php } ?>
        </div>
        <div id="imListContent">
            <?php for ($i = 0, $cnt = count($cur_im); $cnt > $i; $i++) { ?>
                <span id="row_im_<?php echo $i; ?>">
                    <div class="input-row-im">
                        <div class="input-row-im-left">
                            <?php echo Html::textInput("im_" . $cur_im[$i]["tid"] . "_" . $i, $cur_im[$i]["val"], ['class' => 'im-' . $cur_im[$i]["type"], 'placeholder' => $imList[$cur_im[$i]["tid"]]]); ?>
                        </div>
                        <div class="input-row-im-icon">
                            <div class="icon-remove"><a href="#" onclick="return removeImRow(this);"><i class="fa fa-times-circle"></i></a></div>
                        </div>
                    </div>
                </span>
            <?php } ?>
            <span id="row_sample_im" class="hide">
                <div class="input-row-im">
                    <div class="input-row-im-left">
                        <?php echo Html::textInput("sample_im", "", ['id' => 'sample_im']); ?>
                    </div>
                    <div class="input-row-im-icon">
                        <div class="icon-remove"><a href="#" onclick="return removeImRow(this);"><i class="fa fa-times-circle"></i></a></div>
                    </div>
                </div>
            </span>
        </div>
    </div>

    <!-- Partition -->
    <div class="partition"></div>

    <!-- Billing Address -->
    <div class="card-tab">
        <h2 id="billing-address-title"
            data-company-text="<?php echo Yii::t('profile', 'TITLE_COMPANY_ADDRESS'); ?>"
            data-billing-text="<?php echo Yii::t('profile', 'TITLE_BILLING_ADDRESS'); ?>">
            <?php echo Yii::t('profile', 'TITLE_BILLING_ADDRESS'); ?>
        </h2>
    </div>
    <div class="form-normal">
        <label><?php echo Yii::t('profile', 'ENTRY_ADDRESS'); ?></label>
        <?php
        echo $form->field($modelAddressBook, 'entry_street_address')->textInput([
            'placeholder' => Yii::t('profile', 'PLACEHOLDER_ADDRESS1'),
            'required' => ($modelCustomers->einvoice_type === 'business'),
            'data-required-when' => 'business',

        ]);
        echo $form->field($modelAddressBook, 'entry_suburb')->textInput([
            'placeholder' => Yii::t('profile', 'PLACEHOLDER_ADDRESS2'),
        ]);
        ?>
        <label><?php echo Yii::t('profile', 'ENTRY_CITY'); ?></label>
        <?php
        echo $form->field($modelAddressBook, 'entry_city')->textInput([
            'placeholder' => Yii::t('profile', 'ENTRY_CITY'),
            'required' => ($modelCustomers->einvoice_type === 'business'),
            'data-required-when' => 'business',
        ]);
        ?>
        <label for="Country"><?php echo Yii::t('profile', 'ENTRY_COUNTRY'); ?></label>
        <?php
        echo $form->field($modelAddressBook, 'entry_country_id')->widget(\conquer\select2\Select2Widget::className(), [
            'placeholder' => Yii::t('general', 'TEXT_PLEASE_SELECT'),
            'bootstrap' => false,
            'settings' => [
                'disabled' => true,
            ],
        ]);
        ?>
        <div class="input-row-2-coll">
            <label><?php echo Yii::t('profile', 'ENTRY_STATE') . '/' . Yii::t('profile', 'ENTRY_ZIP'); ?></label>
            <?php
            echo $form->field($modelAddressBook, 'entry_zone_id', ['options' => ['class' => 'form-group hide']])->widget(\conquer\select2\Select2Widget::className(), [
                'placeholder' => Yii::t('profile', 'COMBOBOX_STATE'),
                'bootstrap' => false,
            ]);
            echo $form->field($modelAddressBook, 'entry_state')->textInput([
                'placeholder' => Yii::t('profile', 'ENTRY_STATE'),
                'class' => 'hide',
            ]);
            ?>
        </div>
        <div class="input-row-2-coll">
            <div class="input-row-2-coll-right">
                <?php
                echo $form->field($modelAddressBook, 'entry_postcode')->textInput([
                    'placeholder' => Yii::t('profile', 'ENTRY_ZIP'),
                    'required' => ($modelCustomers->einvoice_type === 'business'),
                    'data-required-when' => 'business',
                ]);
                ?>
            </div>
        </div>
        <div class="clearfix">
            <div class="input-row"></div>
            <div class="btn-spot">
                <button type="submit" class="btn-submit"><?php echo Yii::t('profile', 'BTN_SAVE_CHANGED'); ?></button>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<div class="card">
    <div style="display: flex; align-items: center; justify-content: space-between;" class="og-flex-direction-responsive">
        <h2>
            <?php echo Yii::t('profile', 'TEXT_DELETE_ACCOUNT'); ?>
        </h2>

        <div style="display: flex; align-items: center">
            <a rel="noopener noreferrer" target="_blank" class="btn-default" style="margin-right: 10px;white-space: nowrap" href="https://helpdesk.offgamers.com/support/solutions/articles/**********-how-to-disable-or-remove-your-offgamers-account"><?php echo Yii::t('profile', 'TEXT_LEARN_MORE'); ?></a>
            <button class="btn-logout" onClick="fd_request_delete_account()"><?php echo Yii::t('profile', 'BTN_REQUEST'); ?></button>
        </div>
    </div>
</div>

<script>
    window.fwSettings = {
        'widget_id': **********
    };
    ! function() {
        if ("function" != typeof window.FreshworksWidget) {
            var n = function() {
                n.q.push(arguments)
            };
            n.q = [], window.FreshworksWidget = n
        }
    }()
    window.onload = setTimeout(function() {
        FreshworksWidget("hide", "launcher");
    }, 1000);
</script>
<script type='text/javascript' src='https://widget.freshworks.com/widgets/**********.js' async defer></script>