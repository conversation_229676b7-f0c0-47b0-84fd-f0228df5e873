<?php

\frontend\assets\FormAsset::register($this);
\frontend\assets\ProfileAsset::register($this);
$js = <<<JS
    var hash = window.location.hash;
    var open = null;
    if (hash != null && hash != '' && hash.indexOf('#change-') === 0) {
        open = hash.replace('#change-', 'expand-');
    }
    if (open != null) {
        jQuery('[data-target=' + open + ']').click();
    }
JS;
$this->registerJs($js, yii\web\View::POS_READY);
?>

<?php
if (\Yii::$app->session->hasFlash('success')) {
    echo $this->render("/sso/cookieAllImage", \common\components\SSOCom::clientURLSetSSOToken());
}
?>

<div class="page-title">
    <h1><?php echo \Yii::t('profile', 'TITLE_SECURITY_INFO'); ?></h1>
</div>

<!-- Email Address -->
<a id="change-email" class="anchor-after-sticky"></a>
<div class="card">
    <div class="card-title">
        <h2><?php echo \Yii::t('profile', 'ENTRY_EMAIL'); ?></h2>
    </div>
    <div class="card-description-left">
        <p>
            <?php
            echo \Yii::t('profile', 'TEXT_EMAIL_DESC_1', [
                'email' => \yii\helpers\Html::tag('b', $cust_email),
            ]);
            ?>
            <br>
            <?php echo \Yii::t('profile', 'TEXT_EMAIL_DESC_2'); ?>
        </p>
    </div>

    <div class="btn-spot-right-expand">
        <button class="btn-expand-collapse btn-default" type="button" data-target="expand-email" id="expand-email-btn" data-expandlabel="<?php echo \Yii::t('general', 'BTN_EXPAND'); ?>" data-collapselabel="<?php echo \Yii::t('general', 'BTN_CLOSE'); ?>"><?php echo \Yii::t('general', 'BTN_EXPAND'); ?></button>
    </div>

    <div class="collapse-box" id="expand-email">

        <!-- Partition -->
        <div class="partition-shadow"></div>

        <div class="collapse-content">
            <?php
            echo $this->render('_changeEmail', $email_form_param);
            ?>
        </div>
    </div>
</div>
<!-- Password -->
<a id="change-password" class="anchor-after-sticky"></a>
<div class="card">
    <div class="card-title">
        <h2><?php echo \Yii::t('sso', 'ENTRY_PASSWORD'); ?></h2>
    </div>
    <div class="card-description-left">
        <p>
            <?php echo \Yii::t('profile', 'TEXT_PASSWORD_DESC_1'); ?>
            <br>
            <?php echo \Yii::t('profile', 'TEXT_PASSWORD_DESC_2'); ?>
        </p>
    </div>

    <div class="btn-spot-right-expand">
        <button class="btn-expand-collapse btn-default" type="button" data-target="expand-password" id="expand-password-btn" data-expandlabel="<?php echo \Yii::t('general', 'BTN_EXPAND'); ?>" data-collapselabel="<?php echo \Yii::t('general', 'BTN_CLOSE'); ?>"><?php echo \Yii::t('general', 'BTN_EXPAND'); ?></button>
    </div>

    <div class="collapse-box" id="expand-password">

        <!-- Partition -->
        <div class="partition-shadow"></div>

        <div class="collapse-content">
            <?php
            echo $this->render('_changePassword', $password_form_param);
            ?>
        </div>
    </div>
</div>
<!-- Mobile Phone -->
<a id="change-phone" class="anchor-after-sticky"></a>
<div class="card">
    <div class="card-title">
        <h2><?php echo \Yii::t('profile', 'ENTRY_MOBILE_PHONE'); ?></h2>
    </div>
    <div class="card-description-left">
        <p>
            <?php
            if (empty($cust_phone)) {
                echo \Yii::t('profile', 'TEXT_MOBILE_PHONE_DESC_1b');
            } else {
                echo \Yii::t('profile', 'TEXT_MOBILE_PHONE_DESC_1', [
                    'phone' => \yii\helpers\Html::tag('b', $cust_phone),
                ]);
            }
            ?>
            <br>
            <?php echo \Yii::t('profile', 'TEXT_MOBILE_PHONE_DESC_2'); ?>
        </p>
    </div>

    <div class="btn-spot-right-expand">
        <button class="btn-expand-collapse btn-default" type="button" data-target="expand-phone" id="expand-phone-btn" data-expandlabel="<?php echo \Yii::t('general', 'BTN_EXPAND'); ?>" data-collapselabel="<?php echo \Yii::t('general', 'BTN_CLOSE'); ?>"><?php echo \Yii::t('general', 'BTN_EXPAND'); ?></button>
    </div>

    <div class="collapse-box" id="expand-phone">

        <!-- Partition -->
        <div class="partition-shadow"></div>

        <div id="last-four-digit" class="collapse-content">
            <?php
            echo $this->render('_changePhone', $phone_form_param);
            ?>
        </div>
    </div>
</div>
<!-- Two-Factor Auth -->
<a id="change-2fa" class="anchor-after-sticky"></a>
<div class="card">
    <div class="card-title">
        <h2><?php echo \Yii::t('profile', 'ENTRY_TWO_FACTOR_AUTH'); ?></h2>
    </div>
    <div class="card-description-left">
        <p>
            <?php echo \Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_DESC'); ?>
            <br>
            <?php
            if ($twofa_form_param['is_enabled']) {
                echo Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_STATUS', ['STATUS' => '<span style="color: #48cd89;">' . Yii::t('profile', 'TEXT_TFA_STATUS_ACTIVE') . '</span>']);
            } else {
                echo Yii::t('profile', 'TEXT_TWO_FACTOR_AUTH_STATUS', ['STATUS' => '<b>' . Yii::t('profile', 'TEXT_TFA_STATUS_INACTIVE') . '</b>']);
            }
            ?>
        </p>
    </div>

    <div class="btn-spot-right-expand">
        <button class="btn-expand-collapse btn-default" type="button" data-target="expand-2fa" id="expand-2fa-btn" data-expandlabel="<?php echo \Yii::t('general', 'BTN_EXPAND'); ?>" data-collapselabel="<?php echo \Yii::t('general', 'BTN_CLOSE'); ?>"><?php echo \Yii::t('general', 'BTN_EXPAND'); ?></button>
    </div>

    <div class="collapse-box" id="expand-2fa">

        <!-- Partition -->
        <div class="partition-shadow"></div>

        <div class="collapse-content">
            <?php
            echo $this->render('_change2fa', $twofa_form_param);
            ?>
        </div>	
    </div>
</div>