<?php
/* @var $this \yii\web\View */
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center;">
            <h2><?php echo Yii::t("site", 'HEADER_RECAPTCHA'); ?></h2>
        </span>
    </div>
    
    <?php
    if (!empty($captcha['note'])) {
        echo \yii\helpers\Html::tag('p', $captcha['note']);
    }
    ?>
    
    <?php echo \yii\helpers\Html::beginForm('', 'post', ['class' => 'form']); ?>
    <div class="card-captcha">
        <?php
        echo $captcha['html'];
        if (!empty($captcha['js'])) {
            $this->registerJs($captcha['js'], yii\web\View::POS_READY);
        }
        ?>
    </div>
    <div class="input-row"></div>
    <div class="btn-spot">
        <button type="submit" class="btn-default-full"><?php echo Yii::t('general', 'BTN_SUBMIT'); ?></button>
    </div>
    <?php echo \yii\helpers\Html::endForm(); ?>
</div>