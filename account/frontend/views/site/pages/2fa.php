<?php
/* @var $this \yii\web\View */
?>

<div class="page-title">
    <h1>SETUP TWO-FACTOR AUTHENTICATION</h1>
</div>

<div class="page-description">
    <p>Turn on two-factor authentication (2FA) for your personal OffGamers account for an added layer of security. You’ll need to have access to your phone when you sign in to OffGamers - so if your password is compromised or stolen, only you can sign in to your account. </p>
</div>

<div class="card">
    <div class="card-description">
        <h2>How it works:</h2>
        <p>
        <ul>
            <li>Every time you sign in to OffGamers, you’ll be asked to enter a verification code.</li>
            <li>Choose to have the verification code sent by text message or from an authentication app on your phone.</li>
            <li>Enter the code in OffGamers and you’ll be signed in to OffGamers with added peace of mind.</li>
        </ul>
        </p>
    </div>
</div>



<div class="card">
    <div class="card-description">
        <h2>How to Turn on 2FA</h2>
        <p><strong>Step 1 - Download and install an authentication app</strong><br>Before you can set up 2FA on your account, you’ll need to download and install an authentication app on your device. OffGamers 2FA can be used with most Time-Based, One-Time Password (TOTP) applications. Here are a few options to get you started:
        <ul>
            <li><strong>iPhone</strong> - <a href="https://itunes.apple.com/my/app/google-authenticator/id388497605?mt=8" target="_blank" alt="sample" title="sample">Google Authenticator</a>, <a href="https://itunes.apple.com/my/app/duo-mobile/id422663827?mt=8" target="_blank" alt="sample" title="sample">Duo Mobile</a>, <a href="https://itunes.apple.com/us/app/authy/id494168017" target="_blank" alt="sample" title="sample">Authy</a>, <a href="https://itunes.apple.com/my/app/1password-password-manager/id568903335?mt=8" target="_blank" alt="sample" title="sample">1Password</a></li>
            <li><strong>Android</strong> - <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank" alt="sample" title="sample">Google Authenticator</a>, <a href="https://play.google.com/store/apps/details?id=com.duosecurity.duomobile" target="_blank" alt="sample" title="sample">Duo Mobile</a>, <a href="https://play.google.com/store/apps/details?id=com.authy.authy" target="_blank" alt="sample" title="sample">Authy</a></li>
            <li><strong>Windows Phone</strong> - <a href="https://www.microsoft.com/en-my/p/microsoft-authenticator/9nblgggzmcj6?activetab=pivot:overviewtab" target="_blank" alt="sample" title="sample">Microsoft Authenticator</a>, <a href="https://www.microsoft.com/en-my/p/duo-mobile/9nblggh08m1g?activetab=pivot:overviewtab" target="_blank" alt="sample" title="sample">Duo Mobile</a></li>
        </ul>
        </p>

        <!-- Partition -->
        <div class="partition"></div>

        <p><strong>Step 2 - Turn on 2FA in OffGamers</strong></p>
        <ul>
            <li>Sign in to the appropriate workspace, and visit your Account page <a href="https://account.offgamers.com/profile/security#change-2fa" target="_blank" alt="sample" title="sample">here</a>.</li>
            <li>Expand Two-factor Authentication, and click Setup Two-Factor Authentication.</li>
            <li>Enter your password, and click Use an app to retrieve authentication codes from the authentication app on your device.</li>
            <li>Add a new account. In most apps, you can do this by tapping the + icon.</li>
            <li>Scan the QR code by using your device's camera. If you prefer, you can choose to enter the code by hand.</li>
            <li>On OffGamers 2FA configuration page, enter the 6-digit verification code that your authentication app generates.</li>
            <li>To finish, press Verify Code.</li>
        </ul>
        <p>That's it! From now on, when you log in to OffGamers, just open your authentication app and enter a code along with your password.</p>
    </div>
</div>
