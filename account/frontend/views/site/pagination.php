<?php
/* @var $this \yii\web\View */

/*
 * pagination passing param
 * $current : current page
 * $record  : total of record display
 * $total   : total of record
 * $query   : other $_GET params
 */

if ($record > 0) {
    $max_btn = Yii::$app->params["GENERAL_CONFIG"]["PAGER_MAX_BUTTON"];
    $max_page = ceil($total / Yii::$app->params["GENERAL_CONFIG"]["PAGER_ITEM_PER_PAGE"]);
    $page_btn = (($max_btn * ceil($current / $max_btn)) - $max_btn) + 1;

    if (($max_page >= $page_btn) && ($max_btn > ($max_page - $page_btn))) {
        $start_btn = ($page_btn - (($max_btn - ($max_page - $page_btn)) - 1));
        if ($start_btn > 1) {
            $page_btn = $start_btn;
        }
    }

    $link = Yii::$app->controller->id . '/' . Yii::$app->controller->action->id;
    $prev = (($current - 1) > 0 ? Yii::$app->urlManager->createUrl(array_merge([$link, "page" => ($current - 1)], $query)) : "#");
    $last = ($max_page >= ($current + 1) ? Yii::$app->urlManager->createUrl(array_merge([$link, "page" => ($current + 1)], $query)) : "#");

    $start = (($current - 1) * Yii::$app->params["GENERAL_CONFIG"]["PAGER_ITEM_PER_PAGE"]) + 1;
    $end = ($start + $record) - 1;
    ?>
        <div class="pagination">
            <a href="<?php echo $prev; ?>"><span aria-hidden="true">&laquo;</span><span class="sr-only">Previous</span></a>
            <?php
            for ($i = 0; ($max_page >= $page_btn) && ($max_btn > $i); $i++, $page_btn++) {
                if ($page_btn == $current) {
                    ?>
                    <a href="#" class="active"><?php echo $page_btn; ?><span class="sr-only">(current)</span></a>
                <?php } else { ?>
                    <a href="<?php echo Yii::$app->urlManager->createUrl(array_merge([$link, "page" => $page_btn], $query)); ?>"><?php echo $page_btn; ?></a>
                    <?php
                }
            }
            ?>
            <a href="<?php echo $last; ?>"><span aria-hidden="true">&raquo;</span><span class="sr-only">Next</span></a>
            </div>
<?php } ?>
