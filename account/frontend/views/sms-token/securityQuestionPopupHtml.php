<?php
/* @var $this \yii\web\View */
use yii\helpers\Url;
use yii\helpers\Html;

\frontend\assets\SmsTokenAsset::register($this);
?>
<script type="text/javascript">
    var SMS_TOKEN_URL = '<?php echo \yii\helpers\Url::to(['sms-token/request-sms-token'], true) ?>';
    var SMS_RESEND_TOKEN_URL = '<?php echo \yii\helpers\Url::to(['sms-token/request-sms-token-resend'], true) ?>';
    var VIEW_EMAIL_TOKEN = '<?php echo \yii\helpers\Url::to(['sms-token/email-token'], true) ?>';
    var RESEND_EMAIL_TOKEN = '<?php echo \yii\helpers\Url::to(['sms-token/resend-email-token'], true); ?>';

    var TEXT_RESEND_IN_X_SEC = '<?php echo Yii::t("smsToken", "TEXT_RESEND_IN_X_SEC"); ?>';
    var LINK_RESEND = '<?php echo Yii::t("smsToken", "LINK_RESEND"); ?>';
</script>

<?php

$token_answer_field_name = 'tokenAnswer';

$data = [
    'security-token' => 'true',
    'request-type' => $requestType,
    'service-id' => isset($service_id) ? $service_id : Yii::$app->name,
    'token-answer-field-name' => $token_answer_field_name,
];
if (isset($contact_no_dialing_code_id_field) && isset($contact_no_field)) {
    $data['contact_no_dialing_code_id_field'] = $contact_no_dialing_code_id_field;
    $data['contact_no_field'] = $contact_no_field;
}

echo yii\helpers\Html::beginTag('div', [
    'class' => 'card-security-token',
    'data' => $data,
]);

$option = ['id' => 'input-six-box', 'class'=>['form-group',!empty($tokenMsg) ? " has-error" : ""],'style' => ['display' => 'none'] ];
$option2 = ['id' => 'note-memo', 'class' => 'input-row-text', 'style' => ['display' => 'none']];
if (!empty($tokenMsg)) {
    Html::addCssStyle($option, ['display' => 'block']);
    Html::addCssStyle($option2, ['display' => 'block']);
}
?>
    <div class="card-security-title">
        <h2><?php echo Yii::t('smsToken', 'TITLE_SECURITY_TOKEN'); ?></h2>
    </div>
    <div class="tooltip-icon">
        <div class="tooltip" onclick="jQuery(this).find('.tooltip-text').toggleClass('hide');"><i class="fa fa-info-circle"></i>
            <span class="tooltip-text hide"><?php echo Yii::t('tooltips', 'MSG_PHONE_WARNING'); ?></span>
        </div>
    </div>
    
    <div class="clearfix"></div>
    <div class="card-security-content">
        <div class="card-security-request-area">
            <?php echo Html::beginTag('div', $option); ?>
                <?php
                echo \frontend\helpers\Html::tokenInput($token_answer_field_name, $token);
                ?>
                <div class="clearfix"></div>
                <div class="input-row-6-coll-error input-row-error">
                    <i class="fa fa-exclamation-circle"></i>
                    <span class="help-block"><?php echo $tokenMsg; ?></span>
                </div>
            <?php echo Html::endTag('div'); ?>
            
            <div class="btn-spot-special btn-send-token">
                <button id="button_request" type="button" class="btn-default btn-request-token">
                    <span class="fa fa-mobile-phone"></span>
                    <span class="btn-text"><?php echo Yii::t('smsToken', 'BTN_REQUEST_SECURITY_TOKEN') ?></span>
                </button>
                <p id="request-timer"><!--Message holder --></p>
            </div>
               
            <div id="note-memo" class="input-row-text" hidden>
                <p>
                    <?php 
                    if (isset(\Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$data['service-id']]['pre_login_support_url'])) {
                        echo \yii\helpers\Html::a(Yii::t('smsToken', 'LINK_NEED_HELP'), \Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$data['service-id']]['pre_login_support_url'], ['target' => '_blank']);
                    }else{
                        echo \yii\helpers\Html::a(Yii::t('smsToken', 'LINK_NEED_HELP'), \Yii::$app->params['pre_login_support_url'], ['target' => '_blank']);
                    }
                    if ($requestType == 'security_token_request') {
                        echo "&nbsp;/&nbsp;";
                        echo \yii\helpers\Html::a(Yii::t('smsToken', 'LINK_LOST_PHONE'), "javascript:void(0)", [
                            'class' => 'link_resend_token',
                        ]);
                    }
                    ?>
                </p>
            </div>
        </div>
    </div>
    <div class="card-security-captcha">
        <div class="card-captcha"></div>
        <div class="btn-spot-special btn-send-token">
            <button type="button" class="btn-default btn-request-token">
                <span class="fa fa-mobile-phone"></span>
                <span class="btn-text"><?php echo Yii::t('smsToken', 'BTN_REQUEST_SECURITY_TOKEN') ?></span>
            </button>
            <p><!--Message holder --></p>
        </div>
    </div>
    <div class="space-20"></div>
<?php echo yii\helpers\Html::endTag('div');
