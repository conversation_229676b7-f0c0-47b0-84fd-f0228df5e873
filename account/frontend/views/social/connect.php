<?php

use common\models\SnsConnection;
use frontend\widgets\hybridauth\RenderProviders;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);
$js = <<<JS
    var hash = window.location.hash;
    var open = null;
    if (hash != null && hash != '' && hash.indexOf('#social-') === 0) {
        open = 'expand-' + hash.substring(1);
    }
    if (open != null) {
        jQuery('[data-target=' + open + ']').click();
    }
JS;
$this->registerJs($js, yii\web\View::POS_READY);
?>
<div class="page-title">
    <h1><?php echo Yii::t('socialConnect', 'TITLE_SOCIAL_CONNECT'); ?></h1>
</div>

<!-- Social Connect -->
<?php
if (Yii::$app->hybridauth->getAllowedProviders()) {
    
    $link_form_callable = function() use ($model) {
        ob_start();
        $form = \frontend\widgets\ActiveForm::begin([
            'action' => ['social/confirm-link', 'ha_provider' => '{PROVIDER}'],
            'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
        ]);
        ?>
        <label><?php echo Yii::t('socialConnect', 'TEXT_CONFIRM_PASSWORD'); ?></label>
        <?php echo $form->field($model, 'password')->passwordInput(); ?>
        <div class="input-row-text-right">
            <?php echo \yii\helpers\Html::a(Yii::t('profile', 'LINK_FORGET_PASSWORD'), ['sso/forget']); ?>
        </div>
        <div class="input-row"></div>
        <div class="text-center">
            <button type="submit" class="btn-submit"><?php echo Yii::t('socialConnect', 'BTN_CONNECT'); ?></button>
        </div>
        <?php
        \frontend\widgets\ActiveForm::end();
        return ob_get_clean();
    };
    
    $unlink_form_callable = function() use ($model) {
        ob_start();
        $form = \frontend\widgets\ActiveForm::begin([
            'action' => ['social/confirm-unlink'],
            'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
        ]);
        echo Html::hiddenInput('ha_provider', '{PROVIDER}');
        ?>
        <label><?php echo Yii::t('socialConnect', 'TEXT_CONFIRM_PASSWORD'); ?></label>
        <?php echo $form->field($model, 'password')->passwordInput(); ?>
        <div class="input-row-text-right">
            <?php echo \yii\helpers\Html::a(Yii::t('profile', 'LINK_FORGET_PASSWORD'), ['sso/forget']); ?>
        </div>
        <div class="input-row"></div>
        <div class="text-center">
            <button type="submit" class="btn-logout"><?php echo Yii::t('socialConnect', 'BTN_DISCONNECT'); ?></button>
        </div>
<?php
        \frontend\widgets\ActiveForm::end();
        return ob_get_clean();
    };

    echo RenderProviders::widget([
        'connectedProviders' => SnsConnection::model()->getConnections(Yii::$app->user->id),
        'template_items' => '{TEMPLATE_ITEM}',
        'template_item' => function () use ($link_form_callable, $unlink_form_callable) {
            return '
        {TEMPLATE_SEPARATOR}
        <a id="social-{LOWERCASE_PROVIDER}-enabled" class="anchor-after-sticky"></a>
        <div class="card" id="ha-{PROVIDER}-enabled" style="display: {DISPLAY_ENABLED};">
            <div class="card-title">
                <div class="card-title-icon">
                    <i class="{PROVIDER_FONTAWESOME_ICON}"></i>
                </div>
                <h2>{UPPERCASE_PROVIDER}</h2>
            </div>
            <div class="card-description-left">
                <p>' . Yii::t('socialConnect', 'TEXT_CONNECTION_STATUS', ['STATUS' => '<span style="color: #48cd89;">' . Yii::t('socialConnect', 'TEXT_CONNECTION_STATUS_CONNECTED') . '</span>', 'SYS_PROVIDER' => '{PROVIDER}']) . '</p>
                <br>
            </div>
            <div class="btn-spot-right-expand">
                <button class="btn-expand-collapse btn-default" type="button" data-target="expand-social-{LOWERCASE_PROVIDER}-enabled" id="expand-social-{LOWERCASE_PROVIDER}-enabled-btn" data-expandlabel="' . \Yii::t('general', 'BTN_EXPAND') . '" data-collapselabel="' . \Yii::t('general', 'BTN_CLOSE') . '">' . \Yii::t('general', 'BTN_EXPAND') . '</button>
            </div>
            <div class="collapse-box" id="expand-social-{LOWERCASE_PROVIDER}-enabled">

                <!-- Partition -->
                <div class="partition-shadow"></div>

                <div class="collapse-content">
                    ' . $unlink_form_callable() . '
                </div>
            </div>
        </div>
        <a id="social-{LOWERCASE_PROVIDER}-disabled" class="anchor-after-sticky"></a>
        <div class="card" id="ha-{PROVIDER}-disabled" style="display: {DISPLAY_DISABLED};">
            <div class="card-title">
                <div class="card-title-icon">
                    <i class="{PROVIDER_FONTAWESOME_ICON}"></i>
                </div>
                <h2>{UPPERCASE_PROVIDER}</h2>
            </div>
            <div class="card-description-left">
                <p>' . Yii::t('socialConnect', 'TEXT_CONNECTION_STATUS', ['STATUS' => '<b>' . Yii::t('socialConnect', 'TEXT_CONNECTION_STATUS_NOT_CONNECTED') . '</b>', 'SYS_PROVIDER' => '{PROVIDER}']) . '</p>
                <br>
            </div>
            <div class="btn-spot-right-expand">
                <button class="btn-expand-collapse btn-default" type="button" data-target="expand-social-{LOWERCASE_PROVIDER}-disabled" id="expand-social-{LOWERCASE_PROVIDER}-disabled-btn" data-expandlabel="' . \Yii::t('general', 'BTN_EXPAND') . '" data-collapselabel="' . \Yii::t('general', 'BTN_CLOSE') . '">' . \Yii::t('general', 'BTN_EXPAND') . '</button>
            </div>
            <div class="collapse-box" id="expand-social-{LOWERCASE_PROVIDER}-disabled">

                <!-- Partition -->
                <div class="partition-shadow"></div>

                <div class="collapse-content">
                    ' . $link_form_callable() . '
                </div>
            </div>
        </div>';
        },
    ]);
} else {
    echo '<div>' . Yii::t('socialConnect', 'TEXT_SNS_NOT_AVAILABLE') . '</div>';
}
/*


			
 */