<?php
/* @var $this \yii\web\View */

// 3rd party URL query
$fq_arr = array();
$tmp_arr = array();
if (isset($f_query) && !empty($f_query)) {
    parse_str($f_query, $tmp_arr);
    $f_query = '?' . $f_query;
    // 3rd party portal URL query
    if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
        $origin = urldecode(urldecode($tmp_arr["origin"]));
        $fq_arr = $tmp_arr;
    }
} else {
    $f_query = '';
}

$service_id = isset($fq_arr["service"]) ? $fq_arr["service"] : null;
$this->params['service_id'] = $service_id;

$support_url = \Yii::$app->params['pre_login_support_url'];

?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center;">
            <h2><?php echo Yii::t('sso', 'HEADER_TWO_FACTOR'); ?></h2>
        </span>
    </div>
    
    <p><?php echo Yii::t("sso", "TEXT_DEVICE_NOTE_2FA", [
        'SYS_APP_NAME' => Yii::$app->name,
    ]); ?></p>
    
    <?php if ($status) { ?>
        <?php echo \yii\helpers\Html::beginForm(\yii\helpers\Url::to(['sso/device']) . $f_query, 'post', ['class' => 'form']); ?>
        <?php echo \frontend\helpers\Html::tokenInput('pin', ''); ?>
        <p><?php echo Yii::t("sso", "TEXT_DEVICE_NOTE_PROBLEM"); ?>
            <?php echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_DEVICE_CONTACT_SUPPORT'), $support_url, ['target' => '_blank']); ?>
        </p>
        <div class="input-row"></div>
        <div class="btn-spot"><button type="submit" name="<?php echo $token; ?>" value="<?php echo $mail; ?>" class="btn-default-full"><?php echo Yii::t('general', 'BTN_SUBMIT'); ?></button></div>
        <?php echo \yii\helpers\Html::endForm(); ?>
    <?php } else { ?>
        <p><b><?php echo Yii::t("sso", "TEXT_DEVICE_MAX_TRIAL", ['SUPPORT_URL' => $support_url]); ?></b></p>
    <?php } ?>
</div>
<p><?php echo Yii::t('sso', 'TEXT_RETURN_TO', array('SYS_LOGIN' => \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOG_IN'), \yii\helpers\Url::to(['sso/login'], true) . $f_query))); ?></p>
