<?php
/* @var $this \yii\web\View */

if (isset($fq_arr['service'])) {
    $this->params['service_id'] = $fq_arr['service'];
}
?>
<div class="card-title">
    <span style="text-align: center">
        <h2><?php echo Yii::t('sso', 'HEADER_LOGIN_SSO', array("SYS_SITE_NAME" => Yii::$app->name)); ?></h2>
    </span>
</div>
<?php
$form = \frontend\widgets\ActiveForm::begin([
    'id' => 'frm-social-login',
    'action' => \yii\helpers\Url::to(['sso/login'], true) . $f_query,
]);
echo frontend\widgets\hybridauth\RenderProviders::widget(array_merge([
    'url' => \yii\helpers\Url::to(array_merge(['sso/xpress', 'ha_provider' => '{PROVIDER}'], $fq_arr)),
    'display' => 'vertical',
    'item_space_right' => 5,
    'template_items' => '{TEMPLATE_ITEM}',
    'template_item' => '<div class="btn-spot-special btn-spot-{LOWERCASE_PROVIDER}"><button type="submit" class="btn-{LOWERCASE_PROVIDER}" id="ha-{PROVIDER}" onclick="{ON_CLICK}" ><div class="icon-socialconnect"><span class="fa fa-{PROVIDER_FONTAWESOME}"></span></div> ' . Yii::t('socialConnect', 'BTN_CONNECT_WITH_SNS', array('SYS_PROVIDER' => '{PROVIDER}')) . '</button></div>',
    'onClick' => 'jQuery(\'#frm-social-login\').attr(\'action\', \'{URL}\');',
], $is_mode_launcher ? [
    'popup' => true,
] : []));
?>

<?php if($sns > 0){ ?>
        <div class="clearfix"></div>
        <label class="custom-checkbox">
            <?php echo Yii::t('sso', 'TEXT_LOGIN_REMEMBER_ME'); ?>
            <input type="checkbox" checked="true" name="remember_me" style="margin-top: 2px;">
            <span class="checkmark"></span>
        </label>
<?php
    $fb_login_support = \common\components\GeneralComponent::checkGoNativeVersionSupport(Yii::$app->request->getUserAgent(),'1.0.6');
    if (isset($_SERVER['HTTP_USER_AGENT']) && strpos($_SERVER['HTTP_USER_AGENT'], 'GoNativeAndroid') !== false && !$fb_login_support) {
        echo '<style>.btn-spot-facebook{display:none}</style>';
    }
} ?>

<?php $form->end(); ?>
<!-- OR -->
<?php if($sns > 0){ ?>
        <div class="partition"><div class="partition-ball">OR</div></div>
<?php } ?>
<?php
$form = \frontend\widgets\ActiveForm::begin([
    'action' => \yii\helpers\Url::to(['sso/login'], true) . $f_query,
]);
echo $form->field($model, 'customers_email_address')->textInput([
    'placeholder' => Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
    'value' => (isset($mail) ? $mail : '')
]);
echo $form->field($model, 'customers_password')->passwordInput([
    'placeholder' => Yii::t('sso', 'ENTRY_PASSWORD'),
]);
?>
<div class="clearfix"></div>
<div class="input-row-login">
    <label class="custom-checkbox">
        <?php echo Yii::t('sso', 'TEXT_LOGIN_REMEMBER_ME'); ?>
        <input type="checkbox" checked="true" name="remember_me" style="margin-top: 2px;">
        <span class="checkmark"></span>
    </label>
</div>
<div class="input-row-login">
    <div class="input-row-text-right"><?php echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOGIN_PROBLEM_TO_LOGIN'), \yii\helpers\Url::to(['sso/forget']) . $f_query); ?></div>
</div>
<div class="clearfix">
    <div class="btn-spot">
        <button type="submit" class="btn btn-submit-full"><?php echo Yii::t('sso', 'BUTTON_LOGIN'); ?></button>
    </div>
</div>
<?php $form->end(); ?>
<p><?php echo Yii::t('sso', 'TEXT_LOGIN_NOT_YET_WITH_US'); ?> <?php echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOGIN_SIGN_UP_HERE') . ' &rarr;', \yii\helpers\Url::to(['sso/sign-up']) . $f_query); ?></p>