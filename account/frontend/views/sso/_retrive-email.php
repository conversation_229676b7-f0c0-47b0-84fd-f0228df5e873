<?php
\frontend\assets\FormAsset::register($this);

// 3rd party URL query
$fq_arr = array();
if (isset($f_query) && !empty($f_query)) {
    $tmp_arr = array();
    parse_str($f_query, $tmp_arr);
    $f_query = '?' . $f_query;

    // 3rd party portal URL query
    if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
        $origin = urldecode(urldecode($tmp_arr["origin"]));
        if (\common\components\SSOCom::clientURLMatch($tmp_arr["service"], $origin)) {
            $fq_arr = $tmp_arr;
        }
    }
}
?>

<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center;">
            <h2><?php echo Yii::t('sso', 'HEADER_RETRIEVE_EMAIL'); ?></h2>
        </span>
    </div>
    <div> 
        <p style="text-align: center"><?php echo Yii::t('sso', 'RETRIEVE_EMAIL_DESCRIPTION', ["CUSTOMER_EMAIL" => $email]); ?>
            <?php echo Yii::t('sso', 'RETRIEVE_EMAIL_DESCRIPTION_2'); ?></p>
    </div>
    <?php
    $form = \frontend\widgets\ActiveForm::begin([
                'id' => 'customers-setup',
                'action' => \yii\helpers\Url::to(['sso/get-reclaim-mobile'], true) . $f_query,
    ]);
    echo $form->field($model, 'customers_email_address')->textInput([
        'placeholder' => Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
    ]);
    ?>
    <div class="clearfix"></div>
    <div class="input-row"></div>
    <div class="btn-spot">
        <button type="submit" class="btn btn-submit-full"><?php echo Yii::t('sso', 'RECLAIM NOW'); ?></button>
    </div>
    <?php $form->end(); ?>
</div>
