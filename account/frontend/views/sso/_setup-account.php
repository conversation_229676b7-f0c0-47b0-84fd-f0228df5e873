<?php
/* @var $this \yii\web\View */
/* @var $token \frontend\models\UpdatePhoneForm */

\frontend\assets\FlagsAsset::register($this);

$no_token_msg = json_encode(\Yii::t('smsToken', 'ERROR_PLEASE_REQUEST_TOKEN'));
$js = <<<JS
    jQuery('#customers-setup').on('beforeSubmit', function(event) {
        if (jQuery('#customers-setup .card-security-token').length && jQuery('#customers-setup .card-security-token').data('security_question').isTokenAnswerEmpty()) {
            toastr['error']($no_token_msg);
            return false;
        }
        return true;
    });
JS;
$this->registerJs($js, \yii\web\View::POS_END);
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card">
    <div class="card-title">
        <span style="text-align: center">
            <h2><?php echo Yii::t('sso', 'HEADER_SETUP_ACCOUNT'); ?></h2>
        </span>
    </div>
    <div>
        <p style="text-align: center"><?php echo Yii::t('sso', 'SAFETY_DESCRIPTION'); ?></p>
    </div>
    <div class="clearfix"></div>
    <?php
    $form = \frontend\widgets\ActiveForm::begin([
                'id' => 'customers-setup',
                'action' => \yii\helpers\Url::to(['sso/set-customers-setup'], true) . $f_query,
    ]);
    echo $form->field($model, 'customers_firstname')->textInput([
        'placeholder' => Yii::t('sso', 'ENTRY_FIRST_NAME'),
        'onkeypress' => 'return onlyAlphabets(event)',
    ]);
    echo $form->field($model, 'customers_lastname')->textInput([
        'placeholder' => Yii::t('sso', 'ENTRY_LAST_NAME'),
        'onkeypress' => 'return onlyAlphabets(event)',
    ]);
    ?>
    <div class="space-10"></div>
    <div class="clearfix"></div>
    <?php if (empty($info_verified)) { ?>
        <div>
            <p style="text-align: center"><?php echo Yii::t('sso', 'SAFETY_DESCRIPTION_2'); ?></p>
        </div>
        <div class="clearfix"></div>
        <div class="input-row-2-coll">
    <?php
    echo $form->field($model, 'customers_country_dialing_code_id')->widget(\conquer\select2\Select2Widget::className(), [
        'bootstrap' => false,
        'items' => \yii\helpers\ArrayHelper::map($allCountry, 'countries_id', function($array) {
                    return "<span class='flag flag-" . strtolower($array['countries_iso_code_2']) . "'></span> +$array[countries_international_dialing_code] <span class='select2-hide-in-selected'>$array[countries_name]</span>";
                }),
        'settings' => [
            'prompt' => Yii::t('general', 'TEXT_PLEASE_SELECT'),
            'escapeMarkup' => new yii\web\JsExpression('function(text) { return text; }'),
        ],
    ]);
    ?>
        </div>
        <div class="input-row-2-coll">
            <div class="input-row-2-coll-right">
    <?php
    echo $form->field($model, 'customers_telephone')->textInput([
        'placeholder' => Yii::t('sso', 'TEXT_MOBILE_NUMBER'),
        'autocomplete' => 'off',
        'onkeypress' => 'return onlyNumbers(event)',
    ]);
    ?>
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="form-normal" style="margin: 0px !important">
    <?php
    //TODO NKL
    echo $this->render('/sms-token/securityQuestionPopupHtml', [
        'requestType' => 'verify_phone_request',
        'service_id' => isset($fq_arr["service"]) ? $fq_arr["service"] : NULL,
        'tokenMsg' => $token->getFirstError('token_answer'),
        'token' => $token->token_answer,
        'contact_no_dialing_code_id_field' => 'customerssetupform-customers_country_dialing_code_id',
        'contact_no_field' => 'customerssetupform-customers_telephone']);
}
?>
    </div>
    <div class="clearfix"></div>
    <div class="input-row"></div>
    <div class="text-center">
        <a class="btn btn-default" href="<?php echo \yii\helpers\Url::to(['sso/logout']); ?>"><?php echo Yii::t('general', 'BTN_CANCEL'); ?></a>
        &nbsp;&nbsp;
         <button type="submit" class="btn-submit"><?php echo Yii::t('sso', 'SAVE'); ?></button>
    </div>
<?php $form->end(); ?>
</div>