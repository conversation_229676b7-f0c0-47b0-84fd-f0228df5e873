<?php
/* @var $this \yii\web\View */

if (isset($fq_arr['service'])) {
    $this->params['service_id'] = $fq_arr['service'];
}
?>
<div class="card-title">
    <span style="text-align: center">
        <h2><?php echo Yii::t('sso', 'HEADER_SIGNUP_SSO', array("SYS_SITE_NAME" => Yii::$app->name)); ?></h2>
    </span>
</div>
<?php
echo frontend\widgets\hybridauth\RenderProviders::widget(array_merge([
    'url' => \yii\helpers\Url::to(array_merge(['sso/xpress', 'ha_provider' => '{PROVIDER}'], $fq_arr)),
    'display' => 'vertical',
    'item_space_right' => 5,
    'template_items' => '{TEMPLATE_ITEM}',
    'template_item' => '<div class="btn-spot-special"><button class="btn-{LOWERCASE_PROVIDER}" id="ha-{PROVIDER}" onclick="{ON_CLICK}" ><div class="icon-socialconnect"><span class="fa fa-{PROVIDER_FONTAWESOME}"></span></div> ' . Yii::t('socialConnect', 'BTN_CONNECT_WITH_SNS', array('SYS_PROVIDER' => '{PROVIDER}')) . '</button></div>',
], $is_mode_launcher ? [
    'popup' => true,
] : []));
?>
<!-- OR -->
<?php if($sns > 0){ ?>
        <div class="partition"><div class="partition-ball">OR</div></div>
<?php } ?>
<?php
$form = frontend\widgets\ActiveForm::begin([
    'action' => \yii\helpers\Url::to(['sso/sign-up'], true) . (!empty($f_query) ? $f_query : ''),
]);
echo $form->field($model, 'customers_email_address')->textInput([
    'placeholder' => Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
    'value' => (isset($mail) ? $mail : '')
]);
echo $form->field($model, 'customers_password')->passwordInput([
    'placeholder' => Yii::t('sso', 'ENTRY_PASSWORD'),
    'value' => '',
]);
?>
<div class="space-10 hidden-xs"></div>
<?php if (isset($captcha)) { ?>
    <?php if (!empty($captcha['note'])) { ?>
    <div class="row text-center">
        <?php echo $captcha['note']; ?>
        <div class="space-20"></div>
    </div>
    <?php
    }
    echo $captcha['html'];
    if (!empty($captcha['js'])) {
        $this->registerJs($captcha['js'], yii\web\View::POS_READY);
    }
    ?>
<div class="space-20 hidden-xs"></div>
<div class="space-10 visible-xs"></div>
<?php } ?>
<div class="clearfix">
    <div class="btn-spot">
        <button type="submit" class="btn-submit-full" name="prev" value="<?php echo (isset($prev_mail) ? $prev_mail : ''); ?>"><?php echo Yii::t('sso', 'BUTTON_SIGNUP'); ?></button>
        <div class="input-row-text-full">
            <div class="input-row"></div>
            <?php
            echo Yii::t('sso', 'TEXT_TOS', array(
                'SYS_TERMS_OF_SERVICE' => \yii\helpers\Html::a(Yii::t('site', 'LINK_TERM_CONDITION'), Yii::$app->params['OG_URL'] .'/terms-of-service', ["target" => "_blank"]),
                'SYS_PRIVACY_POLICY' => \yii\helpers\Html::a(Yii::t('site', 'LINK_PRIVACY'), Yii::$app->params['OG_URL'] .'/privacy-policy', ["target" => "_blank"]),
            ));
            ?>
        </div>
    </div>
</div>
<?php $form->end(); ?>
<p><?php echo Yii::t('sso', 'TEXT_MEMBER_ALREADY'); ?> <?php echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOGIN_HERE') . ' &rarr;', \yii\helpers\Url::to(['sso/login']) . $f_query); ?></p>
