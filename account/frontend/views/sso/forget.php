<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);
\frontend\assets\FlagsAsset::register($this);

// 3rd party URL query
$fq_arr = array();
$tmp_arr = array();
if (isset($returnUrl) && !empty($returnUrl)) {
    parse_str($returnUrl, $tmp_arr);
    $f_query = '?' . $returnUrl;
    // 3rd party portal URL query
    if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
        $origin = urldecode(urldecode($tmp_arr["origin"]));
        $fq_arr = $tmp_arr;
    }
} else {
    $f_query = '';
}

$service_id = isset($fq_arr["service"]) ? $fq_arr["service"] : null;
$this->params['service_id'] = $service_id;
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center;">
            <h2><?php echo $logged_in ? Yii::t("sso", "HEADER_RESET_FORGOT_PASSWORD") : Yii::t("sso", "HEADER_PROBLEM_LOGIN"); ?></h2>
        </span>
    </div>

    <div class="input-row"></div>

    <!-- FORGOT PASSWORD -->
    <p>
        <strong><?php echo Yii::t("sso", "TEXT_RESET_FORGOT_PASSWORD"); ?></strong> - <?php echo Yii::t("sso", "TEXT_RESET_FORGOT_PASSWORD_NOTE"); ?>
    </p>
    <?php
    $form1 = \frontend\widgets\ActiveForm::begin();
    echo $form1->field($model_passwd, 'email')->textInput([
        'placeholder' => Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
        'autocomplete' => 'off',
        'value' => '',
    ]);
    ?>
    <div class="btn-spot">
        <button name="submit_passwd" type="submit" class="btn-default-full"><?php echo Yii::t('general', 'BTN_SUBMIT'); ?></button>
    </div>
    <?php \frontend\widgets\ActiveForm::end(); ?>
    
    <?php if (!$logged_in && !isset(Yii::$app->request->cookies['safm'])) { ?>
        <!-- OR -->
        <div class="partition"><div class="partition-ball">OR</div></div>

        <?php if ($phone_owner) { ?>
        <p><?php echo Yii::t("sso", "TEXT_FORGOT_EMAIL_ADDRESS_SUCCESS", ["SYS_SITE_NAME" => Yii::$app->name]); ?></p>
        <p class="text-center"><b><?php echo $phone_owner; ?></b></p>
        <?php } else { ?>
        <!-- FORGOT EMAIL ADDRESS -->
        <p>
            <strong><?php echo Yii::t("sso", "TEXT_FORGOT_EMAIL_ADDRESS"); ?></strong> - <?php echo Yii::t("sso", "TEXT_FORGOT_EMAIL_ADDRESS_NOTE"); ?>
        </p>
        <?php
        $form2 = \frontend\widgets\ActiveForm::begin([
            'id' => 'forget-mail',
        ]);
        ?>
        <div class="input-row-2-coll">
            <?php 
            echo $form2->field($model_mail, 'dial_ctry_code')->widget(\conquer\select2\Select2Widget::className(), [
                'placeholder' => Yii::t('general', 'TEXT_PLEASE_SELECT'),
                'bootstrap' => false,
                'items' => \yii\helpers\ArrayHelper::map($ctry, 'countries_id', function($array) {
                    return "<span class='flag flag-" . strtolower($array['countries_iso_code_2']) . "'></span> +$array[countries_international_dialing_code] <span class='select2-hide-in-selected'>$array[countries_name]</span>";
                }),
                'settings' => [
                    'escapeMarkup' => new yii\web\JsExpression('function(text) { return text; }'),
                ],
            ]);
            ?>
        </div>
        <div class="input-row-2-coll">
            <div class="input-row-2-coll-right">
                <?php
                echo $form2->field($model_mail, 'phone')->textInput([
                    'placeholder' => Yii::t('sso', 'TEXT_MOBILE_NUMBER'),
                    'autocomplete' => 'off',
                    'value' => '',
                    'onkeypress' => 'return onlyNumbers(event)',
                ]);
                ?>
            </div>
        </div>
        <div class="btn-spot">
            <button name="submit_mail" type="submit" class="btn-default-full"><?php echo Yii::t('general', 'BTN_SUBMIT'); ?></button>
        </div>
        <?php \frontend\widgets\ActiveForm::end(); ?>
        <?php } ?>
    <?php } ?>
    <?php if (!$logged_in && !$phone_owner) { ?>
        <!-- OR -->
        <div class="partition"><div class="partition-ball">OR</div></div>
        
        <!-- FORGOT MOBILE PHONE NUMBER -->
        <p>
            <?php if (isset(Yii::$app->request->cookies['safm'])) { ?>
                <strong><?php echo Yii::t("sso", "TEXT_FORGOT_EMAIL_ADDRESS"); ?></strong>
            <?php } else { ?>
                <strong><?php echo Yii::t("sso", "TEXT_FORGOT_MOBILE_NUMBER"); ?></strong>
            <?php } ?>
        </p>
        
        <div class="btn-spot">
            <?php
            if (isset(\Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$service_id]['pre_login_support_url'])) {
                echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_CONTACT_SUPPORT'), \Yii::$app->params['MERCHANT_CONFIG_OVERWRITE'][$service_id]['pre_login_support_url'], ["target" => "_blank", "class" => "btn btn-default-full"]);
            } else {
                echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_CONTACT_SUPPORT'), \Yii::$app->params['pre_login_support_url'], ["target" => "_blank", "class" => "btn btn-default-full"]);
            }
            ?>
            
        </div>
        <div class="input-row"></div>
    <?php } ?>
</div>
<p>
    <?php
    if ($logged_in) {
        echo \yii\helpers\Html::a(Yii::t('sso', 'TEXT_SIGNUP_RETURN_PREVIOUS'), $returnUrl);
    } else {
        echo Yii::t('sso', 'TEXT_RETURN_TO', array('SYS_LOGIN' => \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOG_IN'), $returnUrl)));
    }
    ?>
</p>