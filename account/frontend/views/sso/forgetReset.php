<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>

<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center">
            <h2><?php echo Yii::t('sso', 'HEADER_RECOVER_NEW_PASSWORD'); ?></h2>
        </span>
    </div>

    <?php
    $form = \frontend\widgets\ActiveForm::begin();
    echo $form->field($model, 'customers_password')->passwordInput([
        'placeholder' => Yii::t('sso', 'ENTRY_NEW_PASSWORD'),
        'autocomplete' => 'off',
        'value' => '',
    ]);
    echo $form->field($model, 'confirm_password')->passwordInput([
        'placeholder' => Yii::t('sso', 'ENTRY_NEW_PASSWORD_CONFIRM'),
        'autocomplete' => 'off',
        'value' => '',
    ]);
    ?>
    
    <div class="btn-spot">
        <button type="submit" name="code" id="code" value="<?php echo htmlspecialchars($code); ?>" class="btn-default-full"><?php echo Yii::t('general', 'BTN_SUBMIT'); ?></button>
    </div>
    
    <?php $form->end(); ?>
</div>
<p><?php echo Yii::t('sso', 'TEXT_RETURN_TO', array('SYS_LOGIN' => \yii\helpers\Html::a(Yii::t('sso', 'TEXT_LOG_IN'), ['sso/login']))); ?></p>