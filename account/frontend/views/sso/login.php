<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);

// 3rd party URL query
$fq_arr = array();
if (isset($f_query) && !empty($f_query)) {
    $tmp_arr = array();
    parse_str($f_query, $tmp_arr);
    $f_query = '?' . $f_query;

    // 3rd party portal URL query
    if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
        $origin = urldecode(urldecode($tmp_arr["origin"]));
        if (\common\components\SSOCom::clientURLMatch($tmp_arr["service"], $origin)) {
            $fq_arr = $tmp_arr;
        }
    }
}
if (!isset($fq_arr['mode']) && isset($_GET['mode'])) {
    $fq_arr['mode'] = $_GET['mode'];
}
?>
<!-- Just to load FA web font -->
<i class="fa fa-gear fa-5x" style="position:absolute;left:-200px;top:0px;"></i>
<?php
echo $this->render('_login_' . (!$is_mode_launcher ? 'normal' : 'launcher'), [
    'fq_arr' => $fq_arr,
    'f_query' => $f_query,
    'mail' => !empty($mail) ? $mail : null,
    'model' => $model,
    'sns' => $sns,
    'is_mode_launcher' => $is_mode_launcher,
]);