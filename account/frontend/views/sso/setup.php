<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);

// 3rd party URL query
$fq_arr = array();
if (isset($f_query) && !empty($f_query)) {
    $tmp_arr = array();
    parse_str($f_query, $tmp_arr);
    $f_query = '?' . $f_query;

    // 3rd party portal URL query
    if (isset($tmp_arr["service"]) && isset($tmp_arr["origin"])) {
        $origin = urldecode(urldecode($tmp_arr["origin"]));
        if (\common\components\SSOCom::clientURLMatch($tmp_arr["service"], $origin)) {
            $fq_arr = $tmp_arr;
        }
    }
}
?>
<!-- Just to load FA web font -->
<i class="fa fa-gear fa-5x" style="position:absolute;left:-200px;top:0px;"></i>
<?php
echo $this->render('_setup-account' , [
    'f_query' => $f_query,
    'model' => $model,
    'allCountry'=> $allCountry,
    'token' =>$token,
    'info_verified'=> $info_verified,
    'fq_arr' =>$fq_arr,
 ]);