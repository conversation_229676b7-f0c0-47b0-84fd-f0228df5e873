<?php
/* @var $this \yii\web\View */

// 3rd party URL query
$fq_arr = array();
if (!empty($f_query)) {
    parse_str($f_query, $fq_arr);
}
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center">
            <h2><?php echo Yii::t('sso', 'HEADER_LOGIN_SOCIAL_NETWORK'); ?> <?php echo $sns; ?></h2>
        </span>
    </div>

    <!-- social network -->
    <div class="avatar-profile-fb">
        <div class="avatar">
            <?php
            if ($photo) {
                echo \yii\helpers\Html::img($photo, ['width' => '60px']);
            } else {
                echo \yii\helpers\Html::tag('span', ['class' => 'fa fa-user', 'style' => 'font-size: 60px;']);
            }
            ?>
        </div>
        <div class="avatar-text">
            <span class="avatar-text-small">
                <?php
                echo Yii::t('sso', 'TEXT_XPRESS_NOTE', array(
                    "SYS_USERNAME" => \yii\helpers\Html::a(implode(" ", array($fname, $lname)), "#"),
                    "SYS_SITE_NAME" => Yii::$app->name)
                );
                ?>
            </span>
        </div>
    </div>
    <?php echo \yii\helpers\Html::beginForm(array_merge(['sso/xpress', 'ha_provider' => $sns], $fq_arr), 'post', ['class' => 'form']); ?>
    <label class="custom-radio"> <b><?php echo Yii::t('sso', 'TEXT_XPRESS_OPTION_NO', array("SYS_SITE_NAME" => Yii::$app->name)); ?></b><br /><?php echo Yii::t('sso', 'TEXT_XPRESS_OPTION_NO_2', array("SYS_SITE_NAME" => Yii::$app->name)); ?>
    <input type="radio" name="login_method" id="opt1" value="signup" checked="checked">
    <span class="checkmark-radio"></span>
    </label>
    <label class="custom-radio"> <b><?php echo Yii::t('sso', 'TEXT_XPRESS_OPTION_YES', array("SYS_SITE_NAME" => Yii::$app->name, "SYS_SOCIAL_CONNECT" => $sns)); ?></b><br /><?php echo Yii::t('sso', 'TEXT_XPRESS_OPTION_YES_2', array("SYS_SITE_NAME" => Yii::$app->name, "SYS_SOCIAL_CONNECT" => $sns)); ?>
    <input type="radio" name="login_method" id="opt2" value="login">
    <span class="checkmark-radio"></span>
    </label>
    <div class="btn-spot">
        <button type="submit" class="btn-default-full">NEXT &rarr;</button>
    </div>
    <?php echo \yii\helpers\Html::endForm(); ?>
</div>