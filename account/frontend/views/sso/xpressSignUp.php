<?php
/* @var $this \yii\web\View */

\frontend\assets\FormAsset::register($this);
?>
<div class="logo">
    <?php
    $logo = "brand";
    echo '<div class="sa-logo sa-logo-ogm"></div>';
    ?>
</div>
<div class="card-small">
    <!-- Title -->
    <div class="card-title">
        <span style="text-align: center">
            <h2><?php echo Yii::t('sso', 'HEADER_LOGIN_SOCIAL_NETWORK'); ?> <?php echo $sns; ?></h2>
        </span>
    </div>

    <?php
    $form = \frontend\widgets\ActiveForm::begin([
        'action' => \yii\helpers\Url::to(['sso/xpress'], true) . (isset($f_query) ? '?' . $f_query : ''),
    ]);
    echo \yii\helpers\Html::hiddenInput('login_method', $login_method);
    echo $form->field($model, 'customers_email_address')->textInput([
        'placeholder' => Yii::t('sso', 'ENTRY_EMAIL_ADDRESS'),
        'value' => (isset($mail) ? $mail : '')
    ]);
    echo $form->field($model, 'customers_password')->passwordInput([
        'placeholder' => Yii::t('sso', 'ENTRY_CREATE_PASSWORD', [
            'SYS_NAME' => \Yii::$app->name,
        ]),
    ]);
    ?>
    <div class="clearfix">
        <div class="btn-spot">
            <button type="submit" class="btn-submit-full"><?php echo Yii::t('sso', 'BUTTON_SIGNUP'); ?></button>
            <div class="input-row-text-full">
                <div class="input-row"></div>
                <?php
                echo Yii::t('sso', 'TEXT_TOS', array(
                    'SYS_TERMS_OF_SERVICE' => \yii\helpers\Html::a(Yii::t('site', 'LINK_TERM_CONDITION'), Yii::$app->params['OG_URL'] .'/terms-of-service', ["target" => "_blank"]),
                    'SYS_PRIVACY_POLICY' => \yii\helpers\Html::a(Yii::t('site', 'LINK_PRIVACY'), Yii::$app->params['OG_URL'] .'/privacy-policy', ["target" => "_blank"]),
                ));
                ?>
            </div>
        </div>
    </div>
    <?php $form->end(); ?>
</div>