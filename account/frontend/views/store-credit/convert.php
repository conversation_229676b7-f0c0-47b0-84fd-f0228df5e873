<?php
use \yii\helpers\Html;

/* @var $this \yii\web\View */

\frontend\assets\StoreCreditAsset::register($this);

$js = <<<JS
    store_credit.convertCurrencyInit();
JS;
$this->registerJs($js);
?>

<div class="page-title">
    <h1><?php echo Yii::t('storeCredit', 'TITLE_CONVERT_STORE_CREDITS'); ?></h1>
</div>

<!-- CONVERT -->
<div class="card">
    <?php
    if (!$sc_set) {
        echo \frontend\helpers\Html::tag('p', Yii::t('storeCredit', 'TEXT_CONVERT_NO_SC') . ' ' . frontend\helpers\Html::a(Yii::t('storeCredit', 'LINK_TOPUP_STORE_CREDITS'), ['store-credit/index']));
    } else if ($sc_pending_payment) {
        echo \frontend\helpers\Html::tag('p', Yii::t('storeCredit', 'ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND'));
    } else {
        $form = \frontend\widgets\ActiveForm::begin([
            'action' => \yii\helpers\Url::to(['store-credit/convert'], true) . (isset($req_arr) ? "?" . http_build_query($req_arr) : ""),
            'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
        ]);
        ?>
        <div class="input-row-2-coll">
            <div class="input-row-2-coll-left">
                <label><?php echo Yii::t('storeCredit', 'ENTRY_MY_CREDIT_BALANCE'); ?></label>
                <input type="text" placeholder="<?php echo sprintf('%s %s %s', $cur_symbol_left, $sc_balance, $cur_symbol_right); ?>" disabled>
            </div>
        </div>

        <div class="input-row-2-coll">
            <label for="#"><?php echo Yii::t('storeCredit', 'ENTRY_CONVERT_TO'); ?></label>
            <?php
            echo \conquer\select2\Select2Widget::widget([
                'bootstrap' => false,
                'name' => 'currency',
                'items' => $currencyList,
                'options' => [
                    'prompt' => Yii::t('general', 'COMBOBOX_SELECT'),
                    'data' => [
                        "calculation-url" => \yii\helpers\Url::to(['store-credit/calculate-conversion']),
                    ],
                ],
            ]);
            ?>
        </div>
        
        <div class="collapse-box" id="convert-detail">
            <!-- Partition -->
            <div class="partition-dashed"></div>
            
            <div id="convert-in-progress" class="text-center ajax-loading-area" style="height: 100px;">
                <i class="fa fa-3x fa-spin fa-circle-o-notch icon-loading"></i>
            </div>

            <div id="convert-detail-result" class="hide">
                <!-- CALCULATION -->
                <ul class="calculation">
                    <li class="cal-row">
                        <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('storeCredit', 'TEXT_VALUE_AFTER_CONVERSION'); ?>:<br>
                            <small class="cal-row-label-text-small" id="convert-detail-result-rate"></small>
                        </span></span>
                        <span class="cal-row-value"><strong class="cal-row-value-text" id="convert-detail-result-value"></strong></span>
                    </li>
                </ul>

                <div class="btn-spot">
                    <button type="submit" class="btn-submit"><?php echo Yii::t('storeCredit', 'BTN_CONVERT_NOW'); ?></button>
                </div>
            </div>
        </div>
        <?php
        \frontend\widgets\ActiveForm::end();
        ?>
    <?php
    }
    ?>
</div>