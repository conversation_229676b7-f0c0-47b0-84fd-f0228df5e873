<?php

use \yii\helpers\Html;

/* @var $this \yii\web\View */
/* @var $from_cur string */
/* @var $to_cur string */

\frontend\assets\StoreCreditAsset::register($this);
?>

<!-- CONVERT -->
<div class="card">
    <div class="input-row-text">
        <span class="icon-success"><i class="fa fa-check-circle"></i></span>
        <h1><?php echo Yii::t('storeCredit', 'HEADER_CONVERT_SUCCESSFUL'); ?></h1>
    </div>

    <div class="form-normal">
        <!-- CALCULATION -->
        <ul class="calculation">
            <li class="cal-row">
                <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('storeCredit', 'ENTRY_MY_CREDIT_BALANCE'); ?>:</span></span>
                <span class="cal-row-value"><strong class="cal-row-value-text"><?php echo $new_bal; ?></strong></span>
            </li>
        </ul>
        <div class="btn-spot">
            <?php echo Html::a(Yii::t('storeCredit', 'LINK_VIEW_STATEMENT'), ['store-credit/statement'], ['class' => 'btn btn-default']); ?>
        </div>
    </div>
</div>