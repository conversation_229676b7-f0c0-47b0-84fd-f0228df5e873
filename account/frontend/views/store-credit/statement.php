<?php

use yii\helpers\Html;
use common\components\StoreCreditCom;

/* @var $this \yii\web\View */
?>
<div class="page-title">
    <h1><?php echo Yii::t('storeCredit', 'TITLE_STORE_CREDITS_STATEMENT'); ?></h1>
</div>
<div class="page-description-table">
    <p><?php echo Yii::t('storeCredit', 'TITLE_STORE_CREDIT_BALANCE'); ?> : <b class="page-description-highlight"><?php echo $balance; ?></b></p>
    
    <div class="page-description-right">
        <form class="form">
            <div class="statement-date-selection">
                <?php echo \conquer\select2\Select2Widget::widget([
                    'name' => 'date',
                    'items' => $dates,
                    'bootstrap' => false,
                    'value' => $date,
                    'events' => [
                        'change' => 'function() {jQuery(this).closest("form").submit(); }',
                    ],
                ]); ?>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($data)) { ?>
    <div id="card-table">
        <!-- TABLE -->
        <div class="table">
            <div class="table-head">
                <div class="column-table-head" data-label="<?php echo Yii::t('storeCredit', 'TITLE_DATE'); ?>"><?php echo Yii::t('storeCredit', 'TITLE_DATE'); ?></div>
                <div class="column-table-head" data-label="<?php echo Yii::t('storeCredit', 'TITLE_DESCRIPTION'); ?>"><?php echo Yii::t('storeCredit', 'TITLE_DESCRIPTION'); ?></div>
                <div class="column-right" data-label="<?php echo Yii::t('storeCredit', 'TITLE_AMOUNT'); ?>"><?php echo Yii::t('storeCredit', 'TITLE_AMOUNT'); ?></div>
                <div class="column-hide" data-label="<?php echo Yii::t('storeCredit', 'TITLE_BALANCE'); ?>"><?php echo Yii::t('storeCredit', 'TITLE_BALANCE'); ?></div>
            </div>
            <?php foreach ($data as $num => $val) { ?>
                <div class="row">
                    <div class="column" data-label="<?php echo Yii::t('storeCredit', 'TITLE_DATE'); ?>">
                        <?php $_date = strtotime($val["date"]);
                        if (date('Y') == date('Y', $_date)) {
                                if (mktime(0, 0, 0, date("m"), date("d"), date("Y")) == mktime(0, 0, 0, date("m", $_date), date("d", $_date), date("Y", $_date))) {
                                    echo date("h:i a", $_date);
                                } else {
                                    echo date("d M Y", $_date);
                                }
                            } else {
                                echo date("d/m/y", $_date);
                            }
                         ?>
                    </div>
                    <div class="column" data-label="<?php echo Yii::t('storeCredit', 'TITLE_DESCRIPTION'); ?>">
                        <?php echo $val["desc"]; ?>
                    </div>
                    <div class="column-right" data-label="<?php echo Yii::t('storeCredit', 'TITLE_AMOUNT'); ?>">
                        <?php if (empty($val["balance"])) { ?>
                            <span class="td-debit"> 
                                <?php echo " - " . $val["debit"]; ?>
                            </span>
                        <?php } else {
                            echo $val["credit"];
                        } ?>
                    </div>
                    <div class="column-hide" data-label="<?php echo Yii::t('storeCredit', 'TITLE_BALANCE'); ?>">
                        <?php echo $val["balance"]; ?>
                    </div>
                    <div class="col-sm-12">
                        <div class="line"></div>
                    </div>
                </div>
            <?php } ?>
        </div>
        <!-- PAGINATION -->
        <div class="pagination">
            <?php echo $this->render('/site/pagination', $pagination); ?>
        </div>
    </div>
<?php } else { ?>
<div class="card">
    <p><?php echo Yii::t('storeCredit', 'TEXT_NO_STATEMENT', array('SYS_LINK' => Yii::$app->urlManager->createAbsoluteUrl(['store-credit/index']), 'SYS_DATE' => date('M Y', $date))); ?></p>
</div>
<?php }