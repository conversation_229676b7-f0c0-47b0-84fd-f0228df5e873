<?php

use \yii\helpers\Html;

/* @var $this \yii\web\View */
/* @var $from_cur string */
/* @var $to_cur string */

\frontend\assets\StoreCreditAsset::register($this);
?>

<!-- TOPUP -->
<div class="card">
    <div class="input-row-text">
        <span class="icon-success"><i class="fa fa-check-circle"></i></span>
        <h1><?php echo Yii::t('storeCredit', 'HEADER_TOP_UP_SUCCESS'); ?></h1>
        <p>
            <?php echo Yii::t('storeCredit', 'TEXT_ORDER_ID'); ?>
            <b><?php echo $tid; ?></b>
        </p>
    </div>

    <div class="form-normal">
        <!-- CALCULATION -->
        <ul class="calculation">
            <li class="cal-row">
                <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('storeCredit', 'TEXT_TOP_UP_AMOUNT'); ?>:</span></span>
                <span class="cal-row-value"><span class="cal-row-value-text"><?php echo $topup_amt; ?></span></span>
            </li>
            <li class="cal-row">
                <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('storeCredit', 'ENTRY_MY_CREDIT_BALANCE'); ?>:</span></span>
                <span class="cal-row-value"><strong class="cal-row-value-text"><?php echo $new_bal; ?></strong></span>
            </li>
        </ul>
        <div class="btn-spot">
            <?php echo Html::a(Yii::t('storeCredit', 'BTN_VIEW_ORDER_DETAIL'), sprintf(Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'], $tid), ['class' => 'btn btn-default']); ?>
        </div>
    </div>
</div>