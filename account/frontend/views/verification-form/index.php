<?php

use \yii\helpers\Html;
use yii\helpers\Url;
use common\components\PipwaveLanguage;

/* @var $this \yii\web\View */
$base_url = Url::base('https');
$image_selected_text =  Yii::t('verificationSubmission', 'IMAGE_SELECTED');
$review_noted =  Yii::t('verificationSubmission', 'REVIEW_NOTE',['CONTACT_TO' => \Yii::$app->params['og_support_url'], 'ORDER_LIST' => \Yii::$app->params['og_order_list']]);
$lang = PipwaveLanguage::convert(\Yii::$app->language);
$js = <<<JS
        sdk_click = function(type,me){


            $.post('$base_url/verification-form/get-verification-token',{ver_type:type,brand:'$brand',order_id:'$order_id'}).done(function(data){
                tmpData = JSON.parse(data);

                if (typeof(tmpData.error) != 'undefined' ){
                    toastr["error"](tmpData.error);
                    return;
                }

                reqPwDoc.init('pwdocscript', '$api_key', tmpData.token, type, {
                'lang':'$lang',
                'on_finish_callback': function(type, result) {
                    $("#pwdoc-loading").hide();
                    $("#pwdoc-text").removeClass('hidden');

                    $.post('$base_url/verification-form/verification-callback',{ token:tmpData.token, status:result }).done(function(data){

                        data = JSON.parse(data)
                        $(me).parent().html('<label class="btn btn-default custom-fileinput review-label"><p class="upload_status" style="font-size: 12px;"><b>'+ data.label +'</b></p></label><label><div class= "review-note">'+'$review_noted'+'</div></label>');

                    });

                },
            });
            });
        }

        $(".upload-003").click(function(){
            sdk_click("identification",this)
        });
        $(".upload-004").click(function(){
            sdk_click("credit-card", this)
        });

        $(".upload-001").click(function(){
            sdk_click("selfie", this)
        });

        $("input[name^=doc_]").change(function() {
        //Here is where you will make your AJAX call
            $(this).parent().hide();
            $(this).parent().after('<p class="upload_status"><b>$image_selected_text</b></p>');

        });
JS;
$this->registerJs($js);
?>
<script src="<?php echo \Yii::$app->params['pwapi.sdk_url']; ?>"></script>
<style>

    ol.list-no-margin {
        padding-left: 2em;
    }
    
    .text-blue{
        color:#00A4FF;
    }

    .card-description-right {
        margin-bottom: 10px;
        margin-top: -20px;
        text-align: right;
        float: right;
        width: 30%;
    }

    .card-description-left {
        margin-bottom: -30px;
        margin-top: -40px;
    }

    .custom-fileinput{
        margin-top: 0;
        display: inline-block;
        text-align:  center;
    }
    .upload_status{
        margin: 0;
        margin-top: 0.55em;
        color:#999DA8;
    }
    .review-label{
        background-color: #cccccc;
        color: #6ec8c7;
        border: 1px solid #cccccc;
        display: inline-block;
        padding: 10px;
    }
    .btn-right{
            display: inline-block;
            position: absolute;
            top: 80px;
            right: 30px;
    }
    .review-note{
        font-size: 12px;
        margin-top: -5px;
        color: #a2a2a2;
        text-align: center;
        width: 70%;
        float: right;
    }


    @media screen and (max-width : 460px){
        .card-description-right {
            margin-bottom: 10px;
            text-align: right;
            float: none !important;
            width: auto !important;
            padding-right: 30px;
            margin-top: -15px;
            }
        .card-description-left {
            margin-bottom: 10px;
            margin-top: -40px;
            text-align: left;
            width: auto;
        }
        .review-label{
        background-color: #cccccc;
        color: #6ec8c7;
        border: 1px solid #cccccc;
        display: inline-block;
        padding: 10px;
        text-align: center;
        }
        .btn-right{
            display: flow-root;
            position: initial;
            top: 80px;
            right: 30px;
        }
        .review-note{
        font-size: 12px;
        margin-top: -5px;
        color: #a2a2a2;
        text-align: center;
        width: 100%;
        float: none;
    }

    }
    @media screen and (max-width : 600px){
        .card-description-right {
        margin-bottom: 10px;
        text-align: right;
        float: none !important;
        width: auto !important;
        padding-right: 30px;
        margin-top: -15px;
        }
        .card-description-left {
            margin-bottom: 10px;
            margin-top: -40px;
            text-align: left;
            width: auto;
        }
        .review-label{
        background-color: #cccccc;
        color: #6ec8c7;
        border: 1px solid #cccccc;
        display: inline-block;
        padding: 10px;
        text-align: center;
        }
        .custom-fileinput{
        margin-top: -15px;
        display: inherit !important;
        text-align:  center;
        }
        .btn-right{
            display: flow-root;
            position: initial;
            top: 80px;
            right: 30px;
        }
        .review-note{
        font-size: 12px;
        margin-top: -5px;
        color: #a2a2a2;
        text-align: center;
        width: 100%;
        float: none;
    }
    }

</style>

<div class="page-title">
    <h1> <?php echo Yii::t('verificationSubmission', 'TITLE_VERIFICATION_SUBMISSION_FORM'); ?> <?php echo $brand_label; ?></h1>
</div>
<div class="page-description">
    <p><?php echo Yii::t('verificationSubmission', 'TEXT_TAB_SUB_TITLE'); ?></p>
</div>

<?php $flash = Yii::$app->session->getAllFlashes(); ?>
<?php if ($require || !empty($flash)) { ?>
        <?php echo \frontend\helpers\Html::beginForm(["verification-form/upload"], "post", ["enctype" => "multipart/form-data"]); ?>
        <?php $record = false; ?>
        <?php

        if (array_search(0,$files)) {
        foreach ($files as $num => $val) {
            if ($val == 1) {
                continue;
            }
            ?>
        <?php if (!$val || in_array($num, [001,003, 004]) || isset($flash["success-" . $num])) {
            ?>
    <div class="card">
                <div class="card-description-left">

                    <div class="form-normal nomargin">
            <?php if (isset($flash["success-" . $num])) { ?>
                            <div class="alert alert-success" role="alert">
                                <b><?php echo Yii::t('general', 'TEXT_SUCCESS'); ?></b> <?php echo $flash["success-" . $num]; ?>
                            </div>
                        <?php } else if (isset($flash["danger-" . $num])) { ?>
                            <div class="alert alert-danger" role="alert">
                                <b><?php echo Yii::t('general', 'TEXT_DANGER'); ?></b> <?php echo $flash["danger-" . $num]; ?>
                            </div>
            <?php } ?>
                        <?php if (in_array($num, [001,003, 004])) { ?>
                        <!--<small><i><?php echo Yii::t("verificationSubmission", "TEXT_REMARK_UPLOAD_SIZE_LIMIT"); ?></i></small>-->
                            <p><?php echo Yii::t("verificationSubmission", "ENTRY_" . $num); ?>
                            <?php echo Yii::t("verificationSubmission", "INST_" . $num, array("SYS_FILE_SIZE" => Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"])); ?>
                            </p>

            <?php } else {
                ?>
                                        <!--<small><i><?php echo Yii::t("verificationSubmission", "TEXT_REMARK_UPLOAD_SIZE_LIMIT"); ?></i></small>-->
                            <p><?php echo Yii::t("verificationSubmission", "ENTRY_" . $num); ?> <a class="btn-expand-collapse text-blue" data-target="verification-instruction-<?php echo $num ?>-disabled" data-expandlabel="<?php echo Yii::t('verificationSubmission', 'READ_GUIDE'); ?>" data-collapselabel="<?php echo Yii::t('verificationSubmission', 'CLOSE_GUIDE'); ?>"  ><?php echo Yii::t('verificationSubmission', 'READ_GUIDE'); ?></a>

                            <div class="collapse-box" id="verification-instruction-<?php echo $num ?>-disabled">
                                <div class="collapse-content">
                            <?php echo Yii::t("verificationSubmission", "INST_" . $num, array("SYS_FILE_SIZE" => Yii::$app->params["VERIFICATION_DOC"]["FILE_MAXSIZE"])); ?>
                                </div>
                            </div>
                            </p>

                <?php }
            ?>
                    </div>
                </div>
                <div class="card-description-right btn-right">
                    <?php if (!in_array($num, [001,003, 004])) { ?>
                        <label class="btn btn-default custom-fileinput"><?php echo \frontend\helpers\Html::fileInput("doc_" . $num, null, ["style" => "display:none"]); ?><?php echo Yii::t('general', 'BTN_UPLOAD') ?></label>
                    <?php } else {  ?>
                        <?php if (!$val && in_array($status_code[$num], [0,4]) ) { ?>
                            <label  class="btn btn-submit custom-fileinput  upload-<?php echo $num ?>"><?php echo Yii::t('general', 'BTN_VERIFY_NOW') ?> </label>
                <?php } else { ?>
                            <p class="upload_status"><b><?php echo $status[$num]; ?></b></p>
                            <?php } ?>
                        <?php } ?>
                </div>
                </div>
               <?php
                $record = true;
            }
            ?>
    <?php }
        }else{
            echo Yii::t("verificationSubmission", "NO_DOC");
        }
    ?>

        <?php if ($show_submit) { ?>
            <div class="btn-spot-special">
                <button type="submit" class="btn-submit"><?php echo Yii::t("general", "BTN_SUBMIT"); ?></button>
            </div>
    <?php } ?>
    <?php echo \frontend\helpers\Html::endForm(); ?>

    <div id="pwdocscript"></div>
    <?php
}else{ ?>
    <div class="card">
            <?php echo Yii::t("verificationSubmission", "NO_DOC"); ?>
    </div>
<?php } ?>
<?php  echo Yii::t('dev', "FRESHDESK_CHAT"); ?>