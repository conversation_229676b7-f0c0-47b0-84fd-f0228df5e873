<?php

use yii\helpers\Html;
use frontend\widgets\ActiveForm;

/* @var $this \yii\web\View */

\frontend\assets\WorAsset::register($this);

if ($diff <= 0) {
    $redeem_into_url = \yii\helpers\Url::to(['op/redeem-into']);
    $js = <<<JS
        wor.init('$curr', '$redeem_into_url');

        $('#frm-redeem').on('beforeSubmit', function(){
            if ($(this).data('requestRunning')) {
                return false;
            }
            $(this).data('requestRunning', true);
            return true;
        });
JS;
    $this->registerJs($js);
}
?>
<div class="page-title"><h1><?php echo Yii::t('wor', 'TITLE_OP'); ?></h1></div>
<div class="page-description">
    <p class="wor-space-bottom-20">
        <?php echo Yii::t('wor', 'MSG_DEFINE_OP', array('SYS_READ_MORE' => Html::a(Yii::t('wor', 'TEXT_READ_MORE'), Yii::t('wor', 'LINK_READ_MORE'), array("target" => "_blank")))); ?>
    </p>
</div>
<div class="card">
    <!-- REDEEM -->
    <?php
        $form = \frontend\widgets\ActiveForm::begin([
            'id' => 'frm-redeem',
            'options' => ['class' => 'form-normal', 'autocomplete' => 'off'],
        ]);
        ?>

        <label><?php echo Yii::t('wor', 'TITLE_OP_BALANCE'); ?></label>
        <input type="text" placeholder="<?php echo $token_display; ?>" disabled>
        <div class="input-row-text-full"><i class="fa fa-info-circle"></i> <?php echo Yii::t('wor', 'TEXT_OP_INFO_2', array('SYS_MIN_REDEEM' => $min_redeem_display)); ?></div>
        <div class="clearfix"></div>
        <?php if($diff <= 0) { ?>
            <?php if (empty($redeem_point)) { ?>
                <label for="#"><?php echo Yii::t('wor', 'REDEEM_CURRENCY'); ?></label>
                <?php
                echo \conquer\select2\Select2Widget::widget([
                    'bootstrap' => false,
                    'name' => 'reg_cur',
                    'items' => $curr_opt,
                    'value' => $curr,
                ]);
                ?>
            <?php } ?>

            <!-- Partition -->
            <div class="partition-dashed"></div>

            <div class="collapse-box" id="redeem-detail">
                <div id="redeem-in-progress" class="text-center" style="height: 200px;">
                    <i class="fa fa-3x fa-spin fa-circle-o-notch icon-loading"></i>
                </div>

                <div id="redeem-detail-result" class="hide">
                    <!-- CALCULATION -->
                    <ul class="calculation">
                        <li class="cal-row">
                            <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('wor', 'TEXT_REDEEM_INTO'); ?>:</span></span>
                            <span class="cal-row-value"><strong class="cal-row-value-text" id="store-credit-display" ></strong></span>
                        </li>
                    </ul>
                    <div id ="btn-redeem" class="btn-spot"><button type="submit" class="btn-submit" value="submit"><?php echo Yii::t('wor', 'TEXT_REDEEM_NOW'); ?></button>
                        <div class="input-row-text-full"><div class="input-row"></div><i class="fa fa-info-circle"></i> <?php echo Yii::t('wor', 'TEXT_OP_INFO_1'); ?></div>
                    </div>
                </div>
            </div>
        <?php }else{ ?>
            <!-- Partition -->
            <div class="partition-dashed"></div>

            <div class="btn-spot"><button type="button" class="btn-inactive"><?php echo Yii::t('wor', 'TEXT_NO_REDEMPTION', array('SYS_REQUIRED_BALANCE' =>$diff_display )); ?></button>
                <div class="input-row-text-full"><div class="input-row"></div><i class="fa fa-info-circle"></i> <?php echo Yii::t('wor', 'TEXT_OP_INFO_1'); ?></div>
            </div>
       <?php }; ?>
    <?php
    \frontend\widgets\ActiveForm::end();
    ?>
</div>
