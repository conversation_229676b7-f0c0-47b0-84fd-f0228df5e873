<?php

use \yii\helpers\Html;

/* @var $this \yii\web\View */
/* @var $from_cur string */
/* @var $to_cur string */

\frontend\assets\StoreCreditAsset::register($this);
?>

<!-- CONVERT -->
<div class="card">
    <div class="input-row-text">
        <span class="icon-success"><i class="fa fa-check-circle"></i></span>
        <h1><?php echo Yii::t('wor', 'HEADER_REDEEM_SUCCESSFUL'); ?></h1>
        <p>
            <?php echo Yii::t('wor', 'TEXT_ORDER_ID'); ?>
            <b><?php echo $redeem_id; ?></b><br>
            <?php echo Yii::t('wor', 'TEXT_OP_INFO_1'); ?>
        </p>
    </div>

    <div class="form-normal">
        <!-- CALCULATION -->
        <ul class="calculation">
            <li class="cal-row">
                <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('wor', 'TITLE_OP_REDEMPTION'); ?>:</span></span>
                <span class="cal-row-value"><span class="cal-row-value-text"><?php echo $token_redeem_display; ?></span></span>
            </li>
            <li class="cal-row">
                <span class="cal-row-label"><span class="cal-row-label-text"><?php echo Yii::t('wor', 'TITLE_AMOUNT_REDEMPTION'); ?>:</span></span>
                <span class="cal-row-value"><span class="cal-row-value-text"><b><?php echo $store_credit_redeem_display; ?></b></span></span>
            </li>
        </ul>
        <div class="btn-spot">
            <?php echo Html::a(Yii::t('storeCredit', 'LINK_VIEW_STATEMENT'), ['op/statement'], ['class' => 'btn btn-default']); ?>
        </div>
    </div>
</div>