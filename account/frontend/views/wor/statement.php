<?php

use yii\helpers\Html;
use common\components\StorePointsCom;

/* @var $this \yii\web\View */
?>
<div class="page-title">
    <h1><?php echo Yii::t('wor', 'TITLE_OP_STATEMENT'); ?></h1>
</div>
<div class="page-description-table">
    <p><?php echo Yii::t('wor', 'TITLE_OP_BALANCE'); ?> :<b class="page-description-highlight"><?php echo $token_display; ?></b></p>
    
    <div class="page-description-right">
        <form class="form">
            <div class="statement-date-selection">
                <?php echo \conquer\select2\Select2Widget::widget([
                    'name' => 'date',
                    'items' => $dates,
                    'bootstrap' => false,
                    'value' => $date,
                    'events' => [
                        'change' => 'function() {jQuery(this).closest("form").submit(); }',
                    ],
                ]); ?>
            </div>
        </form>
    </div>
</div>

<?php if (isset($f_data) && !empty($f_data)) { ?>
    <div id="card-table">
    
        <!-- TABLE -->
        <div class="table">
            <div class="table-head">
                <div class="column-table-head" data-label="<?php echo Yii::t('wor', 'TITLE_DATE'); ?>"><?php echo Yii::t('wor', 'TITLE_DATE'); ?></div>
                <div class="column-table-head" data-label="<?php echo Yii::t('wor', 'TITLE_DESCRIPTION'); ?>"><?php echo Yii::t('wor', 'TITLE_DESCRIPTION'); ?></div>
                <div class="column-right" data-label="<?php echo Yii::t('wor', 'TITLE_TOKEN'); ?>"><?php echo Yii::t('wor', 'TITLE_TOKEN'); ?></div>
                <div class="column-hide" data-label="<?php echo Yii::t('wor', 'TITLE_BALANCE'); ?>"><?php echo Yii::t('wor', 'TITLE_BALANCE'); ?></div>
            </div>
           <?php for ($i = 0, $cnt = count($f_data); $cnt > $i; $i++) { ?>
                <div class="row">
                    <div class="column" data-label="<?php echo Yii::t('wor', 'TITLE_DATE'); ?>">
                            <?php
                            $_date = strtotime($f_data[$i]["date"]);
                            if (date('Y') == date('Y', $_date)) {
                                if (mktime(0, 0, 0, date("m"), date("d"), date("Y")) == mktime(0, 0, 0, date("m", $_date), date("d", $_date), date("Y", $_date))) {
                                    echo date("h:i a", $_date);
                                } else {
                                    echo date("d M Y", $_date);
                                }
                            } else {
                                echo date("d/m/y", $_date);
                            }
                            ?>
                    </div>
                    <div class="column" data-label="<?php echo Yii::t('wor', 'TITLE_DESCRIPTION'); ?>">
                            <?php echo $f_data[$i]["desc"]; ?>&nbsp;
                            <?php
                            if (!empty($f_data[$i]["trans_id"])) {
                                if (isset($f_data[$i]["site"])) {
                                    switch ($f_data[$i]["site"]) {
                                        case 0:
                                            $link = sprintf(Yii::$app->params['OG_ORDER_DETAIL_PAGE_URL'], $f_data[$i]["trans_id"]);
                                            break;
                                    }
                                    echo Html::a("(" . $f_data[$i]["trans_id"] . ")", $link);
                                } else {
                                    echo "(RDN-" . $f_data[$i]["trans_id"] . ")";
                                }
                            } else {
                                if (!empty($f_data[$i]["activity"])) {
                                    echo " - " . $f_data[$i]["activity"];
                                }
                            }
                            ?>
                    </div>
                    <div class="column-right" data-label="<?php echo Yii::t('wor', 'TITLE_TOKEN'); ?>">
                        <?php if(!empty($f_data[$i]["debit"])){ ?>
                            <span class="td-debit">
                                <?php echo " - " . $f_data[$i]["debit"] ;?>
                            </span>
                        <?php } else{
                            echo $f_data[$i]["credit"];
                        } ?>
                    </div>
                    <div class="column-hide" data-label="<?php echo Yii::t('wor', 'TITLE_BALANCE'); ?>"><?php echo $f_data[$i]["balance"]; ?></div>
                </div>
                    <div class="line"></div>
                <?php } ?>
        </div>
        <!-- PAGINATION -->
        <div class="pagination">
            <?php echo $this->render('/site/pagination', $pagination); ?>
        </div>
    </div> 
<?php } else { ?>
    <div class="card">
        <p><?php echo Yii::t('wor', 'TEXT_NO_STATEMENT', array('SYS_DATE' => date('M Y', $date))); ?></p>
    </div>
<?php }
      