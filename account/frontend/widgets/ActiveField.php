<?php

namespace frontend\widgets;

use Yii;
use yii\helpers\ArrayHelper;
use frontend\helpers\Html;
use yii\web\View;

class ActiveField extends \yii\widgets\ActiveField {
    
    public $template = "{input}\n{hint}\n<div class='input-row-error'><i class='fa fa-exclamation-circle'></i> {error}</div>";
    
    public $errorOptions = [
        'class' => 'help-block',
        'tag' => 'span',
    ];
    
    public $inputOptions = [];
    
    public $radioListOptions = null;
    
    public function radioList($items, $options = array()) {
        $options = ArrayHelper::merge($options, [
            'item' => [$this, '_radio'],
            'count' => count($items),
        ]);
        $this->radioListOptions = $options;
        $result = parent::radioList($items, $options);
        unset($this->radioListOptions);
        return $result;
    }
    
    public function _radio($index, $label, $name, $checked, $value) {
        $count = isset($this->radioListOptions['count']) ? $this->radioListOptions['count'] : 1;
        $row_class = isset($this->radioListOptions['rowClass']) ? $this->radioListOptions['rowClass'] : "input-row-$count-col";
        return Html::tag('div', Html::radio($name, $checked, [
            'value' => $value,
            'label' => Html::encode($label) . Html::tag('span', '', ['class' => 'checkmark-radio']),
            'labelOptions' => [
                'class' => 'custom-radio',
            ],
        ]), [
            'class' => $row_class,
        ]);
    }
    
    public function tokenInput($options = array()) {
        $this->addAriaAttributes($options);
        $this->adjustLabelFor($options);
        $this->template = "{input}\n{hint}\n<div class='clearfix'></div><div class='input-row-6-coll-error input-row-error'><i class='fa fa-exclamation-circle'></i> {error}</div>";
        $this->parts['{input}'] = Html::activeTokenInput($this->model, $this->attribute, $options);

        return $this;
    }
}