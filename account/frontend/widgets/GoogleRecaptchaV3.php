<?php

namespace frontend\widgets;

class GoogleRecaptchaV3 extends \yii\base\Widget {
    
    public $api_key;
    
    public function init() {
        parent::init();
        if (!$this->api_key) {
            $this->api_key = \frontend\components\GoogleRecaptcha::getV3PublicKey();
        }
    }
    
    public function run() {
        parent::run();
        if (!static::shouldImplementAmbientCaptcha($this->api_key)) {
            return;
        }
        \frontend\assets\RecaptchaAsset::register($this->getView());
        $action = \Yii::$app->controller->id;
        $js = <<<JS
            ambient_captcha.init('$this->api_key');
            ambient_captcha.execute('$action');
JS;
        $this->getView()->registerJs($js, \yii\web\View::POS_READY);
    }
    
    public static function shouldImplementAmbientCaptcha($api_key = null) {
        if (!$api_key) {
            $api_key = \frontend\components\GoogleRecaptcha::getV3PublicKey();
        }
        return \frontend\components\GoogleRecaptcha::isRegionSupported() && $api_key;
    }
}
