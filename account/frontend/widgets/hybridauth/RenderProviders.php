<?php

namespace frontend\widgets\hybridauth;

use Yii;

class RenderProviders extends \yii\base\Widget {

    public $connectedProviders;
    public $url;
    public $popup; //'true' or 'false' (default depends on hybridauth settings 'BindingDisplay')
    public $onClick;

    /**
     *  Display the render provider vertical or horizontal
     *
     * @var  string    $display ('vertical','horizontal')
     */
    public $display;

    /**
     *  Class name for the size of render provider icons
     *
     * @var  string    $size ('ha-default','ha-medium','ha-small')
     */
    public $size;

    /**
     *  Class name to show render provider icons without pointer cursor
     *
     * @var  string    $static ('ha-static')
     */
    public $static;

    /**
     *  Class names for render providers for ul and li tags
     *
     * @var  string    $class_items ('ha-items')
     * @var  string    $class_item ('ha-item')
     */
    public $class_items;
    public $class_item;

    /**
     *  Overall padding space in pixels for render provider icons
     *
     * @var  int    $items_space_top (0)
     * @var  int    $items_space_bottom (0)
     * @var  int    $items_space_left (0)
     * @var  int    $items_space_right (0)
     */
    public $items_space_top;
    public $items_space_bottom;
    public $items_space_left;
    public $items_space_right;

    /**
     *  Individual padding space in pixels for render provider icons
     *
     * @var  int    $item_space_top (0)
     * @var  int    $item_space_bottom (0)
     * @var  int    $item_space_left (0)
     * @var  int    $item_space_right (0)
     */
    public $item_space_top;
    public $item_space_bottom;
    public $item_space_left;
    public $item_space_right;

    /**
     *  Templates to render providers in the widget
     *
     * @var  string    $template_items (<ul class="{CLASS_ITEMS} {SIZE} {STATIC}">{TEMPLATE_ITEM}</ul>)
     * @var  string    $template_item (<li class="{CLASS_ITEM}"><a class="ha-{LOWERCASE_PROVIDER}" id="ha-{PROVIDER}" href="javascript:;" onclick="trigger_sn_login(\'{URL_SOCIAL_LOGIN}\', {WIDTH}, {HEIGHT})" ></a></li>)
     * @var  string    $template_separator (HTML code default='null')
     */
    public $template_items;
    public $template_item;
    public $template_separator;

    public function init() {
        $this->display = (isset($this->display) ? $this->display : 'vertical');
        $this->size = (isset($this->size) ? $this->size : 'ha-default');
        $this->static = (isset($this->static) ? $this->static : '');
        $this->popup = (isset($this->popup) ? $this->popup : (isset(Yii::$app->hybridauth->bindingDisplay) && Yii::$app->hybridauth->bindingDisplay == 'popup' ? true : false));
        $this->class_items = (isset($this->class_items) ? $this->class_items : 'ha-items');
        $this->class_item = (isset($this->class_item) ? $this->class_item : 'ha-item');
        $this->items_space_top = (isset($this->items_space_top) ? $this->items_space_top : '0');
        $this->items_space_bottom = (isset($this->items_space_bottom) ? $this->items_space_bottom : '0');
        $this->items_space_left = (isset($this->items_space_left) ? $this->items_space_left : '0');
        $this->items_space_right = (isset($this->items_space_right) ? $this->items_space_right : '0');
        $this->item_space_top = (isset($this->item_space_top) ? $this->item_space_top : '0');
        $this->item_space_bottom = (isset($this->item_space_bottom) ? $this->item_space_bottom : '0');
        $this->item_space_left = (isset($this->item_space_left) ? $this->item_space_left : '0');
        $this->item_space_right = (isset($this->item_space_right) ? $this->item_space_right : '0');
        $this->url = (isset($this->url) ? $this->url : '');
        $this->onClick = (isset($this->onClick) ? $this->onClick : ($this->popup == true ? '' : 'refresh_parent(\'{URL}\')'));
        $this->template_items = (isset($this->template_items) ? $this->template_items : '<ul class="{CLASS_ITEMS} {SIZE} {STATIC}">{TEMPLATE_ITEM}</ul>');
        $this->template_item = (isset($this->template_item) ? $this->template_item : '<li class="{CLASS_ITEM}"><a class="ha-{LOWERCASE_PROVIDER}" id="ha-{PROVIDER}" href="' . ($this->popup == true ? '{URL}' : 'javascript:;') . '" onclick="{ON_CLICK}" ></a></li>');
        $this->template_separator = (isset($this->template_separator) ? $this->template_separator : '');
    }

    public function run() {
        RenderProvidersAsset::register($this->getView());
        return $this->render('render_providers', [
            'connectedProviders' => $this->connectedProviders,
            'url' => $this->url,
            'popup' => $this->popup,
            'onClick' => $this->onClick,
            'display' => $this->display,
            'size' => $this->size,
            'static' => $this->static,
            'class_items' => $this->class_items,
            'class_item' => $this->class_item,
            'items_space_top' => $this->items_space_top,
            'items_space_bottom' => $this->items_space_bottom,
            'items_space_left' => $this->items_space_left,
            'items_space_right' => $this->items_space_right,
            'item_space_top' => $this->item_space_top,
            'item_space_bottom' => $this->item_space_bottom,
            'item_space_left' => $this->item_space_left,
            'item_space_right' => $this->item_space_right,
            'template_items' => $this->template_items,
            'template_item' => $this->template_item,
            'template_separator' => $this->template_separator,
        ]);
    }

}

