var winRef = null;
var previousUrl;

function trigger_sn_login(url, width, height) {
    var screenX = typeof window.screenX != 'undefined' ? window.screenX : window.screenLeft,
            screenY = typeof window.screenY != 'undefined' ? window.screenY : window.screenTop,
            outerWidth = typeof window.outerWidth != 'undefined' ? window.outerWidth : document.body.clientWidth,
            outerHeight = typeof window.outerHeight != 'undefined' ? window.outerHeight : (document.body.clientHeight - 22),
            left = parseInt(screenX + ((outerWidth - width) / 2), 10),
            top = parseInt(screenY + ((outerHeight - height) / 2.5), 10),
            features = (
            'width=' + width +
            ',height=' + height +
            ',left=' + left +
            ',top=' + top
            );

    if (winRef == null || winRef.closed) {
        winRef = window.open(url, "Login_by_Social_Network", features);
    } else if (previousUrl != url) {
        winRef.close();
        winRef = window.open(url, "Login_by_Social_Network", features);
    }

    winRef.focus();

    previousUrl = url;
    return false;
}

function refresh_parent(url) {
    top.location.href = url;
}
function popup_express_login(provider) {
    title = HA_TITLE.replace('{PROVIDER}', provider);
    jQuery('#ha-inneriframe_xdomain').attr('src', HA_PROXY_URL + "?rtype=expressLogin&xTitle=" + title + "&res=" + HA_IframeXdomainUrl);
}