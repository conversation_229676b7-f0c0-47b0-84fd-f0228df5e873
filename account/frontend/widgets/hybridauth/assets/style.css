.ha-items a {
    background-image: url('images/btn_sociallogin.gif');
    background-repeat: no-repeat;
    margin: 0px;
    display: block;
}
.ha-default a {
    width: 219px;
    height: 39px;
}
.ha-medium a {
    width: 31px;
    height: 31px;
}
.ha-small a {
    width: 16px;
    height: 16px;
}
.ha-static a {
    cursor: default;
}
.ha-default a.ha-facebook {
    background-position: 0px 0px;
}
.ha-default a.ha-facebook:hover {
    background-position: 0px -39px;
}
.ha-medium a.ha-facebook {
    background-position: -220px -17px;
}
.ha-small a.ha-facebook {
    background-position: -220px 0px;
}
.ha-default a.ha-google {
    background-position: 0px -78px;
}
.ha-default a.ha-google:hover {
    background-position: 0px -117px;
}
.ha-medium a.ha-google {
    background-position: -219px -50px;
}
.ha-small a.ha-google {
    background-position: -271px 0px;
}
.ha-default a.ha-twitter {
    background-position: 0px -156px;
}
.ha-default a.ha-twitter:hover {
    background-position: 0px -195px;
}
.ha-medium a.ha-twitter {
    background-position: -250px -17px;
}
.ha-small a.ha-twitter {
    background-position: -237px 0px;
}
.ha-render {
    width: 219px;
    height: 39px;
    cursor: pointer;
    text-align: center;
    line-height: 39px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background-color: #999999;
    color: white;
}
.ha-render:hover{
    background-color: #2d2d2d;
}
.ha-render.ha-facebook{
    background-color: #48629b;
    color: white;
    text-shadow: 1px 1px black;
}
.ha-render.ha-facebook:hover{
    background-color: #6379aa;
}
.ha-render.ha-twitter{
    background-color: #22afe5;
    color: white;
    text-shadow: 1px 1px black;
}
.ha-render.ha-twitter:hover{
    background-color: #43bae9;
}
.ha-render.ha-google{
    background-color: #dc4b38;
    color: white;
    text-shadow: 1px 1px black;
}
.ha-render.ha-google:hover{
    background-color: #e16656;
}