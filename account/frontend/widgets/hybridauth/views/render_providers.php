<style type="text/css">
    ul.ha-items {
        margin-top: <?php echo $items_space_top; ?>px;
        margin-bottom: <?php echo $items_space_bottom; ?>px;
        margin-left: <?php echo $items_space_left; ?>px;
        margin-right: <?php echo $items_space_right; ?>px;
        padding: 0px;
        float:left;
    }
    li.ha-item {
        list-style: none;
        margin-top: <?php echo $item_space_top; ?>px;
        margin-bottom: <?php echo $item_space_bottom; ?>px;
        margin-left: <?php echo $item_space_left; ?>px;
        margin-right: <?php echo $item_space_right; ?>px;
        padding: 0px;
        <?php if ($display == 'horizontal') { ?>
            float: left;
        <?php } ?>
    }
</style>
<?php
$item = '';
$counter = 1;
foreach (Yii::$app->hybridauth->getAllowedProviders() as $provider => $settings) {
    $width = isset($settings['width']) ? $settings['width'] : 500;
    $height = isset($settings['height']) ? $settings['height'] : 270;
    $enabled = false;
    if (is_array($connectedProviders) && array_key_exists($provider, $connectedProviders)) {
        $enabled = true;
    }
    $display_separator = (($counter == 1) ? '' : $template_separator);
    $display_enabled = ($enabled ? 'block' : 'none');
    $display_disabled = ($enabled ? 'none' : 'block');

    $fa_icon = "";
    switch ($provider) {
        case 'Google':
            $fa_icon = "google-plus";
            break;
        default:
            $fa_icon = strtolower($provider);
    }

    $icon = ($fa_icon == "paypal") ? "fa fa-" . $fa_icon : "fa fa-" . $fa_icon . "-square";
    $variables = array(
        '{PROVIDER}' => $provider,
        '{LOWERCASE_PROVIDER}' => strtolower($provider),
        '{UPPERCASE_PROVIDER}' => strtoupper($provider),
        '{CLASS_ITEMS}' => $class_items,
        '{CLASS_ITEM}' => $class_item,
        '{SIZE}' => $size,
        '{STATIC}' => $static,
        '{WIDTH}' => $width,
        '{HEIGHT}' => $height,
        '{DISPLAY_ENABLED}' => $display_enabled,
        '{DISPLAY_DISABLED}' => $display_disabled,
        '{TEMPLATE_SEPARATOR}' => $display_separator,
        '{PROVIDER_FONTAWESOME}' => $fa_icon,
        '{PROVIDER_FONTAWESOME_ICON}' => $icon
    );

    $variables[urlencode('{PROVIDER}')] = $provider;
    $variables['{URL}'] = str_replace(array_keys($variables), array_values($variables), $url);
    $variables['{ON_CLICK}'] = str_replace(array_keys($variables), array_values($variables), $onClick);

    $t = $template_item;
    if (is_array($t) && count($t) == 2) {
        $t = call_user_method($t[1], $t[0]);
    } else if (is_callable($t)) {
        $t = call_user_func($t);
    }
    $item .= str_replace(array_keys($variables), array_values($variables), $t);
    $counter++;
}
if ($item != "") {
    $variables['{TEMPLATE_ITEM}'] = $item;
    if (is_array($template_items) && count($template_items) == 2) {
        $template_items = call_user_method($template_items[1], $template_items[0]);
    } else if (is_callable($template_items)) {
        $template_items = call_user_func($template_items);
    }
    $items = str_replace(array_keys($variables), array_values($variables), $template_items);
    echo $items;
}
