# yii console command
/yii

# phpstorm project files
.idea

# netbeans project files
nbproject

# zend studio for eclipse project files
.buildpath
.project
.settings

# CVS
.cvsignore

# windows thumbnail cache
Thumbs.db

# composer vendor dir
/vendor

# composer itself is not needed
composer.phar
composer.lock

# Mac DS_Store Files
.DS_Store

# phpunit itself is not needed
phpunit.phar
# local phpunit config
/phpunit.xml

# application skiplist
/upload/docs/*
/includes/configure.php
/includes/addon/amazonaws_sdk/config.inc.php
/includes/modules/auto_restock/*/config.inc.php

/admin/download/*
!/admin/download/.htaccess
/admin/includes/configure.php
/admin/includes/addon/amazonaws_sdk/config.inc.php
/admin/repository/*
!/admin/repository/.gitignore
/api/includes/configure.php
/api/includes/addon/amazonaws_sdk/config.inc.php