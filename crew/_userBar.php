<div id="userbar-container" class="visible-desktop"></div>
<div id="g2g-userbar" class="navbar navbar-fixed-top">
    <div id="g2g-userbar-inner" class="navbar-inner">
        <div class="container">
            <a id="g2g-userbar-collapsed" class="btn btn-navbar collapsed" data-toggle="collapse" data-target="#g2g-userbar-collapse">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </a>
            <div id="g2g-userbar-collapse" class="nav-collapse collapse">
                <ul class="nav nav-pills pull-right" role="menu">
                    <?php if ($_SESSION['customer_id']) { ?>
                        <li class="dropdown" role="menuitem">
                            <a class="dropdown-toggle" href="" data-toggle="dropdown"><?php echo TEXT_MY_OGM; ?></a>
                            <ul class="dropdown-menu" role="menu">
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL ?>/site/index/c/ogmOverview/a/index"><?php echo TEXT_OVERVIEW; ?></a></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL ?>/site/index/c/MyStoreOrders/a/index"><?php echo TEXT_BUY_HISTORY; ?></a></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL ?>/site/index/c/MyStoreBuyback/a/orders"><?php echo TEXT_SELL_HISTORY; ?></a></li>
<!--                                <li><a href="<?php echo HTTP_SHASSO_PORTAL ?>/site/index/c/WithdrawMoney/a/index"><?php echo TEXT_REQUEST_PAYMENT; ?></a></li>-->
                            </ul>
                        </li>
                        <li class="dropdown" role="menuitem">
                            <a id="ub-profile" class="dropdown-toggle" href="" data-toggle="dropdown">
                                <div class="g2g-cust-avatar"></div>
                                <div style="display: inline-block"><?php echo $customer_first_name; ?></div>
                                <?php if ($avatar = $shasso_obj->getAvatar()) { ?>
                                    <div class="avatar"><img src="<?php echo $avatar; ?>" height="16px"></div>  
                                <?php } else { ?>
                                    <div class="avatar default-avatar"></div>
                                <?php } ?>
                                <div style="display: inline-block"><?php echo $_SESSION['customer_id']; ?></div>
                            </a>
                            <ul id="ub-profile-dd" class="dropdown-menu" role="menu">
                                <li class="nav-header"><?php echo TEXT_ACCOUNT_ID . ' : ' . $_SESSION['customer_id']; ?></li>
                                <li class="nav-header" style="white-space: nowrap;margin-top: 0"><?php echo TEXT_BUY_STATUS . ' : ' . $_SESSION['customers_groups_name']; ?></li>
                                <li class="divider"></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL; ?>/profile/index"><?php echo TEXT_MANAGE_PROFILE; ?></a></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL; ?>/social/connect"><?php echo TEXT_SOCIAL_CONNECT; ?></a></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL; ?>/storeCredit/index#topup"><?php echo TEXT_STORE_CREDITS; ?> : <span id="ub-sc"><b><i>Loading ...</i></b></span></a></li>
                                <li><a href="<?php echo HTTP_SHASSO_PORTAL; ?>/wor/index"><?php echo TEXT_WOR; ?> : <div class="tokenIcon" style="display: none; vertical-align: text-bottom;"></div>&nbsp;<span id="ub-wor"><b><i>Loading ...</i></b></span></a></li>
                                <li class="divider"></li>
                                <li><a href="<?php echo $shasso_obj->getLogoutURL(); ?>"><?php echo TEXT_LOGOUT; ?></a></li>
                            </ul>
                        </li>
                    <?php } else { ?>
                        <li role="menuitem">
                            <a href="<?php echo $shasso_obj->getSignupURL(); ?>"><?php echo TEXT_SIGN_UP; ?></a>
                        </li>
                        <li class="dropdown" role="menuitem">
                            <a class="connect-with">
                                <div id='login-link' onclick="location.href = '<?php echo $shasso_obj->getLoginURL(); ?>'"><?php echo TEXT_LOG_IN; ?></div>
                                <div><?php echo TEXT_OR_CONNECT; ?></div>
                                <?php
                                if ($ogm_fb_obj->FB_connect_switch) {
                                ?>
                                <div class='sns fb' onclick="location.href = '<?php echo $shasso_obj->getLoginURL('facebook'); ?>'"></div>
                                <div class='sns gp' onclick="location.href = '<?php echo $shasso_obj->getLoginURL('google'); ?>'"></div> 
                                <?php
                                }
                                ?>
                                <div class='sns tw' onclick="location.href = '<?php echo $shasso_obj->getLoginURL('twitter'); ?>'"></div>
                            </a>
                        </li>
                    <?php } ?>
                    <li class="dropdown" role="menuitem">
                        <a id="reg-setting" class="dropdown-toggle" href="#" data-toggle="dropdown">
                            <div class="flag <?php echo $_SESSION['countries_iso_code_2']; ?>" style="display: inline-block; vertical-align: text-bottom">
                                <div></div>
                            </div>
                            <div style="display: inline-block">
                                <?php echo '&nbsp;/&nbsp;' . $currency . '&nbsp;/&nbsp;' . $_SESSION['language_name']; ?>
                            </div>
                        </a>
                        <ul id="reg-setting-dd" class="dropdown-menu" role="menu">
                            <?php echo tep_draw_form('account_edit_form', tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('loc_country', 'currency', 'language')), 'SSL'), 'post', 'class="regional-form"')?>
                            <li><?php echo TEXT_REGIONAL_TITLE; ?></li>
                            <li><?php echo tep_draw_pull_down_menu('RegionalSet[loc_country]', array(), '', 'id="reg_ctry" class="g2g-select2 clear-margin-left span3"'); ?></li>
                            <li><?php echo tep_draw_pull_down_menu('RegionalSet[currency]', array(), '', 'id="reg_cur" class="g2g-select2 clear-margin-left span3"'); ?></li>
                            <li><?php echo tep_draw_pull_down_menu('RegionalSet[language]', array(), '', 'id="reg_lang" class="g2g-select2 clear-margin-left span3"'); ?></li>
                            <li style="text-align: center">
                                <button class="btn" name="yt4" type="submit"><?php echo BTN_SAVE_CHANGES; ?></button>
                            </li>
                            </form>
                        </ul>
                    </li>
                    <li class="dropdown" role="menuitem">
                        <a class="dropdown-toggle" href="" data-toggle="dropdown"><i class="fa fa-th" style="font-size: 14px;"></i></a>
                        <ul class="dropdown-menu" role="menu">
                            <li>
                                <a href="http://www.offgamers.com" target="_blank">
                                    <div class="network-container">
                                        <div class="network"><div class="network-logo network-ogm"></div></div>
                                        <div class="network-link">
                                            <?php echo TEXT_OGM_DOMAIN; ?><br/>
                                            <span><?php echo TEXT_OGM_CAPTION; ?></span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a href="http://www.g2g.com" target="_blank">
                                    <div class="network-container">
                                        <div class="network"><div class="network-logo network-g2g"></div></div>
                                        <div class="network-link">
                                            <?php echo TEXT_G2G_DOMAIN; ?><br/>
                                            <span><?php echo TEXT_G2G_CAPTION; ?></span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a href="http://www.gamernizer.com" target="_blank">
                                    <div class="network-container">
                                        <div class="network"><div class="network-logo network-gmz"></div></div>
                                        <div class="network-link">
                                            <?php echo TEXT_GMZ_DOMAIN; ?><br/>
                                            <span><?php echo TEXT_GMZ_CAPTION; ?></span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>                
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var REFRESH_TIME = 90000;
    var COUNTRY_STATIC_DOMAIN = "<?php echo $shasso_obj->getJsonURL('country'); ?>";
    var REGION_STATIC_DOMAIN = "<?php echo $shasso_obj->getJsonURL('region'); ?>";

    jQuery(document).ready(function() {
        g2g.userBar.init("<?php echo $_SESSION['countries_iso_code_2']; ?>", "<?php echo $currency; ?>", "<?php echo $language_code; ?>");
        g2g.profile.getCustomerScWor();
        jQuery("select.g2g-select2").select2();
        g2gResponsiveSelect2();
    });
</script>