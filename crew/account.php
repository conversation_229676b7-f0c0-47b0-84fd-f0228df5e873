<?php
/*
  $Id: account.php,v 1.18 2014/12/29 08:10:51 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

tep_redirect(HTTP_SHASSO_PORTAL);

if (!tep_session_is_registered('customer_id')) {
    $navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT);

// verify email address
if (tep_info_verified_check($customer_id, tep_get_customers_email($customer_id), 'email') != 1) {
    $messageStack->myaccount_add('my_account_home', sprintf(ALERT_EMAIL_VERIFICATION, "<a href=\"" . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . "\">" . TEXT_CLICK_HERE . "</a>"));
}

// verify dispute orders
$orders_disputes_res = tep_db_query("SELECT orders_id FROM " . TABLE_ORDERS . " WHERE orders_status= '8' AND customers_id='" . $customer_id . "' LIMIT 1");
if (tep_db_num_rows($orders_disputes_res)) {
    $messageStack->myaccount_add('my_account_home', sprintf(ALERT_ORDER_DISPUTES, TEXT_LIVE_SUPPORT));
}

function tep_payment_authorised_result($cust_id = '') {
    require_once(DIR_WS_CLASSES . 'payment_methods.php');

    $orders_pending_select_sql = "	SELECT orders_id, payment_methods_id, payment_methods_parent_id 
									FROM " . TABLE_ORDERS . " 
									WHERE customers_id = '" . (int) $cust_id . "' 
										AND orders_status = '1'";
    $orders_pending_select_res = tep_db_query($orders_pending_select_sql);
    $orders_pending_array = array();
    $payment_methods_array = array();
    while ($orders_pending_select_row = tep_db_fetch_array($orders_pending_select_res)) {
        if (!isset($payment_methods_array[$orders_pending_select_row['payment_methods_id']])) {
            $payment_methods_obj = new payment_methods($orders_pending_select_row['payment_methods_id']);
            $payment_methods_obj = $payment_methods_obj->payment_method_array;
            $payment_methods_array[$orders_pending_select_row['payment_methods_id']] = $payment_methods_obj->customer_payment_info;
        }
        if ($payment_methods_array[$orders_pending_select_row['payment_methods_id']] == 'True') {
            $orders_authorised_result_sql = "	SELECT authorisation_result 
												FROM " . TABLE_PAYMENT_EXTRA_INFO . " 
												WHERE orders_id = '" . $orders_pending_select_row['orders_id'] . "'";
            $orders_authorised_result_res = tep_db_query($orders_authorised_result_sql);
            if ($orders_authorised_result_row = tep_db_fetch_array($orders_authorised_result_res)) {
                if (!tep_not_null($orders_authorised_result_row['authorisation_result'])) {
                    $orders_pending_array[] = $orders_pending_select_row['orders_id'];
                }
            } else {
                $orders_pending_array[] = $orders_pending_select_row['orders_id'];
            }
        }
    }
    unset($payment_methods_array);

    return $orders_pending_array;
}

$payment_authorised_orders_array = tep_payment_authorised_result($customer_id);
if (count($payment_authorised_orders_array)) {
    $messageStack->myaccount_add('my_account_home', sprintf(ALERT_ORDER_NEEDS_PAYMENT_INFO, "<a href=\"" . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 's_orders_id=' . tep_array_serialize($payment_authorised_orders_array), 'SSL') . "\">" . TEXT_CLICK_HERE . "</a>"));
}
unset($payment_authorised_orders_array);

$orders_type_status_array = array(1 => 'current',
    2 => 'current',
    7 => 'current',
    8 => 'current',
    5 => 'cancelled',
    3 => 'completed');

//$banner_img_url = "http://image.offgamers.com/my_account_banner_".$languages_id.".jpg";
$banner_img_url = '';

if ($languages_id == '1') {
    $banner_url = "http://space.offgamers.com/" . $language_code . "/blogs/sales-a-promotions/348-september-2010/3951-store-credits-extra-purchase";
} else {
    $banner_url = "http://space.offgamers.com/" . $language_code . "/blogs/sales-a-promotions-cn/353-2010-9/3919-5-extra-with-every-purchase-of-offgamers-store-credit";
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));

$content = CONTENT_ACCOUNT;
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>