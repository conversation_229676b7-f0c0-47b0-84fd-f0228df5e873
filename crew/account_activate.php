<?php

/* 	
  Developer: <PERSON><PERSON>
  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License
 */

require('includes/application_top.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_ACTIVATE);
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'phone_verification.php');

if (!isset($_SESSION['customer_id'])) {
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$user_os = tep_get_os($HTTP_USER_AGENT);
$phone_verify_mode = ($user_os == 'Windows' || $user_os == 'Linux') ? 'xmlhttp_mode' : 'refresh_mode';
$verify_mode = 0;

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$serial = (isset($_REQUEST['serial']) ? $_REQUEST['serial'] : '');
$email = (isset($_REQUEST['email']) ? $_REQUEST['email'] : '');
$auto_mail = (isset($_REQUEST['auto_mail']) ? $_REQUEST['auto_mail'] : '');

// ******** chingyen : allow `verify_email` only
//if ($action != "verify_email") {
    tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/verifyEmail/a/index/p/action=verify' . (tep_not_null($serial) ? '&serialNumber=' . $serial : '') . (tep_not_null($email) ? '&email=' . urlencode($email) : ''));
//}

$activation_box = true;
$message_display = '';
$redirect_path = tep_href_link(FILENAME_LOGIN);
$phone_verification_obj = new phone_verification();

function tep_order_notify($orders_status_id, $customer_id, $customer_email) {
    $orders_id_select_sql = "	SELECT orders_id 
								FROM " . TABLE_ORDERS . " 
								WHERE customers_id = '" . (int) $customer_id . "' 
									AND orders_status = '" . (int) $orders_status_id . "' 
									AND FIND_IN_SET('170', orders_tag_ids)";
    $orders_id_result_sql = tep_db_query($orders_id_select_sql);

    if (tep_db_num_rows($orders_id_result_sql) > 0) {
        $order_cnt = 1;
        $email_subject = 'Customer ID: ' . $customer_id . ' - Email Verified';
        $email_text = 'E-mail: ' . $customer_email . "\n" . 'ID: ' . $customer_id . "\n\n" . 'Order/s awaiting email verification:' . "\n";

        while ($orders_id_row = tep_db_fetch_array($orders_id_result_sql)) {
            $email_text .= $order_cnt . '. ' . $orders_id_row['orders_id'] . "\n";
            $order_cnt++;
        }

        tep_mail(STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}

function tep_set_account_activate($customers_id, $email_address) {
    $account_activated_select_sql = "	SELECT account_activated 
										FROM " . TABLE_CUSTOMERS . " 
										WHERE customers_id = '" . (int) $customers_id . "' 
											AND customers_email_address = '" . tep_db_input($email_address) . "'";
    $account_activated_result_sql = tep_db_query($account_activated_select_sql);
    if ($account_activated_row = tep_db_fetch_array($account_activated_result_sql)) {
        if ($account_activated_row['account_activated'] == 0) {
            $sql_data_array = array('account_activated' => 1);
            tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int) $customers_id . "'");
        }
    }
}

// Send e-mail verification when customer click on the link in the menu
if (isset($_SESSION['customer_id']) && $auto_mail == 'yes') {
    $verify_email_select_sql = "	SELECT customers_email_address, customers_firstname, customers_lastname, customers_gender 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
    $verify_email_query = tep_db_query($verify_email_select_sql);
    $verify_email = tep_db_fetch_array($verify_email_query);

    $serial_exist_select_sql = "	SELECT serial_number 
									FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
									WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "' 
										AND	info_verification_type = 'email' 
										AND customers_info_value  = '" . tep_db_input($verify_email['customers_email_address']) . "'
										AND info_verified = 0";
    $serial_exist_result_sql = tep_db_query($serial_exist_select_sql);
    $serial_exist_row = tep_db_fetch_array($serial_exist_result_sql);

    if (!tep_not_null($serial_exist_row['serial_number'])) {
        tep_send_info_verification($verify_email['customers_email_address'], $verify_email['customers_firstname'], $verify_email['customers_lastname'], $verify_email['customers_gender'], '', $_SESSION['customer_id']);
    }
}

if (isset($_SESSION['customer_id'])) {
    $msg_page_show = CONTENT_ACCOUNT_EDIT;
    $redirect_path = tep_href_link(FILENAME_ACCOUNT_EDIT);

    $customers_info_select_sql = "	SELECT customers_dob, customers_email_address 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
    $customers_info_result_sql = tep_db_query($customers_info_select_sql);
    $customers_info_row = tep_db_fetch_array($customers_info_result_sql);

    if (isset($_SESSION['trace'])) {
        if (tep_not_null($customers_info_row['customers_dob'])) {
            if ($_SESSION['trace'] == 'chkout') {
                $msg_page_show = CONTENT_CHECKOUT_SHIPPING;
                $redirect_path = tep_href_link(FILENAME_CHECKOUT_SHIPPING);
            } else if ($_SESSION['trace'] == 'bb_chkout') {
                $msg_page_show = CONTENT_BUYBACK;
                $redirect_path = tep_href_link(FILENAME_BUYBACK);
            } else if ($_SESSION['trace'] == 'scchkout') {
                $msg_page_show = CONTENT_SC_CHECKOUT_PAYMENT;
                $redirect_path = tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT);
            }
        }
    }
} else {
    $msg_page_show = CONTENT_LOGIN;
}

if ($action == "verify_email") {
    $email_verified_select_sql = "	SELECT civ.customers_id, civ.info_verified, civ.customers_info_value 
									FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
									WHERE civ.info_verification_type = 'email' 
										AND civ.serial_number = '" . tep_db_input($serial) . "'";
    $email_verified_result_sql = tep_db_query($email_verified_select_sql);

    if ($email_verified_row = tep_db_fetch_array($email_verified_result_sql)) {
        if ($email_verified_row['info_verified'] == 1) {
            $message_display = MESSAGE_EMAIL_VERIFIED;
            $activation_box = false;
            $verify = 'email';
        } else {
            if ($email_verified_row['customers_info_value'] == $email) {
                $update_after_verified = "update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set info_verified = '1', serial_number = '', customers_info_verification_date = now() where customers_info_value = '" . tep_db_input($email) . "' AND info_verification_type = 'email' AND serial_number = '" . tep_db_input($serial) . "'";
                tep_db_query($update_after_verified);

                tep_set_account_activate($email_verified_row['customers_id'], $email);

                $message_display = MESSAGE_VERIFIED_SUCCESS;
                $activation_box = false;
                $verify = 'email';

                // Rerun Genesis script for all verifying orders
                $order_info_select_sql = "  SELECT DISTINCT o.payment_methods_id 
                                            FROM " . TABLE_ORDERS . " AS o 
                                            WHERE o.customers_id ='" . tep_db_input($email_verified_row['customers_id']) . "'
                                                and o.orders_status = 7
                                            GROUP BY payment_methods_parent_id";
                $order_info_result_sql = tep_db_query($order_info_select_sql);

                while ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
                    $cust_payment_method_obj = new payment_methods($order_info_row['payment_methods_id']);
                    $cust_payment_method_obj = $cust_payment_method_obj->payment_method_array;

                    if (method_exists($cust_payment_method_obj, 'get_orders_with_payer_email')) {
                        $cust_payment_method_obj->get_orders_with_payer_email($email_verified_row['customers_id'], $email);
                    }
                }

                tep_order_notify(7, $_SESSION['customer_id'], $email_verified_row['customers_info_value']);
            } else {
                $messageStack->add('account_activate', MESSAGE_VERIFIED_NOT_MATCH);
                $activation_box = true;
            }
        }
    } else {
        $messageStack->add('account_activate', MESSAGE_ERROR_MAIL);
        $activation_box = true;
    }
}

$resend_verify_email = false;
$manual_verified = false;

if (isset($action) && ($action == 'resend')) {
    if ($action == 'resend')
        $resend_verify_email = true;

    $customer_email_select_query = "SELECT customers_email_address FROM " . TABLE_CUSTOMERS . " WHERE customers_id='" . $_SESSION['customer_id'] . "' ";
    $customer_email_select_result = tep_db_query($customer_email_select_query);
    $customer_email_select_row = tep_db_fetch_array($customer_email_select_result);
    $email_address = $customer_email_select_row['customers_email_address'];
    //verify=email
    if (strlen($email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
        $messageStack->add_session('account_activate', ENTRY_EMAIL_ADDRESS_ERROR);
        tep_redirect(tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email'));
    } else if (tep_validate_email($email_address) == false) {
        $messageStack->add_session('account_activate', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
        tep_redirect(tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email'));
    } else {
        $customer_resend_select_sql = "	SELECT c.customers_id, c.customers_lastname, c.customers_gender, c.customers_firstname, civ.info_verified, c.account_activated, civ.serial_number 
										FROM " . TABLE_CUSTOMERS . " AS c 
										INNER JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
											ON (c.customers_email_address = civ.customers_info_value AND civ.info_verification_type = 'email' AND c.customers_id = civ.customers_id) 
										WHERE c.customers_email_address = '" . tep_db_input($email_address) . "'";
        $customer_resend_result_sql = tep_db_query($customer_resend_select_sql);
        if ($customer_resend_row = tep_db_fetch_array($customer_resend_result_sql)) {
            if ($customer_resend_row['info_verified'] == 1) {
                $messageStack->add('account_activate', MESSAGE_EMAIL_VERIFIED);
                $verify = 'email';
            } else {
                tep_send_info_verification($email_address, $customer_resend_row['customers_firstname'], $customer_resend_row['customers_lastname'], $customer_resend_row['customers_gender'], $customer_resend_row['serial_number'], $customer_resend_row['customers_id']);
                $messageStack->add_session('account_activate', MESSAGE_SUCCESS_MAIL_2, 'success');
                tep_redirect(tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email'));
            }
        } else {
            $messageStack->add_session('account_activate', $email_address . MESSAGE_ERROR_MAIL);
            tep_redirect(tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email'));
        }
    }
} else if (isset($action) && ($action == 'manual_verified')) {
    if ($action == 'manual_verified')
        $manual_verified = true;

    $manual_email_address = tep_db_prepare_input($_POST['manual_email_address']);
    $manual_verification_code = tep_db_prepare_input($_POST['manual_activation_code']);

    if (strlen($manual_email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
        $messageStack->add('account_activate', ENTRY_EMAIL_ADDRESS_ERROR);
        $activation_box = true;
    } else if (tep_validate_email($manual_email_address) == false) {
        $messageStack->add('account_activate', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
        $activation_box = true;
    } else if (!tep_not_null($manual_activation_code)) {
        $messageStack->add('account_activate', ENTRY_SERIAL_ERROR);
        $activation_box = true;
    } else if (strlen($manual_activation_code) < 12 || strlen($manual_activation_code) > 12) {
        $messageStack->add('account_activate', ENTRY_SERIAL_CHECK_ERROR);
        $activation_box = true;
    } else {
        $email_verified_select_sql = "	SELECT civ.customers_id, civ.info_verified, civ.customers_info_value 
										FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
										WHERE civ.info_verification_type = 'email' 
											AND civ.serial_number = '" . tep_db_input($manual_verification_code) . "'";
        $email_verified_result_sql = tep_db_query($email_verified_select_sql);

        if ($email_verified_row = tep_db_fetch_array($email_verified_result_sql)) {
            if ($email_verified_row['info_verified'] == 1) {
                $message_display = MESSAGE_EMAIL_VERIFIED;
                $activation_box = false;
                $verify = 'email';
            } else {
                if ($email_verified_row['customers_info_value'] == $manual_email_address) {
                    $update_after_verified = "update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set info_verified = '1', serial_number = '', customers_info_verification_date = now() where customers_info_value = '" . tep_db_input($manual_email_address) . "' AND info_verification_type = 'email' AND serial_number = '" . tep_db_input($manual_verification_code) . "'";
                    tep_db_query($update_after_verified);

                    tep_set_account_activate($email_verified_row['customers_id'], $manual_email_address);

                    $message_display = MESSAGE_VERIFIED_SUCCESS;
                    $activation_box = false;
                    $verify = 'email';

                    // Rerun Genesis script for all verifying orders
                    $order_info_select_sql = "	SELECT DISTINCT o.payment_methods_id 
                                                FROM " . TABLE_ORDERS . " AS o 
                                                WHERE o.customers_id ='" . tep_db_input($email_verified_row['customers_id']) . "'
                                                    and o.orders_status = 7";
                    $order_info_result_sql = tep_db_query($order_info_select_sql);

                    while ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
                        $cust_payment_method_obj = new payment_methods($order_info_row['payment_methods_id']);
                        $cust_payment_method_obj = $cust_payment_method_obj->payment_method_array;

                        if (method_exists($cust_payment_method_obj, 'get_orders_with_payer_email')) {
                            $cust_payment_method_obj->get_orders_with_payer_email($email_verified_row['customers_id'], $email);
                        }
                    }

                    tep_order_notify(7, $_SESSION['customer_id'], $email_verified_row['customers_info_value']);
                } else {
                    $messageStack->add('account_activate', MESSAGE_VERIFIED_NOT_MATCH);
                    $activation_box = true;
                }
            }
        } else {
            $messageStack->add('account_activate', MESSAGE_ERROR_MAIL);
            $activation_box = true;
        }
    }
}

//************************
$verify = tep_not_null($_REQUEST['verify']) ? tep_db_prepare_input($_REQUEST['verify']) : $verify;
$order_id = tep_not_null($_REQUEST['order_id']) ? tep_db_prepare_input($_REQUEST['order_id']) : '';

if ($verify == "phone") {
    if (tep_not_null($order_id)) {
        require_once(DIR_WS_CLASSES . 'order.php');
        $order = new order($order_id);

        $pm_obj = new payment_methods($order->info['payment_methods_id']); //new payment_methods('payment_methods');
        // get payment method's confirm completed days (larger than 0 is RP else is NRP)
        $payment_confirm_complete_days = $pm_obj->payment_method_array->confirm_complete_days;

        $breadcrumb->add(NAVBAR_TITLE_4, tep_href_link(FILENAME_ACCOUNT_ACTIVATE, '', 'SSL'));
    } else {
        $breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_ACCOUNT_ACTIVATE, '', 'SSL'));
    }
} else if ($verify == "email") {
    $breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_ACTIVATE, '', 'SSL'));
}

$content = CONTENT_ACCOUNT_ACTIVATE;
$javascript = $content . '.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>