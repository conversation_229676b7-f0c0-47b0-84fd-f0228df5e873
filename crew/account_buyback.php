<?
/*
  	$Id: account_buyback.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: e-gold Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_BUYBACK);

$accept_account_buyback = false;

if (!tep_session_is_registered('ss_account_buyback')) {
	tep_session_register('ss_account_buyback');
	$ss_account_buyback = array();
}

define('DISPLAY_PRICE_DECIMAL', 2);
define('ACCOUNT_BUYBACK_ADMIN_EMAIL_ADDRESS', '<EMAIL>');

$accepted_games_array = array('194' => 'World of Warcraft US', '195' => 'World of Warcraft EU'); // Hard code here for WOW US and WOW EU

$main_char_lvl_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
$others_char_lvl_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );

$char_race_array = array( 	array('id' => '', 'text' => PULL_DOWN_DEFAULT),
							array('id' => 'Draenei', 'text' => 'Draenei'),
							array('id' => 'Dwarf', 'text' => 'Dwarf'),
							array('id' => 'Gnome', 'text' => 'Gnome'),
							array('id' => 'Human', 'text' => 'Human'),
							array('id' => 'Night Elf', 'text' => 'Night Elf'),
							array('id' => 'Blood Elf', 'text' => 'Blood Elf'),
							array('id' => 'Orc', 'text' => 'Orc'),
							array('id' => 'Tauren', 'text' => 'Tauren'),
							array('id' => 'Troll', 'text' => 'Troll'),
							array('id' => 'Undead', 'text' => 'Undead')
						);
/*
$char_class_array = array( 	array('id' => '', 'text' => PULL_DOWN_DEFAULT),
							array('id' => 'Druid', 'text' => 'Druid'),
							array('id' => 'Hunter', 'text' => 'Hunter'),
							array('id' => 'Mage', 'text' => 'Mage'),
							array('id' => 'Paladin', 'text' => 'Paladin'),
							array('id' => 'Priest', 'text' => 'Priest'),
							array('id' => 'Rogue', 'text' => 'Rogue'),
							array('id' => 'Shaman', 'text' => 'Shaman'),
							array('id' => 'Warlock', 'text' => 'Warlock'),
							array('id' => 'Warrior', 'text' => 'Warrior')
						);
*/
$char_class_array = array( 	array('id' => '', 'text' => PULL_DOWN_DEFAULT),
							array('id' => 'Hunter', 'text' => 'Hunter'),
							array('id' => 'Mage', 'text' => 'Mage'),
							array('id' => 'Rogue', 'text' => 'Rogue')
						);

$char_gender_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT),
							array('id' => 'Male', 'text' => 'Male'),
							array('id' => 'Female', 'text' => 'Female')
							);

$equipment_array = array(	array('id' => '', 'text' => PULL_DOWN_DEFAULT),
							array('id' => 'Geared', 'text' => 'Geared'),
							array('id' => 'Stripped', 'text' => 'Stripped')
							);

$mount_array = array(	array('id' => '', 'text' => PULL_DOWN_DEFAULT),
						array('id' => 'No mount', 'text' => 'No mount'),
						array('id' => 'Level 40 mount', 'text' => 'Level 40 mount'),
						array('id' => 'Epic Mount', 'text' => 'Epic Mount')
					);

for ($i=50; $i <= 70; $i++) {
	$main_char_lvl_array[] = array('id' => $i, 'text' => $i);
}

for ($i=1; $i <= 70; $i++) {
	$others_char_lvl_array[] = array('id' => $i, 'text' => $i);
}

$do = $HTTP_GET_VARS['do'];
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

switch($do) {
	case "3":
		$has_alt_char_info = false;
		$error = false;
		
		if (!tep_not_null($ss_account_buyback['name'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_NAME_ERROR);
      	}
		
      	if (strlen($ss_account_buyback['email_address']) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add_session('account_buyback', ENTRY_EMAIL_ADDRESS_ERROR);
	    } else if (tep_validate_email($ss_account_buyback['email_address']) == false) {
	      	$error = true;
	      	$messageStack->add_session('account_buyback', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
	    }
	    
    	if (!tep_not_null($ss_account_buyback['main_char_server'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_SERVER_ERROR);
      	}
      	
    	if (!tep_not_null($ss_account_buyback['main_char_lvl'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_LVL_ERROR);
      	}
      	
    	if (!tep_not_null($ss_account_buyback['main_char_race'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_RACE_ERROR);
      	}
    	
    	if (!tep_not_null($ss_account_buyback['main_char_class'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_CLASS_ERROR);
      	}
    	
    	if (!tep_not_null($ss_account_buyback['main_char_gender'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_GENDER_ERROR);
      	}
      	
    	if (!tep_not_null($ss_account_buyback['main_char_equipment'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT_ERROR);
      	}
      	
    	if (!tep_not_null($ss_account_buyback['main_char_mount'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_MOUNT_ERROR);
      	}
      	
    	if (!tep_not_null($ss_account_buyback['main_char_gold']) || !preg_match("/^[\d]+$/", $ss_account_buyback['main_char_gold'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_GOLD_ERROR);
      	}
      	
      	if (!isset($ss_account_buyback['acc_status'])) {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_STATUS_ERROR);
      	}
    	
    	if (!isset($ss_account_buyback['acc_cdkey'])) {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_CDKEY_ERROR);
      	}
      	
      	if (!isset($ss_account_buyback['acc_secret'])) {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_SECRET_ERROR);
      	}
    	
    	if (!tep_not_null($ss_account_buyback['asking_price']) || !is_numeric($ss_account_buyback['asking_price'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_ASKING_PRICE_ERROR);
      	}
      	
      	if ($ss_account_buyback['acc_cdkey'] != '1' || $ss_account_buyback['acc_secret'] != '1' || $ss_account_buyback['main_char_equipment'] != 'Geared') {
    		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_CANNOT_ACCEPT_ERROR);
    	}
    	
      	if ($error) {
    		tep_redirect(tep_href_link(FILENAME_ACCOUNT_BUYBACK, 'do=1&cPath='. $cPath));
    		exit();
    	} else {
			$server_info_select_sql = "	SELECT p.products_cat_path 
		            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
		            					INNER JOIN " . TABLE_PRODUCTS . " AS p
		            						ON (p2c.products_id = p.products_id)
		            					WHERE p2c.categories_id = '".tep_db_input($ss_account_buyback['main_char_server'])."' 
		            						AND p2c.products_is_link=0
		            						AND p.custom_products_type_id=0
		            						AND p.products_bundle=''
											AND p.products_bundle_dynamic=''";
		    $server_info_result_sql = tep_db_query($server_info_select_sql);
			$server_info_row = tep_db_fetch_array($server_info_result_sql);
			$cat_display_name = tep_display_category_path($server_info_row['products_cat_path'], $ss_account_buyback['main_char_server'], 'catalog', false);
			
			$email_greeting = tep_get_email_greeting($ss_account_buyback['name'], '', '');
			
			$email_content = EMAIL_CONTENTS_HEADER;
			$email_content .= sprintf(EMAIL_SECTION_PARTICULARS, $ss_account_buyback['name'], $ss_account_buyback['email_address']);
			$email_content .= 'Game: ' . $accepted_games_array[$current_category_id] . "\n";
			$email_content .= sprintf(EMAIL_SECTION_MAIN_CHARACTER, $cat_display_name, $ss_account_buyback['main_char_lvl'], $ss_account_buyback['main_char_race'], $ss_account_buyback['main_char_class'], $ss_account_buyback['main_char_gender'], $ss_account_buyback['main_char_equipment'], $ss_account_buyback['main_char_mount'], $ss_account_buyback['main_char_gold']);
			$email_content .= sprintf(EMAIL_SECTION_ACCOUNT_INFO, $ss_account_buyback['acc_status'] == '1' ? OPTION_ACTIVE : OPTION_INACTIVE, $ss_account_buyback['acc_cdkey'] == '1' ? OPTION_AVAILABLE : OPTION_NOT_AVAILABLE, $ss_account_buyback['acc_secret'] == '1' ? OPTION_REMEMBER : OPTION_FORGOTTEN, $ss_account_buyback['asking_price'], $ss_account_buyback['acc_buyback_comments']);
			$email_content .= "\n" . BUYBACK_SECTION_ALTERNATE_CHAR . "\n" . EMAIL_SEPARATOR . "\n";
			
			for ($alt_char_cnt=0; $alt_char_cnt < 3; $alt_char_cnt++) {
				if (tep_not_null($ss_account_buyback['alt_char']['server'][$alt_char_cnt])) {
					$alt_realm_info_select_sql = "	SELECT p.products_cat_path 
					            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
					            					INNER JOIN " . TABLE_PRODUCTS . " AS p
					            						ON (p2c.products_id = p.products_id)
					            					WHERE p2c.categories_id = '".tep_db_input($ss_account_buyback['alt_char']['server'][$alt_char_cnt])."' 
					            						AND p2c.products_is_link=0
					            						AND p.custom_products_type_id=0
					            						AND p.products_bundle=''
														AND p.products_bundle_dynamic=''";
				    $alt_realm_info_result_sql = tep_db_query($alt_realm_info_select_sql);
					$alt_realm_info_row = tep_db_fetch_array($alt_realm_info_result_sql);
					
					$alt_realm_display_name = tep_display_category_path($alt_realm_info_row['products_cat_path'], $ss_account_buyback['alt_char']['server'][$alt_char_cnt], 'catalog', false);
					
					
					$email_content .= sprintf(EMAIL_SECTION_ALT_CHARACTER, $alt_realm_display_name, $ss_account_buyback['alt_char']['level'][$alt_char_cnt], $ss_account_buyback['alt_char']['class'][$alt_char_cnt]);
					
					$has_alt_char_info = true;
				}
			}
			
			if (!$has_alt_char_info)	$email_content .= TEXT_BUYBACK_ACCOUNT_NO_ALT_CHARACTER . "\n\n";
			$email_content .= "\n" . EMAIL_CONTENTS_FOOTER . "\n\n" . EMAIL_FOOTER;
			$email_content = $email_greeting . $email_content;
			
			tep_mail($ss_account_buyback['name'], $ss_account_buyback['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_SUBJECT)), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, ACCOUNT_BUYBACK_ADMIN_EMAIL_ADDRESS);
			
			tep_session_unregister('ss_account_buyback');
		}
		
		break;
	case "2":
		$error = false;
		
		$ss_account_buyback['name'] = tep_db_prepare_input($HTTP_POST_VARS['name']);
		if (!tep_not_null($HTTP_POST_VARS['name'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_NAME_ERROR);
      	}
		
    	$ss_account_buyback['email_address'] = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
      	if (strlen($HTTP_POST_VARS['email_address']) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add_session('account_buyback', ENTRY_EMAIL_ADDRESS_ERROR);
	    } elseif (tep_validate_email($HTTP_POST_VARS['email_address']) == false) {
	      	$error = true;
	      	$messageStack->add_session('account_buyback', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
	    }
	    
    	$ss_account_buyback['main_char_server'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_server']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_server'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_SERVER_ERROR);
      	}
      	
    	$ss_account_buyback['main_char_lvl'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_lvl']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_lvl'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_LVL_ERROR);
      	}
      	
    	$ss_account_buyback['main_char_race'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_race']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_race'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_RACE_ERROR);
      	}
    	
    	$ss_account_buyback['main_char_class'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_class']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_class'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_CLASS_ERROR);
      	}
    	
    	$ss_account_buyback['main_char_gender'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_gender']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_gender'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_GENDER_ERROR);
      	}
      	
    	$ss_account_buyback['main_char_equipment'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_equipment']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_equipment'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT_ERROR);
      	}
      	
    	$ss_account_buyback['main_char_mount'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_mount']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_mount'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_MOUNT_ERROR);
      	}
      	
    	$ss_account_buyback['main_char_gold'] = tep_db_prepare_input($HTTP_POST_VARS['main_char_gold']);
    	if (!tep_not_null($HTTP_POST_VARS['main_char_gold']) || !preg_match("/^[\d]+$/", $HTTP_POST_VARS['main_char_gold'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_CHAR_GOLD_ERROR);
      	}
      	
      	if (isset($HTTP_POST_VARS['acc_status'])) {
        	$ss_account_buyback['acc_status'] = tep_db_prepare_input($HTTP_POST_VARS['acc_status']);
      	} else {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_STATUS_ERROR);
      	}
    	
    	if (isset($HTTP_POST_VARS['acc_cdkey'])) {
        	$ss_account_buyback['acc_cdkey'] = tep_db_prepare_input($HTTP_POST_VARS['acc_cdkey']);
      	} else {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_CDKEY_ERROR);
      	}
      	
    	if (isset($HTTP_POST_VARS['acc_secret'])) {
        	$ss_account_buyback['acc_secret'] = tep_db_prepare_input($HTTP_POST_VARS['acc_secret']);
      	} else {
      		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_MAIN_ACC_SECRET_ERROR);
      	}
    	
    	$ss_account_buyback['asking_price'] = tep_db_prepare_input($HTTP_POST_VARS['asking_price']);
    	if (!tep_not_null($HTTP_POST_VARS['asking_price']) || !is_numeric($HTTP_POST_VARS['asking_price'])) {
        	$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_ASKING_PRICE_ERROR);
      	}
      	
    	$ss_account_buyback['acc_buyback_comments'] = tep_db_prepare_input($HTTP_POST_VARS['acc_buyback_comments']);
    	
    	if (isset($HTTP_POST_VARS['alt_char'])) {
    		if (is_array($HTTP_POST_VARS['alt_char']) && count($HTTP_POST_VARS['alt_char'])) {
    			$ss_account_buyback['alt_char'] = $HTTP_POST_VARS['alt_char'];
    			
    			for ($alt_cnt=0; $alt_cnt < 3; $alt_cnt++) {
    				if (tep_not_null($HTTP_POST_VARS['alt_char']['server'][$alt_cnt])) {
    					if (!tep_not_null($HTTP_POST_VARS['alt_char']['level'][$alt_cnt])) {
    						$error = true;
    						$messageStack->add_session('account_buyback', sprintf(ENTRY_BUYBACK_ALT_CHAR_LVL_ERROR, ($alt_cnt+1) ));
    					}
    					
    					if (!tep_not_null($HTTP_POST_VARS['alt_char']['class'][$alt_cnt])) {
    						$error = true;
    						$messageStack->add_session('account_buyback', sprintf(ENTRY_BUYBACK_ALT_CHAR_CLASS_ERROR, ($alt_cnt+1) ));
    					}
    				}
    			}
    		}
    	}
    	
    	if ($HTTP_POST_VARS['acc_cdkey'] != '1' || $HTTP_POST_VARS['acc_secret'] != '1' || $HTTP_POST_VARS['main_char_equipment'] != 'Geared') {
    		$error = true;
        	$messageStack->add_session('account_buyback', ENTRY_BUYBACK_CANNOT_ACCEPT_ERROR);
    	}
    	
    	if ($error) {
    		tep_redirect(tep_href_link(FILENAME_ACCOUNT_BUYBACK, 'do=1&cPath='. $cPath));
    		exit();
    	}
    	
		break;
	case "1":
		if (!in_array($current_category_id, array_keys($accepted_games_array))) {
			tep_redirect(tep_href_link(FILENAME_ACCOUNT_BUYBACK));
    		exit();
		}
		
		break;
	default:
		break;
}

if (isset($current_category_id) && $current_category_id > 0)	$cPath_new = tep_get_path($current_category_id);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_ACCOUNT_BUYBACK, '', 'SSL'));

$content = CONTENT_ACCOUNT_BUYBACK;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>