<?
/*
  	$Id: account_edit.php,v 1.51 2014/12/29 08:15:41 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/profile/index');
require(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');

$user_os = tep_get_os($HTTP_USER_AGENT);
$phone_verify_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';
$last_page = (isset($_REQUEST['trace']) ? $_REQUEST['trace'] : '');
$order_id = isset($_GET['oid']) ? tep_db_prepare_input($_GET['oid']) : '';

if (tep_not_null($last_page)) {
	$_SESSION['trace'] = $last_page;
}

$log_object = new log_files($customer_id);
$oauth_login = oauth::is_oauth_login();

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_EDIT);

if (isset($HTTP_POST_VARS['action']) && ($HTTP_POST_VARS['action'] == 'process')) {
	$address_update = false;
	
	$edit_profile_gender = tep_not_null($HTTP_POST_VARS['edit_profile_gender']) ? tep_db_prepare_input($HTTP_POST_VARS['edit_profile_gender']) : "";
	$password_current = tep_not_null($HTTP_POST_VARS['edit_profile_current_password']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_current_password'])) : "";
	$password_new = tep_not_null($HTTP_POST_VARS['edit_profile_password']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_password'])) : "";
	$password_confirmation = tep_not_null($HTTP_POST_VARS['edit_profile_confirm_password']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_confirm_password'])) : "";
	
	$edit_profile_firstname = tep_not_null($HTTP_POST_VARS['edit_profile_firstname']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_firstname'])) : "";
	$edit_profile_lastname = tep_not_null($HTTP_POST_VARS['edit_profile_lastname']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_lastname'])) : "";
	$edit_profile_contact_number = tep_not_null($HTTP_POST_VARS['edit_profile_contact_number']) ? strip_tags($HTTP_POST_VARS['edit_profile_contact_number']) : "";
	$edit_profile_email = tep_not_null($HTTP_POST_VARS['edit_profile_email']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_email'])) : "";
    $edit_profile_country_code = tep_not_null($HTTP_POST_VARS['edit_profile_country_code']) ? tep_db_prepare_input($HTTP_POST_VARS['edit_profile_country_code']) : "";
    
	$customer_select_sql = "	SELECT customers_country_dialing_code_id, customers_telephone, customers_password 
								FROM " . TABLE_CUSTOMERS . " 
								WHERE customers_id = '" . (int)$customer_id . "'";
	$customer_result_sql = tep_db_query($customer_select_sql);
	$customer_row = tep_db_fetch_array($customer_result_sql);
	
    if (ACCOUNT_DOB == 'true') {
		$month = tep_db_prepare_input($HTTP_POST_VARS['dob_month']);
		$day = tep_db_prepare_input($HTTP_POST_VARS['dob_day']);
		$year = tep_db_prepare_input($HTTP_POST_VARS['dob_year']);
		
		$dob = $month . "/" . $day . "/" . $year;
    }
	
    $error = false;
	
	// Compare user input current password with database stored current password
	if (tep_not_null($password_current) && strlen($password_current) < ENTRY_PASSWORD_MIN_LENGTH) {
		$error = true;
		$messageStack->add('account_edit', ENTRY_PASSWORD_CURRENT_ERROR);	
	}
	
	if (tep_not_null($password_current) && !tep_validate_password($password_current, $customer_row['customers_password'])) {
		$error = true;
		$messageStack->add('account_edit', ENTRY_PASSWORD_CURRENT_NOT_MATCH_ERROR);
	}
	
	// Password & Confirm Password error messages
	if (tep_not_null($password_current) && strlen($password_new) < ENTRY_PASSWORD_MIN_LENGTH) {
		$error = true;
		$messageStack->add('account_edit', ENTRY_PASSWORD_NEW_ERROR);
	}
	
	if (tep_not_null($password_current) && strlen($password_confirmation) < ENTRY_PASSWORD_MIN_LENGTH) {
		$error = true;
		$messageStack->add('account_edit', sprintf(ENTRY_PASSWORD_CONFIRMATION_ERROR, ENTRY_PASSWORD_MIN_LENGTH));
	}
	
	if (tep_not_null($password_current) && $password_new != $password_confirmation) {
		$error = true;
		$messageStack->add('account_edit', ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR);
	}
    
    if (ACCOUNT_GENDER == 'true') {
      	if ( ($edit_profile_gender != 'm') && ($edit_profile_gender != 'f') ) {
        	$error = true;
        	$messageStack->add('account_edit', ENTRY_GENDER_ERROR);
      	}
	}
	
    if (strlen($edit_profile_firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
      	$error = true;
      	$messageStack->add('account_edit', ENTRY_FIRST_NAME_ERROR);
    }
	
    if (strlen($edit_profile_lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
      	$error = true;
      	$messageStack->add('account_edit', ENTRY_LAST_NAME_ERROR);
    }
	
    if (tep_not_null($year) || tep_not_null($month) || tep_not_null($day)) {
    	$dob_entered = 1;
      	
    	$message_1 = ENTRY_DATE_OF_BIRTH_ERROR_1;
		$message_2 = ENTRY_DATE_OF_BIRTH_ERROR_2;
		$message_3 = ENTRY_DATE_OF_BIRTH_ERROR_3;
		$message_4 = ENTRY_DATE_OF_BIRTH_ERROR_4;
		    
		$day_month_message_1 = "";
		$day_month_message_2 = "";
		$count = 0;
		$month_day_message = 0;
		$year_message = "";
		
		if (!tep_not_null($month)) {
			$day_month_message_2 = $day_month_message_2 . " month";
			$message_1 = $message_1 . " month";
			$count = $count + 1;
			$month_day_message = 1;
		}
			    
		if (!tep_not_null($day)) {
			$month_day_message = 1;
			if ($count == 0) {
				$count = $count + 1;
				$day_month_message_2 = $day_month_message_2 . " day";
				$message_1 = $message_1 . " day";
			} else if($count == 1) {
				$count = $count + 1;
				$day_month_message_2 = $day_month_message_2 . " and day";
				$message_1 = $message_1 . " and day";
			}
		}
				
		if (!tep_not_null($year)) {
			$count++;
			$year_message = $message_4;
		}
				
		if ($count == 0) {
			$message_1 = '';	
			$message_2 = '';
			$message_3 = '';
		}
				
		if ($month_day_message < 1) {
			$message_1 = '';
			$message_2 = '';
			$message_3 = '';
		}
		
		if ($count > 0) {
			$error_message = $error_message . $message_1 . $day_month_message_1 . $message_2 . $day_month_message_2 . $message_3 . $year_message . "\n";
			$error = true;
			$dob_entered = 0;
			$messageStack->add('account_edit', $error_message);
		}
		
		if ($dob_entered == 1) {
			if (!checkdate(substr(tep_date_raw($dob), 4, 2), substr(tep_date_raw($dob), 6, 2), substr(tep_date_raw($dob), 0, 4))) {
        		$error = true;
	       	 	$messageStack->add('account_edit', ENTRY_DATE_OF_BIRTH_ERROR);
      		}
      	}

      	if (tep_day_diff(date("Y-m-d H:i:s"), $year . '-' . $month . '-' . $day . ' 00:00:00', 'year') > 0) {
      		$error = true;
	        $messageStack->add('account_edit', ENTRY_DATE_OF_BIRTH_FUTURE_ERROR);
      	}

      	if (tep_day_diff($year . '-' . $month . '-' . $day . ' 00:00:00', date("Y-m-d H:i:s"), 'year') > ENTRY_AGE_MAX) {
      		$error = true;
	        $messageStack->add('account_edit', ENTRY_DATE_OF_BIRTH_OVER_ERROR);
      	}
    }
	
    if (strlen($edit_profile_email) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
      	$error = true;
      	$messageStack->add('account_edit', ENTRY_EMAIL_ADDRESS_ERROR);
    }
	
	if (!tep_validate_email($edit_profile_email)) {
		$error = true;
		$messageStack->add('account_edit', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
	}
	
	$check_email_sql = "select customers_id from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($edit_profile_email) . "' and customers_id != '" . (int)$customer_id . "'";
    $check_email_query = tep_db_query($check_email_sql);
    $check_email = tep_db_num_rows($check_email_query);
	
    if ($check_email > 0) {
      	$error = true;
      	$messageStack->add('account_edit', ERROR_EMAIL_EXISTED);
    }
    
    if (!tep_not_empty($customer_row['customers_telephone'])) {
        $edit_profile_contact_number = tep_db_prepare_input(preg_replace('/[^\d]/', '', $edit_profile_contact_number));
        $edit_profile_contact_number = tep_parse_telephone($edit_profile_contact_number, $edit_profile_country_code, 'id');
        
        if (is_numeric($edit_profile_country_code) == false) {
            $error = true;
            $messageStack->add('account_edit', ENTRY_LOCATION_ERROR);
        }
        
        if (strlen($edit_profile_contact_number) < ENTRY_TELEPHONE_MIN_LENGTH) {
            $error = true;
            $messageStack->add('account_edit', ENTRY_TELEPHONE_NUMBER_ERROR);
        } else if (tep_is_mobile_num_exist($edit_profile_contact_number, $edit_profile_country_code, $customer_id)) {
            $error = true;
            $messageStack->add('account_edit', ENTRY_CONTACT_NUMBER_EXIST_ERROR);
        }
    }
    
    if (isset($HTTP_POST_VARS['edit_profile_billing_address1'])) {
    	$address_update = true;
    	
    	//if (ACCOUNT_COMPANY == 'true') $company = tep_not_null($HTTP_POST_VARS['company']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['company'])) : "";
	    $edit_profile_billing_address1 = tep_not_null($HTTP_POST_VARS['edit_profile_billing_address1']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_billing_address1'])) : "";
	    //if (ACCOUNT_SUBURB == 'true') $suburb = tep_not_null($HTTP_POST_VARS['suburb']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['suburb'])) : "";
	    $edit_profile_billing_address2 = tep_not_null($HTTP_POST_VARS['edit_profile_billing_address2']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_billing_address2'])) : "";
	    $edit_profile_billing_postcode = tep_not_null($HTTP_POST_VARS['edit_profile_billing_postcode']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_billing_postcode'])) : "";
	    $edit_profile_billing_city = tep_not_null($HTTP_POST_VARS['edit_profile_billing_city']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_billing_city'])) : "";
	    $edit_profile_billing_country = tep_not_null($HTTP_POST_VARS['edit_profile_billing_country']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['edit_profile_billing_country'])) : "";
	    $state = tep_not_null($HTTP_POST_VARS['state']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['state'])) : "";
	    
	   /* if (ACCOUNT_STATE == 'true') {
	      	if (isset($HTTP_POST_VARS['zone_id'])) {
	        	$zone_id = tep_not_null($HTTP_POST_VARS['zone_id']) ? tep_db_prepare_input($HTTP_POST_VARS['zone_id']) : "";
	      	} else {
	        	$zone_id = false;
	      	}
	      	$state = tep_not_null($HTTP_POST_VARS['state']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['state'])) : "";
	    }*/
		
	    if (tep_not_null($edit_profile_billing_address1) && strlen($edit_profile_billing_address1) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add('account_edit', ENTRY_STREET_ADDRESS_ERROR);
	    }
		
	    if (tep_not_null($edit_profile_billing_postcode) && strlen($edit_profile_billing_postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add('account_edit', ENTRY_POST_CODE_ERROR);
	    }
		
	    if (tep_not_null($edit_profile_billing_city) && strlen($edit_profile_billing_city) < ENTRY_CITY_MIN_LENGTH) {
	    	$error = true;
	      	$messageStack->add('account_edit', ENTRY_CITY_ERROR);
	    }
		
	    if (tep_not_null($edit_profile_billing_country) && !is_numeric($edit_profile_billing_country)) {
	      $error = true;
	      $messageStack->add('account_edit', ENTRY_COUNTRY_ERROR);
	    }
	    
	    /*if (ACCOUNT_STATE == 'true') {*/
	    if ($edit_profile_billing_country) {
	      	$zone_id = 0;
	      	$check_query = tep_db_query("SELECT zone_country_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$edit_profile_billing_country . "'");
	      	$entry_state_has_zones = tep_db_num_rows($check_query);
	      	
	      	if ($entry_state_has_zones > 0) {
	      		$zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$edit_profile_billing_country . "' AND zone_id = '" . (int)$state . "'");
	        	if (tep_db_num_rows($zone_query) == 1) {
	          		$zone = tep_db_fetch_array($zone_query);
	          		$zone_id = $zone['zone_id'];
	          		$state = '';
	        	}/* else {
	          		$error = true;
	          		$messageStack->add('account_edit', ENTRY_STATE_ERROR_SELECT);
	        	}*/
	      	} else {
	        	if (tep_not_null($state) && strlen($state) < ENTRY_STATE_MIN_LENGTH) {
	          		$error = true;
	          		$messageStack->add('account_edit', ENTRY_STATE_ERROR);
	        	}
	      	}
		}			
    }
	
//	if ($customers_security_obj->validate_customer_security_answer($HTTP_POST_VARS['answer'],'account_edit',ENTRY_MISMATCH_ANSWER_ERROR) === true) {
//		$error = true;
//	}
    
	// if no error
    if ($error == false) {
    	$customer_log_select_sql = "SELECT c.customers_firstname, c.customers_lastname, c.customers_gender, c.customers_email_address, c.customers_country_dialing_code_id, c.customers_telephone, c.customers_fax, c.customers_dob, c.customers_email_address, c.customers_login_sites, c.customers_default_address_id, ci.customers_info_changes_made 
									FROM " . TABLE_CUSTOMERS . " AS c 
									INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
										ON (c.customers_id = ci.customers_info_id) 
									WHERE customers_id='" . (int)$customer_id . "'";
		$customer_log_result_sql = tep_db_query($customer_log_select_sql);
		$customer_old_log_row = tep_db_fetch_array($customer_log_result_sql);
		
		$all_customers_info_changes_made = $customer_old_log_row['customers_info_changes_made'];
		
		$sql_data_array = array('customers_firstname' => $edit_profile_firstname,
        						'customers_lastname' => $edit_profile_lastname,
        						'customers_dob' => tep_date_raw($dob),
        						'customers_gender' => $edit_profile_gender
        						);
        
        if (tep_not_null($password_new)) {
        	$sql_data_array['customers_password'] = tep_encrypt_password($password_new);
            
            $change_password_email_text = EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE . "\n\n" . EMAIL_FOOTER;
            tep_mail($customer_old_log_row['customers_firstname'] . ' ' . $customer_old_log_row['customers_lastname'], $customer_old_log_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT)), $change_password_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        
		if ($customer_old_log_row['customers_email_address'] != $edit_profile_email) {
            $sql_data_array['customers_email_address'] =  $edit_profile_email;
            $sql_data_array['serial_number'] =  '';
            
            $select_email_verified = "	SELECT info_verified 
										FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
										WHERE customers_id = '" . (int)$customer_id . "' AND customers_info_value = '" . tep_db_input($edit_profile_email) . "' AND info_verification_type = 'email'";
			$email_verified_result_sql = tep_db_query($select_email_verified);
			if (tep_db_num_rows($email_verified_result_sql) < 1) {
				$email_sql_data_array = array(	'customers_id ' => (int)$customer_id,
					        					'customers_info_value' => $edit_profile_email,
					        					'serial_number' => NULL,
					        					'info_verified' => 0,
					        					'info_verification_type' => 'email');
				tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $email_sql_data_array);
			}
        }
        
        // When customer does not has mobile number registered. 
        if (!tep_not_empty($customer_row['customers_telephone'])) {
            $sql_data_array['customers_country_dialing_code_id'] = $edit_profile_country_code;
            $sql_data_array['customers_telephone'] = $edit_profile_contact_number;
        }
		
      	tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int)$customer_id . "'");
      	
		$im_select_sql = "	SELECT * FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " WHERE customer_id = '" . (int)$customer_id . "'";
		$im_select_sql_result = tep_db_query($im_select_sql);
		while ($im_select_row = tep_db_fetch_array($im_select_sql_result)) {
			$im_array['id'][] = $im_select_row['instant_message_accounts_id'];
			$im_array[$im_select_row['instant_message_type_id']][] = $im_select_row['instant_message_accounts_id'];
			
			if ($im_select_row['instant_message_type_id'] != 0) {
				$im_array['without_others'][] = $im_select_row['instant_message_accounts_id'];
			}
		}
		
		// Update existing IM Accounts
		$edit_instantmessageaccountid = isset($HTTP_POST_VARS['edit_instantmessageaccountid']) && is_array($HTTP_POST_VARS['edit_instantmessageaccountid']) ? $HTTP_POST_VARS['edit_instantmessageaccountid'] : array();
  		$edit_instantmessageaccounttype = isset($HTTP_POST_VARS['edit_instantmessageaccounttype']) && is_array($HTTP_POST_VARS['edit_instantmessageaccounttype']) ? $HTTP_POST_VARS['edit_instantmessageaccounttype'] : array();
  		$edit_instantmessageaccount = isset($HTTP_POST_VARS['edit_instantmessageaccount']) && is_array($HTTP_POST_VARS['edit_instantmessageaccount']) ? $HTTP_POST_VARS['edit_instantmessageaccount'] : array();
  		$edit_othersinstantmessageaccountid = isset($HTTP_POST_VARS['edit_othersinstantmessageaccountid']) && is_array($HTTP_POST_VARS['edit_othersinstantmessageaccountid']) ? $HTTP_POST_VARS['edit_othersinstantmessageaccountid'] : array();
  		$edit_othersinstantmessageaccounttype = isset($HTTP_POST_VARS['edit_othersinstantmessageaccounttype']) && is_array($HTTP_POST_VARS['edit_othersinstantmessageaccounttype']) ? $HTTP_POST_VARS['edit_othersinstantmessageaccounttype'] : array();
  		$edit_othersinstantmessageaccountname = isset($HTTP_POST_VARS['edit_othersinstantmessageaccountname']) && is_array($HTTP_POST_VARS['edit_othersinstantmessageaccountname']) ? $HTTP_POST_VARS['edit_othersinstantmessageaccountname'] : array();
  		$edit_othersinstantmessageaccount = isset($HTTP_POST_VARS['edit_othersinstantmessageaccount']) && is_array($HTTP_POST_VARS['edit_othersinstantmessageaccount']) ? $HTTP_POST_VARS['edit_othersinstantmessageaccount'] : array();

  		$result1 = (is_array($im_array['without_others']) && sizeof($im_array['without_others']) > 0) ? array_diff($im_array['without_others'], $edit_instantmessageaccountid) : $edit_instantmessageaccountid;
  		$result2 = (is_array($im_array[0]) && sizeof($im_array[0]) > 0) ? array_diff($im_array[0], $edit_othersinstantmessageaccountid) : $edit_othersinstantmessageaccountid;

			
  		if (sizeof($edit_instantmessageaccounttype) > 0) {
  			for($im_num=0; $im_num < count($edit_instantmessageaccounttype); $im_num++) {
  				if (isset($edit_instantmessageaccount[$im_num])) {
					$sql_data_array = array('customer_id' => $customer_id,
									      	'instant_message_type_id' => $edit_instantmessageaccounttype[$im_num],
									      	'instant_message_userid' => $edit_instantmessageaccount[$im_num]);
					
					if (in_array($edit_instantmessageaccountid[$im_num], $im_array['without_others'])) {
						tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array, 'update', "customer_id='".(int)$customer_id."' AND instant_message_accounts_id='".(int)$edit_instantmessageaccountid[$im_num]."'");
					}
				}
			}
		}
		
		if (sizeof($result1) > 0) {
			foreach ($result1 as $k1) {
				$im_delete_all_sql = "DELETE FROM ". TABLE_INSTANT_MESSAGE_ACCOUNTS ." WHERE customer_id='" . (int)$customer_id . "' AND instant_message_accounts_id='".(int)$k1."'";
				$im_delete_all_result = tep_db_query($im_delete_all_sql);
			}
		}
        
  		if (sizeof($edit_othersinstantmessageaccounttype) > 0) {
  			for($others=0; $others < count($edit_othersinstantmessageaccounttype); $others++) {
				if (isset($edit_othersinstantmessageaccount[$others]) && isset($edit_othersinstantmessageaccountname[$others])) {
					$sql_data_array = array('customer_id' => $customer_id,
											'instant_message_type_id' => $edit_othersinstantmessageaccounttype[$others],
									      	'instant_message_userid' => $edit_othersinstantmessageaccount[$others],
									      	'instant_message_remarks' => $edit_othersinstantmessageaccountname[$others]);
					
					if (in_array($edit_instantmessageaccountid[$others], $im_array['id'])) {
						tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array, 'update', "customer_id='".(int)$customer_id."' AND instant_message_accounts_id='".(int)$edit_othersinstantmessageaccountid[$others]."'");
					}
				}
			}
		}
		
		if (sizeof($result2) > 0) {
			foreach ($result2 as $k2) {
				$im_delete_all_sql = "DELETE FROM ". TABLE_INSTANT_MESSAGE_ACCOUNTS ." WHERE customer_id='" . (int)$customer_id . "' AND instant_message_accounts_id='".(int)$k2."'";
				$im_delete_all_result = tep_db_query($im_delete_all_sql);
			}
		}
		
   		$instantmessageaccountid = isset($HTTP_POST_VARS['instantmessageaccountid']) && is_array($HTTP_POST_VARS['instantmessageaccountid']) ? $HTTP_POST_VARS['instantmessageaccountid'] : array();
  		$instantmessageaccounttype = isset($HTTP_POST_VARS['instantmessageaccounttype']) && is_array($HTTP_POST_VARS['instantmessageaccounttype']) ? $HTTP_POST_VARS['instantmessageaccounttype'] : array();
  		$instantmessageaccount = isset($HTTP_POST_VARS['instantmessageaccount']) && is_array($HTTP_POST_VARS['instantmessageaccount']) ? $HTTP_POST_VARS['instantmessageaccount'] : array();
  		$othersinstantmessageaccounttype = isset($HTTP_POST_VARS['othersinstantmessageaccounttype']) && is_array($HTTP_POST_VARS['othersinstantmessageaccounttype']) ? $HTTP_POST_VARS['othersinstantmessageaccounttype'] : array();
  		$othersinstantmessageaccountname = isset($HTTP_POST_VARS['othersinstantmessageaccountname']) && is_array($HTTP_POST_VARS['othersinstantmessageaccountname']) ? $HTTP_POST_VARS['othersinstantmessageaccountname'] : array();
  		$othersinstantmessageaccount = isset($HTTP_POST_VARS['othersinstantmessageaccount']) && is_array($HTTP_POST_VARS['othersinstantmessageaccount']) ? $HTTP_POST_VARS['othersinstantmessageaccount'] : array();

  		if (sizeof($instantmessageaccounttype) > 0) {
  			for($im_counter=0; $im_counter < count($instantmessageaccounttype); $im_counter++) {
  				if (isset($instantmessageaccount[$im_counter])) {
					$sql_data_array = array('customer_id' => $customer_id,
									      	'instant_message_type_id' => $instantmessageaccounttype[$im_counter],
									      	'instant_message_userid' => $instantmessageaccount[$im_counter]);
					tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
				}
			}
		}
		
  		if (sizeof($othersinstantmessageaccounttype) > 0) {
  			for($others=0; $others < count($othersinstantmessageaccounttype); $others++) {
				if (isset($othersinstantmessageaccount[$others]) && isset($othersinstantmessageaccountname[$others])) {
					$sql_data_array = array('customer_id' => $customer_id,
									      	'instant_message_type_id' => $othersinstantmessageaccounttype[$others],
									      	'instant_message_userid' => $othersinstantmessageaccount[$others],
									      	'instant_message_remarks' => $othersinstantmessageaccountname[$others]);
					
					tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
				}
			}
		} 
		
      	tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set customers_info_date_account_last_modified = now() where customers_info_id = '" . (int)$customer_id . "'");
		$customer_log_result_sql = tep_db_query($customer_log_select_sql);
		$customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);
		
		$customer_changes_array = $log_object->detect_changes($customer_old_log_row, $customer_new_log_row);
		$customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);
		
		// Address Changes
		if ($address_update) {
			$sql_address_data_array = array('entry_firstname' => $edit_profile_firstname,
			                              	'entry_lastname' => $edit_profile_lastname,
			                              	'entry_gender' => $edit_profile_gender,
			                              	'entry_street_address' => $edit_profile_billing_address1,
			                              	'entry_suburb' => $edit_profile_billing_address2,
			                              	'entry_zone_id' => $zone_id,
			                              	'entry_state'	=> $state,
			                              	'entry_postcode' => $edit_profile_billing_postcode,
			                              	'entry_city' => $edit_profile_billing_city,
			                              	'entry_country_id' => (int)$edit_profile_billing_country);
			
			$customer_address_log_select_sql = "	SELECT entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id 
													FROM " . TABLE_ADDRESS_BOOK . " 
													WHERE address_book_id = '" . (int)$customer_old_log_row['customers_default_address_id'] . "'";
			$customer_address_old_log_result_sql = tep_db_query($customer_address_log_select_sql);
			
			if ($customer_address_old_log_row = tep_db_fetch_array($customer_address_old_log_result_sql)) {	// If found address
				if ((int)$customer_address_old_log_row["entry_zone_id"] > 0) {
					$customer_address_old_log_row["entry_state"] = tep_get_zone_name((int)$customer_address_old_log_row["entry_country_id"], (int)$customer_address_old_log_row["entry_zone_id"], '');
					unset($customer_address_old_log_row["entry_zone_id"]);
				}
				
				tep_db_perform(TABLE_ADDRESS_BOOK, $sql_address_data_array, 'update', "address_book_id = '" . (int)$customer_old_log_row['customers_default_address_id'] . "' AND customers_id ='" . (int)$customer_id . "'");
			} else {	// This customer has no address yet, insert new address
				$sql_address_data_array['customers_id'] = $customer_id;
	      		
	      		tep_db_perform(TABLE_ADDRESS_BOOK, $sql_address_data_array);
	      		$customer_new_address_id = tep_db_insert_id();
	      		
	      		$customer_address_data_array = array('customers_default_address_id' => $customer_new_address_id);
	      		tep_db_perform(TABLE_CUSTOMERS, $customer_address_data_array, 'update', "customers_id = '" . (int) $customer_id . "'");
	      		
	      		$_SESSION['customer_default_address_id'] = $customer_new_address_id;
			}
			
			$customer_address_new_log_result_sql = tep_db_query($customer_address_log_select_sql);
			$customer_address_new_log_row = tep_db_fetch_array($customer_address_new_log_result_sql);
			
			if ((int)$customer_address_new_log_row["entry_zone_id"] > 0) {
				$customer_address_new_log_row["entry_state"] = tep_get_zone_name((int)$customer_address_new_log_row["entry_country_id"], (int)$customer_address_new_log_row["entry_zone_id"], '');
				unset($customer_address_new_log_row["entry_zone_id"]);
			}

			$customer_address_changes_array = $log_object->detect_changes($customer_address_old_log_row, $customer_address_new_log_row);
			$customer_address_changes_formatted_array = $log_object->construct_log_message($customer_address_changes_array);
			
			$customer_changes_array = array_merge($customer_changes_array, $customer_address_changes_array);
			$customer_changes_formatted_array = array_merge($customer_changes_formatted_array, $customer_address_changes_formatted_array);
		} else {
			$sql_name_data_array = array('entry_firstname' => $edit_profile_firstname,
										 'entry_lastname' => $edit_profile_lastname,
										 'entry_gender' => $edit_profile_gender
										 );
			
			tep_db_perform(TABLE_ADDRESS_BOOK, $sql_name_data_array, 'update', "address_book_id = '" . (int)$customer_old_log_row['customers_default_address_id'] . "' AND customers_id ='" . (int)$customer_id . "'");
		}
		
		$all_customers_info_changes_made = $log_object->contruct_changes_string($customer_changes_array, $all_customers_info_changes_made);

		if (count($customer_changes_formatted_array)) {
			$changes_str = 'Changes made:' . "\n";
			
			for ($i=0; $i < count($customer_changes_formatted_array); $i++) {
				if (count($customer_changes_formatted_array[$i])) {
					foreach($customer_changes_formatted_array[$i] as $field => $res) {
						if (isset($res['plain_result']) && $res['plain_result'] == '1') {
							$changes_str .= $res['text'] . "\n";
						} else {
							$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
						}
					}
				}
			}
			$log_object->insert_customer_history_log($customer_new_log_row['customers_email_address'], $changes_str);
		}
	    
	    $sql_data_array = array('customers_info_changes_made' => $all_customers_info_changes_made);
		tep_db_perform(TABLE_CUSTOMERS_INFO, $sql_data_array, 'update', "customers_info_id = '" . (int)$customer_id . "'");
		
      	$sql_data_array = array('entry_firstname' => $edit_profile_firstname,
                              	'entry_lastname' => $edit_profile_lastname
                              	);
		if (ACCOUNT_GENDER == 'true') $sql_data_array['entry_gender'] = $edit_profile_gender;
		
		tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array, 'update', "customers_id = '" . (int)$customer_id . "' and address_book_id = '" . $_SESSION['customer_default_address_id'] . "'");
		
		// reset the session variables
      	$_SESSION['customer_first_name'] = $edit_profile_firstname;
      	$_SESSION['customer_last_name'] = $edit_profile_lastname;
      	$_SESSION['customer_country_id'] = $edit_profile_billing_country;
      	
        if ($oauth_login) {
            switch ($oauth_login) {
                case 'oauth':
                    oauth::Oauth_login_success();
                break;
                case 'redirect_url':
                    $redirect_url = $_SESSION['login_redirect_url'];
                    unset($_SESSION['login_redirect_url']);
                    tep_redirect($redirect_url);
                break;
            }
        }
        
      	if (isset($_SESSION['trace'])) {
      		$trace = $_SESSION['trace'];
      		unset($_SESSION['trace']);
      		
      		if ($trace == 'chkout') {
      			if ($cart->count_contents() > 0) {
      				tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
      			} else {
      				$messageStack->add_session('account', SUCCESS_ACCOUNT_UPDATED, 'success');
      				tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
      			}
			} else if ($trace == 'scchkout') {
				tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT));
      		} else if ($trace == 'bb_chkout') {
      			tep_redirect(tep_href_link(FILENAME_BUYBACK, 'action=buyback_checkout&pid='.$_REQUEST['pid'].'&gcat='.$_REQUEST['gcat'], 'SSL'));
      		} else if (tep_not_null($order_id)) {
      			if ($trace == 'chkout_succ') {	// back to when click edit on tel. no from checkout success
      				tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS, '', 'SSL'));
      			} else if ($trace == 'acc_history') {  // back to when click edit on tel. no from order history info page
      				tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_id, 'SSL'));
      			}
      		}
      	} else {
      		$messageStack->add_session('account', SUCCESS_ACCOUNT_UPDATED, 'success');
      		tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
      	}
	}
} else if (isset($_REQUEST['action']) && ($_REQUEST['action'] == 'validate_qna')) {
    if ($customers_security_obj->validate_customer_security_answer($_POST['answer'],'qna',ENTRY_MISMATCH_ANSWER_ERROR) === true) {

        $messageStack->add_session('qna', ENTRY_MISMATCH_ANSWER_ERROR);
	} else {
        $sql_data_array = array('customer_info_account_dormant' => 0);
		tep_db_perform(TABLE_CUSTOMERS_INFO, $sql_data_array, 'update', "customers_info_id = '" . $_SESSION['customer_id'] . "'");
        unset($_SESSION['need_sc_usage_qna']);
    }
    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
}

$account_sql = "SELECT customers_country_dialing_code_id, customers_gender, customers_firstname, customers_lastname, customers_dob, customers_email_address, customers_telephone, customers_default_address_id, account_activated 
				FROM " . TABLE_CUSTOMERS . " 
				WHERE customers_id ='" . (int)$customer_id . "'";
$account_result_sql = tep_db_query($account_sql);
$account = tep_db_fetch_array($account_result_sql);

if ($account['customers_dob']) {
	$dob = explode("/", tep_date_short($account['customers_dob']));
	$dob_day = $dob[1];
	$dob_month = $dob[0];
	$dob_year = $dob[2];
}

$im_type_sql = "SELECT * FROM instant_message_type";
$im_type_result_sql = tep_db_query($im_type_sql);
while ($im_type_row = tep_db_fetch_array($im_type_result_sql)) {
	$im_type[$im_type_row['instant_message_type_id']] = $im_type_row['instant_message_type_name'];
}

$im_sql = "SELECT * FROM instant_message_accounts WHERE customer_id = '" . (int)$customer_id . "'";
$im_result_sql = tep_db_query($im_sql);
$im_existed_number_of_accounts = tep_db_num_rows($im_result_sql);
while ($im_row = tep_db_fetch_array($im_result_sql)) {
	$im_account['id'][] = $im_row['instant_message_accounts_id'];
	$im_account['type'][] = $im_row['instant_message_type_id'];
	$im_account['userid'][] = $im_row['instant_message_userid'];
	$im_account['remarks'][] = $im_row['instant_message_remarks'];
}


$address_select_sql = "	SELECT entry_firstname, entry_lastname, entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id 
						FROM " . TABLE_ADDRESS_BOOK . " 
						WHERE address_book_id = '" . (int)$account['customers_default_address_id'] . "'";
$address_result_sql = tep_db_query($address_select_sql);
$address_row = tep_db_fetch_array($address_result_sql);

$select_email_verified = "	SELECT info_verified 
							FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
							WHERE customers_id = '" . (int)$customer_id . "' AND customers_info_value = '" . tep_db_input($account['customers_email_address']) . "' AND info_verification_type = 'email'";
$email_verified_result_sql = tep_db_query($select_email_verified);
if (tep_db_num_rows($email_verified_result_sql) < 1) {
	$sql_data_array = array('customers_id ' => (int)$customer_id,
        					'customers_info_value' => $account['customers_email_address'],
        					'serial_number' => NULL,
        					'info_verified' => 0,
        					'info_verification_type' => 'email');
	
	tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
	
	$email_verified_result_sql = tep_db_query($select_email_verified);
	$email_verified = tep_db_fetch_array($email_verified_result_sql);
}

$customer_complete_phone_info_array = tep_format_telephone($customer_id);
$complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'));

$content = CONTENT_ACCOUNT_EDIT;
//$javascript = 'form_check.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>