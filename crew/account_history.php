<?
/*
  	$Id: account_history.php,v 1.16 2014/12/29 08:17:05 weesiong Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/MyStoreOrders/a/index');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_HISTORY);

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type='.$orders_type, 'SSL'));

// 1 call function save to $all_product_type_array()
// 2. loop $all_product_type_array array and initiallise 2 new array ($product_type_default_html_array(), $product_type_icon_array())
$all_product_type_array = tep_get_product_type();

$product_type_default_html_array = array( 0 => '<td width="'.(100/count($all_product_type_array)).'" align="center">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>',
										  1 => '<td width="'.(100/count($all_product_type_array)).'" align="center">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>',
										  2 => '<td width="'.(100/count($all_product_type_array)).'" align="center">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>',
										  3 => '<td width="'.(100/count($all_product_type_array)).'" align="center">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>',
										  4 => '<td width="'.(100/count($all_product_type_array)).'" align="center">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>');

$product_type_icon_array = array( 0 => DIR_WS_ICONS . 'icon_cur.gif',
 								  1 => DIR_WS_ICONS . 'icon_pwl.gif',
								  2 => DIR_WS_ICONS . 'icon_cdk.gif',
								  3 => DIR_WS_ICONS . 'icon_sc.gif',
								  4 => DIR_WS_ICONS . 'icon_hla.gif');

$order_by_array = array( 'img_date'		=> 'o.date_purchased',
						 'img_status'	=> 'o.orders_status',
						 'img_item'		=> "cur $asc_desc, pwl $asc_desc, cdkeys",
						 'default'		=> 'orders_id');

$buyback_order_by_array = array( 'img_date'		=> 'brg.buyback_request_group_date',
								 'img_status'	=> 'brg.buyback_status_id',
								 /*'img_item'		=> "cur $asc_desc, pwl $asc_desc, cdkeys $asc_desc",  */
								 'default'		=> 'buyback_request_group_id');

$orders_type_title_array = array('current'		=> CURRENT_ORDERS_TITLE,
						   		 'completed'	=> COMPLETED_ORDERS_TITLE);

$order_status_desc_array = array('current'		=> QUESTION_AND_ANSWER_CURRENT_ORDER_CONTENTS,
						   		 'completed'	=> QUESTION_AND_ANSWER_COMPLETED_ORDER_CONTENTS);

$orders_type = !isset($orders_type_title_array[$_REQUEST['orders_type']]) ? 'current' : tep_db_prepare_input($_REQUEST['orders_type']);
$col = !isset($order_by_array[$_REQUEST['col']]) ? 'default' : tep_db_prepare_input($_REQUEST['col']);
$page = tep_db_prepare_input($_REQUEST['page']);

if (isset($_REQUEST['direction'])) {
	$direction = tep_db_prepare_input($_REQUEST['direction']);
}

$orders_type_array = array(	'current' => array(1, 2, 7, 8),
							'completed' => array(3),
							'' => array(2, 3, 4, 7, 8));

$asc_desc = ($direction == 1) ? "ASC" : "DESC";

function tep_draw_order_record_list($list_array=array(), $order_type="") {
	if (sizeof($list_array)) {
		$order_record_row_html = array(	'on_hold' => array(),
										'others' => array());
		$division = (90/5).'%';

		foreach($list_array as $list_status_id => $list_array1) {
			if (sizeof($list_array1)) {
				foreach($list_array1 as $list_orders_id => $list_array2) {
					$on_hold_line = $list_status_id == 8 ? ' background:#FFFDDB; ' : '';
					$on_hold_status = $list_status_id == 8 ? ' background:#FFFDDB; font-weight:bold; color:red; ' : '';

					$list_record = '
									<tr style="line-height:25px;'.$on_hold_line.'">
										<td class="dottedLine">&nbsp;</td>
										<td width="'.$division.'" style="padding-left:5px; font-weight:bold;" class="dottedLine"><a href="'.tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, tep_get_all_get_params(array('page', 'order_id')).'order_id='.$list_orders_id).'">'. $list_orders_id .'</a></td>
										<td class="dottedLine">&nbsp;</td>
										<td width="'.$division.'" style="padding-left:8px;" class="dottedLine">'.$list_array2['date_purchased'].'</td>
										<td class="dottedLine">&nbsp;</td>
										<td width="'.$division.'" style="text-align: center;'.$on_hold_status.'" class="dottedLine">'.$list_array2['orders_status'].'</td>
										<td class="dottedLine">&nbsp;</td>';
					if ($order_type != "cancelled") {
						$list_record .= '
										<td width="'.$division.'" style="text-align:center;" class="dottedLine">'.$list_array2['product_type'].'</td>
										<td class="dottedLine">&nbsp;</td>';
					}
					$list_record .= '
										<td width="'.$division.'" style="padding-right:5px; text-align:right;" class="dottedLine">'.$list_array2['order_total'].'</td>
										<td class="dottedLine">&nbsp;</td>
									</tr>';

					$order_record_row_html[($list_status_id == 8 ? 'on_hold' : 'others')][] = $list_record;
				}
			}
		}
	}

	return $order_record_row_html;
}


$content = CONTENT_ACCOUNT_HISTORY;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>