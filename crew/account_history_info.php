<?php

/*
  	$Id: account_history_info.php,v 1.29 2014/12/29 08:17:32 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

if ((int) $_REQUEST['order_id'] > 0) {
    tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/MyStoreOrders/a/viewHistoryInfo/p/order_id=' . (int) $_REQUEST['order_id']);
} else {
    tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/MyStoreOrders/a/index');
}

require_once(DIR_WS_CLASSES . 'custom_product.php');
require_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');

if (!tep_session_is_registered('customer_id')) {
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/buyback_system.php')) {
    include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/buyback_system.php');
}

if (isset($_REQUEST['keyident']))
    tep_show_base64_img($_REQUEST['keyident']);

//customer rating setting
$select_point_sql = "SELECT vip_rules_operator, vip_rules_value
					FROM " . TABLE_VIP_RULES . "
					WHERE vip_rules_key='VIP_ADD_SUPPLIER_ORDER_FULLY_COMPLETED'";
$select_point_result = tep_db_query($select_point_sql);
$select_point_row = tep_db_fetch_array($select_point_result);
$max_point = $select_point_row['vip_rules_operator'] . $select_point_row['vip_rules_value'];
$min_point = 1; // min point can't be zero
$off_set = 1;

$order_array = array();
if (isset($_REQUEST['s_orders_id'])) {
    $order_array = tep_array_unserialize($_REQUEST['s_orders_id']);
} else if (isset($_REQUEST['order_id']) && is_numeric($_REQUEST['order_id'])) {
    $order_array = array($_REQUEST['order_id']);
    $order_id = $_REQUEST['order_id'];
}

if (!count($order_array)) {
    tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
}

if ($_REQUEST["idents_arr"] && $_REQUEST["product_id"] && $order_id) {
    //CDKey Download requested.
    $idents_arr = tep_array_unserialize($_REQUEST["idents_arr"]);
    tep_make_base64_img_zip($idents_arr, $_REQUEST["product_id"], $order_id);
}

if ($order_id) {
	if ($history_type != 'buyback') {
		                $orders_select_sql = "	SELECT o.customers_id, o.orders_status
		 					FROM " . TABLE_ORDERS . " AS o
                                                        LEFT JOIN `" . TABLE_ORDERS_EXTRA_INFO . "` AS `oei`
                                                            ON `oei`.`orders_id` = `o`.`orders_id`
                                                            AND `oei`.`orders_extra_info_key` = 'site_id'
		 					WHERE o.customers_id = '" . (int)$customer_id . "'
                                                                AND (`oei`.`orders_extra_info_value` IS NULL OR `oei`.`orders_extra_info_value` = '4')
                                                                AND o.orders_id = '". (int)$order_id . "'";
		$customer_info_query = tep_db_query($orders_select_sql);
	} else {
		$customer_info_query = tep_db_query("select customers_id from buyback_request_group where buyback_request_group_id = '". (int)$order_id . "'");
	}

	$customer_info = tep_db_fetch_array($customer_info_query);
	if ($customer_info['customers_id'] != $_SESSION['customer_id']) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
	}

	// redirect to listing page if "Canceled" status
	if (($history_type != 'buyback') && ($customer_info['orders_status'] == '5')) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
	}
}

// @************ - My Account phase 2 only for Buyer Order
require(DIR_WS_CLASSES . 'order.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_HISTORY_INFO);

require_once(DIR_WS_CLASSES . 'payment_methods.php');

// get orders type
function tep_get_orders_type($status_id) {
    $status_type = array(1 => 'current',
        2 => 'current',
        3 => 'completed',
        5 => 'cancelled',
        7 => 'current',
        8 => 'current'
    );
    return $status_type[$status_id];
}

// get order's expiry countdown hours and minutes
function tep_get_order_expiry_time($order_obj='', $pm='') {
    $date_purchased = explode(" ", $order_obj->info['date_purchased']);
    $date = explode("-", $date_purchased[0]);
    $time = explode(":", $date_purchased[1]);

    $pm_cancel_period = 21600;
    $pm_cancel_time_in_secs = mktime($time[0], $time[1] + $pm_cancel_period, $time[2], $date[1], $date[2], $date[0]);
    $current_time_in_secs = mktime(date('H'), date('i'), date('s'), date('m'), date('d'), date('Y'));
    $pm_cancel_hours_left = ($pm_cancel_time_in_secs - $current_time_in_secs) / 3600;
    $pm_cancel_minutes_left = ($pm_cancel_hours_left - intval($pm_cancel_hours_left)) * 60;

    $pm_cancel_period_array = array('hrs' => intval($pm_cancel_hours_left),
        'mins' => intval($pm_cancel_minutes_left)
    );

    return $pm_cancel_period_array;
}

// get order status history details
function tep_get_order_status_history_details($order_id = '') {
    global $languages_id, $default_languages_id;

    $status_history_array = array();

    $order_status_select_query = "	SELECT os.orders_status_name, osh.date_added, osh.comments, osh.customer_notified
									FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
									LEFT JOIN " . TABLE_ORDERS_STATUS . " AS os
										ON (osh.orders_status_id = os.orders_status_id)
									WHERE osh.orders_id = '" . (int) $_REQUEST["order_id"] . "' AND (os.language_id = 1 OR os.orders_status_id IS NULL) ORDER BY osh.date_added";
    $order_status_select_res = tep_db_query($order_status_select_query);
    while ($order_status_select_row = tep_db_fetch_array($order_status_select_res)) {
        $orders_status_array[$order_id][] = array('status_id' => $order_status_select_row['orders_status_id'],
            'status_name' => $order_status_select_row['orders_status_name'],
            'status_read' => $order_status_select_row['customer_notified'],
            'status_date' => $order_status_select_row['date_added'],
            'status_comment' => $order_status_select_row['comments']);
    }

    return $orders_status_array;
}

// get products amount delivered
function tep_get_products_amount_delivered() {
    global $order_id;

    $products_amount_delivered_query = "SELECT SUM(products_good_delivered_price) as amount_delivered FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = '" . $order_id . "' AND parent_orders_products_id = '0' ";
    $products_amount_delivered_result = tep_db_query($products_amount_delivered_query);
    $products_amount_delivered_array = tep_db_fetch_array($products_amount_delivered_result);

    $amount_delivered = $products_amount_delivered_array['amount_delivered'];
    return $amount_delivered;
}

// get products amount refunded
function tep_get_products_amount_refunded() {
    global $order_id;

    $products_amount_refunded_query = "SELECT SUM(products_canceled_price) as amount_refunded FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = '" . $order_id . "' AND parent_orders_products_id = '0' ";
    $products_amount_refunded_result = tep_db_query($products_amount_refunded_query);
    $products_amount_refunded_array = tep_db_fetch_array($products_amount_refunded_result);

    $amount_refunded = $products_amount_refunded_array['amount_refunded'];
    return $amount_refunded;
}

// get orders products id which refunded
function tep_get_products_id_refunded() {
    global $order_id;

    $products_id_refunded_query = "SELECT orders_products_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = '" . $order_id . "' AND products_canceled_price > 0 AND parent_orders_products_id = '0' ";
    $products_id_refunded_result = tep_db_query($products_id_refunded_query);
    while ($products_id_refunded_row = tep_db_fetch_array($products_id_refunded_result)) {
        $products_id_refunded_array[] = $products_id_refunded_row['orders_products_id'];
    }
    return (sizeof($products_id_refunded_array)) ? $products_id_refunded_array : false;
}

$content = CONTENT_ACCOUNT_HISTORY_INFO;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>