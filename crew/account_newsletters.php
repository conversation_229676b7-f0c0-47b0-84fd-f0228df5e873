<?php
/*
  	$Id: account_newsletters.php,v 1.7 2014/12/29 08:18:13 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

tep_redirect(HTTP_SHASSO_PORTAL);

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_NEWSLETTERS);


if (isset($_REQUEST['action']) && ($_REQUEST['action'] == 'process')) {
	if (isset($_REQUEST['newsletter']) && count($_REQUEST['newsletter'])) {
		$newsletter = implode(',', $_REQUEST['newsletter']);
    } else {
      	$newsletter = '0';
    }
	
    tep_db_query("update " . TABLE_CUSTOMERS . " set customers_newsletter = '" . tep_db_input($newsletter) . "' where customers_id = '" . (int)$customer_id . "'");
	
    $messageStack->add_session('account', SUCCESS_NEWSLETTER_UPDATED, 'success');
    tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
}


// Get all the available newsletters
$newsletter_query = tep_db_query("select customers_newsletter from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
$newsletter_row = tep_db_fetch_array($newsletter_query);
$customer_subscribed_newsletter = explode(',', $newsletter_row['customers_newsletter']);

$newsletter_group_array = array();

$newsletter_group_select_sql = "SELECT newsletters_groups_id, newsletters_groups_name 
								FROM " . TABLE_NEWSLETTERS_GROUPS . " 
								WHERE module = 'newsletter'
								ORDER BY newsletters_groups_sort_order";
$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);

while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
	//$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = $newsletter_group_row['newsletters_groups_name'];
	$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = HTML_NEWSLETTER_BOX;
}
/*
while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
	$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']]['group_name'] = $newsletter_group_row['newsletters_groups_name'];
	//$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']]['newsletters_id'] = $newsletter_group_row['newsletters_id'];
	//$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']]['title'] = $newsletter_group_row['title'];
}
*/
// End of get all the available newsletters

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_NEWSLETTERS, '', 'SSL'));

$content = CONTENT_ACCOUNT_NEWSLETTERS;
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');

?>