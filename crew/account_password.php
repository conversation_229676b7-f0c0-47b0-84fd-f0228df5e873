<?
/*
  	$Id: account_password.php,v 1.7 2009/07/16 04:48:07 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'));

if (!tep_session_is_registered('customer_id')) {
    $navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_PASSWORD);

if (isset($HTTP_POST_VARS['action']) && ($HTTP_POST_VARS['action'] == 'process')) {
	$password_current = tep_db_prepare_input($HTTP_POST_VARS['password_current']);
    $password_new = tep_db_prepare_input($HTTP_POST_VARS['password_new']);
    $password_confirmation = tep_db_prepare_input($HTTP_POST_VARS['password_confirmation']);
	
    $error = false;
	
    if (strlen($password_current) < ENTRY_PASSWORD_MIN_LENGTH) {
      	$error = true;
		
      	$messageStack->add('account_password', ENTRY_PASSWORD_CURRENT_ERROR);
    } else if (strlen($password_new) < ENTRY_PASSWORD_MIN_LENGTH) {
      	$error = true;
		
      	$messageStack->add('account_password', ENTRY_PASSWORD_NEW_ERROR);
    } else if ($password_new != $password_confirmation) {
      	$error = true;
		
      	$messageStack->add('account_password', ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING);
    }
	
    if ($error == false) {
      	$check_customer_query = tep_db_query("select customers_password from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
      	$check_customer = tep_db_fetch_array($check_customer_query);
		
      	if (tep_validate_password($password_current, $check_customer['customers_password'])) {
        	tep_db_query("update " . TABLE_CUSTOMERS . " set customers_password = '" . tep_encrypt_password($password_new) . "' where customers_id = '" . (int)$customer_id . "'");
        	tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set customers_info_date_account_last_modified = now() where customers_info_id = '" . (int)$customer_id . "'");
        	
        	$messageStack->add_session('account', SUCCESS_PASSWORD_UPDATED, 'success');
        	
        	tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
      	} else {
        	$error = true;
			
        	$messageStack->add('account_password', ERROR_CURRENT_PASSWORD_NOT_MATCHING);
		}
	}
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_PASSWORD, '', 'SSL'));

$content = CONTENT_ACCOUNT_PASSWORD;
$javascript = 'form_check.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>