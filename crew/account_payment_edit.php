<?
/*
  	$Id: account_payment_edit.php,v 1.8 2014/12/29 08:18:39 weesiong Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');

# redirect to MY ACCOUNT > Seller Payment > Payment Options
tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/Disbursement/a/index');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_PAYMENT_EDIT);

require(DIR_WS_CLASSES . 'payment_module_info.php');

$content = CONTENT_ACCOUNT_PAYMENT_EDIT;

$pm_object = new payment_module_info($customer_id, 'customers');
$pm_object->set_max_entry(5);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch($subaction) {
	case "insert_pm":
		$_SESSION['pm_form_fields_param'] = isset($_POST['pm_field']) ? $_POST['pm_field'] : '';
		$subaction_res = $pm_object->insert_payment_account($_POST, $messageStack, $content);
		if ($subaction_res) {
			unset($_SESSION['pm_form_fields_param']);
			if (isset($_REQUEST['p_ref']) && $_REQUEST['p_ref']='buyback') {
				tep_redirect(tep_href_link(FILENAME_BUYBACK));
			} else {
				tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params(array('action', 'subaction')) ));
			}
		} else {
			;
		}

		break;
	case "update_pm":
		$_SESSION['pm_form_fields_param'] = isset($_POST['pm_field']) ? $_POST['pm_field'] : '';
		$subaction_res = $pm_object->update_payment_account($_POST, $messageStack, $content);
		if ($subaction_res) {
			unset($_SESSION['pm_form_fields_param']);
			tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params(array('action', 'subaction')) ));
		} else {
			;
		}

		break;
	case "confirm_delete_pm":
		$pm_object->delete_payment_account($HTTP_GET_VARS['book_id'], $messageStack, $content);
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params(array('action', 'subaction', 'book_id')) ));
		break;
	default:
		unset($_SESSION['pm_form_fields_param']);
		break;
}

switch ($action) {
	case "add_pm":
		$form_content = $pm_object->add_payment_account(FILENAME_ACCOUNT_PAYMENT_EDIT);

		break;
	case "edit_pm":
		$form_content = $pm_object->add_payment_account(FILENAME_ACCOUNT_PAYMENT_EDIT, $HTTP_GET_VARS['book_id']);

		break;
	case 'add_payment_method':
		if ($_POST['payment_method']) {
			$_SESSION[$form_session_name]['payment_method'][][$_POST['payment_method']] = array();
		}
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
		break;

	case 'remove_payment_method':
		if (isset($_GET['remove_row_num'])) {
			unset($_SESSION[$form_session_name]['payment_method'][$_GET['remove_row_num']]);
		}
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
		break;
}//end switch

if (!tep_not_null($action)) {
	$form_content = $pm_object->list_payment_account_book(FILENAME_ACCOUNT_PAYMENT_EDIT);
}





$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, '', 'SSL'));

$javascript = 'form_check.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>