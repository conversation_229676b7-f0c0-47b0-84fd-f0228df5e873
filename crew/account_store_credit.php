<?
/*
  	$Id: account_store_credit.php,v 1.11 2014/12/29 08:19:33 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/storeCredit/index#convert');
require_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');

$last_page = (isset($_REQUEST['trace']) ? $_REQUEST['trace'] : '');

if (tep_not_null($last_page)) {
	$_SESSION['trace'] = $last_page;
}

$log_object = new log_files($customer_id);

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_STORE_CREDIT);

// Get customer info
$store_credit_sql = "	SELECT DISTINCT sc_currency_id, sc_conversion_date
						FROM " . TABLE_COUPON_GV_CUSTOMER . " 
						WHERE customer_id ='" . (int)$customer_id . "'";
$store_credit_result_sql = tep_db_query($store_credit_sql);
if (tep_db_num_rows($store_credit_result_sql) == 0) {
    tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
} else {
	$store_credit_row = tep_db_fetch_array($store_credit_result_sql);
	$customer_currency_turned_off = false;
	if (!isset($currencies->currencies[$currencies->get_code_by_id($store_credit_row['sc_currency_id'])])) {
		$customer_currency_turned_off = true;
	}
}

localization::verify_gst_condition();

// Get all system defined payment methods type
$sc_type_array = array();
$system_defined_send_pm_array = payment_module_info::get_system_defined_payment_account_selection(false);
for ($pm_cnt=0; $pm_cnt < count($system_defined_send_pm_array); $pm_cnt++) {
	$sc_type_array[] = $system_defined_send_pm_array[$pm_cnt]['id'];
}

$sc_pending_payments_found = false;
if (count($sc_type_array) > 0) {
	// Get pending/processing store payments by customer
	$store_payment_sql = "	SELECT store_payments_id 
							FROM " . TABLE_STORE_PAYMENTS . "
							WHERE user_id = '" . (int)$customer_id . "'
								AND user_role = 'customers'
								AND store_payments_status IN (1,2)
								AND store_payments_methods_id IN ('" . implode("','", $sc_type_array) . "')";
	$store_payment_result_sql = tep_db_query($store_payment_sql);
	if (tep_db_num_rows($store_payment_result_sql) > 0) {
		$sc_pending_payments_found = true;
		$warning_message = ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND;
	}
}

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case 'process':
			if ($customer_currency_turned_off === false) {
			    $error = false;
			    $success = false;
			    $sc_object = new store_credit($customer_id);
				$to_currency = tep_not_null($_REQUEST['currency_selection']) ? tep_db_prepare_input($_REQUEST['currency_selection']) : "";
				
				/*-- GST :: convert to GST currency only --*/
				if (tep_not_null($_SESSION['RegionGST']['tax_title']) && ($_SESSION['RegionGST']['currency'] != $to_currency)) {
					$to_currency = '';
				}
				
				if (tep_not_null($to_currency) == false) {
					$error = true;
					$messageStack->add('account_store_credit', ERROR_INVALID_STORE_CREDIT_CURRENCY_SELECTION);
				} else if ($sc_pending_payments_found == true) {
					$error = true;
					$messageStack->add('account_store_credit', ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND);
				} else {
					$currency_id = array_search($to_currency, $currencies->internal_currencies);
					if (is_null($currency_id) || $currency_id == false) {
						$error = true;
						$messageStack->add('account_store_credit', ERROR_INVALID_STORE_CREDIT_CURRENCY_SELECTION);
					}
				}
				
				if ($error == false) {
					$success = $sc_object->store_credit_conversion($_REQUEST, $messageStack);
				}
				
				if ($success == true) {
                    $nextURL = tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT, '', 'SSL');
					$messageStack->add_session('account_store_credit', SUCCESS_STORE_CREDIT_CONVERSION, 'success');
                    
					if ($customers_upkeep_obj->getUpkeepValue('account_sc_nextPage') !== FALSE) {
                        $nextURL = $customers_upkeep_obj->getUpkeepValue('account_sc_nextPage');
                        $customers_upkeep_obj->unsetUpkeep('account_sc_nextPage');
                        $customers_upkeep_obj->updateUpkeep();
                    }
                    
                    tep_redirect($nextURL);
				}
			}
			break;
	}
} else {
    if (isset($_GET['nextURL']) && tep_not_empty($_GET['nextURL'])) {
        $customers_upkeep_obj->mergeUpkeep(array('account_sc_nextPage' => tep_db_input($_GET['nextURL'])));
        $customers_upkeep_obj->updateUpkeep();
    } else if ($customers_upkeep_obj->getUpkeepValue('account_sc_nextPage') !== FALSE) {
        $customers_upkeep_obj->unsetUpkeep('account_sc_nextPage');
        $customers_upkeep_obj->updateUpkeep();
    }
}
$to_currency = isset($_REQUEST['to_curr']) ? $_REQUEST['to_curr'] : "";

// current customer store credit currency code
$sc_currency_code = $currencies->get_code_by_id($store_credit_row['sc_currency_id']);

// current customer store credit amount
$sc_object = new store_credit($customer_id);
$sc_amounts_array = $sc_object->get_current_credits_balance($customer_id);
$store_credit_total_amount = 0;
$store_credit_total_amount = ($sc_amounts_array['sc_reverse']+$sc_amounts_array['sc_irreverse']) - ($sc_amounts_array['sc_reverse_reserve_amt']+$sc_amounts_array['sc_irreverse_reserve_amt']);

// Get Currencies list for drop down
$currency_drop_down_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
$currency_list = $currencies->get_currency_set();
for ($i=0, $n=sizeof($currency_list); $i<$n; $i++) {
	// check and filter out current store credit currency from conversion drop down
	if ($currency_list[$i]['id'] != $sc_currency_code) {
		if (!tep_not_null($_SESSION['RegionGST']['tax_title']) || (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst') && tep_not_null($_SESSION['RegionGST']['tax_title']) && ($currency_list[$i]['id'] == $_SESSION['RegionGST']['currency']))) {
			$currency_drop_down_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
		}
	}
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT, '', 'SSL'));

$content = CONTENT_ACCOUNT_STORE_CREDIT;
$javascript = 'form_check.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>