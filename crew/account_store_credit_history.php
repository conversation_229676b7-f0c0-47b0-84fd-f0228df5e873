<?
/*
  	$Id: account_store_credit_history.php,v 1.3 2014/12/29 08:20:16 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/storeCredit/statement');
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_STORE_CREDIT_HISTORY);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

// current customer store credit amount
$sc_object = new store_credit($customer_id);
$sc_amounts_array = $sc_object->get_current_credits_balance($customer_id);
$store_credit_total_amount = 0;
$store_credit_total_amount = ($sc_amounts_array['sc_reverse']+$sc_amounts_array['sc_irreverse']) - ($sc_amounts_array['sc_reverse_reserve_amt']+$sc_amounts_array['sc_irreverse_reserve_amt']);
$sc_total_amt_str = $currencies->format($store_credit_total_amount, false, $currencies->get_code_by_id($sc_amounts_array['sc_currency_id']));

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_HISTORY, '', 'SSL'));

$content = CONTENT_ACCOUNT_STORE_CREDIT_HISTORY;
//$javascript = 'form_check.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>