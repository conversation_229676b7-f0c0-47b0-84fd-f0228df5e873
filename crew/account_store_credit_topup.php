<?
/*
  	$Id: account_store_credit_topup.php,v 1.5 2014/12/29 08:21:01 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/storeCredit/index#topup');
require_once(DIR_WS_CLASSES . 'store_credit.php');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_STORE_CREDIT_TOPUP);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case 'process':
			break;
	}
}

$store_credit_cPath = store_credit::get_cpath_info();

include_once(DIR_WS_CLASSES . 'product.php');
$page_info = new product($store_credit_cPath, 3);
$page_info->cpath_check();

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL'));

$content = CONTENT_ACCOUNT_STORE_CREDIT_TOPUP;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>