<?
/*
	$Id: address_book_process.php,v 1.20 2010/09/23 12:38:16 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'log.php');
$log_object = new log_files($customer_id);

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ADDRESS_BOOK_PROCESS);

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'deleteconfirm') && isset($HTTP_GET_VARS['delete']) && is_numeric($HTTP_GET_VARS['delete'])) {
	if ( (int)$HTTP_GET_VARS['delete'] == $_SESSION['customer_default_address_id']) {
      	$messageStack->add_session('addressbook', WARNING_PRIMARY_ADDRESS_DELETION, 'warning');
      	tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));	
	} else {
		tep_db_query("DELETE FROM " . TABLE_ADDRESS_BOOK . " WHERE address_book_id = '" . (int)$HTTP_GET_VARS['delete'] . "' AND customers_id = '" . (int)$customer_id . "'");
		$messageStack->add_session('addressbook', SUCCESS_ADDRESS_BOOK_ENTRY_DELETED, 'success');
		tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
	}
}

// error checking when updating or adding an entry
$process = false;
if (isset($HTTP_POST_VARS['action']) && (($HTTP_POST_VARS['action'] == 'process') || ($HTTP_POST_VARS['action'] == 'update'))) {
	$process = true;
    $error = false;
	
	if ( (isset($HTTP_POST_VARS['primary']) && ($HTTP_POST_VARS['primary'] == 'on')) || ($HTTP_GET_VARS['edit'] == $_SESSION['customer_default_address_id']) ) {
		$customer_account_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address FROM " . TABLE_CUSTOMERS ." WHERE customers_id = '" . (int)$customer_id . "'";
		$customer_account_result_sql = tep_db_query($customer_account_select_sql);
		$customer_account_row = tep_db_fetch_array($customer_account_result_sql);
		
		$default_account_setting = false;	// This should set to true if do not allow changes of name and gender
		//$default_account_setting = true;
	} else {
		$default_account_setting = false;
	}
	
	if ($default_account_setting) {
		if (ACCOUNT_GENDER == 'true') $gender = tep_db_prepare_input($customer_account_row["customers_gender"]);
		$firstname = tep_db_prepare_input($customer_account_row["customers_firstname"]);
    	$lastname = tep_db_prepare_input($customer_account_row["customers_lastname"]);
	} else {
		if (ACCOUNT_GENDER == 'true') $gender = tep_db_prepare_input($HTTP_POST_VARS['gender']);
		$firstname = tep_db_prepare_input($HTTP_POST_VARS['firstname']);
    	$lastname = tep_db_prepare_input($HTTP_POST_VARS['lastname']);
	}
    
    if (ACCOUNT_COMPANY == 'true') $company = tep_db_prepare_input($HTTP_POST_VARS['company']);
    $street_address = tep_db_prepare_input($HTTP_POST_VARS['street_address']);
    if (ACCOUNT_SUBURB == 'true') $suburb = tep_db_prepare_input($HTTP_POST_VARS['suburb']);
    $postcode = tep_db_prepare_input($HTTP_POST_VARS['postcode']);
    $city = tep_db_prepare_input($HTTP_POST_VARS['city']);
    $country = tep_db_prepare_input($HTTP_POST_VARS['country']);
    
    if (ACCOUNT_STATE == 'true') {
      	if (isset($HTTP_POST_VARS['zone_id'])) {
        	$zone_id = tep_db_prepare_input($HTTP_POST_VARS['zone_id']);
      	} else {
        	$zone_id = false;
      	}
      	$state = tep_db_prepare_input($HTTP_POST_VARS['state']);
    }
	
	if (!$default_account_setting) {
	    if (ACCOUNT_GENDER == 'true') {
	      	if ( ($gender != 'm') && ($gender != 'f') ) {
	        	$error = true;
	        	$messageStack->add('addressbook', ENTRY_GENDER_ERROR);
	      	}
	    }
		
	    if (strlen($firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add('addressbook', ENTRY_FIRST_NAME_ERROR);
	    }
		
	    if (strlen($lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
	      	$error = true;
	      	$messageStack->add('addressbook', ENTRY_LAST_NAME_ERROR);
	    }
	}
	
    if (strlen($street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
      	$error = true;
      	$messageStack->add('addressbook', ENTRY_STREET_ADDRESS_ERROR);
    }
	
    if (strlen($postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
      	$error = true;
      	$messageStack->add('addressbook', ENTRY_POST_CODE_ERROR);
    }

    if (strlen($city) < ENTRY_CITY_MIN_LENGTH) {
      $error = true;

      $messageStack->add('addressbook', ENTRY_CITY_ERROR);
    }

    if (!is_numeric($country)) {
      $error = true;

      $messageStack->add('addressbook', ENTRY_COUNTRY_ERROR);
    }
	
    if (ACCOUNT_STATE == 'true') {
      	$zone_id = 0;
      	$check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country . "'");
      	$check = tep_db_fetch_array($check_query);
      	$entry_state_has_zones = ($check['total'] > 0);
      	if ($entry_state_has_zones == true) {
      		$zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$country . "' AND zone_id = '" . (int)$state . "'");
        	if (tep_db_num_rows($zone_query) == 1) {
          		$zone = tep_db_fetch_array($zone_query);
          		$zone_id = $zone['zone_id'];
        	} else {
          		$error = true;
          		$messageStack->add('addressbook', ENTRY_STATE_ERROR_SELECT);
        	}
      	} else {
        	if (strlen($state) < ENTRY_STATE_MIN_LENGTH) {
          		$error = true;
          		$messageStack->add('addressbook', ENTRY_STATE_ERROR);
        	}
      	}
	}
    
    if ($error == false) {
      	$sql_data_array = array('entry_firstname' => $firstname,
                              	'entry_lastname' => $lastname,
                              	'entry_street_address' => $street_address,
                              	'entry_postcode' => $postcode,
                              	'entry_city' => $city,
                              	'entry_country_id' => (int)$country);
		
      	if (ACCOUNT_GENDER == 'true') $sql_data_array['entry_gender'] = $gender;
      	if (ACCOUNT_COMPANY == 'true') $sql_data_array['entry_company'] = $company;
      	if (ACCOUNT_SUBURB == 'true') $sql_data_array['entry_suburb'] = $suburb;
      	if (ACCOUNT_STATE == 'true') {
        	if ($zone_id > 0) {
          		$sql_data_array['entry_zone_id'] = (int)$zone_id;
          		$sql_data_array['entry_state'] = '';
        	} else {
          		$sql_data_array['entry_zone_id'] = '0';
          		$sql_data_array['entry_state'] = $state;
        	}
      	}
		
  		$customer_log_select_sql = "SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, c.customers_login_sites, a.entry_street_address, a.entry_suburb, a.entry_postcode, a.entry_city, a.entry_state, a.entry_zone_id, a.entry_country_id, ci.customers_info_changes_made 
  									FROM " . TABLE_CUSTOMERS . " c 
  									INNER JOIN " . TABLE_ADDRESS_BOOK . " a 
  										ON (c.customers_default_address_id = a.address_book_id) 
  									INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
  										ON (c.customers_id = ci.customers_info_id) 
  									WHERE c.customers_id = '" . (int)$customer_id ."'";
  		$customer_log_result_sql = tep_db_query($customer_log_select_sql);
		$customer_old_log_row = tep_db_fetch_array($customer_log_result_sql); 
		
      	if ($HTTP_POST_VARS['action'] == 'update') {
			$customers_login_sites = $customer_old_log_row['customers_login_sites'];
			$all_customers_info_changes_made = $customer_old_log_row['customers_info_changes_made'];
			
			if ((int)$customer_old_log_row["entry_zone_id"] > 0) {
				$customer_old_log_row["entry_state"] = tep_get_zone_name((int)$customer_old_log_row["entry_country_id"], (int)$customer_old_log_row["entry_zone_id"], '');
				unset($customer_old_log_row["entry_zone_id"]);
			}
      		tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array, 'update', "address_book_id = '" . (int)$HTTP_GET_VARS['edit'] . "' and customers_id ='" . (int)$customer_id . "'");
			
			// reregister session variables
          	$customer_country_id = $country;
          	$customer_zone_id = (($zone_id > 0) ? (int)$zone_id : '0');
        	if ( (isset($HTTP_POST_VARS['primary']) && ($HTTP_POST_VARS['primary'] == 'on')) || ($HTTP_GET_VARS['edit'] == $_SESSION['customer_default_address_id']) ) {
          		$customer_first_name = $firstname;
          		$_SESSION['customer_default_address_id'] = (int)$HTTP_GET_VARS['edit'];
          		
          		$sql_data_array = array('customers_firstname' => $firstname,
                                  		'customers_lastname' => $lastname,
                                  		'customers_default_address_id' => (int)$HTTP_GET_VARS['edit']);
          		if (ACCOUNT_GENDER == 'true') $sql_data_array['customers_gender'] = $gender;
          		
          		tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int)$customer_id . "'");
          		
          		$customer_log_result_sql = tep_db_query($customer_log_select_sql);
				$customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);
				if ((int)$customer_new_log_row["entry_zone_id"] > 0) {
					$customer_new_log_row["entry_state"] = tep_get_zone_name((int)$customer_new_log_row["entry_country_id"], (int)$customer_new_log_row["entry_zone_id"], '');
					unset($customer_new_log_row["entry_zone_id"]);
				}
          		$customer_changes_array = $log_object->detect_changes($customer_old_log_row, $customer_new_log_row);
          		
          		$customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);
          		
          		$all_customers_info_changes_made = $log_object->contruct_changes_string($customer_changes_array, $all_customers_info_changes_made);
        		
        		if (count($customer_changes_formatted_array)) {
					$changes_str = 'Changes made:' . "\n";
					for ($i=0; $i < count($customer_changes_formatted_array); $i++) {
						if (count($customer_changes_formatted_array[$i])) {
							foreach($customer_changes_formatted_array[$i] as $field => $res) {
								if (isset($res['plain_result']) && $res['plain_result'] == '1') {
									$changes_str .= $res['text'] . "\n";
								} else {
									$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
								}
							}
						}
					}
					$log_object->insert_customer_history_log($customer_account_row['customers_email_address'], $changes_str);
				}
        	}
      	} else {
        	$sql_data_array['customers_id'] = (int)$customer_id;
        	tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array);
        	$new_address_book_id = tep_db_insert_id();
			
			// reregister session variables
        	if (isset($HTTP_POST_VARS['primary']) && ($HTTP_POST_VARS['primary'] == 'on')) {
          		$customer_first_name = $firstname;
          		$customer_country_id = $country;
          		$customer_zone_id = (($zone_id > 0) ? (int)$zone_id : '0');
          		$_SESSION['customer_default_address_id'] = $new_address_book_id;
          		
          		$sql_data_array = array('customers_default_address_id' => $new_address_book_id);
          		$sql_data_array = array('customers_firstname' => $firstname,
                                  		'customers_lastname' => $lastname);
          		if (ACCOUNT_GENDER == 'true') $sql_data_array['customers_gender'] = $gender;
          		
          		$sql_data_array['customers_default_address_id'] = $new_address_book_id;

          		tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int)$customer_id . "'");

          		$customer_log_result_sql = tep_db_query($customer_log_select_sql);
				$customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);
				if ((int)$customer_new_log_row["entry_zone_id"] > 0) {
					$customer_new_log_row["entry_state"] = tep_get_zone_name((int)$customer_new_log_row["entry_country_id"], (int)$customer_new_log_row["entry_zone_id"], '');
					unset($customer_new_log_row["entry_zone_id"]);
				}
          		$customer_changes_array = $log_object->detect_changes($customer_old_log_row, $customer_new_log_row);
          		$customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);
          		$all_customers_info_changes_made = $log_object->contruct_changes_string($customer_changes_array, $all_customers_info_changes_made);
        		
        		if (count($customer_changes_formatted_array)) {
					$changes_str = 'Changes made:' . "\n";
					for ($i=0; $i < count($customer_changes_formatted_array); $i++) {
						if (count($customer_changes_formatted_array[$i])) {
							foreach($customer_changes_formatted_array[$i] as $field => $res) {
								if (isset($res['plain_result']) && $res['plain_result'] == '1') {
									$changes_str .= $res['text'] . "\n";
								} else {
									$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
								}
							}
						}
					}
					$log_object->insert_customer_history_log($customer_account_row['customers_email_address'], $changes_str);
				}
        	}
      	}

      	if ( (isset($HTTP_POST_VARS['primary']) && ($HTTP_POST_VARS['primary'] == 'on')) || ($HTTP_GET_VARS['edit'] == $_SESSION['customer_default_address_id']) ) {
			if (tep_not_null($HTTP_GET_VARS['edit'])) {
				$address_book_id = $HTTP_GET_VARS['edit'];
			} else if (tep_not_null($new_address_book_id)) {
				$address_book_id = $new_address_book_id;
			}
		}

      	$messageStack->add_session('addressbook', SUCCESS_ADDRESS_BOOK_ENTRY_UPDATED, 'success');
		
		if (isset($_REQUEST['back_url']) ) {
			tep_redirect(tep_href_link($_REQUEST['back_url'], tep_get_all_get_params(array('edit', 'back_url')), 'SSL'));
		} else {
			tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
		}
	}
}

if (isset($HTTP_GET_VARS['edit']) && is_numeric($HTTP_GET_VARS['edit'])) {
	$entry_query = tep_db_query("SELECT entry_gender, entry_company, entry_firstname, entry_lastname, entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_zone_id, entry_country_id FROM " . TABLE_ADDRESS_BOOK . " WHERE customers_id = '" . (int)$customer_id . "' AND address_book_id = '" . (int)$HTTP_GET_VARS['edit'] . "'");
    
    if (!tep_db_num_rows($entry_query)) {
      	$messageStack->add_session('addressbook', ERROR_NONEXISTING_ADDRESS_BOOK_ENTRY);
      	tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
    }
	$entry = tep_db_fetch_array($entry_query);
} else if (isset($HTTP_GET_VARS['delete']) && is_numeric($HTTP_GET_VARS['delete'])) {
	if ($HTTP_GET_VARS['delete'] == $_SESSION['customer_default_address_id']) {
      	$messageStack->add_session('addressbook', WARNING_PRIMARY_ADDRESS_DELETION, 'warning');
      	tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
    } else {
      	$check_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where address_book_id = '" . (int)$HTTP_GET_VARS['delete'] . "' and customers_id = '" . (int)$customer_id . "'");
      	$check = tep_db_fetch_array($check_query);
      	
      	if ($check['total'] < 1) {
        	$messageStack->add_session('addressbook', ERROR_NONEXISTING_ADDRESS_BOOK_ENTRY);
        	tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
      	}
	}
} else {
	$entry = array();
}

if (!isset($HTTP_GET_VARS['delete']) && !isset($HTTP_GET_VARS['edit'])) {
	if (tep_count_customer_address_book_entries() >= MAX_ADDRESS_BOOK_ENTRIES) {
		$messageStack->add_session('addressbook', ERROR_ADDRESS_BOOK_FULL);
      	tep_redirect(tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));
	}
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'));

if (isset($HTTP_GET_VARS['edit']) && is_numeric($HTTP_GET_VARS['edit'])) {
	$breadcrumb->add(NAVBAR_TITLE_MODIFY_ENTRY, tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'edit=' . $HTTP_GET_VARS['edit'], 'SSL'));
} else if (isset($HTTP_GET_VARS['delete']) && is_numeric($HTTP_GET_VARS['delete'])) {
	$breadcrumb->add(NAVBAR_TITLE_DELETE_ENTRY, tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'delete=' . $HTTP_GET_VARS['delete'], 'SSL'));
} else {
    $breadcrumb->add(NAVBAR_TITLE_ADD_ENTRY, tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, '', 'SSL'));
}

$content = CONTENT_ADDRESS_BOOK_PROCESS;
$javascript = $content . '.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>