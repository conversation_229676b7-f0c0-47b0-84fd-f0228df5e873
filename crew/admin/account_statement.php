<?
/*
  	$Id: account_statement.php,v 1.5 2010/03/02 09:55:56 wilson.sun Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'payments.php');

$currencies = new currencies();
$payments_object = new payments($login_id, $login_email_address);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch($subaction) {
	case "manual_deduct":
		$subaction_res = $payments_object->manual_deduct_amount($HTTP_POST_VARS, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STATEMENT, tep_get_all_get_params(array('subaction'))));
		
		break;
	case "manual_add":
		$subaction_res = $payments_object->manual_add_amount($HTTP_POST_VARS, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STATEMENT, tep_get_all_get_params(array('subaction'))));
		
		break;
    case "add_comment":
		$subaction_res = $payments_object->add_comment($HTTP_POST_VARS, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STATEMENT, tep_get_all_get_params(array('subaction'))));
		
		break;
	default:
		;	// Nothing to perform
		break;
}

switch($action) {
	case "show_report":
		$header_title = HEADER_FORM_ACC_STAT_TITLE;
		$form_content = $payments_object->show_acc_statement(FILENAME_ACCOUNT_STATEMENT, 'acc_statement_inputs', $_REQUEST, $messageStack);
		
		break;
	case "reset_session":
    	unset($_SESSION['acc_statement_inputs']);
    	tep_redirect(tep_href_link(FILENAME_ACCOUNT_STATEMENT));
    	
    	break;
	default:
		$header_title = HEADER_FORM_ACC_STAT_TITLE;
		$form_content = $payments_object->search_acc_statement(FILENAME_ACCOUNT_STATEMENT, 'acc_statement_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
<?	if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); } ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>