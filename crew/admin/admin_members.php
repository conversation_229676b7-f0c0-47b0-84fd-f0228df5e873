<?php
/*
  	$Id: admin_members.php,v 1.40 2010/10/14 08:56:50 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php'); 
include_once(DIR_WS_FUNCTIONS . 'admin_members.php');

require(DIR_WS_CLASSES . 'log.php');
$system_log_object = new log_files($login_id);

$current_boxes = DIR_FS_ADMIN . DIR_WS_BOXES;
define('CUSTOM_MAX_DISPLAY_MEMBERS_RESULTS', 250);

$manage_admin_members_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_MANAGE_MEMBERS');
$manage_admin_groups_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_MANAGE_GROUPS');
$manage_admin_credit_limit_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_MANAGE_CREDIT_LIMIT');
$assign_admin_group_members_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_GROUP_TRANSFER');
$edit_group_permission_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_EDIT_GROUP_PERMISSION');
$export_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_EXPORT_PERMISSION');
$reactivate_admin_members_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_REACTIVATE_ADMIN_MEMBERS');
$manage_admin_cdkey_view_limit_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_MANAGE_CDKEY_VIEW_LIMIT');
$manage_admin_team_credit_limit_permission = tep_admin_files_actions(FILENAME_ADMIN_MEMBERS, 'ADMIN_MANAGE_TEAM_CREDIT_LIMIT');

$currentDate = date("Y-m-d H:i:s");

if ($HTTP_GET_VARS['action']) {
	switch ($HTTP_GET_VARS['action']) {
		case 'export_permissions':
			if (!$export_permission) {
				tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, tep_get_all_get_params(array('action'))));
			}
			tep_set_time_limit(120);

			$languages_id = 1;
			$admin_files_arr = array();
			
			$get_admin_files_categories_select_sql = "	SELECT admin_files_id, admin_files_name, admin_groups_id 
														FROM " . TABLE_ADMIN_FILES . " 
														WHERE admin_files_to_boxes = '0' ORDER BY admin_files_name";
												
			$get_admin_files_categories_result_sql = tep_db_query($get_admin_files_categories_select_sql);
			while ($get_admin_files_categories_row = tep_db_fetch_array($get_admin_files_categories_result_sql)) {
				$admin_files_arr[] = array("files_id" => $get_admin_files_categories_row['admin_files_id'], 
											"files_name" => $get_admin_files_categories_row['admin_files_name'], 
											"groups_id" => $get_admin_files_categories_row['admin_groups_id'], 
											"files_level" => "1");
				
				$get_admin_files_select_sql = "	SELECT admin_files_id, admin_files_name, admin_groups_id 
												FROM " . TABLE_ADMIN_FILES . " 
												WHERE admin_files_to_boxes = '" . $get_admin_files_categories_row['admin_files_id'] . "' 
												ORDER BY admin_files_name";
										
				$get_admin_files_result_sql = tep_db_query($get_admin_files_select_sql);
				
				while ($get_admin_files_row = tep_db_fetch_array($get_admin_files_result_sql)) {
					$admin_files_arr[] = array("files_id" => $get_admin_files_row['admin_files_id'], 
												"files_name" => $get_admin_files_row['admin_files_name'], 
												"groups_id" => $get_admin_files_row['admin_groups_id'], 
												"files_level" => "2");
					$get_admin_files_action_select_sql = "	SELECT admin_files_actions_id, admin_files_actions_name, admin_groups_id 
															FROM " . TABLE_ADMIN_FILES_ACTIONS . " 
															WHERE admin_files_id = '" . $get_admin_files_row['admin_files_id'] . "' 
															ORDER BY admin_files_sort_order";
											
					$get_admin_files_action_result_sql = tep_db_query($get_admin_files_action_select_sql);
					$get_admin_files_action_num = tep_db_num_rows($get_admin_files_action_result_sql);
					if ($get_admin_files_action_num > 0) {
						while ($get_admin_files_action_row = tep_db_fetch_array($get_admin_files_action_result_sql)) {
							$admin_files_arr[] = array("files_id" => $get_admin_files_action_row['admin_files_actions_id'], 
														"files_name" => $get_admin_files_action_row['admin_files_actions_name'], 
														"groups_id" => $get_admin_files_action_row['admin_groups_id'], 
														"files_level" => "3");	
						}
					}
				}
			}
			
			if (sizeof($admin_files_arr) > 0) {
				$group_info_arr = tep_get_group_info();
				
				$export_csv_data = tep_generate_admin_permission_csv($group_info_arr, $admin_files_arr);
				if (tep_not_null($export_csv_data)) {
					$today_date = date("Ymd");
					$filename = 'group_permission_'.$today_date.'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				}
			}
			break;
		case 'new_member':
        	if (!$manage_admin_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
			break;
    	case 'member_new':
        	if (!$manage_admin_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	
    		if (!tep_validate_email($HTTP_POST_VARS['admin_email_address'])) {
    			$messageStack->add_session(ENTRY_EMAIL_ADDRESS_CHECK_ERROR, 'error');
    			tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&action=new_member'));
    		}
    		
        	$check_email_query = tep_db_query("select admin_email_address from " . TABLE_ADMIN . "");
        	while ($check_email = tep_db_fetch_array($check_email_query)) {
          		$stored_email[] = $check_email['admin_email_address'];
        	}
        	
        	if (in_array($HTTP_POST_VARS['admin_email_address'], $stored_email)) {
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&error=email&action=new_member'));
        	} else {
          		function randomize() {
            		$salt = "abchefghjkmnpqrstuvwxyz0123456789";
            		srand((double)microtime()*1000000); 
            		$i = 0;
    	    		while ($i <= 7) {
    					$num = rand() % 33;
    					$tmp = substr($salt, $num, 1);
    					$pass = $pass . $tmp;
    					$i++;
  	    			}
  	    			return $pass;
          		}
          		$makePassword = randomize();
                
                $sql_data_array = array('admin_groups_id' => '0',
                						'admin_firstname' => tep_db_prepare_input($HTTP_POST_VARS['admin_firstname']),
                    	              	'admin_lastname' => tep_db_prepare_input($HTTP_POST_VARS['admin_lastname']),
                                  		'admin_email_address' => tep_db_prepare_input($HTTP_POST_VARS['admin_email_address']),
                                  		'admin_password' => tep_encrypt_password($makePassword),
                                  		'admin_created' => 'now()');
        		
          		tep_db_perform(TABLE_ADMIN, $sql_data_array);
          		$admin_id = tep_db_insert_id();
        		
          		tep_mail($HTTP_POST_VARS['admin_firstname'] . ' ' . $HTTP_POST_VARS['admin_lastname'], $HTTP_POST_VARS['admin_email_address'], ADMIN_EMAIL_SUBJECT, sprintf(ADMIN_EMAIL_TEXT, $HTTP_POST_VARS['admin_firstname'], HTTP_SERVER . DIR_WS_ADMIN, $HTTP_POST_VARS['admin_email_address'], $makePassword, STORE_OWNER), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
          		
          		// begin system log - new admin member
          		$system_log_object->insert_system_log('New Admin Member', TABLE_ADMIN, $admin_id, 'admin_email_address', 'n/a', $HTTP_POST_VARS['admin_email_address']);
          		// end system log
          		
        		$messageStack->add_session(sprintf(SUCCESS_NEW_MEMBER_CREATED, $HTTP_POST_VARS['admin_email_address']), 'success');
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin_id));
        	}
        	break;
        case 'edit_member':
        	if (!$manage_admin_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	
        	if (!tep_admin_member_exist($HTTP_GET_VARS['mID'])){
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	break;
		case 'member_edit':
        	if (!$manage_admin_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	
        	if (!tep_admin_member_exist($HTTP_GET_VARS['mID'])) {
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	
        	$admin_id = tep_db_prepare_input($HTTP_POST_VARS['admin_id']);
        	$hiddenPassword = '-hidden-';
        	$stored_email[] = 'NONE';
        	
        	$check_email_query = tep_db_query("select admin_email_address from " . TABLE_ADMIN . " where admin_id <> " . $admin_id . "");
        	while ($check_email = tep_db_fetch_array($check_email_query)) {
          		$stored_email[] = $check_email['admin_email_address'];
        	}
        	
        	if (!tep_validate_email($HTTP_POST_VARS['admin_email_address'])) {
    			$messageStack->add_session(ENTRY_EMAIL_ADDRESS_CHECK_ERROR, 'error');
    			tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'].'&mID='.$HTTP_GET_VARS['mID'].'&action=edit_member'));
    		}
    		
        	if (in_array($HTTP_POST_VARS['admin_email_address'], $stored_email)) {
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID'] . '&error=email&action=edit_member'));
        	} else {
		        $member_edit_select_sql = " SELECT admin_firstname, admin_lastname, admin_email_address, admin_groups_id FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$admin_id . "'";
				$member_edit_result_sql = tep_db_query($member_edit_select_sql);
				$member_edit_row_old = tep_db_fetch_array($member_edit_result_sql);

          		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['admin_groups_id']),
                	                  	'admin_firstname' => tep_db_prepare_input($HTTP_POST_VARS['admin_firstname']),
                                  		'admin_lastname' => tep_db_prepare_input($HTTP_POST_VARS['admin_lastname']),
                                  		'admin_email_address' => tep_db_prepare_input($HTTP_POST_VARS['admin_email_address']),
                                  		'admin_modified' => 'now()');
        		
          		tep_db_perform(TABLE_ADMIN, $sql_data_array, 'update', 'admin_id = "' . $admin_id . '"');
          		tep_mail($HTTP_POST_VARS['admin_firstname'] . ' ' . $HTTP_POST_VARS['admin_lastname'], $HTTP_POST_VARS['admin_email_address'], ADMIN_EMAIL_SUBJECT, sprintf(ADMIN_EMAIL_TEXT, $HTTP_POST_VARS['admin_firstname'], HTTP_SERVER . DIR_WS_ADMIN, $HTTP_POST_VARS['admin_email_address'], $hiddenPassword, STORE_OWNER), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

				$member_edit_result_sql = tep_db_query($member_edit_select_sql);
				$member_edit_row_new = tep_db_fetch_array($member_edit_result_sql);
				
				// begin system log - edit admin member
          		$member_edit_changes_array = $system_log_object->detect_changes($member_edit_row_old, $member_edit_row_new);
          		$member_edit_changes_formatted_array = $system_log_object->construct_log_message($member_edit_changes_array);
          		$system_log_object->insert_all_system_log('Edit Admin Member', TABLE_ADMIN, $admin_id, $member_edit_changes_formatted_array);
				// end system log
				
        		$messageStack->add_session(sprintf(SUCCESS_EDIT_MEMBER, $HTTP_POST_VARS['admin_email_address']), 'success');
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin_id));
        	}
        	break;
        case 'edit_member_group':
        	if (!$assign_admin_group_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	
        	if (!tep_admin_member_exist($HTTP_GET_VARS['mID'])) {
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	
        	break;
		case 'member_group_edit':
        	if (!$assign_admin_group_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	
        	if (!tep_admin_member_exist($HTTP_GET_VARS['mID'])) {
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	
        	$admin_id = tep_db_prepare_input($HTTP_POST_VARS['admin_id']);
        	
	        $member_group_edit_select_sql = " SELECT admin_groups_id FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$admin_id . "'";
			$member_group_edit_result_sql = tep_db_query($member_group_edit_select_sql);
			$member_group_edit_row_old = tep_db_fetch_array($member_group_edit_result_sql);

       		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['admin_groups_id']),
                               		'admin_modified' => 'now()');
       		tep_db_perform(TABLE_ADMIN, $sql_data_array, 'update', 'admin_id = "' . $admin_id . '"');
          	
          	// Clear Admin group name based on Admin email array
          	$cache_key = TABLE_ADMIN_GROUPS . '/admin_groups_name/array/admin_email_address';
          	$memcache_obj->delete($cache_key, 0);
          	
			$member_group_edit_result_sql = tep_db_query($member_group_edit_select_sql);
			$member_group_edit_row_new = tep_db_fetch_array($member_group_edit_result_sql);
			
			// begin system log - edit admin member group
       		$member_group_edit_changes_array = $system_log_object->detect_changes($member_group_edit_row_old, $member_group_edit_row_new);
       		$member_group_edit_changes_formatted_array = $system_log_object->construct_log_message($member_group_edit_changes_array);
			$system_log_object->insert_all_system_log('Edit Admin Member Group', TABLE_ADMIN, $admin_id, $member_group_edit_changes_formatted_array);
			// end system log
				
        	$messageStack->add_session(sprintf(SUCCESS_EDIT_MEMBER, $HTTP_POST_VARS['admin_email_address']), 'success');
        	tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin_id));
        	break;
        case 'del_member':
        	if(!$manage_admin_members_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']));
        	}
        	if(!tep_admin_member_exist($HTTP_GET_VARS['mID'])){
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	break;
      	case 'member_delete':
        	$admin_id = tep_db_prepare_input($_REQUEST['admin_id']);
        	
        	if (!$manage_admin_members_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin_id));
        	}
        	
        	if (!tep_admin_member_exist($admin_id)) {
        		$messageStack->add_session(ERROR_MEMBER_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	}
        	/***************************************************************
        		Update the log_admin_id field in log table to store email 
        		address of this user along with deletion date.
        	***************************************************************/
        	$admin_email_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id ='" . $admin_id . "'");
        	$admin_email_row = tep_db_fetch_array($admin_email_query);
        	$log_admin_id_replacement = sprintf(TEXT_INFO_UPDATE_LOG_ADMIN_DELETE, $admin_email_row["admin_email_address"], date("Y-m-d"));
        	
        	// update for customers table
        	$update_customer_verified_by_sql = "UPDATE " . TABLE_CUSTOMERS . " SET customers_phone_verified_by ='" . tep_db_input($admin_email_row["admin_email_address"]) . "' WHERE customers_phone_verified_by='" . $admin_id . "'";
        	tep_db_query($update_customer_verified_by_sql);
        	
        	// update for customers_remarks_history table
        	$update_customer_remarks_sql = "UPDATE " . TABLE_CUSTOMERS_REMARKS_HISTORY . " SET remarks_added_by ='" . tep_db_input($admin_email_row["admin_email_address"]) . "' WHERE remarks_added_by='" . $admin_id . "'";
        	tep_db_query($update_customer_remarks_sql);
        	
        	$update_restock_character_log_sql = "UPDATE " . TABLE_RESTOCK_CHARACTER_LOG . " SET restock_character_log_admin_id ='" . tep_db_input($log_admin_id_replacement) . "' WHERE restock_character_log_admin_id='" . $admin_id . "'";
        	tep_db_query($update_restock_character_log_sql);
        	
        	$update_game_char_log_sql = "UPDATE " . TABLE_GAME_CHAR_LOG . " SET game_char_log_login_user = '" . tep_db_input($admin_email_row["admin_email_address"]) . "' WHERE game_char_log_user_role = 'admin' AND game_char_log_login_user = '" . $admin_id . "'";
        	tep_db_query($update_game_char_log_sql);
        	
        	$update_aft_automation_sql = "UPDATE " . TABLE_AFT_AUTOMATION . " SET admin_id = '" . tep_db_input($admin_email_row['admin_email_address']) . "' WHERE admin_id = '" . $admin_id . "'";
        	tep_db_query($update_aft_automation_sql);
        	
        	$update_aft_automation_version_sql = "UPDATE " . TABLE_AFT_AUTOMATION_VERSION . " SET changed_by = '" . tep_db_input($admin_email_row['admin_email_address']) . "' WHERE changed_by = '" . $admin_id . "'";
        	tep_db_query($update_aft_automation_version_sql);

            // begin system log - delete admin member
            $system_log_object->insert_system_log('Delete Admin Member', TABLE_ADMIN, $admin_id, 'admin_email_address', $admin_email_row["admin_email_address"], 'n/a');
            // end system log

            // update for system log table
            $update_system_log_sql = "UPDATE " . TABLE_SYSTEM_LOG . " SET system_log_admin_id ='" . tep_db_input($log_admin_id_replacement) . "' WHERE system_log_admin_id='" . $admin_id . "'";
            tep_db_query($update_system_log_sql);

            $update_system_log_sql = "UPDATE " . TABLE_SYSTEM_LOG . " SET entry_id ='" . tep_db_input($log_admin_id_replacement) . "' WHERE entry_id='" . $admin_id . "' AND table_name='admin'";	// For table name = admin, entry_id is admins_id
            tep_db_query($update_system_log_sql);

            // update for admin table
            $update_admin_deletion_flag_sql = "UPDATE " . TABLE_ADMIN . " SET admin_deletion_flag = 1 WHERE admin_id=" . $admin_id;
            tep_db_query($update_admin_deletion_flag_sql);

            $update_admin_group_sql = "UPDATE " . TABLE_ADMIN . " SET admin_groups_id = 0 WHERE admin_id=" . $admin_id;
            tep_db_query($update_admin_group_sql);
			
       		$messageStack->add_session(sprintf(SUCCESS_DELETE_MEMBER, $admin_email_row["admin_email_address"]), 'success');
        	tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page']));
        	break;
        case 'reactivate_admin_members':
        	if ($reactivate_admin_members_permission) {
	        	$update_data_array = array('admin_login_attempt' => 0);
				tep_db_perform(TABLE_ADMIN, $update_data_array, "update", " admin_id=".(int)$_GET['mID']);
	   			
	        	$messageStack->add_session(SUCCESS_REACTIVATE_MEMBER, 'success');
	        }
	        
        	tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $_GET['page']));
        	break;
        case 'define_group':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gPath'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
        	break;
      	case 'group_define':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
      		$adm_grp_id = tep_db_prepare_input($HTTP_POST_VARS['admin_groups_id']);
        	$selected_checkbox = isset($HTTP_POST_VARS['groups_to_boxes']) ? $HTTP_POST_VARS['groups_to_boxes'] : array();
        	
        	$define_files_query = tep_db_query("select admin_files_id, admin_files_cat_setting from " . TABLE_ADMIN_FILES . " order by admin_files_id");
        	while ($define_files = tep_db_fetch_array($define_files_query)) {
          		$admin_files_id = $define_files['admin_files_id'];
          		
          		// Clear the old file categories setting first
          		$admin_files_categories_delete_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . tep_db_input($admin_files_id) . "' AND admin_groups_id = '" . tep_db_input($adm_grp_id) . "'";
        		tep_db_query($admin_files_categories_delete_sql);
        		
          		if (in_array ($admin_files_id, $selected_checkbox)) {
            		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_' . $admin_files_id]));
            		
            		$admin_files_cat_ids = tep_db_prepare_input($HTTP_POST_VARS['files_cat_' . $admin_files_id]);
            		if ($define_files['admin_files_cat_setting'] == '1' && tep_not_null($admin_files_cat_ids)) {
            			$admin_files_categories_data_array = array(	'admin_files_id' => $admin_files_id,
            														'admin_groups_id' => $adm_grp_id,
            														'categories_ids' => $admin_files_cat_ids
            														);
            			tep_db_perform(TABLE_ADMIN_FILES_CATEGORIES, $admin_files_categories_data_array);
            		}
          		} else {
            		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_' . $admin_files_id]));
          		}
          		tep_db_perform(TABLE_ADMIN_FILES, $sql_data_array, 'update', 'admin_files_id = \'' . $admin_files_id . '\'');
        	}
            
            $selected_action_checkbox = isset($HTTP_POST_VARS['actions_to_groups']) ? $HTTP_POST_VARS['actions_to_groups'] : array();
            $admin_files_actions_select_sql = "SELECT admin_files_actions_id FROM " . TABLE_ADMIN_FILES_ACTIONS . " order by admin_files_actions_name";
            $admin_files_actions_result_sql = tep_db_query($admin_files_actions_select_sql);
        	while ($admin_files_actions_row = tep_db_fetch_array($admin_files_actions_result_sql)) {
          		$admin_files_actions_id = $admin_files_actions_row['admin_files_actions_id'];
          		
          		if (in_array ($admin_files_actions_id, $selected_action_checkbox)) {
            		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_' . $admin_files_actions_id]));
          		} else {
            		$sql_data_array = array('admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_' . $admin_files_actions_id]));
          		}
          		tep_db_perform(TABLE_ADMIN_FILES_ACTIONS, $sql_data_array, 'update', 'admin_files_actions_id = \'' . $admin_files_actions_id . '\'');
        	}
            
            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PERMISSION_BOXES_FILES, 'success');
        	tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $adm_grp_id));
        	break;
        case 'del_group':
        	if(!$manage_admin_groups_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
        	break;
      	case 'group_delete':
        	if (!$manage_admin_groups_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	
        	if (!tep_admin_group_exist($HTTP_GET_VARS['gID'])) {
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
			
	        $group_delete_select_sql = "SELECT admin_groups_id, admin_groups_name, admin_groups_authorized 
	        							FROM " . TABLE_ADMIN_GROUPS . " 
	        							WHERE admin_groups_id = '" . (int)tep_db_input($HTTP_GET_VARS['gID']) . "'";
			$group_delete_result_sql = tep_db_query($group_delete_select_sql);
			$group_delete_row_old = tep_db_fetch_array($group_delete_result_sql);
			
        	$set_groups_id = tep_db_prepare_input($HTTP_POST_VARS['set_groups_id']);
        	tep_db_query("DELETE FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id = '" . tep_db_input($HTTP_GET_VARS['gID']) . "'");
			
			$all_groups_id_select_sql = "	SELECT admin_groups_id 
                        					FROM " . TABLE_ADMIN_GROUPS . " 
                        					ORDER BY admin_groups_id";
			$all_groups_id_result_sql = tep_db_query($all_groups_id_select_sql);
       		$add_all_group_id = "";
			while ($group_id_row = tep_db_fetch_array($all_groups_id_result_sql)) {
				if($add_all_group_id != ""){
					$add_all_group_id .= ",";
				}
				$add_all_group_id .= "'" . $group_id_row['admin_groups_id'] . "'";
			}
       		tep_db_query("alter table " . TABLE_ADMIN_FILES . " change admin_groups_id admin_groups_id set(" . $add_all_group_id . ") NOT NULL DEFAULT '1' ");
        	/***************************************************************
        		Update the log_admin_id field in log table to store email 
        		address of this user along with deletion date.
        	***************************************************************/
        	// remove deleted group id from authorized group.
			$authorized_groups_id_sql = "SELECT admin_groups_id,admin_groups_authorized 
										FROM " . TABLE_ADMIN_GROUPS;
			$authorized_groups_id_result = tep_db_query($authorized_groups_id_sql);
			while($authorized_groups_id_row = tep_db_fetch_array($authorized_groups_id_result)){
				$new_authorized_groups_id = tep_remove_from_authorized($HTTP_GET_VARS['gID'], $authorized_groups_id_row['admin_groups_authorized']);
          		$update_authorized_groups = "update " . TABLE_ADMIN_GROUPS . " set admin_groups_authorized = '" . $new_authorized_groups_id . "' where admin_groups_id = '" . tep_db_input($authorized_groups_id_row['admin_groups_id']) . "'";
				tep_db_query($update_authorized_groups);
			}
        	// set users in group to unassigned
        	$update_members_unassigned_sql = "  update " . TABLE_ADMIN . " set admin_groups_id = '' 
        										where admin_groups_id = '" . tep_db_input($HTTP_GET_VARS['gID']) . "'";
            tep_db_query($update_members_unassigned_sql);

       		// begin system log - delete admin group
       		$system_log_object->insert_system_log('Delete Admin Group', TABLE_ADMIN_GROUPS, tep_db_input($HTTP_GET_VARS['gID']), 'admin_groups_name', $group_delete_row_old['admin_groups_name'], 'n/a');
       		// end system log

        	// update for system log table
        	$log_group_id_replacement = sprintf(TEXT_INFO_UPDATE_LOG_ADMIN_DELETE, $group_delete_row_old['admin_groups_name'], date("Y-m-d"));
        	$update_system_log_sql = "UPDATE " . TABLE_SYSTEM_LOG . " SET entry_id ='" . tep_db_input($log_group_id_replacement) . "' WHERE entry_id='" . tep_db_input($HTTP_GET_VARS['gID']) . "' AND table_name='admin_groups'";
        	tep_db_query($update_system_log_sql);

            $messageStack->add_session(SUCCESS_DELETE_GROUP, 'success');
        	tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	break;        
      	case 'edit_group':
        	if(!$manage_admin_groups_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
      		break;
      	case 'group_edit':
        	if(!$manage_admin_groups_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
        	$admin_groups_name = tep_db_prepare_input($HTTP_POST_VARS['admin_groups_name']);
        	$name_replace = ereg_replace_dep(" ", "", strtolower($admin_groups_name));        	
        	if (($admin_groups_name == '' || NULL) || (strlen($admin_groups_name) <= 5) ) {
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS[gID] . '&gName=false&action=action=edit_group'));
        	} else {
          		$check_groups_name_query = tep_db_query("select admin_groups_name as group_name_edit from " . TABLE_ADMIN_GROUPS . " where admin_groups_id <> " . $HTTP_GET_VARS['gID'] . " and LOWER(REPLACE(admin_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
          		if ($check_duplicate > 0){
            		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID'] . '&gName=used&action=edit_group'));
          		} else {
			        $group_edit_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id = '" . (int)tep_db_input($HTTP_GET_VARS['gID']) . "'";
					$group_edit_result_sql = tep_db_query($group_edit_select_sql);
					$group_edit_row_old = tep_db_fetch_array($group_edit_result_sql);

            		$admin_groups_id = $HTTP_GET_VARS['gID'];
            		tep_db_query("update " . TABLE_ADMIN_GROUPS . " set admin_groups_name = '" . tep_db_input($admin_groups_name) . "' where admin_groups_id = '" . tep_db_input($admin_groups_id) . "'");
					
					// Clear Admin group name based on Admin email array
		          	$cache_key = TABLE_ADMIN_GROUPS . '/admin_groups_name/array/admin_email_address';
		          	$memcache_obj->delete($cache_key, 0);
		          	
			        $group_edit_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id = '" . (int)tep_db_input($HTTP_GET_VARS['gID']) . "'";
					$group_edit_result_sql = tep_db_query($group_edit_select_sql);
					$group_edit_row_new = tep_db_fetch_array($group_edit_result_sql);

					// begin system log - edit admin group
    		   		$group_edit_changes_array = $system_log_object->detect_changes($group_edit_row_old, $group_edit_row_new);
       				$group_edit_changes_formatted_array = $system_log_object->construct_log_message($group_edit_changes_array);
       				$system_log_object->insert_all_system_log('Edit Admin Group', TABLE_ADMIN_GROUPS, tep_db_input($HTTP_GET_VARS['gID']), $group_edit_changes_formatted_array);
					// end system log

            		$messageStack->add_session(sprintf(SUCCESS_EDIT_GROUP, $admin_groups_name), 'success');
            		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
          		}
        	}
        	break;
        case 'edit_group_members':
        	if(!$assign_admin_group_members_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
        	break;
      	case 'group_member_edit':
        	if(!$assign_admin_group_members_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
      		$gid = tep_db_prepare_input($HTTP_GET_VARS['gID']);
 			if (isset($HTTP_POST_VARS['btn_remove_admin'])) {
	      		$admin_id = tep_db_prepare_input($HTTP_POST_VARS['group_member_remove_id']);
	      		
		        $group_member_edit_select_sql = " SELECT admin_groups_id FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $admin_id . "'";
				$group_member_edit_result_sql = tep_db_query($group_member_edit_select_sql);
				$group_member_edit_row_old = tep_db_fetch_array($group_member_edit_result_sql);
				
				$update_group_id_sql = "update " . TABLE_ADMIN . " set admin_groups_id = '' where admin_id = '".$admin_id."'";
           		tep_db_query($update_group_id_sql);
           		
				$group_member_edit_result_sql = tep_db_query($group_member_edit_select_sql);
				$group_member_edit_row_new = tep_db_fetch_array($group_member_edit_result_sql);
				
				// begin system log - remove admin group member
	       		$group_member_edit_changes_array = $system_log_object->detect_changes($group_member_edit_row_old, $group_member_edit_row_new);
	       		$group_member_edit_changes_formatted_array = $system_log_object->construct_log_message($group_member_edit_changes_array);
	       		$system_log_object->insert_all_system_log('Edit Admin Member Group', TABLE_ADMIN, $admin_id, $group_member_edit_changes_formatted_array);
				// end system log
				
           		$messageStack->add_session(SUCCESS_REMOVE_GROUP_MEMBER, 'success');
 			} else if (isset($HTTP_POST_VARS['btn_add_admin'])) {
	      		$admin_id = tep_db_prepare_input($HTTP_POST_VARS['group_member_add_id']);

		        $group_member_edit_select_sql = " SELECT admin_groups_id FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $admin_id . "'";
				$group_member_edit_result_sql = tep_db_query($group_member_edit_select_sql);
				$group_member_edit_row_old = tep_db_fetch_array($group_member_edit_result_sql);
				
				$update_group_id_sql = "update " . TABLE_ADMIN . " set admin_groups_id = '".$gid."' where admin_id = '".$admin_id."'";
           		tep_db_query($update_group_id_sql);
				
				$group_member_edit_result_sql = tep_db_query($group_member_edit_select_sql);
				$group_member_edit_row_new = tep_db_fetch_array($group_member_edit_result_sql);
				
				// begin system log - add admin group member
	       		$group_member_edit_changes_array = $system_log_object->detect_changes($group_member_edit_row_old, $group_member_edit_row_new);
	       		$group_member_edit_changes_formatted_array = $system_log_object->construct_log_message($group_member_edit_changes_array);
	       		$system_log_object->insert_all_system_log('Edit Admin Member Group', TABLE_ADMIN, $admin_id, $group_member_edit_changes_formatted_array);
				// end system log

           		$messageStack->add_session(SUCCESS_ADD_GROUP_MEMBER, 'success');
 			}
 			
 			// Clear Admin group name based on Admin email array
          	$cache_key = TABLE_ADMIN_GROUPS . '/admin_groups_name/array/admin_email_address';
          	$memcache_obj->delete($cache_key, 0);
          	
 			tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $gid . '&action=edit_group_members'));
      		break;
      	case 'new_group':
        	if(!$manage_admin_groups_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
      		break;
      	case 'group_new':
        	if(!$manage_admin_groups_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	$admin_groups_name = tep_db_prepare_input($HTTP_POST_VARS['admin_groups_name']);
        	$name_replace = ereg_replace_dep(" ", "", strtolower($admin_groups_name));
        	if (($admin_groups_name == '' || NULL) || (strlen($admin_groups_name) <= 5) ) {
          		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS[gID] . '&gName=false&action=new_group'));
        	} else {
          		$check_groups_name_query = tep_db_query("select admin_groups_name as group_name_new from " . TABLE_ADMIN_GROUPS . " where LOWER(REPLACE(admin_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
          		if ($check_duplicate > 0){
            		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID'] . '&gName=used&action=new_group'));
          		} else {
            		$sql_data_array = array('admin_groups_name' => $admin_groups_name);
            		tep_db_perform(TABLE_ADMIN_GROUPS, $sql_data_array);
            		$admin_groups_id = tep_db_insert_id();
            		
            		if ($login_groups_id != '1') {
						$authorized_groups_id_sql = "SELECT admin_groups_authorized 
													FROM " . TABLE_ADMIN_GROUPS . " 
													WHERE admin_groups_id='" . $login_groups_id . "'";
						$authorized_groups_id_result = tep_db_query($authorized_groups_id_sql);
						$authorized_groups_id_row = tep_db_fetch_array($authorized_groups_id_result);
						if (strlen($authorized_groups_id_row['admin_groups_authorized'])>0){
	            			$authorized_groups_id = $authorized_groups_id_row['admin_groups_authorized'] . ',' . $admin_groups_id;
	            		} else {
	            			$authorized_groups_id = $admin_groups_id;
	            		}
	            		$update_authorized_groups = "update " . TABLE_ADMIN_GROUPS . " set admin_groups_authorized = '" . $authorized_groups_id . "' where admin_groups_id = '" . $login_groups_id . "'";
		        		tep_db_query($update_authorized_groups);
    	        	}
    	        	
					$all_groups_id_sql = "SELECT admin_groups_id 
											FROM " . TABLE_ADMIN_GROUPS . " 
											ORDER BY admin_groups_id";
					$all_groups_id_result = tep_db_query($all_groups_id_sql);
            		$add_all_groups_id = "";
					while ($all_groups_id_row = tep_db_fetch_array($all_groups_id_result)) {
						if($add_all_groups_id != ""){
							$add_all_groups_id .= ",";
						}
						$add_all_groups_id .= "'" . $all_groups_id_row['admin_groups_id'] . "'";
					}
					$add_all_groups_id_files = str_replace("'", "", $add_all_groups_id);
					$update_members_permission = "update " . TABLE_ADMIN_GROUPS . " set admin_groups_authorized = '".$add_all_groups_id_files."' where admin_groups_id = '1'";
            		tep_db_query($update_members_permission);
            		
            		tep_db_query("alter table " . TABLE_ADMIN_FILES . " change admin_groups_id admin_groups_id set(" . $add_all_groups_id . ") NOT NULL DEFAULT '1' ");
            		
	          		// begin system log - new admin member
	          		$system_log_object->insert_system_log('New Admin Group', TABLE_ADMIN_GROUPS, $admin_groups_id, 'admin_groups_name', 'n/a', $admin_groups_name);
	          		// end system log

					$messageStack->add_session(sprintf(SUCCESS_NEW_GROUP_CREATED, $admin_groups_name), 'success');
            		tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
				}
        	}
        	break;
        case 'payment_group_define':
        	if(!$edit_group_permission_permission){
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
            $selected_checkbox_payments = isset($HTTP_POST_VARS['groups_to_payments']) ? $HTTP_POST_VARS['groups_to_payments'] : array();
            // Payment permission
            $selected_checkbox_payments_allow = isset($HTTP_POST_VARS['groups_to_payments_allow']) ? $HTTP_POST_VARS['groups_to_payments_allow'] : array();
            // Update Payment permission
            $selected_checkbox_update_payments_allow = isset($HTTP_POST_VARS['groups_to_update_allow']) ? $HTTP_POST_VARS['groups_to_update_allow'] : array();
            // Cancel Payment permission
            $selected_checkbox_cancel_payments_allow = isset($HTTP_POST_VARS['groups_to_cancel_allow']) ? $HTTP_POST_VARS['groups_to_cancel_allow'] : array();
            
            $define_payment_select_sql = "	SELECT payment_methods_id 
                        					FROM " . TABLE_PAYMENT_METHODS . " 
                        					WHERE payment_methods_send_status ='1' 
                        					ORDER BY payment_methods_id";
            $define_payment_result_sql = tep_db_query($define_payment_select_sql);
            
            while ($define_payment_row = tep_db_fetch_array($define_payment_result_sql)) {
              $payment_methods_id = $define_payment_row['payment_methods_id'];
              
              if (in_array ($define_payment_row['payment_methods_id'], $selected_checkbox_payments)) {
          		  $payment_sql_data_array = array('payment_methods_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_payment_' . $payment_methods_id]));
        		  } else {
          		  $payment_sql_data_array = array('payment_methods_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_payment_' . $payment_methods_id]));
        		  }
        		  tep_db_perform(TABLE_PAYMENT_METHODS, $payment_sql_data_array, 'update', 'payment_methods_id = \'' . $payment_methods_id . '\'');

              // Payment permission
              if (in_array ($define_payment_row['payment_methods_id'], $selected_checkbox_payments_allow)) {
                $payment_allow_sql_data_array = array('payment_methods_payment_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_payment_allow_' . $payment_methods_id]));
              } else {
                $payment_allow_sql_data_array = array('payment_methods_payment_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_payment_allow_' . $payment_methods_id]));
              }
              tep_db_perform(TABLE_PAYMENT_METHODS, $payment_allow_sql_data_array, 'update', 'payment_methods_id = \'' . $payment_methods_id . '\'');

              // Update Payment permission
              if (in_array ($define_payment_row['payment_methods_id'], $selected_checkbox_update_payments_allow)) {
                $update_allow_sql_data_array = array('payment_methods_update_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_update_allow_' . $payment_methods_id]));
              } else {
                $update_allow_sql_data_array = array('payment_methods_update_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_update_allow_' . $payment_methods_id]));
              }
              tep_db_perform(TABLE_PAYMENT_METHODS, $update_allow_sql_data_array, 'update', 'payment_methods_id = \'' . $payment_methods_id . '\'');

              // Cancel Payment permission
              if (in_array ($define_payment_row['payment_methods_id'], $selected_checkbox_cancel_payments_allow)) {
                $cancel_allow_sql_data_array = array('payment_methods_cancel_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_cancel_allow_' . $payment_methods_id]));
              } else {
                $cancel_allow_sql_data_array = array('payment_methods_cancel_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_cancel_allow_' . $payment_methods_id]));
              }
              tep_db_perform(TABLE_PAYMENT_METHODS, $cancel_allow_sql_data_array, 'update', 'payment_methods_id = \'' . $payment_methods_id . '\'');
            }
            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PERMISSION_PAYMENT, 'success');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
            
            break;
        case 'flag_group_define':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
            $selected_checkbox_flags = isset($HTTP_POST_VARS['groups_to_flags']) ? $HTTP_POST_VARS['groups_to_flags'] : array();
            
            $define_flags_select_sql = "	SELECT user_flags_id 
                        					FROM " . TABLE_USER_FLAGS . " 
                        					ORDER BY user_flags_id";
            $define_flags_result_sql = tep_db_query($define_flags_select_sql);
            
            while ($define_flags_row = tep_db_fetch_array($define_flags_result_sql)) {
                $user_flags_id = $define_flags_row['user_flags_id'];
                
                if (in_array ($define_flags_row['user_flags_id'], $selected_checkbox_flags)) {
            		$flags_sql_data_array = array('user_flags_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_flags_' . $user_flags_id]));
          		} else {
            		$flags_sql_data_array = array('user_flags_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_flags_' . $user_flags_id]));
          		}
          		tep_db_perform(TABLE_USER_FLAGS, $flags_sql_data_array, 'update', 'user_flags_id = \'' . $user_flags_id . '\'');
            }
            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PERMISSION_CUSTOMER_FLAGS, 'success');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
            
            break;
        case 'site_group_define':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
            $selected_checkbox_site = isset($HTTP_POST_VARS['groups_to_site']) ? $HTTP_POST_VARS['groups_to_site'] : array();
            
            $define_site_select_sql = "	SELECT site_id 
                    					FROM " . TABLE_SITE_CODE . " 
                    					WHERE site_has_buyback = 1
                    					ORDER BY site_id";
            $define_site_result_sql = tep_db_query($define_site_select_sql);
            
            while ($define_site_row = tep_db_fetch_array($define_site_result_sql)) {
                $site_id = $define_site_row['site_id'];
                
                if (in_array ($define_site_row['site_id'], $selected_checkbox_site)) {
            		$site_sql_data_array = array('buyback_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['checked_action_site_' . $site_id]));
          		} else {
            		$site_sql_data_array = array('buyback_admin_groups_id' => tep_db_prepare_input($HTTP_POST_VARS['unchecked_action_site_' . $site_id]));
          		}   
       		    		
          		tep_db_perform(TABLE_SITE_CODE, $site_sql_data_array, 'update', 'site_id = \'' . $site_id . '\'');
            }

            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PERMISSION_BUYBACK_SITE, 'success');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
            
            break;
         case 'products_group_define':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $_GET['gID']));
        	}
        	if(!tep_admin_group_exist($_GET['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
            $selected_checkbox_products = isset($_POST['groups_to_products']) ? $_POST['groups_to_products'] : array();
			
            $define_products_select_sql = "	SELECT custom_products_type_id
                    						FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                    						ORDER BY custom_products_type_id  ";
            $define_products_result_sql = tep_db_query($define_products_select_sql);
            
            while ($define_products_row = tep_db_fetch_array($define_products_result_sql)) {
                $products_permission_id = $define_products_row['custom_products_type_id'];
				
                if (in_array ($define_products_row['custom_products_type_id'], $selected_checkbox_products)) {
            		$products_sql_data_array = array('custom_products_admin_group_id' => tep_db_prepare_input($_POST['checked_action_products_' . $products_permission_id]));
          		} else {
            		$products_sql_data_array = array('custom_products_admin_group_id' => tep_db_prepare_input($_POST['unchecked_action_products_' . $products_permission_id]));
          		}
          		tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE, $products_sql_data_array, 'update', 'custom_products_type_id  = \'' . $products_permission_id . '\''); 
            }
			
            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PRODUCTS, 'success');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
            
            break;
        case 'groups_control_define':
        	if(!$edit_group_permission_permission){
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	if(!tep_admin_group_exist($HTTP_GET_VARS['gID'])){
        		$messageStack->add_session(ERROR_GROUP_NOT_EXIST, 'error');
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=groups'));
        	}
			
	        $group_authorized_select_sql = " SELECT admin_groups_authorized FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id = '" . tep_db_prepare_input($HTTP_GET_VARS['gID']) . "'";
			$group_authorized_result_sql = tep_db_query($group_authorized_select_sql);
			$group_authorized_row_old = tep_db_fetch_array($group_authorized_result_sql);
			
        	$admin_groups_authorized = '';
        	if (isset($HTTP_POST_VARS['groups_control_to']) && is_array($HTTP_POST_VARS['groups_control_to'])) {
	        	sort($HTTP_POST_VARS['groups_control_to']);
	        	$admin_groups_authorized = implode(",", $HTTP_POST_VARS['groups_control_to']);
	        }
	        
			$update_groups_authorizede_sql = "update " . TABLE_ADMIN_GROUPS . " set admin_groups_authorized = '".$admin_groups_authorized."' where admin_groups_id = '".tep_db_input($HTTP_GET_VARS['gID'])."'";
       		tep_db_query($update_groups_authorizede_sql);
			
			$group_authorized_result_sql = tep_db_query($group_authorized_select_sql);
			$group_authorized_row_new = tep_db_fetch_array($group_authorized_result_sql);
			
			// begin system log - edit admin group permission - authorized groups
       		$group_authorized_changes_array = $system_log_object->detect_changes($group_authorized_row_old, $group_authorized_row_new);
       		$group_authorized_changes_formatted_array = $system_log_object->construct_log_message($group_authorized_changes_array);
       		$system_log_object->insert_all_system_log('Edit Group Permission', TABLE_ADMIN_GROUPS, $admin_groups_id, $group_authorized_changes_formatted_array);
			// end system log
			
            $messageStack->add_session(SUCCESS_UPDATE_GROUP_PERMISSION_GROUPS_CONTROL, 'success');
            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $admin_groups_id));
            
        	break;
        case 'edit_credit_limit':
    		if (!$manage_admin_credit_limit_permission) {
				tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
        	
        	break;
        case 'update_credit_limit':
        	if (!$manage_admin_credit_limit_permission) {
	            tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
        	}
                
                $admin_id = tep_db_prepare_input($HTTP_GET_VARS['mID']);

                // current user credit limit
                $_login_limit = tep_get_admin_credit_limit($login_id);
                $_admin_limit = tep_get_admin_credit_limit($admin_id);

                $_login_limit_used = $_login_limit['reset_team_limit_used'] + ($HTTP_POST_VARS['admin_credit_limit_max'] > $_admin_limit['admin_credit_limit_max'] ? $HTTP_POST_VARS['admin_credit_limit_max'] - $_admin_limit['admin_credit_limit_max'] : 0);
                if ($_login_limit['reset_team_limit_max'] >= $_login_limit_used) {
                    if (($HTTP_POST_VARS['reset_credit_limit'] == '1') && ($_admin_limit['admin_credit_limit_total'] > 0)) {
                        $_login_limit_used += $_admin_limit['admin_credit_limit_total'];
                    }
                }

                if (($_login_limit_used >= 0) && ($_login_limit['reset_team_limit_max'] >= $_login_limit_used)) {
                    $_sql = "SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . tep_db_input($admin_id) . "'";
                    $_res = tep_db_query($_sql);
                    $_row = tep_db_fetch_array($_res);
                    $_slack_daily = $_slack_reset = "";
                    if ($_admin_limit["admin_credit_limit_max"] != $HTTP_POST_VARS['admin_credit_limit_max']) {
                        $_slack_daily .= "*Daily Limit* \n" .
                                "From : " . $_admin_limit["admin_credit_limit_max"] . "\n" .
                                "To : " . $HTTP_POST_VARS["admin_credit_limit_max"] . "\n\n";
                    }

                    $admin_credit_limit_sql_data = array(
                        'admin_id' => $admin_id,
                        'admin_credit_limit_max' => tep_db_prepare_input($HTTP_POST_VARS['admin_credit_limit_max']),
                    );

                    // permission to update Reset Authorized Group Daily Credit Limit
                    if ($manage_admin_team_credit_limit_permission) {
                        $admin_credit_limit_sql_data['reset_team_limit_max'] = tep_db_prepare_input($HTTP_POST_VARS['reset_team_limit_max']);

                        if ($_admin_limit["reset_team_limit_max"] != $HTTP_POST_VARS['reset_team_limit_max']) {
                            $_slack_reset .= "*Authorized Group Daily Reset Limit* \n" .
                                    "From : " . $_admin_limit["reset_team_limit_max"] . "\n" .
                                    "To : " . $HTTP_POST_VARS["reset_team_limit_max"] . "\n\n";
                        }
                    }

                    $admin_credit_limit_select_sql = "	SELECT COUNT(admin_id) AS total_record
                                                        FROM " . TABLE_ADMIN_CREDIT_LIMIT . "
                                                        WHERE admin_id = '" . tep_db_input($admin_id) . "'";
                    $admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);
                    $admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql);

                    if ($admin_credit_limit_row['total_record'] < 1) {
                        tep_db_perform(TABLE_ADMIN_CREDIT_LIMIT, $admin_credit_limit_sql_data);
                    } else {
                        if (isset($HTTP_POST_VARS['reset_credit_limit']) && $HTTP_POST_VARS['reset_credit_limit'] == '1') {
                            $admin_credit_limit_sql_data['admin_credit_limit_total'] = 0;
                            $_slack_daily .= "*Reset Daily Limit to 0.0000* \nPrevious Usage : " . $_admin_limit["admin_credit_limit_total"] . "\n\n";
                        }

                        // permission to update Reset Authorized Group Daily Credit Limit
                        if ($manage_admin_team_credit_limit_permission) {
                            if (isset($HTTP_POST_VARS['reset_team_limit_used']) && $HTTP_POST_VARS['reset_team_limit_used'] == '1') {
                                $admin_credit_limit_sql_data['reset_team_limit_used'] = 0;
                                $_slack_reset .= "*Reset Authorized Group Daily Limit to 0.0000* \nPrevious Usage : " . $_admin_limit["reset_team_limit_used"] . "\n\n";
                            }
                        }

                        tep_db_perform(TABLE_ADMIN_CREDIT_LIMIT, $admin_credit_limit_sql_data, 'update', 'admin_id="'.tep_db_input($admin_id).'"');
                    }

                    // update current crew team reset limit
                    tep_db_perform(TABLE_ADMIN_CREDIT_LIMIT, array('reset_team_limit_used' => $_login_limit_used), 'update', 'admin_id="' . tep_db_input($login_id) . '"');

                    // slack notification
                    if ($_slack_daily || $_slack_reset) {
                        include_once(DIR_WS_CLASSES . 'slack_notification.php');
                        $slack = new slack_notification();
                        $data = json_encode(array(
                            'text' => '[OG Crew] Reset Daily Credit Limit - ' . date("F j, Y H:i"),
                            'attachments' => array(
                                array(
                                    'color' => 'warning',
                                    'text' => "Account : " . $_row["admin_email_address"] . "\n\n" .
                                        ($_slack_daily ? $_slack_daily . "\n\n" : "") .
                                        ($_slack_reset ? $_slack_reset . "\n\n" : "") .
                                        "Change by : " . $login_email_address
                                )
                            )
                        ));
                        $slack->send(SLACK_WEBHOOK_RESET_DAILY_LIMIT, $data);
                    }

                    $messageStack->add_session(SUCCESS_UPDATE_CREDIT_LIMIT, 'success');
                } else {
                    $messageStack->add_session(ERROR_UPDATE_CREDIT_LIMIT, 'error');
                }
                tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, tep_get_all_get_params(array('action'))));
        	break;

      //Mohamad - Update CDKey View
      case 'update_cdkey_view_limit':
          if (!$manage_admin_cdkey_view_limit_permission) {
              tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gID']));
          }
          
          $admin_id = tep_db_prepare_input($HTTP_GET_VARS['mID']);

          $admin_cdkey_view_limit_select_sql = " SELECT * FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '" . tep_db_input($admin_id) . "'";
          $admin_cdkey_view_limit_result_sql = tep_db_query($admin_cdkey_view_limit_select_sql);
          $viewLimitNumRow = tep_db_num_rows($admin_cdkey_view_limit_result_sql);

          if ($viewLimitNumRow < 1) {
            $admin_cdkey_view_limit_sql_data = array( 
                  'cdkey_user_id' => tep_db_input($admin_id),
                  'cdkey_limit' => tep_db_prepare_input($HTTP_POST_VARS['cdkey_limit']),
                  'cdkey_total_view' => 0,
                  'cdkey_updated_datetime' => $currentDate
                  );
            tep_db_perform(TABLE_CDKEY_VIEW, $admin_cdkey_view_limit_sql_data, 'insert');

          } else {
            $admin_cdkey_view_limit_sql_data = array( 
                  'cdkey_limit' => tep_db_prepare_input($HTTP_POST_VARS['cdkey_limit']),
                  'cdkey_updated_datetime' => $currentDate
              );
            tep_db_perform(TABLE_CDKEY_VIEW, $admin_cdkey_view_limit_sql_data, 'update', 'cdkey_user_id="'.tep_db_input($admin_id).'"');
          }

          if (isset($HTTP_POST_VARS['reset_cdkey_view_limit']) && $HTTP_POST_VARS['reset_cdkey_view_limit'] == '1') {
              $admin_cdkey_view_limit_sql_data['cdkey_total_view'] = 0;
              tep_db_perform(TABLE_CDKEY_VIEW, $admin_cdkey_view_limit_sql_data, 'update', 'cdkey_user_id="'.tep_db_input($admin_id).'"');
            }
         
          $messageStack->add_session(SUCCESS_UPDATE_CDKEY_VIEW_LIMIT, 'success');
          tep_redirect(tep_href_link(FILENAME_ADMIN_MEMBERS, tep_get_all_get_params(array('action'))));
            
          break;
	}
}

$authorized_group_select_sql = "SELECT admin_groups_authorized 
								FROM " . TABLE_ADMIN_GROUPS . " 
								WHERE admin_groups_id = '" . tep_db_input($login_groups_id) . "'";
$authorized_group_result_sql = tep_db_query($authorized_group_select_sql);
$authorized_group_row = tep_db_fetch_array ($authorized_group_result_sql);
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/admin_xmlhttp.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
	<? require('includes/account_check.js.php'); ?>
	<script language="javascript">
		<!--
		var pageLoaded = false;
		function submitForm(val) {
			if (val==0) {
				document.status.OrderUpdateBtn.disabled = true;
				document.status.OrderUpdateBtn.value = 'Please wait...';
				document.status.submit();
			}
		}
		
		function init() {
			// quit if this function has already been called
	       	if (arguments.callee.done) return;
			
	       	// flag this function so we don't do the same thing twice
	       	arguments.callee.done = true;
			
			SetFocus();
	       	initInfoCaptions();
	       	pageLoaded = true;	// Control when a javascript event in this page can be triggered
		};
		
	   	/* for Mozilla */
	   	if (document.addEventListener) {
	       	document.addEventListener("DOMContentLoaded", init, null);
	   	}
		
		function showHideBox(box_div, box_link_div, classtype) {
			DOMCall(box_div).className = classtype;
            
			if (classtype == 'show') {
				DOMCall(box_link_div).innerHTML = '<a href="javascript:;" onclick="showHideBox(\''+box_div+'\', \''+box_link_div+'\', \'hide\');">'+'<?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?>'+'</a>';
			} else {
				DOMCall(box_link_div).innerHTML = '<a href="javascript:;" onclick="showHideBox(\''+box_div+'\', \''+box_link_div+'\', \'show\');">'+'<?=tep_image(DIR_WS_ICONS . 'expand_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?>'+'</a>';
			}
		}
		
		function customer_flags_checking(cur_sel_flag) {
			var custFlags = document.defineFlagsForm.elements['groups_to_flags[]'];
			
			if (typeof(custFlags.length) != 'undefined') {
				for (i=0; i<custFlags.length; i++) {
					//if (i==2) continue;	// Skip the On Hold flag
					if (cur_sel_flag.checked) {
						if (custFlags[i].value < cur_sel_flag.value) {
							custFlags[i].checked = true;
						}
					} else {
						if (custFlags[i].value > cur_sel_flag.value) {
							custFlags[i].checked = false;
						}
					}
				}
			}
		}
		
	   	/* for other browsers */
	   	window.onload = init;
	   	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->    
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td width="100%" class="dataTableContent">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
<?  if ($HTTP_GET_VARS['action'] == 'define_group') {?>
          						<tr>
          						    <td colspan="2">
                                        [<a href="#boxes"><?=TABLE_HEADING_GROUPS_DEFINE?></a>]&nbsp;
                                        [<a href="#payment"><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS?></a>]&nbsp;
                                        [<a href="#flag"><?=TABLE_HEADING_GROUPS_USER_FLAGS?></a>]&nbsp;
                                        [<a href="#site"><?=TABLE_HEADING_GROUPS_SITE_CODE?></a>]
                                        [<a href="#groups"><?=TABLE_HEADING_GROUPS_CONTROL?></a>]
                                    </td>
          						</tr>
<?  } ?>
        					</table>
<?
	
	echo "<b>" . TEXT_INFO_GROUPS_ALLOWED . "</b>";
	echo "<br />";
	if(tep_not_null($authorized_group_row['admin_groups_authorized'])) {
		$db_admin_authorized_query = "select * from " . TABLE_ADMIN_GROUPS . " 
									  where admin_groups_id IN (".$authorized_group_row['admin_groups_authorized'].")";
		$db_admin_authorized_query = tep_db_query($db_admin_authorized_query);
		while ($admin = tep_db_fetch_array($db_admin_authorized_query)) {
	    	echo $admin['admin_groups_name'] . ", ";
	    }
	}
	echo "<i>" . TEXT_INFO_GROUPS_AND_UNASSIGNED . "</i>";
?>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
<?
 	if ($HTTP_GET_VARS['gPath']) {
 		$load_permission_from_grp = isset($_REQUEST['load_from_group']) && tep_not_null($_REQUEST['load_from_group']) 
 									? tep_db_prepare_input($_REQUEST['load_from_group']) 
 									: tep_db_prepare_input($HTTP_GET_VARS['gPath']);
 		
   		$group_name_query = tep_db_query("SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id = '" . tep_db_input($HTTP_GET_VARS['gPath']) . "'");
   		$group_name = tep_db_fetch_array($group_name_query);
  		
   		if ($HTTP_GET_VARS['gPath'] == 1) {
     		echo tep_draw_form('defineForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath']);
   		} else if ($HTTP_GET_VARS['gPath'] != 1) {
     		echo tep_draw_form('defineForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath'] . '&action=group_define', 'post', 'enctype="multipart/form-data"');
     		echo tep_draw_hidden_field('admin_groups_id', $HTTP_GET_VARS['gPath']); 
   		}
?>
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
											    <td colspan="2">
    												<table border="0" width="100%" cellspacing="0" cellpadding="2">
    												    <tr class="dataTableHeadingRow">
    												        <td width="2%">
    												             <div id="boxes_files_link">
                                          							<a href="javascript:;" onclick="showHideBox('boxes_files_selection', 'boxes_files_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                          						</div>
    												        </td>
    												        <td class="dataTableHeadingContent" width="98%">&nbsp;<a name="boxes"></a><?=TABLE_HEADING_GROUPS_DEFINE?></td>
    												    </tr>
    												</table>
											    </td>
											</tr>
											<tbody id="boxes_files_selection" class="show">
<?
  		$db_boxes_query = tep_db_query("SELECT admin_files_id AS admin_boxes_id, admin_files_name AS admin_boxes_name, admin_groups_id AS boxes_group_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_is_boxes = '1' ORDER BY admin_files_name");
  		while ($group_boxes = tep_db_fetch_array($db_boxes_query)) {
  			$group_boxes_files_select_sql = "	SELECT af.admin_files_id, af.admin_files_name, af.admin_groups_id, af.admin_files_cat_setting, afc.categories_ids 
  												FROM " . TABLE_ADMIN_FILES . " af 
  												LEFT JOIN " . TABLE_ADMIN_FILES_CATEGORIES . " AS afc 
  													ON (af.admin_files_id=afc.admin_files_id AND afc.admin_groups_id='".tep_db_input($load_permission_from_grp)."') 
  												WHERE af.admin_files_is_boxes = '0' 
  													AND af.admin_files_to_boxes = '" . $group_boxes['admin_boxes_id'] . "' 
  												ORDER BY af.admin_files_name";
    		$group_boxes_files_result_sql = tep_db_query($group_boxes_files_select_sql);
			
    		$selectedGroups = $group_boxes['boxes_group_id'];
    		$groupsArray = explode(",", $selectedGroups);
			
			$checked = in_array($load_permission_from_grp, $groupsArray) ? true : false;
			
  			if (in_array($HTTP_GET_VARS['gPath'], $groupsArray)) {
	  			$del_boxes = array($HTTP_GET_VARS['gPath']);
	  			$result = array_diff ($groupsArray, $del_boxes);
	  			sort($result);
	  			$checkedBox = $selectedGroups;
	  			$uncheckedBox = implode (",", $result);
  			} else {
  				$add_boxes = array($HTTP_GET_VARS['gPath']);
      			$result = array_merge ($add_boxes, $groupsArray);
      			sort($result);
      			$checkedBox = implode (",", $result);
      			$uncheckedBox = $selectedGroups;
  			}
?>
                                            <tr class="dataTableRowBoxes">
												<td class="dataTableContent" width="23"><?=tep_draw_checkbox_field('groups_to_boxes[]', $group_boxes['admin_boxes_id'], $checked, '', 'id="groups_' . $group_boxes['admin_boxes_id'] . '" onClick="checkGroups(this)"'); ?></td>
												<td class="dataTableContent"><b><? echo ucwords(substr_replace ($group_boxes['admin_boxes_name'], '', -4)) . ' ' . tep_draw_hidden_field('checked_' . $group_boxes['admin_boxes_id'], $checkedBox) . tep_draw_hidden_field('unchecked_' . $group_boxes['admin_boxes_id'], $uncheckedBox); ?></b></td>
											</tr>
											<tr class="dataTableRow">
												<td class="dataTableContent">&nbsp;</td>
												<td class="dataTableContent">
													<table border="0" cellspacing="0" cellpadding="0">
<?
     		//$group_boxes_files_query = tep_db_query("select admin_files_id, admin_files_name, admin_groups_id from " . TABLE_ADMIN_FILES . " where admin_files_is_boxes = '0' and admin_files_to_boxes = '" . $group_boxes['admin_boxes_id'] . "' order by admin_files_name");
     		$node_count = 0;
     		while($group_boxes_files_row = tep_db_fetch_array($group_boxes_files_result_sql)) {
       			$selectedGroups = $group_boxes_files_row['admin_groups_id'];
       			$groupsArray = explode(",", $selectedGroups);
				
				$checked = in_array($load_permission_from_grp, $groupsArray) ? true : false;
				
       			if (in_array($HTTP_GET_VARS['gPath'], $groupsArray)) {
         			$del_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_diff ($groupsArray, $del_boxes);
         			sort($result);
         			$checkedBox = $selectedGroups;
         			$uncheckedBox = implode (",", $result);
       			} else {
         			$add_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_merge ($add_boxes, $groupsArray);
         			sort($result);
         			$checkedBox = implode (",", $result);
         			$uncheckedBox = $selectedGroups;
       			}
?>
								            			<tr>
															<td width="20"><?=tep_draw_checkbox_field('groups_to_boxes[]', $group_boxes_files_row['admin_files_id'], $checked, '', 'id="subgroups_' . $group_boxes['admin_boxes_id'] . '" onClick="checkSub(this, true)"')?></td>
															<td class="dataTableContent"><? echo $group_boxes_files_row['admin_files_name'] . ( ($HTTP_GET_VARS['gPath'] != '1' && $group_boxes_files_row['admin_files_cat_setting'] == '1')? ' <a href="#c_list" onClick="loadCatSelectionLists(\'admin_files_cat_div\', \''.$group_boxes_files_row['admin_files_id'].'\', \''.(int)$languages_id.'\');">' . tep_image(DIR_WS_ICONS."edit.gif", "Edit File Categories Permission", "", "", 'align="top"') . '</a>' : '') . tep_draw_hidden_field('checked_' . $group_boxes_files_row['admin_files_id'], $checkedBox) . tep_draw_hidden_field('unchecked_' . $group_boxes_files_row['admin_files_id'], $uncheckedBox) . tep_draw_hidden_field('files_cat_'.$group_boxes_files_row['admin_files_id'], $group_boxes_files_row['categories_ids'], 'id="files_cat_'.$group_boxes_files_row['admin_files_id'].'"');?></td>
														</tr>
<?     			
				$files_actions_select_sql = "SELECT admin_files_actions_id, admin_files_actions_name, admin_groups_id FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $group_boxes_files_row['admin_files_id'] . "' ORDER BY admin_files_sort_order";
				$files_actions_result_sql = tep_db_query($files_actions_select_sql);
				if (tep_db_num_rows($files_actions_result_sql)) {
					echo '								<tr class="dataTableRow">
															<td class="dataTableContent">&nbsp;</td>
															<td class="dataTableContent">
																<table border="0" cellspacing="0" cellpadding="0">';
				}
				while($files_actions_row = tep_db_fetch_array($files_actions_result_sql)) {
					$actionSelectedGroups = $files_actions_row['admin_groups_id'];
	       			$actionArray = explode(",", $actionSelectedGroups);
					
					$checked = in_array($load_permission_from_grp, $actionArray) ? true : false;
					
	       			if (in_array($HTTP_GET_VARS['gPath'], $actionArray)) {
	         			$del_boxes = array($HTTP_GET_VARS['gPath']);
	         			$result = array_diff ($actionArray, $del_boxes);
	         			sort($result);
	         			$checkedBox = $actionSelectedGroups;
	         			$uncheckedBox = implode (",", $result);
	       			} else {
	         			$add_boxes = array($HTTP_GET_VARS['gPath']);
	         			$result = array_merge ($add_boxes, $actionArray);
	         			sort($result);
	         			$checkedBox = implode (",", $result);
	         			$uncheckedBox = $actionSelectedGroups;
	       			}
					echo '											<tr>
																		<td width="20">'.tep_draw_checkbox_field('actions_to_groups[]', $files_actions_row['admin_files_actions_id'], $checked, '', 'id="subgroups_actions' . $group_boxes_files_row['admin_files_id'] . '" onClick="checkAction(this, \''.$node_count.'\', \''.$group_boxes['admin_boxes_id'].'\')"').'</td>
																		<td class="dataTableContent">'.$files_actions_row['admin_files_actions_name'] . ' ' . tep_draw_hidden_field('checked_action_' . $files_actions_row['admin_files_actions_id'], $checkedBox) . tep_draw_hidden_field('unchecked_action_' . $files_actions_row['admin_files_actions_id'], $uncheckedBox).'</td>
																	</tr>';
				}
				
				if (tep_db_num_rows($files_actions_result_sql)) {
					echo '										</table>
															</td>
														</tr>';
				}
				$node_count++;
			} ?>
              										</table>
                								</td>
											</tr>
<? 		} ?>
											<tr class="dataTableRowBoxes">
								          		<td colspan=2 class="dataTableContent" valign="top" align="right"><? if ($HTTP_GET_VARS['gPath'] != 1) { echo  tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton'); } else { echo tep_submit_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, '', 'inputButton'); } ?> &nbsp;</td>
											</tr>
											</tbody>
										</table>
									</form>
<?
     		echo tep_draw_form('definePaymentForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath'] . '&action=payment_group_define', 'post', 'enctype="multipart/form-data"');
     		echo tep_draw_hidden_field('admin_groups_id', $HTTP_GET_VARS['gPath']); 
?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                        <tr class="dataTableHeadingRow">
		          			                            <td width="0%">
		          			                                <div id="payment_link">
                                      							<a href="javascript:;" onclick="showHideBox('payment_selection', 'payment_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                      						</div>
		          			                            </td>
		          			                            <td class="dataTableHeadingContent" width="38%">&nbsp;<a name="payment"></a><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS?></td>
		          			                            <td class="dataTableHeadingContent" width="15%"><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS_MESSAGE?></td>
                                                <td class="dataTableHeadingContent" width="15%"><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS_PAYMENT_MESSAGE?></td>
                                                <td class="dataTableHeadingContent" width="15%"><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS_UPDATE_MESSAGE?></td>
                                                <td class="dataTableHeadingContent" width="15%"><?=TABLE_HEADING_GROUPS_PAYMENT_METHODS_CANCEL_MESSAGE?></td>
                                                
		          			                        </tr>
		          			                        
		          			                   
		          			                           <tr>
		          			                       <td></td>
		          			                       <td width="43%">
		          			                       <td width="15.25%"><input type="checkbox" onclick='allowtoview()' id='allow_to_view' checked='true'></td>
		          			                       <td width="15.25%"><input type="checkbox" onclick='allowtoprocess()' id='allow_to_process' checked='true'></td>
		          			                       <td width="15.25%"><input type="checkbox" onclick='allowtoupdate()' id='allow_to_update' checked='true'></td>
		          			                       <td width="0%"><input type="checkbox" onclick='allowtocancel()' id='allow_to_cancel' checked='true'></td>
		          			                      </tr>
		          			                      
		          			                        
		          			                    </table>
		          			                    
		          			                    
		          			                </td>
		          			                
		          			            </tr>
		          			            
		          			            <tbody id="payment_selection" class="show">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
        $pm_select_sql = "SELECT payment_methods_id, payment_methods_admin_groups_id, payment_methods_send_mode_name, 
                          payment_methods_payment_admin_groups_id, payment_methods_update_admin_groups_id, payment_methods_cancel_admin_groups_id
                          FROM " . TABLE_PAYMENT_METHODS . " 
                          WHERE payment_methods_send_status ='1' 
                          ORDER BY payment_methods_sort_order";
        $pm_result_sql = tep_db_query($pm_select_sql);
        if (tep_db_num_rows($pm_result_sql)) {
            while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
              $actionPaymentSelectedGroups = $pm_row['payment_methods_admin_groups_id'];
              $actionPaymentArray = explode(",", $actionPaymentSelectedGroups);
                
              $checkedPayment = in_array($load_permission_from_grp, $actionPaymentArray) ? true : false;
					
         			if (in_array($HTTP_GET_VARS['gPath'], $actionPaymentArray)) {
           			$del_boxes = array($HTTP_GET_VARS['gPath']);
           			$result = array_diff ($actionPaymentArray, $del_boxes);
           			sort($result);
           			$checkedBoxPayment = $actionPaymentSelectedGroups;
           			$uncheckedBoxPayment = implode (",", $result);
         			} else {
           			$add_boxes = array($HTTP_GET_VARS['gPath']);
           			$result = array_merge ($add_boxes, $actionPaymentArray);
           			sort($result);
           			$checkedBoxPayment = implode (",", $result);
           			$uncheckedBoxPayment = $actionPaymentSelectedGroups;
         			}

              // Payment Permission
              $actionPaymentAllowSelectedGroups = $pm_row['payment_methods_payment_admin_groups_id'];
              $actionPaymentAllowArray = explode(",", $actionPaymentAllowSelectedGroups);
                
              $checkedPaymentAllow = in_array($load_permission_from_grp, $actionPaymentAllowArray) ? true : false;
          
              if (in_array($HTTP_GET_VARS['gPath'], $actionPaymentAllowArray)) {
                $del_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_diff ($actionPaymentAllowArray, $del_boxes);
                sort($result);
                $checkedBoxPaymentAllow = $actionPaymentAllowSelectedGroups;
                $uncheckedBoxPaymentAllow = implode (",", $result);
              } else {
                $add_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_merge ($add_boxes, $actionPaymentAllowArray);
                sort($result);
                $checkedBoxPaymentAllow = implode (",", $result);
                $uncheckedBoxPaymentAllow = $actionPaymentAllowSelectedGroups;
              }

              // Update Payment Permission
              $actionUpdatePaymentAllowSelectedGroups = $pm_row['payment_methods_update_admin_groups_id'];
              $actionUpdatePaymentAllowArray = explode(",", $actionUpdatePaymentAllowSelectedGroups);
                
              $checkedUpdatePaymentAllow = in_array($load_permission_from_grp, $actionUpdatePaymentAllowArray) ? true : false;
          
              if (in_array($HTTP_GET_VARS['gPath'], $actionUpdatePaymentAllowArray)) {
                $del_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_diff ($actionUpdatePaymentAllowArray, $del_boxes);
                sort($result);
                $checkedBoxUpdatePaymentAllow = $actionUpdatePaymentAllowSelectedGroups;
                $uncheckedBoxUpdatePaymentAllow = implode (",", $result);
              } else {
                $add_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_merge ($add_boxes, $actionUpdatePaymentAllowArray);
                sort($result);
                $checkedBoxUpdatePaymentAllow = implode (",", $result);
                $uncheckedBoxUpdatePaymentAllow = $actionUpdatePaymentAllowSelectedGroups;
              }

              // Cancel Payment Permission
              $actionCancelPaymentAllowSelectedGroups = $pm_row['payment_methods_cancel_admin_groups_id'];
              $actionCancelPaymentAllowArray = explode(",", $actionCancelPaymentAllowSelectedGroups);
                
              $checkedCancelPaymentAllow = in_array($load_permission_from_grp, $actionCancelPaymentAllowArray) ? true : false;
          
              if (in_array($HTTP_GET_VARS['gPath'], $actionCancelPaymentAllowArray)) {
                $del_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_diff ($actionCancelPaymentAllowArray, $del_boxes);
                sort($result);
                $checkedBoxCancelPaymentAllow = $actionCancelPaymentAllowSelectedGroups;
                $uncheckedBoxCancelPaymentAllow = implode (",", $result);
              } else {
                $add_boxes = array($HTTP_GET_VARS['gPath']);
                $result = array_merge ($add_boxes, $actionCancelPaymentAllowArray);
                sort($result);
                $checkedBoxCancelPaymentAllow = implode (",", $result);
                $uncheckedBoxCancelPaymentAllow = $actionCancelPaymentAllowSelectedGroups;
              }                                      
?>
                                                    
            		          			            <tr class="dataTableRow">
            		          			            
            		          			                <td class="dataTableContent" width="44%"><?=$pm_row['payment_methods_send_mode_name']?></td>
            		          			                <td class="dataTableContent" width="15%"><?=tep_draw_checkbox_field('groups_to_payments[]', $pm_row['payment_methods_id'], $checkedPayment) .  tep_draw_hidden_field('checked_action_payment_' . $pm_row['payment_methods_id'], $checkedBoxPayment) . tep_draw_hidden_field('unchecked_action_payment_' . $pm_row['payment_methods_id'], $uncheckedBoxPayment)?></td>
                                                <td class="dataTableContent" width="15%"><?=tep_draw_checkbox_field('groups_to_payments_allow[]', $pm_row['payment_methods_id'], $checkedPaymentAllow) .  tep_draw_hidden_field('checked_action_payment_allow_' . $pm_row['payment_methods_id'], $checkedBoxPaymentAllow) . tep_draw_hidden_field('unchecked_action_payment_allow_' . $pm_row['payment_methods_id'], $uncheckedBoxPaymentAllow)?></td>
                                                <td class="dataTableContent" width="15%"><?=tep_draw_checkbox_field('groups_to_update_allow[]', $pm_row['payment_methods_id'], $checkedUpdatePaymentAllow) .  tep_draw_hidden_field('checked_action_update_allow_' . $pm_row['payment_methods_id'], $checkedBoxUpdatePaymentAllow) . tep_draw_hidden_field('unchecked_action_update_allow_' . $pm_row['payment_methods_id'], $uncheckedBoxUpdatePaymentAllow)?></td>
                                                <td class="dataTableContent" width="15%"><?=tep_draw_checkbox_field('groups_to_cancel_allow[]', $pm_row['payment_methods_id'], $checkedCancelPaymentAllow) .  tep_draw_hidden_field('checked_action_cancel_allow_' . $pm_row['payment_methods_id'], $checkedBoxCancelPaymentAllow) . tep_draw_hidden_field('unchecked_action_cancel_allow_' . $pm_row['payment_methods_id'], $uncheckedBoxCancelPaymentAllow)?></td>
            		          			            </tr>
<?
            }
        }
?>
            		          			        </table>
            		          			    </td>
            		          			</tr>
		          			            <tr>
		          			                <td align="right" colspan="2"><?=tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton') ?> &nbsp;</td>
        		          			    </tr>
		          			        </table>
		          			        
		          			        
                                    <script type="text/javascript">          
		          			                  function allowtoview(){  
                                                      var groupTopayments=document.getElementsByName('groups_to_payments[]');   
                                                      var allowToview=document.getElementById('allow_to_view');    
                                                      
                                                  if(allowToview.checked == true){
                                                      for(var i=0; i<groupTopayments.length; i++){  
                                                          groupTopayments[i].checked=true; 
                                                       }
                                                     }
                                                       
                                                  else{
                                                     for(var i=0; i<groupTopayments.length; i++){ 
                                                          groupTopayments[i].checked=false;  
                                                       
                                                     }
                                               }};
                                                     
                                              function allowtoprocess(){  
                                                      var groupTopaymentsallow=document.getElementsByName('groups_to_payments_allow[]');   
                                                      var allowToprocess=document.getElementById('allow_to_process');
                                                      
                                                  if(allowToprocess.checked == true){
                                                     for(var i=0; i<groupTopaymentsallow.length; i++){
                                                         groupTopaymentsallow[i].checked=true;  
                                                       }
                                                     }
                                                     
                                                  else{
                                                     for(var i=0; i<groupTopaymentsallow.length; i++){ 
                                                          groupTopaymentsallow[i].checked=false;  
                                                        }
                                                     }
                                              };
                                                       
                                              function allowtoupdate(){  
                                                      var groupToupdateallow=document.getElementsByName('groups_to_update_allow[]');   
                                                      var allowToupdate=document.getElementById('allow_to_update');
                                                      
                                                 if(allowToupdate.checked == true){
                                                     for(var i=0; i<groupToupdateallow.length; i++){  
                                                          groupToupdateallow[i].checked=true;  
                                                       
                                                       }
                                                     }
                                                          
                                                  else{
                                                          for(var i=0; i<groupToupdateallow.length; i++){ 
                                                       groupToupdateallow[i].checked=false;  
                                                       
                                                       }
                                                }};
                                                       
                                                       
                                              function allowtocancel(){  
                                                      var groupTocancelallow=document.getElementsByName('groups_to_cancel_allow[]');   
                                                      var allowTocancel=document.getElementById('allow_to_cancel');
                                                      
                                                  if(allowTocancel.checked == true){
                                                      for(var i=0; i<groupTocancelallow.length; i++){
                                                               groupTocancelallow[i].checked=true;  
                                                      
                                                          }
                                                     }
                                                          
                                                  else{
                                                       for(var i=0; i<groupTocancelallow.length; i++){
                                                                groupTocancelallow[i].checked=false;  
                                                       }
                                                       
                                                }};
                                                      
		          			                        </script>
                                    </form>
                                    
<?
        if ($HTTP_GET_VARS['gPath'] == 1) {
     		echo tep_draw_form('defineFlagsForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath']);
   		} else if ($HTTP_GET_VARS['gPath'] != 1) {
     		echo tep_draw_form('defineFlagsForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath'] . '&action=flag_group_define', 'post', 'enctype="multipart/form-data"');
     		echo tep_draw_hidden_field('admin_groups_id', $HTTP_GET_VARS['gPath']); 
   		}
?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                        <tr class="dataTableHeadingRow">
		          			                            <td width="2%">
		          			                                <div id="flag_link">
                                      							<a href="javascript:;" onclick="showHideBox('flag_selection', 'flag_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                      						</div>
		          			                            </td>
		          			                            <td class="dataTableHeadingContent" width="58%">&nbsp;<a name="flag"></a><?=TABLE_HEADING_GROUPS_USER_FLAGS?></td>
		          			                            <td class="dataTableHeadingContent" width="40%"><?=TABLE_HEADING_GROUPS_USER_FLAGS_MESSAGE?></td>
		          			                        </tr>
		          			                    </table>
		          			                </td>
		          			            </tr>
		          			            <tbody id="flag_selection" class="show">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
        $flags_select_sql = "	SELECT user_flags_id, user_flags_admin_groups_id, user_flags_name  
            					FROM " . TABLE_USER_FLAGS . " 
            					WHERE user_flags_status = 1
            					ORDER BY user_flags_id";
        $flags_result_sql = tep_db_query($flags_select_sql);
        if (tep_db_num_rows($flags_result_sql)) {
            while ($flags_row = tep_db_fetch_array($flags_result_sql)) {
                $actionFlagsSelectedGroups = $flags_row['user_flags_admin_groups_id'];
	       		$actionFlagsArray = explode(",", $actionFlagsSelectedGroups);
                
                $checkedFlags = in_array($load_permission_from_grp, $actionFlagsArray) ? true : false;
					
       			if (in_array($HTTP_GET_VARS['gPath'], $actionFlagsArray)) {
         			$del_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_diff ($actionFlagsArray, $del_boxes);
         			sort($result);
         			$checkedBoxFlags = $actionFlagsSelectedGroups;
         			$uncheckedBoxFlags = implode (",", $result);
       			} else {
         			$add_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_merge ($add_boxes, $actionFlagsArray);
         			sort($result);
         			$checkedBoxFlags = implode (",", $result);
         			$uncheckedBoxFlags = $actionFlagsSelectedGroups;
       			}
?>
            		          			            <tr class="dataTableRow">
            		          			                <td class="dataTableContent" width="60%"><?=$flags_row['user_flags_name']?></td>
            		          			                <td class="dataTableContent" width="40%"><?=tep_draw_checkbox_field('groups_to_flags[]', $flags_row['user_flags_id'], $checkedFlags, '', 'onClick="customer_flags_checking(this);"') .  tep_draw_hidden_field('checked_action_flags_' . $flags_row['user_flags_id'], $checkedBoxFlags) . tep_draw_hidden_field('unchecked_action_flags_' . $flags_row['user_flags_id'], $uncheckedBoxFlags)?></td>
            		          			            </tr>
<?
            }
        }
?>
            		          			        </table>
            		          			    </td>
            		          			</tr>
		          			            <tr>
		          			                <td align="right" colspan="2">
<?
        if ($HTTP_GET_VARS['gPath'] != 1) {
            echo  tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton');
        } else {
            echo tep_submit_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, '', 'inputButton'); 
        }
?>
        		          			        &nbsp;</td>
        		          			    </tr>
		          			        </table>
                                    </form>
<?
        if ($HTTP_GET_VARS['gPath'] == 1) {
     		echo tep_draw_form('defineSiteForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath']);
   		} else if ($HTTP_GET_VARS['gPath'] != 1) {
     		echo tep_draw_form('defineSiteForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath'] . '&action=site_group_define', 'post', 'enctype="multipart/form-data"');
     		echo tep_draw_hidden_field('admin_groups_id', $HTTP_GET_VARS['gPath']); 
   		}
?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                        <tr class="dataTableHeadingRow">
		          			                            <td width="2%">
		          			                                <div id="site_link">
                                      							<a href="javascript:;" onclick="showHideBox('site_selection', 'site_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                      						</div>
		          			                            </td>
		          			                            <td class="dataTableHeadingContent" width="58%">&nbsp;<a name="site"></a><?=TABLE_HEADING_GROUPS_SITE_CODE?></td>
		          			                            <td class="dataTableHeadingContent" width="40%"><?=TABLE_HEADING_GROUPS_SITE_CODE_MESSAGE?></td>
		          			                        </tr>
		          			                    </table>
		          			                </td>
		          			            </tr>
		          			            <tbody id="site_selection" class="show">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
        $site_select_sql = "SELECT site_id, buyback_admin_groups_id, site_name  
            				FROM " . TABLE_SITE_CODE . " 
            				WHERE site_has_buyback = 1
            				ORDER BY site_id";
        $site_result_sql = tep_db_query($site_select_sql);
        if (tep_db_num_rows($site_result_sql)) {
            while ($site_row = tep_db_fetch_array($site_result_sql)) {
                $actionSiteSelectedGroups = $site_row['buyback_admin_groups_id'];
	       		$actionSiteArray = explode(",", $actionSiteSelectedGroups);
                
                $checkedSite = in_array($load_permission_from_grp, $actionSiteArray) ? true : false;
					
       			if (in_array($HTTP_GET_VARS['gPath'], $actionSiteArray)) {
         			$del_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_diff ($actionSiteArray, $del_boxes);
         			sort($result);
         			$checkedBoxSite = $actionSiteSelectedGroups;
         			$uncheckedBoxSite = implode (",", $result);
       			} else {
         			$add_boxes = array($HTTP_GET_VARS['gPath']);
         			$result = array_merge ($add_boxes, $actionSiteArray);
         			sort($result);
         			$checkedBoxSite = implode (",", $result);
         			$uncheckedBoxSite = $actionSiteSelectedGroups;
       			}
?>
            		          			            <tr class="dataTableRow">
            		          			                <td class="dataTableContent" width="60%"><?=$site_row['site_name']?></td>
            		          			                <td class="dataTableContent" width="40%"><?=tep_draw_checkbox_field('groups_to_site[]', $site_row['site_id'], $checkedSite) .  tep_draw_hidden_field('checked_action_site_' . $site_row['site_id'], $checkedBoxSite) . tep_draw_hidden_field('unchecked_action_site_' . $site_row['site_id'], $uncheckedBoxSite)?></td>
            		          			            </tr>
<?
            }
        }
?>
            		          			        </table>
            		          			    </td>
            		          			</tr>
		          			            <tr>
		          			                <td align="right" colspan="2">
<?
        if ($HTTP_GET_VARS['gPath'] != 1) {
            echo  tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton');
        } else {
            echo tep_submit_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, '', 'inputButton'); 
        }
?>
        		          			        &nbsp;</td>
        		          			    </tr>
		          			        </table>
                                    </form>                               
<?
        if ($_GET['gPath'] == 1) {
     		echo tep_draw_form('defineProductForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $_GET['gPath']);
   		} else if ($_GET['gPath'] != 1) {
     		echo tep_draw_form('defineProductForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $_GET['gPath'] . '&action=products_group_define', 'post', 'enctype="multipart/form-data"');
     		echo tep_draw_hidden_field('admin_groups_id', $_GET['gPath']); 
   		}
?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                        <tr class="dataTableHeadingRow">
		          			                            <td width="2%">
		          			                                <div id="product_link">
                                      							<a href="javascript:;" onclick="showHideBox('product_selection', 'product_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                      						</div>
		          			                            </td>
		          			                            <td class="dataTableHeadingContent" width="58%">&nbsp;<a name="site"></a><?=TABLE_HEADING_GROUPS_PRODUCT?></td>
		          			                            <td class="dataTableHeadingContent" width="40%"><?=TABLE_HEADING_GROUPS_PRODUCT_MESSAGE?></td>
		          			                        </tr>
		          			                    </table>
		          			                </td>
		          			            </tr>
		          			            <tbody id="product_selection" class="show">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
       $product_select_sql = "	SELECT  custom_products_type_id, custom_products_type_name, custom_products_admin_group_id
            					FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
            					ORDER BY custom_products_type_id";
        $product_result_sql = tep_db_query($product_select_sql);
        if (tep_db_num_rows($product_result_sql)) {
            while ($product_row = tep_db_fetch_array($product_result_sql)) {
                $actionProductsSelectedGroups = $product_row['custom_products_admin_group_id'];
	       		$actionProductsArray = explode(",", $actionProductsSelectedGroups);
				
                $checkedProducts = in_array($load_permission_from_grp, $actionProductsArray) ? true : false;
				
       			if (in_array($_GET['gPath'], $actionProductsArray)) {
         			$del_boxes = array($_GET['gPath']);
         			$result = array_diff($actionProductsArray, $del_boxes);
         			sort($result);
         			$checkedBoxProducts = $actionProductsSelectedGroups;
         			$uncheckedBoxProducts = implode(",", $result);
       			} else {
         			$add_boxes = array($_GET['gPath']);
         			$result = array_merge($add_boxes, $actionProductsArray);
         			sort($result);
         			$checkedBoxProducts = implode(",", $result);
         			$uncheckedBoxProducts = $actionProductsSelectedGroups;
       			}
?>
            		          			            <tr class="dataTableRow">
            		          			                <td class="dataTableContent" width="60%"><?=$product_row['custom_products_type_name']?></td>
            		          			                <td class="dataTableContent" width="40%"><?=tep_draw_checkbox_field('groups_to_products[]', $product_row['custom_products_type_id'], $checkedProducts) .  tep_draw_hidden_field('checked_action_products_' . $product_row['custom_products_type_id'], $checkedBoxProducts) . tep_draw_hidden_field('unchecked_action_products_' . $product_row['custom_products_type_id'], $uncheckedBoxProducts)?></td>           		          			        
            		          			            </tr>
<?
            }
        }
?>
            		          			        </table>
            		          			    </td>
            		          			</tr>
		          			            <tr>
		          			                <td align="right" colspan="2">
<?
        if ($_GET['gPath'] != 1) {
            echo  tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $_GET['gPath'], '', 'inputButton');
        } else {
            echo tep_submit_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, '', 'inputButton'); 
        }
?>
        		          			        &nbsp;</td>
        		          			    </tr>
		          			        </table>
                                    </form>
<!------------------------------------------------------## above NEW DEVELOPMENT ##--------------------------------------------------------------->                                                                      
<?
        if ($HTTP_GET_VARS['gPath'] == 1) {
     		echo tep_draw_form('groupsControlForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath']);
   		} else if ($HTTP_GET_VARS['gPath'] != 1) {
     		echo tep_draw_form('groupsControlForm', FILENAME_ADMIN_MEMBERS, 'gID=' . $HTTP_GET_VARS['gPath'] . '&action=groups_control_define', 'post', 'enctype="multipart/form-data" onSubmit="return form_checking();"');
     		echo tep_draw_hidden_field('admin_groups_id', $HTTP_GET_VARS['gPath']);
   		}
?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                        <tr class="dataTableHeadingRow">
		          			                            <td width="2%">
		          			                                <div id="groups_link">
                                      							<a href="javascript:;" onclick="showHideBox('groups_control', 'groups_link', 'hide');"><?=tep_image(DIR_WS_ICONS . 'collapse_arrow.gif', IMAGE_ICON_HIDE_CONTENT, '15', '15', 'border="0"')?></a>
                                      						</div>
		          			                            </td>
		          			                            <td class="dataTableHeadingContent" width="58%">&nbsp;<a name="groups"></a><?=TABLE_HEADING_GROUPS_CONTROL?></td>
		          			                            <td class="dataTableHeadingContent" width="40%"><?=TABLE_HEADING_GROUPS_CONTROL_MESSAGE?></td>
		          			                        </tr>
		          			                    </table>
		          			                </td>
		          			            </tr>
		          			            <tbody id="groups_control" class="show">
		          			            <tr>
		          			                <td>
		          			                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
		          			                    	<tr class="dataTableRow">
		          			                    		<td>
<?
	$user_groups_all_array = array( array ('id' => '', "text" => 'Select User Groups', "type" => 'optgroup') );
	$user_groups_authorized_array = array( array ('id' => '', "text" => 'Authorized Groups', "type" => 'optgroup') );
    $authorized_groups_select_sql = "SELECT admin_groups_authorized 
            			FROM " . TABLE_ADMIN_GROUPS . " 
            			WHERE admin_groups_id = '" . $load_permission_from_grp . "'";
    $authorized_groups_result_sql = tep_db_query($authorized_groups_select_sql);
   	$authorized_groups_row = tep_db_fetch_array($authorized_groups_result_sql);
   	$authorized_groups_array = explode(",", $authorized_groups_row['admin_groups_authorized']);

    $groups_select_sql = "SELECT admin_groups_id, admin_groups_name 
            			FROM " . TABLE_ADMIN_GROUPS . " 
            			ORDER BY admin_groups_name";
    $groups_result_sql = tep_db_query($groups_select_sql);
    if (tep_db_num_rows($groups_result_sql)) {
            while ($groups_row = tep_db_fetch_array($groups_result_sql)) {
       			if (in_array($groups_row['admin_groups_id'], $authorized_groups_array)) {
					$user_groups_authorized_array[] = array('id' => $groups_row['admin_groups_id'],'text' => $groups_row['admin_groups_name']);
       			} else {
					$user_groups_all_array[] = array('id' => $groups_row['admin_groups_id'],'text' => $groups_row['admin_groups_name']);
       			}
            }
    }
	echo tep_draw_js_select_boxes('groups_control', $user_groups_all_array, $user_groups_authorized_array, ' size="10" style="width:20em;"');
?>
														</td>
													</tr>
            		          			        </table>
            		          			    </td>
            		          			</tr>
		          			            <tr>
		          			                <td align="right" colspan="2">
<?
        if ($HTTP_GET_VARS['gPath'] != 1) {
            echo  tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton');
        } else {
            echo tep_submit_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, '', 'inputButton'); 
        }
?>
									<script language="javascript">
										<!--
										function form_checking() {
											var selected_grp = document.forms['groupsControlForm'].elements['groups_control_to[]'];
											if (selected_grp != null) {
												for (x=0; x<(selected_grp.length); x++) {
							    					selected_grp.options[x].selected = true;
							  					}
						  					}
						  				}
					  					//-->
									</script>
        		          			        &nbsp;</td>
        		          			    </tr>
		          			        </table>
                                    </form>


									<script>
									<!--
										function validateSelect() {
											var multiSelect = document.getElementById('adm_files_cat_id');
											if (multiSelect.options[0].selected == true) {
												for (i=1; i<multiSelect.length; i++) {
													multiSelect.options[i].selected = false;
												}
											}
										}
										
										function updateFilesCatIds(fid, apply_all) {
											var files_cat_obj = document.getElementById('files_cat_' + fid);
											var sel_cat_array = new Array();
											var multiSelect = document.getElementById('adm_files_cat_id');
											for (i=0; i<multiSelect.length; i++) {
												if (multiSelect.options[i].selected == true) {
													sel_cat_array.push(multiSelect.options[i].value);
												}
											}
											
											if (apply_all) {
												var sel_cat_str = sel_cat_array.join(',');
												if (document.forms.length > 0) {
											    	var field = document.defineForm;
											    	for (i=0; i<field.length; i++) {
												      	if (field.elements[i].type == "hidden" && field.elements[i].id.search(/files_cat_/) == 0) {
												      		field.elements[i].value = sel_cat_str;
												      	}
											    	}
											  	}
											} else {
												if (files_cat_obj != null)	files_cat_obj.value = sel_cat_array.join(',');
											}
										}
									//-->
    								</script>
<? 	} else if ($HTTP_GET_VARS['gID']) { ?>
    									<table border="0" width="100%" cellspacing="0" cellpadding="2">
								        	<tr class="dataTableHeadingRow">
								            	<td class="dataTableHeadingContent">&nbsp;<?=TABLE_HEADING_GROUPS_NAME?></td>
								            	<td class="dataTableHeadingContent" align="center">&nbsp;<?=TABLE_HEADING_GROUPS_MEMBERS_NUM?></td>
								                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
											</tr>
<?
    	$add_groups_prepare = '\'0\'' ;
  		$del_groups_prepare = '\'0\'' ;
  		$count_groups = 0;
  		if(strlen($authorized_group_row['admin_groups_authorized'])>0){
	  		$db_groups_query_raw = "SELECT * FROM " . TABLE_ADMIN_GROUPS . "
  									WHERE admin_groups_id IN (".$authorized_group_row['admin_groups_authorized'].")
  									ORDER BY admin_groups_name";
			$db_groups_query = tep_db_query($db_groups_query_raw);
	  		while ($groups = tep_db_fetch_array($db_groups_query)) {
	    		$add_groups_prepare .= ',\'' . $groups['admin_groups_id'] . '\'' ;
	    		if (((!$HTTP_GET_VARS['gID']) || ($HTTP_GET_VARS['gID'] == $groups['admin_groups_id']) || ($HTTP_GET_VARS['gID'] == 'groups')) && (!$gInfo) ) {
	      			$gInfo = new objectInfo($groups);
	    		}
	    		if ( (is_object($gInfo)) && ($groups['admin_groups_id'] == $gInfo->admin_groups_id) ) {
	      			echo '                		<tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $groups['admin_groups_id'] . '&action=edit_group') . '\'">' . "\n";
	    		} else {
	      			echo '                		<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $groups['admin_groups_id']) . '\'">' . "\n";
	      			$del_groups_prepare .= ',\'' . $groups['admin_groups_id'] . '\'' ;
	    		}
	    		$db_groups_members_query = "SELECT COUNT(admin_id) AS count 
	    									FROM admin 
	    									WHERE admin_groups_id = " . $groups['admin_groups_id'];
	    		$db_groups_members = tep_db_query($db_groups_members_query);
	    		$count_groups_members = tep_db_fetch_array($db_groups_members)
?>
												<td class="dataTableContent">&nbsp;<b><?=$groups['admin_groups_name']?></b></td>
												<td class="dataTableContent" align="center"><?=$count_groups_members['count'] ?></td>
								                <td class="dataTableContent" align="right"><? if ( (is_object($gInfo)) && ($groups['admin_groups_id'] == $gInfo->admin_groups_id) ) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'gID=' . $groups['admin_groups_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
											</tr>
<?
	    		$count_groups++;
	  		}
  		}
?>
								        	<tr>
								            	<td colspan="3">
								            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
								                		<tr>
								                    		<td class="smallText" valign="top"><?=TEXT_COUNT_GROUPS . $count_groups?></td>
								                    		<td class="smallText" valign="top" align="right">
								                    		<? echo tep_button(TEXT_INFO_GROUPS_MEMBERS, TEXT_INFO_GROUPS_MEMBERS, FILENAME_ADMIN_MEMBERS, '', 'inputButton'); ?>
								                    		<? echo tep_button(TEXT_INFO_GROUPS_NEW, TEXT_INFO_GROUPS_NEW, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id . '&action=new_group', $manage_admin_groups_permission?'':'disabled="disabled"', 'inputButton'); ?>
								                    		<? echo tep_button(TEXT_INFO_GROUPS_EXPORT_PERMISSIONS, TEXT_INFO_GROUPS_EXPORT_PERMISSIONS, tep_href_link(FILENAME_ADMIN_MEMBERS, tep_get_all_get_params(array('action')).'action=export_permissions'), $manage_admin_members_permission?'':'disabled="disabled"', 'inputButton'); ?> &nbsp;</td>
								                  		</tr>
								                	</table>
								                </td>
											</tr>
										</table>
<? 	} else { ?> 
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
								        	<tr class="dataTableHeadingRow">
								            	<td class="dataTableHeadingContent"><?=TABLE_HEADING_EMAIL?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_GROUPS?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_CREATED?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_LOGDATE?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_USED_LIMIT?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_LOGNUM?></td>
<?php
		if ($reactivate_admin_members_permission) 
			echo 								'<td class="dataTableHeadingContent" align="center">'.TABLE_HEADING_LOGIN_FAILED_ATTEMPT.'</td>';
?>
								                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
											</tr>
<?php
        $db_admin_query_raw = "	select a.*, acl.admin_credit_limit_total, acl.admin_credit_limit_max from " . TABLE_ADMIN . " AS a left join " . TABLE_ADMIN_CREDIT_LIMIT . " AS acl on a.admin_id=acl.admin_id WHERE (admin_groups_id ='0' and admin_deletion_flag <> '1')";
	  	if (strlen($authorized_group_row['admin_groups_authorized']) > 0) {
			$db_admin_query_raw .= " OR admin_groups_id IN (".$authorized_group_row['admin_groups_authorized'].")";
		}
		
  		$db_admin_query_raw .= " ORDER BY admin_email_address";
    	$db_admin_split = new splitPageResults($HTTP_GET_VARS['page'], CUSTOM_MAX_DISPLAY_MEMBERS_RESULTS, $db_admin_query_raw, $db_admin_query_numrows);
    	$db_admin_split->show_all = false;
  		$db_admin_query = tep_db_query($db_admin_query_raw);
		
  		while ($admin = tep_db_fetch_array($db_admin_query)) {
    		$admin_group_query = tep_db_query("select admin_groups_name from " . TABLE_ADMIN_GROUPS . " where admin_groups_id = '" . $admin['admin_groups_id'] . "'");
    		if ($admin_group = tep_db_fetch_array ($admin_group_query)) {
    			;
    		} else {
    			$admin_group = array();
    		}
    		
    		if (((!$HTTP_GET_VARS['mID']) || ($HTTP_GET_VARS['mID'] == $admin['admin_id'])) && (!$mInfo) ) {
      			$mInfo_array = array_merge($admin, $admin_group);
      			$mInfo = new objectInfo($mInfo_array);
    		}
   			
   			$used_limit = number_format((double)$admin['admin_credit_limit_total'], 4, '.', '');
   			$total_limit = number_format((double)$admin['admin_credit_limit_max'], 4, '.', '');
   			
    		if ( (is_object($mInfo)) && ($admin['admin_id'] == $mInfo->admin_id) ) {
      			echo '                  	<tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" ondblclick="document.location.href=\'' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin['admin_id'] . '&action=edit_member') . '\'">' . "\n";
    		} else {
      			echo '                  	<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" ondblclick="document.location.href=\'' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin['admin_id']) . '\'">' . "\n";
    		}
			
			if(tep_not_null($admin['admin_logdate'])){
				$inactive_days = tep_day_diff($admin['admin_logdate'], date("Y-m-d", time()));
			} else {
				$inactive_days = tep_day_diff($admin['admin_created'], date("Y-m-d", time()));
			}
			
?>
												<td class="dataTableContent"><?php echo $admin['admin_email_address']; ?></td>
								                <td class="dataTableContent" align="center"><?php echo $admin_group['admin_groups_name']; ?></td>
								                <td class="dataTableContent" align="center"><?php if((!tep_not_null($admin['admin_logdate'])) && ($inactive_days > MAX_INACTIVE_ADMIN_LOGIN_DAYS)) echo '<span class="redIndicator">'; echo substr($admin['admin_created'],0,-3); if((!tep_not_null($admin['admin_logdate'])) && ($inactive_days > MAX_INACTIVE_ADMIN_LOGIN_DAYS)) echo '</span>'; ?></td>
								                <td class="dataTableContent" align="center"><?php if($inactive_days > MAX_INACTIVE_ADMIN_LOGIN_DAYS) echo '<span class="redIndicator">'; echo substr($admin['admin_logdate'],0,-3); if ($inactive_days > MAX_INACTIVE_ADMIN_LOGIN_DAYS) echo '</span>'; ?></td>
								                <td class="dataTableContent" align="center"><span class='<?=($used_limit > $total_limit ? 'redIndicator' : '')?>'><?=$used_limit.'/'.$total_limit?></span></td>
								                <td class="dataTableContent" align="center"><?php echo $admin['admin_lognum']; ?></td>
<?php
			if ($reactivate_admin_members_permission) {
				if ($admin['admin_login_attempt'] >= 10)	$admin['admin_login_attempt'] = '<a href="'.tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin['admin_id'] . '&action=reactivate_admin_members').'" onclick="return show_confirm(this);">'.$admin['admin_login_attempt'].'</a>';
				echo							'<td class="dataTableContent" align="center">'.$admin['admin_login_attempt'].'</td>';		
			}
?>
								                <td class="dataTableContent" align="right"><?php if ( (is_object($mInfo)) && ($admin['admin_id'] == $mInfo->admin_id) ) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $admin['admin_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
											</tr>
<?  	}  ?>
            								<tr>
								            	<td colspan="7">
								            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
								                  		<tr>
								                    		<td class="smallText" valign="top" align="right"><?php echo $db_admin_split->display_count($db_admin_query_numrows, CUSTOM_MAX_DISPLAY_MEMBERS_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_MEMBERS); ?><br><?php echo $db_admin_split->display_links($db_admin_query_numrows, CUSTOM_MAX_DISPLAY_MEMBERS_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
								                    		<td class="smallText" valign="top" align="right"><?php echo tep_button(TEXT_INFO_GROUPS_GROUPS, TEXT_INFO_GROUPS_GROUPS, FILENAME_ADMIN_MEMBERS . '?gID=groups', '', 'inputButton'); echo ' '; echo tep_button(TEXT_INFO_GROUPS_MEMBERS_NEW, TEXT_INFO_GROUPS_MEMBERS_NEW, FILENAME_ADMIN_MEMBERS . '?mID=' . $mInfo->admin_id . '&page=' . $HTTP_GET_VARS['page'] . '&action=new_member', $manage_admin_members_permission?'':'disabled="disabled"', 'inputButton'); ?> &nbsp;</td>
								                  		</tr>
								                	</table>
								                </td>
											</tr>
										</table>
<? 	} ?>
									</td>
<?
  	$heading = array();
  	$contents = array();
  	
  	switch ($HTTP_GET_VARS['action']) {
    	case 'new_member':
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW . '</b>');
      		$contents = array('form' => tep_draw_form('newmember', FILENAME_ADMIN_MEMBERS, 'action=member_new&page=' . $page, 'post', 'enctype="multipart/form-data"')); 
      		if ($HTTP_GET_VARS['error']) {
        		$contents[] = array('text' => TEXT_INFO_ERROR); 
      		}
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_FIRSTNAME . '<br>&nbsp;' . tep_draw_input_field('admin_firstname', '', "size=36")); 
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_LASTNAME . '<br>&nbsp;' . tep_draw_input_field('admin_lastname', '', "size=36"));
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_EMAIL . '<br>&nbsp;' . tep_draw_input_field('admin_email_address', '', "size=36"));
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_INSERT, TEXT_INFO_GROUPS_MEMBERS_INSERT, 'onClick="validateForm();return document.returnValue"', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_MEMBERS_CANCEL, TEXT_INFO_GROUPS_MEMBERS_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID'], '', 'inputButton'));
      		break;
    	case 'edit_member': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW . '</b>');
      		
      		$contents = array('form' => tep_draw_form('newmember', FILENAME_ADMIN_MEMBERS, 'action=member_edit&page=' . $page . '&mID=' . $HTTP_GET_VARS['mID'], 'post', 'enctype="multipart/form-data"')); 
      		if ($HTTP_GET_VARS['error']) {
        		$contents[] = array('text' => TEXT_INFO_ERROR); 
      		}
      		$contents[] = array('text' => tep_draw_hidden_field('admin_id', $mInfo->admin_id)); 
      		$contents[] = array('text' => tep_draw_hidden_field('admin_groups_id', $mInfo->admin_groups_id)); 
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_FIRSTNAME . '<br>&nbsp;' . tep_draw_input_field('admin_firstname', $mInfo->admin_firstname, "size=36")); 
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_LASTNAME . '<br>&nbsp;' . tep_draw_input_field('admin_lastname', $mInfo->admin_lastname, "size=36"));
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_EMAIL . '<br>&nbsp;' . tep_draw_input_field('admin_email_address', $mInfo->admin_email_address, "size=36")); 
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_UPDATE, TEXT_INFO_GROUPS_MEMBERS_UPDATE, 'onClick="validateForm();return document.returnValue"', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_MEMBERS_CANCEL, TEXT_INFO_GROUPS_MEMBERS_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID'], '', 'inputButton'));
      		break;
      	case 'edit_member_group':
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW . '</b>');      		
      		$contents = array('form' => tep_draw_form('newmember', FILENAME_ADMIN_MEMBERS, 'action=member_group_edit&page=' . $page . '&mID=' . $HTTP_GET_VARS['mID'], 'post', 'enctype="multipart/form-data"')); 
      		if ($HTTP_GET_VARS['error']) {
        		$contents[] = array('text' => TEXT_INFO_ERROR); 
      		}
      		$contents[] = array('text' => tep_draw_hidden_field('admin_id', $mInfo->admin_id)); 
       		$groups_array = array(array('id' => '0', 'text' => TEXT_NONE));
      		$groups_query_raw = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " ";
      		if(strlen($authorized_group_row['admin_groups_authorized'])>0){
	      		$groups_query_raw .= " WHERE admin_groups_id IN (".$authorized_group_row['admin_groups_authorized'].")";
	      	} else {
	      		$groups_query_raw .= " WHERE 0";
	      	}
	      	$groups_query_raw .= " ORDER BY admin_groups_name";
	     	$groups_query = tep_db_query($groups_query_raw);
       		while ($groups = tep_db_fetch_array($groups_query)) {
        			$groups_array[] = array('id' => $groups['admin_groups_id'],
											'text' => $groups['admin_groups_name']);
       		}
       		if ($mInfo->admin_id == 1) {
	       		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_GROUP . '<br>&nbsp;' . tep_draw_pull_down_menu('admin_groups_id', $groups_array, $mInfo->admin_groups_id, 'disabled="disabled"')); 
       		} else {
	       		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_GROUP . '<br>&nbsp;' . tep_draw_pull_down_menu('admin_groups_id', $groups_array, $mInfo->admin_groups_id, $assign_admin_group_members_permission?'':'disabled="disabled"')); 
	       	}
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_UPDATE, TEXT_INFO_GROUPS_MEMBERS_UPDATE, 'onClick="validateForm();return document.returnValue"', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_MEMBERS_CANCEL, TEXT_INFO_GROUPS_MEMBERS_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID'], '', 'inputButton'));
      		break;
    	case 'del_member': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE . '</b>');
   			$contents = array('form' => tep_draw_form('edit', FILENAME_ADMIN_MEMBERS, 'action=member_delete&page=' . $page . '&mID=' . $admin['admin_id'], 'post', 'enctype="multipart/form-data"')); 
   			$contents[] = array('text' => tep_draw_hidden_field('admin_id', $mInfo->admin_id));
   			$contents[] = array('align' => 'center', 'text' =>  sprintf(TEXT_INFO_DELETE_INTRO, $mInfo->admin_firstname . ' ' . $mInfo->admin_lastname));    
			$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_DELETE, TEXT_INFO_GROUPS_MEMBERS_DELETE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_MEMBERS_CANCEL, TEXT_INFO_GROUPS_MEMBERS_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID'], '', 'inputButton'));
      		break;
    	case 'new_group':
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_GROUPS . '</b>');
      		
      		$contents = array('form' => tep_draw_form('new_group', FILENAME_ADMIN_MEMBERS, 'action=group_new&gID=' . $gInfo->admin_groups_id, 'post', 'enctype="multipart/form-data"')); 
      		if ($HTTP_GET_VARS['gName'] == 'false') {
        		$contents[] = array('text' => TEXT_INFO_GROUPS_NAME_FALSE . '<br>&nbsp;');
      		} elseif ($HTTP_GET_VARS['gName'] == 'used') {
        		$contents[] = array('text' => TEXT_INFO_GROUPS_NAME_USED . '<br>&nbsp;');
      		}
      		$contents[] = array('text' => tep_draw_hidden_field('set_groups_id', substr($add_groups_prepare, 4)) );
      		$contents[] = array('text' => TEXT_INFO_GROUPS_NAME . '<br>');      
      		$contents[] = array('align' => 'center', 'text' => tep_draw_input_field('admin_groups_name'));      
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_INSERT, TEXT_INFO_GROUPS_INSERT, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id, '', 'inputButton'));    
      		break;
    	case 'edit_group': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_GROUP . '</b>');
			
      		$contents = array('form' => tep_draw_form('edit_group', FILENAME_ADMIN_MEMBERS, 'action=group_edit&gID=' . $HTTP_GET_VARS['gID'], 'post', 'enctype="multipart/form-data"')); 
      		if ($HTTP_GET_VARS['gName'] == 'false') {
        		$contents[] = array('text' => TEXT_INFO_GROUPS_NAME_FALSE . '<br>&nbsp;');
      		} elseif ($HTTP_GET_VARS['gName'] == 'used') {
        		$contents[] = array('text' => TEXT_INFO_GROUPS_NAME_USED . '<br>&nbsp;');
      		}
      		$contents[] = array('text' => TEXT_INFO_EDIT_GROUP_INTRO . '<br>&nbsp;<br>' . tep_draw_input_field('admin_groups_name', $gInfo->admin_groups_name)); 
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_SAVE, TEXT_INFO_GROUPS_SAVE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id, '', 'inputButton'));    
      		break;
    	case 'edit_group_members': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_GROUP_MEMBERS . '</b>');
      		$contents = array('form' => tep_draw_form('edit_group_members', FILENAME_ADMIN_MEMBERS, 'action=group_member_edit&gID=' . $gInfo->admin_groups_id)); 
      		$contents[] = array('text' => TEXT_INFO_EDIT_GROUP_MEMBERS_INTRO);
       		
       		$groups_members_array = array(array('id' => '0', 'text' => TEXT_SELECT_REMOVE_MEMBER));
      		$this_group_members_select_sql = "	SELECT admin_id, admin_email_address 
      											FROM " . TABLE_ADMIN . " 
      											WHERE admin_groups_id = '" . tep_db_input($gInfo->admin_groups_id) . "'
      											ORDER BY admin_email_address";
      		$this_group_members_result_sql = tep_db_query($this_group_members_select_sql);
       		while ($this_group_members_row = tep_db_fetch_array($this_group_members_result_sql)) {
        		$groups_members_array[] = array('id' => $this_group_members_row['admin_id'],
												'text' => $this_group_members_row['admin_email_address']);
       		}
       		
      		$contents[] = array('text' => tep_draw_pull_down_menu('group_member_remove_id', $groups_members_array));
      		$contents[] = array('text' => tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_REMOVE, TEXT_INFO_GROUPS_MEMBERS_REMOVE, ' name="btn_remove_admin" value="'. TEXT_INFO_GROUPS_MEMBERS_REMOVE .'" onClick="return updateAdminGroup(\''.TEXT_INFO_GROUPS_MEMBERS_REMOVE.'\')"', 'inputButton'));
      		$contents[] = array('text' => TEXT_INFO_EDIT_GROUP_MEMBERS_REMOVE . '<br><br>');
      		
      		$groups_non_members_array = array(array('id' => '0', 'text' => TEXT_SELECT_ADD_MEMBER));
      		$groups_authorized_members = tep_remove_from_authorized($gInfo->admin_groups_id, $authorized_group_row['admin_groups_authorized']);
      		
      		$groups_non_members_select_sql = "	SELECT admin_id, admin_email_address 
      											FROM " . TABLE_ADMIN . "
      											WHERE admin_groups_id = '' " . (tep_not_null($groups_authorized_members) ? " OR admin_groups_id IN (".$groups_authorized_members.")" : '') . "
      											ORDER BY admin_email_address";
      		$groups_non_members_result_sql = tep_db_query($groups_non_members_select_sql);
       		while ($groups_non_members_row = tep_db_fetch_array($groups_non_members_result_sql)) {
        			$groups_non_members_array[] = array('id' => $groups_non_members_row['admin_id'],
														'text' => $groups_non_members_row['admin_email_address']
														);
      		}
      		$contents[] = array('text' => tep_draw_pull_down_menu('group_member_add_id', $groups_non_members_array)); 
      		$contents[] = array('text' => tep_submit_button(TEXT_INFO_GROUPS_MEMBERS_ADD, TEXT_INFO_GROUPS_MEMBERS_ADD, ' name="btn_add_admin" value="'. TEXT_INFO_GROUPS_MEMBERS_ADD .'" onClick="return updateAdminGroup(\''.TEXT_INFO_GROUPS_MEMBERS_ADD.'\')"', 'inputButton'));
      		$contents[] = array('text' => TEXT_INFO_EDIT_GROUP_MEMBERS_ADD . '<br><br>'); 
      		
      		$contents[] = array('align' => 'center', 'text' => tep_button(TEXT_INFO_GROUPS_MEMBERS_BACK, TEXT_INFO_GROUPS_MEMBERS_BACK, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id, '', 'inputButton')); 
      		break;
    	case 'del_group': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_GROUPS . '</b>');
			
      		$contents = array('form' => tep_draw_form('delete_group', FILENAME_ADMIN_MEMBERS, 'action=group_delete&gID=' . $gInfo->admin_groups_id, 'post', 'enctype="multipart/form-data"')); 
      		if ($gInfo->admin_groups_id == 1) {
        		$contents[] = array('align' => 'center', 'text' => sprintf(TEXT_INFO_DELETE_GROUPS_INTRO_NOT, $gInfo->admin_groups_name));
        		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gID'], '', 'inputButton'));
      		} else {
        		$contents[] = array('text' => tep_draw_hidden_field('set_groups_id', substr($del_groups_prepare, 4)) );
        		$contents[] = array('align' => 'center', 'text' => sprintf(TEXT_INFO_DELETE_GROUPS_INTRO, $gInfo->admin_groups_name));    
        		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_GROUPS_DELETE, TEXT_INFO_GROUPS_DELETE, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_CANCEL, TEXT_INFO_GROUPS_CANCEL, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gID'], '', 'inputButton'));    
      		}
      		break;
    	case 'define_group':
    		$contents = array('form' => tep_draw_form('load_grps_permissions_form', FILENAME_ADMIN_MEMBERS, 'action=define_group&gPath=' . $HTTP_GET_VARS['gPath'], 'post'));
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DEFINE . '</b><a name="c_list"></a>');
      		$contents[] = array('text' => sprintf(TEXT_INFO_DEFINE_INTRO, $group_name['admin_groups_name']));
      		if ($HTTP_GET_VARS['gPath'] == 1) {
        		$contents[] = array('align' => 'center', 'text' => tep_button(TEXT_INFO_GROUPS_BACK, TEXT_INFO_GROUPS_BACK, FILENAME_ADMIN_MEMBERS . '?gID=' . $HTTP_GET_VARS['gPath'], '', 'inputButton'));
      		} else {
      			$load_from_groups_array = array ( array('id' => '', 'text' => TEXT_LOAD_FROM_GROUP) );
      			$admin_grp_select_sql = "	SELECT admin_groups_id, admin_groups_name 
      										FROM " . TABLE_ADMIN_GROUPS . "
      										WHERE admin_groups_id <> '" . tep_db_input($HTTP_GET_VARS['gPath']) . "'";
      			$groups_query = tep_db_query($admin_grp_select_sql);
        		while ($groups = tep_db_fetch_array($groups_query)) {
          			$load_from_groups_array[] = array(	'id' => $groups['admin_groups_id'],
                                  						'text' => $groups['admin_groups_name']);
        		}
				$contents[] = array('align' => 'left', 'text' => tep_draw_pull_down_menu("load_from_group", $load_from_groups_array, '', ' id="load_from_group" onChange="if (this.selectedIndex > 0) { if (confirm_action(\''.JS_CONFIRM_LOAD_LIST.'\')) { this.form.submit(); } else { this.selectedIndex=0; } }"') );
      			$contents[] = array('align' => 'left', 'text' => '<div id="admin_files_cat_div" style="padding: 0.2em;"></div>');
      		}
      		break;
    	case 'show_group': 
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_GROUP . '</b>');
        	$check_email_query = tep_db_query("select admin_email_address from " . TABLE_ADMIN . "");
        	while ($check_email = tep_db_fetch_array($check_email_query)) {
          		$stored_email[] = $check_email['admin_email_address'];
        	}
        	
        	if (in_array($HTTP_POST_VARS['admin_email_address'], $stored_email)) {
          		$checkEmail = "true";
        	} else {
          		$checkEmail = "false";
        	}
      		$contents = array('form' => tep_draw_form('show_group', FILENAME_ADMIN_MEMBERS, 'action=show_group&gID=groups', 'post', 'enctype="multipart/form-data"')); 
      		$contents[] = array('text' => $define_files['admin_files_name'] . tep_draw_input_field('level_edit', $checkEmail)); 
      		break;
    	case 'edit_credit_limit':
      		$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_CREDIT_LIMIT . '</b>');
      		$contents = array('form' => tep_draw_form('credit_limit_form', FILENAME_ADMIN_MEMBERS, 'action=update_credit_limit&page=' . $page . '&mID=' . $mInfo->admin_id, 'post', '')); 
                $admin_credit_limit = tep_get_admin_credit_limit($mInfo->admin_id);
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_DAILY_LIMIT . '&nbsp;' . tep_draw_input_field('admin_credit_limit_max', number_format($admin_credit_limit['admin_credit_limit_max'], 4, '.', ''), "size=10"));
      		$contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_TODAY_USED_LIMIT . '<br>&nbsp;' . '<span id="admin_credit_limit_total">' . number_format($admin_credit_limit['admin_credit_limit_total'], 4, '.', '') . '</span>&nbsp;&nbsp;' . tep_draw_checkbox_field('reset_credit_limit', '1', false, '', '') . TEXT_INFO_RESET);
                if ($manage_admin_team_credit_limit_permission) {
                    $contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_RESET_TEAM_LIMIT . '&nbsp;' . tep_draw_input_field('reset_team_limit_max', number_format($admin_credit_limit['reset_team_limit_max'], 4, '.', ''), "size=10"));
                    $contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_RESET_TEAM_LIMIT_TOTAL . '<br>&nbsp;' . '<span id="reset_team_limit_used">' . number_format($admin_credit_limit['reset_team_limit_used'], 4, '.', '') . '</span>&nbsp;&nbsp;' . tep_draw_checkbox_field('reset_team_limit_used', '1', false, '', '') . TEXT_INFO_RESET);
                }
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_CREDIT_LIMIT_UPDATE, TEXT_INFO_CREDIT_LIMIT_UPDATE, 'onClick="return validate_credit_limit();"', 'inputButton') . ' ' . tep_button(TEXT_INFO_CREDIT_LIMIT_CANCEL, TEXT_INFO_CREDIT_LIMIT_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id, '', 'inputButton'));
      		break;

      // cdkey view limit
      case 'edit_cdkey_view_limit':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_CDKEY_VIEW_LIMIT . '</b>');
          $contents = array('form' => tep_draw_form('cdkey_view_limit_form', FILENAME_ADMIN_MEMBERS, 'action=update_cdkey_view_limit&page=' . $page . '&mID=' . $mInfo->admin_id, 'post', ''));
          $admin_cdkey_view_limit = tep_get_admin_cdkey_view_limit($mInfo->admin_id);
          $contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_DAILY_LIMIT . '&nbsp;' . tep_draw_input_field('cdkey_limit', $admin_cdkey_view_limit['cdkey_limit'], "size=10"));
          $contents[] = array('text' => '<br>&nbsp;' . TEXT_INFO_TODAY_USED_LIMIT . '&nbsp;' . '<span id="cdkey_total_view">' . $admin_cdkey_view_limit['cdkey_total_view'] . '<br></span>&nbsp;&nbsp;' . tep_draw_checkbox_field('reset_cdkey_view_limit', '1', false, '', '') . TEXT_INFO_RESET);
          $contents[] = array('align' => 'center', 'text' => '<br>' . tep_submit_button(TEXT_INFO_CDKEY_VIEW_LIMIT_UPDATE, TEXT_INFO_CDKEY_VIEW_LIMIT_UPDATE, 'onClick="return validate_cdkey_view_limit();"', 'inputButton') . ' ' . tep_button(TEXT_INFO_CDKEY_VIEW_LIMIT_CANCEL, TEXT_INFO_CDKEY_VIEW_LIMIT_CANCEL, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id, '', 'inputButton'));
      break;

    	default:
      		if (is_object($mInfo)) {
        		$heading[] = array('text' => '<b>&nbsp;' . TEXT_INFO_HEADING_DEFAULT . '</b>');
                        if ($login_id != $mInfo->admin_id) {
                            $contents[] = array('align' => 'center', 'text' => tep_button(TEXT_INFO_MEMBERS_EDIT, TEXT_INFO_MEMBERS_EDIT, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id . '&action=edit_member', $manage_admin_members_permission?'':'disabled=disabled', 'inputButton') . ' ' . tep_button(TEXT_INFO_MEMBERS_GROUP_EDIT, TEXT_INFO_MEMBERS_GROUP_EDIT, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id . '&action=edit_member_group', $assign_admin_group_members_permission?'':'disabled=disabled', 'inputButton') . ' ' . tep_button(TEXT_INFO_MEMBERS_DELETE, TEXT_INFO_MEMBERS_DELETE, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id . '&action=del_member', $manage_admin_members_permission?'':'disabled=disabled', 'inputButton') . '<br /><br />' . tep_button(TEXT_INFO_BUTTON_CREDIT_LIMIT, TEXT_INFO_BUTTON_CREDIT_LIMIT, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id . '&action=edit_credit_limit', $manage_admin_credit_limit_permission ? '' : 'disabled=disabled', 'inputButton').' '. tep_button(TEXT_INFO_BUTTON_CDKEY_VIEW_LIMIT, TEXT_INFO_BUTTON_CDKEY_VIEW_LIMIT, FILENAME_ADMIN_MEMBERS . '?page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->admin_id . '&action=edit_cdkey_view_limit', $manage_admin_cdkey_view_limit_permission ? '' : 'disabled=disabled', 'inputButton'));
                        }
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_FULLNAME . '</b><br>&nbsp;' . $mInfo->admin_firstname . ' ' . $mInfo->admin_lastname);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_EMAIL . '</b><br>&nbsp;' . $mInfo->admin_email_address);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_GROUP . '</b>' . $mInfo->admin_groups_name);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_CREATED . '</b><br>&nbsp;' . $mInfo->admin_created);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_MODIFIED . '</b><br>&nbsp;' . $mInfo->admin_modified);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_LOGDATE . '</b><br>&nbsp;' . $mInfo->admin_logdate);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_LOGNUM . '</b>' . $mInfo->admin_lognum);

				$admin_credit_limit = tep_get_admin_credit_limit($mInfo->admin_id);
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_DAILY_LIMIT . '</b>' . number_format($admin_credit_limit['admin_credit_limit_max'], 4, '.', ''));
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_TODAY_USED_LIMIT . '</b>' . number_format($admin_credit_limit['admin_credit_limit_total'], 4, '.', ''));
		        $contents[] = array('text' => '&nbsp;<b>' . TEXT_INFO_RESET_TEAM_LIMIT . '</b>' . number_format($admin_credit_limit['reset_team_limit_used'], 4, '.', '') . '/' . number_format($admin_credit_limit['reset_team_limit_max'], 4, '.', ''));
		        $contents[] = array('text' => '<br>');
      		} elseif (is_object($gInfo)) {
        		$heading[] = array('text' => '<b>&nbsp;' . TEXT_INFO_HEADING_DEFAULT_GROUPS . '</b>');
                $contents[] = array('align' => 'center', 'text' => tep_button(TEXT_INFO_GROUPS_EDIT, TEXT_INFO_GROUPS_EDIT, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id . '&action=edit_group', $manage_admin_groups_permission?'':'disabled="disabled"', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_DELETE, TEXT_INFO_GROUPS_DELETE, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id . '&action=del_group', $manage_admin_groups_permission?'':'disabled="disabled"', 'inputButton') . '<br /><br />' . tep_button(TEXT_INFO_GROUPS_PERMISSION, TEXT_INFO_GROUPS_PERMISSION, FILENAME_ADMIN_MEMBERS . '?gPath=' . $gInfo->admin_groups_id . '&action=define_group', $edit_group_permission_permission?'':'disabled=disabled', 'inputButton') . ' ' . tep_button(TEXT_INFO_GROUPS_MEMBERS, TEXT_INFO_GROUPS_MEMBERS, FILENAME_ADMIN_MEMBERS . '?gID=' . $gInfo->admin_groups_id . '&action=edit_group_members', $assign_admin_group_members_permission?'':'disabled="disabled"', 'inputButton'));
                $contents[] = array('text' => '<br>' . TEXT_INFO_DEFAULT_GROUPS_INTRO . '<br>&nbsp');
      		}
  	}
	
  	if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    	echo '                      <td width="25%" valign="top" nowrap>' . "\n";
    
	    $box = new box;
	    echo $box->infoBox($heading, $contents);
	    echo '                      </td>' . "\n";
  	}
?>
		          			    </tr>
    		        		</table>
    		        	</td>
    		      	</tr>
    			</table>
    			<script language="javascript""><!--
    				function validate_credit_limit() {
						if (!currencyValidation(document.credit_limit_form.admin_credit_limit_max.value) || document.credit_limit_form.admin_credit_limit_max.value < 0) {
							alert("<?=JS_ALERT_DAILY_LIMIT_ENTERED_MUST_BE_A_NUMBER?>");
							return false;
						} else {
							return true;
						}
					}

    				function updateAdminGroup(update_type) {
						if(update_type == 'Remove') {
							var admin_id = document.forms['edit_group_members'].elements['group_member_remove_id'].value;
							if (admin_id == '0') {
								alert("<?=JS_ALERT_GROUP_REMOVE_MEMBER_NO_SELECTION?>");
								return false;
							} else if (admin_id == '1') {
								alert("<?=JS_ALERT_GROUP_REMOVE_MEMBER_ROOT_SELECTED?>");
								return false;
							}else {
		    					return confirm('<?=JS_CONFIRM_GROUP_REMOVE_MEMBER?>');
		    				}
	    				} else if (update_type == 'Add') {
	    					var admin_id = document.forms['edit_group_members'].elements['group_member_add_id'].value;
							if (admin_id == '0'){
								alert("<?=JS_ALERT_GROUP_ADD_MEMBER_NO_SELECTION?>");
								return false;
							} else if (admin_id == '1') {
								alert("<?=JS_ALERT_GROUP_REMOVE_MEMBER_ROOT_SELECTED?>");
								return false;
							} else {
		    					return confirm('<?=JS_CONFIRM_GROUP_ADD_MEMBER?>');
		    				}
	    				} else {
	    					alert("<?=JS_ALERT_GROUP_EDIT_MEMBER_ERROR?>");
	    					return false;
	    				}
    				}
    				
    				function show_confirm() {
    					return confirm("<?php echo JS_CONFIRM_REACTIVATE_ADMIN_MEMBERS; ?>"); 
					}
    			//--></script>
    		</td>
<!-- body_text_eof //-->
      	</tr>
    </table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>