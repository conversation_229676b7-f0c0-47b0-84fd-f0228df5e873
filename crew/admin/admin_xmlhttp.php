<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
require_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
require_once(DIR_WS_CLASSES . 'cache_abstract.php');
require_once(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$action = $_GET['action'];

if ($action != 'refresh_cache'){
	$languages_id = $language_id = (int)$_GET['lang'];
} 

if (isset($language_id) && tep_not_null($language_id)) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE  languages_id = '" . $language_id . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . FILENAME_ADMIN_XMLHTTP)) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . FILENAME_ADMIN_XMLHTTP);
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . ".php")) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . ".php");
	}
}

echo '<response>';
if (tep_not_null($action)) {
	switch($action) {
		case "get_categories_list_boxes":
			$admin_files_id = $_GET['fid'];
			$selected_cat_ids = trim($_GET['sel_cat']);
			$selected_cat_id_array = array();
			
			if (tep_not_null($selected_cat_ids)) {
				$selected_cat_id_array = explode(',', $selected_cat_ids);
				$selected_cat_id_array = array_filter($selected_cat_id_array, "filter_empty_val");
			}
			
			echo "<html_result>";
			
			$admin_file_select_sql = "SELECT admin_files_name FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . tep_db_input($admin_files_id) . "'";
			$admin_file_result_sql = tep_db_query($admin_file_select_sql);
			$admin_file_row = tep_db_fetch_array($admin_file_result_sql);
			
			echo "<cat_boxes_title><![CDATA[";
			echo sprintf(TEXT_FILE_CATS_SETTING, $admin_file_row['admin_files_name']);
			echo "]]></cat_boxes_title>";
			
			echo "<cat_boxes_html><![CDATA[";
			$cat_tree_array = tep_get_category_tree(0, '___', '');
			echo tep_draw_pull_down_menu('adm_files_cat_id[]', $cat_tree_array , $selected_cat_id_array, ' multiple="multiple" size=40 onChange="validateSelect(); document.getElementById(\'cat_save_draft_btn\').disabled=false; document.getElementById(\'cat_apply_all_btn\').disabled=false;" id="adm_files_cat_id"');
			echo "]]></cat_boxes_html>";
			
			echo "<nav_html><![CDATA[";
			echo tep_button(BUTTON_SAVE_DRAFT, ALT_BUTTON_SAVE_DRAFT, '', ' id="cat_save_draft_btn" onClick="updateFilesCatIds(\''.$admin_files_id.'\', false); this.disabled=true;" disabled', 'inputButton') . '&nbsp;';
			echo tep_button(BUTTON_APPLY_ALL, ALT_BUTTON_APPLY_ALL, '', ' id="cat_apply_all_btn" onClick="updateFilesCatIds(\''.$admin_files_id.'\', true); this.disabled=true; document.getElementById(\'cat_save_draft_btn\').disabled=true;" disabled', 'inputButton');
			echo "]]></nav_html>";
			
			echo "</html_result>";
			
			break;
		
		case "refresh_cache":
			echo $memcache_obj->doFlush();
			echo "<cache_refreshed>1</cache_refreshed>";
			
			break;
		
		default:
			echo "<result>Unknown request!</result>";
			
			break;
	}
}

echo '</response>';
?>