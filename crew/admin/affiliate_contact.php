<?
/*
  	$Id: affiliate_contact.php,v 1.4 2005/06/08 04:50:35 weichen Exp $
	
  	OSC-Affiliate
	Contribution based on:
    osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if ( ($HTTP_GET_VARS['action'] == 'send_email_to_user') && ($HTTP_POST_VARS['affiliate_email_address']) && (!$HTTP_POST_VARS['back_x']) ) {
	switch ($HTTP_POST_VARS['affiliate_email_address']) {
      	case '***':
        	$mail_query = tep_db_query("select affiliate_firstname, affiliate_lastname, affiliate_email_address from " . TABLE_AFFILIATE . " ");
        	$mail_sent_to = TEXT_ALL_AFFILIATES;
        	break;
//      case '**D':
//        $mail_query = tep_db_query("select affiliate_firstname, affiliate_lastname, affiliate_email_address from " . TABLE_AFFILIATE . " where affiliate_newsletter = '1'");
//        $mail_sent_to = TEXT_NEWSLETTER_AFFILIATE;
//        break;
      	default:
        	$affiliate_email_address = tep_db_prepare_input($HTTP_POST_VARS['affiliate_email_address']);
        	$mail_query = tep_db_query("select affiliate_firstname, affiliate_lastname, affiliate_email_address from " . TABLE_AFFILIATE . " where affiliate_email_address = '" . tep_db_input($affiliate_email_address) . "'");
        	$mail_sent_to = $HTTP_POST_VARS['affiliate_email_address'];
        	break;
	}
	
    $from = tep_db_prepare_input($HTTP_POST_VARS['from']);
    $subject = tep_db_prepare_input($HTTP_POST_VARS['subject']);
    $message = tep_db_prepare_input($HTTP_POST_VARS['message']);
	
    // Instantiate a new mail object
    $mimemessage = new email(array('X-Mailer: osC mailer'));
	
    // Build the text version
    $text = strip_tags($text);
    if (EMAIL_USE_HTML == 'true') {
      	$mimemessage->add_html($message);
    } else {
      	$mimemessage->add_text($message);
    }
	
    // Send message
    $mimemessage->build_message();
    while ($mail = tep_db_fetch_array($mail_query)) {
      	$mimemessage->send($mail['affiliate_firstname'] . ' ' . $mail['affiliate_lastname'], $mail['affiliate_email_address'], '', $from, $subject);
    }
	
    tep_redirect(tep_href_link(FILENAME_AFFILIATE_CONTACT, 'mail_sent_to=' . urlencode($mail_sent_to)));
}

if ( ($HTTP_GET_VARS['action'] == 'preview') && (!$HTTP_POST_VARS['affiliate_email_address']) ) {
    $messageStack->add(ERROR_NO_AFFILIATE_SELECTED, 'error');
}

if (tep_not_null($HTTP_GET_VARS['mail_sent_to'])) {
	$messageStack->add(sprintf(NOTICE_EMAIL_SENT_TO, $HTTP_GET_VARS['mail_sent_to']), 'notice');
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ( ($HTTP_GET_VARS['action'] == 'preview') && ($HTTP_POST_VARS['affiliate_email_address']) ) {
    switch ($HTTP_POST_VARS['affiliate_email_address']) {
      	case '***':
        	$mail_sent_to = TEXT_ALL_AFFILIATES;
        	break;
//      case '**D':
//        $mail_sent_to = TEXT_NEWSLETTER_AFFILIATES;
//        break;
      	default:
        	$mail_sent_to = $HTTP_POST_VARS['affiliate_email_address'];
        	break;
	}
?>
          						<tr><? echo tep_draw_form('mail', FILENAME_AFFILIATE_CONTACT, 'action=send_email_to_user'); ?>
            						<td>
            							<table border="0" width="100%" cellpadding="0" cellspacing="2">
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_AFFILIATE?></b><br><?=$mail_sent_to?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_FROM?></b><br><?=htmlspecialchars(stripslashes($HTTP_POST_VARS['from']))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_SUBJECT?></b><br><?=htmlspecialchars(stripslashes($HTTP_POST_VARS['subject']))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_MESSAGE?></b><br><?=nl2br(htmlspecialchars(stripslashes($HTTP_POST_VARS['message'])))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td>
<?
/* Re-Post all POST'ed variables */
    reset($HTTP_POST_VARS);
    while (list($key, $value) = each($HTTP_POST_VARS)) {
      	if (!is_array($HTTP_POST_VARS[$key])) {
        	echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
      	}
    }
?>
                									<table border="0" width="100%" cellpadding="0" cellspacing="2">
                  										<tr>
                    										<td><?=tep_image_submit('button_back.gif', IMAGE_BACK, 'name="back"')?></td>
                    										<td align="right"><? echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_CONTACT) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a> ' . tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL); ?></td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
            							</table>
            						</td>
          							</form>
          						</tr>
<?
} else {
?>
          						<tr><? echo tep_draw_form('mail', FILENAME_AFFILIATE_CONTACT, 'action=preview'); ?>
            						<td>
            							<table border="0" cellpadding="0" cellspacing="2">
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
<?
	$affiliate = array();
    $affiliate[] = array('id' => '', 'text' => TEXT_SELECT_AFFILIATE);
    $affiliate[] = array('id' => '***', 'text' => TEXT_ALL_AFFILIATES);
	//$affiliate[] = array('id' => '**D', 'text' => TEXT_NEWSLETTER_AFFILIATES);
    $mail_query = tep_db_query("select affiliate_email_address, affiliate_firstname, affiliate_lastname from " . TABLE_AFFILIATE . " order by affiliate_lastname");
    while($affiliate_values = tep_db_fetch_array($mail_query)) {
      	$affiliate[] = array(	'id' => $affiliate_values['affiliate_email_address'],
                           		'text' => $affiliate_values['affiliate_lastname'] . ', ' . $affiliate_values['affiliate_firstname'] . ' (' . $affiliate_values['affiliate_email_address'] . ')');
    }
?>
              								<tr>
                								<td class="main"><?=TEXT_AFFILIATE?></td>
                								<td><?=tep_draw_pull_down_menu('affiliate_email_address', $affiliate, $HTTP_GET_VARS['affiliate'])?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="main"><?=TEXT_FROM?></td>
                								<td><?=tep_draw_input_field('from', AFFILIATE_EMAIL_ADDRESS, 'size="60"')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="main"><?=TEXT_SUBJECT?></td>
                								<td><?=tep_draw_input_field('subject', '', 'size="60"')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td valign="top" class="main"><?=TEXT_MESSAGE?></td>
                								<td><?=tep_draw_textarea_field('message', 'soft', '60', '15')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td colspan="2" align="right"><?=tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL)?></td>
              								</tr>
            							</table>
            						</td>
          							</form>
          						</tr>
<?
}
?>
<!-- body_text_eof //-->
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>