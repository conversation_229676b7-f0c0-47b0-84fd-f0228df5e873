<?
/*
	$Id: affiliate_payment.php,v 1.6 2006/02/17 03:49:06 weichen Exp $
  	
  	OSC-Affiliate
  	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

if ($_REQUEST["resetSearch"]) {
	unset($_SESSION['affiliate_email']);
	unset($_SESSION['payment_status']);
	
	tep_redirect(tep_href_link(FILENAME_AFFILIATE_PAYMENT));
}

$payments_statuses = array();
$payments_status_array = array();
$payments_status_query = tep_db_query("select affiliate_payment_status_id, affiliate_payment_status_name from " . TABLE_AFFILIATE_PAYMENT_STATUS . " where affiliate_language_id = '" . $languages_id . "'");
while ($payments_status = tep_db_fetch_array($payments_status_query)) {
	$payments_statuses[] = array('id' => $payments_status['affiliate_payment_status_id'],
                                 'text' => $payments_status['affiliate_payment_status_name']);
    $payments_status_array[$payments_status['affiliate_payment_status_id']] = $payments_status['affiliate_payment_status_name'];
}

//echo date("F j, Y, g:i:s a", mktime(0, 0, 0, date("m"), date("d") - AFFILIATE_BILLING_TIME, date("Y")));

switch ($_REQUEST['action'])
{
	case 'start_billing':
		// Billing can be a lengthy process
      	tep_set_time_limit(0);
		// We are only billing orders which are AFFILIATE_BILLING_TIME days old
      	$time = mktime(0, 0, 0, date("m"), date("d") - AFFILIATE_BILLING_TIME, date("Y"));
      	$oldday = date("Y-m-d", $time);
      	
        // Get all the order in reclaim status which had been paid commission in previous payment
        // No need to filter by eligible orders period since it look for those had been billed
        $affiliate_overpay_array = array();
        if (tep_not_null(AFFILIATE_RECLAIM_ORDER_STATUS)) {
        	$paid_reversed_orders_select_sql = "SELECT a.affiliate_id, a.affiliate_orders_id AS orders_id, a.affiliate_payment 
        										FROM " . TABLE_AFFILIATE_SALES . " AS a, " . TABLE_ORDERS . " AS o 
        										WHERE o.orders_id=a.affiliate_orders_id AND o.orders_status IN (" . AFFILIATE_RECLAIM_ORDER_STATUS . ") 
        											AND o.last_modified<='".$oldday."' AND a.affiliate_commission_reclaim=0 AND a.affiliate_billing_status=1
        										";
        	$paid_reversed_orders_result_sql = tep_db_query($paid_reversed_orders_select_sql);
        	
	        while ($paid_reversed_orders_row = tep_db_fetch_array($paid_reversed_orders_result_sql)) {
	       		$affiliate_overpay_array[$paid_reversed_orders_row["affiliate_id"]]["orders_id"][] = $paid_reversed_orders_row["orders_id"];
	       		$affiliate_overpay_array[$paid_reversed_orders_row["affiliate_id"]]["commission"] += $paid_reversed_orders_row["affiliate_payment"];
	       	}
       	} else {
       		$messageStack->add_session(WARNING_EMPTY_RECLAIM_STATUS, 'warning');
       	}
        
        if (tep_not_null(AFFILIATE_COMMISSSION_ORDER_STATUS)) {
	       	// Select all affiliates who earned enough money since last payment
	       	$affiliate_payment_info = array();
	       	
	       	if (tep_not_null(AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD)) {
	       		$date_purchase_where_str = " DATE_FORMAT(o.date_purchased, '%Y-%m-%d %H:%i:%s') <= DATE_ADD(ci.customers_info_date_account_created, INTERVAL " . (int)AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD . " DAY) ";
	       	} else {
	       		$date_purchase_where_str = ' 1 ';
	       	}
	       	
			$affiliate_payment_sql = "	SELECT a.affiliate_id, sum(a.affiliate_payment) AS total_commission
	          							FROM " . TABLE_AFFILIATE_SALES . " AS a, " . TABLE_ORDERS . " AS o, " . TABLE_CUSTOMERS_INFO . " AS ci 
	          							WHERE a.affiliate_billing_status != 1 
	          								AND a.affiliate_orders_id=o.orders_id 
	          								AND o.customers_id = ci.customers_info_id 
	          								AND o.orders_status IN (" . AFFILIATE_COMMISSSION_ORDER_STATUS . ") 
	          								AND o.last_modified <= '" . $oldday . "' 
	          								AND " . $date_purchase_where_str . "
	          							GROUP by a.affiliate_id 
	          							HAVING total_commission >= '" . AFFILIATE_THRESHOLD . "'
	          						";
	        $affiliate_payment_result_sql = tep_db_query($affiliate_payment_sql);
	        
	        while ($affiliate_payment_row_sql = tep_db_fetch_array($affiliate_payment_result_sql)) {
				if (in_array($affiliate_payment_row_sql["affiliate_id"], array_keys($affiliate_overpay_array))) {
					// need to reclaim reversed commission from the affiliate's current elegible commission
					$actual_commission = $affiliate_payment_row_sql["total_commission"] - $affiliate_overpay_array[$affiliate_payment_row_sql["affiliate_id"]]["commission"];
					if ($actual_commission >= AFFILIATE_THRESHOLD)
						$affiliate_payment_info[$affiliate_payment_row_sql["affiliate_id"]] = $actual_commission;
				} else {
					$affiliate_payment_info[$affiliate_payment_row_sql["affiliate_id"]] = $affiliate_payment_row_sql["total_commission"];
				}
			}
			
			foreach ($affiliate_payment_info as $cur_affiliate_id=>$commission) {
				// mysql does not support joins in update (planned in 4.x)
				// Get all orders which are AFFILIATE_BILLING_TIME days old
				$affiliate_orders_select_sql = 	"	SELECT a.affiliate_orders_id 
	          										FROM " . TABLE_AFFILIATE_SALES . " a, " . TABLE_ORDERS . " o, " . TABLE_CUSTOMERS_INFO . " AS ci 
	          										WHERE a.affiliate_billing_status !=1 
	          											AND a.affiliate_orders_id=o.orders_id 
	          											AND o.customers_id = ci.customers_info_id 
	          											AND o.orders_status IN (" . AFFILIATE_COMMISSSION_ORDER_STATUS . ") 
	          											AND a.affiliate_id ='" . $cur_affiliate_id . "' 
	          											AND o.last_modified <= '" . $oldday . "' 
	          											AND " . $date_purchase_where_str ;
	        	$affiliate_orders_result_sql = tep_db_query ($affiliate_orders_select_sql);
	        	$orders_id = "(";
	        	while ($affiliate_orders_row_sql = tep_db_fetch_array($affiliate_orders_result_sql)) {
	          		$orders_id .= $affiliate_orders_row_sql['affiliate_orders_id'] . ",";
	        	}
	        	$orders_id = substr($orders_id, 0, -1) .")";
	        	
	        	// Set the Sales to Temp State (it may happen that an order happend while billing)
	        	$status_update_sql = "	UPDATE " . TABLE_AFFILIATE_SALES . " 
	        							SET affiliate_billing_status=99 
	          							WHERE affiliate_id='" .  $cur_affiliate_id . "' 
	          								AND affiliate_orders_id IN " . $orders_id . " 
	        						";
	        	tep_db_query ($status_update_sql);
	        	
	        	// Get Sum of payment (Could have changed since last selects);
	        	$affiliate_billing_sql = "	SELECT sum(affiliate_payment) as affiliate_payment
	          								FROM " . TABLE_AFFILIATE_SALES . " 
	          								WHERE affiliate_id='" .  $cur_affiliate_id . "' and  affiliate_billing_status=99 
	        						  	";
	        	$affiliate_billing_result_sql = tep_db_query ($affiliate_billing_sql);
	        	$affiliate_billing = tep_db_fetch_array($affiliate_billing_result_sql);
				
				// Get affiliate Informations
	        	$affiliate_info_sql = 	"	SELECT a.*, c.countries_id, c.countries_name, c.countries_iso_code_2, c.countries_iso_code_3, c.address_format_id 
	          								FROM " . TABLE_AFFILIATE . " a 
	          								LEFT JOIN " . TABLE_ZONES . " z on (a.affiliate_zone_id  = z.zone_id) 
	          								LEFT JOIN " . TABLE_COUNTRIES . " c on (a.affiliate_country_id = c.countries_id)
	          								WHERE affiliate_id = '" . $cur_affiliate_id . "' 
	        							";
	        	$affiliate_info_result_sql = tep_db_query ($affiliate_info_sql);
	        	$affiliate = tep_db_fetch_array($affiliate_info_result_sql);
				
				// Get need tax informations for the affiliate
	        	$affiliate_tax_rate = tep_get_affiliate_tax_rate(AFFILIATE_TAX_ID, $affiliate['affiliate_country_id'], $affiliate['affiliate_zone_id']);
	        	$affiliate_tax = tep_round(($affiliate_billing['affiliate_payment'] * $affiliate_tax_rate / 100), 2); // Netto-Provision
	        	$affiliate_payment_total = $affiliate_billing['affiliate_payment'] + $affiliate_tax;
				
				if (in_array($cur_affiliate_id, array_keys($affiliate_overpay_array))) {
					// need to reclaim reversed commission from the affiliate's current elegible commission
					$reclaim_tax = tep_round(($affiliate_overpay_array[$cur_affiliate_id]["commission"] * $affiliate_tax_rate / 100), 2); // Netto-Provision
					$affiliate_payment_reclaimed_total = $affiliate_overpay_array[$cur_affiliate_id]["commission"] + $reclaim_tax;
				} else {
					$affiliate_payment_reclaimed_total = "0.00";
				}
				
				if (isset($affiliate_overpay_array[$cur_affiliate_id]["orders_id"]) && count($affiliate_overpay_array[$cur_affiliate_id]["orders_id"])) {
					$affiliate_payment_reclaimed_sales = implode(",", $affiliate_overpay_array[$cur_affiliate_id]["orders_id"]);
				} else {
					$affiliate_payment_reclaimed_sales = '';
				}
				
				// Bill the order
	        	$affiliate['affiliate_state'] = tep_get_zone_code($affiliate['affiliate_country_id'], $affiliate['affiliate_zone_id'], $affiliate['affiliate_state']);
	        	$sql_data_array = array('affiliate_id' => $cur_affiliate_id,
	                                	'affiliate_payment' => $affiliate_billing['affiliate_payment'],
	                                	'affiliate_payment_tax' => $affiliate_tax,
	                                	'affiliate_payment_total' => $affiliate_payment_total,
	                                	'affiliate_payment_date' => 'now()',
	                                	'affiliate_payment_reclaimed_total' => $affiliate_payment_reclaimed_total,
	                                	'affiliate_payment_reclaimed_sales' => $affiliate_payment_reclaimed_sales,
	                                	'affiliate_payment_last_modified' => 'now()',
	                                	'affiliate_payment_status' => '0',
	                                	'affiliate_firstname' => $affiliate['affiliate_firstname'],
	                                	'affiliate_lastname' => $affiliate['affiliate_lastname'],
	                                	'affiliate_street_address' => $affiliate['affiliate_street_address'],
	                                	'affiliate_suburb' => $affiliate['affiliate_suburb'],
	                                	'affiliate_city' => $affiliate['affiliate_city'],
	                                	'affiliate_country' => $affiliate['countries_name'],
	                                	'affiliate_postcode' => $affiliate['affiliate_postcode'],
	                                	'affiliate_company' => $affiliate['affiliate_company'],
	                                	'affiliate_state' => $affiliate['affiliate_state'],
	                                	'affiliate_address_format_id' => $affiliate['address_format_id']);
	        	tep_db_perform(TABLE_AFFILIATE_PAYMENT, $sql_data_array);
	        	$insert_id = tep_db_insert_id();
	        	
	        	// Set the Sales to Final State 
	        	$billing_status_update_sql = "	UPDATE " . TABLE_AFFILIATE_SALES . 
	        								 "	SET affiliate_payment_id='" . $insert_id . "', affiliate_billing_status=1 
	        								 	WHERE affiliate_id='" . $cur_affiliate_id . "' AND affiliate_billing_status=99 ";
	        	tep_db_query($billing_status_update_sql);
				
				if (isset($affiliate_overpay_array[$cur_affiliate_id]["orders_id"]) && count($affiliate_overpay_array[$cur_affiliate_id]["orders_id"])) {
					$overpay_reclaim_update_sql = "	UPDATE " . TABLE_AFFILIATE_SALES . "
													SET affiliate_commission_reclaim=1  
	        								 		WHERE affiliate_orders_id IN (" . implode(",", $affiliate_overpay_array[$cur_affiliate_id]["orders_id"]) . ")";
	        		tep_db_query($overpay_reclaim_update_sql);
				}
				// Notify Affiliate
	        	if (AFFILIATE_NOTIFY_AFTER_BILLING == 'true') {
					$check_status_query = tep_db_query("SELECT af.affiliate_email_address, ap.affiliate_lastname, ap.affiliate_firstname, ap.affiliate_payment_status, ap.affiliate_payment_date, ap.affiliate_payment_date FROM " . TABLE_AFFILIATE_PAYMENT . " ap, " . TABLE_AFFILIATE . " af WHERE affiliate_payment_id='" . $insert_id . "' AND af.affiliate_id=ap.affiliate_id ");
	          		$check_status = tep_db_fetch_array($check_status_query);
	          		$email = STORE_NAME . "\n" . EMAIL_SEPARATOR . "\n" . EMAIL_TEXT_AFFILIATE_PAYMENT_NUMBER . ' ' . $insert_id . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_AFFILIATE_PAYMENT_INFO, 'payment_id=' . $insert_id, 'SSL') . "\n" . EMAIL_TEXT_PAYMENT_BILLED . ' ' . tep_date_long($check_status['affiliate_payment_date']) . "\n\n" . EMAIL_TEXT_NEW_PAYMENT;
	          		tep_mail($check_status['affiliate_firstname'] . ' ' . $check_status['affiliate_lastname'], $check_status['affiliate_email_address'], EMAIL_TEXT_SUBJECT, nl2br($email), STORE_OWNER, AFFILIATE_EMAIL_ADDRESS);
	        	}
			}
			$messageStack->add_session(SUCCESS_BILLING, 'success');
			
	      	tep_redirect(tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action')) . 'action=edit'));
	    } else {
	    	$messageStack->add_session(WARNING_EMPTY_COMMISSION_STATUS, 'warning');
	    	tep_redirect(tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action'))));
	    }
      	break;
	case 'update_payment':
    	$pID = tep_db_prepare_input($_REQUEST['pID']);
      	$status = tep_db_prepare_input($HTTP_POST_VARS['status']);	// from selection box (latest)
		
      	$payment_updated = false;
      	$check_status_query = tep_db_query("select af.affiliate_email_address, ap.affiliate_id, ap.affiliate_lastname, ap.affiliate_firstname, ap.affiliate_payment_status, ap.affiliate_payment_date, ap.affiliate_payment_date from " . TABLE_AFFILIATE_PAYMENT . " ap, " . TABLE_AFFILIATE . " af where affiliate_payment_id = '" . tep_db_input($pID) . "' and af.affiliate_id = ap.affiliate_id ");
      	$check_status = tep_db_fetch_array($check_status_query);
      	if ($check_status['affiliate_payment_status'] != $status) {
        	tep_db_query("update " . TABLE_AFFILIATE_PAYMENT . " set affiliate_payment_status = '" . tep_db_input($status) . "', affiliate_payment_last_modified = now() where affiliate_payment_id = '" . tep_db_input($pID) . "'");
        	$affiliate_notified = '0';
			// Notify Affiliate
        	if ($HTTP_POST_VARS['notify'] == 'on') {
          		$email = STORE_NAME . "\n" . EMAIL_SEPARATOR . "\n" . EMAIL_TEXT_AFFILIATE_PAYMENT_NUMBER . ' ' . $pID . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_AFFILIATE_PAYMENT_INFO, 'payment_id=' . $pID, 'SSL') . "\n" . EMAIL_TEXT_PAYMENT_BILLED . ' ' . tep_date_long($check_status['affiliate_payment_date']) . "\n\n" . sprintf(EMAIL_TEXT_STATUS_UPDATE, $payments_status_array[$status]);
          		tep_mail($check_status['affiliate_firstname'] . ' ' . $check_status['affiliate_lastname'], $check_status['affiliate_email_address'], EMAIL_TEXT_SUBJECT, nl2br($email), STORE_OWNER, AFFILIATE_EMAIL_ADDRESS);
          		$affiliate_notified = '1';
        	}
			
        	tep_db_query("insert into " . TABLE_AFFILIATE_PAYMENT_STATUS_HISTORY . " (affiliate_payment_id, affiliate_new_value, affiliate_old_value, affiliate_date_added, affiliate_notified) values ('" . tep_db_input($pID) . "', '" . tep_db_input($status) . "', '" . $check_status['affiliate_payment_status'] . "', now(), '" . $affiliate_notified . "')");
        	switch($status) {
        		case "0":
        			$affiliate_account_select_sql = "SELECT affiliate_paid_amount FROM " . TABLE_AFFILIATE_ACCOUNT . " WHERE affiliate_payment_id='". tep_db_input($pID) ."'";
        			$affiliate_account_result_sql = tep_db_query($affiliate_account_select_sql);
      				if ($affiliate_account = tep_db_fetch_array($affiliate_account_result_sql)) {
      					if ($affiliate_account["affiliate_paid_amount"]>0) {
      						// had been paid before
      						$affiliate_account_update_sql = "UPDATE " . TABLE_AFFILIATE_ACCOUNT . " SET affiliate_paid_amount='0.00' WHERE affiliate_payment_id='". tep_db_input($pID) ."'";
		        			tep_db_query($affiliate_account_update_sql);
      					}
      				}
        			break;
        		case "1":
        			$affiliate_account_select_sql = "SELECT affiliate_paid_amount FROM " . TABLE_AFFILIATE_ACCOUNT . " WHERE affiliate_payment_id='". tep_db_input($pID) ."'";
        			$affiliate_account_result_sql = tep_db_query($affiliate_account_select_sql);
      				if ($affiliate_account = tep_db_fetch_array($affiliate_account_result_sql)) {
      					$affiliate_account_update_sql = "UPDATE " . TABLE_AFFILIATE_ACCOUNT . " SET affiliate_paid_amount='" . $_REQUEST["pay_amount"] . "' WHERE affiliate_payment_id='". tep_db_input($pID) ."'";
		        		tep_db_query($affiliate_account_update_sql);
      				} else {
      					tep_db_query("insert into " . TABLE_AFFILIATE_ACCOUNT . " (affiliate_id, affiliate_payment_id, affiliate_total_amount, affiliate_reclaim_amount, affiliate_paid_amount, affiliate_transaction_date) values (".$check_status['affiliate_id'].", '" . tep_db_input($pID) . "', '" . $_REQUEST["affiliate_payment_total"] . "', '" . $_REQUEST["reclaim_amount"] . "', '" . $_REQUEST["pay_amount"] . "', now())");
      				}
        			break;
        	}
        	$payment_updated = true;
		}
		
      	if ($payment_updated) {
       		$messageStack->add_session(SUCCESS_PAYMENT_UPDATED, 'success');
      	}
		
      	tep_redirect(tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action')) . 'action=edit'));
      	break;
	case 'deleteconfirm':
    	$pID = tep_db_prepare_input($_REQUEST['pID']);
      	tep_db_query("delete from " . TABLE_AFFILIATE_PAYMENT . " where affiliate_payment_id = '" . tep_db_input($pID) . "'");
      	tep_db_query("delete from " . TABLE_AFFILIATE_PAYMENT_STATUS_HISTORY . " where affiliate_payment_id = '" . tep_db_input($pID) . "'");
      	tep_redirect(tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action'))));
		break;
}

if ( ($_REQUEST['action'] == 'edit') && tep_not_null($_REQUEST['pID']) ) {
	$pID = tep_db_prepare_input($_REQUEST['pID']);
	$affiliate_payment_select_sql = "select p.*, DATE_FORMAT(p.affiliate_payment_date, '%Y-%m-%d') as affiliate_payment_date, a.affiliate_payment_check, a.affiliate_payment_paypal, a.affiliate_payment_bank_name, a.affiliate_payment_bank_branch_number, a.affiliate_payment_bank_swift_code, a.affiliate_payment_bank_account_name, a.affiliate_payment_bank_account_number from " .  TABLE_AFFILIATE_PAYMENT . " p, " . TABLE_AFFILIATE . " a where affiliate_payment_id = '" . tep_db_input($pID) . "' and a.affiliate_id = p.affiliate_id";
    $payments_query = tep_db_query($affiliate_payment_select_sql);
    $payments_exists = true;
    if (!$payments = tep_db_fetch_array($payments_query)) {
      	$payments_exists = false;
      	$messageStack->add(sprintf(ERROR_PAYMENT_DOES_NOT_EXIST, $pID), 'error');
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?	require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?=BOX_WIDTH?>" valign="top">
    		<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?	require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    		</table>
    	</td>
<!-- body_text //-->
    	<td width="100%" valign="top">
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
  	if ( ($_REQUEST['action'] == 'edit') && ($payments_exists) ) {
    	$affiliate_address['firstname'] = $payments['affiliate_firstname'];
    	$affiliate_address['lastname'] = $payments['affiliate_lastname'];
    	$affiliate_address['street_address'] = $payments['affiliate_street_address'];
    	$affiliate_address['suburb'] = $payments['affiliate_suburb'];
    	$affiliate_address['city'] = $payments['affiliate_city'];
    	$affiliate_address['state'] = $payments['affiliate_state'];
    	$affiliate_address['country'] = $payments['affiliate_country'];
    	$affiliate_address['postcode'] = $payments['affiliate_postcode'];
    	
    	if (!$payments['affiliate_payment_status']) {
	    	$affiliate_account_sql = "	SELECT SUM(affiliate_total_amount) AS total_eligible_payment, 
	    										SUM(affiliate_paid_amount) AS total_actual_payment, 
	    										SUM(affiliate_reclaim_amount) AS total_reclaim_amount 
	    									FROM " . TABLE_AFFILIATE_ACCOUNT . " 
	    									WHERE affiliate_id=".$payments['affiliate_id'];
	    	$affiliate_account_result_sql = tep_db_query($affiliate_account_sql);
	    	if ($affiliate_account = tep_db_fetch_array($affiliate_account_result_sql)) {
	    		$total_held_amount = $affiliate_account["total_eligible_payment"] - $affiliate_account["total_reclaim_amount"] - $affiliate_account["total_actual_payment"];
	    	} else {
	    		$total_held_amount = "0.00";
	    	}
    	} else {
	    	$affiliate_account_sql = "	SELECT affiliate_paid_amount  
										FROM " . TABLE_AFFILIATE_ACCOUNT . " 
										WHERE affiliate_id=".$payments['affiliate_id']." AND affiliate_payment_id='".$pID."'";
	    	$affiliate_account_result_sql = tep_db_query($affiliate_account_sql);
	    	$affiliate_account = tep_db_fetch_array($affiliate_account_result_sql);
	    }
?>
      			<tr>
        			<td width="100%">
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
            					<td class="pageHeading" align="right"><? echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action'))) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
          					</tr>
        				</table>
        			</td>
      			</tr>
      			<tr>
        			<td>
        				<table width="100%" border="0" cellspacing="0" cellpadding="2">
          					<tr>
            					<td colspan="2"><?=tep_draw_separator()?></td>
          					</tr>
          					<tr>
            					<td valign="top" colspan="2" width="100%">
            						<table border="0" width="100%" cellspacing="0" cellpadding="2">
              							<tr>
                							<td class="main" valign="top" width="25%"><b><?=TEXT_AFFILIATE?></b></td>
                							<td class="main" width="75%"><?=tep_address_format($payments['affiliate_address_format_id'], $affiliate_address, 1, '&nbsp;', '<br>')?></td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main"><b><?=TEXT_AFFILIATE_BILLED?></b></td>
                							<td class="main"><?=$payments['affiliate_payment_date']?></td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main" valign="top"><b><?=TEXT_AFFILIATE_PAYING_POSSIBILITIES?></b></td>
                							<td class="main">
                								<table border="1" cellspacing="0" cellpadding="5">
                  									<tr>
<?
  			if (AFFILIATE_USE_BANK == 'true') {
?>
				<td class="main"  valign="top"><? echo '<b>' . TEXT_AFFILIATE_PAYMENT_BANK_TRANSFER . '</b><br><br>' . TEXT_AFFILIATE_PAYMENT_BANK_NAME . ' ' . $payments['affiliate_payment_bank_name'] . '<br>' . TEXT_AFFILIATE_PAYMENT_BANK_BRANCH_NUMBER . ' ' . $payments['affiliate_payment_bank_branch_number'] . '<br>' . TEXT_AFFILIATE_PAYMENT_BANK_SWIFT_CODE . ' ' . $payments['affiliate_payment_bank_swift_code'] . '<br>' . TEXT_AFFILIATE_PAYMENT_BANK_ACCOUNT_NAME . ' ' . $payments['affiliate_payment_bank_account_name'] . '<br>' . TEXT_AFFILIATE_PAYMENT_BANK_ACCOUNT_NUMBER . ' ' . $payments['affiliate_payment_bank_account_number'] . '<br>'; ?></td>
<?
  			}
  			if (AFFILIATE_USE_PAYPAL == 'true') {
?>
                <td class="main"  valign="top"><? echo '<b>' . TEXT_AFFILIATE_PAYMENT_PAYPAL . '</b><br><br>' . TEXT_AFFILIATE_PAYMENT_PAYPAL_EMAIL . '<br>' . $payments['affiliate_payment_paypal'] . '<br>'; ?></td>
<?
  			}
  			if (AFFILIATE_USE_CHECK == 'true') {
?>
				<td class="main"  valign="top"><? echo '<b>' . TEXT_AFFILIATE_PAYMENT_CHECK . '</b><br><br>' . TEXT_AFFILIATE_PAYMENT_CHECK_PAYEE . '<br>' . $payments['affiliate_payment_check'] . '<br>'; ?></td>
<?
  			}
?>
													</tr>
                								</table>
                							</td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              						</table>
              					</td>
              				</tr>
              				<tr>
								<? echo tep_draw_form('status', FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action')) . 'action=update_payment', 'post', 'onSubmit="return form_checking();"'); ?>
								<td colspan="2" width="100%">
									<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<? 	if (!$payments['affiliate_payment_status']) {?>
              							<tr>
                							<td class="main" width="25%"><b><?=TEXT_AFFILIATE_TOTAL_OWE_AMOUNT?></b></td>
                							<td class="main" width="75%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?=$currencies->format($total_held_amount)?></td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main" width="25%" valign="top"><b><?=TEXT_AFFILIATE_PAYMENT?></b><br><?=TEXT_AFFILIATE_BILLING_TIME?></td>
                							<td class="main" width="75%" valign="top">
                								(+)<?=$currencies->format($payments['affiliate_payment_total'])?>
                								<?=tep_draw_hidden_field('affiliate_payment_total', $payments['affiliate_payment_total'])?>
                							</td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main" width="25%" valign="top"><b><?=TEXT_AFFILIATE_TOTAL_RECLAIM?></b><br><?=TEXT_AFFILIATE_BILLING_TIME?></td>
                							<td class="main" width="75%" valign="top">
                								(-)&nbsp;<?=$currencies->format($payments["affiliate_payment_reclaimed_total"])?>
                							<?
                								if (tep_not_null($payments["affiliate_payment_reclaimed_sales"])) {
                									$reclaimed_orders_array = explode(',', $payments["affiliate_payment_reclaimed_sales"]);
                									echo "(For " . (count($reclaimed_orders_array)>1 ? 'orders' : 'order') . " with id: " . implode(', ', $reclaimed_orders_array) . ')';
                								}
                							?>
                							</td>
              							</tr>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main" width="25%"><b><?=TEXT_AFFILIATE_PAY_AMOUNT?></b></td>
                							<td class="main" width="75%">
                							<?
                								echo $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'] . '&nbsp;';
            									$display_amount = $payments['affiliate_payment_total'] + $total_held_amount - $payments['affiliate_payment_reclaimed_total'];
            									$display_amount = str_replace($currencies->currencies[DEFAULT_CURRENCY]['symbol_left'], "", $currencies->format($display_amount));
            									echo tep_draw_input_field('pay_amount', tep_output_string($display_amount), ' id="pay_amount" size="12" onKeyUp="update_withhold()"');
            									echo tep_draw_hidden_field('reclaim_amount', $payments['affiliate_payment_reclaimed_total']);
            								?>
                							</td>
                						</tr>
                						<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
              							<tr>
                							<td class="main" width="25%" valign="top"><b><?=TEXT_AFFILIATE_TOTAL_WITHHOLD?></b></td>
                							<td class="main" width="75%" valign="top">
                								<?=$currencies->currencies[DEFAULT_CURRENCY]['symbol_left']?>
                								<?=tep_draw_input_field('withhold_amount','', ' id="withhold_amount" size="12" READONLY')?>
                							</td>
              							</tr>
              					<?	} else { ?>
              							<tr>
                							<td class="main" width="25%"><b><?=TEXT_AFFILIATE_PAY_AMOUNT?></b></td>
                							<td class="main" width="75%"><?=$currencies->format($affiliate_account["affiliate_paid_amount"])?></td>
              							</tr>
              					<?	} ?>
              							<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
										<tr>
        									<td class="main"><b><?=TEXT_AFFILIATE_PAYMENT_STATUS?></b></td>
                							<td class="main" >
                							<?
                								if (!$payments['affiliate_payment_status']) {
                									echo tep_draw_pull_down_menu('status', $payments_statuses, $payments['affiliate_payment_status']);
                								} else {
                									echo $payments_status_array[$payments['affiliate_payment_status']];
                								}
                							?>
                							</td>
        								</tr>
        							<?	if (!$payments['affiliate_payment_status']) { ?>
        								<tr>
                							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
              							</tr>
        								<tr>
                							<td class="main"><b><?=TEXT_AFFILIATE_PAYMENT_NOTIFY?></b></td>
                							<td class="main"><?=tep_draw_checkbox_field('notify', '', true)?></td>
              							</tr>
              							<tr>
                							<td class="main">&nbsp;</td>
                							<td class="main"><?=tep_image_submit('button_update.gif', IMAGE_UPDATE)?></td>
                						</tr>
                					<?	} ?>
                					</table>
                				</td>
                				</form>
              				</tr>
            			</table>
        			</td>
      			</tr>
      			<tr>
        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      			</tr>
      			<tr>
        			<td class="main">
        				<table border="1" cellspacing="0" cellpadding="5">
          					<tr>
            					<td class="smallText" align="center"><b><?=TABLE_HEADING_NEW_VALUE?></b></td>
            					<td class="smallText" align="center"><b><?=TABLE_HEADING_OLD_VALUE?></b></td>
            					<td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
            					<td class="smallText" align="center"><b><?=TABLE_HEADING_AFFILIATE_NOTIFIED?></b></td>
          					</tr>
<?
    		$affiliate_history_query = tep_db_query("select affiliate_new_value, affiliate_old_value, affiliate_date_added, affiliate_notified from " . TABLE_AFFILIATE_PAYMENT_STATUS_HISTORY . " where affiliate_payment_id = '" . tep_db_input($pID) . "' order by affiliate_status_history_id desc");
    		if (tep_db_num_rows($affiliate_history_query)) {
      			while ($affiliate_history = tep_db_fetch_array($affiliate_history_query)) {
        			echo '  <tr>' . "\n" .
             			 '  	<td class="smallText">' . $payments_status_array[$affiliate_history['affiliate_new_value']] . '</td>' . "\n" .
             			 '      <td class="smallText">' . (tep_not_null($affiliate_history['affiliate_old_value']) ? $payments_status_array[$affiliate_history['affiliate_old_value']] : '&nbsp;') . '</td>' . "\n" .
             			 '      <td class="smallText" align="center">' . tep_date_short($affiliate_history['affiliate_date_added']) . '</td>' . "\n" .
             			 '      <td class="smallText" align="center">';
        			if ($affiliate_history['affiliate_notified'] == '1') {
          				echo tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK);
        			} else {
          				echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
        			}
					echo '  </tr>' . "\n";
      			}
    		} else {
        		echo '      <tr>' . "\n" .
             		 '      	<td class="smallText" colspan="4">' . TEXT_NO_PAYMENT_HISTORY . '</td>' . "\n" .
             		 '      </tr>' . "\n";
    		}
?>
        				</table>
        			</td>
      			</tr>
      			<tr>
        			<td colspan="2" align="right"><? echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_INVOICE, 'pID=' . $_REQUEST['pID']) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> <a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('action'))) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
      			</tr>
      			<script type="text/javascript" language="javascript">
					<!--
<? 				if (!$payments['affiliate_payment_status']) {
					$max_payment = $total_held_amount + $payments['affiliate_payment_total'] - $payments['affiliate_payment_reclaimed_total'];
?>
					var total_payment = <?=$max_payment?>;
					
					function form_checking () {
						if (!currencyValidation(document.getElementById('pay_amount').value)) {
							alert('The pay amount is not a valid currency value!');
							document.getElementById('pay_amount').focus();
							document.getElementById('pay_amount').select();
							return false;
						} else if (document.getElementById('pay_amount').value > total_payment) {
							alert('The pay amount is exceed maximum payout amount!');
							document.getElementById('pay_amount').focus();
							document.getElementById('pay_amount').select();
							return false;
						}
						
						return true;
					}
					
					function update_withhold() {
						if (document.getElementById('pay_amount').value == "" || !currencyValidation(document.getElementById('pay_amount').value)) {
							var pay_amount  = '0.00';
						} else {
							var pay_amount  = parseFloat(document.getElementById('pay_amount').value);
						}
						var remaining = parseFloat(total_payment) - pay_amount;
						
						document.getElementById('withhold_amount').value = currency_display(remaining, 4);
					}
					update_withhold();
<?				} ?>
					//-->
				</script>
<?	} else {
	if (tep_not_null($_REQUEST["affEmail"]))	$_SESSION['affiliate_email'] = $_REQUEST["affEmail"];
	if (tep_not_null($_REQUEST["status"]))		$_SESSION['payment_status'] = $_REQUEST["status"];
	
	$search = " 1 ";
    if ( isset($_SESSION['affiliate_email']) && (tep_not_null($_SESSION['affiliate_email'])) ) {
      	$keywords = tep_db_input(tep_db_prepare_input($_SESSION['affiliate_email']));
      	if ($keywords != "***") {
      		$search .= " and a.affiliate_email_address like '" . $keywords . "'";
      	}
    }
	
	if ( isset($_SESSION['payment_status']) && (tep_not_null($_SESSION['payment_status'])) ) {
      	$keywords = tep_db_input(tep_db_prepare_input($_SESSION['payment_status']));
      	if ($keywords != "***") {
      		$search .= " and s.affiliate_payment_status_id = '" . $keywords . "'";
      	}
    }
    
	$payments_query_raw = "select p.* , DATE_FORMAT(p.affiliate_payment_date, '%Y-%m-%d') as affiliate_payment_date, s.affiliate_payment_status_name, acc.affiliate_paid_amount from " . TABLE_AFFILIATE_PAYMENT . " as p inner join " . TABLE_AFFILIATE_PAYMENT_STATUS . " as s on p.affiliate_payment_status=s.affiliate_payment_status_id inner join " . TABLE_AFFILIATE . " as a on p.affiliate_id=a.affiliate_id left join " . TABLE_AFFILIATE_ACCOUNT . " as acc on p.affiliate_payment_id=acc.affiliate_payment_id where " . $search . " and s.affiliate_language_id = '" . $languages_id . "' order by p.affiliate_payment_id DESC";
	
	$affiliate_array = array ( 	array('id' => '', 'text' => TEXT_SELECT_AFFILIATE),
								array('id' => '***', 'text' => TEXT_ALL_AFFILIATES) );
	$affiliate_select_sql = "SELECT affiliate_email_address, affiliate_firstname, affiliate_lastname FROM " . TABLE_AFFILIATE . " ORDER BY affiliate_lastname";
    $affiliate_result_sql = tep_db_query($affiliate_select_sql);
    while($affiliate_row = tep_db_fetch_array($affiliate_result_sql)) {
      	$affiliate_array[] = array(	'id' => $affiliate_row['affiliate_email_address'],
                           			'text' => $affiliate_row['affiliate_lastname'] . ', ' . $affiliate_row['affiliate_firstname'] . ' (' . $affiliate_row['affiliate_email_address'] . ')');
    }
?>
      			<tr>
        			<td width="100%">
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
					            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
					            <td class="pageHeading"><?='<a href="javascript:void(confirm_billing(\'' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, 'pID=' . $pInfo->affiliate_payment_id. '&action=start_billing' ) . '\'))">' . tep_image_button('button_affiliate_billing.gif', IMAGE_AFFILIATE_BILLING) . '</a>'?></td>
					            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
					            <td align="right">
					            	<table border="0" width="100%" cellspacing="0" cellpadding="0">
              							<tr>
              								<?=tep_draw_form('affiliate_search_form', FILENAME_AFFILIATE_PAYMENT, '', 'post')?>
                							<td class="smallText" align="right">
                								<?=tep_draw_pull_down_menu('affEmail', $affiliate_array, $_SESSION['affiliate_email'], 'onChange="if (this.selectedIndex > 0) { this.form.submit(); }"') . tep_draw_hidden_field('action', 'edit')?>
                							</td>
              								</form>
              							</tr>
              							<tr>
              								<?=tep_draw_form('status', FILENAME_AFFILIATE_PAYMENT, '', 'post')?>
                							<td class="smallText" align="right"><?=HEADING_TITLE_STATUS . ' ' . tep_draw_pull_down_menu('status', array_merge(array(array('id' => '***', 'text' => TEXT_ALL_PAYMENTS)), $payments_statuses), $_SESSION['payment_status'], 'onChange="this.form.submit();"') . tep_draw_hidden_field('action', 'edit')?></td>
              								</form>
              							</tr>
            						</table>
            					</td>
          					</tr>
        				</table>
        			</td>
      			</tr>
      			<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td valign="top">
            						<table border="0" width="100%" cellspacing="0" cellpadding="2">
              							<tr class="dataTableHeadingRow">
                							<td class="dataTableHeadingContent"><?=TABLE_HEADING_AFILIATE_NAME?></td>
                							<td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_DATE_BILLED?></td>
							                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_NET_PAYMENT?></td>
							                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_PAYMENT?></td>
							                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_PAID_AMOUNT?></td>
							                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_STATUS?></td>
							                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
              							</tr>
<?
    	$payments_split = new splitPageResults($_REQUEST['page'], MAX_DISPLAY_SEARCH_RESULTS, $payments_query_raw, $payments_query_numrows);
    	$payments_query = tep_db_query($payments_query_raw);
    	while ($payments = tep_db_fetch_array($payments_query)) {
      		if (((!$_REQUEST['pID']) || ($_REQUEST['pID'] == $payments['affiliate_payment_id'])) && (!$pInfo)) {
        		$pInfo = new objectInfo($payments);
      		}
      		if ( (is_object($pInfo)) && ($payments['affiliate_payment_id'] == $pInfo->affiliate_payment_id) ) {
        		echo '              	<tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id . '&action=edit') . '\'">' . "\n";
      		} else {
        		echo '              	<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID')) . 'pID=' . $payments['affiliate_payment_id']) . '\'">' . "\n";
      		}
?>
                							<td class="dataTableContent"><? echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id . '&action=edit') . '">' . tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $payments['affiliate_firstname'] . ' ' . $payments['affiliate_lastname']; ?></td>
                							<td class="dataTableContent" align="center"><?=$payments['affiliate_payment_date']?></td>
							                <td class="dataTableContent" align="right"><?=$currencies->format(strip_tags($payments['affiliate_payment']))?></td>
							                <td class="dataTableContent" align="right"><?=$currencies->format(strip_tags($payments['affiliate_payment'] + $payments['affiliate_payment_tax']))?></td>
							                <td class="dataTableContent" align="right"><?=$currencies->format($payments['affiliate_paid_amount'])?></td>
							                <td class="dataTableContent" align="right"><?=$payments['affiliate_payment_status_name']?></td>
							                <td class="dataTableContent" align="right"><? if ( (is_object($pInfo)) && ( $payments['affiliate_payment_id'] == $pInfo->affiliate_payment_id) ) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID')) . 'pID=' . $payments['affiliate_payment_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              							</tr>
<?		} ?>
              							<tr>
                							<td colspan="7">
                								<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  									<tr>
                    									<td class="smallText" valign="top"><?=$payments_split->display_count($payments_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_PAYMENTS)?></td>
                    									<td class="smallText" align="right"><?=$payments_split->display_links($payments_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'pID', 'action', 'affName', 'status')) . '&affName='.$_REQUEST["affName"].'&status='.$_REQUEST["status"])?></td>
                  									</tr>
<?	if ( (isset($_SESSION['affiliate_email']) && tep_not_null($_SESSION['affiliate_email'])) || (isset($_SESSION['payment_status']) && tep_not_null($_SESSION['payment_status'])) ) { ?>
                  										<tr>
                    										<td align="right" colspan="2"><?='<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, "resetSearch=1") . '">' . tep_image_button('button_reset.gif', IMAGE_RESET) . '</a>'?></td>
                  										</tr>
<?	} ?>
                								</table>
                							</td>
              							</tr>
            						</table>
            					</td>
            					<script type="text/javascript" language="javascript">
            						<!--
            						function confirm_billing(loc) {
            							answer = confirm('Are you sure to begin billing process?')
										if (answer != 0) { 
											location = loc; 
										}
            						}
            						//-->
            					</script>
<?
  		$heading = array();
  		$contents = array();
  		switch ($_REQUEST['action']) {
    		case 'delete':
      			$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_PAYMENT . '</b>');
			    
			    $contents = array('form' => tep_draw_form('payment', FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id. '&action=deleteconfirm'));
			    $contents[] = array('text' => TEXT_INFO_DELETE_INTRO . '<br>');
			    $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
			    
      			break;
    		default:
      			if (is_object($pInfo)) {
        			$heading[] = array('text' => '<b>[' . $pInfo->affiliate_payment_id . ']&nbsp;&nbsp;' . tep_datetime_short($pInfo->affiliate_payment_date) . '</b>');
					/*
					$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id  . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
        			$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_AFFILIATE_INVOICE, 'pID=' . $pInfo->affiliate_payment_id ) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> ');
        			*/
        			$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, tep_get_all_get_params(array('pID', 'action')) . 'pID=' . $pInfo->affiliate_payment_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_AFFILIATE_INVOICE, 'pID=' . $pInfo->affiliate_payment_id ) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> ');
      			}
      			break;
  		}
  		
  		if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    		echo '            	<td  width="25%" valign="top">' . "\n";
    		
    		$box = new box;
    		echo $box->infoBox($heading, $contents);
    		echo '            	</td>' . "\n";
  		}
?>
							</tr>
        				</table>
        			</td>
      			</tr>
<?
	}
?>
    		</table>
    	</td>
<!-- body_text_eof //-->
  	</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?	require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?	require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>