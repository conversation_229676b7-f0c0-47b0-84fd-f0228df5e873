<?
/*
  	$Id: affiliate_sales.php,v 1.7 2008/01/02 09:33:47 leechuan.goh Exp $
	
  	OSC-Affiliate
  	Contribution based on:
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
        case 'reset_session':
        	unset($_SESSION['aff_sales_param']);
        	tep_redirect(tep_href_link(FILENAME_AFFILIATE_SALES));
        	break;
	}
}

$billing_status_array = array ( "0" => "Not Billed", "1" => "Billed");

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
	    	</table>
	    </td>
<!-- body_text //-->
    	<td width="100%" valign="top">
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($_REQUEST['action'] == 'show_report') {
	if (!$_REQUEST['cont']) {
		unset($_SESSION['ori_aff_sales_select_sql']);
		
		$_SESSION['aff_sales_param']["affiliate_email_address"] = $_REQUEST["affiliate_email_address"];
		$_SESSION['aff_sales_param']["start_date"] = $_REQUEST["start_date"];
		$_SESSION['aff_sales_param']["end_date"] = $_REQUEST["end_date"];
		$_SESSION['aff_sales_param']["order_status"] = $_REQUEST["order_status"];
		$_SESSION['aff_sales_param']["billing_status"] = $_REQUEST["billing_status"];
		$_SESSION['aff_sales_param']["show_reclaim"] = $_REQUEST["show_reclaim"];
		$_SESSION['aff_sales_param']["sort_by"] = $_REQUEST["sort_by"];
  		$_SESSION['aff_sales_param']["sort_order"] = $_REQUEST["sort_order"];
  		
		if (tep_not_null($_REQUEST["affiliate_email_address"])) {
			$affiliate_str = " a.affiliate_email_address LIKE '" . $_REQUEST["affiliate_email_address"] . "' ";
		} else {
			$affiliate_str = "1";
		}
		
		if ($_REQUEST["start_date"]) {
  			list($yr, $mth, $day) = explode("-", $_REQUEST["start_date"]);
  			$start_date_str = " DATE_FORMAT(asale.affiliate_date, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d",mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') ";
  		} else {
  			$start_date_str = " 1 ";
  		}
  		
  		if ($_REQUEST["end_date"]) {
  			list($yr, $mth, $day) = explode("-", $_REQUEST["end_date"]);
  			$end_date_str = " DATE_FORMAT(asale.affiliate_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d",mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') ";
  		} else {
  			$end_date_str = " 1 ";
  		}
		
		$order_status_array = array();
  		$order_status_str = "1";
  		if ($_REQUEST["order_status"] && is_array($_REQUEST["order_status"])) {
  			foreach ($_REQUEST["order_status"] as $status_val) {
  				$order_status_array[] = $status_val;
  			}
  			$order_status_str = (count($order_status_array)) ? " os.orders_status_id IN (".implode(',', $order_status_array).")" : " 1 ";
  		}
  		
  		$sel_billing_status_array = array();
  		$billing_status_str = "1";
  		if ($_REQUEST["billing_status"] && is_array($_REQUEST["billing_status"])) {
  			foreach ($_REQUEST["billing_status"] as $status_val) {
  				$sel_billing_status_array[] = "asale.affiliate_billing_status=$status_val";
  			}
  			$billing_status_str = (count($sel_billing_status_array)) ? "(".implode(" OR ", $sel_billing_status_array).")" : " 1 ";
  		}
  		
  		if ($_REQUEST["show_reclaim"] == "1") {	// reclaimed
  			$reclaim_str = " asale.affiliate_commission_reclaim = 1";
  		} else if ($_REQUEST["show_reclaim"] == "2") {	// not yet reclaimed
  			$reclaim_str = " asale.affiliate_commission_reclaim = 0";
  		} else {
  			$reclaim_str = "1";
  		}
  		
  		if ($_REQUEST["sort_by"] == "a.affiliate_email_address")
  			$sort_str = " order by a.affiliate_email_address " . $_REQUEST["sort_order"];
  		else
  			$sort_str = " order by asale.affiliate_date " . $_REQUEST["sort_order"];
 		
  		$affiliate_sales_select_sql = "	select asale.*, DATE_FORMAT(asale.affiliate_date, '%Y-%m-%d') as affiliate_date, os.orders_status_name as orders_status, a.affiliate_firstname, a.affiliate_lastname from " . TABLE_AFFILIATE_SALES . " asale 
			      						inner join " . TABLE_ORDERS . " o on (asale.affiliate_orders_id = o.orders_id) 
			      						inner join " . TABLE_ORDERS_STATUS . " os on (o.orders_status = os.orders_status_id and language_id = " . $languages_id . ") 
			      						inner join " . TABLE_AFFILIATE . " a  on (a.affiliate_id = asale.affiliate_id) 
			      						where $order_status_str 
			      							and $affiliate_str 
			      							and $start_date_str 
			      							and $end_date_str 
			      							and $billing_status_str 
			      							and $reclaim_str 
			      						$sort_str 
			      						";
		
  		$_SESSION['ori_aff_sales_select_sql'] = $affiliate_sales_select_sql;
  	} else {
  		$affiliate_sales_select_sql = $_SESSION['ori_aff_sales_select_sql'];
  	}
  	
  	$show_records = $_REQUEST["show_records"];
?>
      			<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          					</tr>
        				</table>
        			</td>
      			</tr>
      			<tr>
        			<td valign="top">
        				<table border="0" width="100%" cellspacing="1" cellpadding="2">
          					<tr>
            					<td class="reportBoxHeading"><?=TABLE_HEADING_AFFILIATE?></td>
					            <td class="reportBoxHeading"><?=TABLE_HEADING_DATE?></td>
					            <td class="reportBoxHeading"><?=TABLE_HEADING_ORDER_ID?></td>
					            <td class="reportBoxHeading"><?=TABLE_HEADING_ORDER_STATUS?></td>
					            <td class="reportBoxHeading"><?=TABLE_HEADING_BILLING_STATUS?></td>
					            <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_VALUE?></td>
					            <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_PERCENTAGE?></td>
					            <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_SALES?></td>
          					</tr>
<?
	if ($show_records != "ALL") {
		$aff_sales_split_object = new splitPageResults($_REQUEST["page"], ($show_records=='' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $affiliate_sales_select_sql, $affiliate_sales_sql_numrows);
	}
	
	$affiliate_sales_result_sql = tep_db_query($affiliate_sales_select_sql);
	
	if (tep_db_num_rows($affiliate_sales_result_sql) > 0) {
	    $row_count = 0;
	    while ($affiliate_sales = tep_db_fetch_array($affiliate_sales_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			$order_link = '<a href="' . tep_href_link(FILENAME_ORDERS, "action=edit&oID=".$affiliate_sales['affiliate_orders_id']) . '" target="_blank" class="blacklink" style="font-weight:bold;">'.$affiliate_sales['affiliate_orders_id'].'</a>';
?>
							<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
					            <td class="dataTableContent"><?=$affiliate_sales['affiliate_firstname'] . " ". $affiliate_sales['affiliate_lastname']?></td>
					            <td class="dataTableContent"><?=$affiliate_sales['affiliate_date']?></td>
					            <td class="dataTableContent"><?=$order_link?></td>
					            <td class="dataTableContent"><? if ($affiliate_sales['orders_status']) echo $affiliate_sales['orders_status']; else echo TEXT_DELETED_ORDER_BY_ADMIN; ?></td>
					            <td class="dataTableContent"><?=$billing_status_array[$affiliate_sales["affiliate_billing_status"]]?></td>
					            <td class="dataTableContent" align="right"><?=$currencies->display_price($affiliate_sales['affiliate_value'], '')?></td>
					            <td class="dataTableContent" align="right"><?=$affiliate_sales['affiliate_percent'] . "%"?></td>
					            <td class="dataTableContent" align="right"><?=$currencies->display_price($affiliate_sales['affiliate_payment'], '')?></td>
							</tr>
<?
			$row_count++;
		}
	} else {
?>
          					<tr>
            					<td colspan="8" class="smallText"><?=TEXT_NO_SALES?></td>
          					</tr>
<?	} ?>
          					<tr>
            					<td colspan="8">
            						<table border="0" width="100%" cellspacing="0" cellpadding="2">
              							<tr>
                							<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_PRODUCTS, tep_db_num_rows($affiliate_sales_result_sql) > 0 ? "1" : "0", tep_db_num_rows($affiliate_sales_result_sql), tep_db_num_rows($affiliate_sales_result_sql)) : $aff_sales_split_object->display_count($affiliate_sales_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_SALES)?></td>
                							<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $aff_sales_split_object->display_links($affiliate_sales_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y')))?></td>
              							</tr>
<?	if (isset($_SESSION['affiliate_search']) && tep_not_null($_SESSION['affiliate_search'])) { ?>
                  						<tr>
                    						<td align="right" colspan="2"><?='<a href="' . tep_href_link(FILENAME_AFFILIATE_SALES, "resetSearch=1") . '">' . tep_image_button('button_reset.gif', IMAGE_RESET) . '</a>'?></td>
                  						</tr>
<?	} ?>
            						</table>
            					</td>
          					</tr>
<?
} else {
	$affiliate_array = array ( 	array('id' => '', 'text' => TEXT_SELECT_AFFILIATE) );
	$affiliate_select_sql = "SELECT affiliate_email_address, affiliate_firstname, affiliate_lastname FROM " . TABLE_AFFILIATE . " ORDER BY affiliate_lastname";
    $affiliate_result_sql = tep_db_query($affiliate_select_sql);
    while($affiliate_row = tep_db_fetch_array($affiliate_result_sql)) {
      	$affiliate_array[] = array(	'id' => $affiliate_row['affiliate_email_address'],
                           			'text' => $affiliate_row['affiliate_lastname'] . ', ' . $affiliate_row['affiliate_firstname'] . ' (' . $affiliate_row['affiliate_email_address'] . ')');
    }
    
    $status_options = array();
	$order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . $languages_id  . "' ORDER BY orders_status_sort_order";
	$order_status_result_sql = tep_db_query($order_status_select_sql);
	while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
		$status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
	}
	
	$reclaim_options = array (  array("id"=>0, "text"=>"Both"),
								array("id"=>1, "text"=>"Yes"),
								array("id"=>2, "text"=>"No")
							);
	$show_options = array 	(	array ('id' => MAX_DISPLAY_SEARCH_RESULTS, "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
	$sort_by_array = array	( 	array ('id' => 'asale.affiliate_date', "text" => "Transaction Date"),
								array ('id' => 'a.affiliate_email_address', "text" => "Affiliate")
  							);
?>
							<tr>
								<td>
									<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
											<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
										</tr>
									</table>
								</td>
							</tr>
							<?=tep_draw_form('affiliate_sales_report_criteria', FILENAME_AFFILIATE_SALES, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', 'onSubmit="return form_checking();"');?>
							<tr>
		        				<td>
		        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
			        					<tr>
											<td class="main" WIDTH="15%"><?=ENTRY_AFFILIATE?></td>
							    			<td class="main"><?=tep_draw_pull_down_menu("affiliate_email_address", $affiliate_array, $_SESSION['aff_sales_param']["affiliate_email_address"])?></td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
			        					<script language="javascript"><!--
		  									var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "affiliate_sales_report_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
										//--></script>
										<tr>
		            						<td class="main" >Start Date<br><small>(YYYY-MM-DD)</small></td>
		            						<td class="main" align="laft"><script language="javascript">date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION['aff_sales_param']["start_date"]?>';</script></td>
		          						</tr>
		          						<tr>
		            						<td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
		          						</tr>
				        				<script language="javascript"><!--
		  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "affiliate_sales_report_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
										//--></script>
										<tr>
		            						<td class="main" >End Date<br><small>(YYYY-MM-DD)</small></td>
		            						<td class="main" align="laft"><script language="javascript">date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION['aff_sales_param']["end_date"]?>';</script></td>
		          						</tr>
		          						<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_ORDER_STATUS?></td>
							    			<td class="main">
							    			<?
							    				foreach ($status_options as $id => $title) {
							    					echo tep_draw_checkbox_field('order_status[]', "$id", isset($_SESSION['aff_sales_param']) ? (is_array($_SESSION['aff_sales_param']["order_status"]) && in_array($id, $_SESSION['aff_sales_param']["order_status"]) ? true : false) : ( $id=="2" || $id=="3" ? true : false)) . "&nbsp;" . $title . "&nbsp;";
							    				}
							    			?>
							    			</td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_BILLING_STATUS?></td>
							    			<td class="main">
							    			<?
							    				foreach ($billing_status_array as $id => $title) {
							    					echo tep_draw_checkbox_field('billing_status[]', "$id", isset($_SESSION['aff_sales_param']) ? (is_array($_SESSION['aff_sales_param']["billing_status"]) && in_array($id, $_SESSION['aff_sales_param']["billing_status"]) ? true : false) : true) . "&nbsp;" . $title . "&nbsp;";
							    				}
							    			?>
							    			</td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_PAYMENT_RECLAIM?></td>
							    			<td class="main"><?=tep_draw_pull_down_menu("show_reclaim", $reclaim_options, $_SESSION['aff_sales_param']["show_reclaim"])?></td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_SORT?></td>
							    			<td class="main">
							    				<?=tep_draw_pull_down_menu("sort_by", $sort_by_array, $_SESSION['aff_sales_param']["sort_by"], '')?>
							    				<?=tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['aff_sales_param']) ? ($_SESSION['aff_sales_param']["sort_order"]=='ASC' ? "checked" : '') : '') . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['aff_sales_param']) ? ($_SESSION['aff_sales_param']["sort_order"]=='DESC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_DESC?>
							    			</td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
							    			<td class="main">
							    				<?=tep_draw_pull_down_menu("show_records", $show_options, '', '')?>
							    			</td>
										</tr>
										<tr>
		            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
	          						</table>
	          					</td>
	          				</tr>
	          				<tr>
			  					<td>
			  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
			  							<tr>
			  								<td width="20%">&nbsp;</td>
			  								<td align="right">
			  									<?=tep_image_submit('button_report.gif', IMAGE_REPORT)?>
			  									&nbsp;&nbsp;
			  									<a href="<?=tep_href_link(FILENAME_AFFILIATE_SALES, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
			  								</td>
			  							</tr>
			  						</table>
			  					</td>
			  				</tr>
			  				</form>
			  				<script language="javascript">
			  				<!--
								function form_checking() {
					  				var start_date = document.getElementById('start_date').value;
									if(start_date.length > 0){
					     				if (!validateDate(start_date)) {
					     					alert('Start date is not a valid date format as requested!');
											document.getElementById('start_date').focus();
											document.getElementById('start_date').select();
											return false;
					     				}
					   				}
					   				
					   				var end_date = document.getElementById('end_date').value;
									if(end_date.length > 0){
					     				if (!validateDate(end_date)) {
					     					alert('End date is not a valid date format as requested!');
											document.getElementById('end_date').focus();
											document.getElementById('end_date').select();
											return false;
					     				}
					   				}
					   				
					   				if (start_date.length > 0 && end_date.length > 0) {
					   					if (!validStartAndEndDate(start_date, end_date)) {
					   						alert('Start Date is greater than End Date!');
											document.getElementById('start_date').focus();
											document.getElementById('start_date').select();
											return false;
					   					}
					   				}
					   				
					   				var orderStatusSelect = document.affiliate_sales_report_criteria.elements['order_status[]'];
					   				var emptyOrderStatus = true;
									for (i=0;i<orderStatusSelect.length;i++) {
										if (orderStatusSelect[i].checked) {
											emptyOrderStatus = false;
											break;
										}
									}
									
									if (emptyOrderStatus) {
										alert('Please select at least one order status!');
										return false;
									}
									
					   				var billingStatusSelect = document.affiliate_sales_report_criteria.elements['billing_status[]'];
					   				var emptyBillingStatus = true;
									for (i=0;i<billingStatusSelect.length;i++) {
										if (billingStatusSelect[i].checked) {
											emptyBillingStatus = false;
											break;
										}
									}
									
									if (emptyBillingStatus) {
										alert('Please select at least one billing status!');
										return false;
									}
									
					   				return true;
					   			}
					   		//-->
							</script>
<?
}
?>
        				</table>
        			</td>
      			</tr>
    		</table>
    	</td>
<!-- body_text_eof //-->
  	</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php');?>