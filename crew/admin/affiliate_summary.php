<?
/*
  	$Id: affiliate_summary.php,v 1.5 2005/12/28 02:54:50 weichen Exp $
	
  	OSC-Affiliate
	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

// delete clickthroughs
if (AFFILIATE_DELETE_CLICKTHROUGHS != 'false' && is_numeric(AFFILIATE_DELETE_CLICKTHROUGHS)) {
	$time = mktime (1,1,1,date("m"),date("d") - AFFILIATE_DELETE_CLICKTHROUGHS, date("Y"));
    $time = date("Y-m-d", $time);
    tep_db_query("delete from " . TABLE_AFFILIATE_CLICKTHROUGHS . " where affiliate_clientdate < '". $time . "'");
}
// delete old records from affiliate_banner_history
if (AFFILIATE_DELETE_AFFILIATE_BANNER_HISTORY != 'false' && is_numeric(AFFILIATE_DELETE_AFFILIATE_BANNER_HISTORY)) {
	$time = mktime (1,1,1,date("m"),date("d") - AFFILIATE_DELETE_AFFILIATE_BANNER_HISTORY, date("Y"));
    $time = date("Y-m-d", $time);
    tep_db_query("delete from " . TABLE_AFFILIATE_BANNERS_HISTORY . " where affiliate_banners_history_date < '". $time . "'");
}

$affiliate_banner_history_raw = "select sum(affiliate_banners_shown) as count from " . TABLE_AFFILIATE_BANNERS_HISTORY . "";
$affiliate_banner_history_query = tep_db_query($affiliate_banner_history_raw);
$affiliate_banner_history = tep_db_fetch_array($affiliate_banner_history_query);
$affiliate_impressions = $affiliate_banner_history['count'];
if ($affiliate_impressions == 0) $affiliate_impressions = "n/a";

$affiliate_clickthroughs_raw = "select count(*) as count from " . TABLE_AFFILIATE_CLICKTHROUGHS . "";
$affiliate_clickthroughs_query = tep_db_query($affiliate_clickthroughs_raw);
$affiliate_clickthroughs = tep_db_fetch_array($affiliate_clickthroughs_query);
$affiliate_clickthroughs = $affiliate_clickthroughs['count'];

$affiliate_sales_raw = "select count(*) as count, sum(affiliate_value) as total, sum(affiliate_payment) as payment from " . TABLE_AFFILIATE_SALES . " a 
            			left join " . TABLE_ORDERS . " o on (a.affiliate_orders_id = o.orders_id) 
            			where 1 AND IF(LOCATE(o.orders_status,'".AFFILIATE_COMMISSSION_ORDER_STATUS."'),1,0) ";
$affiliate_sales_query= tep_db_query($affiliate_sales_raw);
$affiliate_sales= tep_db_fetch_array($affiliate_sales_query);

$affiliate_transactions = $affiliate_sales['count'];
if ($affiliate_clickthroughs > 0) {
	$affiliate_conversions = tep_round(($affiliate_transactions / $affiliate_clickthroughs)*100,2) . "%";
} else {
    $affiliate_conversions = "n/a";
}

$affiliate_amount = $affiliate_sales['total'];
if ($affiliate_transactions > 0) {
	$affiliate_average = tep_round($affiliate_amount / $affiliate_transactions, 2);
} else {
	$affiliate_average = "n/a";
}

$affiliate_commission = $affiliate_sales['payment'];

$affiliates_raw = "select count(*) as count from " . TABLE_AFFILIATE . "";
$affiliates_raw_query = tep_db_query($affiliates_raw);
$affiliates_raw = tep_db_fetch_array($affiliates_raw_query);
$affiliate_number = $affiliates_raw['count'];
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript"><!--
function popupWindow(url) {
  	window.open(url,'popupWindow','toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=yes,copyhistory=no,width=450,height=140,screenX=150,screenY=150,top=150,left=150');
}
//--></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent"><?=TEXT_SUMMARY_TITLE?></td>
              								</tr>
            							</table>
            						</td>
          						</tr>
          						<tr>
            						<td>
            							<table width="100%" border="0" cellpadding="4" cellspacing="2" align="center" class="dataTableContent">
            								<tr>
              									<td width="35%" align="right" class="dataTableContent"><?=TEXT_AFFILIATES?>&nbsp;&nbsp;&nbsp;&nbsp;</td>
              									<td width="15%" class="dataTableContent"><?=$affiliate_number?></td>
              									<td width="35%" align="right" class="dataTableContent"></td>
              									<td width="15%" class="dataTableContent"></td>
            								</tr>
            								<tr>
              									<td width="35%" align="right" class="dataTableContent"><?=TEXT_IMPRESSIONS?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_1, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                  	<td width="15%" class="dataTableContent"><?=$affiliate_impressions?></td>
							                  	<td width="35%" align="right" class="dataTableContent"><?=TEXT_VISITS?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_2, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                  	<td width="15%" class="dataTableContent"><?=$affiliate_clickthroughs?></td>
            								</tr>
            								<tr>
              									<td width="35%" align="right" class="dataTableContent"><?=TEXT_TRANSACTIONS?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_3, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                  	<td width="15%" class="dataTableContent"><?=$affiliate_transactions?></td>
							                  	<td width="35%" align="right" class="dataTableContent"><?=TEXT_CONVERSION?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_4, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                  	<td width="15%" class="dataTableContent"><?=$affiliate_conversions?></td>
            								</tr>
            								<tr>
							                  	<td width="35%" align="right" class="dataTableContent"><?=TEXT_AMOUNT?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_5, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                 	<td width="15%" class="dataTableContent"><?=$currencies->display_price($affiliate_amount, '')?></td>
							                  	<td width="35%" align="right" class="dataTableContent"><?=TEXT_AVERAGE?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_6, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
							                  	<td width="15%" class="dataTableContent"><?=$currencies->display_price($affiliate_average, '')?></td>
            								</tr>
            								<tr>
								            	<td width="35%" align="right" class="dataTableContent"><?=TEXT_COMMISSION_RATE?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_7, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></td>
								                <td width="15%" class="dataTableContent"><?=tep_round(AFFILIATE_PERCENT, 2) . ' %'?></td>
								                <td width="35%" align="right" class="dataTableContent"><b><?=TEXT_COMMISSION?><?='<a href="javascript:popupWindow(\'' . tep_catalog_href_link(FILENAME_AFFILIATE_HELP_8, '', 'SSL') . '\')">' . TEXT_SUMMARY_HELP . '</a>'?></b></td>
								                <td width="15%" class="dataTableContent"><b><?=$currencies->display_price($affiliate_commission, '')?></b></td>
            								</tr>
            								<tr>
              									<td colspan="4"><?=tep_draw_separator()?></td>
            								</tr>
            								<tr>
              									<td align="center" class="dataTableContent" colspan="4"><b><?=TEXT_SUMMARY?></b></td>
            								</tr>
            								<tr>
              									<td colspan="4"><?=tep_draw_separator()?></td>
            								</tr>
            								<tr>
              									<td align="right" class="dataTableContent" colspan="4"><?='<a href="' . tep_href_link(FILENAME_AFFILIATE_BANNERS, '') . '">' . tep_image_button('button_affiliate_banners.gif', IMAGE_BANNERS) . '</a> <a href="' . tep_href_link(FILENAME_AFFILIATE_CLICKS, '') . '">' . tep_image_button('button_affiliate_clickthroughs.gif', IMAGE_CLICKTHROUGHS) . '</a> <a href="' . tep_href_link(FILENAME_AFFILIATE_SALES, '') . '">' . tep_image_button('button_affiliate_sales.gif', IMAGE_SALES) . '</a>'?></td>
            								</tr>
            							</table>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php');?>