<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'aft_account_limit.php');
require_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
require_once(DIR_WS_FUNCTIONS . 'html_output.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_AFT_ACCOUNT_LIMIT);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : 'default_list');
$countires_id = (isset($_REQUEST['countries_id']) ? $_REQUEST['countries_id'] : 0);


$filename = FILENAME_AFT_ACCOUNT_LIMIT;

// Function - Actions

switch ($action) {
    case "save_account_limit":
        $aft_account_limit_obj = new aft_account_limit($login_id, $login_email_address);
        $aft_account_limit_obj->save_account_limit($_POST, $messageStack);
        tep_redirect(tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))));
        break;
    case "delete_account_limit":
        $aft_account_limit_obj = new aft_account_limit($login_id, $login_email_address);
        $aft_account_limit_obj->delete_account_limit($_REQUEST, $messageStack);
        tep_redirect(tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))));
        break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/jquery.form.js"></script>
        <script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/out_of_stock_rule.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <?php if ($action == 'default_list') { ?>


                            <tr>
                                <td>
                                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                        <tbody><tr>
                                                <td valign="top" class="pageHeading"><?php echo HEADING_TITLE; ?></td>
                                                <td align="right" class="pageHeading"><img width="1" height="40" border="0" alt="" src="images/pixel_trans.gif"></td>
                                            </tr>
                                        </tbody></table>
                                </td>
                            </tr>

                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td class="pageHeading" valign="top"><?= $header_title ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                </tr>
                                <tr>
                                    <td width="100%" valign="top"> 

                                        <table width="100%" border="0" cellspacing="2" cellpadding="2">

                                            <tr align="right">
                                                <td> [ <a href="<?php echo tep_href_link($filename, 'action=new_account_limit') ?>"><?php echo LINK_ADD_ACCOUNT_LIMIT; ?></a> ]</td>
                                            </tr>

                                            <tr>
                                                <td> 


                                                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                                                        <tr>
                                                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_AFT_COUNTRY; ?></td>
                                                            <td width="5%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_AFT_LIMIT; ?></td>
                                                            <td width="5%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_AFT_DURATION; ?></td>
                                                            <td width="10%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_AFT_ACTION; ?></td>
                                                        </tr>

                                                        <?php
                                                        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
                                                        $aft_acct_select_sql = "SELECT aacl.aft_limit, aacl.aft_duration, c.countries_name, aacl.countries_id
                                                                FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " AS aacl 
                                                                INNER JOIN  " . TABLE_COUNTRIES . " AS c ON aacl.countries_id = c.countries_id
                                                                ORDER BY c.countries_name ASC";

                                                        $show_records = MAX_DISPLAY_SEARCH_RESULTS;
                                                        if ($show_records != "ALL") {
                                                            $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $aft_acct_select_sql, $aft_acct_select_sql_numrows, true);
                                                        }
                                                        $aft_acct_result_sql = tep_db_query($aft_acct_select_sql);
                                                        $row_count = 0;
                                                        while ($rule_row = tep_db_fetch_array($aft_acct_result_sql)) {
                                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                            $action_str = ' <a href="' . tep_href_link($filename, 'action=edit_account_limit&countries_id=' . $rule_row["countries_id"]) . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') . '</a>
                                                                                <a href="javascript:void(confirm_delete(\'AFT Account Limit - ' . $rule_row['countries_name'] . '\', \'\', \'' . tep_href_link($filename, 'action=delete_account_limit&countries_id=' . $rule_row["countries_id"]) . '\'))">' . tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') . '</a>';
                                                            echo '	<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                                                                <td  align="center" valign="top" class="reportRecords">' . $rule_row['countries_name'] . '</td>
                                                                                <td align="center" valign="top" class="reportRecords">' . $rule_row['aft_limit'] . '</td>
                                                                                <td align="center" valign="top" class="reportRecords">' . $rule_row['aft_duration'] . '</td>
                                                                                <td align="center" valign="top" class="reportRecords">' . $action_str . '</td>
                                                                            </tr>';
                                                            ?>

                                                            <?php
                                                            $row_count++;
                                                        }
                                                        ?>

                                                    </table>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                        <tr>
                            <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($aft_acct_result_sql) > 0 ? "1" : "0", tep_db_num_rows($aft_acct_result_sql), tep_db_num_rows($aft_acct_result_sql)) : $rule_split_object->display_count($aft_acct_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                            <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($aft_acct_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
                                
         </table>


<?php } ?>

                        <?php
                        if ($action == 'new_account_limit' || $action == 'edit_account_limit') {

                            $input_countries_id = isset($_POST['countries_id']) ? tep_db_prepare_input($_POST['countries_id']) : 0;
                            $input_aft_limit = isset($_POST['aft_limit']) ? tep_db_prepare_input($_POST['aft_limit']) : '';
                            $input_aft_duration = isset($_POST['aft_duration']) ? tep_db_prepare_input($_POST['aft_duration']) : '';


                            if ($countires_id) {
                                $id_select_sql = "	SELECT countries_id, aft_limit, aft_duration
                                                                                            FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " 
                                                                                            WHERE countries_id = '" . $countires_id . "'";
                                $id_result_sql = tep_db_query($id_select_sql);

                                if ($row = tep_db_fetch_array($id_result_sql)) {
                                    $input_aft_limit = $row['aft_limit'];
                                    $input_aft_duration = $row['aft_duration'];
                                    $input_countries_id = $row['countries_id'];
                                }
                            }
                            ?>

                            <?php
                            echo tep_draw_form('aft_account_limit_form', FILENAME_AFT_ACCOUNT_LIMIT, 'action=save_account_limit', 'post', ' onSubmit="return check_form();" id="aft_account_limit_form"');
                            $button = BUTTON_INSERT;
                            $heading = HEADING_ADD_ACCOUNT_LIMIT;
                            $country_name = '';
                            
                            if ($countires_id) {
                                $button = BUTTON_UPDATE;
                                $heading = HEADING_EDIT_ACCOUNT_LIMIT;
                                $country_select_sql = "	SELECT countries_name
                                                                                            FROM " . TABLE_COUNTRIES . " 
                                                                                            WHERE countries_id = '" . $countires_id . "'";
                                $country_result_sql = tep_db_query($country_select_sql);

                                if ($country_row = tep_db_fetch_array($country_result_sql)) {
                                    $country_name = $country_row['countries_name'];
                                }

                                echo tep_draw_hidden_field('hidden_countries_id', $countires_id);
                            }
                            ?>

                            <table cellspacing="2" cellpadding="2" border="0" width="100%">

                                <tr>
                                    <td>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td class="pageHeading" valign="top"><?php echo $heading; ?></td>
                                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>


                                <tr>
                                    <td>
                                        <table border="0" cellpadding="2" cellspacing="2">

                                            <tr>
                                                <td class="main" valign="top"><?php echo ENTRY_COUNTRY; ?></td>
                                                <td class="main">
                                                    <?php
                                                    if ($countires_id) {
                                                        echo '<b>' . $country_name . '</b>';
                                                    } else {
                                                        echo tep_get_country_list('countries_id', $input_countries_id, ' id ="countries_id" ');
                                                        echo ' <span class="fieldRequired">* Required</span>';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>

                                            <tr><td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td></tr>
                                            
                                            <tr>
                                                <td class="main" width="25%" valign=top><?php echo ENTRY_AFT_LIMIT; ?></td>
                                                <td class="main">
                                                    <?php echo tep_draw_input_field('aft_limit', $input_aft_limit, ' id ="aft_limit" size="10" maxlength="10"') ?>
                                                    <span class="fieldRequired">* Required</span>
                                                <td>
                                            </tr>
                                            <tr><td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td></tr>

                                            <tr>
                                                <td class="main" width="25%" valign=top><?php echo ENTRY_AFT_DURATION; ?></td>
                                                <td class="main">
                                                    <?php echo tep_draw_input_field('aft_duration', $input_aft_duration, ' id ="aft_duration" size="10" maxlength="10"') ?>
                                                    <span class="fieldRequired">* Required</span>
                                                <td>
                                            </tr>
                                            <tr><td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td></tr>
                                            <tr>
                                                <td colspan="2">
                                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td align="middle">
                                                                <?php echo tep_submit_button($button, '', 'onClick="return check_form();"', 'inputButton') ?>
                                                                <?php echo tep_button(BUTTON_CANCEL, '', tep_href_link($filename, ''), '', 'inputButton') ?>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td></tr>

                            </table>


                        <?php } ?>

                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</body>
</html>

<script language="javascript">

    function check_form() {

        var aft_limit_val = $('#aft_limit').val();
        var aft_duration_val = $('#aft_duration').val();

        if ($('#countries_id').val() == '') {
            alert('Please Select Country ! ');
            return false;
        }

        else if (aft_limit_val == '') {
            alert('AFT Account Created Limit Cannot Be Blank ! ');
            return false;
        }

        else if (!aft_limit_val.match(/^\d+$/)) {
            alert('AFT Account Created Limit should be a number ! ');
            return false;
        }

        else if (aft_duration_val == '') {
            alert('AFT Account Created Duration Cannot Be Blank ! ');
            return false;
        }


        else if (!aft_duration_val.match(/^\d+$/)) {
            alert('AFT Account Created Duration should be a number ! ');
            return false;
        }

        return true;



    }


</script>
