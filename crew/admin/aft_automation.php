<?
require_once('includes/application_top.php');
require(DIR_WS_CLASSES . 'aft_automation.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$aft_obj = new aft_automation();

switch($action) {
	case 'set_aft_mode':
		$array_sql_data = array(	'aft_automation_mode' => tep_db_input($_REQUEST['aft_mode']),
	 								'last_modified' => 'now()'
	 								);
		tep_db_perform(TABLE_AFT_AUTOMATION, $array_sql_data, 'update', "aft_automation_category_id = '" . (int)$_REQUEST['aac_id'] . "'");
		
		tep_redirect(FILENAME_AFT_AUTOMATION);
		break;
	case 'save':
		$aft_automation_category_id = (isset($_REQUEST['aac_id']) ? $_REQUEST['aac_id'] : 0);
		
		$aft_obj->save($aft_automation_category_id);
		//tep_redirect(FILENAME_AFT_AUTOMATION);
		break;
	case 'aft_config':
		$aft_automation_category_id = (isset($_REQUEST['aac_id']) ? $_REQUEST['aac_id'] : 0);
		
		$form_content = $aft_obj->anti_fraud_config($aft_automation_category_id);
		break;
	default:
		$form_content = $aft_obj->anti_fraud_menu();
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/anti_fraud.js"></script>
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/edit_area/edit_area_full.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top" width="70%"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
    								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
						<td width="100%"><?=$form_content?></td>
					</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>