<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml; charset=ISO-8859-1');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'anti_fraud.php');

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';
$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$aft_function_id = isset($_SESSION['aft_function_id']) ? $_SESSION['aft_function_id'] : '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

$admin_group_id = isset($_SESSION['login_groups_id']) ? $_SESSION['login_groups_id'] : '';

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case 'get_maxmind_history':
			$alert_isp_array = array('america online');
            
			if (isset($_REQUEST['oID']) && (int)$_REQUEST['oID'] > 0) {
				$oID = (int)$_REQUEST['oID'];
				$limit = (int)(isset($_REQUEST['limit']) ? $_REQUEST['limit'] : 1 ) ;
				$customer_maxmind_history_select_sql = "	SELECT * 
															FROM " . TABLE_MAXMIND_HISTORY . " 
															WHERE orders_id = '" . tep_db_input($oID) ."' 
															ORDER BY maxmind_history_date DESC LIMIT " . $limit;
				$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
				while ($maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql)) {
					
					$coun = tep_get_countries_info($maxmind_history_row['ip_country_code'], "countries_iso_code_2");
					
					echo "<maxmind id='".$oID."'>";
					foreach ($maxmind_history_row as $maxmind_history_key_loop => $maxmind_history_data_loop) {
						echo "<info>";
						echo "<key><![CDATA[".$maxmind_history_key_loop."]]></key>";
						echo "<display><![CDATA[";
						switch ($maxmind_history_key_loop) {
							case 'distance':
								if ($maxmind_history_row['distance'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if ($maxmind_history_row['distance'] == "NA"){
									echo '<span class="redIndicator">NA</span>';
								} else {
									echo $maxmind_history_row['distance'] . " km";
								}
								break;
							case 'ip_country_code':
								if ($maxmind_history_row['ip_country_code']=="No" || $maxmind_history_row['country_match']=="NA"){
									echo '<span class="redIndicator">No</span>';
								} else {
									echo $maxmind_history_row['ip_country_code']. ' - ' .$coun['countries_name'];
								}
								break;
							case 'ip_region':
								if ($maxmind_history_row['ip_region'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if ($maxmind_history_row['ip_region'] == "NA"){
									echo '<span class="redIndicator">NA</span>';
								} else {
									echo $maxmind_history_row['ip_region'];
								}
								break;
							case 'ip_city':
								if ($maxmind_history_row['ip_city'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if ($maxmind_history_row['ip_city'] == "NA"){
									echo '<span class="redIndicator">NA</span>';
								} else {
									echo $maxmind_history_row['ip_city'];
								}
								break;
							case 'country_match':
								if ($maxmind_history_row['country_match'] == "No" || $maxmind_history_row['country_match'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['country_match'].'</span>';
								} else if ($maxmind_history_row['country_match'] == "Yes") {
									echo'<span class="greenIndicator">'.$maxmind_history_row['country_match'].'</span>';
								} else if ($maxmind_history_row['country_match'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else {
								 	echo $maxmind_history_row['country_match'];
								}
								break;
							case 'high_risk_country':
								if ($maxmind_history_row['high_risk_country'] == "No" || $maxmind_history_row['high_risk_country'] == "NA") {
									echo'<span class="greenIndicator">'.$maxmind_history_row['high_risk_country'].'</span>';
								} else if ($maxmind_history_row['high_risk_country'] == "Yes") {
									echo'<span class="redIndicator">'.$maxmind_history_row['high_risk_country'].'</span>';
								} else if ($maxmind_history_row['high_risk_country'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else {
								  	echo $maxmind_history_row['high_risk_country'];
								}
								break;
							case 'city_postal_match':
								if ($maxmind_history_row['city_postal_match'] == "No" || $maxmind_history_row['city_postal_match'] == "NA") {
									echo'<span class="redIndicator">'.$maxmind_history_row['city_postal_match'].'</span>';
								} else if ($maxmind_history_row['city_postal_match'] == "Yes") {
									echo'<span class="greenIndicator">'.$maxmind_history_row['city_postal_match'].'</span>';
								}  else if ($maxmind_history_row['city_postal_match'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								}  else {
									echo $maxmind_history_row['city_postal_match'];
								}
								break;
							case 'score':
								if($maxmind_history_row['score'] < 0.3){
									echo '<span class="greenIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
								} else if($maxmind_history_row['score'] < 0.8){
									echo '<span class="orangeIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
								} else if($maxmind_history_row['score'] <= 10  || $maxmind_history_row['country_match']=="NA" || $maxmind_history_row['country_match']=="Not checked") {
									echo '<span class="redIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
								}
								break;
							case 'anonymous_proxy':
								if ($maxmind_history_row['anonymous_proxy'] == "No" || $maxmind_history_row['anonymous_proxy'] == "NA") {
									echo '<span class="greenIndicator">'.$maxmind_history_row['anonymous_proxy'].'</span>';
								} else if ($maxmind_history_row['anonymous_proxy']=="Yes") {
									echo '<span class="redIndicator">'.$maxmind_history_row['anonymous_proxy'].'</span>';
								} else if ($maxmind_history_row['anonymous_proxy']=="Not Check") {
										echo '<span class="redIndicator">Not checked</span>';
								} else {
									echo $maxmind_history_row['anonymous_proxy'];
								}
								break;
							case 'proxy_score':
								if($maxmind_history_row['proxy_score'] == 0) {
									echo '<span class="greenIndicator">'.$maxmind_history_row['proxy_score'].'</span>';
								} else if($maxmind_history_row['proxy_score'] > 0 || $maxmind_history_row['country_match'] == "NA") {
									echo '<span class="redIndicator">'.$maxmind_history_row['proxy_score'].'</span>';
								}  else if ($maxmind_history_row['proxy_score'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								}
								break;
							case 'ip_isp':
								if ($maxmind_history_row['ip_isp'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['ip_isp'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['ip_isp'].'</span>';
								} else {
									echo in_array(strtolower($maxmind_history_row['ip_isp']), $alert_isp_array) ? '<span class="redIndicator">'.$maxmind_history_row['ip_isp'].'</span>' : $maxmind_history_row['ip_isp'];
								}
								break;
							case 'ip_organization':
								if ($maxmind_history_row['ip_organization'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['ip_isp'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['ip_organization'].'</span>';
								} else {
									echo in_array(strtolower($maxmind_history_row['ip_organization']), $alert_isp_array) ? '<span class="redIndicator">'.$maxmind_history_row['ip_organization'].'</span>' : $maxmind_history_row['ip_organization'];
								}
								break;
							case 'bin_country_code':
								if ($maxmind_history_row['bin_country_code'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['ip_isp'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['bin_country_code'].'</span>';
								} else {
									echo $maxmind_history_row['bin_country_code'];
								}
								break;
							case 'bin_country_code':
								if ($maxmind_history_row['bin_name'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['bin_name'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['bin_name'].'</span>';
								} else {
									echo $maxmind_history_row['bin_name'];
								}
								break;
							case 'bin_name_match':
								if ($maxmind_history_row['bin_name_match']=="No" || $maxmind_history_row['bin_name_match']=="NA") {
									echo '<span class="redIndicator">'.$maxmind_history_row['bin_name_match'].'</span>';
								} else if ($maxmind_history_row['bin_name_match']=="Yes") {
									echo'<span class="greenIndicator">'.$maxmind_history_row['bin_name_match'].'</span>';
								} else if ($maxmind_history_row['bin_name_match']=="Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else {
									echo $maxmind_history_row['bin_name_match'];
								}
								break;
							case 'bin_name':
								if ($maxmind_history_row['bin_name'] == "Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['bin_name'] == "NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['bin_name'].'</span>';
								} else {
									echo $maxmind_history_row['bin_name'];
								}
								break;
							case 'bin_phone':
								if ($maxmind_history_row['bin_phone']=="Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else if($maxmind_history_row['bin_name']=="NA"){
									echo '<span class="redIndicator">'.$maxmind_history_row['bin_phone'].'</span>';
								} else {
									echo $maxmind_history_row['bin_phone'];
								}
								break;
							case 'bin_phone_match':
								if ($maxmind_history_row['bin_phone_match'] == "No" || $maxmind_history_row['bin_phone_match'] == "NA") {
									echo'<span class="redIndicator">'.$maxmind_history_row['bin_phone_match'].'</span>';
								} else if ($maxmind_history_row['bin_phone_match'] == "Yes") {
								  	echo'<span class="greenIndicator">'.$maxmind_history_row['bin_phone_match'].'</span>';
								} else if ($maxmind_history_row['bin_phone_match']=="Not Check") {
									echo '<span class="redIndicator">Not checked</span>';
								} else {
									echo $maxmind_history_row['bin_phone_match'];
								}
								break;
							default:
                                if ($maxmind_history_data_loop == "Not Check" || empty($maxmind_history_data_loop)) {
									echo '<span class="redIndicator">Not checked</span>';
								} else {
									echo $maxmind_history_data_loop;
								}
								break;
						}
						echo "]]></display>";
						echo "</info>";
					}
					echo "</maxmind>";
				}
			}
			break;
		case 'get_aft_function':
			$error_found = 0;
			$content_html = '';
			
			$aft_functions_select_sql = "	SELECT aft_functions_name, aft_functions_description, line_determine, aft_functions_setting 
											FROM " . TABLE_AFT_FUNCTIONS . " 
											WHERE aft_functions_id = '" . (int)$aft_functions_id . "'";
			$aft_functions_result_sql = tep_db_query($aft_functions_select_sql);
			if ($aft_functions_row = tep_db_fetch_array($aft_functions_result_sql)) {
				$anti_obj = new anti_fraud();
				if (method_exists($anti_obj, $aft_functions_row['aft_functions_name'])) {
					$content_html = '					<table border="1" cellspacing="0" cellpadding="2" width="100%">
															<tr>
																<td align="center" colspan="3" class="pageHeading">' . $aft_functions_row['aft_functions_description'] . '</td>
															</tr>';
					
					if (tep_not_null($aft_functions_row['aft_functions_setting'])) {
						$para_type_array = unserialize($aft_functions_row['aft_functions_setting']);
						$para_num = sizeof($para_type_array);
						
						if ($para_num > 0) {
							for ($para_cnt = 0; $para_cnt < $para_num; $para_cnt++) {
								switch($para_type_array[$para_cnt]['type']) {
									case 'text':
										$content_html .= '	<tr>
																<td class="main" valign="top">' . ($para_cnt + 1) . '</td>
																<td valign="top">' . tep_draw_input_field('para_' . $para_cnt, '', ' id="para_' . $para_cnt . '"') . '</td>
																<td class="main" valign="top">' . $para_type_array[$para_cnt]['description'] . '</td>
															</tr>';
										
										break;
									case 'select':
										$content_html .= '	<tr>
																<td class="main" valign="top">' . ($para_cnt + 1) . '</td>
																<td valign="top">' . tep_draw_pull_down_menu('para_' . $para_cnt, $para_type_array[$para_cnt]['option'], '', ' id="para_' . $para_cnt . '"') . '</td>
																<td class="main" valign="top">' . $para_type_array[$para_cnt]['description'] . '</td>
															</tr>';
										
										break;
									case 'order_comment':
										$order_comments_array = array();
										$order_comments_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
										
										$orders_comments_select_sql = "	SELECT orders_comments_id, orders_comments_title 
																		FROM " . TABLE_ORDERS_COMMENTS . " 
																		WHERE orders_comments_status = 1 
																			AND orders_comments_filename = '" . tep_db_input(FILENAME_ORDERS) . "'";
										$orders_comments_result_sql = tep_db_query($orders_comments_select_sql);
										while ($orders_comments_row = tep_db_fetch_array($orders_comments_result_sql)) {
											$order_comments_array[] = array('id' => $orders_comments_row['orders_comments_id'], 'text' => $orders_comments_row['orders_comments_title']);
										}
										
										$content_html .= '	<tr>
																<td class="main" valign="top">' . ($para_cnt + 1) . '</td>
																<td valign="top">' . tep_draw_pull_down_menu('para_' . $para_cnt, $order_comments_array, '', ' id="para_' . $para_cnt . '"') . '</td>
																<td class="main" valign="top">' . $para_type_array[$para_cnt]['description'] . '</td>
															</tr>';
										
										break;
								}
							}
						}
					}
					
					$content_html .= '					</table>';
				} else {
					$error_found = 1;
				}
			} else {
				$error_found = 1;
			}
			
			echo '	<box_content_result>
						<function_name><![CDATA[' . $aft_functions_row['aft_functions_name'] . ']]></function_name>
						<line_determine><![CDATA[' . $aft_functions_row['line_determine'] . ']]></line_determine>
						<box_content><![CDATA[' . $content_html . ']]></box_content>
						<total_para><![CDATA[' . $para_num . ']]></total_para>
						<error><![CDATA[' . $error_found . ']]></error>
					</box_content_result>';
			break;
		default:
			echo '<result>Unknown request!</result>';
			break;
	}
}

echo '</response>';
?>