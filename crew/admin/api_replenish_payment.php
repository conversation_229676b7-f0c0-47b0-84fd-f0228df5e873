<?php
/*
 * Developer: <PERSON><PERSON><PERSON>
 * DateTime : 20/03/20107 3:58PM
 */

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'api_replenish_publishers.php');
require_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
require_once('pear/Date.php');

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_API_REPLENISH_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_API_REPLENISH_PAYMENT);
}

define('DISPLAY_PRICE_DECIMAL', 4);

$allow_create_po_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_NEW_PO');
$allow_process_po_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_PROCESS_PO');
$allow_po_cancel_pending_receive_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_CANCEL_PENDING_RECEIVE');
$allow_verify_po_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_VERIFY_PO');
$allow_add_po_remark_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_ADD_REMARK');
$allow_view_po_remark_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_VIEW_REMARK');
$allow_po_make_payment_permission = tep_admin_files_actions(FILENAME_API_REPLENISH_PAYMENT, 'API_MAKE_PAYMENT');

if (isset($_REQUEST['subaction'])) {
    switch ($_REQUEST['subaction']) {
        case 'create_blank_po':
            if (!$allow_create_po_permission) {
                $messageStack->add_session(ERROR_API_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'add_po_cb':
            if (!$allow_create_po_permission) {
                $messageStack->add_session(ERROR_API_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'create_po':
            $error = false;

            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['low_stock_batch'])) {
                    $messageStack->add_session(ERROR_API_FORM_EMPTY_PRODUCTS, 'error');
                    $error = true;
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_API_FORM_CREATE_PERMISSION, 'error');
            }

            if ($error) {
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
    }
}

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$po_publisher = new api_replenish_publishers();
$edit_po_obj = new api_replenish_payment($_SESSION['login_id'], $_SESSION['login_email_address']);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;

// CSV matching button from PO form
if (isset($_REQUEST['csvMatchBtn'])) {
    if ($subaction == "calculate_po") {
        $_REQUEST['subaction'] = "create_po";
        $_GET['subaction'] = "create_po";
        $subaction = "create_po";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

// Back button from preview
if (isset($_REQUEST['BackBtn'])) {
    if ($subaction == "insert_po_product") {
        $_REQUEST['subaction'] = "create_po_product";
        $_GET['subaction'] = "create_po_product";
        $subaction = "create_po_product";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

// Back button from calculate po
if (isset($_REQUEST['BackBtnToList'])) {
    if ($subaction == "preview_po") {
        $_REQUEST['subaction'] = "create_po";
        $_GET['subaction'] = "create_po";
        $subaction = "create_po";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

// API preview CB
if (isset($_REQUEST['poStatusButton'])) {
    if ($subaction == "calculate_po") {
        $_REQUEST['subaction'] = "preview_po_status";
        $_GET['subaction'] = "preview_po_status";
        $subaction = "preview_po_status";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

// Back button from preview API CB
if (isset($_REQUEST['BackBtnCB'])) {
    if ($subaction == "insert_po_product") {
        $_REQUEST['subaction'] = "add_po_cb";
        $_GET['subaction'] = "add_po_cb";
        $subaction = "add_po_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

// Submit button from preview API CB
if (isset($_REQUEST['submitAPICB'])) {
    if ($subaction == "insert_po_product") {
        $_REQUEST['subaction'] = "insert_po_cb";
        $_GET['subaction'] = "insert_po_cb";
        $subaction = "insert_po_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&subaction=show_po_list'));
    }
}

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case "preview_po":
            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['po_delivery_address']) || (isset($_REQUEST['po_delivery_address']) && !tep_not_null($_REQUEST['po_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['po_items_prod_id']) || (isset($_REQUEST['po_items_prod_id']) && count($_REQUEST['po_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PRODUCTS, 'Error');
                }

                if (isset($_REQUEST['po_items_prod_id'])) {
                    $prod_ids = $_REQUEST['po_items_prod_id'];
                    foreach ($prod_ids as $prd_id) {
                        if (!isset($_REQUEST['po_item_qty_' . $prd_id]) || (isset($_REQUEST['po_item_qty_' . $prd_id]) && $_REQUEST['po_item_qty_' . $prd_id] == 0)) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_API_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
                        }
                    }
                }

                if ($error) {
                    $_REQUEST['subaction'] = "calculate_po";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "calculate_po";
                    $subaction = "calculate_po";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_API_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "calculate_po":
            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['po_delivery_address']) || (isset($_REQUEST['po_delivery_address']) && !tep_not_null($_REQUEST['po_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }
                
                $po_publisher_check = new api_replenish_publishers($_REQUEST['po_supplier']);
                $po_supplier_check_info = $po_publisher_check->get_publishers();
                $po_supplier_id = $po_supplier_check_info[$_REQUEST['po_supplier']]['publishers_supplier_id'];
                $po_supplier_term = $po_supplier_check_info[$_REQUEST['po_supplier']]['publisher_payment_term'];
                
                if (!isset($_REQUEST['api_items_cdk_id']) || (isset($_REQUEST['api_items_cdk_id']) && count($_REQUEST['api_items_cdk_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PRODUCTS, 'Error');
                } else {
                    foreach ($_REQUEST['api_items_cdk_id'] as $api_cdkey) {
                        $po_supplier_item = $po_publisher_check->is_po_supplier_item($api_cdkey);
                        if (!$po_supplier_item) {
                            $error = true;
                            $messageStack->add(ERROR_API_FORM_PUBLISHER_NOT_MATCH, 'Error');
                        }
                        break;
                    }
                }
                if ($po_supplier_term) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_SUPPLIER_TYPE, 'Error');
                }
                
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($po_supplier_id, $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_po";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_po";
                    $subaction = "insert_po";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_API_FORM_CREATE_PERMISSION, 'Error');
            }
            break;

        case "preview_po_status":
            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_API_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_SUPPLIER, 'Error');
                }
                
                if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['po_delivery_address']) || (isset($_REQUEST['po_delivery_address']) && !tep_not_null($_REQUEST['po_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }
                
                $po_publisher_calculate = new api_replenish_publishers($_REQUEST['po_supplier']);
                $po_supplier_info = $po_publisher_calculate->get_publishers();
                $po_supplier_calculate_id = $po_supplier_info[$_REQUEST['po_supplier']]['publishers_supplier_id'];
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($po_supplier_calculate_id, $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_LOCKED_SUPPLIER, 'Error');
                }

                $po_publisher_calculate->set_api_temp_status_empty($_REQUEST['po_supplier'], 'true');
                if (!isset($_REQUEST['api_items_cdk_id']) || (isset($_REQUEST['api_items_cdk_id']) && count($_REQUEST['api_items_cdk_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_API_CB_FORM_EMPTY_PRODUCTS, 'Error');
                } else {
                    $empty_remarks_count = 0;

                    foreach ($_REQUEST['api_items_cdk_id'] as $api_cdkey) {
                        $po_publisher_calculate->set_api_temp_status($api_cdkey, $_REQUEST['api_status_select_'.$api_cdkey]);
                        if (isset($_REQUEST['bulk_remarks']) && tep_not_null($_REQUEST['bulk_remarks'])) {
                            continue;
                        }
                        if (!isset($_REQUEST['remarks_' . $api_cdkey]) || (isset($_REQUEST['remarks_' . $api_cdkey]) && !tep_not_null($_REQUEST['remarks_' . $api_cdkey]))) {
                            $empty_remarks_count++;
                        }
                    }
                    
                    if ($empty_remarks_count > 0) {
                        $error = true;
                        $messageStack->add(ERROR_API_FORM_EMPTY_REMARKS, 'Error');
                    }
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_po";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_po";
                    $subaction = "insert_po";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_API_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "insert_po_product":
            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PO_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PO_DATE, 'Error');
                }

                if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['po_items_prod_id']) || (isset($_REQUEST['po_items_prod_id']) && count($_REQUEST['po_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PRODUCTS, 'Error');
                }

                if (isset($_REQUEST['po_items_prod_id'])) {
                    $prod_ids = $_REQUEST['po_items_prod_id'];
                    foreach ($prod_ids as $prd_id) {
                        if (!isset($_REQUEST['po_item_qty_' . $prd_id]) || (isset($_REQUEST['po_item_qty_' . $prd_id]) && $_REQUEST['po_item_qty_' . $prd_id] == 0)) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_API_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
                        }
                    }
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_API_FORM_CREATE_PERMISSION, 'Error');
            }

            if (!$error) {
                $edit_po_obj->insert_new_po($_REQUEST, $messageStack);
            }
            
            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id'))));

            break;

        case "insert_po_cb":
            if ($allow_create_po_permission) {
                if (!isset($_REQUEST['api_items_cdk_id']) || (isset($_REQUEST['api_items_cdk_id']) && count($_REQUEST['api_items_cdk_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_API_FORM_EMPTY_PRODUCTS, 'Error');
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_API_FORM_CREATE_PERMISSION, 'Error');
            }

            if (!$error) {
                $edit_po_obj->insert_new_cb($_REQUEST, $messageStack);
            }
            
            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id'))));

            break;
            
        case 'approve_po':
            if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'PrintBtn') {
                ;
            } else {
                if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'CancelBtn') {
                    $edit_po_obj->cancel_po($_REQUEST, $messageStack);
                } else if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'ApproveBtn') {
                    $edit_po_obj->approve_po($_REQUEST, $messageStack);
                }
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            }
            break;

        case 'add_remark':
            if ($allow_add_po_remark_permission) {
                $edit_po_obj->po_add_remark($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_API_FORM_ADD_REMARK_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'refund_po':
            if (isset($_REQUEST['CompleteBtn'])) {
                $po_status = '2';
                $edit_po_obj->complete_po($_REQUEST, $messageStack, $po_status);
            }

            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'refund_po_cancel':
            if ($allow_po_cancel_pending_receive_permission) {
                $edit_po_obj->refund_unpaid($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_API_FORM_API_CANCEL_PENDING_RECEIVE_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'rollback':
            if (isset($_REQUEST['RollbackProcessBtn'])) {
                $edit_po_obj->rollback_complete_po($_REQUEST, $messageStack);
            }

            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'verify_po':
            if ($allow_verify_po_permission) {
                $edit_po_obj->verifying_po($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_PO_FORM_VERIFY_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'make_payment':
            if ($allow_po_make_payment_permission) {
                $edit_po_obj->make_po_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_API_FORM_API_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'debit_payment':
            if ($allow_po_make_payment_permission) {
                $edit_po_obj->debit_po_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_API_FORM_API_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po'));
            break;

        case 'edit_po':
            if (!$edit_po_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction'))));
            }
            break;

        case 'search_po':
            if (!$edit_po_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction'))));
            } else {
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_po&po_id=' . $edit_po_obj->po_info['po_id']));
            }
            break;
            
        case 'show_po_list':
            if (isset($_REQUEST['reset'])) {
                unset($_SESSION['po_search']);
                tep_redirect(tep_href_link(FILENAME_API_REPLENISH_PAYMENT));
            }
            
        default:
            break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>general.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/modal_win.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/xmlhttp.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.tabs.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/ogm_jquery.js"></script>
        <?php include_once(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
        <?php include_once(DIR_WS_INCLUDES . 'javascript/api_replenish_payment_xmlhttp.js.php'); ?>
        <script language="javascript">
            <!--
            var pageLoaded = false;
            function init() {
                // quit if this function has already been called
                if (arguments.callee.done)
                    return;

                // flag this function so we don't do the same thing twice
                arguments.callee.done = true;

                initInfoCaptions();
                pageLoaded = true;  // Control when a javascript event in this page can be triggered
            }

            /* for Mozilla */
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", init, null);
            }

            /* for other browsers */
            window.onload = init;
            //-->
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <div id="fancy_box" class="fancy_box" style="display:none;">
            <div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
            <div class="fancy_inner" style="display: block;">
                <div id="fancy_close" class="fancy_close" style="display: none;"></div>
                <div class="fancy_frame_bg">
                    <div class="fancy_bg fancy_bg_n"></div>
                    <div class="fancy_bg fancy_bg_ne"></div>
                    <div class="fancy_bg fancy_bg_e"></div>
                    <div class="fancy_bg fancy_bg_se"></div>
                    <div class="fancy_bg fancy_bg_s"></div>
                    <div class="fancy_bg fancy_bg_sw"></div>
                    <div class="fancy_bg fancy_bg_w"></div>
                    <div class="fancy_bg fancy_bg_nw"></div>
                </div>
                <div id="fancy_content" class="fancy_content"></div>
            </div>
        </div>
        <div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%">
                                <table border="0" width="100%" cellspacing="0" cellpadding="5">
                                    <tr>
                                        <td class="pageHeading" colspan="2">
                                            <?php
                                            if (isset($_REQUEST['subaction']) || isset($_REQUEST['po_list_type'])) {
                                                if ($_REQUEST['subaction'] == 'create_po' ||
                                                    $_REQUEST['subaction'] == 'create_blank_po' ||
                                                    $_REQUEST['subaction'] == 'add_po_cb' ||
                                                    $_REQUEST['subaction'] == 'preview_po_status' ) {
                                                    if ($_REQUEST['po_list_type'] == 'add_po_cb') {
                                                        echo 'API Charge Back Form';
                                                    } else {
                                                        echo 'API Payment Request Form';
                                                    }
                                                } else if ($_REQUEST['subaction'] == 'calculate_po' ||
                                                            $_REQUEST['subaction'] == 'create_po_product') {
                                                    echo 'Calculate API Payment Request Form';
                                                } else if ($_REQUEST['subaction'] == 'preview_po') {
                                                    echo 'Preview API Payment Request Form';
                                                } else if ($_REQUEST['subaction'] == 'edit_po') {
                                                    echo 'API Payment Request';
                                                } else if ($_REQUEST['subaction'] == 'po_report') {
                                                    echo 'API Replenish Report';
                                                } else {
                                                    echo 'API Payment Request List';
                                                }
                                            } else {
                                                echo 'API Payment Request List';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td valign="top">
                                            <?php
                                            switch ($_REQUEST['subaction']) {
                                                case 'create_po_product':
                                                    echo $edit_po_obj->calculate_po_form($_REQUEST);
                                                    break;
                                                case 'create_po':
                                                    echo $edit_po_obj->show_items_po_form($_REQUEST);
                                                    break;
                                                case 'create_blank_po':
                                                    echo $edit_po_obj->show_po_form($_REQUEST);
                                                    break;
                                                case 'add_po_cb':
                                                    echo $edit_po_obj->show_po_form($_REQUEST);
                                                    break;
                                                case 'insert_po':
                                                    echo $edit_po_obj->show_po_form($_REQUEST);
                                                    break;
                                                case 'calculate_po':
                                                    echo $edit_po_obj->calculate_po_form($_REQUEST);
                                                    break;
                                                case 'preview_po':
                                                    echo $edit_po_obj->preview_po_form($_REQUEST);
                                                    break;
                                                case 'preview_po_status':
                                                    echo $edit_po_obj->preview_po_cb_form($_REQUEST);
                                                    break;
                                                case 'edit_po':
                                                    echo $edit_po_obj->show_edit_po_form($_REQUEST);
                                                    break;
                                                case 'show_po_list':
                                                    echo $edit_po_obj->show_po_list($_REQUEST);
                                                    break;
                                                case 'po_report':
                                                    echo $edit_po_obj->show_po_report($_REQUEST);
                                                    break;
                                                default:
                                                    echo $edit_po_obj->search_po_list($_REQUEST);
                                                    break;
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <!-- body_text_eof //-->
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- body_eof //-->
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <?php
        if ($_REQUEST['subaction'] == 'edit_po') {
            echo '<script type="text/javascript">';
            echo '	jQuery().ready(function() {';
            echo "		getAPIPaymentStatistic('" . $edit_po_obj->supplier['supplier_id'] . "');";
            echo '	});';
            echo '</script>';
        }
        if ($_REQUEST['subaction'] == 'po_report') {
            echo '<script type="text/javascript">';
            echo '	jQuery().ready(function() {';
            echo "          jQuery('#search-tab > ul').tabs();";
            echo '	});';
            echo '</script>';
        }
        ?>
    </body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>