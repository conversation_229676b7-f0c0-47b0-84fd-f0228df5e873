<?php
/*
  $Id: api_report.php,v 1.11 2015/10/06 07:22:33 jeeva.ka<PERSON><PERSON>an Exp $

  Developer: <PERSON>
  Copyright (c) 2005 SKC Ventrue

  Released under the GNU General Public License
 */

require('includes/application_top.php');

tep_set_time_limit(1000);

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$report_type_array = array('cdk_dtu' => 'CDK Direct Top Up',
    'display_cdkey_dtu_api_log' => 'CDK Direct Top Up Log');
$report_type_array_sel = array();
foreach ($report_type_array as $report_type_key_loop => $report_type_display_loop) {
    $report_type_array_sel[] = array('id' => $report_type_key_loop,
        'text' => $report_type_display_loop);
}

$api_report_export_permission = tep_admin_files_actions(FILENAME_API_REPORT, 'API_REPORT_CDK_DIRECT_TOP_UP');
$api_dtu_repost_transaction_permission = tep_admin_files_actions(FILENAME_API_REPORT, 'API_DTU_REPOST_TRANSACTION');
$api_dtu_status_change_permission = tep_admin_files_actions(FILENAME_API_REPORT, 'API_DTU_STATUS_CHANGE');

if (tep_not_null($action)) {
    switch ($action) {
        case 'repost':
            if ($api_dtu_repost_transaction_permission) {
                if (isset($_REQUEST['tID']) && (int) $_REQUEST['tID'] > 0) {
                    // check condition
                    $orders_top_up_sql = "	SELECT top_up_status, top_up_process_flag
											FROM " . TABLE_ORDERS_TOP_UP . "
											WHERE top_up_id = '" . (int) $_REQUEST['tID'] . "'";
                    $orders_top_up_result = tep_db_query($orders_top_up_sql);
                    if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result)) {
                        if (($orders_top_up_row['top_up_status'] == '1' || $orders_top_up_row['top_up_status'] == '10') && $orders_top_up_row['top_up_process_flag'] == '2') {

                            $reset_top_up_sql = " 	UPDATE " . TABLE_ORDERS_TOP_UP . " 
													SET top_up_status = '1',
														top_up_process_flag = '0',
														top_up_timestamp = NOW() 
													WHERE top_up_id = '" . (int) $_REQUEST['tID'] . "' 
														AND top_up_process_flag = '2' ";
                            tep_db_query($reset_top_up_sql);

                            $top_up_log_sql_data = array('top_up_id' => (int) $_REQUEST['tID'],
                                'data_added' => 'now()',
                                'remark' => tep_db_prepare_input('Manual Repost top-up transaction'),
                                'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $top_up_log_sql_data);
                            $messageStack->add_session(TEXT_INFO_API_REPORT_TOPUP_REPORT_IN_QUEUE, 'success');
                        } else {
                            $messageStack->add_session(WARNING_API_REPORT_NOT_ALLOWED_TO_REPORT, 'warning');
                        }
                    } else {
                        $messageStack->add_session(WARNING_API_REPORT_DTU_ID_NOT_FOUND, 'warning');
                    }
                } else {
                    $messageStack->add_session(WARNING_API_REPORT_DTU_ID_NOT_FOUND, 'warning');
                }
            } else {
                $messageStack->add_session(WARNING_API_REPORT_NO_PERMISSION_ALLOWED, 'warning');
            }
            tep_redirect(tep_href_link(FILENAME_API_REPORT), tep_get_all_get_params(array('action', 'tID')) . 'action=report');
            break;
        case 'status_change':
            $changed_status = 'failed';
            if ($api_dtu_repost_transaction_permission) {
                if (isset($_REQUEST['top_up_id']) && (int) $_REQUEST['top_up_id'] > 0) {
                    $topup_timestamp = (isset($_REQUEST['topup_timestamp'])) ? $_REQUEST['topup_timestamp'] : date('Y-m-d');
                    $topup_hour = (isset($_REQUEST['topup_hour'])) ? $_REQUEST['topup_hour'] : date('H');
                    $topup_min = (isset($_REQUEST['topup_min'])) ? $_REQUEST['topup_min'] : date('i');
                    $topup_sec = (isset($_REQUEST['topup_sec'])) ? $_REQUEST['topup_sec'] : date('s');

                    $topup_timestamp = $topup_timestamp . ' ' . $topup_hour . ':' . $topup_min . ':' . $topup_sec;
                    
                    $dtu_status = (isset($_REQUEST['dtu_status'])) ? $_REQUEST['dtu_status'] : '';
                    $dtu_remarks = (isset($_REQUEST['dtu_remarks'])) ? $_REQUEST['dtu_remarks'] : '';
                    $publishers_ref_id = (isset($_REQUEST['publishers_ref_id'])) ? $_REQUEST['publishers_ref_id'] : '';

                    // check condition
                    $orders_top_up_sql = "  SELECT top_up_status, top_up_process_flag, orders_products_id, publishers_ref_id
                                            FROM " . TABLE_ORDERS_TOP_UP . "
                                            WHERE top_up_id = '" . (int) $_REQUEST['top_up_id'] . "'";
                    $orders_top_up_result = tep_db_query($orders_top_up_sql);
                    if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result)) {
                        if ($dtu_status == '1') { // Update Failed status
                            // Update DTU status
                            $status_top_up_sql = "  UPDATE " . TABLE_ORDERS_TOP_UP . " 
                                                    SET top_up_status = '10',
                                                        top_up_timestamp = now()" .
                                                    (empty($publishers_ref_id) ? "" : ", publishers_ref_id = '" . tep_db_prepare_input($publishers_ref_id) . "'") .
                                                    "WHERE top_up_id = '" . (int) $_REQUEST['top_up_id'] . "' 
                                                    LIMIT 1";
                            tep_db_query($status_top_up_sql);

                            // Update DTU remarks
                            $top_up_log_sql_data = array(
                                'top_up_id' => (int) $_REQUEST['top_up_id'],
                                'data_added' => 'now()',
                                'remark' => tep_db_prepare_input($dtu_remarks),
                                'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $top_up_log_sql_data);

                            $status_text = 'Failed';
                        } else { // Update Reloaded status
                            // Update DTU status
                            $status_top_up_sql = "  UPDATE " . TABLE_ORDERS_TOP_UP . " 
                                                    SET top_up_status = '3',
                                                        top_up_process_flag = '2',
                                                        publishers_ref_id = '" . tep_db_prepare_input($publishers_ref_id) . "',
                                                        top_up_timestamp = '" . tep_db_prepare_input($topup_timestamp) . "' 
                                                    WHERE top_up_id = '" . (int) $_REQUEST['top_up_id'] . "' 
                                                    LIMIT 1";
                            tep_db_query($status_top_up_sql);
                            
                            // Update DTU Remarks
                            $dtu_remarks_sql = "SELECT * FROM " . TABLE_ORDERS_TOP_UP_REMARK . "
                                                WHERE top_up_id = '" . (int) $_REQUEST['top_up_id'] . "'
                                                ORDER BY orders_top_up_remark_id DESC";
                            $dtu_remarks_result = tep_db_query($dtu_remarks_sql);

                            $top_up_log_sql_data = array(
                                'data_added' => tep_db_prepare_input($topup_timestamp),
                                'top_up_id' => (int) $_REQUEST['top_up_id'],
                                'remark' => tep_db_prepare_input($dtu_remarks),
                                'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $top_up_log_sql_data);

                            $status_text = 'Reloaded';
                        }

                        // Send Email notifications
                        $mail_subject = '[DTU] Manual mark TopUp #' . (int) $_REQUEST['top_up_id'] . ' as ' . $status_text;
                        $mail_content = 'Hi Admin,'."\n";
                        $mail_content .= 'Manual action on DTU TopUp #' . (int) $_REQUEST['top_up_id'] . ' as below:'."\n";
                        $mail_content .= 'Action Date: ' . date('Y-m-d H:i:s') . "\n";
                        $mail_content .= 'Action: ' . (($orders_top_up_row['top_up_status'] == '1') ? 'Pending' : 'Failed') . ' -> ' . $status_text . "\n";
                        $mail_content .= 'Action by: ' . $_SESSION['login_email_address'] . "\n";
                        $mail_content .= 'IP: ' . getenv("REMOTE_ADDR") . "\n";
                        $mail_content .= 'Remark: ' . $dtu_remarks . "\n";

                        // get products_id
                        $prod_id_sql = "SELECT orders_products_id, orders_id, products_id, final_price, products_good_delivered_quantity
                                        FROM " . TABLE_ORDERS_PRODUCTS . " 
                                        WHERE orders_products_id = '" . (int) $orders_top_up_row['orders_products_id'] . "' 
                                        LIMIT 1";
                        $prod_id_result = tep_db_query($prod_id_sql);

                        if ($prod_id_row = tep_db_fetch_array($prod_id_result)) {
                             // Insert log_delivered_products for GP Report
                            include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                            $ogm_report_tool_obj = new ogm_report_tool();
                            $top_up_info_value = isset($prod_id_row['final_price']) ? $prod_id_row['final_price'] : 0;
                            $ogm_report_tool_obj->log_top_up_reloaded((int) $_REQUEST['top_up_id'], $prod_id_row['orders_id'], $prod_id_row['orders_products_id'], $prod_id_row['products_id'], $top_up_info_value, $prod_id_row['products_good_delivered_quantity']);
                            unset($ogm_report_tool_obj);

                            // Send Email
                            $cat_cfg_array = tep_get_cfg_setting($prod_id_row['products_id'], 'product', 'MANUAL_DTU_STATUS_CHANGE_EMAIL');
                            $email_to_array = tep_parse_email_string($cat_cfg_array['MANUAL_DTU_STATUS_CHANGE_EMAIL']);
                            for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        }

                        $changed_status = 'success';
                    } else {
                        $messageStack->add_session(WARNING_API_REPORT_DTU_ID_NOT_FOUND, 'warning');
                    }
                } else {
                    $messageStack->add_session(WARNING_API_REPORT_DTU_ID_NOT_FOUND, 'warning');
                }
            } else {
                $messageStack->add_session(WARNING_API_REPORT_NO_PERMISSION_ALLOWED, 'warning');
            }
            tep_redirect(tep_href_link(FILENAME_API_REPORT, tep_get_all_get_params(array('action', 'top_up_id')) . 'action=status_change_result&status='.$changed_status));
            break;
        case 'reset_session':
            unset($_SESSION['api_report']);
            $messageStack->add_session(TEXT_INFO_API_REPORT_SESSOPN_RESET);
            tep_redirect(tep_href_link(FILENAME_API_REPORT));
            break;
        case 'viewlog':
            $log_records_array = array();
            $top_up_id = isset($_GET['top_up_id']) ? (int) $_GET['top_up_id'] : 0;
            ?>
            <!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
            <html <?= HTML_PARAMS ?> >
                <head>
                    <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
                    <title><?= TITLE ?></title>
                    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
                    <script language="javascript" src="includes/general.js"></script>
                    <script language="javascript">
                        <!--
                            function showOverEffect(object, class_name, extra_row) {
                            rowOverEffect(object, class_name);
                            var rowObjArray = extra_row.split('##');
                            for (var i = 0; i < rowObjArray.length; i++) {
                                if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                    rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
                                }
                            }
                        }

                        function showOutEffect(object, class_name, extra_row) {
                            rowOutEffect(object, class_name);
                            var rowObjArray = extra_row.split('##');
                            for (var i = 0; i < rowObjArray.length; i++) {
                                if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                    rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
                                }
                            }
                        }

                        function showClicked(object, class_name, extra_row) {
                            rowClicked(object, class_name);
                            var rowObjArray = extra_row.split('##');
                            for (var i = 0; i < rowObjArray.length; i++) {
                                if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                    rowClicked(document.getElementById(rowObjArray[i]), class_name);
                                }
                            }
                        }
                        //-->
                    </script>
                </head>
                <body>
                    <center>
                        <table border="1" width="90%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
                            <tr>
                                <td width="15%" style="text-align:center;" class="ordersBoxHeading">API Log Id</td>
                                <td align="left" style="text-align:center;" class="ordersBoxHeading"> Result Code</td>
                                <td align="left" style="text-align:center;" class="ordersBoxHeading">Request Start</td>
                                <td width="12%" style="text-align:center;" class="ordersBoxHeading">Response Log</td>
                                <td width="25%" style="text-align:center;" class="ordersBoxHeading">Request Log</td>

                            </tr>
                            <?php
                            $row = 0;

                            $view_log_top_up_sql = "SELECT al.top_up_id,al.api_log_id,al.result_code,al.request_log,al.response_log,al.request_start 
                                    FROM " . TABLE_API_LOG . " AS al 
                                    WHERE al.top_up_id = " . $top_up_id . " 
                                    ORDER BY al.api_log_id ASC";
                            $view_log_top_up_result = tep_db_query($view_log_top_up_sql);
                            while ($vlog_row = tep_db_fetch_array($view_log_top_up_result)) {
                                $row_style = ($row % 2) ? 'ordersListingEven' : 'ordersListingOdd';
                                ?>
                                <tr class="<?php echo $row_style ?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?php echo $row ?>')"
                                    onmouseout="showOutEffect(this, '<?php echo $row_style ?>', '<?php echo $row ?>')"
                                    onclick="showClicked(this, '<?php echo $row_style ?>', '<?php echo $row ?>')">
                                    <td class="ordersRecords"><?php echo $vlog_row['api_log_id'] ?></td>
                                    <td class="ordersRecords" nowrap><?php echo $vlog_row['result_code'] ?></td>
                                    <td class="ordersRecords" nowrap><?php echo $vlog_row['request_start'] ?></td>
                                    <td class="ordersRecords"><?php echo str_replace(array(','), array(', '), htmlspecialchars($vlog_row['response_log'])); ?></td>
                                    <td class="ordersRecords" nowrap><?php echo $vlog_row['request_log'] ?></td>
                                </tr>
                                <?php
                                $row++;
                            }
                            ?>
                        </table>
                    </center>
                </body>
            </html>
            <?php
            exit;
        case 'status_change_form':
            $log_records_array = array();
            $top_up_id = isset($_GET['top_up_id']) ? (int) $_GET['top_up_id'] : 0;
            $top_up_status = isset($_GET['top_up_status']) ? (int) $_GET['top_up_status'] : 0;

            $dtu_status_array = array(array('id' => '', 'text' => 'Choose Status...'));
            switch ($top_up_status) {
                case '1':
                    $dtu_status_array[] = array(
                        'id' => '1',
                        'text' => 'Failed'
                    );
                    $dtu_status_array[] = array(
                        'id' => '2',
                        'text' => 'Reloaded'
                    );
                    break;
                case '3':
                    $dtu_status_array[] = array(
                        'id' => '1',
                        'text' => 'Failed'
                    );
                    break;
                case '10':
                    $dtu_status_array[] = array(
                        'id' => '2',
                        'text' => 'Reloaded'
                    );
                    break;
                default:
                    break;
            }

            ?>
            <!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
            <html <?= HTML_PARAMS ?> >
                <head>
                    <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
                    <title><?= TITLE ?></title>
                    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
                    <script language="javascript" src="includes/general.js"></script>
                    <script type="text/javascript" src="includes/javascript/jquery-1.11.1.js"></script>
                </head>
                <body>
                    <center>
                        <table border="1" width="90%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
                            <tr>
                                <td>
                                <?= tep_draw_form("dtu_status_form", FILENAME_API_REPORT, tep_get_all_get_params(array("action")) . 'action=status_change', "post", 'id="dtu_status_form"'); ?>
                                    <table id="remarks_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">
                                        <tr>
                                            <td width="3" style="padding:15px 0px;"></td>
                                            <td class="footerPopupTitle" colspan="4">Change DTU Status:</td>
                                        </tr>
                                        <tr>
                                            <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;">Date:</td>
                                            <td class="main" valign="top"><?=tep_draw_input_field('topup_timestamp', '', 'id="topup_timestamp" value="' . date('Y-m-d') . '" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_status_form.topup_timestamp); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_status_form.topup_timestamp);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;">Time:<br><span style="font-size: 10px">(24 hours)</span></td>
                                            <td class="main" valign="center">
                                                <table>
                                                    <tr>
                                                        <td class="main" valign="center">HH: <?=tep_draw_input_field('topup_hour', '', 'id="topup_hour" size="3" maxlength="2"'); ?></td>
                                                        <td class="main" valign="center">MM: <?=tep_draw_input_field('topup_min', '', 'id="topup_hour" size="3" maxlength="2"'); ?></td>
                                                        <td class="main" valign="center">SS: <?=tep_draw_input_field('topup_sec', '', 'id="topup_hour" size="3" maxlength="2"'); ?></td>       
                                                    </tr>
                                                </table>
                                            </td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;">Status:</td>
                                            <td class="main" valign="top"><?=tep_draw_pull_down_menu('dtu_status', $dtu_status_array, '', ' id="dtu_status" ')?></td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;">Publishers Ref ID:</td>
                                            <td class="main" valign="center"><?=tep_draw_input_field('publishers_ref_id', '', 'id="publishers_ref_id" size="32" maxlength="32"'); ?></td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;">Remarks:</td>
                                            <td colspan="3">
                                                <?= tep_draw_textarea_field('dtu_remarks', 'soft', '50', '5', '', 'id="dtu_remarks"'); ?>
                                            </td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="5">
                                                <div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;"></td>
                                            <td colspan="3">
                                                <div class="green_button_fix_width" id="btn_refund_confirm">
                                                    <a href="javascript:;" class="text-decoration: none;" onclick="submitStatusChange()"><span><font><?= BUTTON_CONFIRM ?></font></span></a>
                                                </div>
                                            </td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                    </table>
                                </form>
                                </td>
                            </tr>
                        </table>
                        <script type="text/javascript">
                            function submitStatusChange() {
                                var date_time = jQuery("#topup_timestamp").val();
                                var status = jQuery("#dtu_status").val();
                                var remarks = jQuery("#dtu_remarks").val();
                                var pub_refid = jQuery("#publishers_ref_id").val();
                                var alert_msg = "Please fill in Time, Status" + (status == '2' && !pub_refid ? ", Publisher Ref ID " : "") + " and Remarks.";

                                if (date_time && status && remarks) {
                                    if (status == '1') {
                                        dtu_status_form.submit();
                                    } else if(pub_refid && status == '2') {
                                        dtu_status_form.submit();
                                    } else {
                                        alert(alert_msg);
                                    }
                                } else {
                                    alert(alert_msg);
                                }
                            }
                        </script>
                    </center>
                </body>
            </html>
            <?php
            exit;
        case 'status_change_result':
            $top_up_id = isset($_GET['top_up_id']) ? (int) $_GET['top_up_id'] : 0;
            $changed_status = isset($_GET['status']) ? $_GET['status'] : 'failed';

            $dtu_title_text = 'Successfully change DTU status';
            if ($changed_status === 'failed') {
                $dtu_title_text = 'Failed to change DTU status';
            }

            // Message stack
            if ($messageStack->size > 0) {
                echo $messageStack->output();
            }

            ?>
            <!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
            <html <?= HTML_PARAMS ?> >
                <head>
                    <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
                    <title><?= TITLE ?></title>
                    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
                    <script language="javascript" src="includes/general.js"></script>
                </head>
                <body>
                    <center>
                        <table border="1" width="90%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
                            <tr>
                                <td>
                                <?= tep_draw_form("dtu_status_form", FILENAME_API_REPORT, tep_get_all_get_params(array("action")) . 'action=status_change', "post", 'id="dtu_status_form"'); ?>
                                    <table id="remarks_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">
                                        <tr>
                                            <td width="3" style="padding:15px 0px;"></td>
                                            <td class="footerPopupTitle" colspan="4"><?= $dtu_title_text; ?></td>
                                        </tr>
                                        <tr>
                                            <td colspan="5">
                                                <div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="3" style="padding:5px 10px;"></td>
                                            <td colspan="3">
                                                <div class="green_button_fix_width" id="btn_refund_confirm">
                                                    <a href="javascript:;" class="text-decoration: none;" onclick="parent.$.fancybox.close();"><span><font><?= BUTTON_OK ?></font></span></a>
                                                </div>
                                            </td>
                                            <td width="3" style="padding:5px 10px;"></td>
                                        </tr>
                                    </table>
                                </form>
                                </td>
                            </tr>
                        </table>
                    </center>
                </body>
            </html>
            <?php
            exit;
        //case 'report':
        default:
            echo '<script type="text/javascript" src="includes/javascript/jquery.js"></script>';
            if (!isset($_REQUEST['report_type']) || !isset($report_type_array[$_REQUEST['report_type']])) {
                $messageStack->add_session(WARNING_API_REPORT_INVALID_REPORT_TYPE, 'warning');
                tep_redirect(tep_href_link(FILENAME_API_REPORT));
            } else {
                $_SESSION['api_report']['report_type'] = $_REQUEST['report_type'];

                switch ($_SESSION['api_report']['report_type']) {
                    case 'cdk_dtu':
                        if ($api_report_export_permission) {
                            if (!isset($_REQUEST['publisher']) || (int) $_REQUEST['publisher'] == 0) {
                                $messageStack->add_session(WARNING_API_REPORT_INVALID_PUBLISHER, 'warning');
                                tep_redirect(tep_href_link(FILENAME_API_REPORT));
                            } else if (!isset($_REQUEST['report_month']) || (int) $_REQUEST['report_month'] == 0) {
                                $messageStack->add_session(WARNING_API_REPORT_INVALID_REPORT_MONTH, 'warning');
                                tep_redirect(tep_href_link(FILENAME_API_REPORT));
                            } else {
                                $multiple_files = array();

                                $_SESSION['api_report']['publisher'] = $_REQUEST['publisher'];
                                $_SESSION['api_report']['report_month'] = $_REQUEST['report_month'];

                                foreach ($_SESSION['api_report']['publisher'] as $publisher_id) {
                                    $publishers_sql = "	SELECT publishers_name
													FROM " . TABLE_PUBLISHERS . "
													WHERE publishers_id = '" . $publisher_id . "'";
                                    $publishers_result = tep_db_query($publishers_sql);
                                    if ($publishers_row = tep_db_fetch_array($publishers_result)) {

                                        $products_array = array();
                                        $top_up_array = array();
                                        $bundles_array = array();
                                        $parent_orders_products_array = array();

                                        $top_up_sql = "	SELECT o.customers_id, o.orders_id, o.remote_addr, op.products_delivered_quantity, op.products_id, otu.top_up_id, op.orders_id, op.parent_orders_products_id, 
														DATE_FORMAT(otu.top_up_timestamp, '%d/%m/%Y') as topup_time, op.final_price 
													FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                                                        INNER JOIN " . TABLE_ORDERS . " AS o
                                                                                                                ON op.orders_id = o.orders_id
													INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
														ON otu.orders_products_id = op.orders_products_id 
													WHERE otu.publishers_id = '" . $publisher_id . "' 
														AND otu.top_up_timestamp >= '" . date("Y-m-01 00:00:00", $_SESSION['api_report']['report_month']) . "' 
														AND otu.top_up_timestamp <= '" . date("Y-m-t 23:59:59", $_SESSION['api_report']['report_month']) . "' 
														AND otu.top_up_status = '3' 
													ORDER BY otu.top_up_id";
                                        $top_up_result = tep_db_query($top_up_sql);
                                        if (tep_db_num_rows($top_up_result)) {
                                            while ($top_up_row = tep_db_fetch_array($top_up_result)) {
                                                $top_up_array[$top_up_row['top_up_id']] = $top_up_row;

                                                if (!isset($products_array[$top_up_row['products_id']])) {
                                                    $products_name = tep_get_products_name($top_up_row['products_id']);
                                                    $products_array[$top_up_row['products_id']] = array('name' => $products_name,
                                                        'qty' => 0,
                                                        'total_qty' => 0,
                                                        'amount' => 0);
                                                }

                                                if ($top_up_row['parent_orders_products_id'] > 0) {
                                                    if (!isset($bundles_array[$top_up_row['parent_orders_products_id']]))
                                                        $bundles_array[$top_up_row['parent_orders_products_id']] = 0;
                                                    $bundles_array[$top_up_row['parent_orders_products_id']] = $bundles_array[$top_up_row['parent_orders_products_id']] + $top_up_row['products_delivered_quantity'];
                                                }
                                            }

                                            $file_location = 'download/api_report_' . $publisher_id . '_' . date('YmdHis') . '.csv';
                                            $report_file = fopen($file_location, 'w');

                                            fputcsv($report_file, array('Publisher', $publishers_row['publishers_name']));
                                            fputcsv($report_file, array('Start', date("Y-m-01 00:00:00", $_SESSION['api_report']['report_month'])));
                                            fputcsv($report_file, array('End', date("Y-m-t 23:59:59", $_SESSION['api_report']['report_month'])));
                                            fputcsv($report_file, array(''));

                                            fputcsv($report_file, array('Date',
                                                'Product ID',
                                                'Product Name',
                                                'Customer ID',
                                                'Order IP Country',
                                                'Order ID',
                                                'Top-up ID',
                                                'Qty',
                                                'SRP',
                                                'Amount'));

                                            $total_top_up_amount = 0;
                                            foreach ($top_up_array as $top_up_id_loop => $top_up_data_loop) {
                                                $qty = $top_up_data_loop['products_delivered_quantity'];

                                                if ($top_up_data_loop['parent_orders_products_id'] == 0) {
                                                    $price = $top_up_data_loop['final_price'];
                                                } else {
                                                    if (!isset($parent_orders_products_array[$top_up_data_loop['parent_orders_products_id']])) {
                                                        $parent_top_up_sql = "	SELECT final_price 
																			FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
																			WHERE op.orders_products_id = '" . $top_up_data_loop['parent_orders_products_id'] . "'";
                                                        $parent_top_up_result = tep_db_query($parent_top_up_sql);
                                                        $parent_top_up_row = tep_db_fetch_array($parent_top_up_result);
                                                        $parent_orders_products_array[$top_up_data_loop['parent_orders_products_id']] = $parent_top_up_row['final_price'];
                                                    }
                                                    $price = ($parent_orders_products_array[$top_up_data_loop['parent_orders_products_id']] * ( $qty / $bundles_array[$top_up_data_loop['parent_orders_products_id']]));
                                                }

                                                $total_amount = $price * $qty;
                                                $total_top_up_amount = $total_top_up_amount + $total_amount;

                                                $products_array[$top_up_data_loop['products_id']]['total_qty'] = $products_array[$top_up_data_loop['products_id']]['total_qty'] + $qty;
                                                $products_array[$top_up_data_loop['products_id']]['qty'] ++;
                                                $products_array[$top_up_data_loop['products_id']]['amount'] = $products_array[$top_up_data_loop['products_id']]['amount'] + $total_amount;

                                                $csv_array = array($top_up_data_loop['topup_time'],
                                                    $top_up_data_loop['products_id'],
                                                    $products_array[$top_up_data_loop['products_id']]['name'],
                                                    $top_up_data_loop['customers_id'],
                                                    $top_up_data_loop['remote_addr'],
                                                    $top_up_data_loop['orders_id'],
                                                    $top_up_id_loop,
                                                    $qty,
                                                    $price,
                                                    $total_amount);
                                                fputcsv($report_file, $csv_array);
                                            }

                                            $csv_array = array('',
                                                '',
                                                '',
                                                '',
                                                '',
                                                '',
                                                'Total',
                                                $total_top_up_amount);
                                            fputcsv($report_file, $csv_array);

                                            // 2 new lines
                                            fputcsv($report_file, array(''));
                                            fputcsv($report_file, array(''));

                                            fputcsv($report_file, array('Product ID',
                                                'Product Name',
                                                'Total Top-up Transaction',
                                                'Total Top-up Qty',
                                                'Total Amount (USD)'));

                                            foreach ($products_array as $products_id_loop => $products_data_loop) {
                                                $csv_array = array($products_id_loop,
                                                    $products_data_loop['name'],
                                                    $products_data_loop['qty'],
                                                    $products_data_loop['total_qty'],
                                                    $products_data_loop['amount']);
                                                fputcsv($report_file, $csv_array);
                                            }
                                            fputcsv($report_file, array(''));

                                            fclose($report_file);


                                            if (file_exists($file_location)) {

                                                $filename = basename($file_location);
                                                $mime_type = 'text/x-csv';

                                                $multiple_files[] = $file_location;
                                                // Download
                                                //header('Content-Type: ' . $mime_type);
                                                //header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
                                           // // IE need specific headers
                                           // if (PMA_USR_BROWSER_AGENT == 'IE') {
                                           //     header('Content-Disposition: attachment; filename="' . $filename . '"');
                                           //     header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                                           //     header('Pragma: public');
                                           // } else {
                                           //     header('Content-Disposition: attachment; filename="' . $filename . '"');
                                           //     header('Pragma: no-cache');
                                           // }
                                                //readfile($file_location);
                                                //echo "<script>window.open('$file_location');</script>";
                                            }
                                        }
                                    } else {
                                        $messageStack->add_session(WARNING_API_REPORT_NO_RECORD, 'warning');
                                        tep_redirect(tep_href_link(FILENAME_API_REPORT));
                                    }
                                }
                            }

                            $_SESSION['api_report_files'] = $multiple_files;

                            foreach ($_SESSION['api_report_files'] as $key => $file) {
                                echo "<script>var winObj_$key  = window.open('api_report_xmlhttp.php?api_report_file_name=" . $key . "', '$key','width=400,height=300,scrollbars=no');</script>";
                                $i++;
                            }
                        } else {
                            $messageStack->add_session(WARNING_API_REPORT_NO_PERMISSION_ALLOWED, 'warning');
                            tep_redirect(tep_href_link(FILENAME_API_REPORT));
                            break;
                        }
                        
                        break;
                    case 'display_cdkey_dtu_api_log':
                        // if (!isset($_REQUEST['publisher'][0]) || (int) $_REQUEST['publisher'][0] == 0) {
                        //     $messageStack->add_session(WARNING_API_REPORT_INVALID_PUBLISHER, 'warning');
                        //     tep_redirect(tep_href_link(FILENAME_API_REPORT));
                        // } else 
                        if (!isset($_REQUEST['report_month']) || (int) $_REQUEST['report_month'] == 0) {
                            $messageStack->add_session(WARNING_API_REPORT_INVALID_REPORT_MONTH, 'warning');
                            tep_redirect(tep_href_link(FILENAME_API_REPORT));
                        } else {
                            // $_SESSION['api_report']['publisher'] = (int) $_REQUEST['publisher'][0];
                            $_SESSION['api_report']['publisher'] = $_REQUEST['publisher'];
                            $_SESSION['api_report']['report_month'] = $_REQUEST['report_month'];
                            $_SESSION['api_report']['sel_status'] = (isset($_REQUEST['sel_status']) ? $_REQUEST['sel_status'] : '0');
                        }
                        break;
                    default:
                        $messageStack->add_session(WARNING_API_REPORT_INVALID_REPORT_TYPE, 'warning');
                        tep_redirect(tep_href_link(FILENAME_API_REPORT));
                        break;
                }
            }
            break;
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/jquery_ui/themes/base/ui.all.css" />
        <link rel="stylesheet" type="text/css" href="includes/javascript/fancybox/jquery.fancybox-1.3.0.css" media="screen" />
        <link rel="stylesheet" type="text/css" href="includes/javascript/jquery_ui_multiselect/jquery.multiselect.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/jquery_ui_multiselect/jquery.multiselect.filter.css">
        <script type="text/javascript" src="includes/javascript/jquery-1.11.1.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery-ui-1.11.4/jquery-ui.min.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script type="text/javascript" src="includes/javascript/fancybox/jquery.fancybox-1.3.0.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery_ui_multiselect/jquery.multiselect.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery_ui_multiselect/jquery.multiselect.filter.js"></script>
        
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?= BOX_WIDTH ?>" valign="top">
                    <table border="0" width="<?= BOX_WIDTH ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <!-- , 'onSubmit="return form_checking();"' -->
                                <?= tep_draw_form('api_report_criteria', FILENAME_API_REPORT, tep_get_all_get_params(array('action')) . 'action=report', 'post'); ?>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td>
                                            <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                                <tr>
                                                    <td class="main"><?= ENTRY_API_REPORT_REPORT_TYPE ?> :</td>
                                                    <td class="main"><?= tep_draw_pull_down_menu('report_type', $report_type_array_sel, $_SESSION['api_report']['report_type'], ' id="report_type" onchange="" ') ?></td>
                                                </tr>
                                                <tbody id="report_type_tbody">
                                                    <tr>
                                                        <td class="main" width="10%"><?= ENTRY_API_REPORT_PUBLISHER ?> :</td>
                                                        <?php
                                                        $publishers_array = array();
                                                        $publishers_array[] = array('id' => '',
                                                            'text' => PULL_DOWN_DEFAULT);
                                                        $publishers_sql = "	SELECT publishers_id, publishers_name
                                                        						FROM " . TABLE_PUBLISHERS . "
                                                        						ORDER BY publishers_name";
                                                        $publishers_result = tep_db_query($publishers_sql);
                                                        while ($publishers_row = tep_db_fetch_array($publishers_result)) {
                                                            $publishers_array[] = array('id' => $publishers_row['publishers_id'],
                                                                'text' => $publishers_row['publishers_name']);
                                                        }

                                                        $months_array = array();
                                                        for ($count_month = 0; $count_month < 24; $count_month++) {
                                                            $current_month = mktime(0, 0, 0, date('m') - $count_month, 1);
                                                            $months_array[] = array('id' => $current_month,
                                                                'text' => date("M Y", $current_month));
                                                        }

                                                        $top_up_status_array = array();
                                                        $top_up_status_array['1'] = 'Pending';
                                                        $top_up_status_array['3'] = 'Completed';
                                                        $top_up_status_array['10'] = 'Failed';
                                                        $top_up_status_array['11'] = 'Not Found';

                                                        if (!isset($_SESSION['api_report']['sel_status'])) {
                                                            $_SESSION['api_report']['sel_status'] = '10';
                                                        }

                                                        $status_array = array();
                                                        foreach ($top_up_status_array as $top_up_status_id_loop => $top_up_status_data_loop) {
                                                            $status_array[] = array('id' => $top_up_status_id_loop,
                                                                'text' => $top_up_status_data_loop);
                                                        }
                                                        ?>
                                                        <td class="main"><?= tep_draw_pull_down_menu("publisher[]", $publishers_array, $_SESSION['api_report']["publisher"], 'style="width: 370px; display: none;" id="publisher" multiple="multiple"') ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main"><?= ENTRY_API_REPORT_MONTH ?> :</td>
                                                        <td class="main"><?= tep_draw_pull_down_menu('report_month', $months_array, $_SESSION['api_report']['report_month'], ' id="report_month"') ?></td>
                                                    </tr>
                                                    <tr id="tr_api_report_status" <?= ($_SESSION['api_report']['report_type'] == 'display_cdkey_dtu_api_log' ? '' : ' style="display:none;" ' ) ?>>
                                                        <td class="main"><?= ENTRY_API_REPORT_STATUS ?> :</td>
                                                        <td class="main"><?= tep_draw_pull_down_menu('sel_status', $status_array, $_SESSION['api_report']['sel_status'], ' id="sel_status" ') ?></td>
                                                    </tr>
                                                </tbody>
                                                <tr>
                                                    <td class="main">
                                                    </td>
                                                    <td class="main">
                                                        <input type="submit" name="Submit" value="Submit" class="inputButton">&nbsp;
                                                        <input type="button" name="reset" value="Reset" class="inputButton" onClick="document.location.href = '<?php echo tep_href_link(FILENAME_API_REPORT, 'action=reset_session'); ?>'">&nbsp;
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main">
                                                    </td>
                                                    <td class="main">

                                                    <?
                                                    switch ($_SESSION['api_report']['report_type']) {
                                                        case 'display_cdkey_dtu_api_log':
                                                            $jquery_iframe_id_array = array();
                                                            $status_change_form_iframe = array();
                                                            foreach ($_SESSION['api_report']['publisher'] as $publisher_id) {
                                                                if ($publisher_id == 0) {
                                                                    continue;
                                                                }
                                                                // get publisher name
                                                                $publisher_sql = " SELECT publishers_id, publishers_name
                                                                                    FROM " . TABLE_PUBLISHERS . "
                                                                                    WHERE publishers_id = '" . (int) $publisher_id . "'";
                                                                $publisher_result = tep_db_query($publisher_sql);
                                                                $publisher_name = ($publisher_row = tep_db_fetch_array($publisher_result)) ? $publisher_row['publishers_name'] : '';

                                                                // get topup info
                                                                $top_up_sql = " SELECT op.products_quantity, op.products_id, op.products_good_delivered_quantity, op.products_canceled_quantity, op.products_reversed_quantity, 
                                                                                    otu.top_up_id, op.orders_id, otu.top_up_status, 
                                                                                    DATE_FORMAT(otu.top_up_timestamp, '%Y-%m-%d %H:%i') as topup_time, otu.top_up_process_flag 
                                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                                INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
                                                                                    ON otu.orders_products_id = op.orders_products_id 
                                                                                WHERE otu.publishers_id = '" . $publisher_id . "' 
                                                                                    AND otu.top_up_timestamp >= '" . date("Y-m-01 00:00:00", $_SESSION['api_report']['report_month']) . "' 
                                                                                    AND otu.top_up_timestamp <= '" . date("Y-m-t 23:59:59", $_SESSION['api_report']['report_month']) . "' ";
                                                                if ($_SESSION['api_report']['sel_status'] > 0) {
                                                                    $top_up_sql .= " AND otu.top_up_status = '" . (int) $_SESSION['api_report']['sel_status'] . "' ";
                                                                }
                                                                $top_up_sql .= " ORDER BY otu.top_up_id ";

                                                                $top_up_result = tep_db_query($top_up_sql);
                                                                ?>
                                                                <br>
                                                                <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                                                    <tr colspan="7">
                                                                        <td><span style="text-decoration: underline;"><?= $publisher_name; ?></span></td>
                                                                    </tr>
                                                                    <?
                                                                    if (tep_db_num_rows($top_up_result)) {
                                                                    ?>
                                                                    
                                                                        <tr>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="100px"><?= TABLE_HEADING_TOP_UP_ID ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="100px"><?= TABLE_HEADING_ORDER_ID ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="100px">Order Status</td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="150px"><?= TABLE_HEADING_TOP_UP_DATE ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="100px"><?= TABLE_HEADING_STATUS ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="*%"><?= TABLE_HEADING_DETAILS ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="100px"><?= TABLE_HEADING_PROCESS ?></td>
                                                                            <td class="reportBoxHeading" style="text-align:center;" width="130px"><?= TABLE_HEADING_ACTION ?></td>
                                                                        </tr>

                                                                        <?
                                                                        $count_row = 0;

                                                                        while ($top_up_row = tep_db_fetch_array($top_up_result)) {
                                                                            $row_style = ($count_row % 2 == 0 ? 'ordersListingEven' : 'ordersListingOdd');

                                                                            // Order Status
                                                                            $order_status = array();
                                                                            if ($top_up_row["products_good_delivered_quantity"] > 0) {
                                                                                if ($top_up_row["products_quantity"] > $top_up_row["products_good_delivered_quantity"]) {
                                                                                    $order_status[] = "Partial Delivered";
                                                                                } else if ($top_up_row["products_quantity"] == $top_up_row["products_good_delivered_quantity"]) {
                                                                                    $order_status[] = "Fully Delivered";
                                                                                }
                                                                            }

                                                                            if ($top_up_row["products_canceled_quantity"] > 0) {
                                                                                if ($top_up_row["products_quantity"] > $top_up_row["products_canceled_quantity"]) {
                                                                                    $order_status[] = "Partial Refund";
                                                                                } else if ($top_up_row["products_quantity"] == $top_up_row["products_canceled_quantity"]) {
                                                                                    $order_status[] = "Fully Refund";
                                                                                }
                                                                            }

                                                                            if ($top_up_row["products_reversed_quantity"] > 0) {
                                                                                if ($top_up_row["products_quantity"] > $top_up_row["products_reversed_quantity"]) {
                                                                                    $order_status[] = "Partial Reversed";
                                                                                } else if ($top_up_row["products_quantity"] == $top_up_row["products_reversed_quantity"]) {
                                                                                    $order_status[] = "Fully Reversed";
                                                                                }
                                                                            }

                                                                            if (!isset($products_array[$top_up_row['products_id']])) {
                                                                                $products_array[$top_up_row['products_id']] = strip_tags(tep_get_products_name($top_up_row['products_id']));
                                                                            }
                                                                            ?>
                                                                            <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, '<?= $row_style ?>')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                                                <td class="main" style="text-align:center;" valign="top"><div id="topup_id"><?= $top_up_row['top_up_id'] ?></div></td>
                                                                                <td class="main" style="text-align:center;" valign="top"><a href="<?= tep_href_link(FILENAME_ORDERS, 'oID=' . $top_up_row['orders_id'] . '&action=edit') ?>" target="_new"><?= $top_up_row['orders_id'] ?></a></td>
                                                                                <td class="main" style="text-align:center;" valign="top"><?= implode(", ", $order_status); ?></td>
                                                                                <td class="main" style="text-align:center;" valign="top"><?= $top_up_row['topup_time'] ?></td>
                                                                                <td class="main" style="text-align:center;" valign="top"><?= (isset($top_up_status_array[$top_up_row['top_up_status']]) ? $top_up_status_array[$top_up_row['top_up_status']] : 'Unknown (' . $top_up_row['top_up_status'] . ')') ?></td>
                                                                                <td class="main" valign="top">
                                                                            <?
                                                                            echo $top_up_row['products_quantity'] . " X " . $products_array[$top_up_row['products_id']] . "<BR>";
                                                                            ?>
                                                                                </td>
                                                                                <td class="main" style="text-align:center;" valign="top">
                                                                                <?php
                                                                                $jquery_iframe_id_array[] = '#vlog' . $top_up_row['top_up_id'];
                                                                                $view_log_url = ' [<a id="vlog' . $top_up_row['top_up_id'] . '" href="' . tep_href_link(FILENAME_API_REPORT, tep_get_all_get_params(array('action')) . 'action=viewlog&top_up_id=' . $top_up_row['top_up_id']) . '" title="Top Up Log ID: ' . $top_up_row['top_up_id'] . '">View Log</a>]';

                                                                                // URL for Status change
                                                                                $status_change_form_iframe[] = '#statusChange' . $top_up_row['top_up_id'];
                                                                                $status_change_url = ' [<a id="statusChange' . $top_up_row['top_up_id'] . '" href="' . tep_href_link(FILENAME_API_REPORT, tep_get_all_get_params(array('action')) . 'action=status_change_form&top_up_id=' . $top_up_row['top_up_id'] . '&top_up_status=' . $top_up_row['top_up_status']) . '" title="Top Up ID: ' . $top_up_row['top_up_id'] . '">Change Status</a>]';

                                                                                switch ($top_up_row['top_up_process_flag']) {
                                                                                    case '1':
                                                                                        echo "Processing";
                                                                                        break;
                                                                                    case '2':
                                                                                        echo "Done";
                                                                                        break;
                                                                                    default:
                                                                                        echo "Pending";
                                                                                        break;
                                                                                }
                                                                                ?>
                                                                                </td>
                                                                                <td class="main" style="text-align:center;" valign="top">
                                                                                    <?php
                                                                                    if ($top_up_row['top_up_process_flag'] == 2) {

                                                                                        switch ($top_up_row['top_up_status']) {
                                                                                            case '1':
                                                                                            case '10':
                                                                                                echo '[<a href="' . tep_href_link(FILENAME_API_REPORT, tep_get_all_get_params(array('action')) . 'action=repost&tID=' . $top_up_row['top_up_id']) . '">Re-post</a>]';
                                                                                                break;
                                                                                            default:

                                                                                                break;
                                                                                        }
                                                                                    }
                                                                                    echo $view_log_url.'<br>';
                                                                                    // Status change
                                                                                    switch ($top_up_row['top_up_status']) {
                                                                                        case '1':
                                                                                        case '3':
                                                                                        case '10':
                                                                                            if ($api_dtu_status_change_permission) {
                                                                                                echo $status_change_url;
                                                                                            }
                                                                                            break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                    ?>
                                                                                </td>
                                                                            </tr>
                                                                            <?
                                                                            $count_row++;
                                                                        }
                                                                        
                                                                    } else {
                                                                        echo "<tr colspan='7'><td>Record not found.</td></tr>";
                                                                    }
                                                                ?>
                                                                </table>
                                                            <?
                                                            }
                                                            break;
                                                        default:
                                                            //
                                                            break;
                                                    }
                                                    ?>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                </form>

                            </td>
                        </tr>
                    </table>
                </td>

                <!-- body_text_eof //-->
            </tr>
        </table>

        <script type="text/javascript">
            jQuery(document).ready(function () {
                if (jQuery("#publisher").length>0) {
                    jQuery('#report_type').on('change', function(){
                        if (jQuery(this).val()=='display_cdkey_dtu_api_log'){
                            jQuery('#tr_api_report_status').show();
                        } else {
                            jQuery('#tr_api_report_status').hide();
                        }
                    });

                    jQuery("#publisher").multiselect().multiselectfilter();
                }
                <?php
                // fancybox for View Log
                if (tep_not_null($jquery_iframe_id_array)) {
                    foreach ($jquery_iframe_id_array as $jquery_iframe_id) {
                        ?>
                        jQuery("<?= $jquery_iframe_id ?>").fancybox({
                            'width': '90%',
                            'height': '50%',
                            'autoScale': false,
                            'transitionIn': 'none',
                            'transitionOut': 'none',
                            'type': 'iframe',
                            'onComplete': function() {
                                jQuery(document).scrollTop(0);
                                jQuery("#fancybox-wrap").css({'top':'200px', 'bottom':'auto'});
                            }
                            // 'centerOnScroll': true
                        });
                        <?php
                    } 
                } 

                // fancybox for Change DTU Status
                if (tep_not_null($status_change_form_iframe)) {
                    foreach ($status_change_form_iframe as $status_change_form_id) {
                        ?>
                        jQuery("<?= $status_change_form_id ?>").fancybox({
                            'type': 'iframe',
                            'onClosed':function () {
                                api_report_criteria.submit();
                            },
                            'onComplete': function() {
                                jQuery(document).scrollTop(0);
                                jQuery("#fancybox-wrap").css({'top':'200px', 'bottom':'auto'});
                            }
                        });
                        <?php 
                    }
                } 
                ?>
            });
        </script>


        <!-- body_eof //-->

        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <br>
    </body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>