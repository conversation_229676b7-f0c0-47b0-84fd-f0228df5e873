<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
include_once('includes/application_top.php');
$file = isset($_GET['api_report_file_name']) ? $_GET['api_report_file_name'] : '';
$get_file = isset($_SESSION['api_report_files'][$file]) ? $_SESSION['api_report_files'][$file] : '';
if ($get_file) {
    $api_report_file_loc = $get_file;
    header("Content-Description: File Transfer");
    header("Content-Type: application/octet-stream");
    header("Content-Disposition: attachment; filename=$get_file");
    readfile($api_report_file_loc);
    unlink($api_report_file_loc);
    unset($_SESSION['api_report_files'][$file]);
    exit;
}
?>