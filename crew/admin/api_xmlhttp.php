<?
include_once('includes/configure.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_CLASSES . 'curl.php');

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

$url = ''; 

$data = $_POST;
if(!empty($data['file'])) {
    $url = tep_api_href_link($data['file'], '', 'SSL');
    unset($data['file']);
}

if(!empty($url)) {
    $curl_obj = new curl();
    
    $response = $curl_obj->curl_post($url, $data);
    
    echo $response;
}
?>