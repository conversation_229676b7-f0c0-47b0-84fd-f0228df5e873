<?
/*
  	$Id: batch_update_prices.php,v 1.15 2007/05/10 09:46:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

$currencies = new currencies();

function filter_selection($var) {
	return ($var && $var >= 1);
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');
$cat_filter_array = array();

unset($_SESSION["s_package_qty"]);	// Used in edit price set page

if (tep_not_null($action)) {
	switch ($action) {
		case "do_update_price":
			$valid_price_set_id_array = array (0);	// 0 = Custom
			$price_set_values_array = array();
			$package_qty_update_array = tep_array_unserialize($HTTP_POST_VARS["serialized_package_qty_array"]);
			
	    	$price_set_select_sql = "SELECT batch_update_price_sets_id FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " ORDER BY batch_update_price_sets_sort_order";
			$price_set_result_sql = tep_db_query($price_set_select_sql);
			while($price_set_row = tep_db_fetch_array($price_set_result_sql)) {
				$valid_price_set_id_array[] = $price_set_row["batch_update_price_sets_id"];
				
				$price_values_set_select_sql = "SELECT batch_update_qty, batch_update_price FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " WHERE batch_update_price_sets_id = '" . tep_db_input($price_set_row["batch_update_price_sets_id"]) . "' AND batch_update_qty IN ('".implode("', '", $package_qty_update_array)."')";
				$price_values_set_result_sql = tep_db_query($price_values_set_select_sql);
				while ($price_values_set_row = tep_db_fetch_array($price_values_set_result_sql)) {
					$price_set_values_array[$price_set_row["batch_update_price_sets_id"]][$price_values_set_row["batch_update_qty"]] = $price_values_set_row["batch_update_price"];
				}
			}
			
			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						$import_error = false;
						$show_ind_packages_price = false;
						
						$filename = ($_FILES['csv_import']['tmp_name']);  
					    $handle = fopen($filename, 'r+');
					    
					    $imported_qty_str_array = array();
					    $valid_package_qty_array = tep_array_unserialize($HTTP_POST_VARS["serialized_package_qty_array"]);
					    
					    // skip first two lines
					    for ($skip_cnt=0; $skip_cnt < 2; $skip_cnt++) {
					    	$data = fgetcsv($handle, 1024, ',', '"');
					    }
					    
					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	if (strtolower(trim($data[0])) != 'category id') {
					    		$messageStack->add_session("Category ID field does not exists!", 'error');
					    		$import_error = true;
					    	}
					    	
					    	if (strtolower(trim($data[1])) != 'category') {
					    		$messageStack->add_session("Category field does not exists!", 'error');
					    		$import_error = true;
					    	}
					    	
					    	if (strtolower(trim($data[2])) != 'available qty') {
					    		$messageStack->add_session("Available Qty field does not exists!", 'error');
					    		$import_error = true;
					    	}
					    	
					    	if (strtolower(trim($data[3])) != 'actual qty') {
					    		$messageStack->add_session("Actual Qty field does not exists!", 'error');
					    		$import_error = true;
					    	}
					    	
					    	if (strtolower(trim($data[4])) != 'price set') {
					    		$messageStack->add_session("Price Set field does not exists!", 'error');
					    		$import_error = true;
					    	}
					    	
					    	for ($i=5; $i < count($data); $i++) {
					    		$imported_qty_str_array[$i] = tep_not_null($data[$i]) ? (int)$data[$i] : 'EMPTY';	// need to convert to integer since using the array_diff
					    	}
					    }
					    
					    if (count($imported_qty_str_array)) {
					    	$show_ind_packages_price = true;
					    	
					    	$outsider_package_qty_array = array_diff($imported_qty_str_array, $valid_package_qty_array);
					    	
					    	if (count($outsider_package_qty_array)) {	// implies there is some package qty which is not in the Package Quantity criteria input
						    	$messageStack->add_session("The following package quantity does not match your Package Quantity criteria: " . implode(', ', $outsider_package_qty_array), 'error');
						    	$import_error = true;
						    }
					    }
					    /*
					    if (!count($imported_qty_str_array)) {
					    	$messageStack->add_session("There is no any package quantity information!", 'error');
					    	$import_error = true;
					    }
					    */
					    if (!$import_error) {
					    	$imported_qty_str_array = $show_ind_packages_price ? array_unique($imported_qty_str_array) : $valid_package_qty_array;
					    	
					    	$main_cat_name = tep_get_category_name($_SESSION['batch_update_price_param']["cat_id"], $languages_id);
					    	// Get all the subcategories for the selected category
					    	$category_array = array($_SESSION['batch_update_price_param']["cat_id"]);
					    	tep_get_subcategories($category_array, $_SESSION['batch_update_price_param']["cat_id"]);
					    	
					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    			$cat_id_str = $data[0];
				    			
				    			if (trim($cat_id_str) == '') {	// Assume this row is useless
				    				continue;
				    			}
				    			
								$price_set_id_str = $data[4];
								$price_values_array = array();
								
								if (trim($cat_id_str) == '' || !in_array((int)$cat_id_str, $category_array)) {
					    			$messageStack->add_session("Category ID: " . $cat_id_str . " does not under " . $main_cat_name . "!" , 'error');
					    			$import_error = true;
					    		} else if (tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES, (int)$cat_id_str) != 1) {
					    			$messageStack->add_session(sprintf(ERROR_UPDATE_ACCESS_DENIED_CAT, $cat_id_str), 'error');
					    			$import_error = true;
					    		}
					    		
					    		if (trim($price_set_id_str) == '' || !in_array($price_set_id_str, $valid_price_set_id_array)) {
					    			$messageStack->add_session("Price Set ID: " . $price_set_id_str . " does not exists!" , 'error');
					    			$import_error = true;
					    		}
					    		
					    		if ($show_ind_packages_price && $price_set_id_str == '0') {
									foreach ($imported_qty_str_array as $index => $qty) {
										$data[$index] = trim($data[$index]);
							    		$price_values_array[$qty] = array ('qty'=>$qty, 'price'=>$data[$index]);
							    		
							    		if ($data[$index] != 'Not Available' && $data[$index] != 'Inactive' && !preg_match('/[0-9]+\.?[0-9]*/is', $data[$index])) {
							    			$messageStack->add_session("Price (".$data[$index].") for category with ID: " . (int)$cat_id_str . " must be a currency value!" , 'error');
							    			$import_error = true;
							    		}
							    	}
						    	}
						    	
					    		$imported_categories_array[] = array(	'cat_id' => (int)$cat_id_str,
						    											'price_set_id' => $price_set_id_str,
						    											'packages_price' => $price_values_array
						    										);
							}
					    	
					    	fclose($handle);
					    	
					    	if ($import_error) {
					    		$messageStack->add_session("No data is imported! Please fix the above error(s) and import your file again.", 'error');
					    		tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES, 'action=list_template&cont=1'));
					    	}
					    } else {
					    	fclose($handle);
					    	$messageStack->add_session("No data is imported! Please fix the above error(s) and import your file again.", 'error');
							tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES, 'action=list_template&cont=1'));
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES));
				}
			} else if ($HTTP_POST_VARS['btn_csv_export']) {
				$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
				$export_package_qty_array = tep_array_unserialize($HTTP_POST_VARS["serialized_package_qty_array"]);
				
				$export_csv_data = '';
				if (count($export_csv_array)) {
        			$export_csv_data = '"' . str_replace('"', '""', "Product") . '", ' . str_replace('"', '""', $_SESSION['batch_update_price_param']["product_name"]) . "\n";
        			$export_csv_data .= '"' . str_replace('"', '""', "Category") . '", ' . str_replace('"', '""', tep_get_category_name($_SESSION['batch_update_price_param']["cat_id"], $languages_id)) . "\n";
					$export_csv_data .= "Category ID, Category, Available Qty, Actual Qty, Price Set";
					
					if (isset($_POST['show_ind_packages_price']) && $_POST['show_ind_packages_price'] == '1') {
						for ($i=0; $i < count($export_package_qty_array); $i++) {
							$export_csv_data .= ', ' . $export_package_qty_array[$i];
						}
					}
					$export_csv_data .= "\n";
					
					foreach ($export_csv_array as $cat_id => $res) {
						$cat_id = (int)$cat_id;
						$cat_name = '"' . str_replace('"', '""', $res['cat_name']) . '"';
						$available_qty = '"' . str_replace('"', '""', $res["available_qty"]) . '"';
						$actual_qty = '"' . str_replace('"', '""', $res["actual_qty"]) . '"';
						$price_set_id = '"' . str_replace('"', '""', $res['set_id']) . '"';
						
						$export_csv_data .= "$cat_id,$cat_name,$available_qty,$actual_qty,$price_set_id";
						
						if (isset($_POST['show_ind_packages_price']) && $_POST['show_ind_packages_price'] == '1') {
							for ($i=0; $i < count($export_package_qty_array); $i++) {
								$export_csv_data .= ',' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res['qty_str'][$export_package_qty_array[$i]]);
							}
						}
						$export_csv_data .= "\n";
					}
				}
				
				if (tep_not_null($export_csv_data)) {
					$filename = 'batch_update_price_'.date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES));
				}
			} else if (isset($HTTP_POST_VARS['btn_db_update'])) {	//	The actual update of those packages prices
				$update_error = false;
				$cat_2_price_set_array = array();
				$product_price_update_sql = array();
				
				if (count($HTTP_POST_VARS["price_set"])) {
					foreach ($HTTP_POST_VARS["price_set"] as $this_cat_id => $this_price_set) {
						$cat_2_price_set_array[$this_cat_id] = is_numeric($this_price_set) ? $this_price_set : 0;
						
						if (!in_array($cat_2_price_set_array[$this_cat_id], $valid_price_set_id_array)) {
							$cat_name = tep_get_category_name($this_cat_id, $languages_id);
							$messageStack->add("Price Set ID (".$cat_2_price_set_array[$this_cat_id].") for category with ID: " . $cat_name . " does not exists!", 'error');
							$update_error = true;
						}
					}
					
					$cat_filter_array = array_keys($cat_2_price_set_array);
				}
				
				if (count($HTTP_POST_VARS["update_price"])) {
					foreach ($HTTP_POST_VARS["update_price"] as $this_cat_id => $price_res) {
						if (count($price_res)) {
							foreach ($price_res as $prod_id => $ind_price) {
								if (!is_numeric($ind_price)) {
									$cat_name = tep_get_category_name($this_cat_id, $languages_id);
									$messageStack->add("Price (".$ind_price.") for category with ID: " . $cat_name . " must be a currency value!" , 'error');
									$update_error = true;
								}
								
								if (tep_not_null($cat_2_price_set_array[$this_cat_id]) && $cat_2_price_set_array[$this_cat_id] != 0) {
									$bundle_qty_select_sql = "SELECT subproduct_qty FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = '" . (int)$prod_id . "'";
									$bundle_qty_result_sql = tep_db_query($bundle_qty_select_sql);
									$bundle_qty_row = tep_db_fetch_array($bundle_qty_result_sql);
									if (isset($price_set_values_array[$cat_2_price_set_array[$this_cat_id]][$bundle_qty_row["subproduct_qty"]])) {
										$ind_price = $price_set_values_array[$cat_2_price_set_array[$this_cat_id]][$bundle_qty_row["subproduct_qty"]];
										$product_price_update_sql[] = "UPDATE " . TABLE_PRODUCTS . " SET products_price ='" . (double)$ind_price . "' WHERE products_id = '" . (int)$prod_id . "'; ";
									} else {
										$product_price_update_sql[] = "UPDATE " . TABLE_PRODUCTS . " SET products_price ='" . (double)$ind_price . "' WHERE products_id = '" . (int)$prod_id . "'; ";
									}
								} else {
									$product_price_update_sql[] = "UPDATE " . TABLE_PRODUCTS . " SET products_price ='" . (double)$ind_price . "' WHERE products_id = '" . (int)$prod_id . "'; ";
								}
							}
						}
					}
				}
				
				if (!$update_error) {
					if (count($product_price_update_sql)) {
						foreach ($product_price_update_sql as $price_update_sql) {
							tep_db_query($price_update_sql);
						}
					}
					
					if (count($cat_2_price_set_array)) {
						tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TO_PRICE_SETS . " WHERE categories_id IN ('" . implode("', '", array_keys($cat_2_price_set_array)) . "') ");
						
						foreach ($cat_2_price_set_array as $this_cat_id => $this_price_set) {
							if ($this_price_set != 0) {
								$sql_data_array = array('categories_id' => $this_cat_id,
			                                   			'batch_update_price_sets_id' => $this_price_set
			                                   			);
								
								tep_db_perform(TABLE_CATEGORIES_TO_PRICE_SETS, $sql_data_array);
							}
						}
					}
					
					$messageStack->add_session("Successfully batch update of products prices.", 'success');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES, 'action=list_template&cont=1'));
				} else {
					$messageStack->add("Products prices are not updated! Please fix the above error(s) and update again.", 'error');
				}
			}
			break;
		case "list_template":
			if ($_REQUEST["cont"] != '1') {
				if (tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES, (int)$_REQUEST["cat_id"]) > 0) {
					$_SESSION['batch_update_price_param']["product_name"] = tep_db_prepare_input($_REQUEST["product_name"]);
					$_SESSION['batch_update_price_param']["cat_id"] = (int)$_REQUEST["cat_id"];
					$_SESSION['batch_update_price_param']["qty_str"] = tep_db_prepare_input($_REQUEST["package_quantities"]);
				} else {
					$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
					tep_redirect( tep_href_link(FILENAME_BATCH_UPDATE_PRICES, tep_get_all_get_params(array('action'))) );
				}
			}
			break;
		case "reset_session":
        	unset($_SESSION['batch_update_price_param']);
        	tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES));
        	break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/product_listing.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "list_template" || $action == "do_update_price" || isset($HTTP_POST_VARS['btn_csv_import'])) {
	$export_csv_array = array();
	$default_package_info_array = array();
	$selected_price_set_values_array = array();
	
	$match_product = $_SESSION['batch_update_price_param']["product_name"];
	
	if (isset($HTTP_POST_VARS['btn_csv_import']) && $action == "do_update_price") {	// Use the one specified in csv file
		$package_qty_array = array();
		if (count($imported_qty_str_array)) {
			foreach ($imported_qty_str_array as $this_qty_str) {
				$package_qty_array[] = $this_qty_str;
			}
		}
	} else {
		if (tep_not_null($_SESSION['batch_update_price_param']["qty_str"])) {
			$package_qty_array = explode(';', $_SESSION['batch_update_price_param']["qty_str"]);
			$package_qty_array = array_filter($package_qty_array, "filter_empty_val");
			$package_qty_array = array_unique($package_qty_array);
		} else {
			$package_qty_array = array();
		}
	}
	
	if (count($package_qty_array)) {
		foreach ($package_qty_array as $key => $qty_string) {
			$package_qty_array[$key] = (int)$qty_string;
			$default_package_info_array[(int)$qty_string]["current"] = 'Not<br>Available';
		}
	}
	
	$price_set_array = array( array('id'=>'0', 'text'=>'Custom') );
	$price_set_select_sql = "SELECT batch_update_price_sets_id, batch_update_price_sets_name FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " ORDER BY batch_update_price_sets_sort_order, batch_update_price_sets_name";
	$price_set_result_sql = tep_db_query($price_set_select_sql);
	while($price_set_row = tep_db_fetch_array($price_set_result_sql)) {
		$price_set_array[] = array('id'=>$price_set_row["batch_update_price_sets_id"], 'text'=>$price_set_row["batch_update_price_sets_name"] . '(' .$price_set_row["batch_update_price_sets_id"]. ')');
		
		$price_values_set_select_sql = "SELECT batch_update_qty, batch_update_price FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " WHERE batch_update_price_sets_id = '" . tep_db_input($price_set_row["batch_update_price_sets_id"]) . "' AND batch_update_qty IN ('".implode("', '", $package_qty_array)."')";
		$price_values_set_result_sql = tep_db_query($price_values_set_select_sql);
		while ($price_values_set_row = tep_db_fetch_array($price_values_set_result_sql)) {
			$selected_price_set_values_array[$price_set_row["batch_update_price_sets_id"]][$price_values_set_row["batch_update_qty"]] = $price_values_set_row["batch_update_price"];
		}
	}
	
	$price_set_array[] = array('id'=>'comment', 'text'=>'----------', 'param'=>'DISABLED=true');
	$price_set_array[] = array('id'=>'nps', 'text'=>'New price set ...');
	$price_set_array[] = array('id'=>'eps', 'text'=>'Edit price set ...');
	$price_set_array[] = array('id'=>'rps', 'text'=>'Refresh price set ...');
	
	$row_count = 0;
	if (isset($HTTP_POST_VARS['btn_csv_import']) && $action == "do_update_price") {
		function tep_show_list_items($ImportedArray, $Path, $MatchName) {
			global $languages_id, $package_qty_array, $default_package_info_array, $row_count, $price_set_array, $selected_price_set_values_array, $export_csv_array;
			
			for ($cat_cnt=0; $cat_cnt < count($ImportedArray); $cat_cnt++) {
				$catInfo = new objectInfo($ImportedArray[$cat_cnt]);
				
				$product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_actual_quantity 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON p2c.products_id=p.products_id 
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p2c.products_id=pd.products_id 
										WHERE p2c.categories_id = '" . $catInfo->cat_id . "'
											AND pd.products_name = '".tep_db_input($MatchName)."' 
											AND pd.language_id='".(int)$languages_id."' 
											AND p2c.products_is_link=0 
											AND p.products_bundle = '' 
											AND p.products_bundle_dynamic = '' ";
				$product_result_sql = tep_db_query($product_select_sql);
				if ($product_row = tep_db_fetch_array($product_result_sql)) {
					$DisplayName = tep_output_generated_category_path_sq($catInfo->cat_id);
					if (tep_not_null($Path) && strpos($DisplayName, $Path) === 0) {
						$DisplayName = trim(substr($DisplayName, strlen($Path)));
						
						if (strpos($DisplayName, '>') === 0)	$DisplayName = trim(substr($DisplayName, 1));
					}
						
					$package_pricing_array = $default_package_info_array;
					$active_package_array = array();
					
					$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
					
					$cat_price_set_select_sql = "	SELECT c2ps.batch_update_price_sets_id, bps.batch_update_price_sets_name 
													FROM " . TABLE_CATEGORIES . " AS c 
													LEFT JOIN " . TABLE_CATEGORIES_TO_PRICE_SETS . " AS c2ps 
														ON c.categories_id = c2ps.categories_id 
													LEFT JOIN " . TABLE_BATCH_UPDATE_PRICE_SETS . " AS bps 
														ON c2ps.batch_update_price_sets_id = bps.batch_update_price_sets_id 
													WHERE c.categories_id='" . $catInfo->cat_id . "'" ;
					$cat_price_set_result_sql = tep_db_query($cat_price_set_select_sql);
					$cat_price_set_row = tep_db_fetch_array($cat_price_set_result_sql);
					
					//	Existing price set used by this category
					if (tep_not_null($cat_price_set_row["batch_update_price_sets_id"])) {
						$price_set_col_html = $cat_price_set_row["batch_update_price_sets_name"] . ' (' . $cat_price_set_row["batch_update_price_sets_id"] . ')<br>';
					} else {
						$price_set_col_html = 'Custom<br>';
					}
					
					$product_bundle_select_sql = "	SELECT p.products_id, p.products_price, p.products_status, pb.subproduct_qty, COUNT(pb.subproduct_id) AS total_subproduct 
													FROM " . TABLE_PRODUCTS . " AS p 
													INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
														ON p.products_id=p2c.products_id 
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
														ON p2c.products_id=pb.bundle_id 
													WHERE p2c.categories_id = '" . $catInfo->cat_id . "'
														AND p2c.products_is_link=0 
														AND pb.subproduct_qty IN ('".implode("', '", $package_qty_array)."') 
														AND (p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes') 
													GROUP BY p.products_id
													HAVING total_subproduct = 1 ";
					$product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
					while ($product_bundle_row = tep_db_fetch_array($product_bundle_result_sql)) {
						if ($product_bundle_row["products_status"] == '1') {
							$package_pricing_array[$product_bundle_row["subproduct_qty"]]["current"] = $product_bundle_row["products_price"];
						} else {
							$package_pricing_array[$product_bundle_row["subproduct_qty"]]["current"] = '<span title="Inactive" class="redIndicator">'.$product_bundle_row["products_price"].'</span>';
						}
						$price_to_use = $catInfo->price_set_id > 0 ? $selected_price_set_values_array[$catInfo->price_set_id][$product_bundle_row["subproduct_qty"]] : $catInfo->packages_price[$product_bundle_row["subproduct_qty"]]['price'];
						//$price_to_use = $catInfo->price_set_id > 0 ? $selected_price_set_values_array[$catInfo->price_set_id][$product_bundle_row["subproduct_qty"]] : $product_bundle_row["products_price"];
						if (is_numeric($price_to_use)) {
							$package_pricing_array[$product_bundle_row["subproduct_qty"]]["provision"] = tep_draw_input_field('update_price['.$catInfo->cat_id.']['.$product_bundle_row["products_id"].']', $price_to_use, 'id="update_price_'.$product_bundle_row["products_id"].'" size="10" align="right" onKeyUp="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onBlur=" if (parseFloat(this.value)>parseFloat('.(tep_not_null($price_to_use) ? $price_to_use : 0).') || parseFloat(this.value)<parseFloat('.(tep_not_null($price_to_use) ? $price_to_use : 0).')) { DOMCall(\'price_set_'.$catInfo->cat_id.'\').selectedIndex=0 }" ');
						} else {
							$package_pricing_array[$product_bundle_row["subproduct_qty"]]["provision"] = $price_to_use;
						}
						$active_package_array[] = $product_bundle_row["products_id"];
					}
					
					// Cannot move up coz we need the $active_package_array
					if (count($active_package_array)) {
						$price_set_col_html .= tep_draw_pull_down_menu("price_set[".$catInfo->cat_id."]", $price_set_array, $catInfo->price_set_id, ' id="price_set_'.$catInfo->cat_id.'" onChange="priceSetOptions(this, \''.tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'qty_str='.implode(';', $package_qty_array)).'\', \''.implode(',', $active_package_array).'\');"');
					}
					
					$price_set_body = '';
					foreach ($package_qty_array as $qty_string) {
						$price_set_body .= '<td class="reportRecords" align="'.(isset($package_pricing_array[$qty_string]["provision"]) ? 'right' : 'center').'" valign="top">'.$package_pricing_array[$qty_string]["current"].'<br>'.$package_pricing_array[$qty_string]["provision"].'</td>';
						$price_set_body .= '<td width="1px" class="reportRecords">&nbsp;</td>';
						$export_csv_array[$catInfo->cat_id]['qty_str'][$qty_string] = str_replace('<br>', ' ', preg_replace("'<span[^>]*?>(.*?)</span>'is", "\\1", $package_pricing_array[$qty_string]["current"]));
					}
					
					$available_qty = number_format($product_row["products_quantity"], 0, '.', ',');
					$actual_qty = number_format($product_row["products_actual_quantity"], 0, '.', ',');
					
					$export_csv_array[$catInfo->cat_id]['cat_name'] = $DisplayName;
					$export_csv_array[$catInfo->cat_id]['available_qty'] = $available_qty;
					$export_csv_array[$catInfo->cat_id]['actual_qty'] = $actual_qty;
					$export_csv_array[$catInfo->cat_id]['set_id'] = tep_not_null($cat_price_set_row["batch_update_price_sets_id"]) ? $cat_price_set_row["batch_update_price_sets_id"] : 0;
					
					echo '	<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<!--td class="reportRecords" valign="top">'.$catInfo->cat_id.'</td-->
								<td class="reportRecords" valign="top">'.$DisplayName.'</td>
								<td class="reportRecords" align="right" valign="top">'.$available_qty.'</td>
								<td class="reportRecords" align="right" valign="top">'.$actual_qty.'</td>
								<td class="reportRecords" align="center" valign="top">'.$price_set_col_html.'</td>';
					echo $price_set_body;
					echo '	</tr>';
					$row_count++;
				}
			}
		}
	} else {
		function tep_show_list_items($ListItems, $Path, $MatchName, $CatFilterLists) {
			global $languages_id, $package_qty_array, $row_count, $default_package_info_array, $price_set_array, $export_csv_array;
			global $selected_price_set_values_array;
			
			foreach ($ListItems as $ListItem)
			{
				$NewListItems = array() ;
				
				$p_rank = tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES, $ListItem["categories_id"]);
			
				if ($p_rank > 0) {
					$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name, c2ps.batch_update_price_sets_id, bps.batch_update_price_sets_name 
										FROM " . TABLE_CATEGORIES . " AS c 
										INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
											ON c.categories_id = cd.categories_id 
										LEFT JOIN " . TABLE_CATEGORIES_TO_PRICE_SETS . " AS c2ps 
											ON c.categories_id = c2ps.categories_id 
										LEFT JOIN " . TABLE_BATCH_UPDATE_PRICE_SETS . " AS bps 
											ON c2ps.batch_update_price_sets_id = bps.batch_update_price_sets_id 
										WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
											AND cd.language_id='" . (int)$languages_id ."' 
										ORDER BY sort_order, cd.categories_name " ;
					$cat_result_sql = tep_db_query($cat_select_sql);
					while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
						$NewListItems[] = $cat_row ;
					}
					
					$DisplayName = strip_tags($Path . (tep_not_null($Path) && tep_not_null($ListItem["categories_name"]) ? ' > ' : '') . $ListItem["categories_name"]);
				    if (count($CatFilterLists) > 0) {
						$proceed = (in_array($ListItem["categories_id"], $CatFilterLists)) ? true : false;
					} else {
						$proceed = true;
					}
					
					if ($proceed && $p_rank != 1)	$proceed = false;
					
					if ($proceed) {
					    $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_actual_quantity 
												FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
													ON p.products_id=p2c.products_id 
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON p2c.products_id=pd.products_id 
												WHERE p2c.categories_id = '" . $ListItem["categories_id"] . "'
													AND pd.products_name = '".tep_db_input($MatchName)."' 
													AND pd.language_id='".(int)$languages_id."' 
													AND p2c.products_is_link=0 
													AND p.products_bundle = '' 
													AND p.products_bundle_dynamic = '' ";
						$product_result_sql = tep_db_query($product_select_sql);
						
						if ($product_row = tep_db_fetch_array($product_result_sql)) {
							$package_pricing_array = $default_package_info_array;
							$active_package_array = array();
							
							$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
							
							if (tep_not_null($ListItem["batch_update_price_sets_id"])) {
								$price_set_col_html = $ListItem["batch_update_price_sets_name"] . ' (' . $ListItem["batch_update_price_sets_id"] . ')<br>';
							} else {
								$price_set_col_html = 'Custom<br>';
							}
							
							$product_bundle_select_sql = "	SELECT p.products_id, p.products_price, p.products_status, pb.subproduct_qty, COUNT(pb.subproduct_id) AS total_subproduct 
															FROM " . TABLE_PRODUCTS . " AS p 
															INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
																ON p.products_id=p2c.products_id 
															INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
																ON p2c.products_id=pb.bundle_id 
															WHERE p2c.categories_id = '" . $ListItem["categories_id"] . "'
																AND p2c.products_is_link=0 
																AND pb.subproduct_qty IN ('".implode("', '", $package_qty_array)."') 
																AND (p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes') 
															GROUP BY p.products_id
															HAVING total_subproduct = 1 ";
							$product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
							while ($product_bundle_row = tep_db_fetch_array($product_bundle_result_sql)) {
								if ($product_bundle_row["products_status"] == '1') {
									$package_pricing_array[$product_bundle_row["subproduct_qty"]]["current"] = $product_bundle_row["products_price"];
								} else {
									$package_pricing_array[$product_bundle_row["subproduct_qty"]]["current"] = '<span title="Inactive" class="redIndicator">'.$product_bundle_row["products_price"].'</span>';
								}	
								
								$price_to_use = tep_not_null($ListItem["batch_update_price_sets_id"]) ? $selected_price_set_values_array[$ListItem["batch_update_price_sets_id"]][$product_bundle_row["subproduct_qty"]] : $product_bundle_row["products_price"];
								$package_pricing_array[$product_bundle_row["subproduct_qty"]]["provision"] = tep_draw_input_field('update_price['.$ListItem["categories_id"].']['.$product_bundle_row["products_id"].']', $price_to_use, 'id="update_price_'.$product_bundle_row["products_id"].'" size="10" align="right" onKeyUp="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onBlur=" if (parseFloat(this.value)>parseFloat('.(tep_not_null($price_to_use) ? $price_to_use : 0).') || parseFloat(this.value)<parseFloat('.(tep_not_null($price_to_use) ? $price_to_use : 0).')) { DOMCall(\'price_set_'.$ListItem["categories_id"].'\').selectedIndex=0 }" ');
								$active_package_array[] = $product_bundle_row["products_id"];
							}
							// Cannot move up coz we need the $active_package_array
							if (count($active_package_array)) {
								$price_set_col_html .= tep_draw_pull_down_menu("price_set[".$ListItem["categories_id"]."]", $price_set_array, $ListItem["batch_update_price_sets_id"], ' id="price_set_'.$ListItem["categories_id"].'" onChange="priceSetOptions(this, \''.tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'qty_str='.implode(';', $package_qty_array)).'\', \''.implode(',', $active_package_array).'\');"');
							}
							
							$price_set_body = '';
							foreach ($package_qty_array as $qty_string) {
								$price_set_body .= '<td class="reportRecords" align="'.(isset($package_pricing_array[$qty_string]["provision"]) ? 'right' : 'center').'" valign="top">'.$package_pricing_array[$qty_string]["current"].'<br>'.$package_pricing_array[$qty_string]["provision"].'</td>';
								$price_set_body .= '<td width="1px" class="reportRecords">&nbsp;</td>';
								$export_csv_array[$ListItem["categories_id"]]['qty_str'][$qty_string] = str_replace('<br>', ' ', preg_replace("'<span[^>]*?>(.*?)</span>'is", "\\1", $package_pricing_array[$qty_string]["current"]));
							}
							
							$available_qty = number_format($product_row["products_quantity"], 0, '.', ',');
							$actual_qty = number_format($product_row["products_actual_quantity"], 0, '.', ',');
							
							$export_csv_array[$ListItem["categories_id"]]['cat_name'] = $DisplayName;
							$export_csv_array[$ListItem["categories_id"]]['available_qty'] = $available_qty;
							$export_csv_array[$ListItem["categories_id"]]['actual_qty'] = $actual_qty;
							$export_csv_array[$ListItem["categories_id"]]['set_id'] = tep_not_null($ListItem["batch_update_price_sets_id"]) ? $ListItem["batch_update_price_sets_id"] : 0;
							
							echo '	<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
										<!--td class="reportRecords" valign="top">'.$ListItem["categories_id"].'</td-->
										<td class="reportRecords" valign="top">'.$DisplayName.'</td>
										<td class="reportRecords" align="right" valign="top">'.$available_qty.'</td>
										<td class="reportRecords" align="right" valign="top">'.$actual_qty.'</td>
										<td class="reportRecords" align="center" valign="top">'.$price_set_col_html.'</td>';
							echo $price_set_body;
							echo '	</tr>';
							$row_count++;
						}
				    }
				    
					tep_show_list_items($NewListItems, $DisplayName, $MatchName, $CatFilterLists);
				}
			}
			return true;
		}
	}
	
	$ListItems = array() ;
	
	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name, c2ps.batch_update_price_sets_id, bps.batch_update_price_sets_name 
						FROM " . TABLE_CATEGORIES . " AS c 
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
							ON c.categories_id = cd.categories_id 
						LEFT JOIN " . TABLE_CATEGORIES_TO_PRICE_SETS . " AS c2ps 
							ON c.categories_id = c2ps.categories_id 
						LEFT JOIN " . TABLE_BATCH_UPDATE_PRICE_SETS . " AS bps 
							ON c2ps.batch_update_price_sets_id = bps.batch_update_price_sets_id 
						WHERE c.categories_id='" . $_SESSION['batch_update_price_param']["cat_id"] . "' 
							AND cd.language_id='" . (int)$languages_id ."' 
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);
	
	if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
		$parent_id = $cat_row['parent_id'];
	}
?>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
        							<td class="main" colspan="2"><?=TEXT_REFRESH_PRICE_SET?></td>
        						</tr>
        						<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
        						<tr>
        							<td class="main" width="15%"><?=TEXT_MATCHED_PRODUCT_NAME?></td>
        							<td class="main"><?=$match_product?></td>
        						</tr>
        						<tr>
        							<td class="main"><?=TEXT_MATCHED_CATEGORY_NAME?></td>
        							<td class="main"><?=tep_output_generated_category_path_sq($_SESSION['batch_update_price_param']["cat_id"])?></td>
        						</tr>
        					</table>
						</td>
					</tr>
					<tr>
						<td>
<?	echo tep_draw_form('template_listing_form', FILENAME_BATCH_UPDATE_PRICES, tep_get_all_get_params(array('action')) . 'action=do_update_price', 'post', 'onSubmit="return template_listing_form_checking();"'); ?>
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
        						<tr>
			       					<td class="reportBoxHeading"><?=TABLE_HEADING_CATEGORY?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_AVAILABLE_QTY?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_ACTUAL_QTY?></td>
					                <td align="center" class="reportBoxHeading">
					                	<?=TABLE_HEADING_PRICE_SET?><br><a href="javascript:;" onClick="refreshPriceSetOptions('template_listing_form', 'price_set');" class="highlightLink">Refresh Price Set</a>
					                </td>
<?	foreach ($package_qty_array as $qty_string) {
		echo '<td align="center" width="6%" class="reportBoxHeading">'.$qty_string.'</td>';
		echo '<td width="1px" class="reportBoxHeading">&nbsp;</td>';
	}
?>
			   					</tr>
<?	if (isset($HTTP_POST_VARS['btn_csv_import']) && $action == "do_update_price") {
		tep_show_list_items($imported_categories_array, tep_output_generated_category_path_sq($parent_id), $match_product);
	} else {
		tep_show_list_items($ListItems, '', $match_product, $cat_filter_array);
	}
?>
								<tr>
									<td colspan="<?=(4 + count($package_qty_array)*2)?>" align="left">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td align="left">
													<?=tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_BATCH_UPDATE_PRICES), '', 'inputButton')?>
												</td>
												<td align="right">
													<input type="submit" name="btn_db_update" value="<?=IMAGE_UPDATE?>" title="<?=IMAGE_UPDATE?>" class="inputButton" onClick="return confirm_action('Are you sure to perform this batch update of products price?')">
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							<?=tep_draw_hidden_field("serialized_package_qty_array", tep_array_serialize($package_qty_array)) . "\n"?>
							</form>
						</td>
					</tr>
					<tr>
						<td>
<?
	echo tep_draw_form('price_list_csv_form', FILENAME_BATCH_UPDATE_PRICES, tep_get_all_get_params(array('action')) . 'action=do_update_price', 'post', 'enctype="multipart/form-data"');
	
	echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
	echo tep_draw_hidden_field("serialized_package_qty_array", tep_array_serialize($package_qty_array)) . "\n";
	echo tep_draw_hidden_field("show_ind_packages_price", '0') . "\n";
?>
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
								<tr>
									<td width="50%" align="left">
										<?=tep_draw_file_field('csv_import', 'size="50"')?>
										<input type="submit" name="btn_csv_import" value="Import" title="Import csv file" class="inputButton">
									</td>
									<td width="50%" align="right">
										<input type="submit" name="btn_csv_export" value="Export Template" title="Export as csv file" class="inputButton" onClick="if (confirm_action('Do you want to show the price for each package quantties as well in the CSV file?')) { document.price_list_csv_form.show_ind_packages_price.value='1'; } else { document.price_list_csv_form.show_ind_packages_price.value='0'; }">
									</td>
								</tr>
							</table>
						</form>
						</td>
					</tr>
					<script language="javascript"><!--
						function template_listing_form_checking() {
							return true;
						}
						//-->
					</script>
    			</table>
<?
} else {
	$categories_array = tep_get_eligible_category_tree(FILENAME_BATCH_UPDATE_PRICES, 0, '___', '', $categories_array, false, 0, true);
	
	$cat_cfg_array = tep_get_cfg_setting((int)$_SESSION['batch_update_price_param']["cat_id"], 'catalog', '9', 'group_id');	// Get configuration values for Stock Options group
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('product_selection_form', FILENAME_BATCH_UPDATE_PRICES, tep_get_all_get_params(array('action')) . 'action=list_template', 'post', 'onSubmit="return product_selection_form_checking();"'); ?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="15%"><?=ENTRY_CATEGORY?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, isset($_SESSION['batch_update_price_param']["cat_id"]) ? $_SESSION['batch_update_price_param']["cat_id"] : '', ' id="cat_id" onChange="getCatPackageQuantitySetting(this, \'product_name\', \'package_quantities\')"')?>
					    			</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="15%" valign="top"><?=ENTRY_PRODUCT_NAME?></td>
									<td class="main">
										<?=tep_draw_input_field('product_name', isset($_SESSION['batch_update_price_param']["product_name"]) ? $_SESSION['batch_update_price_param']["product_name"] : $cat_cfg_array['PRODUCT_NAME_FOR_BATCH_UPDATE'], 'size="40" id="product_name"') . '&nbsp;' . TEXT_MATCH_PRODUCT_CRITERIA?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" valign="top"><?=ENTRY_PACKAGE_QUANTITY?></td>
									<td class="main">
										<?=tep_draw_input_field('package_quantities', isset($_SESSION['batch_update_price_param']["qty_str"]) ? $_SESSION['batch_update_price_param']["qty_str"] : $cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'], 'size="40" id="package_quantities"') . '&nbsp;' . TEXT_MULTI_QTY_ENTRIES?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="15%">&nbsp;</td>
									<td>
										<?=tep_submit_button('Edit Prices List', 'Edit Prices List', 'name="btn_show_template"', 'inputButton')?>
										<?=tep_button('Reset', IMAGE_RESET, tep_href_link(FILENAME_BATCH_UPDATE_PRICES, 'action=reset_session'), 'name="btn_reset_session"', 'inputButton')?>
										<?=tep_button('Save Settings', 'Save Settings', '', ' onClick="savePackageQuantitySetting(this, \'product_name\', \'cat_id\', \'package_quantities\');" ', 'inputButton')?>
									</td>
								</tr>
							</table>
						</form>
						</td>
					</tr>
					<script language="javascript"><!--
						function product_selection_form_checking() {
							var error_message = '<?=JS_ERROR?>';
							var error = false;
							var focus_field = '';
							
							if (trim_str(document.getElementById('product_name').value) == "") {
								error_message += '* Please enter the product name.' + "\n";
								focus_field = 'product_name';
								error = true;
							}
							
							if (document.getElementById('cat_id').selectedIndex < 1) {
								error_message += '* Please select category.' + "\n";
								if (focus_field.length < 1)		focus_field = 'cat_id';
								error = true;
							}
							
							if (trim_str(document.getElementById('package_quantities').value) == "") {
								error_message += '* Please enter the package quantity.' + "\n";
								if (focus_field.length < 1)		focus_field = 'package_quantities';
								error = true;
							}
							
							if (error == true) {
								alert(error_message);
								document.getElementById(focus_field).focus();
								return false;
							} else {
								return true;
							}
						}
						//-->
					</script>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>