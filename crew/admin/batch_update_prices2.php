<?
/*
  	$Id: batch_update_prices2.php,v 1.8 2007/07/31 14:15:26 weichen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture

  	Released under the GNU General Public License
*/
require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

$currencies = new currencies();

if (!defined('DISPLAY_PRICE_DECIMAL')) {
	define('DISPLAY_PRICE_DECIMAL', 6);
}

function filter_selection($var) {
	return ($var && $var >= 1);
}

function get_bundle_price($unit_price, $weight, $subproduct_qty, $increment_value) {
    //echo "<br>$unit_price, $weight, $subproduct_qty, $increment_value";
    $price_to_use = 0;
    $unit_price = (float)$unit_price;
    $weight = (int)$weight;
    $subproduct_qty = (int)$subproduct_qty;
    $increment_value_num = (float)increment_value;

    if (substr($increment_value, -1, 1) == '%') {
        $increment_value_num = (int)substr($increment_value, 0, -1) / 100;
        $price_to_use = ($unit_price * (pow((1 + $increment_value_num), ($weight-1)))) * $subproduct_qty;
    } else {
        $price_to_use = ((($weight-1) * $increment_value_num) + $unit_price) * $subproduct_qty;
    }
    return number_format($price_to_use, DISPLAY_PRICE_DECIMAL, '.', '');
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

unset($_SESSION["s_package_qty"]);	// Used in edit price set page

if (tep_not_null($action)) {
	switch ($action) {
		case "do_update_price":

			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						$import_error = false;

						$filename = ($_FILES['csv_import']['tmp_name']);
					    $handle = fopen($filename, 'r+');

					    //Packages that are being viewed (User supplied in first screen, or clicked ALL).
					    $valid_package_qty_array = tep_array_unserialize($HTTP_POST_VARS["serialized_package_qty_array"]);

					    // skip first two lines
					    for ($skip_cnt=0; $skip_cnt < 2; $skip_cnt++) {
					    	$data = fgetcsv($handle, 1024, ',', '"');
					    }

					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	if (strtolower(trim($data[0])) != strtolower(TABLE_HEADING_CATEGORY_ID)) {
					    		$messageStack->add_session("Category ID field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    	if (strtolower(trim($data[1])) != strtolower(TABLE_HEADING_CATEGORY)) {
					    		$messageStack->add_session("Category field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    	if (strtolower(trim($data[2])) != strtolower(TABLE_HEADING_AVAILABLE_QTY)) {
					    		$messageStack->add_session("Available Qty field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    	if (strtolower(trim($data[3])) != strtolower(TABLE_HEADING_ACTUAL_QTY)) {
					    		$messageStack->add_session("Actual Qty field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    	if (strtolower(trim($data[4])) != strtolower(TABLE_HEADING_INCREMENT_VALUE)) {
					    		$messageStack->add_session("Increment Value field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    	if (strtolower(trim($data[5])) != strtolower(TABLE_HEADING_PRODUCTS_PRICE)) {
					    		$messageStack->add_session("Unit Price field does not exists!", 'error');
					    		$import_error = true;
					    	}

					    }
					    /*
					    if (!count($imported_qty_str_array)) {
					    	$messageStack->add_session("There is no any package quantity information!", 'error');
					    	$import_error = true;
					    }
					    */
					    if (!$import_error) {
					    	$main_cat_name = tep_get_category_name($_SESSION['batch_update_price_param']["cat_id"], $languages_id);
					    	// Get all the subcategories for the selected category
					    	$category_array = array($_SESSION['batch_update_price_param']["cat_id"]);
					    	tep_get_subcategories($category_array, $_SESSION['batch_update_price_param']["cat_id"]);

					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    			$cat_id_str = $data[0];
				    			$increment_value_str = $data[4];
				    			$products_price_str = $data[5];
				    			if (trim($cat_id_str) == '') {	// Assume this row is useless
				    				continue;
				    			}

								if (trim($cat_id_str) == '' || !in_array((int)$cat_id_str, $category_array)) {
					    			$messageStack->add_session("Category ID: " . $cat_id_str . " does not under " . $main_cat_name . "!" , 'error');
					    			$import_error = true;
					    		} else if (tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES2, (int)$cat_id_str) != 1) {
					    			$messageStack->add_session(sprintf(ERROR_UPDATE_ACCESS_DENIED_CAT, $cat_id_str), 'error');
					    			$import_error = true;
					    		}

					    		$imported_categories_array[] = array(	'cat_id' => (int)$cat_id_str,
					    		                                        'increment_value' => $increment_value_str,
					    		                                        'products_price' => $products_price_str
						    										);

							}
					    	fclose($handle);
					    	if ($import_error) {
					    		$messageStack->add_session("No data is imported! Please fix the above error(s) and import your file again.", 'error');
					    		tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2, 'action=list_template&cont=1'));
					    	}
					    } else {
					    	fclose($handle);
					    	$messageStack->add_session("No data is imported! Please fix the above error(s) and import your file again.", 'error');
							tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2, 'action=list_template&cont=1'));
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2));
				}
			} else if ($HTTP_POST_VARS['btn_csv_export']) {
				$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
				$export_package_qty_array = tep_array_unserialize($HTTP_POST_VARS["serialized_package_qty_array"]);

				$export_csv_data = '';
				if (count($export_csv_array)) {
        			$export_csv_data = '"' . str_replace('"', '""', "Product") . '", ' . str_replace('"', '""', $_SESSION['batch_update_price_param']["product_name"]) . "\n";
        			$export_csv_data .= '"' . str_replace('"', '""', "Category") . '", ' . str_replace('"', '""', tep_get_category_name($_SESSION['batch_update_price_param']["cat_id"], $languages_id)) . "\n";
					$export_csv_data .= TABLE_HEADING_CATEGORY_ID . ", " . TABLE_HEADING_CATEGORY . ", " . TABLE_HEADING_AVAILABLE_QTY . ", " . TABLE_HEADING_ACTUAL_QTY . ", " . TABLE_HEADING_INCREMENT_VALUE . ", " . TABLE_HEADING_PRODUCTS_PRICE;

					if (isset($_POST['show_ind_packages_price']) && $_POST['show_ind_packages_price'] == '1') {
						for ($i=0; $i < count($export_package_qty_array); $i++) {
							$export_csv_data .= ', ' . $export_package_qty_array[$i];
						}
					}
					$export_csv_data .= "\n";

					foreach ($export_csv_array as $cat_id => $res) {
						$cat_id = (int)$cat_id;
						$cat_name = '"' . str_replace('"', '""', $res['cat_name']) . '"';
						$available_qty = '"' . str_replace('"', '""', $res["available_qty"]) . '"';
						$actual_qty = '"' . str_replace('"', '""', $res["actual_qty"]) . '"';
						$increment_value = str_replace('"', '""', $res["increment_value"]);
						$products_price = str_replace('"', '""', $res["products_price"]);

						$export_csv_data .= "$cat_id,$cat_name,$available_qty,$actual_qty,$increment_value,$products_price";

						if (isset($_POST['show_ind_packages_price']) && $_POST['show_ind_packages_price'] == '1') {
							for ($i=0; $i < count($export_package_qty_array); $i++) {
								$export_csv_data .= ',' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res['qty_str'][$export_package_qty_array[$i]]);
							}
						}
						$export_csv_data .= "\n";
					}
				}

				if (tep_not_null($export_csv_data)) {
					$filename = 'batch_update_price_'.date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2));
				}
			} else if (isset($HTTP_POST_VARS['btn_db_update'])) {	//	The actual update of those packages prices

				$update_error = false;
				$product_price_update_sql = array();

				if (count($HTTP_POST_VARS["update_price"])) {
					foreach ($HTTP_POST_VARS["update_price"] as $this_cat_id => $price_res) {
						if (count($price_res)) {
							foreach ($price_res as $prod_id => $ind_price) {
								$ind_price = number_format($ind_price, DISPLAY_PRICE_DECIMAL, '.', '');
								if (!is_numeric($ind_price)) {
									$cat_name = tep_get_category_name($this_cat_id, $languages_id);
									$messageStack->add("Price (".$ind_price.") for category with ID: " . $cat_name . " must be a currency value!" , 'error');
									$update_error = true;
								} else {
    								$product_price_update_sql[] = "UPDATE " . TABLE_PRODUCTS . " SET products_price ='" . $ind_price . "' WHERE products_id = '" . (int)$prod_id . "'; ";
								}
							}
						}
					}
				}

				if (!$update_error) {
					if (count($product_price_update_sql)) {
						foreach ($product_price_update_sql as $price_update_sql) {
            				tep_db_query($price_update_sql);
						}
					}

    				$cat_id_arr = explode('|', $HTTP_POST_VARS['cat_list']);
    				//Delete increment value setting in category configuration where applicable.
        			$cat_configuration_delete_sql = "DELETE FROM " . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_configuration_key = 'INCREMENT_VALUE_FOR_BATCH_UPDATE' AND categories_id IN (" . implode(',', $cat_id_arr)  . ")";
        			tep_db_query($cat_configuration_delete_sql);

    			    foreach ($cat_id_arr as $cat_id) {
    			        $products_id = $HTTP_POST_VARS["cat_matched_products_id_{$cat_id}"];
    			        $products_price = $HTTP_POST_VARS["products_price_{$cat_id}"];
    			        //Update Unit Price for matched product
                        tep_db_perform(TABLE_PRODUCTS, array('products_price' => (double)$products_price), 'update', "products_id='$products_id'");

                        //Update the category's increment value in categories_configuration
    			        $increment_value = $HTTP_POST_VARS["increment_value_$cat_id"];
                        tep_db_perform(TABLE_CATEGORIES_CONFIGURATION, array('categories_configuration_key' => 'INCREMENT_VALUE_FOR_BATCH_UPDATE', 'categories_id' => $cat_id, 'categories_configuration_value' => $increment_value));
    			    }

    			    //Needed to break dependance of price sets in earlier version of batch update price. Will cause these price settings to appear as 'custom' in that screen.
				    $categories_to_price_sets_delete_sql = "DELETE FROM " . TABLE_CATEGORIES_TO_PRICE_SETS . " WHERE categories_id IN ('" . implode("', '", array_keys($HTTP_POST_VARS["update_price"])) . "') ";
					tep_db_query($categories_to_price_sets_delete_sql);

					$messageStack->add_session("Successfully batch update of products prices.", 'success');
					tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2, 'action=list_template&cont=1'));
				} else {
					$messageStack->add("Products prices are not updated! Please fix the above error(s) and update again.", 'error');
				}
			}
			break;
		case "list_template":
			if ($_REQUEST["cont"] != '1') {
				if (tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES2, (int)$_REQUEST["cat_id"]) > 0) {
					$_SESSION['batch_update_price_param']["product_name"] = tep_db_prepare_input($_REQUEST["product_name"]);
					$_SESSION['batch_update_price_param']["cat_id"] = (int)$_REQUEST["cat_id"];
                    if ($_REQUEST["all_package_quantities"] == '1') {
    					$_SESSION['batch_update_price_param']["qty_str"] = 'all';
                    } else {
    					$_SESSION['batch_update_price_param']["qty_str"] = tep_db_prepare_input($_REQUEST["package_quantities"]);
                    }
				} else {
					$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
					tep_redirect( tep_href_link(FILENAME_BATCH_UPDATE_PRICES2, tep_get_all_get_params(array('action'))) );
				}
			}
			break;
		case "reset_session":
        	unset($_SESSION['batch_update_price_param']);
        	tep_redirect(tep_href_link(FILENAME_BATCH_UPDATE_PRICES2));
        	break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/product_listing.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>

</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "list_template" || $action == "do_update_price" || isset($HTTP_POST_VARS['btn_csv_import'])) {
	$export_csv_array = array();
	$default_package_info_array = array();

	$match_product = $_SESSION['batch_update_price_param']["product_name"];
    $package_qty_exist_array = array();


    if ($_SESSION['batch_update_price_param']["qty_str"] == 'all') {
        $all_subproduct_qty_select_sql = "SELECT DISTINCT subproduct_qty FROM " . TABLE_PRODUCTS_BUNDLES . " ORDER BY subproduct_qty";
        $all_subproduct_qty_result_sql = tep_db_query($all_subproduct_qty_select_sql);
        while($all_subproduct_qty_row = tep_db_fetch_array($all_subproduct_qty_result_sql)) {
            $package_qty_array[] = $all_subproduct_qty_row['subproduct_qty'];
        }
    } elseif (tep_not_null($_SESSION['batch_update_price_param']["qty_str"])) {
		$package_qty_array = explode(';', $_SESSION['batch_update_price_param']["qty_str"]);
		$package_qty_array = array_filter($package_qty_array, "filter_empty_val");
		$package_qty_array = array_unique($package_qty_array);
	} else {
		$package_qty_array = array();
	}

	if (count($package_qty_array)) {
		foreach ($package_qty_array as $key => $qty_string) {
			$package_qty_array[$key] = (int)$qty_string;
			$default_package_info_array[(int)$qty_string]['current_price_db'] = TEXT_PRICE_NOT_AVAILABLE;
		}
	}

	$row_count = 0;
	if (isset($HTTP_POST_VARS['btn_csv_import']) && $action == "do_update_price") {
        function tep_show_list_items($ListItems, $Path, $MatchName) {
            global $languages_id, $package_qty_array, $row_count, $package_qty_exist_array, $default_package_info_array, $export_csv_array, $resultSet;
			
    		for ($cat_cnt=0; $cat_cnt < count($ListItems); $cat_cnt++) {
    			$catInfo = new objectInfo($ListItems[$cat_cnt]);
    			$product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_actual_quantity, p.products_price
    									FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
    									INNER JOIN " . TABLE_PRODUCTS . " AS p
    										ON p2c.products_id=p.products_id
    									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
    										ON p2c.products_id=pd.products_id
    									WHERE p2c.categories_id = '" . $catInfo->cat_id . "'
    										AND pd.products_name = '".tep_db_input($MatchName)."'
    										AND pd.language_id='".(int)$languages_id."'
    										AND p2c.products_is_link=0
    										AND p.products_bundle = ''
    										AND p.products_bundle_dynamic = '' ";
    			$product_result_sql = tep_db_query($product_select_sql);
				
    			if ($product_row = tep_db_fetch_array($product_result_sql)) {
					$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
					$available_qty = number_format($product_row["products_quantity"], 0, '.', ',');
					$actual_qty = number_format($product_row["products_actual_quantity"], 0, '.', ',');
					$resultSet[$catInfo->cat_id]['row_style'] = $row_style;
					$DisplayName = tep_output_generated_category_path_sq($catInfo->cat_id);
                    $DisplayName = trim(substr($DisplayName, strlen($Path)));
					
					$product_display_name = tep_display_category_path(tep_get_product_path($product_row['products_id']), $catInfo->cat_id, 'catalog', false);
					
                    if (strpos($DisplayName, '>') === 0)	$DisplayName = trim(substr($DisplayName, 1));
                    $resultSet[$catInfo->cat_id]['display_name'] = $product_display_name;
                    $resultSet[$catInfo->cat_id]['products_price_db'] = $product_row['products_price'];
                    $resultSet[$catInfo->cat_id]['products_price'] = $catInfo->products_price;
                    $resultSet[$catInfo->cat_id]['matched_products_id'] = $product_row['products_id'];
                    $resultSet[$catInfo->cat_id]['available_qty'] = $available_qty;
                    $resultSet[$catInfo->cat_id]['actual_qty'] = $actual_qty;

        			$cat_configuration_select_sql = "	SELECT categories_configuration_value FROM " . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_configuration_key = 'INCREMENT_VALUE_FOR_BATCH_UPDATE' AND categories_id = '{$catInfo->cat_id}'";
        			$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);
        			if ($cat_configuration_row = tep_db_fetch_array($cat_configuration_result_sql)) {
                        $resultSet[$catInfo->cat_id]['increment_value_db'] = $cat_configuration_row['categories_configuration_value'];
        			} else {
        			    $resultSet[$catInfo->cat_id]['increment_value_db'] = 0;
        			}
                    $resultSet[$catInfo->cat_id]['increment_value'] = (strlen($catInfo->increment_value)>0 ? $catInfo->increment_value : 0);

					$export_csv_array[$catInfo->cat_id]['cat_name'] = $product_display_name;
					$export_csv_array[$catInfo->cat_id]['available_qty'] = $available_qty;
					$export_csv_array[$catInfo->cat_id]['actual_qty'] = $actual_qty;
					$export_csv_array[$catInfo->cat_id]['products_price'] = $product_row['products_price'];
                    $export_csv_array[$catInfo->cat_id]['increment_value'] = $resultSet[$catInfo->cat_id]['increment_value'];

			        $product_bundle_select_sql = "	SELECT p.products_id, p.products_price, p.products_status, pb.subproduct_qty, COUNT(pb.subproduct_id) AS total_subproduct
											FROM " . TABLE_PRODUCTS . " AS p
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
												ON p.products_id=p2c.products_id
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
												ON p2c.products_id=pb.bundle_id
											WHERE p2c.categories_id = '" . $catInfo->cat_id . "'
											AND p2c.products_is_link=0
											AND (p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes')";

					if (count($package_qty_array)) {
					     $product_bundle_select_sql .= " AND pb.subproduct_qty IN ('".implode("', '", $package_qty_array)."')";
					}
					$product_bundle_select_sql .= " GROUP BY p.products_id";
			        $product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
                    $resultSet[$catInfo->cat_id]['products'] = $default_package_info_array;

                    while ($product_bundle_row = tep_db_fetch_array($product_bundle_result_sql)) {
					    $resultSet[$catInfo->cat_id]['products'][$product_bundle_row['subproduct_qty']]['products_id'] = $product_bundle_row["products_id"];
					    $resultSet[$catInfo->cat_id]['products'][$product_bundle_row['subproduct_qty']]['products_status'] = $product_bundle_row["products_status"];
                        if ($product_bundle_row['products_status'] == '1') {
                            $resultSet[$catInfo->cat_id]['products'][$product_bundle_row['subproduct_qty']]['current_price_db'] = $product_bundle_row['products_price'];
						} else {
                            $resultSet[$catInfo->cat_id]['products'][$product_bundle_row['subproduct_qty']]['current_price_db'] = "<span title=\"Inactive\" class=\"redIndicator\">{$product_bundle_row['products_price']}</span>";
						}
						//$resultSet[$catInfo->cat_id]['products'][$product_bundle_row['subproduct_qty']]['current_price'] = $catInfo->packages_price[$product_bundle_row['subproduct_qty']]['price'];
					}
			        if (isset($resultSet[$catInfo->cat_id]['products'])) {
						$weight = 0;
                        krsort($resultSet[$catInfo->cat_id]['products']);
						foreach ($resultSet[$catInfo->cat_id]['products'] as $subproduct_qty => $details) {
						    if (isset($details['products_id'])) {
						        if (!in_array($subproduct_qty, $package_qty_exist_array)) {
							        //This array shortlists only quantities that occur at least once in the resultset. For Header.
							        $package_qty_exist_array[] = $subproduct_qty;
						        }
                                $weight++;
                                $resultSet[$catInfo->cat_id]['products'][$subproduct_qty]['weight'] = $weight;
                                $resultSet[$catInfo->cat_id]['products'][$subproduct_qty]['current_price'] = get_bundle_price($resultSet[$catInfo->cat_id]['products_price'], $weight, $subproduct_qty, $resultSet[$catInfo->cat_id]['increment_value']);
                            }
                            $export_csv_array[$catInfo->cat_id]['qty_str'][$subproduct_qty] = str_replace('<br>', ' ', preg_replace("'<span[^>]*?>(.*?)</span>'is", "\\1", $details['current_price_db']));
                        }
					}
			        $row_count++;
    			}
    		}
    	}
	} else {
		function tep_show_list_items($ListItems, $Path, $MatchName) {
			global $languages_id, $package_qty_array, $row_count, $package_qty_exist_array, $default_package_info_array, $export_csv_array, $resultSet;
			
			foreach ($ListItems as $ListItem)
			{
				$NewListItems = array() ;

				$p_rank = tep_check_cat_tree_permissions(FILENAME_BATCH_UPDATE_PRICES2, $ListItem["categories_id"]);

				if ($p_rank > 0) {
					$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name
										FROM " . TABLE_CATEGORIES . " AS c
										INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											ON c.categories_id = cd.categories_id
										WHERE c.parent_id='" . $ListItem["categories_id"] . "'
											AND cd.language_id='" . (int)$languages_id ."'
										ORDER BY sort_order, cd.categories_name " ;

					$cat_result_sql = tep_db_query($cat_select_sql);
					while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
						$NewListItems[] = $cat_row ;
					}

					$proceed = true;
					if ($p_rank != 1)	$proceed = false;

					if ($proceed) {
					    $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_actual_quantity, p.products_price
												FROM " . TABLE_PRODUCTS . " AS p
												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
													ON p.products_id=p2c.products_id
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
													ON p2c.products_id=pd.products_id
												WHERE p2c.categories_id = '" . $ListItem["categories_id"] . "'
													AND pd.products_name = '".tep_db_input($MatchName)."'
													AND pd.language_id='".(int)$languages_id."'
													AND p2c.products_is_link=0
													AND p.products_bundle = ''
													AND p.products_bundle_dynamic = '' ";
						$product_result_sql = tep_db_query($product_select_sql);

						if ($product_row = tep_db_fetch_array($product_result_sql)) {
							$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
							$available_qty = number_format($product_row["products_quantity"], 0, '.', ',');
							$actual_qty = number_format($product_row["products_actual_quantity"], 0, '.', ',');
							$resultSet[$ListItem["categories_id"]]['row_style'] = $row_style;
							$DisplayName = strip_tags($Path . (tep_not_null($Path) && tep_not_null($ListItem["categories_name"]) ? ' > ' : '') . $ListItem["categories_name"]);
							
							$product_display_name = tep_display_category_path(tep_get_product_path($product_row['products_id']), $ListItem["categories_id"], 'catalog', false);
							
							$resultSet[$ListItem["categories_id"]]['display_name'] = $product_display_name;
						    $resultSet[$ListItem["categories_id"]]['products_price'] = $product_row['products_price'];
						    $resultSet[$ListItem["categories_id"]]['products_price_db'] = $product_row['products_price'];
						    $resultSet[$ListItem["categories_id"]]['matched_products_id'] = $product_row['products_id'];
						    $resultSet[$ListItem["categories_id"]]['available_qty'] = $available_qty;
						    $resultSet[$ListItem["categories_id"]]['actual_qty'] = $actual_qty;

                			$cat_configuration_select_sql = "	SELECT categories_configuration_value FROM " . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_configuration_key = 'INCREMENT_VALUE_FOR_BATCH_UPDATE' AND categories_id = '{$ListItem["categories_id"]}'";
                			$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);
                			if ($cat_configuration_row = tep_db_fetch_array($cat_configuration_result_sql)) {
                                $resultSet[$ListItem["categories_id"]]['increment_value_db'] = $cat_configuration_row['categories_configuration_value'];
                			} else {
                			    $resultSet[$ListItem["categories_id"]]['increment_value_db'] = 0;
                			}
                			$resultSet[$ListItem["categories_id"]]['increment_value'] = (strlen($resultSet[$ListItem["categories_id"]]['increment_value_db']>0) ? $resultSet[$ListItem["categories_id"]]['increment_value_db'] : 0);

							$export_csv_array[$ListItem["categories_id"]]['cat_name'] = $product_display_name;
							$export_csv_array[$ListItem["categories_id"]]['available_qty'] = $available_qty;
							$export_csv_array[$ListItem["categories_id"]]['actual_qty'] = $actual_qty;
							$export_csv_array[$ListItem["categories_id"]]['products_price'] = $product_row['products_price'];
                            $export_csv_array[$ListItem["categories_id"]]['increment_value'] = $resultSet[$ListItem["categories_id"]]['increment_value_db'];

							$product_bundle_select_sql = "	SELECT p.products_id, p.products_price, p.products_status, pb.subproduct_qty, COUNT(pb.subproduct_id) AS total_subproduct
															FROM " . TABLE_PRODUCTS . " AS p
															INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
																ON p.products_id=p2c.products_id
															INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
																ON p2c.products_id=pb.bundle_id
															WHERE p2c.categories_id = '" . $ListItem["categories_id"] . "'
																AND p2c.products_is_link=0
																AND (p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes')";

							if (!$_SESSION['batch_update_price_param']["qty_str"]!='all') {
							     $product_bundle_select_sql .= " AND pb.subproduct_qty IN ('".implode("', '", $package_qty_array)."')";
							}
							$product_bundle_select_sql .= " GROUP BY p.products_id
															HAVING total_subproduct = 1 ";
							$product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
                            $resultSet[$ListItem["categories_id"]]['products'] = $default_package_info_array;
							while ($product_bundle_row = tep_db_fetch_array($product_bundle_result_sql)) {
    						    $resultSet[$ListItem["categories_id"]]['products'][$product_bundle_row['subproduct_qty']]['products_id'] = $product_bundle_row["products_id"];
    						    $resultSet[$ListItem["categories_id"]]['products'][$product_bundle_row['subproduct_qty']]['products_status'] = $product_bundle_row["products_status"];
                                if ($product_bundle_row['products_status'] == '1') {
                                    $resultSet[$ListItem["categories_id"]]['products'][$product_bundle_row['subproduct_qty']]['current_price_db'] = $product_bundle_row['products_price'];
                                } else {
                                    $resultSet[$ListItem["categories_id"]]['products'][$product_bundle_row['subproduct_qty']]['current_price_db'] = "<span title=\"Inactive\" class=\"redIndicator\">{$product_bundle_row['products_price']}</span>";
                                }
							}
					        if (isset($resultSet[$ListItem["categories_id"]]['products'])) {
    							$weight = 0;
                                krsort($resultSet[$ListItem["categories_id"]]['products']);
    							foreach ($resultSet[$ListItem["categories_id"]]['products'] as $subproduct_qty => $details) {
    							    if (isset($details['products_id'])) {
    							        if (!in_array($subproduct_qty, $package_qty_exist_array)) {
        							        //This array shortlists only quantities that occur at least once in the resultset. For Header.
        							        $package_qty_exist_array[] = $subproduct_qty;
    							        }
    							        $weight++;
        							    $resultSet[$ListItem["categories_id"]]['products'][$subproduct_qty]['weight'] = $weight;
                                        $resultSet[$ListItem["categories_id"]]['products'][$subproduct_qty]['current_price'] = get_bundle_price($resultSet[$ListItem["categories_id"]]['products_price'], $weight, $subproduct_qty, $resultSet[$ListItem["categories_id"]]['increment_value']);
    							    }
    							    $export_csv_array[$ListItem["categories_id"]]['qty_str'][$subproduct_qty] = str_replace('<br>', ' ', preg_replace("'<span[^>]*?>(.*?)</span>'is", "\\1", $details['current_price_db']));
    							}
					        }
							$row_count++;
						}
				    }
					tep_show_list_items($NewListItems, $DisplayName, $MatchName);
				}
			}
			return true;
		}
	}

	$ListItems = array() ;

	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name
						FROM " . TABLE_CATEGORIES . " AS c
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
							ON c.categories_id = cd.categories_id
						WHERE c.categories_id='" . $_SESSION['batch_update_price_param']["cat_id"] . "'
							AND cd.language_id='" . (int)$languages_id ."'
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);

	if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
		$parent_id = $cat_row['parent_id'];
	}
?>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="2">
        						<tr>
        							<td class="main" width="15%"><?=TEXT_MATCHED_PRODUCT_NAME?></td>
        							<td class="main" colspan="2"><?=$match_product?></td>
        						</tr>
        						<tr>
        							<td class="main"><?=TEXT_MATCHED_CATEGORY_NAME?></td>
        							<td class="main"><?=tep_output_generated_category_path_sq($_SESSION['batch_update_price_param']["cat_id"])?></td>
        							<td class="main" align="right">
										<div id="show_batch_fill"><a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a></div>
        							</td>
        						</tr>
								<tr>
			        				<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<TBODY id="batch_fill" class="hide">
									<tr>
										<td colspan="3">
											<table width="60%" border="0" cellspacing="0" cellpadding="2">
												<tr>
													<td class="ordersBoxHeading"><?=TABLE_HEADING_BATCH_FILL?></td>
													<td class="ordersBoxHeading" align="left"><?=TABLE_HEADING_INCREMENT_VALUE?></td>
													<td class="ordersBoxHeading" align="left"><?=TABLE_HEADING_PRODUCTS_PRICE?></td>
												</tr>
												<tr class="ordersListingEven">
													<td class="ordersRecords" align="center"><?=TEXT_APPLY_TO_SELECTED?></td>
                                             <?
                                             echo " <td class=\"ordersRecords\" align=\"left\">". tep_draw_input_field("increment_value_batch", 0, " size=\"10\" id=\"increment_value_batch\" ") ."</td>\n
    												<td class=\"ordersRecords\" align=\"left\">". tep_draw_input_field("products_price_batch", 0, " size=\"10\" id=\"products_price_batch\" onKeyUp=\"if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) { this.value = ''; }\" ") . "</td>\n";
                                             ?>
												</tr>
												<tr>
													<td colspan="3" align="right">
														<?=tep_button(IMAGE_BUTTON_BATCH_APPLY, IMAGE_BUTTON_BATCH_APPLY, '', 'onClick="apply_batch(this);"')?>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</TBODY>
        					</table>
						</td>
					</tr>
					<tr>
						<td>
<?	echo tep_draw_form('template_listing_form', FILENAME_BATCH_UPDATE_PRICES2, tep_get_all_get_params(array('action')) . 'action=do_update_price', 'post', 'onSubmit="return template_listing_form_checking();"'); ?>
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
        						<tr>
			       					<td class="reportBoxHeading"><?=tep_draw_checkbox_field('checkAll', '', false, '', ' onclick="select_all_checkbox_click(this, \'chkSelected\')" ')?></td>
			       					<td class="reportBoxHeading"><?=TABLE_HEADING_CATEGORY?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_AVAILABLE_QTY?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_ACTUAL_QTY?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_INCREMENT_VALUE?></td>
					                <td align="center" width="5%" class="reportBoxHeading"><?=TABLE_HEADING_PRODUCTS_PRICE?></td>
<?
    //Building recordset
    $resultSet = array();
    if (isset($HTTP_POST_VARS['btn_csv_import']) && $action == "do_update_price") {
		tep_show_list_items($imported_categories_array, tep_output_generated_category_path_sq($parent_id), $match_product);
	} else {
		tep_show_list_items($ListItems, '', $match_product);
	}
    echo tep_draw_hidden_field("cat_list", implode('|', array_keys($resultSet)), " id=\"cat_list\" ");

	//Display Headers
    sort($package_qty_exist_array);
	foreach ($package_qty_exist_array as $qty_string) {
		echo '<td align="center" width="6%" class="reportBoxHeading">'.$qty_string.'</td>';
		echo '<td class="reportBoxHeading">&nbsp;</td>';
	}
?>
			   					</tr>
<?php
	//Build JS array of cat_ids and products price
    $build_cat_id_js_arr = "\n <script language=\"javascript\" type=\"text/javascript\">\n";
    $build_cat_id_js_arr .= "var cat_id_arr = new Array(); \n";
    $build_cat_id_js_arr .= "var cat_id_arraykeys_arr = new Array(); \n";

    //Display Grid
    foreach ($resultSet as $cat_id => $cat_details) {
        $build_cat_id_js_arr .= "cat_id_arraykeys_arr.push('$cat_id'); \n";
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id'] = new Array(); \n";
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['increment_value'] = '{$cat_details['increment_value']}'; \n";
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['products_price'] = {$cat_details['products_price']}; \n";
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_products_id_arr'] = new Array(); \n";
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_data_arr'] = new Array(); \n";
		
        $build_html_rows_str .= '
		      <tr class="'.$cat_details["row_style"].'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$cat_details["row_style"].'\')" onclick="rowClicked(this, \''.$cat_details["row_style"].'\')">
  					<td class="reportRecords" valign="top">'.tep_draw_checkbox_field("chkSelected[{$cat_id}]", '1', false, '', " id=\"chkSelected{$cat_id}\" ").'</td>
					<td class="reportRecords" valign="top">'.$cat_details['display_name'].'</td>
					<td class="reportRecords" align="right" valign="top">'.$cat_details["available_qty"].'</td>
					<td class="reportRecords" align="right" valign="top">'.$cat_details["actual_qty"].'</td>
					<td class="reportRecords" align="right" valign="top">'.$cat_details['increment_value_db'].'<br>'.tep_draw_input_field("increment_value_{$cat_id}", (string)$cat_details['increment_value'], " size=\"10\" id=\"increment_value_{$cat_id}\" onChange=\"updateIncrementValue('$cat_id', this.value);calculate_price_row('$cat_id', cat_id_arr['$cat_id']['increment_value'], cat_id_arr['$cat_id']['products_price']);\" ").'</td>
					<td class="reportRecords" align="right" valign="top">'.$cat_details['products_price_db'].'<br>'.tep_draw_input_field("products_price_{$cat_id}", (string)$cat_details['products_price'], " size=\"10\" id=\"products_price_{$cat_id}\" onKeyUp=\"if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) { this.value = ''; }\" onChange=\"updateUnitPrice('$cat_id', this.value);calculate_price_row('$cat_id', cat_id_arr['$cat_id']['increment_value'], cat_id_arr['$cat_id']['products_price']);\" ").'</td>';

        $build_cat_id_js_arr .= "var packages_products_id_arr = new Array(); \n";
		foreach ($package_qty_exist_array as $qty_string) {
            $build_html_rows_str .= '<td class="reportRecords" align="right" valign="top">';

	        $pkg_arr = $cat_details['products'][$qty_string];
	        $build_html_rows_str .= $pkg_arr['current_price_db'];

	        if (isset($pkg_arr['products_id'])) {
                $build_cat_id_js_arr .= "packages_products_id_arr.push('".$pkg_arr['products_id']."'); \n";
                $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_data_arr']['".$pkg_arr['products_id']."'] = new Array();";
                $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_data_arr']['".$pkg_arr['products_id']."']['weight'] = {$pkg_arr['weight']};";
                $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_data_arr']['".$pkg_arr['products_id']."']['qty_string'] = $qty_string;";
	        }
	        if ($pkg_arr['current_price_db'] != TEXT_PRICE_NOT_AVAILABLE) {
                $build_html_rows_str .= tep_draw_input_field('update_price['.$cat_id.']['.$pkg_arr['products_id'].']', $pkg_arr['current_price'], "id=\"update_price_{$cat_id}_{$pkg_arr['products_id']}\" size=\"11\" align=\"right\" onKeyUp=\"if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) { this.value = ''; }\" ");
                $build_cat_id_js_arr .= "document.getElementById(\"update_price_{$cat_id}_{$pkg_arr['products_id']}\"); \n";
	        }
		    $build_html_rows_str .= '</td>';
		    $build_html_rows_str .= '<td class="reportBoxHeading">&nbsp;</td>';
		}
        $build_cat_id_js_arr .= "cat_id_arr['$cat_id']['packages_products_id_arr'] = packages_products_id_arr; \n";

        //Matched product is the product that the unit price belongs to
        $build_html_rows_str .= tep_draw_hidden_field("cat_matched_products_id_$cat_id", $cat_details['matched_products_id'], " id=\"cat_matched_products_id_$cat_id\" ");
		$build_html_rows_str .= '  </tr>';
	}
    $build_cat_id_js_arr .= "\n </script>\n";
    echo $build_html_rows_str;
	echo $build_cat_id_js_arr;
?>
								<tr>
									<td colspan="<?=(5 + count($package_qty_exist_array)*2)?>" align="left">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td align="left">
													<?=tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_BATCH_UPDATE_PRICES2), '', 'inputButton')?>
												</td>
												<td align="right">
													<input type="submit" name="btn_db_update" value="<?=IMAGE_UPDATE?>" title="<?=IMAGE_UPDATE?>" class="inputButton" onClick="return confirm_action('Are you sure to perform this batch update of products price?')">
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							<?=tep_draw_hidden_field("serialized_package_qty_array", tep_array_serialize($package_qty_array)) . "\n"?>
							</form>
						</td>
					</tr>
					<tr>
						<td>
<?
	echo tep_draw_form('price_list_csv_form', FILENAME_BATCH_UPDATE_PRICES2, tep_get_all_get_params(array('action')) . 'action=do_update_price', 'post', 'enctype="multipart/form-data"');

	echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
	echo tep_draw_hidden_field("serialized_package_qty_array", tep_array_serialize($package_qty_array)) . "\n";
	echo tep_draw_hidden_field("show_ind_packages_price", '0') . "\n";
?>
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
								<tr>
									<td width="50%" align="left">
										<?=tep_draw_file_field('csv_import', 'size="50"')?>
										<input type="submit" name="btn_csv_import" value="Import" title="Import csv file" class="inputButton">
									</td>
									<td width="50%" align="right">
										<input type="submit" name="btn_csv_export" value="Export Template" title="Export as csv file" class="inputButton" onClick="if (confirm_action('Do you want to show the price for each package quantties as well in the CSV file?')) { document.price_list_csv_form.show_ind_packages_price.value='1'; } else { document.price_list_csv_form.show_ind_packages_price.value='0'; }">
									</td>
								</tr>
							</table>
						</form>
						</td>
					</tr>
					<script language="javascript" type="text/javascript"><!--
						function template_listing_form_checking() {
							return true;
						}
						function calculate_price_row(cat_id, increment_value, unit_price) {
						    var packages_products_id_arr = cat_id_arr[cat_id]['packages_products_id_arr'];
						    var num_packages = packages_products_id_arr.length;
						    //Loop for each column.
						    for (var i=0; i<num_packages; i++) {
						        var products_id = packages_products_id_arr[i];
                                var packages_data_arr = cat_id_arr[cat_id]['packages_data_arr'][products_id];
						        document.getElementById('update_price_' + cat_id + '_' + products_id).value = get_bundle_price(unit_price, packages_data_arr['weight'], packages_data_arr['qty_string'], increment_value);
						    }
						}
						function get_bundle_price(unit_price, weight, subproduct_qty, increment_value) {
                            if (increment_value.substring(increment_value.length, (increment_value.length-1)) == '%') {
                                var price_to_use = (parseFloat(unit_price) * (Math.pow((1 + parseFloat(increment_value.substring(0, (increment_value.length-1)) / 100)), (parseInt(weight)-1)))) * parseInt(subproduct_qty);
                            } else {
                                var price_to_use = (((parseInt(weight)-1) * parseFloat(increment_value)) + parseFloat(unit_price)) * parseInt(subproduct_qty);
                            }
                            return price_to_use.toFixed(4);
						}
                		function apply_batch(BatchFillBtnObj) {
                            BatchFillBtnObj.disabled=true;
                            var increment_value = trim_str(document.getElementById('increment_value_batch').value);
                            var products_price = trim_str(document.getElementById('products_price_batch').value);
                            if ((increment_value.length > 0) || (products_price.length > 0)) {
                                //Loop for each row
    						    for (var j=0; j<cat_id_arraykeys_arr.length; j++) {
    						        if (document.getElementById('chkSelected'+cat_id_arraykeys_arr[j]).checked) {
    						            if (increment_value.length > 0) {
                                            document.getElementById('increment_value_'+cat_id_arraykeys_arr[j]).value = increment_value;
                                            updateIncrementValue(cat_id_arraykeys_arr[j], increment_value);
    						            }
    						            if (products_price.length > 0) {
                                            document.getElementById('products_price_'+cat_id_arraykeys_arr[j]).value = products_price;
                                            updateUnitPrice(cat_id_arraykeys_arr[j], products_price);
    						            }
                                        calculate_price_row(cat_id_arraykeys_arr[j], cat_id_arr[cat_id_arraykeys_arr[j]]['increment_value'], cat_id_arr[cat_id_arraykeys_arr[j]]['products_price']);
    						        }
    						    }
                            }
                            BatchFillBtnObj.disabled=false;
                		}
						function updateUnitPrice(cat_id, val) {
                            cat_id_arr[cat_id]['products_price'] = val;
						}
						function updateIncrementValue(cat_id, val) {
                            cat_id_arr[cat_id]['increment_value'] = val;
						}
                		function select_all_checkbox_click(selectAllObj, ctrlName) {
               				var checked_state = (selectAllObj.checked) ? true : false;
						    for (var j=0; j<cat_id_arraykeys_arr.length; j++) {
            					var chk_box = DOMCall(ctrlName + cat_id_arraykeys_arr[j]);
            					if (chk_box != null && !chk_box.disabled) {
           							chk_box.checked = checked_state;
            					}
						    }
                		}
                		function show_hide_batch_fill(show) {
                			var ele = document.getElementById('show_batch_fill');
                			var ele2 = document.getElementById('batch_fill');

                			if (show) {
                				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(false)"><?=TEXT_HIDE_BATCH_FILL?></a>';
                				ele2.className = "show";
                			} else {
                				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a>';
                				ele2.className = "hide";
                			}
                			return;
                		}
						//-->
					</script>
<?
} else {
	$categories_array = tep_get_eligible_category_tree(FILENAME_BATCH_UPDATE_PRICES2, 0, '___', '', $categories_array, false, 0, true);

	$cat_cfg_array = tep_get_cfg_setting((int)$_SESSION['batch_update_price_param']["cat_id"], 'catalog', '9', 'group_id');	// Get configuration values for Stock Options group
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('product_selection_form', FILENAME_BATCH_UPDATE_PRICES2, tep_get_all_get_params(array('action')) . 'action=list_template', 'post', 'onSubmit="return product_selection_form_checking();"'); ?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="15%"><?=ENTRY_CATEGORY?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, isset($_SESSION['batch_update_price_param']["cat_id"]) ? $_SESSION['batch_update_price_param']["cat_id"] : '', ' id="cat_id" onChange="getCatPackageQuantitySetting_2(this, \'product_name\', \'package_quantities\', \'all_package_quantities\');"')?>
					    			</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="15%" valign="top"><?=ENTRY_PRODUCT_NAME?></td>
									<td class="main">
										<?=tep_draw_input_field('product_name', isset($_SESSION['batch_update_price_param']["product_name"]) ? $_SESSION['batch_update_price_param']["product_name"] : $cat_cfg_array['PRODUCT_NAME_FOR_BATCH_UPDATE'], 'size="40" id="product_name"') . '&nbsp;' . TEXT_MATCH_PRODUCT_CRITERIA?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" valign="top"><?=ENTRY_PACKAGE_QUANTITY?></td>
									<td class="main">
									    <?=tep_draw_checkbox_field('all_package_quantities','1',(($_SESSION['batch_update_price_param']["qty_str"]=='all') ? true : false), '', ' id="all_package_quantities" onClick="toggleQuantities();" ') . '&nbsp;' . TEXT_ALL_PACKAGE_QUANTITIES?>
									    &nbsp;<u><b>OR</b></u>&nbsp;
										<?=tep_draw_input_field('package_quantities', (isset($_SESSION['batch_update_price_param']["qty_str"]) && ($_SESSION['batch_update_price_param']["qty_str"] != 'all')) ? $_SESSION['batch_update_price_param']["qty_str"] : (($cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE']!='all')?$cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE']:''), ' ' . (($_SESSION['batch_update_price_param']["qty_str"]=='all') ? 'disabled' : '' ) . ' size="40" id="package_quantities"') . '&nbsp;' . TEXT_MULTI_QTY_ENTRIES?>

									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="15%">&nbsp;</td>
									<td>
										<?=tep_submit_button('Edit Prices', 'Edit Prices', 'name="btn_show_template"', 'inputButton')?>
										<?=tep_button('Reset', IMAGE_RESET, tep_href_link(FILENAME_BATCH_UPDATE_PRICES2, 'action=reset_session'), 'name="btn_reset_session"', 'inputButton')?>
										<?=tep_button('Save Settings', 'Save Settings', '', ' onClick="savePackageQuantitySetting_2(this, \'product_name\', \'cat_id\', \'package_quantities\', \'all_package_quantities\');" ', 'inputButton')?>
									</td>
								</tr>
							</table>
						</form>
						</td>
					</tr>
					<script language="javascript"><!--
						function product_selection_form_checking() {
							var error_message = '<?=JS_ERROR?>';
							var error = false;
							var focus_field = '';

							if (trim_str(document.getElementById('product_name').value) == "") {
								error_message += '* Please enter the product name.' + "\n";
								focus_field = 'product_name';
								error = true;
							}

							if (document.getElementById('cat_id').selectedIndex < 1) {
								error_message += '* Please select category.' + "\n";
								if (focus_field.length < 1)		focus_field = 'cat_id';
								error = true;
							}

							if ((document.getElementById('all_package_quantities').checked == false) && (trim_str(document.getElementById('package_quantities').value) == "")) {
								error_message += '* Please enter the package quantity or select all packages.' + "\n";
								if (focus_field.length < 1)		focus_field = 'package_quantities';
								error = true;
							}

							if (error == true) {
								alert(error_message);
								document.getElementById(focus_field).focus();
								return false;
							} else {
								return true;
							}
						}
						function toggleQuantities() {
						    if (document.getElementById('all_package_quantities').checked) {
						      document.getElementById('package_quantities').disabled = true;
						    } else {
						      document.getElementById('package_quantities').disabled = false;
						    }
						}
						//-->
					</script>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>