<?
/*
  	$Id: buyback_groups.php,v 1.19 2013/10/07 11:14:32 chingyen Exp $
	
  	Developer: Subrat B.
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/
require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback.php');

$currencies = new currencies();

define('DISPLAY_PRICE_DECIMAL', 6);

function filter_selection($var) {
	return ($var && $var >= 1);
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_buyback_groups":
		case "update_buyback_groups":
		    
            if ($action == "insert_buyback_groups") {
				tep_db_query("insert into buyback_groups (buyback_groups_name, buyback_groups_description, buyback_groups_sort_order)values('".tep_db_prepare_input($_REQUEST["buyback_groups_name"])."','".tep_db_prepare_input($_REQUEST["buyback_groups_description"])."','".tep_db_prepare_input((int)$_REQUEST["buyback_groups_sort_order"])."');");
                $buyback_groups_id = (int)tep_db_insert_id();
			} else {
				tep_db_query("update buyback_groups set buyback_groups_name='".tep_db_prepare_input($_REQUEST["buyback_groups_name"])."', buyback_groups_description='".tep_db_prepare_input($_REQUEST["buyback_groups_description"])."', buyback_groups_sort_order='".(int)$_REQUEST["buyback_groups_sort_order"]."' where buyback_groups_id='".(int)$_REQUEST["gID"]."';");
                $buyback_groups_id = (int)tep_db_prepare_input($_REQUEST["gID"]);
			}
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
		case "change_status_buyback_groups":
			$buyback_group_status=1;
			
			if ((int)$_REQUEST["gID"]>0 && (int)$_REQUEST["buyback_group_status"]>=0) {
				$buyback_group_id = (int)$_REQUEST["gID"];
				$buyback_group_status = (int)$_REQUEST["buyback_group_status"]>=1 ? 1 : 0;
				
				tep_db_query("UPDATE buyback_groups SET buyback_groups_status='$buyback_group_status' WHERE buyback_groups_id='$buyback_group_id';");
			}		
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;			
		case "delete_buyback_groups":
			if (tep_not_null($_REQUEST["gID"])) {
				$buyback_groups_delete_sql = "DELETE FROM buyback_groups WHERE buyback_groups_id='" . $_REQUEST["gID"] . "'";
				tep_db_query($buyback_groups_delete_sql);
				
				$fetch_sql = "SELECT buyback_bracket_tags_id, buyback_groups_tags_info_id from buyback_groups_tags where buyback_groups_id='" . $_REQUEST["gID"] . "'";
				$fetch_result = tep_db_query($fetch_sql);
				
				while ($row = tep_db_fetch_array($fetch_result)) {
					tep_db_query("DELETE FROM ".TABLE_BUYBACK_BRACKET_TAGS." WHERE buyback_bracket_tags_id = '" . $row['buyback_bracket_tags_id'] . "'");
					tep_db_query("DELETE FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_table_name = '".TABLE_BUYBACK_GROUPS_TAGS_INFO."' AND buyback_setting_reference_id = '".tep_db_prepare_input($row['buyback_groups_tags_info_id'])."'");
				}
				
				tep_db_query("DELETE FROM  buyback_groups_tags WHERE buyback_groups_id='" . $_REQUEST["gID"] . "'");
				tep_db_query("DELETE FROM  buyback_groups_to_categories WHERE buyback_groups_id='" . $_REQUEST["gID"] . "'");
				tep_db_query("DELETE FROM  buyback_groups_tags_info WHERE buyback_groups_id='" . $_REQUEST["gID"] . "'");
			}
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
		case "insert_product_tags":
		case "update_product_tags":

    		$buyback_groups_settings_arr = array();
    		$buyback_groups_tags_info_arr = array('buyback_groups_id' => $_REQUEST["gID"]);
            //now update the object with the form posts.
    		foreach($_POST as $fldname => $value) {
    		  switch ($fldname) {
    		      case 'txtProductTagsField':
    		          $buyback_groups_tags_info_arr['buyback_groups_tags_field'] = tep_db_prepare_input($value);
    		          break;
    		      case 'txtProductTagsName':
    		          $buyback_groups_tags_info_arr['buyback_groups_tags_value'] = tep_db_prepare_input($value);
    		          break;
    		      case 'txtProductTagsOrder':
    		          $buyback_groups_tags_info_arr['buyback_groups_tags_order'] = (int)tep_db_prepare_input($value);
    		          break;
    		      case 'txtMinQtyOverwrite':
    		          $buyback_groups_tags_info_arr['buyback_groups_tags_minimum_buyback'] = (int)$value;
    		          break;
    		      case 'rdoMaxQtySource':
    		          $buyback_groups_settings_arr['bgrp_max_qty_source'] = $value;
                      switch ($value) {   
                          case "overwrite":
                            $buyback_groups_settings_arr['bgrp_max_qty_overwrite'] = (int)$_POST['txtMaxQtyOverwrite'];
                            $buyback_groups_tags_info_arr['buyback_groups_tags_maximum_buyback'] = (int)$_POST['txtMaxQtyOverwrite'];
                            break;
                          case "system_defined":
                            //do nothing
                            break;
                      }		          
    		          break;
    		      case 'txtMaxQtyOverwrite':
    		          //do nothing
    		          break;
    		      case 'txtMaxInvSpaceOverwrite':
    		          $buyback_groups_settings_arr['bgrp_max_inv_space_overwrite'] = (int)$value;
    		          break;
    		      case 'rdoSalesRetrievalMethod':
    		          $buyback_groups_settings_arr['bgrp_sales_retrieval_method'] = $value;
    		          break;
    		      case 'txtSalesStartDate':
    		          $buyback_groups_settings_arr['bgrp_sales_start_date'] = $value;
    		          break;
    		      case 'txtSalesEndDate':
    		          //If blank, save as blank.
    		          $buyback_groups_settings_arr['bgrp_sales_end_date'] = (trim($value) ? trim($value) : '');
    		          break;
    		      case 'txtLastNDaysSales':
    		          $buyback_groups_settings_arr['bgrp_last_n_days_sales'] = (int)$value;
    		          break;
    		      case 'txtInventoryDays':
    		          $buyback_groups_settings_arr['bgrp_inventory_days'] = (int)$value;
    		          break;
    		      case 'txtMaxInvSpacePercentage':
    		          $buyback_groups_settings_arr['bgrp_max_inv_space_percentage'] = round((float)$value, 4);
    		          break;
    		  }
    		}
            //validate now
            $errors_found = false;

            if ($_POST['rdoSalesRetrievalMethod'] == 'by_date_range') {
                if (tep_day_diff($_POST['txtSalesStartDate'], (trim($_POST['txtSalesEndDate']) ? $_POST['txtSalesEndDate'] : date('Y-m-d')) ) <= 0) {
                    $messageStack->add_session(MESSAGE_INVALID_START_END_DATE_SINGLE);
                    $errors_found = true;
                }
            } elseif ($_POST['rdoSalesRetrievalMethod'] == 'by_last_n_days') {
                if ((int)$_POST['txtLastNDaysSales'] <= 0) {
                    $messageStack->add_session(MESSAGE_INVALID_NUM_DAYS_SALES);
                    $errors_found = true;
                }            
            }
            
            

            if ($errors_found) {
                
                $messageStack->add_session(MESSAGE_ERROR_SO_ABORT_GENERAL);
    			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));                
            
            } else {
            
                if ($action == "insert_product_tags") {
                	tep_db_perform(TABLE_BUYBACK_GROUPS_TAGS_INFO, $buyback_groups_tags_info_arr);
                    $tag_info_id = tep_db_insert_id();
                } else {
                	tep_db_perform(TABLE_BUYBACK_GROUPS_TAGS_INFO, $buyback_groups_tags_info_arr, 'update', ' buyback_groups_tags_info_id=\''.$_REQUEST["tID"].'\';');
                    $tag_info_id = $_REQUEST["tID"];
                }
                $buyback_groups_setting_delete_sql = "DELETE FROM " . TABLE_BUYBACK_SETTING . " WHERE buyback_setting_reference_id='{$tag_info_id}' 
                                                        AND buyback_setting_table_name='".TABLE_BUYBACK_GROUPS_TAGS_INFO."' AND buyback_setting_key IN ('".implode("','", array_keys($buyback_groups_settings_arr))."')";
                $buyback_groups_setting_result_sql = tep_db_query($buyback_groups_setting_delete_sql);
                foreach ($buyback_groups_settings_arr as $key => $value) {
                    tep_db_perform(TABLE_BUYBACK_SETTING, array('buyback_setting_reference_id' => $tag_info_id, 'buyback_setting_table_name' => TABLE_BUYBACK_GROUPS_TAGS_INFO, 'buyback_setting_key' => $key, 'buyback_setting_value' => tep_db_prepare_input($value)));
                }        
    
    			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
            }
			break;
		case "delete_product_tags":
			if ((int)($_REQUEST["tID"]) > 0) {
				$select_brackets_sql = "SELECT buyback_bracket_tags_id from buyback_groups_tags where buyback_groups_tags_info_id = '".(int)($_REQUEST["tID"])."';";
				$select_brackets_sql_result = tep_db_query($select_brackets_sql);
				
				while ($row = tep_db_fetch_array($select_brackets_sql_result)) {
					// clean all the brackets
					tep_db_query("DELETE from buyback_bracket_tags where buyback_bracket_tags_id = '".$row['buyback_bracket_tags_id']."';");
				}
				
				tep_db_query("DELETE FROM ".TABLE_BUYBACK_GROUPS_TAGS_INFO." WHERE buyback_groups_tags_info_id='" . (int)$_REQUEST["tID"] . "';");
				tep_db_query("DELETE FROM ".TABLE_BUYBACK_GROUPS_TAGS." WHERE buyback_groups_tags_info_id='" . (int)$_REQUEST["tID"] . "';");
                tep_db_query("DELETE FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_table_name = '".TABLE_BUYBACK_GROUPS_TAGS_INFO."' AND buyback_setting_reference_id = '".tep_db_prepare_input($_REQUEST["tID"])."'");		    
			}
			
			break;
		case "insert_bracket":
		case "update_bracket":
			if ($action == "insert_bracket") {
				tep_db_query("insert into buyback_bracket_tags (buyback_bracket_tags_quantity,buyback_bracket_tags_value,buyback_bracket_tags_mode)values('".(int)$_REQUEST['buyback_bracket_quantity']."','".(double)$_REQUEST['buyback_bracket_value']."','".(int)$_REQUEST['buyback_bracket_type']."');");
				$buyback_bracket_id = tep_db_insert_id();
				
				tep_db_query("insert into buyback_groups_tags (buyback_groups_id,buyback_bracket_tags_id,buyback_groups_tags_info_id) values ('".$_REQUEST["gID"]."','".$buyback_bracket_id."','".$_REQUEST["tID"]."');");
			} else {
				$buyback_bracket_data_array = array('buyback_bracket_tags_id' => (int)$_REQUEST["tID"],
												'buyback_bracket_tags_quantity' => (int)$_REQUEST['buyback_bracket_quantity'],
												'buyback_bracket_tags_value' => (double)$_REQUEST['buyback_bracket_value'],
						               			'buyback_bracket_tags_mode' => (int)$_REQUEST['buyback_bracket_type']
						               			);
				tep_db_perform("buyback_bracket_tags",$buyback_bracket_data_array, 'update', ' buyback_bracket_tags_id=\''.(int)$_REQUEST["tID"].'\';');
			}
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
		case "delete_bracket":
			if (tep_not_null($_REQUEST["tID"])) {
				$bracket_delete_sql = "DELETE FROM buyback_bracket_tags WHERE buyback_bracket_tags_id='" . (int)$_REQUEST["tID"] . "';";
				tep_db_query($bracket_delete_sql);
				$bracket_delete_sql = "DELETE FROM buyback_groups_tags WHERE buyback_bracket_tags_id='" . (int)$_REQUEST["tID"] . "';";
				tep_db_query($bracket_delete_sql);
			}
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
		case "assign_groups_confirm":
			$g2c_delete_sql = "DELETE FROM buyback_groups_to_categories WHERE buyback_groups_id = '" . $_REQUEST["gID"] . "'";
			tep_db_query($g2c_delete_sql);
			
			$selected_cat_array = array();
			if ($_REQUEST["browser_id"] == "IE") {
				$selected_cat_array = $_REQUEST["SelectedItem"];
			} else {	// for Non-IE browser
				$selected_cat_array = array_keys(array_filter($_REQUEST["HiddenCat"], "filter_selection"));
			}
			
			if (count($selected_cat_array)) {
				foreach($selected_cat_array as $cat_id) {
					$g2c_insert_sql = "INSERT INTO buyback_groups_to_categories (buyback_groups_id, categories_id) VALUES (".$_REQUEST["gID"].", '".$cat_id."')";
					tep_db_query($g2c_insert_sql);
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
		case "update_product_price":

			$buyback_groups_tags_select_sql = "	SELECT * 
												FROM ".TABLE_BUYBACK_GROUPS_TAGS.", ".TABLE_BUYBACK_GROUPS_TAGS_INFO." 
												WHERE buyback_groups_tags_info.buyback_groups_id = '" . $_REQUEST["selected_gID"] . "' 
													AND buyback_groups_tags_info.buyback_groups_tags_info_id = buyback_groups_tags.buyback_groups_tags_info_id 
												ORDER BY buyback_groups_tags_info.buyback_groups_tags_order ASC;";
			$buyback_groups_tags_result = tep_db_query($buyback_groups_tags_select_sql);

			$g2c_select_sql = "SELECT categories_id,buyback_groups_id FROM " . TABLE_BUYBACK_GROUPS_TO_CATEGORIES . " WHERE buyback_groups_id='" . $_REQUEST["selected_gID"] . "'";
			$g2c_result_sql = tep_db_query($g2c_select_sql);
			$categories_array = array();

			if (tep_db_num_rows($buyback_groups_tags_result)==0) {
			    //buyback group tag does not exist
				while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {
					$categories_id =  (int)$g2c_row["categories_id"];
					array_push($categories_array,$categories_id);
				}

				$sql_buyback_items = "SELECT * FROM buyback;";
				$sql_buyback_items_result = tep_db_query($sql_buyback_items);
				
				while ($row_buyback_items = tep_db_fetch_array($sql_buyback_items_result)) {
					$category_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . (int)$row_buyback_items['products_id'] . "' AND products_is_link=0";
					$category_result_sql = tep_db_query($category_select_sql);
    				$category_id_array = tep_db_fetch_array($category_result_sql);
					
					$current_category_id = (int)$category_id_array['categories_id'];
					
					if (in_array($current_category_id,$categories_array)) {
						tep_db_query("DELETE FROM ".TABLE_BUYBACK_PRODUCTS." where products_id = '".(int)$row_buyback_items['products_id']."';");
						tep_db_query("DELETE FROM ".TABLE_BUYBACK_BRACKET." where buyback_bracket_id = '{$row_buyback_items['buyback_bracket_id']}';");
						tep_db_query("DELETE FROM ".TABLE_BUYBACK." where buyback_id = '{$row_buyback_items['buyback_id']}';");
					}
				}
				
				$categories_array = array();
			} else {
				// loop through all products tags in the group
				while ($buyback_groups_tags_array = tep_db_fetch_array($buyback_groups_tags_result)) {

					// get the friggin categories out
					while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {

						$categories_id =  (int)$g2c_row["categories_id"];
						$buyback_groups_id =  (int)$g2c_row["buyback_groups_id"];
						
						//switch ($buyback_groups_tags_array["buyback_groups_tags_field"]) 
						//{
							// we only have products name now, anyway for future ease
							//case "products_name":
							//       break;
						//}
						$product_select_sql = "	SELECT distinct p.products_id  
												FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON p.products_id=pd.products_id 
												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
													ON pd.products_id=p2c.products_id 
												WHERE p2c.categories_id = '" . $categories_id . "' 
													AND p.products_bundle='' 
													AND p.products_bundle_dynamic='' 
													AND p2c.products_is_link=0 
													AND pd.products_name LIKE '".$buyback_groups_tags_array["buyback_groups_tags_value"]."'";
													/*AND pd.language_id='".(int)$languages_id."' */
						$product_result_sql = tep_db_query($product_select_sql);

						while ($product_row = tep_db_fetch_array($product_result_sql)) {
                            $buybackProductObj = new buyback_product($product_row['products_id']);
                            $buybackProductObj->assign_min_qty((int)$buyback_groups_tags_array['buyback_groups_tags_minimum_buyback']);

                            $tag_info_id = (int)$buyback_groups_tags_array['buyback_groups_tags_info_id'];
                            $buyback_groups_settings_select_sql = "select buyback_setting_key, buyback_setting_value FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_reference_id = '" . $tag_info_id . "' AND buyback_setting_table_name = '" . TABLE_BUYBACK_GROUPS_TAGS_INFO . "'";
                            $buyback_groups_settings_result_sql = tep_db_query($buyback_groups_settings_select_sql);
                            while ($buyback_groups_setting_row = tep_db_fetch_array($buyback_groups_settings_result_sql)) {
                                //For now assuming buyback products have the same settings as buyback groups.
                                $buyback_products_settings_arr[str_replace('bgrp', 'bprd', $buyback_groups_setting_row['buyback_setting_key'])] = $buyback_groups_setting_row['buyback_setting_value'];
                            }                        		
                            $buybackProductObj->assign_buyback_settings($buyback_products_settings_arr);
                            
                            //Delete any/all the brackets belonging to this buyback product.
                            //Mark for deletion only, does not actually delete the record until save() - so we can roll back on errors.
                            $buybackProductObj->set_delete_bracket_all();
                                                   
                            //Assign brackets assigned to this group, to the product
                            $buyback_bracket_arr = array();
    						$buyback_bracket_tags_sql = "SELECT * from ".TABLE_BUYBACK_BRACKET_TAGS.", ".TABLE_BUYBACK_GROUPS_TAGS." where buyback_groups_tags.buyback_groups_id = '".$buyback_groups_id."' and buyback_groups_tags.buyback_bracket_tags_id = buyback_bracket_tags.buyback_bracket_tags_id;";
    						$buyback_bracket_tags_sql_result = tep_db_query($buyback_bracket_tags_sql);
    						while ($buyback_bracket_tags_row = tep_db_fetch_array($buyback_bracket_tags_sql_result)) {
                                //Mark for add only, does not actually add the record until save() - so we can roll back on errors.
                                $buybackProductObj->set_add_edit_bracket($buyback_bracket_tags_row['buyback_bracket_tags_quantity'], $buyback_bracket_tags_row['buyback_bracket_tags_value'], $buyback_bracket_tags_row['buyback_bracket_tags_mode']);    
    						}
                            $buybackProductObj->calculate();
                            $buybackProductObj->save();
                            $buybackProductObj->show_error_warning();
							unset($buybackProductObj);
						}//end while each product
					}//end while each category
				}//end while each buyback group tag
			}//end buyback group tag exist
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_GROUPS));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="Javascript1.2"><!-- // load htmlarea
		// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 Products Description HTML - Head
		        _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
		          var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
		           if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
		            if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
		             if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
		         <?php if (HTML_AREA_WYSIWYG_BASIC_PD == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php } else{ ?> if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php }?>
		
		var config = new Object();  // create new config object
	    config.width = "<?php echo HTML_AREA_WYSIWYG_WIDTH; ?>px";
	    config.height = "<?php echo HTML_AREA_WYSIWYG_HEIGHT; ?>px";
	    config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
		config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
	// --></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            						<?
            							if ($action != "new_buyback_groups") {
            								echo '[ <a href="'.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=new_buyback_groups').'" >'.LINK_ADD_BUYBACK_GROUP.'</a> ]';
            							} else {
            								echo "&nbsp;";
            							}
            						?>
            						</td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "new_buyback_groups" || $action == "edit_buyback_groups") {
	echo tep_draw_form('buyback_groups', FILENAME_BUYBACK_GROUPS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_buyback_groups" ? 'insert_buyback_groups' : 'update_buyback_groups'), 'post', 'onSubmit="return buyback_groups_form_checking();"');
	if ($_REQUEST["gID"]) {
		$buyback_groups_select_sql = "SELECT * FROM buyback_groups WHERE buyback_groups_id='" . (int)$_REQUEST["gID"] . "';";
		$buyback_groups_result_sql = tep_db_query($buyback_groups_select_sql);
		$buyback_groups_row = tep_db_fetch_array($buyback_groups_result_sql);
		
		echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_BUYBACK_GROUP_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('buyback_groups_name', $buyback_groups_row["buyback_groups_name"], 'size="40" id="buyback_groups_name"').'&nbsp;<span class="fieldRequired">*</span>'?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_BUYBACK_GROUP_SORT_ORDER?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('buyback_groups_sort_order', $buyback_groups_row["buyback_groups_sort_order"], 'size="5" id="buyback_groups_sort_order"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_BUYBACK_GROUP_DESCRIPTION?></td>
									<td class="main">
										<?=tep_draw_textarea_field('buyback_groups_description', 'soft', '70', '15', $buyback_groups_row["buyback_groups_description"], ' id="class_description" ')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_buyback_groups" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_BUYBACK_GROUPS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
<?
} else if ($action == "new_product_tags" || $action == "edit_product_tags") {
	
	if ((int)$_REQUEST["gID"] > 0) {
		if ((int)$_REQUEST["tID"] > 0) {
			$buyback_tags_select_sql = "select * from buyback_groups_tags_info where buyback_groups_tags_info_id='".(int)$_REQUEST["tID"]."';";
			$buyback_tags_row = tep_db_fetch_array(tep_db_query($buyback_tags_select_sql));
		}
		
		echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
		echo tep_draw_hidden_field("tID", $_REQUEST["tID"]);

		$matching_field_array = array( 	array ('id' => 0, "text" => "--------------- Select Field ---------------"),
    									array ('id' => "products_name", "text" => "Product Name"));

        if ($action == "new_product_tags") {
            $tag_info_id = 0;
            $buyback_groups_settings_arr = array();
        } else {    									
            $tag_info_id = (int)$_REQUEST["tID"];
            $buyback_groups_settings_select_sql = "select buyback_setting_key, buyback_setting_value FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_reference_id = '" . $tag_info_id . "' AND buyback_setting_table_name = '" . TABLE_BUYBACK_GROUPS_TAGS_INFO . "'";
            $buyback_groups_settings_result_sql = tep_db_query($buyback_groups_settings_select_sql);
            while ($buyback_groups_setting_row = tep_db_fetch_array($buyback_groups_settings_result_sql)) {
                $buyback_groups_settings_arr[$buyback_groups_setting_row['buyback_setting_key']] = $buyback_groups_setting_row['buyback_setting_value'];
            }
        }

    	//-----Start Min-Max Form (For Batch Update)---------------------------------------------
    	$html = tep_draw_form('buyback_product_tags', FILENAME_BUYBACK_GROUPS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_product_tags" ? 'insert_product_tags' : 'update_product_tags'), 'post', 'onSubmit="return buyback_product_tags_form_checking(this);"');
       	$html .= '
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                	            <tr><td colspan="2">
                					<table cellpadding="4" cellspacing="0"  border="0"><!-- Open outer 2-->
                        				<tr valign="top" bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">'.ENTRY_PRODUCT_TAGS_FIELD.'</td>
                							 <td class="reportRecords" align="left">'.tep_draw_pull_down_menu("txtProductTagsField", $matching_field_array, $buyback_tags_row['buyback_groups_tags_field'], ' id="txtProductTagsField" ').'&nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>    
                        				<tr valign="top" bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">'.ENTRY_PRODUCT_NAME.'</td>
                							 <td class="reportRecords" align="left">'.tep_draw_input_field('txtProductTagsName', $buyback_tags_row['buyback_groups_tags_value'], 'size="16" id="txtProductTagsName"').'&nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>
                        				<tr valign="top" bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">'.ENTRY_TAG_ORDER.'</td>
                							 <td class="reportRecords" align="left">'.tep_draw_input_field('txtProductTagsOrder', $buyback_tags_row['buyback_groups_tags_order'], 'size="16" id="txtProductTagsOrder"').'&nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>
                    
                        				<tr><td colspan="2" class="ordersBoxHeading"><b>'.TEXT_MIN_BUYBACK_QUANTITY.'</b></td></tr>
                        				<tr valign="top" bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">'.TEXT_OVERWRITE.'</td>
                							 <td class="reportRecords" align="left">'.tep_draw_input_field("txtMinQtyOverwrite", "{$buyback_tags_row["buyback_groups_tags_minimum_buyback"]}", ' id="txtMinQtyOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'&nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>
                        				
                        				<tr><td colspan="2" class="ordersBoxHeading"><b>'.TEXT_MAX_BUYBACK_QUANTITY.'</b></td></tr>
                    
                    				    <tr valign="top" bgcolor="#D7D5D0">
                    				        <td class="dataTableContent">'.tep_draw_radio_field('rdoMaxQtySource', 'overwrite', ($buyback_groups_settings_arr['bgrp_max_qty_source'] == 'overwrite' ? true : false), '', ' id="rdoMaxQtySource1" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_OVERWRITE.'</td>
                    				        <td class="reportRecords" align="left">'.tep_draw_input_field("txtMaxQtyOverwrite", "{$buyback_groups_settings_arr['bgrp_max_qty_overwrite']}", ' id="txtMaxQtyOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'</td></tr>
                    				        
                        				<tr valign="top" bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">'.tep_draw_radio_field('rdoMaxQtySource', 'system_defined', ($buyback_groups_settings_arr['bgrp_max_qty_source'] == 'system_defined' || (!isset($buyback_groups_settings_arr['bgrp_max_qty_source'])) ? true : false), '', ' id="rdoMaxQtySource2" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_SYSTEM_DEFINE.'</td>
                                             <td class="dataTableContent" align="left">&nbsp;</td></tr>		
                
                        				<tr><td colspan="3" class="ordersBoxHeading"><b>'.TEXT_MAX_INV_SPACE.'</b></td></tr>
                        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.TEXT_USE_GREATER_BETWEEN.'</td></tr>
                        				<tr bgcolor="#D7D5D0">
                        				     <td class="dataTableContent">1. &nbsp;'.TEXT_OVERWRITE.'</td>
                        				     <td class="reportRecords" align="left">'. tep_draw_input_field("txtMaxInvSpaceOverwrite", ($buyback_groups_settings_arr['bgrp_max_inv_space_overwrite'] > 0 ? "{$buyback_groups_settings_arr['bgrp_max_inv_space_overwrite']}" : '0'), ' id="txtMaxInvSpaceOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'</td>
                        				</tr>
                        				
                						<tr bgcolor="#D7D5D0">
                						     <td class="dataTableContent">2. &nbsp;'.TEXT_SYSTEM_DEFINE.'</td>                						
                    						 <td class="dataTableContent" align="left">&nbsp;</td>
                    				    </tr>

                        				<tr bgcolor="#D7D5D0" valign="top">
                        				     <td class="dataTableContent">&nbsp;</td>
                        				     <td class="reportRecords" align="left">
                        						<table border="0" cellspacing="0" cellpadding="2">
                        							<tr valign="top">
                        							 <td class="dataTableContent"><b>'.TABLE_HEADING_SALES_RETRIEVAL_METHOD.'</b>
                                						<table border="0" cellspacing="0" cellpadding="2">
                        							     <tr><td colspan="2" class="dataTableContent">'.tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_date_range', ($buyback_groups_settings_arr['bgrp_sales_retrieval_method']=='by_date_range' ? true : (!isset($buyback_groups_settings_arr['bgrp_sales_retrieval_method']) ? true : false)), '', ' id="rdoSalesRetrievalMethod1" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE.'</td></tr>
                                                         <tr valign="top">
                                                            <td>&nbsp;&nbsp;&nbsp;</td>
                                                            <td class="dataTableContent" align="left">'.TABLE_HEADING_SALES_START_DATE.'<br>'.tep_draw_input_field('txtSalesStartDate', (isset($buyback_groups_settings_arr['bgrp_sales_start_date']) ? $buyback_groups_settings_arr['bgrp_sales_start_date'] : ''), ' id="txtSalesStartDate" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback_product_tags.txtSalesStartDate); }" onKeyPress="return noEnterKey(event)" ')
                                                              .'<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback_product_tags.txtSalesStartDate);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>&nbsp;<span class="fieldRequired">*</span>
                                                              <br>'.TABLE_HEADING_SALES_END_DATE.'
                                                              <br><i>Default: Current Date</i>'.                                                              
                                                              '<br>'.tep_draw_input_field('txtSalesEndDate', (isset($buyback_groups_settings_arr['bgrp_sales_end_date']) ? $buyback_groups_settings_arr['bgrp_sales_end_date'] : ''), ' id="txtSalesEndDate" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback_product_tags.txtSalesEndDate); }" onKeyPress="return noEnterKey(event)" ')
                                                              .'<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback_product_tags.txtSalesEndDate);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
                                                            </td>
                                                         </tr>
                        							     <tr><td colspan="2" class="dataTableContent">'.tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_last_n_days', ($buyback_groups_settings_arr['bgrp_sales_retrieval_method']=='by_last_n_days' ? true : false), '', ' id="rdoSalesRetrievalMethod2" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TABLE_HEADING_SALES_RETRIEVE_BY_LAST
                                                              .'&nbsp;'.tep_draw_input_field('txtLastNDaysSales', (isset($buyback_groups_settings_arr['bgrp_last_n_days_sales']) ? $buyback_groups_settings_arr['bgrp_last_n_days_sales'] : '0'), ' id="txtLastNDaysSales" size="4" maxlength="4"')
                                                              .'&nbsp;'.TEXT_DAYS.'</td></tr>
                                                        </table>
                        							 </td>
                        							</tr>
                        						</table>        				     
                        				     </td>
                        				</tr>
                        				<tr bgcolor="#D7D5D0" valign="top">
                        				     <td class="dataTableContent">&nbsp;</td>
                        				     <td class="reportRecords" align="left"><b>'.TABLE_HEADING_DAYS_INVENTORY.'</b><br>'.tep_draw_input_field('txtInventoryDays', (isset($buyback_groups_settings_arr['bgrp_inventory_days']) ? $buyback_groups_settings_arr['bgrp_inventory_days'] : ''), 'size="16" id="txtInventoryDays" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'&nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>
                           				<tr bgcolor="#D7D5D0" valign="top">
                        				     <td class="dataTableContent">&nbsp;</td>
                        				     <td class="reportRecords" align="left"><b>'.TABLE_HEADING_PERCENTAGE_OF_INV_SPACE.'</b><br>'.tep_draw_input_field('txtMaxInvSpacePercentage', (isset($buyback_groups_settings_arr['bgrp_max_inv_space_percentage']) ? $buyback_groups_settings_arr['bgrp_max_inv_space_percentage'] : '100'), 'size="16" id="txtMaxInvSpacePercentage" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value))) ) { this.value = \'100\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').' &#37; &nbsp;<span class="fieldRequired">*</span></td>
                        				</tr>
                                        

                        		    </table><!-- Close outer 2-->
                	            </td>
                	            </tr>
								<tr>
			        				<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										'.($action=="new_product_tags" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_BUYBACK_GROUPS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>' .'
									</td>
								</tr>
							</table>
           				</td>
        			</tr>
        		</form>';
    //-----End Min-Max Form -----------------------------------------------	
        echo $html;
    
        $js_str = '';
        switch($buyback_groups_settings_arr['bgrp_max_qty_source']) {
            case 'overwrite':
                $js_str .= "enableFieldSet('rdoMaxQtySource1');";
                break;
            case 'system_defined':
                $js_str .= "enableFieldSet('rdoMaxQtySource2');";
                break;
            default:
                $js_str .= "enableFieldSet('rdoMaxQtySource2');";            
                break;
        }
        $js_str .= "enableFieldSet('MaxInvSource');";
        switch($buyback_groups_settings_arr['bgrp_sales_retrieval_method']) {
            case 'by_date_range':
                $js_str .= "enableFieldSet('rdoSalesRetrievalMethod1');";
                break;
            case 'by_last_n_days':
                $js_str .= "enableFieldSet('rdoSalesRetrievalMethod2');";
                break;
            default:
                $js_str .= "enableFieldSet('rdoSalesRetrievalMethod1');";
                break;
        }        
?>    
        <script type="text/javascript">
        <!--
        	function buyback_product_tags_form_checking(form) {
                //Validate Matching Field
                if (document.getElementById('txtProductTagsField').selectedIndex < 1) {
                	alert('Please select your matching field!');
                	document.getElementById('txtProductTagsField').focus();
                	return false;
                }
                //Validate Tag Name
                if (document.getElementById('txtProductTagsName').value == "") {
                	alert('Please assign name/value to this tag!');
                	document.getElementById('txtProductTagsName').focus();
                	return false;
                }
                //Validate Order
                if (document.getElementById('txtProductTagsOrder').value == "") {
                	alert('Please assign order value to this tag!');
                	document.getElementById('txtProductTagsOrder').focus();
                	return false;
                }
        	    
        		//Validate Max Qty
        		var intMaxQtyOverwrite = 0;
        		var intMinQtyOverwrite = 0;
        
                intMinQtyOverwrite = parseInt(document.getElementById('txtMinQtyOverwrite').value);
        		if (!intMinQtyOverwrite > 0 || isNaN(intMinQtyOverwrite)) {
        			alert("Minimum buyback quantity should be greater than 0.");
        			return false;
        		}
        		if (document.getElementById('rdoMaxQtySource1').checked) {
        		    intMaxQtyOverwrite = parseInt(document.getElementById('txtMaxQtyOverwrite').value);
                    if (!intMaxQtyOverwrite > 0 || isNaN(intMaxQtyOverwrite)) {
                        alert('Overwrite Maximum buyback quantity should be greater than 0.');
                        return false;
                    } else if (intMaxQtyOverwrite < intMinQtyOverwrite){
                        alert('Maximum buyback quantity '+intMaxQtyOverwrite+' should be greater than Minimum buyback quantity '+intMinQtyOverwrite+'.');
                        return false;
                    }                    
        		} else if (document.getElementById('rdoMaxQtySource2').checked) {
        		    //do nothing
        		} else {
                    alert('Please select Maximum Buyback type.');
                    return false;
        		}
        
        		//Validate Max Inv Space
        		//Overwrite
                    if (isNaN(document.getElementById('txtMaxInvSpaceOverwrite').value)) {
                        document.getElementById('txtMaxInvSpaceOverwrite').value = 0;
                    } else {
            		    var intMaxInvSpaceOverwrite = parseInt(document.getElementById('txtMaxInvSpaceOverwrite').value);
                        if (intMaxInvSpaceOverwrite < 0) {
                            alert('Overwrite Maximum Inventory Space should be a positive number.');
                            return false;
                        }
                    }
                //System Defined
            		if (document.getElementById('rdoSalesRetrievalMethod1').checked) {
            		    //By date range
                        if (trim_str(document.getElementById('txtSalesStartDate').value) == '') {
                            alert('Invalid Sales Start Date.');
                            return false;
                        }
            		} else if (document.getElementById('rdoSalesRetrievalMethod2').checked){
            		    //By Last X Days
                        if (!parseInt(document.getElementById('txtLastNDaysSales').value) > 0) {
                            alert('Last [X] days of sales should be greater than 0.');
                            return false;
                        }
            		}
            		if (!parseInt(document.getElementById('txtInventoryDays').value) > 0) {
                        alert('No. Days Inventory should be greater than 0.');
                        return false;
            		}
            		if (!parseInt(document.getElementById('txtMaxInvSpacePercentage').value) > 0) {
                        alert('Percentage of quantity should be greater than 0.');
                        return false;
            		}
        	}        
           	function enableFieldSet(setName) {
        	    var fld_id_enable_arr = new Array();
        	    var fld_id_disable_arr = new Array();
                switch (setName) {
                    case 'rdoMaxQtySource1':
                        fld_id_enable_arr.push('txtMaxQtyOverwrite');
                        break;
                    case 'rdoMaxQtySource2':
                        fld_id_disable_arr.push('txtMaxQtyOverwrite');
                        break;
                    case 'MaxInvSource':
                        fld_id_enable_arr.push('txtMaxInvSpaceOverwrite', 'txtInventoryDays', 'txtMaxInvSpacePercentage');
                        break;         
                    case 'rdoSalesRetrievalMethod1':
                        fld_id_disable_arr.push('txtLastNDaysSales');
                        fld_id_enable_arr.push('txtSalesStartDate', 'txtSalesEndDate');
                        break;
                    case 'rdoSalesRetrievalMethod2':
                        fld_id_enable_arr.push('txtLastNDaysSales');
                        fld_id_disable_arr.push('txtSalesStartDate', 'txtSalesEndDate');
                        break;
                }
                setFieldState(fld_id_enable_arr, false);
                setFieldState(fld_id_disable_arr, true);
        	}
        	
        	function setFieldState(fld_id_arr, isDisable) {
                for (i=0;i<fld_id_arr.length;i++) {
                    document.getElementById(fld_id_arr[i]).disabled = isDisable;
                }
        	}
        	
            <?=$js_str?>
        	
        //-->
        </script>

<?php
	}
} else if ($action == "linking_to_group") {
	function tep_show_list_items($ListItems, $Level=0) {
		global $languages_id, $HiddenItems, $g2c_array;
		$SubTotal=0;
		
		foreach ($ListItems as $ListItem) {
			$NewListItems = array() ;
			$parent_to_child = array();
			
			$p_rank = tep_check_cat_tree_permissions(FILENAME_BUYBACK_GROUPS, $ListItem["categories_id"]);
			
			if ($p_rank > 0) {
				$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
										AND cd.language_id='" . (int)$languages_id ."' 
									ORDER BY sort_order, cd.categories_name " ;
				$cat_result_sql = tep_db_query($cat_select_sql);
				while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
					$NewListItems[] = $cat_row ;
					$parent_to_child[] = $cat_row["categories_id"];
				}
				
				$SubTotal += 1 ;
				$DisplayName = htmlspecialchars(strip_tags($ListItem["categories_name"]));
				if (!$DisplayName) $DisplayName = "" ;
			    
			    if (count($NewListItems)) {
			    	$DisplayName .= '&nbsp;&nbsp;[<a href="javascript:;" onClick=" expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'assign_tag\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', true, \''.implode(",", $parent_to_child).'\');">Check All</a>]&nbsp;&nbsp;[<a href="javascript:;" onClick="expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'assign_tag\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', false, \''.implode(",", $parent_to_child).'\');">Uncheck All</a>]';
			    }
			    
			    if ($p_rank == 1) {
				    $prod_select_sql = "SELECT products_id 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
										WHERE categories_id='" . $ListItem["categories_id"] . "' 
											AND products_is_link=0 " ;
					$prod_result_sql = tep_db_query($prod_select_sql);
					if (tep_db_num_rows($prod_result_sql)) {
						$checked_value = (in_array($ListItem["categories_id"], $g2c_array) ? " checked " : "");
						$DisplayName = '<input type="checkbox" name=SelectedItem[] value="'.$ListItem["categories_id"].'" onClick="assign_selection(this); if (this.checked) { expandTree(aux'.$ListItem["categories_id"].');}" id=MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"]. $checked_value . '>' . $DisplayName;
						
						$HiddenItems[] = array('id' => $ListItem["categories_id"], 'value' => (tep_not_null($checked_value) ? 1 : 0));
					}
				}
				
				$res_dups = tep_db_query("SELECT * from buyback_groups_to_categories as a where a.categories_id='".$ListItem["categories_id"]."' and a.buyback_groups_id!='".(int)$_GET['gID']."';");
				
				$count_records = 0;
				
				if (tep_db_num_rows($res_dups)) {
					$conflictString = "&nbsp;[<font color='red'>Assigned to: ";
					
					while ($res_dups_row = tep_db_fetch_array($res_dups)) {
						$count_records++;
						$temp_name_res = tep_db_query("SELECT buyback_groups_name from buyback_groups where buyback_groups_id='$res_dups_row[buyback_groups_id]';");
						
						$temp_name_row = tep_db_fetch_array($temp_name_res);
						
						if ($count_records == tep_db_num_rows($res_dups)) {
							$conflictString.= "".$temp_name_row['buyback_groups_name'];
						} else {
							$conflictString.= "".$temp_name_row['buyback_groups_name'].", ";
						}
					}
					
					$conflictString.= "</font>]";
				} else {
					$conflictString = '';
				}
?>
				aux<?=$ListItem["categories_id"]?> = insFld(<?=($Level==1?"foldersTree":"aux".$ListItem["parent_id"])?>, gFld("<?
				// Text
				echo addslashes($DisplayName).$conflictString?>",<?
				// Link
				if ($url) {?>
					"javascript:MyNewWindow(\"<?=$url?>\",\"Open\",<?=$this->PopupWinWidth?>,<?=$this->PopupWinHeight?>,\"yes\")"))
				<?} else {?>
					"javascript:undefined"))
				<?}?>
				aux<?=$ListItem["categories_id"]?>._readonly = 0;
				<?
				$SubTotal += tep_show_list_items($NewListItems, $Level+1) ;
			}
		}
		return $SubTotal ;
	}
	
	$g2c_array = array();
	$g2c_select_sql = "	SELECT categories_id  
						FROM buyback_groups_to_categories 
						WHERE buyback_groups_id='" . $_REQUEST["gID"] . "';";
	$g2c_result_sql = tep_db_query($g2c_select_sql);
	while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {
		$g2c_array[] = $g2c_row["categories_id"];
	}
	
	$ListItems = array();
	$HiddenItems = array();
	$parent_to_child = array();
	
	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
						FROM " . TABLE_CATEGORIES . " AS c 
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
							ON c.categories_id = cd.categories_id 
						WHERE c.parent_id='0' 
							AND cd.language_id='" . (int)$languages_id ."' 
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);
	while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
		$parent_to_child[] = $cat_row["categories_id"];
	}
	
	$matching_field_array = array( 	array ('id' => 0, "text" => "--------------- Select Field ---------------"),
									array ('id' => "products_name", "text" => "Products Name")
								);
	
	echo tep_draw_form('assign_tag', FILENAME_BUYBACK_GROUPS, tep_get_all_get_params(array('action')) . 'action=assign_groups_confirm', 'post', 'onSubmit="return assign_tag_form_checking();"');
	echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	echo tep_draw_hidden_field("browser_id", '', ' id="browser_id" ');
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			        				<td width="100%">
			    	   				<script language="javascript">
									<!--
									var browserName = navigator.appName; 
									if (browserName == 'Microsoft Internet Explorer')
										document.getElementById('browser_id').value = "IE";
									else
										document.getElementById('browser_id').value = "Non-IE";
									//-->
									</script>
										<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
										<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
										<script>
											function checkAll(folderObj) {
												var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen) {
											      	clickOnNodeObj(folderObj)
											    }
											}
											
											function expandTree(folderObj)
											{
											    var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen)
											      	clickOnNodeObj(folderObj)
												/*
											    // Call this function for all folder children
											    for (i=0 ; i < folderObj.nChildren; i++)  {
											      childObj = folderObj.children[i]
											      if (typeof childObj.setState != "undefined" && !childObj._readonly) {//is folder
											        expandTree(childObj)
											      }
											    }*/
											}
													
											// Close all folders
											function collapseTree()
											{
												//hide all folders
												clickOnNodeObj(foldersTree)
												//restore first level
												clickOnNodeObj(foldersTree)
											}
											
											//Environment variables are usually set at the top of this file.
											USELINKFORFOLDER = 0
											USETEXTLINKS = 0
											STARTALLOPEN = 0
											USEFRAMES = 0
											USEICONS = 0
											WRAPTEXT = 1
											PRESERVESTATE = 1
											ICONPATH = 'includes/javascript/Treeview/'
											BUILDALL = 0
											HIGHLIGHT = 0;
											foldersTree = gFld("<?=TEXT_TOP?>&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('assign_tag', 'MSel_-1_0', true, '<?=implode(",", $parent_to_child)?>');\">Check All</a>]&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('assign_tag', 'MSel_-1_0', false, '<?=implode(",", $parent_to_child)?>');\">Uncheck All</a>]", "");
											<?
												$SubTotal = tep_show_list_items($ListItems, 1);
											?>
											
											
										</script>
										<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
										<span class=formvalue>
											<script>initializeDocument()</script>
											<noscript>
											A tree for site navigation will open here if you enable JavaScript in your browser.
											</noscript>
										</span>
									</td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_image_submit('button_update.gif', IMAGE_UPDATE, "") . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_BUYBACK_GROUPS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
							<?
								for ($i=0; $i<count($HiddenItems); $i++) {
									echo "<input type='hidden' name=HiddenCat[".$HiddenItems[$i]['id']."] value=".$HiddenItems[$i]['value']." id=HiddenCat_".$HiddenItems[$i]['id'].">\n";
								}
							?>
						</td>
					</tr>
					</form>
<?
} else if($action == "new_bracket" || $action == "edit_bracket") {
	echo tep_draw_form('buyback_brackets', FILENAME_BUYBACK_GROUPS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_bracket" ? 'insert_bracket' : 'update_bracket'), 'post', 'onSubmit="return buyback_bracket_form_checking();"');
	
	if ($action == "edit_bracket") {
		if ((int)$_REQUEST["tID"] > 0) {
			$buyback_bracket_select_sql = "SELECT * FROM buyback_bracket_tags WHERE buyback_bracket_tags_id='" . (int)$_REQUEST["tID"] . "';";
			$buyback_bracket_result_sql = tep_db_query($buyback_bracket_select_sql);
			$buyback_bracket_row = tep_db_fetch_array($buyback_bracket_result_sql);
		}
	}
	
	echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	echo tep_draw_hidden_field("tID", $_REQUEST["tID"]);
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_BUYBACK_BRACKET_QUANTITY?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('buyback_bracket_quantity', $buyback_bracket_row["buyback_bracket_tags_quantity"], 'size="40" id="buyback_bracket_quantity"').'&nbsp;<span class="fieldRequired">*</span>'?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_BUYBACK_BRACKET_VALUE?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('buyback_bracket_value',  number_format($buyback_bracket_row["buyback_bracket_tags_value"], DISPLAY_PRICE_DECIMAL), 'size="40" id="buyback_bracket_value"').'&nbsp;<span class="fieldRequired">*</span>'?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_BUYBACK_BRACKET_TYPE?></td>
									<td class="main">
									<? 	$bracket_types = array( array ('id' => 0, "text" => "$"),
																array ('id' => "1", "text" => "%"));
										
										echo tep_draw_pull_down_menu("buyback_bracket_type", $bracket_types, $buyback_bracket_row['buyback_bracket_tags_mode'], ' id="buyback_bracket_type" ').'&nbsp;<span class="fieldRequired">*</span>';
									?>
									</td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_bracket" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_BUYBACK_GROUPS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
<?
}

$buyback_groups_select_sql = "SELECT * FROM buyback_groups order by buyback_groups_sort_order,buyback_groups_name;" ;
$buyback_groups_result_sql = tep_db_query($buyback_groups_select_sql);

if (tep_db_num_rows($buyback_groups_result_sql)) {
?>
								<tr>
			            			<td valign="top">
			            				<table border="0" width="100%" cellspacing="1" cellpadding="2">
			               					<tr>
			               						<td class="reportBoxHeading" width="5%"><?="Action"?></td>
						       					<td class="reportBoxHeading"><?=TABLE_HEADING_BUYBACK_GROUPS_NAME?></td>
						       					<td class="reportBoxHeading"><?=TABLE_HEADING_BUYBACK_SORT_ORDER?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_BUYBACK_GROUPS_DESCRIPTION?></td>								                
								                <td class="reportBoxHeading"><?=TABLE_HEADING_BRACKETS?></td>
						   					</tr>
<?
	$row_count = 0;
	while ($buyback_groups_row = tep_db_fetch_array($buyback_groups_result_sql)) {
    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    	
    	$count_cat_select_sql = " SELECT COUNT(buyback_groups_id) AS total_linked_cat FROM buyback_groups_to_categories WHERE buyback_groups_id='" . $buyback_groups_row["buyback_groups_id"] . "';";
    	$count_cat_result_sql = tep_db_query($count_cat_select_sql);
    	if ($count_cat_row = tep_db_fetch_array($count_cat_result_sql)) {
    		$linked_cat_count = $count_cat_row["total_linked_cat"];
    	} else {
    		$linked_cat_count = 0;
    	}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
												<td class="reportRecords" valign="top">
													<table border="0" width="100%" cellspacing="0" cellpadding="0">
														<tr>
															<td valign="top" align="center">
<?		if((int)$buyback_groups_row['buyback_groups_status']>=1) { ?>
																<a href="<?=tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=edit_buyback_groups&gID='.$buyback_groups_row["buyback_groups_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>&nbsp;
																<a href="<?=tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=linking_to_group&gID='.$buyback_groups_row["buyback_groups_id"])?>"><?=tep_image(DIR_WS_ICONS."linking.gif", "Assign categories to tag", "", "", 'align="top"')?></a>&nbsp;
																<a href="javascript:void(confirm_delete('<?=$buyback_groups_row["buyback_groups_name"]?>', 'Buyback Group', '<?=tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=delete_buyback_groups&gID='.$buyback_groups_row["buyback_groups_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
<?		} ?>
															</td>
														</tr>
														<tr>
									        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									      				</tr>
														<tr>
															<td valign="bottom">
<?		if ($linked_cat_count && (int)$buyback_groups_row['buyback_groups_status'] >= 1) {	// got at least one category is linked
			echo tep_draw_form('form_'.$buyback_groups_row["buyback_groups_id"], FILENAME_BUYBACK_GROUPS, tep_get_all_get_params(array('action')) . 'action=update_product_price', 'post' /*,'onSubmit="return update_price_form_checking(\''.$buyback_groups_row["buyback_groups_id"].'\');"'*/);
			echo tep_draw_hidden_field("selected_gID", $buyback_groups_row["buyback_groups_id"]);
			echo tep_image_submit('button_update.gif', 'Update all brackets for this group.');
			echo "</form>";
		}
?>
															</td>
														</tr>
													</table>
												</td>
												<td class="reportRecords" valign="top"><?=$buyback_groups_row["buyback_groups_name"]?><br><?=sprintf(TEXT_TOTAL_LINKED_CATEGORIES, $linked_cat_count)?><br><?echo ((int)$buyback_groups_row["buyback_groups_status"]==0 ? TEXT_BUYBACK_GROUP_DISABLED.", <a href='".tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=change_status_buyback_groups&gID='.$buyback_groups_row["buyback_groups_id"])."&buyback_group_status=1'>".TEXT_ENABLE_GROUP."</a>?" : "<a href='".tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=change_status_buyback_groups&gID='.$buyback_groups_row["buyback_groups_id"])."&buyback_group_status=0'>[".TEXT_DISABLE_GROUP."]</a>" );?><br><br></td>
												<td class="reportRecords" valign="top"><?=$buyback_groups_row["buyback_groups_sort_order"]?></td>
												<td class="reportRecords" valign="top"><?=$buyback_groups_row["buyback_groups_description"]?></td>
												<td class="reportRecords" valign="top">
													<table border="0" width="100%" cellspacing="1" cellpadding="1">
														<tr>
															<td class="dataTableContent"><? echo '[<a href="'.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=new_product_tags&gID='.$buyback_groups_row["buyback_groups_id"]).'" >'.LINK_NEW_PRODUCT.'</a>]';?></td>
														</tr>
<?
		$buyback_tag_info_sql = "SELECT * FROM buyback_groups_tags_info WHERE buyback_groups_id = '" . $buyback_groups_row["buyback_groups_id"] ."' order by buyback_groups_tags_order asc;";
		$buyback_tag_info_result = tep_db_query($buyback_tag_info_sql);
		
		while ($buyback_tag_info_row = tep_db_fetch_array($buyback_tag_info_result)) {
			echo '<tr class="dataTableContent"><td colspan="3"><br><b>'.$buyback_tag_info_row['buyback_groups_tags_value'].' ('.TEXT_MIN_MAX.$buyback_tag_info_row['buyback_groups_tags_minimum_buyback'].'/'.$buyback_tag_info_row['buyback_groups_tags_maximum_buyback'].')</b> <br>[<a href="javascript:void(confirm_delete(\''.$buyback_tag_info_row['buyback_groups_tags_value'].'\', \'product\', \''.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=delete_product_tags&tID='.$buyback_tag_info_row['buyback_groups_tags_info_id']).'\'))">'.LINK_DELETE_PRODUCT.'</a>] [<a href="'.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=edit_product_tags&gID='.$buyback_groups_row["buyback_groups_id"]).'&tID='.$buyback_tag_info_row["buyback_groups_tags_info_id"].'" >'.LINK_EDIT_PRODUCT.'</a>] [<a href="'.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=new_bracket&gID='.$buyback_groups_row["buyback_groups_id"]).'&tID='.$buyback_tag_info_row["buyback_groups_tags_info_id"].'" >'.LINK_NEW_BRACKET.'</a>]</td></tr>';
?>
														<tr>
						          							<td colspan="3">
																<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
															</td>
						          						</tr>
														<tr>
															<td class="dataTableContent"><?=TABLE_HEADING_BRACKET_ACTION?></td>
															<td class="dataTableContent"><?=TABLE_HEADING_BRACKET_QTY?></td>
															<td class="dataTableContent"><?=TABLE_HEADING_BRACKET_VALUE?></td>														
														</tr>
														<tr>
															<td colspan="3">
																<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
															</td>
														</tr>
<?
			$buyback_brackets_select_sql = "SELECT * FROM buyback_groups_tags,buyback_bracket_tags WHERE buyback_groups_id = '" . $buyback_groups_row["buyback_groups_id"] ."' and buyback_groups_tags_info_id = '".$buyback_tag_info_row['buyback_groups_tags_info_id']."' and buyback_groups_tags.buyback_bracket_tags_id = buyback_bracket_tags.buyback_bracket_tags_id order by buyback_bracket_tags.buyback_bracket_tags_quantity;";
			
			$buyback_brackets_result_sql = tep_db_query($buyback_brackets_select_sql);
			$buyback_bracket_count = 0;
			
			while ($buyback_brackets_row = tep_db_fetch_array($buyback_brackets_result_sql)) {
				$sign = ((int)$buyback_brackets_row['buyback_bracket_tags_mode'] == 1) ? "%" : "";
				echo '
						<tr>
							<td class="dataTableContent"><a href="'.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=edit_bracket&tID='.$buyback_brackets_row["buyback_bracket_tags_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;<a href="javascript:void(confirm_delete(\'Selected\', \'bracket \', \''.tep_href_link(FILENAME_BUYBACK_GROUPS, 'action=delete_bracket&tID='.$buyback_brackets_row["buyback_bracket_tags_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a></td>
							<td class="dataTableContent">'.$buyback_brackets_row['buyback_bracket_tags_quantity'].'</td>
							<td class="dataTableContent">'.number_format($buyback_brackets_row['buyback_bracket_tags_value'], DISPLAY_PRICE_DECIMAL).$sign.'</td>														
						</tr>';
				
				$buyback_bracket_count++;
			}
			
			if (!$buyback_bracket_count) {
				echo '	<tr><td colspan="3" class="reportRecords">No brackets defined!</td></tr>';
			}
		}
?>
													</table>
												</td>
											</tr>
<?		$row_count++;
    }
?>
            							</table>
            						</td>
            					</tr>
<?
}
?>
							<script language="javascript"><!--
								function buyback_groups_form_checking() {
									if (document.getElementById('buyback_groups_name').value == "") {
										alert('Please assign name to this buyback group.');
										return false;
									}
									return true;
								}
								
								function buyback_bracket_form_checking()
								{
									if (isNaN(parseInt(document.getElementById('buyback_bracket_quantity').value)) /*|| parseInt(document.getElementById('buyback_bracket_quantity').value)<=0*/) {
										alert('Invalid quantity!');
										document.getElementById('buyback_bracket_quantity').focus();
										return false;
									}
									
									if (isNaN(parseFloat(document.getElementById('buyback_bracket_value').value)) || parseFloat(document.getElementById('buyback_bracket_value').value) <=0) {
										alert('Invalid value!');
										document.getElementById('buyback_bracket_value').focus();
										return false;
									}
									
									return true;
								
								}
								
								function assign_tag_form_checking() {
									/*
									if (document.getElementById('match_field').selectedIndex < 1) {
										alert('Please select your matching field!');
										document.getElementById('match_field').focus();
										return false;
									}
									*/
									return true;
								}
								
								function assign_selection(obj) {
									if (obj.checked == true) {
										document.getElementById('HiddenCat_'+obj.value).value = 1;
									} else {
										document.getElementById('HiddenCat_'+obj.value).value = 0;
									}
								}
								//-->
							</script>
							
            				</table>
            			</td>
            		</tr>
		    	</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>