<?
require('includes/application_top.php');

$action = $_GET['action'];

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>

<script language="javascript">
<!--
	function changeProductCat(x) {
		var objRegExp  = /\?/;
		
		<?	echo 'var goto_url = "'.tep_href_link(FILENAME_BUYBACK_PRODUCTS, tep_get_all_get_params(array("product_id","cID"))).'";' . "\n"; ?>
		if (objRegExp.test(goto_url)) {
			goto_url = goto_url + '&cID=' + x.value;
		} else {
			goto_url = goto_url + '?cID=' + x.value;
		}
				
		document.location.href = goto_url;
	}
	
	function selectRadio(form, val)
	{
		for (i=0;i<form.length;i++) {
			if (form.elements[i].type=="radio" && form.elements[i].value==val) {
				form.elements[i].checked = true;
			}
		}
	}
	
	function submitDrowpDownForm(doSubmit) {
		if (document.frmProducts2.cID.value!=0) {
			if (document.frmProducts2.pID.value==0) {
				alert("Please select a product.");
				return false;
			}
		} else {
			alert("Please select a category");
			return false;
		}
		
		if (doSubmit)	document.frmProducts2.submit();
	}
//-->
</script>
<?
$products_array = array( array('id' => '0', 'text' => '---Select Products---') );

if (isset($_GET['cID']) && tep_not_null($_GET['cID'])) {
	if (tep_check_cat_tree_permissions(FILENAME_BUYBACK_PRODUCTS, (int)$_GET['cID'], $login_groups_id) == 1) {
		$product_select_sql = "	SELECT p.products_id, pd.products_name 
								FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
								INNER JOIN " . TABLE_PRODUCTS . " AS p
									ON p2c.products_id=p.products_id 
								INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
									ON p2c.products_id=pd.products_id 
								WHERE p2c.categories_id='".(int)$_GET['cID']."' 
									AND p.products_bundle='' 
									AND p.products_bundle_dynamic='' 
									AND pd.language_id = '" . (int)$languages_id . "' 
								ORDER BY p.products_sort_order, pd.products_name";
		$product_result_sql = tep_db_query($product_select_sql);
		
		while ($product_row = tep_db_fetch_array($product_result_sql)) {
			$products_array[] = array('id' => $product_row['products_id'], 'text' => $product_row['products_name']);
		}
	}
}

$categories_array = tep_get_eligible_category_tree(FILENAME_BUYBACK_PRODUCTS, 0, '___', '', $categories_array, false, 0, true);
?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
			</td>
			<td valign="top">
				<form name="frmProducts2" action="<?=tep_href_link(FILENAME_BUYBACK_PRODUCTS_BRACKETS, tep_get_all_get_params(array('product_type', 'product_id', 'action', 'cID'))."action=display_brackets")?>" method="post" onSubmit="return submitDrowpDownForm(false)">
				<table width="100%" border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td colspan="3"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
					</tr>
					<tr>
						<td width="10" class="dataTableContent"><?=HEADING_TITLE_SELECT?></td>
						<td colspan="2"><?=tep_draw_pull_down_menu('cID', $categories_array, '', 'onchange=changeProductCat(this); id="cID" ');?></td>
					</tr>
					<tr>
						<td></td>
						<td><?=tep_draw_pull_down_menu('pID', $products_array, '', 'onFocus="selectRadio(this.form,1);"')?></td>
						<td></td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
					</tr>
					<tr>
						<td></td>
						<td></td>
						<td align="right"><?=tep_image_button("button_next.gif", IMAGE_NEXT, "onClick=submitDrowpDownForm(true)")?></td>
					</tr>
				</table>
				</form>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>