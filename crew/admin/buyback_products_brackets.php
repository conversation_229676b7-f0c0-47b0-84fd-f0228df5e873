<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'buyback.php');

$currencies = new currencies();

define('DISPLAY_PRICE_DECIMAL', 6);

$action = strtolower($_GET['action']);
$cat_id = (int)$_REQUEST['cID'];
$products_id = (int)$_REQUEST['pID'];
if (($products_id > 0)) {
    $buybackProductObj = new buyback_product($products_id);    
}
if (tep_check_cat_tree_permissions(FILENAME_BUYBACK_PRODUCTS, $cat_id, $login_groups_id) != 1) {
	$messageStack->add_session(WARNING_CAT_ACCESS_DENIED, 'warning');
	
	tep_redirect(tep_href_link(FILENAME_BUYBACK_PRODUCTS, tep_get_all_get_params(array('action', 'cID', 'pID'))));
	break;
}

switch ($action) {
	// okay now to add the products that is existing
	case "delete_brackets":

		$bracket_id = (int)$_GET['bracket_id'];
		$buybackProductObj->set_delete_bracket($bracket_id);
		$buybackProductObj->calculate();
		$buybackProductObj->save();
        $buybackProductObj->show_error_warning();
		unset($buybackProductObj);
		tep_redirect(tep_href_link(FILENAME_BUYBACK_PRODUCTS_BRACKETS, tep_get_all_get_params(array('action', 'cID', 'pID'))."action=display_brackets&cID=$cat_id&pID=$products_id"));

	case "display_brackets":
		$buyback_row_count = 0;
		
		// If it has not been tampered
		if ($cat_id > 0 && $products_id > 0) {
			
            $buybackProductObj->calculate();        	
        	$buyback_products_settings_arr = $buybackProductObj->buyback_settings;
        	//echo "<br><h1>Debug</h1><pre>";var_dump($buybackProductObj);echo "</pre>";

		} else {
			tep_redirect(tep_href_link(FILENAME_BUYBACK_PRODUCTS, tep_get_all_get_params(array('action', 'cID', 'pID'))));
		}
		break;

	case "update_brackets":
		// of course we need a products_id else we are screwed
		if ($products_id > 0) {
		    
    		$buyback_products_settings_arr = array();
            //now update the object with the form posts.
    		foreach($_POST as $fldname => $value) {
    		  switch ($fldname) {
    		      case 'txtMinQtyOverwrite':
    		          $buybackProductObj->assign_min_qty($value);
    		          break;
    		      case 'rdoMaxQtySource':
    		          $buyback_products_settings_arr['bprd_max_qty_source'] = $value;
                      switch ($value) {   
                          case "overwrite":
                            $buyback_products_settings_arr['bprd_max_qty_overwrite'] = (int)$_POST['txtMaxQtyOverwrite'];
                            break;
                          case "system_defined":
                            //do nothing
                            break;
                      }		          
    		          break;
    		      case 'txtMaxQtyOverwrite':
    		          //do nothing
    		          break;
    		      case 'txtMaxInvSpaceOverwrite':
    		          $buyback_products_settings_arr['bprd_max_inv_space_overwrite'] = (int)$value;
    		          break;
    		      case 'rdoSalesRetrievalMethod':
    		          $buyback_products_settings_arr['bprd_sales_retrieval_method'] = $value;
    		          break;
    		      case 'txtSalesStartDate':
    		          $buyback_products_settings_arr['bprd_sales_start_date'] = $value;
    		          break;
    		      case 'txtSalesEndDate':
    		          $buyback_products_settings_arr['bprd_sales_end_date'] = (trim($value) ? trim($value) : '');
    		          break;
    		      case 'txtLastNDaysSales':
    		          $buyback_products_settings_arr['bprd_last_n_days_sales'] = (int)$value;
    		          break;
    		      case 'txtInventoryDays':
    		          $buyback_products_settings_arr['bprd_inventory_days'] = (int)$value;
    		          break;
    		      case 'txtMaxInvSpacePercentage':
    		          $buyback_products_settings_arr['bprd_max_inv_space_percentage'] = round((float)$value, 4);
    		          break;
    		  }
    		}
    		
            $buybackProductObj->assign_buyback_settings($buyback_products_settings_arr);

    		$txtQty = $_POST['txtQty'];   
    		$txtPrice = $_POST['txtPrice'];
    		$type = $_POST['type'];

            $buyback_brackets_arr = array();
            foreach ($txtQty as $bracket_id => $bracket_qty) {
                if (trim($bracket_qty) != "") {
                    $buybackProductObj->set_add_edit_bracket($bracket_qty, $txtPrice[$bracket_id], $type[$bracket_id], $bracket_id);                 
                }
            }
            
            $buybackProductObj->calculate();
            $buybackProductObj->save();
            $buybackProductObj->show_error_warning();
            
            unset($buybackProductObj);
		} //end products_id exists
		tep_redirect(tep_href_link(FILENAME_BUYBACK_PRODUCTS_BRACKETS, tep_get_all_get_params(array('action', 'cID', 'pID'))."action=display_brackets&cID=$cat_id&pID=$products_id"));
		
		break;
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script type="text/javascript" language="javascript" src="includes/general.js"></script>
<script type="text/javascript" language="javascript" src="includes/javascript/supplier_xmlhttp.js"></script>
<script type="text/javascript">
<!--
    function init() {
        initInfoCaptions();
    }
	function validate(form) {
        //Validate brackets	    
		for (var i=0; i < document.getElementById('hidNumBrackets').value; i++) {
            if (isNaN(document.getElementById("txtQty_" + i).value) || trim_str(document.getElementById("txtQty_" + i).value) == '') {
                alert('Invalid Qty for Bracket ' + (i+1));            
                return false;
            } else if (isNaN(document.getElementById("txtPrice_" + i).value) || trim_str(document.getElementById("txtPrice_" + i).value) == '') {
                alert('Invalid Price for Bracket ' + (i+1));            
                return false;
            }
		}
		//Validate new bracket. Check for stuff which only apply to new brackets
		var bracket_qty_new = document.getElementById('txtQty_new').value;
		if (trim_str(bracket_qty_new) != '' || trim_str(bracket_qty_new) != '') {
            if (isNaN(bracket_qty_new) || trim_str(bracket_qty_new) == '') {
                alert('Invalid Qty for New Bracket.');            
                return false;
            } else {
                //Does this qty already exist ?
        		for (var i=0; i < document.getElementById('hidNumBrackets').value; i++) {
        		    if (bracket_qty_new == document.getElementById("txtQty_" + i).value) {
                        alert('Bracket Quantity ' + bracket_qty_new + ' already exists.');            
                        return false;
        		    }
        		}
            }
            if (isNaN(document.getElementById('txtPrice_new').value) || trim_str(document.getElementById('txtPrice_new').value) == '') {
                alert('Invalid Price for New Bracket.');
                return false;
            }
		}

		//Validate Max Qty
		var intMaxQtyOverwrite = 0;
		var intMinQtyOverwrite = 0;

        intMinQtyOverwrite = parseInt(document.getElementById('txtMinQtyOverwrite').value);
		if (!intMinQtyOverwrite > 0 || isNaN(intMinQtyOverwrite)) {
			alert("Minimum buyback quantity should be greater than 0.");
			return false;
		}
		if (document.getElementById('rdoMaxQtySource1').checked) {
		    intMaxQtyOverwrite = parseInt(document.getElementById('txtMaxQtyOverwrite').value);
            if (!intMaxQtyOverwrite > 0 || isNaN(intMaxQtyOverwrite)) {
                alert('Overwrite Maximum buyback quantity should be greater than 0.');
                return false;
            } else if (intMaxQtyOverwrite < intMinQtyOverwrite){
                alert('Maximum buyback quantity '+intMaxQtyOverwrite+' should be greater than Minimum buyback quantity '+intMinQtyOverwrite+'.');
                return false;
            }
		} else if (document.getElementById('rdoMaxQtySource2').checked) {
		    //Don't do this cos hidMaxQtySystem is only updated if clicked preview button
		    //intMaxQtyOverwrite = parseInt(document.getElementById('hidMaxQtySystem').value);
		} else {
            alert('Please select Maximum Buyback type.');
            return false;
		}

		//Validate Max Inv Space
		//Overwrite
            if(isNaN(document.getElementById('txtMaxInvSpaceOverwrite').value)) {
                document.getElementById('txtMaxInvSpaceOverwrite').value = '0';
            } else {
    		    var intMaxInvSpaceOverwrite = parseInt(document.getElementById('txtMaxInvSpaceOverwrite').value);
                if (intMaxInvSpaceOverwrite < 0) {
                    alert('Overwrite Maximum Inventory Space should be a positive number.');
                    return false;
                }
            }		
        //System Defined
    		if (document.getElementById('rdoSalesRetrievalMethod1').checked) {
    		    //By date range
                if (trim_str(document.getElementById('txtSalesStartDate').value) == '') {
                    alert('Invalid Sales Start Date.');
                    return false;
                }
    		} else if (document.getElementById('rdoSalesRetrievalMethod2').checked){
    		    //By Last X Days
                if (!parseInt(document.getElementById('txtLastNDaysSales').value) > 0) {
                    alert('Last [X] days of sales should be greater than 0.');
                    return false;
                }
    		}
    		if (!parseInt(document.getElementById('txtInventoryDays').value) > 0) {
                alert('No. Days Inventory should be greater than 0.');
                return false;
    		}
    		if (!parseInt(document.getElementById('txtMaxInvSpacePercentage').value) > 0) {
                alert('Percentage of quantity should be greater than 0.');
                return false;
    		}
	}

	function enableFieldSet(setName) {
	    var fld_id_enable_arr = new Array();
	    var fld_id_disable_arr = new Array();
        switch (setName) {
            case 'rdoMaxQtySource1':
                fld_id_enable_arr.push('txtMaxQtyOverwrite');
                break;
            case 'rdoMaxQtySource2':
                fld_id_disable_arr.push('txtMaxQtyOverwrite');
                break;
            case 'MaxInvSource':
                fld_id_enable_arr.push('txtMaxInvSpaceOverwrite', 'txtInventoryDays', 'txtMaxInvSpacePercentage', 'btnPreview');
                break;
            case 'rdoSalesRetrievalMethod1':
                fld_id_disable_arr.push('txtLastNDaysSales');
                fld_id_enable_arr.push('txtSalesStartDate', 'txtSalesEndDate');
                break;
            case 'rdoSalesRetrievalMethod2':
                fld_id_enable_arr.push('txtLastNDaysSales');
                fld_id_disable_arr.push('txtSalesStartDate', 'txtSalesEndDate');
                break;
        }
        setFieldState(fld_id_enable_arr, false);
        setFieldState(fld_id_disable_arr, true);
	}
	
	function setFieldState(fld_id_arr, isDisable) {
        for (i=0;i<fld_id_arr.length;i++) {
            document.getElementById(fld_id_arr[i]).disabled = isDisable;
        }
	}
//-->
</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="init()">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
			</td>
			<td valign="top">
<?
if ($products_id > 0) {
    echo tep_draw_form('buyback', FILENAME_BUYBACK_PRODUCTS_BRACKETS, tep_get_all_get_params(array("action","bracket_id"))."action=update_brackets", 'post', ' onSubmit="return validate(this);"');
?>			
			
				<table width="100%" border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td colspan="3"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
					</tr>
<?
	$path = tep_output_generated_category_path_sq($cat_id). " > ".tep_get_products_name($products_id);
	$html = '      <tr>
						<td colspan="3" class="dataTableContent"><b>'.TEXT_BRACKETS_FOR.'</b> '.$path
	                   .tep_draw_hidden_field("pID", $products_id)
	                   .tep_draw_hidden_field("cID", $cat_id)
	                   .'</td>
                   </tr>';
?>

<?      
    //-----Start Brackets ---------------------------------------------------
	$html .= '     <tr><td colspan="2">
	                ' . tep_draw_hidden_field('hidNumBrackets', $buybackProductObj->buyback_num_brackets, 'id="hidNumBrackets" ') . '
                    <table cellpadding="3" cellspacing="0" width="1" border="0"><!-- Open outer 1-->';

    $color = "#FFFFCC";
    $num_columns_per_row = 8;
    //Don't enter if num brackets = 0.
	for ($i=0; $i < $buybackProductObj->buyback_num_brackets; $i++) {
		$row = $buybackProductObj->buyback_brackets[$i];
		
        //Bracket field template		
		$row_qty = tep_draw_input_field("txtQty[{$row['buyback_bracket_id']}]",$row['buyback_bracket_quantity'], 'size="12" id="txtQty_'.$i.'" ');
		$row_price = tep_draw_input_field("txtPrice[".$row['buyback_bracket_id']."]", number_format($row['buyback_bracket_value'], DISPLAY_PRICE_DECIMAL), 'size="8" id="txtPrice_'.$i.'" ') 
		              . tep_draw_pull_down_menu("type[".$row['buyback_bracket_id']."]", array(array(id => "0",text => TEXT_MONEY), array(id => "1", text=>TEXT_PERCENTAGE)), $row['buyback_bracket_mode']);
		$row_delete_link = '<a href="'.tep_href_link(FILENAME_BUYBACK_PRODUCTS_BRACKETS,tep_get_all_get_params(array('action', 'bracket_id', 'pID', 'cID'))."pID=$products_id&cID=$cat_id&action=delete_brackets&bracket_id=".$row['buyback_bracket_id']).'">'.TEXT_DELETE.'</a>';
	    
		if (($i % $num_columns_per_row)==0) {
		    //Starting left most column
		    if ($i > 0) {
                $html .= '</tr>';
		    }
            $html .= '   <tr>
	                      <td>
	                          <table cellpadding="3" cellspacing="0" width="1" border="0">
	                              <tr>
	                                  <td width="12%" align="left" class="ordersBoxHeading">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
                                      <td width="12%" align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>		                                  
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="left" class="ordersBoxHeading">'.TEXT_QUANTITY.'</td>
	                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_qty.'</td>
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PRICE.'</td>
	                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_price.'</td>
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="left" class="ordersBoxHeading">&nbsp;</td>
	                                  <td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.$row_delete_link.'</td>
	                              </tr>		                              
	                          </table>
	                      </td>';
		} else {
		    //Second column till row end.
            $html .= '    <td>
	                          <table cellpadding="3" cellspacing="0" width="1" border="0">
	                              <tr>
	                                  <td width="12%" align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_qty.'</td>
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_price.'</td>
	                              </tr>
	                              <tr>
	                                  <td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.$row_delete_link.'</td>
	                              </tr>		                              
	                          </table>
		                  </td>';
		}
		//Put here so new bracket form has color change
		$color = (($i % 2)==0) ? "#D7D5D0" : "#FFFFCC";
	}
	
	//New Bracket form
    $html .= '            <td>
                              <table cellpadding="3" cellspacing="0" width="1" border="0">
                                  <tr>
                                      <td width="12%" align="left" class="ordersBoxHeading">'.TABLE_HEADING_BRACKET_NEW.'</td>
                                  </tr>
                                  <tr>
                                      <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtQty[0]", '', "size='12' id='txtQty_new'").'</td>
                                  </tr>
                                  <tr>
                                      <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtPrice[0]", '', 'size="8" id="txtPrice_new" ') . tep_draw_pull_down_menu("type[0]", array(array(id => "0",text => TEXT_MONEY),array(id => "1", text=>TEXT_PERCENTAGE))).'</td>
                                  </tr>
                                  <tr>
                                      <td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">&nbsp;</td>
                                  </tr>		                              
                              </table>
                          </td>
                          </tr>';		
	
	$html .= '      </table><!-- Close Outer 1-->
	            </td></tr>';
	//-----End Brackets ---------------------------------------------------
	

	//-----Start Min-Max Form ---------------------------------------------
	$html .= '	<tr><td>
					<table cellpadding="4" cellspacing="0"  border="0"><!-- Open outer 2-->';
    $html .= '
        				<tr><td colspan="2" class="ordersBoxHeading"><b>'.TEXT_MIN_BUYBACK_QUANTITY.'</b></td></tr>
        				<tr valign="top" bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_OVERWRITE.'</td>
							 <td class="reportRecords" align="left">'.tep_draw_input_field("txtMinQtyOverwrite", "$buybackProductObj->min_buyback_qty_db", ' id="txtMinQtyOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'</td>
        				</tr>
        				
        				<tr><td colspan="2" class="ordersBoxHeading"><b>'.TEXT_MAX_BUYBACK_QUANTITY.'</b></td></tr>
    
    				    <tr valign="top" bgcolor="#D7D5D0">
    				        <td class="dataTableContent">'.tep_draw_radio_field('rdoMaxQtySource', 'overwrite', ($buyback_products_settings_arr['bprd_max_qty_source'] == 'overwrite' ? true : false), '', ' id="rdoMaxQtySource1" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_OVERWRITE.'</td>
    				        <td class="reportRecords" align="left">'.tep_draw_input_field("txtMaxQtyOverwrite", "{$buyback_products_settings_arr['bprd_max_qty_overwrite']}", ' id="txtMaxQtyOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'</td></tr>
    				        
        				<tr valign="top" bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.tep_draw_radio_field('rdoMaxQtySource', 'system_defined', ($buyback_products_settings_arr['bprd_max_qty_source'] == 'system_defined' || (!isset($buyback_products_settings_arr['bprd_max_qty_source'])) ? true : false), '', ' id="rdoMaxQtySource2" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_SYSTEM_DEFINE.'</td>
                             <td class="dataTableContent" align="left"><span name="spanMaxInvSpaceDerived" id="spanMaxBuyBackQty"><b>'.$buybackProductObj->max_buyback_qty.'</b></span>'.tep_draw_hidden_field('hidMaxQtySystem', $buybackProductObj->max_buyback_qty, 'id="hidMaxQtySystem" ').'</b></td></tr>		

        				<tr><td colspan="3" class="ordersBoxHeading"><b>'.TEXT_MAX_INV_SPACE.'</b></td></tr>
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.TEXT_USE_GREATER_BETWEEN.'</td></tr>
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">1. &nbsp;'.TEXT_OVERWRITE.'</td>
        				     <td class="reportRecords" align="left">'.tep_draw_input_field("txtMaxInvSpaceOverwrite", ($buyback_products_settings_arr['bprd_max_inv_space_overwrite'] > 0 ? "{$buyback_products_settings_arr['bprd_max_inv_space_overwrite']}" : '0'), ' id="txtMaxInvSpaceOverwrite" size="16" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'</td>
        				</tr>
        				
						<tr bgcolor="#D7D5D0">
						     <td class="dataTableContent">2. &nbsp;'.TEXT_SYSTEM_DEFINE.'</td>
    						 <td class="dataTableContent" align="left">&nbsp;</td>
    				    </tr>
        				
        				<tr bgcolor="#D7D5D0" valign="top">
        				     <td class="dataTableContent">&nbsp;</td>
        				     <td class="reportRecords" align="left">
        				     
        				     
        						<table border="0" cellspacing="0" cellpadding="2">
        						
        							<tr valign="top">
        							 <td class="dataTableContent"><b>'.TABLE_HEADING_SALES_RETRIEVAL_METHOD.'</b>
                						<table border="0" cellspacing="0" cellpadding="2">
        							     <tr><td colspan="2" class="dataTableContent">'.tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_date_range', ($buyback_products_settings_arr['bprd_sales_retrieval_method']=='by_date_range' ? true : (!isset($buyback_products_settings_arr['bprd_sales_retrieval_method']) ? true : false)), '', ' id="rdoSalesRetrievalMethod1" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE.'</td></tr>
                                         <tr valign="top">
                                            <td>&nbsp;&nbsp;&nbsp;</td>
                                            <td class="dataTableContent" align="left">'.TABLE_HEADING_SALES_START_DATE.'<br>'.tep_draw_input_field('txtSalesStartDate', (isset($buyback_products_settings_arr['bprd_sales_start_date']) ? $buyback_products_settings_arr['bprd_sales_start_date'] : ''), ' id="txtSalesStartDate" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback.txtSalesStartDate); }" onKeyPress="return noEnterKey(event)" ')
                                              .'<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback.txtSalesStartDate);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>&nbsp;<span class="fieldRequired">*</span>
                                              <br>'.TABLE_HEADING_SALES_END_DATE.'
                                              <br><i>Default: Current Date</i>'.
                                              '<br>'.tep_draw_input_field('txtSalesEndDate', (isset($buyback_products_settings_arr['bprd_sales_end_date']) ? $buyback_products_settings_arr['bprd_sales_end_date'] : ''), ' id="txtSalesEndDate" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback.txtSalesEndDate); }" onKeyPress="return noEnterKey(event)" ')
                                              .'<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback.txtSalesEndDate);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
                                            </td>
                                         </tr>
        							     <tr><td colspan="2" class="dataTableContent">'.tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_last_n_days', ($buyback_products_settings_arr['bprd_sales_retrieval_method']=='by_last_n_days' ? true : false), '', ' id="rdoSalesRetrievalMethod2" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TABLE_HEADING_SALES_RETRIEVE_BY_LAST
                                              .'&nbsp;'.tep_draw_input_field('txtLastNDaysSales', (isset($buyback_products_settings_arr['bprd_last_n_days_sales']) ? $buyback_products_settings_arr['bprd_last_n_days_sales'] : '0'), ' id="txtLastNDaysSales" size="4" maxlength="4"')
                                              .'&nbsp;'.TEXT_DAYS.'</td></tr>
                                        </table>
        							 </td>
        							</tr>
        						</table>        				     
        				     </td>
        				</tr>
        				<tr bgcolor="#D7D5D0" valign="top">
        				     <td class="dataTableContent">&nbsp;</td>
        				     <td class="reportRecords" align="left"><b>'.TABLE_HEADING_DAYS_INVENTORY.'</b><br>'.tep_draw_input_field('txtInventoryDays', (isset($buyback_products_settings_arr['bprd_inventory_days']) ? $buyback_products_settings_arr['bprd_inventory_days'] : ''), 'size="16" id="txtInventoryDays" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ').'&nbsp;<span class="fieldRequired">*</span></td>
        				</tr>
           				<tr bgcolor="#D7D5D0" valign="top">
        				     <td class="dataTableContent">&nbsp;</td>
        				     <td class="reportRecords" align="left"><b>'.TABLE_HEADING_PERCENTAGE_OF_INV_SPACE.'</b><br>'.tep_draw_input_field('txtMaxInvSpacePercentage', (isset($buyback_products_settings_arr['bprd_max_inv_space_percentage']) ? $buyback_products_settings_arr['bprd_max_inv_space_percentage'] : '100'), 'size="16" id="txtMaxInvSpacePercentage" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value))) ) { this.value = \'100\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ')
                                .' &#37; x <span name="spanMaxInvSpaceDerived" id="spanMaxInvSpaceDerived"><b>'.$buybackProductObj->maximum_inventory_space_derived.'</b></span>&nbsp;<span class="fieldRequired">*</span></td>
        				</tr>
        				<tr bgcolor="#D7D5D0" valign="top">
        				     <td class="dataTableContent" colspan="2" align="center"><input name="btnPreview" id="btnPreview" type="button" value="'.TEXT_PREVIEW.'" onClick="preview_suggested_max_qty(\''.(int)$products_id.'\', \''.SID.'\');"></td>
        				</tr>
        				
        		    </table><!-- Close outer 2-->
	            </td>
	            <td valign="top">
					<table cellpadding="4" cellspacing="0"  border="0"><!-- Open outer 3-->
        				<tr><td colspan="3" class="ordersBoxHeading"><b>'.TABLE_HEADING_PRODUCT_LEVELS.'</b></td></tr>
        				
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent" colspan="2" align="center"><b>'.($buybackProductObj->is_buyback ? TEXT_BUYBACK_OK : '<font color="red">'.TEXT_SERVER_FULL.'</font>') .'</b></td>
        				</tr>        				
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent" colspan="2" align="center"><b>'.($buybackProductObj->is_backorder ? '<font color="red">'.TEXT_BACKORDER.'</font>' : TEXT_NOT_BACKORDER) .'</b></td>
        				</tr>

        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_AVAILABLE.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->products_quantity_available".'</td>
        				</tr>                             
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_ACTUAL.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->products_quantity_actual".'</td>
        				</tr>
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_FORECAST_AVAILABLE.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->forecast_quantity_available".'</td>
        				</tr>                             
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_FORECAST_ACTUAL.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->forecast_quantity_actual".'</td>
        				</tr>  					
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
        				
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_DAILY_CONFIRMED_PRODUCTION.'</td>
        				     <td class="reportRecords" align="right">'.$buybackProductObj->get_daily_confirmed_production_qty().'</td>
        				</tr>  					
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_MAX_INV_SPACE.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->maximum_inventory_space".'</td>
        				</tr>  					
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_REMAINDER_INV_SPACE.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->remainder_inventory_space".'</td>
        				</tr>
        				
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_MIN_BUYBACK_QUANTITY.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->min_buyback_qty_db".'</td>
        				</tr>  					
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_MAX_BUYBACK_QUANTITY.'</td>
        				     <td class="reportRecords" align="right">'."$buybackProductObj->max_buyback_qty_db".'</td>
        				</tr>
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_ACTIVE_BRACKET_QTY.'</td>
        				     <td class="reportRecords" align="right">'."{$buybackProductObj->buyback_bracket_active['quantity']}".'</td>
        				</tr>
        				<tr bgcolor="#D7D5D0">
        				     <td class="dataTableContent">'.TEXT_ACTIVE_BRACKET_PRICE.'</td>
        				     <td class="reportRecords" align="right">'."{$buybackProductObj->buyback_bracket_active['price']}".'</td>
        				</tr>
        				
        				<tr bgcolor="#D7D5D0"><td colspan="3" class="dataTableContent">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
        		    </table><!-- Close outer 3-->
	            </td>
	            </tr>';
    //-----End Min-Max Form -----------------------------------------------	

	//Submit/Back buttons
	$html .= '	<tr><td colspan="2">
					<table cellpadding="4" cellspacing="0"  border="0"><!-- Open outer 4-->
				        <tr><td colspan="3">'.tep_image_submit("button_update.gif", IMAGE_UPDATE).'&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_BUYBACK_PRODUCTS) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a></td></tr>
	                </table><!-- Close outer 4-->
	            </td></tr>';   
	
	$html .= '  </table>';
	$html .= '</form>';
    //echo "<pre>".htmlspecialchars($html)."</pre>";
	echo $html;
}
?>
	        </td>
	    </tr>
    </table>
    <div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
<?php
    $js_str = '';
    switch($buyback_products_settings_arr['bprd_max_qty_source']) {
        case 'overwrite':
            $js_str .= "enableFieldSet('rdoMaxQtySource1');";
            break;
        case 'system_defined':
            $js_str .= "enableFieldSet('rdoMaxQtySource2');";
            break;
        default:
            $js_str .= "enableFieldSet('rdoMaxQtySource2');";            
            break;
    }
    $js_str .= "enableFieldSet('MaxInvSource');";
    switch($buyback_products_settings_arr['bprd_sales_retrieval_method']) {
        case 'by_date_range':
            $js_str .= "enableFieldSet('rdoSalesRetrievalMethod1');";
            break;
        case 'by_last_n_days':
            $js_str .= "enableFieldSet('rdoSalesRetrievalMethod2');";
            break;
        default:
            $js_str .= "enableFieldSet('rdoSalesRetrievalMethod1');";            
            break;
    }                
?>    
    <script type="text/javascript">
    <!--
        <?=$js_str?>
    //-->
    </script>    

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>