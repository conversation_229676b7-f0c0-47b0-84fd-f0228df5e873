<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'vip_groups.php');
require(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'log.php');

$currencies = new currencies();
$currencies->set_decimal_places(3);

$allow_search_num_days_ago = 100;
define('CONF_BUYBACK_WEBSITE_EXPIRY_SEC', 10800);

require_once(DIR_WS_FUNCTIONS . 'supplier.php');

$access_customer_profile_page = tep_admin_check_boxes(FILENAME_CUSTOMERS, 'sub_boxes');

$buyback_batch_update_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS, 'BUYBACK_BATCH_UPDATE');

$customer_view_email_listing_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS, 'BUYBACK_VIEW_CUSTOMER_EMAIL');

$status_options = array();
$order_status_select_sql = "SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY buyback_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["buyback_status_id"]] = $order_status_row["buyback_status_name"];
}

$buyback_fr_websites_option = tep_get_site_name_list('buyback');
$deliver_option = array(TEXT_TRADE_ON_MAIL, TEXT_FACE_TO_FACE_TRADE, TEXT_OPEN_STORE_TRADE, ENTRY_TRADE_WITH_OFFGAMERS, ENTRY_TRADE_WITH_CUSTOMERS);
$deliver_option_key = array('ofp_deal_on_mail', 'ofp_deal_on_game', 'ofp_deal_on_open_store', 'vip_deal_on_game', 'ofp_deal_with_customers');

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "show_report":
		    unset($_SESSION['buyback_lists_param']["subaction"]);
		    
		    if (!$_REQUEST['cont']) {
				if ($_REQUEST["subaction"] == "sl_status") {
					if ($_REQUEST["order_status"]) {
						$_REQUEST["order_status"] = tep_array_unserialize($_REQUEST["order_status"]);
					}
				}
				
				if ($_REQUEST["subaction"] == 'buyback_order') {
    				$_SESSION['buyback_lists_param']["subaction"] = $_REQUEST["subaction"];
    				$_SESSION['buyback_lists_param']["products_id"] = $_REQUEST["products_id"];
    				
    				if ($_REQUEST["order_status"]) {
        				$_REQUEST["order_status"] = tep_array_unserialize($_REQUEST["order_status"]);
        			}
    			}
				$_SESSION['buyback_lists_param']["customer_id"] = $_REQUEST["cID"];	// Buyback history link from Customer Profile page
				$_SESSION['buyback_lists_param']["order_status"] = $_REQUEST["order_status"];
				$_SESSION['buyback_lists_param']["start_date"] = $_REQUEST["start_date"];
				$_SESSION['buyback_lists_param']["end_date"] = $_REQUEST["end_date"];
				$_SESSION['buyback_lists_param']["order_id"] = (int)$_REQUEST["order_id"];
				$_SESSION['buyback_lists_param']["buyback_sites"] = is_array($_REQUEST["buyback_sites"]) && count($_REQUEST["buyback_sites"]) ? $_REQUEST["buyback_sites"] : array_keys($buyback_fr_websites_option);
				$_SESSION['buyback_lists_param']["cat_id"] = (int)$HTTP_POST_VARS["cat_id"];
				$_SESSION['buyback_lists_param']["include_subcategory"] = (int)$_REQUEST["include_subcategory"];
				$_SESSION['buyback_lists_param']["customer_email"] = trim($_REQUEST["customer_email"]);
				$_SESSION['buyback_lists_param']["page_refresh"] = $_REQUEST["page_refresh"];
				$_SESSION['buyback_lists_param']["show_records"] = $_REQUEST["show_records"];
				$_SESSION['buyback_lists_param']["deliver_option"] = $_REQUEST["deliver_option"];
				
				if (!tep_not_null(trim($_REQUEST["order_id"]))) {
					if (!tep_not_null(trim($_REQUEST["start_date"])) && !tep_not_null(trim($_REQUEST["end_date"]))) {
						$_SESSION['buyback_lists_param']["start_date"] = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m")  , date("d")-30, date("Y")));	
					} else if (tep_not_null(trim($_REQUEST["start_date"]))) {
						$s_yr = 0;
						$s_mth = 0;
						$s_day = 0;
						$s_hr = 0;
						$s_min = 0;
						$s_sec = 0;
						
						list($s_date_str, $s_time_str) = explode(' ', $_REQUEST["start_date"]);
						if (tep_not_null($s_date_str)) {list($s_yr, $s_mth, $s_day) = explode('-', $s_date_str);}
						if (tep_not_null($s_time_str)) {list($s_hr, $s_min, $s_sec) = explode('-', $s_time_str);}
						
						$_SESSION['buyback_lists_param']["start_date"] = $_REQUEST["start_date"];
						
						if (tep_not_null(trim($_REQUEST["end_date"]))) {
							$enddate = $_REQUEST["end_date"];
						} else {
							$enddate = date("Y-m-d H:i:s");
						}
						
						if (floor(tep_day_diff($_REQUEST["start_date"], $enddate)) >  $allow_search_num_days_ago) {
							$_SESSION['buyback_lists_param']["end_date"] = date("Y-m-d H:i:s", mktime($s_hr, $s_min, $s_sec, $s_mth, $s_day+$allow_search_num_days_ago, $s_yr));
						}
					} else if (tep_not_null(trim($_REQUEST["end_date"]))) {
						$e_yr = 0;
						$e_mth = 0;
						$e_day = 0;
						$e_hr = 0;
						$e_min = 0;
						$e_sec = 0;
						
						list($e_date_str, $e_time_str) = explode(' ', $_REQUEST["end_date"]);
						if (tep_not_null($e_date_str)) {list($e_yr, $e_mth, $e_day) = explode('-', $e_date_str);}
						if (tep_not_null($e_time_str)) {list($e_hr, $e_min, $e_sec) = explode('-', $e_time_str);}
						
						$_SESSION['buyback_lists_param']["start_date"] = date("Y-m-d H:i:s", mktime($e_hr, $e_min, $e_sec, $e_mth, $e_day-$allow_search_num_days_ago, $e_yr));
						$_SESSION['buyback_lists_param']["end_date"] = $_REQUEST["end_date"];
					}
					
				}
		  	}
		  	
		  	if (tep_not_null($_SESSION['buyback_lists_param']["customer_email"])) {
			  	if (!tep_validate_email($_SESSION['buyback_lists_param']["customer_email"])) {
			  		$messageStack->add_session(ERROR_INAVALID_EMAIL_ADDRESS, 'error');
			  		tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS));
			  	}
			}
		  	
			break;
        case "reset_session":
        	unset($_SESSION['buyback_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS));
        	break;
    	case 'batch_update':
			$batch_update_char_mode = isset($_POST['batch_update_char_mode']) ? $_POST['batch_update_char_mode'] : '';
			$buyback_order_array = array();
			if (isset($_POST['cb'])) {
				$buyback_order_array = array_keys($_POST['cb']);
			}
			
			switch ($batch_update_char_mode) {
				case 'ShowSubmittedRestockCharacter':
					//Show restock character
					if (count($buyback_order_array)) {
						$character_visibility_arr = array('show_restock' => '1');
						
						for ($bo_cnt=0; $bo_cnt < count($buyback_order_array); $bo_cnt++) {
							$show_restock_mode_select_sql = "	SELECT show_restock 
																FROM " . TABLE_BUYBACK_REQUEST_GROUP . " 
																WHERE buyback_request_group_id = '" . tep_db_input($buyback_order_array[$bo_cnt]) . "'";
							$show_restock_mode_result_sql = tep_db_query($show_restock_mode_select_sql);
							if ($show_restock_mode_row = tep_db_fetch_array($show_restock_mode_result_sql)) {
								if ($show_restock_mode_row['show_restock'] != '1') {
									$success = tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $character_visibility_arr, 'update', "buyback_request_group_id = '".tep_db_input($buyback_order_array[$bo_cnt])."'");
									
									if ($success) {
										$buyback_history_data_array = array('buyback_request_group_id' => $buyback_order_array[$bo_cnt],
													                        'buyback_status_id' => '0',
																			'date_added' => 'now()',
																			'customer_notified' => '0',
																			'comments' => 'Show Restock Character',
																			'changed_by' => $login_email_address
																			);
										tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
										
										// Reset all comment become non-remark
										$remark_sql_data_array = array('set_as_buyback_remarks' => 0);
										tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $remark_sql_data_array, 'update', "buyback_request_group_id = '" . (int)$buyback_order_array[$bo_cnt] . "'");
										
										$buyback_request_group_site_id_select_sql = "	SELECT buyback_request_group_site_id FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id ='" . (int)$buyback_order_array[$bo_cnt] . "'";
										$buyback_request_group_site_id_result_sql = tep_db_query($buyback_request_group_site_id_select_sql);
										$buyback_request_group_site_id_row = tep_db_fetch_array($buyback_request_group_site_id_result_sql);
										
										if ((int)$buyback_request_group_site_id_row['buyback_request_group_site_id'] > 0) {
											$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																					FROM " . TABLE_ORDERS_COMMENTS . "
																					WHERE orders_comments_id = '87'";
											$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
											
											if ($predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql)) {
												$orders_comments_text = $predefined_order_comment_row['orders_comments_text'];
												$user_notify = '1';
												$is_remarks = '1';
												
												$buyback_order_comment_data_array = array(	'buyback_request_group_id' => $buyback_order_array[$bo_cnt],
																	                        'buyback_status_id' => '0',
																							'date_added' => 'now()',
																							'customer_notified' => $user_notify,
																							'comments' => $orders_comments_text,
																							'set_as_buyback_remarks' => $is_remarks,
																							'changed_by' => $login_email_address
																						);
												tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
											}
										} else {
											$orders_comments_text = TEXT_CHAR_READY;
											$user_notify = '1';
											$is_remarks = '1';
											
											$buyback_order_comment_data_array = array(	'buyback_request_group_id' => $buyback_order_array[$bo_cnt],
																                        'buyback_status_id' => '0',
																						'date_added' => 'now()',
																						'customer_notified' => $user_notify,
																						'comments' => $orders_comments_text,
																						'set_as_buyback_remarks' => $is_remarks,
																						'changed_by' => $login_email_address
																					);
											tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
										}
										
										$messageStack->add_session(sprintf(SUCCESS_SHOW_BUYBACK_RESTOCK_CHARACTER, $buyback_order_array[$bo_cnt]), 'success');
									} else {
										$messageStack->add_session(sprintf(ERROR_SHOW_BUYBACK_RESTOCK_CHARACTER, $buyback_order_array[$bo_cnt]), 'error');
									}
								} else {
									$messageStack->add_session(sprintf(WARNING_RESTOCK_CHARACTER_ALREADY_SHOWN, $buyback_order_array[$bo_cnt]), 'warning');
								}
							} else {
								$messageStack->add_session(sprintf(ERROR_BUYBACK_ORDER_NOT_EXISTS, $buyback_order_array[$bo_cnt]), 'error');
							}
						}
					} else {
						$messageStack->add_session('No buyback orders selected.');
					}
					break;
				case 'HideAllRestockCharacter':
					//Hide restock character
					if (count($buyback_order_array)) {
						$character_visibility_arr = array('show_restock' => '0');
						
						for ($bo_cnt=0; $bo_cnt < count($buyback_order_array); $bo_cnt++) {
							$show_restock_mode_select_sql = "	SELECT show_restock 
																FROM " . TABLE_BUYBACK_REQUEST_GROUP . " 
																WHERE buyback_request_group_id = '" . tep_db_input($buyback_order_array[$bo_cnt]) . "'";
							$show_restock_mode_result_sql = tep_db_query($show_restock_mode_select_sql);
							if ($show_restock_mode_row = tep_db_fetch_array($show_restock_mode_result_sql)) {
								if ($show_restock_mode_row['show_restock'] != '0') {
									$success = tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $character_visibility_arr, 'update', "buyback_request_group_id = '".tep_db_input($buyback_order_array[$bo_cnt])."'");
									
									if ($success) {
										$buyback_request_group_site_id_select_sql = "	SELECT buyback_request_group_site_id FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id ='" . (int)$_REQUEST['buyback_request_group_id'] . "'";
										$buyback_request_group_site_id_result_sql = tep_db_query($buyback_request_group_site_id_select_sql);
										$buyback_request_group_site_id_row = tep_db_fetch_array($buyback_request_group_site_id_result_sql);
										
										if ((int)$buyback_request_group_site_id_row['buyback_request_group_site_id'] > 0) {
											$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																					FROM " . TABLE_ORDERS_COMMENTS . "
																					WHERE orders_comments_id = '121'";
											$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
											
											if ($predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql)) {
												$orders_comments_text = $predefined_order_comment_row['orders_comments_text'];
												$user_notify = '1';
												$is_remarks = '1';
												
												$buyback_history_data_array = array('buyback_request_group_id' => $buyback_order_array[$bo_cnt],
															                        'buyback_status_id' => '0',
																					'date_added' => 'now()',
																					'customer_notified' => '0',
																					'comments' => 'Hide Restock Character',
																					'changed_by' => $login_email_address
																					);
												tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
											}
										} else {
											$orders_comments_text = TEXT_CHAR_DISCONNECTED;
											$user_notify = '1';
											$is_remarks = '1';
											
											$buyback_history_data_array = array('buyback_request_group_id' => $buyback_order_array[$bo_cnt],
														                        'buyback_status_id' => '0',
																				'date_added' => 'now()',
																				'customer_notified' => '0',
																				'comments' => 'Hide Restock Character',
																				'changed_by' => $login_email_address
																				);
											tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
										}
										
										// Set hide restock comments as remark
										// Reset all comment become non-remark
										$remark_sql_data_array = array('set_as_buyback_remarks' => 0);
										tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $remark_sql_data_array, 'update', "buyback_request_group_id = '" . (int)$buyback_order_array[$bo_cnt] . "'");
										
										$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																				FROM " . TABLE_ORDERS_COMMENTS . "
																				WHERE orders_comments_id = '121'";
										$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
										
										if ($predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql)) {
											$orders_comments_text = $predefined_order_comment_row['orders_comments_text'];
											$user_notify = '1';
											$is_remarks = '1';
											
											$buyback_order_comment_data_array = array(	'buyback_request_group_id' => $buyback_order_array[$bo_cnt],
																                        'buyback_status_id' => '0',
																						'date_added' => 'now()',
																						'customer_notified' => $user_notify,
																						'comments' => $orders_comments_text,
																						'set_as_buyback_remarks' => $is_remarks,
																						'changed_by' => $login_email_address
																					);
											tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
										}
										
										$messageStack->add_session(sprintf(SUCCESS_HIDE_BUYBACK_RESTOCK_CHARACTER, $buyback_order_array[$bo_cnt]), 'success');
									} else {
										$messageStack->add_session(sprintf(ERROR_HIDE_BUYBACK_RESTOCK_CHARACTER, $buyback_order_array[$bo_cnt]), 'error');
									}
								} else {
									$messageStack->add_session(sprintf(WARNING_RESTOCK_CHARACTER_ALREADY_HIDE, $buyback_order_array[$bo_cnt]), 'warning');
								}
							} else {
								$messageStack->add_session(sprintf(ERROR_BUYBACK_ORDER_NOT_EXISTS, $buyback_order_array[$bo_cnt]), 'error');
							}
						}
					} else {
						$messageStack->add_session('No buyback orders selected.');
					}
					break;
			}
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report&cont=1'));
			break;
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/buyback.js"></script>
	<script language="javascript">
		function showOverEffect(object, class_name, extra_row) {
			rowOverEffect(object, class_name);
			var rowObjArray = extra_row.split('##');
			for (var i = 0; i < rowObjArray.length; i++) {
				if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
				}
			}
		}

		function showOutEffect(object, class_name, extra_row) {
			rowOutEffect(object, class_name);
			var rowObjArray = extra_row.split('##');
	  		for (var i = 0; i < rowObjArray.length; i++) {
	  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
	  			}
	  		}
		}

		function showClicked(object, class_name, extra_row) {
			rowClicked(object, class_name);
			if (extra_row=="")
				return;
			var rowObjArray = extra_row.split('##');
	  		for (var i = 0; i < rowObjArray.length; i++) {
	  			rowClicked(document.getElementById(rowObjArray[i]), class_name);
			}
		}

		function confirmBatchAction(frmObj, frmStatus, checkName) {
			if (trim_str(frmObj.batch_update_char_mode.value) == '') {
				alert('Please select your batch action!');
				return false;
			} else {
				if (document.getElementById('batch_update_char_mode').value == 'HideAllRestockCharacter') {
					var confirmation = confirm('Confirm to proceed with batch update ?');
					
					if (confirmation == false) {
						return false;
					}
				} else if (document.getElementById('batch_update_char_mode').value == 'ShowSubmittedRestockCharacter') {
					return true;
				}
			}
		}
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
<?
if ($action == 'show_report') {
	$date_range = '';
	
	$orders_select_str = "	SELECT brg.buyback_request_group_id, brg.customers_id, DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d %H:%i') AS date_submitted, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.buyback_request_group_billing_status, brg.buyback_request_group_verify_mode,
								brg.remote_addr, brg.buyback_status_id, brg.buyback_request_group_comment, brg.buyback_request_group_user_type, brg.currency, brg.currency_value, brg.buyback_request_group_site_id, brg.show_restock,
								br.restock_character, br.buyback_dealing_type, br.products_id, br.orders_products_id, brg.orders_tag_ids, brg.orders_read_mode, brg.buyback_request_order_type, l.locking_by ";	

	$tables_joining_str = "	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
							INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
								ON (brg.buyback_request_group_id=br.buyback_request_group_id) 
							LEFT JOIN " . TABLE_LOCKING . " AS l 
								ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') ";
    
	$customer_id_str = (isset($_SESSION['buyback_lists_param']["customer_id"]) && tep_not_null($_SESSION['buyback_lists_param']["customer_id"])) ? " brg.customers_id='" . $_SESSION['buyback_lists_param']["customer_id"] . "'" : "1";
	$order_id_str = (isset($_SESSION['buyback_lists_param']["order_id"]) && tep_not_null($_SESSION['buyback_lists_param']["order_id"])) ? " brg.buyback_request_group_id='" . $_SESSION['buyback_lists_param']["order_id"] . "'" : "1";
	
	$buyback_sites_str = " brg.buyback_request_group_site_id IS NULL ";
	if (isset($_SESSION['buyback_lists_param']["buyback_sites"]) && is_array($_SESSION['buyback_lists_param']["buyback_sites"])) {
	    $allowed_buyback_order_type = array_intersect($_SESSION['buyback_lists_param']["buyback_sites"], array_keys($buyback_fr_websites_option));
	    
	    if (count($allowed_buyback_order_type)) {
	        $buyback_sites_str = " brg.buyback_request_group_site_id IN ('" . implode("', '", $allowed_buyback_order_type) . "') ";
	    }
	}
	
	if (isset($_SESSION['buyback_lists_param']["deliver_option"]) && is_array($_SESSION['buyback_lists_param']["deliver_option"]) && count($_SESSION['buyback_lists_param']["deliver_option"])) {
		$dealing_type = array();
		$order_type = array();
		for ($deliver_count=0; $deliver_count < count($_SESSION['buyback_lists_param']["deliver_option"]); $deliver_count++) {
			if (!in_array($deliver_option_key[$_SESSION['buyback_lists_param']["deliver_option"][$deliver_count]], $dealing_type)) {
				$dealing_type[] = $deliver_option_key[$_SESSION['buyback_lists_param']["deliver_option"][$deliver_count]];
			}
		}
		$deliver_option_str = " br.buyback_dealing_type IN ('" . implode("', '", $dealing_type) . "') ";
	} else {
		$deliver_option_str = " 1 ";
	}
	
  	// Implementation of categories based permissions
	$categories_where_str = ' 1 ';
	if ($_SESSION['buyback_lists_param']["cat_id"] !== '') {
		$have_all_cat_permission = false;
		
		if (tep_check_cat_tree_permissions(FILENAME_BUYBACK_REQUESTS, $_SESSION['buyback_lists_param']["cat_id"]) == 1) {
			$category_array = array($_SESSION['buyback_lists_param']["cat_id"]);
			
			if ($_SESSION['buyback_lists_param']["cat_id"] == '0' && $_SESSION['buyback_lists_param']["include_subcategory"])	$have_all_cat_permission = true;
		} else {
			$category_array = array();
		}
		
  		if (!$have_all_cat_permission) {
  			if ($_SESSION['buyback_lists_param']["include_subcategory"]) {
	  			tep_get_subcategories($category_array, $_SESSION['buyback_lists_param']["cat_id"], FILENAME_BUYBACK_REQUESTS);
	  		}
	  		
  			$tables_joining_str .= " INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc ON (br.products_id = pc.products_id and pc.products_is_link = 0) ";
			$categories_where_str = " pc.categories_id IN ('" . implode("', '", $category_array) . "') ";
  		}
	}
	
  	if (tep_not_null($_SESSION['buyback_lists_param']["start_date"])) {
  		$date_range = '( <b>FROM</b> '.$_SESSION['buyback_lists_param']["start_date"];
		if (strpos($_SESSION['buyback_lists_param']["start_date"], ':') !== false) {
			$startDateObj = explode(' ', trim($_SESSION['buyback_lists_param']["start_date"]));
			list($yr, $mth, $day) = explode('-', $startDateObj[0]);
			list($hr, $min) = explode(':', $startDateObj[1]);
			$start_date_str = " ( brg.buyback_request_group_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['buyback_lists_param']["start_date"]));
			$start_date_str = " ( brg.buyback_request_group_date >= '" . $_SESSION['buyback_lists_param']["start_date"] . "' )";
		}
	} else {
		$start_date_str = " 1 ";
	}

	if (tep_not_null($_SESSION['buyback_lists_param']["end_date"])) {
		$date_range .= ' <b>To</b> '.$_SESSION['buyback_lists_param']["end_date"].' )';
		if (strpos($_SESSION['buyback_lists_param']["end_date"], ':') !== false) {
			$endDateObj = explode(' ', trim($_SESSION['buyback_lists_param']["end_date"]));
			list($yr, $mth, $day) = explode('-', $endDateObj[0]);
			list($hr, $min) = explode(':', $endDateObj[1]);
			$end_date_str = " ( brg.buyback_request_group_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['buyback_lists_param']["end_date"]));
			$end_date_str = " ( brg.buyback_request_group_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' )";
		}
	} else {
		$date_range .= ' <b>To</b> '.date("Y-m-d H:i:s").' )';
		$end_date_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['buyback_lists_param']["customer_email"])) {
		$tables_joining_str .= " INNER JOIN " . TABLE_CUSTOMERS . " AS c ON (brg.customers_id = c.customers_id) ";
		
		$customer_email_str = " customers_email_address = '" . tep_db_input($_SESSION['buyback_lists_param']["customer_email"]) . "'";
	} else {
		$customer_email_str = " 1 ";
	}

    if (tep_not_null($_SESSION['buyback_lists_param']["subaction"]) && $_SESSION['buyback_lists_param']["subaction"] == 'buyback_order') {
        $product_id_str = " br.products_id = '". $_SESSION['buyback_lists_param']["products_id"] ."'";
    } else {
        $product_id_str = " 1 ";
    }

	$orders_where_str = " $customer_id_str AND $order_id_str AND $start_date_str AND $end_date_str AND $buyback_sites_str AND $categories_where_str AND $customer_email_str AND $product_id_str AND $deliver_option_str";
	$orders_group_by_str = " group by brg.buyback_request_group_id ";
  	
	$show_records = $_SESSION['buyback_lists_param']["show_records"];

	$show_order_status = isset($_SESSION['buyback_lists_param']["order_status"]) ? $_SESSION['buyback_lists_param']["order_status"] : array();

	if (!count($show_order_status)) {
		$show_order_status = array_keys($status_options);
	}
?>
					<tr>
						<td class="dataTableContent"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
						<td class="smallText" align="right" valign="top">&nbsp;
						<?
							echo tep_draw_form('buyback_order_form', FILENAME_BUYBACK_REQUESTS_INFO, '', 'post');
							echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('buyback_request_group_id', '', 'size="12"') . tep_draw_hidden_field('action', 'change_order');
							echo "</form>";
						?>
						</td>
					</tr>
					<tr><td colspan="2">&nbsp;<?=$date_range?></td></tr>
<?
	foreach ($show_order_status as $status_id) {
		$status_name = $status_options[$status_id];
		$status_dependent_tables_joining_str = $status_dependent_orders_select_str = $trade_code_select_str ='';
		
		$list_colspan_count = 12;
		if ($status_id == '3' ||  $status_id == '1')	$list_colspan_count += 2;
		
		//if ($status_id == '1')	$list_colspan_count += 2;	// Showing expiry time
		
		$form_name = 'buyback_'.$status_id.'_lists_form';
		$form_filename = FILENAME_BUYBACK_REQUESTS;
?>
					<tr>
          				<td colspan="2">
<?
							echo tep_draw_form($form_name, $form_filename, tep_get_all_get_params(array('action')) . 'action=batch_update', 'post', '');
							echo tep_draw_hidden_field($status_id.'_order_str', '', ' id="'.$status_id.'_order_str" ');
                            
                        	$tag_selection_general = array ( array('id' => '', 'text' => 'Order Lists Options ...'),
                        									 array('id' => 'rd', 'text' => '&nbsp;&nbsp;&nbsp;Mark as read'),
                        									 array('id' => 'ur', 'text' => '&nbsp;&nbsp;&nbsp;Mark as unread'),
                        									 array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
                        								   );
                            
                    		$status = preg_replace("/\s/", '_', $status_options[$status_id]);
                    		echo tep_draw_hidden_field($status.'_order_str', '', ' id="'.$status.'_order_str" ');
                    		
                    		$mirror_for_delete_tag = array();
                    		$status_dependent_tag_array = $tag_selection_general;
                    		$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$status_id."', orders_tag_status_ids) AND filename='".FILENAME_BUYBACK_REQUESTS."' ORDER BY orders_tag_name;";
                    		$order_tag_result_sql = tep_db_query($order_tag_select_sql);
                    		while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
                    			$status_dependent_tag_array[] = array('id' => 'otag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
                    			$mirror_for_delete_tag[] = array('id' => 'rmtag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
                    		}
                    		$status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
                    		$status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
                    		$status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
								<tr>
									<td colspan="<?=$list_colspan_count?>" nowrap>
										<span class="pageHeading"><?=$status_name?></span>
										<span id="showhidedetails_<?=$status_id?>"><a href="javascript:;" onclick="showHideDetails_<?=$status_id?>(this)"><?=TEXT_SHOW_DETAILS?></a></span>
										<?=tep_draw_pull_down_menu($status."_tag_selector", $status_dependent_tag_array, '', ' id="'.$status.'_tag_selector" onChange="orderListsOptions(this, \''.$status_id.'\', \''.(int)$languages_id.'\', \'\', true);"')?>
									</td>
			  					</tr>
								<tr>
									<td class="ordersBoxHeading" width="1%" colspan="2"><?=TABLE_HEADING_BUYBACK_REQUEST_NO?></td>
									<td align="center" class="ordersBoxHeading" width="10%"><?=TABLE_HEADING_BUYBACK_FROM?></td>
									<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_GAME_NAME?></td>
									<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_TRADE_MODE?></td>
<?      if ($status_id == '1') { ?>
									<td align="center" class="ordersBoxHeading" width="12%"><?=TABLE_HEADING_DELIVERY_OPTION?></td>
<?      } ?>
							  		<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_TAG?></td>
							  		<td class="ordersBoxHeading" width="10%"><?=TABLE_HEADING_CUSTOMER_NAME?></td>
							  		<? if ($customer_view_email_listing_permission) { ?>
							  		<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_CUSTOMER_EMAIL?></td>
							  		<? } ?>
							  		<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_DATE?></td>
<?		if ($status_id == '3') { ?>
									<td align="center" class="ordersBoxHeading" width="6%"><?=TABLE_HEADING_BUYBACK_ORDER_BILLING_STATUS?></td>
									<td align="center" class="ordersBoxHeading" width="5%"><?=TABLE_HEADING_BUYBACK_ORDER_VERIFY_STATUS?></td>
<?		} ?>
									<td align="right" class="ordersBoxHeading" width="10%"><?=TABLE_HEADING_PAYABLE_TOTAL?></td>
<?		if ($status_id == '1') { ?>
									<td class="ordersBoxHeading" width="5%" align="center"><?=TABLE_HEADING_BUYBACK_ORDER_EXPIRY_TIME?></td>
									<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_LOCKED_BY?></td>
<?		} ?>
<?		if ($status_id == '2') { ?>
									<td class="ordersBoxHeading" width="5%" align="center"><?=TABLE_HEADING_MINS_SINCE_PROCESSING?></td>
<?		} ?>

									<td class="ordersBoxHeading" width="5%" align="center"><?=TABLE_HEADING_ACTION?></td>
									<td class="ordersBoxHeading" width="1%" align="center">&nbsp;</td>
		  						</tr>
<?
		// check if supplier or customer
		if ($status_id == '2') {
			$status_dependent_tables_joining_str .= " INNER JOIN " . TABLE_BUYBACK_STATUS_HISTORY . " AS bsh ON brg.buyback_request_group_id=bsh.buyback_request_group_id AND bsh.buyback_status_id=2";
			$status_dependent_orders_select_str .= ", IF(max(bsh.date_added) < DATE_SUB(NOW(), INTERVAL 1 HOUR), max(bsh.date_added), now()) AS follow_up_ordering, IF(max(bsh.date_added) < DATE_SUB(NOW(), INTERVAL 1 HOUR), 1, 0) AS processing_alert ";
			$trade_code_select_str .= ", IF(br.buyback_request_customer_org_code <>  buyback_request_customer_matching_code, 1, 0) AS trade_code_alert ";
			
			$orders_order_by_str = " order by follow_up_ordering ASC, brg.buyback_request_group_date desc";
		} else {
			$orders_order_by_str = " order by brg.buyback_request_group_date desc";
		}
		
		$orders_select_sql = $orders_select_str . $status_dependent_orders_select_str . $trade_code_select_str . $tables_joining_str . $status_dependent_tables_joining_str . ' WHERE ' . $orders_where_str . ' AND brg.buyback_status_id = ' . $status_id . $orders_group_by_str . $orders_order_by_str;
		if ($show_records != "ALL") {
			$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$status_id], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_select_sql, $orders_select_sql_numrows, true);
		}
		$orders_result_sql = tep_db_query($orders_select_sql);
		
		$row_count = 0;
		$buyback_detail = array();
		$total_amount_array = array();
		
		while ($row = tep_db_fetch_array($orders_result_sql)) {
			$row_style = ($row_count % 2) ? "ordersListingEven" : "ordersListingOdd";
			
			$buyback_oid = $row['buyback_request_group_id'];
			
			$buyback_detail[$status]['buyback_request_group_id'][] = $buyback_oid;
			
			$js.= "product_ids_$status_id.push(".$row['buyback_request_group_id'].");";
			
			$follow_up_icon = '';
			if ($status_id == '2') {
				if ($row['processing_alert'] == '1') {
					$follow_up_icon .= tep_image(DIR_WS_ICONS."bell_red.gif", '', "16", "16", 'align="top"');
				}
				if ($row['trade_code_alert'] == '1') {
					$follow_up_icon .= tep_image(DIR_WS_ICONS."att.gif", '', "16", "16", 'align="top"');
				}
			}
			
			$tags_str = '';
  			if (tep_not_null($row["orders_tag_ids"])) {
  				$orders_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '".$row["orders_tag_ids"]."') AND filename='".FILENAME_BUYBACK_REQUESTS."';";
				$orders_assigned_tag_result_sql = tep_db_query($orders_assigned_tag_select_sql);
				while ($orders_assigned_tag_row = tep_db_fetch_array($orders_assigned_tag_result_sql)) {
					$tags_str .= $orders_assigned_tag_row["orders_tag_name"] . ', ';
				}
				if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
  			}
			
//			if ($site_name == 'OffGamers US') {
//			    $buyback_from_site = tep_image(DIR_WS_ICONS . 'flag_us.gif', $site_name);
//			} else if ($site_name == 'China Buyback') {
//			    $buyback_from_site = tep_image(DIR_WS_ICONS . 'flag_cn.gif', $site_name);
//			} else {
//			    $buyback_from_site = $site_name;
//			}
			
			$site_name = $buyback_fr_websites_option[$row['buyback_request_group_site_id']];
			
			if ($site_name == 'China Buyback') {
				$buyback_from_site = '<div class="flag CN"></div>';
			} else {
				$country_code_select_sql = "SELECT c.countries_iso_code_2 
											FROM ".TABLE_CUSTOMERS_INFO." AS ci 
											LEFT JOIN ".TABLE_COUNTRIES." AS c 
												ON (ci.customer_info_selected_country = c.countries_id) 
											WHERE customers_info_id = '".(int)$row['customers_id']."'";
				$country_code_result_sql = tep_db_query($country_code_select_sql);
				if ($country_code_row = tep_db_fetch_array($country_code_result_sql)) {
					$buyback_from_site = '<div class="flag '.$country_code_row['countries_iso_code_2'].'"></div>';
				}
			}
			
			$trade_mode_type = $row['buyback_dealing_type'];
			$trade_mode_type_name = TEXT_NON_VIP;
			if((int)$row['buyback_request_order_type'] == 1){
				if ($trade_mode_type == 'ofp_deal_with_customers'){
					$trade_mode_type_name = TEXT_TRADE_WITH_CUSTOMERS;
				} else {
					$trade_mode_type_name = TEXT_TRADE_WITH_OFFGAMERS;
				}
			}
			$user_comments_label = 'Seller Comment';
			
			if ($status_id == '1') {
				$expiry_sec = tep_day_diff(date('Y-m-d H:i:s', mktime(date('H'), (int)date('i')-10, (int)date('s'), date("m"), date("d"), date("Y"))), $row['buyback_request_group_expiry_date'], 'sec');
			}
			
			// Get game name
			$buyback_main_cat_name = tep_get_buyback_main_cat_info($row['products_id'], 'product');
			$game_name = $buyback_main_cat_name['text'];
			
			// Get payable amount for this order.
			$total_payable = 0;
			$buyback_products_select_sql = "SELECT buyback_unit_price, buyback_quantity_received, buyback_quantity_confirmed, buyback_request_quantity
											FROM ".TABLE_BUYBACK_REQUEST."
											WHERE buyback_request_group_id = '" . $buyback_oid . "'";
			$buyback_products_result_sql = tep_db_query($buyback_products_select_sql);
			while ($buyback_products_row = tep_db_fetch_array($buyback_products_result_sql)) {
				// Wei Chen
				$unit_price = $buyback_products_row['buyback_unit_price'];
				
				if ($row['buyback_request_group_site_id'] == 0) {	// For website buyback, assume confirm qty same as request qty
					$buyback_products_row['buyback_quantity_confirmed'] = $buyback_products_row['buyback_request_quantity'];
				} else if ($buyback_products_row['buyback_quantity_confirmed'] == 0 && $buyback_products_row['buyback_quantity_received'] > 0) { // If CN buyback is recevied qty from cancel status
				    $buyback_products_row['buyback_quantity_confirmed'] = $buyback_products_row['buyback_request_quantity'] * 1.1;
				}
				
				$received_quantity = (int)$buyback_products_row['buyback_quantity_received'];
				
				$actual_amount = $unit_price * ($received_quantity > $buyback_products_row['buyback_quantity_confirmed'] ? $buyback_products_row['buyback_quantity_confirmed'] : $received_quantity);
            	
				$total_payable += $actual_amount;
			}
			
			$total_amount_array[$row['currency']] += $currencies->apply_currency_exchange($total_payable, $row['currency'], $row['currency_value']);
			
			//get customer
			$user_id = (int)$row['customers_id'];
			$user_name = $user_email = '';
			
			$customer_select_sql = "SELECT customers_firstname, customers_lastname, customers_email_address
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($user_id) . "'";
			$customer_result_sql = tep_db_query($customer_select_sql);
			if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
				$user_name = $customer_row['customers_firstname']. ' '.$customer_row['customers_lastname'];
				
				if ($access_customer_profile_page)	$user_name = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $user_id . '&action=edit') . '" target="_blank">'.$user_name.'</a>';
				
				$user_email = $customer_row['customers_email_address'];
			}
			
			if ($status_id == '2') {
				$time_diff_result = 0;
				$frm_processing_start_time_select_sql = "	SELECT date_added 
															FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
															WHERE buyback_request_group_id = '" . $row['buyback_request_group_id'] . "'
																AND buyback_status_id = 2 
															ORDER BY date_added desc
															LIMIT 1";
				$frm_processing_start_time_result_sql = tep_db_query($frm_processing_start_time_select_sql);
				if ($frm_processing_start_time_row = tep_db_fetch_array($frm_processing_start_time_result_sql)) {
					$to_time = date("Y-m-d H:i:s");
					$time_diff_result = floor(tep_day_diff($frm_processing_start_time_row['date_added'], $to_time, 'sec') / 60);
				}
			}
			//get remarks
			$latest_buyback_remark_select_sql = "	SELECT comments, changed_by 
													FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
													WHERE buyback_request_group_id = '" . $row['buyback_request_group_id'] . "'
														AND set_as_buyback_remarks=1
													LIMIT 1";
			$latest_buyback_remark_result_sql = tep_db_query($latest_buyback_remark_select_sql);
			if ($latest_buyback_remark_row = tep_db_fetch_array($latest_buyback_remark_result_sql)) {
				$buyback_remark = $latest_buyback_remark_row['comments'];
				$last_changed_by = $latest_buyback_remark_row['changed_by'];
			} else {
				$buyback_remark = $last_changed_by = '';
			}
			
			//get show restock character status
			$show_restock = (int)$row['show_restock'];
?>
								<tbody id="read_<?=$buyback_oid?>" class="<?=$row["orders_read_mode"] > 0 ? '' : 'boldText'?>">
								<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>" onMouseOver="showOverEffect(this, 'ordersListingRowOver', '<?=$status?>_prod_<?=$row_count?>')" onMouseOut="showOutEffect(this,'<?=$row_style?>','<?=$status?>_prod_<?=$row_count?>')" onClick="showClicked(this,'ordersListingRowOver', '<?=$status?>_prod_<?=$row_count?>')">
									<td class="ordersRecords"><?=$follow_up_icon?></td>
									<td class="ordersRecords"><?=$buyback_oid?></td>
									<td align="center" class="ordersRecords">
										<table border="0" cellpadding="0" cellspacing="0">
											<tr><td style="padding-left:3px;"><?=$buyback_from_site?></td></tr>
										</table>
									</td>
									<td class="ordersRecords"><?=$game_name?></td>
									<td class="ordersRecords"><?=$trade_mode_type_name?></td>
<?          if ($status_id == '1') { ?>
								  	<td align="center" class="ordersRecords">
<?
$delivery = '';
if ($row['orders_products_id'] > 0) {
	$delivery_mode_select_sql = "	SELECT opei.orders_products_extra_info_value 
									FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
									LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei 
										ON (op.parent_orders_products_id = opei.orders_products_id) 
									WHERE op.orders_products_id = '".(int)$row['orders_products_id']."' 
									AND opei.orders_products_extra_info_key = 'delivery_mode'";
	$delivery_mode_result_sql = tep_db_query($delivery_mode_select_sql);
	if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result_sql)) {
		$delivery_mode = $delivery_mode_row['orders_products_extra_info_value'];
		
		switch ($delivery_mode) {
			case '1':
				$delivery = tep_image(DIR_WS_ICONS . 'facetoface_active.gif', IMAGE_ICON_FACE_TO_FACE);
				break;
				
			case '2':
				$delivery = tep_image(DIR_WS_ICONS . 'pima.gif', IMAGE_ICON_PIMA);
				break;
				
			case '3':
				$delivery = tep_image(DIR_WS_ICONS . 'mail_active.gif', IMAGE_ICON_MAIL);
				break;	
				
			case '4':
				$delivery = tep_image(DIR_WS_ICONS . 'open_store.gif', IMAGE_ICON_OPEN_STORE);
				break;
				
			default:
				$delivery = '';
				break;
		}
	}
}

                                    if (tep_not_null($delivery)) {
                                        echo '&nbsp;' . $delivery;
                                    }
?>
								  	</td>
<?          } ?>
                                    <td class="ordersRecords"><span class="greenIndicator" id="tag_<?=$buyback_oid?>"><?=$tags_str?></span>&nbsp;</td>
								  	<td class="ordersRecords">
<?
										echo $user_name;
										if ($status_id == '1') {
											if ($show_restock) {
												echo '&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_green.gif');
											} else {
												echo '&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
											}
										}
?>
								  	</td>
								  	<? if ($customer_view_email_listing_permission) { ?>
								  	<td class="ordersRecords"><?=$user_email?></td>
									<? } ?>
								  	<td class="ordersRecords"><?=$row['date_submitted']?></td>
<?			if ($status_id == '3')	{ ?>
									<td class="ordersRecords" align="center"><?=($row['buyback_request_group_billing_status'] == '1' ? TEXT_TRANS_BILLED : TEXT_TRANS_NOT_BILLED)?></td>
									<td class="ordersRecords" align="center"><?=($row['buyback_request_group_verify_mode'] == '1' ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10))?></td>
<?			} ?>
								  	<td align="right" class="ordersRecords"><?=$currencies->format($total_payable, true, $row['currency'], $row['currency_value'])?></td>
<?
			if ($status_id == '1')	{
				$order_locked_img_link = '<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_oid.'&action=lock', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."lock_edit.gif", "Lock & Edit", "15", "16", 'align="top"').'</a>';
				$lock_owner_username = '';
				
				if (tep_not_null($row['locking_by'])) {
					$order_locked_img_link = '';
					
					$admin_email_address_select_sql = " SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$row['locking_by'] . "'";
					$admin_email_address_result_sql = tep_db_query($admin_email_address_select_sql);
					$admin_email_address_row = tep_db_fetch_array($admin_email_address_result_sql);
					
					$lock_owner_username = str_replace(strstr($admin_email_address_row["admin_email_address"], '@'), '', $admin_email_address_row["admin_email_address"]);
				}
?>
									<td align="center" class="ordersRecords"><span class="redIndicator"><?=($expiry_sec ? floor($expiry_sec/60) : TEXT_NOT_AVAILABLE)?></span></td>
									<td align="center" class="ordersRecords"><b><?=$lock_owner_username?></b></td>
									<td>
										<table border="0" cellspacing="0" cellpadding="2" width="100%">
											<tr>
												<td class="ordersRecords" align="center" width="50%"><?='<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_oid, 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "16", "16", 'align="top"').'</a>'?></td>
												<td class="ordersRecords" align="center"><?=$order_locked_img_link?></td>
											</tr>
										</table>
									
<?			} else { ?>
<?				if ($status_id == '2')	{ ?>
									<td class="ordersRecords" align="center"><?=$time_diff_result?></td>
<?				} ?>
								  	<td class="ordersRecords" align="center"><?='<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_oid, 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "16", "16", 'align="top"').'</a>'?></td>
<?			} ?>
									<td class="ordersRecords" align="center">
									<?
										if ($buyback_batch_update_permission && (int)$row['buyback_status_id'] == 1) {
											echo $row['buyback_dealing_type'] != 'ofp_deal_with_customers' ? tep_draw_checkbox_field("cb[{$buyback_oid}]", 1, false, '', "id='cb_{$buyback_oid}'") : '&nbsp;';
										}
									?>
									</td>
							 	</tr>
							    </tbody>
							 	<tr id="<?=$status.'_prod_'.$row_count?>" class="<?=$row_style?>" onMouseOver="showOverEffect(this, 'ordersListingRowOver', '<?=$status?>_main_<?=$row_count?>')" onMouseOut="showOutEffect(this, '<?=$row_style?>', '<?=$status?>_main_<?=$row_count?>')" onClick="showClicked(this, '<?=$row_style?>', '<?=$status?>_main_<?=$row_count?>')">
							 		<td></td>
							  		<td colspan="<?=($list_colspan_count)?>">
							  			<table cellspacing="0" cellpadding="2" border="0" width="100%">
								  			<TBODY class="hide" id="productinfo<?=$buyback_oid?>">
							  				<tr>
												<td class="ordersRecords" colspan="9" width="100%"><div style="border-bottom: 1px solid #996600;"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></div></td>
							  				</tr>
							  				<tr>
								  				<td class="ordersRecords" width="30%"><?=TABLE_HEADING_PRODUCT_NAME?></td>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_ACTUAL_QTY?></td>
<?			if ($status_id == 1) { ?>
								  				<td class="ordersRecords"><?=TABLE_HEADING_RSTK_CHARACTER?></td>
<?			} ?>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_QUANTITY?></td>
					  							<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_CONFIRMED_QUANTITY?></td>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_QUANTITY_RECEIVED?></td>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_BALANCE?></td>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_UNIT_PRICE?></td>
								  				<td class="ordersRecords"><?=TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT?></td>
							  				</tr>
							  				<tr>
								  				<td colspan="9"><div style="border-bottom: 1px solid #996600;"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></div></td>
							  				</tr>
<?
			//get products
			$buyback_product_select_sql = "	SELECT *
											FROM " . TABLE_BUYBACK_REQUEST . " AS br
											WHERE br.buyback_request_group_id ='" . $buyback_oid . "'";
			$buyback_product_result_sql = tep_db_query($buyback_product_select_sql);

			while ($buyback_product_row = tep_db_fetch_array($buyback_product_result_sql)) {
			    $is_provision_confirmed_qty = false;
			    
				$href_link = tep_href_link(FILENAME_CATEGORIES, "pID=".$buyback_product_row["products_id"]."&action=new_product");
                
				//get payable amount per order's detail
				$unit_price = $buyback_product_row['buyback_unit_price'];
				if ($buyback_product_row['buyback_quantity_confirmed'] == 0) {
				    $is_provision_confirmed_qty = true;
				    if ((int)$row['buyback_request_group_site_id'] == 0) {	// For website buyback
				    	$buyback_product_row['buyback_quantity_confirmed'] = $buyback_product_row['buyback_request_quantity'];
				    } else { // If CN buyback is recevied qty from cancel status
				    	$buyback_product_row['buyback_quantity_confirmed'] = $buyback_product_row['buyback_request_quantity'] * 1.1;
				    }
				}
				
				$received_quantity = (int)$buyback_product_row['buyback_quantity_received'];
				
				$actual_amount = $unit_price * ($received_quantity > $buyback_product_row['buyback_quantity_confirmed'] ? $buyback_product_row['buyback_quantity_confirmed'] : $received_quantity);
            	
				$balance = $buyback_product_row['buyback_request_quantity'] - $buyback_product_row['buyback_quantity_received'];
				
				$prod_info_select_sql = "	SELECT p.products_id, p.products_actual_quantity, pd.products_name, pc.categories_id
											FROM " . TABLE_PRODUCTS . " AS p
											LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
												ON p.products_id=pd.products_id
											LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
												ON pd.products_id=pc.products_id
											WHERE p.products_id = " . $buyback_product_row["products_id"] . "
												AND pc.products_is_link=0
												AND pd.language_id = '" . (int)$languages_id . "'
											ORDER BY pc.products_id ";
				$prod_info_result_sql = tep_db_query($prod_info_select_sql);
				$prod_info_row = tep_db_fetch_array($prod_info_result_sql);
				
				if ($row['buyback_dealing_type'] == 'ofp_deal_with_customers') {
					$restock_character = vip_order::tep_get_customer_trading_char($row['orders_products_id']);
				} else {
					if (tep_not_null($row['restock_character'])) {
						$restock_character = $row['restock_character'];
					} else {
						$restock_character = tep_get_buyback_restock_char($prod_info_row['products_id']);
					}
				}
				
				$prod_maincatpath = ($prod_info_row["products_id"]) ? "<span class='categoryPath'>[" . tep_output_generated_category_path_sq($prod_info_row['categories_id']) . "]</span>" : "**--This product is no longer existing in db--**";
?>
											<tr>
								  				<td class="ordersRecords"><a href="<?=$href_link?>" target="_blank"><?=$prod_maincatpath." > ".$prod_info_row['products_name']?></a></td>
												<td class="ordersRecords"><?=(int)$prod_info_row['products_actual_quantity']?></td>
<?
				if ($status_id == 1) {
					echo '						<td class="ordersRecords">'. ((tep_not_null($restock_character)) ? $restock_character : WARNING_TEXT_NO_RESTK_CHAR_ASSIGN) . (($row['show_restock'] == 1) ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif') : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif')) . '</td>';
				}
?>
								  				<td class="ordersRecords"><?=$buyback_product_row['buyback_request_quantity']?></td>
												<td class="ordersRecords"><span class="<?=($is_provision_confirmed_qty ? 'redIndicator' : '')?>"><?=(int)$buyback_product_row['buyback_quantity_confirmed']?></span></td>
								  				<td class="ordersRecords"><?=(int)$buyback_product_row['buyback_quantity_received']?></td>
								  				<td class="ordersRecords"><?=$balance?></td>
								  				<td class="ordersRecords"><?=$currencies->format($unit_price, true, $row['currency'], $row['currency_value'])?></td>
												<td class="ordersRecords"><?=$currencies->format($actual_amount, true, $row['currency'], $row['currency_value'])?></td>
			 				   					<td></td>
					   						</tr>
<?
			}

			if (tep_not_null($row['buyback_request_group_comment'])) {
?>
											<tr>
			    								<td colspan="<?=$list_colspan_count?>" class="ordersRecords">
			    									<br><br><?=$user_comments_label?><br>
			    								</td>
			    							</tr>
											<tr>
												<td bgcolor="#FFFFCC" colspan="<?=$list_colspan_count?>" class="ordersRecords" style="padding:5px;"><?=tep_output_string(nl2br($row['buyback_request_group_comment']))?></td>
											</tr>
<?			}

			if (tep_not_null($buyback_remark)) {
?>
											<tr>
			    								<td colspan="<?=$list_colspan_count?>" class="ordersRecords"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?><br><?=TEXT_ADMIN_COMMENT?><br></td>
			    							</tr>
											<tr>
												<td colspan="<?=$list_colspan_count?>" bgcolor="#FFFFCC">
													<table cellpadding="3" cellspacing="0" width="100%">
														<tr>
															<td class="ordersRecords" width="100%"><?=nl2br($buyback_remark).'<br>('.TEXT_LAST_CHANGED_BY.' '.$last_changed_by.')'?></td>
														</tr>
													</table>
												</td>
											</tr>
<?			} ?>
											<tr>
					    						<td colspan="<?=$list_colspan_count?>" class="ordersRecords"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?><br>&nbsp;</td>
					    					</tr>
											</tbody>
										</table>
									</td>
<?			if ($status_id == '1')	{ ?>
									<td></td>
<?			} ?>
								</tr>
<?			$row_count++;
		}
?>
								<tr>
									<td colspan="<?=($list_colspan_count-2)?>" align="right" class="smallText">
										<?
	                						if (count($total_amount_array)) {
	                							ksort($total_amount_array);
	                							foreach ($total_amount_array as $cur_code => $total_cur_amount) {
	                								echo $currencies->format($total_cur_amount, false, $cur_code) . '<br>';
	                							}
	                						}
	                					?>
									</td>
									<td colspan="2">&nbsp;</td>
								</tr>
<?
		if ($row_count > 0 && $status_id == 1) {
			$restock_menu_arr = array(array('id' => '', 'text' => 'With selected:'));
			$restock_menu_arr[] = array('id' => 'ShowSubmittedRestockCharacter', 'text' => 'Show Submitted Server RSTK CHAR');
			$restock_menu_arr[] = array('id' => 'HideAllRestockCharacter', 'text' => 'Hide All RSTK CHAR');
			
			$buyback_order_comment_arr = array(array('id' => '', 'text' => TEXT_SELECT_PREDEFINED_COMMENT));
			$orders_comments_info_select_sql = "	SELECT orders_comments_id, orders_comments_title 
													FROM " . TABLE_ORDERS_COMMENTS . " 
													WHERE orders_comments_status = 1 
														AND orders_comments_filename = '" . tep_db_input(FILENAME_BUYBACK_REQUESTS_INFO) . "'";
			$orders_comments_info_result_sql = tep_db_query($orders_comments_info_select_sql);
			while ($orders_comments_info_row = tep_db_fetch_array($orders_comments_info_result_sql)) {
				$buyback_order_comment_arr[] = array('id' => $orders_comments_info_row['orders_comments_id'], 'text' => $orders_comments_info_row['orders_comments_title']);
			}
?>
								<tr>
									<td colspan="7" class="smallText" valign="top" NOWRAP>&nbsp;</td>
									<td colspan="7" class="smallText" align="right">
<?              if ($buyback_batch_update_permission) { ?>
										<table border="0" width="75%" cellspacing="0" cellpadding="0">
											<tr>
												<td><?=tep_draw_pull_down_menu('batch_update_char_mode', $restock_menu_arr, 0, 'id="batch_update_char_mode"')?></td>
												<td><?=tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction(this.form, \''.$status_id.'\', \'orders_batch\')"', 'inputButton')?></td>
											</tr>
											<!-- Disable requested by Vince
											<tr>
												<td>
													<div id="buyback_order_comment_div" class="hide">
														<table border="0" cellspacing="0" cellpadding="0" width="100%">
															<tr>
											        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
											      			</tr>
															<tr>
																<td colspan="2"><?=tep_draw_pull_down_menu('buyback_order_comment', $buyback_order_comment_arr, '', ' id="buyback_order_comment" ')?></td>
															</tr>
															<tr>
																<td colspan="2" class="main"><?=tep_draw_checkbox_field('chk_set_as_buyback_remarks','1', true, ' id="chk_set_as_buyback_remarks" ') . ' ' . TEXT_ACTION_SET_BUYBACK_REMARK?></td>
															</tr>
															<tr>
																<td colspan="2" class="main"><?=tep_draw_checkbox_field('chk_user_notify','1', true, '', ' id="chk_user_notify" ') . ' ' . TEXT_NOTIFY?></td>
															</tr>
														</table>
													</div>
												</td>
											</tr>
											-->
										</table>
<?              } ?>
									</td>
								</tr>
<?		} ?>
							</table>
							</form>
						</td>
					</tr>
					<tr>
            			<td colspan="2">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_result_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_result_sql), tep_db_num_rows($orders_result_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'.$status_id], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
                					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$status_id], tep_get_all_get_params(array('page'.$status_id, 'cont', 'subaction', 'criteria_id'))."cont=1", 'page'.$status_id)?></td>
              					</tr>
            				</table>
            			</td>
					</tr>
					<script type="text/javascript">
						var product_ids_<?=$status_id?> = new Array();
						/* disable requested by Vince
						function order_comment_show(batch_update_char_mode) {
							var batch_update_char_mode_value = batch_update_char_mode.value;
							
							if (batch_update_char_mode_value == 'ShowSubmittedRestockCharacter') {
								document.getElementById('buyback_order_comment_div').className = 'show';
							} else {
								document.getElementById('buyback_order_comment_div').className = 'hide';
								document.getElementById('buyback_order_comment').value = '';
							}
						}
						*/

						function showHideDetails_<?=$status_id?>(link_obj) {
							var current_show = '';
							if (typeof(link_obj.innerText) != 'undefined') {
								current_show = link_obj.innerText == '<?=TEXT_SHOW_DETAILS?>' ? true : false;
							} else {
								current_show = link_obj.text == '<?=TEXT_SHOW_DETAILS?>' ? true : false;
							}

							var productinfo;

							if (current_show) {
								link_obj.innerHTML = '<?=TEXT_HIDE_DETAILS?>';
								for (i=0; i < product_ids_<?=$status_id?>.length; i++) {
									productinfo = document.getElementById('productinfo'+product_ids_<?=$status_id?>[i]);
									productinfo.className = "show";
								}
							} else {
								link_obj.innerHTML = '<?=TEXT_SHOW_DETAILS?>';

								for (i=0; i < product_ids_<?=$status_id?>.length; i++) {
									productinfo = document.getElementById('productinfo'+product_ids_<?=$status_id?>[i]);
									productinfo.className = "hide";
								}
							}
						}
						<?=$js?>
						
						<?	if (tep_not_null($_SESSION['buyback_lists_param']["page_refresh"])) { ?>
							    var page_url = "<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('buyback_request_group_id', 'cont'))."cont=1")?>";
							    setAutoRefresh("<?=$_SESSION['buyback_lists_param']["page_refresh"]?>", page_url);
					    <?	} ?>
					    
					    <?	foreach ($buyback_detail as $status_name => $res) {
							$order_str = count($res['buyback_request_group_id']) ? implode(',', $res['buyback_request_group_id']) : '';?>
							document.getElementById('<?=$status_name?>'+'_order_str').value = "<?=$order_str?>";
    					<?	} ?>
					</script>
<?
	}//end for each filter
} else {
	$refresh_options = array 	(	array ('id' => '', "text" => "No Refresh"),
									array ('id' => '1:00', "text" => "1"),
									array ('id' => '5:00', "text" => "5"),
									array ('id' => '15:00', "text" => "15")
								);
	
	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
									<td class="main" align="right">&nbsp;
									<!--	Reserved for saved criteria		-->
									</td>
								</tr>
								<tr>
			        				<td>
									<?
										echo tep_draw_form('buyback_lists_criteria', FILENAME_BUYBACK_REQUESTS, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '');
										echo tep_draw_hidden_field('buyback_lists_subaction', 'do_search', ' id="buyback_lists_subaction" ');
									?>
			        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
			        						<tr>
												<td class="main" width="12%"><?=ENTRY_ORDER_START_DATE?></td>
								    			<td class="main">
								    				<table border="0" cellspacing="2" cellpadding="0">
								    					<tr>
								    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', isset($_SESSION['buyback_lists_param']["start_date"]) ? $_SESSION['buyback_lists_param']["start_date"] : date("Y-m-d H:i", mktime(0, 0, date("s"), date("m"), date("d") - 6, date("Y"))), 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback_lists_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback_lists_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
								    						<td class="main" width="5%">&nbsp;</td>
								    						<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
								    						<td class="main" width="1%">&nbsp;</td>
								    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION['buyback_lists_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.buyback_lists_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.buyback_lists_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
								    					</tr>
								    				</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
											<tr>
												<td class="main"><?=ENTRY_ORDER_ID?></td>
								    			<td class="main"><?=tep_draw_input_field('order_id', $_SESSION['buyback_lists_param']["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_CATEGORY?></td>
								    			<td class="main" valign="top" nowrap>
													<?=tep_draw_pull_down_menu("cat_id", tep_get_category_tree_cacheable(0, '___'), $_SESSION['buyback_lists_param']["cat_id"], ' id="cat_id" ')?>
					    							<?=tep_draw_checkbox_field("include_subcategory", 1, isset($_SESSION['buyback_lists_param']["include_subcategory"]) && $_SESSION['buyback_lists_param']["include_subcategory"] == 0 ? false : true, '', ' id="include_subcategory" ') . '&nbsp;' . TEXT_INCLUDE_SUBCATEGORY?>	
												</td>
											</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main" valign="top"><?=ENTRY_ORDER_STATUS?></td>
								    			<td>
								    				<table border="0" cellspacing="2" cellpadding="0">
								    			<?
								    				if (count($status_options)) {
							    						echo '<tr><td class="main">'.tep_draw_checkbox_field('order_status_any', '1', isset($_SESSION['buyback_lists_param']) && count($_SESSION['buyback_lists_param']["order_status"]) ? false : true, '', 'id="order_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>';
							    						echo '<tr>';
							    						foreach ($status_options as $id => $title) {
							    							$order_status_display_str = '';

								    						$order_status_display_str .=
								    							'	<td class="main">'.
								    									tep_draw_checkbox_field('order_status[]', $id, isset($_SESSION['buyback_lists_param']) ? (is_array($_SESSION['buyback_lists_param']["order_status"]) && in_array($id, $_SESSION['buyback_lists_param']["order_status"]) ? true : false) : (false), '', 'onClick=verify_status_selection();') . '
								    								</td>
								    								<td class="main">'.$title.'</td>';
							    							echo $order_status_display_str;
								    					}
								    					echo '</tr>';
								    				}
								    			?>
								    				</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
			          							<td class="main"><?=ENTRY_CUSTOMER_EMAIL?></td>
			          							<td class="main"><?=tep_draw_input_field('customer_email', $_SESSION['buyback_lists_param']["customer_email"], ' id="customer_email" ');?></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"valign="top"><?=ENTRY_BUYBACK_ORDER_SUBMIT_FROM?></td>
								    			<td>
								    				<table border="0" cellspacing="2" cellpadding="0">
								    					<tr>
								    					<?
								    					foreach ($buyback_fr_websites_option as $web_id => $web_name) {
							    							echo '	<td class="main">'.
								    									tep_draw_checkbox_field('buyback_sites[]', (string)$web_id, isset($_SESSION['buyback_lists_param']) ? (is_array($_SESSION['buyback_lists_param']["buyback_sites"]) && in_array($web_id, $_SESSION['buyback_lists_param']["buyback_sites"]) ? true : false) : true, '', '') . '
								    								</td>
								    								<td class="main">'.$web_name.'</td>';
								    					}
								    					?>
								    					</tr>
								    				</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
			          							<td class="main" valign="top"><?=ENTRY_HEADING_DELIVERY_OPTION?></td>
								    			<td class="main">
									    			<table border="0" cellspacing="2" cellpadding="0">
									    			<?
									    				if (count($deliver_option)) {
								    						echo '<tr><td class="main">'.tep_draw_checkbox_field('deliver_option_any', '1', isset($_SESSION['buyback_lists_param']) && count($_SESSION['buyback_lists_param']["deliver_option"]) ? false : true, '', 'id="deliver_option_any" onClick="set_deliver_option(this);"') . '</td><td class="main" colspan="'.(count($deliver_option)*2-1).'">'.TEXT_ANY.'</td></tr>';
								    						echo '<tr>';
								    						for ($deliver_cnt=0; $deliver_cnt < count($deliver_option); $deliver_cnt++) {
								    							$deliver_option_display_str = '';
																$id = $deliver_cnt;
										    					$title = $deliver_option[$deliver_cnt];
										    					
									    						$deliver_option_display_str .=
									    							'	<td class="main">'.
									    									tep_draw_checkbox_field('deliver_option[]', (string)$id, isset($_SESSION['buyback_lists_param']) ? (is_array($_SESSION['buyback_lists_param']["deliver_option"]) && in_array($id, $_SESSION['buyback_lists_param']["deliver_option"]) ? true : false) : false, '', 'onClick=verify_deliver_selection();') . '
									    								</td>
									    								<td class="main">'.$title.'</td>';
								    							echo $deliver_option_display_str;
									    					}
									    					echo '</tr>';
									    				}
									    			?>
							    					</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_HEADING_PAGE_REFRESH?></td>
								    			<td class="main"><?=tep_draw_pull_down_menu("page_refresh", $refresh_options, tep_not_null($_SESSION['buyback_lists_param']["page_refresh"]) ? $_SESSION['buyback_lists_param']["page_refresh"] : '', '')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
								    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['buyback_lists_param']["show_records"]) ? $_SESSION['buyback_lists_param']["show_records"] : '', '')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			        					</table>
			        				</td>
			        			</tr>
			        			<tr>
				  					<td>
				  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
				  							<tr>
				  								<td align="right">
		  											<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  											<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link(FILENAME_BUYBACK_REQUESTS, 'action=reset_session'), '', 'inputButton')?>
				  								</td>
				  							</tr>
				  						</table>
				  					</td>
				  				</tr>
				  				<tr>
									<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
								</tr>
								</form>
								<script language="javascript"><!--
									function form_checking(form_obj, action) {
										if (action=='save_search') {
											if (trim_str(document.getElementById('search_name').value) != '') {
												document.getElementById('buyback_lists_subaction').value = 'save_search';
												form_obj.SaveSearchBtn.disabled = true;
												form_obj.SaveSearchBtn.value = 'Please wait...';
											} else {
												alert(" You must enter a name for saving your search criteria!");
												document.getElementById('search_name').value = '';
												document.getElementById('search_name').focus();
												return false;
											}
										} else {
											document.getElementById('buyback_lists_subaction').value = 'do_search';
										}

									    form_obj.submit();
										return true;
						    		}

						    		function resetControls(controlObj) {
										if (trim_str(controlObj.value) != '') {
											document.buyback_lists_criteria.start_date.value = '';
											document.buyback_lists_criteria.end_date.value = '';
											document.getElementById('order_status_any').checked = true;
											set_status_option(document.getElementById('order_status_any'));
											document.buyback_lists_criteria.show_records.selectedIndex = 0;
							    		} else {
							    			controlObj.value = '';
							    		}
									}
									function set_status_option(any_status_obj) {
						    			var multi_status_select = document.buyback_lists_criteria.elements['order_status[]'];
						    			if (any_status_obj.checked == true) {
											for (i=0;i<multi_status_select.length;i++) {
												multi_status_select[i].checked = false;

												var cur_status_id = multi_status_select[i].value;
						    					var multi_tags_select = document.buyback_lists_criteria.elements['status_'+cur_status_id+'[]'];
												if (typeof(multi_tags_select) != 'undefined') {
													if (typeof(multi_tags_select.length) != 'undefined') {
														for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
															multi_tags_select[tag_cnt].disabled = true;
															multi_tags_select[tag_cnt].checked = false;
														}
													} else {
														multi_tags_select.disabled = true;
														multi_tags_select.checked = false;
													}

												}
											}
						    			} else {	// force to check if no any order status option is selected
						    				var selected_count = 0;
						    				for (i=0;i<multi_status_select.length;i++) {
						    					var cur_status_id = multi_status_select[i].value;
						    					var multi_tags_select = document.buyback_lists_criteria.elements['status_'+cur_status_id+'[]'];
												if (multi_status_select[i].checked == true) {
													selected_count++;
													if (typeof(multi_tags_select) != 'undefined') {
														if (typeof(multi_tags_select.length) != 'undefined') {
															for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
																multi_tags_select[tag_cnt].disabled = false;
															}
														} else {
															multi_tags_select.disabled = false;
														}
													}
												} else {
													if (typeof(multi_tags_select) != 'undefined') {
														if (typeof(multi_tags_select.length) != 'undefined') {
															for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
																multi_tags_select[tag_cnt].disabled = true;
																multi_tags_select[tag_cnt].checked = false;
															}
														} else {
															multi_tags_select.disabled = true;
															multi_tags_select.checked = false;
														}
													}
												}
											}
											if (!selected_count) {
												any_status_obj.checked = true;
											}
						    			}
						    		}

						    		function verify_status_selection() {
						    			var multi_status_select = document.buyback_lists_criteria.elements['order_status[]'];
						    			var selected_count = 0;
					    				for (i=0;i<multi_status_select.length;i++) {
					    					var cur_status_id = multi_status_select[i].value;
						    				var multi_tags_select = document.buyback_lists_criteria.elements['status_'+cur_status_id+'[]'];
											if (multi_status_select[i].checked == true) {
												selected_count++;
												if (typeof(multi_tags_select) != 'undefined') {
													if (typeof(multi_tags_select.length) != 'undefined') {
														for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
															multi_tags_select[tag_cnt].disabled = false;
														}
													} else {
														multi_tags_select.disabled = false;
													}
												}
											} else {
												if (typeof(multi_tags_select) != 'undefined') {
													if (typeof(multi_tags_select.length) != 'undefined') {
														for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
															multi_tags_select[tag_cnt].disabled = true;
															multi_tags_select[tag_cnt].checked = false;
														}
													} else {
														multi_tags_select.disabled = true;
														multi_tags_select.checked = false;
													}
												}
											}
										}
										if (!selected_count) {
											document.getElementById('order_status_any').checked = true;
										} else {
											document.getElementById('order_status_any').checked = false;
										}
						    		}
						    		
						    		function set_deliver_option(any_status_obj) {
										var multi_deliver_select = document.buyback_lists_criteria.elements['deliver_option[]'];
										if (any_status_obj.checked == true) {
											for (i=0;i<multi_deliver_select.length;i++) {
												multi_deliver_select[i].checked = false;
											}
										} else {	// force to check if no any order status option is selected
											var selected_count = 0;
											for (i=0;i<multi_deliver_select.length;i++) {
												if (multi_deliver_select[i].checked == true) {
													selected_count++;
												}
											}
											if (!selected_count) {
												any_status_obj.checked = true;
											}
										}
									}
									
									function verify_deliver_selection() {
										var multi_deliver_select = document.buyback_lists_criteria.elements['deliver_option[]'];
										var selected_count = 0;
										for (i=0;i<multi_deliver_select.length;i++) {
											if (multi_deliver_select[i].checked == true) {
												selected_count++;
											}
										}
										if (!selected_count) {
											document.getElementById('deliver_option_any').checked = true;
										} else {
											document.getElementById('deliver_option_any').checked = false;
										}
									}

						    		set_status_option(document.getElementById('order_status_any'));
						    		set_deliver_option(document.getElementById('deliver_option_any'));
						    	//-->
								</script>
							</table>
						</td>
					</tr>
<?
}
?>
        		</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>