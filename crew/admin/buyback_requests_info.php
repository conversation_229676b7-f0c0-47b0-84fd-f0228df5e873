<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'store_point.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');

/******************************************************************
	Buyback Order Expiration Cancellation Task
******************************************************************/
include_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
//tep_cancel_expirable_buyback_request();
// End of Buyback Order Expiration Cancellation Task
require_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'edit_order.php');

define('PARTIAL_RECEIVE_STATUS', 2);
define('CANCEL_STATUS', 4);
define('DISPLAY_PRICE_DECIMAL', 6);

//load status labels
$status_options = array();
$order_status_select_sql = "SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY buyback_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["buyback_status_id"]] = $order_status_row["buyback_status_name"];
}

$log_object = new log_files($login_id);
$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$view_supplier_status_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLER_INFO_VIEWING');
$verify_buyback_order_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_VERIFY_STATUS');
$view_buyback_statistics_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_VIEW_ORDER_STATISTICS');
$back_order_stat_link_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_BACK_ORDER_LINK');
$view_rstk_char_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_VIEW_RSTK_CHAR');
$hide_or_show_rstk_char_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_EDIT_RSTK_CHAR_STATUS');
$view_screenshot_permission = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_VIEW_SCREENSHOT');

$flag_icon_array = array(	'flagOrange' => array('on' => 'icon_status_orange_yes.gif', 'off' => 'icon_status_no.gif'),
							'flagPink' => array('on' => 'icon_status_pink_yes.gif', 'off' => 'icon_status_no.gif'),
							'flagRed' => array('on' => 'icon_status_red_yes.gif', 'off' => 'icon_status_no.gif')
							);

$buyback_request_group_id = (int)$_REQUEST['buyback_request_group_id'];
$set_buyback_remark_id = (int)$_REQUEST['set_buyback_remark_id'];

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

//buyback order checking
$buyback_order_checking_select_sql = "SELECT buyback_request_group_id, buyback_status_id FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id = '" . (int)$buyback_request_group_id . "'";
$buyback_order_checking_result_sql = tep_db_query($buyback_order_checking_select_sql);
$buyback_order_exists = true;
if (!tep_db_num_rows($buyback_order_checking_result_sql)) {
	$buyback_order_exists = false;
  	$messageStack->add(sprintf(ERROR_BUYBACK_ORDER_NOT_EXISTS, $buyback_request_group_id), 'error');
}

if (tep_not_null($action)) {
	switch ($action) {
		case 'lock':
			tep_order_lock($buyback_request_group_id, ORDERS_LOG_LOCK_ORDER, $login_id);
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_request_group_id));
			break;
		case 'set_remark':
			$set_remark = true;
			
			$buyback_request_locked_by_select_sql = "	SELECT brg.buyback_status_id, l.locking_by 
														FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
														LEFT JOIN " . TABLE_LOCKING . " AS l 
															ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
														WHERE brg.buyback_request_group_id = '" . (int)$buyback_request_group_id . "'";
			$buyback_request_locked_by_result_sql = tep_db_query($buyback_request_locked_by_select_sql);
			$buyback_request_locked_by_row = tep_db_fetch_array($buyback_request_locked_by_result_sql);
			
			if ($buyback_request_locked_by_row['buyback_status_id'] == 1) {
				if ($buyback_request_locked_by_row['locking_by'] != $login_id) {
					$set_remark = false;
				}
			}
			
			if ($set_remark) {
				tep_db_query("UPDATE " . TABLE_BUYBACK_STATUS_HISTORY . " SET set_as_buyback_remarks=0 WHERE buyback_request_group_id = '" . $buyback_request_group_id . "';");
				tep_db_query("UPDATE " . TABLE_BUYBACK_STATUS_HISTORY . " SET set_as_buyback_remarks=1 WHERE buyback_status_history_id = '" . $set_buyback_remark_id . "';");
				tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('set_buyback_remark_id', 'action'))));
			}
			break;
		case 'change_order':
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_request_group_id));
			
			break;
		case 'partial_receive':
			$oid = $_REQUEST['buyback_request_group_id'];
		    
			$processing_order_select_sql = "SELECT c.customers_email_address, br.orders_id, br.orders_products_id, brg.customers_id, brg.buyback_request_group_user_type, brg.buyback_request_group_site_id, brg.buyback_status_id, br.buyback_dealing_type 
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
											INNER JOIN " . TABLE_CUSTOMERS . " AS c 
												ON (brg.customers_id = c.customers_id) 
											WHERE brg.buyback_request_group_id = '".tep_db_input($oid)."' 
												AND (brg.buyback_status_id = ". PARTIAL_RECEIVE_STATUS ." 
												OR brg.buyback_status_id = ". CANCEL_STATUS .")";
            
			$processing_order_result_sql = tep_db_query($processing_order_select_sql);
			if ($processing_order_row = tep_db_fetch_array($processing_order_result_sql)) {
				$buyback_from_site = $processing_order_row['buyback_request_group_site_id'];
				$buyback_status_id = $processing_order_row['buyback_status_id'];
				$orders_id = $processing_order_row['orders_id'];
				$customers_id = $processing_order_row['customers_id'];
				$customer_email = $processing_order_row['customers_email_address'];
				$orders_products_id = $processing_order_row['orders_products_id'];
				$buyback_dealing_type = $processing_order_row['buyback_dealing_type'];
			} else {
				$messageStack->add_session(sprintf(WARNING_RESTOCK_NON_PENDING_ORDERS, 'The order', $status_options[(int)PARTIAL_RECEIVE_STATUS]), 'warning');
    			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
    			
        		break;
			}
			
			$products = $_POST['products'];
			$partial_recv = $_POST['partial_receive'];
			$partial_recv_operator = $_POST['partial_receive_sign'];
			
			$partial_receive_str = '';
			
			if (is_array($partial_recv) && count($partial_recv)) {
				$br_id_keys = array_keys($partial_recv);
				
				foreach ($br_id_keys as $index => $key) {
					$qty = $partial_recv[$key];
					
					if (tep_not_null($qty) && is_numeric($qty)) {
						$operator = $partial_recv_operator[$key];//+ or -
						$pid = (int)$products[$index];
						
						$update_qty_array = array();
						
						$cid = tep_get_actual_product_cat_id($pid);
						if (tep_check_cat_tree_permissions(FILENAME_BUYBACK_REQUESTS, $cid) != 1) {
							continue;
						}
						
						$qty = intval($operator . $qty);

						if (abs($qty) > 0) {
							$product_info_select_sql = "SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path 
														FROM " . TABLE_PRODUCTS . " 
														WHERE products_id = '" . tep_db_input($pid) . "'";
							$product_info_result_sql = tep_db_query($product_info_select_sql);
							$product_info_row = tep_db_fetch_array($product_info_result_sql);
							
							$buyback_balance_select_sql = "	SELECT buyback_request_quantity, buyback_quantity_confirmed, buyback_quantity_received 
															FROM " . TABLE_BUYBACK_REQUEST . "
															WHERE buyback_request_id = '" . tep_db_input($key) ."'";
							$buyback_balance_result_sql = tep_db_query($buyback_balance_select_sql);
							$buyback_balance_row = tep_db_fetch_array($buyback_balance_result_sql);
							
							$selling_quantity = $buyback_from_site > 0 ? (int)$buyback_balance_row['buyback_quantity_confirmed'] : (int)$buyback_balance_row['buyback_request_quantity'];
							$received_quantity = (int)$buyback_balance_row['buyback_quantity_received'];
							$balance = $selling_quantity - $received_quantity;
							
							if ($buyback_status_id == CANCEL_STATUS && $buyback_balance_row['buyback_quantity_confirmed'] == 0) {
							    $balance = ($buyback_balance_row['buyback_request_quantity'] * 1.1) - $received_quantity;
							}
							
							if ($qty > 0) {	// positive value
								if ($qty <= $balance || ($buyback_status_id == CANCEL_STATUS && $balance == 0)) {
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '+', 
																			'value'=> $qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '+', 
																			'value'=> $qty)
																);
										tep_set_product_qty($pid, $update_qty_array, true, sprintf(LOG_BUYBACK_ORDER, $oid), '');
									}
									
									$received_qty_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST . " 
																SET buyback_quantity_received = IF(buyback_quantity_received IS NULL, 0, buyback_quantity_received) + " . $qty . " 
																WHERE buyback_request_id ='" . tep_db_input($key) . "' 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_str .= "&raquo; ". tep_get_products_name($pid) . "\tx " . $qty . "\n";
									
									if ($buyback_dealing_type != 'extra_inventory') {
										$edit_order_obj = new edit_order($login_id, $login_email_address, $orders_id);
										$edit_order_obj->buyback_deliver_order($qty, $oid, $orders_products_id, $_SESSION['login_email_address'], true, $messageStack);
										$edit_order_obj->set_customers_comment($orders_id, '0', sprintf(TEXT_BUYBACK_ORDER_DELIVERED_REFERENCE_REMARK, $oid), '0');	
									}
								}
							} else if ($qty < 0) {	// negative receive qty => deducting previously received qty
								$deduct_qty = abs($qty);
								if ($deduct_qty <= $received_quantity) {
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty)
																);
										tep_set_product_qty($pid, $update_qty_array, true, sprintf(LOG_BUYBACK_ORDER, $oid), '');
									}
									$received_qty_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST . " 
																SET buyback_quantity_received = IF(buyback_quantity_received IS NULL, 0, buyback_quantity_received) - " . $deduct_qty . " 
																WHERE buyback_request_id ='" . tep_db_input($key) . "' 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_str .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . "\n";
									
									if ($buyback_dealing_type != 'extra_inventory') {
										$edit_order_obj = new edit_order($login_id, $login_email_address, $orders_id);
										$edit_order_obj->buyback_deliver_order($deduct_qty, $oid, $orders_products_id, $_SESSION['login_email_address'], true, $messageStack, '-');
										$edit_order_obj->set_customers_comment($orders_id, '0', sprintf(TEXT_BUYBACK_ORDER_DEDUCTED_REFERENCE_REMARK, $oid), '0');	
										
										// Cancel request customer receive comfirmation, Yes and No
										$update_history_data_sql['changed_by'] = $login_email_address;
										$update_history_data_sql['received'] = '0';
										$update_history_data_sql['last_updated'] = 'now()';
										tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' buyback_request_group_id = "'.(int)$oid.'" AND received IS NULL');
										
										// Remove hla_info
										$remove_hla_info_sql = "DELETE FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id = '".tep_db_input($orders_products_id)."' AND (orders_products_extra_info_key = 'hla_info_stage_1' OR orders_products_extra_info_key = 'hla_info_stage_2')";
										tep_db_query($remove_hla_info_sql);
									}
								}
							}
						}
					}
				}
			}
			
			$extra_receive = $_POST['extra_receive'];
			$extra_receive_operator = $_POST['extra_receive_sign'];
			
			if (is_array($extra_receive) && count($extra_receive)) {
				$br_id_keys = array_keys($extra_receive);
				
				foreach ($br_id_keys as $index => $key) {
					$qty = $extra_receive[$key];
					
					if (tep_not_null($qty) && is_numeric($qty)) {
						$operator = $extra_receive_operator[$key];//+ or -
						$pid = (int)$products[$index];
						
						$update_qty_array = array();
						
						$cid = tep_get_actual_product_cat_id($pid);
						if (tep_check_cat_tree_permissions(FILENAME_BUYBACK_REQUESTS, $cid) != 1) {
							continue;
						}
						
						$qty = intval($operator . $qty);
						
						if (abs($qty) > 0) {
							$product_info_select_sql = "SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path 
														FROM " . TABLE_PRODUCTS . " 
														WHERE products_id = '" . tep_db_input($pid) . "'";
							$product_info_result_sql = tep_db_query($product_info_select_sql);
							$product_info_row = tep_db_fetch_array($product_info_result_sql);
							
							$buyback_balance_select_sql = "	SELECT buyback_request_quantity, buyback_quantity_confirmed, buyback_quantity_received 
															FROM " . TABLE_BUYBACK_REQUEST . "
															WHERE buyback_request_id = '" . tep_db_input($key) ."'";
							$buyback_balance_result_sql = tep_db_query($buyback_balance_select_sql);
							$buyback_balance_row = tep_db_fetch_array($buyback_balance_result_sql);
							
							$selling_quantity = $buyback_from_site > 0 ? (int)$buyback_balance_row['buyback_quantity_confirmed'] : (int)$buyback_balance_row['buyback_request_quantity'];
							$received_quantity = (int)$buyback_balance_row['buyback_quantity_received'];
							$balance = $selling_quantity - $received_quantity;
							
							if ($buyback_status_id == CANCEL_STATUS && $buyback_balance_row['buyback_quantity_confirmed'] == 0) {
							    $balance = ($buyback_balance_row['buyback_request_quantity'] * 1.1) - $received_quantity;
							}
							
							if ($qty > 0) {	// positive value
								if ($balance <= 0) {	// Only can add as extra when no balance remaining
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '+', 
																			'value'=> $qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '+', 
																			'value'=> $qty)
																);
									}
									
									$received_qty_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST . " 
																SET buyback_quantity_received = IF(buyback_quantity_received IS NULL, 0, buyback_quantity_received) + " . $qty . " 
																WHERE buyback_request_id ='" . tep_db_input($key) . "' 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);

									$partial_receive_str .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . " (EXTRA)\n";
								}
							} else if ($qty < 0) {	// negative receive qty => deducting previously extra received qty
								$deduct_qty = abs($qty);
								if ($deduct_qty <= abs($balance)) {
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty)
																);
									}
									$received_qty_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST . " 
																SET buyback_quantity_received = IF(buyback_quantity_received IS NULL, 0, buyback_quantity_received) - " . $deduct_qty . " 
																WHERE buyback_request_id ='" . tep_db_input($key) . "' 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_str .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . " (EXTRA)\n";
								}
							}
							
							if (count($update_qty_array)) {
								// This function will handle the qty adjustment and keep the log if asking so
								tep_set_product_qty($pid, $update_qty_array, true, sprintf(LOG_BUYBACK_ORDER, $oid), 'Extra product quantity entered from Buyback Edit Order page.');
								
								$messageStack->add_session(sprintf(SUCCESS_EXTRA_QUANTITY_UPDATED, $qty, $pid), 'success');
							}
						}
					}
				}
			}
			
			if (tep_not_null($partial_receive_str)) {
				$receive_str = strip_tags($partial_receive_str);
				
				if (tep_not_null($receive_str)) {
					$receive_str = "The following items have been received:" . "\n" . $receive_str;
					
					$buyback_history_data_array = array('buyback_request_group_id' => $oid,
								                        'buyback_status_id' => '0',
														'date_added' => 'now()',
														'customer_notified' => '0',
														'comments' => $receive_str,
														'changed_by' => $login_email_address
														);
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			
			break;
		case 'update_order':
			//updated status and/or comments
			$oid = $_REQUEST['buyback_request_group_id'];
			
			$admin_comment = tep_db_prepare_input($_POST['admin_comment']);
			$to_status = (int)$_POST['status'];
			$user_notify = (int)$_POST['chk_user_notify'];
			$set_as_buyback_remarks = (int)$_POST['chk_set_as_buyback_remarks'];
			
			//if to_status == 0, means user didnt change status
			$buyback_order_select_sql = "	SELECT brg.buyback_status_id, brg.customers_id, brg.buyback_request_group_user_type, brg.buyback_request_group_site_id, brg.buyback_request_group_date, l.locking_by, br.buyback_dealing_type, br.orders_id, br.orders_products_id, br.buyback_request_quantity, br.buyback_quantity_received, ci.customer_info_selected_language_id  
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
												ON (brg.buyback_request_group_id = br.buyback_request_group_id)	
											LEFT JOIN " . TABLE_LOCKING . " AS l 
												ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
											LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
												ON (brg.customers_id = ci.customers_info_id) 
											WHERE brg.buyback_request_group_id = '" . tep_db_input($oid) . "'";
			$buyback_order_result_sql = tep_db_query($buyback_order_select_sql);
			if (tep_db_num_rows($buyback_order_result_sql) && (isset($status_options[$to_status]) || $to_status == 0)) {
				$row = tep_db_fetch_array($buyback_order_result_sql);
				
				$from_status = $row['buyback_status_id'];
				$user_id = $row['customers_id'];
				
				if ($from_status == 1) {
					if ($row['locking_by'] != $login_id) {
						$messageStack->add_session(ERROR_ORDER_NOT_LOCK, 'error');
						
						tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
					}
				}
				
				if ($HTTP_POST_VARS["status_DB_prev"] != $from_status) {	// hidden value passed does not match current status value
	    			$messageStack->add_session(sprintf(WARNING_ORDER_NOT_UPDATED, $oid), 'warning');
	    			$messageStack->add_session("This order status just update by someone!", 'warning');
	    			
	    			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
	        		
	        		break;
	    		} else {
	    			// Check for status update permission and ONLY website buyback can updated from Pending to Processing
		    		if ($to_status > 0 && !tep_check_status_update_permission('B', $login_groups_id, $HTTP_POST_VARS["status_DB_prev"], $to_status)) {
			    		$messageStack->add_session(ERROR_TRANS_UPDATE_DENIED, 'error');
						tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
						
						break;
			    	}
			    }
				
				if ($to_status > 0) {
					$status_update = true;
					switch ($from_status) {
						case 1://from pending
	  						//Always Hide the restock character if status not pending
							$character_visibility_arr = array('show_restock' => '0');
							
							$success = tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $character_visibility_arr, 'update', "buyback_request_group_id = '$oid'");
							
							if ($success) {
								if (tep_not_null($row['locking_by'])) {
									tep_order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, $login_id);
								}
								// unlock the customer order.
								if ($row['buyback_dealing_type'] == 'ofp_deal_with_customers' && (int)$to_status == 4) {
									$hold_by_other_buyback_order_select_sql = "	SELECT brg.buyback_request_group_id 
																				FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
																				INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
																					ON (brg.buyback_request_group_id = br.buyback_request_group_id)
																				WHERE brg.buyback_status_id = '1' 
																					AND brg.buyback_request_group_id <> '".tep_db_input($buyback_request_group_id)."' 
																					AND br.orders_id = '" . tep_db_input($row['orders_id']) . "'
																					AND br.orders_products_id <> 0
																					AND buyback_dealing_type = 'ofp_deal_with_customers' 
																				LIMIT 1";
									$hold_by_other_buyback_order_result_sql = tep_db_query($hold_by_other_buyback_order_select_sql);
									if (!tep_db_num_rows($hold_by_other_buyback_order_result_sql)) { // No other buyback orders for thie customer order
										//check is system lock the order
										$check_order_lock_sql = "	SELECT orders_locked_by 
								    								FROM " . TABLE_ORDERS . " 
								    								WHERE orders_id = '" . tep_db_input($row['orders_id']) . "'";
										$check_order_lock_result = tep_db_query($check_order_lock_sql);
										if ($check_order_lock_row = tep_db_fetch_array($check_order_lock_result)) {
											if ($check_order_lock_row['orders_locked_by'] == 0) {
												//unlock order
												tep_customer_order_locking($row['orders_id'], '0', 'unlock');
											}
										}
									}
								}
								$messageStack->add_session('Hide Restock Character operation successful.', 'success');
							} else {
								$messageStack->add_session('Hide Restock Character operation failed.');
							}
							
							break;
						case 2: //from processing
							switch ($to_status) {
								case 1:
									if ($row['buyback_quantity_received'] > 0) {
										$messageStack->add_session("This Buyback Order has received quantity. Please deduct the received quantity before update to Pending status", 'warning');
	    								tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
									} else {
										// Skipped For Extra Inventory
										if (tep_not_null($row['orders_id']) && $row['orders_id'] > 0) {
											$get_selected_lang_id_sql = "	SELECT ci.customer_info_selected_language_id 
																			FROM ".TABLE_ORDERS." AS o 
																			INNER JOIN ".TABLE_CUSTOMERS_INFO." AS ci 
																				ON (o.customers_id = ci.customers_info_id) 
																			WHERE o.orders_id = '".(int)$row['orders_id']."'";
											$get_selected_lang_id_result =	tep_db_query($get_selected_lang_id_sql);
											
											if ($get_selected_lang_id_row = tep_db_fetch_array($get_selected_lang_id_result)) {
												// Ensure buyback_request_group_id only can have one record in orders_products_history TABLE
												tep_db_query("DELETE FROM ".TABLE_ORDERS_PRODUCTS_HISTORY." WHERE buyback_request_group_id = '".(int)$buyback_request_group_id."' AND received IS NOT NULL");
												
												include_once(DIR_WS_LANGUAGES . 'buyback_system.php');
										
												if ($get_selected_lang_id_row['customer_info_selected_language_id'] == 2) {
													$comments_reason_to_canceled = COMMENTS_REASON_TO_CANCELED_CHINESE;
												} else {
													$comments_reason_to_canceled = COMMENTS_REASON_TO_CANCELED_ENGLISH;
												}
												// Simulate Click on No button
												$update_history_data_sql['dispute_comment'] = $comments_reason_to_canceled;
												$update_history_data_sql['changed_by'] = $login_email_address;
												$update_history_data_sql['received'] = '0';
												$update_history_data_sql['last_updated'] = 'now()';
												tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' buyback_request_group_id = "'.(int)$buyback_request_group_id.'" AND received IS NULL');	
											}
										}
									}
									break;
								case 3: //to complete
	          						$cron_pending_credit_mature_period = 0;
									
									// Updating the product average buyback price
									$buyback_product_select_sql = "	SELECT products_id, buyback_quantity_received, buyback_request_quantity, buyback_unit_price, buyback_quantity_confirmed
																	FROM " . TABLE_BUYBACK_REQUEST . "
																	WHERE buyback_request_group_id = '".tep_db_input($oid)."'";
	          						$buyback_product_result_sql = tep_db_query($buyback_product_select_sql);
	          						
	          						while ($buyback_product_row = tep_db_fetch_array($buyback_product_result_sql)) {
	          							$current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
	          							$current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
										if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
											$prev_buyback_qty = (int)$current_buyback_info_row["products_buyback_quantity"];
											$prev_buyback_price = (double)$current_buyback_info_row["products_buyback_price"];
											
											$order_buyback_qty = (int)$buyback_product_row['buyback_quantity_received'];
											// Already take into account the $order_buyback_qty
											
											$unit_price = $buyback_product_row['buyback_unit_price'];
											/*
											if ($row['buyback_request_group_site_id'] == 0) {	// For website buyback, assume confirm qty same as request qty
												$buyback_product_row['buyback_quantity_confirmed'] = $buyback_product_row['buyback_request_quantity'];
											}
											*/
											$received_quantity = (int)$buyback_product_row['buyback_quantity_received'];
											$total_buyback_price = $unit_price * ($received_quantity > $buyback_product_row['buyback_quantity_confirmed'] ? $buyback_product_row['buyback_quantity_confirmed'] : $received_quantity);
											
											$new_buyback_qty = $prev_buyback_qty + $order_buyback_qty;
											
											if ($new_buyback_qty > 0) {
												$new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) + ($total_buyback_price) ) / $new_buyback_qty;
											} else {
												$new_buyback_price = 0;
											}
											
											$buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . " 
																		SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "' 
																		WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
											tep_db_query($buyback_info_update_sql);
											
											//Always get the max mature period among all the products
											$this_prod_mature_period = tep_get_products_payment_mature_period($buyback_product_row['products_id'], $user_id);
											$cron_pending_credit_mature_period = max($this_prod_mature_period, $cron_pending_credit_mature_period);
										}
	          						}
	          						
									/*************************************************************************************
										Preparing data for scheduled cron job
										This step applies to supplier buyback (user type=1) AND customer buyback(user type=0)
									*************************************************************************************/
									$cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date 
																	FROM " . TABLE_CRON_PENDING_CREDIT . " 
																	WHERE cron_pending_credit_trans_type = 'B' 
																		AND cron_pending_credit_trans_id = '".tep_db_input($oid)."'";
									$cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);
									
									if (!tep_db_num_rows($cron_job_verify_result_sql)) {
										$cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => 'B',
																				'cron_pending_credit_trans_id' => tep_db_input($oid),
																				'cron_pending_credit_trans_created_date' => $row['buyback_request_group_date'],
																				'cron_pending_credit_trans_completed_date' => 'now()',
																				'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
																				'cron_pending_credit_trans_status' => $to_status
																			   );
										tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
									}
									// End of preparing data for scheduled cron job
									
									break;
							}
							break;
						case 3: //completed
							//do nothing. If allow 'completed' to something else, adjust cron_pending accordingly.
							break;
						case 4: //cancel
							switch ($to_status) {
								case 1:
									$buyback_request_info_select_sql = "	SELECT brg.buyback_request_group_site_id, br.buyback_quantity_confirmed 
																			FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																			INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																				ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
																			WHERE brg.buyback_request_group_id = '" . (int)$oid . "'";
									$buyback_request_info_result_sql = tep_db_query($buyback_request_info_select_sql);
									$buyback_request_info_row = tep_db_fetch_array($buyback_request_info_result_sql);
									
									if ((int)$buyback_request_info_row['buyback_quantity_confirmed'] < 1) {
										$buyback_request_group_expiry_date_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET buyback_request_group_expiry_date = DATE_ADD(NOW(), INTERVAL 10 MINUTE) WHERE buyback_request_group_id = '" . (int)$oid . "'";
										tep_db_query($buyback_request_group_expiry_date_update_sql);
									} else {
										$status_update = false;
									}
									
									break;
								case 2: //processing
									$buyback_request_info_select_sql = "	SELECT brg.buyback_request_group_site_id, br.buyback_quantity_confirmed 
																			FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																			INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																				ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
																			WHERE brg.buyback_request_group_id = '" . (int)$oid . "'";
									$buyback_request_info_result_sql = tep_db_query($buyback_request_info_select_sql);
									$buyback_request_info_row = tep_db_fetch_array($buyback_request_info_result_sql);
									
									if ((int)$buyback_request_info_row['buyback_quantity_confirmed'] > 0) {
										$buyback_request_group_expiry_date_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET buyback_request_group_expiry_date = DATE_ADD(NOW(), INTERVAL 10 MINUTE) WHERE buyback_request_group_id = '" . (int)$oid . "'";
										tep_db_query($buyback_request_group_expiry_date_update_sql);
									} else {
										$status_update = false;
									}
									
									break;
								case 3: //to complete
	          						$cron_pending_credit_mature_period = 0;
									
									// Updating the product average buyback price
									$buyback_product_select_sql = "	SELECT products_id, buyback_quantity_received, buyback_request_quantity, buyback_unit_price, buyback_quantity_confirmed
																	FROM " . TABLE_BUYBACK_REQUEST . "
																	WHERE buyback_request_group_id = '".tep_db_input($oid)."'";
	          						$buyback_product_result_sql = tep_db_query($buyback_product_select_sql);
	          						
	          						while ($buyback_product_row = tep_db_fetch_array($buyback_product_result_sql)) {
	          							$current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
	          							$current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
										if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
											$prev_buyback_qty = (int)$current_buyback_info_row["products_buyback_quantity"];
											$prev_buyback_price = (double)$current_buyback_info_row["products_buyback_price"];
											
											$order_buyback_qty = (int)$buyback_product_row['buyback_quantity_received'];
											// Already take into account the $order_buyback_qty
											
											$unit_price = $buyback_product_row['buyback_unit_price'];
											/*
											if ($row['buyback_request_group_site_id'] == 0) {	// For website buyback, assume confirm qty same as request qty
												$buyback_product_row['buyback_quantity_confirmed'] = $buyback_product_row['buyback_request_quantity'];
											}*/
											$received_quantity = (int)$buyback_product_row['buyback_quantity_received'];
											$total_buyback_price = $unit_price * ($received_quantity > $buyback_product_row['buyback_quantity_confirmed'] ? $buyback_product_row['buyback_quantity_confirmed'] : $received_quantity);
											
											$new_buyback_qty = $prev_buyback_qty + $order_buyback_qty;
											
											if ($new_buyback_qty > 0) {
												$new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) + ($total_buyback_price) ) / $new_buyback_qty;
											} else {
												$new_buyback_price = 0;
											}
											
											$buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . " 
																		SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "' 
																		WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
											tep_db_query($buyback_info_update_sql);
											
											//Always get the max mature period among all the products
		          							$this_prod_mature_period = tep_get_products_payment_mature_period($buyback_product_row['products_id'], $user_id);
											$cron_pending_credit_mature_period = max($this_prod_mature_period, $cron_pending_credit_mature_period);
										}
	          						}
	          						
									/*************************************************************************************
										Preparing data for scheduled cron job
										This step applies to supplier buyback (user type=1) AND customer buyback(user type=0)
									*************************************************************************************/
									$cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date 
																	FROM " . TABLE_CRON_PENDING_CREDIT . " 
																	WHERE cron_pending_credit_trans_type = 'B' 
																		AND cron_pending_credit_trans_id = '".tep_db_input($oid)."'";
									$cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);
									
									if (!tep_db_num_rows($cron_job_verify_result_sql)) {
										$cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => 'B',
																				'cron_pending_credit_trans_id' => tep_db_input($oid),
																				'cron_pending_credit_trans_created_date' => $row['buyback_request_group_date'],
																				'cron_pending_credit_trans_completed_date' => 'now()',
																				'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
																				'cron_pending_credit_trans_status' => $to_status
																			   );
										tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
									}
									// End of preparing data for scheduled cron job
									
									break;
							}
							break;
					}
					
					// Status update notification
					tep_status_update_notification('B', $buyback_request_group_id, $login_email_address, $from_status, $to_status, 'M', $admin_comment);
					
					// Update the buyback's tag since buyback status is changed
					tep_update_record_tags(FILENAME_BUYBACK_REQUESTS, (int)$oid, (int)$to_status, '');
				}
				
				//update the status history
				if ($set_as_buyback_remarks == 1) {
					$buyback_status_history_update_arr = array('set_as_buyback_remarks' => '0');
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_status_history_update_arr, 'update', "buyback_request_group_id= '" . (int)$oid . "'");
				}
				
				//update the request group
				if ($to_status > 0 && $status_update == true) {
					if ($to_status == 4) {
						// Skipped For Extra Inventory
						if (tep_not_null($row['orders_products_id']) && $row['orders_products_id'] > 0) {
							$get_custom_product_id_sql = "	SELECT custom_products_type_id 
						    								FROM " . TABLE_ORDERS_PRODUCTS . " 
						    								WHERE orders_products_id = '".tep_db_input($row['orders_products_id'])."'";
					    	$get_custom_product_id_result = tep_db_query($get_custom_product_id_sql);
					    	if($get_custom_product_id_row = tep_db_fetch_array($get_custom_product_id_result)) {
					    		// If HLA product
					    		if($get_custom_product_id_row['custom_products_type_id'] == 4) {
					    			$products_hla_info = tep_draw_products_extra_info($row['orders_products_id'], 'products_hla');
					    			$products_hla_info = tep_array_unserialize($products_hla_info['products_hla']);
					    			if ($products_hla_info['products_hla_id'] > 0) {
					    				$update_hla = "	UPDATE " . TABLE_PRODUCTS_HLA . "
														SET actual_quantity = actual_quantity + 1 
														WHERE products_hla_id = '" . tep_db_input($products_hla_info['products_hla_id']) . "'";
										tep_db_query($update_hla);
					    			}
					    		}
					    	}
							
							$comment = '##BO##'.(int)$oid.'## - BO CANCELED by '.$login_email_address;
						
					    	$comment_array = array(	'orders_id' => $row['orders_id'],
													'orders_status_id' => '0',
													'date_added' => 'now()',
													'customer_notified' => '0',
													'comments' => $comment,
													'comments_type' => '0',
													'set_as_order_remarks' => '0',
													'changed_by' => $login_email_address
													);
							tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
							
							$get_selected_lang_id_sql = "	SELECT ci.customer_info_selected_language_id 
															FROM ".TABLE_ORDERS." AS o 
															INNER JOIN ".TABLE_CUSTOMERS_INFO." AS ci 
																ON (o.customers_id = ci.customers_info_id) 
															WHERE o.orders_id = '".(int)$row['orders_id']."'";
							$get_selected_lang_id_result =	tep_db_query($get_selected_lang_id_sql);
							
							if ($get_selected_lang_id_row = tep_db_fetch_array($get_selected_lang_id_result)) {
								// Ensure buyback_request_group_id only can have one record in orders_products_history TABLE
								tep_db_query("DELETE FROM ".TABLE_ORDERS_PRODUCTS_HISTORY." WHERE buyback_request_group_id = '".(int)$buyback_request_group_id."' AND received IS NOT NULL");
								
								include_once(DIR_WS_LANGUAGES . 'buyback_system.php');
						
								if ($get_selected_lang_id_row['customer_info_selected_language_id'] == 2) {
									$comments_reason_to_canceled = COMMENTS_REASON_TO_CANCELED_CHINESE;
								} else {
									$comments_reason_to_canceled = COMMENTS_REASON_TO_CANCELED_ENGLISH;
								}
								$update_history_data_sql['dispute_comment'] = $comments_reason_to_canceled;
								$update_history_data_sql['changed_by'] = $login_email_address;
								$update_history_data_sql['received'] = '0';
								$update_history_data_sql['last_updated'] = 'now()';
								tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' buyback_request_group_id = "'.(int)$buyback_request_group_id.'" AND received IS NULL');	
							}
						}
                        
                        # remove new order notification
                        tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . (int) $buyback_request_group_id . "' AND orders_type = 'BO' AND site_id = 0");
					}
					
					$buyback_request_group_update_arr = array('buyback_status_id' => $to_status);
					tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_group_update_arr, 'update', "buyback_request_group_id= '" . (int)$oid . "'");
				}
				
				//update buyback history / comments history
				$buyback_history_data_array = array('buyback_request_group_id' => $oid,
												   	'buyback_status_id' => (int)$to_status,
												   	'date_added' => 'now()',
												   	'customer_notified' => $user_notify,
												   	'comments' => $admin_comment,
												   	'set_as_buyback_remarks' => $set_as_buyback_remarks,
												   	'changed_by' => $login_email_address
													);
				tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
				
				// default message
				if ($to_status == 3) {
					$custom_products_type_id = '';
					$buyback_status_history_update_arr = array('set_as_buyback_remarks' => '0');
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_status_history_update_arr, 'update', "buyback_request_group_id= '" . (int)$oid . "'");
					
					include_once(DIR_WS_LANGUAGES . 'buyback_system.php');
					
					if (tep_not_null($row['orders_products_id']) && $row['orders_products_id'] > 0) {
						$get_custom_product_id_sql = "	SELECT custom_products_type_id 
					    								FROM " . TABLE_ORDERS_PRODUCTS . " 
					    								WHERE orders_products_id = '".tep_db_input($row['orders_products_id'])."'";
				    	$get_custom_product_id_result = tep_db_query($get_custom_product_id_sql);
				    	if($get_custom_product_id_row = tep_db_fetch_array($get_custom_product_id_result)) {
				    		$custom_products_type_id = $get_custom_product_id_row['custom_products_type_id'];
				    	}
				    }
			    	
					if ($row['buyback_request_group_site_id'] == 1 || $row['customer_info_selected_language_id'] == 2) {
						if ($custom_products_type_id == 4) {
							$mature_period_day = (int)(($cron_pending_credit_mature_period / 60) / 24);
							$comments_processing_to_completed = sprintf(HLA_COMMENTS_PROCESSING_TO_COMPLETED_CHINESE, $mature_period_day);
						} else {
							$comments_processing_to_completed = COMMENTS_PROCESSING_TO_COMPLETED_CHINESE;
						}
					} else {
						if ($custom_products_type_id == 4) {
							$mature_period_day = (int)(($cron_pending_credit_mature_period / 60) / 24);
							$comments_processing_to_completed = sprintf(HLA_COMMENTS_PROCESSING_TO_COMPLETED_ENGLISH, $mature_period_day);
						} else {
							$comments_processing_to_completed = COMMENTS_PROCESSING_TO_COMPLETED_ENGLISH;
						}
					}
					
					$buyback_history_data_array = array('buyback_request_group_id' => $oid,
													   	'buyback_status_id' => 0,
													   	'date_added' => 'now()',
													   	'customer_notified' => 1,
													   	'comments' => $comments_processing_to_completed,
													   	'set_as_buyback_remarks' => 1,
													   	'changed_by' => 'system'
														);
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
				}
				
				$messageStack->add_session(TEXT_MSG_SUCCESS,'success');
				
			} //end valid buyback req grp id
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			
			break;
		case 'verify_buyback_order':
			$buyback_request_group_id = (int)$_REQUEST["buyback_request_group_id"];
			
			$buyback_orders_verify_update_sql_data = array(	'buyback_request_group_verify_mode' => (int)$_REQUEST["v_mode"],
		  					 								'buyback_request_group_last_modified' => 'now()'
		  					 								);
  			tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_orders_verify_update_sql_data, 'update', "buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' AND buyback_status_id=3");
  			
  			$buyback_order_list_history_data_array = array(	'buyback_request_group_id' => $buyback_request_group_id,
									                        'buyback_status_id' => 0,
									                        'date_added' => 'now()',
									                        'customer_notified' => 0,
									                        'comments' => ((int)$_REQUEST["v_mode"] == '1' ? 'Mark order as verified' : 'Mark order as unverify'),
									                        'changed_by' => $login_email_address
									                       );
			tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_list_history_data_array);
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action', 'v_mode'))));
			
			break;
	    case 'ShowSubmittedRestockCharacter':
			//Show restock character
			if (!$hide_or_show_rstk_char_permission) {
				$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
				tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			}
			
			$buyback_request_group_id = isset($_REQUEST['buyback_request_group_id']) ? $_REQUEST['buyback_request_group_id'] : '';
			
			// Checking the Pending Qty >= SUM() BO request qty.
			$get_pending_qty_select_sql = "	SELECT br.orders_id, br.orders_products_id, op.products_quantity, op.products_delivered_quantity 
											FROM " . TABLE_BUYBACK_REQUEST . " AS br 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (br.orders_products_id = op.orders_products_id) 
											WHERE br.buyback_request_group_id = '".(int)$buyback_request_group_id."'";
			$get_pending_qty_result_sql = tep_db_query($get_pending_qty_select_sql);
			if ($get_pending_qty_row = tep_db_fetch_array($get_pending_qty_result_sql)) {
				$orders_products_id = $get_pending_qty_row['orders_products_id'];
				$order_pending_qty = $get_pending_qty_row['products_quantity'] - $get_pending_qty_row['products_delivered_quantity'];
				
				$sum_bo_qty_select_sql = "	SELECT SUM(br.buyback_request_quantity) AS sum_bo_qty 
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
											WHERE br.orders_products_id = '".(int)$get_pending_qty_row['orders_products_id']."' 
											AND brg.buyback_status_id = '1'";
				$sum_bo_qty_result_sql = tep_db_query($sum_bo_qty_select_sql);
				if ($sum_bo_qty_row = tep_db_fetch_array($sum_bo_qty_result_sql)) {
					if ($order_pending_qty >= $sum_bo_qty_row['sum_bo_qty']) {
						;
					} else {
						$messageStack->add_session(sprintf(ERROR_BUYBACK_QTY_OVER, $get_pending_qty_row['orders_id']), 'error');
						tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
					}
				}
			}
			
			$show_restock_mode_select_sql = "	SELECT brg.show_restock, brg.buyback_request_group_site_id, brg.buyback_request_group_served, br.buyback_dealing_type, l.locking_by 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												INNER JOIN " . TABLE_BUYBACK_REQUEST ." AS br
													ON (br.buyback_request_group_id = brg.buyback_request_group_id)
												LEFT JOIN " . TABLE_LOCKING . " AS l 
    												ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
												WHERE brg.buyback_request_group_id = '" . (int)$buyback_request_group_id . "'";
			$show_restock_mode_result_sql = tep_db_query($show_restock_mode_select_sql);
			if ($show_restock_mode_row = tep_db_fetch_array($show_restock_mode_result_sql)) {
				if ($show_restock_mode_row['locking_by'] == $login_id) {
					if ($show_restock_mode_row['show_restock'] != '1') {
						$show_restock_n_expirydate_update_sql = "	UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " 
									    							SET show_restock = '1', 
									    							buyback_request_group_expiry_date = DATE_ADD(NOW(), INTERVAL 40 MINUTE) 
									    							WHERE buyback_request_group_id = '" . (int)$buyback_request_group_id . "' ";
						$success = tep_db_query($show_restock_n_expirydate_update_sql);
						
						if ($success) {
							$buyback_history_data_array = array('buyback_request_group_id' => (int)$buyback_request_group_id,
										                        'buyback_status_id' => '0',
																'date_added' => 'now()',
																'customer_notified' => '0',
																'comments' => 'Show Restock Character',
																'changed_by' => $login_email_address
																);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
							
							// Reset all comment become non-remark
							$remark_sql_data_array = array('set_as_buyback_remarks' => 0);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $remark_sql_data_array, 'update', "buyback_request_group_id = '" . (int)$buyback_request_group_id . "'");
							
							// serve customer
							if ((int)$show_restock_mode_row['buyback_request_group_served'] == 0 && ($show_restock_mode_row['buyback_dealing_type'] == 'vip_deal_on_game' || $show_restock_mode_row['buyback_dealing_type'] == 'ofp_deal_on_game' || $show_restock_mode_row['buyback_dealing_type'] == 'ofp_deal_on_open_store')) {
								$expiry_date_update_sql = "	UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " 
							    							SET buyback_request_group_served = '1' 
							    							WHERE buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' ";
								tep_db_query($expiry_date_update_sql);
							}
							
							// Uncomment this part due to change of flow - From KIM
							/*
							$predefined_order_comment = $orders_comments_text = '';
							if ((int)$show_restock_mode_row['buyback_request_group_site_id'] > 0) {
								include_once(DIR_WS_LANGUAGES . 'buyback_chinese.php');
								$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																		FROM " . TABLE_ORDERS_COMMENTS . "
																		WHERE orders_comments_id = '87'";
								$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
								
								if ($predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql)) {
									$predefined_order_comment = $predefined_order_comment_row['orders_comments_text'];
								}
							} else {
								include_once(DIR_WS_LANGUAGES . 'buyback_english.php');
								$predefined_order_comment = TEXT_CHAR_READY;
							}

							if ($show_restock_mode_row['buyback_dealing_type'] == 'vip_deal_on_game' || $show_restock_mode_row['buyback_dealing_type'] == 'ofp_deal_on_game') {
								$orders_comments_text = sprintf(TEXT_BUYBACK_LANG_TRADE_PIN, tep_rand(0, 9, 4));
								$orders_comments_text .= "\n" . $predefined_order_comment;
							} else {
								$orders_comments_text = $predefined_order_comment;
							}
							
							$buyback_order_comment_data_array = array(	'buyback_request_group_id' => $buyback_request_group_id,
												                        'buyback_status_id' => '0',
																		'date_added' => 'now()',
																		'customer_notified' => '1',
																		'comments' => $orders_comments_text,
																		'set_as_buyback_remarks' => '1',
																		'changed_by' => $login_email_address
																	);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
							
							if ($show_restock_mode_row['buyback_dealing_type'] == 'vip_deal_on_game' || $show_restock_mode_row['buyback_dealing_type'] == 'ofp_deal_on_game') {
								$confirm_pin_str = '';
								for ($pin_cnt=0; $pin_cnt < 6; $pin_cnt++) {
									$confirm_pin_str .= chr(tep_rand(65, 90));
								}
								
								$orders_comments_text = sprintf(TEXT_BUYBACK_LANG_CONFIRM_TRADE_PIN, $confirm_pin_str);
					
								$buyback_order_comment_data_array = array(	'buyback_request_group_id' => $buyback_request_group_id,
													                        'buyback_status_id' => '0',
																			'date_added' => 'now()',
																			'customer_notified' => '0',
																			'comments' => $orders_comments_text,
																			'set_as_buyback_remarks' => '0',
																			'changed_by' => $login_email_address
																		);
								tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
							}
							*/
							$messageStack->add_session(sprintf(SUCCESS_SHOW_BUYBACK_RESTOCK_CHARACTER, (int)$_REQUEST['buyback_request_group_id']), 'success');
						} else {
							$messageStack->add_session(sprintf(ERROR_SHOW_BUYBACK_RESTOCK_CHARACTER, (int)$_REQUEST['buyback_request_group_id']), 'error');
						}
					} else {
						$messageStack->add_session(sprintf(WARNING_RESTOCK_CHARACTER_ALREADY_SHOWN, (int)$_REQUEST['buyback_request_group_id']), 'warning');
					}
				} else {
					$messageStack->add_session(ERROR_ORDER_NOT_LOCK, 'error');
				}
			} else {
				$messageStack->add_session(sprintf(ERROR_BUYBACK_ORDER_NOT_EXISTS, (int)$_REQUEST['buyback_request_group_id']), 'error');
			}
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			
			break;
		case 'HideAllRestockCharacter':
			//Hide restock character
			if (!$hide_or_show_rstk_char_permission) {
				$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
				tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			}
			
			$character_visibility_arr = array('show_restock' => '0');
			
			$show_restock_mode_select_sql = "	SELECT brg.show_restock, brg.buyback_request_group_site_id, l.locking_by 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												LEFT JOIN " . TABLE_LOCKING . " AS l 
    												ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
												WHERE brg.buyback_request_group_id = '" . (int)$_REQUEST['buyback_request_group_id'] . "'";
			$show_restock_mode_result_sql = tep_db_query($show_restock_mode_select_sql);
			if ($show_restock_mode_row = tep_db_fetch_array($show_restock_mode_result_sql)) {
				if ($show_restock_mode_row['locking_by'] == $login_id) {
					if ($show_restock_mode_row['show_restock'] != '0') {
						$success = tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $character_visibility_arr, 'update', "buyback_request_group_id = '". (int)$_REQUEST['buyback_request_group_id'] ."'");
						
						if ($success) {
							$buyback_history_data_array = array('buyback_request_group_id' => (int)$_REQUEST['buyback_request_group_id'],
										                        'buyback_status_id' => '0',
																'date_added' => 'now()',
																'customer_notified' => '0',
																'comments' => 'Hide Restock Character',
																'changed_by' => $login_email_address
																);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
							
							// Reset all comment become non-remark
							$remark_sql_data_array = array('set_as_buyback_remarks' => 0);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $remark_sql_data_array, 'update', "buyback_request_group_id = '" . (int)$_REQUEST['buyback_request_group_id'] . "'");
							
							if ((int)$show_restock_mode_row['buyback_request_group_site_id'] > 0) {
								$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																		FROM " . TABLE_ORDERS_COMMENTS . "
																		WHERE orders_comments_id = '121'";
								$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
								
								if ($predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql)) {
									$orders_comments_text = $predefined_order_comment_row['orders_comments_text'];
									$user_notify = '1';
									$is_remarks = '1';
									
									$buyback_order_comment_data_array = array(	'buyback_request_group_id' => (int)$_REQUEST['buyback_request_group_id'],
														                        'buyback_status_id' => '0',
																				'date_added' => 'now()',
																				'customer_notified' => $user_notify,
																				'comments' => $orders_comments_text,
																				'set_as_buyback_remarks' => $is_remarks,
																				'changed_by' => $login_email_address
																			);
									tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
								}
							} else {
								$orders_comments_text = TEXT_CHAR_DISCONNECTED;
								$user_notify = '1';
								$is_remarks = '1';
								
								$buyback_order_comment_data_array = array(	'buyback_request_group_id' => (int)$_REQUEST['buyback_request_group_id'],
													                        'buyback_status_id' => '0',
																			'date_added' => 'now()',
																			'customer_notified' => $user_notify,
																			'comments' => $orders_comments_text,
																			'set_as_buyback_remarks' => $is_remarks,
																			'changed_by' => $login_email_address
																		);
								tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_order_comment_data_array);
							}
							
							$messageStack->add_session(sprintf(SUCCESS_HIDE_BUYBACK_RESTOCK_CHARACTER, (int)$_REQUEST['buyback_request_group_id']), 'success');
						} else {
							$messageStack->add_session(sprintf(ERROR_HIDE_BUYBACK_RESTOCK_CHARACTER, (int)$_REQUEST['buyback_request_group_id']), 'error');
						}
					} else {
						$messageStack->add_session(sprintf(WARNING_RESTOCK_CHARACTER_ALREADY_HIDE, (int)$_REQUEST['buyback_request_group_id']), 'warning');
					}
				} else {
					$messageStack->add_session(ERROR_ORDER_NOT_LOCK, 'error');
				}
			} else {
				$messageStack->add_session(sprintf(ERROR_BUYBACK_ORDER_NOT_EXISTS, (int)$_REQUEST['buyback_request_group_id']), 'error');
			}
	        tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
		    
		    break;
		case 'insert_restock_character':
			$locking_by_select_sql = "	SELECT locking_by 
										FROM " . TABLE_LOCKING . " 
										WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' 
											AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
			$locking_by_result_sql = tep_db_query($locking_by_select_sql);
			if ($locking_by_row = tep_db_fetch_array($locking_by_result_sql)) {
				if ($locking_by_row['locking_by'] != $login_id) {
					tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
				}
			} else {
				tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			}
			
			$update_rstk_char = false;
			
			if (isset($HTTP_POST_VARS['restock_char']) && is_array($HTTP_POST_VARS['restock_char'])) {
				foreach($HTTP_POST_VARS['restock_char'] as $buyback_request_id => $restock_character) {
					$buyback_status_id_select_sql = "	SELECT br.restock_character, brg.buyback_status_id 
														FROM " . TABLE_BUYBACK_REQUEST . " AS br 
														INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
															ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
														WHERE buyback_request_id = '" . (int)$buyback_request_id. "'";
					$buyback_status_id_result_sql = tep_db_query($buyback_status_id_select_sql);
					if ($buyback_status_id_row = tep_db_fetch_array($buyback_status_id_result_sql)) {
						if ($buyback_status_id_row['buyback_status_id'] == 1 && !tep_not_null($buyback_status_id_row['restock_character'])) {
							$rstk_char_data_array = array('restock_character' => $restock_character);
							
							tep_db_perform(TABLE_BUYBACK_REQUEST, $rstk_char_data_array, 'update', "buyback_request_id = '" . tep_db_input($buyback_request_id) . "'");
							
							$update_rstk_char = true;
						}
						
						if ($update_rstk_char) {
							$buyback_history_data_array = array('buyback_request_group_id' => (int)$_REQUEST['buyback_request_group_id'],
										                        'buyback_status_id' => '0',
																'date_added' => 'now()',
																'customer_notified' => '0',
																'comments' => 'Manually assign restock character',
																'changed_by' => $login_email_address
																);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
							
							$messageStack->add_session(SUCCESS_RESTOCK_CHARACTER_UPDATE, 'success');
						}
					}
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action'))));
			break;
	}
}

$user_flags_array = tep_get_user_flags();
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=tep_not_null($buyback_request_group_id) ? 'BO-'.$buyback_request_group_id : TITLE ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/orders.js"></script>
	<script language="javascript" src="includes/javascript/buyback.js"></script>
	<script language="javascript">
	<!--
		var pageLoaded = false;
		function init() {
			// quit if this function has already been called
	       	if (arguments.callee.done) return;
			
	       	// flag this function so we don't do the same thing twice
	       	arguments.callee.done = true;
			
	       	initInfoCaptions();
	       	pageLoaded = true;	// Control when a javascript event in this page can be triggered
		};
		
	   	/* for Mozilla */
	   	if (document.addEventListener) {
	       	document.addEventListener("DOMContentLoaded", init, null);
	   	}
		
	   	/* for other browsers */
	   	window.onload = init;
	//-->
	</script>
	<style>
		div#lockingBtn { vertical-align: top; }
		div#navBtn { float: left; background-color: #fff; text-align:right; }
		div#navBtn ul { margin: 5px 0; padding: 0; border: 0; }
		div#navBtn ul li { list-style-type: none; width: 60px; padding: 0; margin: 2; display: block; float: left; background-color: #F3F4F8; font: 12px/20px  "Lucida Grande", Verdana, Arial, sans-serif; text-align: center; }
		div#navBtn ul li a	{ color: #333; text-decoration: none; display: block; padding: 2; margin: 10; border: 1px solid #A8B090; }
		div#navBtn ul a:hover { background-color: #F5B252 }
		
		div#navBtn ul li#unlock_btn a	{ color: #333; text-decoration: none; font-weight: bold; display: block; padding: 2; margin: 0; border: 1px solid #A8B090; background-color: #5CE400; }
		div#navBtn ul li#unlock_btn a:hover { color: #FFF; background-color: #878F78 }
		
		div#navBtn ul li#lock_btn a	{ color: #333; text-decoration: none; font-weight: bold; display: block; padding: 2; margin: 0; border: 1px solid #A8B090; background-color: #EA0101; }
		div#navBtn ul li#lock_btn a:hover { color: #FFF; background-color: #878F78 }
	</style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<div id="dhtmlTooltip"></div>
<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
<!-- header_eof //-->
	<? 	if (tep_not_null($buyback_request_group_id))	echo '<div id="floatingTopCenterMsg">'.HEADING_TITLE_SEARCH.'&nbsp;&nbsp;<b>'.$buyback_request_group_id.'</b></div>'; ?>
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
									<td class="pageHeading"><?=HEADING_TITLE_BUYBACK_ORDERS?></td>
									<td class="smallText" align="right" valign="top">&nbsp;
									<?
										echo tep_draw_form('buyback_order_form', FILENAME_BUYBACK_REQUESTS_INFO, '', 'post');
										echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('buyback_request_group_id', $buyback_request_group_id, 'size="12"') . tep_draw_hidden_field('action', 'change_order');
										echo "</form>";
									?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
<?
if ($buyback_order_exists) {
	$form_action_str = 'partial_receive';
    $prepend_string = "customers_";	// All from customer table
    $allow_update = true;
    $editable_view = false;
    $custom_products_type_id = '';
    
    //order info - Expiry rule based on tep_cancel_expirable_buyback_request()
    $buyback_order_select_sql = "	SELECT * , ( ((TO_DAYS(buyback_request_group_expiry_date)*24*3600) + TIME_TO_SEC(buyback_request_group_expiry_date) ) - ( TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()) ) ) AS expiry_secs, l.locking_by 
    								FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
    								LEFT JOIN " . TABLE_CUSTOMERS . " AS c
    									ON brg.customers_id=c.customers_id 
    								LEFT JOIN " . TABLE_LOCKING . " AS l 
    									ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
    							    INNER JOIN ". TABLE_SITE_CODE ." AS sc
    							        ON (sc.site_id=brg.buyback_request_group_site_id) 
    								WHERE buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
    								    AND FIND_IN_SET(". $login_groups_id .", buyback_admin_groups_id)";
    $buyback_order_result_sql = tep_db_query($buyback_order_select_sql);
    
    if ($buyback_order_row = tep_db_fetch_array($buyback_order_result_sql)) {
        //update read mode
        tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_read_mode = 1 WHERE buyback_request_group_id = '" . $buyback_request_group_id . "'");
        
        $im_select_sql = "	SELECT imt.instant_message_type_name, ima.instant_message_userid, ima.instant_message_type_id, ima.instant_message_remarks  
							FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " AS ima 
							LEFT JOIN " . TABLE_INSTANT_MESSAGE_TYPE . " AS imt 
								ON (ima.instant_message_type_id = imt.instant_message_type_id) 
							WHERE ima.customer_id = '".(int)$buyback_order_row['customers_id']."'
								AND ima.instant_message_userid <> ''";
        								
    	$im_select_result_sql = tep_db_query($im_select_sql);
    	while ($im_select_row = tep_db_fetch_array($im_select_result_sql)) {
    		if ($im_select_row['instant_message_type_id'] != 0) {
    			$user_im .= '<b>'.$im_select_row['instant_message_type_name'].':</b> '.$im_select_row['instant_message_userid'].'<br/>';
    		} else {
    			$user_im .= '<b>'.$im_select_row['instant_message_remarks'].':</b> '.$im_select_row['instant_message_userid'].'<br/>';
    		}
    	}
    	
    	$date_ordered = $buyback_order_row['buyback_request_group_date'];
    	$status_id = $buyback_order_row['buyback_status_id'];
    	$buyback_request_group_served = $buyback_order_row['buyback_request_group_served'];
    	$status_name = $status_options[$buyback_order_row['buyback_status_id']];
    	$buyback_comment = trim($buyback_order_row['buyback_request_group_comment']);
    	$user_gender = (strtoupper($buyback_order_row[$prepend_string.'gender']) == 'M' ? "Male" : "Female");
    	$user_id = (int)$buyback_order_row['customers_id'];
    	$user_fax = $buyback_order_row[$prepend_string.'fax'];
    	$user_mobile = $buyback_order_row[$prepend_string.'mobile'];
//    	$user_qq = $buyback_order_row[$prepend_string.'qq'];
//    	$user_msn = $buyback_order_row[$prepend_string.'msn'];
//    	$user_yahoo = $buyback_order_row[$prepend_string.'yahoo'];
//    	$user_icq = $buyback_order_row[$prepend_string.'icq'];
    	$user_email = $buyback_order_row[$prepend_string.'email_address'];
    	$user_telephone = $buyback_order_row[$prepend_string.'telephone'];
    	$user_ip = $buyback_order_row['remote_addr'];
    	$user_dob = $buyback_order_row[$prepend_string.'dob'];
    	$order_currency = $buyback_order_row['currency'];
    	$order_currency_value = $buyback_order_row['currency_value'];
    	$buyback_from_site_id = (int)$buyback_order_row['buyback_request_group_site_id'];
    	$site_name = tep_get_site_name($buyback_from_site_id);
    	$expiry_mins = floor($buyback_order_row['expiry_secs']/60);
    	$the_orders_tag_ids = $buyback_order_row['orders_tag_ids'];
    	$currency = $buyback_order_row['currency'];
		$order_type = $buyback_order_row['buyback_request_order_type'];
    	$lock_admin_id = $buyback_order_row['locking_by'];
    	$buyback_request_group_expiry_date = $buyback_order_row['buyback_request_group_expiry_date'];
    	
    	if ($status_id == 1) {
    		if ($login_id != $lock_admin_id) {
    			$allow_update = false;
    		}
    	}
		
    	//user flag
    	$cust_name_style = '';
    	$flag_icon_html = '';
    	$customer_flags_array = explode(',', $buyback_order_row['customers_flag']);
    	
    	foreach ($user_flags_array as $flag_id => $flag_info) {
    		$flag_label = $flag_info['user_flags_name'];
    		$flag_icon_img_src = DIR_WS_IMAGES . (in_array($flag_id, $customer_flags_array) ? $flag_icon_array[$flag_info['user_flags_css_style']]['on'] : $flag_icon_array[$flag_info['user_flags_css_style']]['off']);
    		$flag_icon_html .= tep_image($flag_icon_img_src, $flag_label, 11, 11) . '&nbsp;';
    		
    		if (in_array($flag_id, $customer_flags_array))	$cust_name_style = $flag_info['user_flags_css_style'];
    	}
    	
    	$user_type = (int)$buyback_order_row['buyback_request_group_user_type'] > 0 ? TYPE_SUPPLIER : TYPE_CUSTOMER;
    	
    	if ($status_id == 1 || $status_id==PARTIAL_RECEIVE_STATUS || $status_id==CANCEL_STATUS) {
    		$received_qty_adjust_array = array( array('id'=>"+", 'text'=>"+"),
    											array('id'=>"-", 'text'=>"-")
    										);
    	}
    	
    	//phone info
    	//This row adds page load time by 6x
    	$customer_complete_phone_info_array = tep_format_telephone($user_id);
    	
    	//address
    	$customer_address_sql = "	SELECT c.customers_firstname as firstname, c.customers_lastname  as lastname,
    									c.customers_telephone as telephone, c.customers_email_address as email_address,
    									ab.entry_company as company, ab.entry_street_address as street_address, ab.entry_suburb as suburb,
    									ab.entry_postcode as postcode, ab.entry_city as city, ab.entry_zone_id as zone_id,
    									z.zone_name as state, co.countries_id, co.countries_name as country , co.countries_iso_code_2,
    									co.countries_iso_code_3, co.address_format_id as format_id, ab.entry_state, sg.vip_supplier_groups_name 
    								FROM " . TABLE_CUSTOMERS . " AS c
    									LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab ON (c.customers_id=ab.customers_id)
    									LEFT JOIN " . TABLE_ZONES . " AS z ON (ab.entry_zone_id = z.zone_id)
    									LEFT JOIN " . TABLE_COUNTRIES . " AS co ON (ab.entry_country_id = co.countries_id)
    									LEFT JOIN " . TABLE_CUSTOMERS_VIP . " AS cv ON (cv.customers_id=c.customers_id) 
    									LEFT JOIN " . TABLE_VIP_SUPPLIER_GROUPS . " AS sg ON (cv.vip_supplier_groups_id = sg.vip_supplier_groups_id) 
    								WHERE c.customers_id = '" . (int)$user_id . "'
    									AND c.customers_default_address_id = ab.address_book_id";
    	$customer_address_query = tep_db_query($customer_address_sql);
    	if ($row_address = tep_db_fetch_array($customer_address_query)) {
    		$supplier_groups_name = tep_not_null($row_address['vip_supplier_groups_name']) ? $row_address['vip_supplier_groups_name'] : 'Member';
    		$user_address = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $user_id . '&action=edit') . '" target="_blank">'. tep_output_string_protected($row_address['firstname']) . '&nbsp;' . tep_output_string_protected($row_address['lastname']) .'</a>&nbsp;[<b>'.$supplier_groups_name.'</b>] '. $flag_icon_html;
    		if ($view_supplier_status_permission) {
    			$user_address .= '<br>' . tep_address_format($row_address['format_id'], $row_address, 1, '', '<br>', '', false);
    		}
    	}
    	
    	//Start Products box---------------------
    	$payable_total = 0;
    	$js = "";
    	
    	$completely_received_regardless_permission = true;
    	$not_received_any = true;
    	$total_received = 0;
    	$productsHTML = '';
    	//trade mode array
		$trade_mode_array = array(TEXT_NORMAL_ORDERS, TEXT_TRADE_WITH_OFFGAMERS, TEXT_TRADE_WITH_CUSTOMERS);
		$delivery_array = array('', 'F2F', 'PIMA', 'MAIL', 'OPEN STORE');
    	
    	$buyback_order_product_select_sql = "	SELECT br.restock_character, br.orders_products_id, p.products_id AS live_pid, p.products_actual_quantity, pd.products_location, pd.products_name, pc.categories_id, br.* 
    											FROM "  . TABLE_BUYBACK_REQUEST . " AS br 
    											LEFT JOIN " . TABLE_PRODUCTS . " AS p 
    												ON br.products_id=p.products_id 
    											LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
    												ON p.products_id=pd.products_id 
    											LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
    												ON pd.products_id=pc.products_id 
    											WHERE br.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
    												AND pc.products_is_link=0 
    												AND pd.language_id = '" . (int)$languages_id . "' 
    											ORDER BY pc.products_id";
    	$buyback_order_product_result_sql = tep_db_query($buyback_order_product_select_sql);
    	
    	$row_count = 0;
    	while ($buyback_order_product_row = tep_db_fetch_array($buyback_order_product_result_sql)) {
    		$delivery_mode_select_sql = "	SELECT opei.orders_products_extra_info_value 
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
											LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei 
												ON (op.parent_orders_products_id = opei.orders_products_id) 
											WHERE op.orders_products_id = '".(int)$buyback_order_product_row['orders_products_id']."' 
											AND opei.orders_products_extra_info_key = 'delivery_mode'";
	    	$delivery_mode_result_sql = tep_db_query($delivery_mode_select_sql);
	    	if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result_sql)) {
	    		$delivery_mode = $delivery_mode_row['orders_products_extra_info_value'];
	    	}
	    	
	    	$get_custom_product_id_sql = "	SELECT custom_products_type_id 
		    								FROM " . TABLE_ORDERS_PRODUCTS . " 
		    								WHERE orders_products_id = '".tep_db_input($buyback_order_product_row['orders_products_id'])."'";
	    	$get_custom_product_id_result = tep_db_query($get_custom_product_id_sql);
	    	if($get_custom_product_id_row = tep_db_fetch_array($get_custom_product_id_result)) {
	    		$custom_products_type_id = $get_custom_product_id_row['custom_products_type_id'];
	    	}
	    	
    	    $is_provision_confirmed_qty = false;
    		$row_style = ($row_count % 2) ? "ordersListingEven" : "ordersListingOdd";
    		
    		$buyback_quantity_confirmed = $buyback_order_product_row['buyback_quantity_confirmed'];
    		$buyback_quantity_received = $buyback_order_product_row['buyback_quantity_received'];
    		//VIP orders
    		$trade_mode = 0;
    		if($order_type == 1){
	    		if($buyback_order_product_row['buyback_dealing_type'] == 'ofp_deal_with_customers'){
	    			$trade_mode = 2;
	    		} else {
	    			$trade_mode = 1;
	    		}
    		}
    		$orders_id = $buyback_order_product_row['orders_id'];
    		$supplier_code = $buyback_order_product_row['buyback_request_supplier_code'];
    		$customers_ori_code = $buyback_order_product_row['buyback_request_customer_org_code'];
    		$customers_matching_code = $buyback_order_product_row['buyback_request_customer_matching_code'];
    		$screen_shot_before_trade = $buyback_order_product_row['buyback_request_screenshot_before_name'];
    		$screen_shot_after_trade = $buyback_order_product_row['buyback_request_screenshot_after_name'];
    		
    		if (trim($buyback_order_product_row['products_location']) == '')	$buyback_order_product_row['products_location'] = '---';
    		
    		$total_received += (int)$buyback_order_product_row['buyback_quantity_received'];
    		$unit_price = $buyback_order_product_row['buyback_unit_price'];
    		$buyback_dealing_type = $buyback_order_product_row['buyback_dealing_type'];
    		
    		if ($buyback_order_product_row['buyback_quantity_confirmed'] == 0) {
    			$is_provision_confirmed_qty = true;
			    if ($order_info_row['buyback_request_group_site_id'] == 0) {
			    	$buyback_order_product_row['buyback_quantity_confirmed'] = $buyback_order_product_row['buyback_request_quantity'];
			    } else {
			    	$buyback_order_product_row['buyback_quantity_confirmed'] = $buyback_order_product_row['buyback_request_quantity'] * 1.1;
			    }
			}
			
    		$received_quantity = (int)$buyback_order_product_row['buyback_quantity_received'];
    		
    		$actual_amount = $unit_price * ($received_quantity > $buyback_order_product_row['buyback_quantity_confirmed'] ? $buyback_order_product_row['buyback_quantity_confirmed'] : $received_quantity);
        	
    		$payable_total += $actual_amount;
    		$balance = $buyback_order_product_row['buyback_quantity_confirmed'] - $received_quantity;
    		
    		if ($received_quantity > 0)	$not_received_any = false;
    		if ($balance < 0)	$balance = 0;	// For extra receive case
    		
    		$full_cat_permissions = (tep_check_cat_tree_permissions(FILENAME_BUYBACK_REQUESTS, tep_get_actual_product_cat_id($buyback_order_product_row["products_id"])) == 1) ? true : false;
    		
    		if ($balance > 0)	$completely_received_regardless_permission = false;
    		
    		$prod_maincatpath = ($buyback_order_product_row["live_pid"]) ? "<span class='categoryPath'>[" . tep_output_generated_category_path_sq($buyback_order_product_row['categories_id']) . "]</span>" : "**--This product is no longer existing in db--**";
    		
    		$productsHTML .= '	<tr class="'.$row_style.'" id="row_'.$row_count.'" onClick="showClicked(this,\''.$row_style.'\', \'\')" onMouseOver="showOverEffect(this, \'invoiceListingRowOver\', \'\')" onMouseOut="showOutEffect(this, \''.$row_style.'\', \'\')">
    						    	<td valign="top" class="ordersRecords"><a href="'.tep_href_link(FILENAME_CATEGORIES, 'cPath=' . tep_get_product_cPath($buyback_order_product_row['live_pid']) . '&pID='.$buyback_order_product_row["products_id"].'&action=new_product').'" target="_blank" title="Product ID: '.$buyback_order_product_row["products_id"].'">'.$prod_maincatpath.' > '.$buyback_order_product_row['products_name'].'</a></td>';
    		
    		if ($status_id == 1 || $status_id == PARTIAL_RECEIVE_STATUS) {
    		    $check_same_order_select_sql = "SELECT count(*) AS total 
    		                                    FROM ". TABLE_BUYBACK_REQUEST ." AS br
    		                                        INNER JOIN ". TABLE_BUYBACK_REQUEST_GROUP ." AS brg
    		                                        ON (br.buyback_request_group_id=brg.buyback_request_group_id)
    		                                    WHERE br.products_id = '". (int)$buyback_order_product_row["products_id"] ."'
    		                                    AND brg.buyback_status_id IN ('1', '2')";
    		    $check_same_order_result_sql = tep_db_query($check_same_order_select_sql);
    		    
    		    $check_same_order_row = tep_db_fetch_array($check_same_order_result_sql);
                
                //create link to buyback request list page
    		    $buyback_order_array = array('action' => 'show_report',
    		                                 'subaction' => 'buyback_order',
    		                                 'products_id' => (int)$buyback_order_product_row["products_id"],
    		                                 'cat_id' => '0',
    		                                 'include_subcategory' => '1',
    		                                 'order_status' => tep_array_serialize(array(1, 2)),
    		                                 'show_records' => 'DEFAULT'
    		                                );
    		    
    		    if (is_array($buyback_order_array)) {
    		        foreach($buyback_order_array AS $k => $v) {
    		            $link .= $k .'='. $v .'&';
    		        }
    		        $link = substr($link, -1) == '&' ? substr($link, 0, -1) : '';
    		    }
    		    
    		    $productsHTML .= '  <td align="center" valign="top" class="ordersRecords"><a href="'. tep_href_link(FILENAME_BUYBACK_REQUESTS, $link) .'" target="_blank">'. $check_same_order_row['total'] .'</a></td>';
    		}
    		
    		if ($status_id==PARTIAL_RECEIVE_STATUS) {
    			$productsHTML .= '<td valign="top" class="ordersRecords"><span style="color:blue;">'.($buyback_order_product_row['products_location'] && $trade_mode != 2 ? $buyback_order_product_row['products_location'] : TEXT_OPTION_NOT_APPLICABLE).'</span></td>';
    		}
    		
			//if show_character is true.
			$show_restock = $buyback_order_row['show_restock'];
			$restock_character = '';
			$show_restock_status = '';
			
			if ($status_id == 1 && $hide_or_show_rstk_char_permission) {
				if ($login_id == $lock_admin_id) {
				    if ((int)$buyback_order_row['show_restock'] == 1) {
						$show_restock_status = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif') . '&nbsp;<a href="javascript:confirmAction(0, \''. tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'action=HideAllRestockCharacter&buyback_request_group_id=' . $buyback_request_group_id) .'\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif'). '</a>';
				    } else {
						$show_restock_status = '<a href="javascript:confirmAction(1, \''. tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'action=ShowSubmittedRestockCharacter&buyback_request_group_id=' . $buyback_request_group_id) .'\')">'. tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif') . '</a>&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
				    }
				}
			} else {
    			$show_restock_status =  (int)$buyback_order_row['show_restock'] ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif') : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
    		}
			
			if ( ($status_id == 1 && $login_id == $lock_admin_id) || 
				$status_id != 1) {
				if ($view_rstk_char_permission) {
					if (tep_not_null($buyback_order_product_row['restock_character'])) {
						$restock_character = $buyback_order_product_row['restock_character'];
					} else {
						if ($buyback_order_product_row['orders_products_id'] > 0) {
							$restock_character = vip_order::tep_get_customer_trading_char($buyback_order_product_row['orders_products_id']);	
						}
//						if ($trade_mode > 0) { // VIP Orders
//							$restock_character = vip_order::tep_get_customer_trading_char($buyback_order_product_row['orders_products_id']);
//						} else {
//							$restock_character = tep_get_buyback_restock_char($buyback_order_product_row['products_id']);
//						}
					}
				}
			}
			
			$rstk_character_str = $restock_character . ' ' . $show_restock_status;
			
			if ($status_id == 1) {
				if ($login_id == $lock_admin_id) {
					if (!tep_not_null($restock_character)) {
						$form_action_str = 'insert_restock_character';
						$rstk_character_str = '<div id="insert_restock_char_div"><a href="javascript:;" onclick="insert_restock_char('.$buyback_order_product_row['buyback_request_id'].');">' . tep_image(DIR_WS_ICONS."add_item.gif", "", 15, 15) . '</a></div>' . tep_draw_hidden_field('restock_char['.$buyback_order_product_row['buyback_request_id'].']', '', 'id="restock_char['.$buyback_order_product_row['buyback_request_id'].']"');
					}
				} else {
					$rstk_character_str = '';
				}
			}
			
			//$productsHTML .= '<td valign="top" class="ordersRecords" nowrap><div id="char_div" class="' . ($login_id == $lock_admin_id ? 'show' : 'hide') . '">' . $rstk_character_str . '</div></td>';
			
			if ($custom_products_type_id != 4) {
				$productsHTML .= '<td valign="top" class="ordersRecords" nowrap><div id="char_div" class="show">' . $rstk_character_str . '</div></td>';
				$productsHTML .= '<td valign="top" class="ordersRecords" nowrap>' . $buyback_order_product_row['buyback_sender_character'] . '</td>';	
			}
    		
    		$productsHTML .= '	<td align="center" valign="top" class="ordersRecords">'.(int)$buyback_order_product_row['products_actual_quantity'].'</td>';
    		
    		if ($status_id != PARTIAL_RECEIVE_STATUS) $productsHTML .= '<td align="right" valign="top" class="ordersRecords" nowrap>'.$currencies->format($unit_price, true, $order_currency, $order_currency_value).'</td>';
    		
    		$productsHTML .= '	<td align="center" valign="top" class="ordersRecords">'.$buyback_order_product_row['buyback_request_quantity'].'</td>';
    		
        	$productsHTML .= '<td align="center" valign="top" class="ordersRecords" '. ($is_provision_confirmed_qty ? 'onMouseover="ddrivetip(\''.sprintf(TEXT_HELP_PROVISION_CONFIRMED_QTY, $buyback_order_product_row['buyback_request_quantity']).'\', \'\', 250);" onMouseout="hideddrivetip();"' : '') .'><span class="'.($is_provision_confirmed_qty ? 'redIndicator' : '').'">'.$buyback_order_product_row['buyback_quantity_confirmed'].'</span></td>';
    		
    		$productsHTML .= '	<td align="center" valign="top" class="ordersRecords">'.$buyback_quantity_received.'</td>';
    		
    		if ($status_id==PARTIAL_RECEIVE_STATUS || $status_id==CANCEL_STATUS) {
    			if (($trade_mode <= 2) || ($trade_mode == 2 && $customers_matching_code != $customers_ori_code && $customers_matching_code != 0)) {
	    			$productsHTML .= '	<td align="center" valign="top" class="ordersRecords">'.(int)$balance
	    									.tep_draw_hidden_field("balance[{$buyback_order_row['buyback_request_id']}]", (int)$balance, " id='balance_{$buyback_order_product_row['buyback_request_id']}' ")
	    									.tep_draw_hidden_field("products[{$buyback_order_row['buyback_request_id']}]", (int)$buyback_order_product_row['products_id']).'
	    								</td>';
	    			
	    			if ((int)$buyback_order_product_row['buyback_quantity_confirmed'] > 0 || ($status_id==CANCEL_STATUS && (int)$buyback_order_product_row['buyback_quantity_confirmed'] == 0)) {
	    				if ($full_cat_permissions) {
	    					$productsHTML .= '	<td align="center" valign="top" class="ordersRecords" nowrap>' . 
	    											tep_draw_pull_down_menu('partial_receive_sign['.$buyback_order_product_row["buyback_request_id"].']', $received_qty_adjust_array, '', 'id="partial_receive_sign_'.$buyback_order_product_row["buyback_request_id"].'"') . 
	    											tep_draw_input_field('partial_receive['.$buyback_order_product_row["buyback_request_id"].']', 0, ' size="5" id="partial_receive_'.$buyback_order_product_row['buyback_request_id'].'" onChange="checkBalance(this, '.(int)$received_quantity.', '.(int)$balance.')" onKeyPress="return noEnterKey(event)"') . '
	    										</td>
	    										<td align="center" valign="top" class="ordersRecords" nowrap>' .
	    											tep_draw_pull_down_menu('extra_receive_sign['.$buyback_order_product_row["buyback_request_id"].']', $received_qty_adjust_array, '', 'id="extra_receive_sign_'.$buyback_order_product_row["buyback_request_id"].'"') . 
	    											tep_draw_input_field('extra_receive['.$buyback_order_product_row["buyback_request_id"].']', 0, ' size="5" id="extra_receive_'.$buyback_order_product_row["buyback_request_id"].'" class="redInputBox" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"') . '
	    										</td>';
	    					$partial_inputs[$buyback_order_product_row["buyback_request_id"]] = array('received' => $buyback_order_product_row['buyback_quantity_received'], 'balance' => $balance);
	    				} else {
	    					$productsHTML .= tep_draw_input_field('no_access_field', TEXT_NO_ACCESS, ' size="10" DISABLED ');
	    				}
	    			} else {
	    				$productsHTML .= '---';
	    			}
	    			
	    			$productsHTML .= '	</td>';
	    		} else {
	    			$productsHTML .= '	<td align="right" valign="top" class="ordersRecords" nowrap>'.$currencies->format($actual_amount, true, $order_currency, $order_currency_value).'</td>';
	    		}
    		} else {
    			$productsHTML .= '	<td align="right" valign="top" class="ordersRecords" nowrap>'.$currencies->format($actual_amount, true, $order_currency, $order_currency_value).'</td>';
    		}
    		
    		if ($full_cat_permissions)	$js .= "request_ids.push(".$buyback_order_product_row['buyback_request_id'].");";
    		
    		$productsHTML .= '	</tr>';
    		$row_count++;
    	}
    	//End Products Box
    	
    	//Start Comments Box
    	$commentsHTML = '';
    	
    	$buyback_comment_select_sql = "	SELECT * 
    									FROM " . TABLE_BUYBACK_STATUS_HISTORY . " 
    									WHERE buyback_request_group_id = '".$buyback_request_group_id."' 
    									ORDER BY date_added";
    	$buyback_comment_result_sql = tep_db_query($buyback_comment_select_sql);
    	while ($buyback_comment_row = tep_db_fetch_array($buyback_comment_result_sql)) {
    		if ((int)$buyback_comment_row['set_as_buyback_remarks'] == 1) {
              	$buybackRemarkSelectedRow = 'class="orderRemarkSelectedRow"';
              	$action_str	= "&nbsp;";
    		} else {
              	$buybackRemarkSelectedRow = "";
              	$action_str = '<a href="'.tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params()."action=set_remark&set_buyback_remark_id={$buyback_comment_row['buyback_status_history_id']}").'">'.TEXT_ACTION_SET_BUYBACK_REMARK.'</a>';
    		}
    		
            $img_str = ((int)$buyback_comment_row['customer_notified'] == 1) ? tep_image(DIR_WS_ICONS.'tick.gif') : tep_image(DIR_WS_ICONS.'cross.gif');
    		
    		$commentsHTML .= '	<tr '.$buybackRemarkSelectedRow.'>
    								<td class="smallText" align="center">'.$buyback_comment_row['date_added'].'</td>
    								<td class="smallText" align="center">'.$img_str.'</td>
    								<td class="smallText" align="center">'.($status_options[$buyback_comment_row['buyback_status_id']] ? $status_options[$buyback_comment_row['buyback_status_id']] : '&nbsp;').'</td>
    								<td class="smallText">'.(tep_not_null($buyback_comment_row['comments']) ? nl2br($buyback_comment_row['comments']) : '&nbsp;').'</td>
    								<td class="smallText" align="center">'.(tep_not_null($buyback_comment_row['changed_by']) ? $buyback_comment_row['changed_by'] : '&nbsp;').'</td>
    								<td class="smallText" align="center">'.$action_str.'</td>
    							</tr>';
    	}
    	//End Comments Box
    	
    	//buyback tag
    	$tag_selection_general = array ( array('id' => '', 'text' => 'Order Lists Options ...'),
    									 array('id' => 'rd', 'text' => '&nbsp;&nbsp;&nbsp;Mark as read'),
    									 array('id' => 'ur', 'text' => '&nbsp;&nbsp;&nbsp;Mark as unread'),
    									 array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
    								   );
    	
    	$mirror_for_delete_tag = array();
    	$status_dependent_tag_array = $tag_selection_general;
    	$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$status_id."', orders_tag_status_ids) AND filename='".FILENAME_BUYBACK_REQUESTS."' ORDER BY orders_tag_name;";
    	$order_tag_result_sql = tep_db_query($order_tag_select_sql);
    	while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
    		$status_dependent_tag_array[] = array('id' => 'otag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
    		$mirror_for_delete_tag[] = array('id' => 'rmtag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
    	}
    	$status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
    	$status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
    	$status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);
    ?>
          				<tr>
            				<td>
            					<table border="0" width="100%" cellspacing="0" cellpadding="0">
            						<tr>
                						<td valign="top" width="80%">
                							<table border="0" width="100%" cellspacing="0" cellpadding="0">
                								<tr>
                									<td width="70%">
    			            							<table border="0" width="100%" cellspacing="1" cellpadding="3">
    														<tr>
    															<td class="main" width="15%"><b><?=TEXT_BUYBACK_ORDER_NUMBER?></b></td>
    															<td class="main"><b><?=$buyback_request_group_id . ($status_id == 3 ? '&nbsp;' . sprintf(TEXT_BUYBACK_ORDER_VERIFIED_STATUS, ($buyback_order_row['buyback_request_group_verify_mode'] ? TEXT_BUYBACK_ORDER_VERIFIED : TEXT_BUYBACK_ORDER_NOT_VERIFIED)) : '')?></b></td>
    														</tr>
    														<tr>
    															<td class="main"><b><?=TEXT_ORDER_DATE_TIME?></b></td>
    															<td class="main"><?=$date_ordered?></td>
    														</tr>
    														<tr>
    															<td class="main"><b><?=TEXT_ORDER_STATUS?></b></td>
    															<td>
                													<table border="0" cellspacing="2" cellpadding="0">
                														<tr>
                															<td class="main"><?=$status_name?></td>
                															<td align="right" class="main"><?='&nbsp;&nbsp;'.tep_draw_pull_down_menu($status_name."_tag_selector", $status_dependent_tag_array, '', ' id="'.$status_name.'_tag_selector" onChange="orderListsOptions(this, \''.$status_id.'\', \''.(int)$languages_id.'\', \''.$buyback_request_group_id.'\');"')?></td>
                															<td class="smallText" nowrap>
                						            						<?
                						            						$tags_str = '';
                															if (tep_not_null($the_orders_tag_ids)) {
                																$orders_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '".$the_orders_tag_ids."') AND filename='".FILENAME_BUYBACK_REQUESTS."';";
                																$orders_assigned_tag_result_sql = tep_db_query($orders_assigned_tag_select_sql);
                																while ($orders_assigned_tag_row = tep_db_fetch_array($orders_assigned_tag_result_sql)) {
                																	$tags_str .= $orders_assigned_tag_row["orders_tag_name"] . ', ';
                																}
                																if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
                															}
                						            						echo '<span class="greenIndicator" id="tag_'.$buyback_request_group_id.'">&nbsp;&nbsp;'.$tags_str.'</span>';
                						            						?>
                						            						</td>
                														</tr>
                													</table>
                												</td>
    														</tr>
    														<tr>
    															<td class="main"><b><?=TEXT_ORDER_TRADE_MODE?></b></td>
    															<td class="main"><?=$trade_mode_array[$trade_mode]?></td>
    														</tr>
    													</table>
    												</td>
    												<td align="right" valign="top" class="main" nowrap>
<?
    													if (($status_id == 1 || ($status_id == 2 && (int)$total_received == 0)) && $buyback_request_group_expiry_date != '0000-00-00 00:00:00') {
    														echo '<div id="auto_cancellation">';
    														echo sprintf(TEXT_AUTO_CANCELLATION, $expiry_mins . ' + 10');
    														echo tep_button(BUTTON_CANCEL, '', '', 'onClick="deactive_auto_cancellation('.$buyback_request_group_id.');"', 'inputButton');
    														echo '</div>';
    													}
    													
    													if ($verify_buyback_order_permission) {
    														if ($status_id == 3) {
    															echo tep_draw_form('buyback_order_verify_form', FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params() . 'action=verify_buyback_order&v_mode='.($buyback_order_row['buyback_request_group_verify_mode'] ? '0' : '1'), 'POST', '');
    															if ($buyback_order_row['buyback_request_group_verify_mode']) {
    																echo tep_submit_button('Mark as Unverified', 'Mark as Unverify', '', 'inputButton');
    															} else {
    																echo tep_submit_button('Mark as Verified', 'Mark as Verified', '', 'inputButton');
    															}
    															echo '</form>';
    														}
    													}
?>
    												</td>
    											</tr>
    										</table>
    									</td>
    								</tr>
    							</table>
            				</td>
            			</tr>
            			<tr>
            				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          				</tr>
          				<tr>
            				<td>
            					<table width="100%" border="0" cellspacing="0" cellpadding="2">
              					    <tr>
                						<td><?=tep_draw_separator()?></td>
              						</tr>
    								<tr>
                    					<td class="pageHeading" valign="top"><b><?echo $user_type . " ".TEXT_INFORMATION;?></b></td>
                  					</tr>
              					</table>
              				</td>
              			</tr>
                  		<tr>
                			<td valign="top">
                				<table width="100%" border="0" cellspacing="0" cellpadding="0">
                					<tr>
                						<td valign="top" width="50%">
    			            				<table width="100%" border="0" cellspacing="1" cellpadding="3">
    			              					<tr>
    			              						<td class="main" width="25%" valign="top"><b><?=$user_type?>:</b></td>
    												<td class="main"><span class="blackIndicator"><?=$user_address?></span></td>
                    							</tr>
    <?	if ($view_supplier_status_permission) {  ?>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_GENDER?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=$user_gender?></span></td>
    			              					</tr>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_DOB?></b></td>
                    								<td class="main">
                    									<span class="blackIndicator">
    													<? 	echo tep_date_short($user_dob, PREFERRED_DATE_FORMAT);
    			                					 		if (tep_not_null($user_dob))
    															echo '&nbsp;(' . tep_calculate_age($user_dob, '', 0) . ')';
    			                						?>
    			                						</span>
    			                					</td>
    			              					</tr>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_EMAIL?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=$user_email?></span></td>
    			              					</tr>
    			              					<tr>
        			              					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
        			              				</tr>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_TELEPHONE?></b></td>
                    								<td class="main">
                    								<?
                    									if (isset($customer_complete_phone_info_array)) {
                    										echo "+" . $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . $customer_complete_phone_info_array['telephone_number'];
                    										$customer_share_tel_array = tep_get_customer_share_phone($customer_complete_phone_info_array['country_international_dialing_code'], $customer_complete_phone_info_array['telephone_number']);
    
                    										if (count($customer_share_tel_array) > 1) {
    					                						echo 	tep_draw_form('cust_lists_share_tel', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
    																	tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_tel_array)) . "\n" .
    																	'<br><span class="smallText"><span class="redIndicator"><a href="javascript: document.cust_lists_share_tel.submit();"><u>' . count($customer_share_tel_array) . '</u></a> customers sharing this telephone number.</span></span><br>' .
    																	'</form>' . "\n";
    					                					}
                    									} else {	// Old supplier data
                    										echo $user_telephone;
                    									}
                    								?>
    
                    								</td>
    			              					</tr>
    			              					<!-- disable fax as requested
    			              					<tr> 
    			              						<td class="main" valign="top"><b><?=TEXT_FAX?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=$user_fax?></span></td>
    			              					</tr>
    			              					-->
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_MOBILE?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=$user_mobile?></span></td>
    			              					</tr>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_IM?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=(($user_im) ? $user_im : TEXT_NOT_AVAILABLE)?></span></td>
    			              					</tr>
    			              					<tr>
        			              					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
        			              				</tr>
    			              					
    			              					<!--tr>
    			              						<td class="main" valign="top"><b><?=TEXT_SITE?></b></td>
                    								<td class="main"><span class="blackIndicator"><?=$site_name?></span></td>
    			              					</tr-->
<?		} //end $view_supplier_status_permission 
	$co_customer_share_ip_array = tep_get_customer_share_ip($user_ip, 'CO', $date_ordered);
	$bo_customer_share_ip_array = tep_get_customer_share_ip($user_ip, 'BO', $date_ordered);
	
	$ip_alert_date_range_select_sql = "SELECT DATE_SUB('".$date_ordered."', INTERVAL 7 DAY) AS from_date, 
											   IF(DATE_ADD('".$date_ordered."', INTERVAL 7 DAY) > CURDATE(), 'TODAY', DATE_ADD('".$date_ordered."', INTERVAL 7 DAY)) AS to_date ";
	$ip_alert_date_range_result_sql = tep_db_query($ip_alert_date_range_select_sql);
	$ip_alert_date_range_row = tep_db_fetch_array($ip_alert_date_range_result_sql);
?>
												<tr>
    			              						<td class="main" valign="top"><b><?=TEXT_ORDERING_IP_ADD?></b></td>
                    								<td class="main">
                    									<?
                    										echo tep_show_ip($user_ip, ((count($bo_customer_share_ip_array) > 1) || (count($co_customer_share_ip_array) > 1) ? '<span class="redIndicator">'.$user_ip.'</span>' : $user_ip));
                    										if (count($bo_customer_share_ip_array) > 1) {
    							                				echo '<br><span class="smallText"><span class="redIndicator"><a href="' . tep_href_link(FILENAME_CUSTOMERS, 'BO_IP=' . $user_ip . '&action=show_report&customer_lists_subaction=do_search') . '" target="_blank">' . count($bo_customer_share_ip_array) . '</a> customers sharing this IP in BO ('.$ip_alert_date_range_row['from_date'].' - '.$ip_alert_date_range_row['to_date'].').</span></span>' . "\n";
    							                			} else {
    							                				echo '<br><span class="smallText">No customer sharing this IP in BO ('.$ip_alert_date_range_row['from_date'].' - '.$ip_alert_date_range_row['to_date'].')</span>';
    							                			}
    							                			
    							                			if (count($co_customer_share_ip_array) > 1) {
    							                				echo 	tep_draw_form('cust_lists_share_ip', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
																		tep_draw_hidden_field('customer_share_id', tep_array_serialize($co_customer_share_ip_array)) . "\n" . 
																		'<br><span class="smallText"><span class="redIndicator"><a href="javascript: document.cust_lists_share_ip.submit();"><u>' . count($co_customer_share_ip_array) . '</u></a> customers sharing this IP in CO ('.$ip_alert_date_range_row['from_date'].' - ' .$ip_alert_date_range_row['to_date'].').</span></span>' . 
																		'</form>' . "\n";
    							                			} else {
    							                				echo '<br><span class="smallText">No customer sharing this IP in CO ('.$ip_alert_date_range_row['from_date'].' - '.$ip_alert_date_range_row['to_date'].')</span>';
    							                			}
    							                			
    							                			$order_ip_select_sql = "SELECT remote_addr
																					FROM ".TABLE_ORDERS." 
																					WHERE orders_id = '".(int)$orders_id."'";
															$order_ip_result_sql = tep_db_query($order_ip_select_sql);
															if ($order_ip_row = tep_db_fetch_array($order_ip_result_sql)) {
																if ($user_ip == $order_ip_row['remote_addr']) {
																	echo '<br><span class="smallText"><span class="redIndicator">Same as CO IP ('.$order_ip_row['remote_addr'].')</span></span>';
																} else {
																	echo '<br><span class="smallText">Not same as CO IP ('.$order_ip_row['remote_addr'].')</span>';
																}
															}
    							                		?>
                    								</td>
    			              					</tr>
    			              					<tr>
    			              						<td class="main" valign="top"><b><?=$user_type." ".TEXT_COMMENT?>:</b></td>
                    								<td class="main"><?=nl2br($buyback_comment)?></td>
                    							</tr>
                    						</table>
    			            			</td>
    			            			<td valign="top">
<?  	if ($view_buyback_statistics_permission) { ?>
    			            			    <table width="100%" border="0" cellspacing="0" cellpadding="3">
    			            			        <tr>
                			                        <td class="main" valign="top" width="25%"><?='<b>'. TEXT_ORDER_STATISTICS .'</b><br><a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS, 'action=show_report&include_subcategory=1&customer_email='. $user_email) .'" target="_blank">'.LINK_BUYBACK_HISTORY.'</a>'?></td>
                			                        <td class="main" valign="top">
                			                            <table width="100%" border="1" cellspacing="0" cellpadding="3">
                			                                <tr>
                			                                    <td>&nbsp;</td>
                			                                    <td class="main" align="center"><?=SUB_TABLE_HEADING_TOTAL_ORDER?></td>
                			                                    <td class="main" align="right"><?=SUB_TABLE_HEADING_TOTAL_AMOUNT . ($currencies->currencies[$currency]['symbol_left'] != '' ? $currencies->currencies[$currency]['symbol_left'] : $currencies->currencies[$currency]['symbol_right'])?></td>
                			                                </tr>
<?
                                                            // order statistics
                                                            $order_status = tep_get_buyback_status();
                                                            foreach ($order_status AS $key => $order_status_array) {
                                                                $total_count = $one_day_total_count =0;
                                                                $total_amount = $one_day_sum_total_amount =0;
                                                                $actual_amount =  0;
                                                                $sum_total_amount = 0;
                                                                $count_order_select_sql = " SELECT br.*, brg.buyback_request_group_site_id, brg.currency, brg.currency_value, brg.buyback_request_group_date  
                                                                                            FROM ". TABLE_BUYBACK_REQUEST_GROUP ." AS brg 
                                                                                            INNER JOIN ". TABLE_BUYBACK_REQUEST ." AS br 
                                                                                                on (brg.buyback_request_group_id=br.buyback_request_group_id)
                                                                                            WHERE brg.customers_id = '". $user_id ."'
                                                                                                AND brg.buyback_status_id = '". $order_status_array['id'] ."'";
                                                                
                                                                $count_order_result_sql = tep_db_query($count_order_select_sql);
                                                                while ($count_order_row = tep_db_fetch_array($count_order_result_sql)) {
    															    $count_order_row['buyback_request_quantity']."<br>";
    															    
    															    if ($order_status_array['id'] == 2 || $order_status_array['id'] == 3) {
        															    $received_quantity = (int)$count_order_row['buyback_quantity_received'];
        	                                                            
        	                                                            if ($count_order_row['buyback_request_group_site_id'] == 0) { // For website buyback, assume confirm qty same as request qty
        	                                                                $count_order_row['buyback_quantity_confirmed'] = $count_order_row['buyback_request_quantity'];
                                                        				} else if ($count_order_row['buyback_quantity_confirmed'] == 0) { // If CN buyback is recevied qty from cancel status
                                                                		    $count_order_row['buyback_quantity_confirmed'] = $count_order_row['buyback_request_quantity'] * 1.1;
                                                                		}
                                                                		$actual_amount = $count_order_row['buyback_unit_price'] * ($received_quantity > $count_order_row['buyback_quantity_confirmed'] ? $count_order_row['buyback_quantity_confirmed'] : $received_quantity);
                                                                    	$total_amount = $currencies->apply_currency_exchange($actual_amount, $count_order_row['currency'], $count_order_row['currency_value']);
                                                                    	
                                                                    	$sum_total_amount += $total_amount;
                                                                    }
                                                                    
                                                                    if ($order_status_array['id'] != 1 && number_format(tep_day_diff($count_order_row['buyback_request_group_date'], date('Y-m-d H:i:s'))) == 0) {
                                                                        $one_day_total_count++;
                                                                        $one_day_sum_total_amount += $total_amount;
                                                                    }
                                                                    $total_count++;
    															}
    															
        														echo '      <tr>
        														                <td class="main" colspan="3"><b>'. $order_status_array['text'] .'</b></td>
        														            </tr>';
        														if ($order_status_array['id'] != 1) {
    														        echo '  <tr>
    														                    <td class="main">Buyback orders in 1 day</td>
        														                <td class="main" align="center">'. $one_day_total_count .'</td>
        														                <td class="main" align="right">'. $currencies->format($one_day_sum_total_amount, false, $currency) .'</td>
        														            </tr>';
    														    }
    														    
        														echo '      <tr>
        														                <td class="main">Total buyback order</td>
        														                <td class="main" align="center">'. $total_count .'</td>
        														                <td class="main" align="right">'. $currencies->format($sum_total_amount, false, $currency) .'</td>
        														            </tr>';
                                                            }
?>
                                                        </table>
                                                    </td>
                                                </tr>
                                         	</table>
<?		} else {
            echo '&nbsp;';
        }
?>
    			            			</td>
                  					</tr>
<? 			
		if (tep_not_null($orders_id)) { 
			$aft_obj = new anti_fraud();
			$threat_metrix_data_array = $aft_obj->getAftModule()->get_buyback_order_tm_info($buyback_request_group_id, $orders_id);
			
			if (tep_not_null($threat_metrix_data_array['transaction_identifier'])) {
?>
                  					<tr>
                                    	<td valign="top" colspan="2">
                                    		<table width="100%" border="0" cellspacing="1" cellpadding="3">
                    							<tr>
                									<td colspan="2"><?=tep_draw_separator()?></td>
              									</tr>
              									<tr>
              										<td class="pageHeading"><?=TABLE_HEADING_DEVICE_IDENTIFICATION?></td>
              										<td></td>
              									</tr>
              									<tr>
													<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
												</tr>
              									<tr>
              										<td colspan="2">
	              										<table border="0" cellspacing="0" cellpadding="0">
															<tr>
																<td class="main" width="290px"><b><?=TEXT_TRANSACTION_IDENTIFIER?>:</b></td>
																<td class="main"><?=$threat_metrix_data_array['transaction_identifier']?></td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_VERIFICATION_RESULT?>:</b></td>
																<td class="main"><?=($threat_metrix_data_array['request_result'] === 1) ? TEXT_SUCCESS : '<span class="redIndicator">'.TEXT_FAILED.'</span>'?></td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_BO_CO_DEVICE_ID_MATCH?>:</b></td>
																<td class="main">
<?
				if ($threat_metrix_data_array['device_id_match'] === 1) {
					echo '<span class="redIndicator"><b>'.TEXT_MATCH.'</b></span>';
				} else if ($threat_metrix_data_array['device_id_match'] === 0) {
					echo '<b>'.TEXT_NOT_MATCH.'</b>';
				} else {
					echo $threat_metrix_data_array['device_id_match'];
				}
?>
																</td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_DEVICE_ID?>:</b></td>
																<td class="main">
<?
				if (tep_not_null($threat_metrix_data_array['list_of_accounts_involved'])) {
					echo tep_draw_form('shared_device_id_form', FILENAME_CUSTOMERS, tep_get_all_get_params(array('buyback_request_group_id', 'action')).'action=ip_share_summary&cID='.$user_id, 'post', 'target="_blank"') . "\n";
					echo tep_draw_hidden_field('type', 'shared_device') . "\n";
					echo tep_draw_hidden_field('cID', implode(",", $threat_metrix_data_array['list_of_accounts_involved'])) . "\n";
					echo tep_draw_hidden_field('dID', $threat_metrix_data_array['device_id']) . "\n";
					echo tep_draw_hidden_field('qID', $threat_metrix_data_array['query_id']) . "\n";
					echo '</form>';
					echo '<a href="javascript:document.shared_device_id_form.submit();"><span class="redIndicator">' . 
								$threat_metrix_data_array['device_id'] . 
								(count($threat_metrix_data_array['list_of_accounts_involved']) > 1 ? ' (' . count($threat_metrix_data_array['list_of_accounts_involved']) . ')' : '').'</span></a>';
				} else {
					echo TEXT_NONE2;
				}
?>
																</td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_TOTAL_DEVICES?>:</b></td>
																<td class="main"><?=($threat_metrix_data_array['number_of_devices_exist'] == 'NA') ? 'NA' : '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $user_id . '&action=device_id_history') . '" target="_blank">'.$threat_metrix_data_array['number_of_devices_exist'].'</a>'?></td>
															</tr>
														</table>
	              									</td>
	              								</tr>
              									<tr>
													<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
												</tr>
              									<tr>
				          							<td colspan="2">
														<table border="1" width="50%" cellspacing="0" cellpadding="0">
															<tr>
																<td class="main" nowrap style="width:145px"><b><?=TEXT_TRUE_IP?>:</b></td>
																<td class="main" align="center" nowrap><?=($threat_metrix_data_array['true_ip']=='NA') ? 'NA' : tep_show_ip($threat_metrix_data_array['true_ip'])?></td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_CITY?>:</b></td>
																<td class="main" align="center" nowrap><?=$threat_metrix_data_array['true_ip_city']?></td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_COUNTRY?>:</b></td>
																<td class="main" align="center" nowrap>
<?
				if ($threat_metrix_data_array['true_ip']=='NA') {
					echo 'NA';
				} else {
					$country_info = tep_get_countries_info($threat_metrix_data_array['true_ip_country'], 'countries_iso_code_2');
					echo '<span class="'.($threat_metrix_data_array['true_ip_country']=='NA' ? 'redIndicator':'').'">' . $country_info['countries_name'] . '</span>';
				}
?>
																</td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_COUNTRY_CODE?>:</b></td>
																<td class="main" align="center" nowrap><?=$threat_metrix_data_array['true_ip_country']?></td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_ISP?>:</b></td>
																<td class="main" align="center" nowrap><?=$threat_metrix_data_array['true_ip_isp']?></td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_ORGANIZATION?>:</b></td>
																<td class="main" align="center" nowrap><?=$threat_metrix_data_array['true_ip_organization']?></td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
												</tr>
												<tr>
				          							<td colspan="2">
														<table border="1" width="50%" cellspacing="0" cellpadding="0">
															<tr>
																<td class="main" nowrap style="width:145px"><b><?=TEXT_PROXY_DETECTED?>:</b></td>
																<td class="main" align="center" nowrap>
<?
				if ($threat_metrix_data_array['proxy_detected'] === 0) {
					echo '<b>'.TEXT_NO.'</b>';
				} else if ($threat_metrix_data_array['proxy_detected'] === 1) {
					echo '<span class="redIndicator"><b>'.TEXT_YES.'</b></span>';
				} else {
					echo $threat_metrix_data_array['proxy_detected'];
				}
?>
																</td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_PROXY_TYPE?>:</b></td>
																<td class="main" align="center" nowrap><?=($threat_metrix_data_array['proxy_type'] == 'NA') ? 'NA' : '<span class="redIndicator">'.ucfirst($threat_metrix_data_array['proxy_type']).'</span>'?></td>
															</tr>
															<tr>
																<td class="main" nowrap><b><?=TEXT_TRUE_IP_ATTRIBUTES?>:</b></td>
																<td class="main" align="center" nowrap><?=$threat_metrix_data_array['true_ip_attributes']?></td>
															</tr>
															
															<tr>
																<td class="main"><b><?=TEXT_PROXY_IP_INFORMATION?>:</b></td>
																<td class="main" align="center">
<?
				if(count($threat_metrix_data_array['proxy_ip_sharing'])) {
					echo tep_draw_form('shared_proxy_ip_form', FILENAME_CUSTOMERS, tep_get_all_get_params(array('page', 'action')).'action=ip_share_summary', 'post', 'target="_blank"') . "\n";
					echo tep_draw_hidden_field('type', 'shared_device') . "\n";
					echo tep_draw_hidden_field('cID', implode(",", $threat_metrix_data_array['proxy_ip_sharing'])) . "\n";
					echo tep_draw_hidden_field('dID', $threat_metrix_data_array['device_id']) . "\n";
					echo tep_draw_hidden_field('qID', $threat_metrix_data_array['query_id']) . "\n";
					echo '</form>';
					echo '<a href="javascript:document.shared_proxy_ip_form.submit();">' . $threat_metrix_data_array['proxy_ip'] . ' (' . count($threat_metrix_data_array['proxy_ip_sharing']) . ')</a>';
				} else {
					echo $threat_metrix_data_array['proxy_ip'];
				}
?>
																</td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_PROXY_IP_CITY?>:</b></td>
																<td class="main" align="center"><?=$threat_metrix_data_array['proxy_ip_city']?></td>
															<tr>
																<td class="main"><b><?=TEXT_PROXY_IP_COUNTRY_CODE?>:</b></td>
																<td class="main" align="center"><?=$threat_metrix_data_array['proxy_ip_country']?></td>
															</tr>
															<tr>
																<td class="main"><b><?=TEXT_PROXY_IP_ISP?>:</b></td>
																<td class="main" align="center"><?=$threat_metrix_data_array['proxy_ip_isp']?></td>
															</tr>
														</table>
													</td>
												</tr>
              									<tr>
													<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
												</tr>
              								</table>
                                    	</td>
                                    </tr>
<?
			}
		}
?>
                  					<tr>
                                    	<td valign="top" colspan="2">
                                    		<table width="100%" border="0" cellspacing="1" cellpadding="3">
                    							<tr>
                									<td colspan="2"><?=tep_draw_separator()?></td>
              									</tr>
<? 			if (tep_not_null($orders_id) && $orders_id > 0) { ?>
              									<tr>
              										<td class="main" valign="top" width="250"><b><?=TEXT_DELIVER_FOR_ORDER?></b></td>
              										<td class="main"><a href="<?=tep_href_link(FILENAME_ORDERS, 'oID='. $orders_id .'&action=edit')?>" target="_blank"><?=$orders_id?></a></td>
              									</tr>
              									<tr>
													<td class="main" valign="top"><b><?=TEXT_DELIVERY_MODE?></b></td>
													<td class="main"><?=$delivery_array[$delivery_mode]?></td>
												</tr>
<? 			} ?>												
<? 			if ($trade_mode == 2 && ($status_id == 3 || $status_id == PARTIAL_RECEIVE_STATUS)) { ?>
              									<tr>
              										<td class="main" valign="top" width="150"><b><?=TEXT_SUPPLIER_CODE?></b></td>
              										<td class="main"><span class="redIndicator"><?=$supplier_code?></span></td>
              									</tr>
              									<tr>
              										<td class="main" valign="top" width="150"><b><?=TEXT_CUSTOMER_CODE?></b></td>
              										<td class="main"><span class="redIndicator"><?=$customers_matching_code?>&nbsp;(<?=($customers_matching_code==$customers_ori_code ? TEXT_MATCH : TEXT_NOT_MATCH)?>)</span></td>
              									</tr>
              									
<?			}
			echo '							</table>
										</td>
									</tr>';
?>
<? //if ($status_id == 3 || $status_id == PARTIAL_RECEIVE_STATUS) { ?>
<? if ($view_screenshot_permission && tep_not_null($screen_shot_before_trade) && tep_not_null($screen_shot_after_trade)) { ?>
									<tr>
                                    	<td valign="top" colspan="2">
                                    		<table width="100%" border="0" cellspacing="1" cellpadding="3">
                    							<tr>
                									<td colspan="2"><?=tep_draw_separator()?></td>
              									</tr>
              									<tr>
              										<td class="main" valign="top" width="150"><b><?=TEXT_SCREEN_SHOT?></b></td>
              										<td class="main">
<?
 				echo '<a href="'.tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_bb_ss&req_grp_id='.$buyback_request_group_id.'_1').'" target="_blank">before_trade.jpg</a>&nbsp;';
				echo '<a href="'.tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_bb_ss&req_grp_id='.$buyback_request_group_id.'_2').'" target="_blank">after_trade.jpg</a>';
?>
          											</td>
          										</tr>
          									</table>
          								</td>
          							</tr>
<? } ?>
                				</table>
                			</td>
                		</tr>
          				<tr>
							<td><?=tep_draw_separator()?></td>
						</tr>
          				<tr>
            				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          				</tr>
<?		if ($status_id == 1) { ?>
          				<tr>
          					<td>
          						<div id="lockingBtn">
<?
									if (tep_not_null($lock_admin_id)) {
										if ($login_id == $lock_admin_id) {
											$editable_view = true;
											
											echo '<div id="navBtn" style="float:left;">
  												<ul>
													<li id="unlock_btn"><a href="javascript:;" onClick="doBBLocked(\''.$login_id.'\', \''.(int)$buyback_request_group_id.'\', \''.(int)$languages_id.'\', \'ul\', \'lockingBtn\');" title="Unlocking this order">UNLOCK</a></li>
												</ul>
      										</div>
      										<div>'.
												sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $buyback_order_row["locking_from_ip"], $buyback_order_row["locking_from_ip"]) . '
											</div>';
										} else {
											// Order has been locked by other person
											
											$admin_email_address_select_sql = "SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$lock_admin_id . "'";
											$admin_email_address_result_sql = tep_db_query($admin_email_address_select_sql);
											$admin_email_address_row = tep_db_fetch_array($admin_email_address_result_sql);
											
											$admin_group_to_contact = tep_admin_group_unlock_permission();
											if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
												$contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
												$contact_admin_group_id_array = array_keys($admin_group_to_contact);
											} else {
												$contact_admin_group_msg = '';
												$contact_admin_group_id_array = array();
											}
											
											if (in_array($login_groups_id, $contact_admin_group_id_array)) {	// this admin has the permission to unlock other orders
												echo '	<div id="navBtn">
			  												<ul>
																<li id="unlock_btn"><a href="javascript:;" onClick="doBBLocked(\''.$login_id.'\', \''.(int)$buyback_request_group_id.'\', \''.(int)$languages_id.'\', \'ulo\', \'lockingBtn\');" title="Unlocking this order">UNLOCK</a></li>
															</ul>
			      										</div>
			      										<div style="float:left;">'.
															sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, tep_not_null($admin_email_address_row["admin_email_address"]) ? $admin_email_address_row["admin_email_address"] : $lock_admin_id, $buyback_order_row["locking_datetime"], $buyback_order_row["locking_from_ip"]).'
														</div>';
											} else {
												echo '	<div style="float:left;">'.
															sprintf(TEXT_ORDER_LOCKED_BY_OTHER, tep_not_null($admin_email_address_row["admin_email_address"]) ? $admin_email_address_row["admin_email_address"] : $lock_admin_id, $buyback_order_row["locking_datetime"], $buyback_order_row["locking_from_ip"], $contact_admin_group_msg).'
														</div>';
											}
										}
									} else {
										echo '	<div id="navBtn" style="float:left;">
													<ul>
														<li id="lock_btn"><a href="javascript:;" onClick="doBBLocked(\''.$login_id.'\', \''.(int)$buyback_request_group_id.'\', \''.(int)$languages_id.'\', \'l\', \'lockingBtn\', \''.date("Y-m-d H:i:s").'\');" title="Locking this order">LOCK</a></li>
													</ul>
		  										</div>
		  										<div>' . 
													TEXT_ORDER_NOT_BEEN_LOCKED . '
												</div>';
									}
?>
          						</div>
          					</td>
          				</tr>
<?		} ?>
          				<tr>
							<td>
								<div id="locking_history">
									<div style="float:left;"><a href="javascript:;" onClick="lockingHistory('<?=(int)$buyback_request_group_id?>', '1', 'locking_history');">Show Locking History</a></div>
								</div>
							</td>
						</tr>
          				<tr>
            				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          				</tr>
    				  	<tr>
    				  		<td>
    						<form name="partial_receive_form" action="<?=tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO,tep_get_all_get_params(array("action"))."action=" . $form_action_str)?>" method="post">
<?    	$total_colspan = 7;
    	if ($status_id == PARTIAL_RECEIVE_STATUS) {
    		$total_colspan += 6;
    	} else if ($status_id == CANCEL_STATUS) {
    	    $total_colspan += 4;
    	}else {
    		$total_colspan += 2;
    	}
    	
    	$total_colspan += 3;
?>
    				  	    	<table width="100%" border="0" cellspacing="1" cellpadding="2">
    						  		<tr>
<?   	echo '					  		<td class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_NAME.'</td>';
    	
    	if ($status_id == 1 || $status_id == PARTIAL_RECEIVE_STATUS) {
    	    echo '					  	<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_BUYBACK_ORDER.'</td>';
    	    //echo '					  	<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_BACK_ORDER.'</td>';
    		//echo '					  	<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_NORMAL_ORDER.'</td>';
    	}
    	if ($status_id==PARTIAL_RECEIVE_STATUS) {
    		echo '						<td class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_LOCATION.'</td>';
    	}
    	
    	if ($custom_products_type_id != 4) {
    		echo '							<td width="10%" class="ordersBoxHeading">'.TABLE_HEADING_RSTK_CHARACTER.'</td>';
			echo '							<td width="10%" class="ordersBoxHeading">'.TABLE_HEADING_SENDER_CHARACTER.'</td>';
    	}
		
    	echo '					  		<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_ACTUAL_QTY.'</td>';
    	
    	if ($status_id != PARTIAL_RECEIVE_STATUS)	echo '<td width="7%" align="right" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_UNIT_PRICE.'</td>';
    	
    	echo '					  		<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_QUANTITY.'</td>';
    	echo '							<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_CONFIRMED_QUANTITY.'</td>';
    	echo '					  		<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED.'</td>';
    	
    	if ($status_id==PARTIAL_RECEIVE_STATUS || $status_id==CANCEL_STATUS) {
    		if (($trade_mode <= 2) || ($trade_mode == 2 && $customers_matching_code != $customers_ori_code && $customers_matching_code != 0)) {
	    		echo '				  		<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_BALANCE.'</td>
	    							  		<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_RECEIVE.'</td>
	    							  		<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_EXTRA_QTY.'</td>';
	    	} else {
	    		echo '			  			<td width="8%" align="right" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT.'</td>';
	    	}
    	
    	} else {
    		echo '			  			<td width="8%" align="right" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT.'</td>';
    	}
?>
    						  		</tr>
    						  		<?=$productsHTML?>
    								<tr>
    									<td class="ordersRecords" colspan="<?=$total_colspan?>"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
    					 			</tr>
    					 			<tr>
<?		if ($status_id==PARTIAL_RECEIVE_STATUS || $status_id==CANCEL_STATUS) { ?>
    								  	<td class="ordersRecords" colspan="<?=($total_colspan)?>" align="right">
    									<?
    										if (count($partial_inputs))	echo tep_submit_button('Restock', 'Receive Stock', 'name="RestockBtn" onClick="return restock_form_checking();"', 'inputButton');
    									?>
    									</td>
<?		} else {
    		echo '<td align="right" class="ordersRecords" colspan="'.$total_colspan.'">';
    		echo TEXT_TOTAL .': ';
    		$currencies->set_decimal_places(2);
    		echo $currencies->format($payable_total, true, $order_currency, $order_currency_value);
    		$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);
    		echo '						</td>';
    	}
?>
    								</tr>
    								<tr>
    									<td class="ordersRecords" colspan="<?=$total_colspan?>"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
    					 			</tr>
    					 			<tr>
                						<td colspan="<?=$total_colspan?>"><img src="images/pixel_black.gif" border="0" alt="" width="100%" height="1"></td>
              						</tr>
       							</form>
    				  	    	</table>
    				  	    </td>
    				  	</tr>
    					<!-- Comments start -->
    				  	<tr>
    				  		<td class="main">
            					<table border="1" cellspacing="0" cellpadding="5">
              						<tr>
                						<td class="smallText" align="center"><b><?=TEXT_DATE_ADDED?></b></td>
                						<td class="smallText" align="center"><b><?=TEXT_CUSTOMER_NOTIFIED?></b></td>
                						<td class="smallText" align="center"><b><?=TEXT_STATUS?></b></td>
                						<td class="smallText" align="center"><b><?=TEXT_COMMENTS?></b></td>
                						<td class="smallText" align="center"><b><?=TEXT_CHANGED_BY?></b></td>
                						<td class="smallText" align="center"><b><?=TEXT_ACTION?></b></td>
              						</tr>
    								<?=$commentsHTML?>
    							</table>
              				</td>
    				  	</tr>
    					<!-- Comments end -->
						<tbody id="comments_div" class="<?=$allow_update == true ? "show" : "hide"?>">
    				  	<tr>
    				  		<td>
    				  			<form method="POST" action="<?=tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, tep_get_all_get_params(array('action')) . 'action=update_order')?>">
<?
    	echo tep_draw_hidden_field('status_DB_prev', $status_id);
    	
    	if (isset($_SESSION['buyback_lists_param'])) {
    		$back_btn_url = tep_href_link(FILENAME_BUYBACK_REQUESTS, 'action=show_report&cont=1');
    	} else {
    		$back_btn_url = tep_href_link(FILENAME_BUYBACK_REQUESTS);
    	}
    	
    	$orders_statuses_new = array();
    	switch ($status_id) {
    		case 1:	// Pending
    			$orders_statuses_new[] = array(	'id' => "0",
    											'text' => SELECT_OPTION_UPDATE_COMMENT);
    			
    			if (tep_check_status_update_permission('B', $login_groups_id, 1, 4)) { // only the person with permission can move to cancel
        			$orders_statuses_new[] = array(	'id' => "4",
        											'text' => "Cancel");
                }
    			
    			break;
    		case 2:	// Process
    			$orders_statuses_new[] = array(	'id' => "0",
    											'text' => SELECT_OPTION_UPDATE_COMMENT);
    			
    			if ($trade_mode <= 2) {
	    			if (tep_check_status_update_permission('B', $login_groups_id, 2, 1)) { // only the person with permission can move to pending
	    				$orders_statuses_new[] = array(	'id' => "1",
	    												'text' => "*Pending*");
	    			}
    			}
    			
    			if (tep_check_status_update_permission('B', $login_groups_id, 2, 3)) { // only the person with permission can move to complete
	        		$orders_statuses_new[] = array(	'id' => "3",
	        										'text' => "Complete");
	    		}
    			
    			if (tep_check_status_update_permission('B', $login_groups_id, 2, 4)) { // only the person with permission can move to cancel
        			if ($not_received_any) {
        				$orders_statuses_new[] = array(	'id' => "4",
        												'text' => "Cancel");
        			}
        		}
    			break;
    		case 3: // Completed
    			$orders_statuses_new[] = array(	'id' => "0",
    											'text' => SELECT_OPTION_UPDATE_COMMENT);
    			
    			break;
    		case 4:	// Cancel
    			$buyback_quantity_confirmed_select_sql = "	SELECT buyback_quantity_confirmed FROM " . TABLE_BUYBACK_REQUEST . " WHERE buyback_request_group_id = '" . (int)$buyback_request_group_id . "'";
    			$buyback_quantity_confirmed_result_sql = tep_db_query($buyback_quantity_confirmed_select_sql);
    			$buyback_quantity_confirmed_row = tep_db_fetch_array($buyback_quantity_confirmed_result_sql);
    			
    			$orders_statuses_new[] = array(	'id' => "0",
    											'text' => SELECT_OPTION_UPDATE_COMMENT);
    			
    			if (tep_check_status_update_permission('B', $login_groups_id, 4, 1)) { // only the person with permission can move to pending
    				if ((int)$buyback_quantity_confirmed_row['buyback_quantity_confirmed'] < 1) {
    					$orders_statuses_new[] = array(	'id' => "1",
    													'text' => "*Pending*");
    				}
    			}
    			
    			if ((int)$buyback_quantity_confirmed_row['buyback_quantity_confirmed'] > 0) {
    				if (tep_check_status_update_permission('B', $login_groups_id, 4, 2)) { // only the person with permission can move to Processing
    					$orders_statuses_new[] = array(	'id' => "2",
    													'text' => "*Processing*");
    				}
    			}
    			
    			if ($buyback_quantity_confirmed == 0 && $buyback_quantity_received > 0 && tep_check_status_update_permission('B', $login_groups_id, 4, 3)) { // only the person with permission can move to completed
    			    $orders_statuses_new[] = array(	'id' => "3",
    											    'text' => "Complete");
    			}
    			
    			break;
    	}
    ?>
    				  			<table border="0" width="100%" cellspacing="0" cellpadding="2">
                        		    <tr>
                        				<td class="main" colspan="3"><?=tep_draw_comment_select('', FILENAME_BUYBACK_REQUESTS_INFO)?></td>
                        			</tr>
                				  	<tr>
                				  	    <td>
                				  	        <table border="0" cellspacing="0" cellpadding="2">
                				  				<tr>
                				  					<td colspan="2">
                				  						<?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5', '', ' id="admin_comment" ')?>
                				  					</td>
                				  					<td class="main" valign="bottom"><?=tep_draw_checkbox_field('chk_set_as_buyback_remarks','1'). '&nbsp;' . TEXT_ACTION_SET_BUYBACK_REMARK?></td>
                				  				</tr>
                				  				<tr>
                				  					<td class="main" colspan="2">
                				  						<b><?=TEXT_STATUS?>: </b>&nbsp;
                				  						<?=tep_draw_pull_down_menu("status", $orders_statuses_new, 0, ' id="status" onChange="status_check(this, '.(int)$total_received.', ' .($completely_received_regardless_permission ? 'true' : 'false').')" ')?>
                				  					</td>
                				  					<td class="main">&nbsp;</td>
                				  				</tr>
                				  				<tr>
                				  					<td class="main"><b><?=TEXT_NOTIFY.' '.$user_type?>: </b>&nbsp;<?=tep_draw_checkbox_field('chk_user_notify','1')?></td>
                				  					<td class="main" align="right"><input type="button" value="Update" onclick="submit_form(this)" class="inputButton"></td>
                				  					<td class="main">&nbsp;</td>
                				  				</tr>
                				  			</table>
                				  	    </td>
                				  	</tr>
                				</table>
    				  			</form>
    				  		</td>
    				  	</tr>
    					</tbody>
    				  	<tr>
    				  		<td><br>
    				  			<table border="0" width="100%" cellspacing="0" cellpadding="2">
    				  				<tr>
    				  					<td align="left" class="main"><?=tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, $back_btn_url, '', 'inputButton')?></td>
    				  					<td align="right" class="main"><?=($status_id==2 || $status_id==3 ? tep_button(TEXT_PRINTABLE_VERSION, TEXT_PRINTABLE_VERSION, '', 'onclick="javascript:window.open(\''.tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO_PRINT, tep_get_all_get_params()).'\')"', 'inputButton') : '&nbsp;')?>
    				  				</tr>
    				  			</table>
    				  		</td>
    					</tr>
    				  	<script language="javascript">
    						var request_ids = new Array();
    						var total_balance = 0;
    						
    						function showOverEffect(object, class_name, extra_row) {
    							rowOverEffect(object, class_name);
    							var rowObjArray = extra_row.split('##');
    							for (var i = 0; i < rowObjArray.length; i++) {
    								if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
    									rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
    								}
    							}
    						}
    						
    						function showOutEffect(object, class_name, extra_row) {
    							rowOutEffect(object, class_name);
    							var rowObjArray = extra_row.split('##');
    					  		for (var i = 0; i < rowObjArray.length; i++) {
    					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
    					  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
    					  			}
    					  		}
    						}
    						
    						function showClicked(object, class_name, extra_row) {
    							rowClicked(object, class_name);
    							if (extra_row=="")
    								return;
    							var rowObjArray = extra_row.split('##');
    					  		for (var i = 0; i < rowObjArray.length; i++) {
    					  			rowClicked(document.getElementById(rowObjArray[i]), class_name);
    							}
    						}
    						
    						function checkBalance(obj, recv_qty, max_qty) {
    							if (obj != null) {
    								recv_qty = parseInt(recv_qty);
    								max_qty = parseInt(max_qty);
    								obj.value = trim_str(obj.value);
    								
    								var ref_id = replace(obj.id, 'partial_receive_', '');
    								var qty_sign_obj = DOMCall('partial_receive_sign_' + ref_id);
    								
    								if (obj.value != '' && qty_sign_obj != null) {
    									if (!validateInteger(obj.value)) {
    										obj.value = '';
    									} else {
    										var entered_qty = parseInt(qty_sign_obj.value + '' + obj.value);
    										if (entered_qty >= 0) {
    										<?  if ($status_id != CANCEL_STATUS) { ?>
        											if (entered_qty > max_qty) {
        												alert('Entered receive quantity exceed balance quantity!\nPlease reduce the quantity!');
        												obj.focus();
        												return false;
        											}
    										<?  } else { ?>
    										        return true;
    									    <?  } ?>
    										} else {
    											if (Math.abs(entered_qty) > recv_qty) {
    												alert('Entered deduct receive quantity exceed received quantity!\nPlease reduce the deduction quantity!');
    												obj.focus();
    												return false;
    											}
    										}
    									}
    								}
    							}
    							
    							return true;
    						}
    						
    						function restock_form_checking(ref) {
    							var got_input = false;
    							var invalid_input = false;
    							var invalid_receive_input = false;
    							var over_input = false;
    							var extra_over_input = false;
    							var over_deduct_input = false;
    							var cur_obj = '';
    							var cur_recv_obj = '';
    							var error_message = '<?=JS_ERROR?>';
    							var error = false;
    							
    							if (ref != null) {	// restock particular server
    								answer = confirm('Are you sure to restock this product?');
    							} else {
    								answer = confirm('Are you sure to restock?');
    							}
    							if (answer !=0 ) {
    							<?	if (count($partial_inputs)) {
    									$extra_product_array = array();
    									foreach ($partial_inputs as $br_id => $qty_res) {
    										$max_qty = (int)$qty_res["balance"];
    										$recv_qty = (int)$qty_res["received"];
    										
    										if (!in_array($br_id, $extra_product_array)) {
    											$extra_product_array[] = $br_id;	?>
    											cur_recv_obj = DOMCall('extra_receive_' + '<?=$br_id?>');
    											if (cur_recv_obj != null) {
    												if (ref == null || ref == '<?=$br_id?>') {
    													if (trim_str(cur_recv_obj.value) != '') {
    														if (!validateInteger(cur_recv_obj.value)) {
    															invalid_receive_input = true;
    														} else {
    															var entered_recv_qty = parseInt(cur_recv_obj.value);
    															if (entered_recv_qty > 0) {
    															    <? if ($max_qty > 0) {?>
    															            extra_over_input = true;
    															    <? } else { ?>
    																        got_input = true;
    															    <? }?>
    															} else {
    																invalid_receive_input = true;
    															}
    														}
    													}
    												}
    											}
    							<?			} ?>
    									cur_obj = DOMCall('partial_receive_' + '<?=$br_id?>');
    									
    									if (cur_obj != null) {
    										if (ref == null || cur_obj.id.search('partial_receive_'+ref) != -1) {
    											var ref_id = replace(cur_obj.id, 'partial_receive_', '');
    											var qty_sign_obj = DOMCall('partial_receive_sign_' + ref_id);
    											
    											if (trim_str(cur_obj.value) != '' && qty_sign_obj != null) {
    												if (!validateInteger(cur_obj.value)) {
    													invalid_input = true;
    												} else {
    													var entered_qty = parseInt(qty_sign_obj.value + '' + cur_obj.value);
    													if (entered_qty >= 0) {
    													    if (entered_qty > <?=$max_qty?>) {
    															over_input = true;
    														} else {
    															got_input = true;
    														}
    													} else {
    														if (Math.abs(entered_qty) > <?=$recv_qty?>) {
    															over_deduct_input = true;
    														} else {
    															got_input = true;
    														}
    													}
    												}
    											}
    										}
    									}
    							<?	}
    							}
    							?>
    								if (document.getElementById('status').value > 0) {
    									error_message += '* Restock only available for buyback order in processing status.\nIt seems that you intend to update the buyback order status!' + "\n";
    									document.getElementById('status').focus();
    									error = true;
    								}
    								
    								if (invalid_input) {
    									error_message += '* Some input values for receive quantity is invalid!' + "\n";
    									error = true;
    								}
    								
    								if (over_input) {
    									error_message += '* Some input values for receive quantity is over the balance quantity!' + "\n";
    									error = true;
    								}
    								
    								if (extra_over_input) {
    									error_message += '* Some input values for extra receive quantity is over the balance quantity!' + "\n";
    									error = true;
    								}
    								
    								if (over_deduct_input) {
    									error_message += '* Some input values for deduct receive quantity exceed received quantity!' + "\n";
    									error = true;
    								}
    								
    								if (invalid_receive_input) {
    									error_message += '* Some input values for extra quantity is invalid!' + "\n";
    									error = true;
    								}
    								
    								if (!got_input && !error) {
    									error_message += '* There is no any integer value entered for receive quantity!' + "\n";
    									error = true;
    								}
    								
    								if (!error) {
    									if (ref != null) {
    										eval("document.partial_receive_form.ReceiveBtn_" + ref).value = 'Please wait...';
    									} else {
    										document.partial_receive_form.RestockBtn.value = 'Please wait...';
    									}
    									return true;
    								} else {
    									alert(error_message);
    									return false;
    								}
    							} else {
    								return false;
    							}
    						}
    						
    						function status_check(obj, total_received, fully_delivered) {
    							if (obj.value == 3) {
    								if (total_received > 0) {
    									if (!fully_delivered) {
    										answer = confirm('All the products for this buyback order are not fully received. Are you sure you want to change the status to \'Completed\'?');
    										if (answer == false) {
    											obj.selectedIndex=0;
    										}
    									}
    								} else {
    									alert("You cannot change the status of this buyback order to 'Completed' because no product is received.");
    									obj.selectedIndex=0;
    									return false;
    								}
    							}
    						}
    						
    						function submit_form(obj) {
    							obj.value = "Please wait...";
    							obj.disabled = true;
    							obj.form.submit();
    						}
    						
    						function setOrderComment(obj) {
    							obj.disabled = true;
    							
    							var ref_url = "orders_xmlhttp.php?action=get_comments&cmID="+obj.value;
    							
    							xmlhttp.open("GET", ref_url); 
    						    xmlhttp.onreadystatechange = function() { 
    						      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
    						      		var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
    						      		
    						      		document.getElementById('admin_comment').value = R
    						      		obj.disabled = false;
    						      		obj.selectedIndex = 0;
    						      	}
    						    }
    						    
    						    xmlhttp.send(null);
    							
    						   	setTimeout('hideMainInfo()', 3000);
    						}
    						
    						function confirmAction(mode, url_link) {
    						    if (mode == 1) {
    						        var confirmation = confirm('Are you sure want to show Restock Character ?');
    						    } else {
    						        var confirmation = confirm('Are you sure want to hide Restock Character ?');
    						    }
    						    
    						    if (confirmation == true) {
    						        window.location.href = url_link;
                				}
    						}
  							
  							function  deactive_auto_cancellation(brgid) {
  								var confirmation = confirm('Are you sure to deactive the auto cancellation ?');
  								if (confirmation == true) {
  									var ref_url = "buyback_xmlhttp.php?action=deactive_auto_cancellation&brgid="+brgid;
    							
	    							xmlhttp.open("GET", ref_url); 
	    						    xmlhttp.onreadystatechange = function() { 
	    						      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
	    						      		var result = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
	    						      		if (result == 'yes') {
	    						      			document.getElementById('auto_cancellation').innerHTML = '';	    						      			
	    						      		} else {
	    						      			document.getElementById('auto_cancellation').innerHTML = 'Cancel Failed';
	    						      		}
	    						      	}
	    						    }
	    						    
	    						    xmlhttp.send(null);
  								}
  							}
    					</script>
<?
    } else {
        echo '	    <tr>
    			        <td width="100%" class="main">You have no permissionto to view this Buyback Order <b>#'.$_REQUEST['buyback_request_group_id'].'</b>!</td>
    		        </tr>';
    }
} else {
    echo '	        <tr>
    			        <td width="100%" class="main">The Buyback Order <b>#'.$_REQUEST['buyback_request_group_id'].'</b> cannot be found!</td>
    		        </tr>';
}
?>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
<script><?=$js?></script>
</body>
</html>