<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();
$currencies->set_decimal_places(3);

define('PARTIAL_RECEIVE_STATUS', 1);

$request_group_id = (int)$_GET['buyback_request_group_id'];
$customer_notify = (int)$_POST['chk_customer_notify'];
$set_as_buyback_remarks = (int)$_POST['chk_set_as_buyback_remarks'];
$set_buyback_remark_id = (int)$_GET['set_buyback_remark_id'];
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<?
	function getStatusContents($isTop=false, $min=0, $isBottom = false) {
		$arrayStack = array();
		
		if ($isTop) {
			array_push($arrayStack,array("id" => "0", "text" => "All"));
		}
		
		if ($isBottom) {
			array_push($arrayStack,array("id" => "0", "text" => "Update Comment"));
		}
		
		$temp = array();
		$result = tep_db_query("select * from buyback_status where buyback_status_id >= $min;");
	
		while ($row = tep_db_fetch_array($result)) {	
			$temp['id'] 	= $row['buyback_status_id'];
			$temp['text']   = $row['buyback_status_name'];
			array_push($arrayStack,$temp);
			$temp = array();
		}
		
		return $arrayStack;
	}
	
	$filter = (int)$_GET['filter'];
	$generalStatusStack = getStatusContents(true);
	
	function getStatusName($id) {
		if($id==0)
			return '--';
		
		$result = tep_db_query("select * from buyback_status where buyback_status_id = $id;");
	
		if($row=tep_db_fetch_array($result))
			return $row['buyback_status_name'];
		else 
			return "Unknown";
	}
?>
	<table border="0" width="100%" cellspacing="2" cellpadding="2">	
  		<tr>
			<td valign="top">
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
									<td class="pageHeading" colspan="2"><?=HEADING_TITLE_BUYBACK_ORDERS?></td>
								</tr>
							</table>
						</td>
					</tr>
<?
if ($filter<=0) {
	$appendSQL = " order by brg.buyback_request_group_date desc ,brg.buyback_status_id;";
} else {
	$appendSQL = " and brg.buyback_status_id='$filter' order by brg.buyback_request_group_date desc, brg.buyback_status_id;";
}

$result_user_type = tep_db_query("SELECT buyback_request_group_user_type FROM ".TABLE_BUYBACK_REQUEST_GROUP." WHERE buyback_request_group_id='$request_group_id';");

if ($row_user_type = tep_db_fetch_array($result_user_type)) {
	$customer_type = (int)($row_user_type['buyback_request_group_user_type'])<=0 ? TYPE_CUSTOMER:TYPE_SUPPLIER;
}

if ($customer_type == TYPE_CUSTOMER) {
	$result_main = tep_db_query("select * from buyback_request_group as brg, customers as cu, customers_info as cu_info where cu_info.customers_info_id = cu.customers_id and cu.customers_id = brg.customers_id and buyback_request_group_id='$request_group_id' $appendSQL;");
	$row_main=tep_db_fetch_array($result_main);
	$customer_id = (int)$row_main['customers_id'];
	$prepend_string = "customers_";
	$customer_address_query = tep_db_query("select c.customers_firstname as firstname, c.customers_lastname  as lastname, c.customers_telephone as telephone, c.customers_email_address as email_address, ab.entry_company as company, ab.entry_street_address as street_address, ab.entry_suburb as suburb, ab.entry_postcode as postcode, ab.entry_city as city, ab.entry_zone_id as zone_id, z.zone_name as state, co.countries_id, co.countries_name as country , co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id as format_id, ab.entry_state from " . TABLE_CUSTOMERS . " c, " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) left join " . TABLE_COUNTRIES . " co on (ab.entry_country_id = co.countries_id) where c.customers_id = '" . (int)$customer_id . "' and ab.customers_id = '" . (int)$customer_id . "' and c.customers_default_address_id = ab.address_book_id");
	$row_address = tep_db_fetch_array($customer_address_query);  
	$user_address = tep_address_format($row_address['format_id'], $row_address, 1, '', '<br>');      			 
} else {
	$result_main = tep_db_query("select * from buyback_request_group as brg, supplier as s where 
								 s.supplier_id = brg.customers_id 
								 and buyback_request_group_id='$request_group_id' $appendSQL;");
	$row_main=tep_db_fetch_array($result_main);
	$customer_id = (int)$row_main['supplier_id'];
	$prepend_string = "supplier_";
	$user_address = tep_address_format($order->billing['format_id'], $order->billing, 1, '', '<br>');
}

$i=0;

$status_name = getStatusName($row_main['buyback_status_id']);

$result_total = tep_db_query("select sum(buyback_amount) as total from buyback_request where buyback_request_group_id='$row_main[buyback_request_group_id]';");
$row_total = tep_db_fetch_array($result_total);
$row_total['total'] = (double)$row_total['total'];

$result_payable_total = tep_db_query("select sum(buyback_quantity_received * (buyback_amount/buyback_request_quantity)) as total from buyback_request where buyback_request_group_id='$row_main[buyback_request_group_id]';");
$row_payable_total = tep_db_fetch_array($result_payable_total);
$row_payable_total['total'] = (double)$row_payable_total['total'];

$date_ordered = $row_main['buyback_request_group_date'];

if (strtoupper($row_main[$prepend_string.'gender'])=='M') {
	$customers_gender = "Male";
} else {
	$customers_gender = "Female";
}

$buyback_comment = trim($row_main['buyback_request_group_comment']);
?>	
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        						<tr>
            						<td valign="top" width="100%">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main" width="30%"><b><?=TEXT_BUYBACK_ORDER_NUMBER?></b></td>
												<td class="main"><b><?=$row_main['buyback_request_group_id']?></b></td>
											</tr>
											<tr>
												<td class="main"><b><?=TEXT_ORDER_DATE_TIME?></b></td>
												<td class="main"><?=$row_main['buyback_request_group_date']?></td>
											</tr>
											<tr>
												<td class="main"><b><?=TEXT_ORDER_STATUS?></b></td>
												<td>
													<table border="0" cellspacing="2" cellpadding="0">
														<tr>
															<td class="main" colspan="2"><?=$status_name?></td>															
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        			<tr>
        				<td><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
      				</tr>
      				<tr>
        				<td>
        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
          					     <tr>
            						<td><img src="images/pixel_black.gif" border="0" alt="" width="100%" height="1"></td>
          						</tr>	
          					</table>
          				</td>
          			</tr>
              		<tr>
            			<td valign="top" width="100%">
            				<table width="100%" border="0" cellspacing="0" cellpadding="2">
            					<tr>
            						<td valign="top" width="100%">
			            				<table width="100%" border="0" cellspacing="0" cellpadding="2">
			              					<tr>
			              						<td class="main" valign="top" width="30%"><b><?=$customer_type?>:</b></td>
                								<td class="main"><span class="blackIndicator"><?=$row_main[$prepend_string.'firstname'].' '.$row_main[$prepend_string.'lastname']?></class></td>
			              					</tr>

			            				</table>
			            			</td>
              					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
				    	<td><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
				  	</tr>
				  	<tr>
				  		<td>
				  	    	<table width="100%" border="0" cellspacing="1" cellpadding="2">
						  		<tr>					  
								  	<td class="ordersBoxHeading" width="30%"><?=TABLE_HEADING_PRODUCT_NAME?></td>
								  	<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_ACTUAL_QTY?></td>
								  	<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_QUANTITY?></td>
								  	<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED?></td>
<?						 		if ($row_main['buyback_status_id']==PARTIAL_RECEIVE_STATUS) { ?>
								  	<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_BALANCE?></td>
								  	
<?						  		} ?>
							  		<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_UNIT_PRICE?></td>	
							  		<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT?></td>
							  	</tr>
<?
$result = tep_db_query("select * from products_description as pd, buyback_request_group as brg, buyback_request as br, products as p where br.products_id = p.products_id  and p.products_id = pd.products_id and brg.buyback_request_group_id='$row_main[buyback_request_group_id]' and brg.buyback_request_group_id=br.buyback_request_group_id;");
$payable_total = 0;
$js = "";
$fully_received = true;
while ($row=tep_db_fetch_array($result)) {
	$href_link = tep_href_link(FILENAME_CATEGORIES,"pID=$row[products_id]&action=new_product");
	
	if (trim($row['products_location']) == "")
		$row['products_location'] = "---";
	
	$unit_price = 0;
	
	if ($row['buyback_request_quantity'] < 1)
		$row['buyback_request_quantity'] = 1;
	
	$unit_price	= $row['buyback_amount']/$row['buyback_request_quantity'];
	
	$amount = (double)$row['buyback_amount'];
	$actual_amount = $unit_price * $row['buyback_quantity_received'];
	$balance = $row['buyback_request_quantity'] - $row['buyback_quantity_received'];
	
	if ($balance > 0) {
		$fully_received = false;
	}
	
	if (($i%2)==0)
		$class = "ordersListingEven";
	else
		$class = "ordersListingOdd";
	
	$payable_total = $payable_total + $actual_amount;
?>
								<tr>					  
									<td class="ordersRecords" width="50%"><a href="<?=$href_link?>"><?=$row['products_cat_path']." > ".$row['products_name']?></a></td>
									<!--<td class="ordersRecords"><?=$row['products_location']?></td>-->
									<td class="ordersRecords" width="10%"><?=(int)$row['products_actual_quantity']?></td>
									<td class="ordersRecords" width="10%"><?=$row['buyback_request_quantity']?><?=tep_draw_hidden_field("request_group_id",(int)$row_main['buyback_request_group_id'])?></td>
									<td class="ordersRecords" width="10%"><?=(int)$row['buyback_quantity_received']?></td>
<?	if ($row_main['buyback_status_id']==PARTIAL_RECEIVE_STATUS) { ?>
									<td class="ordersRecords" width="10%"><?=(int)$balance?><?=tep_draw_hidden_field("balance[$row[buyback_request_id]]",(int)$balance,' id="balance_'.$row['buyback_request_id'].'" ')?><?=tep_draw_hidden_field("products[$row[buyback_request_id]]",(int)$row['products_id'])?></td>
<?	}
	
	$js .= "request_ids.push(".$row['buyback_request_id'].");"
?>
									<td class="ordersRecords"><?=$currencies->format($unit_price)?></td>
									<td class="ordersRecords" align="right"><?=$currencies->format($actual_amount)?></td>
									<!--<td class="ordersRecords" align="right"><?=$currencies->format($amount)?></td>-->
								</tr>
<?	++$i;
}

if ($row_main['buyback_status_id']==PARTIAL_RECEIVE_STATUS) {
?>						  
								<tr>
									<td class="ordersRecords" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
								</tr>
								<tr>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords" colspan="2" align="right"></td>
									<td class="ordersRecords" align="right"><u><?=$currencies->format($payable_total)?></u></td>
								</tr>
<?
} else {
?>
								<tr>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords"></td>
									<td class="ordersRecords" align="right"><u><?=$currencies->format($payable_total)?></u></td>
								</tr>
<?
}
?>
								<tr>
									<td class="ordersRecords" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
					 			</tr>
					 			<tr>
            						<td colspan="9"><img src="images/pixel_black.gif" border="0" alt="" width="100%" height="1"></td>
          						</tr>
				  	    	</table>
				  	    </td>
				  	</tr>
				  	<tr>
				  		<td class="main">
        					<table border="1" cellspacing="0" cellpadding="5">
          						<tr>
            						<td class="smallText" align="center"><b><?=TEXT_DATE_ADDED?></b></td>
            						<td class="smallText" align="center"><b><?=TEXT_CUSTOMER_NOTIFIED?></b></td>
            						<td class="smallText" align="center"><b><?=TEXT_STATUS?></b></td>
            						<td class="smallText" align="center"><b><?=TEXT_COMMENTS?></b></td>
            						<td class="smallText" align="center"><b><?=TEXT_CHANGED_BY?></b></td>
          						</tr>
<?
$result_comment = tep_db_query("SELECT * FROM ".TABLE_BUYBACK_STATUS_HISTORY." WHERE buyback_request_group_id=$request_group_id order by date_added;");
while ($row_comments = tep_db_fetch_array($result_comment)) {
	if ((int)$row_comments['set_as_buyback_remarks'] == 1) {
		$buybackRemarkSelectedRow = 'class="orderRemarkSelectedRow"';
		$action_str	= "&nbsp;";
	} else {
		$buybackRemarkSelectedRow = "";
		$action_str = '<a href="'.tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO,tep_get_all_get_params()."&set_buyback_remark_id=$row_comments[buyback_status_history_id]").'">'.TEXT_ACTION_SET_BUYBACK_REMARK.'</a>';
	}
	
	if ((int)$row_comments['customer_notified'] == 1) {
		$img_str = tep_image(DIR_WS_ICONS.'tick.gif');
	} else {
		$img_str = tep_image(DIR_WS_ICONS.'cross.gif');
	}
	
    if (trim($row_comments['changed_by'])=="")	$row_comments['changed_by'] = '&nbsp;';
	
	if (trim($row_comments['comments'])=="")	$row_comments['comments'] = "&nbsp;";
?>
          						<tr <?=$buybackRemarkSelectedRow?>>
            						<td class="smallText" align="center"><?=$row_comments['date_added']?></td>
            						<td class="smallText" align="center"><?=$img_str?></td>
            						<td class="smallText" align="center"><?=getStatusName($row_comments['buyback_status_id'])?></td>
            						<td class="smallText"><?=nl2br($row_comments['comments'])?></td>
            						<td class="smallText" align="center"><?=$row_comments['changed_by']?></td>
          						</tr>
<?
}

$result_big_status = tep_db_query("SELECT MAX(buyback_status_id) as maximum FROM ".TABLE_BUYBACK_STATUS_HISTORY." WHERE buyback_request_group_id=$request_group_id;");

if ($row_big_status = tep_db_fetch_array($result_big_status)) {
	$last_big_status_id = (int)$row_big_status['maximum']+1;
} else {
	$last_big_status_id = 0;
}
?>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</body>
</html>