<?

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
include_once(DIR_WS_CLASSES . 'c2c_buyback_order.php');
include_once(DIR_WS_CLASSES . 'edit_order.php');

define('PARTIAL_DELIVERY_STATUS', 2);
define('PARTIAL_RECEIVE_STATUS', 1);

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';
$subaction = isset($HTTP_GET_VARS['subaction']) ? $HTTP_GET_VARS['subaction'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';
$this_admin_email = isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : '';
$admin_email = isset($HTTP_GET_VARS['adm_email']) ? $HTTP_GET_VARS['adm_email'] : '';
$order_id = isset($HTTP_GET_VARS['oid']) ? (int) $HTTP_GET_VARS['oid'] : '';
$delivery_id = isset($HTTP_GET_VARS['delivery_id']) ? (int) $HTTP_GET_VARS['delivery_id'] : '';
$cancel_msg = isset($HTTP_GET_VARS['cancel_msg']) ? $HTTP_GET_VARS['cancel_msg'] : '';
$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$buyback_request_id = isset($HTTP_GET_VARS['buyback_request_id']) ? $HTTP_GET_VARS['buyback_request_id'] : '';
$element = isset($HTTP_GET_VARS['element']) ? $HTTP_GET_VARS['element'] : '';

$log_comment = isset($HTTP_GET_VARS['log_comment']) ? tep_db_prepare_input($HTTP_GET_VARS['log_comment']) : '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "buyback_xmlhttp.php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . "buyback_xmlhttp.php");
}

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

$admin_group_id = isset($_SESSION['login_groups_id']) ? $_SESSION['login_groups_id'] : '';

echo '<response>';

if (tep_not_null($action)) {
    switch ($action) {
        case "perform_tagging":
            $status_id = (int) $_GET['status_id'];
            $setting_value = $_GET['setting'];
            $order_ids_array = explode(',', $_GET['o_str']);
            $list_mode = (int) $_GET['list_mode'];
            $store = $_GET['store'];
            $oid = array();
            echo "<tag_info>";
            if ($subaction == 'nt') {
                if (tep_not_null($setting_value)) {
                    $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('" . $status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_BUYBACK_REQUESTS . "';";
                    $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                    if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                        $orders_tag_id = (int) $tag_verify_row["orders_tag_id"];
                    } else {
                        $insert_sql_data = array('orders_tag_name' => tep_db_prepare_input($setting_value),
                            'orders_tag_status_ids' => $status_id,
                            'filename' => FILENAME_BUYBACK_REQUESTS);
                        tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
                        $orders_tag_id = tep_db_insert_id();
                    }

                    // update all the selected orders with this tag
                    if ($store == 'G2G') {
                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids = IF (orders_tag_ids='', '" . $orders_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . $orders_tag_id . "')) WHERE c2c_buyback_id IN (" . implode(',', $order_ids_array) . ") AND NOT FIND_IN_SET('" . $orders_tag_id . "', orders_tag_ids)";
                    } else {
                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids = IF (orders_tag_ids='', '" . $orders_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . $orders_tag_id . "')) WHERE buyback_request_group_id IN (" . implode(',', $order_ids_array) . ") AND NOT FIND_IN_SET('" . $orders_tag_id . "', orders_tag_ids)";
                    }
                    tep_db_query($assign_orders_tag_update_sql);

                    if (!$store) {
                        generateTagString($order_ids_array);
                    }
                }
            } else if ($subaction == 'at') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_BUYBACK_REQUESTS . "' ;";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders with this tag
                    if ($store == 'G2G') {
                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $setting_value . "')) WHERE c2c_buyback_id IN (" . implode(',', $order_ids_array) . ") AND (NOT FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids) OR orders_tag_ids IS NULL)";
                    } else {
                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $setting_value . "')) WHERE buyback_request_group_id IN (" . implode(',', $order_ids_array) . ") AND NOT FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
                    }
                    tep_db_query($assign_orders_tag_update_sql);

                    if (!$store) {
                        generateTagString($order_ids_array);
                    }
                }
            } else if ($subaction == 'rt') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_BUYBACK_REQUESTS . "' ;";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders by removing this tag from them
                    if ($store == 'G2G') {
                        $unassign_orders_tag_select_sql = "SELECT c2c_buyback_id AS buyback_request_group_id, orders_tag_ids FROM " . TABLE_C2C_BUYBACK . " WHERE c2c_buyback_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
                    } else {
                        $unassign_orders_tag_select_sql = "SELECT buyback_request_group_id, orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
                    }
                    $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                    while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                        $TagRemovePattern = "/(,)?" . (int) $setting_value . "(,)?/is";
                        $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                        if (substr($new_tag_string, 0, 1) == ',')
                            $new_tag_string = substr($new_tag_string, 1);
                        if (substr($new_tag_string, -1) == ',')
                            $new_tag_string = substr($new_tag_string, 0, -1);

                        if ($store == 'G2G') {
                            tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids='" . $new_tag_string . "' WHERE c2c_buyback_id='" . $unassign_orders_tag_row["buyback_request_group_id"] . "'");
                            $oid[] = $unassign_orders_tag_row["buyback_request_group_id"];
                        } else {
                            tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . $unassign_orders_tag_row["buyback_request_group_id"] . "'");
                        }
                        $order_ids_array = explode(',', $new_tag_string);
                    }

                    if (!$store) {
                        generateTagString($order_ids_array);
                    }
                }
            } else if ($subaction == 'rd' || $subaction == 'ur') {
                if (tep_not_null($setting_value) && count($order_ids_array)) {
                    if ($store == 'G2G') {
                        $orders_read_mode_update_sql = "UPDATE " . TABLE_C2C_BUYBACK . " SET orders_read_mode = '" . $setting_value . "' WHERE c2c_buyback_id IN (" . implode(',', $order_ids_array) . "); ";
                    } else {
                        $orders_read_mode_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_read_mode = '" . $setting_value . "' WHERE buyback_request_group_id IN (" . implode(',', $order_ids_array) . "); ";
                    }
                    tep_db_query($orders_read_mode_update_sql);
                    generateReadModeString($order_ids_array);
                }
            }

            if ($store == 'G2G') {
                switch ($subaction) {
                    case 'at':
                    case 'nt':
                    case 'rd':
                    case 'ur':
                        foreach ($order_ids_array as $_num => $_id) {
                            echo c2c_buyback_order::_order_tag($_id, 'xml');
                        }
                        break;

                    case 'rt':
                        foreach ($oid as $_num => $_id) {
                            echo c2c_buyback_order::_order_tag($_id, 'xml');
                        }
                        break;
                }
            } else {
                generateTagSelectionOptions($status_id, $HTTP_GET_VARS['o_str'], ($list_mode == "2" ? true : false));
            }
            echo "</tag_info>";
            break;
        case 'get_restock_character':
            $restock_character = '';
            $show_restock_status = '';

            $restock_character_select_sql = "	SELECT br.restock_character, br.buyback_request_group_id, brg.show_restock
												FROM " . TABLE_BUYBACK_REQUEST . " AS br
												INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													ON (br.buyback_request_group_id = brg.buyback_request_group_id)
			 									WHERE br.buyback_request_id = '" . (int) $buyback_request_id . "'";
            $restock_character_result_sql = tep_db_query($restock_character_select_sql);
            if ($restock_character_row = tep_db_fetch_array($restock_character_result_sql)) {
                $restock_character = $restock_character_row['restock_character'];

                if (tep_not_null($restock_character)) {
                    if ((int) $restock_character_row['show_restock'] == 1) {
                        $show_restock_status = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif') . '&nbsp;<a href="javascript:confirmAction(0, \'' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'action=HideAllRestockCharacter&buyback_request_group_id=' . $restock_character_row['buyback_request_group_id']) . '\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif') . '</a>';
                    } else {
                        $show_restock_status = '<a href="javascript:confirmAction(1, \'' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'action=ShowSubmittedRestockCharacter&buyback_request_group_id=' . $restock_character_row['buyback_request_group_id']) . '\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif') . '</a>&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
                    }

                    $restock_character .= ' ' . $show_restock_status;
                }
            }

            echo '<restock_character><![CDATA[' . $restock_character . ']]></restock_character>';
        case 'show_buyback_lock_btn':
            echo tep_order_locking($order_id, $subaction, $log_comment);

            break;
        case "get_order_locking_history":
            $LockingCommentPattern = "/(?:##)(\S+)(?:##)(.*)/is";

            if ($element == 'locking_history_c2c')
                $filename = FILENAME_C2C_BUYBACK_ORDER;
            else
                $filename = FILENAME_BUYBACK_REQUESTS_INFO;

            $locking_history_select_sql = "	SELECT *
											FROM " . TABLE_ORDERS_LOG_TABLE . "
											WHERE orders_log_orders_id = '" . tep_db_input($order_id) . "'
												AND orders_log_filename ='" . tep_db_input($filename) . "'
											ORDER BY orders_log_time";
            $locking_history_result_sql = tep_db_query($locking_history_select_sql);

            $history_text = '<table border="1" cellspacing="0" cellpadding="5">
      							<tr>
            						<td class="smallText" align="center"><b>Action Date</b></td>
            						<td class="smallText" align="center"><b>Comments</b></td>
            						<td class="smallText" align="center"><b>Admin</b></td>
            						<td class="smallText" align="center"><b>IP</b></td>
          						</tr>';
            $history_count = 0;
            while ($locking_history_row = tep_db_fetch_array($locking_history_result_sql)) {
                $locking_comment_msg = '';
                if (preg_match($LockingCommentPattern, $locking_history_row["orders_log_system_messages"], $regs)) {
                    switch ($regs[1]) {
                        case "l_1":
                            $locking_comment_msg = "Locking order";
                            break;
                        case "ul_1":
                            $locking_comment_msg = "Unlocking order";
                            break;
                        case "ul_2":
                            $other_admin_email = $user_msg = '';
                            if (tep_not_null($regs[2])) {
                                list($other_admin_email, $user_msg) = explode(':~:', $regs[2]);
                            }

                            $locking_comment_msg = "Unlocking other people " . (tep_not_null($other_admin_email) ? "(" . $other_admin_email . ")" : '') . " order" . (tep_not_null($user_msg) ? '<br>' . $user_msg : '');
                            break;
                    }
                }

                if (is_numeric($locking_history_row["orders_log_admin_id"])) {
                    $admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $locking_history_row["orders_log_admin_id"] . "'");
                    if ($admin_info = tep_db_fetch_array($admin_query)) {
                        $admin_email_address = $admin_info["admin_email_address"];
                    } else {
                        $admin_email_address = $locking_history_row["orders_log_admin_id"];
                    }
                } else {
                    $admin_email_address = $locking_history_row["orders_log_admin_id"];
                }
                $history_text .= '	<tr>' . "\n" .
                        '		<td class="smallText" align="center">' . $locking_history_row["orders_log_time"] . '</td>' . "\n" .
                        '		<td class="smallText">' . $locking_comment_msg . '</td>' . "\n" .
                        ' 		<td class="smallText">' . $admin_email_address . '</td>' . "\n" .
                        '      <td class="smallText">' . $locking_history_row["orders_log_ip"] . '</td>' . "\n" .
                        '	</tr>' . "\n";
                $history_count++;
            }

            if ($history_count == 0) {
                $history_text .= '	<tr>' . "\n" .
                        '		<td class="smallText" colspan="4">No Order Locking History Available</td>' . "\n" .
                        '	</tr>' . "\n";
            }
            $history_text .= '</table>';
            echo "<res_code>1</res_code>";
            echo "<result><![CDATA[" . $history_text . "]]></result>";
            break;
        case "deactive_auto_cancellation":
            if (isset($_GET['brgid']) && tep_not_null($_GET['brgid'])) {
                $cancel_expiry_date_sql_data_array = array('buyback_request_group_expiry_date' => '0000-00-00 00:00:00');
                if (tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $cancel_expiry_date_sql_data_array, 'update', "buyback_request_group_id = '" . (int) $_GET['brgid'] . "'")) {
                    $buyback_history_data_array = array('buyback_request_group_id' => $_GET['brgid'],
                        'buyback_status_id' => '0',
                        'date_added' => 'now()',
                        'customer_notified' => '0',
                        'comments' => TEXT_LOG_MESSAGE_AUTO_CANCEL_OFF,
                        'set_as_buyback_remarks' => '1',
                        'changed_by' => $login_email_address
                    );
                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
                    echo "<result>yes</result>";
                } else {
                    echo "<result>no</result>";
                }
            }
            break;
        case "cancelDelivery":
            echo cancelDelivery($delivery_id,$cancel_msg);
        break;
            default:
            echo "<result>Unknown request!</result>";
            break;
    }
}

echo '</response>';

function generateTagSelectionOptions($status, $order_str, $whole_list = false, $apply_tag_sec_only = false) {
    global $languages_id;
    $order_ids_array = tep_not_null($order_str) ? explode(',', $order_str) : array();
    echo "<selection>";
    $order_status_id_select_sql = "SELECT buyback_status_id FROM " . TABLE_BUYBACK_STATUS . " WHERE " . (is_numeric($status) ? "buyback_status_id = '" . (int) $status . "'" : "buyback_status_name = '" . tep_db_input($status) . "'") . " AND language_id = '" . $languages_id . "';";
    $order_status_id_result_sql = tep_db_query($order_status_id_select_sql);
    if ($order_status_id_row = tep_db_fetch_array($order_status_id_result_sql)) {
        if (!$apply_tag_sec_only) {
            echo "<option index=''><![CDATA[Order Lists Options ...]]></option>";
            if ($whole_list == true) {
                echo "<option index='rd'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
                echo "<option index='ur'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
            } else {
                $orders_read_mode_select_sql = "SELECT DISTINCT orders_read_mode FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id IN (" . (count($order_ids_array) ? implode(',', $order_ids_array) : '') . "); ";
                $orders_read_mode_result_sql = tep_db_query($orders_read_mode_select_sql);
                if (tep_db_num_rows($orders_read_mode_result_sql) > 0) {
                    $show_read = $show_unread = false;
                    while ($orders_read_mode_row = tep_db_fetch_array($orders_read_mode_result_sql)) {
                        if ($orders_read_mode_row["orders_read_mode"] == 1) {
                            $show_unread = true;
                        } else if ($orders_read_mode_row["orders_read_mode"] == 0) {
                            $show_read = true;
                        }
                    }
                }
                if (!$show_read && !$show_unread) {
                    echo "<option index='rd' disabled='1'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
                    echo "<option index='ur' disabled='1'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
                } else {
                    if ($show_read)
                        echo "<option index='rd'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
                    if ($show_unread)
                        echo "<option index='ur'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
                }
            }
        }
        echo "<option index='' " . (!$apply_tag_sec_only ? "disabled='1'" : '') . "><![CDATA[Apply tag:]]></option>";

        $mirror_for_delete_tag_str = '';
        $order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $order_status_id_row["buyback_status_id"] . "', orders_tag_status_ids) AND filename = '" . FILENAME_BUYBACK_REQUESTS . "' ORDER BY orders_tag_name;";
        $order_tag_result_sql = tep_db_query($order_tag_select_sql);
        while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
            echo "<option index='" . 'otag_' . $order_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $order_tag_row["orders_tag_name"] . "]]></option>";
            if ($whole_list == true) {
                $mirror_for_delete_tag_str .= "<option index='" . 'rmtag_' . $order_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $order_tag_row["orders_tag_name"] . "]]></option>";
            }
        }

        if (!$apply_tag_sec_only) {
            echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";

            if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
                echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                echo $mirror_for_delete_tag_str;
            } else {
                // select the common tags among those selected orders
                if (count($order_ids_array)) {
                    $orders_tag_remove_select_sql = "SELECT DISTINCT otag.orders_tag_id, otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg WHERE brg.buyback_request_group_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET(otag.orders_tag_id, brg.orders_tag_ids) AND otag.filename='" . FILENAME_BUYBACK_REQUESTS . "' ORDER BY otag.orders_tag_name; ";
                    $orders_tag_remove_result_sql = tep_db_query($orders_tag_remove_select_sql);
                    if (tep_db_num_rows($orders_tag_remove_result_sql) > 0)
                        echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                    while ($orders_tag_remove_row = tep_db_fetch_array($orders_tag_remove_result_sql)) {
                        echo "<option index='" . 'rmtag_' . $orders_tag_remove_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $orders_tag_remove_row["orders_tag_name"] . "]]></option>";
                    }
                }
            }
        }
    }
    echo "</selection>";
}

function generateTagString($order_ids_array, $store = '') {
    echo "<tag_details>";
    for ($i = 0; $i < count($order_ids_array); $i++) {
        if ($store == 'G2G') {
            $orders_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_C2C_BUYBACK . " AS cb WHERE cb.c2c_buyback_id = '" . (int) $order_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, cb.orders_tag_ids) AND otag.filename='" . FILENAME_BUYBACK_REQUESTS . "';";
        } else {
            $orders_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg WHERE brg.buyback_request_group_id = '" . (int) $order_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, brg.orders_tag_ids) AND otag.filename='" . FILENAME_BUYBACK_REQUESTS . "';";
        }
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $tags_str = '';
        while ($orders_tag_row = tep_db_fetch_array($orders_tag_result_sql)) {
            $tags_str .= $orders_tag_row["orders_tag_name"] . ', ';
        }
        if (substr($tags_str, -2) == ', ')
            $tags_str = substr($tags_str, 0, -2);
        echo "<order_tags order_id='" . (int) $order_ids_array[$i] . "'><![CDATA[" . $tags_str . "]]></order_tags>";
    }
    echo "</tag_details>";
}

function generateReadModeString($order_ids_array) {
    echo "<read_mode>";
    for ($i = 0; $i < count($order_ids_array); $i++) {
        $orders_read_mode_select_sql = "SELECT orders_read_mode FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id = '" . (int) $order_ids_array[$i] . "' ;";
        $orders_read_mode_result_sql = tep_db_query($orders_read_mode_select_sql);
        while ($orders_read_mode_row = tep_db_fetch_array($orders_read_mode_result_sql)) {
            echo "<order_mode order_id='" . (int) $order_ids_array[$i] . "'><![CDATA[" . $orders_read_mode_row["orders_read_mode"] . "]]></order_mode>";
        }
    }
    echo "</read_mode>";
}

function tep_order_locking($buyback_request_group_id, $subaction, $log_comment = '') {
    $admin_group_id = isset($_SESSION['login_groups_id']) ? $_SESSION['login_groups_id'] : '';

    $log_object = new log_files($_SESSION['login_id']);
    $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);

    $result_str = '';

    if ($subaction == "ul" || $subaction == "ulo") { // unlocking
        $lock_orders_select_sql = "	SELECT l.locking_by, l.locking_from_ip, l.locking_datetime, a.admin_email_address
									FROM " . TABLE_LOCKING . " AS l
									LEFT JOIN " . TABLE_ADMIN . " AS a
										ON (l.locking_by = a.admin_id)
									WHERE l.locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "'
										AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
        $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
        if ($lock_orders_row = tep_db_fetch_array($lock_orders_result_sql)) {
            if ($lock_orders_row["locking_by"] == $_SESSION['login_id']) {
                $unlocking_orders_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
                tep_db_query($unlocking_orders_sql);

                $result_str .= "<result>This order has been successfully unlocked!</result>";
                $result_str .= "<action>Show Lock Button</action>";
                $result_str .= "<time>" . date("Y-m-d H:i:s") . "</time>";
                $result_str .= "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg>";

                $log_object->insert_orders_log($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_BUYBACK_REQUESTS_INFO);
            } else {
                $admin_group_to_contact = tep_admin_group_unlock_permission();

                if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                    $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                    $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                } else {
                    $contact_admin_group_msg = '';
                    $contact_admin_group_id_array = array();
                }

                if (in_array($admin_group_id, $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                    if ($subaction == "ulo") {
                        $unlocking_orders_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
                        tep_db_query($unlocking_orders_sql);

                        $result_str .= "<result>This order has been successfully unlocked!" . $log_comment . "</result>";
                        $result_str .= "<action>Show Lock Button</action>";
                        $result_str .= "<time>" . date("Y-m-d H:i:s") . "</time>";
                        $result_str .= "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg>";

                        $log_object->insert_orders_log($buyback_request_group_id, sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $log_comment), FILENAME_BUYBACK_REQUESTS_INFO);
                    } else {
                        $result_str .= "<result>This order is locked by someone else!</result>";
                        $result_str .= "<action>Show Unlock Button</action>";
                        $result_str .= "<subaction>Prompt For Unlocking Msg</subaction>";
                        $result_str .= "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["locking_datetime"], $lock_orders_row["locking_from_ip"]) . "]]></lock_msg>";
                    }
                } else {
                    $result_str .= "<result>Unlock order is failed!</result>";
                    $result_str .= "<action>Show Failed Lock Msg</action>";
                    $result_str .= "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["locking_datetime"], $lock_orders_row["locking_from_ip"], $contact_admin_group_msg) . "]]></lock_msg>";
                }
            }
        } else {
            $result_str .= "<result>You are not unlocking this order!</result>";
            $result_str .= "<action>Show Lock Button</action>";
            $result_str .= "<time>" . date("Y-m-d H:i:s") . "</time>";
            $result_str .= "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg>";
        }
    } else if ($subaction == "l") {  // locking
        $lock_orders_select_sql = "	SELECT l.locking_by, l.locking_from_ip, l.locking_datetime, a.admin_email_address
									FROM " . TABLE_LOCKING . " AS l
									LEFT JOIN " . TABLE_ADMIN . " AS a
										ON (l.locking_by = a.admin_id)
									WHERE l.locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "'
										AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
        $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
        if ($lock_orders_row = tep_db_fetch_array($lock_orders_result_sql)) { // this order currently is locked
            if ($lock_orders_row["locking_by"] == $_SESSION['login_id']) {
                $result_str .= "<result>You had been locking this order!</result>";
                //echo "<action>Show Unlock Button</action>";
                $result_str .= "<action>Prompt Alert Message</action>";
                $result_str .= "<close_win>1</close_win>";
                $result_str .= "<lock_msg><![CDATA[" . TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER . "]]></lock_msg>";
            } else {
                $admin_group_to_contact = tep_admin_group_unlock_permission();

                if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                    $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                    $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                } else {
                    $contact_admin_group_msg = '';
                    $contact_admin_group_id_array = array();
                }

                if (in_array($admin_group_id, $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                    $result_str .= "<result>Lock order is failed!</result>";
                    $result_str .= "<action>Show Unlock Button</action>";
                    $result_str .= "<subaction>Prompt For Unlocking Msg</subaction>";
                    $result_str .= "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["locking_datetime"], $lock_orders_row["locking_from_ip"]) . "]]></lock_msg>";
                } else {
                    $result_str .= "<result>Lock order is failed!</result>";
                    $result_str .= "<action>Show Failed Lock Msg</action>";
                    $result_str .= "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["locking_datetime"], $lock_orders_row["locking_from_ip"], $contact_admin_group_msg) . "]]></lock_msg>";
                }
            }
        } else {
            $buyback_status_id_select_sql = "	SELECT buyback_status_id
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . "
												WHERE buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "'";
            $buyback_status_id_result_sql = tep_db_query($buyback_status_id_select_sql);
            $buyback_status_id_row = tep_db_fetch_array($buyback_status_id_result_sql);

            if ($buyback_status_id_row["buyback_status_id"] != 1) {
                $result_str .= "<result>This order cannot be locked!</result>";
                $result_str .= "<action>Prompt Alert Message</action>";
                $result_str .= "<lock_msg><![CDATA[" . TEXT_LOCKED_ORDER_NOT_VALID_STATUS . "]]></lock_msg>";
            } else {
                $from_time = $HTTP_GET_VARS['from_time'];

                $check_within_locking_select_sql = "	SELECT orders_log_id
														FROM " . TABLE_ORDERS_LOG_TABLE . "
														WHERE orders_log_orders_id = '" . tep_db_input($buyback_request_group_id) . "'
															AND DATE_FORMAT(orders_log_time, '%Y-%m-%d %H:%i:%s') >= '" . tep_db_input($from_time) . "'
															AND orders_log_admin_id <> '" . tep_db_input($_SESSION['login_id']) . "'
															AND orders_log_filename = '" . tep_db_input(FILENAME_BUYBACK_REQUESTS) . "'";
                $check_within_locking_result_sql = tep_db_query($check_within_locking_select_sql);

                if (tep_db_num_rows($check_within_locking_result_sql) > 0) { // someone lock and unlock before you manage to lock it.
                    $result_str .= "<result>This order has been updated by someone!</result>";
                    $result_str .= "<action>Prompt Alert Message</action>";
                    $result_str .= "<lock_msg><![CDATA[" . TEXT_LOCKED_OUTDATED_ORDER . "]]></lock_msg>";
                } else {
                    $locking_data_array = array('locking_trans_id' => $buyback_request_group_id,
                        'locking_table_name' => TABLE_BUYBACK_REQUEST_GROUP,
                        'locking_by' => $_SESSION['login_id'],
                        'locking_from_ip' => getenv("REMOTE_ADDR"),
                        'locking_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_LOCKING, $locking_data_array);

                    $result_str .= "<result>This order has been successfully locked!</result>";
                    $result_str .= "<action>Show Unlock Button</action>";
                    $result_str .= "<lock_msg>" . sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $lock_orders_row["locking_datetime"], $lock_orders_row["locking_from_ip"]) . "</lock_msg>";

                    $log_object->insert_orders_log($buyback_request_group_id, ORDERS_LOG_LOCK_ORDER, FILENAME_BUYBACK_REQUESTS_INFO);
                }
            }
        }
    } else {
        $result_str .= "<result>Unknown request!</result>";
    }

    return $result_str;
}

function cancelDelivery($oph_id,$cancel_msg){
    global $messageStack;
    $admin_email = $_SESSION['login_email_address'];
    //select nececessary info
    $oph_sql = "SELECT oph.buyback_request_group_id, oph.received, cbp.c2c_buyback_product_id, oph.delivered_amount, oph.orders_id, oph.date_added, oph.orders_products_id,
     cbp.custom_products_type, cbp.orders_products_id, oph.orders_id,o.date_purchased, cbp.product_name, cbp.c2c_products_listing_id, cb.status as cb_status
    FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph
    INNER JOIN ". TABLE_C2C_BUYBACK_PRODUCT . " as cbp ON oph.buyback_request_group_id = cbp.c2c_buyback_id	
    INNER JOIN ". TABLE_C2C_BUYBACK . " as cb ON cb.c2c_buyback_id = cbp.c2c_buyback_id	
    INNER JOIN ". TABLE_ORDERS . " as o ON o.orders_id = oph.orders_id
    WHERE oph.orders_products_history_id = ".$oph_id;

    $oph_result = tep_db_query($oph_sql);
    if ($oph_row = tep_db_fetch_array($oph_result)) { 
    //updated delivery
        if($oph_row['received'] != 2){

            if($oph_row['cb_status'] == 2){
                //move so to delivering
                $m_attr = array('status' => 1);
                tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id= '" . (int) $oph_row['buyback_request_group_id'] . "'");
                $m_attr = array(
                    'c2c_buyback_id' => $oph_row['buyback_request_group_id'],
                    'c2c_buyback_product_id' => $oph_row['c2c_buyback_product_id'],
                    'status' => 1,
                    'date_added' => 'now()',
                    'seller_notified' => 0,
                    'comments' => '',
                    'set_as_remarks' => 0,
                    'changed_by' => $_SESSION['login_email_address']
                );
                tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');
            }
    
            tep_db_query("UPDATE " . TABLE_ORDERS_PRODUCTS_HISTORY . " SET received = 2, last_updated = '".date('Y-m-d H:i:s')."', changed_by = '". $admin_email . "'  WHERE orders_products_history_id =".$oph_id);
            $cbh_data = [
                'c2c_buyback_id'=>$oph_row['buyback_request_group_id'],
                'c2c_buyback_product_id'=>$oph_row['c2c_buyback_product_id'],
                'status'=>0,
                'date_added'=>date('Y-m-d H:i:s'),
                'seller_notified'=>0,                
                'comments'=>sprintf('The following items have been canceled: <br> %s x -%s<br>Item was previously delivered on %s',$oph_row['product_name'],$oph_row['delivered_amount'],$oph_row['date_added']),
                'set_as_remarks'=>$oph_row['set_as_remarks'], 
                'orders_products_history_id'=>$oph_id,
                'changed_by'=>$admin_email
                
            ];
           
            tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_data, 'insert');
            //save remark for admin PO Crew
            //revert delivered quantity
            // tep_db_query("UPDATE " . TABLE_ORDERS_PRODUCTS. " SET 
            // products_delivered_quantity = products_delivered_quantity - " .$oph_row['delivered_amount'] . ", 
            // products_good_delivered_quantity = products_good_delivered_quantity - ".$oph_row['delivered_amount'] . " 
            // WHERE orders_products_id = ".$oph_row['orders_products_id']);
    
            //revert buyback quantity
            tep_db_query("UPDATE " . TABLE_ORDERS_PRODUCTS_HISTORY . " SET seller_comment = '". tep_db_input($cancel_msg) . "', received = 2, changed_by = '". $admin_email . "'  WHERE orders_products_history_id =". tep_db_input($oph_id));
            tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_PRODUCT . " SET delivered_quantity = delivered_quantity -". $oph_row['delivered_amount'] . "  WHERE c2c_buyback_product_id =".$oph_row['c2c_buyback_product_id']);

            //insert sales activities
            $edit_order_obj = new edit_order($_SESSION['login_id'], $_SESSION['login_email_address'], $oph_row['orders_id']);
            $edit_order_obj->buyback_deliver_order(abs($oph_row['delivered_amount']), $oph_row['buyback_request_group_id'], $oph_row['orders_products_id'], $_SESSION['login_email_address'], true, $messageStack, '-');
            $comment_array = array(	
                'orders_id' => $oph_row['orders_id'],
                'orders_status_id' => '0',
                'date_added' => 'now()',
                'customer_notified' => '0',
                'comments' => 'The following items have been canceled: <br>'.$oph_row['product_name'].' x -'.  $oph_row['delivered_amount'].'<br>Item was previously delivered on '.$oph_row['date_added'],
                'comments_type' => '0',
                'set_as_order_remarks' => '0',
                'changed_by' => $admin_email
            );
            tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
            //revert listing quantity
            $_cpl_sql = "   SELECT c2c_products_listing_id
                                            FROM " . TABLE_C2C_PRODUCTS_LISTING . "
                                            WHERE c2c_products_listing_id = " . $oph_row['c2c_products_listing_id'];
            $_cpl_res = tep_db_query($_cpl_sql);
            if (tep_db_num_rows($_cpl_res)) {
                c2c_order::stockMovement($oph_row['c2c_products_listing_id'], 3, '+', $oph_row['delivered_amount'], $oph_row['orders_id'], 'c2c_buyback_order');
            }
        $oph_sql = "SELECT count(oph.orders_products_history_id) as  delivery_count FROM " . TABLE_ORDERS_PRODUCTS_HISTORY ." as oph
        WHERE oph.orders_id = ".$oph_row['orders_id'] . ' AND oph.received = 0';
       
        $oph_result = tep_db_query($oph_sql);
        if ($oph_no_deliveries = tep_db_fetch_array($oph_result)) {
            if($oph_no_deliveries['delivery_count'] == 0){
                //remove SO tag
                $so_tag_id = G2G_SO_NO_RECEIVE_TAG_ID;
                // update all the selected orders by removing this tag from them
                $unassign_orders_tag_select_sql = "SELECT c2c_buyback_id, orders_tag_ids FROM " . TABLE_C2C_BUYBACK . " WHERE c2c_buyback_id = ". $oph_row['buyback_request_group_id'] ." AND FIND_IN_SET('" . (int) $so_tag_id . "', orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $so_tag_id . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids='" . $new_tag_string . "' WHERE c2c_buyback_id='" . $oph_row["buyback_request_group_id"] . "'");
                }
                
                //remove bo tag
                $bo_tag_id = G2G_BO_NO_RECEIVE_TAG_ID;
                // update all the selected orders by removing this tag from them
                $unassign_orders_tag_select_sql = "SELECT orders_id, orders_tag_ids FROM " . TABLE_ORDERS . " WHERE orders_id = ". $oph_row['orders_id'] ." AND FIND_IN_SET('" . (int) $bo_tag_id . "', orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $bo_tag_id . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . $oph_row["orders_id"] . "'");
                }


            }
        }
            

     }
        return '<result>Delivery!</result>';

    }
}
?>