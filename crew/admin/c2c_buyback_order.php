<?php
/*
  $Id: c2c_buyback_order.php,v 1.7 2014/04/18 02:48:54 wenbin.ng Exp $

  Developer: Ching Yen
 */

die();
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_CATEGORY);
include_once(DIR_WS_CLASSES . FILENAME_CURRENCIES);
include_once(DIR_WS_CLASSES . FILENAME_C2C_BUYBACK_ORDER);
include_once(DIR_WS_CLASSES . 'store_point.php');
include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOM_PRODUCT);

$id = (tep_not_null($_REQUEST['game_id']) ? $_REQUEST['game_id'] : (tep_not_null($_GET['id']) ? $_GET['id'] : ''));
$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subact = (tep_not_null($_REQUEST['subact']) ? $_REQUEST['subact'] : '');

$func = new c2c_buyback_order();

switch ($action) {
    case "add_form":
        if (tep_not_null($id)) {
            $form_content = $func->addForm($id);
        } else {
            $form_content = $func->searchCriteria();
        }
        break;

    case "update":
        if (tep_not_null($id)) {
            $func->addEntry($id, $subact);
            tep_redirect(tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=add_form&id=' . $id));
        } else {
            $form_content = $func->searchResult();
        }
        break;

    case "search_result":
        $form_content = $func->searchResult();
        break;

    default:
        $form_content = $func->searchCriteria();
        break;
}
?>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/buyback.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>

        <script language="javascript">
            <!--
        function showOverEffect(object, class_name, extra_row) {
                rowOverEffect(object, class_name);
                var rowObjArray = extra_row.split('##');
                for (var i = 0; i < rowObjArray.length; i++) {
                    if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                        rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
                    }
                }
            }

            function showOutEffect(object, class_name, extra_row) {
                rowOutEffect(object, class_name);
                var rowObjArray = extra_row.split('##');
                for (var i = 0; i < rowObjArray.length; i++) {
                    if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                        rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
                    }
                }
            }

            function showClicked(object, class_name, extra_row) {
                rowClicked(object, class_name);
                if (extra_row == "")
                    return;
                var rowObjArray = extra_row.split('##');
                for (var i = 0; i < rowObjArray.length; i++) {
                    rowClicked(document.getElementById(rowObjArray[i]), class_name);
                }
            }

            function confirmBatchAction(frmObj, frmStatus, checkName) {
                if (trim_str(frmObj.batch_update_char_mode.value) == '') {
                    alert('Please select your batch action!');
                    return false;
                } else {
                    if (document.getElementById('batch_update_char_mode').value == 'HideAllRestockCharacter') {
                        var confirmation = confirm('Confirm to proceed with batch update ?');

                        if (confirmation == false) {
                            return false;
                        }
                    } else if (document.getElementById('batch_update_char_mode').value == 'ShowSubmittedRestockCharacter') {
                        return true;
                    }
                }
            }

            function orderCancellation() { 
                confirm_box_content = "Customer is requesting order cancellation and awaiting Seller response."; 
                jquery_confirm_box(confirm_box_content, 1, 0, 'Notice'); 
                     
            } 
            function status_form_submit(btnObj) {
                var selected_status = jQuery("#f_status").val();
                var current_status = jQuery("#current_status").val();
                if (selected_status == "4") {
                    var confirm_box_content = 'Is this cancellation due to Seller\'s fault?  <br /><input type="radio" value="1" name="radio_success_rate_flag"> YES <input type="radio" value="0" checked="checked" name="radio_success_rate_flag"> NO <br />';
                    jquery_confirm_box(confirm_box_content, 2, 0, 'Confirm');
                    jQuery("#jconfirm_submit").removeAttr('onClick');
                    jQuery('#jconfirm_submit').click(function() {
                        var checked_flag_value = jQuery("input[name='radio_success_rate_flag']:checked").val();

                        if (!checked_flag_value) {
                            alert('Is this cancellation due to Seller\'s fault?');
                        } else {
                            var count_second = 7;
                            jQuery('#jconfirm_cancel').unbind("click");
                            var checked_flag_text = 'YES';
                            if (checked_flag_value == "0") {
                                checked_flag_text = 'NO';
                            }
                            confirm_box_content = "Is this cancellation due to Seller fault? <br /> " + checked_flag_text;
                            jquery_confirm_box(confirm_box_content, 2, 0, 'Confirm');
                            interval_id = setInterval("process_count_down_button()", 1000);

                            process_count_down_button = function() {
                                jQuery("#jconfirm_submit").attr('onClick', 'void(0);')
                                jQuery("#jconfirm_submit").css('color', 'grey');
                                jQuery("#jconfirm_cancel").attr('onClick', "clearTimeout(interval_id);jQuery.unblockUI();return false;")
                                jQuery("#jconfirm_cancel").css('background-color', '#F49BB2')
                                        .css('color', '#FF0000')
                                        .css('border-color', '#100F37');
                                if (jQuery("#jconfirm_submit").length) {
                                    jQuery("#jconfirm_submit").text(count_second);
                                    if (count_second < 1) {
                                        clearTimeout(interval_id);
                                        jQuery("#success_rate_flag").val(checked_flag_value);
                                        btnObj.form.submit();
                                    } else {
                                        count_second--;
                                    }
                                } else {
                                    clearTimeout(interval_id);
                                }
                            }
                        }
                    });
                } else if (current_status == "3" && selected_status != "0") {
                    var confirm_box_content = 'Reminder:<br />1. Inform ANB to manual deduct Seller Credit if already credited. <br />';
                    jquery_confirm_box(confirm_box_content, 2, 0, 'Confirm rollback from completed?');
                    jQuery("#jconfirm_submit").removeAttr('onClick');
                    jQuery('#jconfirm_submit').click(function() {
                        var count_second = 15;
                        jQuery('#jconfirm_cancel').unbind("click");
                        interval_id = setInterval("process_count_down_button()", 1000);

                        process_count_down_button = function() {
                            jQuery("#jconfirm_submit").attr('onClick', 'void(0);')
                            jQuery("#jconfirm_submit").css('color', 'grey');
                            jQuery("#jconfirm_cancel").attr('onClick', "clearTimeout(interval_id);jQuery.unblockUI();return false;")
                            jQuery("#jconfirm_cancel").css('background-color', '#F49BB2')
                                    .css('color', '#FF0000')
                                    .css('border-color', '#100F37');
                            if (jQuery("#jconfirm_submit").length) {
                                jQuery("#jconfirm_submit").text(count_second);
                                if (count_second < 1) {
                                    clearTimeout(interval_id);
                                    btnObj.form.submit();
                                } else {
                                    count_second--;
                                }
                            } else {
                                clearTimeout(interval_id);
                            }
                        }
                        
                    });
                }
                else {
                    btnObj.form.submit();
                }
            }

            function change_seller_fault (current_fault, url) {
                    if (current_fault == 1) {
                        checked_flag_text = 'NO';
                    } else {
                        checked_flag_text ='YES';
                    }
                    confirm_box_content = "Change seller fault to " + checked_flag_text;     
                    jquery_confirm_box(confirm_box_content, 2, 0, 'Confirm');
                    jQuery("#jconfirm_submit").removeAttr('onClick');
                    jQuery('#jconfirm_submit').click(function() {
                        jQuery("#jconfirm_submit").attr('onClick', 'void(0);')

                            jQuery('#jconfirm_cancel').unbind("click");
                            jQuery("#jconfirm_submit").unbind('click')

                            count_second = 10
                            interval_id = setInterval("process_count_down_button()", 1000);

                            process_count_down_button = function() {
                                jQuery("#jconfirm_submit").attr('onClick', 'void(0);')
                                jQuery("#jconfirm_submit").css('color', 'grey');
                                jQuery("#jconfirm_cancel").attr('onClick', "clearTimeout(interval_id);jQuery.unblockUI();return false;")
                                jQuery("#jconfirm_cancel").css('background-color', '#F49BB2')
                                        .css('color', '#FF0000')
                                        .css('border-color', '#100F37');
                                if (jQuery("#jconfirm_submit").length) {
                                    jQuery("#jconfirm_submit").text(count_second);
                                    if (count_second < 1) {
                                        clearTimeout(interval_id);
                                        window.location.href = url;
                                    } else {
                                        count_second--;
                                    }
                                } else {
                                    clearTimeout(interval_id);
                                }
                            }
                        })
                    };
            var pageLoaded = false;
            function init() {
                // quit if this function has already been called
                if (arguments.callee.done)
                    return;

                // flag this function so we don't do the same thing twice
                arguments.callee.done = true;
                initInfoCaptions();
                pageLoaded = true;	// Control when a javascript event in this page can be triggered

                if(jQuery("#hiddenCancellation").text() == 1){ 
                    orderCancellation(); 
                } 
            }

            /* for Mozilla */
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", init, null);
            }

            /* for other browsers */
            window.onload = init;
            //-->
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" leftmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <?php include_once(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php include_once(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%" valign="top"><?php echo $form_content; ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- footer //-->
        <?php include_once(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <?php include_once(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
    </body>
</html>