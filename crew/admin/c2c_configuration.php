<?php 
/*
  	$Id: c2c_configuration.php,v 1.2 2013/06/07 12:27:27 weichen Exp $
	
	Developer: Ching Yen
*/
die();
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_C2C_CONFIGURATION);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');

$func = new c2c_config();

switch ($action) {
	case "add":
		$func->addEntry($_POST);
		tep_redirect(tep_href_link(FILENAME_C2C_CONFIGURATION));
		exit;
		break;
		
	default:
		$header_title = '';
		$form_content = $func->menuListing();
		break;
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php include_once(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php include_once(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?php echo $form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<?php include_once(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<?php include_once(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
