<?php 
/*
  	$Id: c2c_delivery_speed.php,v 1.2 2013/06/07 12:27:27 weichen Exp $
	
	Developer: Ching Yen
*/
die();
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_C2C_DELIVERY_SPEED);
include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOM_PRODUCT);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (tep_not_null($_REQUEST['cpt']) ? $_REQUEST['cpt'] : (tep_not_null($_GET['id']) ? $_GET['id'] : ''));
$hidden_id = (tep_not_null($_POST['hidden_cpt']) ? $_POST['hidden_cpt'] : '');

$func = new c2c_delivery_speed_config();

switch ($action) {
	case "add_form":
		if (tep_not_null($id)) {
			$form_content = $func->addForm($id);
		} else {
			$form_content = $func->menuListing();
		}
		break;
		
	case "add":
		if (tep_not_null($id)) {
			if ($id == $hidden_id) {
				$func->addEntry($id);
			}
			
			tep_redirect(tep_href_link(FILENAME_C2C_DELIVERY_SPEED, 'action=add_form&id=' . $id));
			exit;
		} else {
			$form_content = $func->menuListing();
		}
		break;
		
	default:
		if (tep_not_null($id)) {
			tep_redirect(tep_href_link(FILENAME_C2C_DELIVERY_SPEED), 'action=add_form&id=' . $id);
		} else {
			$form_content = $func->menuListing();
		}
		break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
