<?php
die();
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_C2C_RATING_TYPE);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$cptc = '1';

$func = new c2c_rating_type();

switch ($action) {
    case "saveForm":
        $rating_id = isset($_POST['rating_id']) ? $_POST['rating_id'] : '0';
        $rating_name_en = isset($_POST['rating_name_en']) ? $_POST['rating_name_en'] : '';
        $rating_name_cnZh = isset($_POST['rating_name_cnZh']) ? $_POST['rating_name_cnZh'] : '';
        $rating_name_cnTw = isset($_POST['rating_name_cnTw']) ? $_POST['rating_name_cnTw'] : '';
        $rating_name_in = isset($_POST['rating_name_in']) ? $_POST['rating_name_in'] : '';
        $cptc = isset($_POST['cptc']) ? $_POST['cptc'] : '';
        $type = isset($_POST['type']) ? $_POST['type'] : '';
        $display = isset($_POST['display']) ? $_POST['display'] : '';
        $sort_order = isset($_POST['sort_order']) ? $_POST['sort_order'] : '';
        
        $cpt = $func->getCpt($cptc);
        $dataArr = array('custom_product_type'=>$cpt,
                        'custom_products_type_child_id'=>$cptc,
                        'rating_type'=>$type,
                        'display_status'=>$display,
                        'sort_order'=>$sort_order            
                         );
        
        $descDataArr = array('1'=>$rating_name_en,
                            '2'=>$rating_name_cnZh,
                            '3'=>$rating_name_cnTw,
                            '4'=>$rating_name_in
                            );        
        
        if($rating_id == '0'){ // save new record
            if($type == 2){
                $cptc_select_sql = "SELECT c2c_rating_type_id FROM " . TABLE_C2C_RATING_TYPE ." 
                                WHERE custom_products_type_child_id = '".$cptc."' AND rating_type ='".$type."'";
                $cptc_result_sql = tep_db_query($cptc_select_sql);
                $dataRow = tep_db_num_rows($cptc_result_sql);
                if($dataRow == 0){
                    $func->save($dataArr, $descDataArr);
                }else{
                    $messageStack->add_session(ERROR_DUPLICATE_SELLER_LEVEL_RATING_TYPE, 'error');
                }
            }else{
                $func->save($dataArr, $descDataArr);
            }
        }else{
            $func->update($dataArr, $descDataArr, $rating_id);
        }        
        tep_redirect(tep_href_link(FILENAME_C2C_RATING_TYPE,'cptc='.$cptc));
    break;
    case "addForm":
        $cptc = isset($_GET['cptc']) ? $_GET['cptc'] : $cptc;
        $type = isset($_GET['type']) ? $_GET['type'] : 'add';
        $ratingId = isset($_GET['rating_id']) ? $_GET['rating_id'] : '0';
        $form_content = $func->addForm($ratingId, $type, $cptc);      
    break;    
    case "updateStatus":
        $rating_id = isset($_GET['rating_id']) ? $_GET['rating_id'] : 0;
        $flag = isset($_GET['flag']) ? $_GET['flag'] : 0;
        
        if($rating_id > 0){
            $sql_data_array = array('display_status' => $flag);
            tep_db_perform(TABLE_C2C_RATING_TYPE, $sql_data_array, 'update', " c2c_rating_type_id = '" . (int)$rating_id . "'");
        }
        
        $form_content = $func->ratingList($cptc);
    break;
    case "delete":
        $cptc = isset($_GET['cptc']) ? $_GET['cptc'] : '0';
        echo $func->delete($cptc);
        exit;
    break;
    default:
        if(isset($_POST['cptc'])){
            $cptc = $_POST['cptc'];
        }else if($_GET['cptc']){
            $cptc = $_GET['cptc'];
        }
        
        $form_content = $func->ratingList($cptc);
    break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/php.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>