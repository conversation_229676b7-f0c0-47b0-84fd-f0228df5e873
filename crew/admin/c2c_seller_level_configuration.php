<?php
die();
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_C2C_SELLER_LEVEL_CONFIGURATION);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$cptc = 1;
$func = new c2c_seller_level_configuration();

switch ($action) {
    case "saveForm":
        $data = array();
        $cptc = isset($_POST['cptc']) ? $_POST['cptc'] : '';

        foreach ($_POST as $key => $val) {
            if (preg_match("/^from/", $key)) {
                $str = explode("_", $key);
                $cnt = $str[1];
                if (isset($_POST["from_" . $cnt]) && isset($_POST["to_" . $cnt]) && isset($_POST["order_" . $cnt]) &&
                        !empty($_POST["from_" . $cnt]) && !empty($_POST["to_" . $cnt]) && !empty($_POST["order_" . $cnt]) &&
                        ($_POST["from_" . $cnt] != $_POST["to_" . $cnt])) {
                    if (!isset($data[$_POST["from_" . $cnt]])) {
                        $data[$_POST["from_" . $cnt]] = array(
                            "min_level" => $_POST["from_" . $cnt],
                            "max_level" => $_POST["to_" . $cnt],
                            "orders_required" => $_POST["order_" . $cnt]
                        );
                    }
                }
            }
        }

        if ($data) {
            sort($data);
            $func->save($data, $cptc);
        }

        tep_redirect(tep_href_link(FILENAME_C2C_SELLER_LEVEL_CONFIGURATION, 'cptc=' . $cptc));
        break;

    case "addForm":
        $cptc = isset($_GET['cptc']) ? $_GET['cptc'] : $cptc;
        $form_content = $func->addForm($cptc);
        break;

    case "delete":
        $cptc = isset($_GET['cptc']) ? $_GET['cptc'] : 0;
        echo $func->delete($cptc);
        exit;

        break;

    default:
        if (isset($_POST['cptc'])) {
            $cptc = $_POST['cptc'];
        } else if ($_GET['cptc']) {
            $cptc = $_GET['cptc'];
        }
        $form_content = $func->levelConfig($cptc);
        break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="JavaScript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/php.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%" valign="top"><?= $form_content ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
    </body>
</html>