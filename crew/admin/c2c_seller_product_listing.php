<?php
die();
/*
  	$Id: c2c_seller_product_listing.php,v 1.4 2014/02/27 04:23:42 wenbin.ng Exp $
	
	Developer: <PERSON> Yen
*/

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_C2C_SELLER_PRODUCT_LISTING);
include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOM_PRODUCT);

include_once(DIR_WS_CLASSES . FILENAME_CATEGORY);
include_once(DIR_WS_CLASSES . FILENAME_CURRENCIES);
include_once(DIR_WS_CLASSES . FILENAME_LOG);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (tep_not_null($_REQUEST['id']) ? $_REQUEST['id'] : '');

$func = new c2c_seller_product_listing();

switch ($action) {
	case "add_form":
		$form_content = $func->addForm($id);
		break;
		
	case "add":
		$func->addEntry($id);
		tep_redirect(tep_href_link(FILENAME_C2C_SELLER_PRODUCT_LISTING, 'action=add_form&id=' . (int)$id));
		exit;
		break;
		
	case "search_result":
		$form_content = $func->searchResult();
		break;
		
	case "get_seller_detail":
		if ( !isset($_SERVER['HTTP_REFERER']) || (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE) ) {
			echo "You are not allowed to access from outside";
			exit;
		} else {
			$data = array();
			
			if (tep_not_null($id)) {
				$data = $func->_get_customer_detail($id);
			}
			
			echo json_encode($data);
		}
		exit();
		break;
		
	case "get_product_listing_remark":
		if ( !isset($_SERVER['HTTP_REFERER']) || (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE) ) {
			echo "You are not allowed to access from outside";
			exit;
		} else {
			$data = array();
			
			if (tep_not_null($id)) {
				$data = $func->_get_product_listing_remark($id);
			}
			
			echo json_encode($data);
		}
		exit();
		break;
	case "get_reserved_qty_listing":
		if ( !isset($_SERVER['HTTP_REFERER']) || (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE) ) {
			echo "You are not allowed to access from outside";
			exit;
		} else {
			$data = array();
			
			if (tep_not_null($id)) {
				$data = $func->_get_reserved_qty_listing($id);
			}
			
			echo json_encode($data);
		}
		exit();
		break;
		
	default:
		$form_content = $func->menuListing();
		break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/php.js"></script>
	<script language="javascript" src="includes/javascript/diff_match_patch.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
				</table>
			</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td width="100%" valign="top"><?php echo $form_content; ?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>