<?
/*
  	$Id: cart_comments.php,v 1.5 2006/03/16 09:18:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$price_tags_field_array = array	(	"products_name" => "Product Name",
									"products_price" => "Product Price"
								);

$field_type_array = array 	(	array	('id'=>'1', 'text'=>'Text Box'),
								array	('id'=>'2', 'text'=>'Text Area'),
								array	('id'=>'3', 'text'=>'Select Box')
							);
							
$field_type_array1 = array 	(	array	('id'=>'1', 'text'=>'Shopping Cart Comments'),
								array	('id'=>'2', 'text'=>'Customer Registration Comments'),
							);
							

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_comments":
		case "update_comments":
			$error = false;
			switch ($_REQUEST["input_type"]) {
				case "1" : // text box
					$input_size = $_REQUEST["hidden_box_size"] . ',' . $_REQUEST["hidden_box_text_max_len"];
					$option_values = 'NULL';
					break;
				case "2" :	// text area
					$input_size = $_REQUEST["hidden_box_row"] . ',' . $_REQUEST["hidden_box_col"];
					$option_values = 'NULL';
					break;
				case "3" :	// selection box
					$input_size = 'NULL';
					$option_array = explode("\r\n", $_REQUEST["selection"]);
					$option_array = array_filter($option_array, "filter_empty_val");
					$option_values = tep_db_prepare_input(implode(':~:', $option_array));
					break;
			}
			
			if(gettype($option_array)=="array"){
				$check_duplicate = array_unique($option_array);
				if (sizeof($check_duplicate) != sizeof($option_array)){
					$error = true;
				}
			}
			
			if ($error == false) {
				if ($_REQUEST['hidden_statistics']=="clear") {
   	      			 tep_db_query("DELETE FROM " . TABLE_USER_COMMENTS . " WHERE user_comments_id = '" . (int)$_REQUEST["commentID"] . "'");
   	     	    } else if ($_REQUEST['hidden_statistics']=="keep") {
   	        		$new_info_title_exist=0;
   	        		$old_info_title_exist=0;
   	        	
					$comments_statistic_selection_box_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " where cart_comments_id = '" . (int)$_REQUEST["commentID"] ."'";
					$comments_statistic_selection_box_result_sql = tep_db_query($comments_statistic_selection_box_sql);
					$comments_statistic_selection_box_row = tep_db_fetch_array($comments_statistic_selection_box_result_sql);

					if($comments_statistic_selection_box_row['cart_comments_option_title']=="1"){
						$old_info_title_exist=1;
					}
					if($_REQUEST["hidden_option_title"]=="1"){
						$new_info_title_exist=1;
					}

					if (tep_not_null($comments_statistic_selection_box_row["cart_comments_options"])) {
						$option_list = explode(':~:', $comments_statistic_selection_box_row["cart_comments_options"]);
					
						$option_list_delete = $option_list;
						$option_list_delete = $option_list_delete;
						$option_list_remain = $option_array;
					
						for($count=0; $count<sizeof($option_list_remain); $count++){
							$option_list_remain[$count] = "'" . addslashes($option_list_remain[$count]) . "'";
						}
					
						for($count=0; $count<sizeof($option_list_delete); $count++){
							$option_list_delete[$count] = "'" . addslashes($option_list_delete[$count]) . "'";
						}
									
						if((sizeof($option_array)-$new_info_title_exist) < (sizeof($option_list)-$old_info_title_exist)){
							$size_chosen = (sizeof($option_array)-$new_info_title_exist);
						} else if((sizeof($option_array)-$new_info_title_exist) > (sizeof($option_list)-$old_info_title_exist)){
							$size_chosen = (sizeof($option_list)-$old_info_title_exist);
						} else {
							$size_chosen = (sizeof($option_array)-$new_info_title_exist);;	
						}
					
						$token = "##UPDATE##" . time() . '_';
						
						for($count=0; $count<$size_chosen; $count++){
							$comments_status_update_sql = "UPDATE " . TABLE_USER_COMMENTS . " SET user_comments = '" . $token . tep_db_input($option_array[($count+$new_info_title_exist)]) . "' WHERE user_comments_id='" . (int)$_REQUEST["commentID"] . "' and user_comments='" . tep_db_input($option_list[($count+$old_info_title_exist)]). "'";
							tep_db_query($comments_status_update_sql);
						}
						
						$option_first_delete = implode(',', $option_list_delete);
						tep_db_query("DELETE FROM " . TABLE_USER_COMMENTS . " WHERE user_comments_id = '" . (int)$_REQUEST["commentID"] . "' and user_comments in (" . $option_first_delete . ")");
						
						$comments_status_update_sql = "UPDATE " . TABLE_USER_COMMENTS . " SET user_comments = REPLACE(user_comments,'". $token ."', '') WHERE user_comments_id='" . (int)$_REQUEST["commentID"] . "'";
						tep_db_query($comments_status_update_sql);
						
						$option_second_delete = implode(',', $option_list_remain);
						tep_db_query("DELETE FROM " . TABLE_USER_COMMENTS . " WHERE user_comments_id = '" . (int)$_REQUEST["commentID"] . "' and user_comments not in (" . $option_second_delete . ")");
   	        		}
   	   			}
					
				$comments_sql_data_array = array(	'cart_comments_title' => tep_db_prepare_input($_REQUEST["title"]),
        		           							'cart_comments_input_type' => tep_db_prepare_input($_REQUEST["input_type"]),
        	 	          							'cart_comments_input_size' => $input_size,
        	 	          							'cart_comments_options' => $option_values,
        	 	          							'cart_comments_option_title' => $_REQUEST["hidden_option_title"],
        	  	         							'cart_comments_required' => $_REQUEST["mandatory_comment"],
        	  	         							'cart_comments_status' => $_REQUEST["comment_status"],
        	  	         							'cart_comments_sort_order' => $_REQUEST["comment_order"],
        	    	       							'cart_comments_type' => $_REQUEST["input_type1"]
            	    	       						);
   	        
	            if ($action == "insert_comments") {
					tep_db_perform(TABLE_CART_COMMENTS, $comments_sql_data_array);
				} else {
					tep_db_perform(TABLE_CART_COMMENTS, $comments_sql_data_array, 'update', ' cart_comments_id="'.(int)$_REQUEST["commentID"].'"');
				}
				tep_redirect(tep_href_link(FILENAME_SHOPPING_CART_COMMENTS));
				break;
			} else {
				$messageStack->add_session("Some option values is duplicated. Please change accordingly.", 'warning');
				tep_redirect(tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, "action=edit_comments&commentID=".$_REQUEST["commentID"]));
				break;	
			}	
		case "set_status":
			if ( ($_REQUEST["status_flag"] == '0') || ($_REQUEST["status_flag"] == '1') ) {
				$comments_status_update_sql = "UPDATE " . TABLE_CART_COMMENTS . " SET cart_comments_status=" . $_REQUEST["status_flag"] . " WHERE cart_comments_id='" . (int)$_REQUEST["commentID"] . "'";
				tep_db_query($comments_status_update_sql);
			}
			tep_redirect(tep_href_link(FILENAME_SHOPPING_CART_COMMENTS));
			break;
		case "set_required":
			if ( ($_REQUEST["required_flag"] == '0') || ($_REQUEST["required_flag"] == '1') ) {
				$comments_required_update_sql = "UPDATE " . TABLE_CART_COMMENTS . " SET cart_comments_required=" . $_REQUEST["required_flag"] . " WHERE cart_comments_id='" . (int)$_REQUEST["commentID"] . "'";
				tep_db_query($comments_required_update_sql);
			}
			tep_redirect(tep_href_link(FILENAME_SHOPPING_CART_COMMENTS));
			break;
		case "delete_comments":
			if (tep_not_null($_REQUEST["commentID"])) {
				$comments_delete_sql = "DELETE FROM " . TABLE_CART_COMMENTS . " WHERE cart_comments_id='" . (int)$_REQUEST["commentID"] . "'";
				tep_db_query($comments_delete_sql);
				$comments_delete_statistics_sql = "DELETE FROM " . TABLE_USER_COMMENTS . " WHERE user_comments_id='" . (int)$_REQUEST["commentID"] . "'";
				tep_db_query($comments_delete_statistics_sql);
			}
			tep_redirect(tep_href_link(FILENAME_SHOPPING_CART_COMMENTS));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript"><!--
		var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4))
		
		function centerWin() {
			var NS = false;
			if (document.all) {
			   /* the following is only available after onLoad */
			   w = document.body.clientWidth;
			   h = document.body.clientHeight;
			   NS = true;
			} else if (document.layers) {
			  	;
			}
			
	     	if (!NS) {
	     		self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
	     	} else {
	     		self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
	    	}
	    }
	    
		// Close the dialog
		function closeme() {
			window.close()
		}
		
		// Handle click of OK button
		function handleOK() {
			if (opener && !opener.closed) {
				opener.dialogWin.returnFunc();
			} else {
				alert("You have closed the main window.\n\nNo action will be taken on the choices in this dialog box.")
			}
			closeme();
			return false;
		}
		//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->

	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        					<?if($action!=="statistic"){?>
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            				
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            						<?
            							if ($action != "new_comments")
            								echo '[ <a href="'.tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=new_comments').'" >'.LINK_ADD_CART_COMMENT.'</a> ]';
            							else
            								echo "&nbsp;";
            						?>
            						</td>
            					</tr>
            					<?} else if($action=="statistic") {?>
            					<tr>
            						<td>
            							<td class="pageHeading" valign="top"><?=HEADING_STATISTIC_TITLE?></td>
            						</td>
            					</tr>
            					<?}?>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?

if ($action == "new_comments" || $action == "edit_comments") {
	if ($_REQUEST["commentID"]) {
		$cart_comment_select_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " WHERE cart_comments_id='" . $_REQUEST["commentID"] . "'";
		$cart_comment_result_sql = tep_db_query($cart_comment_select_sql);
		$cart_comment_row = tep_db_fetch_array($cart_comment_result_sql);
	}
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('cart_comments', FILENAME_SHOPPING_CART_COMMENTS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_comments" ? 'insert_comments' : 'update_comments'), 'post', 'onSubmit="return cart_comments_form_checking()"');
	echo tep_draw_hidden_field("commentID", $_REQUEST["commentID"]);
?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_COMMENT_TITLE?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('title', $cart_comment_row["cart_comments_title"], 'size="40" id="title"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
								
									<td class="main" valign="top"><?=ENTRY_COMMENTS_TYPE?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("input_type1", $field_type_array1, $cart_comment_row["cart_comments_type"], 'id="input_type1" );"')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      					<td class="main" valign="top"><?=ENTRY_COMMENT_FIELD_TYPE?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("input_type", $field_type_array, $cart_comment_row["cart_comments_input_type"], ' id="input_type" onChange="Todo(\'TypeFile\', \'ForeignKeyDisplayField\', \'div13309\', value);"')?>
									</td>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_COMMENT_SETTING?></td>
			        				<td class="main">
			        				<div id="div13309"></div>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_COMMENT_STATUS?></td>
			        				<td class="main">
			        					<?=tep_draw_radio_field('comment_status', '1', $cart_comment_row["cart_comments_status"]=="1" ? true : false) . "&nbsp;" . TEXT_STATUS_ACTIVE . "&nbsp;" . tep_draw_radio_field('comment_status', '0', $cart_comment_row["cart_comments_status"]=="1" ? false : true) . "&nbsp;" . TEXT_STATUS_INACTIVE?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_COMMENT_MANDATORY?></td>
			        				<td class="main">
			        					<?=tep_draw_radio_field('mandatory_comment', '1', $cart_comment_row["cart_comments_required"]=="1" ? true : false) . "&nbsp;" . 'Yes' . "&nbsp;" . tep_draw_radio_field('mandatory_comment', '0', $cart_comment_row["cart_comments_required"]=="1" ? false : true) . "&nbsp;" . 'No'?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_COMMENT_ORDER?></td>
			        				<td class="main">
			        					<?=tep_draw_input_field('comment_order', $cart_comment_row["cart_comments_sort_order"] ? $cart_comment_row["cart_comments_sort_order"] : 50000, 'size="6" id="comment_order"')?>
			        				</td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_comments" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
									
									
									
									
									<script>
									function get_user_msg() {
										var user_msg = prompt("Leave any message for this update?","");
											if (user_msg != null) {
												document.getElementById('user_comment').value = user_msg;
												return true;
											} else {
												return false;
											}
									}
														
									function keep()	{
										document.cart_comments.hidden_statistics.value="keep";
										document.cart_comments.submit();
									}
									
									function cleared(){
										document.cart_comments.hidden_statistics.value="clear";
										document.cart_comments.submit();
									}
									</script>											
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<?=tep_draw_hidden_field("hidden_box_size", '', ' id="hidden_box_size" ')?>
								<?=tep_draw_hidden_field("hidden_box_text_max_len", '', ' id="hidden_box_text_max_len" ')?>
		        				<?=tep_draw_hidden_field("hidden_box_row", '', ' id="hidden_box_row" ')?>
		        				<?=tep_draw_hidden_field("hidden_box_col", '', ' id="hidden_box_col" ')?>
		        				<?=tep_draw_hidden_field("hidden_selection", '', ' id="hidden_selection" ')?>
		        				<?=tep_draw_hidden_field("hidden_option_title", '', ' id="hidden_option_title" ')?>
		        				<?=tep_draw_hidden_field("hidden_statistics", '', ' id="hidden_statistic" ')?>
							</table>
							</form>
        				</td>
        			</tr>
        		<script>
        		<!--
	        	<?
	        		if (tep_not_null($cart_comment_row["cart_comments_input_size"])) {
	        			list($first_val, $second_val) = explode(',', $cart_comment_row["cart_comments_input_size"]);
	        	?>
	        			var first_box_val = '<?=$first_val?>';
	        			var second_box_val = '<?=$second_val?>';
	        	<?	} else { ?>
	        			var first_box_val = '';
	        			var second_box_val = '';
	        	<?	}
	        		
	        		if (tep_not_null($cart_comment_row["cart_comments_options"])) {
	        			$selection_array = explode(':~:', addslashes($cart_comment_row["cart_comments_options"]));
	        	?>
	        			var selection_option_str = "<?=implode('\r\n', $selection_array)?>";
	        	<?	} else { ?>
	        			var selection_option_str = "";
	        	<?	} 
	        		
	        		if ($cart_comment_row["cart_comments_option_title"]) {
	        	?>		var is_title = 1;
	        	<?	} else { ?>
	        			var is_title = 0;
	        	<?	} ?>
	        		
					function Todo(name1, name2, name, val) {
						var out = '';
						switch(val) {
							case "1":
								out = getTextBoxParam();
								break;
							case "2":
								out = getTextAreaParam();
								break;
							case "3":
								out = getSelectBoxParam();
								break;
							
						}
						
						document.getElementById(name).innerHTML = out;
					}
					
					function getTextBoxParam() {
						return 	'Size : <input name="box_size" id="box_size" type="text" value="'+first_box_val+'" size=5>&nbsp;&nbsp;Maximum Character: <input name="box_text_max_len" id="box_text_max_len" type="text" value="'+second_box_val+'" size=5>';
					}
					
					function getTextAreaParam() {
						return 	'Row: <input name="box_row" id="box_row" type="text" value="'+first_box_val+'" size=5>&nbsp;&nbsp;Column: <input name="box_col" id="box_col" type="text" value="'+second_box_val+'" size=5>';
					}
					
					function getSelectBoxParam() {
						return '<table><tr><td valign="top" class="main">Option values:</td><td><textarea name="selection" id="selection" style=width:370px;height:100px>'+selection_option_str+'</textarea></td></tr>'+
									'<tr><td>&nbsp;</td><td class="main">Each option values must be in a new line.</td></tr>'+
									'<tr><td colspan="2" class="main">First option is just a title?&nbsp;<input type="radio" name="option_title" value="1" '+(is_title ? 'CHECKED' : '')+'>&nbsp;Yes&nbsp;<input type="radio" name="option_title" value="0" ' +(is_title ? '' : 'CHECKED')+'>&nbsp;No</td></tr></table>';
					}
					
					var cur_sel = document.getElementById('input_type').selectedIndex + 1;
					
					Todo('', '', 'div13309', cur_sel.toString(10));
					
					function cart_comments_form_checking() {
<?						if ($action=="edit_comments"){
						
						$form_str = '<html>
										<head>
									    	<title>Clear or Keep Statistics</title>
											<script language="JavaScript">
												var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4));
												function centerWin() {
													var NS = false;
													if (document.all) {
														w = document.body.clientWidth;
														h = document.body.clientHeight;
														NS = true;
													} else if (document.layers) {
										  				;
													}
								     				if (!NS) {
								     					self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
								     				} else {
								     					self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
								    				}
								    			}
												
												function closeme() {
													window.close()
												}
												
												function handleOK() {
													opener.keep();
													closeme();
												}
												
												function handle_clear(){
													opener.cleared();
													closeme();	
												}
											</script>
										</head>
									    <body onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
									    	<form>'
									    			.'<table border="0" cellspacing="0" cellpadding="0" align="center">'
									    				."<tr>
									    					<td>
									   							Modify the selection type may make the statistics gathered inaccurate.<br>
									    						Click <b>Reset</b> to clear the statistics, <b>Keep</b> to keep the statistics.
														    </td>
									    				</tr>
									    				<tr>"
									    					.'<td align="center">'
									   						.tep_button('Reset','','','onclick="handle_clear()"','','')
									    					."&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
									   						.tep_button('Keep','','','onclick="handleOK()"','','')
									    					."&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
									    					.tep_button('Cancel','','','onclick="closeme()"','','')
									    					."</td>
									    				</tr>
									   				</table>
									    		</form>
									    	</body>
									    </html>";
						?>
						
						if (document.getElementById('title').value == "") {
								alert('Please enter the title for this comment!');
								document.getElementById('title').focus();
								return false;
							}
						
							var FieldType = document.getElementById('input_type').value;
							switch(FieldType) {
								case "1":
									if (document.getElementById('box_size').value == "") {
										alert('Please enter the display size of text box!');
										document.getElementById('box_size').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_size').value)) {
										alert('Size value must be an integer!');
										document.getElementById('box_size').focus();
										document.getElementById('box_size').select();
										return false;
									}
								
									if (document.getElementById('box_text_max_len').value != "") {
										if (!validateInteger(document.getElementById('box_text_max_len').value)) {
											alert('Maximum length value must be an integer!');
											document.getElementById('box_text_max_len').focus();
											document.getElementById('box_text_max_len').select();
											return false;
										}
									}
										break;
								case "2":
									if (document.getElementById('box_row').value == "") {
										alert('Please enter the number of row!');
										document.getElementById('box_row').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_row').value)) {
										alert('Row number must be an integer!');
										document.getElementById('box_row').focus();
										document.getElementById('box_row').select();
										return false;
									}
								
									if (document.getElementById('box_col').value == "") {
										alert('Please enter the number of column!');
										document.getElementById('box_col').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_col').value)) {
										alert('Column number must be an integer!');
										document.getElementById('box_col').focus();
										document.getElementById('box_col').select();
										return false;
									}
									break;
								case "3":
									if (document.getElementById('selection').value == "") {
										alert('Selection list is empty!');
										document.getElementById('selection').focus();
										return false;
									}
									break;
							}
						
							if (document.getElementById('comment_order').value == "") {
								alert('Please enter the comment sort order!');
								document.getElementById('comment_order').focus();
								return false;
							} else if (!validateInteger(document.getElementById('comment_order').value)) {
								alert('Invalid comment sort order value!');
								document.getElementById('comment_order').focus();
								document.getElementById('comment_order').select();
								return false;
							}
						
							document.getElementById('hidden_box_size').value = FieldType=="1" ? document.getElementById('box_size').value : '';
							document.getElementById('hidden_box_text_max_len').value = FieldType=="1" ? document.getElementById('box_text_max_len').value : '';
							document.getElementById('hidden_box_row').value = FieldType=="2" ? document.getElementById('box_row').value : '';
							document.getElementById('hidden_box_col').value = FieldType=="2" ? document.getElementById('box_col').value : '';
							document.getElementById('hidden_selection').value = FieldType=="3" ? document.getElementById('selection').value : '';
							
							var duplicate = false;
							
							if (FieldType=="3") {
								
								var options_insert = document.getElementById('selection').value.split("\n");
								var check = new Array();
								for(var a=0; a < options_insert.length; a++){
									if(options_insert[a]!=''){
										if(check[options_insert[a]]==null){
											check[options_insert[a]]=1;
										} else {
											duplicate = true;
											break;
										}
									}								
								}
								
								if(duplicate == true){
									alert('Some option values is duplicated. Please change accordingly.');
									return false;
								}
								
								var is_title_obj = document.getElementsByName('option_title');
								var is_title_opt = is_title_obj.length;
								for(var i=0; i<is_title_opt; i++) {
									if (is_title_obj[i].checked) {
										document.getElementById('hidden_option_title').value = is_title_obj[i].value;
										break;
									}
								}
							}
						
<?						$cart_comment_select_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " WHERE cart_comments_id='" . $_REQUEST["commentID"] . "'";
						$cart_comment_result_sql = tep_db_query($cart_comment_select_sql);
						$cart_comment_row = tep_db_fetch_array($cart_comment_result_sql);						
?>						
						
						if(FieldType=="3"){
<?							if($cart_comment_row['cart_comments_input_type'] == "3") { ?>
								openDGDialog('javascript:document.write("<?=str_replace("\r\n", '', addslashes(addslashes($form_str)))?>"); document.close();', 500, 110,'', '');
<?								} 
?>						} 

						if(FieldType!= <?=$cart_comment_row['cart_comments_input_type']?>){
							input_box=confirm("The statistics will be reset, by changing the type field. Click OK to continue or Cancel to abort.");
							
							if(input_box==true){
								document.cart_comments.hidden_statistics.value="clear";
								document.cart_comments.submit();
							}
						}
						
						if (FieldType!="3" && FieldType == <?=$cart_comment_row['cart_comments_input_type']?>){
							document.cart_comments.submit();
						}						
						return false;
						
<?						} else if ($action=="new_comments") { ?>
							
							if (document.getElementById('title').value == "") {
								alert('Please enter the title for this comment!');
								document.getElementById('title').focus();
								return false;
							}
						
							var FieldType = document.getElementById('input_type').value;
							switch(FieldType) {
								case "1":
									if (document.getElementById('box_size').value == "") {
										alert('Please enter the display size of text box!');
										document.getElementById('box_size').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_size').value)) {
										alert('Size value must be an integer!');
										document.getElementById('box_size').focus();
										document.getElementById('box_size').select();
										return false;
									}
								
									if (document.getElementById('box_text_max_len').value != "") {
										if (!validateInteger(document.getElementById('box_text_max_len').value)) {
											alert('Maximum length value must be an integer!');
											document.getElementById('box_text_max_len').focus();
											document.getElementById('box_text_max_len').select();
											return false;
										}
									}
										break;
								case "2":
									if (document.getElementById('box_row').value == "") {
										alert('Please enter the number of row!');
										document.getElementById('box_row').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_row').value)) {
										alert('Row number must be an integer!');
										document.getElementById('box_row').focus();
										document.getElementById('box_row').select();
										return false;
									}
								
									if (document.getElementById('box_col').value == "") {
										alert('Please enter the number of column!');
										document.getElementById('box_col').focus();
										return false;
									} else if (!validateInteger(document.getElementById('box_col').value)) {
										alert('Column number must be an integer!');
										document.getElementById('box_col').focus();
										document.getElementById('box_col').select();
										return false;
									}
									break;
								case "3":
									if (document.getElementById('selection').value == "") {
										alert('Selection list is empty!');
										document.getElementById('selection').focus();
										return false;
									}
									break;
							}
						
							if (document.getElementById('comment_order').value == "") {
								alert('Please enter the comment sort order!');
								document.getElementById('comment_order').focus();
								return false;
							} else if (!validateInteger(document.getElementById('comment_order').value)) {
								alert('Invalid comment sort order value!');
								document.getElementById('comment_order').focus();
								document.getElementById('comment_order').select();
								return false;
							}
						
							document.getElementById('hidden_box_size').value = FieldType=="1" ? document.getElementById('box_size').value : '';
							document.getElementById('hidden_box_text_max_len').value = FieldType=="1" ? document.getElementById('box_text_max_len').value : '';
							document.getElementById('hidden_box_row').value = FieldType=="2" ? document.getElementById('box_row').value : '';
							document.getElementById('hidden_box_col').value = FieldType=="2" ? document.getElementById('box_col').value : '';
							document.getElementById('hidden_selection').value = FieldType=="3" ? document.getElementById('selection').value : '';
						
							var duplicate = false;
						
							if (FieldType=="3") {
								var options_insert = document.getElementById('selection').value.split("\n");
								var check = new Array();
								for(var a=0; a < options_insert.length; a++){
									if(options_insert[a]!=''){
										if(check[options_insert[a]]==null){
											check[options_insert[a]]=1;
										} else {
											duplicate = true;
											break;
										}
									}								
								}
								
								if(duplicate == true){
									alert('Some option values is duplicated. Please change accordingly.');
									return false;
								}
								
								var is_title_obj = document.getElementsByName('option_title');
								var is_title_opt = is_title_obj.length;
								for(var i=0; i<is_title_opt; i++) {
									if (is_title_obj[i].checked) {
										document.getElementById('hidden_option_title').value = is_title_obj[i].value;
										break;
									}
								}
							}
						
							return true;
<?						} ?>
					}
					//-->
				</script>
<?
}

else if($action == "statistic"){
	$row_count = 0;

	$id = $_REQUEST["commentID"];
	$type = $_REQUEST["comments_type"];
	
	$cart_comment_select_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " WHERE cart_comments_id='" . tep_db_input($id) . "'";
	$cart_comment_result_sql = tep_db_query($cart_comment_select_sql);
	$cart_comment_row = tep_db_fetch_array($cart_comment_result_sql);
	
	$comments_statistic_all_sql = "SELECT * FROM " . TABLE_USER_COMMENTS . " where user_comments_id = '" . tep_db_input($id) ."'";
	$comments_statistic_all_result_sql = tep_db_query($comments_statistic_all_sql);
	
	$total = tep_db_num_rows($comments_statistic_all_result_sql);
?>
	<table border="0" width="52%" cellspacing="1" cellpadding="2">

		<tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
		
		<tr><td class="main" colspan="2">
			<table width="100%">
				<tr><td class="main" width="50%"><b><?=ENTRY_COMMENT_TITLE?></b></td>
				<td class="main"><?=$cart_comment_row['cart_comments_title']?></td></tr>
				<tr><td class="main"><b><?=TABLE_HEADING_CART_COMMENTS_INPUT_TYPE?></b></td>
				<td class="main">
				<?
					foreach($field_type_array as $res) {
						if ($res["id"] == $cart_comment_row["cart_comments_input_type"]) {
							echo $res["text"];
						}
					}
				?>				
				</td></tr>
				<tr><td class="main"><b><?=TABLE_HEADING_CART_COMMENTS_TYPE?></b></td>
					<td class="main""><?
						if ($cart_comment_row['cart_comments_type'] == '1') {
							echo "Shopping Cart Comments";
						} else {
							echo "Customer Registration Comments";
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main"><b><?=TABLE_HEADING_CART_COMMENTS_STATUS?><b></td>
					<td class="main">
						<?
						if ($cart_comment_row['cart_comments_status'] == '1') {
							echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
						} else {
							echo tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main"><b><?=TABLE_HEADING_CART_COMMENTS_REQUIRED?></b></td>
					<td class="main">
						<?
						if ($cart_comment_row['cart_comments_required'] == '1') {
							echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
						} else {
							echo tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main"><b><?=TABLE_HEADING_CART_COMMENTS_SORT_ORDER?></b></td>
					<td class="main">
						<?=$cart_comment_row["cart_comments_sort_order"]?>
					</td>
				</tr>
			</table>
		</td></tr>
		
		<tr><td colspan="2""><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td></tr>
		<tr><td colspan="2">
			<table border="0" width="100%" cellspacing="1" cellpadding="2">
			
<?
	echo '<tr class="reportListingEven"><td class="reportBoxHeading">'.TABLE_HEADING_CART_COMMENTS_STATISTIC_OPTION.'</td><td class="reportBoxHeading">' . TABLE_HEADING_CART_COMMENTS_STATISTIC_COUNT . '</td></tr>';
	
	switch ($type){
		case "1":
		case "2":
			
		$comments_statistic_empty_sql = "SELECT * FROM " . TABLE_USER_COMMENTS . " where user_comments_id = '" . tep_db_input($id) ."' AND user_comments = ''";
		$comments_statistic_empty_result_sql = tep_db_query($comments_statistic_empty_sql);
	
		$comments_statistic_notempty_sql = "SELECT * FROM " . TABLE_USER_COMMENTS . " where user_comments_id = '" . tep_db_input($id) ."' AND user_comments != ''";
		$comments_statistic_notempty_result_sql = tep_db_query($comments_statistic_notempty_sql);
		
		echo '<tr class="reportListingOdd"><td class="reportRecords" valign="top">' . ENTRY_COMMENTS_STATISTIC_EMPTY . '</td><td class="reportRecords" valign="top">' . tep_db_num_rows($comments_statistic_empty_result_sql) . '&nbsp;(' . number_format(tep_get_percentage($total, tep_db_num_rows($comments_statistic_empty_result_sql)),2, '.', '').'%)</td></tr>';
		echo '<tr class="reportListingEven"><td class="reportRecords" valign="top">' . ENTRY_COMMENTS_STATISTIC_NOTEMPTY . '</td><td class="reportRecords" valign="top">' . tep_db_num_rows($comments_statistic_notempty_result_sql) . '&nbsp;(' . number_format(tep_get_percentage($total, tep_db_num_rows($comments_statistic_notempty_result_sql)),2, '.', '').'%)</td></tr>';
		break;
		
		case "3":
		$comments_statistic_selection_box_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " where cart_comments_id = '" . tep_db_input($id) ."'";
		$comments_statistic_selection_box_result_sql = tep_db_query($comments_statistic_selection_box_sql);
		$comments_statistic_selection_box_row = tep_db_fetch_array($comments_statistic_selection_box_result_sql);

		if (tep_not_null($comments_statistic_selection_box_row["cart_comments_options"])) {
			$option_list = explode(':~:', $comments_statistic_selection_box_row["cart_comments_options"]);
				
			foreach ($option_list as $val) {
				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
				$comments_statistic_selected_option_sql = "SELECT * FROM " . TABLE_USER_COMMENTS . " where user_comments_id = '" . tep_db_input($id) ."' AND user_comments = '".tep_db_input($val)."'";
				$comments_statistic_selected_option_result_sql = tep_db_query($comments_statistic_selected_option_sql);
				?>	
				<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
					<td class="reportRecords" valign="top" width="50%"><? echo $val ?></td><td class="reportRecords" valign="top"><? echo tep_db_num_rows($comments_statistic_selected_option_result_sql)?>&nbsp; (<?=number_format(tep_get_percentage($total, tep_db_num_rows($comments_statistic_selected_option_result_sql)), 2, '.', '')?>%)</td>
				</tr>
				<?	
				$row_count++;
			}
		}
	}
				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
	
				<tr class="<?=$row_style?>">
					<td class="reportRecords" valign="top"><?=ENTRY_COMMENTS_STATISTIC_TOTAL?></td><td class="reportRecords" valign="top"><?=$total?>&nbsp;(100.00%)</td>
				</tr>
			</table>
				</td>
			</tr>
			<tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
			<tr>	
				<td colspan="2" align="right">
					<?echo '<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS) . '">' . tep_image_button('button_back.gif', IMAGE_CANCEL) . '</a>'?>
				</td>
			</tr>
		<tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '70')?></td></tr>
	</table>
<?
}

$comments_select_sql = "SELECT * FROM " . TABLE_CART_COMMENTS . " ORDER BY cart_comments_type, cart_comments_title, cart_comments_sort_order";
$comments_result_sql = tep_db_query($comments_select_sql);

if (tep_db_num_rows($comments_result_sql)) {
	if($action != "statistic"){
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="80%" cellspacing="1" cellpadding="2">
               					<tr>
               						<td class="reportBoxHeading" width="8%"><?="Action"?></td>
			       					<td class="reportBoxHeading"><?=TABLE_HEADING_CART_COMMENTS_TITLE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_CART_COMMENTS_INPUT_TYPE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_CART_COMMENTS_TYPE?></td>
					                <td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_CART_COMMENTS_STATUS?></td>
					                <td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_CART_COMMENTS_REQUIRED?></td>
					                <td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_CART_COMMENTS_SORT_ORDER?></td>
			   					</tr>
<?
	$row_count = 0;
	while ($comments_row = tep_db_fetch_array($comments_result_sql)) {
    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top">
										<a href="<?=tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=edit_comments&commentID='.$comments_row["cart_comments_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=addslashes($comments_row["cart_comments_title"])?>', 'Comments', '<?=tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=delete_comments&commentID='.$comments_row["cart_comments_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
										<a href="<?=tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=statistic&commentID='.$comments_row["cart_comments_id"].'&comments_type='.$comments_row["cart_comments_input_type"])?>"><?=tep_image(DIR_WS_ICONS."statistics.gif", "Statistic", "", "", 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top">
										<?=$comments_row["cart_comments_title"]?>
									</td>
									<td class="reportRecords" valign="top">
									<?
									foreach($field_type_array as $res) {
										if ($res["id"] == $comments_row["cart_comments_input_type"]) {
											echo $res["text"];
										}
									}
									?>
									</td>
									<td class="reportRecords" valign="top">
									<?
									if ($comments_row['cart_comments_type'] == '1') {
										echo "Shopping Cart Comments";
									} else {
										echo "Customer Registration Comments";
									}
									?>
									</td>
										
										
									<td class="reportRecords" align="center" valign="top">
									<?
									if ($comments_row['cart_comments_status'] == '1') {
										echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=set_status&status_flag=0&commentID=' . $comments_row['cart_comments_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
									} else {
										echo '<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=set_status&status_flag=1&commentID=' . $comments_row['cart_comments_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
									}
									?>
									</td>
									<td class="reportRecords" align="center" valign="top">
									<?
									if ($comments_row['cart_comments_required'] == '1') {
										echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', TEXT_ICON_MANDATORY, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=set_required&required_flag=0&commentID=' . $comments_row['cart_comments_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', TEXT_ICON_SET_OPTIONAL, 10, 10) . '</a>';
									} else {
										echo '<a href="' . tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'action=set_required&required_flag=1&commentID=' . $comments_row['cart_comments_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', TEXT_ICON_SET_MANDATORY, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', TEXT_ICON_OPTIONAL, 10, 10);
									}
									?>
									</td>
									<td class="reportRecords" align="center" valign="top"><?=$comments_row["cart_comments_sort_order"]?></td>
								</tr>
<?
		$row_count++;
	}
?>
		    				</table>
    					</td>
    				</tr>
<?
}
?>

    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<?
}
?>

<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>