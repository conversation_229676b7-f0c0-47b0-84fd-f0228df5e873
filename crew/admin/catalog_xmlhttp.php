<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/xml');

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'category.php');

$cid = (int)(isset($_REQUEST['cid']) ? $_REQUEST['cid'] : '');
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (tep_not_null($action)) {
	echo '<response>';
	switch($action) {
		case 'sub_categories':

            tep_db_connect_og();
            $categories_games_array = [];
            $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
            $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
            while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                $categories_games_array[] = $categories_row['categories_id'];
            }
		
			echo "<categories>";
			$sub_categories_select_sql = "	SELECT c.categories_id, c.categories_parent_path, c.parent_id, cd.categories_name
											FROM " . TABLE_CATEGORIES. " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.parent_id = '" . $cid . "'
												AND cd.language_id='1'
											GROUP BY c.categories_id
											ORDER BY c.sort_order";
			$sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
			while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
				echo "	<category categories_id='".$sub_categories_row['categories_id']."' selected='".(in_array($sub_categories_row['categories_id'], $categories_games_array)?'1':'0')."'><![CDATA[". $sub_categories_row['categories_name']."]]></category>";
			}
			echo "</categories>";
			break;
		default:
			break;
	}
	echo '</response>';
}
/*
function echo_array($array, $level=0) {
	if ( is_array($array)) {
		array_walk($array, "echo_array", $level+1);
	} else {
		echo ($level?str_repeat("_",$level):'') . $array . "<BR>";
	}
}


			$sub_categories_select_sql = "	SELECT c.categories_id, c.categories_parent_path, c.parent_id, cd.categories_name
											FROM " . TABLE_CATEGORIES. " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_parent_path REGEXP '_" . $cid . "_'
												AND cd.language_id='1'
											GROUP BY c.categories_id
											ORDER BY c.sort_order";
			$sub_categories_result_sql =  tep_db_query($sub_categories_select_sql);
			$sub_categories_array = array();
			$sub_categories_name_array = array();
			while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
				$sub_categories_name_array[$sub_categories_row['categories_id']] = $sub_categories_row['categories_name'];
				if ($sub_categories_row['parent_id'] > 0) {
					$sub_categories_parent_path = $sub_categories_row['categories_parent_path'];
					if (substr($sub_categories_parent_path,0,1) == "_") $sub_categories_parent_path = substr($sub_categories_parent_path, 1);
					if (substr($sub_categories_parent_path,-1) == "_") $sub_categories_parent_path = substr($sub_categories_parent_path, 0, -1);
					$sub_categories_parent_path = explode("_", $sub_categories_parent_path);
					
					$sub_categories_str = '';
					foreach ($sub_categories_parent_path as $sub_categories_data) {
						$sub_categories_str .= "['".$sub_categories_data."']";
					}
					$sub_categories_str .= "['".$sub_categories_row['categories_id']."']";
					
					//need to set/study again
					//if (tep_not_null($sub_categories_str)) {
					$flag = true;
					eval('if (isset($sub_categories_array'.$sub_categories_str . ')) $flag=false;');
					if ($flag && tep_not_null($sub_categories_str)) {
					eval('$sub_categories_array'.$sub_categories_str . ' = array(	"id"=>"'.$sub_categories_row['categories_id'].'","name" => "'.tep_db_output($sub_categories_row['categories_name']).'");');
					//eval('echo "sub_categories_array'.$sub_categories_str .' = |\"id\"  = \"'.$sub_categories_row['categories_id'].'\"| == |\"name\"  = \"'.$sub_categories_row['categories_name'].'\"|  <BR>";');
					}
				} else {
					$sub_categories_array[$sub_categories_row['categories_id']] = tep_db_output($sub_categories_row['categories_name']);
				}
			}
			echo"<pre>";print_r($sub_categories_array);exit;echo"</	pre>";
			array_walk($sub_categories_array, "echo_array");*/
?>