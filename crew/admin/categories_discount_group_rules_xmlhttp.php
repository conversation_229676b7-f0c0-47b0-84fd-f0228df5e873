<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'purchase_control_tool.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CATEGORY_DISCOUNT_RULE);
require_once(DIR_WS_CLASSES . 'log.php');
$log_object = new log_files($login_id);

$rule_id = (isset($_REQUEST['rule_id']) ? $_REQUEST['rule_id'] : '');
$pid = (isset($_REQUEST['pid']) ? $_REQUEST['pid'] : '');
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$is_ajax = (isset($_REQUEST['is_ajax']) ? $_REQUEST['is_ajax'] : false);
$purchase_control_tool_object = new purchase_control_tool();

if (tep_not_null($action)) {
    switch ($action) {
        case 'get_category_discount_rules_by_id':
            echo $purchase_control_tool_object->get_category_discount_rules_by_id($rule_id);
            break;
        case 'recalculate_by_pid':
            $recalculate_out_of_stock_permission  = tep_admin_files_actions(FILENAME_CATEGORIES, 'RECALCULATE_OUT_OF_STOCK');
            
            if ($recalculate_out_of_stock_permission) {
                $available_qty =0;

                $products_extra_info_query = tep_db_query("SELECT products_extra_info_value from " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_id= '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'");
                if ($products_extra_info = tep_db_fetch_array($products_extra_info_query)) {
                    $available_qty = $products_extra_info['products_extra_info_value'];
                }

                $products_qty_query = tep_db_query("SELECT products_quantity from " . TABLE_PRODUCTS . " WHERE products_id= '" . (int) $pid . "'");
                if ($products_qty_row = tep_db_fetch_array($products_qty_query)) {
                    $new_prod_available_qty = $products_qty_row['products_quantity'];

                    if ($available_qty !== $new_prod_available_qty) {
                        $sql_data_array = array('products_extra_info_value' => $new_prod_available_qty);

                        if (tep_db_perform(TABLE_PRODUCTS_EXTRA_INFO, $sql_data_array, 'update', "products_id = '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'")) {
                            $log_object->insert_log($pid, 'stock_quantity', $available_qty, $new_prod_available_qty, LOG_PRODUCT_STOCK_QUANTITY_UPDATE, '', $_SESSION['login_email_address']);
                        }
                    }

                    echo $purchase_control_tool_object->recalculate_by_pid($rule_id, $pid, $messageStack, $is_ajax);
                    break;
                }

                echo 'Error, please try again!';
            }
            break;
//        case 'recalculte_by_rule_id':
//            echo $purchase_control_tool_object->recalculate_by_rule_id($rule_id, $messageStack);
//            break;

        default:
            break;
    }
}
?>