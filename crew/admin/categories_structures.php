<?php
require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'jquery_cat_tree.php');

tep_set_time_limit(800);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
if (tep_not_null($action)) {
	switch ($action) {
    	case 'update_games':
    		if (isset($_POST['chk_cat'])) {
	    		$selected_games_array = array();
				foreach ($_POST['chk_cat'] as $selected_games) {
					$selected_games_array[] = $selected_games;
				}
				
				$selected_games_str = '';
				if (count($selected_games_array)) {
					$selected_games_str = implode(",",$selected_games_array);	
				}

				//checked && existing selected games
				$games_select_sql = "	SELECT categories_structures_value
										FROM " . TABLE_CATEGORIES_STRUCTURES . " 
										WHERE categories_structures_key = 'games'";
				$games_result_sql = tep_db_query($games_select_sql);
				$games_row = tep_db_fetch_array($games_result_sql);

                if (tep_not_null($games_row['categories_structures_value'])) {
                    $previously_selected_games = count(explode(',',$games_row['categories_structures_value']));
                } else {
                    $previously_selected_games = 0;
                }

                $prev_selected_game_array = explode(',',$games_row['categories_structures_value']);

                $new_category_ids = array_diff($selected_games_array, $prev_selected_game_array);
                $old_category_ids = array_diff($prev_selected_game_array, $selected_games_array);
                $category_ids = array_merge($new_category_ids,$old_category_ids);

                // if got new / delete category then only send slack notification
                if(!empty($new_category_ids) || !empty($old_category_ids)) {
                    $category_where_statement = " categories_id IN ('" . implode("', '", $category_ids) . "') ";
                    $categories_select_sql = "	SELECT categories_id, categories_name 
                                            FROM " . TABLE_CATEGORIES_DESCRIPTION . "
												WHERE " . $category_where_statement . " AND
												language_id = 1";

                    $cat_result_sql = tep_db_query($categories_select_sql);

                    $attachments = [];
                    while ($categories_row = tep_db_fetch_array($cat_result_sql)) {
                        $status = (in_array($categories_row['categories_id'], $new_category_ids)) ? "Status Change : Inactive => Active" : "Status Change : Active => Inactive";
                        array_push($attachments, [
                            'color' => 'warning',
                            'text' => "Category : <" . HTTPS_SERVER . "categories_structures.php|" . $categories_row['categories_name'] . " (" . $categories_row['categories_id'] . ")" . ">\n" .
                                $status . "\n" .
                                "Change by : " . $login_email_address
                        ]);
                    }

                    // if got any fields is updated then only send out the slack notifications
                    if (!empty($attachments)) {
                        require_once(DIR_WS_CLASSES . 'slack_notification.php');
                        foreach (array_chunk($attachments, 100) as $attachments) {
                            $slack = new slack_notification();
                            $data = json_encode(array(
                                'text' => '[OG Crew] Categories Structures ' . LOG_STATUS_ADJUST . ' - ' . date("F j, Y H:i"),
                                'attachments' => $attachments
                            ));
                            $slack->send(SLACK_WEBHOOK_BDT, $data);
                        }
                    }
                }

                $delete_categories_games_sql = "DELETE FROM " . TABLE_CATEGORIES_STRUCTURES . "
												WHERE categories_structures_key = 'games'";
				tep_db_query($delete_categories_games_sql);
				
	  			$categories_games_sql_data = array(	'categories_structures_key' => 'games',
					 								'categories_structures_value' => tep_db_prepare_input($selected_games_str)
					 								);
	  			tep_db_perform(TABLE_CATEGORIES_STRUCTURES, $categories_games_sql_data);
	  			
	  			$messageStack->add_session("Number of selected games changed from ".$previously_selected_games." -> ".count($selected_games_array).".","success");
	  			tep_redirect(tep_href_link(FILENAME_CATEGORIES_STRUCTURES));
	  			
	  		}
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main" colspan="2"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td class="main" colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td width="60%">
							<?=tep_draw_form('categories_structure_frm', FILENAME_CATEGORIES_STRUCTURES, 'action=update_games', 'post');?>
							<table width="100%" border="0" cellspacing="1" cellpadding="2">
			  					<tr class="ordersBoxHeading">
			  						<td width="11%" align="center"><?=TABLE_HEADING_GAMES?></td>
							  	</tr>
								<tr class="reportListingEven">
									<td class="reportRecords">
	<?php
	$categories_games_select_sql = "SELECT categories_structures_value 
									FROM " . TABLE_CATEGORIES_STRUCTURES . "
									WHERE categories_structures_key  = 'games'";
	$categories_games_result_sql = tep_db_query($categories_games_select_sql);
	$categories_games_row = tep_db_fetch_array($categories_games_result_sql);
	if (tep_not_null($categories_games_row['categories_structures_value'])) {
		$categories_games_array = explode(",", $categories_games_row['categories_structures_value']);
	} else {
		$categories_games_array = array();
	}
	echo tep_ul_categories_tree_array('', $categories_games_row['categories_structures_value']);
	?>
										<br>
										<?=tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' name="btn_categories_structure" onClick="document.categories_structure_frm.submit()" ', 'inputButton')?>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top" width="*%">
							<table width="100%" border="0" cellspacing="1" cellpadding="2">
			  					<tr class="ordersBoxHeading">
			  						<td width="11%" align="center"><?=TABLE_HEADING_SELECTED_GAMES . (count($categories_games_array)>0?' (' . count($categories_games_array) .')':'')?></td>
							  	</tr>
								<tr class="reportListingEven">
									<td class="reportRecords">
<?php
/*
	$selected_categories_array = array();
	if (count($categories_games_array)) {
		$categories_games_str = implode("','", $categories_games_array);
		$selected_categories_select_sql = "	SELECT cd.categories_name, c.categories_id 
											FROM " . TABLE_CATEGORIES. " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_id IN ('" . $categories_games_str . "') 
												AND cd.language_id='1'
											GROUP BY c.categories_id
											ORDER BY c.sort_order, cd.categories_name";
		$selected_categories_result_sql = tep_db_query($selected_categories_select_sql);
		if (tep_db_num_rows($selected_categories_result_sql)) {
			while ($selected_categories_row = tep_db_fetch_array($selected_categories_result_sql)) {
				echo " - " . $selected_categories_row['categories_name'] . " (".$selected_categories_row['categories_id'].")<BR>";
			}
		} else {
			echo TEXT_INFO_ERROR_GAME_NOT_SELECTED;	
		}
	}
 */
?>	
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>		
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
<script type="text/javascript">
	function toggle_cat(pass_obj) {
		if (jQuery(pass_obj)) {
			subbranch = jQuery('ul', pass_obj.parentNode).eq(0);
			if (subbranch.css('display') == 'none') {
				subbranch.show();
				pass_obj.innerHTML = '<font style="color:red;cursor:pointer">[-]</font>';
			} else {
				subbranch.hide();
				pass_obj.innerHTML = '<font style="color:green;cursor:pointer">[+]</font>';
			}
		}
	}
</script>