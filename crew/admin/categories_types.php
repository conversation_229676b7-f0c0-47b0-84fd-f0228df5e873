<?
require('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST["subaction"]) ? $_REQUEST["subaction"] : '');
$cat_types_groups_id = (isset($_REQUEST["cat_types_groups_id"]) ? $_REQUEST["cat_types_groups_id"] : '');
$custom_products_type_id = (isset($_REQUEST["cpt_id"]) ? $_REQUEST["cpt_id"] : '');

function get_last_alias($categories_types_parent_id) {
	$categories_types_id_select_sql = "SELECT categories_types_id FROM " . TABLE_CATEGORIES_TYPES . " WHERE categories_types_parent_id = '" . (int)$categories_types_parent_id . "'";
	$categories_types_id_result_sql = tep_db_query($categories_types_id_select_sql);
	if ($categories_types_id_row = tep_db_fetch_array($categories_types_id_result_sql)) {
		$categories_types_id = $categories_types_id_row['categories_types_id'];
		get_last_alias($categories_types_id);
	}
	
	return $categories_types_parent_id;
}

if (tep_not_null($subaction)) {
	switch($subaction) {
		case 'add_game':
		case 'edit_game':
			if(tep_not_null($HTTP_POST_VARS['categories_types_groups_name'])) {
				if ($action == 'edit_game') {
					$data_sql_array = array('categories_types_groups_name' => $HTTP_POST_VARS['categories_types_groups_name']);
					tep_db_perform(TABLE_CATEGORIES_TYPES_GROUPS, $data_sql_array, 'update', "categories_types_groups_id = '" . (int)$HTTP_POST_VARS['categories_types_groups_id'] . "'");
					
					$messageStack->add_session(SUCCESS_GAME_UPDATE, 'success');
				} else {
					$data_sql_array = array('categories_types_groups_name' => $HTTP_POST_VARS['categories_types_groups_name']);
					tep_db_perform(TABLE_CATEGORIES_TYPES_GROUPS, $data_sql_array);
					
					$messageStack->add_session(SUCCESS_GAME_INSERT, 'success');
				}
			} else {
				$messageStack->add_session(ERROR_GAME, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(array('subaction'))));
			break;
		case 'delete_game':
			$categories_types_sets_id_select_sql = "	SELECT categories_types_sets_id 
														FROM " . TABLE_CATEGORIES_TYPES_SETS . " 
														WHERE categories_types_groups_id = '" . (int)$cat_types_groups_id . "'";
			$categories_types_sets_id_result_sql = tep_db_query($categories_types_sets_id_select_sql);
			while ($categories_types_sets_id_row = tep_db_fetch_array($categories_types_sets_id_result_sql)) {
				$categories_types_id_select_sql = "	SELECT categories_types_id 
			 										FROM " . TABLE_CATEGORIES_TYPES_TO_SETS . " 
			 										WHERE categories_types_sets_id = '" . (int)$categories_types_sets_id_row['categories_types_sets_id'] . "'";
				$categories_types_id_result_sql = tep_db_query($categories_types_id_select_sql);
				while($categories_types_id_row = tep_db_fetch_array($categories_types_id_result_sql)) {
				 	tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES . " WHERE categories_types_id = '" . (int)$categories_types_id_row['categories_types_id'] . "'");
				}
				tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES_TO_SETS . " WHERE categories_types_sets_id = '" . (int)$categories_types_sets_id_row['categories_types_sets_id'] . "'");
			}
			tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES_SETS . " WHERE categories_types_groups_id = '" . (int)$cat_types_groups_id . "'");
			tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES_GROUPS . " WHERE categories_types_groups_id = '" . (int)$cat_types_groups_id . "'");
			tep_redirect(tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(array('subaction', 'cat_types_groups_id'))));
			break;
		case 'add_new_alias':
			$categories_types_sets_id_select_sql = "	SELECT categories_types_sets_id, categories_types_root_id 
														FROM " . TABLE_CATEGORIES_TYPES_SETS . " 
														WHERE categories_types_groups_id = '" . tep_db_input($HTTP_POST_VARS['categories_types_groups_id']) . "' 
															AND custom_products_type_id = '" . tep_db_input($HTTP_POST_VARS['custom_products_type_id']) . "'";
			$categories_types_sets_id_result_sql = tep_db_query($categories_types_sets_id_select_sql);
			
			if (tep_db_num_rows($categories_types_sets_id_result_sql) > 0) {
				$categories_types_sets_id_row = tep_db_fetch_array($categories_types_sets_id_result_sql);
				
				$categories_types_sql_array = array('categories_types_value' => $HTTP_POST_VARS['categories_types_value'],
													'categories_types_parent_id' => $HTTP_POST_VARS['categories_types_id'],
													'categories_types_display' => 0
													);
				tep_db_perform(TABLE_CATEGORIES_TYPES, $categories_types_sql_array);
			} else {
				$categories_types_sql_array = array('categories_types_value' => $HTTP_POST_VARS['categories_types_value']);
				
				tep_db_perform(TABLE_CATEGORIES_TYPES, $categories_types_sql_array);
				$categories_types_root_id = tep_db_insert_id();
				
				$categories_types_sets_sql_array = array(	'categories_types_groups_id' => $HTTP_POST_VARS['categories_types_groups_id'],
															'custom_products_type_id' => $HTTP_POST_VARS['custom_products_type_id'],
															'categories_types_root_id' => $categories_types_root_id
														);
				
				tep_db_perform(TABLE_CATEGORIES_TYPES_SETS, $categories_types_sets_sql_array);
			}
			
			tep_redirect(tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(array('subaction'))));
			break;
		case 'remove_alias':
			tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES . " WHERE categories_types_id = '" . (int)$_REQUEST['categories_types_id'] . "'");
			tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TYPES_SETS . " WHERE categories_types_root_id = '" . (int)$_REQUEST['categories_types_id'] . "'");
			
			$messageStack->add_session(SUCCESS_CATEGORIES_ALIAS_DELETED, 'success');
			
			tep_redirect(tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(array('subaction'))));
			break;
		case 'set_display_status':
			$categories_types_display = (int)$_REQUEST['show'];
			
			$categories_types_display_data_sql_array = array('categories_types_display' => $categories_types_display);
			tep_db_perform(TABLE_CATEGORIES_TYPES, $categories_types_display_data_sql_array, 'update', "categories_types_id = '" . (int)$_REQUEST['categories_types_id'] . "'");
			
			tep_redirect(tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(array('subaction', 'show', 'categories_types_id'))));
			break;
	}
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="Javascript1.2"><!-- // load htmlarea
		// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 Products Description HTML - Head
		        _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
		          var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
		           if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
		            if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
		             if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
		         <?php if (HTML_AREA_WYSIWYG_BASIC_PD == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php } else{ ?> if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php }?>
		
		var config = new Object();  // create new config object
	    config.width = "<?php echo HTML_AREA_WYSIWYG_WIDTH; ?>px";
	    config.height = "<?php echo HTML_AREA_WYSIWYG_HEIGHT; ?>px";
	    config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
		config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
	// --></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
switch ($action) {
	case 'add_game' :
	case 'edit_game':
		$categories_types_groups_name_select_sql = "	SELECT * FROM " . TABLE_CATEGORIES_TYPES_GROUPS . " WHERE categories_types_groups_id = '" . (int)$cat_types_groups_id . "'";
		$categories_types_groups_name_result_sql = tep_db_query($categories_types_groups_name_select_sql);
		$categories_types_groups_name_row = tep_db_fetch_array($categories_types_groups_name_result_sql);
?>
					<tr>
						<td>
<?							echo tep_draw_form('categories_types_groups_form', FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(), 'post');?>
								<table width="40%" border="0" cellspacing="0" cellpadding="0">
									<tr>
										<td class="main" width="45%"><?=ENTRY_GAME?></td>
										<td class="main">
										<?
											echo tep_draw_input_field('categories_types_groups_name', $categories_types_groups_name_row['categories_types_groups_name'], ' id="categories_types_groups_name"');
											echo tep_draw_hidden_field('categories_types_groups_id', $categories_types_groups_name_row['categories_types_groups_id']);
										?>
										</td>
									</tr>
									<tr>
										<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td></td>
										<td>
										<?
											if ($action == 'edit_game') {
												echo tep_image_button('button_update.gif', IMAGE_UPDATE, " onClick='categories_types_groups_form_checking()' ");
												echo tep_draw_hidden_field('subaction', 'edit_game');
											} else {
												echo tep_image_button('button_insert.gif', IMAGE_INSERT, " onClick='categories_types_groups_form_checking()' ");
												echo tep_draw_hidden_field('subaction', 'add_game');
											}
											echo '&nbsp;' . '<a href="' . tep_href_link(FILENAME_CATEGORIES_TYPES) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a></td>';
										?>
										</td>
									</tr>
								</table>
							</form>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
		break;
	case 'edit_cat_type':
		function tep_show_list_items($ListItems, $Level=0) {
			global $HiddenItems, $g2c_array;
			$SubTotal=0;
			
			foreach ($ListItems as $ListItem) {
				$NewListItems = array();
				$parent_to_child = array();
				
				$categories_types_sets_select_sql = "SELECT * FROM " . TABLE_CATEGORIES_TYPES . " WHERE categories_types_parent_id = '" . $ListItem['categories_types_id'] . "'";
				
				$categories_types_sets_result_sql = tep_db_query($categories_types_sets_select_sql);
				while ($categories_types_sets_row = tep_db_fetch_array($categories_types_sets_result_sql)) {
					$NewListItems[] = $categories_types_sets_row;
					$parent_to_child[] = $child_categories_alias_row["categories_types_id"];
				}

				
				$SubTotal += 1 ;
				
				$DisplayName = strip_tags($ListItem["categories_types_value"]);
				if (!$DisplayName) $DisplayName = "" ;
				
				if ($ListItem["categories_types_display"]) {
					$DisplayName .= '&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;<a href="'.tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params() . "subaction=set_display_status&show=0&categories_types_id=".$ListItem['categories_types_id']).'">'.tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10).'</a>';
				} else {
					$DisplayName .= '&nbsp;<a href="' . tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params() . "subaction=set_display_status&show=1&categories_types_id=".$ListItem['categories_types_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '</a>&nbsp;'.tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10);
				}
				
				
				if (!count($NewListItems)) {
					$DisplayName .= '&nbsp;<a href="javascript:;" onclick="add_alias(this.form, '. $ListItem["categories_types_id"] . ', \'add_new_alias\');">'.tep_image(DIR_WS_ICONS."add_item.gif", '', 15, 15) . '</a>';
					$DisplayName .= '&nbsp;<a href="javascript:void(confirm_delete(\''.htmlspecialchars(addslashes($ListItem['categories_types_value']), ENT_QUOTES).'\', \'Categories Alias\', \'' . tep_href_link(FILENAME_CATEGORIES_TYPES, tep_get_all_get_params() . 'subaction=remove_alias&categories_types_id=' . $ListItem['categories_types_id']) . '\'))">' . tep_image(DIR_WS_ICONS."delete.gif", "Delete Category Alias", "", "", 'align="top"') . '</a>';
				}
?>
					
				aux<?=$ListItem["categories_types_id"]?> = insFld(<?=($Level==1?"foldersTree":"aux".$ListItem["categories_types_parent_id"])?>, gFld("<?
				// Text
				echo addslashes($DisplayName);?>",
<?
				// Link
				if ($url) { ?>
					"javascript:MyNewWindow(\"<?=$url?>\",\"Open\",<?=$this->PopupWinWidth?>,<?=$this->PopupWinHeight?>,\"yes\")"))
<?				} else { ?>
					"javascript:undefined"))
<?				} ?>
				aux<?=$ListItem["categories_types_id"]?>._readonly = 0;
<?
				$SubTotal += tep_show_list_items($NewListItems, $Level+1);
			}
			
			return $SubTotal ;
		}
		
		$ListItems = array();
		$HiddenItems = array();
		$parent_to_child = array();
		
		$categories_types_sets_select_sql = "	SELECT cts.*, ct.* 
												FROM " . TABLE_CATEGORIES_TYPES_SETS . " AS cts 
												INNER JOIN " . TABLE_CATEGORIES_TYPES . " AS ct 
													ON (ct.categories_types_id = cts.categories_types_root_id AND categories_types_parent_id = 0) 
												WHERE custom_products_type_id = '" . (int)$custom_products_type_id . "' 
													AND cts.categories_types_groups_id = '" . $cat_types_groups_id . "'";
		$categories_types_sets_result_sql = tep_db_query($categories_types_sets_select_sql);
		while ($categories_types_sets_row = tep_db_fetch_array($categories_types_sets_result_sql)) {
			$ListItems[] = $categories_types_sets_row ;
			$parent_to_child[] = $child_categories_alias_info_row["categories_types_id"];
		}
?>
					<tr>
						<td>
<?							echo tep_draw_form('categories_types_form', FILENAME_CATEGORIES_TYPES, tep_get_all_get_params(), 'post') . tep_draw_hidden_field("browser_id", '', ' id="browser_id" ');?>
								<table width="100%" border="0" cellspacing="0" cellpadding="0">
									<tr>
										<td class="main" width="11%" valign="top"><?=ENTRY_CATEGORY_ALIAS?></td>
										<td class="main">
<?
											echo tep_draw_hidden_field('categories_types_value', '', ' id="categories_types_value" ');
											echo tep_draw_hidden_field('categories_types_id', '', ' id="categories_types_id" ');
											echo tep_draw_hidden_field('categories_types_groups_id', $cat_types_groups_id, ' id="categories_types_groups_id" ');
											echo tep_draw_hidden_field('custom_products_type_id', $tep_draw_hidden_field, ' id="custom_products_type_id" ');
											echo tep_draw_hidden_field('subaction', '', ' id="subaction" ');
?>
											<script language="javascript">
											<!--
											var browserName = navigator.appName; 
											if (browserName == 'Microsoft Internet Explorer')
												document.getElementById('browser_id').value = "IE";
											else
												document.getElementById('browser_id').value = "Non-IE";
											//-->
											</script>
											<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
											<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
											<script>
												function checkAll(folderObj) {
													var childObj;
												    var i;
												
												    // Open folder
												    if (!folderObj.isOpen) {
												      	clickOnNodeObj(folderObj)
												    }
												}
												
												function expandTree(folderObj)
												{
												    var childObj;
												    var i;
												
												    // Open folder
												    if (!folderObj.isOpen)
												      	clickOnNodeObj(folderObj)
													/*
												    // Call this function for all folder children
												    for (i=0 ; i < folderObj.nChildren; i++)  {
												      childObj = folderObj.children[i]
												      if (typeof childObj.setState != "undefined" && !childObj._readonly) {//is folder
												        expandTree(childObj)
												      }
												    }*/
												}
														
												// Close all folders
												function collapseTree()
												{
													//hide all folders
													clickOnNodeObj(foldersTree)
													//restore first level
													clickOnNodeObj(foldersTree)
												}
												
												//Environment variables are usually set at the top of this file.
												USELINKFORFOLDER = 0
												USETEXTLINKS = 0
												STARTALLOPEN = 0
												USEFRAMES = 0
												USEICONS = 0
												WRAPTEXT = 1
												PRESERVESTATE = 1
												ICONPATH = 'includes/javascript/Treeview/'
												BUILDALL = 0
												HIGHLIGHT = 0;
<?												if (tep_db_num_rows($categories_types_sets_result_sql) > 0) { ?>
													foldersTree = gFld("<?=TEXT_TOP?>");
<?												} else { ?>
													foldersTree = gFld('<?=TEXT_TOP?>&nbsp;<a href="javascript:;" onclick="add_alias(this.form, 0, \'add_new_alias\');"><?=tep_image(DIR_WS_ICONS."add_item.gif", "", 15, 15)?></a>', '');
<?												} ?>
												
<?												$SubTotal = tep_show_list_items($ListItems, 1); ?>
											</script>
											<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
											<span class="formvalue">
												<script>initializeDocument()</script>
												<noscript>
												A tree for site navigation will open here if you enable JavaScript in your browser.
												</noscript>
											</span>
										</td>
									</tr>
								</table>
							</form>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
		break;
	default:
?>
					<tr>
						<td><a href="<?=tep_href_link(FILENAME_CATEGORIES_TYPES, 'action=add_game')?>"><?=LINK_NEW_GAME?></a></td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
		break;
}
?>
					<tr>
						<td>
							<table width="40%" border="0" cellspacing="1" cellpadding="2">
			  					<tr class="ordersBoxHeading">
			  						<td width="11%" align="center"><?=TABLE_HEADING_ACTION?></td>
			  						<td width="30%"><?=TABLE_HEADING_GAME?></td>
			  						<td><?=TABLE_HEADING_CATEGORIES_TYPES?></td>
							  	</tr>
<?
$categories_types_groups_select_sql = " SELECT * FROM " . TABLE_CATEGORIES_TYPES_GROUPS;
$categories_types_groups_result_sql = tep_db_query($categories_types_groups_select_sql);
while ($categories_types_groups_row = tep_db_fetch_array($categories_types_groups_result_sql)) {
	$categories_types_groups_style = ($categories_types_groups_row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
								<tr class="<?=$categories_types_groups_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$categories_types_groups_style?>')" onclick="rowClicked(this, '<?=$categories_types_groups_style?>')">
									<td class="reportRecords" align="center">
										<a href="<?=tep_href_link(FILENAME_CATEGORIES_TYPES, 'action=edit_game&cat_types_groups_id='.$categories_types_groups_row['categories_types_groups_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
										<?='<a href="javascript:void(confirm_delete(\''.htmlspecialchars(addslashes($categories_types_groups_row['categories_types_groups_name']), ENT_QUOTES).'\', \'Game\', \'' . tep_href_link(FILENAME_CATEGORIES_TYPES, 'subaction=delete_game&cat_types_groups_id=' . $categories_types_groups_row['categories_types_groups_id']) . '\'))">' . tep_image(DIR_WS_ICONS."delete.gif", "Delete Comment", "", "", 'align="top"') . '</a>'?>
									</td>
									<td class="reportRecords"><?=$categories_types_groups_row['categories_types_groups_name']?></td>
									<td class="reportRecords" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
			          							<td colspan="2"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
			          						<tr>
												<td class="subRecordsBoxHeading" width="16%"><?=TABLE_HEADING_ACTION?></td>
												<td class="subRecordsBoxHeading"><?=TABLE_HEADING_CATEGORIES_TYPES?></td>
			          						</tr>
			          						<tr>
			          							<td colspan="2"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
<?
	$custom_products_type_select_sql = "	SELECT custom_products_type_id, custom_products_type_name FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . " ORDER BY custom_products_type_id";
	$custom_products_type_result_sql = tep_db_query($custom_products_type_select_sql);
	while ($custom_products_type_row = tep_db_fetch_array($custom_products_type_result_sql)) {
?>
											<tr>
												<td class="reportRecords" align="center">
													<a href="<?=tep_href_link(FILENAME_CATEGORIES_TYPES, 'action=edit_cat_type&cat_types_groups_id='.$categories_types_groups_row['categories_types_groups_id'].'&cpt_id='.$custom_products_type_row['custom_products_type_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
												</td>
												<td class="reportRecords"><?=$custom_products_type_row['custom_products_type_name']?></td>
											</tr>
<?	} ?>
										</table>
										
									</td>
								</tr>
<?
}
?>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>		
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
<script language="javascript">
<!--
	function trim(str) {
		str = str.replace(/^\s*|\s*$/g,"");
			
		return str;
	}
		
	function categories_types_groups_form_checking() {
		var err = 0;
	
		if (trim(document.getElementById('categories_types_groups_name').value) == '') {
			err++;
		}
		
		if (err == 0) {		
			document.categories_types_groups_form.submit();
			//return true;
		} else {
			alert('Please fill in the required field!');
			return false;
		}
	}
	
	function add_alias(form_obj, categories_types_id, alias_action) {
		if (alias_action == 'add_new_alias') {
			var alias_name_insert = prompt("Please enter Alias Name","");
			
			if (alias_name_insert != null) {
				document.getElementById('categories_types_value').value = alias_name_insert;
				document.getElementById('categories_types_id').value = categories_types_id;
				document.getElementById('subaction').value = alias_action;
				
				if (alias_name_insert != '') {
					document.categories_types_form.submit();
				}
			}
		}
	} 
//-->
</script>