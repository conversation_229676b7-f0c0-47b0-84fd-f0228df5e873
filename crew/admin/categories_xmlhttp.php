<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');

$currencies = new currencies();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$cat_id = (int)(isset($_REQUEST['cat_id']) ? $_REQUEST['cat_id'] : '');

echo '<response>';

if (tep_not_null($action)){
	switch($action) {
		case "get_delivery_mode":
			$error_flag = true;
			$message = ERROR_DELIVERY_MODE_NOT_FOUND;
			if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
				$products_delivery_info_array = array();
				$products_delivery_info_sql = "	SELECT products_delivery_mode_id
												FROM " . TABLE_PRODUCTS_DELIVERY_INFO . "
												WHERE products_id = '".(int)$_REQUEST['pID']."'";
				$products_delivery_info_result = tep_db_query($products_delivery_info_sql);
				while ($products_delivery_info_row = tep_db_fetch_array($products_delivery_info_result)) {
					$products_delivery_info_array[] = $products_delivery_info_row['products_delivery_mode_id'];
				}
				
				$products_delivery_mode_array = array();
				if (isset($_REQUEST['cptID']) && (int)$_REQUEST['cptID']>0) {
					require_once(DIR_WS_CLASSES . 'product.php');
					$products_delivery_mode_array = product::get_delivery_mode((int)$_REQUEST['cptID']);
				}
				if (count($products_delivery_mode_array) && is_array($products_delivery_mode_array)) {
					$error_flag = false;
					$message = '';
					echo '<delivery_mode>';
					foreach ($products_delivery_mode_array as $products_delivery_mode_id_loop => $products_delivery_mode_data_loop) {
						echo "<mode id='".$products_delivery_mode_id_loop."' ".(in_array($products_delivery_mode_id_loop, $products_delivery_info_array) ? " checked='1' ": '')."><![CDATA[".$products_delivery_mode_data_loop."]]></mode>";
					}
					echo '</delivery_mode>';
				}
			} else {
				$message = ERROR_INCOMPLETE_REQUEST	;
			}
			echo "<error><![CDATA[".($error_flag? 1 : 0 )."]]></error>";
			echo "<message><![CDATA[".$message."]]></message>";
			
			break;
		case "save_delivery_mode":
			$edit_product_delivery_info_permission = tep_admin_files_actions(FILENAME_CATEGORIES, 'CATALOG_EDIT_PRODUCT_DELIVERY_INFO');
			
			$error_flag = true;
			if (!$edit_product_delivery_info_permission) {
				$message = "Permission Denied";
			} else if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
				$pId = (int)$_REQUEST['pID'];
				
				$product_info_array = array();
				$product_info_select_sql = "SELECT p.products_id  
											FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												ON p.products_id=pb.bundle_id 
							    			WHERE pb.subproduct_id='" . tep_db_input($pId) . "'";
				$product_info_result_sql = tep_db_query($product_info_select_sql);
				while ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
					$product_info_array[] = $product_info_row['products_id'];
				}
				
				if (isset($_REQUEST['chk_delivery_mode']) && in_array(6, $_REQUEST['chk_delivery_mode'])) {
					include_once(DIR_WS_CLASSES . 'direct_topup.php');
					$direct_topup_obj = new direct_topup();
					$current_top_up_info_array = array();
					
					$current_game_input = $direct_topup_obj->get_admin_game_input($pId);
					foreach ($current_game_input as $loop_top_up_info_key_loop => $loop_top_up_info_data_loop) {
						$current_top_up_info_array[$loop_top_up_info_key_loop] = $loop_top_up_info_data_loop['top_up_info_value'];
					}
					
					$current_games_info_select_sql = "	SELECT pg.publishers_game, pg.publishers_id
														FROM " . TABLE_PUBLISHERS_GAMES. " AS pg
														INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
															ON pg.publishers_games_id = pp.publishers_games_id
														WHERE pp.products_id = '".(int)$pId."'";
					$current_games_info_result_sql = tep_db_query($current_games_info_select_sql);
					$current_games_info_row = tep_db_fetch_array($current_games_info_result_sql);
					
					foreach ($product_info_array as $product_info_id_loop) {
						$sub_product_select_sql = "	SELECT pb.subproduct_id  
									    			FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
													WHERE pb.bundle_id = '" . $product_info_id_loop . "'
														AND pb.subproduct_id <> '".tep_db_input($pId)."'";
						$sub_product_result_sql = tep_db_query($sub_product_select_sql);
						while ($sub_product_row = tep_db_fetch_array($sub_product_result_sql)) {
							$sub_top_up_info_array = array();
							if ($direct_topup_obj->check_is_supported_by_direct_top_up((int)$sub_product_row['subproduct_id'])) {
								$sub_game_input = $direct_topup_obj->get_admin_game_input($sub_product_row['subproduct_id']);
								
								foreach ($sub_game_input as $loop_top_up_info_key_loop => $loop_top_up_info_data_loop) {
									$sub_top_up_info_array[$loop_top_up_info_key_loop] = $sub_top_up_info_array['top_up_info_value'];
								}
								
								ksort($current_top_up_info_array);
								ksort($sub_top_up_info_array);
								if (count(array_diff_assoc($sub_top_up_info_array, $current_top_up_info_array)) > 0 ||
									count(array_diff_assoc($current_top_up_info_array, $sub_top_up_info_array)) > 0) {
									$error_flag = true;
									
									$sub_products_name = tep_get_products_name($sub_product_row['subproduct_id']);
									
									$message = ERROR_UNABLE_TO_GROUP_DIFF_DIRECT_TOP_UP_GAMES;
									$message .= "<BR>Please check DTU info for product: " . $sub_products_name;
									
									echo "<error><![CDATA[1]]></error>";
									echo "<message><![CDATA[".$message."]]></message>";
									echo '</response>';
									exit;
								}
								
								$sub_games_info_select_sql = "	SELECT pg.publishers_game, pg.publishers_id
																FROM " . TABLE_PUBLISHERS_GAMES. " AS pg
																INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
																	ON pg.publishers_games_id = pp.publishers_games_id
																WHERE pp.products_id = '".(int)$sub_product_row['subproduct_id']."'";
								$sub_games_info_result_sql = tep_db_query($sub_games_info_select_sql);
								$sub_games_info_row = tep_db_fetch_array($sub_games_info_result_sql);
								if ($sub_games_info_row['publishers_game'] != $current_games_info_row['publishers_game'] ||
									$sub_games_info_row['publishers_id'] != $current_games_info_row['publishers_id']) {
									echo "<error><![CDATA[1]]></error>";
									
									$sub_products_name = tep_get_products_name($sub_product_row['subproduct_id']);
									
									$message = ERROR_UNABLE_TO_GROUP_DIFF_DIRECT_TOP_UP_GAMES;
									$message .= "<BR>Please check DTU info for product: " . $sub_products_name;
									
									echo "<message><![CDATA[".$message."]]></message>";
									echo '</response>';
									exit;
								}
							}
						}
					}
				}
				
				$products_select_sql = "SELECT custom_products_type_id 
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '".$pId."'";
				$products_result_sql = tep_db_query($products_select_sql);
				if ($products_row = tep_db_fetch_array($products_result_sql)) {
					$check_delivery_mode_array = array();
					$delivery_mode_selected_array = array();
					$check_delivery_mode_select_sql = "	SELECT pdm.products_delivery_mode_id, pdi.products_delivery_mode_id as is_selected
														FROM " . TABLE_PRODUCTS_DELIVERY_MODE . " AS pdm 
														LEFT JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
															ON pdm.products_delivery_mode_id = pdi.products_delivery_mode_id 
																AND pdi.products_id = '".$pId."'
														WHERE FIND_IN_SET('".(int)$products_row['custom_products_type_id']."', custom_products_type)
															OR pdm.custom_products_type IS NULL";
					$check_delivery_mode_result_sql = tep_db_query($check_delivery_mode_select_sql);
					while ($check_delivery_mode_row = tep_db_fetch_array($check_delivery_mode_result_sql)) {
						$check_delivery_mode_array[] = $check_delivery_mode_row['products_delivery_mode_id'];
						if (tep_not_null($check_delivery_mode_row['is_selected'])) {
							$delivery_mode_selected_array[] = $check_delivery_mode_row['products_delivery_mode_id'];
						}
					}
					$error_flag = false;
					$chk_delivery_mode_array = array();
					if (isset($_REQUEST['chk_delivery_mode'])) {
						foreach ($_REQUEST['chk_delivery_mode'] as $chk_delivery_mode_loop) {
							if (!in_array($chk_delivery_mode_loop, $check_delivery_mode_array)) {
								$error_flag = true;
								$message = "Invalid Delivery Mode";
								break;
							}
							$chk_delivery_mode_array[$chk_delivery_mode_loop] = 1;
						}
					}
					
					if (!$error_flag) {
						$chk_delivery_mode_array = array_keys($chk_delivery_mode_array);
						tep_db_query("DELETE FROM " . TABLE_PRODUCTS_DELIVERY_INFO . " WHERE products_id = '".$pId."' AND products_delivery_mode_id NOT IN ('".implode("','", $chk_delivery_mode_array)."')");
						foreach ($chk_delivery_mode_array as $chk_delivery_mode_id_loop) {
						if (in_array($chk_delivery_mode_id_loop, $delivery_mode_selected_array)) continue;
						$products_delivery_info_data_sql = array(	'products_delivery_mode_id' => (int)$chk_delivery_mode_id_loop,
																	'products_id' => $pId);
							tep_db_perform(TABLE_PRODUCTS_DELIVERY_INFO, $products_delivery_info_data_sql);
						}
						$message = "Delivery Mode updated";
					}
				}
				
				reset($product_info_array);
				foreach ($product_info_array as $product_info_id_loop) {
                    #key:top_up_info/products_id/xxx/is_supported_by_direct_top_up/
					$memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_TOP_UP_INFO . '/products_id/' . $product_info_id_loop . '/is_supported_by_direct_top_up/', 0);
					$memcache_obj->delete(TABLE_PRODUCTS_DELIVERY_INFO . '/products_delivery_mode_id/array/products_id/' . $product_info_id_loop, 0);
				}
				unset($product_info_array);
				
                #key:top_up_info/products_id/xxx/is_supported_by_direct_top_up/
				$memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_TOP_UP_INFO . '/products_id/' . $pId . '/is_supported_by_direct_top_up/', 0);
				$memcache_obj->delete(TABLE_PRODUCTS_DELIVERY_INFO . '/products_delivery_mode_id/array/products_id/' . $pId, 0);
			}
			echo "<error><![CDATA[".($error_flag? 1 : 0 )."]]></error>";
			echo "<message><![CDATA[".$message."]]></message>";
			break;
		case "cat_get_products":
			echo '<products>';
			if ((int)$cat_id > 0 ) {
				$category_array = array();
				$last_cat_id = (int)$cat_id;
				$category_array[] = $last_cat_id;
				tep_get_subcategories($category_array, $last_cat_id, '');
				$product_select_sql = '	SELECT p.products_id, d.products_name,p.products_quantity,p.products_actual_quantity 
										FROM '.TABLE_PRODUCTS.' AS p 
										INNER JOIN '.TABLE_PRODUCTS_DESCRIPTION.' AS d 
											ON p.products_id = d.products_id 
										INNER JOIN '. TABLE_PRODUCTS_TO_CATEGORIES . ' p2c 
											ON p.products_id = p2c.products_id 
										WHERE p.custom_products_type_id = 2
											AND d.language_id ='.(int)$languages_id . ' 
											AND p2c.categories_id IN ("' . implode('", "', $category_array) . '")	
											AND p2c.products_is_link = 0 
										ORDER BY p.products_sort_order, d.products_name ';
				$product_result = tep_db_query($product_select_sql);
				while ($product_row = tep_db_fetch_array($product_result)) {
					echo "	<product id='".$product_row['products_id']."'>
								<products_name><![CDATA[".strip_tags($product_row['products_name'])."]]></products_name>
								<products_quantity><![CDATA[".$product_row['products_quantity']."]]></products_quantity>
								<products_actual_quantity><![CDATA[".$product_row['products_actual_quantity']."]]></products_actual_quantity>
							</product>";
				}
			}
			echo '</products>';
			break;
		case "banners_resources":
			echo '<banners_resources>';
			if (isset($_REQUEST['selected_games_banner']) || isset($_REQUEST['selected_pm_banner'])) {
				
				if (isset($_REQUEST['selected_games_banner'])) {
					$selected_games_banner = explode(',',trim($_REQUEST['selected_games_banner']));
					$selected_games_banner = array_filter($selected_games_banner,'tep_not_null');
				}
				
				if (isset($_REQUEST['selected_pm_banner'])) {
					$selected_pm_banner = explode(',',trim($_REQUEST['selected_pm_banner']));
					$selected_pm_banner = array_filter($selected_pm_banner,'tep_not_null');
				}
				
				$games_image_select_sql = "	SELECT bs.banners_id, bs.banners_resources_key 
											FROM " . TABLE_BANNERS_RESOURCES . " as bs 
											WHERE bs.banners_resources_key IN (	'".KEY_CATEGORY_BANNER_IMAGE."',
																				'".KEY_PM_BANNER_IMAGE."')
											GROUP BY bs.banners_id";
				$games_image_result_sql = tep_db_query($games_image_select_sql);
				$games_image_array = array();
				$pm_image_array = array();
				while ($games_image_row = tep_db_fetch_array($games_image_result_sql)) {
					switch($games_image_row['banners_resources_key']) {
						case KEY_CATEGORY_BANNER_IMAGE:
							$games_image_array[$games_image_row['banners_id']] = 1;
							break;
						case KEY_PM_BANNER_IMAGE:
							$pm_image_array[$games_image_row['banners_id']] = 1;
							break;
					}
				}

                tep_db_connect_og();

                $categories_games_array = [];
                $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
                $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $categories_games_array[] = $categories_row['categories_id'];
                }
				
				$categories_games_str = implode("','", $categories_games_array);
				$selected_categories_select_sql = "	SELECT cd.categories_name, c.categories_id 
													FROM " . TABLE_CATEGORIES. " as c
													INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
														ON c.categories_id = cd.categories_id 
													WHERE c.categories_id IN ('" . $categories_games_str . "') 
														AND cd.language_id='1'
													GROUP BY c.categories_id
													ORDER BY c.sort_order, cd.categories_name";
				$selected_categories_result_sql = tep_db_query($selected_categories_select_sql);
				while ($selected_categories_row = tep_db_fetch_array($selected_categories_result_sql)) {
					echo "<games_banner id='".$selected_categories_row['categories_id']."' selected='".(in_array($selected_categories_row['categories_id'], $selected_games_banner)?1:0)."'><![CDATA[";
					if (isset($games_image_array[$selected_categories_row['categories_id']])) {
						echo strip_tags($selected_categories_row['categories_name']);
					} else {
						echo '<span class="redIndicator">'.strip_tags($selected_categories_row['categories_name']).'</span>';
					}					
					echo "]]></games_banner>";
				}
				
				$pm_description_array = payment_methods::get_all_pm_display_title();
				foreach ($pm_description_array as $pm_id_loop => $pm_value_loop) {
					echo "<pm_banner id='".$pm_id_loop."' selected='".(in_array($pm_id_loop, $selected_pm_banner)?1:0)."'><![CDATA[";
					if (isset($pm_image_array[$pm_id_loop])) {
						echo strip_tags($pm_value_loop);
					} else {
						echo '<span class="redIndicator">'.strip_tags($pm_value_loop).'</span>';
					}					
					echo "]]></pm_banner>";
				}
			}
			echo '</banners_resources>';
			break;
		case "update_banners_resources":
			
			if (isset($_REQUEST['cID']) && (int)$_REQUEST['cID']>0 && 
				(isset($_REQUEST['selected_games']) && tep_not_null(trim($_REQUEST['selected_games'])) || 
				isset($_REQUEST['selected_pm']) && tep_not_null(trim($_REQUEST['selected_pm'])))) {
				
				$cID = (int)$_REQUEST['cID'];
				
				$selected_games_banner = explode(',',trim($_REQUEST['selected_games']));
				$selected_games_banner = array_filter($selected_games_banner,'tep_not_null');
				
				$selected_pm_banner = explode(',',trim($_REQUEST['selected_pm']));
				$selected_pm_banner = array_filter($selected_pm_banner,'tep_not_null');
				
				if (count($selected_games_banner)) {
					echo '<selected_games_banners';
					$selected_games_banner_str = implode("','",$selected_games_banner);
					
					$banner_image_str = '';
					$banner_image_id_str = '';
					$banner_image_select_sql = "SELECT cd.categories_name, cd.categories_id 
												FROM " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												WHERE cd.categories_id IN ('".$selected_games_banner_str."')
												GROUP BY cd.categories_id
												ORDER BY cd.categories_name";
					$banner_image_result_sql = tep_db_query($banner_image_select_sql);
					while ($banner_image_row = tep_db_fetch_array($banner_image_result_sql)) {
						if (tep_not_null($banner_image_str)) {
							$banner_image_str .= ", " . $banner_image_row['categories_name'];
							$banner_image_id_str .= "," . $banner_image_row['categories_id'];
						} else {
							$banner_image_str .= $banner_image_row['categories_name'];
							$banner_image_id_str .= $banner_image_row['categories_id'];
						}
					}
					echo ' id="'.$banner_image_id_str.'"><![CDATA[';
					echo htmlentities($banner_image_str);
					echo ']]></selected_games_banners>';
				}
				
				if (count($selected_pm_banner)) {
					echo '<selected_pm_banners';

					$banner_image_str = '';
					$banner_image_id_str = '';
					$pm_description_array = payment_methods::get_all_pm_display_title();
					foreach ($selected_pm_banner as $pm_banner_loop) {
						if (isset($pm_description_array[$pm_banner_loop])) {
							if (tep_not_null($banner_image_str)) {
								$banner_image_str .= ", " . $pm_description_array[$pm_banner_loop];
								$banner_image_id_str .= "," . $pm_banner_loop;
							} else {
								$banner_image_str .=  $pm_description_array[$pm_banner_loop];
								$banner_image_id_str .= $pm_banner_loop;
							}
						}
					}					
					echo ' id="'.$banner_image_id_str.'"><![CDATA[';
					echo htmlentities($banner_image_str);
					echo ']]></selected_pm_banners>';
				}
			}
			
			break;
		case 'get_discount_settings':
			$categories_id = tep_db_prepare_input($_REQUEST['category_id']);
			$category_rebate_info_array = tep_get_categories_discount($categories_id);
			header('Content-Type: text/html');
			echo '<table border=1>
					<tr>
						<td>Customer Group</td>
						<td>Discount Rate</td>
						<td>Rebate Rate</td>
					</tr>';
			foreach ($category_rebate_info_array as $categories) {			
			echo '
					<tr>
						<td>' . $categories['customers_groups_name'] . '</td>
						<td>' . $categories['cust_group_discount'] . '</td>
						<td>' . $categories['cust_group_rebate'] . '</td>
					</tr>';
			}
			echo '</table>';
			break;	
		case 'get_final_cost':
			$error_flag = true;
			$message = 'Error Currency Exchange';
			$products_cost = 0;
			// get all var
			$original_cost = isset($_REQUEST['o_cost']) ? $_REQUEST['o_cost'] : '';
            $original_curr = isset($_REQUEST['o_curr']) ? $_REQUEST['o_curr'] : '';
            $exchange_curr = isset($_REQUEST['e_curr']) ? $_REQUEST['e_curr'] : '';

            if (tep_not_null($original_cost) && tep_not_null($original_curr) && tep_not_null($exchange_curr)) {
            	$error_flag = false;

            	if ($original_curr != $exchange_curr) {
            		if ($original_curr == DEFAULT_CURRENCY) {
            			$products_cost = $currencies->advance_currency_conversion($original_cost, $original_curr, $exchange_curr, false, 'sell');	
            		} else {
            			$products_cost = $currencies->advance_currency_conversion($original_cost, $original_curr, $exchange_curr, false, 'buy');
            		}
            	}
            	echo "<products_cost><![CDATA[".$products_cost."]]></products_cost>";
            }

			echo "<error><![CDATA[".($error_flag? 1 : 0 )."]]></error>";
			echo "<message><![CDATA[".$message."]]></message>";
			break;
	}
}
echo '</response>';
?>