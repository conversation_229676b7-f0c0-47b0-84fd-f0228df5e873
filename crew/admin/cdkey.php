<?php
require('includes/application_top.php');
if (isset($_GET['keyident'])) tep_show_base64_img($_GET['keyident']);
require(DIR_WS_CLASSES.'base64_converter.php');
require(DIR_WS_CLASSES . 'log.php');
require(DIR_WS_FUNCTIONS.'custom_product.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'edit_purchase_orders.php');
require_once(DIR_WS_CLASSES . 'custom_product_code.php');
require_once(DIR_WS_CLASSES . 'purchase_control_tool.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

tep_set_time_limit(400);

//------Start helper functions
	/**
	 * Info for a particular code
	 */
	function tep_get_gInfo($curr_id)
	{
		global $gInfo, $languages_id;
		
		if ($curr_id) {
			$cdkey_select_sql = "	SELECT a.custom_products_code_id, a.products_id, a.status_id, a.file_name, a.file_type,
										a.code_date_added, a.code_date_modified, a.code_uploaded_by, a.purchase_orders_id,
										a.remarks, b.products_description, b.products_name, p2c.categories_id
									FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " a
									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " b
										ON b.products_id=a.products_id
									INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c
										ON p2c.products_id=a.products_id
									WHERE a.custom_products_code_id = '".$curr_id."'
										AND p2c.products_is_link = 0
										AND b.language_id = '".(int)$languages_id."'";
			$cdkey_result_sql = tep_db_query($cdkey_select_sql);
			if ($cdkey_row = tep_db_fetch_array($cdkey_result_sql)) {
				$gInfo = new objectInfo($cdkey_row);
			}
		}
	}
	/**
	 * Info for a particular code
	 */
	function tep_get_status_qty($status_arr,$filterby_p) {
		global $languages_id;

		$status_qty_arr = array();

 		foreach($status_arr as $status_id => $status_value){
			$cdkey_qty_select_sql = "	SELECT count(custom_products_code_id) AS status_qty
										FROM ".TABLE_CUSTOM_PRODUCTS_CODE. "
										WHERE status_id = '".tep_db_input($status_id)."'
											AND products_id='".tep_db_input($filterby_p)."'";
			$cdkey_qty_result_sql = tep_db_query($cdkey_qty_select_sql);
			$cdkey_qty_row = tep_db_fetch_array($cdkey_qty_result_sql);
			$status_qty_arr[$status_id] = $cdkey_qty_row['status_qty'];
 		}

		$cdkey_qty_select_sql = "	SELECT sum(zip_qty) as status_qty
									FROM " . TABLE_CUSTOM_PRODUCTS_VAULT . "
									WHERE zip_status != '3'
										AND products_id = '".tep_db_input($filterby_p)."'";
		$cdkey_qty_result_sql = tep_db_query($cdkey_qty_select_sql);
		$cdkey_qty_row = tep_db_fetch_array($cdkey_qty_result_sql);

		if ($cdkey_qty_row['status_qty']) {
			$status_qty_arr[2] = $cdkey_qty_row['status_qty'];
		} else {
			$status_qty_arr[2] = 0;
		}
 		return ($status_qty_arr);
	}

	/**
	 * Get a subset of cdkey codes
	 */
	function tep_get_cdkey_products()
	{
		global $languages_id, $cdkey_products;

		if (!count($cdkey_products)) {
			$product_select_sql = '	select p.products_id, d.products_name,p.products_quantity,p.products_actual_quantity, p2c.categories_id
									from '.TABLE_PRODUCTS.' AS p, '.TABLE_PRODUCTS_DESCRIPTION.' AS d, '. TABLE_PRODUCTS_TO_CATEGORIES . ' p2c
									where p.custom_products_type_id=2
										and p2c.products_is_link = 0
										and d.language_id ='.(int)$languages_id.'
										and p.products_id = d.products_id
										and p.products_id = p2c.products_id';
			$product_select_sql .= ' order by p.products_sort_order, d.products_name ';

			$product_result = tep_db_query($product_select_sql);
			while ($product_row = tep_db_fetch_array($product_result)) {
				$cdkey_products[] = $product_row;
			}
		}
		return $cdkey_products;
	}
	/**
	 * Draw the products select list
	 **/
	function tep_get_cdkey_product_object()
	{
		global $languages_id, $cdkey_products;

		$products_array = array();

		if (!$cdkey_products) tep_get_cdkey_products();
		foreach ($cdkey_products as $row) {
			$catid = $row['categories_id'];
			$text = strip_tags($row['products_name']);
			if (tep_not_null($row['products_quantity'])) $text .= ' Available: '.$row['products_quantity'];

			if (tep_not_null($row['products_actual_quantity'])) $text .= ' Actual: '.$row['products_actual_quantity'];

			$products_array[$catid][] = array('id' => $row['products_id'] , 'text' => $text);
		}

		$products_array ["categoryid"] = array_keys($products_array);

		return $products_array;
	}
	/**
	 * Draw the Category select list
	 **/
	function get_cdkey_category_selection() {

		$categories_select_sql = "	SELECT categories_id
									FROM categories
									WHERE custom_products_type_id=2";
		$categories_result_sql = tep_db_query($categories_select_sql);
		$categories_temp_array = array();
		while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
			$text = strip_tags(tep_output_generated_category_path_sq($categories_row['categories_id']));
			$categories_temp_array[$categories_row['categories_id']] = $text;
		}
		asort($categories_temp_array);

		$categories_array = array();
		foreach ($categories_temp_array as $categories_temp_id_loop => $categories_temp_data_loop) {
			$category_array[] = array('id' => $categories_temp_id_loop, 'text' => $categories_temp_data_loop);
		}
		return $category_array;
	}

	/**
	 * Get path parts
	 **/
	function tep_get_path_parts($fullpath)
	{
		$parts = pathinfo($fullpath);
		$path = $parts['dirname'];
		$file = basename($parts['basename'], '.'.$parts['extension']);
		$extension = $parts['extension'];
		return array($path, $file, $extension);
	}

	/**
	 * Get the product's cat path
	 */
	function tep_get_this_prod_cat_path($productID)
	{
		$product_2_category_result_sql = tep_db_query("	select categories_id
													from " . TABLE_PRODUCTS_TO_CATEGORIES . "
													where products_id = '" . $productID . "'
														and products_is_link=0 ");

		$product_2_category_row = tep_db_fetch_array($product_2_category_result_sql);
		$this_prod_cat_path = tep_output_category_nav_path($product_2_category_row["categories_id"], '', '', false);
		return $this_prod_cat_path;
	}

	/**
	 * Check if product id exist.
	 **/
	function tep_isProductIDExist($id)
	{
		global $messageStack;

		$select_sql = "select products_id
				from " . TABLE_PRODUCTS . "
				where products_id = '".(int)$id . "'";
		$result = tep_db_query($select_sql);
		if (!tep_db_num_rows($result)) {
			$messageStack->add(ERROR_INVALID_PRODUCTID, 'error');
			return false;
		}

		return true;
	}

	/**
	 * Add remarks to CD Key
	 */
	function tep_add_Remarks($custom_product_id,$remarks)
	{
		global $messageStack, $log_object;

		$log_object->insert_cdkey_history_log('admin', $custom_product_id, $remarks);
	}
	/**
	 * Update cdkey stock qty
	 */
	function tep_update_cdkey_stock_qty($productID, $offset='+1', $custom_products_code_id)
	{
		global $messageStack, $log_object, $purchase_control_tool_object;

		$products_quantity_select_sql = "	select products_quantity, products_actual_quantity
											from ".TABLE_PRODUCTS."
											where products_id=$productID";

		$products_quantity_result_sql = tep_db_query($products_quantity_select_sql);
		if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
			$old_prod_available_qty = $products_quantity_row['products_quantity'];
			$old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
			$new_prod_available_qty = $old_prod_available_qty + ($offset);
			$new_prod_actual_qty = $old_prod_actual_qty + ($offset);

			$product_sql = "UPDATE ".TABLE_PRODUCTS." SET products_quantity=IF(products_quantity IS NULL, 0, products_quantity) $offset, products_actual_quantity=IF(products_actual_quantity IS NULL, 0, products_actual_quantity) $offset WHERE products_id =".$productID;
			$status = tep_db_query($product_sql);
			if (!$status)
				$messageStack->add(sprintf(ERROR_UPDATE_STOCKQTY_FAILED, tep_get_products_name($productID), $productID), 'error');
			else {
                            # Update out of stock flag
                            $purchase_control_tool_object->updateOutOfStockFlag($productID, $new_prod_available_qty);

                            $messageStack->add(sprintf(SUCCESS_MESSAGE_UPDATE_STOCKQTY, tep_get_products_name($productID), $productID), 'success');
                            $log_object->insert_log((int)$productID, 'products_quantity', $old_prod_available_qty.':~:'.$old_prod_actual_qty, $new_prod_available_qty.':~:'.$new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''), sprintf(LOG_CDKEY_ID_STR, $custom_products_code_id));
			}
			return array($old_prod_actual_qty, $new_prod_actual_qty);
		}
	}

//------End helper functions

//------Start processing functions

	/**
	 * Upload and encode the file, then either insert or update db
	 */
	function tep_process_upload($upload_obj , $custom_products_code_id=0)
	{
		global $file_upload_types, $messageStack;

		if (!is_dir(DIR_FS_SECURE)) {
			$messageStack->add_session(ERROR_DESTINATION_NOT_WRITEABLE, 'error');
		} else {
			$upload = new upload('upload_file');
			$upload->set_extensions($file_upload_types);
			$upload->set_destination(DIR_FS_SECURE_TEMP);
			$upload->set_output_messages('session');

    		if ($upload->parse()) {
    			$filetype = strtolower(array_pop(explode("." , $upload->filename)));
				$upload_obj['file_size'] = $upload->file['size'];

				if ($filetype == "zip" && empty($upload_obj['zip_qty']))
					$messageStack->add(ERROR_INVALID_ZIP_QTY, 'error');
				if ($filetype == "zip" && empty($upload_obj['zip_unit_price']))
					$messageStack->add(ERROR_INVALID_ZIP_UNIT_PRICE, 'error');
                if (($filetype == "jpg" || $filetype == "gif" || $filetype == "png") && $upload_obj['upload_to'] != "jpg")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
				else if ($filetype == "zip" && $upload_obj['upload_to'] == "jpg")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
                else if ($filetype != "jpg" && $filetype != "zip" && $filetype != "gif" && $filetype != "png")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
				else {
		    		$upload->save();

		   			if (file_exists($filepath=$upload->destination.$upload->filename) && (!is_dir($filepath))){

						$params = array();
						$params['filename'] = $filepath;
						$base64enc_obj = new base64_converter($params,'2');
						$files_arr = $base64enc_obj->get_files();

						unset($base64enc_obj);

						// upload type
						// 1 - Upload jpg directly to live, on hold status
						// 2 - Upload zip to vault
						// 3 - Upload zip to vault and extract it to live, on hold status

                        if (($filetype == "jpg" ||$filetype == "gif" || $filetype == "png") && $upload_obj['upload_to'] == "jpg")
                            tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'1',$filetype);
						else if ($filetype == "zip") {
							if ($upload_obj['upload_to'] == "manage_vault")
								tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'2');
							else if ($upload_obj['upload_to'] == "live")
								tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'3');
						}

						unset($files_arr);

						if (file_exists($filepath) && tep_not_null($filepath))
						unlink($filepath);
					}
				}
    		}
		}
	}
	/**
	 * Upload and encode the file via a purchase order, then either insert or update db
	 */
	function tep_process_upload_via_po($upload_obj , $custom_products_code_id=0)
	{
		global $file_upload_types, $messageStack;

		if (!is_dir(DIR_FS_SECURE)) {
			$messageStack->add_session(ERROR_DESTINATION_NOT_WRITEABLE, 'error');
		} else {
			$upload = new upload('upload_file');
			$upload->set_extensions($file_upload_types);
			$upload->set_destination(DIR_FS_SECURE_TEMP);
			$upload->set_output_messages('session');

    		if ($upload->parse()) {
    			$filetype = strtolower(array_pop(explode("." , $upload->filename)));
				$upload_obj['file_size'] = $upload->file['size'];

				if ($filetype == "zip" && empty($upload_obj['zip_qty']))
					$messageStack->add(ERROR_INVALID_ZIP_QTY, 'error');
                if (($filetype == "jpg" || $filetype == "gif" || $filetype == "png") && $upload_obj['po_upload_to'] != "jpg")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
				else if ($filetype == "zip" && $upload_obj['po_upload_to'] == "jpg")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
                else if ($filetype != "jpg" && $filetype != "zip" && $filetype != "gif" && $filetype != "png")
    				$messageStack->add(ERROR_ZIP_FILE_OPTION, 'error');
				else {
		    		$upload->save();

		   			if (file_exists($filepath=$upload->destination.$upload->filename) && (!is_dir($filepath))){

						$params = array();
						$params['filename'] = $filepath;
						$base64enc_obj = new base64_converter($params,'2');
						$files_arr = $base64enc_obj->get_files();

						unset($base64enc_obj);

						// upload type
						// 1 - Upload jpg directly to live, on hold status
						// 2 - Upload zip to vault
						// 3 - Upload zip to vault and extract it to live, on hold status

                        if (($filetype == "jpg" || $filetype == "gif" || $filetype == "png") && $upload_obj['po_upload_to'] == "jpg"){
                            if ($keycode_array = tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'1',$filetype)) {
                                                            tep_recalculate_out_of_stock_qty($upload_obj, $keycode_array);
                                                        }
                                                }
						else if ($filetype == "zip") {
							// if upload via PO, use PO unit price as uploaded unit price
							if (isset($upload_obj['po_number']) && tep_not_null($upload_obj['po_number'])) {
								$upload_obj['zip_unit_price'] = 0;
							}

							if ($upload_obj['po_upload_to'] == "manage_vault"){
								if ($keycode_array = tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'2')) {
                                                                    tep_recalculate_out_of_stock_qty($upload_obj, $keycode_array);
                                                                }
                                                        }
							else if ($upload_obj['po_upload_to'] == "live"){
								if ($keycode_array = tep_manage_keycode($files_arr, $upload_obj, $custom_products_code_id,'3')) {
                                                                    tep_recalculate_out_of_stock_qty($upload_obj, $keycode_array);
                                                                }
                                                        }
						}

						unset($files_arr);

						if (file_exists($filepath) && tep_not_null($filepath))
						unlink($filepath);
					}
				}
    		}
		}
	}

        function tep_recalculate_out_of_stock_qty($upload_info, $keycode_array){
            global $messageStack, $purchase_control_tool_object, $log_object;

            $pid = isset($upload_info['filterby_p_up']) ? $upload_info['filterby_p_up'] : 0;
            $upload_qty = isset($keycode_array['cdkey_id_array']) ? count($keycode_array['cdkey_id_array']) : 0;

            if($pid && $upload_qty) {
                $available_qty = 0;

                $products_extra_info_query = tep_db_query("SELECT products_extra_info_value from " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_id= '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'");
                if ($products_extra_info = tep_db_fetch_array($products_extra_info_query)) {
                    $available_qty = $products_extra_info['products_extra_info_value'];
                }

                $new_prod_available_qty = $purchase_control_tool_object->recalculate_out_of_stock_qty_by_upload_qty($pid, $upload_qty, $messageStack);

                if ($new_prod_available_qty) {
                    $log_object->insert_log($pid, 'stock_quantity', $available_qty, $new_prod_available_qty, LOG_PRODUCT_STOCK_QUANTITY_UPDATE, '', $_SESSION['login_email_address']);
                }
            }
        }

/**
	 * Unzip and insert keycode
	 */
	function tep_manage_keycode_unzip($products_vault_id,$zip_password) {
		global $messageStack, $login_email_address, $cpc_obj;

		$errorStatus = 0;

		$vault_select_sql = "	SELECT count(products_vault_id) as total_unzipping
								FROM " . TABLE_CUSTOM_PRODUCTS_VAULT . "
								WHERE zip_status = '2'";
		$vault_result_sql = tep_db_query($vault_select_sql);
		$vault_row = tep_db_fetch_array($vault_result_sql);

		if ($vault_row['total_unzipping'] >= MAX_UNZIPPING_RUNNING) {
			$messageStack->add_session(ERROR_MAX_UNZIPPING_RUNNING, 'error');
			$errorStatus++;
		} else {
			$timestamp = date('Y-m-d H:i:s');

			$md5_vault_id = md5($products_vault_id);

			$vault_select_sql = "	SELECT products_id,zip_qty,zip_unit_price,purchase_orders_id
										FROM " . TABLE_CUSTOM_PRODUCTS_VAULT . "
										WHERE zip_status = '1'
											AND products_vault_id = '$products_vault_id'";
			$vault_result_sql = tep_db_query($vault_select_sql);
			$vault_row = tep_db_fetch_array($vault_result_sql);
			$product_id = $vault_row['products_id'];
			$po_id = $vault_row['purchase_orders_id'];

			if (file_exists(DIR_FS_SECURE.$products_vault_id.'.keyzip')) {
				$fh = fopen(DIR_FS_SECURE.$products_vault_id.'.keyzip', 'r') or die("can't open file");
				$theData = fread($fh, filesize(DIR_FS_SECURE.$products_vault_id.'.keyzip'));
				fclose($fh);
			}

			$theData = tep_decrypt_data($theData);

			$fh = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip', 'w') or die("can't open file");
			fwrite($fh, $theData);
			fclose($fh);

			$cmd = "unzip -u -qq -P '".$zip_password."' ".DIR_FS_SECURE_TEMP.$md5_vault_id.".zip -d ".DIR_FS_SECURE_TEMP.$md5_vault_id."/";
			$last_line = system($cmd, $retval);

			if ($retval == 0) {
				$email_notify_data_arr = array();
				$email_notify_data_arr['action_type'] = STOCK_CUSTPROD_EMAIL_ACTION_UNZIP;
				$email_notify_data_arr['comments'] = '';

				// Update Zip Vault to 2 - unzipping status
				$vault_select_sql = "UPDATE " . TABLE_CUSTOM_PRODUCTS_VAULT . "
						SET zip_status = '2', zip_unzip_by='".$login_email_address."', zip_date_unzipping='".$timestamp."'
						WHERE products_vault_id='".$products_vault_id."'";
				$vault_result_sql = tep_db_query($vault_select_sql);

				$dh = opendir(DIR_FS_SECURE_TEMP.$md5_vault_id."/");

				while (($file = readdir($dh)) !== false) {
					if ($file != "." && $file != ".." && tep_not_null($file)) {
						$file_array = array();
						$file_array = explode ("." , $file);

						$dir_file_type = strtolower(array_pop($file_array));

                        if ($dir_file_type == "jpg" || $dir_file_type == "gif" || $dir_file_type == "png"){
							$fh = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file , 'r') or die("can't open file");
							$theData = fread($fh, filesize(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file));
							fclose($fh);

							$code_select_sql = sprintf(	'	INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (custom_products_code_id, products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
											 				VALUES (0, "%s", "%s", "%s", "%s", "'.$timestamp.'", "'.$timestamp.'", "'.$login_email_address.'", "", "%s")',
					                     				$product_id,-2, $file, $dir_file_type, $po_id);
							$result_sql = tep_db_query($code_select_sql);

							if (!$result_sql) {
								$messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,' ', 'error'));
								$errorStatus++;
							}
							else {
								$custom_products_code_id = tep_db_insert_id();

								$theData = base64_encode($theData);
								$theData = tep_encrypt_data($theData);

                                if ($cpc_obj->uploadCode($theData, $custom_products_code_id, $product_id, $timestamp) === TRUE) {
                                    $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,'', $custom_products_code_id), 'success');
                                } else {
                                    $messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,' ', 'error'));
                                    $errorStatus++;
                                }
							}
						} else if ($dir_file_type == "csv"){
							$csv_data_object = array();
							$csv_header_array = array();

							$handle = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file , 'r') or die("can't open file");
							while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
								if ($csv_row == 0)
									$csv_header_array = $data;
								else
									$csv_data_object[] = $data;

								$csv_row++;
							}
							fclose($handle);

							foreach ($csv_data_object as $csv_data_row) {
								$col_count = 0;
								$key_string = "";
								foreach ($csv_header_array as $csv_header_row) {
									$key_string .= $csv_header_row." : ".$csv_data_row[$col_count]."<br>";
									$col_count++;
								}

								$key_string = base64_encode($key_string);
								$key_string = tep_encrypt_data($key_string);

								$code_select_sql = sprintf(	'INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (custom_products_code_id, products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
												 	VALUES (0, "%s", "%s", "%s", "%s", "'.$timestamp.'", "'.$timestamp.'", "'.$login_email_address.'", "", "%s")',
						                    		$product_id,-2, $file, 'soft', $po_id);
								$code_result_sql = tep_db_query($code_select_sql);

								if (!$code_result_sql) {
									$messageStack->add_session(ERROR_INSERT_CODE_FAILED, 'error');
									$errorStatus++;
								} else {
									$custom_products_code_id = tep_db_insert_id();

                                    if ($cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_id, $timestamp) === TRUE) {
                                        $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,'', $custom_products_code_id), 'success');
                                    } else {
                                        $messageStack->add_session(ERROR_INSERT_CODE_FAILED, 'error');
                                        $errorStatus++;
                                    }
								}
							}
						} // End csv

						if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file) && tep_not_null($file))
							unlink(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file);
					}
				}
				closedir($dh);

				// Update Zip Vault to 3 - unzipped status
				$vault_select_sql = "	UPDATE " . TABLE_CUSTOM_PRODUCTS_VAULT . "
										SET zip_status = '3', zip_unzip_by='".$login_email_address."', zip_date_unzipping='".$timestamp."'
										WHERE products_vault_id='".$products_vault_id."'";
				$vault_result = tep_db_query($vault_select_sql);

				if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id."/") && tep_not_null($md5_vault_id))
					rmdir (DIR_FS_SECURE_TEMP.$md5_vault_id."/");

				if (file_exists(DIR_FS_SECURE.$products_vault_id.'.keyzip') && tep_not_null($products_vault_id))
					unlink(DIR_FS_SECURE.$products_vault_id.'.keyzip');
			}
			else {
				$messageStack->add_session(ERROR_INVALID_ZIP_PASSWORD,'error');
				$errorStatus++;
			}

			if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip') && tep_not_null($md5_vault_id))
				unlink(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip');
		}

		if ($email_notify_data_arr) {
			$productID = $product_id;
			$status_arr = array('1'=>LINK_FILTER_BY_ACTUAL);
			$qty_temp = tep_get_status_qty($status_arr,$productID);

			$email_notify_data_arr['old_prod_actual_qty'] = $qty_temp[1];
			$email_notify_data_arr['new_prod_actual_qty'] = $qty_temp[1];
			tep_send_custom_product_stock_adjustment_email($email_notify_data_arr, $productID, 0, 2, tep_get_this_prod_cat_path($productID));
		}

		return ($errorStatus);
	}

	/**
	 * Manage update or insert keycode
	 */
	function tep_manage_keycode(&$files_arr, $upload_obj, $custom_products_code_id, $uploadtype, $fileType = 'jpg')
	{
		global $messageStack, $login_email_address, $log_object, $cpc_obj;

                $cdkey_array = array();

		if (!tep_isProductIDExist($upload_obj['filterby_p_up'])) return;
		$timestamp = date('Y-m-d H:i:s');
		$maxlength = pow(2, 32)-1;

		if ($custom_products_code_id) {
			//Update mode
		} else {
			//Insert mode
			$offset = '+1';
			$cnt = 0;
			$email_notify_data_arr = array();
			$email_notify_data_arr['action_type'] = STOCK_CUSTPROD_EMAIL_ACTION_FILEUPLOAD;
			$email_notify_data_arr['comments'] = '';

			foreach($files_arr as $row) {
				list($dummy, $name_str, $type_str) = tep_get_path_parts($row['name']);
				//Check length of base64 encoding - if exceed database column limit (LONGTEXT)

				if (strlen($row['source_base64']) <= $maxlength) {
					// upload type
					// 1 - Upload jpg directly to live, on hold status
					// 2 - Upload zip to vault
					// 3 - Upload zip to vault and extract it to live, on hold status

					$send_upload_cdkey_email = false;

					if ($uploadtype == "1") {
						$send_upload_cdkey_email = true;

						$code_select_sql = sprintf(	'INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (custom_products_code_id, products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
										 VALUES (0, "%s", "%s", "%s", "%s", "'.$timestamp.'", "'.$timestamp.'", "'.$login_email_address.'", "", "%s")',
				                     	 $upload_obj['filterby_p_up'],-2, $name_str.'.'.$type_str, $fileType , $upload_obj['po_id']);
						$code_result_sql = tep_db_query($code_select_sql);

						if (!$code_result_sql) {
							$messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,$row['name']), 'error');
						} else {
							$custom_products_code_id = tep_db_insert_id();

                                                        // SET CDKEY FOR PAYMENT WITHDRAWAL
                                                        $supplier_sql = "SELECT supplier_id FROM " . TABLE_PURCHASE_ORDERS . "
                                                                        WHERE payment_type = 'g'
                                                                        AND purchase_orders_id = '" . $upload_obj['po_id'] . "'
                                                                        LIMIT 1";
                                                        $supplier_result = tep_db_query($supplier_sql);
                                                        if ($supplier_row = tep_db_fetch_array($supplier_result)) {
                                                            $supplier_id = $supplier_row['supplier_id'];
                                                            $cdkey_withdrawal = array(
                                                                'custom_products_code_id' => $custom_products_code_id,
                                                                'supplier_id' => $supplier_id
                                                            );
                                                            tep_db_perform(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, $cdkey_withdrawal);
                                                        }

							$theData = tep_encrypt_data($row['source_base64']);

                            if ($cpc_obj->uploadCode($theData, $custom_products_code_id, $upload_obj['filterby_p_up'], $timestamp) === TRUE) {
                                $cdkey_array[] = $custom_products_code_id;
                                $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE, basename($row['name']), $custom_products_code_id), 'success');
                            } else {
                                $messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED, $row['name']), 'error');
                            }
						}
					} else if ($uploadtype == "2" || $uploadtype == "3") {
						$send_upload_cdkey_email = true;

						$vault_select_sql = "INSERT INTO " . TABLE_CUSTOM_PRODUCTS_VAULT . " (products_id,file_name,file_type,zip_qty,zip_unit_price,zip_date_added,zip_date_modified,zip_uploaded_by,zip_unzip_by,zip_status,remarks,file_size,purchase_orders_id) " . "
										 VALUES ('".$upload_obj['filterby_p_up']."','$name_str\.$type_str','$type_str','".$upload_obj['zip_qty']."','".$upload_obj['zip_unit_price']."','".$timestamp."','".$timestamp."','$login_email_address','','1','".mysql_real_escape_string($upload_obj['zip_description'])."','".$upload_obj['file_size']."','".$upload_obj['po_id']."')";
						$vault_result = tep_db_query($vault_select_sql);

						if (!$vault_result) {
							$messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,$row['name']), 'error');
						} else {
							$products_vault_id = tep_db_insert_id();

							//------------------------------------------------------------------
							// 2 - Upload zip to vault
							//------------------------------------------------------------------

							if ($uploadtype == "2") {
								$theData = tep_encrypt_data($row['source_base64']);

								$fh = fopen(DIR_FS_SECURE.$products_vault_id.'.keyzip', 'w') or die("can't open file");
								fwrite($fh, $theData);
								fclose($fh);

								@chmod(DIR_FS_SECURE.$products_vault_id.'.keyzip', 0600);

								$zipFile = base64_decode($row['source_base64']);
								$md5_vault_id = md5($products_vault_id);

								$fh = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip', 'w') or die("can't open file");
								fwrite($fh, $zipFile);
								fclose($fh);

								$cmd = "unzip -t -qq -P '' ".DIR_FS_SECURE_TEMP.$md5_vault_id.".zip";
								$last_line = system($cmd, $retval);

								if ($retval == "0"){
									global $messageStack;
									$messageStack->add_session(ERROR_ZIP_NOT_PASSWORD_PROTECTED, 'error');

									$vault_select_sql = "DELETE FROM " . TABLE_CUSTOM_PRODUCTS_VAULT . " WHERE products_vault_id = '$products_vault_id'";
									$vault_result = tep_db_query($vault_select_sql);

									if (file_exists(DIR_FS_SECURE.$products_vault_id.'.keyzip') && tep_not_null($products_vault_id))
										unlink(DIR_FS_SECURE.$products_vault_id.'.keyzip');
								}
								else {
									$messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,basename($row['name']), $products_vault_id), 'success');
								}

								if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip') && tep_not_null($md5_vault_id))
									unlink(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip');
							} else if ($uploadtype == "3") {
								$zipFile = base64_decode($row['source_base64']);
								$md5_vault_id = md5($products_vault_id);

								$fh = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip', 'w') or die("can't open file");
								fwrite($fh, $zipFile);
								fclose($fh);

								$cmd = "unzip -u -qq ".DIR_FS_SECURE_TEMP.$md5_vault_id.".zip -d ".DIR_FS_SECURE_TEMP.$md5_vault_id."/";
								$last_line = system($cmd, $retval);

								if ($retval == 0) {
									// Update Zip Vault to 2 - unzipping status
									$vault_select_sql = "UPDATE " . TABLE_CUSTOM_PRODUCTS_VAULT . "
														SET zip_status = '2', zip_unzip_by='".$login_email_address."', zip_date_unzipping='".$timestamp."'
														WHERE products_vault_id='".$products_vault_id."'";
									$vault_result = tep_db_query($vault_select_sql);

									$dh = opendir(DIR_FS_SECURE_TEMP.$md5_vault_id."/");

									while (($file = readdir($dh)) !== false) {
										if ($file != "." && $file != ".." && tep_not_null($file)) {
											$file_array = array();
											$file_array = explode ("." , $file);

											$dir_file_type = strtolower(array_pop($file_array));

                                            if ($dir_file_type == "jpg" || $dir_file_type == "png" ||$dir_file_type == "gif"){
												$fh = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file , 'r') or die("can't open file");
												$theData = fread($fh, filesize(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file));
												fclose($fh);

												$code_select_sql = sprintf(	'	INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (custom_products_code_id, products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
																 					VALUES (0, "%s", "%s", "%s", "%s", "'.$timestamp.'", "'.$timestamp.'", "'.$login_email_address.'", "", "%s")',
										        $upload_obj['filterby_p_up'],-2, $file, $dir_file_type, $upload_obj['po_id']);
												$code_result = tep_db_query($code_select_sql);

												if (!$code_result) {
													$messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,' ', 'error'));
												} else {
													$custom_products_code_id = tep_db_insert_id();

                                                                                                        // SET CDKEY FOR PAYMENT WITHDRAWAL
                                                                                                        $supplier_sql = "SELECT supplier_id FROM " . TABLE_PURCHASE_ORDERS . "
                                                                                                                        WHERE payment_type = 'g'
                                                                                                                        AND purchase_orders_id = '" . $upload_obj['po_id'] . "'
                                                                                                                        LIMIT 1";
                                                                                                        $supplier_result = tep_db_query($supplier_sql);
                                                                                                        if ($supplier_row = tep_db_fetch_array($supplier_result)) {
                                                                                                            $supplier_id = $supplier_row['supplier_id'];
                                                                                                            $cdkey_withdrawal = array(
                                                                                                                'custom_products_code_id' => $custom_products_code_id,
                                                                                                                'supplier_id' => $supplier_id
                                                                                                            );
                                                                                                            tep_db_perform(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, $cdkey_withdrawal);
                                                                                                        }

													$theData = base64_encode($theData);
													$theData = tep_encrypt_data($theData);

                                                    if ($cpc_obj->uploadCode($theData, $custom_products_code_id, $upload_obj['filterby_p_up'], $timestamp) === TRUE) {
                                                        $cdkey_array[] = $custom_products_code_id;
                                                        $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,'', $custom_products_code_id), 'success');
                                                    } else {
                                                        $messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,' ', 'error'));
                                                    }
												}
											} else if ($dir_file_type == "csv") {
												$csv_data_object = array();
												$csv_header_array = array();

												$handle = fopen(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file , 'r') or die("can't open file");
												while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
													if ($csv_row == 0)
														$csv_header_array = $data;
													else
														$csv_data_object[] = $data;

													$csv_row++;
												}
												fclose($handle);

												foreach ($csv_data_object as $csv_data_row) {
													$col_count = 0;
													$key_string = "";
													foreach ($csv_header_array as $csv_header_row) {
														$key_string .= $csv_header_row." : ".$csv_data_row[$col_count]."<br>";
														$col_count++;
													}

													$key_string = base64_encode($key_string);
													$key_string = tep_encrypt_data($key_string);

													$code_select_sql = sprintf(	'INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (custom_products_code_id, products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
																	 VALUES (0, "%s", "%s", "%s", "%s", "'.$timestamp.'", "'.$timestamp.'", "'.$login_email_address.'", "", "%s")',
											                    $upload_obj['filterby_p_up'],-2, $file, 'soft', $upload_obj['po_id']);
													$code_result = tep_db_query($code_select_sql);

													if (!$code_result) {
														$messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,'', 'error'));
													} else {
														$custom_products_code_id = tep_db_insert_id();

                                                                                                                // SET CDKEY FOR PAYMENT WITHDRAWAL
                                                                                                                $supplier_sql = "SELECT supplier_id FROM " . TABLE_PURCHASE_ORDERS . "
                                                                                                                                WHERE payment_type = 'g'
                                                                                                                                AND purchase_orders_id = '" . $upload_obj['po_id'] . "'
                                                                                                                                LIMIT 1";
                                                                                                                $supplier_result = tep_db_query($supplier_sql);
                                                                                                                if ($supplier_row = tep_db_fetch_array($supplier_result)) {
                                                                                                                    $supplier_id = $supplier_row['supplier_id'];
                                                                                                                    $cdkey_withdrawal = array(
                                                                                                                        'custom_products_code_id' => $custom_products_code_id,
                                                                                                                        'supplier_id' => $supplier_id
                                                                                                                    );
                                                                                                                    tep_db_perform(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, $cdkey_withdrawal);
                                                                                                                }

                                                        if ($cpc_obj->uploadCode($key_string, $custom_products_code_id, $upload_obj['filterby_p_up'], $timestamp) === TRUE) {
                                                            $cdkey_array[] = $custom_products_code_id;
                                                            $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,'', $custom_products_code_id), 'success');
                                                        } else {
                                                            $messageStack->add_session(sprintf(ERROR_INSERT_CODE_FAILED,'', 'error'));
                                                        }
													}
												}
											} // End csv

											if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file) && tep_not_null($file))
												unlink(DIR_FS_SECURE_TEMP.$md5_vault_id."/".$file);
										}
									}
									closedir($dh);

									// Update Zip Vault to 3 - unzipped status
									$vault_select_sql = "UPDATE " . TABLE_CUSTOM_PRODUCTS_VAULT . "
														SET zip_status = '3', zip_unzip_by='".$login_email_address."', zip_date_unzipping='".$timestamp."'
														WHERE products_vault_id='".$products_vault_id."'";
									$vault_result = tep_db_query($vault_select_sql);


									if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id."/") && tep_not_null($products_vault_id))
										rmdir (DIR_FS_SECURE_TEMP.$md5_vault_id."/");
								}
								else {
									// Update Zip Vault to 2 - unzipping status
									$vault_select_sql = "DELETE FROM " . TABLE_CUSTOM_PRODUCTS_VAULT . "
														WHERE products_vault_id='".$products_vault_id."'";
									$vault_result = tep_db_query($vault_select_sql);

                                    global $messageStack;
									$messageStack->add_session(sprintf(ERROR_INVALID_ZIP_FILE,' ', 'error'));
								}

								if (file_exists(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip') && tep_not_null($md5_vault_id))
									unlink(DIR_FS_SECURE_TEMP.$md5_vault_id.'.zip');
							}
						} // end if successfully execute SQL
					} // end if 2 & 3

					if ($send_upload_cdkey_email){

						$products_price_select = "SELECT products_price, products_base_currency, products_cat_path
						                          FROM " . TABLE_PRODUCTS . "
												  WHERE products_id = " . tep_db_input($upload_obj['filterby_p_up']) . "";
						$products_price_result = tep_db_query($products_price_select);
						if ($products_price_row = tep_db_fetch_array($products_price_result)) {
							$products_price = $products_price_row['products_price'];
							$products_base_currency = $products_price_row['products_base_currency'];
							$products_cat_path = $products_price_row['products_cat_path'];

							$currencies = new currencies(); //to use the class function
							$email_with_usd_conversion = false;
							$product_USD_selling_price = '';
							$other_price_array = array();

							if ($products_base_currency == 'USD') {
								$product_USD_selling_price = $products_price;
							}

							$products_other_price_select = "	SELECT products_currency_prices_code, products_currency_prices_value
					                          					FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
											  					WHERE products_id = " . tep_db_input($upload_obj['filterby_p_up']) . "";
							$products_other_price_result = tep_db_query($products_other_price_select);

							while ($products_other_price_row = tep_db_fetch_array($products_other_price_result)) { //get other prices
								$other_price_array[] = $products_other_price_row['products_currency_prices_code'].' '.$products_other_price_row['products_currency_prices_value'];

								if ($products_other_price_row['products_currency_prices_code'] == 'USD') {
									$product_USD_selling_price = $products_other_price_row['products_currency_prices_value'];
								}
							}

							if (count($other_price_array) != 0) {
								$email_content_other_price = implode(', ', $other_price_array);
							} else {
								$email_content_other_price = 'n/a';
							}

							if (tep_not_null($product_USD_selling_price)) {	// Either has USD price from Base or Other Price
								$email_with_usd_conversion = false;
							} else {
								$email_with_usd_conversion = true;
								$product_USD_selling_price = $currencies->advance_currency_conversion($products_price, $products_base_currency, DEFAULT_CURRENCY);
							}

							// get unit price if upload via PO
							$po_ref_num = '';
							if (isset($upload_obj['po_id']) && tep_not_null($upload_obj['po_id'])) {
								$po_ref_num = $upload_obj['po_number'];
								$po_obj = new edit_purchase_orders($_SESSION['login_id'], $_SESSION['login_email_address']);
								$po_obj->load_po(array('po_id' => $upload_obj['po_id']), $messageStack);
								foreach ($po_obj->po_items as $po_prod) {
									if ($po_prod['products_id'] == $upload_obj['filterby_p_up']) {
										$upload_obj['zip_unit_price'] = $po_prod['products_usd_unit_price'];
                                        break;
									}
								}
							}

							//selling price - uploaded unit price)/selling price in %
							$product_gp = ($product_USD_selling_price==0 ? 0 : round(((($product_USD_selling_price - $upload_obj['zip_unit_price'])/$product_USD_selling_price)*100), 2));

							if ($email_with_usd_conversion){
								$custom_products_upload_cdkey_email_contents = sprintf(EMAIL_PRODUCT_UPLOADED_NOTIFICATION_CONTENT_WITHOUT_USD, $po_ref_num, $upload_obj['filterby_p_up'], $products_cat_path, strip_tags(tep_get_products_name($upload_obj['filterby_p_up'])), $products_base_currency, $products_price, $product_USD_selling_price, $email_content_other_price, $upload_obj['zip_unit_price'], $product_gp.'%', $upload_obj['zip_description'], date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], tep_get_admin_group_name($_SESSION['login_email_address']));
							} else {
								$custom_products_upload_cdkey_email_contents = sprintf(EMAIL_PRODUCT_UPLOADED_NOTIFICATION_CONTENT, $po_ref_num, $upload_obj['filterby_p_up'], $products_cat_path, strip_tags(tep_get_products_name($upload_obj['filterby_p_up'])), $products_base_currency, $products_price, $email_content_other_price, $upload_obj['zip_unit_price'], $product_gp.'%', $upload_obj['zip_description'], date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], tep_get_admin_group_name($_SESSION['login_email_address']));
							}

							$slack = new slack_notification();
							$data = json_encode(array(
								'text' => implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Cdkey Product Price Uploaded')),
								'attachments' => array(
									array(
										'color' => 'warning',
										'text' => $custom_products_upload_cdkey_email_contents
									)
								)
							));
							$slack->send(SLACK_WEBHOOK_INV_PRODUCT_PRICE, $data);
						}
					}

				} else {
					$messageStack->add_session(sprintf(ERROR_FILESIZE_EXCEEDED, $row['name']), 'error');
				}
			}

			if ($email_notify_data_arr && $uploadtype != "1") {
				$productID = $upload_obj['filterby_p_up'];
				$status_arr = array('1'=>LINK_FILTER_BY_ACTUAL);
				$qty_temp = tep_get_status_qty($status_arr,$productID);

				$email_notify_data_arr['old_prod_actual_qty'] = $qty_temp[1];
				$email_notify_data_arr['new_prod_actual_qty'] = $qty_temp[1];
				tep_send_custom_product_stock_adjustment_email($email_notify_data_arr, $productID, 0, 2, tep_get_this_prod_cat_path($productID));
			}
		}
		unset($files_arr);

                return array(
                    'cdkey_id_array' => $cdkey_array
                );
	}

	function check_upload_quantity_with_purchase_order($input_batch, $stock_offset, &$allow_batch, &$reject_batch) {
		$master_data_arr = array();
		$po_qty_count = array();

		if (is_array($input_batch)) {
			// move cdkey to actual stock
			if ($stock_offset == '+') {
				// get product id and po ref id from selected cdkey batch
				foreach ($input_batch as $curr_id) {
					$cdkey_select_sql = "	SELECT products_id, purchase_orders_id
											FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
											WHERE custom_products_code_id='" . (int)$curr_id . "'";
					$cdkey_result_sql = tep_db_query($cdkey_select_sql);
					$cdkey_row = tep_db_fetch_array($cdkey_result_sql);
					if (tep_not_null($cdkey_row['purchase_orders_id']) && $cdkey_row['purchase_orders_id'] != '0') {
						$prod_id = $cdkey_row['products_id'];
						$po_id = $cdkey_row['purchase_orders_id'];
						$po_ref_id = get_po_reference_number($po_id);

						if (isset($po_qty_count[$po_id][$prod_id])) {
							$po_qty_count[$po_id][$prod_id]++;
						} else {
							$po_qty_count[$po_id] = array($prod_id => 1);
						}
					} else {
						$prod_id = $cdkey_row['products_id'];
						$po_id = '';
						$po_ref_id = '';
					}
					$master_data_arr[] = array('custom_id' => $curr_id, 'prod_id' => $prod_id, 'po_id' => $po_id, 'po_ref_id' => $po_ref_id);
				}

				// check each product within each po, if upload quantity still within po quantity
				$allow_po_arr = $reject_po_arr = array();
				foreach ($po_qty_count as $po_id => $count_data) {
					$allow_po_arr[$po_id]['prod_id'] = array();
					$reject_po_arr[$po_id]['prod_id'] = array();

					foreach ($count_data as $prod_id => $count_amt) {
						$po_prod_select_sql = "SELECT pod.purchase_orders_products_id, pod.purchase_orders_id,
												pod.products_quantity, pod.products_delivered_quantity
												FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod
												INNER JOIN " .TABLE_PRODUCTS . " AS p
													ON (p.products_id = pod.products_id)
												WHERE pod.products_id = '".$prod_id."'
													AND pod.purchase_orders_id='".tep_db_input($po_id)."'";
						$po_prod_result_sql = tep_db_query($po_prod_select_sql);
						if ($po_prod_row = tep_db_fetch_array($po_prod_result_sql)) {
							$purchased_quantity = (int)$po_prod_row['products_quantity'];
							$prev_deliver_qty = (int)$po_prod_row['products_delivered_quantity'];

							$balance_qty = $purchased_quantity - $prev_deliver_qty;
							if ($count_amt <= $balance_qty) {
								$allow_po_arr[$po_id]['prod_id'][] = $prod_id;
							} else {
								$reject_po_arr[$po_id]['prod_id'][] = $prod_id;
							}
						}
					}
				}

				// for each selected cdkey batch, check if it is allowed or rejected
				foreach ($master_data_arr as $input_data) {
					if (tep_not_null($input_data['po_id'])) {
						if (in_array($input_data['prod_id'], $allow_po_arr[$input_data['po_id']]['prod_id'])) {
							$allow_batch[] = $input_data['custom_id'];
						} else {
							$reject_batch[] = $input_data['custom_id'];
						}
					} else {
						$allow_batch[] = $input_data['custom_id'];
					}
				}
			}
			// disable cdkey from stock
			else if ($stock_offset == '-') {
				foreach ($input_batch as $curr_id) {
					$po_check_select_sql = "SELECT po.purchase_orders_status
											FROM " . TABLE_PURCHASE_ORDERS . " AS po
											INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
												ON (po.purchase_orders_id = cpc.purchase_orders_id
												AND cpc.custom_products_code_id = '" . (int)$curr_id . "')";
					$po_check_result_sql = tep_db_query($po_check_select_sql);
					if ($po_check_row = tep_db_fetch_array($po_check_result_sql)) {
						if ($po_check_row['purchase_orders_status'] == '2') {
							$allow_batch[] = $curr_id;
						} else {
							$reject_batch[] = $curr_id;
						}
					} else {
						$allow_batch[] = $curr_id;
					}
				}
			}
		}
	}

	function update_purchase_order_receive_qty($upload_obj, $upload_qty=1, $offset='+1') {
		global $messageStack, $login_email_address, $log_object;

		if (isset($upload_obj['po_id']) && tep_not_null($upload_obj['po_id'])) {
			$po_prod_select_sql = " SELECT pod.purchase_orders_products_id, pod.purchase_orders_id, pod.products_unit_price, pod.products_usd_unit_price,
                                                pod.products_quantity, pod.products_delivered_quantity, pod.products_good_delivered_quantity,
                                                pod.products_name, p.products_cat_path
                                                FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod
                                                INNER JOIN " .TABLE_PRODUCTS . " AS p
                                                        ON (p.products_id = pod.products_id)
                                                WHERE pod.products_id = '".$upload_obj['filterby_p_up']."'
													AND pod.purchase_orders_id='".tep_db_input($upload_obj['po_id'])."'";
			$po_prod_result_sql = tep_db_query($po_prod_select_sql);
			if ($po_prod_row = tep_db_fetch_array($po_prod_result_sql)) {
				$purchased_quantity = (int)$po_prod_row['products_quantity'];
				$prev_deliver_qty = (int)$po_prod_row['products_delivered_quantity'];
				$prev_good_deliver_qty = (int)$po_prod_row['products_good_delivered_quantity'];
				$prod_unit_price = $po_prod_row['products_unit_price'];
				$prod_usd_unit_price = $po_prod_row['products_usd_unit_price'];

				if ($offset > 0) {
					$new_deliver_qty = $prev_deliver_qty + $upload_qty;
					$new_good_deliver_qty = $prev_good_deliver_qty + $upload_qty;
				} else {
					$new_deliver_qty = $prev_deliver_qty - $upload_qty;
					$new_good_deliver_qty = $prev_good_deliver_qty - $upload_qty;
				}
				$new_good_deliver_price = $new_good_deliver_qty * $prod_unit_price;
				$new_good_deliver_usd_price = $new_good_deliver_qty * $prod_usd_unit_price;

				// update delivered quantity and prices
				$sql_data_array = array('products_delivered_quantity' => $new_deliver_qty,
										'products_good_delivered_quantity' => $new_good_deliver_qty,
										'products_good_delivered_price' => $new_good_deliver_price,
										'products_good_delivered_usd_price' => $new_good_deliver_usd_price);
				tep_db_perform(TABLE_PURCHASE_ORDERS_PRODUCTS, $sql_data_array, 'update', "purchase_orders_products_id = '" . (int)$po_prod_row['purchase_orders_products_id'] . "'");

				if ($offset > 0) {
					$delivery_remark = "The following items have been received:" . "\n" . "&raquo; " . $po_prod_row["products_name"] . "\tx " . $upload_qty;
				} else {
					$delivery_remark = "The following items have been reversed out:" . "\n" . "&raquo; " . $po_prod_row["products_name"] . "\tx -" . $upload_qty;
				}

				// add delivery status history
				$sql_data_array = array('purchase_orders_id' => $po_prod_row['purchase_orders_id'],
										'date_added' => 'now()',
										'comments' => $delivery_remark,
										'comments_type' => '2',
										'changed_by' => $_SESSION['login_email_address']);
				tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
				tep_update_purchase_orders_status_counter($sql_data_array);

				$po_status = '2';
				$input_array = array('po_id' => $po_prod_row['purchase_orders_id']);
				$po_object = new edit_purchase_orders($_SESSION['login_id'], $_SESSION['login_email_address']);
				$po_object->complete_po($input_array, $messageStack, $po_status);
			}
		}
	}

	function tep_update_keycode_attrib_batch()
	{
		global $HTTP_POST_VARS, $messageStack, $log_object, $gInfo;

		$temp = $data_arr = $success_arr = $error_arr = array();
		switch($_POST["cb_status"]) {
			case 'delete':
				foreach ($_POST["gselection"] as $curr_id) {
					tep_get_gInfo($curr_id);
					tep_delete_keycode_record();
				}
				break;

			case 'delete_zip':
				foreach ($_POST["gselection"] as $curr_id) {
					tep_delete_keyzip_record($curr_id);
				}
				break;

			case 'remarks':
				$gremarks = $_POST['gremarks'];
				foreach ($_POST["gselection"] as $curr_id) {
					tep_add_Remarks($curr_id,$gremarks);
					$success_arr[] =  $curr_id;
				}

				if (count($success_arr)) $messageStack->add_session(sprintf(SUCCESS_MESSAGE_ADD_REMARKS_BATCH, implode("<br/>&nbsp;&nbsp;", $success_arr)), 'success');

				break;

			default:
				//Changing status.
				// $data_arr['remarks'] = $_POST['gremarks'];
				$new_status = (int)$_POST['cb_status'];

				if ($new_status > 0 || $new_status < 0) {
					$data_arr['status_id'] = $new_status;
				} else {
					$messageStack->add(sprintf(ERROR_UPDATE_CODE_FAILED,'All'), 'error');
					return;
				}

				$cdkey_allow_batch = array();
				$cdkey_reject_batch = array();
				$cdkey_uploaded_batch = array();
				$error_reason = '';
				if ($new_status > 0) { $stock_offset = '+'; $error_reason = 'PO over delivered'; }
				else if ($new_status < 0) { $stock_offset = '-'; $error_reason = 'PO not in Processing status'; }

				$update_stock_received_qty = false;
				switch ($_POST['current_s']) {	// Currently opened Tab
					case 'Actual':
						if ($new_status == '-2') {
							// from Actual to On-Hold, deduct stock received.
							check_upload_quantity_with_purchase_order($_POST['gselection'], $stock_offset, $cdkey_allow_batch, $cdkey_reject_batch);
							$update_stock_received_qty = true;
						} else {
							// else keep as stock received status
							$cdkey_allow_batch = $_POST['gselection'];
						}
						break;
					case 'On Hold':
						if ($new_status == '1') {
							// from On-Hold to Actual, add stock received.
							check_upload_quantity_with_purchase_order($_POST['gselection'], $stock_offset, $cdkey_allow_batch, $cdkey_reject_batch);
							$update_stock_received_qty = true;
						} else {
							$cdkey_allow_batch = $_POST['gselection'];
						}
						break;
					case 'Disabled':
						if ($new_status == '-2') {
							// from Disabled to On-Hold, deduct stock received.
							check_upload_quantity_with_purchase_order($_POST['gselection'], $stock_offset, $cdkey_allow_batch, $cdkey_reject_batch);
							$update_stock_received_qty = true;
						} else {
							// else keep as stock received status
							$cdkey_allow_batch = $_POST['gselection'];
						}
						break;
					default:
						$cdkey_allow_batch = $_POST['gselection'];
						break;
				}

				foreach ($_POST['gselection'] as $curr_id) {
					if (!in_array($curr_id, $cdkey_allow_batch)) {
						continue;
					}

					// get old remarks
					$cdkey_remarks_sql = "SELECT remarks 
											FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
											WHERE custom_products_code_id='" . (int)$curr_id . "'";
					$cdkey_remarks_result = tep_db_query($cdkey_remarks_sql);
					$cdkey_remarks_row = tep_db_fetch_array($cdkey_remarks_result);

					// Check for _API in remarks for API CDK
					// if (strpos($cdkey_remarks_row['remarks'], '_API') === false) {
					// 	// Do nothing
					// } else {
					// 	$data_arr['remarks'] = '_API '.$_POST['gremarks'];
					// }

					$offset = '';
					$po_offset = '';
					$error_found=true;
					tep_get_gInfo($curr_id);

					$indicator = "$curr_id";
					if ($gInfo) {
						if ($gInfo->status_id == $data_arr['status_id']) //No change. Ignore silently.
							continue;

						if ($gInfo->status_id > 0 && $data_arr['status_id'] < 0) {
							//Available to Disabled, reduce stock
							$offset = '-1';
							$temp[$offset.'_'.$gInfo->products_id]['action_type'] = STOCK_CUSTPROD_EMAIL_ACTION_CODE_SET_DISABLE;
						} elseif ($gInfo->status_id < 0 && $data_arr['status_id'] > 0) {
							//Disabled to Availble, add stock
							$offset = '+1';
							$temp[$offset.'_'.$gInfo->products_id]['action_type'] = STOCK_CUSTPROD_EMAIL_ACTION_CODE_SET_AVAILABLE;
						}
						$temp[$offset.'_'.$gInfo->products_id]['filename'][$curr_id] = "{$gInfo->file_name}";

						if ($offset) {
							// Keep log
							$cdkey_log_select_sql = "	SELECT products_id AS custom_products_code_products_id,
															status_id AS custom_products_code_status_id,
															file_name AS custom_products_code_file_name,
															code_uploaded_by AS custom_products_code_uploaded_by,
															remarks AS custom_products_code_remarks, custom_products_code_viewed,
															purchase_orders_id
														FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
														WHERE custom_products_code_id='" . (int)$curr_id . "'";
							$cdkey_old_log_result_sql = tep_db_query($cdkey_log_select_sql);
							$cdkey_old_log_row = tep_db_fetch_array($cdkey_old_log_result_sql);

							$data_arr['code_date_modified'] = date('Y-m-d H:i:s');

							$status = tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $data_arr, 'update', "custom_products_code_id =".$curr_id);
							if ($status) {
								$old_prod_actual_qty = 0;
								list($old_prod_actual_qty, $temp[$offset.'_'.$gInfo->products_id]['new_prod_actual_qty']) = tep_update_cdkey_stock_qty($gInfo->products_id, $offset, $curr_id);
								if (!isset($temp[$offset.'_'.$gInfo->products_id]['old_prod_actual_qty'])) $temp[$offset.'_'.$gInfo->products_id]['old_prod_actual_qty'] = $old_prod_actual_qty;
								$error_found = false;

								// Keep log
								$cdkey_new_log_result_sql = tep_db_query($cdkey_log_select_sql);
								$cdkey_new_log_row = tep_db_fetch_array($cdkey_new_log_result_sql);

								$cdkey_changes_array = $log_object->detect_changes($cdkey_old_log_row, $cdkey_new_log_row);

								$cdkey_changes_formatted_array = $log_object->construct_log_message($cdkey_changes_array);

								if (count($cdkey_changes_formatted_array)) {
									$changes_str = 'Changes made:' . "\n";
									for ($logCnt=0; $logCnt < count($cdkey_changes_formatted_array); $logCnt++) {
										if (count($cdkey_changes_formatted_array[$logCnt])) {
											foreach($cdkey_changes_formatted_array[$logCnt] as $field => $res) {
												if (isset($res['plain_result']) && $res['plain_result'] == '1') {
													$changes_str .= $res['text'] . "\n";
												} else {
													$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
												}
											}
										}
									}
									// Include Remarks text
									$changes_str .= "<b>Remarks:</b> " . $_POST['gremarks'] . "\n";
									$log_object->insert_cdkey_history_log('admin', $curr_id, $changes_str);
								}
							}
						}

						if (($gInfo->status_id == '1' && $data_arr['status_id'] == '-2') ||
							($gInfo->status_id == '-1' && $data_arr['status_id'] == '-2')) {
							// from Actual to On-Hold OR from Disabled to On-Hold, reduce PO stock received
							$po_offset = '-1';
						} else if (($gInfo->status_id == '-2' && $data_arr['status_id'] == '1')) {
							// from On-Hold to Actual, add PO stock received
							$po_offset = '+1';
						}
						if (tep_not_null($po_offset)) {
							if ($offset == '') {
								// update cdkey status only when move from Disabled to On-Hold
								$data_arr['code_date_modified'] = date('Y-m-d H:i:s');
								tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $data_arr, 'update', "custom_products_code_id =".$curr_id);
							}

							$po_id = $gInfo->purchase_orders_id;
							$prod_id = $gInfo->products_id;
							$error_found = false;
							// update PO received quantity
							if (!isset($cdkey_uploaded_batch[$po_id])) { $cdkey_uploaded_batch[$po_id] = array(); }
							if (!isset($cdkey_uploaded_batch[$po_id][$prod_id])) { $cdkey_uploaded_batch[$po_id][$prod_id] = array(); }
							if (!isset($cdkey_uploaded_batch[$po_id][$prod_id][$po_offset])) { $cdkey_uploaded_batch[$po_id][$prod_id][$po_offset] = array('uploaded_count' => 0); }
							$cdkey_uploaded_batch[$po_id][$prod_id][$po_offset]['uploaded_count']++;
						}
					}

					if ($error_found) {
						$error_arr[] = $indicator;
					} else {
						$success_arr[] =  $indicator;
					}
				}

				// update PO received quantity in batch
				if ($update_stock_received_qty) {
					if (count($cdkey_uploaded_batch) > 0) {
						foreach ($cdkey_uploaded_batch as $po_id => $products_array) {
							foreach ($products_array as $prod_id => $offset_array) {
								foreach ($offset_array as $this_offset => $upload_qty_array) {
									if ($upload_qty_array['uploaded_count'] > 0) {
										$upload_obj = array('po_id' => $po_id, 'filterby_p_up' => $prod_id);
										update_purchase_order_receive_qty($upload_obj, $upload_qty_array['uploaded_count'], $this_offset);
									}
								}
							}
						}
					}
				}

				//One email per product per offset
				foreach ($temp as $key => $email_notify_data_arr) {
					list($offset, $productID) = explode('_', $key);
					// $email_notify_data_arr['comments'] = $data_arr['remarks'];
					$email_notify_data_arr['comments'] = $_POST['gremarks'];

					tep_send_custom_product_stock_adjustment_email($email_notify_data_arr, $productID, $offset, 2, tep_get_this_prod_cat_path($productID));
				}
				if (count($success_arr)) $messageStack->add_session(sprintf(SUCCESS_MESSAGE_UPDATE_STATUS_BATCH, implode("<br/>&nbsp;&nbsp;", $success_arr)), 'success');
				if (count($error_arr))	$messageStack->add_session(sprintf(ERROR_UPDATE_STATUS_BATCH_FAILED, implode("<br/>&nbsp;&nbsp;", $error_arr)), 'error');
				if (count($cdkey_reject_batch)) $messageStack->add_session(sprintf(ERROR_UPDATE_STATUS_BATCH_PO_ERROR_FAILED, $error_reason, implode("<br/>&nbsp;&nbsp;", $cdkey_reject_batch)), 'error');
				break;
		}
	}

	function tep_delete_keycode_record()
	{
		global $messageStack, $gInfo, $log_object;

		if ((int)$gInfo->status_id == 0 || (int)$gInfo->status_id > 0) {
			$messageStack->add_session(sprintf(ERROR_DELETE_BATCH_DENIED, $gInfo->custom_products_code_id), 'error');
			return;
		} else {
			$status = tep_db_query("delete from " . TABLE_CUSTOM_PRODUCTS_CODE ." where custom_products_code_id=".(int)$gInfo->custom_products_code_id);
                        // DELETE FROM WITHDRAWAL TABLE
                        tep_db_query("delete from " . TABLE_CUSTOM_PRODUCTS_WITHDRAWAL ." where custom_products_code_id=".(int)$gInfo->custom_products_code_id);

			if ($status) {
				if (file_exists(DIR_FS_SECURE_TEMP.$gInfo->custom_products_code_id.'.key') && tep_not_null($gInfo->custom_products_code_id))
					unlink(DIR_FS_SECURE_TEMP.$gInfo->custom_products_code_id.'.key');
				$log_object->insert_log((int)$gInfo->products_id, 'products_quantity', '', '', sprintf(LOG_DELETE_CDKEY, ''), sprintf(LOG_CDKEY_ID_STR, $gInfo->custom_products_code_id));
				$messageStack->add_session(sprintf(SUCCESS_MESSAGE_DELETE_CODE, $gInfo->custom_products_code_id), 'success');
			} else
				$messageStack->add_session(sprintf(ERROR_DELETE_CODE_FAILED, $gInfo->custom_products_code_id), 'error');
		}
	}

	function tep_delete_keyzip_record($products_vault_id)
	{
		global $messageStack, $log_object;

		$status = tep_db_query("delete from " . TABLE_CUSTOM_PRODUCTS_VAULT ." where products_vault_id=".(int)$products_vault_id);
		if ($status) {
			if (file_exists(DIR_FS_SECURE_TEMP.$products_vault_id.'.keyzip') && tep_not_null($products_vault_id))
				unlink(DIR_FS_SECURE_TEMP.$products_vault_id.'.keyzip');

			// $log_object->insert_log((int)$gInfo->products_id, 'products_quantity', '', '', sprintf(LOG_DELETE_CDKEY, ''), sprintf(LOG_CDKEY_ID_STR, $gInfo->custom_products_code_id));
			$messageStack->add_session(sprintf(SUCCESS_MESSAGE_DELETE_ZIP_CODE, $products_vault_id), 'success');
		}
		else
			$messageStack->add_session(sprintf(ERROR_DELETE_ZIP_CODE_FAILED, $products_vault_id), 'error');
	}
	//------End processing functions

	//------Start presentation functions
	function tep_buildGridUNZIP($sortby, $filterby_c, $filterby_p)
	{
		global $HTTP_GET_VARS, $batch_opt_arr, $layers_arr_all, $update_vault_status, $download_vault_zip_file, $unzip_vault;

		$batch_action_arr = array('delete_zip'=>LABEL_SET_DELETE);
		$batch_opt_arr[] = array('id'=>'','text'=>LABEL_BATCH_ACTION);
		foreach ($batch_action_arr as $key => $val) {
			$batch_opt_arr[] = array('id'=>$key,'text'=>$val);
		}

		$status_arr = array('0'=>LINK_FILTER_BY_SOLD, '1'=>LINK_FILTER_BY_ACTUAL, '-1'=>LINK_FILTER_BY_DISABLED, '-2'=>LINK_FILTER_BY_ONHOLD);

		$status_qty_arr = tep_get_status_qty($status_arr,$filterby_p);

		$langID = ($_SESSION["languages_id"] ? $_SESSION["languages_id"] : 1);

		// extract unzipping process
		$vault_unzipping_obj_arr = array();

		$cdkey_vault_select_sql = "	select products_vault_id, file_name, zip_unzip_by, zip_date_unzipping
									from " . TABLE_CUSTOM_PRODUCTS_VAULT . "
									where zip_status = '2'
										and products_id = ".tep_db_input($filterby_p)."
									order by zip_date_unzipping ASC";
		$cdkey_vault_result_sql = tep_db_query($cdkey_vault_select_sql);

		while ($cdkey_vault_row = tep_db_fetch_array($cdkey_vault_result_sql)) {
			$vault_unzipping_obj_arr[] = array (	'products_vault_id' => $cdkey_vault_row['products_vault_id'],
											'file_name' => $cdkey_vault_row['file_name'],
											'zip_unzip_by' => $cdkey_vault_row['zip_unzip_by'],
											'zip_date_unzipping' => $cdkey_vault_row['zip_date_unzipping']
											);
		}

		// print HTML

		$gridHTML = '';

		$product_status_url = "action=search&filterby_c=".urlencode($filterby_c)."&filterby_p=".urlencode($filterby_p);

		$gridHTML .= tep_draw_separator('pixel_trans.gif', '1', '15').'
					<div id="ListingTab">
						<ul class="ui-tabs-nav">
							<li id="tab-Actual" class=""><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ACTUAL)).'"><span><font color="green">'.LINK_FILTER_BY_ACTUAL.' ('.$status_qty_arr[1].')</font></span></a></li>
							<li id="tab-On-Hold" class=""><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ONHOLD)).'"><span><font color="red">'.LINK_FILTER_BY_ONHOLD.' ('.$status_qty_arr[-2].')</font></span></a></li>
							<li id="tab-Sold" class=""><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_SOLD)).'"><span><font color="blue">'.LINK_FILTER_BY_SOLD.' ('.$status_qty_arr[0].')</font></span></a></li>
							<li id="tab-Disabled" class=""><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DISABLED)).'"><span><font color="black">'.LINK_FILTER_BY_DISABLED.' ('.$status_qty_arr[-1].')</font></span></a></li>
							<li id="tab-Zip" class="ui-tabs-selected"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ZIP)).'"><span><img src=images/icons/locked.gif border=0 align=absmiddle><font color="brown">'.LINK_FILTER_BY_ZIP.' ('.$status_qty_arr[2].'*)</font></span></a></li>
						</ul>
					</div><div class="ui-tabs-panel" style="display: block;">';

		$gridHTML .= tep_draw_form('maingrid', FILENAME_CDKEY, tep_get_all_get_params(array('action','cont', 'gID')) . 'action=batch_update&cont=1', 'post');

		//Start Header
		$gridHTML .= '
					<table border="0" width="100%" cellspacing="0" cellpadding="2">';

		if ($vault_unzipping_obj_arr) {
			$gridHTML .= '
						<tbody id="unzip_status_row" class="show">
							<tr bgcolor="#ffda38">
								<td colspan=100% class="reportRecords">
									<div style="padding: 10px;">
									<big><b><font color=red>'.sprintf(MESSAGE_UNZIPPING_TOP_STATUS, count($vault_unzipping_obj_arr), MAX_UNZIPPING_RUNNING).'</font></b></big><br>';

			foreach ($vault_unzipping_obj_arr as $vault_unzipping_entry) {
				$gridHTML .= '									<li>'.sprintf(MESSAGE_UNZIPPING_TOP_STATUS_ITEM, $vault_unzipping_entry['products_vault_id'], $vault_unzipping_entry['file_name'], tep_datetime_short($vault_unzipping_entry['zip_date_unzipping'], PREFERRED_DATE_TIME_FORMAT), $vault_unzipping_entry['zip_unzip_by']);
			}

			$gridHTML .= '
									</div>
								</td>
							</tr>
							<tr>
								<td colspan=100%>'.tep_draw_separator('pixel_trans.gif', '1', '5').'<td>
							</tr>
						</tbody>';
		}

		$extra_params = '&action='.urlencode($_GET["action"]).'&filterby_c='.urlencode($_GET["filterby_c"]).'&filterby_p='.urlencode($_GET["filterby_p"]).'&filterby_s='.urlencode($_GET["filterby_s"]);

		$gridHTML .= '
						<tbody id="header_'.$i.'" class="show">
							<tr>
								<td class="reportBoxHeading" align="center" width="5%">'.tep_draw_checkbox_field('gselection_m', '','',''," onClick=\"toggleCheckBox()\" ").'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='products_vault_id')? '<b>'.TABLE_HEADING_ZIP_ID.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=products_vault_id' . $extra_params).'">'.TABLE_HEADING_ZIP_ID.'</a>') .'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='file_name')? '<b>'.TABLE_HEADING_ZIP_FILENAME.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=file_name' . $extra_params).'">'.TABLE_HEADING_ZIP_FILENAME.'</a>') .'</td>';

		if ($unzip_vault)
			$gridHTML .= '		<td class="reportBoxHeading" align="center">'.TABLE_HEADING_PASSWORD_UNZIP_FILE.'</td>';

		$gridHTML .= '			<td class="reportBoxHeading" align="center">'. (($sortby=='zip_po_ref')? '<b>'.TABLE_HEADING_PO_REF_NUM.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=zip_po_ref' . $extra_params).'">'.TABLE_HEADING_PO_REF_NUM.'</a>') .'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='zip_qty')? '<b>'.TABLE_HEADING_QTY.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=zip_qty' . $extra_params).'">'.TABLE_HEADING_QTY.'</a>') .'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='zip_unit_price')? '<b>'.TABLE_HEADING_UNIT_PRICE.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=zip_unit_price' . $extra_params).'">'.TABLE_HEADING_UNIT_PRICE.'</a>') .'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='zip_date_added')? '<b>'.TABLE_HEADING_UPLOADED_ON.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=zip_date_added' . $extra_params).'">'.TABLE_HEADING_UPLOADED_ON.'</a>') .'</td>
								<td class="reportBoxHeading" align="center">'. (($sortby=='file_size')? '<b>'.TABLE_HEADING_FILE_SIZE.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=file_size' . $extra_params).'">'.TABLE_HEADING_FILE_SIZE.'</a>') .'</td>
							</tr>
						</tbody>';

		//Start customise sql
		$cdkey_vault_select_sql = "	select products_vault_id,file_name,file_type,zip_qty,zip_unit_price,zip_date_added,zip_uploaded_by,zip_status,file_size,zip_date_unzipping,purchase_orders_id from ".TABLE_CUSTOM_PRODUCTS_VAULT. "
									where products_id = '".tep_db_input($filterby_p)."'
										and (zip_status = '1' OR zip_status = '2')";

		switch ($sortby) {
			case 'products_vault_id' 	: $cdkey_vault_select_sql .= ' order by products_vault_id'		; break;
			case 'file_name' 			: $cdkey_vault_select_sql .= ' ORDER BY file_name'				; break;
			case 'zip_qty' 				: $cdkey_vault_select_sql .= ' ORDER BY zip_qty'				; break;
			case 'zip_po_ref' 			: $cdkey_vault_select_sql .= ' ORDER BY purchase_orders_id'		; break;
			case 'zip_unit_price' 		: $cdkey_vault_select_sql .= ' ORDER BY zip_unit_price'			; break;
			case 'zip_date_added' 		: $cdkey_vault_select_sql .= ' ORDER BY zip_date_added'			; break;
			default 					: $cdkey_vault_select_sql .= ' ORDER BY zip_date_added ASC'		;
		}

		//Start Grid
		$cdkey_split = new splitPageResults($_REQUEST['page'], MAX_DISPLAY_SEARCH_RESULTS_CDK_ZIP, $cdkey_vault_select_sql, $cdkey_query_numrows);
		$cdkey_vault_result_sql = tep_db_query($cdkey_vault_select_sql);
		$totalentry = tep_db_num_rows($cdkey_vault_result_sql);
		$i=0;
		$rowFormat = 'Odd';
		$mouseover = "onmouseover=\"rowOverEffect(this, 'reportListingRowOver')\" onclick=\"rowClicked(this, 'reportListingOdd')\"";

		while ($cdkey_row = tep_db_fetch_array($cdkey_vault_result_sql)) {
			$rowFormat = ($rowFormat=='Even') ? 'Odd' : 'Even';

			if ($download_vault_zip_file)
				$print_filename = '<a href="'.tep_href_link(FILENAME_CDKEY,'action=downloadfile&products_vault_id='.$cdkey_row['products_vault_id']).'&filename='.urlencode($cdkey_row['file_name']).'">'.$cdkey_row['file_name'].'</a>';
			else
				$print_filename = $cdkey_row['file_name'];

			$poRef_str = "&nbsp;";
			if ($cdkey_row['purchase_orders_id'] != '') {
				$po_ref_encoded = urlencode($cdkey_row['purchase_orders_id']);
				$poRef_str = "<a href='" . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('action', 'subaction', 'selected_box', 'sortby', 'filterby_c', 'filterby_p', 'filterby_s', 'filterby_order_id', 'filterby_cdk_id', 'filterby_po_ref', 'page')) . "po_id={$po_ref_encoded}&subaction=edit_po") . "' target='_newwin'>";
				$poRef_str .= get_po_reference_number($cdkey_row['purchase_orders_id']) . '</a>';
			}

			$gridHTML .= "<tbody id=\"detail_{$i}\" class=\"show\">";

			$gridHTML .= "<tr class='reportListing{$rowFormat}' onmouseout=\"rowOutEffect(this, 'reportListing{$rowFormat}')\" {$mouseover} >\n";
			$gridHTML .= '
								<td valign="top" align="center" width="5%">'. tep_draw_checkbox_field("gselection[$i]", $cdkey_row['products_vault_id'], '','',"  id=\"gselection[$i]\" onClick=\"cb_upd_gselection_m(this.checked)\" ") .'</td>
								<td class="reportRecords" valign="top" align="center">'.$cdkey_row['products_vault_id'].'</td>
								<td class="reportRecords" valign="top" align="center">'.$print_filename.' <img src=images/icons/locked.gif border=0 align=absmiddle></td>
						';

			if ($unzip_vault) {
				if ($cdkey_row['zip_status'] == "2")
					$gridHTML .= '								<td class="reportRecords" align=center><font color="red">'.sprintf(MESSAGE_UNZIPPING_TABLE_STATUS, tep_datetime_short($cdkey_row['zip_date_unzipping'], PREFERRED_DATE_TIME_FORMAT)).'</a></td>';
				else
					$gridHTML .= '								<td class="reportRecords" align=center><input type=password name="zip_password_'.$cdkey_row['products_vault_id'].'" id="zip_password_'.$cdkey_row['products_vault_id'].'" size=15 onkeypress="return handleEnter(this, event)"> '.tep_button('Unzip', 'Unzip', '', 'onclick="javascript:unzip_vault(\''.$cdkey_row['products_vault_id'].'\')" onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true).'</td>';
			}

			$gridHTML .= '		<td class="reportRecords" valign="top" align="center" width="10%">'.$poRef_str.'</td>
								<td class="reportRecords" valign="top" align="center" width="10%">'.$cdkey_row['zip_qty'].'*</td>
								<td class="reportRecords" valign="top" align="center" width="10%">$'.$cdkey_row['zip_unit_price'].'</td>
								<td class="reportRecords" valign="top" align="center" width="10%">'.tep_date_short($cdkey_row['zip_date_added'], PREFERRED_DATE_FORMAT).'</td>
								<td class="reportRecords" valign="top" align="center" width="10%">'.(int)($cdkey_row['file_size']/1024).' KB</td>
						  	 </tr>
						</tbody>';
			$i++;
		}

		//Start Footer
		$gridHTML .= tep_draw_hidden_field('grid_numrows', $i, " id=\"grid_numrows\" ");
		$gridHTML .= '	<tbody><tr>
							<td colspan="8" class="smallText" align="left">';

		if ($update_vault_status)
			$gridHTML .= tep_draw_pull_down_menu('cb_status', $batch_opt_arr, '', " onChange=\"doBatchUpdate()\" ");

		$gridHTML .= '</td>
					  	</tr></tbody>';
		$gridHTML .= '	<tbody><tr>
            				<td colspan="8">
	            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	              					<tr>
	                					<td class="smallText" valign="top">' . ($show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_CDKEY, $totalentry > 0 ? "1" : "0", $totalentry, $totalentry) : $cdkey_split->display_count($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK_ZIP, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_CDKEY)) . '</td>
	                					<td class="smallText" align="right">' . ($show_records == "ALL" ? "Page 1 of 1" : $cdkey_split->display_links($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK_ZIP, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont', 'gID'))."cont=1")) . '</td>
	              					</tr>
	            				</table>
	            			</td>
						</tr></tbody><tbody><tr><td class="smallText" colspan=100%>'.MESSAGE_QTY_ESTIMATION.'</td></tr></tbody>';
		$gridHTML .= '</table>'.tep_draw_hidden_field('gremarks', '').tep_draw_hidden_field('gnumRows', $i).tep_draw_hidden_field('current_c', $filterby_c).tep_draw_hidden_field('current_p', $filterby_p).tep_draw_hidden_field('current_s', 'Zip').'</form></div>';
		$gridHTML .= '
			<script language="javascript">
			<!--

			jQuery(document).ready(function() {
		';

		if ($cdkey_query_numrows > MAX_DISPLAY_SEARCH_RESULTS_CDK_ZIP)
				$gridHTML .= '			 	jQuery(maingrid.page).removeOption("all");';
				$gridHTML .= '			 	jQuery(maingrid.page).attr("onchange","document.location.href=\'?action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s=Zip&page=\'+jQuery(maingrid.page).val()");';

		$gridHTML .= '
			});

			//-->
			</script>
		';

		return $gridHTML;
	}

	function tep_buildGridSTATS($sortby, $filterby_c)
	{
		global $HTTP_GET_VARS, $batch_opt_arr, $curr_id, $gInfo, $layers_arr_all;

		$batch_action_arr = array('1'=>LABEL_SET_ACTUAL, '-1'=>LABEL_SET_DISABLED, 'delete'=>LABEL_DELETE_DISABLED);
		$batch_opt_arr[] = array('id'=>'','text'=>LABEL_BATCH_ACTION);
		foreach ($batch_action_arr as $key => $val) {
			$batch_opt_arr[] = array('id'=>$key,'text'=>$val);
		}

		$status_arr = array('0'=>LINK_FILTER_BY_SOLD, '1'=>LINK_FILTER_BY_ACTUAL, '-1'=>LINK_FILTER_BY_DISABLED, '-2'=>LINK_FILTER_BY_ONHOLD);

		$langID = ($_SESSION["languages_id"] ? $_SESSION["languages_id"] : 1);
		$gridHTML = '';

		$gridHTML .= tep_draw_form('maingrid', FILENAME_CDKEY, tep_get_all_get_params(array('action','cont', 'gID')) . 'action=batch_update&cont=1', 'post');

		//Start Header
		$gridHTML .= '
					<table border="0" width="100%" cellspacing="0" cellpadding="4">
						<tbody id="header_'.$i.'" class="show">
							<tr>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_PRODUCT.'</td>
								<td class="reportBoxHeading" align="center"><font color="green">'.LINK_FILTER_BY_ACTUAL.'</font></td>
								<td class="reportBoxHeading" align="center"><font color="red">'.LINK_FILTER_BY_ONHOLD.'</font></td>
								<td class="reportBoxHeading" align="center"><font color="blue">'.LINK_FILTER_BY_SOLD.'</font></td>
								<td class="reportBoxHeading" align="center">'.LINK_FILTER_BY_DISABLED.'</td>
								<td class="reportBoxHeading" align="center"><font color="brown">'.LINK_FILTER_BY_ZIP.'</font></td>
							</tr>
						</tbody>';

		//Start customise sql
		$cdkey_select_sql = "select DISTINCT b.products_name,b.products_id
							from " . TABLE_PRODUCTS . " p,
								".TABLE_CUSTOM_PRODUCTS_CODE. " a,
								".TABLE_PRODUCTS_DESCRIPTION." b,
								". TABLE_PRODUCTS_TO_CATEGORIES . " p2c
							where b.products_id=a.products_id
								and p2c.products_id=p.products_id
								and a.products_id=p.products_id
								and p2c.products_is_link = 0
								and b.language_id=".(int)$langID;

		if ($filterby_c) 	$cdkey_select_sql .= " and p2c.categories_id=$filterby_c";

		switch ($sortby) {
			case 'product' 	: $cdkey_select_sql .= ' order by b.products_name'			; break;
			default 		: $cdkey_select_sql .= ' order by b.products_name asc'	;
		}

		//Start Grid
		$cdkey_split = new splitPageResults($_REQUEST['page'], MAX_DISPLAY_SEARCH_RESULTS_CDK, $cdkey_select_sql, $cdkey_query_numrows,true);
		$cdkey_result_sql = tep_db_query($cdkey_select_sql);
		$totalentry = tep_db_num_rows($cdkey_result_sql);
		$i=0;
		$rowFormat = 'Odd';
		$mouseover = "onmouseover=\"rowOverEffect(this, 'reportListingRowOver')\" onclick=\"rowClicked(this, 'reportListingOdd')\"";

		while ($cdkey_row = tep_db_fetch_array($cdkey_result_sql)) {

			$status_qty_arr = tep_get_status_qty($status_arr,$cdkey_row['products_id']);
			$product_status_url = "action=search&filterby_c=$filterby_c&filterby_p=".urlencode($cdkey_row['products_id']);

			$rowFormat = ($rowFormat=='Even') ? 'Odd' : 'Even';
			$gridHTML .= "<tbody id=\"detail_{$i}\" class=\"show\">";

			$gridHTML .= "<tr class='reportListing{$rowFormat}' onmouseout=\"rowOutEffect(this, 'reportListing{$rowFormat}')\" {$mouseover} >\n";
			$gridHTML .= '
								<td class="reportRecords" valign="top" align="left">'. "{$cdkey_row['products_name']}" .'</td>
								<td class="reportRecords" valign="top" align="center" width="10%"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ACTUAL)).'">'.$status_qty_arr[1].'</a></td>
								<td class="reportRecords" valign="top" align="center" width="10%"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ONHOLD)).'">'.$status_qty_arr[-2].'</a></td>
								<td class="reportRecords" valign="top" align="center" width="10%"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_SOLD)).'">'.$status_qty_arr[0].'</a></td>
								<td class="reportRecords" valign="top" align="center" width="10%"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DISABLED)).'">'.$status_qty_arr[-1].'</a></td>
								<td class="reportRecords" valign="top" align="center" width="10%"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ZIP)).'">'.$status_qty_arr[2].'</a>*</td>
						  	 </tr>
						</tbody>';

			$i++;
		}

		//Start Footer
		$gridHTML .= '	<tbody><tr>
            				<td colspan="7">
	            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	              					<tr>
	                					<td class="smallText" valign="top">' . ($show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_CDKEY, $totalentry > 0 ? "1" : "0", $totalentry, $totalentry) : $cdkey_split->display_count($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_CDKEY)) . '</td>
	                					<td class="smallText" align="right">' . ($show_records == "ALL" ? "Page 1 of 1" : $cdkey_split->display_links($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont', 'gID'))."cont=1")) . '</td>
	              					</tr>
	            				</table>
	            			</td>
						</tr></tbody><tbody><tr><td class="smallText" colspan=100%>'.MESSAGE_QTY_ESTIMATION.'</td></tr></tbody>';
		$gridHTML .= '</table>';
		$gridHTML .= tep_draw_hidden_field('grid_numrows', $i, " id=\"grid_numrows\" ");
		$gridHTML .= tep_draw_hidden_field('gremarks', '').tep_draw_hidden_field('gnumRows', $i).'</form>';
		$gridHTML .= '</form>';
		$gridHTML .= '
			<script language="javascript">
			<!--

				jQuery(document).ready(function() {
		';

		if ($cdkey_query_numrows > MAX_DISPLAY_SEARCH_RESULTS_CDK)
				$gridHTML .= '			 	jQuery(maingrid.page).removeOption("all");';

		$gridHTML .= '
			});

			//-->
			</script>
		';
		return $gridHTML;
	}

	function tep_buildGrid($sortby, $filterby_c, $filterby_p, $filterby_s)
	{
		global $HTTP_POST_VARS, $HTTP_GET_VARS, $batch_opt_arr, $curr_id, $gInfo, $layers_arr_all, $view_cdkey_images_permission, $view_remarks, $update_status_actual, $update_status_disabled, $update_status_on_hold, $update_status_bulk_remarks, $update_status_delete;

		if ($filterby_s == "Actual"){
			if ($update_status_disabled)
				$batch_action_arr['-1'] = LABEL_SET_DISABLED;

			if ($update_status_on_hold)
				$batch_action_arr['-2'] = LABEL_SET_ON_HOLD;

			if ($update_status_bulk_remarks)
				$batch_action_arr['remarks'] = LABEL_SET_BULK_REMARKS;
		}
		elseif ($filterby_s == "On Hold"){
			if ($update_status_actual)
				$batch_action_arr['1'] = LABEL_SET_ACTUAL;

			if ($update_status_bulk_remarks)
				$batch_action_arr['remarks'] = LABEL_SET_BULK_REMARKS;

			if ($update_status_delete)
				$batch_action_arr['delete'] = LABEL_SET_DELETE;
		}
		elseif ($filterby_s == "Sold"){
			if ($update_status_bulk_remarks)
				$batch_action_arr['remarks'] = LABEL_SET_BULK_REMARKS;
		}
		elseif ($filterby_s == "Disabled") {
			if ($update_status_actual)
				$batch_action_arr['1'] = LABEL_SET_ACTUAL;

			if ($update_status_on_hold)
				$batch_action_arr['-2'] = LABEL_SET_ON_HOLD;

			if ($update_status_bulk_remarks)
				$batch_action_arr['remarks'] = LABEL_SET_BULK_REMARKS;

			if ($update_status_delete)
				$batch_action_arr['delete'] = LABEL_DELETE_DISABLED;
		}
        elseif ($filterby_s == "G2G") {
            if ($update_status_actual)
                $batch_action_arr['1'] = LABEL_SET_ACTUAL;

            if ($update_status_on_hold)
                $batch_action_arr['-2'] = LABEL_SET_ON_HOLD;

            if ($update_status_bulk_remarks)
                $batch_action_arr['remarks'] = LABEL_SET_BULK_REMARKS;

            if ($update_status_delete)
                $batch_action_arr['delete'] = LABEL_DELETE_DISABLED;
        }

		$batch_opt_arr[] = array('id'=>'','text'=>LABEL_BATCH_ACTION);

		if ($batch_action_arr) {
			foreach ($batch_action_arr as $key => $val) {
				$batch_opt_arr[] = array('id'=>$key,'text'=>$val);
			}
		}

		$status_arr = array('0'=>LINK_FILTER_BY_SOLD, '1'=>LINK_FILTER_BY_ACTUAL, '-1'=>LINK_FILTER_BY_DISABLED, '-2'=>LINK_FILTER_BY_ONHOLD, '-4' => 'G2G', '-5' => 'Reserved');
		$status_qty_arr = tep_get_status_qty($status_arr,$filterby_p);

		$langID = ($_SESSION["languages_id"] ? $_SESSION["languages_id"] : 1);
		$gridHTML = '';

		if ($filterby_s == "Actual")
			$actual_class = "ui-tabs-selected";
		else if ($filterby_s == "On Hold")
			$onhold_class = "ui-tabs-selected";
		else if ($filterby_s == "Sold")
			$sold_class = "ui-tabs-selected";
		else if ($filterby_s == "Disabled")
			$disabled_class = "ui-tabs-selected";
        else if ($filterby_s == "G2G")
            $g2g_class = "ui-tabs-selected";
        else if ($filterby_s == "Reserved")
            $reserved_class = "ui-tabs-selected";

		$product_status_url = "action=search&filterby_c=".urlencode($filterby_c)."&filterby_p=".urlencode($filterby_p);

		$gridHTML .= tep_draw_separator('pixel_trans.gif', '1', '15').'
					<div id="ListingTab">
						<ul class="ui-tabs-nav">
							<li id="tab-Actual" class="'.$actual_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ACTUAL)).'"><span><font color="green">'.LINK_FILTER_BY_ACTUAL.' ('.$status_qty_arr[1].')</font></span></a></li>
							<li id="tab-On-Hold" class="'.$onhold_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ONHOLD)).'"><span><font color="red">'.LINK_FILTER_BY_ONHOLD.' ('.$status_qty_arr[-2].')</font></span></a></li>
							<li id="tab-Sold" class="'.$sold_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_SOLD)).'"><span><font color="blue">'.LINK_FILTER_BY_SOLD.' ('.$status_qty_arr[0].')</font></span></a></li>
							<li id="tab-Disabled" class="'.$disabled_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DISABLED)).'"><span><font color="black">'.LINK_FILTER_BY_DISABLED.' ('.$status_qty_arr[-1].')</font></span></a></li>
							<li id="tab-G2G" class="'.$g2g_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_G2G)).'"><span><font color="orange">'.LINK_FILTER_BY_G2G.' ('.$status_qty_arr[-4].')</font></span></a></li>
							<li id="tab-G2G" class="'.$reserved_class.'"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_RESERVED)).'"><span><font color="orange">'.LINK_FILTER_BY_RESERVED.' ('.$status_qty_arr[-5].')</font></span></a></li>
							<li id="tab-Zip"><a href="'.tep_href_link(FILENAME_CDKEY,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ZIP)).'"><span><img src=images/icons/locked.gif border=0 align=absmiddle><font color="brown">'.LINK_FILTER_BY_ZIP.' ('.$status_qty_arr[2].'*)</font></span></a></li>
						</ul>
					</div>
					<div class="ui-tabs-panel" style="display: block;">';

		//if ($view_cdkey_images_permission)
			$gridHTML .= '<div style="text-align: right; padding:5px;" id="show_all_cdk"><a href="javascript:getCDKeyImg_list(\'1\')">View All My Uploaded CD Key</a></div>';

		$gridHTML .= tep_draw_form('maingrid', FILENAME_CDKEY, tep_get_all_get_params(array('action','cont', 'gID')) . 'action=batch_update&cont=1', 'post');

		$extra_params = '&action='.urlencode($_GET["action"]).'&filterby_c='.urlencode($_GET["filterby_c"]).'&filterby_p='.urlencode($_GET["filterby_p"]).'&filterby_s='.urlencode($_GET["filterby_s"]);

		//Start Header
		$gridHTML .= '
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tbody id="header_'.$i.'" class="show">
							<tr>
								<td class="reportBoxHeading" align="center" width="5%">'.tep_draw_checkbox_field('gselection_m', '','',''," onClick=\"toggleCheckBox()\" ").'</td>
								<td class="reportBoxHeading" align="left">'. (($sortby=='code')? '<b>'.LABEL_ID.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=code' . $extra_params).'">'.TABLE_HEADING_CDK_ID.'</a>') .'</td>
								<td class="reportBoxHeading" align="center"><b>'.TABLE_HEADING_PO_REF_NUM.'</b></td>';

		if ($filterby_s == "Sold")
			$gridHTML .= '								<td class="reportBoxHeading" align="center"><b>'.TABLE_HEADING_ORDER_ID.'</b></td>';

		$gridHTML .= '
				                <td class="reportBoxHeading" align="center">'. (($sortby=='releasedate')? '<b>'.TABLE_HEADING_RELEASE_DATE.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=releasedate' . $extra_params).'">'.TABLE_HEADING_RELEASE_DATE.'</a>') .'</td>
				                <td class="reportBoxHeading" align="center">'. (($sortby=='date')? '<b>'.TABLE_HEADING_DATE.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=date' . $extra_params).'">'.TABLE_HEADING_DATE.'</a>') .'</td>';

		//if ($view_cdkey_images_permission){
			$gridHTML .= '      <td class="reportBoxHeading" align="center">'. (($sortby=='filename')? '<b>'.TABLE_HEADING_FILENAME.'</b>' : '<a href="'.tep_href_link(FILENAME_CDKEY, 'sortby=filename' . $extra_params).'">'.TABLE_HEADING_FILENAME.'</a>') .'</td>';
			$gridHTML .= '      <td class="reportBoxHeading" align="center">'. TABLE_HEADING_CDK_FILE .'</td>';
		//}

		if ($view_remarks)
			$gridHTML .= '      <td class="reportBoxHeading" align="center">'. TABLE_HEADING_REMARKS .'</td>';

		$gridHTML .= '
							</tr>
						</tbody>';

		//Start customise sql
		$cdkey_select_sql = "	select op.orders_id, a.custom_products_code_id, a.products_id, a.status_id, a.file_name, a.file_type, a.code_date_modified, a.code_date_added, a.remarks, a.purchase_orders_id, a.code_uploaded_by, b.products_name from " . TABLE_PRODUCTS . " as p inner join ".TABLE_CUSTOM_PRODUCTS_CODE. " as a on a.products_id=p.products_id inner join ".TABLE_PRODUCTS_DESCRIPTION." as b on b.products_id=a.products_id inner join ". TABLE_PRODUCTS_TO_CATEGORIES . " as p2c on p2c.products_id=p.products_id left join " . TABLE_ORDERS_PRODUCTS . " as op on a.orders_products_id = op.orders_products_id where p2c.products_is_link = 0 and b.language_id=".(int)$langID;

		if ($filterby_s) {
			if ($filterby_s != "Zip") {
				(int)$id = array_search($filterby_s,$status_arr,false);
				$cdkey_select_sql .= " and a.status_id=$id";
			}
		}

		if ($filterby_p) 	$cdkey_select_sql .= " and b.products_id=$filterby_p";

		switch ($sortby) {
			case 'date' 	: $cdkey_select_sql .= ' order by a.code_date_modified'		; break;
			case 'releasedate' 	: $cdkey_select_sql .= ' order by a.code_date_added'		; break;
			case 'status' 	: $cdkey_select_sql .= ' order by a.status_id'				; break;
			case 'product' 	: $cdkey_select_sql .= ' order by a.products_id'			; break;
			case 'code' 	: $cdkey_select_sql .= ' order by a.custom_products_code_id'	; break;
			case 'filename' : $cdkey_select_sql .= ' order by a.file_name'	; break;
			default 		: $cdkey_select_sql .= ' order by a.custom_products_code_id desc'	;
		}

        tep_db_connect_rr();

		//Start Grid
		$cdkey_split = new splitPageResults($_REQUEST['page'], MAX_DISPLAY_SEARCH_RESULTS_CDK, $cdkey_select_sql, $cdkey_query_numrows);
		$cdkey_result_sql = tep_db_query($cdkey_select_sql, 'read_db_link');
		$totalentry = tep_db_num_rows($cdkey_result_sql);

		$i=0;
		$rowFormat = 'Odd';
		$mouseover = "onmouseover=\"rowOverEffect(this, 'reportListingRowOver')\" onclick=\"rowClicked(this, 'reportListingOdd')\"";

		while ($cdkey_row = tep_db_fetch_array($cdkey_result_sql)) {
			$rowFormat = ($rowFormat=='Even') ? 'Odd' : 'Even';
			$gridHTML .= "<tbody id=\"detail_{$i}\" class=\"show\">";

			$gridHTML .= "<tr class='reportListing{$rowFormat}' onmouseout=\"rowOutEffect(this, 'reportListing{$rowFormat}')\" {$mouseover} >\n";

			if ($view_cdkey_images_permission || $cdkey_row['code_uploaded_by'] == $_SESSION['login_email_address']) {
				$cdkey_id = '<a href="javascript:;" onClick="Show_CDK_Image(\''.$cdkey_row['custom_products_code_id'].'\');">'.$cdkey_row['custom_products_code_id'].'</a>';
			} else {
				$cdkey_id = $cdkey_row['custom_products_code_id'];
			}
			if ($cdkey['file_name'] || $cdkey_row['file_type'])
				$filename = tep_db_prepare_input($cdkey_row['file_name'].'.'.$cdkey_row['file_type']);
			else
				$filename = '';

			$orderId_str = "";
			if ($cdkey_row['status_id']==0) {
				//Get OrderID for Sold CDKey
				$orderId_str = '(Unknown!)';

				if (isset($cdkey_row['orders_id']) && tep_not_null($cdkey_row['orders_id'])) {
					$orderId_str = "<a href='" . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('gID', 'action', 'selected_box', 'sortby', 'filterby_p', 'filterby_s', 'page')) . "oID={$cdkey_row['orders_id']}&action=edit") . "' target='_newwin'>";
					$orderId_str .= $cdkey_row['orders_id'] . '</a>';
				}
			}

			$poRef_str = "&nbsp;";
			if ($cdkey_row['purchase_orders_id'] != '') {
				$po_ref_encoded = urlencode($cdkey_row['purchase_orders_id']);
				$poRef_str = "<a href='" . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('action', 'subaction', 'selected_box', 'sortby', 'filterby_c', 'filterby_p', 'filterby_s', 'filterby_order_id', 'filterby_cdk_id', 'filterby_po_ref', 'page')) . "po_id={$po_ref_encoded}&subaction=edit_po") . "' target='_newwin'>";
				$poRef_str .= get_po_reference_number($cdkey_row['purchase_orders_id']) . '</a>';
			}

			$gridHTML .= '		<td valign="top" align="center" width="5%">'. tep_draw_checkbox_field("gselection[$i]", $cdkey_row['custom_products_code_id'], '','',"  id=\"gselection[$i]\" onClick=\"cb_upd_gselection_m(this.checked)\" ") .'</td>
								<td class="reportRecords" valign="top" align="left">'. $cdkey_id .'</td>
								<td class="reportRecords" valign="top" align="center">'. $poRef_str .'</td>';

			if ($filterby_s == "Sold")
				$gridHTML .= '								<td class="reportRecords" valign="top" align="center">'. $orderId_str .'</td>';

			$gridHTML .= '
								<td class="reportRecords" valign="top" align="center">'. tep_date_short($cdkey_row['code_date_added'], PREFERRED_DATE_FORMAT) .'</td>
								<td class="reportRecords" valign="top" align="center">'. tep_date_short($cdkey_row['code_date_modified'], PREFERRED_DATE_FORMAT) .'</td>
						';

			//Start show cdkey
		//	if ($view_cdkey_images_permission || $cdkey_row['code_uploaded_by'] == $_SESSION['login_email_address']) {
				$custom_products_code_id = $cdkey_row['custom_products_code_id'];
				$gridHTML .= '	<td class="reportRecords" valign="top" align="center">'.$cdkey_row['file_name'].'</td>';
				$gridHTML .= '	<td class="reportRecords" valign="top" align="center"><div id="Show_CDK_image_'.$custom_products_code_id.'"><a href="javascript:Show_CDK_Image(\''.$custom_products_code_id.'\')">Show CDK Image</a></div><div id="CDK_image_'.$custom_products_code_id.'" style="display: none"></div></td>';
		//	}

			if ($view_remarks) {
				$gridHTML .= '
								<td class="reportRecords" valign="top" align="center">
									<a href="javascript:;" onClick="getCDKeyLog('.$cdkey_row['custom_products_code_id'].')">'.tep_image(DIR_WS_ICONS.'statistics.gif').'</a>&nbsp;
								</td>';
			}

			$gridHTML .= '
						  	 </tr>
						</tbody>';
			$i++;
		}

		//Start Footer
		$gridHTML .= tep_draw_hidden_field('grid_numrows', $i, " id=\"grid_numrows\" ");
		$gridHTML .= '
						<tbody>
						<tr>
							<td colspan="4" class="smallText" align="left">';

		if ($update_status_actual || $update_status_disabled || $update_status_on_hold || $update_status_bulk_remarks || $update_status_delete)
			$gridHTML .= tep_draw_pull_down_menu('cb_status', $batch_opt_arr, '', " onChange=\"doBatchUpdate()\" ");

		$gridHTML .= ' (Selected : <span id="select_count" style="font-weight: bold">0 entry</span>)</td>
					  	</tr></tbody>';
		$gridHTML .= '	<tbody><tr>
            				<td colspan="7">
	            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	              					<tr>
	                					<td class="smallText" valign="top">' . ($show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_CDKEY, $totalentry > 0 ? "1" : "0", $totalentry, $totalentry) : $cdkey_split->display_count($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_CDKEY)) . '</td>
	                					<td class="smallText" align="right">' . ($show_records == "ALL" ? "Page 1 of 1" : $cdkey_split->display_links($cdkey_query_numrows, MAX_DISPLAY_SEARCH_RESULTS_CDK, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont', 'gID'))."cont=1")) . '</td>
	              					</tr>
	            				</table>
	            			</td>
						</tr></tbody><tbody><tr><td class="smallText" colspan=100%>* According to quantity entered by Crew member when uploading.</td></tr></tbody>';
		$gridHTML .= '</table>'.tep_draw_hidden_field('gremarks', '').tep_draw_hidden_field('gnumRows', $i).tep_draw_hidden_field('current_c', $filterby_c).tep_draw_hidden_field('current_p', $filterby_p).tep_draw_hidden_field('current_s', $filterby_s).'</form></div>';

		$gridHTML .= '
			<script language="javascript">
			<!--

			jQuery(document).ready(function() {
		';

		if ($cdkey_query_numrows > MAX_DISPLAY_SEARCH_RESULTS_CDK)
				$gridHTML .= '			 	jQuery(maingrid.page).removeOption("all");';
				$gridHTML .= '			 	jQuery(maingrid.page).attr("onchange","document.location.href=\'?action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s='.urlencode($filterby_s).'&page=\'+jQuery(maingrid.page).val()");';

		$gridHTML .= '
			});

			//-->
			</script>
		';

		return $gridHTML;
	}

	// $cdkid - CDK ID / Order ID / PO Ref Num.
	// $type - 1 : CDK ID , 2 : Order ID , 3 : Purchase Order Reference Number

	function tep_buildGridID($cdkid, $type)
	{
		global $view_cdkey_images_permission, $filterby_c_opt_arr, $product_obj;

		$langID = ($_SESSION["languages_id"] ? $_SESSION["languages_id"] : 1);

		//if ($view_cdkey_images_permission)
		$gridHTML .= '<div style="text-align: right; padding:5px;" id="show_all_cdk"><a href="javascript:getCDKeyImg_list(\'1\')">View All My Uploaded CD Key</a></div>';

		$gridHTML .= '
					<form name=maingrid>
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tbody id="header_'.$i.'" class="show">
							<tr>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_CATEGORY.'</td>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_CDK_ID.'</td>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_PO_REF_NUM.'</td>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_ORDER_ID.'</td>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_RELEASE_DATE.'</td>
								<td class="reportBoxHeading" align="left">'.TABLE_HEADING_DATE.'</td>';

		//if ($view_cdkey_images_permission) {
			$gridHTML .= '      						<td class="reportBoxHeading" align="center">'. TABLE_HEADING_FILENAME .'</td>';
			$gridHTML .= '				                <td class="reportBoxHeading" align="center">'. TABLE_HEADING_CDK_FILE .'</td>';
		//}

		$gridHTML .= '
				                <td class="reportBoxHeading" align="center">'. TABLE_HEADING_STATUS .'</td>
				                <td class="reportBoxHeading" align="center">'. TABLE_HEADING_REMARKS .'</td>
							</tr>
						</tbody>';

		//Start customise sql

		if ($type == "1"){
			$cdkey_select_sql = "	select a.orders_products_id, p2c.categories_id, a.custom_products_code_id, a.products_id, a.status_id, a.file_name, a.file_type, a.code_date_modified, a.code_date_added, a.code_uploaded_by, a.remarks, a.purchase_orders_id, b.products_name
									from " . TABLE_PRODUCTS . " p,
										".TABLE_CUSTOM_PRODUCTS_CODE. " a,
										".TABLE_PRODUCTS_DESCRIPTION." b,
										". TABLE_PRODUCTS_TO_CATEGORIES . " p2c
									where b.products_id=a.products_id
										and p2c.products_id=p.products_id
										and a.products_id=p.products_id
										and p2c.products_is_link = 0
										and a.custom_products_code_id='".$cdkid."'
										and b.language_id=".(int)$langID;
		} else if ($type == "2") {
			$custom_products_code_id_array = array();

			$cdkey_orders_id_select_sql = "	select ocp.orders_custom_products_value
											from " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
												ON ocp.orders_products_id=op.orders_products_id
											WHERE orders_custom_products_key='cd_key_id' AND op.orders_id ='".$cdkid."'";
			$cdkey_orders_id_result_sql = tep_db_query($cdkey_orders_id_select_sql);
			while ($cdkey_orders_id_row = tep_db_fetch_array($cdkey_orders_id_result_sql)) {
				$orders_custom_products_value_array = explode(',', $cdkey_orders_id_row['orders_custom_products_value']);

				foreach ($orders_custom_products_value_array as $custom_products_code_id)
					$custom_products_code_id_array[] = "a.custom_products_code_id='".$custom_products_code_id."'";
			}

			$product_code_statement = implode(" OR ", $custom_products_code_id_array);

			if ($product_code_statement) {
				$cdkey_select_sql = "select p2c.categories_id, a.orders_products_id, a.custom_products_code_id, a.products_id, a.status_id, a.file_name, a.file_type, a.code_date_modified, a.code_date_added, a.code_uploaded_by, a.remarks, a.purchase_orders_id, b.products_name
									from " . TABLE_PRODUCTS . " p,
										".TABLE_CUSTOM_PRODUCTS_CODE. " a,
										".TABLE_PRODUCTS_DESCRIPTION." b,
										". TABLE_PRODUCTS_TO_CATEGORIES . " p2c
									where b.products_id=a.products_id
										and p2c.products_id=p.products_id
										and a.products_id=p.products_id
										and p2c.products_is_link = 0
										and (".$product_code_statement.")
										and b.language_id=".(int)$langID;
			}
		} else if ($type == "3") {
			$cdkey_select_sql = "	select p2c.categories_id, a.orders_products_id, a.custom_products_code_id, a.products_id, a.status_id, a.file_name, a.file_type, a.code_date_modified, a.code_date_added, a.code_uploaded_by, a.remarks, a.purchase_orders_id, b.products_name
									from " . TABLE_PRODUCTS . " p,
										".TABLE_CUSTOM_PRODUCTS_CODE. " a,
										".TABLE_PRODUCTS_DESCRIPTION." b,
										". TABLE_PRODUCTS_TO_CATEGORIES . " p2c
									where b.products_id=a.products_id
										and p2c.products_id=p.products_id
										and a.products_id=p.products_id
										and p2c.products_is_link = 0
										and a.purchase_orders_id='".$cdkid."'
										and b.language_id=".(int)$langID;
		}

		$i=0;

		if ($cdkey_select_sql) {
			$cdkey_result_sql = tep_db_query($cdkey_select_sql);

			$mouseover = "onmouseover=\"rowOverEffect(this, 'reportListingRowOver')\" onclick=\"rowClicked(this, 'reportListingOdd')\"";
			while ($cdkey_row = tep_db_fetch_array($cdkey_result_sql)) {

				$temp_category_id = $cdkey_row['categories_id'];

				if ($cdkey_row['status_id'] == 0)
					$status_id_url = "Sold";
				else if ($cdkey_row['status_id'] == -2)
					$status_id_url = "On+Hold";
				else if ($cdkey_row['status_id'] == 1)
					$status_id_url = "Actual";
				else if ($cdkey_row['status_id'] == -1)
					$status_id_url = "Disabled";
                else if ($cdkey_row['status_id'] == -3)
                    $status_id_url = "Preorder";
                else if ($cdkey_row['status_id'] == -4)
                    $status_id_url = "G2G Pin Request";
                else if ($cdkey_row['status_id'] == -5)
                    $status_id_url = "Reserved";

				if (count($filterby_c_opt_arr)) {
					foreach ($filterby_c_opt_arr as $category_id_row){
						if ($category_id_row['id'] == $cdkey_row['categories_id']) {
							$print_category = '<a href="'.tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$cdkey_row['categories_id'].'&filterby_p=&filterby_s='.$status_id_url).'">'.$category_id_row['text'].'</a>';
							break;
						}
					}
				}

				if (count($product_obj[$temp_category_id])) {
					foreach ($product_obj[$temp_category_id] as $product_id_row){
						if ($product_id_row['id'] == $cdkey_row['products_id']) {
							$print_product = '<a href="'.tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$cdkey_row['categories_id'].'&filterby_p='.$cdkey_row['products_id'].'&filterby_s='.$status_id_url).'">'.$product_id_row['text'].'</a>';
							break;
						}
					}
				}

				$rowFormat = ($rowFormat=='Even') ? 'Odd' : 'Even';
				$gridHTML .= "<tbody id=\"detail_{$i}\" class=\"show\">";
				$gridHTML .= "<tr class='reportListing{$rowFormat}' onmouseout=\"rowOutEffect(this, 'reportListing{$rowFormat}')\" {$mouseover} >\n";

				if ($view_cdkey_images_permission || $cdkey_row['code_uploaded_by'] == $_SESSION['login_email_address']) {
					$cdkey_id = '<a href="javascript:;" onClick="Show_CDK_Image(\''.$cdkey_row['custom_products_code_id'].'\');">'.$cdkey_row['custom_products_code_id'].'</a>';
				} else {
					$cdkey_id = $cdkey_row['custom_products_code_id'];
				}

				if ($cdkey_row['file_name'] || $cdkey_row['file_type'])
					$filename = tep_db_prepare_input($cdkey_row['file_name'].'.'.$cdkey_row['file_type']);
				else
					$filename = '';

				$orderId_str = "";
				if ($cdkey_row['status_id']==0) {
					//Get OrderID for Sold CDKey
					$orderId_str = 'Unknown';
					$cdkey_orders_id_select_sql = "	SELECT op.orders_id
													FROM " . TABLE_ORDERS_PRODUCTS . " AS op
													WHERE op.orders_products_id = '".$cdkey_row['orders_products_id']."'";
					$cdkey_orders_id_result_sql = tep_db_query($cdkey_orders_id_select_sql);

					if ($cdkey_orders_id_row = tep_db_fetch_array($cdkey_orders_id_result_sql)) {
						$orderId_str = "<a href='" . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('gID', 'action', 'selected_box', 'sortby', 'filterby_p', 'filterby_s', 'page')) . "oID={$cdkey_orders_id_row['orders_id']}&action=edit") . "' target='_newwin'>";
						$orderId_str .= $cdkey_orders_id_row['orders_id'] . '</a>';
					}
				}

				$poRef_str = "&nbsp;";
				if ($cdkey_row['purchase_orders_id'] != '') {
					$po_ref_encoded = urlencode($cdkey_row['purchase_orders_id']);
					$poRef_str = "<a href='" . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('action', 'subaction', 'selected_box', 'sortby', 'filterby_c', 'filterby_p', 'filterby_s', 'filterby_order_id', 'filterby_cdk_id', 'filterby_po_ref', 'page')) . "po_id={$po_ref_encoded}&subaction=edit_po") . "' target='_newwin'>";
					$poRef_str .= get_po_reference_number($cdkey_row['purchase_orders_id']) . '</a>';
				}

				if ($cdkey_row['status_id'] == 0)
					$status_id_str = "<font color=blue>Sold</font>";
				else if ($cdkey_row['status_id'] == -2)
					$status_id_str = "<font color=red>On Hold</font>";
				else if ($cdkey_row['status_id'] == 1)
					$status_id_str = "<font color=green>Actual</font>";
				else if ($cdkey_row['status_id'] == -1)
					$status_id_str = "Disabled";
                else if ($cdkey_row['status_id'] == -3)
                    $status_id_str = "Preorder";
                else if ($cdkey_row['status_id'] == -4)
                    $status_id_str = "G2G Pin Request";

				$gridHTML .= '								<td class="reportRecords" valign="top" align="left">'.$print_category.' > '.$print_product.'</td>';
				$gridHTML .= '								<td class="reportRecords" valign="top" align="left">'.$cdkey_id .'</td>';

				$gridHTML .= '								<td class="reportRecords" valign="top" align="left">'.$poRef_str .'</td>';

				if ($cdkey_row['status_id']==0)
					$gridHTML .= '								<td class="reportRecords" valign="top" align="center">'. $orderId_str .'</td>';
				else
					$gridHTML .= '								<td class="reportRecords" valign="top" align="center"><br></td>';

				$gridHTML .= '
									<td class="reportRecords" valign="top" align="center">'. tep_date_short($cdkey_row['code_date_added'], PREFERRED_DATE_FORMAT) .'</td>
									<td class="reportRecords" valign="top" align="center">'. tep_date_short($cdkey_row['code_date_modified'], PREFERRED_DATE_FORMAT) .'</td>
							';

				//Start show cdkey
				//if ($view_cdkey_images_permission) {
					$gridHTML .= '								<td class="reportRecords" valign="top" align="center">'.$cdkey_row['file_name'].'</div></td>';
					$custom_products_code_id = $cdkey_row['custom_products_code_id'];
					$gridHTML .= '								<td class="reportRecords" valign="top" align="center"><div id="Show_CDK_image_'.$custom_products_code_id.'"><a href="javascript:Show_CDK_Image(\''.$custom_products_code_id.'\')">Show CDK Image</a></div><div id="CDK_image_'.$custom_products_code_id.'" style="display: none"></div></td>';
				//}

				$gridHTML .= '
									<td class="reportRecords" valign="top" align="center"><b><big>'.$status_id_str.'</big></b></td>
									<td class="reportRecords" valign="top" align="center">
										<a href="javascript:;" onClick="getCDKeyLog('.$cdkey_row['custom_products_code_id'].')">'.tep_image(DIR_WS_ICONS.'statistics.gif').'</a>&nbsp;
									</td>
							  	 </tr>
							</tbody>'.tep_draw_hidden_field("gselection[$i]", $cdkey_row['custom_products_code_id'],'id="gselection['.$i.']"');
				$i++;
			}
		}

		$gridHTML .= '</table>'.tep_draw_hidden_field('gnumRows', $i).'</form>';

		return $gridHTML;
	}

	function tep_buildUploadForm()
	{
		global $file_upload_types, $gInfo, $filterby_p, $filterby_c,$filterby_c_opt_arr,$filterby_p_opt_arr;

		$gridHTML = '';
		$gridHTML .= tep_draw_form('upload_form', FILENAME_CDKEY, 'action=upload&cont=1', 'post', ' enctype="multipart/form-data" onSubmit="return check_form();"')
						."\n<table border=\"0\" width=\"100%\" cellspacing=\"2\" cellpadding=\"2\">
						  	<tr>
						      	<td class=\"smallText\" valign=\"top\">" . LABEL_LIST_CATEGORY . "</td>
							  	<td class=\"smallText\" valign=\"top\">";

					if (count($filterby_c_opt_arr) > 1) $gridHTML .= tep_draw_pull_down_menu('filterby_c_up', $filterby_c_opt_arr, $filterby_c , ' id="select_product_category_up"');

					$gridHTML .= '		</td>
						  	</tr>
						  	<tr>
						      	<td class="smallText" valign="top">' . LABEL_LIST_PRODUCT . '</td>
							  	<td class="smallText" valign="top">';

					if (count($filterby_c_opt_arr) > 1) $gridHTML .= tep_draw_pull_down_menu('filterby_p_up', $filterby_p_opt_arr, $filterby_p , ' id="select_product_up"');

					$gridHTML .= "		</td>
						  	</tr>
							<tr>
							<td class=\"smallText\" align=\"left\">".LABEL_FILENAME."</td>
							<td class=\"smallText\" align=\"left\">".tep_draw_file_field('upload_file', ' size="40" id="u_upload_file" ', true) . " (Max: ". " (Max: ".(substr(ini_get('upload_max_filesize'), 0, -1) < substr(ini_get('post_max_size'), 0, -1) ? ini_get('upload_max_filesize') : ini_get('post_max_size')).")</td>
						  </tr>
							<tr>
							<td class=\"smallText\" align=\"left\">".LABEL_FILETYPES_ALLOWED."</td>
							<td class=\"smallText\" align=\"left\">". implode(', ', $file_upload_types) . " </td>
						  </tr>
							<tr>
							<td class=\"smallText\" align=\"left\" valign=\"top\">".LABEL_ZIP_FILE_OPTION."</td>
							<td class=\"smallText\" align=\"left\">".
							tep_draw_radio_field('upload_to', 'manage_vault', true) . OPTIONS_UPLOAD_TO_MANAGE_VAULT . "<br>".
							tep_draw_radio_field('upload_to', 'live', false) . OPTIONS_UPLOAD_TO_LIVE."<br>".
							tep_draw_radio_field('upload_to', 'jpg', false) . OPTIONS_UPLOAD_TO_JPG."
							</td>
						  </tr>
							<tr>
								<td class=\"smallText\" align=\"left\">".LABEL_QUANTITY."</td>
								<td class=\"smallText\" align=\"left\">".tep_draw_input_field('zip_qty', '',' size="10" id="u_zip_qty" ', true) . "</td>
						  </tr>
							<tr>
								<td class=\"smallText\" align=\"left\">".LABEL_UNIT_PRICE."</td>
								<td class=\"smallText\" align=\"left\">".tep_draw_input_field('zip_unit_price', '',' size="10" id="u_zip_unit_price" ', true) . "</td>
						  </tr><tr>
									<td class=\"smallText\" align=\"left\">".LABEL_DESCRIPTION."</td>
									<td class=\"smallText\" align=\"left\">".tep_draw_input_field('zip_description','', ' size="60" id="u_zip_description" maxlength="255"', false) . "</td>
						  </tr><tr>
							<td class=\"smallText\" colspan=\"2\">
							".tep_button(BUTTON_UPLOAD, BUTTON_UPLOAD, '', 'onmouseout="this.className=\'inputButton\'" onmouseover="this.className=\'inputButtonOver\'" onclick="submit_form(this)"', 'inputButton', true)."
							".tep_draw_hidden_field('MAX_FILE_SIZE', '50000')."</td>
						  </tr></table>
						</form>";
		return $gridHTML;
	}

	function tep_buildPOUploadForm()
	{
		global $file_upload_types, $gInfo, $filterby_p, $filterby_c, $filterby_c_opt_arr, $filterby_p_opt_arr, $po_id, $uploadfor_po;

		$gridHTML = '';
        $gridHTML .= tep_draw_form('upload_po_form', FILENAME_CDKEY, 'action=upload_via_po&cont=1', 'post', ' enctype="multipart/form-data" onSubmit="return check_po_form();"')
						.tep_draw_hidden_field('po_id', $po_id)
						."\n<table border=\"0\" width=\"100%\" cellspacing=\"2\" cellpadding=\"2\">
						  <tr>
							<td class=\"smallText\" align=\"left\">".LABEL_PO_NUMBER."</td>
							<td class=\"smallText\" align=\"left\">".tep_draw_input_field('po_number', $uploadfor_po, ' size="40" id="u_po_number" ', true) . "</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" valign=\"top\">" . LABEL_LIST_CATEGORY . "</td>
							<td class=\"smallText\" valign=\"top\">";
					if (count($filterby_c_opt_arr) > 1) $gridHTML .= tep_draw_pull_down_menu('filterby_c_up', $filterby_c_opt_arr, $filterby_c , ' id="select_po_product_category_up"');

		$gridHTML .= '		</td>
						  </tr>
						  <tr>
							<td class="smallText" valign="top">' . LABEL_LIST_PRODUCT . '</td>
							<td class="smallText" valign="top">';
					if (count($filterby_c_opt_arr) > 1) $gridHTML .= tep_draw_pull_down_menu('filterby_p_up', $filterby_p_opt_arr, $filterby_p , ' id="select_po_product_up"');

		$gridHTML .= "		</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" align=\"left\">".LABEL_FILENAME."</td>
							<td class=\"smallText\" align=\"left\">".tep_draw_file_field('upload_file', ' size="40" id="u_po_upload_file" ', true) . " (Max: ".(substr(ini_get('upload_max_filesize'), 0, -1) < substr(ini_get('post_max_size'), 0, -1) ? ini_get('upload_max_filesize') : ini_get('post_max_size')).")</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" align=\"left\">".LABEL_FILETYPES_ALLOWED."</td>
							<td class=\"smallText\" align=\"left\">". implode(', ', $file_upload_types) . " </td>
						  </tr>
						  <tr>
							<td class=\"smallText\" align=\"left\" valign=\"top\">".LABEL_ZIP_FILE_OPTION."</td>
							<td class=\"smallText\" align=\"left\">".
							tep_draw_radio_field('po_upload_to', 'manage_vault', true) . OPTIONS_UPLOAD_TO_MANAGE_VAULT . "<br>".
							tep_draw_radio_field('po_upload_to', 'live', false) . OPTIONS_UPLOAD_TO_LIVE."<br>".
							tep_draw_radio_field('po_upload_to', 'jpg', false) . OPTIONS_UPLOAD_TO_JPG."
							</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" align=\"left\">".LABEL_QUANTITY."</td>
							<td class=\"smallText\" align=\"left\">".tep_draw_input_field('zip_qty', '',' size="10" id="u_po_zip_qty" ', true) . "</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" align=\"left\">".LABEL_DESCRIPTION."</td>
							<td class=\"smallText\" align=\"left\">".tep_draw_input_field('zip_description','', ' size="60" id="u_po_zip_description" maxlength="255"', false) . "</td>
						  </tr>
						  <tr>
							<td class=\"smallText\" colspan=\"2\">
							".tep_button(BUTTON_UPLOAD, BUTTON_UPLOAD, '', 'onmouseout="this.className=\'inputButton\'" onmouseover="this.className=\'inputButtonOver\'" onclick="submit_form(this)"', 'inputButton', true)."
							".tep_draw_hidden_field('MAX_FILE_SIZE', '50000')."</td>
						  </tr></table>
						</form>";
		return $gridHTML;
	}

	function get_po_id_by_reference($po_ref_id) {
		$po_id = '';
		$get_po_select_sql = "SELECT purchase_orders_id FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_ref_id='" . tep_db_input($po_ref_id) . "'";
		$get_po_result_sql = tep_db_query($get_po_select_sql);
		if ($get_po_row = tep_db_fetch_array($get_po_result_sql)) {
			$po_id = $get_po_row['purchase_orders_id'];
		}
		return $po_id;
	}

	function get_po_reference_number($po_id) {
		$po_ref_id = '';
		$get_po_select_sql = "SELECT purchase_orders_ref_id FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id='" . tep_db_input($po_id) . "'";
		$get_po_result_sql = tep_db_query($get_po_select_sql);
		if ($get_po_row = tep_db_fetch_array($get_po_result_sql)) {
			$po_ref_id = $get_po_row['purchase_orders_ref_id'];
		}
		return $po_ref_id;
	}
//-------------------------------------------------------------------------------------------
//------End presentation functions

//------Permissions
	$view_cdkey_images_permission = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_CDKEY_IMAGES');
 	$view_remarks = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_REMARKS');
 	$search_by_id = tep_admin_files_actions(FILENAME_CDKEY, 'CP_SEARCH_BY_ID');
 	$update_status_actual = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_STATUS_ACTUAL');
 	$update_status_disabled = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_STATUS_DISABLED');
 	$update_status_on_hold = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_STATUS_ON_HOLD');
 	$update_status_bulk_remarks = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_STATUS_BULK_REMARKS');
 	$update_status_delete = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_STATUS_DELETE');
 	$update_vault_status = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPDATE_VAULT_STATUS');
 	$download_vault_zip_file = tep_admin_files_actions(FILENAME_CDKEY, 'CP_DOWNLOAD_VAULT_ZIP_FILE');
 	$unzip_vault = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UNZIP_VAULT');
 	$upload_cd_key = tep_admin_files_actions(FILENAME_CDKEY, 'CP_UPLOAD_CD_KEY');
 	$add_remarks = tep_admin_files_actions(FILENAME_CDKEY, 'CP_ADD_REMARKS');

//------Assign Globals and do init work here
	$log_object = new log_files($login_id);
	$cdkey_products = array();
    $file_upload_types = array('zip','jpg','png','gif');

    $cpc_obj = new custom_product_code();
    $purchase_control_tool_object = new purchase_control_tool();

	$status_arr = array('1'=>LINK_FILTER_BY_ACTUAL, '-1'=>LINK_FILTER_BY_DISABLED, '-2'=>LINK_FILTER_BY_ONHOLD);
	$status_opt_arr[] = array('id'=>'','text'=>LABEL_SET_STATUS);
	foreach ($status_arr as $key => $val) {
		$status_opt_arr[] = array('id'=>$key,'text'=>$val);
	}

	//Filter by Status
	$filterby_s_opt_arr = array(
									array('id'=>'Actual','text'=>LINK_FILTER_BY_ACTUAL),
									array('id'=>'On Hold','text'=>LINK_FILTER_BY_ONHOLD),
									array('id'=>'Sold','text'=>LINK_FILTER_BY_SOLD),
									array('id'=>'Disabled','text'=>LINK_FILTER_BY_DISABLED),
                                    array('id'=>'G2G','text'=>LINK_FILTER_BY_G2G),
                                    array('id'=>'Reserved','text'=>LINK_FILTER_BY_RESERVED),
									array('id'=>'Zip','text'=>LINK_FILTER_BY_ZIP)

								);

	//Filter By Product Category
	$filterby_c_opt_arr = get_cdkey_category_selection();
	$product_obj = array('categoryid'=>array());//tep_get_cdkey_product_object();

	$filterby_p_opt_arr [] = array ('id' => 0, 'text' => '---'.PULL_DOWN_DEFAULT.'---');

	$sortby = ($_GET['sortby']? $_GET['sortby'] : '');
	$filterby_s = ($_GET['filterby_s']? $_GET['filterby_s'] : '');
	$filterby_p = ($_GET['filterby_p']? $_GET['filterby_p'] : '');
	$filterby_c = ($_GET['filterby_c']? $_GET['filterby_c'] : '');
	$po_id = ($_GET['po_id']? $_GET['po_id'] : '');
	$uploadfor_po = '';

	if (tep_not_null($_GET['po_id'])) {
		$uploadfor_po = get_po_reference_number($_GET['po_id']);

		// get cdkey category ID from product ID
		$product_select_sql = "SELECT products_cat_id_path FROM " . TABLE_PRODUCTS . " WHERE products_id = '".tep_db_input($filterby_p)."'";
		$product_result_sql = tep_db_query($product_select_sql);
		if ($product_row = tep_db_fetch_array($product_result_sql)) {
			$prod_cats_id_arr = explode('_', $product_row['products_cat_id_path']);
			foreach ($prod_cats_id_arr as $prod_cat_id) {
				if (tep_not_null($prod_cat_id)) {
					$cat_select_sql = "SELECT categories_id FROM " . TABLE_CATEGORIES . " WHERE custom_products_type_id = 2 AND categories_id = '".$prod_cat_id."'";
					$cat_result_sql = tep_db_query($cat_select_sql);
					if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
						$filterby_c = $cat_row['categories_id'];
                        break;
					}
				}
			}
		}
	}

//------Globals end.

//------System Actions
	if ($_GET['action']) {
		switch($_GET['action']) {
			case 'search':
                $messageStack->identifier = (empty($_GET['stack_id']) ? '' : $_GET['stack_id']);
                $messageStack->loadMessageStack();
				if (!empty($_GET['filterby_c'])) {
					$filterby_c = $_GET['filterby_c'];
				}

				if (!empty($_GET['filterby_p'])) {
					$filterby_p = $_GET['filterby_p'];
				}

				if (!empty($_GET['filterby_s'])) {
					$filterby_s = $_GET['filterby_s'];
				}

				if ($search_by_id) {
					if (!empty($_GET['filterby_order_id'])) {
						$filterby_order_id = $_GET['filterby_order_id'];
					}

					if (!empty($_GET['filterby_cdk_id'])) {
						$filterby_cdk_id = $_GET['filterby_cdk_id'];
					}

					if (!empty($_GET['filterby_po_ref'])) {
						$filterby_po_id = get_po_id_by_reference(urldecode($_GET['filterby_po_ref']));
					}
				}

				if ($filterby_order_id) {
					$HTMLgrid = tep_buildGridID($filterby_order_id, '2');
				}
				else if ($filterby_cdk_id) {
					$HTMLgrid = tep_buildGridID($filterby_cdk_id, '1');
				}
				else if ($filterby_po_ref && !empty($filterby_po_id)) {
					$HTMLgrid = tep_buildGridID($filterby_po_id, '3');
				}
				else if ($filterby_c && $filterby_p) {
					if ($filterby_s == "Zip")
						$HTMLgrid = tep_buildGridUNZIP($sortby,$filterby_c,$filterby_p);
					else
						$HTMLgrid = tep_buildGrid($sortby,$filterby_c,$filterby_p,$filterby_s);
				} else if ($filterby_c) {
					$HTMLgrid = tep_buildGridSTATS($sortby,$filterby_c);
				}
				else {
					$HTMLgrid = "";
				}

				break;
			case 'upload':
				// $upload_obj = $_POST;

				// if ($upload_cd_key) {
				// 	if (empty($upload_obj['filterby_p_up']))
				// 		$messageStack->add(ERROR_INVALID_PRODUCTID, 'error');

				// 	if ($messageStack->size <= 0)
				// 		tep_process_upload($upload_obj);
				// }

				// if ($upload_obj['filterby_c_up'])
				// 	$filterby_c = $upload_obj['filterby_c_up'];
				// else
				// 	$filterby_c = $_POST['filterby_c'];

				// if ($upload_obj['filterby_p_up'])
				// 	$filterby_p = $upload_obj['filterby_p_up'];
				// else
				// 	$filterby_p = $_POST['filterby_p'];

				// if ($upload_obj['upload_to'] == "manage_vault")
				// 	tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s=Zip#tab-upload'));
				// else
				// 	tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s=On+Hold#tab-upload'));

				// exit;

                break;
			case 'upload_via_po':
				$upload_obj = $_POST;
                $messageStack->reset();
                $identifier = (isset($_FILES['upload_file']['tmp_name']) ? end(explode(DIRECTORY_SEPARATOR ,$_FILES['upload_file']['tmp_name'])) : time());
				$messageStack->identifier = (empty($_GET['stack_id']) ? $identifier : $_GET['stack_id']);
                $messageStack->loadMessageStack();
				if ($upload_cd_key) {
					if (empty($upload_obj['filterby_p_up']))
						$messageStack->add(ERROR_INVALID_PRODUCTID, 'error');

					if ($messageStack->size <= 0) {
						tep_process_upload_via_po($upload_obj);
					}
				}

				if ($upload_obj['filterby_c_up'])
					$filterby_c = $upload_obj['filterby_c_up'];
				else
					$filterby_c = $_POST['filterby_c'];

				if ($upload_obj['filterby_p_up'])
					$filterby_p = $upload_obj['filterby_p_up'];
				else
					$filterby_p = $_POST['filterby_p'];

				if ($upload_obj['po_id'])
					$po_id = $upload_obj['po_id'];
				else
					$po_id = $_POST['po_id'];

				if ($messageStack->size <= 0) {
					if ($upload_obj['po_upload_to'] == "manage_vault")
						tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&po_id='.$po_id.'&filterby_c='.$filterby_c.'&stack_id='.$messageStack->identifier.'&filterby_p='.$filterby_p.'&filterby_s=Zip#tab-po-upload'));
					else
						tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&po_id='.$po_id.'&filterby_c='.$filterby_c.'&stack_id='.$messageStack->identifier.'&filterby_p='.$filterby_p.'&filterby_s=On+Hold#tab-po-upload'));

					exit;
				}

				break;
			case 'showcdk':
				$cdk_id = tep_db_prepare_input($_POST['cdk_id']);
                $ajax_remark = tep_db_prepare_input($_POST['remark']);
                $ajax_result = 0;
                $ajax_data = ERROR_FILE_NOT_FOUND;
                $permissionView = TRUE;
                $userID = $_SESSION["login_id"];
				$currentDate = date("Y-m-d H:i:s");

                $cdkey_image_select_sql = "	select file_type, code_uploaded_by, products_id, to_s3, code_date_added
                                        from " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                        WHERE custom_products_code_id = '".$cdk_id."'";
                $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
                $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);

				//Mohamad - CDKey view limit and captured
                if ($_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND ($view_cdkey_images_permission AND isset($ajax_remark))) {

                    $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '" . $userID . "'";
                    $getCurrentTotalView = tep_db_query($getCurrentTotalSql);
                    if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
                        $totalView = $totalViewFetch['cdkey_total_view'];
                        $cdkey_limit = $totalViewFetch['cdkey_limit'];

                        if ($totalView >= $cdkey_limit) {
                            $permissionView = FALSE;
                        } else {
                            $newTotalView = $totalView + 1;
                            $update_sql_data_array = array('cdkey_total_view' => $newTotalView, 'cdkey_updated_datetime' => $currentDate);
                            tep_db_perform(TABLE_CDKEY_VIEW, $update_sql_data_array, 'update', "cdkey_user_id = '".$userID."'");
                        }
                    } else {
                        $permissionView = FALSE;
                    }

                } else if ($_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND ($view_cdkey_images_permission AND !isset($ajax_remark))) {

                    $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '" . $userID . "'";
                	$getCurrentTotalView = tep_db_query($getCurrentTotalSql);
                    if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
                        $totalView = $totalViewFetch['cdkey_total_view'];
                        $cdkey_limit = $totalViewFetch['cdkey_limit'];

                        if ($totalView >= $cdkey_limit) {
                            $permissionView = FALSE;
                        } else {
                            $permissionView = TRUE;
                        }
                    } else {
                        $permissionView = FALSE;
                    }
                }

                //checking for non owner permission
                if ( $_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND
                        ($view_cdkey_images_permission AND tep_not_null($ajax_remark) AND $permissionView === TRUE) ) {

                    if (tep_not_null($ajax_remark))     tep_add_Remarks($cdk_id, $ajax_remark);

                    if (strtolower($cdkey_image_row['file_type']) == "soft") {
                        $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);

                        if ($theData !== FALSE) {
                            $ajax_result = 1;
                            $ajax_data = tep_decrypt_data($theData);
                        }
                    } else {
                       // if (file_exists(DIR_FS_SECURE.$cdk_id.'.key')) {
                            $ajax_result = 1;
                            $ajax_data = '<img src="?action=cdk_image&cdk_id='.$cdk_id.'">';
                       // }
                    }

				//checking for owner
                } else if ($_SESSION['login_email_address'] == $cdkey_image_row['code_uploaded_by']) {

	                $cdkey_image_select_sql = "	select file_type, code_uploaded_by, products_id, to_s3, code_date_added
	                                            from " . TABLE_CUSTOM_PRODUCTS_CODE . "
	                                            WHERE custom_products_code_id = '".$cdk_id."'";
	                $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
	                $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);

                    if (tep_not_null($ajax_remark))     tep_add_Remarks($cdk_id, $ajax_remark);

                    if (strtolower($cdkey_image_row['file_type']) == "soft") {
                        $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);

                        if ($theData !== FALSE) {
                            $ajax_result = 1;
                            $ajax_data = tep_decrypt_data($theData);
                        }
                    } else {
                       // if (file_exists(DIR_FS_SECURE.$cdk_id.'.key')) {
                            $ajax_result = 1;
                            $ajax_data = '<img src="?action=cdk_image&cdk_id='.$cdk_id.'">';
                       // }
                    }
                } else {

                	if ($permissionView === FALSE) {
                            $ajax_result = 3;
							$ajax_data = 'CDKey View limit has been reached. Please come back tomorrow.';
                	} else {
                		if ($view_cdkey_images_permission) {
                            $ajax_result = 2;
                            $ajax_data = 'Request for remark';
	                	} else {
	                        $ajax_data = ERROR_NO_PERMISSION;
	                    }
                	}

                }

                echo json_encode(array('result' => $ajax_result, 'value' => $ajax_data));

				exit;
                break;
			case 'cdk_image':
				$cdk_id = tep_db_prepare_input($_GET['cdk_id']);
                $userID = $_SESSION["login_id"];

                $cdkey_image_select_sql = "	select code_uploaded_by, products_id, to_s3, code_date_added
                                            from " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                            WHERE custom_products_code_id = '".$cdk_id."'";
                $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
                $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);

				if ($cdkey_image_row['code_uploaded_by'] != $_SESSION['login_email_address'] AND $view_cdkey_images_permission) {

                    $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '".$userID."'";
					$getCurrentTotalView = tep_db_query($getCurrentTotalSql);
					if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
						$totalView = $totalViewFetch['cdkey_total_view'];
						$cdkey_limit = $totalViewFetch['cdkey_limit'];

						if ($totalView >= $cdkey_limit) {
							echo 'CDKey View limit has been reached. Please come back tomorrow.';
						} else {
							$theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);

		                    if ($theData !== FALSE) {
		                        $theData = tep_decrypt_data($theData);

		                        header('Content-type: image/jpg');
		                        header("Expires: Mon, 02 May 2001 23:00:00 GMT");
		                        header("Cache-Control: no-store, no-cache, must-revalidate");
		                        header("Cache-Control: post-check=0, pre-check=0", false);
		                        header("Pragma: no-cache");

		                        echo $theData;
		                    }
						}
					} else {
						echo 'CDKey View limit has been reached. Please come back tomorrow.';
					}

				} else if ($cdkey_image_row['code_uploaded_by'] == $_SESSION['login_email_address']) {

					$theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);

                    if ($theData !== FALSE) {
		                $theData = tep_decrypt_data($theData);

		                header('Content-type: image/jpg');
		                header("Expires: Mon, 02 May 2001 23:00:00 GMT");
		                header("Cache-Control: no-store, no-cache, must-revalidate");
		                header("Cache-Control: post-check=0, pre-check=0", false);
		                header("Pragma: no-cache");

		                echo $theData;
		            }
                }

				exit;
				break;
			case 'batch_update':
				if ($update_status_actual || $update_status_disabled || $update_status_on_hold || $update_status_bulk_remarks || $update_status_delete)
					tep_update_keycode_attrib_batch();

				$filterby_p = $_POST["current_p"];
				$filterby_s = $_POST["current_s"];
				$filterby_c = $_POST["current_c"];

				switch($_POST["cb_status"]) {
					case 'delete':
						break;

					case 'delete_zip':
						$filterby_s = "Zip";
						break;

					case 'remarks':
						break;

					default:
						$new_status = (int)$_POST['cb_status'];

						if ($new_status == "1")
							$filterby_s = "Actual";
						else if ($new_status == "-2")
							$filterby_s = "On Hold";
						else if ($new_status == "-1")
							$filterby_s = "Disabled";

						break;
				}

				tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s='.urlencode($filterby_s)));
				break;
			case 'downloadfile':
				if ($download_vault_zip_file) {
					$products_vault_id = $_GET['products_vault_id'];

					if (file_exists(DIR_FS_SECURE.$products_vault_id.'.keyzip')) {
						$fh = fopen(DIR_FS_SECURE.$products_vault_id.'.keyzip', 'r') or die("can't open file");
						$theData = fread($fh, filesize(DIR_FS_SECURE.$products_vault_id.'.keyzip'));
						fclose($fh);

						$theData = tep_decrypt_data($theData);

						$filename = $_GET['filename'];
						$mime_type = 'application/zip';
						// Download
				        header('Content-Type: ' . $mime_type);
				        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
				        // IE need specific headers
				        if (PMA_USR_BROWSER_AGENT == 'IE') {
				            header('Content-Disposition: attachment; filename="' . $filename . '"');
				            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				            header('Pragma: public');
				        } else {
				            header('Content-Disposition: attachment; filename="' . $filename . '"');
				            header('Pragma: no-cache');
				        }

						echo $theData;
					}
				}

				exit;
                break;
			case 'add_remarks':
				$custom_product_id = $_GET['custom_product_id'];
				$remarks = $_GET['remarks'];

				if ($add_remarks) {
					tep_add_Remarks($custom_product_id,$remarks);

					$new_custom_products_code_log_id = tep_db_insert_id();

					$log_select_sql = "	SELECT custom_products_code_log_user, custom_products_code_log_user_role, log_time, log_system_messages, log_ip
                                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE_LOG . "
                                        WHERE custom_products_code_log_id='" . tep_db_input($new_custom_products_code_log_id) . "'";
					$log_result_sql = tep_db_query($log_select_sql);
					while ($log_row = tep_db_fetch_array($log_result_sql)) {
						switch($log_row['custom_products_code_log_user_role']) {
							case 'customers':
								$email_select_sql = "	SELECT customers_email_address
														FROM " . TABLE_CUSTOMERS . "
														WHERE customers_id = '" . tep_db_input($log_row['custom_products_code_log_user']) . "'";
								$email_result_sql = tep_db_query($email_select_sql);
								$email_row = tep_db_fetch_array($email_result_sql);

								$user_email = $email_row['customers_email_address'];

								break;
							case 'admin':
								$email_select_sql = "	SELECT admin_email_address
														FROM " . TABLE_ADMIN . "
														WHERE admin_id = '" . tep_db_input($log_row['custom_products_code_log_user']) . "'";
								$email_result_sql = tep_db_query($email_select_sql);
								$email_row = tep_db_fetch_array($email_result_sql);

								$user_email = $email_row['admin_email_address'];

								break;
							default:
								$user_email = $log_row['custom_products_code_log_user'];

								break;
						}

						echo '<b>' . $log_row["log_time"].'&nbsp;(by '.$user_email.' @ '.$log_row['log_ip'].')</b><br>';
						echo nl2br($log_row["log_system_messages"]) . '<br><br>';
					}
				}

				exit;
                break;
			case 'unzip':
				$products_vault_id = $_POST['products_vault_id'];
				$unzip_password = $_POST["unzip_password"];

				if ($unzip_vault)
					$errorStatus = tep_manage_keycode_unzip($products_vault_id,$unzip_password);

				$filterby_p = $_POST["current_p"];
				$filterby_s = $_POST["current_s"];
				$filterby_c = $_POST["current_c"];

				if ($errorStatus <= 0)
					tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s=On+Hold'));
				else
					tep_redirect(tep_href_link(FILENAME_CDKEY, 'action=search&filterby_c='.$filterby_c.'&filterby_p='.$filterby_p.'&filterby_s=Zip'));

				exit;
				break;
            case 'reupload':
                if ($upload_cd_key) {
                    $cdkey_image_select_sql = "	SELECT custom_products_code_id, code_uploaded_by, products_id, code_date_added
                                                FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                                WHERE to_s3 = 0
                                                LIMIT 15";
                    $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
                    while ($cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql)) {
                        $custom_products_code_id = $cdkey_image_row['custom_products_code_id'];
                        $product_id = $cdkey_image_row['products_id'];
                        $timestamp = $cdkey_image_row['code_date_added'];
                        $theData = $cpc_obj->getCode($custom_products_code_id, 0, $product_id, $timestamp);

                        if ($theData !== FALSE) {
                            if ($cpc_obj->uploadCode($theData, $custom_products_code_id, $product_id, $timestamp, null, true) === TRUE) {
                                $cpc_obj->cleanCode($custom_products_code_id);
                                $messageStack->add_session(sprintf(SUCCESS_MESSAGE_INSERT_CODE,'', $custom_products_code_id . '.key'), 'success');
                            } else {
                                $messageStack->add_session('[1]'.sprintf(ERROR_INSERT_CODE_FAILED, $custom_products_code_id . '.key'), 'error');
                            }
                        } else {
                            $messageStack->add_session('[2]'.sprintf(ERROR_INSERT_CODE_FAILED, $custom_products_code_id . '.key'), 'error');
                        }
                    }
                }

                tep_redirect(tep_href_link(FILENAME_CDKEY));
                break;
		}
	}

    // check local storage has any key failed to upload to AWS S3
    if ($upload_cd_key) {
        $is_exist_select_sql = "	SELECT custom_products_code_id, code_uploaded_by, products_id, code_date_added
                                    FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                    WHERE to_s3 = 0";
        $is_exist_result_sql = tep_db_query($is_exist_select_sql);
        if ($total_found = tep_db_num_rows($is_exist_result_sql)) {
            $messageStack->add('System detected total ' . $total_found .' CDKeys exist in local storage, please kindly click <a href="' . tep_href_link(FILENAME_CDKEY, 'action=reupload') . '">here</a> to perform re-upload. Each process will re-upload 15 Keys.', 'error');
        }
    }
//------System Ends
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET;?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/javascript/select2/css/select2.min.css">
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css?v=<?php echo filemtime('includes/stylesheet.css'); ?>">
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">

	<script language="javascript" src="includes/javascript/jquery/1.11.0/jquery.min.js"></script>
	<script language="javascript" src="includes/javascript/jquery/migrate/jquery-migrate-1.4.1.min.js"></script>
	<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
	<script language="javascript" src="includes/javascript/custom_product_xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/select2/js/select2.min.js"></script>
	
	<script>

    function submit_form(obj) {
        obj.value = "Please wait...";
        obj.disabled = true;
        var form = obj.form
        if(check_po_form()){
            form.submit();
        }
        else{
            obj.value = "Upload";
            obj.disabled = false;
        }
    }

	jQuery.noConflict();

	function check_form()
	{
		var error = 0;
		var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

		if (document.upload_form.filterby_p_up.value == "") {
			error_message = error_message + "* Please select product to upload CDK to.\n";
			error = 1;
		}

		if (document.upload_form.upload_file.value == "") {
			error_message = error_message + "* The 'File name' entry must be entered.\n";
			error = 1;
		}

		var upload_file = document.upload_form.upload_file.value;
		var upload_file_arr = upload_file.split('.');
		var upload_file_type = upload_file_arr.pop();
		upload_file_type = upload_file_type.toLowerCase();

        if (upload_file_type != "jpg" && upload_file_type != "png" && upload_file_type != "gif" && upload_file_type != "zip") {
            error_message = error_message + "* The system only supports JPG, GIF, PNG and ZIP file.\n";
			error = 1;
		}

        if ((upload_file_type == "jpg" || upload_file_type == "png" || upload_file_type == "gif") && document.upload_form.upload_to[2].checked == false) {
            error_message = error_message + "* To upload a JPG/GIF/PNG file, please select \"JPG/GIF/PNG File\" in Zip File Option.\n";
            error = 1;
        }

		if (upload_file_type == "zip" && document.upload_form.upload_to[2].checked == true) {
			error_message = error_message + "* To upload a ZIP file, please select \"Password Protected\" or \"NOT Password protected\" in Zip File Option.\n";
			error = 1;
		}

        if ((upload_file_type != "jpg" && upload_file_type != "png" && upload_file_type != "gif")) {
			if (document.upload_form.zip_qty.value == "") {
				error_message = error_message + "* The 'Quantity' entry must be entered.\n";
				error = 1;
			}

			if (validateInteger(document.upload_form.zip_qty.value) == false) {
				error_message = error_message + "* The 'Quantity' value is invalid.\n";
				error = 1;
			}

			if (document.upload_form.zip_unit_price.value == "") {
				error_message = error_message + "* The 'Unit Price' entry must be entered.\n";
				error = 1;
			}

			if (validateMayHvPtDecimal(document.upload_form.zip_unit_price.value) == false) {
				error_message = error_message + "* The 'Unit Price' value is invalid.\n";
				error = 1;
			}
		}

		if (error == 1) {
			alert(error_message);
			return false;
		}
		else {
			return true;
		}
	}

	function check_po_form()
	{
		var error = 0;
		var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

		if (document.upload_po_form.filterby_p_up.value == "") {
			error_message = error_message + "* Please select product to upload CDK to.\n";
			error = 1;
		}

		if (document.upload_po_form.upload_file.value == "") {
			error_message = error_message + "* The 'File name' entry must be entered.\n";
			error = 1;
		}

		var upload_file = document.upload_po_form.upload_file.value;
		var upload_file_arr = upload_file.split('.');
		var upload_file_type = upload_file_arr.pop();
		upload_file_type = upload_file_type.toLowerCase();

        if ((upload_file_type != "jpg" && upload_file_type != "png" && upload_file_type != "gif") && upload_file_type != "zip") {
            error_message = error_message + "* The system only supports JPG, PNG, GIF and ZIP file.\n";
			error = 1;
		}

        if ((upload_file_type == "jpg" || upload_file_type == "png" || upload_file_type == "gif") && document.upload_po_form.po_upload_to[2].checked == false) {
            error_message = error_message + "* To upload a JPG/PNG/GIF file, please select \"JPG/GIF/PNG File\" in Zip File Option.\n";
			error_message = error_message + "* To upload a JPG file, please select \"JPG File\" in Zip File Option.\n";
			error = 1;
		}

		if (upload_file_type == "zip" && document.upload_po_form.po_upload_to[2].checked == true) {
			error_message = error_message + "* To upload a ZIP file, please select \"Password Protected\" or \"NOT Password protected\" in Zip File Option.\n";
			error = 1;
		}

        if ((upload_file_type != "jpg" && upload_file_type != "png" && upload_file_type != "gif")) {
			if (document.upload_po_form.zip_qty.value == "") {
				error_message = error_message + "* The 'Quantity' entry must be entered.\n";
				error = 1;
			}

			if (validateInteger(document.upload_po_form.zip_qty.value) == false) {
				error_message = error_message + "* The 'Quantity' value is invalid.\n";
				error = 1;
			}

			if (document.upload_po_form.po_number.value == "") {
				error_message = error_message + "* The 'PO Number' entry must be entered.\n";
				error = 1;
			}
		}

		if (error == 1) {
			alert(error_message);
			return false;
		}
		else {
			return true;
		}
	}

<?php
if ($update_status_actual || $update_status_disabled || $update_status_on_hold || $update_status_bulk_remarks || $update_status_delete || $update_vault_status){
?>
	function doBatchUpdate()
	{
		var numRows = document.maingrid.gnumRows.value;

		//Ignore if no checked boxes
		var cnt = 0;

		for (i=0; i < numRows; i++)
		{
			if (document.getElementById("gselection[" + i + "]").checked)
				cnt++;
		}
		if (!cnt) {
			document.maingrid.cb_status.options[0].selected = true;
			return false
		}

		if (isNaN(document.maingrid.cb_status.value)) {
			//doing action, ie. delete
			if ((document.maingrid.cb_status.value=='remarks'))
			{
				//Get remarks
				var remarks = window.prompt('<?php echo TABLE_HEADING_REMARKS;?>', '');
				if (remarks==null || remarks=="") {
					//User clicked Cancel ?
					document.maingrid.cb_status.options[0].selected = true;
					return false;
				}
				else {
					remarks = "<b>Remarks :</b> " + remarks;
					document.maingrid.gremarks.value = remarks;
					document.maingrid.gselection_m.checked = false;
					document.maingrid.submit();
				}
			}
			else if ((document.maingrid.cb_status.value=='delete_zip') && confirmSubmit())
			{
				document.maingrid.submit();
			}
			else if ((document.maingrid.cb_status.value=='delete') && confirmSubmit())
			{
				document.maingrid.submit();
			} else {
				return false;
			}
		} else {
			//Changing status number
			//Block sold status
			if (document.maingrid.cb_status.value == 0)
			{
				alert('<?php echo ERROR_PERMISSION_DENIED; ?>');
				document.maingrid.cb_status.options[0].selected = true;
				return false;
			}
			//Get remarks
			var remarks = window.prompt('<?php echo TABLE_HEADING_REMARKS;?>', '');
			if (remarks==null) {
				//User clicked Cancel ?
				document.maingrid.cb_status.options[0].selected = true;
				return false;
			}
			//Remarks required for Disabling
			if ( (document.maingrid.cb_status.value > 0) || ((document.maingrid.cb_status.value < 0)&&(remarks!="")) ) {
				document.maingrid.gremarks.value = remarks;
				document.maingrid.gselection_m.checked = false;
				document.maingrid.submit();
			} else {
				if (document.maingrid.cb_status.value == "-1")
					alert('<?php echo ERROR_DISABLE_FAILED_REMARKS_CHECK; ?>');
				else
					alert('<?php echo ERROR_ON_HOLD_FAILED_REMARKS_CHECK; ?>');

				document.maingrid.cb_status.options[0].selected = true;
				return false;
			}
		}
	}

	function confirmSubmit()
	{
		var agree=confirm("Are you sure you wish to continue?");
		if (agree)
		{
			return true ;
		} else {
			return false ;
		}
	}
<?php
}
?>

	function toggleCheckBox()
	{
		var numRows = document.maingrid.gnumRows.value;
		if (document.maingrid.gselection_m.checked) {
			var cb_state = true;
		} else {
			var cb_state = false;
		}
		for (i=0; i<numRows; i++)
		{
			document.getElementById("gselection[" + i + "]").checked = cb_state;
		}

		count_checkbox();
	}

	function cb_upd_gselection_m(cb_checked)
	{
		if ((!cb_checked) && (document.maingrid.gselection_m.checked))
			document.maingrid.gselection_m.checked = false;

		count_checkbox();
	}

	function count_checkbox()
	{
		var numRows = document.maingrid.gnumRows.value;
		var checkbox_count = 0;
		for (i=0; i<numRows; i++)
		{
			if (document.getElementById("gselection[" + i + "]").checked == true)
				checkbox_count++;
		}

		if (checkbox_count == 1)
			jQuery("#select_count").html(checkbox_count+" entry");
		else if (checkbox_count > 1)
			jQuery("#select_count").html(checkbox_count+" entries");
		else
			jQuery("#select_count").html("0 entry");
	}

	function ActionSearch(status,tab)
	{
		var select_product_category = jQuery("#select_product_category").val();
		var select_product = jQuery("#select_product").val();

		if (status)
			var filter_status = status;
		else
			var filter_status = jQuery("#filter_status").val();

		var filterby_order_id = jQuery("#filterby_order_id").val();
		var filterby_cdk_id = jQuery("#filterby_cdk_id").val();
		var filterby_po_ref = jQuery("#filterby_po_ref").val();

		if (filterby_order_id != "" || filterby_cdk_id !="" || filterby_po_ref !="")
			var extraquery = "&filterby_order_id="+filterby_order_id+"&filterby_cdk_id="+filterby_cdk_id+"&filterby_po_ref="+escape(filterby_po_ref)+"#tab-cdk";
		else
			var extraquery = "#tab-category";

		document.location.href = '<?php echo FILENAME_CDKEY; ?>?action=search&filterby_c='+select_product_category+'&filterby_p='+select_product+'&r=<?php echo (int) rand ( 0, 99 ); ?>&filterby_s='+filter_status + extraquery;
	}

	function getCDKeyImg_list(showflag)
	{
		var numRows = document.maingrid.gnumRows.value;
		for (i=0; i < numRows; i++)
		{
			CDK_id = document.getElementById("gselection[" + i + "]").value;

			if (showflag)
				Show_CDK_Image(CDK_id, true);
			else
				Hide_CDK_Image(CDK_id, true);
		}

		if (showflag)
			jQuery('#show_all_cdk').html('<a href="javascript:getCDKeyImg_list(\'\')">Hide All CD Key</a>');
		else
			jQuery('#show_all_cdk').html('<a href="javascript:getCDKeyImg_list(\'1\')">View All My Uploaded CD Key</a>');
	}

	function Show_CDK_Image(CDK_id, owner_only)
	{
        var user_msg = '';

        jQuery('#Show_CDK_image_'+CDK_id).hide();

		if (jQuery('#CDK_image_'+CDK_id).html() == "<img src=images/loading.gif>" || jQuery('#CDK_image_'+CDK_id).html() == "") {
			jQuery('#CDK_image_'+CDK_id).show().html("<img src=images/loading.gif>");
			jQuery.post("?action=showcdk", { cdk_id: CDK_id }, function(data){
				if (data.result != 2) {
                    jQuery('#CDK_image_'+CDK_id).html(data.value);
                } else {
                    if (owner_only) {
                        jQuery('#CDK_image_'+CDK_id).html('');
                        jQuery('#Show_CDK_image_'+CDK_id).show();
                    } else {
                        user_msg = window.prompt("<?="This CD Key is not uploaded by you, please enter your reason to view it:" ?>", '');

                        if (user_msg != null && user_msg != '') {
                            jQuery.post("?action=showcdk", { cdk_id: CDK_id, remark: user_msg }, function(data){
                                if (data.result != 2) {
                                    jQuery('#CDK_image_'+CDK_id).html(data.value);
                                } else {
                                    jQuery('#CDK_image_'+CDK_id).html('');
                                    jQuery('#Show_CDK_image_'+CDK_id).show();
                                }
                            }, "json");

                        } else {
                            jQuery('#CDK_image_'+CDK_id).html('');
                            jQuery('#Show_CDK_image_'+CDK_id).show();
                        }
                    }
                }
			}, "json");
		} else {
            jQuery('#CDK_image_'+CDK_id).show();
        }
	}

	function Hide_CDK_Image (CDK_id)
	{
		jQuery('#Show_CDK_image_'+CDK_id).show();
		jQuery('#CDK_image_'+CDK_id).hide();
	}
<?php

if ($unzip_vault) {
?>
	function unzip_vault (products_vault_id)
	{
		document.unzip.unzip_password.value = jQuery('#zip_password_'+products_vault_id).val();
		document.unzip.products_vault_id.value = products_vault_id;

		if (document.unzip.unzip_password.value) {
			document.unzip.submit();
		}
		else {
			alert("You forgot to fill in the password to Unzip this file.");
			document.maingrid.elements["zip_password_"+products_vault_id].focus();
		}
	}
<?php
}
if ($add_remarks) {
?>
	function addRemarks ()
	{
		var remarks_custom_product_id = jQuery('#remarks_custom_product_id').val();
		var remarks = jQuery('#remarks').val();

		if (remarks) {
			remarks = "<b>Remarks :</b> " + remarks;
			jQuery('#remarks_ajax_loading').html("<img src=images/ajax_loading.gif>");

			jQuery.get("?action=add_remarks&custom_product_id="+remarks_custom_product_id+"&remarks="+URLEncode(remarks), function(data){
				if (data)
					jQuery('#remarks_main').append(data);
					jQuery('#remarks_ajax_loading').html("");
					jQuery('#remarks').val("");
			});
		}
	}

<?php
}
?>
	function handleEnter (field, event) {
		var keyCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
		if (keyCode == 13)
			return false;
		else
			return true;
	}

	function change_category_select_box (type) {
		if (type == "1") {
			var category = jQuery("#select_product_category").val();
		} else {
			var category = jQuery("#select_product_category_up").val();
		}

		if (category == "") {
			myOptions = {
				"" : "---All Product---"
			}
		} else {
			var myOptions = { "" : "---All Product---" };
			jQuery.ajax({
				type: 'POST',
				url:'categories_xmlhttp.php',
				data:'action=cat_get_products&cat_id='+category,
				success: function(xml){
					jQuery("#select_product").removeOption(/./);
					jQuery("#select_product_up").removeOption(/./);
					jQuery(xml).find("product").each(function(){
						var hold_id = jQuery(this).attr("id");
						var hold_name = jQuery(this).find("products_name").text();
						var hold_display_qty = '';
						if (jQuery(this).find("products_quantity").text()!='') {
							hold_display_qty += ' Available: ' + jQuery(this).find("products_quantity").text();
						}
						if (jQuery(this).find("products_actual_quantity").text()!='') {
							hold_display_qty += ' Actual: ' + jQuery(this).find("products_actual_quantity").text();
						}
						if (hold_display_qty!='') {
							hold_display_qty = " ("+hold_display_qty+")";
						}
						hold_name = hold_name + hold_display_qty;
						jQuery("#select_product").addOption(hold_id, hold_name);
						jQuery("#select_product_up").addOption(hold_id, hold_name);
					});
					jQuery("#select_product").val('<?=$filterby_p?>');
					jQuery("select[name=filterby_p_up]").val('<?=$filterby_p?>');
				}
			});
		}
		if (type == "1") {
			jQuery("#select_product").removeOption(/./).addOption(myOptions, false);
			jQuery("#select_product_up").removeOption(/./).addOption(myOptions, false);
		} else {
			jQuery("#select_product_up").removeOption(/./).addOption(myOptions, false);
		}

		/*

<?		foreach ($product_obj['categoryid'] as $categoryid) { ?>
			else if (category == "<?php echo $categoryid; ?>") {
				myOptions = {
					"" : "---All Product---",
			<?php
				foreach ($product_obj["$categoryid"] as $row) {
			?>
					"<?php echo $row['id']; ?>" : "<?php echo addslashes($row['text']); ?>",
			<?php
				}
			?>
				}
			}
		<?php
		}
		?>
			else {
				myOptions = {
					"" : "---All Product---",
				}
			}

			*/
	}

	function change_po_category_select_box (type) {
		if (type == "1") {
			var category = jQuery("#select_product_category").val();
		} else {
			var category = jQuery("#select_po_product_category_up").val();
		}

		if (category == "") {
			myOptions = {
				"" : "---All Product---"
			}
		} else {
			var myOptions = { "" : "---All Product---" };
			jQuery.ajax({
				type: 'POST',
				url:'categories_xmlhttp.php',
				data:'action=cat_get_products&cat_id='+category,
				success: function(xml){
					jQuery("#select_product").removeOption(/./);
					jQuery("#select_po_product_up").removeOption(/./);
					jQuery(xml).find("product").each(function(){
						var hold_id = jQuery(this).attr("id");
						var hold_name = jQuery(this).find("products_name").text();
						var hold_display_qty = '';
						if (jQuery(this).find("products_quantity").text()!='') {
							hold_display_qty += ' Available: ' + jQuery(this).find("products_quantity").text();
						}
						if (jQuery(this).find("products_actual_quantity").text()!='') {
							hold_display_qty += ' Actual: ' + jQuery(this).find("products_actual_quantity").text();
						}
						if (hold_display_qty!='') {
							hold_display_qty = " ("+hold_display_qty+")";
						}
						hold_name = hold_name + hold_display_qty;
						jQuery("#select_product").addOption(hold_id, hold_name);
						jQuery("#select_po_product_up").addOption(hold_id, hold_name);
					});
					jQuery("#select_product").val('<?=$filterby_p?>');
					jQuery("select[name=filterby_p_up]").val('<?=$filterby_p?>');
				}
			});
		}
		if (type == "1") {
			jQuery("#select_product").removeOption(/./).addOption(myOptions, false);
			jQuery("#select_po_product_up").removeOption(/./).addOption(myOptions, false);
		} else {
			jQuery("#select_po_product_up").removeOption(/./).addOption(myOptions, false);
		}
	}

	jQuery(document).ready(function() {
		initInfoCaptions();

		jQuery('#filter_status').attr("disabled", true);
		jQuery("#search-tab > ul").tabs();
		jQuery("#select_product_category").select2();
		jQuery("#select_product").select2();
		jQuery("#select_po_product_category_up").select2();
		jQuery("#select_po_product_up").select2();

		change_category_select_box();
		change_po_category_select_box();
		var tempproductvalue = jQuery("#select_product_category").val();

		jQuery('#select_product').on("select2:select", function (data) {
			if (jQuery("#select_product").val() != "") {
				jQuery('#filter_status').attr("disabled", false);
			}
			else {
				jQuery('#filter_status').attr("disabled", true);
			}

<?php
if ($upload_cd_key) {
?>
			document.upload_form.filterby_p_up.selectedIndex = document.goto.filterby_p.selectedIndex;
			document.upload_po_form.filterby_p_up.selectedIndex = document.goto.filterby_p.selectedIndex;
<?php
}
?>
		});

		jQuery('#select_product_category').on("select2:select", function (data) {
			jQuery('#filter_status').attr("disabled", true);
			change_category_select_box ('1');
			change_po_category_select_box ('1');
<?php
if ($upload_cd_key) {
?>
			document.upload_form.filterby_c_up.selectedIndex = document.goto.filterby_c.selectedIndex;
			document.upload_po_form.filterby_c_up.selectedIndex = document.goto.filterby_c.selectedIndex;
<?php
}
?>
		});

		jQuery("#select_product_category_up").change(function (data) {
			change_category_select_box ('0');
		});

		jQuery("#select_po_product_category_up").change(function (data) {
			change_po_category_select_box ('0');
		});

		change_category_select_box ('1');
		change_po_category_select_box ('1');

	<?php
		if ($filterby_p){
			echo '	jQuery("#select_product").val('.$filterby_p.');';
			echo '	jQuery("#select_product").selectOptions("'.$filterby_p.'", true);';
			echo '	jQuery("#filter_status").attr("disabled", false);';
			echo '	jQuery("#select_product_up").selectOptions("'.$filterby_p.'", true);';
			echo '	jQuery("#select_po_product_up").selectOptions("'.$filterby_p.'", true);';
		}
	?>

	});

	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	<!-- header //-->
	<? 	require(DIR_WS_INCLUDES . 'header.php'); ?>
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="2" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
				</table>
			</td>
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td colspan="2" class="pageHeading" valign="top">CD Key Codes</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
						<div ID="div_search">
							<!-- This is search form -->
							<?php
							  echo tep_draw_form('goto', FILENAME_CDKEY, '' , 'get');
							?>
					        <div id="search-tab">
					            <ul>
					                <li><a href="#tab-category" onclick="javascript:document.goto.filterby_order_id.value=''; document.goto.filterby_cdk_id.value=''"><span>Search by Category</span></a></li>
						<?php
							if ($search_by_id)
								echo '<li>';
							else
								echo '<li class="ui-tabs-disabled">';
						?>
										<a href="#tab-cdk" onclick="javscript:document.goto.filterby_c.selectedIndex='0'; document.upload_form.filterby_c_up.selectedIndex='0'; document.upload_po_form.filterby_c_up.selectedIndex='0'; jQuery('#filter_status').attr('disabled', true); change_category_select_box ('1'); change_po_category_select_box ('1');"><span>Search by Order/CD Key</span></a>
									</li>

						<?php
//							if ($upload_cd_key)
//								echo '<li>';
//							else
//								echo '<li class="ui-tabs-disabled">';
						?>
<!--										<a href="#tab-upload"><span><img src=images/icons/file_upload.gif align=absmiddle border=0> Upload Form</span></a>
									</li>-->

						<?php
							if ($upload_cd_key)
								echo '<li>';
							else
								echo '<li class="ui-tabs-disabled">';
						?>
										<a href="#tab-po-upload"><span><img src=images/icons/file_upload.gif align=absmiddle border=0> Upload via PO</span></a>
									</li>
					            </ul>
					            <div id="tab-category" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
									<table border="0" cellspacing="0" cellpadding="2">
									  	<tr>
									      	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_PRODUCT_CATEGORY; ?></td>
										  	<td class="smallText" valign="top">
					<?php
								if (count($filterby_c_opt_arr) > 1) echo tep_draw_pull_down_menu('filterby_c', $filterby_c_opt_arr, $filterby_c , ' id="select_product_category"');
					?>
											</td>
									  	</tr>
									  	<tr>
									      	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_SEARCH_PRODUCT; ?></td>
										  	<td class="smallText" valign="top">
					<?php
								if (count($filterby_c_opt_arr) > 1) echo tep_draw_pull_down_menu('filterby_p', $filterby_p_opt_arr, $filterby_p , ' id="select_product"');
					?>
											</td>
									  	</tr>
									  	<tr>
										  	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_FILTER_STATUS; ?></td>
										  	<td class="smallText" valign="top"><?php echo tep_draw_pull_down_menu('filterby_s', $filterby_s_opt_arr, ($filterby_s) ? $filterby_s : '', ' id="filter_status"'); ?></td>
									  	</tr>
									  	<tr>
										  	<td class="smallText" valign="top" align="left">
											  <?php echo tep_button(' Search ', ' Search ', '', 'onClick="javascript:ActionSearch(\'\',\'0\');" onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true); ?>
											<td class="smallText"><span id="AJAXLoadTop"></span></td>
									  	</tr>
									</table>
					            </div>
					            <div id="tab-cdk" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
									<table border="0" cellspacing="0" cellpadding="2">
									  	<tr>
									      	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_ORDER_ID; ?></td>
										  	<td class="smallText" valign="top">
					<?php
								echo tep_draw_input_field('filterby_order_id','', ' size="20" id="filterby_order_id" maxlength="255" onFocus="javascript:document.goto.filterby_cdk_id.value=\'\';document.goto.filterby_po_ref.value=\'\';"', false);
					?>
											</td>
									  	</tr>
									  	<tr>
									      	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_CDK_ID; ?></td>
										  	<td class="smallText" valign="top">
					<?php
								echo tep_draw_input_field('filterby_cdk_id','', ' size="20" id="filterby_cdk_id" maxlength="255" onFocus="javascript:document.goto.filterby_order_id.value=\'\';document.goto.filterby_po_ref.value=\'\';"', false);
					?>
											</td>
									  	</tr>
									  	<tr>
									      	<td class="smallText" valign="top"><?php echo LABEL_LIST_BY_PO_REF_NUM; ?></td>
										  	<td class="smallText" valign="top">
					<?php
								echo tep_draw_input_field('filterby_po_ref','', ' size="20" id="filterby_po_ref" maxlength="255" onFocus="javascript:document.goto.filterby_order_id.value=\'\';document.goto.filterby_cdk_id.value=\'\';"', false);
					?>
											</td>
									  	</tr>
									  	<tr>
										  	<td class="smallText" valign="top" align="left"><?php echo tep_button(' Search ', ' Search ', '', 'onClick="javascript:ActionSearch(\'\',\'0\');" onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true); ?></td>
									  	</tr>
									</table>
					            </div>
								</form>
<!--					            <div id="tab-upload" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">-->
<?php
	if ($upload_cd_key && FALSE) {  // temp hide this div Remove after 2013-03-01
?>
							<div ID="div_upload" style="border-width: 2px; border-style: dotted; background-color: #FFFFCC; padding: 10px;">
								<!-- This is upload form -->
								<?php //echo tep_buildUploadForm(); ?>
							</div>
<?php
	}
?>
<!--					            </div>-->
					            <div id="tab-po-upload" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
<?php
	if ($upload_cd_key) {
?>
							<div ID="div_po_upload" style="border-width: 2px; border-style: dotted; background-color: #FFFFCC; padding: 10px;">
								<!-- This is upload form -->
								<?php echo tep_buildPOUploadForm(); ?>
							</div>
<?php
	}
?>
					            </div>
					        </div>
						</div>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td valign="top">
										<div id="HTMLGrid"><?php echo $HTMLgrid; ?></div>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	<?php echo tep_draw_form('unzip', FILENAME_CDKEY, 'action=unzip', 'post').tep_draw_hidden_field('products_vault_id', '').tep_draw_hidden_field('unzip_password', '').tep_draw_hidden_field('current_c', $filterby_c).tep_draw_hidden_field('current_p', $filterby_p).tep_draw_hidden_field('current_s', 'Zip').(tep_not_null($uploadfor_po)? tep_draw_hidden_field('uploadfor_po', $uploadfor_po) : '').(tep_not_null($po_id)? tep_draw_hidden_field('po_id', $po_id) : '').'</form>'; ?>
<?
require(DIR_WS_INCLUDES . 'footer.php');
?>
<!-- footer_eof //-->
<br/>
</body>
</html>
<? 	require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
