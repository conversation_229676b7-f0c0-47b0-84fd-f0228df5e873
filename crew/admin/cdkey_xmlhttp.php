<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS. 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'custom_product_code.php');

tep_set_time_limit(300);
tep_db_connect() or die('Unable to connect to database server!');

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$user_ip_in_binary = tep_ip_in_binary_form($_SERVER['REMOTE_ADDR']);
$allow_ip = array( 	array('ip' => '127.0.0.1', 'subnet' => 32),
					array('ip' => '*************', 'subnet' => 32), // Staging API Server
					array('ip' => '*************', 'subnet' => 32)  // Live API Server
				);

if (!empty($action)) {
    switch($action) {
        case 'get_cdkey': 
            $theData = '';
            $custom_products_code_id = isset($_POST['cp_code_id']) ? $_POST['cp_code_id'] : '';
            $customer_id = isset($_POST['customer_id']) ? $_POST['customer_id'] : '';
            if (strpos($_SERVER['HTTP_REFERER'], 'order/get_cdkey') === 0) {
                if (!empty($custom_products_code_id)) {
                    $customer_order_verify_select_sql = "	SELECT o.customers_id 
                                                            FROM " . TABLE_ORDERS . " AS o 
                                                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                ON o.orders_id=op.orders_id
                                                            INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " as cpc
                                                                ON cpc.orders_products_id = op.orders_products_id
                                                            WHERE o.customers_id = '" . $customer_id . "' 
                                                                AND o.orders_status IN (2, 3) 
                                                                AND cpc.custom_products_code_id = '" . tep_db_input($custom_products_code_id) . "'";
                    $customer_order_verify_result_sql = tep_db_query($customer_order_verify_select_sql);
                    if (tep_db_num_rows($customer_order_verify_result_sql) > 0) {
                        foreach ($allow_ip as $ip_info) {
                            $current_ip_in_binary = tep_ip_in_binary_form($ip_info['ip']);
                            $test_user_ip_str = substr($user_ip_in_binary, 0, $ip_info['subnet']);

                            if (strpos($current_ip_in_binary, $test_user_ip_str) === 0) {
                                $cpc_obj = new custom_product_code();
                                $theData = $cpc_obj->getCode($custom_products_code_id);
                                
                                if ($theData !== FALSE) {
                                    echo $theData;
                                } else {
                                    echo 'ERROR_NO_CDKEY';
                                }
                                
                                unset($cpc_obj, $theData);
                            }
                        }
                    } else {
                        echo 'ERROR_NO_CDKEY';
                    }
                } 
            } else {
                echo 'ERROR_NO_CDKEY';
            }
    }
}
?>
