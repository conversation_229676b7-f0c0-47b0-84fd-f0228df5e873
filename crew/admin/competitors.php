<?
require_once("includes/application_top.php");

$action = $_REQUEST["action"];
$subaction = $_REQUEST["subaction"];

$form_sessname = basename($_SERVER['PHP_SELF']);

$form_values_arr = $_SESSION[$form_sessname];

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case 'filter_competitors':
			if (isset($_POST['game_cat_id'])) {
				if (!(int)$_POST['game_cat_id'] && isset($_SESSION[$form_sessname]['game_cat_id'])) {
					unset($_SESSION[$form_sessname]['game_cat_id']);
				} else {
					$_SESSION[$form_sessname]['game_cat_id'] = tep_db_prepare_input((int)$_POST['game_cat_id']);
				}
			}
			tep_redirect(tep_href_link(FILENAME_COMPETITORS));

			break;

		case 'assign_competitor':
			$competitors_id = tep_db_prepare_input($_POST['competitors_id']);
			$competitors_select_sql = "select c.competitors_name
										from ".TABLE_COMPETITORS." AS c
										where
										c.competitors_id = '$competitors_id'";
			$competitors_result_sql = tep_db_query($competitors_select_sql);
			if ($competitors_row = tep_db_fetch_array($competitors_result_sql)) {
				$competitor_name = $competitors_row['competitors_name'];
				$new_competitor_category_array = array(
													'competitors_id' => $competitors_id,
													'categories_id' => $form_values_arr['game_cat_id'],
													'competitors_weight' => tep_db_prepare_input($HTTP_POST_VARS['competitor_weight'])
													);
	    		tep_db_perform(TABLE_COMPETITORS_TO_CATEGORIES, $new_competitor_category_array);
				$messageStack->add_session('Competitor: ' . $competitor_name . ' has been successfully assigned!', 'success');
			}
			if (!$error) {
				tep_redirect(tep_href_link(FILENAME_COMPETITORS, 'page=' . $HTTP_GET_VARS['page']));
			}

			break;

		case "insert_competitor":
        	$competitor_name = tep_db_prepare_input($HTTP_POST_VARS['competitor_name']);
        	$name_replace = ereg_replace_dep(" ", "", strtolower($competitor_name));

        	if (($competitor_name == '' || NULL) || (strlen($competitor_name) <= 1) ) {
          		$messageStack->add(TEXT_INFO_COMPETITOR_NAME_FALSE);
				$error = true;
        	} else {
        		$check_competitor_name_query = tep_db_query("select competitors_name from " . TABLE_COMPETITORS . " where LOWER(REPLACE(competitors_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
          		$check_duplicate = tep_db_num_rows($check_competitor_name_query);
          		if ($check_duplicate > 0){
          			$messageStack->add(TEXT_INFO_COMPETITOR_NAME_USED);
					$error = true;
          		} else {
            		$new_competitor_array = array('competitors_name' => $competitor_name);
            		$success = tep_db_perform(TABLE_COMPETITORS, $new_competitor_array);
            		if ($success) {
						$competitors_id = tep_db_insert_id();
						$new_competitor_category_array = array(
															'competitors_id' => $competitors_id,
															'categories_id' => $form_values_arr['game_cat_id'],
															'competitors_weight' => tep_db_prepare_input($HTTP_POST_VARS['competitor_weight'])
															);
	            		tep_db_perform(TABLE_COMPETITORS_TO_CATEGORIES, $new_competitor_category_array);
						$messageStack->add_session('Competitor: ' . $competitor_name . ' has been successfully created!', 'success');
            		} else {
            			$error = true;
						$messageStack->add_session('Competitor: ' . $competitor_name . ' could not be created.');
            		}
          		}
        	}
			if (!$error) {
				tep_redirect(tep_href_link(FILENAME_COMPETITORS, 'page=' . $HTTP_GET_VARS['page']));
			}

			break;

		case "update_competitor":
			$error = false;

			$competitor_name = tep_db_prepare_input($HTTP_POST_VARS['competitor_name']);
        	$name_replace = ereg_replace_dep(" ", "", strtolower($competitor_name));

        	if (($competitor_name == '' || NULL) || (strlen($competitor_name) <= 1) ) {
          		$messageStack->add(TEXT_INFO_COMPETITOR_NAME_FALSE);
				$error = true;
        	} else {
        		$check_competitor_name_query = tep_db_query("select competitors_name from " . TABLE_COMPETITORS . " where competitors_id <> " . (int)$HTTP_POST_VARS['cpttID'] . " and LOWER(REPLACE(competitors_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
          		$check_duplicate = tep_db_num_rows($check_competitor_name_query);
        		if ($check_duplicate > 0){
            		$messageStack->add(TEXT_INFO_COMPETITOR_NAME_USED);
					$error = true;
          		} else {
          			$sql_data_array = array('competitors_name' => $competitor_name
            								);
            		tep_db_perform(TABLE_COMPETITORS, $sql_data_array, 'update', "competitors_id = '" . (int)$HTTP_POST_VARS['cpttID'] . "'");

          			$messageStack->add_session('Competitor: ' . $competitor_name . ' has been successfully updated!', 'success');
          		}
        	}

			if (isset($HTTP_POST_VARS['competitor_weight'])) {
				$competitor_weight = tep_db_prepare_input($HTTP_POST_VARS['competitor_weight']);

	        	if (($competitor_weight == '' || NULL) || (strlen($competitor_weight) <= 1) ) {
	          		$messageStack->add(TEXT_INFO_COMPETITOR_WEIGHT_FALSE);
					$error = true;
	        	} else {
          			$sql_data_array = array('competitors_weight' => $competitor_weight);
            		tep_db_perform(TABLE_COMPETITORS_TO_CATEGORIES, $sql_data_array, 'update', "competitors_id = '" . (int)$HTTP_POST_VARS['cpttID'] . "' AND categories_id = '{$form_values_arr['game_cat_id']}'");
          			$messageStack->add_session('Competitor weight: ' . $competitor_name . ' has been successfully updated!', 'success');
	        	}
			}

			if (!$error) {
				tep_redirect(tep_href_link(FILENAME_COMPETITORS, 'page=' . $HTTP_GET_VARS['page']));
			}
			break;
		case "confirm_delete_competitor":

		   	$c2c_delete_sql = "DELETE FROM " . TABLE_COMPETITORS_TO_CATEGORIES . " WHERE competitors_id = '" . (int)$_REQUEST['cpttID'] . "' AND categories_id = '{$form_values_arr['game_cat_id']}'";
		   	tep_db_query($c2c_delete_sql);

			$messageStack->add_session('Competitor has been successfully unassigned!', 'success');
			tep_redirect(tep_href_link(FILENAME_COMPETITORS, 'page=' . $HTTP_GET_VARS['page']));

			break;
	}
}

$form_values_arr = $_SESSION[$form_sessname];

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>

</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
if ($action == "new_competitor") {
	$category_select_sql = "select cd.categories_heading_title
							from ".TABLE_CATEGORIES_DESCRIPTION." AS cd
							where cd.categories_id = '{$form_values_arr['game_cat_id']}'";

	$category_result_sql = tep_db_query($category_select_sql);
	$category_row = tep_db_fetch_array($category_result_sql);
	echo tep_draw_form('competitor_form', FILENAME_COMPETITORS, tep_get_all_get_params(array('subaction')) . 'subaction=insert_competitor', 'post', 'onSubmit="return competitor_form_checking();"');
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="30%" valign="top"><?=TABLE_HEADING_MAIN_CATEGORY?></td>
									<td class="main" valign="top"><b><?=$category_row["categories_heading_title"]?></b></td>
								</tr>
        						<tr>
									<td class="main" width="30%" valign="top"><?=TABLE_HEADING_COMPETITOR_WEIGHT?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('competitor_weight', '', ' id="competitor_weight"')?></td>
								</tr>
        						<tr>
									<td class="main" width="30%" valign="top"><?=ENTRY_COMPETITOR_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('competitor_name', '', ' id="competitor_name"')?></td>
								</tr>
								<tr>
									<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
									<td align="left">
										<?=tep_submit_button(IMAGE_INSERT, IMAGE_INSERT, 'name="submitBtn"') . '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_COMPETITORS))?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
        		<script>
					<!--
					function competitor_form_checking() {
						var competitor_name = DOMCall('competitor_name');
						if (competitor_name.value == "") {
							alert('Please enter competitor name!');
							competitor_name.focus();
							return false;
						}
						var competitor_weight = DOMCall('competitor_weight');
						if (competitor_weight.value == "") {
							alert('Please enter competitor weight!');
							competitor_weight.focus();
							return false;
						}
						document.competitor_form.submitBtn.disabled = true;
						document.competitor_form.submitBtn.className = 'disabledSubmitBtn';
						document.competitor_form.submitBtn.value = 'Please wait...';

						return true;
					}
					//-->
				</script>
<?
} else if ($action == "assign_competitor") {
	$category_select_sql = "select cd.categories_heading_title
							from ".TABLE_CATEGORIES_DESCRIPTION." AS cd
							where cd.categories_id = '{$form_values_arr['game_cat_id']}'";

	$category_result_sql = tep_db_query($category_select_sql);
	$category_row = tep_db_fetch_array($category_result_sql);

	$competitors_arr = array(array('id' => '0', 'text' => PULL_DOWN_DEFAULT));
	$competitors_select_sql = "	SELECT c.competitors_id, c.competitors_name 
								FROM " . TABLE_COMPETITORS . " AS c 
								LEFT JOIN " . TABLE_COMPETITORS_TO_CATEGORIES  . " AS c2c 
									ON (c.competitors_id=c2c.competitors_id AND c2c.categories_id = '" . tep_db_input($form_values_arr['game_cat_id']) . "') 
								WHERE c2c.competitors_id IS NULL ";
	$competitors_result_sql = tep_db_query($competitors_select_sql);
	while ($competitors_row = tep_db_fetch_array($competitors_result_sql)) {
		$competitors_arr[] = array('id' => $competitors_row['competitors_id'], 'text' => $competitors_row['competitors_name']);
	}

	echo tep_draw_form('competitor_form', FILENAME_COMPETITORS, tep_get_all_get_params(array('subaction')) . 'subaction=assign_competitor', 'post', 'onSubmit="return competitor_form_checking();"');
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="30%" valign="top"><?=TABLE_HEADING_MAIN_CATEGORY?></td>
									<td class="main" valign="top"><b><?=$category_row["categories_heading_title"]?></b></td>
								</tr>
        						<tr>
									<td class="main" width="30%" valign="top"><?=TABLE_HEADING_COMPETITOR_WEIGHT?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('competitor_weight', '', ' id="competitor_weight"')?></td>
								</tr>
        						<tr>
									<td class="main" width="30%" valign="top"><?=ENTRY_COMPETITOR_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_pull_down_menu('competitors_id', $competitors_arr, '', 'id="competitors_id"')?></td>
								</tr>
								<tr>
									<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
									<td align="left">
										<?=tep_submit_button(IMAGE_ASSIGN, IMAGE_ASSIGN, 'name="submitBtn"') . '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_COMPETITORS))?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
        		<script>
					<!--
					function competitor_form_checking() {
						var competitors_id = DOMCall('competitors_id');
						if (competitors_id.value == "0") {
							alert('Please enter select a competitor!');
							competitors_id.focus();
							return false;
						}
						var competitor_weight = DOMCall('competitor_weight');
						if (competitor_weight.value == "") {
							alert('Please enter competitor weight!');
							competitor_weight.focus();
							return false;
						}
						document.competitor_form.submitBtn.disabled = true;
						document.competitor_form.submitBtn.className = 'disabledSubmitBtn';
						document.competitor_form.submitBtn.value = 'Please wait...';

						return true;
					}
					//-->
				</script>
<?
} elseif ($action == "edit_competitor") {
	echo tep_draw_form('competitor_form', FILENAME_COMPETITORS, tep_get_all_get_params(array('subaction')) . 'subaction=update_competitor', 'post', 'onSubmit="return competitor_form_checking();"');
	if ($_REQUEST["cpttID"]) {

		$competitor_select_sql = "select c.competitors_name  from " . TABLE_COMPETITORS . " AS c
									WHERE c.competitors_id = '{$_REQUEST["cpttID"]}'";
		echo tep_draw_hidden_field("cpttID", $_REQUEST["cpttID"]);
		$competitor_result_sql = tep_db_query($competitor_select_sql);
		$competitor_row = tep_db_fetch_array($competitor_result_sql);
	}
	if ($form_values_arr['game_cat_id'] > 0) {
		$category_select_sql = "select cd.categories_heading_title, c2c.competitors_weight
								from ".TABLE_CATEGORIES_DESCRIPTION." AS cd, ".TABLE_COMPETITORS_TO_CATEGORIES." AS c2c
								where cd.categories_id = '{$form_values_arr['game_cat_id']}'
								and cd.categories_id = c2c.categories_id
								and c2c.competitors_id = '{$_REQUEST["cpttID"]}'";

		$category_result_sql = tep_db_query($category_select_sql);
		$category_row = tep_db_fetch_array($category_result_sql);
	}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<?php
										if ($form_values_arr['game_cat_id'] > 0) {
								?>
	        						<tr>
										<td class="main" width="30%" valign="top"><?=TABLE_HEADING_MAIN_CATEGORY?></td>
										<td class="main" valign="top"><b><?=$category_row["categories_heading_title"]?></b></td>
									</tr>
	        						<tr>
										<td class="main" width="30%" valign="top"><?=TABLE_HEADING_COMPETITOR_WEIGHT?></td>
										<td class="main" valign="top"><?=tep_draw_input_field('competitor_weight', $category_row["competitors_weight"], ' id="competitor_weight"')?></td>
									</tr>
								<?php
										}
								?>
        						<tr>
									<td class="main" width="30%" valign="top"><?=ENTRY_COMPETITOR_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('competitor_name', $competitor_row["competitors_name"], ' id="competitor_name"')?></td>
								</tr>
								<tr>
									<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
									<td align="left">
										<?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="submitBtn"') . '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_COMPETITORS))?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
        		<script>
					<!--
					function competitor_form_checking() {
						var competitor_name = DOMCall('competitor_name');
						if (competitor_name.value == "") {
							alert('Please enter competitor name!');
							competitor_name.focus();
							return false;
						}
						<?php
								if ($form_values_arr['game_cat_id'] > 0) {
						?>
							var competitor_weight = DOMCall('competitor_weight');
							if (competitor_weight.value == "") {
								alert('Please enter competitor weight!');
								competitor_weight.focus();
								return false;
							}
						<?
								}
						?>
						document.competitor_form.submitBtn.disabled = true;
						document.competitor_form.submitBtn.className = 'disabledSubmitBtn';
						document.competitor_form.submitBtn.value = 'Please wait...';

						return true;
					}
					//-->
				</script>
<?
} else {
	if ($form_values_arr['game_cat_id'] > 0) {
		$competitor_select_sql = "select c.*, c2c.competitors_weight from " . TABLE_COMPETITORS . " AS c,
									".TABLE_COMPETITORS_TO_CATEGORIES." AS c2c
									WHERE c2c.categories_id = '{$form_values_arr['game_cat_id']}'
									AND c2c.competitors_id = c.competitors_id
									order by c.competitors_name";
	} else {
		$competitor_select_sql = "select * from " . TABLE_COMPETITORS . " order by competitors_name ";
	}

	$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $competitor_select_sql, $competitor_sql_numrows);
	$competitor_result_sql = tep_db_query($competitor_select_sql);

	$game_cat_id_arr = tep_get_game_list_arr();

?>
      				<tr>
      					<td>
						<?=tep_draw_form('game_selection', FILENAME_COMPETITORS, tep_get_all_get_params(array('subaction')) . 'subaction=filter_competitors')?>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td valign="top" align="left" class="dataTableContent"><b><?=TABLE_HEADING_MAIN_CATEGORY?></b> &nbsp; <?=tep_draw_pull_down_menu('game_cat_id', $game_cat_id_arr, (isset($form_values_arr['game_cat_id']) ? $form_values_arr['game_cat_id'] : '0'), 'onchange="this.form.submit();"')?></td>
							</tr>
						</table>
						</form>
      					</td>
      				</tr>
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td width="20%" class="reportBoxHeading"><?=TABLE_HEADING_COMPETITOR_NAME?></td>
			<?
									if ($form_values_arr['game_cat_id'] > 0) {
										echo '<td width="5%" align="center" class="reportBoxHeading">'.TABLE_HEADING_COMPETITOR_WEIGHT.'</td>';
									}
			?>
								    <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								</tr>
			<?
					$row_count = 0;
					while ($competitor_row = tep_db_fetch_array($competitor_result_sql)) {
			    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$competitor_row["competitors_name"]?></td>
			<?
									if ($form_values_arr['game_cat_id'] > 0) {
										echo '<td class="reportRecords" valign="top">'.$competitor_row['competitors_weight'].'</td>';
									}
			?>
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_COMPETITORS, 'action=edit_competitor&cpttID='.$competitor_row["competitors_id"].'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit competitor", "", "", 'align="top"')?></a>
			<?
							if ($form_values_arr['game_cat_id'] > 0) {
								//only allow unasign if we have a category id
								$safe_name = htmlspecialchars(addslashes($competitor_row["competitors_name"]), ENT_QUOTES);
								echo "<a href='".tep_href_link(FILENAME_COMPETITORS, tep_get_all_get_params(array('action', 'subaction', 'cpttID')) . 'action=delete_competitor&subaction=confirm_delete_competitor&cpttID='.$competitor_row["competitors_id"])."' onclick='return confirm(\"Confirm un-assign <".$safe_name."> from this category ?\")'>"
									.tep_image(DIR_WS_ICONS."delete.gif", "Unassign competitor", "", "", 'align="top"')."</a>";
							}
			?>
									</td>
								</tr>
			<?
						$row_count++;
					}
			?>
							</table>
			   			</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP><?=$page_split_object->display_count($competitor_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_COMPETITORS)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($competitor_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
			<?
						if ($form_values_arr['game_cat_id'] > 0) {
							echo '[ <a href="'.tep_href_link(FILENAME_COMPETITORS, 'action=new_competitor').'" class="actionLink">'.LINK_ADD_COMPETITOR.'</a> ] &nbsp; '
								.'[ <a href="'.tep_href_link(FILENAME_COMPETITORS, 'action=assign_competitor').'" class="actionLink">'.LINK_ASSIGN_COMPETITOR.'</a> ]';
						}
			?>
						</td>
					</tr>
<?
}
?>
          		</table>
          	</td>
		</tr>
	</table>
</body>
</html>