<?php
//error_reporting(E_ERROR);
//ini_set("display_errors", true);
require_once('includes/application_top.php');

require_once(DIR_WS_CLASSES . 'buyback_competitor.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

define('DISPLAY_PRICE_DECIMAL', 6);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$form_sessname = basename($_SERVER['PHP_SELF']);
$game_cat_id_arr = tep_get_game_list_arr();

$brackets_show_mode_default = 'show'; //or 'hide'
$brackets_show_mode_label_default = 'Hide'; //or 'hide'

if (!isset($_SESSION[$form_sessname]['brackets_show_mode'])) {
	$_SESSION[$form_sessname]['brackets_show_mode'] = $brackets_show_mode_default;
}

if (isset($_POST['brackets_show_mode'])) {
	$_SESSION[$form_sessname]['brackets_show_mode'] = $_POST['brackets_show_mode'];
}

$action = (isset($_GET['action']) ? $_GET['action'] : 'show_market_info');

$main_cat_id = 0;
if (isset($_REQUEST['game_cat_id'])) {
	$main_cat_id = $_REQUEST['game_cat_id'];
}

if ($main_cat_id > 0) {
    $buybackCompetitorObj = new buyback_competitor($main_cat_id);
    if (!$buybackCompetitorObj->is_buyback_game()) {
    	$messageStack->add_session(ERROR_AVG_PRICE_INVALID_GAME, 'error');
    	tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE));
    }
    
    $path = $buybackCompetitorObj->category_cat_path;
	$buybackCompetitorObj->calculate_market_price();
}

$products_price_arr = array();

switch($action) {
	case 'delete_brackets':
		$bracket_id = (int)$_GET['bracket_id'];
		
		$buybackCompetitorObj->set_delete_competitor_status_bracket($bracket_id);
		$buybackCompetitorObj->save_competitor_status_brackets();
        $buybackCompetitorObj->show_error_warning();
		unset($buybackCompetitorObj);
		
		tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action'))."action=show_market_info"));
		
		break;
	case 'update_brackets':
		$txtPct = $_POST['txtPct'];
		$txtMultiplier = $_POST['txtMultiplier'];
		
        foreach ($txtPct as $bracket_id => $bracket_qty) {
            if (trim($bracket_qty) != "") {
				$buybackCompetitorObj->set_add_edit_competitor_status_bracket($bracket_qty, $txtMultiplier[$bracket_id], $bracket_id);
            }
        }
		
        $buybackCompetitorObj->save_competitor_status_brackets();
        $buybackCompetitorObj->show_error_warning();
		
        unset($buybackCompetitorObj);
		tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action'))."action=show_market_info"));
		
		break;
	case 'show_market_info':
		if (!$buybackCompetitorObj->category_id) {
			break;
		}
		
		break;
	case 'update_market_info':
		if ($HTTP_POST_VARS['btn_csv_import']) {
			if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
				if ($_FILES['csv_import']["size"] > 0) {
					$import_error = false;
					$filename = ($_FILES['csv_import']['tmp_name']);
				    $handle = fopen($filename, 'r+');
					
				    $overallRowNo = $rowNo = 0;
				    $is_competitor_data = true;
				    $is_product_data = false;
					$csv_currency_used = DEFAULT_CURRENCY;
					
					$exist_product_id_array = array_keys($buybackCompetitorObj->products_arr);
					
				    $buybackCompetitorObj->products_arr = array();
				    $buybackCompetitorObj->competitors_price_arr = array();
				    $buybackCompetitorObj->competitors_arr = array();
				    
			    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
			    		$num_cols = count($data);
			    		if ($num_cols > 1 && trim($data[0]) != '') { //Ignore blank lines
							$overallRowNo++;
							
							if ($overallRowNo == 1) {
								$data[1] = strtoupper($data[1]);
								if (isset($currencies->currencies[$data[1]]))	$csv_currency_used = $data[1];
								
								continue;
							} else {
								$rowNo++;
							}
							
							if (substr($data[0], 0, 3) == '###') {	// Separator between competitors and products
							    $is_competitor_data = false;
							    $is_product_data = true;
							    $rowNo = 0; //reInit the rowno so we know where is the column heading for next set of data
							    continue;
							}
							
							if ($is_competitor_data) {
								//todo: check correct column headers if $rowno ==1
								if ($rowNo > 1) {
									$buybackCompetitorObj->assign_competitor(trim($data[0]), trim($data[1]), trim($data[2]));
								}
							} else {
								//todo: check correct column headers if $rowno ==1
								if ($rowNo == 1) {
									$num_prices_columns = ($num_cols-4)/2;
									
									if ($num_prices_columns != $buybackCompetitorObj->competitors_num) {
										$messageStack->add_session(MESSAGE_MISMATCHED_COLUMNS);
										$import_error = true;
										break;
									}
								} else if ($rowNo > 1) {
									$products_id = $data[0];
									$products_cat_path = $data[1];
									
									$prod_category_select_sql = "SELECT categories_id 
																FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
																WHERE products_id = '".tep_db_input($products_id)."' 
																	AND products_is_link=0";
									$prod_category_result_sql = tep_db_query($prod_category_select_sql);
							    	if ($prod_category_row = tep_db_fetch_array($prod_category_result_sql)) {
							    		if (in_array($products_id, $exist_product_id_array)) {
								    		$buybackCompetitorObj->products_arr[$products_id]['cat_id'] = $prod_category_row['categories_id'];
								    		
											//Get the overwrite values into the class. always the last column.
											if (trim($data[$num_cols-1]) != '') {
												$buybackCompetitorObj->products_arr[$products_id]['avg_competitor_price_overwrite'] = number_format(trim($data[$num_cols-1]), DISPLAY_PRICE_DECIMAL, '.', '');
											} else {
												$buybackCompetitorObj->products_arr[$products_id]['avg_competitor_price_overwrite'] = null;
											}
											
											$competitor_id_arr = array_keys($buybackCompetitorObj->competitors_arr);
											$colNo = 2;
											foreach ($competitor_id_arr as $competitor_id) {
												$statusStr = trim(strtolower($data[$colNo+1]));
												switch ($statusStr) {
													case 'full':
														$is_buyback = 0;
														break;
													default:
														$is_buyback = 1;
												}
												
												$competitor_buying_price = $currencies->advance_currency_conversion((double)$data[$colNo], $csv_currency_used, DEFAULT_CURRENCY, true, 'buy');	// Must same rate as when we show our buying price in I want to sell page
												
												$buybackCompetitorObj->assign_competitors_price($products_id, $competitor_id, $competitor_buying_price, $is_buyback);
												$colNo += 2;
											}
										} else {
											$messageStack->add_session(sprintf(ERROR_AVG_PRICE_IMPORTED_PRODUCT_NOT_MATCH, $products_id), 'warning');
											$import_error = true;
										}
									} else {
										$messageStack->add_session(sprintf(WARNING_PRODUCT_DOES_NOT_EXISTS, $products_id, $products_cat_path), 'warning');
										$import_error = true;
									}
								}
							}//end server data
			    		}
			    	}
					
				    if (!$import_error) {
						//no errors
						$buybackCompetitorObj->calculate_market_price();
						$action = 'show_market_info';
				    	fclose($handle);
				    	
				    	$messageStack->add(SUCCESS_AVG_PRICE_IMPORT_FILE, 'success');
				    } else {
				    	fclose($handle);
				    	$messageStack->add_session(ERROR_IMPORT_FAILED);
				    	tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=show_market_info'));
				    }
				} else {
					$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
			    	tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=show_market_info'));
				}
			} else {
				$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
		    	tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=show_market_info'));
			}
		} else {
			//Update competitor weight
			if (isset($_POST['weight'])) {
				foreach ($_POST['weight'] as $competitor_id => $weight_arr) {
					foreach ($weight_arr as $weight) {
						$buybackCompetitorObj->assign_competitors_weight($competitor_id, $weight);
					}
				}
			}
			
			$buybackCompetitorObj->save_competitors_weight();
			
			if (isset($_POST['competitors_price']) && is_array($_POST['competitors_price']) && count($_POST['competitors_price'])) {
				foreach ($_POST['competitors_price'] as $products_id => $products_arr) {
					foreach ($products_arr as $competitor_id => $competitor_arr) {
						$buybackCompetitorObj->assign_competitors_price($products_id, $competitor_id, $competitor_arr['price'], (int)$competitor_arr['is_buyback']);
					}
					
					if (isset($_POST['avg_competitor_price'][$products_id])) {
						$buybackCompetitorObj->products_arr[$products_id]['avg_competitor_price'] = number_format($_POST['avg_competitor_price'][$products_id], DISPLAY_PRICE_DECIMAL, '.', '');
					}
					
					if (isset($_POST['avg_competitor_price_overwrite'][$products_id]) && trim($_POST['avg_competitor_price_overwrite'][$products_id]) != '') {
						$buybackCompetitorObj->products_arr[$products_id]['avg_competitor_price_overwrite'] = number_format($_POST['avg_competitor_price_overwrite'][$products_id], DISPLAY_PRICE_DECIMAL, '.', '');
					} else {
						$buybackCompetitorObj->products_arr[$products_id]['avg_competitor_price_overwrite'] = null;
					}
				}
			}
			
			if ($_POST['btn_csv_export']) {
				//Clicked export
				$export_csv_data = $buybackCompetitorObj->get_data_csv();
				
				if (tep_not_null($export_csv_data)) {
					$filename = 'avg_market_price_'.date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action'))));
				}
			} else if ($_POST['btn_db_update']) {
				//Clicked update
				//Don't calculate average price again. save resources and use values from POST.
				//Warning: This means no active bracket will be set. but we dont use that in save. and we will redirect anyway.
				//$buybackCompetitorObj->calculate_market_price();
				$buybackCompetitorObj->save_prices();
				
				tep_redirect(tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action'))."action=show_market_info"));
			}
		}
		break;
}

$form_values_arr = $_SESSION[$form_sessname];

//-----Start Brackets ---------------------------------------------------
$bracketsHTML .= '	<tr><td class="dataTableContent" colspan="2">'
					. tep_draw_form('show_brackets', FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=update_brackets', 'post')
	                . tep_draw_hidden_field('hidNumBrackets', $buybackCompetitorObj->buyback_num_brackets, 'id="hidNumBrackets" ')
	                . tep_draw_hidden_field('brackets_show_mode', $form_values_arr['brackets_show_mode'], 'id="brackets_show_mode_2"')
	                . '<table cellpadding="3" cellspacing="0" width="1" border="0"><!-- Open outer 1-->';

$color = "#FFFFCC";
$num_columns_per_row = 8;
//Don't enter if num brackets = 0.
for ($i=0; $i < $buybackCompetitorObj->competitor_status_num_brackets; $i++) {
	$row = $buybackCompetitorObj->competitor_status_brackets[$i];
	
    //Bracket field template
	$row_percent_svr_full = tep_draw_input_field("txtPct[{$row['buyback_competitor_status_bracket_id']}]",$row['buyback_competitor_status_bracket_quantity'], 'size="12" id="txtQty_'.$i.'" ');
	$row_multiplier = tep_draw_input_field("txtMultiplier[".$row['buyback_competitor_status_bracket_id']."]", number_format($row['buyback_competitor_status_bracket_value'], DISPLAY_PRICE_DECIMAL), 'size="8" id="txtPrice_'.$i.'" ');
	$row_delete_link = '<a href="'.tep_href_link(FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action', 'bracket_id'))."action=delete_brackets&bracket_id=".$row['buyback_competitor_status_bracket_id']).'">'.TEXT_DELETE.'</a>';
	
	if (($i % $num_columns_per_row)==0) {
	    //Starting left most column
	    if ($i > 0) {
            $bracketsHTML .= '</tr>';
	    }
	    
        $bracketsHTML .= '	<tr>
								<td>
		                          	<table cellpadding="3" cellspacing="0" width="1" border="0">
		                              	<tr>
		                                  <td width="12%" align="left" class="ordersBoxHeading">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
		                                  <td width="12%" align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>
		                              </tr>
		                              <tr>
		                                  <td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_SVR_FULL.'</td>
		                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_svr_full.'</td>
		                              </tr>
		                              <tr>
		                                  <td width="12%" align="left" class="ordersBoxHeading">'.TEXT_MULTIPLIER.'</td>
		                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_multiplier.'</td>
		                              </tr>
		                              <tr>
		                                  <td width="12%" align="left" class="ordersBoxHeading">&nbsp;</td>
		                                  <td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.$row_delete_link.'</td>
		                              </tr>
		                          </table>
		                      </td>';
	} else {
	    //Second column till row end.
        $bracketsHTML .= '	<td>
								<table cellpadding="3" cellspacing="0" width="1" border="0">
									<tr>
										<td width="12%" align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>
		                           	</tr>
		                            <tr>
		                            	<td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_svr_full.'</td>
		                         	</tr>
		                            <tr>
										<td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_multiplier.'</td>
		                          	</tr>
		                          	<tr>
										<td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.$row_delete_link.'</td>
									</tr>
                          		</table>
	                  		</td>';
	}
	
	//Put here so new bracket form has color change
	$color = (($i % 2)==0) ? "#D7D5D0" : "#FFFFCC";
}

//New Bracket form
$bracketsHTML .= '	<td>
						<table cellpadding="3" cellspacing="0" width="1" border="0">
							<tr>
								<td width="12%" align="left" class="ordersBoxHeading">'.TABLE_HEADING_BRACKET_NEW.'</td>
							</tr>
							<tr>
								<td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtPct[0]", '', "size='12' id='txtQty_new'").'</td>
							</tr>
							<tr>
								<td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtMultiplier[0]", '', 'size="8" id="txtPrice_new" ') .'</td>
							</tr>
							<tr>
								<td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">&nbsp;</td>
							</tr>
                      	</table>
					</td>
				</tr>';

$bracketsHTML .= '
			</table><!-- Close Outer 1-->
		</td>
	</tr>
	<tr>
		<td class="dataTableContent" colspan="2">
			<input type="submit" name="btn_bracket_update" value="'.IMAGE_BUTTON_UPDATE_BRACKETS.'" title="'.IMAGE_BUTTON_UPDATE_BRACKETS.'" class="inputButton">
			</form>
		</td>
	</tr>';
//-----End Brackets ---------------------------------------------------

$form_values_arr = $_SESSION[$form_sessname];
//------Globals end.
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET;?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script type="text/javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/select_box.js"></script>
	<script type="text/javascript">
	<!--
		function toggleBracketsTbody() {
			if (document.getElementById('bracketsTbody').className == 'hide') {
				document.getElementById('bracketLinkLabel').innerHTML = 'Hide';
				document.getElementById('bracketsTbody').className = 'show';
				document.getElementById('brackets_show_mode_1').value = 'show';
				document.getElementById('brackets_show_mode_2').value = 'show';
			} else {
				document.getElementById('bracketLinkLabel').innerHTML = 'Show';
				document.getElementById('bracketsTbody').className = 'hide';
				document.getElementById('brackets_show_mode_1').value = 'hide';
				document.getElementById('brackets_show_mode_2').value = 'hide';
			}
		}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?	require(DIR_WS_INCLUDES . 'header.php'); ?>
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="2" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
				</table>
			</td>
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td colspan="2" class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('game_selection', FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=show_market_info', 'get')?>
								<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td valign="top" align="left" class="dataTableContent"><b><?=TABLE_HEADING_MAIN_CATEGORY?></b> &nbsp; <?=tep_draw_pull_down_menu('game_cat_id', $game_cat_id_arr, $main_cat_id, 'onchange="this.form.submit();"')?></td>
									</tr>
								</table>
							</form>
						</td>
					</tr>
					<tr>
						<td colspan="2" class="dataTableContent">&nbsp;</td>
					</tr>
<?
switch ($action) {
	case 'show_market_info':
		if ($buybackCompetitorObj) {
?>
					<tr>
						<td colspan="2" class="dataTableContent"><b><?=TEXT_BRACKETS_FOR?></b> <?=$path?>&nbsp;&nbsp;<a href="javascript:void(0);" onclick="toggleBracketsTbody();"><div id="bracketLinkLabel"><?=$brackets_show_mode_label_default?></div></a></td>
					</tr>
					<tbody id="bracketsTbody" name="bracketsTbody" class="<?=$form_values_arr['brackets_show_mode']?>">
						<?=$bracketsHTML?>
					</tbody>
					<tr>
						<td>
							<?=tep_draw_form('show_market_info', FILENAME_COMPETITORS_AVERAGE_MARKET_PRICE, tep_get_all_get_params(array('action')) . 'action=update_market_info', 'post', 'enctype="multipart/form-data"')
								. tep_draw_hidden_field('brackets_show_mode', $form_values_arr['brackets_show_mode'], 'id="brackets_show_mode_1"')?>
<?
			//Figure out the column headers
			$competitorsNameHTML = '	<td align="left" class="ordersBoxHeading">'.TABLE_HEADING_COMPETITOR_CODE.'</td>';
			$competitorsWeightHTML = '	<td align="left" class="ordersBoxHeading">'.TABLE_HEADING_COMPETITOR_WEIGHT.'</td>';
			
			foreach ($buybackCompetitorObj->competitors_arr as $competitors_id => $competitors_info_arr) {
				$competitorsNameHTML .= "<td align='center' class='ordersBoxHeading'>{$competitors_info_arr['code']}</td>";
				$competitorsWeightHTML .= "<td rowspan='2' align='center' class='ordersBoxHeading'>".tep_draw_input_field("weight[$competitors_id][$main_cat_id]", "{$competitors_info_arr['weight']}", "size='4' id=\"weight_{$competitors_id}_{$main_cat_id}\"")."</td>";
			}
			$competitorsNameHTML .= '<td rowspan="3" align="center" class="ordersBoxHeading">'.TABLE_HEADING_ACTIVE_BRACKET.'</td>';
			$competitorsNameHTML .= '<td rowspan="3" align="center" class="ordersBoxHeading">'.TABLE_HEADING_AVERAGE_MARKET_PRICE.'</td>';
			$competitorsNameHTML .= '<td rowspan="3" align="center" class="ordersBoxHeading">'.TABLE_HEADING_OVERWRITE_AVERAGE_MARKET_PRICE.'</td>';
			
			$serversHTML = '<td align="left" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT.'</td>';;
			$i = 0;
			
			foreach ($buybackCompetitorObj->products_arr as $products_id => $products_row) {
				$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				$product_display_name = tep_display_category_path(tep_get_product_path($products_id), $products_row['cat_id']);
				
				$serversHTML .= "<tr class='$row_style' onmouseover=\"rowOverEffect(this, 'ordersListingRowOver')\" onmouseout=\"rowOutEffect(this, '$row_style')\" onclick=\"rowClicked(this, '$row_style')\">";
				$serversHTML .= '<td class="ordersRecords">'.$product_display_name.'</td>';
				
				foreach($buybackCompetitorObj->competitors_arr as $competitors_id => $competitors_info_arr) {
					$products_price_arr = $buybackCompetitorObj->competitors_price_arr[$products_id][$competitors_id];
					
					$serversHTML .= '<td class="main" align="center">';
					$is_buyback = (int)$products_price_arr['is_buyback'];
					if (is_numeric($products_price_arr['price'])) {
						$buying_price = number_format($products_price_arr['price'], DISPLAY_PRICE_DECIMAL);
					} else {
						$buying_price = $products_price_arr['price'];
					}
					
					//Full servers in bold.
					$serversHTML .= ($is_buyback ? $buying_price : "<b>".$buying_price."</b>");
					
					$serversHTML .= tep_draw_hidden_field("competitors_price[$products_id][$competitors_id][price]", $buying_price)
									. tep_draw_hidden_field("competitors_price[$products_id][$competitors_id][is_buyback]", $is_buyback)
									. '</td>';
				}
				
				$serversHTML .= '<td class="main">'.$products_row['buyback_competitor_status_bracket']['buyback_competitor_status_bracket_quantity'].'% ('.$products_row['buyback_competitor_status_bracket_key'].')</td>';
				$serversHTML .= '<td align="center" class="main">'.$products_row['avg_competitor_price'].tep_draw_hidden_field("avg_competitor_price[$products_id]", $products_row['avg_competitor_price'], "id=avg_competitor_price_".$products_id).'</td>';
				$serversHTML .= '<td align="center" class="main">'.tep_draw_input_field("avg_competitor_price_overwrite[$products_id]", $products_row['avg_competitor_price_overwrite'], 'size="7"').'</td>';
				$serversHTML .= '</tr>';
				$i++;
			}
?>
							<table border="0" cellspacing="0" cellpadding="2" width="80%">
								<tr>
									<?=$competitorsNameHTML?>
								</tr>
								<tr>
									<?=$competitorsWeightHTML?>
								</tr>
								<?=$serversHTML?>
								<tr>
									<td align="left" colspan="<?=$buybackCompetitorObj->competitors_num+1?>">
										<?=tep_draw_file_field('csv_import', 'size="50"')?>
										<?=tep_submit_button('Import', 'Import csv file', 'name="btn_csv_import"', 'inputButton')?>
									</td>
									<td colspan="3" align="right">
										<?=isset($HTTP_POST_VARS['btn_csv_import']) ? '' : tep_submit_button('Export', 'Export as csv file', 'name="btn_csv_export"', 'inputButton')?>&nbsp;
										<?=tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="btn_db_update"', 'inputButton')?>
									</td>
								</tr>
    						</table>
							</form>
						</td>
					</tr>
<?		}
		break;
}
?>
				</table>
			</td>
		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php');?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>