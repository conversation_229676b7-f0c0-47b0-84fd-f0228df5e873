<?
/*
  	$Id: configuration.php,v 1.8 2006/07/17 07:19:53 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
    switch ($action) {
		case 'save_global_conf':
			while (list($key, $value) = each($HTTP_POST_VARS['configuration'])) {
          		if (is_array($value)) 	$value = implode(", ", $value);
          		$configuration_value = tep_db_prepare_input($value);
          		$conf_update_sql = "UPDATE " . TABLE_CONFIGURATION . " 
          							SET configuration_value = '" . tep_db_input($configuration_value) . "', 
          								last_modified = now() 
          							WHERE configuration_key = '" . $key . "'
          								AND configuration_value <> '" . tep_db_input($configuration_value) . "'";
          		tep_db_query($conf_update_sql);
        	}
        	
        	// set the WARN_BEFORE_DOWN_FOR_MAINTENANCE to false if DOWN_FOR_MAINTENANCE = true
        	if (WARN_BEFORE_DOWN_FOR_MAINTENANCE == 'true') {
        		$down_for_maintenance_query = tep_db_query("SELECT configuration_value FROM " . TABLE_CONFIGURATION . " where configuration_key ='DOWN_FOR_MAINTENANCE' AND configuration_group_id IN (" . (tep_not_null($gID) ? $gID : '') . ")");
        		$down_for_maintenance_row = tep_db_fetch_array($down_for_maintenance_query);
        		if ($down_for_maintenance_row["configuration_value"] == "true") {
        			tep_db_query("UPDATE " . TABLE_CONFIGURATION . " SET configuration_value = 'false', last_modified = '" . NOW . "' WHERE configuration_key = 'WARN_BEFORE_DOWN_FOR_MAINTENANCE'");
        		}
        	} else if (DOWN_FOR_MAINTENANCE == 'true') {
        		$warn_for_maintenance_query = tep_db_query("SELECT configuration_value FROM " . TABLE_CONFIGURATION . " where configuration_key ='WARN_BEFORE_DOWN_FOR_MAINTENANCE' AND configuration_group_id IN (" . (tep_not_null($gID) ? $gID : '') . ")");
        		$warn_for_maintenance_row = tep_db_fetch_array($warn_for_maintenance_query);
        		if ($warn_for_maintenance_row["configuration_value"] == "true") {
        			tep_db_query("UPDATE " . TABLE_CONFIGURATION . " SET configuration_value = 'false', last_modified = '" . NOW . "' WHERE configuration_key = 'WARN_BEFORE_DOWN_FOR_MAINTENANCE'");
        		}
        	}
        	
        	tep_redirect(tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $HTTP_GET_VARS['gID']));
        case 'save_cat_conf':
        	if (tep_not_null($HTTP_GET_VARS['gID'])) {
        		$gID_to_update_array = explode(',', $HTTP_GET_VARS['gID']);
        		$cat_id = (int)$HTTP_POST_VARS['selected_cat_id'];
        		$use_parent_array = is_array($HTTP_POST_VARS["use_parent"]) ? $HTTP_POST_VARS["use_parent"] : array();
        		$cfg_setting_array = is_array($HTTP_POST_VARS["configuration"]) ? $HTTP_POST_VARS["configuration"] : array();
        		$remove_from_child_array = is_array($HTTP_POST_VARS["remove_from_child"]) ? $HTTP_POST_VARS["remove_from_child"] : array();
        		
        		for ($i=0; $i < count($gID_to_update_array); $i++) {
        			$gID = $gID_to_update_array[$i];
        			$full_configuration_set_select_sql = "SELECT categories_configuration_key 
														  FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
														  WHERE categories_configuration_group_id = '" . (int)$gID . "' 
														 	 AND categories_id = 0 
														  ORDER BY sort_order";
					$full_configuration_set_result_sql = tep_db_query($full_configuration_set_select_sql);
					while ($full_configuration_set_row = tep_db_fetch_array($full_configuration_set_result_sql)) {
						$ref_key = $full_configuration_set_row["categories_configuration_key"];
						if (in_array($ref_key, $use_parent_array)) {
							// remove this key setting for this cat_id
							if ($cat_id > 0) {
								$cat_configuration_delete_sql = "	DELETE FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
																	WHERE categories_configuration_key = '" . $ref_key . "'
																		AND categories_configuration_group_id = '" . (int)$gID . "' 
																		AND categories_id = '" . $cat_id . "'";
								tep_db_query($cat_configuration_delete_sql);
							}
						} else {
							if (isset($cfg_setting_array[$ref_key])) {
								$cat_configuration_select_sql = "	SELECT categories_configuration_id  
																	FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
																	WHERE categories_configuration_key = '" . $ref_key . "'
																		AND categories_configuration_group_id = '" . (int)$gID . "' 
																		AND categories_id = '" . $cat_id . "'";
								$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);
								if (!tep_db_num_rows($cat_configuration_result_sql)) {
									// insert
									$cfg_default_value_select_sql = "SELECT *  
																	 FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
																	 WHERE categories_configuration_key = '" . $ref_key . "' 
																	 	AND categories_configuration_group_id = '" . (int)$gID . "' 
																	 	AND categories_id = 0 ";
									$cfg_default_value_result_sql = tep_db_query($cfg_default_value_select_sql);
									if ($cfg_default_value_row = tep_db_fetch_array($cfg_default_value_result_sql)) {
										$sql_data_array = array('categories_id' => (int)$cat_id,
							                      				'categories_configuration_title' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_title"]),
							                      				'categories_configuration_key' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_key"]),
							                      				'categories_configuration_value' => tep_db_prepare_input($cfg_setting_array[$ref_key]),
							                      				'categories_configuration_description' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_description"]),
							                      				'categories_configuration_group_id' => (int)$gID,
							                      				'sort_order' => tep_db_prepare_input($cfg_default_value_row["sort_order"]),
							                      				'last_modified' => 'NULL',
							                      				'date_added' => 'now()',
							                      				'use_function' => tep_db_prepare_input($cfg_default_value_row["use_function"]),
							                      				'set_function' => tep_db_prepare_input($cfg_default_value_row["set_function"])
							                      				);
										tep_db_perform(TABLE_CATEGORIES_CONFIGURATION, $sql_data_array);
									}
								} else if (tep_db_num_rows($cat_configuration_result_sql) == 1) {
									// update
									$configuration_update_value = tep_db_prepare_input($cfg_setting_array[$ref_key]);
									$cat_configuration_update_sql = "	UPDATE " . TABLE_CATEGORIES_CONFIGURATION . " 
																		SET categories_configuration_value = '" . tep_db_input($configuration_update_value) . "'
																		WHERE categories_configuration_key = '" . $ref_key . "'
																			AND categories_configuration_group_id = '" . (int)$gID . "' 
																			AND categories_id = '" . $cat_id . "'";
									tep_db_query($cat_configuration_update_sql);
								} else {
									$messageStack->add_session('More than one records found for this key: ' . $ref_key, 'error');
								}
							}
						}
						
						if (in_array($ref_key, $remove_from_child_array)) {
							// remove this key setting for all its child categories
							tep_get_subcategories($sub_category_array, $cat_id);
							
							if (count($sub_category_array) > 0) {
								$cat_configuration_delete_sql = "	DELETE FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
																	WHERE categories_configuration_key = '" . $ref_key . "'
																		AND categories_configuration_group_id = '" . (int)$gID . "' 
																		AND categories_id <> 0 AND categories_id IN(" . (implode(',', $sub_category_array)) . ")";
								tep_db_query($cat_configuration_delete_sql);
							}
						}
					}
        		}
        	}
        	tep_redirect(tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $HTTP_GET_VARS['gID'] . '&cat_id=' . $cat_id ));
        	break;
        break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
    				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?='Configuration'?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
$gID_str = (isset($HTTP_GET_VARS['gID'])) ? $HTTP_GET_VARS['gID'] : 1;
$gID_array = explode(',', $gID_str);

echo tep_draw_form('configuration_form', FILENAME_CONFIGURATION, 'action=save_global_conf&gID=' . $HTTP_GET_VARS['gID']);
$global_setting_exist = false;
for ($i=0; $i < count($gID_array); $i++) {
	$gID = $gID_array[$i];
	
	$cfg_group_query = tep_db_query("select configuration_group_title from " . TABLE_CONFIGURATION_GROUP . " where configuration_group_id = '" . (int)$gID . "'");
	$cfg_group = tep_db_fetch_array($cfg_group_query);
	
	$global_configuration_select_sql = "SELECT configuration_id, configuration_title, configuration_key, configuration_value, configuration_description, use_function, set_function FROM " . TABLE_CONFIGURATION . " WHERE configuration_group_id = '" . (int)$gID . "' ORDER BY sort_order";
	$global_configuration_result_sql = tep_db_query($global_configuration_select_sql);
	if (tep_db_num_rows($global_configuration_result_sql) > 0) {
		$global_setting_exist = true;
?>
								<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="1" cellpadding="1">
											<tr>
			            						<td class="pageHeading" valign="top" colspan="3"><?=$cfg_group['configuration_group_title']?></td>
			          						</tr>
              								<tr class="dataTableHeadingRow">
                								<td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_TITLE?></td>
									            <td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_VALUE?></td>
									            <td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_DESCRIPTION?></td>
											</tr>
<?
		$row_count = 0;
		while ($global_configuration = tep_db_fetch_array($global_configuration_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			$cInfo = new objectInfo($global_configuration);
			
			if (tep_not_null($cInfo->use_function)) {
		    	$use_function = $cInfo->use_function;
		      	if (ereg_dep('->', $use_function)) {
		        	$class_method = explode('->', $use_function);
		        	if (!is_object(${$class_method[0]})) {
		          		include(DIR_WS_CLASSES . $class_method[0] . '.php');
		          		${$class_method[0]} = new $class_method[0]();
		        	}
		        	$cfgValue = tep_call_function($class_method[1], $cInfo->configuration_value, ${$class_method[0]});
		      	} else {
		        	$cfgValue = tep_call_function($use_function, $cInfo->configuration_value);
		      	}
		    } else {
		      	$cfgValue = $cInfo->configuration_value;
		    }
			
			if ($cInfo->set_function) {
	      		if (strpos($cInfo->set_function, 'tep_cfg_textarea') === 0) {
	      			eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "'.$cInfo->configuration_key.'", "66", "5");');
	      		} else {
	        		eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "'.$cInfo->configuration_key.'");');
	        	}
	      	} else {
	        	$value_field = tep_draw_input_field('configuration[' . $cInfo->configuration_key . ']', $cfgValue, 'size="50"');
	      	}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
                								<td class="cfgRecords" width="30%" valign="top" nowrap><?=$cInfo->configuration_title?></td>
                								<td class="cfgRecords" valign="top"><?=$value_field?></td>
                								<td class="cfgRecords" width="30%" valign="top"><?=$cInfo->configuration_description?></td>
              								</tr>
	<?		$row_count++;
		}
		echo '							</table>
									</td>
								</tr>';
	}
}

if ($global_setting_exist == true) {
	echo '						<tr>
									<td colspan="3" align="right"><br>'.tep_image_submit('button_update.gif', IMAGE_UPDATE).'</td>
								</tr>';
}
?>
								</form>
							</table>
						</td>
					</tr>
					<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
$cfg_exist_in_cat_cfg_query = tep_db_query("select categories_configuration_id from " . TABLE_CATEGORIES_CONFIGURATION . " where categories_configuration_group_id IN (" . (tep_not_null($gID_str) ? $gID_str : '') . ")");
if (tep_db_num_rows($cfg_exist_in_cat_cfg_query) > 0) {
	$categories_array = tep_get_category_tree(0, '___', '', $categories_array);
?>
								<tr>
									<td class="main">
									<?
										echo tep_draw_form('cat_search_form', FILENAME_CONFIGURATION, 'gID=' . $HTTP_GET_VARS['gID']);
				    					echo tep_draw_pull_down_menu("cat_id", $categories_array, $_REQUEST["cat_id"], ' id="cat_id" onChange="this.form.submit();"');
				    					echo '</form>';
				    				?>
					    			</td>
					    		</tr>
<?
}

echo tep_draw_form('cat_configuration_form', FILENAME_CONFIGURATION, 'action=save_cat_conf&gID=' . $HTTP_GET_VARS['gID']);
$selected_cat_id = tep_not_null($_REQUEST["cat_id"]) && is_numeric($_REQUEST["cat_id"]) ? $_REQUEST["cat_id"] : 0;
echo tep_draw_hidden_field("selected_cat_id", $selected_cat_id);
$cat_setting_exist = false;
for ($i=0; $i < count($gID_array); $i++) {
	$gID = $gID_array[$i];
	
	$cfg_group_query = tep_db_query("select configuration_group_title from " . TABLE_CONFIGURATION_GROUP . " where configuration_group_id = '" . (int)$gID . "'");
	$cfg_group = tep_db_fetch_array($cfg_group_query);
	
	// Category based configuration section
	$selected_configuration_array = array();
	$selected_cat_configuration_select_sql = "	SELECT categories_configuration_id, categories_configuration_title, categories_configuration_key, categories_configuration_value, categories_configuration_description, use_function, set_function 
												FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
												WHERE categories_configuration_group_id = '" . (int)$gID . "' 
													AND categories_id = '" . $selected_cat_id . "'
												ORDER BY sort_order";
	$selected_cat_configuration_result_sql = tep_db_query($selected_cat_configuration_select_sql);
	while ($selected_cat_configuration = tep_db_fetch_array($selected_cat_configuration_result_sql)) {
		$selected_configuration_array[$selected_cat_configuration["categories_configuration_key"]] = $selected_cat_configuration;
	}
	
	$cat_configuration_select_sql = "SELECT categories_configuration_id, categories_configuration_title, categories_configuration_key, categories_configuration_value, categories_configuration_description, use_function, set_function 
									 FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
									 WHERE categories_configuration_group_id = '" . (int)$gID . "' 
									 	AND categories_id = 0 
									 ORDER BY sort_order";
	$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);
	if (tep_db_num_rows($cat_configuration_result_sql) > 0) {
		$cat_setting_exist = true;
?>
								<tr>
									<td>
										<table border="0" width="100%" cellspacing="1" cellpadding="1">
											<tr>
			            						<td class="pageHeading" valign="top" colspan="3"><?=$cfg_group['configuration_group_title']?></td>
			          						</tr>
			  								<tr class="dataTableHeadingRow">
			    								<td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_TITLE?></td>
									            <td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_VALUE?></td>
									            <td class="cfgBoxHeading"><?=TABLE_HEADING_CONFIGURATION_DESCRIPTION?></td>
											</tr>
<?
		$row_count = 0;
		while ($cat_configuration = tep_db_fetch_array($cat_configuration_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			$use_parent_setting = false;
			if (is_array($selected_configuration_array[$cat_configuration["categories_configuration_key"]]) && count($selected_configuration_array[$cat_configuration["categories_configuration_key"]])) {
				$cInfo = new objectInfo($selected_configuration_array[$cat_configuration["categories_configuration_key"]]);
			} else {
				$cInfo = new objectInfo($cat_configuration);
				$use_parent_setting = true;
			}
			
			if (!$use_parent_setting) {
				if (tep_not_null($cInfo->use_function)) {
			    	$use_function = $cInfo->use_function;
			      	if (ereg_dep('->', $use_function)) {
			        	$class_method = explode('->', $use_function);
			        	if (!is_object(${$class_method[0]})) {
			          		include(DIR_WS_CLASSES . $class_method[0] . '.php');
			          		${$class_method[0]} = new $class_method[0]();
			        	}
			        	$cfgValue = tep_call_function($class_method[1], $cInfo->categories_configuration_value, ${$class_method[0]});
			      	} else {
			        	$cfgValue = tep_call_function($use_function, $cInfo->categories_configuration_value);
			      	}
			    } else {
			      	$cfgValue = $cInfo->categories_configuration_value;
			    }
			} else {
				$cfgValue = '';
			}
			
	      	if ($cInfo->set_function) {
	      		if (strpos($cInfo->set_function, 'tep_cfg_textarea') === 0) {
	      			eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "'.$cInfo->categories_configuration_key.'", "66", "5");');
	      		} else {
	        		eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "'.$cInfo->categories_configuration_key.'");');
	        	}
	      	} else {
	        	$value_field = tep_draw_input_field('configuration[' . $cInfo->categories_configuration_key . ']', $cfgValue, 'size="50"');
	      	}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
                								<td class="cfgRecords" width="30%" valign="top" nowrap><?=$cInfo->categories_configuration_title?></td>
								                <td>
								                	<table border="0" cellspacing="0" cellpadding="0">
								                <?
								                	if ($selected_cat_id > 0) {
								                		echo '<tr><td class="cfgRecords" valign="top">' . tep_draw_checkbox_field("use_parent[]", $cInfo->categories_configuration_key, ($use_parent_setting ? true : false), "", 'onClick="setFieldAvailability(this.value, this.checked ? \'1\' : \'0\');"') . '</td><td class="cfgRecords"><i>'.TEXT_USE_PARENT_SETTING.'</i></td></tr>';
								                	}
								                	echo '<tr><td class="cfgRecords" valign="top" colspan="2">'.$value_field.'</td></tr>';
								                	echo '<tr><td class="cfgRecords" valign="top">'.tep_draw_checkbox_field("remove_from_child[]", $cInfo->categories_configuration_key, false, "", '') . '</td><td class="cfgRecords"><i>'.TEXT_REMOVE_CHILD_SETTING.'</i></td></tr>';
								                ?>
								                	</table>
								                </td>
								                <td class="cfgRecords" width="30%" valign="top"><?=$cInfo->categories_configuration_description?></td>
              								</tr>
<?			$row_count++;
		}
		echo '							</table>
									</td>
								</tr>';
	}
}

if ($cat_setting_exist == true) {
	echo '						<tr>
									<td colspan="3" align="right"><br>'.tep_image_submit('button_update.gif', IMAGE_UPDATE).'</td>
								</tr>';
}
?>
								</form>
        					</table>
        					<script>
        					<!--
        						function setFieldAvailability(ctrlName, disabledMode) {
        							var curInputSelect = document.cat_configuration_form.elements['configuration['+ctrlName+']'];
    								if (typeof (curInputSelect.length) != 'undefined') {
    									for (cnt=0; cnt<curInputSelect.length; cnt++) {
    										curInputSelect[cnt].disabled = disabledMode == 1 ? true : false;
    									}
    								} else {
    									curInputSelect.disabled = (disabledMode == 1 ? true : false);
    								}
        						}
        						
        						function init() {
	        						var multiSelect = document.cat_configuration_form.elements['use_parent[]'];
	        						if (typeof (multiSelect) != 'undefined' && multiSelect.length > 0) {
	        							for (i=0;i<multiSelect.length;i++) {
		        							if (multiSelect[i].checked == true) {
		        								setFieldAvailability(multiSelect[i].value, 1);
		        							}
		        						}
	        						} else {
	        							if (multiSelect != null && multiSelect.checked == true) {
	        								setFieldAvailability(multiSelect.value, 1);
	        							}
	        						}
        						}
        						
        						init();
        					//-->
        					</script>
        				</td>
      				</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>