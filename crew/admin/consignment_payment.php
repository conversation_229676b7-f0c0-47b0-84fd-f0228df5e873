<?php
/*
  Developer: <PERSON><PERSON>ri
  Copyright (c) 2017 DPSB
 */

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'consignment_payment.php');
require_once('pear/Date.php');

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CDK_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CDK_PAYMENT);
}

define('DISPLAY_PRICE_DECIMAL', 4);

$allow_create_cdk_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_NEW_PO');
$allow_process_cdk_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_PROCESS_PO');
$allow_cdk_cancel_pending_receive_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_CANCEL_PENDING_RECEIVE');
$allow_verify_cdk_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_VERIFY_PO');
$allow_add_cdk_remark_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_ADD_REMARK');
$allow_view_cdk_remark_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_VIEW_REMARK');
$allow_cdk_make_payment_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_MAKE_PAYMENT');

if (isset($_REQUEST['subaction'])) {
    switch ($_REQUEST['subaction']) {
        case 'create_blank_cdk':
            if (!$allow_create_cdk_permission) {
                $messageStack->add_session(ERROR_CDK_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'add_cdk_cb':
            if (!$allow_create_cdk_permission) {
                $messageStack->add_session(ERROR_CDK_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'create_cdk':
            $error = false;

            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['low_stock_batch'])) {
                    $messageStack->add_session(ERROR_CDK_FORM_EMPTY_PRODUCTS, 'error');
                    $error = true;
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_CDK_FORM_CREATE_PERMISSION, 'error');
            }

            if ($error) {
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
    }
}

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$edit_cdk_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;

// Back button from preview
if (isset($_REQUEST['BackBtn'])) {
    if ($subaction == "insert_cdk_product") {
        $_REQUEST['subaction'] = "create_cdk_product";
        $_GET['subaction'] = "create_cdk_product";
        $subaction = "create_cdk_product";
    } else {
        tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id')) . '&subaction=show_cdk_list'));
    }
}

// Back button from calculate cdk
if (isset($_REQUEST['BackBtnToList'])) {
    if ($subaction == "preview_cdk") {
        $_REQUEST['subaction'] = "create_cdk";
        $_GET['subaction'] = "create_cdk";
        $subaction = "create_cdk";
    } else {
        tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id')) . '&subaction=show_cdk_list'));
    }
}

// CDK preview CB
if (isset($_REQUEST['cdkStatusButton'])) {
    if ($subaction == "calculate_cdk") {
        $_REQUEST['subaction'] = "preview_cdk_status";
        $_GET['subaction'] = "preview_cdk_status";
        $subaction = "preview_cdk_status";
    } else {
        tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id')) . '&subaction=show_cdk_list'));
    }
}

// Back button from preview CDK CB
if (isset($_REQUEST['BackBtnCB'])) {
    if ($subaction == "insert_cdk_product") {
        $_REQUEST['subaction'] = "add_cdk_cb";
        $_GET['subaction'] = "add_cdk_cb";
        $subaction = "add_cdk_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id')) . '&subaction=show_cdk_list'));
    }
}

// Submit button from preview DTU CB
if (isset($_REQUEST['submitCDKCB'])) {
    if ($subaction == "insert_cdk_product") {
        $_REQUEST['subaction'] = "insert_cdk_cb";
        $_GET['subaction'] = "insert_cdk_cb";
        $subaction = "insert_cdk_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id')) . '&subaction=show_cdk_list'));
    }
}

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case "preview_cdk":
            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_START_DATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier']) || (isset($_REQUEST['cdk_supplier']) && !tep_not_null($_REQUEST['cdk_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier_payment']) || (isset($_REQUEST['cdk_supplier_payment']) && !tep_not_null($_REQUEST['cdk_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['cdk_currency']) || (isset($_REQUEST['cdk_currency']) && !tep_not_null($_REQUEST['cdk_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_delivery_address']) || (isset($_REQUEST['cdk_delivery_address']) && !tep_not_null($_REQUEST['cdk_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['cdk_items_prod_id']) || (isset($_REQUEST['cdk_items_prod_id']) && count($_REQUEST['cdk_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PRODUCTS, 'Error');
                }
                
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($_REQUEST['cdk_supplier'], $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_cdk_product";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_cdk_product";
                    $subaction = "insert_cdk_product";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "calculate_cdk":
            if ($allow_create_cdk_permission) {                
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_START_DATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier']) || (isset($_REQUEST['cdk_supplier']) && !tep_not_null($_REQUEST['cdk_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_SUPPLIER, 'Error');
                }
                
                if (!isset($_REQUEST['cdk_delivery_address']) || (isset($_REQUEST['cdk_delivery_address']) && !tep_not_null($_REQUEST['cdk_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['cdk_currency']) || (isset($_REQUEST['cdk_currency']) && !tep_not_null($_REQUEST['cdk_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier_payment']) || (isset($_REQUEST['cdk_supplier_payment']) && !tep_not_null($_REQUEST['cdk_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['cdk_new_supplier_payment']) || (isset($_REQUEST['cdk_new_supplier_payment']) && !tep_not_null($_REQUEST['cdk_new_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }
                
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($_REQUEST['cdk_supplier'], $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_cdk";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_cdk";
                    $subaction = "insert_cdk";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            break;

        case "cdk_report":
            if ($_REQUEST['form_load']) {
                # code...
            } else {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_START_DATE, 'Error');
                }

                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_END_DATE, 'Error');
                }

                // if ($error) {
                //     $_REQUEST['subaction'] = "insert_cdk";
                //     $_REQUEST['preview_error'] = "1";
                //     $_GET['subaction'] = "insert_cdk";
                //     $subaction = "insert_cdk";
                // }
            }
            break;

        case "preview_cdk_status":
            if ($allow_create_cdk_permission) {             
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_START_DATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier']) || (isset($_REQUEST['cdk_supplier']) && !tep_not_null($_REQUEST['cdk_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier_payment']) || (isset($_REQUEST['cdk_supplier_payment']) && !tep_not_null($_REQUEST['cdk_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['cdk_currency']) || (isset($_REQUEST['cdk_currency']) && !tep_not_null($_REQUEST['cdk_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_delivery_address']) || (isset($_REQUEST['cdk_delivery_address']) && !tep_not_null($_REQUEST['cdk_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['cdk_items_id']) || (isset($_REQUEST['cdk_items_id']) && count($_REQUEST['cdk_items_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_CB_FORM_EMPTY_PRODUCTS, 'Error');
                }
                
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($_REQUEST['cdk_supplier'], $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_cdk";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_cdk";
                    $subaction = "insert_cdk";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "insert_cdk_product":
            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CDK_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['cdk_supplier']) || (isset($_REQUEST['cdk_supplier']) && !tep_not_null($_REQUEST['cdk_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['cdk_supplier_payment']) || (isset($_REQUEST['cdk_supplier_payment']) && !tep_not_null($_REQUEST['cdk_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['cdk_currency']) || (isset($_REQUEST['cdk_currency']) && !tep_not_null($_REQUEST['cdk_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['cdk_items_prod_id']) || (isset($_REQUEST['cdk_items_prod_id']) && count($_REQUEST['cdk_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_EMPTY_PRODUCTS, 'Error');
                }
                
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($_REQUEST['cdk_supplier'], $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_LOCKED_SUPPLIER, 'Error');
                }

            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }

            if (!$error) {
                $edit_cdk_obj->insert_new_po($_REQUEST, $messageStack);
            }
            
            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction', 'cdk_id'))));

            break;
            
        case 'approve_cdk':
            if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'PrintBtn') {
                ;
            } else {
                if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'CancelBtn') {
                    $edit_cdk_obj->cancel_cdk($_REQUEST, $messageStack);
                } else if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'ApproveBtn') {
                    $edit_cdk_obj->approve_cdk($_REQUEST, $messageStack);
                }
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            }
            break;

        case 'add_remark':
            if ($allow_add_cdk_remark_permission) {
                $edit_cdk_obj->cdk_add_remark($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_CDK_FORM_ADD_REMARK_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'refund_cdk':
            if (isset($_REQUEST['CompleteBtn'])) {
                $po_status = '2';
                $edit_cdk_obj->complete_cdk($_REQUEST, $messageStack, $po_status);
            }

            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'refund_cdk_cancel':
            if ($allow_cdk_cancel_pending_receive_permission) {
                $edit_cdk_obj->refund_unpaid($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_CDK_FORM_CDK_CANCEL_PENDING_RECEIVE_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'rollback':
            if (isset($_REQUEST['RollbackProcessBtn'])) {
                $edit_cdk_obj->rollback_complete_cdk($_REQUEST, $messageStack);
            }

            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'verify_po':
            if ($allow_verify_cdk_permission) {
                $edit_cdk_obj->verifying_po($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_PO_FORM_VERIFY_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'make_payment':
            if ($allow_cdk_make_payment_permission) {
                $edit_cdk_obj->make_cdk_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_CDK_FORM_CDK_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'debit_payment':
            if ($allow_cdk_make_payment_permission) {
                $edit_cdk_obj->debit_cdk_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_CDK_FORM_CDK_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk'));
            break;

        case 'edit_cdk':
            if (!$edit_cdk_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction'))));
            }
            break;

        case 'search_cdk':
            if (!$edit_cdk_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction'))));
            } else {
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_cdk&po_id=' . $edit_cdk_obj->po_info['po_id']));
            }
            break;
            
        case 'show_cdk_list':
            if (isset($_REQUEST['reset'])) {
                unset($_SESSION['po_search']);
                tep_redirect(tep_href_link(FILENAME_CDK_PAYMENT));
            }
            break;
            
        default:
            break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>general.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/modal_win.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/xmlhttp.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.tabs.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/ogm_jquery.js"></script>
        <? include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
        <? include_once (DIR_WS_INCLUDES . 'javascript/consignment_payment_xmlhttp.js.php'); ?>
        <script language="javascript">
            <!--
            var pageLoaded = false;
            function init() {
                // quit if this function has already been called
                if (arguments.callee.done)
                    return;

                // flag this function so we don't do the same thing twice
                arguments.callee.done = true;

                initInfoCaptions();
                pageLoaded = true;	// Control when a javascript event in this page can be triggered
            }
            ;

            /* for Mozilla */
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", init, null);
            }

            /* for other browsers */
            window.onload = init;
            //-->
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <div id="fancy_box" class="fancy_box" style="display:none;">
            <div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
            <div class="fancy_inner" style="display: block;">
                <div id="fancy_close" class="fancy_close" style="display: none;"></div>
                <div class="fancy_frame_bg">
                    <div class="fancy_bg fancy_bg_n"></div>
                    <div class="fancy_bg fancy_bg_ne"></div>
                    <div class="fancy_bg fancy_bg_e"></div>
                    <div class="fancy_bg fancy_bg_se"></div>
                    <div class="fancy_bg fancy_bg_s"></div>
                    <div class="fancy_bg fancy_bg_sw"></div>
                    <div class="fancy_bg fancy_bg_w"></div>
                    <div class="fancy_bg fancy_bg_nw"></div>
                </div>
                <div id="fancy_content" class="fancy_content"></div>
            </div>
        </div>
        <div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%">
                                <table border="0" width="100%" cellspacing="0" cellpadding="5">
                                    <tr>
                                        <td class="pageHeading" colspan="2">
                                            <?php 
                                                if (isset($_REQUEST['subaction']) || isset($_REQUEST['cdk_list_type'])) {
                                                    if ($_REQUEST['subaction'] == 'create_cdk' || 
                                                        $_REQUEST['subaction'] == 'create_blank_cdk' ||
                                                        $_REQUEST['subaction'] == 'add_cdk_cb') {
                                                        if($_REQUEST['cdk_list_type'] == 'add_cdk_cb') {
                                                            echo 'Consignment Report (Status)';
                                                        } else {
                                                            echo 'Consignment Report (Payment Request)';
                                                        }
                                                    } else if ($_REQUEST['subaction'] == 'calculate_cdk' || 
                                                                $_REQUEST['subaction'] == 'create_cdk_product') {
                                                        echo 'Consignment Report (Payment Request)';
                                                    } else if ($_REQUEST['subaction'] == 'preview_cdk') {
                                                        echo 'Consignment Report (Payment Request)';
                                                    } else if ($_REQUEST['subaction'] == 'edit_cdk') {
                                                        echo 'Consignment Report (Payment Request)';
                                                    } else if ($_REQUEST['subaction'] == 'cdk_report') {
                                                        echo 'Consignment Report';
                                                    } else {
                                                        echo 'Consignment Report (Payment Request List)';
                                                    }
                                                } else {
                                                    echo 'Consignment Report (Payment Request)';
                                                }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td valign="top">
                                            <?
                                            switch ($_REQUEST['subaction']) {
                                                case 'create_cdk_product':
                                                    echo $edit_cdk_obj->calculate_cdk_form($_REQUEST);
                                                    break;
                                                case 'create_cdk':
                                                    echo $edit_cdk_obj->show_items_cdk_form($_REQUEST);
                                                    break;
                                                case 'create_blank_cdk':
                                                    echo $edit_cdk_obj->show_cdk_form($_REQUEST);
                                                    break;
                                                case 'add_cdk_cb':
                                                    echo $edit_cdk_obj->show_cdk_form($_REQUEST);
                                                    break;
                                                case 'insert_cdk':
                                                    echo $edit_cdk_obj->show_cdk_form($_REQUEST);
                                                    break;
                                                case 'calculate_cdk':
                                                    echo $edit_cdk_obj->calculate_cdk_form($_REQUEST);
                                                    break;
                                                case 'preview_cdk':
                                                    echo $edit_cdk_obj->preview_cdk_form($_REQUEST);
                                                    break;
                                                case 'preview_cdk_status':
                                                    echo $edit_cdk_obj->preview_cdk_cb_form($_REQUEST);
                                                    break;
                                                case 'edit_cdk':
                                                    echo $edit_cdk_obj->show_edit_cdk_form($_REQUEST);
                                                    break;
                                                case 'show_cdk_list':
                                                    echo $edit_cdk_obj->show_cdk_list($_REQUEST);
                                                    break;
                                                case 'cdk_report':
                                                    echo $edit_cdk_obj->show_cdk_report($_REQUEST);
                                                    break;
                                                default :
                                                    echo $edit_cdk_obj->search_cdk_list($_REQUEST);
                                                    break;
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <!-- body_text_eof //-->
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- body_eof //-->
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <?php
        if ($_REQUEST['subaction'] == 'edit_cdk') {
            echo '<script type="text/javascript">';
            echo '	jQuery().ready(function() {';
            echo "		getCDKPaymentStatistic('" . $edit_cdk_obj->supplier['supplier_id'] . "');";
            echo '	});';
            echo '</script>';
        }
        ?>
    </body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>