<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml; charset=utf-8');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_CLASSES . 'po_suppliers.php');
include_once(DIR_WS_CLASSES . 'payment_module_info.php');
include_once(DIR_WS_CLASSES . 'consignment_payment.php');

$currencies = new currencies();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$subaction = isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '';
$language_id = isset($_REQUEST['lang']) ? (int) $_REQUEST['lang'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : 'english';

$results = '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CDK_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CDK_PAYMENT);
}

define('PARTIAL_RECEIVE_STATUS', 2);

$allow_set_dtu_status_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_PO_SET_PAID_STATUS');

if (tep_not_null($action)) {
    switch ($action) {
        case "popup_remark":
            echo "<popup_remark><result>OK</result></popup_remark>";
            break;

        case "perform_tagging":
            $order_status_id = (int) $_REQUEST['status_id'];
            $setting_value = $_REQUEST['setting'];
            $po_ids_array = explode(',', $_REQUEST['po_str']);
            $list_mode = (int) $_REQUEST['list_mode'];
            echo "<tag_info>";
            if ($subaction == 'nt') {
                if (tep_not_null($setting_value)) {
                    $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_CDK_PAYMENT . "';";
                    $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                    if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                        $po_tag_id = (int) $tag_verify_row["orders_tag_id"];
                    } else {
                        $insert_sql_data = array('orders_tag_name' => tep_db_prepare_input($setting_value),
                            'orders_tag_status_ids' => $order_status_id,
                            'filename' => FILENAME_CDK_PAYMENT
                        );
                        tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
                        $po_tag_id = tep_db_insert_id();
                    }

                    $assign_po_tag_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids = IF (purchase_orders_tag_ids='', '" . $po_tag_id . "', CONCAT_WS(',', purchase_orders_tag_ids, '" . $po_tag_id . "')) WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND NOT FIND_IN_SET('" . $po_tag_id . "', purchase_orders_tag_ids)";
                    tep_db_query($assign_po_tag_update_sql);

                    generateTagString($po_ids_array);
                }
            } else if ($subaction == 'at') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_CDK_PAYMENT . "';";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders with this tag
                    $assign_po_tag_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids = IF (purchase_orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', purchase_orders_tag_ids, '" . (int) $setting_value . "')) WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND NOT FIND_IN_SET('" . (int) $setting_value . "', purchase_orders_tag_ids)";
                    tep_db_query($assign_po_tag_update_sql);

                    generateTagString($po_ids_array);
                }
            } else if ($subaction == 'rt') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_CDK_PAYMENT . "';";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders by removing this tag from them
                    $unassign_po_tag_select_sql = "SELECT purchase_orders_id, purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND FIND_IN_SET('" . (int) $setting_value . "', purchase_orders_tag_ids)";
                    $unassign_po_tag_result_sql = tep_db_query($unassign_po_tag_select_sql);
                    while ($unassign_po_tag_row = tep_db_fetch_array($unassign_po_tag_result_sql)) {
                        $TagRemovePattern = "/(,)?" . (int) $setting_value . "(,)?/is";
                        $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_po_tag_row["purchase_orders_tag_ids"]);
                        if (substr($new_tag_string, 0, 1) == ',')
                            $new_tag_string = substr($new_tag_string, 1);
                        if (substr($new_tag_string, -1) == ',')
                            $new_tag_string = substr($new_tag_string, 0, -1);

                        tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . $unassign_po_tag_row["purchase_orders_id"] . "'");
                    }

                    generateTagString($po_ids_array);
                }
            }
            generateTagSelectionOptions($order_status_id, $_REQUEST['po_str'], ($list_mode == "2" ? true : false));
            echo "</tag_info>";
            break;

        case "get_supplier_info":
            $selected_cdk_payment = '';
            if (isset($_REQUEST['pay_id']) && $_REQUEST['pay_id'] != '') {
                $selected_cdk_payment = $_REQUEST['pay_id'];
            }

            $cdk_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $cdk_supplier_payment_info = $cdk_supplier->get_po_supplier_info($_REQUEST['s_id']);
            if (count($cdk_supplier_payment_info)) {
                // Get CDKey Supplier address Info
                $countries_info = tep_get_countries_info($cdk_supplier_payment_info['country_id'], 'countries_id');
                $address_str = tep_address_format($countries_info['address_format_id'], $cdk_supplier_payment_info, 1, '', '<br>', 'main');

                // Get CDKey Supplier's payment term info
                $cdk_pay_type = $cdk_supplier_payment_info['payment_type'];
                switch ($cdk_pay_type) {
                    case 'g':
                        $cdk_payment = TEXT_SUPPLIER_CONSIGNMENT;
                        break;
                }
                
                // Get CDKey Supplier's delivery address
                $selected_cdk_delivery = '';
                if (isset($_REQUEST['delivery_set']) && $_REQUEST['delivery_set'] != '') {
                    $selected_cdk_delivery = $_REQUEST['delivery_set'];
                } else {
                    $selected_cdk_delivery = $cdk_supplier_payment_info['po_supplier_company_code'];
                }

                $cdk_supplier_company = tep_get_po_company_list('cdk_delivery_address', $selected_cdk_delivery, 'id="cdk_delivery_address"', true);
                
                // Get CDKey Supplier's Currency
                $selected_cdk_currency = '';
                if (isset($_REQUEST['currency_set']) && $_REQUEST['currency_set'] != '') {
                    $selected_cdk_currency = $_REQUEST['currency_set'];
                } else {
                    $selected_cdk_currency = '';
                }
                $po_curr_array = $cdk_supplier->get_po_supplier_consignment_currency($_REQUEST['s_id']);
                
                echo "<result>";
                
                // Supplier Locked?
                $supplier_locked = $cdk_supplier->get_po_supplier_lock_status($_REQUEST['s_id'],$_SESSION['login_id']);
                if (is_null($supplier_locked['po_supplier_locked_by']) === true) {
                        echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                } else if ($supplier_locked['po_supplier_locked_by'] == $_SESSION['login_id']) {
                        echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                } else {
                        echo "<supplier_locked><![CDATA[]]></supplier_locked>";
                }

                echo "	<supplier_address><![CDATA[" . $address_str . "]]></supplier_address>";
                echo "	<supplier_payment><![CDATA[" . $cdk_payment . "]]></supplier_payment>";
                echo "	<supplier_delivery><![CDATA[" . $cdk_supplier_company . "]]></supplier_delivery>";
                echo "  <option_selection><![CDATA[" . tep_draw_pull_down_menu('cdk_currency', $po_curr_array, $selected_cdk_currency, 'id="cdk_currency" onChange="getPOPayMethodByCurrency(this);"') . "]]></option_selection>";
                echo "</result>";
            } else {
                echo "<result><supplier_address>Unknown supplier!</supplier_address></result>";
            }
            break;

        case 'get_supplier_summary':
            $supplier_id = (isset($_REQUEST['supp_id'])) ? $_REQUEST['supp_id'] : '';
            $curr_id = (isset($_REQUEST['curr_id'])) ? $_REQUEST['curr_id'] : '';
            $start_date = (isset($_REQUEST['s_date'])) ? $_REQUEST['s_date'] : '';
            $end_date = (isset($_REQUEST['e_date'])) ? $_REQUEST['e_date'] : '';
            $cdk_status = (isset($_REQUEST['cdk_status'])) ? $_REQUEST['cdk_status'] : '';

            if ($supplier_id && $curr_id && $start_date && $end_date && $cdk_status) {
                $po_products = array();
                $total_quantity = array();
                $subtotal_price = array();

                $html_res = "";

                $cdkey_status_date_qry = "";
                $custom_products_status_str = "";
                if ($cdk_status == 1) {
                    $cdkey_status_date_qry = "code_date_added";
                    // $custom_products_status_str = "AND cpc.status_id = 1 AND cpc.orders_products_id = 0";
                } else {
                    $cdkey_status_date_qry = "code_date_modified";
                    $custom_products_status_str = "AND cpc.orders_products_id > 0";
                }

                $summary_qry = "SELECT cpc.purchase_orders_id, cpc.products_id, COUNT(cpc.products_id) AS total
                                FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                                INNER JOIN " . TABLE_PURCHASE_ORDERS . " AS po
                                    ON cpc.purchase_orders_id = po.purchase_orders_id
                                    AND po.supplier_id = '" . tep_db_input($supplier_id) . "'
                                    AND po.currency = '" . tep_db_input($curr_id) . "'
                                    AND po.purchase_orders_type = '0'
                                    AND po.payment_type = 'g'
                                WHERE cpc." . $cdkey_status_date_qry . " >= '" . tep_db_input($start_date) . " 00:00:00'
                                    AND cpc." . $cdkey_status_date_qry . " <= '" . tep_db_input($end_date) . " 23:59:59'
                                    " . $custom_products_status_str . "
                                GROUP BY cpc.purchase_orders_id, cpc.products_id
                                ORDER BY cpc.purchase_orders_id, cpc.products_id";
                $summary_result = tep_db_query($summary_qry);
                while ($summary_row = tep_db_fetch_array($summary_result)) {
                    $poid = $summary_row['purchase_orders_id'];
                    $prodid = $summary_row['products_id'];
                    $total_poid = $summary_row['total'];

                    $up_qry = "SELECT products_name, products_unit_price_type, products_unit_price_value, products_unit_price
                                FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                WHERE purchase_orders_id = '" . tep_db_input($poid) . "'
                                    AND products_id = '" . tep_db_input($prodid) . "'
                    ";
                    $up_result = tep_db_query($up_qry);
                    $up_row = tep_db_fetch_array($up_result);

                    $products_name = $up_row['products_name'];
                    $unit_price = getProductSellPrice($prodid, $curr_id);
                    $round_unit_price = round($unit_price, 4);
                    $round_unit_price_value = round($up_row['products_unit_price_value'], 4);

                    if ($up_row['products_unit_price_type'] == '%') {
                        $final_unit_price = ($round_unit_price - ($round_unit_price * $round_unit_price_value / 100));
                    } else {
                        $final_unit_price = ($round_unit_price_value);
                    }

                    $po_products[$poid.$prodid]['purchase_orders_id'] = $summary_row['purchase_orders_id'];
                    $po_products[$poid.$prodid]['products_id'] = $summary_row['products_id'];
                    $po_products[$poid.$prodid]['products_name'] = $products_name;
                    $total_quantity[$poid.$prodid] = $total_quantity[$poid.$prodid] + (int) $total_poid;
                    $po_products[$poid.$prodid]['quantity'] = $total_quantity[$poid.$prodid];
                    $po_products[$poid.$prodid]['unit_price'] = $round_unit_price;
                    $po_products[$poid.$prodid]['discount'] = $up_row['products_unit_price_type'] . ' ' . $round_unit_price_value;
                    $subtotal_price[$poid.$prodid] = $total_quantity[$poid.$prodid] * round($final_unit_price, 4);
                    $po_products[$poid.$prodid]['subtotal'] = $subtotal_price[$poid.$prodid];
                }

                // Start Table
                $html_res = '';
                
                // START Table Header
                $html_res .= '<table id="supplier_details_info" border="0" width="100%" cellspacing="1" cellpadding="4">';
                $html_res .= '  <tbody>';
                $html_res .= '      <tr>';
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="15%">' . TABLE_HEADING_REPORT_PO_NUMBER . '</td>';
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="15%">' . TABLE_HEADING_REPORT_PRODUCT_ID . "</td>";
                $html_res .= '          <td class="invoiceBoxHeading">' . TABLE_HEADING_REPORT_PRODUCT_NAME . "</td>";
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . (($cdk_status==1) ? TABLE_HEADING_REPORT_QUANTITY : TABLE_HEADING_REPORT_SOLD_QUANTITY) . "</td>";
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_REPORT_UNIT_PRICE . '</td>';
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_REPORT_DISCOUNT . "</td>";
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_REPORT_SUBTOTAL . "</td>";
                $html_res .= '      </tr>';

                if ($po_products) {
                    $row_count = 0;
                    foreach ($po_products as $po_data) {
                        $row_style = ($row_count%2) ? 'reportListingOdd' : 'reportListingEven' ;

                        $html_res .= '  <tr class="'.$row_style.'">';
                        $html_res .= '      <td class="main" align="center">' . $po_data['purchase_orders_id'] . '</td>';
                        $html_res .= '      <td class="main" align="center">' . $po_data['products_id'] . '</td>';
                        $html_res .= '      <td class="main" align="center">' . $po_data['products_name'] . '</td>';
                        $html_res .= '      <td class="main" align="center">' . $po_data['quantity'] . '</td>';
                        $html_res .= '      <td class="main" align="center">' . number_format($po_data['unit_price'], 4, $currencies->currencies[$curr_id]['decimal_point'], $currencies->currencies[$curr_id]['thousands_point']) . '</td>';
                        $html_res .= '      <td class="main" align="center">' . $po_data['discount'] . '</td>';
                        $html_res .= '      <td class="main" align="center">' . number_format($po_data['subtotal'], 4, $currencies->currencies[$curr_id]['decimal_point'], $currencies->currencies[$curr_id]['thousands_point']) . '</td>';
                        $html_res .= '  </tr>';

                        $row_count++;
                    }
                }

                // CLOSE table
                $html_res .= '  </tbody>';
                $html_res .= '</table>';

            } else {
                $html_res = "Error: No data";
            }

            echo "<result>";
            echo "  <supplier_summary><![CDATA[" . $html_res . "]]></supplier_summary>";
            echo "</result>";
            break;
        
        case "get_po_supplier_pay_method" :
            // Get CDKey Supplier's Disbursement Method
            $selected_cdk_pm = '';
            if (isset($_REQUEST['pm_set']) && $_REQUEST['pm_set'] != '') {
                $selected_cdk_pm = $_REQUEST['pm_set'];
            } else {
                $selected_cdk_pm = '';
            }
            
            $disbursement_array = array();
            $pm_array = array();
            $cdk_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $po_pm_array = $cdk_supplier->get_po_supplier_disburse_method($_REQUEST['s_id'], $_REQUEST['c_id']);
            for ($i=0, $n=sizeof($po_pm_array); $i<$n; $i++) {
                $disbursement_array[$po_pm_array[$i]['id']] = array('id' => $po_pm_array[$i]['id'], 'text' => $po_pm_array[$i]['text']);
            }
            foreach ($disbursement_array as $disbursement) {
                $pm_array[] = $disbursement;
            }
            
            echo "  <option_selection><![CDATA[" . tep_draw_pull_down_menu('cdk_supplier_payment', $pm_array, $selected_cdk_pm, 'id="cdk_supplier_payment" onChange="getPOPayForPO(this, \'\', \'\', false);"') . "]]></option_selection>";
            break;

        case "get_all_disbursement_methods":
            // Get CDKey Supplier's disbursement methods
            $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $_REQUEST['s_id'], '3');
            $pm_array = $pm_obj->get_existing_payment_account_selection();
            // Get current selected disbursement
            $selected_po_payment = '';
            if (isset($_REQUEST['dis_method']) && $_REQUEST['dis_method'] != '') {
                $store_payment_account_book_id = $pm_obj->get_payment_account_book_id($_REQUEST['dis_method'], $_REQUEST['curr_id']);
                $selected_po_payment = $store_payment_account_book_id;
            }

            if (isset($_REQUEST['reload_dis']) && !empty($_REQUEST['reload_dis'])) {
                $selected_po_payment = $_REQUEST['reload_dis'];
            }

            $po_disburse = '<select name="cdk_new_supplier_payment" id="cdk_new_supplier_payment" onChange="getNewCurrencyRate(document.cdk_form.cdk_currency, \'suggest_rate\', \'confirm_rate\');"><option value="">'.PULL_DOWN_DEFAULT.'</option>';
            for ($i=0, $n=sizeof($pm_array); $i<$n; $i++) {
                $selected = '';
                if (tep_not_null($selected_po_payment) && $selected_po_payment == $pm_array[$i]['id']) {
                    $selected = ' selected="selected" ';
                }
                $po_disburse .= '<option value="'.$pm_array[$i]['id'].'"'.$selected.'>'.$pm_array[$i]['text'].'</option>';
            }
            $po_disburse .= '</select>' . TEXT_FIELD_REQUIRED;

            echo "<result>";
            echo "  <supplier_disbursement><![CDATA[".$po_disburse."]]></supplier_disbursement>";
            echo "</result>";
            break;
            
        case "lock_po_supplier" :
            $cdk_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $locked_by = $cdk_supplier->get_po_supplier_lock_validation($_REQUEST['p_id'],$_SESSION['login_id']);
            
            echo "<result>";
            
            if ($locked_by == $_SESSION['login_id'] || empty($locked_by)) {
                $locked_result = $cdk_supplier->lock_po_supplier($_REQUEST['p_id'],$_SESSION['login_id']);
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked]]></action_result>";
            } else {
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked by other user]]></action_result>";
            }
            
            echo "</result>";
            
            break;

        case "unlock_po_supplier" :
            $cdk_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $locked_by = $cdk_supplier->get_po_supplier_lock_validation($_REQUEST['p_id'], $_SESSION['login_id']);

            echo "<result>";
            
            if ($locked_by == $_SESSION['login_id'] || empty($locked_by)) {
                $locked_result = $cdk_supplier->unlock_po_supplier($_REQUEST['p_id']);
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
            echo "<action_result><![CDATA[This CDK supplier is unlocked]]></action_result>";
            } else {
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked by other user]]></action_result>";
            }

            echo "</result>";
            
            break;

        case "get_company_gst":
            $comp_code = isset($_REQUEST['comp_code']) ? tep_db_prepare_input($_REQUEST['comp_code']) : '';
            $total_amt = isset($_REQUEST['total_amt']) ? tep_db_prepare_input($_REQUEST['total_amt']) : '';
            $po_currency = isset($_REQUEST['dtu_curr']) ? tep_db_prepare_input($_REQUEST['dtu_curr']) : '';
            $include_gst = isset($_REQUEST['incl_gst']) ? tep_db_prepare_input($_REQUEST['incl_gst']) : 0;

            $gst_value = $tax_amt = 0;
            $taxable_total = $total_amt;
            $gst_title = sprintf(TABLE_HEADING_BOTTOM_GST, consignment_payment::monetary_decimal_format($gst_value, '%01.1f')) . ':';

            if (tep_not_null($comp_code) && tep_not_null($total_amt) && $total_amt > 0) {
                $dtu_comp_select_sql = "SELECT po_company_gst_percentage FROM " . TABLE_PO_COMPANY . " WHERE po_company_code='" . tep_db_input($comp_code) . "'";
                $dtu_comp_result_sql = tep_db_query($dtu_comp_select_sql);
                if ($dtu_comp_row = tep_db_fetch_array($dtu_comp_result_sql)) {
                    $gst_value = $dtu_comp_row['po_company_gst_percentage'];
                    $gst_title = sprintf(TABLE_HEADING_BOTTOM_GST, consignment_payment::monetary_decimal_format($gst_value, '%01.1f')) . ':';
                    $tax_amt = round(($total_amt * ($gst_value / 100)), 4);
                    $taxable_total = $total_amt + $tax_amt;
                }
            }

            if (tep_not_null($include_gst) && $include_gst == 0) {
                $tax_amt = 0;
                $taxable_total = $total_amt;
            }

            echo "<result>";
            echo "	<gst_value><![CDATA[" . $gst_value . "]]></gst_value>";
            echo "	<gst_title_text><![CDATA[" . $gst_title . "]]></gst_title_text>";
            echo "	<tax_amount><![CDATA[" . $tax_amt . "]]></tax_amount>";
            echo "	<after_tax_total><![CDATA[" . $taxable_total . "]]></after_tax_total>";
            echo "</result>";
            break;

        case "get_currency_rate":
            $curr_code = isset($_REQUEST['curr_code']) ? $_REQUEST['curr_code'] : '';
            $pm_id = isset($_REQUEST['pm_id']) ? $_REQUEST['pm_id'] : '';
            $supp_id = isset($_REQUEST['s_id']) ? $_REQUEST['s_id'] : '';
            
            $rate_str = '0.*********';
            $dest_curr = DEFAULT_CURRENCY;
            $po_credit_note_amt = 0;

            if (tep_not_null($curr_code) && tep_not_null($pm_id) && tep_not_null($supp_id)) {
                $pm_select_sql = "  SELECT pab.store_payment_account_book_id, pm.payment_methods_send_currency
                                    FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                            ON pab.payment_methods_id=pm.payment_methods_id
                                    WHERE pab.payment_methods_id = '" . tep_db_input($pm_id) . "'
                                        AND pab.user_id = '" . tep_db_input($supp_id) . "'
                                        AND pab.user_role = 'customers'
                                    LIMIT 1
                        ";
                $pm_result_sql = tep_db_query($pm_select_sql);
                if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
                    $dest_curr = $currencies->get_code_by_id($pm_row['payment_methods_send_currency']);

                    // Use Spot Rate
                    $curr_data = $currencies->currencies[$curr_code];
                    if ($curr_data['value'] > 0) {
                        $rate_str = round((1 / $curr_data['value']), 8);
                    }

                    if ($dest_curr != DEFAULT_CURRENCY) {
                        $dest_data = $currencies->currencies[$dest_curr];
                        if ($dest_data['value'] > 0) {
                            $rate_str = round(($rate_str * $dest_data['value']), 8);
                        }
                    }
                }
            }

            echo "<result>";
            echo "	<payment_currency><![CDATA[" . $dest_curr . "]]></payment_currency>";
            echo "	<currency_rate><![CDATA[" . $rate_str . "]]></currency_rate>";
            echo "	<sell_price_title><![CDATA[" . sprintf(TABLE_HEADING_CDK_PO_AMOUNT, $curr_code) . "]]></sell_price_title>";
            echo "	<total_price_title><![CDATA[" . sprintf(TABLE_HEADING_CDK_AMOUNT, $curr_code) . "]]></total_price_title>";
            echo "</result>";
            break;

        case "get_new_currency_rate":
            $curr_code = isset($_REQUEST['curr_code']) ? $_REQUEST['curr_code'] : '';
            $pm_id = isset($_REQUEST['pm_id']) ? $_REQUEST['pm_id'] : '';
            $supp_id = isset($_REQUEST['s_id']) ? $_REQUEST['s_id'] : '';
            
            $rate_str = '0.*********';
            $dest_curr = DEFAULT_CURRENCY;
            $po_credit_note_amt = 0;

            $customerIdStatus = false;

            if (tep_not_null($curr_code) && tep_not_null($pm_id) && tep_not_null($supp_id)) {
                $pm_select_sql = "SELECT pab.store_payment_account_book_id, pm.payment_methods_send_currency
                                    FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                        ON pab.payment_methods_id=pm.payment_methods_id
                                    WHERE pab.store_payment_account_book_id = '" . tep_db_input($pm_id) . "'";
                $pm_result_sql = tep_db_query($pm_select_sql);
                if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
                    // checking for disbursement method NRSC
                    if ($pm_row['payment_methods_send_currency'] < 1) {
                        // get user_id (customer)
                        $supplier_account_sql = "SELECT spabd.payment_methods_fields_value
                                                FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd
                                                INNER JOIN payment_methods_fields AS pmf
                                                    ON spabd.payment_methods_fields_id = pmf.payment_methods_fields_id 
                                                    AND pmf.payment_methods_fields_title = 'Customer ID'
                                                WHERE spabd.store_payment_account_book_id = '" . tep_db_input($pm_row['store_payment_account_book_id']) . "'";
                        $supplier_account_result = tep_db_query($supplier_account_sql);
                        if ($supplier_account = tep_db_fetch_array($supplier_account_result)) {
                            if (is_numeric($supplier_account['payment_methods_fields_value'])) {
                                $scArrayResult = ms_store_credit::getScBalance($supplier_account['payment_methods_fields_value']);
                                if ($scArrayResult) {
                                    $customerIdStatus = true;
                                    $dest_curr = $scArrayResult['currency'];
                                }
                            }
                        }

                        if (!$customerIdStatus) {
                            $dest_curr = $curr_code;
                        }
                    } else {
                        $dest_curr = $currencies->get_code_by_id($pm_row['payment_methods_send_currency']);
                    }

                    // Use Spot Rate
                    $curr_data = $currencies->currencies[$curr_code];
                    if ($curr_data['value'] > 0) {
                        $rate_str = round((1 / $curr_data['value']), 8);
                    }

                    if ($dest_curr != DEFAULT_CURRENCY) {
                        $dest_data = $currencies->currencies[$dest_curr];
                        if ($dest_data['value'] > 0) {
                            $rate_str = round(($rate_str * $dest_data['value']), 8);
                        }
                    }
                }
            }

            echo "<result>";
            echo "  <payment_currency><![CDATA[" . $dest_curr . "]]></payment_currency>";
            echo "  <currency_rate><![CDATA[" . $rate_str . "]]></currency_rate>";
            echo "  <sell_price_title><![CDATA[" . sprintf(TABLE_HEADING_CDK_PO_AMOUNT, $curr_code) . "]]></sell_price_title>";
            echo "  <total_price_title><![CDATA[" . sprintf(TABLE_HEADING_CDK_AMOUNT, $curr_code) . "]]></total_price_title>";
            echo "</result>";
            break;

        case "get_product_sell_price":
            $products_id = isset($_REQUEST['p_id']) ? $_REQUEST['p_id'] : '';
            $sell_curr = isset($_REQUEST['sell_curr']) ? $_REQUEST['sell_curr'] : '';
            $selling_price = 0;

            if (tep_not_null($products_id) && tep_not_null($sell_curr)) {
                $selling_price = getProductSellPrice($products_id, $sell_curr);
            }

            echo "<result>";
            echo "	<product_sell_price><![CDATA[" . $selling_price . "]]></product_sell_price>";
            echo "</result>";
            break;

        case "show_lock_btn":
            $log_object = new log_files($_SESSION['login_id']);
            $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
            if ($subaction == "ul" || $subaction == "ulo") { // unlocking
                $lock_orders_select_sql = "	SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po 
											LEFT JOIN " . TABLE_ADMIN . " AS a 
												ON (po.purchase_orders_locked_by = a.admin_id) 
											WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                if ($lock_orders_row["purchase_orders_locked_by"] == $_REQUEST['adm']) {
                    $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                    tep_db_query($unlock_orders_update_sql);
                    echo '	<po_lock>
							<result>This order has been successfully unlocked!</result>
							<action>Show Lock Button</action>
							<time>' . date("Y-m-d H:i:s") . '</time>
							<lock_msg><![CDATA[' . TEXT_ORDER_NOT_BEEN_LOCKED . ']]></lock_msg>
							</po_lock>';

                    $log_object->insert_orders_log($_REQUEST['oid'], ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_CDK_PAYMENT);
                } else {
                    if (tep_not_null($lock_orders_row["purchase_orders_locked_by"])) { // locked by other people
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($_SESSION['login_groups_id'], $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            if ($subaction == "ulo") {
                                $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                                tep_db_query($unlock_orders_update_sql);
                                echo "<po_lock><result>This order has been successfully unlocked!" . tep_db_prepare_input($_REQUEST['log_comment']) . "</result>";
                                echo "<action>Show Lock Button</action>";
                                echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                                echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg></po_lock>";

                                $log_object->insert_orders_log($_REQUEST['oid'], sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], tep_db_prepare_input($_REQUEST['log_comment'])), FILENAME_CDK_PAYMENT);
                            } else {
                                echo "<po_lock><result>This order is locked by someone else!</result>";
                                echo "<action>Show Unlock Button</action>";
                                echo "<subaction>Prompt For Unlocking Msg</subaction>";
                                echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "]]></lock_msg></po_lock>";
                            }
                        } else {
                            echo "<po_lock><result>Unlock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"], $contact_admin_group_msg) . "]]></lock_msg></po_lock>";
                        }
                    } else { // nobody lock this order
                        echo "<po_lock><result>You are not locking this order!</result>";
                        echo "<action>Show Lock Button</action>";
                        echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                        echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg></po_lock>";
                    }
                }
            } else if ($subaction == "l") {  // locking
                $lock_orders_select_sql = "	SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po 
											LEFT JOIN " . TABLE_ADMIN . " 
												AS a ON (po.purchase_orders_locked_by = a.admin_id) 
											WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                if (tep_not_null($lock_orders_row["purchase_orders_locked_by"])) { // this order currently is locked
                    if ($lock_orders_row["purchase_orders_locked_by"] == $_REQUEST['adm']) {
                        echo "<po_lock><result>You had been locking this order!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<close_win>1</close_win>";
                        echo "<lock_msg><![CDATA[" . TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER . "]]></lock_msg></po_lock>";
                    } else {
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($_SESSION['login_groups_id'], $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            echo "<po_lock><result>Lock order is failed!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<subaction>Prompt For Unlocking Msg</subaction>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "]]></lock_msg></po_lock>";
                        } else {
                            echo "<po_lock><result>Lock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"], $contact_admin_group_msg) . "]]></lock_msg></po_lock>";
                        }
                    }
                } else {
                    // check order status
                    $order_status_select_sql = "SELECT purchase_orders_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                    $order_status_result_sql = tep_db_query($order_status_select_sql);
                    $order_status_row = tep_db_fetch_array($order_status_result_sql);

                    if ($order_status_row["purchase_orders_status"] != PARTIAL_RECEIVE_STATUS) {
                        echo "<po_lock><result>This order cannot be locked!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<lock_msg><![CDATA[" . TEXT_LOCKED_ORDER_NOT_VALID_STATUS . "]]></lock_msg></po_lock>";
                    } else {
                        $from_time = $_REQUEST['from_time'];
                        $check_within_locking_select_sql = "	SELECT orders_log_id 
																FROM " . TABLE_ORDERS_LOG_TABLE . " 
																WHERE orders_log_orders_id = '" . $_REQUEST['oid'] . "' 
																	AND orders_log_time >= '" . $from_time . "' 
																	AND orders_log_admin_id <> '" . $_REQUEST['adm'] . "' 
																	AND orders_log_filename = '" . tep_db_input(FILENAME_CDK_PAYMENT) . "'";
                        $check_within_locking_result_sql = tep_db_query($check_within_locking_select_sql);
                        if (tep_db_num_rows($check_within_locking_result_sql)) { // someone lock and unlock before you manage to lock it.
                            echo "<po_lock><result>This order has been updated by someone!</result>";
                            echo "<action>Prompt Alert Message</action>";
                            echo "<lock_msg><![CDATA[" . TEXT_LOCKED_OUTDATED_ORDER . "]]></lock_msg></po_lock>";
                        } else {
                            $lock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = '" . $_REQUEST['adm'] . "', purchase_orders_locked_from_ip='" . tep_get_ip_address() . "', purchase_orders_locked_datetime=now() WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND (purchase_orders_locked_by IS NULL OR purchase_orders_locked_by='')";
                            tep_db_query($lock_orders_update_sql);

                            $lock_orders_select_sql = "SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address FROM " . TABLE_PURCHASE_ORDERS . " AS po inner join " . TABLE_ADMIN . " AS a ON po.purchase_orders_locked_by=a.admin_id WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                            $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                            echo "<po_lock><result>This order has been successfully locked!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<lock_msg>" . sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "</lock_msg></po_lock>";

                            $log_object->insert_orders_log($_REQUEST['oid'], ORDERS_LOG_LOCK_ORDER, FILENAME_CDK_PAYMENT);
                        }
                    }
                }
            } else {
                echo "<po_lock><result>Unknown request!</result></po_lock>";
            }
            break;

        case "get_order_locking_history":
            $LockingCommentPattern = "/(?:##)(\S+)(?:##)(.*)/is";

            $locking_history_select_sql = "SELECT * FROM " . TABLE_ORDERS_LOG_TABLE . " WHERE orders_log_orders_id = '" . $_REQUEST['oid'] . "' AND orders_log_filename ='" . tep_db_input(FILENAME_CDK_PAYMENT) . "' ORDER BY orders_log_time";
            $locking_history_result_sql = tep_db_query($locking_history_select_sql);

            $history_text = '<table border="1" cellspacing="0" cellpadding="5">
      							<tr>
            						<td class="smallText" align="center"><b>Action Date</b></td>
            						<td class="smallText" align="center"><b>Comments</b></td>
            						<td class="smallText" align="center"><b>Admin</b></td>
            						<td class="smallText" align="center"><b>IP</b></td>
          						</tr>';
            $history_count = 0;
            while ($locking_history_row = tep_db_fetch_array($locking_history_result_sql)) {
                $locking_comment_msg = '';
                if (preg_match($LockingCommentPattern, $locking_history_row["orders_log_system_messages"], $regs)) {
                    switch ($regs[1]) {
                        case "l_1":
                            $locking_comment_msg = "Locking order";
                            break;
                        case "ul_1":
                            $locking_comment_msg = "Unlocking order";
                            break;
                        case "ul_2":
                            $other_admin_email = $user_msg = '';
                            if (tep_not_null($regs[2])) {
                                list($other_admin_email, $user_msg) = explode(':~:', $regs[2]);
                            }

                            $locking_comment_msg = "Unlocking other people " . (tep_not_null($other_admin_email) ? "(" . $other_admin_email . ")" : '') . " order" . (tep_not_null($user_msg) ? '<br>' . $user_msg : '');
                            break;
                    }
                }

                if (is_numeric($locking_history_row["orders_log_admin_id"])) {
                    $admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $locking_history_row["orders_log_admin_id"] . "'");
                    if ($admin_info = tep_db_fetch_array($admin_query))
                        $admin_email_address = $admin_info["admin_email_address"];
                    else
                        $admin_email_address = $locking_history_row["orders_log_admin_id"] == 0 ? 'System' : $locking_history_row["orders_log_admin_id"];
                } else {
                    $admin_email_address = $locking_history_row["orders_log_admin_id"];
                }
                $history_text .= '	<tr>
  										<td class="smallText" align="center">' . $locking_history_row['orders_log_time'] . '</td>
  										<td class="smallText">' . $locking_comment_msg . '</td>
  										<td class="smallText">' . $admin_email_address . '</td>
  										<td class="smallText">' . $locking_history_row['orders_log_ip'] . '</td>
  									</tr>';
                $history_count++;
            }

            if ($history_count == 0) {
                $history_text .= '	<tr>
										<td class="smallText" colspan="4">No Order Locking History Available</td>
									</tr>';
            }

            $history_text .= '</table>';
            echo "<locking_history>";
            echo "	<res_code>1</res_code>";
            echo "	<result><![CDATA[" . $history_text . "]]></result>";
            echo "</locking_history>";
            break;

        case 'get_cdk_payment_statistic':
            $stat_html = '<tr>
                            <td width="20%" class="main" valign="top">
                                <b>' . ENTRY_CDK_FORM_CDK_STATISTICS . ':</b><br><a href="' . tep_href_link(FILENAME_CDK_PAYMENT, 'suppID=' . $_REQUEST['suppID'] . '&subaction=show_cdk_list', 'NONSSL') . '" target="_blank">' . ENTRY_CDK_FORM_CDK_HISTORY . '</a>
                            </td>
                            <td class="main" valign="top">
                                <table border="1" width="100%" align="left" cellspacing="0" cellpadding="5">';

            $stat_po_status_array = array(
                '1' => 'Pending',
                '2' => 'Processing',
                '3' => 'Completed',
                '4' => 'Cancel'
            );

            if (count($stat_po_status_array)) {
                $stat_html .= '<tr>
                                <td class="main">' . STAT_TABLE_HEADING_CDK_STATUS . '</td>
                                <td class="main" align="center">' . STAT_TABLE_HEADING_TOTAL_CDK . '</td>
                                <td class="main" align="right">' . sprintf(STAT_TABLE_HEADING_TOTAL_AMOUNT, $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'] . $currencies->currencies[DEFAULT_CURRENCY]['symbol_right']) . '</td>
                            </tr>';

                foreach ($stat_po_status_array as $status_id => $status_name) {
                    if ((int) $status_id < 1) {
                        continue;
                    }

                    $po_1day_count = $po_1day_total = 0;
                    $count_po_1day_select_sql = "SELECT po.purchase_orders_id 
                                                FROM " . TABLE_PURCHASE_ORDERS . " AS po
                                                WHERE 
                                                    po.supplier_id = '" . (int) $_REQUEST['suppID'] . "'
                                                    AND po.purchase_orders_type = '3'
                                                    AND po.purchase_orders_status = '" . (int) $status_id . "'
                                                    AND po.last_modified >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))) . "'
                                                    AND po.last_modified <= '" . date("Y-m-d H:i:s", mktime(date('H'), date('i'), 0, date('m'), date('d'), date('Y'))) . "'";
                    $count_po_1day_result_sql = tep_db_query($count_po_1day_select_sql);
                    while ($count_po_1day_row = tep_db_fetch_array($count_po_1day_result_sql)) {
                        $po_1day_count++;

                        $count_po_1day_total_select_sql = "SELECT usd_value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $count_po_1day_row['purchase_orders_id'] . "' AND class='po_subtotal'";
                        $count_po_1day_total_result_sql = tep_db_query($count_po_1day_total_select_sql);
                        if ($count_po_1day_total_row = tep_db_fetch_array($count_po_1day_total_result_sql)) {
                            $po_1day_total += $count_po_1day_total_row['usd_value'];
                        }
                    }

                    $po_count = $po_total = 0;
                    $count_po_select_sql = "SELECT po.purchase_orders_id 
                                            FROM " . TABLE_PURCHASE_ORDERS . " AS po
                                            WHERE po.supplier_id = '" . (int) $_REQUEST['suppID'] . "'
                                                AND po.purchase_orders_type = '3'
                                                AND po.purchase_orders_status = '" . (int) $status_id . "'";
                    $count_po_result_sql = tep_db_query($count_po_select_sql);
                    while ($count_po_row = tep_db_fetch_array($count_po_result_sql)) {
                        $po_count++;

                        $count_po_total_select_sql = "SELECT usd_value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $count_po_row['purchase_orders_id'] . "' AND class='po_subtotal'";
                        $count_po_total_result_sql = tep_db_query($count_po_total_select_sql);
                        if ($count_po_total_row = tep_db_fetch_array($count_po_total_result_sql)) {
                            $po_total += $count_po_total_row['usd_value'];
                        }
                    }

                    $order_stat_style = '';

                    $stat_html .= '<tr>
                                    <td class="main" valign="top" colspan="3"><b>' . $status_name . '</b></td>
                                </tr>';

                    if ($status_id != 1) {
                        $stat_html .= '<tr>
                                        <td class="main" valign="top" nowrap>' . STAT_TABLE_ENTRY_CDK_1_DAY . '</td>
                                        <td class="main" align="center" valign="top" nowrap>' . ($po_1day_count > 0 ? $po_1day_count : '0') . '</td>
                                        <td class="main" align="right" valign="top" nowrap>' . ($po_1day_total > 0 ? $currencies->format($po_1day_total) : '0.00') . '</td>
                                    </tr>';
                    }

                    $stat_html .= '<tr>
                                    <td class="main" valign="top" nowrap>' . STAT_TABLE_ENTRY_TOTAL_CDK . '</span></td>
                                    <td class="main" align="center" valign="top" nowrap>' . ($po_count > 0 ? $po_count : '0') . '</td>
                                    <td class="main" align="right" valign="top" nowrap>' . ($po_total > 0 ? $currencies->format($po_total) : '0.00') . '</td>
                                </tr>';
                }
            }

            echo '<stat_result>
                    <stat><![CDATA[' . $stat_html . ']]></stat>
                </stat_result>';
            break;

        case "set_po_remark":
            $order_comment_style_array = array("orderCommentSystem", "orderCommentCrew", "orderCommentDelivery");

            $status_history_id = (int) $_REQUEST['shid'];

            $po_history_checking_sql = "SELECT purchase_orders_status_history_id FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND purchase_orders_status_history_id = '" . $status_history_id . "' LIMIT 1";
            $po_history_result_sql = tep_db_query($po_history_checking_sql);

            if (tep_db_num_rows($po_history_result_sql) > 0) {
                tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " SET set_as_po_remarks=0 WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'");
                tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " SET set_as_po_remarks=1 WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND purchase_orders_status_history_id = '" . $status_history_id . "'");

                $po_history_select_sql = "	SELECT osh.*, os.purchase_orders_status_name 
												FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " AS osh 
												LEFT JOIN " . TABLE_PURCHASE_ORDERS_STATUS . " AS os 
													ON (osh.purchase_orders_status_id = os.purchase_orders_status_id) 
												WHERE osh.purchase_orders_id = '" . (int) $_REQUEST['oid'] . "' AND (os.language_id = '" . $languages_id . "' OR os.language_id IS NULL) 
												ORDER BY osh.date_added";
                $po_history_result_sql = tep_db_query($po_history_select_sql);

                $history_text = '<table border="1" cellspacing="0" cellpadding="5">
          							<tr>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_DATE_ADDED . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_STATUS . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_COMMENTS . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_MODIFIED_BY . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_ACTION . '</b></td>
	          						</tr>';
                $oc_cnt = 0;
                while ($po_history_row = tep_db_fetch_array($po_history_result_sql)) {
                    $comment_row_style = $po_history_row["set_as_po_remarks"] == 1 ? 'class="orderRemarkSelectedRow"' : 'class="' . $order_comment_style_array[$po_history_row["comments_type"]] . '"';

                    $formatted_date_comment_added = tep_datetime_short($po_history_row["date_added"]);

                    $history_text .= '	<tbody id="o_comment_' . $po_history_row["comments_type"] . '_' . $po_history_row["purchase_orders_status_history_id"] . '">' . "\n" .
                            '	<tr ' . $comment_row_style . '>' . "\n" .
                            '		<td class="smallText" align="center">' . (tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--') . '</td>' . "\n";
                    $history_text .= ' 		<td class="smallText">' . (tep_not_null($po_history_row['purchase_orders_status_name']) ? $po_history_row['purchase_orders_status_name'] : '--') . '</td>
											<td class="smallText">' . nl2br(str_replace("\t", '&nbsp;&nbsp;&nbsp;&nbsp;', $po_history_row['comments'])) . '</td>
											<td class="smallText">' . nl2br(tep_db_output($po_history_row['changed_by'])) . '</td>
											<td class="smallText"><div id="set_remark_' . $po_history_row["purchase_orders_status_history_id"] . '">' . ($po_history_row['set_as_po_remarks'] == 1 ? '&nbsp;' : '<a href="javascript:;" onClick="setAsPORemark(\'' . $po_history_row["purchase_orders_status_history_id"] . '\', \'' . $po_history_row["purchase_orders_id"] . '\', \'' . $languages_id . '\', \'order_comment_history_box\');"><u>' . TEXT_SET_AS_REMARK . '</u></a>') . '</div></td>
										</tr>
									</tbody>';
                    $history_text .= tep_draw_hidden_field('hidden_o_comment_' . $po_history_row["comments_type"] . '_' . $po_history_row['purchase_orders_status_history_id'], '');
                    $oc_cnt++;
                }

                $history_text .= '<tr><td class="smallText" colspan="6">';
                $history_text .= tep_draw_separator('pixel_trans.gif', '1', '30');
                $history_text .= tep_submit_button(BUTTON_ADD_REMARK, ALT_BUTTON_ADD_REMARK, 'name="AddRemarkBtn" onClick="get_crew_remark();"', 'inputButton');
                $history_text .= '</td></tr>';

                $history_text .= '</table>';
                echo "<set_po_remark>";
                echo "	<res_code>1</res_code>";
                echo "	<result><![CDATA[" . $history_text . "]]></result>";
                echo "</set_po_remark>";
            } else {
                echo "<set_po_remark>";
                echo "	<res_code>0</res_code>";
                echo "	<result>Record not found!</result>";
                echo "</set_po_remark>";
            }
            break;

        case "get_po_info":
            if (tep_not_null($_REQUEST['o_str'])) {

                $order_ids_array = explode(',', $_REQUEST['o_str']);
                $po_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
                echo "<order_info>";
                for ($po_cnt = 0; $po_cnt < count($order_ids_array); $po_cnt++) {
                    // Instantiate order object
                    if ($po_obj->load_po(array('po_id' => $order_ids_array[$po_cnt]), $messageStack)) {
                        $po_remark_select_sql = "SELECT comments, changed_by 
                                                FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " 
                                                WHERE purchase_orders_id = '" . $order_ids_array[$po_cnt] . "' 
                                                    AND set_as_po_remarks = 1 
                                                LIMIT 1";
                        $po_remark_result_sql = tep_db_query($po_remark_select_sql);
                        if ($po_remark_row = tep_db_fetch_array($po_remark_result_sql)) {
                            $po_remark = $po_remark_row["comments"];
                            $remark_by_admin_email_address = $po_remark_row["changed_by"];
                        } else {
                            $po_remark = $remark_by_admin_email_address = '';
                        }

                        // check whether this order got any tax on purchased products
                        $product_got_tax = false;
                        for ($prod_cnt = 0; $prod_cnt < count($po_obj->po_items); $prod_cnt++) {
                            if (abs($po_obj->po_items[$prod_cnt]['products_tax']) > 0) {
                                $product_got_tax = true;
                                break;
                            }
                        }

                        $total_colspan = 6;
                        if ($product_got_tax)
                            $total_colspan++;
                        if ($po_obj->po_info['po_status'] == PARTIAL_RECEIVE_STATUS)
                            $total_colspan += 4;

                        echo "<order_detail order_id='" . $order_ids_array[$po_cnt] . "'><![CDATA[";
                        echo '	<table width="100%" border="0" cellspacing="1" cellpadding="0">
				  <tr>
                                    <td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td></tr>';
                        if ($po_obj->po_info['po_status'] == PARTIAL_RECEIVE_STATUS) {
                            echo '<tr>
                                    <td class="subInvoiceBoxHeading" colspan="2" width="40%" >' . TABLE_HEADING_PRODUCTS . '</td>
                                    <td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_QUANTITY . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_PRICE . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                            if ($product_got_tax) {
                                echo '<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                            }
                            echo '</tr>';
                        } else {
                            echo '<tr>
                                    <td class="subInvoiceBoxHeading" colspan="2" width="46%" >' . TABLE_HEADING_PRODUCTS . '</td>
                                    <td class="subInvoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_QUANTITY . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_PRICE . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                            if ($product_got_tax) {
                                echo '<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                            }
                            echo '</tr>';
                        }
                        echo '<tr>
                                <td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; "></td></tr><tr><td></td></tr>';

                        for ($i = 0, $n = sizeof($po_obj->po_items); $i < $n; $i++) {
                            $product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, 
                                                        p.products_bundle_dynamic, p.products_bundle, p.products_skip_inventory, 
                                                        pc.categories_id 
                                                        FROM " . TABLE_PRODUCTS . " AS p 
                                                        LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                            ON p.products_id=pc.products_id 
                                                        WHERE p.products_id = '" . $po_obj->po_items[$i]['products_id'] . "' AND pc.products_is_link=0 ";
                            $product_info_result_sql = tep_db_query($product_info_select_sql);
                            $row_maincat = tep_db_fetch_array($product_info_result_sql);

                            $prod_maincatpath = tep_output_generated_category_path_sq($row_maincat['categories_id']);

                            $ordered_qty_text = $po_obj->po_items[$i]['suggest_qty'];

                            echo '<tr>
                                    <td class="invoiceRecords" valign="top" colspan="2">' . ($row_maincat['products_id'] ? '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($row_maincat['categories_id']) . '&pID=' . $po_obj->po_items[$i]['products_id'] . '&selected_box=catalog&' . $_REQUEST['SID']) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $po_obj->po_items[$i]['products_id'] . '">' . strip_tags($po_obj->po_items[$i]['products_name']) . '</a>' : "<b>" . strip_tags($po_obj->po_items[$i]['products_name']) . "</b>") . '<br>';
                            if ($row_maincat['products_id'] == "") {
                                echo "**--This product is no longer existing in db--**";
                            } else {
                                echo '<span class="categoryPath">[' . $prod_maincatpath . ']</span>';
                            }

                            echo '  </td>
                                    <td class="invoiceRecords" align="center" valign="top">' . $ordered_qty_text . '</td>' . "\n";
                            
                            echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['products_unit_price'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            if ($product_got_tax) {
                                echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['subtotal'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                                echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format(tep_add_tax($po_obj->po_items[$i]['products_unit_price'], $po_obj->po_items[$i]['products_tax']) * ($po_obj->po_items[$i]['suggest_qty']), $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            } else {
                                echo '<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['subtotal'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            }
                            echo '</tr>';
                        }

                        echo '<tr valign="bottom">
				<td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
                            </tr>';
                        if (tep_not_null($po_remark)) {
                            echo '<tr>
                                    <td colspan="' . ($total_colspan - 1) . '" class="orderRemarkSelectedRow">
					<table cellpadding="3">
                                            <tr>
						<td class="dataTableContent">' .
                            nl2br($po_remark) . '<br>';
                            if (tep_not_null($remark_by_admin_email_address))
                                echo "(" . TEXT_REMARK_MODIFIED_BY . $remark_by_admin_email_address . ")";
                            echo '		</td>
                                            </tr>												
					</table>
                                    </td>
                                    <td>&nbsp;</td>
				</tr>';
                        }
                        echo '  <tr valign="bottom">
				  <td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
				</tr>
			    </table>';
                        echo "]]></order_detail>";
                    } else {
                        echo "<order_error>Record not found:" . $order_ids_array[$po_cnt] . "</order_error>";
                    }
                }
                echo "</order_info>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Empty Purchase Order ID provided.</result>";
            }

            break;

        case "product_price_history":
            if (tep_not_null($_REQUEST['p_id']) && tep_not_null($_REQUEST['o_id'])) {
                echo '<cost_history_html><![CDATA[';
                echo '<table width="100%" cellspacing="1" cellpadding="0" border="0">';
                echo '	<tr><td></td><td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td></tr>';
                echo '	<tr>
							<td width="15%" class="subInvoiceBoxHeading">&nbsp;</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_NO . '</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_DATE . '</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_SUPPLIER . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_UNIT_COST . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_STATUS . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_ISSUE_BY . '</td>
						</tr>';
                echo '	<tr><td></td><td colspan="6"><div style="border-bottom: 1px solid #996600; "></td></tr>';

                $po_currency_sql = "SELECT currency FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($_REQUEST['o_id']) . "'";
                $po_currency_result = tep_db_query($po_currency_sql);
                $po_currency_row = tep_db_fetch_array($po_currency_result);

                $past_po_cost_select_sql = "SELECT po.purchase_orders_ref_id, po.purchase_orders_id, po.supplier_id, po.supplier_name, 
                                                po.purchase_orders_issue_date, po.purchase_orders_status, po.currency, pop.products_unit_price 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po
											INNER JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop 
												ON (po.purchase_orders_id = pop.purchase_orders_id AND pop.products_id = '" . tep_db_input($_REQUEST['p_id']) . "') 
											WHERE po.purchase_orders_id <> '" . tep_db_input($_REQUEST['o_id']) . "'
                                                AND po.purchase_orders_type = 3
                                                AND po.purchase_orders_status = 3
											ORDER BY po.purchase_orders_issue_date DESC LIMIT 4";
                $past_po_cost_result_sql = tep_db_query($past_po_cost_select_sql);
                while ($past_po_cost_row = tep_db_fetch_array($past_po_cost_result_sql)) {
                    $po_status_select_sql = "SELECT purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE purchase_orders_status_id = '" . $past_po_cost_row['purchase_orders_status'] . "' AND language_id='" . (int) $languages_id . "'";
                    $po_status_result_sql = tep_db_query($po_status_select_sql);
                    $po_status_row = tep_db_fetch_array($po_status_result_sql);

                    $po_status_hist_select_sql = "SELECT changed_by FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " WHERE purchase_orders_id = '" . tep_db_input($past_po_cost_row['purchase_orders_id']) . "' AND purchase_orders_status_id = 1";
                    $po_status_hist_result_sql = tep_db_query($po_status_hist_select_sql);
                    $po_status_hist_row = tep_db_fetch_array($po_status_hist_result_sql);

                    $po_cost_in_po_currency = $currencies->advance_currency_conversion($past_po_cost_row['products_unit_price'], $past_po_cost_row['currency'], $po_currency_row['currency']);

                    echo '	<tr height="20">';
                    echo '		<td class="invoiceRecords" valign="top">&nbsp;</td>';
                    echo '		<td class="invoiceRecords" valign="top"><a href="' . tep_href_link(FILENAME_CDK_PAYMENT, 'po_id=' . $past_po_cost_row['purchase_orders_id'] . '&subaction=edit_cdk&selected_box=po', 'NONSSL') . '" target="_blank">' . $past_po_cost_row['purchase_orders_ref_id'] . '</a></td>';
                    echo '		<td class="invoiceRecords" valign="top" nowrap="">' . $past_po_cost_row['purchase_orders_issue_date'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top"><a href="' . tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'po_supplier_id_search=' . $past_po_cost_row['supplier_id'] . '&action=id_search&selected_box=po', 'NONSSL') . '" target="_blank">' . $past_po_cost_row['supplier_name'] . '</a></td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $currencies->currencies[$po_currency_row['currency']]['symbol_left'] . number_format($po_cost_in_po_currency, $currencies->currencies[$po_currency_row['currency']]['decimal_places'], $currencies->currencies[$po_currency_row['currency']]['decimal_point'], $currencies->currencies[$po_currency_row['currency']]['thousands_point']) . $currencies->currencies[$po_currency_row['currency']]['symbol_right'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $po_status_row['purchase_orders_status_name'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $po_status_hist_row['changed_by'] . '</td>';
                    echo '	</tr>';
                }
                echo '	<tr><td colspan="7">&nbsp;</td></tr>';
                echo '</table>';
                echo ']]></cost_history_html>';
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Record not found!</result>";
            }

            break;
        
        case "get_supplier_po":
            // Set post var
            $supplier_id = isset($_REQUEST['s_id']) ? $_REQUEST['s_id'] : '';
            $curr_code = isset($_REQUEST['c_id']) ? $_REQUEST['c_id'] : '';
            $reload_page = isset($_REQUEST['reload']) ? $_REQUEST['reload'] : false;
            $form_type = isset($_REQUEST['form_type']) ? $_REQUEST['form_type'] : '';
            $start_date = isset($_REQUEST['s_date']) ? $_REQUEST['s_date'] : '';
            $end_date = isset($_REQUEST['e_date']) ? $_REQUEST['e_date'] : '';
            
            // Initialize po_suppliers class
            $consignment_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
            // Check if reload data
            if ($reload_page == 'false' || $reload_page == '0' || empty($reload_page)) {
                $set_cb = ($form_type == 'add_cdk_cb') ? 'true' : '';
                $consignment_obj->set_cdk_temp_status_empty($supplier_id, $set_cb);
            }
            // Get PO List
            $po_array = $consignment_obj->get_po_list($supplier_id, $curr_code, $start_date, $end_date, $form_type);
            
            // Start Table
            $html_res = '';
            
            // START Table Header
            $html_res .= '<table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
            $html_res .= '  <tbody>';
            $html_res .= '      <tr>';
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="4%">#</td>';
            if ($form_type == 'create_blank_cdk') {
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="5%">' . TABLE_HEADING_CDK_SELECT . "</td>";
            }
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_CDK_DATE . "</td>";
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="15%">' . TABLE_HEADING_CDK_PO_NUMBER . "</td>";
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_CDK_PO_QUANTITY . "</td>";
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_po_amount_title">' . sprintf(TABLE_HEADING_CDK_PO_AMOUNT, $curr_code) . "</div></td>";
            if ($form_type == 'create_blank_cdk') {
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_CDK_QUANTITY . "</td>";
                $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">' . sprintf(TABLE_HEADING_CDK_AMOUNT, $curr_code) . "</div></td>";
            }
            $html_res .= '          <td class="invoiceBoxHeading" align="center" width="10%">' . TABLE_HEADING_CDK_BILLING_STATUS . "</td>";
            $html_res .= '      </tr>';
            // END Table Header
            
            if ($po_array) {
                $row_count = 0;
                $po_list_amont = 0;
                foreach ($po_array as $po_data) {
                    
                    $po_product_info = getPoProductInfo($po_data['purchase_orders_id'], $curr_code);
                    $po_amount = $po_product_info['po_delivered_amount'];
                    $po_quantity = $po_product_info['po_delivered_quantity'];
                    
                    $cdkey_payment_status = getCdkeyStatus($po_data['purchase_orders_id']);
                    
                    switch ($form_type) {
                        case 'add_cdk_cb':
                            $action = 'cdk_list_cb';
                            break;
                        case 'cdk_report':
                            $action = 'cdk_report';
                            break;
                        default:
                            $action = 'cdk_list';
                            break;
                    }
                    
                    $row_style = ($row_count%2) ? 'reportListingOdd' : 'reportListingEven' ;
                    
                    $html_res .= '  <tr class="'.$row_style.'">';
                    $html_res .= '      <td class="main" align="center">' . ($row_count + 1) . '</td>';
                    if ($form_type == 'create_blank_cdk') {
                        $html_res .= '      <td class="main" align="center">' . tep_draw_checkbox_field('po_consignment_id[]', $po_data['purchase_orders_id'], false, '', 'class="po_consignment_id" id="po_consignment_id_'.$po_data['purchase_orders_id'].'" onClick="return calculatePayPOTotal(this, \'' . $po_data['purchase_orders_id'] . '\')"') . '</td>';
                    }
                    $html_res .= '      <td class="main" align="center">' . $po_data['issue_date'] . '</td>';
                    $html_res .= '      <td class="main" align="center"><a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'action=' . $action . '&po_refid=' . $po_data['ref_id'] . '&curr=' . $curr_code . '&poid=' . $po_data['purchase_orders_id'] . '&s_id=' . $supplier_id . '&filterby_s=Sold') . '\', 1000, 600, \'\');">' . $po_data['ref_id'] . '</a></td>';
                    $html_res .= '      <td class="main" align="center">' . $po_quantity . '</td>';
                    $html_res .= '      <td class="main" align="center"><div id="total_po_amount_'.$po_data['purchase_orders_id'].'_div">' . number_format($po_amount, 4, $currencies->currencies[$curr_code]['decimal_point'], $currencies->currencies[$curr_code]['thousands_point']) . '</div></td>';
                    if ($form_type == 'create_blank_cdk') {
                        $html_res .= '      <td class="main" align="center"><div id="po_select_quantity_'.$po_data['purchase_orders_id'].'"></div></td>';
                        $html_res .= '      <td class="main" align="center"><div id="subtotal_'.$po_data['purchase_orders_id'].'_div"></div></td>';
                    }
                    $html_res .= '      <td class="main" align="center">' . $cdkey_payment_status . '</td>';
                    $html_res .= '  </tr>';
                    
                    $po_list_amount = $po_list_amount + $po_amount;
                    $row_count++;
                }
            }
            else {
                $html_res .= '  <tr>';
                $html_res .= '      <td colspan="9">';
                $html_res .= '          No PO Available';
                $html_res .= '      </td>';
                $html_res .= '  </tr>';
            }
            
            $html_res .= '  </tbody>';
            $html_res .= '</table>';
            
            echo "<result>";
            echo "<po_list><![CDATA[";
            echo $html_res;
            echo "]]></po_list>";
            echo "<po_list_amount><![CDATA[";
            echo $po_list_amount;
            echo "]]></po_list_amount>";
            echo "</result>";
            
            break;
            
        case "update_supplier_po":
            // Set post var
            $po_id = isset($_REQUEST['po_id']) ? $_REQUEST['po_id'] : '';
            $curr_code = isset($_REQUEST['c_id']) ? $_REQUEST['c_id'] : '';
            // Initialize po_suppliers class
            $consignment_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
            
            $withdrawal = "and cpw.cdk_withdrawal_temp_status = '1' and cpw.cdk_cb_status != '2'";
            $cdkey_qry = $consignment_obj->cdk_list_query($po_id, $withdrawal);
            
            $cdkey_result = tep_db_query($cdkey_qry);
            $cdkey_qty = tep_db_num_rows($cdkey_result);
            
            $cdkey_subtotal = 0;
            while ($cdkey_row = tep_db_fetch_array($cdkey_result)) {
                $selling_price = $consignment_obj->getProductSellPrice($cdkey_row['products_id'], $currencies, $curr_code);
                $cdkey_subtotal = $cdkey_subtotal + round($selling_price, 4);
            }
            
            echo "<result>";
            echo "  <cdkey_quantity><![CDATA[" . $cdkey_qty . "]]></cdkey_quantity>";
            echo "  <cdkey_total><![CDATA[" . $cdkey_subtotal . "]]></cdkey_total>";
            echo "</result>";
            break;

        default:
            echo "<result>Unknown request!</result>";

            break;
    }
}

function generateTagSelectionOptions($status, $order_pid_str, $whole_list = false, $apply_tag_sec_only = false) {
    global $language_id;
    $order_ids_array = tep_not_null($order_pid_str) ? explode(',', $order_pid_str) : array();
    echo "<selection>";
    $po_status_id_select_sql = "SELECT purchase_orders_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_status = '" . (int) $status . "'";
    $po_status_id_result_sql = tep_db_query($po_status_id_select_sql);
    if ($po_status_id_row = tep_db_fetch_array($po_status_id_result_sql)) {
        if (!$apply_tag_sec_only) {
            echo "<option index=''><![CDATA[Purchase Order Lists Options ...]]></option>";
        }

        echo "<option index='' " . (!$apply_tag_sec_only ? "disabled='1'" : '') . "><![CDATA[Apply tag:]]></option>";

        $mirror_for_delete_tag_str = '';
        $po_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $po_status_id_row["purchase_orders_status"] . "', orders_tag_status_ids) AND filename='" . FILENAME_CDK_PAYMENT . "' ORDER BY orders_tag_name;";
        $po_tag_result_sql = tep_db_query($po_tag_select_sql);
        while ($po_tag_row = tep_db_fetch_array($po_tag_result_sql)) {
            echo "<option index='" . 'otag_' . $po_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_row["orders_tag_name"] . "]]></option>";
            if ($whole_list == true) {
                $mirror_for_delete_tag_str .= "<option index='" . 'rmtag_' . $po_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_row["orders_tag_name"] . "]]></option>";
            }
        }

        if (!$apply_tag_sec_only) {
            echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";

            if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
                echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                echo $mirror_for_delete_tag_str;
            } else {
                // select the common tags among those selected orders
                if (count($order_ids_array)) {
                    $po_tag_remove_select_sql = "SELECT DISTINCT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PURCHASE_ORDERS . " AS po WHERE po.purchase_orders_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET(otag.orders_tag_id, po.purchase_orders_tag_ids) AND filename='" . FILENAME_CDK_PAYMENT . "' ORDER BY orders_tag_name; ";
                    $po_tag_remove_result_sql = tep_db_query($po_tag_remove_select_sql);
                    if (tep_db_num_rows($po_tag_remove_result_sql) > 0)
                        echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                    while ($po_tag_remove_row = tep_db_fetch_array($po_tag_remove_result_sql)) {
                        echo "<option index='" . 'rmtag_' . $po_tag_remove_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_remove_row["orders_tag_name"] . "]]></option>";
                    }
                }
            }
        }
    }
    echo "</selection>";
}

function generateTagString($po_ids_array) {
    echo "<tag_details>";
    for ($i = 0; $i < count($po_ids_array); $i++) {
        $po_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PURCHASE_ORDERS . " AS po WHERE po.purchase_orders_id  = '" . (int) $po_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, po.purchase_orders_tag_ids) AND filename='" . FILENAME_CDK_PAYMENT . "';";
        $po_tag_result_sql = tep_db_query($po_tag_select_sql);
        $tags_str = '';
        while ($po_tag_row = tep_db_fetch_array($po_tag_result_sql)) {
            $tags_str .= $po_tag_row["orders_tag_name"] . ', ';
        }
        if (substr($tags_str, -2) == ', ')
            $tags_str = substr($tags_str, 0, -2);
        echo "<order_tags order_id='" . (int) $po_ids_array[$i] . "'><![CDATA[" . $tags_str . "]]></order_tags>";
    }
    echo "</tag_details>";
}

function getPoProductInfo($po_id, $curr_code) {
    $product_array = array();
    $product_info_sql = "SELECT p.products_id, p.products_good_delivered_quantity AS delivered_quantity
                        FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS p
                        WHERE p.purchase_orders_id = '" . tep_db_input($po_id) . "'
    ";
    $product_info_result = tep_db_query($product_info_sql);
    
    $selling_price_total = 0;
    $delivered_quantity = 0;
    while ($product_info_row = tep_db_fetch_array($product_info_result)) {
        $selling_price = getProductSellPrice($product_info_row['products_id'], $curr_code);
        $delivered_quantity = $delivered_quantity + $product_info_row['delivered_quantity'];
        $delivered_total = $selling_price * $product_info_row['delivered_quantity'];
        $selling_price_total = $selling_price_total + $delivered_total;
    }
    
    $product_array['po_delivered_quantity'] = $delivered_quantity;
    $product_array['po_delivered_amount'] = $selling_price_total;
    
    return $product_array;
}

function getProductSellPrice($products_id, $sell_curr) {
    $currencies = new currencies();
    $price_array = $currencies->get_product_prices_info($products_id);
    if ($sell_curr == DEFAULT_CURRENCY) {
        if ($price_array['base_cur'] == DEFAULT_CURRENCY) {
            $selling_price = $price_array['price'];
        } else {
            if (count($price_array['defined_price']) && isset($price_array['defined_price'][DEFAULT_CURRENCY])) {
                $selling_price = $price_array['defined_price'][DEFAULT_CURRENCY];
            } else {
                $base_rate = 1;
                if ($currencies->currencies[$price_array['base_cur']]['value'] > 0) {
                    $base_rate = 1 / $currencies->currencies[$price_array['base_cur']]['value'];
                }
                $selling_price = $base_rate * $price_array['price'];
            }
        }
    } else {
        $price_array = $currencies->get_product_prices_info($products_id);
        if ($price_array['base_cur'] == $sell_curr) {
            $selling_price = $price_array['price'];
        } else {
            if (count($price_array['defined_price']) && isset($price_array['defined_price'][$sell_curr])) {
                $selling_price = $price_array['defined_price'][$sell_curr];
            } else {
                $selling_price = $currencies->advance_currency_conversion($price_array['price'], $price_array['base_cur'], $sell_curr, false);
            }
        }
    }
    
    return $selling_price;
}

function getCdkeyStatus($po_id) {
    $cdkey_sql = "  SELECT cpw.cdk_withdrawal_status
                    FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                    INNER JOIN " . TABLE_CUSTOM_PRODUCTS_WITHDRAWAL . " AS cpw
                        ON cpc.custom_products_code_id = cpw.custom_products_code_id
                    WHERE cpc.purchase_orders_id = '" . tep_db_input($po_id) . "'
    ";
    $cdkey_result = tep_db_query($cdkey_sql);
    
    $total_paid = 0;
    $total_others = 0;
    while ($cdkey_row = tep_db_fetch_array($cdkey_result)) {
        switch ($cdkey_row['cdk_withdrawal_status']) {
            case "4":
                $total_paid++;
                break;
            default:
                $total_others++;
                break;
        }
    }
    
    return $total_paid.'/'.$total_others;
}

?>