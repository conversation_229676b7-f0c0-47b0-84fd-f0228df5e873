<?php
/*
  $Id: countries.php,v 1.16 2015/02/26 09:37:51 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case 'insert':
            $countries_name = tep_db_prepare_input($HTTP_POST_VARS['countries_name']);
            $countries_iso_code_2 = tep_db_prepare_input($HTTP_POST_VARS['countries_iso_code_2']);
            $countries_iso_code_3 = tep_db_prepare_input($HTTP_POST_VARS['countries_iso_code_3']);
            $countries_currencies_id = (tep_not_null($HTTP_POST_VARS['countries_currencies_id']) ? tep_db_prepare_input($HTTP_POST_VARS['countries_currencies_id']) : "NULL");
            $countries_international_dialing_code = tep_db_prepare_input($HTTP_POST_VARS['countries_international_dialing_code']);
            $countries_website_domain = tep_db_prepare_input($HTTP_POST_VARS['countries_website_domain']);
            $address_format_id = tep_db_prepare_input($HTTP_POST_VARS['address_format_id']);

            $sql_data_array = array('countries_name' => $countries_name,
                'countries_iso_code_2' => $countries_iso_code_2,
                'countries_iso_code_3' => $countries_iso_code_3,
                'countries_currencies_id' => $countries_currencies_id,
                'countries_international_dialing_code' => $countries_international_dialing_code,
                'countries_website_domain' => $countries_website_domain,
                'address_format_id' => $address_format_id);

            tep_db_perform(TABLE_COUNTRIES, $sql_data_array);

            tep_redirect(tep_href_link(FILENAME_COUNTRIES));
            break;
        case 'save':
            $countries_id = tep_db_prepare_input($HTTP_GET_VARS['cID']);
            $countries_name = tep_db_prepare_input($HTTP_POST_VARS['countries_name']);
            $countries_iso_code_2 = tep_db_prepare_input($HTTP_POST_VARS['countries_iso_code_2']);
            $countries_iso_code_3 = tep_db_prepare_input($HTTP_POST_VARS['countries_iso_code_3']);
            $countries_currencies_id = (tep_not_null($HTTP_POST_VARS['countries_currencies_id']) ? tep_db_prepare_input($HTTP_POST_VARS['countries_currencies_id']) : "NULL");
            $countries_international_dialing_code = tep_db_prepare_input($HTTP_POST_VARS['countries_international_dialing_code']);
            $countries_website_domain = tep_db_prepare_input($HTTP_POST_VARS['countries_website_domain']);
            $address_format_id = tep_db_prepare_input($HTTP_POST_VARS['address_format_id']);

            $sql_data_array = array('countries_name' => $countries_name,
                'countries_iso_code_2' => $countries_iso_code_2,
                'countries_iso_code_3' => $countries_iso_code_3,
                'countries_currencies_id' => $countries_currencies_id,
                'countries_international_dialing_code' => $countries_international_dialing_code,
                'countries_website_domain' => $countries_website_domain,
                'address_format_id' => $address_format_id);

            tep_db_perform(TABLE_COUNTRIES, $sql_data_array, 'update', "countries_id = '" . (int) $countries_id . "'");

            tep_redirect(tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $countries_id));
            break;
        case 'deleteconfirm':
            $countries_id = tep_db_prepare_input($HTTP_GET_VARS['cID']);

            /* -- GST : check if GST using this country info -- */
            $gst_sel_sql = "SELECT otc.country_code
                            FROM " . TABLE_COUNTRIES . " AS c
                            INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc
                                    ON otc.country_code = c.countries_iso_code_2
                            WHERE c.countries_id = '" . (int) $countries_id . "'";
            $gst_res_sql = tep_db_query($gst_sel_sql);
            if (tep_db_num_rows($gst_res_sql) > 0) {
                $messageStack->add_session(ERROR_COUNTRY_IN_USE_IN_GST, 'error');
            } else {
                tep_db_query("delete from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "'");
                tep_db_query("DELETE FROM " . TABLE_COUNTRIES_CONTENT . " WHERE countries_id = '" . (int) $countries_id . "'");
                tep_db_query("DELETE FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " WHERE countries_id = '" . (int) $countries_id . "'");
            }

            tep_redirect(tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page']));
            break;
        case 'update_xml':
            require_once(DIR_WS_CLASSES . 'xml_creator.php');
            $xmlcreator = new xml_creator('create_countries_xml');
            $messageStack->add_session(TEXT_INFO_XML_UPDATED, 'success');
            tep_redirect(tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page']));
            break;
        case 'update_json':
            include_once(DIR_WS_CLASSES . 'json.php');
            $json = new Services_JSON();
            include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
            $aws_obj = new ogm_amazon_ws();
            $status = 'error';
            $msg = TEXT_INFO_JSON_ERROR;


            # generate json data
            $f_ctry = array();
            $f_reg = array();
            $f_state = array();
            $geo_zone_type = array(2, 3);

            # get languages
            $languages_select_sql = "SELECT code, name FROM " . TABLE_LANGUAGES;
            $languages_result_sql = tep_db_query($languages_select_sql);
            $languages_array = array();
            while ($languages_row = tep_db_fetch_array($languages_result_sql)) {
                $languages_array[$languages_row['code']] = $languages_row['name'];
            }

            # get currencies
            $currencies_select_sql = "SELECT code, title, symbol_left, symbol_right FROM " . TABLE_CURRENCIES . " WHERE currencies_used_for <> ''";
            $currencies_result_sql = tep_db_query($currencies_select_sql);
            $currencies_array = array();
            $cur_arr = array();
            while ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
                $currencies_array[$currencies_row['code']] = array(
                    'title' => $currencies_row['title'],
                    'symbol_left' => $currencies_row['symbol_left'],
                    'symbol_right' => $currencies_row['symbol_right']
                );

                $cur_arr[$currencies_row['code']] = $currencies_row['title'] . ' (' . $currencies_row['symbol_left'] . $currencies_row['symbol_right'] . ')';
            }

            // default setting
            $f_reg['refer'] = '';
            $f_reg["default"] = array();

            $def_zones_select_sql = "	SELECT gz.geo_zone_type, zi.geo_zone_info
                                        FROM " . TABLE_GEO_ZONES . " as gz
                                        INNER JOIN " . TABLE_ZONES_TO_GEO_ZONES . " as ztgz
                                                ON gz.geo_zone_id = ztgz.geo_zone_id
                                                AND ztgz.zone_country_id = '" . STORE_COUNTRY . "'
                                        INNER JOIN " . TABLE_ZONES_INFO . " as zi
                                                ON gz.geo_zone_id = zi.geo_zone_id
                                        WHERE gz.geo_zone_type IN (2, 3)";
            $def_zones_result_sql = tep_db_query($def_zones_select_sql);
            while ($def_zones_row = tep_db_fetch_array($def_zones_result_sql)) {
                if (isset($def_zones_row['geo_zone_info'])) {
                    $_data_obj = $json->decode($def_zones_row['geo_zone_info']);
                    switch ($def_zones_row['geo_zone_type']) {
                        case 2: // language
                            if (isset($_data_obj->zone_languages_id) && is_array($_data_obj->zone_languages_id)) {
                                $_val = array();
                                foreach ($_data_obj->zone_languages_id as $_id => $_code) {
                                    if (isset($languages_array[$_code]))
                                        $_val[$_code] = $languages_array[$_code];
                                }

                                $f_reg["default"]["language"] = array(
                                    'val' => $_val,
                                    'def' => $_data_obj->zone_default_languages_id
                                );
                            }
                            break;

                        case 3: // currency
                            if (isset($_data_obj->zone_currency_id) && is_array($_data_obj->zone_currency_id)) {
                                $_val = array();
                                foreach ($_data_obj->zone_currency_id as $_id => $_code) {
                                    if (isset($cur_arr[$_code]))
                                        $_val[$_code] = $cur_arr[$_code];
                                }

                                $f_reg["default"]["currency"] = array(
                                    'val' => $_val,
                                    'def' => $_data_obj->zone_default_currency_id
                                );
                            }
                            break;
                    }
                }
            }

            # get countries
            $countries_select_sql = "   SELECT countries_iso_code_2, countries_id, countries_name, countries_international_dialing_code 
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_display = 1
                                        ORDER BY countries_name";
            $countries_result_sql = tep_db_query($countries_select_sql);
            while ($countries_row = tep_db_fetch_array($countries_result_sql)) {
                $f_ctry[$countries_row["countries_iso_code_2"]] = array(
                    "id" => $countries_row["countries_id"],
                    "name" => $countries_row["countries_name"],
                    "idd" => $countries_row["countries_name"] . " +" . $countries_row["countries_international_dialing_code"]
                );

                $f_data = array();
                foreach ($geo_zone_type as $key => $type) {
                    $geo_zones_select_sql = "	SELECT gz.geo_zone_id, ztgz.zone_country_id, zi.geo_zone_info
                                                FROM " . TABLE_GEO_ZONES . " as gz
                                                INNER JOIN " . TABLE_ZONES_TO_GEO_ZONES . " as ztgz
                                                        ON gz.geo_zone_id = ztgz.geo_zone_id
                                                        AND ztgz.zone_country_id = '" . $countries_row["countries_id"] . "'
                                                INNER JOIN " . TABLE_ZONES_INFO . " as zi
                                                        ON gz.geo_zone_id = zi.geo_zone_id
                                                WHERE gz.geo_zone_type = '" . $type . "'";
                    $geo_zones_result_sql = tep_db_query($geo_zones_select_sql);
                    if ($geo_zones_row = tep_db_fetch_array($geo_zones_result_sql)) {
                        if (isset($geo_zones_row['geo_zone_info'])) {
                            $_data_obj = $json->decode($geo_zones_row['geo_zone_info']);
                            switch ($type) {
                                case 2: // language
                                    if (isset($_data_obj->zone_languages_id) || isset($_data_obj->zone_default_languages_id)) {
                                        $_val = array();
                                        if (is_array($_data_obj->zone_languages_id)) {
                                            foreach ($_data_obj->zone_languages_id as $_id => $_code) {
                                                if (isset($languages_array[$_code]))
                                                    $_val[$_code] = "";
                                            }
                                        }

                                        $f_data['language'] = array(
                                            'val' => $_val,
                                            'def' => $_data_obj->zone_default_languages_id
                                        );
                                    }
                                    break;

                                case 3: // currency
                                    if (isset($_data_obj->zone_currency_id) || isset($_data_obj->zone_default_currency_id)) {
                                        $_val = array();
                                        if (is_array($_data_obj->zone_currency_id)) {
                                            foreach ($_data_obj->zone_currency_id as $_id => $_code) {
                                                if (isset($currencies_array[$_code]))
                                                    $_val[$_code] = '';
                                            }
                                        }
                                        $f_reg['refer'] = array('currency' => $cur_arr, 'language' => $languages_array);
                                        $f_data['currency'] = array(
                                            'val' => $_val,
                                            'def' => $_data_obj->zone_default_currency_id
                                        );
                                    }
                                    break;
                            }
                        }
                    }
                }

                if (!empty($f_data)) {
                    $f_reg[$countries_row["countries_iso_code_2"]] = $f_data;
                    unset($f_data);
                }

                // state
                $state_zone_sel_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = " . $countries_row["countries_id"] . " ORDER BY zone_name";
                $state_zone_res_sql = tep_db_query($state_zone_sel_sql);
                while ($state_zone_row = tep_db_fetch_array($state_zone_res_sql)) {
                    $f_data[$state_zone_row["zone_id"]] = utf8_encode($state_zone_row["zone_name"]);
                }
                if (!empty($f_data)) {
                    $f_state[$countries_row["countries_id"]] = $f_data;
                    unset($f_data);
                }
            }
            $region_filename = "region.json";
            $country_filename = "country.json";
            $state_filename = "state.json";
            if (!$aws_obj->is_aws_s3_enabled()) {
                $regionWriter = fopen("repository/$region_filename", 'w');
                fwrite($regionWriter, "region(" . json_encode($f_reg) . ");");
                fclose($regionWriter);

                $countryWriter = fopen("repository/$country_filename", 'w');
                fwrite($countryWriter, "country(" . json_encode($f_ctry) . ");");
                fclose($countryWriter);

                $fp = fopen("repository/$state_filename", 'w');
                fwrite($fp, "state(" . json_encode($f_state) . ");");
                fclose($fp);

                $status = 'success';
                $msg = TEXT_INFO_JSON_UPDATED;
            } else {
                $aws_obj->set_bucket_key('BUCKET_SHASSO_STATIC');
                $aws_obj->set_filepath('account/data/localization/');
                $aws_obj->set_acl('ACL_PUBLIC');
                $aws_obj->set_file_content("country(" . $json->encode($f_ctry) . ");");
                $mime_type = CFMimeTypes::get_mimetype('json');
                $aws_obj->set_file_content_type($mime_type);
                $aws_obj->set_filename($country_filename);
                if ($aws_obj->save_file()) {
                    $aws_obj->set_file_content("state(" . $json->encode($f_state) . ");");
                    $aws_obj->set_filename($state_filename);
                    if ($aws_obj->save_file()) {
                        $aws_obj->set_file_content("region(" . $json->encode($f_reg) . ");");
                        $aws_obj->set_filename($region_filename);
                        if ($aws_obj->save_file()) {
                            $status = 'success';
                            $msg = TEXT_INFO_JSON_UPDATED;
                        }
                    }
                }
            }

            $messageStack->add_session($msg, $status);
            tep_redirect(tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page']));
            break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top"><table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table></td>
                <!-- body_text //-->
                <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr class="dataTableHeadingRow">
                                                    <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_COUNTRY_NAME; ?></td>
                                                    <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_COUNTRY_WEBSITE_DOMAIN; ?></td>
                                                    <td class="dataTableHeadingContent" align="center" colspan="2"><?php echo TABLE_HEADING_COUNTRY_CODES; ?></td>
                                                    <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
                                                </tr>
                                                <?
                                                $countries_query_raw = "select countries_id, countries_name, countries_iso_code_2, countries_iso_code_3, countries_currencies_id, countries_international_dialing_code, countries_website_domain, address_format_id from " . TABLE_COUNTRIES . " order by countries_name";
                                                $countries_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $countries_query_raw, $countries_query_numrows);
                                                $countries_query = tep_db_query($countries_query_raw);
                                                while ($countries = tep_db_fetch_array($countries_query)) {
                                                    if ((!isset($HTTP_GET_VARS['cID']) || (isset($HTTP_GET_VARS['cID']) && ($HTTP_GET_VARS['cID'] == $countries['countries_id']))) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
                                                        $cInfo = new objectInfo($countries);
                                                    }

                                                    if (isset($cInfo) && is_object($cInfo) && ($countries['countries_id'] == $cInfo->countries_id)) {
                                                        echo '			<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id . '&action=edit') . '\'">' . "\n";
                                                    } else {
                                                        echo '			<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $countries['countries_id']) . '\'">' . "\n";
                                                    }
                                                    ?>
                                                    <td class="dataTableContent"><?php echo $countries['countries_name']; ?></td>
                                                    <td class="dataTableContent"><?php echo $countries['countries_website_domain']; ?></td>
                                                    <td class="dataTableContent" align="center" width="40"><?php echo $countries['countries_iso_code_2']; ?></td>
                                                    <td class="dataTableContent" align="center" width="40"><?php echo $countries['countries_iso_code_3']; ?></td>
                                                    <td class="dataTableContent" align="right"><?php
                                                        if (isset($cInfo) && is_object($cInfo) && ($countries['countries_id'] == $cInfo->countries_id)) {
                                                            echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', '');
                                                        } else {
                                                            echo '<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $countries['countries_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
                                                        }
                                                        ?>&nbsp;</td>
                                        </tr>
                                    <? } ?>
                                    <tr>
                                        <td colspan="5"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td class="smallText" valign="top"><?php echo $countries_split->display_count($countries_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_COUNTRIES); ?></td>
                                                    <td class="smallText" align="right"><?php echo $countries_split->display_links($countries_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                                                </tr>
                                                <? if (empty($action)) { ?>
                                                    <tr>
                                                        <td align="left">
                                                            <a href="<?= tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&action=update_xml') ?>"><?= LINK_UPDATE_XML ?></a>
                                                            <a href="<?= tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&action=update_json') ?>"><?= LINK_UPDATE_JSON ?></a>
                                                        </td>
                                                        <td align="right"><?php echo '<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&action=new') . '">' . tep_image_button('button_new_country.gif', IMAGE_NEW_COUNTRY) . '</a>'; ?></td>
                                                    </tr>
                                                <? } ?>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <?
                            $heading = array();
                            $contents = array();

                            $currencies_default_list_array = tep_get_currencies_list();

                            switch ($action) {
                                case 'new':
                                    $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_COUNTRY . '</b>');

                                    $contents = array('form' => tep_draw_form('countries', FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&action=insert'));
                                    $contents[] = array('text' => TEXT_INFO_INSERT_INTRO);
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_NAME . '<br>' . tep_draw_input_field('countries_name'));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_WEBSITE_DOMAIN . '<br>' . tep_draw_input_field('countries_website_domain'));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_2 . '<br>' . tep_draw_input_field('countries_iso_code_2'));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_3 . '<br>' . tep_draw_input_field('countries_iso_code_3'));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_ADDRESS_FORMAT . '<br>' . tep_draw_pull_down_menu('address_format_id', tep_get_address_formats()));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_MAIN_CURRENCY . '<br>' . tep_draw_pull_down_menu('countries_currencies_id', $currencies_default_list_array));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_IDD . '<br>' . tep_draw_input_field('countries_international_dialing_code'));
                                    $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_insert.gif', IMAGE_INSERT) . '&nbsp;<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
                                    break;
                                case 'edit':
                                    $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_COUNTRY . '</b>');

                                    $contents = array('form' => tep_draw_form('countries', FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id . '&action=save'));
                                    $contents[] = array('text' => TEXT_INFO_EDIT_INTRO);
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_NAME . '<br>' . tep_draw_input_field('countries_name', $cInfo->countries_name));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_WEBSITE_DOMAIN . '<br>' . tep_draw_input_field('countries_website_domain', $cInfo->countries_website_domain));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_2 . '<br>' . tep_draw_input_field('countries_iso_code_2', $cInfo->countries_iso_code_2));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_3 . '<br>' . tep_draw_input_field('countries_iso_code_3', $cInfo->countries_iso_code_3));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_ADDRESS_FORMAT . '<br>' . tep_draw_pull_down_menu('address_format_id', tep_get_address_formats(), $cInfo->address_format_id));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_MAIN_CURRENCY . '<br>' . tep_draw_pull_down_menu('countries_currencies_id', $currencies_default_list_array, $cInfo->countries_currencies_id));
                                    $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_IDD . '<br>' . tep_draw_input_field('countries_international_dialing_code', $cInfo->countries_international_dialing_code));
                                    $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . '&nbsp;<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
                                    break;
                                case 'delete':
                                    $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_COUNTRY . '</b>');

                                    $contents = array('form' => tep_draw_form('countries', FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id . '&action=deleteconfirm'));
                                    $contents[] = array('text' => TEXT_INFO_DELETE_INTRO);
                                    $contents[] = array('text' => '<br><b>' . $cInfo->countries_name . '</b>');
                                    $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_UPDATE) . '&nbsp;<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
                                    break;
                                default:
                                    if (is_object($cInfo)) {
                                        $main_currency = TEXT_NOT_AVAILABLE;

                                        $currency_array = tep_get_currencies_info_array('currencies_id', $cInfo->countries_currencies_id);

                                        if (isset($currency_array[0]['code'])) {
                                            $main_currency = $currency_array[0]['code'];
                                        }

                                        $heading[] = array('text' => '<b>' . $cInfo->countries_name . '</b>');

                                        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_COUNTRIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->countries_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_NAME . '<br>' . $cInfo->countries_name);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_WEBSITE_DOMAIN . ' ' . $cInfo->countries_website_domain);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_2 . ' ' . $cInfo->countries_iso_code_2);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_CODE_3 . ' ' . $cInfo->countries_iso_code_3);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_ADDRESS_FORMAT . ' ' . $cInfo->address_format_id);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_MAIN_CURRENCY . ' ' . $main_currency);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_IDD . ' ' . $cInfo->countries_international_dialing_code);
                                    }
                                    break;
                            }

                            if ((tep_not_null($heading)) && (tep_not_null($contents))) {
                                echo '					<td width="25%" valign="top">' . "\n";

                                $box = new box;
                                echo $box->infoBox($heading, $contents);

                                echo '					</td>' . "\n";
                            }
                            ?>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </td>
    <!-- body_text_eof //-->
</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
