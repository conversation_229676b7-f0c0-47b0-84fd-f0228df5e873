<?php
/*
	$Id: coupon_admin.php,v 1.19 2010/11/30 10:51:32 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$view_discount_code_generation_permission = tep_admin_files_actions(FILENAME_COUPON_ADMIN, 'COUPONS_VIEW_DISCOUNT_COUPON');
$add_discount_code_generation_permission = tep_admin_files_actions(FILENAME_COUPON_ADMIN, 'COUPONS_GENERATION_REQUEST');
$edit_discount_code_generation_permission = tep_admin_files_actions(FILENAME_COUPON_ADMIN, 'COUPONS_GENERATION_EDIT');
$approve_discount_code_generation_permission = tep_admin_files_actions(FILENAME_COUPON_ADMIN, 'COUPONS_GENERATION_APPROVE');

if ($_GET['selected_box']) {
	$_GET['action']='';
    $_GET['old_action']='';
}

if (($_GET['action'] == 'send_email_to_user') && ($_POST['customers_email_address']) && (!$_POST['back_x'])) {
	switch ($_POST['customers_email_address']) {
    	case '***':
      		$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS);
      		$mail_sent_to = TEXT_ALL_CUSTOMERS;
      		break;
    	case '**D':
      		$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_newsletter <> '0' and customers_newsletter IS NOT NULL and account_activated ='1'");
      		$mail_sent_to = TEXT_NEWSLETTER_CUSTOMERS;
      		break;
    	default:
      		$customers_email_address = tep_db_prepare_input($_POST['customers_email_address']);
      		$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($customers_email_address) . "'");
      		$mail_sent_to = $_POST['customers_email_address'];
      		break;
	}
    
    $coupon_query = tep_db_query("select coupon_code from " . TABLE_COUPONS . " where coupon_id = '" . $_GET['cid'] . "'");
    $coupon_result = tep_db_fetch_array($coupon_query);
    $coupon_name_query = tep_db_query("select coupon_name from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . $_GET['cid'] . "' and language_id = '" . $languages_id . "'");
    $coupon_name = tep_db_fetch_array($coupon_name_query);
	
    $from = tep_db_prepare_input($_POST['from']);
    $subject = tep_db_prepare_input($_POST['subject']);
    
    while ($mail = tep_db_fetch_array($mail_query)) {
      	$message = tep_db_prepare_input($_POST['message']);
      	$message .= "\n\n" . TEXT_TO_REDEEM . "\n\n";
      	$message .= TEXT_VOUCHER_IS . $coupon_result['coupon_code'] . "\n\n";
      	$message .= TEXT_REMEMBER . "\n\n";
      	$message .= TEXT_VISIT . "\n\n";
     	
      	//Let's build a message object using the email class
      	$mimemessage = new email(array('X-Mailer: osCommerce bulk mailer'));
      	// add the message to the object
		// MaxiDVD Added Line For WYSIWYG HTML Area: BOF (Send TEXT Email when WYSIWYG Disabled)
    	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
    		$mimemessage->add_text($message);
    	} else {
    		$mimemessage->add_html($message);
    	}
		// MaxiDVD Added Line For WYSIWYG HTML Area: EOF (Send HTML Email when WYSIWYG Enabled)
      	$mimemessage->build_message();    
      	$mimemessage->send($mail['customers_firstname'] . ' ' . $mail['customers_lastname'], $mail['customers_email_address'], '', $from, $subject);
	}
	
	tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, 'mail_sent_to=' . urlencode($mail_sent_to)));
}

if ( ($_GET['action'] == 'preview_email') && (!$_POST['customers_email_address']) ) {
	$_GET['action'] = 'email';    
    $messageStack->add(ERROR_NO_CUSTOMER_SELECTED, 'error');
}

if ($_GET['mail_sent_to']) {
	$messageStack->add(sprintf(NOTICE_EMAIL_SENT_TO, $_GET['mail_sent_to']), 'notice');
}

switch ($_GET['action']) {
	case 'coupons_list':
		if ($_POST['cgid'] == '0') {
			tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, ''));
		}
		break;
	case 'voucherdelete':
  	$delete_query=tep_db_query("UPDATE " . TABLE_COUPONS . " SET coupon_active = 'D', date_modified = now() WHERE coupon_id='".tep_db_input($_GET['cid'])."'");
      	
		// create status history
		$user_comments = urldecode($_GET['comments']);
		$comments = 'Delete discount code.' . "\n" . $user_comments;
		$status_array = array('coupon_id' => tep_db_prepare_input($_GET['cid']),
							  'coupon_active' => 'D',
							  'date_added' => 'now()',
							  'comments' => tep_db_prepare_input($comments),
							  'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
		tep_db_perform(TABLE_COUPONS_STATUS_HISTORY, $status_array);
		
		tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, ((isset($_GET['page']))? 'page='.$_GET['page']:'')));
  	break;
	case 'update':
		// get all _POST and validate
		$languages = tep_get_languages();
        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
          	$language_id = $languages[$i]['id'];
          	$_POST['coupon_name'][$language_id] = trim($_POST['coupon_name'][$language_id]);
          	$_POST['coupon_desc'][$language_id] = trim($_POST['coupon_desc'][$language_id]);
          	
		}
      	$_POST['coupon_amount'] = trim($_POST['coupon_amount']);
      	$update_errors = 0;
      	if (!$_POST['coupon_name'][$languages[0]['id']]) {
        	$update_errors = 1;
        	$messageStack->add(ERROR_NO_COUPON_NAME, 'error');
      	}
      	if (!$_POST['coupon_amount']) {
        	$update_errors = 1;
        	$messageStack->add(ERROR_NO_COUPON_AMOUNT, 'error');
      	}
      	
      	if ((!isset($_POST['coupon_uses_coupon']) || empty($_POST['coupon_uses_coupon'])) || (!isset($_POST['coupon_uses_user']) || empty($_POST['coupon_uses_user']))) {
      		if ((!isset($_POST['uses_per_coupon_unlimited']) || $_POST['uses_per_coupon_unlimited']=='N') && (!isset($_POST['coupon_uses_coupon']) || empty($_POST['coupon_uses_coupon']))) {
      			$update_errors = 1;
      			$messageStack->add(ERROR_COUPON_USES_COUPON, 'error');
      		}
      		if ((!isset($_POST['uses_per_user_unlimited']) || $_POST['uses_per_user_unlimited']=='N') && (!isset($_POST['coupon_uses_user']) || empty($_POST['coupon_uses_user']))) {
      			$update_errors = 1;
      			$messageStack->add(ERROR_COUPON_USES_USER, 'error');
      		}
      	} else if ($_POST['coupon_uses_coupon'] < $_POST['coupon_uses_user']) {
      		$update_errors = 1;
      		$messageStack->add(ERROR_COUPON_USES_ERROR, 'error');
      	}
      	
      	if ($update_errors != 0) {
        	$_GET['action'] = 'voucheredit';  
      	} else {  
        	$_GET['action'] = 'update_preview';
      	}
      	break;
	case 'update_confirm':
		$user_comments = tep_db_prepare_input($_POST['comments']);
    	$coupon_type = "F";
    	if (substr($_POST['coupon_amount'], -1) == '%') $coupon_type='P';
    	
        if (!isset($_POST['uses_per_coupon_unlimited']) || (isset($_POST['uses_per_coupon_unlimited']) && $_POST['uses_per_coupon_unlimited']=='')) {
        	$_POST['uses_per_coupon_unlimited'] = 'N';
        }
        
        if (!isset($_POST['uses_per_user_unlimited']) || (isset($_POST['uses_per_user_unlimited']) && $_POST['uses_per_user_unlimited']=='')) {
        	$_POST['uses_per_user_unlimited'] = 'N';
        }
    	
    	$restrict_to_customers_groups = array();
    	foreach ($_POST['coupon_customers_groups_id'] as $groups) {
    		$restrict_to_customers_groups[] = $groups;
    	}
    	
        $sql_data_array = array('coupon_type' => tep_db_prepare_input($coupon_type),
                                'coupon_amount' => tep_db_prepare_input($_POST['coupon_amount']),
                                'coupon_minimum_order' => tep_db_prepare_input($_POST['coupon_min_order']),
                                'uses_per_coupon' => tep_db_prepare_input($_POST['coupon_uses_coupon']),
                                'uses_per_coupon_unlimited' => tep_db_prepare_input($_POST['uses_per_coupon_unlimited']),
                                'uses_per_user' => tep_db_prepare_input($_POST['coupon_uses_user']),
                                'uses_per_user_unlimited' => tep_db_prepare_input($_POST['uses_per_user_unlimited']),
                                'restrict_to_products' => tep_db_prepare_input($_POST['coupon_products']),
                                'restrict_to_categories' => tep_db_prepare_input($_POST['coupon_categories']),
                                'restrict_to_customers_groups' => tep_db_prepare_input(implode(',', $restrict_to_customers_groups)),
                                'coupon_start_date' => $_POST['coupon_startdate'],
                                'coupon_expire_date' => $_POST['coupon_finishdate'],
                                'date_modified' => 'now()');
        
    	$languages = tep_get_languages();
    	for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
      		$language_id = $languages[$i]['id'];
      		$sql_data_marray[$i] = array('coupon_name' => tep_db_prepare_input($_POST['coupon_name'][$language_id]),
                             			 'coupon_description' => tep_db_prepare_input($_POST['coupon_desc'][$language_id])
                             			);
		}
		
    	if ($_GET['oldaction']=='voucheredit') {
      		tep_db_perform(TABLE_COUPONS, $sql_data_array, 'update', "coupon_id='" . tep_db_input($_GET['cid']) . "'"); 
      		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
      			$language_id = $languages[$i]['id'];
        		$update = tep_db_query("update " . TABLE_COUPONS_DESCRIPTION . " set coupon_name = '" . tep_db_prepare_input($_POST['coupon_name'][$language_id]) . "', coupon_description = '" . tep_db_prepare_input($_POST['coupon_desc'][$language_id]) . "' where coupon_id = '" . tep_db_input($_GET['cid']) . "' and language_id = '" . $language_id . "'");
      		}
      		$insert_id = $_GET['cid'];
			$status_str = 'Update discount code.'."\n".$user_comments;
    	} else {   
      		$query = tep_db_perform(TABLE_COUPONS, $sql_data_array);
      		$insert_id = tep_db_insert_id($query);
      		
      		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
        		$language_id = $languages[$i]['id'];
        		$sql_data_marray[$i]['coupon_id'] = $insert_id;
        		$sql_data_marray[$i]['language_id'] = $language_id;
        		tep_db_perform(TABLE_COUPONS_DESCRIPTION, $sql_data_marray[$i]);
      		}
			$status_str = 'Create new discount code.'."\n".$user_comments;
  		}
  		
		// create status history
		$status_array = array('coupon_id' => tep_db_prepare_input($insert_id),
	                          'coupon_active' => '',
	                          'date_added' => 'now()',
	                          'comments' => tep_db_prepare_input($status_str),
	                          'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
		tep_db_perform(TABLE_COUPONS_STATUS_HISTORY, $status_array);
		
  		tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, 'action=coupons_list&cgid='.$_GET['cgid'].'&cid='.$_GET['cid'].((isset($_GET['page']))? '&page='.$_GET['page']:'')));
    	
    	break;
	case 'generationupdate':
		if ($add_discount_code_generation_permission || $edit_discount_code_generation_permission) {
			// get all _POST and validate
			$languages = tep_get_languages();
	        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
	          	$language_id = $languages[$i]['id'];
	          	$_POST['coupon_name'][$language_id] = trim($_POST['coupon_name'][$language_id]);
	          	$_POST['coupon_desc'][$language_id] = trim($_POST['coupon_desc'][$language_id]);
			}
	      	$_POST['coupon_amount'] = trim($_POST['coupon_amount']);
	      	$update_errors = 0;
	      	if (!$_POST['coupon_name'][$languages[0]['id']]) {
	        	$update_errors = 1;
	        	$messageStack->add(ERROR_NO_COUPON_NAME, 'error');
	      	}
	      	if (!$_POST['coupon_amount']) {
	        	$update_errors = 1;
	        	$messageStack->add(ERROR_NO_COUPON_AMOUNT, 'error');
	      	}
	      	$coupon_code_prefix = (isset($_POST['coupon_code_prefix']) && tep_not_null($_POST['coupon_code_prefix'])) ? trim($_POST['coupon_code_prefix']): '';
	      	$coupon_code_suffix = (isset($_POST['coupon_code_suffix']) && tep_not_null($_POST['coupon_code_suffix'])) ? trim($_POST['coupon_code_suffix']): '';
	      	
	      	if (!isset($_POST['coupon_number']) || (!isset($_POST['coupon_uses_coupon']) || empty($_POST['coupon_uses_coupon'])) || (!isset($_POST['coupon_uses_user']) || empty($_POST['coupon_uses_user']))) {
	      		if (!isset($_POST['coupon_number'])) {
	      			$update_errors = 1;
	      			$messageStack->add(ERROR_COUPON_NUMBER, 'error');
	      		}
	      		if ((!isset($_POST['uses_per_coupon_unlimited']) || $_POST['uses_per_coupon_unlimited']=='N') && (!isset($_POST['coupon_uses_coupon']) || empty($_POST['coupon_uses_coupon']))) {
	      			$update_errors = 1;
	      			$messageStack->add(ERROR_COUPON_USES_COUPON, 'error');
	      		}
	      		if ((!isset($_POST['uses_per_user_unlimited']) || $_POST['uses_per_user_unlimited']=='N') && (!isset($_POST['coupon_uses_user']) || empty($_POST['coupon_uses_user']))) {
	      			$update_errors = 1;
	      			$messageStack->add(ERROR_COUPON_USES_USER, 'error');
	      		}
	      	} else {
	      		if ($_POST['coupon_number'] <= 0) {
	      			$update_errors = 1;
	      			$messageStack->add(ERROR_COUPON_NUMBER, 'error');
	      		}
	      		if ($_POST['coupon_number'] > MAX_COUPON_NUMBER) {
	      			$update_errors = 1;
	      			$messageStack->add(sprintf(ERROR_COUPON_NUMBER_MAX, MAX_COUPON_NUMBER), 'error');
	      		}
	      		if ($_POST['coupon_uses_coupon'] < $_POST['coupon_uses_user']) {
	      			$update_errors = 1;
	      			$messageStack->add(ERROR_COUPON_USES_ERROR, 'error');
	      		}
	      	}
	      	
			$coupon_startdate = mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'],$_POST['coupon_startdate_year']);
			$coupon_finishdate = mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'],$_POST['coupon_finishdate_year']);
      		if ($coupon_finishdate < $coupon_startdate) {
	      		$update_errors = 1;
	      		$messageStack->add(ERROR_COUPON_EXPIRY_OVER_START_DATE, 'error');
	      	}
	      	
	      	if ($update_errors != 0) {
	        	$_GET['action'] = $_GET['oldaction'];  
	      	} else {  
	        	$_GET['action'] = 'generation_preview';
	      	}
	    } else {
      		$update_errors = 1;
      		$messageStack->add(ERROR_NO_EDIT_PERMISSION, 'error');
        	$_GET['action']='';
	    }
      	break;
	case 'generation_confirm':
		if ($add_discount_code_generation_permission || $edit_discount_code_generation_permission) {
			$user_comments = tep_db_prepare_input($_POST['comments']);
        	$coupon_type = "F";
        	if (substr($_POST['coupon_amount'], -1) == '%') $coupon_type='P';
        	
        	if (!isset($_POST['uses_per_coupon_unlimited']) || (isset($_POST['uses_per_coupon_unlimited']) && $_POST['uses_per_coupon_unlimited']=='')) {
        		$_POST['uses_per_coupon_unlimited'] = 'N';
        	}
        	
        	if (!isset($_POST['uses_per_user_unlimited']) || (isset($_POST['uses_per_user_unlimited']) && $_POST['uses_per_user_unlimited']=='')) {
        		$_POST['uses_per_user_unlimited'] = 'N';
        	}
	    	
	    	$restrict_to_customers_groups = array();
	    	foreach ($_POST['coupon_customers_groups_id'] as $groups) {
	    		$restrict_to_customers_groups[] = $groups;
	    	}
        	
	        $sql_data_array = array('coupon_amount' => tep_db_prepare_input($_POST['coupon_amount']),
	                                'coupon_type' => tep_db_prepare_input($coupon_type),
	                                'coupon_code_prefix' => tep_db_prepare_input($_POST['coupon_code_prefix']),
	                                'coupon_code_suffix' => tep_db_prepare_input($_POST['coupon_code_suffix']),
	                                'coupon_number' => tep_db_prepare_input($_POST['coupon_number']),
	                                'uses_per_coupon' => tep_db_prepare_input($_POST['coupon_uses_coupon']),
	                                'uses_per_coupon_unlimited' => tep_db_prepare_input($_POST['uses_per_coupon_unlimited']),
	                                'uses_per_user' => tep_db_prepare_input($_POST['coupon_uses_user']),
	                                'uses_per_user_unlimited' => tep_db_prepare_input($_POST['uses_per_user_unlimited']),
	                                'coupon_minimum_order' => tep_db_prepare_input($_POST['coupon_min_order']),
	                                'restrict_to_products' => tep_db_prepare_input($_POST['coupon_products']),
	                                'restrict_to_categories' => tep_db_prepare_input($_POST['coupon_categories']),
	                                'restrict_to_customers_groups' => tep_db_prepare_input(implode(',', $restrict_to_customers_groups)),
	                                'coupon_start_date' => $_POST['coupon_startdate'],
	                                'coupon_expire_date' => $_POST['coupon_finishdate'],
	                                'coupon_generation_status' => tep_db_prepare_input($_POST['coupon_generation_status']),
	                                'date_modified' => "now()");
	        
        	$languages = tep_get_languages();
        	for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
          		$language_id = $languages[$i]['id'];
          		$sql_data_marray[$i] = array('coupon_generation_name' => tep_db_prepare_input($_POST['coupon_name'][$language_id]),
                                 			 'coupon_generation_description' => tep_db_prepare_input($_POST['coupon_desc'][$language_id])
                                 			);
			}
			
			if (isset($_GET['cgid']) && $_GET['cgid'] > 0) {
				$insert_id = tep_db_input($_GET['cgid']);
				$query = tep_db_perform(TABLE_COUPONS_GENERATION, $sql_data_array, 'update', "coupon_generation_id='".tep_db_input($insert_id)."'");
				$status_str = 'Update discount code generation.'."\n".$user_comments;
			} else {
				$sql_data_array['date_created'] = "now()";
				$sql_data_array['requester_id'] = tep_db_prepare_input($_SESSION['login_id']);
     			$query = tep_db_perform(TABLE_COUPONS_GENERATION, $sql_data_array);
      			$insert_id = tep_db_insert_id($query);
				$status_str = 'Create discount code generation.'."\n".$user_comments;
      		}
          	
      		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
      			if (isset($_GET['cgid']) && $_GET['cgid'] > 0) {
	        		$language_id = $languages[$i]['id'];
	        		$sql_data_marray[$i]['coupon_generation_id'] = $insert_id;
	        		$sql_data_marray[$i]['language_id'] = $language_id;
	        		tep_db_perform(TABLE_COUPONS_GENERATION_DESCRIPTION, $sql_data_marray[$i], 'update', "coupon_generation_id='".tep_db_input($insert_id)."' and language_id='".$language_id."'");
      			} else {
	        		$language_id = $languages[$i]['id'];
	        		$sql_data_marray[$i]['coupon_generation_id'] = $insert_id;
	        		$sql_data_marray[$i]['language_id'] = $language_id;
	        		tep_db_perform(TABLE_COUPONS_GENERATION_DESCRIPTION, $sql_data_marray[$i]);
	        	}
      		}
      		
			// create status history
			$status_array = array('coupons_generation_id' => tep_db_prepare_input($insert_id),
	                              'coupons_generation_status' => (isset($_GET['cgid']) && $_GET['cgid'] > 0 ? '' : 'P'),
	                              'date_added' => 'now()',
	                              'comments' => tep_db_prepare_input($status_str),
	                              'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
			tep_db_perform(TABLE_COUPONS_GENERATION_STATUS_HISTORY, $status_array);
      		
      		tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, 'cgid='.$_GET['cgid']));
	    } else {
      		$update_errors = 1;
      		$messageStack->add(ERROR_NO_EDIT_PERMISSION, 'error');
        	$_GET['action']='';
	    }
    	break;
	case 'generationapprove':
		if ($approve_discount_code_generation_permission) {
			if (isset($_GET['cgid']) && $_GET['cgid'] > 0) {
				$date_salt = date('YmdHis');  // using current datetime as salt when generating discount code
				
				$coupon_query=tep_db_query("SELECT * FROM " . TABLE_COUPONS_GENERATION . " WHERE coupon_generation_id = '" . tep_db_input($_GET['cgid']) . "'");
				if ($coupon_generation_row = tep_db_fetch_array($coupon_query)) {
					// get discount code generation names and descriptions, to be used by discount codes
					$coupon_desc = array();
					$coupon_desc_query = tep_db_query("select coupon_generation_name, coupon_generation_description, language_id from " . TABLE_COUPONS_GENERATION_DESCRIPTION . " where coupon_generation_id = '" .  tep_db_input($_GET['cgid']) . "'");
					while ($coupon_desc_row = tep_db_fetch_array($coupon_desc_query)) {
						$coupon_desc[$coupon_desc_row['language_id']] = array(	'coupon_name' => $coupon_desc_row['coupon_generation_name'],
																				'coupon_description' => $coupon_desc_row['coupon_generation_description']);
					}
					
					// calculate so far how many discount codes has been generated for this discount code generation
					$generated_query = tep_db_query("SELECT COUNT(coupon_id) AS generated_count FROM " . TABLE_COUPONS . " WHERE coupon_generation_id = '" . tep_db_input($_GET['cgid']) . "'");
					$generated_row = tep_db_fetch_array($generated_query);
					$generated_count = $generated_row['generated_count'];
					
					// change ini setting to allow the script to finish execution without timeout
					// if discount codes to be generated excess 3000 count
					if (($coupon_generation_row['coupon_number']-$generated_count) > 3000) {
						$ori_mem_limit = ini_get("memory_limit"); // keep original memory limit setting
						$ini_reset = true;
						
						tep_set_time_limit(0);
					}
					
					for ($i=$generated_count+1; $i <= $coupon_generation_row['coupon_number']; $i++) {
						// generate discount code with prefix, suffix, running number, and salt
						$check_duplicate=true;
						while ($check_duplicate) {
							$new_coupon_code = strtoupper(generate_discount_code($coupon_generation_row['coupon_code_prefix'], $coupon_generation_row['coupon_code_suffix'], $i, $date_salt));
							$codecheck_query = tep_db_query("SELECT coupon_id FROM " . TABLE_COUPONS . " WHERE coupon_code='".$new_coupon_code."'");
							if (tep_db_num_rows($codecheck_query) == 0) {
								$check_duplicate=false;
							}
						}
						if ($new_coupon_code) {
							// create discount code record with newly generated discount code
							$coupon_array = array('coupon_generation_id' => tep_db_prepare_input($_GET['cgid']),
												  'coupon_type' => tep_db_prepare_input($coupon_generation_row['coupon_type']),
												  'coupon_code' => tep_db_prepare_input($new_coupon_code),
												  'coupon_amount' => tep_db_prepare_input($coupon_generation_row['coupon_amount']),
												  'coupon_minimum_order' => tep_db_prepare_input($coupon_generation_row['coupon_minimum_order']),
												  'coupon_start_date' => tep_db_prepare_input($coupon_generation_row['coupon_start_date']),
												  'coupon_expire_date' => tep_db_prepare_input($coupon_generation_row['coupon_expire_date']),
												  'uses_per_coupon' => tep_db_prepare_input($coupon_generation_row['uses_per_coupon']),
												  'uses_per_coupon_unlimited' => tep_db_prepare_input($coupon_generation_row['uses_per_coupon_unlimited']),
												  'uses_per_user' => tep_db_prepare_input($coupon_generation_row['uses_per_user']),
												  'uses_per_user_unlimited' => tep_db_prepare_input($coupon_generation_row['uses_per_user_unlimited']),
												  'restrict_to_products' => tep_db_prepare_input($coupon_generation_row['restrict_to_products']),
												  'restrict_to_categories' => tep_db_prepare_input($coupon_generation_row['restrict_to_categories']),
												  'restrict_to_customers' => tep_db_prepare_input($coupon_generation_row['restrict_to_customers']),
												  'restrict_to_customers_groups' => tep_db_prepare_input($coupon_generation_row['restrict_to_customers_groups']),
												  'coupon_active' => 'P',
												  'date_created' => 'now()',
												  'date_modified' => 'now()');
							tep_db_perform(TABLE_COUPONS, $coupon_array);
							$insert_id = tep_db_insert_id($query);
							
							// create discount code status history
							$status_array = array('coupon_id' => tep_db_prepare_input($insert_id),
												  'coupon_active' => 'P',
												  'date_added' => 'now()',
												  'comments' => 'New discount code generated.',
												  'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
							tep_db_perform(TABLE_COUPONS_STATUS_HISTORY, $status_array);
							
							// create discount code description records
							foreach ($coupon_desc as $lang_id => $data_array) {
								$desc_data = array(	'coupon_id' => tep_db_prepare_input($insert_id),
													'language_id' => tep_db_prepare_input($lang_id),
													'coupon_name' => tep_db_prepare_input($data_array['coupon_name']),
													'coupon_description' => tep_db_prepare_input($data_array['coupon_description']));
								tep_db_perform(TABLE_COUPONS_DESCRIPTION, $desc_data);
								unset($desc_data);
							}
							unset($coupon_array);
							unset($status_array);
						}
					}
					
					// counter checking total number of discount codes has been generated
					$count_check_query = tep_db_query("SELECT COUNT(coupon_id) AS count_check FROM " . TABLE_COUPONS . " WHERE coupon_generation_id = '" . tep_db_input($_GET['cgid']) . "'");
					$count_check_row = tep_db_fetch_array($count_check_query);
					if (($count_check_row['count_check']*1) == ($coupon_generation_row['coupon_number']*1)) {
						// update discount codes generation status
						$status_query = tep_db_query("UPDATE " . TABLE_COUPONS_GENERATION . " SET coupon_generation_status = 'Y', date_modified = now() WHERE coupon_generation_id='" . tep_db_input($_GET['cgid']) . "'");
	      				
						// create discount code generation status history
						$user_comments = urldecode($_GET['comments']);
						$comments = 'Approve discount code generation.' . "\n" . $user_comments;
						$status_array = array('coupons_generation_id' => tep_db_prepare_input($_GET['cgid']),
											  'coupons_generation_status' => 'Y',
											  'date_added' => 'now()',
											  'comments' => tep_db_prepare_input($comments),
											  'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
						tep_db_perform(TABLE_COUPONS_GENERATION_STATUS_HISTORY, $status_array);
						
						// send email notification to discount codes generation owner
						$admin_email_select_sql = "	SELECT admin_firstname, admin_lastname, admin_email_address 
													FROM " . TABLE_ADMIN . " 
													WHERE admin_id='" . $coupon_generation_row['requester_id'] . "'";
						$admin_email_select_result = tep_db_query($admin_email_select_sql);
						$admin_email_row = tep_db_fetch_array($admin_email_select_result);
						tep_mail($admin_email_row['admin_firstname'].' '.$admin_email_row['admin_lastname'], $admin_email_row['admin_email_address'], EMAIL_APPROVAL_SUBJECT_TEXT, sprintf(EMAIL_APPROVAL_BODY_TEXT, $coupon_desc[1]['coupon_generation_name'])."\n".$comments, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					} else {
						$messageStack->add_session('Error occur - Discount code is partially generated.', 'error');
					}
					/*
					// reset back the ini setting to its original state
					if (isset($ini_reset) && $ini_reset == true) {
						ini_set("memory_limit", $ori_mem_limit);
						tep_set_time_limit(30);
					}
					*/
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, ((isset($_GET['page']))? 'page='.$_GET['page']:'')));
		} else {
      		$update_errors = 1;
      		$messageStack->add(ERROR_NO_APPROVE_PERMISSION, 'error');
        	$_GET['action']='';
		}
		break;
	case 'generationcancel':
  	$delete_query=tep_db_query("UPDATE " . TABLE_COUPONS_GENERATION . " SET coupon_generation_status = 'N', date_modified = now() WHERE coupon_generation_id='" . tep_db_input($_GET['cgid']) . "'");
		
		// create status history
		$user_comments = urldecode($_GET['comments']);
		$comments = 'Cancel discount code generation.' . "\n" . $user_comments;
		$status_array = array('coupons_generation_id' => tep_db_prepare_input($_GET['cgid']),
							  'coupons_generation_status' => 'N',
							  'date_added' => 'now()',
							  'comments' => tep_db_prepare_input($comments),
							  'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']));
		tep_db_perform(TABLE_COUPONS_GENERATION_STATUS_HISTORY, $status_array);
		
		// send email notification to discount codes generation owner
  	$requester_sql = "SELECT cg.requester_id, cgd.coupon_generation_name 
  						FROM " . TABLE_COUPONS_GENERATION . " AS cg 
  						INNER JOIN " . TABLE_COUPONS_GENERATION_DESCRIPTION . " AS cgd
  							ON (cgd.coupon_generation_id=cg.coupon_generation_id AND cgd.language_id='" . $_SESSION['languages_id'] . "') 
  						WHERE cg.coupon_generation_id='" . tep_db_input($_GET['cgid']) . "'";
  	$requester_query = tep_db_query($requester_sql);
  	$requester_row = tep_db_fetch_array($requester_query);
      	
		$admin_email_select_sql = "	SELECT admin_firstname, admin_lastname, admin_email_address 
									FROM " . TABLE_ADMIN . " 
									WHERE admin_id='" . $requester_row['requester_id'] . "'";
		$admin_email_select_result = tep_db_query($admin_email_select_sql);
		$admin_email_row = tep_db_fetch_array($admin_email_select_result);
		tep_mail($admin_email_row['admin_firstname'].' '.$admin_email_row['admin_lastname'], $admin_email_row['admin_email_address'], EMAIL_CANCEL_SUBJECT_TEXT, sprintf(EMAIL_CANCEL_BODY_TEXT, $requester_row['coupon_generation_name'])."\n".$comments, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		
		tep_redirect(tep_href_link(FILENAME_COUPON_ADMIN, ((isset($_GET['page']))? 'page='.$_GET['page']:'')));
		break;
}

$customers_groups_array = array('ALL' => array('text' => 'ALL', 'id' => 'ALL'));
$groups_query = tep_db_query("select customers_groups_id, customers_groups_name from " . TABLE_CUSTOMERS_GROUPS ." order by sort_order, customers_groups_name ASC");
while($groups = tep_db_fetch_array($groups_query)) {
	$customers_groups_array[$groups['customers_groups_id']] = array('text' => $groups['customers_groups_name'], 'id' => $groups['customers_groups_id']);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
<script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<script language="javascript" src="includes/javascript/modal_win.js"></script>
<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
<script language="javascript">
	jQuery.noConflict();
	var dateAvailable = new ctlSpiffyCalendarBox("dateAvailable", "new_product", "products_date_available","btnDate1","<?php echo $pInfo->products_date_available; ?>",scBTNMODE_CUSTOMBLUE);
</script>
<script language="Javascript1.2"><!-- // load htmlarea
// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <head>
	_editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
	var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
	if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
	if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
	if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
	<?php if (HTML_AREA_WYSIWYG_BASIC_EMAIL == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
		document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
		document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
	<?php } else{ ?> if (win_ie_ver >= 5.5) {
		document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
		document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
	<?php }?>
// --></script>
<script language="JavaScript" src="htmlarea/validation.js"></script>
<script language="JavaScript">
<!-- Begin
	function init() {
		define('customers_email_address', 'string', 'Customer or Newsletter Group');
	}
//  End -->
</script>
</head>
<body OnLoad="init()" marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">

<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
			</table>
		</td>
<!-- body_text //-->
<?php 
switch ($_GET['action']) {
/* BEGIN VOUCHER REPORT */
	case 'voucherreport':
?>
		<td width="100%" valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td valign="top">
									<table border="0" width="100%" cellspacing="0" cellpadding="2">
										<tr class="dataTableHeadingRow">
											<td class="dataTableHeadingContent"><?php echo CUSTOMER_ID; ?></td>
											<td class="dataTableHeadingContent" align="center"><?php echo CUSTOMER_NAME; ?></td>	
											<td class="dataTableHeadingContent" align="center"><?php echo IP_ADDRESS; ?></td>	
											<td class="dataTableHeadingContent" align="center"><?php echo REDEEM_DATE; ?></td>	
											<td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
										</tr>
<?php
		$cc_query_raw = "select * from " . TABLE_COUPON_REDEEM_TRACK . " where coupon_id = '" . $_GET['cid'] . "'";
		$cc_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $cc_query_raw, $cc_query_numrows);
		$cc_query = tep_db_query($cc_query_raw);
		while ($cc_list = tep_db_fetch_array($cc_query)) {
			$rows++;
			if (strlen($rows) < 2) {
				$rows = '0' . $rows;
			}
			if (((!$_GET['uid']) || (@$_GET['uid'] == $cc_list['unique_id'])) && (!$cInfo)) {
				$cInfo = new objectInfo($cc_list);
			}
			if ( (is_object($cInfo)) && ($cc_list['unique_id'] == $cInfo->unique_id) ) {
				echo '										<tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link('coupon_admin.php', tep_get_all_get_params(array('cid', 'action', 'uid')) . 'cid=' . $cInfo->coupon_id . '&action=voucherreport&uid=' . $cinfo->unique_id) . '\'">' . "\n";
			} else {
				echo '										<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link('coupon_admin.php', tep_get_all_get_params(array('cid', 'action', 'uid')) . 'cid=' . $cc_list['coupon_id'] . '&action=voucherreport&uid=' . $cc_list['unique_id']) . '\'">' . "\n";
			}
			$customer_query = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . $cc_list['customer_id'] . "'");
			$customer = tep_db_fetch_array($customer_query);
?>
											<td class="dataTableContent"><?php echo $cc_list['customer_id']; ?></td>
											<td class="dataTableContent" align="center"><?php echo $customer['customers_firstname'] . ' ' . $customer['customers_lastname']; ?></td>
											<td class="dataTableContent" align="center"><?php echo $cc_list['redeem_ip']; ?></td>
											<td class="dataTableContent" align="center"><?php echo tep_date_short($cc_list['redeem_date'], PREFERRED_DATE_FORMAT); ?></td>
											<td class="dataTableContent" align="right"><?php if ( (is_object($cInfo)) && ($cc_list['unique_id'] == $cInfo->unique_id) ) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_COUPON_ADMIN, 'page=' . $_GET['page'] . '&cid=' . $cc_list['coupon_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
										</tr>
<?php
	}
?>
										<tr>
											<td class="smallText" colspan="2">&nbsp;<?php if (is_object($cc_split)) { echo $cc_split->display_count($cc_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_RECORDS); } ?>&nbsp;</td>
											<td align="right" class="smallText" colspan="3">&nbsp;<?php if (is_object($cc_split)) { echo $cc_split->display_links($cc_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page'))); } ?>&nbsp;</td>
										</tr>
									</table>
								</td>
<?php
		$heading = array();
		$contents = array();
		$coupon_description_query = tep_db_query("select coupon_name from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . $_GET['cid'] . "' and language_id = '" . $languages_id . "'");
		$coupon_desc = tep_db_fetch_array($coupon_description_query);
		$count_customers = tep_db_query("select * from " . TABLE_COUPON_REDEEM_TRACK . " where coupon_id = '" . $_GET['cid'] . "' and customer_id = '" . $cInfo->customer_id . "'");
		
		$coupon_count_select_sql = "SELECT COUNT(coupon_id) total_use FROM " . TABLE_COUPON_REDEEM_TRACK . " WHERE coupon_id = '" . $_GET['cid'] . "'";
		$coupon_count_result_sql = tep_db_query($coupon_count_select_sql);
		$coupon_count_row = tep_db_fetch_array($coupon_count_result_sql);
		
		$heading[] = array('text' => '<b>[' . $_GET['cid'] . ']' . COUPON_NAME . ' ' . $coupon_desc['coupon_name'] . '</b>');
		$contents[] = array('text' => '<b>' . TEXT_REDEMPTIONS . '</b>');
		$contents[] = array('text' => TEXT_REDEMPTIONS_TOTAL . '=' . $coupon_count_row['total_use']);
		$contents[] = array('text' => TEXT_REDEMPTIONS_CUSTOMER . '=' . tep_db_num_rows($count_customers));
		$contents[] = array('text' => '');
?>
								<td width="25%" valign="top">
<?php
		$box = new box;
		echo $box->infoBox($heading, $contents);
		echo '								</td>' . "\n";
?>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
<?php
		break;
/* END VOUCHER REPORT */
/* BEGIN PREVIEW EMAIL */
	case 'preview_email': 
		$coupon_query = tep_db_query("select coupon_code from " .TABLE_COUPONS . " where coupon_id = '" . $_GET['cid'] . "'");
		$coupon_result = tep_db_fetch_array($coupon_query);
		$coupon_name_query = tep_db_query("select coupon_name from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . $_GET['cid'] . "' and language_id = '" . $_SESSION['languages_id'] . "'");
		$coupon_name = tep_db_fetch_array($coupon_name_query);
		switch ($_POST['customers_email_address']) {
			case '***':
				$mail_sent_to = TEXT_ALL_CUSTOMERS;
				break;
			case '**D':
				$mail_sent_to = TEXT_NEWSLETTER_CUSTOMERS;
				break;
			default:
				$mail_sent_to = $_POST['customers_email_address'];
				break;
		}
?>
		<td width="100%" valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr><?php echo tep_draw_form('mail', FILENAME_COUPON_ADMIN, 'action=send_email_to_user&cid=' . $_GET['cid']); ?>
					<td>
						<table border="0" width="100%" cellpadding="0" cellspacing="2">
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="smallText"><b><?php echo TEXT_CUSTOMER; ?></b><br><?php echo $mail_sent_to; ?></td>
							</tr>
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="smallText"><b><?php echo TEXT_COUPON; ?></b><br><?php echo $coupon_name['coupon_name']; ?></td>
							</tr>
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="smallText"><b><?php echo TEXT_FROM; ?></b><br><?php echo htmlspecialchars(stripslashes($_POST['from'])); ?></td>
							</tr>
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="smallText"><b><?php echo TEXT_SUBJECT; ?></b><br><?php echo htmlspecialchars(stripslashes($_POST['subject'])); ?></td>
							</tr>
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="smallText"><b><?php if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { echo (stripslashes($_POST['message'])); } else { echo htmlspecialchars(stripslashes($_POST['message'])); } ?></td>
							</tr>
							<tr>
								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td>
<?php
/* Re-Post all POST'ed variables */
		reset($_POST);
		while (list($key, $value) = each($_POST)) {
			if (!is_array($_POST[$key])) {
				echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
			}
		}
?>
									<table border="0" width="100%" cellpadding="0" cellspacing="2">
										<tr>
											<td><?php ?>&nbsp;</td>
										</tr>
										<tr>
											<td align="right"><?php echo '<a href="' . tep_href_link(FILENAME_MAIL) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a> ' . tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL); ?></td>
										</tr>
										<tr>
											<td class="smallText">
											<?php 
											if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
												echo tep_image_submit('button_back.gif', IMAGE_BACK, 'name="back"');
											}
											if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
												echo(TEXT_EMAIL_BUTTON_HTML);
											} else { echo(TEXT_EMAIL_BUTTON_TEXT); }
											?>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
					</form>
				</tr>
			</table>
		</td>
<?php 
			break;       
/* END PREVIEW EMAIL */
/* BEGIN EMAIL */
	case 'email':
		$coupon_query = tep_db_query("select coupon_code from " . TABLE_COUPONS . " where coupon_id = '" . $_GET['cid'] . "'");
		$coupon_result = tep_db_fetch_array($coupon_query);
		$coupon_name_query = tep_db_query("select coupon_name from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . $_GET['cid'] . "' and language_id = '" . $languages_id . "'");
		$coupon_name = tep_db_fetch_array($coupon_name_query);
?>
		<td width="100%" valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr><?php echo tep_draw_form('mail', FILENAME_COUPON_ADMIN, 'action=preview_email&cid='. $_GET['cid']); ?>
					<td>
						<table border="0" cellpadding="0" cellspacing="2">
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
<?php
		$customers = array();
		$customers[] = array('id' => '', 'text' => TEXT_SELECT_CUSTOMER);
		$customers[] = array('id' => '***', 'text' => TEXT_ALL_CUSTOMERS);
		$customers[] = array('id' => '**D', 'text' => TEXT_NEWSLETTER_CUSTOMERS);
		$mail_query = tep_db_query("select customers_email_address, customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " order by customers_lastname");
		while($customers_values = tep_db_fetch_array($mail_query)) {
			$customers[] = array('id' => $customers_values['customers_email_address'],
							'text' => $customers_values['customers_lastname'] . ', ' . $customers_values['customers_firstname'] . ' (' . $customers_values['customers_email_address'] . ')');
		}
?>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="main"><?php echo TEXT_COUPON; ?>&nbsp;&nbsp;</td>
								<td><?php echo $coupon_name['coupon_name']; ?></td>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="main"><?php echo TEXT_CUSTOMER; ?>&nbsp;&nbsp;</td>
								<td><?php echo tep_draw_pull_down_menu('customers_email_address', $customers, $_GET['customer']);?></td>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td class="main"><?php echo TEXT_FROM; ?>&nbsp;&nbsp;</td>
								<td><?php echo tep_draw_input_field('from', EMAIL_FROM); ?></td>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
<?php
/*
							<tr>
								<td class="main"><?php echo TEXT_RESTRICT; ?>&nbsp;&nbsp;</td>
								<td><?php echo tep_draw_checkbox_field('customers_restrict', $customers_restrict);?></td>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
*/
?>
							<tr>
								<td class="main"><?php echo TEXT_SUBJECT; ?>&nbsp;&nbsp;</td>
								<td><?php echo tep_draw_input_field('subject'); ?></td>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td valign="top" class="main"><?php echo TEXT_MESSAGE; ?></td>
								<td><?php echo tep_draw_textarea_field('message', 'soft', '60', '15'); ?></td>
<?php
// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email - <body>
			if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { ?>
							<script language="JavaScript1.2" defer>
								var config = new Object();  // create new config object
								config.width = "<?php echo EMAIL_AREA_WYSIWYG_WIDTH; ?>px";
								config.height = "<?php echo EMAIL_AREA_WYSIWYG_HEIGHT; ?>px";
								config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
								config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
								editor_generate('message',config);
<?php
			}
// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <body>
?>
								</script>
							</tr>
							<tr>
								<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
							</tr>
							<tr>
								<td colspan="2" align="right">
			<?php if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable'){ echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL, 'onClick="validate();return returnVal;"');
				} else { echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL); }?>
								</td>
							</tr>
						</table>
					</form>
					</td>
				</tr>
			</table>
		</td>
<?php      
		break;
/* END EMAIL */
/* BEGIN UPDATE PREVIEW */
	case 'update_preview':
?>
		<td width="100%" valign="top">
	      	<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=EDIT_HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="6">
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
							<tr>
								<td align="left" width="30%"><?php echo COUPON_NAME." (".$languages[$i]['name'].")"; ?></td>
								<td align="left"><?php echo $_POST['coupon_name'][$language_id]; ?></td>
							</tr>
<?php
		}
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
							<tr>
								<td align="left"><?php echo COUPON_DESC." (".$languages[$i]['name'].")"; ?></td>
								<td align="left"><?php echo $_POST['coupon_desc'][$language_id]; ?></td>
							</tr>
<?php
		}
?>
							<tr>
								<td align="left"><?php echo COUPON_AMOUNT; ?></td>
								<td align="left"><?php echo $_POST['coupon_amount']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_MIN_ORDER; ?></td>
								<td align="left"><?php echo $_POST['coupon_min_order']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_CODE; ?></td>
								<td align="left"><?php echo $_POST['coupon_code']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_USES_COUPON; ?></td>
								<td align="left"><?php echo (($_POST['uses_per_coupon_unlimited']=='Y'||$_POST['uses_per_coupon_unlimited']=='on') ? "Unlimited" : $_POST['coupon_uses_coupon']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_USES_USER; ?></td>
								<td align="left"><?php echo (($_POST['uses_per_user_unlimited']=='Y'||$_POST['uses_per_user_unlimited']=='on') ? "Unlimited" : $_POST['coupon_uses_user']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_PRODUCTS; ?></td>
								<td align="left"><?php echo str_replace(' ', '', $_POST['coupon_products']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_CATEGORIES; ?></td>
								<td align="left"><?php echo str_replace(' ', '', $_POST['coupon_categories']); ?></td>
							</tr>
							<tr>
								<td align="left" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS; ?></td>
								<td align="left">
									<?php
									$cnt=0;
									foreach ($_POST['coupon_customers_groups_id'] as $item) {
										echo ($cnt>0 ? ',': '').$customers_groups_array[$item]['text'];
										$cnt++;
									}
									?>
								</td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_STARTDATE; ?></td>
<?php
		$start_date = date(DATE_FORMAT, mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] ));
?>
								<td align="left"><?php echo $start_date; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_FINISHDATE; ?></td>
<?php
		$finish_date = date(DATE_FORMAT, mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] ));
?>
								<td align="left"><?php echo $finish_date; ?></td>
							</tr>
							<script language="javascript">
								function get_comment() {
									var user_comment = prompt('Please provide your reason of change:','');
									if (user_comment != null) {
										document.getElementById('comments').value = user_comment;
										return true;
									} else {
										return false;
									}
								}
							</script>
							<tr>
<?php echo tep_draw_form('coupon', 'coupon_admin.php', 'action=update_confirm&oldaction=' . $_GET['oldaction'] . '&cid=' . $_GET['cid'] . '&cgid=' . $_GET['cgid']); ?>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			echo tep_draw_hidden_field('coupon_name[' . $languages[$i]['id'] . ']', $_POST['coupon_name'][$language_id]);
			echo tep_draw_hidden_field('coupon_desc[' . $languages[$i]['id'] . ']', $_POST['coupon_desc'][$language_id]);
		}
		echo tep_draw_hidden_field('coupon_amount', $_POST['coupon_amount']);
		echo tep_draw_hidden_field('coupon_min_order', $_POST['coupon_min_order']);
		echo tep_draw_hidden_field('coupon_uses_coupon', $_POST['coupon_uses_coupon']);
		echo tep_draw_hidden_field('uses_per_coupon_unlimited', (($_POST['uses_per_coupon_unlimited']=='on'||$_POST['uses_per_coupon_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_uses_user', $_POST['coupon_uses_user']);
		echo tep_draw_hidden_field('uses_per_user_unlimited', (($_POST['uses_per_user_unlimited']=='on'||$_POST['uses_per_user_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_products', str_replace(' ', '', $_POST['coupon_products']));
		echo tep_draw_hidden_field('coupon_categories', str_replace(' ', '', $_POST['coupon_categories']));
		foreach ($_POST['coupon_customers_groups_id'] as $item) {
			echo tep_draw_hidden_field('coupon_customers_groups_id[]', str_replace(' ', '', $item));
		}
		echo tep_draw_hidden_field('coupon_startdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] )));
		echo tep_draw_hidden_field('coupon_finishdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] )));
		echo tep_draw_hidden_field('coupon_active', $_POST['coupon_active']);
		echo tep_draw_hidden_field('comments', '', 'id="comments"');
?>
									<td align="left"><?php echo tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, 'onClick="return get_comment();"', 'inputButton'); ?></td>
								</form>
<?php echo tep_draw_form('couponback', 'coupon_admin.php', 'action=voucherback&oldaction=' . $_GET['action'] . '&cid=' . $_GET['cid'] . '&cgid=' . $_GET['cgid']); ?>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			echo tep_draw_hidden_field('coupon_name[' . $languages[$i]['id'] . ']', $_POST['coupon_name'][$language_id]);
			echo tep_draw_hidden_field('coupon_desc[' . $languages[$i]['id'] . ']', $_POST['coupon_desc'][$language_id]);
		}
		echo tep_draw_hidden_field('coupon_amount', $_POST['coupon_amount']);
		echo tep_draw_hidden_field('coupon_min_order', $_POST['coupon_min_order']);
		echo tep_draw_hidden_field('coupon_code', $_POST['coupon_code']);
		echo tep_draw_hidden_field('coupon_uses_coupon', $_POST['coupon_uses_coupon']);
		echo tep_draw_hidden_field('uses_per_coupon_unlimited', (($_POST['uses_per_coupon_unlimited']=='on'||$_POST['uses_per_coupon_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_uses_user', $_POST['coupon_uses_user']);
		echo tep_draw_hidden_field('uses_per_user_unlimited', (($_POST['uses_per_user_unlimited']=='on'||$_POST['uses_per_user_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_products', str_replace(' ', '', $_POST['coupon_products']));
		echo tep_draw_hidden_field('coupon_categories', str_replace(' ', '', $_POST['coupon_categories']));
		foreach ($_POST['coupon_customers_groups_id'] as $item) {
			echo tep_draw_hidden_field('coupon_customers_groups_id[]', str_replace(' ', '', $item));
		}
		echo tep_draw_hidden_field('coupon_startdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] )));
		echo tep_draw_hidden_field('coupon_finishdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] )));
		echo tep_draw_hidden_field('coupon_active', $_POST['coupon_active']);
?>
									<td align="left"><?php echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton'); ?></td>
								</form>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
<?php      
		break;
/* END UPDATE PREVIEW */
/* BEGIN DISCOUNT CODE EDIT */
	case 'voucheredit':
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			$coupon_query = tep_db_query("select coupon_name,coupon_description from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" .  tep_db_input($_GET['cid']) . "' and language_id = '" . $language_id . "'");
			$coupon = tep_db_fetch_array($coupon_query);
			$coupon_name[$language_id] = $coupon['coupon_name'];
			$coupon_desc[$language_id] = $coupon['coupon_description'];
		}
		
		$coupon_query=tep_db_query("select * from " . TABLE_COUPONS . " where coupon_id = '" . tep_db_input($_GET['cid']) . "'");
		$coupon=tep_db_fetch_array($coupon_query);
		$coupon_amount = $coupon['coupon_amount'];
		if ($coupon['coupon_type']=='P') {
			$coupon_amount .= '%';
		}
		if ($coupon['coupon_type']=='S') {
			$coupon_free_ship .= true;
		}
		
		$coupon_min_order = $coupon['coupon_minimum_order'];
		$coupon_code = $coupon['coupon_code'];
		$coupon_uses_coupon = $coupon['uses_per_coupon'];
		$coupon_uses_user = $coupon['uses_per_user'];
		$uses_per_coupon_unlimited = $coupon['uses_per_coupon_unlimited'];
		$uses_per_user_unlimited = $coupon['uses_per_user_unlimited'];
		$coupon_products = $coupon['restrict_to_products'];
		$coupon_categories = $coupon['restrict_to_categories'];
		$coupon_customers_groups_id = (tep_not_null($coupon['restrict_to_customers_groups'])? $coupon['restrict_to_customers_groups'] : NULL);
		$edit_startdate = $coupon['coupon_start_date'];
		$edit_finishdate = $coupon['coupon_expire_date'];
		$coupon_active = $coupon['coupon_active'];
/* END DISCOUNT CODE EDIT */
/* BEGIN DISCOUNT CODE BACK */
	case 'voucherback':
		if ($action == 'voucherback') {
			$languages = tep_get_languages();
			for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
				$language_id = $languages[$i]['id'];
				$coupon_name[$language_id] = $_POST['coupon_name'][$language_id];
				$coupon_desc[$language_id] = $_POST['coupon_desc'][$language_id];
			}
			
			$coupon_amount = $_POST['coupon_amount'];
			$coupon_min_order = $_POST['coupon_min_order'];
			$coupon_code = $_POST['coupon_code'];
			$coupon_uses_coupon = $_POST['coupon_uses_coupon'];
			$coupon_uses_user = $_POST['coupon_uses_user'];
			$uses_per_coupon_unlimited = $_POST['uses_per_coupon_unlimited'];
			$uses_per_user_unlimited = $_POST['uses_per_user_unlimited'];
			$coupon_products = $_POST['coupon_products'];
			$coupon_categories = $_POST['coupon_categories'];
			$coupon_customers_groups_id = implode(',', $_POST['coupon_customers_groups_id']);
			$edit_startdate = $_POST['coupon_startdate'];
			$edit_finishdate = $_POST['coupon_finishdate'];
			$coupon_active = $_POST['coupon_active'];
		}
/* END DISCOUNT CODE BACK */
/* BEGIN NEW DISCOUNT CODE */
	//case 'new':	// <--- can not create new individual discount code
		// set some defaults
		if (!$coupon_active) $coupon_active = 'P';
		if (!tep_not_null($uses_per_coupon_unlimited)) $uses_per_coupon_unlimited = 'N';
		if (!tep_not_null($uses_per_user_unlimited)) $uses_per_user_unlimited = 'N';
		$coupon_uses_coupon = ($uses_per_coupon_unlimited=='Y'||$uses_per_coupon_unlimited=='on')? '0': $coupon_uses_coupon;
		$coupon_uses_user = ($uses_per_user_unlimited=='Y'||$uses_per_user_unlimited=='on')? '0': $coupon_uses_user;
?>
		<td width="100%" valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=EDIT_HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
<?php 
		echo tep_draw_form('coupon', 'coupon_admin.php', 'action=update&oldaction='.$_GET['action'] . '&cid=' . $_GET['cid'] . '&cgid=' . $_GET['cgid']); 
		echo tep_draw_hidden_field('coupon_active', $coupon_active);
		echo tep_draw_hidden_field('coupon_code', $coupon_code);
?>
						<table border="0" width="100%" cellspacing="0" cellpadding="6">
							<tr>
								<td align="left" class="main"><?php echo COUPON_STATUS; ?></td>
								<td align="left" class="main"><b>
<?php
		switch ($coupon_active) {
			case "P":
				echo "Pending";
				break;
			case "Y":
				echo "Active";
				break;
			case "N":
				echo "Closed";
				break;
			case "D":
				echo "Deleted";
				break;
		}
?>
								</b></td>
								<td align="left" class="main"></td>
							</tr>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td colspan="3">
									<table border="0" width="58%" cellspacing="0" cellpadding="0">
										<tr>
											<td>
												<div id="languages_tab">
													<ul>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			echo '													<li id="languages_li_' . $i . '"><a href="#languages_tab_'.$i.'"><span>'.$languages[$i]['name'].'</span></a></li>';
		}
?>
													</ul>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
													<div id="languages_tab_<?=$i?>" class="languages_tab">
														<table border="0" width="100%" cellspacing="0" cellpadding="6">
															<tr>
																<td align="left" class="main"><?php echo COUPON_NAME; ?></td>
																<td align="left" class="main"><?php echo tep_draw_input_field('coupon_name[' . $languages[$i]['id'] . ']', $coupon_name[$language_id]); ?></td>
																<td align="left" class="main" width="40%"><?php echo COUPON_NAME_HELP; ?></td>
															</tr>
															<tr>
																<td align="left" valign="top" class="main"><?php echo COUPON_DESC; ?></td>
																<td align="left" valign="top" class="main"><?php echo tep_draw_textarea_field('coupon_desc[' . $languages[$i]['id'] . ']','physical','24','3', $coupon_desc[$language_id]); ?></td>
																<td align="left" valign="top" class="main"><?php echo COUPON_DESC_HELP; ?></td>
															</tr>
														</table>
													</div>
<?php
		}
?>
												</div><!--languages_tab-->
												<script language="javascript">
													jQuery("#languages_tab > ul").tabs();
													jQuery('.languages_tab').css({
														border:'1px solid #C9C9C9'
													});
													
													function coupon_uses_check_selection (check_obj, target_name) {
														if (check_obj != null) {
															var check_mode = check_obj.checked ? true : false;
															var target_object = document.getElementById(target_name);
															target_object.disabled = check_mode;
															
															var per_coupon_check_object = document.getElementById('uses_per_coupon_unlimited');
															var per_user_check_object = document.getElementById('uses_per_user_unlimited');
															var per_coupon_object = document.getElementById('coupon_uses_coupon');
															var per_user_object = document.getElementById('coupon_uses_user');
															
															if (check_obj.id == "uses_per_user_unlimited") {
																if (check_obj.checked == true) {
																	var chain_target_check_object = document.getElementById('uses_per_coupon_unlimited');
																	var chain_target_text_object = document.getElementById('coupon_uses_coupon');
																	chain_target_check_object.checked = check_obj.checked;
																	chain_target_text_object.disabled = check_obj.checked;
																	per_coupon_object.value = '0';
																	per_user_object.value = '0';
																}
															}
															
															else if (check_obj.id == "uses_per_coupon_unlimited") {
																if (check_obj.checked == true) {
																	per_coupon_object.value = '0';
																}
																
																if (check_obj.checked == false) {
																	var target_text_object = document.getElementById('coupon_uses_coupon');
																	var chain_target_check_object = document.getElementById('uses_per_user_unlimited');
																	// if uses per customer unlimited is still remain as checked, dont allow uncheck this
																	if (chain_target_check_object.checked == true) {
																		check_obj.checked = true;
																		target_text_object.disabled = true;
																	}
																}
															}
														}
													}
													
													function showHideLogHistory(log_div, log_link_div, classtype) {
														DOMCall(log_div).className = classtype;
														
														if (classtype == 'show') {
															DOMCall(log_link_div).innerHTML = '[<a href="javascript:;" onclick="showHideLogHistory(\''+log_div+'\', \''+log_link_div+'\', \'hide\');">'+'<?=LINK_HIDE_INFO_HISTORY_BOX?>'+'</a>]';
														} else {
															DOMCall(log_link_div).innerHTML = '[<a href="javascript:;" onclick="showHideLogHistory(\''+log_div+'\', \''+log_link_div+'\', \'show\');">'+'<?=LINK_SHOW_INFO_HISTORY_BOX?>'+'</a>]';
														}
													}
													
													function getReturnedValue(received_val, fieldname) {
														var ori_list = DOMCall(fieldname).value;
														var add_str = '';
														
														if (ori_list.length > 0) {
															add_str = ',';
														}
														DOMCall(fieldname).value = ori_list + add_str + received_val;
													}
													
													function toggleChecks(cbElem) {
														var f = cbElem.form;
														for (var elem, i = 0; (elem = f.elements[i]); i++) {
															if (elem.type == 'checkbox' && elem != cbElem && elem.id == 'coupon_customers_groups_id') {
																elem.checked = (cbElem.checked==true)?!cbElem.checked:false;
																elem.disabled = cbElem.checked;
															}
														}
													}
												</script>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_AMOUNT; ?></td>
								<td align="left"><?php echo tep_draw_input_field('coupon_amount', $coupon_amount); ?></td>
								<td align="left" class="main"><?php echo COUPON_AMOUNT_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_MIN_ORDER; ?></td>
								<td align="left"><?php echo tep_draw_input_field('coupon_min_order', $coupon_min_order); ?></td>
								<td align="left" class="main"><?php echo COUPON_MIN_ORDER_HELP; ?></td>
							</tr>
							<!--tr>
								<td align="left" class="main"><?php echo COUPON_FREE_SHIP; ?></td>
								<td align="left"><?php echo tep_draw_checkbox_field('coupon_free_ship', $coupon_free_ship); ?></td>
								<td align="left" class="main"><?php echo COUPON_FREE_SHIP_HELP; ?></td>
							</tr-->
							<tr>
								<td align="left" class="main"><?php echo COUPON_CODE; ?></td>
								<td align="left"><?php echo $coupon_code; ?></td>
								<td align="left" class="main"><?php echo COUPON_CODE_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_USES_COUPON; ?></td>
								<td align="left" class="main"><?php echo tep_draw_input_field('coupon_uses_coupon', $coupon_uses_coupon, 'id="coupon_uses_coupon" '.($uses_per_coupon_unlimited=='Y'? 'disabled="disabled"': '').' onChange="calculate_max_usage()"'); echo tep_draw_checkbox_field("uses_per_coupon_unlimited", (($uses_per_coupon_unlimited=='Y') ? $uses_per_coupon_unlimited : ''), (($uses_per_coupon_unlimited=='Y') ? true : false), '', 'id="uses_per_coupon_unlimited" onClick="coupon_uses_check_selection(this,\'coupon_uses_coupon\')"'); echo "&nbsp;".TEXT_UNLIMITED; ?></td>
								<td align="left" class="main"><?php echo COUPON_USES_COUPON_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_USES_USER; ?></td>
								<td align="left" class="main"><?php echo tep_draw_input_field('coupon_uses_user', $coupon_uses_user, 'id="coupon_uses_user" '.($uses_per_user_unlimited=='Y'? 'disabled="disabled"': '')); echo tep_draw_checkbox_field("uses_per_user_unlimited", (($uses_per_user_unlimited=='Y') ? $uses_per_user_unlimited : ''), (($uses_per_user_unlimited=='Y') ? true : false), '', 'id="uses_per_user_unlimited" onClick="coupon_uses_check_selection(this,\'coupon_uses_user\')"'); echo "&nbsp;".TEXT_UNLIMITED; ?></td>
								<td align="left" class="main"><?php echo COUPON_USES_USER_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_PRODUCTS; ?></td>
								<td align="left">
<?php
		echo tep_draw_input_field('coupon_products', $coupon_products, 'id="coupon_products"');
		echo '<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showProducts();"').'</a>';
		echo "&nbsp;<a href=\"javascript:;\" target=\"_blank\" onclick=\"window.open('" 
			. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=products_cache&fieldname=coupon_products' ) 
			. "', 'Valid_Products', 'scrollbars=yes,resizable=yes,menubar=yes,width=600,height=250'); return false;\">View</a>";
?>
								</td>
								<td align="left" class="main"><?php echo COUPON_PRODUCTS_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_CATEGORIES; ?></td>
								<td align="left">
<?php
		echo tep_draw_input_field('coupon_categories', $coupon_categories, 'id="coupon_categories"'); 
		echo '<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showCats();"').'</a>';
		echo "&nbsp;<a href=\"javascript:;\" target=\"_blank\" onclick=\"window.open('" 
			. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=category_cache&fieldname=coupon_categories' ) 
			. "', 'Valid_Products', 'scrollbars=yes,resizable=yes,menubar=yes,width=600,height=200'); return false;\">View</a>";
?>
								</td>
								<td align="left" class="main"><?php echo COUPON_CATEGORIES_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS; ?></td>
								<td align="left">
<?php
		$current_gid = explode(',', $coupon_customers_groups_id);
?>
									<input type="checkbox" onclick="toggleChecks(this)" <?=($current_gid[0]=='ALL' || is_null($coupon_customers_groups_id)) ? "checked" : '' ?> name="coupon_customers_groups_id[]" value="ALL"> All<br>
<?php
		foreach ($customers_groups_array as $gid => $groups) {
			if ($gid != 'ALL') {
				$checked = false;
				if (in_array($groups['id'], $current_gid)) {
					$checked = true;
				}
				echo tep_draw_checkbox_field('coupon_customers_groups_id[]" id="coupon_customers_groups_id'.(($current_gid[0] == 'ALL' || is_null($coupon_customers_groups_id))? '" disabled="disabled' : ''), $groups['id'], $checked); echo $groups['text']."<br>";
			}
		}
?>	
								</td>
								<td align="left" class="main" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS_HELP; ?></td>
							</tr>
							<tr>
<?php
		if (!$_POST['coupon_startdate']) {
			if (isset($edit_startdate) && tep_not_null($edit_startdate)) {
				$coupon_startdate = split_dep("[-]", $edit_startdate);
			} else {
				$coupon_startdate = split_dep("[-]", date('Y-m-d'));
			}
		} else {
			$coupon_startdate = split_dep("[-]", $_POST['coupon_startdate']);
		}
		if (!$_POST['coupon_finishdate']) {
			if (isset($edit_finishdate) && tep_not_null($edit_finishdate)) {
				$coupon_finishdate = split_dep("[-]", $edit_finishdate);
			} else {
				$coupon_finishdate = split_dep("[-]", date('Y-m-d'));
				$coupon_finishdate[0] = $coupon_finishdate[0] + 1;
			}
		} else {
			$coupon_finishdate = split_dep("[-]", $_POST['coupon_finishdate']);
		}
?>
								<td align="left" class="main"><?php echo COUPON_STARTDATE; ?></td>
								<td align="left"><?php echo tep_draw_date_selector('coupon_startdate', mktime(0,0,0, $coupon_startdate[1], $coupon_startdate[2], $coupon_startdate[0], 0)); ?></td>
								<td align="left" class="main"><?php echo COUPON_STARTDATE_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_FINISHDATE; ?></td>
								<td align="left"><?php echo tep_draw_date_selector('coupon_finishdate', mktime(0,0,0, $coupon_finishdate[1], $coupon_finishdate[2], $coupon_finishdate[0], 0)); ?></td>
								<td align="left" class="main"><?php echo COUPON_FINISHDATE_HELP; ?></td>
							</tr>
<?
		if ($_GET['action']=='voucheredit') {
?>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td align="top" colspan="3">
									<div id="logLink" align="top"> 
										[<a href="javascript:;" onClick="showHideLogHistory('logBoxDiv', 'logLink', 'show');"><?=LINK_SHOW_INFO_HISTORY_BOX?></a>]
									</div>
									<div id="logBoxDiv" class="hide">
										<table border="1" cellspacing="0" cellpadding="5" width="80%">
											<tr>
												<td class="smallText"><?=TEXT_INFO_DATE_ADDED?></td>
												<td class="smallText"><?=TEXT_INFO_STATUS?></td> 
												<td class="smallText"><?=TEXT_INFO_REMARK?></td>
												<td class="smallText"><?=TEXT_INFO_ADDED_BY?></td>
											</tr>
<?
		if (isset($_GET['cid'])) {
			$info_history_select_sql = "SELECT * FROM ". TABLE_COUPONS_STATUS_HISTORY ." 
										WHERE coupon_id='". tep_db_input($_GET['cid']) ."'
										ORDER BY coupons_status_history_id";
			$info_history_result_sql = tep_db_query($info_history_select_sql);
			while ($info_history_row = tep_db_fetch_array($info_history_result_sql)) {
				switch ($info_history_row['coupon_active']) {
					case 'P':
						$status = 'Pending';
						break;
					case 'Y':
						$status = 'Active';
						break;
					case 'N':
						$status = 'Closed';
						break;
					case 'D':
						$status = 'Deleted';
						break;
					default:
						$status = '--';
						break;
				}
				$tr_class = ($info_history_row['changed_by']=='system')? "orderCommentSystem": "orderCommentCrew";
?>
											<tr class="<?=$tr_class?>">
												<td class="smallText"><?=$info_history_row['date_added'];?></td>
												<td class="smallText"><?=$status;?></td>
												<td class="smallText"><?=nl2br($info_history_row['comments']);?></td>
												<td class="smallText"><?=$info_history_row['changed_by'];?></td>
											</tr>
<?
			}
		}
?>
										</table>
									</div>
								</td>
							</tr>
<?		} ?>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td align="left"><?php echo tep_submit_button(BUTTON_PREVIEW, ALT_BUTTON_PREVIEW, '', 'inputButton'); ?></td>
								<td align="left"><?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link('coupon_admin.php', 'action=coupons_list&cgid='.$_GET['cgid'].'&cid='.$_GET['cid'].((isset($_GET['page']))? '&page='.$_GET['page']: '')), '', 'inputButton'); ?></td>
							</tr>
						</table>
					</form>
					</td>
				</tr>
			</table>
		</td>
<?php
		break;
/* END NEW DISCOUNT CODE */
/* BEGIN GENERATION UPDATE PREVIEW */
	case 'generation_preview':
?>
		<td width="100%" valign="top">
	      	<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="6">
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
							<tr>
								<td align="left" width="30%"><?php echo COUPON_GENERATION_NAME." (".$languages[$i]['name'].")"; ?></td>
								<td align="left"><?php echo $_POST['coupon_name'][$language_id]; ?></td>
							</tr>
<?php
		}
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
							<tr>
								<td align="left"><?php echo COUPON_GENERATION_DESC." (".$languages[$i]['name'].")"; ?></td>
								<td align="left"><?php echo $_POST['coupon_desc'][$language_id]; ?></td>
							</tr>
<?php
		}
?>
							<tr>
								<td align="left"><?php echo COUPON_AMOUNT; ?></td>
								<td align="left"><?php echo $_POST['coupon_amount']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_MIN_ORDER; ?></td>
								<td align="left"><?php echo $_POST['coupon_min_order']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_PREFIX; ?></td>
								<td align="left"><?php echo $_POST['coupon_code_prefix']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_SUFFIX; ?></td>
								<td align="left"><?php echo $_POST['coupon_code_suffix']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_NUMBERS; ?></td>
								<td align="left"><?php echo $_POST['coupon_number']; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_USES_COUPON; ?></td>
								<td align="left"><?php echo (($_POST['uses_per_coupon_unlimited']=='on'||$_POST['uses_per_coupon_unlimited']=='Y') ? "Unlimited" : $_POST['coupon_uses_coupon']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_USES_USER; ?></td>
								<td align="left"><?php echo (($_POST['uses_per_user_unlimited']=='on'||$_POST['uses_per_user_unlimited']=='Y') ? "Unlimited" : $_POST['coupon_uses_user']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_PRODUCTS; ?></td>
								<td align="left"><?php echo str_replace(' ', '', $_POST['coupon_products']); ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_CATEGORIES; ?></td>
								<td align="left"><?php echo str_replace(' ', '', $_POST['coupon_categories']); ?></td>
							</tr>
							<tr>
								<td align="left" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS; ?></td>
								<td align="left">
									<?php
									$cnt=0;
									foreach ($_POST['coupon_customers_groups_id'] as $item) {
										echo ($cnt>0 ? ',': '').$customers_groups_array[$item]['text'];
										$cnt++;
									}
									?>
								</td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_STARTDATE; ?></td>
<?php
		$start_date = date(DATE_FORMAT, mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] ));
?>
								<td align="left"><?php echo $start_date; ?></td>
							</tr>
							<tr>
								<td align="left"><?php echo COUPON_FINISHDATE; ?></td>
<?php
		$finish_date = date(DATE_FORMAT, mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] ));
?>
								<td align="left"><?php echo $finish_date; ?></td>
							</tr>
							<script language="javascript">
								function get_comment() {
									var user_comment = prompt('Please provide your reason of change:','');
									if (user_comment!=null) {
										document.getElementById('comments').value = user_comment;
										return true;
									} else {
										return false;
									}
								}
							</script>
							<tr>
<?php echo tep_draw_form('generation', 'coupon_admin.php', 'action=generation_confirm&oldaction=' . $_GET['oldaction'] . '&cgid=' . $_GET['cgid'] . ((isset($_GET['page']))? '&page='.$_GET['page']: '')); ?>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			echo tep_draw_hidden_field('coupon_name[' . $languages[$i]['id'] . ']', $_POST['coupon_name'][$language_id]);
			echo tep_draw_hidden_field('coupon_desc[' . $languages[$i]['id'] . ']', $_POST['coupon_desc'][$language_id]);
		}
		echo tep_draw_hidden_field('coupon_amount', $_POST['coupon_amount']);
		echo tep_draw_hidden_field('coupon_min_order', $_POST['coupon_min_order']);
		echo tep_draw_hidden_field('coupon_code_prefix', $_POST['coupon_code_prefix']);
		echo tep_draw_hidden_field('coupon_code_suffix', $_POST['coupon_code_suffix']);
		echo tep_draw_hidden_field('coupon_number', $_POST['coupon_number']);
		echo tep_draw_hidden_field('coupon_uses_coupon', $_POST['coupon_uses_coupon']);
		echo tep_draw_hidden_field('uses_per_coupon_unlimited', (($_POST['uses_per_coupon_unlimited']=='on'||$_POST['uses_per_coupon_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_uses_user', $_POST['coupon_uses_user']);
		echo tep_draw_hidden_field('uses_per_user_unlimited', (($_POST['uses_per_user_unlimited']=='on'||$_POST['uses_per_user_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_products', str_replace(' ', '', $_POST['coupon_products']));
		echo tep_draw_hidden_field('coupon_categories', str_replace(' ', '', $_POST['coupon_categories']));
		foreach ($_POST['coupon_customers_groups_id'] as $item) {
			echo tep_draw_hidden_field('coupon_customers_groups_id[]', str_replace(' ', '', $item));
		}
		echo tep_draw_hidden_field('coupon_startdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] )));
		echo tep_draw_hidden_field('coupon_finishdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] )));
		echo tep_draw_hidden_field('requester_id', $_SESSION['login_id']);
		echo tep_draw_hidden_field('coupon_generation_status', $_POST['coupon_generation_status']);
		echo tep_draw_hidden_field('comments', '', 'id="comments"');
?>
									<td align="left"><?php echo tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, 'onClick="return get_comment();"', 'inputButton'); ?></td>
								</form>
<?php echo tep_draw_form('generation_back', 'coupon_admin.php', 'action=generation_back&oldaction=' . $_GET['action'] . '&cgid=' . $_GET['cgid'] . ((isset($_GET['page']))? '&page='.$_GET['page']: '')); ?>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			echo tep_draw_hidden_field('coupon_name[' . $languages[$i]['id'] . ']', $_POST['coupon_name'][$language_id]);
			echo tep_draw_hidden_field('coupon_desc[' . $languages[$i]['id'] . ']', $_POST['coupon_desc'][$language_id]);
		}
		echo tep_draw_hidden_field('coupon_amount', $_POST['coupon_amount']);
		echo tep_draw_hidden_field('coupon_min_order', $_POST['coupon_min_order']);
		echo tep_draw_hidden_field('coupon_code_prefix', $_POST['coupon_code_prefix']);
		echo tep_draw_hidden_field('coupon_code_suffix', $_POST['coupon_code_suffix']);
		echo tep_draw_hidden_field('coupon_number', $_POST['coupon_number']);
		echo tep_draw_hidden_field('coupon_uses_coupon', $_POST['coupon_uses_coupon']);
		echo tep_draw_hidden_field('uses_per_coupon_unlimited', (($_POST['uses_per_coupon_unlimited']=='on'||$_POST['uses_per_coupon_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_uses_user', $_POST['coupon_uses_user']);
		echo tep_draw_hidden_field('uses_per_user_unlimited', (($_POST['uses_per_user_unlimited']=='on'||$_POST['uses_per_user_unlimited']=='Y')? 'Y' : 'N'));
		echo tep_draw_hidden_field('coupon_products', str_replace(' ', '', $_POST['coupon_products']));
		echo tep_draw_hidden_field('coupon_categories', str_replace(' ', '', $_POST['coupon_categories']));
		foreach ($_POST['coupon_customers_groups_id'] as $item) {
			echo tep_draw_hidden_field('coupon_customers_groups_id[]', str_replace(' ', '', $item));
		}
		echo tep_draw_hidden_field('coupon_startdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_startdate_month'],$_POST['coupon_startdate_day'] ,$_POST['coupon_startdate_year'] )));
		echo tep_draw_hidden_field('coupon_finishdate', date('Y-m-d', mktime(0, 0, 0, $_POST['coupon_finishdate_month'],$_POST['coupon_finishdate_day'] ,$_POST['coupon_finishdate_year'] )));
		echo tep_draw_hidden_field('requester_id', $_SESSION['login_id']);
		echo tep_draw_hidden_field('coupon_generation_status', $_POST['coupon_generation_status']);
?>
								<td align="left"><?php echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton'); ?></td>
								</form>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
<?php      
		break;
/* END GENERATION UPDATE PREVIEW */
/* BEGIN GENERAION EDIT */
	case 'generationedit':
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
			$coupon_query = tep_db_query("select coupon_generation_name,coupon_generation_description from " . TABLE_COUPONS_GENERATION_DESCRIPTION . " where coupon_generation_id = '" .  tep_db_input($_GET['cgid']) . "' and language_id = '" . $language_id . "'");
			$coupon = tep_db_fetch_array($coupon_query);
			$coupon_name[$language_id] = $coupon['coupon_generation_name'];
			$coupon_desc[$language_id] = $coupon['coupon_generation_description'];
		}
		
		$coupon_query=tep_db_query("select * from " . TABLE_COUPONS_GENERATION . " where coupon_generation_id = '" . tep_db_input($_GET['cgid']) . "'");
		$coupon=tep_db_fetch_array($coupon_query);
		$coupon_amount = $coupon['coupon_amount'];
		if ($coupon['coupon_type']=='P') {
			$coupon_amount .= '%';
		}
		if ($coupon['coupon_type']=='S') {
			$coupon_free_ship .= true;
		}
		
		$coupon_min_order = $coupon['coupon_minimum_order'];
		$coupon_code_prefix = $coupon['coupon_code_prefix'];
		$coupon_code_suffix = $coupon['coupon_code_suffix'];
		$coupon_number = $coupon['coupon_number'];
		$coupon_uses_coupon = $coupon['uses_per_coupon'];
		$coupon_uses_user = $coupon['uses_per_user'];
		$uses_per_coupon_unlimited = $coupon['uses_per_coupon_unlimited'];
		$uses_per_user_unlimited = $coupon['uses_per_user_unlimited'];
		$coupon_products = $coupon['restrict_to_products'];
		$coupon_categories = $coupon['restrict_to_categories'];
		$coupon_customers_groups_id = (tep_not_null($coupon['restrict_to_customers_groups'])? $coupon['restrict_to_customers_groups'] : NULL);
		$edit_startdate = $coupon['coupon_start_date'];
		$edit_finishdate = $coupon['coupon_expire_date'];
		$requester_id = $coupon['requester_id'];
		$coupon_generation_status = $coupon['coupon_generation_status'];
/* END GENERAION EDIT */
/* BEGIN GENERAION BACK*/
	case 'generation_back':
		if ($action == 'generation_back') {
			$languages = tep_get_languages();
			for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
				$language_id = $languages[$i]['id'];
				$coupon_name[$language_id] = $_POST['coupon_name'][$language_id];
				$coupon_desc[$language_id] = $_POST['coupon_desc'][$language_id];
			}
			
			$coupon_amount = $_POST['coupon_amount'];
			$coupon_min_order = $_POST['coupon_min_order'];
			$coupon_code_prefix = $_POST['coupon_code_prefix'];
			$coupon_code_suffix = $_POST['coupon_code_suffix'];
			$coupon_number = $_POST['coupon_number'];
			$coupon_uses_coupon = $_POST['coupon_uses_coupon'];
			$coupon_uses_user = $_POST['coupon_uses_user'];
			$uses_per_coupon_unlimited = $_POST['uses_per_coupon_unlimited'];
			$uses_per_user_unlimited = $_POST['uses_per_user_unlimited'];
			$coupon_products = $_POST['coupon_products'];
			$coupon_categories = $_POST['coupon_categories'];
			$coupon_customers_groups_id = implode(',', $_POST['coupon_customers_groups_id']);
			$edit_startdate = $_POST['coupon_startdate'];
			$edit_finishdate = $_POST['coupon_finishdate'];
			$requester_id = $_POST['requester_id'];
			$coupon_generation_status = $_POST['coupon_generation_status'];
		}
/* END GENERAION BACK */
/* BEGIN NEW GENERATION */
	case 'generationnew':
		if (!$coupon_code_prefix) $coupon_code_prefix = 'OGM';
		if (!$coupon_generation_status) $coupon_generation_status = 'P';
		if (!tep_not_null($uses_per_coupon_unlimited)) $uses_per_coupon_unlimited = 'N';
		if (!tep_not_null($uses_per_user_unlimited)) $uses_per_user_unlimited = 'N';
		$coupon_uses_coupon = ($uses_per_coupon_unlimited=='Y')? '0': $coupon_uses_coupon;
		$coupon_uses_user = ($uses_per_user_unlimited=='Y')? '0': $coupon_uses_user;
		if ($uses_per_coupon_unlimited == 'Y' || $uses_per_user_unlimited == 'Y') {
			$max_usage = 'unlimited';
		} else {
			$max_usage = $coupon_uses_coupon*$coupon_number;
		}
		if (isset($requester_id)) {
			$admin_email_result = tep_db_query("SELECT admin_email_address FROM ".TABLE_ADMIN." WHERE admin_id='".tep_db_input($requester_id)."'");
			$admin_email_row = tep_db_fetch_array($admin_email_result);
			$requester_email = $admin_email_row['admin_email_address'];
		}
?>
		<td width="100%" valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
<?php 
		echo tep_draw_form('coupon', 'coupon_admin.php', 'action=generationupdate&oldaction='.$_GET['action'] . '&cgid=' . $_GET['cgid']. ((isset($_GET['page']))? '&page='.$_GET['page']: '')); 
		echo tep_draw_hidden_field('coupon_generation_status', $coupon_generation_status);
?>
						<table border="0" width="100%" cellspacing="0" cellpadding="6">
							<tr>
								<td align="left" class="main"><?php echo COUPON_STATUS; ?></td>
								<td align="left" class="main"><b>
<?php
		switch ($coupon_generation_status) {
			case "P":
				echo "Pending";
				break;
			case "Y":
				echo "Active";
				break;
			case "N":
				echo "Cancelled";
				break;
		}
?>
								</b></td>
								<td align="left" class="main"></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_REQUESTER; ?></td>
								<td align="left" class="main"><b><?php echo $requester_email; ?></b></td>
								<td align="left" class="main"></td>
							</tr>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td colspan="3">
									<table border="0" width="58%" cellspacing="0" cellpadding="0">
										<tr>
											<td>
												<div id="languages_tab">
													<ul>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			echo '													<li id="languages_li_' . $i . '"><a href="#languages_tab_'.$i.'"><span>'.$languages[$i]['name'].'</span></a></li>';
		}
?>
													</ul>
<?php
		$languages = tep_get_languages();
		for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
			$language_id = $languages[$i]['id'];
?>
													<div id="languages_tab_<?=$i?>" class="languages_tab">
														<table border="0" width="100%" cellspacing="0" cellpadding="6">
															<tr>
																<td align="left" class="main"><?php echo COUPON_GENERATION_NAME; ?></td>
																<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_name[' . $languages[$i]['id'] . ']', $coupon_name[$language_id]) : $coupon_name[$language_id]); ?></td>
																<td align="left" class="main" width="40%"><?php echo COUPON_GENERATION_NAME_HELP; ?></td>
															</tr>
															<tr>
																<td align="left" valign="top" class="main"><?php echo COUPON_GENERATION_DESC; ?></td>
																<td align="left" valign="top" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_textarea_field('coupon_desc[' . $languages[$i]['id'] . ']','physical','24','3', $coupon_desc[$language_id]) : $coupon_desc[$language_id]); ?></td>
																<td align="left" valign="top" class="main"><?php echo COUPON_GENERATION_DESC_HELP; ?></td>
															</tr>
														</table>
													</div>
<?php
		}
?>
												</div><!--languages_tab-->
												<script language="javascript">
													jQuery("#languages_tab > ul").tabs();
													jQuery('.languages_tab').css({
														border:'1px solid #C9C9C9'
													});
													
													function get_comment() {
														var user_comment = prompt('Please provide your reason of change:','');
														if (user_comment!=null) {
															document.getElementById('comments').value = user_comment;
															return true;
														} else {
															return false;
														}
													}
													
													function calculate_max_usage() {
														var coupon_count_text_object = document.getElementById('coupon_number');
														var per_coupon_text_object = document.getElementById('coupon_uses_coupon');
														var msg_object = document.getElementById('coupon_max_usage');
														
														if (coupon_count_text_object.value == '') {
															coupon_count_text_object.value = 0;
														}
														
														var max_usage = (per_coupon_text_object.value*1) * (coupon_count_text_object.value*1);
														msg_object.innerHTML = max_usage;
													}
													
													function coupon_uses_check_selection (check_obj, target_name) {
														if (check_obj != null) {
															var check_mode = check_obj.checked ? true : false;
															var target_object = document.getElementById(target_name);
															target_object.disabled = check_mode;
															
															var msg_object = document.getElementById('coupon_max_usage');
															var per_coupon_check_object = document.getElementById('uses_per_coupon_unlimited');
															var per_user_check_object = document.getElementById('uses_per_user_unlimited');
															var per_coupon_object = document.getElementById('coupon_uses_coupon');
															var per_user_object = document.getElementById('coupon_uses_user');
															
															if (check_obj.id == "uses_per_user_unlimited") {
																if (check_obj.checked == true) {
																	var chain_target_check_object = document.getElementById('uses_per_coupon_unlimited');
																	var chain_target_text_object = document.getElementById('coupon_uses_coupon');
																	chain_target_check_object.checked = check_obj.checked;
																	chain_target_text_object.disabled = check_obj.checked;
																	per_coupon_object.value = '0';
																	per_user_object.value = '0';
																	msg_object.innerHTML = "Unlimited";
																}
															}
															
															else if (check_obj.id == "uses_per_coupon_unlimited") {
																if (check_obj.checked == true) {
																	msg_object.innerHTML = "Unlimited";
																	per_coupon_object.value = '0';
																}
																
																if (check_obj.checked == false) {
																	var target_text_object = document.getElementById('coupon_uses_coupon');
																	var chain_target_check_object = document.getElementById('uses_per_user_unlimited');
																	// if uses per customer unlimited is still remain as checked, dont allow uncheck this
																	if (chain_target_check_object.checked == true) {
																		check_obj.checked = true;
																		target_text_object.disabled = true;
																	}
																}
															}
															
															// max usage calculation
															if (per_coupon_check_object.checked == false && per_user_check_object.checked == false) {
																calculate_max_usage();
															}
														}
													}
													
													function showHideLogHistory(log_div, log_link_div, classtype) {
														DOMCall(log_div).className = classtype;
														
														if (classtype == 'show') {
															DOMCall(log_link_div).innerHTML = '[<a href="javascript:;" onclick="showHideLogHistory(\''+log_div+'\', \''+log_link_div+'\', \'hide\');">'+'<?=LINK_HIDE_INFO_HISTORY_BOX?>'+'</a>]';
														} else {
															DOMCall(log_link_div).innerHTML = '[<a href="javascript:;" onclick="showHideLogHistory(\''+log_div+'\', \''+log_link_div+'\', \'show\');">'+'<?=LINK_SHOW_INFO_HISTORY_BOX?>'+'</a>]';
														}
													}
													
													function getReturnedValue(received_val, fieldname) {
														var ori_list = DOMCall(fieldname).value;
														var add_str = '';
														
														if (ori_list.length > 0) {
															add_str = ',';
														}
														DOMCall(fieldname).value = ori_list + add_str + received_val;
													}
													
													function toggleChecks(cbElem) {
														var f = cbElem.form;
														for (var elem, i = 0; (elem = f.elements[i]); i++) {
															if (elem.type == 'checkbox' && elem != cbElem && elem.id == 'coupon_customers_groups_id') {
																elem.checked = (cbElem.checked==true)?!cbElem.checked:false;
																elem.disabled = cbElem.checked;
															}
														}
													}
												</script>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_AMOUNT; ?></td>
								<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_amount', $coupon_amount) : $coupon_amount); ?></td>
								<td align="left" class="main"><?php echo COUPON_AMOUNT_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_MIN_ORDER; ?></td>
								<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_min_order', $coupon_min_order) : $coupon_min_order); ?></td>
								<td align="left" class="main"><?php echo COUPON_MIN_ORDER_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_PREFIX; ?></td>
								<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_code_prefix', $coupon_code_prefix, 'maxlength="5"') : $coupon_code_prefix); ?></td>
								<td align="left" class="main"><?php echo COUPON_PREFIX_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_SUFFIX; ?></td>
								<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_code_suffix', $coupon_code_suffix, 'maxlength="5"') : $coupon_code_suffix); ?></td>
								<td align="left" class="main"><?php echo COUPON_SUFFIX_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_NUMBERS; ?></td>
								<td align="left" class="main"><?php echo ($coupon_generation_status == 'P' ? tep_draw_input_field('coupon_number', $coupon_number, 'id="coupon_number" onChange="calculate_max_usage()"') : $coupon_number); ?></td>
								<td align="left" class="main"><?php echo COUPON_NUMBERS_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_USES_COUPON; ?></td>
								<td align="left" class="main"><?php if ($coupon_generation_status == 'P') { echo tep_draw_input_field('coupon_uses_coupon', $coupon_uses_coupon, 'id="coupon_uses_coupon" '.($uses_per_coupon_unlimited=='Y'? 'disabled="disabled"': '').' onChange="calculate_max_usage()"'); echo tep_draw_checkbox_field("uses_per_coupon_unlimited", (($uses_per_coupon_unlimited=='Y') ? $uses_per_coupon_unlimited : ''), (($uses_per_coupon_unlimited=='Y') ? true : false), '', 'id="uses_per_coupon_unlimited" onClick="coupon_uses_check_selection(this,\'coupon_uses_coupon\')"'); echo "&nbsp;".TEXT_UNLIMITED; } else { echo (($uses_per_coupon_unlimited=='Y') ? "Unlimited" : $coupon_uses_coupon); } ?></td>
								<td align="left" class="main"><?php echo COUPON_USES_COUPON_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_USES_USER; ?></td>
								<td align="left" class="main"><?php if ($coupon_generation_status == 'P') { echo tep_draw_input_field('coupon_uses_user', $coupon_uses_user, 'id="coupon_uses_user" '.($uses_per_user_unlimited=='Y'? 'disabled="disabled"': '')); echo tep_draw_checkbox_field("uses_per_user_unlimited", (($uses_per_user_unlimited=='Y') ? $uses_per_user_unlimited : ''), (($uses_per_user_unlimited=='Y') ? true : false), '', 'id="uses_per_user_unlimited" onClick="coupon_uses_check_selection(this,\'coupon_uses_user\')"'); echo "&nbsp;".TEXT_UNLIMITED; } else { echo (($uses_per_user_unlimited=='Y') ? "Unlimited" : $coupon_uses_user); } ?></td>
								<td align="left" class="main"><?php echo COUPON_USES_USER_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_PRODUCTS; ?></td>
								<td align="left" class="main">
<?php
		if ($coupon_generation_status == 'P') {
			echo tep_draw_input_field('coupon_products', $coupon_products, 'id="coupon_products"'); 
			echo '<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showProducts();"').'</a>';
			echo "&nbsp;<a href=\"javascript:;\" target=\"_blank\" onclick=\"window.open('" 
				. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=products_cache&fieldname=coupon_products' ) 
				. "', 'Valid_Products', 'scrollbars=yes,resizable=yes,menubar=yes,width=600,height=250'); return false;\">View</a>";
		} else {
			echo $coupon_products.((tep_not_null($coupon_products)) ? ' '.tep_draw_hidden_field('coupon_products',$coupon_products,'id="coupon_products"').'<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showProducts();"').'</a>' : '');
		}
?>
								</td>
								<td align="left" class="main"><?php echo COUPON_PRODUCTS_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_CATEGORIES; ?></td>
								<td align="left" class="main">
<?php
		if ($coupon_generation_status == 'P') {
			echo tep_draw_input_field('coupon_categories', $coupon_categories, 'id="coupon_categories"'); 
			echo '<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showCats();"').'</a>';
			echo "&nbsp;<a href=\"javascript:;\" target=\"_blank\" onclick=\"window.open('" 
				. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=category_cache&fieldname=coupon_categories' ) 
				. "', 'Valid_Products', 'scrollbars=yes,resizable=yes,menubar=yes,width=600,height=200'); return false;\">View</a>";
		} else {
			echo $coupon_categories.((tep_not_null($coupon_categories)) ? ' '.tep_draw_hidden_field('coupon_categories',$coupon_categories,'id="coupon_categories"').'<a href="javascript:;">'.tep_image(DIR_WS_ICONS."att.gif", "Detail Info", "", "", 'align="top" onClick="showCats();"').'</a>' : '');
		}
?>
								</td>
								<td align="left" class="main"><?php echo COUPON_CATEGORIES_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS; ?></td>
								<td align="left">
<?php
		$current_gid = explode(',', $coupon_customers_groups_id);
?>
									<input type="checkbox" onclick="toggleChecks(this)" <?=($current_gid[0]=='ALL' || is_null($coupon_customers_groups_id)) ? "checked" : '' ?> name="coupon_customers_groups_id[]" value="ALL"> All<br>
<?php
		foreach ($customers_groups_array as $gid => $groups) {
			if ($gid != 'ALL') {
				$checked = false;
				if (in_array($groups['id'], $current_gid)) {
					$checked = true;
				}
				echo tep_draw_checkbox_field('coupon_customers_groups_id[]" id="coupon_customers_groups_id'.(($current_gid[0] == 'ALL' || is_null($coupon_customers_groups_id))? '" disabled="disabled' : ''), $groups['id'], $checked); echo $groups['text']."<br>";
			}
		}
?>	
								</td>
								<td align="left" class="main" valign="top"><?php echo COUPON_CUSTOMERS_GROUPS_HELP; ?></td>
							</tr>
							<tr>
<?php
		if (!$_POST['coupon_startdate']) {
			if (isset($edit_startdate) && tep_not_null($edit_startdate)) {
				$coupon_startdate = split_dep("[-]", $edit_startdate);
			} else {
				$coupon_startdate = split_dep("[-]", date('Y-m-d'));
			}
		} else {
			$coupon_startdate = split_dep("[-]", $_POST['coupon_startdate']);
		}
		if (!$_POST['coupon_finishdate']) {
			if (isset($edit_finishdate) && tep_not_null($edit_finishdate)) {
				$coupon_finishdate = split_dep("[-]", $edit_finishdate);
			} else {
				$coupon_finishdate = split_dep("[-]", date('Y-m-d'));
				$coupon_finishdate[1] = $coupon_finishdate[1] + 1;
			}
		} else {
			$coupon_finishdate = split_dep("[-]", $_POST['coupon_finishdate']);
		}
?>
								<td align="left" class="main"><?php echo COUPON_STARTDATE; ?></td>
								<td align="left" class="main"><?php if ($coupon_generation_status == 'P') { echo tep_draw_date_selector('coupon_startdate', mktime(0,0,0, $coupon_startdate[1], $coupon_startdate[2], $coupon_startdate[0])); } else { echo $edit_startdate; } ?></td>
								<td align="left" class="main"><?php echo COUPON_STARTDATE_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_FINISHDATE; ?></td>
								<td align="left" class="main"><?php if ($coupon_generation_status == 'P') { echo tep_draw_date_selector('coupon_finishdate', mktime(0,0,0, $coupon_finishdate[1], $coupon_finishdate[2], $coupon_finishdate[0])); } else { echo $edit_finishdate; } ?></td>
								<td align="left" class="main"><?php echo COUPON_FINISHDATE_HELP; ?></td>
							</tr>
							<tr>
								<td align="left" class="main"><?php echo COUPON_MAX_USAGE; ?></td>
								<td align="left" class="main"><div id="coupon_max_usage"><?php echo $max_usage; ?></div></td>
								<td align="left" class="main"><?php echo COUPON_MAX_USAGE_HELP; ?></td>
							</tr>
<?
		if ($_GET['action']=='generationedit') {
?>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td align="top" colspan="3">
									<div id="logLink" align="top"> 
										[<a href="javascript:;" onClick="showHideLogHistory('logBoxDiv', 'logLink', 'show');"><?=LINK_SHOW_INFO_HISTORY_BOX?></a>]
									</div>
									<div id="logBoxDiv" class="hide">
										<table border="1" cellspacing="0" cellpadding="5" width="80%">
											<tr>
												<td class="smallText"><?=TEXT_INFO_DATE_ADDED?></td>
												<td class="smallText"><?=TEXT_INFO_STATUS?></td> 
												<td class="smallText"><?=TEXT_INFO_REMARK?></td>
												<td class="smallText"><?=TEXT_INFO_ADDED_BY?></td>
											</tr>
<?
		if (isset($_GET['cgid'])) {
			$info_history_select_sql = "SELECT * FROM ". TABLE_COUPONS_GENERATION_STATUS_HISTORY ." 
										WHERE coupons_generation_id='". tep_db_input($_GET['cgid']) ."'
										ORDER BY coupons_generation_status_history_id";
			$info_history_result_sql = tep_db_query($info_history_select_sql);
			while ($info_history_row = tep_db_fetch_array($info_history_result_sql)) {
				switch ($info_history_row['coupons_generation_status']) {
					case 'P':
						$status = 'Pending';
						break;
					case 'Y':
						$status = 'Active';
						break;
					case 'N':
						$status = 'Deleted';
						break;
					default:
						$status = '--';
						break;
				}
				$tr_class = ($info_history_row['changed_by']=='system')? "orderCommentSystem": "orderCommentCrew";
?>
											<tr class="<?=$tr_class?>">
												<td class="smallText"><?=$info_history_row['date_added'];?></td>
												<td class="smallText"><?=$status;?></td>
												<td class="smallText"><?=nl2br($info_history_row['comments']);?></td>
												<td class="smallText"><?=$info_history_row['changed_by'];?></td>
											</tr>
<?
			}
		}
?>
										</table>
									</div>
								</td>
							</tr>
<?		} ?>
							<tr>
								<td align="left" class="main" colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td align="left"><?php if ($coupon_generation_status == 'P') { echo tep_submit_button(BUTTON_PREVIEW, ALT_BUTTON_PREVIEW, '', 'inputButton'); } ?></td>
								<td align="left"><?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link('coupon_admin.php', ((isset($_GET['page']))? 'page='.$_GET['page']: '')), '', 'inputButton')?></td>
							</tr>
						</table>
					</form>
					</td>
				</tr>
			</table>
		</td>
<?php
		break;
/* END NEW GENERATION */
/* BEGIN DISCOUNT CODES LIST */
	case "coupons_list":
?>    
		<td width="100%" valign="top">
	   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td width="100%">
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td class="pageHeading" colspan="2"><?php echo EDIT_HEADING_TITLE; ?></td>
							</tr>
							<tr><td class="pageHeading" colspan="2"><hr></td></tr>
							<tr>
								<td class="main">
<?php
		$cg_tree_array = array();
		$cg_tree_array[] = array('id' => '0', 'text' => TEXT_TOP, 'param' => '');
		$cg_query = tep_db_query("SELECT coupon_generation_id, coupon_generation_name FROM " . TABLE_COUPONS_GENERATION_DESCRIPTION . " WHERE language_id='" . $languages_id . "' ORDER BY coupon_generation_name");
		while ($cg_row = tep_db_fetch_array($cg_query)) {
			$cg_tree_array[] = array("id" => $cg_row["coupon_generation_id"], 'text' => $cg_row["coupon_generation_name"], 'param' => '');
		}
		
		echo tep_draw_form('goto', FILENAME_COUPON_ADMIN, 'action=coupons_list', 'post');
	    echo HEADING_TITLE_GOTO . ' ' . tep_draw_pull_down_menu('cgid', $cg_tree_array, trim($cgid), 'onChange="this.form.submit();"');
	    echo '</form>';
?>
								</td>
								<td class="main" align="right"><?php echo tep_draw_form('status', FILENAME_COUPON_ADMIN, 'action=coupons_list&cgid='.$cgid, 'post'); ?>
<?php
		$status_array[] = array('id' => '*', 'text' => TEXT_COUPON_ALL);
		$status_array[] = array('id' => 'P', 'text' => TEXT_COUPON_PENDING);
		$status_array[] = array('id' => 'Y', 'text' => TEXT_COUPON_ACTIVE);
		$status_array[] = array('id' => 'N', 'text' => TEXT_COUPON_CLOSED);
		$status_array[] = array('id' => 'D', 'text' => TEXT_COUPON_DELETED);
		
		if (isset($_GET['status'])) { 
			$_POST['status'] = $_GET['status'];
		}
		
		if ($_POST['status']) {
			$status = tep_db_prepare_input($_POST['status']);
		} else {
			$status = '*';
		} 
		echo HEADING_TITLE_STATUS . ' ' . tep_draw_pull_down_menu('status', $status_array, $status, 'onChange="this.form.submit();"'); 
?>
								</form>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td valign="top">
									<table width="100%" align="center" border="0" cellpadding="5" cellspacing="2" class="main">
										<tr>
											<td class="reportBoxHeading"><font size="1"><?php echo TABLE_HEADING_COUPON_NAME; ?></font></td>
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_AMOUNT; ?></font></td>	
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_CODE; ?></font></td>	
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_USAGE; ?></font></td>
                      <td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_STATUS; ?></font></td>	
											<td class="reportBoxHeading" align="center" colspan="2"><font size="1"><?php echo TABLE_HEADING_ACTION; ?></font></td>
										</tr>
<?php
	if (isset($_POST['cgid']) || isset($_GET['cgid'])) {
		$selected_cgid = (isset($_POST['cgid'])? $_POST['cgid'] : (isset($_GET['cgid'])? $_GET['cgid'] : ''));
		if (tep_not_null($selected_cgid)) {
			$requester_query = tep_db_query("SELECT requester_id FROM " . TABLE_COUPONS_GENERATION . " WHERE coupon_generation_id = '" . tep_db_input($selected_cgid) . "'");
			$requester_row = tep_db_fetch_array($requester_query);
		}
	}
	
	if ($view_discount_code_generation_permission || (isset($requester_row) && $requester_row['requester_id']==$_SESSION['login_id'])) {
		if ($_GET['page'] > 1) $rows = $_GET['page'] * 20 - 20;
		$cc_query_raw = "select coupon_id, coupon_code, coupon_amount, coupon_type, coupon_start_date, coupon_expire_date, uses_per_user, uses_per_coupon, restrict_to_products, restrict_to_categories, date_created, date_modified, coupon_active from " . TABLE_COUPONS . " where coupon_type != 'G' AND coupon_generation_id='" . tep_db_input($cgid) . "'" . (($status != '*')? " AND coupon_active='". tep_db_input($status) . "'" : "") . " ORDER BY date_created DESC";
		$cc_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $cc_query_raw, $cc_query_numrows);
		$cc_split->show_all = false;
		$cc_query = tep_db_query($cc_query_raw);
		while ($cc_list = tep_db_fetch_array($cc_query)) {
			$rows++;
			if ($rows % 2 == "1") {
				$tr_classname = "reportListingEven";
			}
			else {
				$tr_classname = "reportListingOdd";
			}
			
			echo '										<tr class="'.($_GET['cid']==$cc_list['coupon_id']? 'rowSelected' :$tr_classname).'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$tr_classname.'\')" onclick="rowClicked(this, \''.$tr_classname.'\')">' . "\n";
			$coupon_description_query = tep_db_query("select coupon_name from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . tep_db_input($cc_list['coupon_id']) . "' and language_id = '" . $_SESSION['languages_id'] . "'");
			$coupon_desc = tep_db_fetch_array($coupon_description_query);
?>
											<td class="dataTableContent"><?php echo '<a href="' . tep_href_link(FILENAME_COUPON_ADMIN, 'action=voucheredit&cid='.$cc_list['coupon_id'].'&cgid='.$_GET['cgid'].((isset($_GET['page']))? '&page='.$_GET['page']:'')) . '">' . tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW ,'' , '' , ' align="absmiddle" border="0"') . '&nbsp;' . $coupon_desc['coupon_name'] . '</a>'; ?></td>
											<td class="dataTableContent" align="center">
<?php  
			if ($cc_list['coupon_type'] == 'P') {
				echo $cc_list['coupon_amount'] . '%';
			} elseif ($cc_list['coupon_type'] == 'S') {
				echo TEXT_FREE_SHIPPING;
			} else {
				echo $currencies->format($cc_list['coupon_amount']);
			}
?>
											&nbsp;</td>
											<td class="dataTableContent" align="center"><?php echo $cc_list['coupon_code']; ?></td>
                      <?php
                        // Calculate redeemed count for each coupon
                        $redeemed_count = 0;
                        $redeemed_count_sql = "SELECT COUNT(unique_id) AS total FROM " . TABLE_COUPON_REDEEM_TRACK . " WHERE coupon_id = '" . $cc_list['coupon_id'] . "'";
                        $redeemed_count_result = tep_db_query($redeemed_count_sql);
                        if ($redeemed_count_row = tep_db_fetch_array($redeemed_count_result)) {
                          $redeemed_count = $redeemed_count_row['total'];
                        }
                      ?>
                      <td class="dataTableContent" align="center"><?php echo $redeemed_count . ' / ' . (($cc_list['uses_per_coupon'] == 0) ? '&#8734;' : $cc_list['uses_per_coupon']); ?></td>
											<td class="dataTableContent" align="center">
<?php
			switch ($cc_list['coupon_active']) {
				case 'P':
					echo "Pending";
					break;
				case 'Y':
					echo "Active";
					break;
				case 'D':
					echo "Deleted";
					break;
				case 'N':
				default:
					echo "Closed";
					break;
			}
?>
											</td>
											<td class="dataTableContent" align="center" width="5%">
<?php
			echo '<a href="'.tep_href_link('coupon_admin.php','action=voucheredit&cid='.$cc_list['coupon_id'].'&cgid='.$_GET['cgid'].((isset($_GET['page']))? '&page='.$_GET['page']:''),'NONSSL').'">Edit</a>';
?>
											</td>
											<td class="dataTableContent" align="center" width="5%">
<?php
		if ($cc_list['coupon_active'] != 'D' && $cc_list['coupon_active'] != 'N') {
			echo '<a href="'.tep_href_link('coupon_admin.php','action=voucherdelete&cid='.$cc_list['coupon_id'].'&cgid='.$_GET['cgid'].((isset($_GET['page']))? '&page='.$_GET['page']:''),'NONSSL').'" onClick="return confirm_delete(this)">Delete</a>';
		}
?>
											</td>
										</tr>
<?php
		}
	}
?>
										<script language="javascript">
											function urlencode(str) {
												return escape(str).replace(/\+/g,'%2B').replace(/%20/g, '+').replace(/\*/g, '%2A').replace(/\//g, '%2F').replace(/@/g, '%40');
											}
											
											function get_comment(this_object) {
												var user_comment = prompt('Please provide your reason of change:','');
												if (user_comment!=null) {
													var original_url = this_object.href;
													var new_url = original_url + '&comments=' + urlencode(user_comment);
													this_object.href = new_url;
													return true;
												} else {
													return false;
												}
											}
											
											function confirm_delete(this_object) {
												var text = 'Are you sure you want to DELETE this discount code?';
												if (confirm(text)) {
													return get_comment(this_object);
												} else {
													return false;
												}
											}
										</script>
										<tr>
											<td colspan="6">
												<table border="0" width="100%" cellspacing="0" cellpadding="2">
													<tr>
														<td class="smallText">&nbsp;<?php if (is_object($cc_split)) { echo $cc_split->display_count($cc_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_COUPONS); } ?>&nbsp;</td>
														<td align="right" class="smallText">&nbsp;<?php if (is_object($cc_split)) { echo $cc_split->display_links($cc_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'action', 'cgid')) . 'action=coupons_list&cgid='.$cgid.(isset($_POST['status'])? '&status='.$_POST['status']:'')); } ?>&nbsp;</td>
													</tr>
													<tr>
														<td align="right" colspan="2" class="smallText"></td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
<?php
		break;
/* END DISCOUNT CODES LIST */
/* BEGIN DISCOUNT CODES GENERATION LIST */
	default:  // <-- discount code generation list
?>    
		<td width="100%" valign="top">
	   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td width="100%">
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td class="pageHeading" colspan="2"><?php echo HEADING_TITLE; ?></td>
							</tr>
							<tr><td class="pageHeading" colspan="2"><hr></td></tr>
							<tr>
								<td class="main">
<?php
		$cg_tree_array = array();
		$cg_tree_array[] = array('id' => '0', 'text' => TEXT_TOP, 'param' => '');
		$cg_query = tep_db_query("SELECT coupon_generation_id, coupon_generation_name FROM " . TABLE_COUPONS_GENERATION_DESCRIPTION . " WHERE language_id='" . $languages_id . "' ORDER BY coupon_generation_name");
		while ($cg_row = tep_db_fetch_array($cg_query)) {
			$cg_tree_array[] = array("id" => $cg_row["coupon_generation_id"], 'text' => $cg_row["coupon_generation_name"], 'param' => '');
		}
		
		echo tep_draw_form('goto', FILENAME_COUPON_ADMIN, 'action=coupons_list', 'post');
	    echo HEADING_TITLE_GOTO . ' ' . tep_draw_pull_down_menu('cgid', $cg_tree_array, trim($cgid), 'onChange="this.form.submit();"');
	    echo '</form>';
?>
								</td>
								<td class="main" align="right"><?php echo tep_draw_form('dcg_status', FILENAME_COUPON_ADMIN, '', 'get'); ?>
<?php
		$status_array[] = array('id' => '*', 'text' => TEXT_COUPON_GENERATION_ALL);
		$status_array[] = array('id' => 'P', 'text' => TEXT_COUPON_GENERATION_PENDING);
		$status_array[] = array('id' => 'Y', 'text' => TEXT_COUPON_GENERATION_APPROVED);
		$status_array[] = array('id' => 'N', 'text' => TEXT_COUPON_GENERATION_CANCEL);
		
		if (isset($_GET['dcg_status'])) { 
			$_POST['dcg_status'] = $_GET['dcg_status'];
		}
		
		if ($_GET['dcg_status']) {
			$dcg_status = tep_db_prepare_input($_GET['dcg_status']);
		} else { 
			$dcg_status = '*';
		} 
		echo HEADING_TITLE_STATUS . ' ' . tep_draw_pull_down_menu('dcg_status', $status_array, $dcg_status, 'onChange="this.form.submit();"'); 
?>
								</form>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td valign="top">
									<table width="100%" align="center" border="0" cellpadding="5" cellspacing="2" class="main">
										<tr>
											<td class="reportBoxHeading"><font size="1"><?php echo TABLE_HEADING_COUPON_NAME; ?></font></td>
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_REQUESTER; ?></font></td>
											<td class="reportBoxHeading" align="right"><font size="1"><?php echo TABLE_HEADING_COUPON_AMOUNT; ?></font></td>	
											<td class="reportBoxHeading" align="center" width="13%"><font size="1"><?php echo TABLE_HEADING_COUPON_NUMBERS; ?></font></td>
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_PREFIX; ?></font></td>
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_SUFFIX; ?></font></td>
											<td class="reportBoxHeading" align="center"><font size="1"><?php echo TABLE_HEADING_COUPON_STATUS; ?></font></td>	
											<td class="reportBoxHeading" align="center" colspan="3"><font size="1"><?php echo TABLE_HEADING_ACTION; ?></font></td>
										</tr>
<?php
		if ($_GET['page'] > 1) $rows = $_GET['page'] * 20 - 20;
		$dcg_query_raw = "select * from " . TABLE_COUPONS_GENERATION . " where coupon_type != 'G'" . (($dcg_status != '*')? " and coupon_generation_status='".tep_db_input($dcg_status)."'" : "") . " ORDER BY coupon_generation_id DESC";
		$dcg_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $dcg_query_raw, $dcg_query_numrows);
		$dcg_split->show_all = false;
		$dcg_query = tep_db_query($dcg_query_raw);
		while ($dcg_list = tep_db_fetch_array($dcg_query)) {
			$rows++;
			if ($rows % 2 == "1") {
				$tr_classname = "reportListingEven";
			}
			else {
				$tr_classname = "reportListingOdd";
			}
			
			$generated_query = tep_db_query("SELECT COUNT(coupon_id) AS generated_count FROM " . TABLE_COUPONS . " WHERE coupon_generation_id = '" . tep_db_input($dcg_list['coupon_generation_id']) . "'");
			$generated_row = tep_db_fetch_array($generated_query);
			
			$admin_email_result = tep_db_query("SELECT admin_email_address FROM ".TABLE_ADMIN." WHERE admin_id='".$dcg_list['requester_id']."'");
			$admin_email_row = tep_db_fetch_array($admin_email_result);
			
			echo '										<tr class="'.(($_GET['cgid']==$dcg_list['coupon_generation_id'])? "rowSelected":$tr_classname).'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$tr_classname.'\')" onclick="rowClicked(this, \''.$tr_classname.'\')">' . "\n";
			$coupon_description_query = tep_db_query("select coupon_generation_name from " . TABLE_COUPONS_GENERATION_DESCRIPTION . " where coupon_generation_id = '" . tep_db_input($dcg_list['coupon_generation_id']) . "' and language_id = '" . $_SESSION['languages_id'] . "'");
			$coupon_desc = tep_db_fetch_array($coupon_description_query);
?>
<?php if ($view_discount_code_generation_permission || $dcg_list['requester_id'] == $_SESSION['login_id']) { ?>
											<td class="dataTableContent"><?php echo (($dcg_list['coupon_generation_status']!='Y' || $generated_row['generated_count']==0) ? tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER,'' , '' , ' align="absmiddle"') . "&nbsp;" . $coupon_desc['coupon_generation_name'] : '<a href="' . tep_href_link(FILENAME_COUPON_ADMIN, 'action=coupons_list&cgid='.$dcg_list['coupon_generation_id'].((isset($_GET['page']))? '&page='.$_GET['page']:'')) . '">' . tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER,'' , '' , ' align="absmiddle"') . '&nbsp;' . $coupon_desc['coupon_generation_name'] . '</a>'); ?></td>
<?php } else { ?>
											<td class="dataTableContent"><?php echo tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER,'' , '' , ' align="absmiddle"') . "&nbsp;" . $coupon_desc['coupon_generation_name']; ?></td>
<?php } ?>
											<td class="dataTableContent" align="center"><?php echo $admin_email_row['admin_email_address']; ?></td>
											<td class="dataTableContent" align="center">
<?php  
			
			if ($dcg_list['coupon_type'] == 'P') {
				echo $dcg_list['coupon_amount'] . '%';
			} elseif ($dcg_list['coupon_type'] == 'S') {
				echo TEXT_FREE_SHIPPING;
			} else {
				echo $currencies->format($dcg_list['coupon_amount']);
			}
?>
											&nbsp;</td>
											<td class="dataTableContent" align="center"><?php echo $generated_row['generated_count'] . ' / ' . $dcg_list['coupon_number']; ?></td>
											<td class="dataTableContent" align="center"><?php echo $dcg_list['coupon_code_prefix']; ?></td>
											<td class="dataTableContent" align="center"><?php echo $dcg_list['coupon_code_suffix']; ?></td>
											<td class="dataTableContent" align="center">
<?php
			switch ($dcg_list['coupon_generation_status']) {
				case 'P':
					echo tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_YELLOW, 10, 10);
					break;
				case 'Y':
					echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
					break;
				case 'N':
				default:
					echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
					break;
			}
?>
											</td>
											<td class="dataTableContent" align="center" width="5%">
<?php
			if ($edit_discount_code_generation_permission || $dcg_list['requester_id'] == $_SESSION['login_id']) {
				echo '<a href="'.tep_href_link('coupon_admin.php','action=generationedit&cgid='.$dcg_list['coupon_generation_id'].((isset($_GET['page']))? '&page='.$_GET['page']:''),'NONSSL').'">Edit</a>';
			}
?>
											</td>
											<td class="dataTableContent" align="center" width="5%">
<?php
			if ($approve_discount_code_generation_permission && $dcg_list['coupon_generation_status']=='P') {
				echo '<a href="'.tep_href_link('coupon_admin.php','action=generationapprove&cgid='.$dcg_list['coupon_generation_id'],'NONSSL').'" onClick="return get_comment(this)">Approve</a>';
			}
?>
											</td>
											<td class="dataTableContent" align="center" width="5%">
<?php
			if ($approve_discount_code_generation_permission && $dcg_list['coupon_generation_status']=='P') {
				echo '<a href="'.tep_href_link('coupon_admin.php','action=generationcancel&cgid='.$dcg_list['coupon_generation_id'],'NONSSL').((isset($_GET['page']))? '&page='.$_GET['page']:'').'" onClick="return confirm_delete(this)">Cancel</a>';
			}
?>
											</td>
										</tr>
<?php
		}
?>
										<tr>
											<td colspan="10">
												<table border="0" width="100%" cellspacing="0" cellpadding="2">
													<tr>
														<td class="smallText">&nbsp;<?php if (is_object($dcg_split)) { echo $dcg_split->display_count($dcg_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_COUPONS); } ?>&nbsp;</td>
														<td align="right" class="smallText">&nbsp;<?php if (is_object($dcg_split)) { echo $dcg_split->display_links($dcg_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'action')).(isset($_POST['dcg_status'])? '&dcg_status='.$_POST['dcg_status']:'')); } ?>&nbsp;</td>
													</tr>
													<tr>
														<td align="right" colspan="2" class="smallText">
															<?php
															if ($add_discount_code_generation_permission) {
																echo tep_button(BUTTON_INSERT, ALT_BUTTON_INSERT, tep_href_link('coupon_admin.php', 'page=' . $_GET['page'] . '&cgID=' . $cgInfo->coupon_generation_id . '&action=generationnew'), '', 'inputButton');
															}
															?>
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
									
									<script language="javascript">
										function urlencode(str) {
											return escape(str).replace(/\+/g,'%2B').replace(/%20/g, '+').replace(/\*/g, '%2A').replace(/\//g, '%2F').replace(/@/g, '%40');
										}
										
										function get_comment(this_object) {
											var user_comment = prompt('Please provide your reason of change:','');
											if (user_comment!=null) {
												var original_url = this_object.href;
												var new_url = original_url + '&comments=' + urlencode(user_comment);
												this_object.href = new_url;
												return true;
											} else {
												return false;
											}
										}
										
										function confirm_delete(this_object) {
											var text = 'Are you sure you want to CANCEL this discount code generation?';
											if (confirm(text)) {
												return get_comment(this_object);
											} else {
												return false;
											}
										}
									</script>
								</td>
<?php
}
?>
							</tr>
						</table>
					</td>
<!-- body_text_eof //-->
				</tr>
			</table>
		</td>
	</tr>
</table>
<script language="javascript">
	function showProducts() {
		var prds = jQuery("#coupon_products").val();
		if (prds.length > 0) {
			jQuery.ajax({
				type: 'POST',
				url: 'coupon_admin_xmlhttp.php',
				data: {'action' : 'show_products_hint', pids : prds}, 
				async: false,
				dataType: 'xml',
				success: function(xml){
					var product_html = '';
					jQuery(xml).find('response').each(function(){
						product_html += jQuery("products_html", this).text();
					});
					
					jquery_confirm_box(product_html, 1, 0, 'Products Name', 0, 800);
				}
			});
		}
	}
	
	function showCats() {
		var cats = jQuery("#coupon_categories").val();
		if (cats.length > 0) {
			jQuery.ajax({
				type: 'POST',
				url: 'coupon_admin_xmlhttp.php',
				data: {'action' : 'show_categories_hint', cats : cats}, 
				async: false,
				dataType: 'xml',
				success: function(xml){
					var cat_html = '';
					jQuery(xml).find('response').each(function(){
						cat_html += jQuery("categories_html", this).text();
					});
					
					jquery_confirm_box(cat_html, 1, 0, 'Categories Name', 0, 800);
				}
			});
		}
	}
</script>
<!-- body_eof //-->
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>