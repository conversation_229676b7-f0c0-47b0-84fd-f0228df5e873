<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$cats_param = (isset($_REQUEST['cats']) ? urldecode($_REQUEST['cats']) : '');
$pids_param = (isset($_REQUEST['pids']) ? urldecode($_REQUEST['pids']) : '');

echo '<response>';
if (tep_not_null($action)) {
	switch($action) {
		case "show_categories_hint":
			if (tep_not_null($cats_param)) {
				$cats_name_array = array();
				$html_table = '';
				
				$cats_id_array = explode(',', $cats_param);
				foreach ($cats_id_array as $cat_id) {
					if (tep_not_null(trim($cat_id))) {
						$cat_path = tep_output_generated_category_path_sq($cat_id);
						$cats_name_array[] = array('id' => $cat_id, 'text' => $cat_path);
					}
				}
				
				if (count($cats_name_array) > 0) {
					$cat_cnt = count($cats_name_array);
					$tab_num = 1;
					if ($cat_cnt > 10) {
						$tab_num = (floor($cat_cnt / 10)) + (($cat_cnt % 10 === 0) ? 0 : 1);
					}
					
					$html_table .= '<div id="res_tab">';
					$html_table .= '<ul>';
					for ($i = 1; $i <= $tab_num; $i++) {
						$html_table .= '<li id="res_li_'.$i.'"><a href="#res_tab_'.$i.'"><span>Page '.$i.'</span></a></li>';
					}
					$html_table .= '</ul>';
					
					$m = 0;
					$tab_n = 0;
					$tab_opened = false;
					
					foreach ($cats_name_array as $cat_path) {
						if ($m % 10 === 0) {
							$tab_opened = true;
							$tab_n++;
							$html_table .= '<div id="res_tab_'.$tab_n.'" class="res_tab">';
							$html_table .= '	<table border="0" width="100%" cellspacing="2" cellpadding="6">';
						}
						
						$html_table .= '		<tr><td class="dataTableContent">'.$cat_path['id'].'</td><td class="dataTableContent">'.$cat_path['text'].'</td></tr>';
						$m++;
						
						if ($tab_opened === true && $m % 10 === 0) {
							$html_table .= '	</table>';
							$html_table .= '</div>';
							$tab_opened = false;
						}
					}
					
					if ($tab_opened === true) {
						$html_table .= '	</table>';
						$html_table .= '</div>';
					}
					
					$html_table .= '</div>';
					$html_table .= '<script language="javascript">';
					$html_table .= '	jQuery("#res_tab > ul").tabs();';
					$html_table .= '	jQuery("res_tab").css({';
					$html_table .= '		border:"1px solid #C9C9C9"';
					$html_table .= '	});';
					$html_table .= '</script>';
				} else {
					$html_table .= '<table width="100%" align="center" border="1" cellpadding="5" cellspacing="2" class="main">';
					foreach ($cats_id_array as $cat_id) {
						$html_table .= '<tr><td class="dataTableContent">'.$cat_id.'</td><td class="dataTableContent">&nbsp;</td></tr>';
					}
					$html_table .= '</table>';
				}
				
				echo "<html_result>";
				echo "<categories_html><![CDATA[".$html_table."]]></categories_html>";
				echo "</html_result>";
			} else {
				echo "<html_result></html_result>";
			}
			
			break;
		
		case "show_products_hint":
			if (tep_not_null($pids_param)) {
				$prds_name_array = array();
				$html_table = '';
				
				$prds_id_array = explode(',', $pids_param);
				foreach ($prds_id_array as $p_id) {
					if (tep_not_null(trim($p_id))) {
						$prd_path = tep_get_product_path($p_id);
						$prds_name_array[] = array('id' => $p_id, 'text' => $prd_path);
					}
				}
				
				if (count($prds_name_array) > 0) {
					$prd_cnt = count($prds_name_array);
					$tab_num = 1;
					if ($prd_cnt > 10) {
						$tab_num = (floor($prd_cnt / 10)) + (($prd_cnt % 10 === 0) ? 0 : 1);
					}
					
					$html_table .= '<div id="res_tab">';
					$html_table .= '<ul>';
					for ($i = 1; $i <= $tab_num; $i++) {
						$html_table .= '<li id="res_li_'.$i.'"><a href="#res_tab_'.$i.'"><span>Page '.$i.'</span></a></li>';
					}
					$html_table .= '</ul>';
					
					$m = 0;
					$tab_n = 0;
					$tab_opened = false;
					
					foreach ($prds_name_array as $prd_path) {
						if ($m % 10 === 0) {
							$tab_opened = true;
							$tab_n++;
							$html_table .= '<div id="res_tab_'.$tab_n.'" class="res_tab">';
							$html_table .= '	<table border="0" width="100%" cellspacing="2" cellpadding="6">';
						}
						
						$html_table .= '		<tr><td class="dataTableContent">'.$prd_path['id'].'</td><td class="dataTableContent">'.$prd_path['text'].'</td></tr>';
						$m++;
						
						if ($tab_opened === true && $m % 10 === 0) {
							$html_table .= '	</table>';
							$html_table .= '</div>';
							$tab_opened = false;
						}
					}
					
					if ($tab_opened === true) {
						$html_table .= '	</table>';
						$html_table .= '</div>';
					}
					
					$html_table .= '</div>';
					$html_table .= '<script language="javascript">';
					$html_table .= '	jQuery("#res_tab > ul").tabs();';
					$html_table .= '	jQuery("res_tab").css({';
					$html_table .= '		border:"1px solid #C9C9C9"';
					$html_table .= '	});';
					$html_table .= '</script>';
				} else {
					$html_table .= '<table width="100%" align="center" border="1" cellpadding="5" cellspacing="2" class="main">';
					foreach ($prds_id_array as $p_id) {
						$html_table .= '<tr><td class="dataTableContent">'.$p_id.'</td><td class="dataTableContent">&nbsp;</td></tr>';
					}
					$html_table .= '</table>';
				}
				
				echo "<html_result>";
				echo "<products_html><![CDATA[".$html_table."]]></products_html>";
				echo "</html_result>";
			} else {
				echo "<html_result></html_result>";
			}
			
			break;
		
		default:
			echo "<result>Unknown request!</result>";
			
			break;
	}
}

echo '</response>';
?>