<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

// Prevent direct access from Web URL
if (!isset($_SERVER['argv'])) {
	exit;
} else {
	$permission_code = $_SERVER['argv'][1];
}

tep_db_connect() or die('Unable to connect to database server!');

if ($permission_code == 'kj2601ed#%337c7c') {
	$daily_limit_update_sql = "	UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . " 
								SET admin_credit_limit_total = 0
								WHERE 1";
	tep_db_query($daily_limit_update_sql);

	//Reset CDKey View Limit Daily
	$cdkey_view_limit_update_sql = "UPDATE " . TABLE_CDKEY_VIEW . " SET cdkey_total_view = 0";
	tep_db_query($cdkey_view_limit_update_sql);
}
?>