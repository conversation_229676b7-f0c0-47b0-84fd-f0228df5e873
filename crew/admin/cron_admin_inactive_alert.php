<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_db_connect() or die('Unable to connect to database server!');

$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

tep_set_time_limit(0);

if (defined('STORE_INACTIVE_ADMIN_ALERT_EMAIL_ADDRESS') && tep_not_null(STORE_INACTIVE_ADMIN_ALERT_EMAIL_ADDRESS)) {
	$inactive_admin_select_sql = "	SELECT admin_email_address 
									FROM " . TABLE_ADMIN . " 
									WHERE IF(admin_logdate IS NULL, 
										admin_created < DATE_SUB(NOW(), INTERVAL " . (int)MAX_INACTIVE_ADMIN_LOGIN_DAYS . " DAY),
										admin_logdate < DATE_SUB(NOW(), INTERVAL " . (int)MAX_INACTIVE_ADMIN_LOGIN_DAYS . " DAY))
									ORDER BY admin_email_address";
	$inactive_admin_result_sql = tep_db_query($inactive_admin_select_sql);
	
	$inactive_admin_array = array();

	while ($inactive_admin_row = tep_db_fetch_array($inactive_admin_result_sql)) {
		$inactive_admin_array[] = $inactive_admin_row['admin_email_address'];
	}
	
	if (count($inactive_admin_array)) {
		$email_contents = "<b>" . count($inactive_admin_array) . " admin members did not login to the system for more than " . MAX_INACTIVE_ADMIN_LOGIN_DAYS . " days</b>\n\n";
		$email_contents .= implode("\n", $inactive_admin_array);
		
		$admin_email_to_array = tep_parse_email_string(STORE_INACTIVE_ADMIN_ALERT_EMAIL_ADDRESS);
   		for ($admin_email_to_cnt=0; $admin_email_to_cnt < count($admin_email_to_array); $admin_email_to_cnt++) {
   			@tep_mail($admin_email_to_array[$admin_email_to_cnt]['name'], $admin_email_to_array[$admin_email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Inactive Admin Members')), $email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
   		}
	}
}
?>