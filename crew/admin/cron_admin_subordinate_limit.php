<?php

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

// Prevent direct access from Web URL
if (!isset($_SERVER['argv'])) {
    exit;
} else {
    $permission_code = $_SERVER['argv'][1];
}

tep_db_connect() or die('Unable to connect to database server!');

if ($permission_code == 'm7uPANyuByy2jdF') {
    $sql = "UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . " SET reset_team_limit_used = 0 WHERE 1";
    tep_db_query($sql);
}
?>