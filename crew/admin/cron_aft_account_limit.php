<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_CLASSES . 'json.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_set_time_limit(0);

$cron_filename = 'cron_aft_account_limit.php';
$cron_title = 'Aft Account Limit ';

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 1 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=1, 
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0 
                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($cron_process_update_sql);

        read_and_update_last_call();

        validate_limit_and_send_notification();

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=0 
                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = $cron_title . 'cronjob failed at ' . date("Y-m-d H:i:s");
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}

function validate_limit_and_send_notification() {
    $aft_admin_email = '<EMAIL>';

    $account_limit_sql = " SELECT aacl.countries_id, aacl.aft_limit, aacl.aft_duration, aacl.last_call_total, c.countries_name
                            FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " AS aacl INNER JOIN " . TABLE_COUNTRIES . " AS c ON aacl.countries_id = c.countries_id 
                            WHERE last_call > next_call";

    $account_limit_result_sql = tep_db_query($account_limit_sql);


    while ($account_limit_row = tep_db_fetch_array($account_limit_result_sql)) {
        if ($account_limit_row['last_call_total'] > $account_limit_row['aft_limit']) {
            // fire an email notification
            $body = "<table width=100% border=0>";
            $body .= '<tr><td colspan=2><strong>Dear AFT,</strong></td></tr>';
            $body .= '<tr><td colspan=2><br/><font size=3>There was a spike in account creation for ' . $account_limit_row['countries_name'] . '.</font><br/> Please go through the relevant country <b></b></td></tr>';
            $body .= '<tr><td colspan=2>Make sure there were no suspicious activity. Take note on : <br/></td></tr>';
            $body .= '<tr><td colspan=2>- SHARED IP History Page <br/>
                                        - Device ID and IP History Page <br/>
                                        - Account with similar amount/ product <br/>
                                        - Payment gateway used 
                           </td></tr></table>';

            @tep_mail($aft_admin_email, $aft_admin_email, 'Account creation spike', $body, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

            $data_array = array(
                'countries_id' => $account_limit_row['countries_id'],
                'aft_limit' => $account_limit_row['aft_limit'],
                'aft_duration' => $account_limit_row['aft_duration'],
                'last_call_total' => $account_limit_row['last_call_total'],
                'created_date_time' => 'now()',
            );

            tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT_LOG, $data_array);
        }

        $data_array = array(
            'next_call' => date('Y-m-d H:i:s', strtotime('+ ' . $account_limit_row['aft_duration'] . 'minutes')),
            'last_call_total' => 0,
            'cron_last_modified_date_time' => 'now()',
        );

        tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT, $data_array, 'update', 'countries_id="' . tep_db_input($account_limit_row['countries_id']) . '"');
    }
}

function read_and_update_last_call() {
    // Step 1 Query records

    $account_limit_sql = " SELECT countries_id, last_call, last_call_total
                                            FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " ORDER BY countries_id ASC";

    $account_limit_result_sql = tep_db_query($account_limit_sql);

    while ($account_limit_row = tep_db_fetch_array($account_limit_result_sql)) {

        $country_id = $account_limit_row['countries_id'];
        $last_call_total = $account_limit_row['last_call_total'];
        $last_call = $account_limit_row['last_call'];
        $next_call = date('Y-m-d H:i:s');

        $account_created_by_country_sql = " SELECT count(customers_info_id) as total_account
                                   FROM " . TABLE_CUSTOMERS_INFO . " 
                                   WHERE customers_info_date_account_created BETWEEN '$last_call' AND '$next_call' AND
                                   customer_info_selected_country = $country_id";

        $account_created_by_country_result_sql = tep_db_query($account_created_by_country_sql);

        if ($account_created_by_country_row = tep_db_fetch_array($account_created_by_country_result_sql)) {
            $total_account = $account_created_by_country_row['total_account'];

            $data_array = array(
                'last_call' => $next_call,
                'last_call_total' => $last_call_total + $total_account,
                'cron_last_modified_date_time' => 'now()',
            );

            // Step 2  Update last call and last call total

            tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT, $data_array, 'update', 'countries_id="' . tep_db_input($country_id) . '"');
        }
    }
}

?>