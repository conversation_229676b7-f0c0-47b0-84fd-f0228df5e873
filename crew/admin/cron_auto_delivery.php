<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

$_REQUEST['action'] = ''; // used in po_supplier.php language file
$language = 'english';  // used in store_credit.php class
$default_languages_id = 1;
$languages_id = $default_languages_id;

tep_set_time_limit(0);
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('   SELECT configuration_key as cfgKey, configuration_value as cfgValue
                                        FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

define('PARTIAL_DELIVERY_STATUS', 2);
define('TABLE_COUPON_GV_CUSTOMER', 'coupon_gv_customer');

include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_CLASSES . 'auto_delivery.php');

// Update Last Process Date
$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                            SET cron_process_track_in_action = 0,
                                cron_process_track_start_date = now(),
                                cron_process_track_failed_attempt = 0
                            WHERE cron_process_track_filename = 'cron_auto_delivery.php'";
tep_db_query($cron_process_update_sql);

try {
    $extra_params = array(
        'delivery_sc_permission' => array('full_cat' => true)
    );

    $ad_obj = new auto_delivery();
    $ad_obj->processBatchFullDelivery($extra_params);
    unset($ad_obj);
} catch (Exception $e) {
    reportError(array('e' => $e->getMessage()));
}


function reportError($response_data, $ext_subject = '') {
    ob_start();
    echo "<pre>" . $ext_subject;
    echo "================================================RESPONSE================================================";
    print_r($response_data);
    echo "========================================================================================================";
    $response_data = ob_get_contents();
    ob_end_clean();

    $subject = '[OFFGAMERS] Cron Auto Delivery Error - ' . date("F j, Y H:i");

    @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

?>