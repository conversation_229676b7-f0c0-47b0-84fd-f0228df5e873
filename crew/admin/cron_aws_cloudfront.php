<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$cron_filename = 'cron_aws_cloudfront.php';
$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

$cron_process_checking_row = query_cron_process($cron_filename);
if (count($cron_process_checking_row)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        lock_cron_process($cron_filename);

        $cf_bucket = '';
        $cf_id_array = array();
        $paths_array = array();
        $aws_obj = new ogm_amazon_ws();
        $aws_obj->init_cloudfront();

        $get_bkey_select_sql = "    SELECT bucket_key
                                    FROM " . TABLE_CRON_AWS_CF . " 
                                    ORDER BY cf_id
                                    LIMIT 1";
        $get_bkey_result_sql = tep_db_query($get_bkey_select_sql);
        while ($get_bkey_row = tep_db_fetch_array($get_bkey_result_sql)) {
            $cf_bucket = $get_bkey_row['bucket_key'];
        }

        if (!empty($cf_bucket)) {
            $cf_select_sql = "	SELECT cf_id, bucket_key, filepath, filename
                                FROM " . TABLE_CRON_AWS_CF . " 
                                WHERE bucket_key = '" . $cf_bucket . "'";
            $cf_result_sql = tep_db_query($cf_select_sql);
            while ($cf_row = tep_db_fetch_array($cf_result_sql)) {
                $cf_id_array[] = $cf_row['cf_id'];
                $paths_array[] = $cf_row['filepath'] . urlencode($cf_row['filename']);
            }

            if ($paths_array) {
                $distribution_id = $aws_obj->get_distribution_id_by_bucket($cf_bucket);

                if (!empty($distribution_id)) {
                    if ($aws_obj->clear_cloudfront_cache($distribution_id, $paths_array)) {
                        // Clean up once success
                        $delete_sql = "DELETE FROM " . TABLE_CRON_AWS_CF . " WHERE cf_id IN ('" . implode("','", $cf_id_array) . "')";
                        tep_db_query($delete_sql);
                    }
                } else {
                    $delete_sql = "DELETE FROM " . TABLE_CRON_AWS_CF . " WHERE cf_id IN ('" . implode("','", $cf_id_array) . "')";
                    tep_db_query($delete_sql);
                }

                unset($cf_id_array, $paths_array);
            }
        }

        unlock_cron_process($cron_filename);
    } else {
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['cron_process_track_failed_attempt'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                                                            SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                                                            WHERE cron_process_track_filename = '" . $cron_filename . "'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'AWS CF cronjob failed at ' . $cron_process_datetime;
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                unlock_cron_process($cron_filename);
            }
        }
    }
}

function lock_cron_process($filename) {
    $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                SET cron_process_track_in_action = 1, 
                                    cron_process_track_start_date = now(), 
                                    cron_process_track_failed_attempt = 0 
                                WHERE cron_process_track_filename = '" . $filename . "'";
    tep_db_query($cron_process_update_sql);
}

function unlock_cron_process($filename) {
    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                SET cron_process_track_in_action = 0 
                                WHERE cron_process_track_filename = '" . $filename . "'";
    tep_db_query($unlock_cron_process_sql);
}

function query_cron_process($filename) {
    $cron_process_checking_select_sql = "	SELECT  cron_process_track_in_action, 
                                                    cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, 
                                                    cron_process_track_failed_attempt 
                                            FROM " . TABLE_CRON_PROCESS_TRACK . "
                                            WHERE cron_process_track_filename = '" . $filename . "'";
    $cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
    return tep_db_fetch_array($cron_process_checking_result_sql);
}

?>