<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_set_time_limit(0);

$languages_id = 1;
$cron_filename = 'cron_cancel_buyback.php';
$cron_title = 'Cron Buy & Sell Order Auto Complete ';
$cron_fail_mail_recipient = '<EMAIL>';
$total_processed = 0;
$max_count = 50;

tep_db_connect() or die('Unable to connect to database server!');
                
// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}
        
$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 3 MINUTE) AS overdue_process, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AS long_process, cron_process_track_failed_attempt 
										FROM " . TABLE_CRON_PROCESS_TRACK . "
										WHERE cron_process_track_filename = '" . $cron_filename . "'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=1, 
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0 
									WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($cron_process_update_sql);


        include(DIR_WS_LANGUAGES . 'english.php');
        include(DIR_WS_LANGUAGES . 'english/' . FILENAME_BUYBACK_REQUESTS_INFO);
        include(DIR_WS_LANGUAGES . 'buyback_system.php');

        require_once(DIR_WS_FUNCTIONS . 'supplier.php');
        include_once(DIR_WS_CLASSES . 'currencies.php');
        include_once(DIR_WS_CLASSES . 'vip_order.php');
        include_once(DIR_WS_CLASSES . 'vip_groups.php');
        include_once(DIR_WS_CLASSES . 'mime.php');
        include_once(DIR_WS_CLASSES . 'email.php');
        require_once(DIR_WS_CLASSES . 'order.php');
        include_once(DIR_WS_CLASSES . 'c2c_order.php');
        include_once(DIR_WS_CLASSES . 'c2c_buyback_order.php');

        // include caching class
        require(DIR_WS_CLASSES . 'cache_abstract.php');
        require(DIR_WS_CLASSES . 'memcache.php');
        $memcache_obj = new OGM_Cache_MemCache();

        $read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
        ## PART 1: Load all the order which status are PROCESSING
        ## PART 2: Checking: Is Order Qty Fully Delivered?
        ## PART 3: Checking: Auto confirm delivered, Which counting time exceeded but haven't update. Will update here.
        ## PART 4: Checking: Fully confirm delivered?
        ## PART 5: If Order QTY Fully delivered + Customer confirmed all the QTY delivered then order will update to COMPLETED

        $order_id_arr = array();

        $currencies = new currencies();
        ## PART 1: Load all the order which status are PROCESSING
        $order_select_sql = "	SELECT DISTINCT o.orders_id, o.date_purchased, o.orders_status
						FROM " . TABLE_ORDERS . " AS o
						INNER JOIN " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph
							ON ( o.orders_id = oph.orders_id )
						WHERE o.orders_status = 2";
        $order_select_result_sql = tep_db_query($order_select_sql, 'read_db_link');

        while ($order_select_row = tep_db_fetch_array($order_select_result_sql)) {
            if ($total_processed >= $max_count)
                break;

            $order_id = $order_select_row['orders_id'];
            $date_purchased = $order_select_row['date_purchased'];
            $orders_status = $order_select_row['orders_status'];
            $site_id = c2c_order::orderSiteID($order_id);

            $total_product_currency_qty = 0;
            $undelivered_qty = 0;
            $fully_delivered_order = false;
            $fully_confirm_delivered = false;

            if ($orders_status == '2') {
                ## PART 2: Checking: Order Qty Fully Delivered?
                $order = new order($order_id);
                $order->get_compensate_products();
                if (($order->info['normal_purchase_fully_delivered'] == 1) && ($order->info['compensation_fully_delivered'] == 1)) {
                    $fully_delivered_order = true;
                } else {
                    $fully_delivered_order = false;
                }
            }

            ## PART 3: Checking: Auto confirm delivered, Which counting time exceeded but haven't update. Will update here.
            $auto_confirm_delivered_select_sql = "	SELECT NOW() as nowtime, date_confirm_delivered, buyback_request_group_id, orders_products_id, orders_products_history_id
                                                FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . "
                                                WHERE orders_id = '" . (int) $order_id . "'
                                                    AND received IS NULL
                                                    AND rolled_back = 0";
            $auto_confirm_delivered_result_sql = tep_db_query($auto_confirm_delivered_select_sql, 'read_db_link');

            while ($auto_confirm_delivered_row = tep_db_fetch_array($auto_confirm_delivered_result_sql)) {
                $confirmation_eta = strtotime($auto_confirm_delivered_row['date_confirm_delivered']);
                $current_datetime = strtotime($auto_confirm_delivered_row['nowtime']);
                $orders_products_id = $auto_confirm_delivered_row['orders_products_id'];

                if ($current_datetime > $confirmation_eta) {
                    $buyback_request_group_id = $auto_confirm_delivered_row['buyback_request_group_id'];

                    $update_history_data_sql = array();
                    $update_history_data_sql['changed_by'] = 'system';
                    $update_history_data_sql['received'] = '1';
                    $update_history_data_sql['last_updated'] = 'now()';
                    $update_history_data_sql['date_confirm_delivered'] = 'now()';
                    tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' orders_products_history_id = "' . (int) $auto_confirm_delivered_row['orders_products_history_id'] . '" ');
                }
            }

            if ($site_id == 5) {
                $_sel = "   SELECT cb.c2c_buyback_id, cb.seller_id, cb.status, cb.date_added,
                                cbp.product_id, cbp.custom_products_type, cbp.delivered_quantity, cbp.orders_products_id,
                                csg.payout_grace_period_offset, cbp.c2c_products_listing_id 
                            FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                            INNER JOIN " . TABLE_C2C_BUYBACK . " AS cb
                                ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                            INNER JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
                                ON cc.customers_id = cb.seller_id
                            INNER JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
                                ON csg.seller_group_id = cc.seller_group_id
                            WHERE cbp.orders_id = " . $order_id . "
                                AND cbp.delivered_quantity > 0
                                AND cb.status = 2";
                $_res = tep_db_query($_sel);
                if ($_row = tep_db_fetch_array($_res)) {
                    $fully_delivered_order = false;
                    $fully_confirm_delivered = false;

                    $_oph_sel = "   SELECT SUM(delivered_amount) AS delivered_amount
                                    FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . "
                                    WHERE buyback_request_group_id = " . $_row['c2c_buyback_id'] . "
                                        AND received = 1";
                    $_oph_res = tep_db_query($_oph_sel);
                    if ($_oph_row = tep_db_fetch_array($_oph_res)) {
                        # complete deliver
                        if ($_oph_row['delivered_amount'] == $_row['delivered_quantity']) {
                            $fully_delivered_order = true;
                            $fully_confirm_delivered = true;

                            # complete `processing` sell order
                            $cb_attr = array('status' => 3);
                            tep_db_perform(TABLE_C2C_BUYBACK, $cb_attr, 'update', "c2c_buyback_id = '" . $_row['c2c_buyback_id'] . "'");
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set success_so = success_so+1 where seller_id = '" . $_row['seller_id'] . "'";
                            tep_db_query($csr_update);

                            $cbh_attr = array(
                                'c2c_buyback_id' => $_row['c2c_buyback_id'],
                                'status' => 3,
                                'date_added' => 'now()',
                                'seller_notified' => 0,
                                'comments' => '',
                                'set_as_remarks' => '0',
                                'changed_by' => 'system'
                            );
                            tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                            unset($cbh_attr);

                            /*
                              Preparing data for scheduled cron job
                              This step applies to supplier buyback (user type=1) AND customer buyback(user type=0)
                             */
                            $cron_pending_credit_mature_period = 0;
                            $cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date
                                                    FROM " . TABLE_CRON_PENDING_CREDIT . "
                                                    WHERE cron_pending_credit_trans_type = 'SO'
                                                        AND cron_pending_credit_trans_id = '" . tep_db_input($_row['c2c_buyback_id']) . "'";
                            $cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);
                            if (!tep_db_num_rows($cron_job_verify_result_sql)) {
                                include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                                //check for listing insurance duration
                                $orders_products_extra_info_sql = "	SELECT orders_products_extra_info_value
                                FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
                                WHERE orders_products_id = '" . $_row['orders_products_id'] . "'
                                    AND orders_products_extra_info_key = 'listing'";
                                $orders_products_extra_info_result = tep_db_query($orders_products_extra_info_sql);
                                if ($orders_products_extra_info_row = tep_db_fetch_array($orders_products_extra_info_result)) {
                                    $g2g_prod = tep_array_unserialize($orders_products_extra_info_row['orders_products_extra_info_value']);
                                    if(isset($g2g_prod['listing_insurance'])){
                                        $listing_insurance_row = $g2g_prod['listing_insurance'];
                                    }
                                } 

                                if(isset($listing_insurance_row)){
                                    $cron_pending_credit_mature_period = $listing_insurance_row;
                                } else {
                                    $g2g_prod_json = c2c_buy_order::c2c_product_snapshot($date_purchased, $_row['seller_id'], $_row['c2c_products_listing_id']);
                                    if (!empty($g2g_prod_json) && isset($g2g_prod_json['product_listing']["listing_insurance"])) {
                                        //use s3 snapshot order details
                                        $cron_pending_credit_mature_period = $g2g_prod_json['product_listing']["listing_insurance"];
                                    } else {
                                        $_cpl_sql = "   SELECT listing_insurance
                                            FROM " . TABLE_C2C_PRODUCTS_LISTING . "
                                            WHERE c2c_products_listing_id = " . $_row['c2c_products_listing_id'];
                                        $_cpl_res = tep_db_query($_cpl_sql);
                                        if ($_cpl_row = tep_db_fetch_array($_cpl_res)) {
                                            //use listing insurance duration value
                                            $cron_pending_credit_mature_period = $_cpl_row['listing_insurance'];
                                        } else {
                                            //hardcode if not able to find any listing insurance
                                            $cron_pending_credit_mature_period = 14;
                                        }
                                    }
                                }

                                $cron_pending_credit_mature_period = $cron_pending_credit_mature_period * 24 * 60;

                                if ($cron_pending_credit_mature_period <= 0) {
                                    //if listing insurance duration smaller than 0, use current default value.
                                    $cron_pending_credit_mature_period = tep_get_products_payment_mature_period($_row['product_id']) + (int) $_row['payout_grace_period_offset'];
                                }

                                $cpc_attr = array(
                                    'cron_pending_credit_trans_type' => 'SO',
                                    'cron_pending_credit_trans_id' => tep_db_input($_row['c2c_buyback_id']),
                                    'cron_pending_credit_trans_created_date' => $_row['date_added'],
                                    'cron_pending_credit_trans_completed_date' => 'now()',
                                    'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
                                    'cron_pending_credit_trans_status' => 3
                                );
                                tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cpc_attr);
                                unset($cpc_attr);
                            }
                            // End of preparing data for scheduled cron job

                            tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $_row['c2c_buyback_id']);
                            $cbh_attr = array(
                                'c2c_buyback_id' => $_row['c2c_buyback_id'],
                                'status' => 0,
                                'date_added' => 'now()',
                                'seller_notified' => 1,
                                'comments' => sprintf(G2G_COMMENTS_PROCESSING_TO_COMPLETED, ($cron_pending_credit_mature_period + 15)), // +15min cronjob time gap
                                'set_as_remarks' => 1,
                                'changed_by' => 'system'
                            );
                            tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                            unset($cbh_attr);

                            # disable chat and private message permission
                            c2c_buyback_order::_chat_permission($_row['c2c_buyback_id'], $_row['orders_products_id'], 'delete');
                        }
                    }
                }
            }

            if ($fully_delivered_order) {
                if ($site_id != 5) {
                    ## PART 4: Checking: Fully confirm delivered?
                    $order_product_select_sql = "	SELECT op.products_quantity, op.products_good_delivered_quantity, op.orders_products_is_compensate, op.parent_orders_products_id, op.custom_products_type_id
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op
											INNER JOIN " . TABLE_PRODUCTS . " AS p
												ON (op.products_id = p.products_id)
											WHERE op.orders_id = '" . (int) $order_id . "'";
                    $order_product_result_sql = tep_db_query($order_product_select_sql, 'read_db_link');

                    while ($order_product_row = tep_db_fetch_array($order_product_result_sql)) {
                        if ($order_product_row['orders_products_is_compensate'] == '1' || $order_product_row['parent_orders_products_id'] != '0' || $order_product_row['custom_products_type_id'] == 4) {
                            //$products_quantity = $order_product_row['products_quantity'] - ($order_product_row['products_canceled_quantity'] + $order_product_row['products_reversed_quantity']);
                            $products_quantity = $order_product_row['products_good_delivered_quantity'];
                            $total_product_currency_qty += $products_quantity;
                        }
                    }

                    // Get total received qty CONFIRMED by Customer from Order History page
                    $confirm_delivered_select_sql = "	SELECT SUM(delivered_amount) AS total_delivered_amt
												FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . "
												WHERE orders_id = '" . (int) $order_id . "'
													AND received = 1";
                    $confirm_delivered_result_sql = tep_db_query($confirm_delivered_select_sql, 'read_db_link');
                    $confirm_delivered_row = tep_db_fetch_array($confirm_delivered_result_sql);

                    if ($total_product_currency_qty == $confirm_delivered_row['total_delivered_amt']) {
                        $fully_confirm_delivered = true;
                    } else {
                        $diff_day = tep_day_diff($date_purchased, date("Y-m-d H:i:s"));
                        if ($diff_day >= 5) {
                            $order_id_arr[] = $order_id;
                        }
                    }
                }

                ## PART 5: If Order QTY Fully delivered + Customer confirmed all the QTY delivered then order will update to COMPLETED
                if ($fully_confirm_delivered) {
                    $status_cur = '3';
                    $orders_status_update_sql_data = array('orders_status' => $status_cur,
                        'last_modified' => 'now()');
                    tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . (int) $order_id . "'");

                    tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $order_id . "', $status_cur, now(), '0', 'Completed by cronjob', 2, 'system')");
                    tep_update_orders_status_counter(array("orders_id" => $order_id, "orders_status_id" => $status_cur, "date_added" => 'now()', "changed_by" => 'system'));

                    // Update the order's tag since it is updated to "Completed"
                    tep_update_record_tags(FILENAME_STATS_ORDERS_TRACKING, (int) $order_id, $status_cur, '');

                    tep_insert_cron_pending_credit('OP', $order_id, $date_purchased, OP_CREDIT_DURATION, $status_cur);
                    $total_processed++;
                }
            }
        }

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=0 
									WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                       SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                       WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($cron_process_attempt_update_sql);
            if ($cron_process_checking_row['cron_process_track_failed_attempt'] < 3) {
                 tep_mail("Dev", $cron_fail_mail_recipient, "[OFFGAMERS] Buyback Cronjob Failed", 
                         $cron_title . 'cronjob failed at ' . date("Y-m-d H:i:s"), 
                         STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                 
   
        		
            }
        }

        if ($cron_process_checking_row['long_process'] == 1) {  // Prevent cronjob keep awaiting, next cronjob will resume back to normal
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                        SET cron_process_track_in_action=0,
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($cron_process_update_sql);
        }
    }
}
?>