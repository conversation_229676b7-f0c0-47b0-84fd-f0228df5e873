<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

$cron_filename = 'cron_checkout_qty_ctrl.php';
$cron_config_key = 'STOCK_NON_ZERO_PRICE_CHECKOUT_QTY_NOTIFICATION';
$languages_id = 1;
$counter = 1;

$cron_mail_content = '';
$mail_recipient = array();

$configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_CONFIGURATION . ' WHERE configuration_key = "' . $cron_config_key . '"');
if ($configuration = tep_db_fetch_array($configuration_query)) {
    $mail_recipient = $configuration['cfgValue'];
}

if (tep_not_empty($mail_recipient)) {
    $cron_mail_recipient = explode(',', $mail_recipient);

    $cron_process_checking_select_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
    $cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

    if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
        if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                        SET cron_process_track_in_action=1, 
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0 
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($cron_process_update_sql);


            // Retrieve product info which price > 0 and checkout qty ctrl activate
            $products_select_sql = "SELECT p.products_id, p.products_cat_path, pd.products_name 
                                    FROM " . TABLE_PRODUCTS_CHECKOUT_SETTING . " AS pcs 
                                    INNER JOIN " . TABLE_PRODUCTS . " AS p 
                                        ON p.products_id = pcs.products_id
                                    INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
                                        ON p.products_id = pd.products_id 
                                            AND pd.language_id = " . $languages_id . " 
                                    WHERE p.products_price > 0
                                    ORDER BY p.products_cat_path";
            $products_result_sql = tep_db_query($products_select_sql);
            if (tep_db_num_rows($products_result_sql) > 0) {
                $cron_mail_content = 'The following non zero price product(s) has activate Product Checkout Quantity Control:' . "\n\n";

                while ($row = tep_db_fetch_array($products_result_sql)) {
                    $cron_mail_content .= $counter . '. ' . $row['products_cat_path'] . ' > ' . $row['products_name'] . ' (' . $row['products_id'] . ') ' . "\n\n";
                    $counter++;
                }

                if (tep_not_null($cron_mail_recipient)) {
                    foreach ($cron_mail_recipient as $num => $recipient) {
                        $subject = "[OFFGAMERS] Product Checkout Quantity Control";
                        @tep_mail($recipient, $recipient, $subject, $cron_mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }
                }
            }

            echo "DONE";

            // Release cron process "LOCK"
            $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                        SET cron_process_track_in_action=0 
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($unlock_cron_process_sql);
        } else { // Check if previous cron job has overdue / something bad happened
            if ($cron_process_checking_row['overdue_process'] == '1') {
                if ($cron_process_checking_row['overdue_process'] < 5) {
                    $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                        SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
                    tep_db_query($cron_process_attempt_update_sql);
                } else {
                    $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                    $message = 'Products Checkout Control cronjob failed at ' . date("Y-m-d H:i:s");
                    @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
            }
        }
    }
}
?>