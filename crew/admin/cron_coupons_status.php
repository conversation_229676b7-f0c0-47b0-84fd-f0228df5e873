<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

error_reporting(E_ALL ^ E_NOTICE);

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$cron_process_datetime = date("Y-m-d H:i:s");	// Set the time for this cron process

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
										FROM " . TABLE_CRON_PROCESS_TRACK . "
										WHERE cron_process_track_filename = 'cron_coupons_status.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
	if ($cron_process_checking_row['cron_process_track_in_action'] == '0') {	// No any same cron process is running
		$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action = 1, 
										cron_process_track_start_date = now(), 
										cron_process_track_failed_attempt = 0 
									WHERE cron_process_track_filename = 'cron_coupons_status.php'";
		tep_db_query($cron_process_update_sql);
		
		$b4_7days_start = date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d')-7, date('Y')));
		$date_start = date("Y-m-d H:i:s", mktime(date('H'), date('i'), date('s'), date('m'), date('d'), date('Y')));
		$date_end = date("Y-m-d H:i:s", mktime(23, 59, 59, date('m'), date('d'), date('Y')));
		// testing date
		//$b4_7days_start = date("Y-m-d H:i:s", mktime(0, 0, 0, 6, 13-7, 2010));
		//$date_start = date("Y-m-d H:i:s", mktime(0, 0, 0, 6, 13, 2010));
		//$date_end = date("Y-m-d H:i:s", mktime(23, 59, 59, 6, 13, 2010));
		
		// 1. Pending coupon check for start date and move to Active.
		$pending_coupon_array = array();
		$pending_coupons_select_sql = "	SELECT coupon_id 
										FROM " . TABLE_COUPONS . " 
										WHERE coupon_active='P' 
										AND coupon_start_date >= '".$b4_7days_start."' 
										AND coupon_start_date <= '".$date_end."'";
		$pending_coupons_result_sql = tep_db_query($pending_coupons_select_sql);
		while ($pending_coupons_row = tep_db_fetch_array($pending_coupons_result_sql)) {
			$pending_coupon_array[] = $pending_coupons_row['coupon_id'];
		}
		
		$pending_coupons_update_sql = "	UPDATE " . TABLE_COUPONS . " 
										SET coupon_active = 'Y', 
											date_modified = now() 
										WHERE coupon_start_date >= '".$b4_7days_start."' 
											AND coupon_start_date <= '".$date_end."' 
											AND coupon_active = 'P'";
		$update_result = tep_db_query($pending_coupons_update_sql);
		
		if ($update_result) {
			foreach ($pending_coupon_array as $coupon_id) {
				$status_array = array(	'coupon_id' => $coupon_id,
										'coupon_active' => 'Y',
										'date_added' => 'now()',
										'comments' => 'Activate discount coupon code',
										'changed_by' => 'system');
				tep_db_perform(TABLE_COUPONS_STATUS_HISTORY, $status_array);
			}
		}
		unset($update_result);
		unset($pending_coupon_array);
		
		// 2. Active coupon check for expiry date and move to Closed, regardless unlimited usage
		$active_coupon_array = array();
		
		$active_coupon_select_sql = "	SELECT coupon_id 
										FROM " . TABLE_COUPONS . " 
										WHERE coupon_active='Y' 
											AND coupon_expire_date < '".$date_start."'";
		$active_coupon_result_sql = tep_db_query($active_coupon_select_sql);
		while ($active_coupon_row = tep_db_fetch_array($active_coupon_result_sql)) {
			$active_coupon_array[] = $active_coupon_row['coupon_id'];
		}
		
		$active_coupons_update_sql = "	UPDATE " . TABLE_COUPONS . "
										SET coupon_active = 'N', 
											date_modified = now() 
										WHERE coupon_active = 'Y'
											AND coupon_expire_date < '".$date_start."'";
		$update_result = tep_db_query($active_coupons_update_sql);
		
		if ($update_result) {
			foreach ($active_coupon_array as $coupon_id) {
				$status_array = array(	'coupon_id' => tep_db_prepare_input($coupon_id),
										'coupon_active' => 'N',
										'date_added' => 'now()',
										'comments' => 'Discount coupon code expired',
										'changed_by' => 'system');
				tep_db_perform(TABLE_COUPONS_STATUS_HISTORY, $status_array);
			}
		}
		unset($update_result);
		
		// 3. Check for max usage before expiry date, approved status, and not unlimited, then move to expired.
		/*
		$active_cg_select_sql = "	SELECT coupon_generation_id, coupon_number, uses_per_coupon  
									FROM " . TABLE_COUPONS_GENERATION . "
									WHERE uses_per_coupon_unlimited='N'
										AND uses_per_user_unlimited='N'
										AND coupon_generation_status='Y'
										AND coupon_start_date >= '".$date_start."'
										AND coupon_expire_date <= '".$date_end."'";
		$active_cg_result_sql = tep_db_query($active_cg_select_sql);
		while ($active_cg_row = tep_db_fetch_array($active_cg_result_sql)) {
			$running_count = 0;
			// get all coupons within 1 discount code generation
			$coupon_select_sql = "	SELECT coupon_id, uses_per_coupon, uses_per_user 
									FROM " . TABLE_COUPONS . "
									WHERE uses_per_coupon_unlimited='N'
									AND uses_per_user_unlimited='N'
									AND coupon_active='Y'
									AND coupon_start_date >= '".$date_start."'
									AND coupon_expire_date <= '".$date_end."'
									AND coupon_generation_id = '".tep_db_input($active_cg_row['coupon_generation_id'])."'";
			$coupon_result_sql = tep_db_query($coupon_select_sql);
			while ($coupon_row = tep_db_fetch_array($coupon_result_sql)) {
				// check individual coupon maximum allowed usage
				$current_allow_usage = $coupon_row['uses_per_coupon'] * $coupon_row['uses_per_user'];
				
				$coupon_used_select_sql = "	SELECT coupon_id 
											FROM " . TABLE_COUPON_REDEEM_TRACK . "
											WHERE coupon_id = '" . tep_db_input($coupon_row['coupon_id'])."'";
				$coupon_used_result_sql = tep_db_query($coupon_used_select_sql);
				$used_per_coupon_count = tep_db_num_rows($coupon_used_result_sql);
				
				if ($used_per_coupon_count >= $currenct_allow_usage) {
					// has reached the max allow usage for this coupon, closed the coupon
					tep_db_query("UPDATE " . TABLE_COUPONS . " SET coupon_active='N', date_modified = now() WHERE coupon_id = '".tep_db_input($coupon_row['coupon_id'])."'");
				}
				
				$running_count = $running_count + $used_per_coupon_count;
			}
			
			if ($running_count >= ($active_cg_row['coupon_number']*$active_cg_row['uses_per_coupon'])) {
				// has reached total maximum usage
				// do we need to close the discount code generation?
				tep_db_query("UPDATE " . TABLE_COUPONS_GENERATION . " SET coupon_generation_status='N', date_modified = now() WHERE coupon_generation_id = '".tep_db_input($active_cg_row['coupon_generation_id'])."'");
				
				// close all discount codes within the same generation criteria
				tep_db_query("UPDATE " . TABLE_COUPONS . " SET coupon_active='N', date_modified = now() WHERE coupon_generation_id='".tep_db_input($active_cg_row['coupon_generation_id'])."' AND coupon_active='Y' AND uses_per_coupon_unlimited='N' AND uses_per_user_unlimited='N' AND coupon_start_date >= '".$date_start."' AND coupon_expire_date <= '".$date_end."'");
			}
		}
		*/
		
		// Release cron process "LOCK"
		$unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action = 0 
									WHERE cron_process_track_filename = 'cron_coupons_status.php'";
		tep_db_query($unlock_cron_process_sql);
	} else {	// Check if previous cron job has overdue / something bad happened
		if ($cron_process_checking_row['overdue_process'] == '1') {
			if ($cron_process_checking_row['overdue_process'] < 5) {
				$cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
													SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
													WHERE cron_process_track_filename = 'cron_coupons_status.php'";
				tep_db_query($cron_process_attempt_update_sql);
			} else {
				mail("<EMAIL>", "[OFFGAMERS] Cronjob Failed", 'Coupons status cronjob failed at ' . $cron_process_datetime,
				     "From: <EMAIL>\r\n" .
				     "X-Mailer: PHP/" . phpversion());
			}
		}
	}
}
?>