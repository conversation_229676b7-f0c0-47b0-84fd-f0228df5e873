<?php
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');


$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

tep_set_time_limit(0);

if (isset($_SERVER['argv']) && is_numeric($_SERVER['argv'][1])) {
	$customers_groups_id = (int)$_SERVER['argv'][1];
} else if (isset($_REQUEST['cgID']) && is_numeric($_REQUEST['cgID'])) {
	$customers_groups_id = (int)$_REQUEST['cgID'];
} else {
	exit;
}

$customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name 
								FROM customers_groups
								WHERE customers_groups_id = '" . $customers_groups_id . "'";
$customers_groups_result_sql = tep_db_query($customers_groups_select_sql, 'read_db_link');
$customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);
$customers_groups = $customers_groups_row['customers_groups_name'];

$filename = date('YmdHis').'.csv';
//$file_location = 'C:/Apache2/vhosts/pima.localhost/httpdocs/admin/customers_stats_' . $customers_groups . '_'.$filename;
$file_location = 'download/30days/' . $customers_groups . '_'.$filename;

if (!$handle = fopen($file_location, 'a+')) {
     exit;
}


$countries_array = array();
$countries_select_sql = "	SELECT countries_id, countries_name 
							FROM countries";
$countries_result_sql = tep_db_query($countries_select_sql, 'read_db_link');
while ($countries_row = tep_db_fetch_array($countries_result_sql)) {
	$countries_array[$countries_row['countries_id']] = preg_replace("/,/i", '', $countries_row['countries_name']);
}

$customers_export_csv_data = "Customer ID, Membership, Date registered, Country of residence, Last purchase, Last log-in\n";
fwrite($handle, $customers_export_csv_data);

$customers_array = array();
$customers_select_sql = "	SELECT c.customers_id, c.customers_groups_id, ci.customers_info_date_of_last_logon, 
								ci.customer_info_selected_country, ci.customers_info_date_account_created 
							FROM " . TABLE_CUSTOMERS . " AS c 
							INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
								ON c.customers_id = ci.customers_info_id
							WHERE c.customers_groups_id = '".(int)$customers_groups_id."'";
$customers_result_sql = tep_db_query($customers_select_sql, 'read_db_link');
while($customers_row = tep_db_fetch_array($customers_result_sql)) {
	$customers_array[$customers_row['customers_id']] = $customers_row;
	$customers_array[$customers_row['customers_id']]['date_purchased'] = '';
	if (count($customers_array) >= 30) {
		$orders_select_sql = "	SELECT date_purchased, customers_id 
								FROM " . TABLE_ORDERS . "
								WHERE customers_id IN ('".implode("','", array_keys($customers_array))."')
								GROUP BY customers_id 
								ORDER BY orders_id DESC";
		$orders_result_sql = tep_db_query($orders_select_sql, 'read_db_link');
		while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
			$customers_array[$orders_row['customers_id']]['date_purchased'] = $orders_row['date_purchased'];
		}
		
		$customers_export_csv_data = '';
		foreach ($customers_array as $customers_id_loop => $customers_data_loop) {
			$customers_export_csv_data .= $customers_id_loop . ", " . $customers_groups . ", " . $customers_data_loop['customers_info_date_account_created'] . ", " . (isset($countries_array[$customers_data_loop['customer_info_selected_country']])?$countries_array[$customers_data_loop['customer_info_selected_country']]:'n/a') . ", " . $customers_data_loop['date_purchased'] . ", " . $customers_data_loop['customers_info_date_of_last_logon'] . "\n";
		}
		fwrite($handle, $customers_export_csv_data);
		$customers_array = array();
	}
}

if (count($customers_array)) {
	$orders_select_sql = "	SELECT date_purchased, customers_id 
							FROM " . TABLE_ORDERS . "
							WHERE customers_id IN ('".implode("','", array_keys($customers_array))."')
							GROUP BY customers_id 
							ORDER BY orders_id DESC";
	$orders_result_sql = tep_db_query($orders_select_sql, 'read_db_link');
	while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
		$customers_array[$orders_row['customers_id']]['date_purchased'] = $orders_row['date_purchased'];
	}
	
	$customers_export_csv_data = '';
	foreach ($customers_array as $customers_id_loop => $customers_data_loop) {
		$customers_export_csv_data .= $customers_id_loop . ", " . $customers_groups . ", " . $customers_data_loop['customers_info_date_account_created'] . ", " . (isset($countries_array[$customers_data_loop['customer_info_selected_country']])?$countries_array[$customers_data_loop['customer_info_selected_country']]:'n/a') . ", " . $customers_data_loop['date_purchased'] . ", " . $customers_data_loop['customers_info_date_of_last_logon'] . "\n";
	}
	fwrite($handle, $customers_export_csv_data);
	$customers_array = array();
}
?>