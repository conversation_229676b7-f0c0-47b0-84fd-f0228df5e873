<?php
/*
 * Auto upgrade of AFT Trust Group from Regular to Bronze status. Change requirement to only upgrade after 60 days and hit overbought limit
 */
ini_set('display_errors',1);
error_reporting(E_ALL);

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');

tep_set_time_limit(0);

$languages_id = 1;
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

$show = 0;
$result_str = '';

$start_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d")-67, date("Y")));
$end_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d")-60, date("Y")));

$last_30_day_start_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d")-31, date("Y")));
$last_30_day_end_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d")-1, date("Y")));

$customers_id_select_sql = "SELECT DISTINCT(c.customers_id), c.customers_email_address, c.customers_firstname, c.customers_lastname, c.customers_gender 
							FROM " . TABLE_CUSTOMERS . " AS c 
							INNER JOIN " . TABLE_ORDERS. " AS o 
								ON (o.customers_id = c.customers_id AND o.orders_status = '3') 
							INNER JOIN " . TABLE_SALES_ACTIVITIES . " AS sa 
								ON o.orders_id = sa.sales_activities_orders_id 
							INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
								ON (op.orders_id = o.orders_id AND (op.products_good_delivered_price > 0) )
							WHERE sa.sales_activities_date >= '".$start_date."'
								AND sa.sales_activities_date < '".$end_date."'
								AND c.customers_status = '1' 
								AND NOT FIND_IN_SET('4', c.customers_flag) 
								AND c.customers_aft_groups_id = '2'";
$customers_id_result_sql = tep_db_query($customers_id_select_sql, 'read_db_link');
while ($customers_id_row = tep_db_fetch_array($customers_id_result_sql)) {
	$bad_order_select_sql = "	SELECT orders_id 
								FROM " . TABLE_ORDERS . " 
								WHERE customers_id = '" . (int)$customers_id_row['customers_id'] . "' 
									AND (orders_status = 8 OR (orders_status = 3 AND orders_cb_status = 2)) 
								LIMIT 1";
	$bad_order_result_sql = tep_db_query($bad_order_select_sql, 'read_db_link');
	
	// No recorded orders under Reversed - Lost and On Hold
	if (!tep_db_num_rows($bad_order_result_sql)) {
		// Check if last 30 days transaction hit the overbought limit
		
		$sql = "SELECT o.orders_id
				FROM orders AS o
				INNER JOIN pipwave_payment AS pp
					ON o.orders_id = pp.orders_id
				WHERE o.customers_id = " . tep_db_input($customers_id_row['customers_id']) . "
					AND o.date_purchased >= '" . $last_30_day_start_date . "'
					AND o.date_purchased < '" . $last_30_day_end_date . "'
					AND pp.matched_rules LIKE '%Overbought%'";

		$res = tep_db_query($sql, 'read_db_link');
		if (tep_db_num_rows($res)) { // If hit overbought rules
			// Upgrade to TL2

			$customers_groups_id_update_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
												SET customers_aft_groups_id = '12' 
												WHERE customers_id = '" . tep_db_input($customers_id_row['customers_id']) . "'";
			tep_db_query($customers_groups_id_update_sql);

			$sql_data_array = array('customers_id' => $customers_id_row['customers_id'],
									'date_remarks_added' => 'now()',
									'remarks' => 'Upgraded to Trust Level 2 by system',
									'remarks_added_by' => 'system');
			tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);

			$result_str .= $customers_id_row['customers_id'] . ' | ' . $customers_id_row['customers_email_address'] . "\n";
		}
	}
}

echo $result_str . "\n";

echo 'End of script';
?>