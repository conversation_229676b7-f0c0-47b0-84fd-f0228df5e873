<?
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mail/htmlMimeMail5/htmlMimeMail.php');

tep_set_time_limit(0);

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

//tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$email[] = '"Wei Chen"<<EMAIL>>';

$data_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d")-($day_cnt+1), date("Y")));
$email_subject = 'Customer Newsletter Status ' . $data_date;

$export_csv_array = array();

$export_csv_data = 'Customer Name, Customer Email';
$export_csv_data .= "\n";

$get_customer_rec_select_sql = "SELECT c.customers_email_address, ci.customers_info_date_of_last_logon
								FROM " . TABLE_CUSTOMERS . " AS c 
								inner join customers_info AS ci 
									ON c.customers_id = ci.customers_info_id
								WHERE c.account_activated = '1' 
								AND c.customers_status = '1' 
								AND ci.customers_info_date_of_last_logon >= '2010-01-01 00:00:00'
								AND ((c.customers_newsletter != '') AND (c.customers_newsletter != '0'))";
$get_customer_rec_result_sql = tep_db_query($get_customer_rec_select_sql, 'read_db_link');

while ($get_customer_rec_row = tep_db_fetch_array($get_customer_rec_result_sql)) {
	if (strpos($get_customer_rec_row['customers_email_address'], '@yahoo') === FALSE) {
		$customers_last_login	= '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $get_customer_rec_row['customers_info_date_of_last_logon']). '"';
		$email_address 	= '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $get_customer_rec_row['customers_email_address']) . '"';
		
		$export_csv_data .= "$email_address,$customers_last_login";
		$export_csv_data .= "\n";
	}
}

if (tep_not_null($export_csv_data)) {
	$mail = new htmlMimeMail();
	$mail->setSubject($email_subject);
	$attachment = $export_csv_data;
	
	$mail->addAttachment($attachment, $data_date.'_cus_newsletter_stat.csv', 'text/csv');
	$mail->setFrom(STORE_OWNER_EMAIL_ADDRESS);
	
	if (EMAIL_TRANSPORT == 'smtp') {
		$result = $mail->send($email, 'smtp');
	} else {
		$result = $mail->send($email);
	}
}
?>