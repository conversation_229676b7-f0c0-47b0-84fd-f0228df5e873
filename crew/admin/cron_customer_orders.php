<?php

/*
  Cancellation Period:
  > 10 days for ALL Payment methods
 */

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$customer_email_notification = false;
$language = '';
$jobs_done = 0;

$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_FUNCTIONS . 'sessions.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

include_once(DIR_WS_CLASSES . 'table_block.php');
include_once(DIR_WS_CLASSES . 'box.php');
include_once(DIR_WS_CLASSES . 'message_stack.php');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

$messageStack = new messageStack;
$languages_id = 1; // Define language id
$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

tep_set_time_limit(0);

// DEBUG SETTING - PLEASE REMOVE
//$_SERVER['argv'] = array(1, 10);
// Only perform the action if total records to be processed is passed.
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
    exit;
} else {
    $first_N_record = $_SERVER['argv'][1];
}

if (file_exists(DIR_WS_LANGUAGES . 'english.php')) {
    include_once(DIR_WS_LANGUAGES . 'english.php');
}

if (file_exists(DIR_WS_LANGUAGES . 'english/orders.php')) {
    include_once(DIR_WS_LANGUAGES . 'english/orders.php');
}

// $two_days_ago = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 2, date('Y')));
// $default_floating_days = 2;

$two_hours_ago = date('Y-m-d H:i:s', strtotime('-2 hours'));
$default_floating_hours = 2;

$payment_method_floating_days = array('340' => 4,
    '347' => 4,
    '341' => 4,
    '342' => 4,
    '348' => 4,
    '339' => 4,
    '343' => 4,
    '338' => 4,
    '345' => 4,
    '344' => 4,
    '346' => 4,
    '349' => 4,
    '290' => 4,
    '405' => 4,
    '401' => 4,
    '402' => 4,
    '400' => 4,
    '403' => 4,
    '404' => 4,
    '383' => 4,
    '371' => 4,
    '220' => 3,
    '214' => 3,
    '251' => 3,
    '186' => 3,
    '187' => 3,
    '213' => 3,
    '241' => 3,
    '16' => 3,
    '227' => 3,
    '217' => 3,
    '252' => 3,
    '222' => 3,
    '215' => 3,
    '243' => 3,
    '216' => 3,
    '218' => 3,
    '242' => 3,
    '317' => 3,
    '224' => 3,
    '244' => 3,
    '318' => 3,
    '223' => 3,
    '248' => 3,
    '246' => 3,
    '228' => 3,
    '249' => 3,
    '247' => 3,
    '15' => 7,
    '14' => 7
);

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AS overdue_process, cron_process_track_failed_attempt
                                    FROM " . TABLE_CRON_PROCESS_TRACK . "
                                    WHERE cron_process_track_filename = 'cron_customer_orders.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action=1,
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0
                                    WHERE cron_process_track_filename = 'cron_customer_orders.php'";
        tep_db_query($cron_process_update_sql);

        $pending_status = tep_get_order_status_name(1, 1);
        $cancel_status = tep_get_order_status_name(5, 1);

        // Grab the first N records //LIMIT " . $first_N_record;
        $expired_order_select_sql = "	SELECT o.orders_id, o.customers_id, o.customers_name, o.customers_email_address, oei.orders_extra_info_value, 
                                            o.date_purchased, o.payment_methods_parent_id, o.payment_methods_id,
                                            TIMESTAMPDIFF(HOUR,o.date_purchased,NOW()) as floating_hours
                                        FROM " . TABLE_ORDERS . " AS o
                                        LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei 
                                            ON oei.orders_id = o.orders_id AND oei.orders_extra_info_key = 'site_id'
                                        WHERE o.orders_status = '1'
                                            AND o.date_purchased < '" . $two_hours_ago . "'
                                        ORDER BY o.orders_id DESC
                                        LIMIT 1000";
        $expired_order_result_sql = tep_db_query($expired_order_select_sql);
        while ($expired_order_row = tep_db_fetch_array($expired_order_result_sql)) {
            if ($jobs_done >= $first_N_record)
                break;

            if (isset($payment_method_floating_days[$expired_order_row['payment_methods_id']])) {
                if ($expired_order_row['floating_hours'] < ($payment_method_floating_days[$expired_order_row['payment_methods_id']] * 24)) {
                    continue;
                }
            } else {
                if ($expired_order_row['floating_hours'] < $default_floating_hours) {
                    continue;
                }
            }

            if (isset($expired_order_row['orders_extra_info_value']) && $expired_order_row['orders_extra_info_value'] == "5") {
                //do nothing if g2g order
            } else {
                $oID = $expired_order_row['orders_id'];
                $order_comment = 'Auto Cancellation';
                $notify_comments = sprintf(EMAIL_TEXT_COMMENTS_UPDATE, $order_comment) . "\n";

                // Update from Pending to Canceled - Issue back the store credit used if any
                $scDataRequest = array(
                    'order_id' => $oID,
                    'start_date' => strtotime($expired_order_row['date_purchased']),
                    'end_date' => time(),
                    'activity' => 'P',
                );
                // get all transaction related to orders_id and activity
                $scArrayList = ms_store_credit::getScTransactions($scDataRequest);
                
                if (!$scArrayList) { // If no store credit used in this order
                    $jobs_done += 1;
                    
                    // release coupon
                    $coupon_sql = tep_db_query("SELECT unique_id FROM " . TABLE_COUPON_REDEEM_TRACK . " WHERE order_id = " . tep_db_input($oID));
                    if (tep_db_num_rows($coupon_sql)) {
                        $order_comment .= "\nRelease coupon";
                        tep_db_query("UPDATE " . TABLE_COUPON_REDEEM_TRACK . " SET coupon_id = 0 WHERE order_id = " . tep_db_input($oID));
                    }

                    # Update Order Status
                    $orders_status_update_sql_data = array('orders_status' => 5,
                        'last_modified' => 'now()',
                        'orders_locked_by' => 'NULL',
                        'orders_locked_from_ip' => 'NULL',
                        'orders_locked_datetime' => 'NULL'
                    );
                    tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . tep_db_input($oID) . "'");

                    # Update History
                    $orders_status_history_sql_data = array('orders_id' => $oID,
                        'orders_status_id' => 5,
                        'date_added' => 'now()',
                        'customer_notified' => $customer_email_notification ? 1 : 0,
                        'comments' => $order_comment,
                        'comments_type' => '0',
                        'changed_by' => 'system'
                    );
                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data);
                    tep_update_orders_status_counter($orders_status_history_sql_data);

                    # Remove New Order Notification
                    tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . $oID . "' AND orders_type = 'CO'");

                    # Customer Notification
                    if ($customer_email_notification) {
                        tep_cron_email_notification($expired_order_row['customers_id'], $expired_order_row, $pending_status, $cancel_status, $notify_comments);
                    }

                    # Admin notification
                    tep_status_update_notification('C', $oID, 'system', 1, 5, 'A', $order_comment);
                }
            }
        }

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action=0
                                    WHERE cron_process_track_filename = 'cron_customer_orders.php'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                    WHERE cron_process_track_filename = 'cron_customer_orders.php'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Auto cancel pending orders cronjob failed at ' . $cron_process_datetime;
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}

function tep_cron_email_notification($cust_id, $order_info, $from_status, $to_status, $order_comment) {
    $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($cust_id) . "'";
    $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
    if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
        $email_firstname = $customer_profile_row["customers_firstname"];
        $email_lastname = $customer_profile_row["customers_lastname"];
    } else {
        $email_firstname = $order_info['customers_name'];
        $email_lastname = $order_info['customers_name'];
    }

    $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);

    $email = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . $order_info['orders_id'] . "\n"
            . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($order_info['date_purchased']) . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_info['orders_id'], 'SSL') . "\n\n";

    $email .= sprintf(EMAIL_TEXT_UPDATED_STATUS, $from_status . ' -> ' . $to_status) .
            $order_comment . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
    @tep_mail($order_info['customers_name'], $order_info['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, $order_info['orders_id']))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

?>