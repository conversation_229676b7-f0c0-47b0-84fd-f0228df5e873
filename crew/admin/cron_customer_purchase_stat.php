<?
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_CLASSES . 'mail/htmlMimeMail5/htmlMimeMail.php');

tep_set_time_limit(300);

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// DEBUG SETTING - PLEASE REMOVE
//$_SERVER['argv'] = array(1, 2);

// Only check for price if the is records number been passed. Corresponding row number in price_check_slots table
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
	exit;
} else {
	$mode = $_SERVER['argv'][1];
}

//tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
	if (!defined($configuration['cfgKey'])) {
		define($configuration['cfgKey'], $configuration['cfgValue']);
	}
}

switch ($mode) {
	case '1':
		/**************************** Customer Purchse Stat **********************************/
		for ($day_cnt=0; $day_cnt < 1; $day_cnt++) {
			$data_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d")-($day_cnt+1), date("Y")));
			
			$date_from = date("Y-m-d H:i:s", mktime (0,0,0,date("m"),date("d")-1,date("Y")));
			$date_to = date("Y-m-d H:i:s", mktime (0,0,0,date("m"),date("d"),date("Y"))); 
			
			$date_range_sql = "(o.date_purchased >= '" . $date_from . "' AND o.date_purchased < '" . $date_to . "')";

			$email_subject = 'Customer Purchase Statistic ' . $data_date;
			
			$export_csv_array = array();
			
			$export_csv_data = 'Customer ID, Customer Email, Order ID, Order Date, Order Amount (SubTotal), Order Status, Country, Discount Group Status, Gender, Age, Purchase Frequency (Last 30 Days)';
			$export_csv_data .= "\n";
			
			$total_customer_purchased_select_sql = "SELECT c.customers_id, c.customers_email_address, o.orders_id, o.customers_country, cg.customers_groups_name, c.customers_gender, ot.value, c.customers_dob, o.date_purchased, os.orders_status_name 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_CUSTOMERS . " AS c 
														ON o.customers_id = c.customers_id 
													INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
														ON cg.customers_groups_id = c.customers_groups_id 
													INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
														ON o.orders_id	= ot.orders_id AND ot.class = 'ot_subtotal' 
													INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
														ON o.orders_status = os.orders_status_id AND os.language_id = '1' 
													WHERE $date_range_sql";
			$total_customer_purchased_result_sql = tep_db_query($total_customer_purchased_select_sql, 'read_db_link');
			while ($total_customer_purchased_row = tep_db_fetch_array($total_customer_purchased_result_sql)) {
				$purchase_frequency_select_sql = "	SELECT COUNT(orders_id) AS total_order
													FROM " . TABLE_ORDERS . " 
													WHERE customers_id = '".$total_customer_purchased_row['customers_id']."' 
													AND date_purchased BETWEEN DATE_SUB(CURDATE(), INTERVAL " . ($day_cnt+30) . " DAY) AND DATE_SUB(CURDATE(), INTERVAL " . ($day_cnt+1) . " DAY)";
				$purchase_frequency_result_sql = tep_db_query($purchase_frequency_select_sql, 'read_db_link');
				$purchase_frequency_row = tep_db_fetch_array($purchase_frequency_result_sql);
				
				$filter_csv_data = array();
				$customer_purchased_arr = array();
			
				$customer_purchased_arr[] 	= $total_customer_purchased_row['customers_id'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['customers_email_address'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['orders_id'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['date_purchased'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['value'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['orders_status_name'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['customers_country'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['customers_groups_name'];
				$customer_purchased_arr[] 	= $total_customer_purchased_row['customers_gender'];
				
				if (tep_not_null($total_customer_purchased_row["customers_dob"])) {
					$age = tep_calculate_age($total_customer_purchased_row["customers_dob"], '', 0);
				} else {
					$age = '';
				}
				$customer_purchased_arr[] 	= $age;
				$customer_purchased_arr[] 	= $purchase_frequency_row['total_order'];
				
				foreach ($customer_purchased_arr as $csv_data) {
					$filter_csv_data[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $csv_data) . '"';
				}
				$export_csv_data .= implode(',', $filter_csv_data) . "\n";
			}
		
			if (tep_not_null($export_csv_data)) {
				$filename = date('YmdHis').'.csv';
				$file_location = 'download/30days/customer_purchase_stat/'.$filename;
				
				if (!$handle = fopen($file_location, 'w')) {
				     exit;
				}
				
				// Write to our opened file.
				if (fwrite($handle, $export_csv_data) === FALSE) {
					fclose($handle);
				    exit;
				}
				fclose($handle);
			}
		}
	break;
	
	case '2';
	
		$filename = date('YmdHis').'.csv';
		$file_location = 'download/30days/crt_kpi_report/'.$filename;
		
		if (!$handle = fopen($file_location, 'a+')) {
		     exit;
		}
			
		/**************************** KPI Report For CRT **********************************/
		$kpi_date_from = date("Y-m-d H:i:s", mktime (0,0,0,date("m"),date("d")-1,date("Y")));
		$kpi_date_to = date("Y-m-d H:i:s", mktime (0,0,0,date("m"),date("d"),date("Y")));
		
		$kpi_date_range_sql = "(osh.date_added >= '" . $kpi_date_from . "' AND osh.date_added < '" . $kpi_date_to . "')";
		
		$kpi_export_csv_data = "KPI Report for ".date('Y-m-d')."\n";
		$kpi_export_csv_data .= 'Orders ID, Date Time, User, Payment Gateway, //Processing';
		$kpi_export_csv_data .= "\n";
		
		$status_processing_cnt = true;
		$status_canceled_cnt = true;
		$orders_status_history_select_sql = "	SELECT o.orders_id, osh.orders_status_id, osh.date_added, osh.changed_by, o.payment_method 
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh 
												INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
													ON osh.orders_status_id = os.orders_status_id 
												INNER JOIN " . TABLE_ORDERS . " AS o 
													ON osh.orders_id = o.orders_id 
												WHERE $kpi_date_range_sql 
													AND os.language_id = '1' 
													AND osh.orders_status_id = '2' 
													ORDER BY osh.orders_status_id"; // 2 = Processing, 5 = Canceled
		$orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql, 'read_db_link');
		
		while ($orders_status_history_row = tep_db_fetch_array($orders_status_history_result_sql)) {
			$filter_csv_data = array();
			$orders_status_history_arr = array();
			
			$orders_status_stat_pre = '';
			$orders_status_stat = '';
			$orders_status_stat_datetime = '';
			$orders_status_stat_select_sql = "	SELECT latest_date, orders_status_id 
												FROM " . TABLE_ORDERS_STATUS_STAT . "
												WHERE orders_id = '".$orders_status_history_row['orders_id']."'
													AND orders_status_id IN ('2','3','7') 
													AND latest_date <= '".$orders_status_history_row['date_added']."'
												ORDER BY latest_date";
			$orders_status_stat_result_sql = tep_db_query($orders_status_stat_select_sql);
			while ($orders_status_stat_row = tep_db_fetch_array($orders_status_stat_result_sql)) {
				$orders_status_stat_pre = $orders_status_stat;
				$orders_status_stat = $orders_status_stat_row['orders_status_id'];
				$orders_status_stat_datetime = $orders_status_stat_row['latest_date'];
			}
			
			if ($orders_status_stat == '2' && $orders_status_stat_pre == '7') {
				if (tep_not_null($orders_status_history_row['changed_by'])) {
					$orders_status_history_arr[] = $orders_status_history_row['orders_id'];
					$orders_status_history_arr[] = $orders_status_stat_datetime;
					$orders_status_history_arr[] = $orders_status_history_row['changed_by'];
					$orders_status_history_arr[] = $orders_status_history_row['payment_method'];
					
					foreach ($orders_status_history_arr as $kpi_csv_data) {
						$filter_csv_data[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($kpi_csv_data)) . '"';
					}
					$kpi_export_csv_data .= implode(',', $filter_csv_data) . "\n";
				}
			}
		}
		tep_db_free_result($orders_status_history_result_sql);
		unset($orders_status_history_row);
		
		$kpi_export_csv_data .= "\n\n";
		fwrite($handle, $kpi_export_csv_data);
		
		$orders_status_history_array = array();
		$orders_status_history_flag_array = array();
		
		$pre_filter_order_array = array();
		$orders_status_history_select_sql = "	SELECT osh.orders_id 
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh 
												WHERE $kpi_date_range_sql 
													AND osh.orders_status_id IN ('1', '2', '7') 
												GROUP BY osh.orders_id
												ORDER BY osh.orders_status_history_id";
		$orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql, 'read_db_link');
		while ($orders_status_history_row = tep_db_fetch_array($orders_status_history_result_sql)) {
			$pre_filter_order_array[] = $orders_status_history_row['orders_id'];
		}

		$orders_status_history_select_sql = "	SELECT osh.orders_status_history_id, o.orders_id, osh.orders_status_id, osh.date_added, osh.changed_by, o.payment_method, osh.comments, osh.customer_notified, osh.comments_type, a.admin_id 
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh 
												INNER JOIN " . TABLE_ORDERS . " AS o 
													ON osh.orders_id = o.orders_id 
												LEFT JOIN " . TABLE_ADMIN . " AS a
													ON a.admin_email_address = osh.changed_by
												WHERE $kpi_date_range_sql 
													AND o.orders_id IN ('".implode("','", $pre_filter_order_array)."')
													AND osh.orders_status_id IN ('1', '2', '3', '7', '0')
												ORDER BY osh.orders_status_history_id"; // 2 = Processing, 3 = Completed
		unset($pre_filter_order_array);
		
		$orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql, 'read_db_link');
		while ($orders_status_history_row = tep_db_fetch_array($orders_status_history_result_sql)) {
			$orders_status_history_array[$orders_status_history_row['orders_id']][$orders_status_history_row['orders_status_history_id']] = array(	'date_added' => $orders_status_history_row['date_added'],
																																					'changed_by' => $orders_status_history_row['changed_by'],
																																					'payment_method' => $orders_status_history_row['payment_method'],
																																					'comments' => $orders_status_history_row['comments'],
																																					'comments_type' => $orders_status_history_row['comments_type'],
																																					'customer_notified' => $orders_status_history_row['customer_notified'],
																																					'admin_id' => $orders_status_history_row['admin_id']);
			if (!isset($orders_status_history_flag_array[$orders_status_history_row['orders_id']][$orders_status_history_row['orders_status_id']])) $orders_status_history_flag_array[$orders_status_history_row['orders_id']][$orders_status_history_row['orders_status_id']] = $orders_status_history_row['orders_status_history_id'];
		}
		
		$kpi_export_csv_data = "Orders ID, Date Time, User, Payment Gateway, Remark, //1st remark after order moved to Processing status by System\n";
		
		foreach ($orders_status_history_array as $orders_id_loop => $orders_id_data ) {
			/* START : 1st remark that notifies customer from user after order moved from Pending to Verifying status */
			if (isset($orders_status_history_flag_array[$orders_id_loop]['2']) && $orders_status_history_array[$orders_id_loop][$orders_status_history_flag_array[$orders_id_loop]['2']]['changed_by'] == 'system') {
				foreach ($orders_status_history_array[$orders_id_loop] as $orders_status_history_id_loop => $orders_status_history_id_data) {
					$orders_status_history_arr = array();
					if ($orders_status_history_id_loop > $orders_status_history_flag_array[$orders_id_loop]['2'] && (!isset($orders_status_history_flag_array[$orders_id_loop]['3']) || $orders_status_history_id_loop < $orders_status_history_flag_array[$orders_id_loop]['3'])) {
						if ($orders_status_history_id_data['comments_type'] == '1' && tep_not_null($orders_status_history_id_data['comments']) && $orders_status_history_id_data['customer_notified'] == '1' && tep_not_null($orders_status_history_id_data['admin_id']) && (int)$orders_status_history_id_data['admin_id']>0) {
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_id_loop)) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['date_added'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['changed_by'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['payment_method'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['comments'])) . '"';
							
							$kpi_export_csv_data .= implode(',', $orders_status_history_arr) . "\n";
							
							break;
						}
						
					}
				}
			}
			/* END : 1st remark that notifies customer from user after order moved from Pending to Verifying status */
		}
		
		$kpi_export_csv_data .= "\n\n";
		fwrite($handle, $kpi_export_csv_data);
		
		$kpi_export_csv_data = "Orders ID, Date Time, User, Payment Gateway, Remark, //1st remark that notifies customer from user after order moved from Pending to Verifying status\n";
		
		foreach ($orders_status_history_array as $orders_id_loop => $orders_id_data ) {
			/* START : 1st remark that notifies customer from user after order moved from Pending to Verifying status */
			if (isset($orders_status_history_flag_array[$orders_id_loop]['7'])) {
				foreach ($orders_status_history_array[$orders_id_loop] as $orders_status_history_id_loop => $orders_status_history_id_data) {
					$orders_status_history_arr = array();
					if ($orders_status_history_id_loop > $orders_status_history_flag_array[$orders_id_loop]['7'] && (!isset($orders_status_history_flag_array[$orders_id_loop]['2']) || $orders_status_history_id_loop < $orders_status_history_flag_array[$orders_id_loop]['2'])) {
						if ($orders_status_history_id_data['comments_type'] == '1' && tep_not_null($orders_status_history_id_data['comments']) && $orders_status_history_id_data['customer_notified'] == '1' && tep_not_null($orders_status_history_id_data['admin_id']) && (int)$orders_status_history_id_data['admin_id']>0) {
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_id_loop)) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['date_added'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['changed_by'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['payment_method'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['comments'])) . '"';
							
							$kpi_export_csv_data .= implode(',', $orders_status_history_arr) . "\n";
							break;
						}
						
					}
				}
			}
			/* END : 1st remark that notifies customer from user after order moved from Pending to Verifying status */
		}
		
		$kpi_export_csv_data .= "\n\n";
		fwrite($handle, $kpi_export_csv_data);
		
		$kpi_export_csv_data = "Orders ID, Date Time, User, Payment Gateway, Remark, //1st remark from user for Pending order\n";
		
		foreach ($orders_status_history_array as $orders_id_loop => $orders_id_data ) {
			/* START : 1st remark from user for Pending order */
			if (isset($orders_status_history_flag_array[$orders_id_loop]['1'])) {
				foreach ($orders_status_history_array[$orders_id_loop] as $orders_status_history_id_loop => $orders_status_history_id_data) {
					$orders_status_history_arr = array();
					if ($orders_status_history_id_loop > $orders_status_history_flag_array[$orders_id_loop]['1'] && (!isset($orders_status_history_flag_array[$orders_id_loop]['7']) || $orders_status_history_id_loop < $orders_status_history_flag_array[$orders_id_loop]['7'])) {
						if ($orders_status_history_id_data['comments_type'] == '1' && tep_not_null($orders_status_history_id_data['comments']) && $orders_status_history_id_data['customer_notified'] == '1' && tep_not_null($orders_status_history_id_data['admin_id']) && (int)$orders_status_history_id_data['admin_id']>0) {
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_id_loop)) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['date_added'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['changed_by'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['payment_method'])) . '"';
							$orders_status_history_arr[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($orders_status_history_id_data['comments'])) . '"';
							
							$kpi_export_csv_data .= implode(',', $orders_status_history_arr) . "\n";
							break;
						}
						
					}
				}
			}
			/* END : 1st remark from user for Pending order */
		}
		fwrite($handle, $kpi_export_csv_data);
		
		unset($kpi_export_csv_data);
		unset($orders_status_history_array);
		unset($orders_status_history_flag_array);
		
		fclose($handle);
	break;
}

?>