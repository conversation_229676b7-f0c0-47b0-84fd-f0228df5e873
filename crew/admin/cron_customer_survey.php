<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

$language = 'english';  // used in store_credit.php class
$default_languages_id = 1;
$languages_id = $default_languages_id;

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// email classes
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

$_SESSION['default_languages_id'] = 1;

$aws_obj = new ogm_amazon_ws();

$cron_filename = 'cron_customer_survey.php';
$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

$email_content = <<<EOL
Hi there %1\$s!

We hope your first purchase at OffGamers was a pleasant and smooth experience.

As a valued new member of the OffGamers community, we’d love to hear your feedback in order to make your next purchasing experience even better!

Just click on the link below to take our short 5-minute survey.

<a href="%2\$s">%2\$s</a>

Thanks!

Best Regards,
Alice Wong
Customer Experience Manager
EOL;

$from_dt = CUSTOMER_SURVEY_LAST_DT;
$to_dt = date('Y-m-d', strtotime("-1 days"));

$cron_process_checking_row = query_cron_process($cron_filename);
if (count($cron_process_checking_row)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        lock_cron_process($cron_filename);

        # capture new data
        capture_new_customer_info($from_dt, $to_dt);

        # process sending email
        process_send_email();

        # clean data
        delete_old_customer_info();

        unlock_cron_process($cron_filename);
    } else {
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['cron_process_track_failed_attempt'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Customer Survey cronjob failed at ' . $cron_process_datetime;
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                unlock_cron_process($cron_filename);
            }
        }
    }
}

# =========================================================== FUNC ===========================================================

function process_send_email() {
    global $email_content;

    $select_sql = "     SELECT customers_id, customer_notified
                        FROM customers_survey
                        WHERE customer_notified <= 0
                            AND account_created_datetime < DATE_SUB(NOW(), INTERVAL 24 HOUR)
                        ORDER BY customer_notified DESC
                        LIMIT 500
                        ";
    $result_sql = tep_db_query($select_sql);
    while ($row_c = tep_db_fetch_array($result_sql)) {
        $notified = false;

        $customers_id = $row_c['customers_id'];
        $notified_cnt = $row_c['customer_notified'];

        # only 1st purchase from OG store
        # only success delivered and completed order
        $order_select_sql = "SELECT oei.orders_extra_info_value
                            FROM orders AS o 
                            INNER JOIN orders_products AS op
                                ON o.orders_id = op.orders_id AND op.products_bundle_id = 0
                            INNER JOIN orders_extra_info AS oei
                                    ON o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id'
                            WHERE o.customers_id = '" . $customers_id . "'
                                AND o.orders_status = 3
                                AND o.orders_cb_status IS NULL
                                AND op.products_good_delivered_price > 0
                            ORDER BY o.orders_id ASC
                            LIMIT 1
                            ";
        $order_result_sql = tep_db_query($order_select_sql);
        if ($o_row = tep_db_fetch_array($order_result_sql)) {
            if ($o_row['orders_extra_info_value'] == 0) {
                $customers_select_sql = "SELECT customers_firstname, customers_lastname, customers_gender, customers_email_address 
                                         FROM " . TABLE_CUSTOMERS . " 
                                         WHERE customers_id = " . $customers_id;
                $customers_result_sql = tep_db_query($customers_select_sql);
                if ($customers_row = tep_db_fetch_array($customers_result_sql)) {
                    $lastname = empty($customers_row['customers_lastname']) ? $customers_row['customers_firstname'] : $customers_row['customers_lastname'];
                    $firstname = empty($customers_row['customers_firstname']) ? $lastname : $customers_row['customers_firstname'];
                    $email = $customers_row['customers_email_address'];

                    $store_owner = STORE_OWNER;
                    $store_email = STORE_OWNER_EMAIL_ADDRESS;

                    if ($customers_id % 2 == 0) {
                        # option B
                        $survey_url = 'http://ogms.co/newsurvey';
                        $email_subject = 'Survey for Our New OffGamers Customer';
                    } else {
                        # option A
                        $survey_url = 'http://ogms.co/usersurvey';
                        $email_subject = 'Invitation to take a survey at OffGamers';
                    }

                    $result_str = sprintf($email_content, $firstname, $survey_url) . "\n\n";

                    if (custom_mail($lastname, $email, $email_subject, $result_str, $store_owner, $store_email)) {
                        $notified = true;

                        $update_array = array(
                            'survey_sent_datetime' => 'now()',
                            'customer_notified' => 1
                        );

                        tep_db_perform('customers_survey', $update_array, 'update', "customers_id = " . $customers_id);
                    }
                }
            } else {
                $notified = true;

                # G2G order does not required to participate in this survey.
                $update_array = array(
                    'customer_notified' => 2
                );

                tep_db_perform('customers_survey', $update_array, 'update', "customers_id = " . $customers_id);
            }
        }

        if (!$notified) {
            $update_array = array(
                'customer_notified' => $notified_cnt - 1
            );

            tep_db_perform('customers_survey', $update_array, 'update', "customers_id = " . $customers_id);
        }
    }
}

function custom_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address, $email_charset = '') {
    global $aws_obj;

    $sent_status = false;

    $letter = array();
    $letter['message']['subject'] = $email_subject;
    $letter['message']['body'] = $email_text;
    $letter['envelope']['to'] = array('name' => $to_name, 'address' => $to_email_address);
    $letter['envelope']['from'] = array('name' => $from_email_name, 'address' => $from_email_address);

    $aws_obj = new ogm_amazon_ws();

    if ($aws_obj->send_mail_by_ses_controller($letter)) {
        $sent_status = $aws_obj->send_mail_by_ses($letter);
    }

    unset($aws_obj, $letter);

    return $sent_status;
}

function delete_old_customer_info() {
    $delete_sql = "DELETE FROM customers_survey WHERE customer_notified <= 0 AND account_created_datetime < DATE_SUB(NOW(), INTERVAL 1 YEAR)";
    tep_db_query($delete_sql);
}

function capture_new_customer_info($date_from, $date_to) {
    if ($date_from != $date_to) {
        $select_sql = "     INSERT INTO customers_survey (customers_id, account_created_datetime) 
                                SELECT ci.customers_info_id, ci.customers_info_date_account_created 
                                FROM customers_info AS ci
                                LEFT JOIN customers_survey AS cs on ci.customers_info_id = cs.customers_id
                                WHERE cs.customers_id IS null 
                                    AND ci.customers_info_date_account_created >= '" . $date_from . " 00:00:00' 
                                    AND ci.customers_info_date_account_created <= '" . $date_to . " 23:59:59'";
        if (tep_db_query($select_sql)) {
            $conf_update_sql = "UPDATE " . TABLE_CONFIGURATION . " 
                                SET configuration_value = '" . $date_to . "', 
                                    last_modified = now() 
                                WHERE configuration_key = 'CUSTOMER_SURVEY_LAST_DT'
                                    AND configuration_value <> '" . $date_to . "'";
            tep_db_query($conf_update_sql);
        }
    }
}

function query_cron_process($filename) {
    $cron_process_checking_select_sql = "SELECT  cron_process_track_in_action, 
                                            cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, 
                                            cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = '" . $filename . "'";
    $cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
    return tep_db_fetch_array($cron_process_checking_result_sql);
}

function lock_cron_process($filename) {
    $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                SET cron_process_track_in_action = 1, 
                                    cron_process_track_start_date = now(), 
                                    cron_process_track_failed_attempt = 0 
                                WHERE cron_process_track_filename = '" . $filename . "'";
    tep_db_query($cron_process_update_sql);
}

function unlock_cron_process($filename) {
    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                SET cron_process_track_in_action = 0 
                                WHERE cron_process_track_filename = '" . $filename . "'";
    tep_db_query($unlock_cron_process_sql);
}

?>