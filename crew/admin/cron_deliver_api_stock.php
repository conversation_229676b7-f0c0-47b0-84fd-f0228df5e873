<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
require_once(DIR_WS_CLASSES . 'custom_product_code.php');
include_once(DIR_WS_CLASSES . 'curl.php');

$db_link = tep_db_connect(DB_SERVER, DB_SERVER_USERNAME, DB_SERVER_PASSWORD, DB_DATABASE, 'db_link') or die('Unable to connect to database server!');
$db_og_link = tep_db_connect(DB_OG_SERVER, DB_OG_SERVER_USERNAME, DB_OG_SERVER_PASSWORD, DB_OG_DATABASE, 'db_og_link') or die('Unable to connect to database server!');

$process_limit = 10;
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
    exit;
} else {
    $process_limit = $_SERVER['argv'][1];
}

$reseller_id = 1;
$seller_setting_query = tep_db_query("SELECT * FROM reseller_setting WHERE reseller_id = " . $reseller_id, 'db_og_link');
$seller_setting_array = [];
//BASE_URL, API_KEY, API_SECRET, WEBHOOK_TOKEN, USER_ID
while ($seller_setting = tep_db_fetch_array($seller_setting_query)) {
    $seller_setting_array[$seller_setting["key"]] = $seller_setting["value"];
}
$base_url = $seller_setting_array["BASE_URL"];
$api_version = "v2";
$cpc_obj = new custom_product_code();

$g2g_og_order_mapper_select_sql = "	SELECT mapper.id, mapper.g2g_order_id, mapper.delivery_id, op.orders_products_id 
                            FROM g2g_og_order_mapper AS mapper
                            INNER JOIN orders AS o ON mapper.og_order_id = o.orders_id
                            INNER JOIN orders_products AS op ON o.orders_id = op.orders_id
                            WHERE mapper.status = 0 AND mapper.error < 1 AND o.orders_status = 3
                            LIMIT $process_limit";
$g2g_og_order_mapper_result_sql = tep_db_query($g2g_og_order_mapper_select_sql, "db_link");
$ajax_data = [];
while ($g2g_og_order_mapper_row = tep_db_fetch_array($g2g_og_order_mapper_result_sql)) {
    //update g2g_og_order_mapper status to processing status
    $timestamp = time();
    $cron_process_update_sql = "UPDATE g2g_og_order_mapper
                                SET `status` = 1,
                                    updated_at = " . $timestamp . "
                                WHERE id = '" . $g2g_og_order_mapper_row["id"] . "'";
    tep_db_query($cron_process_update_sql);

    $delivery_id = $g2g_og_order_mapper_row["delivery_id"];
    $path = 'orders/' . $g2g_og_order_mapper_row["g2g_order_id"] . '/delivery';
    $cdkey_image_select_sql = "SELECT custom_products_code_id, file_type, code_uploaded_by, products_id, to_s3, code_date_added
                                FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                WHERE orders_products_id = '" . $g2g_og_order_mapper_row["orders_products_id"] . "'";
    $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql, "db_link");
    $ajax_data = [];
    while ($cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql)) {
        if (strtolower($cdkey_image_row['file_type']) == "soft") {
            $cdk_id = $cdkey_image_row["custom_products_code_id"];
            $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);

            if ($theData !== FALSE) {
                $ajax_data[] = [
                    'content' => 'OffGamers Reference : ' . $cdk_id . ',' . str_replace('<br>', ',', tep_decrypt_data($theData)),
                    'content_type' => 'text/plain',
                    'reference_id' => $cdk_id
                ];
            }
        }
    }

    if ($ajax_data) {
        
        $data = [
            'ignore_label_format' => true,
            'delivery_id' => $delivery_id,
            'codes' => $ajax_data
        ];


        $string = '/' . $api_version . '/' . $path . $seller_setting_array["API_KEY"] . $seller_setting_array["USER_ID"] . $timestamp;
        $signature = hash_hmac('sha256', $string, $seller_setting_array["API_SECRET"]);
        // $options = [
        //     'headers' => [
        //         'g2g-api-key' => $seller_setting_array["API_KEY"],
        //         'g2g-userid' => $seller_setting_array["USER_ID"],
        //         'g2g-signature' => $signature,
        //         'g2g-timestamp' => $timestamp,
        //         'Content-Type' => 'application/json'
        //     ],
        //     'http_errors' => false
        // ];
        $header = [
            'g2g-api-key' => $seller_setting_array["API_KEY"],
            'g2g-userid' => $seller_setting_array["USER_ID"],
            'g2g-signature' => $signature,
            'g2g-timestamp' => $timestamp,
            'Content-Type' => 'application/json'
        ];

        $curl_obj = new curl();
        $parsed_data = $curl_obj->curl_post_serverless($base_url . '/' . $path, 'POST', json_encode($data), $header);

        if (isset($parsed_data['code']) && $parsed_data['code'] === 20000001) {
            $cron_process_update_sql = "UPDATE g2g_og_order_mapper
                                        SET `status` = 3,
                                            updated_at = " . $timestamp . "
                                        WHERE id = '" . $g2g_og_order_mapper_row["id"] . "'";
            tep_db_query($cron_process_update_sql);
        } elseif (isset($parsed_data['code']) && isset($parsed_data['message'])) {
            $cron_process_update_sql = "UPDATE g2g_og_order_mapper
                                        SET `status` = 0,
                                            error = 1,
                                            updated_at = " . $timestamp . "
                                        WHERE id = '" . $g2g_og_order_mapper_row["id"] . "'";
            tep_db_query($cron_process_update_sql);
            reportError('cron_delivery_api_stock ~ Invalid Response Code from G2G API', $g2g_og_order_mapper_row["id"] . ' : ' . json_encode($parsed_data));
        } else {
            $cron_process_update_sql = "UPDATE g2g_og_order_mapper
                                        SET `status` = 0,
                                            error = 1,
                                            updated_at = " . $timestamp . "
                                        WHERE id = '" . $g2g_og_order_mapper_row["id"] . "'";
            tep_db_query($cron_process_update_sql);
            reportError('cron_delivery_api_stock ~ Invalid Response from G2G API', $g2g_og_order_mapper_row["id"] . ' : ' . json_encode($parsed_data));
        }
    } else {
        $cron_process_update_sql = "UPDATE g2g_og_order_mapper
                                    SET `status` = 9,
                                        updated_at = " . $timestamp . "
                                    WHERE id = '" . $g2g_og_order_mapper_row["id"] . "'";
        tep_db_query($cron_process_update_sql);
        reportError('cron_delivery_api_stock ~ No Code Delivery', $g2g_og_order_mapper_row["id"]);
    }
}



function reportError($response_data, $ext_subject = '') {
    include_once(DIR_WS_CLASSES . 'slack_notification.php');
    $slack = new slack_notification();
    $data = json_encode(array(
        'text' => '[OG Crew] Cron Delivery API Stock Error - ' . date("F j, Y H:i"),
        'attachments' => array(
            array(
                'color' => 'warning',
                'text' => $ext_subject . "\n\n" . json_encode($response_data)
            )
        )
    ));
    $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
}
