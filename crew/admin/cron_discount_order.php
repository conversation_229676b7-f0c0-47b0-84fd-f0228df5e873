<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the read-only database... now
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$discounted_orders = array("Payment Gateway, Payment Method, Order Date, Customer ID, Order ID, Sub-total, Discount Amount($), Coupon Code");

$report_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d") - 1, date("Y")));
$date_from = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") - 1, date("Y")));
$date_to = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d"), date("Y")));
$has_record = false;

$discount_order_select_sql = "	SELECT pm.payment_methods_title, o.customers_id, o.payment_method, o.date_purchased, o.orders_id, ot.value, otS.value AS subtotal, ot.title
                                FROM `orders` AS o
                                INNER JOIN orders_total AS ot
                                        ON (o.orders_id=ot.orders_id AND ot.class = 'ot_coupon')
                                INNER JOIN orders_total AS otS
                                        ON (o.orders_id=otS.orders_id AND otS.class = 'ot_subtotal')
                                INNER JOIN payment_methods AS pm
                                        ON o.payment_methods_parent_id = pm.payment_methods_id
                                WHERE o.date_purchased >= '" . $date_from . "'
                                        AND o.date_purchased < '" . $date_to . "'
                                        AND o.orders_status NOT IN (5)
                                ORDER BY o.payment_method; ";
$discount_order_result_sql = tep_db_query($discount_order_select_sql, 'read_db_link');

while ($discount_order_row = tep_db_fetch_array($discount_order_result_sql)) {
	$coupon_code =  strpos($discount_order_row['title'], ':') !== false ? trim(substr($discount_order_row['title'], strpos($discount_order_row['title'], ':') + 1)) : '';
    $discounted_orders[] = $discount_order_row['payment_methods_title'] . ',' . $discount_order_row['payment_method'] . ',' . $discount_order_row['date_purchased'] . ',' . $discount_order_row['customers_id'] . ',' . $discount_order_row['orders_id'] . ',' . $discount_order_row['subtotal'] . ',' . $discount_order_row['value'] . ',' .$coupon_code;
    $has_record = true;
}

if (!$has_record) {
    $discounted_orders[] = 'No discount order';
}

$mail_content = implode("\n", $discounted_orders);

$subject = "[OFFGAMERS] Orders with Discount Code for " . $report_date;

@tep_mail('<EMAIL>', '<EMAIL>', $subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
@tep_mail('<EMAIL>', '<EMAIL>', $subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

?>