<?php

/*
 * site url : https://promo.offgamers.com/black-friday/index.html 
 * To-do for Xmas campaign
 *  - exclude category id (17797)
 *  - start 16th Dec & end 6th Jan 2017
 */
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

$language = 'english';  // used in store_credit.php class
$default_languages_id = 1;
$languages_id = $default_languages_id;

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
include_once(DIR_WS_CLASSES . 'log.php');

require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

$_SESSION['default_languages_id'] = 1;

$aws_obj = new ogm_amazon_ws();
$log_object = new log_files('system');

$date = new DateTime();
$date->setTimezone(new DateTimeZone('America/Los_Angeles'));  //America/Los_Angeles Asia/Kuala_Lumpur
$curr_time = strtotime($date->format('Y-m-d H:i:s')) + (1 * 60 * 60) + 600;

$category_products_array = array(
    # Day 11 (28-11-2016 checked)
    'day_11' => array(
        '17884' => array(172294, 171201, 171202, 172295),
        '5329' => array(169338, 169339, 171452, 173063, 173064),
        '5147' => array(173416, 173417, 172836, 169386, 172837, 169387, 173418, 173421, 171375, 173422, 171594, 171595),
        '3466' => array(169340, 171453, 169341, 171454),
        '4130' => array(172592, 172447, 172448, 172449, 172450),
        '17855' => array(169455, 169437, 171198, 169438, 171451),
        '4444' => array(168269, 169358, 171222, 169359, 171223),
        '5236' => array(171346, 169356, 171347, 169357),
        '17710' => array(171769, 169352, 172014, 172015, 172016, 172017),
        '20056' => array(173018, 173019, 173020, 173021, 173022, 173023),
        '4458' => array(173010, 173011, 173012, 173013, 173014, 173015, 173016),
    ),
    # Day 10 (27-11-2016 checked)
    'day_10' => array(
        '22581' => array(171601, 171602, 171603, 171604, 172041, 172042),
        '17634' => array(171017, 169362, 171764, 169403, 171765, 171766),
        '20318' => array(171399, 169361, 172921, 169404, 171400, 172920),
        '23637' => array(173206, 173207),
        '21160' => array(172922, 172923, 172924, 172925, 172926, 172927),
        '23360' => array(173345, 173346, 172456, 172457, 172458, 172459),
        '21166' => array(169811, 169812, 169813, 169814, 172281, 169815),
        '17949' => array(172928, 171757, 171758, 171759, 172929, 171760, 171761, 172930, 171762, 171763, 172931, 172280, 173213),
        '23357' => array(172470, 172471, 172472, 172473, 172474),
        '21136' => array(172475, 172476, 172477, 172934, 172478, 172479, 172480),
        '21120' => array(172465, 172466, 172467, 172468, 172469),
        '21114' => array(172460, 172461, 172462, 172463, 172464, 172935),
        '23575' => array(172938, 172936, 172937, 172939, 172940),
        '21172' => array(172288, 172289, 172941, 172290, 172291, 172292),
        '21175' => array(172481, 172482, 172483, 172484, 172485),
        '21123' => array(171932, 171933, 171934, 171935, 171936, 171937, 171938, 171939),
        '21169' => array(172282, 172283, 172284, 172285, 172286, 172287),
        '12750' => array(172988, 172989, 172990, 172991, 172992),
        '9578' => array(173037, 173038, 173039, 173040, 173041, 173042),
    ),
    # Day 9 (26-11-2016 checked)
    'day_9' => array(
        '21492' => array(169460, 173437),
        '17712' => array(169360, 172012, 172013),
        '21596' => array(171770, 171771, 172018),
        '8601' => array(172048, 169430, 172049, 172050),
        '18181' => array(169458, 169336, 169337, 171767),
        '20343' => array(171586, 171203, 171204, 173438),
        '2522' => array(169280, 173289),
        '2299' => array(169283, 173290),
        '19958' => array(167590),
        '23296' => array(173003, 173004, 173005, 173006, 173007, 173008, 173009),
        '4097' => array(172982, 172983, 172984, 172985, 172986, 172987),
    ),
    # Day 8 (25-11-2016 checked)
    'day_8' => array(
        '22668' => array(172392, 172393, 172394),
        '18267' => array(171376, 169388, 172032, 169389, 173439, 173440),
        '21117' => array(172033, 172034, 172035, 172036, 173435, 173436),
        '20573' => array(172307),
        '21180' => array(172309),
        '22553' => array(172308),
        '23455' => array(172898),
        '16059' => array(169444, 171269, 171455, 171456),
        '22741' => array(171807, 171808, 171810, 171809),
        '7779' => array(172993, 172994, 172995, 172996),
        '4100' => array(173032, 173033, 173034, 173035, 173036),
    ),
    # Day 7 (24-11-2016 checked)
    'day_7' => array(
        '20891' => array(173065, 173066),
        '20362' => array(173072, 173073, 173074, 173075),
        '20365' => array(173069, 173070, 173071),
        '20784' => array(173067, 173068),
        '19148' => array(169433),
        '19149' => array(167710),
        '23256' => array(173455, 173456),
        '22126' => array(173454, 171387),
        '21000' => array(172030, 172031),
        '16565' => array(171255, 171256, 171257, 171258, 171259, 171260),
        '16328' => array(166769, 172997, 172998, 172999, 173000),
    ),
    # Day 6 (23-11-2016 checked)
    'day_6' => array(
        '21278' => array(172349, 172350, 173059, 173060, 173061, 173062),
        '4360' => array(172302, 172303, 172304),
        '23117' => array(172332, 172333, 173225, 173226, 173227, 173228),
        '23120' => array(173229, 173230, 173231, 173232, 173233),
        '23114' => array(172334, 172335, 173234, 173235),
        '21485' => array(171799, 171800, 171801, 171803),
        '21704' => array(172153, 171386),
        '22753' => array(172343, 171772, 171773, 171774, 171775),
        '20771' => array(172344, 171776, 173469, 173470, 173471),
        '22661' => array(171606, 171605),
//        '17797' => array(172155,172156,172157,172158,172160,172161,172162,172163,172164,172165),
        '6306' => array(169456, 172977, 172978, 172979, 172980, 172981),
    ),
    # Day 5 (22-11-2016 checked)
    'day_5' => array(
        '21889' => array(172311, 172312, 171389, 171796, 173299, 173300),
        '4832' => array(169519, 171792, 172396, 172395),
        '4835' => array(171352, 169520, 173077),
        '4841' => array(169522, 166765, 173078),
        '4858' => array(172620, 172621, 173301, 173302),
        '23063' => array(172229, 172313),
        '23067' => array(172228, 172314),
        '23363' => array(172618, 172619),
        '4829' => array(169518, 173324),
        '8599' => array(173325, 171650, 171651, 173326),
        '17508' => array(170375, 171042, 171043, 171044),
        '21608' => array(171747, 171748, 171749, 171750),
    ),
    # Day 4 (21-11-2016 checked)
    'day_4' => array(
        '3456' => array(169384, 171395, 169385, 171596),
        '19752' => array(169382, 171394, 171597, 171598),
        '12371' => array(173480),
        '21673' => array(172831, 172832, 172833),
        '22569' => array(171587, 171588, 172019),
        '20359' => array(169436, 171196, 171197),
        '20052' => array(171790, 169435, 172020, 172021),
        '22768' => array(172022, 172023, 171791, 171793),
        '20855' => array(169434, 172024, 172025),
        '21599' => array(171794, 171795),
        '22671' => array(172278, 172279, 172026),
        '22961' => array(172027, 172028, 172029),
        '22506' => array(171087, 171088, 171089),
        '23378' => array(173389, 173392, 173390, 173391),
        '4932' => array(173393, 173394, 173395, 173396, 173397, 173398),
        '23551' => array(173304, 173305, 173306, 173347, 173216, 173217, 173218, 173219, 173220, 173221, 173222, 173223),
    ),
    # Day 3 (20-11-2016 checked)
    'day_3' => array(
        '20994' => array(169380, 169381),
        '22860' => array(173419, 173420),
        '20997' => array(172300, 172301),
        '18268' => array(171228, 169378, 169379, 173441),
        '22870' => array(173442, 173443, 173444, 173445),
        '21676' => array(173446, 173447, 173448, 173449),
        '21188' => array(173450, 173451, 173452, 173453),
        '22573' => array(173457, 173458, 173459, 173460),
        '5380' => array(173461, 173462, 173463, 173464),
        '23531' => array(173465, 173466, 173467, 173468),
        '22865' => array(173472, 173473, 173474, 173475),
        '23339' => array(173476, 173477, 173478, 173479),
        '6031' => array(172092, 172093, 172095, 172096, 172097, 172098, 172099, 172100),
        '6248' => array(172838, 172839, 172840),
        '15139' => array(169408, 169409, 173379, 173380, 173381, 173382),
        '20869' => array(173383, 173384, 173385, 173386, 173387, 173388),
    ),
    # Day 2 (19-11-2016 checked)
    'day_2' => array(
        '17512' => array(173399, 173400),
        '23468' => array(173401, 173402),
        '23600' => array(173083),
        '23606' => array(173085),
        '23603' => array(173084),
        '21098' => array(173403, 173404),
        '23642' => array(173405, 173406),
        '22964' => array(173410, 173411, 173412, 173413, 173414),
        '13136' => array(169377, 173415),
        '19196' => array(169290, 169291),
        '16853' => array(173370, 173371, 173372),
        '7276' => array(136942, 136943, 136944, 136945, 136946),
        '16321' => array(173373, 173374, 173375, 173376, 173377, 173378),
        '21393' => array(172915, 172916, 172917, 173001, 172918, 172919, 173002),
        '8502' => array(171205, 171206, 171207),
        '4229' => array(171125),
        '21666' => array(169410),
        '22635' => array(171010),
        '22634' => array(171002),
        '22633' => array(170994),
        '23034' => array(172077),
        '22632' => array(170986),
        '22630' => array(170970),
        '23023' => array(171708),
        '22631' => array(170978),
        '23010' => array(171684),
        '23016' => array(171696),
        '23038' => array(171729),
        '22995' => array(171660),
        '23500' => array(172791),
        '23331' => array(172398),
        '23504' => array(172799),
        '17587' => array(156918),
        '14654' => array(137571),
        '20775' => array(167560),
        '20883' => array(167781),
        '20814' => array(167811),
    ),
    # Day 1 (18-11-2016)
    'day_1' => array(
        '20087' => array(169366),
        '20842' => array(173158),
        '23207' => array(173113),
        '23210' => array(172091),
        '22797' => array(173114),
        '2299' => array(173115),
        '22623' => array(172337, 172764),
        '22714' => array(172340),
        '22722' => array(172341),
        '22718' => array(172342),
        '23147' => array(172315, 173127, 173128),
        '23143' => array(172316, 173129),
        '23139' => array(172317, 173130),
        '23135' => array(172318, 173131),
        '23127' => array(172319, 173133),
        '23155' => array(172320, 173139),
        '23131' => array(172321, 173140, 173144),
        '23398' => array(173150),
        '22340' => array(172322, 171211),
        '23459' => array(173153, 173154),
        '23463' => array(173155, 173156),
        '20528' => array(169287),
        '21331' => array(173159),
        '18295' => array(172296, 171768, 172297, 172298, 172299),
        '23483' => array(173160, 173161, 173162, 173163),
        '22350' => array(172329, 173171),
        '22734' => array(171779, 171780, 171781, 171782),
        '22738' => array(171783, 171784, 171785, 171786),
        '22745' => array(171787, 171788, 171789),
        '23083' => array(173176),
        '23088' => array(173177, 173178),
        '23104' => array(173184),
        '23108' => array(173185),
        '21505' => array(172346, 172347),
        '22749' => array(172348, 171811, 171812),
        '20858' => array(169431, 169432),
        '22835' => array(172046, 172047),
        '23123' => array(172051, 172052),
        '20872' => array(171460, 171461, 171462, 171463),
        '21106' => array(172043),
        '22110' => array(172044, 172045),
        '23222' => array(173192, 173193),
        '23299' => array(173188, 173189, 173190, 173191),
        '22038' => array(171270, 171271, 171272, 171273),
        '17880' => array(172336, 172767, 172768),
        '21704' => array(169440), // pID-169440.jpg
        '22344' => array(173175, 173174, 171212),
        '23434' => array(173172, 173173),
        '20933' => array(173157),
        '23271' => array(172786, 172787),
        '22681' => array(173053, 173054, 173055, 173056, 173057, 173058),
        '22660' => array(172330, 169443, 172331),
        '20865' => array(173147, 173148),
        '23342' => array(172788, 172789, 172790),
        '23440' => array(173205),
        '23548' => array(173141, 173142, 173143),
        '22587' => array(173136, 173137, 173138),
        '22847' => array(173132, 173134, 173135),
        '8046' => array(172830),
        '18269' => array(172834, 172835),
        '23525' => array(173125, 173126),
        '20633' => array(169354, 169355),
        '1667' => array(172305, 172306),
        '15699' => array(169153),
        '23348' => array(173116, 173117, 173118, 173119, 173120, 173121),
        '23189' => array(172323, 172539),
        '23188' => array(173168, 173169, 172324, 173170),
        '23092' => array(173179, 173180),
        '23100' => array(173182, 173183),
        '16996' => array(155471, 155472, 155473),
        '17945' => array(173368),
        '23253' => array(172209, 172210, 172211),
        '23428' => array(173369),
        '22758' => array(171177),
        '21838' => array(169684),
        '22756' => array(171163),
        '22757' => array(171170),
        '22759' => array(171178),
        '21222' => array(171580),
        '19492' => array(164269),
        '19193' => array(161857),
        '20329' => array(166742),
        '23588' => array(172970),
        '20848' => array(167718),
        '17261' => array(159290),
        '23366' => array(172604),
        '19874' => array(164431),
        '22901' => array(171490),
        '21570' => array(171333),
        '20779' => array(167572),
        '17911' => array(159567),
        '23276' => array(172195),
        '23292' => array(172212),
        '23583' => array(172961),
        '4507' => array(88375),
        '4100' => array(137482),
    ),
);

$exclude_id_array = array(
    171177, 169684, 171163, 171170, 171178, 171580, 164269, 161857, 166742, 172970, 167718, 159290, 172604, 164431, 171490, 171333, 167572, 159567, 172195, 172212, 172961, 88375, 137482,
    171125, 169410, 171010, 171002, 170994, 172077, 170986, 170970, 171708, 170978, 171684, 171696, 171729, 171660, 172791, 172398, 172799, 156918, 137571, 167560, 167781, 167811, 169153, 172091, 173083, 173085, 173084, 155471, 155472, 155473
);

$event_controller = array(
    'start' => array(
        '2016-12-16 00:00:00' => array(
            'day_1', 'day_2', 'day_3', 'day_4', 'day_5', 'day_6', 'day_7', 'day_8', 'day_9', 'day_10', 'day_11'
        ),
    ),
    'end' => array(
        '2017-01-06 00:00:00' => array(
            'day_1', 'day_2', 'day_3', 'day_4', 'day_5', 'day_6', 'day_7', 'day_8', 'day_9', 'day_10', 'day_11'
        ),
    )
);

if ($curr_time < strtotime('2017-01-07 00:00:00')) {
    foreach ($event_controller as $event => $event_data) {
        $product_status = $event === 'start' ? '1' : '0';
        $new = 0;

        if ($event === 'start') {
            # activate products + upload new list
            $json_array = array();
            $upload_datetime = '';

            foreach ($event_data as $datetime => $key_array) {
                if (strtotime($datetime) < $curr_time && $curr_time < strtotime('2016-12-17 00:00:00')) {
                    $upload_datetime = $datetime;

                    foreach ($key_array as $key) {
                        event_process($category_products_array[$key], $product_status);
                        $json_array = BlackFriday($category_products_array, $key, '-', 'Buy', $json_array);
                    }
                }
            }

            if ($json_array) {
                list($date, ) = explode(' ', $upload_datetime);
                uploadJson('request-' . $date . '.js', "angular.callbacks._0(" . json_encode(array(
                            'config' => array(
                                'active' => 'active-yes'
                            ),
                            'data' => $json_array
                        )) . ")");
            }
        } else {
            # deactivation products
            foreach ($event_data as $datetime => $key_array) {
                if (strtotime($datetime) < $curr_time) {
                    foreach ($key_array as $key) {
                        event_process($category_products_array[$key], $product_status);
                    }
                }
            }
        }
    }
}

function event_process($array_list, $status) {
    global $memcache_obj, $log_object, $exclude_id_array;

    $return_bool = true;
    $clear_cache = false;

    foreach ($array_list as $game_id => $p_array) {
        foreach ($p_array as $pid) {
            if (!in_array($pid, $exclude_id_array)) {
                # activate product
                $check_select_sql = "   SELECT products_id
                                        FROM " . TABLE_PRODUCTS . "
                                        WHERE products_id = '" . $pid . "'
                                            AND products_status != '" . $status . "'";
                $check_result_sql = tep_db_query($check_select_sql);
                if (tep_db_num_rows($check_result_sql)) {
                    if (tep_set_product_status($pid, $status)) {
                        $log_object->insert_log($pid, 'products_status', ($status == '1' ? '0' : '1'), $status, LOG_STATUS_ADJUST);
                        $clear_cache = true;
                    } else {
                        // fail to update status, email this issue
                        reportError(array('product_id' => $pid), '[event_process] Failed to update status to ' . $status);
//                        $return_bool = false;
                    }
                } else {
                    $clear_cache = true;
                }
            }
        }

        if ($clear_cache) {
            $cache_key = 'category_and_products/array/category_id/' . $game_id;

            #key:category_and_products/array/category_id/xxx
            $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . $cache_key, 0);
//            reportError(array('ids' => $game_id, 'p' => $p_array), '[event_process] Success update status to ' . $status . ' : ' . ($clear_cache ? 'success' : 'failed'));
        }
    }

    return $return_bool;
}

function uploadJson($filename, $file_content) {
    global $aws_obj;

    $s3 = true;
    $bucket = 'promo.offgamers.com';
    $file_name = 'winter-sales/assets/' . $filename;
    $opt_array = array(
        'body' => $file_content,
        'contentType' => CFMimeTypes::get_mimetype('js'),
        'acl' => constant('AmazonS3::ACL_PUBLIC'),
        'headers' => '',
//        'storage' => '',
//        'headers' => '',
    );

    try {
        if ($s3) {
            $result = $aws_obj->s3->create_object($bucket, $file_name, $opt_array);
            $return_status = (int) $result->status;

            if ($return_status >= 300) {
                reportError(array('r' => $result, 'b' => $bucket, 'f' => $file_name), "[uploadJson] Failed to upload with status " . $return_status);
                $return_status = false;
            } else {
                reportError(array('r' => $result, 'b' => $bucket, 'f' => $file_name), "[uploadJson] Success to upload with status " . $return_status);
                $return_status = true;
            }
        } else {
            $fh = fopen('/var/www/html/offgamers.dev/www54g2.offgamers.dev/admin/' . $file_name, 'w') or die("can't open file");
            $return_status = fwrite($fh, $file_content);
            fclose($fh);
        }

        unset($result);
    } catch (Exception $e) {
        reportError(array('e_msg' => $e->getMessage(), 'b' => $bucket, 'f' => $file_name), "[uploadJson] Failed to upload with exeption.");
    }

    return $return_status;
}

function BlackFriday($category_id_array, $key, $tag, $btn_label = 'Buy', & $promo_category = array()) {
    global $event_controller;
    $edate = '2999-01-01';
    $etime = '';

    # find expiry date
    foreach ($event_controller['end'] as $end_date => $end_key_array) {
        if (in_array($key, $end_key_array)) {
            $edatetime = $end_date;
            list($edate, $etime) = explode(' ', $edatetime);
            break;
        }
    }

    foreach ($category_id_array[$key] as $cid => $product_arr) {
        $promo_category[] = array(
            "d" => getCustomMappedStr($cid, 'name'),
            "c" => getCustomMappedStr($cid, 'link'),
            "i" => '/black-friday/assets/img/' . $key . '/' . $cid . '.jpg',
            "h" => $tag,
            "btn" => $btn_label,
            "e" => (strtotime($edate) * 1000)
        );
    }

    # First time list
//    if (!$promo_category) {
//        $get_first_id_array = array_pop($show_hide_config);
//        $cid = $get_first_id_array[0];
//
//        $default_name = getCustomMappedStr($cid, 'name');
//        $default_link = getCustomMappedStr($cid, 'link');
//        $is_active = false;
//
//        for ($i=0; $i < 15; $i++) {
//            $promo_category[] = array(
//                "dc" => 'even',
//                "d" => $default_name,
//                "c" => $default_link,
//                "i" => '//promo.offgamers.com/black-friday/assets/img/' . $cid . '.jpg',
//                "h" => '-',
//            );
//        }
//    }
//    $return_array = array(
//        'is_active' => $is_active,
//        'data' => $promo_category
//    );

    return $promo_category;
}

function getCustomMappedStr($cid, $type) {
    $return_str = '';

    if ($type === 'link') {
        $page_url = createCategoryPageUrl($cid);
        $temp_arr = explode('/', $page_url);
        $campaign_key = '';

        if ($campaign_key = array_pop($temp_arr)) {
            $campaign_key = '-' . $campaign_key;
        }

        $return_str = 'https://www.offgamers.com/' . $page_url . '?utm_campaign=bfcm2016' . $campaign_key . '&utm_source=web&utm_medium=referral';
    } else if ($type === 'name') {
        $return_str = tep_get_categories_name($cid, 1);
    }

    return $return_str;
}

function createCategoryPageUrl($cid) {
    $seoUrl = '';
    $categories_url_alias = '';

    $cat_name_select_sql = "SELECT categories_url_alias
                            FROM " . TABLE_CATEGORIES . "
                            WHERE categories_id='" . $cid . "' ";
    $cat_name_result_sql = tep_db_query($cat_name_select_sql);
    if ($cat_name_row = tep_db_fetch_array($cat_name_result_sql)) {
        $categories_url_alias = $cat_name_row['categories_url_alias'];
    }

    if ($categories_url_alias) {
        $tag_key = array();

        $cat_name_select_sql = "SELECT tag_id
                                FROM categories_tagmap
                                WHERE game_id='" . $cid . "'";
        $cat_name_result_sql = tep_db_query($cat_name_select_sql);
        if ($cat_name_row = tep_db_fetch_array($cat_name_result_sql)) {
            $tag_id = $cat_name_row['tag_id'];

            $cat_name_select_sql2 = "SELECT parent.tag_id, parent.tag_key
                                    FROM categories_tag AS node,
                                        categories_tag AS parent
                                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                                        AND node.tag_id = " . $tag_id . "
                                        AND node.tag_status = 1
                                    ORDER BY node.tag_lft";
            $cat_name_result_sql2 = tep_db_query($cat_name_select_sql2);
            while ($p_array = tep_db_fetch_array($cat_name_result_sql2)) {
                $tag_key[$p_array['tag_id']] = $p_array['tag_key'];
            }
        }

        if ($tag_key) {
            array_shift($tag_key);
            $seoUrl = str_ireplace('_', '-', strtolower(implode('/', $tag_key))) . '/' . $categories_url_alias;
        } else {
            $seoUrl = $categories_url_alias;
        }
    }

    return $seoUrl;
}

function reportError($response, $ext_subject = '') {
    ob_start();
    echo "<pre>" . $ext_subject;
    echo "================================================RESPONSE================================================";
    print_r($response);
    echo "========================================================================================================";
    $response_data = ob_get_contents();
    ob_end_clean();

    $subject = 'Cron Event Error - ' . date("F j, Y H:i");

    @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

?>