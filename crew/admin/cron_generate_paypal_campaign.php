<?php

/*
 * site url : https://promo.offgamers.com/paypalcampaign/index.html 
 * To-do
 *  - 
 */
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

$language = 'english';  // used in store_credit.php class
$default_languages_id = 1;
$languages_id = $default_languages_id;

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
include_once(DIR_WS_CLASSES . 'log.php');

require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

$_SESSION['default_languages_id'] = 1;

$aws_obj = new ogm_amazon_ws();
$log_object = new log_files('system');

$date = new DateTime();
$date->setTimezone(new DateTimeZone('America/Los_Angeles'));  //America/Los_Angeles Asia/Kuala_Lumpur
$curr_time = strtotime($date->format('Y-m-d H:i:s')) + (1 * 60 * 60) + 600;

$category_products_array = array(
    # Generate All Product Selected
    'all' => array(
        '4444' => array(),
        '5236' => array(),
        '17712' => array(),
        '17710' => array(),
        '21596' => array(),
        '17716' => array(),
        '17715' => array(),
        '17714' => array(),
        '17717' => array(),
        '22831' => array(),
        '17713' => array(),
        '22828' => array(),
        '17711' => array(),
        '21584' => array(),
        '18261' => array(),
        '22844' => array(),
        '24115' => array(),
        '23596' => array(),
        '17855' => array(),
        '22569' => array(),
        '20359' => array(),
        '20855' => array(),
        '21599' => array(),
        '20052' => array(),
        '22768' => array(),
        '23940' => array(),
        '22671' => array(),
        '22961' => array(),
        '22587' => array(),
        '22847' => array(),
        '22585' => array(),
        '22586' => array(),
        '24178' => array(),
        '24175' => array(),
        '24199' => array(),
        '24196' => array(),
        '24252' => array(),
        '24251' => array(),
        '24202' => array(),
        '17884' => array(),
        '23848' => array(),
        '20343' => array(),
        '22819' => array(),
        '22545' => array(),
        '23756' => array(),
        '23757' => array(),
        '20767' => array(),
        '23851' => array(),
        '18181' => array(),
        '23943' => array(),
        '20994' => array(),
        '22860' => array(),
        '20997' => array(),
        '18268' => array(),
        '3466' => array(),
        '18269' => array(),
        '21673' => array(),
        '24248' => array(),
        '21492' => array(),
        '5147' => array(),
        '5329' => array(),
        '23256' => array(),
        '21000' => array(),
        '18267' => array(),
        '21117' => array(),
        '24032' => array(),
        '22870' => array(),
        '21676' => array(),
        '21188' => array(),
        '22572' => array(),
        '22573' => array(),
        '5380' => array(),
        '23531' => array(),
        '22865' => array(),
        '22730' => array(),
        '22126' => array(),
        '20362' => array(),
        '20365' => array(),
        '20784' => array(),
        '20891' => array(),
        '17634' => array(),
        '22581' => array(),
        '20318' => array(),
        '21090' => array(),
        '23360' => array(),
        '21172' => array(),
        '21175' => array(),
        '21123' => array(),
        '21169' => array(),
        '21166' => array(),
        '23357' => array(),
        '21136' => array(),
        '21120' => array(),
        '21114' => array(),
        '23575' => array(),
        '17949' => array(),
        '24126' => array(),
        '8601' => array(),
        '22110' => array(),
        '23222' => array(),
        '23299' => array(),
        '20858' => array(),
        '22835' => array(),
        '22753' => array(),
        '20771' => array(),
        '23348' => array(),
        '18295' => array(),
        '23440' => array(),
        '21165' => array(),
        '5164' => array(),
        '20573' => array(),
        '21180' => array(),
        '22553' => array(),
        '23455' => array(),
        '20842' => array(),
        '23207' => array(),
        '23210' => array(),
        '23606' => array(),
        '22714' => array(),
        '4444' => array(),
        '5236' => array(),
        '18261' => array(),
        '17712' => array(),
        '23088' => array(),
        '23104' => array(),
        '22344' => array(),
        '23548' => array(),
        '23528' => array(),
        '4360' => array(),
        '21331' => array(),
        '21505' => array(),
        '21278' => array(),
        '22038' => array(),
        '23342' => array(),
        '1667' => array(),
        '20633' => array(),
        '15699' => array(),
        '23147' => array(),
        '23139' => array(),
        '23155' => array(),
        '22340' => array(),
        '23189' => array(),
        '23188' => array(),
        '22350' => array(),
        '21897' => array(),
        '19196' => array(),
        '23063' => array(),
        '23067' => array(),
        '21889' => array(),
        '4835' => array(),
        '4841' => array(),
        '4858' => array(),
        '4852' => array(),
        '4849' => array(),
        '21732' => array(),
        '4130' => array(),
        '2522' => array(),
        '2299' => array(),
        '22734' => array(),
        '22738' => array(),
        '22745' => array(),
        '23100' => array(),
        '22749' => array(),
        '17880' => array(),
        '21704' => array(),
        '4344' => array(),
        '14357' => array(),
        '23117' => array(),
        '23120' => array(),
        '23114' => array(),
        '21485' => array(),
        '22660' => array(),
        '23946' => array(),
        '20865' => array(),
        '22661' => array(),
        '13136' => array(),
        '12715' => array(),
        '21393' => array(),
        '22506' => array(),
        '16565' => array(),
        '4458' => array(),
        '16853' => array(),
        '6306' => array(),
        '16328' => array(),
        '7779' => array(),
        '23483' => array(),
        '9578' => array(),
        '23802' => array(),
        '17797' => array(),
        '23551' => array(),
        '23892' => array(),
        '23229' => array(),
        '24172' => array(),
        '14533' => array(),
        '4536' => array(),
        '16059' => array(),
        '22741' => array(),
        '17508' => array(),
        '21608' => array(),
        '11860' => array(),
        '4100' => array(),
        '4097' => array(),
        '23296' => array(),
        '12750' => array(),
        '14601' => array(),
        '4699' => array(),
        '8502' => array(),
        '4932' => array(),
        '6031' => array(),
        '15139' => array(),
        '6248' => array(),
        '7276' => array(),
        '20056' => array(),
        '16321' => array(),
        '5154' => array(),
        '17945' => array(),
        '23253' => array(),
        '23378' => array(),
        '23910' => array(),
        '11690' => array(),
        '22995' => array(),
        '23331' => array(),
        '23500' => array(),
        '23504' => array(),
        '21570' => array(),
        '20779' => array(),
        '20848' => array(),
        '17911' => array(),
        '23276' => array(),
        '20775' => array(),
        '14662' => array(),
        '21222' => array()
    ),
);

$exclude_id_array = array();

$event_controller = array(
    'start' => array(
        '2017-06-19 00:00:00' => array(
            'all'
        ),
    ),
    'end' => array(
        '2017-06-20 00:00:00' => array(
            'all'
        ),
    )
);


foreach ($event_controller as $event => $event_data) {
    $product_status = $event === 'start' ? '1' : '0';
    $new = 0;

    if ($event === 'start') {
        # activate products + upload new list
        $json_array = array();
        $upload_datetime = '';

        foreach ($event_data as $datetime => $key_array) {
            $upload_datetime = $datetime;

            foreach ($key_array as $key) {
                event_process($category_products_array[$key], $product_status);
                $json_array = BlackFriday($category_products_array, $key, '-', 'Buy', $json_array);
            }
        }

        if ($json_array) {
            list($date, ) = explode(' ', $upload_datetime);
            uploadJson('request-13thanniversary.js', "angular.callbacks._0(" . json_encode(array(
                        'config' => array(
                            'active' => 'active-yes'
                        ),
                        'data' => $json_array
                    )) . ")");
        }
    } else {
        # deactivation products
        foreach ($event_data as $datetime => $key_array) {
            if (strtotime($datetime) < $curr_time) {
                foreach ($key_array as $key) {
                    event_process($category_products_array[$key], $product_status);
                }
            }
        }
    }
}

function event_process($array_list, $status) {
    global $memcache_obj, $log_object, $exclude_id_array;

    $return_bool = true;
    $clear_cache = false;

    foreach ($array_list as $game_id => $p_array) {
        foreach ($p_array as $pid) {
            if (!in_array($pid, $exclude_id_array)) {
                # activate product
                $check_select_sql = "   SELECT products_id
                                        FROM " . TABLE_PRODUCTS . "
                                        WHERE products_id = '" . $pid . "'
                                            AND products_status != '" . $status . "'";
                $check_result_sql = tep_db_query($check_select_sql);
                if (tep_db_num_rows($check_result_sql)) {
                    if (tep_set_product_status($pid, $status)) {
                        $log_object->insert_log($pid, 'products_status', ($status == '1' ? '0' : '1'), $status, LOG_STATUS_ADJUST);
                        $clear_cache = true;
                    } else {
                        // fail to update status, email this issue
                        reportError(array('product_id' => $pid), '[event_process] Failed to update status to ' . $status);
//                        $return_bool = false;
                    }
                } else {
                    $clear_cache = true;
                }
            }
        }

        if ($clear_cache) {
            $cache_key = 'category_and_products/array/category_id/' . $game_id;

            #key:category_and_products/array/category_id/xxx
            $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . $cache_key, 0);
        }
    }

    return $return_bool;
}

function uploadJson($filename, $file_content) {
    global $aws_obj;

    $s3 = false;
    $bucket = 'promo.offgamers.com';
    $file_name = $filename;
    $opt_array = array(
        'body' => $file_content,
        'contentType' => CFMimeTypes::get_mimetype('js'),
        'acl' => constant('AmazonS3::ACL_PUBLIC'),
        'headers' => '',
//        'storage' => '',
//        'headers' => '',
    );

    try {
        if ($s3) {
            $result = $aws_obj->s3->create_object($bucket, $file_name, $opt_array);
            $return_status = (int) $result->status;

            if ($return_status >= 300) {
                reportError(array('r' => $result, 'b' => $bucket, 'f' => $file_name), "[uploadJson] Failed to upload with status " . $return_status);
                $return_status = false;
            } else {
                reportError(array('r' => $result, 'b' => $bucket, 'f' => $file_name), "[uploadJson] Success to upload with status " . $return_status);
                $return_status = true;
            }
        } else {
            $fh = fopen('/var/www/html/crew.offgamers.com/admin/download/' . $file_name, 'w') or die("can't open file");
            $return_status = fwrite($fh, $file_content);
            fclose($fh);
        }

        unset($result);
    } catch (Exception $e) {
        reportError(array('e_msg' => $e->getMessage(), 'b' => $bucket, 'f' => $file_name), "[uploadJson] Failed to upload with exeption.");
    }

    return $return_status;
}

function BlackFriday($category_id_array, $key, $tag, $btn_label = 'Buy', & $promo_category = array()) {
    global $event_controller;
    $edate = '2999-01-01';
    $etime = '';

    # find expiry date
    foreach ($event_controller['end'] as $end_date => $end_key_array) {
        if (in_array($key, $end_key_array)) {
            $edatetime = $end_date;
            list($edate, $etime) = explode(' ', $edatetime);
            break;
        }
    }

    foreach ($category_id_array[$key] as $cid => $product_arr) {
        $promo_category[] = array(
            "d" => getCustomMappedStr($cid, 'name'),
            "c" => getCustomMappedStr($cid, 'link'),
            "i" => '/13thanniversary/assets/img/all/' . $cid . '.jpg',
            "h" => $tag,
            "btn" => $btn_label,
            "e" => (strtotime($edate) * 1000)
        );
    }

    # First time list
//    if (!$promo_category) {
//        $get_first_id_array = array_pop($show_hide_config);
//        $cid = $get_first_id_array[0];
//
//        $default_name = getCustomMappedStr($cid, 'name');
//        $default_link = getCustomMappedStr($cid, 'link');
//        $is_active = false;
//
//        for ($i=0; $i < 15; $i++) {
//            $promo_category[] = array(
//                "dc" => 'even',
//                "d" => $default_name,
//                "c" => $default_link,
//                "i" => '//promo.offgamers.com/black-friday/assets/img/' . $cid . '.jpg',
//                "h" => '-',
//            );
//        }
//    }
//    $return_array = array(
//        'is_active' => $is_active,
//        'data' => $promo_category
//    );

    return $promo_category;
}

function getCustomMappedStr($cid, $type) {
    $return_str = '';

    if ($type === 'link') {
        $page_url = createCategoryPageUrl($cid);
        $temp_arr = explode('/', $page_url);
        $campaign_key = '';

        if ($campaign_key = array_pop($temp_arr)) {
            $campaign_key = '-' . $campaign_key;
        }

        $return_str = 'https://www.offgamers.com/' . $page_url . '?utm_campaign=ogm13thanniversary&utm_source=landingpage&utm_medium=web';
    } else if ($type === 'name') {
        $return_str = tep_get_categories_name($cid, 1);
    }

    return $return_str;
}

function createCategoryPageUrl($cid) {
    $seoUrl = '';
    $categories_url_alias = '';

    $cat_name_select_sql = "SELECT categories_url_alias
                            FROM " . TABLE_CATEGORIES . "
                            WHERE categories_id='" . $cid . "' ";
    $cat_name_result_sql = tep_db_query($cat_name_select_sql);
    if ($cat_name_row = tep_db_fetch_array($cat_name_result_sql)) {
        $categories_url_alias = $cat_name_row['categories_url_alias'];
    }

    if ($categories_url_alias) {
        $tag_key = array();

        $cat_name_select_sql = "SELECT tag_id
                                FROM categories_tagmap
                                WHERE game_id='" . $cid . "'";
        $cat_name_result_sql = tep_db_query($cat_name_select_sql);
        if ($cat_name_row = tep_db_fetch_array($cat_name_result_sql)) {
            $tag_id = $cat_name_row['tag_id'];

            $cat_name_select_sql2 = "SELECT parent.tag_id, parent.tag_key
                                    FROM categories_tag AS node,
                                        categories_tag AS parent
                                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                                        AND node.tag_id = " . $tag_id . "
                                        AND node.tag_status = 1
                                    ORDER BY node.tag_lft";
            $cat_name_result_sql2 = tep_db_query($cat_name_select_sql2);
            while ($p_array = tep_db_fetch_array($cat_name_result_sql2)) {
                $tag_key[$p_array['tag_id']] = $p_array['tag_key'];
            }
        }

        if ($tag_key) {
            array_shift($tag_key);
            $seoUrl = str_ireplace('_', '-', strtolower(implode('/', $tag_key))) . '/' . $categories_url_alias;
        } else {
            $seoUrl = $categories_url_alias;
        }
    }

    return $seoUrl;
}

function reportError($response, $ext_subject = '') {
    ob_start();
    echo "<pre>" . $ext_subject;
    echo "================================================RESPONSE================================================";
    print_r($response);
    echo "========================================================================================================";
    $response_data = ob_get_contents();
    ob_end_clean();

    $subject = 'Cron Event Error - Paypal Campaign' . date("F j, Y H:i");

    @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

?>