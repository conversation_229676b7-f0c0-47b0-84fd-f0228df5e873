<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_LANGUAGES . 'english/aft_automation.php');

tep_set_time_limit(0);
tep_db_connect() or die('Unable to connect to database server!');
// set application wide parameters
$configuration_query = tep_db_query('   SELECT configuration_key as cfgKey, configuration_value as cfgValue
                                        FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_CLASSES . 'anti_fraud.php');
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'c2c_buyback_order.php');
require_once(DIR_WS_CLASSES . 'c2c_order.php');

$memcache_obj = new OGM_Cache_MemCache();
$currencies = new currencies();

include_once(DIR_WS_FUNCTIONS . 'custom_product.php');
include_once(DIR_WS_LANGUAGES . 'english.php');
$cron_filename = 'cron_genesis_orders.php';
$jobs_done = 0;
$first_N_record = 20;
$default_languages_id = 1;
$languages_id = $default_languages_id;
// Only perform the action if total records to be processed is passed.
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
    exit;
} else {
    $first_N_record = $_SERVER['argv'][1];
}

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 3 MINUTE) AS overdue_process, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 8 MINUTE) AS long_process, cron_process_track_failed_attempt
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
        if ($cron_process_checking_row['overdue_process'] == '1') {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($cron_process_attempt_update_sql);

            if ($cron_process_checking_row['cron_process_track_failed_attempt'] < 3) {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Genesis Order cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'];
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }

        if ($cron_process_checking_row['long_process'] == 1) {  // Prevent DTU cronjob keep awaiting
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                        SET cron_process_track_in_action=1,
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
            tep_db_query($cron_process_update_sql);
        } else {
            exit;
        }
    } else {
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
									SET cron_process_track_in_action=1,
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0
									WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($cron_process_update_sql);
    }

    $lock_obj = new process_locking();
    // Get Latest OG Orders and set the Flag to 2 for processing
    $hold_genesis_orders = "UPDATE " . TABLE_CRON_GENESIS_ORDERS . " t1
                            SET t1.flag = 2, t1.last_modified = now()
                            WHERE t1.flag = 0 AND
                                EXISTS (SELECT 1
                                        FROM " . TABLE_ORDERS_EXTRA_INFO . " t2
                                        WHERE t2.orders_id = t1.orders_id AND
                                                t2.orders_extra_info_key = 'site_id' AND
                                                t2.orders_extra_info_value='0'
                                        )
                            ORDER BY t1.orders_id DESC
                            LIMIT " . $first_N_record;
    tep_db_query($hold_genesis_orders);

    // Grab flag = 2 (hold) records
    $genesis_order_select_sql = "	SELECT cgo.orders_id, cgo.re_run, o.orders_status
										FROM " . TABLE_CRON_GENESIS_ORDERS . " AS cgo
                                        INNER JOIN " . TABLE_ORDERS . " AS o
                                            ON (cgo.orders_id = o.orders_id)
										WHERE cgo.flag = 2
										ORDER BY cgo.orders_id DESC";
    $genesis_order_result_sql = tep_db_query($genesis_order_select_sql);
    while ($genesis_order_row = tep_db_fetch_array($genesis_order_result_sql)) {
        $oID = $genesis_order_row['orders_id'];
        // $site_id = c2c_order::orderSiteID($oID);

        if ($genesis_order_row['orders_status'] == '7') {
            if ($jobs_done >= $first_N_record)
                break;

            $process_status = true;
            // if($site_id==5){
            //     $orders_cancel_request_select_sql = "	SELECT orders_id
            //                                 FROM " . TABLE_ORDERS_CANCEL_REQUEST . "
            //                                         WHERE orders_id = '" . $oID . "'";
            //     $orders_cancel_request_result_sql = tep_db_query($orders_cancel_request_select_sql);
            //     if (tep_db_fetch_array($orders_cancel_request_result_sql)) {
            //         $process_status = false;
            //     }
            // }

            if($process_status === true){
                $order = new order($oID);
                $log_object = new log_files($oID);

                // Run AFT to delivery order
                $aft_obj = new anti_fraud($oID);
                $aft_obj->getAftModule()->execute_stored_query_call($oID, $order->customer['id']);
                $aft_obj->execute_aft_script($genesis_order_row['re_run']);
                $jobs_done += 1;

                // G2G : update available-qty
                // if (($site_id == 5) && ($genesis_order_row['re_run'] == 3)) {
                //     $order->get_product_order_info(); // retrieve EXTRA_INFO

                //     for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
                //         $_listing = tep_array_unserialize($order->products[$i]['extra_info']['listing']);

                //         $c2c_obj = new c2c_order();
                //         $c2c_obj->postOrderRules($_listing['c2c_products_listing_id'], 1, 7, ($order->products[$i]['qty'] - $order->products[$i]['delivered_qty']), $oID, 'cron_genesis_orders');
                //     }
                // }
            }

            $genesis_orders_delivered_sql = "UPDATE " . TABLE_CRON_GENESIS_ORDERS . "
                                                SET flag = 1, last_modified = now()
                                                WHERE orders_id = '" . $oID . "'";
            tep_db_query($genesis_orders_delivered_sql);
        } else {
            $genesis_orders_delivered_sql = "   UPDATE " . TABLE_CRON_GENESIS_ORDERS . "
                                                    SET flag = 1, last_modified = now()
                                                    WHERE orders_id = '" . $oID . "'";
            tep_db_query($genesis_orders_delivered_sql);
        }

        # release lock
        $lock_obj->releaseLocked($oID, 'OGM_ORDER_RUN_GENESIS');
        
        tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_id = '" . $oID . "'");
    }

    $genesis_cron_delete_sql = "	DELETE FROM " . TABLE_CRON_GENESIS_ORDERS . "
                                        WHERE flag = 1";
    tep_db_query($genesis_cron_delete_sql);

    // Release cron process "LOCK"
    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
									SET cron_process_track_in_action=0
									WHERE cron_process_track_filename = '" . $cron_filename . "'";
    tep_db_query($unlock_cron_process_sql);
}
?>