<?
	include_once('includes/configure.php');
	include_once(DIR_WS_INCLUDES . 'filenames.php');
	include_once(DIR_WS_INCLUDES . 'database_tables.php');
	include_once(DIR_WS_FUNCTIONS . 'database.php');
	include_once(DIR_WS_FUNCTIONS . 'general.php');
	include_once(DIR_WS_FUNCTIONS . 'html_output.php');
	include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

	require_once('includes/classes/ogm_xml_to_ary.php');

	tep_set_time_limit(300);
	tep_db_connect() or die('Unable to connect to database server!');

	// set application wide parameters
	$configuration_query = tep_db_query('SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM ' . TABLE_CONFIGURATION . " WHERE configuration_key IN ('SEARCH_ENGINE_FRIENDLY_URLS', 'GRAPHIC_MAIL_DATASET_ID', 'GRAPHIC_MAIL_NEWSLETTER_ID', 'GRAPHIC_MAIL_API_USERNAME', 'GRAPHIC_MAIL_API_PASSWORD', 'AWS_S3_ENABLED')");
	while ($configuration = tep_db_fetch_array($configuration_query)) {
		define($configuration['cfgKey'], $configuration['cfgValue']);
	}

	$contact_delete_sql = "DELETE FROM " . TABLE_INVITER_CONTACTS . " WHERE now() > (inviter_contacts_insert_datetime + INTERVAL 30 DAY);";
	tep_db_query($contact_delete_sql);

	$aws_obj = new ogm_amazon_ws();
	$aws_obj->set_bucket_key('BUCKET_NEWSLETTER');
	$aws_obj->set_filepath('graphic_mail/');
	$aws_obj->set_storage('STORAGE_STANDARD');
	$aws_enabled = $aws_obj->is_aws_s3_enabled();

	//Configuration
	$max_records = 300;
	$import_from_url = $aws_obj->get_image_url_by_instance(tep_catalog_href_link('data/graphic_mail/'));
	// &Col2=1&Col3=2&Col4=3&Col5=4&Col6=5&Col7=6
	$column_mapping = '&Col1=2&Col2=3&Col3=4&Col4=5&Col5=6&Col6=7'; // REMARK : define column [Col1 == Dataset's Extra first Column] eg. Col1 = 3 [CSV's 3rd column map with Graphic Mail's first additional column.

	$default_dataset_id = GRAPHIC_MAIL_DATASET_ID;
	$default_newsletter_id = 502528;
	$default_cn_newsletter_id = 502555;

	#API Setting
	$API_username = 'encUsername='.GRAPHIC_MAIL_API_USERNAME;
	$API_password = 'encPassword='.GRAPHIC_MAIL_API_PASSWORD;

	#Email Setting
	$sender_email = '<EMAIL>';
	$sender_name = urlencode(htmlentities('<GM'.$default_dataset_id.'.3>iEmail</GM>'));
	$mail_subject = urlencode('Your friend has invited you to join OffGamers.');
	$mail_subject_2 = urlencode('您的好友邀请您加入OffGamers');

	#CURL Setting
	$prefix = 'https://www.graphicmail.com/api.aspx?'.$API_username.'&'.$API_password.'&';
	$options = array(	CURLOPT_VERBOSE => TRUE,
             			CURLOPT_SSL_VERIFYPEER => FALSE,
             			CURLOPT_RETURNTRANSFER => TRUE,
             			CURLOPT_FOLLOWLOCATION => TRUE,
             			CURLOPT_SSL_VERIFYHOST => 1,
             			CURLOPT_TIMEOUT => 30,
             			CURLOPT_USERAGENT => "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)"
            	);

	if(cron_job_process('start')) {
		$records_select_sql = "	SELECT im.inviter_messages_type
								FROM inviter_messages im
								WHERE im.inviter_messages_type != 'P'
								GROUP BY im.inviter_messages_type";
		$records_select_result_sql = tep_db_query($records_select_sql);
		while ($records_select_row = tep_db_fetch_array($records_select_result_sql)) {
			$mailing_list_name = $records_select_row['inviter_messages_type'];
			$datetime = explode("__",  $mailing_list_name);
			list(,$yr, $mth, $day) = explode('_', $datetime[0]);
			list($hr, $min, $sc) = explode('_', $datetime[1]);

			$dataset_id = 0;
			$mailing_list_id = 0;
			$newsletter_id = 0;

			if (tep_day_diff("$yr-$mth-$day $hr:$min:$sc", date("y-m-d H:i:s"), "sec") >= 86400) {
				$dt = $yr.'_'.$mth.'_'.$day.'__'.$hr.'_'.$min.'_'.$sc;
				$dataset_name = 'DS_'.$dt;
				$newsletter_name = 'NL_'.$dt;
				$dataset_filename = 'DSF_'.$dt.'.csv';
				$newsletter_filename = $newsletter_name.'.html';
				$dataset_csv_location = DIR_FS_DATA_GRAPHIC_MAIL.$dataset_filename;
				$newsletter_location = DIR_FS_DATA_GRAPHIC_MAIL.$newsletter_filename;

				$email_sent_status = check_email_sent($mailing_list_name, '20'.$yr.'-'.$mth.'-'.$day);

				if ($email_sent_status >= 0) {
					if ($email_sent_status == 1) {
						tep_db_query("DELETE FROM inviter_messages WHERE inviter_messages_type = '".$mailing_list_name."'");
					} else {
						$update_data_sql = array('inviter_messages_type' => 'P');
						tep_db_perform("inviter_messages", $update_data_sql, 'update', "inviter_messages_type = '".$mailing_list_name."'");
						report_error('[Set To Pending][ML:' . $mailing_list_name . '] Deliver Failed');
					}

					delete_mailing_list($mailing_list_id);
					delete_dataset($dataset_id);
					delete_newsletter($newsletter_id);
				}
			}
		}

		$records_select_sql = "	SELECT im.inviter_messages_language_id
								FROM inviter_messages im
								WHERE im.inviter_messages_type = 'P'
								GROUP BY im.inviter_messages_language_id";
		$records_select_result_sql = tep_db_query($records_select_sql);
		while ($records_select_row = tep_db_fetch_array($records_select_result_sql)) {
			$language_id = $records_select_row['inviter_messages_language_id'];

			$next_process = true;
			$dataset_id = 0;
			$mailing_list_id = 0;
			$newsletter_id = 0;

			if (cron_job_process('start_next')) {
				$cron_select_sql = "	SELECT cron_process_track_start_date
										FROM ".TABLE_CRON_PROCESS_TRACK."
										WHERE cron_process_track_filename = 'cron_graphic_mail.php'";
				$cron_select_result_sql = tep_db_query($cron_select_sql);
				if ($cron_select_row = tep_db_fetch_array($cron_select_result_sql)) {
					$dt = date_format(date_create($cron_select_row['cron_process_track_start_date']), "y_m_d__H_i_s");
					$dataset_name = 'DS_'.$dt;
					$mailing_list_name = 'ML_'.$dt;
					$newsletter_name = 'NL_'.$dt;
					$dataset_filename = 'DSF_'.$dt.'.csv';
					$newsletter_filename = $newsletter_name.'.html';
					$dataset_csv_location = DIR_FS_DATA_GRAPHIC_MAIL.$dataset_filename;
					$newsletter_location = DIR_FS_DATA_GRAPHIC_MAIL.$newsletter_filename;

					if (create_dataset_CSV($language_id) == 0) {	// >0 : success, 0 : error
						$next_process = false;
					}

					if ($next_process) {
						if (copy_dataset() == 0) {		// 1 : success, 0 : error
							$next_process = false;
						}
					}

					if ($next_process) {
						$dataset_id = get_dataset_id();	// >0 : success, 0 : error

						if ($dataset_id == 0) {
							$next_process = false;
						}
					}

					if ($next_process) {
						$mailing_list_id = get_new_mailing_list_id();	// >0 : success, 0 : error

						if ($mailing_list_id == 0) {
							$next_process = false;
						}
					}

					if ($next_process) {
						if (create_newsletter($language_id) == 0) {	// >0 : success, 0 : error
							$next_process = false;
						}
					}

					if ($next_process) {
						$newsletter_id = get_imported_newsletter_id();		// 1 : success, 0 : error

						if ($newsletter_id == 0) {
							$next_process = false;
						}
					}

					if ($next_process) {
						if (tep_not_null($dataset_id) && tep_not_null($mailing_list_id) && tep_not_null($newsletter_id)) {

							if (send_dataset() > 0) {
								send_mail($language_id == 2 ? $mail_subject_2 : $mail_subject);
							}
						}
					}
				}
			} else {
				break;
			}
		}
		cron_job_process('end');
	}

	unset($aws_obj);

	function gm_curl ($suffix) {
		global $options, $prefix;

		$options[CURLOPT_URL] = $prefix.$suffix;

		$ch = curl_init();
		curl_setopt_array($ch, $options);
		$response = curl_exec($ch);

		if (empty($response)) {
			report_error('Function[gm_curl] PHP curl no response');
		}

		curl_close($ch);

		return $response;
	}

	function check_email_sent ($mailing_list_name, $sent_date) {
		global $dataset_id, $mailing_list_id, $newsletter_id, $mailing_list_name;

		$check_status = 0;
		$statistics_return = gm_curl('Function=get_statistics&Row=20'); //FromDate='.$sent_date.'&ToDate='.$sent_date);

		if (tep_not_null($statistics_return)) {
			$xml_array_obj = new ogm_xml_to_ary($statistics_return, 'content');
			$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

			if (isset($xml_data_array['statistics']['_c']['send']['_c'])) {
				foreach ($xml_data_array['statistics']['_c']['send']['_c'] as $item => $value) {
					$temp_array[$item] = $value['_v'];
				}

				if ($temp_array['mailinglistname'] == $mailing_list_name) {
					$check_status = 1;
					$dataset_id = get_dataset_id();
					$mailing_list_id = $temp_array['mailinglistid'];
					$newsletter_id = $temp_array['newsletterid'];
				}
				unset($temp_array);
			} else if (isset($xml_data_array['statistics']['_c']['send'])) {
				foreach ($xml_data_array['statistics']['_c']['send'] as $statistics_array) {
					foreach ($statistics_array['_c'] as $item => $value) {
						$temp_array[$item] = $value['_v'];
					}

					if ($temp_array['mailinglistname'] == $mailing_list_name) {
						$check_status = 1;
						$dataset_id = get_dataset_id();
						$mailing_list_id = $temp_array['mailinglistid'];
						$newsletter_id = $temp_array['newsletterid'];
						unset($temp_array);
						break;
					}
					unset($temp_array);
				}
			}

			if (!tep_not_null($dataset_id)) {
				$dataset_id = get_dataset_id();
			}
			if (!tep_not_null($mailing_list_id)) {
				$mailing_list_id = get_mailing_list_id();
			}
			if (!tep_not_null($newsletter_id)) {
				$newsletter_id = get_newsletter_id();
			}

			unset($statistics_return, $xml_array_obj, $xml_data_array);
		} else { // No response
			$check_status = -1;
			report_error('Function[check_email_sent][ML:' . $mailing_list_name . '] No response');
		}

		return $check_status;
	}

	function delete_mailing_list ($mailing_list_id) {
		global $mailing_list_name;

		if (tep_not_null($mailing_list_id)) {
			$delete_mail_list_return = gm_curl('Function=post_delete_mailinglist&MailinglistID='.$mailing_list_id);

			if (tep_not_null($delete_mail_list_return)) {
				$delete_mail_list_array_return = explode("|", $delete_mail_list_return);

				if ((int)$delete_mail_list_array_return[0] == 0) {
					report_error('Function[delete_mailing_list][ML:' . $mailing_list_name . '] Error : ' . $delete_mail_list_array_return[1]);
				}

				unset($delete_mail_list_array_return);
			} else {
				report_error('Function[delete_mailing_list][ML:' . $mailing_list_name . '] No response');
			}

			unset($delete_mail_list_return);
		} else {
			report_error('Function[delete_mailing_list][ML:' . $mailing_list_name . '] ID not found');
		}
	}

	function delete_dataset ($dataset_id) {
		global $dataset_csv_location, $dataset_filename, $dataset_name, $aws_enabled, $aws_obj;

		if ($aws_enabled) {
			$aws_obj->delete_file ($dataset_filename);
		} else if (file_exists($dataset_csv_location)) {
			@unlink($dataset_csv_location);
		}

		if (tep_not_null($dataset_id)) {
			$delete_dataset_return = gm_curl('Function=post_delete_dataset&DatasetID='.$dataset_id);

			if (tep_not_null($delete_dataset_return)) {
				$delete_dataset_array_return = explode("|", $delete_dataset_return);

				if((int)$delete_dataset_array_return[0] == 0) {
					report_error('Function[delete_dataset][DS:' . $dataset_name . '] Error : ' . $delete_dataset_array_return[1]);
				}

				unset($delete_dataset_array_return);
			} else {
				report_error('Function[delete_dataset][DS:' . $dataset_name . '] No response');
			}

			unset($delete_dataset_return);
		} else {
			report_error('Function[delete_dataset][ML:' . $dataset_name . '] ID not found');
		}
	}

	function delete_newsletter ($newsletter_id) {
		global $newsletter_location, $newsletter_filename, $newsletter_name, $aws_enabled, $aws_obj;

		if ($aws_enabled) {
			$aws_obj->delete_file ($newsletter_filename);
		} else if (file_exists($newsletter_location)) {
			@unlink($newsletter_location);
		}

		if (tep_not_null($newsletter_id)) {
			$delete_newsletter_return = gm_curl('Function=post_delete_newsletter&NewsletterID='.$newsletter_id);

			if (tep_not_null($delete_newsletter_return)) {
				$delete_newsletter_array_return = explode("|", $delete_newsletter_return);

				if ((int)$delete_newsletter_array_return[0] == 0) {
					report_error('Function[delete_newsletter][NL:' . $newsletter_name . '] Error : ' . $delete_newsletter_array_return[1]);
				}

				unset($delete_newsletter_array_return);
			} else {
				report_error('Function[delete_newsletter][NL:' . $newsletter_name . '] No response');
			}

			unset($delete_newsletter_return);
		} else {
			report_error('Function[delete_newsletter][NL:' . $newsletter_name . '] No response');
		}
	}

	function get_new_mailing_list_id () {
		global $mailing_list_name;

		$mailing_list_id = 0;
		$mail_list_id_return = gm_curl('Function=post_create_mailinglist&ReturnMailingListID=true&NewMailinglist='.$mailing_list_name);

		if (tep_not_null($mail_list_id_return)) {
			$mail_list_id_array_return = explode("|", $mail_list_id_return);

			if ((int)$mail_list_id_array_return[0] == 1) {
				$mailing_list_id =  (int)$mail_list_id_array_return[1];
			} else {
				report_error('Function[get_new_mailing_list_id][ML:' . $mailing_list_name . '] Error : ' . $mail_list_id_array_return[1]);
			}

			unset($mail_list_id_array_return);
		} else {
			report_error('Function[get_new_mailing_list_id][ML:' . $mailing_list_name . '] No response');
		}

		unset($mail_list_id_return);

		return $mailing_list_id;
	}

	function get_dataset_id () {
		global $dataset_name;

		$return_ds_id = 0;
		$datasets_return = gm_curl('Function=get_datasets');

		if (tep_not_null($datasets_return)) {
			$xml_array_obj = new ogm_xml_to_ary($datasets_return, 'content');
			$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

			if (isset($xml_data_array['datasets']['_c']['dataset']['_c'])) {
				foreach ($xml_data_array['datasets']['_c']['dataset']['_c'] as $item => $value) {
					$temp_array[$item] = $value['_v'];
				}

				if (array_search($dataset_name, $temp_array)) {
					$return_ds_id = (int)$temp_array['datasetid'];
					break;
				}
				unset($temp_array);
			} else if (isset($xml_data_array['datasets']['_c']['dataset'])) {
				foreach ($xml_data_array['datasets']['_c']['dataset'] as $ds_array) {
					foreach ($ds_array['_c'] as $item => $value) {
						$temp_array[$item] = $value['_v'];
					}

					if (array_search($dataset_name, $temp_array)) {
						$return_ds_id = (int)$temp_array['datasetid'];
						break;
					}
					unset($temp_array);
				}
			}

			if ($return_ds_id == 0) {
				report_error('Function[get_dataset_id][DS:' . $dataset_name . '] ID not found');
			}

			unset($xml_array_obj, $xml_data_array);
		} else {
			report_error('Function[get_dataset_id][DS:' . $dataset_name . '] No response');
		}

		unset($datasets_return);

		return $return_ds_id;
	}

	function get_mailing_list_id () {
		global $mailing_list_name;

		$return_mailing_list_id = 0;
		$mailing_lists_return = gm_curl('Function=get_mailinglists');

		if (tep_not_null($mailing_lists_return)) {
			$xml_array_obj = new ogm_xml_to_ary($mailing_lists_return, 'content');
			$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

			if (isset($xml_data_array['mailinglists']['_c']['mailinglist']['_c'])) {
				foreach ($xml_data_array['mailinglists']['_c']['mailinglist']['_c'] as $item => $value) {
					$temp_array[$item] = $value['_v'];
				}

				if (array_search($mailing_list_name, $temp_array)) {
					$return_mailing_list_id = (int)$temp_array['mailinglistid'];
					break;
				}
				unset($temp_array);
			} else if (isset($xml_data_array['mailinglists']['_c']['mailinglist'])) {
				foreach ($xml_data_array['mailinglists']['_c']['mailinglist'] as $ds_array) {
					foreach ($ds_array['_c'] as $item => $value) {
						$temp_array[$item] = $value['_v'];
					}

					if (array_search($mailing_list_name, $temp_array)) {
						$return_mailing_list_id = (int)$temp_array['mailinglistid'];
						break;
					}
					unset($temp_array);
				}
			}

			if ($return_mailing_list_id == 0) {
				report_error('Function[get_mailing_list_id][ML:' . $mailing_list_name . '] ID not found');
			}

			unset($xml_array_obj, $xml_data_array);
		} else {
			report_error('Function[get_mailing_list_id][ML:' . $mailing_list_name . '] No response');
		}

		unset($mailing_lists_return);

		return $return_mailing_list_id;
	}

	function get_newsletter_id () {
		global $newsletter_name;

		$return_newsletter_id = 0;
		$newsletters_return = gm_curl('Function=get_newsletters');

		if (tep_not_null($newsletters_return)) {
			$xml_array_obj = new ogm_xml_to_ary($newsletters_return, 'content');
			$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

			if (isset($xml_data_array['newsletters']['_c']['newsletter']['_c'])) {
				foreach ($xml_data_array['newsletters']['_c']['newsletter']['_c'] as $item => $value) {
					$temp_array[$item] = $value['_v'];
				}

				if (array_search($newsletter_name, $temp_array)) {
					$return_newsletter_id = (int)$temp_array['newsletterid'];
					break;
				}
				unset($temp_array);
			} else if (isset($xml_data_array['newsletters']['_c']['newsletter'])) {
				foreach ($xml_data_array['newsletters']['_c']['newsletter'] as $ds_array) {
					foreach ($ds_array['_c'] as $item => $value) {
						$temp_array[$item] = $value['_v'];
					}

					if (array_search($newsletter_name, $temp_array)) {
						$return_newsletter_id = (int)$temp_array['newsletterid'];
						break;
					}
					unset($temp_array);
				}
			}

			if ($return_newsletter_id == 0) {
				report_error('Function[get_newsletter_id][NL:' . $newsletter_name . '] ID not found');
			}

			unset($xml_array_obj, $xml_data_array);
		} else {
			report_error('Function[get_newsletter_id][NL:' . $newsletter_name . '] No response');
		}

		unset($newsletters_return);

		return $return_newsletter_id;
	}

	function create_newsletter ($language_id) {
		global $default_dataset_id, $default_newsletter_id, $default_cn_newsletter_id, $dataset_id, $newsletter_location, $newsletter_filename, $newsletter_name, $aws_enabled, $aws_obj;

		$create_status = 0;
		$newsletter_type_id = ($language_id == 1 ? $default_newsletter_id : $default_cn_newsletter_id);
		$newsletter_content_return = gm_curl('Function=get_newsletter&NewsletterID='.$newsletter_type_id);

		if (tep_not_null($newsletter_content_return)) {
			$newsletter_content_return = str_ireplace('data="'.$default_dataset_id.'.', 'data="'.$dataset_id.'.', $newsletter_content_return);
			$newsletter_content_return = str_ireplace('GM'.$default_dataset_id.'.', 'GM'.$dataset_id.'.', $newsletter_content_return);

			if ($aws_enabled) {
				$aws_obj->set_filename($newsletter_filename);
				$aws_obj->set_file_content($newsletter_content_return);
				$create_status = $aws_obj->save_file() ? 1 : 0;
			} else if (($fnewsletter = @fopen($newsletter_location, "w")) !== false) {
				$create_status = fwrite($fnewsletter, $newsletter_content_return);
				fclose($fnewsletter);
			} else {
				report_error('Function[create_newsletter][NL:' . $newsletter_name . '] Failed to open and write newsletter');
			}
		} else {
			report_error('Function[create_newsletter][NL:' . $newsletter_name . '] No response');
		}

		unset($newsletter_content_return);

		return $create_status;
	}

	function get_imported_newsletter_id () {
		global $import_from_url, $newsletter_filename, $newsletter_name;

		$newsletter_id = 0;
		$get_newsletter_return = gm_curl('Function=post_import_newsletter&HtmlURL='.$import_from_url.$newsletter_filename.'&NewsletterName='.$newsletter_name.'&NewExisting=New');

		if (tep_not_null($get_newsletter_return)) {
			$get_newsletter_return_array = explode("|", $get_newsletter_return);

			if ((int)$get_newsletter_return_array[0] == 1) {
				$newsletter_id = (int)$get_newsletter_return_array[1];
			} else {
				report_error('Function[get_imported_newsletter_id][NL:' . $newsletter_name . '] Error : ' . $get_newsletter_return_array[1]);
			}

			unset($get_newsletter_return_array);
		} else {
			report_error('Function[get_imported_newsletter_id][NL:' . $newsletter_name . '] No response');
		}

		unset($get_newsletter_return);

		return $newsletter_id;
	}

	function copy_dataset () {
		global $default_dataset_id, $dataset_name;

		$copy_status = 0;
		$copy_DS_return = gm_curl('Function=post_copy_dataset&DatasetID='.$default_dataset_id.'&NewDataset='.$dataset_name);

		if (tep_not_null($copy_DS_return)) {
			$copy_DS_return_array = explode("|", $copy_DS_return);
			$copy_status = (int)$copy_DS_return_array[0];

			if ($copy_status == 0) {	// 1 : success, 0 : error
				report_error('Function[copy_dataset][DS:' . $dataset_name . '] Error : ' . $copy_DS_return_array[1]);
			}

			unset($copy_DS_return_array);
		} else {
			report_error('Function[copy_dataset][DS:' . $dataset_name . '] No response');
		}

		unset($copy_DS_return);

		return $copy_status;
	}

	function create_dataset_CSV ($language_id) {
		global $max_records, $dataset_csv_location, $dataset_filename, $mailing_list_name, $aws_enabled, $aws_obj;

		$create_status = 0;
		$inviter_message_id_array = array();
		$records_array = array();
		$cvs_records_string = '';

		$records_select_sql = "	SELECT im.inviter_messages_id, im.message as c2,
									ii.inviter_imports_contact_email, ii.inviter_imports_contact_name as c1,
									c.customers_firstname, c.customers_lastname, c.customers_email_address
								FROM inviter_messages as im
								LEFT JOIN inviter_imports as ii
									ON im.inviter_imports_id = ii.inviter_imports_id
								LEFT JOIN ".TABLE_CUSTOMERS." as c
									ON ii.customer_id = c.customers_id
								WHERE im.inviter_messages_type = 'P' AND im.inviter_messages_language_id = ".$language_id."
								ORDER BY im.inviter_imports_id
								LIMIT ".$max_records;
		$records_select_result_sql = tep_db_query($records_select_sql);

		while ($records_select_row = tep_db_fetch_array($records_select_result_sql)) {
			$link_1 = $link_2 = $content = '';
			list($link_1, $link_2, $content) = explode("#~#", $records_select_row['c2']);

			$inviter_message_id_array[] = $records_select_row['inviter_messages_id'];
			$iName = $records_select_row['customers_firstname'].(tep_not_null($records_select_row['customers_lastname']) ? ' '.$records_select_row['customers_lastname'] : '' );

			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $records_select_row['inviter_imports_contact_email']) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $records_select_row['c1']) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $iName) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $records_select_row['customers_email_address']) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $link_1) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $link_2) . '"';
			$records_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $content) . '"';

			$cvs_records_string .= implode(",", $records_array) . "\n";
			unset($records_array);
		}

		if (count($inviter_message_id_array)) {
			if ($aws_enabled) {
				$aws_obj->set_filename($dataset_filename);
				$aws_obj->set_file_content($cvs_records_string);
				$create_status = $aws_obj->save_file() ? 1 : 0;
			} else if (($fdata = @fopen($dataset_csv_location, "w")) !== false) {
				$create_status = fwrite($fdata, $cvs_records_string);
				fclose($fdata) ;
			}

			if ($create_status > 0) {
				$update_data_sql = array('inviter_messages_type' => $mailing_list_name);
				tep_db_perform('inviter_messages', $update_data_sql, 'update', "inviter_messages_id IN (".implode(",", $inviter_message_id_array).")");
			} else {
				report_error('Function[create_dataset_CSV][DSF:' . $dataset_filename . '] Failed to write');
			}

			unset($inviter_message_id_array);
		}

		unset($cvs_records_string);

		return $create_status;
	}

	function send_dataset ($rerun_counter = 0, $import_status = 0) {
		global $dataset_id, $mailing_list_id, $import_from_url, $dataset_filename, $column_mapping, $dataset_name;

		$check_status_counter = 0;

		if ($import_status == 0) {
			// REMARK : define column [Col1 == CSV's first Column] eg. Col3 = 1 [CSV's 3rd column map with Graphic Mail's first additional column.
			$import_status_return = gm_curl('Function=post_import_dataset&EmailCol=1'.$column_mapping.'&DatasetID='.$dataset_id.'&MailingListID='.$mailing_list_id.'&ImportMode=1&MobileListID=0&FileUrl='.$import_from_url.$dataset_filename.'&IsCsv=true');

			if (tep_not_null($import_status_return)) {
				$import_status_array_return = explode("|", $import_status_return);
				$import_status = (int)$import_status_array_return[0];	//(0 = Failed, 1 = Done, 2 = Pending)

				if ($import_status == 0) {
					report_error('Function[send_dataset][DS:' . $dataset_name . '][Count:' . $rerun_counter . '] Error : ' . $import_status_array_return[1]);
				}

				unset($import_status_array_return);
			} else {
				report_error('Function[send_dataset][DS:' . $dataset_name . '][Count:' . $rerun_counter . '] No response');
			}

			unset($import_status_return);
		}

		if ($import_status == 2) {
			do {
				$check_status_counter += 1;
				$import_status_array_return = get_importqueue_array($dataset_id, $mailing_list_id);

				if (count($import_status_array_return)) {
					//(0 = Pending, 1 = Done, 2 = Failed) change to (1 == 1, 0,2 == 2)
					$import_status = ((int)$import_status_array_return[0]['status'] == 1) ? 1 : 2;
					break;
				} else {
					sleep(2);
				}

				unset($import_status_array_return);
			} while ($check_status_counter < 3);
		}

		if ($import_status == 1) {
			return $import_status;
		} elseif ($rerun_counter < 3) {
			if ($import_status == 0) {
				send_dataset(++$rerun_counter);
			} else {
				send_dataset(++$rerun_counter, $import_status);
			}
		} else {
			report_error('Function[send_dataset][DS:' . $dataset_name . '][Count:' . $rerun_counter . '] Failed');
			return 0;
		}
	}

	function send_mail($mail_subject) {
		global $default_dataset_id, $dataset_id, $mailing_list_id, $newsletter_id, $sender_email, $sender_name, $mailing_list_name;

		$send_mail_status_id = 0;
		$var_sender_name = str_ireplace('GM'.$default_dataset_id.'.', 'GM'.$dataset_id.'.', $sender_name);
		$var_mail_subject = str_ireplace('GM'.$default_dataset_id.'.', 'GM'.$dataset_id.'.', $mail_subject);

		$send_mail_status_return = gm_curl('Function=post_sendmail&FromEmail='.$sender_email.'&FromName='.$var_sender_name.'&TextOnly=0&ReturnSendID=true&NewsletterID='.$newsletter_id.'&MailinglistID='.$mailing_list_id.'&Subject='.$var_mail_subject);

		if(tep_not_null($send_mail_status_return)) {
			$send_mail_status_return_array = explode("|", $send_mail_status_return);

			if ((int)$send_mail_status_return_array[0] == 1) {
				$send_mail_status_id =  $send_mail_status_return_array[1];
			} else {
				report_error('Function[send_mail][ML:' . $mailing_list_name . '] Error : ' . $send_mail_status_return_array[1]);
			}

			unset($send_mail_status_return_array);
		} else {
			report_error('Function[send_mail][ML:' . $mailing_list_name . '] No response');
		}

		unset($send_mail_status_return);

		return $send_mail_status_id;
	}

	function get_importqueue_array ($search_DS_id='', $search_ML_id='') {
		$return_array = array();
		$get_importqueue_return = gm_curl('Function=get_importqueue_dataset');

		if(tep_not_null($get_importqueue_return)) {
			$xml_array_obj = new ogm_xml_to_ary($get_importqueue_return, 'content');
			$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

			if (isset($xml_data_array['queue']['_c']['queueitem']['_c'])) {
				foreach ($xml_data_array['queue']['_c']['queueitem']['_c'] as $item => $value) {
					$temp_array[$item] = $value['_v'];
				}

				if (tep_not_null($search_DS_id) && tep_not_null($search_ML_id)) {
					if ($search_DS_id === (int)$temp_array['datasetid'] && $search_ML_id === (int)$temp_array['mailinglistid']) {
						$return_array[] = $temp_array;
						break;
					}
				} else {
					$return_array[] = $temp_array;
				}
				unset($temp_array);
			} else if (isset($xml_data_array['queue']['_c']['queueitem'])) {
				foreach ($xml_data_array['queue']['_c']['queueitem'] as $item_array) {
					foreach ($item_array['_c'] as $item => $value) {
						$temp_array[$item] = $value['_v'];
					}

					if (tep_not_null($search_DS_id) && tep_not_null($search_ML_id)) {
						if ($search_DS_id === (int)$temp_array['datasetid'] && $search_ML_id === (int)$temp_array['mailinglistid']) {
							$return_array[] = $temp_array;
							break;
						}
					} else {
						$return_array[] = $temp_array;
					}
					unset($temp_array);
				}
			}
			unset($xml_array_obj, $xml_data_array);
		}

		unset($get_importqueue_return);

		return $return_array;
	}

	function cron_job_process($type) {
		$cron_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AS overdue_process
								FROM ".TABLE_CRON_PROCESS_TRACK."
								WHERE cron_process_track_filename = 'cron_graphic_mail.php'";
		$cron_select_result_sql = tep_db_query($cron_select_sql);
		if ($cron_select_row = tep_db_fetch_array($cron_select_result_sql)) {
			if ($type == 'start' && $cron_select_row['cron_process_track_in_action'] == '0') {
				$update_data_sql = array(	'cron_process_track_in_action' => 1,
											'cron_process_track_start_date' => 'now()',
											'cron_process_track_failed_attempt' => 0);
				tep_db_perform(TABLE_CRON_PROCESS_TRACK, $update_data_sql, 'update', "cron_process_track_filename = 'cron_graphic_mail.php'");

				return true;
			} elseif ($type == 'start_next') {
				if ($cron_select_row['cron_process_track_in_action'] == '1') {
					$update_data_sql = array('cron_process_track_start_date' => 'now()');
					tep_db_perform(TABLE_CRON_PROCESS_TRACK, $update_data_sql, 'update', "cron_process_track_filename = 'cron_graphic_mail.php'");

					return true;
				} else {
					return false;
				}
			} elseif ($type == 'end') {
				$update_data_sql = array(	'cron_process_track_in_action' => 0,
											'cron_process_track_start_date' => 'now()',
											'cron_process_track_failed_attempt' => 0);
				tep_db_perform(TABLE_CRON_PROCESS_TRACK, $update_data_sql, 'update', "cron_process_track_filename = 'cron_graphic_mail.php'");

				return true;
			} else {
				if ($cron_select_row['overdue_process'] < 5) {
					$cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
														SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
														WHERE cron_process_track_filename = 'cron_graphic_mail.php'";
					tep_db_query($cron_process_attempt_update_sql);
				} else {
					mail("<EMAIL>", "[OFFGAMERS] Cronjob Failed", 'Graphic Mail cronjob failed at ' . date("Y-m-d H:i:s"),
				     	"From: <EMAIL>\r\n" .
				     	"X-Mailer: PHP/" . phpversion());
				}

				return false;
			}
		} else {
			return false;
		}
	}

	function report_error ($message) {
            //
	}
?>