<?
/*
  $Id: cron_group_permission_status.php,v 1.3 2009/06/09 07:56:00 boonhock Exp $
*/
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'mail/htmlMimeMail5/htmlMimeMail.php');

tep_set_time_limit(0);

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$email[] = '"Wei Chen"<<EMAIL>>';

$email_subject = 'Group Permission Status [' . date("Y-m-d") . ']';

$languages_id = 1;
$admin_files_arr = array();

$get_admin_files_categories_select_sql = "	SELECT admin_files_id, admin_files_name, admin_groups_id 
											FROM " . TABLE_ADMIN_FILES . " 
											WHERE admin_files_to_boxes = '0' ORDER BY admin_files_name";
									
$get_admin_files_categories_result_sql = tep_db_query($get_admin_files_categories_select_sql, 'read_db_link');
while ($get_admin_files_categories_row = tep_db_fetch_array($get_admin_files_categories_result_sql)) {
	$admin_files_arr[] = array("files_id" => $get_admin_files_categories_row['admin_files_id'], 
								"files_name" => $get_admin_files_categories_row['admin_files_name'], 
								"groups_id" => $get_admin_files_categories_row['admin_groups_id'], 
								"files_level" => "1");
	
	$get_admin_files_select_sql = "	SELECT admin_files_id, admin_files_name, admin_groups_id 
									FROM " . TABLE_ADMIN_FILES . " 
									WHERE admin_files_to_boxes = '" . $get_admin_files_categories_row['admin_files_id'] . "' 
									ORDER BY admin_files_name";
							
	$get_admin_files_result_sql = tep_db_query($get_admin_files_select_sql, 'read_db_link');
	
	while ($get_admin_files_row = tep_db_fetch_array($get_admin_files_result_sql)) {
		$admin_files_arr[] = array("files_id" => $get_admin_files_row['admin_files_id'], 
									"files_name" => $get_admin_files_row['admin_files_name'], 
									"groups_id" => $get_admin_files_row['admin_groups_id'], 
									"files_level" => "2");
		$get_admin_files_action_select_sql = "	SELECT admin_files_actions_id, admin_files_actions_name, admin_groups_id 
												FROM " . TABLE_ADMIN_FILES_ACTIONS . " 
												WHERE admin_files_id = '" . $get_admin_files_row['admin_files_id'] . "' 
												ORDER BY admin_files_sort_order";
								
		$get_admin_files_action_result_sql = tep_db_query($get_admin_files_action_select_sql, 'read_db_link');
		$get_admin_files_action_num = tep_db_num_rows($get_admin_files_action_result_sql);
		if ($get_admin_files_action_num > 0) {
			while ($get_admin_files_action_row = tep_db_fetch_array($get_admin_files_action_result_sql)) {
				$admin_files_arr[] = array("files_id" => $get_admin_files_action_row['admin_files_actions_id'], 
											"files_name" => $get_admin_files_action_row['admin_files_actions_name'], 
											"groups_id" => $get_admin_files_action_row['admin_groups_id'], 
											"files_level" => "3");	
			}
		}
	}
}

if (sizeof($admin_files_arr) > 0) {
	$group_info_arr = cron_get_group_info();
	
	$export_csv_data = cron_generate_csv($group_info_arr, $admin_files_arr);
	if (tep_not_null($export_csv_data)) {
		cron_send_csv_email($email, $email_subject, $export_csv_data);
	}
}

function cron_send_csv_email ($email, $email_subject, $export_csv_data) {
	$mail = new htmlMimeMail();
	$mail->setSubject($email_subject);
	$attachment = $export_csv_data;
	
	$today_date = date("Ymd");
	
	$mail->addAttachment($attachment, 'group_permission_'.$today_date.'.csv', 'text/csv');
	$mail->setFrom(STORE_OWNER_EMAIL_ADDRESS);
	
	if (EMAIL_TRANSPORT == 'smtp') {
		$result = $mail->send($email, 'smtp');
	} else {
		$result = $mail->send($email);
	}
}

function cron_get_group_info () {
	$group_info_arr = array();
	$get_group_info_select_sql = "	SELECT admin_groups_id, admin_groups_name 
								FROM " . TABLE_ADMIN_GROUPS . " ORDER BY admin_groups_name";
								
	$get_group_info_result_sql = tep_db_query($get_group_info_select_sql, 'read_db_link');
	
	while ($get_group_info_row = tep_db_fetch_array($get_group_info_result_sql)) {
		$group_info_arr[] = array("groups_id" => $get_group_info_row['admin_groups_id'], "groups_name" => $get_group_info_row['admin_groups_name'],);
	}
	
	return $group_info_arr;
}

function cron_generate_csv ($group_info_arr, $admin_files_arr) {
	$export_csv_data = '';
	$export_heading_csv = array();
	$groups_name_arr = array();
	$export_group_csv_data = array();
	$export_admin_file_csv_data = array();
	$group_id_arr = array();
	$merged_csv_data_rows = array();
	$generate_csv_data = array();
	
	$export_heading_csv[] = 'Permission / Group name';
	
	foreach ($group_info_arr as $group_info) {
		$groups_name_arr[] = $group_info['groups_name'];
	}
	$export_group_csv_data = array_merge($export_heading_csv, $groups_name_arr);
	
	foreach ($admin_files_arr as $admin_arr_id => $admin_files) {
		$group_id_arr = explode("," ,$admin_files['groups_id']);

		switch ($admin_files['files_level']) {
			case '1':
				$export_admin_file_csv_data[$admin_arr_id][] = filter_csv($admin_files['files_name']);
			break;
			
			case '2':
				$export_admin_file_csv_data[$admin_arr_id][] = str_repeat(" ", 5) . filter_csv($admin_files['files_name']);
			break;
			
			case '3':
				$export_admin_file_csv_data[$admin_arr_id][] = str_repeat(" ", 10) . filter_csv($admin_files['files_name']);
			break;
		}
		
		foreach ($group_info_arr as $group_info) {
			if (in_array($group_info['groups_id'], $group_id_arr)) {
				$export_admin_file_csv_data[$admin_arr_id][] = '1';
			} else {
				$export_admin_file_csv_data[$admin_arr_id][] = '0';
			}
		}
		unset($group_id_arr);
	}
	
	$merged_csv_data_rows = array_merge(array($export_group_csv_data), $export_admin_file_csv_data);
	
	foreach ($merged_csv_data_rows as $csv_data) {
		$export_csv_data .= implode(',', $csv_data) . "\n";
	}
	
	return $export_csv_data;
}

function filter_csv ($csv_data) {
	$csv_data = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $csv_data) . '"';
	return $csv_data;
}


?>