<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_set_time_limit(0);

$languages_id = 1;
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = 'cron_hla_account_reservation.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=1, 
                                        cron_process_track_start_date=now(),
                                        cron_process_track_failed_attempt=0 
                                    WHERE cron_process_track_filename = 'cron_hla_account_reservation.php'";
        tep_db_query($cron_process_update_sql);

        $added_by = 'system';

        $cron_hla_select_sql = "SELECT ch.products_hla_id, ph.products_hla_reserve_id 
                                FROM " . TABLE_CRON_HLA . " AS ch 
                                INNER JOIN " . TABLE_PRODUCTS_HLA . " AS ph
                                ON ch.products_hla_id = ph.products_hla_id";
        $cron_hla_result_sql = tep_db_query($cron_hla_select_sql);
        while ($cron_hla_row = tep_db_fetch_array($cron_hla_result_sql)) {
            if (!tep_not_null($cron_hla_row['products_hla_reserve_id'])) { // HLA products does not has reserveID
                $status = 0;
                $message = '';

                $products_supplier_select_sql = "SELECT ps.supplier_code, ps.reserve_account_api 
                                                FROM " . TABLE_PRODUCTS_SUPPLIER . " AS ps 
                                                INNER JOIN " . TABLE_PRODUCTS_HLA . " AS ph 
                                                    ON ps.supplier_id = ph.seller_id 
                                                WHERE ph.products_hla_id = '" . $cron_hla_row['products_hla_id'] . "'";
                $products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
                if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
                    if ($products_supplier_row['reserve_account_api'] == 1) { // Product Supplier Reservation API is Active
                        if (file_exists(DIR_WS_MODULES_HLA . $products_supplier_row['supplier_code'] . '/api.php')) {
                            include_once(DIR_WS_MODULES_HLA . $products_supplier_row['supplier_code'] . '/api.php');

                            $api = new api($cron_hla_row['products_hla_id'], $added_by);
                            $api->account_reservation();
                        } else {
                            $products_hla_log_sql_data = array('products_hla_id' => $cron_hla_row['products_hla_id'],
                                'log_date' => 'now()',
                                'action' => 'Reserve Account API',
                                'status' => 0,
                                'message' => 'API script not found',
                                'added_by' => $added_by
                            );
                            tep_db_perform(TABLE_PRODUCTS_HLA_LOG, $products_hla_log_sql_data);
                        }
                    }
                }
            }

            $cron_hla_delete_sql = "DELETE FROM " . TABLE_CRON_HLA . " WHERE products_hla_id = '" . $cron_hla_row['products_hla_id'] . "'";
            tep_db_query($cron_hla_delete_sql);
        }

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=0 
                                    WHERE cron_process_track_filename = 'cron_hla_account_reservation.php'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                    WHERE cron_process_track_filename = 'cron_hla_account_reservation.php'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'HLA Account Reservation cronjob failed at ' . date("Y-m-d H:i:s");
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}
?>