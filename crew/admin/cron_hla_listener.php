<?php

# listen to cron_down_hla_rss.sh and cron_download_hla_profile.sh
# handle request to retrieve data from MySQL or server config

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'products_supplier.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

$cron_mail_to = '<EMAIL>, <EMAIL>, <EMAIL>';

$cron_mail_subject = array( 'cron_download_hla_rss' => 'Download HLA RSS',
							'cron_download_hla_profile' => 'Download HLA Profile' );

if (tep_not_null($_SERVER['argv'][1])) {
	if (preg_match('/:/', $_SERVER['argv'][1])) {
		list($action, $value) = explode(':', $_SERVER['argv'][1]);
	} else {
		$action = $_SERVER['argv'][1];
	}

	switch ($action) {
		case 'DIR_FS_IMAGES_DEST':
			echo DIR_FS_HLA;
			break;

		case 'cron_status':
			$cron_process_track_in_action = 1;

			if (tep_not_null($value)) {
				$cron_process_datetime = date("Y-m-d H:i:s");	// Set the time for this cron process

				$cron_select_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process
									FROM " . TABLE_CRON_PROCESS_TRACK . "
									WHERE cron_process_track_filename = '" . $value . "'";
				$cron_result_sql = tep_db_query($cron_select_sql);
				if ($cron_row = tep_db_fetch_array($cron_result_sql)) {
					if ($cron_row['cron_process_track_in_action'] == '0') {
						$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
													SET cron_process_track_in_action = 1,
														cron_process_track_start_date = now(),
														cron_process_track_failed_attempt = 0
													WHERE cron_process_track_filename = '" . $value . "'";
						tep_db_query($cron_process_update_sql);
					} else {
						// Check if previous cron job has overdue / something bad happened
						if ($cron_row['overdue_process'] == '1') {
							if ($cron_row['overdue_process'] < 5) {
								$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
															SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
															WHERE cron_process_track_filename = '" . $value . "'";
								tep_db_query($cron_process_update_sql);
							} else {
								list($filename, ) = explode('.', $value);
								mail($cron_mail_to, "[OFFGAMERS] Cronjob Failed", $cron_mail_subject[$filename] . ' cronjob failed at ' . $cron_process_datetime,
								     "From: <EMAIL>\r\n" .
								     "X-Mailer: PHP/" . phpversion());
							}
						}
					}
					$cron_process_track_in_action = $cron_row['cron_process_track_in_action'];
				}
			}

			echo $cron_process_track_in_action;
			break;

		case 'cron_unlock':
			// Release cron process "LOCK"
			$unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
										SET cron_process_track_in_action = 0
										WHERE cron_process_track_filename = '" . $value . "'";
			tep_db_query($unlock_cron_process_sql);
			break;

		case 'send_alert_mail':
			if (file_exists($value)) {
				$mail_content = file_get_contents($value);
				mail($cron_mail_to, "[OFFGAMERS] Cronjob Alert", $mail_content,
					"From: <EMAIL>\r\n" .
					"X-Mailer: PHP/" . phpversion());
			}
			break;

		case 'supplier_status':
			$products_rss_links = '';
			$psInfo = new products_supplier($value);

			if (tep_not_null($psInfo->products_supplier) && ($psInfo->products_supplier['supplier_status'] == 1)) {
				$psInfo->get_products_rss_link();

				if (tep_not_null($psInfo->products_rss_link)) {
					foreach ($psInfo->products_rss_link as $products_id => $products_info_arr) {
						foreach ($products_info_arr as $cnt => $products_info) {
							$products_rss_links .= $products_id . ':~:' . $products_info['url'] . ' ';
						}
					}
					echo $products_rss_links;
				}
			}
			break;
	}
}
?>