<?php

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

tep_set_time_limit(0);

$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$fr_date = date("Y-m-d 00:00:00", strtotime("-1 day"));
$to_date = date("Y-m-d 00:00:00");
$next_date = "";

if (isset($_SERVER['argv'][1])) {
    if (tep_not_null($_SERVER['argv'][1])) {
        $fr_date = trim($_SERVER['argv'][1]) . " 00:00:00";

        if (isset($_SERVER['argv'][2]) && tep_not_null($_SERVER['argv'][2])) {
            $to_date = trim($_SERVER['argv'][2]) . " 00:00:00";
        }
    }
}

$header[] = "";
$row_new[] = "New Customers";
$row_old[] = "Existing Customers";

// exclude OGC product ID
$product = [];
$sql = 'SELECT products_id FROM products WHERE products_cat_id_path LIKE "%16346_%"';
$res = tep_db_query($sql, 'read_db_link');
while ($row = tep_db_fetch_array($res)) {
    $product[] = $row["products_id"];
}

// exclude Store Credit top-up
$sql = 'SELECT products_id FROM products WHERE products_cat_id_path LIKE "%4770_%"';
$res = tep_db_query($sql, 'read_db_link');
while ($row = tep_db_fetch_array($res)) {
    $product[] = $row["products_id"];
}

do {
    $next_date = date("Y-m-d 00:00:00", strtotime($fr_date . " +1 day"));
    $sign_date = date("Y-m-01 00:00:00", strtotime($fr_date . " -2 month"));

    $new_total = 0;
    $old_total = 0;
    $header[] = date("Y-m-d", strtotime($fr_date));

    $sql = "SELECT DISTINCT(o.customers_id) as customers_id
            FROM orders AS o
            INNER JOIN orders_extra_info AS oei
                ON oei.orders_id = o.orders_id
                AND oei.orders_extra_info_key = 'site_id'
            WHERE o.date_purchased >= '" . $fr_date . "'
                AND o.date_purchased < '" . $next_date . "'
                AND o.orders_status = 3 
                AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)";
    $res = tep_db_query($sql, 'read_db_link');
    while ($row = tep_db_fetch_array($res)) {
        // new customer
        $_sql = "SELECT ci.customers_info_id
                FROM customers_info AS ci
                WHERE ci.customers_info_id = " . $row["customers_id"] . "
                    AND ci.customers_info_date_account_created >= '" . $sign_date . "'
                    AND ci.customers_info_date_account_created < '" . $next_date . "'";
        $_res = tep_db_query($_sql, 'read_db_link');
        $new_customer = tep_db_num_rows($_res) ? true : false;

        $_sql = "SELECT o.orders_id, op.products_good_delivered_price
                FROM orders AS o
                INNER JOIN orders_products AS op
                    ON op.orders_id = o.orders_id
                INNER JOIN orders_extra_info AS oei
                    ON oei.orders_id = o.orders_id
                    AND oei.orders_extra_info_key = 'site_id'
                WHERE o.customers_id = " . $row["customers_id"] . "
                    AND o.date_purchased >= '" . $fr_date . "'
                    AND o.date_purchased < '" . $next_date . "'
                    AND o.orders_status = 3
                    AND op.products_bundle_id = 0
                    AND op.products_id NOT IN (" . implode(",", $product) . ")
                    AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)";
        $_res = tep_db_query($_sql, 'read_db_link');
        while ($_row = tep_db_fetch_array($_res)) {
            if ($new_customer) {
                $new_total += $_row["products_good_delivered_price"];
            } else {
                $old_total += $_row["products_good_delivered_price"];
            }
        }
    }

    $row_new[] = $new_total;
    $row_old[] = $old_total;

    $fr_date = $next_date;
} while ($next_date != $to_date);

if (count($header) > 1) {
    // upload to s3
    $s3_bucket = 'BUCKET_DATA';
    $s3_filepath = 'report/';
    $filename = "new_customer_sales_" . date('YmdHis') . ".csv";

    $aws_obj = new ogm_amazon_ws();
    $aws_obj->set_bucket_key($s3_bucket);
    $aws_obj->set_storage('STORAGE_STANDARD');
    $aws_obj->set_filepath($s3_filepath);

    if ($aws_obj->is_aws_s3_enabled()) {
        $file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $filename;
    } else {
        echo "Fail to upload to S3";
        exit();
    }

    $fp = fopen($file_path, "w+");
    fwrite($fp, implode("|", $header) . "\n");
    fwrite($fp, implode("|", $row_new) . "\n");
    fwrite($fp, implode("|", $row_old) . "\n");
    fclose($fp);

    if ($aws_obj->is_aws_s3_enabled()) {
        $aws_obj->set_file(array('tmp_name' => $file_path));
        $aws_obj->set_filename($filename);
        $aws_obj->save_file();
    }
    @unlink($file_path);
}
?>