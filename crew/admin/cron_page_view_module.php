<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_CLASSES . "page_view_module.php");

ini_set("memory_limit", "550M");
tep_set_time_limit(0);

// Prevent direct access from Web URL
if (!isset($_SERVER['argv'])) {
	exit;
} else {
	$permission_code = $_SERVER['argv'][1];
}

tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

if ($permission_code == 'kj2601ed%337c7c') {
	ob_start();
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
	<title>Page View Module - <?=date("Y/m/d H:i")?></title>
	<base href="<?=HTTP_SERVER . DIR_WS_ADMIN ?>"/>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="pageHeading">
							Page View Module - Current List backup (<?=date("Y/m/d H:i")?>)
<?
$page_view_module_obj = new page_view_module();
$tags_names_array = $page_view_module_obj->get_tags_names();

?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
            						<td><?=tep_draw_separator('	pixel_trans.gif', '1', '10')?></td>
            					</tr>
          						<tr>
            						<td class="pageHeading">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
            								<tr>
			            						<td>
			            							<table border="0" width="100%" cellspacing="2" cellpadding="0">
														<tr>
															
															
					                						<td class="reportBoxHeading" width="120px" align="center">IP Address</td>
					                						<td class="reportBoxHeading" width="100px" align="center">NS Lookup</td>
					                						<td class="reportBoxHeading" width="*%" align="center">Remark</td>
<? foreach ($tags_names_array as $tags_name => $tags_text) {?>
															<td class="reportBoxHeading" width="70px" align="center"><?=$tags_text?></td>
<? } ?>
															<td class="reportBoxHeading" width="130px" align="center">Last Visit</td>
															<td class="reportBoxHeading" width="170px" align="center">Visitor</td>
															<td class="reportBoxHeading" width="170px" align="center">Last blocked on</td>
						            					</tr>
<?
	$page_view_ip_list_select_sql = "	SELECT pvil.page_view_ip_list_id, pvil.page_view_ip_list_ip, pvil.page_view_ip_list_ip_subnet, 
											DATE_FORMAT(pvil.page_view_ip_list_last_update, '%Y-%m-%d %H:%i') as page_view_ip_list_last_update, 
											pvil.page_view_ip_list_last_url, pvil.page_view_ip_list_mode, pvil.page_view_ip_list_last_blocked, 
											pvil.page_view_ip_list_list, pvil.page_view_ip_list_remark 
										FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil 
										WHERE page_view_ip_list_list = 'c' 
										ORDER BY page_view_ip_list_ip";
	$page_view_ip_list_result_sql = tep_db_query($page_view_ip_list_select_sql, 'read_db_link');
	$page_view_ip_list_array = array();
	while ($page_view_ip_list_row = tep_db_fetch_array($page_view_ip_list_result_sql)) {
		$page_view_ip_list_array[$page_view_ip_list_row['page_view_ip_list_id']] = array(	'page_view_ip_list_ip'=> $page_view_ip_list_row['page_view_ip_list_ip'],
																							'page_view_ip_list_ip_subnet'=> $page_view_ip_list_row['page_view_ip_list_ip_subnet'],
																							'page_view_ip_list_last_blocked'=> $page_view_ip_list_row['page_view_ip_list_last_blocked'],
																							'page_view_ip_list_last_update'=> $page_view_ip_list_row['page_view_ip_list_last_update'],
																							'page_view_ip_list_last_url'=> $page_view_ip_list_row['page_view_ip_list_last_url'],
																							'page_view_ip_list_mode'=> $page_view_ip_list_row['page_view_ip_list_mode'],
																							'page_view_ip_list_list'=> $page_view_ip_list_row['page_view_ip_list_list'],
																							'page_view_ip_list_remark'=> $page_view_ip_list_row['page_view_ip_list_remark']);
	}
	if (count($page_view_ip_list_array)) {
		$page_view_ip_list_id_str = array_keys($page_view_ip_list_array);
		$page_view_ip_list_id_str = implode("','",$page_view_ip_list_id_str);
		
		$delete_page_view_ip_sql = "	DELETE FROM " . TABLE_PAGE_VIEW_IP_LIST . "
										WHERE page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')";
		tep_db_query($delete_page_view_ip_sql);
        
		$ip_tags_stats_select_sql = "	SELECT page_view_ip_list_id, script_tags_name, ip_tags_stats_counter, 
											DATE_FORMAT(ip_tags_stats_last_visit, '%Y-%m-%d %H:%i') as ip_tags_stats_last_visit
										FROM " . TABLE_IP_TAGS_STATS . " 
										WHERE page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')";
		$ip_tags_stats_result_sql = tep_db_query($ip_tags_stats_select_sql, 'read_db_link');
		$ip_tags_stats_array = array();
		while ($ip_tags_stats_row = tep_db_fetch_array($ip_tags_stats_result_sql)) {
			$ip_tags_stats_array[$ip_tags_stats_row['page_view_ip_list_id']][$ip_tags_stats_row['script_tags_name']] = array(	'ip_tags_stats_counter'=> $ip_tags_stats_row['ip_tags_stats_counter'],
																																'ip_tags_stats_last_visit'=> $ip_tags_stats_row['ip_tags_stats_last_visit']);
		}
		
		$ip_history_select_sql = "	SELECT c.customers_email_address, ilh.page_view_ip_list_id, ilh.customers_id  
									FROM " . TABLE_IP_LIST_HISTORY . " as ilh 
									LEFT JOIN " . TABLE_CUSTOMERS . " as c
										ON c.customers_id = ilh.customers_id
									WHERE ilh.page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')
									GROUP BY ilh.page_view_ip_list_id
									ORDER BY ilh.ip_list_history_datatime DESC";
		$ip_history_result_sql = tep_db_query($ip_history_select_sql, 'read_db_link');
		$ip_history_array = array();
		while ($ip_history_row = tep_db_fetch_array($ip_history_result_sql)) {
			$ip_history_array[$ip_history_row['page_view_ip_list_id']] = $ip_history_row['customers_email_address'];
		}
		
		$ip_tags_stats_sql = "	DELETE FROM " . TABLE_IP_TAGS_STATS . "
								WHERE page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')";
		tep_db_query($ip_tags_stats_sql);
		
		$ip_list_history_select_sql = "	SELECT ilh.*, c.customers_email_address 
										FROM " . TABLE_IP_LIST_HISTORY . " as ilh 
										LEFT JOIN " . TABLE_CUSTOMERS . " as c 
											ON c.customers_id = ilh.customers_id 
										WHERE ilh.page_view_ip_list_id  IN ('".$page_view_ip_list_id_str."')";
		$ip_list_history_result_sql = tep_db_query($ip_list_history_select_sql, 'read_db_link');
		$ip_list_history_array = array();
		while ($ip_list_history_row = tep_db_fetch_array($ip_list_history_result_sql)) {
			$ip_list_history_array[$ip_list_history_row['page_view_ip_list_id']][$ip_list_history_row['ip_list_history_tags']][$ip_list_history_row['ip_list_history_id']] = $ip_list_history_row;
		}
		/* Try deleting one-by-one to resolve Table Locking issue for following INSERT query
		$ip_list_history_sql = "DELETE FROM " . TABLE_IP_LIST_HISTORY . "
								WHERE page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')";
		tep_db_query($ip_list_history_sql);
		*/
        unset($page_view_ip_list_id_str, $delete_page_view_ip_sql, $ip_tags_stats_sql, $ip_list_history_sql);
		$row_count = 0;
		foreach ($page_view_ip_list_array as $page_view_ip_list_id_loop => $page_view_ip_list_data_loop) {
            $ip_list_history_delete_sql = " DELETE FROM " . TABLE_IP_LIST_HISTORY . "
                                            WHERE page_view_ip_list_id = '".$page_view_ip_list_id_loop."'";
            tep_db_query($ip_list_history_delete_sql);
            
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			echo "										<tr id='main_".$row_count."' class='".$row_style."' onmouseover=\"showOverEffect(this, 'ordersListingRowOver', 'sub_".$row_count."##')\" onmouseout=\"showOutEffect(this, '".$row_style."', '".$row_count."##sub2_".$row_count."')\" onclick=\"showClicked(this, '".$row_style."', 'sub_".$row_count."##sub2_".$row_count."')\">
															<td class='dataTableContent'>&nbsp;".tep_show_ip($page_view_ip_list_data_loop['page_view_ip_list_ip'])."</td>";
            $linux_host_result_str = 'N/A';
            
			$remark_str = '';
			if (preg_match('/(.+)(\<br)/',nl2br($page_view_ip_list_data_loop['page_view_ip_list_remark']), $matched_str)) {
				$remark_str = $matched_str[1] . "...[<a class='jq_ogm_remark' onclick='show_information(\"div_remark_".$page_view_ip_list_id_loop."\")' style='cursor:pointer;text-decoration:underline;'>more</a>]";
			} else if (strlen($page_view_ip_list_data_loop['page_view_ip_list_remark'])>50) {
				$remark_str = substr($page_view_ip_list_data_loop['page_view_ip_list_remark'],0,50)."...[<a class='jq_ogm_remark' onclick='show_information(\"div_remark_".$page_view_ip_list_id_loop."\")' style='cursor:pointer;text-decoration:underline;'>more</a>]";
			} else {
				$remark_str = $page_view_ip_list_data_loop['page_view_ip_list_remark'];
			}
			
			echo "											<td class='dataTableContent'>&nbsp;".$linux_host_result_str."</td>
															<td class='dataTableContent'>&nbsp;<a class='jq_ogm_remark' onclick='show_information(\"div_remark_".$page_view_ip_list_id_loop."\")' style='cursor:pointer;text-decoration:underline;'>".tep_image('images/icons/edit.gif', 'remark', 10, 10)."</a><span id='span_remark_".$page_view_ip_list_id_loop."'>".$remark_str."</span>
															<div id='div_remark_".$page_view_ip_list_id_loop."' style='border: 4px solid grey; padding: 5px;display:none;background: white none repeat scroll 0% 0%; position: absolute; top: 50px; left: 170px;'>
																<div>
																	<table width='100%'>
																		<tr>
																			<td class='pageHeading'>".$page_view_ip_list_data_loop['page_view_ip_list_ip']." - Remark:</td>
																			<td align='right'>
																				<a href='javascript:hide_information(\"div_remark_".$page_view_ip_list_id_loop."\")'>
																					<img src='images/cal_close_small.gif'/>
																				</a>
																			</td>
																		</tr>
																		<tr>
																			<td colspan='2'>
																				<textarea id='txt_remark' cols='150' rows='5' name='txt_remark'>".$page_view_ip_list_data_loop['page_view_ip_list_remark']."</textarea>
																			</td>
																		</tr>
																	</table>
																</div>
															</div>";
			foreach ($tags_names_array as $tags_name => $tags_text) {
				echo "											<div id='div_information_".$page_view_ip_list_id_loop."_".$tags_name."' class='div_information' style='padding:5px;display:none;position:absolute;top:50px;left:170px;background:white;border:4px solid grey;'>
																	<div style='width:100%;'>
																		<table width='100%'>
																			<tr>
																				<td class='pageHeading'>".$page_view_ip_list_data_loop['page_view_ip_list_ip']." - History:</td><td align='right'><a href='javascript:hide_information(\"div_information_".$page_view_ip_list_id_loop."_".$tags_name."\")'><img src='images/cal_close_small.gif'></a></td>
										   									</tr>
										   									<tr>
										   										<td colspan='2'>
										   											<table width='100%'>
										   												<tr class='reportBoxHeading'>
										   													<td>Date</td>
										   													<td>URL</td>
										   													<td>Customer</td>
										   													<td>Remark</td>
										   												</tr>";
				if (isset($ip_list_history_array[$page_view_ip_list_id_loop][$tags_name])) {
					foreach ($ip_list_history_array[$page_view_ip_list_id_loop][$tags_name] as $ip_list_history_id_loop => $ip_list_history_id_data) {
						echo "															<tr class='dataTableRow' onmouseover='this.className=\"dataTableRowOver\";this.style.cursor=\"hand\"' onmouseout='this.className=\"dataTableRow\"'>
																							<td class='dataTableContent'>".$ip_list_history_id_data['ip_list_history_datatime']."</td>
																							<td class='dataTableContent'>".$ip_list_history_id_data['scripts_name']."</td>
																							<td class='dataTableContent'>".(tep_not_null($ip_list_history_id_data['customers_email_address'])?$ip_list_history_id_data['customers_email_address']:'Anonymous')."</td>
																							<td class='dataTableContent'>".$ip_list_history_id_data['ip_list_history_remark']."</td>
																						</tr>";
					}
				}
				echo "																</table>
																				</td>
																			</tr>
																		</table>
																	</div>
																</div>";
				}
				echo "										</td>";
			foreach ($tags_names_array as $tags_name => $tags_text) {
				if (isset($ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_counter'])) {
					echo '									<td class="dataTableContent" align="center" title="Last Access: '.$ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_last_visit'].'"><a class="jq_ogm_tooltips" onclick="show_information(\'div_information_'.$page_view_ip_list_id_loop.'_'.$tags_name.'\')" style="text-decoration:underline;cursor:pointer;">'.$ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_counter']."</a></td>";
				} else {
					echo "									<td class='dataTableContent' align='center'>0</td>";
				}
			}
			echo "											<td class='dataTableContent' align='center'>".$page_view_ip_list_data_loop['page_view_ip_list_last_update']."</td>
															<td class='dataTableContent' align='center'>".(isset($ip_history_array[$page_view_ip_list_id_loop]) && tep_not_null($ip_history_array[$page_view_ip_list_id_loop])?$ip_history_array[$page_view_ip_list_id_loop]:'Anonymous')."</td>
															<td class='dataTableContent' align='center'>".(tep_not_null($page_view_ip_list_data_loop['page_view_ip_list_last_blocked']) && $page_view_ip_list_data_loop['page_view_ip_list_last_blocked'] != '0000-00-00 00:00:00'?$page_view_ip_list_data_loop['page_view_ip_list_last_blocked']:'&nbsp;')."</td>
														</tr>";
			$row_count++;
		}
		echo "											<tr>
															<td><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
														</tr>";
}
?>
						            				</table>
			            						</td>
			            					</tr>
			            				</table>
										<script>
											function showOverEffect(object, class_name, extra_row) {
												rowOverEffect(object, class_name);
												var rowObjArray = extra_row.split('##');
												for (var i = 0; i < rowObjArray.length; i++) {
													if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
														rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
													}
												}
											}
											
											function showOutEffect(object, class_name, extra_row) {
												rowOutEffect(object, class_name);
												var rowObjArray = extra_row.split('##');
										  		for (var i = 0; i < rowObjArray.length; i++) {
										  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
										  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
										  			}
										  		}
											}
											
											function showClicked(object, class_name, extra_row) {
												rowClicked(object, class_name);
												var rowObjArray = extra_row.split('##');
										  		for (var i = 0; i < rowObjArray.length; i++) {
										  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
										  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
										  			}
												}
											}	
											
											function hide_information(pass_id) {
												jQuery("#"+pass_id).hide();
											}
											
											function show_information(pass_id) {
												jQuery("#"+pass_id).css("top",((window.pageYOffset*1)+10) + "px");
												jQuery(".div_information").hide();
												jQuery("#"+pass_id).show();
											}
										</script>
			            			</td>
            					</tr>
            				</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</body>
</html>
<?
		$display_html = ob_get_contents();
		ob_end_clean();
        
		# upload to s3
        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

        $output_file_ext = '.html';
        $s3_bucket = 'BUCKET_DATA';
        $s3_filepath = 'page_view/';
        $output_file_prefix = 'page_view_';
        
        $report_filename = $output_file_prefix . date('YmdHis') . $output_file_ext;
        $zip_report_filename = $output_file_prefix . date('YmdHis') . '.zip';
        
        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key($s3_bucket);
        $aws_obj->set_storage('STORAGE_STANDARD');
        $aws_obj->set_filepath($s3_filepath);
        
        if ($aws_obj->is_aws_s3_enabled()) {
            $file_path = DIR_FS_DOCUMENT_ROOT . 'download/30days/pageview/' . $report_filename;
            $zip_file_path = DIR_FS_DOCUMENT_ROOT . 'download/30days/pageview/' . $zip_report_filename;
            
            $handle = fopen($file_path, 'w+');
            fwrite($handle, $display_html);
            fclose($handle);
            
            unset($display_html);
            // create object
            $zip = new ZipArchive();
            
            // open archive
            if ($zip->open($zip_file_path, ZIPARCHIVE::CREATE) !== TRUE) {
                die ("Could not open archive");
            }

            $zip->addFile($file_path, $report_filename) or die ("ERROR: Could not add file: $report_filename");
            
            // close and save archive
            $zip->close();
            
            @unlink($file_path);
            
            $aws_obj->set_file(array('tmp_name' => $zip_file_path));
            $aws_obj->set_filename($zip_report_filename);
            $aws_obj->save_file();
            
            @unlink($zip_file_path);
        }
	}
?>