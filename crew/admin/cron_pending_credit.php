<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_CLASSES . 'supplier_order.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'g2g_serverless.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// DEBUG SETTING - PLEASE REMOVE
// $_SERVER['argv'] = array(1, 10);

if (date("H") == 0 && (date("i") >= 0 && date("i") < 15)) {
    exit;
}

// Only check for price if the is records number been passed. Corresponding row number in price_check_slots table
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
    exit;
} else {
    $first_N_record = $_SERVER['argv'][1];
}

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

if (file_exists(DIR_WS_LANGUAGES . 'english/payment.php')) {
    include_once(DIR_WS_LANGUAGES . 'english/payment.php');
}

$currencies = new currencies();

$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

$cron_process_checking_select_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, cron_process_track_failed_attempt
                                     FROM " . TABLE_CRON_PROCESS_TRACK . "
                                     WHERE cron_process_track_filename = 'cron_pending_credit.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action = 1,
                                        cron_process_track_start_date = now(),
                                        cron_process_track_failed_attempt = 0
                                    WHERE cron_process_track_filename = 'cron_pending_credit.php'";
        tep_db_query($cron_process_update_sql);

        // Grab the first N records
        $pending_credit_select_sql = "SELECT *
                                    FROM " . TABLE_CRON_PENDING_CREDIT . "
                                    WHERE cron_pending_credit_trans_error <> 1
                                        AND cron_pending_credit_trans_completed_date < DATE_SUB(NOW(), INTERVAL CAST(cron_pending_credit_mature_period AS UNSIGNED) MINUTE)
                                    ORDER BY FIELD(cron_pending_credit_trans_type, 'PO') DESC, cron_pending_credit_trans_completed_date ASC
                                    LIMIT " . $first_N_record;
        $pending_credit_result_sql = tep_db_query($pending_credit_select_sql);
        while ($pending_credit_row = tep_db_fetch_array($pending_credit_result_sql)) {
            $process_success_flag = false;

            switch ($pending_credit_row['cron_pending_credit_trans_type']) {
                case 'S':
                    // Check the transaction status
                    $order_info_select_sql = "	SELECT supplier_order_lists_status, suppliers_id, supplier_order_lists_billing_status, currency, currency_value
  												FROM " . TABLE_SUPPLIER_ORDER_LISTS . "
  												WHERE supplier_order_lists_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                    $order_info_result_sql = tep_db_query($order_info_select_sql);
                    $order_info_row = tep_db_fetch_array($order_info_result_sql);

                    if ($order_info_row['supplier_order_lists_status'] != $pending_credit_row['cron_pending_credit_trans_status'] || $order_info_row['supplier_order_lists_billing_status'] == '1') {
                        $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
													SET cron_pending_credit_trans_error = 1
													WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
														AND cron_pending_credit_trans_id = '" . $pending_credit_row['cron_pending_credit_trans_id'] . "'";
                        tep_db_query($trans_error_update_sql);
                    } else {
                        // Update supplier_order_lists_billing_status
                        $trans_billing_update_sql = "	UPDATE " . TABLE_SUPPLIER_ORDER_LISTS . "
														SET supplier_order_lists_billing_status = 1
														WHERE supplier_order_lists_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                        tep_db_query($trans_billing_update_sql);

                        // Get payable amount
                        $payable_amount = supplier_order::get_order_total_payable_amount($pending_credit_row['cron_pending_credit_trans_id']);
                        $final_currency_payable_amount = $payable_amount * $order_info_row['currency_value'];

                        $credit_update_success_flag = tep_cron_credit_user_balance($order_info_row['suppliers_id'], 'supplier', $order_info_row['currency'], $final_currency_payable_amount, $pending_credit_row['cron_pending_credit_trans_type'], $pending_credit_row['cron_pending_credit_trans_id']);

                        // Insert order comment and update last modified time
                        if ($credit_update_success_flag) {
                            $trans_history_data_array = array('supplier_order_lists_id' => $pending_credit_row['cron_pending_credit_trans_id'],
                                'supplier_order_lists_status' => 0,
                                'date_added' => 'now()',
                                'supplier_notified' => 0,
                                'comments' => 'Credited to account balance',
                                'changed_by' => 'system'
                            );
                            tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $trans_history_data_array);

                            $last_modified_update_sql = "	UPDATE " . TABLE_SUPPLIER_ORDER_LISTS . "
															SET supplier_order_lists_last_modified = now()
															WHERE supplier_order_lists_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                            tep_db_query($last_modified_update_sql);

                            $process_success_flag = true;
                        }
                    }

                    break;
                case 'B':
                    $buyback_request_group_id = tep_db_input($pending_credit_row['cron_pending_credit_trans_id']);

                    $vip_bonus_percentage = 0; // 5% Bonus
                    // Check the transaction status
                    $order_info_select_sql = "	SELECT buyback_status_id, customers_id, buyback_request_group_user_type, buyback_request_group_site_id, buyback_request_group_billing_status, currency, currency_value, buyback_request_order_type
  												FROM " . TABLE_BUYBACK_REQUEST_GROUP . "
  												WHERE buyback_request_group_id = '" . $buyback_request_group_id . "'";
                    $order_info_result_sql = tep_db_query($order_info_select_sql);
                    $order_info_row = tep_db_fetch_array($order_info_result_sql);

                    if ($order_info_row['buyback_status_id'] != $pending_credit_row['cron_pending_credit_trans_status'] || $order_info_row['buyback_request_group_billing_status'] == '1') {
                        $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
													SET cron_pending_credit_trans_error = 1
													WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
														AND cron_pending_credit_trans_id = '" . $buyback_request_group_id . "'";
                        tep_db_query($trans_error_update_sql);
                    } else {
                        // No errors. Update buyback_request_group_billing_status
                        $trans_billing_update_sql = "	UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
														SET buyback_request_group_billing_status = 1
														WHERE buyback_request_group_id = '" . $buyback_request_group_id . "'";
                        tep_db_query($trans_billing_update_sql);

                        // Get payable amount
                        $payable_amount = 0;
                        $buyback_products_select_sql = "SELECT buyback_unit_price, buyback_quantity_received, buyback_quantity_confirmed, buyback_request_quantity
														FROM " . TABLE_BUYBACK_REQUEST . "
														WHERE buyback_request_group_id = '" . $buyback_request_group_id . "'";
                        $buyback_products_result_sql = tep_db_query($buyback_products_select_sql);
                        while ($buyback_products_row = tep_db_fetch_array($buyback_products_result_sql)) {
                            // Wei Chen
                            $unit_price = $buyback_products_row['buyback_unit_price'];

                            if ($buyback_products_row['buyback_quantity_confirmed'] == 0 && $buyback_products_row['buyback_quantity_received'] > 0) { // If CN buyback is recevied qty from cancel status
                                if ($order_info_row['buyback_request_group_site_id'] == 0) {
                                    $buyback_products_row['buyback_quantity_confirmed'] = $buyback_products_row['buyback_request_quantity'];
                                } else {
                                    $buyback_products_row['buyback_quantity_confirmed'] = $buyback_products_row['buyback_request_quantity'] * 1.1;
                                }
                            }

                            $received_quantity = (int) $buyback_products_row['buyback_quantity_received'];
                            $actual_amount = $unit_price * ($received_quantity > $buyback_products_row['buyback_quantity_confirmed'] ? $buyback_products_row['buyback_quantity_confirmed'] : $received_quantity);

                            $payable_amount += $actual_amount;
                        }

                        $final_currency_payable_amount = $payable_amount * $order_info_row['currency_value'];
                        $credit_update_success_flag = tep_cron_credit_user_balance($order_info_row['customers_id'], 'customers', $order_info_row['currency'], $final_currency_payable_amount, $pending_credit_row['cron_pending_credit_trans_type'], $buyback_request_group_id);

                        // Insert order comment
                        if ($credit_update_success_flag) {
                            $trans_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
                                'buyback_status_id' => 0,
                                'date_added' => 'now()',
                                'customer_notified' => 0,
                                'comments' => 'Credited to account balance',
                                'changed_by' => 'system'
                            );
                            tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $trans_history_data_array);

                            $last_modified_update_sql = "	UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
															SET buyback_request_group_last_modified = now()
															WHERE buyback_request_group_id = '" . $buyback_request_group_id . "'";
                            tep_db_query($last_modified_update_sql);

                            // Bonus for VIPM buyback
                            if ($order_info_row['buyback_request_order_type'] == '1') { // VIP Order
                                if ($vip_bonus_percentage > 0) {
                                    $credit_store_credit_success_flag = tep_cron_credit_store_credit($order_info_row['customers_id'], $payable_amount * ($vip_bonus_percentage / 100), $pending_credit_row['cron_pending_credit_trans_type'], $buyback_request_group_id);
                                }
                            }

                            //Payable amount successfully credited to account balance.
                            $process_success_flag = true;
                        }
                    }

                    break;
                case 'PWL':
                    // Check the transaction status
                    $orders_product_id = $pending_credit_row['cron_pending_credit_trans_id'];

                    $pwl_orders_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price,
													sta.suppliers_id, sta.supplier_tasks_status, sta.supplier_tasks_billing_status, ocp.orders_custom_products_number, o.orders_id, op.products_id, o.currency, o.currency_value
												FROM " . TABLE_ORDERS . " AS o
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
													ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1)
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info')
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta
													ON (op.orders_products_id=sta.orders_products_id)
												WHERE sta.orders_products_id='" . tep_db_input($orders_product_id) . "'";
                    $pwl_orders_result_sql = tep_db_query($pwl_orders_select_sql);
                    $pwl_orders_row = tep_db_fetch_array($pwl_orders_result_sql);

                    if ($pwl_orders_row['supplier_tasks_status'] != $pending_credit_row['cron_pending_credit_trans_status'] || $pwl_orders_row['supplier_tasks_billing_status'] == '1' || $pwl_orders_row['suppliers_id'] <= 0) {
                        $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
													SET cron_pending_credit_trans_error = 1
													WHERE cron_pending_credit_trans_type = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_type']) . "'
														AND cron_pending_credit_trans_id = '" . tep_db_input($orders_product_id) . "'";
                        tep_db_query($trans_error_update_sql);
                    } else {
                        $readable_trans_id = $pwl_orders_row['orders_id'] . '-' . $pwl_orders_row['orders_custom_products_number'];

                        // Update supplier_tasks_billing_status
                        $trans_billing_update_sql = "	UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . "
														SET supplier_tasks_billing_status = 1
														WHERE orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                        tep_db_query($trans_billing_update_sql);

                        // Get payable amount
                        $payable_amount = (double) $pwl_orders_row['payable_price'];
                        //$final_currency_payable_amount = $payable_amount * $pwl_orders_row['currency_value'];
                        // Use Order ID - Seq No# as the trans_id
                        $credit_update_success_flag = tep_cron_credit_user_balance($pwl_orders_row['suppliers_id'], 'supplier', DEFAULT_CURRENCY, $payable_amount, $pending_credit_row['cron_pending_credit_trans_type'], $readable_trans_id);

                        // Insert order comment and update last modified time
                        if ($credit_update_success_flag) {
                            $trans_history_data_array = array('orders_products_id' => $orders_product_id,
                                'supplier_tasks_status' => 'NULL',
                                'date_added' => 'now()',
                                'comments' => 'Credited to account balance',
                                'changed_by' => 'system',
                                'user_role' => 'admin',
                                'notify_recipient' => 0,
                                'supplier_tasks_allocation_history_show' => 0
                            );
                            tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $trans_history_data_array);

                            $process_success_flag = true;
                        }
                    }

                    break;
                case 'A':
                    break;
                case 'AFF':
                    // Approve Affiliate Commission
                    $order_info_select_sql = "	SELECT orders_id, orders_status
												FROM " . TABLE_ORDERS . "
												WHERE orders_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                    $order_info_result_sql = tep_db_query($order_info_select_sql);
                    $order_info_row = tep_db_fetch_array($order_info_result_sql);

                    if ($order_info_row['orders_status'] != $pending_credit_row['cron_pending_credit_trans_status']) {
                        $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
													SET cron_pending_credit_trans_error = 1
													WHERE cron_pending_credit_trans_type = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_type']) . "'
														AND cron_pending_credit_trans_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                        tep_db_query($trans_error_update_sql);
                    }

                    break;
                case 'OP':
                    $order_info_select_sql = "	SELECT o.orders_id, customers_id, orders_status, currency, orders_rebated, oei.orders_extra_info_value, oei.orders_extra_info_value
												FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei on o.orders_id = oei.orders_id  AND oei.orders_extra_info_key = 'site_id'
												WHERE o.orders_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                    $order_info_result_sql = tep_db_query($order_info_select_sql);
                    $order_info_row = tep_db_fetch_array($order_info_result_sql);

                    if ($order_info_row['orders_rebated'] != 0) {
                        $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
													SET cron_pending_credit_trans_error = 1
													WHERE cron_pending_credit_trans_type = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_type']) . "'
														AND cron_pending_credit_trans_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                        tep_db_query($trans_error_update_sql);
                    } else {
                        if ($order_info_row['orders_status'] != $pending_credit_row['cron_pending_credit_trans_status']) {
                            $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
														SET cron_pending_credit_trans_error = 1,
															cron_pending_credit_trans_executable_error = 1
														WHERE cron_pending_credit_trans_type = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_type']) . "'
															AND cron_pending_credit_trans_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                            tep_db_query($trans_error_update_sql);
                        } else {
                            $points_rebated = 0;

                            $data_update_sql_array = array('orders_rebated' => 1,
                                'last_modified' => 'now()'
                            );
                            tep_db_perform(TABLE_ORDERS, $data_update_sql_array, 'update', "orders_id = '" . (int) $order_info_row['orders_id'] . "'");

                            $orders_products_select_sql = "	SELECT orders_products_id, final_price, products_quantity, products_good_delivered_price, op_rebate
															FROM " . TABLE_ORDERS_PRODUCTS . "
															WHERE orders_id = '" . (int) $order_info_row['orders_id'] . "'
																AND parent_orders_products_id < 1
																AND orders_products_is_compensate = 0
																AND products_good_delivered_price > 0";
                            $orders_products_result_sql = tep_db_query($orders_products_select_sql);
                            while ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
                                $total_pd_point_rebated = ((($orders_products_row['products_good_delivered_price'] / ($orders_products_row['final_price'] * $orders_products_row['products_quantity']) ) * $orders_products_row['op_rebate']));

                                $total_pd_point_rebated = number_format($total_pd_point_rebated, 0, '.', '');
                                if ($total_pd_point_rebated > $orders_products_row['op_rebate']) {
                                    $total_pd_point_rebated = $orders_products_row['op_rebate'];
                                }

                                $op_rebate_delivered_data_array = array('op_rebate_delivered' => $total_pd_point_rebated);
                                tep_db_perform(TABLE_ORDERS_PRODUCTS, $op_rebate_delivered_data_array, 'update', "orders_products_id = '" . (int) $orders_products_row['orders_products_id'] . "'");

                                $points_rebated += $total_pd_point_rebated;
                            }

                            if ($points_rebated >= 0) {
                                $process_success_flag = tep_cron_credit_store_point($order_info_row['customers_id'], $points_rebated, 'C', $order_info_row['orders_id'], 'P', '',$order_info_row['orders_extra_info_value']);
                            }
                        }
                    }

                    break;
                case 'PO':
                    $po_info_select_sql = "SELECT purchase_orders_id, supplier_id, purchase_orders_status, 
                                            purchase_orders_billing_status, currency, confirmed_currency_value, 
                                            store_payment_account_book_id, payment_type, purchase_orders_type, purchase_orders_paid_currency
                                           FROM " . TABLE_PURCHASE_ORDERS . "
                                           WHERE purchase_orders_id = '" . tep_db_input($pending_credit_row['cron_pending_credit_trans_id']) . "'";
                    $po_info_result_sql = tep_db_query($po_info_select_sql);
                    $po_info_row = tep_db_fetch_array($po_info_result_sql);

                    if ($po_info_row['purchase_orders_billing_status'] == '1') {
                        $trans_error_update_sql = "UPDATE " . TABLE_CRON_PENDING_CREDIT . "
                                                   SET cron_pending_credit_trans_error = 1
                                                   WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
                                                   AND cron_pending_credit_trans_id = '" . $pending_credit_row['cron_pending_credit_trans_id'] . "'";
                        tep_db_query($trans_error_update_sql);
                    } else {
                        $allow_process = false;
                        if ($po_info_row['purchase_orders_status'] != $pending_credit_row['cron_pending_credit_trans_status']) {
                            if ($pending_credit_row['cron_pending_credit_trans_status'] == '2' && in_array($po_info_row['purchase_orders_status'], array('2', '3'))) {
                                // allow PO in Processing or Completed status to proceed when pending credit requested during Processing stage
                                $allow_process = true;
                            } else {
                                $trans_error_update_sql = "UPDATE " . TABLE_CRON_PENDING_CREDIT . "
                                                           SET cron_pending_credit_trans_error = 1
                                                           WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
                                                           AND cron_pending_credit_trans_id = '" . $pending_credit_row['cron_pending_credit_trans_id'] . "'";
                                tep_db_query($trans_error_update_sql);
                            }
                        } else {
                            $allow_process = true;
                        }

                        // Special previlage for DTU Payment
                        if ($po_info_row['payment_type'] == 'd' || $po_info_row['purchase_orders_type'] == 2 || $po_info_row['purchase_orders_type'] == 3) {
                            $allow_process = true;
                        }

                        if ($allow_process) {
                            // No errors. Update purchase_orders_billing_status
                            $trans_billing_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . "
                                                         SET purchase_orders_billing_status = 1, last_modified = now()
                                                         WHERE purchase_orders_id = '" . $po_info_row['purchase_orders_id'] . "'";
                            tep_db_query($trans_billing_update_sql);

                            // Get payable amount
                            $currency_payable = tep_get_supplier_account_book_info($po_info_row['supplier_id'], $po_info_row['store_payment_account_book_id']);

                            // Check for NRSC used?
                            if ($currency_payable['pm_is_nrsc'] < 1) {
                                $currency_payable['pm_currency'] = $po_info_row['purchase_orders_paid_currency'];
                            }

                            $final_currency_payable_amount = round(tep_get_po_total_payable_amount($pending_credit_row['cron_pending_credit_trans_id'], false), $currencies->currencies[$currency_payable['pm_currency']]['decimal_places']);

                            $credit_update_success_flag = tep_cron_credit_po_wsc($po_info_row['supplier_id'], 'customers', $currency_payable['pm_currency'], $final_currency_payable_amount, $pending_credit_row['cron_pending_credit_trans_type'], $pending_credit_row['cron_pending_credit_trans_id']);

                            // Insert order comment and update last modified time
                            if ($credit_update_success_flag) {
                                $trans_history_data_array = array('purchase_orders_id' => $pending_credit_row['cron_pending_credit_trans_id'],
                                    'date_added' => 'now()',
                                    'comments' => 'Credited to PO WSC balance',
                                    'changed_by' => 'system'
                                );
                                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $trans_history_data_array);
                                tep_update_purchase_orders_status_counter($trans_history_data_array);

                                $process_success_flag = true;
                            }
                        }
                    }
                    break;
                case 'SO':  // G2G Sell Order
                    $soid = tep_db_input($pending_credit_row['cron_pending_credit_trans_id']);

                    // Check the transaction status
                    $cb_sel = " SELECT seller_id, status, currency, currency_value
                                FROM " . TABLE_C2C_BUYBACK . "
                                WHERE c2c_buyback_id = " . $soid;
                    $cb_res = tep_db_query($cb_sel);
                    if ($cb_row = tep_db_fetch_array($cb_res)) {
                        // Verify duplicate SO for same CO ( same listing-id )
                        $cbp_sel = "SELECT cbp1.orders_id, cbp2.c2c_buyback_id
                                    FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp1
                                    INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp2
                                    ON cbp2.orders_id = cbp1.orders_id
                                        AND cbp2.c2c_products_listing_id = cbp1.c2c_products_listing_id
                                        AND cbp2.c2c_buyback_id != cbp1.c2c_buyback_id
                                    WHERE cbp1.c2c_buyback_id = " . $soid;
                        $cbp_res = tep_db_query($cbp_sel);
                        if (tep_db_num_rows($cbp_res)) {
                            $conf_res = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key IN ('G2G_EMAIL_SUBJECT_PREFIX',  'G2G_STORE_OWNER_EMAIL_ADDRESS')");
                            while ($conf_row = tep_db_fetch_array($conf_res)) {
                                define($conf_row['cfgKey'], $conf_row['cfgValue']);
                            }

                            $mail = '';
                            while ($cbp_row = tep_db_fetch_array($cbp_res)) {
                                if (empty($mail)) {
                                    $mail = "Buy Order #" . $cbp_row['orders_id'] . "\n\nDuplicate Sell Orders : \n" . $soid . "\n";
                                }
                                $mail .= $cbp_row['c2c_buyback_id'] . "\n";
                            }

                            $subject = "[" . G2G_EMAIL_SUBJECT_PREFIX . "] Duplicate Sell Order #" . $soid;
                            $message = 'Buy Order # \nDuplicate Sell Orders : ' . $cron_process_datetime;
                            @tep_mail('G2G', G2G_STORE_OWNER_EMAIL_ADDRESS, $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }

                        if ($cb_row['status'] != $pending_credit_row['cron_pending_credit_trans_status']) {
                            $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
                                                        SET cron_pending_credit_trans_error = 1
                                                        WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
                                                            AND cron_pending_credit_trans_id = '" . $soid . "'";
                            tep_db_query($trans_error_update_sql);
                        } else {
                            // Get payable amount
                            $payable_amount = 0;
                            $cbp_sel = "SELECT delivered_quantity, after_fee_unit_price, custom_products_type_child_id, total_screenshot
                                        FROM " . TABLE_C2C_BUYBACK_PRODUCT . "
                                        WHERE c2c_buyback_id = " . $soid . "
                                            AND delivered_quantity > 0";
                            $cbp_res = tep_db_query($cbp_sel);
                            $uploadScreenshot = 0;
                            $required_screenshot = true;
                            while ($cbp_row = tep_db_fetch_array($cbp_res)) {
                                $uploadScreenshot += $cbp_row['total_screenshot'];
                                $payable_amount += ( $cbp_row['after_fee_unit_price'] * $cbp_row['delivered_quantity']);
                                //HLA do not required upload proof
                                if($cbp_row['custom_products_type_child_id'] == "5"){
                                    $required_screenshot = false;
                                }
                            }

                            if($required_screenshot == true && $uploadScreenshot <= 0){
                                $trans_error_update_sql = "	UPDATE " . TABLE_CRON_PENDING_CREDIT . "
                                                        SET cron_pending_credit_trans_error = 1, cron_pending_credit_trans_executable_error = 2
                                                        WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
                                                            AND cron_pending_credit_trans_id = '" . $soid . "'";
                                    tep_db_query($trans_error_update_sql);
                            } else {
                                if ($payable_amount > 0) {
                                    $credit_update_success_flag = tep_cron_credit_user_balance($cb_row['seller_id'], 'customers', $cb_row['currency'], $payable_amount, $pending_credit_row['cron_pending_credit_trans_type'], $soid);

                                    // Insert order comment
                                    if ($credit_update_success_flag) {
                                        $cbh_attr = array(
                                            'c2c_buyback_id' => $soid,
                                            'status' => 0,
                                            'date_added' => 'now()',
                                            'seller_notified' => 0,
                                            'comments' => 'Credited to account balance',
                                            'changed_by' => 'system'
                                        );
                                        tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                                    }
                                }

                                //Payable amount successfully credited to account balance.
                                $process_success_flag = true;
                            }
                        }
                    }
                    break;
            }

            // Clean up once success
            if ($process_success_flag) {
                $pending_credit_delete_sql = "DELETE FROM " . TABLE_CRON_PENDING_CREDIT . "
                                              WHERE cron_pending_credit_trans_type = '" . $pending_credit_row['cron_pending_credit_trans_type'] . "'
                                              AND cron_pending_credit_trans_id = '" . $pending_credit_row['cron_pending_credit_trans_id'] . "'";
                tep_db_query($pending_credit_delete_sql);
            }
        }

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action = 0
                                    WHERE cron_process_track_filename = 'cron_pending_credit.php'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                    WHERE cron_process_track_filename = 'cron_pending_credit.php'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Pending credit cronjob failed at ' . $cron_process_datetime;
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}

function tep_get_supplier_account_book_info($user_id, $acct_book_id = '') {
    global $currencies;
    $acct_book_arr = array();

    if (tep_not_null($user_id) && tep_not_null($acct_book_id)) {
        $acct_book_select_sql = "SELECT spab.payment_methods_id, pm.payment_methods_send_currency, pm.payment_methods_send_status
                                 FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
                                 INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                    ON (pm.payment_methods_id = spab.payment_methods_id)
                                 WHERE spab.store_payment_account_book_id = '" . tep_db_input($acct_book_id) . "'
                                 AND spab.user_id = '" . tep_db_input($user_id) . "'
                                 AND spab.user_role = 'customers'";
        $acct_book_result_sql = tep_db_query($acct_book_select_sql);
        if ($acct_book_row = tep_db_fetch_array($acct_book_result_sql)) {
            $pm_currency = ($acct_book_row['payment_methods_send_currency'] < 1) ? 0 : $currencies->get_code_by_id($acct_book_row['payment_methods_send_currency']);
            $pm_is_nrsc = ($acct_book_row['payment_methods_send_currency'] < 1) ? 0 : 1;
            $acct_book_arr = array('pm_id' => $acct_book_row['payment_methods_id'],
                'pm_status' => $acct_book_row['payment_methods_send_status'],
                'pm_currency' => $pm_currency,
                'pm_is_nrsc' => $pm_is_nrsc
            );
        }
    }

    return $acct_book_arr;
}

function tep_get_po_total_payable_amount($po_id = '', $full_amount = true) {
    $total_payable_amount = 0;
    $total_charge_back = 0;
    $total_debit_note = 0;
    $total_adjustment_plus = 0;
    $total_adjustment_minus = 0;

    if (tep_not_null($po_id)) {
        $po_select_sql = "SELECT payment_type, currency, confirmed_currency_value, purchase_orders_paid_amount, purchase_orders_type,
                          purchase_orders_paid_currency, purchase_orders_bankcharges_included, purchase_orders_bankcharges_refunded,
                          purchase_orders_gst_currency, purchase_orders_gst_value, purchase_orders_gst_amount, purchase_orders_status
                          FROM " . TABLE_PURCHASE_ORDERS . "
                          WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
        $po_result_sql = tep_db_query($po_select_sql);
        $po_row = tep_db_fetch_array($po_result_sql);
        $payment_type = $po_row['payment_type'];
        $pay_rate = $po_row['confirmed_currency_value'];
        $paid_amount = $po_row['purchase_orders_paid_amount']; // included credit note amount + previously paid amount (if any)

        $raw_charge_back = 0;
        $raw_debit_note = 0;
        
        $po_total_select_sql = "SELECT value, class FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . tep_db_input($po_id) . "'";
        $po_total_result_sql = tep_db_query($po_total_select_sql);
        while ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
            switch ($po_total_row['class']) {
                case 'po_charge_back':
                    $raw_charge_back = $po_total_row['value'];
                    $total_charge_back = $po_total_row['value'] * $pay_rate;
                    break;
                case 'po_debit_note':
                    $raw_debit_note = $po_total_row['value'];
                    $total_debit_note = $po_total_row['value'] * $pay_rate;
                    break;
                case 'po_adjustment_plus':
                    $total_adjustment_plus = $po_total_row['value'] * $pay_rate;
                    break;
                case 'po_adjustment_minus':
                    $total_adjustment_minus = $po_total_row['value'] * $pay_rate;
                    break;
            }
        }

        if ($payment_type == 'c' || $payment_type == 'd' || $po_row['purchase_orders_type'] == 2 || $po_row['purchase_orders_type'] == 3) { // Pre-payment & DTU Payment
            $has_prod_delivered = false;
            $raw_delivered_amount = 0;

            if ($po_row['purchase_orders_status'] == '3') { // when PO is completed, get delivered amount instead
                $po_products_select_sql = " SELECT purchase_orders_products_id, products_good_delivered_quantity, products_good_delivered_price
                                            FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                            WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
                $po_products_result_sql = tep_db_query($po_products_select_sql);

                while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
                    if ($po_products_row['products_good_delivered_quantity'] > 0) {
                        $has_prod_delivered = true;
                        $total_payable_amount += $po_products_row['products_good_delivered_price'];
                    }
                }
            }

            if ($has_prod_delivered == true) {
                $raw_delivered_amount = $total_payable_amount;
                $total_payable_amount = $total_payable_amount * $pay_rate;
            } else {
                $po_total_select_sql = "SELECT value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . tep_db_input($po_id) . "' AND class='po_subtotal'";
                $po_total_result_sql = tep_db_query($po_total_select_sql);
                if ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                    $raw_delivered_amount = $po_total_row['value'];
                    $total_payable_amount = $po_total_row['value'] * $pay_rate;  // take subtotal amount cause need to deduct credit note amount if not required full amount
                }
            }

            // deduct charge back
            if ($total_charge_back > 0) {
                $total_payable_amount = $total_payable_amount - $total_charge_back;
                $raw_delivered_amount = $raw_delivered_amount - $raw_charge_back;
            }

            // add GST amount in paid currency
            if ($po_row['purchase_orders_gst_value'] > 0) {
                $total_payable_amount = $total_payable_amount + (($raw_delivered_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $pay_rate);
            }
        } else if ($payment_type == 't' || $payment_type == 'g') {
            $po_products_select_sql = "SELECT purchase_orders_products_id, products_quantity, products_unit_price, products_good_delivered_price
										FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
										WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
            $po_products_result_sql = tep_db_query($po_products_select_sql);
            while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
                // for term payment, use delivered price
                $total_payable_amount += $po_products_row['products_good_delivered_price'];
            }
            $raw_delivered_amount = $total_payable_amount;
            $total_payable_amount = $total_payable_amount * $pay_rate;

            // add GST amount in paid currency
            if ($po_row['purchase_orders_gst_value'] > 0) {
                $total_payable_amount = $total_payable_amount + (($raw_delivered_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $pay_rate);
            }
        }

        // deduct debit note
        if ($total_debit_note > 0) {
            $total_payable_amount = $total_payable_amount - $total_debit_note;
        }

        // add bank charges, if any
        $bankcharges_tobe_pay = 0;
        if ($po_row['purchase_orders_bankcharges_included'] > 0 || $po_row['purchase_orders_bankcharges_refunded'] > 0) {
            $initial_bankcharges_amt = $po_row['purchase_orders_bankcharges_included'];
            $refunded_bankcharges_amt = $po_row['purchase_orders_bankcharges_refunded'];
            $bankcharges_tobe_pay = $initial_bankcharges_amt - $refunded_bankcharges_amt;
        }

        // Adjustment value +
        if ($total_adjustment_plus > 0) {
            $total_payable_amount = $total_payable_amount + $total_adjustment_plus;
        }

        // Adjustment value -
        if ($total_adjustment_minus > 0) {
            $total_payable_amount = $total_payable_amount - $total_adjustment_minus;
        }

        $total_payable_amount = $total_payable_amount + $bankcharges_tobe_pay;

        if (!$full_amount) {
            // deduct previously paid + credit note used amount
            $total_payable_amount = $total_payable_amount - $paid_amount;
        }
    }

    return $total_payable_amount;
}

function tep_cron_get_user_particulars($user_id, $user_role) {
    if ($user_role == 'supplier') {
        $user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email
									FROM " . TABLE_SUPPLIER . "
									WHERE supplier_id = '" . tep_db_input($user_id) . "'";
    } else {
        $user_info_select_sql = "	SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($user_id) . "'";
    }

    $user_info_result_sql = tep_db_query($user_info_select_sql);
    $user_info_row = tep_db_fetch_array($user_info_result_sql);

    return $user_info_row;
}

function tep_cron_calculate_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
    global $currencies;

    $trans_fee_array = array('cost' => 0, 'percent' => 0);

    $payment_fee_select_sql = "	SELECT *
								FROM " . TABLE_PAYMENT_FEES . "
								WHERE payment_methods_id = '" . tep_db_input($payment_method_id) . "'
									AND payment_methods_mode = 'SEND'";
    $payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
    $payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);

    if ($payment_fee_row['payment_fees_cost_value'] > 0)
        $trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];

    if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
        $percent_fees_not_in_range = false;

        $percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;

        if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
            $w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
            if ($percent_fees < $w_currency_min_fee) {
                $percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
                $percent_fees_not_in_range = true;
            }
        }

        if ($payment_fee_row['payment_fees_cost_percent_max'] > 0) {
            $w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');

            if ($percent_fees > $w_currency_max_fee) {
                $percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
                $percent_fees_not_in_range = true;
            }
        }

        if ($percent_fees_not_in_range) {
            $trans_fee_array['cost'] += $percent_fees;
        } else {
            $trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
        }
    }

    return $trans_fee_array;
}

function tep_cron_credit_user_balance($user_id, $user_role, $credit_currency, $credit_amount, $trans_type, $trans_id) {
    // Store the credit into corresponding CURRENCY account

    if (tep_not_null($user_id) && tep_not_null($user_role) && tep_not_null($credit_currency)) {
        /*         * ***********************************************************************
          Lock the TABLE_STORE_ACCOUNT_BALANCE and TABLE_STORE_ACCOUNT_HISTORY.
          REMEMBER: Need to lock all the tables involved in this block.
         * *********************************************************************** */
        tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE, " . TABLE_STORE_ACCOUNT_HISTORY . " WRITE;");

        $user_credit_select_sql = "	SELECT store_account_balance_amount
									FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
									WHERE user_id = '" . tep_db_input($user_id) . "'
										AND user_role = '" . tep_db_input($user_role) . "'
										AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
        $user_credit_result_sql = tep_db_query($user_credit_select_sql);

        if ($user_credit_row = tep_db_fetch_array($user_credit_result_sql)) {
            $current_balance = $user_credit_row['store_account_balance_amount'];
        } else { // Create new account
            $account_balance_data_array = array('user_id' => $user_id,
                'user_role' => $user_role,
                'store_account_balance_currency' => $credit_currency,
                'store_account_balance_amount' => 0,
                'store_account_last_modified' => 'now()'
            );
            tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $account_balance_data_array);

            $current_balance = 0;
        }

        $new_balance = $current_balance + (double) $credit_amount;

        // Insert account statement history
        $account_balance_history_data_array = array('user_id' => $user_id,
            'user_role' => $user_role,
            'store_account_history_date' => 'now()',
            'store_account_history_currency' => $credit_currency,
            'store_account_history_debit_amount' => 'NULL',
            'store_account_history_credit_amount' => (double) $credit_amount,
            'store_account_history_after_balance' => (double) $new_balance,
            'store_account_history_trans_type' => $trans_type,
            'store_account_history_trans_id' => $trans_id,
            'store_account_history_added_by' => 'system',
            'store_account_history_added_by_role' => 'admin'
        );
        tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);

        // Update store account new balance
        $account_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
										SET store_account_balance_amount = store_account_balance_amount + " . (double) $credit_amount . ",
											store_account_last_modified = now()
										WHERE user_id = '" . tep_db_input($user_id) . "'
											AND user_role = '" . tep_db_input($user_role) . "'
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
        tep_db_query($account_balance_update_sql);

        tep_db_query("UNLOCK TABLES;");
        /*         * ******************************************************************
          End of locking the TABLE_STORE_ACCOUNT_BALANCE and
          TABLE_STORE_ACCOUNT_HISTORY tables.
         * ****************************************************************** */

        return true;
    } else {
        return false;
    }
}

function tep_cron_credit_po_wsc($user_id, $user_role, $credit_currency, $credit_amount, $trans_type, $trans_id) {
    // Store the PO WSC into corresponding CURRENCY account

    if (tep_not_null($user_id) && tep_not_null($user_role) && tep_not_null($credit_currency)) {
        /*         * ***********************************************************************
          Lock the TABLE_STORE_ACCOUNT_BALANCE and TABLE_STORE_ACCOUNT_HISTORY.
          REMEMBER: Need to lock all the tables involved in this block.
         * *********************************************************************** */
        tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE, " . TABLE_STORE_ACCOUNT_HISTORY . " WRITE;");

        $po_wsc_select_sql = "	SELECT store_account_po_wsc
                                FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
                                WHERE user_id = '" . tep_db_input($user_id) . "'
                                    AND user_role = '" . tep_db_input($user_role) . "'
                                    AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
        $po_wsc_result_sql = tep_db_query($po_wsc_select_sql);

        if ($po_wsc_row = tep_db_fetch_array($po_wsc_result_sql)) {
            $current_balance = $po_wsc_row['store_account_po_wsc'];
        } else { // Create new account
            $po_wsc_data_array = array('user_id' => $user_id,
                'user_role' => $user_role,
                'store_account_balance_currency' => $credit_currency,
                'store_account_po_wsc' => 0,
                'store_account_last_modified' => 'now()'
            );
            tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $po_wsc_data_array);

            $current_balance = 0;
        }

        $new_balance = $current_balance + (double) $credit_amount;

        // Insert account statement history
        $account_balance_history_data_array = array('user_id' => $user_id,
            'user_role' => $user_role,
            'store_account_history_date' => 'now()',
            'store_account_history_currency' => $credit_currency,
            'store_account_history_account_type' => 'POWSC',
            'store_account_history_debit_amount' => 'NULL',
            'store_account_history_credit_amount' => (double) $credit_amount,
            'store_account_history_after_balance' => (double) $new_balance,
            'store_account_history_trans_type' => $trans_type,
            'store_account_history_trans_id' => $trans_id,
            'store_account_history_added_by' => 'system',
            'store_account_history_added_by_role' => 'admin'
        );
        tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);

        // Update store account new balance
        $po_wsc_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
                                SET store_account_po_wsc = store_account_po_wsc + " . (double) $credit_amount . ",
                                    store_account_last_modified = now()
                                WHERE user_id = '" . tep_db_input($user_id) . "'
                                    AND user_role = '" . tep_db_input($user_role) . "'
                                    AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
        tep_db_query($po_wsc_update_sql);

        tep_db_query("UNLOCK TABLES;");
        /*         * ******************************************************************
          End of locking the TABLE_STORE_ACCOUNT_BALANCE and
          TABLE_STORE_ACCOUNT_HISTORY tables.
         * ****************************************************************** */

        return true;
    } else {
        return false;
    }
}

function tep_cron_set_store_acc_balance($user_id, $user_role, $credit_currency, $update_info) {
    /*     * *****************************************************************
      operator = +, -, and =
     * ***************************************************************** */
    $sql_update_array = array();

    // Generate the update sql
    for ($update_cnt = 0; $update_cnt < count($update_info); $update_cnt++) {
        if (($update_info[$update_cnt]['field_name'] == 'store_account_balance_amount' || $update_info[$update_cnt]['field_name'] == 'store_account_reserve_amount')) {
            $update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
            switch ($update_info[$update_cnt]['operator']) {
                case '+':
                    $sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
                    break;
                case '-':
                    $sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
                    break;
                case '=':
                    $sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
                    break;
                default:
                    break;
            }
        }
    }

    if (count($sql_update_array)) {
        $sql_update_array[] = ' store_account_last_modified = now() ';

        $update_sql_str = " SET " . implode(', ', $sql_update_array);

        /*         * ***********************************************************************
          Lock the TABLE_STORE_ACCOUNT_BALANCE
          REMEMBER: Need to lock all the tables involved in this block.
         * *********************************************************************** */
        tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");

        $store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE .
                $update_sql_str . "
											WHERE user_id = '" . tep_db_input($user_id) . "'
												AND user_role = '" . tep_db_input($user_role) . "'
												AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
        tep_db_query($store_acc_balance_update_sql);

        $new_balance_select_sql = "	SELECT store_account_balance_amount
									FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
									WHERE user_id = '" . tep_db_input($user_id) . "'
										AND user_role = '" . tep_db_input($user_role) . "'
										AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
        $new_balance_result_sql = tep_db_query($new_balance_select_sql);
        $new_balance_row = tep_db_fetch_array($new_balance_result_sql);

        tep_db_query("UNLOCK TABLES;");
        /*         * ******************************************************************
          End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
         * ****************************************************************** */

        return $new_balance_row['store_account_balance_amount'];
    }

    return false;
}

function tep_cron_credit_store_credit($user_id, $credit_amount, $trans_type, $trans_id) {
    // ONLY DEFAULT CURRENCY - USD
    // Will create store credit account if not exists
    if (tep_cron_check_credits_account_exists($user_id)) {
        $credit_amount = trim($credit_amount);
        if (is_numeric($credit_amount) && $credit_amount > 0) {
            $credit_amount = number_format($credit_amount, 2, '.', '');
            // Update live credit balance
            $update_info = array(array('field_name' => 'sc_irreversible_amount',
                    'operator' => '+',
                    'value' => $credit_amount)
            );

            $new_sc_balance = tep_cron_set_store_credit_balance($user_id, $update_info);

            // Insert store credit history
            $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($user_id),
                'store_credit_account_type' => 'NR',
                'store_credit_history_date' => 'now()',
                'store_credit_history_currency_id' => $new_sc_balance['sc_currency_id'],
                'store_credit_history_debit_amount' => 'NULL',
                'store_credit_history_credit_amount' => (double) $credit_amount,
                'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                'store_credit_history_trans_type' => $trans_type,
                'store_credit_history_trans_id' => $trans_id,
                'store_credit_activity_type' => LOG_SC_ACTIVITY_TYPE_BONUS,
                'store_credit_history_added_by' => 'system',
                'store_credit_history_added_by_role' => 'admin'
            );
            tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
            tep_db_insert_id();
        }
    }
}

function tep_cron_check_credits_account_exists($user_id) {
    $credit_acc_select_sql = "	SELECT customer_id
								FROM " . 'coupon_gv_customer' . "
								WHERE customer_id = '" . tep_db_input($user_id) . "'";
    $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);

    if (tep_db_num_rows($credit_acc_result_sql) > 0) { // Store credit account exists
        return true;
    } else { // Check if this is a valid customer
        $customer_select_sql = "SELECT customers_id
								FROM " . TABLE_CUSTOMERS . "
								WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $customer_result_sql = tep_db_query($customer_select_sql);

        if (tep_db_num_rows($customer_result_sql) > 0) { // Valid customer
            $sc_balance_data_array = array('customer_id' => $user_id,
                'sc_reversible_amount' => 0,
                'sc_reversible_reserve_amount' => 0,
                'sc_irreversible_amount' => 0,
                'sc_irreversible_reserve_amount' => 0,
                'sc_currency_id' => 1,
                'sc_last_modified' => 'now()'
            );
            tep_db_perform('coupon_gv_customer', $sc_balance_data_array);

            return true;
        } else {
            return false;
        }
    }
}

function tep_cron_check_points_account_exists($user_id) {
    $point_acc_select_sql = "	SELECT customers_id
								FROM " . TABLE_STORE_POINTS . "
								WHERE customers_id = '" . tep_db_input($user_id) . "'";
    $point_acc_result_sql = tep_db_query($point_acc_select_sql);

    if (tep_db_num_rows($point_acc_result_sql) > 0) { // Store point account exists
        return true;
    } else { // Check if this is a valid customer
        $customer_select_sql = "SELECT customers_id
								FROM " . TABLE_CUSTOMERS . "
								WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $customer_result_sql = tep_db_query($customer_select_sql);

        if (tep_db_num_rows($customer_result_sql) > 0) { // Valid customer
            $sp_balance_data_array = array('customers_id' => $user_id,
                'sp_amount' => 0,
                'sp_last_modified' => 'now()'
            );
            tep_db_perform(TABLE_STORE_POINTS, $sp_balance_data_array);

            return true;
        } else {
            return false;
        }
    }
}

function tep_cron_credit_store_point($user_id, $credit_amount, $trans_type, $trans_id, $activity_type = 'P', $remark = '', $site_id) {
    $credit_amount = trim($credit_amount);
    if ($site_id == 5){
        return true;
        $result = g2g_serverless::adjustG2gTokenAmount($user_id, $activity_type, $credit_amount, $remark, $trans_id, false);
        return $result;
    }

    if (tep_not_null($user_id) && tep_not_null($credit_amount) && tep_not_null($trans_type) && is_numeric($credit_amount)) {
        if (tep_cron_check_points_account_exists($user_id)) {
            // Update live credit balance
            $update_info = array(array('field_name' => 'sp_amount',
                    'operator' => '+',
                    'value' => $credit_amount)
            );

            $new_sp_balance = tep_cron_set_store_point_balance($user_id, $update_info);

            $customer_info_array = tep_cron_get_user_particulars($user_id, 'customers');

            $sp_balance_history_data_array = array('customer_id' => tep_db_prepare_input($user_id),
                'store_points_history_date' => 'now()',
                'store_points_history_debit_amount' => 'NULL',
                'store_points_history_credit_amount' => (double) $credit_amount,
                'store_points_history_after_balance' => (double) $new_sp_balance['sp_amount'],
                'store_points_history_trans_type' => $trans_type,
                'store_points_history_trans_id' => $trans_id,
                'store_points_history_activity_type' => $activity_type,
                'store_points_history_activity_desc' => $remark,
                'store_points_history_activity_desc_show' => 1,
                'store_points_history_added_by' => $customer_info_array['email'],
                'store_points_history_added_by_role' => 'customers'
            );
            tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_balance_history_data_array);

            return true;
        }
    } else {
        return false;
    }
}

function tep_cron_set_store_point_balance($user_id, $sp_array) {
    global $log_object;

    /*     * *****************************************************************
      operator = + (add point), - (deduct point), = (assign new point)
     * ***************************************************************** */
    $sql_update_array = array();

    // Generate the update sql
    for ($sp_cnt = 0; $sp_cnt < count($sp_array); $sp_cnt++) {
        if (($sp_array[$sp_cnt]['field_name'] == 'sp_amount')) {
            $sp_array[$sp_cnt]['operator'] = trim($sp_array[$sp_cnt]['operator']);
            switch ($sp_array[$sp_cnt]['operator']) {
                case '+':
                    $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . $sp_array[$sp_cnt]['field_name'] . ' + ' . tep_db_input($sp_array[$sp_cnt]['value']);
                    break;
                case '-':
                    $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . $sp_array[$sp_cnt]['field_name'] . ' - ' . tep_db_input($sp_array[$sp_cnt]['value']);
                    break;
                case '=':
                    $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . tep_db_input($sp_array[$sp_cnt]['value']);
                    break;
                default:
                    break;
            }
        }
    }

    if (count($sql_update_array)) {
        $sql_update_array[] = ' sp_last_modified = now() ';

        $update_sql_str = " SET " . implode(', ', $sql_update_array);

        /*         * ***********************************************************************
          Lock the TABLE_STORE_POINTS
          REMEMBER: Need to lock all the tables involved in this block.
         * *********************************************************************** */
        tep_db_query("LOCK TABLES " . TABLE_STORE_POINTS . " WRITE;");

        $sp_update_sql = "	UPDATE " . TABLE_STORE_POINTS .
                $update_sql_str . "
							WHERE customers_id = '" . tep_db_input($user_id) . "'";
        tep_db_query($sp_update_sql);
        tep_db_query("UNLOCK TABLES;");

        /*         * ******************************************************************
          End of locking the TABLE_STORE_POINTS table.
         * ****************************************************************** */

        return tep_cron_get_current_points_balance($user_id);
    }
}

// Sets the available & actual qty of a product
function tep_cron_set_store_credit_balance($user_id, $sc_array) {
    global $log_object;
    /*     * *****************************************************************
      operator = + (add credit), - (deduct credit), = (assign new credit)
     * ***************************************************************** */
    $sql_update_array = array();

    $current_sc_array = tep_cron_get_current_credits_balance($user_id);

    // Generate the update sql
    for ($sc_cnt = 0; $sc_cnt < count($sc_array); $sc_cnt++) {
        if (($sc_array[$sc_cnt]['field_name'] == 'sc_reversible_amount' || $sc_array[$sc_cnt]['field_name'] == 'sc_irreversible_amount')) {
            $sc_array[$sc_cnt]['operator'] = trim($sc_array[$sc_cnt]['operator']);
            $sc_array[$sc_cnt]['value'] = tep_cron_get_store_credit_conversion($sc_array[$sc_cnt]['value'], DEFAULT_CURRENCY, $current_sc_array['sc_currency']);
            //$sc_array[$sc_cnt]['value'] = number_format($sc_array[$sc_cnt]['value'], 2, '.', '');
            switch ($sc_array[$sc_cnt]['operator']) {
                case '+':
                    $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' + ' . tep_db_input($sc_array[$sc_cnt]['value']);
                    break;
                case '-':
                    $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' - ' . tep_db_input($sc_array[$sc_cnt]['value']);
                    break;
                case '=':
                    $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . tep_db_input($sc_array[$sc_cnt]['value']);
                    break;
                default:
                    break;
            }
        }
    }

    if (count($sql_update_array)) {
        $sql_update_array[] = ' sc_last_modified = now() ';

        $update_sql_str = " SET " . implode(', ', $sql_update_array);

        /*         * ***********************************************************************
          Lock the 'coupon_gv_customer'
          REMEMBER: Need to lock all the tables involved in this block.
         * *********************************************************************** */
        tep_db_query("LOCK TABLES " . 'coupon_gv_customer' . " WRITE;");

        $sc_update_sql = "	UPDATE " . 'coupon_gv_customer' .
                $update_sql_str . "
							WHERE customer_id = '" . tep_db_input($user_id) . "'";
        tep_db_query($sc_update_sql);

        tep_db_query("UNLOCK TABLES;");
        /*         * ******************************************************************
          End of locking the 'coupon_gv_customer' table.
         * ****************************************************************** */

        return tep_cron_get_current_credits_balance($user_id);
    }
}

function tep_cron_get_current_credits_balance($user_id) {
    global $currencies;

    $credit_accounts = array();

    $credit_acc_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount, sp_amount, sc_currency_id
								FROM " . 'coupon_gv_customer' . "
								WHERE customer_id = '" . tep_db_input($user_id) . "'";
    $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
    if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
        $credit_accounts = array('sc_reverse' => $credit_acc_row['sc_reversible_amount'],
            'sc_irreverse' => $credit_acc_row['sc_irreversible_amount'],
            'sc_currency_id' => $credit_acc_row['sc_currency_id'],
            'sc_currency' => $currencies->get_code_by_id($credit_acc_row['sc_currency_id'])
        );
    }

    return $credit_accounts;
}

function tep_cron_get_current_points_balance($user_id) {
    $point_accounts = array();

    $sp_amount_select_sql = "	SELECT sp_amount
								FROM " . TABLE_STORE_POINTS . "
								WHERE customers_id = '" . (int) $user_id . "'";
    $sp_amount_result_sql = tep_db_query($sp_amount_select_sql);
    if ($sp_amount_row = tep_db_fetch_array($sp_amount_result_sql)) {
        $point_accounts = array('sp_amount' => $sp_amount_row['sp_amount']);
    }

    return $point_accounts;
}

function tep_cron_get_store_credit_conversion($sc_amount = 0, $sc_from_currency = DEFAULT_CURRENCY, $sc_to_currency = DEFAULT_CURRENCY) {
    global $currencies;

    if (trim($sc_from_currency) == trim($sc_to_currency))
        return $sc_amount;

    $return_value = $currencies->advance_currency_conversion($sc_amount, $sc_from_currency, $sc_to_currency, true, 'sell');

    return $return_value;
}

?>