<?php

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

tep_db_connect() or die('Unable to connect to database server!');
tep_set_time_limit(0);


// set application wide parameters
$configuration_query = tep_db_query('SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$sc_pending_orders = array();

$sql = "SELECT o.orders_id
        FROM " . TABLE_ORDERS . " AS o
        INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
            ON ot.orders_id = o.orders_id
            AND ot.class = 'ot_gv'
        WHERE o.orders_status = 1
            AND o.date_purchased < DATE_SUB(NOW(), INTERVAL 3 HOUR)";
$res = tep_db_query($sql);
while ($row = tep_db_fetch_array($res)) {
    $sc_pending_orders[] = $row["orders_id"];
}

if (count($sc_pending_orders)) {
    $subject = "[OFFGAMERS] Store Credit Pending Orders";
    $message = 'The following Pending Orders has Store Credit in used:' . "\n" . implode("\n", $sc_pending_orders);
    @tep_mail('<EMAIL>', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    @tep_mail('<EMAIL>', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

// remove CO cancellation request when orders_status 'completed' or 'canceled'
$sql = "SELECT ocr.orders_id, o.orders_status
        FROM " . TABLE_ORDERS_CANCEL_REQUEST . " AS ocr
        INNER JOIN " . TABLE_ORDERS . " AS o
            ON o.orders_id = ocr.orders_id
        WHERE o.orders_status IN ( 3, 5 )";
$res = tep_db_query($sql);
while ($row = tep_db_fetch_array($res)) {
    $_sql = "DELETE FROM " . TABLE_ORDERS_CANCEL_REQUEST . " WHERE orders_id = '" . $row['orders_id'] . "'";
    tep_db_query($_sql);
}
?>