<?php
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$first_day = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m") - 1, 1, date("Y")));
$last_day = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m"), 1, date("Y")));

# upload to s3
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$output_file_prefix = 'pg_country_sales_';
$output_file_ext = '.csv';
$s3_bucket = 'BUCKET_DATA';
$s3_filepath = 'report/';

$report_filename = $output_file_prefix . date('YmdHis') . $output_file_ext;
$zip_report_filename = $output_file_prefix . date('YmdHis') . '.zip';

$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key($s3_bucket);
$aws_obj->set_storage('STORAGE_STANDARD');
$aws_obj->set_filepath($s3_filepath);

if ($aws_obj->is_aws_s3_enabled()) {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $report_filename;
    $zip_file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $zip_report_filename;
} else {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/monthly/pg_country_sales/' . $report_filename;
    $zip_file_path = DIR_FS_DOCUMENT_ROOT . 'download/monthly/pg_country_sales/' . $zip_report_filename;
}

$fp = fopen($file_path, "w+");
fputcsv($fp, array('Report From', $first_day, ' until ', $last_day), ',', '"');
fputcsv($fp, array('Country', 'Order Number', 'Payment Date', 'Customer Group (When Place Order)', 'Payment Gateway', 'Payment Method', 'Currency', 'Surcharge (USD)', 'Purchase Amount (USD)', 'Customer ID', 'SC Reload Order'), ',', '"');

$payment_gateway = array();

$payment_gateway_select_sql = " SELECT payment_methods_id, payment_methods_title
								FROM " . TABLE_PAYMENT_METHODS . " 
								WHERE payment_methods_receive_status = '1'";
$payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql, 'read_db_link');
while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
	$payment_gateway[$payment_gateway_row['payment_methods_id']] = $payment_gateway_row['payment_methods_title'];
}

$customer_grp_array = array();

$customer_group_select_sql = "  SELECT customers_groups_id, customers_groups_name 
								FROM " . TABLE_CUSTOMERS_GROUPS . " 
								WHERE 1";
$customer_group_result_sql = tep_db_query($customer_group_select_sql, 'read_db_link');
while ($customer_group_row = tep_db_fetch_array($customer_group_result_sql)) {
	$customer_grp_array[$customer_group_row['customers_groups_id']] = $customer_group_row['customers_groups_name'];
}

$monthly_order_select_sql = "   SELECT o.orders_id, o.customers_id, o.payment_methods_id, o.payment_methods_parent_id, o.currency, o.customers_groups_id, ot.value, ot2.value AS surcharge, oss.first_date
                                FROM " . TABLE_ORDERS . " AS o
                                INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
                                    ON ( o.orders_id = ot.orders_id AND ot.class = 'ot_total' )
                                INNER JOIN " . TABLE_ORDERS_STATUS_STAT. " AS oss
                                    ON (o.orders_id = oss.orders_id AND oss.orders_status_id = 7)
                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot2 
                                    ON ( o.orders_id = ot2.orders_id AND ot2.class = 'ot_surcharge' )
                                WHERE oss.first_date >= '".$first_day."'
                                    AND oss.first_date < '".$last_day."'
                                    AND o.orders_status IN (7, 2, 3, 8)
                                    AND o.payment_methods_id > 0";
$monthly_order_result_sql = tep_db_query($monthly_order_select_sql, 'read_db_link');

while ($monthly_order_row = tep_db_fetch_array($monthly_order_result_sql)) {
    $order_ip_country = 'Unknown';
    $sc_order = '';
    
    $order_ip_country_select_sql = "SELECT c.countries_name 
                                    FROM orders_extra_info AS oei
                                    INNER JOIN countries AS c
                                        ON oei.orders_extra_info_value = c.countries_id
                                    WHERE oei.orders_id = '".$monthly_order_row['orders_id']."'
                                        AND oei.orders_extra_info_key = 'ip_country'";
    $order_ip_country_result_sql = tep_db_query($order_ip_country_select_sql, 'read_db_link');
    
    if ($order_ip_country_row = tep_db_fetch_array($order_ip_country_result_sql)) {
        $order_ip_country = $order_ip_country_row['countries_name'];
    }
    
    $sc_order_select_sql = "SELECT orders_products_id 
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_id = '".$monthly_order_row['orders_id']."'
                                AND custom_products_type_id = 3";
    $sc_order_result_sql = tep_db_query($sc_order_select_sql, 'read_db_link');
    if (tep_db_num_rows($sc_order_result_sql)) {
        $sc_order = 'Yes';
    }
    
    fputcsv($fp, array($order_ip_country, $monthly_order_row['orders_id'], $monthly_order_row['first_date'], (isset($customer_grp_array[$monthly_order_row['customers_groups_id']]) ? $customer_grp_array[$monthly_order_row['customers_groups_id']] : $monthly_order_row['customers_groups_id']), $payment_gateway[$monthly_order_row['payment_methods_parent_id']], $payment_gateway[$monthly_order_row['payment_methods_id']], $monthly_order_row['currency'], number_format($monthly_order_row['surcharge'], 2, '.', ''), number_format($monthly_order_row['value'], 2, '.', ''), $monthly_order_row['customers_id'], $sc_order), ',', '"');
}

fclose($fp);

// create object
$zip = new ZipArchive();

// open archive
if ($zip->open($zip_file_path, ZIPARCHIVE::CREATE) !== TRUE) {
    die ("Could not open archive");
}

$zip->addFile($file_path, $report_filename) or die ("ERROR: Could not add file: $report_filename");
    
// close and save archive
$zip->close();

if ($aws_obj->is_aws_s3_enabled()) {
    $aws_obj->set_file(array('tmp_name' => $zip_file_path));
    $aws_obj->set_filename($zip_report_filename);
    $aws_obj->save_file();
    
    @unlink($zip_file_path);
}
@unlink($file_path);

echo 'Completed';
?>