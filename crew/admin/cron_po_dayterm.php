<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header("Content-type: text/html; charset=utf-8");

define('CHARSET', 'utf-8');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'currencies.php');

// include the mail classes
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

if (date("H") == 0 && (date("i") >= 0 && date("i") < 15)) {
    exit;
}

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$currencies = new currencies();

$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

$cron_process_checking_select_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, cron_process_track_failed_attempt
                                     FROM " . TABLE_CRON_PROCESS_TRACK . "
                                     WHERE cron_process_track_filename = 'cron_po_dayterm.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action = 1,
                                        cron_process_track_start_date = now(),
                                        cron_process_track_failed_attempt = 0
                                    WHERE cron_process_track_filename = 'cron_po_dayterm.php'";
        tep_db_query($cron_process_update_sql);

        // Grab the first N records
        $po_dayterm_select_sql = "SELECT *
                                    FROM " . TABLE_CRON_PO_DAYTERM . "
                                    WHERE cron_po_dayterm_trans_error <> 1
                                    ORDER BY cron_po_dayterm_trans_created_date ASC
                                    LIMIT 25";
        $po_dayterm_result_sql = tep_db_query($po_dayterm_select_sql);

        $mail_body = '';

        while ($po_dayterm_row = tep_db_fetch_array($po_dayterm_result_sql)) {
            
            $process_dayterm = (date('Y-m-d H:i:s', strtotime($po_dayterm_row['cron_po_dayterm_trans_created_date'].' +'.($po_dayterm_row['cron_po_dayterm_trans_period']-5).' day')) <= date('Y-m-d H:i:s')) ? true : false;

            if ($process_dayterm) {
                // Check if payment been made for the PO
                $insert_cron = false;
                $payment_sql = "SELECT spti.store_payments_transaction_id, spti.store_payments_reimburse_id, sp.store_payments_status 
                                FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti 
                                INNER JOIN " . TABLE_STORE_PAYMENTS . " AS sp 
                                    ON spti.store_payments_id = sp.store_payments_id 
                                WHERE spti.store_payments_reimburse_id = " . $po_dayterm_row['cron_po_dayterm_trans_id'] . " 
                                ORDER BY store_payments_transaction_id 
                                DESC LIMIT 1";
                $payment_result = tep_db_query($payment_sql);
                if ($payment_row = tep_db_fetch_array($payment_result)) {
                    if ($payment_row['store_payments_status'] < 3) {
                        $insert_cron = true;
                    } else {
                        $trans_error_update_sql = "UPDATE " . TABLE_CRON_PO_DAYTERM . "
                                                   SET cron_po_dayterm_trans_error = 1
                                                   WHERE cron_po_dayterm_trans_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "'";
                        tep_db_query($trans_error_update_sql);
                    }
                } else {
                    $insert_cron = true;
                }

                // Check if PO has been canceled
                $po_status_sql = "SELECT purchase_orders_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "'";
                $po_status_result = tep_db_query($po_status_sql);
                if ($po_status_row = tep_db_fetch_array($po_status_result)) {
                    if ($po_status_row['purchase_orders_status'] == '4') {
                        $trans_error_update_sql = "UPDATE " . TABLE_CRON_PO_DAYTERM . "
                                                   SET cron_po_dayterm_trans_error = 1
                                                   WHERE cron_po_dayterm_trans_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "'";
                        tep_db_query($trans_error_update_sql);
                        $insert_cron = false;
                    }
                }

                // Check if PO have 0 payable amount
                $po_total_sql = "SELECT value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "' AND class = 'po_total'";
                $po_total_result = tep_db_query($po_total_sql);
                if ($po_total_row = tep_db_fetch_array($po_total_result)) {
                    if (empty($po_total_row['value']) || $po_total_row['value'] == 0) {
                        $trans_update_sql = "DELETE FROM " . TABLE_CRON_PO_DAYTERM . " WHERE cron_po_dayterm_trans_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "'";
                        tep_db_query($trans_update_sql);
                        $insert_cron = false;
                    }
                }

                if ($insert_cron) {
                    // start po table
                    $po_table = '';
                    $po_table .= '<table cellspacing="0" cellpadding="1" border="1" width="100%">';
                    $po_table .= ' <tr>';

                    $po_info_select_sql = "SELECT purchase_orders_id, purchase_orders_ref_id, currency
                                           FROM " . TABLE_PURCHASE_ORDERS . "
                                           WHERE purchase_orders_id = '" . tep_db_input($po_dayterm_row['cron_po_dayterm_trans_id']) . "'";
                    $po_info_result_sql = tep_db_query($po_info_select_sql);
                    $po_info_row = tep_db_fetch_array($po_info_result_sql);

                    $date1 = new DateTime(date('Y-m-d'));
                    $date2 = new DateTime(date('Y-m-d', strtotime($po_dayterm_row['cron_po_dayterm_trans_date'])));

                    $no_of_days_left = $date2->diff($date1)->format('%R%a days');

                    $po_table .= "      <td colspan='10'><b>".$po_info_row['purchase_orders_ref_id']." : ".$no_of_days_left."</b></td>";


                    $po_table .= '  </tr>';
                    $po_table .= '  <tr>';
                    $po_table .= '      <td width="8%" align="center">Product ID</td>';
                    $po_table .= '      <td>Product Name</td>';
                    $po_table .= '      <td width="8%" align="center">Quantity</td>';
                    $po_table .= '      <td width="8%" align="center">RECV Quantity</td>';
                    $po_table .= '      <td width="8%" align="center">Sales Quantity</td>';
                    $po_table .= '      <td width="8%" align="center">Remaining Quantity</td>';
                    $po_table .= '      <td width="8%" align="center">Sales &#37;</td>';
                    $po_table .= '      <td width="8%" align="center">Remaining &#37;</td>';
                    $po_table .= '      <td width="8%" align="center">Sales Amount</td>';
                    $po_table .= '      <td width="8%" align="center">Remaining Amount</td>';
                    $po_table .= '  </tr>';

                    // Get products details
                    $po_products_sql = "SELECT products_id, products_name, products_quantity, products_good_delivered_quantity, products_unit_price
                                        FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                        WHERE purchase_orders_id = '" . $po_info_row['purchase_orders_id'] . "'";
                    $po_products_result = tep_db_query($po_products_sql);

                    $subtotal_sales_amount = 0;
                    $subtotal_remaining_amount = 0;
                    while ($po_products_row = tep_db_fetch_array($po_products_result)) {
                        $total_sales = 0;
                        $total_remaining = 0;
                        $total_sales_percentage = 0;
                        $total_remaining_percentage = 0;
                        $total_sales_amount = 0;
                        $total_remaining_amount = 0;
                        $prod_sql = "SELECT status_id, count(custom_products_code_id) AS total 
                                    FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
                                    WHERE purchase_orders_id = " . $po_info_row['purchase_orders_id'] . "
                                        AND products_id = " . $po_products_row['products_id'] . " 
                                        AND status_id IN (1,0)
                                    GROUP BY status_id
                                    ORDER BY status_id";
                        $prod_result = tep_db_query($prod_sql);
                        while ($prod_row = tep_db_fetch_array($prod_result)) {
                            if ($prod_row['status_id'] == 1) {
                                $total_remaining = $prod_row['total'];
                            } else {
                                $total_sales = $prod_row['total'];
                            }
                        }

                        $total_sales_percentage = ($total_sales) ? number_format(($total_sales / ($total_sales + $total_remaining)) * 100, 2) : 0;

                        $total_remaining_percentage = ($total_remaining) ? number_format(($total_remaining / ($total_sales + $total_remaining)) * 100, 2) : 0;

                        $total_sales_amount = ($total_sales) ? number_format(($total_sales * $po_products_row['products_unit_price']), $currencies->currencies[$po_info_row['currency']]['decimal_places'], $currencies->currencies[$po_info_row['currency']]['decimal_point'], $currencies->currencies[$po_info_row['currency']]['thousands_point']) : 0;

                        $total_remaining_amount = ($total_remaining) ? number_format(($total_remaining * $po_products_row['products_unit_price']), $currencies->currencies[$po_info_row['currency']]['decimal_places'], $currencies->currencies[$po_info_row['currency']]['decimal_point'], $currencies->currencies[$po_info_row['currency']]['thousands_point']) : 0;

                        $subtotal_sales_amount = $subtotal_sales_amount + ($total_sales * $po_products_row['products_unit_price']);

                        $subtotal_remaining_amount = $subtotal_remaining_amount + ($total_remaining * $po_products_row['products_unit_price']);

                        // table body
                        $po_table .= '  <tr>';
                        $po_table .= '      <td align="center">'.$po_products_row['products_id'].'</td>';
                        $po_table .= '      <td>'.$po_products_row['products_name'].'</td>';
                        $po_table .= '      <td align="center">'.$po_products_row['products_quantity'].'</td>';
                        $po_table .= '      <td align="center">'.$po_products_row['products_good_delivered_quantity'].'</td>';
                        $po_table .= '      <td align="center">'.$total_sales.'</td>';
                        $po_table .= '      <td align="center">'.$total_remaining.'</td>';
                        $po_table .= '      <td align="center">'.$total_sales_percentage.'</td>';
                        $po_table .= '      <td align="center">'.$total_remaining_percentage.'</td>';
                        $po_table .= '      <td align="center">'.$currencies->currencies[$po_info_row['currency']]['symbol_left'].$total_sales_amount.$currencies->currencies[$po_info_row['currency']]['symbol_right'].'</td>';
                        $po_table .= '      <td align="center">'.$currencies->currencies[$po_info_row['currency']]['symbol_left'].$total_remaining_amount.$currencies->currencies[$po_info_row['currency']]['symbol_right'].'</td>';
                        $po_table .= '  </tr>';
                    }

                    $subtotal_po_amount = number_format(($subtotal_sales_amount + $subtotal_remaining_amount), $currencies->currencies[$po_info_row['currency']]['decimal_places'], $currencies->currencies[$po_info_row['currency']]['decimal_point'], $currencies->currencies[$po_info_row['currency']]['thousands_point']);

                    $subtotal_sales_percentage = ($subtotal_sales_amount) ? number_format(($subtotal_sales_amount / ($subtotal_sales_amount + $subtotal_remaining_amount) * 100), 2) : 0;

                    $subtotal_remaining_percentage = ($subtotal_remaining_amount) ? number_format(($subtotal_remaining_amount / ($subtotal_sales_amount + $subtotal_remaining_amount) * 100), 2) : 0;

                    $subtotal_sales_amount = number_format($subtotal_sales_amount, $currencies->currencies[$po_info_row['currency']]['decimal_places'], $currencies->currencies[$po_info_row['currency']]['decimal_point'], $currencies->currencies[$po_info_row['currency']]['thousands_point']);

                    $subtotal_remaining_amount = number_format($subtotal_remaining_amount, $currencies->currencies[$po_info_row['currency']]['decimal_places'], $currencies->currencies[$po_info_row['currency']]['decimal_point'], $currencies->currencies[$po_info_row['currency']]['thousands_point']);

                    // Total line
                    $po_table .= '  <tr>';
                    $po_table .= '      <td colspan="8" align="right">Total:</td>';
                    $po_table .= '      <td align="center">'.$currencies->currencies[$po_info_row['currency']]['symbol_left'].$subtotal_sales_amount.$currencies->currencies[$po_info_row['currency']]['symbol_right'].'<br>['.$subtotal_sales_percentage.'&#37;]</td>';
                    $po_table .= '      <td align="center">'.$currencies->currencies[$po_info_row['currency']]['symbol_left'].$subtotal_remaining_amount.$currencies->currencies[$po_info_row['currency']]['symbol_right'].'<br>['.$subtotal_remaining_percentage.'&#37;]</td>';
                    $po_table .= '</tr>';

                    // Total PO line
                    $po_table .= '  <tr>';
                    $po_table .= '      <td colspan="8" align="right">Total PO:</td>';
                    $po_table .= '      <td colspan="2" align="center">'.$currencies->currencies[$po_info_row['currency']]['symbol_left'].$subtotal_po_amount.$currencies->currencies[$po_info_row['currency']]['symbol_right'].'</td>';
                    $po_table .= '</tr>';

                    $po_table .= '</table><br>';

                    $mail_body .= $po_table;

                    $trans_count_update_sql = "UPDATE " . TABLE_CRON_PO_DAYTERM . "
                                               SET cron_po_dayterm_trans_notification_count = cron_po_dayterm_trans_notification_count + 1
                                               WHERE cron_po_dayterm_trans_id = '" . $po_dayterm_row['cron_po_dayterm_trans_id'] . "'";
                    tep_db_query($trans_count_update_sql);
                }
            }
        }
        
        if ($mail_body) {
            if (defined('DAYTERM_NOTIFICATION_EMAIL') && tep_not_null(DAYTERM_NOTIFICATION_EMAIL)) {
                // Send email to reciever set in configuration CDK PO/Supplier > Configuration
                $mail_subject = 'PO Day Term Notification - '.date('Y-m-d');
                $email_to_array = tep_parse_email_string(DAYTERM_NOTIFICATION_EMAIL);
                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                    tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $mail_subject, $mail_body, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
            }
        }

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                    SET cron_process_track_in_action = 0
                                    WHERE cron_process_track_filename = 'cron_po_dayterm.php'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                    WHERE cron_process_track_filename = 'cron_po_dayterm.php'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                echo "Cron Failed";
            }
        }
    }
}

?>