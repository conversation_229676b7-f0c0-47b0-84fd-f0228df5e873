<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

error_reporting(E_ALL ^ E_NOTICE);

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

$current_system_year = date('y');	// current cron running system year in 2 digits.

$po_suppliers_select_sql = "SELECT po_suppliers_id, po_supplier_po_ref_year FROM " . TABLE_PO_SUPPLIERS;
$po_suppliers_result_sql = tep_db_query($po_suppliers_select_sql);
while ($po_suppliers_row = tep_db_fetch_array($po_suppliers_result_sql)) {
	$alias_year = $po_suppliers_row['po_supplier_po_ref_year'];
	if ($alias_year != $current_system_year) {
		$ref_counter_update_sql = "	UPDATE " . TABLE_PO_SUPPLIERS . " 
									SET po_supplier_po_ref_year = '".$current_system_year."', 
										po_supplier_po_ref_counter = '0' 
									WHERE po_suppliers_id = '".$po_suppliers_row['po_suppliers_id']."'";
		$update_result = tep_db_query($ref_counter_update_sql);
	}
}
?>