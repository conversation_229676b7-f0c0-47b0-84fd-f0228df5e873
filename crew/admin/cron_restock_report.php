<?

/*
  $Id: cron_restock_report.php,v 1.13 2014/08/18 11:39:56 weichen Exp $
 */
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_set_time_limit(300);

// make a connection to the master database... now
tep_db_connect() or die('Unable to connect to database server!');

// make a connection to the read-only database... now
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

$date_before = 30;
$date_arr = array();
$product_arr = array();

$date_from = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") - $date_before, date("Y")));
$date_to = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m"), date("d") - 1, date("Y")));

$stock_date_from = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d"), date("Y")));
$stock_date_to = date("Y-m-d H:i:s", mktime(2, 0, 0, date("m"), date("d"), date("Y")));

$previous_stock_date_from = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") - 1, date("Y")));
$previous_stock_date_to = date("Y-m-d H:i:s", mktime(2, 0, 0, date("m"), date("d") - 1, date("Y")));

$date_arr = cron_assign_date_arr($date_to, $date_before);

$languages_id = 1;
$product_arr = array();
$sold_qty_arr = array();
$store_credit = 78717;

$date_range_sql = "(o.date_purchased >= '" . $date_from . "' AND o.date_purchased <= '" . $date_to . "')";

$order_qty_fileds_str = "op.products_quantity AS total_purchase_qty, op.products_canceled_quantity AS total_canceled_qty";

$get_products_select_sql = "(SELECT p.products_id, p.products_cat_path, p.products_quantity_order, pd.products_name, sh.products_quantity, sh.products_actual_quantity, sh2.products_actual_quantity AS previous_day_actual_qty
                            FROM " . TABLE_PRODUCTS . " AS p
                            INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                ON p.products_id = pd.products_id
                            INNER JOIN " . TABLE_STOCK_HISTORY . " AS sh
                                ON (sh.products_id = p.products_id
                                AND sh.stock_history_date >= '" . $stock_date_from . "'
                                AND sh.stock_history_date <= '" . $stock_date_to . "')
                            LEFT JOIN " . TABLE_STOCK_HISTORY . " AS sh2
                                ON (sh2.products_id = p.products_id
                                AND sh2.stock_history_date >= '" . $previous_stock_date_from . "'
                                AND sh2.stock_history_date <= '" . $previous_stock_date_to . "')
                            WHERE p.custom_products_type_id = '2'
                                AND pd.language_id='" . (int) $languages_id . "'
                            ORDER BY p.products_cat_path, p.products_sort_order)
                            UNION
                            (SELECT p.products_id, p.products_cat_path, p.products_quantity_order, pd.products_name, NULL AS products_quantity, NULL AS products_actual_quantity, NULL AS previous_day_actual_qty
                            FROM " . TABLE_PRODUCTS . " AS p
                            INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                ON p.products_id = pd.products_id
                            WHERE p.products_id = " . $store_credit . "
                                AND pd.language_id='" . (int) $languages_id . "')";
$get_products_result_sql = tep_db_query($get_products_select_sql, 'read_db_link');

while ($get_products_row = tep_db_fetch_array($get_products_result_sql)) {
    if (tep_not_null($get_products_row['products_quantity_order'])) {
        $products_quantity_order = $get_products_row['products_quantity_order'];
    } else {
        $cat_cfg_array = tep_get_cfg_setting($get_products_row['products_id'], 'product', 'STOCK_REORDER_LEVEL');
        $products_quantity_order = $cat_cfg_array["STOCK_REORDER_LEVEL"];
    }

    $product_arr[$get_products_row['products_id']][] = $get_products_row['products_cat_path'];
    $product_arr[$get_products_row['products_id']][] = $get_products_row['products_id'];
    $product_arr[$get_products_row['products_id']][] = strip_tags($get_products_row['products_name']);
    $product_arr[$get_products_row['products_id']][] = $products_quantity_order;
    $product_arr[$get_products_row['products_id']][] = $get_products_row["products_quantity"];
    $product_arr[$get_products_row['products_id']][] = $get_products_row["products_actual_quantity"];
    $product_arr[$get_products_row['products_id']][] = $get_products_row["previous_day_actual_qty"];

    $orders_products_select_sql = "SELECT op.orders_products_id, o.orders_id, DATE_FORMAT(o.date_purchased, '%Y-%m-%d') AS date_purchased, o.orders_status, o.orders_cb_status, " . $order_qty_fileds_str . "
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                        ON o.orders_id = op.orders_id
                                    WHERE op.orders_products_is_compensate=0
                                        AND o.orders_status IN (2,3)
                                        AND $date_range_sql
                                        AND op.products_id = '" . $get_products_row['products_id'] . "'";
    $orders_products_result_sql = tep_db_query($orders_products_select_sql, 'read_db_link');
    $orders_products_num = tep_db_num_rows($orders_products_result_sql);
    if ($orders_products_num > 0) {
        while ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
            $is_dtu_select_sql = "SELECT orders_products_id
                                FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
                                WHERE opei.orders_products_id='" . $orders_products_row['orders_products_id'] . "'
                                    AND opei.orders_products_extra_info_key = 'delivery_mode'
                                    AND opei.orders_products_extra_info_value = '6'";
            $is_dtu_result_sql = tep_db_query($is_dtu_select_sql, 'read_db_link');
            if (tep_db_num_rows($is_dtu_result_sql)) {
                continue;
            }

            $sold_qty = 0;
            if ($orders_products_row['orders_status'] == 2 || $orders_products_row['orders_status'] == 3) {
                $sold_qty += ( $orders_products_row['total_purchase_qty'] - $orders_products_row['total_canceled_qty'] );
            }

            $date_purchased = $orders_products_row['date_purchased'];

            if (!isset($sold_qty_arr[$get_products_row['products_id']][$date_purchased])) {
                $sold_qty_arr[$get_products_row['products_id']][$date_purchased] = 0;
            }
            $sold_qty_arr[$get_products_row['products_id']][$date_purchased] += $sold_qty;
        }
        foreach ($date_arr as $date) {
            if (tep_not_null($sold_qty_arr[$get_products_row['products_id']][$date])) {
                $product_arr[$get_products_row['products_id']][] = $sold_qty_arr[$get_products_row['products_id']][$date];
            } else {
                $product_arr[$get_products_row['products_id']][] = '0';
            }
        }
    } else {
        foreach ($date_arr as $date) {
            $product_arr[$get_products_row['products_id']][] = '0';
        }
    }
}

if (sizeof($product_arr) > 0) {
    cron_generate_csv($product_arr, $date_arr);
}

function cron_generate_csv($product_arr, $date_arr)
{
    global $stock_date_from, $previous_stock_date_from;

    $line_break = "\n";
    $export_csv_data = '';
    $merged_csv_data = array();

    $csv_data_heading[] = 'Categories';
    $csv_data_heading[] = 'Product ID';
    $csv_data_heading[] = 'Product Name';
    $csv_data_heading[] = 'Reorder Level';
    $csv_data_heading[] = 'Available Qty (' . $stock_date_from . ')';
    $csv_data_heading[] = 'Actual Qty (' . $stock_date_from . ')';
    $csv_data_heading[] = 'Actual Qty (' . $previous_stock_date_from . ')';

    $csv_data_heading = array_merge($csv_data_heading, $date_arr);

    $merged_csv_data = array_merge(array($csv_data_heading), $product_arr);

    // CSV product contents
    foreach ($merged_csv_data as $merged_csv_data_rows) {
        $generate_csv_data = array();
        foreach ($merged_csv_data_rows as $csv_data) {
            $generate_csv_data[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $csv_data) . '"';
        }
        $export_csv_data .= implode(',', $generate_csv_data) . "\n";
    }

    $filename_csv = date('YmdHis') . '.csv';
    $file_location_csv = 'download/30days/cdkey_sales/' . $filename_csv;

    if (!$handle = fopen($file_location_csv, 'w')) {
        exit;
    }

    // Write to our opened file.
    if (fwrite($handle, $export_csv_data) === FALSE) {
        fclose($handle);
        exit;
    }
    fclose($handle);
}

function cron_assign_date_arr($date_to, $date_before)
{
    if (tep_not_null($date_to) && tep_not_null($date_before)) {
        $date_to_splitted = split_dep("-", $date_to);
        $date_arr = array();

        $year = $date_to_splitted[0];
        $month = $date_to_splitted[1];
        $day = $date_to_splitted[2];

        for ($date_cnt = 0; $date_cnt < $date_before; $date_cnt ++) {
            $date_arr[] = date("Y-m-d", mktime(0, 0, 0, $month, $day - $date_cnt, $year));
        }
        return $date_arr;
    } else {
        return false;
    }
}

?>