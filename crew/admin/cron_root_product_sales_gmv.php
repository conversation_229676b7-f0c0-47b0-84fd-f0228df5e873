<?php

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

tep_set_time_limit(0);

$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
$db_og_link = tep_db_connect(DB_OG_SERVER, DB_OG_SERVER_USERNAME, DB_OG_SERVER_PASSWORD, DB_OG_DATABASE, 'db_og_link') or die('Unable to connect to database server!');

$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$fr_date = date("Y-m-d 00:00:00", strtotime("-1 day"));
$to_date = date("Y-m-d 00:00:00");
$next_date = "";

if (isset($_SERVER['argv'][1])) {
    if (tep_not_null($_SERVER['argv'][1])) {
        $fr_date = trim($_SERVER['argv'][1]) . " 00:00:00";

        if (isset($_SERVER['argv'][2]) && tep_not_null($_SERVER['argv'][2])) {
            $to_date = trim($_SERVER['argv'][2]) . " 00:00:00";
        }
    }
}

// upload to s3
$s3_bucket = 'BUCKET_DATA';
$s3_filepath = 'report/';
$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key($s3_bucket);
$aws_obj->set_storage('STORAGE_STANDARD');
$aws_obj->set_filepath($s3_filepath);

// category
$game_cat_array = [];
$categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
$categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
    $game_cat_array[] = $categories_row['categories_id'];
}

do {
    $next_date = date("Y-m-d 00:00:00", strtotime($fr_date . " +1 day"));
    $sign_date = date("Y-m-01 00:00:00", strtotime($fr_date . " -2 month"));

    if ($aws_obj->is_aws_s3_enabled()) {
        $filename = "root_product_sales_gmv_" . date("Ymd", strtotime($fr_date)) . "_to_" . date("Ymd", strtotime($next_date)) . "_" . date('YmdHis') . ".csv";
        $file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $filename;
    } else {
        echo "Fail to upload to S3";
        exit();
    }
    $fp = fopen($file_path, "w+");
    fputcsv($fp, array('Report From ' . $fr_date . ' until ' . $next_date), ',', '"');
    fputcsv($fp, array('Game ID', 'Products ID', 'Game Title', 'Total Sales (USD)', 'New Customer', 'Existing Customer'), ',', '"');

    $game_result = array();
    $languages_id = 1;

    $sql = "SELECT o.orders_id, o.customers_id, op.products_id, op.final_price, op.products_quantity
        FROM orders AS o
        INNER JOIN orders_products AS op
            ON op.orders_id = o.orders_id
        INNER JOIN orders_extra_info AS oei
            ON oei.orders_id = o.orders_id
            AND oei.orders_extra_info_key = 'site_id'
        INNER JOIN orders_status_stat AS oss
            ON oss.orders_id = o.orders_id
        WHERE oss.latest_date >= '" . $fr_date . "'
            AND oss.latest_date < '" . $next_date . "'
            AND oss.orders_status_id = 7
            AND op.products_bundle_id = 0
            AND op.orders_products_is_compensate = 0
            AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)";
    $res = tep_db_query($sql, 'read_db_link');
    while ($row = tep_db_fetch_array($res)) {
        $sales_amt = $row["final_price"] * $row["products_quantity"];
        $main_product_id = $row["products_id"];
        $game_id = 0;

        if (abs($sales_amt) > 0 && $main_product_id > 0) {
            // sign-up within 3 months consider as new customer
            $_sql = "SELECT ci.customers_info_id
                FROM customers_info AS ci
                WHERE ci.customers_info_id = " . $row["customers_id"] . "
                    AND ci.customers_info_date_account_created >= '" . $sign_date . "'
                    AND ci.customers_info_date_account_created < '" . $next_date . "'";
            $_res = tep_db_query($_sql, 'read_db_link');
            $new_customer = tep_db_num_rows($_res) ? true : false;

            $_sql = "SELECT products_cat_id_path
                    FROM products
                    WHERE products_id = '" . $main_product_id . "'";
            $_res = tep_db_query($_sql, 'read_db_link');
            $_row = tep_db_fetch_array($_res);

            $product_cat_path = explode('_', substr($_row['products_cat_id_path'], 1, -1));
            if (count($product_cat_path)) {
                foreach ($product_cat_path as $cat_id) {
                    if (in_array($cat_id, $game_cat_array)) {
                        $game_id = $cat_id;
                        break;
                    }
                }
            }

            if (!isset($game_result[$game_id])) {
                $game_result[$game_id] = array();
                $game_result[$game_id]['sales'] = 0;
                $game_result[$game_id]['new'] = 0;
                $game_result[$game_id]['exist'] = 0;
                $game_result[$game_id]['main_prod'] = array();
            }

            $game_result[$game_id]['sales'] += $sales_amt;
            if ($new_customer) {
                $game_result[$game_id]['new'] += $sales_amt;
            } else {
                $game_result[$game_id]['exist'] += $sales_amt;
            }

            if (!in_array($main_product_id, $game_result[$game_id]['main_prod'])) {
                $game_result[$game_id]['main_prod'][] = $main_product_id;
            }
        }
    }

    foreach ($game_result as $game_id => $game_sales) {
        $_sql = "SELECT categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " WHERE categories_id = '" . (int) $game_id . "' AND language_id = 1";
        $_res = tep_db_query($_sql, 'read_db_link');
        $_row = tep_db_fetch_array($_res);

        fputcsv($fp, array($game_id, implode(',', $game_sales['main_prod']), $_row['categories_name'], $game_sales['sales'], $game_sales['new'], $game_sales['exist']), ',', '"');
    }

    if ($aws_obj->is_aws_s3_enabled()) {
        $aws_obj->set_file(array('tmp_name' => $file_path));
        $aws_obj->set_filename($filename);
        $aws_obj->save_file();
    }
    @unlink($file_path);
    
    $fr_date = $next_date;
} while ($next_date != $to_date);
?>