<?php

header("Content-type: text/html; charset=utf-8");

// Include application configuration parameters
require('includes/configure.php');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');
require(DIR_WS_FUNCTIONS . 'general.php');

tep_set_time_limit(0);

// Only check for price if the is records number been passed. Corresponding row number in price_check_slots table
if (!isset($_SERVER['argv']) || !tep_not_null($_SERVER['argv'][1])) {
    exit;
} else {
    $report_type = $_SERVER['argv'][1];
}

// make a connection to the database... now
tep_db_connect();
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
$db_og_link = tep_db_connect(DB_OG_SERVER, DB_OG_SERVER_USERNAME, DB_OG_SERVER_PASSWORD, DB_OG_DATABASE, 'db_og_link') or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$monday_day_offset = 0;
$output_file_prefix = '';

if (strtolower($report_type) == 'day') {
    $output_file_prefix = 'root_product_sales_dailyV2_';

    $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 1, date('Y')));
    $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d'), date('Y')));
} else if (strtolower($report_type) == 'week') {
    $output_file_prefix = 'root_product_sales_weeklyNew_';

    if (date('w') >= 1) {
        $monday_day_offset = date('w') - 1;
    } else {
        exit;
    }

    $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - $monday_day_offset - 7, date('Y')));
    $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - $monday_day_offset, date('Y')));
} else if (strtolower($report_type) == 'month') {
    $output_file_prefix = 'root_product_sales_monthlyNew_';

    $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m") - 1, 1, date("Y")));
    $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m"), 1, date("Y")));
} else {
    exit;
}

# upload to s3
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$output_file_ext = '.csv';
$s3_bucket = 'BUCKET_DATA';
$s3_filepath = 'report/';

$report_filename = $output_file_prefix . date('YmdHis') . $output_file_ext;

$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key($s3_bucket);
$aws_obj->set_storage('STORAGE_STANDARD');
$aws_obj->set_filepath($s3_filepath);

if ($aws_obj->is_aws_s3_enabled()) {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $report_filename;
} else {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/monthly/root_product_sales/' . $report_filename;
}

$fp = fopen($file_path, "w+");
fputcsv($fp, array('Report From', $start_date, ' until ', $end_date), ',', '"');
fputcsv($fp, array('Game ID', 'Products ID', 'Game Title', 'Sales (USD)'), ',', '"');

$game_result = array();
$languages_id = 1;
$store_credit = 78717;

$game_cat_array = [];
$categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
$categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
    $game_cat_array[] = $categories_row['categories_id'];
}

$order_select_sql = "	SELECT sa.sales_activities_orders_id, sa.sales_activities_operator, sa.sales_activities_amount, sa.sales_activities_products_id, op.custom_products_type_id 
                        FROM sales_activities AS sa
                        INNER JOIN orders_products AS op
                            ON sa.sales_activities_orders_products_id=op.orders_products_id
                        WHERE sa.sales_activities_date >= '" . $start_date . "'
                            AND sa.sales_activities_date < '" . $end_date . "'
                            AND sa.sales_activities_code='D'";
$order_result_sql = tep_db_query($order_select_sql, 'read_db_link');

while ($order_row = tep_db_fetch_array($order_result_sql)) {
    $processed_parent_id = array();

    $sales_amt = 0;
    $main_product_id = 0;
    $game_id = 0;
    
    if (($order_row['custom_products_type_id'] == '2') || ($order_row['sales_activities_products_id'] == $store_credit)) {
        $sales_amt = $order_row['sales_activities_operator'] == '-' ? ($order_row['sales_activities_amount']*-1) : $order_row['sales_activities_amount'];
        $main_product_id = $order_row['sales_activities_products_id'];
    }

    if (abs($sales_amt) > 0 && $main_product_id > 0) {
        $product_path_select_sql = "SELECT products_cat_id_path
                                    FROM products
                                    WHERE products_id ='" . $main_product_id . "'";
        $product_path_result_sql = tep_db_query($product_path_select_sql, 'read_db_link');
        $product_path_row = tep_db_fetch_array($product_path_result_sql);

        $product_cat_path = explode('_', substr($product_path_row['products_cat_id_path'], 1, -1));

        if (count($product_cat_path)) {
            foreach ($product_cat_path as $cat_id) {
                if (in_array($cat_id, $game_cat_array)) {
                    $game_id = $cat_id;
                    break;
                }
            }
        }

        if (!isset($game_result[$game_id])) {
            $game_result[$game_id] = array();
        }

        if (!isset($game_result[$game_id]['sales'])) {
            $game_result[$game_id]['sales'] = 0;
        }

        if (!isset($game_result[$game_id]['order'])) {
            $game_result[$game_id]['order'] = array();
        }

        if (!isset($game_result[$game_id]['main_prod'])) {
            $game_result[$game_id]['main_prod'] = array();
        }

        $game_result[$game_id]['sales'] += $sales_amt;
        $game_result[$game_id]['order'][] = $order_row['sales_activities_orders_id'];

        if (!in_array($main_product_id, $game_result[$game_id]['main_prod'])) {
            $game_result[$game_id]['main_prod'][] = $main_product_id;
        }
    }
}

foreach ($game_result as $game_id => $game_sales) {
    $game_name = tep_get_categories_name($game_id, 1);

    fputcsv($fp, array($game_id, implode(',', $game_sales['main_prod']), $game_name, $game_sales['sales']), ',', '"');
}

fclose($fp);

if ($aws_obj->is_aws_s3_enabled()) {
    $aws_obj->set_file(array('tmp_name' => $file_path));
    $aws_obj->set_filename($report_filename);
    $aws_obj->save_file();
}
@unlink($file_path);
?>