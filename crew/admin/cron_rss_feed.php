<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
require_once(DIR_WS_CLASSES . 'rss_feed.php');

tep_set_time_limit(0);
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('   SELECT configuration_key as cfgKey, configuration_value as cfgValue
                                        FROM ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}


$avaliable_service_array = array(
    1 => 'price_panda',
    2 => 'allcdkey',
    3 => 'criteo',
);
$service_id = isset($_GET['service']) ? (int) $_GET['service'] : '';
$service = isset($avaliable_service_array[$service_id]) ? $avaliable_service_array[$service_id] : $avaliable_service_array[2];

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, 
                                            cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process, 
                                            cron_process_track_failed_attempt
										FROM " . TABLE_CRON_PROCESS_TRACK . "
										WHERE cron_process_track_filename = 'cron_rss_feed.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
									SET cron_process_track_in_action = 1,
										cron_process_track_start_date = now(),
										cron_process_track_failed_attempt = 0
									WHERE cron_process_track_filename = 'cron_rss_feed.php'";

        tep_db_query($cron_process_update_sql);

// ====================================================================== Start ==================================================================================
        try {
            $rss_feed = new rss_feed();

            if ($service === 'criteo') {
                $rss_feed->xml_title = "Criteo RSS Feed";
                $rss_feed->map_category_array = array(
                    4444, 5236, 17710, 17855, 20359, 5147, 5329, 22671, 17634, 22581,
                    17949, 20318, 21492, 3466, 17508, 21608, 17797, 18181, 19958, 4130,
                    20573, 22553, 21180, 22661, 22660, 22681, 16059, 22741, 20771, 6306
                );
            } else if ($service === 'allcdkey') {
                $rss_feed->xml_title = "All CD Key RSS Feed";
                $rss_feed->map_category_region_array = array('Europe (EU)', 'United Kingdom (UK)', 'Global', 'United State (US)');
            }

            $rss_feed->generate_feed($service);

            unset($rss_feed);
        } catch (Exception $e) {
            reportError(array('e' => $e->getMessage()));
        }
// ======================================================================  End  ==================================================================================
        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
									SET cron_process_track_in_action = 0
									WHERE cron_process_track_filename = 'cron_rss_feed.php'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                WHERE cron_process_track_filename = 'cron_rss_feed.php'";
            tep_db_query($cron_process_attempt_update_sql);

            if ($cron_process_checking_row['cron_process_track_failed_attempt'] < 3) {
                $headers = 'To: <EMAIL>' . "\r\n" .
                        'From: ' . STORE_NAME . '<<EMAIL>>' . "\r\n" .
                        'Reply-To: <EMAIL>' . "\r\n" .
                        'X-Mailer: PHP/' . phpversion();
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Deliver to S3  RSS file cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'];
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            } else {
                $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                            SET cron_process_track_in_action = 0
                                            WHERE cron_process_track_filename = 'cron_rss_feed.php'";
                tep_db_query($unlock_cron_process_sql);
            }
        }
    }
}

function reportError($response_data, $ext_subject = '') {
    include_once(DIR_WS_CLASSES . 'slack_notification.php');
    $slack = new slack_notification();
    $data = json_encode(array(
        'text' => '[OG Crew] Cron RSS Feed Error - ' . date("F j, Y H:i"),
        'attachments' => array(
            array(
                'color' => 'warning',
                'text' => $ext_subject . "\n\n" . json_encode($response_data)
            )
        )
    ));
    $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
}

?>