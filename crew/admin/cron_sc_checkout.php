<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

tep_db_connect();
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

tep_set_time_limit(0);

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION, 'read_db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$payment_gateway = array();

$first_day = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m") - 1, 1, date("Y")));
$last_day = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m"), 1, date("Y")));

# upload to s3
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$output_file_prefix = '';
$output_file_ext = '.csv';
$s3_bucket = 'BUCKET_DATA';
$s3_filepath = 'report/sc_checkout/';

$report_filename = $output_file_prefix . date('YmdHis') . $output_file_ext;

$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key($s3_bucket);
$aws_obj->set_storage('STORAGE_STANDARD');
$aws_obj->set_filepath($s3_filepath);

if ($aws_obj->is_aws_s3_enabled()) {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/' . $report_filename;
} else {
    $file_path = DIR_FS_DOCUMENT_ROOT . 'download/monthly/sc_checkout/' . $report_filename;
}

$fp = fopen($file_path, "w+");
fputcsv($fp, array('Report From ', $first_day, ' until ', $last_day), ',', '"');
fputcsv($fp, array('Order ID', 'SC Amount (USD)', 'PG Amount (USD)', 'PG > PM', 'Customer Group Status Name', 'Customer ID'), ',', '"');

// retrieve all active Payment Gateway and Payment Method
$payment_gateway_select_sql = "(SELECT payment_methods_id, payment_methods_title
								FROM " . TABLE_PAYMENT_METHODS . " 
								WHERE payment_methods_receive_status = '1' )
								UNION 
								(SELECT 0 AS payment_methods_id, 'Full Store Credit' AS payment_methods_title)";
$payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql, 'read_db_link');
while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
	$payment_gateway[$payment_gateway_row['payment_methods_id']] = $payment_gateway_row['payment_methods_title'];
}

$sc_orders_select_sql = "	SELECT o.orders_id, o.customers_id, o.payment_methods_parent_id, o.payment_methods_id, 
								cg.customers_groups_name, 
								ot_gv.value AS ot_gv_value, 
								ot_total.value AS ot_total_value 
							FROM " . TABLE_ORDERS . " AS o
                            INNER JOIN " . TABLE_ORDERS_STATUS_STAT. " AS oss
                                ON (o.orders_id = oss.orders_id AND oss.orders_status_id = 7)
							INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv 
								ON ot_gv.orders_id = o.orders_id 
									AND ot_gv.class='ot_gv' 
									AND ot_gv.value > 0 
							LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_total 
								ON ot_total.orders_id = o.orders_id 
									AND ot_total.class='ot_total' 
							INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
								ON cg.customers_groups_id = o.customers_groups_id 
							WHERE oss.first_date >= '" . $first_day . "' 
								AND oss.first_date < '" . $last_day . "'
								AND o.orders_status IN (2,3,7,8) 
							ORDER BY o.payment_methods_parent_id, o.payment_methods_id";
$sc_order_result_sql = tep_db_query($sc_orders_select_sql, 'read_db_link');
while ($sc_order_row = tep_db_fetch_array($sc_order_result_sql)) {
	$payment_title = '';
	
	if ($sc_order_row['payment_methods_parent_id'] == 0) {
		$payment_title = $payment_gateway[$sc_order_row['payment_methods_parent_id']];
	} else {
		$payment_title = $payment_gateway[$sc_order_row['payment_methods_parent_id']] . ' > ' . $payment_gateway[$sc_order_row['payment_methods_id']];
	}
	
    fputcsv($fp, array($sc_order_row['orders_id'], $sc_order_row['ot_gv_value'], $sc_order_row['ot_total_value'], $payment_title, $sc_order_row['customers_groups_name'], $sc_order_row['customers_id']), ',', '"');
}

fclose($fp);

if ($aws_obj->is_aws_s3_enabled()) {
    $aws_obj->set_file(array('tmp_name' => $file_path));
    $aws_obj->set_filename($report_filename);
    $aws_obj->save_file();
    
    @unlink($file_path);
}
?>