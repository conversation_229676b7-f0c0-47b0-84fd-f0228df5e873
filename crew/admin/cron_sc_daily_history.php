<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

include_once(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
include_once(DIR_WS_CLASSES . 'currencies.php');

tep_set_time_limit(0);

$currencies = new currencies();
$rsc_daily_total_history = array();		// array for RSC daily total balance snapshot
$nrsc_daily_total_history = array();	// array for NRSC daily total balance snapshot
$rsc_daily_history = array();	// array for RSC daily individual balance snapshot
$nrsc_daily_history = array();	// array for NRSC daily individual balance snapshot
$wsc_daily_history = array();	// array for WSC daily balance by customer/supplier snapshot
$wsc_daily_history_currency_total = array(); // array for WSC daily total by currency snapshot

// populate WSC currency total array
$currency_select_sql = "SELECT code FROM ".TABLE_CURRENCIES;
$currency_result_sql = tep_db_query($currency_select_sql);
while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
	$wsc_daily_history_currency_total[$currency_row['code']] = array("balance" => 0, "reserve" => 0, "po_wsc" => 0);
}

$store_credit_daily_history_date = date("Y-m-d H:i:s");	// Set the time for this stock history records

// Get daily total balance for RSC and NRSC credit type
$sc_daily_history_select_sql = " SELECT SUM( sc_reversible_amount ) AS sum_reversible_amount, 
                                        SUM( sc_reversible_reserve_amount ) AS sum_reversible_reserve_amount, 
                                        SUM( sc_irreversible_amount ) AS sum_irreversible_amount, 
                                        SUM( sc_irreversible_reserve_amount ) AS sum_irreversible_reserve_amount,
                                        sc_currency_id
                                 FROM " . TABLE_COUPON_GV_CUSTOMER . "
                                 GROUP BY sc_currency_id";
$sc_daily_history_result_sql = tep_db_query($sc_daily_history_select_sql);
while ($sc_daily_history_row = tep_db_fetch_array($sc_daily_history_result_sql)) {
    $rsc_daily_total_history = array();
	$rsc_daily_total_history['store_credit_daily_history_date']            = $store_credit_daily_history_date;
	$rsc_daily_total_history['store_credit_daily_history_currency']        = $currencies->get_code_by_id($sc_daily_history_row['sc_currency_id']);
	$rsc_daily_total_history['store_credit_daily_history_credit_type']     = 'RSC';
	$rsc_daily_total_history['store_credit_daily_history_amount']          = $sc_daily_history_row['sum_reversible_amount'];
	$rsc_daily_total_history['store_credit_daily_history_reserved_amount'] = $sc_daily_history_row['sum_reversible_reserve_amount'];
	$rsc_daily_total_history['user_id']                                    = 'system';
	$rsc_daily_total_history['user_role']                                  = 'system';
	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $rsc_daily_total_history);
	
    $nrsc_daily_total_history = array();
	$nrsc_daily_total_history['store_credit_daily_history_date']           = $store_credit_daily_history_date;
	$nrsc_daily_total_history['store_credit_daily_history_currency']       = $currencies->get_code_by_id($sc_daily_history_row['sc_currency_id']);
	$nrsc_daily_total_history['store_credit_daily_history_credit_type']    = 'NRSC';
	$nrsc_daily_total_history['store_credit_daily_history_amount']         = $sc_daily_history_row['sum_irreversible_amount'];
	$nrsc_daily_total_history['store_credit_daily_history_reserved_amount']= $sc_daily_history_row['sum_irreversible_reserve_amount'];
	$nrsc_daily_total_history['user_id']                                   = 'system';
	$nrsc_daily_total_history['user_role']                                 = 'system';
	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $nrsc_daily_total_history);
}

// Get daily total balance for store points
$sp_daily_history_select_sql = "	SELECT SUM( sp_amount ) AS sum_sp_amount 
									FROM " . TABLE_STORE_POINTS;
$sp_daily_history_result_sql = tep_db_query($sp_daily_history_select_sql);
while ($sp_daily_history_row = tep_db_fetch_array($sp_daily_history_result_sql)) {
    $sp_daily_total_history = array();
	$sp_daily_total_history['store_credit_daily_history_date']			   = $store_credit_daily_history_date;
	$sp_daily_total_history['store_credit_daily_history_credit_type']	   = 'OP';
	$sp_daily_total_history['store_credit_daily_history_amount']		   = $sp_daily_history_row['sum_sp_amount'];
	$sp_daily_total_history['user_id']                                     = 'system';
	$sp_daily_total_history['user_role']                                   = 'system';
	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $sp_daily_total_history);
}

// Get daily balance of individual for RSC and NRSC credit type
//$sc_daily_history_select_sql = " SELECT sc_reversible_amount, 
//                                        sc_reversible_reserve_amount, 
//                                        sc_irreversible_amount, 
//                                        sc_irreversible_reserve_amount,
//                                        sc_currency_id,
//                                        customer_id
//                                 FROM ".TABLE_COUPON_GV_CUSTOMER;
//$sc_daily_history_result_sql = tep_db_query($sc_daily_history_select_sql);
//while ($sc_daily_history_row = tep_db_fetch_array($sc_daily_history_result_sql)) {
//	$rsc_daily_history['store_credit_daily_history_date']            = $store_credit_daily_history_date;
//	$rsc_daily_history['store_credit_daily_history_currency']        = $currencies->get_code_by_id($sc_daily_history_row['sc_currency_id']);
//	$rsc_daily_history['store_credit_daily_history_credit_type']     = 'RSC';
//	$rsc_daily_history['store_credit_daily_history_amount']          = $sc_daily_history_row['sc_reversible_amount'];
//	$rsc_daily_history['store_credit_daily_history_reserved_amount'] = $sc_daily_history_row['sc_reversible_reserve_amount'];
//	$rsc_daily_history['user_id']                                    = $sc_daily_history_row['customer_id'];
//	$rsc_daily_history['user_role']                                  = 'customer';
//	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $rsc_daily_history);
//	
//	$nrsc_daily_history['store_credit_daily_history_date']           = $store_credit_daily_history_date;
//	$nrsc_daily_history['store_credit_daily_history_currency']       = $currencies->get_code_by_id($sc_daily_history_row['sc_currency_id']);
//	$nrsc_daily_history['store_credit_daily_history_credit_type']    = 'NRSC';
//	$nrsc_daily_history['store_credit_daily_history_amount']         = $sc_daily_history_row['sc_irreversible_amount'];
//	$nrsc_daily_history['store_credit_daily_history_reserved_amount']= $sc_daily_history_row['sc_irreversible_reserve_amount'];
//	$nrsc_daily_history['user_id']                                   = $sc_daily_history_row['customer_id'];
//	$nrsc_daily_history['user_role']                                 = 'customer';
//	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $nrsc_daily_history);
//}

// Get daily balance of individual for point
//$sp_daily_history_select_sql = "	SELECT customers_id, sp_amount FROM " . TABLE_STORE_POINTS;
//$sp_daily_history_result_sql = tep_db_query($sp_daily_history_select_sql);
//while ($sp_daily_history_row = tep_db_fetch_array($sp_daily_history_result_sql)) {
//	$sp_daily_history['store_credit_daily_history_date']			 = $store_credit_daily_history_date;
//	$sp_daily_history['store_credit_daily_history_credit_type']		 = 'OP';
//	$sp_daily_history['store_credit_daily_history_amount']			 = $sp_daily_history_row['sp_amount'];
//	$sp_daily_history['user_id']									 = $sp_daily_history_row['customers_id'];
//	$sp_daily_history['user_role']									 = 'customer';
//	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $sp_daily_history);
//}

// Get customer WSC daily balance, calculate total balance by currency
$wsc_daily_history_select_sql = " SELECT store_account_balance_currency,
                                    SUM(store_account_balance_amount) AS total_account_balance,
                                    SUM(store_account_reserve_amount) AS total_reserve_amount,
                                    SUM(store_account_po_wsc) AS total_po_wsc
                                  FROM ".TABLE_STORE_ACCOUNT_BALANCE . "
                                  GROUP BY store_account_balance_currency";
$wsc_daily_history_result_sql = tep_db_query($wsc_daily_history_select_sql);
while ($wsc_daily_history_row = tep_db_fetch_array($wsc_daily_history_result_sql)) {
	if (!isset($wsc_daily_history_currency_total[$wsc_daily_history_row['store_account_balance_currency']])) {
		$wsc_daily_history_currency_total[$wsc_daily_history_row['store_account_balance_currency']] = array("balance" => 0, "reserve" => 0, "po_wsc" => 0);
	}
    
	$wsc_daily_history_currency_total[$wsc_daily_history_row['store_account_balance_currency']]['balance'] = $wsc_daily_history_row['total_account_balance'];
	$wsc_daily_history_currency_total[$wsc_daily_history_row['store_account_balance_currency']]['reserve'] = $wsc_daily_history_row['total_reserve_amount'];
    $wsc_daily_history_currency_total[$wsc_daily_history_row['store_account_balance_currency']]['po_wsc'] = $wsc_daily_history_row['total_po_wsc'];
}

// Write WSC daily total by currency
foreach ($wsc_daily_history_currency_total as $currency => $totals) {
    $wsc_daily_history = array();
	$wsc_daily_history['store_credit_daily_history_date']            = $store_credit_daily_history_date;
	$wsc_daily_history['store_credit_daily_history_currency']        = $currency;
	$wsc_daily_history['store_credit_daily_history_credit_type']     = 'WSC';
	$wsc_daily_history['store_credit_daily_history_amount']          = $totals['balance'];
	$wsc_daily_history['store_credit_daily_history_reserved_amount'] = $totals['reserve'];
	$wsc_daily_history['user_id']                                    = 'system';
	$wsc_daily_history['user_role']                                  = 'system';
	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $wsc_daily_history);
    
    $wsc_daily_history = array();
	$wsc_daily_history['store_credit_daily_history_date']            = $store_credit_daily_history_date;
	$wsc_daily_history['store_credit_daily_history_currency']        = $currency;
	$wsc_daily_history['store_credit_daily_history_credit_type']     = 'POSC';
	$wsc_daily_history['store_credit_daily_history_amount']          = $totals['po_wsc'];
	$wsc_daily_history['store_credit_daily_history_reserved_amount'] = '0.00';
	$wsc_daily_history['user_id']                                    = 'system';
	$wsc_daily_history['user_role']                                  = 'system';
	tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $wsc_daily_history);
}

if (date('j') == 1) {   // First day of the month then snapshot for every User with WSC
    // Get customer WSC daily balance, calculate total balance by currency
    $wsc_daily_history_select_sql = " SELECT user_id, user_role,
                                             store_account_balance_currency,
                                             store_account_balance_amount,
                                             store_account_reserve_amount
                                      FROM ".TABLE_STORE_ACCOUNT_BALANCE;
    $wsc_daily_history_result_sql = tep_db_query($wsc_daily_history_select_sql);
    while ($wsc_daily_history_row = tep_db_fetch_array($wsc_daily_history_result_sql)) {
        $wsc_daily_history = array();
        $wsc_daily_history['store_credit_daily_history_date']            = $store_credit_daily_history_date;
        $wsc_daily_history['store_credit_daily_history_currency']        = $wsc_daily_history_row['store_account_balance_currency'];
        $wsc_daily_history['store_credit_daily_history_credit_type']     = 'WSC';
        $wsc_daily_history['store_credit_daily_history_amount']          = $wsc_daily_history_row['store_account_balance_amount'];
        $wsc_daily_history['store_credit_daily_history_reserved_amount'] = $wsc_daily_history_row['store_account_reserve_amount'];
        $wsc_daily_history['user_id']                                    = $wsc_daily_history_row['user_id'];
        $wsc_daily_history['user_role']                                  = $wsc_daily_history_row['user_role'];
        tep_db_perform(TABLE_STORE_CREDIT_DAILY_HISTORY, $wsc_daily_history);
    }
}
?>