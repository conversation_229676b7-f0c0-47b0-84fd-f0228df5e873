<?
/*
  $Id: cron_selling_price.php,v 1.9 2011/01/11 08:53:34 keepeng.foong Exp $

   cron_selling_price.php?tpl=1 = sparter
   cron_selling_price.php?tpl=2 = mmobux
*/

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
require_once(DIR_WS_CLASSES . 'export_product.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');
require(DIR_WS_FUNCTIONS . 'configuration.php');

tep_db_connect() or die('Unable to connect to database server!');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

tep_set_time_limit(300);
$languages_id = 1;

$game_id_array = array();

// DEBUG SETTING - PLEASE REMOVE
//$_SERVER['argv'] = array(1, 2, 3, 4);

// Only grab price if the is tpl been passed. Corresponding external parties (Sparter / mmobux)
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
	exit;
} else {
	$tpl = $_SERVER['argv'][1];
}

switch($tpl) {
	case '1':
		$game_id_array = array(194, 195);
		$total_games = count($game_id_array);
		
		for ($game_cnt=0; $game_cnt < $total_games; $game_cnt++) {
			$downloadCatalog = new export_product($game_id_array[$game_cnt], $tpl);
			$downloadCatalog->set_save_location(DIR_FS_CATALOG . 'download');
			$downloadCatalog->get_data_xml($languages_id);
			$downloadCatalog->download(0);
		}
		break;
		
	case '2':
		$category_id_array = tep_get_game_list_arr();
		
		$downloadCatalog = new export_product($category_id_array, $tpl);
		$downloadCatalog->set_save_location(DIR_FS_CATALOG . 'download');
		$downloadCatalog->get_data_xml($languages_id);
		$downloadCatalog->download(0);
		
		break;
		
	case '3':
		$category_id_array = tep_get_game_list_arr();
		
		$downloadCatalog = new export_product($category_id_array, $tpl);
		$downloadCatalog->get_data_html($languages_id);
		$downloadCatalog->download(2);
		break;
		
	case '4':
		$downloadCatalog = new export_product('', $tpl);
		$downloadCatalog->set_save_location(DIR_FS_CATALOG . 'download');
		$downloadCatalog->get_data_xml($languages_id);
		$downloadCatalog->download(3);
		
		break;
}
?>