<?
/*
	Meta Tag Generation
*/
//echo '<pre>';
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

//$s_time = microtime();

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

tep_set_time_limit(500);
tep_db_connect() or die('Unable to connect to database server!');

$entry_count = 0;
$language_key = '';
$custom_products_type_id = '';
$language_id = '';
$cPath = '';
$default_languages_id = 1;
$languages = tep_get_languages();
$flagged_categories_id_array = array();
$game_id_array = array();

//1: en, 2:zh-cn, 3:zh-tw
$language_key_array = array('1' => array(	'buy' => 'Buy ', 
											'cheap' => 'Cheap ', 
											'sell' => 'Sell ', 
											'desc_buy' => 'Buy ',
											'from_offgamers_Your_24_7' => ' from OffGamers. Your 24/7 ',
											'store' => ' store.',
											'affordable' => 'Affordable ',
											'from_offgamers' => ' from OffGamers.',
											'your' => 'Your ',
											'dot' => '.'
										), 
							'2' => array(	'buy' => '购买',
											'cheap' => '优惠', 
											'sell' => '售卖',
											'desc_buy' => '在OffGamers购买',
											'from_offgamers_Your_24_7' => '。 全天24小时',
											'store' => '商铺。',
											'affordable' => '廉价超值的',
											'from_offgamers' => '。',
											'your' => '您的',
											'dot' => '。'
										),
							'3' => array(	'buy' => '購買',
											'cheap' => '優惠', 
											'sell' => '售賣',
											'desc_buy' => '在OffGamers購買',
											'from_offgamers_Your_24_7' => '。 全天24小時',
											'store' => '商舖。',
											'affordable' => '廉價超值的',
											'from_offgamers' => '。',
											'your' => '您的',
											'dot' => '。'
										)
							);
$product_type_key_array = array('0' => 'product_type_keyword_currency', 
								'1' => 'product_type_keyword_pwl', 
								'2' => 'product_type_keyword_cdkey',
								'4' => 'product_type_keyword_pwl');

$flagged_categories_select_sql = " SELECT categories_id FROM " . TABLE_CATEGORIES_SETTING . " WHERE categories_setting_key = 'update_flag' AND categories_setting_value = '1'";
$flagged_categories_result_sql = tep_db_query($flagged_categories_select_sql);
while ($flagged_categories_row = tep_db_fetch_array($flagged_categories_result_sql)) {
	$flagged_categories_id_array[] = $flagged_categories_row['categories_id'];
}

$categories_structures_sql = "	SELECT c.categories_id, c.parent_id, c.categories_parent_path
								FROM " . TABLE_CATEGORIES . " AS c,
									" . TABLE_CATEGORIES_STRUCTURES . " AS cs 
								WHERE FIND_IN_SET(c.categories_id, cs.categories_structures_value)";
$categories_structures_result = tep_db_query($categories_structures_sql);
while ($categories_structures_row = tep_db_fetch_array($categories_structures_result)) {
	if ($categories_structures_row['parent_id'] == 0) {
		if (in_array($categories_structures_row['categories_id'], $flagged_categories_id_array)) {
			$game_id_array[] = array('id' => $categories_structures_row['categories_id'], 'main_id' => $categories_structures_row['categories_id']);
		}
	} else {
		$cat_path_array = explode('_', substr($categories_structures_row['categories_parent_path'], 1, -1));
		if (in_array($cat_path_array[0], $flagged_categories_id_array)) {
			$game_id_array[] = array('id' => $categories_structures_row['categories_id'], 'main_id' => $categories_structures_row['parent_id']);
		}
	}
}

foreach ($game_id_array as $array_index => $game_arr) {
	for ($i=0; $i<sizeof($languages); $i++) {
		$game_array = array();
		
		$language_id = $languages[$i]['id'];
		$language_key = isset($language_key_array[$language_id]) ? $language_id : 1;
		$lang_buy = $language_key_array[$language_key]['buy'];
		$lang_cheap = $language_key_array[$language_key]['cheap'];
		$lang_sell = $language_key_array[$language_key]['sell'];
		
		$game_id = $game_arr['id'];
		$category_name = tep_get_categories_name($game_arr['main_id'], $language_id);
		$category_short_name = tep_get_categories_short_name($game_arr['main_id'], $language_id);
		$category_name_space = mb_detect_encoding($category_name) == 'ASCII' ? ' ' : '';
		$category_short_name_space = mb_detect_encoding($category_short_name) == 'ASCII' ? ' ' : '';
		
		build_SEO ($game_id);
		unset($game_array);
	}
}

tep_db_query("DELETE FROM ".TABLE_CATEGORIES_SETTING." WHERE categories_setting_value = '1' AND categories_setting_key = 'update_flag'");

function build_SEO ($category_id){
	global $game_array, $game_id, $product_type_key_array, $custom_products_type_id, $language_id, $cPath;
	
	// Determine which SEO format to use (Game / Game Type / Others)
	$custom_product_name_array = array();
	$SEO_product_type_keyword = '';
	
	// Get custom_products_type_id
	$custom_products_type_sql = "SELECT categories_parent_path,custom_products_type_id FROM categories WHERE categories_id = ".(int)$category_id;
	$custom_products_type_result = tep_db_query($custom_products_type_sql);
	$custom_products_type_row = tep_db_fetch_array($custom_products_type_result);
	
	$cPath = substr($custom_products_type_row['categories_parent_path'], 1).$category_id;
	$custom_products_type_id = $custom_products_type_row['custom_products_type_id'];
	
	$game_array[$category_id]['category_name'] = tep_get_categories_name($category_id, $language_id);
	
	if ($custom_products_type_id != 999) { //Define Category Type --------------------------------------------------------------------------------------
		$game_array[$category_id]['game_type_id'] = $custom_products_type_id;
	} else {
		$categoryPath_id_array = explode('_', $cPath);
		for ($path_cnt=count($categoryPath_id_array)-1; $path_cnt >= 0; $path_cnt--) {
			if (isset($game_array[$categoryPath_id_array[$path_cnt]]['game_type_id'])) {
				$custom_products_type_id = $game_array[$categoryPath_id_array[$path_cnt]]['game_type_id'];
				break;
			}
		}
	}
	
	if ($category_id == $game_id) {	// Use Game meta format --------------------------------------------------------------------------------------------
		$categoryPath_id_array = explode('_', $cPath);
		
		for ($path_cnt=count($categoryPath_id_array)-1; $path_cnt >= 0; $path_cnt--) { // Check Parent Path For Keywords
			$product_type_keyword_sql = "	SELECT categories_setting_key,categories_setting_value
											FROM " . TABLE_CATEGORIES_SETTING_LANG . "
											WHERE language_id = " . (int)$language_id. "
												AND categories_id =" . (int)$categoryPath_id_array[$path_cnt]. "
												AND categories_setting_key IN ('product_type_keyword_currency','product_type_keyword_pwl','product_type_keyword_cdkey')";			
			$product_type_keyword_result = tep_db_query($product_type_keyword_sql);
			while ($product_type_keyword_row = tep_db_fetch_array($product_type_keyword_result)) {
				if($product_type_keyword_row['categories_setting_value']!=''){
					$custom_product_name_array[$product_type_keyword_row['categories_setting_key']] = explode(',', $product_type_keyword_row['categories_setting_value']);
					$game_array[$category_id][$product_type_keyword_row['categories_setting_key']] = $product_type_keyword_row['categories_setting_value'];
				}
			}
			
			if(tep_not_null($custom_product_name_array))
				break;
		}
		
		if (tep_not_null($custom_product_name_array)) {
			switch(count($custom_product_name_array)) {
				case 3:
					$keyword1 = $custom_product_name_array['product_type_keyword_cdkey'][0];
					$keyword2 = isset($custom_product_name_array['product_type_keyword_cdkey'][1]) ? $custom_product_name_array['product_type_keyword_cdkey'][1] : $keyword1;
					$keyword3 = $custom_product_name_array['product_type_keyword_currency'][0];
					$keyword4 = $custom_product_name_array['product_type_keyword_pwl'][0];
					break;
				case 2:
					$keyword1 = isset($custom_product_name_array['product_type_keyword_cdkey'][0]) ? $custom_product_name_array['product_type_keyword_cdkey'][0] : $custom_product_name_array['product_type_keyword_currency'][0];
					$keyword2 = isset($custom_product_name_array['product_type_keyword_pwl'][0]) ? $custom_product_name_array['product_type_keyword_pwl'][0] : $keyword2 = $custom_product_name_array['product_type_keyword_currency'][0];
					
					$keyword3 = isset($custom_product_name_array['product_type_keyword_cdkey'][1]) ? $custom_product_name_array['product_type_keyword_cdkey'][1] : '';
					if(!tep_not_null($keyword3)) {
						$keyword3 = isset($custom_product_name_array['product_type_keyword_currency'][1]) ? $custom_product_name_array['product_type_keyword_currency'][1] : '';
						if(!tep_not_null($keyword3)) {
							$keyword3 = isset($custom_product_name_array['product_type_keyword_pwl'][1]) ? $custom_product_name_array['product_type_keyword_pwl'][1] : '';
							if(!tep_not_null($keyword3))
								$keyword3 = $keyword1;
						}
					}
						
					$keyword4 = isset($custom_product_name_array['product_type_keyword_pwl'][1]) ? $custom_product_name_array['product_type_keyword_pwl'][1] : '';
					if(!tep_not_null($keyword4)) {
						$keyword4 = isset($custom_product_name_array['product_type_keyword_currency'][1]) ? $custom_product_name_array['product_type_keyword_currency'][1] : '';
						if(!tep_not_null($keyword4)) {
							$keyword4 = isset($custom_product_name_array['product_type_keyword_cdkey'][1]) ? $custom_product_name_array['product_type_keyword_cdkey'][1] : '';
							if(!tep_not_null($keyword4))
								$keyword4 = $keyword2;
						}
					}
					break;
				case 1:
					foreach ($custom_product_name_array as $keywords_array) {
						$keyword1 = $keywords_array[0];
						$keyword2 = isset($keywords_array[1]) ? $keywords_array[1] : $keyword1;
						$keyword3 = isset($keywords_array[2]) ? $keywords_array[2] : $keyword1;
						$keyword4 = isset($keywords_array[3]) ? $keywords_array[3] : $keyword1;
					}
					break;
				case 0:
					$keyword1 = '';
					$keyword2 = '';
					$keyword3 = '';
					$keyword4 = '';
					break;
			}
			$SEO_product_type_keyword = $keyword1.','.$keyword2.','.$keyword3.','.$keyword4;
		}
	} else if ($custom_products_type_id != 999) { // Game type or Game Listing or Game Desc ------------------------------------------------------------
		$product_type_key = $product_type_key_array[$custom_products_type_id];
		
		$product_type_keyword_sql = "	SELECT categories_setting_value AS cfgValue
										FROM " . TABLE_CATEGORIES_SETTING_LANG . "
										WHERE language_id = " . (int)$language_id . "
											AND categories_id = " . (int)$category_id . "
											AND categories_setting_key = '" . $product_type_key . "';";
		$product_type_keyword_result = tep_db_query($product_type_keyword_sql);
		$product_type_keyword_row = tep_db_fetch_array($product_type_keyword_result);
		
		if (tep_not_null($product_type_keyword_row) && $product_type_keyword_row['cfgValue']!='') {
			$SEO_product_type_keyword = $product_type_keyword_row['cfgValue'];
		} else {
			$categoryPath_id_array = explode('_', $cPath);
			
			for ($path_cnt=count($categoryPath_id_array)-1; $path_cnt >= 0; $path_cnt--) {
				if (isset($game_array[$categoryPath_id_array[$path_cnt]][$product_type_key])) {
					$SEO_product_type_keyword = $game_array[$categoryPath_id_array[$path_cnt]][$product_type_key];
					break;
				}
			}
		}
		
		$game_array[$category_id][$product_type_key] = $SEO_product_type_keyword;
	} else { // Others type ----------------------------------------------------------------------------------------------------------------------------
	}
	
	if ($category_id == $game_id || $custom_products_type_id != 999) { // Only Create Game / Game Type & Under Game Type Meta Tag -----------------------
		$product_name_array = explode(',', $SEO_product_type_keyword);
		for ($keyword_count = 0 ; $keyword_count <= 3 ; $keyword_count++) {
			$seo_product[$keyword_count] = trim($product_name_array[$keyword_count % count($product_name_array)]);
		}
		
		$next_category_sql = "SELECT categories_id FROM " . TABLE_CATEGORIES . " WHERE categories_parent_path = '_" . $cPath . "_'";
		$next_category_result = tep_db_query($next_category_sql);
		$next_category_row = tep_db_fetch_array($next_category_result);
		if(tep_not_null($next_category_row)){
			update_format_game_and_gametype ($seo_product);
			
			do { 
				build_SEO ($next_category_row['categories_id']);
			} while ($next_category_row = tep_db_fetch_array($next_category_result));
		} else {
			update_format_gamelisting_and_gamedesc ($seo_product, $category_id);
		}
	}
}

function update_format_game_and_gametype ($seo_product, $region = '') {
	global 	$language_key_array, $language_key, $category_name, $category_short_name,  $category_name_space, $category_short_name_space, 
			$custom_products_type_id, $cPath, $lang_buy, $lang_cheap, $lang_sell;
	
	if ($custom_products_type_id == 999){ // landing game
		$SEO_meta_title  = "$category_name{$region}$category_name_space".$seo_product[0];
		$SEO_meta_title .= ", $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $lang_sell$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_description  = $language_key_array[$language_key]['desc_buy']."$category_short_name{$region} ".$seo_product[0];
		$SEO_meta_description .= ", $category_name{$region} ".$seo_product[1]." & $category_short_name ";
		$SEO_meta_description .= $seo_product[2].$language_key_array[$language_key]['from_offgamers_Your_24_7'];
		$SEO_meta_description .= "$category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['store'];
		
		$SEO_meta_keywords  = "$category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
		$SEO_meta_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
		$SEO_meta_keywords .= ", $category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
		$SEO_meta_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
		$SEO_meta_keywords .= ", ".$seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
		$SEO_meta_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
		
	} else {
		$SEO_meta_title  = "$category_name{$region}$category_name_space".$seo_product[0];
		$SEO_meta_title .= ", $lang_buy$category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_cheap$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_description  = $language_key_array[$language_key]['affordable']."$category_short_name{$region} ".$seo_product[0];
		$SEO_meta_description .= $language_key_array[$language_key]['dot']." ".$language_key_array[$language_key]['desc_buy'];
		$SEO_meta_description .= "$category_name{$region} ".$seo_product[1].", $category_short_name ".$seo_product[2];
		$SEO_meta_description .= " & $category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['from_offgamers'];
		
		$SEO_meta_keywords  = "$category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
		$SEO_meta_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
		$SEO_meta_keywords .= ", $category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
		$SEO_meta_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
		$SEO_meta_keywords .= ", ".$seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
		$SEO_meta_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
	}
	cron_update_category_meta_tag ('cPath='.$cPath, $SEO_meta_title, $SEO_meta_description, $SEO_meta_keywords);
}

function update_format_gamelisting_and_gamedesc ($seo_product, $category_id, $region = '') {
	global  $game_array, $language_key_array, $language_key, $category_name, $category_short_name,  $category_name_space, $category_short_name_space, 
			$custom_products_type_id, $cPath, $lang_buy, $lang_cheap;
	
	$SEO_meta_product_title = '';
	
	$SEO_meta_description  = $language_key_array[$language_key]['your']."$category_short_name{$region} ".$seo_product[0];
	$SEO_meta_description .= $language_key_array[$language_key]['store']." ".$language_key_array[$language_key]['desc_buy'];
	$SEO_meta_description .= "$category_name{$region} ".$seo_product[1].", $category_short_name{$region} ".$seo_product[2];
	$SEO_meta_description .= " & $category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['from_offgamers'];
	
	$SEO_meta_keywords  = $seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
	$SEO_meta_keywords .= ", $category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
	$SEO_meta_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
	$SEO_meta_keywords .= ", $category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
	$SEO_meta_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
	$SEO_meta_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
	
	if($custom_products_type_id == 0) { //currency
		$currency_listing_desc = '';
		$ttl_level = substr_count($cPath,'_');
		
		if($ttl_level >= 3) { //Correct category name start from level 4 onward.
			$cPath_flag_arr = explode('_',$cPath);
			for ($count = 3 ; $count <= $ttl_level ; $count++) {
				$currency_listing_desc .= ' '.$game_array[$cPath_flag_arr[$count]]['category_name'];
			}
		}
		
		$SEO_meta_title  = $seo_product[0].$currency_listing_desc.", $category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
	} else if($custom_products_type_id == 1) { //pwl
		$SEO_meta_title  = $seo_product[0]." ".$game_array[$category_id]['category_name'].", $category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_title  = $seo_product[0]." ##PRODUCT_CATEGORY_NAME##, $category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_product_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_product_title .= " $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_description  = $language_key_array[$language_key]['affordable']."##PRODUCT_CATEGORY_NAME## ".$seo_product[0];
		$SEO_meta_product_description .= $language_key_array[$language_key]['dot']." ".$language_key_array[$language_key]['desc_buy'];
		$SEO_meta_product_description .= "$category_short_name{$region} ".$seo_product[1].", $category_short_name{$region} ".$seo_product[2];
		$SEO_meta_product_description .= " & $category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['from_offgamers'];
		
		$SEO_meta_product_keywords  = $seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
		
	} else if($custom_products_type_id == 2) { //cdkey
		$SEO_meta_title  = "$category_name{$region}$category_name_space".$seo_product[0];
		$SEO_meta_title .= ", $lang_buy$category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_cheap$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_title  = $seo_product[0]." ##PRODUCT_CATEGORY_NAME##, $category_short_name{$region}$category_short_name_space".$seo_product[1];
  		$SEO_meta_product_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
  		$SEO_meta_product_title .= " $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_description  = $language_key_array[$language_key]['affordable']."##PRODUCT_CATEGORY_NAME## ".$seo_product[0];
		$SEO_meta_product_description .= $language_key_array[$language_key]['dot']." ".$language_key_array[$language_key]['desc_buy'];
		$SEO_meta_product_description .= "$category_short_name{$region} ".$seo_product[1].", $category_short_name{$region} ".$seo_product[2];
		$SEO_meta_product_description .= " & $category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['from_offgamers'];
		
		$SEO_meta_product_keywords  = "$category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", ".$seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
	} else if($custom_products_type_id == 4) { //hla
		$SEO_meta_title  = $seo_product[0]." ".$game_array[$category_id]['category_name'].", $category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_title .= ", $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_title  = $seo_product[0]." ##PRODUCT_CATEGORY_NAME##, $category_short_name{$region}$category_short_name_space".$seo_product[1];
		$SEO_meta_product_title .= ", $lang_buy$category_name{$region}$category_name_space".$seo_product[2];
		$SEO_meta_product_title .= " $lang_cheap$category_short_name{$region}$category_short_name_space".$seo_product[3].".";
		
		$SEO_meta_product_description  = $language_key_array[$language_key]['affordable']."##PRODUCT_CATEGORY_NAME## ".$seo_product[0];
		$SEO_meta_product_description .= $language_key_array[$language_key]['dot']." ".$language_key_array[$language_key]['desc_buy'];
		$SEO_meta_product_description .= "$category_short_name{$region} ".$seo_product[1].", $category_short_name{$region} ".$seo_product[2];
		$SEO_meta_product_description .= " & $category_short_name{$region} ".$seo_product[3].$language_key_array[$language_key]['from_offgamers'];
		
		$SEO_meta_product_keywords  = $seo_product[0].", ".$seo_product[1].", ".$seo_product[2].", ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $category_short_name{$region} ".$seo_product[0].", $category_short_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_short_name{$region} ".$seo_product[2].", $category_short_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[0].", $category_name{$region} ".$seo_product[1];
		$SEO_meta_product_keywords .= ", $category_name{$region} ".$seo_product[2].", $category_name{$region} ".$seo_product[3];
		$SEO_meta_product_keywords .= ", $lang_buy$category_short_name{$region} ".$seo_product[0]." & $lang_cheap$category_name{$region} ".$seo_product[0].".";
		
	}
	
	if(tep_not_null($SEO_meta_product_title))
		cron_update_category_meta_tag ('cPath='.$cPath.'&product', $SEO_meta_product_title, $SEO_meta_product_description, $SEO_meta_product_keywords);
	
	cron_update_category_meta_tag ('cPath='.$cPath, $SEO_meta_title, $SEO_meta_description, $SEO_meta_keywords);
}

function cron_update_category_meta_tag ($seo_querystring, $seo_meta_title, $seo_meta_description, $seo_meta_keywords) {
	global $language_id, $entry_count;
	$entry_count+=1;
	
	$seo_meta_sql = "	SELECT seo_meta_id, seo_meta_title_overwrite, seo_meta_description_overwrite, seo_meta_keywords_overwrite 
						FROM " . TABLE_SEO_META_TAG . " 
						WHERE seo_meta_baseurl='".FILENAME_DEFAULT."' 
							AND seo_meta_query_string='".$seo_querystring."' 
							AND language_id = " . (int)$language_id;
	$seo_meta_result = tep_db_query($seo_meta_sql);
	$seo_meta_row = tep_db_fetch_array($seo_meta_result);

	if (tep_not_null($seo_meta_row)) {
		$meta_data_sql = array();
		$meta_data_sql['seo_meta_lastupdate'] = 'now()';
		
		if ($seo_meta_row['seo_meta_title_overwrite'] != "1") {
			$meta_data_sql['seo_meta_title'] = $seo_meta_title;
		}
		if ($seo_meta_row['seo_meta_description_overwrite'] != "1") {
			$meta_data_sql['seo_meta_description'] = $seo_meta_description;
		}
		if ($seo_meta_row['seo_meta_keywords_overwrite'] != "1") {
			$meta_data_sql['seo_meta_keywords'] = $seo_meta_keywords;
		}
		if (count($meta_data_sql)) {
			tep_db_perform(TABLE_SEO_META_TAG, $meta_data_sql, 'update', "seo_meta_id = " . (int)$seo_meta_row['seo_meta_id']);
		}
	} else {
		$meta_data_sql = array(	'language_id' => $language_id,
								'seo_meta_page_type' => '1',
								'seo_meta_baseurl' => FILENAME_DEFAULT,
								'seo_meta_query_string' => $seo_querystring,
								'seo_meta_title' => $seo_meta_title,
								'seo_meta_title_overwrite' => '0',
								'seo_meta_description' => $seo_meta_description,
								'seo_meta_description_overwrite' => '0',
								'seo_meta_keywords' => $seo_meta_keywords,
								'seo_meta_keywords_overwrite' => '0',
								'seo_meta_robots' => '1',
								'seo_meta_lastupdate' => 'now()');
		tep_db_perform(TABLE_SEO_META_TAG, $meta_data_sql);
	}

	if ($entry_count % 50 == 0) {
		sleep(3);
	}
}

//	$end_time = microtime();
//	$time_start = explode(' ', $s_time);
//	$time_end = explode(' ', $end_time);
//	$totaltime = ($time_end[1] + $time_end[0]) - ($time_start[1] + $time_start[0]);
//	echo 'Take Time: ' . $totaltime;
//	exit;
?>