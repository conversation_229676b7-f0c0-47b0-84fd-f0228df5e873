<?php

// Include application configuration parameters
require('includes/configure.php');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');
require(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$memcache_obj = new OGM_Cache_MemCache();

tep_set_time_limit(0);

// make a connection to the database... now
tep_db_connect();

// Setting
$order_count_to_process = 300;
$effective_date = '2015-04-01 00:00:00';
$current_date = date('Y-m-d H:i:s', mktime(date('H'), date('i'), 0, date('m'), date('d'), date('Y')));
$from_date = $effective_date;
$tax_product_category = array(
    21392 => "MY"
);
# TODO: update date when live
$einvoice_effective_date = '2025-07-01 00:00:00';

if (strtotime($current_date) - strtotime($effective_date) <= 0) {
    echo "Not a valid period yet!";
    exit;
}

// Retrive all Verifying Order 
$last_exe_select_sql = "SELECT last_run_datetime
                        FROM invoice_queue_date";
$last_exe_result_sql = tep_db_query($last_exe_select_sql);
if ($last_exe_row = tep_db_fetch_array($last_exe_result_sql)) {
    $from_date = $last_exe_row['last_run_datetime'];
} else {
    $insert_sql_data = array('last_run_datetime' => $from_date);
    tep_db_perform('invoice_queue_date', $insert_sql_data);
}

$verifying_order_select_sql = " SELECT os.orders_id, os.occurrence, os.latest_date, oei.orders_extra_info_key, oei.orders_extra_info_value
                                FROM orders_status_stat AS os
                                INNER JOIN orders AS o ON os.orders_id = o.orders_id
                                LEFT JOIN orders_extra_info AS oei on os.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'use_g2g_pg' 
                                WHERE os.latest_date >= '" . $from_date . "'
                                    AND os.latest_date < '" . $current_date . "'
                                    AND o.customers_country_international_dialing_code != '60'
                                    AND os.orders_status_id=7 ORDER BY os.latest_date DESC";
$verifying_order_result_sql = tep_db_query($verifying_order_select_sql);
while ($verifying_order_row = tep_db_fetch_array($verifying_order_result_sql)) {
    //exclude if use_g2g_pg = 1;
    if ($verifying_order_row['orders_extra_info_key'] == "use_g2g_pg") {
        //do nothing
    } else {
        if ($verifying_order_row['occurrence'] > 1) {   // Rollback occurred. Check if is from Pending to Verifying
            $rollback_order_select_sql = "  SELECT orders_status_id
                                        FROM orders_status_stat
                                        WHERE latest_date < '" . $verifying_order_row['latest_date'] . "'
                                            AND orders_id = '" . $verifying_order_row['orders_id'] . "' 
                                        ORDER BY latest_date DESC
                                        LIMIT 1";
            $rollback_order_result_sql = tep_db_query($rollback_order_select_sql);
            if ($rollback_order_row = tep_db_fetch_array($rollback_order_result_sql)) {
                if ($rollback_order_row['orders_status_id'] == '1') { // Rollback from Pending
                    $insert_sql_data = array(
                        'created_datetime' => $verifying_order_row['latest_date'],
                        'orders_id' => $verifying_order_row['orders_id'],
                        'extra_info' => 'ALL'
                    );
                    tep_db_perform('invoice_queue', $insert_sql_data);
                }
            }
        } else {
            $insert_sql_data = array(
                'created_datetime' => $verifying_order_row['latest_date'],
                'orders_id' => $verifying_order_row['orders_id'],
                'extra_info' => 'ALL'
            );
            tep_db_perform('invoice_queue', $insert_sql_data);
        }
    }
}

// Get Completed Orders from MY Buyers
$completed_order_select_sql = " SELECT os.orders_id, os.occurrence, os.latest_date, oei.orders_extra_info_key, oei.orders_extra_info_value
                                FROM orders_status_stat AS os
                                INNER JOIN orders AS o ON os.orders_id = o.orders_id
                                LEFT JOIN orders_extra_info AS oei on os.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'use_g2g_pg' 
                                WHERE os.latest_date >= '" . $from_date . "'
                                    AND os.latest_date < '" . $current_date . "'
                                    AND o.customers_country_international_dialing_code = '60'
                                    AND os.occurrence = 1
                                    AND os.orders_status_id=3 ORDER BY os.latest_date DESC";
$completed_order_result_sql = tep_db_query($completed_order_select_sql);
while ($completed_order_row = tep_db_fetch_array($completed_order_result_sql)) {
    $insert_sql_data = array(
        'created_datetime' => $completed_order_row['latest_date'],
        'orders_id' => $completed_order_row['orders_id'],
        'extra_info' => 'ALL'
    );
    tep_db_perform('invoice_queue', $insert_sql_data);
}

// Update last execution time
$last_exe_update_sql = "UPDATE invoice_queue_date SET last_run_datetime = '" . $current_date . "'";
tep_db_query($last_exe_update_sql);

$currencies = new currencies();

$aws_obj = new ogm_amazon_ws();
$params = array(
    'key' => AWS_SQS_KEY,
    'secret' => AWS_SQS_SECRET,
);
$aws_obj->init_sqs(AWS_SQS_INVOICE_QUEUE_URL, $params);

$og_aws_obj = new ogm_amazon_ws();
$og_aws_obj->init_sqs(AWS_SQS_PDF_QUEUE_URL, $params);

$tax_country_array = $order_extra_array = array();
$tax_select_sql = " SELECT otc.country_code, otc.company_name, otc.address_1, otc.address_2, otc.address_3, otc.company_logo, otc.contact, otc.website,
                        otc.gst_registration_no, otcd.orders_tax_title, otcd.orders_tax_title_short, otc.tax_registration_name, otc.tax_invoice_title, orders_provide_invoice_status
                    FROM orders_tax_configuration AS otc 
                    INNER JOIN orders_tax_configuration_description AS otcd 
                        ON otc.orders_tax_id = otcd.orders_tax_id 
                        AND otcd.language_id = '1' 
                    WHERE 1";
$tax_result_sql = tep_db_query($tax_select_sql);
while ($tax_row = tep_db_fetch_array($tax_result_sql)) {
    $tax_country_array[$tax_row['country_code']] = $tax_row;
}

// Get Order from Invoice Queue
$filetype = 'invoice';

$queue_select_sql = "   SELECT *
                        FROM invoice_queue 
                        WHERE 1
                        ORDER BY created_datetime
                        LIMIT " . $order_count_to_process;
$queue_result_sql = tep_db_query($queue_select_sql);
while ($queue_row = tep_db_fetch_array($queue_result_sql)) {
    $merchant = 'ogm';
    $order_id = $queue_row['orders_id'];
    $order_date = $queue_row['created_datetime'];
    $order_extra_array = array();

    $order_extra_info_select_sql = "SELECT orders_extra_info_key, orders_extra_info_value
                                    FROM orders_extra_info
                                    WHERE orders_id = '" . $order_id . "'";
    $order_extra_info_result_sql = tep_db_query($order_extra_info_select_sql);
    while ($order_extra_info_row = tep_db_fetch_array($order_extra_info_result_sql)) {
        $order_extra_array[$order_extra_info_row['orders_extra_info_key']] = $order_extra_info_row['orders_extra_info_value'];
    }

    //g2g orders
    if (isset($order_extra_array['site_id']) && $order_extra_array['site_id'] == "5") {
        $receiver_email = '';
        //if checkout by pipwave
        if (isset($order_extra_array['checkoutSite']) && $order_extra_array['checkoutSite'] == "pipwave") {
            $pipwave_payment_select_sql = "SELECT pp.pg_raw_data, ppm.pg_code
                                            FROM pipwave_payment AS pp
                                            INNER JOIN pipwave_payment_mapper AS ppm
                                            ON pp.payment_method_code = ppm.pipwave_payment_code
                                            WHERE ppm.site_id = '5'
                                                  AND pp.orders_id = '" . $order_id . "'";

            $pipwave_payment_result_sql = tep_db_query($pipwave_payment_select_sql);
            if ($pipwave_payment_row = tep_db_fetch_array($pipwave_payment_result_sql)) {
                switch ($pipwave_payment_row['pg_code']) {
                    case 'paypalEC':
                        $rawData = json_decode($pipwave_payment_row['pg_raw_data'], true);
                        $receiver_email = (isset($rawData['receiver_email']) ? $rawData['receiver_email'] : '');
                        break;
                }
            }
        } else {
            //check if match g2g orders criteria
            $paypalec_select_sql = "SELECT receiver_email 
                                    FROM paypalec
                                    WHERE paypal_order_id = '" . $order_id . "'";
            $paypalec_result_sql = tep_db_query($paypalec_select_sql);
            if ($paypalec_row = tep_db_fetch_array($paypalec_result_sql)) {
                $receiver_email = $paypalec_row['receiver_email'];
            }
        }

        //hardcode
        if ($receiver_email == G2G_PAYPAL_RECEIVER_EMAIL) {
            //removed after 1st of april
            $merchant = "g2g";
            //fire API to g2g db, generaet invoice from g2g
            include_once(DIR_WS_CLASSES . 'c2c_invoice.php');
            c2c_invoice::create_c2c_invoice_queue($order_id, 'purchase', strtotime($order_date));
            // Delete invoice queue
            $delete_queue_sql = "DELETE FROM invoice_queue WHERE orders_id = '" . $order_id . "' AND created_datetime = '" . $order_date . "'";
            tep_db_query($delete_queue_sql);
            continue;
        }
    }

    $order_select_sql = "   SELECT o.customers_id, o.customers_name, o.customers_email_address, o.customers_telephone, o.currency, o.currency_value, o.payment_methods_id, o.billing_street_address, o.billing_suburb, o.billing_city, o.billing_postcode, o.billing_state, o.billing_country, o.customers_country_international_dialing_code, o.payment_method, o.date_purchased,
                            ppm.pipwave_payment_code 
                            FROM orders AS o 
                            LEFT JOIN pipwave_payment_mapper AS ppm 
                            ON ppm.pm_id = o.payment_methods_id 
                            WHERE o.orders_id = '" . $order_id . "'";
    $order_result_sql = tep_db_query($order_select_sql);
    $order_row = tep_db_fetch_array($order_result_sql);

    $payment_title = (isset($order_row['payment_method']) && tep_not_null($order_row['payment_method'])) ? $order_row['payment_method'] : '-';
    $payment_code = (isset($order_row['pipwave_payment_code']) && tep_not_null($order_row['pipwave_payment_code'])) ? $order_row['pipwave_payment_code'] : '';

    if ($merchant == "g2g") {
        //generate G2G invoice
        if (isset($order_extra_array['tax_country'])) {
            $invoice_template = 'tax-invoice';
            $invoice_number = cron_tep_get_g2g_invoice_number($order_extra_array['tax_country'], $order_date);
        } else {
            $invoice_template = 'invoice';
            $invoice_number = cron_tep_get_g2g_invoice_number('GENERAL', $order_date);
        }

        if ($invoice_template == 'tax-invoice') {
            //hardcode
            $invoice_company = array(
                "name" => "Gamer2Gamer Sdn Bhd (1051802-M)",
                "address_1" => "Unit B-G-07, SME Technopreneur Centre 2 Cyberjaya, 2260,",
                "address_2" => "Jalan Usahawan 1, Cyber 6, 63000 Cyberjaya, Selangor Darul Ehsan.",
                "contact" => "+603 92234673",
                "website" => "www.g2g.com",
                "info" => "GST Registration No.: -",
                "logo" => 'https://d32b8ciqbrecdn.cloudfront.net/G2G/gallery/tax/g2g_logo.jpg'
            );
        } else {
            //hardcode
            $invoice_company = array(
                "name" => 'G2G.com',
                "website" => 'www.g2g.com',
                "logo" => 'https://d32b8ciqbrecdn.cloudfront.net/G2G/gallery/tax/g2g_logo.jpg'
            );
        }
    } else {
        if (isset($order_extra_array['tax_country'])) {
            // product specific tax-invoice country
            $_sql = "SELECT products_categories_id FROM orders_products WHERE orders_id = " . $order_id;
            $_res = tep_db_query($_sql);
            while ($_row = tep_db_fetch_array($_res)) {
                if (isset($tax_product_category[$_row["products_categories_id"]]) && isset($tax_country_array[$tax_product_category[$_row["products_categories_id"]]])) {
                    $order_extra_array['tax_country'] = $tax_product_category[$_row["products_categories_id"]];
                    break;
                }
            }

            if (isset($tax_country_array[$order_extra_array['tax_country']])) {
                $invoice_template = 'tax-invoice';
                $invoice_number = cron_tep_get_invoice_number($order_extra_array['tax_country'], $order_date);
            } else {
                if ((isset($order_extra_array['tax_surcharge_amount']) && ($order_extra_array['tax_surcharge_amount'] > 0)) ||
                        (isset($order_extra_array['tax_amount']) && ($order_extra_array['tax_amount'] > 0))) {
                    // default tax-invoice template SG
                    $order_extra_array['tax_country'] = "SG";
                    $invoice_template = 'tax-invoice';
                    $invoice_number = cron_tep_get_invoice_number($order_extra_array['tax_country'], $order_date);
                } else {
                    $invoice_template = 'invoice';
                    $invoice_number = cron_tep_get_invoice_number('GENERAL', $order_date);
                }
            }
        } else {
            $invoice_template = 'invoice';
            $invoice_number = cron_tep_get_invoice_number('GENERAL', $order_date);
        }

        if ($invoice_template == 'tax-invoice') {
            $invoice_company = array(
                "name" => $tax_country_array[$order_extra_array['tax_country']]['company_name'],
                "address_1" => $tax_country_array[$order_extra_array['tax_country']]['address_1'],
                "address_2" => $tax_country_array[$order_extra_array['tax_country']]['address_2'] . ' ' . $tax_country_array[$order_extra_array['tax_country']]['address_3'],
                "contact" => $tax_country_array[$order_extra_array['tax_country']]['contact'],
                "website" => $tax_country_array[$order_extra_array['tax_country']]['website'],
                "info" => $tax_country_array[$order_extra_array['tax_country']]['tax_registration_name'] . " : " . $tax_country_array[$order_extra_array['tax_country']]['gst_registration_no'],
                "tax_invoice_title" => (!empty($order_extra_array['tax_invoice_title']) ? $order_extra_array['tax_invoice_title'] : $tax_country_array[$order_extra_array['tax_country']]['tax_invoice_title']),
                "logo" => 'http://d130xiciw9h9wz.cloudfront.net/gallery/tax/20150326123357.jpg'
                    // "logo" => "http://image.offgamers.com/" . $tax_country_array[$order_extra_array['tax_country']]['company_logo']
            );
        } else {
            $invoice_company = array(
                "name" => 'OffGamers.com',
                "website" => 'www.offgamers.com',
                "logo" => 'http://d130xiciw9h9wz.cloudfront.net/gallery/tax/20150326123357.jpg'
            );
        }
    }

    $country_info = tep_get_countries_info($order_extra_array['payment_ip_country'], 'countries_iso_code_2');

    $invoice_customer = array(
        "id" => $order_row['customers_id'],
        "name" => $order_row['customers_name'],
        'email' => $order_row['customers_email_address'],
        'mobile_country_code' => (tep_not_null($order_row['customers_country_international_dialing_code']) ? $order_row['customers_country_international_dialing_code'] : ''),
        "address" => (tep_not_null($order_row['billing_street_address']) ? $order_row['billing_street_address'] . ', ' : '') .
        (tep_not_null($order_row['billing_suburb']) ? $order_row['billing_suburb'] . ', ' : '') .
        (tep_not_null($order_row['billing_city']) ? $order_row['billing_city'] . ', ' : '') .
        (tep_not_null($order_row['billing_postcode']) ? $order_row['billing_postcode'] . ', ' : '') .
        (tep_not_null($order_row['billing_state']) ? $order_row['billing_state'] . ', ' : '') .
        $order_row['billing_country']
    );

    $tax_title = '';
    if ($invoice_template == 'tax-invoice') {
        if (!empty($order_extra_array['tax_short_title'])) {
            $tax_title = $order_extra_array['tax_short_title'];
        } elseif (!empty($tax_country_array[$order_extra_array['tax_country']]['orders_tax_title_short'])) {
            $tax_title = $tax_country_array[$order_extra_array['tax_country']]['orders_tax_title_short'];
        }
    }

    $ot_info = cron_tep_get_order_total($order_id, $order_row['currency'], $order_row['currency_value'], $currencies, $tax_title);
    $portal = (isset($order_extra_array['site_id']) && $order_extra_array['site_id'] == '5' ? 'G2G' : 'OGM');

    $remark = "";
    if ($portal == "OGM") {
        $sql = "SELECT settlement_account FROM pipwave_payment WHERE orders_id = '" . $order_id . "'";
        $res = tep_db_query($sql);
        $row = tep_db_fetch_array($res);
        if (isset($row["settlement_account"]) && !empty($row["settlement_account"])) {
            preg_match('#\[(.*?)\]#', $row["settlement_account"], $matches);
            $settlement = isset($matches[1]) ? strtoupper($matches[1]) : "";
            if (($settlement == "OGMY") && ($order_row['currency'] <> "MYR")) {
                $sql = " SELECT value FROM orders_total WHERE orders_id = '" . $order_id . "' AND class= 'ot_total'";
                $res = tep_db_query($sql);
                $row = tep_db_fetch_array($res);
                $order_total = $row['value'] * $order_row['currency_value'];

                $equal_rate = $currencies->currency_history_rate_conversion($order_row['currency'], "MYR", $queue_row['created_datetime']);
                $equal_total = ($order_total * $equal_rate);
                $remark = '<b>For Internal Use only:</b><br />'
                        . '<b>SUBTOTAL ≈ MYR ' . number_format($equal_total, $currencies->currencies["MYR"]['decimal_places'], $currencies->currencies["MYR"]['decimal_point'], $currencies->currencies["MYR"]['thousands_point']) . '</b><br /><br />'
                        . '<b>*1.00 ' . $order_row['currency'] . ' ≈ MYR ' . $equal_rate . '</b><br />'
                        . '≈ Amount shown is approximate and subject to currency exchange rates';
            }
        }
    }

    $invoice_data = array(
        "merchant" => $merchant,
        "template" => $invoice_template,
        "filename" => $invoice_number . '.pdf',
        "filetype" => $filetype,
        "company" => $invoice_company,
        "customer" => $invoice_customer,
        "checkout_info" => array(
            "invoice_number" => $invoice_number,
            "invoice_date" => date("d/m/Y", strtotime($order_date)),
            "order_number" => $order_id,
            "credit_note_number" => '',
            "credit_note_date" => '',
            "ip" => $order_extra_array['payment_ip'],
            "ip_country" => $country_info['countries_name'],
            "tax_country" => $order_extra_array['payment_ip_country'],
            "tax_title" => $order_extra_array['tax_short_title'],
            "tax_reverse_charge" => (!empty($order_extra_array['tax_invoice_reverse_charge']) ? 1 : 0),
            "currency" => $order_row['currency'],
            "total" => $ot_info['total'],
            "payment_code" => $payment_code,
            "payment_method" => $payment_title,
            'portal' => $portal
        ),
        'product' => cron_tep_get_order_product($order_id, $order_row['currency'], $order_row['currency_value'], $currencies, $portal, $invoice_template, (isset($order_extra_array['tax_country']) ? $order_extra_array['tax_country'] : "")),
        'order_total' => $ot_info['ot'],
        'remark' => $remark
    );

    if (isset($order_extra_array['tax_country']) && isset($tax_country_array[$order_extra_array['tax_country']]['orders_provide_invoice_status'])) {
        $invoice_data['send_email'] = $tax_country_array[$order_extra_array['tax_country']]['orders_provide_invoice_status'];
    } else {
        $invoice_data['send_email'] = true;
    }

    if ($invoice_data['checkout_info']['tax_reverse_charge']) {
        $invoice_data['checkout_info']['tax_normal_percentage'] = $order_extra_array['tax_normal_percentage'];
        $invoice_data['checkout_info']['tax_invoice_reverse_amount'] = number_format($order_extra_array['tax_invoice_reverse_amount'] * $order_row['currency_value'], 2, '.', '');
    }

    $insert_sql_data = array(
        'invoice_number' => $invoice_number,
        'invoice_type' => $invoice_template,
        'orders_id' => $order_id,
        'file_raw_data' => json_encode($invoice_data),
        'created_datetime' => $order_date,
        'last_modified_datetime' => 'now()',
    );

    if ($merchant == "g2g") {
        tep_db_perform('c2c_invoice', $insert_sql_data);
        $invoice_id = tep_db_insert_id();
    } else {
        tep_db_perform('invoice', $insert_sql_data);
        $invoice_id = tep_db_insert_id();
    }

    $einvoice_payload = $cust_profile = [];
    $is_my_buyer = $order_row['customers_country_international_dialing_code'] == "60";
    $is_requested_einvoice = false;

    if ($is_my_buyer){
        $profile_result_sql = tep_db_query("SELECT field_key, value FROM customers_profile WHERE customers_id = '" . (int) $order_row['customers_id'] . "'");
        $cust_profile = tep_db_fetch_array($profile_result_sql);

        if (tep_db_num_rows($profile_result_sql) > 0){
            $is_requested_einvoice = true;
        }
    }

    // TODO: After phase3, data collection is not needed anymore -> add condition to check is_requested_einvoice
    $required_einvoice_submission = (strtotime($order_row["date_purchased"]) > strtotime($einvoice_effective_date)) && $is_my_buyer;

    if ($required_einvoice_submission) {
        $order_row["completed_at"] = $order_date;
        $einvoice_payload = construct_einvoice_payload($invoice_data, $order_row, $cust_profile);
        tep_db_perform('einvoice', [
            'invoice_id' => $invoice_id,
            'orders_id' => $order_id,
            'file_raw_data' => json_encode($einvoice_payload),
            'einvoice_status' => 1,
            'created_at' => 'now()',
            'updated_at' => 'now()',
        ]);
    }
    else{
        if ($merchant == "g2g") {
            $aws_obj->sendMessage(json_encode($invoice_data));
        } else {
            $og_aws_obj->sendMessage(json_encode(['data' => $invoice_data]));
            $memcache_obj->delete('sqs/' . md5(AWS_SQS_PDF_QUEUE_URL) . '/flag', 0);
        }
    }

    // Delete invoice queue
    $delete_queue_sql = "DELETE FROM invoice_queue WHERE orders_id = '" . $order_id . "' AND created_datetime = '" . $order_date . "'";
    tep_db_query($delete_queue_sql);
}

function construct_einvoice_payload($invoice_data, $order_row, $cust_profile = [])
{
    $payload = [
        "invoice_category" => "sale",
        "einvoice_document_type" => "invoice",
        "buyer_info" => [
            "is_required_einvoice" => false
        ],
        "system_source" => "legacy",
        "order_raw_data" => []
    ];

    $product = $invoice_data["product"][0];
    if ((isset($product["custom_product_type_id"]) && $product["custom_product_type_id"] == 3)) {
        $payload["is_store_credit_topup"] = true;
    }

    $payload["order_raw_data"]["order_item"] = [
        "order_id" => $invoice_data["checkout_info"]["order_number"],
        "offer_title" => $product["name"],
        "unit_price" => $product["unit_price"],
        "item_amount" =>  $product["total_price"],
        "delivered_qty" => $product["quantity"],
        "purchased_qty" => $product["quantity"],
        "completed_at" => strtotime($order_row["completed_at"]) * 1000,
        "created_at" => strtotime($order_row["date_purchased"]) * 1000,
    ];

    $processing_fee_amount = 0;
    if (!empty($invoice_data["order_total"])){
        foreach ($invoice_data["order_total"] as $ot_id => $ot) {
            foreach ($ot as $ot_class_id => $ot_class) {
                if (isset($ot_class['type']) && $ot_class['type'] == "ot_surcharge") {
                    $processing_fee_amount = $ot_class["value"];
                    break 2;
                }
            }
        }
    }

    $payload["order_raw_data"]["order"] = [
        "checkout_currency" => $invoice_data["checkout_info"]["currency"],
        "created_at" => strtotime($order_row["date_purchased"]) * 1000,
        "order_id" => $invoice_data["checkout_info"]["order_number"],
        "processing_fee_amount" => $processing_fee_amount,
        "total_amount" => $invoice_data["checkout_info"]["total"], //note: if use full SC to checkout, total amount will be 0
    ];

    $payload["order_raw_data"]["buyer"] = [
        "buyer_id" => $invoice_data["customer"]["id"],
        "email" => $invoice_data["customer"]["email"],
        "first_name" => $invoice_data["customer"]["name"],
        "last_name" => "",
        "username" => $invoice_data["customer"]["name"],
        "contact_no" => $order_row['customers_telephone'],
        "contact_no_country_iso2" => $invoice_data["customer"]["mobile_country_code"],
        "contact_no_country_code" => "MY",
        "national_id_number" => isset($cust_profile["identity_number"]) ? $cust_profile["identity_number"] : "",
        "company_name" => isset($cust_profile['company_name']) ? $cust_profile['company_name'] : "",
        "tax_number" => isset($cust_profile["tax_reg_number"]) ? $cust_profile["tax_reg_number"] : "",
        "tax_country" => isset($cust_profile['tax_country_code']) ? $cust_profile['tax_country_code'] : "",
        "registration_number" => isset($cust_profile["company_reg_number"]) ? $cust_profile["company_reg_number"] : "",
        "sst_number" => "",
        "msic_code" => "",
        "checkout_ip_address" => $invoice_data["checkout_info"]["ip"],
        "checkout_ip_country" => $invoice_data["checkout_info"]["ip_country"],
        "billing_info" => [
            'zip' => tep_not_null($order_row['billing_postcode']) ? $order_row['billing_postcode'] : "",
            'country' => tep_not_null($order_row['billing_country']) ? $order_row['billing_country'] : "",
            'address_2' => tep_not_null($order_row['billing_suburb']) ?  $order_row['billing_suburb'] : "",
            'city' => tep_not_null($order_row['billing_city']) ? $order_row['billing_city'] : "",
            'address_1' => tep_not_null($order_row['billing_street_address']) ? $order_row['billing_street_address'] : "",
            'name' => $invoice_data["customer"]["name"],
            'country_name' => tep_not_null($order_row['billing_country']) ? $order_row['billing_country'] : "",
            'state' => tep_not_null($order_row['billing_state']) ? $order_row['billing_state'] : "",
            'country_iso2' => tep_get_countries_info($order_row["billing_country"], "countries_name")["countries_iso_code_2"],
        ],
    ];
    
    $payload["order_raw_data"]["invoice_number"] = $invoice_data["checkout_info"]["invoice_number"];
    $payload["order_raw_data"]["invoice"] = $invoice_data;

    return $payload;
}

function cron_tep_get_invoice_number($tax_country, $date)
{
    $order_month = date('n', strtotime($date));
    $order_year = date('Y', strtotime($date));

    $invoice_number = 1;

    #LOCK TABLE
    tep_db_query("LOCK TABLES invoice_running_number WRITE;");

    $inv_no_select_sql = "  SELECT invoice_number, invoice_month
                            FROM invoice_running_number
                            WHERE country_iso2 = '" . $tax_country . "' 
                                AND invoice_month = " . (int) $order_month . "
                                AND invoice_year = " . $order_year;
    $inv_no_result_sql = tep_db_query($inv_no_select_sql);
    if ($inv_no_row = tep_db_fetch_array($inv_no_result_sql)) {
        $invoice_number = $inv_no_row['invoice_number'] + 1;
        $update_sql_data = array('invoice_number' => $invoice_number);

        tep_db_perform('invoice_running_number', $update_sql_data, 'update', " country_iso2 = '" . $tax_country . "' AND invoice_month = " . (int) $order_month . " AND invoice_year = " . $order_year);
    } else {
        $insert_sql_data = array(
            'country_iso2' => $tax_country,
            'invoice_number' => $invoice_number,
            'invoice_month' => $order_month,
            'invoice_year' => $order_year
        );
        tep_db_perform('invoice_running_number', $insert_sql_data);
    }

    tep_db_query("UNLOCK TABLES;");

    return 'INV' . ($tax_country != 'GENERAL' ? strtoupper($tax_country) : '') . date("Ym", strtotime($date)) . str_pad($invoice_number, 6, '0', STR_PAD_LEFT);
}

//generate g2g invoice number
function cron_tep_get_g2g_invoice_number($tax_country, $date)
{
    $order_month = date('n', strtotime($date));
    $order_year = date('Y', strtotime($date));

    $invoice_number = 1;

    #LOCK TABLE
    tep_db_query("LOCK TABLES c2c_invoice_running_number WRITE;");

    $inv_no_select_sql = "  SELECT invoice_number, invoice_month
                            FROM c2c_invoice_running_number
                            WHERE country_iso2 = '" . $tax_country . "' 
                                AND invoice_month = " . (int) $order_month . "
                                AND invoice_year = " . $order_year;
    $inv_no_result_sql = tep_db_query($inv_no_select_sql);
    if ($inv_no_row = tep_db_fetch_array($inv_no_result_sql)) {
        $invoice_number = $inv_no_row['invoice_number'] + 1;
        $update_sql_data = array('invoice_number' => $invoice_number);

        tep_db_perform('c2c_invoice_running_number', $update_sql_data, 'update', " country_iso2 = '" . $tax_country . "' AND invoice_month = " . (int) $order_month . " AND invoice_year = " . $order_year);
    } else {
        $insert_sql_data = array(
            'country_iso2' => $tax_country,
            'invoice_number' => $invoice_number,
            'invoice_month' => $order_month,
            'invoice_year' => $order_year
        );
        tep_db_perform('c2c_invoice_running_number', $insert_sql_data);
    }

    tep_db_query("UNLOCK TABLES;");

    return 'GINV' . ($tax_country != 'GENERAL' ? strtoupper($tax_country) : '') . date("Ym", strtotime($date)) . str_pad($invoice_number, 6, '0', STR_PAD_LEFT);
}

#TODO: check invoice template

function cron_tep_get_order_product($order_id, $orderCurrency, $rate, $currencies, $portal, $invoice_template, $tax_country = '')
{
    $invoice_products = array();

    $order_product_select_sql = "   SELECT orders_products_id, products_id, products_name, products_quantity, final_price, custom_products_type_id, parent_orders_products_id
                                    FROM orders_products op
                                    WHERE orders_id = '" . $order_id . "'
                                        AND parent_orders_products_id=0
                                        AND orders_products_is_compensate=0";
    $order_product_result_sql = tep_db_query($order_product_select_sql);
    while ($order_product_row = tep_db_fetch_array($order_product_result_sql)) {
        $decimal_places = $portal == 'G2G' || $order_product_row['custom_products_type_id'] == 3 ? 6 : $currencies->currencies[$orderCurrency]['decimal_places'];
        $format_unit_price = number_format($order_product_row['final_price'] * $rate, $decimal_places, $currencies->currencies[$orderCurrency]['decimal_point'], $currencies->currencies[$orderCurrency]['thousands_point']);
        $unit_price = number_format($order_product_row['final_price'] * $rate, $decimal_places, $currencies->currencies[$orderCurrency]['decimal_point'], '');

        $total_amount_number = number_format($order_product_row['products_quantity'] * $unit_price, $currencies->currencies[$orderCurrency]['decimal_places'], $currencies->currencies[$orderCurrency]['decimal_point'], '');
        $total_amount = number_format($order_product_row['products_quantity'] * $unit_price, $currencies->currencies[$orderCurrency]['decimal_places'], $currencies->currencies[$orderCurrency]['decimal_point'], $currencies->currencies[$orderCurrency]['thousands_point']);

        if ($tax_country == 'MY') {
            if ($invoice_template == 'tax-invoice' && in_array($order_product_row['products_id'], array(166516, 120561, 63551, 40061, 40062, 40063, 40064, 140657, 171599, 171477, 171478, 172155, 172156, 172157, 172158, 172160, 172161, 172162, 172163, 172164, 172165, 173297, 173298, 173695, 173696, 173697, 173698, 173699, 174601, 174564, 174567))) {
                $total_amount .= '&nbsp;(OS)';
            }
        }
        $products_data = array(
            "name" => $order_product_row['products_name'],
            "quantity" => $order_product_row['products_quantity'],
            "unit_price" => $format_unit_price,
            "total_price_number" => $total_amount_number,
            "total_price" => $total_amount,
            "custom_product_type_id" => $order_product_row['custom_products_type_id'],
        );
        if ($invoice_template == 'tax-invoice' && $order_product_row['parent_orders_products_id'] == 0) {
            $tax_exempted_amount = checkTaxExemptionAmount($order_product_row['orders_products_id']);
            if ($tax_exempted_amount > 0) {
                $products_data['tax_exempted_amount'] = $tax_exempted_amount;
            }
        }
        $invoice_products[] = $products_data;
    }

    return $invoice_products;
}

function checkTaxExemptionAmount($op_id)
{
    $amount = 0;
    $tax_exemption_sql = "SELECT orders_products_extra_info_value
                            FROM orders_products_extra_info
                            WHERE orders_products_id = $op_id AND orders_products_extra_info_key = 'tax_exempted_amount'";

    $tax_exemption_result = tep_db_query($tax_exemption_sql);
    if ($row = tep_db_fetch_array($tax_exemption_result)) {
        $amount = $row['orders_products_extra_info_value'];
    }
    return $amount;
}

#Get subtotal

function cron_tep_get_order_total($order_id, $orderCurrency, $rate, $currencies, $tax_title = '')
{
    $sub_total_1 = $sub_total_2 = $sub_total_3 = array();
    $order_total_amount = $order_sub_total_amount = 0.00;
    $has_tax = false;

    $order_total_select_sql = " SELECT title, text, class, value
                                FROM orders_total
                                WHERE orders_id = '" . $order_id . "'
                                ORDER BY sort_order";
    $order_total_result_sql = tep_db_query($order_total_select_sql);

    while ($order_total_row = tep_db_fetch_array($order_total_result_sql)) {
        $format_amount = number_format($order_total_row['value'] * $rate, $currencies->currencies[$orderCurrency]['decimal_places'], $currencies->currencies[$orderCurrency]['decimal_point'], $currencies->currencies[$orderCurrency]['thousands_point']);
        $amount = number_format($order_total_row['value'] * $rate, 2, '.', '');

        switch ($order_total_row['class']) {
            case 'ot_subtotal':
                $order_sub_total_amount = $amount;
                break;
            case 'ot_gst':
                $sub_total_3[] = array(
                    'type' => $order_total_row['class'],
                    'title' => str_replace(':', '', $order_total_row['title']),
                    'value' => $format_amount
                );
                $has_tax = true;
                break;
            case 'ot_coupon':
                $order_sub_total_amount -= $amount;
                $sub_total_1[] = array(
                    'title' => 'Discount',
                    'value' => '(' . $format_amount . ')'
                );
                break;
            case 'ot_gv':
                $order_sub_total_amount -= $amount;
                $sub_total_1[] = array(
                    'type' => $order_total_row['class'],
                    'title' => 'Store Credit',
                    'value' => '(' . $format_amount . ')'
                );
                break;
            case 'ot_surcharge':
                $order_sub_total_amount += $amount;
                $sub_total_1[] = array(
                    'type' => $order_total_row['class'],
                    'title' => 'Handling Fee',
                    'value' => $format_amount
                );
                break;
            case 'ot_total':
                $order_total_amount = $format_amount;
                break;
        }
    }

    $sub_total_2[] = array(
        'title' => $has_tax ? 'Subtotal (Excluding ' . $tax_title . ')' : 'Subtotal',
        'value' => number_format($order_sub_total_amount, $currencies->currencies[$orderCurrency]['decimal_places'], $currencies->currencies[$orderCurrency]['decimal_point'], $currencies->currencies[$orderCurrency]['thousands_point'])
    );

    return array('total' => $order_total_amount, 'ot' => array($sub_total_1, array_merge($sub_total_2, $sub_total_3)));
}

echo "Done";
exit;
?>