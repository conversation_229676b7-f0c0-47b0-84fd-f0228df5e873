<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

tep_set_time_limit(0);

$cron_filename = 'cron_undelivered_report.php';

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = '" . $cron_filename . "'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '0') { // No any same cron process is running
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=1, 
                                        cron_process_track_start_date=now(),
                                        cron_process_track_failed_attempt=0 
                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($cron_process_update_sql);

        define('TABLE_CRON_UNDELIVERED_REPORT', 'cron_undelivered_report');

        $output_file_prefix = 'anb_undelivered_';
        $output_file_ext = '.html';
        $s3_bucket = 'BUCKET_DATA';
        $s3_filepath = 'report/';

        $start_date = '';
        $end_date = '';
        $today = date('d');

        switch ($today) {
            case '1' :
                $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, 1, date('Y')));
                $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, 11, date('Y')));
                break;

            case '2' :
                $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, 11, date('Y')));
                $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, 21, date('Y')));
                break;

            case '3' :
                $start_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, 21, date('Y')));
                $end_date = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), 1, date('Y')));

                $html_filename = $output_file_prefix . date('YmdHis') . $output_file_ext;
                $html_start_date = date('d-m-Y', mktime(0, 0, 0, date('m') - 1, 1, date('Y')));
                $html_end_date = date('d-m-Y', mktime(0, 0, 0, date('m'), 0, date('Y')));
                break;
        }

        if (tep_not_empty($start_date) && tep_not_empty($end_date)) {
            if ($today == 1) {
                tep_db_query("TRUNCATE " . TABLE_CRON_UNDELIVERED_REPORT);
            }

            $orders_id_select_sql = "	SELECT o.orders_id, o.payment_methods_id, o.date_purchased, o.currency, ot.value 
                                        FROM " . TABLE_ORDERS . " AS o 
                                        INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
                                            ON (o.orders_id = ot.orders_id AND ot.class = 'ot_subtotal') 
                                        INNER JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss
                                            ON (o.orders_id = oss.orders_id AND oss.orders_status_id = 7)
                                        WHERE o.orders_status IN (7, 2, 3)
                                            AND oss.first_date >= '" . $start_date . "'
                                            AND oss.first_date < '" . $end_date . "'
                                        ORDER BY o.orders_id";
            $orders_id_result_sql = tep_db_query($orders_id_select_sql, 'read_db_link');
            while ($orders_id_row = tep_db_fetch_array($orders_id_result_sql)) {
                # exclude ROLLBACK order
                $completed_order_select_sql = "	SELECT first_date 
                                                FROM " . TABLE_ORDERS_STATUS_STAT . " 
                                                WHERE orders_id = '" . (int) $orders_id_row['orders_id'] . "'
                                                    AND orders_status_id = 3
                                                    AND first_date < '" . $end_date . "'";
                $completed_order_result_sql = tep_db_query($completed_order_select_sql, 'read_db_link');
                if ($completed_order_row = tep_db_fetch_array($completed_order_result_sql)) {
                    $rollback_order_select_sql = "SELECT orders_id 
                                                FROM " . TABLE_ORDERS_STATUS_STAT . " 
                                                WHERE orders_id = '" . (int) $orders_id_row['orders_id'] . "'
                                                    AND orders_status_id <> 3
                                                    AND first_date > '" . $completed_order_row['first_date'] . "'";
                    $rollback_order_result_sql = tep_db_query($rollback_order_select_sql, 'read_db_link');

                    if ($rollback_order_row = tep_db_fetch_array($rollback_order_result_sql)) {
                        ;
                    } else {
                        continue;
                    }
                }

                $deliver_amt = 0;
                $product_type_array = array();

                # undelivered order amount
                $pos_delivery_select_sql = "SELECT SUM(sales_activities_amount) as total_pos_delivery
                                            FROM sales_activities 
                                            WHERE sales_activities_orders_id = '" . (int) $orders_id_row['orders_id'] . "' 
                                                AND sales_activities_code IN ('D', 'RFD') 
                                                AND sales_activities_date > '" . $orders_id_row['date_purchased'] . "'
                                                AND sales_activities_date < '" . $end_date . "'";
                $pos_delivery_result_sql = tep_db_query($pos_delivery_select_sql, 'read_db_link');
                $pos_delivery_row = tep_db_fetch_array($pos_delivery_result_sql);

                $neg_delivery_select_sql = "	SELECT SUM(sales_activities_amount) as total_neg_delivery
                                                FROM sales_activities 
                                                WHERE sales_activities_orders_id = '" . (int) $orders_id_row['orders_id'] . "' 
                                                    AND sales_activities_code IN ('RD', 'RFRD') 
                                                    AND sales_activities_date > '" . $orders_id_row['date_purchased'] . "'
                                                    AND sales_activities_date < '" . $end_date . "'";
                $neg_delivery_result_sql = tep_db_query($neg_delivery_select_sql, 'read_db_link');
                $neg_delivery_row = tep_db_fetch_array($neg_delivery_result_sql);

                $not_deliver_amt = (double) $orders_id_row['value'] - (double) $pos_delivery_row['total_pos_delivery'] + (double) $neg_delivery_row['total_neg_delivery'];

                if ($not_deliver_amt > 0.01) {
                    # undelivered by Order Product
                    $order_product_select_sql = "SELECT orders_products_id, custom_products_type_id, final_price, products_quantity
                                                FROM orders_products
                                                WHERE orders_id = '" . $orders_id_row['orders_id'] . "'
                                                    AND orders_products_is_compensate = 0
                                                    AND parent_orders_products_id = 0";
                    $order_product_result_sql = tep_db_query($order_product_select_sql, 'read_db_link');
                    while ($order_product_row = tep_db_fetch_array($order_product_result_sql)) {
                        $product_type = 0;
                        $op_pos_delivery_select_sql = "	SELECT SUM(sales_activities_amount) as total_pos_delivery
                                                        FROM sales_activities 
                                                        WHERE sales_activities_orders_id = '" . (int) $orders_id_row['orders_id'] . "' 
                                                            AND sales_activities_orders_products_id = '" . $order_product_row['orders_products_id'] . "'
                                                            AND sales_activities_code IN ('D', 'RFD') 
                                                            AND sales_activities_date > '" . $orders_id_row['date_purchased'] . "'
                                                            AND sales_activities_date < '" . $end_date . "'";
                        $op_pos_delivery_result_sql = tep_db_query($op_pos_delivery_select_sql, 'read_db_link');
                        $op_pos_delivery_row = tep_db_fetch_array($op_pos_delivery_result_sql);

                        $op_neg_delivery_select_sql = "	SELECT SUM(sales_activities_amount) as total_neg_delivery
                                                        FROM sales_activities 
                                                        WHERE sales_activities_orders_id = '" . (int) $orders_id_row['orders_id'] . "' 
                                                            AND sales_activities_orders_products_id = '" . $order_product_row['orders_products_id'] . "'
                                                            AND sales_activities_code IN ('RD', 'RFRD') 
                                                            AND sales_activities_date > '" . $orders_id_row['date_purchased'] . "'
                                                            AND sales_activities_date < '" . $end_date . "'";
                        $op_neg_delivery_result_sql = tep_db_query($op_neg_delivery_select_sql, 'read_db_link');
                        $op_neg_delivery_row = tep_db_fetch_array($op_neg_delivery_result_sql);

                        $op_not_deliver_amt = ((double) $order_product_row['final_price'] * $order_product_row['products_quantity']) - (double) $op_pos_delivery_row['total_pos_delivery'] + (double) $op_neg_delivery_row['total_neg_delivery'];

                        if ($op_not_deliver_amt > 0.01) {
                            if ($order_product_row['custom_products_type_id'] > 0) {
                                $product_type = $order_product_row['custom_products_type_id'];
                            } else {
                                $sub_product_select_sql = "SELECT custom_products_type_id
                                                            FROM orders_products
                                                            WHERE parent_orders_products_id = '" . $order_product_row['orders_products_id'] . "'
                                                            LIMIT 1";
                                $sub_product_result_sql = tep_db_query($sub_product_select_sql, 'read_db_link');
                                $sub_product_row = tep_db_fetch_array($sub_product_result_sql);

                                $product_type = (int) $sub_product_row['custom_products_type_id'];
                            }

                            if (isset($product_type_array[$product_type])) {
                                $product_type_array[$product_type] += $op_not_deliver_amt;
                            } else {
                                $product_type_array[$product_type] = $op_not_deliver_amt;
                            }
                        }
                    }

                    $cur = isset($product_type_array[0]) ? $product_type_array[0] : 0;
                    $pwl = isset($product_type_array[1]) ? $product_type_array[1] : 0;
                    $cdk = isset($product_type_array[2]) ? $product_type_array[2] : 0;
                    $sc = isset($product_type_array[3]) ? $product_type_array[3] : 0;
                    $hla = isset($product_type_array[4]) ? $product_type_array[4] : 0;

                    $undeliver_sel_sql = "SELECT payment_methods_id, currency 
                                        FROM " . TABLE_CRON_UNDELIVERED_REPORT . " 
                                        WHERE payment_methods_id = '" . $orders_id_row['payment_methods_id'] . "'
                                            AND currency = '" . $orders_id_row['currency'] . "'";
                    $undeliver_res_sql = tep_db_query($undeliver_sel_sql);
                    if ($undeliver_row = tep_db_fetch_array($undeliver_res_sql)) {
                        $undeliver_update_sql = "UPDATE " . TABLE_CRON_UNDELIVERED_REPORT . " SET 
                                                    cur = cur + " . $cur . ", 
                                                    pwl = pwl + " . $pwl . ", 
                                                    cdk = cdk + " . $cdk . ", 
                                                    sc = sc + " . $sc . ", 
                                                    hla = hla + " . $hla . ", 
                                                    subtotal = subtotal + " . $orders_id_row['value'] . "
                                                WHERE payment_methods_id = '" . $orders_id_row['payment_methods_id'] . "'
                                                    AND currency = '" . $orders_id_row['currency'] . "'";
                        tep_db_query($undeliver_update_sql);
                    } else {
                        $data = array('payment_methods_id' => $orders_id_row['payment_methods_id'],
                            'currency' => $orders_id_row['currency'],
                            'cur' => $cur,
                            'pwl' => $pwl,
                            'cdk' => $cdk,
                            'sc' => $sc,
                            'hla' => $hla,
                            'subtotal' => $orders_id_row['value']);
                        tep_db_perform(TABLE_CRON_UNDELIVERED_REPORT, $data, 'insert');
                    }
                }
            }


            # generate HTML output
            if ($today == 3) {
                $grand_cur = 0;
                $grand_pwl = 0;
                $grand_cdk = 0;
                $grand_sc = 0;
                $grand_hla = 0;
                $grand_undeliver_amt = 0;
                $grand_order_amt = 0;
                $grand_undeliver_percentage = 0;

                # Payment Gateway by Registered Company
                $ogm = array(array('company_name' => 'OFFGAMERS LIMITED (OGHK)',
                        'payment_gateway' => array(array('id' => '0',
                                'title' => 'Full Store Credit',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'bibit.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'moneybookers.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'kuaiqian.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'alipay.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'cashU.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'global_collect.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'offline.php',
                                'incl_pm_code' => 'wiretransfer',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'onecard.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'webmoney.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'smart2pay.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => '')
                        )
                    ),
                    array('company_name' => 'OFFGAMERS GLOBAL PTE LTD (OGSG)',
                        'payment_gateway' => array(array('filename' => 'paypal.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => "MYR")
                        )
                    ),
                    array('company_name' => 'OFFGAMERS LLC (OGUS)',
                        'payment_gateway' => array(array('filename' => 'dineromail.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'mozcom.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'paynearme.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => '')
                        )
                    ),
                    array('company_name' => 'OFFGAMERS SDN BHD (OGSB)',
                        'payment_gateway' => array(array('filename' => 'paypal.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => "MYR",
                                'excl_currency' => ''),
                            array('filename' => 'maybank.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'mobile_money.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'cimb.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'iPay88.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'rhb.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => '',
                                'incl_currency' => '',
                                'excl_currency' => ''),
                            array('filename' => 'offline.php',
                                'incl_pm_code' => '',
                                'excl_pm_code' => 'wiretransfer',
                                'incl_currency' => '',
                                'excl_currency' => '')
                        )
                    )
                );

                ob_start();
                ?>
                <html>
                    <head>
                        <title>OffGamers Group</title>
                    </head>
                    <body>
                        <table width="100%">
                            <!-- Header -->
                            <tr><td colspan="10"><b>OFFGAMERS GROUP</b><br/>UNDELIVERY REPORT<br/>REPORT DATE:-<br/>From <?= $html_start_date; ?> to <?= $html_end_date; ?></td></tr>
                            <tr><td colspan="10"><hr></td></tr>
                            <tr><td colspan="7"></td><td colspan="3"><center>TOTAL in USD</center></td></tr>
                    <tr>
                        <td></td>
                        <td width="100px">CUR</td>
                        <td width="100px">PWL</td>
                        <td width="100px">CDK</td>
                        <td width="100px">SC</td>
                        <td width="100px">HLA</td>
                        <td></td>
                        <td width="100px">Undelivered</td>
                        <td width="100px">Order Amount</td>
                        <td width="100px">Undelivered %</td>
                    </tr>
                    <tr><td colspan="10"><hr></td></tr>
                    <?php
                    for ($i = 0, $ogm_cnt = count($ogm); $ogm_cnt > $i; $i++) {
                        $ogm_data = array();

                        $sub_cur = 0;
                        $sub_pwl = 0;
                        $sub_cdk = 0;
                        $sub_sc = 0;
                        $sub_hla = 0;
                        $sub_undeliver_amt = 0;
                        $sub_order_amt = 0;
                        $sub_undeliver_percentage = 0;

                        $company_name = $ogm[$i]['company_name'];
                        ?>
                        <tr>
                            <td><b><u><?= $company_name; ?></u></b></td>
                        <tr><td colspan="9"></td></tr>
                    </tr>
                    <?php
                    $pm_title = '';

                    for ($j = 0, $pg_cnt = count($ogm[$i]['payment_gateway']); $pg_cnt > $j; $j++) {
                        $pm_id_array = array();
                        $pm_title_array = array();

                        if (isset($ogm[$i]['payment_gateway'][$j]['filename']) && tep_not_empty($ogm[$i]['payment_gateway'][$j]['filename'])) {
                            $pm_filename = $ogm[$i]['payment_gateway'][$j]['filename'];

                            $incl_pm_sql = '';
                            $excl_pm_sql = '';

                            if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['incl_pm_code'])) {
                                $incl_pm_list = array();
                                $incl_pm_arr = explode(',', $ogm[$i]['payment_gateway'][$j]['incl_pm_code']);

                                foreach ($incl_pm_arr as $num => $val) {
                                    $incl_pm_list[] = "'" . trim($val) . "'";
                                }

                                $incl_pm_sql = " AND pm2.payment_methods_code IN (" . implode(",", $incl_pm_list) . ")";
                            }

                            if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['excl_pm_code'])) {
                                $excl_pm_list = array();
                                $excl_pm_arr = explode(',', $ogm[$i]['payment_gateway'][$j]['excl_pm_code']);

                                foreach ($excl_pm_arr as $num => $val) {
                                    $excl_pm_list[] = "'" . trim($val) . "'";
                                }

                                $excl_pm_sql = " AND pm2.payment_methods_code NOT IN (" . implode(",", $excl_pm_list) . ")";
                            }

                            $pm_sel_sql = "	SELECT pm2.payment_methods_id, pm2.payment_methods_title 
														FROM " . TABLE_PAYMENT_METHODS . " pm1 
														INNER JOIN " . TABLE_PAYMENT_METHODS . " pm2 
															ON pm2.payment_methods_parent_id = pm1.payment_methods_id 
																AND pm2.payment_methods_receive_status = '1' 
														WHERE pm1.payment_methods_filename = '" . $pm_filename . "'" .
                                    $incl_pm_sql .
                                    $excl_pm_sql;
                            $pm_res_sql = tep_db_query($pm_sel_sql);
                            while ($pm_row = tep_db_fetch_array($pm_res_sql)) {
                                $pm_id_array[] = $pm_row['payment_methods_id'];
                                $pm_title_array[] = (tep_not_empty($pm_row['payment_methods_title']) ? $pm_row['payment_methods_title'] : $pm_filename);
                            }

                            if (($pm_filename != 'offline.php') && tep_not_empty($pm_id_array)) {
                                $pm_id_list = implode(',', $pm_id_array);
                                unset($pm_id_array);
                                $pm_id_array[] = $pm_id_list;
                                $pm_title_array = array();

                                $pm_title_sel_sql = "	SELECT payment_methods_title 
																	FROM " . TABLE_PAYMENT_METHODS . "
																	WHERE payment_methods_filename = '" . $pm_filename . "'";
                                $pm_title_res_sql = tep_db_query($pm_title_sel_sql);
                                if ($pm_title_row = tep_db_fetch_array($pm_title_res_sql)) {
                                    $pm_title_array[] = $pm_title_row['payment_methods_title'];
                                } else {
                                    $pm_title_array[] = $pm_filename;
                                }
                            }
                        } else {
                            if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['title'])) {
                                $pm_title_array[] = $ogm[$i]['payment_gateway'][$j]['title'];
                            }

                            if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['id'])) {
                                $pm_id_array[] = $ogm[$i]['payment_gateway'][$j]['id'];
                            }
                        }

                        if (tep_not_empty($pm_id_array)) {
                            for ($l = 0, $pm_cnt = count($pm_id_array); $pm_cnt > $l; $l++) {
                                $cur = 0;
                                $pwl = 0;
                                $cdk = 0;
                                $sc = 0;
                                $hla = 0;
                                $undeliver_amt = 0;
                                $order_amt = 0;
                                $undeliver_percentage = 0;

                                $incl_cur_sql = '';
                                $excl_cur_sql = '';

                                $pm_id = $pm_id_array[$l];
                                $pm_title = (tep_not_empty($pm_title_array[$l]) ? $pm_title_array[$l] : $ogm[$i]['payment_gateway'][$j]['filename']);

                                if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['incl_currency'])) {
                                    $incl_cur_list = array();
                                    $incl_cur_arr = explode(',', $ogm[$i]['payment_gateway'][$j]['incl_currency']);

                                    foreach ($incl_cur_arr as $num => $val) {
                                        $incl_cur_list[] = "'" . trim($val) . "'";
                                    }

                                    $incl_cur_sql = " AND currency IN (" . implode(",", $incl_cur_list) . ")";
                                    $pm_title .= ' (' . $ogm[$i]['payment_gateway'][$j]['incl_currency'] . ')';
                                }

                                if (tep_not_empty($ogm[$i]['payment_gateway'][$j]['excl_currency'])) {
                                    $excl_cur_list = array();
                                    $excl_cur_arr = explode(',', $ogm[$i]['payment_gateway'][$j]['excl_currency']);

                                    foreach ($excl_cur_arr as $num => $val) {
                                        $excl_cur_list[] = "'" . trim($val) . "'";
                                    }

                                    $excl_cur_sql = " AND currency NOT IN (" . implode(",", $excl_cur_list) . ")";
                                }

                                $undeliver_sel_sql = "	SELECT SUM( cur ) AS cur, SUM( pwl ) AS pwl, 
																		SUM( cdk ) AS cdk, SUM( sc ) AS sc, 
																		SUM( hla ) AS hla, SUM( subtotal ) AS subtotal 
																	FROM " . TABLE_CRON_UNDELIVERED_REPORT . " 
																	WHERE payment_methods_id IN (" . $pm_id . ")" .
                                        $incl_cur_sql .
                                        $excl_cur_sql;
                                $undeliver_res_sql = tep_db_query($undeliver_sel_sql);
                                if ($undeliver_row = tep_db_fetch_array($undeliver_res_sql)) {
                                    $cur = $undeliver_row['cur'];
                                    $pwl = $undeliver_row['pwl'];
                                    $cdk = $undeliver_row['cdk'];
                                    $sc = $undeliver_row['sc'];
                                    $hla = $undeliver_row['hla'];

                                    $undeliver_amt = $cur + $pwl + $cdk + $sc + $hla;
                                    $order_amt = $undeliver_row['subtotal'];

                                    if (($undeliver_amt > 0.01) && ($order_amt > 0.01)) {
                                        $undeliver_percentage = ( $undeliver_amt / $order_amt ) * 100;
                                    }
                                }

                                $ogm_data[] = array('pm_title' => $pm_title,
                                    'cur' => $cur,
                                    'pwl' => $pwl,
                                    'cdk' => $cdk,
                                    'sc' => $sc,
                                    'hla' => $hla,
                                    'undeliver_amt' => $undeliver_amt,
                                    'order_amt' => $order_amt,
                                    'undeliver_percentage' => $undeliver_percentage);
                            }
                        }
                    }

                    usort($ogm_data, 'pm_title_cmp');

                    for ($j = 0, $od_cnt = count($ogm_data); $od_cnt > $j; $j++) {
                        ?>
                        <tr>
                            <td><?= $ogm_data[$j]['pm_title']; ?></td>
                            <td><?= number_format($ogm_data[$j]['cur'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['pwl'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['cdk'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['sc'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['hla'], 2, '.', ','); ?></td>
                            <td></td>
                            <td><?= number_format($ogm_data[$j]['undeliver_amt'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['order_amt'], 2, '.', ','); ?></td>
                            <td><?= number_format($ogm_data[$j]['undeliver_percentage'], 2, '.', ''); ?>%</td>
                        </tr>
                        <?php
                        $sub_cur += $ogm_data[$j]['cur'];
                        $sub_pwl += $ogm_data[$j]['pwl'];
                        $sub_cdk += $ogm_data[$j]['cdk'];
                        $sub_sc += $ogm_data[$j]['sc'];
                        $sub_hla += $ogm_data[$j]['hla'];
                        $sub_undeliver_amt += $ogm_data[$j]['undeliver_amt'];
                        $sub_order_amt += $ogm_data[$j]['order_amt'];
                    }

                    if (($sub_undeliver_amt > 0.01) && ($sub_order_amt > 0.01)) {
                        $sub_undeliver_percentage = ( $sub_undeliver_amt / $sub_order_amt ) * 100;
                    }

                    $grand_cur += $sub_cur;
                    $grand_pwl += $sub_pwl;
                    $grand_cdk += $sub_cdk;
                    $grand_sc += $sub_sc;
                    $grand_hla += $sub_hla;
                    $grand_undeliver_amt += $sub_undeliver_amt;
                    $grand_order_amt += $sub_order_amt;
                    ?>
                    <tr>
                        <td></td>
                        <td colspan="5"><hr></td>
                        <td></td>
                        <td colspan="3"><hr></td>
                    </tr>
                    <tr>
                        <td><b>Sub-Total</b></td>
                        <td><?= number_format($sub_cur, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_pwl, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_cdk, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_sc, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_hla, 2, '.', ','); ?></td>
                        <td></td>
                        <td><?= number_format($sub_undeliver_amt, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_order_amt, 2, '.', ','); ?></td>
                        <td><?= number_format($sub_undeliver_percentage, 2, '.', ''); ?>%</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td colspan="5"><hr></td>
                        <td></td>
                        <td colspan="3"><hr></td>
                    </tr>
                    <tr><td colspan="10"><br></td></tr>
                    <tr><td colspan="10"><br></td></tr>
                    <?php
                }

                if (($grand_undeliver_amt > 0.01) && ($grand_order_amt > 0.01)) {
                    $grand_undeliver_percentage = ( $grand_undeliver_amt / $grand_order_amt ) * 100;
                }
                ?>
                <!-- Grand Total by Product -->
                <tr>
                    <td></td>
                    <td colspan="5"><hr></td>
                    <td></td>
                    <td colspan="3"><hr></td>
                </tr>
                <tr>
                    <td><b>GRAND TOTAL by PRODUCT</b></td>
                    <td><?= number_format($grand_cur, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_pwl, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_cdk, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_sc, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_hla, 2, '.', ','); ?></td>
                    <td></td>
                    <td><?= number_format($grand_undeliver_amt, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_order_amt, 2, '.', ','); ?></td>
                    <td><?= number_format($grand_undeliver_percentage, 2, '.', ''); ?>%</td>
                </tr>
                <tr>
                    <td></td>
                    <td colspan="5"><hr></td>
                    <td></td>
                    <td colspan="3"><hr></td>
                </tr>
                </table>
                </body>
                </html>
                <?php
                $html = ob_get_contents();
                ob_end_clean();

                $filename = DIR_FS_DOCUMENT_ROOT . 'download/' . $html_filename;
                $fp = fopen($filename, 'w');
                fwrite($fp, $html);
                fclose($fp);

                # upload to s3
                include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

                $aws_obj = new ogm_amazon_ws();
                $aws_obj->set_bucket_key($s3_bucket);
                $aws_obj->set_storage('STORAGE_STANDARD');
                $aws_obj->set_filepath($s3_filepath);

                if ($aws_obj->is_aws_s3_enabled()) {
                    $aws_obj->set_file(array('tmp_name' => $filename));
                    $aws_obj->set_filename($html_filename);
                    $aws_obj->save_file();
                }

                unlink(DIR_FS_DOCUMENT_ROOT . 'download/' . $html_filename);
            }
        }

        echo "DONE";

        // Release cron process "LOCK"
        $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                    SET cron_process_track_in_action=0 
                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
        tep_db_query($unlock_cron_process_sql);
    } else { // Check if previous cron job has overdue / something bad happened
        if ($cron_process_checking_row['overdue_process'] == '1') {
            if ($cron_process_checking_row['overdue_process'] < 5) {
                $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                    SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
                                                    WHERE cron_process_track_filename = '" . $cron_filename . "'";
                tep_db_query($cron_process_attempt_update_sql);
            } else {
                $headers = 'To: <EMAIL>' . "\r\n" .
                        'From: ' . STORE_NAME . '<<EMAIL>>' . "\r\n" .
                        'Reply-To: <EMAIL>' . "\r\n" .
                        'X-Mailer: PHP/' . phpversion();
                $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
                $message = 'Undelivered Report cronjob failed at ' . date("Y-m-d H:i:s");
                @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}

function pm_title_cmp($a, $b) {
    return strcmp(strtolower($a['pm_title']), strtolower($b['pm_title']));
}
?>