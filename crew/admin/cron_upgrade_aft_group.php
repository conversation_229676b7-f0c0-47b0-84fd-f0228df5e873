<?php

/*
 * Auto upgrade of AFT Trust Level Group from Trust Level 1 to Trust Level 5
 */
ini_set('display_errors', 1);
error_reporting(E_ALL);

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_set_time_limit(0);
$languages_id = 1;

tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// Only check for price if the is records number been passed. Corresponding row number in price_check_slots table
if (!isset($_SERVER['argv']) || !is_numeric($_SERVER['argv'][1]) || $_SERVER['argv'][1] < 0) {
    $max_customers_per_call = 15000;
} else {
    $max_customers_per_call = $_SERVER['argv'][1];
}

// set application wide parameters
$config_sql = "SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM " . TABLE_CONFIGURATION;
$config_res = tep_db_query($config_sql, 'read_db_link');
while ($config_row = tep_db_fetch_array($config_res)) {
    define($config_row['cfgKey'], $config_row['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');

$cron_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process
            FROM " . TABLE_CRON_PROCESS_TRACK . "
            WHERE cron_process_track_filename = 'cron_upgrade_aft_group.php'";
$cron_res = tep_db_query($cron_sql, 'read_db_link');
$cron_row = tep_db_fetch_array($cron_res);

$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

if ($cron_row['cron_process_track_in_action'] == '1') {
    if ($cron_row['overdue_process'] == '1') {
        $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
        $message = 'AFT upgrade cronjob failed at ' . $cron_process_datetime;
        @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
    exit;
}

$currencies = new currencies();

$pm_array = array();
$pm_comfirm_completed_select_sql = "SELECT pci.payment_methods_id, pmid.payment_configuration_info_value 
                                    FROM payment_configuration_info as pci
                                    LEFT JOIN payment_configuration_info_description as pmid
                                        ON pmid.payment_configuration_info_id = pci.payment_configuration_info_id
                                    WHERE pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE%'
                                    GROUP BY pci.payment_methods_id
                                    ORDER BY payment_methods_id";
$pm_comfirm_completed_result_sql = tep_db_query($pm_comfirm_completed_select_sql, 'read_db_link');
while ($pm_comfirm_completed_row = tep_db_fetch_array($pm_comfirm_completed_result_sql)) {
    $pm_array[$pm_comfirm_completed_row['payment_methods_id']] = $pm_comfirm_completed_row['payment_configuration_info_value'];
}

$pm_select_sql = "SELECT payment_methods_id, payment_methods_parent_id FROM " . TABLE_PAYMENT_METHODS;
$pm_result_sql = tep_db_query($pm_select_sql, 'read_db_link');
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
    if (!isset($pm_array[$pm_row['payment_methods_id']]) && $pm_row['payment_methods_parent_id'] > 0) {
        $pm_array[$pm_row['payment_methods_id']] = (int) $pm_array[$pm_row['payment_methods_parent_id']];
    }
}

$init_sql = "SELECT cron_customer_upgrade_from, cron_customer_upgrade_to, cron_customer_last_process_date,
                cron_customer_upgrade_in_action, cron_customer_upgrade_last_process_customer_id,
                cron_customer_upgrade_processed_count
            FROM " . TABLE_CRON_CUSTOMER_AFT_UPGRADE . "
            WHERE cron_customer_upgrade_in_action = '1' ";
$init_res = tep_db_query($init_sql, 'read_db_link');

if ($init_row = tep_db_fetch_array($init_res)) {
    // Mark the start of cronjob
    $cron_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                SET cron_process_track_in_action = 1,
                    cron_process_track_start_date = now(),
                    cron_process_track_failed_attempt = 0
                WHERE cron_process_track_filename = 'cron_upgrade_aft_group.php'";
    tep_db_query($cron_sql);

    $upgrade_processed_amount = (int) $init_row['cron_customer_upgrade_processed_count']; // How many has been processed previously
    $last_customer_id = $init_row['cron_customer_upgrade_last_process_customer_id'];
    $from_status = $init_row['cron_customer_upgrade_from'];
    $to_status = $init_row['cron_customer_upgrade_to'];
    $to_status_name = "";
    $next_from_status = $next_to_status = 0;

    if ($from_status == '12' && $to_status == '3') { // Trust Level 2(Bronze) to Trust Level 3(Silver)
        $to_status_name = 'Trust Level 3(Silver)';
        $next_from_status = 4;
        $next_to_status = 5;

        //daily sales
        $min_sales = array(
            'rp' => 500, //1000
            'srp' => 800, //2000
            'nrp' => 2500, //5000
            'total' => 2000,
        );
        $last_complete_order_date = date("Y-m-d H:i:s", strtotime("-90 day"));
    } else if ($from_status == '3' && $to_status == '4') { // Trust Level 3(Silver) to Trust Level 4(Gold)
        $to_status_name = 'Trust Level 4(Gold)';
        $next_from_status = 12;
        $next_to_status = 3;

        //daily sales
        $min_sales = array(
            'rp' => 3000, //9000
            'srp' => 5000, //15000
            'nrp' => 3000, //9000
            'total' => 18000,
        );
        $last_complete_order_date = date("Y-m-d H:i:s", strtotime("-120 day"));
    } else if ($from_status == '4' && $to_status == '5') { // Trust Level 4(Gold) to Trust Level 5(Plat)
        $to_status_name = 'Trust Level 5(Plat)';
        $next_from_status = 3;
        $next_to_status = 4;

        //daily sales
        $min_sales = array(
            'rp' => 4000, //12000
            'srp' => 9000, //20000
            'nrp' => 5000, //12000
            'total' => 24000,
        );
        $last_complete_order_date = date("Y-m-d H:i:s", strtotime("-150 day"));
    }

    $total_success_upgrade = tep_upgrade($max_customers_per_call, $min_sales, $from_status, $to_status, $to_status_name, $last_customer_id, $last_complete_order_date);
    $upgrade_processed_amount += $total_success_upgrade;

    if ($total_success_upgrade < $max_customers_per_call) { // Complete of customer upgrade
        $customer_upgrade_data_sql = array('cron_customer_upgrade_in_action' => 0,
            'cron_customer_last_process_date' => $cron_process_datetime,
            'cron_customer_upgrade_processed_count' => $upgrade_processed_amount,
            'cron_customer_upgrade_last_process_customer_id' => $last_customer_id);
        tep_db_perform(TABLE_CRON_CUSTOMER_AFT_UPGRADE, $customer_upgrade_data_sql, 'update', " cron_customer_upgrade_from = '" . $from_status . "' AND cron_customer_upgrade_to = '" . $to_status . "'");

        // Set the next cronjob group
        $customer_upgrade_data_sql = array('cron_customer_upgrade_in_action' => 1,
            'cron_customer_upgrade_processed_count' => 0,
            'cron_customer_upgrade_last_process_customer_id' => 0);
        tep_db_perform(TABLE_CRON_CUSTOMER_AFT_UPGRADE, $customer_upgrade_data_sql, 'update', " cron_customer_upgrade_from = '" . $next_from_status . "' AND cron_customer_upgrade_to = '" . $next_to_status . "'");
    } else { // Just complete 50 upgrade, wait for next cronjob call
        $customer_upgrade_data_sql = array('cron_customer_last_process_date' => $cron_process_datetime,
            'cron_customer_upgrade_processed_count' => $upgrade_processed_amount,
            'cron_customer_upgrade_last_process_customer_id' => $last_customer_id);
        tep_db_perform(TABLE_CRON_CUSTOMER_AFT_UPGRADE, $customer_upgrade_data_sql, 'update', " cron_customer_upgrade_from = '" . $from_status . "' AND cron_customer_upgrade_to = '" . $to_status . "'");
    }

    // Release cron process "LOCK"
    $cron_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                SET cron_process_track_in_action = 0
                WHERE cron_process_track_filename = 'cron_upgrade_aft_group.php'";
    tep_db_query($cron_sql);
} else {
    tep_mail('OffGamers', '<EMAIL>', '[Cronjob] AFT Upgrade failed', 'AFT Upgrade does not run', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

function tep_upgrade($max_count, $min_sales, $from_status, $to_status, $to_status_name, &$last_customer_id, $last_complete_order_date)
{
    global $pm_array, $currencies;

    $total_processed = 0;
    $last_logon_date = date("Y-m-d H:i:s", strtotime("-6 months"));
    if (!$last_complete_order_date) {
        $last_complete_order_date = date("Y-m-d H:i:s", strtotime("-90 day"));
    }

    $cust_sql = "SELECT c.customers_id, customers_flag
                FROM " . TABLE_CUSTOMERS . " AS c
                INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
                    ON c.customers_id = ci.customers_info_id
                WHERE c.customers_id > " . $last_customer_id . "
                    AND c.customers_status = 1
                    AND c.customers_aft_groups_id = " . $from_status . " 
                    AND NOT FIND_IN_SET('4', c.customers_flag)
                    AND ci.customers_info_date_of_last_logon > '" . $last_logon_date . "'
                    ORDER BY c.customers_id LIMIT " . $max_count;
    $cust_res = tep_db_query($cust_sql, 'read_db_link');
    while ($cust_row = tep_db_fetch_array($cust_res)) {
        if ($total_processed >= $max_count)
            break;

        $total_processed++;    
        $last_customer_id = $cust_row['customers_id'];

        $ekyc_approved_select_sql = "   SELECT customers_id 
                                        FROM customers_verification_document 
                                        WHERE customers_id = '" . (int) $cust_row['customers_id'] . "' 
                                            AND files_003_status = '1'";
        $ekyc_approved_res = tep_db_query($ekyc_approved_select_sql, 'read_db_link');

        // Not yet eKYC approved, we do not upgrade to TL3
        if (!tep_db_num_rows($ekyc_approved_res))   continue;

        // No recorded orders under Reversed - Lost, Reversed - Win and On Hold
        $bad_order_sql = "SELECT orders_id
                        FROM " . TABLE_ORDERS . "
                        WHERE customers_id = '" . (int) $cust_row['customers_id'] . "'
                            AND (orders_status = 8 OR (orders_status = 3 AND orders_cb_status = 1) OR (orders_status = 3 AND orders_cb_status = 2))
                        LIMIT 1";
        $bad_order_res = tep_db_query($bad_order_sql, 'read_db_link');
        if (!tep_db_num_rows($bad_order_res)) {
            $last_complete_sql = "SELECT o.orders_id 
                                FROM " . TABLE_ORDERS . " AS o 
                                INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                    ON o.orders_id=op.orders_id 
                                WHERE o.customers_id = '" . (int) $cust_row['customers_id'] . "' 
                                    AND o.orders_status = 3 
                                    AND o.date_purchased <= '" . $last_complete_order_date . "' 
                                    AND op.products_good_delivered_price > 0 
                                LIMIT 1";
            $last_complete_res = tep_db_query($last_complete_sql, 'read_db_link');
            if (tep_db_num_rows($last_complete_res)) {
                $total_completed_sales = 0;
                //check daily purchase limit
                $hit_purchase_limit = false;
                foreach ($min_sales as $type => $_sales) {
                    $purchase_result = tep_check_purchase($cust_row['customers_id'], $type, $_sales);
                    if ($purchase_result == true) {
                        $hit_purchase_limit = true;
                        break;
                    }
                }

                if ($hit_purchase_limit == false) {
                    continue;
                }

                $pm_sel = "SELECT DISTINCT payment_methods_id
                        FROM " . TABLE_ORDERS . "
                        WHERE customers_id='" . (int) $cust_row['customers_id'] . "'
                            AND orders_status IN (2, 3) ";
                $pm_res = tep_db_query($pm_sel, 'read_db_link');
                while ($pm_row = tep_db_fetch_array($pm_res)) {
                    if (isset($pm_array[$pm_row['payment_methods_id']])) {
                        $confirm_complete_day = (int) $pm_array[$pm_row['payment_methods_id']] > 0 ? (int) $pm_array[$pm_row['payment_methods_id']] : 0;
                    } else {
                        $confirm_complete_day = 150;
                    }

                    $sql = "SELECT o.orders_id, SUM(op.products_good_delivered_price) AS delivered_amt
                            FROM " . TABLE_ORDERS . " AS o
                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                ON op.orders_id = o.orders_id 
                                AND op.orders_products_is_compensate = 0 
                            WHERE o.customers_id = " . $cust_row['customers_id'] . "
                                AND o.payment_methods_id = '" . tep_db_input($pm_row["payment_methods_id"]) . "'
                                AND o.orders_status IN (2, 3) 
                                AND o.date_purchased <= DATE_SUB(NOW(), INTERVAL " . $confirm_complete_day . " DAY) 
                                AND o.orders_cb_status IS NULL 
                                AND op.products_good_delivered_price > 0 
                            GROUP BY o.orders_id";
                    $res = tep_db_query($sql, 'read_db_link');
                    while ($row = tep_db_fetch_array($res)) {
                        if ($row["delivered_amt"] > 0) {
                            $_sql = "SELECT ot.value FROM " . TABLE_ORDERS_TOTAL . " AS ot WHERE ot.orders_id = " . $row["orders_id"] . " AND ot.class = 'ot_total'";
                            $_res = tep_db_query($_sql, 'read_db_link');
                            $_row = tep_db_fetch_array($_res);
                            if (isset($_row["value"]) && ($_row["value"] > 0)) {
                                $total_completed_sales += ($row["delivered_amt"] > $_row["value"] ? $_row["value"] : $row["delivered_amt"]);
                            }
                        }
                    }
                }

                $completed_sales = $min_sales['total'];

                if ($total_completed_sales >= $completed_sales) {
                    $customers_groups_id_update_sql = "	UPDATE " . TABLE_CUSTOMERS . "
                                                        SET customers_aft_groups_id = '" . $to_status . "'
                                                        WHERE customers_id = '" . tep_db_input($cust_row['customers_id']) . "'";
                    tep_db_query($customers_groups_id_update_sql);

                    $sql_data_array = array(
                        'customers_id' => $cust_row['customers_id'],
                        'date_remarks_added' => 'now()',
                        'remarks' => 'Upgraded to ' . $to_status_name . ' by system',
                        'remarks_added_by' => 'system'
                    );
                    tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
                }
            }
        }
    }

    return $total_processed;
}

function tep_check_purchase($customer_id, $type, $sales_amount)
{
        $lastMonth = strtotime("-1 month");
        $start_date = date("Y-m", $lastMonth) . "-01 00:00:00";
        $end_date = date("Y-m-t", $lastMonth) . " 23:59:59";
        switch ($type) {
            case 'rp':
                $is_rp = 1;
                break;
            case 'srp':
                $is_rp = 2;
                break;
            case 'nrp':
                $is_rp = 0;
                break;
            default:
                return false;
        }

        $sales_sql = 'SELECT SUM(ott.value) AS total_sales, DATE(latest_date) DateOnly FROM (
                            SELECT DISTINCT o.orders_id, ot.value, oss.latest_date
                            FROM orders_status_stat AS oss
                            INNER JOIN orders AS o 
                                ON oss.orders_id = o.orders_id  
                            INNER JOIN pipwave_payment_mapper as ppm
                            ON o.payment_methods_id = ppm.pm_id
                            INNER JOIN orders_total AS ot
                                ON oss.orders_id = ot.orders_id AND class = "ot_total"    
                            WHERE o.customers_id = "' . $customer_id . '"
                                AND ppm.is_rp = "' . $is_rp . '"
                                AND oss.orders_status_id = "7"
                                AND oss.latest_date >= "' . $start_date . '"  AND oss.latest_date <= "' . $end_date . '"
                                AND o.orders_cb_status IS NULL 
                                AND ot.value > 0) AS ott GROUP BY DateOnly HAVING total_sales >= ' . $sales_amount;
        $sales_res = tep_db_query($sales_sql, 'read_db_link');
        while ($sales_row = tep_db_fetch_array($sales_res)) {
            if ($sales_row['total_sales'] >= $sales_amount) {
                return true;
                break;
            }
        }
    return false;
}
