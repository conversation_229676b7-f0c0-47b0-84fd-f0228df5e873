<?php

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_set_time_limit(0);
$languages_id = 1;

tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

// set application wide parameters
$config_sql = "SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM " . TABLE_CONFIGURATION;
$config_res = tep_db_query($config_sql, 'read_db_link');
while ($config_row = tep_db_fetch_array($config_res)) {
    define($config_row['cfgKey'], $config_row['cfgValue']);
}

include_once(DIR_WS_LANGUAGES . 'english.php');

$cron_sql = "SELECT cron_process_track_in_action, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process
            FROM " . TABLE_CRON_PROCESS_TRACK . "
            WHERE cron_process_track_filename = 'cron_upgrade_gold_plat.php'";
$cron_res = tep_db_query($cron_sql, 'read_db_link');
$cron_row = tep_db_fetch_array($cron_res);

$cron_process_datetime = date("Y-m-d H:i:s"); // Set the time for this cron process

if ($cron_row['cron_process_track_in_action'] == '1') {
    if ($cron_row['overdue_process'] == '1') {
        $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
        $message = 'Customer upgrade Gold to Plat cronjob failed at ' . $cron_process_datetime;
        @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
    exit;
}

$init_sql = "SELECT cron_customer_upgrade_from, cron_customer_upgrade_to, 
                cron_customer_upgrade_last_process_customer_id, cron_customer_upgrade_processed_count
            FROM " . TABLE_CRON_CUSTOMER_UPGRADE . "
            WHERE cron_customer_upgrade_from = 4
                AND cron_customer_upgrade_to = 5";
$init_res = tep_db_query($init_sql, 'read_db_link');
if ($init_row = tep_db_fetch_array($init_res)) {
    // Mark the start of cronjob
    $cron_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                SET cron_process_track_in_action = 1, 
                    cron_process_track_start_date = now(), 
                    cron_process_track_failed_attempt = 0 
                WHERE cron_process_track_filename = 'cron_upgrade_gold_plat.php'";
    tep_db_query($cron_sql);

    $upgrade_processed_amount = (int) $init_row['cron_customer_upgrade_processed_count']; // How many has been processed previously
    $last_customer_id = $init_row['cron_customer_upgrade_last_process_customer_id'];
    $from_status = $init_row['cron_customer_upgrade_from'];
    $to_status = $init_row['cron_customer_upgrade_to'];
    $to_status_name = "Platinum";
    $min_sales = 1000;
    $max_customers_per_call = 25;

    $total_success_upgrade = tep_upgrade($max_customers_per_call, $min_sales, $from_status, $to_status, $to_status_name, $last_customer_id);
    $upgrade_processed_amount += $total_success_upgrade;

    if ($total_success_upgrade < $max_customers_per_call) { // Complete of customer upgrade
        $upgrade_processed_amount = 0;
        $last_customer_id = 0;
    }

    $update_sql = array(
        'cron_customer_upgrade_in_action' => 0,
        'cron_customer_last_process_date' => $cron_process_datetime,
        'cron_customer_upgrade_processed_count' => $upgrade_processed_amount,
        'cron_customer_upgrade_last_process_customer_id' => $last_customer_id
    );
    tep_db_perform(TABLE_CRON_CUSTOMER_UPGRADE, $update_sql, 'update', " cron_customer_upgrade_from = '" . $from_status . "' AND cron_customer_upgrade_to = '" . $to_status . "'");

    // Release cron process "LOCK"
    $cron_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                SET cron_process_track_in_action = 0 
                WHERE cron_process_track_filename = 'cron_upgrade_gold_plat.php'";
    tep_db_query($cron_sql);
} else {
    tep_mail('OffGamers', '<EMAIL>', '[Cronjob] Customers Upgrade Gold to Plat failed', 'Customers Upgrade Gold to Plat does not run', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

function tep_upgrade($max_count, $min_sales, $from_status, $to_status, $to_status_name, &$last_customer_id)
{
    $total_processed = 0;
    $last_logon_date = date("Y-m-d H:i:s", strtotime("-1 months"));
    do {
        $cust_sql = "SELECT c.customers_id 
                    FROM " . TABLE_CUSTOMERS . " AS c
                    INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
                        ON c.customers_id = ci.customers_info_id
                    WHERE c.customers_id > " . $last_customer_id . "
                        AND c.customers_status = 1
                        AND c.customers_groups_id = " . $from_status . "
                        AND ci.customers_info_date_of_last_logon > '" . $last_logon_date . "'
                    ORDER BY c.customers_id
                    LIMIT " . $max_count;
        $cust_res = tep_db_query($cust_sql, 'read_db_link');
        $cnt_row = tep_db_num_rows($cust_res);
        if ($cnt_row) {
            while ($cust_row = tep_db_fetch_array($cust_res)) {
                if ($total_processed >= $max_count)
                    break 2;

                $last_customer_id = $cust_row['customers_id'];
                $total_completed_sales = 0;
                $sql = "SELECT o.orders_id, SUM(op.products_good_delivered_price) AS delivered_amt
                    FROM " . TABLE_ORDERS . " AS o
                    INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                        ON op.orders_id = o.orders_id
                        AND op.orders_products_is_compensate = 0
                    WHERE o.customers_id = " . $cust_row['customers_id'] . "
                        AND o.orders_status = 3
                        AND o.orders_cb_status IS NULL
                        AND op.custom_products_type_id <> 3
                    GROUP BY o.orders_id";
                $res = tep_db_query($sql, 'read_db_link');
                while ($row = tep_db_fetch_array($res)) {
                    if ($row["delivered_amt"] > 0) {
                        $_sql = "SELECT ot.value FROM " . TABLE_ORDERS_TOTAL . " AS ot WHERE ot.orders_id = " . $row["orders_id"] . " AND ot.class = 'ot_coupon'";
                        $_res = tep_db_query($_sql, 'read_db_link');
                        $_row = tep_db_fetch_array($_res);
                        if (isset($_row["value"]) && ($_row["value"] > 0)) {
                            $total_completed_sales += ($row["delivered_amt"] > $_row["value"] ? $row["delivered_amt"] - $_row["value"] : 0);
                        } else {
                            $total_completed_sales += $row["delivered_amt"];
                        }
                    }
                }

                if ($total_completed_sales >= $min_sales) {
                    $total_processed++;

                    $check_customer_status_sql = "SELECT customers_id
                                FROM " . TABLE_CUSTOMERS . "
                                WHERE customers_id = '" . tep_db_input($cust_row['customers_id']) . "'
                                AND customers_groups_id = '" . $from_status . "'";
                    $check_customer_status_res = tep_db_query($check_customer_status_sql);
                    if ($check_customer_status_row = tep_db_fetch_array($check_customer_status_res)) {
                        $customers_groups_id_update_sql = "	UPDATE " . TABLE_CUSTOMERS . "
                                                SET customers_groups_id = '" . $to_status . "'
                                                WHERE customers_id = '" . tep_db_input($cust_row['customers_id']) . "'";
                        tep_db_query($customers_groups_id_update_sql);

                        $sql_data_array = array(
                            'customers_id' => $cust_row['customers_id'],
                            'date_remarks_added' => 'now()',
                            'remarks' => 'Upgraded to ' . strtoupper($to_status_name) . ' by system',
                            'remarks_added_by' => 'system'
                        );
                        tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
                    }
                }
            }
        } else {
            break;
        }
    } while ($total_processed < $max_count);

    return $total_processed;
}

?>