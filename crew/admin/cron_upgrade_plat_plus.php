<?php
// Include application configuration parameters
require('includes/configure.php');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

$languages_id = 1;

// make a connection to the database... now
tep_db_connect();   // Need to get Category Name (Game Name)

$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

function cron_upgrade_to_plat_plus($customer_id) {
    $customers_groups_id_update_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
                                        SET customers_groups_id = '15' 
                                        WHERE customers_id = '" . tep_db_input($customer_id) . "'";
    tep_db_query($customers_groups_id_update_sql);

    $sql_data_array = array('customers_id' => $customer_id,
                            'date_remarks_added' => 'now()',
                            'remarks' => 'Upgraded to PLATINUM PLUS by system',
                            'remarks_added_by' => 'system');
    tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
}

// set application wide parameters
$configuration_sql = "	SELECT configuration_key as cfgKey, configuration_value as cfgValue 
                        FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_sql);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$languages_id = 1;
$email_content = 'Customer ID, Last 30 Days Purchase(USD)' . "\n";

$date_from = date("Y-m-d H:i:s", mktime (0,0,0, date('m'), date('d')-30, date('Y')));    // Inclusive
$date_to = date("Y-m-d H:i:s", mktime (0,0,0, date("m"), date('d'), date("Y"))); // Exclusive

// Exclude list
$exclude_list = array(226867, 196236, 338446);

$last_30_day_sales_select_sql ="    SELECT o.customers_id, SUM( op.products_good_delivered_price ) AS total_purchase 
                                    FROM orders AS o
                                    INNER JOIN orders_products AS op 
                                        ON o.orders_id = op.orders_id 
                                    INNER JOIN customers AS c 
                                        ON o.customers_id=c.customers_id 
                                    WHERE o.date_purchased >= '".$date_from."' 
                                        AND o.date_purchased < '".$date_to."' 
                                        AND o.orders_status = 3 
                                        AND op.custom_products_type_id <> 3
                                        AND c.customers_groups_id = 5
                                    GROUP BY o.customers_id
                                    HAVING SUM( op.products_good_delivered_price ) >= 1000";
$last_30_day_sales_result_sql = tep_db_query($last_30_day_sales_select_sql, 'read_db_link');

while ($last_30_day_sales_row = tep_db_fetch_array($last_30_day_sales_result_sql)) {
    if (!in_array($last_30_day_sales_row['customers_id'], $exclude_list)) {
        cron_upgrade_to_plat_plus($last_30_day_sales_row['customers_id']);

        $email_content .= $last_30_day_sales_row['customers_id'] . ',' . $last_30_day_sales_row['total_purchase'] . "\n";
    }
}

tep_mail('Alice Wong', '<EMAIL>', '[Cronjob] Daily Plat -> Plat Plus Upgrade (Based on Last 30-day sales)', $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

echo "Done";
?>