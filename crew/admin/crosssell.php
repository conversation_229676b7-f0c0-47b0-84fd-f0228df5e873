<?
/***
   $iD crossell.php$

   Copyright (C) 2001 - 2004 IN-Solution, <PERSON>
      http://www.in-solution.de
	  Updated by <PERSON> -->
	  http://www.z-is.net

                     All rights reserved.

   This program is free software licensed under the GNU General Public 
License (GPL).

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; either version 2 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307
   USA

***********************************************************************************
   based on:
   (c) 2000 - 2001 The Exchange Project
   (c) 2001 - 2003 osCommerce, Open Source E-Commerce Solutions
   Released under the GNU General Public License
***********************************************************************************
***/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$languages = tep_get_languages();

$in_products_id = tep_db_prepare_input($HTTP_GET_VARS['in_products_id']);

switch ($HTTP_GET_VARS['action']) {
        case 'new':
                $xsell_query = tep_db_query("INSERT INTO ". TABLE_PRODUCTS_XSELL . " (products_id, xsell_id) VALUES (" . $HTTP_GET_VARS['in_products_id'] . ", " . $HTTP_GET_VARS['iID'] . ")");
                break;
        case 'delete':
                $xsell_query = tep_db_query("DELETE FROM ". TABLE_PRODUCTS_XSELL . " WHERE xsell_id='" . $HTTP_GET_VARS['iID'] . "'");
                break;
}


if (isset($in_products_id) && tep_not_null($in_products_id)) {
        $product_query = tep_db_query("SELECT products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id='" . $in_products_id . "'");
        $product = tep_db_fetch_array($product_query);

        $xsell_query = tep_db_query("SELECT pd.products_name,px.xsell_id,p.products_price, p.products_tax_class_id FROM " . TABLE_PRODUCTS_DESCRIPTION . " pd 
                                       LEFT JOIN " . TABLE_PRODUCTS . " p ON p.products_id=pd.products_id
                                       LEFT JOIN " . TABLE_PRODUCTS_XSELL . " px ON px.xsell_id=pd.products_id
                                       WHERE px.products_id='" . $in_products_id . "' AND pd.language_id = '" . $languages_id . "'
                                    ");
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="popupcalendar" class="text"></div>
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top"><table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    </table></td>
<!-- body_text //-->
    <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?>&nbsp;<? echo $product[products_name]; ?></td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          </tr>
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS; ?></td>
                <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_PRODUCTS_PRICE; ?></td>
              </tr>
<?

        while($xsell = tep_db_fetch_array($xsell_query)) {
                if((!$HTTP_GET_VARS['iID']) || (@$HTTP_GET_VARS['iID'] == $xsell['xsell_id'])) {
                        $xinfo = new objectInfo($xsell);
                }

                if ( (is_object($xinfo)) && ($xsell['xsell_id'] == $xinfo->xsell_id) ) {
                        echo '              <tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_CROSSSELL_PRODUCS, 'page=' . $HTTP_GET_VARS['page'] . '&in_products_id=' . $HTTP_GET_VARS['in_products_id'] . '&iID=' .$xsell[xsell_id] ) . '\'">' . "\n"; #regt mi der scheiss heind aaf!

                } else {
                        echo '              <tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_CROSSSELL_PRODUCS, 'page=' . $HTTP_GET_VARS['page'] . '&in_products_id=' . $HTTP_GET_VARS['in_products_id'] . '&iID=' .$xsell[xsell_id] ) . '\'">' . "\n"; #regt mi der scheiss heind aaf!
                }
?>
                <td  class="dataTableContent"><?php echo $xsell['products_name']; ?></td>
                <td class="dataTableContent" align="right"><? echo $currencies->display_price($xsell['products_price'], tep_get_tax_rate($xsell['products_tax_class_id'])); ?></td>
              </tr>
<?
        }
        $heading = array();
        $contents = array();
        $heading[] = array('text' => '<b>' . $xinfo->products_name . '</b>');
        $contents[] = array('align' => 'center', 'text' => '</a> <a href="' . tep_href_link(FILENAME_CROSSSELL_PRODUCS, 'page=' . $HTTP_GET_VARS['page'] . '&in_products_id=' . $HTTP_GET_VARS['in_products_id'] . '&iID=' . $xinfo->xsell_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
?>
                        </table>
<?
if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
        
        echo '<br>            <td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

        echo tep_draw_form('search', FILENAME_CROSSSELL_PRODUCS, tep_get_all_get_params(array('action')), 'get'); 
        echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search', $HTTP_GET_VARS['search']);
        echo tep_draw_hidden_field('in_products_id', $HTTP_GET_VARS['in_products_id']);
        echo tep_draw_hidden_field('iID', $HTTP_GET_VARS['iID']);
        echo "</form>";

    echo '            </td>' . "\n";
  }

if ($HTTP_GET_VARS['search']) {
        echo '      <tr>' . "\n";
        echo '        <td><table border="0" width="100%" cellspacing="0" cellpadding="2">' . "\n";
        echo '          <tr class="dataTableHeadingRow">' . "\n";
        echo '            <td class="dataTableHeadingContent">' . TABLE_HEADING_MODEL . '</td>' . "\n";
        echo '            <td class="dataTableHeadingContent">' . TABLE_HEADING_NAME . '</td>' . "\n";
        echo '            <td class="dataTableHeadingContent" align="right">' . TABLE_HEADING_PRICE . '</td>' . "\n";
        echo '          </tr>' . "\n";

        $x_query = tep_db_query("SELECT xsell_id FROM " . TABLE_PRODUCTS_XSELL . " WHERE products_id='" . $HTTP_GET_VARS['in_products_id'] . "'");
        $tmp_query = "NOT IN(";
        while($x_ids = tep_db_fetch_array($x_query)) {
                $is = "TRUE";
                $tmp_query .= $x_ids[xsell_id].", ";
        } 
        $tmp_query[strlen($tmp_query)-2] = ")";
        if(!$is) {
                $tmp_query = "";
        }
        $products_query = tep_db_query("select p.products_id, p.products_model, pd.products_name, p.products_tax_class_id, 
                                        if(s.status, s.specials_new_products_price, p.products_price) as products_price, p.products_status 
                                        from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
                                        left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id 
                                        where p.products_id = pd.products_id 
                                        and pd.language_id = '" . $languages_id . "' and p.products_id " . $tmp_query . " 
                                        and (pd.products_name like '%" . $HTTP_GET_VARS['search'] . "%' or p.products_model like '%" . $HTTP_GET_VARS['search'] . "') 
                                        order by p.products_model");

        while ($products = tep_db_fetch_array($products_query)) {

        if (((!$HTTP_GET_VARS['in_products_id']) || (@$HTTP_GET_VARS['in_products_id'] == $products['products_id'])) && (!$xinfo)) {
            $pInfo = new objectInfo($products);

        }
           echo '          <tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_CROSSSELL_PRODUCS, 'search=' . $HTTP_GET_VARS['search'] . '&amp;in_products_id=' . $HTTP_GET_VARS['in_products_id'] . '&amp;iID=' . $products['products_id'] . '&action=new') . '\'">' . "\n";
            echo '            <td class="dataTableContent">' . $products['products_model'] . '</td>' . "\n";
            echo '            <td class="dataTableContent">' . $products['products_name'] . '</td>' . "\n";
            echo '            <td class="dataTableContent" align="right">' . $currencies->display_price($products['products_price'], tep_get_tax_rate($products['products_tax_class_id'])) . '</td>' . "\n";
?>
          </tr>
<?php
        }
        echo '        </table></td>' . "\n";
        echo '      </tr>' . "\n";
      }


?>
            </td>
            </tr>
          </tr>
        </table></td>
      </tr>
    </table></td>
<!-- body_text_eof //-->
  </tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>

