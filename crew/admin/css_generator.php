<?
/*
  	$Id: css_generator.php,v 1.152 2013/05/16 06:54:08 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

define("DIR_WS_CSS", DIR_FS_THEME.$_REQUEST["themeID"].'/css/');

$file_exist = file_exists(DIR_FS_THEME. $themes_row["themes_id"]) ? true : false;

$token_array = array (	"MAIN_BG", "ODD_BG", "EVEN_BG", "HEADER_BG", "LATEST_NEWS_BG", "LATEST_NEWS_BORDER", "INPUT_BG", "INPUT_BORDER", "ROLLOVER_BG1", "ROLLOVER_BG2",
						"PAGE_HEADING_LABEL", "SYSTEM_LABEL", "PRICING_LABEL", "INPUT_TEXT", "WARNING_LABEL", "SUCCESS_MSG_LABEL", "SMALLEST_FONT", "SMALL_FONT", "MEDIUM_FONT", "LARGE_FONT", "LARGEST_FONT",
						"MAIN_HEADING_NEWS_LABEL", "NEWS_TITLE_LABEL",
						"SYSTEM_NAV1", "SYSTEM_NAV2", "STORE_NAV1", "STORE_NAV2", "CAT_NAV_FONT",
						"BTN_BORDER_TOP_WIDTH", "BTN_BORDER_TOP_COLOUR", "BTN_BORDER_TOP_STYLE", "BTN_BORDER_BOTTOM_WIDTH", "BTN_BORDER_BOTTOM_COLOUR", "BTN_BORDER_BOTTOM_STYLE", "BTN_BORDER_LEFT_WIDTH", "BTN_BORDER_LEFT_COLOUR", "BTN_BORDER_LEFT_STYLE", "BTN_BORDER_RIGHT_WIDTH", "BTN_BORDER_RIGHT_COLOUR", "BTN_BORDER_RIGHT_STYLE", "BTN_FONT_COLOR", "BTN_BACKGROUND", 
						"BTN_BORDER_TOP_WIDTH_OVR", "BTN_BORDER_TOP_COLOUR_OVR", "BTN_BORDER_TOP_STYLE_OVR", "BTN_BORDER_BOTTOM_WIDTH_OVR", "BTN_BORDER_BOTTOM_COLOUR_OVR", "BTN_BORDER_BOTTOM_STYLE_OVR", "BTN_BORDER_LEFT_WIDTH_OVR", "BTN_BORDER_LEFT_COLOUR_OVR", "BTN_BORDER_LEFT_STYLE_OVR", "BTN_BORDER_RIGHT_WIDTH_OVR", "BTN_BORDER_RIGHT_COLOUR_OVR", "BTN_BORDER_RIGHT_STYLE_OVR", "BTN_FONT_COLOR_OVR", "BTN_BACKGROUND_OVR",
						"BTN_FONT", "BTN_MIN_WIDTH",
						"BTN_ADD_CART_BORDER_TOP_WIDTH", "BTN_ADD_CART_BORDER_TOP_COLOUR", "BTN_ADD_CART_BORDER_TOP_STYLE", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH", "BTN_ADD_CART_BORDER_BOTTOM_COLOUR", "BTN_ADD_CART_BORDER_BOTTOM_STYLE", "BTN_ADD_CART_BORDER_LEFT_WIDTH", "BTN_ADD_CART_BORDER_LEFT_COLOUR", "BTN_ADD_CART_BORDER_LEFT_STYLE", "BTN_ADD_CART_BORDER_RIGHT_WIDTH", "BTN_ADD_CART_BORDER_RIGHT_COLOUR", "BTN_ADD_CART_BORDER_RIGHT_STYLE", "BTN_ADD_CART_FONT_COLOR", "BTN_ADD_CART_BACKGROUND", 
						"BTN_ADD_CART_BORDER_TOP_WIDTH_OVR", "BTN_ADD_CART_BORDER_TOP_COLOUR_OVR", "BTN_ADD_CART_BORDER_TOP_STYLE_OVR", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR", "BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR", "BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR", "BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR", "BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR", "BTN_ADD_CART_BORDER_LEFT_STYLE_OVR", "BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR", "BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR", "BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR", "BTN_ADD_CART_FONT_COLOR_OVR", "BTN_ADD_CART_BACKGROUND_OVR",
						"BTN_PRE_ORDER_BORDER_TOP_WIDTH", "BTN_PRE_ORDER_BORDER_TOP_COLOUR", "BTN_PRE_ORDER_BORDER_TOP_STYLE", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH", "BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH", "BTN_PRE_ORDER_BORDER_LEFT_COLOUR", "BTN_PRE_ORDER_BORDER_LEFT_STYLE", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH", "BTN_PRE_ORDER_BORDER_RIGHT_COLOUR", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE", "BTN_PRE_ORDER_FONT_COLOR", "BTN_PRE_ORDER_BACKGROUND", 
						"BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR", "BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR", "BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR", "BTN_PRE_ORDER_FONT_COLOR_OVR", "BTN_PRE_ORDER_BACKGROUND_OVR",
						"BTN_NO_STOCK_BORDER_TOP_WIDTH", "BTN_NO_STOCK_BORDER_TOP_COLOUR", "BTN_NO_STOCK_BORDER_TOP_STYLE", "BTN_NO_STOCK_BORDER_BOTTOM_WIDTH", "BTN_NO_STOCK_BORDER_BOTTOM_COLOUR", "BTN_NO_STOCK_BORDER_BOTTOM_STYLE", "BTN_NO_STOCK_BORDER_LEFT_WIDTH", "BTN_NO_STOCK_BORDER_LEFT_COLOUR", "BTN_NO_STOCK_BORDER_LEFT_STYLE", "BTN_NO_STOCK_BORDER_RIGHT_WIDTH", "BTN_NO_STOCK_BORDER_RIGHT_COLOUR", "BTN_NO_STOCK_BORDER_RIGHT_STYLE", "BTN_NO_STOCK_FONT_COLOR", "BTN_NO_STOCK_BACKGROUND", 
						"BTN_NO_STOCK_BORDER_OVR", "BTN_NO_STOCK_FONT_COLOR_OVR", "BTN_NO_STOCK_BACKGROUND_OVR");

$token_properties["BTN_MIN_WIDTH"] = array( 'NO_COLON' => true);
/*
if(preg_match($Pattern, $text, $regs)) {
	echo htmlentities($regs[0])."<br>".htmlentities($regs[1])."<br>".htmlentities($regs[2])."<br>".htmlentities($regs[3]);
} else echo "not found";
*/
$css_template = "
	
	/*DEF_SMALLEST_FONT*/
	\$SMALLEST_FONT = ##SMALLEST_FONT##
	/*DEF_SMALLEST_FONT*/\n
	/*DEF_SMALL_FONT*/
	\$SMALL_FONT = ##SMALL_FONT##
	/*DEF_SMALL_FONT*/\n
	/*DEF_MEDIUM_FONT*/
	\$MEDIUM_FONT = ##MEDIUM_FONT##
	/*DEF_MEDIUM_FONT*/\n
	/*DEF_LARGE_FONT*/
	\$LARGE_FONT = ##LARGE_FONT##
	/*DEF_LARGE_FONT*/\n
	/*DEF_LARGEST_FONT*/
	\$LARGEST_FONT = ##LARGEST_FONT##
	/*DEF_LARGEST_FONT*/\n
	/*DEF_BTN_FONT*/
	\$BTN_FONT = ##BTN_FONT##
	/*DEF_BTN_FONT*/\n
	/*DEF_CAT_NAV_FONT*/
  	\$CAT_NAV_FONT = ##CAT_NAV_FONT##
  	/*DEF_CAT_NAV_FONT*/
  	
	/*DEF_BTN_BORDER_TOP_WIDTH*/
	\$BTN_BORDER_TOP_WIDTH = ##BTN_BORDER_TOP_WIDTH##
	/*DEF_BTN_BORDER_TOP_WIDTH*/\n
	/*DEF_BTN_BORDER_BOTTOM_WIDTH*/
	\$BTN_BORDER_BOTTOM_WIDTH = ##BTN_BORDER_BOTTOM_WIDTH##
	/*DEF_BTN_BORDER_BOTTOM_WIDTH*/\n
	/*DEF_BTN_BORDER_LEFT_WIDTH*/
	\$BTN_BORDER_LEFT_WIDTH = ##BTN_BORDER_LEFT_WIDTH##
	/*DEF_BTN_BORDER_LEFT_WIDTH*/\n
	/*DEF_BTN_BORDER_RIGHT_WIDTH*/
	\$BTN_BORDER_RIGHT_WIDTH = ##BTN_BORDER_RIGHT_WIDTH##
	/*DEF_BTN_BORDER_RIGHT_WIDTH*/\n
	
	/*DEF_BTN_BORDER_TOP_WIDTH_OVR*/
	\$BTN_BORDER_TOP_WIDTH_OVR = ##BTN_BORDER_TOP_WIDTH_OVR##
	/*DEF_BTN_BORDER_TOP_WIDTH_OVR*/\n
	/*DEF_BTN_BORDER_BOTTOM_WIDTH_OVR*/
	\$BTN_BORDER_BOTTOM_WIDTH_OVR = ##BTN_BORDER_BOTTOM_WIDTH_OVR##
	/*DEF_BTN_BORDER_BOTTOM_WIDTH_OVR*/\n
	/*DEF_BTN_BORDER_LEFT_WIDTH_OVR*/
	\$BTN_BORDER_LEFT_WIDTH_OVR = ##BTN_BORDER_LEFT_WIDTH_OVR##
	/*DEF_BTN_BORDER_LEFT_WIDTH_OVR*/\n
	/*DEF_BTN_BORDER_RIGHT_WIDTH_OVR*/
	\$BTN_BORDER_RIGHT_WIDTH_OVR = ##BTN_BORDER_RIGHT_WIDTH_OVR##
	/*DEF_BTN_BORDER_RIGHT_WIDTH_OVR*/\n
	
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH*/
	\$BTN_ADD_CART_BORDER_TOP_WIDTH = ##BTN_ADD_CART_BORDER_TOP_WIDTH##
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH*/\n
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH*/
	\$BTN_ADD_CART_BORDER_BOTTOM_WIDTH = ##BTN_ADD_CART_BORDER_BOTTOM_WIDTH##
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH*/\n
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH*/
	\$BTN_ADD_CART_BORDER_LEFT_WIDTH = ##BTN_ADD_CART_BORDER_LEFT_WIDTH##
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH*/\n
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH*/
	\$BTN_ADD_CART_BORDER_RIGHT_WIDTH = ##BTN_ADD_CART_BORDER_RIGHT_WIDTH##
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH*/\n
	
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH_OVR*/
	\$BTN_ADD_CART_BORDER_TOP_WIDTH_OVR = ##BTN_ADD_CART_BORDER_TOP_WIDTH_OVR##
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH_OVR*/\n
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR*/
	\$BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR = ##BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR##
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR*/\n
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR*/
	\$BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR = ##BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR##
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR*/\n
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR*/
	\$BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR = ##BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR##
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR*/\n
	
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH*/
	\$BTN_PRE_ORDER_BORDER_TOP_WIDTH = ##BTN_PRE_ORDER_BORDER_TOP_WIDTH##
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH*/
	\$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH = ##BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH##
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH*/
	\$BTN_PRE_ORDER_BORDER_LEFT_WIDTH = ##BTN_PRE_ORDER_BORDER_LEFT_WIDTH##
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH*/
	\$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH = ##BTN_PRE_ORDER_BORDER_RIGHT_WIDTH##
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH*/\n
	
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR*/
	\$BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR = ##BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR##
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR*/
	\$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR = ##BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR##
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR*/
	\$BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR = ##BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR##
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR*/\n
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR*/
	\$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR = ##BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR##
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR*/\n
	
	/*DEF_BTN_NO_STOCK_BORDER_TOP_WIDTH*/
	\$BTN_NO_STOCK_BORDER_TOP_WIDTH = ##BTN_NO_STOCK_BORDER_TOP_WIDTH##
	/*DEF_BTN_NO_STOCK_BORDER_TOP_WIDTH*/\n
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_WIDTH*/
	\$BTN_NO_STOCK_BORDER_BOTTOM_WIDTH = ##BTN_NO_STOCK_BORDER_BOTTOM_WIDTH##
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_WIDTH*/\n
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_WIDTH*/
	\$BTN_NO_STOCK_BORDER_LEFT_WIDTH = ##BTN_NO_STOCK_BORDER_LEFT_WIDTH##
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_WIDTH*/\n
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_WIDTH*/
	\$BTN_NO_STOCK_BORDER_RIGHT_WIDTH = ##BTN_NO_STOCK_BORDER_RIGHT_WIDTH##
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_WIDTH*/\n
	/*DEF_LINE_HEIGHT*/
	\$LINE_HEIGHT = 'normal'; 
	/*DEF_LINE_HEIGHT*/\n
	/*DEF_FONT_FAMILY*/
	\$FONT_FAMILY = 'Arial'; 
	/*DEF_FONT_FAMILY*/\n
:~:END_OF_PHP:~: 

html, body {
	margin: 0px;
	padding: 0px;
	height: 100%;
	background-color: #edfbfe;
	color: #000000;
}

body, td, div, span, p , font {
	font-family: Arial, Tahoma, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
	text-align: left;
}

a {	color: #004B91;	text-decoration: none;}
a:active { color: #004B91; text-decoration: none;}
a:link { color: #004B91; text-decoration: none;}
a:visited { color: #004B91;	text-decoration: none;}
a:hover { text-decoration: underline;}

.breakLine {height: 8px;clear:both;}
.breakLineImg {
	clear:both;
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) 0 -591px repeat-x;
	height: 5px;
}
.halfBreakLine {height: 5px;clear:both;}
.dottedLine { margin: 5px 0px; border-width:0px;border-bottom-width:1px;border-style:dotted;border-color: #A4A4A4;clear:both;}
.dottedLineGray { margin: 0; border-width:0px;border-bottom-width:1px;border-style:dotted;border-color: #cecece;clear:both;}
.solidGreyLine { margin: 5px 0px; border-width:0px;border-bottom-width:1px;border-style:solid;border-color: #A4A4A4;clear:both;}
.solidLine { margin: 5px 0px; border-width:0px;border-bottom-width:1px;border-style:solid;border-color: #666666;clear:both;}
.solidThickLine { margin: 5px 0px; border-width:0px;border-bottom-width:2px;border-style:solid;border-color: #303030;clear:both;}
.solidWhiteLineThick { border-width:0px;border-bottom-width:1px;border-style:solid;border-color: #666666;clear:both;width:225px;margin:0px 10px;border-color:#ffffff;border-size: 2px;clear:both;}
.dashedLine {	margin: 5px 0px; border-width:0px;border-bottom-width:1px;border-style:dashed;border-color: #666666;clear:both;}
.spacer_20 { float:left;width:20px; }
/* Images */

.smallestFont {font-size: {\$SMALLEST_FONT}px;}
.smallFont {font-size: {\$SMALL_FONT}px;}
.mediumFont {font-size: {\$MEDIUM_FONT}px;}
.largeFont {font-size: {\$LARGE_FONT}px;}
.largestFont {font-size: {\$LARGEST_FONT}px;}

div.redCloseIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."icons/x_button.gif) no-repeat;
	width: 14px;
	height:14px;
}

div.searchPictureIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -208px -122px;
	width: 13px;
	height: 16px;
	float:left;
	margin: 3px 2px;
}

div.searchCategoryIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -12px -157px;
	width: 9px;
	height: 8px;
	float:left;
	margin: 8px 5px 0 0;
	float:left;
}

div.searchCategoryOptions {
	position: absolute;
	top: 7px;
	right: 52px;
}

.searchCategorySelectField {
	width:156px;
	filter: alpha(opacity=0)
	-moz-opacity: 0;
	opacity: 0;
	border: 0px;
}


div.searchGoButton {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -12px -165px;
	width: 36px;
	height: 16px;
	float:left;
	margin: 5px 5px;
	cursor:pointer;
}

div.triangleRightIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -212px -233px;
	width: 5px;
	height: 9px;
	float:left;
}

div.triangleRightIconBlue {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -222px -132px;
	width: 5px;
	height: 9px;
	float:left;
}

div.triangleRightIconGlay {
	background: transparent url(".DIR_WS_CSS_IMAGE."box_images_2011.gif) no-repeat;
	background-position: -38px -2px;
	width: 5px;
	height: 9px;
	float:left;
}

div.closeIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -218px -233px;
	width: 10px;
	height: 10px;
	float:left;
	cursor:pointer;
}

div.closeIconBlack {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -222px -122px;
	width: 10px;
	height: 10px;
	float:left;
	cursor:pointer;
}

div.questionMarkIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -190px -508px;
	width: 42px;
	height: 42px;
}

div.deleteIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -222px -122px;
	width: 10px;
	height: 10px;
}

div.trashIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -132px -533px;
	width: 11px;
	height: 14px;
}

div.noteIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: -147px -522px;
	width: 14px;
	height: 14px;
}

.transparent_class {
	filter:alpha(opacity=0);
	-moz-opacity:0;
	-khtml-opacity: 0;
	opacity: 0;
}
/* End Images */

/* Layout */

div.headerTopText {
	background-color: #0260c0;
	float:left;
	width:100%;
	position:relative;
	height:10px;
}

div.top50Window {
	z-index: 9999;
	position:absolute;
	width: 966px;
	top: 5px;
	left: 8px;
}

a.whiteText,span.whiteText {
	color: #FFFFFF;
}

a.whiteText:link,span.whiteText:link {
	color: #FFFFFF;
}

a.whiteText:visited,span.whiteText:visited {
	color: #FFFFFF;
}

a.whiteText:active,span.whiteText:active {
	color: #FFFFFF;
}

a.whiteText:hover,span.whiteText:hover {
	text-decoration:underline;
	color: #FFFFFF;
}

.whiteText {
	color: #FFFFFF;
}

div.headerTopLine {
	background-color: #479cd3;
	height: 1px;
	clear:both;
}

div.headerTopImage {
	background: transparent url(".DIR_WS_CSS_IMAGE."horizontal_picture.gif) no-repeat;
	background-position: 0px 0px;
	width: 984px;
	height:3px;
}

div.footerBottomImage {
	background: transparent url(".DIR_WS_CSS_IMAGE."horizontal_picture.gif) no-repeat;
	background-position: 0px -4px;
	width: 984px;
	height:5px;
	position:relative;
}

div.footerBottomBackground {
	background-color: #000000;
	width: 982px;
	height:85px;
	filter:alpha(opacity=10)
	-moz-opacity: 0.1;
	opacity: 0.1;
	position: absolute;
	z-index:1;
}

div.footerBottomText {
	width: 982px;
	height:85px;
	position: absolute;
	z-index:2;
}

div.headerTop {
	position:relative;
}

div.headerMiddle {
	width:984px;
}

div.headerMiddleLeft, div.headerMiddleRight {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	float:left;
}

div.headerMiddleLeft{
	background-position: -235px -126px;
	width: 5px;
	height:32px;
}

div.headerMiddleContent{
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	float:left;
	background-position: 0px -67px;
	width: 975px;
	height:32px;
}

div.headerMiddleRight{
	background-position: -241px -126px;
	width: 4px;
	height:32px;
}

div.headerLoginPanel {
	color:#ffffff;
	font-size:{\$SMALL_FONT}px;
	position: absolute;
	top: 43px;
	left : 300px;
}

div#select_link_zones {
	border: 1px solid transparent;
	float:left;
	cursor:pointer;
	width:18px;
}

#link_zones_separator {
	margin: 0px 1px 0px 4px;
}

div.headerShoppingCart {
	color:#ffffff;
	font-size:{\$SMALL_FONT}px;
	position: absolute;
	top: 38px;
	right : 0px;
	width: 245px;
	z-index:20;
}

div.miniShoppingCart {
	position: absolute;
	top: -55px;
	right : 0px;
	width: 245px;
	z-index:21;
}

div.miniShoppingCartTotal {
	float:right;
	text-align:right;
	width:115px;
}

div.miniShoppingCartTotalItems {
	float:left;
	width:110px;
	text-align:left;
}

div.miniShoppingCartTotalItems a {
	color:#5B5B5B;
}

div.miniShoppingCartBackgroundTop {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: 0px -206px;
	width: 245px;
	height: 4px;
}

div.miniShoppingCartBackgroundMiddle {
	background-color: #000000;
}

div.miniShoppingCartBackgroundBottom {
	clear: both;
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: 0px -227px;
	width: 245px;
	height: 4px;
}

div.miniCartTable {
	width: 225px;
	float:left;
}

div.miniCartColumn1 {
	float:left;
	text-align:left;
	width:140px
}

div.miniCartColumn2 {
	float:right;
	text-align:right;
	width:84px
}

div.shoppingCartText {
	color:#8dd1f4;
	font-size:{\$SMALL_FONT}px;
	margin-top: 5px;
	white-space:nowrap;
	float:right;
}

div.shoppingCartText div a:hover b font {
	text-decoration: underline;
}

div.headerTopTab {
	height:	80px;
	height:110px;
	position:relative;
}

div.headerTopTabContent {
	position: relative;
}

div.headerTabMenu {
	bottom:0px;
	position:absolute;
	width:100%;
}

div.bodyTopImage {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: 0px -99px;
	width: 984px;
	height: 7px;
	background-color: #ffffff;
	clear:both;
}

div.searchPanel {
	float:right;
	margin: 3px 0;
	height:24px;
}

div.searchBoxLeft, div.searchBoxRight, div.searchBoxMiddle {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	float:left;
}

div.searchBoxLeft {
	background-position: 0px -157px;
	width: 5px;
	height: 24px;
}

div.searchBoxRight {
	background-position: -6px -157px;
	width: 5px;
	height: 24px;
}

div.searchBoxMiddle {
	background-position: 0px -182px;
	height: 24px;
}

div.searchField {
	float:left;
}

input.searchFieldInput {
	border:0 none;
	font-family:Arial,Tahoma,Geneva,sans-serif;
	font-size:{\$SMALL_FONT}px;
	font-size-adjust:none;
	margin:2pt 2pt 0 3px;
	width:150px;
}

div.searchCategory {
	margin:3pt 8px;
	float:left;
	color: #666666;
	width: 120px;
}

div.bodyContent {
	background-color: #FFFFFF;
	min-height: 700px;
	width:100%
	float: left;
}

div.footerContent {
	background-color: #FFFFFF;
	height: 140px;
}


div.bodyContentLeft {
	width: 187px;
	float:left;
}

div.bodyContentCenterLeft {
	width: 598px;
	float:left;
}

div.bodyContentCenterLeftWide {
	width: 780px;
	float:left;
}

div.bodyContentCenterRight {
	width: 139px;
	float:right;
}

div.leftBoxTop {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: 0px -337px;
	width: 187px;
	height: 5px;
}

div.leftBoxMiddle {
	background: transparent url(".DIR_WS_CSS_IMAGE."horizontal2_picture.gif) repeat-y;
	background-position: 0 0;
	background-color: #e6e6e6;
	width: 187px;
}

div.leftBoxBottom {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: 0px -343px;
	width: 187px;
	height: 5px;
}

div.rightBoxTop {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: 0px -361px;
	width: 180px;
	height: 5px;
}

div.rightBoxMiddle {
	background: transparent url(".DIR_WS_CSS_IMAGE."horizontal2_picture.gif) repeat-y;
	background-position: -188px 0px;
	background-color: #e6e6e6;
	width: 180px;
}

div.rightBoxBottom {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: 0px -367px;
	width: 180px;
	height: 5px;
}

div.boxHeader {
	width: 175px;
	height: 25px;
	margin: 0 0 0 5px;
}

div.boxHeaderSmall {
	width: 168px;
	height: 25px;
	margin: 0px 10px;
}

div.boxContent {
	width: 175px;
	min-height: 25px;
	margin: 0px 5px;
}

div.boxContent ul {
	margin: 0px;
	padding: 5px 5px 5px 5px;
	clear: both;
}

div.boxContent ul li {
	list-style: none outside none; 
}

div.boxContentShort {
	padding: 5px 10px;
	color: #333333;
}

div.boxContentSmall {
	width: 168px;
	margin: 0px 10px;
}

div.boxHeaderLeft {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -207px transparent;
	width: 7px;
	height: 25px;
	float:left;
}

div.boxHeaderRight {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat right -207px transparent;
	width: 7px;
	height: 25px;
	float:left;
}

.boxHeaderLeftTable {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -207px transparent;
	width: 7px;
	height: 25px;
}

.boxHeaderLeftHigherTable {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 67px -508px transparent;
	width: 5px;
	height: 51px;
}

.boxHeaderRightTable {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat right -207px transparent;
	width: 7px;
	height: 25px;
}

.boxHeaderRightHigherTable {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 60px -508px transparent;
	width: 5px;
	height: 51px;
}

div.boxHeaderExpand {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat -44px -508px transparent;
	width: 43px;
	height: 25px;
	float:left;
}

div.boxHeaderDivider {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat -44px -508px transparent;
	width: 33px;
	height: 25px;
	float:left;
}

div.boxHeaderCollapse {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -508px transparent;
	width: 43px;
	height: 25px;
	float:left;
}

div.boxHeaderCenter {
	overflow: hidden;
	background-color: #000000;
	width: 161px;
	height: 25px;
	float:left;
}

.boxHeaderCenterTable {
	background-color: #000000;
	height: 25px;
}

.boxHeaderCenterTable th {
	color: white;
	font-weight: normal;
	font-size: {\$SMALL_FONT}px;
}

.ordersListing {
	height: 25px;
}

.ordersListingOver {
	height: 25px;
	background-color: #dbdbdb;
}

.ordersListingOut {
	height: 25px;
	background-color: #fbfbfb;
}

div.boxHeaderCenterGames {
	background-color: #000000;
	width: 554px;
	height: 25px;
	float:left;
}

div.boxMiddleContentGames {
	width: 568px;
}

div.loginBoxLink {
	float:left;
	font-weight: bold;
}

div.loginBoxHeader {
	width: 456px;
	margin: 5px 0 5px 5px;
}

div.loginBoxHeaderLeft {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: -178px -508px;
	width: 5px;
	height: 51px;
	float:left;
}

div.loginBoxHeaderRight {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: -184px -508px;;
	width: 5px;
	height: 51px;
	float:left;
}

div.loginBoxHeaderCenter {
	background-color: #000000;
	width: 446px;
	height: 51px;
	float:left;
}

div.loginBoxHeaderCenter font{
	font-size: {\$SMALL_FONT}px;
	color: #ffffff;
	padding: 5px 0 0 6px;
	float:left; 
}

div.loginBoxContent {
	padding: 5px;
}

div.boxHeaderCenterShort {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: -7px -207px;
	width: 156px;
	height: 25px;
	float:left;
}

div.boxHeaderCenterShort font {
	font-size: {\$SMALL_FONT}px;
	color: #ffffff;
	padding: 5px 0;
	float:left; 
}

div.boxHeaderCenterSmall font {
	font-size: {\$SMALL_FONT}px;
	color: #ffffff;
	padding: 6px 0;
	float:left; 
}

div.boxHeaderCenterSmall font {
	font-size: {\$SMALL_FONT}px;
	color: #ffffff;
	padding: 6px 0;
	float:left; 
}

div.boxHeaderCenter font, div.boxHeaderCenterGames font {
	font-size: {\$SMALL_FONT}px;
	color: #ffffff;
	padding: 5px 0 0 0;
	display:block;
	font-weight: bold;
}

div.boxHeaderExpandIcon {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x;
	background-position: -200px -233px;
	width: 11px;
	height: 11px;
	margin: 7px 0px;
	float:right;
}

a.boxLinkFirst , a.boxLink, a.boxLinkSelectedFirst , a.boxLinkSelected{
	font-size: {\$SMALL_FONT}px;
	display:block;
    padding:5px 0px 5px 0px;
    width:100%;
	font-weight: bold;
}

a.boxLink {
	border-top:1px dotted #000000;
}

a.boxLinkFirst , a.boxLink, a.boxLinkFirst:active , a.boxLink:active, a.boxLinkFirst:link , a.boxLink:link, a.boxLinkFirst:visited , a.boxLink:visited {
	color:#000000;
}

a.boxLinkFirst:hover , a.boxLink:hover {
	color:#999999;
}

a.boxLinkSelectedFirst , a.boxLinkSelected {
	color:#004B91;
}

a.boxLink {
	border-top:1px dotted #000000;
}

div.prouctListingEntryTop {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: 0 -349;
	height: 5px;
	width: 132px;
}

div.prouctListingEntryMiddle {
	background: transparent url(".DIR_WS_CSS_IMAGE."horizontal2_picture.gif) repeat-y;
	background-position: -369px 0;
	width: 132px;
}

div.prouctListingEntryMiddle div {
	padding:5px;
	text-align:center;
}

div.prouctListingEntryBottom {
	background: transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: 0 -355;
	height: 5px;
	width: 132px;
}

div.middleBox {
	border-style:solid;
	border-width:1px;
	border-color:#cecece;
	padding: 0;
}

div.middleBoxPadding {
	border-style:solid;
	border-width:1px;
	border-color:#cecece;
	padding: 14px;
}

div.middleBoxHeader {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x scroll 0 -560px transparent;
	height: 37px;
}

div.middleBoxHeader h2 {
	font-size: {\$LARGEST_FONT}px;
	font-weight: bold;
	padding: 8px 0 0 15px;
	margin:0px;
	display:block;
}


div.middleBoxContent {
	width:100%;
}

div.middleBoxRow {
	display:block;
	margin: 0px;
	position:relative;
	padding: 10px 10px 10px 35px;
	float: left;
}

div.middleBoxRowCol1, div.middleBoxRowCol2, div.middleBoxRowCol3 {	float:left; }
div.middleBoxRowCol1 { width: 270px; font-weight: bold; }
div.middleBoxRowCol2 { width: 140px; }
div.middleBoxRowCol3 { width: 120px; text-align: center; }

div.middleBoxRow div.newIcon {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x -88px -508px transparent;
	height: 43px;
	width: 43px;
	position:absolute;
	left: 0px;
	top: 0px;
}

div.loginColumn {
	width: 467px;
	float: left;
}

div.loginColumnBorder {
	width: 100%;
	float: left;
}

div.grayLineBox {
	border:solid 1px #cfcfcf;
	width: 100%;
	float: left;
}

div.divTableRow {
	padding: 10px 0px;
	clear: both;
	float:left;
}

div.divTableRowTight {
	padding: 5px 0px;
	clear: both;
	float:left;
}

div.divTableCell {
	float:left;
}

div.divTableCell font {
	display:block;
	font-size:{\$SMALL_FONT}px;
	padding:6px 0px;
}

div.loginRightBoxHeader {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -632px transparent;
	height: 56px;
	width: 180px;
}

div.loginRightBoxContent {
	background-color: #000000;
}

div.loginRightBoxFooter {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -689px transparent;
	height: 52px;
	width: 180px;
}

div.select_box_zones {
	left:30px;
	margin:0px;
	padding:0px;
	position:absolute;
	top:0px;
	z-index: 999999;
	text-align:left;
	display:none;
	height:200px;
	width: 360px;
}

div.select_box_zones_countries {
	text-align:left;
	height:170px;
	width: 360px;
	overflow: auto;
	color: #FFFFFF;
}

div.select_box_zones_background {
	left:25px;
	margin:0px;
	padding:0px;
	position:absolute;
	top:-5px;
	z-index: 999998;
	display:none;
	height:210px;
	width: 370px;
	background-color: #000000;
	opacity:0.8;
	filter: alpha(opacity=80);
	-moz-opacity: 0.8;
}

div.select_box_currencies {
	left:180px;
	margin:0px;
	padding:0px;
	position:absolute;
	top:-8px;
	z-index: 999999;
	text-align:left;
	display:none;
	width: 220px;
	height:210px;
	overflow: auto;
}

div.select_box_currencies_background {
	left:180px;
	margin:0px;
	padding:0px;
	position:absolute;
	top:-8px;
	z-index: 999998;
	background: #000000;
	opacity:0.8;
	filter: alpha(opacity=80);
	-moz-opacity: 0.8;
	display:none;
	width: 230px;
	height:220px;
	overflow: auto;
}

input.zoneCountryFilter {
	border:0 none;
	font-family:Arial,Tahoma,Geneva,sans-serif;
	font-size:{\$SMALL_FONT}px;
	font-size-adjust:none;
	width:100%;
	padding:4px 5px 4px 10px;
	color: #ffffff;
	background-color: #000000;
}

div.select_zones{display:block; padding: 4px 5px 4px 10px; white-space:nowrap;cursor:pointer;width:325px;float:left;clear:both;color:#FFFFFF}
div.select_zones:hover {background-color:#000000; text-decoration: none;}

div.select_currencies{display:block; padding: 4px 5px 4px 10px; color:#FFFFFF;white-space:nowrap;cursor:pointer;width:180px;float:left;clear:both}
div.select_currencies:hover {background-color:#000000; color:#FFFFFF; text-decoration: none;}

div.alphabetSeletor {
	border-style: solid;
	border-color: #cdcdcd;
	border-width: 1px; 
	padding: 2px 5px;
	float:left;
	margin: 0 4px 0 0;
	background-color: #f8f8f8;
	color: #dddddd;
}

div.alphabetSeletor h3 {
	color: #000000;
}

div.mainPageBlackBG {
	background: #000000;
	float: left;
	width: 598px;
}

div.mainPageBlackBGPadding {
	padding:20px;
	color:#ffffff;
	float:left;
}

div.mainPageBlackColumn {
	float: left;
	width: 198px;
}

div.mainPageBlackColumnGames {
	float: left;
	width: 180px;
}

div.mainPageBlackColumnGamesSpace {
	float: left;
	width: 9px;
}

div.mainPageBlackColumnContent {
	float:left;
	width: 183px;
}

div.mainPageBlackColumnContent a.mainPageBlackLink {
	border-bottom:1px dotted #666666;
	display:block;
	width:100%;
	padding:5px 0px;
	color: #ffffff;
}

.imageBorder {padding:2px; border:1px solid #cccccc; float:left;}
a.imageBorder:link {display:block; padding:2px; border:1px solid #cccccc;}
a.imageBorder:visited {display:block; padding:2px; border:1px solid #cccccc;}
a.imageBorder:hover {display:block; padding:0; border:3px solid #004B91;}

div.boxHeaderMiddle {
	height: 25px;
	margin: 0;
}

div.boxHeaderMiddleGames {
	height: 25px;
	margin: 0;
	width: 568px;
}

div.middleBox b a {
	color: #000000;
}

div.mainPageBG {
	background: ;
	float: left;
	width:100%;
	height:150px;
}

div.middleBoxRightColumn {
	border:solid 1px #cecece;
	width: 720px;
	padding: 14px;
}

div.theLayer {
	position:absolute;
	width:450px;
	left:320px;
	top:351px;
	visibility:hidden;
	z-index:999;
}

div.theLayerBg {
	visibility:hidden;
	border: medium none ;
	margin: 0pt;
	padding: 0pt;
	z-index: 998;
	width: 100%;
	top: 0pt;
	left: 0pt;
	background-color: rgb(0, 0, 0);
	opacity:0.5;
	filter: alpha(opacity=50);
	-moz-opacity: 0.5;
	position: fixed;
	height: 100%;
}

#scrollup {
	position: relative;
	overflow: hidden;
	height: 110px;
	width: 160px;
	cursor: pointer;
}

#pgScrollUp {
	position: relative;
	overflow: hidden;
	height: 70px;
	width: 160px;
	cursor: pointer;
}

.headline, .pgHeadLine {
	position: absolute;
	height: 60px;
	width: 155px;
	display:none;
}

/* End Layout */

/* TAB */

ul.headerTopNode {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin:0;
	padding:0;
	position: absolute;
	right: 0px;
	top: -1px;
}

ul.headerTopNode li {
	float:left;
	margin:0;
	padding:0;
}

ul.headerTopNode a {
	cursor:pointer;
	text-decoration:none;
}

ul.headerTopNode a, ul.headerTopNode li.notSelectedLast div, ul.headerTopNode li.selectedLast div {
	background:transparent url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat;
	background-position: 0 -268px;
	display:block;
	float:left;
	height:22px;
	margin:0;
	padding:0;
}

ul.headerTopNode font {
	color:#FFFFFF;
	display:block;
	font-size:{\$SMALL_FONT}px;
	padding:3px 10px 0;
	white-space:nowrap;
}

ul.headerTopNode li.selectedFirst font, ul.headerTopNode li.selectedLast font, ul.headerTopNode li.selectedMiddle font {
	color:#8dd1f4;
}

ul.headerTopNode li.selectedFirst a {
	background-position:0 -245px;
}

ul.headerTopNode li.selectedFirst a , ul.headerTopNode li.notSelectedFirst a {
	padding: 0 0 0 13px;
}

ul.headerTopNode li.notSelectedMiddle a {
	background-position:0 -291px;
}

ul.headerTopNode li.selectedMiddle a {
	background-position:0 -314px;
}

ul.headerTopNode li.selectedLast a {
	background-position:right -245px;
}

ul.headerTopNode li.selectedLast a , ul.headerTopNode li.notselectedLast a {
	padding: 0 13px 0 0;
}

ul.headerTopNode li.notselectedLast a {
	background-position:right -268px;
}

ul.headerTopNode li.selectedLast div {
	float:left;
	background-position:0 -314px;
	width: 2px;
}

ul.headerTopNode li.notSelectedLast div {
	float:left;
	background-position:0 -314px;
	width: 2px;
}

ul.headerTopNode li.notSelectedLast a:hover {
	background-position:right -245px;
	text-decoration: none;
}

ul.headerTopNode li.notSelectedFirst a:hover {
	background-position:0 -245px;
	text-decoration: none;
}

ul.headerTopNode li.notSelectedMiddle a:hover {
	background-position:0 -314px;
	text-decoration: none;
}

div.tab2Header {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) repeat-x 0px -598px transparent;
	height:33px;
}

div.tab2Content {
	border-left:solid 1px #caccc9;
	border-right:solid 1px #caccc9;
	border-bottom:solid 1px #caccc9;
	background-color:#f2f4f1;
}

ul.tab2 {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin:0;
	padding:0;
	left : 0px;
	top: 1px;
	display:block;
}

ul.tab2 li {
	float:left;
	margin:0;
	padding:0 5px;
}

ul.tab2 a {
	cursor:default;
	text-decoration:none;
}

ul.tab2 li.tab2NotSelected a {
	cursor:pointer;
	text-decoration:none;
}

ul.tab2 li.tab2First {
	font-weight: bold;
}

ul.tab2 li.tab2First font {
	color: #000000;
}

ul.tab2 li.tab2Selected a,ul.tab2 li.tab2NotSelected a, ul.tab2 li.tab2NotSelected span, ul.tab2 li.tab2Selected span {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat scroll 0 50% transparent;
	display:block;
	float:left;
	height:33px;
	margin:0;
	padding:0;
}

ul.tab2 font {
	color:#004B91;
	display:block;
	font-size:{\$SMALL_FONT}px;
	padding:6px 19px 0;
	white-space:nowrap;
}

ul.tab2 li.tab2NotSelected a, ul.tab2 li.tab2Selected a {
	cursor: pointer;
}

ul.tab2 a:hover {
	text-decoration: none;
}

ul.tab2 li.tab2NotSelected a:hover font {
	color:#999999;
}

ul.tab2 li.tab2Selected font {
	color:#333333;
}

ul.tab2 li.tab2Selected a {
	background-position:0 -373px;
}

ul.tab2 li.tab2Selected span {
	background-position:right -406px;
}

ul.tab2 li.tab2NotSelected a {
	background-position:0 -440px;
}

ul.tab2 li.tab2NotSelected span {
	background-position:right -474px;
}

ul.tab3 {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin:0;
	padding:0;
	left : 0px;
	top: 1px;
	display:block;
}

ul.tab3 li {
	float:left;
	margin:0;
	padding:0 6px 0 0;
}

ul.tab3 a {
	cursor:default;
	text-decoration:none;
}

ul.tab3 li.tab3NotSelected a {
	cursor:pointer;
	text-decoration:none;
}

ul.tab3 li.tab3First {
	font-weight: bold;
}

ul.tab3 li.tab3First font {
	color: #000000;
}

ul.tab3 li.tab3Selected a,ul.tab3 li.tab3NotSelected a, ul.tab3 li.tab3NotSelected span, ul.tab3 li.tab3Selected span {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat scroll 0 50% transparent;
	display:block;
	float:left;
	height:33px;
	margin:0;
	padding:0;
}

ul.tab3 font {
	color:#004B91;
	display:block;
	font-family:Tahoma; font-size: 9px;
	padding:8px 14px 0;
	white-space:nowrap;
}

ul.tab3 li.tab3NotSelected a, ul.tab3 li.tab3Selected a {
	cursor: pointer;
}

ul.tab3 a:hover {
	text-decoration: none;
}

ul.tab3 li.tab3NotSelected a:hover font {
	color:#999999;
}

ul.tab3 li.tab3Selected font {
	color:#FFFFFF;
	padding: 8px 100px 0 14px;
}

ul.tab3 li.tab3Selected a {
	background-position:0 -373px;
}

ul.tab3 li.tab3Selected span {
	background-position:right -406px;
}

ul.tab3 li.tab3NotSelected a {
	background-position:0 -440px;
}

ul.tab3 li.tab3NotSelected span {
	background-position:right -474px;
}
/* End Tab */

/* Button */
.shoppingCartUpdateButton {
	border: 0px none ; 
	margin: -1pt 0pt 0pt; 
	padding: 0pt; 
	font-weight: bold; 
	font-size: {\$SMALL_FONT}px; 
	float: left;
	color: #004992;
	background-color: #fff;
}

.shoppingCartUpdateButton:hover {
	text-decoration: underline;
}

div.buttonTopGames {
	background: url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat 0px -107px transparent;
	margin: 3px 0px;
	width: 193px;
	height:24px;
	float:left;
	position: absolute;
	top: 113px;
	left: 8px;
	z-index:2;
	cursor: pointer;
}

div.buttonTopGames font {
	font-weight: bold;
	color: #FFFFFF;
	padding: 4px 0 0 25px;
	display: block;
}

a.green_button_cart font {
	color:#FFFFFF;
	font-weight: bold;
}

.green_button_cart {
	display:block;
}

.green_button_cart, .green_button_cart a {
	width: 98px;
	height: 23px;
	margin:0;
	cursor: pointer;
	text-align: left;
} 

.green_button_cart span, .green_button_cart a {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.green_button_cart a {			float:left;	background-position: left -323px;}
.green_button_cart a:hover {		float:left;	background-position: left -347px;color: #ffffff; text-decoration: none;}
.green_button_cart font { display:block; font-size:{\$SMALL_FONT}px; font-weight:bold; padding: 4px 15px 0 30px;}

.greenBlack_button {
	padding:0px 5px;
	display:block;
}

.greenBlack_button, .greenBlack_button span, .greenBlack_button a {
	height: 29px;
	margin:0;
	cursor: pointer;
} 

.greenBlack_button span, .greenBlack_button a {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.greenBlack_button a {			float:left;	background-position: left  -438px;}
.greenBlack_button span {		float:left;	background-position: right -467px;}
.greenBlack_button a:hover {		float:left;	background-position: left -496;text-decoration: none;}
.greenBlack_button a:hover span {float:left;	background-position: right -525px;}
.greenBlack_button font { text-align:left;display:block; font-size:11px; font-weight:bold; padding: 7px 15px 0 30px;}

.greenBig_button {
	padding:0px 5px;
	display:block;
	float:left;
}

.greenBig_button, .greenBig_button span, .greenBig_button a {
	height: 57px;
	margin:0;
	cursor: pointer;
} 

.greenBig_button span, .greenBig_button a {	background:url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.greenBig_button a {			float:left;	background-position: left -814px;}
.greenBig_button span {		float:left;	background-position: right -871px;}
.greenBig_button font { text-align:left;display:block; font-size:11px; padding: 10px 10px 0; font-weight: bold;}
.greenBig_button b { font-size:16px;}


.orangeBig_button {
	padding:0px 5px;
	display:block;
	float:left;
}

.orangeBig_button, .orangeBig_button span, .orangeBig_button a {
	height: 57px;
	margin:0;
	cursor: pointer;
} 

.orangeBig_button span, .orangeBig_button a {	background:url(".DIR_WS_CSS_IMAGE."vertical_picture.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.orangeBig_button a {			float:left;	background-position: left -699px;}
.orangeBig_button span {		float:left;	background-position: right -756px;}
.orangeBig_button font { text-align:left;display:block; font-size:11px; padding: 10px 10px 0; font-weight: bold;}
.orangeBig_button b { font-size:16px;}

.green_button, .green_button_fix_width {
	display:block;
}

.green_button, .green_button span, .green_button a,.green_button_fix_width, .green_button_fix_width span, .green_button_fix_width a {
	height: 29px;
	margin:0;
	cursor: pointer;
} 

.green_button span, .green_button a,.green_button_fix_width span, .green_button_fix_width a  {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.green_button a,.green_button_fix_width a {			float:left;	background-position: left 0px;}
.green_button span,.green_button_fix_width span {		float:left;	background-position: right -29px;}
.green_button a:hover,.green_button_fix_width a:hover  {		float:left;	background-position: left -58px;text-decoration: none;}
.green_button a:hover span,.green_button_fix_width a:hover span {float:left;	background-position: right -87px;}
.green_button font,.green_button_fix_width font { text-align:center;display:block; font-size:{$SMALL_FONT}px; font-weight:bold; padding: 6px 15px 0 30px;}


.green_button_fix_width, .green_button_fix_width span, .green_button_fix_width a{
	width:110px;
}

.white_button, .white_button_fix_width {
	padding:0px;
	display:block;
}

.white_button, .white_button span, .white_button a,.white_button_fix_width, .white_button_fix_width span, .white_button_fix_width a {
	height: 16px;
	margin:0;
	cursor: pointer;
} 

.white_button span, .white_button a,.white_button_fix_width span, .white_button_fix_width a  {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.white_button a,.white_button_fix_width a {			float:left;	background-position: left -371;}
.white_button span,.white_button_fix_width span {		float:left;	background-position: right -388;}
.white_button a:hover,.white_button_fix_width a:hover  {		float:left;	background-position: left -371;text-decoration: none;}
.white_button a:hover span,.white_button_fix_width a:hover span {float:left;	background-position: right -388;}
.white_button font,.white_button_fix_width font { text-align:center;display:block; font-size:{\$SMALL_FONT}px; font-weight:bold; padding: 2px 15px 0; color: #000000}

.white_button_fix_width, .white_button_fix_width span, .white_button_fix_width a{
	width:110px;
}

.gray_small_button {
	list-style:none;
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	float: left;
	padding:0px 5px;
	display:block;
}

.gray_small_button, .gray_small_button span, .gray_small_button a {
	height: 22px;
	margin:0;
	cursor: pointer;
}

.gray_small_button span, .gray_small_button a {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;} 
.gray_small_button a {				float:left;	background-position: left -138px;}
.gray_small_button span {			float:left;	background-position: right -116px;}
.gray_small_button a:hover {		float:left;	background-position: left -182px;}
.gray_small_button a:hover span {	float:left;	background-position: right -160px;}
.gray_small_button font { text-align:left;overflow:visible; display:block; font-size:{\$SMALL_FONT}px; padding:3px 10px 0 10px;}

.gray_button, .gray_button_no_hover, .gray_button_fix_width {
	padding:0px 5px;
	display:block;
}

.gray_button, .gray_button_no_hover, .gray_button span, .gray_button_no_hover span, .gray_button a, .gray_button_no_hover a,.gray_button_fix_width, .gray_button_fix_width span, .gray_button_fix_width a {
	height: 29px;
	margin:0;
	white-space: nowrap;
	cursor: pointer;
} 

.gray_button span, .gray_button_no_hover span, .gray_button a, .gray_button_no_hover a, .gray_button_fix_width span, .gray_button_fix_width a  {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.gray_button a, .gray_button_no_hover a, .gray_button_fix_width a {			float:left;	background-position: left 0px;}
.gray_button span, .gray_button_no_hover span, .gray_button_fix_width span {		float:left;	background-position: right -29px;}
.gray_button_no_hover a:hover {text-decoration:none;}
.gray_button a:hover, .gray_button_fix_width a:hover  {		float:left;	background-position: left -58px;text-decoration: none;}
.gray_button a:hover span,.gray_button_fix_width a:hover span {float:left;	background-position: right -87px;}
.gray_button font, .gray_button_no_hover font, .gray_button_fix_width font { text-align:center;display:block; font-size:{$SMALL_FONT}px; font-weight:bold; padding: 6px 22px 0 30px;}

.gray_button_fix_width, .gray_button_no_hover, .gray_button_fix_width span, .gray_button_fix_width a{
	width:110px;
}

/* Green Submit Button */
.green_submit_button {
background: url(".DIR_WS_CSS_IMAGE."green_button.gif) no-repeat scroll 0% -24px transparent;
color:#FFFFFF;
text-decoration:none;
height: 23px;
padding: 0 0 2px 14px;
width: 99px;
border-width:0px;
font-weight:bold;
font-size:{\$SMALL_FONT}px;
cursor:pointer;
}

.green_submit_button, {
padding: 0 0 0 14px;
}

.green_submit_button:hover {
background-position:left -0px;
}

/* Gray Submit Button */
.gray_submit_button {
background: url(".DIR_WS_CSS_IMAGE."gray_button.gif) no-repeat scroll 0% -24px transparent;
color:#FFFFFF;
text-decoration:none;
height: 23px;
padding: 0 0 0 12px;
width: 99px;
border-width:0px;
font-weight:bold;
font-size:{\$SMALL_FONT}px;
cursor:pointer;
}

.gray_submit_button:hover {
background-position:left -0px;
}

.green_long_button, .red_button_fix_width {
	padding:0px 5px;
	display:block;
}

.green_long_button, .green_long_button span, .green_long_button a,.red_button_fix_width, .red_button_fix_width span, .red_button_fix_width a {
	height: 29px;
	margin:0;
	cursor: pointer;
} 

.green_long_button span, .green_long_button a,.red_button_fix_width span, .red_button_fix_width a  {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.green_long_button a,.red_button_fix_width a {			float:left;	background-position: left 0px;}
.green_long_button span,.red_button_fix_width span {		float:left;	background-position: right -29px;}
.green_long_button a:hover,.red_button_fix_width a:hover  {		float:left;	background-position: left -58px;text-decoration: none;}
.green_long_button a:hover span,.red_button_fix_width a:hover span {float:left;	background-position: right -87px;}
.green_long_button font,.red_button_fix_width font { text-align:center;display:block; font-size:{\$SMALL_FONT}px; font-weight:bold; padding: 7px 15px 0 30px;}

.red_button_fix_width, .red_button_fix_width span, .red_button_fix_width a{
	width:110px;
}

.yellow_submit_button {
background: url(".DIR_WS_CSS_IMAGE."yellow_button.gif) no-repeat scroll 0% -24px transparent;
color:#FFFFFF;
text-decoration:none;
height: 23px;
padding: 0 0 0 12px;
width: 99px;
border-width:0px;
font-weight:bold;
font-size:{\$SMALL_FONT}px;
cursor:pointer;
}

.yellow_submit_button:hover {
background-position:left -0px;
}

.yellow_button, .yellow_button_fix_width{
display:block;
float:left;
list-style-image:none;
list-style-position:outside;
list-style-type:none;
padding:0px 2px;
}

.yellow_button, .yellow_button_fix_width {
	padding:0px 5px;
	display:block;
}

.yellow_button, .yellow_button span, .yellow_button a,.yellow_button_fix_width, .yellow_button_fix_width span, .yellow_button_fix_width a {
	height: 29px;
	margin:0;
	cursor: pointer;
} 

.yellow_button span, .yellow_button a,.yellow_button_fix_width span, .yellow_button_fix_width a  {	background:url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat; text-decoration:none; color:#FFFFFF;}
.yellow_button a,.yellow_button_fix_width a {			float:left;	background-position: left 0px;}
.yellow_button span,.yellow_button_fix_width span {		float:left;	background-position: right -29px;}
.yellow_button a:hover,.yellow_button_fix_width a:hover  {		float:left;	background-position: left -58px;text-decoration: none;}
.yellow_button a:hover span,.yellow_button_fix_width a:hover span {float:left;	background-position: right -87px;}
.yellow_button font,.yellow_button_fix_width font { text-align:center;display:block; font-size:{\$SMALL_FONT}px; font-weight:bold; padding: 7px 15px 0 30px;}

.yellow_button_fix_width, .yellow_button_fix_width span, .yellow_button_fix_width a{
	width:110px;
}

.lightgray_button, .lightgray_button_fix_width {
display:block;
list-style-image:none;
list-style-position:outside;
list-style-type:none;
padding:0px 2px;
}
.lightgray_button, .lightgray_button span, .lightgray_button a, .lightgray_button_fix_width, .lightgray_button_fix_width span, .lightgray_button_fix_width a {
cursor:default;
height:24px;
margin:0pt;
}

.lightgray_button_fix_width, .lightgray_button_fix_width span, .lightgray_button_fix_width a{
width:110px;
}

.lightgray_button span, .lightgray_button a, .lightgray_button_fix_width span, .lightgray_button_fix_width a {
background: url(".DIR_WS_CSS_IMAGE."buttons.gif) no-repeat scroll 0% transparent;
color:#FFFFFF;
text-decoration:none;
}
.lightgray_button a, .lightgray_button_fix_width a {
background-position:left -236px;
float:left;
}
.lightgray_button span, .lightgray_button_fix_width span {
background-position:right -207px;
float:left;
}

.lightgray_button font, .lightgray_button_fix_width font {
display:block;
font-size:{\$SMALL_FONT}px;
font-weight:bold;
padding:4px 15px 0 30px;
white-space:nowrap;
text-align:center;
}

.red_submit_button {
	display: block;
}

.red_submit_button, .red_submit_button span, .red_submit_button a {
	height: 29px;
	margin: 0;
	cursor: pointer;
} 

.red_submit_button span, .red_submit_button a {
	background: url(". DIR_WS_CSS_IMAGE . "buttons.gif) no-repeat; 
	text-decoration: none; 
	color: #FFFFFF;
}

.red_submit_button a {
	float: left;	
	background-position: left -554px;
}

.red_submit_button span {
	float: left;
	background-position: right -583px;
}

.red_submit_button a:hover {
	float: left;
	background-position: left -612px;
	text-decoration: none;
}

.red_submit_button a:hover span {
	float: left;
	background-position: right -641px;
}

.red_submit_button font { 
	text-align: center;
	display:block;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	padding: 4px 15px 0 30px;
}

/* Button */

/*-------------------- footer END --------------------*/
img {
	border: none;
	}

h1 {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$LARGEST_FONT}px;
	font-weight: bold;
	text-transform: normal;
	margin-top: 5px;
	display: inline;
	color: #303030;
}

h2 {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$LARGE_FONT}px;
	font-weight: bold;
	text-transform: normal;
	display: inline;
}

h3 {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	text-transform: normal;
	display: inline;
}

h4 {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	text-transform: normal;
	display: inline;
}

input.blur {color:#999999;}

.filter_field_input {
	border: solid 1px #cccccc;
	font-size: 12px;
	font-family: Arial, Tahoma, Geneva, sans-serif;
	height: 18px;
	width: 170px;
	margin: 5px 0px;
	position: block;
}

/* Old Layout CSS - Phase out soon */
.giantSystemBox {
}

.systemBox {
	/*DEF_MAIN_BG*/
	background: #FFFFFF;
	/*DEF_MAIN_BG*/
}

.systemBoxHeading_spc {
	font-family: Arial, Verdana, sans-serif, Helvetica;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	letter-spacing: 0px;
  	color: #132B38; 
  	padding: 0px 0px 6px 14px;
}

.systemBoxHeading {
	font-family: Arial, Verdana, sans-serif, Helvetica;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	letter-spacing: 0px;
  	color: #132B38; 
  	padding: 0px 0px 0px 13px;
}

a.systemNav {
    font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	text-decoration: none;
}

a.systemNav:link { 
	font-family: Arial, Verdana, sans-serif;
	/*DEF_SYSTEM_NAV1*/
	color: #00619F; 
	/*DEF_SYSTEM_NAV1*/
	text-decoration: none;
  	font-size: {\$SMALL_FONT}px;
}

a.systemNav:visited { 
	font-family: Arial, Verdana, sans-serif;
	color: #00619F; 
	text-decoration: none;
  	font-size: {\$SMALL_FONT}px;
}

a.systemNav:hover {
	font-family: Arial, Verdana, sans-serif;
	/*DEF_SYSTEM_NAV2*/
	color: #BCBCBC; 
	/*DEF_SYSTEM_NAV2*/
  	text-decoration: none;
  	font-size: {\$SMALL_FONT}px;
}

.systemBoxText {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

.systemBoxLabel {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	/*DEF_SYSTEM_LABEL*/
  	color: #333333; 
  	/*DEF_SYSTEM_LABEL*/
}

.systemBoxField {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1;
}

.systemPrice {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

td.note, p.note, div.note {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALLEST_FONT}px;
  	line-height: 1.5;
}

td.systemBoxTransImg, td.storeBoxTransImg {
}

td.systemBoxLine, td.storeBoxLine {
	background-color: #CCCCCC; 
}

/* Left Panel */
.storeBox {
	background: #FFFFFF; 
}

.storeBoxHeading {
	font-family: Arial, Verdana, sans-serif, Helvetica;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	letter-spacing: 0px;
  	color: #0066FF; 
}

.storeBoxText {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
	color: #0078FF; 
}

/* Contents */
.pageHeadingTitle {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$LARGEST_FONT}px;/*15*/
  	font-weight: bold;
  	letter-spacing: 0px;
  	/*DEF_PAGE_HEADING_LABEL*/
  	color: #0066FF; 
  	/*DEF_PAGE_HEADING_LABEL*/
}

table.messageBox {
}

tr.messageRow {
}

td.messageData {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

.price{
	font-family: Arial, Verdana, sans-serif;
	/*DEF_PRICING_LABEL*/
	color: #333333; 
	/*DEF_PRICING_LABEL*/
	font-size: {\$SMALL_FONT}px;
}

.finalPrice{
	font-family: Arial, Verdana, sans-serif;
	font-weight: bold;
	color: #333333; 
	font-size: {\$SMALL_FONT}px;
}

.priceLabel{
	font-family: Arial, Verdana, sans-serif;
	/*font-weight: bold;*/
	color: #333333; 
	font-size: {\$SMALL_FONT}px;
}

.finalPriceLabel{
	font-family: Arial, Verdana, sans-serif;
	font-weight: bold;
	color: #333333; 
	font-size: {\$SMALL_FONT}px;
}

span.productSpecialPrice {
  	font-family: Arial, Verdana, sans-serif;
  	font-weight: bold;
  	color: #333333; 
  	font-size: {\$SMALL_FONT}px;
}

td.instruction, span.instruction, p.instruction, td.font12Info, span.font12Info, p.font12Info {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

td.spacingInstruction, p.spacingInstruction, div.spacingInstruction {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
  	line-height: 1.5;
}

td.actionMsg, p.actionMsg, div.actionMsg {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
  	line-height: 1.5;
}

td.spacingInfo, p.spacingInfo, div.spacingInfo, .questionLabel {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
  	line-height: 1.5;
}

.questionAnswer {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
  	color: #333333; 
}

td.info, p.info, div.info, span.info {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

.requiredInfo {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALLEST_FONT}px;
	/*DEF_WARNING_LABEL*/
	color: #FF0607; 
	/*DEF_WARNING_LABEL*/
}

h3.thanksMsg {
	color: #333333; 
}

span.greetUser {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
  	font-weight: bold;
}

/* InfoLink */
td.infoLinkContent, p.infoLinkContent, div.infoLinkContent {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color : #333333; 
}

.sectionTitle { 
	font-weight: 700;
	font-size: {\$LARGE_FONT}px;
	color: #333333; 
	border-bottom: #996600 1px dashed;
	font-family: 'Trebuchet MS', Verdana, sans-serif;
	text-align: left;
} 

.sectionTitleBold { 
	font-size: {\$SMALL_FONT}px;
	color: #333333; 
	font-family: 'Trebuchet MS', Verdana, sans-serif;
	letter-spacing: 2px;
	font-weight: bold;
}  

.sectionContent { 
	font-size: {\$SMALL_FONT}px;
	padding-bottom: 20px;
	padding-top: 10px;
	color : #333333; 
}

/* Navigation */
span.headerNavigation {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

a.headerNavigation {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;   
  	text-decoration: none; 
}

a.headerNavigation:link { 
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV1*/
  	color: #0078FF; 
  	/*DEF_STORE_NAV1*/
  	text-decoration: none;
}

a.headerNavigation:visited {
	font-family: Arial, Verdana, sans-serif;
  	color: #0078FF; 
  	text-decoration: none;
}

a.headerNavigation:hover {
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV2*/
  	color: #FF6400; 
  	/*DEF_STORE_NAV2*/
  	text-decoration: none;
}

a.categoryNavigation {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$CAT_NAV_FONT}px;
  	text-decoration: none;
}

a.subCategoryNavigation, a.productNavigation, a.systemBoxNavText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	text-decoration: none;
}

a.categoryNavigation:link, a.subCategoryNavigation:link, a.productNavigation:link, a.systemBoxNavText:link { 
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV1*/
  	color: #004992; 
  	/*DEF_STORE_NAV1*/
  	text-decoration: none;
}

a.categoryNavigation:visited, a.subCategoryNavigation:visited,a.productNavigation:visited, a.systemBoxNavText:visited {
	font-family: Arial, Verdana, sans-serif;
  	color: #004992; 
  	text-decoration: none;
}

a.categoryNavigation:hover, a.subCategoryNavigation:hover, a.productNavigation:hover, a.systemBoxNavText:hover {
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV2*/
  	color: #004992; 
  	/*DEF_STORE_NAV2*/
  	text-decoration: underline;
}

a.searchResultsHeading {
	font-family: Arial, Verdana, sans-serif;
	font-size : {\$SMALL_FONT}px;
  	font-weight: bold;
	text-decoration: none
}

a.searchResultsHeading:link {
	font-family: Arial, Verdana, sans-serif;
	font-weight: bold;
	color: #00619F; 
	text-decoration: none;
}

a.searchResultsHeading:visited {
	font-family: Arial, Verdana, sans-serif;
	font-weight: bold;
	color: #00619F; 
	text-decoration: none;
}

a.searchResultsHeading:hover {
  	font-family: Arial, Verdana, sans-serif;
  	font-weight: bold;
  	color: #BCBCBC; 
  	text-decoration: none;
}

a.promotionLink, a.annLink, a.noticeLink, a.latestNewsLink, a.pageResults {
	font-family: Arial, Verdana, sans-serif;
	font-size : {\$SMALL_FONT}px;
	text-decoration: none
}

a.promotionLink:link, a.annLink:link, a.noticeLink:link, a.latestNewsLink:link, a.pageResults:link {
	font-family: Arial, Verdana, sans-serif;
	color: #00619F; 
	text-decoration: none;
}

a.promotionLink:visited, a.annLink:visited, a.noticeLink:visited, a.latestNewsLink:visited, a.pageResults:visited {
	font-family: Arial, Verdana, sans-serif;
	color: #00619F; 
	text-decoration: none;
}

a.promotionLink:hover, a.annLink:hover, a.noticeLink:hover, a.latestNewsLink:hover, a.pageResults:hover {
  	font-family: Arial, Verdana, sans-serif;
  	color: #BCBCBC; 
  	text-decoration: none;
}

/* Button */
.buttonBox {
}

.buttonBoxContents {
}

.inputButton {
	cursor: pointer;	/* for non-IE */
	cursor: hand;
}

input.generalBtn {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight: bold;
	border-top-width: {$BTN_BORDER_TOP_WIDTH}px;
	/*DEF_BTN_BORDER_TOP_COLOUR*/
	border-top-color: #D8DFE9; 
	/*DEF_BTN_BORDER_TOP_COLOUR*/
	/*DEF_BTN_BORDER_TOP_STYLE*/
	border-top-style: outset; 
	/*DEF_BTN_BORDER_TOP_STYLE*/
	
	border-bottom-width: {$BTN_BORDER_BOTTOM_WIDTH}px;
	/*DEF_BTN_BORDER_BOTTOM_COLOUR*/
	border-bottom-color: #D8DFE9; 
	/*DEF_BTN_BORDER_BOTTOM_COLOUR*/
	/*DEF_BTN_BORDER_BOTTOM_STYLE*/
	border-bottom-style: outset; 
	/*DEF_BTN_BORDER_BOTTOM_STYLE*/
	
	border-left-width: {$BTN_BORDER_LEFT_WIDTH}px;
	/*DEF_BTN_BORDER_LEFT_COLOUR*/
	border-left-color: #D8DFE9; 
	/*DEF_BTN_BORDER_LEFT_COLOUR*/
	/*DEF_BTN_BORDER_LEFT_STYLE*/
	border-left-style: outset; 
	/*DEF_BTN_BORDER_LEFT_STYLE*/
	
	border-right-width: {$BTN_BORDER_RIGHT_WIDTH}px;
	/*DEF_BTN_BORDER_RIGHT_COLOUR*/
	border-right-color: #D8DFE9; 
	/*DEF_BTN_BORDER_RIGHT_COLOUR*/
	/*DEF_BTN_BORDER_RIGHT_STYLE*/
	border-right-style: outset; 
	/*DEF_BTN_BORDER_RIGHT_STYLE*/
	
	/*DEF_BTN_FONT_COLOR*/
	color: #FFFFFF; 
	/*DEF_BTN_FONT_COLOR*/
	/*DEF_BTN_BACKGROUND*/
	background-color: #8296B4; 
	/*DEF_BTN_BACKGROUND*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align:center;
}

input.generalBtnOver {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight:bold;
	
	border-top-width: {$BTN_BORDER_TOP_WIDTH_OVR}px;
	/*DEF_BTN_BORDER_TOP_COLOUR_OVR*/
	border-top-color: #AFBDD2; 
	/*DEF_BTN_BORDER_TOP_COLOUR_OVR*/
	/*DEF_BTN_BORDER_TOP_STYLE_OVR*/
	border-top-style: outset; 
	/*DEF_BTN_BORDER_TOP_STYLE_OVR*/
	
	border-bottom-width: {$BTN_BORDER_BOTTOM_WIDTH_OVR}px;
	/*DEF_BTN_BORDER_BOTTOM_COLOUR_OVR*/
	border-bottom-color: #AFBDD2; 
	/*DEF_BTN_BORDER_BOTTOM_COLOUR_OVR*/
	/*DEF_BTN_BORDER_BOTTOM_STYLE_OVR*/
	border-bottom-style: outset; 
	/*DEF_BTN_BORDER_BOTTOM_STYLE_OVR*/
	
	border-left-width: {$BTN_BORDER_LEFT_WIDTH_OVR}px;
	/*DEF_BTN_BORDER_LEFT_COLOUR_OVR*/
	border-left-color: #AFBDD2; 
	/*DEF_BTN_BORDER_LEFT_COLOUR_OVR*/
	/*DEF_BTN_BORDER_LEFT_STYLE_OVR*/
	border-left-style: outset; 
	/*DEF_BTN_BORDER_LEFT_STYLE_OVR*/
	
	border-right-width: {$BTN_BORDER_RIGHT_WIDTH_OVR}px;
	/*DEF_BTN_BORDER_RIGHT_COLOUR_OVR*/
	border-right-color: #AFBDD2; 
	/*DEF_BTN_BORDER_RIGHT_COLOUR_OVR*/
	/*DEF_BTN_BORDER_RIGHT_STYLE_OVR*/
	border-right-style: outset; 
	/*DEF_BTN_BORDER_RIGHT_STYLE_OVR*/
	
	/*DEF_BTN_FONT_COLOR_OVR*/
	color: #CCCCCC; 
	/*DEF_BTN_FONT_COLOR_OVR*/
	/*DEF_BTN_BACKGROUND_OVR*/
	background-color: #70819B; 
	/*DEF_BTN_BACKGROUND_OVR*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align:center;
}

input.addCartBtn {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight: bold;
	
	border-top-width: {$BTN_ADD_CART_BORDER_TOP_WIDTH}px;
	/*DEF_BTN_ADD_CART_BORDER_TOP_COLOUR*/
	border-top-color: #CDF371; 
	/*DEF_BTN_ADD_CART_BORDER_TOP_COLOUR*/
	/*DEF_BTN_ADD_CART_BORDER_TOP_STYLE*/
	border-top-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_TOP_STYLE*/
	
	border-bottom-width: {$BTN_ADD_CART_BORDER_BOTTOM_WIDTH}px;
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_COLOUR*/
	border-bottom-color: #CDF371; 
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_COLOUR*/
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_STYLE*/
	border-bottom-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_STYLE*/
	
	border-left-width: {$BTN_ADD_CART_BORDER_LEFT_WIDTH}px;
	/*DEF_BTN_ADD_CART_BORDER_LEFT_COLOUR*/
	border-left-color: #CDF371; 
	/*DEF_BTN_ADD_CART_BORDER_LEFT_COLOUR*/
	/*DEF_BTN_ADD_CART_BORDER_LEFT_STYLE*/
	border-left-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_LEFT_STYLE*/
	
	border-right-width: {$BTN_ADD_CART_BORDER_RIGHT_WIDTH}px;
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_COLOUR*/
	border-right-color: #CDF371; 
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_COLOUR*/
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_STYLE*/
	border-right-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_STYLE*/
	
	/*DEF_BTN_ADD_CART_FONT_COLOR*/
	color: #FFFFFF; 
	/*DEF_BTN_ADD_CART_FONT_COLOR*/
	/*DEF_BTN_ADD_CART_BACKGROUND*/
	background-color: #8ABA18; 
	/*DEF_BTN_ADD_CART_BACKGROUND*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align: center;
}

input.addCartBtnOver {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight:bold;
	
	border-top-width: {$BTN_ADD_CART_BORDER_TOP_WIDTH_OVR}px;
	/*DEF_BTN_ADD_CART_BORDER_TOP_COLOUR_OVR*/
	border-top-color: #C5D4A1; 
	/*DEF_BTN_ADD_CART_BORDER_TOP_COLOUR_OVR*/
	/*DEF_BTN_ADD_CART_BORDER_TOP_STYLE_OVR*/
	border-top-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_TOP_STYLE_OVR*/
	
	border-bottom-width: {$BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR}px;
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR*/
	border-bottom-color: #C5D4A1; 
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR*/
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR*/
	border-bottom-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR*/
	
	border-left-width: {$BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR}px;
	/*DEF_BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR*/
	border-left-color: #C5D4A1; 
	/*DEF_BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR*/
	/*DEF_BTN_ADD_CART_BORDER_LEFT_STYLE_OVR*/
	border-left-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_LEFT_STYLE_OVR*/
	
	border-right-width: {$BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR}px;
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR*/
	border-right-color: #C5D4A1; 
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR*/
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR*/
	border-right-style: outset; 
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR*/
	
	/*DEF_BTN_ADD_CART_FONT_COLOR_OVR*/
	color: #CCCCCC; 
	/*DEF_BTN_ADD_CART_FONT_COLOR_OVR*/
	/*DEF_BTN_ADD_CART_BACKGROUND_OVR*/
	background-color: #699500; 
	/*DEF_BTN_ADD_CART_BACKGROUND_OVR*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align: center;
}

input.preOrderBtn {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight: bold;
	border-top-width: {$BTN_PRE_ORDER_BORDER_TOP_WIDTH}px;
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_COLOUR*/
	border-top-color: #FED586; 
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_COLOUR*/
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_STYLE*/
	border-top-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_STYLE*/
	
	border-bottom-width: {$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH}px;
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR*/
	border-bottom-color: #FED586; 
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR*/
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_STYLE*/
	border-bottom-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_STYLE*/
	
	border-left-width: {$BTN_PRE_ORDER_BORDER_LEFT_WIDTH}px;
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_COLOUR*/
	border-left-color: #FED586; 
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_COLOUR*/
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_STYLE*/
	border-left-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_STYLE*/
	
	border-right-width: {$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH}px;
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_COLOUR*/
	border-right-color: #FED586; 
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_COLOUR*/
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_STYLE*/
	border-right-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_STYLE*/
	
	/*DEF_BTN_PRE_ORDER_FONT_COLOR*/
	color: #FFFFFF; 
	/*DEF_BTN_PRE_ORDER_FONT_COLOR*/
	/*DEF_BTN_PRE_ORDER_BACKGROUND*/
	background-color: #F5A100; 
	/*DEF_BTN_PRE_ORDER_BACKGROUND*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align: center;
}

input.preOrderBtnOver {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight:bold;
	border-top-width: {$BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR}px;
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR*/
	border-top-color: #D6BE83; 
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR*/
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR*/
	border-top-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR*/
	
	border-bottom-width: {$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR}px;
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR*/
	border-bottom-color: #D6BE83; 
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR*/
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR*/
	border-bottom-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR*/
	
	border-left-width: {$BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR}px;
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR*/
	border-left-color: #D6BE83; 
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR*/
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR*/
	border-left-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR*/
	
	border-right-width: {$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR}px;
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR*/
	border-right-color: #D6BE83; 
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR*/
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR*/
	border-right-style: outset; 
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR*/
	
	/*DEF_BTN_PRE_ORDER_FONT_COLOR_OVR*/
	color: #CCCCCC; 
	/*DEF_BTN_PRE_ORDER_FONT_COLOR_OVR*/
	/*DEF_BTN_PRE_ORDER_BACKGROUND_OVR*/
	background-color: #BF8700; 
	/*DEF_BTN_PRE_ORDER_BACKGROUND_OVR*/
	cursor: pointer;
	cursor: hand;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align: center;
}

input.noStockBtn {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight: bold;
	
	border-top-width: {$BTN_NO_STOCK_BORDER_TOP_WIDTH}px;
	/*DEF_BTN_NO_STOCK_BORDER_TOP_COLOUR*/
	border-top-color: #666666; 
	/*DEF_BTN_NO_STOCK_BORDER_TOP_COLOUR*/
	/*DEF_BTN_NO_STOCK_BORDER_TOP_STYLE*/
	border-top-style: solid; 
	/*DEF_BTN_NO_STOCK_BORDER_TOP_STYLE*/
	
	border-bottom-width: {$BTN_NO_STOCK_BORDER_BOTTOM_WIDTH}px;
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_COLOUR*/
	border-bottom-color: #666666; 
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_COLOUR*/
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_STYLE*/
	border-bottom-style: solid; 
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_STYLE*/
	
	border-left-width: {$BTN_NO_STOCK_BORDER_LEFT_WIDTH}px;
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_COLOUR*/
	border-left-color: #666666; 
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_COLOUR*/
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_STYLE*/
	border-left-style: solid; 
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_STYLE*/
	
	border-right-width: {$BTN_NO_STOCK_BORDER_RIGHT_WIDTH}px;
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_COLOUR*/
	border-right-color: #666666; 
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_COLOUR*/
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_STYLE*/
	border-right-style: solid; 
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_STYLE*/
	
	/*DEF_BTN_NO_STOCK_FONT_COLOR*/
	color: #999999; 
	/*DEF_BTN_NO_STOCK_FONT_COLOR*/
	/*DEF_BTN_NO_STOCK_BACKGROUND*/
	background-color: #666666; 
	/*DEF_BTN_NO_STOCK_BACKGROUND*/
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 3px;
	padding-right: 3px;
	align: center;
	text-align: center;
}

#minBtnWidth {
	/*DEF_BTN_MIN_WIDTH*/
	width: 90px;
	/*DEF_BTN_MIN_WIDTH*/
}

/* INFORMATION */
.instructionBox {
	border-right: 1px solid #CCCCCC; 
	padding-right: 10px;
	border-top: 1px solid #CCCCCC; 
	padding-left: 10px;
	font-size: {\$SMALL_FONT}px;
	padding-bottom: 5px;
	margin: 0px 0px 15px;
	border-left: 1px solid #CCCCCC; 
	color: #333333; 
	padding-top: 10px;
	border-bottom: 1px solid #CCCCCC; 
	font-family: Arial, Verdana,Helvetica,Sans-Serif;
}

td.instructionBoxHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

.instructionBoxContents {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

td.infoBoxHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

td.orderBoxHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

/* Input */
.inputBox {
	border-right: 1px solid #CCCCCC; 
	padding-right: 10px;
	border-top: 1px solid #CCCCCC; 
	padding-left: 10px;
	padding-bottom: 5px;
	margin: 0px 0px 15px;
	border-left: 1px solid #CCCCCC; 
	padding-top: 10px;
	border-bottom: 1px solid #CCCCCC; 
}

.inputBoxContents {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

.inputNestedBox {
}

.inputNestedBoxContents {
}

td.inputBoxHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

td.inputLabel, p.inputLabel, div.inputLabel, span.inputLabel {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
  	color: #333333; 
}

td.inputField, p.inputField, div.inputField, span.inputField {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
  	/*DEF_INPUT_TEXT*/
  	color: #333333; 
  	/*DEF_INPUT_TEXT*/
}

td.inputLabelBold, p.inputLabelBold, div.inputLabelBold, span.inputLabelBold {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	line-height: 1.5;
  	color: #333333; 
}

td.inputFieldBold, p.inputFieldBold, div.inputFieldBold, span.inputFieldBold {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	line-height: 1.5;
}

/* Easy Sign Up */
/* For standard browsers*/
#ezSignUp input[type='text'],#ezSignUp input[type='password'],#ezSignUp select 
{
	font-size: {\$SMALL_FONT}px;
	height: 15px;
}

/* For standard browsers, except IE */
#ezSignUp input[type='text']:focus,#ezSignUp input[type='text']:focus,#ezSignUp select:focus 
{
	border: 2px solid #09577D;
	font-size: {\$SMALL_FONT}px;
	height: 15px;
}

/* For Internet Explorer */
#ezSignUp input.focus
{
	border: 2px solid #09577D;
	font-size: {\$SMALL_FONT}px;
	height: 15px;
}

input.inputfocus
{
	border: 2px solid #09577D;
}

/* For standard browsers */
textarea
{
	font-size: {\$SMALL_FONT}px;
}

/* For standard browsers, except IE */
textarea:focus 
{
	border: 2px solid #09577D;
	font-size: {\$SMALL_FONT}px;
}

/* For Internet Explorer */
textarea.focus
{
	border: 2px solid #09577D;
	font-size: {\$SMALL_FONT}px;
}

#ezSignUp select
{ 
	font: Arial {\$SMALL_FONT}px;
	border-width: 1px; 
	border-style: solid;
	height: 21px;
} 

/* For Internet Explorer */
#ezSignUp select.focus 
{ 
	font: Arial {\$SMALL_FONT}px;
	border-width: 2px;
	border-color: #09577D;
	border-style: solid;
	height: 21px;
}

.ezInputLabel
{
	font-family: Arial;
	font-size: {\$SMALL_FONT}px;
	color: #303030;
	text-indent: 0.35cm;
}

.ezInputField
{
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	width: 120px;
  	height: 21px;
  	color: #333333; 
}

.ezInstantMessageLabel
{
	font-family: Arial;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	color: #000000;
	text-align: right;
}

.ezNewsletter
{
	font-family: Arial;
	font-size: {\$SMALL_FONT}px;
	color: #6B6B6B;
}

.ezExample
{
	font-family: Arial;
	font-size: {\$SMALL_FONT}px;
	color: #969696;
}

.ezHideMessage
{
	display: none;
}

.ezShowMessage
{
	display: inline;
	font-family: Arial;
	font-size: {\$SMALL_FONT}px;
  	color: red;
}

.ezShowPanel
{
	display: visible;
}

.ezHidePanel
{
	display: none;
}
/* END of Easy Sign Up */

.imageSubmit {
	border-width: 0px;
}

/*  Supported in IE */
input.radio, input.checkBox {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
  	/*background: #FFFFFF; */
  	border: 1px #CCCCCC; 
}

.cancelOrderBtn {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
}

CHECKBOX, RADIO {
  	font-family: Arial, Verdana, Helvetica, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	background: #FFFFFF; 
  	color: #333333; 
  	border: 1px #CCCCCC; 
}

/* Rollover Effect */
.moduleRow { }

.moduleRowOver {
	/*DEF_ROLLOVER_BG1*/
	background-color: #E9E9E9; 
	/*DEF_ROLLOVER_BG1*/
	cursor: pointer;
	cursor: hand;
}

.moduleRowSelected {
	/*DEF_ROLLOVER_BG2*/
	/*background-color: #E9E9E9;*/
	/*DEF_ROLLOVER_BG2*/
}

/* Division */
.hiddenDivision { position: relative; visibility: hidden; }

/* Footer */
div.loggingFromMsg {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	text-align: center;
	color: #FF0000; 
}

td.pageLoadingMsg {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALLEST_FONT}px;
  	text-align: center;
  	vertical-align: middle;
  	color: #333333; 
}

/* Result Page Navigation */
td.pageResultsText, span.pageResultsText, p.pageResultsText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color : #333333; 
}

/* Result Display Page */
.searchResultsBoxHeading, .shoppingCartBoxHeading, .affiliateReportBoxHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
  	/*DEF_HEADER_BG*/
  	background: #F1F0F0; 
  	/*DEF_HEADER_BG*/
}

.paymentBoxHeading, .shippingBoxHeading, .addressBoxHeading, .commentBoxHeading, .creditBoxHeading, .productBoxHeading, .affiliateBannerTitle {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

table.searchResultsBox, table.shoppingCartBox, table.affiliateReportBox {
  	border: 1px;
  	border-style: solid;
  	border-color: #CCCCCC; 
  	border-spacing: 1px;
  	border-top-width: 1px;
  	border-right-width : 0px;
  	border-bottom-width : 1px;
  	border-left-width : 0px;
  	border-collapse: collapse;
}
	
tr.searchListingEven, tr.shoppingCartListingEven, tr.productListingEven, tr.upcomingProductsEven, tr.affiliateListingEven {
	/*DEF_EVEN_BG*/
	background: #F1F0F0; 
	/*DEF_EVEN_BG*/
}

tr.searchListingOdd, tr.shoppingCartListingOdd, tr.productListingOdd, tr.upcomingProductsOdd, tr.affiliateListingOdd {
	/*DEF_ODD_BG*/
	background: #FBFBFB; 
	/*DEF_ODD_BG*/
}

tbody.shoppingCartListingEvenHide, tr.productListingEvenHide {
	display: none;
	background: #F1F0F0; 
}

tbody.shoppingCartListingEvenShow, tr.productListingEvenShow {
	display: block;
	display: table-row-group;
	background: #F1F0F0; 
}

tbody.shoppingCartListingOddHide, tr.productListingOddHide {
	display: none;
	background: #FBFBFB; 
}

tbody.shoppingCartListingOddShow, tr.productListingOddShow {
	display: block;
	display: table-row-group;
	background: #FBFBFB; 
}

td.searchResults {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALLEST_FONT}px;
  	color: #333333; 
  	border: 1px;
  	border-style: solid;
  	border-color: #CCCCCC; 	
  	border-spacing: 1px;
}

td.shoppingCartRecords {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALLEST_FONT}px;
  	color : #333333; 
  	border: 1px;
  	border-color: #CCCCCC; 
  	border-spacing: 1px;
}

td.shoppingCartTotal, td.shoppingCartSave, td.affiliateTotal, td.agreementAcceptance {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	line-height: 1.5;
  	color: #333333; 
}

td.paymentModuleTitle, td.shippingModuleTitle {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	line-height: 1.5;
}

td.paymentModuleInfo, td.shippingModuleInfo, td.creditRecords {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
}

td.paymentAddress, td.shippingAddress, td.address, td.productOrderInfo, td.paymentInfo, td.paymentPriceTitle, td.paymentPriceValue, td.affiliateReportTitle, td.affiliateReportInfo, td.commentMessage {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
  	color: #333333; 
}

td.paymentAddressTitle, td.shippingAddressTitle, td.addressTitle, td.creditTitle {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	line-height: 1.5;
  	color: #333333; 
}

/* Message Box */
.messageBox {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
}
.messageStackError, .messageStackWarning {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
	color: #FF0000; 
}

.messageStockWarning {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
	color: #FF0000; 
}

.messageStackSuccess {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
	/*DEF_SUCCESS_MSG_LABEL*/
	color: #333333; 
	/*DEF_SUCCESS_MSG_LABEL*/
}

span.markProductOutOfStock {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #FF0000; 
}

span.errorText, td.errorText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #FF0000; 
}

/* Product Listing */
td.categoryDesc {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
	line-height: 1.5;
	color: #333333; 
}

span.breadCrumb {
	font-family: Arial, Verdana, sans-serif;
  	color: #004992; 
  	font-size: {\$SMALL_FONT}px;
}

table.productListing {
  	border: 1px;
  	border-style: solid;
  	border-color: #CCCCCC; 	
  	border-spacing: 1px;
  	border-top-width: 1px;
  	border-right-width : 0px;
  	border-bottom-width : 1px;
  	border-left-width : 0px;
}

td.productSmallText, span.productSmallText, p.productSmallText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color : #333333; 
}

td.productText, span.productText, p.productText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color : #333333; 
}

.productDesc {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color : #333333; 
}

.productTitle, .faqTitle {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$LARGEST_FONT}px;
  	font-weight: bold;
  	color: #333333; 
} /* in product info page */

.subCategoryBox {
  	border: 1px;
  	border-style: solid;
  	border-color: #CCCCCC; 
  	border-spacing: 1px;
}

.subCategoryContents {
	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
}

/* News */
.newsOutline {
	/*DEF_LATEST_NEWS_BORDER*/
	border-right: 1px solid #CCCCCC; 
	/*DEF_LATEST_NEWS_BORDER*/
	padding-right: 10px;
	border-top: 1px solid #CCCCCC; 
	border-left: 1px solid #CCCCCC; 
	border-bottom: 1px solid #CCCCCC; 
	padding-left: 10px;
	font-family: Arial, Verdana, Helvetica, Sans-Serif;
	font-size: {\$SMALL_FONT}px;
	padding-bottom: 5px;
	margin: 0px 0px 15px;
	color: #333333; 
	padding-top: 10px;
	/*DEF_LATEST_NEWS_BG*/
	background-color: #FBFBFB; 
	/*DEF_LATEST_NEWS_BG*/
}

.promotionBox, .annBox, .noticeBox, .latestNewsBox {
	border-right: 1px solid #CCCCCC; 
	padding-right: 10px;
	border-top: 1px solid #CCCCCC; 
	padding-left: 10px;
	padding-bottom: 5px;
	margin: 0px 0px 15px;
	border-left: 1px solid #CCCCCC; 
	padding-top: 10px;
	border-bottom: 1px solid #CCCCCC; 
	background-color: #FBFBFB; 
}

.promotionBoxContents, .annBoxContents, .noticeBoxContents, .latestNewsBoxContents {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

td.promotionBoxHeading, td.annBoxHeading, td.noticeBoxHeading, td.latestNewsBoxHeading {
  	font-family: 'Trebuchet MS', Verdana, sans-serif;
  	font-size: {\$LARGEST_FONT}px;
  	font-style: oblique;
  	font-weight: bolder;
  	letter-spacing: 5px;
  	text-align: right;
  	/*DEF_MAIN_HEADING_NEWS_LABEL*/
  	color: #8296B4; 
  	/*DEF_MAIN_HEADING_NEWS_LABEL*/
  	margin: 10 20 5 20;		
}

div.promotionTitle, div.annTitle, div.noticeTitle, div.latestNewsTitle {
	font-family: 'Trebuchet MS', Verdana, sans-serif;
	font-weight: 700;
	font-size: {\$LARGE_FONT}px;
	/*DEF_NEWS_TITLE_LABEL*/
	color: #365261; 
	/*DEF_NEWS_TITLE_LABEL*/
	border-bottom: 1px dashed #8296B4; 
	text-align: left
}

div.promotionDate, div.annDate, div.noticeDate, div.latestNewsDate {
	font-family: 'Trebuchet MS', Verdana, sans-serif;
	font-size: {\$SMALLEST_FONT}px;
	color: #8296B4; 
	letter-spacing: 2px
}

div.row_separator {
	border-bottom: 1px dotted;
	border-color: #cccccc;
}

div.col_separator {
	border-right: 1px dashed;
	border-color: #cccccc;
}

tr.pwlCategoryRowOut, div.pwlCategoryRowOut {
	background: #E5ECEF;
	height: 25px;
	padding-top: 6px;
	cursor: pointer;
}

tr.pwlCategoryRowOver {
	background: #ECF4F6;
	height: 25px;
	padding-top: 6px;
	cursor: pointer;
}

tr.pwlCategoryRowSelected {
	background: #ECF4F6;
	padding-top: 6px;
	height: 25px;
	cursor: pointer;
}

tr.pwlProductRowOut {
	background: #FFFFFF;
	cursor: pointer;
}

tr.pwlProductRowOver {
	background: #EEEEEE;
	cursor: pointer;
}

tr.pwlProductRowSelected {
	background: #EEEEEE;
	cursor: pointer;
}

div.productRow {
	background: #FFFFFF;
}

FORM {
	display: inline;
}

TR.header {
  	background: #FFFFFF;
}

TD {
	font-family: Arial, Verdana, sans-serif;
}

TD.product_heading {
		font-family: Arial, Verdana, sans-serif;
  		font-size: {\$SMALL_FONT}px;
		font-weight: bolder;
		background: #ebebeb;
	}

TD.headerNavigation {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALLEST_FONT}px;
  	background: #FFFFFF; 
  	color: #FFFFFF;
  	padding-bottom : 2px;
  	padding-top : 2px;
}

TR.headerError {
	/*background: #FF0000;*/
}

TD.headerError {
  	font-family: Tahoma, Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #FF0000; 
  	font-weight : bold;
  	text-align : center;
}

TD.headerErrorTitle {
  	font-family: Tahoma, Arial, Verdana, sans-serif;
  	font-size: {\$LARGEST_FONT}px;
  	color: #FF0000; 
  	font-weight : bold;
  	text-align : center;
}

TR.headerInfo {
}

TD.headerInfo {
  	font-family: Tahoma, Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #FFFFFF;
  	font-weight: bold;
  	text-align: center;
}

TR.footer {
}

/* CHECKOUT PROCESS */
.checkoutBarFrom, .checkoutBarTo {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALLEST_FONT}px;
	color: #333333; 
}

.checkoutBarCurrent {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALLEST_FONT}px;
	color: #FF0000; 
}

/* STOCK */
span.newItemInCart {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #FF0000; 
}

/* AFFILIATE */
table.formArea {
	background: #FFFFFF; 
  	border-color: #CCCCCC; 
  	border-style: solid;
  	border-width: 1px;
}

table.main {
	border-bottom: 1px dashed;
	border-color: #cccccc;
}

td.formAreaTitle {
  	font-family: Tahoma, Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
}

TD.pageHeading, DIV.pageHeading {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	font-weight: bold;
  	color: #333333; 
}

td.main, p.main, div.main {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	line-height: 1.5;
}

td.smallText, span.smallText, p.smallText {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px; /* font +2 */
}

.boxText {
	font-family: Arial, Verdana, sans-serif;
	font-size: {\$SMALL_FONT}px;
	color: #333333; 
}

.errorBox {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	font-size : {\$SMALLEST_FONT}px;
	font-weight: bold;
	color: #FF0000; 
}

.infoBox {
	border: 1px solid #cccccc;
	padding: 10px 10px 5px 10px;
	font-size: {\$SMALL_FONT}px;
	margin: 0px 0px 15px;
	color: #333333; 
	font-family: Arial, Verdana,Helvetica,Sans-Serif;
	background-color: #fbfbfb; 
}

.infoBoxNavTitle {
	font-family: Arial, Verdana, sans-serif;
  	font-size: 18px;
  	color: #333333; 
  	font-weight: bold;
}

.infoBoxTitle {
	font-family: Arial, Verdana, sans-serif;
  	font-size: 15px;
  	color: #333333; 
  	font-weight: bold;
}

.infoBoxContents {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	color: #333333; 
}

td.infoBox, span.infoBox {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {\$SMALL_FONT}px;
  	border: 1px;
  	border-style: solid;
  	border-color: #CCCCCC; 
  	border-spacing: 1px;
}

/* Table Properties*/
tbody.show, div.show {
	display: table-row-group;
}

tbody.hide, div.hide {
	display: none;
}

.jproduct_vertical_dash {border-width:0px;border-right-width:1px;border-style:dashed;border-color: #000000;}
.feature{display:block; border-bottom:1px dotted #cccccc; padding: 2px 0 2px 0; font-family: Arial; font-size:{\$SMALL_FONT}px; color:#333333;}
a.feature:link {color: #00619f; text-decoration: none; }
a.feature:visited {color: #00619f; text-decoration: none; }
a.feature:hover {color: #bcbcbc; text-decoration: none;}

.news{display:block; border-bottom:1px dotted #cccccc; padding: 2px 0 2px 0; font-family: Arial; font-size:{\$SMALL_FONT}px; color:#00619f;}
a.news:link {color: #00619f; text-decoration: none; }
a.news:visited {color: #00619f; text-decoration: none; }
a.news:hover {color: #bcbcbc; text-decoration: none;}

div.flag div {
    display: block;
    float: left;
    width: 16px;
    height: 11px;
    line-height: 11px;
    font: 1px monospace;
    background-image: url(".DIR_WS_CSS_IMAGE."flags_matrix.gif);
    margin: 1px; /*2px 4px 2px 0;*/
}

#error_page div{
	font-weight:bold;
	font-family:{\$FONT_FAMILY};
	color:#333333;
	padding:20px 40px;
}

#error_page ul {
		font-weight:normal;
		font-size: {\$MEDIUM_FONT}px;
		list-style-type:square;
		padding-left:25px;
		margin-top:-30px;
}
	
.chatBoxIcon {
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) repeat-x scroll -18px -35px transparent;
	width: 16px;
	height: 16px;
}

.liveChatIcon {
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) repeat-x scroll -54px -35px transparent;
	width: 16px;
	height: 16px;
}

.ArrowRightIcon {
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) repeat-x scroll -73px -35px transparent;
	width: 16px;
	height: 16px;
}

.ArrowTopIcon {
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) repeat-x scroll -121px -48px transparent;
	height: 4px;
	width: 7px;
}

.footerSeperator {
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) no-repeat 0px -127px transparent;
	width: 1px;
	height: 33px;
	padding: 0px 1px;
}

div.footerTabMenu {
	border: solid 0px;
	height: 18px;
	float:left;
	padding:0 6px;
	list-style:none;
	color: white;
}

div.footerTabMenu span, div.footerTabMenu a {
	height: 39px;
	margin:0;
	cursor: default;
	float:left;
} 

div.footerTabMenu a {
	/*text-decoration:none;*/
	outline:0px;
	padding:0 10px 0 0;
}

div.footerTabMenu font {
	padding: 10px 17px 0 25px;
	color:white;
	display:block;
	font-size:11px;
	font-weight:bold;
}

div.footerTabMenu a.footerTabMenuSelected span, div.footerTabMenu a.footerTabMenuSelected {
	background:transparent url(".DIR_WS_CSS_IMAGE."sprite_header.gif) no-repeat;
} 

div.footerTabMenu a.footerTabMenuSelected {
	background-position: right -295px;
}

div.footerTabMenu a.footerTabMenuSelected span {
	background-position: 0 -256px;
}

div.footerTabMenu a.footerTabMenuSelected font {
	color:black;
}

div.ashGreybutton {
	float:left;
	border: solid 0px;
	color: white;
	height: 32px;
	padding:0px;
}

div.ashGreybutton span {
	float:left;
	margin:0;
	cursor: pointer;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) 0px 0px no-repeat transparent;
} 

div.ashGreybutton a {
	float:left;
	text-decoration:none;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) right -32px no-repeat transparent;
	padding:0 6px 0 0;
}

div.ashGreybutton font {
	color:white;
	display:block;
	font-size:13px;
	font-weight:bold;
	text-align:center;
	padding: 7px 10px 0px 16px;
	/* border: solid 1px red; */
}

div.ashGreybutton a:hover{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) right -96px no-repeat transparent;
}

div.ashGreybutton a:hover span{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) 0px -64px no-repeat transparent;
}

div.ashGreybutton a:hover font{
	color:white;
}

div.greenArrowbutton {
	float:left;
	border: solid 0px;
	color: white;
	height: 32px;
	padding:0px;
}

div.greenArrowbutton span {
	float:left;
	margin:0;
	cursor: pointer;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) 0px -128px no-repeat transparent;
} 

div.greenArrowbutton a {
	float:left;
	text-decoration:none;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) right -160px no-repeat transparent;
	padding:0 16px 0 0;
}

div.greenArrowbutton font {
	color:white;
	display:block;
	font-size: {\$MEDIUM_FONT}px;
	font-weight:bold;
	text-align:center;
	padding: 6px 10px 0px 19px;
	/* border: solid 1px red; */
}

div.greenArrowbutton a:hover{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) right -224px no-repeat transparent;
}

div.greenArrowbutton a:hover span{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header.gif) 0px -192px no-repeat transparent;
}

div.greenArrowbutton a:hover font{
	color:white;
}

div.red_button {
	float:left;
	border: solid 0px;
	color: white;
	height: 32px;
	padding:0px;
}

div.red_button span {
	float:left;
	margin:0;
	cursor: pointer;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header_checkout.gif) 0px -0px no-repeat transparent;
} 

div.red_button a {
	float:left;
	text-decoration:none;
	height: 32px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_header_checkout.gif) right -32px no-repeat transparent;
	padding:0 16px 0 0;
}

div.red_button font {
	color: white;
	display: block;
	font-size: {\$SMALL_FONT}px;
	font-weight: bold;
	text-align: center;
	padding: 8px 10px 0px 19px;
}

div.red_button a:hover{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header_checkout.gif) right -96px no-repeat transparent;
}

div.red_button a:hover span{
	background: url(".DIR_WS_CSS_IMAGE."sprite_header_checkout.gif) 0px -64px no-repeat transparent;
}

div.red_button a:hover font{
	color:white;
}

div.footerTab font, div.footerOnClickTab font {
	color:white;
	display:block;
	font-size:11px;
	font-weight:bold;
	text-align:center;
	padding: 10px 0 0 13px;
	/* border: solid 1px red; */
}

div.facebookConnect a {
	float:left;
	margin:0;
	cursor: pointer;
	height: 31px;
	width:174px;
	background: url(".DIR_WS_CSS_IMAGE."sprite_footer.gif) 0px 0px no-repeat transparent;
}

div.facebookConnect_footerbar a {
	float: left;
	margin: 8px;
	cursor: pointer;
	height: 21px;
	width: 85px;
	background: url(".DIR_WS_CSS_IMAGE."fbconnect.gif) 0px 0px no-repeat transparent;
}

.footerPopupTitle {
	padding:0 20px 0px 3px;
	font-size:15px;
	color:#296c97;
	font-weight:bold;
	text-align:left;
}

div.flag.AD div { background-position:-16px -44px; }
div.flag.AE div { background-position:-16px -55px; }
div.flag.AF div { background-position:-16px -66px; }
div.flag.AG div { background-position:-16px -77px; }
div.flag.AI div { background-position:-16px -99px; }
div.flag.AL div { background-position:-16px -132px; }
div.flag.AM div { background-position:-16px -143px; }
div.flag.AN div { background-position:-16px -154px; }
div.flag.AO div { background-position:-16px -165px; }
div.flag.AQ div { background-position:-16px -187px; }
div.flag.AR div { background-position:-16px -198px; }
div.flag.AS div { background-position:-16px -209px; }
div.flag.AT div { background-position:-16px -220px; }
div.flag.AU div { background-position:-16px -231px; }
div.flag.AW div { background-position:-16px -253px; }
div.flag.AX div { background-position:-16px -264px; }
div.flag.AZ div { background-position:-16px -286px; }
div.flag.BA div { background-position:-32px -11px; }
div.flag.BB div { background-position:-32px -22px; }
div.flag.BD div { background-position:-32px -44px; }
div.flag.BE div { background-position:-32px -55px; }
div.flag.BF div { background-position:-32px -66px; }
div.flag.BG div { background-position:-32px -77px; }
div.flag.BH div { background-position:-32px -88px; }
div.flag.BI div { background-position:-32px -99px; }
div.flag.BJ div { background-position:-32px -110px; }
div.flag.BM div { background-position:-32px -143px; }
div.flag.BN div { background-position:-32px -154px; }
div.flag.BO div { background-position:-32px -165px; }
div.flag.BR div { background-position:-32px -198px; }
div.flag.BS div { background-position:-32px -209px; }
div.flag.BT div { background-position:-32px -220px; }
div.flag.BV div { background-position:-32px -242px; }
div.flag.BW div { background-position:-32px -253px; }
div.flag.BY div { background-position:-32px -275px; }
div.flag.BZ div { background-position:-32px -286px; }
div.flag.CA div { background-position:-48px -11px; }
div.flag.CC div { background-position:-48px -33px; }
div.flag.CD div { background-position:-48px -44px; }
div.flag.CF div { background-position:-48px -66px; }
div.flag.CG div { background-position:-48px -77px; }
div.flag.CH div { background-position:-48px -88px; }
div.flag.CI div { background-position:-48px -99px; }
div.flag.CK div { background-position:-48px -121px; }
div.flag.CL div { background-position:-48px -132px; }
div.flag.CM div { background-position:-48px -143px; }
div.flag.CN div { background-position:-48px -154px; }
div.flag.CO div { background-position:-48px -165px; }
div.flag.CR div { background-position:-48px -198px; }
div.flag.CS div { background-position:-48px -209px; }
div.flag.CU div { background-position:-48px -231px; }
div.flag.CV div { background-position:-48px -242px; }
div.flag.CX div { background-position:-48px -264px; }
div.flag.CY div { background-position:-48px -275px; }
div.flag.CZ div { background-position:-48px -286px; }
div.flag.DE div { background-position:-64px -55px; }
div.flag.DJ div { background-position:-64px -110px; }
div.flag.DK div { background-position:-64px -121px; }
div.flag.DM div { background-position:-64px -143px; }
div.flag.DO div { background-position:-64px -165px; }
div.flag.DZ div { background-position:-64px -286px; }
div.flag.EC div { background-position:-80px -33px; }
div.flag.EE div { background-position:-80px -55px; }
div.flag.EG div { background-position:-80px -77px; }
div.flag.EH div { background-position:-80px -88px; }
div.flag.ER div { background-position:-80px -198px; }
div.flag.ES div { background-position:-80px -209px; }
div.flag.ET div { background-position:-80px -220px; }
div.flag.FI div { background-position:-96px -99px; }
div.flag.FJ div { background-position:-96px -110px; }
div.flag.FK div { background-position:-96px -121px; }
div.flag.FM div { background-position:-96px -143px; }
div.flag.FO div { background-position:-96px -165px; }
div.flag.FR div { background-position:-96px -198px; }
div.flag.FX div { background-position:-96px -264px; }
div.flag.GA div { background-position:-112px -11px; }
div.flag.GB div { background-position:-112px -22px; }
div.flag.GD div { background-position:-112px -44px; }
div.flag.GE div { background-position:-112px -55px; }
div.flag.GF div { background-position:-112px -66px; }
div.flag.GG div { background-position:-112px -77px; }
div.flag.GH div { background-position:-112px -88px; }
div.flag.GI div { background-position:-112px -99px; }
div.flag.GL div { background-position:-112px -132px; }
div.flag.GM div { background-position:-112px -143px; }
div.flag.GN div { background-position:-112px -154px; }
div.flag.GP div { background-position:-112px -176px; }
div.flag.GQ div { background-position:-112px -187px; }
div.flag.GR div { background-position:-112px -198px; }
div.flag.GS div { background-position:-112px -209px; }
div.flag.GT div { background-position:-112px -220px; }
div.flag.GU div { background-position:-112px -231px; }
div.flag.GW div { background-position:-112px -253px; }
div.flag.GY div { background-position:-112px -275px; }
div.flag.HK div { background-position:-128px -121px; }
div.flag.HM div { background-position:-128px -143px; }
div.flag.HN div { background-position:-128px -154px; }
div.flag.HR div { background-position:-128px -198px; }
div.flag.HT div { background-position:-128px -220px; }
div.flag.HU div { background-position:-128px -231px; }
div.flag.ID div { background-position:-144px -44px; }
div.flag.IE div { background-position:-144px -55px; }
div.flag.IL div { background-position:-144px -132px; }
div.flag.IN div { background-position:-144px -154px; }
div.flag.IO div { background-position:-144px -165px; }
div.flag.IQ div { background-position:-144px -187px; }
div.flag.IR div { background-position:-144px -198px; }
div.flag.IS div { background-position:-144px -209px; }
div.flag.IT div { background-position:-144px -220px; }
div.flag.JM div { background-position:-160px -143px; }
div.flag.JO div { background-position:-160px -165px; }
div.flag.JP div { background-position:-160px -176px; }
div.flag.KE div { background-position:-176px -55px; }
div.flag.KG div { background-position:-176px -77px; }
div.flag.KH div { background-position:-176px -88px; }
div.flag.KI div { background-position:-176px -99px; }
div.flag.KM div { background-position:-176px -143px; }
div.flag.KN div { background-position:-176px -154px; }
div.flag.KP div { background-position:-176px -176px; }
div.flag.KR div { background-position:-176px -198px; }
div.flag.KW div { background-position:-176px -253px; }
div.flag.KY div { background-position:-176px -275px; }
div.flag.KZ div { background-position:-176px -286px; }
div.flag.LA div { background-position:-192px -11px; }
div.flag.LB div { background-position:-192px -22px; }
div.flag.LC div { background-position:-192px -33px; }
div.flag.LI div { background-position:-192px -99px; }
div.flag.LK div { background-position:-192px -121px; }
div.flag.LR div { background-position:-192px -198px; }
div.flag.LS div { background-position:-192px -209px; }
div.flag.LT div { background-position:-192px -220px; }
div.flag.LU div { background-position:-192px -231px; }
div.flag.LV div { background-position:-192px -242px; }
div.flag.LY div { background-position:-192px -275px; }
div.flag.MA div { background-position:-208px -11px; }
div.flag.MC div { background-position:-208px -33px; }
div.flag.MD div { background-position:-208px -44px; }
div.flag.ME div { background-position:-208px -55px; }
div.flag.MG div { background-position:-208px -77px; }
div.flag.MH div { background-position:-208px -88px; }
div.flag.MK div { background-position:-208px -121px; }
div.flag.ML div { background-position:-208px -132px; }
div.flag.MM div { background-position:-208px -143px; }
div.flag.MN div { background-position:-208px -154px; }
div.flag.MO div { background-position:-208px -165px; }
div.flag.MP div { background-position:-208px -176px; }
div.flag.MQ div { background-position:-208px -187px; }
div.flag.MR div { background-position:-208px -198px; }
div.flag.MS div { background-position:-208px -209px; }
div.flag.MT div { background-position:-208px -220px; }
div.flag.MU div { background-position:-208px -231px; }
div.flag.MV div { background-position:-208px -242px; }
div.flag.MW div { background-position:-208px -253px; }
div.flag.MX div { background-position:-208px -264px; }
div.flag.MY div { background-position:-208px -275px; }
div.flag.MZ div { background-position:-208px -286px; }
div.flag.NA div { background-position:-224px -11px; }
div.flag.NC div { background-position:-224px -33px; }
div.flag.NE div { background-position:-224px -55px; }
div.flag.NF div { background-position:-224px -66px; }
div.flag.NG div { background-position:-224px -77px; }
div.flag.NI div { background-position:-224px -99px; }
div.flag.NL div { background-position:-224px -132px; }
div.flag.NO div { background-position:-224px -165px; }
div.flag.NP div { background-position:-224px -176px; }
div.flag.NR div { background-position:-224px -198px; }
div.flag.NU div { background-position:-224px -231px; }
div.flag.NZ div { background-position:-224px -286px; }
div.flag.OM div { background-position:-240px -143px; }
div.flag.PA div { background-position:-256px -11px; }
div.flag.PE div { background-position:-256px -55px; }
div.flag.PF div { background-position:-256px -66px; }
div.flag.PG div { background-position:-256px -77px; }
div.flag.PH div { background-position:-256px -88px; }
div.flag.PK div { background-position:-256px -121px; }
div.flag.PL div { background-position:-256px -132px; }
div.flag.PM div { background-position:-256px -143px; }
div.flag.PN div { background-position:-256px -154px; }
div.flag.PR div { background-position:-256px -198px; }
div.flag.PS div { background-position:-256px -209px; }
div.flag.PT div { background-position:-256px -220px; }
div.flag.PW div { background-position:-256px -253px; }
div.flag.PY div { background-position:-256px -275px; }
div.flag.QA div { background-position:-272px -11px; }
div.flag.RE div { background-position:-288px -55px; }
div.flag.RO div { background-position:-288px -165px; }
div.flag.RS div { background-position:-288px -209px; }
div.flag.RU div { background-position:-288px -231px; }
div.flag.RW div { background-position:-288px -253px; }
div.flag.SA div { background-position:-304px -11px; }
div.flag.SB div { background-position:-304px -22px; }
div.flag.SC div { background-position:-304px -33px; }
div.flag.SD div { background-position:-304px -44px; }
div.flag.SE div { background-position:-304px -55px; }
div.flag.SG div { background-position:-304px -77px; }
div.flag.SH div { background-position:-304px -88px; }
div.flag.SI div { background-position:-304px -99px; }
div.flag.SJ div { background-position:-304px -110px; }
div.flag.SK div { background-position:-304px -121px; }
div.flag.SL div { background-position:-304px -132px; }
div.flag.SM div { background-position:-304px -143px; }
div.flag.SN div { background-position:-304px -154px; }
div.flag.SO div { background-position:-304px -165px; }
div.flag.SR div { background-position:-304px -198px; }
div.flag.SS div { background-position:-304px -209px; }
div.flag.ST div { background-position:-304px -220px; }
div.flag.SV div { background-position:-304px -242px; }
div.flag.SY div { background-position:-304px -275px; }
div.flag.SZ div { background-position:-304px -286px; }
div.flag.TC div { background-position:-320px -33px; }
div.flag.TD div { background-position:-320px -44px; }
div.flag.TF div { background-position:-320px -66px; }
div.flag.TG div { background-position:-320px -77px; }
div.flag.TH div { background-position:-320px -88px; }
div.flag.TJ div { background-position:-320px -110px; }
div.flag.TK div { background-position:-320px -121px; }
div.flag.TL div { background-position:-320px -132px; }
div.flag.TM div { background-position:-320px -143px; }
div.flag.TN div { background-position:-320px -154px; }
div.flag.TO div { background-position:-320px -165px; }
div.flag.TP div { background-position:-320px -176px; }
div.flag.TR div { background-position:-320px -198px; }
div.flag.TT div { background-position:-320px -220px; }
div.flag.TV div { background-position:-320px -242px; }
div.flag.TW div { background-position:-320px -253px; }
div.flag.TZ div { background-position:-320px -286px; }
div.flag.UA div { background-position:-336px -11px; }
div.flag.UG div { background-position:-336px -77px; }
div.flag.UM div { background-position:-336px -143px; }
div.flag.US div { background-position:-336px -209px; }
div.flag.UY div { background-position:-336px -275px; }
div.flag.UZ div { background-position:-336px -286px; }
div.flag.VA div { background-position:-352px -11px; }
div.flag.VC div { background-position:-352px -33px; }
div.flag.VE div { background-position:-352px -55px; }
div.flag.VG div { background-position:-352px -77px; }
div.flag.VI div { background-position:-352px -99px; }
div.flag.VN div { background-position:-352px -154px; }
div.flag.VU div { background-position:-352px -231px; }
div.flag.WF div { background-position:-368px -66px; }
div.flag.WS div { background-position:-368px -209px; }
div.flag.YE div { background-position:-400px -55px; }
div.flag.YT div { background-position:-400px -220px; }
div.flag.YU div { background-position:-400px -231px; }
div.flag.ZA div { background-position:-416px -11px; }
div.flag.ZM div { background-position:-416px -143px; }
div.flag.ZW div { background-position:-416px -253px; }
div.flag.ZR div { background-position:-416px -198px; }

.jwindow_popup_title_blue {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #E0EDFD;
}

.jwindow_popup_content_blue {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #96BDFF;
}

a.vip_button_link {
	color: #004B91;	text-decoration: none;
}
a.vip_button_link:active {
	color: #004B91;	text-decoration: none;
}
a.vip_button_link:link{
	color: #004B91;	text-decoration: none;
}
a.vip_button_link:visited{
	color: #004B91;	text-decoration: none;
}
a.vip_button_link:hover{
	color: #004B91;	text-decoration: underline;
}

.vipTextTitle {
	font-size: 11px;
	font-weight: bold;
}

div.buybackBoxHeaderCollapse {
	background: transparent url(".DIR_WS_CSS_IMAGE."icons/arw_close.gif) no-repeat;
	width: 7px;
	height:4px;
	float:left;
}

div.buybackBoxHeaderExpand {
	background: transparent url(".DIR_WS_CSS_IMAGE."icons/arw_open.gif) no-repeat;
	width: 7px;
	height:4px;
	float:left;
}

.menuTitle {
	color: #004B91;
	font-weight: bold;
}

.jwindow_popup_title_blue {font-family: Verdana, Arial, sans-serif;font-size: 12px;font-weight: bold;color: #000; background: #E0EDFD;}
.jwindow_popup_content_blue {font-family: Verdana, Arial, sans-serif;font-size: 12px;font-weight: bold;color: #000; background: #96BDFF;}
.jwindow_popup_title_red {font-family: Verdana, Arial, sans-serif;font-size: 12px;font-weight: bold;color: #000; background: #FDF6F9;}
.jwindow_popup_content_red {font-family: Verdana, Arial, sans-serif;font-size: 12px;font-weight: bold;color: #000; background: #F4D0DA;}

.boxMenuSelected { background-color:#c3c3c3;}

.shareThis{
	background: transparent url(".DIR_WS_CSS_IMAGE."dotroundbox.gif) no-repeat scroll 0pt -21pt;
	display: block;
	float: left;
	font: bold 13px Arial; 
	line-height: 22px; 
	height: 30px; 
	padding-left: 8px; 
	text-decoration: none;
}

a:link.shareThis, a:visited.shareThis, a:hover.shareThis, a:active.shareThis{
	color: blue; 
	margin:2px;
}

.shareThis span{
	background: transparent url(".DIR_WS_CSS_IMAGE."dotroundbox.gif) no-repeat top right;
	display: block;
	padding: 2px 10px 4px 1px; /*Set 10px to be equal or greater than'padding-left' value above*/
}

a:hover.shareThis{
	text-decoration: underline;
}

.shareThisWrapper{
	overflow: hidden;
	width: 100%;
}

.redIndicator {
	color: red;
}

.greenIndicator {
	color: green;
}

/* START of Game Landing Template & Game Publisher Template */
/* Divide Content */
div.gl_bodyContentCenterTop {
	width: 100%;
	top: 0px;
  	left: 0px;;
}
div.gl_bodyContentCenterBottom {
	width: 100%;
  	left: 0px;;
}
div.gl_mainPageBlackBG {
	background: #000000;
	background-position:top; 
	background-repeat:no-repeat;
	float: left;
	width:100%;
}
div.gl_bodyContentCenterLeft {
	width: 475px;
	float:left;
	padding-left:15px;
	padding-right:10px;
}
div.gl_bodyContentCenterRight {
	float:left;
}

/* Box Content */
.gl_top_left_corner, .gl_top_right_corner, .gl_bottom_left_corner, .gl_bottom_right_corner {
	background:transparent url('".DIR_WS_CSS_IMAGE."gl_box.gif') no-repeat scroll 0px 0px;
}

.gl_bottom_left_corner_grey, .gl_bottom_right_corner_grey {
	background:transparent url('".DIR_WS_CSS_IMAGE."gl_box_grey.gif') no-repeat scroll 0px 0px;
}

.gl_top_line, .gl_left_line, .gl_right_line, .gl_bottom_line{
	background-color:transparent;
	background-image:url('".DIR_WS_CSS_IMAGE."gl_dot.gif');
	background-attachment: scroll;
}

.gl_top_left_corner {
	background-position: 0px 0px;
	width:5px; /* image width */
	height:5px; /* image hight */
	position:relative;
}
.gl_top_right_corner {
	background-position: -17px 0px;
	width:5px;
	height:5px;
	position:relative;
}
.gl_bottom_left_corner{
	background-position: 0px -17px;
	width:5px;
	height:5px;
	position:relative;
}
.gl_bottom_right_corner{
	background-position: -17px -17px;
	width:5px;
	height:5px;
	position:relative;
}
.gl_bottom_left_corner_grey{
	background-position: 0px -17px;
	width:5px;
	height:5px;
	position:relative;
}
.gl_bottom_right_corner_grey {
	background-position: -17px -17px;
	width:5px;
	height:5px;
	position:relative;
}

.gl_top_line {
	background-repeat:repeat-x;
	background-position:top left;
	background-color:white;
}
.gl_left_line {
	background-repeat:repeat-y;
	background-position:top left;
	background-color:white;
}
.gl_right_line {
	background-repeat:repeat-y;
	background-position:top right;
	background-color:white;
}
.gl_bottom_line {
	background-repeat:repeat-x;
	background-position:left bottom;
	background-color:white;
	height:5px; /* image hight */
}

.gl_currency_btn, .gl_pwl_btn, .gl_cdkey_btn, .gl_sc_btn, .gl_hla_btn, .gl_antilag, #gl_game_features_subdesc tr td div, .gl_download_btn, .gl_signup_btn, .gl_ebox_right, .stbutton_background {
	background-color:transparent;
	background-image:url('".DIR_WS_CSS_IMAGE."gl_button.gif');
	background-attachment:scroll;
	background-repeat:no-repeat;
}

/* RESET */
#gl_game_detail_box ul, #gl_game_detail_box ul li, #gl_esrb a ul, #gl_esrb a ul li {border:0; outline:none; margin:0; padding:0; list-style:none;}

/* Manage 'Shop Online For' Content Layout */
.gl_currency_btn {background-position: 5px -104px;}
.gl_currency_btn {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_currency_btn span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_currency_btn span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; font-weight:normal; color: #666666; vertical-align: middle;}
.gl_currency_btn:hover {text-decoration:none; background-position:5px -151px;}
.gl_currency_btn:hover span {color:#FFFFFF; text-decoration:none;}
.gl_currency_btn:hover span font {color: #FFFFFF;}

.gl_pwl_btn {background-position:5px -198px;}
.gl_pwl_btn {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_pwl_btn span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_pwl_btn span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma;font-weight:normal; color: #666666; vertical-align: middle;}
.gl_pwl_btn:hover {text-decoration:none; background-position:5px -245px;}
.gl_pwl_btn:hover span {color:#FFFFFF; text-decoration:none;}
.gl_pwl_btn:hover span font {color: #FFFFFF;}

.gl_cdkey_btn {background-position: 5px -10px}
.gl_cdkey_btn {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_cdkey_btn span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_cdkey_btn span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma;font-weight:normal;  color: #666666; vertical-align: middle;}
.gl_cdkey_btn:hover {text-decoration:none; background-position:5px -57px;}
.gl_cdkey_btn:hover span {color:#FFFFFF; text-decoration:none;}
.gl_cdkey_btn:hover span font {color: #FFFFFF;}

.gl_sc_btn {background-position: 5px -547px;}
.gl_sc_btn {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_sc_btn span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_sc_btn span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; font-weight:normal; color: #666666; vertical-align: middle;}
.gl_sc_btn:hover {text-decoration:none; background-position:5px -594px;}
.gl_sc_btn:hover span {color:#FFFFFF; text-decoration:none;}
.gl_sc_btn:hover span font {color: #FFFFFF;}

.gl_hla_btn {background-position: 5px -735px;}
.gl_hla_btn {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_hla_btn span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_hla_btn span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; font-weight:normal; color: #666666; vertical-align: middle;}
.gl_hla_btn:hover {text-decoration:none; background-position:5px -782px;}
.gl_hla_btn:hover span {color:#FFFFFF; text-decoration:none;}
.gl_hla_btn:hover span font {color: #FFFFFF;}

.gl_antilag {background-position: 5px -640px;}
.gl_antilag {display:block; text-decoration:none; text-align:left; height:45px; width:444px; overflow:hidden;}
.gl_antilag span {line-height:43px; padding:0px 24px 0px 70px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_antilag span font {padding:0px 0px 0px 10px; font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; font-weight:normal; color: #666666; vertical-align: middle;}
.gl_antilag:hover {text-decoration:none; background-position:5px -687px;}
.gl_antilag:hover span {color:#FFFFFF; text-decoration:none;}
.gl_antilag:hover span font {color: #FFFFFF;}

/* Manage Game Features */
#gl_game_features_desc {font-size:{\$MEDIUM_FONT}px; color: #333333; padding: 0px 6px;}
#gl_game_features_subdesc tr td {font-size:{\$MEDIUM_FONT}px; color: #333333; padding: 5px;}
#gl_game_features_subdesc tr td div {display:block; background-position:-22px -516px; height:22px; width:20px;}

/* Game Publisher Game List */
#gl_publisher_game_list tr td {padding: 6px 10px 6px 0px;}
#gl_publisher_game_list tr td a {font-size:{\$MEDIUM_FONT}px; color: #333333; text-decoration:underline; font-weight: bold;}
#gl_publisher_game_list tr td a:hover {color: #666666; text-decoration:none; font-weight: bold;}

/* Manage News/Events Extended Box */
#gl_news_events {padding: 0px 6px;}
#gl_news_events tr td span {font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; color:#999;}
#gl_news_events tr td a, #gl_news_events tr td a:visited {color: #333333; text-decoration:underline; font-weight: bold;}
#gl_news_events tr td a:hover {color: #666666; text-decoration:none; font-weight: bold;}
#gl_news_events tr td.left_box {padding: 0px 5px 5px 0px;}
#gl_news_events tr td.right_box {font-size:{\$SMALL_FONT}px; margin:0; padding:0;}
#gl_news_events tr td.one_box {font-size:{\$SMALL_FONT}px; padding-left: 3px; padding-bottom: 3px;}

.gl_extend_box_border {border-bottom: 1px solid #CCCCCC;}
.gl_extend_box_border div {height:1px;}
.gl_extend_box_bgcolor {background-color: rgb(244,244,244);}
#gl_extend_box_layout {padding-top:5px;}
#gl_extend_box_layout a {display: block; cursor:pointer;}
#gl_extend_box_layout a span.ebox_left {text-decoration:underline; line-height:23px; height:25px; text-align:right; width:88%; float: left; color:#004598; font-size:{\$SMALL_FONT}px;}
#gl_extend_box_layout a span.gl_ebox_right {background-position:0px -516px; height:23px; width:20px; float: right;}
#gl_extend_box_layout a:hover span.ebox_left {color:#666666;}

/* Manage Game Details */
#gl_game_detail_box {font-size:{\$SMALLEST_FONT}px; font-family:Tahoma; color: #333333; line-height: 20px; padding:0 5px;}
#gl_game_detail_box ul li b {font-weight:700; }

#gl_esrb {border-top:1px dotted #a0a0a0; border-bottom:1px dotted #a0a0a0; padding:10px 0px; margin: 10px 0px;}
#gl_esrb a {text-decoration:none; color: #000000; cursor:pointer;}
#gl_esrb a ul {list-style-image:none; list-style-position:outside; list-style-type:none;  margin: 0px 6px;}
#gl_esrb a ul li {background-repeat:no-repeat; min-height:50px; height:auto !important; height:50px; padding:0 6px 0 38px;}

.gl_download_btn {background-position:3px -297px; display:block; text-decoration:none; height:34px; width:200px; overflow:hidden; margin-bottom:10px;}
.gl_download_btn span {line-height:34px; padding-left:45px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_download_btn:hover {text-decoration:none; background-position:3px -334px;}
.gl_download_btn:hover span {color:#FFFFFF;}

.gl_signup_btn {background-position:3px -371px; display:block; text-decoration:none; height:34px; width:200px; overflow:hidden;}
.gl_signup_btn span {line-height:34px; padding-left:45px; font-size:{\$LARGE_FONT}px; font-weight:bold; color:#000000;}
.gl_signup_btn:hover {text-decoration:none; background-position:3px -408px;}
.gl_signup_btn:hover span {color:#FFFFFF;}

/* Game Publisher Profile */
#gl_publisher_profile_desc {border-bottom:1px dotted #a0a0a0; padding:0px 0px 10px 0px; margin: 0px 0px 10px 0px;}
#gl_publisher_profile_desc span {padding: 0px 6px; display: block;}

/* Shared Link */
#share_box {padding: 0px 0px 0px 0px;}
a.stbutton_background {display:block; background-position:0 -445px; height:33px; width:235px; padding:0px 0px 0px 0px; !important}
a.stbutton_background:hover {text-decoration:none; background-position:0 -480px;}
a.stbutton_background span {line-height:33px; padding:0px 0px 0px 40px; font-size:{$LARGE_FONT}px; font-weight:bold; color:#000000;}
a.stbutton_background:hover span {color:#FFFFFF;}

/* General Setting */
#gl_game_features_desc, #gl_game_features_subdesc, div.gl_specifications_desc {
	line-height:{\$LINE_HEIGHT};
}

table.gl_table_center {width: 475px;}
table.gl_table_right {width: 235px;}

div.gl_box_content {background-color:white; padding: 10px;}
div.gl_box_content div.gl_title_box {font-size:{\$LARGE_FONT}px; text-transform:uppercase; font-weight:bolder; letter-spacing:-0.1px; border-bottom:1px solid #ccc; padding:0 0 10px; margin-bottom: 10px;}
div.gl_breakBox {margin-bottom: 5px; clear:both;}
div.gl_specifications_desc {font-size:{\$SMALL_FONT}px; color: #333333; padding:0px 6px;}

span.gl_empty_message {color:999999;}

#gl_header_height {height: 170px;}
#gl_footer_height {height: 20px;}

.langInfoLineHeight1_5 {
	line-height:{\$LINE_HEIGHT};
}

.langInfoSizeMedium {
	font-size:{\$MEDIUM_FONT}px; 
	color: #333333; 
	padding: 0px 6px;
}
/* END of Game Landing Template & Game Publisher Template */

div.fancy_inner {
	position: relative;
	width:100%;
	height:100%;
	background: #FFF;
}

div.fancy_content {
	border:3px solid #94CEF7;
	bottom:1px;
	display:block;
	height:auto;
	left:1px;
	right:1px;
	top:1px;
	width:auto;
	
	margin:0;
	position:absolute;
	z-index:100;
}

div.fancy_close {
	position: absolute;
	top: -12px;
	right: -15px;
	height: 31px;
	width: 31px;
	background: url('".DIR_WS_CSS_IMAGE."fancybox/fancy_closebox.png') top left no-repeat;
	cursor: pointer;
	z-index: 181;
	display: none;
}

div.fancy_frame_bg {
	position: absolute;
	top: 0; left: 0;
	width: 100%;
	height: 100%;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}
	
div.fancy_bg {
	position: absolute;
	display: block;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}

div.fancy_bg_n {
	top: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_n.png') repeat-x;
}

div.fancy_bg_ne {
	top: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_ne.png') no-repeat;
}

div.fancy_bg_e {
	right: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_e.png') repeat-y;
}

div.fancy_bg_se {
	bottom: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_se.png') no-repeat;
}

div.fancy_bg_s {
	bottom: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_s.png') repeat-x;
}

div.fancy_bg_sw {
	bottom: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_sw.png') no-repeat;
}

div.fancy_bg_w {
	left: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_w.png') repeat-y;
}

div.fancy_bg_nw {
	top: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_nw.png') no-repeat;
}

/*  Clone Fancy  */
div.fancy_box_login {
	position: absolute;
    padding: 20px;
    margin: 0;
    width:450px;
	left:439;
	visibility:hidden;
	z-index:999;
}

div.fancy_inner_login {
	position: relative;
	width:100%;
	height:100%;
	background: #FFF;
}

div.fancy_content_login {
	border:3px solid #94CEF7;
	bottom:1px;
	display:block;
	height:auto;
	left:1px;
	right:1px;
	top:1px;
	width:auto;
	
	margin:0;
	position:absolute;
	z-index:100;
}

div.fancy_close_login {
	position: absolute;
	top: -12px;
	right: -15px;
	height: 31px;
	width: 31px;
	background: url('".DIR_WS_CSS_IMAGE."fancybox/fancy_closebox.png') top left no-repeat;
	cursor: pointer;
	z-index: 181;
}

div.fancy_frame_bg_login {
	position: absolute;
	top: 0; left: 0;
	width: 100%;
	height: 100%;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}
	
div.fancy_bg_login {
	position: absolute;
	display: block;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}

div.fancy_bg_n_login {
	top: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_n.png') repeat-x;
}

div.fancy_bg_ne_login {
	top: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_ne.png') no-repeat;
}

div.fancy_bg_e_login {
	right: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_e.png') repeat-y;
}

div.fancy_bg_se_login {
	bottom: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_se.png') no-repeat;
}

div.fancy_bg_s_login {
	bottom: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_s.png') repeat-x;
}

div.fancy_bg_sw_login {
	bottom: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_sw.png') no-repeat;
}

div.fancy_bg_w_login {
	left: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_w.png') repeat-y;
}

div.fancy_bg_nw_login {
	top: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('".DIR_WS_CSS_IMAGE."fancybox/fancy_shadow_nw.png') no-repeat;
}
/*  END Clone Fancy  */

.black_tpl, .black_tpr, .black_bml, .black_bmr {
	background: url('".DIR_WS_CSS_IMAGE."box_black_corner.gif') no-repeat 0px 0px transparent;
	width:10px;
	height:10px;
}
.black_tpc, .black_mdl, .black_mdc, .black_mdr, .black_bmc {background-color:#000;}
.black_tpl {background-position: 0px 0px;}
.black_tpr {background-position: -10px 0px;}
.black_bml {background-position: 0px -10px;}
.black_bmr {background-position: -10px -10px;}

/* Popup Bubble Box Structure Define */
.black_tp img { display: block;margin-left: 21px;}
.popupBubble {
    position: absolute;
    display: none;
    z-index: 850;
    border-collapse: collapse;
}

.popupBubble table.popup-contents {
    font-size: 12px;
	margin: 5px;
    background-color: #000;
    color: #FFF;
}

table.popup-contents td {
	text-align: left;
}
/* Popup Bubble Box Structure Define */

/* Tab header menu */
#tabMenu ul {
	border: solid 0px;
}

#tabMenu li {
	float:left;
	list-style:none;
	color: white;
}

#tabMenu li span, #tabMenu li a {
	height: 39px;
	margin:0;
	cursor: pointer;
	float:left;
} 

#tabMenu li a {
	text-decoration:none;
	outline:0px;
	padding:0 10px 0 0;
}

#tabMenu li font {
	padding: 10px 17px 0 25px;
	color:white;
	display:block;
	font-size:{\$MEDIUM_FONT}px;
	font-weight:bold;
}

#tabMenu li a.tabMenuSelected span, #tabMenu li a.tabMenuSelected {
	background:transparent url(".DIR_WS_CSS_IMAGE."sprite_header.gif) no-repeat;
} 

#tabMenu li a.tabMenuSelected {
	background-position: right -295px;
}

#tabMenu li a.tabMenuSelected span {
	background-position: 0 -256px;
}

#tabMenu li a.tabMenuSelected font {
	color:black;
}

#tabMenu li a:hover{
	background:transparent url(".DIR_WS_CSS_IMAGE."sprite_header.gif) no-repeat;
	background-position: right -295px;
}

#tabMenu li a:hover span{
	background:transparent url(".DIR_WS_CSS_IMAGE."sprite_header.gif) no-repeat;
	background-position: 0 -256px;
}

#tabMenu li a:hover font{
	color:black;
}

.logo {
	background:transparent url(".DIR_WS_CSS_IMAGE."logo.gif) no-repeat;
	width: 251px;
	height: 72px;
}

/* Express Checkout */
.ec_delivery_mode_bg_content {
	width: 140px;
}

.ec_delivery_mode_bg_content a {
    color: #FFFFFF;
    text-decoration: none;
    display: block;
    font-family: Tahoma;
    font-size: {\$SMALL_FONT}px;
    font-weight: normal;	
}

.ec_delivery_mode_bg_content a:hover {
    text-decoration: underline;
}
/* END of Express Checkout */

/* dhtmlxcombo */
.dhx_combo_img{
	position:absolute;
	top:8px;
	right:8px;
	width:7px;
	height:4px;
}

.dhx_combo_option_img{ 
	position:relative; 
	top:1px; 
	margin-left:2px; 
	left:0px; 
	width:18px; 
	height:18px; 
} 

.dhx_combo_box {
	position:relative;
	text-align:left;
	border:1px solid #CCCCCC;
	height:20px;
	_height:22px;
	overflow:hidden;
    background-color: white;
}

.dhx_combo_box input, .dhx_combo_box input.inputfocus { 
	border:0px; 
	padding:2px 2px 2px 2px; 
	position:absolute; 
	top:0px;
	font-family: Tahoma;
	font-size: {\$SMALL_FONT}px;
	font-weight: normal;
	color: #545454;
}

.dhx_combo_box input[disabled=''], .dhx_combo_box input.inputfocus[disabled=''] { 
	background-color: #FFFFFF;
}

.dhx_combo_list {
	position:absolute;
	z-index:230;
	overflow-y:auto;
	overflow-x:hidden;
	border:1px solid #CCCCCC;
	height:100px;
	background-color: white;
	font-family: Tahoma;
	font-size: {\$SMALL_FONT}px;
	font-weight: normal;
	color: #333333;
}

.dhx_combo_list div{
	cursor:default;
	padding: 4px 2px 3px 6px;
	text-align:left;
	font-family: Tahoma;
	font-size: {\$SMALL_FONT}px;
	font-weight: normal;
	line-height: 16px;
	border-top: 1px dotted #CECECE;
	color: #545454;
}

.dhx_selected_option{
	background-color: #E9E9E9;
	color: #FFFFFF;
}

.dhx_combo_img_rtl{
	position:absolute;
	top:0px;
	left:1px;
	width:17px;
	height:20px;
}

.dhx_combo_option_img_rtl{
	float:right;
	margin-right:0px;
	width:18px;
	height:18px;
}

.dhx_combo_list_rtl{
	direction: rtl;
	unicode-bidi: bidi-override;
	position:absolute;
	z-index:230;
	overflow-y:auto;
	overflow-x:hidden;
	border:1px solid black;
	height:100px;
	background-color: white;
	font-family: Tahoma;
	font-size: {\$SMALL_FONT}px;
	font-weight: normal;
	color: #616161;
}

.dhx_combo_list_rtl div{
	direction: rtl;
	unicode-bidi : bidi-override;
}

.dhx_combo_list_rtl div div{
	float :right !important;
	cursor:default;
	padding:2px 2px 2px 2px;	
}

.dhx_combo_list_rtl div img{
	float :right !important;
}

.dhx_combo_list_rtl div input{
	float :right !important;
}
/* END of dhtmlxcombo */
div.cdkeyImageDisplay {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "product_set.gif') no-repeat -7px -21px;
	width: 85px;
	height: 77px;
	display: block;
}

div.goldImageDisplay {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "product_set.gif') no-repeat -108px -8px;
	width: 79px;
	height: 90px;
	display: block;
}

div.pwlImageDisplay {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "product_set.gif') no-repeat -212px -10px;
	width: 76px;
	height: 82px;
	display: block;
}

.greyBgBox { background-color: #f4f4f4; }

.whiteBgBox { background-color: #FFFFFF; }

div.hlaAdvSearch {
	width:100%;
	border:solid 0px;
}

div.hlaAdvSearch ul {
	list-style: none outside none;
	width:100%;
	border:solid 0px;
	padding-left:0px;
}

div.hlaAdvSearch li {
	float:left;
	width:250px;
	padding-right:28px;
}

div.hlaAdvSearch li select {
	width:150px;
}

td.gameBox {border-bottom:1px solid #CECECE;}

td.delivery_mode_bg {
	border:1px solid #FFFFFF;
}

td.delivery_mode_bg label{
	font-weight: bold;
}

td.delivery_mode_bg:hover:not(.delivery_mode_bg_selected) {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "delivery_mode_bg.gif') repeat-x;
	border:1px solid #CCCCCC;
	cursor:pointer;
}

td.delivery_mode_bg label {
	cursor:pointer;
}

div.delivery_mode_bg {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "delivery_mode_bg.png') repeat-x;
}

div.delivery_mode_bg:hover {
	background: transparent url('" . DIR_WS_CSS_IMAGE . "delivery_mode_bg.png') repeat-x;
}

div.delivery_mode_selection_first {
	border-top:1px solid #CBCBCB;
}

div.delivery_mode_selection {
	height: 47px;
	clear:both;
	display: block;
	border-left:1px solid #CBCBCB;
	border-right:1px solid #CBCBCB;
	border-bottom:1px solid #CBCBCB;
}

div.delivery_mode_selection div.radio_button {
	float:left;
	padding-top:13px;
	width:30px;
	text-align:center;
}

div.delivery_mode_selection div.title {
	float:left;
	padding-top:13px;
}

div.delivery_mode_content {
	background-color:#FFFFFF;
	padding:5px 5px 15px 30px;
}

div.delivery_mode_bg_content {
	width:230px;
}

.wrapword{
	white-space: -moz-pre-wrap !important;  /* Mozilla, since 1999 */
	white-space: -pre-wrap;      /* Opera 4-6 */
	white-space: -o-pre-wrap;    /* Opera 7 */
	white-space: pre-wrap;       /* css-3 */
	word-wrap: break-word;       /* Internet Explorer 5.5+ */
}

/* Promotion Box */
.promotionContentBox{border: 6px solid #CCCCCC;}
.promotionContentBox .pheader{background:url('".DIR_WS_CSS_IMAGE."promo_top.gif') repeat-x scroll 0 0 transparent;border-bottom: 1px solid #CECECE;height:80px;margin-top:20px;margin-left:-10px;}
.ribbontag {background: url('".DIR_WS_CSS_IMAGE."ribbontag.png') no-repeat scroll 0 0 transparent;height:57px;width:303px;margin-left:-10px;position:relative;}
.ribbontag span{font-size:18px;font-weight:bold;display:block;padding:13px 0 0 25px;color:#FFF;}

.counterBox{background:url('".DIR_WS_CSS_IMAGE."countdown_mid.gif') repeat-x scroll 0 0 transparent;height:44px;width:220px;}
.counterBox_left, .counterBox_right{height:44px;width:5px;float:left;}
.counterBox_left{background:url('".DIR_WS_CSS_IMAGE."countdown_left.gif') no-repeat scroll 0 0 transparent;}
.counterBox_right{background:url('".DIR_WS_CSS_IMAGE."countdown_right.gif') no-repeat scroll -5px 0 transparent;}
.counterBox_middle{float:left;}
.counterBox_partition{background:url('".DIR_WS_CSS_IMAGE."countdown_line.gif') no-repeat;height:44px;width:2px;float:left;}
.counterBox_middle .md, .counterBox_middle .mh, .counterBox_middle .mm, .counterBox_middle .ms{padding:4px 3px;color:#FFF;width:45px;float:left;}
.counterBox div span{font-weight:bold;text-align:center;display:block;}
.counterBox div span.digit{font-size:20px;}
.counterBox div span.label{font-size:8px;}


/* Product Listing DTU inputbox*/
.productListingDTUPreInput {
	color: #999999;
	font-family: arial;
	font-size: 11px;
	padding-left: 3px;
	border:1px solid #cdcdcd;
}

.productListingDTUAftInput {
	color: #545454;
	font-family: arial;
	font-size: 11px;
	padding-left: 3px;
	border:1px solid #cdcdcd;
}

select.productListingDTUPreInput, select.productListingDTUAftInput { padding-top: 5px; padding-right: 5px; padding-bottom: 5px; width: 125px; height: 26px; }
input.productListingDTUPreInput, input.productListingDTUAftInput { width: 120px; height: 22px; }
";
//echo nl2br($css_template);
$action = $_REQUEST["action"];
$subaction = $_REQUEST["subaction"];

if (tep_not_null($subaction)) {
	if ($subaction == "generate_css") {
		$languages = tep_get_languages();
		
		foreach($token_array as $index=>$item) {
			if ($_REQUEST[$item]) {
				$update_val = substr($_REQUEST[$item], 0, 1) == '#' ? strtoupper($_REQUEST[$item]) : $_REQUEST[$item];
				if ($token_properties[$item]['NO_COLON'] == true)
					$css_template = str_replace("##".$item."##", $update_val, $css_template);
				else
					$css_template = str_replace("##".$item."##", $update_val.'; ', $css_template);
			} else if (substr($item, -1) > 1) {
				$default_item = substr($item, 0, -1)."1";
				if (in_array($default_item, $token_array) && $_REQUEST[$default_item] != '') {
					$update_val = substr($_REQUEST[$default_item], 0, 1) == '#' ? strtoupper($_REQUEST[$default_item]) : $_REQUEST[$default_item];
					if ($token_properties[$item]['NO_COLON'] == true)
						$css_template = str_replace("##".$item."##", $update_val, $css_template);
					else
						$css_template = str_replace("##".$item."##", $update_val.'; ', $css_template);
				}
			} else {
				if ($token_properties[$item]['NO_COLON'] == true)
					$css_template = str_replace("##".$item."##", '', $css_template);
				else
					$css_template = str_replace("##".$item."##", '; ', $css_template);
			}
		}
		
		list($php_content, $css_content) = explode(':~:END_OF_PHP:~:', tep_db_prepare_input($css_template));
		
		##########################################################   Generate PHP Code[START]   #################################################
		// Backup current copy before generate a new one
		if (file_exists(DIR_WS_CSS."stylesheet.php")) {
			if (file_exists(DIR_WS_CSS."stylesheet.bak")) {
				@unlink(DIR_WS_CSS."stylesheet.bak");
			}
			$oldPermission = @umask(0);
			@chmod(DIR_WS_CSS."stylesheet.php", 0755);
			@umask($oldPermission);
			@rename(DIR_WS_CSS."stylesheet.php", DIR_WS_CSS."stylesheet.bak");
		}
		
		if (($fp = @fopen(DIR_WS_CSS."stylesheet.php", "w")) !== false) {
			$php_content .= 'if (isset($lng_id) && (!in_array($lng_id, array(1,4)))) {' . "\n" .
						'	$SMALLEST_FONT += 1;' . "\n" .
						'	$SMALL_FONT += 1;' . "\n" .
						'	$MEDIUM_FONT += 1;' . "\n" .
						'	$LARGE_FONT += 1;' . "\n" .
						'	$LARGEST_FONT += 1;' . "\n" .
						'	$BTN_FONT += 1;' . "\n" .
						'	$LINE_HEIGHT = 1.5;' . "\n" .
						'	$FONT_FAMILY = "宋体";' . "\n" .
						'}' . "\n";
			$php_content .= '$style_string = "';
			$php_content .= $css_content . "\n";
			$php_content .= '";' . "\n";
//			$content .= 'echo $style_string;' . "\n";

			fwrite($fp, "<?\n".$php_content."\n?>") ;
			fclose($fp) ;
		}
		##########################################################   Generate PHP Code[END]   ###################################################
		
		##########################################################   Generate CSS Code[START]   #################################################
		for ($i = 0; $i < sizeof($languages); $i++) {
			$lng_id = $languages[$i]['id'];
			$language_code = $languages[$i]['code'];
			
			$css_filename = 'stylesheet_'.$language_code.'.css';
			
			if (file_exists(DIR_WS_CSS.$css_filename)) {
				@unlink(DIR_WS_CSS.$css_filename);
			}
			
			if (($fcss = @fopen(DIR_WS_CSS.$css_filename, "w")) !== false) {
				eval($php_content);
				fwrite($fcss, $style_string);
				fclose($fcss);
				
				$messageStack->add_session("The ".$css_filename." file had been updated!", 'success');
			} else {
				$messageStack->add_session("Failed to open ".$css_filename." file for write!", 'error');
			}
		}//for
		##########################################################   Generate PHP Code[END]   ###################################################
		
		tep_redirect(tep_href_link(FILENAME_CSS_GENERATOR, tep_get_all_get_params(array('action', 'subaction', 'themeID')) . 'action=load_css&themeID='.$_REQUEST["themeID"]));
	} else if ($subaction == "restore_css") {
		$css_file_contents = '';
		
		if (file_exists(DIR_WS_CSS."stylesheet.bak")) {
			if (file_exists(DIR_WS_CSS."stylesheet.php")) {
				//	@unlink(DIR_WS_CSS."stylesheet.bak");
				$oldPermission = @umask(0);
				@chmod(DIR_WS_CSS."stylesheet.bak", 0755);
				@umask($oldPermission);
				@rename(DIR_WS_CSS."stylesheet.bak", DIR_WS_CSS."stylesheet.bak2");
				
				$oldPermission = @umask(0);
				@chmod(DIR_WS_CSS."stylesheet.php", 0755);
				@umask($oldPermission);
				@rename(DIR_WS_CSS."stylesheet.php", DIR_WS_CSS."stylesheet.bak");
				
				$oldPermission = @umask(0);
				@chmod(DIR_WS_CSS."stylesheet.bak2", 0755);
				@umask($oldPermission);
				@rename(DIR_WS_CSS."stylesheet.bak2", DIR_WS_CSS."stylesheet.php");
			} else {
				$oldPermission = @umask(0);
				@chmod(DIR_WS_CSS."stylesheet.bak", 0755);
				@umask($oldPermission);
				@rename(DIR_WS_CSS."stylesheet.bak", DIR_WS_CSS."stylesheet.php");
			}
		}
		if (is_dir(DIR_WS_CSS)) {
			if (is_readable(DIR_WS_CSS)) {
				if (is_readable(DIR_WS_CSS."stylesheet.php")) {
					$css_file_contents = file_get_contents(DIR_WS_CSS."stylesheet.php");
					$css_file_contents = str_ireplace(array("<?","?>"),"",$css_file_contents);
				}
			}
		}
		
		##########################################################   Generate CSS Code[START]   #################################################
		if(tep_not_null($css_file_contents)) {
			$languages = tep_get_languages();
			
			for ($i = 0; $i < sizeof($languages); $i++) {
				$lng_id = $languages[$i]['id'];
				$language_code = $languages[$i]['code'];
				
				$css_filename = 'stylesheet_'.$language_code.'.css';
				
				if (file_exists(DIR_WS_CSS.$css_filename)) {
					@unlink(DIR_WS_CSS.$css_filename);
				}
				
				if (($fcss = @fopen(DIR_WS_CSS.$css_filename, "w")) !== false) {
					eval($css_file_contents) ;
					fwrite($fcss, $style_string) ;
					fclose($fcss) ;
					
					$messageStack->add_session("The ".$css_filename." file had been updated!", 'success');
				} else {
					$messageStack->add_session("Failed to open ".$css_filename." file for write!", 'error');
				}
			}//for
		}
		##########################################################   Generate PHP Code[END]   ###################################################
		
		tep_redirect(tep_href_link(FILENAME_CSS_GENERATOR, tep_get_all_get_params(array('action', 'subaction', 'themeID')) . 'action=load_css&themeID='.$_REQUEST["themeID"]));
	}
}

if ($action == 'load_css') {
	$default_colour_array = array();
	
	if (is_dir(DIR_WS_CSS)) {
		if (is_readable(DIR_WS_CSS)) {
			if (is_readable(DIR_WS_CSS."stylesheet.php")) {
				$css_file_contents = file_get_contents(DIR_WS_CSS."stylesheet.php");
			}
		}
	}
	
	foreach ($token_array as $ind_token) {
		$Pattern = "/\/\*DEF_".$ind_token."\*\/[^#]+#([^; ]+).*?\/\*DEF_".$ind_token."\*\//is";
		$Pattern_2 = "/\/\*DEF_".$ind_token."\*\/[^\d]+([\d|\.]+).*?\/\*DEF_".$ind_token."\*\//is";
		$Pattern_3 = "/\/\*DEF_".$ind_token."\*\/[^:]+(?::)(?:\s)*([^;]+).*?\/\*DEF_".$ind_token."\*\//is";
		
		if (preg_match($Pattern, $css_file_contents, $regs)) {
			$default_colour_array[$ind_token] = "#".$regs[1];
		} else if (	in_array($ind_token, array(	"SMALLEST_FONT", "SMALL_FONT", "MEDIUM_FONT", "LARGE_FONT", "LARGEST_FONT", "BTN_FONT", "BTN_MIN_WIDTH",
												"CAT_NAV_FONT",
												"BTN_BORDER_TOP_WIDTH", "BTN_BORDER_BOTTOM_WIDTH", "BTN_BORDER_LEFT_WIDTH", "BTN_BORDER_RIGHT_WIDTH",
												"BTN_BORDER_TOP_WIDTH_OVR", "BTN_BORDER_BOTTOM_WIDTH_OVR", "BTN_BORDER_LEFT_WIDTH_OVR", "BTN_BORDER_RIGHT_WIDTH_OVR",
												"BTN_ADD_CART_BORDER_TOP_WIDTH", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH", "BTN_ADD_CART_BORDER_LEFT_WIDTH", "BTN_ADD_CART_BORDER_RIGHT_WIDTH",
												"BTN_ADD_CART_BORDER_TOP_WIDTH_OVR", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR", "BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR", "BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR",
												"BTN_PRE_ORDER_BORDER_TOP_WIDTH", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH",
												"BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR",
												"BTN_NO_STOCK_BORDER_TOP_WIDTH", "BTN_NO_STOCK_BORDER_BOTTOM_WIDTH", "BTN_NO_STOCK_BORDER_LEFT_WIDTH", "BTN_NO_STOCK_BORDER_RIGHT_WIDTH")) && 
					preg_match($Pattern_2, $css_file_contents, $regs)) {
			$default_colour_array[$ind_token] = trim($regs[1]);
		} else if (	in_array($ind_token, array(	"BTN_BORDER_TOP_STYLE", "BTN_BORDER_BOTTOM_STYLE", "BTN_BORDER_LEFT_STYLE", "BTN_BORDER_RIGHT_STYLE",
												"BTN_BORDER_TOP_STYLE_OVR", "BTN_BORDER_BOTTOM_STYLE_OVR", "BTN_BORDER_LEFT_STYLE_OVR", "BTN_BORDER_RIGHT_STYLE_OVR",
												"BTN_ADD_CART_BORDER_TOP_STYLE", "BTN_ADD_CART_BORDER_BOTTOM_STYLE", "BTN_ADD_CART_BORDER_LEFT_STYLE", "BTN_ADD_CART_BORDER_RIGHT_STYLE",
												"BTN_ADD_CART_BORDER_TOP_STYLE_OVR", "BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR", "BTN_ADD_CART_BORDER_LEFT_STYLE_OVR", "BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR",
												"BTN_PRE_ORDER_BORDER_TOP_STYLE", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE", "BTN_PRE_ORDER_BORDER_LEFT_STYLE", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE",
												"BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR", "BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR",
												"BTN_NO_STOCK_BORDER_TOP_STYLE", "BTN_NO_STOCK_BORDER_BOTTOM_STYLE", "BTN_NO_STOCK_BORDER_LEFT_STYLE", "BTN_NO_STOCK_BORDER_RIGHT_STYLE")) && 
					preg_match($Pattern_3, $css_file_contents, $regs)) {
			$default_colour_array[$ind_token] = trim($regs[1]);
		} else {
			if (in_array($ind_token, array(	"SMALLEST_FONT", "SMALL_FONT", "MEDIUM_FONT", "LARGE_FONT", "LARGEST_FONT", "BTN_FONT", "BTN_MIN_WIDTH",
											"CAT_NAV_FONT",
											"BTN_BORDER_TOP_WIDTH", "BTN_BORDER_BOTTOM_WIDTH", "BTN_BORDER_LEFT_WIDTH", "BTN_BORDER_RIGHT_WIDTH",
											"BTN_BORDER_TOP_WIDTH_OVR", "BTN_BORDER_BOTTOM_WIDTH_OVR", "BTN_BORDER_LEFT_WIDTH_OVR", "BTN_BORDER_RIGHT_WIDTH_OVR",
											"BTN_ADD_CART_BORDER_TOP_WIDTH", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH", "BTN_ADD_CART_BORDER_LEFT_WIDTH", "BTN_ADD_CART_BORDER_RIGHT_WIDTH",
											"BTN_ADD_CART_BORDER_TOP_WIDTH_OVR", "BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR", "BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR", "BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR",
											"BTN_PRE_ORDER_BORDER_TOP_WIDTH", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH",
											"BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR",
											"BTN_NO_STOCK_BORDER_TOP_WIDTH", "BTN_NO_STOCK_BORDER_BOTTOM_WIDTH", "BTN_NO_STOCK_BORDER_LEFT_WIDTH", "BTN_NO_STOCK_BORDER_RIGHT_WIDTH"))) {
				$default_colour_array[$ind_token] = "10";
			} else if (in_array($ind_token, array(	"BTN_BORDER_TOP_STYLE", "BTN_BORDER_BOTTOM_STYLE", "BTN_BORDER_LEFT_STYLE", "BTN_BORDER_RIGHT_STYLE",
													"BTN_BORDER_TOP_STYLE_OVR", "BTN_BORDER_BOTTOM_STYLE_OVR", "BTN_BORDER_LEFT_STYLE_OVR", "BTN_BORDER_RIGHT_STYLE_OVR",
													"BTN_ADD_CART_BORDER_TOP_STYLE", "BTN_ADD_CART_BORDER_BOTTOM_STYLE", "BTN_ADD_CART_BORDER_LEFT_STYLE", "BTN_ADD_CART_BORDER_RIGHT_STYLE",
													"BTN_ADD_CART_BORDER_TOP_STYLE_OVR", "BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR", "BTN_ADD_CART_BORDER_LEFT_STYLE_OVR", "BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR",
													"BTN_PRE_ORDER_BORDER_TOP_STYLE", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE", "BTN_PRE_ORDER_BORDER_LEFT_STYLE", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE",
													"BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR", "BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR", "BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR", "BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR",
													"BTN_NO_STOCK_BORDER_TOP_STYLE", "BTN_NO_STOCK_BORDER_BOTTOM_STYLE", "BTN_NO_STOCK_BORDER_LEFT_STYLE", "BTN_NO_STOCK_BORDER_RIGHT_STYLE"))) {
				$default_colour_array[$ind_token] = "solid";
			} else {
				$default_colour_array[$ind_token] = "#000000";
			}
		}
	}
}

$theme_array = array( array('id'=>'', 'text'=>'Select Theme') );
$theme_select_sql = "SELECT themes_id, themes_title 
					 FROM " . TABLE_THEMES . " 
					 ORDER BY themes_title";
$theme_result_sql = tep_db_query($theme_select_sql);
while($theme_row = tep_db_fetch_array($theme_result_sql)) {
	$theme_array[] = array('id' => $theme_row["themes_id"], 'text' => $theme_row["themes_title"]);
}
?>
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php');?>
	<!-- header_eof //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
						<?=tep_draw_form('theme_css_form', FILENAME_CSS_GENERATOR, tep_get_all_get_params(array('action')) . 'action=load_css', 'post', '');?>
							<table border="0" align="center" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
									<td class="main" width="5%" valign="top"><?=ENTRY_THEME?></td>
									<td class="main" valign="top"><?=tep_draw_pull_down_menu("themeID", $theme_array, $curThemeID, ' id="themes_id" onChange="if(this.value) this.form.submit();"')?></td>
            					</tr>
            					<tr><td colspan="2">&nbsp;</td></tr>
            				</table>
            			</form>
<?
if ($action == 'load_css') {
	$themeID = (int)$_REQUEST["themeID"];
	
	if ($themeID > 0 && $file_exist) {
						echo tep_draw_form('generate_css', FILENAME_CSS_GENERATOR, '', 'post');
						echo tep_draw_hidden_field('subaction', 'generate_css');
						echo tep_draw_hidden_field('themeID', $themeID);
?>
							<script>
								var modalWin = null;
								function popUpColorLab(item) {
									if (window.showModalDialog) {
										var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
										var selectedColor = window.showModalDialog(url + "popups/select_color.html",null,"resizable:no;help:no;status:no;scroll:no;");
										if (selectedColor)  {
											document.getElementById(item).value = "#"+selectedColor;
											return true;
										}
									} else {
										var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
										var selectedColor = window.open(url + "popups/select_color.html", 'colour', 'width=230,height=165');
										//opener.blockEvents();
									}
									return false;
								}
							</script>
        					<table border="0" align="center" width="90%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableContent" colspan="2">Background</td>
									    	</tr>
                							<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="MAIN_BG">Main, Left and Right Column:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('MAIN_BG', $default_colour_array["MAIN_BG"], 'id="MAIN_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('MAIN_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="ODD_BG">Odd Listing:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('ODD_BG', $default_colour_array["ODD_BG"], 'id="ODD_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('ODD_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="EVEN_BG">Even Listing:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('EVEN_BG', $default_colour_array["EVEN_BG"], 'id="EVEN_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('EVEN_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="HEADER_BG">Table Header (e.g. search result, shopping cart):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('HEADER_BG', $default_colour_array["HEADER_BG"], 'id="HEADER_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('HEADER_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="LATEST_NEWS_BG">Latest News (e.g. promotions and notices):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('LATEST_NEWS_BG', $default_colour_array["LATEST_NEWS_BG"], 'id="LATEST_NEWS_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('LATEST_NEWS_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                									<?=tep_draw_input_field('LATEST_NEWS_BORDER', $default_colour_array["LATEST_NEWS_BORDER"], 'id="LATEST_NEWS_BORDER" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(frame)&nbsp;
                									<a href="javascript:;" onClick="popUpColorLab('LATEST_NEWS_BORDER');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="INPUT_BG">Input Field (e.g. text box and selection box):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('INPUT_BG', $default_colour_array["INPUT_BG"], 'id="INPUT_BG" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('INPUT_BG');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                									<?=tep_draw_input_field('INPUT_BORDER', $default_colour_array["INPUT_BORDER"], 'id="INPUT_BORDER" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(frame)&nbsp;
                									<a href="javascript:;" onClick="popUpColorLab('INPUT_BORDER');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="ROLLOVER_BG1">Rollover (e.g. selecting order history records, payment):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('ROLLOVER_BG1', $default_colour_array["ROLLOVER_BG1"], 'id="ROLLOVER_BG1" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(over)
                									<a href="javascript:;" onClick="popUpColorLab('ROLLOVER_BG1');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                									<?=tep_draw_input_field('ROLLOVER_BG2', $default_colour_array["ROLLOVER_BG2"], 'id="ROLLOVER_BG2" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(selected)
                									<a href="javascript:;" onClick="popUpColorLab('ROLLOVER_BG2');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr>
									    		<td class="pageHeading" align="right" height="30" colspan="2">&nbsp;</td>
									    	</tr>
									    	<tr class="dataTableHeadingRow">
                								<td class="dataTableContent" colspan="2">Text</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="PAGE_HEADING_LABEL">Page, System and Store Box Heading:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('PAGE_HEADING_LABEL', $default_colour_array["PAGE_HEADING_LABEL"], 'id="PAGE_HEADING_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('PAGE_HEADING_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="SYSTEM_LABEL">System Label and Information:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('SYSTEM_LABEL', $default_colour_array["SYSTEM_LABEL"], 'id="SYSTEM_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('SYSTEM_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="PRICING_LABEL">Pricing:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('PRICING_LABEL', $default_colour_array["PRICING_LABEL"], 'id="PRICING_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('PRICING_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="INPUT_TEXT">Input Field:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('INPUT_TEXT', $default_colour_array["INPUT_TEXT"], 'id="INPUT_TEXT" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('INPUT_TEXT');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="WARNING_LABEL">Warning Message (inc. required info and IP):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('WARNING_LABEL', $default_colour_array["WARNING_LABEL"], 'id="WARNING_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('WARNING_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="SUCCESS_MSG_LABEL">Success Message (add to cart):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('SUCCESS_MSG_LABEL', $default_colour_array["SUCCESS_MSG_LABEL"], 'id="SUCCESS_MSG_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('SUCCESS_MSG_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="SMALLEST_FONT">Font Size (px):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('SMALLEST_FONT', $default_colour_array["SMALLEST_FONT"], 'id="SMALLEST_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(smallest)&nbsp;
                									<?=tep_draw_input_field('SMALL_FONT', $default_colour_array["SMALL_FONT"], 'id="SMALL_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(small)&nbsp;
                									<?=tep_draw_input_field('MEDIUM_FONT', $default_colour_array["MEDIUM_FONT"], 'id="MEDIUM_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(medium)&nbsp;
                									<?=tep_draw_input_field('LARGE_FONT', $default_colour_array["LARGE_FONT"], 'id="LARGE_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(large)&nbsp;
                									<?=tep_draw_input_field('LARGEST_FONT', $default_colour_array["LARGEST_FONT"], 'id="LARGEST_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(largest)
                								</td>
									    	</tr>
									    	<tr>
									    		<td class="pageHeading" align="right" height="30" colspan="2">&nbsp;</td>
									    	</tr>
									    	<tr class="dataTableHeadingRow">
                								<td class="dataTableContent" colspan="2">Latest News Text</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="MAIN_HEADING_NEWS_LABEL">Main Heading (inc. date and dotted line):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('MAIN_HEADING_NEWS_LABEL', $default_colour_array["MAIN_HEADING_NEWS_LABEL"], 'id="MAIN_HEADING_NEWS_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('MAIN_HEADING_NEWS_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="NEWS_TITLE_LABEL">News Title:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('NEWS_TITLE_LABEL', $default_colour_array["NEWS_TITLE_LABEL"], 'id="NEWS_TITLE_LABEL" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>
                									&nbsp;<a href="javascript:;" onClick="popUpColorLab('NEWS_TITLE_LABEL');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr>
									    		<td class="pageHeading" align="right" height="30" colspan="2">&nbsp;</td>
									    	</tr>
									    	<tr class="dataTableHeadingRow">
                								<td class="dataTableContent" colspan="2">Navigation Link</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="STORE_NAV1">Product, Category and Breadcrumb:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('STORE_NAV1', $default_colour_array["STORE_NAV1"], 'id="STORE_NAV1" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(link)
                									<a href="javascript:;" onClick="popUpColorLab('STORE_NAV1');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                									<?=tep_draw_input_field('STORE_NAV2', $default_colour_array["STORE_NAV2"], 'id="STORE_NAV2" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(hover)
                									<a href="javascript:;" onClick="popUpColorLab('STORE_NAV2');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="CAT_NAV_FONT">Category Navigation Link:</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('CAT_NAV_FONT', $default_colour_array["CAT_NAV_FONT"], 'id="CAT_NAV_FONT" size="3" onfocus="select()" title="Enter the integer value for this font size"');?>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="SYSTEM_NAV1">System Link and Table Heading (inc. default):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('SYSTEM_NAV1', $default_colour_array["SYSTEM_NAV1"], 'id="SYSTEM_NAV1" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(link)
                									<a href="javascript:;" onClick="popUpColorLab('SYSTEM_NAV1');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                									<?=tep_draw_input_field('SYSTEM_NAV2', $default_colour_array["SYSTEM_NAV2"], 'id="SYSTEM_NAV2" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(hover)
                									<a href="javascript:;" onClick="popUpColorLab('SYSTEM_NAV2');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
                								</td>
									    	</tr>
									    	<tr>
									    		<td class="pageHeading" align="right" height="30" colspan="2">&nbsp;</td>
									    	</tr>
									    	<tr class="dataTableHeadingRow">
                								<td class="dataTableContent" colspan="2">Button</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="BTN_FONT">Font Size (px):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('BTN_FONT', $default_colour_array["BTN_FONT"], 'id="BTN_FONT" size="3" onfocus="select()" title="Enter the integer value for button font size" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>
                								</td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
                								<td class="dataTableContent" width="40%"><label for="BTN_MIN_WIDTH">Button Minimum Width (px):</lable></td>
                								<td class="dataTableContent" width="60%">
                									<?=tep_draw_input_field('BTN_MIN_WIDTH', $default_colour_array["BTN_MIN_WIDTH"], 'id="BTN_MIN_WIDTH" size="3" onfocus="select()" title="Enter the integer value for button\'s minimum width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>
                								</td>
									    	</tr>
									    	<tr>
									    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
									    	</tr>
									    	<tr class="dataTableRowSelected">
									    		<td class="dataTableContent" colspan="2">
									    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
									    				<tr class="dataTableRowSelected">
												    		<td width="15%" class="dataTableContent" valign="top" rowspan="3">Normal</td>
			                								<td class="dataTableContent" width="25%" valign="top"><label for="BTN_BORDER_TOP_WIDTH">Mouse Out State:</lable></td>
			                								<td width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_TOP_WIDTH', $default_colour_array["BTN_BORDER_TOP_WIDTH"], 'id="BTN_BORDER_TOP_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_TOP_COLOUR', $default_colour_array["BTN_BORDER_TOP_COLOUR"], 'id="BTN_BORDER_TOP_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_TOP_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_TOP_STYLE', $default_colour_array["BTN_BORDER_TOP_STYLE"], 'id="BTN_BORDER_TOP_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_BOTTOM_WIDTH', $default_colour_array["BTN_BORDER_BOTTOM_WIDTH"], 'id="BTN_BORDER_BOTTOM_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_BOTTOM_COLOUR', $default_colour_array["BTN_BORDER_BOTTOM_COLOUR"], 'id="BTN_BORDER_BOTTOM_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_BOTTOM_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_BOTTOM_STYLE', $default_colour_array["BTN_BORDER_BOTTOM_STYLE"], 'id="BTN_BORDER_BOTTOM_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_LEFT_WIDTH', $default_colour_array["BTN_BORDER_LEFT_WIDTH"], 'id="BTN_BORDER_LEFT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_LEFT_COLOUR', $default_colour_array["BTN_BORDER_LEFT_COLOUR"], 'id="BTN_BORDER_LEFT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_LEFT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_LEFT_STYLE', $default_colour_array["BTN_BORDER_LEFT_STYLE"], 'id="BTN_BORDER_LEFT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_RIGHT_WIDTH', $default_colour_array["BTN_BORDER_RIGHT_WIDTH"], 'id="BTN_BORDER_RIGHT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_RIGHT_COLOUR', $default_colour_array["BTN_BORDER_RIGHT_COLOUR"], 'id="BTN_BORDER_RIGHT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_RIGHT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_RIGHT_STYLE', $default_colour_array["BTN_BORDER_RIGHT_STYLE"], 'id="BTN_BORDER_RIGHT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_FONT_COLOR', $default_colour_array["BTN_FONT_COLOR"], 'id="BTN_FONT_COLOR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_FONT_COLOR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_BACKGROUND', $default_colour_array["BTN_BACKGROUND"], 'id="BTN_BACKGROUND" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BACKGROUND');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    	<tr>
												    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
												    	</tr>
												    	<tr class="dataTableRowSelected">
												    		<td class="dataTableContent" valign="top"><label for="BTN_BORDER_TOP_WIDTH_OVR">Mouse Over State:</lable></td>
			                								<td class="dataTableContent" width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_TOP_WIDTH_OVR', $default_colour_array["BTN_BORDER_TOP_WIDTH_OVR"], 'id="BTN_BORDER_TOP_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_TOP_COLOUR_OVR', $default_colour_array["BTN_BORDER_TOP_COLOUR_OVR"], 'id="BTN_BORDER_TOP_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_TOP_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_TOP_STYLE_OVR', $default_colour_array["BTN_BORDER_TOP_STYLE_OVR"], 'id="BTN_BORDER_TOP_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_BOTTOM_WIDTH_OVR', $default_colour_array["BTN_BORDER_BOTTOM_WIDTH_OVR"], 'id="BTN_BORDER_BOTTOM_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_BOTTOM_COLOUR_OVR', $default_colour_array["BTN_BORDER_BOTTOM_COLOUR_OVR"], 'id="BTN_BORDER_BOTTOM_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_BOTTOM_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_BOTTOM_STYLE_OVR', $default_colour_array["BTN_BORDER_BOTTOM_STYLE_OVR"], 'id="BTN_BORDER_BOTTOM_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_LEFT_WIDTH_OVR', $default_colour_array["BTN_BORDER_LEFT_WIDTH_OVR"], 'id="BTN_BORDER_LEFT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_LEFT_COLOUR_OVR', $default_colour_array["BTN_BORDER_LEFT_COLOUR_OVR"], 'id="BTN_BORDER_LEFT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_LEFT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_LEFT_STYLE_OVR', $default_colour_array["BTN_BORDER_LEFT_STYLE_OVR"], 'id="BTN_BORDER_LEFT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_BORDER_RIGHT_WIDTH_OVR', $default_colour_array["BTN_BORDER_RIGHT_WIDTH_OVR"], 'id="BTN_BORDER_RIGHT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_RIGHT_COLOUR_OVR', $default_colour_array["BTN_BORDER_RIGHT_COLOUR_OVR"], 'id="BTN_BORDER_RIGHT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_BORDER_RIGHT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_BORDER_RIGHT_STYLE_OVR', $default_colour_array["BTN_BORDER_RIGHT_STYLE_OVR"], 'id="BTN_BORDER_RIGHT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_FONT_COLOR_OVR', $default_colour_array["BTN_FONT_COLOR_OVR"], 'id="BTN_FONT_COLOR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
					                										<a href="javascript:;" onClick="popUpColorLab('BTN_FONT_COLOR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_BACKGROUND_OVR', $default_colour_array["BTN_BACKGROUND_OVR"], 'id="BTN_BACKGROUND_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
					                										<a href="javascript:;" onClick="popUpColorLab('BTN_BACKGROUND_OVR');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    </table>
												</td>
											</tr>
											<tr>
									    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
									    	</tr>
											<tr class="dataTableRowSelected">
									    		<td class="dataTableContent" colspan="2">
									    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
												    	<tr class="dataTableRowSelected">
												    		<td width="15%" class="dataTableContent" valign="top" rowspan="3">Add to Cart</td>
												    		<td class="dataTableContent" width="25%" valign="top"><label for="BTN_ADD_CART_BORDER_TOP_WIDTH">Mouse Out State:</lable></td>
			                								<td width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_WIDTH', $default_colour_array["BTN_ADD_CART_BORDER_TOP_WIDTH"], 'id="BTN_ADD_CART_BORDER_TOP_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_COLOUR', $default_colour_array["BTN_ADD_CART_BORDER_TOP_COLOUR"], 'id="BTN_ADD_CART_BORDER_TOP_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_TOP_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_STYLE', $default_colour_array["BTN_ADD_CART_BORDER_TOP_STYLE"], 'id="BTN_ADD_CART_BORDER_TOP_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_WIDTH', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_WIDTH"], 'id="BTN_ADD_CART_BORDER_BOTTOM_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_COLOUR', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_COLOUR"], 'id="BTN_ADD_CART_BORDER_BOTTOM_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_BOTTOM_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_STYLE', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_STYLE"], 'id="BTN_ADD_CART_BORDER_BOTTOM_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_WIDTH', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_WIDTH"], 'id="BTN_ADD_CART_BORDER_LEFT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_COLOUR', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_COLOUR"], 'id="BTN_ADD_CART_BORDER_LEFT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_LEFT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_STYLE', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_STYLE"], 'id="BTN_ADD_CART_BORDER_LEFT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_WIDTH', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_WIDTH"], 'id="BTN_ADD_CART_BORDER_RIGHT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_COLOUR', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_COLOUR"], 'id="BTN_ADD_CART_BORDER_RIGHT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_RIGHT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_STYLE', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_STYLE"], 'id="BTN_ADD_CART_BORDER_RIGHT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_FONT_COLOR', $default_colour_array["BTN_ADD_CART_FONT_COLOR"], 'id="BTN_ADD_CART_FONT_COLOR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_FONT_COLOR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BACKGROUND', $default_colour_array["BTN_ADD_CART_BACKGROUND"], 'id="BTN_ADD_CART_BACKGROUND" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BACKGROUND');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    	<tr>
												    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
												    	</tr>
												    	<tr class="dataTableRowSelected">
												    		<td class="dataTableContent" valign="top"><label for="BTN_ADD_CART_BORDER_TOP_WIDTH_OVR">Mouse Over State:</lable></td>
			                								<td class="dataTableContent" width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_WIDTH_OVR', $default_colour_array["BTN_ADD_CART_BORDER_TOP_WIDTH_OVR"], 'id="BTN_ADD_CART_BORDER_TOP_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_COLOUR_OVR', $default_colour_array["BTN_ADD_CART_BORDER_TOP_COLOUR_OVR"], 'id="BTN_ADD_CART_BORDER_TOP_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_TOP_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_TOP_STYLE_OVR', $default_colour_array["BTN_ADD_CART_BORDER_TOP_STYLE_OVR"], 'id="BTN_ADD_CART_BORDER_TOP_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR"], 'id="BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR"], 'id="BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_BOTTOM_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR', $default_colour_array["BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR"], 'id="BTN_ADD_CART_BORDER_BOTTOM_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR"], 'id="BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR"], 'id="BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_LEFT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_LEFT_STYLE_OVR', $default_colour_array["BTN_ADD_CART_BORDER_LEFT_STYLE_OVR"], 'id="BTN_ADD_CART_BORDER_LEFT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR"], 'id="BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR"], 'id="BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BORDER_RIGHT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR', $default_colour_array["BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR"], 'id="BTN_ADD_CART_BORDER_RIGHT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_ADD_CART_FONT_COLOR_OVR', $default_colour_array["BTN_ADD_CART_FONT_COLOR_OVR"], 'id="BTN_ADD_CART_FONT_COLOR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_FONT_COLOR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_ADD_CART_BACKGROUND_OVR', $default_colour_array["BTN_ADD_CART_BACKGROUND_OVR"], 'id="BTN_ADD_CART_BACKGROUND_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_ADD_CART_BACKGROUND_OVR');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    </table>
												</td>
											</tr>
											<tr>
									    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
									    	</tr>
											<tr class="dataTableRowSelected">
									    		<td class="dataTableContent" colspan="2">
									    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
												    	<tr class="dataTableRowSelected">
												    		<td width="15%" class="dataTableContent" valign="top" rowspan="3">Pre Order</td>
			                								<td class="dataTableContent" width="25%" valign="top"><label for="BTN_PRE_ORDER_BORDER_TOP_WIDTH">Mouse Out State:</lable></td>
			                								<td width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_WIDTH', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_WIDTH"], 'id="BTN_PRE_ORDER_BORDER_TOP_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_COLOUR', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_COLOUR"], 'id="BTN_PRE_ORDER_BORDER_TOP_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_TOP_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_STYLE', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_STYLE"], 'id="BTN_PRE_ORDER_BORDER_TOP_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_STYLE', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_STYLE"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_WIDTH', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_WIDTH"], 'id="BTN_PRE_ORDER_BORDER_LEFT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_COLOUR', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_COLOUR"], 'id="BTN_PRE_ORDER_BORDER_LEFT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_LEFT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_STYLE', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_STYLE"], 'id="BTN_PRE_ORDER_BORDER_LEFT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_WIDTH', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_WIDTH"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_COLOUR', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_COLOUR"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_RIGHT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_STYLE', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_STYLE"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_FONT_COLOR', $default_colour_array["BTN_PRE_ORDER_FONT_COLOR"], 'id="BTN_PRE_ORDER_FONT_COLOR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_FONT_COLOR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BACKGROUND', $default_colour_array["BTN_PRE_ORDER_BACKGROUND"], 'id="BTN_PRE_ORDER_BACKGROUND" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BACKGROUND');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    	<tr>
												    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
												    	</tr>
												    	<tr class="dataTableRowSelected">
												    		<td class="dataTableContent" width="25%" valign="top"><label for="BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR">Mouse Over State:</lable></td>
			                								<td width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR"], 'id="BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR"], 'id="BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_TOP_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR"], 'id="BTN_PRE_ORDER_BORDER_TOP_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_BOTTOM_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR"], 'id="BTN_PRE_ORDER_BORDER_BOTTOM_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR"], 'id="BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR"], 'id="BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_LEFT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR"], 'id="BTN_PRE_ORDER_BORDER_LEFT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BORDER_RIGHT_COLOUR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR', $default_colour_array["BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR"], 'id="BTN_PRE_ORDER_BORDER_RIGHT_STYLE_OVR" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_PRE_ORDER_FONT_COLOR_OVR', $default_colour_array["BTN_PRE_ORDER_FONT_COLOR_OVR"], 'id="BTN_PRE_ORDER_FONT_COLOR_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_FONT_COLOR_OVR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_PRE_ORDER_BACKGROUND_OVR', $default_colour_array["BTN_PRE_ORDER_BACKGROUND_OVR"], 'id="BTN_PRE_ORDER_BACKGROUND_OVR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_PRE_ORDER_BACKGROUND_OVR');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    </table>
												</td>
											</tr>
											<tr>
									    		<td class="dataTableRowSelected" align="right" height="10" colspan="2"></td>
									    	</tr>
											<tr class="dataTableRowSelected">
									    		<td class="dataTableContent" colspan="2">
									    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
									    				<tr class="dataTableRowSelected">
												    		<td width="15%" class="dataTableContent" valign="top" rowspan="2">Out of Stock</td>
			                								<td class="dataTableContent" width="25%" valign="top"><label for="BTN_NO_STOCK_BORDER_TOP_WIDTH">Mouse Out State:</lable></td>
			                								<td width="60%">
			                									<table border="0" cellspacing="0" cellpadding="1">
			                										<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_TOP_WIDTH', $default_colour_array["BTN_NO_STOCK_BORDER_TOP_WIDTH"], 'id="BTN_NO_STOCK_BORDER_TOP_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border top width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_TOP_COLOUR', $default_colour_array["BTN_NO_STOCK_BORDER_TOP_COLOUR"], 'id="BTN_NO_STOCK_BORDER_TOP_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border top colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_BORDER_TOP_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_TOP_STYLE', $default_colour_array["BTN_NO_STOCK_BORDER_TOP_STYLE"], 'id="BTN_NO_STOCK_BORDER_TOP_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border top style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_BOTTOM_WIDTH', $default_colour_array["BTN_NO_STOCK_BORDER_BOTTOM_WIDTH"], 'id="BTN_NO_STOCK_BORDER_BOTTOM_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border bottom width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_BOTTOM_COLOUR', $default_colour_array["BTN_NO_STOCK_BORDER_BOTTOM_COLOUR"], 'id="BTN_NO_STOCK_BORDER_BOTTOM_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border bottom colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_BORDER_BOTTOM_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_BOTTOM_STYLE', $default_colour_array["BTN_NO_STOCK_BORDER_BOTTOM_STYLE"], 'id="BTN_NO_STOCK_BORDER_BOTTOM_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border bottom style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_LEFT_WIDTH', $default_colour_array["BTN_NO_STOCK_BORDER_LEFT_WIDTH"], 'id="BTN_NO_STOCK_BORDER_LEFT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border left width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_LEFT_COLOUR', $default_colour_array["BTN_NO_STOCK_BORDER_LEFT_COLOUR"], 'id="BTN_NO_STOCK_BORDER_LEFT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border left colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_BORDER_LEFT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_LEFT_STYLE', $default_colour_array["BTN_NO_STOCK_BORDER_LEFT_STYLE"], 'id="BTN_NO_STOCK_BORDER_LEFT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border left style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
			                												<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_RIGHT_WIDTH', $default_colour_array["BTN_NO_STOCK_BORDER_RIGHT_WIDTH"], 'id="BTN_NO_STOCK_BORDER_RIGHT_WIDTH" size="3" onfocus="select()" title="Enter the integer value for border width" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" ');?>&nbsp;(border right width)
			                											</td>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_RIGHT_COLOUR', $default_colour_array["BTN_NO_STOCK_BORDER_RIGHT_COLOUR"], 'id="BTN_NO_STOCK_BORDER_RIGHT_COLOUR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(border right colour)
						                									<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_BORDER_RIGHT_COLOUR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BORDER_RIGHT_STYLE', $default_colour_array["BTN_NO_STOCK_BORDER_RIGHT_STYLE"], 'id="BTN_NO_STOCK_BORDER_RIGHT_STYLE" size="8" onfocus="select()" title="Enter the border style (e.g. solid, double, none, etc.)" ');?>&nbsp;(border right style)
						                								</td>
						                							</tr>
						                							<tr>
			                											<td class="dataTableContent">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_FONT_COLOR', $default_colour_array["BTN_NO_STOCK_FONT_COLOR"], 'id="BTN_NO_STOCK_FONT_COLOR" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(font colour)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_FONT_COLOR');"><img src="<?=IMAGE_FONT_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                								<td class="dataTableContent" colspan="2">
						                									<?=tep_draw_input_field('BTN_NO_STOCK_BACKGROUND', $default_colour_array["BTN_NO_STOCK_BACKGROUND"], 'id="BTN_NO_STOCK_BACKGROUND" size="9" onfocus="select()" title="Enter colour value or click colour button to choose a colour"');?>&nbsp;(background)
			                												<a href="javascript:;" onClick="popUpColorLab('BTN_NO_STOCK_BACKGROUND');"><img src="<?=IMAGE_BG_COLOUR_SELECTION?>" border=0 unselectable="on"></a>
						                								</td>
						                							</tr>
						                						</table>
			                								</td>
												    	</tr>
												    </table>
												</td>
											</tr>
									    	<tr>
									    		<td class="pageHeading" align="right" height="60" valign="bottom" colspan="2">
												<? if (file_exists(DIR_WS_CSS."stylesheet.bak")) { ?>
									    				<a href="javascript:void(confirm_action('Are you sure to restore previous css settings?', '<?=tep_href_link(FILENAME_CSS_GENERATOR, 'subaction=restore_css&themeID='.$themeID)?>'))"><?=tep_image_button('button_restore.gif', IMAGE_RESTORE)?></a>
									    		<?	} ?>
									    			<?=tep_image_submit('button_update.gif', IMAGE_UPDATE, 'onClick="return confirm_action(\'Are you sure to overwrite the existing css file with this new settings?\');"')?>
									    		</td>
									    	</tr>
                						</table>
                					</td>
                				</tr>
    						</table>
<?	} else { ?>
							<table border="0" align="center" width="90%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top"><?=ERROR_THEME_DIRECTORY_DOES_NOT_EXIST?></td>
            					</tr>
            				</table>
<?	}
} else if ($action == "generate_css") {
	echo tep_draw_form('wrtie_css', FILENAME_CSS_GENERATOR, '', 'post');
	echo tep_draw_hidden_field('subaction', 'write_to_file');
?>
							<!--script language="Javascript1.2"><!-- // load htmlarea
								// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 Products Description HTML - Head
								        _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
								          var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
								           if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
								            if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
								             if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
								         <?php if (HTML_AREA_WYSIWYG_BASIC_PD == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
								         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
								         document.write(' language="Javascript1.2"></scr' + 'ipt>');
								            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
								         <?php } else{ ?> if (win_ie_ver >= 5.5) {
								         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
								         document.write(' language="Javascript1.2"></scr' + 'ipt>');
								            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
								         <?php }?>
							// --></script-->
							<table border="0" align="center" width="70%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top" class="main">
										<a href="javascript:selectAll('result')" class="splitPageLink">Select All</a>
									</td>
								</tr>
								<tr>
									<td>
										<?=tep_draw_textarea_field("result", "soft", 120, 50, $css_template, ' id=result ')?>
										<script language="JavaScript1.2" defer>
											function selectAll(theField) {
												var textContainer = document.getElementById(theField);
												textContainer.focus();
												textContainer.select();
											}
											/*
			             					var config = new Object();  // create new config object
			             					config.width = "505px";
			             					config.height = "140px";
			             					config.bodyStyle = 'background-color: White; font-family: "Times New Roman"; color: Black; font-size: 12pt;';
			             					config.debug = 0;
			             				//	config.imgURL = "<?php echo HTTP_SERVER . DIR_WS_ADMIN . '/'; ?>";  // URL to htmlarea files
			                    			editor_generate('result',config);
			                    			*/
			           					</script>
           							</td>
           						</tr>
           						<tr>
						    		<td class="main" align="right"><a href="<?=tep_href_link(FILENAME_CSS_GENERATOR)?>"><?=tep_image_button('button_back.gif', IMAGE_BACK)?></a>&nbsp;&nbsp;<?=tep_image_submit('button_update.gif', IMAGE_UPDATE)?></td>
						    	</tr>
           					</table>
           					</form>
<?	
}
?>
    					</td>
    				</tr>
    			</table>
    		</td>
    	</tr>
    </table>
    </form>
    <!-- body_eof //-->
	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
<body>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php');?>