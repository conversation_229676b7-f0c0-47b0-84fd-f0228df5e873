<?
/*
  	$Id: csv_manager.php,v 1.2 2008/05/14 02:58:33 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'csv_manager.php');

$csv_manager_object = new csv_manager('payment');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch($action) {
	case "convert":
		$action_res = $csv_manager_object->do_csv_convert(FILENAME_CSV_MANAGER, 'csv_manager_inputs', $_REQUEST, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_CSV_MANAGER, tep_get_all_get_params(array('action'))));
		
		break;
/*
	case "reset_session":
    	unset($_SESSION['acc_statement_inputs']);
    	tep_redirect(tep_href_link(FILENAME_ACCOUNT_STATEMENT));
    	
    	break;
*/
	default:
		$header_title = HEADER_FORM_CSV_MANAGER_CRITERIA_TITLE;
		$form_content = $csv_manager_object->show_converter_form(FILENAME_CSV_MANAGER, 'csv_manager_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>