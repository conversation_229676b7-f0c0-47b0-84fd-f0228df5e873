<?php
/*
  $Id: currencies.php,v 1.21 2016/02/22 10:44:57 jeeva.ka<PERSON><PERSON>an Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'currencies_history.php');

$currencies = new currencies();
$currencies_history = new currencies_history();
$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case 'insert':
        case 'save':
            if (isset($HTTP_GET_VARS['cID']))
                $currency_id = tep_db_prepare_input($HTTP_GET_VARS['cID']);
            $title = tep_db_prepare_input($_POST['title']);
            $code = tep_db_prepare_input($_POST['code']);
            $symbol_left = tep_db_prepare_input($_POST['symbol_left']);
            $symbol_right = tep_db_prepare_input($_POST['symbol_right']);
            $decimal_point = tep_db_prepare_input($_POST['decimal_point']);
            $thousands_point = tep_db_prepare_input($_POST['thousands_point']);
            $decimal_places = tep_db_prepare_input($_POST['decimal_places']);
            $buy_value = tep_db_prepare_input($_POST['buy_value']);
            $value = tep_db_prepare_input($_POST['value']);
            $sell_value = tep_db_prepare_input($_POST['sell_value']);
            $currency_used_for = (isset($_POST['currency_used_for']) && is_array($_POST['currency_used_for']) ? implode(',', $_POST['currency_used_for']) : '');

            $sql_data_array = array('title' => $title,
                'code' => $code,
                'symbol_left' => $symbol_left,
                'symbol_right' => $symbol_right,
                'decimal_point' => $decimal_point,
                'thousands_point' => $thousands_point,
                'decimal_places' => $decimal_places,
                'buy_value' => $buy_value,
                'value' => $value,
                'sell_value' => $sell_value,
                'currencies_used_for' => $currency_used_for);

            $history_data_array = array('code' => $code,
                'buy_value' => $buy_value,
                'spot_value' => $value,
                'sell_value' => $sell_value,
            );
            if ($action == 'insert') {
                tep_db_perform(TABLE_CURRENCIES, $sql_data_array);
                $currency_id = tep_db_insert_id();
                $pr = $currencies_history->getCurrenciesHistoryId($currency_id, $history_data_array);
            } else if ($action == 'save') {
                $pr = $currencies_history->getCurrenciesHistoryId($currency_id, $history_data_array);
                tep_db_perform(TABLE_CURRENCIES, $sql_data_array, 'update', "currencies_id = '" . (int) $currency_id . "'");
            }
            if (isset($HTTP_POST_VARS['default']) && ($HTTP_POST_VARS['default'] == 'on')) {
                tep_db_query("update " . TABLE_CONFIGURATION . " set configuration_value = '" . tep_db_input($code) . "' where configuration_key = 'DEFAULT_CURRENCY'");
            }

            tep_redirect(tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $currency_id));
            break;
        case 'deleteconfirm':
            $currencies_id = tep_db_prepare_input($HTTP_GET_VARS['cID']);

			// /*-- GST : check if GST using this currency info --*/
			// $gst_sel_sql = "SELECT otc.currency 
			// 				FROM " . TABLE_CURRENCIES . " AS c 
			// 				INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
			// 					ON otc.currency = c.code 
			// 				WHERE c.currencies_id = '" . $currencies_id . "'";
			// $gst_res_sql = tep_db_query($gst_sel_sql);
			// if (tep_db_num_rows($gst_res_sql) > 0) {
			// 	$messageStack->add_session(ERROR_CURRENCY_IN_USE_IN_GST, 'error');
			// } else {
            $currency_query = tep_db_query("select currencies_id from " . TABLE_CURRENCIES . " where code = '" . DEFAULT_CURRENCY . "'");
            $currency = tep_db_fetch_array($currency_query);

            if ($currency['currencies_id'] == $currencies_id) {
                tep_db_query("update " . TABLE_CONFIGURATION . " set configuration_value = '' where configuration_key = 'DEFAULT_CURRENCY'");
            }

            tep_db_query("update " . TABLE_COUNTRIES . " set countries_currencies_id = NULL where countries_currencies_id = '" . tep_db_input($currencies_id) . "'");

            $history_data_array = array(
                'buy_value' => 0,
                'spot_value' => 0,
                'sell_value' => 0,
            );
            $pr = $currencies_history->getCurrenciesHistoryId($currencies_id, $history_data_array);
            tep_db_query("delete from " . TABLE_CURRENCIES . " where currencies_id = '" . (int) $currencies_id . "'");


	        // }

            tep_redirect(tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page']));
            break;
        case 'update':
            tep_set_time_limit(150);
            $spot_rate_array = array();

            $currency_query = tep_db_query("select currencies_id, code, value from " . TABLE_CURRENCIES . " where currencies_live_update = 1");
            $curl_rate = json_decode(quote_layer_currency(),1);
            while ($currency = tep_db_fetch_array($currency_query)) {
                $server_used = 'layer';
                $rate = $curl_rate["quotes"][DEFAULT_CURRENCY.$currency['code']];
                if (tep_not_null($rate)) {
                    $spot_rate_array[$currency['currencies_id']] = array(
                        'check' => 1,
                        'rate' => $rate,
                        'server' => $server_used,
                    );
                } else {
                    $spot_rate_array[$currency['currencies_id']] = array(
                        'check' => 0,
                        'rate' => $currency['value'],
                        'server' => $server_used,
                    );
                }
            }
            break;
        case 'confirm_update':
            if (isset($HTTP_POST_VARS['value_param']) && is_array($HTTP_POST_VARS['value_param'])) {
                $changed_currency_array = array();
                foreach ($HTTP_POST_VARS['value_param'] as $cur_id => $spot_value) {
                    $buy_value = preg_replace('/[^\d.]/', '', preg_quote($HTTP_POST_VARS['buy_value_param'][$cur_id]));
                    $sell_value = preg_replace('/[^\d.]/', '', preg_quote($HTTP_POST_VARS['sell_value_param'][$cur_id]));
                    $currency_code = $currencies->get_code_by_id($cur_id);
                    $changed_currency_array[] = $currency_code;
                    $sql_data_array = array('value' => $spot_value,
                        'buy_value' => $buy_value,
                        'buy_value_adjust' => $HTTP_POST_VARS['buy_percent_param'][$cur_id],
                        'sell_value' => $sell_value,
                        'sell_value_adjust' => $HTTP_POST_VARS['sell_percent_param'][$cur_id]
                    );

                    $history_data_array = array('code' => $currency_code,
                        'buy_value' => $buy_value,
                        'spot_value' => $spot_value,
                        'sell_value' => $sell_value,
                    );

                    $pr = $currencies_history->getCurrenciesHistoryId($cur_id, $history_data_array);
                    tep_db_perform(TABLE_CURRENCIES, $sql_data_array, 'update', "currencies_id = '" . (int) $cur_id . "'");
                    $messageStack->add_session(sprintf(SUCCESS_CURRENCY_UPDATED, $currency_code), 'success');
                }
            }
            tep_redirect(tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page']));

            break;
        case 'delete':
            $currencies_id = tep_db_prepare_input($HTTP_GET_VARS['cID']);

            $currency_query = tep_db_query("select code from " . TABLE_CURRENCIES . " where currencies_id = '" . (int) $currencies_id . "'");
            $currency = tep_db_fetch_array($currency_query);

            $remove_currency = true;
            if ($currency['code'] == DEFAULT_CURRENCY) {
                $remove_currency = false;
                $messageStack->add(ERROR_REMOVE_DEFAULT_CURRENCY, 'error');
            }
            break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <?
                        if ($action == 'update') {
                            $currency_query_raw = "select * from " . TABLE_CURRENCIES . " where currencies_live_update = 1 and code <> '" . DEFAULT_CURRENCY . "' order by title";
                            $currency_query = tep_db_query($currency_query_raw);
                            ?>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td valign="top">
                                                <?= tep_draw_form('currencies_update_form', FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&action=confirm_update', 'post', 'onSubmit="return currencies_update_form_checking();"') ?>
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <td class="reportBoxHeading"><?= TABLE_HEADING_CURRENCY_NAME ?></td>
                                                        <td class="reportBoxHeading"><?= TABLE_HEADING_CURRENCY_CODES ?></td>
                                                        <td width="8%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_CURRENCY_BUY_RATIO ?></td>
                                                        <td width="12%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_CURRENCY_BUY_VALUE ?></td>
                                                        <td width="12%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_CURRENCY_VALUE ?></td>
                                                        <td width="12%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_CURRENCY_SELL_VALUE ?></td>
                                                        <td width="8%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_CURRENCY_SELL_RATIO ?></td>
                                                        <td width="5%" class="reportBoxHeading" align="center">&nbsp;</td>
                                                    </tr>
                                                    <?
                                                    $updatable_currency_array = array();

                                                    $row_count = 0;
                                                    while ($currency = tep_db_fetch_array($currency_query)) {
                                                        $updatable_currency_array[$currency['currencies_id']] = $currency['code'];

                                                        $row_style = ($row_count % 2) ? "reportListingEven" : "reportListingOdd";

                                                        $currency_title = (DEFAULT_CURRENCY == $currency['code']) ? $currency['title'] . ' (' . TEXT_DEFAULT . ')' : $currency['title'];

                                                        $spot_value = number_format($spot_rate_array[$currency['currencies_id']]['rate'], 8, '.', '');
                                                        $provision_buy_value = (1 - ((double) $currency['buy_value_adjust'] / 100)) * $spot_value;
                                                        $provision_sell_value = (1 + ((double) $currency['sell_value_adjust'] / 100)) * $spot_value;
                                                        ?>
                                                        <tr class="<?= $row_style ?>" onMouseOver="rowOverEffect(this, 'reportListingRowOver')" onMouseOut="rowOutEffect(this, '<?= $row_style ?>')" onClick="rowClicked(this, 'reportListingRowOver')">
                                                            <td class="dataTableContent"><?= $currency_title ?></td>
                                                            <td class="dataTableContent"><?= $currency['code'] ?></td>
                                                            <td align="center" class="dataTableContent" nowrap>
                                                                <?
                                                                echo '- ' . tep_draw_input_field('buy_percent_param[' . $currency['currencies_id'] . ']', $currency['buy_value_adjust'], ' id="buy_value_adjust_' . $currency['currencies_id'] . '" size="5" onChange="if (trim_str(this.value)!=\'\' && !currencyValidation(this.value)) { this.value =\'\'; } else { update_rate(this, \'' . $currency['currencies_id'] . '\', \'buy\'); }" onKeyPress="return noEnterKey(event)" ') . '%';
                                                                ?>
                                                            </td>
                                                            <td align="center" class="dataTableContent">
                                                                <?
                                                                echo tep_draw_input_field('buy_value_param[' . $currency['currencies_id'] . ']', $provision_buy_value, ' id="buy_value_' . $currency['currencies_id'] . '" size="12" onChange="if (trim_str(this.value)!=\'\' && !currencyValidation(this.value)) { this.value =\'\'; }" onKeyPress="return noEnterKey(event)" ');
                                                                echo '<br><span class="blueIndicator"><i>' . number_format($currency['buy_value'], 8) . '</i></span>' . tep_draw_hidden_field('buy_old_value_param[' . $currency['currencies_id'] . ']', number_format($currency['buy_value'], 8, '.', ''), 'id="buy_old_value_' . $currency['currencies_id'] . '"');
                                                                ?>
                                                            </td>
                                                            <td align="center" class="dataTableContent">
                                                                <?
                                                                echo tep_draw_input_field('value_param[' . $currency['currencies_id'] . ']', $spot_value, ' id="value_' . $currency['currencies_id'] . '" size="12" onChange="if (trim_str(this.value)!=\'\' && !currencyValidation(this.value)) { this.value =\'\'; } else { update_rate(DOMCall(\'buy_value_adjust_' . $currency['currencies_id'] . '\'), \'' . $currency['currencies_id'] . '\', \'buy\'); update_rate(DOMCall(\'sell_value_adjust_' . $currency['currencies_id'] . '\'), \'' . $currency['currencies_id'] . '\', \'sell\');} " onKeyPress="return noEnterKey(event)" ');
                                                                echo '<br><span class="blueIndicator"><i>' . number_format($currency['value'], 8) . ' (' . $spot_rate_array[$currency['currencies_id']]['server'] . ')</span>' . tep_draw_hidden_field('old_value_param[' . $currency['currencies_id'] . ']', number_format($currency['value'], 8, '.', ''), 'id="old_value_' . $currency['currencies_id'] . '"');
                                                                ?>
                                                            </td>
                                                            <td align="center" class="dataTableContent">
                                                                <?
                                                                echo tep_draw_input_field('sell_value_param[' . $currency['currencies_id'] . ']', $provision_sell_value, ' id="sell_value_' . $currency['currencies_id'] . '" size="12" onChange="if (trim_str(this.value)!=\'\' && !currencyValidation(this.value)) { this.value =\'\'; }" onKeyPress="return noEnterKey(event)" ');
                                                                echo '<br><span class="blueIndicator"><i>' . number_format($currency['sell_value'], 8) . '</span>' . tep_draw_hidden_field('sell_old_value_param[' . $currency['currencies_id'] . ']', number_format($currency['sell_value'], 8, '.', ''), 'id="sell_old_value_' . $currency['currencies_id'] . '"');
                                                                ?>
                                                            </td>
                                                            <td align="center" class="dataTableContent" nowrap>
                                                                <?
                                                                echo '+ ' . tep_draw_input_field('sell_percent_param[' . $currency['currencies_id'] . ']', $currency['sell_value_adjust'], ' id="sell_value_adjust_' . $currency['currencies_id'] . '" size="5" onChange="if (trim_str(this.value)!=\'\' && !currencyValidation(this.value)) { this.value =\'\'; } else { update_rate(this, \'' . $currency['currencies_id'] . '\', \'sell\'); }" onKeyPress="return noEnterKey(event)" ') . '%<br>';
                                                                ?>
                                                            </td>
                                                            <td>&nbsp;</td>
                                                        </tr>
                                                        <?
                                                        $row_count++;
                                                    }
                                                    ?>
                                                    <tr>
                                                        <td colspan="7">&nbsp;</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="7">
                                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                <tr>
                                                                    <td align="left"><?= tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page']), '', 'inputButton') ?></td>
                                                                    <td align="right"><?= tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, '', 'redButton') ?></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                    <script type="text/javascript">
                                        <!--
                                            function update_rate(adjust_obj, box_id, exchange_type) {
                                            var update_obj = DOMCall(exchange_type + '_value_' + box_id);
                                            var spot_val = DOMCall('value_' + box_id).value;

                                            if (update_obj != null) {
                                                if (exchange_type == 'buy') {
                                                    update_obj.value = currency_display((1 - (adjust_obj.value / 100)) * spot_val, 8);
                                                } else {
                                                    update_obj.value = currency_display((1 + (adjust_obj.value / 100)) * spot_val, 8);
                                                }
                                            }
                                        }

                                        function currencies_update_form_checking() {
                                            var buy_new_obj = '';
                                            var buy_old_obj = '';
                                            var spot_new_obj = '';
                                            var spot_old_obj = '';
                                            var sell_new_obj = '';
                                            var sell_old_obj = '';
                                            var adjusted_ratio = 0;
                                            var warning_message = '';

                                            var cur_array = new Array();
    <?
    foreach ($updatable_currency_array as $cur_id => $cur_code) {
        echo "cur_array['" . $cur_id . "'] = '" . $cur_code . "';\n";
    }
    ?>
                                            for (cur_id in cur_array) {
                                                buy_new_obj = DOMCall('buy_value_' + cur_id);
                                                buy_old_obj = DOMCall('buy_old_value_' + cur_id);

                                                spot_new_obj = DOMCall('value_' + cur_id);
                                                spot_old_obj = DOMCall('old_value_' + cur_id);

                                                sell_new_obj = DOMCall('sell_value_' + cur_id);
                                                sell_old_obj = DOMCall('sell_old_value_' + cur_id);

                                                if (buy_new_obj != null && buy_old_obj != null) {
                                                    if (buy_old_obj.value > 0) {
                                                        adjusted_ratio = (buy_new_obj.value - buy_old_obj.value) / buy_old_obj.value * 100;

                                                        if (Math.abs(adjusted_ratio) > 2) {
                                                            warning_message += cur_array[cur_id] + "\'s Buy Value get adjusted more than 2% \(" + currency_display(adjusted_ratio, 4) + "%\)" + "\n";
                                                        }
                                                    }
                                                }

                                                if (spot_new_obj != null && spot_old_obj != null) {
                                                    if (spot_old_obj.value > 0) {
                                                        adjusted_ratio = (spot_new_obj.value - spot_old_obj.value) / spot_old_obj.value * 100;

                                                        if (Math.abs(adjusted_ratio) > 2) {
                                                            warning_message += cur_array[cur_id] + "\'s Spot Value get adjusted more than 2% \(" + currency_display(adjusted_ratio, 4) + "%\)" + "\n";
                                                        }
                                                    }
                                                }

                                                if (sell_new_obj != null && sell_old_obj != null) {
                                                    if (sell_old_obj.value > 0) {
                                                        adjusted_ratio = (sell_new_obj.value - sell_old_obj.value) / sell_old_obj.value * 100;

                                                        if (Math.abs(adjusted_ratio) > 2) {
                                                            warning_message += cur_array[cur_id] + "\'s Sell Value get adjusted more than 2% \(" + currency_display(adjusted_ratio, 4) + "%\)" + "\n";
                                                        }
                                                    }
                                                }
                                            }

                                            if (warning_message != '') {
                                                warning_message += "\nClick on \"OK\" if you want to proceed this update or \nClick on \"Cancel\" to make changes";
                                                return confirm_action(warning_message);
                                            }

                                            return true;
                                        }
                                        //-->
                                    </script>
                                </td>
                            </tr>
                            <?
                        } else {
                            $currency_query_raw = "select currencies_id, title, code, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, last_updated, value, buy_value, sell_value, currencies_used_for from " . TABLE_CURRENCIES . " order by title";
                            $currency_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $currency_query_raw, $currency_query_numrows);
                            $currency_query = tep_db_query($currency_query_raw);
                            ?>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td valign="top">
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <td class="reportBoxHeading"><?php echo TABLE_HEADING_CURRENCY_NAME; ?></td>
                                                        <td class="reportBoxHeading"><?php echo TABLE_HEADING_CURRENCY_CODES; ?></td>
                                                        <!--td class="reportBoxHeading" align="left"><?= TABLE_HEADING_CURRENCY_USED_FOR ?></td-->
                                                        <td class="reportBoxHeading" align="right"><?= TABLE_HEADING_CURRENCY_BUY_VALUE ?></td>
                                                        <td class="reportBoxHeading" align="right"><?= TABLE_HEADING_CURRENCY_VALUE ?></td>
                                                        <td class="reportBoxHeading" align="right"><?= TABLE_HEADING_CURRENCY_SELL_VALUE ?></td>
                                                        <td class="reportBoxHeading" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
                                                    </tr>
                                                    <?
                                                    while ($currency = tep_db_fetch_array($currency_query)) {
                                                        if ((!isset($HTTP_GET_VARS['cID']) || (isset($HTTP_GET_VARS['cID']) && ($HTTP_GET_VARS['cID'] == $currency['currencies_id']))) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
                                                            $cInfo = new objectInfo($currency);
                                                        }

                                                        if (isset($cInfo) && is_object($cInfo) && ($currency['currencies_id'] == $cInfo->currencies_id)) {
                                                            echo '       					       	<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=edit') . '\'">' . "\n";
                                                        } else {
                                                            echo '              					<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $currency['currencies_id']) . '\'">' . "\n";
                                                        }

                                                        if (DEFAULT_CURRENCY == $currency['code']) {
                                                            echo '                						<td class="dataTableContent"><b>' . $currency['title'] . ' (' . TEXT_DEFAULT . ')</b></td>' . "\n";
                                                        } else {
                                                            echo '                						<td class="dataTableContent">' . $currency['title'] . '</td>' . "\n";
                                                        }
                                                        ?>
                                                        <td class="dataTableContent"><?php echo $currency['code']; ?></td>
                                                        <!--td class="dataTableContent" align="left"><?= $currency['currencies_used_for'] ?></td-->
                                                        <td class="dataTableContent" align="right"><?= number_format($currency['buy_value'], 8) ?></td>
                                                        <td class="dataTableContent" align="right"><?= number_format($currency['value'], 8) ?></td>
                                                        <td class="dataTableContent" align="right"><?= number_format($currency['sell_value'], 8) ?></td>
                                                        <td class="dataTableContent" align="right"><?php if (isset($cInfo) && is_object($cInfo) && ($currency['currencies_id'] == $cInfo->currencies_id)) {
                                                    echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif');
                                                } else {
                                                    echo '<a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $currency['currencies_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
                                                } ?>&nbsp;</td>
                                            </tr>
    <? } ?>
                                        <tr>
                                            <td colspan="6">
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <td class="smallText" valign="top"><?php echo $currency_split->display_count($currency_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_CURRENCIES); ?></td>
                                                        <td class="smallText" align="right"><?php echo $currency_split->display_links($currency_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                                                    </tr>
                                                    <? if (empty($action)) { ?>
                                                        <tr>
                                                            <td><?php if (CURRENCY_SERVER_PRIMARY) {
                                                            echo tep_button(BUTTON_PREVIEW_CURRENCY, ALT_BUTTON_PREVIEW_CURRENCY, tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=update'), '', 'inputButton');
                                                        } ?></td>
                                                            <td align="right"><?php echo '<a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=new') . '">' . tep_image_button('button_new_currency.gif', IMAGE_NEW_CURRENCY) . '</a>'; ?></td>
                                                        </tr>
                                <? } ?>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <?
                                $heading = array();
                                $contents = array();

                                switch ($action) {
                                    case 'new':
                                        $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_CURRENCY . '</b>');

                                        $contents = array('form' => tep_draw_form('currencies', FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . (isset($cInfo) ? '&cID=' . $cInfo->currencies_id : '') . '&action=insert'));
                                        $contents[] = array('text' => TEXT_INFO_INSERT_INTRO);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_TITLE . '<br>' . tep_draw_input_field('title'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_CODE . '<br>' . tep_draw_input_field('code'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SYMBOL_LEFT . '<br>' . tep_draw_input_field('symbol_left'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SYMBOL_RIGHT . '<br>' . tep_draw_input_field('symbol_right'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_DECIMAL_POINT . '<br>' . tep_draw_input_field('decimal_point'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_THOUSANDS_POINT . '<br>' . tep_draw_input_field('thousands_point'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_DECIMAL_PLACES . '<br>' . tep_draw_input_field('decimal_places'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_BUY_VALUE . '<br>' . tep_draw_input_field('buy_value'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_VALUE . '<br>' . tep_draw_input_field('value'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SELL_VALUE . '<br>' . tep_draw_input_field('sell_value'));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_USED_FOR . '<br><div style="float: left;">' . tep_draw_checkbox_field("currency_used_for[]", 'SELL', false) . '</div><div>Selling Site</div><br><div style="float: left;">' . tep_draw_checkbox_field("currency_used_for[]", 'BUY', false) . '</div><div>Buying Site</div>');

                                        $contents[] = array('text' => '<br>' . tep_draw_checkbox_field('default') . ' ' . TEXT_INFO_SET_AS_DEFAULT);
                                        $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $HTTP_GET_VARS['cID']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');

                                        break;
                                    case 'edit':
                                        $currency_usage_array = explode(',', $cInfo->currencies_used_for);

                                        $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_CURRENCY . '</b>');

                                        $contents = array('form' => tep_draw_form('currencies', FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=save'));
                                        $contents[] = array('text' => TEXT_INFO_EDIT_INTRO);
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_TITLE . '<br>' . tep_draw_input_field('title', $cInfo->title));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_CODE . '<br>' . tep_draw_input_field('code', $cInfo->code));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SYMBOL_LEFT . '<br>' . tep_draw_input_field('symbol_left', htmlentities($cInfo->symbol_left)));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SYMBOL_RIGHT . '<br>' . tep_draw_input_field('symbol_right', htmlentities($cInfo->symbol_right)));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_DECIMAL_POINT . '<br>' . tep_draw_input_field('decimal_point', $cInfo->decimal_point));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_THOUSANDS_POINT . '<br>' . tep_draw_input_field('thousands_point', $cInfo->thousands_point));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_DECIMAL_PLACES . '<br>' . tep_draw_input_field('decimal_places', $cInfo->decimal_places));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_BUY_VALUE . '<br>' . tep_draw_input_field('buy_value', $cInfo->buy_value));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_VALUE . '<br>' . tep_draw_input_field('value', $cInfo->value));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SELL_VALUE . '<br>' . tep_draw_input_field('sell_value', $cInfo->sell_value));
                                        $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_USED_FOR . '<br><div style="float: left;">' . tep_draw_checkbox_field("currency_used_for[]", 'SELL', (in_array('SELL', $currency_usage_array) ? true : false)) . '</div><div>Selling Site</div><br><div style="float: left;">' . tep_draw_checkbox_field("currency_used_for[]", 'BUY', (in_array('BUY', $currency_usage_array) ? true : false)) . '</div><div>Buying Site</div>');

                                        if (DEFAULT_CURRENCY != $cInfo->code)
                                            $contents[] = array('text' => '<br>' . tep_draw_checkbox_field('default') . ' ' . TEXT_INFO_SET_AS_DEFAULT);
                                        $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');

                                        break;
                                    case 'delete':
                                        $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_CURRENCY . '</b>');

                                        $contents[] = array('text' => TEXT_INFO_DELETE_INTRO);
                                        $contents[] = array('text' => '<br><b>' . $cInfo->title . '</b>');
                                        $contents[] = array('align' => 'center', 'text' => '<br>' . (($remove_currency) ? '<a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=deleteconfirm') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>' : '') . ' <a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');

                                        break;
                                    default:
                                        if (is_object($cInfo)) {
                                            $heading[] = array('text' => '<b>' . $cInfo->title . '</b>');

                                            $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_CURRENCIES, 'page=' . $HTTP_GET_VARS['page'] . '&cID=' . $cInfo->currencies_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
                                            $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_TITLE . ' ' . $cInfo->title);
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_CODE . ' ' . $cInfo->code);
                                            $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_SYMBOL_LEFT . ' ' . $cInfo->symbol_left);
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_SYMBOL_RIGHT . ' ' . $cInfo->symbol_right);
                                            $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_DECIMAL_POINT . ' ' . $cInfo->decimal_point);
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_THOUSANDS_POINT . ' ' . $cInfo->thousands_point);
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_DECIMAL_PLACES . ' ' . $cInfo->decimal_places);
                                            $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_LAST_UPDATED . ' ' . tep_date_short($cInfo->last_updated));
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_BUY_VALUE . ' ' . number_format($cInfo->buy_value, 8));
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_VALUE . ' ' . number_format($cInfo->value, 8));
                                            $contents[] = array('text' => TEXT_INFO_CURRENCY_SELL_VALUE . ' ' . number_format($cInfo->sell_value, 8));
                                            $contents[] = array('text' => '<br>' . TEXT_INFO_CURRENCY_EXAMPLE . '<br>' . $currencies->format('30', false, DEFAULT_CURRENCY) . ' = ' . $currencies->format('30', true, $cInfo->code));
                                        }
                                        break;
                                }

                                if ((tep_not_null($heading)) && (tep_not_null($contents))) {
                                    echo '            					<td width="25%" valign="top">' . "\n";

                                    $box = new box;
                                    echo $box->infoBox($heading, $contents);

                                    echo '            					</td>' . "\n";
                                }
                                ?>
                            </tr>
                        </table>
                    </td>
                </tr>
    <?
}
?>
        </table>
    </td>
    <!-- body_text_eof //-->
</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>