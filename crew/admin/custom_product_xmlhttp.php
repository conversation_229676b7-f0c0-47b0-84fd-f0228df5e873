<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

$currencies = new currencies();

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';
$subaction = isset($HTTP_GET_VARS['subaction']) ? $HTTP_GET_VARS['subaction'] : '';
$game_char_id = isset($HTTP_GET_VARS['game_char_id']) ? (int)$HTTP_GET_VARS['game_char_id'] : '';
$language_id = isset($HTTP_GET_VARS['lang']) ? (int)$HTTP_GET_VARS['lang'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';
$this_admin_email = isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : '';

$results = '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

if (tep_not_null($action)) {
	switch($action) {
		case "get_full_products_categories_path":
			echo '<response>';
			echo '<products_cat_path><![CDATA[';
			if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
				$publishers_products_select_sql = "	SELECT p.products_cat_path, pd.products_name 
													FROM " . TABLE_PRODUCTS . " AS p 
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
														ON p.products_id = pd.products_id
													WHERE p.products_id = '".(int)$_REQUEST['pID']."'
														AND pd.language_id = 1 
														AND pd.products_name <> ''";
				$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
				if ($publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
					echo strip_tags($publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name']);
				}
			}
			echo ']]></products_cat_path>';
			echo '</response>';
			break;
		case "get_cdkey_image":
			$custom_product_id = (int)$HTTP_GET_VARS['cp_id'];
			if (tep_not_null($custom_product_id)) {
				$img_source = '';
				
				$view_cdkey_images_permission = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_CDKEY_IMAGES');
				
				if ($view_cdkey_images_permission) {
					$cdkey_info_select_sql = "	SELECT file_name 
												FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
												WHERE custom_products_code_id = '".$custom_product_id."'";
					$cdkey_info_result_sql = tep_db_query($cdkey_info_select_sql);
					$cdkey_info_row = tep_db_fetch_array($cdkey_info_result_sql);
					
					$img_source = tep_image(tep_href_link(FILENAME_CDKEY, 'keyident='.$custom_product_id), tep_db_prepare_input($cdkey_info_row['file_name']));
				}
				
				echo '<response>';
				echo '<cdkey_image><![CDATA[';
				echo $img_source;
				echo ']]></cdkey_image>';
				echo '</response>';
			}
			
			break;

		case "get_cdkey_image_multiple":
			echo '<response><cdkey_image>';
			
			$custom_product_id = (string)$HTTP_GET_VARS['cp_id'];
			if (tep_not_null($custom_product_id)) {
				$view_cdkey_images_permission = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_CDKEY_IMAGES');
				
				if ($view_cdkey_images_permission) {
					
					$cdkey_info_select_sql = "	SELECT custom_products_code_id, code_date_added, file_name 
												FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
												WHERE custom_products_code_id in (".$custom_product_id.")";
					$cdkey_info_result_sql = tep_db_query($cdkey_info_select_sql);
					while ($cdkey_info_row = tep_db_fetch_array($cdkey_info_result_sql)) {
						$img_source = tep_image(tep_href_link(FILENAME_CDKEY, 'keyident='.$cdkey_info_row['custom_products_code_id']), tep_db_prepare_input($cdkey_info_row['file_name']));
						
						echo "<image_".$cdkey_info_row['custom_products_code_id'].">";
						echo '<![CDATA[';
						echo $img_source;
						echo ']]>';
						echo "</image_".$cdkey_info_row['custom_products_code_id'].">";
					}
				}
			}
			echo '</cdkey_image></response>';
			
			break;
		case "get_cdkey_log":
			$add_remarks = tep_admin_files_actions(FILENAME_CDKEY, 'CP_ADD_REMARKS');
			$custom_product_id = (int)$HTTP_GET_VARS['cp_id'];
			
			$result_html = '<table boprder="0" cellspacing="0" cellpadding="2">
								<tr><td class="main">CD Key Log:</td></tr>
								<tr>
									<td class="main" id="remarks_main">';

			$log_select_sql = "	SELECT custom_products_code_log_user, custom_products_code_log_user_role, log_time, log_system_messages, log_ip 
								FROM " . TABLE_CUSTOM_PRODUCTS_CODE_LOG . " 
								WHERE custom_products_code_id='" . tep_db_input($custom_product_id) . "' 
								ORDER BY log_time";
			$log_result_sql = tep_db_query($log_select_sql);
			while ($log_row = tep_db_fetch_array($log_result_sql)) {
				switch($log_row['custom_products_code_log_user_role']) {
					case 'customers':
						$email_select_sql = "	SELECT customers_email_address 
												FROM " . TABLE_CUSTOMERS . "
												WHERE customers_id = '" . tep_db_input($log_row['custom_products_code_log_user']) . "'";
						$email_result_sql = tep_db_query($email_select_sql);
						$email_row = tep_db_fetch_array($email_result_sql);
						
						$user_email = $email_row['customers_email_address'];
						
						break;
					case 'admin':
						$email_select_sql = "	SELECT admin_email_address 
												FROM " . TABLE_ADMIN . "
												WHERE admin_id = '" . tep_db_input($log_row['custom_products_code_log_user']) . "'";
						$email_result_sql = tep_db_query($email_select_sql);
						$email_row = tep_db_fetch_array($email_result_sql);
						
						$user_email = $email_row['admin_email_address'];
						
						break;
					default:
						$user_email = $log_row['custom_products_code_log_user'];
						
						break;
				}
				
				$result_html .= '<b>' . $log_row["log_time"].'&nbsp;(by '.$user_email.' @ '.$log_row['log_ip'].')</b><br>';
				$result_html .= nl2br($log_row["log_system_messages"]) . '<br><br>';
			}
			
			$result_html .= '		</td>
								</tr>
							</table><br>';

			if ($add_remarks) {
				$result_html .= '
								<form name="remarks">
								<table cellpadding=3 cellspacing=0 border=0>
								<tr>
								<td class="main" valign=top><b>Add Remarks : </b></td><td class="main">'.tep_draw_textarea_field('remarks', 1, 55, 7, '', "size=\"50\" maxlength=\"50\" id=\"remarks\" ").'</td>
								</tr>
								<tr>
								<td>'.tep_button(' Add ', ' Add ', '', 'onClick="javascript:addRemarks()" onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true).'
								<td><div id="remarks_ajax_loading"></div></td>
								</tr>
								</table>'.tep_draw_hidden_field("remarks_custom_product_id", $custom_product_id, ' id="remarks_custom_product_id"').'
								</form>';
			}
						
			echo $result_html;
			break;
		case "perform_tagging":
			$order_status_id = (int)$HTTP_GET_VARS['status_id'];
			$setting_value = $HTTP_GET_VARS['setting'];
			$order_ids_array = explode(',', $HTTP_GET_VARS['o_str']);
			$list_mode = (int)$HTTP_GET_VARS['list_mode'];
			echo "<tag_info>";
			if ($subaction == 'nt') {
				if (tep_not_null($setting_value)) {
					$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
					$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
					if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
						$orders_tag_id = (int)$tag_verify_row["orders_tag_id"];
					} else {
						$insert_sql_data = array(	'orders_tag_name' => tep_db_prepare_input($setting_value),
	                                   				'orders_tag_status_ids' => $order_status_id,
	                                   				'filename' => FILENAME_PROGRESS_REPORT
	                                   			);
						tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
						$orders_tag_id = tep_db_insert_id();
					}
					
					$assign_orders_tag_update_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids = IF (orders_tag_ids='', '".$orders_tag_id."', CONCAT_WS(',', orders_tag_ids, '".$orders_tag_id."')) WHERE orders_products_id IN (".implode(',', $order_ids_array).") AND NOT FIND_IN_SET('".$orders_tag_id."', orders_tag_ids)";
					tep_db_query($assign_orders_tag_update_sql);
					
					generateTagString($order_ids_array);
				}
			} else if ($subaction == 'at') {
				$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int)$setting_value . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
				$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
				if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
					// update all the selected orders with this tag
					$assign_orders_tag_update_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids = IF (orders_tag_ids='', '".(int)$setting_value."', CONCAT_WS(',', orders_tag_ids, '".(int)$setting_value."')) WHERE orders_products_id IN (".implode(',', $order_ids_array).") AND NOT FIND_IN_SET('".(int)$setting_value."', orders_tag_ids)";
					tep_db_query($assign_orders_tag_update_sql);
					
					generateTagString($order_ids_array);
				}
			} else if ($subaction == 'rt') {
				$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int)$setting_value . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
				$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
				if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
					// update all the selected orders by removing this tag from them
					$unassign_orders_tag_select_sql = "SELECT orders_products_id, orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id IN (".implode(',', $order_ids_array).") AND FIND_IN_SET('".(int)$setting_value."', orders_tag_ids)";
					$unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
					while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
						$TagRemovePattern = "/(,)?".(int)$setting_value."(,)?/is";
						$new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
						if (substr($new_tag_string, 0, 1) == ',') 	$new_tag_string = substr($new_tag_string, 1);
						if (substr($new_tag_string, -1) == ',') 	$new_tag_string = substr($new_tag_string, 0, -1);
						
						tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='".$new_tag_string."' WHERE orders_products_id='" . $unassign_orders_tag_row["orders_products_id"] . "'");
					}
					
					generateTagString($order_ids_array);
				}
			}/* else if ($subaction == 'rd' || $subaction == 'ur') {
				if (tep_not_null($setting_value) && count($order_ids_array)) {
					$orders_read_mode_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_read_mode = '" . $setting_value . "' WHERE orders_id IN (".implode(',', $order_ids_array)."); ";
					tep_db_query($orders_read_mode_update_sql);
					generateReadModeString($order_ids_array);
				}
			}*/
			generateTagSelectionOptions($order_status_id, $HTTP_GET_VARS['o_str'], ($list_mode == "2" ? true : false));
			echo "</tag_info>";
			break;
		case "retrieve_status_tags":
			generateTagSelectionOptions($HTTP_GET_VARS['status'], '', '', true);
			break;
		case "refresh_tag_selection":
			$orders_tag_id = substr($HTTP_GET_VARS['status'], 2);
			generateTagSelectionOptions($orders_tag_id, $HTTP_GET_VARS['o_str']);
			break;
		case "update_follow_up":
			$order_product_id = (int)$HTTP_GET_VARS['op_id'];
			
			echo '<response>';
			$task_allocation_select_sql = "SELECT DATE_FORMAT(supplier_tasks_follow_up_datetime, '%Y-%m-%d %H:%i') AS current_follow_date FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id='" . $order_product_id . "'";
			$task_allocation_result_sql = tep_db_query($task_allocation_select_sql);
			if ($task_allocation_row = tep_db_fetch_array($task_allocation_result_sql)) {
				if (!tep_not_null($HTTP_GET_VARS['follow_date']) || tep_day_diff(date('Y-m-d H:i:s'), $HTTP_GET_VARS['follow_date']) !== FALSE) {
					if ($task_allocation_row['current_follow_date'] != $HTTP_GET_VARS['follow_date']) {
						$follow_date_update_sql = "	UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
													SET supplier_tasks_follow_up_datetime = " . (tep_not_null($HTTP_GET_VARS['follow_date']) ? "'".tep_db_input($HTTP_GET_VARS['follow_date'])."'" : 'NULL') . "
													WHERE orders_products_id='" . $order_product_id . "'";
						tep_db_query($follow_date_update_sql);
						
						$follow_date_select_sql = "SELECT supplier_tasks_follow_up_datetime FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id='" . $order_product_id . "'";
						$follow_date_result_sql = tep_db_query($follow_date_select_sql);
						$follow_date_row = tep_db_fetch_array($follow_date_result_sql);
						
						$task_allocation_history_str = tep_not_null($HTTP_GET_VARS['follow_date']) ? 'Set progress report follow-up date to ' . tep_db_input($follow_date_row["supplier_tasks_follow_up_datetime"]) : 'Clear the follow-up date';
						$task_allocation_history_data_array = array('orders_products_id' => tep_db_prepare_input($order_product_id),
																	'supplier_tasks_status' => 'NULL',
																	'date_added' => 'now()',
																	'comments' => tep_db_prepare_input($task_allocation_history_str),
																	'changed_by' => tep_db_prepare_input($this_admin_email),
																	'user_role' => 'admin',
																	'notify_recipient' => 0,
																	'supplier_tasks_allocation_history_show' => 0
																	);
						tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $task_allocation_history_data_array);
						
						echo "<res_code>1</res_code>";
						echo "<result><![CDATA[Follow-up date successfully updated to ".$follow_date_row["supplier_tasks_follow_up_datetime"]."]]></result>";
					} else {
						echo "<res_code>0</res_code>";
						echo "<result>No date changes!</result>";
					}
				} else {
					echo "<res_code>0</res_code>";
					echo "<result>Invalid date!</result>";
				}
			} else {
				echo "<res_code>0</res_code>";
				echo "<result>Powerleveling Order not found!</result>";
			}
			echo '</response>';
			
			break;
		case "character_history_date":
				$profiler_date_count = 1;
				
				$game_char_history_date_select_sql = "	SELECT gch.game_char_history_date 
														FROM " . TABLE_GAME_CHAR . " AS gc 
														INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
															ON (gc.game_char_id = gch.game_char_id) 
														WHERE gc.game_char_id ='" . tep_db_input($game_char_id) . "' ORDER BY gch.game_char_history_date ASC";
				$game_char_history_date_result_sql = tep_db_query($game_char_history_date_select_sql);
				
				echo "<selection>";
				while ($game_char_history_date_row = tep_db_fetch_array($game_char_history_date_result_sql)) {
					echo "<option index='".$profiler_date_count."'><![CDATA[".$game_char_history_date_row["game_char_history_date"]."]]></option>";
					$profiler_date_count++;
				}
				echo "</selection>";
			break;
		case "start_level_task":
			if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "order_cp_info.php")) {
				include_once(DIR_WS_LANGUAGES . $language . '/' . "order_cp_info.php");
			}
			
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_start_level = (int)$HTTP_GET_VARS['s_level'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $data_pool_level_id . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
		    
			$complete_range_array = tep_get_bracket_range($data_pool_level_id);
			$range_mode = $complete_range_array["mode"];
			
			$level_select_sql = "	SELECT CAST(b1.brackets_value as SIGNED) AS level_val, b2.brackets_value AS alias
									FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2 
									WHERE b1.brackets_dependent=b2.brackets_dependent 
										AND b1.data_pool_level_id ='" . $data_pool_level_id . "' 
										AND b1.brackets_value > " . $sel_start_level . "
										AND b1.brackets_key='pl_end_level' 
										AND b2.brackets_key='pl_level_alias' 
									ORDER BY level_val";
			$level_result_sql = tep_db_query($level_select_sql);
			echo '<response>';
			echo "<desired_selection>";
			$i = 0;
			$previous_val = $sel_start_level;
			while ($level_row = tep_db_fetch_array($level_result_sql)) {
				if ($range_mode=='continuos') {
					if ($i == 0) {	// First loop, need to check this bracket is the 'first bracket' of the whole bracket level. If yes, do not auto generate the rage between the selected start level and this first bracket level.
						if ($sel_start_level >= $complete_range_array["range"][0]["level"]) {
							for ($linking=$previous_val+1; $linking < $level_row['level_val']; $linking++) {
								echo "<option index='".$linking."'><![CDATA[".$linking."]]></option>";
							}
						}
					} else {
						for ($linking=$previous_val+1; $linking < $level_row['level_val']; $linking++) {
							echo "<option index='".$linking."'><![CDATA[".$linking."]]></option>";
						}
					}
				}
				$previous_val = $level_row['level_val'];
				echo "<option index='".$level_row['level_val']."'><![CDATA[".(isset($level_row['alias']) && trim($level_row['alias']) != '' ? $level_row['alias'] : $level_row['level_val'])."]]></option>";
				$i++;
			}
			echo "</desired_selection>";
			
			$starting_path = tep_get_level_name_path($data_pool_level_id, ' > ');
			$level_tree_array = array();
			tep_get_catalog_datapool_subtree_array($data_pool_level_id, $level_tree_array, 0, $starting_path);
			
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_start_level);
			echo "</class_selection>";
			
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, 0);
			if (count($option_html_array)) {
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr>
      								<td colspan="2">
      									<div id="custom_bracket_tags_div"></div>
      								</td>
      							</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
						$field_resource = tep_draw_option_field($option_key, $option_res);
						
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$option_res["data_pool_options_title"].'</td>
										<td valign="top" class="inputField">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
			}
			echo "]]></option_selection>";
			echo '</response>';
			
			break;
		case "end_level_task":
			if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "order_cp_info.php")) {
				include_once(DIR_WS_LANGUAGES . $language . '/' . "order_cp_info.php");
			}
			
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_start_level = (int)$HTTP_GET_VARS['s_level'];
			$sel_end_level = (int)$HTTP_GET_VARS['e_level'];
			$sel_current_class = (int)$HTTP_GET_VARS['class_root_id'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $data_pool_level_id . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
			
			$starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$level_tree_array = array();
			tep_get_catalog_datapool_subtree_array($sel_current_class, $level_tree_array, 0, $starting_path);
			
			if ($sel_current_class > 0 && $sel_current_class != $data_pool_level_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$level_tree_array = array (	array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
												   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
														'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
														'name' => $data_pool_level_row['data_pool_level_name'], 
														'ident' => 0, 
														'path' => $starting_path,
														'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
														'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
														'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
														'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
														'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
														'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
														'child' => $level_tree_array)
											);
				}
			}
			
			echo '<response>';
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_end_level, $sel_current_class);
			echo "</class_selection>";
			
			tep_calculate_bracket_price($data_pool_level_id, $sel_start_level, $sel_end_level, $price, $eta, $msg, $custom_tags);
			
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, $sel_end_level);
			if (count($option_html_array)) {
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr>
      								<td colspan="2">
      									<div id="custom_bracket_tags_div">';
      					
		      			if (count($custom_tags) > 0) {
							$custom_bracket_tags_html = '<table width="100%" cellspacing="0" cellpadding="0">';
			  				foreach ($custom_tags as $custom_bracket_tags_res) {
			  					$custom_bracket_tags_html .= 
			  									'<tr>
													<td width="40%" valign="top" class="inputLabel">'.$custom_bracket_tags_res["display_label"].'</td>
													<td valign="top" class="inputField">'.$custom_bracket_tags_res["value"].'</td>
												</tr>';
			  				}
			  				$custom_bracket_tags_html .= '</table>';
			  				echo $custom_bracket_tags_html;
						}
						
						echo '			</div>
									</td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
						$field_resource = tep_draw_option_field($option_key, $option_res);
						
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$option_res["data_pool_options_title"].'</td>
										<td valign="top" class="inputField">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
			}
			echo "]]></option_selection>";
			
			echo "<bracket_info>";
			if (trim($price) != '')		echo "<price><![CDATA[$price]]></price>";
			if (trim($eta) != '')		echo "<eta><![CDATA[$eta]]></eta>";
			if (trim($msg) != '')		echo "<msg><![CDATA[$msg]]></msg>";
			echo "</bracket_info>";
			
			echo '</response>';
			
			break;
		case "class_task":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_end_level = (int)$HTTP_GET_VARS['e_level'];
			$sel_current_class = (int)$HTTP_GET_VARS['class_root_id'];
			
			$starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$level_tree_array = array();
			tep_get_catalog_datapool_subtree_array($sel_current_class, $level_tree_array, 0, $starting_path);
			
			if ($sel_current_class > 0 && $sel_current_class != $data_pool_level_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
												   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
														'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
														'name' => $data_pool_level_row['data_pool_level_name'], 
														'ident' => 0, 
														'path' => $starting_path,
														'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
														'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
														'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
														'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
														'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
														'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
														'child' => $level_tree_array)
											);
				}
			}
			
			echo '<response>';
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_end_level, $sel_current_class);
			echo "</class_selection>";
			echo '</response>';
			
			break;
		case "list_global_options":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $data_pool_level_id . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
		    
		    echo '<response>';
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, 0);
			if (count($option_html_array)) {
				$preload_price_eta_js = "\nvar cp_option_price = new Array();\n" . 
										"var cp_option_eta = new Array();\n";
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
						$field_resource = tep_draw_option_field($option_key, $option_res);
						
						if (tep_not_null($field_resource["js"])) {
							$preload_price_eta_js .= $field_resource["js"];
						}
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$option_res["data_pool_options_title"].'</td>
										<td valign="top" class="inputLabel">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
				echo 	"\n<script>" . $preload_price_eta_js . "</script>";
			}
			echo "]]></option_selection>";
			echo '</response>';
			
			break;
		case 'get_supplier_info':
			$search_value = $_GET['search_val'];
			
			$user_select_sql = "SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_code AS scode, supplier_email_address AS email, supplier_id 
								FROM " . TABLE_SUPPLIER . " AS s 
								WHERE supplier_firstname LIKE '%" . tep_db_input($search_value) . "%' 
									OR supplier_lastname LIKE '%" . tep_db_input($search_value) . "%' 
									OR supplier_code LIKE '%" . tep_db_input($search_value) . "%' 
									OR supplier_email_address LIKE '%" . tep_db_input($search_value) . "%' 
								ORDER BY s.supplier_firstname";
			$user_result_sql = tep_db_query($user_select_sql);
			echo "<selection>";
			$rec_cnt = 0;
			while ($user_row = tep_db_fetch_array($user_result_sql)) {
				if ($rec_cnt < 250) {
					echo "<option><![CDATA[".'first_name='.$user_row['fname'].':~:last_name='.$user_row['lname'].':~:supplier_code='.$user_row['scode'].':~:email='.$user_row['email'].':~:supplier_id='.$user_row['supplier_id']."]]></option>";
				} else {
					break;
				}
				$rec_cnt++;
			}
			echo "</selection>";
			break;
		default:
			echo "<result>Unknown request!</result>";
			
			break;
	}
}

function generateTagSelectionOptions($status, $order_pid_str, $whole_list=false, $apply_tag_sec_only = false) {
	global $language_id;
	$order_ids_array = tep_not_null($order_pid_str) ? explode(',', $order_pid_str) : array();
	echo "<selection>";
	$supplier_tasks_status_id_select_sql = "SELECT supplier_tasks_status_id FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE " . (is_numeric($status) ? "supplier_tasks_status_id = '" . (int)$status . "'" : "orders_status_name = '" . tep_db_input($status) . "'") . " AND language_id = '" . $language_id . "';";
	$supplier_tasks_status_id_result_sql = tep_db_query($supplier_tasks_status_id_select_sql);
	if ($supplier_tasks_status_id_row = tep_db_fetch_array($supplier_tasks_status_id_result_sql)) {
		if (!$apply_tag_sec_only) {
			echo "<option index=''><![CDATA[Progress Report Lists Options ...]]></option>";
			/*
			if ($whole_list == true) {
				echo "<option index='rd'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
				echo "<option index='ur'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
			} else {
				$orders_read_mode_select_sql = "SELECT DISTINCT orders_read_mode FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id IN (".(count($order_ids_array) ? implode(',', $order_ids_array) : '')."); ";
				$orders_read_mode_result_sql = tep_db_query($orders_read_mode_select_sql);
				if (tep_db_num_rows($orders_read_mode_result_sql) > 0) {
					$show_read = $show_unread = false;
					while ($orders_read_mode_row = tep_db_fetch_array($orders_read_mode_result_sql)) {
						if ($orders_read_mode_row["orders_read_mode"] == 1) {
							$show_unread = true;
						} else if ($orders_read_mode_row["orders_read_mode"] == 0) {
							$show_read = true;
						}
					}
				}
				if (!$show_read && !$show_unread) {
					echo "<option index='rd' disabled='1'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
					echo "<option index='ur' disabled='1'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
				} else {
					if ($show_read)		echo "<option index='rd'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
					if ($show_unread)	echo "<option index='ur'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
				}
			}*/
		}
		
		echo "<option index='' ".(!$apply_tag_sec_only ? "disabled='1'" : '')."><![CDATA[Apply tag:]]></option>";
		
		$mirror_for_delete_tag_str = '';
		$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$supplier_tasks_status_id_row["supplier_tasks_status_id"]."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."' ORDER BY orders_tag_name;";
		$order_tag_result_sql = tep_db_query($order_tag_select_sql);
		while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
			echo "<option index='".'otag_'.$order_tag_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$order_tag_row["orders_tag_name"]."]]></option>";
			if ($whole_list == true) {
				$mirror_for_delete_tag_str .= "<option index='".'rmtag_'.$order_tag_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$order_tag_row["orders_tag_name"]."]]></option>";
			}
		}
		
		if (!$apply_tag_sec_only) {
			echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";
			
			if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
				echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
				echo $mirror_for_delete_tag_str;
			} else {
				// select the common tags among those selected orders
				if (count($order_ids_array)) {
					$orders_tag_remove_select_sql = "SELECT DISTINCT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta WHERE orders_products_id IN (".implode(',', $order_ids_array).") AND FIND_IN_SET(otag.orders_tag_id, sta.orders_tag_ids) AND filename='".FILENAME_PROGRESS_REPORT."' ORDER BY orders_tag_name; ";
					$orders_tag_remove_result_sql = tep_db_query($orders_tag_remove_select_sql);
					if (tep_db_num_rows($orders_tag_remove_result_sql) > 0)
						echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
					while ($orders_tag_remove_row = tep_db_fetch_array($orders_tag_remove_result_sql)) {
						echo "<option index='".'rmtag_'.$orders_tag_remove_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$orders_tag_remove_row["orders_tag_name"]."]]></option>";
					}
				}
			}
		}
	}
	echo "</selection>";
}

function generateTagString($order_ids_array) {
	echo "<tag_details>";
	for ($i=0; $i < count($order_ids_array); $i++) {
		$orders_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta WHERE sta.orders_products_id  = '" . (int)$order_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, sta.orders_tag_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
		$orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
		$tags_str = '';
		while ($orders_tag_row = tep_db_fetch_array($orders_tag_result_sql)) {
			$tags_str .= $orders_tag_row["orders_tag_name"] . ', ';
		}
		if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
		echo "<order_tags order_id='".(int)$order_ids_array[$i]."'><![CDATA[".$tags_str."]]></order_tags>";
	}
	echo "</tag_details>";
}

function tep_display_selection_option($level_tree_array, $selected_level) {
	for($i=0; $i < count($level_tree_array); $i++) {
		if ($level_tree_array[$i]["data_pool_min_level"] <= $selected_level && $level_tree_array[$i]["data_pool_max_level"] >= $selected_level || 
			$level_tree_array[$i]["data_pool_min_level"] == 0 && $level_tree_array[$i]["data_pool_max_level"] == 0) {
				echo "<option index='".$level_tree_array[$i]["id"]."'><![CDATA[".$level_tree_array[$i]["path"]."]]></option>";
		}
		if (isset($level_tree_array[$i]["child"]) && count($level_tree_array[$i]["child"])) {
			tep_display_selection_option($level_tree_array[$i]["child"], $selected_level);
		}
	}
}
?>