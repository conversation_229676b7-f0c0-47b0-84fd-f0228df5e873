<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml; charset=utf-8');

include_once('includes/application_top.php');

include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_FUNCTIONS . 'admin_members.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');
include_once(DIR_WS_CLASSES . 'g2g_serverless.php');


$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$country_id = isset($_GET['country_id']) ? (int) $_GET['country_id'] : '';
$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$telephone = isset($_GET['telephone']) ? $_GET['telephone'] : '';
$customer_id = isset($_REQUEST['customer_id']) ? (int) $_REQUEST['customer_id'] : '';
$code_received = isset($_GET['code']) ? $_GET['code'] : '';
$order_id = isset($_GET['order_id']) ? (int) $_GET['order_id'] : '';
$doc_id = isset($_POST['doc_id']) ? $_POST['doc_id'] : '';
$title = isset($_POST['title']) ? $_POST['title'] : '';
$customer_email = isset($HTTP_GET_VARS['customer_email']) ? $HTTP_GET_VARS['customer_email'] : '';
$edit_discount_box = null;

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

echo '<response>';
if (tep_not_null($action)) {
    switch ($action) {
        case "popup_remark":
            echo "<popup_remark><result>OK</result></popup_remark>";
            break;
            
        case "submit_extra_op":
            if (isset($_REQUEST['pmid']) && (int) $_REQUEST['pmid'] > 0 && isset($_REQUEST['id']) && (int) $_REQUEST['id'] > 0) {
                if (isset($_REQUEST['txt_all'])) {
                    $customers_groups_extra_op_data_sql = array();

                    $delete_customers_groups_extra_op_sql = "	DELETE FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . "
																WHERE payment_methods_id = '" . (int) $_REQUEST['pmid'] . "'
																	AND customers_groups_discount_id = '" . (int) $_REQUEST['id'] . "'";
                    tep_db_query($delete_customers_groups_extra_op_sql);

                    $customers_groups_extra_op_data_sql['currency'] = '*';
                    $customers_groups_extra_op_data_sql['bonus_op'] = (double) (( isset($_REQUEST['sel_all']) ? $_REQUEST['sel_all'] : '-' ) . $_REQUEST['txt_all']);
                    $customers_groups_extra_op_data_sql['payment_methods_id'] = (int) $_REQUEST['pmid'];
                    $customers_groups_extra_op_data_sql['customers_groups_discount_id'] = (int) $_REQUEST['id'];
                    tep_db_perform(TABLE_CUSTOMERS_GROUPS_EXTRA_OP, $customers_groups_extra_op_data_sql);
                } else if (isset($_REQUEST['txt_extra_op']) && count($_REQUEST['txt_extra_op'])) {

                    $delete_customers_groups_extra_op_sql = "	DELETE FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . "
																WHERE payment_methods_id = '" . (int) $_REQUEST['pmid'] . "'
																	AND customers_groups_discount_id = '" . (int) $_REQUEST['id'] . "'
																	AND currency = '*'";
                    tep_db_query($delete_customers_groups_extra_op_sql);

                    foreach ($_REQUEST['txt_extra_op'] as $currency_loop => $op_loop) {
                        $customers_groups_extra_op_data_sql = array();

                        if (is_numeric($currency_loop) && (int) $currency_loop > 0) {
                            $customers_groups_extra_op_data_sql['bonus_op'] = (double) (isset($_REQUEST['sel_extra_operator'][$currency_loop]) ? $_REQUEST['sel_extra_operator'][$currency_loop] : '-') . $op_loop;
                            tep_db_perform(TABLE_CUSTOMERS_GROUPS_EXTRA_OP, $customers_groups_extra_op_data_sql, 'update', " customers_groups_extra_op_id = '" . (int) $currency_loop . "' ");
                        } else {
                            $customers_groups_extra_op_data_sql['currency'] = tep_db_prepare_input($currency_loop);
                            $customers_groups_extra_op_data_sql['payment_methods_id'] = (int) $_REQUEST['pmid'];
                            $customers_groups_extra_op_data_sql['customers_groups_discount_id'] = (int) $_REQUEST['id'];
                            $customers_groups_extra_op_data_sql['bonus_op'] = (double) (isset($_REQUEST['sel_extra_operator'][$currency_loop]) ? $_REQUEST['sel_extra_operator'][$currency_loop] : '-') . $op_loop;
                            tep_db_perform(TABLE_CUSTOMERS_GROUPS_EXTRA_OP, $customers_groups_extra_op_data_sql);
                        }
                    }
                }
            }
            break;
        case "load_customers_groups_extra_op":
            if (isset($_REQUEST['id']) && (int) $_REQUEST['id'] > 0) {
                $payment_methods_id = (int) $_REQUEST['pmid'];
                $customers_groups_discount_id = (int) $_REQUEST['id'];
                $payment_methods_select_sql = "	SELECT pm.payment_methods_title, pm.payment_methods_id, pm.payment_methods_parent_id
												FROM " . TABLE_PAYMENT_METHODS . " AS pm
												WHERE pm.payment_methods_id = '" . (int) $payment_methods_id . "'";
                $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                $payment_methods_row = tep_db_fetch_array($payment_methods_result_sql);

                echo "	<title><![CDATA[" . $payment_methods_row['payment_methods_title'] . "]]></title>";
                echo "	<currencies>";

                $currencies_select_sql = "	SELECT pmi.currency_code
											FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " AS pmi
											WHERE pmi.payment_methods_id = '" . $payment_methods_id . "'
											ORDER BY pmi.currency_code";
                $currencies_result_sql = tep_db_query($currencies_select_sql);
                if (!tep_db_num_rows($currencies_result_sql)) {
                    $currencies_select_sql = "	SELECT pmi.currency_code
												FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " AS pmi
												WHERE pmi.payment_methods_id = '" . $payment_methods_row['payment_methods_parent_id'] . "'
												ORDER BY pmi.currency_code";
                    $currencies_result_sql = tep_db_query($currencies_select_sql);
                }
                $currencies_array = array();
                while ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
                    $currencies_array[$currencies_row['currency_code']] = array();
                }

                $selected_for_all_select_sql = "SELECT cgeo.payment_methods_id, cgeo.bonus_op, cgeo.customers_groups_extra_op_id
												FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . " AS cgeo
												WHERE cgeo.currency = '*'
													AND cgeo.payment_methods_id IN ('" . $payment_methods_row['payment_methods_id'] . "','" . $payment_methods_row['payment_methods_parent_id'] . "')
													AND cgeo.customers_groups_discount_id = '" . (int) $customers_groups_discount_id . "' ";
                $selected_for_all_result_sql = tep_db_query($selected_for_all_select_sql);
                if ($selected_for_all_row = tep_db_fetch_array($selected_for_all_result_sql)) {
                    preg_match("/([0-9\.]+)/i", $selected_for_all_row['bonus_op'], $bonus_op);
                    echo "	<currency code='*' id='" . (int) $selected_for_all_row['customers_groups_extra_op_id'] . "' operator='" . ($selected_for_all_row['bonus_op'] < 0 ? '-' : '+') . "'><![CDATA[" . number_format($bonus_op[1], 2, '.', '') . "]]></currency>";
                }

                $customers_groups_extra_op_select_sql = "	SELECT cgeo.currency, cgeo.customers_groups_extra_op_id, cgeo.bonus_op
															FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . " AS cgeo
															WHERE cgeo.payment_methods_id = '" . $payment_methods_id . "'
																AND cgeo.customers_groups_discount_id = '" . (int) $customers_groups_discount_id . "'
																AND cgeo.currency IN ('" . implode("','", array_keys($currencies_array)) . "')";
                $customers_groups_extra_op_result_sql = tep_db_query($customers_groups_extra_op_select_sql);
                while ($customers_groups_extra_op_row = tep_db_fetch_array($customers_groups_extra_op_result_sql)) {
                    $currencies_array[$customers_groups_extra_op_row['currency']] = array('customers_groups_extra_op_id' => $customers_groups_extra_op_row['customers_groups_extra_op_id'],
                        'bonus_op' => $customers_groups_extra_op_row['bonus_op']);
                }
                reset($currencies_array);
                foreach ($currencies_array as $currencies_loop => $data_loop) {
                    preg_match("/([0-9\.]+)/i", $data_loop['bonus_op'], $bonus_op);
                    echo "	<currency id='" . (int) $data_loop['customers_groups_extra_op_id'] . "' code='" . $currencies_loop . "' operator='" . ($data_loop['bonus_op'] < 0 ? '-' : '+') . "'><![CDATA[" . number_format($bonus_op[1], 2, '.', '') . "]]></currency>";
                }

                echo "	</currencies>";
            }
            break;
        case "state_list":
            $zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $country_id . "' ORDER BY zone_name";
            $zones_result_sql = tep_db_query($zones_select_sql);

            echo "<selection>";
            while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
                echo "<option index='" . $zones_row['zone_id'] . "'><![CDATA[" . $zones_row['zone_name'] . "]]></option>";
            }
            echo "</selection>";

            break;
        case "refresh_country_code":
            $country_dialing_select_sql = "select countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id ='" . $country_id . "'";
            $country_dialing_result_sql = tep_db_query($country_dialing_select_sql);
            $country_dialing_row = tep_db_fetch_array($country_dialing_result_sql);
            echo "<country_dialing_code>";
            echo $country_dialing_row['countries_international_dialing_code'];
            echo "</country_dialing_code>";

            break;
        case "get_customer_info":
            $search_by = $_GET['search_by'];
            $search_value = $_GET['search_val'];

            if ($search_by == 'supplier_email_address') {
                $user_select_sql = "SELECT s.supplier_firstname AS fname, s.supplier_lastname AS lname, s.supplier_email_address AS email
									FROM " . TABLE_SUPPLIER . " AS s
									WHERE " . $search_by . " LIKE '%" . tep_db_input($search_value) . "%'
									ORDER BY s.supplier_firstname";
            } else {
                $user_select_sql = "SELECT c.customers_firstname AS fname, c.customers_lastname AS lname, c.customers_email_address AS email
									FROM " . TABLE_CUSTOMERS . " AS c
									WHERE " . $search_by . " LIKE '%" . tep_db_input($search_value) . "%'
									ORDER BY c.customers_firstname";
            }

            $user_result_sql = tep_db_query($user_select_sql);
            echo "<selection>";
            $rec_cnt = 0;
            while ($user_row = tep_db_fetch_array($user_result_sql)) {
                if ($rec_cnt < 250) {
                    echo "<option><![CDATA[" . 'first_name=' . $user_row['fname'] . ':~:last_name=' . $user_row['lname'] . ':~:email=' . $user_row['email'] . "]]></option>";
                } else {
                    break;
                }
                $rec_cnt++;
            }
            echo "</selection>";
            break;
        case "get_og_customer_online_status":
            echo '<status>';
            if (tep_session_active_customer($customer_id)) {
                echo TEXT_ONLINE;
            } else {
                echo TEXT_OFFLINE;
            }
            echo '</status>';
            break;

        case "get_customer_2fa_status":
            echo '<status>';
            if (tep_has_2fa($customer_id)) {
                echo 'Enabled';
            } else {
                echo 'Disabled';
            }
            echo '</status>';
            break;
        case "get_state":
            $state_list_array = '';
            $country_id = $HTTP_GET_VARS['country_id'];

            $state_select_sql = "SELECT zone_id, zone_name
		                         FROM " . TABLE_ZONES . "
		                         WHERE zone_country_id = '" . (int) $country_id . "'";
            $state_result_sql = tep_db_query($state_select_sql);

            if (tep_db_num_rows($state_result_sql) > 0) {
                while ($state_row = tep_db_fetch_array($state_result_sql)) {
                    $state_list_array[] = array('id' => $state_row['zone_id'],
                        'text' => $state_row['zone_name']
                    );
                }
            } else {
                $state_list_array[] = array('id' => '',
                    'text' => TEXT_SELECT_NO_STATE
                );
            }

            echo '<state>';
            echo tep_draw_pull_down_menu('state_id[]', $state_list_array, '', 'multiple="multiple" size="9" style="width:20em;"');
            echo '</state>';
            break;
        case 'kick_customer':
            $result = tep_kick_customer($customer_id);
            echo "<status>" . (int)(isset($result["status"]) ? $result["status"] : false) . "</status>";
            break;
        case 'disable_customer_2fa':
            $result = tep_disabled_2fa($customer_id);
            echo "<status>" . (int)(isset($result["status"]) ? $result["status"] : false) . "</status>";
            break;
        case 'get_customer_group_discount':
            if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOMERS_GROUPS)) {
                include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOMERS_GROUPS);
            }

            $customers_groups_id = $_GET["customers_groups_id"];
            $grp_discount_select_sql = "SELECT gd.customers_groups_discount_id, gd.categories_id, gd.customers_groups_discount, gd.customers_groups_rebate,
                                            gd.c2c_customers_groups_discount, gd.c2c_customers_groups_rebate
                                        FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd
                                        LEFT JOIN " . TABLE_CATEGORIES . " AS c
                                            ON gd.categories_id=c.categories_id
                                        LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                            ON (c.categories_id=cd.categories_id AND cd.language_id = '" . (int) $languages_id . "')
                                        WHERE gd.customers_groups_id = '" . (int) $customers_groups_id . "'
                                        ORDER BY c.sort_order, cd.categories_name";
            $grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
            if (tep_db_num_rows($grp_discount_result_sql) > 0) {
                while ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
                    if (tep_check_cat_tree_permissions(FILENAME_CUSTOMERS_GROUPS, $grp_discount_row['categories_id']) != 1) {
                        $full_group_cat_access = false;
                        continue;
                    }
                    if ($grp_discount_row['categories_id'] == 0) {
                        $cat_name = '[Top]';
                    } else {
                        $cat_name = tep_output_generated_category_path_sq($grp_discount_row['categories_id'], 'category');
                    }
                    $safe_grp_discount_name = htmlspecialchars(addslashes($cat_name), ENT_QUOTES);

                    $edit_discount_permission = tep_admin_group_edit_discount_permission($customers_groups_id);
                    if ($edit_discount_permission) {
                        $edit_discount_box = '	<a href="' . tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction')) . 'action=edit_discount&cID=' . (int) $customers_groups_id . '&dis_id=' . $grp_discount_row['customers_groups_discount_id']) . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') . '</a>
												<a href="javascript:void(confirm_delete(\'' . $safe_grp_discount_name . '\', \'' . TABLE_HEADING_DISCOUNT . '\', \'' . tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction')) . 'subaction=delete_discount&cID=' . (int) $customers_groups_id . '&dis_id=' . $grp_discount_row['customers_groups_discount_id']) . '\'))">' . tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') . '</a>';
                    }
                    echo '<customer_group_discount_info><![CDATA[';
                    echo '<tr>
											<td class="reportRecords" valign="top" align="center" width="12%" nowrap>
											' . $edit_discount_box . '
											</td>
											<td class="reportRecords" valign="top">' . $cat_name . '</td>
											<td class="reportRecords" valign="top" width="15%">' . $grp_discount_row["customers_groups_discount"] . '</td>
											<td class="reportRecords" valign="top" width="15%">' . $grp_discount_row["customers_groups_rebate"] . '</td>
											<td class="reportRecords" valign="top" width="15%">' . $grp_discount_row["c2c_customers_groups_discount"] . '</td>
											<td class="reportRecords" valign="top" width="15%">' . $grp_discount_row["c2c_customers_groups_rebate"] . '</td>
										</tr>';
                    echo ']]></customer_group_discount_info>';
                }
            } else {
                echo '<customer_group_discount_info><![CDATA[';
                echo '<tr>
											<td colspan="3" class="reportRecords" valign="top" align="center">' . TEXT_NO_DISCOUNT_DEFINE . '</td>
										</tr>';
                echo ']]></customer_group_discount_info>';
            }


            break;
        case 'get_archive_content':
            echo '<logs>';
            if (tep_admin_files_actions(FILENAME_CUSTOMERS, 'CUSTOMER_VIEW_ALL_DOC') && tep_not_null($customer_id) && tep_not_null($doc_id)) {
                $log_email = '';
                $customer_info_select_sql = "	SELECT log_id, log_users_id, log_users_type, log_IP, log_datetime, log_filename, log_action
												FROM " . TABLE_VERIFICATION_DOC_LOG . "
												WHERE log_customers_id=" . $customer_id . " AND log_docs_id='" . $doc_id . "'
													AND (log_action = 'UPLOAD' OR log_action = 'MODIFY')
												ORDER BY log_datetime desc";
                $customers_query = tep_db_query($customer_info_select_sql);
                while ($vlog = tep_db_fetch_array($customers_query)) {
                    if ($vlog['log_users_type'] == 'admin') {
                        if ((int) $vlog['log_users_id'] > 0) {
                            $admin_email_select_sql = "	SELECT admin_email_address
														FROM " . TABLE_ADMIN . "
														WHERE admin_id='" . $vlog['log_users_id'] . "'";
                            $admin_email_select_result = tep_db_query($admin_email_select_sql);
                            $admin_email_select_row = tep_db_fetch_array($admin_email_select_result);
                            $log_email = $admin_email_select_row['admin_email_address'];
                        } else {
                            $log_email = $vlog['log_users_id'];
                        }
                    } else {
                        $log_email = strtoupper($vlog['log_users_type']);
                    }

                    // re add the extension to detect non image in customers.php
                    //list($vf_filename, ) = explode(".", $vlog['log_filename']);
                    $vf_filename = $vlog['log_filename'];

                    echo "<log id='" . $vlog['log_id'] . "' ip='" . $vlog['log_IP'] . "' filename='" . $vf_filename . "'><![CDATA[" . $vlog['log_datetime'] . '. <br>' . $vlog['log_action'] . ' by ' . $log_email . "]]></log>";
                }

                if (tep_not_null($log_email)) {
                    $insert_data_array = array('log_users_id' => $_SESSION['login_id'],
                        'log_users_type' => 'admin',
                        'log_customers_id' => $customer_id,
                        'log_docs_id' => $doc_id,
                        'log_IP' => tep_get_ip_address(),
                        'log_datetime' => 'now()',
                        'log_filename' => $title,
                        'log_action' => 'VIEW ARCHIVE');
                    tep_db_perform(TABLE_VERIFICATION_DOC_LOG, $insert_data_array);
                    unset($insert_data_array);
                }
            }
            echo '</logs>';
            break;

        case "view_shasso_security_code":
            if (isset($_GET["api_action"])) {
                include_once(DIR_WS_CLASSES . 'curl.php');

                $curl_obj = new curl();
                $data = array(
                    'merchant' => HTTP_SHASSO_CLIENT_ID,
                    'signature' => md5($customer_id . "|" . HTTP_SHASSO_CLIENT_SECRET),
                    'action' => $_GET["api_action"],
                    'cid' => $customer_id,
                    'otp_key' => $_GET["otp_key"]
                );
                $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/secureCode", $data);
                $result = json_decode($curl_resp, true);

                echo "<status>" . $result["status"] . "</status>";
                echo "<result>";
                foreach ($result["result"][0] as $key => $val) {
                    echo "<$key>$val</$key>";
                }
                echo "</result>";
                echo "<error>" . $result["error"] . "</error>";
            }
            break;
            
        case "remove_aft_whitelist":
            $check_flag_permission = tep_admin_files_actions(FILENAME_CUSTOMERS, 'CUSTOMER_CHECK_FLAG');
            if ($check_flag_permission) {
                if (isset($_REQUEST["customer_id"])) {
                    $delete_aft_sql = "	DELETE FROM " . TABLE_CUSTOMERS_SETTING . "
							WHERE customers_id = '" . (int) $_REQUEST['customer_id'] . "'
							AND  customers_setting_key = 'aft_genesis_whitelisted'";
                    if (tep_db_query($delete_aft_sql)) {
                        echo '<result>1</result>';
                    } else {
                        echo '<result>0</result>';
                    }
                }
            }

            break;

            case 'get_og_storecredit_op':
                $og_sc = "NA";
                $og_op = "NA";
    
                // store credit
                $sc_balance_array = ms_store_credit::getScBalance($customer_id);
                $scAccountStatus = ms_store_credit::getScAccountStatus($customer_id);
                if ($sc_balance_array) {
                    $og_sc = '<a href="' . CREW2_PATH . '/store-credit/index?report_type=1&customers_id=' . $customer_id . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, date("m"), date("d") - 7, date("Y")))) . '&end_date=' . date('Y-m-d') . '" target="_blank">' . $currencies->format($sc_balance_array['amount'], false, $sc_balance_array['currency']) . '</a> (' . (($scAccountStatus) ? "ACTIVE" : "SUSPENDED") . ')';
                }
    
                include_once(DIR_WS_CLASSES . 'store_point.php');
                $sp_balance_array = store_point::get_current_points_balance($customer_id);
                if (isset($sp_balance_array['sp_amount'])) {
                    $og_op = '<a href="' . tep_href_link(FILENAME_STORE_POINT, 'action=show_report&customer_id=' . $customer_id . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - 7, date('Y')))), 'SSL') . '" target="_blank">' . $sp_balance_array['sp_amount'] . '</a>';
                }
    
                echo '  <stat_result>
                            <og_storecredit><![CDATA[' . $og_sc . ']]></og_storecredit>
                            <og_op><![CDATA[' . $og_op . ']]></og_op>
                        </stat_result>';
                break;
            case 'get_pipwave_name_check':
                //Make sure name and DOB locked before can call name scanning
                $verify_customer_name_select = "SELECT name_lock FROM " . TABLE_CUSTOMERS_DOCUMENT_ID_VERIFICATION . " WHERE customers_id='" . (int) $customer_id . "'";
                $verify_customer_name_sql = tep_db_query($verify_customer_name_select);
                $name_locked = false;
                if ($verify_customer_name_row = tep_db_fetch_array($verify_customer_name_sql)) {
                    if($verify_customer_name_row['name_lock']){
                        $name_locked = true;
                    }
                }
                $customer_dob_select = "SELECT locked FROM " . TABLE_CUSTOMER_INFO_LOCK . " WHERE customer_id='" . (int) $customer_id . "' AND info_key = 'customers.customers_dob'";
                $customer_dob_lock_sql = tep_db_query($customer_dob_select);
                $dob_locked = false;
                if ($customer_dob_row = tep_db_fetch_array($customer_dob_lock_sql)) {
                    if($customer_dob_row['locked']){
                        $dob_locked = true;
                    }
                }
                if (!$name_locked) {
                    echo " <stat_result>
                        <name_screen><![CDATA[Please check and lock customer's name before performing Live Name Screening]]></name_screen>
                        </stat_result>'"; 
                    break;
                }
                if (!$dob_locked) {
                    echo " <stat_result>
                        <name_screen><![CDATA[Please check and lock customer's DOB (date of birth) before performing Live Name Screening]]></name_screen>
                        </stat_result>'"; 
                    break;
                }
                $files003_select_sql = "SELECT files_003 FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " WHERE customers_id='" . (int) $customer_id . "'";
                $files003_result_sql = tep_db_query($files003_select_sql);
                $files_003_row = tep_db_fetch_array($files003_result_sql);
                if ($files_003_row && !empty($files_003_row['files_003']) && strpos($files_003_row['files_003'], '_og_') !== false) {
                    //OG
                    $pipwave = new pipwave(null ,0);
                } else {
                    //G2G
                    $pipwave = new pipwave(null ,5);
                }
                $result = $pipwave->newNameCheck($customer_id);
                if(isset($result) && ($result['status'] == 200)){
                    $saved_result = $pipwave->saveNameScreenResult($customer_id, $result);
                } else if(isset($result) && ($result['status'] == 500)){
                    g2g_serverless::g2g_slack('Name screen error for user'.$customer_id);
                }
                if ($result && $result['status'] == 200) {
                    $content =  'Last verified date: ' . $saved_result['name_verification_last_name_check_called'] . ' Match count: '. $saved_result['name_verification_match_count'];
                } elseif($result['status'] == 9001){
                    $content  = 'Error: ' . $result['message'];       
                } else {
                    $content = 'Error';
                }
            echo " <stat_result>
                    <name_screen><![CDATA[$content]]></name_screen>
                    </stat_result>'"; 
            break;

        default:
            echo "<result>Unknown request!</result>";

            break;
    }
}

echo '</response>';
?>