<?
/*
  	$Id: customers_acc_merging.php,v 1.10 2008/06/11 04:22:38 boonhock Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture
  	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$debug_mode = false;

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "step_2":
			$error = false;
			
			$from_customer_email = tep_db_prepare_input($HTTP_POST_VARS['from_account_email']);
			$to_customer_email = tep_db_prepare_input($HTTP_POST_VARS['to_account_email']);
			
			if (!tep_not_null($from_customer_email)) {
				$messageStack->add_session(ERROR_MISSING_SOURCE_CUSTOMER, 'error');
				$error = true;
			}
			
			if (!tep_not_null($to_customer_email)) {
				$messageStack->add_session(ERROR_MISSING_DESTINATION_CUSTOMER, 'error');
				$error = true;
			}
			
			if (!$error) {
				$from_customer_select_sql = "	SELECT c.*, a.*, country.countries_name 
												FROM " . TABLE_CUSTOMERS . " AS c 
												LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS a 
													ON (c.customers_id=a.customers_id AND c.customers_default_address_id = a.address_book_id) 
												LEFT JOIN " . TABLE_COUNTRIES . " AS country 
													ON (a.entry_country_id=country.countries_id) 
												WHERE c.customers_email_address = '" . tep_db_input($from_customer_email) . "'";
	        	$from_customer_result_sql = tep_db_query($from_customer_select_sql);
			
	        	if ($from_customer_row = tep_db_fetch_array($from_customer_result_sql)) {
		            $cFromInfo = new objectInfo($from_customer_row);
		            if ($cFromInfo->customers_status != '1') {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_ACTIVE, $from_customer_email), 'error');
						$error = true;
		            } else if(strstr(','.$cFromInfo->customers_login_sites.',', ',1,') || strstr(','.$cFromInfo->customers_login_sites.',', ',2,')) {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_FROM_BUYBACK_OR_AFFILIATE, $from_customer_email), 'error');
						$error = true;
		            }
		        } else {
		        	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_EXISTS, $from_customer_email), 'error');
					$error = true;
		        }
		        
		        $to_customer_select_sql = "	SELECT c.*, a.*, country.countries_name 
											FROM " . TABLE_CUSTOMERS . " AS c 
											LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS a 
												ON (c.customers_id=a.customers_id AND c.customers_default_address_id = a.address_book_id) 
											LEFT JOIN " . TABLE_COUNTRIES . " AS country 
												ON (a.entry_country_id=country.countries_id) 
											WHERE c.customers_email_address = '" . tep_db_input($to_customer_email) . "'";
	        	$to_customer_result_sql = tep_db_query($to_customer_select_sql);
				
	        	if ($to_customer_row = tep_db_fetch_array($to_customer_result_sql)) {
		            $cToInfo = new objectInfo($to_customer_row);
		            
		            if ($cToInfo->customers_status != '1') {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_ACTIVE, $to_customer_email), 'error');
						$error = true;
		            } else if(strstr(','.$cToInfo->customers_login_sites.',', ',1,') || strstr(','.$cToInfo->customers_login_sites.',', ',2,')) {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_FROM_BUYBACK_OR_AFFILIATE, $to_customer_email), 'error');
						$error = true;
		            }
		        } else {
		        	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_EXISTS, $to_customer_email), 'error');
					$error = true;
		        }
		        
		        if ($from_customer_email == $to_customer_email) {
		        	$messageStack->add_session(ERROR_SAME_CUSTOMERS, 'error');
					$error = true;
		        }
	        }
	        
			if ($error) {
				tep_redirect(tep_href_link(FILENAME_CUSTOMERS_ACC_MERGING, tep_get_all_get_params(array('action')) . 'action=step_1'));
			}
			
			break;
		case "step_3";
			$error = false;
			
			$from_customer_id = tep_db_prepare_input($HTTP_POST_VARS['from_customer_id']);
			$to_customer_id = tep_db_prepare_input($HTTP_POST_VARS['to_customer_id']);
			$from_customer_email = $to_customer_email = '';
			
			if (!tep_not_null($from_customer_id)) {
				$messageStack->add_session(ERROR_MISSING_SOURCE_CUSTOMER, 'error');
				$error = true;
			}
			
			if (!tep_not_null($to_customer_id)) {
				$messageStack->add_session(ERROR_MISSING_DESTINATION_CUSTOMER, 'error');
				$error = true;
			}
			
			if (!$error) {
				$from_customer_select_sql = "	SELECT customers_email_address, customers_status
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
	        	$from_customer_result_sql = tep_db_query($from_customer_select_sql);
				
	        	if ($from_customer_row = tep_db_fetch_array($from_customer_result_sql)) {
		            $from_customer_email = $from_customer_row['customers_email_address'];
		            
		            if ($from_customer_row['customers_status'] != '1') {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_ACTIVE, $from_customer_email), 'error');
						$error = true;
		            }
		        } else {
		        	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_EXISTS, '#'.$from_customer_id), 'error');
					$error = true;
		        }
		        
		        $to_customer_select_sql = "	SELECT customers_email_address, customers_status
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . tep_db_input($to_customer_id) . "'";
	        	$to_customer_result_sql = tep_db_query($to_customer_select_sql);
				
	        	if ($to_customer_row = tep_db_fetch_array($to_customer_result_sql)) {
		            $to_customer_email = $to_customer_row['customers_email_address'];
		            
		            if ($to_customer_row['customers_status'] != '1') {
		            	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_ACTIVE, $to_customer_email), 'error');
						$error = true;
		            }
		        } else {
		        	$messageStack->add_session(sprintf(ERROR_CUSTOMER_NOT_EXISTS, '#'.$to_customer_id), 'error');
					$error = true;
		        }
		        
		        if ($from_customer_id == $to_customer_id) {
		        	$messageStack->add_session(ERROR_SAME_CUSTOMERS, 'error');
					$error = true;
		        }
		    }
		    
		    if ($error) {
				tep_redirect(tep_href_link(FILENAME_CUSTOMERS_ACC_MERGING, tep_get_all_get_params(array('action')) . 'action=step_1'));
			} else {	// If everything fine, update both account to permanent inactive
				$customer_status_update_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
												SET customers_status = 9 
												WHERE customers_id IN('" . tep_db_input($from_customer_id) . "', '".tep_db_input($to_customer_id)."')";
				if ($debug_mode) {
					echo $customer_status_update_sql . '<br><br>';
				} else {
					tep_db_query($customer_status_update_sql);
				}
			}
			
			break;
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
          						<tr>
        							<td colspan="2" class="dataTableHeadingRow"><?=defined("HEADING_TITLE_" . strtoupper($action)) ? constant("HEADING_TITLE_" . strtoupper($action)) : '&nbsp;'?></td>
        						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
<?
switch ($action) {
	case "step_1":
		echo tep_draw_form('acc_merging_form', FILENAME_CUSTOMERS_ACC_MERGING, tep_get_all_get_params(array('action')) . 'action=step_2', 'post');
?>
						
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td><?=sprintf(ENTRY_ACCOUNT_MERGING_EMAILS, tep_draw_input_field('from_account_email', '', 'size="35"'), tep_draw_input_field('to_account_email', '', 'size="35"'))?></td>
								</tr>
								<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td><?=TEXT_ACC_MERGING_NOTES?></td>
								</tr>
								<tr>
									<td align="right"><br>
										<?=tep_submit_button(BUTTON_NEXT, ALT_BUTTON_NEXT, '', 'inputButton')?>&nbsp;
									</td>
								</tr>
							</table>
						</form>
<?
		break;
	case "step_2":
		$from_zones_select_sql = "	SELECT zone_name 
									FROM " . TABLE_ZONES . " 
									WHERE zone_country_id = '" . (int)$cFromInfo->entry_country_id . "' 
										AND zone_id = '" . tep_db_input($cFromInfo->entry_zone_id) . "'";
    	$from_zones_result_sql = tep_db_query($from_zones_select_sql);
    	if ($from_zones_row = tep_db_fetch_array($from_zones_result_sql)) {
    		$from_zone_name = $from_zones_row['zone_name'];
    	} else {
    		$from_zone_name = $cFromInfo->entry_zone_name;
    	}
    	
    	$to_zones_select_sql = "SELECT zone_name 
								FROM " . TABLE_ZONES . " 
								WHERE zone_country_id = '" . (int)$cToInfo->entry_country_id . "' 
									AND zone_id = '" . tep_db_input($cToInfo->entry_zone_id) . "'";
    	$to_zones_result_sql = tep_db_query($to_zones_select_sql);
    	if ($to_zones_row = tep_db_fetch_array($to_zones_result_sql)) {
    		$to_zone_name = $to_zones_row['zone_name'];
    	} else {
    		$to_zone_name = $cToInfo->entry_zone_name;
    	}
    	
		echo tep_draw_form('acc_merging_form', FILENAME_CUSTOMERS_ACC_MERGING, tep_get_all_get_params(array('action')) . 'action=step_3', 'post');
		echo tep_draw_hidden_field('from_customer_id', $cFromInfo->customers_id);
		echo tep_draw_hidden_field('to_customer_id', $cToInfo->customers_id);
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" class="formArea">
								<tr>
									<td width="50%" align="left" colspan="2" class="formAreaTitle"><?=TABLE_HEADING_MERGE_FROM_CUSTOMER_INFO?></td>
									<td width="50%" align="left" colspan="2" class="formAreaTitle"><?=TABLE_HEADING_MERGE_TO_CUSTOMER_INFO?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_ACCOUNT_STATUS?></td>
									<td align="left" class="main"><span class="redIndicator"><?=$cFromInfo->customers_status ? ACTIVE : NOT_ACTIVE?></span></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_ACCOUNT_STATUS?></td>
									<td align="left" class="main"><span class="redIndicator"><?=$cToInfo->customers_status ? ACTIVE : NOT_ACTIVE?></span></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_GENDER?></td>
									<td align="left" class="main"><?=$cFromInfo->customers_gender == 'm' ? MALE : FEMALE?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_GENDER?></td>
									<td align="left" class="main"><?=$cToInfo->customers_gender == 'm' ? MALE : FEMALE?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_FIRST_NAME?></td>
									<td align="left" class="main"><?=$cFromInfo->customers_firstname?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_FIRST_NAME?></td>
									<td align="left" class="main"><?=$cToInfo->customers_firstname?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_LAST_NAME?></td>
									<td align="left" class="main"><?=$cFromInfo->customers_lastname?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_LAST_NAME?></td>
									<td align="left" class="main"><?=$cToInfo->customers_lastname?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_DATE_OF_BIRTH?></td>
									<td align="left" class="main"><?=tep_datetime_short($cFromInfo->customers_dob, PREFERRED_DATE_FORMAT_SHORT)?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_DATE_OF_BIRTH?></td>
									<td align="left" class="main"><?=tep_datetime_short($cToInfo->customers_dob, PREFERRED_DATE_FORMAT_SHORT)?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_EMAIL_ADDRESS?></td>
									<td align="left" class="main"><span class="redIndicator"><?=$cFromInfo->customers_email_address?></span></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_EMAIL_ADDRESS?></td>
									<td align="left" class="main"><span class="redIndicator"><?=$cToInfo->customers_email_address?></span></td>
								</tr>
								<tr>
			        				<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_STREET_ADDRESS?></td>
									<td align="left" class="main"><?=$cFromInfo->entry_street_address?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_STREET_ADDRESS?></td>
									<td align="left" class="main"><?=$cToInfo->entry_street_address?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_SUBURB?></td>
									<td align="left" class="main"><?=$cFromInfo->entry_suburb?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_SUBURB?></td>
									<td align="left" class="main"><?=$cToInfo->entry_suburb?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_POST_CODE?></td>
									<td align="left" class="main"><?=$cFromInfo->entry_postcode?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_POST_CODE?></td>
									<td align="left" class="main"><?=$cToInfo->entry_postcode?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_CITY?></td>
									<td align="left" class="main"><?=$cFromInfo->entry_city?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_CITY?></td>
									<td align="left" class="main"><?=$cToInfo->entry_city?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_STATE?></td>
									<td align="left" class="main"><?=$from_zone_name?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_STATE?></td>
									<td align="left" class="main"><?=$to_zone_name?></td>
								</tr>
								<tr class="dataTableRow">
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_COUNTRY?></td>
									<td align="left" class="main"><?=$cFromInfo->countries_name?></td>
									<td width="10%" align="left" class="main" nowrap><?=ENTRY_COUNTRY?></td>
									<td align="left" class="main"><?=$cToInfo->countries_name?></td>
								</tr>
								<tr>
									<td align="right" colspan="4"><br>
										<?=tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, 'onClick="if (confirm(\''.sprintf(JS_CONFIRM_MERGE, $cFromInfo->customers_email_address, $cToInfo->customers_email_address).'\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }"', 'inputButton')?>&nbsp;
										<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_CUSTOMERS_ACC_MERGING, tep_get_all_get_params(array('action')) . 'action=step_1'), '', 'inputButton')?>&nbsp;
									</td>
								</tr>
							</table>
						</form>
<?
		break;
	case "step_3":
		echo TEXT_WAIT_MERGING_PROCESS;
		
		if ($debug_mode) echo "<br>You are in debug mode. No actual database update.<br><br>";
		
		flush();
		
		// Update address info
		$address_update_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
								SET customers_id = '" . tep_db_input($to_customer_id) . "'
								WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		if ($debug_mode) {
			echo $address_update_sql . '<br><br>';
		} else {
			tep_db_query($address_update_sql);
		}
		
		// Update buyback order
		$buyback_order_update_sql = "	UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " 
										SET customers_id = '" . tep_db_input($to_customer_id) . "'
										WHERE customers_id = '" . tep_db_input($from_customer_id) . "'
											AND buyback_request_group_user_type <> 1";
		if ($debug_mode) {
			echo $buyback_order_update_sql . '<br><br>';
		} else {
			tep_db_query($buyback_order_update_sql);
		}
		
		// Update customer coupon balance
		$to_customer_coupon_balance_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount 
													FROM " . TABLE_COUPON_GV_CUSTOMER . " 
													WHERE customer_id = '" . tep_db_input($to_customer_id) . "'";
		$to_customer_coupon_balance_result_sql = tep_db_query($to_customer_coupon_balance_select_sql);
		
		$from_customer_coupon_balance_select_sql = "SELECT sc_reversible_amount, sc_irreversible_amount 
													FROM " . TABLE_COUPON_GV_CUSTOMER . " 
													WHERE customer_id = '" . tep_db_input($from_customer_id) . "'";
		$from_customer_coupon_balance_result_sql = tep_db_query($from_customer_coupon_balance_select_sql);

		$from_customer_coupon_balance_amount = '';
		if ($from_customer_coupon_balance_row = tep_db_fetch_array($from_customer_coupon_balance_result_sql)) {
		     $from_customer_coupon_balance_amount = $from_customer_coupon_balance_row['amount'];

			if (tep_db_num_rows($to_customer_coupon_balance_result_sql)) {
				$coupon_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . " 
												SET sc_reversible_amount = sc_reversible_amount + " . (double)$from_customer_coupon_balance_row['sc_reversible_amount'] . ", 
													sc_irreversible_amount = sc_irreversible_amount + " . (double)$from_customer_coupon_balance_row['sc_irreversible_amount'] . " 
												WHERE customer_id = '" . tep_db_input($to_customer_id) . "'";
				
				$remove_from_cust_bal_delete_sql = "DELETE FROM  " . TABLE_COUPON_GV_CUSTOMER . " 
													WHERE customer_id = '" . tep_db_input($from_customer_id) . "'";
				if ($debug_mode) {
					echo $remove_from_cust_bal_delete_sql . '<br><br>';
				} else {
					tep_db_query($remove_from_cust_bal_delete_sql);
				}
			} else {
				$coupon_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . " 
												SET customer_id = '" . tep_db_input($to_customer_id) . "' 
												WHERE customer_id = '" . tep_db_input($from_customer_id) . "'";
			}
			
			if ($debug_mode) {
				echo $coupon_balance_update_sql . '<br><br>';
			} else {
				tep_db_query($coupon_balance_update_sql);
			}
		}
		
		// Update user comment
		$user_comment_update_sql = "UPDATE " . TABLE_USER_COMMENTS . " 
									SET user_id = '" . tep_db_input($to_customer_id) . "'
									WHERE user_id = '" . tep_db_input($from_customer_id) . "'
										AND user_role = 'customer'";
		if ($debug_mode) {
			echo $user_comment_update_sql . '<br><br>';
		} else {
			tep_db_query($user_comment_update_sql);
		}
		
		// Update coupon tables
		$coupon_queue_update_sql = "UPDATE " . TABLE_COUPON_GV_QUEUE . " 
									SET customer_id = '" . tep_db_input($to_customer_id) . "'
									WHERE customer_id = '" . tep_db_input($from_customer_id) . "'";
		if ($debug_mode) {
			echo $coupon_queue_update_sql . '<br><br>';
		} else {
			tep_db_query($coupon_queue_update_sql);
		}
		
		$coupon_redeem_update_sql = "	UPDATE " . TABLE_COUPON_REDEEM_TRACK . " 
										SET customer_id = '" . tep_db_input($to_customer_id) . "'
										WHERE customer_id = '" . tep_db_input($from_customer_id) . "'";
		if ($debug_mode) {
			echo $coupon_redeem_update_sql . '<br><br>';
		} else {
			tep_db_query($coupon_redeem_update_sql);
		}
		
		// Update customers_info_verification table
		$customer_info_verification_select_sql = "	SELECT customers_info_value, info_verification_type 
													FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
													WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		$customer_info_verification_result_sql = tep_db_query($customer_info_verification_select_sql);
		
		while ($customer_info_verification_row = tep_db_fetch_array($customer_info_verification_result_sql)) {
			$to_customer_info_verification_select_sql = "	SELECT customers_id 
															FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
															WHERE customers_id = '" . tep_db_input($to_customer_id) . "' 
																AND customers_info_value = '" . tep_db_input($customer_info_verification_row['customers_info_value']) . "' 
																AND info_verification_type = '" . tep_db_input($customer_info_verification_row['info_verification_type']) . "'";
			$to_customer_info_verification_result_sql = tep_db_query($to_customer_info_verification_select_sql);
			if (tep_db_num_rows($to_customer_info_verification_result_sql)) {	// If found same verification value
				$customer_info_verification_update_sql = "	DELETE FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
															WHERE customers_id = '" . tep_db_input($from_customer_id) . "' 
																AND customers_info_value = '" . tep_db_input($customer_info_verification_row['customers_info_value']) . "' 
																AND info_verification_type = '" . tep_db_input($customer_info_verification_row['info_verification_type']) . "'";
			} else {
				$customer_info_verification_update_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
															SET customers_id = '" . tep_db_input($to_customer_id) . "' 
															WHERE customers_id = '" . tep_db_input($from_customer_id) . "' 
																AND customers_info_value = '" . tep_db_input($customer_info_verification_row['customers_info_value']) . "' 
																AND info_verification_type = '" . tep_db_input($customer_info_verification_row['info_verification_type']) . "'";
			}
			
			if ($debug_mode) {
				echo $customer_info_verification_update_sql . '<br><br>';
			} else {
				tep_db_query($customer_info_verification_update_sql);
			}
		}
		
		// Update customers_remarks_history table
		$prefix_remark = "[From " . $from_customer_email . "(ID: " . $from_customer_id . ")]\n";
		
		$customer_remark_update_sql = "	UPDATE " . TABLE_CUSTOMERS_REMARKS_HISTORY . " 
										SET customers_id = '" . tep_db_input($to_customer_id) . "',
											remarks = CONCAT('".tep_db_input($prefix_remark)."', remarks) 
										WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		if ($debug_mode) {
			echo $customer_remark_update_sql . '<br><br>';
		} else {
			tep_db_query($customer_remark_update_sql);
		}
		
		// Update order table
		$customer_order_update_sql = "	UPDATE " . TABLE_ORDERS . " 
										SET customers_id = '" . tep_db_input($to_customer_id) . "' 
										WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		if ($debug_mode) {
			echo $customer_order_update_sql . '<br><br>';
		} else {
			tep_db_query($customer_order_update_sql);
		}
		
		// Update products_notifications table
		$from_prod_notification_select_sql = "	SELECT products_id 
												FROM " . TABLE_PRODUCTS_NOTIFICATIONS . " 
												WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		$from_prod_notification_result_sql = tep_db_query($from_prod_notification_select_sql);
		
		while ($from_prod_notification_row = tep_db_fetch_array($from_prod_notification_result_sql)) {
			$to_prod_notification_select_sql = "SELECT products_id 
												FROM " . TABLE_PRODUCTS_NOTIFICATIONS . " 
												WHERE customers_id = '" . tep_db_input($to_customer_id) . "' 
													AND products_id = '" . tep_db_input($from_prod_notification_row['products_id']) . "'";
			$to_prod_notification_result_sql = tep_db_query($to_prod_notification_select_sql);
			if (tep_db_num_rows($to_prod_notification_result_sql)) {	// If found same notification product
				$prod_notification_update_sql = "	DELETE FROM " . TABLE_PRODUCTS_NOTIFICATIONS . " 
													WHERE customers_id = '" . tep_db_input($from_customer_id) . "' 
														AND products_id = '" . tep_db_input($from_prod_notification_row['products_id']) . "'";
			} else {
				$prod_notification_update_sql = "	UPDATE " . TABLE_PRODUCTS_NOTIFICATIONS . " 
													SET customers_id = '" . tep_db_input($to_customer_id) . "' 
													WHERE customers_id = '" . tep_db_input($from_customer_id) . "' 
														AND products_id = '" . tep_db_input($from_prod_notification_row['products_id']) . "'";
			}
			
			if ($debug_mode) {
				echo $prod_notification_update_sql . '<br><br>';
			} else {
				tep_db_query($prod_notification_update_sql);
			}
		}
		
		// Update customer account info
		$from_acc_info_select_sql = "	SELECT customers_info_date_account_created, global_product_notifications 
										FROM " . TABLE_CUSTOMERS_INFO . " 
										WHERE customers_info_id = '" . tep_db_input($from_customer_id) . "'";
		$from_acc_info_result_sql = tep_db_query($from_acc_info_select_sql);
		$from_acc_info_row = tep_db_fetch_array($from_acc_info_result_sql);
		
		$to_acc_info_select_sql = "	SELECT customers_info_date_account_created > '".$from_acc_info_row['customers_info_date_account_created']."' AS acc_creation, global_product_notifications 
									FROM " . TABLE_CUSTOMERS_INFO . "  
									WHERE customers_info_id = '" . tep_db_input($to_customer_id) . "'";
		$to_acc_info_result_sql = tep_db_query($to_acc_info_select_sql);
		$to_acc_info_row = tep_db_fetch_array($to_acc_info_result_sql);
		
		if ($to_acc_info_row['acc_creation'] == '1') {
			$acc_creation_update_sql = " 	UPDATE " . TABLE_CUSTOMERS_INFO . " 
											SET customers_info_date_account_created = '".$from_acc_info_row['customers_info_date_account_created']."' 
											WHERE customers_info_id = '".tep_db_input($to_customer_id)."'; ";
			
			if ($debug_mode) {
				echo $acc_creation_update_sql . '<br><br>';
			} else {
				tep_db_query($acc_creation_update_sql);
			}
		}
		
		if ($from_acc_info_row['global_product_notifications'] != $to_acc_info_row['global_product_notifications'] && $from_acc_info_row['global_product_notifications'] == '1') {
			$global_prod_notification_update_sql = "UPDATE " . TABLE_CUSTOMERS_INFO . " 
													SET global_product_notifications = '1' 
													WHERE customers_info_id = '".tep_db_input($to_customer_id)."'; ";
			
			if ($debug_mode) {
				echo $global_prod_notification_update_sql . '<br><br>';
			} else {
				tep_db_query($global_prod_notification_update_sql);
			}
		}
		
		$from_acc_select_sql = "SELECT * 
								FROM " . TABLE_CUSTOMERS . " 
								WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
		$from_acc_result_sql = tep_db_query($from_acc_select_sql);
		$from_acc_row = tep_db_fetch_array($from_acc_result_sql);
		
		$to_acc_select_sql = "	SELECT * 
								FROM " . TABLE_CUSTOMERS . " 
								WHERE customers_id = '" . tep_db_input($to_customer_id) . "'";
		$to_acc_result_sql = tep_db_query($to_acc_select_sql);
		$to_acc_row = tep_db_fetch_array($to_acc_result_sql);
		
		if ((int)$from_acc_row['customers_groups_id'] > (int)$to_acc_row['customers_groups_id']) {
			$cust_grp_update_sql = "UPDATE " . TABLE_CUSTOMERS . " 
									SET customers_groups_id = '".$from_acc_row['customers_groups_id']."' 
									WHERE customers_id = '".tep_db_input($to_customer_id)."'; ";
			
			if ($debug_mode) {
				echo $cust_grp_update_sql . '<br><br>';
			} else {
				tep_db_query($cust_grp_update_sql);
			}
		}
		
		if ($from_acc_row['customers_flag'] != $to_acc_row['customers_flag'] && tep_not_null($from_acc_row['customers_flag'])) {
			$from_flag_array = tep_not_null($from_acc_row['customers_flag']) ? explode(',', $from_acc_row['customers_flag']) : array();
			$to_flag_array = tep_not_null($to_acc_row['customers_flag']) ? explode(',', $to_acc_row['customers_flag']) : array();
			
			$missing_flag_array = array_diff($from_flag_array, $to_flag_array);
			$missing_flag_array = array_filter($missing_flag_array, "filter_empty_val");
			
			if (count($missing_flag_array)) {
				$complete_flag_array = array_merge($to_flag_array, $missing_flag_array);
				asort($complete_flag_array);
				reset($complete_flag_array);
				
				$cust_flag_update_sql = " 	UPDATE " . TABLE_CUSTOMERS . " 
											SET customers_flag = '".implode(',', $complete_flag_array)."' 
											WHERE customers_id = '".tep_db_input($to_customer_id)."'; ";
				
				if ($debug_mode) {
					echo $cust_flag_update_sql . '<br><br>';
				} else {
					tep_db_query($cust_flag_update_sql);
				}
			}
		}
		
		/****************************************************************
			Final Tasks:
			1. Turn the primary account from permanent inactive to active
			2. Update the customers_merged_profile field
		****************************************************************/
		$merged_profile_array = array();
		
		$from_customer_merged_select_sql = "SELECT customers_merged_profile, customers_firstname, customers_lastname, customers_gender 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
    	$from_customer_merged_result_sql = tep_db_query($from_customer_merged_select_sql);
		$from_customer_merged_row = tep_db_fetch_array($from_customer_merged_result_sql);
		
		$to_customer_merged_select_sql = "	SELECT customers_merged_profile, customers_firstname, customers_lastname, customers_gender  
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . tep_db_input($to_customer_id) . "'";
    	$to_customer_merged_result_sql = tep_db_query($to_customer_merged_select_sql);
		$to_customer_merged_row = tep_db_fetch_array($to_customer_merged_result_sql);
		
    	if (tep_not_null($from_customer_merged_row['customers_merged_profile'])) {
    		$merged_profile_array[] = $from_customer_merged_row['customers_merged_profile'];
    	}
		
		if (tep_not_null($to_customer_merged_row['customers_merged_profile'])) {
    		$merged_profile_array[] = $to_customer_merged_row['customers_merged_profile'];
    	}
    	
    	$merged_profile_array[] = $from_customer_id;
    	
    	// Keep a record in old customer account where does this account merged into
    	$merge_into_remark = $from_customer_email . " (ID: ".$from_customer_id.") has been merged into ".$to_customer_email."(ID: ".$to_customer_id . ")";
		$from_customer_remark_insert_sql = "INSERT INTO " . TABLE_CUSTOMERS_REMARKS_HISTORY . " (customers_id, date_remarks_added, remarks, remarks_added_by) 
											VALUES ('".tep_db_input($from_customer_id)."', now(), '".tep_db_input($merge_into_remark)."', '".$login_id."') ";
		if ($debug_mode) {
			echo $from_customer_remark_insert_sql . '<br><br>';
		} else {
			tep_db_query($from_customer_remark_insert_sql);
		}
		
		$to_customer_remark_insert_sql = "	INSERT INTO " . TABLE_CUSTOMERS_REMARKS_HISTORY . " (customers_id, date_remarks_added, remarks, remarks_added_by) 
											VALUES ('".tep_db_input($to_customer_id)."', now(), '".tep_db_input($merge_into_remark)."', '".$login_id."') ";
		if ($debug_mode) {
			echo $to_customer_remark_insert_sql . '<br><br>';
		} else {
			tep_db_query($to_customer_remark_insert_sql);
		}
		
		
    	$from_customer_merged_profile_updat_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
    												SET customers_merged_profile = '' 
													WHERE customers_id = '" . tep_db_input($from_customer_id) . "'";
    	if ($debug_mode) {
			echo $from_customer_merged_profile_updat_sql . '<br><br>';
		} else {
			tep_db_query($from_customer_merged_profile_updat_sql);
		}
		
    	$to_customer_merged_profile_updat_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
    												SET customers_merged_profile = '" . implode(",", $merged_profile_array) . "' 
													WHERE customers_id = '" . tep_db_input($to_customer_id) . "'";
    	if ($debug_mode) {
			echo $to_customer_merged_profile_updat_sql . '<br><br>';
		} else {
			tep_db_query($to_customer_merged_profile_updat_sql);
		}
		
		$to_customer_status_updat_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
    										SET customers_status = 1 
											WHERE customers_id = '" . tep_db_input($to_customer_id) . "'";
    	if ($debug_mode) {
			echo $to_customer_status_updat_sql . '<br><br>';
		} else {
			tep_db_query($to_customer_status_updat_sql);
		}
		
		echo "<br><br>" . sprintf(TEXT_ACC_MERGING_DONE, $from_customer_email, $to_customer_email);

		//send notification email to from_customer and to_customer
		$email = EMAIL_CUSTOMERS_ACC_MERGED_SUBJECT . " \n" .
		         EMAIL_SEPARATOR . "\n\n" .
		         sprintf(EMAIL_CUSTOMERS_ACC_MERGED_EMAILS, $from_customer_email, $to_customer_email) .
		         sprintf(EMAIL_CUSTOMERS_ACC_MERGED_STATUS, PERMANENT_INACTIVE, $to_customer_email, $to_customer_email) .
		         sprintf(EMAIL_CUSTOMERS_ACC_MERGED_PASS, $to_customer_email) . "\n\n" .
		         (($from_customer_coupon_balance_amount > 0) ? sprintf(EMAIL_CUSTOMERS_ACC_VOUCHER_BALANCE, $from_customer_email, $to_customer_email) . "\n\n" : '') .
		         EMAIL_TEXT_CLOSING . "\n\n" .
			     EMAIL_FOOTER;

        // account A
		$from_email_greeting = tep_get_email_greeting($from_customer_merged_row['customers_firstname'], $from_customer_merged_row['customers_lastname'], $from_customer_merged_row['customers_gender']);
		$from_customers_email = $from_email_greeting . $email;

        // account B
		$to_email_greeting = tep_get_email_greeting($to_customer_merged_row['customers_firstname'], $to_customer_merged_row['customers_lastname'], $to_customer_merged_row['customers_gender']);
		$to_customers_email = $to_email_greeting . $email;

		@tep_mail($from_customer_merged_row['customers_firstname'].' '.$from_customer_merged_row['customers_lastname'], $from_customer_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_CUSTOMERS_ACC_MERGED_SUBJECT)), $from_customers_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		@tep_mail($to_customer_merged_row['customers_firstname'].' '.$to_customer_merged_row['customers_lastname'], $to_customer_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_CUSTOMERS_ACC_MERGED_SUBJECT)), $to_customers_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

		break;
}
?>
        				</td>
        			</tr>
        		</table>
    		</td>
    	</tr>
    </table>
<!-- body_text //-->
<!-- body_text_eof //-->
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>