<?php
/*
  	Group Discount
  	by hO<PERSON><PERSON><PERSON>, <EMAIL>, http://hozone.cjb.net

  	visit osCommerceITalia, http://www.oscommerceitalia.com

  	derived by:
  	Discount_Groups_v1.1, by <PERSON>, 2003/5/22

  	for:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
include_once(DIR_WS_FUNCTIONS . 'admin_members.php');
require(DIR_WS_CLASSES . 'log.php');
$system_log_object = new log_files($login_id);

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

$add_customer_group_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_ADD_GROUP');
$edit_customer_group_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_EDIT_GROUP');
$add_discount_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_ADD_DISCOUNT_SETTING');
$edit_discount_permission = tep_admin_group_edit_discount_permission($_GET['cID']);
$extra_sc_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_EXTRA_SC');
$add_aft_group_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_ADD_AFT_GROUP');
$edit_aft_group_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_GROUPS, 'CUSTOMER_GROUP_EDIT_AFT_GROUP');

if (tep_not_null($action) || tep_not_null($subaction)) {
	if (isset($_REQUEST['dis_id'])) {
		$cat_select_sql = "	SELECT categories_id
							FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
							WHERE customers_groups_discount_id = '" . tep_db_input($_REQUEST['dis_id']) . "'";
		$cat_result_sql = tep_db_query($cat_select_sql);
		$cat_row = tep_db_fetch_array($cat_result_sql);

		if (tep_check_cat_tree_permissions(FILENAME_CUSTOMERS_GROUPS, $cat_row['categories_id']) != 1) {
    		$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
			tep_redirect( tep_href_link(FILENAME_CUSTOMERS_GROUPS) );
    	}
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
      	case 'update':
      		$error = false;

      		$customers_groups_id = tep_db_prepare_input($_GET['cID']);
        	$customers_groups_name = tep_db_prepare_input($_POST['customers_groups_name']);
        	$name_replace = ereg_replace_dep(' ', '', strtolower($customers_groups_name));
        	$customers_groups_legend_color = tep_db_prepare_input($_POST['customers_groups_legend_color']);
        	$extra_sc_percentage = tep_db_prepare_input($_POST['extra_sc']);

			$sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/",$_POST['sort_order']) ? $_POST['sort_order'] : 50000);
      		if ($edit_customer_group_permission) {
	      		if (tep_not_null($customers_groups_name)) {
	      			$check_groups_name_query = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id <> " . tep_db_input($customers_groups_id) . " and LOWER(REPLACE(customers_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	          			$zone_payment_gateway_id_array = array();
	          			if (isset($_REQUEST['zone_payment_gateway_id']) && count($_REQUEST['zone_payment_gateway_id'])) {
	          				$zone_payment_gateway_id_array = $_REQUEST['zone_payment_gateway_id'];
	          			}

	          			$customer_group_update_data_sql = array('customers_groups_name' => $customers_groups_name,
	          													'sort_order' => (int)$sort_order,
	          													'customers_groups_legend_color' => $customers_groups_legend_color,
	          													'customers_groups_payment_methods' => tep_db_prepare_input(implode(",",$zone_payment_gateway_id_array)));

                        if ($extra_sc_permission) {
                            $customer_group_update_data_sql['customers_groups_extra_sc'] = (double)$extra_sc_percentage;
                        }
	          			tep_db_perform(TABLE_CUSTOMERS_GROUPS, $customer_group_update_data_sql, 'update', " customers_groups_id = '" . tep_db_input($customers_groups_id) . "'");
	          		}
	      		} else {
	      			$messageStack->add(ERROR_CUSTOMERS_GROUPS_NAME);
					$error = true;
	      		}
      		} else {
      			$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
      			$error = true;
      		}

        	if (!$error) {
        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction', 'tab')) . 'cID=' . $customers_groups_id . '&tab=dis_grp'));
			}

			break;
        case 'update_aft':
            $error = false;

      		$customers_aft_groups_id = tep_db_prepare_input($_GET['aftID']);
        	$customers_aft_groups_name = tep_db_prepare_input($_POST['customers_aft_groups_name']);
        	$name_replace = ereg_replace_dep(' ', '', strtolower($customers_aft_groups_name));
        	$sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/",$_POST['sort_order']) ? $_POST['sort_order'] : 50000);

      		if ($edit_aft_group_permission) {
	      		if (tep_not_null($customers_aft_groups_name)) {
	      			$check_groups_name_query = tep_db_query("select customers_aft_groups_name from " . TABLE_CUSTOMERS_AFT_GROUPS . " where customers_aft_groups_id <> " . tep_db_input($customers_aft_groups_id) . " and LOWER(REPLACE(customers_aft_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	          			$customer_aft_group_update_data_sql = array('customers_aft_groups_name' => $customers_aft_groups_name,
                                        							'sort_order' => $sort_order
                                                                    );
	          			tep_db_perform(TABLE_CUSTOMERS_AFT_GROUPS, $customer_aft_group_update_data_sql, 'update', " customers_aft_groups_id = '" . tep_db_input($customers_aft_groups_id) . "'");
	          		}
	      		} else {
	      			$messageStack->add(ERROR_CUSTOMERS_GROUPS_NAME);
					$error = true;
	      		}
      		} else {
      			$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
      			$error = true;
      		}

        	if (!$error) {
        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('aftID', 'action', 'subaction', 'tab')) . 'aftID=' . $customers_aft_groups_id . '&tab=aft_grp'));
			}

            break;
      	case 'deleteconfirm':
      	    if ($edit_customer_group_permission) {
      	        $error = false;
      	        $can_delete = false;
      	        $group_id = tep_db_prepare_input($_GET['cID']);
      	        
      	        // Select the group name from database
      	        $nameselect = tep_db_query("SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id = '".tep_db_input($group_id)."'");
      	        $namedisplay = mysql_fetch_array($nameselect);
      	        
      	        // Check if there is any customer has this customers Group
      	        $pmcg_select_sql = "SELECT customers_id
	        						FROM " . TABLE_CUSTOMERS . "
									WHERE customers_groups_id = '".tep_db_input($group_id)."'";
      	        $pmcg_result_sql = tep_db_query($pmcg_select_sql);
      	        $row = tep_db_num_rows($pmcg_result_sql); //Get the number of customers
      	        $can_delete = ($row > 0) ? false: true;
      	        
      	        
      	        if ($can_delete) {
      	            tep_db_query("delete from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id= " . $group_id);
      	            tep_db_query("UPDATE " . TABLE_CUSTOMERS . " set customers_groups_id=1 where customers_groups_id=" . $group_id);
      	            tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " WHERE customers_groups_id = '" . tep_db_input($group_id) . "'");
      	            tep_db_query("DELETE FROM " . TABLE_SITE_CUSTOMERS_ACCESS . " WHERE customers_groups_id = '" . tep_db_input($group_id) . "'");
      	            tep_db_query("DELETE FROM " . TABLE_PAYMENT_FEES . " WHERE payment_fees_customers_groups_id = '" .tep_db_input($group_id). "' AND payment_methods_mode = 'RECEIVE' AND payment_fees_follow_group = '0' ");
      	            # remove record in Customer Group Status (orders_status_conf.php) {start}
      	            tep_db_query('DELETE FROM ' . TABLE_STATUS_CONFIGURATION . ' WHERE status_configuration_trans_type = "CGRP" AND (status_configuration_source_status_id = "' . (int)$group_id . '" OR status_configuration_destination_status_id = "' . (int)$group_id . '")');
      	            
      	            # remove record in Customer Group Status (orders_status_conf.php) {end}
      	        } else {
      	            $messageStack->add("Error: Failed to delete, ". $row . " of customer at ". $namedisplay[0] . " customer group ");
      	            $error = true;
      	        }
      	        
      	        if (!$error) {
      	            tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction'))));
      	        }
      	    } else {
      	        $messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
      	    }
      	    
      	    break;
        case 'del_confirm_aft':
      		if ($edit_aft_group_permission) {  
	      		$error = false;
	      		$can_delete = false;
	        	$group_id = tep_db_prepare_input($_GET['aftID']);

	        	// Check if there is any customer has this AFT Group
	        	$cust_select_sql = "SELECT customers_id
	        						FROM " . TABLE_CUSTOMERS . "
									WHERE customers_aft_groups_id = '".tep_db_input($group_id)."'
                                    LIMIT 1";
	        	$cust_result_sql = tep_db_query($cust_select_sql);
	        	$can_delete = (tep_db_num_rows($cust_result_sql) > 0) ? false: true;

	        	if ($can_delete) {
		        	tep_db_query("delete from " . TABLE_CUSTOMERS_AFT_GROUPS . " where customers_aft_groups_id= " . $group_id);

                    # remove record in Customer Group Status (orders_status_conf.php) {start}
					tep_db_query('DELETE FROM ' . TABLE_STATUS_CONFIGURATION . ' WHERE status_configuration_trans_type = "AFTGR" AND (status_configuration_source_status_id = "' . (int)$group_id . '" OR status_configuration_destination_status_id = "' . (int)$group_id . '")');
					# remove record in Customer Group Status (orders_status_conf.php) {end}

					# remove record in Purchase Limit (purchase_limit.php) {start}
					tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . " WHERE customers_aft_groups_id = '" . tep_db_input($group_id) . "'");
					# remove record in Purchase Limit (purchase_limit.php) {end}
				} else {
	      			$messageStack->add(ERROR_AFT_GROUPS_HAS_CUSTOMER);
					$error = true;
				}

				if (!$error) {
	        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction'))));
	        	}
	        } else {
	        	$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
	        }

        	break;
      	case 'newconfirm':
      		$error = false;

        	$customers_groups_name = tep_db_prepare_input($_POST['customers_groups_name']);
        	$name_replace = ereg_replace_dep(" ", "", strtolower($customers_groups_name));
        	$customers_groups_legend_color = tep_db_prepare_input($_POST['customers_groups_legend_color']);
        	$extra_sc_percentage = tep_db_prepare_input($_POST['extra_sc']);
			$sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/",$_POST['sort_order']) ? $_POST['sort_order'] : 50000);

	    	if ($add_customer_group_permission) {
	      		if (tep_not_null($customers_groups_name)) {
	      			$check_groups_name_query = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where LOWER(REPLACE(customers_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	          			$customer_group_data_array = array(	'customers_groups_name' => $customers_groups_name,
	          												'sort_order' => $sort_order,
	          												'customers_groups_legend_color' => $customers_groups_legend_color
	          											   );
                        if ($extra_sc_permission) {
                            $customer_group_data_array['customers_groups_extra_sc'] = (double)$extra_sc_percentage;
                        }
						tep_db_perform(TABLE_CUSTOMERS_GROUPS, $customer_group_data_array );
						$new_customer_group_id = tep_db_insert_id();

						$site_customers_access_data_array = array(	'site_id' => '0',
																	'customers_groups_id' => $new_customer_group_id);
						tep_db_perform(TABLE_SITE_CUSTOMERS_ACCESS, $site_customers_access_data_array);
	          		}
	      		} else {
	      			$messageStack->add(ERROR_CUSTOMERS_GROUPS_NAME);
					$error = true;
	      		}
      		} else {
      			$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
      			$error = true;
      		}

      		if (!$error) {
        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('action', 'subaction', 'tab')) . 'tab=dis_grp'));
        	}

        	break;
        case 'newconfirm_aft':
      		$error = false;

            $customers_aft_groups_name = tep_db_prepare_input($_POST['customers_aft_groups_name']);
        	$name_replace = ereg_replace_dep(' ', '', strtolower($customers_aft_groups_name));
        	$sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/",$_POST['sort_order']) ? $_POST['sort_order'] : 50000);

	    	if ($add_aft_group_permission) {
	      		if (tep_not_null($customers_aft_groups_name)) {
	      			$check_groups_name_query = tep_db_query("select customers_aft_groups_name from " . TABLE_CUSTOMERS_AFT_GROUPS . " where LOWER(REPLACE(customers_aft_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	          			$customer_aft_group_update_data_sql = array('customers_aft_groups_name' => $customers_aft_groups_name,
                                        							'sort_order' => $sort_order
                                                                    );
	          			tep_db_perform(TABLE_CUSTOMERS_AFT_GROUPS, $customer_aft_group_update_data_sql);
	          		}
	      		} else {
	      			$messageStack->add(ERROR_CUSTOMERS_GROUPS_NAME);
					$error = true;
	      		}
      		} else {
      			$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
      			$error = true;
      		}
      		if (!$error) {
        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('action', 'subaction', 'tab')) . 'tab=aft_grp'));
			}

        	break;
        case 'new_discount_confirm':
        case 'edit_discount_confirm':
        	$error = false;

        	$group_id = tep_db_prepare_input($_REQUEST['cID']);
        	$group_discount_id = tep_db_prepare_input($_REQUEST['dis_id']);	// For editing discount
        	$category_id = tep_db_prepare_input($_POST['cat_id']);
        	$customers_groups_discount_sign = tep_db_prepare_input($_POST['customers_groups_discount_sign']);
        	$customers_groups_discount = tep_db_prepare_input($_POST['customers_groups_discount']);
        	$customers_groups_rebate_sign = tep_db_prepare_input($_POST['customers_groups_rebate_sign']);
        	$customers_groups_rebate = tep_db_prepare_input($_POST['customers_groups_rebate']);
        	$c2c_customers_groups_discount_sign = tep_db_prepare_input($_POST['c2c_customers_groups_discount_sign']);
        	$c2c_customers_groups_discount = tep_db_prepare_input($_POST['c2c_customers_groups_discount']);
        	$c2c_customers_groups_rebate_sign = tep_db_prepare_input($_POST['c2c_customers_groups_rebate_sign']);
        	$c2c_customers_groups_rebate = tep_db_prepare_input($_POST['c2c_customers_groups_rebate']);

        	if (!tep_not_null($category_id)) {
        		$messageStack->add(ERROR_NO_CATEGORY_SELECTED);
				$error = true;
        	} else if (tep_check_cat_tree_permissions(FILENAME_CUSTOMERS_GROUPS, $category_id) != 1) {
        		$messageStack->add(ERROR_CAT_ACCESS_DENIED, 'error');
        		$error = true;
        	}

        	if (!tep_not_null($customers_groups_discount) || !tep_not_null($c2c_customers_groups_discount)) {
        		$messageStack->add(ERROR_EMPTY_DISCOUNT);
				$error = true;
        	}

        	if (!$error) {
        		$customer_group_discount_notification_select_sql = "SELECT discount_setting_notification FROM " .TABLE_SITE_CUSTOMERS_ACCESS . " WHERE customers_groups_id = '" . tep_db_input($group_id) . "'";
				$customer_group_discount_notification_result_sql = tep_db_query($customer_group_discount_notification_select_sql);
				if ($customer_group_discount_notification_row = tep_db_fetch_array($customer_group_discount_notification_result_sql)) {
					$email_to_array = tep_parse_email_string($customer_group_discount_notification_row['discount_setting_notification']);
				}
        		if ($subaction == 'edit_discount_confirm') {
        			if ($edit_discount_permission) {
						$old_discount_select_sql = "SELECT customers_groups_discount, customers_groups_rebate, categories_id, c2c_customers_groups_discount, c2c_customers_groups_rebate
													FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
													WHERE customers_groups_discount_id = '" . (int)$group_discount_id . "'";
						$old_discount_result_sql = tep_db_query($old_discount_select_sql);
						$old_discount_row = tep_db_fetch_array($old_discount_result_sql);
						$old_discount_rate = $old_discount_row['customers_groups_discount'];
						$old_rebate_rate = $old_discount_row['customers_groups_rebate'];
						$old_c2c_discount_rate = $old_discount_row['c2c_customers_groups_discount'];
						$old_c2c_rebate_rate = $old_discount_row['c2c_customers_groups_rebate'];

	        			$cat_discount_select_sql = "SELECT customers_groups_discount_id
	        										FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
	        										WHERE customers_groups_id = '" . tep_db_input($group_id) . "'
	        											AND customers_groups_discount_id <> '" . tep_db_input($group_discount_id) . "' AND categories_id = '" . tep_db_input($category_id) . "'";
		        		$cat_discount_result_sql = tep_db_query($cat_discount_select_sql);
		          		if (tep_db_num_rows($cat_discount_result_sql) > 0) {
		          			$messageStack->add(ERROR_CAT_DISCOUNT_EXISTS);
							$error = true;
		          		} else {
		          			$customer_group_discount_data_array = array('categories_id' => $category_id,
		          														'customers_groups_discount' => $customers_groups_discount_sign . $customers_groups_discount,
		          														'customers_groups_rebate' => $customers_groups_rebate_sign . $customers_groups_rebate,
		          														'c2c_customers_groups_discount' => $c2c_customers_groups_discount_sign . $c2c_customers_groups_discount,
		          														'c2c_customers_groups_rebate' => $c2c_customers_groups_rebate_sign . $c2c_customers_groups_rebate);
							tep_db_perform(TABLE_CUSTOMERS_GROUPS_DISCOUNT, $customer_group_discount_data_array, 'update', "customers_groups_discount_id = '" . tep_db_input($group_discount_id) . "'");
							$notification_email_content ='';
							$notification_email_subject = implode(' ', array(EMAIL_SUBJECT_PREFIX, CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_UPDATE));
							if ($old_discount_row['categories_id'] != $category_id) {
		          				$system_log_object->insert_system_log('Change Customer Discount Group Category', TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', ((int)$old_discount_row['categories_id'] > 0 ? tep_output_generated_category_path_sq($old_discount_row['categories_id']) : '[Top]'), ((int)$category_id > 0 ? tep_output_generated_category_path_sq($category_id) : '[Top]'));
		          				$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category Update: \''.((int)$old_discount_row['categories_id'] > 0 ? tep_output_generated_category_path_sq($old_discount_row['categories_id']) : '[Top]').'\' -> \''.((int)$category_id > 0 ? tep_output_generated_category_path_sq($category_id) : '[Top]').'\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';
		          				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
									tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								}
		          			}

		          			if ($old_discount_rate != $customers_groups_discount_sign . $customers_groups_discount) {
		          				$system_log_object->insert_system_log('Change Customer Discount Group Category Discount Rate<br> - ' . (((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', $old_discount_rate, $customers_groups_discount_sign . $customers_groups_discount);
		          				$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category: '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]').'<br>Discount Rate Update: \''.$old_discount_rate. '\' -> \''.$customers_groups_discount_sign . $customers_groups_discount. '\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';

		          				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
									tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								}
		          			}

		          			if ($old_rebate_rate != $customers_groups_rebate_sign . $customers_groups_rebate) {
		          				$system_log_object->insert_system_log('Change Customer Discount Group Category Rebate Rate<br> - ' . (((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', $old_rebate_rate, $customers_groups_rebate_sign . $customers_groups_rebate);
		          				$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category: '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]').'<br>Rebate Rate Update: \''.$old_rebate_rate. '\' -> \''.$customers_groups_rebate_sign . $customers_groups_rebate. '\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';

		          				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
									tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								}
		          			}

		          			if ($old_c2c_discount_rate != $c2c_customers_groups_discount_sign . $c2c_customers_groups_discount) {
		          				$system_log_object->insert_system_log('Change Customer Discount Group Category C2C Discount Rate<br> - ' . (((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', $old_c2c_discount_rate, $c2c_customers_groups_discount_sign . $c2c_customers_groups_discount);
		          				$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category: '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]').'<br>C2C Discount Rate Update: \''.$old_c2c_discount_rate. '\' -> \''.$c2c_customers_groups_discount_sign . $c2c_customers_groups_discount. '\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';

		          				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
									tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								}
		          			}

		          			if ($old_c2c_rebate_rate != $c2c_customers_groups_rebate_sign . $c2c_customers_groups_rebate) {
		          				$system_log_object->insert_system_log('Change Customer Discount Group Category C2C Rebate Rate<br> - ' . (((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', $old_c2c_rebate_rate, $c2c_customers_groups_rebate_sign . $c2c_customers_groups_rebate);
		          				$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category: '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]').'<br>C2C Rebate Rate Update: \''.$old_rebate_rate. '\' -> \''.$c2c_customers_groups_rebate_sign . $c2c_customers_groups_rebate. '\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';

		          				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
									tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								}
		          			}
		          		}
		          	} else {
		        		$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
	        			$error = true;
	        		}
        		} else if ($subaction == 'new_discount_confirm') {
        			if ($add_discount_permission && $edit_discount_permission) {	// If Admin user has permission to add and also for that particular custmer group (control by edit permission)
	        			$cat_discount_select_sql = "SELECT customers_groups_discount_id
		        									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
		        									WHERE customers_groups_id = '" . tep_db_input($group_id) . "'
		        										AND categories_id = '" . tep_db_input($category_id) . "'";
		        		$cat_discount_result_sql = tep_db_query($cat_discount_select_sql);
		          		if (tep_db_num_rows($cat_discount_result_sql) > 0) {
		          			$messageStack->add(ERROR_CAT_DISCOUNT_EXISTS);
							$error = true;
		          		} else {
		          			$customer_group_discount_data_array = array('customers_groups_id' => $group_id,
		          														'categories_id' => $category_id,
		          														'customers_groups_discount' => $customers_groups_discount_sign . $customers_groups_discount,
		          														'customers_groups_rebate' => $customers_groups_rebate_sign . $customers_groups_rebate,
		          														'c2c_customers_groups_discount' => $c2c_customers_groups_discount_sign . $c2c_customers_groups_discount,
		          														'c2c_customers_groups_rebate' => $c2c_customers_groups_rebate_sign . $c2c_customers_groups_rebate
		          														);
							tep_db_perform(TABLE_CUSTOMERS_GROUPS_DISCOUNT, $customer_group_discount_data_array);
							$system_log_object->insert_system_log('Add Customer Discount Group Category Discount Rate<br> - '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', TEXT_NOT_AVAILABLE, $customers_groups_discount_sign . $customers_groups_discount);
							$system_log_object->insert_system_log('Add Customer Discount Group Category Rebate Rate<br> - '.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $group_id, 'customers_groups_name', TEXT_NOT_AVAILABLE, $customers_groups_rebate_sign . $customers_groups_rebate);
							$notification_email_subject = implode(' ', array(EMAIL_SUBJECT_PREFIX, CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_INSERT));
							$notification_email_content = 'Customer Group Name: '. $customers_groups_name .'<br>Category: \'N/A\' -> \''.(((int)$category_id > 0) ? tep_output_generated_category_path_sq($category_id) : '[Top]').'\'<br>Discount Rate: \'N/A\' -> \''. $customers_groups_discount_sign . $customers_groups_discount . '\'<br>Rebate Rate: \'N/A\' -> \' \''. $customers_groups_rebate_sign . $customers_groups_rebate . '\'<br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';
							for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
								tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							}
						}
        			} else {
        				$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
        				$error = true;
        			}
	          	}
        	}

        	if (!$error) {
        		tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction'))));
        	}
        	break;
        case 'delete_discount':
        	$group_discount_id = tep_db_prepare_input($_REQUEST['dis_id']);
        	if (tep_not_null($group_discount_id) && $edit_discount_permission) {
				$old_discount_select_sql = "SELECT lc.customers_groups_discount, lc.customers_groups_rebate, lc.categories_id, lc.customers_groups_id, la.customers_groups_name
											FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " as lc INNER JOIN  " . TABLE_CUSTOMERS_GROUPS . " as la ON (lc.customers_groups_id = la.customers_groups_id)
											WHERE customers_groups_discount_id = '" . (int)$group_discount_id . "'";
				$old_discount_result_sql = tep_db_query($old_discount_select_sql);
				$old_discount_row = tep_db_fetch_array($old_discount_result_sql);
				$system_log_object->insert_system_log('Remove Customer Discount Group Category Discount Rate<br> - '.(((int)$old_discount_row['categories_id'] > 0) ? tep_output_generated_category_path_sq($old_discount_row['categories_id']) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $old_discount_row['customers_groups_id'], 'customers_groups_name', $old_discount_row['customers_groups_discount'], TEXT_NOT_AVAILABLE);
        		$system_log_object->insert_system_log('Remove Customer Discount Group Category Rebate Rate<br> - '.(((int)$old_discount_row['categories_id'] > 0) ? tep_output_generated_category_path_sq($old_discount_row['categories_id']) : '[Top]'), TABLE_CUSTOMERS_GROUPS, $old_discount_row['customers_groups_id'], 'customers_groups_name', $old_discount_row['customers_groups_rebate'], TEXT_NOT_AVAILABLE);

        		tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " WHERE customers_groups_discount_id = '" . tep_db_input($group_discount_id) . "'");
    			$customer_group_discount_notification_select_sql = "SELECT discount_setting_notification FROM " .TABLE_SITE_CUSTOMERS_ACCESS . " WHERE customers_groups_id = '" . tep_db_prepare_input($_GET['cID']) . "'";
				$customer_group_discount_notification_result_sql = tep_db_query($customer_group_discount_notification_select_sql);

				if($customer_group_discount_notification_row = tep_db_fetch_array($customer_group_discount_notification_result_sql)) {
					$email_to_array = tep_parse_email_string($customer_group_discount_notification_row['discount_setting_notification']);
					$notification_email_subject = implode(' ', array(EMAIL_SUBJECT_PREFIX, CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_DELETE));
	    			$notification_email_content = 'Customer Group Name: '. $old_discount_row['customers_groups_name'] . '<br>Category: \''.(((int)$old_discount_row['categories_id'] > 0) ? tep_output_generated_category_path_sq($old_discount_row['categories_id']) : '[Top]'). '\' -> \'N/A\' <br>Discount Rate: \''. $old_discount_row['customers_groups_discount'] . '\' -> \'N/A\' <br>Rebate Rate: \' \''. $old_discount_row['customers_groups_rebate'] . '\' -> \'N/A\' <br><br>Update Date: '.date("Y-m-d H:i:s").'<br>Update IP: '.getenv("REMOTE_ADDR").'<br>Update User: '.$_SESSION['login_email_address'].' ['.tep_get_admin_group_name($_SESSION["login_email_address"]) .']';
    			}
				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
					tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $notification_email_subject, $notification_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				}

        		$messageStack->add_session(SUCCESS_CAT_DISCOUNT_DELETED, 'success');
        	} else {
        		$messageStack->add(ERROR_PERFORMED_ACTION_DENIED, 'error');
        	}

	        tep_redirect(tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction'))));

        	break;

	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
<script language="javascript" src="includes/javascript/jquery.form.js"></script>
<script language="javascript" src="includes/javascript/php.packed.js"></script>
<script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
    		</table>
    	</td>
		<!-- body_text //-->
    	<td width="100%" valign="top">
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($_GET['action'] == 'edit' && $edit_customer_group_permission) {
	$customers_groups_query = tep_db_query("select c.customers_groups_id, c.customers_groups_name, c.sort_order, c.customers_groups_payment_methods, c.customers_groups_legend_color, c.customers_groups_extra_sc from " . TABLE_CUSTOMERS_GROUPS . " c  where c.customers_groups_id = '" . $_GET['cID'] . "'");
	$customers_groups = tep_db_fetch_array($customers_groups_query);
	$cInfo = new objectInfo($customers_groups);
?>

<script language="javascript">
<!--
	function check_form() {
  		var error = 0;
  		var customers_groups_name = document.customers.customers_groups_name.value;
  		if (trim_str(customers_groups_name) == "") {
    		error_message = "<?php echo ERROR_CUSTOMERS_GROUPS_NAME; ?>";
    		error = 1;
  		}

  		if (error == 1) {
    		alert(error_message);
    		return false;
  		} else {
    		return true;
  		}
	}
//-->
</script>
      			<tr>
        			<td>
        				<?=tep_draw_form('customers', FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=update', 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td>
			        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          					<tr>
			            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
			            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
				  			<tr>
			        			<td class="formAreaTitle"><?php echo CATEGORY_PERSONAL; ?></td>
			      			</tr>
			      			<tr>
			        			<td class="formArea">
			        				<table border="0" cellspacing="2" cellpadding="2">
			          					<tr>
								            <td class="main"><?=ENTRY_CUSTOMER_GROUP_NAME?></td>
								            <td class="main"><?=tep_draw_input_field('customers_groups_name', $cInfo->customers_groups_name, 'maxlength="32"', false)?></td>
			          					</tr>
			          					<tr>
											<td class="main"><?=ENTRY_GROUPS_LEGEND_COLOR?></td>
											<td class="main">
												<?=tep_draw_input_field('customers_groups_legend_color', $cInfo->customers_groups_legend_color, 'id="customers_groups_legend_color"', false)?>
												<a href="javascript:;" onClick="popUpColorLab('customers_groups_legend_color');">
													<?=tep_image('htmlarea/images/ed_color_bg.gif', '', '', '', 'border="0" unselectable="on"')?>
												</a>
											</td>
										</tr>
                                        <tr>
								            <td class="main"><?=ENTRY_CUSTOMER_GROUP_EXTRA_SC?></td>
								            <td class="main"><?=tep_draw_input_field('extra_sc', $cInfo->customers_groups_extra_sc, 'size="6"')?></td>
			          					</tr>
					  					<tr>
								            <td class="main"><?=ENTRY_SORT_ORDER?></td>
								            <td class="main"><?=tep_draw_input_field('sort_order', $cInfo->sort_order, 'size="6"')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
				  			<tr>
			        			<td class="formAreaTitle"><?=ENTRY_PAYMENT_METHODS?></td>
			      			</tr>
				  			<tr>
			        			<td class="formArea">
<?
				$payment_gateway_opt_arr = array();
				$payment_gateway_array = array();
				$payment_methods_array = array();
				$payment_confirm_complete_info_array = array();

				$payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_title, pm.payment_methods_parent_id,
													pci.payment_configuration_info_key, pcid.payment_configuration_info_value
												FROM " . TABLE_PAYMENT_METHODS . " AS pm
												LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO . " AS pci
													ON pm.payment_methods_id = pci.payment_methods_id
														AND pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE'
												LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " AS pcid
													ON pcid.payment_configuration_info_id = pci.payment_configuration_info_id
														AND pcid.languages_id = '1'
												WHERE pm.payment_methods_receive_status = 1
												ORDER BY pm.payment_methods_title";
				$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
				while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
					if (tep_not_null($payment_methods_row['payment_configuration_info_key'])) {
						$payment_confirm_complete_info_array[(int)$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_configuration_info_value'];
					}
					if ((int)$payment_methods_row['payment_methods_parent_id'] > 0) {
						$payment_methods_array[(int)$payment_methods_row['payment_methods_parent_id']][] = array(	'payment_methods_id' => $payment_methods_row['payment_methods_id'],
																													'payment_methods_title' => $payment_methods_row['payment_methods_title']
																												);
					} else {
						$payment_gateway_array[] = array(	'payment_methods_id' => $payment_methods_row['payment_methods_id'],
															'payment_methods_title' => $payment_methods_row['payment_methods_title']
														);
					}
				}
				tep_db_free_result($payment_methods_result_sql);

				foreach ($payment_gateway_array as $payment_gateway) {
					$payment_gateway_opt_arr[] = array ('payment_gateway' => $payment_gateway,
														'payment_methods' => $payment_methods_array[$payment_gateway['payment_methods_id']]
														);
				}
				unset($payment_gateway_array);
				unset($payment_methods_array);

				$selected_payment_methods_array = array();
				if (isset($cInfo->customers_groups_payment_methods) && count($cInfo->customers_groups_payment_methods)) {
					$selected_payment_methods_array = explode(",", $cInfo->customers_groups_payment_methods);
				}
?>
			        				<table border="0" cellspacing="2" cellpadding="2" width="100%">
										<tr>
						 					<td width="100%" class="main" valign="top">
						 						<ul class="myTree" style="list-style:none;">
<?					foreach ($payment_gateway_opt_arr as $payment_gateway) { ?>
													<li style="list-style:none;" class="treeItem">
														<img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
														<span class="textHolder"><?=$payment_gateway['payment_gateway']['payment_methods_title']?></span>
<?						if (isset($payment_gateway['payment_methods']) && count($payment_gateway['payment_methods'])) { ?>
														<ul style="display:none;list-style:none;">
<?							foreach ($payment_gateway['payment_methods'] as $payment_methods) { ?>
															<li style="list-style:none;" class="treeItem">
																<span class="textHolder">
																	<?
																		echo tep_draw_checkbox_field('zone_payment_gateway_id[]', $payment_methods['payment_methods_id'], (is_array($selected_payment_methods_array) && in_array($payment_methods['payment_methods_id'], $selected_payment_methods_array) ? true : false)) . '&nbsp;';
																		if (isset($payment_confirm_complete_info_array[$payment_methods['payment_methods_id']])) {
																			if ((int)$payment_confirm_complete_info_array[$payment_methods['payment_methods_id']]>0) {
																				echo ' <span class="redIndicator">[rp]</span>';
																			} else {
																				echo ' <span class="greenIndicator">[nrp]</span>';
																			}
																		} else if (isset($payment_confirm_complete_info_array[$payment_gateway['payment_gateway']['payment_methods_id']])) {
																			if ((int)$payment_confirm_complete_info_array[$payment_gateway['payment_gateway']['payment_methods_id']]>0) {
																				echo ' <span class="redIndicator">[rp]</span>';
																			} else {
																				echo ' <span class="greenIndicator">[nrp]</span>';
																			}
																		} else {
																			echo ' <span class="redIndicator">[n/a]</span>';
																		}
																		echo '&nbsp;';
																		echo $payment_methods['payment_methods_title'] . '&nbsp;';
																	?>
																</span>
															</li>
<?							} ?>
														</ul>
<?						} ?>
													</li>
<?					} ?>
												</ul>
								            </td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      			<tr>
			        			<td align="right" class="main"><?=tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link('customers_groups.php', tep_get_all_get_params(array('action', 'subaction', 'cID', 'dis_id'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
			      			</tr>
				  			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '70')?></td>
			      			</tr>
			      		</table>
			      		</form>
			      		<script language="javascript">
							jQuery(document).ready(function() {
								tree = jQuery('#myTree');
								jQuery('img.expandImage', tree.get(0)).click(
									function() {
										if (this.src.indexOf('spacer') == -1) {
											subbranch = jQuery('ul', this.parentNode).eq(0);
											if (subbranch.css('display') == 'none') {
												subbranch.show();
												this.src = 'images/icon-collapse-small.gif';
											} else {
												subbranch.hide();
												this.src = 'images/icon-expand-small.gif';
											}
										}
									}
								);
							});

							function popUpColorLab(item) {
								if (window.showModalDialog) {
									var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
									var selectedColor = window.showModalDialog(url + "popups/select_color.html",null,"resizable:no;help:no;status:no;scroll:no;");
									if (selectedColor)  {
										document.getElementById(item).value = "#"+selectedColor;
										return true;
									}
								} else {
									var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
									var selectedColor = window.open(url + "popups/select_color.html", 'colour', 'width=230,height=165');
									//opener.blockEvents();
								}
								return false;
							}
						</script>
			      	</td>
			    </tr>
<?
} else if ($_GET['action'] == 'new' && $add_customer_group_permission) {
?>
	<script language="javascript">
	<!--
		function check_form() {
  			var error = 0;
  			var customers_groups_name = document.customers.customers_groups_name.value;

  			if (trim_str(customers_groups_name) == '') {
    			error_message = "<?php echo ERROR_CUSTOMERS_GROUPS_NAME; ?>";
    			error = 1;
  			}

  			if (error == 1) {
    			alert(error_message);
    			return false;
  			} else {
    			return true;
  			}
		}

		function popUpColorLab(item) {
			if (window.showModalDialog) {
				var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
				var selectedColor = window.showModalDialog(url + "popups/select_color.html",null,"resizable:no;help:no;status:no;scroll:no;");
				if (selectedColor)  {
					document.getElementById(item).value = "#"+selectedColor;
					return true;
				}
			} else {
				var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
				var selectedColor = window.open(url + "popups/select_color.html", 'colour', 'width=230,height=165');
				//opener.blockEvents();
			}
			return false;
		}
	//-->
	</script>
				<tr>
        			<td>
        				<?=tep_draw_form('customers', FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=newconfirm', 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td>
			        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          					<tr>
			            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
			            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      			<tr>
			        			<td class="formAreaTitle"><?=CATEGORY_PERSONAL?></td>
			      			</tr>
			      			<tr>
			        			<td class="formArea">
			        				<table border="0" cellspacing="2" cellpadding="2">
			          					<tr>
			            					<td class="main"><?=ENTRY_GROUPS_NAME?></td>
			            					<td class="main"><?=tep_draw_input_field('customers_groups_name', '', 'maxlength="32"', false)?></td>
			          					</tr>
										<tr>
											<td class="main"><?=ENTRY_GROUPS_LEGEND_COLOR?></td>
											<td class="main">
												<?=tep_draw_input_field('customers_groups_legend_color', $cInfo->customers_groups_legend_color, 'id="customers_groups_legend_color"', false)?>
												<a href="javascript:;" onClick="popUpColorLab('customers_groups_legend_color');">
													<?=tep_image('htmlarea/images/ed_color_bg.gif', '', '', '', 'border="0" unselectable="on"')?>
												</a>
											</td>
										</tr>
                                        <tr>
								            <td class="main"><?=ENTRY_CUSTOMER_GROUP_EXTRA_SC?></td>
								            <td class="main"><?=tep_draw_input_field('extra_sc', 0, 'size="6"')?></td>
			          					</tr>
					  					<tr>
								            <td class="main"><?=ENTRY_SORT_ORDER?></td>
								            <td class="main"><?=tep_draw_input_field('sort_order', ($cID=='' ? 50000 : $cInfo->sort_order), 'size="6"')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      			<tr>
			        			<td align="right" class="main"><?=tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link('customers_groups.php', tep_get_all_get_params(array('action', 'cID', 'dis_id'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
			      			</tr>
			      		</table>
			      		</form>
			      	</td>
			    </tr>
<?
} else if ($_GET['action'] == 'new_discount' || $_GET['action'] == 'edit_discount') {
	if ($add_discount_permission || $edit_discount_permission) {
		$customers_groups_select_sql = "SELECT c.customers_groups_name, gd.categories_id, gd.customers_groups_discount, gd.customers_groups_rebate,
                                            gd.c2c_customers_groups_discount, gd.c2c_customers_groups_rebate
										FROM " . TABLE_CUSTOMERS_GROUPS . " AS c
										LEFT JOIN " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd
											ON (c.customers_groups_id = gd.customers_groups_id AND gd.customers_groups_discount_id = '" . (int)$_GET['dis_id'] . "')
										WHERE c.customers_groups_id = '" . (int)$_GET['cID'] . "'";
		$customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
		$customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);

		$cInfo = new objectInfo($customers_groups_row);

		$categories_array = tep_get_eligible_category_tree_cacheable(FILENAME_CUSTOMERS_GROUPS, 0, '___', '', $categories_array, false, 0, true);
?>
		<script language="javascript">
		<!--
			function check_form() {
	  			var error = 0;
	  			var error_message = "<?=JS_ERROR?>";
	  			var customers_groups_discount = document.discount_form.cat_id.value;

	  			if (trim_str(customers_groups_discount) == "") {
	    			error_message += "<?=JS_ERROR_NO_CATEGORY_SELECTED?>";
	    			error = 1;
	  			}

				if (trim_str(document.discount_form.customers_groups_discount.value) == "") {
	    			error_message += "<?=JS_ERROR_EMPTY_DISCOUNT?>";
	    			error = 1;
	  			}

	  			if (error == 1) {
	    			alert(error_message);
	    			return false;
	  			} else {
	    			return true;
	  			}
			}
		//-->
		</script>
				<tr>
        			<td>
        				<?=tep_draw_form('discount_form', FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction='.($_GET['action'] == 'edit_discount' ? 'edit_discount_confirm' : 'new_discount_confirm'), 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td>
			        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          					<tr>
			            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
			            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
			      			</tr>
			      			<tr>
			        			<td>
			        				<table border="0" cellspacing="2" cellpadding="2">
			          					<tr>
			            					<td class="main"><?=ENTRY_CUSTOMER_GROUP_NAME?></td>
			            					<td class="main"><?=$cInfo->customers_groups_name?></td>
			            					<?=tep_draw_hidden_field(customers_groups_name,$cInfo->customers_groups_name)?>
			          					</tr>
			          					<tr>
						        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						      			</tr>
						      			<tr>
											<td class="main" width="15%"><?=ENTRY_CATEGORY?></td>
							    			<td class="main">
							    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, isset($cInfo->categories_id) ? $cInfo->categories_id : '', ' id="cat_id"')?>
							    			</td>
										</tr>
										<tr>
						        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						      			</tr>
			          					<tr>
			            					<td class="main"><?=ENTRY_DEFAULT_DISCOUNT?></td>
			            					<td class="main">
                                                <?php
                                                    $_disc = 0;
                                                    if ($cInfo->customers_groups_discount >= 0) {
                                                        $_sign = '+';
                                                        $_disc = $cInfo->customers_groups_discount;
                                                    } else {
                                                        $_sign = '-';
                                                        $_disc = substr($cInfo->customers_groups_discount, 1);
                                                    }
                                                ?>
						   						<select name="customers_groups_discount_sign">
						        					<option name="minus" value="-" <? if ($_sign == '-') echo 'selected="selected"' ?>>-</option>
													<option name="plus" value="+"  <? if ($_sign == '+') echo 'selected="selected"' ?>>+</option>
						   						</select>&nbsp;<?=tep_draw_input_field('customers_groups_discount', $_disc, 'maxlength="9"', false)?>&nbsp;%
											</td>
					  					</tr>
					  					<tr>
						        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						      			</tr>
						      			<tr>
			            					<td class="main"><?=ENTRY_DEFAULT_REBATE?></td>
			            					<td class="main">
                                                <?php
                                                    $_rebate = 0;
                                                    if ($cInfo->customers_groups_rebate >= 0) {
                                                        $_sign = '+';
                                                        $_rebate = $cInfo->customers_groups_rebate;
                                                    } else {
                                                        $_sign = '-';
                                                        $_rebate = substr($cInfo->customers_groups_rebate, 1);
                                                    }
                                                ?>
						   						<select name="customers_groups_rebate_sign">
						        					<option name="minus" value="-" <? if ($_sign == '-') echo 'selected="selected"' ?>>-</option>
													<option name="plus" value="+"  <? if ($_sign == '+') echo 'selected="selected"' ?>>+</option>
						   						</select>&nbsp;<?=tep_draw_input_field('customers_groups_rebate', $_rebate, 'maxlength="9"', false)?>
											</td>
					  					</tr>
			          					<tr>
			            					<td class="main"><?=ENTRY_DEFAULT_C2C_DISCOUNT?></td>
			            					<td class="main">
                                                <?php
                                                    $_c2c_disc = 0;
                                                    if ($cInfo->c2c_customers_groups_discount >= 0) {
                                                        $_sign = '+';
                                                        $_c2c_disc = $cInfo->c2c_customers_groups_discount;
                                                    } else {
                                                        $_sign = '-';
                                                        $_c2c_disc = substr($cInfo->c2c_customers_groups_discount, 1);
                                                    }
                                                ?>
						   						<select name="c2c_customers_groups_discount_sign">
						        					<option name="minus" value="-" <? if ($_sign == '-') echo 'selected="selected"' ?>>-</option>
													<option name="plus" value="+"  <? if ($_sign == '+') echo 'selected="selected"' ?>>+</option>
						   						</select>&nbsp;<?=tep_draw_input_field('c2c_customers_groups_discount', $_c2c_disc, 'maxlength="9"', false)?>&nbsp;%
											</td>
					  					</tr>
					  					<tr>
						        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						      			</tr>
						      			<tr>
			            					<td class="main"><?=ENTRY_DEFAULT_C2C_REBATE?></td>
			            					<td class="main">
                                                <?php
                                                    $_c2c_rebate = 0;
                                                    if ($cInfo->c2c_customers_groups_rebate >= 0) {
                                                        $_sign = '+';
                                                        $_c2c_rebate = $cInfo->c2c_customers_groups_rebate;
                                                    } else {
                                                        $_sign = '-';
                                                        $_c2c_rebate = substr($cInfo->c2c_customers_groups_rebate, 1);
                                                    }
                                                ?>
						   						<select name="c2c_customers_groups_rebate_sign">
						        					<option name="minus" value="-" <? if ($_sign == '-') echo 'selected="selected"' ?>>-</option>
													<option name="plus" value="+"  <? if ($_sign == '+') echo 'selected="selected"' ?>>+</option>
						   						</select>&nbsp;<?=tep_draw_input_field('c2c_customers_groups_rebate', $_c2c_rebate, 'maxlength="9"', false)?>
											</td>
					  					</tr>
					  					<tr>
						        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						      			</tr>
						      			<tr>
			            					<td class="main"></td>
			            					<td class="main">
						   						<? echo ($_GET['action'] == 'edit_discount' ? tep_image_submit('button_update.gif', IMAGE_UPDATE) : tep_image_submit('button_insert.gif', IMAGE_INSERT)) . ' <a href="' . tep_href_link('customers_groups.php', tep_get_all_get_params(array('action', 'cID', 'dis_id'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?>
											</td>
					  					</tr>
			        				</table>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
			      			</tr>
			      		</table>
			      		</form>
<?		if (isset($_REQUEST['dis_id']) && $_GET['action'] == 'edit_discount') { ?>
			      		<form name="discount_form" id="discount_form">
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td class="main">
<?
	$receiving_payment_methods_array = array();
	$receiving_payment_methods_sql = "	SELECT payment_methods_id, payment_methods_parent_id, payment_methods_title
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_receive_status = '1'
											AND payment_methods_parent_id <> 0";
	$receiving_payment_result = tep_db_query($receiving_payment_methods_sql);
	while ($receiving_payment_row = tep_db_fetch_array($receiving_payment_result)) {
		$receiving_payment_methods_array[$receiving_payment_row['payment_methods_parent_id']][$receiving_payment_row['payment_methods_id']] = $receiving_payment_row['payment_methods_title'];
	}

	$payment_gateway_array = array();
	$payment_gateway_sql = "	SELECT payment_methods_id, payment_methods_title
								FROM " . TABLE_PAYMENT_METHODS . "
								WHERE payment_methods_parent_id = 0";
	$payment_gateway_result = tep_db_query($payment_gateway_sql);
	while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result)) {
		$payment_gateway_array[$payment_gateway_row['payment_methods_id']] = $payment_gateway_row['payment_methods_title'];
	}
?>
									Payment Methods:
									<select name="sel_payment_methods" id="sel_payment_methods" onchange="load_customers_groups(this.value)">
										<option value="">[Please select]</option>
<?	foreach ($payment_gateway_array as $payment_gateway_id_loop => $payment_gateway_data_loop) { ?>
										<optgroup label="<?=$payment_gateway_data_loop?>">
<?		if (isset($receiving_payment_methods_array[$payment_gateway_id_loop]) && count($receiving_payment_methods_array[$payment_gateway_id_loop])) {
			foreach ($receiving_payment_methods_array[$payment_gateway_id_loop] as $receiving_payment_methods_id_loop => $receiving_payment_methods_data_loop) { ?>
											<option value="<?=$receiving_payment_methods_id_loop?>"><?=$receiving_payment_methods_data_loop?></option>
<?			}
		}
?>
										</optgroup>
<?	} ?>
									</select>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
			      			</tr>
			      			<tr>
			        			<td class="main">
			        				<form id="extra_op_frm" name="extra_op_frm">
				        				<div id="div_customers_groups_extra_op" style="background-color: #F0F1F1;padding:5px;"></div>
			        				</form>
			        			</td>
			      			</tr>
                            <?php if ($_GET['action'] == 'new_discount') { ?>
			      			<tr>
			        			<td align="right" class="main"><? echo tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link('customers_groups.php', tep_get_all_get_params(array('action', 'cID', 'dis_id'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?></td>
			      			</tr>
                            <?php } ?>
			      		</table>
			      		</form>
			      		<script>
			      			function load_customers_groups(pass_id) {
			      				if (pass_id > 0) {
				      				display_html = pass_id;
									jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
									jQuery.ajax({
										type: "GET",
										dataType: 'xml',
										url: "customer_xmlhttp.php?action=load_customers_groups_extra_op&pmid="+ pass_id +"&id=<?=$_REQUEST['dis_id']?>",
										timeout: 10000,
									    error: function(){
									        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
									    },
										success: function(xml){
											var display_column = 5;
											var selected_all;
											if (jQuery(xml).find('currency[code=*]').length) {
												selected_all = true;
											} else {
												selected_all = false;
											}
											var display_html = 	'<b>' + jQuery(xml).find('title').text() + '</b><br>' +
																'<table class="main">' +
																'	<tr>' +
																'		<td>All</td>'+
																'		<td colspan="'+((display_column*2)-1)+'" align="left">'+
																'			<input type="checkbox" name="chk_all" id="chk_all" onclick="check_all()" ' + (selected_all ? ' checked ' : '') + '> &nbsp; '+
																'			<select name="sel_all" id="sel_all"><option value="+" selected>+</option><option value="-" '+(jQuery(xml).find('currency[code=*]').attr('operator')=='-' ? ' selected ' : '')+'>-</option></select>' +
																'			<input type="text" name="txt_all" id="txt_all" value="'+(selected_all ? jQuery(xml).find('currency[code=*]').text() : '0.00') +'">' +
																'	</td>' +
																'	</tr>';
											if (jQuery(xml).find('currency').length) {
												var count_currency = 0;
												jQuery(xml).find('currency').each(function(){
													if (jQuery(this).attr('code') != '*') {
														count_currency++;
														display_html += '<td class="main">'+jQuery(this).attr('code')+'</td>';
														display_html += '<td class="main">'+
																		'	<select cgeop_id="'+jQuery(this).attr('id')+'" code="'+jQuery(this).attr('code')+'" name="sel_extra_operator['+jQuery(this).attr('code')+']" class="txt_extra_op"><option value="-" selected>-</option><option value="+" '+(jQuery(this).attr('operator')=='+' ? ' selected ' : '')+'>+</option></select>' +
																		'	<input type="text" cgeop_id="'+jQuery(this).attr('id')+'" code="'+jQuery(this).attr('code')+'" name="txt_extra_op['+jQuery(this).attr('code')+']" class="txt_extra_op" value="'+jQuery(this).text()+'">' +
																		'</td>';

														if (count_currency == display_column) {
															display_html += '</tr><tr>';
															count_currency = 0;
														}
													}
												});
											}
											var display_left = display_column - count_currency;
											if (display_column - count_currency > 0) {
												for (count_left=display_left;count_left--;count_left>0) {
													display_html += '<td></td><td></td>';
												}
											}
											display_html += '		</tr>' +
															'		<tr>' +
															'			<td colspan="'+(display_column*2)+'" align="right">'+
															'				<input type="button" value="Update" onclick="submit_extra_op(\''+pass_id+'\')">&nbsp;&nbsp;'+
															'				<input type="button" value="Reset" onclick="load_customers_groups(\''+pass_id+'\')">'+
															'			</td>'+
															'		</tr>' +
															'	</table>';

											jQuery("#div_customers_groups_extra_op").html(display_html);

						      				if (jQuery("#chk_all").attr('checked')) {
						      					jQuery("#txt_all").attr("disabled", false);
						      					jQuery("#sel_all").attr("disabled", false);
						      					jQuery(".txt_extra_op").attr("disabled", true);
						      				} else {
						      					jQuery("#txt_all").attr("disabled", true);
						      					jQuery("#sel_all").attr("disabled", true);
						      					jQuery(".txt_extra_op").attr("disabled", false);
						      				}

											jQuery.unblockUI();
										}
									});
								} else {
									jQuery("#div_customers_groups_extra_op").html('');
								}
			      			}

			      			function submit_extra_op(pass_id) {
								jquery_confirm_box("Are you sure to update?", 2, 0 , "Confirm");
								jQuery('#jconfirm_submit').click(function() {
				      				var submit_data = '';

				      				jquery_confirm_box('<h1>Loading...</h1>', 0, 0);

				      				if (jQuery("#txt_all").val()!='' && !jQuery("#txt_all").attr("disabled")) {
				      					submit_data += "txt_all=" + jQuery("#txt_all").val()+"&sel_all=" + (jQuery("#sel_all").val()!=''? jQuery("#sel_all").val() : '-' );
				      				} else {
					      				jQuery("input.txt_extra_op").each(function(){
					      					if (jQuery(this).attr('cgeop_id')>0) {
					      						submit_data += "txt_extra_op["+jQuery(this).attr('cgeop_id')+"]=" + jQuery(this).val()+"&";
					      					} else {
					      						submit_data += "txt_extra_op["+jQuery(this).attr('code')+"]=" + jQuery(this).val()+"&";
					      					}
					      				});
					      				jQuery("select.txt_extra_op").each(function(){
					      					if (jQuery(this).attr('cgeop_id')>0) {
					      						submit_data += "sel_extra_operator["+jQuery(this).attr('cgeop_id')+"]=" + jQuery(this).val()+"&";
					      					} else {
					      						submit_data += "sel_extra_operator["+jQuery(this).attr('code')+"]=" + jQuery(this).val()+"&";
					      					}
					      				});

				      				}

									jQuery.ajax({
										type: "POST",
										dataType: 'xml',
										url: "customer_xmlhttp.php?action=submit_extra_op&pmid="+ pass_id +"&id=<?=$_REQUEST['dis_id']?>",
										data: submit_data,
										timeout: 10000,
									    error: function(){
									        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
									    },
										success: function(xml){
											load_customers_groups(pass_id);
										}
									});
								});
			      			}

			      			function check_all() {
			      				if (jQuery("#chk_all").attr('checked')) {
			      					jQuery("#txt_all").attr("disabled", false);
			      					jQuery("#sel_all").attr("disabled", false);
			      					jQuery(".txt_extra_op").attr("disabled", true);
			      				} else {
			      					jQuery("#txt_all").attr("disabled", true);
			      					jQuery("#sel_all").attr("disabled", true);
			      					jQuery(".txt_extra_op").attr("disabled", false);
			      				}
			      			}

			      			function check_op_form() {
			      				if (jQuery("#chk_all").attr('checked')) {
			      					if (jQuery("#txt_all").val()=='') {
			      						alert("Invalid OP.");
			      						jQuery("#txt_all").focus();
			      						return false;
			      					}
			      					return true;
			      				}
			      			}

			      		</script>
			      	</td>
			    </tr>
<?		} ?>
<?
	}
} else if ($_GET['action'] == 'aft_edit' && $edit_aft_group_permission) {
    $aftID = (int)$_GET['aftID'];

    $customer_aft_group_select_sql = "SELECT customers_aft_groups_name, sort_order FROM " . TABLE_CUSTOMERS_AFT_GROUPS . " WHERE customers_aft_groups_id = '".$aftID."'";
	$customer_aft_group_result_sql = tep_db_query($customer_aft_group_select_sql);
	$customer_aft_group_row = tep_db_fetch_array($customer_aft_group_result_sql);
?>
    <script language="javascript">
    <!--
        function check_form() {
            var error = 0;
            var customers_groups_name = document.customers_aft_form.customers_aft_groups_name.value;
            if (trim_str(customers_groups_name) == '') {
                error_message = "<?php echo ERROR_CUSTOMERS_GROUPS_NAME; ?>";
                error = 1;
            }

            if (error == 1) {
                alert(error_message);
                return false;
            } else {
                return true;
            }
        }
    //-->
    </script>
      			<tr>
        			<td>
        				<?=tep_draw_form('customers_aft_form', FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=update_aft', 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="50%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td class="pageHeading"><?=HEADING_TITLE_AFT_GROUP?></td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      			<tr>
			        			<td class="formArea">
			        				<table border="0" cellspacing="2" cellpadding="2">
			          					<tr>
								            <td class="main"><?=ENTRY_CUSTOMER_AFT_GROUP_NAME?></td>
								            <td class="main"><?=tep_draw_input_field('customers_aft_groups_name', $customer_aft_group_row['customers_aft_groups_name'], 'maxlength="32"', false)?></td>
			          					</tr>
					  					<tr>
								            <td class="main"><?=ENTRY_SORT_ORDER?></td>
								            <td class="main"><?=tep_draw_input_field('sort_order', $customer_aft_group_row['sort_order'], 'size="6"')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
                            <tr>
                                <td class="main" align="right">
                                    <br /><?php echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton') . ' ' . tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('action', 'aftID', 'tab')).'tab=aft_grp'), '', 'inputButton'); ?>
                                </td>
                            </tr>
                        </table>
                        </form>
                    </td>
                </tr>
<?
} else if ($_GET['action'] == 'aft_new' && $add_aft_group_permission) {
?>
	<script language="javascript">
	<!--
		function check_form() {
  			var error = 0;
  			var customers_groups_name = document.customers_aft_form.customers_aft_groups_name.value;

  			if (trim_str(customers_groups_name) == '') {
    			error_message = "<?php echo ERROR_CUSTOMERS_GROUPS_NAME; ?>";
    			error = 1;
  			}

  			if (error == 1) {
    			alert(error_message);
    			return false;
  			} else {
    			return true;
  			}
		}
	//-->
	</script>
				<tr>
        			<td>
        				<?=tep_draw_form('customers_aft_form', FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=newconfirm_aft', 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="50%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td class="pageHeading"><?=HEADING_TITLE_AFT_GROUP?></td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      			<tr>
			        			<td class="formArea">
			        				<table border="0" cellspacing="2" cellpadding="2">
			          					<tr>
			            					<td class="main"><?=ENTRY_CUSTOMER_AFT_GROUP_NAME?></td>
			            					<td class="main"><?=tep_draw_input_field('customers_aft_groups_name', '', 'maxlength="32"', false)?></td>
			          					</tr>
					  					<tr>
								            <td class="main"><?=ENTRY_SORT_ORDER?></td>
								            <td class="main"><?=tep_draw_input_field('sort_order', 50000, 'size="6"')?></td>
			          					</tr>
			        				</table>
			        			</td>
			      			</tr>
                            <tr>
                                <td class="main" align="right">
                                    <br /><?php echo tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', 'inputButton') . ' ' . tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('action', 'aftID', 'tab')).'tab=aft_grp'), '', 'inputButton'); ?>
                                </td>
                            </tr>
			      		</table>
			      		</form>
			      	</td>
			    </tr>
<?
} else {
    $selected_tab = tep_not_null($_GET['tab']) ? tep_db_prepare_input($_GET['tab']) : 'dis_grp';
?>
      			<tr>
        			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
      			</tr>
                <tr>
                    <td>
                        <div id="customer_grp-tab">
                            <ul>
                                <li class="<?php echo ($selected_tab == 'dis_grp' ? 'ui-tabs-selected' : '')?>">
                                    <a href="#tab-cust-grp"><span>Discount Group</span></a>
                                </li>
                                <li class="<?php echo ($selected_tab == 'aft_grp' ? 'ui-tabs-selected' : '')?>">
                                    <a href="#tab-aft-grp"><span>AFT Group</span></a>
                                </li>
                            </ul>
                            <div id="tab-cust-grp" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
                                <table border="0" width="100%" cellspacing="1" cellpadding="2">
<?php	if ($add_customer_group_permission) { ?>
                                    <tr>
                                        <td align="left"><?='[ <a href="'.tep_href_link(FILENAME_CUSTOMERS_GROUPS, 'action=new').'" >'.LINK_ADD_CUSTOMER_GROUP.'</a> ]'?></td>
                                    </tr>
<?php   }

        $customers_groups_query_raw = "select g.customers_groups_id, g.customers_groups_name, g.sort_order, g.customers_groups_extra_sc from " . TABLE_CUSTOMERS_GROUPS . " g order by g.sort_order, g.customers_groups_name ASC";
        $customers_groups_query = tep_db_query($customers_groups_query_raw);

        switch ($listing) {
            case "id-asc":
                $order = "g.customers_groups_id";
                break;
            case "group":
                $order = "g.customers_groups_name";
                break;
            case "group-desc":
                $order = "g.customers_groups_name DESC";
                break;
            case "discount":
                $order = "g.customers_groups_discount";
                break;
            case "discount-desc":
                $order = "g.customers_groups_discount DESC";
                break;
            default:
                $order = "g.customers_groups_id ASC";
        }
?>
                                    <tr>
                                        <td valign="top">
                                            <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                <tr>
<?
		if($edit_customer_group_permission) {
?>
                                                    <td class="reportBoxHeading" width="5%"><?=TABLE_HEADING_ACTION?></td>
<?
		}
?>
                                                    <td class="reportBoxHeading" width="190"><?=TABLE_HEADING_NAME?></td>
                                                    <td class="reportBoxHeading" width="190"><?=TABLE_HEADING_CUSTOMER_GROUP_EXTRA_SC?></td>
                                                    <td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_SORT_ORDER?></td>
                                                    <td class="reportBoxHeading"><?=TABLE_HEADING_DISCOUNT_SETTING?></td>
                                                </tr>
<?
        $row_count = 0;
        while ($customers_groups = tep_db_fetch_array($customers_groups_query)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd' ;
            $safe_grp_name = htmlspecialchars(addslashes($customers_groups['customers_groups_name']), ENT_QUOTES);
?>
                                                <tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
<?
            if ($edit_customer_group_permission) {
?>
                                                    <td class="reportRecords" valign="top">
                                                        <a href="<?=tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'aftID', 'dis_id', 'action', 'subaction', 'tab')) . 'cID='.$customers_groups['customers_groups_id'].'&action=edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
<?
                if ($customers_groups['customers_groups_id'] != 1) {
?>
                                                        <a href="javascript:void(confirm_delete('<?=$safe_grp_name?>', 'Customer Group', '<?=tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'dis_id', 'action', 'subaction')) . 'cID=' . $customers_groups['customers_groups_id'] . '&subaction=deleteconfirm')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
<?
                }
?>
                                                    </td>
<?php
            }
?>
                                                    <td class="reportRecords" valign="top"><?=$customers_groups['customers_groups_name']?></td>
                                                    <td class="reportRecords"  valign="top"><?=$customers_groups['customers_groups_extra_sc'] ?></td>
                                                    <td class="reportRecords"  valign="top"><?=$customers_groups['sort_order'] ?></td>
                                                    <td>
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
                                                            </tr>
                                                            <tr>
                                                                <td class="subRecordsBoxHeading" valign="top" align="center" width="12%" nowrap>
<?
            if($add_discount_permission) {
?>
                                                                    [<a href="<?=tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'aftID', 'dis_id', 'action', 'subaction')) . 'action=new_discount&cID='.$customers_groups['customers_groups_id'])?>">Add New</a>]
<?
            }
?>
                                                                </td>
                                                                <td class="subRecordsBoxHeading"><?=TABLE_HEADING_CATEGORY?></td>
                                                                <td width="15%" class="subRecordsBoxHeading"><?=TABLE_HEADING_DISCOUNT?></td>
                                                                <td width="15%" class="subRecordsBoxHeading"><?=TABLE_HEADING_REBATE?></td>
                                                                <td width="15%" class="subRecordsBoxHeading"><?=TABLE_HEADING_C2C_DISCOUNT?></td>
                                                                <td width="15%" class="subRecordsBoxHeading"><?=TABLE_HEADING_C2C_REBATE?></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
                                                            </tr>

                                                            <tr>
                                                                <td colspan="6">
                                                                    <a href="javascript:void(0);" onClick="show_customer_group_discount('<?=$customers_groups['customers_groups_id']?>', '<?=TEXT_SHOW?>', '<?=TEXT_HIDE?>');"><span id="toggle_discount_setting_<?=$customers_groups['customers_groups_id']?>"><?=TEXT_SHOW?></span> <?=TEXT_CUSTOMER_GROUP_DISCOUNT?></a>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td colspan="6">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="0" id="customer_group_discount_info_<?=$customers_groups['customers_groups_id']?>">
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
<?          $row_count++;
        }
?>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div id="tab-aft-grp" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
                                <table border="0" width="100%" cellspacing="1" cellpadding="2">
<?php	if ($add_aft_group_permission) { ?>
                                    <tr>
                                        <td align="left"><?='[ <a href="'.tep_href_link(FILENAME_CUSTOMERS_GROUPS, 'action=aft_new').'" >'.LINK_ADD_CUSTOMER_AFT_GROUP.'</a> ]'?></td>
                                    </tr>
<?php   } ?>
<?php
        $customers_aft_groups_select_sql = "select customers_aft_groups_id, customers_aft_groups_name, sort_order from " . TABLE_CUSTOMERS_AFT_GROUPS . " order by sort_order";
        $customers_aft_groups_result_sql = tep_db_query($customers_aft_groups_select_sql);
?>
                                    <tr>
                                        <td valign="top">
                                            <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                <tr>
<?
		if ($edit_aft_group_permission) {
?>
                                                    <td class="reportBoxHeading" width="5%"><?=TABLE_HEADING_ACTION?></td>
<?
		}
?>
                                                    <td class="reportBoxHeading" width="190"><?=TABLE_HEADING_NAME?></td>
                                                    <td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_SORT_ORDER?></td>
                                                </tr>
<?
        $row_count = 0;
        while ($customers_aft_groups_row = tep_db_fetch_array($customers_aft_groups_result_sql)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd' ;
            $safe_grp_name = htmlspecialchars(addslashes($customers_aft_groups_row['customers_aft_groups_name']), ENT_QUOTES);
?>
                                                <tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
<?
            if ($edit_aft_group_permission) {
?>
                                                    <td class="reportRecords" valign="top">
                                                        <a href="<?=tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'aftID', 'dis_id', 'action', 'subaction', 'tab')) . 'aftID='.$customers_aft_groups_row['customers_aft_groups_id'].'&action=aft_edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
<?
                if ($customers_aft_groups_row['customers_aft_groups_id'] != 1) {
?>
                                                        <a href="javascript:void(confirm_delete('<?=$safe_grp_name?>', 'AFT Group', '<?=tep_href_link(FILENAME_CUSTOMERS_GROUPS, tep_get_all_get_params(array('cID', 'aftID', 'dis_id', 'action', 'subaction')) . 'aftID=' . $customers_aft_groups_row['customers_aft_groups_id'] . '&subaction=del_confirm_aft')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
<?
                }
?>
                                                    </td>
<?
            }
?>
                                                    <td class="reportRecords" valign="top"><?=$customers_aft_groups_row['customers_aft_groups_name']?></td>
                                                    <td class="reportRecords"  valign="top"><?=$customers_aft_groups_row['sort_order'] ?></td>
                                                </tr>
<?          $row_count++;
        }
?>
                                            </table>
                                        </td>
                                    </tr>
<?
}
?>
                                </table>
                            </div>
                        </div>
                    </td>
                </tr>
    		</table>
    	</td>
		<!-- body_text_eof //-->
  	</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<script type="text/javascript">
jQuery(document).ready(function() {
	jQuery("#customer_grp-tab > ul").tabs();
});
</script>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
