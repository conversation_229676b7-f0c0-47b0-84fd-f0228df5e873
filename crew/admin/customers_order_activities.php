<?
/*
 	$Id: customers_order_activities.php,v 1.6 2010/08/24 08:09:02 weichen Exp $
	
 	Copyright (c) 2007 Dynamic Podium
	
 	Released under the GNU General Public License
*/


require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'customers_order_activities.php');
include(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');

tep_set_time_limit(300);

$currencies = new currencies();
$coa_object = new customers_order_activities($login_id, $login_email_address);

$view_coa_report_permission = tep_admin_files_actions(FILENAME_CUSTOMERS_ORDER_ACTIVITIES, 'CUSTOMERS_ORDER_ACTIVITIES_REPORT');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch($action) {
	case "show_report":
		$header_title = HEADER_FORM_CUSTOMERS_ORDER_ACTIVITIES_TITLE;
		$form_content = $coa_object->show_coa(FILENAME_CUSTOMERS_ORDER_ACTIVITIES, 'coa_inputs', $_REQUEST, $messageStack);

		break;
	case 'export_csv':
		$export_csv_content = $coa_object->export_coa(FILENAME_CUSTOMERS_ORDER_ACTIVITIES, 'coa_inputs');
		if (tep_not_null($export_csv_content)) {
			$filename = str_replace(".php", "_", FILENAME_CUSTOMERS_ORDER_ACTIVITIES) . date('YmdHis') . '.csv';
			$mime_type = 'text/x-csv';
			// Download
			header('Content-Type: ' . $mime_type);
			header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			// IE need specific headers
			if (PMA_USR_BROWSER_AGENT == 'IE') {
				header('Content-Disposition: inline; filename="' . $filename . '"');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Pragma: public');
			} else {
				header('Content-Disposition: attachment; filename="' . $filename . '"');
				header('Pragma: no-cache');
			} //end if
			echo $export_csv_content;
			exit();
		} else {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			tep_redirect(tep_href_link(FILENAME_CUSTOMERS_ORDER_ACTIVITIES));
		} //end if

		break;
	case "reset_session":
    	unset($_SESSION['coa_inputs']);
    	tep_redirect(tep_href_link(FILENAME_CUSTOMERS_ORDER_ACTIVITIES));
    	
    	break;
	default:
		$header_title = HEADER_FORM_CUSTOMERS_ORDER_ACTIVITIES_TITLE;
		$form_content = $coa_object->search_coa(FILENAME_CUSTOMERS_ORDER_ACTIVITIES, 'coa_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
<?	if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); } ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
