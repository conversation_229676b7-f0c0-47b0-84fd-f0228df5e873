<?
require('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

$sid = (isset($_REQUEST['sid']) ? $_REQUEST['sid'] : '');
$cgid = (isset($_REQUEST['cgid']) ? $_REQUEST['cgid'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "update":
			$conf_sql_data_array = array( 	'admin_groups_id' => (isset($_POST['user_grp_to']) && count($_POST['user_grp_to']) ? implode(",", $_POST['user_grp_to']) : ''),
											'discount_setting_admin_groups_id' => (isset($_POST['discount_setting_group_to']) && count($_POST['discount_setting_group_to']) ? implode(",", $_POST['discount_setting_group_to']) : ''),
											'discount_setting_notification' => (isset($_POST['discount_setting_notification']) ? tep_db_prepare_input($_POST['discount_setting_notification']) : '')
										);
			tep_db_perform(TABLE_SITE_CUSTOMERS_ACCESS, $conf_sql_data_array, 'update', " site_id = '" . (int)$sid . "' AND customers_groups_id = '" . (int)$cgid . "'");
			tep_redirect(tep_href_link(FILENAME_CUSTOMERS_TYPE_CONF));
			break;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
					</tr>
					<tr>
						<td>
<?
	if ($action == 'edit') {
		$user_groups_from_array = array( array ('id' => '', "text" => 'Select User Groups', "type" => 'optgroup') );
		$user_groups_to_array = array( array ('id' => '', "text" => 'Selected User Groups', "type" => 'optgroup') );
		$discount_setting_groups_from_array = array( array ('id' => '', "text" => 'Select User Groups', "type" => 'optgroup') );
		$discount_setting_groups_to_array = array( array ('id' => '', "text" => 'Selected User Groups', "type" => 'optgroup') );
		
		$store_select_sql = " 	SELECT sca.site_id, sca.customers_groups_id, sca.admin_groups_id, sca.discount_setting_admin_groups_id, sca.discount_setting_notification, cg.customers_groups_name, sc.site_name 
								FROM " . TABLE_SITE_CUSTOMERS_ACCESS . " AS sca 
								INNER JOIN " . TABLE_SITE_CODE . " AS sc 
									ON (sc.site_id = sca.site_id)
								LEFT JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
									ON (cg.customers_groups_id = sca.customers_groups_id) 
								WHERE sca.site_id = '" . (int)$sid . "' 
									AND sca.customers_groups_id = '" . (int)$cgid . "';";
		
		$store_result_sql = tep_db_query($store_select_sql);
		if ($store_row = tep_db_fetch_array($store_result_sql)) {
			if (tep_not_null($store_row['admin_groups_id'])) {
				$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id NOT IN (".$store_row['admin_groups_id'].") ORDER BY admin_groups_name";
				$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
				while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
					$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
														'text' => $unassigned_user_row['admin_groups_name']
														);
				}
				
				$assigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_row['admin_groups_id'].") ORDER BY admin_groups_name";
				$assigned_user_result_sql = tep_db_query($assigned_user_select_sql);
				while ($assigned_user_row = tep_db_fetch_array($assigned_user_result_sql)) {
					$user_groups_to_array[] = array(	'id' => $assigned_user_row['admin_groups_id'],
														'text' => $assigned_user_row['admin_groups_name']
														);
				}
			} else {
				$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS;
				$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
				while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
					$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
														'text' => $unassigned_user_row['admin_groups_name']
														);
				}
			}
			
			if (tep_not_null($store_row['discount_setting_admin_groups_id'])) {
				$unassigned_discount_setting_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id NOT IN (".$store_row['discount_setting_admin_groups_id'].") ORDER BY admin_groups_name";
				$unassigned_discount_setting_user_result_sql = tep_db_query($unassigned_discount_setting_user_select_sql);
				while ($unassigned_discount_setting_user_row = tep_db_fetch_array($unassigned_discount_setting_user_result_sql)) {
					$discount_setting_groups_from_array[] = array(	'id' => $unassigned_discount_setting_user_row['admin_groups_id'],
																	'text' => $unassigned_discount_setting_user_row['admin_groups_name']
																	);
				}
				
				$assigned_discount_setting_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_row['discount_setting_admin_groups_id'].") ORDER BY admin_groups_name";
				$assigned_user_result_sql = tep_db_query($assigned_discount_setting_user_select_sql);
				while ($assigned_discount_setting_user_row = tep_db_fetch_array($assigned_user_result_sql)) {
					$discount_setting_groups_to_array[] = array(	'id' => $assigned_discount_setting_user_row['admin_groups_id'],
																	'text' => $assigned_discount_setting_user_row['admin_groups_name']
																	);
				}
			} else {
				$unassigned_discount_setting_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS;
				$unassigned_discount_setting_user_result_sql = tep_db_query($unassigned_discount_setting_user_select_sql);
				while ($unassigned_discount_setting_user_row = tep_db_fetch_array($unassigned_discount_setting_user_result_sql)) {
					$discount_setting_groups_from_array[] = array(	'id' => $unassigned_discount_setting_user_row['admin_groups_id'],
																	'text' => $unassigned_discount_setting_user_row['admin_groups_name']
																	);
				}
			}
			
						echo tep_draw_form('customers_type_conf_form', FILENAME_CUSTOMERS_TYPE_CONF, tep_get_all_get_params(array('action')) . 'action=update', 'post', 'onSubmit="return form_checking();"');
?>
							<table width="100%" border="0" cellspacing="2" cellpadding="1">
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main" valign="top"><?=TABLE_HEADING_SITE?></td>
									<td class="main" valign="top"><?=$store_row['site_name']?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main" valign="top"><?=TABLE_HEADING_CUSTOMER_GROUP?></td>
									<td class="main" valign="top"><?=$store_row['customers_groups_name']?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main" valign="top"><?=TABLE_HEADING_EDIT_CUSTOMER_PROFILE?></td>
									<td class="main" valign="top"><?=tep_draw_js_select_boxes('user_grp', $user_groups_from_array, $user_groups_to_array, ' size="10" style="width:20em;"')?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main" valign="top"><?=TABLE_HEADING_EDIT_DISCOUNT_SETTING?></td>
									<td class="main" valign="top"><?=tep_draw_js_select_boxes('discount_setting_group', $discount_setting_groups_from_array, $discount_setting_groups_to_array, ' size="10" style="width:20em;"')?></td>
								</tr>
								<tr>
									<td class="main" valign="top"><?=TABLE_HEADING_EDIT_DISCOUNT_SETTING_NOTIFICATION?></td>
									<td class="main" valign="top"><?=tep_draw_textarea_field('discount_setting_notification', 'soft', '60', '5', $store_row['discount_setting_notification'])?></td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<input type="button" name="update" value="Update" class="inputButton" onClick="return button_lock();">&nbsp;&nbsp;
	            						<input type="button" name="cancel" value="Cancel" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_CUSTOMERS_TYPE_CONF)?>'">
									</td>
									<script language="javascript">
										<!--	
										function form_checking() {
											var selected_grp = document.customers_type_conf_form.elements['user_grp_to[]'];
											var selected_discount_setting_grp = document.customers_type_conf_form.elements['discount_setting_group_to[]'];
											if (selected_grp != null) {
												for (x=0; x<(selected_grp.length); x++) {
							    					selected_grp.options[x].selected = true;
							  					}
						  					}
						  					if (selected_discount_setting_grp != null) {
												for (x=0; x<(selected_discount_setting_grp.length); x++) {
							    					selected_discount_setting_grp.options[x].selected = true;
							  					}
						  					}
						  				}
						  				
										function button_lock() {
						  					document.customers_type_conf_form.update.disabled = true;
						  					form_checking();
						  					document.customers_type_conf_form.submit();
							  			}
					  					//-->
									</script>
								</tr>
							</table>
						</form>
<?
		} else {
			echo 'Record Not Found!';
		}
	} else {
?>	
							<table border="0" width="80%" cellspacing="1" cellpadding="4">
								<tr>
									<td class="ordersBoxHeading" width="10%"><?=TABLE_HEADING_SITE?></td>
									<td class="ordersBoxHeading" width="10%"><?=TABLE_HEADING_CUSTOMER_GROUP?></td>
									<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_EDIT_CUSTOMER_PROFILE?></td>
									<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_EDIT_DISCOUNT_SETTING?></td>
									<td class="ordersBoxHeading" width="22%"><?=TABLE_HEADING_NOTIFICATION?></td>
									<td class="ordersBoxHeading" align="center" width="8%"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?
		$row_count = 0;
		$current_status='';
		$acc_row_buffer = '';
		$new_section = false;
		
		$store_info_select_sql = "	SELECT sca.site_id, sca.customers_groups_id, sca.admin_groups_id, sca.discount_setting_admin_groups_id, sca.discount_setting_notification, cg.customers_groups_name, sc.site_name 
									FROM " . TABLE_SITE_CUSTOMERS_ACCESS . " AS sca 
									INNER JOIN " . TABLE_SITE_CODE . " AS sc 
										ON (sc.site_id = sca.site_id)
									LEFT JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
										ON (cg.customers_groups_id = sca.customers_groups_id)
									ORDER BY sca.site_id, cg.sort_order, cg.customers_groups_name ASC";
		
		$store_info_result_sql = tep_db_query($store_info_select_sql);
		
		while ($store_info_row = tep_db_fetch_array($store_info_result_sql)) {
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
			
			$user_group_string_array = array();
			if (tep_not_null($store_info_row['admin_groups_id'])) {
				$user_group_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_info_row['admin_groups_id'].") ORDER BY admin_groups_name";
				//echo $user_group_select_sql;
				$user_group_result_sql = tep_db_query($user_group_select_sql);
				while ($user_group_row = tep_db_fetch_array($user_group_result_sql)) {
					$user_group_string_array[] = $user_group_row['admin_groups_name'];
				}
			}
			
			$discount_setting_group_string_array = array();
			if (tep_not_null($store_info_row['discount_setting_admin_groups_id'])) {
				$discount_setting_group_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_info_row['discount_setting_admin_groups_id'].") ORDER BY admin_groups_name";
				$discount_setting_group_result_sql = tep_db_query($discount_setting_group_select_sql);
				while ($discount_settingr_group_row = tep_db_fetch_array($discount_setting_group_result_sql)) {
					$discount_setting_group_string_array[] = $discount_settingr_group_row['admin_groups_name'];
				}
			}
			
			if ($current_status != $store_info_row['site_name']) {
    			$current_status = $store_info_row['site_name'];
    			$new_section = true;
    		} else {
    			$new_section = false;
    		}
    		ob_start();
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')";>
<?
			if ($new_section) {
				echo				'<td class="reportRecords" valign="top" rowspan="##MAIN_ROW_SPAN##" >' . $store_info_row['site_name'] . '</td>';
			}
?>
									<td class="reportRecords" valign="top"><?=$store_info_row['customers_groups_name']?></td>
									<td class="reportRecords" valign="top"><?=implode('<br>', $user_group_string_array)?></td>
									<td class="reportRecords" valign="top"><?=implode('<br>', $discount_setting_group_string_array)?></td>
									<td class="reportRecords" valign="top"><?=nl2br(htmlentities($store_info_row['discount_setting_notification']))?></td>
									<td class="reportRecords" align="center" valign="top">
										<a href="<?=tep_href_link(FILENAME_CUSTOMERS_TYPE_CONF, 'action=edit&sid=' . $store_info_row['site_id']. '&cgid=' . $store_info_row['customers_groups_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
									</td>
								</tr>
<?	
			$row_buffer = ob_get_contents();
			ob_end_clean();
			if ($new_section) {
				echo str_replace("##MAIN_ROW_SPAN##", $main_row_span, $acc_row_buffer);
				$acc_row_buffer = $row_buffer;				
				$main_row_span = 1;
			} else {
				$acc_row_buffer .= "\n" . $row_buffer;
				$main_row_span++;
			}
			$row_count++;
		}
		if (tep_not_null($acc_row_buffer)) {
			echo str_replace("##MAIN_ROW_SPAN##", $main_row_span, $acc_row_buffer);
		}
	}
?>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>
	<script language="javascript"><!--
		function showOverEffect(object, class_name, extra_row) {
			rowOverEffect(object, class_name);
			var rowObjArray = extra_row.split('##');
		  	for (var i = 0; i < rowObjArray.length; i++) {
		  		if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
		  			rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
		  		}
		  	}
		}
		
		function showOutEffect(object, class_name, extra_row) {
			rowOutEffect(object, class_name);
			var rowObjArray = extra_row.split('##');
			for (var i = 0; i < rowObjArray.length; i++) {
				if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
				}
			}
		}
		
		function showClicked(object, class_name, extra_row) {
			rowClicked(object, class_name);
			var rowObjArray = extra_row.split('##');
	  		for (var i = 0; i < rowObjArray.length; i++) {
	  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
	  			}
			}
		}
	//-->
	</script>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>