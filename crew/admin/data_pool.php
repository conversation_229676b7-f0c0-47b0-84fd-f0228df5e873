<?php
require('includes/application_top.php');

tep_set_time_limit(1000);

function filter_selection($var) {
	return ($var && $var >= 1);
}

$parent_id = (int)$_REQUEST['parent_id'];
$id = (int)$_REQUEST['id'];
$ref_id = (int)$_REQUEST['ref_id'];
$action = strtolower($_REQUEST['action']);
$template_id = (int)$_REQUEST['template_id'];
$brackets_groups_id = (int)$_REQUEST['brackets_groups_id'];
$error_code = $_GET['error_code'];

if (strtolower($_REQUEST['action'])=="bracket_group" || strpos(strtolower($_REQUEST['action']),"bracket_group")) {
	$action_type = "bracket_group";
} else {
	$action_type = "";
}

$type = "PL";	//POWER_LEVELING, this variable will be passed dynamically later
define('BRACKETS_PER_ROW', 10);

$field_type_array = array(	array	('id'=>'1', 'text'=>'Text Box'),
							array	('id'=>'2', 'text'=>'Text Area'),
							array	('id'=>'3', 'text'=>'Dropdown Menu'),
							array	('id'=>'4', 'text'=>'Radio Button'),
							array	('id'=>'5', 'text'=>'Date Selection'),
							array	('id'=>'6', 'text'=>'Display Label'),
							array	('id'=>'7', 'text'=>'Information Text')
						);

function tep_get_bracket_roots($data_pool_id, $type='data_pool_level_tags_id') {
	$data_pool_id = (int)$data_pool_id;
	$ret = array();
	
	$bracket_tags_select_sql = "SELECT * FROM " . TABLE_BRACKETS_TAGS . " 
						    	WHERE $type ='" . $data_pool_id . "' AND 
								brackets_tags_key ='" . KEY_PL_BRACKET . "';";
	$bracket_tags_result_sql = tep_db_query($bracket_tags_select_sql);
	
	while ($bracket_tags_row = tep_db_fetch_array($bracket_tags_result_sql)) {
		array_push($ret, $bracket_tags_row);
	}
	
	return $ret;
}

function tep_get_bracket_value($bracket_id, $tag=KEY_PL_END_LEVEL, $macth_operator='=') {
	$bracket_value_select_sql = "	SELECT brackets_tags_value FROM " . TABLE_BRACKETS_TAGS . " 
						    		WHERE brackets_tags_dependent ='" . (int)$bracket_id . "' AND brackets_tags_key ".$macth_operator." '" . $tag . "'";
	$bracket_value_result_sql = tep_db_query($bracket_value_select_sql);
	
	if ($bracket_value_row = tep_db_fetch_array($bracket_value_result_sql)) {
		return $bracket_value_row['brackets_tags_value'];
	} else {
		return false;
	}
}

function tep_get_min_bracket_level($id,$type='data_pool_level_tags_id') {
	$id = (int)$id;
	
	$result = tep_db_query("select min(CAST(brackets_tags_value as SIGNED)) as val from ".TABLE_BRACKETS_TAGS." 
						    where $type='$id' and 
							brackets_tags_key='".KEY_PL_END_LEVEL."';");
	
	if ($row = tep_db_fetch_array($result)) {
		return (int)$row['val'];
	} else {
		return 0;			
	}
}

function tep_bubble_sort($arr) {
	$size = sizeof($arr);
	
	if ($size==0) {
		return array();
	}
	
	for ($i=($size - 1); $i >= 0; $i--) {
		for ($j = 1; $j <= $i; $j++) {
			if ($arr[$j-1][KEY_PL_END_LEVEL] > $arr[$j][KEY_PL_END_LEVEL]) {
				$temp = $arr[$j-1];
				$arr[$j-1] = $arr[$j];
				$arr[$j] = $temp;
			}
		}
	}
	
	return $arr;
}

function tep_create_system_options($template_id) {
	
	if (tep_not_null($template_id)) {
		$option_sql_data_array = array(	'data_pool_template_id' => $template_id,
		       							'data_pool_options_title' => SYSTEM_PRICE_ETA_OPTION_TITLE,
		       							'data_pool_options_class' => 'GENERAL',
		       							'data_pool_options_input_type' => 999,
		       							'data_pool_options_input_size' => 'NULL',
		       							'data_pool_options_required' => 1,
		       							'data_pool_options_sort_order' => 50000
	           						);
		tep_db_perform(TABLE_DATA_POOL_OPTIONS_TAGS, $option_sql_data_array);
		$option_id = tep_db_insert_id();
		
		$option_values_sql_data_array = array(	'data_pool_options_values_id' => 1,
				        	       				'data_pool_options_id' => $option_id,
	        	       							'data_pool_options_values_min_level' => 0,
	        	       							'data_pool_options_values_max_level' => 0,
	                   						);
		tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES_TAGS, $option_values_sql_data_array);
	}
}

if (strtolower($_SERVER['REQUEST_METHOD'])=='post' || substr($action,0,3)=="do_") {
	switch ($action) {
		//case "insert":
		case "edit":
			if ($id != 0) {
				/******************************************************************************************
					Update data pool record
				******************************************************************************************/
				$selection_group_name = tep_db_prepare_input($_POST['selection_group_name']);
				$selection_group_description = tep_db_prepare_input($_POST['selection_group_description']);
				$selection_group_input_field = tep_db_prepare_input($_POST['selection_group_input_field']);
				
				/******************************************************************************************
					Update existing selection record(s)
				******************************************************************************************/
				$selection_name_update = $_POST['selection_name_update'];
				$selection_sort_order_update = $_POST['selection_sort_order_update'];
				$selection_min_level_update = $_POST['selection_min_level_update'];
				$selection_max_level_update = $_POST['selection_max_level_update'];
				
				$current_selection_group_id = (int)$_POST['selection_group_id'];	// Data pool id
				
				if (is_array($selection_name_update) && $current_selection_group_id > 0) {
					if (tep_not_null($selection_group_name)) {
						$data_pool_data_array = array(	'data_pool_name' => $selection_group_name,
                                  						'data_pool_description' => $selection_group_description,
                                  						'data_pool_input_field' => $selection_group_input_field
                                  					);
						tep_db_perform(TABLE_DATA_POOL, $data_pool_data_array, 'update', "data_pool_id ='" . $current_selection_group_id . "'");
					}
					
					$selection_name_update_keys = array_keys($selection_name_update);
					foreach ($selection_name_update_keys as $key) {
						$selection_name_update[$key] = tep_db_prepare_input($selection_name_update[$key]);
						
						if (tep_not_null($selection_name_update[$key])) {
							$selection_sort_order_update[$key] = (int)$selection_sort_order_update[$key];
							$selection_min_level_update[$key] = (int)$selection_min_level_update[$key];
							$selection_max_level_update[$key] = (int)$selection_max_level_update[$key];
							
							$level_tags_data_array = array(	'data_pool_level_name' => $selection_name_update[$key],
	                                  						'data_pool_sort_order' => $selection_sort_order_update[$key],
	                                  						'data_pool_min_level' => $selection_min_level_update[$key],
	                                  						'data_pool_max_level' => $selection_max_level_update[$key]
	                                  					);
							tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $level_tags_data_array, 'update', "data_pool_level_tags_id ='" . $key . "'");
						}
					}
				}
				
				$selection_name = $_POST['selection_name'];
				$selection_sort_order = $_POST['selection_sort_order'];
				$selection_min_level = $_POST['selection_min_level'];
				$selection_max_level = $_POST['selection_max_level'];
				
				$first_time = true;
				
				if (is_array($selection_name)) {
					for ($x=0; $x < sizeof($selection_name); $x++) {
						$selection_name[$x] = tep_db_prepare_input($selection_name[$x]);
						
						if (tep_not_null($selection_name[$x])) {
							$selection_sort_order[$x] = (int)$selection_sort_order[$x];
							$selection_min_level[$x] = (int)$selection_min_level[$x];
							$selection_max_level[$x] = (int)$selection_max_level[$x];
							
							/***************************************************************************
								If there is no any data pool defined for this selection group yet,
								insert new selection group, aka datapool.
							***************************************************************************/
							if ((int)$current_selection_group_id <= 0) {
								if (tep_not_null($selection_group_name)) {
									$selection_group_class_name = tep_gen_random_key(str_replace(" " , "_" , $selection_group_name), TABLE_DATA_POOL, "data_pool_class");
									
									$data_pool_data_array = array(	'data_pool_name' => $selection_group_name,
																	'data_pool_class' => $selection_group_class_name,
																	'data_pool_description' => $selection_group_description,
																	'data_pool_input_field' => $selection_group_input_field
																);
									tep_db_perform(TABLE_DATA_POOL, $data_pool_data_array);
									$current_selection_group_id = tep_db_insert_id();
									
									$first_time = false;
								}
							}
							
							if ((int)$current_selection_group_id > 0) {
								// if first time then
								if ($first_time) {
									if (tep_not_null($selection_group_name)) {
										$data_pool_data_array = array(	'data_pool_name' => $selection_group_name,
				                                  						'data_pool_description' => $selection_group_description,
				                                  						'data_pool_input_field' => $selection_group_input_field
				                                  					);
										tep_db_perform(TABLE_DATA_POOL, $data_pool_data_array, 'update', "data_pool_id ='" . $current_selection_group_id . "'");
									}
									
									$first_time = false;
								}
								
								$arr = array('data_pool_id' => (int)$current_selection_group_id);
								tep_db_perform(TABLE_DATA_POOL_REF, $arr);
								$lid = tep_db_insert_id();
								
								$arr = array('data_pool_level_parent_id' => $id,
										 	 'data_pool_level_name' => $selection_name[$x],
										 	 'data_pool_min_level' => $selection_min_level[$x],
										 	 'data_pool_max_level' => $selection_max_level[$x],
										 	 'data_pool_ref_id' => $lid,
										 	 'data_pool_sort_order' => $selection_sort_order[$x]
											);
								tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $arr);
							}
						}
					}
				}
				
				tep_redirect(tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params()));
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params()));
			break;
		
		case "do_import_selections":
			$inc_subcat = (int)$_GET['inc_subcat'];	// Is "Include Subcategories" is checked
			$import_from_id = (int)$_GET['import_from_id'];	// Starting import from which level tag id
			$current_selection_group_id = (int)$_GET['selection_group_id'];	//	Current data pool id
			
			if ($import_from_id) {
				if ($inc_subcat) {
					tep_import_selections($import_from_id, $id, $current_selection_group_id, $id);				
				} else {
					$import_level_tags_child_select_sql = "	SELECT a.data_pool_level_name, a.data_pool_level_value, a.data_pool_sort_order 
															FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " AS a 
															WHERE a.data_pool_level_parent_id ='" . $import_from_id . "';";
					$import_level_tags_child_result_sql = tep_db_query($import_level_tags_child_select_sql);
					
					while ($import_level_tags_child_row = tep_db_fetch_array($import_level_tags_child_result_sql)) {
						if ((int)$current_selection_group_id <= 0) {
							$selection_group_class_name = tep_gen_random_key("untitled_group", TABLE_DATA_POOL, "data_pool_class");
							
							$selection_group_name = tep_gen_random_key("Untitled Group ", TABLE_DATA_POOL, "data_pool_name");
							
							$data_pool_data_array = array(	'data_pool_name' => $selection_group_name,
														 	'data_pool_class' => $selection_group_class_name,
														 	'data_pool_description' => "Untitled Selection Group",
														 	'data_pool_input_field' => "radio"
														);
							
							tep_db_perform(TABLE_DATA_POOL, $data_pool_data_array);
							$current_selection_group_id = tep_db_insert_id();
						}
						
						if ($current_selection_group_id) {
							$arr = array('data_pool_id' => $current_selection_group_id);
							tep_db_perform(TABLE_DATA_POOL_REF, $arr);
							$lid = tep_db_insert_id();
							
							$import_level_tags_child_row['data_pool_level_parent_id'] = $id;
							$import_level_tags_child_row['data_pool_ref_id'] = $lid;
							
							tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $import_level_tags_child_row);
						}
					}
				}
			}
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('action', 'inc_subcat', 'import_from_id', 'selection_group_id'))."action=edit"));
			break;
		
		case "do_delete":
			tep_data_pool_delete($id);	
			
			$sql_ref_datapool = "select data_pool_id from ".TABLE_DATA_POOL_REF." where data_pool_ref_id='$ref_id';";
			$result_ref_datapool = tep_db_query($sql_ref_datapool);
			
			if ($row_ref_datapool = tep_db_fetch_array($result_ref_datapool)) {
				$ref_data_pool_id = (int)$row_ref_datapool['data_pool_id'];		
			}
			
			tep_db_query("DELETE from ".TABLE_DATA_POOL_REF." where data_pool_ref_id = '$ref_id';");
			
			// check if still datapool id exists, if it does not exist, remove the main datapool, simple as that		
			$sql_datapool = "SELECT data_pool_ref_id from ".TABLE_DATA_POOL_REF." WHERE data_pool_id = '$ref_data_pool_id';";
			$sql_datapool_result = tep_db_query($sql_datapool);
			
			if (tep_db_num_rows($sql_datapool_result)==0) {
				tep_db_query("delete from ".TABLE_DATA_POOL." WHERE data_pool_id = '$ref_data_pool_id';");				
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL,"id=".(int)$_GET['parent_id']."&ref_id=$ref_id&action=edit&path=$_GET[path]&template_id=".$template_id));
			break;
		
		case "do_delete_all":
			$parent_id = (int)$_GET['parent_id'];
			$sql = "select * from ".TABLE_DATA_POOL_LEVEL_TAGS." where data_pool_level_parent_id='$parent_id';";
			$result = tep_db_query($sql);
			
			while ($row=tep_db_fetch_array($result)) {
				tep_data_pool_delete((int)$row['data_pool_level_tags_id']);	
				
				$sql_ref_datapool = "select data_pool_id from ".TABLE_DATA_POOL_REF." where data_pool_ref_id='$row[data_pool_ref_id]';";
				$result_ref_datapool = tep_db_query($sql_ref_datapool);
				
				if ($row_ref_datapool = tep_db_fetch_array($result_ref_datapool)) {
					$ref_data_pool_id = (int)$row_ref_datapool['data_pool_id'];
				}
				
				tep_db_query("DELETE from ".TABLE_DATA_POOL_REF." where data_pool_ref_id = '$row[data_pool_ref_id]';");
				
				// check if still datapool id exists, if it does not exist, remove the main datapool, simple as that		
				$sql_datapool = "SELECT data_pool_ref_id from ".TABLE_DATA_POOL_REF." WHERE data_pool_id = '$ref_data_pool_id';";
				$sql_datapool_result = tep_db_query($sql_datapool);
				
				if (tep_db_num_rows($sql_datapool_result)==0) {
					tep_db_query("delete from ".TABLE_DATA_POOL." WHERE data_pool_id = '$ref_data_pool_id';");				
				}
			}
			tep_redirect(tep_href_link(FILENAME_DATA_POOL,"id=".$parent_id."&ref_id=$ref_id&action=edit&path=$_GET[path]&template_id=".$template_id));
			break;
		
		case "do_delete_bracket_group":
			tep_db_query("DELETE FROM " . TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "';");
			tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "';");
			tep_db_query("DELETE FROM " . TABLE_BRACKETS_GROUPS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "';");
			tep_redirect(tep_href_link(FILENAME_DATA_POOL,"action=bracket_group"));
			break;
		
		case "edit_bracket_group":
		case "new_bracket_group":
			//tep_clean_data($_POST['bracket_group_name'],true);
			$_POST['bracket_group_sort_order'] = (int)$_POST['bracket_group_sort_order'];
			$txtLevelStart = (int)$_POST['bracket_group_start_level'];
			//tep_clean_data($_POST['bracket_group_description'],true);
			
			if ($txtLevelStart <= 0)	$txtLevelStart = 1;
			
			if (trim($_POST['bracket_group_name']) != "" /*&& trim($_POST['data_pool_class'])!="" && trim($_POST['data_pool_description'])!=""*/) {
				if ($action == "new_bracket_group") {
					$arr = array(	'brackets_groups_name' => tep_db_prepare_input($_POST['bracket_group_name']),
									'brackets_groups_description' => tep_db_prepare_input($_POST['bracket_group_description']),
									'brackets_groups_sort_order' => $_POST['bracket_group_sort_order']
								);
					tep_db_perform(TABLE_BRACKETS_GROUPS, $arr);
					$lid = tep_db_insert_id();
					
					//	Each bracket group will has one entry for start level
					$arr = array('brackets_tags_key' => KEY_PL_START_LEVEL,
								 'brackets_tags_dependent' => 0,
								 'data_pool_level_tags_id' => 0,
								 'brackets_tags_value' => $txtLevelStart,
								 'brackets_groups_id' => $lid 
								);
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
					tep_redirect(tep_href_link(FILENAME_DATA_POOL,"action=bracket_group"));
				} else {
					$arr = array(	'brackets_groups_name' => tep_db_prepare_input($_POST['bracket_group_name']),
									'brackets_groups_description' => tep_db_prepare_input($_POST['bracket_group_description']),
									'brackets_groups_sort_order' => $_POST['bracket_group_sort_order']
								);
					
					tep_db_perform(TABLE_BRACKETS_GROUPS, $arr, 'update', ' brackets_groups_id="'.$brackets_groups_id.'"');
					
					$arr = array('brackets_tags_value' => $txtLevelStart );
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr, 'update', ' brackets_groups_id="'.$brackets_groups_id.'" AND  brackets_tags_key ="'.KEY_PL_START_LEVEL.'"');			
					
					tep_redirect(tep_href_link(FILENAME_DATA_POOL,"action=edit_bracket_group&brackets_groups_id=$brackets_groups_id"));
				}
			}
			break;
		
		case "new_template":
			function tep_get_data_pool_id($product_type_id, &$a) {
				$product_type_id = (int)$product_type_id;
				
				$result = tep_db_query("SELECT * FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . " WHERE custom_products_type_id ='" . $product_type_id . "';");
				
				if ($row = tep_db_fetch_array($result)) {
					$a = $row;
					return (int)$row['data_pool_id'];			
				}
				
				$a = array();
				return 0;
			}
			
			$duplicating_template_select_sql = "SELECT data_pool_level_tags_id FROM " . TABLE_DATA_POOL_TEMPLATE . " WHERE data_pool_template_id = '" . (int)$_POST["nodes_type"] . "';";
			$duplicating_template_result_sql = tep_db_query($duplicating_template_select_sql);
			if ($duplicating_template_row = tep_db_fetch_array($duplicating_template_result_sql)) {
				$data_pool_id = tep_data_duplicate_template((int)$duplicating_template_row['data_pool_level_tags_id'], 0);
				/*
				tep_filtersqldata($_POST['data_pool_template_name']);
				tep_filtersqldata($_POST['data_pool_template_description']);
				*/
				$arr = array(	'data_pool_template_name' => tep_db_prepare_input($_POST['data_pool_template_name']),
					 			'data_pool_template_description' => tep_db_prepare_input($_POST['data_pool_template_description']),
					 			'data_pool_level_tags_id' => $data_pool_id,
					 			'custom_products_type_id' => (int)$_POST['custom_products_type'],
				     		);
				tep_db_perform(TABLE_DATA_POOL_TEMPLATE, $arr);
				
				$lid = tep_db_insert_id();
				
				tep_duplicate_system_options((int)$_POST["nodes_type"], $lid);
				
				tep_redirect(tep_href_link(FILENAME_DATA_POOL, "template_id=" . $lid));
			} else {
				$ret = array();
				
				// Get the datapool id for this product type
				$data_pool_id = tep_get_data_pool_id($_POST['custom_products_type'], $ret);
				
				// create ref with the datapool id 
				if ($data_pool_id > 0) {
					$lid = 0;
					$arr = array('data_pool_level_parent_id' => '0',
								 'data_pool_level_name' => $ret['custom_products_type_name'],
								 'data_pool_level_value' => '0',
								 'data_pool_ref_id' => $lid,
								 'data_pool_sort_order' => '0'
								 );						 
					
					tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $arr);	
					
					$lid = tep_db_insert_id();		
					
					// create the damn template now!
					tep_filtersqldata($_POST['data_pool_template_name']);
					tep_filtersqldata($_POST['data_pool_template_description']);
					
					$arr = array('data_pool_template_name' => $_POST['data_pool_template_name'],
								'data_pool_template_description' => $_POST['data_pool_template_description'],
								'data_pool_level_tags_id' => $lid,
								'custom_products_type_id' => (int)$_POST['custom_products_type'],
								);
					
					tep_db_perform(TABLE_DATA_POOL_TEMPLATE, $arr);
					$lid = tep_db_insert_id();
					
					tep_create_system_options($lid);
					
					tep_redirect(tep_href_link(FILENAME_DATA_POOL, "template_id=".$lid));
				} else {
					tep_redirect(tep_href_link(FILENAME_DATA_POOL));
				}
			}
			break;		
		
		case "do_delete_template":
			$result = tep_db_query("SELECT data_pool_level_tags_id from ".TABLE_DATA_POOL_TEMPLATE." where data_pool_template_id='$template_id';");
			$row = tep_db_fetch_array($result);
			tep_data_pool_delete((int)$row['data_pool_level_tags_id']);
			
			tep_db_query("DELETE from ".TABLE_DATA_POOL_TEMPLATE." where data_pool_template_id='$template_id';");
			tep_db_query("DELETE from ".TABLE_POOL_TEMPLATE_T0_CATEGORIES." where data_pool_template_id='$template_id';");
			
			$option_tags_select_sql = "SELECT data_pool_options_id FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id ='" . $template_id ."'";
			$option_tags_result_sql = tep_db_query($option_tags_select_sql);
			while ($option_tags_row =  tep_db_fetch_array($option_tags_result_sql)) {
				$option_values_tag_delete_sql = "DELETE FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . $option_tags_row["data_pool_options_id"] . "'";
				tep_db_query($option_values_tag_delete_sql);
			}
			
			$option_tag_delete_sql = "DELETE FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id ='" . $template_id . "'";
			tep_db_query($option_tag_delete_sql);
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL));
			break;
		
		case "do_apply_template":
			$result = tep_db_query("SELECT data_pool_level_tags_id, data_pool_template_name, custom_products_type_id FROM " . TABLE_DATA_POOL_TEMPLATE . " WHERE data_pool_template_id ='" . $template_id . "';");
			
			if ($row = tep_db_fetch_array($result)) {
				$applicable_product_select_sql = "	SELECT p.products_id 
													FROM " . TABLE_PRODUCTS . " AS p 
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
														ON p.products_id=pd.products_id 
													INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
														ON pd.products_id=p2c.products_id 
													INNER JOIN " . TABLE_POOL_TEMPLATE_T0_CATEGORIES . " AS t2c 
														ON p2c.categories_id=t2c.categories_id 
													WHERE t2c.data_pool_template_id ='" . $template_id . "' 
														AND p2c.products_is_link=0 
														AND pd.products_name ='" . tep_db_input($row["data_pool_template_name"]) . "' 
														AND p.custom_products_type_id ='" . (int)$row["custom_products_type_id"] . "' 
														AND pd.language_id ='" . (int)$languages_id . "'";
				$applicable_product_result_sql = tep_db_query($applicable_product_select_sql);
				while ($applicable_product_row =  tep_db_fetch_array($applicable_product_result_sql)) {
					tep_apply_template($template_id, (int)$row['data_pool_level_tags_id'], 0, $applicable_product_row['products_id']);
				}
				$applied_products_count = tep_db_num_rows($applicable_product_result_sql);
				if ($applied_products_count > 0) {
					$messageStack->add_session(sprintf(SUCCESS_APPLY_TEMPLATE, $applied_products_count), 'success');
				} else {
					$messageStack->add_session(WARNING_NO_MATCH_PRODUCT_TO_APPLY, 'warning');
				}
			} else {
				$messageStack->add_session(ERROR_TEMPLATE_NOT_EXISTS, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "page=".$_REQUEST['page']."&template_id=$template_id"));
			break;
		
		case "do_apply_brackets":
			$arr = array();	
			
			$sql = "SELECT data_pool_level_tags_id FROM " . TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "';";
			$result = tep_db_query($sql);
			
			while ($row_main=tep_db_fetch_array($result)) {
				//	Copy the brackets
				$res = tep_db_query("SELECT DISTINCT brackets_tags_dependent FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent != '0' AND brackets_groups_id ='" . $brackets_groups_id . "';");
				
				//	CLEAR ALL brackets first
				tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id ='" . $row_main['data_pool_level_tags_id'] . "';");
				
				while ($row = tep_db_fetch_array($res)) {
					//	Create the bracket parent first
					$arr = array('brackets_tags_key' => KEY_PL_BRACKET,
								 'brackets_tags_dependent' => 0,
								 'data_pool_level_tags_id' => $row_main['data_pool_level_tags_id']
								 );		
					
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
					$lid_second = tep_db_insert_id();
					
					$res_second = tep_db_query("SELECT * FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent ='" . $row['brackets_tags_dependent'] . "';");
					while ($row_second = tep_db_fetch_array($res_second)) {
						$arr = array('brackets_tags_key' => $row_second['brackets_tags_key'],
									 'brackets_tags_value' => $row_second['brackets_tags_value'],
									 'brackets_tags_dependent' => $lid_second,
									 'data_pool_level_tags_id' => $row_main['data_pool_level_tags_id']
									 );
						
						tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
					}
				}
				
				if ((int)$lid_second>0) {
					//	Brackets were added, so we need to assign a start level
					$arr = array('brackets_tags_key' => KEY_PL_START_LEVEL,
								 'brackets_tags_value' => tep_get_one_time_bracket_value($brackets_groups_id, 'brackets_groups_id'),
								 'brackets_tags_dependent' => 0,
								 'data_pool_level_tags_id' => $row_main['data_pool_level_tags_id']
								 );
					
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=bracket_group&brackets_groups_id=$brackets_groups_id"));
			break;
			
		case "edit_template":		
			$_POST[custom_products_type] = (int)$_POST[custom_products_type];
			tep_clean_data($_POST[data_pool_template_name],true);
			tep_clean_data($_POST[data_pool_template_description],true);
			
			$template_update_sql = "UPDATE " . TABLE_DATA_POOL_TEMPLATE . " 
									SET data_pool_template_name ='" . $_POST[data_pool_template_name] . "',
										data_pool_template_description ='" . $_POST[data_pool_template_description] . "' 
				    				WHERE data_pool_template_id ='" . $template_id . "'";
			tep_db_query($template_update_sql);
			
			$verify_system_option_tags_select_sql = "SELECT data_pool_options_id FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id = '" . $template_id . "' AND data_pool_options_input_type=999";
			$verify_system_option_tags_result_sql = tep_db_query($verify_system_option_tags_select_sql);
			if (!tep_db_num_rows($verify_system_option_tags_result_sql)) {
				tep_create_system_options($template_id);
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=edit_template&template_id=$template_id"));	
			break;
			
		case "template_linking":
			$template_2_cat_delete_sql = "DELETE FROM " . TABLE_POOL_TEMPLATE_T0_CATEGORIES . " WHERE data_pool_template_id ='" . (int)$_REQUEST["template_id"] . "'";
			tep_db_query($template_2_cat_delete_sql);
			
			$selected_cat_array = array();
			
			if ($_REQUEST["browser_id"] == "IE") {
				$selected_cat_array = $_REQUEST["SelectedItem"];
			} else {	// for Non-IE browser
				$selected_cat_array = array_keys(array_filter($_REQUEST["HiddenCat"], "filter_selection"));
			}
			
			if (count($selected_cat_array)) {
				foreach($selected_cat_array as $cat_id) {
					$template_2_cat_insert_sql = "INSERT INTO " . TABLE_POOL_TEMPLATE_T0_CATEGORIES . " (data_pool_template_id, categories_id) VALUES (".(int)$_REQUEST["template_id"].", '".$cat_id."')";
					tep_db_query($template_2_cat_insert_sql);
				}
			}
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, 'template_id='.$template_id));
			break;
		
		case "bracket_group_level_linking":
			$bracket_grp_2_level_tag_delete_sql = "DELETE FROM " . TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS . " WHERE brackets_groups_id ='" . (int)$_REQUEST["brackets_groups_id"] . "';";
			tep_db_query($bracket_grp_2_level_tag_delete_sql);
			
			$selected_cat_array = array();
			$selected_cat_array = $_POST["SelectedItem"];
			
			if (count($selected_cat_array)) {
				foreach ($selected_cat_array as $level_tag_id) {
					$bracket_grp_2_level_tag_insert_sql = "INSERT INTO " . TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS . " (brackets_groups_id, data_pool_level_tags_id) VALUES (".(int)$_REQUEST["brackets_groups_id"].", '".$level_tag_id."')";
					tep_db_query($bracket_grp_2_level_tag_insert_sql);
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=bracket_group&brackets_groups_id=$brackets_groups_id"));
			break;
		
		case "new_bracket_group_bracket":
		case "edit_bracket_group_bracket":
			$level_tags_id = 0;
			//$bracket_roots = tep_get_bracket_roots($level_tags_id);
			$isduplicate = false;				
			
			//$txtLevelStart = (int)$_POST['txtLevelStart'];
			//if($txtLevelStart<=0)
			$txtLevelStart = 1;
			
			$txtLevelEnd = $_POST['txtLevelEnd'];
			$txtValue = $_POST['txtValue'];
			$txtInterval = $_POST['txtInterval'];
			
			$txtLevelEnd = (int)$txtLevelEnd;
			$txtValue = (double)$txtValue;
			$txtInterval = (double)$txtInterval;
			$brackets_tags_id = (int)$_REQUEST['brackets_tags_id'];
			
			$result_check = tep_db_query("SELECT brackets_tags_id FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "' AND brackets_tags_key ='" . KEY_PL_END_LEVEL . "' AND brackets_tags_value ='" . (int)$txtLevelEnd . "';");
			
			if (tep_db_num_rows($result_check))
				$isduplicate = true;
			else
				$isduplicate = false;
			
			$result_check = tep_db_query("SELECT brackets_tags_id FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_groups_id ='" . $brackets_groups_id . "' AND brackets_tags_key ='" . KEY_PL_START_LEVEL . "';");
			
			if (tep_db_num_rows($result_check))
				$has_start_level = true;
			else
				$has_start_level = false;
			
			if ($action=="edit_bracket_group_bracket" && $brackets_tags_id > 0) {
				$txtLevelStart = tep_get_one_time_bracket_value($brackets_groups_id, 'brackets_groups_id');
				if ($txtLevelStart>0 && $txtLevelStart <= $txtLevelEnd && $txtInterval > 0 && $txtValue > 0) {
					$arr = array('brackets_tags_value' => $txtLevelEnd);
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr, "update", "brackets_tags_key='".KEY_PL_END_LEVEL."' and brackets_groups_id='$brackets_groups_id' and brackets_tags_dependent='$brackets_tags_id';");
					
					$arr = array('brackets_tags_value' => $txtValue);
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr, "update", "brackets_tags_key='".KEY_PL_VALUE."' and brackets_groups_id='$brackets_groups_id' and brackets_tags_dependent='$brackets_tags_id';");			
					
					$arr = array('brackets_tags_value' => $txtInterval);
					tep_db_perform(TABLE_BRACKETS_TAGS, $arr, "update", "brackets_tags_key='".KEY_PL_INTERVAL."' and brackets_groups_id='$brackets_groups_id' and brackets_tags_dependent='$brackets_tags_id';");
				}
			} else {
				if ($isduplicate == false) {
					if ($txtLevelStart>0 && $txtLevelStart <= $txtLevelEnd && $txtInterval > 0 && $txtValue > 0) {
						// create the damn bracket
						$arr = array(	'brackets_tags_key' => KEY_PL_BRACKET,
										'brackets_tags_dependent' => 0,
										'data_pool_level_tags_id' => $level_tags_id,
										'brackets_groups_id' => $brackets_groups_id 
									 );
						tep_db_perform(TABLE_BRACKETS_TAGS, $arr);											
						$lid = tep_db_insert_id();
						
						if (!$has_start_level) {
							$arr = array(	'brackets_tags_key' => KEY_PL_START_LEVEL,
											'brackets_tags_dependent' => 0,
											'data_pool_level_tags_id' => $level_tags_id,
											'brackets_tags_value' => (int)$txtLevelStart,
											'brackets_groups_id' => $brackets_groups_id 
										);								
							tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
						}
						
						$arr = array(	'brackets_tags_key' => KEY_PL_END_LEVEL,
										'brackets_tags_dependent' => $lid,
										'data_pool_level_tags_id' => $level_tags_id,
										'brackets_tags_value' => $txtLevelEnd,
										'brackets_groups_id' => $brackets_groups_id 
									);
						tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
						
						$arr = array(	'brackets_tags_key' => KEY_PL_VALUE,
										'brackets_tags_dependent' => $lid,
										'data_pool_level_tags_id' => $level_tags_id,
										'brackets_tags_value' => $txtValue,
										'brackets_groups_id' => $brackets_groups_id 
									);
						tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
						
						$arr = array(	'brackets_tags_key' => KEY_PL_INTERVAL,
										'brackets_tags_dependent' => $lid,
										'data_pool_level_tags_id' => $level_tags_id,
										'brackets_tags_value' => $txtInterval,
										'brackets_groups_id' => $brackets_groups_id 
									);
						tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
					}
				}
			}
			
			if ($action=="edit_bracket_group_bracket" && $brackets_tags_id > 0)
				tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=$action&brackets_tags_id=".$brackets_tags_id."&brackets_groups_id=".$brackets_groups_id));
			else
				tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=bracket_group&brackets_groups_id=".$brackets_groups_id));
			break;
		
		case "do_delete_bracket":
			$path = $_GET['path'];
			tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent ='".(int)$_GET['bracket_id']."' OR brackets_tags_id='".(int)$_GET['bracket_id']."'");
			
			/*
			// Commented out by weichen  since decision has been made where bracket is not mandatory option anymore.
			
			$res = tep_db_query("SELECT COUNT(*) AS countval FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "' AND brackets_tags_key ='" . tep_db_input(KEY_PL_BRACKET) . "';");
			//	Remove all one time entries when necessary [Start Level, Base Time, Base Price...]
			if ($row = tep_db_fetch_array($res)) {
				if ($row['countval'] < 1) {
					tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "';");
				}
			}
			*/
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "id=".$id."&path=".$path."&action=brackets&template_id=".$template_id));
			break;
		
		case "do_delete_bracket_group_bracket":		
			tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent ='" . (int)$_GET['brackets_tags_id'] . "' OR brackets_tags_id ='" . (int)$_GET['brackets_tags_id'] . "'");
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=bracket_group&brackets_groups_id=".$brackets_groups_id));
			break;
		
		case "brackets":
			switch (strtoupper($type)) {
				case "PL":
					//	Get all the brackets for this level tag
					$bracket_roots = tep_get_bracket_roots($id);
					$path = $_GET['path'];
					$isduplicate = false;
					
					$has_brackets = (sizeof($bracket_roots) == 0) ? false : true;
					
					$txtLevelStart = (int)$_POST['txtLevelStart'];
					$txtStartLevelAlias = tep_db_prepare_input($_POST['txtStartLevelAlias']);
					$txtBaseTime = (double)$_POST['txtBaseTime'];
					$txtBasePrice = (double)$_POST['txtBasePrice'];
					$txtMinTime = (double)$_POST['txtMinTime'];
					$txtLevelLabel = tep_not_null($_POST['txtLevelLabel']) ? tep_db_prepare_input($_POST['txtLevelLabel']) : DEFAULT_LEVEL_TEXT;
					$txtClassLabel = tep_not_null($_POST['txtClassLabel']) ? tep_db_prepare_input($_POST['txtClassLabel']) : DEFAULT_CLASS_TEXT;
					
					if ($txtLevelStart <= 0)	$txtLevelStart = 1;
					
					$txtLevelEnd = $_POST['txtLevelEnd'];
					$txtValue = $_POST['txtValue'];
					$txtInterval = $_POST['txtInterval'];
					
					$txtLevelEnd[0] = (int)$txtLevelEnd[0];
					$txtLevelAlias[0] = tep_db_prepare_input($txtLevelAlias[0]);
					$txtValue[0] = (double)$txtValue[0];
					$txtInterval[0] = (double)$txtInterval[0];
					
					// Customised bracket settings
					$custom_bracket_labels_array = array_filter($_POST['txt_pl_custom_field'], "filter_empty_val");
					
					$custom_bracket_values_array = $_POST['txt_pl_custom_value'];
					if (count($custom_bracket_values_array)) {
						foreach ($custom_bracket_values_array as $key => $res) {
							$custom_bracket_values_array[$key] = array_filter($res, "filter_empty_val");
						}
					}
					$custom_tag_array = array();
					if (count($custom_bracket_labels_array)) {
						$reorder_num = 0;
						foreach ($custom_bracket_labels_array as $key => $text) {
							if (count($custom_bracket_values_array[$key]) > 0) {
								$custom_tag_array[$reorder_num]  = array('org_num' => $key, 'text' => $text, 'values' => $custom_bracket_values_array[$key]);
								$reorder_num++;
							}
						}
					}
					
					//	Do nothing if no end level is specified
					if (tep_not_null($txtLevelEnd[0])) {
						//	Verify is this bracket same as one of the existing bracket
						$result_check = tep_db_query("SELECT brackets_tags_id FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "' AND brackets_tags_key ='" . KEY_PL_END_LEVEL . "' AND brackets_tags_value ='". (int)$txtLevelEnd[0] . "';");
						if (tep_db_num_rows($result_check))		$isduplicate = true;
						
						if ($isduplicate == false) {
							$insert_error = false;
							
							if ($txtLevelStart > 0) {
								if ($txtLevelStart >= $txtLevelEnd[0]) {
									$messageStack->add_session(ERROR_LESS_THAN_START_LEVEL, 'error');
									$insert_error = true;
								}
								
								if ($txtInterval[0] <= 0) {
									$messageStack->add_session(WARNING_INVALID_INTERVAL_VALUE, 'warning');
									//$insert_error = true;
								}
								
								if ($txtValue[0] <= 0) {
									$messageStack->add_session(WARNING_INVALID_PRICE_VALUE, 'warning');
									//$insert_error = true;
								}
								
								if (!$insert_error) {
									//	Create the bracket reference
									$arr = array(	'brackets_tags_key' => KEY_PL_BRACKET,
													'brackets_tags_dependent' => 0,
													'data_pool_level_tags_id' => $id
												);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
									$lid = tep_db_insert_id();
									
									$arr = array(	'brackets_tags_key' => KEY_PL_END_LEVEL,
													'brackets_tags_dependent' => $lid,
													'data_pool_level_tags_id' => $id,
													'brackets_tags_value' => tep_db_prepare_input($txtLevelEnd[0])
												);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
									
									$arr = array(	'brackets_tags_key' => KEY_PL_LEVEL_ALIAS,
													'brackets_tags_dependent' => $lid,
													'data_pool_level_tags_id' => $id,
													'brackets_tags_value' => tep_db_prepare_input($txtLevelAlias[0])
												);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
									
									$arr = array(	'brackets_tags_key' => KEY_PL_VALUE,
													'brackets_tags_dependent' => $lid,
													'data_pool_level_tags_id' => $id,
													'brackets_tags_value' => tep_db_prepare_input($txtValue[0])
												);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
									
									$arr = array(	'brackets_tags_key' => KEY_PL_INTERVAL,
													'brackets_tags_dependent' => $lid,
													'data_pool_level_tags_id' => $id,
													'brackets_tags_value' => tep_db_prepare_input($txtInterval[0])
												);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
									
									for ($c_cnt=0; $c_cnt < count($custom_tag_array); $c_cnt++) {
										$custom_tag_key = 'pl_c_' . ($c_cnt + 1) . '_' . tep_db_prepare_input($custom_tag_array[$c_cnt]['text']);
										$custom_bracket_entry_array = array('brackets_tags_key' => $custom_tag_key,
																			'brackets_tags_dependent' => $lid,
																			'data_pool_level_tags_id' => $id,
																			'brackets_tags_value' => tep_db_prepare_input($custom_tag_array[$c_cnt]['values'][0])
																			);
										tep_db_perform(TABLE_BRACKETS_TAGS, $custom_bracket_entry_array);
									}
								}
							}
						}
					}
					
					if ($has_brackets) {
						// Updating the current brackets
						$level_array_keys = array_keys($txtLevelEnd);
						foreach ($level_array_keys as $key) {
							$key = (int)$key;
							if ($key > 0) {
								if ($txtLevelStart>0 && $txtLevelStart < $txtLevelEnd[$key] && $txtInterval[$key] >= 0 && $txtValue[$key] >= 0) {
									$arr = array('brackets_tags_value' => (int)$txtLevelEnd[$key]);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr, "update", "brackets_tags_key ='" . KEY_PL_END_LEVEL . "' AND brackets_tags_dependent='$key';");
									
									if (tep_get_bracket_value($key, KEY_PL_LEVEL_ALIAS) !== false) {
										$arr = array('brackets_tags_value' => tep_db_prepare_input($txtLevelAlias[$key]));
										tep_db_perform(TABLE_BRACKETS_TAGS, $arr, "update", "brackets_tags_key ='" . KEY_PL_LEVEL_ALIAS . "' AND brackets_tags_dependent='$key';");
									} else {
										$alias_data_array = array(	'brackets_tags_key' => KEY_PL_LEVEL_ALIAS,
																	'brackets_tags_dependent' => $key,
																	'data_pool_level_tags_id' => $id,
																	'brackets_tags_value' => tep_db_prepare_input($txtLevelAlias[$key])
																);
										tep_db_perform(TABLE_BRACKETS_TAGS, $alias_data_array);
									}
									
									$arr = array('brackets_tags_value' => (double)$txtValue[$key]);
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr,"update","brackets_tags_key='".KEY_PL_VALUE."' and brackets_tags_dependent='$key';");
									
									$arr = array('brackets_tags_value' => (double)$txtInterval[$key]);	
									tep_db_perform(TABLE_BRACKETS_TAGS, $arr,"update","brackets_tags_key='".KEY_PL_INTERVAL."' and brackets_tags_dependent='$key';");
									
									for ($c_cnt=0; $c_cnt < count($custom_tag_array); $c_cnt++) {
										$custom_tag_key_prefix = 'pl_c_' . ($c_cnt + 1) . '_';
										$custom_tag_key = $custom_tag_key_prefix . tep_db_prepare_input($custom_tag_array[$c_cnt]['text']);
										
										if (tep_get_bracket_value($key, $custom_tag_key_prefix . '%', ' LIKE ') !== false) {
											$custom_bracket_entry_array = array('brackets_tags_value' => tep_db_prepare_input($custom_tag_array[$c_cnt]['values'][$key]),
																				'brackets_tags_key' => $custom_tag_key,
																				);
											tep_db_perform(TABLE_BRACKETS_TAGS, $custom_bracket_entry_array, "update", "brackets_tags_dependent='$key' AND brackets_tags_key LIKE '" . $custom_tag_key_prefix . "%'");
										} else {
											$custom_bracket_entry_array = array('brackets_tags_key' => $custom_tag_key,
																				'brackets_tags_dependent' => $key,
																				'data_pool_level_tags_id' => $id,
																				'brackets_tags_value' => tep_db_prepare_input($custom_tag_array[$c_cnt]['values'][$key])
																				);
											tep_db_perform(TABLE_BRACKETS_TAGS, $custom_bracket_entry_array);
										}
									}
									
									for ($c_garbage_cnt=count($custom_tag_array)+1; $c_garbage_cnt <= 5; $c_garbage_cnt++) {
										$custom_tag_key_prefix = 'pl_c_' . ($c_garbage_cnt) . '_';
										tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS  . " WHERE brackets_tags_dependent='$key' AND brackets_tags_key LIKE '" . $custom_tag_key_prefix . "%'");
									}
								}
							}
						}
					}
					
					//	Allow non-bracket package
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_START_LEVEL) === false) {
						$start_level_data_array = array('brackets_tags_key' => KEY_PL_START_LEVEL,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtLevelStart
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_data_array);
					} else {
						$start_level_data_array = array('brackets_tags_value' => $txtLevelStart);
						tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_data_array, "update", "brackets_tags_key ='" . KEY_PL_START_LEVEL . "' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_START_LEVEL_ALIAS) === false) {
						$start_level_alias_data_array = array(	'brackets_tags_key' => KEY_PL_START_LEVEL_ALIAS,
																'brackets_tags_dependent' => 0,
																'data_pool_level_tags_id' => $id,
																'brackets_tags_value' => $txtStartLevelAlias
																);
						tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_alias_data_array);
					} else {
						$start_level_alias_data_array = array('brackets_tags_value' => $txtStartLevelAlias);
						tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_alias_data_array, "update", " brackets_tags_key='".KEY_PL_START_LEVEL_ALIAS."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_BASE_TIME) === false) {
						$base_time_data_array = array(	'brackets_tags_key' => KEY_PL_BASE_TIME,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtBaseTime
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $base_time_data_array);
					} else {
						$base_time_data_array = array('brackets_tags_value' => $txtBaseTime);
						tep_db_perform(TABLE_BRACKETS_TAGS, $base_time_data_array, "update", " brackets_tags_key='".KEY_PL_BASE_TIME."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_BASE_PRICE) === false) {
						$base_price_data_array = array(	'brackets_tags_key' => KEY_PL_BASE_PRICE,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtBasePrice
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $base_price_data_array);
					} else {
						$base_price_data_array = array('brackets_tags_value' => $txtBasePrice);
						tep_db_perform(TABLE_BRACKETS_TAGS, $base_price_data_array, "update", " brackets_tags_key='".KEY_PL_BASE_PRICE."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_LEVEL_LABEL) === false) {
						$level_label_data_array = array('brackets_tags_key' => KEY_PL_LEVEL_LABEL,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtLevelLabel
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $level_label_data_array);
					} else {
						$level_label_data_array = array('brackets_tags_value' => $txtLevelLabel);
						tep_db_perform(TABLE_BRACKETS_TAGS, $level_label_data_array, "update", " brackets_tags_key='".KEY_PL_LEVEL_LABEL."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_CLASS_LABEL) === false) {
						$class_label_data_array = array('brackets_tags_key' => KEY_PL_CLASS_LABEL,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtClassLabel
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $class_label_data_array);
					} else {
						$class_label_data_array = array('brackets_tags_value' => $txtClassLabel);
						tep_db_perform(TABLE_BRACKETS_TAGS, $class_label_data_array, "update", " brackets_tags_key='".KEY_PL_CLASS_LABEL."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					if (tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_MIN_TIME) === false) {
						$min_time_data_array = array(	'brackets_tags_key' => KEY_PL_MIN_TIME,
														'brackets_tags_dependent' => 0,
														'data_pool_level_tags_id' => $id,
														'brackets_tags_value' => $txtMinTime
														);
						tep_db_perform(TABLE_BRACKETS_TAGS, $min_time_data_array);
					} else {
						$min_time_data_array = array('brackets_tags_value' => $txtMinTime);
						tep_db_perform(TABLE_BRACKETS_TAGS, $min_time_data_array, "update", " brackets_tags_key='".KEY_PL_MIN_TIME."' AND data_pool_level_tags_id ='" . $id . "';");
					}
					
					tep_redirect(tep_href_link(FILENAME_DATA_POOL, "id=".$id."&path=".$path."&action=brackets&template_id=".$template_id));
					
					break;	// type break
			}
			break;
		
		case "new_option":
		case "edit_option":
			$tplID = (int)$HTTP_POST_VARS["tplID"];
			$option_id = (int)$HTTP_POST_VARS["optionID"];
			$input_type = tep_db_prepare_input($HTTP_POST_VARS["option_input_type"]);
			
			if ($input_type == 999) {
				$option_sql_data_array = array(	'data_pool_options_class' => 'GENARAL',
												'data_pool_options_sort_order' => (int)$HTTP_POST_VARS["option_order"],
												'data_pool_options_show_supplier' => (int)$HTTP_POST_VARS["show_supplier_option"]
											  );
				
				tep_db_perform(TABLE_DATA_POOL_OPTIONS_TAGS, $option_sql_data_array, 'update', ' data_pool_options_id="'.$option_id.'"');
			} else {
				switch ($input_type) {
					case "1" : // text box
						$input_size = $_REQUEST["box_size"] . ',' . $_REQUEST["box_text_max_len"];
						break;
					case "2" :	// text area
						$input_size = $_REQUEST["box_row"] . ',' . $_REQUEST["box_col"];
						break;
					case "5" :	// Date Selection
						$input_size = $_REQUEST["box_from"] . ',' . $_REQUEST["box_period"];
						break;
					default :
						$input_size = 'NULL';
						break;
				}
				
				$option_sql_data_array = array(	'data_pool_template_id' => $tplID,
	        	       							'data_pool_options_title' => tep_db_prepare_input($HTTP_POST_VARS["option_title"]),
	        	       							'data_pool_options_class' => tep_db_prepare_input($HTTP_POST_VARS["option_class"]),
	        	       							'data_pool_options_input_type' => $input_type,
	        	       							'data_pool_options_input_size' => $input_size,
	        	       							'data_pool_options_required' => (int)$HTTP_POST_VARS["mandatory_option"],
	        	       							'data_pool_options_show_supplier' => (int)$HTTP_POST_VARS["show_supplier_option"],
	        	       							'data_pool_options_sort_order' => (int)$HTTP_POST_VARS["option_order"]
	                   						);
				
	            if ($action == "new_option") {
					tep_db_perform(TABLE_DATA_POOL_OPTIONS_TAGS, $option_sql_data_array);
					$option_id = tep_db_insert_id();
				} else {
					tep_db_perform(TABLE_DATA_POOL_OPTIONS_TAGS, $option_sql_data_array, 'update', ' data_pool_options_id="'.$option_id.'"');
					$clear_option_values_sql = "DELETE FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . $option_id . "'";
					tep_db_query($clear_option_values_sql);
				}
				
				if ($input_type == '1' || $input_type == '2' || $input_type == '5' || $input_type == '6') {
					$option_values_sql_data_array = array(	'data_pool_options_values_id' => '1',
				        	       							'data_pool_options_id' => $option_id,
				        	       							'data_pool_options_values_price' => (double)$HTTP_POST_VARS["option_price"][0],
				        	       							'data_pool_options_values_eta' => (double)$HTTP_POST_VARS["option_time"][0],
				        	       							'data_pool_options_values_min_level' => (int)$HTTP_POST_VARS["option_min_level"][0],
				        	       							'data_pool_options_values_max_level' => (int)$HTTP_POST_VARS["option_max_level"][0]
				                   						);
					tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES_TAGS, $option_values_sql_data_array);
				} else if ($input_type == '7') {
					$option_values_sql_data_array = array(	'data_pool_options_values_id' => '1',
				        	       							'data_pool_options_id' => $option_id,
				        	       							'data_pool_options_value' => tep_db_prepare_input($HTTP_POST_VARS["option_name"][0]),
				        	       							'data_pool_options_values_price' => (double)$HTTP_POST_VARS["option_price"][0],
				        	       							'data_pool_options_values_eta' => (double)$HTTP_POST_VARS["option_time"][0],
				        	       							'data_pool_options_values_min_level' => (int)$HTTP_POST_VARS["option_min_level"][0],
				        	       							'data_pool_options_values_max_level' => (int)$HTTP_POST_VARS["option_max_level"][0]
				                   						);
					tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES_TAGS, $option_values_sql_data_array);
				} else {
					if (isset($HTTP_POST_VARS["option_name"]) && count($HTTP_POST_VARS["option_name"])) {
						for ($i=0; $i < count($HTTP_POST_VARS["option_name"]); $i++) {
							if (trim($HTTP_POST_VARS["option_name"][$i]) != '') {
								$option_values_sql_data_array = array(	'data_pool_options_values_id' => ($i+1),
							        	       							'data_pool_options_id' => $option_id,
							        	       							'data_pool_options_value' => tep_db_prepare_input($HTTP_POST_VARS["option_name"][$i]),
							        	       							'data_pool_options_values_price' => (double)$HTTP_POST_VARS["option_price"][$i],
							        	       							'data_pool_options_values_eta' => (double)$HTTP_POST_VARS["option_time"][$i],
							        	       							'data_pool_options_values_min_level' => (int)$HTTP_POST_VARS["option_min_level"][$i],
							        	       							'data_pool_options_values_max_level' => (int)$HTTP_POST_VARS["option_max_level"][$i],
							        	       							'data_pool_options_values_sort_order' => (int)$HTTP_POST_VARS["option_sort_order"][$i]
							                   						);
								tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES_TAGS, $option_values_sql_data_array);
							}
						}
					}
				}
			}
			
			if (isset($_REQUEST['page'])) {
				tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=edit_option&template_id=".$tplID."&optionID=".$option_id."&page=".$_REQUEST['page']."&cont=".$_REQUEST['cont']));
			} else {
				tep_redirect(tep_href_link(FILENAME_DATA_POOL, "action=edit_option&template_id=".$tplID."&optionID=".$option_id));
			}
			
			break;
		case "do_delete_option":
			$option_tags_type_select_sql = "SELECT data_pool_options_input_type FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_options_id ='" . (int)$_REQUEST["optionID"] . "'";
			$option_tags_type_result_sql = tep_db_query($option_tags_type_select_sql);
			$option_tags_type_row = tep_db_fetch_array($option_tags_type_result_sql);
			
			if ($option_tags_type_row["data_pool_options_input_type"] != 999) {
				$option_tag_delete_sql = "DELETE FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_options_id ='" . (int)$_REQUEST["optionID"] . "'";
				tep_db_query($option_tag_delete_sql);
				
				$option_values_tag_delete_sql = "DELETE FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . (int)$_REQUEST["optionID"] . "'";
				tep_db_query($option_values_tag_delete_sql);
			}
			
			tep_redirect(tep_href_link(FILENAME_DATA_POOL, "template_id=$template_id"));
			
			break;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script>
	<!--
		var tbody_count = 0;
		function validate_brackets() {
		}
		
		function trim(str) {
			str = str.replace(/^\s*|\s*$/g,"");
			
			return str;
		}
		
		function insert_edit_data_item_form_checking() {
			var err = 0;
			var current_start_level = parseInt(document.getElementById('bracket_start_level').value);
			
			var txtLevelEnd = parseInt(document.getElementById('txtLevelEnd').value);
			var txtValue = parseFloat(document.getElementById('txtValue').value);
			var txtInterval = parseFloat(document.getElementById('txtInterval').value);
			
			if (isNaN(txtLevelEnd) || txtLevelEnd < current_start_level) {
				alert("Level should be more than or equal to bracket start level.");
				err++;
			} else if(isNaN(txtValue) || txtValue <= 0) {
				alert("Invalid Level value.");	
				err++;				
			} else if(isNaN(txtInterval) || txtValue <= 0) {
				alert("Invalid Interval.");
				err++;
			}
			
			if (err==0) {		
				document.frmDataPool.submit();
				return true;
			} else {
				return false;
			}
		}
		
		//function validate edit/insert level
		function insert_level_form_checking() {
			var err = 0;
			
			if (trim(document.getElementById('data_pool').value) == "") {
				alert('Please assign a valid datapool.');
				err++;
			}
			
			if (err==0) {
				document.frmDataPool.submit();
				return true;
			} else {
				return false;
			}
		}
		
		function recursiveDelete(name,strLink) {
			var yn = confirm("Are you sure you want to delete '" + name + "' and all its children?");
			
			if (yn)	document.location.href = strLink;
		}
		
		function edit_level_form_checking() {
			if (trim_str(document.getElementById('selection_group_name').value) == "") {
				alert('Please assign a valid selection group name.');
				document.getElementById('selection_group_name').value = "";
				document.getElementById('selection_group_name').focus();
				return false;
			}
			
			document.frmDataPool.submit();
		}
		
		function edit_option_form_checking() {
			var opt_title = DOMCall('option_title');
			if (trim_str(opt_title.value) == '') {
				alert('Please assign option title.');
				opt_title.value = '';
				opt_title.focus();
				return false;
			}
			
			document.frmDataPool.submit();
		}
		
		//function validate edit/insert template information
		function insert_edit_template_form_checking() {
			var err = 0;
			
			if (trim(document.getElementById('data_pool_template_name').value) == "") {
				alert('Please assign a valid template name.');
				err++;
			}
			
			if (err==0) {		
				document.frmDataPool.submit();
				return true;
			} else {
				return false;
			}
		}
		
		// validate edit/insert data pool
		function insert_edit_bracket_group_form_checking() {
			var err = 0;
			
			//alert(parseInt(document.getElementById('bracket_min_level'));
			var bracket_group_start_level = parseInt(document.getElementById('bracket_group_start_level').value);
			var bracket_min_level = parseInt(document.getElementById('bracket_min_level').value);
			
			if (trim(document.getElementById('bracket_group_name').value) == "") {
				alert('Please assign a valid bracket group name.');
				err++;
			} else if (isNaN(bracket_group_start_level) || bracket_group_start_level<=0) {
				alert('Start level cannot be less than 1');
				err++;
			} else if (bracket_group_start_level > bracket_min_level) {
				alert('Start level cannot be more than the smallest bracket level (' + bracket_min_level + ').');
				err++;
			}
			
			if (err==0) {
				document.frmDataPool.submit();
				return true;
			} else {
				return false;
			}
		}
		
		function removeRow(obj) {
			var parentTable = document.getElementById('parentTable');
			parentTable.removeChild(obj.parentNode.parentNode.parentNode.parentNode);
			return false;
		}
		
		function selection_subcategories() {
			var obj = document.getElementById('include_subcategories');
			
			if (obj.checked)
				obj.checked=false;
			else
				obj.checked=true;
			
			return false;					
		}
		
		function import_selections(obj) {
			var a = parseInt(obj.value);
			var yn, loc, ischecked=false, current_id=<?=$id?>;
			
			if (a == current_id) {
				alert("You cannot import a category to itself.");
				obj.selectedIndex = 0;
				return false;
			}
			
			var selection_group_id = parseInt(document.frmDataPool.selection_group_id.value);
			if (isNaN(selection_group_id))	selection_group_id = 0;
			
			var x = document.getElementById('include_subcategories');
			if (a > 0) {
				ischecked = x.checked;
				
				if (ischecked) {
					yn = confirm("Are you sure you want to import the selections and all its subcategories?");
				} else {
					yn = confirm("Are you sure you want to import the selections?");
				}
				
				if (!yn) {
					obj.selectedIndex = 0;
					return false;
				} else {
					if (ischecked) {
						loc = "<? echo tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('action'))."action=do_import_selections&inc_subcat=1")?>" + "&import_from_id=" +  a + "&selection_group_id=" + selection_group_id;
						
						document.location.href = loc;	
					} else {
						loc = "<? echo tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('action'))."action=do_import_selections")?>" + "&import_from_id=" +  a + "&selection_group_id=" + selection_group_id;
						
						document.location.href = loc;
					}	
				}
			} else if (a == -1) {
				alert("This node does not have any children to import.");
				obj.selectedIndex = 0;
				return false;
			}
		}
		
		function addNewSelectionRow() {
			var parentTable = document.getElementById('parentTable');			
			
			// lets construct the inner elements first
			var tr = document.createElement("tr");
			var tbody = document.createElement("tbody");
			
			tbody.setAttribute("id", "tbody_" + tbody_count);	
			
			var td = document.createElement("td");
			var div = document.createElement("div");
			td.className = "main";
			//td.setAttribute("class", "main");				
			div.innerHTML = '<?=tep_draw_input_field('selection_name[]', '', 'size="45" id="selection_name"')?>';
			td.appendChild(div);
			tr.appendChild(td);
			
			td = document.createElement("td");
			div = document.createElement("div");
			td.className = "main";	
			td.setAttribute("align","center");			
			div.innerHTML = '<?=tep_draw_input_field('selection_sort_order[]', '50000', 'size="5" id="selection_sort_order"')?>';
			td.appendChild(div);
			tr.appendChild(td);
			
			td = document.createElement("td");
			div = document.createElement("div");
			td.className = "main";	
			td.setAttribute("align","center");			
			div.innerHTML = '<?=tep_draw_input_field('selection_min_level[]', '', 'size="5" id="selection_min_level"')?>';
			td.appendChild(div);
			tr.appendChild(td);
			
			td = document.createElement("td");
			div = document.createElement("div");
			td.className = "main";	
			td.setAttribute("align","center");			
			div.innerHTML = '<?=tep_draw_input_field('selection_max_level[]', '', 'size="5" id="selection_max_level"')?>';
			td.appendChild(div);
			tr.appendChild(td);
			
			td = document.createElement("td");
			div = document.createElement("div");
			td.className = "main";
			td.setAttribute("align","center");			
			div.innerHTML = "-";
			td.appendChild(div);
			tr.appendChild(td);
			
			td = document.createElement("td");
			div = document.createElement("div");
			td.className = "main";	
			td.setAttribute("align","center");			
			div.innerHTML = '<a href="#" onClick="return removeRow(this);"><?=TEXT_REMOVE?></a>';
			td.appendChild(div);
			tr.appendChild(td);	
			
			tbody.appendChild(tr);
			parentTable.appendChild(tbody);
			
			tbody_count++;
		}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<?
switch ($action) {
	case "bracket_group":
		$content.= '<tr><td>[ <a href="'.tep_href_link(FILENAME_DATA_POOL, "action=new_bracket_group").'">'.TEXT_NEW_BRACKET_GROUP.'</a> ]</td></tr>';
		break;
	
	case "brackets":
		//$innerContent = "";
		$tabindex = 1;
		
		$template_name_select_sql = " SELECT data_pool_template_name FROM " . TABLE_DATA_POOL_TEMPLATE . " WHERE data_pool_template_id ='" . $template_id . "'";
		$template_name_result_sql = tep_db_query($template_name_select_sql);		
		$template_name_row = tep_db_fetch_array($template_name_result_sql);
		
		$content = '<tr><td valign="top" class="main"><b>Brackets for:</b> '.($template_name_row["data_pool_template_name"]).'</td></tr><tr>';
		$content.= '<tr>
						<td>
							<table width="100%" border="0" cellspacing="0" cellpadding="3">';
		
		//	Check to see if brackets are already assigned or not
		$bracket_roots = tep_get_bracket_roots($id);
		$has_brackets = (sizeof($bracket_roots) > 0) ? true : false;
		
		$start_level = tep_get_one_time_bracket_value($id);
		$start_level_alias = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_START_LEVEL_ALIAS);
		$base_time = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_BASE_TIME);
		$base_price = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_BASE_PRICE);
		$level_label = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_LEVEL_LABEL);
		$class_label = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_CLASS_LABEL);
		$min_time = tep_get_one_time_bracket_value($id, 'data_pool_level_tags_id', KEY_PL_MIN_TIME);
		
		$content.= '<tr>
						<td>
							<table border="0" cellspacing="2" cellpadding="2">
								<tr class="dataTableContent">
									<td>'.TEXT_STARTING_LEVEL.'</td><td>'.tep_draw_input_field("txtLevelStart", $start_level, 'size="10" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"').'</td>
									<td>'.TEXT_STARTING_LEVEL_ALIAS.'</td><td>'.tep_draw_input_field("txtStartLevelAlias", $start_level_alias, ' tabindex="'.($tabindex++).'" ').'</td>
								</tr>
								<tr class="dataTableContent">
									<td>'.TEXT_BASE_PRICE.'</td><td>'.tep_draw_input_field("txtBasePrice", $base_price, 'size="10" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
									<td>'.TEXT_BASE_TIME.'</td><td>'.tep_draw_input_field("txtBaseTime", $base_time, 'size="10" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
								</tr>
								<tr class="dataTableContent">
									<td>'.TEXT_MIN_TIME.'</td><td>'.tep_draw_input_field("txtMinTime", $min_time, 'size="10" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
									<td colspan="2">&nbsp;</td>
								</tr>
								<tr class="dataTableContent">
									<td>'.TEXT_LEVEL_LABEL.'</td><td>'.tep_draw_input_field("txtLevelLabel", $level_label, 'size="10" tabindex="'.($tabindex++).'"').'</td>
									<td>'.TEXT_CLASS_LABEL.'</td><td>'.tep_draw_input_field("txtClassLabel", $class_label, 'size="10" tabindex="'.($tabindex++).'"').'</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
					</tr>
					<tr>
						<td>
							<table width="1" cellspacing="0" cellpadding="2">';
		
		if ($has_brackets == false) {
			$subTitle = TEXT_DATA_POOL_BRACKETS_ADD;
			//DEFAULT_LEVEL_TEXT
			$content .= '<tr>
							<td align="center" class="ordersBoxHeading"></td>
							<td align="center" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,1).'</td>
							<td></td>
						</tr>
						<tr class="dataTableContent">
							<td width="1%" class="ordersBoxHeading">'.TEXT_LEVEL.'</td>
							<td width="5%" align="center" bgcolor="#D7D5D0">'.tep_draw_input_field("txtLevelEnd[0]", '', 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"').'</td>
							<td></td>
						</tr>
						<tr class="dataTableContent">
							<td width="1%" class="ordersBoxHeading">'.BRACKET_LABEL_ALIAS.'</td>
							<td width="5%" align="center" bgcolor="#D7D5D0">'.tep_draw_input_field("txtLevelAlias[0]", '', 'size="7" tabindex="'.($tabindex++).'"').'</td>
							<td></td>
						</tr>
						<tr class="dataTableContent" class="dataTableRowOver">
							<td width="1%" class="ordersBoxHeading">'.TEXT_VALUE.'</td>
							<td width="5%" align="center" bgcolor="#D7D5D0">'.tep_draw_input_field("txtValue[0]", '', 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
							<td></td>
						</tr>
						<tr class="dataTableContent" class="dataTableRowOver">
								<td width="1%" class="ordersBoxHeading">'.TEXT_INTERVAL.'</td>
								<td width="5%" align="center" bgcolor="#D7D5D0">'.tep_draw_input_field("txtInterval[0]", '', 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
								<td></td>
						</tr>
						<tbody>';
			
			for ($dyn_bracket_field_cnt=0; $dyn_bracket_field_cnt<5; $dyn_bracket_field_cnt++) {
				$content .= '<tr class="dataTableContent" class="dataTableRowOver">
								<td width="1%" class="ordersBoxHeading">'.tep_draw_input_field("txt_pl_custom_field[".$dyn_bracket_field_cnt."]", '', 'size="7" tabindex="'.($tabindex++).'"').'</td>
								<td width="5%" align="center" bgcolor="#D7D5D0">'.tep_draw_input_field("txt_pl_custom_value[".$dyn_bracket_field_cnt."][0]", '', 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>
								<td></td>
							</tr>';
			}
			$content.= '</tbody>
						</table></td></tr>								
						<tr>
							<td><br>'.tep_image_submit("button_insert.gif", IMAGE_INSERT, 'tabindex="'.($tabindex++).'"') . '&nbsp;
							<a href="' . tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('ref_id', 'id', 'action', 'path'))) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a></td>
						</tr>';
		} else {
			$subTitle = TEXT_DATA_POOL_BRACKETS_EDIT;
			$arr_brackets = array();
			$sortStack=array();
			$custom_bracket_labels_array = array();
			
			foreach ($bracket_roots as $root) {
				$tmp = array();
				
				$sql = "SELECT * FROM ".TABLE_BRACKETS_TAGS." WHERE brackets_tags_dependent = '".$root['brackets_tags_id']."';";
				$result = tep_db_query($sql);
				
				$tmp['data_pool_level_tags_id'] = $id;
				$tmp['brackets_tags_dependent'] = $root['brackets_tags_id'];
				
				while ($row = tep_db_fetch_array($result)) {
					$tmp[$row['brackets_tags_key']] = $row['brackets_tags_value'];
				}
				
				$tmp[KEY_PL_END_LEVEL] =  (int)$tmp[KEY_PL_END_LEVEL];
				
				$arr_brackets[] = $tmp;
			}
			
			$arr_brackets = tep_bubble_sort($arr_brackets);
			
			$rowAction=$rowHead=$rowLevel=$rowValue=$rowInterval=$strAppend=$strPrepend="";
			$rowHeadOriginal = $rowHead[0] = '<td class="ordersBoxHeading">&nbsp;</td>';
			$rowLevelOriginal = $rowLevel[0] =	'<td class="ordersBoxHeading">'.TEXT_LEVEL.'</td>';
			$rowAliasOriginal = $rowAlias[0] =	'<td class="ordersBoxHeading">'.BRACKET_LABEL_ALIAS.'</td>';
			$rowValueOriginal = $rowValue[0] =	'<td class="ordersBoxHeading">'.TEXT_VALUE.'</td>';
			$rowIntervalOriginal = $rowInterval[0] = '<td class="ordersBoxHeading">'.TEXT_INTERVAL.'</td>';
			
			if (count($arr_brackets[0])) {
				foreach ($arr_brackets[0] as $field_key => $field_value) {
					if (preg_match('/(?:pl_c_)(\d+)_(.*)/is', $field_key, $regs)) {
						$custom_bracket_labels_array[$regs[1]-1] = $regs[2];
					}
				}
			}
			
			for ($dyn_bracket_field_cnt=0; $dyn_bracket_field_cnt<5; $dyn_bracket_field_cnt++) {
				$rowCustom[$dyn_bracket_field_cnt][0] = '<td class="ordersBoxHeading">'.tep_draw_input_field("txt_pl_custom_field[".$dyn_bracket_field_cnt."]", $custom_bracket_labels_array[$dyn_bracket_field_cnt], 'size="7" tabindex="'.($tabindex++).'" onBlur="update_custom_label(this, \''.$dyn_bracket_field_cnt.'\');"').'</td>';
			}
			
			$rowActionOriginal = $rowAction[0] = '<td class="ordersBoxHeading">'.TEXT_ACTION.'</td>';
			
			$index = 0;		
			$count_times = 0;	   
			
			for ($x=0; $x<=sizeof($arr_brackets); $x++) {
				if ($x < sizeof($arr_brackets)) {
					$row=$arr_brackets[$x];
				} else {
					$row = array();
					$row['brackets_tags_id'] = 0;
				}
				
				$color = (($x % 2)==0) ? "#D7D5D0" : "#FFFFCC";
				
				if ($count_times == BRACKETS_PER_ROW) {
					$count_times = 0;
					$index++;
					
					$rowHead[$index] = '<td class="ordersBoxHeading">&nbsp;</td>';
					$rowLevel[$index] =	'<td class="ordersBoxHeading">'.TEXT_LEVEL.'</td>';
					$rowAlias[$index] =	'<td class="ordersBoxHeading">'.BRACKET_LABEL_ALIAS.'</td>';
					$rowValue[$index] =	'<td class="ordersBoxHeading">'.TEXT_VALUE.'</td>';
					$rowInterval[$index] = '<td class="ordersBoxHeading">'.TEXT_INTERVAL.'</td>';
					
					for ($dyn_bracket_field_cnt=0; $dyn_bracket_field_cnt<5; $dyn_bracket_field_cnt++) {
						$rowCustom[$dyn_bracket_field_cnt][$index] = '<td class="ordersBoxHeading" nowrap><span id="custom_span_label_'.$dyn_bracket_field_cnt.'_'.$index.'"></span></td>';
					}
					
					$rowAction[$index] = '<td class="ordersBoxHeading">'.TEXT_ACTION.'</td>';					
				}
				
				$count_times++;
				
				$rowHead[$index] .= '<td class="ordersBoxHeading" align="center">'.sprintf(TABLE_HEADING_BRACKET,$x+1).'</td>';			
				$rowLevel[$index] .= '<td bgcolor="'.$color.'">'.tep_draw_input_field("txtLevelEnd[".(int)$row['brackets_tags_dependent']."]", $row[KEY_PL_END_LEVEL], 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"').'</td>';
				$rowAlias[$index] .= '<td bgcolor="'.$color.'">'.tep_draw_input_field("txtLevelAlias[".(int)$row['brackets_tags_dependent']."]", $row[KEY_PL_LEVEL_ALIAS], 'size="7" tabindex="'.($tabindex++).'"').'</td>';
				$rowValue[$index] .= '<td bgcolor="'.$color.'">'.tep_draw_input_field("txtValue[".(int)$row['brackets_tags_dependent']."]", $row[KEY_PL_VALUE], 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>';
				$rowInterval[$index] .= '<td bgcolor="'.$color.'">'.tep_draw_input_field("txtInterval[".(int)$row['brackets_tags_dependent']."]", $row[KEY_PL_INTERVAL], 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>';
				
				for ($dyn_bracket_field_cnt=0; $dyn_bracket_field_cnt<5; $dyn_bracket_field_cnt++) {
					$this_dyn_value = '';
					if (count($row)) {
						foreach ($row as $field_key => $field_value) {
							if (preg_match('/(?:pl_c_)('.($dyn_bracket_field_cnt+1).')_(.*)/is', $field_key, $regs)) {
								$this_dyn_value = $field_value;
								break;
							}
						}
					}
					
					$rowCustom[$dyn_bracket_field_cnt][$index] .= '<td bgcolor="'.$color.'">'.tep_draw_input_field("txt_pl_custom_value[".$dyn_bracket_field_cnt."][".(int)$row['brackets_tags_dependent']."]", $this_dyn_value, 'size="7" tabindex="'.($tabindex++).'" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"').'</td>';
				}
				
				if ($row['brackets_tags_dependent'] > 0) {
					$rowAction[$index].= '<td align="center" bgcolor="'.$color.'"><a href="'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array("action"))."action=do_delete_bracket&bracket_id=".(int)$row['brackets_tags_dependent']).'">'.TEXT_DELETE.'</a></td>'; 
				} else {
					$rowAction[$index].= '<td align="center" bgcolor="'.$color.'"></td>';
				}
			}
			
			for ($y=0; $y <= $index; $y++) {
				$content .= '<tr>'.$rowHead[$y].'</tr>';
				$content .= '<tr>'.$rowLevel[$y].'</tr>';
				$content .= '<tr>'.$rowAlias[$y].'</tr>';
				$content .= '<tr>'.$rowValue[$y].'</tr>';
				$content .= '<tr>'.$rowInterval[$y].'</tr>';
				
				for ($dyn_bracket_field_cnt=0; $dyn_bracket_field_cnt<5; $dyn_bracket_field_cnt++) {
					$content .= '<tr>'.$rowCustom[$dyn_bracket_field_cnt][$y].'</tr>';
				}
				
				$content.='<tr>'.$rowAction[$y].'</tr>';
			}
			
			$content.= '</table></td></tr>
						<tr>
							<td><br>'.
								tep_image_submit("button_update.gif", IMAGE_UPDATE, 'onClick="validate_brackets();" tabindex="'.($tabindex++).'"') . '&nbsp;
								<a href="' . tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('ref_id', 'id', 'action', 'path'))) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>
							</td>
						</tr>';
			$content.= '<script>
							if (document.frmDataPool.txtLevelStart != null) {
								document.frmDataPool.txtLevelStart.focus();
							}
							
							function update_custom_label(c_input_obj, c_numbering) {
								var total_loop = "'.$index.'";
								
								for (i=1; i <= total_loop; i++) {
									var c_span_obj = DOMCall("custom_span_label_"+c_numbering+"_"+i);
									c_span_obj.innerHTML = c_input_obj.value.length > 10 ? c_input_obj.value.substr(0, 10) + ".." : c_input_obj.value;
								}
							}
						</script>';
		}
		break;
		
	case "edit":
		/************************************************************************************************
			Check if i have children, and if i have children, fetch its datapool/selection group.
		************************************************************************************************/
		$data_pool_selection_select_sql = " SELECT dp.* 
											FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " AS lt, " . TABLE_DATA_POOL_REF . " AS r, " . TABLE_DATA_POOL . " AS dp 
											WHERE lt.data_pool_level_parent_id ='" . $id . "' AND lt.data_pool_ref_id=r.data_pool_ref_id AND r.data_pool_id=dp.data_pool_id";
		$data_pool_selection_result_sql = tep_db_query($data_pool_selection_select_sql);
		$data_pool_selection_row = tep_db_fetch_array($data_pool_selection_result_sql);
		
		$has_selections = (tep_db_num_rows($data_pool_selection_result_sql)) ? true : false;
		
		$current_selection_group_id = (int)$data_pool_selection_row['data_pool_id'];
		
		$nodesPullDown = array(	array('id' => 'radio','text' => TEXT_RADIO),
								array('id' => 'select','text' => TEXT_SELECT)
							);
		
		$content = '<tr>
						<td valign="top" class="main">' . TEXT_PATH . '</td>
						<td valign="top" class="main">' . htmlentities(urldecode($_GET['path']), ENT_QUOTES) . '</td>
					</tr>
					<tr>
						<td class="main">' . TEXT_SELECTION_GROUP_NAME . '</td>
						<td>' . tep_draw_input_field('selection_group_name', $data_pool_selection_row["data_pool_name"], 'size="40" id="selection_group_name"') . '</td>
					</tr>
					<tr>
						<td class="main" valign="top">' . TEXT_SELECTION_GROUP_DESCRIPTION . '</td>
						<td>' . tep_draw_textarea_field("selection_group_description", "", 50, 10, $data_pool_selection_row['data_pool_description'], 'id="selection_group_description"') . '</td>
					</tr>
					<tr>
						<td class="main">' . TEXT_SELECTION_GROUP_INPUT_TYPE . '</td>
						<td>' . tep_draw_pull_down_menu("selection_group_input_field", $nodesPullDown, $data_pool_selection_row['data_pool_input_field'], ' id="selection_group_input_field" ') . '</td>
					</tr>
					<tr>
						<td class="main" colspan="2">' . tep_draw_hidden_field('selection_group_id', $current_selection_group_id) . '<br/><hr/></td>
					</tr>';
		/*
		$bracket_roots = tep_get_bracket_roots($id);
		$has_brackets = (sizeof($bracket_roots)==0) ? false : true;
		*/
		$content.=	'<tr>
						<td class="main" colspan="2" align="right">'.tep_html_datapool_tree_dropdown($template_id).'</tr>
					 </tr>
					 <tr>
						<td class="main" colspan="2" align="right" valign="top"><input type="checkbox" name="include_subcategories" value="1" id="include_subcategories"><label for="include_subcategories">'.TEXT_INCLUDE_SUBCATEGORY.'</label></tr>
					 </tr>
					 <tr>
					 	<td colspan="2">
							<table border="0" width="100%" cellspacing="2" cellpadding="3" id="parentTable">
								<tr class="ordersBoxHeading">
									<td class="main">' .
										TEXT_SELECTION . '
									</td>
									<td class="main" align="center" width="10%">' .
										TEXT_SORT_ORDER . '
									</td>
									<td class="main" align="center" width="10%">' .
										TEXT_MIN_LEVEL . '
									</td>
									<td class="main" align="center" width="10%">' .
										TEXT_MAX_LEVEL . '
									</td>
									<td class="main" align="center" width="10%">' .
										TEXT_MODIFY . '
									</td>
									<td class="main" align="center" width="16%" bgcolor="red">
										<a href="javascript:recursiveDelete(\'ALL SELECTIONS\',\''.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('id','action','parent_id'))."action=do_delete_all&parent_id=$id").'\')"><font color="white"><b>'.TEXT_DELETE_ALL.'</b></font></a>'.'
									</td>
								</tr>';
		
		$query_selections = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_parent_id='" . $id . "' ORDER BY data_pool_sort_order, data_pool_level_name;";
		$result_selections = tep_db_query($query_selections);
		
		while ($row_selections = tep_db_fetch_array($result_selections)) {
			$path = $_GET['path']. " > ".$row_selections['data_pool_level_name'];
			$path = urlencode($path);
			
			if (sizeof(tep_get_bracket_roots($row_selections['data_pool_level_tags_id'])) > 0 ) {	// This selection has brackets
				$bracket_edit_label = '<a href="'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=brackets&ref_id=$row_selections[data_pool_ref_id]&id=$row_selections[data_pool_level_tags_id]&path=$path").'">'.TEXT_ADD_EDIT_BRACKETS.'</a>';
			} else {
				$bracket_edit_label = '<a href="'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action','path'))."action=edit&ref_id=$row_selections[data_pool_ref_id]&id=$row_selections[data_pool_level_tags_id]&path=$path").'">'.TEXT_EDIT.'</a>';
			}
			
			$content.= '		<tr>
									<td class="main">' .
										tep_draw_input_field("selection_name_update[$row_selections[data_pool_level_tags_id]]", "$row_selections[data_pool_level_name]", 'size="45" id="selection_name['.$row_selections[data_pool_level_tags_id].']"'). '
									</td>
									<td class="main" align="center">' .
										tep_draw_input_field("selection_sort_order_update[$row_selections[data_pool_level_tags_id]]", "$row_selections[data_pool_sort_order]", 'size="5" id="selection_sort_order['.$row_selections[data_pool_level_tags_id].']"'). '
									</td>
									<td class="main" align="center">' .
										tep_draw_input_field("selection_min_level_update[$row_selections[data_pool_level_tags_id]]", "$row_selections[data_pool_min_level]", 'size="5" id="selection_min_level_update['.$row_selections[data_pool_level_tags_id].']"'). '
									</td>
									<td class="main" align="center">' .
										tep_draw_input_field("selection_max_level_update[$row_selections[data_pool_level_tags_id]]", "$row_selections[data_pool_max_level]", 'size="5" id="selection_max_level_update['.$row_selections[data_pool_level_tags_id].']"'). '
									</td>
									<td class="main" align="center">' .
										$bracket_edit_label . '
									</td>
									<td class="main" align="center">
										<a href="javascript:recursiveDelete(\''.$row_selections['data_pool_level_name'].'\',\''.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action','parent_id'))."action=do_delete&ref_id=$row_selections[data_pool_ref_id]&id=$row_selections[data_pool_level_tags_id]&parent_id=$id").'\')">'.TEXT_DELETE.'</a>
									</td>
								</tr>';
		}
		
		$max_entries = 5;
		for ($z=0; $z < $max_entries; $z++) {
			$content.=	'		<tbody>
									<tr>
										<td class="main">
											<div>' .
												tep_draw_input_field('selection_name[]', "", 'size="45" id="selection_name"'). '
											<div>
										</td>
										<td class="main" align="center">
											<div>' .
												tep_draw_input_field('selection_sort_order[]', "50000", 'size="5" id="data_pool_sort_order"'). '
											<div>
										</td>
										<td class="main" align="center">
											<div>' .
												tep_draw_input_field('selection_min_level[]', '', 'size="5" id="selection_min_level"'). '
											<div>
										</td>
										<td class="main" align="center">
											<div>' .
												tep_draw_input_field('selection_max_level[]', '', 'size="5" id="selection_max_level"'). '
											<div>
										</td>
										<td class="main" align="center">
											<div>-<div>
										</td>
										<td class="main" align="center">
											<div>
												<a href="#" onClick="return removeRow(this);">'.TEXT_REMOVE.'</a>
											<div>
										</td>
									</tr>
								</tbody>';
		}
		
		$link = '<a href="javascript:addNewSelectionRow()">'.TEXT_ADD_NEW_SELECTION.'</a>';
		
		$content.=	'		</table>
						</td>
					</tr>
					<tr>
						<td>' .
							$link . '
						</td>
					</tr>
					<tr>
						<td colspan="2" valign="top">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
					</tr>
					<tr>
						<td align="left" colspan="2">'.tep_image_button('button_update.gif', IMAGE_UPDATE, " onClick='edit_level_form_checking()' ") . '&nbsp;<a href="' . tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('ref_id','id','action', 'path'))) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a></td>
					</tr>';
		
		$subTitle = TEXT_DATA_POOL_EDIT;
		break;
	
	case "insert":
		$dropDown = tep_generate_pool_dropdown($row['data_pool_id'],$ref_id);
		$content = '<tr><td valign="top" class="main">'.TEXT_PATH.'</td><td valign="top" class="main">'.htmlentities(urldecode($_GET['path']),ENT_QUOTES).'</td></tr>';
		if ($action=="edit") {
			$content.='	<tr>
							<td class="main">'.TEXT_LEVEL_NAME.'</td>
							<td>'.tep_draw_input_field('data_pool_level_name', "", 'size="40" id="data_pool_level_name"').'</td>
						</tr>
						<tr>
							<td class="main">'.TEXT_LEVEL_VALUE.'</td>
							<td>'.tep_draw_input_field('data_pool_level_value', "", 'size="10" id="data_pool_level_value"').'</td>					
						</tr>
	
						<tr>
							<td class="main">'.TEXT_DATA_SORT_ORDER.'</td>
							<td>'.tep_draw_input_field('data_pool_sort_order', "", 'size="5" id="data_pool_sort_order"').'</td>					
						</tr>';
		}
		
		$content.= '<tr>
						<td class="main">'.TEXT_DATA_POOL.'</td>
						<td>'.tep_generate_pool_dropdown(0,$ref_id).'</td>				
					</tr>
					<tr>
						<td colspan="2" valign="top">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
					</tr>
					<tr>
						<td class="main"></td>
						<td>'.tep_image_button('button_insert.gif', IMAGE_INSERT, " onClick='insert_level_form_checking()' ").'</td>			
					</tr>';
		
		$subTitle = TEXT_DATA_POOL_INSERT;
		break;		
		
	case "edit_bracket_group_bracket":	
		$tmp = array();
		$sql = "SELECT * FROM ".TABLE_BRACKETS_TAGS." WHERE brackets_tags_dependent = '".$brackets_tags_id."';";
		$result = tep_db_query($sql);
		
		//$tmp['data_pool_level_tags_id'] = $id;
		$tmp['brackets_tags_dependent'] = $brackets_tags_id;
		
		while ($row = tep_db_fetch_array($result)) {
			$tmp[$row['brackets_tags_key']] = $row['brackets_tags_value'];									
		}
		
		$tmp[KEY_PL_END_LEVEL] =  (int)$tmp[KEY_PL_END_LEVEL];
		
	case "new_bracket_group_bracket":
		$start_level = tep_get_one_time_bracket_value($brackets_groups_id,'brackets_groups_id');
		$content = '<tr>
						<td class="main" width="30%">'.TEXT_LEVEL.tep_draw_hidden_field('bracket_start_level',$start_level,' id="bracket_start_level" ').'</td>
						<td width="70%">'.tep_draw_input_field('txtLevelEnd', $tmp[KEY_PL_END_LEVEL], 'size="5" id="txtLevelEnd"').tep_draw_hidden_field('brackets_groups_id', $brackets_groups_id).'</td>
					</tr>
					<tr>
						<td class="main" width="30%">'.TEXT_LEVEL_VALUE.'</td>
						<td width="70%">'.tep_draw_input_field('txtValue', $tmp[KEY_PL_VALUE], 'size="5" id="txtValue"').'</td>
					</tr>
					<tr>
						<td class="main" width="30%">'.TEXT_INTERVAL.'</td>
						<td width="70%">'.tep_draw_input_field('txtInterval', $tmp[KEY_PL_INTERVAL], 'size="5" id="txtInterval"').'</td>
					</tr>';
		
		if ($action == "edit_bracket_group_bracket") {
			$content.= '<tr>
							<td class="main"></td>
							<td>'.tep_image_button('button_update.gif', IMAGE_UPDATE, " onClick='insert_edit_data_item_form_checking()' ").'</td>			
						</tr>';
			$subTitle = TEXT_EDIT_BRACKET;
		} else {
			$content.= '<tr>
							<td class="main"></td>
							<td>'.tep_image_button('button_insert.gif', IMAGE_INSERT, " onClick='insert_edit_data_item_form_checking()' ").'</td>			
						</tr>';	
			$subTitle = TEXT_NEW_BRACKET;		
		}
		break;
	
	case "new_template":
	case "edit_template":
		$template_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_TEMPLATE . " 
							   	WHERE data_pool_template_id = '$template_id'; ";
		$template_result_sql = tep_db_query($template_select_sql);
		$row = tep_db_fetch_array($template_result_sql);	
		
		$custom_product_select_sql = "SELECT * FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . ";";
		$custom_product_result_sql = tep_db_query($custom_product_select_sql);
		$product_types = array();
		
		while ($custom_product_row = tep_db_fetch_array($custom_product_result_sql)) {
			$product_types[] = array(	'id' => $custom_product_row['custom_products_type_id'],
										'text' => $custom_product_row['custom_products_type_name']);
		}
		
		$all_template_select_sql = "SELECT * from data_pool_template as a, custom_products_type as b where a.custom_products_type_id = b.custom_products_type_id;";
		$template_result = tep_db_query($all_template_select_sql);
		
		$nodesPullDown = array(	array(	'id' => '0',
						  				'text' => TEXT_FROM_NEW_TEMPLATE)
						  		);
		
		while ($nodes_row = tep_db_fetch_array($template_result)) {
			$nodesPullDown[] = array(	'id' => $nodes_row['data_pool_template_id'],
										'text' => TEXT_DUPLICATE_FROM . '"' . $nodes_row['data_pool_template_name'] . '['.(strlen($nodes_row['data_pool_template_description']) > 30 ? substr($nodes_row['data_pool_template_description'], 0, 30).'...' : $nodes_row['data_pool_template_description']).']"');
		}
		
		//$dropDown = tep_generate_pool_dropdown($row['data_pool_id'], $ref_id);
		$content = '<tr>
						<td class="main">'.TEXT_TEMPLATE_NAME.'</td>
						<td>'.tep_draw_input_field('data_pool_template_name', $row['data_pool_template_name'], 'size="40" id="data_pool_template_name"').tep_draw_hidden_field('template_id',$template_id).'</td>
					</tr>
					';
		$content.= '<script>
					function splitup(string,delim)
					{
						var index = string.indexOf(delim);
						var x = new Array(2);
						
						if (index == -1) {
							x[0] = "0,0";
							x[1] = string;
							
							return x;	
						} else {
							x[0] = string.substring(0,index);
							x[1] = string.substring(index+1,string.length);
							
							return x;
						}
					}
					
					function change_product_type(a) {
						var num = 0;
						var str = "";
						var id = "";
						y = splitup(a.value,\'#\');
						
						z = splitup(y[0],\',\');
						
						num = parseInt(z[0]);
						id = z[1];
						str = y[1];
						
						if (num == 0) {
							document.getElementById("id_product_type").innerHTML = \''.tep_draw_pull_down_menu("custom_products_type", $product_types, $row['custom_products_type_id'], ' id="custom_products_type" ').'\';
						} else {
							document.getElementById("id_product_type").innerHTML = "<input type=\'hidden\' name=\'custom_products_type\' value=\'"+ id +"\'>" + str;
						}
					}
					</script>';			
					
		if ($action=="new_template") {
			$content.='	<tr>
								<td class="main">'.TEXT_NODES.'</td>
								<td>'.tep_draw_pull_down_menu("nodes_type", $nodesPullDown, '', ' id="nodes_type" ').'</td>
							</tr>';
			//$content .= tep_draw_hidden_field('nodes_type','0');
			
			$content.= '<tr>
							<td class="main">'.TEXT_PRODUCT_TYPE.'</td>
							<td id="id_product_type">'.tep_draw_pull_down_menu("custom_products_type", $product_types, $row['custom_products_type_id'], ' id="custom_products_type" ').'</td>
						</tr>';	
		}
		
		$content.=	'<tr><td class="main" valign="top">'.TEXT_TEMPLATE_DESCRIPTION.'</td>
						<td>'.tep_draw_textarea_field("data_pool_template_description", "", 50, 10,$row['data_pool_template_description'], 'id="data_pool_template_description"').'</td>
					</tr>
					<tr>
						<td colspan="2" valign="top">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
					</tr>';
		
		if ($action=="new_template") {
			$content.= '<tr>
							<td class="main"></td>
							<td>' .
								tep_image_button('button_insert.gif', IMAGE_INSERT, " onClick='insert_edit_template_form_checking()' ") . '&nbsp;
								<a href="' . tep_href_link(FILENAME_DATA_POOL) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>
							</td>
						</tr>';
		} else {
			$content.= '<tr>
							<td class="main"></td>
							<td>' .
								tep_image_button('button_update.gif', IMAGE_UPDATE, " onClick='insert_edit_template_form_checking()' ") . '&nbsp;
								<a href="' . tep_href_link(FILENAME_DATA_POOL, 'template_id='.$template_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>
							</td>
						</tr>';							
		}
		
		if ($action == "edit_template")
			$subTitle = TEXT_EDIT_TEMPLATE;		
		else
			$subTitle = TEXT_INSERT_TEMPLATE;
		
		break;
	
	case "edit_bracket_group":
		$result = tep_db_query("SELECT * from ".TABLE_BRACKETS_GROUPS." as a  
							   where a.brackets_groups_id='$brackets_groups_id';");
		$row = tep_db_fetch_array($result);	
		
		$bracket_start_level = tep_get_one_time_bracket_value($brackets_groups_id,'brackets_groups_id');
		$bracket_min_level = tep_get_min_bracket_level($brackets_groups_id,'brackets_groups_id');
		
		case "new_bracket_group":
			if (trim($row['brackets_groups_sort_order'])=="") {
				$row['brackets_groups_sort_order']=50000;	
			}
			
			if ($error_code == 'DUPLICATE_CLASS_OR_NAME') {
				$content = '<tr>
								<td colspan="2" class="main"><font color="#ff0000">'.TEXT_ERROR.": ".TEXT_ERROR_DUPLICATE_CLASS_OR_NAME.'</font></td>
							</tr>';
			}
		
			$content.= '<tr>
							<td class="main">'.TEXT_BRACKET_GROUP_NAME.tep_draw_hidden_field('bracket_min_level',(int)$bracket_min_level,'id="bracket_min_level"').'</td>
							<td>'.tep_draw_input_field('bracket_group_name', $row['brackets_groups_name'], 'size="40" id="bracket_group_name"').'</td>
						</tr>
						<tr>
							<td class="main">'.TEXT_BRACKET_GROUP_SORT_ORDER.'</td>
							<td>'.tep_draw_input_field('bracket_group_sort_order', $row['brackets_groups_sort_order'], 'size="5" id="bracket_group_sort_order"').'</td>
						</tr>
						<tr>
							<td class="main">'.TEXT_BRACKET_GROUP_START_LEVEL.'</td>
							<td>'.tep_draw_input_field('bracket_group_start_level', $bracket_start_level, 'size="5" id="bracket_group_start_level"').'</td>
						</tr>		
						<tr>
							<td class="main" valign="top">'.TEXT_BRACKET_GROUP_DESCRIPTION.'</td>
							<td>'.tep_draw_textarea_field('bracket_group_description', "",50,10,$row['brackets_groups_description'], 'id="data_pool_description"').'</td>
						</tr>';
			
			$content.=	'<tr>
							<td colspan="2" valign="top">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
						</tr>';
			
			if ($action=="new_bracket_group") {
				$content.= '<tr>
								<td class="main"></td>
								<td>'.tep_image_button('button_insert.gif', IMAGE_INSERT, " onClick='insert_edit_bracket_group_form_checking()' ").'</td>			
							</tr>';
			} else {
				$content.= '<tr>
								<td class="main"></td>
								<td>'.tep_image_button('button_update.gif', IMAGE_UPDATE, " onClick='insert_edit_bracket_group_form_checking()' ").'</td>			
							</tr>
							';
			}
			
			if ($action == "edit_bracket_group")
				$subTitle = TEXT_EDIT_BRACKET_GROUP;		
			else
				$subTitle = TEXT_INSERT_NEW_BRACKET_GROUP;
		
			break;
		
		case "template_linking":
			$template_info_select_sql = "	SELECT data_pool_template_name 
											FROM " . TABLE_DATA_POOL_TEMPLATE . " 
											WHERE data_pool_template_id ='" . (int)$_REQUEST["template_id"] . "'";
			$template_info_result_sql = tep_db_query($template_info_select_sql);
			$template_info_row = tep_db_fetch_array($template_info_result_sql);
			
			$content = "<span class='redIndicator'>Note:</span> Only those products in the selected categories with the name matches this template name (<b>" . $template_info_row["data_pool_template_name"] . "</b>) will get updated!";
			
			function tep_show_list_items($ListItems, $Level=0) {
				global $languages_id, $HiddenItems, $g2c_array,$content;
				$SubTotal=0;
				foreach ($ListItems as $ListItem) {
					$NewListItems = array() ;
					$parent_to_child = array();
					
					$p_rank = tep_check_cat_tree_permissions(FILENAME_DATA_POOL, $ListItem["categories_id"]);
					
					if ($p_rank > 0) {
						$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
											FROM " . TABLE_CATEGORIES . " AS c 
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
												ON c.categories_id = cd.categories_id 
											WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
												AND cd.language_id='" . (int)$languages_id ."' 
											ORDER BY sort_order, cd.categories_name " ;
						$cat_result_sql = tep_db_query($cat_select_sql);
						while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
							$NewListItems[] = $cat_row ;
							$parent_to_child[] = $cat_row["categories_id"];
						}
						
						$SubTotal += 1 ;
						$DisplayName = htmlspecialchars(strip_tags($ListItem["categories_name"]));
						
						if (!$DisplayName) $DisplayName = "" ;
						
						if (count($NewListItems)) {					
							$DisplayName .= '&nbsp;&nbsp;[<a href="javascript:;" onClick=" expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'frmDataPool\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', true, \''.implode(",", $parent_to_child).'\');">Check All</a>]&nbsp;&nbsp;[<a href="javascript:;" onClick="expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'frmDataPool\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', false, \''.implode(",", $parent_to_child).'\');">Uncheck All</a>]';
						}
						
						if ($p_rank == 1) {
							$prod_select_sql = "SELECT products_id 
												FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
												WHERE categories_id='" . $ListItem["categories_id"] . "' 
													AND products_is_link=0 " ;
							$prod_result_sql = tep_db_query($prod_select_sql);
							if (tep_db_num_rows($prod_result_sql)) {
								$checked_value = (in_array($ListItem["categories_id"], $g2c_array) ? " checked " : "");
								$DisplayName = '<input type="checkbox" name=SelectedItem[] value="'.$ListItem["categories_id"].'" onClick="assign_selection(this); if (this.checked) { expandTree(aux'.$ListItem["categories_id"].');}" id=MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"]. $checked_value . '>' . $DisplayName;
								
								$HiddenItems[] = array('id' => $ListItem["categories_id"], 'value' => (tep_not_null($checked_value) ? 1 : 0));
							}
							
							$other_template_to_cat_select_sql = "	SELECT t.data_pool_template_name 
																	FROM " . TABLE_POOL_TEMPLATE_T0_CATEGORIES . " AS t2c 
																	INNER JOIN " . TABLE_DATA_POOL_TEMPLATE . " AS t 
																		ON t2c.data_pool_template_id=t.data_pool_template_id 
																	WHERE t2c.categories_id ='" . $ListItem["categories_id"] . "' 
																		AND t2c.data_pool_template_id <> '" . (int)$_GET['template_id'] . "';";
							$other_template_to_cat_result_sql = tep_db_query($other_template_to_cat_select_sql);
							
							if (tep_db_num_rows($other_template_to_cat_result_sql)) {
								$conflictString = '';
								while ($other_template_to_cat_row = tep_db_fetch_array($other_template_to_cat_result_sql)) {
									$conflictString .= $other_template_to_cat_row['data_pool_template_name'] . ", ";
								}
								
								if (tep_not_null($conflictString)) 	$conflictString = substr($conflictString, 0, -2);
								
								$conflictString = " [<span class='redIndicator'>Assigned to: " . $conflictString . "</font>]";
							} else {
								$conflictString = "";
							}
						}
						
						$content.= 'aux'.$ListItem["categories_id"].'= insFld('.($Level==1 ? "foldersTree" : "aux".$ListItem["parent_id"]).'
									, gFld("'.addslashes($DisplayName).$conflictString.'",';
						
						if ($url) {
						 	$content.='"javascript:MyNewWindow("'.$url.'","Open"'.$this->PopupWinWidth.','.$this->PopupWinHeight.',"yes")';
						} else {
							$content.="\"javascript:undefined\"));";
						}
						
						$content.='aux'.$ListItem["categories_id"].'_readonly = 0;';
						
						$SubTotal += tep_show_list_items($NewListItems, $Level+1) ;
					}
				}
				return $SubTotal ;
			}
			
			$g2c_array = array();
			$g2c_select_sql = "	SELECT categories_id  
								FROM data_pool_template_to_categories 
								WHERE data_pool_template_id='" . (int)$_REQUEST["template_id"] . "';";
			$g2c_result_sql = tep_db_query($g2c_select_sql);
			
			while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {
				$g2c_array[] = $g2c_row["categories_id"];
			}
			
			$ListItems = array() ;
			$HiddenItems = array();
			$parent_to_child = array();
			
			$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id = cd.categories_id 
								WHERE c.parent_id=0 
									AND cd.language_id='" . (int)$languages_id ."' 
								ORDER BY sort_order, cd.categories_name " ;
			$cat_result_sql = tep_db_query($cat_select_sql);
			while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
				$ListItems[] = $cat_row;
				$parent_to_child[] = $cat_row["categories_id"];
			}
			$content.=tep_draw_hidden_field("browser_id", '', ' id="browser_id" ');	
			$content.= '<script language="javascript">
				  			function assign_selection(obj) {
								if (obj.checked == true) {
									document.getElementById("HiddenCat_"+obj.value).value = 1;
								} else {
									document.getElementById("HiddenCat_"+obj.value).value = 0;
								}
							}
				  			var browserName = navigator.appName; 
				  			if (browserName == "Microsoft Internet Explorer")
				  				document.getElementById("browser_id").value = "IE";
				  			else
								document.getElementById("browser_id").value = "Non-IE";
				 		</script>
				 		<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
				 		<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
				 		<script>
							function checkAll(folderObj) {
								var childObj;
								var i;
								
								// Open folder
								if (!folderObj.isOpen) {
									clickOnNodeObj(folderObj)
								}
							}
							
							function expandTree(folderObj) {
						    	var childObj;
								var i;
								
								// Open folder
								if (!folderObj.isOpen)
									clickOnNodeObj(folderObj)
							}
							
							function collapseTree() {
								//hide all folders
								clickOnNodeObj(foldersTree)
								//restore first level
								clickOnNodeObj(foldersTree)
							}
							//Environment variables are usually set at the top of this file.
							USELINKFORFOLDER = 0
							USETEXTLINKS = 0
							STARTALLOPEN = 0
							USEFRAMES = 0
							USEICONS = 0
							WRAPTEXT = 1
							PRESERVESTATE = 1
							ICONPATH = "includes/javascript/Treeview/"
							BUILDALL = 0
							HIGHLIGHT = 0;
							foldersTree = gFld("'.TEXT_TOP.'&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox(\'frmDataPool\', \'MSel_-1_0'.'\', true, \''.implode(",", $parent_to_child).'\');\">Check All</a>]&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox(\'frmDataPool\', \'MSel_-1_0'.'\', false, \''.implode(",", $parent_to_child).'\');\">Uncheck All</a>]", "")
							';
							
			$SubTotal = tep_show_list_items($ListItems, 1);
			$content.= '</script>';
			$content.='<tr>	<td>
							  <a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
							  <span class=formvalue>
							  <script>initializeDocument()</script>
							  <noscript>
								  A tree for site navigation will open here if you enable JavaScript in your browser.
							  </noscript>
							  </span>
							</td>
						</tr>
						<tr>
							<td colspan="2" align="right">
								'.tep_image_submit('button_update.gif', IMAGE_UPDATE, "") . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_DATA_POOL) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'.'
							</td>
						</tr>';				
			
			for ($i=0; $i<count($HiddenItems); $i++) {
				$content.= "<input type='hidden' name=HiddenCat[".$HiddenItems[$i]['id']."] value=".$HiddenItems[$i]['value']." id=HiddenCat_".$HiddenItems[$i]['id'].">\n";
			}
			break;
		case "new_option":
		case "edit_option":
			$option_values_array = array();
			$tplID = (int)$_REQUEST["template_id"];
			if ($action == "edit_option") {
				$option_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_options_id ='" . (int)$_REQUEST["optionID"] . "'";
				$option_result_sql = tep_db_query($option_select_sql);
				$option_row = tep_db_fetch_array($option_result_sql);
				
				$option_values_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . (int)$_REQUEST["optionID"] . "' ORDER BY data_pool_options_values_sort_order, data_pool_options_values_id";
				$option_values_result_sql = tep_db_query($option_values_select_sql);
				while ($option_values_row = tep_db_fetch_array($option_values_result_sql)) {
					$option_values_array[] = $option_values_row;
				}
			}
			
			$subTitle = SUBTITLE_ADD_OPTIONS;
			
			ob_start();
				echo tep_draw_hidden_field("optionID", $_REQUEST["optionID"]);
				echo tep_draw_hidden_field("tplID", $tplID);
				
			if ($option_row["data_pool_options_input_type"] == 999) {
				echo tep_draw_hidden_field("option_input_type", 999);
?>
					<tr>
	   					<td width="100%">
	    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_OPTIONS_TITLE?></td>
									<td class="main" valign="top"><?=$option_row["data_pool_options_title"]?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_OPTION_SHOW_SUPPLIER?></td>
			      					<td class="main">
			      						<?=tep_draw_checkbox_field('show_supplier_option', '1', $option_row["data_pool_options_show_supplier"]=="1" ? true : false)?>
			      					</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
			      					<td class="main" valign="top"><?=ENTRY_OPTION_ORDER?></td>
			        				<td class="main">
			        					<?=tep_draw_input_field('option_order', $option_row["data_pool_options_sort_order"] ? $option_row["data_pool_options_sort_order"] : 50000, 'size="6" id="option_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"')?>
			        				</td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_image_submit('button_update.gif', IMAGE_UPDATE, '') . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_DATA_POOL, 'template_id='.$tplID) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
<?
			} else {
?>
				<tr>
   					<td width="100%">
    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" width="21%" valign="top"><?=ENTRY_OPTIONS_TITLE?></td>
								<td class="main" valign="top"><?=tep_draw_input_field('option_title', $option_row["data_pool_options_title"], 'size="70" id="option_title"')?></td>
							</tr>
							<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
							<tr>
								<td class="main" width="21%" valign="top"><?=ENTRY_OPTIONS_CLASS?></td>
								<?
									$option_class_array = array();
									$option_class_array[] = array('id' => 'GENERAL', 'text' => 'GENERAL');
									$option_class_array[] = array('id' => 'ACCOUNT USERNAME', 'text' => 'ACCOUNT USERNAME');
									$option_class_array[] = array('id' => 'ACCOUNT PASSWORD', 'text' => 'ACCOUNT PASSWORD');
									$option_class_array[] = array('id' => 'CHARACTER NAME', 'text' => 'CHARACTER NAME');
									$option_class_array[] = array('id' => 'REALM', 'text' => 'REALM');
									$option_class_array[] = array('id' => 'FACTION', 'text' => 'FACTION');
								?>
								<td class="main" valign="top"><?=tep_draw_pull_down_menu('option_class', $option_class_array, $option_row["data_pool_options_class"])?></td>
							</tr>
							<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
							<tr>
								<td class="main" valign="top"><?=ENTRY_OPTION_FIELD_TYPE?></td>
								<td class="main">
									<?=tep_draw_pull_down_menu("option_input_type", $field_type_array, $option_row["data_pool_options_input_type"], ' id="option_input_type" onChange="Todo(\'div13309\', value);"')?>
								</td>
							</tr>
							<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
		      				<tr>
		      					<td class="main" valign="top"><?=ENTRY_OPTION_SETTING?></td>
		        				<td class="main">
		        				<div id="div13309"></div>
		        				</td>
		      				</tr>
		      				<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
		      				<tr>
		      					<td class="main" valign="top"><?=ENTRY_OPTION_MANDATORY?></td>
		        				<td class="main">
		        					<?=tep_draw_radio_field('mandatory_option', '1', $option_row["data_pool_options_required"]=="1" ? true : false) . "&nbsp;" . 'Yes' . "&nbsp;" . tep_draw_radio_field('mandatory_option', '0', $option_row["data_pool_options_required"]=="1" ? false : true) . "&nbsp;" . 'No'?>
		        				</td>
		      				</tr>
		      				<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
		      				<tr>
		      					<td class="main" valign="top"><?=ENTRY_OPTION_SHOW_SUPPLIER?></td>
		      					<td class="main">
		      						<?=tep_draw_checkbox_field('show_supplier_option', '1', $option_row["data_pool_options_show_supplier"]=="1" ? true : false)?>
		      					</td>
		      				</tr>
		      				<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
		      				<tr>
		      					<td class="main" valign="top"><?=ENTRY_OPTION_ORDER?></td>
		        				<td class="main">
		        					<?=tep_draw_input_field('option_order', $option_row["data_pool_options_sort_order"] ? $option_row["data_pool_options_sort_order"] : 50000, 'size="6" id="option_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"')?>
		        				</td>
		      				</tr>
							<tr>
								<td colspan="2" align="right">
									<?=($action=="new_option" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, 'onClick="return edit_option_form_checking()"') : tep_image_submit('button_update.gif', IMAGE_UPDATE, 'onClick="return edit_option_form_checking()"')) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_DATA_POOL, 'template_id='.$tplID) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
								</td>
							</tr>
							<tr>
		        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		      				</tr>
						</table>
    				</td>
    			</tr>
    			<script>
        		<!--
        			var opt_val_array = new Array();
        		<?
	        		if (tep_not_null($option_row["data_pool_options_input_size"])) {
	        			list($first_val, $second_val) = explode(',', $option_row["data_pool_options_input_size"]);
	        	?>
	        			var first_box_val = '<?=$first_val?>';
	        			var second_box_val = '<?=$second_val?>';
	        	<?	} else { ?>
	        			var first_box_val = '';
	        			var second_box_val = '';
	        	<?	}
	        		
	        		for ($i=0; $i < count($option_values_array); $i++) {
	        			if (count($option_values_array[$i])) {
	        				echo "opt_val_array[".$i."] = new Array();\n";
	        				foreach ($option_values_array[$i] as $key => $val) {
	        					echo "opt_val_array[".$i."]['".$key."'] = '".htmlentities(addslashes($val))."';\n";
	        				}
	        			}
	        		}
	        	?>
        			function Todo(name, val) {
						var out = '';
						switch(val) {
							case "1":
								out = getTextBoxParam();
								document.getElementById(name).innerHTML = out;
								break;
							case "2":
								out = getTextAreaParam();
								document.getElementById(name).innerHTML = out;
								break;
							case "3":
							case "4":
								out = getSelectBoxParam();
								document.getElementById(name).innerHTML = out;
								break;
							case "5":	// Date selection box
								out = getDateSelectionBoxParam();
								document.getElementById(name).innerHTML = out;
								break;
							case "6":	// Display label
								//out = getDateSelectionBoxParam();
								document.getElementById(name).innerHTML = 'No setting needed! This is just a normal text label.';
								break;
							case "7":	// Information text
								out = getInfoTextParam();
								document.getElementById(name).innerHTML = out;
								break;
						}
					}
					
					function getTextBoxParam() {
						var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main">Size</td><td class="main"><input name="box_size" id="box_size" type="text" value="'+first_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Maximum Character</td><td class="main"><input name="box_text_max_len" id="box_text_max_len" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<tr><td class="main">Min. Level</td><td class="main"><input name="option_min_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_min_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Max. Level</td><td class="main"><input name="option_max_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_max_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<tr><td class="main">Price</td><td class="main"><input name="option_price[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_price'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td><td class="main">Time</td><td class="main"><input name="option_time[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_eta'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td></tr>';
						dyn_html += '</table></div>';
						
						return dyn_html;
					}
					
					function getTextAreaParam() {
						var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main">Row</td><td class="main"><input name="box_row" id="box_row" type="text" value="'+first_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Column</td><td class="main"><input name="box_col" id="box_col" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<tr><td class="main">Min. Level</td><td class="main"><input name="option_min_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_min_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Max. Level</td><td class="main"><input name="option_max_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_max_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<tr><td class="main">Price</td><td class="main"><input name="option_price[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_price'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td><td class="main">Time</td><td class="main"><input name="option_time[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_eta'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td></tr>';
						dyn_html += '</table></div>';
						
						return dyn_html;
					}
					
					function getSelectBoxParam() {
						var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" width="100%" cellspacing="2" cellpadding="2" id="optionValuesTable"><tbody><tr class="ordersBoxHeading"><td class="main">'+
											'<?=TEXT_OPTION?>'+'</td><td class="main" align="center" width="10%">'+
											'<?=TEXT_SORT_ORDER?>'+'</td><td class="main" align="center" width="10%">'+
											'<?=TEXT_MIN_LEVEL?>'+'</td><td class="main" align="center" width="10%">'+
											'<?=TEXT_MAX_LEVEL?>'+'</td><td class="main" align="center" width="10%">'+
											'<?=TEXT_OPTION_PRICE?>'+'</td><td class="main" align="center" width="10%">'+
											'<?=TEXT_OPTION_ETA?>'+'</td><td class="main" align="center" width="10%">'+'<?=TEXT_REMOVE_OPTION?>'+'</td></tr>';
						for (opt_cnt=0; opt_cnt < opt_val_array.length; opt_cnt++)  {
							dyn_html += '<tr><td class="main">'+'<?=tep_draw_input_field('option_name[]', "'+opt_val_array[opt_cnt]['data_pool_options_value']+'", 'size="45" ')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_sort_order[]', "'+opt_val_array[opt_cnt]['data_pool_options_values_sort_order']+'", 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_min_level[]', "'+opt_val_array[opt_cnt]['data_pool_options_values_min_level']+'", 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_max_level[]', "'+opt_val_array[opt_cnt]['data_pool_options_values_max_level']+'", 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_price[]', "'+opt_val_array[opt_cnt]['data_pool_options_values_price']+'", 'size="5" onKeyUP="if (trim_str(this.value) != \\\'\\\' && !currencyValidation(trim_str(this.value))) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_time[]', "'+opt_val_array[opt_cnt]['data_pool_options_values_eta']+'", 'size="5" onKeyUP="if (trim_str(this.value) != \\\'\\\' && !currencyValidation(trim_str(this.value))) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center"><a href="#" onClick="return delRow(this, \'optionValuesTable\');">'+'<?=LINK_REMOVE_OPTION?>'+'</a></td>';
							dyn_html += '</tr>';
						}
						
						for (blank=0; blank < 5; blank++) {
							dyn_html += '<tr><td class="main">'+'<?=tep_draw_input_field('option_name[]', "", 'size="45" ')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_sort_order[]', "50000", 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_min_level[]', '', 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_max_level[]', '', 'size="5" onKeyUP="if (trim_str(this.value)==\\\'\\\' || (trim_str(this.value) != \\\'\\\' && !validateInteger(trim_str(this.value))) ) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_price[]', '', 'size="5" onKeyUP="if (trim_str(this.value) != \\\'\\\' && !currencyValidation(trim_str(this.value))) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center">'+'<?=tep_draw_input_field('option_time[]', '', 'size="5" onKeyUP="if (trim_str(this.value) != \\\'\\\' && !currencyValidation(trim_str(this.value))) { this.value = \\\'\\\'; }"')?>'+'</td>';
							dyn_html += '<td class="main" align="center"><a href="#" onClick="return delRow(this, \'optionValuesTable\');">'+'<?=LINK_REMOVE_OPTION?>'+'</a></td>';
							dyn_html += '</tr>';
						}
						
						dyn_html += '</tbody></table></div>';
						dyn_html += '<div style="margin-left: 2px;">[<a href="javascript:addNewOptionRow(\'optionValuesTable\')">'+'<?=TEXT_ADD_NEW_OPTION?>'+'</a>]</div>';
						
						return dyn_html;
					}
					
					function getDateSelectionBoxParam() {
						var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main" valign="top">From</td><td class="main"><input name="box_from" id="box_from" type="text" value="'+first_box_val+'" size="12" onBlur="if (trim_str(this.value) != \'\' && (this.value != \'TODAY\' && !validateDate(trim_str(this.value))) ) { this.value = \'TODAY\'; }"><br><small>Options:<br>YYYY-MM-DD<br>"TODAY" to use system current date</small></td><td class="main" valign="top">Periods (days)</td><td class="main" valign="top"><input name="box_period" id="box_period" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<tr><td class="main">Min. Level</td><td class="main"><input name="option_min_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_min_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Max. Level</td><td class="main"><input name="option_max_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_max_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '</table></div>';
						
						return dyn_html;
					}
					
					function getInfoTextParam() {
						var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main" colspan="4">Display Text&nbsp;<input name="option_name[]" type="text" value="'+(opt_val_array.length > 0 ? opt_val_array[0]['data_pool_options_value'] : '')+'" size="50"></td></tr>';
						dyn_html += '<tr><td class="main">Min. Level</td><td class="main"><input name="option_min_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_min_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Max. Level</td><td class="main"><input name="option_max_level[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_max_level'] : '')+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
						dyn_html += '<!--tr><td class="main">Price</td><td class="main"><input name="option_price[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_price'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td><td class="main">Time</td><td class="main"><input name="option_time[]" type="text" value="'+(opt_val_array.length> 0 ? opt_val_array[0]['data_pool_options_values_eta'] : '')+'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"></td></tr-->';
						dyn_html += '</table></div>';
						
						return dyn_html;
					}
					
					function addNewOptionRow(tbl_id) {
						var tbody = document.getElementById(tbl_id).getElementsByTagName('tbody')[0];
						var row = document.createElement('TR');
						var option_cell = document.createElement('TD');
						var sorting_cell = document.createElement('TD');
						var min_cell = document.createElement('TD');
						var max_cell = document.createElement('TD');
						var price_cell = document.createElement('TD');
						var time_cell = document.createElement('TD');
						var rem_cell = document.createElement('TD');
						
						var option_field = document.createElement('INPUT');
						option_field.setAttribute('type','text');
						option_field.setAttribute('name', 'option_name[]');
						option_field.setAttribute('size', '45');
						
						option_cell.setAttribute('className', 'main');
						option_cell.setAttribute('class', 'main');
						option_cell.appendChild(option_field);
						
						var sorting_field = document.createElement('INPUT');
						sorting_field.setAttribute('type','text');
						sorting_field.setAttribute('name', 'option_sort_order[]');
						sorting_field.setAttribute('size', '5');
						sorting_field.setAttribute('value', '50000');
						sorting_field.onkeyup=function() {
							if (trim_str(this.value)=='' || (trim_str(this.value) != '' && !validateInteger(trim_str(this.value))) )  {
								this.value = '';
							}
						}
						
						sorting_cell.setAttribute('className', 'main');
						sorting_cell.setAttribute('class', 'main');
						sorting_cell.setAttribute('align', 'center');
						sorting_cell.appendChild(sorting_field);
						
						var min_field = document.createElement('INPUT');
						min_field.setAttribute('type','text');
						min_field.setAttribute('name', 'option_min_level[]');
						min_field.setAttribute('size', '5');
						min_field.onkeyup=function() {
							if (trim_str(this.value)=='' || (trim_str(this.value) != '' && !validateInteger(trim_str(this.value))) )  {
								this.value = '';
							}
						}
						
						min_cell.setAttribute('className', 'main');
						min_cell.setAttribute('class', 'main');
						min_cell.setAttribute('align', 'center');
						min_cell.appendChild(min_field);
						
						var max_field = document.createElement('INPUT');
						max_field.setAttribute('type','text');
						max_field.setAttribute('name', 'option_max_level[]');
						max_field.setAttribute('size', '5');
						max_field.onkeyup=function() {
							if (trim_str(this.value)=='' || (trim_str(this.value) != '' && !validateInteger(trim_str(this.value))) )  {
								this.value = '';
							}
						}
						
						max_cell.setAttribute('className', 'main');
						max_cell.setAttribute('class', 'main');
						max_cell.setAttribute('align', 'center');
						max_cell.appendChild(max_field);
						
						var price_field = document.createElement('INPUT');
						price_field.setAttribute('type','text');
						price_field.setAttribute('name', 'option_price[]');
						price_field.setAttribute('size', '5');
						price_field.onkeyup=function() {
							if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) {
								this.value = '';
							}
						}
						
						price_cell.setAttribute('className', 'main');
						price_cell.setAttribute('class', 'main');
						price_cell.setAttribute('align', 'center');
						price_cell.appendChild(price_field);
						
						var time_field = document.createElement('INPUT');
						time_field.setAttribute('type','text');
						time_field.setAttribute('name', 'option_time[]');
						time_field.setAttribute('size', '5');
						time_field.onkeyup=function() {
							if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) {
								this.value = '';
							}
						}
						
						time_cell.setAttribute('className', 'main');
						time_cell.setAttribute('class', 'main');
						time_cell.setAttribute('align', 'center');
						time_cell.appendChild(time_field);
						
						var rem_field = document.createElement('A');
						rem_field.innerHTML = '<?=LINK_REMOVE_OPTION?>';
						rem_field.href= '#';
						rem_field.onclick=function() {
							return delRow(this, 'optionValuesTable');
						}
						
						rem_cell.setAttribute('className', 'main');
						rem_cell.setAttribute('class', 'main');
						rem_cell.setAttribute('align', 'center');
						rem_cell.appendChild(rem_field);
						
						row.appendChild(option_cell);
						row.appendChild(sorting_cell);
						row.appendChild(min_cell);
						row.appendChild(max_cell);
						row.appendChild(price_cell);
						row.appendChild(time_cell);
						row.appendChild(rem_cell);
						tbody.appendChild(row);
					}
					
					var cur_sel = document.getElementById('option_input_type').selectedIndex + 1;
					Todo('div13309', cur_sel.toString(10));
					//-->
				</script>
<?
			}
				$content .= ob_get_contents();
				ob_end_clean() ;
			break;
		case "bracket_group_level_linking":
			$assigned_links = array();
			
			$sql="SELECT distinct data_pool_level_tags_id FROM ".TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS." WHERE brackets_groups_id='$brackets_groups_id';";
			$result = tep_db_query($sql);
			
			if (tep_db_num_rows($result)) {
				while($row = tep_db_fetch_array($result)) {
					array_push($assigned_links,(int)$row['data_pool_level_tags_id']);					
				}
			}
?>
			<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
			<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
			<script>
			function checkAll(folderObj) {
				var childObj;
				var i;
				
				if (!folderObj.isOpen) {
					clickOnNodeObj(folderObj)
				}
			}
			
			function expandTree(folderObj) {
				var childObj;
				var i;
				
				// Open folder
				if (!folderObj.isOpen)
					clickOnNodeObj(folderObj)
			}
			
			// Close all folders
			function collapseTree() {
				//hide all folders
				clickOnNodeObj(foldersTree)
				
				//restore first level
				clickOnNodeObj(foldersTree)
			}
			
			//Environment variables are usually set at the top of this file.
			USELINKFORFOLDER = 0
			USETEXTLINKS = 0
			STARTALLOPEN = 1
			USEFRAMES = 0
			USEICONS = 0
			WRAPTEXT = 1
			PRESERVESTATE = 1
			ICONPATH = 'includes/javascript/Treeview/'
			BUILDALL = 0
			HIGHLIGHT = 0;
			</script>
<?			echo "<script>foldersTree = gFld('','');\n\n</script>";
			$sql = "select * from ".TABLE_DATA_POOL_TEMPLATE." order by data_pool_template_name;";			
			$result = tep_db_query($sql);
			
			if (tep_db_num_rows($result)) {
				while($row = tep_db_fetch_array($result)) {
					echo '<script>template'.$row['data_pool_template_id'].' = insFld(foldersTree,gFld("<u><b>'.addslashes($row['data_pool_template_name']).'</b></u>",""));'."\n\n".'template'.$row['data_pool_template_id'].'._readonly = 0;'."\n\n".tep_html_datapool_tree($row['data_pool_template_id'],"Untitled",true)."</script>";
					
					//[<a href=\"'.tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('ref_id','id','action'))."action=edit_template&id=$row[data_pool_template_id]").'\">Edit</a> | <a href=\"javascript:recursiveDelete(\''.$row['data_pool_template_name'].'\',\''.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=delete_template&id=$row[data_pool_template_id]").'\')\">Delete</a>]
				}
				
				$content.= '<tr><td>
								<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
								<span class=formvalue>
								<script>initializeDocument();</script>
								<noscript>
								A tree for site navigation will open here if you enable JavaScript in your browser.
								</noscript>
								</span>
							</td></tr><tr><td align="right">'.tep_image_submit('button_update.gif', IMAGE_UPDATE, "") . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_DATA_POOL,"action=bracket_group") . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'.'</td></tr>';
			}
		
			break;
		
		default:
?>
			<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
			<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
			<script>
			function checkAll(folderObj) 
			{
				var childObj;
				var i;
				
				if (!folderObj.isOpen) {
					clickOnNodeObj(folderObj)
				}
			}
			
			function recursiveDelete(name,strLink) {
				var yn = confirm("Are you sure you want to delete '" + name + "' and all its children?");
				
				if (yn)
					document.location.href = strLink;
			}
			
			function expandTree(folderObj) {
				var childObj;
				var i;
				
				// Open folder
				if (!folderObj.isOpen)
					clickOnNodeObj(folderObj)
			}
			
			// Close all folders
			function collapseTree() {
				//hide all folders
				clickOnNodeObj(foldersTree)
				
				//restore first level
				clickOnNodeObj(foldersTree)
			}
													
			//Environment variables are usually set at the top of this file.
			USELINKFORFOLDER = 0
			USETEXTLINKS = 0
			STARTALLOPEN = 0
			USEFRAMES = 0
			USEICONS = 0
			WRAPTEXT = 1
			PRESERVESTATE = 1
			ICONPATH = 'includes/javascript/Treeview/'
			BUILDALL = 0
			HIGHLIGHT = 0;
			</script>
<?
			//$content = "";
			$content = '<tr><td>[ <a href="'.tep_href_link(FILENAME_DATA_POOL, "action=new_template").'">'.TEXT_NEW_TEMPLATE.'</a> ]</td></tr>';
			if ($template_id > 0) {
				$template_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_TEMPLATE . " WHERE data_pool_template_id ='" . $template_id . "';";
				$template_result_sql = tep_db_query($template_select_sql);
				
				if ($template_row = tep_db_fetch_array($template_result_sql)) {
					echo "<script>foldersTree = gFld('<b>".addslashes($template_row['data_pool_template_name'])."</b> [<a href=\"".tep_href_link(FILENAME_DATA_POOL, tep_get_all_get_params(array('ref_id','id','action'))."action=edit_template&id=$row[data_pool_template_id]")."\">".TEXT_EDIT_TEMPLATE_INFO."</a>]', '');\n\n</script>";
					echo "<script>".tep_html_datapool_tree($template_row['data_pool_template_id'])."</script>";
					
					//[<a href=\"'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=edit_template&id=$row[data_pool_template_id]").'\">Edit</a> | <a href=\"javascript:recursiveDelete(\''.$row['data_pool_template_name'].'\',\''.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=delete_template&id=$row[data_pool_template_id]").'\')\">Delete</a>]
					
					$content .= '<tr><td>
								<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
								<span class=formvalue>
								<script>initializeDocument();</script>
								<noscript>
									A tree for site navigation will open here if you enable JavaScript in your browser.
								</noscript>
								</span>
								</td></tr>';
				}
			}
			
			break;
	}
	
	if ($action_type == "bracket_group" || $brackets_groups_id > 0) {
		/********************************************************************************
			This is the bracket group section.
			For batch update of bracket values.
			Currently taken off this feature.
		********************************************************************************/
		$bracket_group_select_sql = "	SELECT bg.brackets_groups_id, bg.brackets_groups_name, bg.brackets_groups_description, 
											bg.brackets_groups_visible, bg.brackets_groups_sort_order, bt.brackets_tags_value 
										FROM " . TABLE_BRACKETS_GROUPS . " AS bg 
										LEFT JOIN " . TABLE_BRACKETS_TAGS . " AS bt 
											ON bt.brackets_groups_id = bg.brackets_groups_id and 
												bt.brackets_tags_key='".KEY_PL_START_LEVEL."' 
										ORDER BY bg.brackets_groups_sort_order, bg.brackets_groups_name;";
		
		//$sql_template = "select * from ".TABLE_BRACKETS_GROUPS." as bg, ".TABLE_BRACKETS_TAGS." AS bt where bt.brackets_groups_id = bg.brackets_groups_id
		//				 and bt.brackets_tags_key='".KEY_PL_START_LEVEL."' order by bg.brackets_groups_sort_order,bg.brackets_groups_name;";
		$bracket_group_result_sql = tep_db_query($bracket_group_select_sql);			
		
		if (tep_db_num_rows($bracket_group_result_sql)) {
			$content.= '<tr><td colspan="2">';
			$content.= '<table border="0" width="100%" cellspacing="1" cellpadding="2">';
			$content.= '<tr><td colspan="4">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td></tr>
							<tr>
								<td class="reportBoxHeading" width="5%">'.TABLE_HEADING_ACTION.'</td>
								<td class="reportBoxHeading">'.TABLE_HEADING_BRACKET_GROUP_NAME.'</td>
								<td class="reportBoxHeading">'.TABLE_HEADING_BRACKET_GROUP_SORT_ORDER.'</td>
								<td class="reportBoxHeading">'.TABLE_HEADING_BRACKET_GROUP_START_LEVEL.'</td>
								<td class="reportBoxHeading">'.TABLE_HEADING_BRACKET_GROUP_DESCRIPTION.'</td>
								<td class="reportBoxHeading">'.TABLE_HEADING_BRACKET_GROUP_BRACKETS.'</td>
							</tr>';
			
			$row_count = 0;
			$content_append = $current_content = "";
			while ($bracket_group_row = tep_db_fetch_array($bracket_group_result_sql)) {
				$count_products = 0;
				//$sql_products_count = "select count(a.custom_products_type_id) from ".TABLE_PRODUCTS." as a, ".TABLE_DATA_POOL_TEMPLATE." as b 
				//					   where a.custom_products_type_id = b.custom_products_type_id and b.data_pool_template_id='".$bracket_group_row['data_pool_template_id']."';";
				
				$sql_products_count = "	SELECT COUNT(a.data_pool_level_tags_id) AS counter 
										FROM ".TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS." AS a 
									   	WHERE a.brackets_groups_id = '" . $bracket_group_row['brackets_groups_id'] . "';";
				
				$products_count_result = tep_db_query($sql_products_count);			
				
				if ($row_products_count = tep_db_fetch_array($products_count_result)) {
					$count_products = (int)$row_products_count['counter'];
				}
				
				$buttonHtml = '';
				if ($count_products > 0) {
					$buttonHtml = "<br><br>".'<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=do_apply_brackets&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image_button("button_update.gif", "Apply Brackets", "", "", 'align="top"').'</a>';
				}
				
				if ($brackets_groups_id == $bracket_group_row['brackets_groups_id']) {
					// Currently edited bracket group
					$row_style = 'rowSelected';
				} else {
					$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
					++$row_count;
				}
				
				// This return all the bracket record rows for this bracket group
				$result_subitems = tep_get_bracket_roots($bracket_group_row['brackets_groups_id'], 'brackets_groups_id');
				$subitems_html = '';
				if (sizeof($result_subitems)) {
					$subitems_html = '<table width="100%" cellspacing="0" cellpading="0">
										<tr>
					       			  		<td colspan="4">
									  			<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
									  		</td>
					          			  	</tr>
											  	<tr>
											  		<td class="dataTableContent" width="25%">'.TABLE_HEADING_BRACKET_ACTION.'</td>
													<td class="dataTableContent" width="25%">'.TABLE_HEADING_BRACKET_END_LEVEL.'</td>
													<td class="dataTableContent" width="25%">'.TABLE_HEADING_BRACKET_VALUE.'</td>
													<td class="dataTableContent" width="25%">'.TABLE_HEADING_BRACKET_INTERVAL.'</td>
												</tr>
												<tr>
													<td colspan="4">
														<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
													</td>
												</tr>';
					$links_delete_edit = '';
					$arr_brackets = array();
					foreach ($result_subitems as $row_subitems) {
						$tmp = array();
						$bracket_dependence_select_sql = "SELECT brackets_tags_key, brackets_tags_value FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent = '" . $row_subitems['brackets_tags_id'] . "';";
						$bracket_dependence_result_sql = tep_db_query($bracket_dependence_select_sql);
						
						//$tmp['data_pool_level_tags_id'] = $id;
						$tmp['brackets_tags_dependent'] = $row_subitems['brackets_tags_id'];
						
						while ($bracket_dependence_row = tep_db_fetch_array($bracket_dependence_result_sql)) {
							$tmp[$bracket_dependence_row['brackets_tags_key']] = $bracket_dependence_row['brackets_tags_value'];									
						}
						
						$tmp[KEY_PL_END_LEVEL] =  (int)$tmp[KEY_PL_END_LEVEL];
						$arr_brackets[] = $tmp;
					}
					
					$arr_brackets = tep_bubble_sort($arr_brackets);
					
					foreach ($arr_brackets as $arr_bracket) {
						$links_delete_edit = '<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=edit_bracket_group_bracket&brackets_tags_id='.$arr_bracket['brackets_tags_dependent'].'&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;
											  <a href="javascript:void(confirm_delete(\''.$arr_bracket['brackets_tags_id'].'\', \'bracket\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_bracket_group_bracket&brackets_tags_id='.$arr_bracket['brackets_tags_dependent'].'&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Bracket", "", "", 'align="top"').'</a>';
						
						$subitems_html.='<tr>
											 <td class="dataTableContent">
											 '.$links_delete_edit.'		
											 </td>
											 <td class="dataTableContent">
											 '.$arr_bracket[KEY_PL_END_LEVEL].'
											 </td>
											 <td class="dataTableContent">
											 '.$arr_bracket[KEY_PL_VALUE].'
											 </td>
											 <td class="dataTableContent">
											 '.$arr_bracket[KEY_PL_INTERVAL].'
											 </td>																
										 </tr>';
					}
					$subitems_html.='</table>';
				}
				
				if ($brackets_groups_id == $bracket_group_row['brackets_groups_id']) {
					$current_content = '<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
										 	<td class="reportRecords" width="5%" valign="top" align="center">'.
											'<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=edit_bracket_group&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;
											<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=bracket_group_level_linking&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image(DIR_WS_ICONS."linking.gif", "View All Nodes", "", "", 'align="top"').'</a>&nbsp;';
					
					$current_content.=	'&nbsp;<a href="javascript:void(confirm_delete(\''.$bracket_group_row['data_pool_name'].'\', \'bracket group\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_bracket_group&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Bracket Group", "", "", 'align="top"').'</a>'.$buttonHtml;
					
					$current_content.=	 '</td>
											 <td class="reportRecords" valign="top">'.$bracket_group_row['brackets_groups_name'].'<br></td>
											 <td class="reportRecords" valign="top">'.(int)$bracket_group_row['brackets_groups_sort_order'].'</td>
											 <td class="reportRecords" valign="top">'.(int)$bracket_group_row['brackets_tags_value'].'</td>
											 <td class="reportRecords" valign="top">'.$bracket_group_row['brackets_groups_description'].'</td>
											 <td class="reportRecords" valign="top">[<a href="'.tep_href_link(FILENAME_DATA_POOL,'action=new_bracket_group_bracket&brackets_groups_id='.(int)$bracket_group_row['brackets_groups_id']).'">'.TEXT_ADD_BRACKET.'</a>]'.'<BR><BR>'.$subitems_html.'</td>
										 </tr>';						
				} else {
					$content_append.= '<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
										 <td class="reportRecords" width="5%" valign="top" align="center">'.
										'<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=edit_bracket_group&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;
										<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=bracket_group_level_linking&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'">'.tep_image(DIR_WS_ICONS."linking.gif", "View All Nodes", "", "", 'align="top"').'</a>&nbsp;';
					
					$content_append.='&nbsp;<a href="javascript:void(confirm_delete(\''.$bracket_group_row['data_pool_name'].'\', \'bracket group\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_bracket_group&brackets_groups_id='.$bracket_group_row['brackets_groups_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Bracket Group", "", "", 'align="top"').'</a>'.$buttonHtml;
					
					$content_append.=	'</td>
											 <td class="reportRecords" valign="top">'.$bracket_group_row['brackets_groups_name'].'<br></td>
											 <td class="reportRecords" valign="top">'.(int)$bracket_group_row['brackets_groups_sort_order'].'</td>
											 <td class="reportRecords" valign="top">'.(int)$bracket_group_row['brackets_tags_value'].'</td>
											 <td class="reportRecords" valign="top">'.$bracket_group_row['brackets_groups_description'].'</td>
											 <td class="reportRecords" valign="top">[<a href="'.tep_href_link(FILENAME_DATA_POOL,'action=new_bracket_group_bracket&brackets_groups_id='.(int)$bracket_group_row['brackets_groups_id']).'">'.TEXT_ADD_BRACKET.'</a>]'.'<BR><BR>'.$subitems_html.'</td>
										 </tr>';						
				}			
			}					
			
			$content.= $current_content.$content_append.'</table></td></tr>';				
		}
	} else {
		$template_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_TEMPLATE . " AS a, " . TABLE_CUSTOM_PRODUCTS_TYPE . " AS b 
					 			WHERE a.custom_products_type_id=b.custom_products_type_id order by a.data_pool_template_name";
		
		if ($show_records != "ALL") {
			$template_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $template_select_sql, $template_select_sql_numrows, true);
		}
		
		$template_result_sql = tep_db_query($template_select_sql);
		
		if (tep_db_num_rows($template_result_sql)) {
			$content .= '<tr>
							<td colspan="2">
								<table border="0" width="100%" cellspacing="1" cellpadding="2">
									<tr><td colspan="4">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td></tr>';
			$content .= '			<tr>
										<td class="reportBoxHeading" width="7%">'.TABLE_HEADING_ACTION.'</td>
										<td class="reportBoxHeading">'.TABLE_HEADING_TEMPLATE_NAME.'</td>
										<td class="reportBoxHeading">'.TABLE_HEADING_TEMPLATE_TYPE.'</td>
										<td width="30%" class="reportBoxHeading">'.TABLE_HEADING_TEMPLATE_DESCRIPTION.'</td>
										<td class="reportBoxHeading">'.TABLE_HEADING_TEMPLATE_OPTIONS.'</td>
									</tr>';
			$row_count = 0;
			$content_append = $current_content = '';
			
			while ($template_row = tep_db_fetch_array($template_result_sql)) {
				if ($template_id == $template_row['data_pool_template_id']) {
					$row_style = 'rowSelected';
				} else {
					$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
					++$row_count;
				}
				
				$data_pool_options_html = '	<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
			          							<td colspan="7"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
			               					<tr>
			               						<td class="subRecordsBoxHeading" valign="top" width="8%">';
			    if (isset($_REQUEST['page'])) {
			    	$data_pool_options_html	.=		'[<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=new_option&template_id='.$template_row['data_pool_template_id']).'&page='.$_REQUEST["page"].'&cont='.$_REQUEST["cont"].'">Add New</a>]';
			    } else {
					$data_pool_options_html	.=		'[<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=new_option&template_id='.$template_row['data_pool_template_id']).'">Add New</a>]';
				}
				
				$data_pool_options_html	.=		'</td>
												<td class="subRecordsBoxHeading" nowrap>'.TABLE_HEADING_OPTIONS_TITLE.'</td>
						       					<td class="subRecordsBoxHeading" width="22%" nowrap>'.TABLE_HEADING_OPTIONS_INPUT_TYPE.'</td>
						       					<td class="subRecordsBoxHeading" width="10%" nowrap>'.TABLE_HEADING_OPTIONS_SORT_ORDER.'</td>
						   					</tr>
						   					<tr>
			          							<td colspan="7"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
			          						</tr>';
				
				$options_select_sql = "SELECT data_pool_options_id, data_pool_options_title, data_pool_options_input_type, data_pool_options_sort_order, data_pool_options_required FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id ='" . $template_row['data_pool_template_id'] . "' ORDER BY data_pool_options_sort_order";
				$options_result_sql = tep_db_query($options_select_sql);
				while ($options_row = tep_db_fetch_array($options_result_sql)) {
					$field_type_str = '';
					if ($options_row["data_pool_options_input_type"] == 999) {
						$field_type_str = 'System Field';
					} else {
						for ($type_cnt=0; $type_cnt < count($field_type_array); $type_cnt++) {
							if ($field_type_array[$type_cnt]['id'] == $options_row["data_pool_options_input_type"]) {
								$field_type_str = $field_type_array[$type_cnt]['text'];
								break;
							}
						}
					}
					
					if (tep_not_null($field_type_str) && $options_row["data_pool_options_required"] == 1) {
						$field_type_str .= '<span class="redIndicator">*</span>';
					}
					
					$page_str = '';
					
					if (isset($_REQUEST['page'])) {
						$page_str = 'page='.$_REQUEST['page']. '&cont='.$_REQUEST['cont'] . '&';
					}
					
					$data_pool_options_html .= '<tr>
													<td class="reportRecords" valign="top" nowrap>
														<a href="'.tep_href_link(FILENAME_DATA_POOL, $page_str.'action=edit_option&template_id='.$template_row['data_pool_template_id'].'&optionID='.$options_row["data_pool_options_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>';
					if ($options_row["data_pool_options_input_type"] != 999) {
						$data_pool_options_html .= '	<a href="javascript:void(confirm_delete(\''.strip_tags($options_row["data_pool_options_title"]).'\', \'Options\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_option&template_id='.$template_row['data_pool_template_id'].'&optionID='.$options_row["data_pool_options_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a>';
					}
					$data_pool_options_html .= '	</td>
													<td class="reportRecords" valign="top">'.$options_row["data_pool_options_title"].'</td>
													<td class="reportRecords" valign="top" nowrap>'.$field_type_str.'</td>
													<td class="reportRecords" valign="top">'.$options_row["data_pool_options_sort_order"].'</td>
												</tr>';
				}
				
				$data_pool_options_html .= '</table>';
				
				$count_products = 0;
				$sql_products_count = "	SELECT COUNT(a.categories_id) AS counter 
										FROM " . TABLE_POOL_TEMPLATE_T0_CATEGORIES . " AS a 
									   	WHERE a.data_pool_template_id = '" . $template_row['data_pool_template_id'] . "';";
				$products_count_result = tep_db_query($sql_products_count);
				
				if ($row_products_count = tep_db_fetch_array($products_count_result)) {
					$count_products = (int)$row_products_count['counter'];
				}
				
				$buttonHtml = '';
				if ($count_products>0) {
					$buttonHtml = tep_button(BUTTON_UPDATE, '', '', 'onClick="this.disabled=\'true\';location.href=\''.tep_href_link(FILENAME_DATA_POOL, 'page='.$_GET['page'].'&action=do_apply_template&template_id='.$template_row['data_pool_template_id']).'\'; " id="template_id_'.$template_row['data_pool_template_id'].'"', 'InputButton');
				}
				
				if ($template_id == $template_row['data_pool_template_id']) {
					// Currently selected template
					$current_content = '<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
											<td class="reportRecords" valign="top" align="center">
												<a href="'.tep_href_link(FILENAME_DATA_POOL, 'template_id='.$template_row['data_pool_template_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;
											 	<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=template_linking&template_id='.$template_row['data_pool_template_id']).'">'.tep_image(DIR_WS_ICONS."linking.gif", "View All Nodes", "", "", 'align="top"').'</a>&nbsp;
											 	<a href="javascript:void(confirm_delete(\''.$template_row['data_pool_template_name'].'\', \'template\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_template&template_id='.$template_row['data_pool_template_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Template", "", "", 'align="top"').'</a>' . $buttonHtml . '
											 </td>
											 <td class="reportRecords" valign="top">'.$template_row['data_pool_template_name'].'<br>'.TEXT_TOTAL_CATEGORIES.$count_products.'<br><br></td>
											 <td class="reportRecords" valign="top">'.$template_row['custom_products_type_name'].'</td>
											 <td class="reportRecords" valign="top">'.$template_row['data_pool_template_description'].'</td>
											 <td class="reportRecords" valign="top">'.$data_pool_options_html.'</td>
										</tr>';
				} else {
					$content_append.= '<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
										 	<td class="reportRecords" width="5%" valign="top" align="center">
												<a href="'.tep_href_link(FILENAME_DATA_POOL, 'template_id='.$template_row['data_pool_template_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;
											 	<a href="'.tep_href_link(FILENAME_DATA_POOL, 'action=template_linking&template_id='.$template_row['data_pool_template_id']).'">'.tep_image(DIR_WS_ICONS."linking.gif", "View All Nodes", "", "", 'align="top"').'</a>&nbsp;
											 	<a href="javascript:void(confirm_delete(\''.$template_row['data_pool_template_name'].'\', \'template\', \''.tep_href_link(FILENAME_DATA_POOL, 'action=do_delete_template&template_id='.$template_row['data_pool_template_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Template", "", "", 'align="top"').'</a>' . $buttonHtml . '
											 </td>
											 <td class="reportRecords" valign="top">'.$template_row['data_pool_template_name'].'<br>'.TEXT_TOTAL_CATEGORIES.$count_products.'<br><br></td>
											 <td class="reportRecords" valign="top">'.$template_row['custom_products_type_name'].'</td>
											 <td class="reportRecords" valign="top">'.$template_row['data_pool_template_description'].'</td>
											 <td class="reportRecords" valign="top">'.$data_pool_options_html.'</td>
										</tr>';
				}
			}
			
			$content.= $current_content.$content_append.'</table></td></tr>';
		}
	}
?>
			<td valign="top">
				<form name="frmDataPool" action="<?=tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array("action"))."action=$action")?>" method="post">
					<table width="100%"  border="0" cellspacing="0" cellpadding="3">
						<tr>
							<td class="pageHeading">
								<?=HEADING_TITLE?><br><span class="main"><?=$subTitle?></span>
							</td>
						</tr>
					</table>
					<table width="100%"  border="0" cellspacing="0" cellpadding="3">
						<tr><td valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td></tr>
						<?=$content?>
					</table>
				</form>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_TEMPLATES, tep_db_num_rows($template_select_sql) > 0 ? "1" : "0", tep_db_num_rows($template_select_sql), tep_db_num_rows($template_select_sql)) : $template_split_object->display_count($template_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_TEMPLATES)?></td>
						<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $template_split_object->display_links($template_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<?
require(DIR_WS_INCLUDES . 'footer.php'); 
?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>