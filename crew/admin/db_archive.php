<?
/*
  $Id: db_archive.php,v 1.27 2015/03/10 03:46:07 chingyen Exp $

  Developer: <PERSON>
  Copyright (c) 2007 SKC Ventrue

  Released under the GNU General Public License
 */

require('includes/application_top.php');

$raw_db = DB_DATABASE;
$archive_db = 'archive_' . DB_DATABASE;

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch ($action) {
    case "do_archive":
        if (isset($_POST['temp_process_btn'])) {
            $truncate_temp_process_sql = "TRUNCATE TABLE " . TABLE_TEMP_PROCESS;
            tep_db_query($truncate_temp_process_sql);
        } else if (isset($_POST['process_optimise_pvm_tables_btn'])) {

            tep_set_time_limit(0);

            tep_db_query("TRUNCATE " . TABLE_PAGE_VIEW_IP_LIST . "_tmp");
            tep_db_query("TRUNCATE " . TABLE_IP_TAGS_STATS . "_tmp");
            tep_db_query("TRUNCATE " . TABLE_IP_LIST_HISTORY . "_tmp");

            tep_db_query("INSERT INTO " . TABLE_PAGE_VIEW_IP_LIST . "_tmp SELECT * FROM " . TABLE_PAGE_VIEW_IP_LIST);
            tep_db_query("INSERT INTO " . TABLE_IP_TAGS_STATS . "_tmp SELECT * FROM " . TABLE_IP_TAGS_STATS);

            $page_view_ip_list_select_sql = "	SELECT page_view_ip_list_id
												FROM " . TABLE_PAGE_VIEW_IP_LIST . "_tmp";
            $page_view_ip_list_result_sql = tep_db_query($page_view_ip_list_select_sql);
            while ($page_view_ip_list_row = tep_db_fetch_array($page_view_ip_list_result_sql)) {
                tep_db_query("	INSERT INTO " . TABLE_IP_LIST_HISTORY . "_tmp
								SELECT *
								FROM " . TABLE_IP_LIST_HISTORY . "
								WHERE page_view_ip_list_id = '" . $page_view_ip_list_row['page_view_ip_list_id'] . "'
								ORDER BY ip_list_history_id DESC
								LIMIT 500");
            }

            tep_db_query("TRUNCATE " . TABLE_PAGE_VIEW_IP_LIST);
            tep_db_query("TRUNCATE " . TABLE_IP_LIST_HISTORY);
            tep_db_query("TRUNCATE " . TABLE_IP_TAGS_STATS);

            $select_list_array = array();
            $select_list_sql = "	SELECT *
									FROM " . TABLE_PAGE_VIEW_IP_LIST . "_tmp";
            $select_list_result_sql = tep_db_query($select_list_sql);
            while ($select_list_row = tep_db_fetch_array($select_list_result_sql)) {

                $select_list_array = $select_list_row;
                $select_list_array['page_view_ip_list_id'] = '';
                tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $select_list_array);
                $last_insert_id = tep_db_insert_id();

                $ip_tags_stats_array = array();
                $ip_tags_stats_sql = "	SELECT *
										FROM " . TABLE_IP_TAGS_STATS . "_tmp
										WHERE page_view_ip_list_id = '" . $select_list_row['page_view_ip_list_id'] . "'";
                $ip_tags_stats_result_sql = tep_db_query($ip_tags_stats_sql);
                while ($ip_tags_stats_row = tep_db_fetch_array($ip_tags_stats_result_sql)) {
                    $ip_tags_stats_array = $ip_tags_stats_row;
                    $ip_tags_stats_array['page_view_ip_list_id'] = $last_insert_id;
                    tep_db_perform(TABLE_IP_TAGS_STATS, $ip_tags_stats_array);
                }

                $ip_history_stats_array = array();
                $ip_history_select_sql = "	SELECT *
											FROM " . TABLE_IP_LIST_HISTORY . "_tmp
											WHERE page_view_ip_list_id = '" . $select_list_row['page_view_ip_list_id'] . "'";
                $ip_history_result_sql = tep_db_query($ip_history_select_sql);
                while ($ip_history_row = tep_db_fetch_array($ip_history_result_sql)) {
                    $ip_history_stats_array = $ip_history_row;
                    $ip_history_stats_array['page_view_ip_list_id'] = $last_insert_id;
                    $ip_history_stats_array['ip_list_history_id'] = '';
                    tep_db_perform(TABLE_IP_LIST_HISTORY, $ip_history_stats_array);
                }
            }

            tep_db_query("TRUNCATE " . TABLE_PAGE_VIEW_IP_LIST . "_tmp");
            tep_db_query("TRUNCATE " . TABLE_IP_TAGS_STATS . "_tmp");
            tep_db_query("TRUNCATE " . TABLE_IP_LIST_HISTORY . "_tmp");
        } else if (isset($_POST['customer_cart_btn'])) {
            tep_db_query("TRUNCATE " . TABLE_CUSTOMERS_BASKET);
            tep_db_query("TRUNCATE " . TABLE_CUSTOMERS_BASKET_BUNDLE);
            tep_db_query("TRUNCATE " . TABLE_CUSTOMERS_BASKET_CUSTOM);
            tep_db_query("TRUNCATE " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES);
            tep_db_query("TRUNCATE " . TABLE_SESSIONS);
            tep_db_query("TRUNCATE " . TABLE_CUSTOMERS_SC_CART);
            tep_db_query("TRUNCATE " . TABLE_MOBILE_CUSTOMERS_BASKET);
        } else if (isset($_POST['sc_history_archive_btn'])) {

            tep_set_time_limit(0);
            $archive_link = tep_db_connect(DB_SERVER, DB_SERVER_USERNAME, DB_SERVER_PASSWORD, $raw_db, 'archive_link');
            $year_checked_array = array();

            //$limit_6_months = date('Y-m-d H:i:s', mktime(0,0,0,date('m')-6,date('d'),date('Y')));
            $limit_6_months = '2012-03-01 00:00:00';

            $sc_history_select_sql = "	SELECT *
											FROM " . $raw_db . ".store_credit_daily_history
											WHERE store_credit_daily_history_date < '" . $limit_6_months . "'
											ORDER BY store_credit_daily_history_date ASC, store_credit_daily_history_currency, store_credit_daily_history_credit_type, user_id
											LIMIT 10000";
            $sc_history_result_sql = tep_db_query($sc_history_select_sql);

            while ($sc_history_row = tep_db_fetch_array($sc_history_result_sql)) {
                $archive_year = date('Y', strtotime($sc_history_row['store_credit_daily_history_date']));
                $archive_table_name = 'store_credit_daily_history_' . $archive_year;

                $table_existed = false;
                if (in_array($archive_year, $year_checked_array)) {
                    $table_existed = true;
                } else {
                    if (tep_db_num_rows(tep_db_query("SHOW TABLES FROM " . $raw_db . " LIKE '" . $archive_table_name . "'"))) {
                        $table_existed = true;
                        $year_checked_array[] = $archive_year;
                    }
                }

                if (!$table_existed) {
                    $create_table_sql = "CREATE TABLE `" . $archive_table_name . "` (
											`store_credit_daily_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
											`store_credit_daily_history_currency` varchar(4) NOT NULL default '',
											`store_credit_daily_history_credit_type` varchar(4) NOT NULL default '',
											`store_credit_daily_history_amount` decimal(15,4) NOT NULL default '0.0000',
											`store_credit_daily_history_reserved_amount` decimal(15,4) NOT NULL default '0.0000',
											`user_id` varchar(32) NOT NULL default '',
											`user_role` varchar(16) NOT NULL default '',
											PRIMARY KEY  (`store_credit_daily_history_credit_type`,`store_credit_daily_history_date`,`store_credit_daily_history_currency`,`user_id`),
											KEY `closing_balance` (`store_credit_daily_history_credit_type`,`user_id`,`store_credit_daily_history_date`),
											KEY `index_daily_date` (`store_credit_daily_history_date`)
											) ENGINE=MyISAM DEFAULT CHARSET=latin1";
                    tep_db_query($create_table_sql, 'archive_link');
                    $year_checked_array[] = $archive_year;
                }

                $sc_history_copy_sql = "INSERT INTO `" . $archive_table_name . "`
													(`store_credit_daily_history_date`,`store_credit_daily_history_currency`,`store_credit_daily_history_credit_type`,
													`store_credit_daily_history_amount`,`store_credit_daily_history_reserved_amount`,`user_id`,`user_role`)
											VALUES(	'" . $sc_history_row['store_credit_daily_history_date'] . "','" . $sc_history_row['store_credit_daily_history_currency'] . "',
													'" . $sc_history_row['store_credit_daily_history_credit_type'] . "','" . $sc_history_row['store_credit_daily_history_amount'] . "',
													'" . $sc_history_row['store_credit_daily_history_reserved_amount'] . "','" . $sc_history_row['user_id'] . "','" . $sc_history_row['user_role'] . "')";
                tep_db_query($sc_history_copy_sql, 'archive_link');

                /* $sc_history_delete_sql = "DELETE FROM " . $raw_db.".store_credit_daily_history
                  WHERE " . $raw_db.".store_credit_daily_history.store_credit_daily_history_date='".$sc_history_row['store_credit_daily_history_date']."'
                  AND " . $raw_db.".store_credit_daily_history.store_credit_daily_history_currency='".$sc_history_row['store_credit_daily_history_currency']."'
                  AND " . $raw_db.".store_credit_daily_history.store_credit_daily_history_credit_type='".$sc_history_row['store_credit_daily_history_credit_type']."'
                  AND " . $raw_db.".store_credit_daily_history.user_id='".$sc_history_row['user_id']."'";
                  tep_db_query($sc_history_delete_sql); */
            }

            $history_delete_sql = "	DELETE FROM " . $raw_db . ".store_credit_daily_history
									WHERE store_credit_daily_history_date < '" . $limit_6_months . "'
									ORDER BY store_credit_daily_history_date ASC, store_credit_daily_history_currency, store_credit_daily_history_credit_type, user_id
									LIMIT 10000";
            tep_db_query($history_delete_sql);

            tep_db_close('archive_link');
        } else if (isset($_POST['canceled_orders_archive_btn'])) {

            tep_set_time_limit(0);
            $archive_link = tep_db_connect(DB_SERVER, DB_SERVER_USERNAME, DB_SERVER_PASSWORD, $archive_db, 'archive_link');

            $limit_1_year = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d'), date('Y') - 1));

            $orders_list = $orders_products_list = $topup_list = array();
            $tables_list = $missing_table = array();
            $field_num_diff_list = array();
            $table_field_diff_list = array();
            $error = false;

            // master table list
            $master_table_arrays = array('orders', 'game_char_log', 'maxmind_history', 'orders_log_table', 'payment_extra_info', 'orders_total', 'orders_extra_info', 'orders_status_history', 'orders_status_stat', 'coupon_redeem_track', 'coupon_gv_queue', 'orders_products', 'orders_products_attributes', 'orders_products_eta', 'orders_products_extra_info', 'orders_products_item', 'orders_custom_products', 'customers_top_up_info', 'game_char', 'orders_compensate_products', 'orders_top_up', 'orders_top_up_remark');

            // check table exist in archive database
            foreach ($master_table_arrays as $table_name) {
                if (tep_db_num_rows(tep_db_query("SHOW TABLES FROM " . $archive_db . " LIKE '" . $table_name . "'"))) {
                    $tables_list[] = $table_name;
                } else {
                    $missing_table[] = $table_name;
                }
            }

            if (count($missing_table) > 0) {
                $missing_str = implode(', ', $missing_table);
                $messageStack->add_session("Missing table found in archive database: " . $missing_str, 'error');
                $error = true;
            }

            // check fields number differences between master and archine database
            foreach ($tables_list as $table_name) {
                $check_sql = 'DESC ' . $table_name;
                $master_result = tep_db_query($check_sql);
                $archive_result = tep_db_query($check_sql, 'archive_link');
                if (tep_db_num_rows($master_result) != tep_db_num_rows($archive_result)) {
                    $field_num_diff_list[] = $table_name;
                }

                $master_fields = $archive_fields = array();
                while ($master_row = tep_db_fetch_array($master_result)) {
                    $master_fields[] = $master_row['Field'];
                }
                while ($archive_row = tep_db_fetch_array($master_result)) {
                    $archive_fields[] = $archive_row['Field'];
                }
                $field_diff = array_diff($archive_fields, $master_fields);
                if (count($field_diff) > 0) {
                    $table_field_diff_list[] = $table_name;
                }
            }

            if (count($field_num_diff_list) > 0) {
                $field_num_diff_str = implode(', ', $field_num_diff_list);
                $messageStack->add_session("Tables with different fields number in archive database: " . $field_num_diff_str, 'error');
                $error = true;
            }

            if (count($table_field_diff_list) > 0) {
                $table_field_diff_str = implode(', ', $table_field_diff_list);
                $messageStack->add_session("Table fields with differences in archive database: " . $table_field_diff_str, 'error');
                $error = true;
            }

            if (!$error) {
                $orders_select_sql = "	SELECT orders_id
										FROM " . $raw_db . ".orders
										WHERE date_purchased < '" . $limit_1_year . "'
											AND orders_status = '5'
										ORDER BY orders_id ASC
										LIMIT 500";
                $orders_result_sql = tep_db_query($orders_select_sql);
                while ($orders_row = tep_db_fetch_array($orders_result_sql)) {

                    $o_select_sql = "SELECT * FROM " . $raw_db . ".orders WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $o_result_sql = tep_db_query($o_select_sql);
                    if ($o_row = tep_db_fetch_array($o_result_sql)) {
                        $orders_copy_sql = "INSERT INTO `orders`
													(`orders_id`,`customers_id`,`customers_name`,`customers_company`,`customers_street_address`,
													`customers_suburb`,`customers_city`,`customers_postcode`,`customers_state`,
													`customers_country`,`customers_telephone_country`,`customers_country_international_dialing_code`,`customers_telephone`,
													`customers_email_address`,`customers_address_format_id`,`customers_groups_id`,`delivery_name`,
													`delivery_company`,`delivery_street_address`,`delivery_suburb`,`delivery_city`,
													`delivery_postcode`,`delivery_state`,`delivery_country`,`delivery_address_format_id`,
													`billing_name`,`billing_company`,`billing_street_address`,`billing_suburb`,
													`billing_city`,`billing_postcode`,`billing_state`,`billing_country`,
													`billing_address_format_id`,`payment_method`,`payment_methods_parent_id`,`payment_methods_id`,
													`cc_type`,`cc_owner`,`cc_number`,`cc_expires`,
													`last_modified`,`date_purchased`,`orders_status`,`orders_cb_status`,
													`orders_date_finished`,`currency`,`remote_addr`,`currency_value`,
													`paypal_ipn_id`,`pm_2CO_cc_owner_firstname`,`pm_2CO_cc_owner_lastname`,`orders_locked_by`,
													`orders_locked_from_ip`,`orders_locked_datetime`,`orders_follow_up_datetime`,`orders_tag_ids`,
													`orders_read_mode`,`orders_aft_executed`,`orders_rebated`)
											VALUES(	'" . tep_db_input($o_row['orders_id']) . "','" . tep_db_input($o_row['customers_id']) . "','" . tep_db_input($o_row['customers_name']) . "','" . tep_db_input($o_row['customers_company']) . "','" . tep_db_input($o_row['customers_street_address']) . "',
													'" . tep_db_input($o_row['customers_suburb']) . "','" . tep_db_input($o_row['customers_city']) . "','" . tep_db_input($o_row['customers_postcode']) . "','" . tep_db_input($o_row['customers_state']) . "',
													'" . tep_db_input($o_row['customers_country']) . "','" . tep_db_input($o_row['customers_telephone_country']) . "','" . tep_db_input($o_row['customers_country_international_dialing_code']) . "','" . tep_db_input($o_row['customers_telephone']) . "',
													'" . tep_db_input($o_row['customers_email_address']) . "','" . tep_db_input($o_row['customers_address_format_id']) . "','" . tep_db_input($o_row['customers_groups_id']) . "','" . tep_db_input($o_row['delivery_name']) . "',
													'" . tep_db_input($o_row['delivery_company']) . "','" . tep_db_input($o_row['delivery_street_address']) . "','" . tep_db_input($o_row['delivery_suburb']) . "','" . tep_db_input($o_row['delivery_city']) . "',
													'" . tep_db_input($o_row['delivery_postcode']) . "','" . tep_db_input($o_row['delivery_state']) . "','" . tep_db_input($o_row['delivery_country']) . "','" . tep_db_input($o_row['delivery_address_format_id']) . "',
													'" . tep_db_input($o_row['billing_name']) . "','" . tep_db_input($o_row['billing_company']) . "','" . tep_db_input($o_row['billing_street_address']) . "','" . tep_db_input($o_row['billing_suburb']) . "',
													'" . tep_db_input($o_row['billing_city']) . "','" . tep_db_input($o_row['billing_postcode']) . "','" . tep_db_input($o_row['billing_state']) . "','" . tep_db_input($o_row['billing_country']) . "',
													'" . tep_db_input($o_row['billing_address_format_id']) . "','" . tep_db_input($o_row['payment_method']) . "','" . tep_db_input($o_row['payment_methods_parent_id']) . "','" . tep_db_input($o_row['payment_methods_id']) . "',
													'" . tep_db_input($o_row['cc_type']) . "','" . tep_db_input($o_row['cc_owner']) . "','" . tep_db_input($o_row['cc_number']) . "','" . tep_db_input($o_row['cc_expires']) . "',
													'" . tep_db_input($o_row['last_modified']) . "','" . tep_db_input($o_row['date_purchased']) . "','" . tep_db_input($o_row['orders_status']) . "','" . tep_db_input($o_row['orders_cb_status']) . "',
													'" . tep_db_input($o_row['orders_date_finished']) . "','" . tep_db_input($o_row['currency']) . "','" . tep_db_input($o_row['remote_addr']) . "','" . tep_db_input($o_row['currency_value']) . "',
													'" . tep_db_input($o_row['paypal_ipn_id']) . "','" . tep_db_input($o_row['pm_2CO_cc_owner_firstname']) . "','" . tep_db_input($o_row['pm_2CO_cc_owner_lastname']) . "','" . tep_db_input($o_row['orders_locked_by']) . "',
													'" . tep_db_input($o_row['orders_locked_from_ip']) . "','" . tep_db_input($o_row['orders_locked_datetime']) . "','" . tep_db_input($o_row['orders_follow_up_datetime']) . "','" . tep_db_input($o_row['orders_tag_ids']) . "',
													'" . tep_db_input($o_row['orders_read_mode']) . "','" . tep_db_input($o_row['orders_aft_executed']) . "','" . tep_db_input($o_row['orders_rebated']) . "')";
                        tep_db_query($orders_copy_sql, 'archive_link');
                    }

                    $ot_select_sql = "SELECT * FROM " . $raw_db . ".orders_total WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $ot_result_sql = tep_db_query($ot_select_sql);
                    while ($ot_row = tep_db_fetch_array($ot_result_sql)) {
                        $ot_copy_sql = "INSERT INTO `orders_total`
													(`orders_total_id`,`orders_id`,`title`,`text`,`value`,`class`,`sort_order`)
											VALUES(	'" . tep_db_input($ot_row['orders_total_id']) . "','" . tep_db_input($ot_row['orders_id']) . "','" . tep_db_input($ot_row['title']) . "',
													'" . tep_db_input($ot_row['text']) . "','" . tep_db_input($ot_row['value']) . "','" . tep_db_input($ot_row['class']) . "','" . tep_db_input($ot_row['sort_order']) . "')";
                        tep_db_query($ot_copy_sql, 'archive_link');
                    }

                    $oei_select_sql = "SELECT * FROM " . $raw_db . ".orders_extra_info WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $oei_result_sql = tep_db_query($oei_select_sql);
                    while ($oei_row = tep_db_fetch_array($oei_result_sql)) {
                        $oei_copy_sql = "INSERT INTO `orders_extra_info`
													(`orders_id`,`orders_extra_info_key`,`orders_extra_info_value`)
											VALUES(	'" . tep_db_input($oei_row['orders_id']) . "','" . tep_db_input($oei_row['orders_extra_info_key']) . "','" . tep_db_input($oei_row['orders_extra_info_value']) . "')";
                        tep_db_query($oei_copy_sql, 'archive_link');
                    }

                    $osh_select_sql = "SELECT * FROM " . $raw_db . ".orders_status_history WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $osh_result_sql = tep_db_query($osh_select_sql);
                    while ($osh_row = tep_db_fetch_array($osh_result_sql)) {
                        $osh_copy_sql = "INSERT INTO `orders_status_history`
													(`orders_status_history_id`,`orders_id`,`orders_status_id`,`date_added`,`customer_notified`,`comments`,`comments_type`,`set_as_order_remarks`,`changed_by`)
											VALUES(	'" . tep_db_input($osh_row['orders_status_history_id']) . "','" . tep_db_input($osh_row['orders_id']) . "','" . tep_db_input($osh_row['orders_status_id']) . "',
													'" . tep_db_input($osh_row['date_added']) . "','" . tep_db_input($osh_row['customer_notified']) . "','" . tep_db_input($osh_row['comments']) . "',
													'" . tep_db_input($osh_row['comments_type']) . "','" . tep_db_input($osh_row['set_as_order_remarks']) . "','" . tep_db_input($osh_row['changed_by']) . "')";
                        tep_db_query($osh_copy_sql, 'archive_link');
                    }

                    $oss_select_sql = "SELECT * FROM " . $raw_db . ".orders_status_stat WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $oss_result_sql = tep_db_query($oss_select_sql);
                    while ($oss_row = tep_db_fetch_array($oss_result_sql)) {
                        $oss_copy_sql = "INSERT INTO `orders_status_stat`
													(`orders_id`,`orders_status_id`,`occurrence`,`first_date`,`latest_date`,`changed_by`)
											VALUES(	'" . tep_db_input($oss_row['orders_id']) . "','" . tep_db_input($oss_row['orders_status_id']) . "','" . tep_db_input($oss_row['occurrence']) . "',
													'" . tep_db_input($oss_row['first_date']) . "','" . tep_db_input($oss_row['latest_date']) . "','" . tep_db_input($oss_row['changed_by']) . "')";
                        tep_db_query($oss_copy_sql, 'archive_link');
                    }

                    $crt_select_sql = "SELECT * FROM " . $raw_db . ".coupon_redeem_track WHERE order_id='" . $orders_row['orders_id'] . "'";
                    $crt_result_sql = tep_db_query($crt_select_sql);
                    while ($crt_row = tep_db_fetch_array($crt_result_sql)) {
                        $crt_copy_sql = "INSERT INTO `coupon_redeem_track`
													(`unique_id`,`coupon_id`,`customer_id`,`redeem_date`,`redeem_ip`,`order_id`)
											VALUES(	'" . tep_db_input($crt_row['unique_id']) . "','" . tep_db_input($crt_row['coupon_id']) . "','" . tep_db_input($crt_row['customer_id']) . "',
													'" . tep_db_input($crt_row['redeem_date']) . "','" . tep_db_input($crt_row['redeem_ip']) . "','" . tep_db_input($crt_row['order_id']) . "')";
                        tep_db_query($crt_copy_sql, 'archive_link');
                    }

                    $cgq_select_sql = "SELECT * FROM " . $raw_db . ".coupon_gv_queue WHERE order_id='" . $orders_row['orders_id'] . "'";
                    $cgq_result_sql = tep_db_query($cgq_select_sql);
                    while ($cgq_row = tep_db_fetch_array($cgq_result_sql)) {
                        $cgq_copy_sql = "INSERT INTO `coupon_gv_queue`
													(`unique_id`,`customer_id`,`order_id`,`amount`,`date_created`,`ipaddr`,`release_flag`)
											VALUES(	'" . tep_db_input($cgq_row['unique_id']) . "','" . tep_db_input($cgq_row['customer_id']) . "','" . tep_db_input($cgq_row['order_id']) . "','" . tep_db_input($cgq_row['amount']) . "',
													'" . tep_db_input($cgq_row['date_created']) . "','" . tep_db_input($cgq_row['ipaddr']) . "','" . tep_db_input($cgq_row['release_flag']) . "')";
                        tep_db_query($cgq_copy_sql, 'archive_link');
                    }

                    $gcl_select_sql = "SELECT * FROM " . $raw_db . ".game_char_log WHERE game_char_log_orders_id='" . $orders_row['orders_id'] . "'";
                    $gcl_result_sql = tep_db_query($gcl_select_sql);
                    while ($gcl_row = tep_db_fetch_array($gcl_result_sql)) {
                        $gcl_copy_sql = "INSERT INTO `game_char_log`
													(`game_char_log_id`,`game_char_log_orders_id`,`game_char_log_time`,`game_char_log_account_name`,
													`game_char_log_server`,`game_char_log_realm`,`game_char_log_race`,`game_char_log_sender`,
													`game_char_log_receiver`,`game_char_log_subject`,`game_char_log_messages`,`game_char_log_balance_before`,
													`game_char_log_balance_after`,`game_char_log_system_messages`,`game_char_log_send`,`game_char_log_receive`,
													`game_char_log_type`,`game_char_log_login_as`,`game_char_log_computer_name`,`game_char_log_login_user`,`game_char_log_user_role`)
											VALUES(	'" . tep_db_input($gcl_row['game_char_log_id']) . "','" . tep_db_input($gcl_row['game_char_log_orders_id']) . "','" . tep_db_input($gcl_row['game_char_log_time']) . "','" . tep_db_input($gcl_row['game_char_log_account_name']) . "',
													'" . tep_db_input($gcl_row['game_char_log_server']) . "','" . tep_db_input($gcl_row['game_char_log_realm']) . "','" . tep_db_input($gcl_row['game_char_log_race']) . "','" . tep_db_input($gcl_row['game_char_log_sender']) . "',
													'" . tep_db_input($gcl_row['game_char_log_receiver']) . "','" . tep_db_input($gcl_row['game_char_log_subject']) . "','" . tep_db_input($gcl_row['game_char_log_messages']) . "','" . tep_db_input($gcl_row['game_char_log_balance_before']) . "',
													'" . tep_db_input($gcl_row['game_char_log_balance_after']) . "','" . tep_db_input($gcl_row['game_char_log_system_messages']) . "','" . tep_db_input($gcl_row['game_char_log_send']) . "','" . tep_db_input($gcl_row['game_char_log_receive']) . "',
													'" . tep_db_input($gcl_row['game_char_log_type']) . "','" . tep_db_input($gcl_row['game_char_log_login_as']) . "','" . tep_db_input($gcl_row['game_char_log_computer_name']) . "',
													'" . tep_db_input($gcl_row['game_char_log_login_user']) . "','" . tep_db_input($gcl_row['game_char_log_user_role']) . "')";
                        tep_db_query($gcl_copy_sql, 'archive_link');
                    }

                    $mh_select_sql = "SELECT * FROM " . $raw_db . ".maxmind_history WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $mh_result_sql = tep_db_query($mh_select_sql);
                    while ($mh_row = tep_db_fetch_array($mh_result_sql)) {
                        $mh_copy_sql = "INSERT INTO `maxmind_history`
													(`orders_id`,`maxmind_history_date`,`distance`,`country_match`,
													`ip_country_code`,`free_mail`,`anonymous_proxy`,`score`,
													`bin_match`,`bin_country_code`,`error`,`proxy_score`,
													`spam_score`,`ip_region`,`ip_city`,`ip_latitude`,
													`ip_longitude`,`bin_name`,`ip_isp`,`ip_organization`,
													`bin_name_match`,`bin_phone_match`,`bin_phone`,`customer_phone_in_billing_location`,
													`high_risk_country`,`queries_remaining`,`city_postal_match`,`shipping_city_postal_match`,
													`maxmind_history_source`,`maxmind_id`)
											VALUES(	'" . tep_db_input($mh_row['orders_id']) . "','" . tep_db_input($mh_row['maxmind_history_date']) . "','" . tep_db_input($mh_row['distance']) . "','" . tep_db_input($mh_row['country_match']) . "',
													'" . tep_db_input($mh_row['ip_country_code']) . "','" . tep_db_input($mh_row['free_mail']) . "','" . tep_db_input($mh_row['anonymous_proxy']) . "','" . tep_db_input($mh_row['score']) . "',
													'" . tep_db_input($mh_row['bin_match']) . "','" . tep_db_input($mh_row['bin_country_code']) . "','" . tep_db_input($mh_row['error']) . "','" . tep_db_input($mh_row['proxy_score']) . "',
													'" . tep_db_input($mh_row['spam_score']) . "','" . tep_db_input($mh_row['ip_region']) . "','" . tep_db_input($mh_row['ip_city']) . "','" . tep_db_input($mh_row['ip_latitude']) . "',
													'" . tep_db_input($mh_row['ip_longitude']) . "','" . tep_db_input($mh_row['bin_name']) . "','" . tep_db_input($mh_row['ip_isp']) . "','" . tep_db_input($mh_row['ip_organization']) . "',
													'" . tep_db_input($mh_row['bin_name_match']) . "','" . tep_db_input($mh_row['bin_phone_match']) . "','" . tep_db_input($mh_row['bin_phone']) . "','" . tep_db_input($mh_row['customer_phone_in_billing_location']) . "',
													'" . tep_db_input($mh_row['high_risk_country']) . "','" . tep_db_input($mh_row['queries_remaining']) . "','" . tep_db_input($mh_row['city_postal_match']) . "','" . tep_db_input($mh_row['shipping_city_postal_match']) . "',
													'" . tep_db_input($mh_row['maxmind_history_source']) . "','" . tep_db_input($mh_row['maxmind_id']) . "')";
                        tep_db_query($mh_copy_sql, 'archive_link');
                    }

                    $olt_select_sql = "SELECT * FROM " . $raw_db . ".orders_log_table WHERE orders_log_orders_id='" . $orders_row['orders_id'] . "'";
                    $olt_result_sql = tep_db_query($olt_select_sql);
                    while ($olt_row = tep_db_fetch_array($olt_result_sql)) {
                        $olt_copy_sql = "INSERT INTO `orders_log_table`
													(`orders_log_id`,`orders_log_admin_id`,`orders_log_ip`,`orders_log_time`,`orders_log_orders_id`,`orders_log_system_messages`,`orders_log_filename`)
											VALUES(	'" . tep_db_input($olt_row['orders_log_id']) . "','" . tep_db_input($olt_row['orders_log_admin_id']) . "','" . tep_db_input($olt_row['orders_log_ip']) . "','" . tep_db_input($olt_row['orders_log_time']) . "',
													'" . tep_db_input($olt_row['orders_log_orders_id']) . "','" . tep_db_input($olt_row['orders_log_system_messages']) . "','" . tep_db_input($olt_row['orders_log_filename']) . "')";
                        tep_db_query($olt_copy_sql, 'archive_link');
                    }

                    $pei_select_sql = "SELECT * FROM " . $raw_db . ".payment_extra_info WHERE orders_id='" . $orders_row['orders_id'] . "'";
                    $pei_result_sql = tep_db_query($pei_select_sql);
                    while ($pei_row = tep_db_fetch_array($pei_result_sql)) {
                        $pei_copy_sql = "INSERT INTO `payment_extra_info`
													(`orders_id`,`transaction_id`,`transaction_status`,`transaction_amount`,
													`transaction_currency`,`transaction_text_amount`,`credit_card_type`,`credit_card_owner`,
													`cardholder_aut_result`,`email_address`,`billing_address`,`country`,
													`country_code`,`ip_address`,`tel`,`fax`,
													`check_result`,`alert_message`,`authorisation_mode`,`authorisation_result`)
											VALUES(	'" . tep_db_input($pei_row['orders_id']) . "','" . tep_db_input($pei_row['transaction_id']) . "','" . tep_db_input($pei_row['transaction_status']) . "','" . tep_db_input($pei_row['transaction_amount']) . "',
													'" . tep_db_input($pei_row['transaction_currency']) . "','" . tep_db_input($pei_row['transaction_text_amount']) . "','" . tep_db_input($pei_row['credit_card_type']) . "','" . tep_db_input($pei_row['credit_card_owner']) . "',
													'" . tep_db_input($pei_row['cardholder_aut_result']) . "','" . tep_db_input($pei_row['email_address']) . "','" . tep_db_input($pei_row['billing_address']) . "','" . tep_db_input($pei_row['country']) . "',
													'" . tep_db_input($pei_row['country_code']) . "','" . tep_db_input($pei_row['ip_address']) . "','" . tep_db_input($pei_row['tel']) . "','" . tep_db_input($pei_row['fax']) . "',
													'" . tep_db_input($pei_row['check_result']) . "','" . tep_db_input($pei_row['alert_message']) . "','" . tep_db_input($pei_row['authorisation_mode']) . "','" . tep_db_input($pei_row['authorisation_result']) . "')";
                        tep_db_query($pei_copy_sql, 'archive_link');
                    }

                    $op_select_sql = "	SELECT *
										FROM " . $raw_db . ".orders_products
										WHERE orders_id = '" . $orders_row['orders_id'] . "'
										ORDER BY orders_products_id ASC";
                    $op_result_sql = tep_db_query($op_select_sql);
                    while ($op_row = tep_db_fetch_array($op_result_sql)) {

                        $op_copy_sql = "INSERT INTO `orders_products`
													(`orders_products_id`,`orders_id`,`products_id`,`products_model`,`products_name`,
													`orders_products_store_price`,`products_price`,`final_price`,`op_rebate`,
													`op_rebate_delivered`,`products_tax`,`products_quantity`,`products_delivered_quantity`,
													`products_good_delivered_quantity`,`products_good_delivered_price`,`products_canceled_quantity`,`products_canceled_price`,
													`products_reversed_quantity`,`products_reversed_price`,`products_bundle_id`,`parent_orders_products_id`,
													`products_pre_order`,`custom_products_type_id`,`orders_products_is_compensate`,`orders_products_purchase_eta`,
													`products_categories_id`)
											VALUES(	'" . tep_db_input($op_row['orders_products_id']) . "','" . tep_db_input($op_row['orders_id']) . "','" . tep_db_input($op_row['products_id']) . "','" . tep_db_input($op_row['products_model']) . "','" . tep_db_input($op_row['products_name']) . "',
													'" . tep_db_input($op_row['orders_products_store_price']) . "','" . tep_db_input($op_row['products_price']) . "','" . tep_db_input($op_row['final_price']) . "','" . tep_db_input($op_row['op_rebate']) . "',
													'" . tep_db_input($op_row['op_rebate_delivered']) . "','" . tep_db_input($op_row['products_tax']) . "','" . tep_db_input($op_row['products_quantity']) . "','" . tep_db_input($op_row['products_delivered_quantity']) . "',
													'" . tep_db_input($op_row['products_good_delivered_quantity']) . "','" . tep_db_input($op_row['products_good_delivered_price']) . "','" . tep_db_input($op_row['products_canceled_quantity']) . "','" . tep_db_input($op_row['products_canceled_price']) . "',
													'" . tep_db_input($op_row['products_reversed_quantity']) . "','" . tep_db_input($op_row['products_reversed_price']) . "','" . tep_db_input($op_row['products_bundle_id']) . "','" . tep_db_input($op_row['parent_orders_products_id']) . "',
													'" . tep_db_input($op_row['products_pre_order']) . "','" . tep_db_input($op_row['custom_products_type_id']) . "','" . tep_db_input($op_row['orders_products_is_compensate']) . "','" . tep_db_input($op_row['orders_products_purchase_eta']) . "',
													'" . tep_db_input($op_row['products_categories_id']) . "')";
                        tep_db_query($op_copy_sql, 'archive_link');

                        $opa_select_sql = "SELECT * FROM " . $raw_db . ".orders_products_attributes WHERE orders_id='" . $orders_row['orders_id'] . "' AND orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $opa_result_sql = tep_db_query($opa_select_sql);
                        while ($opa_row = tep_db_fetch_array($opa_result_sql)) {
                            $opa_copy_sql = "INSERT INTO `orders_products_attributes`
														(`orders_products_attributes_id`,`orders_id`,`orders_products_id`,`products_options`,`products_options_values`,
														`options_values_price`,`price_prefix`,`products_options_id`,`products_options_values_id`)
												VALUES(	'" . tep_db_input($opa_row['orders_products_attributes_id']) . "','" . tep_db_input($opa_row['orders_id']) . "','" . tep_db_input($opa_row['orders_products_id']) . "',
														'" . tep_db_input($opa_row['products_options']) . "','" . tep_db_input($opa_row['products_options_values']) . "','" . tep_db_input($opa_row['options_values_price']) . "',
														'" . tep_db_input($opa_row['price_prefix']) . "','" . tep_db_input($opa_row['products_options_id']) . "','" . tep_db_input($opa_row['products_options_values_id']) . "')";
                            tep_db_query($opa_copy_sql, 'archive_link');
                        }


                        $ope_select_sql = "SELECT * FROM " . $raw_db . ".orders_products_eta WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $ope_result_sql = tep_db_query($ope_select_sql);
                        while ($ope_row = tep_db_fetch_array($ope_result_sql)) {
                            $ope_copy_sql = "INSERT INTO `orders_products_eta`
														(`orders_products_id`,`expiry_hour`,`start_time`,`total_buyback_time`)
												VALUES(	'" . tep_db_input($ope_row['orders_products_id']) . "','" . tep_db_input($ope_row['expiry_hour']) . "','" . tep_db_input($ope_row['start_time']) . "','" . tep_db_input($ope_row['total_buyback_time']) . "')";
                            tep_db_query($ope_copy_sql, 'archive_link');
                        }

                        $opei_select_sql = "SELECT * FROM " . $raw_db . ".orders_products_extra_info WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $opei_result_sql = tep_db_query($opei_select_sql);
                        while ($opei_row = tep_db_fetch_array($opei_result_sql)) {
                            $opei_copy_sql = "INSERT INTO `orders_products_extra_info`
														(`orders_products_id`,`orders_products_extra_info_key`,`orders_products_extra_info_value`)
												VALUES(	'" . tep_db_input($opei_row['orders_products_id']) . "','" . tep_db_input($opei_row['orders_products_extra_info_key']) . "','" . tep_db_input($opei_row['orders_products_extra_info_value']) . "')";
                            tep_db_query($opei_copy_sql, 'archive_link');
                        }

                        $opi_select_sql = "SELECT * FROM " . $raw_db . ".orders_products_item WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $opi_result_sql = tep_db_query($opi_select_sql);
                        while ($opi_row = tep_db_fetch_array($opi_result_sql)) {
                            $opi_copy_sql = "INSERT INTO `orders_products_item`
														(`orders_products_id`,`products_hla_characters_id`,`products_hla_characters_name`,`orders_products_item_info`)
												VALUES(	'" . tep_db_input($opi_row['orders_products_id']) . "','" . tep_db_input($opi_row['products_hla_characters_id']) . "','" . tep_db_input($opi_row['products_hla_characters_name']) . "','" . tep_db_input($opi_row['orders_products_item_info']) . "')";
                            tep_db_query($opi_copy_sql, 'archive_link');
                        }

                        $ocp_select_sql = "SELECT * FROM " . $raw_db . ".orders_custom_products WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $ocp_result_sql = tep_db_query($ocp_select_sql);
                        while ($ocp_row = tep_db_fetch_array($ocp_result_sql)) {
                            $ocp_copy_sql = "INSERT INTO `orders_custom_products`
														(`orders_custom_products_id`,`products_id`,`orders_products_id`,`orders_custom_products_key`,
														`orders_custom_products_value`,`orders_custom_products_number`)
												VALUES(	'" . tep_db_input($ocp_row['orders_custom_products_id']) . "','" . tep_db_input($ocp_row['products_id']) . "','" . tep_db_input($ocp_row['orders_products_id']) . "',
														'" . tep_db_input($ocp_row['orders_custom_products_key']) . "','" . tep_db_input($ocp_row['orders_custom_products_value']) . "','" . tep_db_input($ocp_row['orders_custom_products_number']) . "')";
                            tep_db_query($ocp_copy_sql, 'archive_link');
                        }

                        $gc_select_sql = "SELECT * FROM " . $raw_db . ".game_char WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $gc_result_sql = tep_db_query($gc_select_sql);
                        while ($gc_row = tep_db_fetch_array($gc_result_sql)) {
                            $gc_copy_sql = "INSERT INTO `game_char`
														(`game_char_id`,`orders_products_id`,`name`,`server`)
												VALUES(	'" . tep_db_input($gc_row['game_char_id']) . "','" . tep_db_input($gc_row['orders_products_id']) . "','" . tep_db_input($gc_row['name']) . "','" . tep_db_input($gc_row['server']) . "')";
                            tep_db_query($gc_copy_sql, 'archive_link');
                        }

                        $ctui_select_sql = "SELECT * FROM " . $raw_db . ".customers_top_up_info WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $ctui_result_sql = tep_db_query($ctui_select_sql);
                        while ($ctui_row = tep_db_fetch_array($ctui_result_sql)) {
                            $ctui_copy_sql = "INSERT INTO `customers_top_up_info`
														(`top_up_info_id`,`orders_products_id`,`top_up_value`)
												VALUES(	'" . tep_db_input($ctui_row['top_up_info_id']) . "','" . tep_db_input($ctui_row['orders_products_id']) . "','" . tep_db_input($ctui_row['top_up_value']) . "')";
                            tep_db_query($ctui_copy_sql, 'archive_link');
                        }

                        $ocp_select_sql = "SELECT * FROM " . $raw_db . ".orders_compensate_products WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $ocp_result_sql = tep_db_query($ocp_select_sql);
                        while ($ocp_row = tep_db_fetch_array($ocp_result_sql)) {
                            $ocp_copy_sql = "INSERT INTO `orders_compensate_products`
														(`orders_products_id`,`orders_id`,`compensate_for_orders_products_id`,
														`compensate_entered_currency`,`compensate_entered_currency_value`,`compensate_order_currency`,
														`compensate_order_currency_value`,`compensate_accident_amount`,`compensate_non_accident_amount`,
														`compensate_supplier_amount`,`compensate_by_supplier_id`,`compensate_by_supplier_firstname`,
														`compensate_by_supplier_lastname`,`compensate_by_supplier_code`,`compensate_by_supplier_email_address`,
														`orders_compensate_products_added_by`,`orders_compensate_products_messages`)
												VALUES(	'" . tep_db_input($ocp_row['orders_products_id']) . "','" . tep_db_input($ocp_row['orders_id']) . "','" . tep_db_input($ocp_row['compensate_for_orders_products_id']) . "',
														'" . tep_db_input($ocp_row['compensate_entered_currency']) . "','" . tep_db_input($ocp_row['compensate_entered_currency_value']) . "','" . tep_db_input($ocp_row['compensate_order_currency']) . "',
														'" . tep_db_input($ocp_row['compensate_order_currency_value']) . "','" . tep_db_input($ocp_row['compensate_accident_amount']) . "','" . tep_db_input($ocp_row['compensate_non_accident_amount']) . "',
														'" . tep_db_input($ocp_row['compensate_supplier_amount']) . "','" . tep_db_input($ocp_row['compensate_by_supplier_id']) . "','" . tep_db_input($ocp_row['compensate_by_supplier_firstname']) . "',
														'" . tep_db_input($ocp_row['compensate_by_supplier_lastname']) . "','" . tep_db_input($ocp_row['compensate_by_supplier_code']) . "','" . tep_db_input($ocp_row['compensate_by_supplier_email_address']) . "',
														'" . tep_db_input($ocp_row['orders_compensate_products_added_by']) . "','" . tep_db_input($ocp_row['orders_compensate_products_messages']) . "')";
                            tep_db_query($ocp_copy_sql, 'archive_link');
                        }

                        $otu_select_sql = "SELECT * FROM " . $raw_db . ".orders_top_up WHERE orders_products_id='" . $op_row['orders_products_id'] . "'";
                        $otu_result_sql = tep_db_query($otu_select_sql);
                        while ($otu_row = tep_db_fetch_array($otu_result_sql)) {

                            $otu_copy_sql = "INSERT INTO `orders_top_up`
														(`orders_products_id`,`top_up_id`,`publishers_ref_id`,`top_up_status`,`publishers_response_time`,
														`customer_before_balance`,`customer_after_balance`,`game`,`server`,
														`account`,`character`,`publishers_id`,`top_up_process_flag`,
														`result_code`,`top_up_last_processed_time`,`top_up_timestamp`,`top_up_created_date`)
												VALUES(	'" . tep_db_input($otu_row['orders_products_id']) . "','" . tep_db_input($otu_row['top_up_id']) . "','" . tep_db_input($otu_row['publishers_ref_id']) . "',
														'" . tep_db_input($otu_row['top_up_status']) . "','" . tep_db_input($otu_row['publishers_response_time']) . "','" . tep_db_input($otu_row['customer_before_balance']) . "',
														'" . tep_db_input($otu_row['customer_after_balance']) . "','" . tep_db_input($otu_row['game']) . "','" . tep_db_input($otu_row['server']) . "',
														'" . tep_db_input($otu_row['account']) . "','" . tep_db_input($otu_row['character']) . "','" . tep_db_input($otu_row['publishers_id']) . "',
														'" . tep_db_input($otu_row['top_up_process_flag']) . "','" . tep_db_input($otu_row['result_code']) . "','" . tep_db_input($otu_row['top_up_last_processed_time']) . "',
														'" . tep_db_input($otu_row['top_up_timestamp']) . "','" . tep_db_input($otu_row['top_up_created_date']) . "')";
                            tep_db_query($otu_copy_sql, 'archive_link');

                            $otur_select_sql = "SELECT * FROM " . $raw_db . ".orders_top_up_remark WHERE top_up_id='" . $otu_row['top_up_id'] . "'";
                            $otur_result_sql = tep_db_query($otur_select_sql);
                            while ($otur_row = tep_db_fetch_array($otur_result_sql)) {
                                $otur_copy_sql = "INSERT INTO `orders_top_up_remark`
															(`orders_top_up_remark_id`,`top_up_id`,`data_added`,`remark`)
													VALUES(	'" . tep_db_input($otur_row['orders_top_up_remark_id']) . "','" . tep_db_input($otur_row['top_up_id']) . "','" . tep_db_input($otur_row['data_added']) . "','" . tep_db_input($otur_row['remark']) . "')";
                                tep_db_query($otur_copy_sql, 'archive_link');
                            }

                            $topup_list[] = $otu_row['top_up_id'];
                        }

                        $orders_products_list[] = $op_row['orders_products_id'];
                    }

                    $orders_list[] = $orders_row['orders_id'];
                }

                if (count($topup_list) > 0) {
                    $topup_str = implode("','", $topup_list);
                    $otur_delete_sql = "DELETE FROM " . $raw_db . ".orders_top_up_remark
										WHERE top_up_id IN ('" . $topup_str . "')";
                    tep_db_query($otur_delete_sql);
                }

                if (count($orders_products_list) > 0) {
                    $op_str = implode("','", $orders_products_list);

                    $ocp_delete_sql = "	DELETE FROM " . $raw_db . ".orders_compensate_products
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($ocp_delete_sql);

                    $otu_delete_sql = "	DELETE FROM " . $raw_db . ".orders_top_up
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($otu_delete_sql);

                    $ctui_delete_sql = "DELETE FROM " . $raw_db . ".customers_top_up_info
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($ctui_delete_sql);

                    $gc_delete_sql = "	DELETE FROM " . $raw_db . ".game_char
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($gc_delete_sql);

                    $ocp_delete_sql = "	DELETE FROM " . $raw_db . ".orders_custom_products
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($ocp_delete_sql);

                    $opi_delete_sql = "	DELETE FROM " . $raw_db . ".orders_products_item
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($opi_delete_sql);

                    $opei_delete_sql = "DELETE FROM " . $raw_db . ".orders_products_extra_info
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($opei_delete_sql);

                    $ope_delete_sql = "	DELETE FROM " . $raw_db . ".orders_products_eta
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($ope_delete_sql);

                    $opa_delete_sql = "	DELETE FROM " . $raw_db . ".orders_products_attributes
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($opa_delete_sql);

                    $op_delete_sql = "	DELETE FROM " . $raw_db . ".orders_products
										WHERE orders_products_id IN ('" . $op_str . "')";
                    tep_db_query($op_delete_sql);
                }

                if (count($orders_list) > 0) {
                    $o_str = implode("','", $orders_list);

                    $pei_delete_sql = "	DELETE FROM " . $raw_db . ".payment_extra_info
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($pei_delete_sql);

                    $olt_delete_sql = "DELETE FROM " . $raw_db . ".orders_log_table
										WHERE orders_log_orders_id IN ('" . $o_str . "')";
                    tep_db_query($olt_delete_sql);

                    $mh_delete_sql = "	DELETE FROM " . $raw_db . ".maxmind_history
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($mh_delete_sql);

                    $gcl_delete_sql = "	DELETE FROM " . $raw_db . ".game_char_log
										WHERE game_char_log_orders_id IN ('" . $o_str . "')";
                    tep_db_query($gcl_delete_sql);

                    $cgq_delete_sql = "DELETE FROM " . $raw_db . ".coupon_gv_queue
										WHERE order_id IN ('" . $o_str . "')";
                    tep_db_query($cgq_delete_sql);

                    $crt_delete_sql = "	DELETE FROM " . $raw_db . ".coupon_redeem_track
										WHERE order_id IN ('" . $o_str . "')";
                    tep_db_query($crt_delete_sql);

                    $oss_delete_sql = "	DELETE FROM " . $raw_db . ".orders_status_stat
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($oss_delete_sql);

                    $osh_delete_sql = "	DELETE FROM " . $raw_db . ".orders_status_history
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($osh_delete_sql);

                    $oei_delete_sql = "	DELETE FROM " . $raw_db . ".orders_extra_info
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($oei_delete_sql);

                    $ot_delete_sql = "	DELETE FROM " . $raw_db . ".orders_total
										WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($ot_delete_sql);

                    $o_delete_sql = "DELETE FROM " . $raw_db . ".orders
									WHERE orders_id IN ('" . $o_str . "')";
                    tep_db_query($o_delete_sql);
                }
            }
            tep_db_close('archive_link');
        } else if (isset($_POST['yii_sess_btn'])) {
            tep_db_query("TRUNCATE " . TABLE_YIISESSION);
        } else if (isset($_POST['g2g_cart_btn'])) {
            tep_db_query("TRUNCATE " . TABLE_C2C_CUSTOMERS_BASKET);
        }

        $messageStack->add_session("Archive has been successfully performed.", 'success');
        tep_redirect(tep_href_link(FILENAME_DB_ARCHIVE, tep_get_all_get_params(array('action'))));

        break;
    default:
        $header_title = HEADER_FORM_DB_ARCHIVE_TITLE;

        ob_start();
        echo tep_draw_form('archive_db_form', FILENAME_DB_ARCHIVE, tep_get_all_get_params(array('action', 'subaction')) . 'action=do_archive', 'post');
        ?>
        <table border="0" width="60%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
            <tr>
                <td class="reportBoxHeading" width="80%"><?= TABLE_HEADING_DB_ARCHIVE_ACTIVITY ?></td>
                <td class="reportBoxHeading" width="20%"><?= TABLE_HEADING_DB_ARCHIVE_ACTION ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Clear Temp Process Table</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="temp_process_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Optimise PVM tables</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="process_optimise_pvm_tables_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Clear Customer Cart</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="customer_cart_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Archive Store Credit Daily History table</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="sc_history_archive_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Archive Canceled Orders</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="canceled_orders_archive_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <!--<tr class="ordersListingEven">
                    <td class="reportRecords">Average Buying Price</td>
                    <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="buying_price_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>-->
            <tr class="ordersListingEven">
                <td class="reportRecords">Clear Shasso Session</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="yii_sess_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <tr class="ordersListingEven">
                <td class="reportRecords">Clear G2G Cart</td>
                <td class="reportRecords"><?= tep_submit_button('Go', 'Go', 'name="g2g_cart_btn" onClick="return confirm_action(\'Are you sure?\')"', 'inputButton') ?></td>
            </tr>
            <table>
        <?
        $form_content = ob_get_contents();
        ob_end_clean();
        break;
}
?>
        <!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
        <html <?= HTML_PARAMS ?> >
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
                <title><?= TITLE ?></title>
                <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
                <script language="javascript" src="includes/general.js"></script>
            </head>
            <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
                <div id="spiffycalendar" class="text"></div>
                <!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
                <!-- header_eof //-->
                <!-- body //-->
                <table border="0" width="100%" cellspacing="2" cellpadding="2">
                    <tr>
                        <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                            <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                                <!-- left_navigation //-->
<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                                <!-- left_navigation_eof //-->
                            </table>
                        </td>
                        <!-- body_text //-->
                        <td width="100%" valign="top">
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td width="100%" class="pageHeading"><b><?= $header_title ?></b></td>
                                </tr>
                                <tr>
                                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                </tr>
                                <tr>
                                    <td width="100%" valign="top"><?= $form_content ?></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
                <!-- footer_eof //-->
                <? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
            </body>
        </html>