<?php  
require('includes/application_top.php');

tep_set_time_limit(0);

function tep_keyword_generator($sentence) {
	$keyword = '';
	$words = explode(' ', $sentence);
	for ($word_counter = 0, $max_word = count($words)-1; $word_counter < $max_word; $word_counter++) {
		$keyword .= substr($words[$word_counter], 0, 1); 
	}
	$keyword .= ' ' . trim($words[count($words) - 1], '.?![](){}*');
	
	return trim($keyword);
}

$default_languages_id = 1;
$news_events_date = '';

$games_array[] = array('id' => '', 'text' => '---'.PULL_DOWN_DEFAULT.'---');

$language_key_array = array( 1 => array(	'SHOP_ONLINE_FOR' => 'SHOP ONLINE FOR',
											'GAME_FEATURES' => 'GAME FEATURES',
											'GAME_LIST' => 'GAME LIST',
											'GAME_DETAILS' => 'GAME DETAILS',
											'PUBLISHER' => 'Publisher',
											'DEVELOPER' => 'Developer',
											'PLATFORM' => 'Platform',
											'CATEGORY' => 'Category',
											'MODEL' => 'Model',
											'REGION' => 'Region',
											'LANGUAGE' => 'Language',
											'STATUS' => 'Status',
											'INTERFACE' => 'Interface',
											'CLIENT_TYPE' => 'Client Type',
											'TIME_KEEPING_SYSTEM' => 'Time Keeping System',
											'YEAR' => 'Year',
											'EXPANSIONS' => 'Expansions',
											'LAUNCHING_DATE' => 'Launching Date',
											'WEBSITE' => 'Website',
											'DOWNLOAD_CLIENT' => 'Download Client',
											'SIGN_UP_ACCOUNT' => 'Sign Up Account',
											'PUBLISHER_PROFILE' => 'PUBLISHER PROFILE',
											'COMPANY' => 'Company',
											'NEWS_EVENTS' => 'NEWS/EVENTS',
											'MORE_NEWS_EVENTS' => 'more news/events',
											'SPECIFICATIONS' => 'SPECIFICATIONS',
											'MINIMUM' => 'Minimum',
											'RECOMMENDED' => 'Recommended',
											'EMPTY_GAMES' => 'There are currently no games available.',
											'EMPTY_NEW_EVENTS' => 'There are currently no new events available.',
											'NOT_AVAILABLE' => 'Not available.',
											'UPCOMING' => 'Upcoming',
											'ESRB_URL' => '/esrb-game-rating-and-review-i-770.ogm'
										),
								2 => array(	'SHOP_ONLINE_FOR' => '在线购物',
											'GAME_FEATURES' => '游戏特色',
											'GAME_LIST' => '游戏列表',
											'GAME_DETAILS' => '游戏详情',
											'PUBLISHER' => '运营商',
											'DEVELOPER' => '开发商',
											'PLATFORM' => '平台',
											'CATEGORY' => '游戏类型',
											'MODEL' => '模式',
											'REGION' => '区域',
											'LANGUAGE' => '语言',
											'STATUS' => '状况',
											'INTERFACE' => '界面',
											'CLIENT_TYPE' => '客户端类型',
											'TIME_KEEPING_SYSTEM' => '计时系统',
											'YEAR' => '年',
											'EXPANSIONS' => '扩张',
											'LAUNCHING_DATE' => '发行日期',
											'WEBSITE' => '网站',
											'DOWNLOAD_CLIENT' => '下载客户端',
											'SIGN_UP_ACCOUNT' => '注册帐户',
											'PUBLISHER_PROFILE' => '运营商简介',
											'COMPANY' => '公司',
											'NEWS_EVENTS' => '消息/活动',
											'MORE_NEWS_EVENTS' => '更多消息/活动',
											'SPECIFICATIONS' => '使用规格',
											'MINIMUM' => '最低配置',
											'RECOMMENDED' => '推荐配置',
											'EMPTY_GAMES' => '暂无游戏。',
											'EMPTY_NEW_EVENTS' => '暂无新促销活动。',
											'NOT_AVAILABLE' => '无。',
											'UPCOMING' => '即将推出',
											'ESRB_URL' => '/zh-CN/esrb-game-rating-and-reviews-cnsm-i-771.ogm'
										),
								3 => array(	'SHOP_ONLINE_FOR' => '在線購物',
											'GAME_FEATURES' => '游戲特色',
											'GAME_LIST' => '游戲列表',
											'GAME_DETAILS' => '游戲詳情',
											'PUBLISHER' => '運營商',
											'DEVELOPER' => '開發商',
											'PLATFORM' => '平臺',
											'CATEGORY' => '游戲類型',
											'MODEL' => '模式',
											'REGION' => '區域',
											'LANGUAGE' => '語言',
											'STATUS' => '狀況',
											'INTERFACE' => '界面',
											'CLIENT_TYPE' => '客戶端類型',
											'TIME_KEEPING_SYSTEM' => '計時系統',
											'YEAR' => '年',
											'EXPANSIONS' => '擴張',
											'LAUNCHING_DATE' => '發行日期',
											'WEBSITE' => '網站',
											'DOWNLOAD_CLIENT' => '下載客戶端',
											'SIGN_UP_ACCOUNT' => '注冊帳戶',
											'PUBLISHER_PROFILE' => '運營商簡介',
											'COMPANY' => '公司',
											'NEWS_EVENTS' => '消息/活動',
											'MORE_NEWS_EVENTS' => '更多消息/活動',
											'SPECIFICATIONS' => '使用規格',
											'MINIMUM' => '最低配置',
											'RECOMMENDED' => '推薦配置',
											'EMPTY_GAMES' => '暫無游戲。',
											'EMPTY_NEW_EVENTS' => '暫無新促銷活動。',
											'NOT_AVAILABLE' => '無。',
											'UPCOMING' => '即將推出',
											'ESRB_URL' => '/zh-TW/esrb-game-rating-and-reviews-cntd-i-772.ogm'
										)
							);
										
$game_templates_array = array( 	0 => array('id' => 'GAMES_LANDING', 'text' => 'GAME'),
								1 => array('id' => 'GAMES_PUBLISHER', 'text' => 'PUBLISHER')
								);

$lang_id = empty($languages_id) ? $default_languages : $languages_id;
$language_id = isset($_REQUEST['language_id']) ? $_REQUEST['language_id'] : $lang_id;
$language_key = isset($language_key_array[$language_id]) ? $language_id : $default_languages_id;
$copy_from_language_id = (isset($_REQUEST['copy_from_language_id']) && $_REQUEST['copy_from_language_id'] != '-') ? $_REQUEST['copy_from_language_id'] : $language_id;
$category_id = isset($_REQUEST['category_id']) ? $_REQUEST['category_id'] : '';
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

// Game Database Configuration
$game_db_array = array();
$game_db_config = array ( 'esrb', 'genre', 'platform', 'language', 'region' );

foreach ($game_db_config as $cftype) {
	$cftable = strtoupper($cftype);
	$main_table = 'TABLE_GAME_' . $cftable;
	$desc_table = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';
	
	$game_config_select_sql = "	SELECT t2.game_{$cftype}_id AS id, t2.game_{$cftype}_description AS description
								FROM " . constant($main_table) . " AS t1 
								LEFT JOIN " . constant($desc_table) . " AS t2 
									ON t2.game_{$cftype}_id = t1.game_{$cftype}_id 
							 	WHERE t2.language_id = '" . $default_languages_id . "' 
							 	ORDER BY t1.sort_order";
	$game_config_result_sql = tep_db_query($game_config_select_sql);
	while ($game_config_rows = tep_db_fetch_array($game_config_result_sql)) {
		$game_db_array[$cftype][] = array (	'id' => $game_config_rows['id'], 
											'text' => $game_config_rows['description'] );
	}
}

if ($category_id > 0) {
	$config_value_select_sql = "SELECT categories_setting_value 
								FROM ".TABLE_CATEGORIES_SETTING_LANG." 
								WHERE categories_id = '".(int)$category_id."' 
									AND language_id = '".(int)$copy_from_language_id."' 
									AND categories_setting_key = 'define_land_game'";
	$config_value_result_sql = tep_db_query($config_value_select_sql);
	$config_value_row = tep_db_fetch_array($config_value_result_sql);
	$content_array = unserialize($config_value_row["categories_setting_value"]);
	
	// get data from table categories_game_details
	$get_game_details_sql = "	SELECT game_publisher, game_developer, game_model, game_website_name, 
									game_website_url, game_keyword, game_download_client_url, 
									game_signup_account_url, game_spec_minimum, game_spec_recommended, 
									game_template 
								FROM ".TABLE_CATEGORIES_GAME_DETAILS." 
							 	WHERE categories_id = '" . (int) $category_id . "' 
							 		AND language_id = '" . (int) $copy_from_language_id . "'";
	$get_game_details_result_sql = tep_db_query($get_game_details_sql);
	$game_array = tep_db_fetch_array($get_game_details_result_sql);
}

/****************************************************
 *	Start Variables & Arrays defined section		*
 ****************************************************/

if (tep_not_null($action)) {
	if ($_REQUEST["action"] == 'changed_template') {
		$game_template_id = $_REQUEST["game_template_id"];
		$game_features_array = $content_array["game_features_array"];
		$game_details_array = $game_array;
		$news_events_array = $content_array["news_events_array"];
		$specifications_array = $game_array;
		$background_setting_array = $content_array["background_setting_array"];
		$selected_categories_id_array = (array)$content_array["selected_categories_id_array"];
		$upcoming_categories_id_array = (array)$content_array["upcoming_categories_id_array"];
		$gamepublisher_profiles_array = $content_array["gamepublisher_profiles_array"];
	} else {
		$game_template_id = tep_db_prepare_input($_REQUEST["game_template_id"]);
		$game_features_array = tep_db_prepare_input($_REQUEST["game_features"]);
		$game_details_array = tep_db_prepare_input($_REQUEST["game_details"]);
		$news_events_array = tep_db_prepare_input($_REQUEST["news_events"]);
		$specifications_array = tep_db_prepare_input($_REQUEST["specifications"]);
		$background_setting_array = tep_db_prepare_input($_REQUEST["background_setting"]);
		$selected_categories_id_array = tep_db_prepare_input((array)$_REQUEST["selected_categories_id"]);
		$upcoming_categories_id_array = tep_db_prepare_input(array_intersect((array)$_REQUEST["selected_categories_id"], (array)$_REQUEST["upcoming_categories_id"]));
		$gamepublisher_profiles_array = tep_db_prepare_input ($_REQUEST["gamepublisher_profiles"]);
		$autocomplete_status = $_REQUEST["autocomplete_status"];
	}
} else {
	$game_template_id = (($game_array['game_template'] == '') || ($game_array['game_template'] == 0)) ? 'GAMES_LANDING' : 'GAMES_PUBLISHER';
	$game_features_array = $content_array["game_features_array"];
	$game_details_array = $game_array;
	$news_events_array = $content_array["news_events_array"];
	$specifications_array = $game_array;
	$background_setting_array = $content_array["background_setting_array"];
	$selected_categories_id_array = (array)$content_array["selected_categories_id_array"];
	$upcoming_categories_id_array = (array)$content_array["upcoming_categories_id_array"];
	$gamepublisher_profiles_array = $content_array["gamepublisher_profiles_array"];
}


if (tep_not_null($action)) {
	$content_mainpage = '';
	$content_tab = '';
	$games_list_array = array();
	
	if (($action == 'GAMES_LANDING') || ($action == 'GAMES_PUBLISHER')) {
		/*-- clear memcache for SEARCH_ALL_GAME --*/
		$memcache_obj->delete(TABLE_GAME_GENRE_TO_CATEGORIES . '/game_genre_id/array/categories_id/' . (int)$category_id, 0);
		$memcache_obj->delete(TABLE_GAME_PLATFORM_TO_CATEGORIES . '/game_platform_id/array/categories_id/' . (int)$category_id, 0);
		
		$delete_publisher_sql = "DELETE FROM " . TABLE_GAME_TO_PUBLISHER . " WHERE game_id = '" . (int)$category_id . "'";
		tep_db_query($delete_publisher_sql);
		
		$delete_esrb_sql = "DELETE FROM " . TABLE_GAME_ESRB_TO_CATEGORIES . " WHERE categories_id = '" . (int)$category_id . "'";
		tep_db_query($delete_esrb_sql);
		
		$delete_platform_sql = "DELETE FROM " . TABLE_GAME_PLATFORM_TO_CATEGORIES . " WHERE categories_id = '" . (int)$category_id . "'";
		tep_db_query($delete_platform_sql);
		
		$delete_genre_sql = "DELETE FROM " . TABLE_GAME_GENRE_TO_CATEGORIES . " WHERE categories_id = '" . (int)$category_id . "'";
		tep_db_query($delete_genre_sql);
		
		$delete_language_sql = "DELETE FROM " . TABLE_GAME_LANGUAGE_TO_CATEGORIES . " WHERE categories_id = '" . (int)$category_id . "'";
		tep_db_query($delete_language_sql);
		
		$delete_region_sql = "DELETE FROM " . TABLE_GAME_REGION_TO_CATEGORIES . " WHERE categories_id = '" . (int)$category_id . "'";
		tep_db_query($delete_region_sql);
	}
	
	$content_array = array(	"game_template_id" => $game_template_id,
							"game_features_array" => $game_features_array,
							"game_details_array" => $game_details_array, 
							"news_events_array" => $news_events_array,
							"specifications_array" => $specifications_array,
							"background_setting_array" => $background_setting_array,
							"selected_categories_id_array" => $selected_categories_id_array,
							"upcoming_categories_id_array" => $upcoming_categories_id_array,
							"gamepublisher_profiles_array" => $gamepublisher_profiles_array);
	
	switch ($action) {
		case "GAMES_LANDING":
			// when template change from PUBLISHER to GAME
			$delete_publisher_sql = "DELETE FROM " . TABLE_GAME_TO_PUBLISHER . " WHERE publisher_id = '" . (int)$category_id . "'";
			tep_db_query($delete_publisher_sql);
			
			$sql_data_array = array('categories_id' => (int)$category_id, 
									'language_id' => (int)$language_id,
									'categories_setting_key' => 'define_land_game',
									'categories_setting_value' => serialize($content_array)
									);
			
			$sql_select_res = tep_db_query("SELECT categories_id FROM ".TABLE_CATEGORIES_SETTING_LANG." 
											WHERE categories_id = ".(int)$category_id." AND 
												language_id = ".(int)$language_id." AND 
												categories_setting_key = 'define_land_game'");
			
			if (tep_db_num_rows($sql_select_res) > 0) {
				tep_db_perform(TABLE_CATEGORIES_SETTING_LANG, $sql_data_array, 'update', "categories_id = ".(int)$category_id." AND language_id = ".(int)$language_id." AND categories_setting_key = 'define_land_game'");	
			} else {
				tep_db_perform(TABLE_CATEGORIES_SETTING_LANG, $sql_data_array);	
			}
			
			$sql_game_details_array = array('categories_id' => $category_id,
											'language_id' => $language_id,
											'game_publisher' => $game_details_array["publisher"],
											'game_developer' => $game_details_array["developer"],
											'game_model' => $game_details_array["model"],
											'game_status' => $game_details_array["status"],
											'game_interface' => $game_details_array["interface"],
											'game_client_type' => $game_details_array["client_type"],
											'game_time_keeping_system' => $game_details_array["time_keeping_system"],
											'game_year' => $game_details_array["year"],
											'game_expansions' => $game_details_array["expansions"],
											'game_launching_date' => $game_details_array["launching_date"],
											'game_website_name' => $game_details_array["website_name"],
											'game_website_url' => $game_details_array["website_url"],
											'game_keyword' => $game_details_array["keyword"], 
											'game_download_client_url' => $game_details_array["download_client"],
											'game_signup_account_url' => $game_details_array["sign_up_account"],
											'game_spec_minimum' => $specifications_array["minimum"],
											'game_spec_recommended' => $specifications_array["recommended"],
											'game_template' => 0
											);
			
			$select_game_details_sql = "SELECT * FROM ".TABLE_CATEGORIES_GAME_DETAILS." 
										WHERE categories_id > '0' AND categories_id = '".(int) $category_id."' 
											AND language_id > '0' AND language_id = '".(int) $language_id."' ";
			$sql_select_res = tep_db_query($select_game_details_sql);
			
			if (tep_db_num_rows($sql_select_res) > 0) {
				tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $sql_game_details_array, 'update', "categories_id = '".(int) $category_id."' AND language_id = '".(int) $language_id."' ");
			} else {
				tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $sql_game_details_array);
			}
			
		
			/*-- game_to_publisher --*/
			if (tep_not_null($game_details_array['publisher_id'])) {
				for ($i=0, $cnt = count($game_details_array['publisher_id']); $cnt > $i; $i++) {
					$publisher_id = (int)$game_details_array['publisher_id'][$i];
					
					$publisher_array = array (	'game_id' => (int)$category_id, 
												'publisher_id' => (int)$publisher_id );
					tep_db_perform(TABLE_GAME_TO_PUBLISHER, $publisher_array);
				}
			}
			
			
			/*-- game_esrb_to_categories --*/
			if (tep_not_null($game_details_array['game_standard'])) {
				$esrb_array = array (	'game_esrb_id' => (int)$game_details_array['game_standard'], 
										'categories_id' => (int)$category_id );
				tep_db_perform(TABLE_GAME_ESRB_TO_CATEGORIES, $esrb_array);
			}
			
			
			/*-- game_platform_to_categories --*/
			if (tep_not_null($game_details_array['game_platform'])) {
				for ($i=0, $cnt = count($game_details_array['game_platform']); $cnt > $i; $i++) {
					$game_platform_id = (int)$game_details_array['game_platform'][$i];
					
					$platform_array = array (	'game_platform_id' => $game_platform_id, 
												'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_PLATFORM_TO_CATEGORIES, $platform_array);
				}
			}
			
			
			/*-- game_genre_to_categories --*/
			if (tep_not_null($game_details_array['game_genre'])) {
				for ($i=0, $cnt = count($game_details_array['game_genre']); $cnt > $i; $i++) {
					$game_genre_id = (int)$game_details_array['game_genre'][$i];
					
					$genre_array = array (	'game_genre_id' => $game_genre_id, 
											'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_GENRE_TO_CATEGORIES, $genre_array);
				}
			}
			
			/*-- game_region_to_categories --*/
			if (tep_not_null($game_details_array['game_region'])) {
				for ($i=0, $cnt = count($game_details_array['game_region']); $cnt > $i; $i++) {
					$game_region_id = (int)$game_details_array['game_region'][$i];
					
					$region_array = array (	'game_region_id' => $game_region_id, 
											'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_REGION_TO_CATEGORIES, $region_array);
				}
			}
			
			/*-- game_language_to_categories --*/
			if (tep_not_null($game_details_array['game_language'])) {
				for ($i=0, $cnt = count($game_details_array['game_language']); $cnt > $i; $i++) {
					$game_language_id = (int)$game_details_array['game_language'][$i];
					
					$language_array = array (	'game_language_id' => $game_language_id, 
												'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_LANGUAGE_TO_CATEGORIES, $language_array);
				}
			}
			
			
			/*-- create "load_game_detail_data.js" --*/
			$filename = 'load_game_detail_data.js';
			
			if ((tep_not_null($autocomplete_status) && ($autocomplete_status == '1')) || !file_exists(DIR_FS_ADMIN . 'repository/' . $filename)) {
				$file_location = DIR_FS_ADMIN . 'repository/' . $filename;
				
				if (file_exists($file_location)) {
					if (file_exists($file_location . '.bak')) {
						@unlink($file_location . '.bak');
					}
					$oldPermission = @umask(0);
					@chmod($file_location, 0755);
					@umask($oldPermission);
					@rename($file_location, $file_location . '.bak');
				}
				
				if (!$handle = @fopen($file_location, 'w')) {
					exit;
				}
				
				
				/*-- game publisher --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_publisher) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_publisher != '' ORDER BY game_publisher";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_publisher = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_publisher']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game developer --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_developer) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_developer != '' ORDER BY game_developer";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_developer = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_developer']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game model --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_model) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_model != '' ORDER BY game_model";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_model = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_model']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game status --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_status) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_status != '' ORDER BY game_status";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_status = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_status']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game interface --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_interface) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_interface != '' ORDER BY game_interface";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_interface = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_interface']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game client type --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_client_type) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_client_type != '' ORDER BY game_client_type";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_client_type = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_client_type']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game time keeping system --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_time_keeping_system) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_time_keeping_system != '' ORDER BY game_time_keeping_system";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_time_keeping_system = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_time_keeping_system']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game expansions --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_expansions) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_expansions != '' ORDER BY game_expansions";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_expansions = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_expansions']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game website name --*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_website_name) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_website_name != '' ORDER BY game_website_name";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_website_name = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_website_name']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game website url--*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_website_url) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_website_url != '' ORDER BY game_website_url";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_website_url = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_website_url']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game download client url--*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_download_client_url) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_download_client_url != '' ORDER BY game_download_client_url";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_download_client_url = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_download_client_url']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				/*-- game signup account url--*/
				$counter = 0;
				$sel_row_sql = "SELECT DISTINCT(game_signup_account_url) FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE game_signup_account_url != '' ORDER BY game_signup_account_url";
				$sel_row_res = tep_db_query($sel_row_sql);
				
				fwrite($handle, 'var game_signup_account_url = [' . "\n");
				while ($game_row = tep_db_fetch_array($sel_row_res)) {
					fwrite($handle, '{ name: "' . ++$counter . '", to: "' . tep_db_prepare_input($game_row['game_signup_account_url']) . '" }, ' . "\n");
				}
				fwrite($handle, '];' . "\n\n");
				
				
				fclose($handle);
			}
			/*-- create "load_game_detail_data.js" --*/
			
			break;
			
		case "GAMES_PUBLISHER":
			$sql_game_details_array = array('categories_id' => $category_id,
											'language_id' => $language_id,
											'game_publisher' => $game_details_array["publisher"],
											'game_developer' => $game_details_array["developer"],
											'game_website_name' => $game_details_array["website_name"],
											'game_website_url' => $game_details_array["website_url"],
											'game_keyword' => $game_details_array["keyword"], 
											'game_template' => 1
											);
			
			$select_game_details_sql = "SELECT categories_id 
										FROM " . TABLE_CATEGORIES_GAME_DETAILS . " 
										WHERE categories_id > '0' AND categories_id = '".(int) $category_id."' 
											AND language_id > '0' AND language_id = '".(int) $language_id."' ";
			$sql_select_res = tep_db_query($select_game_details_sql);
			if (tep_db_num_rows($sql_select_res) > 0) {
				tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $sql_game_details_array, 'update', "categories_id = '".(int) $category_id."' AND language_id = '".(int) $language_id."' ");	
			} else {
				tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $sql_game_details_array);	
			}
			
			/*-- game_platform_to_categories --*/
			if (tep_not_null($game_details_array['game_platform'])) {
				for ($i=0, $cnt = count($game_details_array['game_platform']); $cnt > $i; $i++) {
					$game_platform_id = (int)$game_details_array['game_platform'][$i];
					
					$platform_array = array (	'game_platform_id' => $game_platform_id, 
												'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_PLATFORM_TO_CATEGORIES, $platform_array);
				}
			}
			
			/*-- game_region_to_categories --*/
			if (tep_not_null($game_details_array['game_region'])) {
				for ($i=0, $cnt = count($game_details_array['game_region']); $cnt > $i; $i++) {
					$game_region_id = (int)$game_details_array['game_region'][$i];
					
					$region_array = array (	'game_region_id' => $game_region_id, 
											'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_REGION_TO_CATEGORIES, $region_array);
				}
			}
			
			/*-- game_language_to_categories --*/
			if (tep_not_null($game_details_array['game_language'])) {
				for ($i=0, $cnt = count($game_details_array['game_language']); $cnt > $i; $i++) {
					$game_language_id = (int)$game_details_array['game_language'][$i];
					
					$language_array = array (	'game_language_id' => $game_language_id, 
												'categories_id' => (int)$category_id );
					tep_db_perform(TABLE_GAME_LANGUAGE_TO_CATEGORIES, $language_array);
				}
			}
			
			$games_list_temp_array = array();
			
			$game_list_select_sql = "	SELECT c.categories_id, cd.categories_image
										FROM ".TABLE_CATEGORIES." AS c
										INNER JOIN ".TABLE_CATEGORIES_DESCRIPTION." AS cd
											ON (c.categories_id = cd.categories_id AND cd.language_id = 1)
										WHERE c.categories_id IN ('".implode("','",$selected_categories_id_array)."')
										ORDER BY cd.categories_name";
			$game_list_result_sql = tep_db_query($game_list_select_sql);
			while ($categories_row = tep_db_fetch_array($game_list_result_sql)) {
				if (in_array($categories_row['categories_id'], $upcoming_categories_id_array)) {
					$games_list_temp_array[] = array( 'id' => $categories_row['categories_id'], 'image' => $categories_row['categories_image']);
				} else {
					$games_list_array[] = array( 'id' => $categories_row['categories_id'], 'image' => $categories_row['categories_image']);
				}
			}
			$games_list_array = array_merge($games_list_temp_array, $games_list_array);
			break;
			
		default:
			break;
	}
	
	// force update `game_template` for all language by `categories_id`
	if (($action == 'GAMES_LANDING') || ($action == 'GAMES_PUBLISHER')) {
		/*-- update advance search `categories_search` --*/
		tep_update_categories_keywords($category_id);
		
		$sql_game_template_array = array ( 'game_template' => (($action == 'GAMES_LANDING') ? 0 : 1) );
		tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $sql_game_template_array, 'update', "categories_id = '" . (int)$category_id . "' ");
	}
}

$autocomplete_status = 0;
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?=HTML_PARAMS?> >
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
		<title><?=TITLE?></title>
		<link rel="stylesheet" type="text/css" href="includes/stylesheet.css?v=<?php echo filemtime('includes/stylesheet.css'); ?>">
		<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
		<link rel="stylesheet" type="text/css" href="includes/javascript/select2/css/select2.min.css">
		
		<script language="javascript" src="includes/javascript/jquery/1.11.0/jquery.min.js"></script>
		<script language="javascript" src="includes/javascript/jquery/migrate/jquery-migrate-1.4.1.min.js"></script>

		<script language="javascript" src="includes/javascript/jquery.tabs.js"></script> 
		<script language="javascript" src="includes/general.js"></script>
		<script language="javascript" src="includes/javascript/php.packed.js"></script>
		<script language="JavaScript" src="includes/javascript/select_box.js"></script>
		<script language="JavaScript" src="includes/javascript/ogm_jquery.js?v=<?php echo filemtime('includes/javascript/ogm_jquery.js'); ?>"></script>
		<script language="javascript" src="includes/javascript/select2/js/select2.min.js"></script>

		<!-- AutoComplete -->
		<script type='text/javascript' src='includes/javascript/jquery.autocomplete.js'></script>
		<script type='text/javascript' src='repository/load_game_detail_data.js'></script>
		<link rel="stylesheet" type="text/css" href="includes/jquery.autocomplete.css" />
		<!-- EOF AutoComplete -->
		
		<script type="text/javascript">
			function add_game_region() {
				var html = '';
				var game_region_id = jQuery('#region').val();
				var game_region_txt = jQuery("#region option[value='" + game_region_id + "']").text();
				
				if (!empty(game_region_id) && typeof game_region_id != 'undefined' && (jQuery('#game_region_' + game_region_id).length == 0)) {
					html += '<div id="game_region_' + game_region_id + '">';
					html += '<a href="javascript: del_game_region(\'' + game_region_id + '\');"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="bottom"></a>';
					html += '&nbsp;&nbsp;' + game_region_txt + '<input type="hidden" name="game_details[game_region][]" value="' + game_region_id + '" />';
					html += '</div>';
				}
				
				jQuery('#game_region_section').append(html);
			}
			
			function del_game_region(game_region_id) {
				if (!empty(game_region_id) && typeof game_region_id != 'undefined') {
					jQuery('#game_region_' + game_region_id).remove();
				}
			}
			
			//var date_start_date = Array();
			var giMonthMode=1;
			
			function addTableRow(jTable){		    
			    var tds = '';
			    var jQtable = jQuery(jTable);
			    
			    jQtable.each(function(){
			        var $table = jQuery(this);
			         // Number of td's in the last table row
			        var n = jQuery('tr:last td', this).length;
			        var r = jQuery('tr', this).length;
			       	
			       	if($table.attr("id") == 'game_features_desc'){
			       		tds += '<tr>';
			       		tds += '<td><textarea name="game_features[subdesc][]" wrap="soft" cols="70" rows="3"></textarea></td>';
			        	tds += '</tr>';
			       	} else {
			       		tds += '<tr>';
						tds += '<td style="vertical-align:top;"><input type="text" name="news_events[image_source][]" style="width:250px" maxlength="180"></td>';
						tds += '<td style="vertical-align:top;"><input type="text" name="news_events[link_desc][]" style="width:250px" maxlength="180"></td>';
						tds += '<td style="vertical-align:top;"><input type="text" name="news_events[link_url][]" style="width:250px" maxlength="180"></td>';
						tds += '<td style="vertical-align:top;"><input id="news_events_date_'+r+'" name="news_events[date][]" type="text" style="vertical-align:top;" onblur="if (self.gfPop) { gfPop.validateUserInput(this); }" maxlength="16" size="16"/><a href="javascript:void(0)" onclick="if(self.gfPop) gfPop.fPopCalendar(document.getElementById(\'news_events_date_'+r+'\'));return false;" HIDEFOCUS><img name="popcal" align="top" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a></td>';
			        	tds += '</tr>';
			       	}
			       	
			        if(jQuery('tbody', this).length > 0){
			            jQuery('tbody', this).append(tds);
			        }else {
			            jQuery(this).append(tds);
			        }
			    });
			}
			function removeTableRow(jTable){
				var jQtable = jQuery(jTable);
				jQtable.each(function(){
				    jQuery('tbody tr:last', this).remove();
				});
			}
			
			function formSubmit(frmname) {
				var prompt_alert = true;
				var message = '';
				var cnt = 0;
				
				document.getElementById('define_landgamepage_form').name= frmname; 
				
				if ((prompt_alert == true) && (message != '')) {
					alert(message);
				} else {
					document.forms[frmname].submit();
				}
			}

			jQuery.noConflict();
			jQuery(document).ready(function() {
				jQuery("#category_id").select2();
				jQuery("#language_id").select2({
					minimumResultsForSearch: -1
				});
				jQuery("#game_template_id").select2({
					minimumResultsForSearch: -1
				});
				jQuery("#copy_from_language_id").select2({
					minimumResultsForSearch: -1
				});
				jQuery("#region").select2({
					minimumResultsForSearch: -1
				});
				jQuery("#game_landing_tab > ul").tabs();
				
				jQuery('#game_features_tab').css({
 					border:'1px solid #C9C9C9'
				});
				
 				jQuery('#game_details_tab').css({
 					border:'1px solid #C9C9C9'
 				});
 				
				callAutoComplete('game_publisher', game_publisher);
				callAutoComplete('game_developer', game_developer);
				callAutoComplete('game_model', game_model);
				callAutoComplete('game_status', game_status);
				callAutoComplete('game_interface', game_interface);
				callAutoComplete('game_client_type', game_client_type);
				callAutoComplete('game_time_keeping_system', game_time_keeping_system);
				callAutoComplete('game_expansions', game_expansions);
				callAutoComplete('game_website_name', game_website_name);
				callAutoComplete('game_website_url', game_website_url);
				callAutoComplete('game_download_client', game_download_client_url);
				callAutoComplete('game_signup_account', game_signup_account_url);
			});


		</script>
		<style type="text/css">
			.header{
				font-weight:bold;text-decoration:underline;line-height: 25px;
			}
			
			.main {
				width: 220px;
				line-height: 25px;
			}
			
			.main_tab {
				width: 105;
			}
		</style>
	</head>
	
	<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
		<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
		<!-- header //-->
		<? require(DIR_WS_INCLUDES . 'header.php'); ?>
		<!-- header_eof //-->
		
		<table border="0" width="100%" cellspacing="2" cellpadding="2">
  			<tr>
    			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
						<!-- left_navigation //-->
						<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
						<!-- left_navigation_eof //-->
    				</table>
    			</td>
				<td valign="top">
					<?=tep_draw_form('define_landgamepage_form', FILENAME_DEFINE_GAME_LAND_PAGE, '', 'post', 'id="define_landgamepage_form"'); ?>
					<?=tep_draw_hidden_field('action','update','id="action"')?>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td class="main"><span class="pageHeading"><? echo HEADING_TITLE; ?></span></td>
						</tr>
						<tr>
							<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
						</tr>
						<tr>
							<td>
								<!-- Selection -->
								<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	$categories_structure_select_sql = "SELECT categories_structures_value 
										FROM " . TABLE_CATEGORIES_STRUCTURES . "
										WHERE categories_structures_key = 'games'";
	$categories_structure_result_sql = tep_db_query($categories_structure_select_sql);
	$categories_structure_row = tep_db_fetch_array($categories_structure_result_sql);
	if(tep_not_null($categories_structure_row)){
		$categories_select_sql = "	SELECT c.categories_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON (c.categories_id = cd.categories_id AND cd.language_id = '1')
									WHERE c.categories_id IN (" . $categories_structure_row['categories_structures_value'] . ") 
									ORDER BY cd.categories_name, c.sort_order";
		$categories_result_sql = tep_db_query($categories_select_sql);
		while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
			$games_array[] = array('id' => $categories_row['categories_id'], 'text' => $categories_row['categories_name']);
		}
	}
?>
									<tr>
										<td class="main" style="font-weight:bold;height: 15px" width="25%"><?=ENTRY_GAMES?></td>
										<td class="main" colspan="2">
											<?=tep_draw_pull_down_menu('category_id', $games_array, $category_id, 'id="category_id" onChange="document.getElementById(\'action\').value=\'\';this.form.submit();"') ?>
										</td>
									</tr>
<? 
	if ($category_id > 0) {
		$languages_select_query = "	SELECT languages_id, name FROM ".TABLE_LANGUAGES."
									WHERE languages_id IN (SELECT language_id FROM ".TABLE_CATEGORIES_DESCRIPTION." WHERE categories_id = ". (int)$category_id .") 
									ORDER BY sort_order";
		$languages_select_result = tep_db_query($languages_select_query);
		while ($languages_select_row = tep_db_fetch_array($languages_select_result)) {
			$languages_array[] = array (
									"id" => $languages_select_row["languages_id"],
									"text" => $languages_select_row["name"]
								);
		}
?>
									<tr>
										<td class="main" style="font-weight:bold;height: 15px"><?=ENTRY_LANGUAGE?></td>
										<td class="main" colspan="2">
											<?=tep_draw_pull_down_menu('language_id', $languages_array, $language_id, 'id="language_id" onChange="document.getElementById(\'action\').value=\'\';this.form.submit();"') ?>
										</td>
									</tr>
									<tr>
										<td class="main" style="font-weight:bold;height: 15px"><?=ENTRY_TEMPLATE?></td>
										<td class="main" colspan="2">
											<?=tep_draw_pull_down_menu('game_template_id', $game_templates_array, $game_template_id, 'id="game_template_id" onChange="document.getElementById(\'action\').value=\'changed_template\';this.form.submit();"') ?>
										</td>
									</tr>
									<tr>
										<td class="main" style="font-weight:bold;height: 15px"><?=ENTRY_COPY_FROM_LANGUAGE?></td>
										<td class="main" colspan="2">
<?
		$copy_from_languages_array[] = array('id' => '-', 'text' => ENTRY_USING_OWN_LANGUAGE);
		foreach($languages_array as $language_array){
			if($language_array['id'] != $language_id) {
				$copy_from_languages_array[] = array('id' => $language_array['id'], 'text' => 'Copy From '.$language_array['text']);
			}
		}
											echo tep_draw_pull_down_menu('copy_from_language_id', $copy_from_languages_array, $copy_from_language_id, 'id="copy_from_language_id" onChange="document.getElementById(\'action\').value=\'\';this.form.submit();"') 
?>
										</td>
									</tr>
									<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
<?php } ?>
								</table>
							</td>
						</tr>
					</table>
					

					<table border="0" width="95%" cellspacing="0" cellpadding="2">
<?php if ($category_id > 0) { ?>
						<tr>
							<td>
								<div id="game_landing_tab">
						            <ul class="ui-tabs-nav">
										<li id="game_features" class="main main_tab ui-tabs-selected"><a href="#game_features_tab"><span><?=SECTION_TITLE_GAME_FEATURES ?></span></a></li>
										<li id="game_details" class="main main_tab"><a href="#game_details_tab"><span><?=SECTION_TITLE_GAME_DETAILS?></span></a></li>
									</ul>
									<div id="game_features_tab" class="ui-tabs-panel" style="border: 1px solid rgb(201, 201, 201); display: block;">
										<table border="0" width="90%" cellspacing="0" cellpadding="2">

<?php
		if ($game_template_id == 'GAMES_LANDING') { 
?>
											<tr><td class="main header" colspan="3"><?=SECTION_TITLE_GAME_FEATURES?></td></tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_DESCRIPTION?></td>
												<td class="main" colspan="2"><?=tep_draw_textarea_field("game_features[desc]", 'soft','70', '10', $game_features_array['desc']);?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_SUB_DESCRIPTION?></td>
												<td class="main" colspan="2">
													<table id="game_features_desc" border="0" width="100%" cellspacing="0" cellpadding="0">
														<tbody>
															
												<?php	for ($k=0; $k < count($game_features_array['subdesc']); $k++) { ?>
															<tr>
																<td><?=tep_draw_textarea_field("game_features[subdesc][]", 'soft','70', '3', $game_features_array['subdesc'][$k]);?></td>
															</tr>
												<?php	}	?>
												
														</tbody>
													</table>
												</td>
											</tr>
											<tr>
												<td>&nbsp;</td>
												<td style="text-align:left;" colspan="2">
													<input type="button" value="Add New Row" onclick="addTableRow('#game_features_desc')">
													<input type="button" value="Delete Last Row" onclick="removeTableRow('#game_features_desc')">
												</td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
											
											<tr><td class="main header" colspan="3"><?=SECTION_TITLE_NEWS_EVENTS?></td></tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_MORE_NEWS_EVENTS?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("news_events[more_news_events]", $news_events_array['more_news_events'],"style='width:250px' maxlength='180'") ?></td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
											
											<tr>
												<td colspan="3">
													<table id="news_events" border="0" cellspacing="0" cellpadding="1">
														<thead>
															<tr>
																<td class="main" width="250px"><?=ENTRY_NEWS_EVENTS_IMAGE_SOURCE?></td>
																<td class="main" width="250px"><?=ENTRY_NEWS_EVENTS_DESCRIPTION?></td>
																<td class="main" width="250px"><?=ENTRY_NEWS_EVENTS_LINK?></td>
																<td class="main"><?=ENTRY_NEWS_EVENTS_DATE?></td>
															</tr>
														</thead>
														<tbody>
															
														<?php	for ($k=0; $k < count($news_events_array['image_source']); $k++){ ?>
															<tr style="vertical-align:top;">
																<td class="main"><?=tep_draw_input_field("news_events[image_source][]", $news_events_array['image_source'][$k],"style='width:250px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[link_desc][]", $news_events_array['link_desc'][$k],"style='width:250px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[link_url][]", $news_events_array['link_url'][$k],"style='width:250px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[date][]", $news_events_array['date'][$k], "style='vertical-align:top;' id='news_events_date_".$k."' size='16' maxlength='16' onblur='if(self.gfPop) gfPop.validateUserInput(this);' readonly") . "<a href='javascript:void(0)' onclick='if(self.gfPop)gfPop.fPopCalendar(document.getElementById(\"news_events_date_".$k."\"));return false;' HIDEFOCUS><img name='popcal' src='includes/javascript/PopCalendarXp/calbtn.gif' style='vertical-align:top;' width='32' height='22' border='0' alt=''></a>"?></td>
															</tr>
														<?php	} ?>
														
														</tbody>
													</table>
												</td>
											</tr>
											<tr>
												<td>&nbsp;</td>
												<td style="text-align:left;" colspan="2">
													<input type="button" value="Add New Row" onclick="addTableRow('#news_events')">
													<input type="button" value="Delete Last Row" onclick="removeTableRow('#news_events')">
												</td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
<?php
		} else {
?>
											<tr>
												<td class="main header" colspan="3"><?=SECTION_TITLE_GAME_PUBLISHER_PROFILE?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_DESCRIPTION?></td>
												<td class="main" colspan="2"><?=tep_draw_textarea_field("gamepublisher_profiles[desc]", 'soft','70', '10', $gamepublisher_profiles_array['desc']);?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_COMPANY_NAME?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("gamepublisher_profiles[company]", $gamepublisher_profiles_array['company'],"style='width:250px'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_COMPANY_WEBSITE?>:</td>
												<td class="main"><?=tep_draw_input_field("gamepublisher_profiles[website_name]", $gamepublisher_profiles_array['website_name'],"style='width:250px'") ?>(NAME)&nbsp;<?=tep_draw_input_field("gamepublisher_profiles[website_url]", $gamepublisher_profiles_array['website_url'],"style='width:250px'") ?>(URL)</td>
											</tr>
											<tr><td class="main" style="line-height: 20px">&nbsp;</td></tr>
											<tr>
												<td class="main header" colspan="3"><?=SECTION_TITLE_NEWS_EVENTS?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_MORE_NEWS_EVENTS?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("news_events[more_news_events]", $news_events_array['more_news_events'],"style='width:250px' maxlength='180'") ?></td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
											<tr>
												<td colspan="3">
													<table id="news_events" border="0" cellspacing="0" cellpadding="2">
														<thead>
															<tr>
																<td class="main" width="200px"><?=ENTRY_NEWS_EVENTS_IMAGE_SOURCE?></td>
																<td class="main" width="200px"><?=ENTRY_NEWS_EVENTS_DESCRIPTION?></td>
																<td class="main" width="200px"><?=ENTRY_NEWS_EVENTS_LINK?></td>
																<td class="main"><?=ENTRY_NEWS_EVENTS_DATE?></td>
															</tr>
														</thead>
														<tbody>
						<?php
							for ($k=0; $k < count($news_events_array['image_source']); $k++) { 
						?>
															<tr style="vertical-align:top;">
																<td class="main"><?=tep_draw_input_field("news_events[image_source][]", $news_events_array['image_source'][$k],"style='width:200px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[link_desc][]", $news_events_array['link_desc'][$k],"style='width:200px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[link_url][]", $news_events_array['link_url'][$k],"style='width:200px' maxlength='180'") ?></td>
																<td class="main"><?=tep_draw_input_field("news_events[date][]", $news_events_array['date'][$k], "style='vertical-align:top;' id='news_events_date_".$k."' width='120px' size='16' maxlength='16' onblur='if(self.gfPop) gfPop.validateUserInput(this);' readonly") . "<a href='javascript:void(0)' onclick='if(self.gfPop)gfPop.fPopCalendar(document.getElementById(\"news_events_date_".$k."\"));return false;' HIDEFOCUS><img name='popcal' src='includes/javascript/PopCalendarXp/calbtn.gif' style='vertical-align:top;' width='32' height='22' border='0' alt=''></a>"?></td>
															</tr>
						<?php
							}
						?>
														</tbody>
													</table>
												</td>
											</tr>
											<tr>
												<td>&nbsp;</td>
												<td colspan="2" style="text-align:left;">
													<input type="button" value="Add New Row" onclick="addTableRow('#news_events')">
													<input type="button" value="Delete Last Row" onclick="removeTableRow('#news_events')">
												</td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
<?php
		}
?>

											<tr>
												<td class="main header" colspan="3"><?=SECTION_TITLE_BACKGROUND_SETTING?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_HEADER_IMAGE?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("background_setting[header]", $background_setting_array['header'],"id='background_setting_header' style='width:250px' maxlength='180'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_FOOTER_IMAGE?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("background_setting[footer]", $background_setting_array['footer'],"id='background_setting_footer' style='width:250px' maxlength='180'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_BACKGROUND_COLOR?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("background_setting[color]", $background_setting_array['color'],"id='background_setting_color' style='width:250px' maxlength='180'") ?></td>
											</tr>
											
											<tr><td colspan="3" style="line-height: 15px;">&nbsp;</td></tr>
										</table>
									</div>

<?php 
			$select_game_details_sql_2 = "	SELECT cgd.*, ged.game_esrb_id 
											FROM ".TABLE_CATEGORIES_GAME_DETAILS." AS cgd 
											LEFT JOIN " . TABLE_GAME_ESRB_TO_CATEGORIES . " AS getc 
												ON getc.categories_id = cgd.categories_id 
											LEFT JOIN " . TABLE_GAME_ESRB_DESCRIPTION . " AS ged 
												ON ged.game_esrb_id = getc.game_esrb_id 
											WHERE cgd.categories_id = '".(int) $category_id."' 
												AND cgd.categories_id > '0' 
												AND cgd.language_id = '".(int) $copy_from_language_id."' 
												AND cgd.language_id > '0' ";
			$select_game_details_sql_res_2 = tep_db_query($select_game_details_sql_2);
			$game_array = tep_db_fetch_array($select_game_details_sql_res_2);
			$game_details_array = $game_array;
			$specifications_array = $game_array;
			
			// Game Platform
			$platform_array = array();
			$platform_select_sql = "SELECT game_platform_id FROM " . TABLE_GAME_PLATFORM_TO_CATEGORIES . " 
									WHERE categories_id = '" . (int) $category_id . "'";
			$platform_result_sql = tep_db_query($platform_select_sql);
			while ($platform_rows = tep_db_fetch_array($platform_result_sql)) {
				$platform_array[] = $platform_rows['game_platform_id'];
			}
			$game_details_array['game_platform'] = $platform_array;
			
			if ($game_template_id == 'GAMES_LANDING') {
				// Game Genre
				$genre_array = array();
				$genre_select_sql = "	SELECT game_genre_id FROM " . TABLE_GAME_GENRE_TO_CATEGORIES . " 
										WHERE categories_id = '" . (int) $category_id . "'";
				$genre_result_sql = tep_db_query($genre_select_sql);
				while ($genre_rows = tep_db_fetch_array($genre_result_sql)) {
					$genre_array[] = $genre_rows['game_genre_id'];
				}
				$game_details_array['game_genre'] = $genre_array;
				
				
				// Game Publisher
				$publisher_array = array();
				$publisher_select_sql = "	SELECT DISTINCT(publisher_id) FROM " . TABLE_GAME_TO_PUBLISHER . " 
											WHERE game_id = '" . (int)$category_id . "'";
				$publisher_result_sql = tep_db_query($publisher_select_sql);
				while ($publisher_rows = tep_db_fetch_array($publisher_result_sql)) {
					$publisher_array[] = $publisher_rows['publisher_id'];
				}
				$game_details_array['publisher_id'] = $publisher_array;
			}
			
			// Game Region and Language
			$reg_array = array();
			$lang_array = array();
			
			// Game Region
			$region_select_sql = "	SELECT game_region_id FROM " . TABLE_GAME_REGION_TO_CATEGORIES . " 
									WHERE categories_id = '" . (int) $category_id . "'";
			$region_result_sql = tep_db_query($region_select_sql);
			while ($region_rows = tep_db_fetch_array($region_result_sql)) {
				$reg_array[] = $region_rows['game_region_id'];
			}
			
			// Game Language
			$lang_select_sql = "	SELECT game_language_id FROM " . TABLE_GAME_LANGUAGE_TO_CATEGORIES . " 
									WHERE categories_id = '" . (int) $category_id . "'";
			$lang_result_sql = tep_db_query($lang_select_sql);
			while ($lang_rows = tep_db_fetch_array($lang_result_sql)) {
				$lang_array[] = $lang_rows['game_language_id'];
			}
			
			$game_details_array['game_region'] = $reg_array;
			$game_details_array['game_language'] = $lang_array;
?>

									<div id="game_details_tab" class="ui-tabs-panel ui-tabs-hide" style="border: 1px solid rgb(201, 201, 201);">
										<table border="0" width="90%" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main header" colspan="3">
													<?=SECTION_TITLE_GAME_DETAILS?>
													<?=tep_draw_hidden_field("autocomplete_status", $autocomplete_status, "id='autocomplete_status'");?>
												</td>
											</tr>
											<?php
												if (tep_not_null($game_db_array['region'])) {
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_REGION?>:</td>
												<td class="main" colspan="2">
													<select id="region" name="region" onChange="javascript: add_game_region();">
														<option value="">-- Select Region --</option>
														<?php
															foreach ($game_db_array['region'] as $num => $array) {
														?>
															<option value="<?php echo $array['id']; ?>"><?php echo $array['text']; ?></option>
														<?php
															}
														?>
													</select>
													<div id="game_region_section">
														<?php
															foreach ($game_db_array['region'] as $num => $array) {
																if (in_array($array['id'], $game_details_array['game_region'])) {
																	echo '<div id="game_region_' . $array['id'] . '">';
																	echo '<a href="javascript: del_game_region(\'' . $array['id'] . '\');"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="bottom"></a>';
																	echo '&nbsp;&nbsp;' . $array['text'] . '<input type="hidden" name="game_details[game_region][]" value="' . $array['id'] . '" />';
																	echo '</div>';
																}
															}
														?>
													</div>
												</td>
											</tr>
											<?php
												}
												
												if (tep_not_null($game_db_array['language'])) {
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_LANGUAGE?>:</td>
												<td class="main" colspan="2">
													<?php
														foreach ($game_db_array['language'] as $num => $array) {
															$checked = false;
															if (in_array($array['id'], $game_details_array['game_language'])) {
																$checked = true;
															}
															
															echo '<nobr>';
															echo tep_draw_checkbox_field('game_details[game_language][]" id="game_language_' . $num . '"', $array['id'], $checked); 
															echo '<label for="game_language_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
															echo '</nobr><wbr />';
														}
													?>
												</td>
											</tr>
											<?php
												}
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_PUBLISHER?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[publisher]", $game_details_array['game_publisher'],"id='game_publisher' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_DEVELOPER?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[developer]", $game_details_array['game_developer'],"id='game_developer' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<?php
												if (tep_not_null($game_db_array['platform'])) {
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_PLATFORM?>:</td>
												<td class="main" colspan="2">
													<?php
														foreach ($game_db_array['platform'] as $num => $array) {
															$checked = false;
															if (in_array($array['id'], $game_details_array['game_platform'])) {
																$checked = true;
															}
															
															echo '<nobr>';
															echo tep_draw_checkbox_field('game_details[game_platform][]" id="game_platform_' . $num . '"', $array['id'], $checked); 
															echo '<label for="game_platform_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
															echo '</nobr><wbr />';
														}
													?>
												</td>
											</tr>
											<?php
												}
												
if ($game_template_id == 'GAMES_LANDING') {
												if (tep_not_null($game_db_array['genre'])) {
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_CATEGORY?>:</td>
												<td class="main" colspan="2">
													<?php
														foreach ($game_db_array['genre'] as $num => $array) {
															$checked = false;
															if (in_array($array['id'], $game_details_array['game_genre'])) {
																$checked = true;
															}
															
															echo '<nobr>';
															echo tep_draw_checkbox_field('game_details[game_genre][]" id="game_genre_' . $num . '"', $array['id'], $checked); 
															echo '<label for="game_genre_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
															echo '</nobr><wbr />';
														}
													?>
												</td>
											</tr>
											<?php
												}
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_MODEL?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[model]", $game_details_array['game_model'],"id='game_model' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_STATUS?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[status]", $game_details_array['game_status'],"id='game_status' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_INTERFACE?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[interface]", $game_details_array['game_interface'],"id='game_interface' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_CLIENT_TYPE?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[client_type]", $game_details_array['game_client_type'],"id='game_client_type' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_TIME_KEEPING_SYSTEM?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[time_keeping_system]", $game_details_array['game_time_keeping_system'],"id='game_time_keeping_system' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_YEAR?>:</td>
												<td class="main" colspan="2">
													<?php
														$array = array();
														
														for ($i=1990; (date('Y') + 5) >= $i; $i++) {
															$array[] = array ( 'id' => $i, 'text' => $i );
														}
														
														echo tep_draw_pull_down_menu("game_details[year]", $array, tep_not_null($game_details_array['game_year']) ? $game_details_array['game_year'] : '');
													?>
												</td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_EXPANSIONS?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[expansions]", $game_details_array['game_expansions'],"id='game_expansions' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_LAUNCHING_DATE?>:</td>
												<td class="main" colspan="2">
													<?=tep_draw_input_field("game_details[launching_date]", $game_details_array['game_launching_date'],"id='game_launching_date' style='width:120px' readonly onBlur='if(self.gfPop) gfPop.validateUserInput(this);'") .
														tep_draw_hidden_field("hid_launching_date", $game_details_array['game_launching_date'],"id='hid_game_launching_date'"). 
														str_repeat('&nbsp;', 3) . "<a href='javascript:void(0)' onclick='if(self.gfPop)gfPop.fPopCalendar(document.getElementById(\"game_launching_date\")); return false;' HIDEFOCUS><img id='popcal' name='popcal' src='includes/javascript/PopCalendarXp/calbtn.gif' style='vertical-align:top;' width='32' height='22' border='0' alt=''></a>"
													?>
												</td>
											</tr>
<?php
}
?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_WEBSITE?>:</td>
												<td class="main" nowrap><?=tep_draw_input_field("game_details[website_name]", $game_details_array['game_website_name'],"id='game_website_name' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?> (NAME)&nbsp;<?=tep_draw_input_field("game_details[website_url]", $game_details_array['game_website_url'],"id='game_website_url' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?> (URL)</td>
											</tr>
											<tr><td style="line-height: 20px">&nbsp;</td></tr>
											<?php
if ($game_template_id == 'GAMES_LANDING') {
												if (tep_not_null($game_db_array['esrb'])) {
													$array = $game_db_array['esrb'];
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_GAME_ESRB?>:</td>
												<td class="main" colspan="2">
													<?=tep_draw_pull_down_menu("game_details[game_standard]", $array, tep_not_null($game_details_array['game_esrb_id']) ? $game_details_array['game_esrb_id'] : '')?>
												</td>
											</tr>
											<tr><td style="line-height: 20px">&nbsp;</td></tr>
											<?php
												}
}
											?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_KEYWORD?>:</td>
												<td class="main" colspan="2">
													<?
														if (is_array($game_array) && count($game_array) > 0) {
															echo tep_draw_textarea_field("game_details[keyword]", 'soft','70', '3', $game_details_array['game_keyword']);
														} else {
															$cat_name = tep_get_categories_name($category_id, $language_id);
															
															if (strlen($cat_name) != mb_strlen($cat_name, mb_detect_encoding($cat_name))) {	// If non-ASCII
																echo tep_draw_textarea_field("game_details[keyword]", 'soft','70', '3', $cat_name);
															} else {
																$keyword = tep_keyword_generator($cat_name);
																echo tep_draw_textarea_field("game_details[keyword]", 'soft','70', '3', $keyword);
															}
														}
													
													?>
													<br /><?=TEXT_SEPARATE_BY_COMMA ?>
												</td>
											</tr>
											<tr><td style="line-height: 20px">&nbsp;</td></tr>
											<?php
if ($game_template_id == 'GAMES_LANDING') {
?>
											<tr>
												<td class="main" valign="top"><?=ENTRY_DOWNLOAD_CLIENT?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[download_client]", $game_details_array['game_download_client_url'],"id='game_download_client' style='width:250px' maxlength='180' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_SIGNUP_ACCOUNT?>:</td>
												<td class="main" colspan="2"><?=tep_draw_input_field("game_details[sign_up_account]", $game_details_array['game_signup_account_url'],"id='game_signup_account' style='width:250px' maxlength='180' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_GAME_PUBLISHER;?>:</td>
												<td class="main" colspan="2">
													<?php
														$publisher_select_sql = "	SELECT DISTINCT(cgd.categories_id), cd.categories_name 
																					FROM " . TABLE_CATEGORIES_GAME_DETAILS . " AS cgd 
																					LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
																						ON cd.categories_id = cgd.categories_id AND cd.language_id = '1' 
																					WHERE cgd.game_template = '1'
																						AND cgd.categories_id != '" . $category_id . "'";
														$publisher_result_sql = tep_db_query($publisher_select_sql);
														if (tep_db_num_rows($publisher_result_sql) > 0) {
															while ($publisher_rows = tep_db_fetch_array($publisher_result_sql)) {
																$checked = false;
																if (in_array($publisher_rows['categories_id'], $game_details_array['publisher_id'])) {
																	$checked = true;
																}
																
																echo tep_draw_checkbox_field('game_details[publisher_id][]" id="publisher_id_' . $publisher_rows['categories_id'], $publisher_rows['categories_id'], $checked);
																echo '<label for="publisher_id_' . $publisher_rows['categories_id'] . '">' . $publisher_rows['categories_name'] . '</label>' . '<br />';
															}
														} else {
															echo '<i>- Empty -</i>';
														}
													?>
												</td>
											</tr>
											<tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
											
											<tr>
												<td class="main header" colspan="3"><?=SECTION_TITLE_SPECIFICATIONS?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_MINIMUM?>:</td>
												<td class="main" colspan="2"><?=tep_draw_textarea_field("specifications[minimum]", 'soft','70', '3', $specifications_array['game_spec_minimum']);?></td>
											</tr>
											<tr>
												<td class="main" valign="top"><?=ENTRY_RECOMMENDED?>:</td>
												<td class="main" colspan="2"><?=tep_draw_textarea_field("specifications[recommended]", 'soft','70', '3', $specifications_array['game_spec_recommended']);?></td>
											</tr>
<?php
}

if ($game_template_id == 'GAMES_PUBLISHER') {
?>

											<tr>
												<td class="main" valign="top"><?=ENTRY_SUPPORTED_TITLES?>:</td>
												<td class="main" colspan="2">
													<?php
														$game_support_select_sql = "SELECT cd.categories_name 
																					FROM " . TABLE_GAME_TO_PUBLISHER . " AS gtp 
																					LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
																						ON cd.categories_id = gtp.game_id AND cd.language_id = '" . (int)$language_id . "' 
																					WHERE gtp.publisher_id = '" . (int) $category_id . "'";
														$game_support_result_sql = tep_db_query($game_support_select_sql);
														if (tep_db_num_rows($game_support_result_sql) > 0) {
															while ($game_support_rows = tep_db_fetch_array($game_support_result_sql)) {
																echo $game_support_rows['categories_name'] . '<br />';
															}
														} else {
															echo '<i>- Empty -</i>';
														}
													?>
												</td>
											</tr>
<?php
}
?>
											<tr><td colspan="3" style="line-height: 15px;">&nbsp;</td></tr>
										</table>
									</div>
							</td>
						</tr>
						<tr>
							<td align="right">
								<input type="button" name="update" value="Update" class="inputButton" onClick="document.getElementById('action').value='<?=$game_template_id;?>'; formSubmit('game_features_form')">&nbsp;
			            		<input type="button" name="cancel" value="Cancel" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_DEFINE_GAME_LAND_PAGE)?>'">
							</td>
						</tr>
<?php } ?>
					</table>
					</form>
				</td>
			</tr>
		</table>

	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
	<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
	</body>
</html>