<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'page_template.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DEFINE_GAME_LAND_PAGE);

tep_set_time_limit(0);
$default_array = array(array('id' => '', 'text' => '---' . PULL_DOWN_DEFAULT . '---'));

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
# 0: category template; 1: product template; 2: game blog template;
$game_template_key = isset($_REQUEST['template_key']) && $_REQUEST['template_key'] !== '' ? (int) $_REQUEST["template_key"] : '';
$id = isset($_REQUEST['id']) ? (int) $_REQUEST['id'] : 0;
$cPath_array = isset($_REQUEST['cPath']) ? tep_parse_category_path($_REQUEST['cPath']) : array();

// Game Database Configuration
$pt_obj = new page_template($id, $game_template_key, $cPath_array);

if ($action == 'update') {
    if ($id > 0  && $game_template_key !== '') {
        switch ($game_template_key) {
            case 0:
            case 1:
            case 2:
                $extra_params = $cPath_array !== array() ? '&cPath=' . implode('_', $cPath_array) : '';
                if ($pt_obj->saveTemplates($_REQUEST) === true) {
                    $messageStack->add_session('Template ' . $pt_obj->getTemplateLabelByKey($game_template_key) . ' successfully saved.', 'success');
                } else if($pt_obj->saveTemplates($_REQUEST) === 'duplicated_custom_url'){
                    $messageStack->add_session('Template ' . $pt_obj->getTemplateLabelByKey($game_template_key) . ' failed to save. This custom_url already belongs to another game.', 'error');
                } else {
                    $messageStack->add_session('Template ' . $pt_obj->getTemplateLabelByKey($game_template_key) . ' failed to save.', 'error');
                }
                
                tep_redirect(tep_href_link(FILENAME_DEFINE_GAME_LAND_PAGE2, 'id=' . $id . '&template_key=' . $game_template_key . $extra_params));
                break;
        }
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?=HTML_PARAMS?>>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
    <title><?=TITLE?></title>
    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css?v=<?php echo filemtime('includes/stylesheet.css'); ?>">
    <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
    <link rel="stylesheet" type="text/css" href="includes/javascript/select2/css/select2.min.css">

    <script language="javascript" src="includes/javascript/jquery/1.11.0/jquery.min.js"></script>
    <script language="javascript" src="includes/javascript/jquery/migrate/jquery-migrate-1.4.1.min.js"></script>

    <script language="javascript" src="includes/javascript/jquery.tabs.js"></script> 
    <script language="javascript" src="includes/general.js"></script>
    <script language="javascript" src="includes/javascript/php.packed.js"></script>
    <script language="JavaScript" src="includes/javascript/select_box.js"></script>
    <script language="JavaScript" src="includes/javascript/ogm_jquery.js?v=<?php echo filemtime('includes/javascript/ogm_jquery.js'); ?>"></script>
    <script language="javascript" src="includes/javascript/jquery.tabs.js"></script> 
    <script type='text/javascript' src='includes/javascript/define_gamepage.js?v=20140724'></script>
    <script language="javascript" src="includes/javascript/select2/js/select2.min.js"></script>
    
    <!-- AutoComplete -->
<!--    <script type='text/javascript' src='includes/javascript/jquery.autocomplete.js'></script>
    <link rel="stylesheet" type="text/css" href="includes/jquery.autocomplete.css" />-->
    <!-- EOF AutoComplete -->
    <style type="text/css">
        .header{
            font-weight:bold;text-decoration:underline;line-height: 25px;
        }

        .main {
            width: 220px;
            line-height: 25px;
        }

        .main_tab {
            width: 105;
        }

        #game_region_section {
            margin-top: 10px;
        }

    </style>
    <script>
        jQuery(document).ready(function(){
            // Initialize select2
            jQuery("#template_key").select2({
                minimumResultsForSearch: -1
			});
            jQuery("#category_id").select2();

            jQuery("#region").select2({
                minimumResultsForSearch: -1
            });
        });
    </script>
    
</head>
	
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
    <div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
    <!-- header //-->
    <? require(DIR_WS_INCLUDES . 'header.php'); ?>
    <!-- header_eof //-->
    <table border="0" width="100%" cellspacing="2" cellpadding="2">
        <tr>
            <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                    <!-- left_navigation //-->
                    <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                    <!-- left_navigation_eof //-->
                </table>
            </td>
            <td valign="top">
                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="main"><span class="pageHeading"><? echo HEADING_TITLE; ?></span></td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
                    </tr>
                    <tr>
                        <td>
<?php
    echo tep_draw_form('define_landgamepage_form', FILENAME_DEFINE_GAME_LAND_PAGE2, '', 'post', 'id="define_landgamepage_form"');
?>
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php
        if (in_array($game_template_key, array('', 0, 2))) {
?>
                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%"><?='Template'?></td>
                                    <td class="main" colspan="2">
                                        <?=tep_draw_pull_down_menu('template_key', $pt_obj->getTemplateList($default_array), $game_template_key, 'id="template_key" onChange="page_reload(\'tpl\')"'); ?>
                                    </td>
                                </tr>
<?php
        }
        
        if ($game_template_key !== '') {
            switch ($game_template_key) {
                case 0:
?>
                                
                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%"><?="Select Category"?></td>
                                    <td class="main" colspan="2">
                                        <?=tep_draw_pull_down_menu('id', $pt_obj->getCategoryList($default_array), $id, 'id="category_id" onChange="page_reload(\'\')"'); ?>
                                    </td>
                                </tr>
<?php
                    break;
                case 2: # Game Blog Template
?>
                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%"><?=ENTRY_GAMES?></td>
                                    <td class="main" colspan="2">
                                        <?=tep_draw_pull_down_menu('id', $pt_obj->getGameList(0, $default_array), $id, 'id="category_id" onChange="page_reload(\'\')"'); ?>
                                    </td>
                                </tr>
<?php
                    break;
                case 1: # Product Template
                    echo    tep_draw_hidden_field('id', $id, 'id="category_id"') .
                            tep_draw_hidden_field('template_key', $game_template_key, 'id="template_key"') .
                            tep_draw_hidden_field('cPath', implode('_', $cPath_array), 'id="cPath"');
?>
                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%"><?='Template'?></td>
                                    <td class="main" colspan="2">Product</td>
                                </tr>
                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%">Product Name</td>
                                    <td class="main" colspan="2"><?=tep_get_products_name($id, 1); ?></td>
                                </tr>
<!--                                <tr>
                                    <td class="main" style="font-weight:bold;height: 15px;width:25%"></td>
                                    <td class="main" colspan="2">
                                        <?=tep_draw_checkbox_field('game[tpl_status]', 1, $pt_obj->getGameValues('tpl_status', 1), '', ' id="tpl_status"'); ?>
                                        <label for="tpl_status">Follow Category Template Setting</label>
                                    </td>
                                </tr>-->
<?php
                    break;
?>
                            </table>
<?php
            }
        }
    if ($id) {
        $languages = tep_get_languages();
        echo tep_draw_hidden_field('action', 'update','id="action"');

?>
                            <!-- Selection -->
                            <table border="0" width="95%" cellspacing="0" cellpadding="2">
<?php
        if (in_array($game_template_key, array(0,2))) {
            if ($game_template_key == 0) {
?>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td class="main header" colspan="3">Category Hierarchy</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div style="width: 510px; max-height: 250px; overflow-y: scroll;">
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td width="8%" align="left" class="ordersBoxHeading">No.</td>	
                                                    <td align="center" class="ordersBoxHeading">Game / Category</td>	
                                                    <td width="4%" class="ordersBoxHeading" align="center">Actions</td>
                                                </tr>
<?php
                if ($category_tag_array = $pt_obj->getCategoryTag()) {
                    $selected_array = $pt_obj->getSelectedCategoryTag();
                    $num = 1;
                    foreach ($category_tag_array as $idx => $array) {
                        $checked = in_array($array['tag_id'], $selected_array) ? true : false;
                        $row_style = ($num%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
?>
                                                <tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')">
                                                    <td class="ordersRecords"><?=$num?></td>
                                                    <td class="ordersRecords"><?php echo '| ' . str_repeat('&#8212;', $array['depth']) . ' ' . $array['tag_title']; ?></td>
                                                    <td class="ordersRecords" align="center">
<?php
                        echo $array['depth'] > 1 ? tep_draw_radio_field('game[category_tag_id][]" id="category_tag_id_' . $num, $array['tag_id'], $checked) : '';
?>
                                                    </td>
                                                </tr>
<?
                        $num++;
                    }
                } else { ?>
                                                <tr>
                                                    <td align="center" class="ordersRecords" colspan="3">No category tag was found.</td>
                                                </tr>
<?		

                }
?>
                                                </table>
                                                </div>
                                            </td>
                                        </tr>
                                 </table>
                                </td>
                            </tr>
<?php
            }
?>
                                            
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td class="main header" colspan="3"><?=SECTION_TITLE_EXTRA_SETTING?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_AF_NOTICE_ENABLE_OPTION?>:</td>
                                            <td class="main" colspan="2">
                                            <?php
                                                $af_notice_enable_status = $pt_obj->getGameValues('af_notice_enable_status', 0);
                                                
                                                echo tep_draw_radio_field("game[extra_setting][af_notice_option]", 0, $af_notice_enable_status == 0, '', " id='extra_setting_af_notice_option_0'") . '<label for="extra_setting_af_notice_option_0">Disabled</label>&nbsp;&nbsp;';
                                                echo tep_draw_radio_field("game[extra_setting][af_notice_option]", 1, $af_notice_enable_status == 1, '', " id='extra_setting_af_notice_option_1'") . '<label for="extra_setting_af_notice_option_1">Enabled with hide option</label>&nbsp;&nbsp;';
                                                echo tep_draw_radio_field("game[extra_setting][af_notice_option]", 2, $af_notice_enable_status == 2, '', " id='extra_setting_af_notice_option_2'") . '<label for="extra_setting_af_notice_option_2">Enabled without hide option</label>';
                                            ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="main header" colspan="3"><?=SECTION_TITLE_BACKGROUND_SETTING?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_HEADER_IMAGE?>:</td>
                                            <td class="main" colspan="2"><?=tep_draw_input_field("game[background_setting][url]", $pt_obj->getGameValues('background_source'),"id='background_setting_url' style='width:250px' maxlength='180'") ?></td>
                                        </tr>
                                        <?php
                                        if ($game_template_key == 2) {
                                        ?>
                                            <tr>
                                                <td class="main" valign="top">Custom URL:</td>
                                                <td class="main" colspan="2"><?=tep_draw_input_field("game[blog][custom_url]", $pt_obj->getGameBlogUrl($id),"id='custom_url' style='width:250px' maxlength='180'") ?></td>
                                            </tr>
                                        <?php
                                        }
                                        ?>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_BACKGROUND_COLOR?>:</td>
                                            <td class="main" colspan="2"><?=tep_draw_input_field("game[background_setting][color]", $pt_obj->getGameValues('background_color'),"id='background_setting_color' style='width:250px' maxlength='180'") ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top">Logo URL:</td>
                                            <td class="main" colspan="2"><?=tep_draw_input_field("game[logo]", $pt_obj->getGameValues('logo_source'), "id='game_image' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_REGION?>:</td>
                                            <td class="main" colspan="2">
<?php
            if ($game_region = $pt_obj->getGameDetailOptions('region')) {
?>
                                                <select id="region" name="region" onChange="javascript: add_game_region();">
                                                   <option value="">-- Select Region --</option>
                                                   <?php
                                                       foreach ($game_region as $num => $array) {
                                                   ?>
                                                       <option value="<?php echo $array['id']; ?>"><?php echo $array['text']; ?></option>
                                                   <?php
                                                       }
                                                   ?>
                                               </select>
                                               <div id="game_region_section">
                                                   <?php
                                                       foreach ($game_region as $num => $array) {
                                                           if (in_array($array['id'], $pt_obj->getGameInfo('game_region', array()))) {
                                                               echo '<div id="game_region_' . $array['id'] . '">';
                                                               echo '<a href="javascript: del_game_region(\'' . $array['id'] . '\');"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="bottom"></a>';
                                                               echo '&nbsp;&nbsp;' . $array['text'] . '<input type="hidden" name="game[game_region][]" value="' . $array['id'] . '" />';
                                                               echo '</div>';
                                                           }
                                                       }
                                                   ?>
                                               </div>
<?php                                            
            }
?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_LANGUAGE?>:</td>
                                            <td class="main" colspan="2">
<?php
            foreach ($pt_obj->getGameDetailOptions('language') as $num => $array) {
                $checked = in_array($array['id'], $pt_obj->getGameInfo('game_language', array())) ? true : false;

                echo '<nobr>';
                echo tep_draw_checkbox_field('game[game_language][]" id="game_language_' . $num, $array['id'], $checked); 
                echo '<label for="game_language_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
                echo '</nobr><wbr />';
            }
?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_PLATFORM?>:</td>
                                            <td class="main" colspan="2">
<?php
            foreach ($pt_obj->getGameDetailOptions('platform') as $num => $array) {
                $checked = in_array($array['id'], $pt_obj->getGameInfo('game_platform', array())) ? true : false;

                echo '<nobr>';
                echo tep_draw_checkbox_field('game[game_platform][]" id="game_platform_' . $num, $array['id'], $checked); 
                echo '<label for="game_platform_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
                echo '</nobr><wbr />';
            }
?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="main" valign="top"><?=ENTRY_CATEGORY?>:</td>
                                            <td class="main" colspan="2">
<?php
            foreach ($pt_obj->getGameDetailOptions('genre') as $num => $array) {
                $checked = in_array($array['id'], $pt_obj->getGameInfo('game_genre', array())) ? true : false;
                
                echo '<nobr>';
                echo tep_draw_checkbox_field('game[game_genre][]" id="game_genre_' . $num, $array['id'], $checked); 
                echo '<label for="game_genre_' . $num . '">' . $array['text'] . '</label>' . str_repeat('&nbsp;', 3);
                echo '</nobr><wbr />';
            }
?>
                                            </td>
                                        </tr>
<?php
        }
        
        if (in_array($game_template_key, array(0, 1, 2))) {
?>
                                        <tr>
                                            <td class="main" valign="top">Supported Games / Categories:</td>
                                            <td class="main" colspan="2">
                                                <div style="width: 510px; max-height: 250px; overflow-y: scroll;">
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td width="8%" align="left" class="ordersBoxHeading">No.</td>	
                                                    <td align="center" class="ordersBoxHeading">Game / Category</td>	
                                                    <td width="4%" class="ordersBoxHeading" align="center">Actions</td>
                                                </tr>
<?php
                if($game_list_array = $pt_obj->getGameList()) {
                    $selected_array = $pt_obj->getSelectedGameList();
                    $num = 1;
                    foreach ($game_list_array as $idx => $array) {
                        $checked = in_array($array['id'], $selected_array) ? true : false;
                        $row_style = ($num%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
?>
                                                <tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')">
                                                    <td class="ordersRecords"><?=$num?></td>
                                                    <td class="ordersRecords"><?=$array['text']; ?></td>
                                                    <td class="ordersRecords" align="center">
<?php
                        echo tep_draw_checkbox_field('game[supported_' . strtolower($pt_obj->getTemplateLabelByKey($game_template_key)) . '_id][]" id="game_supported_game_id_' . $num, $array['id'], $checked);
?>
                                                    </td>
                                                </tr>
<?
                        $num++;
                    }
                } else { 
?>
                                                <tr>
                                                    <td align="center" class="ordersRecords" colspan="3">No game was found.</td>
                                                </tr>
<?php
                }
?>
                                                </table>
                                                </div>
                                            </td>
                                        </tr>
<?php
        }
?>
                                        <tr><td colspan="3" style="line-height: 20px;">&nbsp;</td></tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div id="languages_tab">
                                        <ul>
<?php
        for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
            echo '<li id="languages_li_' . $i . '"><a href="#languages_tab_'.$i.'"><span>'.$languages[$i]['name'].'</span></a></li>';
        }
?>
                                        </ul>
<?php
        for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
            $lang_id = $languages[$i]['id'];
            $pt_obj->language_id = $lang_id;
?>
                                        <div id="languages_tab_<?=$i?>" class="languages_tab">
                                            <table border="0" width="90%" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td class="main header" colspan="2"><?=SECTION_TITLE_GAME_FEATURES?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top">Notice:</td>
                                                    <td class="main"><?=tep_draw_textarea_field("game_details[" . $lang_id . "][notice]", 'soft','70', '3', $pt_obj->getGameDetailValues('notice')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><?=ENTRY_DESCRIPTION?>:</td>
                                                    <td class="main"><?=tep_draw_textarea_field("game_details[" . $lang_id . "][description]", 'soft','70', '10', $pt_obj->getGameDetailValues('description')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top">System Requirements:</td>
                                                    <td class="main"><?=tep_draw_textarea_field("game_details[" . $lang_id . "][system_requirements]", 'soft','70', '10', $pt_obj->getGameDetailValues('system_requirements')); ?></td>
                                                </tr>
<?php
            if (in_array($game_template_key, array(0, 2))) {
?>
                                                <tr>
                                                    <td class="main" valign="top">Remark:</td>
                                                    <td class="main"><?=tep_draw_textarea_field("game_details[" . $lang_id . "][remark]", 'soft','70', '5', $pt_obj->getGameDetailValues('remark')); ?></td>
                                                </tr>
<?php
            }
?>
                                                <tr><td colspan="2" style="line-height: 20px;">&nbsp;</td></tr>
                                                <tr>
                                                    <td class="main header" colspan="2">Gallery</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <table id="gallery_<?php echo $lang_id; ?>" border="0" cellspacing="0" cellpadding="1">
                                                            <thead>
                                                                <tr>
                                                                    <td class="main" width="250px">IMAGE SOURCE</td>
                                                                    <td class="main" width="250px">LINK</td>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr style="vertical-align:top;">
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][image_source][]", $pt_obj->getGalleryImageInfo(0, 'source'),"style='width:250px' maxlength='180'") ?></td>
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][link_url][]", $pt_obj->getGalleryImageInfo(0, 'link'),"style='width:250px' maxlength='180'") ?></td>
                                                            </tr>
                                                            <tr style="vertical-align:top;">
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][image_source][]", $pt_obj->getGalleryImageInfo(1, 'source'),"style='width:250px' maxlength='180'") ?></td>
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][link_url][]", $pt_obj->getGalleryImageInfo(1, 'link'),"style='width:250px' maxlength='180'") ?></td>
                                                            </tr>
                                                            <tr style="vertical-align:top;">
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][image_source][]", $pt_obj->getGalleryImageInfo(2, 'source'),"style='width:250px' maxlength='180'") ?></td>
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][link_url][]", $pt_obj->getGalleryImageInfo(2, 'link'),"style='width:250px' maxlength='180'") ?></td>
                                                            </tr>
                                                            <tr style="vertical-align:top;">
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][image_source][]", $pt_obj->getGalleryImageInfo(3, 'source'),"style='width:250px' maxlength='180'") ?></td>
                                                                <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][gallery][link_url][]", $pt_obj->getGalleryImageInfo(3, 'link'),"style='width:250px' maxlength='180'") ?></td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
<?php
            if (in_array($game_template_key, array(0, 2))) {
?>
                                                <tr><td colspan="2" style="line-height: 20px;">&nbsp;</td></tr>
                                                <tr>
                                                    <td class="main header" colspan="2">
                                                        <?=SECTION_TITLE_GAME_DETAILS?>
                                                        <?=tep_draw_hidden_field("autocomplete_status[" . $lang_id . "]", $autocomplete_status, "id='autocomplete_status'");?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><?='Title'?>:</td>
                                                    <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][game_title]", $pt_obj->getGameDetailValues('game_title'), "id='game_title_" . $lang_id ."'style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><?=ENTRY_PUBLISHER?>:</td>
                                                    <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][game_publisher]", $pt_obj->getGameDetailValues('game_publisher'), "id='game_publisher_" . $lang_id ."' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><?=ENTRY_DEVELOPER?>:</td>
                                                    <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][game_developer]", $pt_obj->getGameDetailValues('game_developer'), "id='game_developer_" . $lang_id ."' style='width:250px' onFocus='javascript:document.getElementById(\"autocomplete_status\").value=1'") ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top">Release date:</td>
                                                    <td class="main">
                                                        <?php
                                                        echo tep_draw_input_field("game_details[" . $lang_id . "][game_release_date]", $pt_obj->getGameDetailValues('game_release_date'),"id='game_launching_date_" . $lang_id . "' style='width:120px' readonly onBlur='if(self.gfPop) gfPop.validateUserInput(this);'") .
    //														tep_draw_hidden_field("hid_launching_date", $pt_obj->getGameDetailValues('game_launching_date'),"id='hid_game_launching_date'"). 
                                                            str_repeat('&nbsp;', 3) . "<a href='javascript:void(0)' onclick='if(self.gfPop)gfPop.fPopCalendar(document.getElementById(\"game_launching_date_" . $lang_id . "\")); return false;' HIDEFOCUS><img id='popcal' name='popcal' src='includes/javascript/PopCalendarXp/calbtn.gif' style='vertical-align:top;' width='32' height='22' border='0' alt=''></a>"
                                                        ?>
                                                    </td>
                                                </tr>
<?php
                $game_keyword = $pt_obj->getGameDetailValues('game_keyword', $lang_id, '-');
                
                echo '<tr><td class="main" valign="top">' . ENTRY_KEYWORD . ':</td><td class="main">';
                
                if ($game_keyword !== '-') {
                    echo tep_draw_textarea_field("game_details[" . $lang_id . "][keyword]", 'soft','70', '3', $game_keyword);
                } else if (in_array($game_template_key, array(0, 2))) {
                    $cat_name = $pt_obj->getTitle($id, $lang_id);
                    
                    if (strlen($cat_name) != mb_strlen($cat_name, mb_detect_encoding($cat_name))) {	// If non-ASCII
                        echo tep_draw_textarea_field("game_details[" . $lang_id . "][keyword]", 'soft','70', '3', $cat_name);
                    } else {
                        echo tep_draw_textarea_field("game_details[" . $lang_id . "][keyword]", 'soft','70', '3', $pt_obj->keyword_generator($cat_name));
                    }
                }
			
				echo '<br />' . TEXT_SEPARATE_BY_COMMA . '</td></tr>';
?>
                                                <tr>
                                                    <td class="main header" colspan="2">Related Link</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <table id="related_link_<?php echo $lang_id; ?>" border="0" cellspacing="0" cellpadding="1">
                                                            <thead>
                                                                <tr>
                                                                    <td class="main" width="250px">Label</td>
                                                                    <td class="main" width="250px">Link</td>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                            <?php foreach ($pt_obj->getRelatedLinkInfo($lang_id) as $info_array){ ?>
                                                                <tr style="vertical-align:top;">
                                                                    <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][related_link][label][]", $info_array['label'], "style='width:250px' maxlength='180'") ?></td>
                                                                    <td class="main"><?=tep_draw_input_field("game_details[" . $lang_id . "][related_link][link][]", $info_array['link'], "style='width:250px' maxlength='180'") ?></td>
                                                                </tr>
                                                            <?php	} ?>

                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align:left;" colspan="2">
                                                        <input type="button" value="Add New Row" onclick="addTableRow('#related_link', '<?php echo $lang_id; ?>')">
                                                        <input type="button" value="Delete Last Row" onclick="removeTableRow('#related_link_<?php echo $lang_id; ?>')">
                                                    </td>
                                                </tr>
<?php
            }
?>
                                                <tr><td colspan="3" style="line-height: 15px;">&nbsp;</td></tr>
                                            </table>
                                        </div>
<?php
        }
?>
                                    </div><!--languages_tab-->
                                </td>
                            </tr>
                            <tr>
                                <td align="right">
                                    <input type="button" name="update" value="Update" class="inputButton" onClick="formSubmit('game_features_form')">&nbsp;
                                    <input type="button" name="cancel" value="Cancel" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_DEFINE_GAME_LAND_PAGE2)?>'">
                                </td>
                            </tr>
                            </table>
					</form>
<?php
    }
?>
				</td>
			</tr>
		</table>

	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
	<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
	</body>
</html>