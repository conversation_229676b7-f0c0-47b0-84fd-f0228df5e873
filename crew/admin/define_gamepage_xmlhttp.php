<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($_REQUEST['action']) ? tep_db_input($_REQUEST['action']) : '';
$category_id = (int)(isset($_REQUEST['category_id']) ? $_REQUEST['category_id'] : 0);
$to_language_id = (int)(isset($_REQUEST['to_language_id']) ? $_REQUEST['to_language_id'] : 0);
$background_setting = tep_db_prepare_input((array)$_REQUEST['background_setting']);
$content_array = array();

echo '<response>';

if (tep_not_null($action)){
	switch($action) {
		case "copy_bgsetting_methods":
			$error_flag = true;
			
			$config_value_select_sql = "SELECT categories_setting_value 
							FROM ".TABLE_CATEGORIES_SETTING_LANG." 
							WHERE categories_id = ".$category_id." 
								AND language_id = ".$to_language_id." 
								AND categories_setting_key = 'define_land_game'";
			$config_value_result_sql = tep_db_query($config_value_select_sql);
			$config_value_row = tep_db_fetch_array($config_value_result_sql);
			if(tep_not_null($config_value_row)){
				$content_array = unserialize($config_value_row["categories_setting_value"]);
				$sql_data_array = array_merge($content_array, array('background_setting_array' => $background_setting));
				$sql_data_array = array('categories_setting_value' => serialize($sql_data_array));
				
				tep_db_perform(TABLE_CATEGORIES_SETTING_LANG, $sql_data_array, 'update', "categories_id = ".(int)$category_id." AND language_id = ".(int)$to_language_id." AND categories_setting_key = 'define_land_game'");	
				
				$category_desc_select_sql = "	SELECT categories_description 
												FROM ".TABLE_CATEGORIES_DESCRIPTION." 
												WHERE categories_id = ".$category_id." 
													AND language_id = ".$to_language_id;
				$category_desc_result_sql = tep_db_query($category_desc_select_sql);
				$category_desc_row = tep_db_fetch_array($category_desc_result_sql);
				if(tep_not_null($category_desc_row)){
					$patterns[0] = '/'.$content_array['background_setting_array']['color'].'/'; 
					$patterns[1] = '/'.str_replace("/","\/",$content_array['background_setting_array']['header']).'/';
					$patterns[2] = '/'.str_replace("/","\/",$content_array['background_setting_array']['footer']).'/';
					$replacements[0] = $background_setting['color']; 
					$replacements[1] = $background_setting['header'];
					$replacements[2] = $background_setting['footer'];
					
					$content_mainpage = preg_replace($patterns, $replacements, $category_desc_row['categories_description']);
					$sql_data_array2 = array('categories_description' => $content_mainpage);
					tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array2, 'update', "categories_id = ".$category_id." AND language_id = ".$to_language_id);	
				}
				$error_flag = false;
			} else {
				$content_array = array("background_setting_array" => $background_setting);
				$sql_data_array = array('categories_id' => $category_id, 
										'language_id' => $to_language_id,
										'categories_setting_key' => 'define_land_game',
										'categories_setting_value' => serialize($content_array)
										);
				
				tep_db_perform(TABLE_CATEGORIES_SETTING_LANG, $sql_data_array);	
				$error_flag = false;
			}
			echo "	<backgroundsetting_methods>";
			
			if($error_flag){
				$message = TEXT_INFO_DATA_FAILED_COPY;
				echo "	<success>0</success>";
			} else {
				$message = TEXT_INFO_DATA_COPIED;
				echo "	<success>1</success>";
			}
			
			echo "	<message><![CDATA[".$message."]]></message>";
			echo "	</backgroundsetting_methods>";
			break;
	}
}
echo '</response>';
?>