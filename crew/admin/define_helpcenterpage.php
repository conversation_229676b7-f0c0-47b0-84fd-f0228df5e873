<?
require('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (isset($_REQUEST['id']) ? $_REQUEST['id'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "update":
			$sql_data_array = array('language_id' => $languages_id,
									'Text' => $HTTP_POST_VARS['content_text']);
			
			tep_db_perform(TABLE_DEFINE_MAINPAGE, $sql_data_array, 'update', "language_id = '" . (int)$languages_id . "' AND Id = 2");
			break;
	}
	tep_redirect(tep_href_link(FILENAME_DEFINE_HELPCENTERPAGE));
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</head>
<?
$define_helpcenterpage_content_select_sql = " SELECT Text FROM " . TABLE_DEFINE_MAINPAGE . " WHERE language_id = '" . (int)$languages_id . "' AND Id = 2";
$define_helpcenterpage_content_result_sql = tep_db_query($define_helpcenterpage_content_select_sql);
$define_helpcenterpage_content_row = tep_db_fetch_array($define_helpcenterpage_content_result_sql);
?>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
<?				echo tep_draw_form('define_helpcenterpage_form', FILENAME_DEFINE_HELPCENTERPAGE, 'action=update'); ?>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
						</tr>
						<tr>
							<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
						</tr>
						<tr>
							<td>
								<table border="0" width="100%" cellspacing="0" cellpadding="0">
									<tr>
										<td class="main" valign="top"><?=ENTRY_HELP_CENTER_PAGE_CONTENT?></td>
										<td><?=tep_draw_textarea_field('content_text', 'soft', '100%', '110', stripslashes($define_helpcenterpage_content_row['Text']))?></td>
									</tr>
									<tr>
										<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td></td>
										<td><?=tep_image_button('button_update.gif', IMAGE_INSERT, 'onClick="document.define_helpcenterpage_form.submit();" style="cursor: pointer;"') . ' ' . '<a href="' . tep_href_link(FILENAME_DEFINE_MAINPAGE) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</form>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>