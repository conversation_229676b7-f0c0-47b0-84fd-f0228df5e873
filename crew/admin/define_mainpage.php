<?php
include_once('includes/application_top.php');

tep_set_time_limit(120);

# page setting
define('MAX_BEST_SELLING_IMAGE', 4);
define('DEFAULT_GEO_TYPE', 1);			// localization type : Game & Product
define('DEFAULT_GEO_ZONE_ID', 4);		// default localization id
define('MAX_ALL_PAYMENT_IMAGE', 1);

$lang_id = tep_not_empty($languages_id) ? $languages_id : 1;
$language_id = tep_not_empty($_REQUEST['language_id']) ? $_REQUEST['language_id'] : $lang_id;
$geo_zone_id = tep_not_empty($_REQUEST['geo_zone_id']) ? $_REQUEST['geo_zone_id'] : DEFAULT_GEO_ZONE_ID;
$action = tep_not_empty($_REQUEST['action']) ? $_REQUEST['action'] : '';
$id = tep_not_empty($_REQUEST['id']) ? $_REQUEST['id'] : '';


if (tep_not_empty($action)) {
	switch ($action) {
		case 'update':
			$geo_zone_id_list = array();
			$serialize_data = array();
			$slider = array();
			
			$serialize_data['mainpage_best_selling_image'] = tep_db_prepare_input($_REQUEST['img_src']);
			$serialize_data['footer_all_payment_image'] = tep_db_prepare_input($_REQUEST['payment_img_src']);
			
			if(tep_not_empty($_POST['slider_background_image'])) {
				foreach ($_POST['slider_background_image'] as $key => $val) {
					$slider[] = array ( 'slider_background_image' => $val, 
										'slider_thumbnail_image' => tep_not_empty($_POST['slider_thumbnail_image'][$key]) ? $_POST['slider_thumbnail_image'][$key] : '', 
										'slider_image_url' => tep_not_empty($_POST['slider_image_url'][$key]) ? $_POST['slider_image_url'][$key] : '',
										'slider_caption' => tep_not_empty($_POST['slider_caption'][$key]) ? $_POST['slider_caption'][$key] : '' );
				}
				$serialize_data['mainpage_slider_content'] = $slider;
			}
			
			if (tep_not_empty($_POST['clone_geo_zone_id'])) {
				$geo_zone_id_list = explode(',', $_POST['clone_geo_zone_id']);
			}
			$geo_zone_id_list[] = (int)$geo_zone_id;
			
			foreach ($geo_zone_id_list as $zone_id) {
				$sql_data_array = array('geo_zone_id' => (int)$zone_id, 
										'Id' => '1', 
										'language_id' => $language_id,
										'mainpage_slider_content' => serialize($serialize_data['mainpage_slider_content']), 
										'mainpage_best_selling_image' => serialize($serialize_data['mainpage_best_selling_image']), 
										'footer_all_payment_image' => serialize($serialize_data['footer_all_payment_image']));
				$sql_select_res = tep_db_query("SELECT geo_zone_id FROM " . TABLE_DEFINE_MAINPAGE . " WHERE Id = '1' AND language_id = '" . (int)$language_id . "' AND geo_zone_id = '" . (int)$zone_id . "'");
				if (tep_db_num_rows($sql_select_res) > 0) {
					tep_db_perform(TABLE_DEFINE_MAINPAGE, $sql_data_array, 'update', "Id = '1' AND language_id = '" . (int)$language_id . "' AND geo_zone_id = '" . (int)$zone_id . "' ");	
				} else {
					tep_db_perform(TABLE_DEFINE_MAINPAGE, $sql_data_array);	
				}
			}
			break;
		
		default:
			break;
	}
}

// retrieve setting
$sett_sel_sql = "	SELECT mainpage_slider_content, mainpage_best_selling_image, footer_all_payment_image
					FROM " . TABLE_DEFINE_MAINPAGE . " 
					WHERE Id = '1' 
						AND language_id = '" . (int)$language_id . "' 
						AND geo_zone_id = '" . (int)$geo_zone_id . "'";
$sett_res_sql = tep_db_query($sett_sel_sql);
if ($select_row = tep_db_fetch_array($sett_res_sql)) {	
	$mainpage_slider_content = tep_not_empty($select_row['mainpage_slider_content']) ? unserialize($select_row['mainpage_slider_content']) : '';
	$mainpage_best_selling_image = tep_not_empty($select_row['mainpage_best_selling_image']) ? unserialize($select_row['mainpage_best_selling_image']) : '';
	$footer_all_payment_image = tep_not_empty($select_row['footer_all_payment_image']) ? unserialize($select_row['footer_all_payment_image']) : '';
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/php.packed.js"></script>
	<script language="javascript" src="includes/javascript/select_box.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
				</table>
			</td>
			
			<td valign="top">
				<?php echo tep_draw_form('define_mainpage_form', FILENAME_DEFINE_MAINPAGE); ?>
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="pageHeading"><?=HEADING_TITLE?></td>
					</tr>
					
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
					</tr>
					
					<tr>
						<td>
							<!-- Start Main Page Template -->
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<?php
									$get_geo_zone_sql = "	SELECT geo_zone_id, geo_zone_name 
															FROM " . TABLE_GEO_ZONES . " 
															WHERE  geo_zone_type = '" . DEFAULT_GEO_TYPE . "' 
															ORDER BY geo_zone_name";
									$get_geo_zone_result = tep_db_query($get_geo_zone_sql);
									while ($get_geo_zone_row = tep_db_fetch_array($get_geo_zone_result)) {
										$geo_zone_array[] = array (
																'id' => $get_geo_zone_row['geo_zone_id'],
																'text' => $get_geo_zone_row['geo_zone_name']
															);
									}
									
									$languages_select_query = "	SELECT * FROM ".TABLE_LANGUAGES." ORDER BY sort_order";
									$languages_select_result = tep_db_query($languages_select_query);
									while ($languages_select_row = tep_db_fetch_array($languages_select_result)) {
										$languages_array[] = array (
																'id' => $languages_select_row['languages_id'],
																'text' => $languages_select_row['name']
															);
									}
								?>
								<tr>
									<td class="main" width="10%"><b><?=ENTRY_ZONES?></b></td>
									<td class="main">
										<?=tep_draw_pull_down_menu('geo_zone_id', $geo_zone_array, $geo_zone_id, 'id="geo_zone_id" onChange="document.getElementById(\'action\').value=\'\';this.form.submit();"') ?>
									</td>
								</tr>
								<tr>
									<td class="main" width="10%"><b><?=ENTRY_LANGUAGE?></b></td>
									<td class="main">
										<?=tep_draw_pull_down_menu('language_id', $languages_array, $language_id, 'id="language_id" onChange="document.getElementById(\'action\').value=\'\';this.form.submit();"') ?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
					</tr>
					
					<!-- Start Slider Content -->
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle" colspan="2"><?=HEADER_SLIDER_CONTENT ?></td>
								</tr>
								<tr>
									<td class="formArea">
										<input type="button" name="add_slider_btn" value="Add Slider" class="inputButton" onClick="javascript:add_slider();" />
										<?=tep_draw_hidden_field('slider_row_counter', tep_not_empty($mainpage_slider_content) ? count($mainpage_slider_content) : 0, 'id="slider_row_counter"')?>
										<br />
										<table id="slider_content" border="0" width="100%" cellspacing="0" cellpadding="2">
											<?php
												if (tep_not_empty($mainpage_slider_content)) {
													foreach ($mainpage_slider_content as $num => $val) {
														$cnt = $num + 1;
											?>
														<tr id="slider_content_row_<?=$cnt;?>">
															<td>
																<table border="0" width="80%" cellspacing="0" cellpadding="2">
																	<tr>
																		<td class="dottedLine" colspan="2">&nbsp;</td>
																	</tr>
																	<tr>
																		<td class="main" colspan="2" align="right">
																			<a href="javascript: del_slider(<?=$cnt;?>);"><img src="images/icons/delete.gif" border="0" alt="<?=ENTRY_DELETE_SLIDER;?>" title="<?=ENTRY_DELETE_SLIDER;?>" align="bottom"></a>
																		</td>
																	</tr>
																	<tr>
																		<td class="main" width="180px"><?=ENTRY_SLIDER_THUMBNAIL_IMAGE;?></td>
																		<td class="main"><?=tep_draw_input_field('slider_thumbnail_image[' . $cnt . ']', $val['slider_thumbnail_image'], ' id="slider_thumbnail_image_' . $cnt . '" size="100"'); ?></td>
																	</tr>
																	<tr>
																		<td class="main" width="180px"><?=ENTRY_SLIDER_BACKGROUND_IMAGE;?></td>
																		<td class="main"><?=tep_draw_input_field('slider_background_image[' . $cnt . ']', $val['slider_background_image'], ' id="slider_background_image_' . $cnt . '" size="100"'); ?></td>
																	</tr>
																	<tr>
																		<td class="main" width="180px"><?=ENTRY_SLIDER_IMAGE_URL;?></td>
																		<td class="main"><?=tep_draw_input_field('slider_image_url[' . $cnt . ']', $val['slider_image_url'], ' id="slider_image_url_' . $cnt . '" size="100"'); ?></td>
																	</tr>
																	<tr>
																		<td class="main" width="180px"><?=ENTRY_SLIDER_CAPTION;?></td>
																		<td class="main"><?=tep_draw_input_field('slider_caption[' . $cnt . ']', $val['slider_caption'], ' id="slider_caption_' . $cnt . '" size="50"'); ?></td>
																	</tr>
																</table>
															</td>
														</tr>
											<?php
													}
												}
											?>
										</table>
									</td>
							</table>
						</td>
					</tr>
					<!-- // End of Slider Content -->
					
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					
					<!-- Start Best Selling Product Image Column -->
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle" colspan="2"><?=HEADER_BESTSELLING_CONTENT_COLUMNS ?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td>&nbsp;</td>
												<td class="main"><?=ENTRY_IMAGE_SOURCE;?></td>
											</tr>
											<?php
												for ($i=0; MAX_BEST_SELLING_IMAGE > $i; $i++) {
													$cols = $i + 1;
											?>
													<tr>
														<th class="main" width="180px" align="left" valign="top"><?=HEADER_COLUMNS." # $cols" ?></th>
														<td><?=tep_draw_textarea_field("img_src[$c]", '', '100', '5', $mainpage_best_selling_image[$i]);?></td>
													</tr>
											<?php
												}
											?>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<!-- // End Best Selling Product Column -->
					
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					
					<!-- Start All Payment Bar Image Column -->
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle" colspan="2"><?=HEADER_ALL_PAYMENT_COLUMNS ?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td>&nbsp;</td>
												<td class="main"><?=ENTRY_IMAGE_SOURCE;?></td>
											</tr>
											<?php
												for ($i=0; MAX_ALL_PAYMENT_IMAGE > $i; $i++) {
													$cols = $i + 1;
											?>
													<tr>
														<th class="main" width="180px" align="left" valign="top"><?=HEADER_COLUMNS." # $cols" ?></th>
														<td><?=tep_draw_textarea_field("payment_img_src[$c]", '', '100', '5', $footer_all_payment_image[$i]);?></td>
													</tr>
											<?php
												}
											?>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<!-- // End All Payment Bar Image Column -->
					
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
					</tr>
					
					<tr>
						<td align="right">
							<?=tep_draw_hidden_field('clone_geo_zone_id', '', ' id="clone_geo_zone_id"')?>
							<?=tep_draw_hidden_field('action', '', ' id="action"')?>
							<input type="submit" name="action" value="Update" class="inputButton" onClick="javascript:pre_submit();" />&nbsp;
							<a href="<?=tep_href_link(FILENAME_DEFINE_MAINPAGE);?>" style="text-decoration:none;"><input type="button" name="cancel" value="Cancel" class="inputButton" /></a>
						</td>
					</tr>
					
					</table>
					</form>
				</td>
			</tr>
			
			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
		</table>
		
		<!-- footer //-->
		<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
		<!-- footer_eof //-->
		
		<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
	</body>
</html>

<script type="text/javascript"><!--
	function pre_submit () {
		var display_html = '<table border="0" width="350" cellpadding="0" cellspacing="0"><tr><td>';
		display_html += '<div style="max-height:200px;overflow:auto;">';
		display_html += '<table border="0" width="100%" cellpadding="0" cellspacing="0">';
		display_html += '<tr>';
		<?php
			$geo_zone_cnt = 0;
			foreach ($geo_zone_array as $zone) :
				if ($zone['id'] == $geo_zone_id) continue;
				$geo_zone_cnt ++;
				if($geo_zone_cnt % 2) :
		?>
					display_html += "</tr><tr>";
		<?php		
				endif;
		?>
			display_html += '<td><input type="checkbox" class="zone_id_checkbox" value="<?=$zone['id']?>"><?=$zone['text']?></td>';
		<?php
			endforeach;
		?>
		display_html += "</tr>";
		display_html += "</table>";
		display_html += "</div>";
		display_html += "</td></tr></table>";
		
		jquery_confirm_box(display_html, 2, 0, 'Optional: Apply changes to the following as well:');
		//jQuery("#jconfirm_cancel").text('Skip');
		
		
		jQuery('#jconfirm_submit').click(function() {
			var store_id = '';
			var zone_id_cnt = 0;
			jQuery(".zone_id_checkbox").each(function(i){
			    if(jQuery(this).attr("checked") == true) {
			    	if (zone_id_cnt != 0) {
			    		store_id += ',';
			    	}
			    	store_id += this.value;
			    	zone_id_cnt ++;
			    }
			});
			
			jQuery("#clone_geo_zone_id").val(store_id);
			document.getElementById('action').value = 'update';
			document.define_mainpage_form.submit();
		});
	}
	
	function add_slider() {
		var html = '';
		var row = eval(jQuery('#slider_row_counter').val() + '+' + 1);
		
		html += '<tr id="slider_content_row_' + row + '">';
		html += '	<td>';
		html += '		<table border="0" width="80%" cellspacing="0" cellpadding="2">';
		html += '			<tr>';
		html += '				<td colspan="2" class="dottedLine">&nbsp;</td>';
		html += '			</tr>';
		html += '			<tr>';
		html += '				<td class="main" colspan="2" align="right">';
		html += '					<a href="javascript: del_slider(' + row + ');"><img src="images/icons/delete.gif" border="0" alt="<?=ENTRY_DELETE_SLIDER;?>" title="<?=ENTRY_DELETE_SLIDER;?>" align="bottom"></a>';
		html += '				</td>';
		html += '			</tr>';
		html += '			<tr>';
		html += '				<td class="main" width="180px"><?=ENTRY_SLIDER_THUMBNAIL_IMAGE;?></td>';
		html += '				<td class="main"><input type="text" id="slider_thumbnail_image_' + row + '" name="slider_thumbnail_image[' + row + ']" size="100" /></td>';
		html += '			</tr>';
		html += '			<tr>';
		html += '				<td class="main" width="180px"><?=ENTRY_SLIDER_BACKGROUND_IMAGE;?></td>';
		html += '				<td class="main"><input type="text" id="slider_background_image_' + row + '" name="slider_background_image[' + row + ']" size="100" /></td>';
		html += '			</tr>';
		html += '			<tr>';
		html += '				<td class="main" width="180px"><?=ENTRY_SLIDER_IMAGE_URL;?></td>';
		html += '				<td class="main"><input type="text" id="slider_image_url_' + row + '" name="slider_image_url[' + row + ']" size="100" /></td>';
		html += '			</tr>';
		html += '			<tr>';
		html += '				<td class="main" width="180px"><?=ENTRY_SLIDER_CAPTION;?></td>';
		html += '				<td class="main"><input type="text" id="slider_caption_' + row + '" name="slider_caption[' + row + ']" size="50" /></td>';
		html += '			</tr>';
		html += '		</table>';
		html += '	</td>';
		html += '	<td align="right" valign="top">';
		html += '		';
		html += '	</td>';
		html += '</tr>';
		
		jQuery('#slider_content').append(html);
		jQuery('#slider_row_counter').val(row);
	}
	
	function del_slider(num) {
		if (!empty(num) && typeof num != 'undefined') {
			jQuery('#slider_content_row_' + num).remove();
		}
	}
--></script>