<?
require('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "update":
		if (isset($_POST['custom_products_type_child_id']) && isset($_POST['custom_products_content'])
			 && count($_POST['custom_products_content'])) {
			
			$delete_product_types_content_sql = "	DELETE FROM " . TABLE_DEFINE_PRODUCT_TYPE_PAGE . " 
													WHERE custom_products_type_child_id = " . (int)$_POST['custom_products_type_child_id'];
			tep_db_query($delete_product_types_content_sql);
			
			foreach ($_POST['custom_products_content'] as $lng_id => $content) {
				$sql_data_array = array('custom_products_type_child_id' => (int)$_POST['custom_products_type_child_id'],
										'language_id' => $lng_id,
										'Text' => tep_db_prepare_input($content));
				tep_db_perform(TABLE_DEFINE_PRODUCT_TYPE_PAGE, $sql_data_array);
			}
			break;
		}
	}
	tep_redirect(tep_href_link(FILENAME_DEFINE_PRODUCT_TYPE_PAGE, "custom_products_type_child_id=" . $_REQUEST['custom_products_type_child_id']));
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="JavaScript" src="includes/javascript/tiny_mce/tiny_mce.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" type="text/javascript">
		tinyMCE.init({
	//		mode : "exact",
			mode : "none",
			elements : "<?php echo $printelements; ?>",
			theme : "advanced",
			skin : "o2k7",
			verify_html : false,
			relative_urls : false,
			convert_urls : false,
			forced_root_block : false,
			force_p_newlines : false,
			verify_css_classes : false,
			nowrap : true,
			cleanup : false,
			fix_table_elements : false,
	
			plugins : "inlinepopups,safari,style,layer,table,advhr,advimage,advlink,insertdatetime,preview,searchreplace,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras",
			theme_advanced_buttons1_add : "fontselect,fontsizeselect",
			theme_advanced_buttons2_add_before: "cut,copy,paste,pastetext,separator",
			theme_advanced_buttons2_add : "preview,separator,forecolor,backcolor,separator,visualchars,iespell,advhr,separator,fullscreen",
			theme_advanced_buttons3_add_before : "tablecontrols,separator",
			theme_advanced_buttons3_add : "insertlayer,moveforward,movebackward,absolute,separator,styleprops",
			theme_advanced_buttons4 : "",
			theme_advanced_toolbar_location : "top",
			theme_advanced_toolbar_align : "left",
			theme_advanced_statusbar_location : "bottom",
			theme_advanced_resizing : true,
	
	//		content_css : "/images/css/wow_stylesheet.php",
	//		extended_valid_elements : "hr[class|width|size|noshade],font[face|size|color|style],span[class|align|style]",
			nonbreaking_force_tab : true,
			apply_source_formatting : true,
			relative_urls : false,
			remove_script_host : false
		});
		jQuery.noConflict();
	</script>
</head>
<?

//get product type
$products_type_select_sql = "	SELECT cl.custom_products_type_child_id, cl.custom_products_type_child_name 
								FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . " as cl
								INNER JOIN " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " as c 
									ON c.custom_products_type_child_id=cl.custom_products_type_child_id
								WHERE cl.languages_id = " . $_SESSION['languages_id'] . "
								ORDER BY c.sort_order";
$products_type_result_sql = tep_db_query($products_type_select_sql);
$products_type_array = array();
$products_type_array[] = array("id"=>"","text"=>"[".PULL_DOWN_DEFAULT."]");
while ($products_type_row = tep_db_fetch_array($products_type_result_sql)) {
	$products_type_array[] = array(	"id"=>$products_type_row['custom_products_type_child_id'],
									"text"=>$products_type_row['custom_products_type_child_name']);
}

?>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
						</tr>
						<tr>
							<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
						</tr>
						<tr>
							<td>
								<table border="0" width="100%" cellspacing="0" cellpadding="0">
									<tr>
										<td class="main" valign="top" width="150px"><?=ENTRY_CUSTOM_PRODUCTS_TYPE?></td>
										<td width="*%"><?=tep_draw_form('custom_products_type_frm', FILENAME_DEFINE_PRODUCT_TYPE_PAGE, '', 'post') . 
												tep_draw_pull_down_menu("custom_products_type_child_id", $products_type_array, (isset($_REQUEST['custom_products_type_child_id'])?(int)$_REQUEST['custom_products_type_child_id']:'') , ' id="custom_products_type_child_id" onchange="document.custom_products_type_frm.submit()"')?>
											</form>
										</td>
									</tr>
<?
	if (isset($_REQUEST['custom_products_type_child_id']) && tep_not_null($_REQUEST['custom_products_type_child_id'])) {
		$product_type_page_select_sql = "	SELECT text, language_id
											FROM " . TABLE_DEFINE_PRODUCT_TYPE_PAGE . " 
											WHERE custom_products_type_child_id = '".(int)$_REQUEST['custom_products_type_child_id']."'";
		$product_type_page_result_sql = tep_db_query($product_type_page_select_sql);
		$product_type_page_array = array();
		while ($product_type_page_row = tep_db_fetch_array($product_type_page_result_sql)) {
			$product_type_page_array[$product_type_page_row['language_id']] = $product_type_page_row['text'];
		}
?>
									<tr>
										<td class="main" valign="top" colspan='2'>
											<?=tep_draw_form('custom_products_content_frm', FILENAME_DEFINE_PRODUCT_TYPE_PAGE, 'action=update', 'post') . 
												tep_draw_hidden_field('custom_products_type_child_id',(int)$_REQUEST['custom_products_type_child_id'])?>
											<table width="100%">
<?
	$language_array = array();
	$language_select_sql = "SELECT * 
							FROM ".TABLE_LANGUAGES." 
							ORDER BY sort_order ASC";
	$language_result_sql = tep_db_query($language_select_sql);
	while ($language_row = tep_db_fetch_array($language_result_sql)) {
		$language_array[] = array( 	'code' => $language_row['code'],
									'languages_id' => $language_row['languages_id'],
									'name' => $language_row['name'],
									'default' => (DEFAULT_LANGUAGE == $language_row['code']) ? "default" : "");
	}
	
	$firstlanguagetextarea = 0;
	foreach ($language_array as $language_row) {
		if ($firstlanguagetextarea) {
			print "<br><br>";
		}
		$content_language_id = $language_row['languages_id'];
?>
											
												<tr>
													<td class="main">
														<table cellpadding=0 border=0 width=100%>
															<tr>
																<td><b><?php echo $language_row['name']; ?></b></td>
																<td align=right>
																	<div id="custom_products_content_<?=$language_row['languages_id']?>_start">
																		<a href="javascript:starteditor('custom_products_content_<?=$language_row['languages_id']?>')">HTML Editor</a>
																	</div>
																	<div style="display: none" id="custom_products_content_<?=$language_row['languages_id']?>_stop">
																		<a href="javascript:stopeditor('custom_products_content_<?=$language_row['languages_id']?>')">Text Editor</a>
																	</div>
																</td>
															</tr>
														</table>
														<?=tep_draw_textarea_field('custom_products_content['.$language_row['languages_id']."]" , 'soft', '40', '6', htmlspecialchars($product_type_page_array[$content_language_id]) , ' style="width: 100%" id="custom_products_content_'.$language_row['languages_id'].'" ')?>
													</td>
												</tr>
												<tr>
													<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
												</tr>
<?	
	}
?>
												<tr>
													<td colspan="2"><?=tep_image_button('button_update.gif', IMAGE_INSERT, 'onClick="document.custom_products_content_frm.submit();" style="cursor: pointer;"') . ' ' . '<a href="' . tep_href_link(FILENAME_DEFINE_PRODUCT_TYPE_PAGE) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
												</tr>
											</table>
											</form>
										</td>
									</tr>
<?
	}
?>
								</table>
							</td>
						</tr>
					</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>