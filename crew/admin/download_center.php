<?php
/*
  $Id: download_center.php,v 1.16 2015/05/06 07:44:46 weichen Exp $

  Developer: CacPhy Foong Kee Peng
  Copyright (c) 2004 SKC Ventrue

  Released under the GNU General Public License
 */
require('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

$report_permission_array = array(
    'root_product_sales' => 'DOWNLOAD_ROOT_PRODUCT_SALES',
    'pg_country_sales' => 'DOWNLOAD_PG_COUNTRY_SALES',
    'anb_undelivered_report' => 'ANB_UNDELIVERED_REPORT',
    'sc_checkout' => 'DOWNLOAD_CENTER_SC_CHECKOUT',
    'root_product_sales_gmv' => 'DOWNLOAD_ROOT_PRODUCT_SALES'
);

switch ($action) {
    case 'list_file':
        $file_type_download_permission = false;

        if (isset($_POST['report_type']) && tep_not_null($_POST['report_type'])) {
            $now_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d"), date("Y")));

            switch ($_POST['report_type']) {
                case 'cdkey_sales':
                    $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CDKEY_SALES');
                    $download_path = '30days/cdkey_sales/';
                    $clean_file = true;
                    break;

                case 'customer_purchase_stat':
                    $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CUSTOMER_PURCHASE_STAT');
                    $download_path = '30days/customer_purchase_stat/';
                    $clean_file = true;
                    break;

                case 'crt_kpi_report':
                    $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CRT_KPI');
                    $download_path = '30days/crt_kpi_report/';
                    $clean_file = true;
                    break;

                case 'page_view':
                    $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_PAGE_VIEW_MODULE');
                    $download_path = '30days/pageview/';
                    $clean_file = true;
                    break;

                case 'sc_checkout':
                    $s3_file_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_SC_CHECKOUT');
                    $download_path = 'monthly/sc_checkout/';
                    $clean_file = true;

                    if ($s3_file_download_permission) {
                        $s3_bucket = 'BUCKET_DATA';
                        $s3_filepath = 'report/sc_checkout/';
                        $s3_file_prefix = '';

                        $aws_obj = new ogm_amazon_ws();
                        $aws_obj->set_bucket_key($s3_bucket);
                        $aws_obj->set_filepath($s3_filepath);

                        $display_files = '';
                        $num_record = 0;
                        $max_file_date = date('YmdHis', mktime(0, 0, 0, date('m') - 6, date('d'), date('Y')));

                        if ($aws_obj->is_aws_s3_enabled()) {
                            $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath . $s3_file_prefix)));

                            $num_record = count($s3_files);
                            for ($i = 0; $num_record > $i; $i++) {
                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);

                                $fileinfo = pathinfo(str_replace($s3_file_prefix, '', $filename));
                                $fileform = $fileinfo['filename'];
                                $filedate = $fileinfo['basename'];

                                $display_name = $fileinfo['filename'];

                                $display_files .= '	<tr>
                                    <td class="dataTableContent" align="right">' . $display_name . ':</td>
                                    <td class="dataTableContent" align="left">' .
                                        tep_draw_form('download_' . $fileform . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action', 'filename')) . 'action=download_file&filename=' . $filename, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                    </td>
                                </tr>';

                            }
                        }
                    }
                    break;

                case 'anb_undelivered_report':
                    $s3_file_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'ANB_UNDELIVERED_REPORT');

                    if ($s3_file_download_permission) {
                        $s3_bucket = 'BUCKET_DATA';
                        $s3_filepath = 'report/';
                        $s3_file_prefix = 'anb_undelivered_';

                        $aws_obj = new ogm_amazon_ws();
                        $aws_obj->set_bucket_key($s3_bucket);
                        $aws_obj->set_storage('STORAGE_STANDARD');
                        $aws_obj->set_filepath($s3_filepath);

                        $display_files = '';
                        $num_record = 0;
                        $max_file_date = date('YmdHis', mktime(0, 0, 0, date('m'), date('d'), date('Y') - 1));

                        if ($aws_obj->is_aws_s3_enabled()) {
                            $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath . $s3_file_prefix)));

                            $num_record = count($s3_files);
                            for ($i = 0; $num_record > $i; $i++) {
                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);

                                $fileinfo = pathinfo(str_replace($s3_file_prefix, '', $filename));
                                $fileform = $fileinfo['filename'];
                                $filedate = $fileinfo['basename'];

                                $yrs = substr($filedate, 0, 4);
                                $mth = substr($filedate, 4, 2);
                                $day = substr($filedate, 6, 2);
                                $hrs = substr($filedate, 8, 2);
                                $min = substr($filedate, 10, 2);
                                $sec = substr($filedate, 12, 2);

                                $display_name = $yrs . '-' . $mth . '-' . $day . ' ' . $hrs . ':' . $min . ':' . $sec;

                                if ($max_file_date > $filedate) {
                                    $aws_obj->delete_file($filename);
                                } else {
                                    $display_files .= '	<tr>
                                        <td class="dataTableContent" align="right">' . $display_name . ':</td>
                                        <td class="dataTableContent" align="left">' .
                                            tep_draw_form('download_' . $fileform . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $filename, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                        </td>
                                    </tr>';
                                }
                            }
                        }
                    }
                    break;

                case 'pg_country_sales':
                    $s3_file_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_PG_COUNTRY_SALES');

                    if ($s3_file_download_permission) {
                        $s3_bucket = 'BUCKET_DATA';
                        $s3_filepath = 'report/';
                        $s3_file_prefix = 'pg_country_sales_';

                        $aws_obj = new ogm_amazon_ws();
                        $aws_obj->set_bucket_key($s3_bucket);
                        $aws_obj->set_filepath($s3_filepath);

                        $display_files = '';
                        $num_record = 0;

                        if ($aws_obj->is_aws_s3_enabled()) {
                            $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath . $s3_file_prefix)));

                            $num_record = count($s3_files);
                            for ($i = 0; $num_record > $i; $i++) {
                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);

                                $fileinfo = pathinfo(str_replace($s3_file_prefix, '', $filename));
                                $fileform = $fileinfo['filename'];

                                $display_name = $fileform;

                                $display_files .= '	<tr>
                                    <td class="dataTableContent" align="right">' . $display_name . ':</td>
                                    <td class="dataTableContent" align="left">' .
                                        tep_draw_form('download_' . $fileform . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $filename, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                    </td>
                                </tr>';
                            }
                        }
                    }
                    break;

                case 'root_product_sales':
                    $s3_file_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, $report_permission_array[$_POST['report_type']]);

                    if ($s3_file_download_permission) {
                        $s3_bucket = 'BUCKET_DATA';
                        $s3_filepath = 'report/';
                        $s3_file_prefix = $_POST['report_type'] . '_';

                        $aws_obj = new ogm_amazon_ws();
                        $aws_obj->set_bucket_key($s3_bucket);
                        $aws_obj->set_filepath($s3_filepath);

                        $display_files = '';
                        $num_record = 0;
                        $max_file_date = date('YmdHis', mktime(0, 0, 0, date('m') - 6, date('d'), date('Y')));

                        if ($aws_obj->is_aws_s3_enabled()) {
                            $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath . $s3_file_prefix)));

                            $num_record = count($s3_files);
                            for ($i = 0; $num_record > $i; $i++) {
                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);

                                $fileinfo = pathinfo(str_replace($s3_file_prefix, '', $filename));
                                $fileform = $fileinfo['filename'];
                                list($useless, $filedate) = explode('_', $fileinfo['basename']);

                                $display_name = $fileinfo['filename'];

                                $display_files .= '	<tr>
                                    <td class="dataTableContent" align="right">' . $display_name . ':</td>
                                    <td class="dataTableContent" align="left">' .
                                        tep_draw_form('download_' . $fileform . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $filename, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                    </td>
                                </tr>';
                            }
                        }
                    }
                    break;

                case 'root_product_sales_gmv':
                    $s3_file_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, $report_permission_array[$_POST['report_type']]);

                    if ($s3_file_download_permission) {
                        $s3_bucket = 'BUCKET_DATA';
                        $s3_filepath = 'report/';
                        $s3_file_prefix = $_POST['report_type'] . '_';

                        $aws_obj = new ogm_amazon_ws();
                        $aws_obj->set_bucket_key($s3_bucket);
                        $aws_obj->set_filepath($s3_filepath);

                        $display_files = '';
                        $num_record = 0;

                        if ($aws_obj->is_aws_s3_enabled()) {
                            $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath . $s3_file_prefix)));

                            $num_record = count($s3_files);
                            for ($i = 0; $num_record > $i; $i++) {
                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);

                                $display_files .= '	<tr>
                                    <td class="dataTableContent" align="right">' . $filename . ':</td>
                                    <td class="dataTableContent" align="left">' .
                                        tep_draw_form('download_' . $fileform . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $filename, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                    </td>
                                </tr>';
                            }
                        }
                    }
                    break;

                default:
                    $file_type_download_permission = false;
                    $download_path = '';
                    $clean_file = false;
                    break;
            }

            $base_dir = 'download/';

            if (($file_type_download_permission)) {
                $dir = $base_dir . $download_path;

                if (is_dir($dir)) {
                    if ($dh = opendir($dir)) {
                        $file_name_arr = array();
                        $display_files = '';
                        $num_record = 0;

                        while (($file = readdir($dh)) !== false) {
                            if (is_file($dir . $file)) {
                                list($filename_section, $ext_section) = explode('.', $file);

                                switch ($_POST['report_type']) {
                                    default:
                                        $yr = substr($filename_section, 0, 4);
                                        $mth = substr($filename_section, 4, 2);
                                        $day = substr($filename_section, 6, 2);
                                        $hr = substr($filename_section, 8, 2);
                                        $min = substr($filename_section, 10, 2);
                                        $sec = substr($filename_section, 12, 2);

                                        $created_date = date("Y-m-d H:i:s", mktime($hr, $min, $sec, $mth, $day, $yr));
                                        $file_name_arr[$created_date] = $file;
                                        break;
                                }
                            }
                        }
                        krsort($file_name_arr);
                        if (count($file_name_arr) > 0) {
                            foreach ($file_name_arr as $file_date => $file_name) {

                                $file_type = substr(strrchr($file_name, '.'), 1);
                                if ($file_type == 'csv' || $file_type == 'html') {
                                    if ($clean_file) {
                                        $file_created_date_arr = explode("-", $file_date);

                                        if ($_POST['report_type'] == 'sc_checkout') {
                                            $file_expired_date = date("Y-m-d", mktime(0, 0, 0, $file_created_date_arr[1] + 3, $file_created_date_arr[2], $file_created_date_arr[0]));
                                        } else {
                                            $file_expired_date = date("Y-m-d", mktime(0, 0, 0, $file_created_date_arr[1], $file_created_date_arr[2] + 45, $file_created_date_arr[0]));
                                        }

                                        if ($file_expired_date > $now_date) {
                                            $num_record ++;
                                            $display_files .= '<tr>
                                                <td class="dataTableContent" align="right">' . $file_date . ':</td>
                                                <td class="dataTableContent" align="left">' .
                                                    tep_draw_form('download_' . $file_name . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $file_name, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                                </td>
                                            </tr>';
                                        } else {
                                            unlink($dir . $file_name);
                                        }
                                    } else {
                                        $num_record ++;

                                        $display_files .= '<tr>
                                            <td class="dataTableContent" align="right">' . $file_date . ':</td>
                                            <td class="dataTableContent" align="left">' .
                                                tep_draw_form('download_' . $file_name . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $file_name, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                            </td>
                                        </tr>';
                                    }
                                } else if ($report_type == 'page_view') {
                                    $num_record ++;

                                    $display_files .= '<tr>
                                        <td class="dataTableContent" align="right">' . $file_date . ':</td>
                                        <td class="dataTableContent" align="left">' .
                                            tep_draw_form('download_' . $file_name . '_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=download_file&filename=' . $file_name, 'post') . tep_submit_button('Download', 'Download', '', 'InputButton') . tep_draw_hidden_field('report_type', $_POST['report_type']) . '</form>
                                        </td>
                                    </tr>';
                                }
                            }
                        }
                        if ($num_record == 0) {
                            $display_files .= '<tr class="dataTableRowSelected"><td colspan="2" align="center">' . TEXT_NO_RECORD . '</td></tr>';
                        }
                        closedir($dh);
                    }
                }
            }
        }
        break;
    case 'download_file':
        $filename = $_REQUEST['filename'];

        $report_type = '';
        $file_type_download_permission = false;

        if (isset($_REQUEST['report_type']) && tep_not_null($_REQUEST['report_type'])) {
            $report_type = $_REQUEST['report_type'];
        }

        switch ($report_type) {
            case 'cdkey_sales';
                $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CDKEY_SALES');
                $download_path = 'download/30days/cdkey_sales/';
                break;

            case 'customer_purchase_stat':
                $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CUSTOMER_PURCHASE_STAT');
                $download_path = 'download/30days/customer_purchase_stat/';
                break;

            case 'crt_kpi_report':
                $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_CRT_KPI');
                $download_path = 'download/30days/crt_kpi_report/';
                break;

            case 'page_view':
                $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, 'DOWNLOAD_CENTER_PAGE_VIEW_MODULE');
                $download_path = 'download/30days/pageview/';
                break;

            case 'anb_undelivered_report':
            case 'pg_country_sales':
            case 'root_product_sales':
            case 'sc_checkout':
            case 'root_product_sales_gmv':
                $file_type_download_permission = tep_admin_files_actions(FILENAME_DOWNLOAD_CENTER, $report_permission_array[$report_type]);

                $s3_bucket = 'BUCKET_DATA';
                $s3_filepath = ($report_type == 'sc_checkout' ? 'report/' . $report_type . '/' : 'report/');

                $aws_obj = new ogm_amazon_ws();
                $aws_obj->set_bucket_key($s3_bucket);
                $aws_obj->set_filepath($s3_filepath);
                break;
        }

        if ($file_type_download_permission) {
            $export_file_name = $filename;

            $mime_type = 'text/x-csv';
            // Download
            header('Content-Type: ' . $mime_type);
            header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            // IE need specific headers
            if (PMA_USR_BROWSER_AGENT == 'IE') {
                header('Content-Disposition: attachment; filename="' . $export_file_name . '"');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');
            } else {
                header('Content-Disposition: attachment; filename="' . $export_file_name . '"');
                header('Pragma: no-cache');
            }

            if (in_array($report_type, array('anb_undelivered_report', 'pg_country_sales', 'root_product_sales', 'sc_checkout', 'root_product_sales_gmv'))) {
                if ($aws_obj->is_aws_s3_enabled()) {
                    $file_content = $aws_obj->get_file($filename);
                    echo $file_content->body;
                }
            } else {
                $file_location = $download_path . $filename;
                readfile($file_location);
            }
            exit();
        }
        break;
}

$download_files_arr = array();
$download_files_arr[] = array("id" => '', "text" => PULL_DOWN_DEFAULT);
$download_files_arr[] = array("id" => "cdkey_sales", "text" => TEXT_DOWNLOAD_REPORT_CDKEY_SALES);
$download_files_arr[] = array("id" => "customer_purchase_stat", "text" => TEXT_DOWNLOAD_REPORT_CUSTOMER_PURCHASE_STAT);
$download_files_arr[] = array("id" => "crt_kpi_report", "text" => TEXT_DOWNLOAD_REPORT_CRT_KPI_REPORT);
$download_files_arr[] = array("id" => "page_view", "text" => TEXT_DOWNLOAD_PAGE_VIEW_MODULE);
$download_files_arr[] = array("id" => "sc_checkout", "text" => TEXT_DOWNLOAD_REPORT_SC_CHECKOUT);
$download_files_arr[] = array("id" => "anb_undelivered_report", "text" => TEXT_DOWNLOAD_REPORT_ANB_UNDELIVERED_REPORT);
$download_files_arr[] = array("id" => "pg_country_sales", "text" => TEXT_DOWNLOAD_REPORT_PG_COUNTRY_SALES);
$download_files_arr[] = array("id" => "root_product_sales", "text" => TEXT_DOWNLOAD_REPORT_ROOT_PRODUCT_SALES);
$download_files_arr[] = array("id" => "root_product_sales_gmv", "text" => TEXT_DOWNLOAD_REPORT_ROOT_PRODUCT_SALES . " (GMV)");
?>
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?= BOX_WIDTH ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="25%" cellspacing="0" cellpadding="2">
                                    <tr class="dataTableRowSelected">
                                        <td class="dataTableContent" align="right" style="padding:10px;"><?= TEXT_REPORT ?>:</td>
                                        <td class="dataTableContent" align="left" style="padding:10px;">
                                            <?
                                            echo tep_draw_form('download_form', FILENAME_DOWNLOAD_CENTER, tep_get_all_get_params(array('action')) . 'action=list_file', 'post');
                                            echo tep_draw_pull_down_menu('report_type', $download_files_arr, '', ' onChange="this.form.submit();"');
                                            echo "</form>";
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2">
                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <?= $display_files ?>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- body_eof //-->
        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
    <body>
    </body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>