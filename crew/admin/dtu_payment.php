<?php
/*
  Developer: <PERSON><PERSON>ri
  Copyright (c) 2017 DPSB
 */

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'publishers.php');
require_once(DIR_WS_CLASSES . 'dtu_payment.php');
require_once('pear/Date.php');

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT);
}

define('DISPLAY_PRICE_DECIMAL', 4);

$allow_create_dtu_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_NEW_PO');
$allow_process_dtu_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_PROCESS_PO');
$allow_dtu_cancel_pending_receive_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_CANCEL_PENDING_RECEIVE');
$allow_verify_dtu_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_VERIFY_PO');
$allow_add_dtu_remark_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_ADD_REMARK');
$allow_view_dtu_remark_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_VIEW_REMARK');
$allow_dtu_make_payment_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_MAKE_PAYMENT');

if (isset($_REQUEST['subaction'])) {
    switch ($_REQUEST['subaction']) {
        case 'create_blank_dtu':
            if (!$allow_create_dtu_permission) {
                $messageStack->add_session(ERROR_DTU_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'add_dtu_cb':
            if (!$allow_create_dtu_permission) {
                $messageStack->add_session(ERROR_DTU_FORM_CREATE_PERMISSION, 'error');
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
        case 'create_dtu':
            $error = false;

            if ($allow_create_dtu_permission) {
                if (!isset($_REQUEST['low_stock_batch'])) {
                    $messageStack->add_session(ERROR_DTU_FORM_EMPTY_PRODUCTS, 'error');
                    $error = true;
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_DTU_FORM_CREATE_PERMISSION, 'error');
            }

            if ($error) {
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, 'page=' . $_REQUEST['page']));
            }
            break;
            
    }
}

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$dtu_publisher = new publishers();
$edit_dtu_obj = new dtu_payment($_SESSION['login_id'], $_SESSION['login_email_address']);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;

// Back button from preview
if (isset($_REQUEST['BackBtn'])) {
    if ($subaction == "insert_dtu_product") {
        $_REQUEST['subaction'] = "create_dtu_product";
        $_GET['subaction'] = "create_dtu_product";
        $subaction = "create_dtu_product";
    } else {
        tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id')) . '&subaction=show_dtu_list'));
    }
}

// Back button from calculate dtu
if (isset($_REQUEST['BackBtnToList'])) {
    if ($subaction == "preview_dtu") {
        $_REQUEST['subaction'] = "create_dtu";
        $_GET['subaction'] = "create_dtu";
        $subaction = "create_dtu";
    } else {
        tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id')) . '&subaction=show_dtu_list'));
    }
}

// DTU preview CB
if (isset($_REQUEST['dtuStatusButton'])) {
    if ($subaction == "calculate_dtu") {
        $_REQUEST['subaction'] = "preview_dtu_status";
        $_GET['subaction'] = "preview_dtu_status";
        $subaction = "preview_dtu_status";
    } else {
        tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id')) . '&subaction=show_dtu_list'));
    }
}

// Back button from preview DTU CB
if (isset($_REQUEST['BackBtnCB'])) {
    if ($subaction == "insert_dtu_product") {
        $_REQUEST['subaction'] = "add_dtu_cb";
        $_GET['subaction'] = "add_dtu_cb";
        $subaction = "add_dtu_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id')) . '&subaction=show_dtu_list'));
    }
}

// Submit button from preview DTU CB
if (isset($_REQUEST['submitDTUCB'])) {
    if ($subaction == "insert_dtu_product") {
        $_REQUEST['subaction'] = "insert_dtu_cb";
        $_GET['subaction'] = "insert_dtu_cb";
        $subaction = "insert_dtu_cb";
    } else {
        tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id')) . '&subaction=show_dtu_list'));
    }
}

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case "preview_dtu":
            if ($allow_create_dtu_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier']) || (isset($_REQUEST['dtu_supplier']) && !tep_not_null($_REQUEST['dtu_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier_payment']) || (isset($_REQUEST['dtu_supplier_payment']) && !tep_not_null($_REQUEST['dtu_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['dtu_currency']) || (isset($_REQUEST['dtu_currency']) && !tep_not_null($_REQUEST['dtu_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_delivery_address']) || (isset($_REQUEST['dtu_delivery_address']) && !tep_not_null($_REQUEST['dtu_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['dtu_items_prod_id']) || (isset($_REQUEST['dtu_items_prod_id']) && count($_REQUEST['dtu_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PRODUCTS, 'Error');
                }

                if (isset($_REQUEST['dtu_items_prod_id'])) {
                    $prod_ids = $_REQUEST['dtu_items_prod_id'];
                    foreach ($prod_ids as $prd_id) {

                        if (!isset($_REQUEST['dtu_item_qty_' . $prd_id]) || (isset($_REQUEST['dtu_item_qty_' . $prd_id]) && $_REQUEST['dtu_item_qty_' . $prd_id] == 0)) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_DTU_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
                        }

                        if (!isset($_REQUEST['unit_price_' . $prd_id]) || (isset($_REQUEST['unit_price_' . $prd_id]) && !is_numeric($_REQUEST['unit_price_' . $prd_id]))) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_DTU_FORM_EMPTY_STOCK_PRICE, $prd_id), 'Error');
                        }
                    }
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_dtu_product";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_dtu_product";
                    $subaction = "insert_dtu_product";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_DTU_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "calculate_dtu":
            if ($allow_create_dtu_permission) {                
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier']) || (isset($_REQUEST['dtu_supplier']) && !tep_not_null($_REQUEST['dtu_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_SUPPLIER, 'Error');
                }
                
                $dtu_publisher_check = new publishers($_REQUEST['dtu_supplier']);
                $dtu_supplier_check_info = $dtu_publisher_check->get_publishers();
                $dtu_supplier_id = $dtu_supplier_check_info[$_REQUEST['dtu_supplier']]['publishers_supplier_id'];
                $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));
                if ($supplier_info['payment_type']!='d') {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_SUPPLIER_TYPE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier_payment']) || (isset($_REQUEST['dtu_supplier_payment']) && !tep_not_null($_REQUEST['dtu_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['dtu_currency']) || (isset($_REQUEST['dtu_currency']) && !tep_not_null($_REQUEST['dtu_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_delivery_address']) || (isset($_REQUEST['dtu_delivery_address']) && !tep_not_null($_REQUEST['dtu_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                $dtu_pay_count = 0;
                if (!isset($_REQUEST['dtu_items_top_up_id']) || (isset($_REQUEST['dtu_items_top_up_id']) && count($_REQUEST['dtu_items_top_up_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PRODUCTS, 'Error');
                } else {
                    $dtu_publisher->set_dtu_temp_status_empty($_REQUEST['dtu_supplier']);
                    foreach ($_REQUEST['dtu_items_top_up_id'] as $dtu_topup_id) {
                        $dtu_publisher->set_dtu_temp_status($dtu_topup_id);
                        if ($_REQUEST['dtu_cb_status_'.$dtu_topup_id]=='0') {
                            $dtu_pay_count++;
                        }
                    }
                }
                
                if ($dtu_pay_count == 0) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CB_PRODUCTS, 'Error');
                }
                
                $dtu_publisher_calculate = new publishers($_REQUEST['dtu_supplier']);
                $dtu_supplier_info = $dtu_publisher_calculate->get_publishers();
                $dtu_supplier_calculate_id = $dtu_supplier_info[$_REQUEST['dtu_supplier']]['publishers_supplier_id'];
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($dtu_supplier_calculate_id, $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_dtu";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_dtu";
                    $subaction = "insert_dtu";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_DTU_FORM_CREATE_PERMISSION, 'Error');
            }
            break;

        case "preview_dtu_status":
            if ($allow_create_dtu_permission) {             
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_START_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DTU_END_DATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier']) || (isset($_REQUEST['dtu_supplier']) && !tep_not_null($_REQUEST['dtu_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_SUPPLIER, 'Error');
                }
                
                $dtu_publisher_check = new publishers($_REQUEST['dtu_supplier']);
                $dtu_supplier_check_info = $dtu_publisher_check->get_publishers();
                $dtu_supplier_id = $dtu_supplier_check_info[$_REQUEST['dtu_supplier']]['publishers_supplier_id'];
                $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));
                if ($supplier_info['payment_type']!='d') {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_SUPPLIER_TYPE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier_payment']) || (isset($_REQUEST['dtu_supplier_payment']) && !tep_not_null($_REQUEST['dtu_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['dtu_currency']) || (isset($_REQUEST['dtu_currency']) && !tep_not_null($_REQUEST['dtu_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_delivery_address']) || (isset($_REQUEST['dtu_delivery_address']) && !tep_not_null($_REQUEST['dtu_delivery_address']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
                }

                if (!isset($_REQUEST['dtu_items_top_up_id']) || (isset($_REQUEST['dtu_items_top_up_id']) && count($_REQUEST['dtu_items_top_up_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_CB_FORM_EMPTY_PRODUCTS, 'Error');
                }
                
                $dtu_publisher_calculate = new publishers($_REQUEST['dtu_supplier']);
                $dtu_supplier_info = $dtu_publisher_calculate->get_publishers();
                $dtu_supplier_calculate_id = $dtu_supplier_info[$_REQUEST['dtu_supplier']]['publishers_supplier_id'];
                $po_locked = $po_suppliers->get_po_supplier_lock_validation($dtu_supplier_calculate_id, $_SESSION['login_id']);
                
                if (empty($po_locked)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_NOT_LOCKED_SUPPLIER, 'Error');
                } else if ($po_locked != $_SESSION['login_id']) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_LOCKED_SUPPLIER, 'Error');
                }

                if ($error) {
                    $_REQUEST['subaction'] = "insert_dtu";
                    $_REQUEST['preview_error'] = "1";
                    $_GET['subaction'] = "insert_dtu";
                    $subaction = "insert_dtu";
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_DTU_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
            
        case "insert_dtu_product":
            if ($allow_create_dtu_permission) {
                if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PO_DATE, 'Error');
                }
                
                if (!isset($_REQUEST['end_date']) || (isset($_REQUEST['end_date']) && !tep_not_null($_REQUEST['end_date']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PO_DATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier']) || (isset($_REQUEST['dtu_supplier']) && !tep_not_null($_REQUEST['dtu_supplier']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_SUPPLIER, 'Error');
                }

                if (!isset($_REQUEST['dtu_supplier_payment']) || (isset($_REQUEST['dtu_supplier_payment']) && !tep_not_null($_REQUEST['dtu_supplier_payment']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PAYMENT_METHOD, 'Error');
                }

                if (!isset($_REQUEST['dtu_currency']) || (isset($_REQUEST['dtu_currency']) && !tep_not_null($_REQUEST['dtu_currency']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY, 'Error');
                }

                if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
                }

                if (!isset($_REQUEST['dtu_items_prod_id']) || (isset($_REQUEST['dtu_items_prod_id']) && count($_REQUEST['dtu_items_prod_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PRODUCTS, 'Error');
                }

                if (isset($_REQUEST['dtu_items_prod_id'])) {
                    $prod_ids = $_REQUEST['dtu_items_prod_id'];
                    foreach ($prod_ids as $prd_id) {

                        if (!isset($_REQUEST['dtu_item_qty_' . $prd_id]) || (isset($_REQUEST['dtu_item_qty_' . $prd_id]) && $_REQUEST['dtu_item_qty_' . $prd_id] == 0)) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_DTU_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
                        }

                        if (!isset($_REQUEST['unit_price_' . $prd_id]) || (isset($_REQUEST['unit_price_' . $prd_id]) && !is_numeric($_REQUEST['unit_price_' . $prd_id]))) {
                            $error = true;
                            $messageStack->add(sprintf(ERROR_DTU_FORM_EMPTY_STOCK_PRICE, $prd_id), 'Error');
                        }
                    }
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_DTU_FORM_CREATE_PERMISSION, 'Error');
            }

            if (!$error) {
                $edit_dtu_obj->insert_new_po($_REQUEST, $messageStack);
            }
            
            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id'))));

            break;

        case "insert_dtu_cb":
            if ($allow_create_dtu_permission) {

                if (!isset($_REQUEST['dtu_items_top_up_id']) || (isset($_REQUEST['dtu_items_top_up_id']) && count($_REQUEST['dtu_items_top_up_id']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_DTU_FORM_EMPTY_PRODUCTS, 'Error');
                }

//                if (isset($_REQUEST['dtu_items_top_up_id'])) {
//                    $prod_ids = $_REQUEST['dtu_items_top_up_id'];
//                    foreach ($prod_ids as $prd_id) {
//
//                        if (!isset($_REQUEST['dtu_status_select_'.$prd_id]) || (isset($_REQUEST['dtu_status_select_'.$prd_id]) && $_REQUEST['dtu_status_select_'.$prd_id] == '0')) {
//                            $error = true;
//                            $messageStack->add(sprintf(ERROR_DTU_FORM_EMPTY_SELECT_STATUS, $prd_id), 'Error');
//                        }
//                    }
//                }
            } else {
                $error = true;
                $messageStack->add(ERROR_DTU_FORM_CREATE_PERMISSION, 'Error');
            }

            if (!$error) {
                $edit_dtu_obj->insert_new_cb($_REQUEST, $messageStack);
            }
            
            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'dtu_id'))));

            break;
            
        case 'approve_dtu':
            if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'PrintBtn') {
                ;
            } else {
                if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'CancelBtn') {
                    $edit_dtu_obj->cancel_dtu($_REQUEST, $messageStack);
                } else if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'ApproveBtn') {
                    $edit_dtu_obj->approve_dtu($_REQUEST, $messageStack);
                }
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            }
            break;

        case 'add_remark':
            if ($allow_add_dtu_remark_permission) {
                $edit_dtu_obj->dtu_add_remark($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_DTU_FORM_ADD_REMARK_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'refund_dtu':
            if (isset($_REQUEST['CompleteBtn'])) {
                $po_status = '2';
                $edit_dtu_obj->complete_dtu($_REQUEST, $messageStack, $po_status);
            }

            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'refund_dtu_cancel':
            if ($allow_dtu_cancel_pending_receive_permission) {
                $edit_dtu_obj->refund_unpaid($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_DTU_FORM_DTU_CANCEL_PENDING_RECEIVE_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'rollback':
            if (isset($_REQUEST['RollbackProcessBtn'])) {
                $edit_dtu_obj->rollback_complete_dtu($_REQUEST, $messageStack);
            }

            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'verify_po':
            if ($allow_verify_dtu_permission) {
                $edit_dtu_obj->verifying_po($_REQUEST, $messageStack);
            } else {
                $messageStack->add_session(ERROR_PO_FORM_VERIFY_PERMISSION, 'error');
            }
            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'make_payment':
            if ($allow_dtu_make_payment_permission) {
                $edit_dtu_obj->make_dtu_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_DTU_FORM_DTU_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'debit_payment':
            if ($allow_dtu_make_payment_permission) {
                $edit_dtu_obj->debit_dtu_pre_payment($_REQUEST['po_id'], $messageStack);
            } else {
                $messageStack->add_session(ERROR_DTU_FORM_DTU_MAKE_PAYMENT_PERMISSION, 'error');
            }

            tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu'));
            break;

        case 'edit_dtu':
            if (!$edit_dtu_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction'))));
            }
            break;

        case 'search_dtu':
            if (!$edit_dtu_obj->load_po($_REQUEST, $messageStack)) {
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction'))));
            } else {
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=edit_dtu&po_id=' . $edit_dtu_obj->po_info['po_id']));
            }
            break;
            
        case 'show_dtu_list':
            if (isset($_REQUEST['reset'])) {
                unset($_SESSION['po_search']);
                tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT));
            }
            
        default:
            break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>general.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/modal_win.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/xmlhttp.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.tabs.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/ogm_jquery.js"></script>
        <? include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
        <? include_once (DIR_WS_INCLUDES . 'javascript/dtu_payment_xmlhttp.js.php'); ?>
        <script language="javascript">
            <!--
            var pageLoaded = false;
            function init() {
                // quit if this function has already been called
                if (arguments.callee.done)
                    return;

                // flag this function so we don't do the same thing twice
                arguments.callee.done = true;

                initInfoCaptions();
                pageLoaded = true;	// Control when a javascript event in this page can be triggered
            }
            ;

            /* for Mozilla */
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", init, null);
            }

            /* for other browsers */
            window.onload = init;
            //-->
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <div id="fancy_box" class="fancy_box" style="display:none;">
            <div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
            <div class="fancy_inner" style="display: block;">
                <div id="fancy_close" class="fancy_close" style="display: none;"></div>
                <div class="fancy_frame_bg">
                    <div class="fancy_bg fancy_bg_n"></div>
                    <div class="fancy_bg fancy_bg_ne"></div>
                    <div class="fancy_bg fancy_bg_e"></div>
                    <div class="fancy_bg fancy_bg_se"></div>
                    <div class="fancy_bg fancy_bg_s"></div>
                    <div class="fancy_bg fancy_bg_sw"></div>
                    <div class="fancy_bg fancy_bg_w"></div>
                    <div class="fancy_bg fancy_bg_nw"></div>
                </div>
                <div id="fancy_content" class="fancy_content"></div>
            </div>
        </div>
        <div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%">
                                <table border="0" width="100%" cellspacing="0" cellpadding="5">
                                    <tr>
                                        <td class="pageHeading" colspan="2">
                                            <?php 
                                                if (isset($_REQUEST['subaction']) || isset($_REQUEST['dtu_list_type'])) {
                                                    if ($_REQUEST['subaction'] == 'create_dtu' || 
                                                        $_REQUEST['subaction'] == 'create_blank_dtu' ||
                                                        $_REQUEST['subaction'] == 'add_dtu_cb') {
                                                        if($_REQUEST['dtu_list_type'] == 'add_dtu_cb') {
                                                            echo 'DTU Charge Back Form';
                                                        } else {
                                                            echo 'DTU Payment Request Form';
                                                        }
                                                    } else if ($_REQUEST['subaction'] == 'calculate_dtu' || 
                                                                $_REQUEST['subaction'] == 'create_dtu_product') {
                                                        echo 'Calculate DTU Payment Request Form';
                                                    } else if ($_REQUEST['subaction'] == 'preview_dtu') {
                                                        echo 'Preview DTU Payment Request Form';
                                                    } else if ($_REQUEST['subaction'] == 'edit_dtu') {
                                                        echo 'DTU Payment Request';
                                                    } else if ($_REQUEST['subaction'] == 'dtu_report') {
                                                        echo 'DTU Report';
                                                    } else {
                                                        echo 'DTU Payment Request List';
                                                    }
                                                } else {
                                                    echo 'DTU Payment Request List';
                                                }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td valign="top">
                                            <?
                                            switch ($_REQUEST['subaction']) {
                                                case 'create_dtu_product':
                                                    echo $edit_dtu_obj->calculate_dtu_form($_REQUEST);
                                                    break;
                                                case 'create_dtu':
                                                    echo $edit_dtu_obj->show_items_dtu_form($_REQUEST);
                                                    break;
                                                case 'create_blank_dtu':
                                                    echo $edit_dtu_obj->show_dtu_form($_REQUEST);
                                                    break;
                                                case 'add_dtu_cb':
                                                    echo $edit_dtu_obj->show_dtu_form($_REQUEST);
                                                    break;
                                                case 'insert_dtu':
                                                    echo $edit_dtu_obj->show_dtu_form($_REQUEST);
                                                    break;
                                                case 'calculate_dtu':
                                                    echo $edit_dtu_obj->calculate_dtu_form($_REQUEST);
                                                    break;
                                                case 'preview_dtu':
                                                    echo $edit_dtu_obj->preview_dtu_form($_REQUEST);
                                                    break;
                                                case 'preview_dtu_status':
                                                    echo $edit_dtu_obj->preview_dtu_cb_form($_REQUEST);
                                                    break;
                                                case 'edit_dtu':
                                                    echo $edit_dtu_obj->show_edit_dtu_form($_REQUEST);
                                                    break;
                                                case 'show_dtu_list':
                                                    echo $edit_dtu_obj->show_dtu_list($_REQUEST);
                                                    break;
                                                case 'dtu_report':
                                                    echo $edit_dtu_obj->show_dtu_report($_REQUEST);
                                                    break;
                                                default :
                                                    echo $edit_dtu_obj->search_dtu_list($_REQUEST);
                                                    break;
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <!-- body_text_eof //-->
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- body_eof //-->
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <?php
        if ($_REQUEST['subaction'] == 'edit_dtu') {
            echo '<script type="text/javascript">';
            echo '	jQuery().ready(function() {';
            echo "		getDTUPaymentStatistic('" . $edit_dtu_obj->supplier['supplier_id'] . "');";
            echo '	});';
            echo '</script>';
        }
        if ($_REQUEST['subaction'] == 'dtu_report') {
            echo '<script type="text/javascript">';
            echo '	jQuery().ready(function() {';
            echo "          jQuery('#search-tab > ul').tabs();";
            echo '	});';
            echo '</script>';
        }
        ?>
    </body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>