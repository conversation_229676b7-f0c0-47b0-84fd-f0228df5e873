<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml; charset=utf-8');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_CLASSES . 'po_suppliers.php');
include_once(DIR_WS_CLASSES . 'publishers.php');
include_once(DIR_WS_CLASSES . 'payment_module_info.php');
include_once(DIR_WS_CLASSES . 'dtu_payment.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');

$currencies = new currencies();
$dtu_publishers = new publishers();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$subaction = isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '';
$language_id = isset($_REQUEST['lang']) ? (int) $_REQUEST['lang'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : 'english';

$results = '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT);
}

define('PARTIAL_RECEIVE_STATUS', 2);

$allow_set_dtu_status_permission = tep_admin_files_actions(FILENAME_DTU_PAYMENT, 'DTU_PO_SET_PAID_STATUS');

if (tep_not_null($action)) {
    switch ($action) {
        case "popup_remark":
            echo "<popup_remark><result>OK</result></popup_remark>";
            break;

        case "perform_tagging":
            $order_status_id = (int) $_REQUEST['status_id'];
            $setting_value = $_REQUEST['setting'];
            $po_ids_array = explode(',', $_REQUEST['po_str']);
            $list_mode = (int) $_REQUEST['list_mode'];
            echo "<tag_info>";
            if ($subaction == 'nt') {
                if (tep_not_null($setting_value)) {
                    $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
                    $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                    if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                        $po_tag_id = (int) $tag_verify_row["orders_tag_id"];
                    } else {
                        $insert_sql_data = array('orders_tag_name' => tep_db_prepare_input($setting_value),
                            'orders_tag_status_ids' => $order_status_id,
                            'filename' => FILENAME_DTU_PAYMENT
                        );
                        tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
                        $po_tag_id = tep_db_insert_id();
                    }

                    $assign_po_tag_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids = IF (purchase_orders_tag_ids='', '" . $po_tag_id . "', CONCAT_WS(',', purchase_orders_tag_ids, '" . $po_tag_id . "')) WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND NOT FIND_IN_SET('" . $po_tag_id . "', purchase_orders_tag_ids)";
                    tep_db_query($assign_po_tag_update_sql);

                    generateTagString($po_ids_array);
                }
            } else if ($subaction == 'at') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders with this tag
                    $assign_po_tag_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids = IF (purchase_orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', purchase_orders_tag_ids, '" . (int) $setting_value . "')) WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND NOT FIND_IN_SET('" . (int) $setting_value . "', purchase_orders_tag_ids)";
                    tep_db_query($assign_po_tag_update_sql);

                    generateTagString($po_ids_array);
                }
            } else if ($subaction == 'rt') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders by removing this tag from them
                    $unassign_po_tag_select_sql = "SELECT purchase_orders_id, purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id IN (" . implode(',', $po_ids_array) . ") AND FIND_IN_SET('" . (int) $setting_value . "', purchase_orders_tag_ids)";
                    $unassign_po_tag_result_sql = tep_db_query($unassign_po_tag_select_sql);
                    while ($unassign_po_tag_row = tep_db_fetch_array($unassign_po_tag_result_sql)) {
                        $TagRemovePattern = "/(,)?" . (int) $setting_value . "(,)?/is";
                        $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_po_tag_row["purchase_orders_tag_ids"]);
                        if (substr($new_tag_string, 0, 1) == ',')
                            $new_tag_string = substr($new_tag_string, 1);
                        if (substr($new_tag_string, -1) == ',')
                            $new_tag_string = substr($new_tag_string, 0, -1);

                        tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . $unassign_po_tag_row["purchase_orders_id"] . "'");
                    }

                    generateTagString($po_ids_array);
                }
            }
            generateTagSelectionOptions($order_status_id, $_REQUEST['po_str'], ($list_mode == "2" ? true : false));
            echo "</tag_info>";
            break;

        case "get_supplier_info":
            $selected_dtu_payment = '';
            if (isset($_REQUEST['pay_id']) && $_REQUEST['pay_id'] != '') {
                $selected_dtu_payment = $_REQUEST['pay_id'];
            }

            $dtu_publisher = new publishers($_REQUEST['s_id']);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$_REQUEST['s_id']]['publishers_supplier_id'];

            $dtu_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $dtu_supplier_payment_info = $dtu_supplier->get_po_supplier_info($dtu_supplier_id);
            if (count($dtu_supplier_payment_info)) {
                // Get CDKey Supplier address Info
                $countries_info = tep_get_countries_info($dtu_supplier_payment_info['country_id'], 'countries_id');
                $address_str = tep_address_format($countries_info['address_format_id'], $dtu_supplier_payment_info, 1, '', '<br>', 'main');

                // Get CDKey Supplier's payment term info
                $dtu_pay_type = $dtu_supplier_payment_info['payment_type'];
                switch ($dtu_pay_type) {
                    case 'g':
                        $dtu_payment = TEXT_SUPPLIER_CONSIGNMENT;
                        break;
                    case 'c':
                        $dtu_payment = TEXT_SUPPLIER_PRE_PAYMENT;
                        break;
                    case 'd':
                        $dtu_payment = TEXT_SUPPLIER_DTU_PAYMENT;
                        break;
                    case 't':
                        $dtu_payment = $dtu_supplier_payment_info['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                        break;
                }

                // Get CDKey Supplier's disbursement methods
                $dtu_disburse = '<select name="dtu_supplier_payment" id="dtu_supplier_payment" onChange="getCurrencyRate(document.dtu_form.dtu_currency, \'suggest_rate\', \'confirm_rate\');"><option value="">' . PULL_DOWN_DEFAULT . '</option>';
                $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $dtu_supplier_id, '3');
                $pm_array = $pm_obj->get_existing_payment_account_selection();
                for ($i = 0, $n = sizeof($pm_array); $i < $n; $i++) {
                    $selected = '';
                    if (tep_not_null($selected_dtu_payment) && $selected_dtu_payment == $pm_array[$i]['id']) {
                        $selected = ' selected="selected" ';
                    }
                    $dtu_disburse .= '<option value="' . $pm_array[$i]['id'] . '"' . $selected . '>' . $pm_array[$i]['text'] . '</option>';
                }
                $dtu_disburse .= '</select>' . TEXT_FIELD_REQUIRED;
                
                // Get CDKey Supplier's delivery address
                $selected_dtu_delivery = '';
                if (isset($_REQUEST['delivery_set']) && $_REQUEST['delivery_set'] != '') {
                    $selected_dtu_delivery = $_REQUEST['delivery_set'];
                } else {
                    $selected_dtu_delivery = $dtu_supplier_payment_info['po_supplier_company_code'];
                }

                $dtu_supplier_company = tep_get_po_company_list('dtu_delivery_address', $selected_dtu_delivery, 'id="dtu_delivery_address" onChange="getGST(this);"', true);
                
                echo "<result>";
                
                // Supplier Locked?
                $supplier_locked = $dtu_supplier->get_po_supplier_lock_status($dtu_supplier_id,$_SESSION['login_id']);
                if (is_null($supplier_locked['po_supplier_locked_by']) === true) {
                        echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                } else if ($supplier_locked['po_supplier_locked_by'] == $_SESSION['login_id']) {
                        echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                } else {
                        echo "<supplier_locked><![CDATA[]]></supplier_locked>";
                }

                echo "	<supplier_address><![CDATA[" . $address_str . "]]></supplier_address>";
                echo "	<supplier_payment><![CDATA[" . $dtu_payment . "]]></supplier_payment>";
                echo "	<supplier_disbursement><![CDATA[" . $dtu_disburse . "]]></supplier_disbursement>";
                echo "	<supplier_delivery><![CDATA[" . $dtu_supplier_company . "]]></supplier_delivery>";
                echo "</result>";
            } else {
                echo "<result><supplier_address>Unknown supplier!</supplier_address></result>";
            }
            break;
            
        case "lock_po_supplier" :
            $dtu_publisher = new publishers($_REQUEST['p_id']);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$_REQUEST['p_id']]['publishers_supplier_id'];
            $dtu_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $locked_by = $dtu_supplier->get_po_supplier_lock_validation($dtu_supplier_id,$_SESSION['login_id']);
            
            echo "<result>";
            
            if ($locked_by == $_SESSION['login_id'] || empty($locked_by)) {
                $locked_result = $dtu_supplier->lock_po_supplier($dtu_supplier_id,$_SESSION['login_id']);
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked]]></action_result>";
            } else {
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked by other user]]></action_result>";
            }
            
            echo "</result>";
            
            break;

        case "unlock_po_supplier" :
            $dtu_publisher = new publishers($_REQUEST['p_id']);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$_REQUEST['p_id']]['publishers_supplier_id'];
            $dtu_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
            $locked_by = $dtu_supplier->get_po_supplier_lock_validation($dtu_supplier_id,$_SESSION['login_id']);

            echo "<result>";
            
            if ($locked_by == $_SESSION['login_id'] || empty($locked_by)) {
                $locked_result = $dtu_supplier->unlock_po_supplier($dtu_supplier_id);
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
            echo "<action_result><![CDATA[This CDK supplier is unlocked]]></action_result>";
            } else {
                echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', 'id="supplier_lock" name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
                echo "<action_result><![CDATA[This CDK supplier is locked by other user]]></action_result>";
            }

            echo "</result>";
            
            break;

        case "get_company_gst":
            $comp_code = isset($_REQUEST['comp_code']) ? tep_db_prepare_input($_REQUEST['comp_code']) : '';
            $total_amt = isset($_REQUEST['total_amt']) ? tep_db_prepare_input($_REQUEST['total_amt']) : '';
            $po_currency = isset($_REQUEST['dtu_curr']) ? tep_db_prepare_input($_REQUEST['dtu_curr']) : '';
            $include_gst = isset($_REQUEST['incl_gst']) ? tep_db_prepare_input($_REQUEST['incl_gst']) : 0;

            $gst_value = $tax_amt = 0;
            $taxable_total = $total_amt;
            $gst_title = sprintf(TABLE_HEADING_BOTTOM_GST, dtu_payment::monetary_decimal_format($gst_value, '%01.1f')) . ':';

            if (tep_not_null($comp_code) && tep_not_null($total_amt) && $total_amt > 0) {
                $dtu_comp_select_sql = "SELECT po_company_gst_percentage FROM " . TABLE_PO_COMPANY . " WHERE po_company_code='" . tep_db_input($comp_code) . "'";
                $dtu_comp_result_sql = tep_db_query($dtu_comp_select_sql);
                if ($dtu_comp_row = tep_db_fetch_array($dtu_comp_result_sql)) {
                    $gst_value = $dtu_comp_row['po_company_gst_percentage'];
                    $gst_title = sprintf(TABLE_HEADING_BOTTOM_GST, dtu_payment::monetary_decimal_format($gst_value, '%01.1f')) . ':';
                    $tax_amt = round(($total_amt * ($gst_value / 100)), 4);
                    $taxable_total = $total_amt + $tax_amt;
                }
            }

            if (tep_not_null($include_gst) && $include_gst == 0) {
                $tax_amt = 0;
                $taxable_total = $total_amt;
            }

            echo "<result>";
            echo "	<gst_value><![CDATA[" . $gst_value . "]]></gst_value>";
            echo "	<gst_title_text><![CDATA[" . $gst_title . "]]></gst_title_text>";
            echo "	<tax_amount><![CDATA[" . $tax_amt . "]]></tax_amount>";
            echo "	<after_tax_total><![CDATA[" . $taxable_total . "]]></after_tax_total>";
            echo "</result>";
            break;

        case "get_currency_rate":
            $curr_code = isset($_REQUEST['curr_code']) ? $_REQUEST['curr_code'] : '';
            $pm_id = isset($_REQUEST['pm_id']) ? $_REQUEST['pm_id'] : '';
            $supp_id = isset($_REQUEST['s_id']) ? $_REQUEST['s_id'] : '';
            
            $dtu_publisher = new publishers($supp_id);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$supp_id]['publishers_supplier_id'];
            
            $rate_str = '0.*********';
            $dest_curr = DEFAULT_CURRENCY;
            $po_credit_note_amt = 0;

            $scArrayResult = false;
            $customerIdStatus = true;

            if (tep_not_null($curr_code) && tep_not_null($pm_id) && tep_not_null($dtu_supplier_id)) {
                $pm_select_sql = "SELECT pab.store_payment_account_book_id, pm.payment_methods_send_currency
                                    FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                            ON pab.payment_methods_id=pm.payment_methods_id
                                    WHERE pab.store_payment_account_book_id = '" . tep_db_input($pm_id) . "'";
                $pm_result_sql = tep_db_query($pm_select_sql);
                if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
                    // checking for disbursement method NRSC
                    if ($pm_row['payment_methods_send_currency'] < 1) {
                        // get user_id (customer)
                        $supplier_account_sql = "SELECT spabd.payment_methods_fields_value
                                                FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd
                                                INNER JOIN payment_methods_fields AS pmf
                                                    ON spabd.payment_methods_fields_id = pmf.payment_methods_fields_id 
                                                    AND pmf.payment_methods_fields_title = 'Customer ID'
                                                WHERE spabd.store_payment_account_book_id = '" . tep_db_input($pm_row['store_payment_account_book_id']) . "'";
                        $supplier_account_result = tep_db_query($supplier_account_sql);
                        if ($supplier_account = tep_db_fetch_array($supplier_account_result)) {
                            if (is_numeric($supplier_account['payment_methods_fields_value'])) {
                                $scArrayResult = ms_store_credit::getScBalance($supplier_account['payment_methods_fields_value']);
                                if ($scArrayResult) {
                                    $customerIdStatus = true;
                                    $dest_curr = $scArrayResult['currency'];
                                }
                            }
                        }

                        if (!$customerIdStatus) {
                            $dest_curr = $curr_code;
                        }
                    } else {
                        $dest_curr = $currencies->get_code_by_id($pm_row['payment_methods_send_currency']);
                    }

                    // Use Spot Rate
                    $curr_data = $currencies->currencies[$curr_code];
                    if ($curr_data['value'] > 0) {
                        $rate_str = round((1 / $curr_data['value']), 8);
                    }

                    if ($dest_curr != DEFAULT_CURRENCY) {
                        $dest_data = $currencies->currencies[$dest_curr];
                        if ($dest_data['value'] > 0) {
                            $rate_str = round(($rate_str * $dest_data['value']), 8);
                        }
                    }
                }
            }

            echo "<result>";
            echo "	<payment_currency><![CDATA[" . $dest_curr . "]]></payment_currency>";
            echo "	<currency_rate><![CDATA[" . $rate_str . "]]></currency_rate>";
            echo "	<sell_price_title><![CDATA[" . sprintf(TABLE_HEADING_DTU_SRP, $curr_code) . "]]></sell_price_title>";
            echo "	<total_price_title><![CDATA[" . sprintf(TABLE_HEADING_DTU_AMOUNT, $curr_code) . "]]></total_price_title>";
            echo "</result>";
            break;

        case "get_product_sell_price":
            $products_id = isset($_REQUEST['p_id']) ? $_REQUEST['p_id'] : '';
            $topup_id = isset($_REQUEST['tp_id']) ? $_REQUEST['tp_id'] : '';
            $sell_curr = isset($_REQUEST['sell_curr']) ? $_REQUEST['sell_curr'] : '';
            $dtu_type = isset($_REQUEST['dtu_type']) ? $_REQUEST['dtu_type'] : '';
            $main_pid = isset($_REQUEST['main_pid']) ? $_REQUEST['main_pid'] : '';

            $publisher_obj = new publishers($_REQUEST['pub_id']);

            $delivered_qty = 0;
            $selling_price = 0;
            $final_po_price = 0;

            if (tep_not_null($products_id) && tep_not_null($sell_curr)) {
                $sub_products_id = $products_id;
                if (tep_not_null($main_pid)) {
                    $products_id = $main_pid;
                }
                
                $_sql = tep_db_query("SELECT products_type FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $products_id);
                $_row = tep_db_fetch_array($_sql);

                if ($dtu_type == 'dn_type' || $_row["products_type"] == 3) {
                    $po_sql = " SELECT otw.purchase_orders_id, op.products_delivered_quantity, otw.orders_products_id
                                FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw
                                INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                    ON otw.orders_products_id = op.orders_products_id
                                WHERE otw.top_up_id = '" . tep_db_input($topup_id) . "'";
                    $po_result = tep_db_query($po_sql);
                    $po_row = tep_db_fetch_array($po_result);

                    $delivered_qty = round($po_row['products_delivered_quantity'], 4);
                    $po_price_info = $publisher_obj->get_dtu_po_final_price($po_row['purchase_orders_id'], $sub_products_id);
                    $round_selling_price = round($po_price_info['products_selling_price'], 4);
                    $round_unit_price_value = round($po_price_info['products_unit_price_value'], 4);

                    if ($po_price_info['products_unit_price_type'] == '%') {
                        $final_po_price = ($round_selling_price - ($round_selling_price * $round_unit_price_value / 100)) * $delivered_qty;
                    } else {
                        $final_po_price = ($round_selling_price - $round_unit_price_value) * $delivered_qty;
                    }

                    $gst_value = ($po_price_info['purchase_orders_gst_value']) ? $po_price_info['purchase_orders_gst_value'] : 0;
                    $final_po_price = $final_po_price + ($gst_value / 100 * $final_po_price);
                    
                    if ($sell_curr == DEFAULT_CURRENCY) {
                        if ($po_price_info['currency'] == DEFAULT_CURRENCY) {
                            $selling_price_dn = $final_po_price;
                        } else {
                            $base_rate = 1;
                            if ($currencies->currencies[$po_price_info['currency']]['value'] > 0) {
                                $base_rate = 1 / $currencies->currencies[$po_price_info['currency']]['value'];
                            }
                            $selling_price_dn = $base_rate * $final_po_price;
                        }
                    } else {
                        if ($po_price_info['currency'] == $sell_curr) {
                            $selling_price_dn = $final_po_price;
                        } else {
                            $selling_price_dn = $currencies->advance_currency_conversion($final_po_price, $po_price_info['currency'], $sell_curr, false);
                        }
                    }
                }

                if ($_row["products_type"] == 3 && $sub_products_id != $products_id) {
                    // virtual listing
                    $_sql2 = tep_db_query("SELECT top_up_id, currency_code, currency_settle_amount FROM " . TABLE_ORDERS_TOP_UP . " WHERE orders_products_id = " . $po_row["orders_products_id"]);
                    $_row2 = tep_db_fetch_array($_sql2);
                    // Get the topup quantity
                    $_sql3 = tep_db_query("SELECT COUNT(*) AS total FROM " . TABLE_ORDERS_TOP_UP_SUB_ORDER . " WHERE status = 1 AND top_up_id = " . $_row2['top_up_id']);
                    $_row3 = tep_db_fetch_array($_sql3);

                    $selling_price = getVirtualProductSellPrice($_row2['currency_code'], $_row2['currency_settle_amount'] * $_row3['total'], $sell_curr);
                } else {
                    $selling_price = getProductSellPrice($products_id, $sell_curr);
                }
            }

            echo "<result>";
            echo "	<product_sell_price><![CDATA[" . $selling_price . "]]></product_sell_price>";
            echo "  <product_dn_sell_price><![CDATA[" . $selling_price_dn . "]]></product_dn_sell_price>";
            echo "</result>";
            break;

        case "show_lock_btn":
            $log_object = new log_files($_SESSION['login_id']);
            $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
            if ($subaction == "ul" || $subaction == "ulo") { // unlocking
                $lock_orders_select_sql = "	SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po 
											LEFT JOIN " . TABLE_ADMIN . " AS a 
												ON (po.purchase_orders_locked_by = a.admin_id) 
											WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                if ($lock_orders_row["purchase_orders_locked_by"] == $_REQUEST['adm']) {
                    $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                    tep_db_query($unlock_orders_update_sql);
                    echo '	<po_lock>
							<result>This order has been successfully unlocked!</result>
							<action>Show Lock Button</action>
							<time>' . date("Y-m-d H:i:s") . '</time>
							<lock_msg><![CDATA[' . TEXT_ORDER_NOT_BEEN_LOCKED . ']]></lock_msg>
							</po_lock>';

                    $log_object->insert_orders_log($_REQUEST['oid'], ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_DTU_PAYMENT);
                } else {
                    if (tep_not_null($lock_orders_row["purchase_orders_locked_by"])) { // locked by other people
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($_SESSION['login_groups_id'], $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            if ($subaction == "ulo") {
                                $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                                tep_db_query($unlock_orders_update_sql);
                                echo "<po_lock><result>This order has been successfully unlocked!" . tep_db_prepare_input($_REQUEST['log_comment']) . "</result>";
                                echo "<action>Show Lock Button</action>";
                                echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                                echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg></po_lock>";

                                $log_object->insert_orders_log($_REQUEST['oid'], sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], tep_db_prepare_input($_REQUEST['log_comment'])), FILENAME_DTU_PAYMENT);
                            } else {
                                echo "<po_lock><result>This order is locked by someone else!</result>";
                                echo "<action>Show Unlock Button</action>";
                                echo "<subaction>Prompt For Unlocking Msg</subaction>";
                                echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "]]></lock_msg></po_lock>";
                            }
                        } else {
                            echo "<po_lock><result>Unlock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"], $contact_admin_group_msg) . "]]></lock_msg></po_lock>";
                        }
                    } else { // nobody lock this order
                        echo "<po_lock><result>You are not locking this order!</result>";
                        echo "<action>Show Lock Button</action>";
                        echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                        echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg></po_lock>";
                    }
                }
            } else if ($subaction == "l") {  // locking
                $lock_orders_select_sql = "	SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po 
											LEFT JOIN " . TABLE_ADMIN . " 
												AS a ON (po.purchase_orders_locked_by = a.admin_id) 
											WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                if (tep_not_null($lock_orders_row["purchase_orders_locked_by"])) { // this order currently is locked
                    if ($lock_orders_row["purchase_orders_locked_by"] == $_REQUEST['adm']) {
                        echo "<po_lock><result>You had been locking this order!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<close_win>1</close_win>";
                        echo "<lock_msg><![CDATA[" . TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER . "]]></lock_msg></po_lock>";
                    } else {
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($_SESSION['login_groups_id'], $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            echo "<po_lock><result>Lock order is failed!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<subaction>Prompt For Unlocking Msg</subaction>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "]]></lock_msg></po_lock>";
                        } else {
                            echo "<po_lock><result>Lock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_orders_row["admin_email_address"], $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"], $contact_admin_group_msg) . "]]></lock_msg></po_lock>";
                        }
                    }
                } else {
                    // check order status
                    $order_status_select_sql = "SELECT purchase_orders_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                    $order_status_result_sql = tep_db_query($order_status_select_sql);
                    $order_status_row = tep_db_fetch_array($order_status_result_sql);

                    if ($order_status_row["purchase_orders_status"] != PARTIAL_RECEIVE_STATUS) {
                        echo "<po_lock><result>This order cannot be locked!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<lock_msg><![CDATA[" . TEXT_LOCKED_ORDER_NOT_VALID_STATUS . "]]></lock_msg></po_lock>";
                    } else {
                        $from_time = $_REQUEST['from_time'];
                        $check_within_locking_select_sql = "	SELECT orders_log_id 
																FROM " . TABLE_ORDERS_LOG_TABLE . " 
																WHERE orders_log_orders_id = '" . $_REQUEST['oid'] . "' 
																	AND orders_log_time >= '" . $from_time . "' 
																	AND orders_log_admin_id <> '" . $_REQUEST['adm'] . "' 
																	AND orders_log_filename = '" . tep_db_input(FILENAME_DTU_PAYMENT) . "'";
                        $check_within_locking_result_sql = tep_db_query($check_within_locking_select_sql);
                        if (tep_db_num_rows($check_within_locking_result_sql)) { // someone lock and unlock before you manage to lock it.
                            echo "<po_lock><result>This order has been updated by someone!</result>";
                            echo "<action>Prompt Alert Message</action>";
                            echo "<lock_msg><![CDATA[" . TEXT_LOCKED_OUTDATED_ORDER . "]]></lock_msg></po_lock>";
                        } else {
                            $lock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = '" . $_REQUEST['adm'] . "', purchase_orders_locked_from_ip='" . tep_get_ip_address() . "', purchase_orders_locked_datetime=now() WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND (purchase_orders_locked_by IS NULL OR purchase_orders_locked_by='')";
                            tep_db_query($lock_orders_update_sql);

                            $lock_orders_select_sql = "SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, po.purchase_orders_locked_datetime, a.admin_email_address FROM " . TABLE_PURCHASE_ORDERS . " AS po inner join " . TABLE_ADMIN . " AS a ON po.purchase_orders_locked_by=a.admin_id WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'";
                            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                            $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                            echo "<po_lock><result>This order has been successfully locked!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<lock_msg>" . sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . "</lock_msg></po_lock>";

                            $log_object->insert_orders_log($_REQUEST['oid'], ORDERS_LOG_LOCK_ORDER, FILENAME_DTU_PAYMENT);
                        }
                    }
                }
            } else {
                echo "<po_lock><result>Unknown request!</result></po_lock>";
            }
            break;

        case "get_order_locking_history":
            $LockingCommentPattern = "/(?:##)(\S+)(?:##)(.*)/is";

            $locking_history_select_sql = "SELECT * FROM " . TABLE_ORDERS_LOG_TABLE . " WHERE orders_log_orders_id = '" . $_REQUEST['oid'] . "' AND orders_log_filename ='" . tep_db_input(FILENAME_DTU_PAYMENT) . "' ORDER BY orders_log_time";
            $locking_history_result_sql = tep_db_query($locking_history_select_sql);

            $history_text = '<table border="1" cellspacing="0" cellpadding="5">
      							<tr>
            						<td class="smallText" align="center"><b>Action Date</b></td>
            						<td class="smallText" align="center"><b>Comments</b></td>
            						<td class="smallText" align="center"><b>Admin</b></td>
            						<td class="smallText" align="center"><b>IP</b></td>
          						</tr>';
            $history_count = 0;
            while ($locking_history_row = tep_db_fetch_array($locking_history_result_sql)) {
                $locking_comment_msg = '';
                if (preg_match($LockingCommentPattern, $locking_history_row["orders_log_system_messages"], $regs)) {
                    switch ($regs[1]) {
                        case "l_1":
                            $locking_comment_msg = "Locking order";
                            break;
                        case "ul_1":
                            $locking_comment_msg = "Unlocking order";
                            break;
                        case "ul_2":
                            $other_admin_email = $user_msg = '';
                            if (tep_not_null($regs[2])) {
                                list($other_admin_email, $user_msg) = explode(':~:', $regs[2]);
                            }

                            $locking_comment_msg = "Unlocking other people " . (tep_not_null($other_admin_email) ? "(" . $other_admin_email . ")" : '') . " order" . (tep_not_null($user_msg) ? '<br>' . $user_msg : '');
                            break;
                    }
                }

                if (is_numeric($locking_history_row["orders_log_admin_id"])) {
                    $admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $locking_history_row["orders_log_admin_id"] . "'");
                    if ($admin_info = tep_db_fetch_array($admin_query))
                        $admin_email_address = $admin_info["admin_email_address"];
                    else
                        $admin_email_address = $locking_history_row["orders_log_admin_id"] == 0 ? 'System' : $locking_history_row["orders_log_admin_id"];
                } else {
                    $admin_email_address = $locking_history_row["orders_log_admin_id"];
                }
                $history_text .= '	<tr>
  										<td class="smallText" align="center">' . $locking_history_row['orders_log_time'] . '</td>
  										<td class="smallText">' . $locking_comment_msg . '</td>
  										<td class="smallText">' . $admin_email_address . '</td>
  										<td class="smallText">' . $locking_history_row['orders_log_ip'] . '</td>
  									</tr>';
                $history_count++;
            }

            if ($history_count == 0) {
                $history_text .= '	<tr>
										<td class="smallText" colspan="4">No Order Locking History Available</td>
									</tr>';
            }

            $history_text .= '</table>';
            echo "<locking_history>";
            echo "	<res_code>1</res_code>";
            echo "	<result><![CDATA[" . $history_text . "]]></result>";
            echo "</locking_history>";
            break;

        case 'get_dtu_payment_statistic':
            $stat_html = '<tr>
                            <td width="20%" class="main" valign="top">
                                <b>' . ENTRY_DTU_FORM_DTU_STATISTICS . ':</b><br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'suppID=' . $_REQUEST['suppID'] . '&subaction=show_dtu_list', 'NONSSL') . '" target="_blank">' . ENTRY_DTU_FORM_DTU_HISTORY . '</a>
                            </td>
                            <td class="main" valign="top">
                                <table border="1" width="100%" align="left" cellspacing="0" cellpadding="5">';

            $stat_po_status_array = array(
                '1' => 'Pending',
                '2' => 'Processing',
                '3' => 'Completed',
                '4' => 'Cancel'
            );

            if (count($stat_po_status_array)) {
                $stat_html .= '<tr>
                                <td class="main">' . STAT_TABLE_HEADING_DTU_STATUS . '</td>
                                <td class="main" align="center">' . STAT_TABLE_HEADING_TOTAL_DTU . '</td>
                                <td class="main" align="right">' . sprintf(STAT_TABLE_HEADING_TOTAL_AMOUNT, $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'] . $currencies->currencies[DEFAULT_CURRENCY]['symbol_right']) . '</td>
                            </tr>';

                foreach ($stat_po_status_array as $status_id => $status_name) {
                    if ((int) $status_id < 1) {
                        continue;
                    }

                    $po_1day_count = $po_1day_total = 0;
                    $count_po_1day_select_sql = "SELECT po.purchase_orders_id 
                                                FROM " . TABLE_PURCHASE_ORDERS . " AS po
                                                WHERE 
                                                    po.supplier_id = '" . (int) $_REQUEST['suppID'] . "'
                                                    AND po.purchase_orders_type = '1'
                                                    AND po.purchase_orders_status = '" . (int) $status_id . "'
                                                    AND po.last_modified >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))) . "'
                                                    AND po.last_modified <= '" . date("Y-m-d H:i:s", mktime(date('H'), date('i'), 0, date('m'), date('d'), date('Y'))) . "'";
                    $count_po_1day_result_sql = tep_db_query($count_po_1day_select_sql);
                    while ($count_po_1day_row = tep_db_fetch_array($count_po_1day_result_sql)) {
                        $po_1day_count++;

                        $count_po_1day_total_select_sql = "SELECT usd_value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $count_po_1day_row['purchase_orders_id'] . "' AND class='po_subtotal'";
                        $count_po_1day_total_result_sql = tep_db_query($count_po_1day_total_select_sql);
                        if ($count_po_1day_total_row = tep_db_fetch_array($count_po_1day_total_result_sql)) {
                            $po_1day_total += $count_po_1day_total_row['usd_value'];
                        }
                    }

                    $po_count = $po_total = 0;
                    $count_po_select_sql = "SELECT po.purchase_orders_id 
                                            FROM " . TABLE_PURCHASE_ORDERS . " AS po
                                            WHERE po.supplier_id = '" . (int) $_REQUEST['suppID'] . "'
                                                AND po.purchase_orders_type = '1'
                                                AND po.purchase_orders_status = '" . (int) $status_id . "'";
                    $count_po_result_sql = tep_db_query($count_po_select_sql);
                    while ($count_po_row = tep_db_fetch_array($count_po_result_sql)) {
                        $po_count++;

                        $count_po_total_select_sql = "SELECT usd_value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $count_po_row['purchase_orders_id'] . "' AND class='po_subtotal'";
                        $count_po_total_result_sql = tep_db_query($count_po_total_select_sql);
                        if ($count_po_total_row = tep_db_fetch_array($count_po_total_result_sql)) {
                            $po_total += $count_po_total_row['usd_value'];
                        }
                    }

                    $order_stat_style = '';

                    $stat_html .= '<tr>
                                    <td class="main" valign="top" colspan="3"><b>' . $status_name . '</b></td>
                                </tr>';

                    if ($status_id != 1) {
                        $stat_html .= '<tr>
                                        <td class="main" valign="top" nowrap>' . STAT_TABLE_ENTRY_DTU_1_DAY . '</td>
                                        <td class="main" align="center" valign="top" nowrap>' . ($po_1day_count > 0 ? $po_1day_count : '0') . '</td>
                                        <td class="main" align="right" valign="top" nowrap>' . ($po_1day_total > 0 ? $currencies->format($po_1day_total) : '0.00') . '</td>
                                    </tr>';
                    }

                    $stat_html .= '<tr>
                                    <td class="main" valign="top" nowrap>' . STAT_TABLE_ENTRY_TOTAL_DTU . '</span></td>
                                    <td class="main" align="center" valign="top" nowrap>' . ($po_count > 0 ? $po_count : '0') . '</td>
                                    <td class="main" align="right" valign="top" nowrap>' . ($po_total > 0 ? $currencies->format($po_total) : '0.00') . '</td>
                                </tr>';
                }
            }

            echo '<stat_result>
                    <stat><![CDATA[' . $stat_html . ']]></stat>
                </stat_result>';
            break;

        case "set_po_remark":
            $order_comment_style_array = array("orderCommentSystem", "orderCommentCrew", "orderCommentDelivery");

            $status_history_id = (int) $_REQUEST['shid'];

            $po_history_checking_sql = "SELECT purchase_orders_status_history_id FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND purchase_orders_status_history_id = '" . $status_history_id . "' LIMIT 1";
            $po_history_result_sql = tep_db_query($po_history_checking_sql);

            if (tep_db_num_rows($po_history_result_sql) > 0) {
                tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " SET set_as_po_remarks=0 WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "'");
                tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " SET set_as_po_remarks=1 WHERE purchase_orders_id = '" . $_REQUEST['oid'] . "' AND purchase_orders_status_history_id = '" . $status_history_id . "'");

                $po_history_select_sql = "	SELECT osh.*, os.purchase_orders_status_name 
												FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " AS osh 
												LEFT JOIN " . TABLE_PURCHASE_ORDERS_STATUS . " AS os 
													ON (osh.purchase_orders_status_id = os.purchase_orders_status_id) 
												WHERE osh.purchase_orders_id = '" . (int) $_REQUEST['oid'] . "' AND (os.language_id = '" . $languages_id . "' OR os.language_id IS NULL) 
												ORDER BY osh.date_added";
                $po_history_result_sql = tep_db_query($po_history_select_sql);

                $history_text = '<table border="1" cellspacing="0" cellpadding="5">
          							<tr>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_DATE_ADDED . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_STATUS . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_COMMENTS . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_MODIFIED_BY . '</b></td>
	            						<td class="smallText" align="center"><b>' . STATUS_TABLE_HEADING_ACTION . '</b></td>
	          						</tr>';
                $oc_cnt = 0;
                while ($po_history_row = tep_db_fetch_array($po_history_result_sql)) {
                    $comment_row_style = $po_history_row["set_as_po_remarks"] == 1 ? 'class="orderRemarkSelectedRow"' : 'class="' . $order_comment_style_array[$po_history_row["comments_type"]] . '"';

                    $formatted_date_comment_added = tep_datetime_short($po_history_row["date_added"]);

                    $history_text .= '	<tbody id="o_comment_' . $po_history_row["comments_type"] . '_' . $po_history_row["purchase_orders_status_history_id"] . '">' . "\n" .
                            '	<tr ' . $comment_row_style . '>' . "\n" .
                            '		<td class="smallText" align="center">' . (tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--') . '</td>' . "\n";
                    $history_text .= ' 		<td class="smallText">' . (tep_not_null($po_history_row['purchase_orders_status_name']) ? $po_history_row['purchase_orders_status_name'] : '--') . '</td>
											<td class="smallText">' . nl2br(str_replace("\t", '&nbsp;&nbsp;&nbsp;&nbsp;', $po_history_row['comments'])) . '</td>
											<td class="smallText">' . nl2br(tep_db_output($po_history_row['changed_by'])) . '</td>
											<td class="smallText"><div id="set_remark_' . $po_history_row["purchase_orders_status_history_id"] . '">' . ($po_history_row['set_as_po_remarks'] == 1 ? '&nbsp;' : '<a href="javascript:;" onClick="setAsPORemark(\'' . $po_history_row["purchase_orders_status_history_id"] . '\', \'' . $po_history_row["purchase_orders_id"] . '\', \'' . $languages_id . '\', \'order_comment_history_box\');"><u>' . TEXT_SET_AS_REMARK . '</u></a>') . '</div></td>
										</tr>
									</tbody>';
                    $history_text .= tep_draw_hidden_field('hidden_o_comment_' . $po_history_row["comments_type"] . '_' . $po_history_row['purchase_orders_status_history_id'], '');
                    $oc_cnt++;
                }

                $history_text .= '<tr><td class="smallText" colspan="6">';
                $history_text .= tep_draw_separator('pixel_trans.gif', '1', '30');
                $history_text .= tep_submit_button(BUTTON_ADD_REMARK, ALT_BUTTON_ADD_REMARK, 'name="AddRemarkBtn" onClick="get_crew_remark();"', 'inputButton');
                $history_text .= '</td></tr>';

                $history_text .= '</table>';
                echo "<set_po_remark>";
                echo "	<res_code>1</res_code>";
                echo "	<result><![CDATA[" . $history_text . "]]></result>";
                echo "</set_po_remark>";
            } else {
                echo "<set_po_remark>";
                echo "	<res_code>0</res_code>";
                echo "	<result>Record not found!</result>";
                echo "</set_po_remark>";
            }
            break;

        case "get_po_info":
            if (tep_not_null($_REQUEST['o_str'])) {

                $order_ids_array = explode(',', $_REQUEST['o_str']);
                $po_obj = new dtu_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
                echo "<order_info>";
                for ($po_cnt = 0; $po_cnt < count($order_ids_array); $po_cnt++) {
                    // Instantiate order object
                    if ($po_obj->load_po(array('po_id' => $order_ids_array[$po_cnt]), $messageStack)) {
                        $po_remark_select_sql = "SELECT comments, changed_by 
                                                FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " 
                                                WHERE purchase_orders_id = '" . $order_ids_array[$po_cnt] . "' 
                                                    AND set_as_po_remarks = 1 
                                                LIMIT 1";
                        $po_remark_result_sql = tep_db_query($po_remark_select_sql);
                        if ($po_remark_row = tep_db_fetch_array($po_remark_result_sql)) {
                            $po_remark = $po_remark_row["comments"];
                            $remark_by_admin_email_address = $po_remark_row["changed_by"];
                        } else {
                            $po_remark = $remark_by_admin_email_address = '';
                        }

                        // check whether this order got any tax on purchased products
                        $product_got_tax = false;
                        for ($prod_cnt = 0; $prod_cnt < count($po_obj->po_items); $prod_cnt++) {
                            if (abs($po_obj->po_items[$prod_cnt]['products_tax']) > 0) {
                                $product_got_tax = true;
                                break;
                            }
                        }

                        $total_colspan = 6;
                        if ($product_got_tax)
                            $total_colspan++;
                        if ($po_obj->po_info['po_status'] == PARTIAL_RECEIVE_STATUS)
                            $total_colspan += 4;

                        echo "<order_detail order_id='" . $order_ids_array[$po_cnt] . "'><![CDATA[";
                        echo '	<table width="100%" border="0" cellspacing="1" cellpadding="0">
				  <tr>
                                    <td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td></tr>';
                        if ($po_obj->po_info['po_status'] == PARTIAL_RECEIVE_STATUS) {
                            echo '<tr>
                                    <td class="subInvoiceBoxHeading" colspan="2" width="40%" >' . TABLE_HEADING_PRODUCTS . '</td>
                                    <td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_QUANTITY . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_PRICE . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                            if ($product_got_tax) {
                                echo '<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                            }
                            echo '</tr>';
                        } else {
                            echo '<tr>
                                    <td class="subInvoiceBoxHeading" colspan="2" width="46%" >' . TABLE_HEADING_PRODUCTS . '</td>
                                    <td class="subInvoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_QUANTITY . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_PRICE . '</td>
                                    <td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                            if ($product_got_tax) {
                                echo '<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                            }
                            echo '</tr>';
                        }
                        echo '<tr>
                                <td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; "></td></tr><tr><td></td></tr>';

                        for ($i = 0, $n = sizeof($po_obj->po_items); $i < $n; $i++) {
                            $product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, 
                                                        p.products_bundle_dynamic, p.products_bundle, p.products_skip_inventory, 
                                                        pc.categories_id 
                                                        FROM " . TABLE_PRODUCTS . " AS p 
                                                        LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                            ON p.products_id=pc.products_id 
                                                        WHERE p.products_id = '" . $po_obj->po_items[$i]['products_id'] . "' AND pc.products_is_link=0 ";
                            $product_info_result_sql = tep_db_query($product_info_select_sql);
                            $row_maincat = tep_db_fetch_array($product_info_result_sql);

                            $prod_maincatpath = tep_output_generated_category_path_sq($row_maincat['categories_id']);

                            $ordered_qty_text = $po_obj->po_items[$i]['suggest_qty'];

                            echo '<tr>
                                    <td class="invoiceRecords" valign="top" colspan="2">' . ($row_maincat['products_id'] ? '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($row_maincat['categories_id']) . '&pID=' . $po_obj->po_items[$i]['products_id'] . '&selected_box=catalog&' . $_REQUEST['SID']) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $po_obj->po_items[$i]['products_id'] . '">' . strip_tags($po_obj->po_items[$i]['products_name']) . '</a>' : "<b>" . strip_tags($po_obj->po_items[$i]['products_name']) . "</b>") . '<br>';
                            if ($row_maincat['products_id'] == "") {
                                echo "**--This product is no longer existing in db--**";
                            } else {
                                echo '<span class="categoryPath">[' . $prod_maincatpath . ']</span>';
                            }

                            echo '  </td>
                                    <td class="invoiceRecords" align="center" valign="top">' . $ordered_qty_text . '</td>' . "\n";
                            
                            echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['products_unit_price'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            if ($product_got_tax) {
                                echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['subtotal'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                                echo '<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format(tep_add_tax($po_obj->po_items[$i]['products_unit_price'], $po_obj->po_items[$i]['products_tax']) * ($po_obj->po_items[$i]['suggest_qty']), $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            } else {
                                echo '<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->currencies[$po_obj->po_info['currency']]['symbol_left'] . number_format($po_obj->po_items[$i]['subtotal'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_places'], $currencies->currencies[$po_obj->po_info['currency']]['decimal_point'], $currencies->currencies[$po_obj->po_info['currency']]['thousands_point']) . $currencies->currencies[$po_obj->po_info['currency']]['symbol_right'] . '</b></td>' . "\n";
                            }
                            echo '</tr>';
                        }

                        echo '<tr valign="bottom">
				<td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
                            </tr>';
                        if (tep_not_null($po_remark)) {
                            echo '<tr>
                                    <td colspan="' . ($total_colspan - 1) . '" class="orderRemarkSelectedRow">
					<table cellpadding="3">
                                            <tr>
						<td class="dataTableContent">' .
                            nl2br($po_remark) . '<br>';
                            if (tep_not_null($remark_by_admin_email_address))
                                echo "(" . TEXT_REMARK_MODIFIED_BY . $remark_by_admin_email_address . ")";
                            echo '		</td>
                                            </tr>												
					</table>
                                    </td>
                                    <td>&nbsp;</td>
				</tr>';
                        }
                        echo '  <tr valign="bottom">
				  <td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
				</tr>
			    </table>';
                        echo "]]></order_detail>";
                    } else {
                        echo "<order_error>Record not found:" . $order_ids_array[$po_cnt] . "</order_error>";
                    }
                }
                echo "</order_info>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Empty Purchase Order ID provided.</result>";
            }

            break;

        case "product_price_history":
            if (tep_not_null($_REQUEST['p_id']) && tep_not_null($_REQUEST['o_id'])) {
                echo '<cost_history_html><![CDATA[';
                echo '<table width="100%" cellspacing="1" cellpadding="0" border="0">';
                echo '	<tr><td></td><td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td></tr>';
                echo '	<tr>
							<td width="15%" class="subInvoiceBoxHeading">&nbsp;</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_NO . '</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_DATE . '</td>
							<td width="15%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_SUPPLIER . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_UNIT_COST . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_STATUS . '</td>
							<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_COT_PRICE_PO_ISSUE_BY . '</td>
						</tr>';
                echo '	<tr><td></td><td colspan="6"><div style="border-bottom: 1px solid #996600; "></td></tr>';

                $po_currency_sql = "SELECT currency FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($_REQUEST['o_id']) . "'";
                $po_currency_result = tep_db_query($po_currency_sql);
                $po_currency_row = tep_db_fetch_array($po_currency_result);

                $past_po_cost_select_sql = "SELECT po.purchase_orders_ref_id, po.purchase_orders_id, po.supplier_id, po.supplier_name, 
                                                po.purchase_orders_issue_date, po.purchase_orders_status, po.currency, pop.products_unit_price 
											FROM " . TABLE_PURCHASE_ORDERS . " AS po
											INNER JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop 
												ON (po.purchase_orders_id = pop.purchase_orders_id AND pop.products_id = '" . tep_db_input($_REQUEST['p_id']) . "') 
											WHERE po.purchase_orders_id <> '" . tep_db_input($_REQUEST['o_id']) . "'
                                                AND po.purchase_orders_type = 1
                                                AND po.purchase_orders_status = 3
											ORDER BY po.purchase_orders_issue_date DESC LIMIT 4";
                $past_po_cost_result_sql = tep_db_query($past_po_cost_select_sql);
                while ($past_po_cost_row = tep_db_fetch_array($past_po_cost_result_sql)) {
                    $po_status_select_sql = "SELECT purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE purchase_orders_status_id = '" . $past_po_cost_row['purchase_orders_status'] . "' AND language_id='" . (int) $languages_id . "'";
                    $po_status_result_sql = tep_db_query($po_status_select_sql);
                    $po_status_row = tep_db_fetch_array($po_status_result_sql);

                    $po_status_hist_select_sql = "SELECT changed_by FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " WHERE purchase_orders_id = '" . tep_db_input($past_po_cost_row['purchase_orders_id']) . "' AND purchase_orders_status_id = 1";
                    $po_status_hist_result_sql = tep_db_query($po_status_hist_select_sql);
                    $po_status_hist_row = tep_db_fetch_array($po_status_hist_result_sql);

                    $po_cost_in_po_currency = $currencies->advance_currency_conversion($past_po_cost_row['products_unit_price'], $past_po_cost_row['currency'], $po_currency_row['currency']);

                    echo '	<tr height="20">';
                    echo '		<td class="invoiceRecords" valign="top">&nbsp;</td>';
                    echo '		<td class="invoiceRecords" valign="top"><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id=' . $past_po_cost_row['purchase_orders_id'] . '&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">' . $past_po_cost_row['purchase_orders_ref_id'] . '</a></td>';
                    echo '		<td class="invoiceRecords" valign="top" nowrap="">' . $past_po_cost_row['purchase_orders_issue_date'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top"><a href="' . tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'po_supplier_id_search=' . $past_po_cost_row['supplier_id'] . '&action=id_search&selected_box=po', 'NONSSL') . '" target="_blank">' . $past_po_cost_row['supplier_name'] . '</a></td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $currencies->currencies[$po_currency_row['currency']]['symbol_left'] . number_format($po_cost_in_po_currency, $currencies->currencies[$po_currency_row['currency']]['decimal_places'], $currencies->currencies[$po_currency_row['currency']]['decimal_point'], $currencies->currencies[$po_currency_row['currency']]['thousands_point']) . $currencies->currencies[$po_currency_row['currency']]['symbol_right'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $po_status_row['purchase_orders_status_name'] . '</td>';
                    echo '		<td class="invoiceRecords" valign="top">' . $po_status_hist_row['changed_by'] . '</td>';
                    echo '	</tr>';
                }
                echo '	<tr><td colspan="7">&nbsp;</td></tr>';
                echo '</table>';
                echo ']]></cost_history_html>';
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Record not found!</result>";
            }

            break;
            
        case "get_dtu_supplier":
            
            // Set sell currency
            $sell_curr = isset($_REQUEST['c_id']) ? $_REQUEST['c_id'] : '';
            // Set topup ID
            $tp_id = isset($_REQUEST['tp_id']) ? $_REQUEST['tp_id'] : '';
            // Initialize Publishers class
            $publisher_obj = new publishers($_REQUEST['pub_id']);
            // Get DTU list
            $dtu_array = $publisher_obj->get_dtu_list($_REQUEST['l_type'], $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date'], $_REQUEST['page'], $_REQUEST['dtu_sort'], $_REQUEST['sort_option']);
            // Get DTU paging
            $dtu_pages = $publisher_obj->get_dtu_paging($_REQUEST['l_type'], $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
            // Set stock ptice type
            $stock_price_type = array(array('id' => '%', 'text' => '%'), array('id' => '$', 'text' => '$'));
            
            // Var declaration
            $html_res = '';
            $dtu_table_header = '';
            $dtu_table = '';
            $dtu_table_footer = '';
            $dtu_search_button = '';
            
            // Bulk status change to the listed item
            $bulk_status_options = '';
            $group_checked= '';
            if ($_REQUEST['l_type'] == 'add_dtu_cb') {
                $bulk_status_array[] = array('id' => '0', 'text' => 'Bulk Status Change...');
                $bulk_status_array[] = array('id' => '1', 'text' => 'Charge Back');
                $bulk_status_array[] = array('id' => '2', 'text' => 'Debit Note');
                $bulk_status_array[] = array('id' => '3', 'text' => 'DTU Issue');
                if ($allow_set_dtu_status_permission) {
                    $bulk_status_array[] = array('id' => '11', 'text' => 'Paid');
                    $bulk_status_array[] = array('id' => '12', 'text' => 'Un-Paid');
                }

                $bulk_status_optons = tep_draw_pull_down_menu('bulk_status_select', $bulk_status_array, '0', 'id="bulk_status_select" onChange="setCbBulkChecked(this); refreshDTUTotal();"', false);
            } else {
                $group_checked = tep_draw_checkbox_field('gselect_api', '',true,''," id=\"gselect_api\" onClick=\"toggleCheckBox(this)\" ");
            }
            
            // Set dtu list table
            $sort_array = setHeaderUrlSortDTUList($_REQUEST['dtu_sort'], $_REQUEST['sort_option']);
            $default_url_topup_date = setDefaultURLSortDTUList('topup_date');
            $default_url_prod_id = setDefaultURLSortDTUList('prod_id');
            $default_url_prod_name = setDefaultURLSortDTUList('prod_name');
            $default_url_order_num = setDefaultURLSortDTUList('order_num');
            $default_url_topup_id = setDefaultURLSortDTUList('topup_id');
            
            switch ($_REQUEST['dtu_sort']) {
                case 'topup_date':
                    $sort_array_topup_date = $sort_array;
                    break;
                case 'prod_id':
                    $sort_array_prod_id = $sort_array;
                    break;
                case 'prod_name':
                    $sort_array_prod_name = $sort_array;
                    break;
                case 'order_num':
                    $sort_array_order_num = $sort_array;
                    break;
                case 'topup_id':
                    $sort_array_topup_id = $sort_array;
                    break;
            }
            
            // START Table Header
            $dtu_table_header .= '<table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
            $dtu_table_header .= '  <tbody>';
            $dtu_table_header .= '      <tr>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="4%">#</td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="4%">' . TABLE_HEADING_DTU_SELECT . $group_checked . "</td>";
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_topup_date['sort_url']) ? $default_url_topup_date : $sort_array_topup_date['sort_url']) . '>' . TABLE_HEADING_DTU_DATE . $sort_array_topup_date['sort_heading'] . '</a></td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_prod_id['sort_url']) ? $default_url_prod_id : $sort_array_prod_id['sort_url']) . '>' . TABLE_HEADING_DTU_PRODUCT_ID . $sort_array_prod_id['sort_heading'] . '</a></td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" width="16%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_prod_name['sort_url']) ? $default_url_prod_name : $sort_array_prod_name['sort_url']) . '>' . TABLE_HEADING_DTU_PRODUCT_NAME . $sort_array_prod_name['sort_heading'] . '</a></td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_order_num['sort_url']) ? $default_url_order_num : $sort_array_order_num['sort_url']) . '>' . TABLE_HEADING_DTU_ORDER_NO . $sort_array_order_num['sort_heading'] . '</a></td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_topup_id['sort_url']) ? $default_url_topup_id : $sort_array_topup_id['sort_url']) . '>' . TABLE_HEADING_DTU_TOP_UP_ID . $sort_array_topup_id['sort_heading'] . '</a></td>';
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_DTU_QUANTITY . "</td>";
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">' . sprintf(TABLE_HEADING_DTU_SRP, $_REQUEST['c_id']) . "</div></td>";
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">' . sprintf(TABLE_HEADING_DTU_AMOUNT, $_REQUEST['c_id']) . "</div></td>";
            $dtu_table_header .= '          <td class="invoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_DTU_BILLING_STATUS . $bulk_status_optons ."</td>";
            $dtu_table_header .= '      </tr>';
            // END Table Header
            
            if ($dtu_array) {
                // DTU Item Numbering
                if ($_REQUEST['page'] > 1) {
                    //$row_count = (($_REQUEST['page']-1) == 0) ? '' : ($_REQUEST['page']-1) * 200;
                    $row_count = $_REQUEST['row_num'];
                } else {
                    $row_count = 0;
                }

                $prod_type = array();
                foreach ($dtu_array as $dtu_data) {
                    // Skip data that has been use as Charge Back or Debit Note
                    if ($dtu_data['cb_deduction_status'] == '1') {
                        continue;
                    }

                    if (!isset($prod_type[$dtu_data["products_id"]])) {
                        $_sql = tep_db_query("SELECT products_type FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $dtu_data["products_id"]);
                        $_row = tep_db_fetch_array($_sql);
                        $prod_type[$dtu_data["products_id"]] = (isset($_row["products_type"]) ? $_row["products_type"] : null);
                    }

                    if ($prod_type[$dtu_data["products_id"]] == 3) {
                        // virtual listing
                        $_sql = tep_db_query("SELECT orders_products_id, products_name FROM " . TABLE_ORDERS_PRODUCTS . " WHERE products_id = " . $dtu_data["products_id"] . " AND orders_id = " . $dtu_data["orders_id"]);
                        $_row = tep_db_fetch_array($_sql);
                        $products_name = (isset($_row["products_name"]) ? $_row["products_name"] : tep_get_products_name($dtu_data['products_id']));
                        
                        if (isset($_row["orders_products_id"])) {
                            $_sql2 = tep_db_query("SELECT currency_code, currency_settle_amount FROM " . TABLE_ORDERS_TOP_UP . " WHERE orders_products_id = " . $_row["orders_products_id"]);
                            $_row2 = tep_db_fetch_array($_sql2);
                            $selling_price = getVirtualProductSellPrice($_row2['currency_code'], $_row2['currency_settle_amount'], $sell_curr);
                         } else {
                            $selling_price = getProductSellPrice($dtu_data['products_id'], $sell_curr);
                        }
                    } else {
                        $products_name = tep_get_products_name($dtu_data['products_id']);
                        $selling_price = getProductSellPrice($dtu_data['products_id'], $sell_curr);
                    }
                    $dtu_quantity = number_format($dtu_data['products_delivered_quantity'],0);
                    $total_amount = round($selling_price, 4) * $dtu_quantity;
                    $final_po_price = 0;
                    
                    $row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
                    
                    // Set withdrawal status text display
                    switch ($dtu_data['withdrawal_status']) {
                        case '0':
                            $dtu_withdrawal_text = 'unpaid';
                            break;
                        case '1':
                            $dtu_withdrawal_text = 'processing';
                            break;
                        case '3':
                            $dtu_withdrawal_text = 'approved';
                            break;
                        case '7':
                            $dtu_withdrawal_text = 'canceled';
                            break;
                        case '11':
                            $dtu_withdrawal_text = 'paid<br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.getPoIdPaidDtu($dtu_data['top_up_id']).'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$dtu_data['withdrawal_ref'].'</a>';
                            break;
                    }
                    
                    // Check DTU CB Status (overide withdrwal status text)
                    switch ($dtu_data['cb_status']) {
                        case '1':
                            $dtu_withdrawal_text = 'charge back';
                            break;
                        case '2':
                            $dtu_withdrawal_text = 'debit note<br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.getPoIdPaidDtu($dtu_data['top_up_id']).'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$dtu_data['withdrawal_ref'].'</a>';
                            $po_price_info = $publisher_obj->get_dtu_po_final_price($dtu_data['purchase_orders_id'], $dtu_data['products_id']);
                            $round_selling_price = round($po_price_info['products_selling_price'], 4);
                            $round_unit_price_value = round($po_price_info['products_unit_price_value'], 4);
                            if ($po_price_info['products_unit_price_type'] == '%') {
                                $final_po_price = ($round_selling_price - ($round_selling_price * $round_unit_price_value / 100)) * $dtu_quantity;
                            } else {
                                $final_po_price = ($round_selling_price - $round_unit_price_value) * $dtu_quantity;
                            }
                            $gst_value = ($po_price_info['purchase_orders_gst_value']) ? $po_price_info['purchase_orders_gst_value'] : 0;
                            $final_po_price = $final_po_price + ($gst_value / 100 * $final_po_price);
                    
                            if ($sell_curr == DEFAULT_CURRENCY) {
                                if ($po_price_info['currency'] == DEFAULT_CURRENCY) {
                                    $total_amount = $final_po_price;
                                } else {
                                    $base_rate = 1;
                                    if ($currencies->currencies[$po_price_info['currency']]['value'] > 0) {
                                        $base_rate = 1 / $currencies->currencies[$po_price_info['currency']]['value'];
                                    }
                                    $total_amount = $base_rate * $final_po_price;
                                }
                            } else {
                                if ($po_price_info['currency'] == $sell_curr) {
                                    $total_amount = $final_po_price;
                                } else {
                                    $total_amount = $currencies->advance_currency_conversion($final_po_price, $po_price_info['currency'], $sell_curr, false);
                                }
                            }
                            break;
                        case '3':
                            $dtu_withdrawal_text = 'dtu issue';
                            break;
                    }
                    
                    if ($_REQUEST['l_type'] == 'add_dtu_cb') { //Process data for DTU CB form
                    
                        $dtu_status_array = array();
                        $dtu_withdrawal_checked = false;
                        
                        if($dtu_data['withdrawal_status'] == '0' || $dtu_data['withdrawal_status'] == '7') {
                            $dtu_status_array[] = array('id' => '0', 'text' => 'Un-Paid');
                            $dtu_status_array[] = array('id' => '1', 'text' => 'Charge Back');
                            if ($allow_set_dtu_status_permission) {
                                $dtu_status_array[] = array('id' => '11', 'text' => 'Paid');
                            }
                        } else if ($dtu_data['withdrawal_status']=='11') {
                            $dtu_status_array[] = array('id' => '0', 'text' => 'Paid');
                            $dtu_status_array[] = array('id' => '2', 'text' => 'Debit Note');
                            if ($allow_set_dtu_status_permission) {
                                $dtu_status_array[] = array('id' => '12', 'text' => 'Un-Paid');
                            }
                        } else {
                            $dtu_withdrawal_checked_state = 'disabled';
                        }

                        // cont. Dropdown list for CB from
                        $dtu_status_array[] = array('id' => '3', 'text' => 'DTU Issue');
                        
                        if ($_REQUEST['dtu_reload'] == 'true') {
                            $select_data = $dtu_data['cb_temp_status'];
                            $dtu_withdrawal_checked = ($dtu_data['cb_temp_status'] == '1' || $dtu_data['cb_temp_status'] == '2' || $dtu_data['cb_temp_status'] == '3' || $dtu_data['cb_temp_status'] != $dtu_data['cb_status']) ? true : false;
                        } else {
                            $select_data = $dtu_data['cb_status'];
                        }
                        
                        // Dropdown list for CB form (0-current DTU withdrwal status, 1-Charge Back, 2-Debit Note, 3-DTU Issue)
                        $withdrawable_status_optons = tep_draw_pull_down_menu('dtu_status_select_'.$dtu_data['top_up_id'], $dtu_status_array, $select_data, 'id="dtu_status_select_' . $dtu_data['top_up_id'] . '" onChange="setCBDTUChecked('. $dtu_data['top_up_id'] .'); refreshDTUTotal();"', true);
                        
                        $dtu_withdrawal_status = $withdrawable_status_optons;
                        
                        if($dtu_data['withdrawal_status']=='0' || $dtu_data['withdrawal_status']=='7' || $dtu_data['withdrawal_status']=='11') {
                            $dtu_withdrawal_checked_state = '';
                        } else {
                            $dtu_withdrawal_checked_state = 'disabled';
                            $dtu_withdrawal_status = $dtu_withdrawal_text;
                        }
                        
                    } else { // Process data for DTU Payment Request form
                        
                        if($dtu_data['withdrawal_status'] == '0' || $dtu_data['withdrawal_status'] == '7') {
                            $dtu_withdrawal_checked = true;
                            $dtu_withdrawal_checked_state = '';
                        } else if ($dtu_data['withdrawal_status']=='11') {
                            $dtu_withdrawal_checked = false;
                            $dtu_withdrawal_checked_state = 'disabled';
                        } else {
                            $dtu_withdrawal_checked = false;
                            $dtu_withdrawal_checked_state = 'disabled';
                        }

                        if (($dtu_data['cb_status'] == '1' || $dtu_data['cb_status'] == '2')) {
                            $dtu_withdrawal_checked = true;
                            $dtu_withdrawal_checked_state = '';
                        } else if ($dtu_data['cb_status'] == '3') {
                            $dtu_withdrawal_checked = false;
                            $dtu_withdrawal_checked_state = 'disabled';
                        }
                        
                        if ($_REQUEST['dtu_reload'] == 'true') {
                            $dtu_withdrawal_checked = ($dtu_data['withdrawal_temp_status'] == '1') ? true : false;
                        }
                        
                        $dtu_withdrawal_status = $dtu_withdrawal_text;
                        
                    }
                    
                    $dtu_table .= '  <tr class="'.$row_style.'" id="' . ($row_count + 1) . '">';
                    $dtu_table .= '      <td class="main" align="center">' . ($row_count + 1) . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . tep_draw_checkbox_field('dtu_items_top_up_id[]', $dtu_data['top_up_id'], $dtu_withdrawal_checked, '', 'class="dtu_items_top_up_id" id="dtu_items_top_up_id_'.$dtu_data['top_up_id'].'" onClick="return calculatePayDTUTotal(this, \'' . $dtu_data['top_up_id'] . '\')" '.$dtu_withdrawal_checked_state.'') . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_data['topup_time'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center"><div id="dtu_product_id_' . $dtu_data['top_up_id'] . '">' . ((isset($dtu_data['sub_products_id']) && !empty($dtu_data['sub_products_id'])) ? $dtu_data['sub_products_id'] : $dtu_data['products_id']) . '</div>';

                    // Set main product id hidden
                    if (isset($dtu_data['sub_products_id']) && !empty($dtu_data['sub_products_id'])) {
                        $dtu_table .= tep_draw_hidden_field('dtu_main_products_id_' . $dtu_data['top_up_id'], $dtu_data['products_id'], 'id="dtu_main_products_id_' . $dtu_data['top_up_id'] . '"');
                    }

                    $dtu_table .= '</td>';
                    
                    $dtu_table .= '      <td class="main">' . $products_name . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_data['orders_id'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_data['top_up_id'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center"><div id="dtu_qty_' . $dtu_data['top_up_id'] . '">' . $dtu_quantity . '</div></td>';
                    $dtu_table .= '      <td class="main" align="center"><div id="sell_price_'.$dtu_data['top_up_id'].'_div">' . number_format($selling_price, 4, $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']) . '</div></td>';
                    $dtu_table .= '      <td class="main" align="center"><div id="subtotal_'.$dtu_data['top_up_id'].'_div">' . number_format($total_amount, 4, $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']) . '</div></td>';
                    $dtu_table .= '      <td class="main" align="center">' . tep_draw_hidden_field('dtu_cb_status_'.$dtu_data['top_up_id'], $dtu_data['cb_status'], 'id="dtu_cb_status_'.$dtu_data['top_up_id'].'"') . $dtu_withdrawal_status . '</td>';
                    $dtu_table .= '  </tr>';
                    
                    $row_count++;
                }
            }
            else {
                $dtu_table .= '  <tr>';
                $dtu_table .= '      <td colspan="9">';
                $dtu_table .= '          No DTU Available';
                $dtu_table .= '      </td>';
                $dtu_table .= '  </tr>';
            }
            
            // Set load more dtu button
            
            if ($dtu_pages['pages'] > 1 && $_REQUEST['page'] < $dtu_pages['pages']) {
                $new_page = $_REQUEST['page'] + 1;
                $dtu_table .= '<tr>';
                $dtu_table .= ' <td class="invoiceBoxHeading" align="center" colspan="11" id="tr_load">';
                $dtu_table .= '     <a href="#load_more" id="load_more" onclick="searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, \'' . $_REQUEST['dtu_reload'] . '\', \'' . $new_page . '\', \'' . $_REQUEST['dtu_sort'] . '\', \'' . $_REQUEST['sort_option'] . '\');"><p class="pageHeading">LOAD MORE</p></a>';
                $dtu_table .= ' </td>';
                $dtu_table .= '</tr>';
            }
            
            $dtu_table_footer .= '  </tbody>';
            $dtu_table_footer .= '</table>';
            
            // Set table content
            if ($_REQUEST['page'] == 1) {
                $html_res .= $dtu_table_header;
                $html_res .= $dtu_table;
                $html_res .= $dtu_table_footer;
            } else {
                $html_res .= $dtu_table;
            }

            $dtu_search_button = '<div id="div_search_dtu_button">' . tep_button(BUTTON_SEARCH_DTU_RESET, ALT_BUTTON_SEARCH_DTU_RESET, '', 'onclick="if(checkDTUform(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier)){searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, \'false\', \'\', \'topup_date\', \'asc\');}"') . '</div>';
            
            echo "<result>";
            echo "<dtu_list><![CDATA[";
            echo $html_res;
            echo "]]></dtu_list>";
            echo "<dtu_search_button><![CDATA[";
            echo $dtu_search_button;
            echo "]]></dtu_search_button>";
            echo "</result>";
            break;
            
        case "get_dtu_list_report":
            
            $pub_id = isset($_REQUEST['pub_id']) ? $_REQUEST['pub_id'] : '';
            $sell_curr = isset($_REQUEST['c_id']) ? $_REQUEST['c_id'] : '';
            $prod_id = isset($_REQUEST['prd_id']) ? $_REQUEST['prd_id'] : '';
            $order_id = isset($_REQUEST['ord_id']) ? $_REQUEST['ord_id'] : '';
            $tp_id = isset($_REQUEST['tp_id']) ? $_REQUEST['tp_id'] : '';
            
            $publisher_obj = new publishers($pub_id);
            
            // get dtu list
            $dtu_array = $publisher_obj->get_dtu_list_report($_REQUEST['list_type'], $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date'], $_REQUEST['page'], $_REQUEST['dtu_sort'], $_REQUEST['sort_option']);
            //get dtu paging
            $dtu_pages = $publisher_obj->get_dtu_paging_report($_REQUEST['list_type'], $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
            // get dtu total
            if ($_REQUEST['list_type'] == 'unpaid') {
                if ($_REQUEST['page'] == 1) {
                    $dtu_pages_paid = $publisher_obj->get_dtu_paging_report('paid', $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
                    $dtu_pages_cb = $publisher_obj->get_dtu_paging_report('charge_back', $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
                    $dtu_pages_dn = $publisher_obj->get_dtu_paging_report('debit_note', $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
                    $dtu_pages_issue = $publisher_obj->get_dtu_paging_report('dtu_issue', $prod_id, $order_id, $tp_id, $_REQUEST['s_date'], $_REQUEST['e_date']);
                }
            }
            
            $html_res = '';
            $dtu_tab_header = '';
            $dtu_table_header = '';
            $dtu_table = '';
            $dtu_table_footer = '';
            $dtu_search_button = '';
            $sort_array_topup_date = array();
            $sort_array_prod_id = array();
            $sort_array_pub_name = array();
            $sort_array_prod_name = array();
            $sort_array_order_num = array();
            $sort_array_topup_id = array();
            
            // Set tab header
            $dtu_tab_header .= '<ul>';
            $dtu_tab_header .= '    <li>';
            $dtu_tab_header .= '        <a href="#tab_unpaid" onclick="refreshDTUTotalReport(\'unpaid\');"><span>Un-Paid (' . $dtu_pages["total"] . ')</span></a>';
            $dtu_tab_header .= '    </li>';
            $dtu_tab_header .= '    <li>';
            $dtu_tab_header .= '        <a href="#tab_paid" onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'paid\', \'1\', \'topup_date\', \'asc\');refreshDTUTotalReport(\'paid\');"><span>paid (' . $dtu_pages_paid["total"] . ')</span></a>';
            $dtu_tab_header .= '    </li>';
            $dtu_tab_header .= '    <li>';
            $dtu_tab_header .= '        <a href="#tab_charge_back" onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'charge_back\', \'1\', \'topup_date\', \'asc\');refreshDTUTotalReport(\'charge_back\');"><span>Charge Back (' . $dtu_pages_cb["total"] . ')</span></a>';
            $dtu_tab_header .= '    </li>';
            $dtu_tab_header .= '    <li>';
            $dtu_tab_header .= '        <a href="#tab_debit_note" onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'debit_note\', \'1\', \'topup_date\', \'asc\');refreshDTUTotalReport(\'debit_note\');"><span>Debit Note (' . $dtu_pages_dn["total"] . ')</span></a>';
            $dtu_tab_header .= '    </li>';
            $dtu_tab_header .= '    <li>';
            $dtu_tab_header .= '        <a href="#tab_dtu_issue" onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'dtu_issue\', \'1\', \'topup_date\', \'asc\');refreshDTUTotalReport(\'dtu_issue\');"><span>DTU Issue (' . $dtu_pages_issue["total"] . ')</span></a>';
            $dtu_tab_header .= '    </li>';
            $dtu_tab_header .= '</ul>';
            
            // Set dtu list table
            $sort_array = setHeaderUrlSorting($_REQUEST['dtu_sort'], $_REQUEST['sort_option'], $_REQUEST['list_type']);
            $default_url_topup_date = setDefaultURLSorting($_REQUEST['list_type'], 'topup_date');
            $default_url_prod_id = setDefaultURLSorting($_REQUEST['list_type'], 'prod_id');
            $default_url_pub_name = setDefaultURLSorting($_REQUEST['list_type'], 'pub_name');
            $default_url_prod_name = setDefaultURLSorting($_REQUEST['list_type'], 'prod_name');
            $default_url_order_num = setDefaultURLSorting($_REQUEST['list_type'], 'order_num');
            $default_url_topup_id = setDefaultURLSorting($_REQUEST['list_type'], 'topup_id');
            
            switch ($_REQUEST['dtu_sort']) {
                case 'topup_date':
                    $sort_array_topup_date = $sort_array;
                    break;
                case 'prod_id':
                    $sort_array_prod_id = $sort_array;
                    break;
                case 'pub_name':
                    $sort_array_pub_name = $sort_array;
                    break;
                case 'prod_name':
                    $sort_array_prod_name = $sort_array;
                    break;
                case 'order_num':
                    $sort_array_order_num = $sort_array;
                    break;
                case 'topup_id':
                    $sort_array_topup_id = $sort_array;
                    break;
            }
            
            $dtu_table_header .= '  <table id="' . $_REQUEST['list_type'] . '_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
            $dtu_table_header .= '      <tbody>';
            $dtu_table_header .= '          <tr>';
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="4%">#</td>';
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" '. (empty($sort_array_topup_date['sort_url']) ? $default_url_topup_date : $sort_array_topup_date['sort_url']) . '>' . TABLE_HEADING_DTU_DATE . $sort_array_topup_date['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" ' . (empty($sort_array_prod_id['sort_url']) ? $default_url_prod_id : $sort_array_prod_id['sort_url']) . '>' . TABLE_HEADING_DTU_PRODUCT_ID . $sort_array_prod_id['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" width="10%"><a style="font-weight:bold;" href="#" ' . (empty($sort_array_pub_name['sort_url']) ? $default_url_pub_name : $sort_array_pub_name['sort_url']) . '>' . TABLE_HEADING_DTU_PUBLISHER_NAME . $sort_array_pub_name['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" width="10%"><a style="font-weight:bold;" href="#" ' . (empty($sort_array_prod_name['sort_url']) ? $default_url_prod_name : $sort_array_prod_name['sort_url']) . '>' . TABLE_HEADING_DTU_PRODUCT_NAME . $sort_array_prod_name['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" ' . (empty($sort_array_order_num['sort_url']) ? $default_url_order_num : $sort_array_order_num['sort_url']) . '>' . TABLE_HEADING_DTU_ORDER_NO . $sort_array_order_num['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="8%"><a style="font-weight:bold;" href="#" ' . (empty($sort_array_topup_id['sort_url']) ? $default_url_topup_id : $sort_array_topup_id['sort_url']) . '>' . TABLE_HEADING_DTU_TOP_UP_ID . $sort_array_topup_id['sort_heading'] . "</a></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_DTU_QUANTITY . "</td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">' . sprintf(TABLE_HEADING_DTU_SRP, $_REQUEST['c_id']) . "</div></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">' . sprintf(TABLE_HEADING_DTU_AMOUNT, $_REQUEST['c_id']) . "</div></td>";
            $dtu_table_header .= '              <td class="invoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_DTU_BILLING_STATUS . "</td>";
            $dtu_table_header .= '          </tr>';
            
            // Set dtu list table content
            if ($dtu_array) {
                
                // DTU Item Numbering
                if ($_REQUEST['page'] > 1) {
                    $row_count = (($_REQUEST['page']-1) == 0) ? '' : ($_REQUEST['page']-1) * 200;
                } else {
                    $row_count = 0;
                }

                $prod_type = array();
                foreach ($dtu_array as $dtu_data) {
                    if (!isset($prod_type[$dtu_data["products_id"]])) {
                        $_sql = tep_db_query("SELECT products_type FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $dtu_data["products_id"]);
                        $_row = tep_db_fetch_array($_sql);
                        $prod_type[$dtu_data["products_id"]] = (isset($_row["products_type"]) ? $_row["products_type"] : null);
                    }

                    if ($prod_type[$dtu_data["products_id"]] == 3) {
                        // virtual listing
                        $_sql = tep_db_query("SELECT orders_products_id, products_name FROM " . TABLE_ORDERS_PRODUCTS . " WHERE products_id = " . $dtu_data["products_id"] . " AND orders_id = " . $dtu_data["orders_id"]);
                        $_row = tep_db_fetch_array($_sql);
                        $products_name = (isset($_row["products_name"]) ? $_row["products_name"] : tep_get_products_name($dtu_data['products_id']));

                        if (isset($_row["orders_products_id"])) {
                            $_sql2 = tep_db_query("SELECT currency_code, currency_settle_amount FROM " . TABLE_ORDERS_TOP_UP . " WHERE orders_products_id = " . $_row["orders_products_id"]);
                            $_row2 = tep_db_fetch_array($_sql2);
                            $selling_price = getVirtualProductSellPrice($_row2['currency_code'], $_row2['currency_settle_amount'], $sell_curr);
                         } else {
                            $selling_price = getProductSellPrice($dtu_data['products_id'], $sell_curr);
                        }
                    } else {
                        $products_name = $dtu_data['products_name'];
                        $selling_price = getProductSellPrice($dtu_data['products_id'], $sell_curr);
                    }

                    $dtu_quantity = number_format($dtu_data['products_delivered_quantity'],0);
                    $total_amount = round($selling_price, 4) * $dtu_quantity;
                    $final_po_price = 0;
                    
                    $row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
                    
                    // Set withdrawal status text display
                    switch ($dtu_data['withdrawal_status']) {
                        case '0':
                            $dtu_withdrawal_text = 'unpaid';
                            break;
                        case '1':
                            $dtu_withdrawal_text = 'processing';
                            break;
                        case '3':
                            $dtu_withdrawal_text = 'approved';
                            break;
                        case '7':
                            $dtu_withdrawal_text = 'calceled';
                            break;
                        case '11':
                            $dtu_withdrawal_text = 'paid<br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.getPoIdPaidDtu($dtu_data['top_up_id']).'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$dtu_data['withdrawal_ref'].'</a>';
                            break;
                    }
                    
                    // Check DTU CB Status (overide withdrwal status text)
                    switch ($dtu_data['cb_status']) {
                        case '1':
                            $cb_additional_text = '';
                            $cb_po_info = getPoIdCBDtu($dtu_data['top_up_id']);
                            if ($dtu_data['cb_deduction_status'] == '1') {
                                $cb_additional_text = '<br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.$cb_po_info['po_id'].'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$cb_po_info['po_ref_id'].'</a>';
                            }
                            $dtu_withdrawal_text = 'charge back' . $cb_additional_text;
                            break;
                        case '2':
                            $dtu_withdrawal_text = 'debit note<br><a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.getPoIdPaidDtu($dtu_data['top_up_id']).'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$dtu_data['withdrawal_ref'].'</a>';
                            $po_price_info = $publisher_obj->get_dtu_po_final_price($dtu_data['purchase_orders_id'], $dtu_data['products_id']);
                            $round_selling_price = round($po_price_info['products_selling_price'], 4);
                            $round_unit_price_value = round($po_price_info['products_unit_price_value'], 4);
                            if ($po_price_info['products_unit_price_type'] == '%') {
                                $final_po_price = ($round_selling_price - ($round_selling_price * $round_unit_price_value / 100)) * $dtu_quantity;
                            } else {
                                $final_po_price = ($round_selling_price - $round_unit_price_value) * $dtu_quantity;
                            }
                            $gst_value = ($po_price_info['purchase_orders_gst_value']) ? $po_price_info['purchase_orders_gst_value'] : 0;
                            $final_po_price = $final_po_price + ($gst_value / 100 * $final_po_price);
                            $total_amount = $final_po_price;
                            break;
                        case '3':
                            $dtu_withdrawal_text = 'dtu issue';
                            break;
                    }
                    
                    $dtu_table .= '  <tr class="'.$row_style.'">';
                    $dtu_table .= '      <td class="main" align="center">' . ($row_count+1) . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_data['topup_time'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . ((isset($dtu_data['sub_products_id']) && !empty($dtu_data['sub_products_id'])) ? $dtu_data['sub_products_id'] : $dtu_data['products_id']) . '</td>';
                    $dtu_table .= '      <td class="main">' . $dtu_data['publishers_name'] . '</td>';
                    $dtu_table .= '      <td class="main">' . $products_name . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_data['orders_id'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . tep_draw_hidden_field('dtu_' . $_REQUEST['list_type'] . '_top_up_id[]', $dtu_data['top_up_id'], 'class="dtu_' . $_REQUEST['list_type'] . '_top_up_id" id="dtu_' . $_REQUEST['list_type'] . '_top_up_id_'.$dtu_data['top_up_id'].'"') . $dtu_data['top_up_id'] . '</td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_quantity . '</td>';
                    $dtu_table .= '      <td class="main" align="center"><div id="sell_price_'.$dtu_data['top_up_id'].'_div">' . number_format($selling_price, 4, $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']) . '</div></td>';
                    $dtu_table .= '      <td class="main" align="center">' . tep_draw_hidden_field('subtotal_'.$dtu_data['top_up_id'], round($total_amount, 4), 'id="subtotal_'.$dtu_data['top_up_id'].'"') . '<div id="subtotal_'.$dtu_data['top_up_id'].'_div">' . number_format($total_amount, 4, $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']) . '</div></td>';
                    $dtu_table .= '      <td class="main" align="center">' . $dtu_withdrawal_text . '</td>';
                    $dtu_table .= '  </tr>';
                    
                    $row_count++;
                }
            }
            else {
                $dtu_table .= '      <tr>';
                $dtu_table .= '          <td colspan="9">';
                $dtu_table .= '              No DTU Available';
                $dtu_table .= '          </td>';
                $dtu_table .= '      </tr>';
            }
            
            // Set load more dtu button
            if ($dtu_pages['pages'] > 1 && $_REQUEST['page'] < $dtu_pages['pages']) {
                $new_page = $_REQUEST['page'] + 1;
                $dtu_table .= '<tr>';
                $dtu_table .= ' <td class="invoiceBoxHeading" align="center" colspan="11" id="tr_load_' . $_REQUEST['list_type'] . '">';
                $dtu_table .= '     <a href="#load_more_' . $_REQUEST['list_type'] . '" id="load_more_' . $_REQUEST['list_type'] . '" onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \''.$_REQUEST['list_type'].'\', \''.$new_page.'\', \'' . $_REQUEST['dtu_sort'] . '\', \'' . $_REQUEST['sort_option'] . '\');refreshDTUTotalReport(\'' . $_REQUEST['list_type'] . '\');"><p class="pageHeading">LOAD MORE</p></a>';
                $dtu_table .= ' </td>';
                $dtu_table .= '</tr>';
            }
            
            $dtu_table_footer .= '      </tbody>';
            $dtu_table_footer .= '  </table>';
            
            // Set tab content Un-Paid
            if ($_REQUEST['list_type'] == 'unpaid') {
                if ($_REQUEST['page'] == 1) {
                    $html_res .= $dtu_tab_header;
                    $html_res .= '<div id="tab_unpaid" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">';
                    $html_res .= $dtu_table_header;
                    $html_res .= $dtu_table;
                    $html_res .= $dtu_table_footer;
                    $html_res .= '</div>';
                    $html_res .= '<div id="tab_paid" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>';
                    $html_res .= '<div id="tab_charge_back" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>';
                    $html_res .= '<div id="tab_debit_note" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>';
                    $html_res .= '<div id="tab_dtu_issue" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>';
                } else {
                    $html_res .= $dtu_table;
                }
            } else {
                if ($_REQUEST['page'] == 1) {
                    $html_res .= $dtu_table_header;
                    $html_res .= $dtu_table;
                    $html_res .= $dtu_table_footer;
                } else {
                    $html_res .= $dtu_table;
                }
            }
            
            $dtu_search_button .= tep_button(BUTTON_SEARCH_DTU_RESET, ALT_BUTTON_SEARCH_DTU_RESET, '', 'onclick="if(checkDTUReportform(document.dtu_form.start_date, document.dtu_form.end_date)){getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'unpaid\', \'\', \'topup_date\', \'asc\');}"');
            
            echo "<result>";
            echo "<dtu_list><![CDATA[";
            echo $html_res;
            echo "]]></dtu_list>";
            echo "<dtu_search_button><![CDATA[";
            echo $dtu_search_button;
            echo "]]></dtu_search_button>";
            echo "</result>";
            
            break;

        default:
            echo "<result>Unknown request!</result>";

            break;
    }
}

function generateTagSelectionOptions($status, $order_pid_str, $whole_list = false, $apply_tag_sec_only = false) {
    global $language_id;
    $order_ids_array = tep_not_null($order_pid_str) ? explode(',', $order_pid_str) : array();
    echo "<selection>";
    $po_status_id_select_sql = "SELECT purchase_orders_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_status = '" . (int) $status . "'";
    $po_status_id_result_sql = tep_db_query($po_status_id_select_sql);
    if ($po_status_id_row = tep_db_fetch_array($po_status_id_result_sql)) {
        if (!$apply_tag_sec_only) {
            echo "<option index=''><![CDATA[Purchase Order Lists Options ...]]></option>";
        }

        echo "<option index='' " . (!$apply_tag_sec_only ? "disabled='1'" : '') . "><![CDATA[Apply tag:]]></option>";

        $mirror_for_delete_tag_str = '';
        $po_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $po_status_id_row["purchase_orders_status"] . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "' ORDER BY orders_tag_name;";
        $po_tag_result_sql = tep_db_query($po_tag_select_sql);
        while ($po_tag_row = tep_db_fetch_array($po_tag_result_sql)) {
            echo "<option index='" . 'otag_' . $po_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_row["orders_tag_name"] . "]]></option>";
            if ($whole_list == true) {
                $mirror_for_delete_tag_str .= "<option index='" . 'rmtag_' . $po_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_row["orders_tag_name"] . "]]></option>";
            }
        }

        if (!$apply_tag_sec_only) {
            echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";

            if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
                echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                echo $mirror_for_delete_tag_str;
            } else {
                // select the common tags among those selected orders
                if (count($order_ids_array)) {
                    $po_tag_remove_select_sql = "SELECT DISTINCT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PURCHASE_ORDERS . " AS po WHERE po.purchase_orders_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET(otag.orders_tag_id, po.purchase_orders_tag_ids) AND filename='" . FILENAME_DTU_PAYMENT . "' ORDER BY orders_tag_name; ";
                    $po_tag_remove_result_sql = tep_db_query($po_tag_remove_select_sql);
                    if (tep_db_num_rows($po_tag_remove_result_sql) > 0)
                        echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                    while ($po_tag_remove_row = tep_db_fetch_array($po_tag_remove_result_sql)) {
                        echo "<option index='" . 'rmtag_' . $po_tag_remove_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . $po_tag_remove_row["orders_tag_name"] . "]]></option>";
                    }
                }
            }
        }
    }
    echo "</selection>";
}

function generateTagString($po_ids_array) {
    echo "<tag_details>";
    for ($i = 0; $i < count($po_ids_array); $i++) {
        $po_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PURCHASE_ORDERS . " AS po WHERE po.purchase_orders_id  = '" . (int) $po_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, po.purchase_orders_tag_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
        $po_tag_result_sql = tep_db_query($po_tag_select_sql);
        $tags_str = '';
        while ($po_tag_row = tep_db_fetch_array($po_tag_result_sql)) {
            $tags_str .= $po_tag_row["orders_tag_name"] . ', ';
        }
        if (substr($tags_str, -2) == ', ')
            $tags_str = substr($tags_str, 0, -2);
        echo "<order_tags order_id='" . (int) $po_ids_array[$i] . "'><![CDATA[" . $tags_str . "]]></order_tags>";
    }
    echo "</tag_details>";
}

function getProductSellPrice($products_id, $sell_curr) {
    $currencies = new currencies();
    $price_array = $currencies->get_product_prices_info($products_id);
    if ($sell_curr == DEFAULT_CURRENCY) {
        if ($price_array['base_cur'] == DEFAULT_CURRENCY) {
            $selling_price = $price_array['price'];
        } else {
            if (count($price_array['defined_price']) && isset($price_array['defined_price'][DEFAULT_CURRENCY])) {
                $selling_price = $price_array['defined_price'][DEFAULT_CURRENCY];
            } else {
                $base_rate = 1;
                if ($currencies->currencies[$price_array['base_cur']]['value'] > 0) {
                    $base_rate = 1 / $currencies->currencies[$price_array['base_cur']]['value'];
                }
                $selling_price = $base_rate * $price_array['price'];
            }
        }
    } else {
        $price_array = $currencies->get_product_prices_info($products_id);
        if ($price_array['base_cur'] == $sell_curr) {
            $selling_price = $price_array['price'];
        } else {
            if (count($price_array['defined_price']) && isset($price_array['defined_price'][$sell_curr])) {
                $selling_price = $price_array['defined_price'][$sell_curr];
            } else {
                $selling_price = $currencies->advance_currency_conversion($price_array['price'], $price_array['base_cur'], $sell_curr, false);
            }
        }
    }
    
    return $selling_price;
}

function getVirtualProductSellPrice($cur_code, $price, $sell_curr) {
    $currencies = new currencies();
    if ($sell_curr == DEFAULT_CURRENCY) {
        if ($cur_code == DEFAULT_CURRENCY) {
            $selling_price = $price;
        } else {
            $base_rate = 1;
            if ($currencies->currencies[$cur_code]['value'] > 0) {
                $base_rate = 1 / $currencies->currencies[$cur_code]['value'];
            }
            $selling_price = $base_rate * $price;
        }
    } else {
        if ($cur_code == $sell_curr) {
            $selling_price = $price;
        } else {
            $selling_price = $currencies->advance_currency_conversion($price, $cur_code, $sell_curr, false);
        }
    }
    return $selling_price;
}

function getPoIdPaidDtu($topup_id) {
    $paid_dtu_sql = "SELECT purchase_orders_id FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " WHERE top_up_id = '" . (int) $topup_id . "'";
    $paid_dtu_result = tep_db_query($paid_dtu_sql);
    if ($paid_dtu_row = tep_db_fetch_array($paid_dtu_result)) {
        $po_id = $paid_dtu_row['purchase_orders_id'];
    }
    
    return $po_id;
}

function getPoIdCBDtu($topup_id) {
    $po_info = array();
    $cb_dtu_sql = "SELECT po.purchase_orders_id, po.purchase_orders_ref_id FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw, " . TABLE_PURCHASE_ORDERS . " AS po WHERE po.purchase_orders_id = otw.top_up_cb_deduction_po_id AND otw.top_up_id = '" . (int) $topup_id . "'";
    $cb_dtu_result = tep_db_query($cb_dtu_sql);
    if ($cb_dtu_row = tep_db_fetch_array($cb_dtu_result)) {
        $po_info['po_id'] = $cb_dtu_row['purchase_orders_id'];
        $po_info['po_ref_id'] = $cb_dtu_row['purchase_orders_ref_id'];
    }
    
    return $po_info;
}

function setHeaderUrlSorting($sort_by, $sort_option, $dtu_list_type) {
    $sort_array = array();
    $sort_option_reverse = '';
    
    if ($sort_option == 'asc') {
        $sort_array['sort_heading'] = TABLE_HEADING_DTU_SORT_UP;
        $sort_option_reverse = 'desc';
    } else {
        $sort_array['sort_heading'] = TABLE_HEADING_DTU_SORT_DOWN;
        $sort_option_reverse = 'asc';
    }
    
    $sort_array['sort_url'] = 'onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'' . $dtu_list_type . '\', \'\', \'' . $sort_by . '\', \'' . $sort_option_reverse . '\');refreshDTUTotalReport(\'' . $dtu_list_type . '\');"';
    
    return $sort_array;
}

function setDefaultURLSorting($list_type, $sort_by) {
    
    $onclick_default = 'onclick="getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id, \'USD\', \'' . $list_type . '\', \'\', \'' . $sort_by . '\', \'asc\');refreshDTUTotalReport(\'' . $list_type . '\');"';
    
    return $onclick_default;
}

function setHeaderUrlSortDTUList($sort_by, $sort_option) {
    $sort_array = array();
    $sort_option_reverse = '';
    
    if ($sort_option == 'asc') {
        $sort_array['sort_heading'] = TABLE_HEADING_DTU_SORT_UP;
        $sort_option_reverse = 'desc';
    } else {
        $sort_array['sort_heading'] = TABLE_HEADING_DTU_SORT_DOWN;
        $sort_option_reverse = 'asc';
    }
    
    $sort_array['sort_url'] = 'onclick="searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, \'false\', \'\', \'' . $sort_by . '\', \'' . $sort_option_reverse . '\');"';
    
    return $sort_array;
}

function setDefaultURLSortDTUList($sort_by) {
    
    $onclick_default = 'onclick="searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, \'false\', \'\', \'' . $sort_by . '\', \'asc\');"';
    
    return $onclick_default;
}

?>