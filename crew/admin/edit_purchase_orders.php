<?php
/*
  	$Id: edit_purchase_orders.php,v 1.5 2011/08/08 08:53:55 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
//require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once('pear/Date.php');

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_EDIT_PURCHASE_ORDERS)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_EDIT_PURCHASE_ORDERS);
}

define('DISPLAY_PRICE_DECIMAL', 4);

$allow_create_po_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_NEW_PO');
$allow_process_po_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_PROCESS_PO');
$allow_po_refund_credit_note_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_REFUND_CREDIT_NOTE');
$allow_po_refund_cash_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_REFUND_CASH');
$allow_po_cancel_pending_receive_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_CANCEL_PENDING_RECEIVE');
$allow_verify_po_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_VERIFY_PO');
$allow_add_po_remark_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_ADD_REMARK');
$allow_view_po_remark_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_VIEW_REMARK');
$allow_po_make_payment_permission = tep_admin_files_actions(FILENAME_EDIT_PURCHASE_ORDERS, 'PO_MAKE_PAYMENT');

if (isset($_REQUEST['subaction'])) {
	switch ($_REQUEST['subaction']) {
		case 'create_blank_po':
			if (!$allow_create_po_permission) {
				$messageStack->add_session(ERROR_PO_FORM_CREATE_PERMISSION, 'error');
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_LOW_STOCK, 'page=' . $_REQUEST['page']));
			}
			break;
		case 'create_po':
			$error = false;
			
			if ($allow_create_po_permission) {
				if (!isset($_REQUEST['low_stock_batch'])) {
					$messageStack->add_session(ERROR_PO_FORM_EMPTY_PRODUCTS, 'error');
					$error = true;
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_PO_FORM_CREATE_PERMISSION, 'error');
			}
			
			if ($error) {
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_LOW_STOCK, 'page=' . $_REQUEST['page']));
			}
			break;
		case 'navigate':
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_LOW_STOCK, 'page=' . $_REQUEST['page']));
			break;
	}
}

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$edit_po_obj = new edit_purchase_orders($_SESSION['login_id'], $_SESSION['login_email_address']);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;

if (isset($_REQUEST['BackBtn'])) {
	if ($subaction == "insert_po") {
		$_REQUEST['low_stock_batch'] = $_REQUEST['po_items_prod_id'];
		$_POST['low_stock_batch'] = $_REQUEST['po_items_prod_id'];
		$_REQUEST['subaction'] = "create_po";
		$_GET['subaction'] = "create_po";
		$subaction = "create_po";
	} else {
		tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction','po_id')).'&subaction=show_po_list'));
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "preview_po":
			if ($allow_create_po_permission) {
				if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PO_DATE, 'Error');
				}
				
				if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_SUPPLIER, 'Error');
				}
				
				if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PAYMENT_METHOD, 'Error');
				}
				
				if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_CURRENCY, 'Error');
				}
				
				if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
				}
				
				if (!isset($_REQUEST['po_delivery_address']) || (isset($_REQUEST['po_delivery_address']) && !tep_not_null($_REQUEST['po_delivery_address']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_DELIVERY_ADDRESS, 'Error');
				}
				
				if (!isset($_REQUEST['po_items_prod_id']) || (isset($_REQUEST['po_items_prod_id']) && count($_REQUEST['po_items_prod_id']) == 0)) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PRODUCTS, 'Error');
				}
				
				if (isset($_REQUEST['po_items_prod_id'])) {
					$prod_ids = $_REQUEST['po_items_prod_id'];
					foreach ($prod_ids as $prd_id) {
				
						if (!isset($_REQUEST['suggest_qty_'.$prd_id]) || (isset($_REQUEST['suggest_qty_'.$prd_id]) && $_REQUEST['suggest_qty_'.$prd_id] == 0)) {
							$error = true;
							$messageStack->add(sprintf(ERROR_PO_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
						}
						
						if (!isset($_REQUEST['unit_price_'.$prd_id]) || (isset($_REQUEST['unit_price_'.$prd_id]) && !is_numeric($_REQUEST['unit_price_'.$prd_id]))) {
							$error = true;
							$messageStack->add(sprintf(ERROR_PO_FORM_EMPTY_STOCK_PRICE, $prd_id), 'Error');
						}
					}
				}
				
				if ($error) {
					$_REQUEST['subaction'] = "insert_po";
					$_REQUEST['preview_error'] = "1";
					$_GET['subaction'] = "insert_po";
					$subaction = "insert_po";
				}
			} else {
				$error = true;
				$messageStack->add(ERROR_PO_FORM_CREATE_PERMISSION, 'Error');
			}
			break;
			
		case "insert_po":
			if ($allow_create_po_permission) {
				if (!isset($_REQUEST['start_date']) || (isset($_REQUEST['start_date']) && !tep_not_null($_REQUEST['start_date']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PO_DATE, 'Error');
				}
				
				if (!isset($_REQUEST['po_supplier']) || (isset($_REQUEST['po_supplier']) && !tep_not_null($_REQUEST['po_supplier']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_SUPPLIER, 'Error');
				}
				
				if (!isset($_REQUEST['po_supplier_payment']) || (isset($_REQUEST['po_supplier_payment']) && !tep_not_null($_REQUEST['po_supplier_payment']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PAYMENT_METHOD, 'Error');
				}
				
				if (!isset($_REQUEST['po_currency']) || (isset($_REQUEST['po_currency']) && !tep_not_null($_REQUEST['po_currency']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_CURRENCY, 'Error');
				}
				
				if (!isset($_REQUEST['confirm_rate']) || (isset($_REQUEST['confirm_rate']) && !tep_not_null($_REQUEST['confirm_rate']))) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_CURRENCY_CONFIRM_RATE, 'Error');
				}
				
				if (!isset($_REQUEST['po_items_prod_id']) || (isset($_REQUEST['po_items_prod_id']) && count($_REQUEST['po_items_prod_id']) == 0)) {
					$error = true;
					$messageStack->add(ERROR_PO_FORM_EMPTY_PRODUCTS, 'Error');
				}
				
				if (isset($_REQUEST['po_items_prod_id'])) {
					$prod_ids = $_REQUEST['po_items_prod_id'];
					foreach ($prod_ids as $prd_id) {
				
						if (!isset($_REQUEST['suggest_qty_'.$prd_id]) || (isset($_REQUEST['suggest_qty_'.$prd_id]) && $_REQUEST['suggest_qty_'.$prd_id] == 0)) {
							$error = true;
							$messageStack->add(sprintf(ERROR_PO_FORM_EMPTY_SUGGEST_QUANTITY, $prd_id), 'Error');
						}
						
						if (!isset($_REQUEST['unit_price_'.$prd_id]) || (isset($_REQUEST['unit_price_'.$prd_id]) && !is_numeric($_REQUEST['unit_price_'.$prd_id]))) {
							$error = true;
							$messageStack->add(sprintf(ERROR_PO_FORM_EMPTY_STOCK_PRICE, $prd_id), 'Error');
						}
					}
				}
			} else {
				$error = true;
				$messageStack->add(ERROR_PO_FORM_CREATE_PERMISSION, 'Error');
			}
			
			if (!$error) {
				$edit_po_obj->insert_new_po($_REQUEST, $messageStack);
			}
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction','po_id'))));
			
			break;
			
		case 'approve_po':
			if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'PrintBtn') {
				;
			} else {
				if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'CancelBtn') {
					$edit_po_obj->cancel_po($_REQUEST, $messageStack);
				} else if (isset($_REQUEST['action_button']) && $_REQUEST['action_button'] == 'ApproveBtn') {
					$edit_po_obj->approve_po($_REQUEST, $messageStack);
				}
				tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			}
			break;
			
		case 'add_remark':
			if ($allow_add_po_remark_permission) {
				$edit_po_obj->po_add_remark($_REQUEST, $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_ADD_REMARK_PERMISSION, 'error');
			}
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'refund_po':
			if (isset($_REQUEST['CompleteBtn'])) {
				$po_status = '2';
				$edit_po_obj->complete_po($_REQUEST, $messageStack, $po_status);
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'refund_po_credit_note':
			if ($allow_po_refund_credit_note_permission) {
				$edit_po_obj->refund_credit_note($_REQUEST, $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_PO_REFUND_CREDIT_NOTE_PERMISSION, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'refund_po_cash':
			if ($allow_po_refund_cash_permission) {
				$edit_po_obj->refund_cash($_REQUEST, $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_PO_REFUND_CASH_PERMISSION, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'refund_po_cancel':
			if ($allow_po_cancel_pending_receive_permission) {
				$edit_po_obj->refund_unpaid($_REQUEST, $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_PO_CANCEL_PENDING_RECEIVE_PERMISSION, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'rollback':
			if (isset($_REQUEST['RollbackProcessBtn'])) {
				$edit_po_obj->rollback_complete_po($_REQUEST, $messageStack);
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'verify_po':
			if ($allow_verify_po_permission) {
				$edit_po_obj->verifying_po($_REQUEST, $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_VERIFY_PERMISSION, 'error');
			}
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'make_payment':
			if ($allow_po_make_payment_permission) {
				$edit_po_obj->make_po_pre_payment($_REQUEST['po_id'], $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_PO_MAKE_PAYMENT_PERMISSION, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'debit_payment':
			if ($allow_po_make_payment_permission) {
				$edit_po_obj->debit_po_pre_payment($_REQUEST['po_id'], $messageStack);
			} else {
				$messageStack->add_session(ERROR_PO_FORM_PO_MAKE_PAYMENT_PERMISSION, 'error');
			}
			
			tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po'));
			break;
			
		case 'edit_po':
			if (!$edit_po_obj->load_po($_REQUEST, $messageStack)) {
				tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction'))));
			}
			break;
			
		case 'search_po':
			if (!$edit_po_obj->load_po($_REQUEST, $messageStack)) {
				tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction'))));
			} else {
				tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')).'&subaction=edit_po&po_id='.$edit_po_obj->po_info['po_id']));
			}
			break;
		case 'show_po_list':
			if (isset($_REQUEST['reset'])) {
				unset($_SESSION['po_search']);
				tep_redirect(tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS));
			}
		default:
			break;
	}	
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/modal_win.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/ogm_jquery.js"></script>
<?	include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
<?	include_once (DIR_WS_INCLUDES . 'javascript/edit_purchase_orders_xmlhttp.js.php'); ?>
<script language="javascript">
<!--
	var pageLoaded = false;
	function init() {
		// quit if this function has already been called
       	if (arguments.callee.done) return;
		
       	// flag this function so we don't do the same thing twice
       	arguments.callee.done = true;
		
       	initInfoCaptions();
       	pageLoaded = true;	// Control when a javascript event in this page can be triggered
	};
	
   	/* for Mozilla */
   	if (document.addEventListener) {
       	document.addEventListener("DOMContentLoaded", init, null);
   	}
	
   	/* for other browsers */
   	window.onload = init;
//-->
</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<div id="fancy_box" class="fancy_box" style="display:none;">
	<div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
	<div class="fancy_inner" style="display: block;">
		<div id="fancy_close" class="fancy_close" style="display: none;"></div>
		<div class="fancy_frame_bg">
			<div class="fancy_bg fancy_bg_n"></div>
			<div class="fancy_bg fancy_bg_ne"></div>
			<div class="fancy_bg fancy_bg_e"></div>
			<div class="fancy_bg fancy_bg_se"></div>
			<div class="fancy_bg fancy_bg_s"></div>
			<div class="fancy_bg fancy_bg_sw"></div>
			<div class="fancy_bg fancy_bg_w"></div>
			<div class="fancy_bg fancy_bg_nw"></div>
		</div>
		<div id="fancy_content" class="fancy_content"></div>
	</div>
</div>
<div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
			</table>
		</td>
<!-- body_text //-->
		<td width="100%" valign="top">
	   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td width="100%">
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td class="pageHeading" colspan="2"><?php echo HEADING_TITLE; ?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td valign="top">
									<?
									if ($_REQUEST['subaction'] == 'create_po') {
										echo $edit_po_obj->show_items_po_form($_REQUEST);
									} else if ($_REQUEST['subaction'] == 'preview_po') {
										echo $edit_po_obj->preview_po_form($_REQUEST);
									} else if ($_REQUEST['subaction'] == 'insert_po') {
										$edit_po_obj->repopulate_po_cart_items($_REQUEST);
										echo $edit_po_obj->show_po_form($_REQUEST);
									} else if ($_REQUEST['subaction'] == 'create_blank_po') {
										echo $edit_po_obj->show_po_form($_REQUEST);
									} else if ($_REQUEST['subaction'] == 'edit_po') {
										echo $edit_po_obj->show_edit_po_form($_REQUEST);
									} else if ($_REQUEST['subaction'] == 'show_po_list') {
										echo $edit_po_obj->show_po_list($_REQUEST);
									} else {
										echo $edit_po_obj->search_po_list($_REQUEST);
									}
									?>
								</td>
							</tr>
						</table>
					</td>
<!-- body_text_eof //-->
				</tr>
			</table>
		</td>
	</tr>
</table>
<!-- body_eof //-->
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<?php
	if ($_REQUEST['subaction'] == 'edit_po') {
		echo '<script type="text/javascript">';
		echo '	jQuery().ready(function() {';
		echo "		getPurchaseOrderStatistic('".$edit_po_obj->supplier['supplier_id']."');";
		echo '	});';
		echo '</script>';
	}
 ?>       
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>