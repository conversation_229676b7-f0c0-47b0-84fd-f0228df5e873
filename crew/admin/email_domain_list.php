<?php
require_once('includes/application_top.php');

$next_action = '';
$email_domain_groups_array = array();
$email_domain_group_array = array();
$button_label = BUTTON_INSERT;
$next_action = 'insert_group';
$file_directory = DIR_FS_ADMIN . 'images/user_icons/';

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');
$group_id = isset($_REQUEST['groupID']) ? (int)$_REQUEST['groupID'] : 0;
$group_name = isset($_REQUEST['groupName']) ? $_REQUEST['groupName'] : '';

# Process Action Arguments
switch ($action) {
	case "update_group":	#Update email domain group and upload image file
	case "insert_group":
		if ($action == 'update_group')	$check_exist_select_sql_ext = "AND email_domain_groups_id !=".$group_id;
		
		if (!tep_not_null($group_name)) {
			$messageStack->add_session(ERROR_EMPTY_EMAIL_DOMAIN_GROUP, 'error');
			tep_redirect(tep_href_link(FILENAME_EMAIL_DOMAIN_LIST));
		}
		
		$data_array = array ('email_domain_groups_name' => $group_name);
		
		$check_exist_select_sql = "	SELECT email_domain_groups_id
									FROM " . TABLE_EMAIL_DOMAIN_GROUPS . " 
									WHERE email_domain_groups_name = '".$group_name."'" .
										$check_exist_select_sql_ext;
		$check_exist_result_sql = tep_db_query($check_exist_select_sql);
		if (!tep_db_num_rows($check_exist_result_sql)) {
			if ($action == 'insert_group') {
				tep_db_perform(TABLE_EMAIL_DOMAIN_GROUPS, $data_array);
				$group_id = tep_db_insert_id();
				
				$messageStack->add_session(SUCCESS_INSERT_EMAIL_DOMAIN_GROUP, 'success');
			} else {
				tep_db_perform(TABLE_EMAIL_DOMAIN_GROUPS, $data_array, "update", " email_domain_groups_id=".$group_id);
				$messageStack->add_session(SUCCESS_UPDATE_EMAIL_DOMAIN_GROUP, 'success');
			}
		} else {
			$messageStack->add_session(ERROR_EMAIL_DOMAIN_GROUP_USED, 'error');
			tep_redirect(tep_href_link(FILENAME_EMAIL_DOMAIN_LIST));
		}
		
		unset($data_array);
		// Check the mime type
		foreach ($_FILES AS $file_io_name => $file_info) {
			if (tep_not_null($file_info['name'])) {
				#check and delete previous image if existed.
				$check_exist_select_sql = "	SELECT ".$file_io_name."_files_name as file_name
											FROM " . TABLE_EMAIL_DOMAIN_GROUPS . " 
											WHERE email_domain_groups_id = ".$group_id;
				$check_exist_result_sql = tep_db_query($check_exist_select_sql);
				if ($check_exist_row = tep_db_fetch_array($check_exist_result_sql)){
					if (tep_not_null($check_exist_row['file_name']) && file_exists($file_directory . $check_exist_row['file_name']))	{
						@unlink($file_directory . $check_exist_row['file_name']);
					}
				}
				
//				$icons_image = new upload($file_io_name);
				$icons_image = new upload($file_io_name, '', '0775', array('gif', 'jpg'));
				$icons_image->set_destination($file_directory);
				
				if ($icons_image->parse() && $icons_image->save($group_id.'_'.$file_io_name, 'small')) {
					$update_data_array = array ($file_io_name.'_files_name' => $icons_image->filename);
					
					tep_db_perform(TABLE_EMAIL_DOMAIN_GROUPS, $update_data_array, "update", " email_domain_groups_id=".$group_id);
					unset($icons_image, $update_data_array);
				}
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_EMAIL_DOMAIN_LIST));
		break;
	case "remove_group":	#delete email domain group, clear the domain group's domains and removed uploaded image file
		if ($group_id > 0) {
			$check_exist_select_sql = "	SELECT verified_files_name, not_verified_files_name 
										FROM " . TABLE_EMAIL_DOMAIN_GROUPS . " 
										WHERE email_domain_groups_id = ".$group_id;
			$check_exist_result_sql = tep_db_query($check_exist_select_sql);
			if ($check_exist_row = tep_db_fetch_array($check_exist_result_sql)){
				if (tep_not_null($check_exist_row['verified_files_name']) && file_exists($file_directory . $check_exist_row['verified_files_name']))	{
					@unlink($file_directory . $check_exist_row['verified_files_name']);
				}
				
				if (tep_not_null($check_exist_row['not_verified_files_name']) && file_exists($file_directory . $check_exist_row['not_verified_files_name']))	{
					@unlink($file_directory . $check_exist_row['not_verified_files_name']);
				}
				
				$delete_data_sql = "DELETE FROM " . TABLE_EMAIL_DOMAIN_GROUPS . " WHERE email_domain_groups_id=".$group_id;
				tep_db_query($delete_data_sql);
				
				$delete_data_sql = "DELETE FROM " . TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS . " WHERE email_domain_groups_id=".$group_id;
				tep_db_query($delete_data_sql);
				
				$messageStack->add_session(SUCCESS_REMOVE_EMAIL_DOMAIN_GROUP, 'success');
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_EMAIL_DOMAIN_LIST));
		break;
	case "add_group" :	#pre-load when hit button add new domain group
		$email_domain_group_array = array(	'email_domain_groups_id' => 0,
											'email_domain_groups_name' => '');
		break;
	case "edit_group" :	#pre-load when hit button edit domain group
		$email_domain_groups_select_sql = "	SELECT email_domain_groups_name
											FROM " . TABLE_EMAIL_DOMAIN_GROUPS . "
											WHERE email_domain_groups_id = ".$group_id;
		$email_domain_groups_result_sql = tep_db_query($email_domain_groups_select_sql);
		if ($email_domain_groups_row = tep_db_fetch_array($email_domain_groups_result_sql)){
			$email_domain_group_array = array (	'email_domain_groups_id' => $group_id,
												'email_domain_groups_name' => $email_domain_groups_row['email_domain_groups_name']
												);
		}
		
		break;
	default : #pre-load when start domain groups listing page
		# Get Email Domain Groups From Database
		$email_domain_groups_select_sql = "	SELECT email_domain_groups_id, email_domain_groups_name 
											FROM " . TABLE_EMAIL_DOMAIN_GROUPS . "
											ORDER BY email_domain_groups_name";
		$email_domain_groups_result_sql = tep_db_query($email_domain_groups_select_sql);
		while ($email_domain_groups_row = tep_db_fetch_array($email_domain_groups_result_sql)){
			$email_domain_groups_array[] = array (	'email_domain_groups_id' => $email_domain_groups_row['email_domain_groups_id'],
													'email_domain_groups_name' => $email_domain_groups_row['email_domain_groups_name']
												);
		}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title>Email Domain List</title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<style type="text/css">
		h3 { border-bottom:2px solid #B5D5FF; margin:0; padding:6px 0; font-size:11pt; }
		#bar { border-bottom:1px solid #b5d5ff; padding-bottom:5px; height:5px; }
		#loading { float:left; padding-top:5px; background-color:#ffffff; display:none; padding-right:5px;}
		#btn_add {font-family: arial; font-size:10pt;}
		#email_domain { color:#444444; width:80%; margin:0; font-family: arial; font-size:10pt; }
		#msg { background-color:#ff3333;  padding:1px 4px; display:none; font-weight:bold; cursor:pointer; }
		#msgdetails { padding:1px 4px; border:1px solid #ff3333; background-color:#fff; display:none; max-width:700px; }
		#domainlist { list-style-type: none; margin: 0; padding: 0;}
		#domainlist li { padding:6px 2px 6px 6px; border-bottom:1px solid #DEDEDE; min-height:18px; margin-bottom:1px; background-color:#fff; }
		#domainlist li:hover { background-color:#f6f6f6; }
		.tab-content { padding:8px; border-bottom:2px solid #DEDEDE; background-color: #ededed; }
		.domain-middle { margin-left:5px; margin-right:65px; }
		.domain-actions { float:right; width:65px; text-align:right; }
		.domain-actions a { margin-left:2px; margin-right:2px; }
		.domain-actions img { border:none; width:16px; height:16px; }
		.domain-title {}
	</style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php
switch ($action) {
	case 'edit_group' :
		$button_label = BUTTON_UPDATE;
		$next_action = 'update_group';
	case 'add_group' :
		echo tep_draw_form('email_domain_group_form', FILENAME_EMAIL_DOMAIN_LIST, tep_get_all_get_params(array('action')).'action='.$next_action, 'post', ' enctype="multipart/form-data"');
		echo tep_draw_hidden_field("groupID", $email_domain_group_array['email_domain_groups_id'], "id='groupID'");
?>
					<tr>
	    				<td width="100%">
	    					<table border="0" width="100%" cellspacing="0" cellpadding="0">
	      						<tr>
	        						<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
	        					</tr>
	        				</table>
	        			</td>
	        		</tr>
	        		<tr>
	    				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	  				</tr>
					<tr>
	    				<td width="100%">
	    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?php echo ENTRY_GROUP_NAME; ?></td>
									<td class="main" valign="top"><?php echo tep_draw_input_field('groupName', $email_domain_group_array["email_domain_groups_name"], 'size="40" maxlength="120" id="groupName"'); ?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
			      				</tr>
				  				<tr>
				  					<td class="main" valign="top"><?php echo ENTRY_UPLOAD_ICONS; ?></td>
			        				<td width="100%">
			        					<table border="0" cellspacing="0" cellpadding="2">
						      				<tr>
												<td class="main" colspan="2">
													<fieldset class="selectedFieldSet" style="padding: 5px 30px 5px 5px;">
														<legend align=left class="sectionHeader"><?php echo 'icons'; ?></legend>
														<table border="0" cellspacing="0" cellpadding="2">
															<tr>	
										      					<td class="main" width="32%" valign="top"><?php echo ENTRY_UPLOAD_EMAIL_VERIFIED; ?></td>
										      					<td class="main"><?php echo tep_draw_file_field('verified', 'size="40"'); ?></td>
										      				</tr>
										      				<tr>	
										      					<td class="main" width="32%" valign="top"><?php echo ENTRY_UPLOAD_EMAIL_UNVERIFY; ?></td>
										      					<td class="main"><?php echo tep_draw_file_field('not_verified', 'size="40"'); ?></td>
										      				</tr>
										      			</table>
													</fieldset>
												</td>
											</tr>
											<tr>
												<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
											</tr>
										</table>
						      		</td>
						      	</tr>
						      	<tr>
									<td colspan="2" align="right">
										<input type="button" name="update" value="<?php echo $button_label; ?>" class="inputButton" onClick="return form_checking();">&nbsp;&nbsp;
            							<input type="button" name="cancel" value="<?php echo BUTTON_CANCEL; ?>" class="inputButton" onClick="document.location.href='<?php echo tep_href_link(FILENAME_EMAIL_DOMAIN_LIST); ?>'">
									</td>
								</tr>
							</form>
<?php
		if ($action == 'edit_group') {
?>
								<tr>
									<td class="main" valign="top"><?php echo ENTRY_EMAIL_DOMAIN_LIST; ?></td>
									<td class="main" valign="top">
										<div id="bar">
										 	<div style="float:left"><span id="msg" onClick="toggleMsgDetails()"></span><div id="msgdetails"></div></div>
										</div>
										<div>
											<div id="tabs">
												<br clear="all">
												<div class="tab-content" id="newdomain"> 
													<form name="domain" onSubmit="return newDomain(this)">
														<input type="text" name="email_domain" value="" maxlength="120" id="email_domain"> 
														<input type="submit" value="<?php echo BTN_ADD_DOMAIN; ?>" id="btn_add"><br>
														<span>Input Example: @example1.com,@example2.com</span>
													</form>
												</div>
											</div>
											<div id="loading"><img width="22px" src="images/loading.gif"></div>
											<h3>
												<span><span class="btnstr"><?php echo ENTRY_DOMAIN_LIST_HEADER; ?></span> (<span id="total">0</span>)</span>
											</h3>
											<div id="domaincontainer" style="max-height: 250px; overflow-y: scroll;">
											 	<ol id="domainlist" class="sortable"></ol>
											</div>
										</div>
									</td>
								</tr>
<?php
		}
?>
			      			</table>
			      		</td>
			      	</tr>
<?php
		break;
	default :
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
            					</tr>
            					<tr>
    								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            							<?php echo '[ <a href="'.tep_href_link(FILENAME_EMAIL_DOMAIN_LIST, 'action=add_group').'" >'.LINK_ADD_GROUP.'</a> ]'; ?>
            						</td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
            			<td valign="top">
            				<table border="0" width="90%" cellspacing="1" cellpadding="2">
               					<tr>
			       					<td class="reportBoxHeading"><?php echo TABLE_HEADING_GROUP_NAME; ?></td>
					                <td class="reportBoxHeading" width="5%"><?php echo TABLE_HEADING_ACTION; ?></td>
			   					</tr>
<?php
		foreach ($email_domain_groups_array AS $row_count => $email_domain_group) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr height="28" class="<?php echo $row_style; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style; ?>')" onclick="rowClicked(this, '<?php echo $row_style; ?>')">
									<td class="reportRecords" valign="top"><?php echo $email_domain_group['email_domain_groups_name']; ?></td>
									<td class="reportRecords" valign="top">
										<table border="0" cellspacing="0" cellpadding="1">
											<tr>
												<td>
													<a href="<?php echo tep_href_link(FILENAME_EMAIL_DOMAIN_LIST, 'action=edit_group&groupID='.$email_domain_group['email_domain_groups_id']); ?>"><?php echo tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"'); ?></a>
												</td>
												<td><a href="javascript:void(confirm_delete('', 'Group', '<?php echo tep_href_link(FILENAME_EMAIL_DOMAIN_LIST, 'action=remove_group&groupID='.$email_domain_group['email_domain_groups_id']); ?>'))"> 
														<?php echo tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", "align='top'"); ?></a></td>
											</tr>
										</table>
									</td>
								</tr>
<?php
		}
		
		if (tep_not_null($email_domain_groups_array)) {
			unset($email_domain_groups_array);
?>
								<tr>
			            			<td colspan="2">
			            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			              					<tr>
			                					<td class="smallText" valign="top"></td>
			                					<td class="smallText" align="right"></td>
			              					</tr>
			            				</table>
			            			</td>
          						</tr>
<?php
		} else {
?>
								<tr>
									<td class="reportRecords" colspan="2"><?php echo TEXT_NO_EMAIL_GROUP; ?></td>
								</tr>
<?php
		}
?>
							</table>
    					</td>
    				</tr>
<?php
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
	<script language="javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery_ui/ui/<?=FILENAME_JS_JQUERY_UI?>"></script>
	<script type="text/javascript" src="includes/javascript/email_domain_list.js"></script>
<?php
	if (tep_not_null($action)) {
?>
	<script language="javascript">
	<!--
		jQuery.noConflict();
		lang = {
				actionDelete: "delete",
				confirmDelete: "<?php echo COMFIRM_DELETE_DOMAIN; ?>",
				error: "<?php echo ERROR_MESSAGE; ?>",
				insert_error_detail: "<?php echo ERROR_INSERT_EMAIL_DOMAIN; ?>",
				error_domain: "<?php echo ERROR_EMAIL_DOMAIN; ?>"
			};
		
		jQuery(document).ready(function(){
			loadDomains();
			preloadImg();
		});
		jQuery().ajaxSend( function(r,s) {jQuery("#loading").show();} );
		jQuery().ajaxStop( function(r,s) {jQuery("#loading").fadeOut();} );
	//-->
	</script>
<?php
	}
?>
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>