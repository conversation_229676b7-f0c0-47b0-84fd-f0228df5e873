<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($_REQUEST['action']) ? tep_db_input($_REQUEST['action']) : '';
$group_id = isset($_REQUEST['group_id']) ? (int)$_REQUEST['group_id'] : 0;
$domain_name_list = (isset($_REQUEST['domain_name']) && tep_not_null($_REQUEST['domain_name'])) ? $_REQUEST['domain_name'] : '';
$domain_id = isset($_REQUEST['domain_id']) ? (int)$_REQUEST['domain_id'] : 0;

#validate the domains, filter out incorrect domains and check exist new domain
function xmlhttp_tep_validated_domain($entry_domains_list) {
	global $domain_existed_array, $valid_domain_array;
	
	$entry_domains_list = str_ireplace(" ", "", $entry_domains_list);
	$entry_domains_list = strtolower($entry_domains_list);
	$entry_domains_array = explode(",", $entry_domains_list);
	
	foreach ($entry_domains_array as $domain) {
		if (tep_not_null($domain)) {
			if (strpos($domain, '@') !== 0) {
				$domain_existed_array[] = $domain;
			} else if (in_array($domain, $valid_domain_array)) {
				$domain_existed_array[] = $domain;
			} else {
				$valid_domain_array[] = $domain;
			}
		}
	}
	return count($valid_domain_array) ? true : false;
}

echo '<response>';

if (tep_not_null($action)){
	switch($action) {
		case "get_email_domain_list" :
			echo '<domains>';
			$email_domains_select_sql = "	SELECT email_domain_groups_domains_id, email_domain_groups_domains_name 
											FROM " . TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS . "
											WHERE email_domain_groups_id = ".$group_id;
			$email_domains_result_sql = tep_db_query($email_domains_select_sql);
			while ($email_domains_row = tep_db_fetch_array($email_domains_result_sql)) {
				echo "<domain id='".$email_domains_row['email_domain_groups_domains_id']."'><![CDATA[".strip_tags($email_domains_row['email_domain_groups_domains_name'])."]]></domain>";
			}
			echo '</domains>';
			break;
			
		case "insert_domain" :
			$domain_existed_array = array();
			$valid_domain_array = array();
			
			if (xmlhttp_tep_validated_domain($domain_name_list)) {
				$email_domains_exist_select_sql = "	SELECT email_domain_groups_domains_name 
													FROM " . TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS . "
													WHERE email_domain_groups_domains_name IN ('".implode("','", $valid_domain_array)."')";
				$email_domains_exist_result_sql = tep_db_query($email_domains_exist_select_sql);
				while ($email_domains_exist_row = tep_db_fetch_array($email_domains_exist_result_sql)) {
					$domain_existed_array[] = $email_domains_exist_row['email_domain_groups_domains_name'];
				}
				
				if (count($domain_existed_array)) {
					$valid_domain_array = array_diff($valid_domain_array, $domain_existed_array);
					echo '<error_domains>'.implode(", ", $domain_existed_array).'</error_domains>';
					unset($domain_existed_array);
				}
				
				if (count($valid_domain_array)) {
					echo '<domains>';
					$insert_sql = "INSERT INTO ".TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS." (email_domain_groups_domains_name, email_domain_groups_id) VALUES ('".implode("',".$group_id."),('", $valid_domain_array)."',".$group_id.")";							
					tep_db_query($insert_sql);
					
					$email_domains_insert_select_sql = "	SELECT email_domain_groups_domains_id, email_domain_groups_domains_name 
															FROM " . TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS . "
															WHERE email_domain_groups_domains_name IN ('".implode("','", $valid_domain_array)."')";
					$email_domains_insert_result_sql = tep_db_query($email_domains_insert_select_sql);
					while ($email_domains_insert_row = tep_db_fetch_array($email_domains_insert_result_sql)) {
						echo "<domain id='".$email_domains_insert_row['email_domain_groups_domains_id']."'><![CDATA[".strip_tags($email_domains_insert_row['email_domain_groups_domains_name'])."]]></domain>";
					}
					echo '</domains>';
					unset($valid_domain_array);
				}
			} else {
				if (count($domain_existed_array)) {
					echo '<error_domains>'.implode(", ", $domain_existed_array).'</error_domains>';
					unset($domain_existed_array);
				}
			}
			
			break;
		case "remove_domain" :
			if ($domain_id > 0) {
				$email_domains_delete_sql = "DELETE FROM ".TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS." WHERE email_domain_groups_domains_id = ".$domain_id;
				$result = tep_db_query($email_domains_delete_sql);
				
				if ($result) {
					echo "<domain id='".$domain_id."'><![CDATA[]]></domain>";
				}
			}
			
			break;
	}
}
echo '</response>';
?>