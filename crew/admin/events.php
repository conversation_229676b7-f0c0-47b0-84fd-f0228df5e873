<?
/*
  	$Id: events.php,v 1.13 2015/06/08 09:16:58 weesiong Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 Will Mays
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$no_error = true;

$share_save_sql = false;
$input_type_case = 0;

$subTitle = '';
$languages = tep_get_languages();

if (strtolower($_SERVER['REQUEST_METHOD'])=='get') {
	switch($_GET['action']) {
		case "edit_events" :
			$subTitle = SUBTITLE_EDIT_EVENTS;
			$events_lang_info_array = array();
			
			if (isset($_GET['events_id'])) {
				$get_events_select_sql = "	SELECT e.news_id, e.events_remark, e.events_sender_email, e.events_admin_copy_email,
												   e.events_order_period_date_from, e.events_order_period_date_to, e.events_order_period_note,
												   e.events_order_period_empty_err_msg, e.events_order_period_invalid_err_msg 
											FROM " . TABLE_EVENTS . " AS e 
											WHERE e.events_id = '" . tep_db_input($_GET['events_id']) . "'";
				$get_events_result_sql = tep_db_query($get_events_select_sql);
				
				if ($get_events_row = tep_db_fetch_array($get_events_result_sql)) {
					$news_id						 		= $get_events_row['news_id'];
					$events_remark 							= $get_events_row['events_remark'];
					$events_sender_email 			 		= $get_events_row['events_sender_email'];
					$events_admin_copy_email 		 		= $get_events_row['events_admin_copy_email'];
					$events_order_period_date_from   		= $get_events_row['events_order_period_date_from'];
					$events_order_period_date_to     		= $get_events_row['events_order_period_date_to'];
					$events_order_period_note    	 		= $get_events_row['events_order_period_note'];
					$events_order_period_empty_err_msg     	= $get_events_row['events_order_period_empty_err_msg'];
					$events_order_period_invalid_err_msg    = $get_events_row['events_order_period_invalid_err_msg'];
					
					$event_desc_select_sql = '	SELECT events_name, language_id
												FROM ' . TABLE_EVENTS_DESCRIPTION . '
												WHERE events_id = "'. tep_db_input($_GET['events_id']) .'"';
					$event_desc_result_sql = tep_db_query($event_desc_select_sql);
					while ($event_desc_row = tep_db_fetch_array($event_desc_result_sql)) {
						$events_lang_info_array[$event_desc_row['language_id']] = array('events_name' => $event_desc_row['events_name']);
					}
				}
			}
			
			break;
		case "delete_events" :
			if (isset($_GET['events_id'])) {
				$del_event_id = tep_db_prepare_input($_GET['events_id']);
				
				$delete_events_sql = "	DELETE FROM " . TABLE_EVENTS . "
										WHERE events_id = '".tep_db_input($del_event_id)."';";
				
				$delete_events_description_sql = "	DELETE FROM " . TABLE_EVENTS_DESCRIPTION . "
													WHERE events_id = '".tep_db_input($del_event_id)."';";
				tep_db_query($delete_events_description_sql);
				
				$search_linking_record_sql = "	SELECT events_options_id 
												FROM " . TABLE_EVENTS_OPTIONS . "
												WHERE events_id = '".tep_db_input($del_event_id)."';";
				$search_linking_record_result_sql = tep_db_query($search_linking_record_sql);
				
				while ($search_linking_record_row = tep_db_fetch_array($search_linking_record_result_sql)) {
					$delete_events_options_values_sql = "	DELETE FROM " . TABLE_EVENTS_OPTIONS_VALUES . "
															WHERE events_options_id = '" . $search_linking_record_row['events_options_id'] . "'";
					tep_db_query($delete_events_options_values_sql);
					
					$delete_events_options_desc_sql = "	DELETE FROM " . TABLE_EVENTS_OPTIONS_DESCRIPTION . "
														WHERE events_options_id = '" . $search_linking_record_row['events_options_id'] . "'";
					tep_db_query($delete_events_options_desc_sql);
				}
				
				$delete_events_options_sql = "	DELETE FROM " . TABLE_EVENTS_OPTIONS . "
												WHERE events_id = '".tep_db_input($del_event_id)."';";
				
				if (tep_db_query($delete_events_sql) && tep_db_query($delete_events_options_sql)) {
					$messageStack->add_session(SUCCESS_DELETE_EVENT, 'success');
					tep_redirect(tep_href_link(FILENAME_EVENTS));
				}
			}
			
			break;
		case "edit_option" :
			$subTitle = SUBTITLE_EDIT_OPTIONS;
			
			if (isset($_GET['options_id'])) {
				$edit_option_id = tep_db_prepare_input($_GET['options_id']);
				
				$get_options_info_array = array();
				$get_options_value_array = array();
				
				$get_options_select_sql = "	SELECT  eo.events_options_id, eo.events_options_input_type, eo.events_options_required, eo.events_options_sort_order, eo.events_options_status 
											FROM " . TABLE_EVENTS_OPTIONS . " AS eo 
											WHERE eo.events_options_id = '" . tep_db_input($edit_option_id) . "'";
				$get_options_result_sql = tep_db_query($get_options_select_sql);
				
				if ($get_options_row = tep_db_fetch_array($get_options_result_sql)) {
				 	$options_id					= $get_options_row['events_options_id'];
					$options_input_type 		= $get_options_row['events_options_input_type'];
					$options_required 			= $get_options_row['events_options_required'];
					$options_status 			= $get_options_row['events_options_status'];
					$options_sort_order 		= $get_options_row['events_options_sort_order'];
				 	
				 	$opt_desc_select_sql = 'SELECT eod.events_options_max_size, eod.events_options_row_size, eod.events_options_column_size, eod.events_options_name,
				 								eod.events_options_err_msg, eod.events_options_title, eod.language_id, eod.events_options_note
				 							FROM ' . TABLE_EVENTS_OPTIONS_DESCRIPTION . ' AS eod
				 							WHERE eod.events_options_id = "'.tep_db_input($edit_option_id).'"';
				 	$opt_desc_result_sql = tep_db_query($opt_desc_select_sql);
				 	while ($opt_desc_row = tep_db_fetch_array($opt_desc_result_sql)) {
				 		$get_options_info_array[$opt_desc_row['language_id']] = $opt_desc_row;
				 	}
				 	
				 	$get_options_value_select_sql = "	SELECT events_options_values, events_options_values_sort_order, language_id
														FROM " . TABLE_EVENTS_OPTIONS_VALUES . "
														WHERE events_options_id = '" . tep_db_input($edit_option_id) . "'
														ORDER BY events_options_values_id";
					$get_options_value_result_sql = tep_db_query($get_options_value_select_sql);
					
					while ($get_options_value_row = tep_db_fetch_array($get_options_value_result_sql)) {
						$get_options_value_array[$get_options_value_row['language_id']]['events_options_values'][] = $get_options_value_row['events_options_values'];
						$get_options_value_array[$get_options_value_row['language_id']]['events_options_values_sort_order'][] = $get_options_value_row['events_options_values_sort_order'];
					}
				}
			}
			
			break;
		case 'delete_option':
			if (isset($_GET['options_id'])) {
				$del_option_id = tep_db_prepare_input($_GET['options_id']);
				
				$delete_events_options_sql = "	DELETE FROM ".TABLE_EVENTS_OPTIONS." 
												WHERE events_options_id = '".tep_db_input($del_option_id)."';";
				
				$delete_events_options_description_sql = "	DELETE FROM ".TABLE_EVENTS_OPTIONS_DESCRIPTION."
															WHERE events_options_id = '".tep_db_input($del_option_id)."';";
				
				$delete_events_options_values_sql = "	DELETE FROM ".TABLE_EVENTS_OPTIONS_VALUES."
														WHERE events_options_id = '".tep_db_input($del_option_id)."';";
				
				if (tep_db_query($delete_events_options_sql)) {
					tep_db_query($delete_events_options_description_sql);
					tep_db_query($delete_events_options_values_sql);
					
					$messageStack->add_session(SUCCESS_DELETE_EVENT_OPTION, 'success');
					tep_redirect(tep_href_link(FILENAME_EVENTS));
				}
			}
			
			break;
		case 'email_template' :
			$subTitle = SUBTITLE_EMAIL_TEMPLATES;
			
			break;
	}
}

if (strtolower($_SERVER['REQUEST_METHOD'])=='post') {
	switch($action) {
		case "new_event" :
		case "edit_events" :
			if ($action == 'edit_events') {
				$edit_event_id = tep_db_prepare_input($_POST['events_id']);
			} else {
				$edit_event_id = 0;
			}
			$headline_id = tep_db_prepare_input($_POST['headline_id']);
			
			if (!tep_not_null($_POST['event_name'][1])) {
				$no_error =  false;
				$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_EVENT_NAME, 'error');
			}
			
			if (!tep_not_null($_POST['events_sender_email'])) {
				$no_error =  false;
				$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_EMAIL_SENDER, 'error');
			}
			
			if (!tep_not_null($_POST['events_admin_copy_email'])) {
				$no_error =  false;
				$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_EMAIL_RECIPIENT, 'error');
			}
			
			if ($action == 'new_event') {
				$check_news_id_select_sql = "	SELECT events_id 
												FROM " . TABLE_EVENTS . "
												WHERE news_id = '" . tep_db_input($headline_id) . "'";
				$check_news_id_result_sql = tep_db_query($check_news_id_select_sql);
				
				if (tep_db_num_rows($check_news_id_result_sql) > 0) {	// One new only can belong to 1 event
					$no_error =  false;
					$messageStack->add_session(ERROR_NEWS_EXISTED, 'error');
				}
			} else if ($action == 'edit_events') {
				$check_news_id_select_sql = "	SELECT events_id 
												FROM " . TABLE_EVENTS . "
												WHERE news_id = '" . tep_db_input($headline_id) . "'
													AND events_id <> '".tep_db_input($edit_event_id)."'";
				$check_news_id_result_sql = tep_db_query($check_news_id_select_sql);
				
				if (tep_db_num_rows($check_news_id_result_sql) > 0) {
					$no_error =  false;
					$messageStack->add_session(ERROR_NEWS_EXISTED, 'error');
				}
			}
			
			if ($no_error) {
				$arr = array(	'news_id' => $headline_id,
					 			'events_remark' => tep_db_prepare_input($_POST['events_remark']),
					 			'events_sender_email' => tep_db_prepare_input($_POST['events_sender_email']),
					 			'events_admin_copy_email' => tep_db_prepare_input($_POST['events_admin_copy_email']),
					 			'events_order_period_date_from' => tep_db_prepare_input($_POST['start_date']),
					 			'events_order_period_date_to' => tep_db_prepare_input($_POST['end_date']),
					 			'events_order_period_note' => tep_db_prepare_input($_POST['events_note']),
					 			'events_order_period_empty_err_msg' => tep_db_prepare_input($_POST['events_err_msg']),
					 			'events_order_period_invalid_err_msg' => tep_db_prepare_input($_POST['events_err_msg2'])
				     		);
				
				if ($action == 'new_event') {
				    $arr['events_status'] = tep_db_prepare_input($_POST['status']);
					if (tep_db_perform(TABLE_EVENTS, $arr)) {
						$events_id = tep_db_insert_id();
						$messageStack->add_session(SUCCESS_INSERT_EVENT, 'success');
					}
					
					foreach ($_POST['event_name'] as $events_name_lang_id => $events_name) {
						$events_name_arr = array('events_id' => tep_db_prepare_input($events_id),
									 			 'language_id' => tep_db_prepare_input($events_name_lang_id),
									 			 'events_name' => tep_db_prepare_input($events_name)
					     						);
					    tep_db_perform(TABLE_EVENTS_DESCRIPTION, $events_name_arr);
					}
				} elseif ($action == 'edit_events') {
					if (tep_db_perform(TABLE_EVENTS, $arr, 'update', "events_id ='" . tep_db_input($edit_event_id) . "'")) {
						$messageStack->add_session(SUCCESS_UPDATE_EVENT, 'success');
					}
					
					foreach ($_POST['event_name'] as $events_name_lang_id => $events_name) {
						$events_name_arr = array('events_name' => tep_db_prepare_input($events_name));
					    
					    $events_lang_check_select_sql = "	SELECT events_id
								 							FROM " . TABLE_EVENTS_DESCRIPTION . "
								 							WHERE events_id = '" . tep_db_input($edit_event_id) . "'
								 			  					AND language_id = '".(int)$events_name_lang_id."'";
						$events_lang_check_result_sql = tep_db_query($events_lang_check_select_sql);
						
						if (tep_db_num_rows($events_lang_check_result_sql)) {	// if has record
							tep_db_perform(TABLE_EVENTS_DESCRIPTION, $events_name_arr,  'update', "events_id ='" . tep_db_input($edit_event_id) . "'AND language_id = '" . (int)$events_name_lang_id ."'");
						} else {
					    	$events_name_arr['events_id'] = tep_db_prepare_input($_POST['events_id']);
					    	$events_name_arr['language_id'] = tep_db_prepare_input($events_name_lang_id);
					    	
					    	tep_db_perform(TABLE_EVENTS_DESCRIPTION, $events_name_arr);
						}
					}
				}
				tep_redirect(tep_href_link(FILENAME_EVENTS));
			} else {
				tep_redirect(tep_href_link(FILENAME_EVENTS, "err_handle=new_event"));
			}
			
			break;
		case 'new_option':
			if(isset($_GET['events_id'])) {
				$events_id = tep_db_prepare_input($_GET['events_id']);
			} else {
				$events_id = '';
			}
			
			break;
		case 'add_new_option':
		case 'edit_option':
				if (!tep_not_null($_POST['op_title'][1])) {//check english option title
					$no_error = false;
					$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_OPTION_TITLE, 'error');
				}
				
				if (!tep_not_null($_POST['op_sort_order'])) { //check sort order for the event
					$no_error = false;
					$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_SORT_ORDER, 'error');
				}
 				
				if ($action == 'add_new_option'){
					$check_post = isset($_POST['events_id']);
				} elseif ($action == 'edit_option') {
					$check_post = tep_not_null($_POST['options_id']);
				}
  				$events_options_id = tep_db_prepare_input($_POST['options_id']);
  				
				if ($check_post) {
					switch ((int)$_POST['op_type_id']) {
						case '1' ://## TEXTBOX
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {//if tep_not_null(otpion title) op_size & op_max_size_text != ''
								$lng_id = $languages[$i]['id'];
								$lng_type = $languages[$i]['name'];
								if (tep_not_null($_POST['op_title'][$lng_id])){
									if (!tep_not_null($_POST['op_size'][$lng_id]) || ($_POST['op_size'][$lng_id] == 0)) {
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_SIZE.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
									
									if(!tep_not_null($_POST['op_max_size_text'][$lng_id]) || ($_POST['op_max_size_text'][$lng_id] == 0)){
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_MAX_CHARACTER.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
								}
							}
							
							if ($no_error) {
								$share_save_sql = true;
								$input_type_case = 1;//edit option on page load
							}
							break;
						
						case '2' ://## TEXTAREA
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {//if tep_not_null(otpion title) op_size & op_max_size_text != ''
								$lng_id = $languages[$i]['id'];
								$lng_type = $languages[$i]['name'];
								if (tep_not_null($_POST['op_title'][$lng_id])){
									if (!tep_not_null($_POST['op_row_textarea'][$lng_id]) || ($_POST['op_row_textarea'][$lng_id] == 0)) {
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_ROW.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
									
									if(!tep_not_null($_POST['op_column_textarea'][$lng_id]) || ($_POST['op_column_textarea'][$lng_id] == 0)){
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_COLUMN.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
									
									if(!tep_not_null($_POST['op_max_size_textarea'][$lng_id]) || ($_POST['op_max_size_textarea'][$lng_id] == 0)){
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_MAX_CHARACTER.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
								}
							}
							
							if ($no_error) {
								$share_save_sql = true;
								$input_type_case = 2;//edit option on page load
							}
							break;
						
						case '3' :
						case '4' :
							if ((int)$_POST['op_type_id'] == '3'){
								$loop_num = count($_POST['op_radio_name']);
								$option_name_loop = $_POST['op_radio_name'];
								$option_sort_loop = $_POST['op_radio_input_type_sort_order'];
								$count_options_name = sizeof($_POST['op_radio_name']);
							} elseif((int)$_POST['op_type_id'] == '4'){
								$loop_num = count($_POST['op_dd_name']);
								$option_name_loop = $_POST['op_dd_name'];
								$option_sort_loop = $_POST['op_dd_input_type_sort_order'];
								$count_options_name = sizeof($_POST['op_dd_name']);
							}
							
							$new_option_name_array = array();
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
								$lng_id = $languages[$i]['id'];
								if (tep_not_null($_POST['op_title'][$lng_id])){
									for ($op_radio_name_cnt = 0; $op_radio_name_cnt < $loop_num; $op_radio_name_cnt++){
										if (tep_not_null($option_name_loop[$op_radio_name_cnt][$lng_id])){
											$new_option_name_array[$lng_id][] = $option_name_loop[$op_radio_name_cnt][$lng_id];
										}
									}
									
									$lng_type = $languages[$i]['name'];
									if (count($new_option_name_array[$lng_id]) == 0){
										$no_error = false;
										if ($lng_id == '1'){
											$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_OPTION_FIELD.' '.'('.$lng_type.')', 'error');
										} 
										if ($lng_id == '2'){
											$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_OPTION_FIELD.' '.'('.$lng_type.')', 'error');
										} 
										if ($lng_id == '3'){
											$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_OPTION_FIELD.' '.'('.$lng_type.')', 'error');
										} 
									} elseif (count($new_option_name_array[$lng_id]) < 2){
										$no_error = false;
										if ($lng_id == '1'){
											$messageStack->add_session(ERROR_AT_LEAST_TWO_VALUES.' '.'('.$lng_type.')', 'error');
										} 
										if ($lng_id == '2'){
											$messageStack->add_session(ERROR_AT_LEAST_TWO_VALUES.' '.'('.$lng_type.')', 'error');
										} 
										if ($lng_id == '3'){
											$messageStack->add_session(ERROR_AT_LEAST_TWO_VALUES.' '.'('.$lng_type.')', 'error');
										}
									}
								}
							}
							
							if ($no_error) {
								$arr = array(	'events_options_status' => tep_db_prepare_input($_POST['op_status']),
									 			'events_options_input_type' => tep_db_prepare_input($_POST['op_type_id']),
									 			'events_options_required' => tep_db_prepare_input($_POST['op_mandatory_status']),
									 			'events_options_sort_order' => tep_db_prepare_input($_POST['op_sort_order']),
									 			'events_options_last_modified' => 'now()',
								     		);
								if ($action == 'add_new_option'){
									$arr['events_id'] = tep_db_prepare_input($_POST['events_id']);
									$arr['events_options_date_added'] = 'now()';
									
									if (tep_db_perform(TABLE_EVENTS_OPTIONS, $arr)) {
										$messageStack->add_session(SUCCESS_UPDATE_EVENT_OPTION, 'success');
										$events_options_id = tep_db_insert_id();
										
										if ($count_options_name > 0) {
											foreach ($_POST['op_title'] as $op_title_lang_id => $op_title_heading_title) {//get language id + language loop
												for ($eo_radio_value_cnt=0; $eo_radio_value_cnt < $count_options_name; $eo_radio_value_cnt++) {//loop to get all the row(s)
													if (tep_not_null($option_name_loop[$eo_radio_value_cnt][$op_title_lang_id])) {//prevent from storing empty row to db
														$sub_arr = array(	'events_options_id' => tep_db_prepare_input($events_options_id),
																			'language_id' => tep_db_prepare_input($op_title_lang_id),
																			'events_options_values' => tep_db_prepare_input($option_name_loop[$eo_radio_value_cnt][$op_title_lang_id]),
																 			'events_options_values_sort_order' => tep_db_prepare_input($option_sort_loop[$eo_radio_value_cnt][$op_title_lang_id]),
														     			);
													    tep_db_perform(TABLE_EVENTS_OPTIONS_VALUES, $sub_arr);
													}
												}
											}
										}
									}
								} else if ($action == 'edit_option'){
									if (tep_db_perform(TABLE_EVENTS_OPTIONS, $arr, 'update', 'events_options_id='.tep_db_input($_POST['options_id']))) {
										$messageStack->add_session(SUCCESS_UPDATE_EVENT_OPTION, 'success');
										$count_options_name = sizeof($option_name_loop);
										
										if ($count_options_name > 0) {
											$delete_events_options_value_sql = "	DELETE FROM ".TABLE_EVENTS_OPTIONS_VALUES." 
																					WHERE events_options_id = '".tep_db_input($_POST['options_id'])."';";
											tep_db_query($delete_events_options_value_sql);
											
											foreach ($_POST['op_title'] as $op_title_lang_id => $op_title_heading_title) {//get language id + language loop
												for ($eo_radio_value_cnt=0; $eo_radio_value_cnt < $count_options_name; $eo_radio_value_cnt++) {//loop to get all the row(s)
													if (tep_not_null($option_name_loop[$eo_radio_value_cnt][$op_title_lang_id])){//prevent from storing empty row to db
														$sub_arr = array(	'events_options_id' => tep_db_prepare_input($_POST['options_id']),
																			'language_id' => tep_db_prepare_input($op_title_lang_id),
																			'events_options_values' => tep_db_prepare_input($option_name_loop[$eo_radio_value_cnt][$op_title_lang_id]),
																 			'events_options_values_sort_order' => tep_db_prepare_input($option_sort_loop[$eo_radio_value_cnt][$op_title_lang_id]),
														     			);
													    tep_db_perform(TABLE_EVENTS_OPTIONS_VALUES, $sub_arr);
													}
												}
											}
										}
									}
								}
								
								foreach ($_POST['op_title'] as $op_title_lang_id => $op_title_heading_title) {//loop to get all languages
									$sql_data_array = array('events_options_title' => tep_db_prepare_input($op_title_heading_title),
			                    							'events_options_note' => tep_db_prepare_input($_POST['op_note'][$op_title_lang_id]),
										 					'events_options_err_msg' => tep_db_prepare_input($_POST['op_err_msg'][$op_title_lang_id])
			                    							);
									
									if ($action == 'add_new_option'){
										$sql_data_array['events_options_id'] = tep_db_prepare_input($events_options_id);
										$sql_data_array['language_id'] = tep_db_prepare_input($op_title_lang_id);
										
										tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array);
									} elseif ($action == 'edit_option') {
										$events_desc_lang_id_select ="	SELECT events_options_id
												 					   	FROM " . TABLE_EVENTS_OPTIONS_DESCRIPTION . "
												 						WHERE events_options_id = '" . tep_db_input($events_options_id) . "'
												 							AND language_id = '" . (int)$op_title_lang_id . "'";
										$events_desc_lang_id_result = tep_db_query($events_desc_lang_id_select);
										
										if (tep_db_num_rows($events_desc_lang_id_result) == 0) {//if no record
											$sql_data_array['events_options_id'] = tep_db_prepare_input($events_options_id);
											$sql_data_array['language_id'] = tep_db_prepare_input($op_title_lang_id);
											tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array);
										} else { //if there is/are record(s)
											tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array, 'update', "events_options_id='" . tep_db_input($events_options_id) . "' AND language_id = '" . (int)$op_title_lang_id ."'");
										}
									}
								}
								tep_redirect(tep_href_link(FILENAME_EVENTS));
							}
							
							break;
						
						case '5' ://## INFORMATION TEXT
							for ($i=1, $n=sizeof($languages); $i<$n; $i++) {//if tep_not_null(otpion title) op_size & op_max_size_text != ''
								$lng_id = $languages[$i]['id'];
								$lng_type = $languages[$i]['name'];
								if (tep_not_null($_POST['op_title'][$lng_id])){
									if (!tep_not_null($_POST['op_text_name'][$lng_id])) {
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_DISPLAY_TEXT.' '.'('.$lng_type.')', 'error');
									}
								}
							}
							
							if (!tep_not_null($_POST['op_text_name'][1])) {
								$no_error = false;
								$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_DISPLAY_TEXT, 'error');
							} elseif($no_error) {
								$share_save_sql = true;
								$input_type_case = 5;//edit option on page load
							}
							break;
							
						case '6' ://## UPLOAD FILE
							for ($i=1, $n=sizeof($languages); $i<$n; $i++) {//if tep_not_null(otpion title) op_size & op_max_size_text != ''
								$lng_id = $languages[$i]['id'];
								$lng_type = $languages[$i]['name'];
								if (tep_not_null($_POST['op_title'][$lng_id])){
									if (!tep_not_null($_POST['op_max_size_upload'][$lng_id]) || ($_POST['op_max_size_upload'][$lng_id] == 0)) {
										$no_error = false;
										$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_MAX_UPLOAD_SIZE.' '.'or 0'.' '.'('.$lng_type.')', 'error');
									}
								}
							}
							
							if ((!tep_not_null($_POST['op_max_size_upload'][1]))) {
								$no_error = false;
								$messageStack->add_session(ERROR_CANNOT_LEFT_BLANK_MAX_UPLOAD_SIZE, 'error');
								
							} elseif ($no_error) {
								$share_save_sql = true;
								$input_type_case = 6;//edit option on page load
							}
							break;
						
						default :
							break;
					}
							
					if ($share_save_sql) {
						$arr = array(	'events_options_status' => tep_db_prepare_input($_POST['op_status']),
							 			'events_options_input_type' => tep_db_prepare_input($_POST['op_type_id']),
							 			'events_options_required' => tep_db_prepare_input($_POST['op_mandatory_status']),
							 			'events_options_sort_order' => tep_db_prepare_input($_POST['op_sort_order']),
							 			'events_options_last_modified' => 'now()',
						     		);

						if ($action == 'add_new_option'){
							$arr['events_id'] = tep_db_prepare_input($_POST['events_id']);
							$arr['events_options_date_added'] = 'now()';
							if (tep_db_perform(TABLE_EVENTS_OPTIONS, $arr)) {
								$messageStack->add_session(SUCCESS_UPDATE_EVENT_OPTION, 'success');
								$events_options_id = tep_db_insert_id();
							}
						} else if ($action == 'edit_option') {
							tep_db_perform(TABLE_EVENTS_OPTIONS, $arr, 'update', "events_options_id='" . tep_db_input($_POST['options_id']) . "'");
							$messageStack->add_session(SUCCESS_UPDATE_EVENT_OPTION, 'success');
						}
						
						if ($input_type_case == '1') {
							$events_options_row_size	= $_POST['op_size'];
							$events_options_max_size	= $_POST['op_max_size_text'];
						} else if ($input_type_case == '2') {
							$events_options_row_size	= $_POST['op_row_textarea'];
							$events_options_column_size = $_POST['op_column_textarea'];
							$events_options_max_size	= $_POST['op_max_size_textarea'];
						} else if ($input_type_case == '5') {
							$events_options_name		= $_POST['op_text_name'];
						} else if ($input_type_case == '6') {
							$events_options_max_size	= $_POST['op_max_size_upload'];
						}
						
						foreach ($_POST['op_title'] as $op_title_lang_id => $op_title_heading_title) {
							$sql_data_array = array('events_options_title' => tep_db_prepare_input($op_title_heading_title),
	                    							'events_options_note' => tep_db_prepare_input(tep_not_null($_POST['op_note'][$op_title_lang_id]) ? $_POST['op_note'][$op_title_lang_id] : ''),
	                    							'events_options_name' => tep_db_prepare_input(tep_not_null($events_options_name[$op_title_lang_id]) ? $events_options_name[$op_title_lang_id] : ''),
	                    							'events_options_row_size' => tep_db_prepare_input(tep_not_null($events_options_row_size[$op_title_lang_id]) ? $events_options_row_size[$op_title_lang_id] : ''),
													'events_options_column_size' => tep_db_prepare_input(tep_not_null($events_options_column_size[$op_title_lang_id]) ? $events_options_column_size[$op_title_lang_id] : ''),
								 					'events_options_max_size' => tep_db_prepare_input(tep_not_null($events_options_max_size[$op_title_lang_id]) ? $events_options_max_size[$op_title_lang_id] : ''),
								 					'events_options_err_msg' => tep_db_prepare_input(tep_not_null($_POST['op_err_msg'][$op_title_lang_id]) ? $_POST['op_err_msg'][$op_title_lang_id] : '')
	                    							);
	                    	
							if ($action == 'add_new_option') {
								$sql_data_array['events_options_id'] = tep_db_prepare_input($events_options_id);
								$sql_data_array['language_id'] = tep_db_prepare_input($op_title_lang_id);
								tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array);
							} else if ($action == 'edit_option') {
								$events_desc_lang_id_select ="	SELECT events_options_id
										 					   	FROM " . TABLE_EVENTS_OPTIONS_DESCRIPTION . "
										 						WHERE events_options_id = '" . tep_db_input($events_options_id) . "'
										 							AND language_id = '" . (int)$op_title_lang_id . "'";
								$events_desc_lang_id_result = tep_db_query($events_desc_lang_id_select);
								
								if (tep_db_num_rows($events_desc_lang_id_result) == 0) {//if no record
									$sql_data_array['events_options_id'] = tep_db_prepare_input($events_options_id);
									$sql_data_array['language_id'] = tep_db_prepare_input($op_title_lang_id);
									tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array);
								} else { //if there is/are record(s)
									tep_db_perform(TABLE_EVENTS_OPTIONS_DESCRIPTION, $sql_data_array, 'update', "events_options_id='" . tep_db_input($events_options_id) . "' AND language_id = '" . (int)$op_title_lang_id ."'");
								}
							}
						}
					}				
					tep_redirect(tep_href_link(FILENAME_EVENTS));
							
					if ($no_error == false) {
						tep_redirect(tep_href_link(FILENAME_EVENTS, "action=new_option&events_id=".$_POST['events_id']));
					}
				}
				
				break;
		case 'email_template' ://on submit
			if ((isset($_POST['secure_code'])) && ($_POST['secure_code'] == 'secured')) {
				if (isset($_POST['events_email_tpl'])) {
					$edit_event_id = tep_db_prepare_input($_POST['events_id']);
					
					foreach ($_POST['events_email_tpl'] as $email_tpl_lang_id => $events_email_tpl) {//update email content into db
						$events_desc_select_sql = "	SELECT events_id
													FROM " . TABLE_EVENTS_DESCRIPTION . "
													WHERE events_id = '" . tep_db_input($edit_event_id) . "'
														AND language_id = '".(int)$email_tpl_lang_id."'";
						$events_desc_result_sql = tep_db_query($events_desc_select_sql);
						
						if (tep_db_num_rows($events_desc_result_sql)) { // Exist
							$event_desc_data_array = array('events_email_tpl' => tep_db_prepare_input($events_email_tpl));
							tep_db_perform(TABLE_EVENTS_DESCRIPTION, $event_desc_data_array, 'update', "events_id='".tep_db_input($edit_event_id)."' AND language_id = '".(int)$email_tpl_lang_id."'");
						} else {
							$event_desc_data_array = array( 'events_id' => $edit_event_id,
															'language_id' => (int)$email_tpl_lang_id,
															'events_name' => '',
															'events_email_tpl' => tep_db_prepare_input($events_email_tpl));
							tep_db_perform(TABLE_EVENTS_DESCRIPTION, $event_desc_data_array);
						}
					}
					$messageStack->add_session(SUCCESS_UPDATE_EMAIL_TEMPLATE, 'success');
				}
				
				tep_redirect(tep_href_link(FILENAME_EVENTS));
			}
			
			break;
		default :
			
			break;
	}
}

$events_select_sql = "	SELECT e.events_id, e.news_id, e.events_remark, e.events_sender_email, e.events_admin_copy_email,
							   e.events_status, e.events_order_period_date_from, e.events_order_period_date_to,
							   e.events_order_period_note, e.events_order_period_empty_err_msg, e.events_order_period_invalid_err_msg, ed.events_name, ed.events_email_tpl
						FROM " . TABLE_EVENTS . " as e
						INNER JOIN ".TABLE_EVENTS_DESCRIPTION." as ed
							ON (e.events_id = ed.events_id)
						WHERE ed.language_id = '1'
						ORDER BY e.events_id DESC";

if ($show_records != "ALL") {
	$template_split_object = new splitPageResults($_GET['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $events_select_sql, $events_select_sql_numrows, true);
}
$events_result_sql = tep_db_query($events_select_sql);

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/latest_news_xmlhttp.js"></script>
</head>

<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td class="pageHeading"><?=HEADING_TITLE?></td>
					</tr>
				</table>
<?
ob_start();
?>
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td class="pageHeading"><span class="main"><?=$subTitle?></span></td>
					</tr>
					<tr id="add_new_events_1">
						<td class="main" width="15%">
<!-- Add new option -->
<?php
   	switch ($action) {
    	case 'new_option' :
			$input_type_array[] = array("id" => '1', "text" => "Text Box", "param" => '');
			$input_type_array[] = array("id" => '2', "text" => "Text Area", "param" => '');
			$input_type_array[] = array("id" => '3', "text" => "Radio Button", "param" => '');
			$input_type_array[] = array("id" => '4', "text" => "Drop Down Menu", "param" => '');
			$input_type_array[] = array("id" => '5', "text" => "Information Text", "param" => '');
			$input_type_array[] = array("id" => '6', "text" => "File Upload", "param" => '');
?>
					<?=tep_draw_form('Opt_Event', FILENAME_EVENTS, tep_get_all_get_params(array('action')) . 'action=add_new_option', 'post');?>
					<?=tep_draw_hidden_field('events_id',$events_id);?>
						<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
							<tr>
								<td colspan="2">
									<table border="0" width="62%" cellspacing="0" cellpadding="0">
										<tr>
											<td>
												<div id="languages_tab">
													<ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
														<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>											
													</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
												<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
													<table border="0" width="100%" cellspacing="0" cellpadding="4">
														<tr>
															<td class="main" valign="top" width="20%"><?=ENTRY_OPTIONS_TITLE?></td>
															<td class="main"><?=tep_draw_input_field('op_title[' . $languages[$lang_cnt]['id'] . ']', '', 'size="35" id="op_title[' . $languages[$lang_cnt]['id']  . ']"', $lang_cnt <= 0 ? true : '');?></td>
														</tr>
														<tr>
															<td class="main" valign="top"><?=ENTRY_OPTIONS_NOTE?></td>
															<td class="main"><?=tep_draw_input_field('op_note[' . $languages[$lang_cnt]['id'] . ']', '', 'size="70" id="op_note_[' . $languages[$lang_cnt]['id'] . ']"');?></td>
														</tr>
														<tr valign="top">
															<td class="main" valign="top"><?=ENTRY_OPTIONS_SETTING?></td>
															<td style="padding:10px 5px 10px 5px;">
																<!--type_1--><!-- textbox -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																	<tr id="input_type_new_1_<?=$lang_cnt?>" style="display:none">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_SIZE?></td>
																		<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('op_size[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_size[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; } "', $lang_cnt <= 0 ? true : '');?></td>
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_CHAR?></td>
																		<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('op_max_size_text[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_max_size_text[' . $languages[$lang_cnt]['id']  . ']"  onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt <= 0 ? true : '');?></td>
																	</tr>
																</table>
																<!--type_2--><!-- textarea -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																	<tr id="input_type_new_2_<?=$lang_cnt?>" style="display:none">
																		<td>
																		<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																			<tr valign="top">
																				<td class="main" valign="top"><?=ENTRY_OPTIONS_ROW?></td>
																				<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_row_textarea[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_row_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; } "', $lang_cnt <= 0 ? true : '');?></td>
																			</tr>
																			<tr valign="top">
																				<td class="main" valign="top"><?=ENTRY_OPTIONS_COLUMN?></td>
																				<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_column_textarea[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_column_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt <= 0 ? true : '');?></td>
																			</tr>
																			<tr valign="top">
																				<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_CHAR?></td>
																				<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_max_size_textarea[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_max_size_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt <= 0 ? true : '');?></td>
																			</tr>
																		</table>
																	</td>
																	</tr>
																</table>
																<!--type_3--><!-- radio button -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																	<tr id="input_type_new_3_<?=$lang_cnt?>" style="display:none">
																		<td>
																			<div id="input_type_3">
																				<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																					<tbody id="opt_radio_<?=$languages[$lang_cnt]['id']?>">
																						<tr valign="top" class="ordersBoxHeading">
																							<td width="300" style="padding:5px 10px 5px 10px"><?=TABLE_HEADING_EVENTS_OPTIONS?></td>
																							<td width="100" align="center" style="padding:5px 10px 5px 10px"><?=ENTRY_OPTION_ORDER?></td>
																						</tr>
<?php
	for ($eo_radio_cnt=0; $eo_radio_cnt < 4; $eo_radio_cnt++) {
		echo '<tr>
				<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '', ' class="option_radio_'.$languages[$lang_cnt]['id'].'" size="50" id="op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id']  . ']"').'</td>
				<td align="center">'.tep_draw_input_field('op_radio_input_type_sort_order['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '50000', 'size="7" id="op_radio_input_type_sort_order['.$eo_radio_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
			</tr>';
	}
?>
																					</tbody>
																					<tr valign="top">
																						<td colspan="10" style="padding:5px 10px 5px 10px">[ <a href="javascript:addNewOptionRow('input_type_3','radio', <?=$languages[$lang_cnt]['id']?>);"><?=TEXT_ADD_NEW_OPTION?></a> ]</td>
																					</tr>
																				</table>
																			</div>
																		</td>
																	</tr>
																</table>
																<!--type_4--><!-- drop down menu --><!-- DONE -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																	<tr id="input_type_new_4_<?=$lang_cnt?>" style="display:none">
																		<td>
																			<div id="input_type_4">
																			<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																				
																				<tbody id="opt_dd_<?=$languages[$lang_cnt]['id']?>">
																				<tr valign="top" class="ordersBoxHeading">
																					<td width="300" style="padding:5px 10px 5px 10px"><?=TABLE_HEADING_EVENTS_OPTIONS?></td>
																					<td width="100" align="center" style="padding:5px 10px 5px 10px"><?=ENTRY_OPTION_ORDER?></td>
																				</tr>
<?php 
	for ($eo_dropdown_cnt=0; $eo_dropdown_cnt<4; $eo_dropdown_cnt++) {
		echo '<tr>
				<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_dd_name['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '', ' class="option_dd_'.$languages[$lang_cnt]['id'].'" size="50" id="op_dd_name['.$eo_dropdown_cnt.']['.$languages[$lang_cnt]['id'].']"').'</td>
				<td align="center">'.tep_draw_input_field('op_dd_input_type_sort_order['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '50000', 'size="7" id="op_dd_input_type_sort_order['.$eo_dropdown_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
			</tr>';
	}
?>
																				</tbody>
																				<tr valign="top">
																					<td colspan="10" style="padding:5px 10px 5px 10px">[ <a href="javascript:addNewOptionRow('input_type_4','dd', <?=$languages[$lang_cnt]['id']?>);"><?=TEXT_ADD_NEW_OPTION?></a> ]</td>
																				</tr>
																			</table>
																			</div>
																		</td>
																	</tr>
																</table>
																<!--type_5--><!-- information text -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em; valign="top">
																	<tr id="input_type_new_5_<?=$lang_cnt?>" style="display:none">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_DISPLAY_TEXT?></td>
																		<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_text_name[' . $languages[$lang_cnt]['id'] . ']', '', 'size="50" id="op_text_name[' . $languages[$lang_cnt]['id'] . ']"', $lang_cnt <= 0 ? true : '');?></td>
																	</tr>
																</table>
																<!--type_6--><!-- file upload -->
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em; valign="top">
																	<tr id="input_type_new_6_<?=$lang_cnt?>" style="display:none">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_UPLOAD_SIZE?></td>
																		<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_max_size_upload[' . $languages[$lang_cnt]['id'] . ']', '', 'size="7" id="op_max_size_upload[' . $languages[$lang_cnt]['id'] . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt <= 0 ? true : '');?></td>
																		<td class="main" valign="top">kb</td>
																	</tr>
																</table>
																<tr valign="top">
																	<td class="main" valign="top"><?=ENTRY_OPTIONS_ERROR_MSG?>
																	<br/><div style="font-size:9px;">(<?=ENTRY_EVENT_INPUT_DOESNT_PROVIDED?>)</div></td>
																	<td style="padding:10px 5px 10px 5px;">
																		<?=tep_draw_input_field('op_err_msg[' . $languages[$lang_cnt]['id'] . ']', '', 'size="70" id="op_err_msg_[' . $languages[$lang_cnt]['id'] . ']"');?>
																	</td>
																</tr>
															</td>
														</tr>
													</table>
												</div>
			<? } ?>
												</div><!--languages_tab-->
											</td>
										</tr>
									</table>
									<script type="text/javascript">
										jQuery(document).ready(function() {
											jQuery.noConflict();
											jQuery("#languages_tab > ul").tabs();
										});
									</script>
								</td>
							</tr>
							<tr valign="top">
								<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_INPUT_TYPE?></td>
								<td style="padding:10px 5px 10px 5px;">
									<?=tep_draw_pull_down_menu("op_type_id", $input_type_array, '', 'id="op_type_id" onChange="input_type(this.value,\'Opt_Event\');"')?>
								</td>
							</tr>
							<tr align="left" valign="top">
								<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_MANDATORY?></td>
								<td style="padding:10px 5px 10px 5px;">
									<?=tep_draw_radio_field('op_mandatory_status', '1', false, '', 'id=op_mandatory_status_1');?><?=ENTRY_OPTIONS_YES?>
									<?=tep_draw_radio_field('op_mandatory_status', '0', true, '', 'id=op_mandatory_status_2');?><?=ENTRY_OPTIONS_NO?>
								</td>
							</tr>
							<tr align="left" valign="top">
								<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_STATUS?></td>
								<td style="padding:10px 5px 10px 5px;">
									<?=tep_draw_radio_field('op_status', '1', false);?><?=ENTRY_OPTIONS_ACTIVE?>
									<?=tep_draw_radio_field('op_status', '0', true);?><?=ENTRY_OPTIONS_INACTIVE?>
								</td>
							</tr>
							<tr valign="top">
								<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTION_ORDER?></td>
								<td style="padding:10px 5px 10px 5px;">
									<?=tep_draw_input_field('op_sort_order', '50000', 'size="70" id="op_sort_order" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', true);?>
								</td>
							</tr>
							<tr valign="top">
								<td width="125" style="padding:10px 5px 10px 5px;">&nbsp;</td>
								<td style="padding:10px 5px 10px 5px;">
									<?=tep_submit_button(BTN_INSERT, 'SubmitFrmEvent', 'onClick="return option_form_checking(\'Opt_Event\')"', 'inputbutton');?>
									<?=tep_button(BTN_CANCEL, '', tep_href_link(FILENAME_EVENTS), '', 'inputbutton');?>
								</td>
							</tr>
						</table>
					</form>
<!-- Edit event--> 
<?php 
		break;
	case 'edit_events' :
		$latest_news_select_array = array();
		$latest_news_id_array = array();
		
		$latest_news_id_select_sql = "SELECT news_groups_id, news_groups_name FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " WHERE language_id = '" . $_SESSION['languages_id'] . "'";
		$latest_news_id_result_sql = tep_db_query($latest_news_id_select_sql);
		$latest_news_id_array = array( array("id" => '', "text" => "All News", "param" => '') );
		
		while ($latest_news_id_row = tep_db_fetch_array($latest_news_id_result_sql)) {
			$latest_news_id_array[] = array('id' => $latest_news_id_row['news_groups_id'],'text' => $latest_news_id_row['news_groups_name'],'param' => '');
		}
		
		$latest_news_select_sql = "	SELECT lnd.news_id, lnd.headline 
									FROM " . TABLE_LATEST_NEWS. " AS ln 
									INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd 
										ON ln.news_id = lnd.news_id 
									WHERE lnd.language_id = '".$_SESSION['languages_id']."'
									ORDER BY ln.date_added DESC";
		$latest_news_result_sql = tep_db_query($latest_news_select_sql);
		
		while ($latest_news_row = tep_db_fetch_array($latest_news_result_sql)) {
			$latest_news_select_array[] = array('id'=> $latest_news_row["news_id"], 'text' => strip_tags($latest_news_row["headline"]));
		}
 ?> 
				<?=tep_draw_form('EditfrmEvent', FILENAME_EVENTS, tep_get_all_get_params(array('action')) . 'action=edit_events', 'post');?>
				<?=tep_draw_hidden_field('events_id',$_GET['events_id']);?>
					
					<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
						<tr>
							<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
						</tr>
						<tr valign="top">
							<td colspan="2">
								 <table border="0" width="55%" cellspacing="0" cellpadding="0">
										<tr>
											<td>
												<div id="languages_tab">
													<ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
														<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>
													</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
												<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
													<table border="0" width="100%" cellspacing="0" cellpadding="0">
														<tr valign="top">
															<td class="main" valign="top" width="20%"><?=ENTRY_OPTIONS_EVENT_NAME?></td>
															<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('event_name[' . $languages[$lang_cnt]['id'] . ']', (isset($events_lang_info_array[$languages[$lang_cnt]['id']]['events_name']) ? $events_lang_info_array[$languages[$lang_cnt]['id']]['events_name'] : ''), 'size="35" id="event_name"', $lang_cnt == 0 ? true : '');?>
														</tr>
													</table>
												</div>
<? } ?>
												</div><!--languages_tab-->
											</td>
										</tr>
								</table>
							<script type="text/javascript">
								jQuery(document).ready(function() {
									jQuery.noConflict();
									jQuery("#languages_tab > ul").tabs();
								});
							</script>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_LATEST_NEWS?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_pull_down_menu("news_id", $latest_news_id_array, '', 'id="news_id" onChange="refreshDynamicSelectOptions(this, \'headline_id\', \''.$_SESSION['languages_id'].'\', false);"')?>
  								<?=tep_draw_pull_down_menu("headline_id", $latest_news_select_array, $news_id, ' id="headline_id" ')?>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_REMARK?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_textarea_field("events_remark", "", 50, 10, $events_remark, 'id="events_remark"');?><font color="red">*</font><?=ENTRY_TASK_ERROR?>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_SENDER_EMAIL?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_input_field('events_sender_email', $events_sender_email, 'size="35" id="events_sender_email"', true);?>
  								<div><?=htmlspecialchars(TEXT_SENDER_EMAIL_DESCRIPTION)?></div>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_ADMIN_COPY_EMAIL?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_input_field('events_admin_copy_email', $events_admin_copy_email, 'size="35" id="events_admin_copy_email"', true);?>
  								<div><?=htmlspecialchars(TEXT_ADMIN_COPY_EMAIL_DESCRIPTION)?></div>
							</td>
						</tr>
						<tr><td colspan="2">
						<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
							<tr>
								<td>
								<fieldset>
									<legend><?=FIELDSET_ORDER_TITLE_SETTING?></legend>
									<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_EVENT_VALIDATION_PERIOD?></td>
										<td style="padding:10px 5px 10px 5px;">
											<table border="0" width="100%" cellpadding="0" cellspacing="0">
												<tr>
													<td class="main" valign="top" width="100" nowrap><?=ENTRY_EVENTS_START_DATE?></td>
				  									<td class="main" valign="top" width="200" nowrap><?=tep_draw_input_field('start_date', tep_not_null($events_order_period_date_from) ? $events_order_period_date_from : '', 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.EditfrmEvent.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.EditfrmEvent.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?><br/><?=ENTRY_EVENTS_DATE_MARK?></td>
						    						<td class="main" valign="top" width="100" nowrap><?=ENTRY_EVENTS_END_DATE?></td>
						    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', tep_not_null($events_order_period_date_to) ? $events_order_period_date_to : '', 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.EditfrmEvent.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.EditfrmEvent.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?><br/><?=ENTRY_EVENTS_DATE_MARK?></td>
						    					</tr>
						    				</table>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_EVENT_NOTE?></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_note", $events_order_period_note, 'size="55" id="events_note"');?>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_ERROR_MSG?><div style="font-size:9px;">(<?=ENTRY_EVENT_INPUT_DOESNT_PROVIDED?>)</div></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_err_msg", $events_order_period_empty_err_msg, 'size="55" id="events_err_msg"');?>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_ERROR_MSG?><div style="font-size:9px;">(<?=ENTRY_EVENT_INVALID_ORDER_RANGE?>)</div></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_err_msg2", $events_order_period_invalid_err_msg, 'size="55" id="events_err_msg2"');?>
										</td>
									</tr>
									</table>
								</fieldset>
								</td>
							</tr>
						</table>
					</td></tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;">&nbsp;</td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_submit_button('Update', 'SubmitEditFrmEvent', 'onClick="return event_form_checking(\'SubmitEditFrmEvent\')"', 'inputbutton');?>
							</td>
						</tr>
					</table>
				</form>
<!-- Edit Option--> 
<?php 
		break;
	case 'edit_option' :
			$disabled_mandatory = '';
			$disabled_note = '';
			$disabled_err_msg = '';
			
			if ($options_input_type == 5 || $options_input_type == 4 || $options_input_type == 3) {
				$disabled_mandatory = 'DISABLED';
				$disabled_err_msg = 'DISABLED';
				
				if ($options_input_type == 5){
					$disabled_note = 'DISABLED';
				} else {
					$disabled_note = '';
				}
			} else {
				$disabled_mandatory = '';
				$disabled_note = '';
				$disabled_err_msg = '';
			}
			
			$input_type_array[] = array("id" => '1', "text" => "Text Box", "param" => '');
			$input_type_array[] = array("id" => '2', "text" => "Text Area", "param" => '');
			$input_type_array[] = array("id" => '3', "text" => "Radio Button", "param" => '');
			$input_type_array[] = array("id" => '4', "text" => "Drop Down Menu", "param" => '');
			$input_type_array[] = array("id" => '5', "text" => "Information Text", "param" => '');
			$input_type_array[] = array("id" => '6', "text" => "File Upload", "param" => '');
?> 
			<?=tep_draw_form('EditOpt', FILENAME_EVENTS, tep_get_all_get_params(array('action')) . 'action=edit_option', 'post');?>
			<?=tep_draw_hidden_field('options_id',$options_id);?>
				<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
					<tr>
						<td colspan="2">
							<table border="0" width="62%" cellspacing="0" cellpadding="0">	
								<tr>
									<td>
										<div id="languages_tab">
											<ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
												<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>
											</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
										<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
											<table border="0" width="100%" cellspacing="0" cellpadding="4">
												<tr>
													<td class="main" valign="top" width="20%"><?=ENTRY_OPTIONS_TITLE?></td>
													<td class="main"><?=tep_draw_input_field('op_title[' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_title']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_title'] : ''), 'size="35" id="op_title[' . $languages[$lang_cnt]['id']  . ']"', $lang_cnt == 0 ? true : '');?></td>
												</tr>
												<tr>
													<td class="main" valign="top"><?=ENTRY_OPTIONS_NOTE?></td>
													<td class="main"><?=tep_draw_input_field('op_note[' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_note']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_note'] : ''), 'size="70" id="op_note_[' . $languages[$lang_cnt]['id'] . ']"'.$disabled_note);?></td>
												</tr>
												<tr valign="top">
													<td class="main" valign="top"><?=ENTRY_OPTIONS_SETTING?></td>
													<td style="padding:10px 5px 10px 5px;">
														<!--type_1--><!-- textbox -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
															<tr id="input_type_new_1_<?=$lang_cnt?>" style="display:none">
																<td class="main" valign="top"><?=ENTRY_OPTIONS_SIZE?></td>
																<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('op_size[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 1 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_row_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_row_size'] : '') : '', 'size="7" id="op_size[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
																<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_CHAR?></td>
																<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('op_max_size_text[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 1 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size'] : '') : '', 'size="7" id="op_max_size_text[' . $languages[$lang_cnt]['id']  . ']"  onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
															</tr>
														</table>
														<!--type_2--><!-- textarea -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
															<tr id="input_type_new_2_<?=$lang_cnt?>" style="display:none">
																<td>
																<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																	<tr valign="top">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_ROW?></td>
																		<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_row_textarea[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 2 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_row_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_row_size'] : '')  : '', 'size="7" id="op_row_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
																	</tr>
																	<tr valign="top">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_COLUMN?></td>
																		<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_column_textarea[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 2 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_column_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_column_size'] : '')  : '', 'size="7" id="op_column_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
																	</tr>
																	<tr valign="top">
																		<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_CHAR?></td>
																		<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_max_size_textarea[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 2 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size'] : '')  : '', 'size="7" id="op_max_size_textarea[' . $languages[$lang_cnt]['id']  . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
																	</tr>
																</table>
															</td>
															</tr>
														</table>
														<!--type_3--><!-- radio button -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
															<tr id="input_type_new_3_<?=$lang_cnt?>" style="display:none">
																<td>
																	<div id="input_type_3">
																		<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																			<tbody id="opt_radio_<?=$languages[$lang_cnt]['id']?>">
																				<tr valign="top" class="ordersBoxHeading">
																					<td width="300" style="padding:5px 10px 5px 10px"><?=TABLE_HEADING_EVENTS_OPTIONS?></td>
																					<td width="100" align="center" style="padding:5px 10px 5px 10px"><?=ENTRY_OPTION_ORDER?></td>
																				</tr>
<?php 
	if ($options_input_type == 3) {
		for ($eo_radio_cnt=0; $eo_radio_cnt < (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values']) ? count($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values']) : 4); $eo_radio_cnt++) {
			echo '<tr>
					<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values'][$eo_radio_cnt]) ? $get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values'][$eo_radio_cnt] : ''), ' class="option_radio_'.$languages[$lang_cnt]['id'].'" size="50" id="op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id']  . ']"').'</td>
					<td align="center">'.tep_draw_input_field('op_radio_input_type_sort_order['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values_sort_order'][$eo_radio_cnt]) ? $get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values_sort_order'][$eo_radio_cnt] : ''), 'size="7" id="op_radio_input_type_sort_order['.$eo_radio_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
				</tr>';
		}
	} else {
		for ($eo_radio_cnt=0; $eo_radio_cnt < 4; $eo_radio_cnt++) {
			echo '<tr>
					<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '', ' class="option_radio_'.$languages[$lang_cnt]['id'].'" size="50" id="op_radio_name['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id']  . ']"').'</td>
					<td align="center">'.tep_draw_input_field('op_radio_input_type_sort_order['.$eo_radio_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '50000', 'size="7" id="op_radio_input_type_sort_order['.$eo_radio_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
				</tr>';
		}
	}
?>
																			</tbody>
																			<tr valign="top">
																				<td colspan="10" style="padding:5px 10px 5px 10px">[ <a href="javascript:addNewOptionRow('input_type_3','radio', <?=$languages[$lang_cnt]['id']?>);"><?=TEXT_ADD_NEW_OPTION?></a> ]</td>
																			</tr>
																		</table>
																	</div>
																</td>
															</tr>
														</table>
														<!--type_4--><!-- drop down menu -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
															<tr id="input_type_new_4_<?=$lang_cnt?>" style="display:none">
																<td>
																	<div id="input_type_4">
																	<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em;" valign="top">
																		<tbody id="opt_dd_<?=$languages[$lang_cnt]['id']?>">
																		<tr valign="top" class="ordersBoxHeading">
																			<td width="300" style="padding:5px 10px 5px 10px"><?=TABLE_HEADING_EVENTS_OPTIONS?></td>
																			<td width="100" align="center" style="padding:5px 10px 5px 10px"><?=ENTRY_OPTION_ORDER?></td>
																		</tr>
<?php 
	if ($options_input_type == 4) {
		for ($eo_dropdown_cnt=0; $eo_dropdown_cnt < (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values']) ? count($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values']) : 4); $eo_dropdown_cnt++) {
			echo '<tr>
					<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_dd_name['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values'][$eo_dropdown_cnt]) ? $get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values'][$eo_dropdown_cnt] : ''), ' class="option_dd_'.$languages[$lang_cnt]['id'].'" size="50" id="op_dd_name['.$eo_dropdown_cnt.']['.$languages[$lang_cnt]['id'].']"').'</td>
					<td align="center">'.tep_draw_input_field('op_dd_input_type_sort_order['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values_sort_order'][$eo_dropdown_cnt]) ? $get_options_value_array[$languages[$lang_cnt]['id']]['events_options_values_sort_order'][$eo_dropdown_cnt] : ''), 'size="7" id="op_dd_input_type_sort_order['.$eo_dropdown_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
				</tr>';
		}
	} else {
		for ($eo_dropdown_cnt=0; $eo_dropdown_cnt<4; $eo_dropdown_cnt++) {
			echo '<tr>
					<td style="padding:5px 10px 5px 10px">'.tep_draw_input_field('op_dd_name['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '', ' class="option_dd_'.$languages[$lang_cnt]['id'].'" size="50" id="op_dd_name['.$eo_dropdown_cnt.']['.$languages[$lang_cnt]['id'].']"').'</td>
					<td align="center">'.tep_draw_input_field('op_dd_input_type_sort_order['.$eo_dropdown_cnt.'][' . $languages[$lang_cnt]['id'] . ']', '50000', 'size="7" id="op_dd_input_type_sort_order['.$eo_dropdown_cnt.']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"').'</td>
				</tr>';
		}
	}
?>
																		</tbody>
																		<tr valign="top">
																			<td colspan="10" style="padding:5px 10px 5px 10px">[ <a href="javascript:addNewOptionRow('input_type_4','dd', <?=$languages[$lang_cnt]['id']?>);"><?=TEXT_ADD_NEW_OPTION?></a> ]</td>
																		</tr>
																	</table>
																	</div>
																</td>
															</tr>
														</table>
														<!--type_5--><!-- information text -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em; valign="top">
															<tr id="input_type_new_5_<?=$lang_cnt?>" style="display:none">
																<td class="main" valign="top"><?=ENTRY_OPTIONS_DISPLAY_TEXT?></td>
																<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_text_name[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 5 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_name']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_name'] : '')  : '', 'size="50" id="op_text_name[' . $languages[$lang_cnt]['id'] . ']"', $lang_cnt == 0 ? true : '');?></td>
															</tr>
														</table>
														<!--type_6--><!-- file upload -->
														<table border="0" cellspacing="0" cellpadding="0" style="font-size:1em; valign="top">
															<tr id="input_type_new_6_<?=$lang_cnt?>" style="display:none">
																<td class="main" valign="top"><?=ENTRY_OPTIONS_MAX_UPLOAD_SIZE?></td>
																<td style="padding:0px 10px 10px 10px"><?=tep_draw_input_field('op_max_size_upload[' . $languages[$lang_cnt]['id'] . ']', $options_input_type == 6 ? (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_max_size'] : '')  : '', 'size="7" id="op_max_size_upload[' . $languages[$lang_cnt]['id'] . ']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', $lang_cnt == 0 ? true : '');?></td>
																<td class="main" valign="top">kb</td>
															</tr>
														</table>
														<tr valign="top">
															<td class="main" valign="top"><?=ENTRY_OPTIONS_ERROR_MSG?>
															<br/><div style="font-size:9px;">(<?=ENTRY_EVENT_INPUT_DOESNT_PROVIDED?>)</div></td>
															<td style="padding:10px 5px 10px 5px;">
																<?=tep_draw_input_field('op_err_msg[' . $languages[$lang_cnt]['id'] . ']', (isset($get_options_info_array[$languages[$lang_cnt]['id']]['events_options_err_msg']) ? $get_options_info_array[$languages[$lang_cnt]['id']]['events_options_err_msg'] : ''), 'size="70" id="op_err_msg_[' . $languages[$lang_cnt]['id'] . ']"'.$disabled_err_msg);?>
															</td>
														</tr>
													</td>
												</tr>
											</table>
										</div>
<? } ?>
										</div><!--languages_tab-->
									</td>
								</tr>
							</table>
							<script type="text/javascript">
								jQuery(document).ready(function() {
									jQuery.noConflict();
									jQuery("#languages_tab > ul").tabs();
								});
							</script>
						</td>
					</tr>
					<tr valign="top">
						<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_INPUT_TYPE?></td>
						<td style="padding:10px 5px 10px 5px;">
							<?=tep_draw_pull_down_menu("op_type_id", $input_type_array, $options_input_type, 'id="op_type_id" onChange="input_type(this.value,\'EditOpt\');"')?>
						</td>
					</tr>
					<tr align="left" valign="top">
						<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_MANDATORY?></td>
						<td style="padding:10px 5px 10px 5px;">
							<?=tep_draw_radio_field('op_mandatory_status', '1', $options_required == 1 ? true : false, '', 'id="op_mandatory_status_1"'.$disabled_mandatory);?><?=ENTRY_OPTIONS_YES?>
							<?=tep_draw_radio_field('op_mandatory_status', '0', $options_required == 0 ? true : false, '', 'id="op_mandatory_status_2"'.$disabled_mandatory);?><?=ENTRY_OPTIONS_NO?>
						</td>
					</tr>
					<tr align="left" valign="top">
						<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_STATUS?></td>
						<td style="padding:10px 5px 10px 5px;">
							<?=tep_draw_radio_field('op_status', '1', $options_status == 1 ? true : false);?><?=ENTRY_OPTIONS_ACTIVE?>
							<?=tep_draw_radio_field('op_status', '0', $options_status == 0 ? true : false);?><?=ENTRY_OPTIONS_INACTIVE?>
						</td>
					</tr>
					<tr valign="top">
						<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTION_ORDER?></td>
						<td style="padding:10px 5px 10px 5px;">
							<?=tep_draw_input_field('op_sort_order', $options_sort_order, 'size="70" id="op_sort_order" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }"', true);?>
						</td>
					</tr>
					<tr valign="top">
						<td width="125" style="padding:10px 5px 10px 5px;">&nbsp;</td>
						<td style="padding:10px 5px 10px 5px;">
							<?=tep_submit_button(BTN_UPDATE, 'EditFrmOption', 'onClick="return option_form_checking(\'EditOpt\')"', 'inputbutton');?>
							<?=tep_button(BTN_CANCEL, '', tep_href_link(FILENAME_EVENTS), '', 'inputbutton');?>
						</td>
					</tr>
				</table>
			</form>
<?php 
		break;
	case 'email_template' ://page load
			$events_email_tpl_select_sql = "	SELECT events_email_tpl, language_id
												FROM " . TABLE_EVENTS_DESCRIPTION . " 
												WHERE events_id = '" .tep_db_input($_GET['events_id']) . "'";
			$events_email_tpl_result_sql = tep_db_query($events_email_tpl_select_sql);
				$get_email_template_array = array();
				 while($events_email_tpl_row = tep_db_fetch_array($events_email_tpl_result_sql)) {
				 	If (tep_not_null($events_email_tpl_row['events_email_tpl'])) {
				 		$get_email_template_array[$events_email_tpl_row['language_id']]['events_email_tpl'] = $events_email_tpl_row['events_email_tpl'];
				 	}
				 }
?>
				<?=tep_draw_form('EmailTemp', FILENAME_EVENTS, tep_get_all_get_params(array('action')) . 'action=email_template', 'post');?>
				<?=tep_draw_hidden_field('events_id',$_GET['events_id']);?>
				<?=tep_draw_hidden_field('secure_code', 'secured');?>
							<table border="0" width="55%" cellspacing="0" cellpadding="0">
								<tr>
									<td>
										<div id="languages_tab">
											<ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
												<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>
											</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
										<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
											<table border="0" width="100%" cellspacing="0" cellpadding="0">
<? 
	if ($lang_cnt == '0'){
		$customer_notification_event_content = CUSTOMER_NOTIFICATION_EVENT_CONTENT_ENGLISH;
	} else if ($lang_cnt == '1') {
		$customer_notification_event_content = CUSTOMER_NOTIFICATION_EVENT_CONTENT_TRADITIONAL;
	} else if ($lang_cnt == '2') {
		$customer_notification_event_content =CUSTOMER_NOTIFICATION_EVENT_CONTENT_SIMPLIFIED;
	}
?>
												<tr>
													<td class="main" valign="top"><div style=""><?=tep_draw_textarea_field('events_email_tpl[' . $languages[$lang_cnt]['id'] . ']', '', 95, 30, (isset($get_email_template_array[$languages[$lang_cnt]['id']]['events_email_tpl']) ? $get_email_template_array[$languages[$lang_cnt]['id']]['events_email_tpl'] : $customer_notification_event_content), 'id="events_email_tpl"');?></div></td>
												</tr>
											</table>
										</div>
<? } ?>
										</div><!--languages_tab-->
									</td>
								</tr>
							</table>
							<script type="text/javascript">
								jQuery(document).ready(function() {
									jQuery.noConflict();
									jQuery("#languages_tab > ul").tabs();
								});
							</script>
						<div style="padding:10px;">
							<div style=""><?=TEXT_EMAIL_TEMP_DESCRIPTION_ROW_1?></div>
							<div style=""><?=TEXT_EMAIL_TEMP_DESCRIPTION_ROW_2?></div>
							<div style=""><?=TEXT_EMAIL_TEMP_DESCRIPTION_ROW_3?></div>
							
							<div style=""><?=htmlspecialchars(TEXT_EMAIL_TEMP_DESCRIPTION_ROW_4)?></div>
							<div style="clear:both;padding:20px 0px 0px 20px;">
								<?=tep_submit_button(BTN_SUBMIT, 'SubmitFrmEmailTemp', '', 'inputbutton');?>
								<?=tep_button(BTN_CANCEL, '', tep_href_link(FILENAME_EVENTS), '', 'inputbutton');?>
							</div>
						</div>
			</form>
<?php
		break;
	case 'new_events' :
			$latest_news_select_array = array();
			$latest_news_id_array = array();
			
			$latest_news_id_select_sql = "SELECT news_groups_id, news_groups_name FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION. " WHERE language_id = '" . $_SESSION['languages_id'] . "'";
			$latest_news_id_result_sql = tep_db_query($latest_news_id_select_sql);
			$latest_news_id_array = array( array("id" => '', "text" => "All News", "param" => '') );
			
			while ($latest_news_id_row = tep_db_fetch_array($latest_news_id_result_sql)) {
				$latest_news_id_array[] = array('id' => $latest_news_id_row['news_groups_id'],'text' => $latest_news_id_row['news_groups_name'],'param' => '');
			}
			
			$latest_news_select_sql = "	SELECT lnd.news_id, lnd.headline
										FROM " . TABLE_LATEST_NEWS. " AS ln
										INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd 
											ON ln.news_id = lnd.news_id
										WHERE lnd.language_id = '".$_SESSION['languages_id']."'
										ORDER BY ln.date_added DESC";
			$latest_news_result_sql = tep_db_query($latest_news_select_sql);
			
			while ($latest_news_row = tep_db_fetch_array($latest_news_result_sql)) {
				$latest_news_select_array[] = array('id'=> $latest_news_row["news_id"], 'text' => strip_tags($latest_news_row["headline"]));
			}
			
 ?> <!-- Add new event-->
				<div id="add_new_events">
					<?=tep_draw_form('frmEvent', FILENAME_EVENTS, tep_get_all_get_params(array('action')) . 'action=new_event', 'post');?>
					<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
						<tr>
							<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
						</tr>
						<tr valign="top">
							<td colspan="2">
								 <table border="0" width="55%" cellspacing="0" cellpadding="0">
										<tr>
											<td>
												<div id="languages_tab">
													<ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
														<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>
													</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
												<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
													<table border="0" width="100%" cellspacing="0" cellpadding="0">
														<tr valign="top">
															<td class="main" valign="top" width="20%"><?=ENTRY_OPTIONS_EVENT_NAME?></td>
															<td style="padding:0px 10px 0px 10px"><?=tep_draw_input_field('event_name[' . $languages[$lang_cnt]['id'] . ']', '', 'size="35" id="event_name"', $lang_cnt == 0 ? true : '');?>
														</tr>
													</table>
												</div>
<? } ?>
												</div><!--languages_tab-->
											</td>
										</tr>
								</table> 
							<script type="text/javascript">
								jQuery(document).ready(function() {
									jQuery.noConflict();
									jQuery("#languages_tab > ul").tabs();
								});
							</script>
							</td>
						</tr>
						<tr align="left" valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_STATUS?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_radio_field('status', '1', isset($_POST['status']) ? true : true);?><?=ENTRY_OPTIONS_ACTIVE?>
  								<?=tep_draw_radio_field('status', '0', isset($_POST['status']) ? true : false);?><?=ENTRY_OPTIONS_INACTIVE?>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_LATEST_NEWS?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_pull_down_menu("news_id", $latest_news_id_array, isset($_POST['news_id']) ? $_POST['news_id'] : '', 'id="news_id" onChange="refreshDynamicSelectOptions(this, \'headline_id\', \''.$_SESSION['languages_id'].'\', false);"')?>
  								<?=tep_draw_pull_down_menu("headline_id", $latest_news_select_array, isset($_POST['headline_id']) ? $_POST['headline_id'] : '', ' id="headline_id" ')?>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_REMARK?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_textarea_field("events_remark", "", 50, 10, isset($_POST['events_remark']) ? $_POST['events_remark'] : '', 'id="events_remark"');?><font color="red">*</font><?=ENTRY_TASK_ERROR?>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_SENDER_EMAIL?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_input_field('events_sender_email', isset($_POST['events_sender_email']) ? $_POST['events_sender_email'] : '', 'size="35" id="events_sender_email"', true);?>
  								<div><?=htmlspecialchars(TEXT_SENDER_EMAIL_DESCRIPTION)?></div>
							</td>
						</tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_EVENT_ADMIN_COPY_EMAIL?></td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_draw_input_field('events_admin_copy_email', isset($_POST['events_admin_copy_email']) ? $_POST['events_admin_copy_email'] : '', 'size="35" id="events_admin_copy_email"', true);?>
  								<div><?=htmlspecialchars(TEXT_ADMIN_COPY_EMAIL_DESCRIPTION)?></div>
							</td>
						</tr>
						<tr><td colspan="2">
						<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
							<tr>
								<td>
								<fieldset>
									<legend><?=FIELDSET_ORDER_TITLE_SETTING?></legend>
									<table border="0" width="100%" cellpadding="0" cellspacing="0" style="font-size:1em;">
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_EVENT_VALIDATION_PERIOD?></td>
										<td style="padding:10px 5px 10px 5px;">
											<table border="0" width="100%" cellpadding="0" cellspacing="0">
												<tr>
													<td class="main" valign="top" width="100" nowrap><?=ENTRY_EVENTS_START_DATE?></td>
				  									<td class="main" valign="top" width="200" nowrap><?=tep_draw_input_field('start_date', isset($_POST['start_date']) ? $_POST['start_date'] : '', 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.frmEvent.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.frmEvent.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?><br/><?=ENTRY_EVENTS_DATE_MARK?></td>
						    						<td class="main" valign="top" width="100" nowrap><?=ENTRY_EVENTS_END_DATE?></td>
						    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', isset($_POST['end_date']) ? $_POST['end_date'] : '', 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.frmEvent.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.frmEvent.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?><br/><?=ENTRY_EVENTS_DATE_MARK?></td>
						    					</tr>
						    				</table>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_EVENT_NOTE?></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_note", isset($_POST['events_note']) ? $_POST['events_note'] : '', 'size="55" id="events_note"');?>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_ERROR_MSG?><div style="font-size:9px;">(<?=ENTRY_EVENT_INPUT_DOESNT_PROVIDED?>)</div></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_err_msg", isset($_POST['events_err_msg']) ? $_POST['events_err_msg'] : '', 'size="55" id="events_err_msg"');?>
										</td>
									</tr>
									<tr valign="top">
										<td width="125" style="padding:10px 5px 10px 5px;"><?=ENTRY_OPTIONS_ERROR_MSG?><div style="font-size:9px;">(<?=ENTRY_EVENT_INVALID_ORDER_RANGE?>)</div></td>
										<td style="padding:10px 5px 10px 5px;">
			  								<?=tep_draw_input_field("events_err_msg2", isset($_POST['events_err_msg2']) ? $_POST['events_err_msg2'] : '', 'size="55" id="events_err_msg2"');?>
										</td>
									</tr>
									</table>
								</fieldset>
								</td>
							</tr>
						</table>
						</td></tr>
						<tr valign="top">
							<td width="125" style="padding:10px 5px 10px 5px;">&nbsp;</td>
							<td style="padding:10px 5px 10px 5px;">
  								<?=tep_submit_button('Insert', 'SubmitFrmEvent', 'onClick="return event_form_checking()"', 'inputbutton');?>
							</td>
						</tr>
					</table>
					</form>
				</div>
<?		break;
}
?>
				</td>
			</tr>
		</table>
<?
	$form_content = ob_get_contents();
	ob_end_clean();
?>
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr><td>[ <a href="<?=tep_href_link(FILENAME_EVENTS, 'action=new_events')?>"><u><?=TEXT_NEW_EVENTS?></u></a> ]</td></tr>
					<tr>
						<td><?=$form_content?></td>
					</tr>
					<tr>
						<td colspan="2">
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
								<tr><td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '3');?></td></tr>
								<tr align="center">
									<td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_ACTION;?></td>
									<td class="reportBoxHeading" width="5%"><?=TABLE_HEADING_STATUS;?></td>
									<td class="reportBoxHeading"><?=TABLE_HEADING_EVENTS_NAME;?></td>
									<td width="25%" class="reportBoxHeading"><?=TABLE_HEADING_EVENTS_SETTING_INFO;?></td>
									<td class="reportBoxHeading"><?=TABLE_HEADING_EVENTS_OPTIONS;?></td>
								</tr>
								<?php
									$row_count = 0;
									$headline = '';
									$url = '';
									
									while ($events_row = tep_db_fetch_array($events_result_sql)) {
										$options_select_sql = "SELECT eo.events_options_id, eo.events_options_status, eod.events_options_title, eo.events_options_input_type, eo.events_options_sort_order
																FROM " . TABLE_EVENTS_OPTIONS . " as eo
																LEFT JOIN " . TABLE_EVENTS_OPTIONS_DESCRIPTION . " as eod
																	ON (eo.events_options_id = eod.events_options_id)
																WHERE eo.events_id = '" . $events_row['events_id'] . "'
																AND eod.language_id = '1'
																ORDER BY eo.events_options_sort_order";
										$options_result_sql = tep_db_query($options_select_sql);
										
										$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
										++$row_count;
										
										if (tep_not_null($events_row['news_id'])) {
											$headline_select_sql = "SELECT headline 
																	FROM " . TABLE_LATEST_NEWS_DESCRIPTION. " 
																	WHERE news_id = '" . $events_row['news_id'] . "'
																		AND language_id = '".$_SESSION['languages_id']."'";
											$headline_result_sql = tep_db_query($headline_select_sql);
											if($headline_row = tep_db_fetch_array($headline_result_sql)) {
												$headline = $headline_row['headline'];
											}
										}
										
										echo '<tr valign="top" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">';
										echo '<td class="subRecordsBoxHeading" align="center" nowrap>
											<a href="'.tep_href_link(FILENAME_EVENTS, 'action=edit_events&events_id='.$events_row['events_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit Events", "", "", 'align="top"').'</a>
											<a href="javascript:void(confirm_delete(\''.$events_row['events_name'].'\', \'events\', \''.tep_href_link(FILENAME_EVENTS, 'action=delete_events&events_id='.$events_row['events_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Events", "", "", 'align="top"').'</a>
											<br/><br/>[ <a href="?action=email_template&events_id='.$events_row['events_id'].'">Email Template</a> ]</td>';
										echo '<td class="subRecordsBoxHeading" align="center">';
										echo '<div style="text-align: center;" id="status-e-'.$events_row['events_id'].'">';
												if ($events_row['events_status'] == '1') {
													echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:change_status(\''.$events_row['events_id'].'\',\'0\',\'update_event_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
												} else {
												    echo '<a href="javascript:change_status(\''.$events_row['events_id'].'\',\'1\',\'update_event_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
												}
										echo '</div>';
										echo '</td>';
										echo '<td class="subRecordsBoxHeading"><a href="'.tep_catalog_href_link('event/index', 'nid='.$events_row['news_id']).'" target="_blank">'.$events_row['events_name'].'</a></td>';
										echo '<td>
												<table class="subRecordsBoxHeading">
													<tr><td><b>'.ENTRY_OPTIONS_LATEST_NEWS.' : '.$headline.'</b></td></tr>
													<tr><td>&nbsp;</td></tr>
													<tr><td><b>'.ENTRY_OPTIONS_EVENT_REMARK.' : </b>'.nl2br($events_row['events_remark']).'</td></tr>
													<tr><td>&nbsp;</td></tr>
													<tr><td><b>'.ENTRY_OPTIONS_EVENT_SENDER_EMAIL.' : </b>'.htmlspecialchars($events_row['events_sender_email']).'</td></tr>
													<tr><td><b>'.ENTRY_OPTIONS_EVENT_ADMIN_COPY_EMAIL.' : </b>'.htmlspecialchars($events_row['events_admin_copy_email']).'</td></tr>
												</table>
											  </td>';
										echo '<td class="subRecordsBoxHeading">
												<table border="0" width="100%" cellpadding="3" cellspacing="0">
													<tr>
					          							<td colspan="10"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
					          						</tr>
													<tr align="center">
														<td class="subRecordsBoxHeading" width="60" nowrap>[<a href="'.tep_href_link(FILENAME_EVENTS, 'action=new_option&events_id='.$events_row['events_id']).'">'.TABLE_HEADING_ADD_NEW.'</a>]</td>
														<td class="subRecordsBoxHeading" width="35" nowrap>'.TABLE_HEADING_STATUS.'</td>
														<td class="subRecordsBoxHeading">'.TABLE_HEADING_OPTIONS_TITLE.'</td>
														<td class="subRecordsBoxHeading">'.TABLE_HEADING_OPTIONS_INPUT_TYPE.'</td>
														<td class="subRecordsBoxHeading">'.TABLE_HEADING_OPTIONS_ID.'</td>
														<td class="subRecordsBoxHeading">'.TABLE_HEADING_OPTIONS_SORT_ORDER.'</td>
													</tr>
													<tr>
					          							<td colspan="10"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
					          						</tr>';
			          						while ($options_row = tep_db_fetch_array($options_result_sql)) {
			          							$input_type_title = get_title_by_id($options_row['events_options_input_type']);
												echo '<tr align="center">
					          							<td class="subRecordsBoxHeading">
					          							<a href="'.tep_href_link(FILENAME_EVENTS, 'action=edit_option&options_id='.$options_row['events_options_id']).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit Option", "", "", 'align="top"').'</a>
														<a href="javascript:void(confirm_delete(\''.$options_row['events_options_title'].'\', \'options\', \''.tep_href_link(FILENAME_EVENTS, 'action=delete_option&options_id='.$options_row['events_options_id']).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Option", "", "", 'align="top"').'</a>
					          							</td>';
					          					echo '<td class="subRecordsBoxHeading" align="center">';
					          					echo '<div style="text-align: center;" id="status-o-'.$options_row['events_options_id'].'">';
												if ($options_row['events_options_status'] == '1') {
													echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:change_status(\''.$options_row['events_options_id'].'\',\'0\',\'update_option_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
												} else {
												    echo '<a href="javascript:change_status(\''.$options_row['events_options_id'].'\',\'1\',\'update_option_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
												}
												echo '</div>';
												echo '</td>';
					          					echo   '<td align="left" class="subRecordsBoxHeading">'.$options_row['events_options_title'].'</td>
					          							<td class="subRecordsBoxHeading">'.$input_type_title.'</td>
					          							<td class="subRecordsBoxHeading">##'.$options_row['events_options_id'].'##</td>
					          							<td align="right" class="subRecordsBoxHeading">'.$options_row['events_options_sort_order'].'</td>
					          						</tr>';
											}
										echo  '</table>
											</td>';
										echo '</tr>';
									}
								?>
							</table>
						</td>
					</tr>
				</table>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_EVENTS, tep_db_num_rows($events_select_sql) > 0 ? "1" : "0", tep_db_num_rows($events_select_sql), tep_db_num_rows($events_select_sql)) : $template_split_object->display_count($events_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_EVENTS)?></td>
						<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $template_split_object->display_links($events_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
					</tr>
				</table>
			</td>
  		</tr>
	</table>
			
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
<?php
function countValuesRecursive($array, $count = 0) {
    // Cycle through the array
    foreach ($array as $value) {
        // Check if the value is an array
        if (is_array($value)) {
            // Cycle through deeper level
            $count = countValuesRecursive($value, $count);
        } else {
            // Check if the value is TRUE
            if ($value) {
                $count++;
                }
            }
        }
    // Return the count
    return $count;
}

function get_title_by_id ($input_type_id) {
	switch ($input_type_id) {
		case '1' :
			$title = 'Text Box';
			break;
			
		case '2' :
			$title = 'Text Area';
			break;
			
		case '3' :
			$title = 'Radio Button';
			break;
			
		case '4' :
			$title = 'Drop Down Menu';
			break;
			
		case '5' :
			$title = 'Information Text';
			break;
			
		case '6' :
			$title = 'File Upload';
			break;
			
		default:
			$title = 'none';
			break;
	}
	return $title;
}
?>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<script>
	<!--
		var page_action = '<?=$action?>';
		var count_lang = '<?=count($languages)?>';
		
		page_load(page_action, count_lang);
		
		function page_load(page_act, count_lang){
			for (var count_lang_loop=0; count_lang_loop<=count_lang; count_lang_loop++) {
				if (page_act == 'new_option'){
						jQuery('#input_type_new_1_'+count_lang_loop).show();
				} else if (page_act == 'edit_option') {
					var options_input_type_default	= '<?=$options_input_type?>';
					if (options_input_type_default == "1") {
						jQuery('#input_type_new_1_'+count_lang_loop).show();
					} else if (options_input_type_default == "2") {
						jQuery('#input_type_new_2_'+count_lang_loop).show();
					} else if (options_input_type_default == "3") {
						jQuery('#input_type_new_3_'+count_lang_loop).show();
					} else if (options_input_type_default == "4") {
						jQuery('#input_type_new_4_'+count_lang_loop).show();
					} else if (options_input_type_default == "5") {
						jQuery('#input_type_new_5_'+count_lang_loop).show();
					} else if (options_input_type_default == "6") {
						jQuery('#input_type_new_6_'+count_lang_loop).show();
					}
				}
			}
		}
		
		function event_form_checking(frm_name ) {
			var event_name = DOMCall('event_name');
			var event_desc = DOMCall('events_remark');
			var events_sender_email = DOMCall('events_sender_email');
			var events_admin_copy_email = DOMCall('events_admin_copy_email');
			
			if (trim_str(event_name.value) == '') {
				alert('Please assign event name.');
				event_name.value = '';
				event_name.focus();
				return false;
			} else if (trim_str(event_desc.value) == '') {
				alert('Please assign event description.');
				event_desc.value = '';
				event_desc.focus();
				return false;
			} else if (trim_str(events_sender_email.value) == '') {
				alert('Please assign email sender.');
				events_sender_email.value = '';
				events_sender_email.focus();
				return false;
			} else if (trim_str(events_admin_copy_email.value) == '') {
				alert('Please assign email recipient.');
				events_admin_copy_email.value = '';
				events_admin_copy_email.focus();
				return false;
			} else {
				document.frm_name.submit();
			}
		}
		
		function option_form_checking(frm_name) {
				var op_type_id = DOMCall('op_type_id');
				var op_sort_order = DOMCall('op_sort_order');
				var count_option1 = count_option2 = count_option3 = 0;
				var error = false;
			
			for (var option_lg=1; option_lg<=count_lang; option_lg++) {
				var op_title = DOMCall('op_title['+option_lg+']');
				var op_size = DOMCall('op_size['+option_lg+']'); //textbox
				var op_max_size_text = DOMCall('op_max_size_text['+option_lg+']');
				var op_row_textarea = DOMCall('op_row_textarea['+option_lg+']'); //textarea
				var op_column_textarea = DOMCall('op_column_textarea['+option_lg+']');
				var op_max_size_textarea = DOMCall('op_max_size_textarea['+option_lg+']');
				
				if (op_type_id.value == '3'){ //radio button
					var option_str = ".option_radio_";
				}
				if (op_type_id.value == '4'){
					var option_str = ".option_dd_";
				}
				
				var option_array = new Array;
			  	jQuery(option_str+option_lg).each(function() {
				var option_array = jQuery(this).val();
					if (option_lg == '1') {
						if (option_array != '') {
							count_option1 = count_option1 + 1;
						}
					}
					if (option_lg == '2') {
						if (option_array != '') {
							count_option2 = count_option2 + 1;
						}
					}
					if (option_lg == '3') {
						if (option_array != '') {
							count_option3 = count_option3 + 1;
						}
					}
				});
				
				var op_text_name = DOMCall('op_text_name['+option_lg+']'); //information text
				var op_max_size_upload = DOMCall('op_max_size_upload['+option_lg+']'); //upload size
				
				if (option_lg == '1') {
					var lang_tab = '(English)';
				}
				if (option_lg == '2') {
					var lang_tab = '(Simplified Chinese)';
				}
				if (option_lg == '3') {
					var lang_tab = '(Traditional Chinese)';
				}
				
				if (op_title.value == '') {
					if (option_lg == '1') {
						alert('Please assign Option Title '+lang_tab+'.');
						error = true;
					}
				} else if (op_sort_order.value == '') {
					alert('Please assign Sort Order.');
					error = true;
				} else if (op_title.value != ''){
					switch(op_type_id.value){
						case '1':
							if (op_size.value == '') {
								alert('Please assign Textbox Size '+lang_tab+'.');
								error = true;
							} else {
								if (op_max_size_text.value == '') {
									alert('Please assign Textbox Max Char '+lang_tab+'.');
									error = true;
								}
							}
						break;
						case '2':
							if (op_row_textarea.value == '') {
								alert('Please assign Textarea Row Size '+lang_tab+'.');
								error = true;
							} else { 
								if (op_column_textarea.value == '') {
									alert('Please assign Textarea Column Size '+lang_tab+'.');
									error = true;
								} else {
									if (op_max_size_textarea.value == '') {
										alert('Please assign Textarea Max Size '+lang_tab+'.');
										error = true;
									}
								}
							}
						break;
						case '3':
						case '4':
							if (option_lg == '1'){
								if (count_option1 < 2){
									alert('Please assign at least two options '+lang_tab+'.');
									error = true;
								}
							}
							if (option_lg == '2'){
								if (count_option2 < 2){
									alert('Please assign at least two options '+lang_tab+'.');
									error = true;
								}
							}
							if (option_lg == '3'){
								if (count_option3 < 2){
									alert('Please assign at least two options '+lang_tab+'.');
									error = true;
								}
							}
						break;
						case '5':
							if (op_text_name.value == '') {
								alert('Please assign Display Text '+lang_tab+'.');
								error = true;
							}
						break;
						case '6':
							if (op_max_size_upload.value == '') {
								alert('Please assign Max Upload Size '+lang_tab+'.');
								error = true;
							}
						break;
					}
				}
			}
			if (error){
				return false;
			} else {
				document.frm_name.submit();
			}
		}
		
		function input_type (type, form_name) {
			if (type == 5 || type==4 || type==3) {
					document.getElementById("op_mandatory_status_1").disabled=true;
					document.getElementById("op_mandatory_status_2").disabled=true;
				for (var lang_id_loop=1; lang_id_loop<=count_lang; lang_id_loop++) {
					document.getElementById("op_err_msg_["+lang_id_loop+"]").disabled=true;
				}
				if (type == 5) {
					for (var lang_id_loop=1; lang_id_loop<=count_lang; lang_id_loop++) {
						document.getElementById("op_note_["+lang_id_loop+"]").disabled=true;
					}
				} else {
					for (var lang_id_loop=1; lang_id_loop<=count_lang; lang_id_loop++) {
						document.getElementById("op_note_["+lang_id_loop+"]").disabled=false;
					}
				}
				
			} else {
				document.getElementById("op_mandatory_status_1").disabled=false;
				document.getElementById("op_mandatory_status_2").disabled=false;
				for (var lang_id_loop=1; lang_id_loop<=count_lang; lang_id_loop++) {
					document.getElementById("op_err_msg_["+lang_id_loop+"]").disabled=false;
					document.getElementById("op_note_["+lang_id_loop+"]").disabled=false;
				}
			}
			
			for (var count_lang_loop=0; count_lang_loop<=count_lang; count_lang_loop++) {
				//upon changes, it will hide all then only display the selected type
				jQuery('#input_type_new_1_'+count_lang_loop).hide();
				jQuery('#input_type_new_2_'+count_lang_loop).hide();
				jQuery('#input_type_new_3_'+count_lang_loop).hide();
				jQuery('#input_type_new_4_'+count_lang_loop).hide();
				jQuery('#input_type_new_5_'+count_lang_loop).hide();
				jQuery('#input_type_new_6_'+count_lang_loop).hide();
				
				if (type == "1") {
					jQuery('#input_type_new_1_'+count_lang_loop).show();
				} else if (type == "2") {
					jQuery('#input_type_new_2_'+count_lang_loop).show();
				} else if (type == "3") {
					jQuery('#input_type_new_3_'+count_lang_loop).show();
				} else if (type == "4") {
					jQuery('#input_type_new_4_'+count_lang_loop).show();
				} else if (type == "5") {
					jQuery('#input_type_new_5_'+count_lang_loop).show();
				} else if (type == "6") {
					jQuery('#input_type_new_6_'+count_lang_loop).show();
				}
			}
			
		}
		
		function addNewOptionRow(tbl_id, tbl_opt_name, lang_id) {
			if (tbl_id == 'input_type_3'){
				var total_row = jQuery('.option_radio_'+lang_id).size();
				var tbody = document.getElementById('opt_radio_'+lang_id);
				var class_name = ('option_radio_'+lang_id);
			}
			
			if (tbl_id == 'input_type_4'){
				var total_row = jQuery('.option_dd_'+lang_id).size();
				var tbody = document.getElementById('opt_dd_'+lang_id);
				var class_name = ('option_dd_'+lang_id);
			}
			
			var row = document.createElement('TR');
			var option_cell = document.createElement('TD');
			var sorting_cell = document.createElement('TD');
			var option_field = document.createElement('INPUT');
			
			option_field.setAttribute('id', 'op_'+tbl_opt_name+'_name['+total_row+']');
			option_field.setAttribute('class', class_name); // to keep track of the total number of rows
			option_field.setAttribute('type','text');
			option_field.setAttribute('name', 'op_'+tbl_opt_name+'_name['+total_row+']['+lang_id+']');
			option_field.setAttribute('size', '50');
			
			option_cell.setAttribute('style', 'padding:5px 10px 5px 10px');
			option_cell.setAttribute('class', 'main');
			option_cell.appendChild(option_field);
			
			var sorting_field = document.createElement('INPUT');
			sorting_field.setAttribute('id', 'op_'+tbl_opt_name+'_input_type_sort_order['+total_row+']');
			sorting_field.setAttribute('type','text');
			sorting_field.setAttribute('name', 'op_'+tbl_opt_name+'_input_type_sort_order['+total_row+']['+lang_id+']');
			sorting_field.setAttribute('size', '7');
			sorting_field.setAttribute('value', '50000');
			sorting_field.onkeyup=function() {
				if (trim_str(this.value)=='' || (trim_str(this.value) != '' && !validateInteger(trim_str(this.value))) )  {
					this.value = '';
				}
			}
			
			sorting_cell.setAttribute('className', 'main');
			sorting_cell.setAttribute('class', 'main');
			sorting_cell.setAttribute('align', 'center');
			sorting_cell.appendChild(sorting_field);
			
			row.appendChild(option_cell);
			row.appendChild(sorting_cell);
			
			tbody.appendChild(row);
		}
	//-->
</script>