<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'game_database_config.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$config_type = (isset($_REQUEST['config_type']) ? $_REQUEST['config_type'] : '');
$id = (isset($_REQUEST['id']) ? $_REQUEST['id'] : '');

$func = new game_database_config(); // Function - Actions

switch($action) {
	case "add_form":
		$header_title = "";
		$form_content = $func->addForm($id, $config_type);
		break;
		
	case "add":
		$func->addEntry($id);
		tep_redirect(tep_href_link(FILENAME_GAME_DATABASE_CONFIG,'selected_box=infolinks&config_type='.urlencode($_POST['config_type'])));
		exit;
		break;
		
	case "delete":
		echo $func->deleteEntry($id, $config_type);
		exit;
		break;
		
	default:
		$header_title = "";
		$form_content = $func->menuListing();
		break;
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/php.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>

