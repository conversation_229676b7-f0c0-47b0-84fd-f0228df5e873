<?
/*
  	$Id: geo_zones.php,v 1.12 2011/06/14 10:59:21 chingyen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$saction = (isset($HTTP_GET_VARS['saction']) ? $_GET['saction'] : '');
$zone_type = (isset($_GET['zone_type']) ? (tep_admin_files_actions(FILENAME_GEO_ZONES, 'GEO_ZONE_TYPE_' . $_GET['zone_type']) ? $_GET['zone_type'] : '') : '');

if (tep_not_null($saction)) {
	$cache_key = TABLE_GEO_ZONES . '/geo_zone_type/array/' . $zone_type;
    $memcache_obj->delete($cache_key, 0);
    
    switch ($saction) {
      	case 'insert_sub':
      	    $zID = tep_db_prepare_input($_GET['zID']);
      	    $zone_country_id = tep_db_prepare_input($_POST['zone_country_id']);
      	    $zone_id = tep_not_null(tep_db_prepare_input($_POST['zone_id'])) ? ',' . implode(',', tep_db_prepare_input($_POST['zone_id'])) . ',' : '';
      	    
      	    //check for the country has been add to other zone or not 
      	     $zone_select_sql = "SELECT b.geo_zone_name
                                 FROM " .TABLE_ZONES_TO_GEO_ZONES. " as a 
                                 INNER JOIN " .TABLE_GEO_ZONES. " as b 
                                 ON a.geo_zone_id = b.geo_zone_id
                                 WHERE b.geo_zone_type = '".tep_db_input($zone_type)."'
      	                         AND a.zone_country_id = '".tep_db_input($zone_country_id)."'";
      	     $zone_result_sql = tep_db_query($zone_select_sql);
   
      	     //Select the zone name
      	     $zone_name_result_sql =  mysql_fetch_array($zone_result_sql);
      	     $can_insert = (tep_db_num_rows($zone_result_sql) > 0) ? false: true;
      	   
      	    if($can_insert){
        	tep_db_query("insert into " . TABLE_ZONES_TO_GEO_ZONES . " (zone_country_id, zone_id, geo_zone_id, date_added) values ('" . (int)$zone_country_id . "'," . (tep_not_null($zone_id) ? "'".$zone_id."'" : 'null') . ", '" . (int)$zID . "', now())");
        	$new_subzone_id = tep_db_insert_id();
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $new_subzone_id));
      	    }
        	
        	else{ 
        	    //select the country name 
        	    $country_name_select_sql = tep_db_query("SELECT countries_name FROM " .TABLE_COUNTRIES. " WHERE countries_id = '".tep_db_input($zone_country_id)."'");
        	    $country_name_result_sql = mysql_fetch_array($country_name_select_sql);
        	    //error
        	    $messageStack->add_session("ERROR: " . $country_name_result_sql[0] . " has been added in " . $zone_name_result_sql[0]."", 'error');
        	    tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage']));
        	   
        	} 
        
        	break;
      	case 'save_sub':
        	$sID = tep_db_prepare_input($_GET['sID']);
        	$zID = tep_db_prepare_input($_GET['zID']);
        	$zone_country_id = tep_db_prepare_input($_POST['zone_country_id']);
        	$zone_id = tep_not_null(tep_db_prepare_input($_POST['zone_id'])) ? ',' . implode(',', tep_db_prepare_input($_POST['zone_id'])) . ',' : '';
			
        	tep_db_query("update " . TABLE_ZONES_TO_GEO_ZONES . " set geo_zone_id = '" . (int)$zID . "', zone_country_id = '" . (int)$zone_country_id . "', zone_id = " . (tep_not_null($zone_id) ? "'".$zone_id."'" : 'null') . ", last_modified = now() where association_id = '" . (int)$sID . "'");
			
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $_GET['sID']));
        	break;
      	case 'deleteconfirm_sub':
        	$sID = tep_db_prepare_input($_GET['sID']);
			
        	tep_db_query("delete from " . TABLE_ZONES_TO_GEO_ZONES . " where association_id = '" . (int)$sID . "'");
			
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage']));
        	break;
	}
}

$action = (isset($_GET['action']) ? $_GET['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case 'insert_zone':
        	$geo_zone_name = tep_db_prepare_input($_POST['geo_zone_name']);
        	$geo_zone_description = tep_db_prepare_input($_POST['geo_zone_description']);
        	$geo_zone_type = tep_db_prepare_input($_POST['geo_zone_type']);

        	tep_db_query("insert into " . TABLE_GEO_ZONES . " (geo_zone_name, geo_zone_description, date_added, geo_zone_type) values ('" . tep_db_input($geo_zone_name) . "', '" . tep_db_input($geo_zone_description) . "', now(), '".tep_db_input($geo_zone_type)."')");
        	$new_zone_id = tep_db_insert_id();
			
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $new_zone_id . '&zone_type=' . $geo_zone_type));
        	break;
      	case 'save_zone':
        	$zID = tep_db_prepare_input($_GET['zID']);
        	$geo_zone_name = tep_db_prepare_input($_POST['geo_zone_name']);
        	$geo_zone_description = tep_db_prepare_input($_POST['geo_zone_description']);
        	$geo_zone_type = tep_db_prepare_input($_POST['geo_zone_type']);
			
        	tep_db_query("update " . TABLE_GEO_ZONES . " set geo_zone_type = '".tep_db_input($geo_zone_type)."', geo_zone_name = '" . tep_db_input($geo_zone_name) . "', geo_zone_description = '" . tep_db_input($geo_zone_description) . "', last_modified = now() where geo_zone_id = '" . (int)$zID . "'");
			
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&zone_type=' . $geo_zone_type));
        	break;
      	case 'deleteconfirm_zone':
        	$zID = tep_db_prepare_input($_GET['zID']);
			
        	tep_db_query("delete from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int)$zID . "'");
        	tep_db_query("delete from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . (int)$zID . "'");
			tep_db_query("DELETE FROM " . TABLE_COUNTRIES_CONTENT . " WHERE geo_zone_id = '" . (int)$zID . "'");
			tep_db_query("DELETE FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " WHERE geo_zone_id = '" . (int)$zID . "'");
        	
        	if (tep_admin_files_actions(FILENAME_GEO_ZONES, 'GEO_ZONE_TYPE_' . $zone_type)) {
	        	if ($zone_type=='1') {
		        	tep_db_query("delete from " . TABLE_DEFINE_MAINPAGE . " where geo_zone_id = '" . (int)$zID . "'");
		        	tep_db_query("delete from " . TABLE_ZONES_INFO . " where geo_zone_id = '" . (int)$zID . "'");
				} else if ($zone_type == '5') {
					tep_db_query("delete from " . TABLE_ZONES_INFO . " where geo_zone_id = '" . (int)$zID . "'");
				}
			}
			
        	tep_redirect(tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zone_type=' .$zone_type));
        	break;
	}
}

// permission on zone type option
$zone_type_options[] = array('id' => '', 'text' => SELECT_ZONES_DEFAULT);

for ($i=1; 6 > $i; $i++) {
	$permission = tep_admin_files_actions(FILENAME_GEO_ZONES, 'GEO_ZONE_TYPE_' . $i);
	
	if ($permission) {
		$zone_type_options[] = array ('id' => $i, 'text' => constant('LIST_ZONE_TYPE_' . $i));
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/general.js"></script>
<?
if (isset($_GET['zID']) && (($saction == 'edit') || ($saction == 'new'))) {
?>
	<script language="javascript"><!--
		function resetZoneSelected(theForm, selection) {
			var multiZoneSelect = theForm.elements['zone_id[]'];
			update_zone(theForm);
  			if (theForm.zone_country_id.selectedIndex >= 0) {
  				var cur_selected = selection;	
				
				for (i=0; i<multiZoneSelect.length; i++) {
					opt_val = ','+ multiZoneSelect.options[i].value +',';
					if (cur_selected.indexOf(opt_val) != -1) {
						multiZoneSelect.options[i].selected = true;
					}
				}
  			} else {
  				//multiZoneSelect.options[0].text = '<?php echo JS_STATE_SELECT; ?>';
  			}
		}
		
		function update_zone(theForm) {
			var multiZoneSelect = theForm.elements['zone_id[]'];
  			var NumState = multiZoneSelect.length;
  			var SelectedCountry = "";
			
  			while (NumState > 0) {
    			NumState--;
    			multiZoneSelect.options[NumState] = null;
  			}
			
  			SelectedCountry = theForm.zone_country_id.options[theForm.zone_country_id.selectedIndex].value;
			
			<?php echo tep_js_zone_list('SelectedCountry', 'theForm', 'zone_id[]', 'multiple'); ?>
			
			if (multiZoneSelect.length < 10) {
				multiZoneSelect.size = multiZoneSelect.length;
			} else {
				multiZoneSelect.size = 10;
			}
		}
		
		function validateSelect(theForm) {
			var multiZoneSelect = theForm.elements['zone_id[]'];
			
			if (multiZoneSelect.options[0].selected == true) {
				for (i=1; i<multiZoneSelect.length; i++) {
					multiZoneSelect.options[i].selected = false;
				}
			}
		}
		//-->
	</script>
<?
}
?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><? echo HEADING_TITLE; if (isset($_GET['zone'])) echo '<br><span class="smallText">' . tep_get_geo_zone_name($_GET['zone']) . '</span>';?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
					<tr>
						<td colspan=100%>
							<?=TEXT_INFO_ZONE_TYPE?>: <?=tep_draw_pull_down_menu('zone_type', $zone_type_options, $zone_type, 'id="zone_type" onChange="document.location.href=\'?zone_type=\'+jQuery(\'#zone_type\').val()"');?>
						</td>
					</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
<?
if ($action == 'list') {
?>
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_COUNTRY?></td>
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_COUNTRY_ZONE?></td>
                								<td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
             	 							</tr>
<?
	$rows = 0;
    $zones_query_raw = "select a.association_id, a.zone_country_id, c.countries_name, a.zone_id, a.geo_zone_id, a.last_modified, a.date_added, z.zone_name from " . TABLE_ZONES_TO_GEO_ZONES . " a left join " . TABLE_COUNTRIES . " c on a.zone_country_id = c.countries_id left join " . TABLE_ZONES . " z on a.zone_id = z.zone_id where a.geo_zone_id = " . $_GET['zID'] . " order by association_id";
    $zones_split = new splitPageResults($_GET['spage'], MAX_DISPLAY_SEARCH_RESULTS, $zones_query_raw, $zones_query_numrows);
    $zones_query = tep_db_query($zones_query_raw);
    while ($zones = tep_db_fetch_array($zones_query)) {
    	$selected_zones_name = array();
    	if (tep_not_null($zones['zone_id'])) {
    		$zone_name_select_sql = "SELECT zone_name FROM " . TABLE_ZONES . " WHERE zone_id IN (".substr($zones['zone_id'], 1, -1).")";
    		$zone_name_result_sql = tep_db_query($zone_name_select_sql);
    		while ($zone_name_row = tep_db_fetch_array($zone_name_result_sql)) {
    			$selected_zones_name[] = $zone_name_row['zone_name'];
    		}
    	}
    	
      	$rows++;
      	
      	if ((!isset($_GET['sID']) || (isset($_GET['sID']) && ($_GET['sID'] == $zones['association_id']))) && !isset($sInfo) && (substr($action, 0, 3) != 'new')) {
        	$sInfo = new objectInfo($zones);
      	}
      	
      	if (isset($sInfo) && is_object($sInfo) && ($zones['association_id'] == $sInfo->association_id)) {
        	echo '                  		<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id . '&saction=edit') . '\'">' . "\n";
      	} else {
        	echo '                  		<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $zones['association_id']) . '\'">' . "\n";
      	}
?>
								                <td class="dataTableContent"><?=($zones['countries_name']) ? $zones['countries_name'] : TEXT_ALL_COUNTRIES?></td>
								                <td class="dataTableContent"><?=(tep_not_null($zones['zone_id'])) ? ($zones['zone_id'] != ',0,' ? implode(', ', $selected_zones_name) : PLEASE_SELECT) : TEXT_NO_ZONES_SELECTION?></td>
								                <td class="dataTableContent" align="right"><? if (isset($sInfo) && is_object($sInfo) && ($zones['association_id'] == $sInfo->association_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $zones['association_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              								</tr>
<?	} ?>
              								<tr>
                								<td colspan="3">
                									<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  										<tr>
                    										<td class="smallText" valign="top"><?php echo $zones_split->display_count($zones_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['spage'], TEXT_DISPLAY_NUMBER_OF_COUNTRIES); ?></td>
                    										<td class="smallText" align="right"><?php echo $zones_split->display_links($zones_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['spage'], 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type, 'spage'); ?></td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
              								<tr>
                								<td align="right" colspan="3"><? if (empty($saction)) echo '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID']) . '&zone_type=' .$zone_type . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a> <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&' . (isset($sInfo) ? 'sID=' . $sInfo->association_id . '&' : '') . 'saction=new') . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
              								</tr>
            							</table>
<?
} else {
?>
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">											
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_TAX_ZONES?></td>
                								<td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
              								</tr>
<?
	$zones_query_raw = "select geo_zone_id, geo_zone_name, geo_zone_description, geo_zone_type, last_modified, date_added from " . TABLE_GEO_ZONES . " WHERE geo_zone_type = '$zone_type' order by geo_zone_name";
    $zones_split = new splitPageResults($_GET['zpage'], MAX_DISPLAY_SEARCH_RESULTS, $zones_query_raw, $zones_query_numrows);
    $zones_query = tep_db_query($zones_query_raw);
    while ($zones = tep_db_fetch_array($zones_query)) {
      	if ((!isset($_GET['zID']) || (isset($_GET['zID']) && ($_GET['zID'] == $zones['geo_zone_id']))) && !isset($zInfo) && (substr($action, 0, 3) != 'new')) {
        	$num_zones_query = tep_db_query("select count(*) as num_zones from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . (int)$zones['geo_zone_id'] . "' group by geo_zone_id");
        	$num_zones = tep_db_fetch_array($num_zones_query);
			
        	if ($num_zones['num_zones'] > 0) {
          		$zones['num_zones'] = $num_zones['num_zones'];
        	} else {
          		$zones['num_zones'] = 0;
        	}
			
        	$zInfo = new objectInfo($zones);
      	}
      	if (isset($zInfo) && is_object($zInfo) && ($zones['geo_zone_id'] == $zInfo->geo_zone_id)) {
        	echo '                  		<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=list&zone_type='.$zone_type) . '\'">' . "\n";
      	} else {
        	echo '                  		<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_GEO_ZONES, 'zone_type=' .$zone_type . '&zpage=' . $_GET['zpage'] . '&zID=' . $zones['geo_zone_id']) . '\'">' . "\n";
      	}
?>
                								<td class="dataTableContent"><?='<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zones['geo_zone_id'] . '&action=list&zone_type='.$zone_type) . '">' . tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER) . '</a>&nbsp;' . $zones['geo_zone_name']?></td>
                								<td class="dataTableContent" align="right"><? if (isset($zInfo) && is_object($zInfo) && ($zones['geo_zone_id'] == $zInfo->geo_zone_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zones['geo_zone_id']) . '&zone_type=' .$zone_type . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              								</tr>
<?	} ?>
              								<tr>
                								<td colspan="2">
                									<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  										<tr>
                    										<td class="smallText"><? echo $zones_split->display_count($zones_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['zpage'], TEXT_DISPLAY_NUMBER_OF_TAX_ZONES); ?></td>
                    										<td class="smallText" align="right"><? echo $zones_split->display_links($zones_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['zpage'], tep_get_all_get_params(array('zpage')), 'zpage'); ?></td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
              								<tr>
                								<td align="right" colspan="2"><? if (!$action) echo '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=new_zone&zone_type=' .$zone_type) . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
              								</tr>
            							</table>
<?
}

echo '								</td>';

$heading = array();
$contents = array();

if ($action == 'list') {
    switch ($saction) {
      	case 'new':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_SUB_ZONE . '</b>');
			
        	
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&' . (isset($_GET['sID']) ? 'sID=' . $_GET['sID'] . '&' : '') . 'saction=insert_sub'));
        	$contents[] = array('text' => TEXT_INFO_NEW_SUB_ZONE_INTRO);
        	$contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY . '<br>' . tep_draw_pull_down_menu('zone_country_id', tep_get_countries(TEXT_ALL_COUNTRIES), '', 'onChange="update_zone(this.form);"'));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_ZONE . '<br>' . tep_draw_pull_down_menu('zone_id[]', tep_prepare_country_zones_pull_down(), '', ' multiple="multiple" size=10 onChange="validateSelect(this.form);" '));
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&' . (isset($_GET['sID']) ? 'sID=' . $_GET['sID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	
        	break;
      	case 'edit':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_SUB_ZONE . '</b>');
			
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id . '&saction=save_sub'));
        	$contents[] = array('text' => TEXT_INFO_EDIT_SUB_ZONE_INTRO);
        	$contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY . '<br>' . tep_draw_pull_down_menu('zone_country_id', tep_get_countries(TEXT_ALL_COUNTRIES), $sInfo->zone_country_id, 'onChange="update_zone(this.form);"'));
        	//$contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_ZONE . '<br>' . tep_draw_pull_down_menu('zone_id', tep_prepare_country_zones_pull_down($sInfo->zone_country_id), $sInfo->zone_id));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_COUNTRY_ZONE . '<br>' . tep_draw_pull_down_menu('zone_id[]', tep_prepare_country_zones_pull_down($sInfo->zone_country_id), $sInfo->zone_id, ' multiple="multiple" size=10 onChange="validateSelect(this.form);" '));
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	case 'delete':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_SUB_ZONE . '</b>');
			
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id . '&saction=deleteconfirm_sub'));
        	$contents[] = array('text' => TEXT_INFO_DELETE_SUB_ZONE_INTRO);
        	$contents[] = array('text' => '<br><b>' . $sInfo->countries_name . '</b>');
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	default:
        	if (isset($sInfo) && is_object($sInfo)) {
          		$heading[] = array('text' => '<b>' . ($sInfo->zone_country_id == 0 ? TEXT_ALL_COUNTRIES : $sInfo->countries_name) . '</b>');
				
          		$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id . '&saction=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=list&zone_type='.$zone_type.'&spage=' . $_GET['spage'] . '&sID=' . $sInfo->association_id . '&saction=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
          		
          		$contents[] = array('text' => '<br>' . TEXT_INFO_DATE_ADDED . ' ' . tep_date_short($sInfo->date_added));
          		if (tep_not_null($sInfo->last_modified)) $contents[] = array('text' => TEXT_INFO_LAST_MODIFIED . ' ' . tep_date_short($sInfo->last_modified));
        	}
        	break;
	}
} else {
	switch ($action) {
      	case 'new_zone':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_ZONE . '</b>');
			
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID'] . '&action=insert_zone'));
        	$contents[] = array('text' => TEXT_INFO_NEW_ZONE_INTRO);
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_NAME . '<br>' . tep_draw_input_field('geo_zone_name'));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_DESCRIPTION . '<br>' . tep_draw_input_field('geo_zone_description'));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_TYPE . '<br>' . tep_draw_pull_down_menu('geo_zone_type', $zone_type_options,$zone_type));
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $_GET['zID']) . '&zone_type=' .$zone_type . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	case 'edit_zone':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_ZONE . '</b>');
			
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=save_zone&zone_type=' .$zone_type));
        	$contents[] = array('text' => TEXT_INFO_EDIT_ZONE_INTRO);
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_NAME . '<br>' . tep_draw_input_field('geo_zone_name', $zInfo->geo_zone_name));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_DESCRIPTION . '<br>' . tep_draw_input_field('geo_zone_description', $zInfo->geo_zone_description));
        	$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_TYPE . '<br>' . tep_draw_pull_down_menu('geo_zone_type', $zone_type_options ,$zInfo->geo_zone_type));
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id) . '&zone_type=' .$zone_type . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	case 'delete_zone':
        	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_ZONE . '</b>');
			
        	$contents = array('form' => tep_draw_form('zones', FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=deleteconfirm_zone' . '&zone_type=' .$zone_type ));
        	$contents[] = array('text' => TEXT_INFO_DELETE_ZONE_INTRO);
        	$contents[] = array('text' => '<br><b>' . $zInfo->geo_zone_name . '</b>');
        	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id.'&zone_type=' .$zone_type ) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	default:
        	if (isset($zInfo) && is_object($zInfo)) {
          		$heading[] = array('text' => '<b>' . $zInfo->geo_zone_name . '</b>');
				
          		$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=edit_zone'. '&zone_type=' .$zone_type ) . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=delete_zone&zone_type=' .$zone_type ) . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>' . ' <a href="' . tep_href_link(FILENAME_GEO_ZONES, 'zpage=' . $_GET['zpage'] . '&zID=' . $zInfo->geo_zone_id . '&action=list&zone_type='.$zone_type) . '">' . tep_image_button('button_details.gif', IMAGE_DETAILS) . '</a>');
          		$contents[] = array('text' => '<br>' . TEXT_INFO_NUMBER_ZONES . ' ' . $zInfo->num_zones);
          		$contents[] = array('text' => '<br>' . TEXT_INFO_DATE_ADDED . ' ' . tep_date_short($zInfo->date_added));
          		if (tep_not_null($zInfo->last_modified)) $contents[] = array('text' => TEXT_INFO_LAST_MODIFIED . ' ' . tep_date_short($zInfo->last_modified));
          		$contents[] = array('text' => '<br>' . TEXT_INFO_ZONE_DESCRIPTION . '<br>' . $zInfo->geo_zone_description);
        	}
        	break;
	}
}

if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
	echo '            				<td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '            				</td>' . "\n";
}
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
<?
if ($action == 'list' && ($saction == 'new' || $saction == 'edit')) {
?>
	<script>
	<!--
		resetZoneSelected (document.zones, '<?=$sInfo->zone_id?>');
	//-->
	</script>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>