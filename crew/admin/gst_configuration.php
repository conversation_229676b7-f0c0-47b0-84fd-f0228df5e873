<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . FILENAME_GST_CONFIGURATION);

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (tep_not_null($_REQUEST['id']) ? $_REQUEST['id'] : '');
$flag = (tep_not_null($_REQUEST['flag']) ? $_REQUEST['flag'] : '');

$func = new gst_configuration($login_id); // Function - Actions

switch($action) {
	case "add_form":
		$header_title = "";
		$form_content = $func->addForm($id);
		break;
		
	case "add":
		$func->addEntry($id);
		tep_redirect(tep_href_link(FILENAME_GST_CONFIGURATION));
		exit;
		break;
		
	case "delete":
		echo $func->deleteEntry($id);
		exit;
		break;
	
	case 'set_orders_provide_invoice_status':
		$data = array ( 'orders_provide_invoice_status' => (int)$flag );
		tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION, $data, 'update', 'orders_tax_id = "'. $id . '"');
		tep_redirect(tep_href_link(FILENAME_GST_CONFIGURATION, 'page=' . (int)$_GET['page']));
		break;	
	
	case 'set_orders_tax_status':
		$data = array ( 'orders_tax_status' => (int)$flag );
		tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION, $data, 'update', 'orders_tax_id = "'. $id . '"');
		tep_redirect(tep_href_link(FILENAME_GST_CONFIGURATION, 'page=' . (int)$_GET['page']));
		break;
		
	default:
		$header_title = '';
		$form_content = $func->menuListing();
		break;
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
        <script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/jquery.tabs.js"></script> 
	<script language="javascript" src="includes/javascript/php.js"></script>
        <script language="javascript" src="includes/javascript/modal_win.js"></script>
        <script>
        jQuery(document).ready(function() {
            jQuery("#languages_tab > ul").tabs();
            jQuery('.languages_tab').css({
                border:'1px solid #C9C9C9'
            });
        });
        </script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>