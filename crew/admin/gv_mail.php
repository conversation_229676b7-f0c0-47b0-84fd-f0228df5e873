<?
/*
  	$Id: gv_mail.php,v 1.8 2007/05/10 09:50:46 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');

$currencies = new currencies();

$action = $HTTP_GET_VARS['action'];

switch ($action) {
	case "preview":
		$mail_sent_to = '';
		$mail_error = false;
		
		if (!tep_not_null($HTTP_POST_VARS['email_to'])) {
			$messageStack->add(ERROR_NO_CUSTOMER_SELECTED, 'error');
			$mail_error = true;
		}
		
		if (!tep_not_null($HTTP_POST_VARS['amount'])) {
			$messageStack->add(ERROR_NO_AMOUNT_SELECTED, 'error');
			$mail_error = true;
		}
		
		if (!$mail_error)	$mail_sent_to = $HTTP_POST_VARS['email_to'];
		
		break;
	case "send_email_to_user":
		if (tep_not_null($HTTP_POST_VARS['email_to']) && !isset($HTTP_POST_VARS['back_x'])) {
			$mail_sent_to = $HTTP_POST_VARS['email_to'];
			$subject = tep_db_prepare_input($HTTP_POST_VARS['subject']);
	    	$from = tep_db_prepare_input(trim($HTTP_POST_VARS['from']));
	    	
			$existing_customer_select_sql = "SELECT customers_firstname, customers_lastname, customers_gender FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_input($mail_sent_to) . "'";
			$existing_customer_result_sql = tep_db_query($existing_customer_select_sql);
			
			$existing_customer = tep_db_num_rows($existing_customer_result_sql) ? true : false;
			$existing_customer_row = tep_db_fetch_array($existing_customer_result_sql);
			
			$email_greeting = $existing_customer ? tep_get_email_greeting($existing_customer_row['customers_firstname'], $existing_customer_row['customers_lastname'], $existing_customer_row['customers_gender']) : tep_get_email_greeting(EMAIL_TEXT_FRIEND_TITLE, EMAIL_TEXT_FRIEND_TITLE);
	    	$receiver_name = $existing_customer ? $existing_customer_row['customers_firstname'] . ' ' . $existing_customer_row['customers_lastname'] : EMAIL_TEXT_FRIEND_TITLE;
	    	
		    if (preg_match("/([^<]*?)(?:<)([^>]*?)(?:>)/i", $from, $regs)) {
				$sender = trim($regs[1]);
				$sender_email = trim($regs[2]);
			} else {
				$sender = '';
				$sender_email = $from;
			}
			
	    	$id1 = create_coupon_code($mail_sent_to);
	      	$message = $email_greeting . tep_db_prepare_input($HTTP_POST_VARS['message']);
	      	$message .= "\n\n" . EMAIL_TEXT_GV_WORTH  . $currencies->format($HTTP_POST_VARS['amount']) . "\n\n";
	      	$message .= sprintf(EMAIL_TEXT_CODE, $id1) . "\n\n";
	      	$message .= sprintf(EMAIL_TEXT_TO_REDEEM, tep_catalog_href_link(), tep_catalog_href_link()) . "\n\n";
	      	$message .= EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
			
			tep_mail($receiver_name, $mail_sent_to, implode(' ', array(EMAIL_SUBJECT_PREFIX, $subject)), $message, $sender, $sender_email);
	      	
	      	// Now create the coupon main and email entry
	      	$coupon_data_array = array(	'coupon_code' => $id1,
										'coupon_type' => 'G',
										'coupon_amount' => $HTTP_POST_VARS['amount'],
										'date_created' => 'now()'
									);
			tep_db_perform(TABLE_COUPONS, $coupon_data_array);
			$insert_id = tep_db_insert_id($insert_query);
			
			$admin_info_select_sql = "	SELECT admin_email_address, admin_firstname, admin_lastname 
										FROM " . TABLE_ADMIN . " 
										WHERE admin_id = '" . $login_id . "'";
			$admin_info_result_sql = tep_db_query($admin_info_select_sql);
			$admin_info_row = tep_db_fetch_array($admin_info_result_sql);
			
			$coupon_track_data_array = array(	'coupon_id' => $insert_id,
												'customer_id_sent' => $login_id,
												'sent_role' => 'admin',
												'sent_email_address' => $admin_info_row['admin_email_address'],
												'sent_firstname' => $admin_info_row['admin_firstname'],
												'sent_lastname' => $admin_info_row['admin_lastname'],
												'emailed_to' => $mail_sent_to,
												'date_sent' => 'now()'
											);
			tep_db_perform(TABLE_COUPON_EMAIL_TRACK, $coupon_track_data_array);
			
			tep_redirect(tep_href_link(FILENAME_GV_MAIL, 'mail_sent_to=' . urlencode($mail_sent_to)));
		}
		
		break;
}

if ($HTTP_GET_VARS['mail_sent_to']) {
	$messageStack->add(sprintf(SUCCESS_EMAIL_SENT_TO, $HTTP_GET_VARS['mail_sent_to']), 'success');
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
</head>
	<script language="Javascript1.2"><!-- // load htmlarea
		// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <head>
    	_editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
        var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
        if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
        if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
        if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
       	<?
       	if (HTML_AREA_WYSIWYG_BASIC_EMAIL == 'Basic') {
       	?>
       		if (win_ie_ver >= 5.5) {
       			document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
       			document.write(' language="Javascript1.2"></scr' + 'ipt>');
			} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       	<? } else{ ?>
       		if (win_ie_ver >= 5.5) {
       			document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       			document.write(' language="Javascript1.2"></scr' + 'ipt>');
          	} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <? } ?>
	// --></script>
    <script language="JavaScript" src="htmlarea/validation.js"></script>
    <script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
    <script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
    <script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
    <script language="JavaScript">
		<!-- Begin
       	function init() {
			//define('customers_email_address', 'string', 'Customer or Newsletter Group');
		}
		//  End -->
	</script>
</head>
<body onLoad="init()" marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	if ( ($HTTP_GET_VARS['action'] == 'preview') && tep_not_null($mail_sent_to) ) {
		$existing_customer_select_sql = "SELECT customers_firstname, customers_lastname FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_input($mail_sent_to) . "'";
		$existing_customer_result_sql = tep_db_query($existing_customer_select_sql);
		$receiver_status_text = sprintf(TEXT_RECEIVER_INFO, $mail_sent_to, (tep_db_num_rows($existing_customer_result_sql) ? TEXT_EXISTING_CUSTOMER : TEXT_XEXISTING_CUSTOMER))
?>
          						<tr>
            						<td>
            							<?=tep_draw_form('mail', FILENAME_GV_MAIL, 'action=send_email_to_user')?>
            							<table border="0" width="100%" cellpadding="0" cellspacing="2">
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_CUSTOMER?></b><br><?=$receiver_status_text?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_FROM?></b><br><?=htmlspecialchars(stripslashes($HTTP_POST_VARS['from']))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_SUBJECT?></b><br><?=htmlspecialchars(stripslashes($HTTP_POST_VARS['subject']))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_AMOUNT?></b><br><?=nl2br(htmlspecialchars(stripslashes($HTTP_POST_VARS['amount'])))?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?=TEXT_MESSAGE . '</b><br>'?><? if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { echo (stripslashes(nl2br($HTTP_POST_VARS['message']))); } else { echo htmlspecialchars(stripslashes(nl2br($HTTP_POST_VARS['message']))); } ?></td>
              								</tr>
              								<tr>
                								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td>
<?
		/* Re-Post all POST'ed variables */
    	reset($HTTP_POST_VARS);
    	while (list($key, $value) = each($HTTP_POST_VARS)) {
      		if (!is_array($HTTP_POST_VARS[$key])) {
        		echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
      		}
    	}
?>
													<table border="0" width="100%" cellpadding="0" cellspacing="2">
                  										<tr>
                    										<td align="right"><?=tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL) . '&nbsp;<a href="' . tep_href_link(FILENAME_GV_MAIL) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
                    									</tr>
                    										<td class="smallText">
                											<?
                												if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
                													echo tep_image_submit('button_back.gif', IMAGE_BACK, 'name="back"');
                												}
                											 	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
                											 		echo TEXT_EMAIL_BUTTON_HTML;
                 												} else { echo TEXT_EMAIL_BUTTON_TEXT; }
                 											?>
                    										</td>
                  										</tr>
                									</table>
                								</td>
             								</tr>
            							</table>
            							</form>
            						</td>
          						</tr>
<? 	} else { ?>
          						<tr>
            						<td>
            							<?=tep_draw_form('mail', FILENAME_GV_MAIL, 'action=preview')?>
            							<table border="0" cellpadding="0" cellspacing="2">
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
<?/*
		$customers = array();
    	$customers[] = array('id' => '', 'text' => TEXT_SELECT_CUSTOMER);
    	$customers[] = array('id' => '***', 'text' => TEXT_ALL_CUSTOMERS);
    	$customers[] = array('id' => '**D', 'text' => TEXT_NEWSLETTER_CUSTOMERS);
    	$mail_query = tep_db_query("select customers_email_address, customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " order by customers_lastname");
    	while($customers_values = tep_db_fetch_array($mail_query)) {
      		$customers[] = array(	'id' => $customers_values['customers_email_address'],
                           			'text' => $customers_values['customers_lastname'] . ', ' . $customers_values['customers_firstname'] . ' (' . $customers_values['customers_email_address'] . ')');
    	}
    */
?>
              								<!--tr>
                								<td class="main"><?=TEXT_CUSTOMER?></td>
                								<td><?=tep_draw_pull_down_menu('customers_email_address', $customers, $HTTP_GET_VARS['customer'])?></td>
              								</tr-->
               								<tr>
                								<td class="main"><?=TEXT_TO?></td>
                								<td><?=tep_draw_input_field('email_to', '', 'id="email_to" size="40"') . '&nbsp;<a href="javascript:;" onClick="searchCustomers(\'customer_info_div\', \'email_to\', \'customers_email_address\')">Customers Lookup</a>'?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
             								<tr>
                								<td class="main"><?=TEXT_FROM?></td>
                								<td><?=tep_draw_input_field('from', EMAIL_FROM, 'size="40"')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td class="main"><?=TEXT_SUBJECT?></td>
                								<td><?=tep_draw_input_field('subject', '', 'size="40"')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td valign="top" class="main"><?=TEXT_AMOUNT?></td>
                								<td><?=tep_draw_input_field('amount')?></td>
              								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                								<td valign="top" class="main"><?=TEXT_MESSAGE?></td>
                								<td><?=tep_draw_textarea_field('message', 'soft', '60', '15')?></td>
<? 		if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { ?>
        	<script language="JavaScript1.2" defer>
				// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email - <body>
           		var config = new Object();  // create new config object
	           	config.width = "<?php echo EMAIL_AREA_WYSIWYG_WIDTH; ?>px";
	           	config.height = "<?php echo EMAIL_AREA_WYSIWYG_HEIGHT; ?>px";
	           	config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
	           	config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
	           	editor_generate('message',config);
<? 		}
				// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <body>
?>
          	</script>
            								</tr>
              								<tr>
                								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              								</tr>
              								<tr>
                 								<td colspan="2" align="right">
<? 		if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') {
			echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL, 'onClick="validate();return returnVal;"');
		} else {
        	echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL);
        }
?>
              									</td>
              								</tr>
            							</table>
            							</form>
            						</td>
            						<td width="50%" align="left" valign="top"><div id="customer_info_div" class="hide"></div></td>
          						</tr>
<?	} ?>
<!-- body_text_eof //-->
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>