<?php
/*
	$Id: gv_sent.php,v 1.10 2007/03/27 07:47:12 weichen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 - 2003 osCommerce

  	Gift Voucher System v1.0
  	Copyright (c) 2001,2002 <PERSON>
  	http://www.phesis.org

  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
        case 'show_report':
        	if (!$_REQUEST['cont']) {
        		$gv_status_join_sql = '';

				if ($_REQUEST["start_date"])
	        		$_SESSION['gv_lists_param']["start_date"] = $_REQUEST["start_date"];
	        	if ($_REQUEST["end_date"])
					$_SESSION['gv_lists_param']["end_date"] = $_REQUEST["end_date"];
				if ($_REQUEST["customer_email"])
					$_SESSION['gv_lists_param']["customer_email"] = tep_db_prepare_input($_REQUEST["customer_email"]);
				if ($_REQUEST["gv_status"])
					$_SESSION['gv_lists_param']["gv_status"] = $_REQUEST["gv_status"];
				/*
				$_SESSION['gv_lists_param']["sort_by"] = $_REQUEST["sort_by"];
				$_SESSION['gv_lists_param']["sort_order"] = $_REQUEST["sort_order"];
				$_SESSION['gv_lists_param']["show_records"] = $_REQUEST["show_records"];
				*/

				$customer_email_str = (isset($_SESSION['gv_lists_param']["customer_email"]) && tep_not_null($_SESSION['gv_lists_param']["customer_email"])) ? " et.emailed_to='" . tep_db_input($_SESSION['gv_lists_param']["customer_email"]) . "'" : "1";

			  	if (tep_not_null($_SESSION['gv_lists_param']["start_date"])) {
					if (strpos($_SESSION['gv_lists_param']["start_date"], ':') !== false) {
						$startDateObj = explode(' ', trim($_SESSION['gv_lists_param']["start_date"]));
						list($yr, $mth, $day) = explode('-', $startDateObj[0]);
						list($hr, $min) = explode(':', $startDateObj[1]);
						$start_date_str = " ( et.date_sent >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION['gv_lists_param']["start_date"]));
						$start_date_str = " ( DATE_FORMAT(et.date_sent, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
					}
				} else {
					$start_date_str = " 1 ";
				}

				if (tep_not_null($_SESSION['gv_lists_param']["end_date"])) {
					if (strpos($_SESSION['gv_lists_param']["end_date"], ':') !== false) {
						$endDateObj = explode(' ', trim($_SESSION['gv_lists_param']["end_date"]));
						list($yr, $mth, $day) = explode('-', $endDateObj[0]);
						list($hr, $min) = explode(':', $endDateObj[1]);
						$end_date_str = " ( et.date_sent <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION['gv_lists_param']["end_date"]));
						$end_date_str = " ( DATE_FORMAT(et.date_sent, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
					}
				} else {
					$end_date_str = " 1 ";
				}

				$voucher_status_str = " 1 ";
				if ( (in_array('1', $_SESSION['gv_lists_param']["gv_status"]) && in_array('2', $_SESSION['gv_lists_param']["gv_status"])) ) {
					$gv_status_join_sql = " left join " . TABLE_COUPON_REDEEM_TRACK . " as crt on c.coupon_id=crt.coupon_id ";
				} else if (in_array('2', $_SESSION['gv_lists_param']["gv_status"])) {
					$gv_status_join_sql = " inner join " . TABLE_COUPON_REDEEM_TRACK . " as crt on c.coupon_id=crt.coupon_id ";
				} else if (in_array('1', $_SESSION['gv_lists_param']["gv_status"])) {
					$gv_status_join_sql = " left join " . TABLE_COUPON_REDEEM_TRACK . " as crt on c.coupon_id=crt.coupon_id ";
					$voucher_status_str = " crt.unique_id IS NULL ";
				}

				$gv_where_str = " where 1 ";
			  	$gv_where_str .= " and $customer_email_str and $start_date_str and $end_date_str and $voucher_status_str ";

			  	$gv_order_by_str = " order by et.date_sent desc";

				$gv_select_sql = "select c.coupon_amount, c.coupon_code, c.coupon_id, et.sent_firstname, et.sent_lastname, et.customer_id_sent, et.emailed_to, et.date_sent, c.coupon_id";
				$gv_select_sql .= ($gv_status_join_sql) ? ", crt.unique_id, crt.customer_id, crt.redeem_date, crt.redeem_ip" : "";
				$gv_select_sql .= " from " . TABLE_COUPONS . " as c inner join " . TABLE_COUPON_EMAIL_TRACK . " as et on c.coupon_id = et.coupon_id " . $gv_status_join_sql . $gv_where_str . $gv_order_by_str;

				$_SESSION['gv_lists_param']['sql'] = $gv_select_sql;
		  	} else {
		  		$gv_select_sql = $_SESSION['gv_lists_param']['sql'];
		  	}

        	break;
        case 'reset_session':
        	unset($_SESSION['gv_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_GV_SENT));

        	break;
        case 'show_delete_preview':
        		$message = tep_db_prepare_input($_REQUEST['message']);
        		$gv_sender_email = tep_db_prepare_input($_REQUEST['gv_sender_email']);
        		$email_subject = tep_db_prepare_input($_REQUEST['email_subject']);
        case 'show_delete_form':
        	if ($coupon_id = (int)$_REQUEST['coupon_id']) {
        		$coupon_code = $_REQUEST['coupon_code'];
        		$gv_cust_email = $_REQUEST['gv_cust_email'];
        		$gv_status_text = ($gv_redeemed = (int)$_REQUEST['gv_redeemed']) ? TEXT_STATUS_REDEEMED : TEXT_STATUS_NOT_REDEEMED;
        	}
        	break;
        case 'do_delete':
        	if ($coupon_id = tep_db_input((int)$_REQUEST['coupon_id'])) {
        		$coupon_code = tep_db_prepare_input($_REQUEST['coupon_code']);
        		$email_subject = tep_db_prepare_input($_REQUEST['email_subject']);

        		$gv_redeemed = (int)$_REQUEST['gv_redeemed'];
        		$gv_cust_email = tep_db_prepare_input($_REQUEST['gv_cust_email']);
        		$gv_message = tep_db_prepare_input($_REQUEST['message']);
        		$gv_sender_email = tep_db_prepare_input($_REQUEST['gv_sender_email']);

        		$redirect_url = tep_href_link(FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'coupon_code', 'gv_cust_email')) . 'action=show_report');

        		$email_pattern = "/([^<]*?)(?:<)([^>]*?)(?:>)/i";

        		//Get tracking data
				$existing_customer_select_sql = "SELECT customers_firstname, customers_lastname, customers_gender FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_input($gv_cust_email) . "'";
				$existing_customer_result_sql = tep_db_query($existing_customer_select_sql);
				if ($existing_customer_row = tep_db_fetch_array($existing_customer_result_sql)) {
					$gv_cust_greeting = tep_get_email_greeting($existing_customer_row['customers_firstname'], $existing_customer_row['customers_lastname'], $existing_customer_row['customers_gender']);
			    	$gv_cust_name = $existing_customer_row['customers_firstname'] . ' ' . $existing_customer_row['customers_lastname'];
				} else {
					$gv_cust_greeting = tep_get_email_greeting(EMAIL_TEXT_FRIEND_TITLE, EMAIL_TEXT_FRIEND_TITLE);
					$gv_cust_name = EMAIL_TEXT_FRIEND_TITLE;
				}
				
        		if ($gv_redeemed) {
        			$gv_amount_select_sql = "	SELECT cpn.coupon_amount, crt.customer_id, cgc.amount as tot_cust_voucher_balance 
        										FROM " . TABLE_COUPONS . " as cpn, " . TABLE_COUPON_REDEEM_TRACK . " as crt, " . TABLE_COUPON_GV_CUSTOMER . " as cgc 
        										WHERE cpn.coupon_id = '".$coupon_id."' 
        											AND crt.coupon_id = cpn.coupon_id 
        											AND crt.customer_id = cgc.customer_id";
        			$gv_amount_result_sql = tep_db_query($gv_amount_select_sql);
        			if ($gv_amount_row = tep_db_fetch_array($gv_amount_result_sql)) {
        				$gv_customer_id = tep_db_input($gv_amount_row['customer_id']);
        				$gv_amount = number_format($gv_amount_row['coupon_amount'], 2);
        				$tot_cust_voucher_balance = number_format($gv_amount_row['tot_cust_voucher_balance'], 2);
        				(float) $difference = number_format(($tot_cust_voucher_balance - $gv_amount), 2);
						
        				if ($difference >= 0) {
        					//Excess could be due to other GV(s) redeemed but not utilised in an order.
        					$gv_deduct_redeemed_amt = array("amount" => tep_db_prepare_input($difference));
	       				} else {
	       					//Amount has been utilised in an order, thus causing customer balance to be reduced to less than current GV amount.
        					$gv_deduct_redeemed_amt = array("amount" => 0);
							
        					//This is a loss so notify admin silently.
        					$admin_subject = EMAIL_SUBJECT_PREFIX . ' ' . EMAIL_SUBJECT_DELETE_REDEEMED_COUPON;
	       					$admin_message = sprintf(EMAIL_TEXT_DELETED_SHOW_LOSS_ADMIN, $coupon_code, $login_email_address, date('Y-m-d h:i:s'), $gv_cust_name, $gv_cust_email, $gv_cust_email, $tot_cust_voucher_balance, $gv_amount, number_format(abs($difference), 2), $gv_message);
							
							$email_to_array = tep_parse_email_string(CANCELLATION_VOUCHER_LOST_EMAIL);
							for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
								tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $admin_subject, $admin_message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							}
        				}
       					tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $gv_deduct_redeemed_amt, 'update', " customer_id = '$gv_customer_id' ");
        			}
        			//Unredeem the voucher.
        			$gv_redeem_track_delete_sql = "delete from " . TABLE_COUPON_REDEEM_TRACK . " where coupon_id = '" . $coupon_id . "'";
					tep_db_query($gv_redeem_track_delete_sql);
        		} else {
	        		//Double check is not redeemed in the interim, else quit on error.
	        		$gv_isredeemed_select_sql = "select unique_id from " . TABLE_COUPON_REDEEM_TRACK . " where coupon_id = '" . $coupon_id . "'";
	        		$gv_isredeemed_result_sql = tep_db_query($gv_isredeemed_select_sql);
	        		if ($gv_isredeemed_row = tep_db_fetch_array($gv_isredeemed_result_sql)) {
		        		$messageStack->add_session(MESSAGE_COUPON_IS_REDEEMED . sprintf(TEXT_INFO_CODE, $coupon_code), 'error');
		        		tep_redirect($redirect_url);
		        		exit;
	        		}
        		}

	        	//Delete gift voucher
        		$gv_delete_coupons_sql = "delete from " . TABLE_COUPONS . " where coupon_id = '" . $coupon_id . "' and coupon_type='G'";
	        	$gv_delete_coupons_result = tep_db_query($gv_delete_coupons_sql);
				if ($gv_delete_coupons_result) {
	        		$messageStack->add_session(MESSAGE_DELETE_CODE_OK . sprintf(TEXT_INFO_CODE, $coupon_code), 'success');
				} else {
	        		$messageStack->add_session(MESSAGE_DELETE_CODE_FAILED . sprintf(TEXT_INFO_CODE, $coupon_code), 'error');
				}
        		//Delete tracking data
	        	$gv_delete_coupon_email_track_sql = "delete from " . TABLE_COUPON_EMAIL_TRACK . " where coupon_id = '" . $coupon_id . "'";
	        	tep_db_query($gv_delete_coupon_email_track_sql);

        		//Email the customer.
			    if (preg_match($email_pattern, $gv_sender_email, $regs)) {
					$sender = trim($regs[1]);
					$sender_email = trim($regs[2]);
				} else {
					$sender = STORE_OWNER;
					$sender_email = $gv_sender_email;
				}
		      	$cust_message = $gv_cust_greeting . EMAIL_TEXT_DELETED_GV_CODE . sprintf(TEXT_INFO_CODE, $coupon_code) . "\n\n" . $gv_message . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
				tep_mail($gv_cust_name, $gv_cust_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $cust_message, $sender, $sender_email);
        		$messageStack->add_session(sprintf(MESSAGE_EMAIL_NOTIFY_DELETE, $gv_cust_email), 'success');
        	}
			tep_redirect($redirect_url);
        	break;

	} //end switch
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
<script language="javascript" src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
</head>
	<script language="Javascript1.2"><!-- // load htmlarea
		// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <head>
    	_editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
        var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
        if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
        if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
        if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
       	<?
       	if (HTML_AREA_WYSIWYG_BASIC_EMAIL == 'Basic') {
       	?>
       		if (win_ie_ver >= 5.5) {
       			document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
       			document.write(' language="Javascript1.2"></scr' + 'ipt>');
			} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       	<? } else{ ?>
       		if (win_ie_ver >= 5.5) {
       			document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       			document.write(' language="Javascript1.2"></scr' + 'ipt>');
          	} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <? } ?>
	// -->
	</script>
    <script language="JavaScript" src="htmlarea/validation.js"></script>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>

<!-- Include div for price calculation formula //-->
<div id="dhtmlTooltip"></div>
<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
<!-- End of include div for price calculation formula //-->

<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="2" cellpadding="0" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
switch ($action) {
	case 'show_report':
?>
      				<tr>
	        			<td width="100%">
	        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr>
              									<td class="reportBoxHeading"><?=TABLE_HEADING_DATE_SENT?></td>
                								<td class="reportBoxHeading"><?=TABLE_HEADING_SENDERS_NAME?></td>
                								<td class="reportBoxHeading"><?=TABLE_HEADING_RECEIVER_EMAIL?></td>
								                <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_VOUCHER_VALUE.'&nbsp;'?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_VOUCHER_CODE?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_VOUCHER_STATUS?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_VOUCHER_ACTION?></td>
              								</tr>
<?
	$gv_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $gv_select_sql, $gv_query_numrows);
	$gv_query = tep_db_query($gv_select_sql);
	$row_count = 0;
	while ($gv_list = tep_db_fetch_array($gv_query)) {
		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd';
		$redeem_text = '';

		if (tep_not_null($gv_list['unique_id'])) {
			$redeem_text = 	TEXT_INFO_DATE_REDEEMED . ' ' . tep_date_short($gv_list['redeem_date'], PREFERRED_DATE_FORMAT) . '<br>' .
							TEXT_INFO_IP_ADDRESS . ' ' . $gv_list['redeem_ip'] . '<br>' .
							TEXT_INFO_CUSTOMERS_ID . ' ' . $gv_list['customer_id'];
			$gv_status_text = '<span onMouseover="ddrivetip(\''.$redeem_text.'\', \'\', 200);" onMouseout="hideddrivetip();">' . TEXT_STATUS_REDEEMED . '</span>';
			$gv_redeemed = 1;
		} else {
			$gv_status_text = TEXT_STATUS_NOT_REDEEMED;
			$gv_redeemed = 0;
		}
		$gv_action_link_text = "<a href=\"" . tep_href_link(FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'voucher_code', 'gv_cust_email', 'gv_redeemed')) . 'action=show_delete_form&coupon_id='.$gv_list['coupon_id'].'&coupon_code='.$gv_list['coupon_code'].'&gv_cust_email='.$gv_list['emailed_to'].'&gv_redeemed='.$gv_redeemed) . "\">" . tep_image(DIR_WS_ICONS."delete.gif", TEXT_TOOLTIP_DELETE, "", "", 'align="top"') . '</a>';
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								                <td class="reportRecords"><?=tep_datetime_short($gv_list['date_sent'], PREFERRED_DATE_TIME_FORMAT)?></td>
								                <td class="reportRecords"><?=$gv_list['sent_firstname'] . ' ' . $gv_list['sent_lastname']?></td>
								                <td class="reportRecords"><?=$gv_list['emailed_to']?></td>
								                <td class="reportRecords" align="right"><?=$currencies->format($gv_list['coupon_amount']).'&nbsp;'?></td>
								                <td class="reportRecords"><?=$gv_list['coupon_code']?></td>
								                <td class="reportRecords"><?=$gv_status_text?>&nbsp;</td>
								                <td class="reportRecords"><?=$gv_action_link_text?>&nbsp;</td>
              								</tr>
<?		$row_count++;
	}
?>
              								<tr>
                								<td colspan="7">
                									<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  										<tr>
                    										<td class="smallText" valign="top"><?=$gv_split->display_count($gv_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_GIFT_VOUCHERS)?></td>
                    										<td class="smallText" align="right"><?=$gv_split->display_links($gv_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont', 'subaction'))."cont=1", 'page')?></td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
            							</table>
            						</td>
<?
/*
	$heading = array();
	$contents = array();

	$heading[] = array('text' => '[' . $gInfo->coupon_id . '] ' . ' ' . $currencies->format($gInfo->coupon_amount));
	$redeem_query = tep_db_query("select * from " . TABLE_COUPON_REDEEM_TRACK . " where coupon_id = '" . $gInfo->coupon_id . "'");
	$redeemed = 'No';
	if (tep_db_num_rows($redeem_query) > 0) $redeemed = 'Yes';
  	$contents[] = array('text' => TEXT_INFO_SENDERS_ID . ' ' . $gInfo->customer_id_sent);
  	$contents[] = array('text' => TEXT_INFO_AMOUNT_SENT . ' ' . $currencies->format($gInfo->coupon_amount));
  	$contents[] = array('text' => TEXT_INFO_DATE_SENT . ' ' . tep_date_short($gInfo->date_sent, PREFERRED_DATE_FORMAT));
  	$contents[] = array('text' => TEXT_INFO_VOUCHER_CODE . ' ' . $gInfo->coupon_code);
  	$contents[] = array('text' => TEXT_INFO_EMAIL_ADDRESS . ' ' . $gInfo->emailed_to);
  	if ($redeemed=='Yes') {
    	$redeem = tep_db_fetch_array($redeem_query);
    	$contents[] = array('text' => '<br>' . TEXT_INFO_DATE_REDEEMED . ' ' . tep_date_short($redeem['redeem_date'], PREFERRED_DATE_FORMAT));
    	$contents[] = array('text' => TEXT_INFO_IP_ADDRESS . ' ' . $redeem['redeem_ip']);
    	$contents[] = array('text' => TEXT_INFO_CUSTOMERS_ID . ' ' . $redeem['customer_id']);
  	} else {
    	$contents[] = array('text' => '<br>' . TEXT_INFO_NOT_REDEEMED);
  	}

  	if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    	echo '            <td width="25%" valign="top">' . "\n";
    	$box = new box;
    	echo $box->infoBox($heading, $contents);
    	echo '            </td>' . "\n";
  	}
  	*/
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
<?php
		break;
	case 'show_delete_form':
?>
      				<tr>
	        			<td width="100%" colspan="3">
	        				<table border="0" width="100%" cellspacing="2" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_DELETE_VOUCHER?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<?php
        					//Start Delete voucher form
        						if (defined('EMAIL_AREA_WYSIWYG_WIDTH'))
        							$col2_width = " width=\"" . EMAIL_AREA_WYSIWYG_WIDTH . "\" ";
        						else
        							$col2_width = " width=\"%\' ";

								echo tep_draw_form('delete_gv', FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'coupon_code', 'gv_cust_email', 'gv_redeemed')) . 'action=show_delete_preview', 'post');
       							echo "<table border=\"0\" width=\"100%\" cellspacing=\"2\" cellpadding=\"0\">" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\" nowrap>" . TEXT_INFO_VOUCHER_CODE . "</td>" .
    								 	"<td $col2_width class=\"main\" align=\"left\" valign=\"top\"><b>$coupon_code</b></td>" .
       								 "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\" nowrap>" . TEXT_INFO_VOUCHER_STATUS . "</td>" .
    								 	"<td $col2_width class=\"main\" align=\"left\" valign=\"top\">$gv_status_text</td>" .
       								 "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .

       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\" >" . TEXT_TO . "</td>" .
    								 	"<td $col2_width class=\"main\" align=\"left\" valign=\"top\">".htmlspecialchars($gv_cust_email)."</td>" .
       								 "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\" >" . TEXT_FROM . "</td>" .
    								 	"<td $col2_width align=\"left\" valign=\"top\">" . tep_draw_input_field('gv_sender_email', EMAIL_FROM, 'size="40"') . "</td>" .
       								 "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\" >" . TEXT_SUBJECT . "</td>" .
    								 	"<td $col2_width align=\"left\" valign=\"top\">" . tep_draw_input_field('email_subject', EMAIL_SUBJECT_DELETE_COUPON, " id=\"email_subject\" size=\"40\" ") . "</td>" .
       								 "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
       								 "<tr>" .
    									"<td class=\"main\" align=\"left\" valign=\"top\">" . TEXT_MESSAGE . "</td>" .
    								 	"<td $col2_width align=\"left\" valign=\"top\">" . tep_draw_textarea_field('message', 'soft', '60', '15') . "</td>";

								if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') {
									echo "\n<script language=\"JavaScript1.2\" defer>" .
										"\n<!--// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email - <body>\n" .
										"	var config = new Object();  // create new config object
											config.width = \"" . EMAIL_AREA_WYSIWYG_WIDTH ."px\";
								           	config.height = \"" . EMAIL_AREA_WYSIWYG_HEIGHT ."px\";
								           	config.bodyStyle = \" background-color: '" . HTML_AREA_WYSIWYG_BG_COLOUR . "'; font-family: '" . HTML_AREA_WYSIWYG_FONT_TYPE . "'; color: '" . HTML_AREA_WYSIWYG_FONT_COLOUR . "'; font-size: '" . HTML_AREA_WYSIWYG_FONT_SIZE . "pt'\";
								           	config.debug = ". HTML_AREA_WYSIWYG_DEBUG .";
								           	editor_generate('message',config);" .
										"\n// --> MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <body>" .
										"\n</script>";
								}
       							echo "</tr>" .
      								 "<tr>
        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
      								 </tr>" .
									 "<tr>" .
    								 "<td align=\"right\" colspan=\"2\">" .
    									tep_draw_hidden_field('coupon_id', $coupon_id, " id=\"coupon_id\" ") .
    									tep_draw_hidden_field('coupon_code', $coupon_code, " id=\"coupon_code\" ") .
    									tep_draw_hidden_field('gv_cust_email', $gv_cust_email, " id=\"gv_cust_email\" ") .
										tep_draw_hidden_field('gv_redeemed', $gv_redeemed);

										if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') {
											echo tep_image_submit('button_preview.gif', IMAGE_PREVIEW, 'onClick="validate();return returnVal;"') . "&nbsp;";
										} else {
											echo tep_image_submit('button_preview.gif', IMAGE_PREVIEW);
										}

								echo "<a href=\"" . tep_href_link(FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'coupon_code', 'gv_cust_email', 'gv_redeemed')) . 'action=show_report') . "\">" . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . "</a>" .
										"</td>" .
       								 "</tr>" .
       								 "</table>";

							?>
        					</form>
        				</td>
   						<td width="40%" align="left" valign="top">&nbsp;</td>
      				</tr>
<?php
		break;

	case 'show_delete_preview':
?>
      				<tr>
	        			<td width="100%" colspan="3">
	        				<table border="0" width="100%" cellspacing="2" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_DELETE_VOUCHER . ' - ' . HEADING_DELETE_VOUCHER_PREVIEW?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
<?php
							echo tep_draw_form('delete_gv_preview', FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'coupon_code', 'gv_cust_email')) . 'action=do_delete', 'post');
	       							echo "<table border=\"0\" width=\"100%\" cellspacing=\"2\" cellpadding=\"0\">" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
	       								 "<tr>" .
	    									"<td class=\"smallText\" align=\"left\" valign=\"top\" ><b>" . TEXT_INFO_VOUCHER_CODE . "</b></td>" .
	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">$coupon_code</td>" .
	       								 "</tr>" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
//	       								 "<tr>" .
//	    									"<td class=\"smallText\" align=\"left\" valign=\"top\" ><b>" . TEXT_INFO_VOUCHER_STATUS . "</b></td>" .
//	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">$gv_status_text</td>" .
//	       								 "</tr>" .
//	      								 "<tr>
//	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
//	      								 </tr>" .
	       								 "<tr>" .
	    									"<td class=\"smallText\" align=\"left\" valign=\"top\" ><b>" . TEXT_TO . "</b></td>" .
	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">".htmlspecialchars(stripslashes($gv_cust_email))."</td>" .
	       								 "</tr>" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
	       								 "<tr>" .
	    									"<td class=\"smallText\" align=\"left\" valign=\"top\" ><b>" . TEXT_FROM . "</b></td>" .
	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">".htmlspecialchars(stripslashes($gv_sender_email))."</td>" .
	       								 "</tr>" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
	       								 "<tr>" .
	    									"<td class=\"smallText\" align=\"left\" valign=\"top\" ><b>" . TEXT_SUBJECT . "</b></td>" .
	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">".htmlspecialchars(stripslashes($email_subject))."</td>" .
	       								 "</tr>" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
	       								 "<tr>" .
	    									"<td class=\"smallText\" align=\"left\" valign=\"top\"><b>" . TEXT_MESSAGE . "</b></td>" .
	    								 	"<td class=\"smallText\" align=\"left\" valign=\"top\">".nl2br($message)."</td>" .
	       								 "</tr>" .
	      								 "<tr>
	        								<td colspan=\"2\">" .tep_draw_separator('pixel_trans.gif', '1', '10') . "</td>
	      								 </tr>" .
	       								 "</table>";
									echo "<table border=\"0\" width=\"100%\" cellpadding=\"0\" cellspacing=\"2\">" .
										 "<tr>" .
										 "	<td class=\"smallText\" align=\"right\">";

										if ($coupon_id) echo "\n" . tep_draw_hidden_field('coupon_id', $coupon_id);
										if ($coupon_code) echo "\n" . tep_draw_hidden_field('coupon_code', $coupon_code);
										if ($gv_sender_email) echo "\n" . tep_draw_hidden_field('gv_sender_email', $gv_sender_email);
										if ($gv_cust_email) echo "\n" . tep_draw_hidden_field('gv_cust_email', $gv_cust_email);
										if ($email_subject) echo "\n" . tep_draw_hidden_field('email_subject', $email_subject);
										if ($message) echo "\n" . tep_draw_hidden_field('message', $message);
										if ($gv_redeemed) echo "\n" . tep_draw_hidden_field('gv_redeemed', $gv_redeemed);

									echo 		tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL, 'onClick="return confirm(\'' . TEXT_CONFIRM_DELETE . '\');"') . '&nbsp;
												<a href="' . tep_href_link(FILENAME_GV_SENT, tep_get_all_get_params(array('action', 'coupon_id', 'coupon_code', 'gv_cust_email')) . 'action=show_report') . '">' .
												tep_image_button('button_cancel.gif', IMAGE_CANCEL) . "</a>" .
										 "	</td>" .
										 "</tr>" .
										 "<tr>" .
										 "	<td class=\"smallText\" align=\"right\">";
										if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
											echo tep_image_submit('button_back.gif', IMAGE_BACK, 'name="back"');
									 		echo TEXT_EMAIL_BUTTON_HTML;
										}
									echo "	</td>" .
										 "</tr>" .
										 "</table>";
							?>
							</form>
        				</td>
   						<td width="50%" align="left" valign="top">&nbsp;</td>
      				</tr>
      				<tr>
      					<td class="smallText" colspan="3"><?=(HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') ? TEXT_EMAIL_BUTTON_TEXT : ""?></td>
      				</tr>
<?php
		break;

	default:
		$status_options_array = array (	'1' => TEXT_STATUS_NOT_REDEEMED,
  									'2' => TEXT_STATUS_REDEEMED
						  		);
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('gv_form_criteria', FILENAME_GV_SENT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', 'onSubmit="return form_checking();"');?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td>
			        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
			          						<script language="javascript"><!--
			  									var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "gv_form_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
			  									calMgr.showHelpAlerts = true;
											//--></script>
											<tr>
			            						<td class="main"><?=ENTRY_START_DATE?></td>
			            						<td class="main" align="left"><script language="javascript">date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION['gv_lists_param']["start_date"]?>';</script></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<script language="javascript"><!--
			  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "gv_form_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
											//--></script>	<tr>
			            						<td class="main"><?=ENTRY_END_DATE?></td>
			            						<td class="main" align="left"><script language="javascript">date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION['gv_lists_param']["end_date"]?>';</script></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main" width="12%"><?=ENTRY_CUSTOMER_EMAIL?></td>
								    			<td class="main"><?=tep_draw_input_field('customer_email', tep_db_prepare_input($_SESSION['gv_lists_param']["customer_email"]), ' id="customer_email" size="40" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_GV_STATUS?></td>
								    			<td class="main">
								    			<?
								    				foreach ($status_options_array as $id => $title) {
								    					echo tep_draw_checkbox_field('gv_status[]', $id, isset($_SESSION['gv_lists_param']) ? (is_array($_SESSION['gv_lists_param']["gv_status"]) && in_array($id, $_SESSION['gv_lists_param']["gv_status"]) ? true : false) : true, '', 'id="gv_status_'.$id.'"') . "&nbsp;" . $title . "&nbsp;";
								    				}
								    			?>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          					</table>
									</td>
								</tr>
						        <tr>
				  					<td>
				  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
				  							<tr>
				  								<td width="20%">&nbsp;</td>
				  								<td align="right">
				  									<?=tep_image_submit('button_report.gif', IMAGE_REPORT)?>&nbsp;<a href="<?=tep_href_link(FILENAME_GV_SENT, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
				  								</td>
				  							</tr>
				  						</table>
				  					</td>
				  				</tr>
	  						</table>
	  						</form>
	  					</td>
	  				</tr>
<?
}// end switch
?>
    			</table>
    		</td>
			<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>