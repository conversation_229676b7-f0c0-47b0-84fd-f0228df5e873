<?php

include_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// Prevent direct access from Web URL
if (!isset($_SERVER['argv'])) {
	exit;
} else {
	$process_info = $_SERVER['argv'][1];
}

define('DIR_FS_HLA_RSS', DIR_FS_HLA . 'hla/rss/');
define('DIR_FS_HLA_PROFILE', DIR_FS_HLA . 'hla/profile/');
$ignored_ref_id_path = DIR_FS_HLA . 'hla/ignored_ref_id.txt';
$ignored_acc_id_path = DIR_FS_HLA . 'hla/ignored_acc_id.txt';
$profilelist_path = DIR_FS_HLA . 'hla/profilelist.txt';

$html_file_path = DIR_FS_HLA_PROFILE . '%s.html';

$default_language_id = 1;
$default_currency = 'USD';
$default_product_status = '-1'; // -1 = Inactive, 0 = Sold, 1 = Active
$default_product_display = '0'; // 0=hide, 1=show
$products_hla_id_arr = array();

if (tep_not_null((string)$process_info)) {
	$process_info_arr = split_dep(':~:', $process_info);
	
	reset_file($ignored_ref_id_path);
	reset_file($ignored_acc_id_path);
	
	foreach ($process_info_arr as $process_info_val) {
		$xml_file_name = basename($process_info_val);
		list($file_name, $file_type) = explode('.', $xml_file_name);
		list($seller_id, $product_id, $count_no) = split_dep('_', $file_name);
		
		if (tep_not_null((string)$seller_id) && tep_not_null((string)$product_id)) {
			if (file_exists($process_info_val)) {
				$get_supplier_info_sql = "	SELECT supplier_code 
											FROM " . TABLE_PRODUCTS_SUPPLIER . " 
											WHERE supplier_id = '".tep_db_input($seller_id)."' 
											AND supplier_status = '1'";
				$get_supplier_info_result = tep_db_query($get_supplier_info_sql);
				
				if ($get_supplier_info_row = tep_db_fetch_array($get_supplier_info_result)) {
					$supplier_code = $get_supplier_info_row['supplier_code'];
					include_once(DIR_WS_MODULES . 'hla/' . $supplier_code . '/' . $supplier_code . '.php');
					
					$profiler = 0;
					$parse_obj = new $supplier_code($seller_id, $product_id, $default_language_id, $default_currency, $default_product_status, $default_product_display, $html_file_path, $profilelist_path, $ignored_ref_id_path, $ignored_acc_id_path, $profiler);

					$parse_obj->parsing_xml($process_info_val);
					
					foreach ($parse_obj->products_hla_id_arr as $products_hla_id) {
						$products_hla_id_arr[$seller_id][] = $products_hla_id;
					}
				} else {
					// supplier status inactive or dont have this supplier record.
				}
			} else {
				// file not exist
			}
		}
	}
	
	if (count($products_hla_id_arr) > 0) {
		// Clean Profile
		clean_profile($products_hla_id_arr, $html_file_path, '');
		// Activate Product
		$ignored_acc_id_arr = get_ignore_acc_id ($ignored_ref_id_path, $ignored_acc_id_path);
		activate_product($products_hla_id_arr, $ignored_acc_id_arr);
	}
}

function get_ignore_acc_id ($ignored_ref_id_path, $ignored_acc_id_path) {
	$acc_id_arr = array();
	$ignored_acc_id_arr = array();
	
	if (tep_not_null((string)$ignored_ref_id_path)) {
		$ignored_ref_id_arr = array();
		$ignored_ref_id_arr = ignored_list($ignored_ref_id_path);
		
		if (count($ignored_ref_id_arr) > 0) {
			foreach ($ignored_ref_id_arr as $ref_id) {
				if (tep_not_null((string)$ref_id)) {
					$get_acc_id_select_sql = "	SELECT products_hla_id 
												FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " 
												WHERE products_ref_id = '".tep_db_input($ref_id)."'";
					$get_acc_id_result_sql = tep_db_query($get_acc_id_select_sql);
					if ($get_acc_id_row = tep_db_fetch_array($get_acc_id_result_sql)) {
						if (!in_array($get_acc_id_row['products_hla_id'], $acc_id_arr)) {
							$acc_id_arr[] = $get_acc_id_row['products_hla_id'];
						}
					}
				}
			}
		}
	}
	
	if (tep_not_null((string)$ignored_acc_id_path)) {
		$ignored_acc_id_arr = array();
		$ignored_acc_id_arr = ignored_list($ignored_acc_id_path);
		
		if (count($ignored_acc_id_arr) > 0) {
			foreach ($ignored_acc_id_arr as $acc_id) {
				if (tep_not_null((string)$acc_id)) {
					$get_acc_id_select_sql = "	SELECT products_hla_id 
												FROM " . TABLE_PRODUCTS_HLA . " 
												WHERE products_account_id  = '".tep_db_input($acc_id)."'";
					$get_acc_id_result_sql = tep_db_query($get_acc_id_select_sql);
					if ($get_acc_id_row = tep_db_fetch_array($get_acc_id_result_sql)) {
						if (!in_array($get_acc_id_row['products_hla_id'], $acc_id_arr)) {
							$acc_id_arr[] = $get_acc_id_row['products_hla_id'];
						}
					}
				}
			}
		}
	}
	
	return $acc_id_arr;
}

function activate_product ($products_hla_id_arr, $ignored_acc_id_arr) {
	if (count($products_hla_id_arr) > 0) {
		if (is_array($ignored_acc_id_arr)) {
			if (count($ignored_acc_id_arr) > 0) {
				$ignored_acc_id_arr = array_filter($ignored_acc_id_arr);
			}
		} else {
			$ignored_acc_id_arr = array();
		}
			
		foreach ($products_hla_id_arr as $seller_id => $products_hla_id_info) {
			foreach ($products_hla_id_info as $products_hla_id) {
				if (!in_array($products_hla_id, $ignored_acc_id_arr)) {
					$update_status_sql_data_array = array('products_status' => '1', 
														  'products_display' => '1');
					tep_db_perform(TABLE_PRODUCTS_HLA, $update_status_sql_data_array, 'update', "products_hla_id = '" . $products_hla_id . "'");	
				}
			}
		}
	}
}

function clean_old_character($products_hla_id, $ref_id_arr) {
	if (count($ref_id_arr) > 0) {
		$not_exist_in_database_sql = "	SELECT products_hla_characters_id 
										FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " 
										WHERE products_ref_id NOT IN ('" . implode("', '", $ref_id_arr) . "') 
										AND products_hla_id = '".tep_db_input($products_hla_id)."'";
		$not_exist_in_database_result = tep_db_query($not_exist_in_database_sql);
		while ($not_exist_in_database_row = tep_db_fetch_array($not_exist_in_database_result)) {
			tep_db_query("DELETE FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " WHERE products_hla_characters_id = '".tep_db_input($not_exist_in_database_row['products_hla_characters_id'])."'");
		}
	}
}

function clean_profile($products_hla_id_arr, $html_file_path, $language_id) {
	if (count($products_hla_id_arr) > 0) {
		foreach ($products_hla_id_arr as $seller_id => $products_hla_id_info) {
			if (count($products_hla_id_info) > 0) {
				$not_exist_in_database_sql = "	SELECT hla.products_hla_id 
												FROM " . TABLE_PRODUCTS_HLA . " AS hla 
												WHERE hla.products_hla_id NOT IN ('" . implode("', '", $products_hla_id_info) . "') 
													AND hla.available_quantity = '1' 
													AND hla.actual_quantity = '1' 
													AND hla.seller_id = '".tep_db_input($seller_id)."'";
				$not_exist_in_database_result = tep_db_query($not_exist_in_database_sql);
				while ($not_exist_in_database_row = tep_db_fetch_array($not_exist_in_database_result)) {
					$products_hla_id = $not_exist_in_database_row['products_hla_id'];
					$profile_type = 'direct_link';
					
					remove_old_data($products_hla_id, $html_file_path, true, $language_id, $profile_type);
					tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA." WHERE products_hla_id = '".tep_db_input($products_hla_id)."'");
				}
			}
		}
	}
}

function database_process ($type, $action, &$char_info_arr, &$products_hla_id_arr) {
	global $seller_id;
	
	$no_error = true;
	
	if (!tep_not_null((double)$char_info_arr['products_price'])) {
		$no_error = false;
	}
	
	if (!tep_not_null((string)$char_info_arr['ref_id'])) {
		$no_error = false;
	}
	
	if (!tep_not_null((string)$char_info_arr['character_name']) && !tep_not_null((string)$char_info_arr['description'])) {
		$no_error = false;
	}
	
	$profile_url = isset($char_info_arr['profile']) && $char_info_arr['profile'] == 'direct_link' ? $char_info_arr['profile_link'] : '';
	
	if ($no_error) {
		$mapping['bloodelf'] = 'Blood Elf';
		$mapping['nightelf'] = 'Night Elf';
		$mapping['deathknight'] = 'Death Knight';
		$mapping['beastmastery'] = 'Beast Mastery';
		$mapping['feralcombat'] = 'Feral Combat';
		
		switch ($type) {
			case 'single':
				switch ($action) {
					case 'update':
						$hla_sql_data_array = array('products_base_currency' => $char_info_arr['default_currency'], 
													'products_price' => $char_info_arr['products_price'], 
													'products_original_price' => $char_info_arr['products_original_price']);
						tep_db_perform(TABLE_PRODUCTS_HLA, $hla_sql_data_array, 'update', "products_hla_id = '" . $char_info_arr['products_hla_id'] . "'");
						
						$hla_url_data_array = array('products_hla_characters_url' => $profile_url);
						tep_db_perform(TABLE_PRODUCTS_HLA_CHARACTERS, $hla_url_data_array, 'update', "products_hla_characters_id = '" . (int)$char_info_arr['characters_id'] . "'");
						
						tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_ATTRIBUTES." WHERE products_hla_characters_id = '".(int)$char_info_arr['characters_id']."' AND language_id = '".(int)$char_info_arr['language_id']."'");
					break;
					case 'insert':
						$hla_sql_data_array = array('seller_id' => $seller_id,
													'products_id' => $char_info_arr['product_id'],
													'products_type' => '1',
													'available_quantity' => '1',
													'actual_quantity' => '1',
													'products_price' => $char_info_arr['products_price'],
													'products_original_price' => $char_info_arr['products_original_price'],
													'products_base_currency' => $char_info_arr['default_currency'],
													'products_status' => $char_info_arr['products_status'],
													'products_display' => $char_info_arr['products_display'],
													'created_date' => 'now()');
						tep_db_perform(TABLE_PRODUCTS_HLA, $hla_sql_data_array);
						$products_hla_id = tep_db_insert_id();
						$products_hla_id_arr[] = $products_hla_id;
						
						$hla_characters_sql_data_array = array('products_hla_id' => $products_hla_id,
															   'products_ref_id' => $char_info_arr['ref_id'],
															   'products_hla_characters_url' => $profile_url);
						tep_db_perform(TABLE_PRODUCTS_HLA_CHARACTERS, $hla_characters_sql_data_array);
						
						$char_info_arr['characters_id'] = tep_db_insert_id();
					break;
				}
			break;
			case 'multiple':
				switch ($action) {
					case 'update':
						$hla_url_data_array = array('products_hla_characters_url' => $profile_url);
						tep_db_perform(TABLE_PRODUCTS_HLA_CHARACTERS, $hla_url_data_array, 'update', "products_hla_characters_id = '" . (int)$char_info_arr['characters_id'] . "'");	
					break;
					case 'insert':
						$get_char_id_select_sql = "	SELECT products_hla_characters_id 
													FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " 
													WHERE products_ref_id = '".tep_db_input($char_info_arr['ref_id'])."' 
													AND products_hla_id = '".tep_db_input($char_info_arr['products_hla_id'])."'";
						$get_char_id_result_sql = tep_db_query($get_char_id_select_sql);
						if ($get_char_id_row = tep_db_fetch_array($get_char_id_result_sql)) {
							$char_info_arr['characters_id'] = $get_char_id_row['products_hla_characters_id'];
							
							$hla_url_data_array = array('products_hla_characters_url' => $profile_url);
							tep_db_perform(TABLE_PRODUCTS_HLA_CHARACTERS, $hla_url_data_array, 'update', "products_hla_characters_id = '" . (int)$char_info_arr['characters_id'] . "'");	
						} else {
							$hla_characters_sql_data_array = array('products_hla_id' => $char_info_arr['products_hla_id'],
																   'products_ref_id' => $char_info_arr['ref_id'],
																   'products_hla_characters_url' => $profile_url);
							tep_db_perform(TABLE_PRODUCTS_HLA_CHARACTERS, $hla_characters_sql_data_array);
							
							$char_info_arr['characters_id'] = tep_db_insert_id();
						}
						
					break;
				}
			break;
		}
		
		$insert_update_desc_sql = "	INSERT INTO " . TABLE_PRODUCTS_HLA_DESCRIPTION . " 
									SET products_hla_characters_id = '".tep_db_input($char_info_arr['characters_id'])."', 
									products_hla_characters_name = '".tep_db_input($char_info_arr['character_name'])."', 
									products_hla_characters_description = '".tep_db_input($char_info_arr['description'])."', 
									language_id = '".(int)$char_info_arr['language_id']."' 
										ON DUPLICATE KEY 
									UPDATE products_hla_characters_name = '".tep_db_input($char_info_arr['character_name'])."', 
									products_hla_characters_description = '".tep_db_input($char_info_arr['description'])."'";
		tep_db_query($insert_update_desc_sql);
		
		$country_info = get_countries_info();
		
		foreach ($char_info_arr['attr_arr'] as $attribute => $attribute_value) {
			if (tep_not_null((string)$attribute_value)) {
				$attribute_value = ($attribute == 'country' && isset($country_info[$attribute_value])) ? $country_info[$attribute_value] : $attribute_value;
				$mapping_key = strtolower($attribute_value);
				$attribute_value = isset($mapping[$mapping_key]) ? $mapping[$mapping_key] : $attribute_value;
				
				$insert_update_attr_sql = "	INSERT INTO " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " 
											SET products_hla_characters_id = '".tep_db_input($char_info_arr['characters_id'])."', 
											products_hla_attributes_type = '".tep_db_input($attribute)."', 
											products_hla_value = '".tep_db_input($attribute_value)."', 
											language_id = '".(int)$char_info_arr['language_id']."' 
												ON DUPLICATE KEY 
											UPDATE products_hla_attributes_type = '".tep_db_input($attribute)."', 
											products_hla_value = '".tep_db_input($attribute_value)."'";
				tep_db_query($insert_update_attr_sql);
			}
		}
	}
	
	return $no_error;
}

function get_countries_info() {
	$country_info = array();
	
	$country_info_select_sql = "SELECT countries_name, countries_iso_code_2 FROM " . TABLE_COUNTRIES;
	$country_info_result_sql = tep_db_query($country_info_select_sql);
	
	while ($country_info_row = tep_db_fetch_array($country_info_result_sql)) {
		$country_info[$country_info_row['countries_iso_code_2']] = $country_info_row['countries_name'];
	}
	
	return $country_info;
}

function filter_character_name (&$character_name) {
	if(strpos($character_name, 'REF#') !== FALSE) {
		preg_match('/(.*?)(REF#)/', $character_name, $matches);
		$character_name = tep_not_null((string)$matches[1]) ? $matches[1] : $character_name;
		$character_name = trim(str_replace('-', '', $character_name));
	}
}

function remove_old_data ($products_hla_id, $remove_profile_path = '', $remove_char = true, $language_id=1, $profile_type = '') {
	$get_character_id_sql = "	SELECT products_hla_characters_id 
								FROM ".TABLE_PRODUCTS_HLA_CHARACTERS." 
								WHERE products_hla_id = '".tep_db_input($products_hla_id)."'";
	$get_character_id_result = tep_db_query($get_character_id_sql);								
	while ($get_character_id_row = tep_db_fetch_array($get_character_id_result)) {
		$characters_id = $get_character_id_row['products_hla_characters_id'];
		
		if (tep_not_null((string)$language_id)) {
			tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_ATTRIBUTES." WHERE products_hla_characters_id = '".(int)$characters_id."' AND language_id = '".(int)$language_id."'");
			tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_DESCRIPTION." WHERE products_hla_characters_id = '".(int)$characters_id."' AND language_id = '".(int)$language_id."'");	
		} else {
			tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_ATTRIBUTES." WHERE products_hla_characters_id = '".(int)$characters_id."'");
			tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_DESCRIPTION." WHERE products_hla_characters_id = '".(int)$characters_id."'");
		}
		
		if (tep_not_null((string)$remove_profile_path) && ($profile_type == 'download')) {
			$html_file_path = sprintf($remove_profile_path, $characters_id);
			if (file_exists($html_file_path)) {
				unlink($html_file_path); // Remove Profile ref_id.html
			}
		}
	}
	
	if ($remove_char) {
		tep_db_query("DELETE FROM ".TABLE_PRODUCTS_HLA_CHARACTERS." WHERE products_hla_id = '".tep_db_input($products_hla_id)."'");	
	}
}

function store_attributes ($ts_object, $cnt = 0) {
	$attr_arr = array(  'race' => (string)$ts_object->race[$cnt], // e.g Human
						'class' => (string)$ts_object->class[$cnt], // e.g Warlock
						'side' => (string)$ts_object->side[$cnt], // e.g Alliance
						'level' => (string)$ts_object->level[$cnt], // e.g Level
						'talent' => (string)$ts_object->talent[$cnt], // e.g Affliction
						'gender' => (string)$ts_object->gender[$cnt], // e.g Male
						'server' => (string)$ts_object->server[$cnt], // e.g Server
						'country' => (string)$ts_object->country
						);
	return $attr_arr;							
}

function reset_file ($file_path) {
	$fp = fopen($file_path, 'w');
	fwrite($fp, "");
	fclose($fp);
}

function write_in_file ($file_path, $text_content, $mode = 'a+') {
	$fp = fopen($file_path, $mode);
	fwrite($fp, $text_content."\r\n");
	fclose($fp);
}

function ignored_list ($ignored_path) {
	$ignored_id_arr = array();
	if (file_exists($ignored_path) && (filesize($ignored_path) > 0)) {
		$handle = fopen($ignored_path, "r");
		$ignored_id_arr = explode("\r\n", fread($handle, filesize($ignored_path)));
		fclose($handle);
	}
	
	return $ignored_id_arr;
}
?>