<?php
require('includes/application_top.php');

$action = $_REQUEST['action'];
$template_id = (int)$_REQUEST['tID'];
$hla_template_status = (int)$_GET['flag'];

$products_supplier_sql_data = array();

if (tep_not_null($action)) {
	switch ($action) {
		case 'set_products_template_status':
			$options_update_sql = " UPDATE " . TABLE_HLA_TEMPLATE . " 
									SET hla_template_status = '" . tep_db_input($_GET['flag']) . "'
				    				WHERE hla_template_id = '" . $template_id . "'";
			tep_db_query($options_update_sql);
			
			$options_select_sql = " SELECT hla_template_id, hla_template_status 
									FROM " . TABLE_HLA_TEMPLATE . " 
									WHERE hla_template_id = '" . $template_id . "'";
			$options_result_sql = tep_db_query($options_select_sql);
	 		$options_row = tep_db_fetch_array($options_result_sql);
			if ($options_row['hla_template_status'] == '1') {
				$content_text = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:change_status(\''.$template_id.'\',\'0\',\'set_products_template_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;';
			} else {
			    $content_text = '<a href="javascript:change_status(\''.$template_id.'\',\'1\',\'set_products_template_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10).'&nbsp;&nbsp;';
			}
			echo $content_text;
				
			die();
			break;
			
		case 'delete_products_hla_template':
			if (tep_not_null($template_id)) {
				$get_template_name_select_sql = " 	SELECT hla_template_name 
													FROM " . TABLE_HLA_TEMPLATE . " 
													WHERE hla_template_id = '" . tep_db_input($template_id) . "'";
				$get_template_name_result_sql = tep_db_query($get_template_name_select_sql);
		 		$get_template_name_row = tep_db_fetch_array($get_template_name_result_sql);
		 		
				// Remove template
				$delete_hla_template_sql = "DELETE FROM " . TABLE_HLA_TEMPLATE . " WHERE hla_template_id = '" . tep_db_input($template_id) . "'";
				tep_db_query($delete_hla_template_sql);
				
				$messageStack->add_session($get_template_name_row['hla_template_name'].' is Deleted', 'success');
				tep_redirect(tep_href_link(FILENAME_HLA_TEMPLATE));
			}
			break;
			
		case 'update_hla_template':
			$template_id = $_POST['template_id'];
			
			if (tep_not_null($template_id)) {
				$template_form_template = '';
			 
				if (tep_not_null($_POST['template_form_template'])) {
					eval('$template_form_template = '.stripslashes($_POST['template_form_template']).';');
					$template_form_template = tep_array_serialize($template_form_template);
				}
				
				$hla_template_sql_data = array ('hla_template_name' => tep_db_prepare_input($_POST['template_name']), 
												'hla_template_data' => tep_db_prepare_input($template_form_template), 
												'hla_template_sort_order' => tep_db_prepare_input($_POST['template_sort_order']), 
												'hla_template_status' => tep_db_prepare_input($_POST['template_status'])
												);
													
				tep_db_perform(TABLE_HLA_TEMPLATE, $hla_template_sql_data, 'update', 'hla_template_id = "' . tep_db_input($template_id) . '"');
			}
				
			tep_redirect(tep_href_link(FILENAME_HLA_TEMPLATE));
			break;
		case 'insert_hla_template':
			if ( ! tep_not_null($_POST['template_name'])) {
				$error_msg[] = 'Template Name cannot be left blank.';
			}
			
			if ( ! tep_not_null($_POST['template_status'])) {
				$error_msg[] = 'Status cannot be left blank.';
			}
			
			if ( ! tep_not_null($_POST['template_sort_order'])) {
				$error_msg[] = 'Sort Order cannot be left blank.';
			}
			
			if (count($error_msg) > 0) {
				$all_error_value = '<ul>';
				foreach ($error_msg as $error_value) {
					$all_error_value .= '<li>'.$error_value.'</li>';
				}
				$all_error_value .= '</ul>';
				$messageStack->add_session($all_error_value, 'error');
				
				$_SESSION['submitted_info'] = $_POST;
				tep_redirect(tep_href_link(FILENAME_HLA_TEMPLATE, tep_get_all_get_params(array('action'))));
			} else {
				$hla_template_sql_data = array ('hla_template_name' => tep_db_prepare_input($_POST['template_name']), 
												'hla_template_data' => tep_db_prepare_input($_POST['template_form_template']), 
												'hla_template_sort_order' => tep_db_prepare_input($_POST['template_sort_order']), 
												'hla_template_status' => tep_db_prepare_input($_POST['template_status'])
												);
				tep_db_perform(TABLE_HLA_TEMPLATE, $hla_template_sql_data);
			}
			
			tep_redirect(tep_href_link(FILENAME_HLA_TEMPLATE));
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
		<title><?=TITLE?></title>
		<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
		
		<script language="javascript" src="includes/javascript/jquery.js"></script>
		<script language="javascript" src="includes/general.js"></script>
		<script type="text/javascript"><!--
			function form_submit() {
				if (document.getElementById('template_name').value == "") {
					alert("Template Name is required!");
					document.getElementById('template_name').focus();
					return false;
				}
			}
		--></script>
		<script language="javascript" src="includes/javascript/modal_win.js"></script>
	</head>
	
	<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" >
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<!-- header_eof //-->
	
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
				</table>
			</td>
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_TITLE;?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?php
$template_name = $template_data = $template_sort_order = '';
if (tep_not_null($action)) {
	$template_status_active = false; 
	$template_status_inactive = true;
	
	if ($action == 'edit_template') {
		$hla_template_status_select_sql = "	SELECT hla_template_name, hla_template_data, hla_template_status, hla_template_sort_order 
											FROM " . TABLE_HLA_TEMPLATE . " 
											WHERE hla_template_id = '".tep_db_input($template_id)."'";
		$hla_template_status_result_sql = tep_db_query($hla_template_status_select_sql);
		if ($hla_template_status_row = tep_db_fetch_array($hla_template_status_result_sql)) {
			$template_name = $hla_template_status_row['hla_template_name'];
			
			if (tep_not_null($hla_template_status_row['hla_template_data'])) {
				$template_data = var_export(tep_array_unserialize($hla_template_status_row['hla_template_data']), true);
			}
			
			$template_sort_order = $hla_template_status_row['hla_template_sort_order'];
			if ($hla_template_status_row['hla_template_status'] == '1') {
				$template_status_active = true;
				$template_status_inactive = false;
			}
		}
		$action_param = 'update_hla_template';
		$submit_button = tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="next" onClick="return form_submit();"', 'inputButton');
	} else if($action == 'new_template') {
		$action_param = 'insert_hla_template';
		$submit_button = tep_submit_button(IMAGE_INSERT, IMAGE_INSERT, 'name="next" onClick="return form_submit();"', 'inputButton');
	}
	
	if (isset($_SESSION['submitted_info']['template_status'])) {
		if ($_SESSION['submitted_info']['template_status'] == '1') {
			$template_status_active = true; 
			$template_status_inactive = false;
		} else {
			$template_status_active = false; 
			$template_status_inactive = true;
		}
	}
	
	$template_name = isset($_SESSION['submitted_info']['template_name']) ? $_SESSION['submitted_info']['template_name'] : $template_name;
	$template_data = isset($_SESSION['submitted_info']['template_form_template']) ? $_SESSION['submitted_info']['template_form_template'] : $template_data;
	$template_sort_order = isset($_SESSION['submitted_info']['template_sort_order']) ? $_SESSION['submitted_info']['template_sort_order'] : $template_sort_order;
?>
					<tr>
						<td>
							<fieldset class="selectedFieldSet">
								<?php echo tep_draw_form('hla_template', FILENAME_HLA_TEMPLATE, 'action='.$action_param, 'post'); ?>
								<?php echo tep_draw_hidden_field('template_id', $template_id, 'id="template_id"');?>
								
								<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td class="customerFormAreaTitle"><?=TEXT_HEADING_HLA_TEMPLATE;?></td>
									</tr>
									<tr>
					    				<td class="formArea">
											<table border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_HLA_TEMPLATE_STATUS;?></td>
													<td class="main">
														<?php echo tep_draw_radio_field('template_status', '1', $template_status_active, '', 'id="template_status"') . '&nbsp;' . TEXT_ACTIVE . '&nbsp;' . tep_draw_radio_field('template_status', '0', $template_status_inactive, '', 'id="template_status"') . '&nbsp;' . TEXT_INACTIVE; ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_HLA_TEMPLATE_NAME;?></td>
													<td class="main">
														<?php echo tep_draw_input_field('template_name', $template_name, 'id="template_name" size="30"', true); ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%" valign="top"><?=ENTRY_HLA_TEMPLATE_FORM_TEMPLATE;?></td>
													<td class="main">
														<?php echo tep_draw_textarea_field('template_form_template', '', '90', '8', $template_data, 'id="template_form_template"', true);?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_HLA_SORT_ORDER;?></td>
													<td class="main">
														<?php echo tep_draw_input_field('template_sort_order', $template_sort_order, 'id="template_sort_order" size="10" maxlength="5"', true); ?>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									
									<tr>
										<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									
									<tr>
										<td align="right" class="main">
										<?php
											echo $submit_button;
											echo '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_HLA_TEMPLATE), 'name="next"', 'inputButton');
										?>
										</td>
									</tr>
								</table>
								</form>
							</fieldset>
						</td>
					</tr>
<?php
} else {
	$hla_template_status_array = array('1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT), 
									   '0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
										);
	
	$hla_template_select_sql = "SELECT hla_template_id, hla_template_name, hla_template_data, hla_template_sort_order, hla_template_status FROM " . TABLE_HLA_TEMPLATE;
	$hla_template_result_sql = tep_db_query($hla_template_select_sql);
	
	$page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, strtolower($hla_template_select_sql), $hla_template_numrows);
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
			   						<td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_TEMPLATE_ID;?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_TEMPLATE;?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_FORM_TEMPLATE;?></td>
								    <td width="12%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SORT_ORDER;?></td>
								    <td width="12%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_STATUS;?></td>
								    <td width="12%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION;?></td>
								</tr>
								<?php
									$row_count = 0;
									while ($hla_template_row = tep_db_fetch_array($hla_template_result_sql)) {
										$row_style = ($row_count % 2) ? 'ordersListingEven' : 'ordersListingOdd';
										
										$template_id = $hla_template_row['hla_template_id'];
								?>
									<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
										<td align="center" class="ordersRecords" valign="top"><?=$template_id;?></td>
										<td class="ordersRecords" valign="top"><?=$hla_template_row['hla_template_name']?></td>
										<td class="ordersRecords" valign="top"><?=(strlen($hla_template_row['hla_template_data']) > 100 ? substr($hla_template_row['hla_template_data'], 0, 100)."..." : $hla_template_row['hla_template_data'])?></td>
										<td align="center" class="ordersRecords" valign="top"><?=$hla_template_row['hla_template_sort_order'];?></td>
										<td align="center" class="ordersRecords" valign="top">
											<div style="text-align: center;" id="status-o-<?=$template_id?>">
										<?php
											foreach ($hla_template_status_array as $status_id => $img_res) {
												if ((int)$hla_template_row['hla_template_status'] == (int)$status_id) {
													echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
												} else {
													echo '<a href="javascript:change_status(\''.$template_id.'\',\''.$status_id.'\',\'set_products_template_status\')">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
												}
											}
										?>
											</div>
										</td>
										<td align="center" class="ordersRecords" valign="top" nowrap>
											<a href="<?=tep_href_link(FILENAME_HLA_TEMPLATE, 'action=edit_template&tID=' . $template_id); ?>"><?=tep_image(DIR_WS_ICONS . 'edit.gif', 'Edit Products Supplier', '', '', 'align="top"');?></a>
											<a href="javascript:void(confirm_delete('<?=htmlspecialchars(addslashes($hla_template_row['hla_template_name']))?>', 'HLA Template', '<?=tep_href_link(FILENAME_HLA_TEMPLATE, 'action=delete_products_hla_template&tID=' . $template_id);?>'))"><?=tep_image(DIR_WS_ICONS . 'delete.gif', 'Delete HLA Template', '', '', 'align="top"')?></a>
										</td>
									</tr>
								<?php
										$row_count++;
									}
								?>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" nowrap><?=$page_split_object->display_count($hla_template_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_TEMPLATES)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($hla_template_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr align="right">
						<td>
							<a href="<?=tep_href_link(FILENAME_HLA_TEMPLATE, 'action=new_template'); ?>" style="text-decoration: none;"><?php echo tep_submit_button('New Template', 'New Template', '', 'inputButton'); ?></a>
						</td>
					</tr>
<?php
}
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	
	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
	<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
	
	</body>
</html>
<script>
function change_status(id, flag, action) {
	var status	= confirm('Confirm to update status?');
	if (status ==  true) {
		switch (action) {
			case 'set_products_template_status':
				var ref_url = "<?=tep_href_link(FILENAME_HLA_TEMPLATE)?>?action=" + action + "&flag=" + flag + "&tID="+id;
				
				jQuery.get(ref_url, function(data) {
					jQuery('#status-o-'+id).html(data);
				});
			break;
		}
	}
}
</script>