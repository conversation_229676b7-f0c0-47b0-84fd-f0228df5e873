<!-- based on insimage.dlg -->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD W3 HTML 3.2//EN">
<HTML id=dlgFind STYLE="width: 420px; height: 250px; ">
   <HEAD>
      <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
      <meta http-equiv="MSThemeCompatible" content="Yes">
      <TITLE>
         Cell Properties
      </TITLE>
      <style>
        html, body, button, div, input, select, fieldset { font-family: MS Shell Dlg; font-size: 8pt; position: absolute; };
      </style>
<script>
opener = window.dialogArguments;
var editor_obj = opener.document.all["_" + opener.getGlobalVar('_editor_field') + "_editor"];
var td = opener.getGlobalVar('_editor_cell');
</script>
<SCRIPT defer>
function _CloseOnEsc() {
  if (event.keyCode == 27) { window.close(); return; }
}
window.onerror = HandleError
function HandleError(message, url, line) {
  var std = "An error has occurred in this dialog." + "\n\n"
  + "Error: " + line + "\n" + message;
  alert(std);
  window.close();
  return true;
}
function Init() {
  // event handlers  
  document.body.onkeypress = _CloseOnEsc;
  btnOK.onclick = new Function("btnOKClick()");
  align.selectedIndex = 0;
  for (var i=0; i< align.length; i++) {
     if (align[i].value.toLowerCase() == td.align.toLowerCase()) {
        align.selectedIndex = i;
     }
  }
  vAlign.selectedIndex = 0;
  for (var i=0; i< vAlign.length; i++) {
     if (vAlign[i].value.toLowerCase() == td.vAlign.toLowerCase()) {
        vAlign.selectedIndex = i;
     }
  }
  //if (td.bgColor == ''){
     bgColor.innerText = ' ' + td.bgColor.toUpperCase();
  //}
  bgColor.style.backgroundColor = td.bgColor;
  //if (td.borderColor == ''){
     borderColor.innerText = ' ' + td.borderColor.toUpperCase();
  //}
  borderColor.style.backgroundColor = td.borderColor;
  //if (td.borderColorDark == ''){
     borderColorDark.innerText = ' ' + td.borderColorDark.toUpperCase();
  //}
  borderColorDark.style.backgroundColor = td.borderColorDark;
  //if (td.borderColorLight == ''){
     borderColorLight.innerText = ' ' + td.borderColorLight.toUpperCase();
  //}
  borderColorLight.style.backgroundColor = td.borderColorLight;
  if (td.height == null){
    height.value = '';
    heightExt.value = '';
  } else { 
     if (td.height.search(/%/) < 0) {
        height.value = td.height;
        heightExt.value = '';
     } else {
        height.value = td.height.split('%')[0]; 
        heightExt.value = '%';
     }
  }   
  if (td.width == null){
    width.value = '';
    widthExt.value = '';
  } else { 
     if (td.width.search(/%/) < 0) {
        widthExt.value = '';
        width.value = td.width;
     } else {
        width.value = td.width.split('%')[0]; 
        widthExt.value = '%';
     }
  } 
  id.value = td.id;
  title.value = td.title;
  if (td.background == null) {
     background.value = '';
  } else {
     background.value = td.background;
  }
  colSpan.value = td.colSpan;
  rowSpan.value = td.rowSpan;
  noWrap.checked = td.noWrap;
}
function radioValue(radioobject){
   for (var i=0; i < radioobject.length; i++) {
      if (direction[i].checked) {
         return radioobject[i].value;
      }
   }
} 
function _isValidNumber(txtBox) {
  if (txtBox == '') {
     return true;
  } else {  
     var val = parseInt(txtBox);
     if (isNaN(val) || val < 0 || val > 9999) { return false; }
     return true;
  }
}
function btnOKClick() {
   if (!_isValidNumber(height.value)) {
      alert("You must specify a number between 0 and 9999 for height!");
      heigth.focus();
      return;
   }
   if (height.value == ''){heightExt.value = '';}
   if (!_isValidNumber(width.value)) {
      alert("You must specify a number between 0 and 9999 for width!");
      width.focus();
      return;
   }
   if (width.value == ''){widthExt.value = '';}
   td.align = align[align.selectedIndex].value;
   td.vAlign = vAlign[vAlign.selectedIndex].value;
   td.bgColor = bgColor.style.backgroundColor;
   td.borderColor = borderColor.style.backgroundColor;
   td.borderColorDark = borderColorDark.style.backgroundColor;
   td.borderColorLight = borderColorLight.style.backgroundColor;
   td.height = height.value + heightExt.value;
   td.id = id.value ;
   if (title.value == ''){
      if (td.getAttribute('title') != null){td.removeAttribute('title');}
   } else {td.title = title.value;}
   if (width.value == ''){
      if (td.getAttribute('width') != null){td.removeAttribute('width');}
   } else {td.width = width.value + widthExt.value;}
   if (background.value == '' ){
      if (td.getAttribute('background') != null){td.removeAttribute('background');}
   } else {td.background = background.value;}
   if (!_isValidNumber(colSpan.value)) {
      alert("You must specify a number between 0 and 9999 for colSpan!");
      colSpan.focus();
      return;
   }
   if (colSpan.value == '' || colSpan.value == '0' || colSpan.value == '1'){
      if (td.getAttribute('colSpan') != null){td.removeAttribute('colSpan');}
   } else {td.colSpan = colSpan.value;}
   if (!_isValidNumber(rowSpan.value)) {
      alert("You must specify a number between 0 and 9999 for rowSpan!");
      rowSpan.focus();
      return;
   }
   if (rowSpan.value == '' || rowSpan.value == '0' || rowSpan.value == '1'){
      if (td.getAttribute('rowSpan') != null){td.removeAttribute('rowSpan');}
   } else {td.rowSpan = rowSpan.value;}
   td.noWrap = noWrap.checked;
   window.close();
   return true;
}
function setColors (but) {
   but.style.borderStyle = 'inset';
   var color=showModalDialog('set_color.html',but.style.backgroundColor,'resizable:no;help:no;status:no;scroll:no;');
   if (color == '' || color == null){
     but.innerText = '';
     but.style.backgroundColor = '';
   } else {
     but.innerText = ' #' + color.toUpperCase();
     but.style.backgroundColor =  color;
   }
   but.style.borderStyle = 'outset';
   return;
}
</SCRIPT>
   </HEAD>
   <BODY id=bdy onload="Init()" style="background: threedface; color: windowtext;" scroll=no>
      <DIV id=divHeight style="left: 1em;                    top: 1em; width: 10em; height: 1.9em; ">
        Height:
      </DIV>
      <INPUT ID=height type=text style="left: 10em;          top: 0.8em; width: 3em; height: 1.9em; " tabIndex=10 onfocus="select()">
      <select name="heightExt" style="left: 13.2em;          top: 0.8em; width: 6em; height: 1.9em; " tabIndex=20>
         <option value="">Pixels</option>
         <option value="%" selected>Percent</option>
      </select>
      <DIV id=divWidth style="left: 20em;                     top: 1em; width: 10em; height: 1.9em; ">
        Width:
      </DIV>
      <INPUT ID=width type=text style="left: 27.9em;           top: 0.8em; width: 3em; height: 1.9em; " tabIndex=30 onfocus="select()">
      <select name="widthExt" style="left: 31.1em;           top: 0.8em; width: 6em; height: 1.9em; " tabIndex=40>
         <option value="">Pixels</option>
         <option value="%" selected>Percent</option>
      </select>
      <DIV id=divId style="left: 1em;                        top: 3em; width: 10em; height: 1.2168em; ">
        Id:
      </DIV>
      <INPUT ID=id type=text style="left: 10em;              top: 2.8em; width: 27.04em; height: 1.9em; " tabIndex=50 onfocus="select()">
      <DIV id=divTitle style="left: 1em;                     top: 5em; width: 31em; height: 1.2168em; ">
        Tooltip:
      </DIV>
      <INPUT ID=title type=text style="left: 10em;           top: 4.8em; width: 27.04em; height: 1.9em; " tabIndex=60 onfocus="select()">
      <DIV id=divBackground style="left: 1em;                top: 7em; width: 10em; height: 1.2168em; ">
        Background Image:
      </DIV>
      <INPUT ID=background type=text style="left: 10em;      top: 6.8em; width: 27.04em; height: 1.9em; " tabIndex=70 onfocus="select()">
      <DIV id=divAlign style="left: 1em;                     top: 9em; width: 10em; height: 1.2168em; ">
        Horizontal Align:
      </DIV>
      <select name="align" style="left: 10em;                top: 8.8em; width: 6em; height: 1.9em; " tabIndex=80>
           <option value="">Not set</option>
           <option value="center">center</option>
           <option value="justify">Justify</option>
           <option value="left">Left</option>
           <option value="right">Right</option>
      </select>
      <DIV id=divvAlign style="left: 20em;                    top: 9em; width: 10em; height: 1.2168em; ">
        Vertical Align:
      </DIV>
      <select name="vAlign" style="left: 31em;               top: 8.8em; width: 6em; height: 1.9em; " tabIndex=90>
           <option value="">Not set</option>
           <option value="middle">Middle</option>
           <option value="baseline">Baseline</option>
           <option value="bottom">Bottom</option>
           <option value="top">Top</option>
      </select>
      <DIV id=divBgColor style="left: 1em;                   top: 11em; width: 10em; height: 1.2168em; ">
        Background Color:
      </DIV>
      <DIV id=bgColor style="left: 10em;                     top: 11em; width: 6em; height: 1.2168em;border-style: outset; border-width: thin;" onclick="setColors(this);"></DIV>
      <DIV id=divBorderColor style="left: 20em;              top: 11em; width: 10em; height: 1.2168em; ">
        Border Color:
      </DIV>
      <DIV id=borderColor style="left: 31em;                 top: 11em; width: 6em; height: 1.2168em;border-style: outset; border-width: thin;" onclick="setColors(this);"></DIV>
      <DIV id=divBorderColorDark style="left: 1em;           top: 13em; width: 10em; height: 1.2168em; ">
        Border Color Dark:
      </DIV>
      <DIV id=borderColorDark style="left: 10em;             top: 13em; width: 6em; height: 1.2168em;border-style: outset; border-width: thin;" onclick="setColors(this);"></DIV>
      <DIV id=divBorderCOlorLight style="left: 20em;         top: 13em; width: 10em; height: 1.2168em; ">
        Border Color Light:
      </DIV>
      <DIV id=borderColorLight style="left: 31em;            top: 13em; width: 6em; height: 1.2168em;border-style: outset; border-width: thin;" onclick="setColors(this);"></DIV>
      <DIV id=divColSpan style="left: 1em;                   top: 15em; width: 10em; height: 1.9em; ">
        Colspan:
      </DIV>
      <INPUT ID=colSpan type=text style="left: 10em;         top: 14.8em; width: 3em; height: 1.9em; " tabIndex=100 onfocus="select()">
      <DIV id=divrowSpan style="left: 17em;                  top: 15em; width: 10em; height: 1.9em; ">
        Rowspan:
      </DIV>
      <INPUT ID=rowSpan type=text style="left: 23em;         top: 14.8em; width: 3em; height: 1.9em; " tabIndex=110 onfocus="select()">
      <DIV id=divrowSpan style="left: 30em;                  top: 15em; width: 10em; height: 1.9em; ">
        No Wrap:
      </DIV>
      <input ID=noWrap  type=checkbox  style="left: 35.6em;  top: 15em; width: 1.5em; height: 1.5em; " tabIndex=120 onfocus="select()">
      <BUTTON ID=btnOK style="left: 10em;                    top: 17em; width: 8.5em; height: 2.2em; " type=submit tabIndex=130> 
         OK
      </BUTTON>
      <BUTTON ID=btnCancel style="left: 19.9em;              top: 17em; width: 8.5em; height: 2.2em; " type=reset tabIndex=140 onClick="window.close();">
         Cancel
      </BUTTON>
   </BODY>
</HTML>