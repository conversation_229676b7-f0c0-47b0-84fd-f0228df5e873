/*
  $Id: stylesheet.css,v 1.2 2004/09/23 05:36:08 stanley Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

.boxText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.errorBox { font-family : Verdana, Arial, sans-serif; font-size : 10px; background: #ffb3b5; font-weight: bold; }
.stockWarning { font-family : Verdana, Arial, sans-serif; font-size : 10px; color: #cc0033; }
.productsNotifications { background: #f2fff7; }
.orderEdit { font-family : Verdana, Arial, sans-serif; font-size : 10px; color: #70d250; text-decoration: underline; }

BODY {
  background: #ffffff;
  color: #000000;
  margin: 0px;
}

A {
  color: #0000FF;
  text-decoration: none underline;
}

A:hover {
  color: #AABBDD;
  text-decoration: underline;
}

FORM {
	display: inline;
}

TR.header {
  background: #ffffff;
}

TR.headerNavigation {
  background: #bbc3d3;
}

TD.headerNavigation {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  background: #bbc3d3;
  color: #ffffff;
  font-weight : bold;
}

A.headerNavigation { 
  color: #FFFFFF; 
}

a:hover.headerNavigation {
  color: #ffffff;
}

TR.headerError {
  background: #ff0000;
}

TD.headerError {
  font-family: Tahoma, Verdana, Arial, sans-serif;
  font-size: 12px;
  background: #ff0000;
  color: #ffffff;
  font-weight : bold;
  text-align : center;
}

TR.headerInfo {
  background: #00ff00;
}

TD.headerInfo {
  font-family: Tahoma, Verdana, Arial, sans-serif;
  font-size: 12px;
  background: #00ff00;
  color: #ffffff;
  font-weight: bold;
  text-align: center;
}

TR.footer {
  background: #bbc3d3;
}

TD.footer {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  background: #bbc3d3;
  color: #ffffff;
  font-weight: bold;
}

.infoBox {
  background: #b6b7cb;
}

.infoBoxContents {
  background: #f8f8f9;
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
}

.infoBoxNotice {
  background: #FF8E90;
}

.infoBoxNoticeContents {
  background: #FFE6E6;
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
}

TD.infoBoxHeading {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  font-weight: bold;
  background: #bbc3d3;
  color: #ffffff;
}

TD.infoBox, SPAN.infoBox {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
}

TR.accountHistory-odd, TR.addressBook-odd, TR.alsoPurchased-odd, TR.payment-odd, TR.productListing-odd, TR.productReviews-odd, TR.upcomingProducts-odd, TR.shippingOptions-odd {
  background: #f8f8f9;
}

TR.accountHistory-even, TR.addressBook-even, TR.alsoPurchased-even, TR.payment-even, TR.productListing-even, TR.productReviews-even, TR.upcomingProducts-even, TR.shippingOptions-even {
  background: #f8f8f9;
}

TABLE.productListing {
  border: 1px;
  border-style: solid;
  border-color: #b6b7cb;
  border-spacing: 1px;
}

.productListing-heading {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  background: #b6b7cb;
  color: #FFFFFF;
  font-weight: bold;
}

TD.productListing-data {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
}

A.pageResults {
  color: #0000FF;
}

a:hover.pageResults {
  color: #0000FF;
  background: #FFFF33;
}

TD.pageHeading, DIV.pageHeading {
  font-family: Verdana, Arial, sans-serif;
  font-size: 20px;
  font-weight: bold;
  color: #9a9a9a;
}

TR.subBar {
  background: #f4f7fd;
}

TD.subBar {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  color: #000000;
}

TD.main, P.main {
  font-family: Verdana, Arial, sans-serif;
  font-size: 11px;
  line-height: 1.5;
}

TD.smallText, SPAN.smallText, P.smallText {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
}

TD.accountCategory {
  font-family: Verdana, Arial, sans-serif;
  font-size: 13px;
  color: #aabbdd;
}

TD.fieldKey {
  font-family: Verdana, Arial, sans-serif;
  font-size: 12px;
  font-weight: bold;
}

TD.fieldValue {
  font-family: Verdana, Arial, sans-serif;
  font-size: 12px;
}

TD.tableHeading {
  font-family: Verdana, Arial, sans-serif;
  font-size: 12px;
  font-weight: bold;
}

SPAN.newItemInCart {
  font-family: Verdana, Arial, sans-serif;
  font-size: 10px;
  color: #ff0000;
}

CHECKBOX, INPUT, RADIO, SELECT {
  font-family: Verdana, Arial, sans-serif;
  font-size: 11px;
}

TEXTAREA {
  width: 100%;
  font-family: Verdana, Arial, sans-serif;
  font-size: 11px;
}

SPAN.greetUser {
  font-family: Verdana, Arial, sans-serif;
  font-size: 12px;
  color: #f0a480;
  font-weight: bold;
}

TABLE.formArea {
  background: #f1f9fe;
  border-color: #7b9ebd;
  border-style: solid;
  border-width: 1px;
}

TD.formAreaTitle {
  font-family: Tahoma, Verdana, Arial, sans-serif;
  font-size: 12px;
  font-weight: bold;
}

SPAN.markProductOutOfStock {
  font-family: Tahoma, Verdana, Arial, sans-serif;
  font-size: 12px;
  color: #c76170;
  font-weight: bold;
}

SPAN.productSpecialPrice {
  font-family: Verdana, Arial, sans-serif;
  color: #ff0000;
}

SPAN.errorText {
  font-family: Verdana, Arial, sans-serif;
  color: #ff0000;
}

.moduleRow { }
.moduleRowOver { background-color: #D7E9F7; cursor: pointer; cursor: hand; }
.moduleRowSelected { background-color: #E9F4FC; }

.checkoutBarFrom, .checkoutBarTo { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #8c8c8c; }
.checkoutBarCurrent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; }

/* message box */

.messageBox { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.messageStackError, .messageStackWarning { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #ffb3b5; }
.messageStackSuccess { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #99ff00; }

/* input requirement */

.inputRequirement { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }
