<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'images.php');
require_once(DIR_WS_CLASSES . 'customers_verification_document.php');

$customer_letter_edit_permission = tep_admin_files_actions(FILENAME_CUSTOMERS, 'CUSTOMER_EDIT_LATEST_DOC');

/////////////////////////CONFIG//////////////////////////////
// max script exec time
set_time_limit(60);
// max memory usage for larger images, defaults 8 MB in most PHP installations

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$cust_id = isset($_REQUEST['cID']) ? (int)$_REQUEST['cID'] : '';
$doc_id = isset($_REQUEST['doc_id']) ? $_REQUEST['doc_id'] : '';
$tocrop = isset($_REQUEST['tocrop']) ? $_REQUEST['tocrop'] : '';
$angle = isset($_REQUEST['angle']) ? $_REQUEST['angle'] : '';
$color = isset($_REQUEST['color']) ? $_REQUEST['color'] : '';
$textstring = isset($_REQUEST['textstring']) ? $_REQUEST['textstring'] : '';
$mirror = isset($_REQUEST['mirror']) ? $_REQUEST['mirror'] : '';
$message = isset($_REQUEST['message']) ? $_REQUEST['message'] : '';
$changes_applied = false;

$imageeditor_dir = "includes/javascript/imageEditor/";
$fnt_dir = $imageeditor_dir."ttf/";
$tpl_dir = $imageeditor_dir."tpl/";

$cvd_obj = new customers_verification_document($login_id);
$cvd_obj->setCustomerID($cust_id);

$editing_path = $cvd_obj->getLocalEditPath() . '/'; //DIR_FS_DATA_LL_SUBMISSION_TEMP;
$editing_file_name = 'user_'.$_SESSION['login_id'].'_'.$doc_id;
$temp_filename = $editing_filename = $editing_file_name . '.jpg';

///////////////////////// images ////////////////////////////
if (!$customer_letter_edit_permission) {
	echo ERROR_EDITOR_ACCESS_DENIED;
	exit;
} else if ($action=="preview" && $fontfile) {
	$image = new imagetext($fnt_dir.$fontfile,$fontsize,$fontcolor,$text,$fontangle);
	$image->preview();
	exit;
} else if ($action == "color_picker") {
	require_once($tpl_dir.'picker.php');
	exit;
} else if ($action == 'edit_preview') {
    // read from s3 temp folder
    $img = $cvd_obj->readTEMPDocument($temp_filename);
    header('Content-Type: image/jpeg');
    imagejpeg($img);
    imagedestroy($img);
	exit;
} else if ($action == "load_image") {
    // load the image to s3 temp folder
    if (!$temp_filename = $cvd_obj->copyDocumentToTemp($doc_id)) {
        echo ERROR_NO_UPLOAD_FILE;
        exit;
    }
} else if ($action == "save_image") {
    // copy from editing folder to source folder
	if($cvd_obj->saveDocument($doc_id, $temp_filename)) {
		$message = SUCCESS_FILE_UPDATE_SAVED_SUCCESSFULLY;
	} else {
		$message = SUCCESS_FILE_UPDATE_NOT_SAVED;
	}
	
	tep_redirect(tep_href_link(FILENAME_IMAGE_EDITOR, tep_get_all_get_params(array('action','message')).'message='.$message));
}

// download from s3 temp folder
if ($cvd_obj->downloadTempDocument($temp_filename)) {
	$info = new imageobject($editing_path, $temp_filename);
	$info->setNewfilename($editing_file_name);

	if ($info->handle){
		if ($tocrop == 'true' && ($offset_top != $offset_bottom && $offset_right != $offset_left)) {
			$info = $info->cropImage($offset_top,$offset_right,$offset_bottom,$offset_left);
			$changes_applied = true;
		}
		if ($angle) {
			$info = $info->rotateImage($angle);
			$changes_applied = true;
		}
		if ($color && ($offset_top != $offset_bottom && $offset_right != $offset_left)) {
			$info = $info->mergeColor($offset_top,$offset_right,$offset_bottom,$offset_left,$color);
			$changes_applied = true;
		}
		if ($textstring) {
			$info = $info->writeText($textxpos, $textypos, $textstring, $fontsize, $fnt_dir.$truetype, $fontcolor, $fontangle);
			$changes_applied = true;
		}
		if ($mirror) {
			$info = $info->mirrorImage();
			$changes_applied = true;
		}
		
		if (!$changes_applied) {
			$info->resizeImage();
		}
	}
	
	if ($info->handle) {
        $cvd_obj->uploadTempDocument($editing_path, $editing_filename);
	}
    
    if ($temp_filename !== $editing_filename) {
        if (file_exists($editing_path . $temp_filename)) {
            @unlink($editing_path . $temp_filename);
        }
    }
}

if (!$info->handle){
    if (tep_not_null($message)){ 
        echo "<h3>".$message."</h3>";	
    }
    exit;
}

// used for sorting
function cmp($a,$b) {
	return strcasecmp($a, $b);
}

function getTrueTypes() {
	$retstr = '';
	$handle = opendir($GLOBALS['fnt_dir']);
	
	while($file = readdir($handle)) $files[] = $file;
	usort($files, "cmp");
	foreach($files as $file){
		if (!is_dir($file) && strpos($file, ".ttf")){
			$split = split_dep("\.",$file);
			$retstr .= "<option value=\"".$file."\">".$split[0];
		}
	}
	return $retstr."</option>";
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title>Image Editor</title>
	<link rel="stylesheet" type="text/css" href="includes/javascript/imageEditor/css/imgareaselect-default.css" />
	<link rel="stylesheet" type="text/css" href="includes/javascript/imageEditor/css/imgeditor.css" />
	<script type="text/javascript" src="includes/javascript/jquery.js"></script>
	<script type="text/javascript" src="includes/javascript/imageEditor/scripts/jquery.imgareaselect.pack.js"></script>
	<script type="text/javascript" src="includes/javascript/imageEditor/scripts/colorPicker.js"></script>
	<script type="text/javascript" src="includes/javascript/imageEditor/scripts/imglib.js"></script>
	<script language="JavaScript1.2">
    	var first=true;
    	var default_width = <?=$info->width?>;
    	var default_height = <?=$info->height?>;
    	
		function showAreaSelect(show, atype){		
			if (show){
				if (atype == 'crop' && $("#colordiv").css("visibility") != 'hidden') {
					alert("It's in use.");
					return false;
				} else if (atype == 'color' && $("#clipdiv").css("visibility") != 'hidden') {
					alert("It's in use.");
					return false;
				} else if (atype == 'crop') {
					document.mainform.tocrop.value = true;	
				}
			 	
				$('img#photo').imgAreaSelect({ enable: true, x1: document.mainform.offset_left.value, y1: document.mainform.offset_top.value, x2: document.mainform.offset_right.value, y2: document.mainform.offset_bottom.value});
			}else{
				getElement("color").value = '';
				getElement("textstring").value = '';
				$("div.imgareaselect-selection").css("background-color", '');
				document.mainform.tocrop.value = false;
				$('img#photo').imgAreaSelect({ disable: true, hide: true });
			}
			
			if (atype == 'crop') {
				$('input[name=offset_left],input[name=offset_left2]').val(0); 
				$('input[name=offset_top],input[name=offset_top2]').val(0); 
				$('input[name=offset_right],input[name=offset_right2]').val(default_width); 
				$('input[name=offset_bottom],input[name=offset_bottom2]').val(default_height);
			} else if (atype == 'color') {
				document.mainform.tocrop.value = false;
				getElement("color").value = '';
				$("div.imgareaselect-selection").css("background-color", '');
			}
			return true;
		}
		
		function saveChanges() {
			document.mainform.action.value = "save_image";
			document.mainform.submit();
		}
		
		$(document).ready(function () { 
			$('img#photo').imgAreaSelect({ 
				handles: true,
				disable: true,
				onSelectEnd: function (img, selection) { 
					$('input[name=offset_left],input[name=offset_left2]').val(selection.x1); 
					$('input[name=offset_top],input[name=offset_top2]').val(selection.y1); 
					$('input[name=offset_right],input[name=offset_right2]').val(selection.x2); 
					$('input[name=offset_bottom],input[name=offset_bottom2]').val(selection.y2);
				}
			});
		});
    </script>
</head>

<body onload="libinit()">
	<?=tep_draw_form("mainform", FILENAME_IMAGE_EDITOR, tep_get_all_get_params(array('action','message')), "post", "enctype='multipart/form-data'")?>
		<input type="hidden" name="cID" value="<?=$cust_id?>">
		<input type="hidden" name="doc_id" value="<?=$doc_id?>">
		<input type="hidden" name="action" value="">
		<input type="hidden" name="offset_left" value="0" size="2">
		<input type="hidden" name="offset_top" value="0" size="2">
		<input type="hidden" name="offset_right" value="<?=$info->width?>" size="2">
		<input type="hidden" name="offset_bottom" value="<?=$info->height?>" size="2">
					
		<div id="tooldiv">
			<table border="0" width="200px" cellspacing="2" cellpadding="2" class="white">
			<tr>
				<th><?=HEADING_TITLE?></th>
			</tr>
			<tr>
				<td>
					<table cellspacing="0" cellpadding="0" border="0" width="100%" class="white">
					<tr>
						<td align="center"><a href="#" onclick="objrotate.showIt();return false;"><?=BUTTON_ROTATE?></a></td>
					</tr>
					<tr>
						<td align="center"><a href="#" onclick="if(showAreaSelect(true,'crop')) objclip.showIt();return false;"><?=BUTTON_CROP?></a></td>
					</tr>
					<tr>
						<td align="center"><a href="#" onclick="objtext.showIt();return false;"><?=BUTTON_INSERT_TEXT?></a></td>
					</tr>
					<tr>
						<td align="center"><a href="#" onclick="if(showAreaSelect(true,'color')) objcolor.showIt();return false;"><?=BUTTON_MODIFY_COLOR?></a></td>
					</tr>
					<tr>
						<td align="center"><!--a href="#" onclick="saveChanges();return false;"><?=BUTTON_SAVE_CHANGES?></a-->

						</td>
					</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td align="center" style="background-color:#FFF">
					<a href="#" onclick="saveChanges();return false;">
<?php
						echo tep_image_submit('button_save.gif', IMAGE_SAVE, 'style="border-color:#FFF"');
?>
					</a>
				</td>
			</tr>
			</table>
		</div>

		<div id="rotatediv">
			<table border="0" cellspacing="2" cellpadding="2" class="white">
			<th colspan="2"><?=ENTRY_ROTATE?></th>
			<th><a href="#" onclick="objrotate.hideIt();return false;"><?=BUTTON_CLOSE?></a></th>
			<tr>
				<td>
					<select name="angle">
						<option value="" selected><?=ROTATE_OPTION_ROTATE?>
						<option value="90"><?=ROTATE_OPTION_LEFT?>
						<option value="180"><?=ROTATE_OPTION_FLIP?>
						<option value="270"><?=ROTATE_OPTION_RIGHT?>
					</select>
				</td>
				<td><input valign="bottom" type="checkbox" name="mirror" value="true"><?=CHECKBOX_MIRROR?></td>
				<td><input type="submit" value="<?=BUTTON_APPLY?>"></td>
			</tr>
			</table>
		</div>

		<div id="colordiv">
			<table cellspacing="2" cellpadding="2" class="white">
		    <th colspan="2"><?=ENTRY_COLOR_MASKING?></th>
			<th><a href="#" onclick="showAreaSelect(false,'color');objcolor.hideIt();return false;"><?=BUTTON_CLOSE?></a></th>
			<tr>
				<td><?=TEXT_COLOR?></td>
				<td>
					<input id="color" type="text" name="color" size="8" readonly="readonly">
					<a href="javascript:TCP.popup( document.forms['mainform'].elements['color'], 1, '<?=FILENAME_IMAGE_EDITOR?>?action=color_picker')">
						<img style="width: 16px; height: 15px;" title="Open Color Picker" src="includes/javascript/imageEditor/images/bg.gif" border="0"/>
					</a>
				</td>
				<td><input type="submit" value="<?=BUTTON_APPLY?>"></td>
			</tr>
			</table>
		</div>

		<div id="clipdiv">
			<table cellspacing="2" cellpadding="2" class="white">
			<th colspan="4"><?=ENTRY_CROP?></th>
			<th><a href="#" onclick="showAreaSelect(false,'crop');objclip.hideIt();return false;"><?=BUTTON_CLOSE?></a></th>
			<tr>
				<td><?=TEXT_LEFT?></td>
				<td><input name="offset_left2" type="text" value="0" size="2" readonly="readonly"> px</td>
				<td><?=TEXT_TOP?></td>
				<td><input name="offset_top2" type="text" value="0" size="2" readonly="readonly"> px</td>
				<td><input type="hidden" name="tocrop"></td>
			</tr>
			<tr>
				<td><?=TEXT_RIGHT?></td>
				<td><input name="offset_right2" type="text" value="<?=$info->width?>" size="2" readonly="readonly"> px</td>
				<td><?=TEXT_BOTTOM?></td>
				<td><input name="offset_bottom2" type="text" value="<?=$info->height?>" size="2" readonly="readonly"> px</td>
				<td><input type="submit" value="<?=BUTTON_APPLY?>"></td>
			</tr>
			</table>
		</div>
		
		<div id="textdiv">
			<table cellspacing="2" cellpadding="2" class="white">
			<th colspan="4"><?=ENTRY_TEXT?></th>
			<th><a href="#" onclick="objpreview.hideIt();objtext.hideIt();return false;"><?=BUTTON_CLOSE?></a></th>
			<tr>
				<td><?=TEXT_TEXT?></td>
				<td colspan="3">
					<input id="textstring" size="40" type="text" name="textstring" onkeyup="showTextPreviewImage(this);">
					<input type="hidden" name="textxpos">
					<input type="hidden" name="textypos">
				</td>
			</tr>
			<tr>
				<td><?=TEXT_FONT?></td>
				<td>
					<select name="truetype" onchange="showTextPreviewImage(document.mainform.textstring)"><?=getTrueTypes()?></select>
				</td>
				<td><?=TEXT_SIZE?></td>
				<td>
					<select id="fontsize" name="fontsize" onchange="showTextPreviewImage(document.mainform.textstring)"><?=$info->writeOptions(12,200,40)?></select> px
				</td>
				<td></td>
			</tr>
			<tr>
				<td><?=TEXT_FONT_COLOR?></td>
				<td colspan="3">
					<select id="fontcolor" name="fontcolor" onchange="showTextPreviewImage(document.mainform.textstring)"><?=$info->getNamedColors("black");?></select>
				</td>
				<td></td>
			</tr>
			<tr>
				<td><?=TEXT_ANGLE?></td>
				<td colspan="3">
					<select id="fontangle" name="fontangle" onchange="showTextPreviewImage(document.mainform.textstring)"><?=$info->writeOptions(0,90,0)?></select> <?=SYMBOLS_TERM_DEGREES?>
				</td>
				<td><input type="submit" value="<?=BUTTON_APPLY?>"></td>
			</tr>
			</table>
		</div>
	
		<div id="previewdiv" onclick="setTextPreviewXY()">
			<img name="previewimage" id="previewimage">
		</div>
	</form>
<?php
if (tep_not_null($message)){ 
	echo "<h3>".$message."</h3>";	
}	
?>
	<div id="imgdiv" style="width:<?=$info->width?>px;height:<?=$info->height?>px;">
		<img id="photo" src="<?=tep_href_link(FILENAME_IMAGE_EDITOR, tep_get_all_get_params(array('action','message')).'action=edit_preview')?>" <?=$info->string; ?> align="absmiddle">
	</div>
	<div id="seconddiv"><img name="secondimage" id="secondimage"></div>
</body>
</html>