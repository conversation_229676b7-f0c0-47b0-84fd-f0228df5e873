<?php
/*
  $Id: image_upload.php,v 1.9 2015/10/16 09:54:08 jeeva.ka<PERSON><PERSON>an Exp $

  Developer: Siong <PERSON>at Ch'ng
  Copyright (c) 2005 SKC Ventrue

  Released under the GNU General Public License

 * ALL SECTION RELATED NEED TO RECHECK BEFORE LIVE
 * Using temporary folder as the upload destination, because cannot connect to the live directory before the script committed to live server.
 */

require('includes/application_top.php');
require('includes/classes/ogm_amazon_ws.php');
require(DIR_WS_FUNCTIONS . 'image_upload.php');
define('SEARCH_KEY_IMAGE_CATEGORY', 'image_category');

$images_expire_period = 180; //6 months in days
$maxsize_limit = 5 * 1024 * 1024;
$file_directory = tep_db_prepare_input($_GET['dir']);
$file_post = tep_db_prepare_input($_GET['file']);
$gallery_post = tep_db_prepare_input($_GET['gallery']);
$uploader_post = tep_db_prepare_input($_GET['uploader']);
$action = tep_db_prepare_input($_GET['action']);
$s3_folder_constant = '$folder$';

$s3 = new ogm_amazon_ws();
$aws_flag = $s3->is_aws_s3_enabled();

if (tep_not_null($action)) {
    if (tep_not_null($file_directory)) {
        if (tep_check_image_upload_permission($file_directory)) {
            if ($aws_flag) {
                $s3_info = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY, $file_directory);

                if (is_object($s3_info['aws_s3_info'])) {
                    $bucket = $s3_info['aws_s3_info']->{'bucket'};
                    $path = $s3_info['aws_s3_info']->{'path'};
                    $s3->set_bucket_key($bucket);
                    $s3->set_filepath($path);
                    $s3->set_acl('ACL_PUBLIC');
                    $s3->set_storage('STORAGE_STANDARD');
                    $image_path = array('web_path' => $s3->get_image_url_by_instance());

                    unset($s3_info);
                }
            } else {
                $image_path = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY, $file_directory);
            }

            switch ($action) {
                case 'simple_upload':
                    $process_error = false;

                    if (!$aws_flag) {
                        $file_path = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY, $file_directory);
                    }

                    if ($aws_flag || tep_not_null($file_path)) {
                        for ($i = 0; $i < count($_FILES['ufile']['tmp_name']); $i++) {
                            $tempFile = $_FILES['ufile']['tmp_name'][$i];
                            $targetPath = $file_path['file_path'] . strtolower(basename($_FILES['ufile']['name'][$i]));
                            $fileType = $_FILES['ufile']['type'][$i];
                            $fileSize = $_FILES['ufile']['size'][$i];
                            $cache_control_flag = isset($_POST['cache_control_check']) && $_POST['cache_control_check'] === '1';

                            if ($cache_control_flag) {
                                $filePathInfo = pathinfo($_FILES['ufile']['name'][$i]);
                                $fileName = strtolower($filePathInfo['filename']) . '-' . date('Ymd-his') . '.' . $filePathInfo['extension'];
                            } else {
                                $fileName = strtolower($_FILES['ufile']['name'][$i]);
                            }

                            if (tep_not_null($tempFile)) {
                                // check for uploaded file size
                                if ($fileSize > $maxsize_limit) {
                                    $error_msg = TEXT_INFO_ERROR_LARGE_FILE_SIZE;
                                    $process_error = true;
                                }

                                //check if its image file
                                $im = tep_verify_file_mime($tempFile);
                                if (!$im) {
                                    if ($fileType != 'application/x-shockwave-flash' || $fileType != 'application/octet-stream') {
                                        $error_msg = TEXT_INFO_ERROR_FILE_TYPE;
                                        $process_error = true;
                                    }
                                }

                                if (!$process_error) {
                                    if ($aws_flag) {
                                        $s3->set_file(array('tmp_name' => $tempFile));
                                        $s3->set_filename($fileName);

                                        if ($cache_control_flag) {
                                            $s3->set_headers(array('Cache-Control' => 'max-age=' . $s3->default_cache_control_max_age));
                                        } else {
                                            $s3->set_headers(null);
                                        }

                                        $s3->save_file();
                                    } else {
                                        move_uploaded_file($tempFile, $targetPath);
                                    }

                                    if (end($_FILES['ufile']['tmp_name']) == $tempFile) {
                                        $messageStack->add_session(TEXT_INFO_NOTICE_FILE_UPLOAD_SUCCESSFUL, 'success');
                                    }
                                } else {
                                    $messageStack->add_session($error_msg, 'error');
                                }
                            } else {
                                $messageStack->add_session(TEXT_INFO_ERROR_EMPTY_FILE_UPLOAD, 'error');
                            }
                        }
                    } else {
                        $messageStack->add_session(TEXT_INFO_ERROR_NO_PERMISSION_UPLOAD, 'error');
                    }

                    tep_redirect(tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'cache_control_check')) . 'action=upload_image_file&cache_control_check=' . $_POST['cache_control_check']));
                    break;
                // Delete files
                case 'del_image_file' :
                    if ($aws_flag) { //delete object in S3
                        if (tep_not_null($file_post)) {
                            $s3->delete_file($file_post);
                            tep_redirect(tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action')) . 'action=edit_image'));
                        }
                    } else {
                        if (tep_not_null($file_post) && $image_path['file_path']) {
                            $path = $image_path['file_path'] . '/' . $file_post;
                            if (file_exists($path)) {
                                unlink($path);
                                tep_redirect(tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action')) . 'action=edit_image'));
                            }
                        }
                    }
                    break;
            }
        }
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <!-- header //-->
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>	<head>
        <script language="javascript" src="includes/general.js"></script>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/uploadify/uploadify.css">
        <link rel="stylesheet" href="includes/javascript/fancybox/jquery.fancybox-1.3.4.css" type="text/css" media="screen"/>
        <script type="text/javascript" src="includes/javascript/uploadify/swfobject.js"></script>
        <script type="text/javascript" src="includes/javascript/uploadify/jquery-1.4.2.min.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery.MultiFile.js"></script>
        <script type="text/javascript" src="includes/javascript/uploadify/jquery.uploadify.v2.1.4.js"></script>
        <script type="text/javascript" src="includes/javascript/fancybox/jquery.fancybox-1.3.4.pack.js"></script>
        <script type="text/javascript" src="includes/javascript/image_upload.js"></script>

    </head>
    <!--next comes the form, you must set the enctype to "multipart/frm-data" and use an input type "file" -->

    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <?php
                //Image Gallery
                if ($action == "edit_image" && tep_check_image_upload_permission($file_directory)) {
                    ?>	
                    <td width="100%" valign="top">
                        <fieldset style="border: 1px solid #CDCDCD; padding: 8px; padding-bottom:0px; margin: 8px 0">
                            <legend><strong><?= $image_path['web_path'] . ' ' . TEXT_INFO_HEADING_GALLERY_TITLE ?></strong></legend>			
                            <h2><?= $file_directory ?></h2>
                            <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('file', 'gallery')) . 'gallery=1') ?>"><?= TEXT_LINK_TITLE_LATEST_IMAGES ?></a> 
                            <?
                            if (!$aws_flag) {
                                echo "| <a href=\"" . tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('file', 'gallery')) . 'gallery=2') . "\">" . TEXT_LINK_TITLE_OLD_IMAGES . "</a>";
                            }
                            ?>
                            <p><?= TEXT_INFO_HEADING_GALLERY_INSTRUCTION ?></p>
                            <?
                            //New Image Gallery Section
                            if ($gallery_post == 1) {
                                ?>							
                                <p><b><?= TEXT_INFO_HEADING_NEW_IMAGES_GALLERY ?></b></p>
                                <table border="0" width="90%" cellspacing="1" cellpadding="2">
                                    <tr>	
                                        <td class="reportBoxHeading"><?= TABLE_HEADING_FILE_NAME ?></td>
                                        <td class="reportBoxHeading" width="15%"><?= TABLE_HEADING_FILE_SIZE ?></td>
                                        <td class="reportBoxHeading" width="20%"><?= TABLE_HEADING_LAST_ACCESS ?></td>
                                        <td class="reportBoxHeading" width="15%"><?= TABLE_HEADING_FILE_DELETE ?></td>
                                    </tr>
                                    <?
                                    if ($aws_flag) {
                                        $new_list = array();
                                        do {
                                            if (count($bucket_contents)) {
                                                $bucket_contents = $s3->s3_api(array('method' => 'list_objects', $bucket, array("prefix" => $path, "delimiter" => '/', 'marker' => (string) end($bucket_contents->body->query('descendant-or-self::Contents')->getArrayCopy())->Key))); // Add marker as point to resume if files listing over 1k.
                                            } else {
                                                $bucket_contents = $s3->s3_api(array('method' => 'list_objects', $bucket, array("prefix" => $path, "delimiter" => '/'))); // Add delimiter to just retrieve current level files.
                                            }

                                            foreach ($bucket_contents->body->query('descendant-or-self::Contents')->getArrayCopy() as $file) {
                                                $fname = basename($file->Key);
                                                $fsize = round($file->Size / 1000, 2);
                                                $fmod = date("Y-m-d H:i:s", strtotime($file->LastModified));
                                                if (!strpos($file->Key, $s3_folder_constant)) {
                                                    $new_list[] = array("fmod" => $fmod,
                                                        "fname" => $fname,
                                                        "fsize" => $fsize
                                                    );
                                                }
                                            }
                                        } while ((string) $bucket_contents->body->IsTruncated === 'true');

                                        if (tep_not_null($new_list)) {
                                            arsort($new_list);
                                        }

                                        $row_count = 0;

                                        foreach ($new_list as $file) {
                                            $fname = $file['fname'];
                                            $fsize = $file['fsize'];
                                            $fmod = $file['fmod'];
                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                            ?>		
                                            <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                <td class="reportRecords"><a href="<?= $image_path['web_path'] . $fname ?>" title="<?= $fname ?>" class="grouped_elements" rel="[<?= $image_path['web_path'] ?>]"><b><?= $fname ?></a></b></td>
                                                <td class="reportRecords"><b><?= $fsize ?> <?= TEXT_INFO_TITLE_KB ?></b></td>	
                                                <td class="reportRecords"><?= $fmod ?></td>																													
                                                <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'file')) . 'action=del_image_file&file=' . $fname) ?>" onclick="return confirm('<?= TEXT_INFO_NOTICE_DELETE_CONFIRMATION, $fname ?>?')" ><?= tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", 'align="top"') ?></a></td>
                                            </tr>
                                            <?
                                            $row_count++;
                                        }
                                    } else {
                                        $dirlist = tep_get_directory_files($image_path['file_path']);
                                        arsort($dirlist);
                                        $sysDate = time();
                                        $row_count = 0;

                                        foreach ($dirlist as $file) {
                                            $day = 24 * 60 * 60;
                                            $img_lastacc_date = date(DATE_RFC822, $file['lastacc']);
                                            $days_diff = round(($sysDate - strtotime($img_lastacc_date)) / $day); // change to per min for testing
                                            $filetype = tep_get_file_ext($file['name']);

                                            if ($days_diff <= $images_expire_period) {
                                                if ($filetype == 'jpg' || $filetype == 'gif' || $filetype == 'png' || $filetype == 'jpeg') {
                                                    $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                    ?>
                                                    <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                        <td class="reportRecords"><a href="<?= $image_path['web_path'] . $file['name'] ?>" title="<?= $file['name'] ?>" class="grouped_elements" rel="[<?= $image_path['web_path'] ?>]"><b><?= $file['name'] ?></a></b></td>
                                                        <td class="reportRecords"><b><?= round($file['size'] / 1000, 2); ?> <?= TEXT_INFO_TITLE_KB ?></b></td>	
                                                        <td class="reportRecords"><?= $img_lastacc_date ?></td>																													
                                                        <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'file')) . 'action=del_image_file&file=' . $file['name']) ?>" onclick="return confirm('<?= TEXT_INFO_NOTICE_DELETE_CONFIRMATION, $file[name] ?>?')" ><?= tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", 'align="top"') ?></a></td>
                                                    </tr>
                                                    <?
                                                } else {
                                                    $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                    ?>
                                                    <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                        <td class="reportRecords"><a href="<?= $image_path['web_path'] . $file['name'] ?>" title="<?= $file['name'] ?>"><b><?= $file['name'] ?></a></b></td>
                                                        <td class="reportRecords"><b><?= round($file['size'] / 1000, 2); ?> <?= TEXT_INFO_TITLE_KB ?></b></td>	
                                                        <td class="reportRecords"><?= $img_lastacc_date ?></td>																													
                                                        <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'file')) . 'action=del_image_file&file=' . $file['name']) ?>" onclick="return confirm('<?= TEXT_INFO_NOTICE_DELETE_CONFIRMATION, $file[name] ?>?')" ><?= tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", 'align="top"') ?></a></td>
                                                    </tr>
                                                    <?
                                                }
                                            }
                                            $row_count++;
                                        }
                                    }
                                    ?>
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                </table>
                                <?
                                //Old Image Gallery Section
                            } elseif ($gallery_post == 2 && !$aws_flag) {
                                ?>
                                <p><b><?= TEXT_INFO_HEADING_OLD_IMAGES_GALLERY ?></b></p>
                                <table border="0" width="90%" cellspacing="1" cellpadding="2">
                                    <tr>	
                                        <td class="reportBoxHeading"><?= TABLE_HEADING_FILE_NAME ?></td>
                                        <td class="reportBoxHeading" width="15%"><?= TABLE_HEADING_FILE_SIZE ?></td>
                                        <td class="reportBoxHeading" width="20%"><?= TABLE_HEADING_LAST_ACCESS ?></td>
                                        <td class="reportBoxHeading" width="15%"><?= TABLE_HEADING_FILE_DELETE ?></td>         
                                    </tr>
                                    <?
                                    $dirlist = tep_get_directory_files($image_path['file_path']);
                                    arsort($dirlist);
                                    $row_count = 0;

                                    foreach ($dirlist as $file) {
                                        $day = 60 * 60 * 24;
//				$min = 60;
                                        $sysDate = strtotime(date(DATE_RFC822));
                                        $imgDate = strtotime(date(DATE_RFC822, $file['lastacc']));
                                        $days_diff = round(($sysDate - $imgDate) / $day); // change to per min for testing
                                        $filetype = tep_get_file_ext($file['name']);

                                        if ($days_diff > $images_expire_period) {
                                            if ($filetype == 'jpg' || $filetype == 'gif' || $filetype == 'png' || $filetype == 'jpeg') {
                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                ?>
                                                <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">								
                                                    <td class="reportRecords" ><a href="<?= $image_path['web_path'] . $file['name'] ?>" class="single_element" title="<?= $file['name'] ?>"><b><?= $file['name'] ?></a></b> </td>
                                                    <td class="reportRecords"><b><?= round($file['size'] / 1000, 2); ?> <?= TEXT_INFO_TITLE_KB ?></b></td>	
                                                    <td class="reportRecords"><?= date(DATE_RFC822, $file['lastacc']) ?></td>																															
                                                    <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'file')) . 'action=del_image_file&file=' . $file['name']) ?>" onclick="return confirm('<?= TEXT_INFO_NOTICE_DELETE_CONFIRMATION, $file[name] ?>?')" ><?= tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", 'align="top"') ?></a></td>
                                                </tr>
                                                <?
                                            } else {
                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                ?>
                                                <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">								
                                                    <td class="reportRecords" ><a href="<?= $image_path['web_path'] . $file['name'] ?>" title="<?= $file['name'] ?>"><b><?= $file['name'] ?></a></b> </td>
                                                    <td class="reportRecords"><b><?= round($file['size'] / 1000, 2); ?> <?= TEXT_INFO_TITLE_KB ?></b></td>	
                                                    <td class="reportRecords"><?= date(DATE_RFC822, $file['lastacc']) ?></td>																															
                                                    <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action', 'file')) . 'action=del_image_file&file=' . $file['name']) ?>" onclick="return confirm('<?= TEXT_INFO_NOTICE_DELETE_CONFIRMATION, $file[name] ?>?')" ><?= tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", 'align="top"') ?></a></td>
                                                </tr>
                                                <?
                                            }
                                        }
                                        $row_count++;
                                    }
                                    ?>	
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                </table>
                                <?php
                            }
                            ?>
                        </fieldset>
                        <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD) ?>"><?= TEXT_LINK_TITLE_GO_BACK ?></a>
                    </td>
                    <?
//File Upload
                } else if ($action == "upload_image_file" && tep_check_image_upload_permission($file_directory)) {
                    ?>
                    <td width="100%" valign="top">
                        <fieldset style="border: 1px solid #CDCDCD; padding: 8px; padding-bottom:0px; margin: 8px 0">
                            <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('uploader')) . 'uploader=1') ?>"><?= TEXT_LINK_TITLE_SIMPLE_UPLOAD ?></a> |  
                            <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('uploader')) . 'uploader=2') ?>"><?= TEXT_LINK_TITLE_ADVANCED_UPLOAD ?></a> 
                            <legend><strong><?= $image_path['web_path'] ?></strong></legend>
                            <h2><?= TEXT_INFO_HEADING_UPLOAD_TITLE ?></h2>
                            <p><?= TEXT_INFO_NOTICE_MAX_UPLOAD_FILES ?></p>
                            <p><?= TEXT_INFO_NOTICE_MAX_UPLOAD_SIZES ?></p>
                            <p><?= TEXT_INFO_NOTICE_UPLOAD_SUPPORTED_FILE_TYPE ?></p>
                            <?
                            if ($uploader_post == 1) {
                                $radio_value = isset($_GET['cache_control_check']) ? $_GET['cache_control_check'] : '1';

                                echo tep_draw_form('upload_form', FILENAME_IMAGE_UPLOAD, tep_get_all_get_params(array('action')) . 'action=simple_upload', 'post', 'enctype="multipart/form-data"');
                                ?>
                                <table width="500" border="0" cellpadding="0" cellspacing="1" >
                                    <tr>
                                    <tr>
                                        <td>
                                            <?php echo TEXT_CACHE_CONTROL; ?> &nbsp;<input type="radio" name="cache_control_check" value="1" <?php echo ($radio_value == '1') ? 'checked="checked"' : ''; ?> > Yes 
                                            <input type="radio" name="cache_control_check" value="0" <?php echo ($radio_value == '0') ? 'checked="checked"' : ''; ?>>No
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="file" id="ufile" name="ufile[]" size="30" />
                                        </td>
                                        <td>
                                            <input type="submit" name="upload" value="Upload" class="inputButton" onClick="return size_checking();">&nbsp;&nbsp;
                                            <input type="button" name="cancel" value="Cancel" class="inputButton" onClick="jQuery('input:file').MultiFile('reset');">
                                        </td>
                                    <script language="javascript">
                                        <!--
                                            //for basic uploader file size checking on the fly
                                        function size_checking() {
                                            var return_status = false;
                                            var file = document.getElementById('ufile');
                                            if (file == null) {
                                                location.reload();
                                            }

                                            jQuery.each(file.files, function (index) {
                                                var size = file.files[index] != undefined ? file.files[index].fileSize : 0;

                                                if (parseInt(size) > parseInt(<?= $maxsize_limit; ?>)) {
                                                    alert('File size error, please try the other files.');
                                                    location.reload();
                                                } else {
                                                    return_status = true;
                                                }
                                            });

                                            return return_status;
                                            //-->
                                        }
                                    </script>
                                    </tr>
                                    </tr>
                                </table>
                                </form>
                                <?
                            } else if ($uploader_post == 2) {
                                $mass_radio_value = isset($_POST['mass_cache_control_check']) ? $_POST['mass_cache_control_check'] : '1';
                                ?>
                                <div id="message_complete"></div>
                                <div id="filesUploaded"><a id="hiddenclicker" class="uploaded_element"></a></div>
                                <div id="message_all_complete"></div>
                                <div id="upload"><?= TEXT_INFO_NOTICE_JAVASCRIPT_WARNING ?></div>
                                <span id="fileUploadError" class="error-small" style="display:none;"><?= TEXT_INFO_NOTICE_SUPPORTED_TYPE_WARNING ?></span>
                                <table>
                                    <tr>
                                        <td>
                                            <?php echo TEXT_CACHE_CONTROL; ?> &nbsp;<input type="radio" onclick="changeScriptData()"  name="mass_cache_control_check"  value="1" <?php echo ($mass_radio_value == '1') ? 'checked="checked"' : ''; ?> > Yes 
                                            <input type="radio" onclick="changeScriptData()" name="mass_cache_control_check"  value="0" <?php echo ($mass_radio_value == '0') ? 'checked="checked"' : ''; ?>>No
                                        </td>
                                    </tr>
                                </table>
                                <a href="javascript:jQuery('#upload').uploadifyUpload()"><?= TEXT_LINK_TITLE_START_UPLOAD ?></a> |  
                                <a href="javascript:jQuery('#upload').uploadifyClearQueue()"><?= TEXT_LINK_TITLE_CLEAR_QUEUE ?></a>
                                <p></p>
                                <?
                            }
                            ?>
                        </fieldset>
                        <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD) ?>"><?= TEXT_LINK_TITLE_GO_BACK ?></a>
                    </td>
                    <?
//Directories Listing
                } else {
                    ?>
                    <td width="100%" valign="top">
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                <td><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                            </tr>
                        </table>
                        <table border="0" width="90%" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="reportBoxHeading" ><?= TABLE_HEADING_DIRECTORIES_URL ?></td>
                                <td class="reportBoxHeading" width="30%"><?= TABLE_HEADING_SECTION ?></td>
                                <td class="reportBoxHeading" width="15%"><?= TABLE_HEADING_ACTION ?></td>
                            </tr>          
                            <?
                            $row_count = 0;
                            $image_configuration_select_sql = "	SELECT image_category, file_path, web_path
										FROM " . TABLE_IMAGE_CONFIGURATION . " 
										WHERE FIND_IN_SET( '" . $_SESSION['login_groups_id'] . "', user_groups)";
                            $image_configuration_result_sql = tep_db_query($image_configuration_select_sql);
                            while ($image_configuration_row = tep_db_fetch_array($image_configuration_result_sql)) {
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                ?>
                                <tr height="28" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                    <td class="reportRecords"><b><?= $image_configuration_row['web_path'] ?></b> </td>
                                    <td class="reportRecords"><?= $image_configuration_row['image_category'] ?></td>																															
                                    <td><a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, 'action=upload_image_file&dir=' . $image_configuration_row['image_category']) ?>"><?= tep_image(DIR_WS_ICONS . "file_upload.gif", TEXT_INFO_NOTICE_UPLOAD_TO_THIS_DIRECTORY, "", "", 'align="top"') ?></a>
                                        <a href="<?= tep_href_link(FILENAME_IMAGE_UPLOAD, 'action=edit_image&dir=' . $image_configuration_row['image_category'] . '&gallery=1') ?>"><?= tep_image(DIR_WS_ICONS . "edit.gif", TEXT_INFO_NOTICE_MAINTAIN_IMAGES_IN_THIS_DIRECTORY, "", "", 'align="top"') ?></a>   
                                    </td>
                                </tr>
                                <?
                                $row_count++;
                            }
                            ?>
                        </table>
                    </td>
                    <?
                }
                ?>
            </tr>
        </table>	
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <br>
    </body>
</html>
<script type="text/javascript">

    var img_conf_obj = {'FILENAME_IMAGE_UPLOAD': '<?php echo FILENAME_IMAGE_UPLOAD ?>',
        'gallery': '<?php echo $gallery_post ?>',
        'dir': '<?php echo $file_directory ?>',
        'mass_upload_cache': jQuery('input[name="mass_cache_control_check"]:checked').val(),
        'TEXT_INFO_NOTICE_DELETE_CONFIRMATION': '<?php echo TEXT_INFO_NOTICE_DELETE_CONFIRMATION ?>',
        'img_del': '<?php echo tep_image(DIR_WS_ICONS . "delete.gif", TEXT_INFO_NOTICE_CONFIRM_DELETE, "", "", "align=\'top\'") ?>',
        'web_path': '<?php echo $image_path["web_path"] ?>',
        'TEXT_INFO_HEADING_UPLOADED_FILES_TITLE': '<?php echo TEXT_INFO_HEADING_UPLOADED_FILES_TITLE ?>',
        'TEXT_INFO_NOTICE_FILE_UPLOAD_SUCCESSFUL': '<?php echo TEXT_INFO_NOTICE_FILE_UPLOAD_SUCCESSFUL ?>',
        'REF': '<?php echo $_SERVER["HTTP_REFERER"] ?>'
    };
    function changeScriptData() {
        jQuery('#upload').uploadifySettings("scriptData", {'mass_upload_cache': jQuery('input[name="mass_cache_control_check"]:checked').val()});
    }

</script>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>