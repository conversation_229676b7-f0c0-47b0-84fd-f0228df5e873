<?
require('includes/application_top.php');

$action = tep_db_prepare_input($_GET['action']);
$directory = tep_db_prepare_input($_GET['dir']);

if (tep_not_null($action)) {
	switch ($action) {
		case "update":
			$combined_user_group = array();
			$img_conf_sql_data_array = array( 'user_groups' => (isset($_POST['user_grp_to']) && count($_POST['user_grp_to']) ? implode(",", $_POST['user_grp_to']) : '') );			
			tep_db_perform(TABLE_IMAGE_CONFIGURATION, $img_conf_sql_data_array, 'update', " image_category = '" . $directory . "'");
			
			tep_redirect(tep_href_link(FILENAME_IMAGE_UPLOAD_CONF));
			break;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
					</tr>
					<tr>
						<td>
<?
	if ($action == 'edit') {
		$user_groups_from_array = array( array ('id' => '', "text" => 'Select User Groups', "type" => 'optgroup') );
		$user_groups_to_array = array( array ('id' => '', "text" => 'Selected User Groups', "type" => 'optgroup') );

		$store_select_sql = " 	SELECT user_groups 
								FROM " . TABLE_IMAGE_CONFIGURATION . "
								WHERE image_category = '". $directory ."'";
		$store_result_sql = tep_db_query($store_select_sql);
		if ($store_row = tep_db_fetch_array($store_result_sql)) {
			if (tep_not_null($store_row['user_groups'])) {
				$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id NOT IN (".$store_row['user_groups'].") ORDER BY admin_groups_name";
				$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
				while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
					$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
														'text' => $unassigned_user_row['admin_groups_name']
														);
				}
				$assigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_row['user_groups'].") ORDER BY admin_groups_name";
				$assigned_user_result_sql = tep_db_query($assigned_user_select_sql);
				while ($assigned_user_row = tep_db_fetch_array($assigned_user_result_sql)) {
					$user_groups_to_array[] = array(	'id' => $assigned_user_row['admin_groups_id'],
														'text' => $assigned_user_row['admin_groups_name']
														);
				}
			} else {
				$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " ORDER BY admin_groups_name";
				$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
				while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
					$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
														'text' => $unassigned_user_row['admin_groups_name']
														);
				}
			}
					echo tep_draw_form('image_upload_conf_form', FILENAME_IMAGE_UPLOAD_CONF, tep_get_all_get_params(array('action')) . 'action=update', 'post', 'onSubmit="return form_checking();"');
?>
						<table width="100%" border="0" cellspacing="2" cellpadding="1">
							<tr>
								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td class="main" valign="top"><?=TABLE_HEADING_IMAGE_DIRECTORIES?></td>
								<td class="main" valign="top"><?=$directory?></td>
							</tr>
							<tr>
								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							</tr>
							<tr>
								<td class="main" valign="top"><?=TABLE_HEADING_USER_GROUPS?></td>
								<td class="main" valign="top"><?=tep_draw_js_select_boxes('user_grp', $user_groups_from_array, $user_groups_to_array, ' size="10" style="width:20em;"')?></td>
							</tr>
							<tr>
								<td colspan="2" align="right">
									<input type="button" name="update" value="Update" class="inputButton" onClick="return button_lock();">&nbsp;&nbsp;
            						<input type="button" name="cancel" value="Cancel" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_IMAGE_UPLOAD_CONF)?>'">
								</td>
								<script language="javascript">
									<!--
									function form_checking() {
										var selected_grp = document.image_upload_conf_form.elements['user_grp_to[]'];
										if (selected_grp != null) {
											for (x=0; x<(selected_grp.length); x++) {
						    					selected_grp.options[x].selected = true;
						  					}
					  					}
					  				}
					  				
					  				function button_lock() {
					  					document.image_upload_conf_form.update.disabled = true;
					  					form_checking();
					  					document.image_upload_conf_form.submit();
						  			}
				  					//-->
								</script>
							</tr>
						</table>
					</form>
<?
		} else {
			echo 'Record Not Found!';
		}
	} else {
?>	
							<table border="0" width="48%" cellspacing="1" cellpadding="3">
								<tr>
									<td class="ordersBoxHeading"><?=TABLE_HEADING_IMAGE_DIRECTORIES?></td>
									<td class="ordersBoxHeading"><?=TABLE_HEADING_USER_GROUPS?></td>
									<td class="ordersBoxHeading" align="center" width="8%"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?
		$row_count = 0;
		$store_info_select_sql = "	SELECT image_category, user_groups 
									FROM " . TABLE_IMAGE_CONFIGURATION;
		$store_info_result_sql = tep_db_query($store_info_select_sql);
		
		while ($store_info_row = tep_db_fetch_array($store_info_result_sql)) {
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
			$user_group_string_array = array();
			if (tep_not_null($store_info_row['user_groups'])) {
				$user_group_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$store_info_row['user_groups'].")";
				//echo $user_group_select_sql;
				$user_group_result_sql = tep_db_query($user_group_select_sql);
				while ($user_group_row = tep_db_fetch_array($user_group_result_sql)) {
					$user_group_string_array[] = $user_group_row['admin_groups_name'];
				}
			}
?>
					<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')";>
						<td class="reportRecords" valign="top"><?=$store_info_row['image_category']?></td>
						<td class="reportRecords" valign="top"><?=implode('<br>', $user_group_string_array)?></td>
						<td class="reportRecords" align="center" valign="top">
							<a href="<?=tep_href_link(FILENAME_IMAGE_UPLOAD_CONF, 'action=edit&dir=' . $store_info_row['image_category'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
						</td>
					</tr>
<?	
			$row_count++;
		}

	}
?>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>&nbsp;</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>