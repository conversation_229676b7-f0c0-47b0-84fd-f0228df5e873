<?php

if (isset($_POST['SID']) && isset($_POST['REF'])) {
    session_id($_POST['SID']);
}

require('includes/application_top.php');
require('includes/classes/ogm_amazon_ws.php');
require(DIR_WS_FUNCTIONS . 'image_upload.php');
define('SEARCH_KEY_IMAGE_CATEGORY', 'image_category');

$action = tep_db_prepare_input($_GET['action']);

$s3 = new ogm_amazon_ws();
$aws_flag = $s3->is_aws_s3_enabled();
if ($aws_flag) {
    $s3->set_acl('ACL_PUBLIC');
    $s3->set_storage('STORAGE_STANDARD');
}

$file_path = array();

if (isset($_SERVER['HTTP_REFERER']) || isset($_POST['REF'])) {
    if (tep_not_null($_POST['folder']) || tep_not_null($_POST['dir'])) {
        if (tep_check_image_upload_permission($_POST['folder']) || tep_check_image_upload_permission($_POST['dir'])) {
            if ($action == 'validate_file') {
                $fileArray = array();
                if ($aws_flag) {
//					$file_path = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY,$_POST['folder']);
//				
//					// Ask to replace to file 
//					foreach ($_POST as $key => $value) {
//						if ($key != 'folder') {
//							$exists = $s3->s3_api(array('method'=>'if_object_exists',$file_path['aws_s3_info']->{'bucket'},$file_path['aws_s3_info']->{'path'}.$value));
//							if ($exists) {
//								$fileArray[$key] = $value;
//							}
//						}
//					}
//					echo json_encode($fileArray);
                } else {
                    //call full path frm dir
                    $file_path = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY, $_POST['folder']);

                    if (tep_not_null($file_path)) {
                        foreach ($_POST as $key => $value) {
                            if ($key != 'folder') {
                                if (file_exists($file_path['file_path'] . $value)) {
                                    $fileArray[$key] = $value;
                                }
                            }
                        }
                    }
                    //file replace checker
                    echo json_encode($fileArray);
                }
            } else if ($action == 'do_upload') {
                // dir permission check
                $file_path = tep_get_image_configuration(SEARCH_KEY_IMAGE_CATEGORY, $_POST['dir']);

                if (tep_not_null($file_path)) {
                    if (tep_not_null($_FILES)) {
                        $maxsize_limit = 5 * 1024 * 1024; //5MB
                        $tempFile = $_FILES['Filedata']['tmp_name'];
                        $fileError = $_FILES['Filedata']['error'];
                        $fileParts = pathinfo($_FILES['Filedata']['name']);
                        $fileSize = $_FILES['Filedata']['size'];
                        $fileParts_ext = strtolower($fileParts['extension']);
                        $im = null;

                        //backend validation
                        if ($fileParts_ext == 'jpg' || $fileParts_ext == 'jpeg' || $fileParts_ext == 'gif' || $fileParts_ext == 'png') {
                            if (tep_not_null($tempFile)) {//prevent file > 2MB which PHP server don't allow but Uploader permitted to go thru
                                $im = tep_verify_file_mime($tempFile); //check image content
                            }
                        }

                        if ($im || $fileParts_ext == 'swf' || $fileParts_ext == 'flv') {//check filetype
                            if ($fileSize <= $maxsize_limit) {//check file size
                                if ($aws_flag) {
                                    $targetFilePath = $file_path['aws_s3_info']->{'path'};
                                } else {
                                    $targetFilePath = $file_path['file_path'];
                                }

                                //Uncomment the following line if you want to make the directory if it doesn't exist
                                //mkdir(str_replace('//','/',$targetPath), 0755, true);
                                $cache_control_flag = isset($_POST['mass_upload_cache']) && $_POST['mass_upload_cache'] === '1';

                                if ($cache_control_flag) {
                                    $filePathInfo = pathinfo($_FILES['Filedata']['name']);
                                    $targetFilename = strtolower($filePathInfo['filename']) . '-' . date('Ymd-His') . '.' . $filePathInfo['extension'];
                                } else {
                                    $targetFilename = strtolower($_FILES['Filedata']['name']);
                                }


                                if ($aws_flag) {
                                    $s3->set_bucket_key($file_path['aws_s3_info']->{'bucket'});
                                    $s3->set_file($_FILES['Filedata']);
                                    $s3->set_filepath($targetFilePath);
                                    $s3->set_filename($targetFilename);
                                    if ($cache_control_flag) {
                                        $s3->set_headers(array('Cache-Control' => 'max-age=' . $s3->default_cache_control_max_age));
                                    } else {
                                        $s3->set_headers(null);
                                    }
                                     $s3->save_file();
                                } else {
                                    move_uploaded_file($tempFile, $targetFilePath . $targetFilename);
                                }
                                //echo str_replace($_SERVER['DOCUMENT_ROOT'],'',$targetFile);
                                /*
                                  $myFile = "testFile.txt";
                                  $fh = fopen($myFile, 'w') or die("can't open file");
                                  $stringData = $fileParts_ext;
                                  fwrite($fh, $stringData);
                                  fclose($fh);
                                 */
                            } elseif ($fileSize > $maxsize_limit) {
                                $error = "Invalid file size. Your uploaded file was deleted!\n";
                                echo $error;
                            }
                        } else {
                            $error = "Invalid file type. Your uploaded file was deleted!\n";
                            echo $error;
                        }

                        switch ($fileError) {
                            case 0:
                                // $msg = ""; // comment this out if you don't want a message to appear on success.
                                break;
                            case 1:
                                $msg = "The file is bigger than this PHP installation allows";
                                break;
                            case 2:
                                $msg = "The file is bigger than this form allows";
                                break;
                            case 3:
                                $msg = "Only part of the file was uploaded";
                                break;
                            case 4:
                                $msg = "No file was uploaded";
                                break;
                            case 6:
                                $msg = "Missing a temporary folder";
                                break;
                            case 7:
                                $msg = "Failed to write file to disk";
                                break;
                            case 8:
                                $msg = "File upload stopped by extension";
                                break;
                            default:
                                $msg = "unknown error " . $_FILES['Filedata']['error'];
                                break;
                        }

                        if ($msg) {
                            $stringData = "Error Info: " . $msg;
                        } else {
                            $stringData = 1;
                        } // This is required for onComplete to fire on Mac OSX

                        echo $stringData . (isset($targetFilename) ? '#' . $targetFilename : '');
                    }
                }
            }
        } else {
            echo 'No permission to upload.';
        }
    }
} else {
    echo "You are not allowed to access from outside";
}
?>