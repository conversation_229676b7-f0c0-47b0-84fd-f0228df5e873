<?
/*
  	$Id: account_check.js.php,v 1.5 2007/06/18 03:17:54 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
?>

<?
if (substr(basename($PHP_SELF), 0, 12) == 'admin_member') {
?>

<script language="JavaScript" type="text/JavaScript">
<!--
	function validateForm() { 
  		var p,z,xEmail,errors='',dbEmail,result=0,i;
		
  		var adminName1 = document.newmember.admin_firstname.value;
  		var adminName2 = document.newmember.admin_lastname.value;
  		var adminEmail = document.newmember.admin_email_address.value;
  		
  		if (adminName1 == '') { 
    		errors+='<?php echo JS_ALERT_FIRSTNAME; ?>';
  		} else if (adminName1.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) { 
    		errors+='- Firstname length must over  <?php echo (ENTRY_FIRST_NAME_MIN_LENGTH); ?>\n';
  		}
		
  		if (adminName2 == '') { 
    		errors+='<?php echo JS_ALERT_LASTNAME; ?>';
  		} else if (adminName2.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) { 
    		errors+='- Lastname length must over  <?php echo (ENTRY_LAST_NAME_MIN_LENGTH);  ?>\n';
  		}
		
  		if (adminEmail == '') {
    		errors+='<?php echo JS_ALERT_EMAIL; ?>';
  		} else if (adminEmail.indexOf("@") <= 1 || adminEmail.indexOf("@") >= (adminEmail.length - 3) || adminEmail.indexOf("@.") >= 0 ) {
    		errors+='<?php echo JS_ALERT_EMAIL_FORMAT; ?>';
  		} else if (adminEmail.length < <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>) {
    		errors+='<?php echo JS_ALERT_EMAIL_FORMAT; ?>';
  		}
		
  		if (errors) alert('The following error(s) occurred:\n'+errors);
  		document.returnValue = (errors == '');
	}
	
	function checkGroups(obj) {
  		var subgroupID, subGroupActionID, i;
  		subgroupID = eval("document.defineForm.subgroups_"+parseFloat((obj.id).substring(7)));
    	
  		if (subgroupID.length > 0) {
    		for (i=0; i<subgroupID.length; i++) {
      			if (obj.checked == true) { subgroupID[i].checked = true; }
      			else { subgroupID[i].checked = false; }
      			
      			initializeAction(subgroupID[i], i);
    		}
  		} else {
    		if (obj.checked == true) { subgroupID.checked = true; }
    		else { subgroupID.checked = false; }
    		initializeAction(subgroupID, 'null');
  		}
	}
	
	function checkSub(obj, initialize) {
  		var groupID, subgroupID, i, num=0;
  		groupID = eval("document.defineForm.groups_"+parseFloat((obj.id).substring(10)));
  		subgroupID = eval("document.defineForm."+(obj.id));
      	
  		if (subgroupID.length > 0) {    
    		for (i=0; i < subgroupID.length; i++) {
      			if (subgroupID[i].checked == true) num++;
      			if (subgroupID[i].value == obj.value) {
      				if (initialize) { initializeAction(subgroupID[i], i); }
      			}
    		}
  		} else {
    		if (subgroupID.checked == true) num++;
    		if (initialize)	{ initializeAction(subgroupID, 'null');}
  		}
  		
  		if (num>0) { groupID.checked = true; }
  		else { groupID.checked = false; }
	}
	
	function checkAction(obj, nodeIndex, parentID) {
		var subGroupID, subGroupActionID, i, num=0;
		
  		subGroupID = eval("document.defineForm.subgroups_"+parentID);
  		subGroupActionID = eval("document.defineForm."+(obj.id));
      	
  		if (subGroupActionID.length > 0) {
    		for (i=0; i < subGroupActionID.length; i++) {
      			if (subGroupActionID[i].checked == true) num++;
    		}
  		} else {
    		if (subGroupActionID.checked == true) num++;
  		}
  		
  		if (nodeIndex == 0) {
  			if (typeof(subGroupID[nodeIndex]) != 'undefined')
  				var curSubGroup = subGroupID[nodeIndex];
  			else
  				var curSubGroup = subGroupID;
  		} else {
  			var curSubGroup = subGroupID[nodeIndex];
  		}
  		
  		if (num > 0) {
  			curSubGroup.checked = true;
  		}
  		
  		checkSub(curSubGroup, false);
	}
	
	function initializeAction(node, node_count) {
		var subGroupID, subGroupActionID, i, num=0;
		subGroupID = node_count != 'null' ? eval("document.defineForm."+(node.id)+'['+node_count+']') : eval("document.defineForm."+(node.id));
  		subGroupActionID = eval("document.defineForm.subgroups_actions"+parseFloat(node.value));
      	
      	if (typeof(subGroupActionID) != 'undefined') {
	  		if (subGroupActionID.length > 0) {
	    		for (i=0; i < subGroupActionID.length; i++) {
	    			if (subGroupID.checked == true) { subGroupActionID[i].checked = true; }
	      			else { subGroupActionID[i].checked = false; }
	    		}
	  		} else {
	  			if (subGroupID.checked == true) { subGroupActionID.checked = true; }
	    		else { subGroupActionID.checked = false; }
	  		}
	  	}
	}
	//-->
</script>
<?
} else {
?>
<script language="JavaScript" type="text/JavaScript">
<!--
	function validateForm() {
  		var p,z,xEmail,errors='',dbEmail,result=0,i;
		
  		var adminName1 = document.account.admin_firstname.value;
  		var adminName2 = document.account.admin_lastname.value;
  		var adminEmail = document.account.admin_email_address.value;
  		var adminPass1 = document.account.admin_password.value;
  		var adminPass2 = document.account.admin_password_confirm.value;
  		
  		if (adminName1 == '') { 
    		errors+='<?php echo JS_ALERT_FIRSTNAME; ?>';
  		} else if (adminName1.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) { 
    		errors+='<?php echo JS_ALERT_FIRSTNAME_LENGTH . ENTRY_FIRST_NAME_MIN_LENGTH; ?>\n';
  		}
		
  		if (adminName2 == '') { 
    		errors+='<?php echo JS_ALERT_LASTNAME; ?>';
  		} else if (adminName2.length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) { 
    		errors+='<?php echo JS_ALERT_LASTNAME_LENGTH . ENTRY_LAST_NAME_MIN_LENGTH;  ?>\n';
  		}
		
  		if (adminEmail == '') {
    		errors+='<?php echo JS_ALERT_EMAIL; ?>';
  		} else if (adminEmail.indexOf("@") <= 1 || adminEmail.indexOf("@") >= (adminEmail.length - 3) || adminEmail.indexOf("@.") >= 0 ) {
    		errors+='<?php echo JS_ALERT_EMAIL_FORMAT; ?>';
  		} else if (adminEmail.length < <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>) {
    		errors+='<?php echo JS_ALERT_EMAIL_FORMAT; ?>';
  		}
  		
  		if (adminPass1 == '') { 
    		errors+='<?php echo JS_ALERT_PASSWORD; ?>';
  		} else if (adminPass1.length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>) { 
    		errors+='<?php echo JS_ALERT_PASSWORD_LENGTH . ENTRY_PASSWORD_MIN_LENGTH; ?>\n';
  		} else if (adminPass1 != adminPass2) {
    		errors+='<?php echo JS_ALERT_PASSWORD_CONFIRM; ?>';
  		}
  		
  		if (errors) alert('The following error(s) occurred:\n'+errors);
  		document.returnValue = (errors == '');
	}
//-->
</script>
<?
}
?>