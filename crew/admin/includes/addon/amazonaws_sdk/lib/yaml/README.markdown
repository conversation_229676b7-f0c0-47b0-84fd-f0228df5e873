Symfony YAML: A PHP library that speaks YAML
============================================

Symfony YAML is a PHP library that parses YAML strings and converts them to
PHP arrays. It can also converts PHP arrays to YAML strings. Its official
website is at http://components.symfony-project.org/yaml/.

The documentation is to be found in the `doc/` directory.

Symfony YAML is licensed under the MIT license (see LICENSE file).

The Symfony YAML library is developed and maintained by the
[symfony](http://www.symfony-project.org/) project team. It has been extracted
from symfony to be used as a standalone library. Symfony YAML is part of the
[symfony components project](http://components.symfony-project.org/).
