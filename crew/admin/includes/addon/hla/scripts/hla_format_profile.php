<?php

include_once('../../../configure.php');

if (!isset($_SERVER['argv'])) {
	exit;
} else {
	$char_id = $_SERVER['argv'][1];
	
	define('DIR_FS_HLA_PROFILE', DIR_FS_HLA . 'hla/profile/');

	$unfilter_file_path = DIR_FS_HLA_PROFILE . $char_id.'.html.lock';
	$filtered_file_path = DIR_FS_HLA_PROFILE . $char_id.'.html';
	$filtered_content =  array();
	$filtered_content_text = '';
	
	if (file_exists($unfilter_file_path)) {
		$handle = fopen($unfilter_file_path, "r");
		$contents = fread($handle, filesize($unfilter_file_path));
		fclose($handle);
		
		// Filter UNWANTED STRING
		$patterns = array();
		$patterns[0] = '/\s\s+/'; // Filter Extra White Space
		$patterns[1] = '/(?:<div id="productmethod" class="items-moreright">)(.*?)(<\/div>([\s]+)<div class="spec">)/is'; // Filter UNWANTED STRING
		$patterns[2] = '/(showProductForms\(\))/'; // Filter UNWANTED STRING
		$patterns[3] = '/(hideProductForms\(\))/'; // Filter UNWANTED STRING
		
		$replacements = array();
		$replacements[0] = ' ';
		$replacements[1] = '<div class="spec">';
		$replacements[2] = '';
		$replacements[3] = '';
		
		$contents = preg_replace($patterns, $replacements, trim($contents));
		
		$pattern = '/(?:<div id="productmethod" class="items-moreright">)(.*?)(<\/div>([\s]+)<div class="spec">)/is';
		$contents = preg_replace($pattern, '<div class="spec">', $contents);
		
		// For the IE6 issue: cannot view the left and right listing items
		$replace = '<span><img width="1" height="1" src="images/pixel.gif" alt=""></span><div class="items-right">';
		$contents = str_replace('<div class="items-right">', $replace, $contents);
		
		// Getting the MAIN PROFILE STRING
		$pattern = '/(?:profile-master">)(.*)(<\/div>([\s]+)<div class="tooltip" id="tooltipcontainer")/is';
		preg_match($pattern,$contents,$matches);
		$filtered_content[] = $matches[1];
		
		// Getting the initialize js variable PART 1
		$pattern = '/(?:<script type="text\/javascript">([\s]+)var theClassId =)(.*?)(<\/script>)/is';
		preg_match($pattern,$contents,$matches);
		$filtered_content[] = $matches[0];
		
		
		// Getting the initialize js variable PART 2
		$pattern = '/(?:<script type="text\/javascript">([\s]+)function strengthObject())(.*?)(<\/script>)/is';
		preg_match($pattern,$contents,$matches);
		$filtered_content[] = $matches[0];
		
		$filtered_content_text = serialize($filtered_content);
		
		if (trim($filtered_content_text) != '') {
			$fp = fopen($filtered_file_path, 'w');
			fwrite($fp, $filtered_content_text);
			fclose($fp);
			
			unlink($unfilter_file_path);
		}
	}
}
?>