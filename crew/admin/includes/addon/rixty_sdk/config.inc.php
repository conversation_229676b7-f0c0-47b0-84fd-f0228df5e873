<?php if (!class_exists('rixty_api')) die('No direct access allowed.');

define('RIXTY_API_MODE', 'development');

switch (RIXTY_API_MODE) {
    case 'production':
        define('RIXTY_API_URL', 'https://partner.rixty.com/rixtyapi/nvp');
        define('RIXTY_API_SIGNATURE', 'AC6KCDDR724C9G4QLDEHQC7CM3XALDAT');
        break;
    case 'development':
        define('RIXTY_API_URL', 'https://sandbox.rixty.com/rixtyapi/nvp');
        define('RIXTY_API_SIGNATURE', 'AC6KCDDR724C9G4QLDEHQC7CM3XALDAT');
        break;
}