<?php
/*
  	$Id: application_top.php,v 1.21 2015/06/15 10:48:06 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

// Start the clock for the page parse time log
define('PAGE_PARSE_START_TIME', microtime());

// Set the level of error reporting
error_reporting(E_ALL & ~E_NOTICE & ~E_STRICT);

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

// Check if register_globals is enabled.
// Since this is a temporary measure this message is hardcoded. The requirement will be removed before 2.2 is finalized.
//if (function_exists('ini_get')) {
//	ini_get('register_globals') or exit('FATAL ERROR: register_globals is disabled in php.ini, please enable it!');
//}

// Set the local configuration parameters - mainly for developers
if (file_exists('includes/local/configure.php')) include('includes/local/configure.php');

// Include application configuration parameters
require('includes/configure.php');

// Define the project version
define('PROJECT_VERSION', 'osCommerce 2.2-MS2');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
//$PHP_SELF = (isset($HTTP_SERVER_VARS['PHP_SELF']) ? $HTTP_SERVER_VARS['PHP_SELF'] : $HTTP_SERVER_VARS['SCRIPT_NAME']);
$PHP_SELF = $HTTP_SERVER_VARS['SCRIPT_NAME'];

$Sep = (strstr(DIR_FS_DOCUMENT_ROOT, ":/")?";":":") ;
$GLOBALS["_PEAR_Path_"] = DIR_FS_DOCUMENT_ROOT . "pear" ;
ini_set("include_path", $GLOBALS["_PEAR_Path_"]. $Sep.".".$Sep .$_SERVER['DOCUMENT_ROOT']) ;

// Used in the "Backup Manager" to compress backups
define('LOCAL_EXE_GZIP', '/usr/bin/gzip');
define('LOCAL_EXE_GUNZIP', '/usr/bin/gunzip');
define('LOCAL_EXE_ZIP', '/usr/local/bin/zip');
define('LOCAL_EXE_UNZIP', '/usr/local/bin/unzip');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// customization for the design layout
define('BOX_WIDTH', 160); // how wide the boxes should be in pixels (default: 125)

// Define how do we update currency exchange rates
// Possible values are 'oanda' 'xe' or ''
define('CURRENCY_SERVER_PRIMARY', 'xe');
define('CURRENCY_SERVER_BACKUP', 'oanda');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// storing the total time taken by all the mysql queries
$mysql_query_time = 0;

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

// define our general functions used application-wide
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');
//Admin begin
require(DIR_WS_FUNCTIONS . 'password_funcs.php');
//Admin end

// Get particular categories configuration setting(s)
require(DIR_WS_FUNCTIONS . 'configuration.php');

// set the cookie domain
$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);

// initialize the logger class
require(DIR_WS_CLASSES . 'logger.php');

// include shopping cart class
require(DIR_WS_CLASSES . 'shopping_cart.php');

// include products low stock class
require(DIR_WS_CLASSES . 'products_low_stock.php');

// some code to solve compatibility issues
//require(DIR_WS_FUNCTIONS . 'compatibility.php');

// check to see if php implemented session management functions - if not, include php3/php4 compatible session class
if (!function_exists('session_start')) {
	define('PHP_SESSION_NAME', 'osCAdminID');
    define('PHP_SESSION_PATH', '/');
    define('PHP_SESSION_SAVE_PATH', SESSION_WRITE_DIRECTORY);
	
    include(DIR_WS_CLASSES . 'sessions.php');
}

// define how the session functions will be used
require(DIR_WS_FUNCTIONS . 'sessions.php');

// set the session name and save path
tep_session_name('osCAdminID');
tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
if (function_exists('session_set_cookie_params')) {
	//session_set_cookie_params(0, DIR_WS_ADMIN);
	session_set_cookie_params(0, $cookie_path, $cookie_domain);
} elseif (function_exists('ini_set')) {
	ini_set('session.cookie_lifetime', '0');
    //ini_set('session.cookie_path', DIR_WS_ADMIN);
    ini_set('session.cookie_path', $cookie_path);
    ini_set('session.cookie_domain', $cookie_domain);
}

// lets start our session
tep_session_start();

require(DIR_WS_FUNCTIONS . 'compatibility.php');

// set the language
if (!isset($_SESSION['language']) || isset($HTTP_GET_VARS['language'])) {
//	if (!tep_session_is_registered('language')) {
//    	tep_session_register('language');
//      	tep_session_register('languages_id');
//	}
	
    include(DIR_WS_CLASSES . 'language.php');
    $lng = new language();
	
    if (isset($HTTP_GET_VARS['language']) && tep_not_null($HTTP_GET_VARS['language'])) {
    	$lng->set_language($HTTP_GET_VARS['language']);
    } else {
      	$lng->get_browser_language();
    }
	
    $language = $_SESSION['language'] = $lng->language['directory'];
    $languages_id = $_SESSION['languages_id'] = $lng->language['id'];
} else {
    $language = $_SESSION['language'];
    $languages_id = $_SESSION['languages_id'];
}

// include the language translations
if ($_SESSION['language'] === 'english') {
    require(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php');
    $current_page = basename($PHP_SELF);
    if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . $current_page)) {
            include(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . $current_page);
    }
} else {
    die('Error: Your browser does not support english.');
}

$_SESSION['default_languages_id'] = 1;

// define our localization functions
require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
require(DIR_WS_CLASSES . 'table_block.php');
require(DIR_WS_CLASSES . 'box.php');

// initialize the message stack for output messages
require(DIR_WS_CLASSES . 'message_stack.php');
$messageStack = new messageStack;

// split-page-results
require(DIR_WS_CLASSES . 'split_page_results.php');

// entry/item info classes
require(DIR_WS_CLASSES . 'object_info.php');

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

// file uploading class
require(DIR_WS_CLASSES . 'upload.php');

// calculate category path
if ($_REQUEST["subaction"] == "goto_cat") {	// drop-down menu from categories.php
	$cPath = tep_get_path($_REQUEST["cat_id"], false);	// do not return in value pair format
} else {
	if (isset($HTTP_GET_VARS['cPath'])) {
		$cPath = $HTTP_GET_VARS['cPath'];
	} else if ($_REQUEST['cPath']) {
		$cPath = $_REQUEST['cPath'];
	} else {
		$cPath = '';
	}
}

if (tep_not_null($cPath)) {
	$cPath_array = tep_parse_category_path($cPath);
    $cPath = implode('_', $cPath_array);
    $current_category_id = $cPath_array[(sizeof($cPath_array)-1)];
} else {
	$current_category_id = 0;
}

if (substr(STORE_VERSION, 0, 2) != '1.') {
	$cat_conf_array = array();
	$categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id=0; ");
	while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
		$cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
	}
	
	$cat_path_array = array();
	if (isset($cID) && $cID > 0) {
		$cat_path_array = explode('_', tep_get_particular_cat_path($cID));
	} else if (isset($current_category_id) && $current_category_id > 0) {
		$cat_path_array = $cPath_array;
	}
	
	if (count($cat_path_array)) {
		for ($i=0; $i < count($cat_path_array); $i++) {
			$categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id='".$cat_path_array[$i]."'; ");
			while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
				$cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
			}
		}
	}
	
	foreach ($cat_conf_array as $cfgKey => $cfgValue) {
		define($cfgKey, $cfgValue);
	}
}

// default open navigation box
if (!isset($_SESSION['selected_box'])) {
	$selected_box = $_SESSION['selected_box'] = 'configuration';
}

if (isset($_GET['selected_box'])) {
	$selected_box = $_SESSION['selected_box'] = $_GET['selected_box'];
} else {
    $selected_box = $_SESSION['selected_box'];
}

// the following cache blocks are used in the Tools->Cache section
// ('language' in the filename is automatically replaced by available languages)
$cache_blocks = array(	array('title' => TEXT_CACHE_CATEGORIES, 'code' => 'categories', 'file' => 'categories_box-language.cache', 'multiple' => true),
						array('title' => TEXT_CACHE_MANUFACTURERS, 'code' => 'manufacturers', 'file' => 'manufacturers_box-language.cache', 'multiple' => true),
                        array('title' => TEXT_CACHE_ALSO_PURCHASED, 'code' => 'also_purchased', 'file' => 'also_purchased-language.cache', 'multiple' => true)
					);

// check if a default currency is set
if (!defined('DEFAULT_CURRENCY')) {
	$messageStack->add(ERROR_NO_DEFAULT_CURRENCY_DEFINED, 'error');
}

// check if a default language is set
if (!defined('DEFAULT_LANGUAGE')) {
	$messageStack->add(ERROR_NO_DEFAULT_LANGUAGE_DEFINED, 'error');
}

if (function_exists('ini_get') && ((bool)ini_get('file_uploads') == false) ) {
	$messageStack->add(WARNING_FILE_UPLOADS_DISABLED, 'warning');
}
//Admin begin
if (basename($PHP_SELF) != FILENAME_SUPPLIER_ACTIVATE && basename($PHP_SELF) != FILENAME_LOGIN && basename($PHP_SELF) != FILENAME_PASSWORD_FORGOTTEN) { 
	tep_admin_check_login(); 
}

if (isset($_SESSION['login_id'])) {
    $login_id = $_SESSION['login_id'];
    $login_groups_id = $_SESSION['login_groups_id'];
    $login_first_name = $_SESSION['login_first_name'];
    $login_email_address = $_SESSION['login_email_address'];
}

//Admin end
// Include OSC-AFFILIATE
require('includes/affiliate_application_top.php');
// include giftvoucher
require(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');

$const_alluseraccess = "0";


// BROWSER AND VERSION
// (must check everything else before Mozilla)
if (preg_match('@Opera(/| )([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'OPERA');
} else if (preg_match('@MSIE ([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'IE');
} else if (preg_match('@OmniWeb/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'OMNIWEB');
//} else if (ereg('Konqueror/([0-9].[0-9]{1,2})', $HTTP_USER_AGENT, $log_version)) {
// Konqueror 2.2.2 says Konqueror/2.2.2
// Konqueror 3.0.3 says Konqueror/3
} else if (preg_match('@(Konqueror/)(.*)(;)@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'KONQUEROR');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)
           && preg_match('@Safari/([0-9]*)@', $HTTP_USER_AGENT, $log_version2)) {
    define('PMA_USR_BROWSER_VER', $log_version[1] . '.' . $log_version2[1]);
    define('PMA_USR_BROWSER_AGENT', 'SAFARI');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'MOZILLA');
} else {
    define('PMA_USR_BROWSER_VER', 0);
    define('PMA_USR_BROWSER_AGENT', 'OTHER');
}
?>