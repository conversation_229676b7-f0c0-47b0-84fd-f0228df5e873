<?
/*
  	$Id: administrator.php,v 1.18 2015/05/28 10:52:12 darren.ng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- administrator //-->
<?php
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_ADMIN_FILES => BOX_ADMINISTRATOR_BOXES, FILENAME_DEFINE_LANGUAGE => BOX_TOOLS_DEFINE_LANGUAGE,
								FILENAME_LOG_FILES => BOX_ADMINISTRATOR_LOG, FILENAME_ADMIN_MEMBERS => BOX_ADMINISTRATOR_MEMBERS, 
								FILENAME_ORDERS_STATUS_CONF => BOX_ADMINISTRATOR_ORDERS_STATUS_CONF, FILENAME_PAYMENT_GATEWAY => BOX_ADMINISTRATOR_PAYMENT_GATEWAY,
								FILENAME_IMAGE_UPLOAD_CONF => BOX_ADMINISTRATOR_IMAGE_UPLOAD_CONF
							 );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
	  	$heading[] = array(	'text'  => BOX_HEADING_ADMINISTRATOR,
	                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=administrator')
	                     	);
		
	  	if ($selected_box == 'administrator') {
	  		// need to check for permission later
	  		$gID_str = '';
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE (configuration_group_id IN (902, 12, 3, 2, 16, 1, 13, 99, 14, 10, 15, 903, 908, 909, 910, 913, 914, 915)) AND visible = '1' ORDER BY configuration_group_title");
	    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	    		$gID_str .= $configuration_groups["cgID"] . ',';
	    	}
	    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
	  		
	    	$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_ADMIN_FILES, BOX_ADMINISTRATOR_BOXES) .
	    							   		tep_admin_files_boxes(FILENAME_CUSTOMERS_TYPE_CONF, BOX_ADMINISTRATOR_CUSTOMERS_CONF) . 
	    							   		tep_admin_files_boxes(FILENAME_DEFINE_LANGUAGE, BOX_TOOLS_DEFINE_LANGUAGE) . 
	    							   		tep_admin_files_boxes(FILENAME_IMAGE_UPLOAD_CONF, BOX_ADMINISTRATOR_IMAGE_UPLOAD_CONF) .
	                                   		tep_admin_files_boxes(FILENAME_LOG_FILES, BOX_ADMINISTRATOR_LOG) .
	                                   		tep_admin_files_boxes(FILENAME_ADMIN_MEMBERS, BOX_ADMINISTRATOR_MEMBERS) .
	                                   		tep_admin_files_boxes(FILENAME_SYSTEM_LOG, BOX_ADMINISTRATOR_SYSTEM_LOG) .
	                                   		tep_admin_files_boxes(FILENAME_ORDERS_STATUS_CONF, BOX_ADMINISTRATOR_ORDERS_STATUS_CONF) .
	                                   		tep_admin_files_boxes(FILENAME_PAYMENT_GATEWAY, BOX_ADMINISTRATOR_PAYMENT_GATEWAY) . 
	                                   		tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
								);
		}
		
		$box = new box;
		echo $box->menuBox($heading, $contents);
		
		echo '	</td>
			 </tr>';
	}
?>
<!-- administrator_eof //-->