<?
/*
  	$Id: affiliate.php,v 1.6 2006/07/20 06:06:33 weichen Exp $
	
  	OSC-Affiliate
	
  	Contribution based on:
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- affiliates //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_AFFILIATE => BOX_AFFILIATE, FILENAME_AFFILIATE_BANNER_MANAGER => BOX_AFFILIATE_BANNERS,
								FILENAME_AFFILIATE_CLICKS => BOX_AFFILIATE_CLICKS, FILENAME_AFFILIATE_CONTACT => BOX_AFFILIATE_CONTACT, 
								FILENAME_AFFILIATE_PAYMENT => BOX_AFFILIATE_PAYMENT, FILENAME_AFFILIATE_SALES => BOX_AFFILIATE_SALES,
								FILENAME_AFFILIATE_SUMMARY => BOX_AFFILIATE_SUMMARY);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_AFFILIATE,
                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=affiliate')
                     	);
	
  	if ($selected_box == 'affiliate') {
  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id=900 AND visible = '1'");
    	if ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
      		$cfg_string = '<a href="' . tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $configuration_groups['cgID'], 'NONSSL') . '" class="menuBoxContentLink">'.BOX_CONFIGURATION_LINK.'</a>';
    	}
    	$contents[] = array('text'  => 	'<a href="' . tep_href_link(FILENAME_AFFILIATE, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE . '</a><br>' .
    							   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_BANNER_MANAGER, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_BANNERS . '</a><br>' .
    							   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_CLICKS, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_CLICKS . '</a><br>' .
    							   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_CONTACT, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_CONTACT . '</a><br>' .
    							   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_PAYMENT, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_PAYMENT . '</a><br>' .
                                   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_SALES, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_SALES . '</a><br>' .
    							   		'<a href="' . tep_href_link(FILENAME_AFFILIATE_SUMMARY, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_AFFILIATE_SUMMARY . '</a><br>' .
    							   		tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$configuration_groups['cgID'])
							);
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- affiliates_eof //-->