<!-- buyback //-->
<?
  	$heading = array();
  	$contents = array();

	$filenames_array = array (	FILENAME_BUYBACK_PRODUCTS => BOX_BUYBACK_PRODUCTS,
								FILENAME_BUYBACK_GROUPS => BOX_BUYBACK_GROUPS,
								FILENAME_BUYBACK_REQUESTS => BOX_BUYBACK_REQUESTS,
								FILENAME_VIP_INVENTORY_REPORT => BOX_VIP_BUYBACK_FOLLOW_UP,
								FILENAME_SUPPLIERS_RESTOCK_CHARACTERS => BOX_SUPPLIERS_RESTOCK_CHARACTERS,
								FILENAME_SUPPLIERS_GROUPS => BOX_SUPPLIERS_GROUPS,
								FILENAME_SUPPLIERS_LIST => BOX_SUPPLIERS_LIST,
								FILENAME_PRODUCTS_PURCHASE_QUANTITY => BOX_SUPPLIERS_PURCHASE_LISTS,
								FILENAME_SUPPLIERS_PRICING => BOX_SUPPLIER_PRICING,
								FILENAME_SUPPLIERS_ORDERS_TRACKING => BOX_SUPPLIERS_ORDERS_TRACKING,
								FILENAME_SUPPLIERS_PAYMENT => BOX_SUPPLIER_PAYMENT,
								FILENAME_SUPPLIERS_REPORT => BOX_SUPPLIER_REPORT,
								FILENAME_COMPETITORS => BOX_COMPETITORS,
								FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE => BOX_SUPPLIERS_AVERAGE_OFFER_PRICE,
								FILENAME_VIP_SUPPLIER_RANKING => BOX_VIP_SUPPLIER_RANKING,
								FILENAME_VIP_RULES => BOX_VIP_RULES,
								FILENAME_PRICE_AUTOMATION => BOX_PRICE_AUTOMATION, 
								FILENAME_PRODUCTS_SUPPLIER => BOX_PRODUCTS_SUPPLIER
							);

	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
	  	$heading[] = array(	'text'  => BOX_HEADING_BUYBACK,
	                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=buyback'));

	  	if ($selected_box == 'buyback') {
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id=901 AND visible = '1'");
	    	if ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	      		$cfg_string = '<a href="' . tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $configuration_groups['cgID'], 'NONSSL') . '" class="menuBoxContentLink">'.BOX_CONFIGURATION_LINK.'</a>';
	    	}

			$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_BUYBACK_PRODUCTS, BOX_BUYBACK_PRODUCTS) .
											tep_admin_files_boxes(FILENAME_BUYBACK_GROUPS, BOX_BUYBACK_GROUPS) .
											tep_admin_files_boxes(FILENAME_BUYBACK_REQUESTS, BOX_BUYBACK_REQUESTS) .
											tep_admin_files_boxes(FILENAME_VIP_INVENTORY_REPORT, BOX_VIP_BUYBACK_FOLLOW_UP) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, BOX_SUPPLIERS_RESTOCK_CHARACTERS) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_GROUPS, BOX_SUPPLIERS_GROUPS) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_LIST, BOX_SUPPLIERS_LIST) .
											tep_admin_files_boxes(FILENAME_PRODUCTS_PURCHASE_QUANTITY, BOX_SUPPLIERS_PURCHASE_LISTS) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_PRICING, BOX_SUPPLIER_PRICING) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_ORDERS_TRACKING, BOX_SUPPLIERS_ORDERS_TRACKING) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_PAYMENT, BOX_SUPPLIER_PAYMENT) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_REPORT, BOX_SUPPLIER_REPORT) .
											tep_admin_files_boxes(FILENAME_COMPETITORS, BOX_COMPETITORS) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, BOX_SUPPLIERS_AVERAGE_OFFER_PRICE) .
											tep_admin_files_boxes(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, BOX_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS) .
											tep_admin_files_boxes(FILENAME_VIP_SUPPLIER_RANKING, BOX_VIP_SUPPLIER_RANKING) .
											tep_admin_files_boxes(FILENAME_VIP_RULES, BOX_VIP_RULES) .
											tep_admin_files_boxes(FILENAME_PRICE_AUTOMATION, BOX_PRICE_AUTOMATION) .
											tep_admin_files_boxes(FILENAME_PRODUCTS_SUPPLIER, BOX_PRODUCTS_SUPPLIER) .
											tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID=' . $configuration_groups['cgID'])
								);
	  	}

	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);

	  	echo '	</td>
			 </tr>';
	}
?>
<!-- buyback_eof //-->