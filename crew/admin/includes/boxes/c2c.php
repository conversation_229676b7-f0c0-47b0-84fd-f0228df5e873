<?php
/*
  	$Id: c2c.php,v 1.3 2013/07/30 08:36:53 chunhoong.leong Exp $
	
	Developer: Ching Yen
*/
	
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_C2C_SELLER_GROUP => BOX_C2C_SELLER_GROUP, 
								FILENAME_C2C_SELLER_PRODUCT_LISTING => BOX_C2C_SELLER_PRODUCT_LISTING, 
								FILENAME_C2C_SELLER_VERIFICATION_SETTING => BOX_C2C_SELLER_VERIFICATION_SETTING, 
								FILENAME_C2C_BUYBACK_ORDER => BOX_C2C_BUYBACK_ORDER, 
								FILENAME_C2C_DELIVERY_SPEED => BOX_C2C_DELIVERY_SPEED, 
								FILENAME_C2C_PRODUCT_CONFIGURATION => BOX_C2C_PRODUCT_CONFIGURATION, 
								FILENAME_C2C_CONFIGURATION => BOX_C2C_CONFIGURATION, );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
		
		$heading[] = array(	'text'  => BOX_HEADING_C2C,
							'link'  => tep_href_link($filtered_files_array[0], 'selected_box=c2c')
							);
		
		if ($selected_box == 'c2c') {
			$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_C2C_BUYBACK_ORDER, BOX_C2C_BUYBACK_ORDER, 'selected_box=c2c') . 
                                            tep_admin_files_boxes(FILENAME_C2C_DELIVERY_SPEED, BOX_C2C_DELIVERY_SPEED, 'selected_box=c2c') .
                                            tep_admin_files_boxes(FILENAME_C2C_PRODUCT_CONFIGURATION, BOX_C2C_PRODUCT_CONFIGURATION, 'selected_box=c2c') . 
                                            tep_admin_files_boxes(FILENAME_C2C_RATING_TYPE, BOX_C2C_RATING_TYPE, 'selected_box=c2c') .
                                            tep_admin_files_boxes(FILENAME_C2C_SELLER_GROUP, BOX_C2C_SELLER_GROUP, 'selected_box=c2c') . 
                                            tep_admin_files_boxes(FILENAME_C2C_SELLER_LEVEL_CONFIGURATION, BOX_C2C_SELLER_LEVEL_CONFIGURATION, 'selected_box=c2c') .
											tep_admin_files_boxes(FILENAME_C2C_SELLER_PRODUCT_LISTING, BOX_C2C_SELLER_PRODUCT_LISTING, 'selected_box=c2c') . 
											tep_admin_files_boxes(FILENAME_C2C_SELLER_VERIFICATION_SETTING, BOX_C2C_SELLER_VERIFICATION_SETTING, 'selected_box=c2c') .
											tep_admin_files_boxes(FILENAME_C2C_CONFIGURATION, BOX_C2C_CONFIGURATION, 'selected_box=c2c')                                           
                                            
								);
		}
		
	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);
		
	  	echo '	</td>
			 </tr>';
	}
?>
<!-- purchase order_eof //-->