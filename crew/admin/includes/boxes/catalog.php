<?
/*
  	$Id: catalog.php,v 1.16 2011/05/31 10:19:49 chingyen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- catalog //-->
	<tr>
		<td>
<?php
	$heading = array();
	$contents = array();
	
	$filenames_array = array (	
    FILENAME_CATEGORIES => BOX_CATALOG_CATEGORIES_PRODUCTS, 
    FILENAME_PRODUCTS_EXPECTED => BOX_CATALOG_PRODUCTS_EXPECTED, 
    FILENAME_PRICE_TAGS => BOX_CATALOG_PRICE_TAGS, 
    FILENAME_BATCH_UPDATE_PRICES => BOX_CATALOG_BATCH_UPDATE_PRICES, 
    FILENAME_BATCH_UPDATE_PRICES2 => BOX_CATALOG_BATCH_UPDATE_PRICES2, 
    FILENAME_PRINTABLE_LIST => BOX_CATALOG_PRINTABLE_LIST, 
    FILENAME_PRODUCTS_FLAG => 'Product Flags', 
    FILENAME_NEW_ATTRIBUTES => BOX_CATALOG_ATTRIBUTE_MANAGER, 
    FILENAME_PRODUCTS_ATTRIBUTES => BOX_CATALOG_CATEGORIES_PRODUCTS_ATTRIBUTES, 
    FILENAME_MANUFACTURERS => 'Realms', 
    FILENAME_PRODUCT_PROMOTIONS => BOX_MODULES_PRODUCT_PROMOTIONS, 
    FILENAME_REVIEWS => BOX_CATALOG_REVIEWS, 
    FILENAME_PRODUCTS_PAYMENT_RESTRICTIONS => BOX_CATALOG_PAYMENT_RESTRICTIONS,
    FILENAME_EASYPOPULATE => BOX_CATALOG_EASYPOPULATE
  );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	$heading[] = array(	
    'text'  => BOX_HEADING_CATALOG, 
    'link'  => tep_href_link($filtered_files_array[0], 'selected_box=catalog')
  );
	
	if ($selected_box == 'catalog') {
    $gID_str = '';
    $configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id IN (905, 907, 899, 8, 9) AND visible = '1' ORDER BY configuration_group_title");
    while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
            $gID_str .= $configuration_groups["cgID"] . ',';
    }
    if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);

    $contents[] = array('text'  => 	//tep_admin_files_boxes(FILENAME_XSELL_PRODUCTS, BOX_CATALOG_XSELL_PRODUCTS) .
                                tep_admin_files_boxes(FILENAME_PRODUCTS_EXPECTED, BOX_CATALOG_PRODUCTS_EXPECTED) .
                                tep_admin_files_boxes(FILENAME_PRICE_TAGS, BOX_CATALOG_PRICE_TAGS) .
                                tep_admin_files_boxes(FILENAME_BATCH_UPDATE_PRICES, BOX_CATALOG_BATCH_UPDATE_PRICES) .
                                tep_admin_files_boxes(FILENAME_BATCH_UPDATE_PRICES2, BOX_CATALOG_BATCH_UPDATE_PRICES2) .
                                tep_admin_files_boxes(FILENAME_PRINTABLE_LIST, BOX_CATALOG_PRINTABLE_LIST) .
                                tep_admin_files_boxes(FILENAME_PRODUCTS_FLAG, 'Product Flags') .
                                tep_admin_files_boxes(FILENAME_NEW_ATTRIBUTES, BOX_CATALOG_ATTRIBUTE_MANAGER) .
                                tep_admin_files_boxes(FILENAME_PRODUCTS_ATTRIBUTES, BOX_CATALOG_CATEGORIES_PRODUCTS_ATTRIBUTES) .
                                tep_admin_files_boxes(FILENAME_PRODUCT_PROMOTIONS, BOX_MODULES_PRODUCT_PROMOTIONS) .
                                tep_admin_files_boxes(FILENAME_MANUFACTURERS, 'Realms') .
                                tep_admin_files_boxes(FILENAME_REVIEWS, BOX_CATALOG_REVIEWS) .
                                tep_admin_files_boxes(FILENAME_PRODUCTS_PAYMENT_RESTRICTIONS, BOX_CATALOG_PAYMENT_RESTRICTIONS) .
                                tep_admin_files_boxes(FILENAME_CATEGORIES, BOX_CATALOG_CATEGORIES_PRODUCTS) .
                                tep_admin_files_boxes(FILENAME_OUT_OF_STOCK_RULE, BOX_CATALOG_OUT_OF_STOCK_RULE) .
                                tep_admin_files_boxes(FILENAME_CATEGORIES_STRUCTURES, BOX_CATALOG_CATEGORIES_STRUCTURES) .
                                //tep_admin_files_boxes(FILENAME_PRODUCTS_TYPE_TEMPLATE, BOX_CATALOG_PRODUCTS_TYPE_TEMPLATE) .
                                tep_admin_files_boxes(FILENAME_CATEGORIES_TYPES, BOX_CATALOG_CATEGORIES_TYPES) .
                                //tep_admin_files_boxes(FILENAME_SPECIALS, BOX_CATALOG_SPECIALS) .
                                tep_admin_files_boxes(FILENAME_CATEGORY_DISCOUNT_RULE, BOX_CATALOG_DISCOUNT_RULE) .
                                tep_admin_files_boxes(FILENAME_EASYPOPULATE, BOX_CATALOG_EASYPOPULATE) . 
                                tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
    ); //Admin end
	}
	
	$box = new box;
	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- catalog_eof //-->