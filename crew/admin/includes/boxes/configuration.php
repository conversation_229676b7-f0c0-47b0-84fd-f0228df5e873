<?
/*
  	$Id: configuration.php,v 1.4 2006/07/19 07:31:26 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- configuration //-->
	<!--tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
  	$heading[] = array(	'text'  => BOX_HEADING_CONFIGURATION,
                     	'link'  => tep_href_link(FILENAME_CONFIGURATION, 'gID=1&selected_box=configuration'));
	
  	if ($selected_box == 'configuration') {
    	$cfg_groups = '';
    	$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE visible = '1' ORDER BY configuration_group_title ASC");
    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
      		$cfg_groups .= '<a href="' . tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $configuration_groups['cgID'], 'NONSSL') . '" class="menuBoxContentLink">' . $configuration_groups['cgTitle'] . '</a><br>';
    	}
		/*
    	$contents[] = array('text'  => 	$cfg_groups .
    									tep_admin_files_boxes(FILENAME_ORDERS_STATUS, BOX_LOCALIZATION_ORDERS_STATUS)
     						);
     	*/
     	$contents[] = array('text'  => 	$cfg_groups);
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr-->
<!-- configuration_eof //-->