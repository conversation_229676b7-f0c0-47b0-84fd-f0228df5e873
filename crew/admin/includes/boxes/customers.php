<?
/*
  	$Id: customers.php,v 1.11 2013/06/07 13:07:35 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- customers //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_CUSTOMERS_GROUPS => BOX_CUSTOMERS_GROUPS, FILENAME_CUSTOMERS => BOX_CUSTOMERS_CUSTOMERS,
								FILENAME_CUSTOMERS_ACC_MERGING => BOX_CUSTOMERS_ACC_MERGING, FILENAME_NEWSLETTERS => BOX_TOOLS_NEWSLETTER_MANAGER, 
								FILENAME_PURCHASE_LIMIT => BOX_CUSTOMERS_PURCHASE_LIMIT, FILENAME_STATS_CUSTOMERS => BOX_REPORTS_ORDERS_TOTAL,
								FILENAME_WHOS_ONLINE => BOX_TOOLS_WHOS_ONLINE, 
								FILENAME_CUSOMTERS_VERIFICATION_SETTING => BOX_CUSOMTERS_VERIFICATION_SETTING );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_CUSTOMERS,
                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=customers'));
	
  	if ($selected_box == 'customers') {
		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id IN (5, 906, 912) AND visible = '1'");
    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
    		$gID_str .= $configuration_groups["cgID"] . ',';
    	}
    	
    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
    	
    	$contents[] = array('text'  =>	tep_admin_files_boxes(FILENAME_CUSTOMERS_GROUPS, BOX_CUSTOMERS_GROUPS) .
                                   		tep_admin_files_boxes(FILENAME_CUSTOMERS, BOX_CUSTOMERS_CUSTOMERS) .
                                   		tep_admin_files_boxes(FILENAME_CUSTOMERS_ACC_MERGING, BOX_CUSTOMERS_ACC_MERGING, 'action=step_1') .
                                   		tep_admin_files_boxes(FILENAME_NEWSLETTERS, BOX_TOOLS_NEWSLETTER_MANAGER) .
                                   		tep_admin_files_boxes(FILENAME_PURCHASE_LIMIT, BOX_CUSTOMERS_PURCHASE_LIMIT) .
                                   		tep_admin_files_boxes(FILENAME_STATS_CUSTOMERS, BOX_REPORTS_ORDERS_TOTAL) . 
                                   		tep_admin_files_boxes(FILENAME_WHOS_ONLINE, BOX_TOOLS_WHOS_ONLINE) .
										tep_admin_files_boxes(FILENAME_CUSOMTERS_VERIFICATION_SETTING, BOX_CUSOMTERS_VERIFICATION_SETTING) .
                                   		tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
							);
	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- customers_eof //-->