<!-- buyback //-->
<?
  	$heading = array();
  	$contents = array();
	
  	$filenames_array = array (	FILENAME_CDKEY => BOX_CDKEY_TEMPLATE,
								FILENAME_PROGRESS_REPORT => BOX_PROGRESS_REPORT_TEMPLATE,
								FILENAME_CUSTOM_PRODUCT_PAYMENT => BOX_CUSTOM_PRODUCT_PAYMENT_TEMPLATE,
								FILENAME_DATA_POOL => BOX_DATA_POOL_TEMPLATE,
								FILENAME_HLA_TEMPLATE => BOX_HLA_TEMPLATE,
								FILENAME_TRADE_MAIL_LOG => BOX_TRADE_MAIL_LOGS_TEMPLATE,
								FILENAME_PUBLISHERS => BOX_PUBLISHERS_TEMPLATE,
								FILENAME_PUBLISHERS_GAMES => BOX_PUBLISHERS_GAMES_TEMPLATE,
                                                                FILENAME_PUBLISHERS_REPLENISH => BOX_PUBLISHERS_REPLENISH_TEMPLATE
							);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
	  	$heading[] = array(	'text'  => BOX_HEADING_CUSTOM_PRODUCT,
	  						'link'  => tep_href_link($filtered_files_array[0], 'selected_box=data_pool'));
		
	  	if ($selected_box == 'data_pool') {
            // need to check for permission later
	  		$gID_str = '';
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE (configuration_group_id IN (904)) AND visible = '1' ORDER BY configuration_group_title");
	    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	    		$gID_str .= $configuration_groups["cgID"] . ',';
	    	}
	    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
            
	    	$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_CDKEY, BOX_CDKEY_TEMPLATE) .
	    									tep_admin_files_boxes(FILENAME_PROGRESS_REPORT, BOX_PROGRESS_REPORT_TEMPLATE) .
	    									tep_admin_files_boxes(FILENAME_CUSTOM_PRODUCT_PAYMENT, BOX_CUSTOM_PRODUCT_PAYMENT_TEMPLATE) .
	    									tep_admin_files_boxes(FILENAME_DATA_POOL, BOX_DATA_POOL_TEMPLATE) .
	    									tep_admin_files_boxes(FILENAME_HLA_TEMPLATE, BOX_HLA_TEMPLATE) .
											tep_admin_files_boxes(FILENAME_TRADE_MAIL_LOG, BOX_TRADE_MAIL_LOGS_TEMPLATE) .
	    									tep_admin_files_boxes(FILENAME_PUBLISHERS, BOX_PUBLISHERS_TEMPLATE) . 
	    									tep_admin_files_boxes(FILENAME_PUBLISHERS_GAMES, BOX_PUBLISHERS_GAMES_TEMPLATE) .
                                                                                tep_admin_files_boxes(FILENAME_PUBLISHERS_REPLENISH, BOX_PUBLISHERS_REPLENISH_TEMPLATE) .
                                            tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
								);
	  	}
		
	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);
	  	echo '	</td>
			 </tr>';
	}
?>
<!-- buyback_eof //-->