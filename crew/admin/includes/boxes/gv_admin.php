<?
/*
  	$Id: gv_admin.php,v 1.5 2007/05/10 09:57:01 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Gift Voucher System v1.0
  	Copyright (c) 2001,2002 <PERSON>
  	http://www.phesis.org
	
  	Released under the GNU General Public License
*/
?>
<!-- gv_admin //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_COUPON_ADMIN => BOX_COUPON_ADMIN, FILENAME_GV_SENT => BOX_COUPON_SENT,
								FILENAME_GV_QUEUE => BOX_COUPON_QUEUE, FILENAME_GV_MAIL => BOX_COUPON_MAIL);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_COUPON,
                     	'link'  => tep_href_link($filtered_files_array[0], 'set=gv_admin&selected_box=gv_admin')
                     	);
	
  	if ($selected_box == 'gv_admin') {
    	$contents[] = array('text'  => 	'<a href="' . tep_href_link(FILENAME_COUPON_ADMIN, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_COUPON_ADMIN . '</a><br>' .
                                   		'<a href="' . tep_href_link(FILENAME_GV_SENT, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_COUPON_SENT . '</a><br>'.
                                   		
    							   		'<a href="' . tep_href_link(FILENAME_GV_QUEUE, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_COUPON_QUEUE . '</a><br>'
    							   		/*
                                   		'<a href="' . tep_href_link(FILENAME_GV_MAIL, '', 'NONSSL') . '" class="menuBoxContentLink">' . BOX_COUPON_MAIL . '</a>'*/
							);
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- gv_admin_eof //-->