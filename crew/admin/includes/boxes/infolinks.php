<!-- information //-->
	<tr>
		<td>
<?php
  	$heading = array();
  	$contents = array();
  	
  	$filenames_array = array (	FILENAME_INFOLINKS_INDEX => BOX_INFOLINKS_INDEX,
								FILENAME_DEFINE_MAINPAGE => BOX_DEFINE_MAIN_PAGE,
								FILENAME_DEFINE_PRODUCT_TYPE_PAGE => BOX_CATALOG_DEFINE_PRODUCT_TYPE_PAGE,
								FILENAME_DEFINE_GAME_LAND_PAGE => BOX_DEFINE_GAME_LAND_PAGE,
                                FILENAME_DEFINE_GAME_LAND_PAGE2 => BOX_DEFINE_GAME_LAND_PAGE . '2',
								FILENAME_GAME_DATABASE_CONFIG => BOX_GAME_DATABASE_CONFIG,
								FILENAME_MENU_MANAGEMENT => BOX_MENU_MANAGEMENT
							);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_INFOLINKS,
                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=infolinks')
                     	);
	
	if ($selected_box == "infolinks") {
		$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_INFOLINKS_INDEX, BOX_INFOLINKS_INDEX) .
										tep_admin_files_boxes(FILENAME_DEFINE_MAINPAGE, BOX_DEFINE_MAIN_PAGE) .
										tep_admin_files_boxes(FILENAME_DEFINE_PRODUCT_TYPE_PAGE, BOX_CATALOG_DEFINE_PRODUCT_TYPE_PAGE) .
										tep_admin_files_boxes(FILENAME_DEFINE_GAME_LAND_PAGE, BOX_DEFINE_GAME_LAND_PAGE) .
                                        tep_admin_files_boxes(FILENAME_DEFINE_GAME_LAND_PAGE2, BOX_DEFINE_GAME_LAND_PAGE . '2') .
										tep_admin_files_boxes(FILENAME_GAME_DATABASE_CONFIG, BOX_GAME_DATABASE_CONFIG) . 
										tep_admin_files_boxes(FILENAME_MENU_MANAGEMENT, BOX_MENU_MANAGEMENT)
							);
	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- information_eof //-->