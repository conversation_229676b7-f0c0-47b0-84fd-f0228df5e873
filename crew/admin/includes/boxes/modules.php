<?
/*
  	$Id: modules.php,v 1.7 2009/03/10 03:03:00 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- modules //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_MODULES => BOX_MODULES_ORDER_TOTAL, FILENAME_PAYMENT_MODULE => BOX_MODULES_PAYMENT, FILENAME_PAYMENT_METHODS => BOX_MODULES_PAYMENT_METHODS, FILENAME_SHOPPING_CART_COMMENTS => BOX_MODULES_SHOPPING_CART_COMMENTS);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_MODULES,
                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=modules' . ($filtered_files_array[0]==FILENAME_MODULES ? '&set=ordertotal' : '')));
  	
  	if ($selected_box == 'modules') {
  		$gID_str = '';
  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id IN (7) AND visible = '1' ORDER BY configuration_group_title");
    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
    		$gID_str .= $configuration_groups["cgID"] . ',';
    	}
    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
  		
    	$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_MODULES, BOX_MODULES_ORDER_TOTAL, 'set=ordertotal') . 
                                   		tep_admin_files_boxes(FILENAME_PAYMENT_METHODS, BOX_MODULES_PAYMENT_METHODS) . 
                                   		tep_admin_files_boxes(FILENAME_PAYMENT_MODULE, BOX_MODULES_PAYMENT) . 
                                   		tep_admin_files_boxes(FILENAME_MODULES, BOX_MODULES_SHIPPING, 'set=shipping') . 
                                   		tep_admin_files_boxes(FILENAME_SHOPPING_CART_COMMENTS, BOX_MODULES_SHOPPING_CART_COMMENTS) . 
                                   		tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
							);
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- modules_eof //-->