<?
/*
  	$Id: payments.php,v 1.4 2011/06/17 05:58:29 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/
?>
<!-- payments //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_ACCOUNT_STATEMENT => BOX_PAYMENTS_ACCOUNT_STATEMENT,
								FILENAME_PAYMENT => BOX_PAYMENTS_PAYMENT,
								FILENAME_REFUND => BOX_PAYMENTS_REFUND,
								FILENAME_REDEEM => BOX_POINTS_REDEEM,
								FILENAME_STORE_CREDIT => BOX_PAYMENTS_STORE_CREDIT,
								FILENAME_STORE_POINT => BOX_PAYMENTS_STORE_POINT,
								FILENAME_CREDIT_NOTES_STATEMENT => BOX_PAYMENTS_CREDIT_NOTES_STATEMENT,
								FILENAME_PO_PROVISION_PAYMENTS => BOX_PO_PROVISION_PAYMENTS
							);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
  	$heading[] = array(	'text'  => BOX_HEADING_PAYMENTS,
                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=payments')
                     	);
	
  	if ($selected_box == 'payments') {
		$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_ACCOUNT_STATEMENT, BOX_PAYMENTS_ACCOUNT_STATEMENT) .
										tep_admin_files_boxes(FILENAME_PAYMENT, BOX_PAYMENTS_PAYMENT) .
										tep_admin_files_boxes(FILENAME_REFUND, BOX_PAYMENTS_REFUND) .
										tep_admin_files_boxes(FILENAME_REDEEM, BOX_POINTS_REDEEM) .
										tep_admin_files_boxes(FILENAME_STORE_CREDIT, BOX_PAYMENTS_STORE_CREDIT) .
										tep_admin_files_boxes(FILENAME_STORE_POINT, BOX_PAYMENTS_STORE_POINT) .
										tep_admin_files_boxes(FILENAME_CREDIT_NOTES_STATEMENT, BOX_PAYMENTS_CREDIT_NOTES_STATEMENT) .
										tep_admin_files_boxes(FILENAME_PO_PROVISION_PAYMENTS, BOX_PO_PROVISION_PAYMENTS)
							);
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- payments_eof //-->