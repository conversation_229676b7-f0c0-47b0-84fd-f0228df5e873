<?
/*
  	$Id: paypalipn.php,v 1.4 2005/05/13 03:33:48 weichen Exp $
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Paypal IPN v0.981 for Milestone 2
  	Copyright (c) 2003 <PERSON>
  	<EMAIL>
  	http://www.osmosisdc.com
	
  	Released under the GNU General Public License
*/
?>
<!-- paypalipn //-->
	<tr>
		<td>
<?
  	$heading = array();
  	$contents = array();
	
  	$heading[] = array	(	'text'  => BOX_HEADING_PAYPALIPN_ADMIN,
                     		'link'  => tep_href_link(FILENAME_PAYPAL, 'selected_box=paypalipn')
                     	);

  	if ($selected_box == 'paypalipn') {
  		//$contents[] = array('text'  => '<a href="' . tep_href_link(FILENAME_PAYPALIPN_TESTS) . '?action=view">' . BOX_PAYPALIPN_ADMIN_TESTS . '</a><br>');
    	//$contents[] = array('text'  => '<a href="' . tep_href_link(FILENAME_PAYPALIPN_TRANSACTIONS) . '?action=view">' . BOX_PAYPALIPN_ADMIN_TRANSACTIONS . '</a><br>');
    	//begin PayPal_Shopping_Cart_IPN
    	//$contents[] = array('text'  => '<a href="' . tep_href_link(FILENAME_PAYPAL, 'action=view') . '">' . BOX_CUSTOMERS_PAYPAL . '</a><br>');
    	$contents[] = array('text'  => tep_admin_files_boxes(FILENAME_PAYPAL, BOX_CUSTOMERS_PAYPAL, "action=view"));
    	//end PayPal_Shopping_Cart_IPN
  	}
	
  	$box = new box;
  	echo $box->menuBox($heading, $contents);
?>
		</td>
	</tr>
<!-- paypalipn_eof //-->