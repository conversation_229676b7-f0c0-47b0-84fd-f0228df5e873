<!-- purchase order //-->
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_PRODUCTS_LOW_STOCK => BOX_REPORTS_PRODUCTS_LOW_STOCK,
                                        FILENAME_DTU_PAYMENT => BOX_DTU_PAYMENT_TEMPLATE,
                                        FILENAME_API_REPLENISH_PAYMENT => BOX_API_REPLENISH_PAYMENT_TEMPLATE,
                                FILENAME_CDK_PAYMENT => BOX_CDK_PAYMENT_TEMPLATE,
		                        FILENAME_PO_COMPANY => BOX_PO_COMPANY,
                                        FILENAME_EDIT_PURCHASE_ORDERS => BOX_EDIT_PURCHASE_ORDERS, 
                                        FILENAME_PO_SUPPLIERS_LIST => BOX_PO_SUPPLIERS_LIST,
                                        FILENAME_PO_PAYMENT => BOX_PO_PAYMENT
                                );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
	  	$heading[] = array(	'text'  => BOX_HEADING_PO,
	                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=po'));
		
	  	if ($selected_box == 'po') {
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id=911 AND visible = '1'");
	    	if ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	      		$cfg_string = '<a href="' . tep_href_link(FILENAME_CONFIGURATION, 'gID=' . $configuration_groups['cgID'], 'NONSSL') . '" class="menuBoxContentLink">'.BOX_CONFIGURATION_LINK.'</a>';
	    	}
			
			$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_PRODUCTS_LOW_STOCK, BOX_REPORTS_PRODUCTS_LOW_STOCK) .
                                                        tep_admin_files_boxes(FILENAME_DTU_PAYMENT, BOX_DTU_PAYMENT_TEMPLATE) .
                                                        tep_admin_files_boxes(FILENAME_API_REPLENISH_PAYMENT, BOX_API_REPLENISH_PAYMENT_TEMPLATE) .
                                            tep_admin_files_boxes(FILENAME_CDK_PAYMENT, BOX_CDK_PAYMENT_TEMPLATE) .
                                                        tep_admin_files_boxes(FILENAME_PO_COMPANY, BOX_PO_COMPANY) .
                                                        tep_admin_files_boxes(FILENAME_EDIT_PURCHASE_ORDERS, BOX_EDIT_PURCHASE_ORDERS) .
                                                        tep_admin_files_boxes(FILENAME_PO_SUPPLIERS_LIST, BOX_PO_SUPPLIERS_LIST) .
                                                        tep_admin_files_boxes(FILENAME_PO_PAYMENT, BOX_PO_PAYMENT) .
                                                        tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$configuration_groups['cgID'])
                                );
	  	}
		
	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);
		
	  	echo '	</td>
			 </tr>';
	}
?>
<!-- purchase order_eof //-->