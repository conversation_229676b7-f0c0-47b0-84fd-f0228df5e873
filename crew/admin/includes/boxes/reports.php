<?
/*
  $Id: reports.php,v 1.10 2011/04/15 04:29:59 boonhock Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
 */
?>
<!-- reports //-->
<tr>
    <td>
        <?
        $heading = array();
        $contents = array();

        $filenames_array = array(
            FILENAME_API_REPORT => BOX_REPORTS_API,
            FILENAME_STATS_PRODUCTS_PURCHASED => BOX_REPORTS_PRODUCTS_PURCHASED,
            FILENAME_STATS_PRODUCTS_VIEWED => BOX_REPORTS_PRODUCTS_VIEWED,
            FILENAME_STATS_SALES_REPORT => BOX_REPORTS_SALES,
            FILENAME_SALES_REPORT => BOX_REPORTS_SALES_NEW,
            FILENAME_STOCK_REPORT => BOX_REPORTS_STOCK,
            FILENAME_MGC_SALES_REPORT => BOX_REPORTS_MGC_SALES,
        );

        $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

        $heading[] = array('text' => BOX_HEADING_REPORTS,
            'link' => tep_href_link($filtered_files_array[0], 'selected_box=reports'));

        if ($selected_box == 'reports') {
            $contents[] = array('text' =>
                tep_admin_files_boxes(FILENAME_API_REPORT, BOX_REPORTS_API) .
                tep_admin_files_boxes(FILENAME_STATS_PRODUCTS_PURCHASED, BOX_REPORTS_PRODUCTS_PURCHASED) .
                tep_admin_files_boxes(FILENAME_STATS_PRODUCTS_VIEWED, BOX_REPORTS_PRODUCTS_VIEWED) .
                tep_admin_files_boxes(FILENAME_STATS_SALES_REPORT, BOX_REPORTS_SALES) .
                tep_admin_files_boxes(FILENAME_SALES_REPORT, BOX_REPORTS_SALES_NEW) .
                tep_admin_files_boxes(FILENAME_STOCK_REPORT, BOX_REPORTS_STOCK) .
                tep_admin_files_boxes(FILENAME_MGC_SALES_REPORT, BOX_REPORTS_MGC_SALES)
            );
        }

        $box = new box;
        echo $box->menuBox($heading, $contents);
        ?>
    </td>
</tr>
<!-- reports_eof //-->