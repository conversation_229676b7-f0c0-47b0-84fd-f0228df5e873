<!-- sales //-->
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array ( 	FILENAME_STATS_ORDERS_TRACKING => BOX_SALES_ACTIVE_ORDERS,
								FILENAME_STATS_ORDERS_COMMENT => BOX_SALES_PREDEFINED_COMMENTS,
								FILENAME_ORDERS_TAGS => BOX_SALES_ORDERS_TAGS,
								FILENAME_CUSTOMERS_ORDER_ACTIVITIES => BOX_SALES_CUSTOMERS_ORDER_ACTIVITIES,
								FILENAME_ORDERS_MATCHING => BOX_SALES_ORDERS_MATCHING,
								FILENAME_ORDERS_LISTING => BOX_SALES_ORDERS_LISTING
							);
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
	  	$heading[] = array(	'text'  => BOX_HEADING_SALES,
	                     	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=sales'));
		
	  	if ($selected_box == 'sales') {
	    	$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_STATS_ORDERS_TRACKING, BOX_SALES_ACTIVE_ORDERS) . 
											tep_admin_files_boxes(FILENAME_STATS_ORDERS_COMMENT, BOX_SALES_PREDEFINED_COMMENTS) . 
											tep_admin_files_boxes(FILENAME_ORDERS_TAGS, BOX_SALES_ORDERS_TAGS) .
											tep_admin_files_boxes(FILENAME_CUSTOMERS_ORDER_ACTIVITIES, BOX_SALES_CUSTOMERS_ORDER_ACTIVITIES) .
											tep_admin_files_boxes(FILENAME_ORDERS_MATCHING, BOX_SALES_ORDERS_MATCHING) .
											tep_admin_files_boxes(FILENAME_ORDERS_LISTING, BOX_SALES_ORDERS_LISTING)
								);
	  	}
		
		echo '<tr>
				<td>';
	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);
	  	echo '	</td>
			  </tr>';
	}
?>
<!-- sales_eof //-->