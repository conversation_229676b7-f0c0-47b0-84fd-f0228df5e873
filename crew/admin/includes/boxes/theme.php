<!-- theme //-->
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array (	FILENAME_THEME_CUSTOMIZING => BOX_THEME_CUSTOMIZING, FILENAME_THEME_ASSIGNATION => BOX_THEME_ASSIGNATION, FILENAME_CSS_GENERATOR => BOX_TOOLS_CSS_GENERATOR);
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
		$heading[] = array(	'text'  => BOX_HEADING_THEME,
        	             	'link'  => tep_href_link($filtered_files_array[0], 'selected_box=theme'));
		
		if ($selected_box == 'theme') {
			$gID_str = '';
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id IN (4, 200) AND visible = '1' ORDER BY configuration_group_title");
	    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	    		$gID_str .= $configuration_groups["cgID"] . ',';
	    	}
	    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
	  		
	    	$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_THEME_CUSTOMIZING, BOX_THEME_CUSTOMIZING) .
	    									tep_admin_files_boxes(FILENAME_THEME_ASSIGNATION, BOX_THEME_ASSIGNATION) .
	    									tep_admin_files_boxes(FILENAME_CSS_GENERATOR, BOX_TOOLS_CSS_GENERATOR) .
	    									tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
	    						);
	  	}
		
		echo '	<tr>
					<td>';
	  	$box = new box;
	  	echo $box->menuBox($heading, $contents);
	  	echo '		</td>
				</tr>';
	}
?>
<!-- theme_eof //-->