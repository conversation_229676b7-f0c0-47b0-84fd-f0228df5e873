<?
/*
  	$Id: tools.php,v 1.20 2016/02/02 08:47:25 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- tools //-->
<?
  	$heading = array();
  	$contents = array();
	
	$filenames_array = array ( 	FILENAME_AFT_AUTOMATION => BOX_TOOLS_AFT_AUTOMATION,
                                FILENAME_AFT_RULES => BOX_TOOLS_AFT_RULE,
								FILENAME_CACHE => BOX_TOOLS_CACHE, FILENAME_CSV_MANAGER => BOX_TOOLS_CSV_MANAGER,
								FILENAME_DB_ARCHIVE => BOX_TOOLS_DB_ARCHIVE, FILENAME_BACKUP => BOX_TOOLS_BACKUP, 
								FILENAME_BANNER_MANAGER => BOX_TOOLS_BANNER_MANAGER, FILENAME_DEFINE_LANGUAGE => BOX_TOOLS_DEFINE_LANGUAGE, 
								FILENAME_DOWNLOAD_CENTER => BOX_TOOLS_DOWNLOAD_CENTER, FILENAME_EMAIL_DOMAIN_LIST => BOX_ADMINISTRATOR_EMAIL_DOMAIN_LIST,
								FILENAME_PAGE_VIEW_MODULE => BOX_PAGE_VIEW_MODULE, FILENAME_SERVER_INFO => BOX_TOOLS_SERVER_INFO, 								
								FILENAME_UPGRADE => BOX_TOOLS_UPGRADE, FILENAME_WHOS_ONLINE => BOX_TOOLS_WHOS_ONLINE,
								FILENAME_SEO_META_TAG => BOX_SEO_META_TAG,FILENAME_IMAGE_UPLOAD => BOX_TOOLS_IMAGE_UPLOAD
								 );
	
	$filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
	
	if (count($filtered_files_array)) {
		echo '<tr>
				<td>';
  		$heading[] = array(	'text'  => BOX_HEADING_TOOLS,
    	                 	'link'  => tep_href_link((in_array(FILENAME_UPGRADE, $filtered_files_array) ? FILENAME_UPGRADE : $filtered_files_array[0]), 'selected_box=tools'));
	
  		if ($selected_box == 'tools') {
  			$gID_str = '';
	  		$configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE configuration_group_id IN (112) AND visible = '1' ORDER BY configuration_group_title");
	    	while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
	    		$gID_str .= $configuration_groups["cgID"] . ',';
	    	}
	    	if (substr($gID_str, -1) == ',')	$gID_str = substr($gID_str, 0, -1);
	  		
    		$contents[] = array('text'  => 	tep_admin_files_boxes(FILENAME_AFT_AUTOMATION, BOX_TOOLS_AFT_AUTOMATION) . 
                                            tep_admin_files_boxes(FILENAME_AFT_RULES, BOX_TOOLS_AFT_RULE) . 
                                                                                tep_admin_files_boxes(FILENAME_AFT_ACCOUNT_LIMIT, BOX_TOOLS_AFT_ACCOUNT_LIMIT) . 
    										tep_admin_files_boxes(FILENAME_CACHE, BOX_TOOLS_CACHE) . 
    										tep_admin_files_boxes(FILENAME_CSV_MANAGER, BOX_TOOLS_CSV_MANAGER) . 
    										tep_admin_files_boxes(FILENAME_DB_ARCHIVE, BOX_TOOLS_DB_ARCHIVE) .
    										tep_admin_files_boxes(FILENAME_BACKUP, BOX_TOOLS_BACKUP) .
	    									tep_admin_files_boxes(FILENAME_DEFINE_LANGUAGE, BOX_TOOLS_DEFINE_LANGUAGE) .
	    									tep_admin_files_boxes(FILENAME_DOWNLOAD_CENTER, BOX_TOOLS_DOWNLOAD_CENTER) .
	    									tep_admin_files_boxes(FILENAME_EMAIL_DOMAIN_LIST, BOX_ADMINISTRATOR_EMAIL_DOMAIN_LIST) .
	    									tep_admin_files_boxes(FILENAME_PAGE_VIEW_MODULE, BOX_PAGE_VIEW_MODULE) . 
		                                   	tep_admin_files_boxes(FILENAME_BANNER_MANAGER, BOX_TOOLS_BANNER_MANAGER) . 
											tep_admin_files_boxes(FILENAME_SEO_META_TAG, BOX_SEO_META_TAG).
											tep_admin_files_boxes(FILENAME_IMAGE_UPLOAD , BOX_TOOLS_IMAGE_UPLOAD).
		                                   	tep_admin_files_boxes(FILENAME_SERVER_INFO, BOX_TOOLS_SERVER_INFO) .
		                                   	tep_admin_files_boxes(FILENAME_UPGRADE, BOX_TOOLS_UPGRADE) .
		                                   	tep_admin_files_boxes(FILENAME_WHOS_ONLINE, BOX_TOOLS_WHOS_ONLINE) . 
		                                   	tep_admin_files_boxes(FILENAME_CONFIGURATION, BOX_CONFIGURATION_LINK, 'gID='.$gID_str)
	                       		);
  		}
	  	
	  	$box = new box;
  		echo $box->menuBox($heading, $contents);
  		
  		echo '	</td>
			 </tr>';
  	}
?>
<!-- tools_eof //-->