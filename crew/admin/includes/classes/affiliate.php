<?
/**************************************************
 PAP 4.0 Affiliate system integrate
**************************************************/
class affiliate {
	var $merchant_username, $merchant_password;
	
	function affiliate() {
		$merchant_login_select_sql = "  SELECT qga.username, qga.rpassword
            	                        FROM qu_g_authusers AS qga
                    	                INNER JOIN qu_g_users AS qgu
                            	                ON (qga.authid = qgu.authid)
										WHERE qgu.roleid = 'pap_merc'
											AND qgu.rstatus = 'A'
										LIMIT 1";
		$merchant_login_result_sql = tep_db_query($merchant_login_select_sql);
		$merchant_login_row = tep_db_fetch_array($merchant_login_result_sql);
		
		$this->merchant_username = $merchant_login_row['username'];
		$this->merchant_password = $merchant_login_row['rpassword'];
	}
	
	function affiliate_set_total_cost($order_id, $custom_products_type_id, $amount) {
		$totalcost_update_sql = "	UPDATE " . TABLE_QU_PAP_TRANSACTIONS . " 
									SET totalcost = '" . tep_db_input($amount) . "' 
									WHERE orderid = '" . (int)$order_id . "' 
										AND productid = '" . (int)$custom_products_type_id . "'";
		tep_db_query($totalcost_update_sql);
	}
	
	function affiliate_get_commission_status($order_id) {
		$rstatus_select_sql = "	SELECT rstatus 
								FROM " . TABLE_QU_PAP_TRANSACTIONS . " 
								WHERE orderid = '" . (int)$order_id . "'";
		$rstatus_result_sql = tep_db_query($rstatus_select_sql);
		$rstatus_row = tep_db_fetch_array($rstatus_result_sql);
		
		return $rstatus_row['rstatus'];
	}
	
	function affiliate_update_commission_status($order_id, $rstatus) {
		$rstatus_update = false;
		
		if ($rstatus == 'A') {
			$payoutstatus_select_sql = "	SELECT payoutstatus 
											FROM " . TABLE_QU_PAP_TRANSACTIONS . " 
											WHERE orderid = '" . (int)$order_id . "'";
			$payoutstatus_result_sql = tep_db_query($payoutstatus_select_sql);
			$payoutstatus_row = tep_db_fetch_array($payoutstatus_result_sql);
				
			if ($payoutstatus_row['payoutstatus'] == 'U') {
				$rstatus_update = true;
			}
		}
		
		if ($rstatus_update == true) {
			$rstatus_update_sql = "	UPDATE " . TABLE_QU_PAP_TRANSACTIONS . " 
									SET rstatus = '" . tep_db_input($rstatus) . "' 
									WHERE orderid = '" . (int)$order_id . "'";
			tep_db_query($rstatus_update_sql);
		}
	}
	
	function affiliate_update_commission($order_id) {
		global $messageStack;
		
		$pd_type_delivered_price_array = array();
		$product_delivered = false;
		
		$parent_op_info_select_sql = "	SELECT orders_products_id, products_good_delivered_price, custom_products_type_id 
										FROM " . TABLE_ORDERS_PRODUCTS . " 
										WHERE orders_id = '" . (int)$order_id . "'
											AND orders_products_is_compensate = 0 
											AND parent_orders_products_id = 0";
		$parent_op_info_result_sql = tep_db_query($parent_op_info_select_sql);
		while ($parent_op_info_row = tep_db_fetch_array($parent_op_info_result_sql)) {
			$child_op_info_select_sql = "	SELECT custom_products_type_id 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE parent_orders_products_id = '" . (int)$parent_op_info_row['orders_products_id'] . "' 
												AND orders_id = '" . (int)$order_id . "'";
			$child_op_info_result_sql = tep_db_query($child_op_info_select_sql);
			if ($child_op_info_row = tep_db_fetch_array($child_op_info_result_sql)) {
				$pd_type_delivered_price_array[$child_op_info_row['custom_products_type_id']] += $parent_op_info_row['products_good_delivered_price'];
			} else {
				$pd_type_delivered_price_array[$parent_op_info_row['custom_products_type_id']] += $parent_op_info_row['products_good_delivered_price'];
			}
			
			if ($parent_op_info_row['products_good_delivered_price'] > 0) {
				$product_delivered = true;
			}
		}
		
		if ($product_delivered) {
			foreach ($pd_type_delivered_price_array as $custom_products_type_id => $total_delivered) {
				$this->affiliate_set_total_cost($order_id, $custom_products_type_id, $total_delivered);
				$this->affiliate_compute_commission($order_id, $custom_products_type_id, $total_delivered);
			}
		} else {
/*--* 20111021 chingyen :: disable affiliate --*
			$affiliate_result = $this->affiliate_cancel_commission($order_id, 'full_refund_order');
/*--* 20111021 chingyen :: disable affiliate --*/
			
			if (isset($affiliate_result) && tep_not_null($affiliate_result)) {
				$affiliate_result_array = explode('&', $affiliate_result);
				
				for ($i = 0; $i < sizeof($affiliate_result_array); $i++) {
					if (substr($affiliate_result_array[$i], 0, 8) == 'res_code') {
						$res_code = substr($affiliate_result_array[$i], 9);
					} else if (substr($affiliate_result_array[$i], 0, 7) == 'message') {
						$aff_message = substr($affiliate_result_array[$i], 8);
					}
				}
				
				if ($res_code == 1) {
					$messageStack->add_session($aff_message, 'success');
				} else {
					$messageStack->add_session($aff_message, 'error');
				}
			}
		}
	}
	
	function affiliate_cancel_commission($order_id, $action) {
		$transid_select_sql = " SELECT transid, productid 
                                FROM qu_pap_transactions
                                WHERE orderid = '" . (int)$order_id . "'";
		$transid_result_sql = tep_db_query($transid_select_sql);
		while ($transid_row = tep_db_fetch_array($transid_result_sql)) {
			$data = 'username=' . $this->merchant_username . '&rpassword=' . $this->merchant_password . '&trans_id=' . $transid_row['transid']  . '&order_id=' . $order_id  . '&product_id=' . $transid_row['productid'] . '&action=' . $action;
			$url = tep_affiliate_href_link('scripts/ogm_crew_access.php');
			
			$ch = curl_init($url);
			
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		
			$response = curl_exec($ch);
			curl_close($ch);
			
			return $response;
		}
	}
	
	function affiliate_compute_commission($order_id, $custom_products_type_id, $pd_good_delivered_price) {
		$commtypeid_select_sql = "	SELECT qpt.transid, qpt.commtypeid, qpc.commissiongroupid 
									FROM " . TABLE_QU_PAP_TRANSACTIONS . " AS qpt 
									INNER JOIN qu_pap_commissiongroups AS qpc 
										ON (qpt.campaignid = qpc.campaignid) 
									WHERE orderid = '" . (int)$order_id . "' 
										AND productid = '" . (int)$custom_products_type_id . "'";
		$commtypeid_result_sql = tep_db_query($commtypeid_select_sql);
		if ($commtypeid_row = tep_db_fetch_array($commtypeid_result_sql)) {
			$commission_select_sql = "	SELECT commissionvalue, commissiontype 
										FROM qu_pap_commissions 
										WHERE commissiongroupid = '" . tep_db_input($commtypeid_row['commissiongroupid']) . "' 
											AND commtypeid = '" . tep_db_input($commtypeid_row['commtypeid']) . "' 
											AND tier = 1";
			$commission_result_sql = tep_db_query($commission_select_sql);
			if ($commission_row = tep_db_fetch_array($commission_result_sql)) {
				if ($commission_row['commissiontype'] == '%') {
					$commission = number_format((($pd_good_delivered_price * $commission_row['commissionvalue']) / 100), 2, '.', '');
				} else {
					$commission = $commission_row['commissionvalue'];
				}
				
				$update_commission_sql = "UPDATE " . TABLE_QU_PAP_TRANSACTIONS . " SET commission = '" . tep_db_input($commission) . "' WHERE transid = '" . tep_db_input($commtypeid_row['transid']) . "'";
				tep_db_query($update_commission_sql);
			}
		}
	}
}
?>