<?php

class aft_account_limit {

    public $identity, $identity_email, $rule_info;

    public function __construct($identity, $identity_email) {
        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin email
    }

    public function save_account_limit($input_array, &$messageStack) {

        $hidden_countries_id = tep_db_prepare_input($input_array['hidden_countries_id']);
        $countries_id = tep_db_prepare_input($input_array['countries_id']);
        $aft_limit = tep_db_prepare_input($input_array['aft_limit']);
        $aft_duration = tep_db_prepare_input($input_array['aft_duration']);


        if ($hidden_countries_id) {
            $aft_account_create_select_sql = "SELECT aft_limit, aft_duration, last_call_total FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " 
                                              WHERE  countries_id = '" . $hidden_countries_id . "'";
            $aft_account_create_res_sql = tep_db_query($aft_account_create_select_sql);

            if ($aft_account_row = tep_db_fetch_array($aft_account_create_res_sql)) {

                if ($aft_account_row['last_call_total'] > 0) {
                    $log_array = array(
                        'countries_id' => $hidden_countries_id,
                        'aft_limit' => $aft_account_row['aft_limit'],
                        'aft_duration' => $aft_account_row['aft_duration'],
                        'last_call_total' => $aft_account_row['last_call_total'],
                        'created_date_time' => 'now()',
                    );

                    tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT_LOG, $log_array);
                }
            }

            $data_array = array(
                'aft_limit' => $aft_limit,
                'aft_duration' => $aft_duration,
                'last_call' => 'now()',
                'next_call' => 'now()',
                'last_call_total' => 0,
                'last_modified_date_time' => 'now()',
            );


            if (tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT, $data_array, 'update', 'countries_id="' . tep_db_input($hidden_countries_id) . '"')) {
                $messageStack->add_session(MESSAGE_UPDATE_SUCCESS, 'success');
            }
        } else {
            $data_array = array(
                'countries_id' => $countries_id,
                'aft_limit' => $aft_limit,
                'aft_duration' => $aft_duration,
                'created_date_time' => 'now()',
                'last_call' => 'now()',
                'next_call' => 'now()',
            );

            $aft_account_count_sql = "SELECT countries_id FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " WHERE  countries_id = '" . $countries_id . "'";
            $aft_account_res_sql = tep_db_query($aft_account_count_sql);

            if (tep_db_num_rows($aft_account_res_sql) > 0) {
                $messageStack->add_session(MESSAGE_COUNTRY_EXISTS, 'error');
            } else {
                if (tep_db_perform(TABLE_AFT_ACCOUNT_CREATE_LIMIT, $data_array)) {
                    $messageStack->add_session(MESSAGE_INSERT_SUCCESS, 'success');
                }
            }
        }
    }

    public function delete_account_limit($input_array, &$messageStack) {
        global $memcache_obj;
        $countries_id = tep_db_prepare_input($input_array['countries_id']);
        $aft_account_delete_sql = "DELETE FROM " . TABLE_AFT_ACCOUNT_CREATE_LIMIT . " WHERE  countries_id = '" . $countries_id . "'";
        tep_db_query($aft_account_delete_sql);
        $messageStack->add_session(MESSAGE_DELETE_SUCCESS, 'success');
        return true;
    }

    

    

      
}
?>