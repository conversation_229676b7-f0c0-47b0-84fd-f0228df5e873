<?php

class aft_automation {

    private $aft_mode_array;
    
    function aft_automation(){
        $this->aft_mode_array = array(0 => TEXT_OFF,
            1 => TEXT_DEMO,
            2 => TEXT_LIVE
        );
    }
    
    function save($aft_automation_category_id) {
        global $login_id;

        $script_update = false;
        $mode_udpate = false;

        $aft_automation_code = tep_db_prepare_input($_POST['aft_code']);
        $aft_automation_mode = tep_db_prepare_input($_POST['aft_mode']);

        $aft_automation_select_sql = "	SELECT aft_automation_id, aft_automation_mode, aft_automation_code, aft_automation_version
										FROM " . TABLE_AFT_AUTOMATION . "
										WHERE aft_automation_category_id = '" . (int) $aft_automation_category_id . "'";
        $aft_automation_result_sql = tep_db_query($aft_automation_select_sql);
        if ($aft_automation_row = tep_db_fetch_array($aft_automation_result_sql)) {
            if ($aft_automation_code != $aft_automation_row['aft_automation_code']) {
                $aft_automation_id = $aft_automation_row['aft_automation_id'];
                $latest_version = $aft_automation_row['aft_automation_version'] + 0.1;
                $script_update = true;

                $sql_data_array = array('aft_automation_code' => $aft_automation_code,
                    'aft_automation_version' => $latest_version,
                    'admin_id' => $login_id,
                    'last_modified' => 'now()'
                );
            }

            if ($aft_automation_mode != $aft_automation_row['aft_automation_mode']) {
                $sql_data_array['aft_automation_mode'] = $aft_automation_mode;
                $mode_udpate = true;
            }
        }

        if ($mode_udpate == true || $script_update == true) {
            tep_db_perform(TABLE_AFT_AUTOMATION, $sql_data_array, 'update', " aft_automation_category_id = '" . (int) $aft_automation_category_id . "'");

            $message_type = 0;

            if ($script_update == true) {
                $sql_version_data_array = array('aft_automation_version' => $latest_version,
                    'aft_automation_id' => $aft_automation_id,
                    'aft_automation_version_code' => $aft_automation_code,
                    'changed_by' => $login_id,
                    'date_added' => 'now()'
                );
                tep_db_perform(TABLE_AFT_AUTOMATION_VERSION, $sql_version_data_array);

                $message = 'Scripts have been updated.';
            }

            if ($mode_udpate == true) {
                if ($script_update == true) {
                    $message .= '<br>';
                }

                $message .= ENTRY_STATUS . ' updated';
            }
        } else {
            $message_type = 1;
            $message = 'Script not updated, due to no changes have been made.';
        }

        header('Content-Type: text/xml');

        echo '	<result>
    				<message_type><![CDATA[' . $message_type . ']]></message_type>
					<message><![CDATA[' . $message . ']]></message>
					<aft_status><![CDATA[' . $this->aft_mode_array[$aft_automation_mode] . ']]></aft_status>
    			</result>';
        exit;
    }

    function anti_fraud_menu() {
        global $languages_id;

        $row_num = 0;

        $menu_html = '	<table border="0" cellspacing="1" cellpadding="2" align="center" width="25%">
							<tr>
								<td class="ordersBoxHeading">' . TABLE_HEADING_ORDER_STATUS . '</td>
								<td class="ordersBoxHeading" align="right">' . ENTRY_VERSION . '</td>
								<td class="ordersBoxHeading" align="center"></td>
								<td class="ordersBoxHeading" align="center">' . TABLE_HEADING_ACTION . '</td>
							</tr>';

        $aft_automation_cat_select_sql = "	SELECT aac.*, os.orders_status_name, aac.aft_automation_category_id, aa.aft_automation_version, aa.aft_automation_mode
											FROM " . TABLE_AFT_AUTOMATION_CATEGORY . " AS aac
											INNER JOIN " . TABLE_ORDERS_STATUS . " AS os
												ON (aac.aft_automation_category_key = os.orders_status_id)
											INNER JOIN " . TABLE_AFT_AUTOMATION . " AS aa
												ON (aa.aft_automation_category_id = aac.aft_automation_category_id)
											WHERE aft_automation_category_type = 'C'";
        $aft_automation_cat_result_sql = tep_db_query($aft_automation_cat_select_sql);
        while ($aft_automation_cat_row = tep_db_fetch_array($aft_automation_cat_result_sql)) {
            $row_class = (($row_num % 2) == 0) ? 'reportListingOdd' : 'reportListingEven';

            $menu_html .= '	<tr class="' . $row_class . '">
								<td class="main">' . $aft_automation_cat_row['orders_status_name'] . '</td>
								<td class="main">' . $aft_automation_cat_row['aft_automation_version'] . '</td>
								<td align="center">' . tep_3_switch_image((int) $aft_automation_cat_row['aft_automation_mode'], tep_href_link(FILENAME_AFT_AUTOMATION, tep_get_all_get_params(array('action', 'aac_id', 'aft_mode')) . 'action=set_aft_mode&aac_id=' . $aft_automation_cat_row['aft_automation_category_id'] . '&aft_mode=2'), tep_href_link(FILENAME_AFT_AUTOMATION, tep_get_all_get_params(array('action', 'aac_id', 'aft_mode')) . 'action=set_aft_mode&aac_id=' . $aft_automation_cat_row['aft_automation_category_id'] . '&aft_mode=1'), tep_href_link(FILENAME_AFT_AUTOMATION, tep_get_all_get_params(array('action', 'aac_id', 'aft_mode')) . 'action=set_aft_mode&aac_id=' . $aft_automation_cat_row['aft_automation_category_id'] . '&aft_mode=0')) . '</td>
								<td align="center">
									<a href="' . tep_href_link(FILENAME_AFT_AUTOMATION, 'action=aft_config&aac_id=' . $aft_automation_cat_row['aft_automation_category_id']) . '">' . tep_image(DIR_WS_ICONS . 'edit.gif', 'Edit', 14, 13, 'align="top"') . '</a>
								</td>
							</tr>';

            $row_num++;
        }

        $menu_html .= '	</table>';

        return $menu_html;
    }

    function anti_fraud_config($aft_automation_category_id) {
        $aft_automation_select_sql = "	SELECT aft_automation_mode, aft_automation_code
										FROM " . TABLE_AFT_AUTOMATION . "
										WHERE aft_automation_category_id = '" . (int) $aft_automation_category_id . "'";
        $aft_automation_result_sql = tep_db_query($aft_automation_select_sql);
        $aft_automation_row = tep_db_fetch_array($aft_automation_result_sql);


        ob_start();
        ?>
        <script language="javascript">
            editAreaLoader.init({
                id: 'aft_code'	// id of the textarea to transform
                , start_highlight: true	// if start with highlight
                , allow_resize: 'both'
                , allow_toggle: false
                , language: 'en'
                , syntax: 'php'
            });

            String.prototype.count = function(s1) {
                return (this.length - this.replace(new RegExp(s1, 'g'), '').length) / s1.length;
            }

            function compile_script(aac_id, aft_mode) {
                var script_content = editAreaLoader.getValue('aft_code');

                var error = false;
                var message = '';
                var myRegExp = /((^for)(\s+)?(\())|((\s+)(for)(\s+)?(\())|((^while)(\s+)?(\())|((\s+)(while)(\s+)?(\())/;
                var lines = script_content.split('\n');

                jquery_confirm_box('Compiling...');

                for (var line_cnt = 0; line_cnt < lines.length; line_cnt++) {
                    var matchPos1 = lines[line_cnt].search(myRegExp);

                    if (matchPos1 != -1) {
                        error = true;
                        message += 'There is a looping in line# ' + (line_cnt + 1) + '<br>';
                    }
                }

                no_op_bracket = script_content.count('{');
                no_cl_bracket = script_content.count('}');

                if (no_op_bracket != no_cl_bracket) {
                    error = true;

                    if (no_op_bracket > no_cl_bracket) {
                        message += '} expected in line# ' + (lines.length) + '<br>';
                    } else if (no_op_bracket < no_cl_bracket) {
                        var myRegExp = /(\})/;

                        for (var line_cnt = 0; line_cnt < lines.length; line_cnt++) {
                            var cl_bracket_match_pos = lines[line_cnt].search(myRegExp);

                            if (cl_bracket_match_pos != -1) {
                                error = true;

                                last_cl_bracket_line = line_cnt + 1;
                            }
                        }

                        message += 'Unexpected } in line# ' + last_cl_bracket_line + '<br>';
                    }
                }

                if (error == true) {
                    jquery_confirm_box(message, 1, 2);
                } else {
                    jQuery.post('<?= tep_href_link(FILENAME_AFT_AUTOMATION, "action=save&aac_id='+aac_id+'") ?>', {aft_code: script_content, aft_mode: aft_mode}, function(data) {
                        jQuery(data).find('result').each(function() {
                            var msg = jQuery('message', this).text();
                            var msg_type = jQuery('message_type', this).text();
                            var aft_status = jQuery('aft_status', this).text();

                            jquery_confirm_box(msg, 1, msg_type);
                            jQuery('#status_div').html(aft_status);
                        });
                    });
                }

                return false;
            }
        </script>
        <?= tep_draw_form('aft_form', FILENAME_AFT_AUTOMATION, 'action=save', 'post') ?>
        <table border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td></td>
                <td>
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="main"><?= ENTRY_STATUS . ':&nbsp;' ?></td>
                            <td class="main">
                                <div id="status_div"><?= $this->aft_mode_array[$aft_automation_row['aft_automation_mode']] ?></div>
                            </td>
                        </tr>
                    </table>
                </td>
                <td colspan="2"><?= tep_submit_button(BUTTON_GO_LIVE, ALT_BUTTON_GO_LIVE, ' onclick="return compile_script(' . $aft_automation_category_id . ', 2);" style="padding: 5px;" ', 'greenButton') ?></td>
            </tr>
            <tr>
                <td colspan="4"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>
            <tr>
                <td><?= tep_draw_separator('pixel_trans.gif', '101', '1') ?></td>
                <td align="center"><?= tep_draw_textarea_field('aft_code', false, 118, 50, $aft_automation_row['aft_automation_code'], ' id="aft_code" ') ?></td>
                <td>&nbsp;</td>
                <td valign="top">
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="main"><?= ENTRY_FUNCTION ?></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <?php
                                            $aft_function_array = array();
                                            $aft_function_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
                                            $aft_function_array[] = array('id' => '', 'text' => 'Non-action Functions:', 'param' => 'disabled');

                                            $non_action_functions_select_sql = "	SELECT aft_functions_id, aft_functions_name
                                            FROM " . TABLE_AFT_FUNCTIONS . "
                                            WHERE aft_functions_action = 0";
                                            $non_action_functions_result_sql = tep_db_query($non_action_functions_select_sql);
                                            while ($non_action_functions_row = tep_db_fetch_array($non_action_functions_result_sql)) {
                                            $aft_function_array[] = array('id' => $non_action_functions_row['aft_functions_id'], 'text' => '&nbsp;&nbsp;&nbsp;' . $non_action_functions_row['aft_functions_name']);
                                            }

                                            $aft_function_array[] = array('id' => '', 'text' => 'Action Functions:', 'param' => 'disabled');

                                            $action_functions_name_select_sql = "	SELECT aft_functions_id, aft_functions_name
                                            FROM " . TABLE_AFT_FUNCTIONS . "
                                            WHERE aft_functions_action = 1";
                                            $action_functions_name_result_sql = tep_db_query($action_functions_name_select_sql);
                                            while ($action_functions_name_row = tep_db_fetch_array($action_functions_name_result_sql)) {
                                            $aft_function_array[] = array('id' => $action_functions_name_row['aft_functions_id'], 'text' => '&nbsp;&nbsp;&nbsp;' . $action_functions_name_row['aft_functions_name']);
                                            }

                                            echo tep_draw_pull_down_menu('functions', $aft_function_array, '', ' id="functions" onchange="getFunctionConfig(this.value);"');
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="4"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>
            <tr>
                <td></td>
                <td colspan="2" align="right">
                    <table border="0" cellspacing="0" cellpadding="0" width="100%">
                        <tr>
                            <td><?= tep_submit_button(BUTTON_OFF, ALT_BUTTON_OFF, ' onclick="return compile_script(' . $aft_automation_category_id . ', 0);" style="padding: 5px;" ', 'redButton') ?></td>
                            <td align="right"><?= tep_submit_button(BUTTON_PUBLISH_TO_DEMO, ALT_BUTTON_PUBLISH_TO_DEMO, ' onclick="return compile_script(' . $aft_automation_category_id . ', 1);" style="padding: 5px;" ', 'inputButton') ?></td>
                        </tr>
                    </table>
                </td>
                <td></td>
            </tr>
            <tr>
                <td colspan="4"><?= tep_draw_separator('pixel_trans.gif', '1', '15') ?></td>
            </tr>
        </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

}
?>