<?php

class aft_rule {
    private $aft_obj = null;
    private $order_container = array();
	public $rule_log_key, $rule_log = array();
    
	function __construct() {}
    
    private function set_rule_log($log) {
        if (!$this->rule_log_key) {
            $this->rule_log_key = 0;
        }
        
        $this->rule_log[$this->rule_log_key][] = $log;
    }
    
    private function get_order_info($order_id, $field = null, $return_string = '') {
        if (!isset($this->order_container[$order_id]['o'])) {
            $id_select_sql = "	SELECT payment_methods_id, customers_country_international_dialing_code
                                FROM " . TABLE_ORDERS . " 
                                WHERE orders_id = '" . $order_id . "'";
            $id_result_sql = tep_db_query($id_select_sql);
            if ($row = tep_db_fetch_array($id_result_sql)) {
                $this->order_container[$order_id]['o'] = $row;
            }
        }
        
        if (isset($this->order_container[$order_id]['o']) && $field) {
            $return_string = isset($this->order_container[$order_id]['o'][$field]) ? $this->order_container[$order_id]['o'][$field] : $return_string;
        }
        
        return $return_string;
    }
    
    private function get_product_cat_id_path($products_id) {
        $return_array = array();
        
        $product_path_select_sql = "SELECT products_cat_id_path
                                    FROM " . TABLE_PRODUCTS . "
                                    WHERE products_id = '" . $products_id . "'";
        $product_path_result_sql = tep_db_query($product_path_select_sql);
        if ($product_path_row = tep_db_fetch_array($product_path_result_sql)) {
            $return_array = explode('_', $product_path_row['products_cat_id_path']);
        }
        
        return $return_array;
    }
    
    private function getAntiFraudObj($order_id) {
        if (!$this->aft_obj) {
            require_once('anti_fraud.php');
            $this->aft_obj = new anti_fraud($order_id);
        }
        
        return $this->aft_obj;
    }
    
    private function get_kount_info($order_id, $field = null, $return_string = '') {
        if (!isset($this->order_container[$order_id]['tm'])) {
            if ($tm_array = $this->getAntiFraudObj($order_id)->getAftModule()->get_db_tbl_transaction_id_array()) {
                $this->order_container[$order_id]['tm'] = $this->getAntiFraudObj($order_id)->getAftModule()->get_device_info_by_query_id($tm_array['api_tm_query_id']);
            }
        }
        
        if (isset($this->order_container[$order_id]['tm']) && $field) {
            $return_string = isset($this->order_container[$order_id]['tm'][$field]) ? $this->order_container[$order_id]['tm'][$field] : $return_string;
        }
        
        return $return_string;
    }

    private function get_order_product_info($order_id, $return_string = array()) {
        if (!isset($this->order_container[$order_id]['op'])) {
            $select_sql = "	SELECT orders_products_id, products_id
                            FROM " . TABLE_ORDERS_PRODUCTS . " 
                            WHERE orders_id = '" . $order_id . "'
                                AND parent_orders_products_id = 0";
            $result_sql = tep_db_query($select_sql);
            while ($row = tep_db_fetch_array($result_sql)) {
                $select_sql2 = "	SELECT products_id
                                    FROM " . TABLE_ORDERS_PRODUCTS . " 
                                    WHERE parent_orders_products_id = '" . $row['orders_products_id'] . "'";
                $result_sql2 = tep_db_query($select_sql2);
                if (tep_db_num_rows($result_sql2)) {
                    while ($row2 = tep_db_fetch_array($result_sql2)) {
                        $this->order_container[$order_id]['op'][$row2['products_id']] = array(
                            'cPath' => $this->get_product_cat_id_path($row2['products_id']),
                        );
                    }
                } else {
                    $this->order_container[$order_id]['op'][$row['products_id']] = array(
                        'cPath' => $this->get_product_cat_id_path($row['products_id']),
                    );
                }
            }
        }
        
        if (isset($this->order_container[$order_id]['op'])) {
            $return_string = $this->order_container[$order_id]['op'];
        }
        
        return $return_string;
    }
    
    # check phone country
    private function rule_dailing_code($order_id, $rule_criteria) {
        $return_bool = false;
        $input_country_dialing_code = isset($rule_criteria['country_dialing_code']) ? (int) $rule_criteria['country_dialing_code'] : 0;
        $dailing_code = $this->get_order_info($order_id, 'customers_country_international_dialing_code');
        
        if ($dailing_code == $input_country_dialing_code) {
            $return_bool = true;
        }

        $this->set_rule_log(array(
            'key' => 'rule_dailing_code',
            'msg' => 'Dailing code: ' . $dailing_code . ' match rule (' . $input_country_dialing_code . '): ' . ($return_bool ? 'true' : 'false'),
        ));
        
        return $return_bool;
    }
    
    # check broswer language
    private function rule_browser_language($order_id, $rule_criteria) {
        $return_bool = false;
        $input_browser_language_empty = isset($rule_criteria['browser_language_empty']) ? $rule_criteria['browser_language_empty'] : 0;
        $input_browser_language = isset($rule_criteria['browser_language']) && !empty($rule_criteria['browser_language']) ? explode('||', $rule_criteria['browser_language']) : array();
        $browser_array = $this->get_kount_info($order_id, 'browser', array());
        
        if ($input_browser_language) {
            if (isset($browser_array['browser_language'])) {
                if ($browser_array['browser_language']) {
                    foreach ($input_browser_language as $find_bl) {
                        if ($browser_array['browser_language'] == trim($find_bl)) {
                            $return_bool = true;
                            break;
                        }
                    }
                } else if ($input_browser_language_empty) {
                    $return_bool = true;
                }

                $this->set_rule_log(array(
                    'key' => 'rule_browser_language',
                    'msg' => 'Browser Lang: ' . $browser_array['browser_language'] . ' match rule (' . implode(',', $input_browser_language) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            } else {
                $return_bool = true;

                $this->set_rule_log(array(
                    'key' => 'rule_browser_language',
                    'msg' => 'Browser Lang: NULL match rule (' . implode(',', $input_browser_language) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            }
        } else {
            $return_bool = true;
            $browser_lang = isset($browser_array['browser_language']) ? $browser_array['browser_language'] : 'null';
            
            if (isset($browser_array['browser_language'])) {
                if ($browser_array['browser_language']) {
                } else if ($input_browser_language_empty) {
                } else {
                    $return_bool = false;
                }
            }
            
            $this->set_rule_log(array(
                'key' => 'rule_browser_language',
                'msg' => 'Browser Lang: ' . $browser_lang . ' match rule ( empty ): ' . ($return_bool ? 'true' : 'false'),
            ));
        }
        
        return $return_bool;
    }
    
    # check screen resolution
    private function rule_screen_resolution($order_id, $rule_criteria) {
        $return_bool = false;
        $input_screen_resolution_empty = isset($rule_criteria['screen_resolution_empty']) ? $rule_criteria['screen_resolution_empty'] : 0;
        $input_screen_resolution = isset($rule_criteria['screen_resolution']) && !empty($rule_criteria['screen_resolution']) ? explode('||', $rule_criteria['screen_resolution']) : array();
        $browser_array = $this->get_kount_info($order_id, 'device', array());
        
        if ($input_screen_resolution) {
            if (isset($browser_array['screen_res'])) {
                if ($browser_array['screen_res']) {
                    foreach ($input_screen_resolution as $find_sr) {
                        if ($browser_array['screen_res'] == trim($find_sr)) {
                            $return_bool = true;
                            break;
                        }
                    }
                } else if ($input_screen_resolution_empty) {
                    $return_bool = true;
                }

                $this->set_rule_log(array(
                    'key' => 'rule_screen_resolution',
                    'msg' => 'Screen Res: ' . $browser_array['screen_res'] . ' match rule (' . implode(',', $input_screen_resolution) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            } else {
                $return_bool = true;

                $this->set_rule_log(array(
                    'key' => 'rule_screen_resolution',
                    'msg' => 'Screen Res: NULL match rule (' . implode(',', $input_screen_resolution) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            }
        } else {
            $return_bool = true;
            $screen_res = isset($browser_array['screen_res']) ? $browser_array['screen_res'] : 'null';
            
            if (isset($browser_array['screen_res'])) {
                if ($browser_array['screen_res']) {
                } else if ($input_screen_resolution_empty) {
                } else {
                    $return_bool = false;
                }
            }
            
            $this->set_rule_log(array(
                'key' => 'rule_screen_resolution',
                'msg' => 'Screen Res: ' . $screen_res . ' match rule ( empty ): ' . ($return_bool ? 'true' : 'false'),
            ));
        }
        
        return $return_bool;
    }
    
    # check operating system
    private function rule_operating_system($order_id, $rule_criteria) {
        $return_bool = false;
        $input_operating_system_empty = isset($rule_criteria['operating_system_empty']) ? $rule_criteria['operating_system_empty'] : 0;
        $input_operating_system = isset($rule_criteria['operating_system']) && !empty($rule_criteria['screen_resolution']) ? explode('||', $rule_criteria['operating_system']) : array();
        $browser_array = $this->get_kount_info($order_id, 'device', array());
        
        if ($input_operating_system) {
            if (isset($browser_array['os'])) {
                if ($browser_array['os']) {
                    foreach ($input_operating_system as $find_os) {
                        if ($browser_array['os'] == trim($find_os)) {
                            $return_bool = true;
                            break;
                        }
                    }
                } else if ($input_operating_system_empty) {
                    $return_bool = true;
                }

                $this->set_rule_log(array(
                    'key' => 'rule_operating_system',
                    'msg' => 'OS: ' . $browser_array['os'] . ' match rule (' . implode(',', $input_operating_system) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            } else {
                $return_bool = true;

                $this->set_rule_log(array(
                    'key' => 'rule_operating_system',
                    'msg' => 'OS: NULL match rule (' . implode(',', $input_operating_system) . '): ' . ($return_bool ? 'true' : 'false'),
                ));
            }
        } else {
            $return_bool = true;
            $os = isset($browser_array['os']) ? $browser_array['os'] : 'null';
            
            if (isset($browser_array['os'])) {
                if ($browser_array['os']) {
                } else if ($input_operating_system_empty) {
                } else {
                    $return_bool = false;
                }
            }
            
            $this->set_rule_log(array(
                'key' => 'rule_operating_system',
                'msg' => 'OS: ' . $os . ' match rule ( empty ): ' . ($return_bool ? 'true' : 'false'),
            ));
        }
        
        return $return_bool;
    }
    
    # javascript enabled
    private function rule_javascript_enabled($order_id, $rule_criteria) {
//        $return_bool = false;
//        $input_javascript_enabled = isset($rule_criteria['javascript_enabled']) ? $rule_criteria['javascript_enabled'] : 0;
//        $browser_array = $this->get_kount_info($order_id, 'browser', array());
//        
//        if (isset($browser_array['enabled_js'])) {
//            $enabled_js = $browser_array['enabled_js'] !== '' ? $browser_array['enabled_js'] : 'N';
//        } else {
//            $enabled_js = 'NULL';
//        }
//        
//        if (in_array(strtoupper($enabled_js), array('Y', 'YES'))) {
//            if ($input_javascript_enabled) {
//                $return_bool = true;
//            }
//        } else {
//            if (!$input_javascript_enabled) {
                $return_bool = true;
//            }
//        }
//        
//        $this->set_rule_log(array(
//            'key' => 'rule_javascript_enabled',
//            'msg' => 'enabled_js: ' . $enabled_js . ' match rule (javascript enabled: ' . ($input_javascript_enabled ? 'Y' : 'N') . '): ' . ($return_bool ? 'true' : 'false'),
//        ));
        
        return $return_bool;
    }
    
    # flash enabled
    private function rule_flash_enabled($order_id, $rule_criteria) {
//        $return_bool = false;
//        $input_flash_enabled = isset($rule_criteria['flash_enabled']) ? $rule_criteria['flash_enabled'] : 0;
//        $browser_array = $this->get_kount_info($order_id, 'browser', array());
//        
//        if (isset($browser_array['enabled_fl'])) {
//            $enabled_fl = $browser_array['enabled_fl'] !== '' ? $browser_array['enabled_fl'] : 'N';
//        } else {
//            $enabled_fl = 'NULL';
//        }
//        
//        if (in_array(strtoupper($enabled_fl), array('Y', 'YES'))) {
//            if ($input_flash_enabled) {
                $return_bool = true;
//            }
//        } else {
//            if (!$input_flash_enabled) {
//            $return_bool = true;
//            }
//        }
//        
//        $this->set_rule_log(array(
//            'key' => 'rule_flash_enabled',
//            'msg' => 'enabled_fl: ' . $enabled_fl . ' match rule (flash enabled: ' . ($input_flash_enabled ? 'Y' : 'N') . '): ' . ($return_bool ? 'true' : 'false'),
//        ));
        
        return $return_bool;
    }
    
    # cookies enabled
    private function rule_cookies_enabled($order_id, $rule_criteria) {
//        $return_bool = false;
//        $input_cookies_enabled = isset($rule_criteria['cookies_enabled']) ? $rule_criteria['cookies_enabled'] : 0;
//        $browser_array = $this->get_kount_info($order_id, 'browser', array());
//        
//        if (isset($browser_array['enabled_ck'])) {
//            $enabled_ck = $browser_array['enabled_ck'] !== '' ? $browser_array['enabled_ck'] : 'N';
//        } else {
//            $enabled_ck = 'NULL';
//        }
//        
//        if (in_array(strtoupper($enabled_ck), array('Y', 'YES'))) {
//            if ($input_cookies_enabled) {
                $return_bool = true;
//            }
//        } else {
//            if (!$input_cookies_enabled) {
//                $return_bool = true;
//            }
//        }
//        
//        $this->set_rule_log(array(
//            'key' => 'rule_cookies_enabled',
//            'msg' => 'enabled_ck: ' . $enabled_ck . ' match rule (cookies enabled: ' . ($input_cookies_enabled ? 'Y' : 'N') . '): ' . ($return_bool ? 'true' : 'false'),
//        ));
        
        return $return_bool;
    }
    
    # payment method
    private function rule_payment_method($order_id, $rule_criteria) {
        $return_bool = false;
        $input_payment_methods_id = isset($rule_criteria['payment_methods_id']) ? $rule_criteria['payment_methods_id'] : array();
        $order_pm = $this->get_order_info($order_id, 'payment_methods_id');

        if (in_array($order_pm, $input_payment_methods_id)) {
            $return_bool = true;
        }
        
        $this->set_rule_log(array(
            'key' => 'rule_payment_method',
            'msg' => 'payment method: ' . $order_pm . ' match rule (' . implode(',', $input_payment_methods_id) . '): ' . ($return_bool ? 'true' : 'false'),
        ));
        
        return $return_bool;
    }
    
    # check category id
    private function rule_category_id($order_id, $rule_criteria) {
        $return_bool = false;
        $msg = '';
        
        $input_cat_id_array = (isset($rule_criteria['cat_id']) && $rule_criteria['cat_id']) ? explode(',', $rule_criteria['cat_id']) : array();
        $order_products_array = $this->get_order_product_info($order_id);
        
        foreach ($input_cat_id_array as $raw_cid) {
            $cid = trim($raw_cid);
            
            foreach ($order_products_array as $product_id => $p_arr) {
                if (in_array($cid, $p_arr['cPath'])) {
                    $msg .= 'product_id: ' . $product_id . '  with cpath ' . implode('_', $p_arr['cPath']) . ') ';
                    
                    $return_bool = true;
                    break 2;
                }
            }
        }
        
        $this->set_rule_log(array(
            'key' => 'rule_category_id',
            'msg' => $msg . 'match rule (' . $rule_criteria['cat_id'] . '): ' . ($return_bool ? 'true' : 'false'),
        ));
        
        return $return_bool;
    }
    
    private function all_criteria_matched($order_id, $rule_criteria) {
        $return_int = 0;

        # check phone country
        if ($this->rule_dailing_code($order_id, $rule_criteria) &&
                # check category id
                $this->rule_category_id($order_id, $rule_criteria) &&
                # check broswer language
                $this->rule_browser_language($order_id, $rule_criteria) &&
                # check screen resolution
                $this->rule_screen_resolution($order_id, $rule_criteria) &&
                # check operating system
                $this->rule_operating_system($order_id, $rule_criteria) &&
                # javascript enabled
                $this->rule_javascript_enabled($order_id, $rule_criteria) &&
                # flash enabled
                $this->rule_flash_enabled($order_id, $rule_criteria) &&
                # cookies enabled
                $this->rule_cookies_enabled($order_id, $rule_criteria) &&
                # payment method
                $this->rule_payment_method($order_id, $rule_criteria)
                ) {
            $return_int = 1;
        }
        
        return $return_int;
    }
    
    public function rules_matched($order_id) {
        $return_bool = false;
        $return_description = '';
        $trans_identifier = $this->get_kount_info($order_id, 'transaction_identifier');
        
        # initial check 
        # - to ensure order is paid by using PG
        # - to ensure tm/kount data available
        if ($this->get_order_info($order_id, 'payment_methods_id', 0) == 0) {
             $return_description = 'Store Credit Payment';
        } else if ($trans_identifier['transaction_id'] == 'NA') {
            $return_description = 'TM/Kount info missing';
        } else {
            $id_desc_select_sql = "	SELECT aft_rule_id AS id, aft_rule_name AS description, aft_rule_value  
                                    FROM " . TABLE_AFT_RULE . "
                                    ORDER BY aft_rule_sort_order DESC";
            $id_desc_result_sql = tep_db_query($id_desc_select_sql);
            while ($id_desc_rows = tep_db_fetch_array($id_desc_result_sql)) {
                $rule_criteria = json_decode($id_desc_rows['aft_rule_value'], true);
                $this->rule_log_key = $id_desc_rows['description'];
                
                if ($result = $this->all_criteria_matched($order_id, $rule_criteria)) {
                    $return_description = $id_desc_rows['description'];
                    $return_bool = true;
                    break;
                }
            }
        }
        
        return array(
            $return_bool,
            $return_description,
        );
    }
	
	public function menuListing() {
		$listing_obj = array();
		
		$id_desc_select_sql = "	SELECT aft_rule_id AS id, aft_rule_name AS description, aft_rule_sort_order  
								FROM " . TABLE_AFT_RULE . "
								ORDER BY aft_rule_sort_order DESC";
		$id_desc_result_sql = tep_db_query($id_desc_select_sql);
		while ($id_desc_rows = tep_db_fetch_array($id_desc_result_sql)) {
            $row_obj = array (	
                'id' => $id_desc_rows['id'],
                'description' => $id_desc_rows['description'],
                'aft_rule_sort_order' => $id_desc_rows['aft_rule_sort_order'],
            );
			
			$listing_obj[] = $row_obj;
		}
		
		ob_start();
?>
		<script language="javascript">
		<!--
		function deleteentry(t,id) {
			answer = confirm('Are you sure to delete ' + '"' + t + '"' + ' record?')
			if (answer !=0) { 
				jQuery.get("?action=delete&id="+id, function(data){
					if (data == "success")
			 			jQuery('#row-'+id).fadeOut('slow');
				});
			}
		}
		//-->
		</script>
		
		<form name=listing>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class=main align="left"></td>
								<td align="right">[ <a href="?action=add_form" ><?php echo LINK_ADD_SETTING; ?></a> ]</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign="top">
						<table border="0" width="100%" cellspacing="1" cellpadding="2">
		 					<tr>
			 					<td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_DESCRIPTION; ?></td>
                                <td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_SORT_ORDER; ?></td>
		 						<td class="reportBoxHeading" width="5%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
		 					</tr>
							<?php
							if (tep_not_null($listing_obj)) {
								$entryCount = 0;
								
								foreach ($listing_obj as $rows) {
									$entryCount++;
									
									($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
?>
								<tr id="row-<?php echo $rows['id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
									<td class="reportRecords" valign="top"><?php echo $rows['description']?></td>
                                    <td class="reportRecords" valign="top" align=center><?php echo $rows['aft_rule_sort_order']?></td>
									<td class="reportRecords" valign="top" align=center>
										<a href="?action=add_form&id=<?php echo $rows['id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
										<a href="javascript:void(deleteentry('<?php echo htmlentities($rows['description']); ?>','<?php echo htmlentities($rows['id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
									</td>
								</tr>
<?php
								}
							} else {
?>
								<tr class="reportListingEven">
									<td class="reportRecords" align="center" colspan="3"><i>Empty</i></td>
								</tr>
<?php
							}
?>
						</table>
		 			</td>
		 		</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	function addForm($id = "") {
        $id = (int) $id;
        
        $input_description = isset($_POST['description']) ? tep_db_prepare_input($_POST['description']) : '';
        $input_country_dialing_code_id = isset($_POST['country_dialing_code_id']) ? (int) $_POST['country_dialing_code_id'] : 0;
        $input_cat_id = isset($_POST['cat_id']) ? tep_db_prepare_input($_POST['cat_id']) : 0;
        $input_payment_methods_id = isset($_POST['payment_methods_id']) ? tep_db_prepare_input($_POST['payment_methods_id']) : array();
        $input_sort_order = isset($_POST['sort_order']) ? (int) $_POST['sort_order'] : 0;
        $input_javascript_enabled = isset($_POST['javascript_enabled']) ? 1 : 0;
        $input_flash_enabled = isset($_POST['flash_enabled']) ? 1 : 0;
        $input_cookies_enabled = isset($_POST['cookies_enabled']) ? 1 : 0;
        $input_browser_language_empty = isset($_POST['browser_language_empty']) ? 1 : 0;
        $input_browser_language = isset($_POST['browser_language']) ? tep_db_prepare_input($_POST['browser_language']) : '';
        $input_screen_resolution_empty = isset($_POST['screen_resolution_empty']) ? 1 : 0;
        $input_screen_resolution = isset($_POST['screen_resolution']) ? tep_db_prepare_input($_POST['screen_resolution']) : '';
        $input_operating_system_empty = isset($_POST['operating_system_empty']) ? 1 : 0;
        $input_operating_system = isset($_POST['operating_system']) ? tep_db_prepare_input($_POST['operating_system']) : '';
        
        if ($id) {
			$id_select_sql = "	SELECT aft_rule_name, aft_rule_sort_order, aft_rule_value
								FROM " . TABLE_AFT_RULE . " 
								WHERE aft_rule_id = '" . $id . "'";
			$id_result_sql = tep_db_query($id_select_sql);
	 		if ($row = tep_db_fetch_array($id_result_sql)) {
                $input_description = $row['aft_rule_name'];
                $input_sort_order = $row['aft_rule_sort_order'];
                $input_json = json_decode($row['aft_rule_value'], true);
                
                foreach ($input_json as $key => $value) {
                    $f = 'input_' . $key;
                    $$f = $value;
                }
            }
		}
        
        $payment_gateways_array = array();
//        $payment_gateways_array['method'][SYSTEM_PAYMENT_STORE_CREDITS] = 'Store Credits';
        
        /*-- payment method --*/
        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id, payment_methods_legend_color 
                                        FROM " . TABLE_PAYMENT_METHODS . "
                                        WHERE payment_methods_receive_status = '1' 
                                        ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            if ($payment_methods_row['payment_methods_parent_id']>0) {
                $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
            } else {
                $payment_gateways_array['method'][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                $payment_gateways_array['display_colour'][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_legend_color'] ? $payment_methods_row['payment_methods_legend_color'] : "#E1E1E2";
            }
        }
		
		ob_start();
?>
		<script language="javascript">
		<!--
		function check_form() {
            return true;
		}
        
        function getReturnedValue(received_val, input_field) {
            if (jQuery('#'+input_field).val()) {
                received_val = jQuery('#'+input_field).val() + ',' + received_val;
            }
            
            jQuery('#'+input_field).val(received_val);
        }
		//-->
		</script>	
<?php
        echo tep_draw_form('menu_form', FILENAME_AFT_RULES, 'action=add', 'post', ' onSubmit="return check_form();" id="menu_form"');
        if ($id) {
            echo tep_draw_hidden_field('id', $id);
        }
?>
		<table cellspacing="2" cellpadding="2" border="0" width="100%">
			<tr>
				<td>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td class="pageHeading" valign="top"><?php if ($id != "") { echo HEADING_EDIT_MENU; } else { echo HEADING_ADD_MENU; } ?></td>
							<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table border="0" cellpadding="2" cellspacing="2">
						<tr id="config-title-row">
							<td class="main" width="25%" valign=top><?php echo ENTRY_DESCRIPTION; ?></td>
							<td class="main">
								<?=tep_draw_input_field('description', $input_description, ' size="50" maxlength="255"')?>
                                <span class="fieldRequired">* Required</span>
							<td>
						</tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_PHONE_COUNTRY; ?></td>
                            <td class="main">
                                <?php echo tep_get_country_list('country_dialing_code_id', $input_country_dialing_code_id); ?>
                                <span class="fieldRequired">* Required</span>
                            </td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" width="12%"><?=ENTRY_CATEGORY?></td>
                            <td class="main">
<?php
    echo tep_draw_input_field('cat_id', $input_cat_id, ' id="cat_id" size="50" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
    echo ' <span class="fieldRequired">* Required</span>';
    echo '&nbsp;<a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=category_cache&fieldname=cat_id') . '\', 600, 250, \'\');">(Category List)</a>';
?>
                            </td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_BROWSER_LANGUAGE?></td>
                            <td class="main">
                                <textarea wrap="soft" rows="3" cols="70" name="browser_language"><?=$input_browser_language?></textarea>
                                <?=tep_draw_checkbox_field('browser_language_empty', 1, $input_browser_language_empty, ' onClick="javascript:void(0);"')?>Including (empty)
                                <br>Separate each keyword with '||'
                            </td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_SCREEN_RESOLUTION?></td>
                            <td class="main">
                                <textarea wrap="soft" rows="3" cols="70" name="screen_resolution"><?=$input_screen_resolution?></textarea>
                                <?=tep_draw_checkbox_field('screen_resolution_empty', 1, $input_screen_resolution_empty, ' onClick="javascript:void(0);"')?>Including (empty)
                                <br>Separate each keyword with '||'
                            </td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_OPERATING_SYSTEM?></td>
                            <td class="main">
                                <textarea wrap="soft" rows="3" cols="70" name="operating_system"><?=$input_operating_system?></textarea>
                                <?=tep_draw_checkbox_field('operating_system_empty', 1, $input_operating_system_empty, ' onClick="javascript:void(0);"')?>Including (empty)
                                <br>Separate each keyword with '||'
                            </td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_JAVASCRIPT_ENABLED?></td>
                            <td class="main"><?=tep_draw_checkbox_field('javascript_enabled', 1, $input_javascript_enabled, ' onClick="javascript:void(0);"')?></td>
                        </tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_FLASH_ENABLED?></td>
                            <td class="main"><?=tep_draw_checkbox_field('flash_enabled', 1, $input_flash_enabled, ' onClick="javascript:void(0);"')?></td>
                        </tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_COOKIES_ENABLED?></td>
                            <td class="main"><?=tep_draw_checkbox_field('cookies_enabled', 1, $input_cookies_enabled, ' onClick="javascript:void(0);"')?></td>
                        </tr>
                        <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td></tr>
                        <tr>
                            <td class="main" valign="top"><?=ENTRY_HEADING_PAYMENT_METHOD?></td>
                            <td class="main">
                                <ul class="myTree">
<!--                                    <li style="-moz-user-select: none;" class="treeItem" id="">
                                        <span class="textHolder payment_gateways">
                                        &nbsp;&nbsp;&nbsp;
                                        <input type="checkbox" class="pg_any" name="payment_gateways_id[]" value="any" <?=((!in_array('any', $input_payment_methods_id) && count($input_payment_methods_id)) || count($input_payment_methods_id)) ? '' : ' checked '?>>
                                        <i>Any</i>
                                        </span>
                                    </li>-->
<?php 
        foreach ($payment_gateways_array['method'] as $payment_gateways_id => $payment_gateways_title) {
?>
                                    <li style="-moz-user-select: none;" class="treeItem" id="<?=$payment_gateways_id?>">
<?php

            if (isset($payment_methods_array[$payment_gateways_id]) && count($payment_methods_array[$payment_gateways_id])) {
                $pm_checked_flag = false;

                if (count($input_payment_methods_id)) {
                    foreach ($payment_methods_array[$payment_gateways_id] as $check_pm_id=>$check_pm_text) {
                        if (in_array($check_pm_id, $input_payment_methods_id)) {
                            $pm_checked_flag = true;
                            break;
                        }
                    }
                }

                if ($pm_checked_flag) {
?>
                                        <img src="images/icon-collapse-small.gif" class="expandImage" width="9" height="7">
                                        <span class="textHolder payment_gateways">
<!--                                            <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>">-->
                                            <?=$payment_gateways_title?> [<a class="checkall" href="javascript:void(0)">all</a>]
                                        </span>
                                        <ul style="display: display;" class="open">
<?php
                } else {
?>
                                        <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                        <span class="textHolder payment_gateways">
<!--                                            <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>">-->
                                            <?=$payment_gateways_title?> [<a class="checkall" href="javascript:void(0)">all</a>]
                                        </span>
                                        <ul style="display: none;">
<?php
                }
                
                foreach ($payment_methods_array[$payment_gateways_id] as $payment_methods_id => $payment_methods_title) {
?>
                                            <li style="-moz-user-select: none;" class="treeItem" id="<?=$payment_methods_id?>">&nbsp;&nbsp;
                                            <span class="textHolder payment_methods">
                                                <input type="checkbox" name="payment_methods_id[]" value="<?=$payment_methods_id?>"<?=(in_array($payment_methods_id, $input_payment_methods_id)?' checked ':'')?>>
                                                <?=$payment_methods_title?>
                                            </span>
                                            </li>
<?php
                }
?>
                                        </ul>
<?php
            } else {
?>
                                        <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                        <span class="textHolder payment_gateways">
<!--                                            <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>">-->
                                            <?=$payment_gateways_title?> [<a class="checkall" href="javascript:void(0)">all</a>]
                                        </span>
<?php
            }
?>
                                    </li>
                                    <script>
                                    jQuery("li#<?=$payment_gateways_id?> span.payment_gateways a[class='checkall']").click(function() {
                                        if (!jQuery("li#<?=$payment_gateways_id?> > ul:visible").length)
                                            jQuery("li#<?=$payment_gateways_id?> > img.expandImage").click();
                                        jQuery("li#<?=$payment_gateways_id?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                    });

                                    jQuery(document).ready(function() {
                                           if (jQuery("li#<?=$payment_gateways_id?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                               jQuery("li#<?=$payment_gateways_id?> span.payment_methods input[type='checkbox']").attr("checked",'');
                                           }
                                    });
                                    </script>
<?php
        }		
?>
                                </ul>

                                <script>
                                jQuery(document).ready(function() {
                                    tree = jQuery('.myTree');
                                    jQuery('img.expandImage', tree.get(0)).click(
                                        function() {
                                            if (this.src.indexOf('spacer') == -1) {
                                                subbranch = jQuery('ul', this.parentNode).eq(0);

                                                if (subbranch.css('display') == 'none') {
                                                    subbranch.show();
                                                    this.src = 'images/icon-collapse-small.gif';
                                                } else {
                                                    subbranch.hide();
                                                    this.src = 'images/icon-expand-small.gif';
                                                }
                                            }
                                        }
                                    );
                                });

                                jQuery('.myTree li input').click(function(){
                                    if (jQuery(this).hasClass('pg_any')) {
                                        jQuery('.myTree li input:not(.pg_any)').attr('checked',false);
                                        jQuery('.myTree li .pg_any').attr('checked',true);
                                    } else {
                                        if (jQuery('.myTree li input:not(.pg_any):checked').length > 0 ) {
                                            jQuery('.myTree li .pg_any').attr('checked',false);
                                        } else {
                                            jQuery('.myTree li .pg_any').attr('checked',true);
                                        }
                                    }
                                });
                                </script>
                            </td>
                        </tr>
						<tr>
							<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_SORT_ORDER; ?></td>
							<td class="main">
								<?=tep_draw_input_field('sort_order', $input_sort_order, ' id="sort_order" size="5" maxlength="5"')?>
							</td>
						</tr>
						<tr>
							<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr><td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td></tr>
			<tr>
				<td class="main" align="right">
<?php 
    if ($id) {
        echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton');
    } else {
        echo tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', 'inputButton');
    } 
?>
					<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, '?selected_box=infolinks', '', 'inputButton')?>
				</td>
			</tr>
		</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
	
	
	function addEntry($id = "") {
		global $messageStack;
		
		$error = 0;
		$error_msg = '';
        
		$id = (int) $id;
        $input_description = isset($_POST['description']) ? tep_db_prepare_input($_POST['description']) : '';
        $input_country_dialing_code_id = isset($_POST['country_dialing_code_id']) ? (int) $_POST['country_dialing_code_id'] : 0;
        $input_cat_id = isset($_POST['cat_id']) ? tep_db_prepare_input($_POST['cat_id']) : '';
        $input_payment_methods_id = isset($_POST['payment_methods_id']) ? tep_db_prepare_input($_POST['payment_methods_id']) : array();
        $input_sort_order = isset($_POST['sort_order']) ? (int) $_POST['sort_order'] : 0;
        $input_javascript_enabled = isset($_POST['javascript_enabled']) ? 1 : 0;
        $input_flash_enabled = isset($_POST['flash_enabled']) ? 1 : 0;
        $input_cookies_enabled = isset($_POST['cookies_enabled']) ? 1 : 0;
        $input_browser_language_empty = isset($_POST['browser_language_empty']) ? 1 : 0;
        $input_browser_language = isset($_POST['browser_language']) ? tep_db_prepare_input($_POST['browser_language']) : '';
        $input_screen_resolution_empty = isset($_POST['screen_resolution_empty']) ? 1 : 0;
        $input_screen_resolution = isset($_POST['screen_resolution']) ? tep_db_prepare_input($_POST['screen_resolution']) : '';
        $input_operating_system_empty = isset($_POST['operating_system_empty']) ? 1 : 0;
        $input_operating_system = isset($_POST['operating_system']) ? tep_db_prepare_input($_POST['operating_system']) : '';
        
        if ($input_description == '' || $input_country_dialing_code_id == '' || $input_cat_id == '') {
            $error = 1;
            $error_msg = 'Required entry field is empty.';
        }
        
		if ($error == 0) {
            $country_id_select_sql = "  SELECT countries_international_dialing_code
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_id ='" . tep_db_input($input_country_dialing_code_id) . "'";
            $country_id_result_sql = tep_db_query($country_id_select_sql);
            if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
                $input_country_dialing_code = $country_id_row['countries_international_dialing_code'];
            }

			$data = array(
                'aft_rule_name' => $input_description,
                'aft_rule_value' => json_encode(array(
                    'country_dialing_code_id' => $input_country_dialing_code_id,
                    'country_dialing_code' => $input_country_dialing_code,
                    'cat_id' => $input_cat_id,
                    'payment_methods_id' => $input_payment_methods_id,
                    'javascript_enabled' => $input_javascript_enabled,
                    'flash_enabled' => $input_flash_enabled,
                    'cookies_enabled' => $input_cookies_enabled,
                    'browser_language_empty' => $input_browser_language_empty,
                    'browser_language' => $input_browser_language,
                    'screen_resolution_empty' => $input_screen_resolution_empty,
                    'screen_resolution' => $input_screen_resolution,
                    'operating_system_empty' => $input_operating_system_empty,
                    'operating_system' => $input_operating_system,
                )),
                'aft_rule_sort_order' => $input_sort_order,
                'last_modified_date' => 'now()',
            );
			
			if ($id) {
				if (tep_db_perform(TABLE_AFT_RULE, $data, 'update', "aft_rule_id = '" . $id . "'")) {
                    $messageStack->add_session('Success: "' . htmlentities($input_description) . '" rule updated successfully.', 'success');
                }
			} else {
                $data['created_date'] = 'now()';
                
				tep_db_perform(TABLE_AFT_RULE, $data, 'insert');
				
                if ($id = tep_db_insert_id()) {
                    $messageStack->add_session('Success: "' . htmlentities($input_description) . '" rule saved successfully.', 'success');
                }
			}
		} else {
			$messageStack->add_session(sprintf(ERROR_TITLE_EXIST, $error_msg), 'error');
		}
	}
	
	function deleteEntry($id = '') {
        $id_delete_sql = "DELETE FROM " . TABLE_AFT_RULE . " WHERE aft_rule_id = '" . $id . "'";
        $main_result_sql = tep_db_query($id_delete_sql);

        if ($main_result_sql == 1) {
            return 'success';
        }

        return '';
	}	
}
?>