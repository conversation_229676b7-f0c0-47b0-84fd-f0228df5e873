<?php

include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'user_agent_detector.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
include_once(DIR_WS_CLASSES . 'c2c_order.php');
include_once(DIR_FS_CATALOG . 'includes/classes/auto_restock.php');
require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/classes/ogm_maxmind.php');
include_once(DIR_WS_CLASSES . 'process_locking.php');
include_once(DIR_WS_CLASSES . 'aft_rule.php');
include_once(DIR_WS_LANGUAGES . 'email_customers.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');
require_once(DIR_WS_CLASSES . 'g2g_serverless.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');
require_once(DIR_WS_CLASSES . 'ms_hub_email.php');

class anti_fraud {

    private $aft_module, $pw_obj;
    var $orders_id, $customers_id, $aft_name, $aft_mode, $aft_version, $execute_log, $cdkey_delivered, $orders_status_array, $products_not_to_deliver_array,
            $delivery_remark, $order_status_update, $order_date_purchased, $delivery_details,
            $lock_obj, $status_update, $changed_by;

    function anti_fraud($orders_id = '') {
        $this->orders_id = $orders_id;
        $this->aft_mode = 0;
        $this->execute_log = '';
        $this->delivery_remark = '';
        $this->cdkey_delivered = false;
        $this->order_status_update = false;
        $this->aff_mature_period = 21600; // Affiliate commission mature period
        $this->changed_by = 'system';
        $this->delivery_details = array();

        $customers_id_select_sql = "	SELECT customers_id, payment_methods_parent_id, payment_methods_id, date_purchased
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . $this->orders_id . "'";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        $customers_id_row = tep_db_fetch_array($customers_id_result_sql);
        
        // Prep SC data query activity = Purchase
        $scDataRequest = array(
            'order_id' => (string)$this->orders_id,
            'activity' => 'P',
        );
        // get all transaction related to orders_id and activity
        if ((c2c_order::orderSiteID($this->orders_id) == 5)) {
            $scArrayList = g2g_serverless::getScTransactions($scDataRequest);
        } else {
            $scArrayList = ms_store_credit::getScTransactions($scDataRequest);
        }
        $this->order_with_sc = (count($scArrayList) ? true : false );

        $check_gc_select_sql = "SELECT gift_card_redemption_id
								FROM " . TABLE_GIFT_CARD_REDEMPTION . "
								WHERE transaction_type='CO'
									AND transaction_id = '" . (int) $this->orders_id . "'";
        $check_gc_result_sql = tep_db_query($check_gc_select_sql);
        $this->order_with_gc = (tep_db_num_rows($check_gc_result_sql) ? true : false );

        $this->customers_id = $customers_id_row['customers_id'];
        $this->payment_gateway_id = $customers_id_row['payment_methods_parent_id'];
        $this->payment_methods_id = $customers_id_row['payment_methods_id'];
        $this->order_date_purchased = $customers_id_row['date_purchased'];

        $this->products_not_to_deliver_array = array(
            # Physical Games added 2016-09-06
            173257, 173258, 173259, 173260, 173261, 173262, 173263, 173264, 173265, 173266, 173267, 173268, 173269, 173270, 173271, 173272, 173273, 173274, 173275, 173276, 173277, 173278
        );
        $this->products_not_to_deliver_array[] = 153763;
        $this->products_not_to_deliver_array[] = 154154;
        $this->products_not_to_deliver_array[] = 156901;
        $this->products_not_to_deliver_array[] = 156866;
        $this->products_not_to_deliver_array[] = 158757;
        $this->products_not_to_deliver_array[] = 158755;
        $this->products_not_to_deliver_array[] = 158758;
        $this->products_not_to_deliver_array[] = 158756;
        $this->products_not_to_deliver_array[] = 158761;
        $this->products_not_to_deliver_array[] = 158759;
        $this->products_not_to_deliver_array[] = 158760;
        $this->products_not_to_deliver_array[] = 158771;
        $this->products_not_to_deliver_array[] = 158772;
        $this->products_not_to_deliver_array[] = 158775;
        $this->products_not_to_deliver_array[] = 158773;
        $this->products_not_to_deliver_array[] = 158774;
        $this->products_not_to_deliver_array[] = 158768;
        $this->products_not_to_deliver_array[] = 158767;
        $this->products_not_to_deliver_array[] = 158766;
        $this->products_not_to_deliver_array[] = 158762;
        $this->products_not_to_deliver_array[] = 158763;
        $this->products_not_to_deliver_array[] = 158765;
        $this->products_not_to_deliver_array[] = 158764;
        $this->products_not_to_deliver_array[] = 158769;
        $this->products_not_to_deliver_array[] = 158770;
        $this->products_not_to_deliver_array[] = 160062;
        $this->products_not_to_deliver_array[] = 160063;
        $this->products_not_to_deliver_array[] = 160302;
        $this->products_not_to_deliver_array[] = 166344;
        $this->products_not_to_deliver_array[] = 166380;
        $this->products_not_to_deliver_array[] = 166381;
        $this->products_not_to_deliver_array[] = 166382;
        $this->products_not_to_deliver_array[] = 166383;
        $this->products_not_to_deliver_array[] = 168842;

        $orders_status_select_sql = "	SELECT orders_status_id, orders_status_name
										FROM " . TABLE_ORDERS_STATUS . "
										WHERE language_id = 1
										ORDER BY orders_status_sort_order";
        $orders_status_result_sql = tep_db_query($orders_status_select_sql);
        while ($orders_status_row = tep_db_fetch_array($orders_status_result_sql)) {
            $this->orders_status_array[$orders_status_row['orders_status_id']] = $orders_status_row['orders_status_name'];
        }

        $this->lock_obj = new process_locking();

        if (KOUNT_ENABLED == 'true') {
            include_once(DIR_WS_CLASSES . 'ogm_kount.php');
            $this->aft_module = new ogm_kount($this->orders_id);
        } else if (TM_SERVICE_ENABLED == 'true') {
            include_once(DIR_WS_CLASSES . 'ogm_threat_metrix.php');
            $this->aft_module = new ogm_threat_metrix($this->orders_id);
        } else {
            include_once(DIR_WS_CLASSES . 'ogm_kount.php');
            //If both are disabled, ogm_kount can still be used to serve AFT queries which
            //  is already cached previously.
            $this->aft_module = new ogm_kount($this->orders_id);
        }
    }

    public function getAftModule() {
        return $this->aft_module;
    }

    function execute_aft_script($reexecute = 0) {
        global $login_email_address;

        $this->order_status_update = false;

        if ($this->lock_obj->isLocked($this->orders_id, process_locking::MOVE_ORDER, false) !== true) {
            $aft_automation_code_select_sql = "	SELECT aa.aft_automation_code, aa.aft_automation_mode, aa.aft_automation_name, o.orders_aft_executed, aa.aft_automation_version, o.orders_status, o.date_purchased
                                                FROM " . TABLE_AFT_AUTOMATION . " AS aa
                                                INNER JOIN " . TABLE_AFT_AUTOMATION_CATEGORY . " AS aac
                                                    ON (aac.aft_automation_category_id = aa.aft_automation_category_id)
                                                INNER JOIN " . TABLE_ORDERS . " AS o
                                                    ON (o.orders_status = aac.aft_automation_category_key AND aac.aft_automation_category_type = 'C')
                                                WHERE o.orders_id = '" . (int) $this->orders_id . "'";
            $aft_automation_code_result_sql = tep_db_query($aft_automation_code_select_sql);
            if ($aft_automation_code_row = tep_db_fetch_array($aft_automation_code_result_sql)) {
                $start_orders_aft_executed = $aft_automation_code_row['orders_aft_executed'];

                $this->aft_name = $aft_automation_code_row['aft_automation_name'];
                $this->aft_mode = $aft_automation_code_row['aft_automation_mode'];
                $this->aft_version = $aft_automation_code_row['aft_automation_version'];

                if ($start_orders_aft_executed == 0 || $reexecute == 1) {
                    if ($start_orders_aft_executed != '-1') {
                        if ($aft_automation_code_row['orders_status'] == 7 && $this->is_suspicious_order()) {
                            @tep_mail('OffGamers', '<EMAIL>', $this->orders_id . ' - Paid amount less than total need to pay', 'validate_subtotal_against_product_amount(): false', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            $this->set_order_remark(0, 0, "<span class='redIndicator'>Paid amount less than total need to pay</span>", 0, 0);
                        } else {
                            if ($this->aft_mode != 0) {
                                if ($reexecute == 1 && $login_email_address) {
                                    $this->changed_by = $login_email_address;
                                }

                                $sql_data_array = array('orders_aft_executed' => 1);
                                tep_db_perform(TABLE_ORDERS, $sql_data_array, 'update', " orders_id = '" . (int) $this->orders_id . "'");

                                // verify order_total to ensure payment amount correct
                                $ot_data = array(
                                    'ot_coupon' => 0,
                                    'ot_gst' => 0,
                                    'ot_gv' => 0,
                                    'ot_ogc' => 0,
                                    'ot_subtotal' => 0,
                                    'ot_surcharge' => 0,
                                    'ot_total' => 0
                                );
                                $ot_sel = "SELECT ot.value, ot.class, oei.orders_extra_info_value
                                            FROM " . TABLE_ORDERS_TOTAL . " AS ot
                                            LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei
                                                ON ot.orders_id = oei.orders_id
                                                AND oei.orders_extra_info_key = 'site_id'
                                            WHERE ot.orders_id = '" . $this->orders_id . "'";
                                $ot_res = tep_db_query($ot_sel);
                                while ($ot_row = tep_db_fetch_array($ot_res)) {
                                    // get all transaction related to orders_id and activity
                                    if ($ot_row['orders_extra_info_value'] == 5) {
                                        if ($ot_row['class'] == 'ot_gv') {
                                            // Prep SC data query Activity = Purchase
                                            $scDataRequest = array(
                                                'order_id' => $this->orders_id,
                                                'activity' => 'P',
                                            );
                                            
                                            $scArrayList = g2g_serverless::getScAllTransactions($scDataRequest);
                                            
                                            // compile debit total
                                            $totalScDebit = 0;
                                            foreach ($scArrayList as $scList) {
                                                if ($scList['transaction_type'] == 'SUBTRACT_CREDIT') {
                                                    $totalScDebit += $scList['transaction_amount'];
                                                }
                                            }

                                            $co_sel = "SELECT currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . $this->orders_id . "'";
                                            $co_res = tep_db_query($co_sel);
                                            $co_row = tep_db_fetch_array($co_res);

                                            $sc_debit_usd = ($totalScDebit / $co_row['currency_value']);
                                            $ot_data[$ot_row['class']] = ($sc_debit_usd > $ot_row['value'] ? $ot_row['value'] : $sc_debit_usd);
                                        } else {
                                            $ot_data[$ot_row['class']] = $ot_row['value'];
                                        }
                                    } else {
                                        $ot_data[$ot_row['class']] = $ot_row['value'];
                                    }
                                }

                                $ot_total = ($ot_data['ot_subtotal'] - ($ot_data['ot_coupon'] + $ot_data['ot_gv'] + $ot_data['ot_ogc']) + ($ot_data['ot_gst'] + $ot_data['ot_surcharge']));
                                if (($ot_total == $ot_data['ot_total']) || (abs($ot_data['ot_total'] - $ot_total) <= 0.5)) {
                                    if (tep_not_null($aft_automation_code_row['aft_automation_code'])) {
                                        $locked_process = true;

                                        $temp_id_select_sql = "	SELECT temp_id, created_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS dead_process
                                                            FROM " . TABLE_TEMP_PROCESS . "
                                                            WHERE page_name = 'anti_fraud.php'
                                                                AND match_case = '" . (int) $this->orders_id . "'";
                                        $temp_id_result_sql = tep_db_query($temp_id_select_sql);

                                        if ($temp_id_row = tep_db_fetch_array($temp_id_result_sql)) {
                                            if ($temp_id_row['dead_process'] == 1) {
                                                $locked_process = false;    // Allow rerun if previous execution is at least 5 mins ago
                                            }
                                        } else {
                                            $locked_process = false;
                                        }

                                        if (!$locked_process) {
                                            $temp_process_sql_array = array('page_name' => 'anti_fraud.php',
                                                'match_case' => $this->orders_id,
                                                'created_date' => 'now()'
                                            );

                                            tep_db_perform(TABLE_TEMP_PROCESS, $temp_process_sql_array);

                                            $this->execute_log = "Start Execute\n";

                                            eval($aft_automation_code_row['aft_automation_code']);

                                            $this->execute_log .= 'End Execute ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";

                                            if ($this->order_status_update == true) {
                                                $this->set_order_remark($this->get_order_status(false), 0, $this->execute_log, 0, 0);
                                            } else {
                                                $this->set_order_remark(0, 0, $this->execute_log, 0, 0);
                                            }

                                            $delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . "
                                                                    WHERE page_name = 'anti_fraud.php'
                                                                        AND match_case = '" . (int) $this->orders_id . "'";
                                            tep_db_query($delete_temp_process_sql);
                                        }
                                    }
                                } else {
                                    $this->execute_log .= "<font color=\"red\"><b>Paid amount less than total need to pay</b></font>\n";
                                    $this->set_order_remark(0, 0, $this->execute_log, 0, 0);
                                }
                            }
                        }
                    } else {
                        $this->set_order_remark(0, 0, "Genesis Project turned off - rerun stopped.", 0, 0);
                    }
                }
            }

            if ($this->order_status_update == true) {
                $this->execute_aft_script();
            }
        } else {
            //
        }
    }

    function is_aft_rules_matched($logged = true) {
        $aft_rule_obj = new aft_rule();
        list($return_bool, $rule) = $aft_rule_obj->rules_matched($this->orders_id);
        $status = $return_bool ? 'true' . ' (<font color="red"><b>' . $rule . '</b></font>)' : 'false' . ' (' . $rule . ')';

        if ($logged === true) {
            $this->execute_log .= 'is_aft_rules_matched(): ' . $status . "\n";
        } else if ($logged === 1) {
            $temp_log = $aft_rule_obj->rule_log;
            $temp = '';

            array_pop($temp_log);

            foreach ($temp_log as $desc => $log_arr) {
                $temp .= '  - rule (' . $desc . '): false' . "\n";
            }

            $this->execute_log .= 'is_aft_rules_matched(): ' . $status . "\n" . $temp;
        } else if ($logged === 2) {
            $temp = '';

            foreach ($aft_rule_obj->rule_log as $desc => $log_arr) {
                $temp .= 'Start rule: ' . $desc . "\n";
                foreach ($log_arr as $log) {
                    $temp .= '  - ' . $log['key'] . ' ' . $log['msg'] . "\n";
                }
                $temp .= 'End rule' . "\n";
            }

            $this->execute_log .= 'is_aft_rules_matched(): ' . $status . "\n" . $temp;
        }

        unset($aft_rule_obj);

        return $return_bool;
    }

    function is_suspicious_order() {
        if (!$this->validate_subtotal_against_product_amount(10)) {
            $return_bool = TRUE;
        } else if ($this->payment_gateway_id == 0 && $this->get_order_total(false) > 0.1) {
            $return_bool = TRUE;
        } else {
            $return_bool = FALSE;
        }

        return $return_bool;
    }

    function get_order_status($logged = true) {
        $orders_status_select_sql = "	SELECT orders_status
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
        $orders_status_result_sql = tep_db_query($orders_status_select_sql);
        $orders_status_row = tep_db_fetch_array($orders_status_result_sql);

        if ($logged) {
            $this->execute_log .= 'get_order_status(): ' . $orders_status_row['orders_status'] . "\n";
        }

        return $orders_status_row['orders_status'];
    }

    function get_order_payment_method($logged = true) {
        global $order;

        if (is_object($order) && isset($order->info['payment_methods_id'])) {
            $payment_methods_id = $order->info['payment_methods_id'];
        } else {
            $payment_methods_id = $this->payment_methods_id;
        }

        if ($logged) {
            $this->execute_log .= 'get_order_payment_method(): ' . $payment_methods_id . "\n";
        }

        return $payment_methods_id;
    }

    function get_order_payment_method_parent($logged = true) {
        global $order;

        if (is_object($order) && isset($order->info['payment_methods_parent_id'])) {
            $payment_methods_parent_id = $order->info['payment_methods_parent_id'];
        } else {
            $pm_id_parent_select_sql = "	SELECT payment_methods_parent_id
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . (int) $this->orders_id . "'";
            $pm_id_parent_result_sql = tep_db_query($pm_id_parent_select_sql);
            $pm_id_parent_row = tep_db_fetch_array($pm_id_parent_result_sql);

            $payment_methods_parent_id = $pm_id_parent_row['payment_methods_parent_id'];
        }

        if ($logged) {
            $this->execute_log .= 'get_order_payment_method_parent(): ' . $payment_methods_parent_id . "\n";
        }

        return $payment_methods_parent_id;
    }

    function get_total_rp_amount($completed_period_sql, $logged = true) {
        $payment_info_array = array();
        $stat_RP_method_array = array();

        $periodical_sales_total_amount = 0;
        $total_RP_refund_value = 0;

        $payment_methods_obj = new payment_methods('payment_methods');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        if (isset($payment_info_array) && is_array($payment_info_array)) {
            foreach ($payment_info_array as $pm_id => $pm_info) {
                if ((int) $pm_info->confirm_complete_days > 0) {
                    $stat_RP_method_array[] = $pm_id;
                }
            }
        }

        $customers_id_select_sql = "	SELECT customers_id
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        $customers_id_row = tep_db_fetch_array($customers_id_result_sql);

        $order_completed_within_period_select_sql = "	SELECT o.orders_id, o.orders_cb_status, o.date_purchased, ot.value AS order_amount
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
															ON (o.orders_id = ot.orders_id AND ot.class='ot_total')
														WHERE o.customers_id = '" . (int) $customers_id_row['customers_id'] . "'
															AND o.orders_status IN (2, 3)
															AND ot.value > 0
															AND o.date_purchased >= DATE_SUB(NOW(), INTERVAL " . $completed_period_sql . ")
															AND o.payment_methods_id IN ('" . implode("', '", $stat_RP_method_array) . "')";
        $order_completed_within_period_result_sql = tep_db_query($order_completed_within_period_select_sql);
        while ($order_completed_within_period_row = tep_db_fetch_array($order_completed_within_period_result_sql)) {
            $payment_gateway_canceled_value_select_sql = "	SELECT SUM(products_canceled_price) AS total_refund, SUM(products_reversed_price) AS total_reversed
															FROM " . TABLE_ORDERS_PRODUCTS . "
															WHERE orders_id = '" . tep_db_input($order_completed_within_period_row['orders_id']) . "'";
            $payment_gateway_canceled_value_result_sql = tep_db_query($payment_gateway_canceled_value_select_sql);
            $payment_gateway_canceled_value_row = tep_db_fetch_array($payment_gateway_canceled_value_result_sql);
            $total_RP_refund_value = $payment_gateway_canceled_value_row['total_refund'];

            // Prep SC data query Activity = Refund
            $scDataRequest = array(
                'order_id' => $order_completed_within_period_row['orders_id'],
                'activity' => 'R',
            );
            // get all sc transaction related to orders_id and activity
            if ((c2c_order::orderSiteID($order_completed_within_period_row['orders_id']) == 5)) {
                $scArrayList = g2g_serverless::getScAllTransactions($scDataRequest);
            } else {
                $scArrayList = ms_store_credit::getScAllTransactions($scDataRequest);
            }
            foreach ($scArrayList as $scList) {
                $total_RP_refund_value -= $scList['transaction_amount'];
            }

            if ($order_completed_within_period_row['orders_cb_status'] == '1' || $order_completed_within_period_row['orders_cb_status'] == '2') {
                $total_RP_refund_value += $payment_gateway_canceled_value_row['total_reversed'];
            }

            if ($order_completed_within_period_row['order_amount'] > $total_RP_refund_value) {
                $periodical_sales_total_amount += ($order_completed_within_period_row['order_amount'] - $total_RP_refund_value);
            }
        }

        if ($this->get_order_status(false) == 7) { // Including this order
            $periodical_sales_total_amount += $this->get_order_total(false);
        }

        if ($logged == true) {
            $this->execute_log .= 'get_total_rp_amount(): ' . $periodical_sales_total_amount . "\n";
        }

        return $periodical_sales_total_amount;
    }

    function get_total_nrp_amount($completed_period_sql, $logged = true) {
        $payment_info_array = array();
        $stat_NRP_method_array = array();

        $periodical_sales_total_amount = 0;
        $total_NRP_refund_value = 0;

        $payment_methods_obj = new payment_methods('payment_methods');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        if (isset($payment_info_array) && is_array($payment_info_array)) {
            foreach ($payment_info_array as $pm_id => $pm_info) {
                if ((int) $pm_info->confirm_complete_days == 0) {
                    $stat_NRP_method_array[] = $pm_id;
                }
            }
        }

        $customers_id_select_sql = "	SELECT customers_id
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        $customers_id_row = tep_db_fetch_array($customers_id_result_sql);

        $order_completed_within_period_select_sql = "	SELECT o.orders_id, o.orders_cb_status, o.date_purchased,  ot.value AS order_amount
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
															ON (o.orders_id = ot.orders_id AND ot.class='ot_total')
														WHERE o.customers_id = '" . (int) $customers_id_row['customers_id'] . "'
															AND o.orders_status IN (2, 3)
															AND ot.value > 0
															AND o.date_purchased >= DATE_SUB(NOW(), INTERVAL " . $completed_period_sql . ")
															AND o.payment_methods_id IN ('" . implode("', '", $stat_NRP_method_array) . "')";
        $order_completed_within_period_result_sql = tep_db_query($order_completed_within_period_select_sql);
        while ($order_completed_within_period_row = tep_db_fetch_array($order_completed_within_period_result_sql)) {
            $payment_gateway_canceled_value_select_sql = "	SELECT SUM(products_canceled_price) AS total_refund, SUM(products_reversed_price) AS total_reversed
															FROM " . TABLE_ORDERS_PRODUCTS . "
															WHERE orders_id = '" . tep_db_input($order_completed_within_period_row['orders_id']) . "'";
            $payment_gateway_canceled_value_result_sql = tep_db_query($payment_gateway_canceled_value_select_sql);
            $payment_gateway_canceled_value_row = tep_db_fetch_array($payment_gateway_canceled_value_result_sql);
            $total_NRP_refund_value = $payment_gateway_canceled_value_row['total_refund'];
            
            // Prep SC data query Activity = Refund
            $scDataRequest = array(
                'order_id' => $order_completed_within_period_row['orders_id'],
                'activity' => 'R',
            );
            // get all transaction related to orders_id and activity
            if ((c2c_order::orderSiteID($order_completed_within_period_row['orders_id']) == 5)) {
                $scArrayList = g2g_serverless::getScAllTransactions($scDataRequest);
            } else {
                $scArrayList = ms_store_credit::getScAllTransactions($scDataRequest);
            }
            foreach ($scArrayList as $scList) {
                $total_NRP_refund_value -= $scList['transaction_amount'];
            }

            if ($order_completed_within_period_row['orders_cb_status'] == '1' || $order_completed_within_period_row['orders_cb_status'] == '2') {
                $total_NRP_refund_value += $payment_gateway_canceled_value_row['total_reversed'];
            }

            if ($order_completed_within_period_row['order_amount'] > $total_NRP_refund_value) {
                $periodical_sales_total_amount += ($order_completed_within_period_row['order_amount'] - $total_NRP_refund_value);
            }
        }

        if ($this->get_order_status(false) == 7) { // Including this order
            $periodical_sales_total_amount += $this->get_order_total(false);
        }

        if ($logged == true) {
            $this->execute_log .= 'get_total_nrp_amount(): ' . $periodical_sales_total_amount . "\n";
        }

        return $periodical_sales_total_amount;
    }

    function get_total_pg_amount($date_interval, $logged = true) {

        $periodical_sales_total_amount = 0;

        $order_completed_within_period_select_sql = "	SELECT SUM(ot.value) AS daily_speding
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
															ON (o.orders_id = ot.orders_id AND ot.class='ot_total')
														WHERE o.customers_id = '" . (int) $this->customers_id . "'
															AND o.payment_methods_parent_id = '" . (int) $this->payment_gateway_id . "'
															AND o.orders_status IN (7, 2, 3)
															AND ot.value > 0
															AND o.date_purchased >= DATE_SUB(NOW(), INTERVAL " . $date_interval . ") ";
        $order_completed_within_period_result_sql = tep_db_query($order_completed_within_period_select_sql);
        $order_completed_within_period_row = tep_db_fetch_array($order_completed_within_period_result_sql);
        $periodical_sales_total_amount = $order_completed_within_period_row['daily_speding'];

        if ($logged == true) {
            $this->execute_log .= 'get_total_pg_amount(): ' . $periodical_sales_total_amount . "\n";
        }

        return $periodical_sales_total_amount;
    }

    function get_confirm_completed_amount() {
        $payment_info_array = array();

        $payment_methods_obj = new payment_methods('payment_methods');
        $payment_info_array = $payment_methods_obj->payment_methods_array;
        // foreach ($payment_methods_obj->payment_methods_array as $payment_methods_id => $payment_methods_data) {
        // 	$payment_info_array[$payment_methods_id] = new objectInfo($payment_methods_data);
        // 	//$payment_info_array[$payment_methods_id]->pm_object = $payment_methods_data;
        // }

        $total_confirmed_completed_sales = 0;

        $customers_id_select_sql = "	SELECT customers_id
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        $customers_id_row = tep_db_fetch_array($customers_id_result_sql);

        $customer_payment_methods_select_sql = "SELECT DISTINCT payment_methods_id
												FROM " . TABLE_ORDERS . "
												WHERE customers_id = '" . (int) $customers_id_row['customers_id'] . "'
													AND orders_status IN (2, 3) ";
        $customer_payment_methods_result_sql = tep_db_query($customer_payment_methods_select_sql);
        while ($customer_payment_methods_row = tep_db_fetch_array($customer_payment_methods_result_sql)) {
            $confirm_complete_day = (int) $payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days > 0 ? (int) $payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days : 0;
            $confirm_complete_order_select_sql = "	SELECT o.orders_id, o.orders_cb_status, o.date_purchased,  ot.value AS order_amount
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
														ON (o.orders_id = ot.orders_id AND ot.class='ot_total')
													WHERE o.customers_id = '" . (int) $customers_id_row['customers_id'] . "'
														AND o.date_purchased <= DATE_SUB(NOW(), INTERVAL " . $confirm_complete_day . " DAY)
														AND o.orders_status IN (2, 3)
														AND ot.value > 0
														AND o.payment_methods_id = '" . tep_db_input($customer_payment_methods_row["payment_methods_id"]) . "'";
            $confirm_complete_order_result_sql = tep_db_query($confirm_complete_order_select_sql);
            while ($confirm_complete_order_row = tep_db_fetch_array($confirm_complete_order_result_sql)) {
                $payment_gateway_canceled_value_select_sql = "	SELECT SUM(products_canceled_price) AS total_refund, SUM(products_reversed_price) AS total_reversed
																FROM " . TABLE_ORDERS_PRODUCTS . "
																WHERE orders_id = '" . tep_db_input($confirm_complete_order_row['orders_id']) . "'";
                $payment_gateway_canceled_value_result_sql = tep_db_query($payment_gateway_canceled_value_select_sql);
                $payment_gateway_canceled_value_row = tep_db_fetch_array($payment_gateway_canceled_value_result_sql);
                $total_refund_value = $payment_gateway_canceled_value_row['total_refund'];
                
                // Prep SC data query Activity = Refund
                $scDataRequest = array(
                    'order_id' => $confirm_complete_order_row['orders_id'],
                    'activity' => 'R',
                );
                // get all transaction related to orders_id and activity
                if ((c2c_order::orderSiteID($confirm_complete_order_row['orders_id']) == 5)) {
                    $scArrayList = g2g_serverless::getScAllTransactions($scDataRequest);
                } else {
                    $scArrayList = ms_store_credit::getScAllTransactions($scDataRequest);
                }
                foreach ($scArrayList as $scList) {
                    $total_refund_value -= $scList['transaction_amount'];
                }

                if ($confirm_complete_order_row['orders_cb_status'] == '1' || $confirm_complete_order_row['orders_cb_status'] == '2') {
                    $total_refund_value += $payment_gateway_canceled_value_row['total_reversed'];
                }

                if ($confirm_complete_order_row['order_amount'] > $total_refund_value) {
                    $total_confirmed_completed_sales += ($confirm_complete_order_row['order_amount'] - $total_refund_value);
                }
            }
        }

        $this->execute_log .= 'get_confirm_completed_amount(): ' . $total_confirmed_completed_sales . "\n";

        return $total_confirmed_completed_sales;
    }

    function get_maxmind_score($logged = true) {
        $maxmind_score = 0;

        $score_select_sql = "	SELECT score
								FROM " . TABLE_MAXMIND_HISTORY . "
								WHERE orders_id = '" . (int) $this->orders_id . "'
								ORDER BY maxmind_history_date DESC
								LIMIT 1";
        $score_result_sql = tep_db_query($score_select_sql);
        if ($score_row = tep_db_fetch_array($score_result_sql)) {
            $maxmind_score = $score_row['score'];
        } else {
            $maxmind_obj = new ogm_maxmind();
            $maxmind_score = $maxmind_obj->crew_score_request($this->orders_id);
            unset($maxmind_obj);
        }

        if ($logged) {
            $this->execute_log .= 'get_maxmind_score: ' . $maxmind_score . "\n";
        }

        return $maxmind_score;
    }

    function get_maxmind_proxy_score($logged = true) {
        $proxy_score = 0;

        $score_select_sql = "	SELECT proxy_score
								FROM " . TABLE_MAXMIND_HISTORY . "
								WHERE orders_id = '" . (int) $this->orders_id . "'
								ORDER BY maxmind_history_date DESC
								LIMIT 1";
        $score_result_sql = tep_db_query($score_select_sql);
        if ($score_row = tep_db_fetch_array($score_result_sql)) {
            $proxy_score = $score_row['proxy_score'];
        } else {
            $maxmind_obj = new ogm_maxmind();
            if ($maxmind_obj->crew_score_request($this->orders_id)) {
                $proxy_score = $this->get_maxmind_proxy_score(false);
            }

            unset($maxmind_obj);
        }

        if ($logged) {
            $this->execute_log .= 'get_maxmind_proxy_score: ' . $proxy_score . "\n";
        }

        return $proxy_score;
    }

    function get_maxmind_phone_type() {
        $phone_verification_obj = new phone_verification();
        $phone_info_select_sql = "	SELECT customers_id, customers_telephone_country, customers_country, customers_country_international_dialing_code, customers_telephone
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
        $phone_info_result_sql = tep_db_query($phone_info_select_sql);
        $phone_info_row = tep_db_fetch_array($phone_info_result_sql);

        if (tep_not_null($phone_info_row['customers_country_international_dialing_code'])) {
            $country_code = $phone_info_row['customers_country_international_dialing_code'];
        } else {
            $inter_dialing_code_select_sql = "	SELECT countries_international_dialing_code
												FROM " . TABLE_COUNTRIES . "
												WHERE countries_name = '" . tep_db_input($phone_info_row['customers_country']) . "'";
            $inter_dialing_code_result_sql = tep_db_query($inter_dialing_code_select_sql);
            $inter_dialing_code_row = tep_db_fetch_array($inter_dialing_code_result_sql);

            $country_code = $inter_dialing_code_row['countries_international_dialing_code'];
        }

        $phonetype = $phone_verification_obj->get_phone_type($phone_info_row['customers_id'], $phone_info_row['customers_telephone_country'], $country_code, $phone_info_row['customers_telephone']);
        $this->execute_log .= 'get_maxmind_phone_type(): ' . $phonetype . "\n";
        unset($phone_verification_obj);
        return $phonetype;
    }

    function get_customer_aft_group($logged = true) {
        $customers_groups_id_select_sql = "	SELECT customers_aft_groups_id
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id = '" . (int) $this->customers_id . "'";
        $customers_groups_id_result_sql = tep_db_query($customers_groups_id_select_sql);
        $customers_groups_id_row = tep_db_fetch_array($customers_groups_id_result_sql);

        if ($logged == true) {
            $this->execute_log .= 'get_customer_aft_group(): ' . $customers_groups_id_row['customers_aft_groups_id'] . "\n";
        }

        return $customers_groups_id_row['customers_aft_groups_id'];
    }

    function get_customer_group($logged = true) {
        $customers_groups_id_select_sql = "	SELECT customers_groups_id
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id = '" . (int) $this->customers_id . "'";
        $customers_groups_id_result_sql = tep_db_query($customers_groups_id_select_sql);
        $customers_groups_id_row = tep_db_fetch_array($customers_groups_id_result_sql);

        if ($logged == true) {
            $this->execute_log .= 'get_customer_group(): ' . $customers_groups_id_row['customers_groups_id'] . "\n";
        }

        return $customers_groups_id_row['customers_groups_id'];
    }

    function get_order_total($logged = true) {
        $value_select_sql = "	SELECT value
								FROM " . TABLE_ORDERS_TOTAL . "
								WHERE orders_id = '" . (int) $this->orders_id . "'
									AND class = 'ot_total'";
        $value_result_sql = tep_db_query($value_select_sql);
        $value_row = tep_db_fetch_array($value_result_sql);

        if ($logged == true) {
            $this->execute_log .= "get_order_total(): " . $value_row['value'] . "\n";
        }

        return $value_row['value'];
    }

    function get_order_subtotal() {
        $subtotal_select_sql = "	SELECT value
									FROM " . TABLE_ORDERS_TOTAL . "
									WHERE orders_id = '" . (int) $this->orders_id . "'
										AND class = 'ot_subtotal'";
        $subtotal_result_sql = tep_db_query($subtotal_select_sql);
        $subtotal_row = tep_db_fetch_array($subtotal_result_sql);

        $this->execute_log .= "get_order_subtotal(): " . $subtotal_row['value'] . "\n";

        return $subtotal_row['value'];
    }

    function set_customer_flag_type($flag_id, $function_name, $line = '') {
        $customers_flag_select_sql = "	SELECT customers_flag, customers_id
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . (int) $this->customers_id . "'";
        $customers_flag_result_sql = tep_db_query($customers_flag_select_sql);
        if ($customers_flag_row = tep_db_fetch_array($customers_flag_result_sql)) {
            $flag_array = explode(',', $customers_flag_row['customers_flag']);

            if (!in_array($flag_id, $flag_array)) {
                if (sizeof($flag_array) > 0) {
                    $customers_flag_str = $flag_id . ',' . $customers_flag_row['customers_flag'];
                } else {
                    $customers_flag_str = $flag_id;
                }

                $sql_data_array = array('customers_flag' => $customers_flag_str);
                tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', " customers_id = '" . (int) $customers_flag_row['customers_id'] . "'");

                if (tep_not_null($line)) {
                    $this->execute_log .= "set_customer_flag_type():\nLine #" . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
                }
            }
        }
    }

    function is_account_inactive_x_period_by_last_complete_order($day_period, $line = '') {
        $last_complete_order_sql = " SELECT customers_id 
                                     FROM " . TABLE_ORDERS . " 
                                     WHERE customers_id = '" . (int) $this->customers_id . "' 
                                        AND DATE_SUB(CURDATE(), INTERVAL $day_period DAY) <= date_purchased 
                                        AND orders_status = 3";
        $last_complete_order_result = tep_db_query($last_complete_order_sql);
        if (tep_db_num_rows($last_complete_order_result) == 0) {
            $this->execute_log .= "is_account_inactive_x_period_by_last_complete_order(): true\n";
            $this->set_customer_flag_type('1', '', $line); // set normal flag for this customer
            return true;
        }

        $this->execute_log .= "is_account_inactive_x_period_by_last_complete_order(): false\n";
        return false;
    }

    function is_within_order_quantity_limit($day_duration, $total_order_qty_limit, $line) {
        $exp_date = '';
        $skip_validation = false;

        $aft_whitelist_select_sql = "   SELECT customers_setting_value
                                        FROM " . TABLE_CUSTOMERS_SETTING . " 
                                        WHERE customers_id = '" . (int) $this->customers_id . "'
                                            AND customers_setting_key = 'aft_genesis_whitelisted'";
        $aft_whitelist_result_sql = tep_db_query($aft_whitelist_select_sql);
        if ($aft_whitelist_row = tep_db_fetch_array($aft_whitelist_result_sql)) {
            $exp_date = $aft_whitelist_row['customers_setting_value'];

            if (time() > strtotime($exp_date)) {
                # reset
                $exp_date = " (reset expiry date : $exp_date)";

                $delete_temp_process_sql = "DELETE FROM " . TABLE_CUSTOMERS_SETTING . "
                                            WHERE customers_id = '" . (int) $this->customers_id . "'
                                                AND customers_setting_key = 'aft_genesis_whitelisted'";
                tep_db_query($delete_temp_process_sql);
            } else {
                $skip_validation = true;
            }
        }

        if ($skip_validation) {
            $this->execute_log .= "is_within_order_quantity_limit(): true (expiry date : $exp_date)\n";
            return true;
        } else {
            $expired_order_select_sql = "   SELECT o.customers_id, count(o.orders_id) as total_order
                                            FROM " . TABLE_ORDERS . " AS o
                                            WHERE o.orders_status != '5' AND o.customers_id = '" . (int) $this->customers_id . "' 
                                                AND o.date_purchased BETWEEN DATE_SUB(NOW(), INTERVAL $day_duration DAY) AND NOW()";
            $expired_order_result_sql = tep_db_query($expired_order_select_sql);
            if ($expired_order_row = tep_db_fetch_array($expired_order_result_sql)) {
                if ($expired_order_row['total_order'] > $total_order_qty_limit) {
                    $this->execute_log .= "is_within_order_quantity_limit(): false$exp_date\n";
                    $this->set_customer_flag_type('1', '', $line);
                    return false;
                } else {
                    $this->execute_log .= "is_within_order_quantity_limit(): true$exp_date\n";
                    return true;
                }
            } else {
                $this->execute_log .= "is_within_order_quantity_limit(): true$exp_date\n";
                return true;
            }
        }
    }

    function is_flag_customer() {
        $result = $this->is_customer_flag(1);

        if ($result == true) {
            $this->execute_log .= "is_flag_customer(): true\n";

            $message_select_sql = "	SELECT message
									FROM " . TABLE_CUSTOMERS_FLAG_MESSAGE . "
									WHERE customers_id = '" . (int) $this->customers_id . "'
										AND flag_id = 1";
            $message_result_sql = tep_db_query($message_select_sql);
            if ($message_row = tep_db_fetch_array($message_result_sql)) {
                $this->execute_log .= "\n" . $message_row['message'] . "\n";
            }
        } else {
            $this->execute_log .= "is_flag_customer(): false\n";
        }

        return $result;
    }

    function is_nrp_customer() {
        $result = $this->is_customer_flag(2);

        if ($result == true) {
            $this->execute_log .= "is_nrp_customer(): true\n";
        } else {
            $this->execute_log .= "is_nrp_customer(): false\n";
        }

        return $result;
    }

    function is_cb_customer() {
        $result = $this->is_customer_flag(4);

        if ($result == true) {
            $this->execute_log .= "is_cb_customer(): true\n";
        } else {
            $this->execute_log .= "is_cb_customer(): false\n";
        }

        return $result;
    }

    function is_customer_flag($flag_id) {
        $customers_id_select_sql = "	SELECT customers_id
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . (int) $this->customers_id . "'
											AND FIND_IN_SET('" . $flag_id . "', customers_flag)";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        if (tep_db_num_rows($customers_id_result_sql) > 0) {
            return true;
        } else {
            return false;
        }
    }

    function is_email_verified() {
        $info_verified_select_sql = "	SELECT civ.info_verified
										FROM " . TABLE_CUSTOMERS . " AS c
										INNER JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
											ON (c.customers_id = civ.customers_id AND civ.customers_info_value = c.customers_email_address AND civ.info_verification_type = 'email')
										WHERE c.customers_id = '" . (int) $this->customers_id . "'";
        $info_verified_result_sql = tep_db_query($info_verified_select_sql);
        $info_verified_row = tep_db_fetch_array($info_verified_result_sql);

        if ($info_verified_row['info_verified'] == 1) {
            $this->execute_log .= "is_email_verified(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_email_verified(): false\n";

            return false;
        }
    }

    function is_paypal_email_verified() {
        $payer_email_select_sql = "	SELECT p.payer_email, o.customers_id
									FROM " . TABLE_PAYPAL . " AS p
									INNER JOIN " . TABLE_ORDERS . " AS o
										ON (p.invoice = o.orders_id)
									WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $payer_email_result_sql = tep_db_query($payer_email_select_sql);
        if ($payer_email_row = tep_db_fetch_array($payer_email_result_sql)) {
            $info_verified_select_sql = "	SELECT info_verified
											FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
											WHERE customers_id = '" . (int) $payer_email_row['customers_id'] . "'
												AND customers_info_value = '" . tep_db_input($payer_email_row['payer_email']) . "'
												AND info_verification_type = 'email'";
            $info_verified_result_sql = tep_db_query($info_verified_select_sql);
            if ($info_verified_row = tep_db_fetch_array($info_verified_result_sql)) {
                if ($info_verified_row['info_verified'] == 1) {
                    $this->execute_log .= "is_paypal_email_verified(): true\n";

                    return true;
                } else {
                    $this->execute_log .= "is_paypal_email_verified(): false\n";

                    return false;
                }
            } else {
                $this->execute_log .= "is_paypal_email_verified(): false\n";

                return false;
            }
        } else {
            $this->execute_log .= "is_paypal_email_verified(): Order not made by Paypal\n";

            return false;
        }
    }

    function is_pg_email_verified() {
        $return_int = 0;
        $payment_table = '';
        $payment_join = '';
        $payment_search_field = '';
        $pipwave = new pipwave($this->orders_id);
        if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
            $walletEmailName = '';
            if (isset($pipwave->payment_method_code)) {
                $pmInfo = $pipwave->pipwavePaymentMapper('pipwave', $pipwave->payment_method_code);
            }
            if (isset($pmInfo['pg_code'])) {
                switch ($pmInfo['pg_code']) {
                    case 'paypal':
                    case 'paypalEC':
                        $walletEmailName = 'payer_email';
                        break;
                    case 'moneybookers':
                        $walletEmailName = 'moneybooker_email';
                        break;
                }
            }
            if (!empty($walletEmailName)) {
                $pg_email_select_sql = "	SELECT api.info_value, o.customers_id
                                        FROM " . TABLE_ORDERS . " AS o
                                        INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS api
                                            ON (o.orders_id = api.orders_id)
                                        WHERE o.orders_id = '" . (int) $this->orders_id . "'
                                            AND api.info_key = '" . $walletEmailName . "'";
                $pg_email_result_sql = tep_db_query($pg_email_select_sql);
                if ($pg_email_row = tep_db_fetch_array($pg_email_result_sql)) {
                    $info_verified_select_sql = "	SELECT info_verified
                                                FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                                WHERE customers_id = '" . (int) $pg_email_row['customers_id'] . "'
                                                    AND customers_info_value = '" . tep_db_input($pg_email_row['info_value']) . "'
                                                    AND info_verification_type = 'email'";
                    $info_verified_result_sql = tep_db_query($info_verified_select_sql);
                    if ($info_verified_row = tep_db_fetch_array($info_verified_result_sql)) {
                        if ($info_verified_row['info_verified'] == 1) {
                            // email existand has been verified
                            $return_int = 1;
                        } else {
                            // email exist but has not been verified
                            $return_int = -1;
                        }
                    } else {
                        // email doesn't exist and has not been verified
                        $return_int = -2;
                    }
                } else {
                    // no record found
                    $return_int = 0;
                }
                if ($return_int !== 0) {
                    if ($return_int === 1) {
                        $this->execute_log .= "is_pg_email_verified(): true\n";
                    } else {    // $info_verified == -1 || $info_verified == -2
                        $this->execute_log .= "is_pg_email_verified(): false\n";
                    }
                } else {
                    $this->execute_log .= "is_pg_email_verified(): Order not made by " . $pipwave->payment_method_code . "\n";
                }
            } else {
                $this->execute_log .= "is_pg_email_verified(): not checked\n";
                // payment_method not found.
            }
        } else {
            $payment_methods_filename = $this->get_payment_methods_filename();

            if (tep_not_empty($payment_methods_filename)) {
                switch (strtolower($payment_methods_filename)) {
                    case 'paypal.php':
                        $payment_table = TABLE_PAYPAL;
                        $payment_join = 'p.invoice = o.orders_id';
                        $payment_search_field = 'payer_email';

                        break;
                    case 'paypalec.php':
                        $payment_table = TABLE_PAYPALEC;
                        $payment_join = 'p.paypal_order_id = o.orders_id';
                        $payment_search_field = 'payer_email';

                        break;
                    case 'moneybookers.php':
                        $payment_table = TABLE_PAYMENT_MONEYBOOKERS;
                        $payment_join = 'p.mb_trans_id = o.orders_id';
                        $payment_search_field = 'mb_payer_email';

                        break;
                }

                if (tep_not_empty($payment_table)) {
                    $payer_email_select_sql = "	SELECT p." . $payment_search_field . " as email, o.customers_id
                                            FROM " . $payment_table . " AS p
                                            INNER JOIN " . TABLE_ORDERS . " AS o
                                                ON (" . $payment_join . ")
                                            WHERE o.orders_id = '" . (int) $this->orders_id . "'";
                    $payer_email_result_sql = tep_db_query($payer_email_select_sql);
                    if ($payer_email_row = tep_db_fetch_array($payer_email_result_sql)) {
                        $info_verified_select_sql = "	SELECT info_verified
                                                    FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                                    WHERE customers_id = '" . (int) $payer_email_row['customers_id'] . "'
                                                        AND customers_info_value = '" . tep_db_input($payer_email_row['email']) . "'
                                                        AND info_verification_type = 'email'";
                        $info_verified_result_sql = tep_db_query($info_verified_select_sql);
                        if ($info_verified_row = tep_db_fetch_array($info_verified_result_sql)) {
                            if ($info_verified_row['info_verified'] == 1) {
                                // email existand has been verified
                                $return_int = 1;
                            } else {
                                // email exist but has not been verified
                                $return_int = -1;
                            }
                        } else {
                            // email doesn't exist and has not been verified
                            $return_int = -2;
                        }
                    } else {
                        // no record found
                        $return_int = 0;
                    }

                    if ($return_int !== 0) {
                        if ($return_int === 1) {
                            $this->execute_log .= "is_pg_email_verified(): true\n";
                        } else {    // $info_verified == -1 || $info_verified == -2
                            $this->execute_log .= "is_pg_email_verified(): false\n";
                        }
                    } else {
                        $this->execute_log .= "is_pg_email_verified(): Order not made by " . ucfirst(str_ireplace(".php", "", $payment_methods_filename)) . "\n";
                    }
                } else {
                    $this->execute_log .= "is_pg_email_verified(): not checked\n";
                    // payment_method is not in the checking list.
                }
            } else {
                $this->execute_log .= "is_pg_email_verified(): not checked\n";
                // payment_method not found.
            }
        }

        return ($return_int === 1);
    }

    function is_phone_verified() {
        $phone_verified_select_sql = "	SELECT customers_phone_verified
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . (int) $this->customers_id . "'";
        $phone_verified_result_sql = tep_db_query($phone_verified_select_sql);
        $phone_verified_row = tep_db_fetch_array($phone_verified_result_sql);

        if ($phone_verified_row['customers_phone_verified'] == 1) {
            $this->execute_log .= "is_phone_verified(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_phone_verified(): false\n";

            return false;
        }
    }

    function is_telephone_verified() {
        $telephone_select_sql = "	SELECT customers_country_international_dialing_code, customers_telephone
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
        $telephone_result_sql = tep_db_query($telephone_select_sql);
        $telephone_row = tep_db_fetch_array($telephone_result_sql);

        $country_code = $telephone_row['customers_country_international_dialing_code'];
        $telephone = preg_replace('/[^\d]/', '', $telephone_row['customers_telephone']);

        if (!tep_not_null($country_code)) {
            $dialing_code_select_sql = "SELECT countries_international_dialing_code
										FROM " . TABLE_ORDERS . " AS o
										LEFT JOIN " . TABLE_COUNTRIES . " AS coun
											ON (coun.countries_name = o.customers_country)
										WHERE o.orders_id = '" . (int) $this->orders_id . "'";
            $dialing_code_result_sql = tep_db_query($dialing_code_select_sql);
            $dialing_code_row = tep_db_fetch_array($dialing_code_result_sql);

            $country_code = $dialing_code_row['countries_international_dialing_code'];
        }

        $complete_tel = $country_code . $telephone;

        $info_verified_info = tep_info_verified_check($this->customers_id, $complete_tel, 'telephone');
        if ($info_verified_info !== FALSE) {
            if (in_array($info_verified_info, array(1, 'A', 'M'))) {
                $this->execute_log .= "is_telephone_verified(): true\n";

                return true;
            } else {
                $this->execute_log .= "is_telephone_verified(): false\n";

                return false;
            }
        } else {
            $this->execute_log .= "is_telephone_verified(): false\n";

            return false;
        }
    }

    function is_order_fully_nrsc($logged = true) {
        $result = false;

        if ($this->get_order_payment_method(false) == 0 && $this->get_order_payment_method_parent(false) == 0) {
            $store_credit_history_id_select_sql = " SELECT store_credit_history_id
                                                    FROM " . TABLE_STORE_CREDIT_HISTORY . "
                                                    WHERE store_credit_history_trans_type = 'C'
                                                        AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
                                                        AND store_credit_activity_type = 'P'
                                                        AND store_credit_account_type = 'NR'";
            $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
            if (tep_db_num_rows($store_credit_history_id_result_sql) > 0) {
                $store_credit_history_id_select_sql = " SELECT store_credit_history_id
                                                        FROM " . TABLE_STORE_CREDIT_HISTORY . "
                                                        WHERE store_credit_history_trans_type = 'C'
                                                            AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
                                                            AND store_credit_activity_type = 'P'
                                                            AND store_credit_account_type = 'R'";
                $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
                if (!tep_db_num_rows($store_credit_history_id_result_sql)) {
                    $result = true;
                }
            }
        }

        if ($logged) {
            if ($result == true) {
                $this->execute_log .= "is_order_fully_nrsc(): true\n";
            } else {
                $this->execute_log .= "is_order_fully_nrsc(): false\n";
            }
        }

        return $result;
    }

    function is_order_fully_rsc($logged = true) {
        $result = false;

        if ($this->get_order_payment_method(false) == 0 && $this->get_order_payment_method_parent(false) == 0) {
            $store_credit_history_id_select_sql = "	SELECT store_credit_history_id
													FROM " . TABLE_STORE_CREDIT_HISTORY . "
													WHERE store_credit_history_trans_type = 'C'
														AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
														AND store_credit_activity_type = 'P'
														AND store_credit_account_type = 'R'";
            $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
            if (tep_db_num_rows($store_credit_history_id_result_sql) > 0) {
                $store_credit_history_id_select_sql = "	SELECT store_credit_history_id
														FROM " . TABLE_STORE_CREDIT_HISTORY . "
														WHERE store_credit_history_trans_type = 'C'
															AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
															AND store_credit_activity_type = 'P'
															AND store_credit_account_type = 'NR'";
                $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
                if (!tep_db_num_rows($store_credit_history_id_result_sql)) {
                    $result = true;
                }
            }
        }

        if ($logged) {
            if ($result == true) {
                $this->execute_log .= "is_order_fully_rsc(): true\n";
            } else {
                $this->execute_log .= "is_order_fully_rsc(): false\n";
            }
        }

        return $result;
    }

    function is_order_nrp_payment() {
        $result = false;
        $payment_info_array = array();

        $payment_methods_obj = new payment_methods('payment_methods');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        if (isset($payment_info_array[$this->get_order_payment_method(false)]) && $payment_info_array[$this->get_order_payment_method(false)]->confirm_complete_days <= 0) {
            $orders_total_select_sql = "SELECT orders_id FROM " . TABLE_ORDERS_TOTAL . " WHERE orders_id = '" . (int) $this->orders_id . "' AND class = 'ot_gv'";
            $orders_total_result_sql = tep_db_query($orders_total_select_sql);

            if (tep_db_num_rows($orders_total_result_sql) > 0) {
                $store_credit_history_id_select_sql = "	SELECT store_credit_history_id
														FROM " . TABLE_STORE_CREDIT_HISTORY . "
														WHERE store_credit_history_trans_type = 'C'
															AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
															AND store_credit_activity_type = 'P'
															AND store_credit_account_type = 'R'";
                $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
                if (tep_db_num_rows($store_credit_history_id_result_sql) < 1) {
                    $result = true;
                }
            } else {
                $result = true;
            }
        } else if (!isset($payment_info_array[$this->get_order_payment_method(false)]) && $this->order_with_sc) {
            $result = true;
            $store_credit_history_id_select_sql = "	SELECT store_credit_history_id
													FROM " . TABLE_STORE_CREDIT_HISTORY . "
													WHERE store_credit_history_trans_type = 'C'
														AND store_credit_history_trans_id = '" . (int) $this->orders_id . "'
														AND store_credit_activity_type = 'P'
														AND store_credit_account_type = 'R'";
            $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
            if (tep_db_num_rows($store_credit_history_id_result_sql)) {
                $result = false;
            }
        }

        if ($result == true) {
            $this->execute_log .= "is_order_nrp_payment(): true\n";
        } else {
            $this->execute_log .= "is_order_nrp_payment(): false\n";
        }

        return $result;
    }

    function is_gc_order() {
        $result = $this->order_with_gc;

        if ($result == true) {
            $this->execute_log .= "is_gc_order(): true\n";
        } else {
            $this->execute_log .= "is_gc_order(): false\n";
        }

        return $result;
    }

    function post_capture($line = '') {
        $post_capture = false;

        $pipwave = new pipwave($this->orders_id);
        if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
            $captureRes = $pipwave->captureAPI();
            if (isset($captureRes['status']) && $captureRes['status'] == 200) {
                $post_capture = true;
            }
        } else {
            $payment_method_select_sql = "	SELECT o.payment_methods_parent_id, o.payment_methods_id, pm.payment_methods_filename
    									FROM " . TABLE_ORDERS . " AS o
    									INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
    										ON o.payment_methods_parent_id = pm.payment_methods_id
    									WHERE o.orders_id = '" . (int) $this->orders_id . "'
    										";
            $payment_method_result_sql = tep_db_query($payment_method_select_sql);
            if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
                switch ($payment_method_row['payment_methods_filename']) {
                    case 'adyen.php':
                        $payment_methods_obj = new payment_methods($payment_method_row['payment_methods_id']);
                        $payment_methods_obj = $payment_methods_obj->payment_method_array;

                        if ((int) $payment_methods_obj->payment_methods_id > 0) {
                            if (method_exists($payment_methods_obj, 'post_capture')) {
                                $capture_response = $payment_methods_obj->post_capture($this->orders_id, 'system');
                            }
                            if ($capture_response['code'] == 'success') {
                                $post_capture = true;
                            }
                        }
                        break;
                    case 'bibit.php':
                        $payment_methods_obj = new payment_methods($payment_method_row['payment_methods_id']);
                        $payment_methods_obj = $payment_methods_obj->payment_method_array;

                        if ((int) $payment_methods_obj->payment_methods_id > 0) {
                            if (method_exists($payment_methods_obj, 'check_trans_status')) {
                                $payment_methods_obj->check_trans_status($this->orders_id);
                            }

                            if (method_exists($payment_methods_obj, 'post_capture')) {
                                $capture_response = $payment_methods_obj->post_capture($this->orders_id, 'system');
                            }

                            if ($capture_response['code'] == 'success') {
                                $post_capture = true;
                            }
                        }
                        break;
                    case 'global_collect.php':
                        $payment_methods_obj = new payment_methods($payment_method_row['payment_methods_id']);
                        $payment_methods_obj = $payment_methods_obj->payment_method_array;

                        if ((int) $payment_methods_obj->payment_methods_id > 0) {
                            if (method_exists($payment_methods_obj, 'post_authorisation')) {
                                $capture_response = $payment_methods_obj->post_authorisation($this->orders_id);
                            }

                            if (strtolower($capture_response['RESULT']) == 'ok') {
                                $post_capture = true;
                            }
                        }
                        break;
                    case 'payU.php':
                        $payment_methods_obj = new payment_methods($payment_method_row['payment_methods_id']);
                        $payment_methods_obj = $payment_methods_obj->payment_method_array;

                        if ((int) $payment_methods_obj->payment_methods_id > 0) {
                            if (method_exists($payment_methods_obj, 'post_capture')) {
                                $capture_response = $payment_methods_obj->post_capture($this->orders_id, 'system');
                            }
                            if ($capture_response['code'] == 'success') {
                                $post_capture = true;
                            }
                        }
                        break;
                }
            }
        }

        $capture_msg = 'post_capture(): ' . (($post_capture) ? 'Post Capture Success' : 'Post Capture Failed') . "\n";

        if (tep_not_null($line)) {
            $capture_msg .= 'Line #' . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
        }

        $this->execute_log .= $capture_msg;

        return $post_capture;
    }

    function is_share_ip() {
        $order_info_select_sql = "	SELECT remote_addr, date_purchased
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        $reference_date_time = tep_not_null($order_info_row['date_purchased']) ? $order_info_row['date_purchased'] : date('Y-m-d H:i:s');

        $share_ip_select_sql = "SELECT DISTINCT(c.customers_id) AS user_id
								FROM " . TABLE_ORDERS . " AS o
								INNER JOIN " . TABLE_CUSTOMERS . " AS c
									ON (o.customers_id = c.customers_id AND c.customers_id <> '" . (int) $this->customers_id . "')
								WHERE o.date_purchased >= DATE_SUB('" . $reference_date_time . "', INTERVAL 7 DAY)
									AND o.date_purchased <= DATE_ADD('" . $reference_date_time . "', INTERVAL 7 DAY)
									AND o.remote_addr = '" . tep_db_input($order_info_row['remote_addr']) . "'";
        $share_ip_result_sql = tep_db_query($share_ip_select_sql);
        if (tep_db_num_rows($share_ip_result_sql) > 0) {
            $this->execute_log .= "is_share_ip(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_share_ip(): false\n";

            return false;
        }
    }

    function is_share_ip_with_cb_customers() {
        $order_info_select_sql = "	SELECT remote_addr, date_purchased
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        $reference_date_time = tep_not_null($order_info_row['date_purchased']) ? $order_info_row['date_purchased'] : date('Y-m-d H:i:s');

        $share_ip_select_sql = "SELECT DISTINCT(c.customers_id) AS user_id
								FROM " . TABLE_ORDERS . " AS o
								INNER JOIN " . TABLE_CUSTOMERS . " AS c
									ON (o.customers_id = c.customers_id AND FIND_IN_SET(4, customers_flag) AND c.customers_id <> '" . (int) $this->customers_id . "')
								WHERE o.date_purchased >= DATE_SUB('" . $reference_date_time . "', INTERVAL 7 DAY)
									AND o.date_purchased <= DATE_ADD('" . $reference_date_time . "', INTERVAL 7 DAY)
									AND o.remote_addr = '" . tep_db_input($order_info_row['remote_addr']) . "'";
        $share_ip_result_sql = tep_db_query($share_ip_select_sql);
        if (tep_db_num_rows($share_ip_result_sql) > 0) {
            $this->execute_log .= "is_share_ip_with_cb_customers(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_share_ip_with_cb_customers(): false\n";

            return false;
        }
    }

    function is_share_tel() {
        $order = new order($this->orders_id);
        $customer_id_array = array();

        $tel_code = preg_replace('/[^\d]/', '', $order->customer['order_country_code']);
        $tel_num = preg_replace('/[^\d]/', '', $order->customer['telephone']);

        $share_tel_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
									FROM " . TABLE_ORDERS . " AS o
									LEFT JOIN " . TABLE_COUNTRIES . " AS country
										ON (o.customers_country = country.countries_name)
									WHERE IF(o.customers_country_international_dialing_code IS NOT NULL, o.customers_country_international_dialing_code, country.countries_international_dialing_code) = '" . tep_db_input($tel_code) . "'
										AND o.customers_telephone = '" . tep_db_input($tel_num) . "'
										AND o.customers_id <> '" . (int) $order->customer['id'] . "'
										AND o.customers_telephone <> ''";
        $share_tel_result_sql = tep_db_query($share_tel_select_sql);
        if (tep_db_num_rows($share_tel_result_sql) > 0) {
            $this->execute_log .= "is_share_tel(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_share_tel(): false\n";

            return false;
        }
    }

    function is_share_tel_with_cb_customers() {
        $order = new order($this->orders_id);
        $customer_id_array = array();

        $tel_code = preg_replace('/[^\d]/', '', $order->customer['order_country_code']);
        $tel_num = preg_replace('/[^\d]/', '', $order->customer['telephone']);

        $share_tel_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
									FROM " . TABLE_ORDERS . " AS o
									LEFT JOIN " . TABLE_COUNTRIES . " AS country
										ON (o.customers_country = country.countries_name)
									WHERE IF(o.customers_country_international_dialing_code IS NOT NULL, o.customers_country_international_dialing_code, country.countries_international_dialing_code) = '" . tep_db_input($tel_code) . "'
										AND o.customers_telephone = '" . tep_db_input($tel_num) . "'
										AND o.customers_id <> '" . (int) $order->customer['id'] . "'
										AND o.customers_telephone <> ''";
        $share_tel_result_sql = tep_db_query($share_tel_select_sql);
        while ($share_tel_row = tep_db_fetch_array($share_tel_result_sql)) {
            $customer_id_array[] = $share_tel_row['cust_id'];
        }

        if (sizeof($customer_id_array) > 0) {
            $customers_id_select_sql = "	SELECT customers_id
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id IN (" . implode(',', $customer_id_array) . ")
												AND FIND_IN_SET(4, customers_flag)";
            $customers_id_result_sql = tep_db_query($customers_id_select_sql);
            if (tep_db_num_rows($customers_id_result_sql) > 0) {
                $this->execute_log .= "is_share_tel_with_cb_customers(): true\n";

                return true;
            } else {
                $this->execute_log .= "is_share_tel_with_cb_customers(): false\n";

                return false;
            }
        } else {
            $this->execute_log .= "is_share_tel_with_cb_customers(): false\n";

            return false;
        }
    }

    function is_share_credit_card() {
        $payment_method_select_sql = "	SELECT pm.payment_methods_filename
    									FROM " . TABLE_ORDERS . " AS o
    									INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
    										ON o.payment_methods_parent_id = pm.payment_methods_id
    									WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $payment_method_result_sql = tep_db_query($payment_method_select_sql);
        $payment_method_row = tep_db_fetch_array($payment_method_result_sql);

        switch ($payment_method_row['payment_methods_filename']) {
            case 'adyen.php':
                $adyen_card_number_select_sql = "	SELECT adyen_cc_card_summary, adyen_cc_expiry_date
													FROM " . TABLE_ADYEN . "
													WHERE adyen_order_id = '" . (int) $this->orders_id . "'";
                $adyen_card_number_result_sql = tep_db_query($adyen_card_number_select_sql);
                if ($adyen_card_number_row = tep_db_fetch_array($adyen_card_number_result_sql)) {
                    $check_credit_card_select_sql = "	SELECT o.customers_id
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_ADYEN . " AS a
															ON (o.orders_id = a.adyen_order_id)
														WHERE a.adyen_cc_card_summary = '" . tep_db_input($adyen_card_number_row['adyen_cc_card_summary']) . "'
															AND a.adyen_cc_expiry_date = '" . tep_db_input($adyen_card_number_row['adyen_cc_expiry_date']) . "'
														GROUP BY o.customers_id";
                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                    if (tep_db_num_rows($check_credit_card_result_sql) > 1) {
                        $this->execute_log .= "is_share_credit_card(): true\n";

                        return true;
                    } else {
                        $this->execute_log .= "is_share_credit_card(): false\n";

                        return false;
                    }
                } else {
                    $this->execute_log .= "is_share_credit_card(): No Credit Card Number Found\n";

                    return false;
                }
                break;
            case 'payU.php':
                $payu_card_number_select_sql = "	SELECT cc_number, cc_expiration_date
													FROM " . TABLE_PAYU . "
													WHERE order_id = '" . $this->orders_id . "'";
                $payu_card_number_result_sql = tep_db_query($payu_card_number_select_sql);
                if ($payu_card_number_row = tep_db_fetch_array($payu_card_number_result_sql)) {
                    $check_credit_card_select_sql = "	SELECT o.customers_id
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_PAYU . " AS p
															ON (o.orders_id = p.order_id)
														WHERE p.cc_number = '" . tep_db_input($payu_card_number_row['cc_number']) . "'
															AND p.cc_expiration_date = '" . tep_db_input($payu_card_number_row['cc_expiration_date']) . "'
														GROUP BY o.customers_id";
                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                    if (tep_db_num_rows($check_credit_card_result_sql) > 1) {
                        $this->execute_log .= "is_share_credit_card(): true\n";

                        return true;
                    } else {
                        $this->execute_log .= "is_share_credit_card(): false\n";

                        return false;
                    }
                } else {
                    $this->execute_log .= "is_share_credit_card(): No Credit Card Number Found\n";

                    return false;
                }
                break;
            case 'bibit.php':
                $bibit_card_number_select_sql = "	SELECT bibit_card_number
													FROM " . TABLE_BIBIT . "
													WHERE orders_id = '" . (int) $this->orders_id . "'";
                $bibit_card_number_result_sql = tep_db_query($bibit_card_number_select_sql);
                if ($bibit_card_number_row = tep_db_fetch_array($bibit_card_number_result_sql)) {
                    $check_credit_card_select_sql = "	SELECT o.customers_id
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_BIBIT . " AS b
															ON (o.orders_id = b.orders_id)
														WHERE b.bibit_card_number = '" . tep_db_input($bibit_card_number_row['bibit_card_number']) . "'
														GROUP BY o.customers_id";
                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                    if (tep_db_num_rows($check_credit_card_result_sql) > 1) {
                        $this->execute_log .= "is_share_credit_card(): true\n";

                        return true;
                    } else {
                        $this->execute_log .= "is_share_credit_card(): false\n";

                        return false;
                    }
                } else {
                    $this->execute_log .= "is_share_credit_card(): No Credit Card Number Found\n";

                    return false;
                }
                break;
            case 'global_collect.php':
                $global_collect_card_number_select_sql = "	SELECT global_collect_cc_last_4_digit, global_collect_cc_expiry_date
															FROM " . TABLE_GLOBAL_COLLECT . "
															WHERE global_collect_orders_id = '" . (int) $this->orders_id . "'";
                $global_collect_card_number_result_sql = tep_db_query($global_collect_card_number_select_sql);

                if ($global_collect_card_number_row = tep_db_fetch_array($global_collect_card_number_result_sql)) {
                    $check_credit_card_select_sql = "	SELECT o.customers_id
														FROM " . TABLE_ORDERS . " AS o
														INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc
															ON (o.orders_id = gc.global_collect_orders_id)
														WHERE gc.global_collect_cc_last_4_digit = '" . tep_db_input($global_collect_card_number_row['global_collect_cc_last_4_digit']) . "'
															AND gc.global_collect_cc_expiry_date = '" . tep_db_input($global_collect_card_number_row['global_collect_cc_expiry_date']) . "'
														GROUP BY o.customers_id";
                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                    if (tep_db_num_rows($check_credit_card_result_sql) > 1) {
                        $this->execute_log .= "is_share_credit_card(): true\n";

                        return true;
                    } else {
                        $this->execute_log .= "is_share_credit_card(): false\n";

                        return false;
                    }
                } else {
                    $this->execute_log .= "is_share_credit_card(): No Credit Card Number Found\n";

                    return false;
                }
                break;
        }
        $this->execute_log .= "is_share_credit_card(): Payment Not Support Credit Card Number Checking\n";

        return false;
    }

    function is_cardholder_authenticated() {
        $authenticated_result = false;

        $payment_method_select_sql = "	SELECT pm.payment_methods_filename
    									FROM " . TABLE_PAYMENT_METHODS . " AS pm
    									WHERE pm.payment_methods_id = '" . $this->payment_gateway_id . "'";
        $payment_method_result_sql = tep_db_query($payment_method_select_sql);
        $payment_method_row = tep_db_fetch_array($payment_method_result_sql);

        switch ($payment_method_row['payment_methods_filename']) {
            case 'adyen.php':
                $adyen_select_sql = "	SELECT adyen_cc_three_d_auth
                                        FROM " . TABLE_ADYEN . "
                                        WHERE adyen_order_id = '" . (int) $this->orders_id . "'";
                $adyen_result_sql = tep_db_query($adyen_select_sql);
                if ($adyen_row = tep_db_fetch_array($adyen_result_sql)) {
                    if ($adyen_row['adyen_cc_three_d_auth'] == 'true') {
                        $this->execute_log .= "is_cardholder_authenticated: true\n";

                        $authenticated_result = true;
                    } else {
                        $this->execute_log .= "is_cardholder_authenticated: false\n";
                    }
                } else {
                    $this->execute_log .= "is_cardholder_authenticated: Info Not Found\n";
                }
                break;
            case 'payU.php':
                $payu_select_sql = "	SELECT response_code
                                        FROM " . TABLE_PAYU . "
                                        WHERE order_id = '" . $this->orders_id . "'";
                $payu_result_sql = tep_db_query($payu_select_sql);
                if ($payu_row = tep_db_fetch_array($payu_result_sql)) {
                    if ($payu_row['response_code'] == 'APPROVED') {
                        $this->execute_log .= "is_cardholder_authenticated: true\n";

                        $authenticated_result = true;
                    } else {
                        $this->execute_log .= "is_cardholder_authenticated: false\n";
                    }
                } else {
                    $this->execute_log .= "is_cardholder_authenticated: Info Not Found\n";
                }
                break;
            case 'bibit.php':
                $bibit_select_sql = "	SELECT bibit_cardholder_aut_result
                                        FROM " . TABLE_BIBIT . "
                                        WHERE orders_id = '" . (int) $this->orders_id . "'";
                $bibit_result_sql = tep_db_query($bibit_select_sql);
                if ($bibit_row = tep_db_fetch_array($bibit_result_sql)) {
                    if ($bibit_row['bibit_cardholder_aut_result'] == 'Cardholder authenticated') {
                        $this->execute_log .= "is_cardholder_authenticated: true\n";

                        $authenticated_result = true;
                    } else {
                        $this->execute_log .= "is_cardholder_authenticated: false\n";
                    }
                } else {
                    $this->execute_log .= "is_cardholder_authenticated: Info Not Found\n";
                }
                break;
            case 'global_collect.php':
                $global_collect_select_sql = "	SELECT global_collect_eci
                                                FROM " . TABLE_GLOBAL_COLLECT . "
                                                WHERE global_collect_orders_id = '" . (int) $this->orders_id . "'";
                $global_collect_result_sql = tep_db_query($global_collect_select_sql);

                if ($global_collect_card_number_row = tep_db_fetch_array($global_collect_result_sql)) {
                    if ($global_collect_card_number_row['global_collect_eci'] == '2' || $global_collect_card_number_row['global_collect_eci'] == '5') {
                        $this->execute_log .= "is_cardholder_authenticated: true\n";

                        $authenticated_result = true;
                    } else {
                        $this->execute_log .= "is_cardholder_authenticated: false\n";
                    }
                } else {
                    $this->execute_log .= "is_cardholder_authenticated: Info Not Found\n";
                }
                break;
        }
        $this->execute_log .= "is_cardholder_authenticated: Not a Credit Card Payment Method\n";

        return $authenticated_result;
    }

    function is_product_fully_delivered($orders_products_id) {
        global $order;

        $pd_fully_delivered = true;

        $products_id_select_sql = "	SELECT products_id
									FROM " . TABLE_ORDERS_PRODUCTS . "
									WHERE orders_products_id = '" . (int) $orders_products_id . "'";
        $products_id_result_sql = tep_db_query($products_id_select_sql);
        $products_id_row = tep_db_fetch_array($products_id_result_sql);

        $product_select_sql = "	SELECT products_bundle, products_bundle_dynamic
								FROM " . TABLE_PRODUCTS . "
								WHERE products_id = '" . (int) $products_id_row['products_id'] . "'";
        $product_result_sql = tep_db_query($product_select_sql);
        if ($product_row = tep_db_fetch_array($product_result_sql)) {
            if ($product_row['products_bundle_dynamic'] == 'yes') { // dynamic bundle
            } else if ($product_row['products_bundle'] == 'yes') { // static bundle
                $pd_delivered_quantity_select_sql = "	SELECT orders_products_id, products_delivered_quantity, products_quantity
														FROM " . TABLE_ORDERS_PRODUCTS . "
														WHERE orders_id = '" . (int) $this->orders_id . "'
															AND parent_orders_products_id = '" . (int) $orders_products_id . "'
															AND orders_products_is_compensate = 0";
                $pd_delivered_quantity_result_sql = tep_db_query($pd_delivered_quantity_select_sql);
                while ($pd_delivered_quantity_row = tep_db_fetch_array($pd_delivered_quantity_result_sql)) {
                    if ($pd_delivered_quantity_row['products_delivered_quantity'] < $pd_delivered_quantity_row['products_quantity']) {
                        $this->delivery_details[$pd_delivered_quantity_row['orders_products_id']]['products_delivery_status'] = false;
                        $pd_fully_delivered = false;
                    }
                }
            } else {
                $pd_delivered_quantity_select_sql = "	SELECT products_delivered_quantity, products_quantity
														FROM " . TABLE_ORDERS_PRODUCTS . "
														WHERE orders_id = '" . (int) $this->orders_id . "'
															AND orders_products_id = '" . (int) $orders_products_id . "'";
                $pd_delivered_quantity_result_sql = tep_db_query($pd_delivered_quantity_select_sql);
                $pd_delivered_quantity_row = tep_db_fetch_array($pd_delivered_quantity_result_sql);

                if ($pd_delivered_quantity_row['products_delivered_quantity'] < $pd_delivered_quantity_row['products_quantity']) {
                    $this->delivery_details[$orders_products_id]['products_delivery_status'] = false;
                    $pd_fully_delivered = false;
                }
            }
        }

        if (!is_object($order)) {
            $order = new order($this->orders_id);
        }

        if (!isset($order->info['compensation_fully_delivered'])) {
            $order->get_compensate_products();
        }

        if ($order->info['compensation_fully_delivered'] != 1)
            $pd_fully_delivered = false;

        return $pd_fully_delivered;
    }

    function is_sharing_paypal_id() {
        $is_sharing_res = false;

        $payer_id_select_sql_select_sql = "	SELECT p.payer_id
											FROM " . TABLE_ORDERS . " AS o
											INNER JOIN " . TABLE_PAYPAL . " AS p
												ON (p.invoice = o.orders_id)
											WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $payer_id_select_sql_result_sql = tep_db_query($payer_id_select_sql_select_sql);
        if ($payer_id_select_sql_row = tep_db_fetch_array($payer_id_select_sql_result_sql)) {
            $paypal_select_sql = "	SELECT DISTINCT(customers_id)
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_PAYPAL . " AS p
										ON (p.invoice = o.orders_id)
									WHERE p.payer_id = '" . tep_db_input($payer_id_select_sql_row['payer_id']) . "'";
            $paypal_result_sql = tep_db_query($paypal_select_sql);

            if (tep_db_num_rows($paypal_result_sql) > 1) {
                $is_sharing_res = true;
            }
        }

        if ($is_sharing_res == true) {
            $this->execute_log .= "is_sharing_paypal_id(): true\n";

            return true;
        } else {
            $this->execute_log .= "is_sharing_paypal_id(): false\n";

            return false;
        }
    }

    function is_sharing_pg_id() {
        $is_sharing_res = FALSE;
        $payment_table = '';
        $payment_join = '';
        $payment_search_field = '';
        $payment_methods_filename = $this->get_payment_methods_filename();

        if (tep_not_empty($payment_methods_filename)) {
            switch (strtolower($payment_methods_filename)) {
                case 'paypal.php':
                    $payment_table = TABLE_PAYPAL;
                    $payment_join = 'p.invoice = o.orders_id';
                    $payment_search_field = 'payer_id';

                    break;
                case 'paypalec.php':
                    $payment_table = TABLE_PAYPALEC;
                    $payment_join = 'p.paypal_order_id = o.orders_id';
                    $payment_search_field = 'payer_email';

                    break;
                case 'moneybookers.php':
                    $payment_table = TABLE_PAYMENT_MONEYBOOKERS;
                    $payment_join = 'p.mb_trans_id = o.orders_id';
                    $payment_search_field = 'mb_payer_email';

                    break;
            }

            if (tep_not_empty($payment_table)) {
                $select_sql = "	SELECT p." . $payment_search_field . " as search_val
                                FROM " . TABLE_ORDERS . " AS o
                                INNER JOIN " . $payment_table . " AS p
                                    ON (" . $payment_join . ")
                                WHERE o.orders_id = '" . (int) $this->orders_id . "'";
                $result_sql = tep_db_query($select_sql);
                if ($result_row = tep_db_fetch_array($result_sql)) {
                    $select2_sql = "	SELECT DISTINCT(o.customers_id)
                                        FROM " . TABLE_ORDERS . " AS o
                                        INNER JOIN " . $payment_table . " AS p
                                            ON (" . $payment_join . ")
                                        WHERE p." . $payment_search_field . " = '" . $result_row['search_val'] . "'";
                    $result2_sql = tep_db_query($select2_sql);
                    if (tep_db_num_rows($result2_sql) > 1) {
                        $is_sharing_res = TRUE;
                    }
                }

                if ($is_sharing_res === TRUE) {
                    $this->execute_log .= "is_sharing_pg_id(): true\n";
                } else {
                    $this->execute_log .= "is_sharing_pg_id(): false\n";
                }
            } else {
                $this->execute_log .= "is_sharing_pg_id(): not checked\n";
                // payment_method is not in the checking list.
            }
        } else {
            $this->execute_log .= "is_sharing_pg_id(): not checked\n";
            // payment_method not found.
        }

        return $is_sharing_res;
    }

    function is_within_pg_purchase_limit($period, $amount_in_usd = '-1') {
        $completed_per_period_array = array('day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '1 MONTH');

        $periodical_sales_total_amount = $this->get_total_pg_amount($completed_per_period_array[$period], true);

        if ($amount_in_usd == '-1') {
            $this->execute_log .= "is_within_pg_purchase_limit(" . $period . "): not checked\n";

            return false;
        } else if ($periodical_sales_total_amount > (int) $amount_in_usd) {

            $this->execute_log .= "is_within_pg_purchase_limit(" . $period . ", " . $amount_in_usd . "): false\n";

            return false;
        } else {
            $this->execute_log .= "is_within_pg_purchase_limit(" . $period . ", " . $amount_in_usd . "): true\n";

            return true;
        }
    }

    function is_within_purchase_limit($period) {
        global $order;

        $completed_per_period_array = array('day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '1 MONTH');

        $purchase_limit_per = array('day' => '-1',
            'week' => '-1',
            'month' => '-1');

        if (is_object($order) && isset($order->info['remote_addr'])) {
            $ip_address = $order->info['remote_addr'];
        } else {
            $remote_addr_select_sql = "	SELECT remote_addr
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
            $remote_addr_result_sql = tep_db_query($remote_addr_select_sql);
            $remote_addr_row = tep_db_fetch_array($remote_addr_result_sql);

            $ip_address = $remote_addr_row['remote_addr'];
        }

        $ip_country_info_array = tep_get_ip_country_info($ip_address);
        if (count($ip_country_info_array)) {
            $purchase_limit_select_sql = "	SELECT purchase_limit_per_day, purchase_limit_per_week, purchase_limit_per_month, purchase_limit_used
											FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . "
											WHERE customers_aft_groups_id = " . (int) $this->get_customer_aft_group(false) . "
												AND aft_countries_risk_type = '" . tep_db_input($ip_country_info_array['aft_risk_type']) . "'";
            $purchase_limit_result_sql = tep_db_query($purchase_limit_select_sql);
            if ($purchase_limit_row = tep_db_fetch_array($purchase_limit_result_sql)) {
                if ($purchase_limit_row['purchase_limit_used']) {
                    $purchase_limit_per['day'] = $purchase_limit_row['purchase_limit_per_day'];
                    $purchase_limit_per['week'] = $purchase_limit_row['purchase_limit_per_week'];
                    $purchase_limit_per['month'] = $purchase_limit_row['purchase_limit_per_month'];
                }
            }
        }

        $periodical_sales_total_amount = $this->get_total_rp_amount($completed_per_period_array[$period], false);

        if ($purchase_limit_per[$period] == '-1') {
            $this->execute_log .= "is_within_purchase_limit(" . $period . "): not checked\n";

            return false;
        } else if ($periodical_sales_total_amount > $purchase_limit_per[$period]) {

            $this->execute_log .= "is_within_purchase_limit(" . $period . "): false\n";

            return false;
        } else {
            $this->execute_log .= "is_within_purchase_limit(" . $period . "): true\n";

            return true;
        }
    }

    function get_purchase_limit($ip_country_info) {
        global $memcache_obj;

        $customer_aft_group_id = $this->get_customer_aft_group(false);

        $purchase_limit_per = array('day' => '-1',
            'week' => '-1',
            'month' => '-1');

        $cache_key = TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . '/purchase_limit/customers_aft_groups_id/' . $customer_aft_group_id . '/aft_countries_risk_type/' . tep_db_input($ip_country_info);

        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $purchase_limit_per = $cache_result;
        } else {
            $purchase_limit_select_sql = "	SELECT purchase_limit_per_day, purchase_limit_per_week, purchase_limit_per_month, purchase_limit_used
											FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . "
											WHERE customers_aft_groups_id = " . tep_db_input($customer_aft_group_id) . "
												AND aft_countries_risk_type = '" . tep_db_input($ip_country_info) . "'";
            $purchase_limit_result_sql = tep_db_query($purchase_limit_select_sql);
            if ($purchase_limit_row = tep_db_fetch_array($purchase_limit_result_sql)) {
                if ($purchase_limit_row['purchase_limit_used']) {
                    $purchase_limit_per['day'] = $purchase_limit_row['purchase_limit_per_day'];
                    $purchase_limit_per['week'] = $purchase_limit_row['purchase_limit_per_week'];
                    $purchase_limit_per['month'] = $purchase_limit_row['purchase_limit_per_month'];
                    $memcache_obj->store($cache_key, $purchase_limit_per, 86400);
                }
            }
        }
        return $purchase_limit_per;
    }

    function is_within_rp_purchase_limit($period) {
        global $order;

        $completed_per_period_array = array('day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '1 MONTH');

        $purchase_limit_per = array('day' => '-1',
            'week' => '-1',
            'month' => '-1');

        if (is_object($order) && isset($order->info['remote_addr'])) {
            $ip_address = $order->info['remote_addr'];
        } else {
            $remote_addr_select_sql = "	SELECT remote_addr
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . (int) $this->orders_id . "'";
            $remote_addr_result_sql = tep_db_query($remote_addr_select_sql);
            $remote_addr_row = tep_db_fetch_array($remote_addr_result_sql);

            $ip_address = $remote_addr_row['remote_addr'];
        }

        $ip_country_info_array = tep_get_ip_country_info($ip_address);

        if (count($ip_country_info_array)) {
            $purchase_limit_per = $this->get_purchase_limit($ip_country_info_array['aft_risk_type']);
        }

        $periodical_sales_total_amount = $this->get_total_rp_amount($completed_per_period_array[$period], false);

        if ($purchase_limit_per[$period] == '-1') {
            $this->execute_log .= "is_within_rp_purchase_limit(" . $period . "): not checked\n";

            return false;
        } else if ($periodical_sales_total_amount > $purchase_limit_per[$period]) {

            $this->execute_log .= "is_within_rp_purchase_limit(" . $period . "): false\n";

            return false;
        } else {
            $this->execute_log .= "is_within_rp_purchase_limit(" . $period . "): true\n";

            return true;
        }
    }

    function is_within_nrp_purchase_limit($period) {
        global $order;

        $completed_per_period_array = array('day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '1 MONTH');

        $purchase_limit_per = array('day' => 400,
            'week' => 400,
            'month' => 400);

        $periodical_sales_total_amount = $this->get_total_nrp_amount($completed_per_period_array[$period], false);

        if ($purchase_limit_per[$period] == '-1') {
            $this->execute_log .= "is_within_nrp_purchase_limit(" . $period . "): not checked\n";

            return false;
        } else if ($periodical_sales_total_amount > $purchase_limit_per[$period]) {

            $this->execute_log .= "is_within_nrp_purchase_limit(" . $period . "): false\n";

            return false;
        } else {
            $this->execute_log .= "is_within_nrp_purchase_limit(" . $period . "): true\n";

            return true;
        }
    }

    function is_match_email_domain_group($email_domain_groups_id) {
        $customers_email_select_sql = "	SELECT customers_email_address
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . (int) $this->customers_id . "'";
        $customers_email_result_sql = tep_db_query($customers_email_select_sql);
        if ($customers_email_row = tep_db_fetch_array($customers_email_result_sql)) {
            $email_address_array = explode('@', $customers_email_row['customers_email_address']);

            $email_domain_id_select_sql = "	SELECT email_domain_groups_domains_id
											FROM " . TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS . "
											WHERE email_domain_groups_domains_name = '@" . $email_address_array[1] . "'
												AND email_domain_groups_id = '" . (int) $email_domain_groups_id . "'";
            $email_domain_id_result_sql = tep_db_query($email_domain_id_select_sql);
            if (tep_db_num_rows($email_domain_id_result_sql) > 0) {
                $this->execute_log .= "is_match_email_domain_group(): true\n";

                return true;
            } else {
                $this->execute_log .= "is_match_email_domain_group(): false\n";

                return false;
            }
        } else {
            $this->execute_log .= "is_match_email_domain_group(): Customer not existed\n";

            return false;
        }
    }

    function set_customer_flag($line = '') {
        if (!$this->is_flag_customer()) {
            $this->set_customer_flag_type(1, 'set_customer_flag', $line);
        }
    }

    function set_customer_nrp_flag($line = '') {
        if (!$this->is_nrp_customer()) {
            $this->set_customer_flag_type(2, 'set_customer_nrp_flag', $line);
        }
    }

    function set_customer_cb_flag($line = '') {
        if (!$this->is_cb_customer()) {
            $this->set_customer_flag_type(3, 'set_customer_cb_flag', $line);
        }
    }

    function set_customer_group($customers_groups_id, $line = '') {
        if ($this->get_customer_group() != $customers_group_id) {
            $customers_id_select_sql = "	SELECT customers_id
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . (int) $this->orders_id . "'";
            $customers_id_result_sql = tep_db_query($customers_id_select_sql);
            if ($customers_id_row = tep_db_fetch_array($customers_id_result_sql)) {
                $sql_data_array = array('customers_groups_id' => $customers_groups_id);
                tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', " customers_id = '" . (int) $customers_id_row['customers_id'] . "'");

                if (tep_not_null($line)) {
                    $this->execute_log .= "set_customer_group():\nLine #" . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
                }
            }
        }
    }

    function set_order_status($orders_status, $line = '') {
        global $order;
        global $login_email_address;

        $curr_status = $this->get_order_status(false);
        $remark_order_status = 0;
        $comment_type = 0;
        $execute_log = '';

        if ($curr_status != $orders_status) {
            $orders_status_name_select_sql = "	SELECT orders_status_name
													FROM " . TABLE_ORDERS_STATUS . "
													WHERE orders_status_id = '" . (int) $orders_status . "'";
            $orders_status_name_result_sql = tep_db_query($orders_status_name_select_sql);
            $orders_status_name_row = tep_db_fetch_array($orders_status_name_result_sql);

            if (tep_not_null($line)) {
                $execute_log = 'set_order_status(): ' . $orders_status_name_row['orders_status_name'] . "\nLine #" . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
            }

            if ($this->aft_mode == 2) {
                $remark_order_status = $orders_status;
            }

            if ($this->changed_by == $login_email_address) {
                $comment_type = 1;
            }

            if ($this->aft_mode == 2) {
                $proceed = true;
                if (($curr_status == 7 || $curr_status == 1) && $orders_status == 2) { // Update from Pending / Verifying to Processing
                    if (!is_object($this->pw_obj)) {
                        $this->pw_obj = new pipwave($this->orders_id);
                    }
                    if (isset($this->pw_obj->checkoutSite) && $this->pw_obj->checkoutSite == 'pipwave') {
                        $payment_methods_filename = $this->get_payment_methods_filename();
                        if ($payment_methods_filename != 'offline.php') { //skip this checkout for offline pm
                            if (isset($this->pw_obj->transaction_status) && $this->pw_obj->transaction_status != 10 || !isset($this->pw_obj->transaction_status)) {
                                $proceed = false;
                                $execute_log = 'set_order_status(): ' . $orders_status_name_row['orders_status_name'] . " (Failed - order is not paid)\nLine #" . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
                            }
                        }
                    }
                    if ($proceed) {
                        $pwl_not_in_process = tep_order_product_pending_delivery(2, 1, $this->customers_id) > 0 ? false : true;
                        $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . (int) $this->customers_id . "'";
                        $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
                        if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
                            $email_firstname = $customer_profile_row["customers_firstname"];
                            $email_lastname = $customer_profile_row["customers_lastname"];
                        } else {
                            $email_firstname = $order->customer['name'];
                            $email_lastname = '';
                        }
                        $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);
                        $_res = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
                        while ($_row = tep_db_fetch_array($_res)) {
                            if (!defined($_row['cfgKey'])) {
                                define($_row['cfgKey'], $_row['cfgValue']);
                            }
                        }
                        $mail_prefix = G2G_EMAIL_SUBJECT_PREFIX;
                        $mail_to = G2G_EMAIL_TO;
                        $mail_footer = G2G_EMAIL_FOOTER;
                        $store_mail = G2G_STORE_OWNER_EMAIL_ADDRESS;
                        $store_name = G2G_STORE_NAME;
                        $store_owner = G2G_STORE_OWNER;
                        $pwlTagAdded = false;
                        $hlaTagAdded = false;
                        for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
                            switch ($order->products[$i]['custom_products_type_id']) {
                                case '0': //currency
                                case '5': //item
                                    //Email during SO created (c2c_buyback_orders.php)
                                    // $email_text = $email_greeting .
                                    //         TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT1 .
                                    //         TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT2 .
                                    //         TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK .
                                    //         TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE .
                                    //         TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK .
                                    //         "\n\n\n" . sprintf(EMAIL_CONTACT, $mail_to, $store_name) . $mail_footer;
                                    // tep_mail($order->customer['name'], $order->customer['profile_email_address'], implode(' ', array($mail_prefix, sprintf(EMAIL_TEXT_SUBJECT, (int) $this->orders_id))), $email_text, $store_owner, $store_mail);
                                    // $this->set_order_remark(0, 0, 'G2G delivery template e-mail sent', 0, 0);
                                    break;
                                case '1': //pwl
                                    //add '[PWL - PWLING UNSUBMITTED]' tag
                                    if (!$pwlTagAdded) {
                                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', " . PWLING_UNSUBMITTED_ORDER_TAG_ID . ", CONCAT_WS(',', orders_tag_ids, " . PWLING_UNSUBMITTED_ORDER_TAG_ID . ")) WHERE orders_id = " . (int) $this->orders_id . " AND NOT FIND_IN_SET(" . PWLING_UNSUBMITTED_ORDER_TAG_ID . ", orders_tag_ids)";
                                        tep_db_query($assign_orders_tag_update_sql);
                                        $pwlTagAdded = true;
                                    }
                                    break;
                                case '4': //hla
                                    //Email during SO created (c2c_buyback_orders.php)
                                    // $email_text = $email_greeting .
                                    //         TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT1 .
                                    //         TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT2 .
                                    //         TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK .
                                    //         TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE .
                                    //         TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK .
                                    //         "\n\n\n" . sprintf(EMAIL_CONTACT, $mail_to, $store_name) . $mail_footer;
                                    // tep_mail($order->customer['name'], $order->customer['profile_email_address'], implode(' ', array($mail_prefix, sprintf(EMAIL_TEXT_SUBJECT, (int) $this->orders_id))), $email_text, $store_owner, $store_mail);
                                    // $this->set_order_remark(0, 0, 'G2G delivery template e-mail sent', 0, 0);
                                    //add '[HLA - WOW Account]' tag
                                    if (!$hlaTagAdded) {
                                        $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', " . HLA_WOW_ACC_ORDER_TAG_ID . ", CONCAT_WS(',', orders_tag_ids, " . HLA_WOW_ACC_ORDER_TAG_ID . ")) WHERE orders_id = " . (int) $this->orders_id . " AND NOT FIND_IN_SET(" . HLA_WOW_ACC_ORDER_TAG_ID . ", orders_tag_ids)";
                                        tep_db_query($assign_orders_tag_update_sql);
                                        $hlaTagAdded = true;
                                    }
                                    break;
                            }
                        }
                        $cust_defined_eta_update_sql = "UPDATE " . TABLE_ORDERS_PRODUCTS . " AS opMain
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS opSub
														ON opMain.orders_products_id = opSub.parent_orders_products_id
													INNER JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
														ON (opMain.orders_products_id = opei.orders_products_id AND opei.orders_products_extra_info_key = 'char_online_time')
													SET opSub.orders_products_purchase_eta = opei.orders_products_extra_info_value
													WHERE opSub.orders_id = '" . (int) $this->orders_id . "'
														AND opSub.orders_products_purchase_eta = -999
														AND opSub.custom_products_type_id = 0";
                        tep_db_query($cust_defined_eta_update_sql);

                        if ($pwl_not_in_process) {
                            // Those without Customer provided ETA will be assumed buy now
                            $listing_order_product_sql = "	SELECT orders_products_id, parent_orders_products_id
						    							FROM " . TABLE_ORDERS_PRODUCTS . "
						    							WHERE orders_id = '" . (int) $this->orders_id . "'
						    								AND parent_orders_products_id <> 0
						    								AND custom_products_type_id = 0";
                            $listing_order_product_result = tep_db_query($listing_order_product_sql);
                            while ($listing_order_product_row = tep_db_fetch_array($listing_order_product_result)) {
                                $extra_info_array = tep_draw_products_extra_info($listing_order_product_row['parent_orders_products_id']);

                                // IF not F2F, will direct open buyback.
                                if (tep_direct_open_buyback($extra_info_array['delivery_mode'])) {
                                    $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET orders_products_purchase_eta = NULL
													WHERE orders_id = '" . (int) $this->orders_id . "'
														AND orders_products_id = '" . (int) $listing_order_product_row['orders_products_id'] . "'
													";
                                    tep_db_query($eta_update_sql);

                                    $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET orders_products_purchase_eta = NULL
													WHERE orders_id = '" . (int) $this->orders_id . "'
														AND orders_products_id = '" . (int) $listing_order_product_row['parent_orders_products_id'] . "'
													";
                                    tep_db_query($eta_update_sql);

                                    $vipOrderObj = new vip_order($listing_order_product_row['orders_products_id']);
                                    $vipOrderObj->get_orders_details();
                                    if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
                                        $vipOrderObj->get_available_supplier();
                                        $assign_order_item = array();
                                        $assign_order_item[] = "&raquo; " . $vipOrderObj->order_detail['products_name'];

                                        if (count($vipOrderObj->assigned_supplier)) {
                                            $assign_order_str = "The following items have been assign to supplier:" . "\n" . implode("\n", $assign_order_item) . "\n" . implode("\n", $vipOrderObj->assigned_supplier);
                                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $vipOrderObj->order_detail['orders_id'] . "', 0, now(), '0', '" . tep_db_input($assign_order_str) . "', 1, '" . $login_email_address . "')");
                                        }
                                    }
                                } else {
                                    $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET orders_products_purchase_eta = '-999'
													WHERE orders_id = '" . (int) $this->orders_id . "'
														AND orders_products_id = '" . (int) $listing_order_product_row['orders_products_id'] . "'
													";
                                    tep_db_query($eta_update_sql);

                                    $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET orders_products_purchase_eta = '-999'
													WHERE orders_id = '" . (int) $this->orders_id . "'
														AND orders_products_id = '" . (int) $listing_order_product_row['parent_orders_products_id'] . "'
													";
                                    tep_db_query($eta_update_sql);
                                }
                            }
                        } else {
                            $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
											SET orders_products_purchase_eta = -999
											WHERE orders_id = '" . (int) $this->orders_id . "'
												AND custom_products_type_id = 0";
                            tep_db_query($eta_update_sql);
                        }
                    }
                } else if ($curr_status == 2 && $orders_status == 7) { // Update from Processing to Verifying
                    $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
										SET orders_products_purchase_eta = -999
										WHERE orders_id = '" . (int) $this->orders_id . "'
											AND custom_products_type_id = 0";
                    tep_db_query($eta_update_sql);
                } else if ($curr_status == 2 && $orders_status == 3) {
                    $date_purchased_select_sql = "	SELECT date_purchased, orders_rebated
													FROM " . TABLE_ORDERS . "
													WHERE orders_id = '" . (int) $this->orders_id . "'";
                    $date_purchased_result_sql = tep_db_query($date_purchased_select_sql);
                    $date_purchased_row = tep_db_fetch_array($date_purchased_result_sql);

                    if ($date_purchased_row['orders_rebated'] == 0) {
                        $cron_pending_credit_mature_period = OP_CREDIT_DURATION; // minutes

                        tep_insert_cron_pending_credit('OP', $this->orders_id, $date_purchased_row['date_purchased'], $cron_pending_credit_mature_period, 3);
                    }
                } else if ($curr_status == 7 && $orders_status == 5) {      // Verifying to Cancel for Credit Card Payment method
                    $this->send_order_notification(EN_EMAIL_TEXT_GENESIS_ORDER_CANCEL);
                    $this->set_order_remark(0, 1, EN_EMAIL_TEXT_GENESIS_ORDER_CANCEL, 0, 0);
                }
                if ($proceed) {
                    tep_status_update_notification('C', $this->orders_id, $this->changed_by, $curr_status, $orders_status, ($this->changed_by == 'system') ? 'A' : 'M');

                    $this->order_status_update = true;
                    $orders_update_sql_data = array('orders_aft_executed' => 0,
                        'orders_status' => tep_db_input($orders_status),
                        'last_modified' => 'now()'
                    );
                    tep_db_perform(TABLE_ORDERS, $orders_update_sql_data, 'update', "orders_id = '" . (int) $this->orders_id . "'");
                    if ($curr_status == 7 && $orders_status == 2) {      // Verifying to Processing
                        $pipwave = new pipwave($this->orders_id);
                        $acceptOrder = $pipwave->acceptOrder();
                    }
                }
            }
            $this->execute_log .= $execute_log;
        }
    }

    function set_order_remark($orders_status_id = 0, $customer_notified = 0, $comments, $comments_type = 0, $set_as_order_remarks = 0) {
        if ($this->aft_mode == 2) {
            $comments = 'GENESIS PROJECT: ' . $this->aft_name . "\nVersion: " . $this->aft_version . " - LIVE\n\n " . $comments;
        } else if ($this->aft_mode == 1) {
            $comments = 'GENESIS PROJECT: ' . $this->aft_name . "\nVersion: " . $this->aft_version . " - DEMO\n\n " . $comments;
        }

        $sql_data_array = array('orders_id' => $this->orders_id,
            'orders_status_id' => $orders_status_id,
            'date_added' => 'now()',
            'customer_notified' => $customer_notified,
            'comments' => $comments,
            'comments_type' => $comments_type,
            'set_as_order_remarks' => $set_as_order_remarks,
            'changed_by' => $this->changed_by);

        tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
        tep_update_orders_status_counter($sql_data_array);
    }

    function send_order_notification($extra_comment = '') {
        if ($this->aft_mode == 2) {
            $customers_info_select_sql = "  SELECT customers_id, date_purchased, orders_status, customers_name, customers_email_address
                                            FROM " . TABLE_ORDERS . "
                                            WHERE orders_id='" . tep_db_input($this->orders_id) . "'";
            $customers_info_result_sql = tep_db_query($customers_info_select_sql);

            if ($customers_info_row = tep_db_fetch_array($customers_info_result_sql)) {
                $cur_status = $customers_info_row['orders_status'];

                $customer_profile_select_sql = "SELECT customers_firstname, customers_lastname, customers_email_address
                                                FROM " . TABLE_CUSTOMERS . "
                                                WHERE customers_id = '" . tep_db_input($customers_info_row['customers_id']) . "'";
                $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);

                if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
                    $email_firstname = $customer_profile_row['customers_firstname'];
                    $email_lastname = $customer_profile_row['customers_lastname'];
                    $email_address = $customer_profile_row['customers_email_address'];
                } else {
                    $email_firstname = $customers_info_row['customers_name'];
                    $email_lastname = $customers_info_row['customers_name'];
                    $email_address = $customers_info_row['customers_email_address'];
                }

                $siteId = c2c_order::orderSiteID($this->orders_id);

                if ($siteId != 5) {
                    $emailQueue = new ms_hub_email();
                    // Data array to send to SQS
                    $emailDataSqs = array(
                        'filetype' => 'order_email',
                        'email_template' => 'order-genesis-cancel',
                        'email_subject_prefix' => EMAIL_SUBJECT_PREFIX,
                        'store_owner' => "<EMAIL>",
                        'store_owner_email_address' => "<EMAIL>",
                        'customer' => array(
                            'id' => $customers_info_row['customers_id'],
                            'firstname' => $email_firstname,
                            'language' => tep_get_customer_language($customers_info_row['customers_id']),
                            'email' => $email_address,
                        ),
                        'orders' => array(
                            'orders_id' => (int) $this->orders_id,
                            'date_purchased' => tep_date_long($customers_info_row['date_purchased']),
                            'orders_products' => '',
                            'comments' => ''
                        ),
                    );
                    $emailQueue->setSqsEmail($emailDataSqs);
                    unset($emailDataSqs);
                } else {
                    $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname);

                    $email = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . (int) $this->orders_id . "\n"
                            . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($customers_info_row['date_purchased']) . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this->orders_id, 'SSL') . "\n\n" .
                            $extra_comment . "\n\n";
                    tep_mail($email_firstname, $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (int) $this->orders_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
            }
        }
    }

    function get_order_profile_address_country_iso() {
        $customers_country_select_sql = "	SELECT c.countries_iso_code_2
											FROM " . TABLE_ORDERS . " AS o
											INNER JOIN " . TABLE_COUNTRIES . " AS c
												ON (c.countries_name = o.customers_country)
											WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $customers_country_result_sql = tep_db_query($customers_country_select_sql);
        $customers_country_row = tep_db_fetch_array($customers_country_result_sql);

        $this->execute_log .= 'get_order_profile_country: ' . $customers_country_row['countries_iso_code_2'] . "\n";

        return $customers_country_row['countries_iso_code_2'];
    }

    function get_order_billing_address_country_iso() {
        $billing_country_select_sql = "	SELECT c.countries_iso_code_2
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_COUNTRIES . " AS c
											ON (c.countries_name = o.billing_country)
										WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $billing_country_result_sql = tep_db_query($billing_country_select_sql);
        $billing_country_row = tep_db_fetch_array($billing_country_result_sql);

        $this->execute_log .= 'get_order_billing_address_country: ' . $billing_country_row['countries_iso_code_2'] . "\n";

        return $billing_country_row['countries_iso_code_2'];
    }

    function get_order_telephone_country_iso() {
        $tel_country_select_sql = "	SELECT c.countries_iso_code_2
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_COUNTRIES . " AS c
                                        ON (o.customers_telephone_country=c.countries_name)
                                    WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $tel_country_result_sql = tep_db_query($tel_country_select_sql);
        $tel_country_row = tep_db_fetch_array($tel_country_result_sql);

        $this->execute_log .= 'get_order_telephone_country: ' . $tel_country_row['countries_iso_code_2'] . "\n";

        return $tel_country_row['countries_iso_code_2'];
    }

    function deliver_products($line = '') {
        $fully_deliver = true;

        if (tep_not_null($line)) {
            $this->execute_log .= "deliver_products()\nLine #" . $line . ', ' . date('Y-m-d H:i:s') . ', ' . TEXT_MEMORY_USAGE . ': ' . memory_get_usage() . "\n";
        }

        $orders_locked_by_select_sql = "	SELECT orders_locked_by, orders_status
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . (int) $this->orders_id . "'";
        $orders_locked_by_result_sql = tep_db_query($orders_locked_by_select_sql);
        $orders_locked_by_row = tep_db_fetch_array($orders_locked_by_result_sql);

        if (tep_not_null($orders_locked_by_row['orders_locked_by'])) {
            if ($orders_locked_by_row['orders_locked_by'] != 0) {
                tep_customer_order_locking($this->orders_id, 0, 'unlock', 0);
            }
        } else {
            tep_customer_order_locking($this->orders_id);
        }

        if ($this->aft_mode == 2) {
            $siteId = c2c_order::orderSiteID($this->orders_id);
            $ordersEmailDetails = array();

            $_c2c_config_sel = "SELECT configuration_value FROM " . TABLE_C2C_CONFIGURATION . " WHERE configuration_key = 'G2G_STORE_OWNER_EMAIL_ADDRESS'";
            $_c2c_config_res = tep_db_query($_c2c_config_sel);
            $_c2c_config_row = tep_db_fetch_array($_c2c_config_res);

            $orders_products_select_sql = "	SELECT orders_products_id, custom_products_type_id, products_id
                                            FROM " . TABLE_ORDERS_PRODUCTS . "
                                            WHERE orders_id = '" . (int) $this->orders_id . "'
                                                AND parent_orders_products_id < 1
                                                AND orders_products_is_compensate = 0";
            $orders_products_query = tep_db_query($orders_products_select_sql);
            while ($orders_products = tep_db_fetch_array($orders_products_query)) {
                if (($siteId == 5) && ($orders_products['custom_products_type_id'] != 3)) {
                    $so_res = c2c_buyback_order::_create_buyback_order($orders_products['orders_products_id']);
                    $fully_deliver = false;

                    if (isset($so_res['errno']) && $so_res['errno'] > 0) {
                        $subject = '[G2G] Auto Create Sell Order';
                        $message = "Fail to auto create Sell Order for the following Customer Order : \n\n" . $this->orders_id . ' : ' . $so_res['error'];
                        @tep_mail($_c2c_config_row['configuration_value'], $_c2c_config_row['configuration_value'], $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }
                } else {
                    switch ($orders_products['custom_products_type_id']) {
                        case 2: // CD Key
                            $this->deliver_cdkey_automation($orders_products['orders_products_id']);
                            break;
                        case 3: // SC
                            $this->delivery_product_automation($orders_products['orders_products_id']);
                            break;
                        case 4: // Physical Goods
                            $this->deliver_cdkey_automation($orders_products['orders_products_id']);
                            break;
                    }

                    if ($this->is_product_fully_delivered($orders_products['orders_products_id']) == false) {
                        $fully_deliver = false;
                    }

                    $ordersEmailDetails = $this->delivery_details;
                    $this->delivery_details = array();
                }
            }
        } else {
            $fully_deliver = false;
        }

        tep_customer_order_locking($this->orders_id, 0, 'unlock', 0);

        if ($fully_deliver) {
            $this->set_order_status(3);
        }

        if ($this->cdkey_delivered == true) {
            $order_information_select_sql = "	SELECT customers_id, customers_name, orders_status, date_purchased
												FROM " . TABLE_ORDERS . "
												WHERE orders_id = '" . (int) $this->orders_id . "'";
            $order_information_result_sql = tep_db_query($order_information_select_sql);
            if ($order_information_row = tep_db_fetch_array($order_information_result_sql)) {
                $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address
												FROM " . TABLE_CUSTOMERS . "
												WHERE customers_id = '" . (int) $order_information_row['customers_id'] . "'";
                $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
                if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
                    $this->delivery_remark = 'The following items have been delivered:' . "\n" . $this->delivery_remark;
                    $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row['customers_gender']);

                    $emailQueue = new ms_hub_email();
                    
                    $custLang = tep_get_customer_language($order_information_row['customers_id']);
                    if ($siteId != 5) { // OGM Usage
                        // Data array to send to SQS
                        $emailDataSqs = array(
                            'filetype' => 'order_email',
                            'email_template' => (($fully_deliver == true) ? 'order-full-deliver' : 'order-partial-deliver'),
                            'email_subject_prefix' => EMAIL_SUBJECT_PREFIX,
                            'store_owner' => "<EMAIL>",
                            'store_owner_email_address' => "<EMAIL>",
                            'customer' => array(
                                'id' => $order_information_row['customers_id'],
                                'firstname' => $customer_profile_row['customers_firstname'],
                                'language' => $custLang,
                                'email' => $customer_profile_row['customers_email_address'],
                            ),
                            'orders' => array(
                                'orders_id' => (int) $this->orders_id,
                                'date_purchased' => tep_date_long($order_information_row['date_purchased']),
                                'orders_products' => $ordersEmailDetails,
                                'comments' => ''
                            ),
                        );
                        $emailQueue->setSqsEmail($emailDataSqs);
                        unset($emailDataSqs);
                    } else {
                        $email_firstname = $customer_profile_row['customers_firstname'];
                        $email_lastname = $customer_profile_row['customers_lastname'];

                        $delivery_email = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . (int) $this->orders_id . "\n"
                                . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($order_information_row['date_purchased']) . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this->orders_id, 'SSL') . "\n\n";
                        $delivery_email .= sprintf(EMAIL_TEXT_UPDATED_STATUS, $this->orders_status_array[$order_information_row['orders_status']]) .
                                EMAIL_TEXT_PARTIAL_DELIVERY .
                                str_replace("\t", '&nbsp;&nbsp;&nbsp;&nbsp;', $this->delivery_remark) . "\n\n" . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;

                        tep_mail($order_information_row['customers_name'], $customer_profile_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (int) $this->orders_id))), $delivery_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                        unset($delivery_email);
                    }

                    tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $this->orders_id . "', 0, now(), 1, '" . tep_db_input($this->delivery_remark) . "', 2, '" . $this->changed_by . "')");

                    // Send template
                    $orders_comments_text_select_sql = "	SELECT orders_comments_text
		      												FROM " . TABLE_ORDERS_COMMENTS . "
		      												WHERE orders_comments_id = 41";
                    $orders_comments_text_result_sql = tep_db_query($orders_comments_text_select_sql);
                    if ($orders_comments_text_row = tep_db_fetch_array($orders_comments_text_result_sql)) {
                        if ($siteId != 5) { // OGM Usage
                            // Data array to send to SQS
                            $emailDataSqs = array(
                                'filetype' => 'order_email',
                                'email_template' => 'order-comment',
                                'email_subject_prefix' => EMAIL_SUBJECT_PREFIX,
                                'store_owner' => "<EMAIL>",
                                'store_owner_email_address' => "<EMAIL>",
                                'customer' => array(
                                    'id' => $order_information_row['customers_id'],
                                    'firstname' => $customer_profile_row['customers_firstname'],
                                    'language' => $custLang,
                                    'email' => $customer_profile_row['customers_email_address'],
                                ),
                                'orders' => array(
                                    'orders_id' => (int) $this->orders_id,
                                    'date_purchased' => tep_date_long($order_information_row['date_purchased']),
                                    'orders_products' => '',
                                    'comments' => $orders_comments_text_row['orders_comments_text']
                                ),
                            );
                            $emailQueue->setSqsEmail($emailDataSqs);
                            unset($emailDataSqs);
                        } else {
                            $email = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . (int) $this->orders_id . "\n" . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($order_information_row['date_purchased']) . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this->orders_id, 'SSL') . "\n\n";

                            $email .= sprintf(EMAIL_TEXT_UPDATED_STATUS, (($fully_deliver == true) ? $this->orders_status_array[$orders_locked_by_row['orders_status']] . ' -> ' . $this->orders_status_array[3] : $this->orders_status_array[$orders_locked_by_row['orders_status']])) .
                                    $orders_comments_text_row['orders_comments_text'] . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;

                            tep_mail($order_information_row['customers_name'], $customer_profile_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (int) $this->orders_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                            unset($email);
                        }

                        tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $this->orders_id . "', 0, now(), 1, '" . tep_db_input($orders_comments_text_row['orders_comments_text']) . "', 2, '" . $this->changed_by . "')");
                    }
                }
            }
        }
    }

    function get_order_item_total_values($orders_products_id) {
        $total = 0;
        if (!tep_not_null($orders_products_id)) {
            return $total;
        }

        $orders_products_info_select_sql = "	SELECT orders_products_id, orders_products_store_price, products_quantity
												FROM " . TABLE_ORDERS_PRODUCTS . "
												WHERE orders_id = '" . (int) $this->orders_id . "'
													AND parent_orders_products_id = '" . (int) $orders_products_id . "'
													AND orders_products_is_compensate = 0";
        $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
        while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
            $total = $total + ($orders_products_info_row['products_quantity'] * $orders_products_info_row['orders_products_store_price']);
        }

        return tep_round($total, 6);
    }

    function deliver_cdkey_automation($orders_products_id) {
        $currency_select_sql = "	SELECT currency
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
        $currency_result_sql = tep_db_query($currency_select_sql);
        $currency_row = tep_db_fetch_array($currency_result_sql);

        $orders_products_select_sql = "	SELECT products_id, products_quantity, final_price, products_model
										FROM " . TABLE_ORDERS_PRODUCTS . "
										WHERE orders_products_id = '" . (int) $orders_products_id . "'";
        $orders_products_result_sql = tep_db_query($orders_products_select_sql);
        $orders_products_row = tep_db_fetch_array($orders_products_result_sql);

        if ($currency_row['currency'] == DEFAULT_CURRENCY) {
            $decimal_places_select_sql = "	SELECT decimal_places
											FROM " . TABLE_CURRENCIES . "
											WHERE code = '" . tep_db_input($currency_row['currency']) . "'";
            $decimal_places_result_sql = tep_db_query($decimal_places_select_sql);
            $decimal_places_row = tep_db_fetch_array($decimal_places_result_sql);

            $final_price = tep_round($orders_products_row['final_price'], $decimal_places_row['decimal_places']);
        } else {
            $final_price = $orders_products_row['final_price'];
        }

        $log_message_to_use = sprintf(LOG_PARTIAL_DELIVERY_SALES, $this->orders_id);
        $partial_deliver_str = '';

        $stock_select_sql = "	SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_skip_inventory, products_cat_path, custom_products_type_id, products_type
								FROM " . TABLE_PRODUCTS . "
								WHERE products_id = '" . (int) $orders_products_row['products_id'] . "'";
        $stock_result_sql = tep_db_query($stock_select_sql);
        if ($stock_row = tep_db_fetch_array($stock_result_sql)) {
            if ($stock_row['products_bundle_dynamic'] == 'yes') { // dynamic bundle
;
            } else if ($stock_row['products_bundle'] == 'yes') { // static bundle
                $recalculate_delivered_price = false;
                $static_deliver_item = array();
                $total_package_values = $this->get_order_item_total_values($orders_products_id);

                $orders_products_info_select_sql = "	SELECT orders_products_id, products_delivered_quantity, products_quantity, products_id, products_name, orders_products_store_price, products_model
														FROM " . TABLE_ORDERS_PRODUCTS . "
														WHERE orders_id = '" . (int) $this->orders_id . "'
															AND parent_orders_products_id = '" . (int) $orders_products_id . "'
															AND orders_products_is_compensate = 0";
                $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
                $total_products_bundle = tep_db_num_rows($orders_products_info_result_sql);

                if ($total_products_bundle > 0) {
                    while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
                        if (in_array($orders_products_info_row['products_id'], $this->products_not_to_deliver_array))
                            continue;

                        if ($api = auto_restock::isSupportedSKU($orders_products_info_row['products_model'])) {
                            $this->delivery_product_automation($orders_products_info_row['orders_products_id'], $api);
                            continue;
                        }

                        $qty_updated = false;
                        $pending_deliver_qty = $orders_products_info_row['products_quantity'];

                        if ($orders_products_info_row['products_delivered_quantity'] > 0) {
                            $pending_deliver_qty = $orders_products_info_row['products_quantity'] - $orders_products_info_row['products_delivered_quantity'];
                        }

                        if ($pending_deliver_qty > 0) {
                            $subproduct_stock_select_sql = "	SELECT p.products_quantity, p.products_actual_quantity, p.products_skip_inventory, p.products_cat_path, p.custom_products_type_id
																FROM " . TABLE_PRODUCTS . " AS p
																WHERE products_id = '" . (int) $orders_products_info_row['products_id'] . "'";
                            $subproduct_stock_result_sql = tep_db_query($subproduct_stock_select_sql);
                            if ($subproduct_stock_row = tep_db_fetch_array($subproduct_stock_result_sql)) {
                                if ($subproduct_stock_row['custom_products_type_id'] == 2) {
                                    $available_deliver_qty = $pending_deliver_qty;

                                    if ($subproduct_stock_row['products_actual_quantity'] < $pending_deliver_qty) {
                                        $available_deliver_qty = $subproduct_stock_row['products_actual_quantity'];
                                    }

                                    $orders_products_extra_info_sql = "	SELECT opei.orders_products_id
																		FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
																		WHERE opei.orders_products_id = '" . (int) $orders_products_info_row['orders_products_id'] . "'
																			AND opei.orders_products_extra_info_key = 'delivery_mode'
																			AND opei.orders_products_extra_info_value = '6'";
                                    $orders_products_extra_info_result = tep_db_query($orders_products_extra_info_sql);

                                    if (tep_db_num_rows($orders_products_extra_info_result)) {
                                        if ($pending_deliver_qty > 0) { // DTU does not need to refer actual quantity
                                            $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
																		SET products_delivered_quantity = products_delivered_quantity + " . $orders_products_info_row['products_quantity'] . ",
																			products_good_delivered_quantity = products_good_delivered_quantity + " . $orders_products_info_row['products_quantity'] . "
																		WHERE orders_id = '" . (int) $this->orders_id . "'
																			AND products_id = '" . (int) $orders_products_info_row['products_id'] . "'
																			AND orders_products_id = '" . (int) $orders_products_info_row['orders_products_id'] . "'
																			AND orders_products_is_compensate = 0
																			AND products_bundle_id = '" . (int) $orders_products_row['products_id'] . "'";
                                            tep_db_query($delived_qty_update_sql);

                                            $static_deliver_item[] = '&raquo; ' . $orders_products_info_row['products_name'] . "\tx " . $orders_products_info_row['products_quantity'];
                                            $this->delivery_details[$orders_products_info_row['orders_products_id']] = array(
                                                'products_name' => $orders_products_info_row['products_name'],
                                                'products_quantity' => $orders_products_info_row['products_quantity'],
                                                'products_delivery_status' => true
                                            );
                                            $recalculate_delivered_price = true;
                                            $total_item_amount = $orders_products_info_row['orders_products_store_price'] * $orders_products_info_row["products_quantity"];

                                            // Sales activity log
                                            $this->insert_sales_activity($orders_products_id, $orders_products_info_row['products_id'], 'D', tep_round((($total_item_amount / $total_package_values) * ($final_price * $orders_products_row['products_quantity'])), 4), $orders_products_info_row['products_quantity'], $orders_products_info_row['products_quantity']);
                                            $this->cdkey_delivered = true;

                                            $qty_updated = true;

                                            $orders_top_up_select_sql = "	SELECT orders_products_id
																			FROM " . TABLE_ORDERS_TOP_UP . "
																			WHERE orders_products_id = '" . (int) $orders_products_info_row['orders_products_id'] . "'";
                                            $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
                                            if (!tep_db_num_rows($orders_top_up_result_sql)) {
                                                include_once(DIR_WS_CLASSES . 'direct_topup.php');
                                                $direct_topup_obj = new direct_topup();

                                                $products_cost_select_sql = "SELECT products_cost, products_currency
                                                                                        FROM " . TABLE_PRODUCTS_COST . "
                                                                                        WHERE products_id = '" . $orders_products_info_row['products_id'] . "'";
                                                $products_cost_result_sql = tep_db_query($products_cost_select_sql);
                                                if ($products_cost_row = tep_db_fetch_array($products_cost_result_sql)) {
                                                    $product_cost = $products_cost_row['products_cost'];
                                                    $products_currency = $products_cost_row['products_currency'];
                                                }

                                                $orders_top_up_data_sql = array('orders_products_id' => (int) $orders_products_info_row['orders_products_id'],
                                                    'top_up_status' => '1',
                                                    'top_up_process_flag' => '0',
                                                    'currency_settle_amount' => isset($product_cost) ? $product_cost : 0,
                                                    'currency_code' => isset($products_currency) ? $products_currency : '',
                                                    'publishers_id' => (int) $direct_topup_obj->get_product_publisher($orders_products_info_row['products_id']),
                                                    'top_up_created_date' => 'now()'
                                                );
                                                tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql);

                                                $last_top_up_id = tep_db_insert_id();

                                                $orders_top_up_data_sql = array('top_up_id' => (int) $last_top_up_id,
                                                    'data_added' => 'now()',
                                                    'remark' => 'Added to queue');
                                                tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_data_sql);

                                                $orders_top_up_data_sql = array(
                                                    'top_up_id' => (int) $last_top_up_id,
                                                    'orders_products_id' => (int) $orders_products_info_row['orders_products_id'],
                                                    'publishers_id' => (int) $direct_topup_obj->get_product_publisher($orders_products_info_row['products_id']));
                                                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $orders_top_up_data_sql);

                                                // Set DTU for GP Report log (log_delivered_products)
                                                include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                                                $ogm_report_tool_obj = new ogm_report_tool();
                                                $ogm_report_tool_obj->log_top_up_reloaded($last_top_up_id, $this->orders_id, $orders_products_info_row['orders_products_id'], $orders_products_info_row['products_id'], $final_price, $orders_products_info_row['products_quantity']);
                                                unset($ogm_report_tool_obj);
                                            }
                                        }
                                    } else {
                                        if ($available_deliver_qty > 0) {
                                            $cdkey_id_arr = deliver_cdkey($orders_products_info_row['products_id'], $orders_products_info_row['orders_products_id'], $available_deliver_qty, 'deliver', 0, 0, false);
                                            if ($cdkey_id_arr) {
                                                $total_cdkey_id = sizeof($cdkey_id_arr);
                                                $log_user_msg = sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr));

                                                if (!$subproduct_stock_row['products_skip_inventory']) {
                                                    $update_qty_array = array(array('field_name' => 'products_actual_quantity',
                                                            'operator' => '-',
                                                            'value' => $total_cdkey_id)
                                                    );
                                                    // This function will handle the qty adjustment and keep the log if asking so
                                                    tep_set_product_qty($orders_products_info_row['products_id'], $update_qty_array, true, $log_message_to_use, $log_user_msg);
                                                }

                                                $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
																			SET products_delivered_quantity = products_delivered_quantity + " . $total_cdkey_id . ",
																				products_good_delivered_quantity = products_good_delivered_quantity + " . $total_cdkey_id . "
																			WHERE orders_id = '" . (int) $this->orders_id . "'
																				AND products_id = '" . (int) $orders_products_info_row['products_id'] . "'
																				AND orders_products_id = '" . (int) $orders_products_info_row['orders_products_id'] . "'
																				AND orders_products_is_compensate = 0
																				AND products_bundle_id = '" . (int) $orders_products_row['products_id'] . "'";
                                                tep_db_query($delived_qty_update_sql);

                                                $static_deliver_item[] = '&raquo; ' . $orders_products_info_row['products_name'] . "\tx " . $total_cdkey_id;
                                                $this->delivery_details[$orders_products_info_row['orders_products_id']] = array(
                                                    'products_name' => $orders_products_info_row['products_name'],
                                                    'products_quantity' => $total_cdkey_id,
                                                    'products_delivery_status' => true
                                                );
                                                $recalculate_delivered_price = true;
                                                $total_item_amount = $orders_products_info_row['orders_products_store_price'] * $orders_products_info_row["products_quantity"];
                                                $total_item_final_amount = ($total_item_amount / $total_package_values) * ($final_price * $orders_products_row['products_quantity']);

                                                // Sales activity log
                                                //$this->insert_sales_activity($orders_products_id, tep_get_prid($orders_products_row['products_id']), 'D', (($final_price * $orders_products_row['products_quantity']) / $total_products_bundle), $orders_products_info_row['products_quantity'], $total_cdkey_id);
                                                $this->insert_sales_activity($orders_products_id, $orders_products_info_row['products_id'], 'D', tep_round($total_item_final_amount, 4), $orders_products_info_row['products_quantity'], $total_cdkey_id);
                                                $this->cdkey_delivered = true;

                                                $qty_updated = true;

                                                include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                                                $ogm_report_tool_obj = new ogm_report_tool();
                                                $ogm_report_tool_obj->log_cdkey_delivered($cdkey_id_arr, $this->orders_id, $orders_products_info_row['orders_products_id'], $orders_products_info_row['products_id'], $total_item_final_amount, $orders_products_info_row["products_quantity"]);
                                                $ogm_report_tool_obj->log_cdkey_delivered_released($this->orders_id, $orders_products_info_row['products_id'], $orders_products_info_row['orders_products_id'], $total_item_final_amount, $orders_products_info_row["products_quantity"]);
                                                unset($ogm_report_tool_obj);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (count($static_deliver_item))
                    $partial_deliver_str .= implode("\n", $static_deliver_item) . "\n";
                if ($recalculate_delivered_price)
                    $this->update_delivered_price($orders_products_id, 'products_bundle');
            } else {
                if ($api = auto_restock::isSupportedSKU($orders_products_row['products_model'])) {
                    $this->delivery_product_automation($orders_products_id, $api);
                } else {
                    $recalculate_delivered_price = false;

                    $orders_products_info_select_sql = "	SELECT products_delivered_quantity, final_price, products_quantity, products_id, products_name
                                                            FROM " . TABLE_ORDERS_PRODUCTS . "
                                                            WHERE orders_products_id = '" . (int) $orders_products_id . "'";
                    $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
                    if ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
                        if ($stock_row['custom_products_type_id'] == 2 && !in_array($orders_products_info_row['products_id'], $this->products_not_to_deliver_array)) {
                            $pending_deliver_qty = $orders_products_info_row['products_quantity'];

                            if ($orders_products_info_row['products_delivered_quantity'] > 0) {
                                $pending_deliver_qty = $orders_products_info_row['products_quantity'] - $orders_products_info_row['products_delivered_quantity'];
                            }

                            $available_deliver_qty = $pending_deliver_qty;

                            if ($stock_row['products_actual_quantity'] < $pending_deliver_qty) {
                                $available_deliver_qty = $stock_row['products_actual_quantity'];
                            }

                            $orders_products_extra_info_sql = "	SELECT opei.orders_products_id
                                                                FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
                                                                WHERE opei.orders_products_id = '" . (int) $orders_products_id . "'
                                                                    AND opei.orders_products_extra_info_key = 'delivery_mode'
                                                                    AND opei.orders_products_extra_info_value = '6'";
                            $orders_products_extra_info_result = tep_db_query($orders_products_extra_info_sql);
                            if (tep_db_num_rows($orders_products_extra_info_result)) {
                                if ($pending_deliver_qty > 0) {
                                    $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
                                                                SET products_delivered_quantity = products_delivered_quantity + " . $orders_products_row['products_quantity'] . ",
                                                                    products_good_delivered_quantity = products_good_delivered_quantity + " . $orders_products_row['products_quantity'] . "
                                                                WHERE orders_id = '" . (int) $this->orders_id . "'
                                                                    AND products_id = '" . (int) $orders_products_info_row['products_id'] . "'
                                                                    AND orders_products_id = '" . (int) $orders_products_id . "'
                                                                    AND orders_products_is_compensate = 0
                                                                    AND products_bundle_id = 0";
                                    tep_db_query($delived_qty_update_sql);

                                    $partial_deliver_str .= '&raquo; ' . $orders_products_info_row['products_name'] . "\tx " . $orders_products_row['products_quantity'] . "\n";
                                    $this->delivery_details[$orders_products_id] = array(
                                        'products_name' => $orders_products_info_row['products_name'],
                                        'products_quantity' => $orders_products_row['products_quantity'],
                                        'products_delivery_status' => true
                                    );
                                    $recalculate_delivered_price = true;

                                    // Sales activity log
                                    $this->insert_sales_activity($orders_products_id, tep_get_prid($orders_products_row['products_id']), 'D', $final_price * $orders_products_row['products_quantity'], $orders_products_row['products_quantity'], $orders_products_row['products_quantity']);
                                    $this->cdkey_delivered = true;

                                    $orders_top_up_select_sql = "	SELECT orders_products_id
                                                                    FROM " . TABLE_ORDERS_TOP_UP . "
                                                                    WHERE orders_products_id = '" . (int) $orders_products_id . "'";
                                    $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
                                    if (!tep_db_num_rows($orders_top_up_result_sql)) {
                                        include_once(DIR_WS_CLASSES . 'direct_topup.php');
                                        $direct_topup_obj = new direct_topup();

                                        $products_cost_select_sql = "	SELECT products_cost, products_currency
                                                    FROM " . TABLE_PRODUCTS_COST . "
                                                    WHERE products_id = '" . $orders_products_info_row['products_id'] . "'";
                                        $products_cost_result_sql = tep_db_query($products_cost_select_sql);
                                        if ($products_cost_row = tep_db_fetch_array($products_cost_result_sql)) {
                                            $product_cost = $products_cost_row['products_cost'];
                                            $products_currency = $products_cost_row['products_currency'];
                                        }

                                        $orders_top_up_data_sql = array(
                                            'orders_products_id' => (int) $orders_products_id,
                                            'top_up_status' => '1',
                                            'top_up_process_flag' => '0',
                                            'currency_settle_amount' => isset($product_cost) ? $product_cost : 0,
                                            'currency_code' => isset($products_currency) ? $products_currency : '',
                                            'publishers_id' => (int) $direct_topup_obj->get_product_publisher($orders_products_info_row['products_id']),
                                            'top_up_created_date' => 'now()'
                                        );
                                        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql);

                                        $last_top_up_id = tep_db_insert_id();

                                        $orders_top_up_data_sql = array(
                                            'top_up_id' => (int) $last_top_up_id,
                                            'data_added' => 'now()',
                                            'remark' => 'Added to queue');
                                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_data_sql);

                                        $orders_top_up_data_sql = array(
                                            'top_up_id' => (int) $last_top_up_id,
                                            'orders_products_id' => (int) $orders_products_id,
                                            'publishers_id' => (int) $direct_topup_obj->get_product_publisher($orders_products_info_row['products_id']));
                                        tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $orders_top_up_data_sql);

                                        // Set DTU for GP Report log (log_delivered_products)
                                        include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                                        $ogm_report_tool_obj = new ogm_report_tool();
                                        $ogm_report_tool_obj->log_top_up_reloaded($last_top_up_id, $this->orders_id, $orders_products_id, $orders_products_info_row['products_id'], $final_price, $orders_products_row['products_quantity']);
                                        unset($ogm_report_tool_obj);
                                    }

                                    $qty_updated = true;
                                }
                            }
                            else if($stock_row['products_type'] == 4){
                                if ($pending_deliver_qty > 0) {
                                    $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
                                                                SET products_delivered_quantity = products_delivered_quantity + " . $orders_products_row['products_quantity'] . ",
                                                                    products_good_delivered_quantity = products_good_delivered_quantity + " . $orders_products_row['products_quantity'] . "
                                                                WHERE orders_id = '" . (int)$this->orders_id . "'
                                                                    AND products_id = '" . (int)$orders_products_info_row['products_id'] . "'
                                                                    AND orders_products_id = '" . (int)$orders_products_id . "'
                                                                    AND orders_products_is_compensate = 0
                                                                    AND products_bundle_id = 0";
                                    tep_db_query($delived_qty_update_sql);

                                    $partial_deliver_str .= '&raquo; ' . $orders_products_info_row['products_name'] . "\tx " . $orders_products_row['products_quantity'] . "\n";
                                    $this->delivery_details[$orders_products_id] = array(
                                        'products_name' => $orders_products_info_row['products_name'],
                                        'products_quantity' => $orders_products_row['products_quantity'],
                                        'products_delivery_status' => true
                                    );
                                    $recalculate_delivered_price = true;

                                    // Sales activity log
                                    $this->insert_sales_activity($orders_products_id, tep_get_prid($orders_products_row['products_id']), 'D', $final_price * $orders_products_row['products_quantity'],
                                        $orders_products_row['products_quantity'], $orders_products_row['products_quantity']);
                                    $this->cdkey_delivered = true;
                                    $qty_updated = true;

                                    // Set GP Report log (log_delivered_products)
                                    include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                                    $ogm_report_tool_obj = new ogm_report_tool();
                                    $ogm_report_tool_obj->log_physical_goods_delivered($orders_products_id, $this->orders_id, $orders_products_id, $orders_products_info_row['products_id'], $final_price, $orders_products_row['products_quantity']);
                                    unset($ogm_report_tool_obj);

                                    $email_title = "Physical Goods Order Update : " . $this->orders_id;

                                    $db_og = tep_db_connect(DB_OG_SERVER, DB_OG_SERVER_USERNAME, DB_OG_SERVER_PASSWORD, DB_OG_DATABASE, 'db_og');
                                    mysql_set_charset('utf8mb4', $db_og);
                                    $orders_delivery_select_sql = "SELECT recipient_name, contact_number, addr_1, addr_2, city, country_name, state, postcode
                                                                                        FROM " . TABLE_ORDERS_DELIVERY_ADDRESS . "
                                                                                        WHERE orders_products_id = '" . (int) $orders_products_id . "'
                                                                                                                                        ";
                                    $delivery_address_query = tep_db_query($orders_delivery_select_sql, 'db_og');

                                    if(tep_db_num_rows($delivery_address_query) > 0) {
                                        while ($delivery_address_details = tep_db_fetch_array($delivery_address_query)) {
                                            $delivery_address = '<h3>'.TEXT_DELIVERY_DETAILS_TITLE.'</h3>'.
                                                TEXT_DELIVERY_ADDRESS_PRODUCT_NAME . ' : ' . $orders_products_info_row['products_name']. '(' . $orders_products_info_row['products_id'] . ')' .'<br/>'.
                                                TEXT_DELIVERY_ADDRESS_QUANTITY . ' : ' . ($orders_products_info_row['products_delivered_quantity'] + $orders_products_row['products_quantity']). '<br/>'.
                                                TEXT_DELIVERY_ADDRESS_RECIPIENT_NAME. ' : '. $delivery_address_details['recipient_name']. '<br/>'.
                                                TEXT_DELIVERY_ADDRESS_CONTACT_NUMBER. ' : '. $delivery_address_details['contact_number']. '<br/>'.
                                                TEXT_DELIVERY_ADDRESS .' : '.$delivery_address_details['addr_1']. ', '. $delivery_address_details['addr_2']. ', '. $delivery_address_details['postcode'] . ', '.$delivery_address_details['city'].', ' .$delivery_address_details['state']. ', '.$delivery_address_details['country_name'] .' <br/><br/>';

                                            if(!empty($delivery_address)) {
                                                $delivery_email_cet = $delivery_address . "\n\n" . EMAIL_FOOTER;
                                                tep_mail(DELIVERY_EMAIL_CET_ADDRESS, DELIVERY_EMAIL_CET_ADDRESS, $email_title , $delivery_email_cet, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                            }
                                        }
                                    }
                                }
                            }
                            else if ($available_deliver_qty > 0) {
                                $cdkey_id_arr = deliver_cdkey($orders_products_info_row['products_id'], $orders_products_id, $available_deliver_qty, 'deliver', 0, 0, false);

                                if ($cdkey_id_arr) {
                                    $total_cdkey_id = sizeof($cdkey_id_arr);
                                    $log_user_msg = sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr));

                                    if (!$stock_row['products_skip_inventory']) {
                                        $update_qty_array = array(array('field_name' => 'products_actual_quantity',
                                                'operator' => '-',
                                                'value' => $total_cdkey_id)
                                        );
                                        // This function will handle the qty adjustment and keep the log if asking so
                                        tep_set_product_qty($orders_products_row['products_id'], $update_qty_array, true, $log_message_to_use, $log_user_msg);
                                    }

                                    $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
                                                                SET products_delivered_quantity = products_delivered_quantity + " . $total_cdkey_id . ",
                                                                    products_good_delivered_quantity = products_good_delivered_quantity + " . $total_cdkey_id . "
                                                                WHERE orders_id = '" . (int) $this->orders_id . "'
                                                                    AND products_id = '" . (int) $orders_products_info_row['products_id'] . "'
                                                                    AND orders_products_id = '" . (int) $orders_products_id . "'
                                                                    AND orders_products_is_compensate = 0
                                                                    AND products_bundle_id = 0";
                                    tep_db_query($delived_qty_update_sql);

                                    $partial_deliver_str .= '&raquo; ' . $orders_products_info_row['products_name'] . "\tx " . $total_cdkey_id . "\n";
                                    $this->delivery_details[$orders_products_id] = array(
                                        'products_name' => $orders_products_info_row['products_name'],
                                        'products_quantity' => $total_cdkey_id,
                                        'products_delivery_status' => true
                                    );
                                    $recalculate_delivered_price = true;

                                    // Sales activity log
                                    $this->insert_sales_activity($orders_products_id, tep_get_prid($orders_products_row['products_id']), 'D', $final_price * $orders_products_row['products_quantity'], $orders_products_row['products_quantity'], $total_cdkey_id);
                                    $this->cdkey_delivered = true;

                                    $qty_updated = true;

                                    $total_item_final_amount = $orders_products_info_row['final_price'] * $orders_products_info_row["products_quantity"];

                                    include_once(DIR_WS_CLASSES . 'ogm_report_tool.php');
                                    $ogm_report_tool_obj = new ogm_report_tool();
                                    $ogm_report_tool_obj->log_cdkey_delivered($cdkey_id_arr, $this->orders_id, $orders_products_id, $orders_products_info_row['products_id'], $total_item_final_amount, $orders_products_info_row["products_quantity"]);
                                    $ogm_report_tool_obj->log_cdkey_delivered_released($this->orders_id, $orders_products_info_row['products_id'], $orders_products_id, $total_item_final_amount, $orders_products_info_row["products_quantity"]);
                                    unset($ogm_report_tool_obj);
                                }
                            }
                        }
                    }

                    if ($recalculate_delivered_price)
                        $this->update_delivered_price($orders_products_id, '');
                }
            }
        }

        while (substr($partial_deliver_str, -1) == "\n")
            $partial_deliver_str = substr($partial_deliver_str, 0, -1);
        $partial_deliver_str = strip_tags($partial_deliver_str);

        if (tep_not_null($partial_deliver_str)) {
            if (tep_not_null($this->delivery_remark)) {
                $this->delivery_remark .= "\n" . $partial_deliver_str;
            } else {
                $this->delivery_remark .= $partial_deliver_str;
            }
        }
    }

    function delivery_product_automation($orders_products_id, $api_provider = '') {
        if (AUTO_DELIVERY_SC_ENABLED == 'true') {
            $this->execute_log .= "Locking product[" . $orders_products_id . "] for delivery";

            if ($this->lock_obj->processLockOrder($this->orders_id, process_locking::MOVE_ORDER) || $this->lock_obj->isLocked() === TRUE) {
                $order_delivery_queue_array = array(
                    'orders_products_id' => $orders_products_id,
                    'orders_id' => $this->orders_id,
                    'extra_info' => json_encode(array(
                        'api_provider' => $api_provider
                    )),
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_ORDERS_DELIVERY_QUEUE, $order_delivery_queue_array)) {
                    $this->execute_log .= ": success";
                } else {
                    $this->execute_log .= ": failed";
                }
            } else {
                $this->execute_log .= ": failed - locked by " . $this->lock_obj->getOrderIsLockedByEmail();
            }

            $this->execute_log .= "\n";
        }
    }

    function update_delivered_price($orders_product_id, $product_type = '') {
        $sub_prod_amt = 0;
        $delivered_amt = 0;
        $total_amount = 0;

        $order_info_select_sql = "	SELECT o.currency, c.decimal_places
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_CURRENCIES . " AS c
										ON (o.currency = c.code)
									WHERE o.orders_id = '" . tep_db_input($this->orders_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        if (!tep_not_null($product_type)) { // Hack the release function for bundle cdkey product
            $check_bundle_select_sql = "	SELECT parent_orders_products_id
											FROM " . TABLE_ORDERS_PRODUCTS . "
											WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
            $check_bundle_result_sql = tep_db_query($check_bundle_select_sql);

            if ($check_bundle_row = tep_db_fetch_array($check_bundle_result_sql)) {
                if ($check_bundle_row['parent_orders_products_id'] > 0) {
                    $orders_product_id = $check_bundle_row['parent_orders_products_id']; // Use package order product id instead of subproduct's order product id
                    $product_type = 'products_bundle'; // NOTE: Need to further check whether this is Dynamic or Static if they do not share the SAME case
                }
            }
        }

        switch ($product_type) {
            case 'products_bundle_dynamic':
            case 'products_bundle':
                $update_status = false;
                $total_subproduct = 0;
                $good_delivered_ratio = 0;

                $purchased_final_price_select_sql = "	SELECT products_id, final_price, products_quantity, custom_products_type_id
    													FROM " . TABLE_ORDERS_PRODUCTS . "
    													WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
    														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                $purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);

                if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
                    if ($order_info_row['currency'] == DEFAULT_CURRENCY) {
                        $total_amount = $purchased_final_price_row['products_quantity'] * tep_round($purchased_final_price_row['final_price'], $order_info_row['decimal_places']);
                    } else {
                        $total_amount = $purchased_final_price_row['products_quantity'] * $purchased_final_price_row['final_price'];
                    }

                    $subproduct_select_sql = "	SELECT products_quantity, products_good_delivered_quantity, orders_products_store_price, custom_products_type_id
												FROM " . TABLE_ORDERS_PRODUCTS . "
												WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
													AND parent_orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                    $subproduct_result_sql = tep_db_query($subproduct_select_sql);

                    while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
                        if ($purchased_final_price_row['custom_products_type_id'] == 2) { // new calculation for CDKEY product
                            if ($order_info_row['currency'] == DEFAULT_CURRENCY) {
                                $sub_prod_amt += $subproduct_row['products_quantity'] * tep_round($subproduct_row['orders_products_store_price'], $order_info_row['decimal_places']);
                                $delivered_amt += $subproduct_row['products_good_delivered_quantity'] * tep_round($subproduct_row['orders_products_store_price'], $order_info_row['decimal_places']);
                            } else {
                                $sub_prod_amt += $subproduct_row['products_quantity'] * $subproduct_row['orders_products_store_price'];
                                $delivered_amt += $subproduct_row['products_good_delivered_quantity'] * $subproduct_row['orders_products_store_price'];
                            }
                            $total_subproduct++;
                        } else {
                            if ($subproduct_row['products_quantity'] > 0) {
                                $good_delivered_ratio += (double) $subproduct_row['products_good_delivered_quantity'] / $subproduct_row['products_quantity'];
                                $total_subproduct++;
                            }
                        }
                    }

                    if ($total_subproduct) {
                        if ($purchased_final_price_row['custom_products_type_id'] == 2) { // new calculation for CDKEY product
                            if ($sub_prod_amt > 0) {
                                $latest_delivered_price = (double) ($delivered_amt / $sub_prod_amt) * $total_amount;
                                $update_status = true;
                            }
                        } else {
                            $latest_delivered_price = (double) ($total_amount * $good_delivered_ratio) / $total_subproduct;
                            $update_status = true;
                        }

                        if ($update_status) {
                            // Update the latest delivered price
                            $delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
															SET products_good_delivered_price = '" . tep_db_input($latest_delivered_price) . "'
			    											WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
			    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                            tep_db_query($delivered_price_update_sql);
                        }
                    }
                }

                break;
            default:
                // Update the latest delivered price
                if ($order_info_row['currency'] == DEFAULT_CURRENCY) {
                    $delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET products_good_delivered_price = products_good_delivered_quantity * ROUND(final_price, " . (int) $order_info_row['decimal_places'] . ")
	    											WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                } else {
                    $delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
													SET products_good_delivered_price = products_good_delivered_quantity * final_price
	    											WHERE orders_id = '" . tep_db_input($this->orders_id) . "'
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
                }
                tep_db_query($delivered_price_update_sql);

                break;
        }
    }

    // Any changes to this function, remember to change the 1 in edit_order.php
    function insert_sales_activity($op_id, $p_id, $activity_code, $total_amt, $total_qty, $activity_qty) {
        global $login_id, $login_email_address;

        if ($this->changed_by == $login_id) {
            $sales_activities_by_admin_id = $login_email_address;
        } else {
            $sales_activities_by_admin_id = $this->changed_by;
        }

        if ($total_qty > 0 && $activity_qty > 0) {
            switch ($activity_code) {
                case 'D': // Deliver
                    $activity_operator = '+';
                    break;
                case 'RD': // Reduce delivered qty
                    $activity_operator = '-';
                    break;
                case 'CD': // Compensate delivered qty
                    $activity_operator = '+';
                    break;
                case 'CRD': // Compensate reduce delivered qty
                    $activity_operator = '-';
                    break;
                case 'RFD': // Refund delivered qty
                    $activity_operator = '+';
                    break;
                case 'RFRD':// Refund reduce delivered qty
                    $activity_operator = '-';
                    break;
                case 'RVD': // Reverse delivered qty
                    $activity_operator = '+';
                    break;
                case 'RVRD':// Reverse reduce delivered qty
                    $activity_operator = '-';
                    break;
            }

            $sales_activities_sql_data = array('sales_activities_date' => 'now()',
                'sales_activities_orders_id' => $this->orders_id,
                'sales_activities_orders_products_id' => $op_id,
                'sales_activities_products_id' => $p_id,
                'sales_activities_code' => $activity_code,
                'sales_activities_operator' => $activity_operator,
                'sales_activities_amount' => ($activity_qty * $total_amt) / $total_qty,
                'sales_activities_quantity' => $activity_qty,
                'sales_activities_by_admin_id' => $sales_activities_by_admin_id,
            );
            tep_db_perform(TABLE_SALES_ACTIVITIES, $sales_activities_sql_data);
        }
    }

    function is_order_payment_info_verified() {
        global $order;

        $check_info = array();
        $compare_type = '';
        $return_result = '';
        $payment_methods_filename = $this->get_payment_methods_filename();

        if (tep_not_empty($payment_methods_filename)) {
            switch (strtolower($payment_methods_filename)) {
                case 'adyen.php':
                    $compare_type = 'credit_card';
                    $credit_card_number_select_sql = "	SELECT adyen_cc_card_summary as card_number
														FROM " . TABLE_ADYEN . "
														WHERE adyen_order_id = '" . $order->order_id . "'";
                    $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                    $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                    if (tep_not_null($credit_card_number_row['card_number'])) {
                        $check_info = array('card_number' => $credit_card_number_row['card_number'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'payU.php':
                    $compare_type = 'credit_card';
                    $credit_card_number_select_sql = "	SELECT cc_number as card_number
														FROM " . TABLE_PAYU . "
														WHERE order_id = '" . $order->order_id . "'";
                    $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                    $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                    if (tep_not_null($credit_card_number_row['card_number'])) {
                        $check_info = array('card_number' => $credit_card_number_row['card_number'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'paypal.php':
                    $compare_type = 'paypal_payer_id';
                    $payment_info_select_sql = "	SELECT p.payer_id
													FROM " . TABLE_PAYPAL . " AS p
													WHERE p.invoice = '" . $order->order_id . "'";
                    $payment_info_result_sql = tep_db_query($payment_info_select_sql);
                    $payment_info_row = tep_db_fetch_array($payment_info_result_sql);

                    if (tep_not_null($payment_info_row['payer_id'])) {
                        $check_info = array('payer_id' => $payment_info_row['payer_id'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'paypalec.php':
                    $compare_type = 'paypal_payer_id';
                    $payment_info_select_sql = "	SELECT p.payer_id
													FROM " . TABLE_PAYPALEC . " AS p
													WHERE p.paypal_order_id = '" . $order->order_id . "'";
                    $payment_info_result_sql = tep_db_query($payment_info_select_sql);
                    $payment_info_row = tep_db_fetch_array($payment_info_result_sql);

                    if (tep_not_null($payment_info_row['payer_id'])) {
                        $check_info = array('payer_id' => $payment_info_row['payer_id'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'bibit.php':
                    $compare_type = 'credit_card';
                    $credit_card_number_select_sql = "	SELECT b.bibit_card_number as card_number
														FROM " . TABLE_BIBIT . " AS b
														WHERE b.orders_id = '" . $order->order_id . "'";
                    $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                    $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                    if (tep_not_null($credit_card_number_row['card_number'])) {
                        $check_info = array('card_number' => $credit_card_number_row['card_number'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'global_collect.php':
                    $compare_type = 'credit_card';
                    $credit_card_number_select_sql = "	SELECT gc.global_collect_cc_last_4_digit as card_number
														FROM " . TABLE_GLOBAL_COLLECT . " AS gc
														WHERE gc.global_collect_orders_id = '" . $order->order_id . "'";
                    $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                    $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                    if (tep_not_null($credit_card_number_row['card_number'])) {
                        $check_info = array('card_number' => $credit_card_number_row['card_number'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
                case 'moneybookers.php':
                    $compare_type = 'moneybookers';
                    $select_sql = "	SELECT mb_payer_email
                                        FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
                                        WHERE mb_trans_id = '" . $order->order_id . "'";
                    $result_sql = tep_db_query($select_sql);
                    $result_row = tep_db_fetch_array($result_sql);

                    if (tep_not_null($result_row['mb_payer_email'])) {
                        $check_info = array('email' => $result_row['mb_payer_email'],
                            'date_purchased' => $order->info['date_purchased'],
                            'orders_id' => $order->order_id,
                            'customers_id' => $order->customer['id']);
                    }
                    break;
            }
        }

        $return_result = tep_get_payment_info_verified_date($check_info, $compare_type);

        $this->execute_log .= 'First successful verified date by this user: ' . (tep_not_null($return_result) ? $return_result : "<span class='redIndicator'>NEVER</span>") . "\n";

        return (tep_not_null($return_result) ? true : false );
    }

    function get_order_confirm_complete_day($logged = false) {
        global $order;

        $confirm_complete_days = 150;
        $payment_methods_id = 0;
        $checkout_site = '';

        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->payment_method_code)) {
            $pmInfo = $this->pw_obj->pipwavePaymentMapper('pipwave', $this->pw_obj->payment_method_code);
            if (isset($pmInfo['is_rp']) && $pmInfo['is_rp'] > 0) {
                //RP & SRP
                $confirm_complete_days = 150;
            } else {
                //NRP
                $confirm_complete_days = 0;
            }
        } else {
            if (is_object($order) && isset($order->info['payment_methods_id'])) {
                $payment_methods_id = $order->info['payment_methods_id'];
            } else {
                $pm_id_select_sql = "	SELECT payment_methods_id
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . (int) $this->orders_id . "'";
                $pm_id_result_sql = tep_db_query($pm_id_select_sql);
                $pm_id_row = tep_db_fetch_array($pm_id_result_sql);

                $payment_methods_id = $pm_id_row['payment_methods_id'];
            }

            if ($payment_methods_id > 0) {
                $payment_methods_obj_tmp = new payment_methods($payment_methods_id);
                $payment_methods_obj = $payment_methods_obj_tmp->payment_method_array;
                $confirm_complete_days = $payment_methods_obj->confirm_complete_days;
            }
        }

        if ($logged) {
            $this->execute_log .= "get_order_confirm_complete_day(): " . $confirm_complete_days . "\n";
        }

        return $confirm_complete_days;
    }

    function get_reversible_fund_percentage() {
        global $currencies;

        if ($this->get_order_payment_method(false) == 0 && $this->get_order_payment_method_parent(false) == 0 && $this->validate_zero_amount_order() === true) {
            $reversible_fund_percentage = 0;
            $this->execute_log .= "is_free_product(): true\n";
        } else {
            $sc_used_info = 0;

            $order_select_sql = "   SELECT o.currency_value, o.payment_methods_id, o.currency
                                    FROM " . TABLE_ORDERS . " AS o
                                    WHERE o.orders_id = '" . (int) $this->orders_id . "' ";
            $order_result_sql = tep_db_query($order_select_sql);
            $order_row = tep_db_fetch_array($order_result_sql);
            
            $ot_gv_qry = " SELECT ot.value
                                        FROM " . TABLE_ORDERS_TOTAL . " AS ot
                                        WHERE ot.orders_id = '" . (int) $this->orders_id . "' 
                                            AND ot.class = 'ot_gv'";
            $ot_gv_result = tep_db_query($ot_gv_qry);
            if ($ot_gv_row = tep_db_fetch_array($ot_gv_result)) {
                $sc_used_info = number_format($ot_gv_row['value'] * $order_row['currency_value'], $currencies->currencies[$order_row['currency']]['decimal_places'], $currencies->currencies[$order_row['currency']]['decimal_point'], '');
            }

            $get_order_confirm_complete_day = $this->get_order_confirm_complete_day();

            if ($sc_used_info > 0) {
                $display_total_value = 0;
                $display_sub_total_value = 0;
                $display_discount_total_value = 0;
                $display_surcharge_value = 0;

                $order_total_select_sql = "	SELECT ot.value, ot.class
											FROM " . TABLE_ORDERS_TOTAL . " AS ot
											WHERE ot.orders_id = '" . (int) $this->orders_id . "' ";
                $order_total_result_sql = tep_db_query($order_total_select_sql);
                while ($order_total_row = tep_db_fetch_array($order_total_result_sql)) {
                    switch ($order_total_row['class']) {
                        case 'ot_total':
                            $display_total_value = number_format($order_total_row['value'] * $order_row['currency_value'], $currencies->currencies[$order_row['currency']]['decimal_places'], $currencies->currencies[$order_row['currency']]['decimal_point'], '');
                            break;
                        case 'ot_subtotal':
                            $display_sub_total_value = number_format($order_total_row['value'] * $order_row['currency_value'], $currencies->currencies[$order_row['currency']]['decimal_places'], $currencies->currencies[$order_row['currency']]['decimal_point'], '');
                            break;
                        case 'ot_coupon':
                            $display_discount_total_value = number_format($order_total_row['value'] * $order_row['currency_value'], $currencies->currencies[$order_row['currency']]['decimal_places'], $currencies->currencies[$order_row['currency']]['decimal_point'], '');
                            break;
                        case 'ot_surcharge':
                            $display_surcharge_value = number_format($order_total_row['value'] * $order_row['currency_value'], $currencies->currencies[$order_row['currency']]['decimal_places'], $currencies->currencies[$order_row['currency']]['decimal_point'], '');
                            break;
                    }
                }

                $display_sub_total_value = $display_sub_total_value - $display_discount_total_value + $display_surcharge_value;

                if ($get_order_confirm_complete_day > 0) {
                    $display_rsc_percent_value = (((double) (isset($sc_used_info) ? $sc_used_info : 0) + $display_total_value) / $display_sub_total_value);
                } else {
                    $display_rsc_percent_value = ((double) (isset($sc_used_info) ? $sc_used_info : 0) / $display_sub_total_value);
                }
                $reversible_fund_percentage = round($display_rsc_percent_value * 100, 2);
            } else {
                if ($get_order_confirm_complete_day == 0) {
                    $reversible_fund_percentage = 0;
                } else {
                    $reversible_fund_percentage = 100;
                }
            }
        }
        $this->execute_log .= 'get_reversible_fund_percentage(): ' . $reversible_fund_percentage . "%\n";

        return $reversible_fund_percentage;
    }

    function is_paypal_country_matched_billing_country($logged = true) {
        $result = false;
        $payment_method_select_sql = "	SELECT pm.payment_methods_filename, o.customers_id, o.billing_country
										FROM " . TABLE_PAYMENT_METHODS . " AS pm
										INNER JOIN " . TABLE_ORDERS . " AS o
											ON o.payment_methods_parent_id = pm.payment_methods_id
										WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $payment_method_result_sql = tep_db_query($payment_method_select_sql);
        $payment_method_row = tep_db_fetch_array($payment_method_result_sql);
        if ($payment_method_row['payment_methods_filename'] == 'paypal.php') {
            $paypal_select_sql = "	SELECT address_country
									FROM " . TABLE_PAYPAL . "
									WHERE invoice = '" . (int) $this->orders_id . "'";
            $paypal_result_sql = tep_db_query($paypal_select_sql);
            if ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {
                if (strtolower(trim($paypal_row['address_country'])) == strtolower(trim($payment_method_row['billing_country']))) {
                    $result = true;
                    if ($logged) {
                        $this->execute_log .= "is_paypal_country_matched_billing_country(): true\n";
                    }
                } else {
                    if ($logged) {
                        $this->execute_log .= "is_paypal_country_matched_billing_country(): false\n";
                    }
                }
            }
        } else if ($payment_method_row['payment_methods_filename'] == 'paypalEC.php') {
            $paypal_select_sql = "	SELECT address_country
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . (int) $this->orders_id . "'";
            $paypal_result_sql = tep_db_query($paypal_select_sql);
            if ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {
                if (strtolower(trim($paypal_row['address_country'])) == strtolower(trim($payment_method_row['billing_country']))) {
                    $result = true;
                    if ($logged) {
                        $this->execute_log .= "is_paypal_country_matched_billing_country(): true\n";
                    }
                } else {
                    if ($logged) {
                        $this->execute_log .= "is_paypal_country_matched_billing_country(): false\n";
                    }
                }
            }
        } else {
            if ($logged) {
                $this->execute_log .= "is_paypal_country_matched_billing_country(): Non PayPal order detected.\n";
            }
        }

        return $result;
    }

    function is_paypal_name_matched_profile_name($logged = true) {

        $payment_method_select_sql = "	SELECT pm.payment_methods_filename, o.customers_id, c.customers_firstname, c.customers_lastname
										FROM " . TABLE_PAYMENT_METHODS . " AS pm
										INNER JOIN " . TABLE_ORDERS . " AS o
											ON o.payment_methods_parent_id = pm.payment_methods_id
										INNER JOIN " . TABLE_CUSTOMERS . " AS c
											ON o.customers_id = c.customers_id
										WHERE o.orders_id = '" . (int) $this->orders_id . "'";
        $payment_method_result_sql = tep_db_query($payment_method_select_sql);
        $payment_method_row = tep_db_fetch_array($payment_method_result_sql);
        if ($payment_method_row['payment_methods_filename'] == 'paypal.php') {
            $paypal_select_sql = "	SELECT first_name, last_name
									FROM " . TABLE_PAYPAL . "
									WHERE invoice = '" . (int) $this->orders_id . "'";
            $paypal_result_sql = tep_db_query($paypal_select_sql);
            if ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {

                $customer_name = strtolower(trim($payment_method_row['customers_firstname'] . ' ' . $payment_method_row['customers_lastname']));
                $customer_name = preg_match_all("/[a-zA-Z\/\\._]+/i", $customer_name, $customer_name_tmp);
                if (isset($customer_name_tmp[0]) && count($customer_name_tmp[0])) {
                    $customer_name = implode(" ", $customer_name_tmp[0]);
                }

                $paypal_name = strtolower(trim($paypal_row['first_name'] . ' ' . $paypal_row['last_name']));
                $paypal_name = preg_match_all("/[a-zA-Z\/\\._]+/i", $paypal_name, $paypal_name_tmp);
                if (isset($paypal_name_tmp[0]) && count($paypal_name_tmp[0])) {
                    $paypal_name = implode(" ", $paypal_name_tmp[0]);
                }

                $paypal_name = strtolower($paypal_row['first_name'] . ' ' . $paypal_row['last_name']);
                $customer_name = strtolower($payment_method_row['customers_firstname'] . ' ' . $payment_method_row['customers_lastname']);

                $customer_name_array = array();
                $customer_name_array[] = explode(" ", $customer_name);
                $customer_name_array[] = array(str_replace(" ", "", $customer_name));
                if (preg_match("/([\/\\._]+)/i", $customer_name)) {
                    $customer_name_array[] = explode(" ", preg_replace("/([\/\\._]+)/i", "", $customer_name));

                    $customer_name_tmp_array = explode(" ", preg_replace("/([\/\\._]+)/i", " ", $customer_name));
                    $customer_name_tmp_array = array_filter($customer_name_tmp_array);
                    reset($customer_name_tmp_array);
                    $customer_name_array[] = $customer_name_tmp_array;
                }

                $paypal_name_array = array();
                $paypal_name_array[] = explode(" ", $paypal_name);
                $paypal_name_array[] = array(str_replace(" ", "", $paypal_name));
                if (preg_match("/([\/\\._]+)/i", $paypal_name)) {
                    $paypal_name_array[] = explode(" ", preg_replace("/([\/\\._]+)/i", "", $paypal_name));

                    $paypal_name_tmp_array = explode(" ", preg_replace("/([\/\\._]+)/i", " ", $paypal_name));
                    $paypal_name_tmp_array = array_filter($paypal_name_tmp_array);
                    reset($paypal_name_tmp_array);
                    $customer_name_array[] = $paypal_name_tmp_array;
                }

                foreach ($customer_name_array as $customer_name_loop) {
                    foreach ($paypal_name_array as $paypal_name_loop) {
                        $used_index = array();
                        $matched_counter = 0;
                        if (count($customer_name_loop) == count($paypal_name_loop)) {
                            foreach ($customer_name_loop as $index_loop => $customer_word_loop) {
                                if (in_array($customer_word_loop, $paypal_name_loop) && !in_array($index_loop, $used_index)) {
                                    $matched_counter++;
                                    $used_index[] = $index_loop;
                                } else {
                                    break;
                                }
                            }
                        }
                        if ($matched_counter == count($customer_name_loop) && count($used_index) == count($customer_name_loop)) {
                            if ($logged) {
                                $this->execute_log .= "is_paypal_name_matched_profile_name(): true\n";
                            }
                            return true;
                        }
                    }
                }
            }
            if ($logged) {
                $this->execute_log .= "is_paypal_name_matched_profile_name(): false\n";
            }
        } else if ($payment_method_row['payment_methods_filename'] == 'paypalEC.php') {
            $paypal_select_sql = "	SELECT first_name, last_name
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . (int) $this->orders_id . "'";
            $paypal_result_sql = tep_db_query($paypal_select_sql);
            if ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {

                $customer_name = strtolower(trim($payment_method_row['customers_firstname'] . ' ' . $payment_method_row['customers_lastname']));
                $customer_name = preg_match_all("/[a-zA-Z\/\\._]+/i", $customer_name, $customer_name_tmp);
                if (isset($customer_name_tmp[0]) && count($customer_name_tmp[0])) {
                    $customer_name = implode(" ", $customer_name_tmp[0]);
                }

                $paypal_name = strtolower(trim($paypal_row['first_name'] . ' ' . $paypal_row['last_name']));
                $paypal_name = preg_match_all("/[a-zA-Z\/\\._]+/i", $paypal_name, $paypal_name_tmp);
                if (isset($paypal_name_tmp[0]) && count($paypal_name_tmp[0])) {
                    $paypal_name = implode(" ", $paypal_name_tmp[0]);
                }

                $paypal_name = strtolower($paypal_row['first_name'] . ' ' . $paypal_row['last_name']);
                $customer_name = strtolower($payment_method_row['customers_firstname'] . ' ' . $payment_method_row['customers_lastname']);

                $customer_name_array = array();
                $customer_name_array[] = explode(" ", $customer_name);
                $customer_name_array[] = array(str_replace(" ", "", $customer_name));
                if (preg_match("/([\/\\._]+)/i", $customer_name)) {
                    $customer_name_array[] = explode(" ", preg_replace("/([\/\\._]+)/i", "", $customer_name));

                    $customer_name_tmp_array = explode(" ", preg_replace("/([\/\\._]+)/i", " ", $customer_name));
                    $customer_name_tmp_array = array_filter($customer_name_tmp_array);
                    reset($customer_name_tmp_array);
                    $customer_name_array[] = $customer_name_tmp_array;
                }

                $paypal_name_array = array();
                $paypal_name_array[] = explode(" ", $paypal_name);
                $paypal_name_array[] = array(str_replace(" ", "", $paypal_name));
                if (preg_match("/([\/\\._]+)/i", $paypal_name)) {
                    $paypal_name_array[] = explode(" ", preg_replace("/([\/\\._]+)/i", "", $paypal_name));

                    $paypal_name_tmp_array = explode(" ", preg_replace("/([\/\\._]+)/i", " ", $paypal_name));
                    $paypal_name_tmp_array = array_filter($paypal_name_tmp_array);
                    reset($paypal_name_tmp_array);
                    $customer_name_array[] = $paypal_name_tmp_array;
                }

                foreach ($customer_name_array as $customer_name_loop) {
                    foreach ($paypal_name_array as $paypal_name_loop) {
                        $used_index = array();
                        $matched_counter = 0;
                        if (count($customer_name_loop) == count($paypal_name_loop)) {
                            foreach ($customer_name_loop as $index_loop => $customer_word_loop) {
                                if (in_array($customer_word_loop, $paypal_name_loop) && !in_array($index_loop, $used_index)) {
                                    $matched_counter++;
                                    $used_index[] = $index_loop;
                                } else {
                                    break;
                                }
                            }
                        }
                        if ($matched_counter == count($customer_name_loop) && count($used_index) == count($customer_name_loop)) {
                            if ($logged) {
                                $this->execute_log .= "is_paypal_name_matched_profile_name(): true\n";
                            }
                            return true;
                        }
                    }
                }
            }
            if ($logged) {
                $this->execute_log .= "is_paypal_name_matched_profile_name(): false\n";
            }
        } else {
            if ($logged) {
                $this->execute_log .= "is_paypal_name_matched_profile_name(): false\n";
            }
        }
        return false;
    }

    function is_exceed_total_unique_order_ip($max_counter, $duration = 30) {
        $orders_ip_number = 0;

        $date_purchased_select_sql = "	SELECT o.date_purchased
    									FROM " . TABLE_ORDERS . " AS o
    									WHERE o.orders_id = '" . $this->orders_id . "'";
        $date_purchased_result_sql = tep_db_query($date_purchased_select_sql);
        if ($date_purchased_row = tep_db_fetch_array($date_purchased_result_sql)) {
            $orders_ip_select_sql = "	SELECT distinct o.remote_addr
	    								FROM " . TABLE_ORDERS . " AS o
	    								WHERE o.customers_id = '" . $this->customers_id . "'
	    									AND o.date_purchased >= DATE_SUB('" . $date_purchased_row['date_purchased'] . "', INTERVAL " . $duration . " MINUTE)
	    									AND o.orders_id <= '" . $this->orders_id . "'";
            $orders_ip_result_sql = tep_db_query($orders_ip_select_sql);
            $orders_ip_number = tep_db_num_rows($orders_ip_result_sql);
        }

        $result = ($orders_ip_number > $max_counter ? true : false);
        $this->execute_log .= "is_exceed_total_unique_order_ip: " . ($result ? 'true' : 'false') . ", " . $orders_ip_number . "\n";

        return $result;
    }

    // duration: minutes (default to 1 Month)
    // Check for Login IP of the Customer within this duration and anyone sharing with it within 6 months
    function is_exceed_shared_login_ip($max_counter, $this_cust_duration = 43200, $other_cust_duration = 43200) {
        $exceed_shared_login_ip = false;

        $this_customer_login_ip_select_sql = "	SELECT DISTINCT customers_login_ip
                                                FROM " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . "
                                                WHERE customers_id = '" . $this->customers_id . "'
                                                    AND customers_login_date >= DATE_SUB(NOW(), INTERVAL " . $this_cust_duration . " MINUTE);";
        $this_customer_login_ip_result_sql = tep_db_query($this_customer_login_ip_select_sql);
        while ($this_customer_login_ip_row = tep_db_fetch_array($this_customer_login_ip_result_sql)) {
            $share_ip_select_sql = "	SELECT COUNT(DISTINCT customers_id) AS total
	    								FROM " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . "
	    								WHERE customers_login_date >= DATE_SUB(NOW(), INTERVAL " . $other_cust_duration . " MINUTE)
	    									AND customers_login_ip = '" . $this_customer_login_ip_row['customers_login_ip'] . "'";
            $share_ip_result_sql = tep_db_query($share_ip_select_sql);
            $share_ip_row = tep_db_fetch_array($share_ip_result_sql);

            if ($share_ip_row['total'] > $max_counter) {
                $exceed_shared_login_ip = true;
                break;
            }
        }

        $this->execute_log .= "is_exceed_shared_login_ip: " . ($exceed_shared_login_ip ? 'true' : 'false') . "\n";

        return $exceed_shared_login_ip;
    }

    // duration: minutes
    function is_exceed_unverified_pg_counter($max_counter, $duration = 30) {
        global $order;

        if ($duration > 10080) {
            $this->execute_log .= "is_exceed_unverified_pg_counter: max duration force reduce from " . $duration . " to 10080\n";
            $duration = 10080; // 7 days
        }

        $result = false;

        $unverified_counter = 0;
        $compare_type = '';
        $return_result = '';

        $date_purchased_select_sql = "	SELECT o.date_purchased
    									FROM " . TABLE_ORDERS . " AS o
    									WHERE o.orders_id = '" . $this->orders_id . "'";
        $date_purchased_result_sql = tep_db_query($date_purchased_select_sql);
        if ($date_purchased_row = tep_db_fetch_array($date_purchased_result_sql)) {
            $payment_methods_array = array();

            $payment_methods_select_sql = "	SELECT o.orders_id, o.payment_methods_id, pm.payment_methods_filename, o.date_purchased
		    								FROM " . TABLE_ORDERS . " AS o
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
												ON pm.payment_methods_id = o.payment_methods_parent_id
		    								WHERE o.customers_id = '" . $this->customers_id . "'
		    									AND o.date_purchased >= DATE_SUB('" . $date_purchased_row['date_purchased'] . "', INTERVAL " . $duration . " MINUTE)
		    									AND o.orders_id <= '" . $this->orders_id . "'
		    								ORDER BY o.orders_id";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                $payment_methods_array[$payment_methods_row['orders_id']] = $payment_methods_row;
            }

            foreach ($payment_methods_array as $orders_id_loop => $payment_data_loop) {
                $check_info = array();

                switch ($payment_data_loop['payment_methods_filename']) {
                    case 'adyen.php':
                        $compare_type = 'credit_card';
                        $credit_card_number_select_sql = "	SELECT adyen_cc_card_summary as card_number
															FROM " . TABLE_ADYEN . "
															WHERE adyen_order_id = '" . $orders_id_loop . "'";
                        $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                        $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                        if (tep_not_null($credit_card_number_row['card_number'])) {
                            $check_info = array('card_number' => $credit_card_number_row['card_number'],
                                'date_purchased' => $payment_data_loop['date_purchased'],
                                'orders_id' => $order->order_id,
                                'customers_id' => $order->customer['id']);
                        }
                        break;
                    case 'paypal.php':
                        $compare_type = 'paypal_payer_id';
                        $payment_info_select_sql = "	SELECT p.payer_id
															FROM " . TABLE_PAYPAL . " AS p
															WHERE p.invoice = '" . $orders_id_loop . "'";
                        $payment_info_result_sql = tep_db_query($payment_info_select_sql);
                        $payment_info_row = tep_db_fetch_array($payment_info_result_sql);

                        if (tep_not_null($payment_info_row['payer_id'])) {
                            $check_info = array('payer_id' => $payment_info_row['payer_id'],
                                'date_purchased' => $payment_data_loop['date_purchased'],
                                'orders_id' => $order->order_id,
                                'customers_id' => $order->customer['id']);
                        }
                        break;
                    case 'paypalEC.php':
                        $compare_type = 'paypal_payer_id';
                        $payment_info_select_sql = "	SELECT p.payer_id
															FROM " . TABLE_PAYPALEC . " AS p
															WHERE p.paypal_order_id = '" . $orders_id_loop . "'";
                        $payment_info_result_sql = tep_db_query($payment_info_select_sql);
                        $payment_info_row = tep_db_fetch_array($payment_info_result_sql);

                        if (tep_not_null($payment_info_row['payer_id'])) {
                            $check_info = array('payer_id' => $payment_info_row['payer_id'],
                                'date_purchased' => $payment_data_loop['date_purchased'],
                                'orders_id' => $order->order_id,
                                'customers_id' => $order->customer['id']);
                        }
                        break;
                    case 'bibit.php':
                        $compare_type = 'credit_card';
                        $credit_card_number_select_sql = "	SELECT b.bibit_card_number as card_number
																FROM " . TABLE_BIBIT . " AS b
																WHERE b.orders_id = '" . $orders_id_loop . "'";
                        $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                        $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                        if (tep_not_null($credit_card_number_row['card_number'])) {
                            $check_info = array('card_number' => $credit_card_number_row['card_number'],
                                'date_purchased' => $payment_data_loop['date_purchased'],
                                'orders_id' => $order->order_id,
                                'customers_id' => $order->customer['id']);
                        }
                        break;
                    case 'global_collect.php':
                        $compare_type = 'credit_card';
                        $credit_card_number_select_sql = "	SELECT gc.global_collect_cc_last_4_digit as card_number
																FROM " . TABLE_GLOBAL_COLLECT . " AS gc
																WHERE gc.global_collect_orders_id = '" . $orders_id_loop . "'";
                        $credit_card_number_result_sql = tep_db_query($credit_card_number_select_sql);
                        $credit_card_number_row = tep_db_fetch_array($credit_card_number_result_sql);

                        if (tep_not_null($credit_card_number_row['card_number'])) {
                            $check_info = array('card_number' => $credit_card_number_row['card_number'],
                                'date_purchased' => $payment_data_loop['date_purchased'],
                                'orders_id' => $order->order_id,
                                'customers_id' => $order->customer['id']);
                        }
                        break;
                }

                if (isset($check_info['customers_id'])) {
                    $return_result = tep_get_payment_info_verified_date($check_info, $compare_type, array('7', '2', '3'));

                    if (!tep_not_null($return_result)) {
                        $unverified_counter++;
                    }

                    if ($unverified_counter > $max_counter) {
                        $result = true;
                        break;
                    }
                }
            }
        }

        $this->execute_log .= "is_exceed_unverified_pg_counter: " . ($result ? 'true' : 'false') . ", exceed " . $unverified_counter . "\n";

        return $result;
    }

    function get_total_unique_customers_by_device($duration) {
        $result = $this->aft_module->get_total_unique_customers_by_device($duration);
        $this->execute_log .= "get_total_unique_customers_by_device: " . $result . "\n";
        return $result;
    }

    function get_device_verification_result() {
        $result = $this->aft_module->get_device_verification_result();
        $this->execute_log .= "get_device_verification_result: " . $result . "\n";
        return $result;
    }

    function get_last_login_device_os($day_interval = '1440') {
        $result = array();

        if (tep_not_empty($this->order_date_purchased)) {
            $login_ip_history_select = "SELECT DISTINCT customers_login_ua_info
                                        FROM " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . "
                                        WHERE customers_id ='" . $this->customers_id . "'
                                            AND customers_login_date >= DATE_SUB('" . $this->order_date_purchased . "', INTERVAL " . $day_interval . " MINUTE)
                                            AND customers_login_date <= '" . $this->order_date_purchased . "'";
            $login_ip_history_result = tep_db_query($login_ip_history_select);
            while ($login_ip_history_row = tep_db_fetch_array($login_ip_history_result)) {
                $ua_detector_obj = new user_agent_detector($login_ip_history_row['customers_login_ua_info']);
                $result[] = $ua_detector_obj->os;
                unset($ua_detector_obj);
            }
        }

        $this->execute_log .= "get_last_login_device_os: " . implode(", ", $result) . "\n";
        return $result;
    }

    function validate_subtotal_against_product_amount($max_acceptable_diff = '') {
        $validation_select_sql = "	SELECT o.orders_id,
									(
										SELECT SUM( op.final_price * op.products_quantity )
										FROM " . TABLE_ORDERS_PRODUCTS . " AS op
										WHERE op.orders_id = o.orders_id
									) AS actual_payment_amt,
									ot_subtotal.value AS subtotal,
									(
										(	SELECT SUM( op.final_price * op.products_quantity )
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op
											WHERE op.orders_id = o.orders_id
										) - ot_subtotal.value
									) AS diff
									FROM " . TABLE_ORDERS . "  AS o
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot_subtotal
										ON ot_subtotal.orders_id = o.orders_id
									WHERE o.orders_id = '" . $this->orders_id . "'
										AND ot_subtotal.class = 'ot_subtotal'
									HAVING diff > " . (tep_not_null($max_acceptable_diff) && (double) $max_acceptable_diff > 0 ? (double) $max_acceptable_diff : 3);
        $validation_result_sql = tep_db_query($validation_select_sql);
        if ($validation_result_sql = tep_db_fetch_array($validation_result_sql)) {
            return false;
        }
        return true;
    }

    function validate_zero_amount_order() {
        $order_product_price_validate_sql = "	SELECT p.products_id
				    							FROM " . TABLE_ORDERS_PRODUCTS . " AS op
				    							INNER JOIN " . TABLE_PRODUCTS . " as p
				    								ON op.products_id = p.products_id
				    							WHERE op.orders_id = '" . (int) $this->orders_id . "'
				    								AND op.parent_orders_products_id = 0
				    								AND p.products_price > 0";
        $order_product_price_validate_result = tep_db_query($order_product_price_validate_sql);

        // Found non-zero price products
        if (tep_db_num_rows($order_product_price_validate_result) > 0) {
            return false;
        }

        return true;
    }

    function do_set_order_status($payment_methods_filename, $orders_status, $line = '') {
        if (!tep_not_null($line)) {
            $line = __LINE__;
        }

        if ($payment_methods_filename == 'bibit.php' || $payment_methods_filename == 'global_collect.php' || $payment_methods_filename == 'adyen.php') {
            if ($this->post_capture() == true) {
                $this->set_order_status($orders_status, $line);
            }
        } else {
            $this->set_order_status($orders_status, $line);
        }
    }

    function get_payment_methods_filename() {
        return payment_methods::get_payment_methods_filename($this->payment_gateway_id);
    }

    function updateG2GOrders() {
        $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "' ;";
        $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
        if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
            // update all the selected orders with this tag
            $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $setting_value . "')) WHERE orders_id IN (" . implode(',', $order_ids_array) . ") AND NOT FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
            tep_db_query($assign_orders_tag_update_sql);

            generateTagString($order_ids_array);
        }
    }

    function pipwavePrediction() {
        global $login_email_address;
        $proceedAPI = true;
        $prediction_result_select_sql = "	SELECT result_label, result_score, version
                                            FROM " . TABLE_PIPWAVE_PREDICTION . "
                                            WHERE order_id = '" . (int) $this->orders_id . "'";
        $prediction_result_result_sql = tep_db_query($prediction_result_select_sql);
        if ($prediction_result_row = tep_db_fetch_array($prediction_result_result_sql)) {
            if ($prediction_result_row['version'] && $prediction_result_row['version'] == PIPWAVE_MERCHANT_API_VERSION) {
                if (isset($prediction_result_row['result_label']) && !empty($prediction_result_row['result_label']) && isset($prediction_result_row['result_score']) && !empty($prediction_result_row['result_score'])) {
                    $this->execute_log .= "pipwave_prediction label: " . $prediction_result_row['result_label'] . ", score: " . $prediction_result_row['result_score'] . "\n";
                    $proceedAPI = false;
                }
            }
        }
        $_sel_sql = "	SELECT orders_extra_info_value
                        FROM " . TABLE_ORDERS_EXTRA_INFO . "
                        WHERE orders_id = '" . (int) $this->orders_id . "'
                            AND orders_extra_info_key = 'site_id'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $site_id = $_row_sql['orders_extra_info_value'];
        }
        if (isset($site_id) && $site_id == '5') {
            $key = PIPWAVE_G2G_MERCHANT_API_KEY;
            $secret = PIPWAVE_G2G_MERCHANT_API_SECRET;
        } else {
            $key = PIPWAVE_MERCHANT_API_KEY;
            $secret = PIPWAVE_MERCHANT_API_SECRET;
        }
        if (!isset($key) || !isset($secret)) {
            $proceedAPI = false;
        }
        if ($proceedAPI) {
            $requestData = array();
            $order_select_sql = " SELECT o.orders_id as transaction_id, c.customers_firstname as buyer_firstname, c.customers_lastname as buyer_lastname,
                                  o.customers_city as buyer_city, o.customers_country as buyer_country,o.customers_country_international_dialing_code as buyer_dialing_code,
                                  o.customers_email_address as buyer_email, o.customers_postcode as buyer_postcode,
                                  o.customers_state as buyer_state, o.customers_street_address as buyer_street, o.customers_suburb as buyer_street2,
                                  o.customers_telephone as buyer_phone, o.customers_telephone_country as buyer_phone_country,
                                  o.currency as currency, o.date_purchased as transaction_date, o.remote_addr as ip_address,
                                  o.payment_methods_id as pm_id, o.payment_methods_parent_id as pg_id
                                  FROM " . TABLE_ORDERS . " AS o
                                  INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                    ON o.customers_id = c.customers_id
                                  WHERE orders_id = '" . (int) $this->orders_id . "'";
            $order_result_sql = tep_db_query($order_select_sql);
            if ($order_result_row = tep_db_fetch_array($order_result_sql)) {
                $requestData = $order_result_row;
                $requestData += [
                    'pm_name' => '',
                    'pg_code' => '',
                    'cc_expiry' => '',
                    'cc_pan' => '',
                    'payer_id' => '',
                    'payer_email' => '',
                    'browser_language' => '',
                    'browser_ua' => '',
                    'browser_js' => '',
                    'browser_flash' => '',
                    'browser_cookie' => '',
                    'browser_referer' => '',
                    'device_id' => '',
                    'device_os' => '',
                    'device_resolution' => '',
                    'device_timezone' => '',
                    'device_firstseen' => '',
                    'true_ip' => '',
                    'true_ip_city' => '',
                    'true_ip_country' => '',
                    'true_ip_lat' => '',
                    'true_ip_long' => '',
                    'true_ip_org' => '',
                    'true_ip_region' => '',
                    'customer_login_ip' => '',
                    'customer_login_date' => '',
                    'kount_fingerprint' => '',
                    'total_amount' => '',
                    'total_usd_amount' => '',
                    'item_1_name' => '',
                    'item_1_amount' => '',
                    'item_1_qty' => '',
                    'item_1_sku' => '',
                    'item_2_name' => '',
                    'item_2_amount' => '',
                    'item_2_qty' => '',
                    'item_2_sku' => '',
                ];
                //get PG info
                if ($requestData['pm_id']) {
                    $pm_select_sql = "  SELECT payment_methods_title
                                        FROM " . TABLE_PAYMENT_METHODS . "
                                        WHERE payment_methods_id = '" . $requestData['pm_id'] . "'";
                    $pm_result_sql = tep_db_query($pm_select_sql);
                    if ($pm_result_row = tep_db_fetch_array($pm_result_sql)) {
                        $requestData['pm_name'] = $pm_result_row["payment_methods_title"];
                    } else {
                        $requestData['pm_name'] = '';
                    }
                }
                unset($requestData['pm_id']);
                if ($requestData['pg_id']) {
                    $pg_select_sql = "  SELECT payment_methods_code, payment_methods_filename
                                        FROM " . TABLE_PAYMENT_METHODS . "
                                        WHERE payment_methods_id = '" . $requestData['pg_id'] . "'";
                    $pg_result_sql = tep_db_query($pg_select_sql);
                    if ($pg_result_row = tep_db_fetch_array($pg_result_sql)) {
                        $requestData['pg_code'] = $pg_result_row["payment_methods_code"];
                        switch ($pg_result_row['payment_methods_filename']) {
                            case 'adyen.php':
                                $adyen_card_number_select_sql = "   SELECT adyen_cc_card_summary, adyen_cc_expiry_date
                                                                    FROM " . TABLE_ADYEN . "
                                                                    WHERE adyen_order_id = '" . (int) $this->orders_id . "'";
                                $adyen_card_number_result_sql = tep_db_query($adyen_card_number_select_sql);
                                if ($adyen_card_number_result_row = tep_db_fetch_array($adyen_card_number_result_sql)) {
                                    if (isset($adyen_card_number_result_row['adyen_cc_card_summary']) && $adyen_card_number_result_row['adyen_cc_expiry_date']) {
                                        $requestData['cc_pan'] = $adyen_card_number_result_row["adyen_cc_card_summary"];
                                        $requestData['cc_expiry'] = $adyen_card_number_result_row["adyen_cc_expiry_date"];
                                    }
                                }
                                break;
                            case 'payU.php':
                                $payu_card_number_select_sql = "   SELECT cc_number, cc_expiration_date
                                                                   FROM " . TABLE_PAYU . "
                                                                   WHERE order_id = '" . (int) $this->orders_id . "'";
                                $payu_card_number_result_sql = tep_db_query($payu_card_number_select_sql);
                                if ($payu_card_number_result_row = tep_db_fetch_array($payu_card_number_result_sql)) {
                                    if (isset($payu_card_number_result_row['cc_number']) && $payu_card_number_result_row['cc_expiration_date']) {
                                        $requestData['cc_pan'] = $payu_card_number_result_row["cc_number"];
                                        $requestData['cc_expiry'] = $payu_card_number_result_row["cc_expiration_date"];
                                    }
                                }
                                break;
                            case 'bibit.php':
                                $bibit_card_number_select_sql = "   SELECT bibit_card_number
                                                                   FROM " . TABLE_BIBIT . "
                                                                   WHERE orders_id = '" . (int) $this->orders_id . "'";
                                $bibit_card_number_result_sql = tep_db_query($bibit_card_number_select_sql);
                                if ($bibit_card_number_result_row = tep_db_fetch_array($bibit_card_number_result_sql)) {
                                    if (isset($bibit_card_number_result_row['bibit_card_number'])) {
                                        $requestData['cc_pan'] = $bibit_card_number_result_row["bibit_card_number"];
                                    }
                                }
                                break;
                            case 'global_collect.php':
                                $gc_card_number_select_sql = "  SELECT global_collect_cc_last_4_digit, global_collect_cc_expiry_date
                                                                FROM " . TABLE_GLOBAL_COLLECT . "
                                                                WHERE global_collect_orders_id = '" . (int) $this->orders_id . "'";
                                $gc_card_number_result_sql = tep_db_query($gc_card_number_select_sql);
                                if ($gc_card_number_result_row = tep_db_fetch_array($gc_card_number_result_sql)) {
                                    if (isset($gc_card_number_result_row['global_collect_cc_last_4_digit']) && $gc_card_number_result_row['global_collect_cc_expiry_date']) {
                                        $requestData['cc_pan'] = $gc_card_number_result_row["global_collect_cc_last_4_digit"];
                                        $requestData['cc_expiry'] = $gc_card_number_result_row["global_collect_cc_expiry_date"];
                                    }
                                }
                                break;
                            case 'paypal.php':
                                $paypal_select_sql = "  SELECT payer_id, payer_email
                                                        FROM " . TABLE_PAYPAL . "
                                                        WHERE invoice = '" . (int) $this->orders_id . "'";
                                $paypal_result_sql = tep_db_query($paypal_select_sql);
                                if ($paypal_result_row = tep_db_fetch_array($paypal_result_sql)) {
                                    $requestData['payer_id'] = $paypal_result_row['payer_id'];
                                    $requestData['payer_email'] = $paypal_result_row['payer_email'];
                                }
                                break;
                            case 'paypalEC.php':
                                $paypal_select_sql = "  SELECT payer_id, payer_email
                                                        FROM " . TABLE_PAYPALEC . "
                                                        WHERE paypal_order_id = '" . (int) $this->orders_id . "'";
                                $paypal_result_sql = tep_db_query($paypal_select_sql);
                                if ($paypal_result_row = tep_db_fetch_array($paypal_result_sql)) {
                                    $requestData['payer_id'] = $paypal_result_row['payer_id'];
                                    $requestData['payer_email'] = $paypal_result_row['payer_email'];
                                }
                                break;
                            case 'moneybookers.php':
                                $mb_select_sql = "  SELECT mb_payer_email
                                                    FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
                                                    WHERE mb_trans_id = '" . (int) $this->orders_id . "'";
                                $mb_result_sql = tep_db_query($mb_select_sql);
                                if ($mb_result_row = tep_db_fetch_array($mb_result_sql)) {
                                    $requestData['payer_email'] = $mb_result_row['mb_payer_email'];
                                }
                                break;
                        }
                    }
                    unset($requestData['pg_id']);
                }
                $kount_select_sql = "  SELECT atb.browser_language as browser_language, atb.browser_string as browser_ua, atb.enabled_js as browser_js, atb.enabled_fl as browser_flash,
                                        atb.enabled_ck as browser_cookie, atb.http_referer as browser_referer, atd.device_id as device_id, atd.os as device_os, atd.screen_res as device_resolution,
                                        atd.time_zone as device_timezone, atd.device_first_seen as device_firstseen, atti2.true_ip as true_ip,
                                        atti2.true_ip_city as true_ip_city, atti2.true_ip_geo as true_ip_country, atti2.true_ip_latitude as true_ip_lat, atti2.true_ip_longitude as true_ip_long,
                                        atti2.true_ip_organization as true_ip_org, atti2.true_ip_region as true_ip_region, atti.customers_login_ip as customer_login_ip,
                                        atti.customers_login_date as customer_login_date, atti.device_id as kount_fingerprint
                                        FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . " AS atti
                                        LEFT JOIN " . TABLE_API_TM_BROWSER . " AS atb
                                        ON atti.api_tm_query_id = atb.api_tm_query_id
                                        LEFT JOIN " . TABLE_API_TM_DEVICE . " AS atd
                                        ON atti.api_tm_query_id = atd.api_tm_query_id
                                        LEFT JOIN " . TABLE_API_TM_TRUE_IP . " atti2
                                        ON atti.api_tm_query_id = atti2.api_tm_query_id
                                        WHERE atti.transaction_id = '" . (int) $this->orders_id . "'";
                $kount_result_sql = tep_db_query($kount_select_sql);
                if ($kount_result_row = tep_db_fetch_array($kount_result_sql)) {
                    foreach ($kount_result_row as $key => $val) {
                        $requestData[$key] = $val;
                    }
                }

                //subtotal
                $totals_select_sql = "  SELECT text, value
                                        FROM " . TABLE_ORDERS_TOTAL . "
                                        WHERE orders_id = '" . (int) $this->orders_id . "'
                                        AND class = 'ot_total'";
                $totals_result_sql = tep_db_query($totals_select_sql);
                if ($totals_result_row = tep_db_fetch_array($totals_result_sql)) {
                    $requestData['total_amount'] = preg_replace('/[^0-9"."]/', '', strip_tags($totals_result_row['text']));
                    $requestData['total_usd_amount'] = $totals_result_row['value'];
                }

                //shopping cart
                $products = array();
                $orders_products_select_sql = "     SELECT products_quantity, products_id, products_name, final_price
                                                    FROM " . TABLE_ORDERS_PRODUCTS . "
                                                    WHERE orders_id = '" . (int) $this->orders_id . "'
                                                    AND products_bundle_id = 0";
                $orders_products_result_sql = tep_db_query($orders_products_select_sql);
                while ($orders_products = tep_db_fetch_array($orders_products_result_sql)) {
                    $products[] = array(
                        'qty' => $orders_products['products_quantity'],
                        'id' => $orders_products['products_id'],
                        'name' => $orders_products['products_name'],
                        'final_price' => $orders_products['final_price'],
                    );
                }

                foreach ($products as $index => $val) {
                    if ($index <= 1) {
                        $indexes = $index + 1;
                        $requestData['item_' . $indexes . '_name'] = $val['name'];
                        $requestData['item_' . $indexes . '_amount'] = $val['final_price'];
                        $requestData['item_' . $indexes . '_qty'] = $val['qty'];
                        $requestData['item_' . $indexes . '_sku'] = $val['id'];
                    }
                }
                $timestamp = time();
                $array = array(
                    'action' => 'Predict',
                    'api_key' => PIPWAVE_MERCHANT_API_KEY,
                    'api_secret' => PIPWAVE_MERCHANT_API_SECRET,
                    'timestamp' => $timestamp,
                    'version' => PIPWAVE_MERCHANT_API_VERSION
                );
                $s = '';
                foreach ($array as $key => $value) {
                    $s .= $key . ':' . $value;
                }
                $requestData['signature'] = sha1($s);
                $requestData += $array;

                $json_data = json_encode($requestData);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, PIPWAVE_MERCHANT_API_URL);
                curl_setopt($ch, CURLOPT_VERBOSE, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($json_data))
                );
                $predictResult = curl_exec($ch);
                if ($predictResult) {
                    $predictResult = json_decode($predictResult, 1);
                    if (isset($predictResult['status']) && $predictResult['status'] == 200) {
                        if (isset($predictResult['result']['predictionPercent']) && isset($predictResult['result']['predictionLabel'])) {
                            $prediction_result_sql_array = array(
                                'order_id' => $this->orders_id,
                                'result_label' => $predictResult['result']['predictionLabel'],
                                'result_score' => $predictResult['result']['predictionPercent'],
                                'version' => PIPWAVE_MERCHANT_API_VERSION,
                                'data' => json_encode($predictResult),
                                'date_added' => 'now()',
                                'date_modified' => 'now()'
                            );
                            tep_db_perform(TABLE_PIPWAVE_PREDICTION, $prediction_result_sql_array);
                        }
                    } else {
                        if (isset($predictResult['message']) && !empty($predictResult['message'])) {
                            return array('status' => $predictResult['status'], 'message' => $predictResult['message']);
                        }
                    }
                }
            }
        }
    }

    function is_pipwave_order() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->checkoutSite) && $this->pw_obj->checkoutSite == 'pipwave') {
            $this->execute_log .= "is_pipwave_order(): true\n";
            return true;
        } else {
            $this->execute_log .= "is_pipwave_order(): false\n";
            return false;
        }
    }

    function get_pipwave_payment_method_code() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->payment_method_code)) {
            $this->execute_log .= "get_pipwave_payment_method_code(): " . $this->pw_obj->payment_method_code . "\n";
            return $this->pw_obj->payment_method_code;
        } else {
            $this->execute_log .= "get_pipwave_payment_method_code(): null\n";
            return null;
        }
    }

    function get_pipwave_pg_code() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->payment_method_code)) {
            $pmInfo = $this->pw_obj->pipwavePaymentMapper('pipwave', $this->pw_obj->payment_method_code);
            if (isset($pmInfo['pg_code'])) {
                $this->execute_log .= "get_pipwave_pg_code(): " . $pmInfo['pg_code'] . "\n";
                return $pmInfo['pg_code'];
            } else {
                $this->execute_log .= "get_pipwave_pg_code(): null\n";
                return null;
            }
        } else {
            $this->execute_log .= "get_pipwave_pg_code(): null\n";
            return null;
        }
    }

    function get_pipwave_aft_score() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->pipwave_score)) {
            $this->execute_log .= "get_pipwave_aft_score(): " . $this->pw_obj->pipwave_score . "\n";
            return $this->pw_obj->pipwave_score;
        } else {
            $this->execute_log .= "get_pipwave_aft_score(): null\n";
            return null;
        }
    }

    function get_pipwave_aft_result() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->rules_action)) {
            $this->execute_log .= "get_pipwave_aft_result(): " . $this->pw_obj->rules_action . "\n";
            return $this->pw_obj->rules_action;
        } else {
            $this->execute_log .= "get_pipwave_aft_result(): null\n";
            return null;
        }
    }

    function get_pipwave_require_capture() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->require_capture)) {
            $boolean = ($this->pw_obj->require_capture == 1) ? true : false;
            $result = ($boolean) ? 'true' : 'false';
            $this->execute_log .= "get_pipwave_require_capture(): " . $result . "\n";
            return $boolean;
        } else {
            $this->execute_log .= "get_pipwave_require_capture(): null\n";
            return null;
        }
    }

    function get_pipwave_reversible_payment() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->reversible_payment)) {
            $boolean = ($this->pw_obj->reversible_payment == 1 || $this->pw_obj->reversible_payment == 2) ? true : false;
            $result = ($boolean) ? 'true' : 'false';
            $this->execute_log .= "get_pipwave_reversible_payment(): " . $result . "\n";
            return $boolean;
        } else {
            $this->execute_log .= "get_pipwave_reversible_payment(): null\n";
            return null;
        }
    }

    function get_pipwave_mobile_verified() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->mobile_number_verification)) {
            $boolean = ($this->pw_obj->mobile_number_verification == 1) ? true : false;
            $result = ($boolean) ? 'true' : 'false';
            $this->execute_log .= "get_pipwave_mobile_verified(): " . $result . "\n";
            return $boolean;
        } else {
            $this->execute_log .= "get_pipwave_mobile_verified(): null\n";
            return null;
        }
    }

    function get_pipwave_risk_service_type() {
        if (!is_object($this->pw_obj)) {
            $this->pw_obj = new pipwave($this->orders_id);
        }
        if (isset($this->pw_obj->risk_service_type)) {
            $this->execute_log .= "get_pipwave_risk_service_type(): " . $this->pw_obj->risk_service_type . "\n";
            return $this->pw_obj->risk_service_type;
        } else {
            $this->execute_log .= "get_pipwave_risk_service_type(): null\n";
            return null;
        }
    }

}

?>