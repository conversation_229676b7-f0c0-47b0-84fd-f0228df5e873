<?php

class api_replenish_publishers {

    var     $pass_id, 
            $publishers_id, 
            $api_report_paging, 
            $api_list_limit,
            $api_method,
            $api_provider,
            $api_list_paging;

    function api_replenish_publishers($pass_id = '') {
        $this->publishers_id = (int) $pass_id;
        $this->api_report_paging = 500;
        $this->api_list_paging = 500;
        $this->api_list_limit = 3000;
        
        if ($pass_id) {
            $publishers_select_sql = "SELECT publishers_api_method, publishers_api_provider
                                    FROM " . TABLE_PUBLISHERS_REPLENISH . "
                                    WHERE publishers_replenish_id = '" . $this->publishers_id . "'";
            $publishers_result_sql = tep_db_query($publishers_select_sql);
            if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
                $this->api_method = $publishers_row['publishers_api_method'];
                $this->api_provider = $publishers_row['publishers_api_provider'];
            }
        }
    }

    function get_publishers() {
        $publishers_array = array();
        $publishers_select_sql = "SELECT * 
                                  FROM " . TABLE_PUBLISHERS_REPLENISH . "
                                  WHERE publishers_replenish_id = '" . $this->publishers_id . "'";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_replenish_id']] = $publishers_row;
        }
        return $publishers_array;
    }

    function get_all_publishers() {
        $publishers_array = array();
        $publishers_select_sql = "SELECT * FROM " . TABLE_PUBLISHERS_REPLENISH;
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_replenish_id']] = $publishers_row;
        }
        return $publishers_array;
    }
    
    function get_all_publishers_unlocked() {
        $publishers_array = array();
        $publishers_select_sql = "  SELECT p.publishers_replenish_id, p.publishers_name, p.publishers_status, 
                                    ps.po_supplier_locked_by, a.admin_email_address
                                    FROM " . TABLE_PUBLISHERS_REPLENISH . " AS p
                                    INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
                                        ON (p.publishers_supplier_id = ps.po_suppliers_id 
                                        AND ps.po_supplier_status='1')
                                    LEFT JOIN " . TABLE_ADMIN . " AS a
                                        ON ps.po_supplier_locked_by = a.admin_id
                                    ";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_replenish_id']] = $publishers_row;
        }
        return $publishers_array;
    }
    
    function get_all_api_provider($pubb_id = '') {
        // Get current API Provider if publishers_id is set
        $where_publisher = '';
        if ($pubb_id) {
            $pub_provider_sql = "SELECT publishers_api_provider FROM " . TABLE_PUBLISHERS_REPLENISH . " WHERE publishers_replenish_id = " . (int)$pubb_id;
            $pub_provider_result = tep_db_query($pub_provider_sql);
            if ($pub_provider_row = tep_db_fetch_array($pub_provider_result)) {
                $where_publisher = "WHERE publishers_api_provider != '" . $pub_provider_row['publishers_api_provider'] . "'";
            }
        }
        
        // Get API Provider list that is not set for any publishers.
        $provider_array = array(array('id' => '', 'text' => 'API Provider...'));
        $provider_sql = "SELECT api_provider
                         FROM " . TABLE_LOG_API_RESTOCK . "
                         WHERE 
                            api_provider NOT IN (
                                SELECT publishers_api_provider 
                                FROM " . TABLE_PUBLISHERS_REPLENISH . "
                                " . $where_publisher . " 
                                GROUP BY publishers_api_provider
                            )
                         GROUP BY api_provider";
        $provider_result = tep_db_query($provider_sql);
        while ($provider_row = tep_db_fetch_array($provider_result)) {
            if (!empty($provider_row['api_provider'])) {
                $provider_array[] = array('id' => $provider_row['api_provider'], 'text' => $provider_row['api_provider']);
            }
        }
        return $provider_array;
    }
    
    function get_all_api_method() {
        $method_array = array(array('id' => '', 'text' => 'API Method...'));
        $method_sql = "SELECT method FROM " . TABLE_LOG_API_RESTOCK . " GROUP BY method";
        $method_result = tep_db_query($method_sql);
        while ($method_row = tep_db_fetch_array($method_result)) {
            if (!empty($method_row['method'])) {
                $method_array[] = array('id' => $method_row['method'], 'text' => $method_row['method']);
            }
        }
        return $method_array;
    }
    
    function is_po_supplier_item($cdkey_id) {
        $api_check_sql = "SELECT publishers_id FROM " . TABLE_LOG_API_RESTOCK . " WHERE custom_products_code_id = '" . (int)$cdkey_id . "'";
        $api_check_result = tep_db_query($api_check_sql);
        $api_check_row = tep_db_fetch_array($api_check_result);
        
        if($api_check_row['publishers_id'] == $this->publishers_id) {
            return true;
        } else {
            return false;
        }
    }

    function download_csv($publisher_id, $start_date, $end_date) {
        $array_list = array();
        // get api_provider
        $api_sql = "SELECT publishers_api_provider, publishers_api_method FROM " . TABLE_PUBLISHERS_REPLENISH . " WHERE publishers_replenish_id = '" . (int)$publisher_id . "'";
        $api_result = tep_db_query($api_sql);
        $api_row = tep_db_fetch_array($api_result);
        
        $steam_sales_select_sql = " SELECT lar.id, lar.custom_products_code_id, lar.sku, lar.amount, lar.settle_amount, lar.serialnumber, lar.flag_state, lar.error_msg, 
                                        lar.created_datetime, op.parent_orders_products_id, op.final_price, op.orders_products_store_price
                                    FROM " . TABLE_LOG_API_RESTOCK . " as lar
                                    INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc 
                                        ON lar.custom_products_code_id = cpc.custom_products_code_id
                                    LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                        ON cpc.orders_products_id = op.orders_products_id
                                    WHERE lar.method = '" . $api_row['publishers_api_method'] . "' 
                                        AND lar.api_provider = '" . $api_row['publishers_api_provider'] . "'
                                        AND lar.created_datetime >= '" . $start_date . " 00:00:00'
                                        AND lar.created_datetime < '" . $end_date . " 00:00:00'
        ";
        $steam_sales_result_sql = tep_db_query($steam_sales_select_sql);

        while ($steam_sales_row = tep_db_fetch_array($steam_sales_result_sql)) {
            $srp_price = $steam_sales_row['final_price'];
            
            if ($steam_sales_row['parent_orders_products_id'] > 0) {
                if (!isset($package_price_array[$steam_sales_row['parent_orders_products_id']])) {
                    $subproduct_info_select_sql = " SELECT SUM(orders_products_store_price * products_quantity) AS total
                                                    FROM " . TABLE_ORDERS_PRODUCTS . "
                                                    WHERE parent_orders_products_id = '" . $steam_sales_row['parent_orders_products_id'] . "'";
                    $subproduct_info_result_sql = tep_db_query($subproduct_info_select_sql);

                    if ($subproduct_info_row = tep_db_fetch_array($subproduct_info_result_sql)) {
                        $package_price_array[$steam_sales_row['parent_orders_products_id']]['total'] = $subproduct_info_row['total'];
                    } else {
                        $package_price_array[$steam_sales_row['parent_orders_products_id']]['total'] = 0;
                    }
                    
                    $bundle_product_info_select_sql = " SELECT (final_price * products_quantity) AS total
                                                        FROM " . TABLE_ORDERS_PRODUCTS . "
                                                        WHERE orders_products_id = '" . $steam_sales_row['parent_orders_products_id'] . "'";
                    $bundle_product_info_result_sql = tep_db_query($bundle_product_info_select_sql);

                    if ($bundle_product_info_row = tep_db_fetch_array($bundle_product_info_result_sql)) {
                        $package_price_array[$steam_sales_row['parent_orders_products_id']]['paid_amount'] = $bundle_product_info_row['total'];
                    } else {
                        $package_price_array[$steam_sales_row['parent_orders_products_id']]['paid_amount'] = 0;
                    }
                    
                    $srp_price = ($package_price_array[$steam_sales_row['parent_orders_products_id']]['paid_amount'] / $package_price_array[$steam_sales_row['parent_orders_products_id']]['total']) * $steam_sales_row['orders_products_store_price'];
                } else {
                    $srp_price = ($package_price_array[$steam_sales_row['parent_orders_products_id']]['paid_amount'] / $package_price_array[$steam_sales_row['parent_orders_products_id']]['total']) * $steam_sales_row['orders_products_store_price'];
                }
            }
            
            $array_list[] = $steam_sales_row['id'] . ',"' . $steam_sales_row['custom_products_code_id'] . '","' . $steam_sales_row['sku'] . '","' . $steam_sales_row['amount'] . '","' . $steam_sales_row['settle_amount'] . '","' . $steam_sales_row['serialnumber'] . '","' . $steam_sales_row['flag_state'] . '","' . $steam_sales_row['error_msg'] . '","' . $steam_sales_row['created_datetime'] . '","' . $srp_price . '"';
        }

        return $array_list;
    }
            
    function upload_csv_matching($upload_csv, $publisher_id) {
        $untracked_cdkey = array();
        $this->set_api_temp_status_empty($publisher_id);
        // get api_provider
        $api_sql = "SELECT publishers_api_provider FROM " . TABLE_PUBLISHERS_REPLENISH . " WHERE publishers_replenish_id = '" . (int)$publisher_id . "'";
        $api_result = tep_db_query($api_sql);
        $api_row = tep_db_fetch_array($api_result);
        $file = fopen($upload_csv['upload_file']['tmp_name'], 'r');
        while (($line = fgetcsv($file)) !== FALSE) {
            foreach($line as $api_id) {
                switch ($api_row['publishers_api_provider']) {
                    case 'VTC':
                        $api_check_sql = "SELECT id FROM " . TABLE_LOG_API_RESTOCK . " WHERE id = '" . tep_db_prepare_input($api_id) . "' AND api_provider = 'VTC'";
                        $api_check_result = tep_db_query($api_check_sql);
                        if ($api_check_row = tep_db_fetch_array($api_check_result)) {
                            $check_array = array(
                                'api_withdrawal_temp_status' => '1',
                                'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
                            );
                            tep_db_perform(TABLE_LOG_API_RESTOCK, $check_array, 'update', "id = " . tep_db_prepare_input($api_id));
                        } else {
                            $untracked_cdkey[] = $api_id;
                        }
                        break;
                    case 'OFCARD':
                        $api_check_sql = "SELECT id FROM " . TABLE_LOG_API_RESTOCK . " WHERE serilanumber LIKE '" . tep_db_prepare_input($api_id) . "%' AND api_provider = 'OFCARD'";
                        $api_check_result = tep_db_query($api_check_sql);
                        if ($api_check_row = tep_db_fetch_array($api_check_result)) {
                            $check_array = array(
                                'api_withdrawal_temp_status' => '1',
                                'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
                            );
                            tep_db_perform(TABLE_LOG_API_RESTOCK, $check_array, 'update', "serialnumber LIKE '" . tep_db_prepare_input($api_id) . "%'");
                        } else {
                            $untracked_cdkey[] = $api_id;
                        }
                        break;
                }
            }
        }
        fclose($file);
        
        return $untracked_cdkey;
    }
                
    function set_api_temp_status($cdkey_id, $set_cb = '') {
        if ($set_cb) {
            $update_col = 'api_cb_temp_status';
            $temp_value = (string) $set_cb;
        } else {
            $update_col = 'api_withdrawal_temp_status';
            $temp_value = '1';
        }
        $check_array = array(
            $update_col => $temp_value,
            'changed_by' => tep_db_prepare_input($_SESSION['login_email_address'])
        );
        tep_db_perform(TABLE_LOG_API_RESTOCK, $check_array, 'update', "custom_products_code_id = " . $cdkey_id);
    }
    
    function set_api_temp_status_empty($publisher_id = '', $set_cb = '') {
        $update_col = ($set_cb) ? 'api_cb_temp_status' : 'api_withdrawal_temp_status';
        if ($publisher_id) {
            $where_col = "publishers_id = '" . $publisher_id . "'";
        } else {
            $where_col = "publishers_id = '" . $this->publishers_id . "'";
        }
        $check_array = array($update_col => '0');
        tep_db_perform(TABLE_LOG_API_RESTOCK, $check_array, 'update', $where_col);
    }
            
    function get_api_withdrawal_status($cdkey_id) {
        $cdkey_withdrawal_sql = "SELECT lar.custom_products_code_id, lar.api_withdrawal_status
                                FROM " . TABLE_LOG_API_RESTOCK . " AS lar
                                WHERE lar.custom_products_code_id = '" . (int)$cdkey_id . "'";
        $cdkey_withdrawal_result = tep_db_query($cdkey_withdrawal_sql);
        if ($cdkey_withdrawal_row = tep_db_fetch_array($cdkey_withdrawal_result)) {
            $cdkey_withdrawal_status = $cdkey_withdrawal_row['api_withdrawal_status'];
        }
        return $cdkey_withdrawal_status;
        
    }
    
    function get_api_cdkey_status_temp($publishers_id) {
        $api_cdkey_array = array();        
        $api_status_sql = " SELECT custom_products_code_id, api_cb_status
                            FROM " . TABLE_LOG_API_RESTOCK . "
                            WHERE publishers_id = '" . (int)$publishers_id . "'
                                AND api_withdrawal_temp_status = '1'";
        $api_status_result = tep_db_query($api_status_sql);
        while ($api_status_row = tep_db_fetch_array($api_status_result)) {
            $api_cdkey_array[] = $api_status_row;
        }
        return $api_cdkey_array;
    }
            
    function get_api_po_final_price($po_id, $products_id) {
        $api_price_sql = "SELECT po.purchase_orders_id, po.purchase_orders_gst_value, pop.products_id,
                          pop.products_unit_price_type, pop.products_unit_price_value, pop.products_selling_price 
                          FROM " . TABLE_PURCHASE_ORDERS . " AS po
                          LEFT JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop
                            ON po.purchase_orders_id = pop.purchase_orders_id 
                            AND pop.products_id = '" . (int)$products_id . "' 
                          WHERE po.purchase_orders_id = '" . (int)$po_id . "'";
        
        $api_price_result = tep_db_query($api_price_sql);
        if ($api_price_row = tep_db_fetch_array($api_price_result)) {
            return $api_price_row;
        } else {
            return false;
        }
        
    }
    
    function get_cdkey_prices_info($cdkey_id, $products_id) {
        $base_price_info_select_sql = " SELECT currency_settle_amount, currency_code
                                        FROM " . TABLE_LOG_API_RESTOCK . "
                                        WHERE custom_products_code_id = '" . tep_db_input($cdkey_id) . "'";
        $base_price_info_result_sql = tep_db_query($base_price_info_select_sql);
        if ($base_price_info_row = tep_db_fetch_array($base_price_info_result_sql)) {
            if (!empty($base_price_info_row['currency_settle_amount']) && !empty($base_price_info_row['currency_code'])) {
                $product_price_array = array(
                    'price' => $base_price_info_row['currency_settle_amount'],
                    'base_cur' => $base_price_info_row['currency_code']
                );
            } else {
                $products_cost_sql = " SELECT products_cost, products_currency
                                                FROM " . TABLE_PRODUCTS_COST . "
                                                WHERE products_id = '" . tep_db_input($products_id) . "'";
                $products_cost_result = tep_db_query($products_cost_sql);
                $products_cost_row = tep_db_fetch_array($products_cost_result);
                $product_price_array = array(
                    'price' => $products_cost_row['products_cost'],
                    'base_cur' => $products_cost_row['products_currency']
                );
            }
        }

        return $product_price_array;
    }
    
    function get_products_id($cdkey_id) {
        $prod_id_sql = " SELECT products_id
                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                        WHERE custom_products_code_id = '" . tep_db_input($cdkey_id) . "'";
        $prod_id_result = tep_db_query($prod_id_sql);
        $prod_id_row = tep_db_fetch_array($prod_id_result);

        return $prod_id_row['products_id'];
    }
            
    function get_api_items($comma_list) {
        $cdkey_array = array();
        $select_qry = " lar.custom_products_code_id, lar.sku, lar.amount, lar.settle_amount, lar.serialnumber, 
                        lar.flag_state, lar.error_msg, lar.created_datetime, op.parent_orders_products_id, op.final_price, 
                        op.orders_products_store_price, op.orders_id, cpc.products_id, pd.products_name,
                        lar.purchase_orders_id, lar.api_withdrawal_ref_id as withdrawal_ref, 
                        lar.api_withdrawal_status as withdrawal_status, lar.api_withdrawal_temp_status as withdrawal_temp_status, 
                        lar.api_cb_status as cb_status, lar.api_cb_temp_status as cb_temp_status,
                        lar.api_cb_deduction_po_id as cb_deduction_po_id, lar.api_cb_deduction_status as cb_deduction_status";
        
        $cdkey_sql = " SELECT " . $select_qry . "
                        FROM " . TABLE_LOG_API_RESTOCK . " AS lar
                        INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                            ON lar.custom_products_code_id = cpc.custom_products_code_id
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON cpc.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                            ON cpc.orders_products_id = op.orders_products_id
                        WHERE lar.custom_products_code_id IN (" . $comma_list . ")";
        
        $cdkey_result = tep_db_query($cdkey_sql);
        
        while ($cdkey_row = tep_db_fetch_array($cdkey_result)) {
            $cdkey_array[$cdkey_row['custom_products_code_id']] = $cdkey_row;
        }
        
        return $cdkey_array;
    }
            
    function get_api_list($api_list_type, $cdk_id, $start_date, $end_date, $page, $sort_by, $sort_option) {
        $api_array = array();
        $list_limit = $this->api_list_limit;
        // How many items to list per page
        $limit = $this->api_list_paging;
        // Calculate the offset for the query
        $offset = ($page - 1) * $limit;
        
        for($i = 0; $i < 2; $i++) {
            if($api_list_type == 'add_po_cb' && $i == 0) {
                continue;
            }
            $withdrawal_status = '';
            $date_range = '';
            if($i == 0) {
                $withdrawal_status = "AND (lar.api_withdrawal_status = '0' OR lar.api_withdrawal_status = '3' OR lar.api_cb_status = '1' OR lar.api_cb_status = '2')";
                $date_range = "AND lar.created_datetime < '" . $start_date . " 00:00:00'";
            } else {
                $date_range = "AND lar.created_datetime >= '" . $start_date . " 00:00:00' AND lar.created_datetime <= '" . $end_date . " 23:59:59'";
            }
            
            $api_result = $this->api_list_query('list', $cdk_id, $date_range, $withdrawal_status, $list_limit, $limit, $offset, $sort_by, $sort_option);
            
            $list_limit = $list_limit - tep_db_num_rows($api_result);
            
            while ($api_row = tep_db_fetch_array($api_result)) {
                $api_array[$api_row['custom_products_code_id']] = $api_row;
            }
        }
        return $api_array;
    }
    
    function get_api_paging($api_list_type, $cdk_id, $start_date, $end_date) {
        $pages = array();
        $list_limit = $this->api_list_limit;
        
        $pages['total'] = 0;
        $pages['pages'] = 0;
        
        for($i = 0; $i < 2; $i++) {
            if($api_list_type == 'add_po_cb' && $i == 0) {
                continue;
            }
            $withdrawal_status = '';
            $date_range = '';
            if($i == 0) {
                $withdrawal_status = "AND (lar.api_withdrawal_status = '0' OR lar.api_withdrawal_status = '3' OR lar.api_cb_status = '1' OR lar.api_cb_status = '2')";
                $date_range = "AND lar.created_datetime < '" . $start_date . " 00:00:00'";
            } else {
                $date_range = "AND lar.created_datetime >= '" . $start_date . " 00:00:00' AND lar.created_datetime <= '" . $end_date . " 23:59:59'";
            }
            
            $api_result = $this->api_list_query('pages', $cdk_id, $date_range, $withdrawal_status, $list_limit, '', '', '', '');
            
            $list_limit = $list_limit - tep_db_num_rows($api_result);
            
            if ($api_row = tep_db_fetch_array($api_result)) {
                // How many pages will there be
                $pages['total'] = $pages['total'] + $api_row['total_api'];
            }
        }
        
        $pages['pages'] = ceil($pages['total'] / $this->api_list_paging);
        
        return $pages;
    }
    
    function api_list_query($query_type, $cdk_id, $date_range, $withdrawal_status, $list_limit, $limit, $offset, $sort_by, $sort_option) {
        $cdkey_qry = (empty($cdk_id)) ? '' : 'AND lar.custom_products_code_id = ' . (int)$cdk_id;
        if ($query_type == 'pages') {
            $select_qry = "count(lar.custom_products_code_id) AS total_api";
            $limit_qry = "LIMIT " . $list_limit;
            $offset_qry = "";
            $order_by_qry = "";
        } else {
            switch ($sort_by) {
                case 'cdkey_date':
                    $sort_by_qry = "lar.created_datetime";
                    break;
                case 'prod_id':
                    $sort_by_qry = "cpc.products_id";
                    break;
                case 'prod_name':
                    $sort_by_qry = "pd.products_name";
                    break;
                case 'order_num':
                    $sort_by_qry = "op.orders_id";
                    break;
                case 'cdkey_id':
                    $sort_by_qry = "lar.custom_products_code_id";
                    break;
            }
            $select_qry = "lar.custom_products_code_id, lar.sku, lar.amount, lar.settle_amount, lar.serialnumber, 
                           lar.flag_state, lar.error_msg, lar.created_datetime, op.parent_orders_products_id, op.final_price, 
                           op.orders_products_store_price, op.orders_id, cpc.products_id,
                           lar.purchase_orders_id, lar.api_withdrawal_ref_id as withdrawal_ref, 
                           lar.api_withdrawal_status as withdrawal_status, lar.api_withdrawal_temp_status as withdrawal_temp_status, 
                           lar.api_cb_status as cb_status, lar.api_cb_temp_status as cb_temp_status,
                           lar.api_cb_deduction_po_id as cb_deduction_po_id, lar.api_cb_deduction_status as cb_deduction_status";
            $limit_qry = "LIMIT " . $limit;
            $offset_qry = "OFFSET " . $offset;
            $order_by_qry = "ORDER BY " . $sort_by_qry . " " . $sort_option;
        }
        
        $api_sql = "SELECT " . $select_qry . "
                        FROM " . TABLE_LOG_API_RESTOCK . " AS lar
                        INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                            ON lar.custom_products_code_id = cpc.custom_products_code_id
                        LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                            ON cpc.orders_products_id = op.orders_products_id
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON cpc.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        WHERE lar.method = '" . $this->api_method . "'
                            AND lar.api_provider = '" . $this->api_provider . "'
                            AND lar.flag_state = 'S'
                            AND lar.custom_products_code_id != '0'
                            " . $withdrawal_status . "
                            " . $date_range . "
                            " . $cdkey_qry . "
                        " . $order_by_qry . "
                        " . $limit_qry . "
                        " . $offset_qry;
        $api_result = tep_db_query($api_sql);
        
        return $api_result;
    }
            
    function get_api_list_report($api_list_type, $product_id, $order_id, $cdkey_id, $start_date, $end_date, $page, $sort_by, $sort_option) {
        $api_array = array();
        $sort_array = array('sort_by' => $sort_by, 'sort_option' => $sort_option);
        
        // How many items to list per page
        $limit = $this->api_report_paging;
        
        // Calculate the offset for the query
        $offset = ($page - 1) * $limit;
        
        $withdrawal_status = $this->withdrawal_status_query($api_list_type);
        
        $api_result = $this->api_reports_query('list', $product_id, $order_id, $cdkey_id, $start_date, $end_date, $withdrawal_status, $limit, $offset, $sort_array);
        
        while ($api_row = tep_db_fetch_array($api_result)) {
            $api_array[$api_row['custom_products_code_id']] = $api_row;
        }
        
        return $api_array;
    }
    
    function get_api_paging_report($api_list_type, $product_id, $order_id, $cdkey_id, $start_date, $end_date) {
        $pages = array();
        
        $withdrawal_status = $this->withdrawal_status_query($api_list_type);
        
        $api_result = $this->api_reports_query('pages', $product_id, $order_id, $cdkey_id, $start_date, $end_date, $withdrawal_status, '', '');
        
        if ($api_row = tep_db_fetch_array($api_result)) {
            // How many pages will there be
            $pages['total'] = $api_row['total_api'];
            $pages['pages'] = ceil($api_row['total_api'] / $this->api_report_paging);
        }
        
        return $pages;
    }
    
    function withdrawal_status_query($api_list_type) {
        switch ($api_list_type) {
            case 'unpaid':
                $withdrawal_status = "AND (lar.api_withdrawal_status = '0' OR lar.api_withdrawal_status = '3') AND lar.api_cb_status = '0'";
                break;
            case 'paid':
                $withdrawal_status = "AND (lar.api_withdrawal_status = '2' OR lar.api_withdrawal_status = '4') AND lar.api_cb_status = '0'";
                break;
            case 'charge_back':
                $withdrawal_status = "AND (lar.api_cb_status = '1')";
                break;
            case 'debit_note':
                $withdrawal_status = "AND (lar.api_cb_status = '4')";
                break;
            case 'api_issue':
                $withdrawal_status = "AND (lar.api_cb_status = '2')";
                break;
            case 'api_removed':
                $withdrawal_status = "AND (lar.api_cb_status = '3')";
                break;
            default :
                $withdrawal_status = '';
                break;
        }
        
        return $withdrawal_status;
    }

    function api_reports_query($query_type, $product_id, $order_id, $cdkey_id, $start_date, $end_date, $withdrawal_status, $limit, $offset, $sort_array = array()) {
        $sort_option = $sort_array['sort_option'];
        if ($query_type == 'pages') {
            $select_qry = "count(lar.custom_products_code_id) AS total_api";
            $order_by_qry = "";
            $limit_qry = "";
            $offset_qry = "";
        } else {
            switch ($sort_array['sort_by']) {
                case 'cdkey_date':
                    $sort_by_qry = "lar.created_datetime";
                    break;
                case 'prod_id':
                    $sort_by_qry = "cpc.products_id";
                    break;
                case 'pub_name':
                    $sort_by_qry = "pu.publishers_name";
                    break;
                case 'prod_name':
                    $sort_by_qry = "pd.products_name";
                    break;
                case 'order_num':
                    $sort_by_qry = "op.orders_id";
                    break;
                case 'cdkey_id':
                    $sort_by_qry = "lar.custom_products_code_id";
                    break;
            }
            $select_qry = "lar.custom_products_code_id, lar.sku, lar.amount, lar.settle_amount, lar.serialnumber, 
                           lar.flag_state, lar.error_msg, lar.created_datetime, op.parent_orders_products_id, op.final_price, 
                           op.orders_products_store_price, op.orders_id, cpc.products_id, pu.publishers_name,
                           lar.purchase_orders_id, lar.api_withdrawal_ref_id as withdrawal_ref, pd.products_name,
                           lar.api_withdrawal_status as withdrawal_status, lar.api_withdrawal_temp_status as withdrawal_temp_status, 
                           lar.api_cb_status as cb_status, lar.api_cb_temp_status as cb_temp_status,
                           lar.api_cb_deduction_po_id as cb_deduction_po_id, lar.api_cb_deduction_status as cb_deduction_status";
            $order_by_qry = "ORDER BY " . $sort_by_qry . " " . $sort_option;
            $limit_qry = "LIMIT " . $limit;
            $offset_qry = "OFFSET " . $offset;
        }
        
        if ($this->publishers_id == 0) {
            $method_array = array();
            $api_provider_array = array();
            $publisher_array = $this->get_all_publishers();
            foreach ($publisher_array as $publishers) {
                $method_array[] = $publishers['publishers_api_method'];
                $api_provider_array[] = $publishers['publishers_api_provider'];
            }
            $method_comma_list = implode("', '", $method_array);
            $api_provider_comma_list = implode("', '", $api_provider_array);
            $publisher_qry = "lar.method IN ('" . $method_comma_list . "') AND lar.api_provider IN ('" . $api_provider_comma_list . "')";
        } else {
            $publisher_qry = "lar.publishers_id = '" . $this->publishers_id . "' AND lar.method = '" . $this->api_method . "' AND lar.api_provider = '" . $this->api_provider . "'";
        }
        
        $cdkey_qry = (empty($cdkey_id)) ? '' : 'AND cpc.custom_products_code_id = ' . (int)$cdkey_id;
        $product_qry = (empty($product_id)) ? '' : 'AND cpc.products_id = ' . (int)$product_id;
        $order_qry = (empty($order_id)) ? '' : 'AND op.orders_id = ' . (int)$order_id;
        
        $top_up_sql = "SELECT " . $select_qry . "
                        FROM " . TABLE_LOG_API_RESTOCK . " AS lar
                        INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                            ON lar.custom_products_code_id = cpc.custom_products_code_id
                            " . $cdkey_qry . "
                        LEFT OUTER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                            ON cpc.orders_products_id = op.orders_products_id
                        INNER JOIN " . TABLE_PUBLISHERS_REPLENISH . " AS pu
                            ON lar.publishers_id = pu.publishers_replenish_id
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON cpc.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        WHERE 
                            " . $publisher_qry . "
                            " . $withdrawal_status . "
                            AND lar.custom_products_code_id != '0'
                            AND lar.created_datetime >= '" . $start_date . " 00:00:00' AND lar.created_datetime <= '" . $end_date . " 23:59:59'
                            " . $order_qry . "
                            " . $product_qry . "
                            AND lar.flag_state = 'S'
                        " . $order_by_qry . "
                        " . $limit_qry . "
                        " . $offset_qry;
        $top_up_result = tep_db_query($top_up_sql);
        
        return $top_up_result;
    }
}

?>