<?php

class auto_delivery {

    const DEFAULT_LOG_IDENTITY = 'system';
    const DEFAULT_ADMIN_ID = 0;
    const DEFAULT_ADMIN_EMAIL = 'system';
    const MOVE_ORDER = 'PROCESS_MOVING_ORDER';

    private $lock_obj,
            $order_obj,
            $fully_delivered_desc;
    public $product_info;

    public function __construct() {
        global $language, $currencies, $log_object, $memcache_obj;

        include_once(DIR_WS_CLASSES . 'order.php');
        include_once(DIR_WS_CLASSES . 'edit_order.php');
        include_once(DIR_WS_CLASSES . 'email.php');
        include_once(DIR_WS_CLASSES . 'mime.php');
        include_once(DIR_WS_CLASSES . 'currencies.php');
        include_once(DIR_WS_CLASSES . 'process_locking.php');
        include_once(DIR_WS_CLASSES . 'log.php');
        include_once(DIR_WS_CLASSES . 'cache_abstract.php');
        include_once(DIR_WS_CLASSES . 'memcache.php');

        include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

        include_once(DIR_WS_LANGUAGES . 'english.php');
        include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ORDERS);

        // used in global
        $currencies = new currencies();
        $log_object = new log_files(self::DEFAULT_LOG_IDENTITY);
        $memcache_obj = new OGM_Cache_MemCache();

        $this->lock_obj = new process_locking();
    }

    private function isProductFullyDelivered($order_id) {
        $new_order_obj = new order($order_id);
        $return_bool = $new_order_obj->info['normal_purchase_fully_delivered'] == 1 ? TRUE : FALSE;
        unset($new_order_obj);

        return $return_bool;
    }

    private function deleteDeliveryQueue($order_products_array) {
        $delete_queue_sql = "	DELETE FROM " . TABLE_ORDERS_DELIVERY_QUEUE . "
                                WHERE orders_products_id IN (" . implode(',', array_keys($order_products_array)) . ")";
        return tep_db_query($delete_queue_sql);
    }

    private function getDeliveryQueue($limit = NULL) {
        $limit = is_null($limit) ? 'LIMIT 20' : $limit;
        $return_array = array();

        $order_select_sql = "SELECT op.orders_id, op.orders_products_id, op.products_name, op.products_quantity FROM orders_delivery_queue t LEFT JOIN orders_products op ON t.orders_products_id = op.orders_products_id order by op.products_id, op.orders_id ".$limit;

        $order_result_sql = tep_db_query($order_select_sql);
        while ($order_row = tep_db_fetch_array($order_result_sql)) {
            $order_id = $order_row['orders_id'];

            $order_select_sql2 = "	SELECT orders_products_id, extra_info
                                    FROM " . TABLE_ORDERS_DELIVERY_QUEUE . "
                                    WHERE orders_id = '" . $order_id . "'";
            $order_result_sql2 = tep_db_query($order_select_sql2);
            while ($order_row2 = tep_db_fetch_array($order_result_sql2)) {
                $return_array[$order_id][$order_row2['orders_products_id']] = !empty($order_row2['extra_info']) ? json_decode($order_row2['extra_info'], true) : array();
            }
        }

        return $return_array;
    }

    private function getOrderObj($order_id) {
        if (!isset($this->order_obj[$order_id])) {
            $this->order_obj[$order_id] = new order($order_id);
            $this->order_obj[$order_id]->edit = new edit_order(self::DEFAULT_ADMIN_ID, self::DEFAULT_ADMIN_EMAIL, $order_id);
        }

        return $this->order_obj[$order_id];
    }

    private function getProductInfo($product_id, $field = NULL, $force_reload = false) {
        if ($force_reload || !isset($this->product_info[$product_id])) {
            $product_select = "	SELECT  products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path,
                                        products_quantity, products_skip_inventory, products_cat_path, custom_products_type_id
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . tep_get_prid($product_id) . "'";
            $product_result = tep_db_query($product_select);
            $this->product_info[$product_id] = tep_db_fetch_array($product_result);
        }

        return is_null($field) ? $this->product_info[$product_id] : (isset($this->product_info[$product_id][$field]) ? $this->product_info[$product_id][$field] : '');
    }

    private function getDeliveredDesc($delimiter = '') {
        return implode($delimiter, $this->fully_delivered_desc);
    }

    private function setDeliveredDesc($desc = '', $reset = FALSE) {
        if ($reset) {
            $this->fully_delivered_desc = array();
        } else {
            if (!empty($desc)) {
                $this->fully_delivered_desc[] = $desc;
            }
        }
    }

    public function processBatchFullDelivery($extra_params = array(), $limit_job = NULL) {
        $return_bool = TRUE;
        $after_delivered_permission_array = array('notify_customer' => true);

        try {
            $delivery_queue = $this->getDeliveryQueue($limit_job) ;

            // delete delivery queue before action to prevent order get process multiple time
            foreach ($delivery_queue as $order_id => $order_queue_array) {
                $this->deleteDeliveryQueue($order_queue_array);
            }

            foreach ($delivery_queue as $order_id => $order_queue_array) {
                //TODO Implements lock before process order
                if ($this->lock_obj->isLocked($order_id, process_locking::MOVE_ORDER, FALSE)) {
                    // reset delivery status
                    $this->setDeliveredDesc('', TRUE);

                    if ($this->processPackage($order_id, $order_queue_array, $extra_params)) {
                        $order_obj = $this->getOrderObj($order_id);
                        $order_obj->edit->after_delivered($order_obj, $this->isProductFullyDelivered($order_id), $this->getDeliveredDesc(), $after_delivered_permission_array);
                        unset($order_obj);
                    }
                }
                $this->lock_obj->releaseLocked();
            }
        } catch (Exception $e) {
            $return_bool = FALSE;
            $this->reportError(array('e' => $e->getMessage()), 'processBatchDelivery()');
            $this->lock_obj->releaseLocked();
        }

        return $return_bool;
    }

    /*
     * Delivery single or bundle product which has cd key
     * qty is only apply for single product
     */

    private function processDeliveryCDKeys($order_id, $oproduct_array, $qty, $extra_params = array()) {
        $order_obj = $this->getOrderObj($order_id);
        $customer_id = $order_obj->customer['id'];

        if ($process_result = $order_obj->edit->process_delivery_CDKeys($customer_id, $qty, $oproduct_array, $extra_params)) {
            $this->setDeliveredDesc($process_result['partial_deliver_str']);
        }
    }

    private function processDeliveryStoreCredit($order_id, $oproduct_array, $qty, $extra_params = array()) {
        $order_obj = $this->getOrderObj($order_id);
        $customer_id = $order_obj->customer['id'];

        $permission_array = isset($extra_params['delivery_sc_permission']) ? $extra_params['delivery_sc_permission'] : array();
        $sc_type = $order_obj->edit->_get_store_credit_account_type($order_obj->info['payment_methods_id']);

        $order_product_array = array(
            'product' => array(
                'id' => $oproduct_array['id'],
                'name' => $oproduct_array['name'],
                'cat_path' => $this->getProductInfo($oproduct_array['id'], 'products_cat_path'),
            ),
            'id' => $oproduct_array['order_products_id'],
            'sc_type' => $sc_type,
            'purchased_qty' => $oproduct_array['qty'],
            'delivered_qty' => $oproduct_array["delivered_qty"],
            'good_delivered_qty' => $oproduct_array["qty_info"]['delivered_quantity'],
            'final_price' => $oproduct_array["final_price"],
            'orders_sc_currency_id' => $oproduct_array["sc_currency_info"]['currency_id']
        );

        if ($process_result = $order_obj->edit->process_delivery_store_credit($customer_id, $qty, $order_product_array, $permission_array)) {
            $this->setDeliveredDesc($process_result['partial_deliver_str']);
        }
    }

    /*
     * Deliver based on product type
     */

    private function processDeliveryItem($order_id, $oproduct_array, $qty, $extra_params = array()) {
        global $messageStack;
        $messageStack = array();

        if ($stock_row = $this->getProductInfo($oproduct_array['product']['id'])) {
            switch ($stock_row['custom_products_type_id']) {
                case 3: // store credit
                    $this->processDeliveryStoreCredit($order_id, $oproduct_array['product'], $qty, $extra_params);
                    break;
                case 2: // cd key
                    $this->processDeliveryCDKeys($order_id, $oproduct_array, $qty, $extra_params);
                    break;
            }

            if ($messageStack !== array()) {
                $this->reportError(array('order_id' => $order_id, 'order_products_id' => $oproduct_array['order_products_id'], 'error' => $messageStack));
            }

            unset($oproduct_array, $messageStack);
        }
    }

    private function processPackage($order_id, $to_deliver_orders_products_array, $extra_params) {
        $order_obj = $this->getOrderObj($order_id);
        $products_not_to_deliver_array = array();
        $to_deliver_orders_products_id_array = array_keys($to_deliver_orders_products_array);

        foreach ($order_obj->products as $idx => $products_array) {
            if (isset($products_array['bundle'])) {
                // no supported yet
            } else if (isset($products_array['static'])) {
                foreach ($products_array['static'] as $sub_idx => $sub_products_array) {
                    if (in_array($sub_products_array['order_products_id'], $to_deliver_orders_products_id_array)) {
                        $extra_params = array_merge($to_deliver_orders_products_array[$sub_products_array['order_products_id']], $extra_params);

                        if (!in_array($sub_products_array['id'], $products_not_to_deliver_array)) {
                            $oproduct_array = array(
                                'bundle' => 'static',
                                'parent' => $products_array,
                                'product' => $sub_products_array,
                            );

                            $qty = $sub_products_array['qty'] - $sub_products_array["delivered_qty"];
                            $this->processDeliveryItem($order_id, $oproduct_array, $qty, $extra_params);
                        }
                    }
                }
            } else {
                if (in_array($products_array['order_products_id'], $to_deliver_orders_products_id_array)) {
                    $extra_params = array_merge($to_deliver_orders_products_array[$products_array['order_products_id']], $extra_params);

                    if (!in_array($products_array['id'], $products_not_to_deliver_array)) {
                        $oproduct_array = array(
                            'bundle' => 'single',
                            'product' => $products_array,
                        );

                        $qty = $products_array['qty'] - $products_array["delivered_qty"];
                        $this->processDeliveryItem($order_id, $oproduct_array, $qty, $extra_params);
                    }
                }
            }
        }

        return $this->getDeliveredDesc() != '';
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Auto Delivery Error - ' . date("F j, Y H:i");
        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>