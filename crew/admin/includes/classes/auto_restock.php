<?php

class auto_restock
{

    private $service;
    public static $service_api = array(
        'rixty_api' => array(
            array('prefix_len' => 4, 'prefix_text' => 'RXT-')
        ),
        'eclub_api' => array(
            array('prefix_len' => 3, 'prefix_text' => 'ESW')
        ),
    );

    public function __construct()
    {
        self::include_lib();
    }

    private static function include_lib()
    {
        include_once(DIR_WS_INCLUDES . 'addon/rixty_sdk/rixty_api.php');
        include_once(DIR_WS_INCLUDES . 'addon/eClubStore_sdk/eclub_api.php');
    }

    private function isSupportedService($service)
    {
        return isset(self::$service_api[$service]);
    }

    public static function isSupportedSKU($sku)
    {
        $return_string = '';

        if ($sku) {
            self::include_lib();

            foreach (self::$service_api as $service_api => $service_array) {
                if (call_user_func_array($service_api . '::isSupportedSKU', $sku)) {
                    $return_string = $service_api;
                    break;
                }
            }
        }

        return $return_string;
    }

    private function isSupportedSKUPattern($sku)
    {
        $return_string = '';

        foreach (self::$service_api as $service => $pattern_array) {
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return_string = $service;
                    break;
                }
            }
        }

        return $return_string;
    }

    private function getServiceObj($sku, $service)
    {
        if (tep_not_empty($sku)) {
            // first layer if service provide invalid
            if (!$this->isSupportedService($service)) {
                // second layer find service if pattern match
                $service = $this->isSupportedSKUPattern($sku);
            }

            if (!isset($this->service[$service])) {
                if (!tep_not_empty($service)) {
                    $service = self::isSupportedSKU($sku);
                }

                if (tep_not_empty($service)) {
                    $this->service[$service] = new $service();
                }
            }
        }

        return $service != '' ? $this->service[$service] : false;
    }

    public function processBatchRestock($orders_products_id, $process_products_id, $process_store_price, $sku, $qty, $extra_params)
    {
        $return_bool = false;
        $service_provider = isset($extra_params['api_provider']) ? $extra_params['api_provider'] : '';

        if ($service_obj = $this->getServiceObj($sku, $service_provider)) {
            $extra_info_params = array(
                'orders_products_id' => $orders_products_id
            );

            $return_bool = $service_obj->processBatchRestock($process_products_id, $process_store_price, $sku, $qty, $extra_info_params);

            unset($service_obj);
        }

        return $return_bool;
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Auto Restock Error - ' . date("F j, Y H:i");
        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>