<?php
/*
  	$Id: base64_converter.php,v 1.6 2008/03/12 09:24:50 edwin.wang Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
class base64_converter
{
	var $files_arr = array();
	
	function base64_converter($params, $type="1")
	{
		global $messageStack;
		
		$filename = $params['filename'];
		if (!file_exists($filename)) {
			$messageStack->add_session(ERROR_FILE_DOES_NOT_EXIST, 'error');	
		} else {
			$path_parts = pathinfo($filename);
			$handler = 'encode_'.strtolower($path_parts['extension']);
			
			if ($type == "2") {
				$this->encode_file($filename);
			} else if (method_exists($this, $handler)) {
				$this->$handler($filename);
			} else {
				$this->encode_jpg($filename);
			}
		}
	}
	
	function encode_zip($filename)
	{
		global $messageStack;		
		if (!extension_loaded('zip')) {
			//use linux native unzip
			$this->encode_zip_native($filename);
			return;
		}
		
		$zip = @zip_open($filename);
		
		if ($zip) {
			$i=0;
			while ($zip_entry = zip_read($zip)) {
				$i++;
				$this->files_arr[$i]['name'] = addslashes(zip_entry_name($zip_entry));			   
				$this->files_arr[$i]['size_actual'] = zip_entry_filesize($zip_entry);			   
				$this->files_arr[$i]['size_compressed'] = zip_entry_compressedsize($zip_entry);
		        if (zip_entry_open($zip, $zip_entry, "r")) {
		           //$raw_source = zip_entry_read($zip_entry, $this->files_arr[$i]['size_compressed']);
		           $raw_source = zip_entry_read($zip_entry, zip_entry_filesize($zip_entry));
				   $this->files_arr[$i]['source_base64'] = chunk_split(base64_encode($raw_source),200,''); 		           
		           zip_entry_close($zip_entry);
		        }
		    }
	        zip_close($zip);
		} else {
			$messageStack->add_session(ERROR_FILE_NOT_READABLE, 'error');
		}
	}
	
	function encode_zip_native($filename)
	{
		global $messageStack;
		$tmpdirname = DIR_FS_IMPORT;
		if (!file_exists($filename))
		{
			//cannot find zip file
		} elseif (!is_dir(DIR_FS_IMPORT) && !mkdir(DIR_FS_IMPORT, 0755)) {
			$messageStack->add(ERROR_DESTINATION_NOT_WRITEABLE, 'error');
		} else {			
			$scommand = "unzip $filename -d $tmpdirname";
			exec($scommand, $output_arr, $retval_arr);
			if ($handle_dir = opendir($tmpdirname)) { 
				$i = 0; 
				while($file = readdir($handle_dir)) {    
					if ($file == '.' || $file == '..') 
						continue;    
					$i++;
					$file_fullpath = $tmpdirname.'/'.$file;
					$file_size = filesize($file_fullpath);
					$handle_file = fopen($file_fullpath, "r");
					$this->files_arr[$i]['name'] = $file;
					$this->files_arr[$i]['size_actual'] = $file_size;
					$this->files_arr[$i]['source_base64'] = chunk_split(base64_encode(fread($handle_file, $file_size)),200,'');  
					fclose($handle_file);
					unlink($file_fullpath);
				}  
				closedir($handle_dir);  
			}
		}
	}
	
	function encode_jpg($filename)
	{
		global $messageStack;		
		$parts = pathinfo($filename);
		$this->files_arr[0]['name'] = $parts['basename'];
		$this->files_arr[0]['size_actual'] = filesize($filename);
		if (($handle = fopen($filename, 'r')) && ($contents = fread($handle, filesize($filename))))
		{
			//$this->files_arr[0]['source_md5'] = md5($contents);
			$this->files_arr[0]['source_base64'] = base64_encode($contents);
			fclose($handle);
		} else 
			$messageStack->add_session(ERROR_FILE_DOES_NOT_EXIST, 'error');	
	}
	
	function encode_file($filename)
	{
		global $messageStack;		
		$parts = pathinfo($filename);
//		$this->files_arr[0]['name'] = basename($parts['basename'], $parts['extension']);
		$this->files_arr[0]['name'] = $parts['basename'];
		$this->files_arr[0]['size_actual'] = filesize($filename);
		if (($handle = fopen($filename, 'r')) && ($contents = fread($handle, filesize($filename))))
		{
			//$this->files_arr[0]['source_md5'] = md5($contents);
			$this->files_arr[0]['source_base64'] = base64_encode($contents);
			fclose($handle);
		} else 
			$messageStack->add_session(ERROR_FILE_DOES_NOT_EXIST, 'error');	
	}
	
	function get_files()
	{
		return $this->files_arr;
	}
}
?>
