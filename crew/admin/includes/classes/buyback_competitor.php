<?php
/*
  	$Id: buyback_competitor.php,v 1.23 2009/06/12 05:33:58 weichen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/
require_once(DIR_WS_FUNCTIONS . 'supplier.php');

class buyback_competitor {
    var $category_id;
    var $products_id;
    var $category_cat_path = '';
	
	var $_DECIMAL_PLACES = 6; //Calculation
	var $_DECIMAL_PLACES_DISPLAY = 4; //Display
	
	var $buyback_competitor_status_bracket_set_id = 0;
	var $buyback_competitor_status_bracket_set_name = '';
	
    var $competitor_status_brackets = array();        //All brackets for this category.
    var $competitor_status_brackets_delete = array(); //bracket ids marked for deletion
	
    var $competitor_status_num_brackets = 0;          //count(brackets)
	
	var $competitors_arr = array();
	var $competitors_price_arr = array();
	var $competitors_num = 0;
	var $competitors_num_is_full = array();
	
	var $products_arr = array();
	
    var $error_msg_arr = array();           //The complain queue for messageStack. Otherwise always assign messages as fatal.
	
    //Public Constructor
    function buyback_competitor($category_id, $products_id=0) {
        $this->category_id = $category_id;
        $this->products_id = $products_id;
        $this->category_cat_path = tep_output_generated_category_path_sq($this->category_id);
		
        $this->set_competitor_status_brackets();
        $this->set_competitors();
        $this->set_products();
        //$this->set_products_prices();//average prices will be overwritten by calculate method.
    }

    /*****************************************************************************************************
     * Public methods -  Callable
     *****************************************************************************************************/

    //Add a bracket to this class. Can be used in calculate() to trigger errors, then saved in save() if no error.
    function set_add_edit_competitor_status_bracket($buyback_categories_bracket_quantity, $buyback_bracket_value, $buyback_categories_bracket_id=0) {
        $success = false;

        //Lets check if the bracket id already exists.
        $bracket_id_exists = false;
        if ((int)$buyback_categories_bracket_id > 0) {
        	//edit mode.

            foreach ($this->competitor_status_brackets as $key => $bracket_arr) {
                if ((int)$bracket_arr['buyback_competitor_status_bracket_id'] == (int)$buyback_categories_bracket_id) {
                    $bracket_id_exists = true;
                    //exists, so edit
                    $this->competitor_status_brackets[$key]['buyback_competitor_status_bracket_quantity'] = $buyback_categories_bracket_quantity;
                    $this->competitor_status_brackets[$key]['buyback_competitor_status_bracket_value'] = $buyback_bracket_value;
                    break;
                }
            }
        }
        if (!$bracket_id_exists) {
			//add mode
			$this->competitor_status_brackets[] = array('buyback_competitor_status_bracket_id' => $buyback_categories_bracket_id,
														'buyback_competitor_status_bracket_set_id' => $this->buyback_competitor_status_bracket_set_id,
														'buyback_competitor_status_bracket_quantity' => $buyback_categories_bracket_quantity,
														'buyback_competitor_status_bracket_value' => $buyback_bracket_value);

            $this->sort_competitor_status_brackets_by_quantity();
            $this->update_competitor_status_brackets_count();
            $success = true;
        }
        return $success;
    }
	
    //Delete a single/array of bracket(s) from this class. Can be used in calculate() to trigger errors, then saved in save() if no error.
    function set_delete_competitor_status_bracket($bracket_id) {
        $success = false;
        $delete_bracket_array = array();

        if (!$bracket_id) {
            //In case bracket id is zero (no auto increment id yet) or is empty array for whatever reason.
            return false;
        }
        if (!is_array($bracket_id)) {
            $delete_bracket_array[] = $bracket_id;
        } else {
            $delete_bracket_array = $bracket_id;
        }


        foreach ($delete_bracket_array as $del_bracket_id) {
            $this->competitor_status_brackets_delete[] = $del_bracket_id;
            foreach ($this->competitor_status_brackets as $key => $bracket_array) {
                //Pop the bracket out so it doesn't interfere with calculations
                if ($bracket_array['buyback_competitor_status_bracket_id'] == $del_bracket_id) {
                    unset($this->competitor_status_brackets[$key]);
                    $success = true;
                    break 1;
                }
            }
        }

        $this->sort_competitor_status_brackets_by_quantity();
        $this->update_competitor_status_brackets_count();
        return $success;
    }
	
	function is_buyback_game() {
		$buyback_cat_select_sql = "	SELECT categories_id 
									FROM " . TABLE_CATEGORIES . " 
									WHERE categories_buyback_main_cat = 1 
										AND categories_id = '" . tep_db_input($this->category_id) . "'";
		$buyback_cat_result_sql = tep_db_query($buyback_cat_select_sql);
		
		if (tep_db_num_rows($buyback_cat_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
	
    //Call this after all changes to object vars ($this->assign_*) to recalculate.
    //Updates class with real-time values. Will not change db unless you call save() after calculate().
    function calculate_market_price() {
    	if (!$this->competitors_price_arr) {
    		return;
    	}
		
    	//calculate average price per product
		foreach ($this->products_arr as $products_id => $products_arr) {
			$active_competitor_status_bracket_arr = array();
			$active_competitor_status_bracket_key = 0;
			
			//get the active bracket for competitor status
	    	$competitors_pct_is_full = $this->competitors_num > 0 ? ($this->competitors_num_is_full[$products_id] / $this->competitors_num) * 100 : 0;
	        foreach ($this->competitor_status_brackets as $bracket_key => $bracket_arr) {
	            if ((float)$bracket_arr['buyback_competitor_status_bracket_quantity'] >= $competitors_pct_is_full) {
	            	$active_competitor_status_bracket_arr = $bracket_arr;
	            	$active_competitor_status_bracket_key = $bracket_key+1;
	                break;
	            }
	        }
			
	        if (!$active_competitor_status_bracket_arr) {
	            //Looks like the percentage of competitors facing server full is greater than all brackets. Lets just grab the largest bracket so we have something.
	            $bracket_key = (int)$this->competitor_status_num_brackets - 1;
            	$active_competitor_status_bracket_arr = $this->competitor_status_brackets[$bracket_key];
            	$active_competitor_status_bracket_key = $bracket_key+1;
	        }
			
			$competitors_tot_weight = 0;
			$competitors_tot_price = 0;
			$avg_competitor_price = 0;
			foreach ($this->competitors_arr as $competitor_id => $competitor_arr) {
				$competitors_tot_price += $competitor_arr['weight'] * $this->competitors_price_arr[$products_id][$competitor_id]['price'];
				//Ignore weight if price is zero
				if ($this->competitors_price_arr[$products_id][$competitor_id]['price'] > 0) {
					$competitors_tot_weight += $competitor_arr['weight'];
				}
			}
			
			$avg_competitor_price = $competitors_tot_weight > 0 ? ($competitors_tot_price / $competitors_tot_weight) * $active_competitor_status_bracket_arr['buyback_competitor_status_bracket_value'] : 0;
			
			$this->products_arr[$products_id]['avg_competitor_price'] = $this->number_format($avg_competitor_price);
        	$this->products_arr[$products_id]['buyback_competitor_status_bracket'] = $active_competitor_status_bracket_arr;
        	$this->products_arr[$products_id]['buyback_competitor_status_bracket_key'] = $active_competitor_status_bracket_key;
		}
    }

    //Save class data to db. Dont save if errors found (rollback) so we can use play with figures with impunity
    //and only save clean data.
//    function save_prices() {
//    	global $messageStack;
//		
//        if (count($this->error_msg_arr) == 0) {
//			$today = date('Y-m-d H:i:s');
//			foreach ($this->products_arr as $products_id => $products_arr) {
//				$success = false;
//				$insert_average_price_history_arr = array(	'products_id' => $products_id,
//															'competitors_average_price' => $products_arr['avg_competitor_price'],
//															'created_by' => $_SESSION['login_id'],
//															'created_on' => $today);
//				$competitors_average_price_final = $products_arr['avg_competitor_price'];
//				$competitors_average_price_is_overwrite = null;
//				if (!is_null($products_arr['avg_competitor_price_overwrite'])) {
//					$competitors_average_price_final = $products_arr['avg_competitor_price_overwrite'];
//					$competitors_average_price_is_overwrite = '1';
//				}
//				$insert_average_price_history_arr['competitors_average_price_final'] = $competitors_average_price_final;
//				$insert_average_price_history_arr['competitors_average_price_is_overwrite'] = $competitors_average_price_is_overwrite;
//
//				$success = tep_db_perform(TABLE_COMPETITORS_AVERAGE_PRICE_HISTORY, $insert_average_price_history_arr);
//				if ($success) {
//					$competitors_average_price_history_id = tep_db_insert_id();
//
//					$insert_sql = '';
//					
//					if (is_array($this->competitors_price_arr[$products_id]) && sizeof($this->competitors_price_arr[$products_id]) > 0) {
//						foreach ($this->competitors_price_arr[$products_id] as $competitor_id => $competitor_arr) {
//							$insert_competitor_buying_price_history_arr = array('products_id' => $products_id,
//																				'competitors_id' => $competitor_id,
//																				'competitors_average_price_history_id' => $competitors_average_price_history_id,
//																				'competitors_buying_price' => $competitor_arr['price'],
//																				'competitors_buying_status' => $competitor_arr['is_buyback']
//																				);
//							tep_db_perform(TABLE_COMPETITORS_BUYING_PRICE_HISTORY, $insert_competitor_buying_price_history_arr);
//						}
//					}
//				}
//			}
//			
//			$messageStack->add_session(SUCCESS_UPDATE_BUYING_PRICE, 'success');
//        } else {
//            $this->set_error(sprintf(MESSAGE_ERROR_SO_ABORT, $this->category_cat_path));
//        }
//    }

    function save_competitor_status_brackets() {
        //Brackets stuff starts here
    	if ((int)$this->buyback_competitor_status_bracket_set_id) { 
    		//Edit existing set
    		$buyback_competitor_status_bracket_set_arr = array('buyback_competitor_status_bracket_set_name' => $this->buyback_competitor_status_bracket_set_name);
			tep_db_perform(TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET_SET, $buyback_competitor_status_bracket_set_arr, 'update', "buyback_competitor_status_bracket_set_id='{$this->buyback_competitor_status_bracket_set_id}'");
    		
    	} else {
    		//Create new set
    		$buyback_competitor_status_bracket_set_arr = array(
    				'buyback_competitor_status_bracket_set_name' => $this->buyback_competitor_status_bracket_set_name);
			$success = tep_db_perform(TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET_SET, $buyback_competitor_status_bracket_set_arr);
			
			if ($success) {
				$this->buyback_competitor_status_bracket_set_id = tep_db_insert_id();
				
				//Insert/Update cat-set relationship
				$buyback_categories_arr = array('categories_id' => $this->category_id,
												'buyback_competitor_status_bracket_set_id' => $this->buyback_competitor_status_bracket_set_id);
				$cat_set_select_sql = "SELECT buyback_categories_id FROM ".TABLE_BUYBACK_CATEGORIES." WHERE buyback_competitor_status_bracket_set_id='{$this->buyback_competitor_status_bracket_set_id}'";
				$cat_set_result_sql = tep_db_query($cat_set_select_sql);
				if ($cat_set_row = tep_db_fetch_array($cat_set_result_sql)) { //exists
					$buyback_categories_id = $cat_set_row['buyback_categories_id'];
					tep_db_perform(TABLE_BUYBACK_CATEGORIES, $buyback_categories_arr, "categories_id='$buyback_categories_id'");
				} else {
					tep_db_perform(TABLE_BUYBACK_CATEGORIES, $buyback_categories_arr);
				}  
			}
    	}
    	
        //First delete those marked for deletion
		if (count($this->competitor_status_brackets_delete)) {
			tep_db_query("DELETE FROM " . TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET . "
							WHERE buyback_competitor_status_bracket_set_id = '{$this->buyback_competitor_status_bracket_set_id}'
							AND buyback_competitor_status_bracket_id IN ('" . implode("','", $this->competitor_status_brackets_delete) . "')");
		}    	
		
        //save brackets
        for ($i=0; $i<$this->competitor_status_num_brackets; $i++) {
        	//Update the bracket_set_id
        	$this->competitor_status_brackets[$i]['buyback_competitor_status_bracket_set_id'] = (int)$this->buyback_competitor_status_bracket_set_id;
        	
            //Update the bracket
            $bracket_id = (int)$this->competitor_status_brackets[$i]['buyback_competitor_status_bracket_id'];
            if ($bracket_id == 0) {
                //Insert New Bracket
    		    tep_db_perform(TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET, $this->competitor_status_brackets[$i]);
            } else {
                //Update Bracket. Don't screw up the auto increment id.
    		    $success = tep_db_perform(TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET, $this->competitor_status_brackets[$i], 'update', "buyback_competitor_status_bracket_id='{$bracket_id}'");
            }
        }
    }
	
    function save_competitors_weight () {
		global $messageStack;
		
		//save the competitors weight
		foreach ($this->competitors_arr as $competitors_id => $competitors_arr) {
			$competitors_weight_select_sql = "	SELECT competitors_to_categories_id 
												FROM " . TABLE_COMPETITORS_TO_CATEGORIES . " 
												WHERE categories_id='" . tep_db_input($this->category_id) . "' 
													AND competitors_id='" . tep_db_input($competitors_id) . "'";
			$competitors_weight_result_sql = tep_db_query($competitors_weight_select_sql);
			
			if ($competitors_weight_row = tep_db_fetch_array($competitors_weight_result_sql)) {
				//existing record
				$competitors_to_categories_id = $competitors_weight_row['competitors_to_categories_id'];
				$update_competitors_arr = array('competitors_weight' => $competitors_arr['weight']);
				
				tep_db_perform(TABLE_COMPETITORS_TO_CATEGORIES, $update_competitors_arr, 'update', "competitors_to_categories_id='".tep_db_input($competitors_to_categories_id)."'");
			} else {
				//create new if competitor id exists
				$competitors_id_select_sql = "	SELECT competitors_id 
												FROM " . TABLE_COMPETITORS . " 
												WHERE competitors_id = '" . tep_db_input($competitors_id) . "'";
				$competitors_id_result_sql = tep_db_query($competitors_id_select_sql);
				if ($competitors_id_row = tep_db_fetch_array($competitors_id_result_sql)) {
					$update_competitors_arr = array('competitors_id' => $competitors_id,
													'categories_id' => $this->category_id,
													'competitors_weight' => $competitors_arr['weight']);
					tep_db_perform(TABLE_COMPETITORS_TO_CATEGORIES, $update_competitors_arr);
				} else {
					$messageStack->add_session(sprintf(MESSAGE_INVALID_SUPPLIER_ID, $competitors_id));
				}
			}
		}
    }
	
	function assign_competitor($competitor_id, $competitor_code, $competitor_weight) {
		$this->competitors_arr[$competitor_id] = array('code' => $competitor_code, 'weight' => $competitor_weight);
		$this->competitors_num = count($this->competitors_arr);
	}

//	function assign_competitors_price($products_id, $competitor_id, $price, $is_buyback) {
//		if (!isset($this->competitors_num_is_full[$products_id])) {
//			$this->competitors_num_is_full[$products_id] = 0;
//		}
//		
//		$this->competitors_price_arr[$products_id][$competitor_id] = array('price' => $price, 'is_buyback' => $is_buyback);
//		
//		if (!$is_buyback) {
//			$this->competitors_num_is_full[$products_id]++;
//		}
//	}

	function assign_products($products_id, $product_info_array) {
		$this->products_arr[$products_id]['cat_id'] = $product_info_array['categories_id'];
		$this->products_arr[$products_id]['cat_path'] = $product_info_array['products_cat_path'];
		$this->products_arr[$products_id]['product_name'] = $product_info_array['products_name'];
		$this->products_arr[$products_id]['customer_price'] = $product_info_array['products_price'];
		$this->products_arr[$products_id]['available_qty'] = $product_info_array['products_quantity'];
		$this->products_arr[$products_id]['actual_qty'] = $product_info_array['products_actual_quantity'];
		$this->products_arr[$products_id]['categories_name'] = $product_info_array['categories_name'];
		$this->products_arr[$products_id]['error_msg'] = '';
		// $this->products_arr[$products_id]['avg_offer_price'] storing buyback_price FROM automate_buyback_price TABLE
		$this->products_arr[$products_id]['avg_offer_price'] = $product_info_array['buyback_price'];
	}

	function assign_competitors_weight($competitor_id, $competitor_weight) {
		global $messageStack;
		
		if ($competitor_weight > 0) {
			$this->competitors_arr[$competitor_id]['weight'] = $competitor_weight;
		} else {
			$messageStack->add_session(sprintf(WARNING_COMPETITOR_WEIGHT_INVALID, $competitor_weight, $this->competitors_arr[$competitor_id]['code']));
		}
	}
	
	function get_data_csv() {
		$line_break = "\n";
		$empty_columns = str_repeat(',', ($this->competitors_num*2));
		$export_csv_data = '';
		$export_csv_product = '';
		
		$export_csv_competitor = CSV_HEADING_BUYING_PRICE_IMPORT_CURRENCY .','.DEFAULT_CURRENCY. $line_break;
		$export_csv_competitor .= CSV_HEADING_COMPETITOR_ID.','.TABLE_HEADING_COMPETITOR_CODE.','.TABLE_HEADING_COMPETITOR_WEIGHT;
		
		foreach ($this->competitors_arr as $competitors_id => $competitors_arr) {
			$export_csv_competitor .= $line_break;
			$export_csv_competitor .= $competitors_id . ',' . $this->competitors_arr[$competitors_id]['code'] . ',' . $this->competitors_arr[$competitors_id]['weight'];
			$export_csv_competitor .= $empty_columns;
		}
		
		$export_csv_product .= CSV_HEADING_COMPETITOR_PRODUCT_ID. ',' . TABLE_HEADING_PRODUCT;
		
		for ($i=0; $i<$this->competitors_num; $i++) {
			$export_csv_product .= ',' . CSV_HEADING_COMPETITOR_PRODUCT_PRICE . ',' . CSV_HEADING_COMPETITOR_PRODUCT_STATUS;
		}
		
		$export_csv_product .= ',' . TABLE_HEADING_AVERAGE_MARKET_PRICE . ',' . TABLE_HEADING_OVERWRITE_AVERAGE_MARKET_PRICE . $line_break;
		$export_csv_product = strip_tags($export_csv_product);
		
		foreach ($this->products_arr as $products_id => $products_arr) {
			$export_csv_product .= $products_id . ',' . $products_arr['cat_path'];
			
			foreach ($this->competitors_arr as $competitors_id => $competitors_arr) {
				if (isset($this->competitors_price_arr[$products_id][$competitors_id])) {
					$competitors_arr = $this->competitors_price_arr[$products_id][$competitors_id];
					$export_csv_product .= ',' . $competitors_arr['price'] . ',' . ($competitors_arr['is_buyback'] ? 'Normal' : 'Full');
				} else {
					$export_csv_product .= ',,';
				}
			}
			
			$export_csv_product .= ',' . $products_arr['avg_competitor_price'] . ',' . (is_null($products_arr['avg_competitor_price_overwrite']) ? '' : $products_arr['avg_competitor_price_overwrite']) . $line_break;
		}
		
		$export_csv_data = $export_csv_competitor . $line_break . '###' . $empty_columns . $line_break . $export_csv_product;
		
		return $export_csv_data;
	}

	/**
	 * Wrapper for number formatter. For calculation, not for display.
	 */
	function number_format($number) {
		$formatted_number = number_format($number, $this->_DECIMAL_PLACES, '.', '');
		return $formatted_number;
	}

    function show_error_warning () {
        global $messageStack;
        foreach ($this->error_msg_arr as $msg) {
            $messageStack->add_session($msg);
        }
    }

    /*****************************************************************************************************
     * Private methods -  Not Callable
     *****************************************************************************************************/
    //Set db values to class
    function set_competitor_status_brackets() {
    	//get bracket set names
    	$bracket_set_name_select_sql = "SELECT bc.buyback_competitor_status_bracket_set_id, bs.buyback_competitor_status_bracket_set_name
    									FROM ".TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET_SET." as bs, ".TABLE_BUYBACK_CATEGORIES." as bc
    									WHERE bc.categories_id = '" . tep_db_input($this->category_id) . "'
    									AND bc.buyback_competitor_status_bracket_set_id = bs.buyback_competitor_status_bracket_set_id";

    	$bracket_set_name_result_sql = tep_db_query($bracket_set_name_select_sql);
    	if ($bracket_set_name_row = tep_db_fetch_array($bracket_set_name_result_sql)) {
			$this->buyback_competitor_status_bracket_set_name = $bracket_set_name_row['buyback_competitor_status_bracket_set_name'];
			$this->buyback_competitor_status_bracket_set_id = $bracket_set_name_row['buyback_competitor_status_bracket_set_id'];
    	}
		if ((int)$this->buyback_competitor_status_bracket_set_id) { 
	        //competitor status brackets
	        $competitor_status_brackets = array();
			$competitor_status_brackets_select_sql = "SELECT sb.*
	        								FROM " . TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET . " AS sb
	        								WHERE sb.buyback_competitor_status_bracket_set_id = '{$this->buyback_competitor_status_bracket_set_id}'
	        								ORDER BY sb.buyback_competitor_status_bracket_quantity ASC;";
	
			$competitor_status_brackets_result_sql = tep_db_query($competitor_status_brackets_select_sql);
			$this->competitor_status_num_brackets = tep_db_num_rows($competitor_status_brackets_result_sql);
			while ($row = tep_db_fetch_array($competitor_status_brackets_result_sql)) {
			  $competitor_status_brackets[] = $row;
			}
			$this->competitor_status_brackets = $competitor_status_brackets;
		}
    }

    function update_competitor_status_brackets_count() {
    	$new_num_brackets = count($this->competitor_status_brackets);
    	$curr_num_brackets = $this->competitor_status_num_brackets;
    	if (($curr_num_brackets != $new_num_brackets) && $curr_num_brackets==0 && $new_num_brackets>0) {
			//remove the error message
			$error_key = array_search(MESSAGE_INVALID_BRACKET_COUNT, $this->error_msg_arr);
			unset($this->error_msg_arr[$error_key]);
    	}

        $this->competitor_status_num_brackets = $new_num_brackets;

        if ($this->competitor_status_num_brackets == 0) {
            $this->set_error(MESSAGE_INVALID_BRACKET_COUNT);
        }
    }
	
    function sort_competitor_status_brackets_by_quantity() {
        foreach ($this->competitor_status_brackets as $key => $bracket_array) {
           $bracket_quantity_arr[$key]  = $bracket_array['buyback_competitor_status_bracket_quantity'];
        }
        
        array_multisort($bracket_quantity_arr, SORT_ASC, $this->competitor_status_brackets);
    }

	function set_competitors() {
		$competitors_arr = array();
		
		$competitors_select_sql = "	SELECT c.competitors_id, c.competitors_name, c2c.competitors_weight 
									FROM " . TABLE_COMPETITORS . " AS c 
									INNER JOIN " . TABLE_COMPETITORS_TO_CATEGORIES . " AS c2c 
										ON (c.competitors_id=c2c.competitors_id AND c2c.categories_id='".tep_db_input($this->category_id)."')";
		$competitors_select_result = tep_db_query($competitors_select_sql);
		
		while ($competitors_select_row = tep_db_fetch_array($competitors_select_result)) {
			$competitors_arr[$competitors_select_row['competitors_id']] = array('code' => $competitors_select_row['competitors_name'], 
																				'weight' => ((double)$competitors_select_row['competitors_weight'] > 0 ? (double)$competitors_select_row['competitors_weight'] : '1.0')
																				);
		}
		
		$this->competitors_arr = $competitors_arr;
		$this->competitors_num = count($competitors_arr);
	}
	
	function set_products() {
		global $languages_id;
		
		if ($this->products_id > 0) {
			//get this product only
			$products_select_product = "SELECT p2c.categories_id, cd.categories_name, p.products_id AS prd_id, p.products_cat_path, p.products_price, p.products_quantity, p.products_actual_quantity, p.products_main_cat_id, p.products_cat_id_path, abp.buyback_price 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
				            			INNER JOIN " . TABLE_PRODUCTS . " AS p
				            				ON (p2c.products_id = p.products_id)
										INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
				            				ON (p2c.categories_id = cd.categories_id AND cd.language_id = '" . (int)$languages_id . "')
				            			LEFT JOIN " . TABLE_AUTOMATE_BUYBACK_PRICE . " AS abp 
												ON p2c.products_id = abp.products_id 
				            			WHERE p.products_id = '".tep_db_input($this->products_id)."'
				            				AND p2c.products_is_link=0
				            				AND p.custom_products_type_id=0
				            				AND p.products_bundle=''
											AND p.products_bundle_dynamic=''
				            			ORDER BY p.products_cat_path, p.products_sort_order";
		} else {
			//get the whole category
			//$category_array = array($this->category_id);
			//tep_get_subcategories($category_array, $this->category_id);
			
			$cat_parent_path = tep_get_categories_parent_path($this->category_id);
			if (tep_not_null($cat_parent_path)) {
				$cat_parent_array = explode("_",$cat_parent_path);
				$top_parent_id = $cat_parent_array[1];
				
				$cat_parent_path .= $this->category_id."_";
			} else {
				$cat_parent_path = "_".$this->category_id."_";
				$top_parent_id = (int)$this->category_id;
			}
			$cat_parent_path = str_replace('_', '\_', $cat_parent_path);
			
			$products_select_product = "SELECT p.products_id AS prd_id, pd.products_name, p.products_cat_id_path, 
												p.products_cat_path, p.products_price, p.products_quantity, 
												p.products_actual_quantity, p.products_main_cat_id, abp.buyback_price 
		            					FROM " . TABLE_PRODUCTS . " AS p 
		            					INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											ON (p.products_id=pd.products_id AND pd.language_id = '" . (int)$languages_id . "') 
		            					INNER JOIN " . TABLE_BUYBACK_PRODUCTS . " AS bp
											ON p.products_id=bp.products_id 
										LEFT JOIN " . TABLE_AUTOMATE_BUYBACK_PRICE . " AS abp 
											ON p.products_id = abp.products_id 
		            					WHERE p.products_main_cat_id = '".(int)$top_parent_id."'
											AND p.products_cat_id_path LIKE '".$cat_parent_path."%'
		            						AND p.custom_products_type_id=0
		            						AND p.products_bundle=''
											AND p.products_bundle_dynamic=''
		            					ORDER BY p.products_cat_path, p.products_sort_order";
		}
		
		$products_result = tep_db_query($products_select_product);
		while ($products_row = tep_db_fetch_array($products_result)) {
			$product_floating_qty = tep_calculate_floating_available_qty($products_row['prd_id']);
			$products_row['products_quantity'] += $product_floating_qty;	// Add floating available qty to prevent purchasing those stock too early and minimise banning lost
			
			$product_cat_path_array = explode("_",$products_row['products_cat_id_path']);
			$products_row['categories_id'] = $product_cat_path_array[count($product_cat_path_array)-2];	// Last element is '_'
			
			$this->assign_products($products_row['prd_id'], $products_row);
		}
	}
	
//	function set_products_prices() {
//		$history_id_arr = array();
//		$latest_price_select_sql = "SELECT max(competitors_average_price_history_id) as competitors_average_price_history_id
//									FROM ".TABLE_COMPETITORS_AVERAGE_PRICE_HISTORY."
//									WHERE products_id IN ('".implode("','", array_keys($this->products_arr))."')
//									GROUP BY products_id";
//		$latest_price_result_sql = tep_db_query($latest_price_select_sql);
//		if (tep_db_num_rows($latest_price_result_sql)) {
//			while ($latest_price_row = tep_db_fetch_array($latest_price_result_sql)) {
//				$history_id_arr[] = $latest_price_row['competitors_average_price_history_id'];
//			}
//			
//			$latest_price_set_select_sql = "SELECT competitors_average_price_history_id, products_id, competitors_average_price, competitors_average_price_final, competitors_average_price_is_overwrite, created_by, created_on
//											FROM ".TABLE_COMPETITORS_AVERAGE_PRICE_HISTORY."
//											WHERE competitors_average_price_history_id IN ('".implode("','", $history_id_arr)."')";
//			$latest_price_set_result_sql = tep_db_query($latest_price_set_select_sql);
//			while ($latest_price_set_row = tep_db_fetch_array($latest_price_set_result_sql)) {
//				$competitors_average_price_history_id = $latest_price_set_row['competitors_average_price_history_id'];
//				$products_id = $latest_price_set_row['products_id'];
//				
//				$this->products_arr[$products_id]['avg_competitor_price'] = $this->number_format($latest_price_set_row['competitors_average_price']);
//				$this->products_arr[$products_id]['avg_competitor_price_final'] = $this->number_format($latest_price_set_row['competitors_average_price_final']);
//				
//				$overwrite_price = null;
//				
//				if ((int)$latest_price_set_row['competitors_average_price_is_overwrite']) {
//					$overwrite_price = $this->number_format($latest_price_set_row['competitors_average_price_final']);
//				}
//				
//				$this->products_arr[$products_id]['avg_competitor_price_overwrite'] = $overwrite_price;
//				
//				$latest_competitor_price_select_sql = "	SELECT competitors_id, competitors_buying_price, competitors_buying_status
//														FROM ".TABLE_COMPETITORS_BUYING_PRICE_HISTORY."
//														WHERE products_id = '" . tep_db_input($products_id) . "' 
//															AND competitors_average_price_history_id = '".tep_db_input($competitors_average_price_history_id)."'";
//				$latest_competitor_price_result_sql = tep_db_query($latest_competitor_price_select_sql);
//				
//				while ($latest_competitor_price_row = tep_db_fetch_array($latest_competitor_price_result_sql)) {
//					if (isset($this->competitors_arr[$latest_competitor_price_row['competitors_id']]) && is_array($this->competitors_arr[$latest_competitor_price_row['competitors_id']])) {
//						$this->assign_competitors_price($products_id, $latest_competitor_price_row['competitors_id'], $latest_competitor_price_row['competitors_buying_price'], (int)$latest_competitor_price_row['competitors_buying_status']);
//					}
//				}
//			}
//		}
//	}

    function set_error($msg, $fatal=false) {
        $this->error_msg_arr[] = $msg;
        
        if ($fatal) {
            $this->show_error_warning();
        }
    }
}
?>