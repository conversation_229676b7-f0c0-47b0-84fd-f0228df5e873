<?php

class c2c_buy_order {

    public function __construct() {
        
    }

    public static function c2c_order_product_extra_info($opid, $key) {
        $result = array();

        $_sql = "   SELECT orders_products_extra_info_value
                    FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
                    WHERE orders_products_id = " . $opid . "
                        AND orders_products_extra_info_key = '" . $key . "'";
        $_res = tep_db_query($_sql);
        if ($_row = tep_db_fetch_array($_res)) {
            $result = tep_array_unserialize($_row['orders_products_extra_info_value']);
            if (empty($result)) {
                $result = $_row['orders_products_extra_info_value'];
            }
        }

        return $result;
    }

    public static function c2c_product_snapshot($datetime, $cid, $lid) {
        global $memcache_obj;

        $result = array();
        $name = date('YmdHis', strtotime($datetime)) . "_" . $cid . "_" . $lid;
        $filename = $name . ".json";

        $cache_key = TABLE_ORDERS . '/g2g_order_listing_attribute/array/' . $name;
        $cache_result = $memcache_obj->fetch($cache_key);
        
        if ($cache_result !== FALSE) {
            $result = $cache_result;
        } else {
            include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
            $aws_obj = new ogm_amazon_ws();
            if ($aws_obj->is_aws_s3_enabled()) {
                $s3_bucket = 'BUCKET_DATA';
                $s3_filepath = 'G2G/order_snapshot/' . date("Y/m", strtotime($datetime)) . "/";

                $aws_obj->set_bucket_key($s3_bucket);
                $aws_obj->set_filepath($s3_filepath);
                $file_content = $aws_obj->get_file($filename, $s3_filepath, $s3_bucket);
                $prod_json = $file_content->body;
            } else {
                $_tmp = file_get_contents($filename);
                $prod_json = $_tmp;
            }

            if (!empty($prod_json)) {
                $result = json_decode($prod_json, true);
                $memcache_obj->store($cache_key, $result, 86400);
            }
        }

        return $result;
    }

}
