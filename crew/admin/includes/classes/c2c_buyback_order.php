<?php
/*
  $Id: c2c_buyback_order.php,v 1.51 2016/02/29 03:43:48 wenbin.ng Exp $

  Developer: Ching Yen
 */
require_once(DIR_WS_CLASSES . 'g2g_serverless.php');
require_once(DIR_WS_CLASSES . 'c2c_order.php');
include_once(DIR_WS_LANGUAGES . 'buyback_english.php');


class c2c_buyback_order {

    const CHANGE_BY = 'G2G system';
    const IDENTITY = 0;
    const MOVE_ORDER = 'G2G_CREATE_SO';

    public $c2c_cpt_list = '';

    public function __construct() {
        $this->c2c_cpt_list = explode(',', str_replace(' ', '', C2C_PRODUCT_TYPE));
    }

    public function searchCriteria() {
        # Search By
        $search_opt = array(array('id' => '',
                'text' => SELECT_DEFAULT_EMPTY),
            array('id' => 'c2c_buyback_id',
                'text' => SELECT_C2C_BUYBACK_ORDER_ID),
            array('id' => 'seller_id',
                'text' => SELECT_SELLER_ID),
            array('id' => 'customers_email_address',
                'text' => SELECT_CUSTOMERS_EMAIL_ADDRESS),
        );

        # C2C Customer Product Type
        $cpt_opt = _get_custom_product_type($this->c2c_cpt_list);

        # Game List
        $_def_game_list[] = array(
            'id' => '',
            'text' => SELECT_DEFAULT_EMPTY
        );
        $games_list = category::get_game_list_by_product_type(implode(',', $this->c2c_cpt_list));
        $games_list = array_merge($_def_game_list, $games_list);

        ob_start();
        ?>
        <script language="javascript">
            <!--
            function check_form() {
            var error = 0;
            var error_message = "Errors have occured during the process of your search!\nPlease make the following corrections:\n\n";

            if (document.listing.start_date.value === "" && document.listing.end_date.value === "" &&
            document.listing.search_text.value === "" && document.listing.search_opt.value === "" &&
            document.listing.games_list.value === "" && document.listing.cpt_opt.value === "") {
            var bo_status_opt = document.listing.elements['order_status[]'];
            var opt_selected = 0;

            for (i = 0, cnt = bo_status_opt.length; cnt > i; i++) {
            if (bo_status_opt[i].checked === true) {
            opt_selected++;
            }
            }

            if ((opt_selected === 0) && (document.getElementById('order_status_any').checked === false)) {
            error_message = error_message + "* Fill-in at least 1 search criteria.\n";
            error = 1;
            }
            }

            if (document.listing.start_date.value != "" && document.listing.end_date.value != "" && !diffStartAndEndDate(document.listing.start_date.value, document.listing.end_date.value, 31)) {
            error_message = error_message + "* Date range not more than 31 days.\n";
            error = 1;
            }

            if (document.listing.search_text.value !== "" && document.listing.search_opt.value === "") {
            error_message = error_message + "* 'Search By' option must be selected.\n";
            error = 1;
            }

            if (document.listing.search_text.value === "" && document.listing.search_opt.value !== "") {
            var sel = document.listing.search_opt;
            error_message = error_message + "* 'Search By : " + sel.options[sel.selectedIndex].text + "' entry must be entered.\n";
            error = 1;
            }

            if (error === 1) {
            alert(error_message);
            return false;
            } else {
            return true;
            }
            }

            function bo_status_checkbox() {
            var bo_status_opt = document.listing.elements['order_status[]'];
            var opt_selected = 0;

            for (i = 0, cnt = bo_status_opt.length; cnt > i; i++) {
            if (bo_status_opt[i].checked === true) {
            opt_selected++;
            }
            }

            if (opt_selected > 0) {
            document.getElementById('order_status_any').checked = false;
            } else {
            document.getElementById('order_status_any').checked = true;
            }
            }

            function bo_status_reset() {
            var bo_status_opt = document.listing.elements['order_status[]'];

            if (document.getElementById('order_status_any').checked === true) {
            for (i = 0, cnt = bo_status_opt.length; cnt > i; i++) {
            bo_status_opt[i].checked = false;
            }
            }
            }
            //-->
        </script>

        <form name="listing" method="post" id="addForm" action="<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=search_result'); ?>" onSubmit="return check_form();">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_SEARCH_CRITERIA; ?></td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="reportRecords">
                        <div>
                            <iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?php echo DIR_WS_INCLUDES; ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td valign="top">
                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="main" width="15%" align="left"><?php echo TEXT_START_CREATE_DATE; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('start_date', '', 'id="start_date" size="20" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.listing.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.listing.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'; ?></td>
                                <td class="main" width="15%" align="left"><?php echo TEXT_END_CREATE_DATE; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('end_date', '', 'id="end_date" size="20" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.listing.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.listing.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'; ?></td>
                            </tr>
                            <tr>
                                <td class="main" align="left"><?php echo TEXT_SEARCH_BY; ?></td>
                                <td class="main" colspan="3">
                                    <?php
                                    echo tep_draw_input_field('search_text', '', ' id="search_text" size="40"');
                                    echo str_repeat('&nbsp', 3);
                                    echo tep_draw_pull_down_menu('search_opt', $search_opt, '', ' id="search_opt"');
                                    ?>
                                </td>
                            </tr>
                            <?php
                            $_buyback_status_data = self::_get_buyback_status();
                            if (!empty($_buyback_status_data)) {
                                ?>
                                <tr>
                                    <td class="main" align="left" valign="top"><?php echo TEXT_BUYBACK_ORDER_STATUS; ?></td>
                                    <td class="main" colspan="3">
                                        <?php
                                        echo tep_draw_checkbox_field('order_status_any', 0, (isset($_SESSION['c2c_bo_params']['order_status_any']) || !isset($_SESSION['c2c_bo_params']['order_status']) ? true : false), '', 'id="order_status_any" onclick="javascript:bo_status_reset()"') . TEXT_ANY . '<br/>';

                                        foreach ($_buyback_status_data AS $_status_id => $_buyback_status) {
                                            echo tep_draw_checkbox_field('order_status[]', $_status_id, (isset($_SESSION['c2c_bo_params']['order_status']) && in_array($_status_id, $_SESSION['c2c_bo_params']['order_status']) ? true : false), '', 'onclick="javascript:bo_status_checkbox()"') . $_buyback_status . '&nbsp;&nbsp;';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php } ?>
                            <tr>
                                <td class="main" align="left"><?php echo TEXT_GAME_CATEGORIES; ?></td>
                                <td class="main" colspan="3"><?php echo tep_draw_pull_down_menu('games_list', $games_list, '', ' id="games_list"'); ?></td>
                            </tr>
                            <tr>
                                <td class="main" align="left"><?php echo TEXT_PRODUCT_TYPE; ?></td>
                                <td class="main" colspan="3"><?php echo tep_draw_pull_down_menu('cpt_opt', $cpt_opt, '', ' id="cpt_opt"'); ?></td>
                            </tr>
                            <tr>
                                <td colspan="4"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                            </tr>
                            <tr>
                                <td class="main" colspan="4" align="right"><?php echo tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, '', 'inputButton') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        if (isset($_SESSION['c2c_bo_params'])) {
            unset($_SESSION['c2c_bo_params']);
        }

        return $listing_html;
    }

    public function searchResult() {
        $error = 0;
        $tr_class = '';

        $_cond1_sql = array();
        $_cond2_sql = array();
        $_rows = array();
        $_status_cond = array();
        $_status_filt = array();

        $cur_obj = new currencies();

        if (isset($_SESSION['c2c_bo_params']) && !empty($_SESSION['c2c_bo_params']) && empty($_POST)) {
            foreach ($_SESSION['c2c_bo_params'] as $key => $val) {
                $_POST[$key] = $val;
            }
            unset($_SESSION['c2c_bo_params']);
        }

        if (isset($_POST['start_date']) && ($_POST['start_date'] != '')) {
            $_cond1_sql[] = " cb.date_added >= '" . $_POST['start_date'] . " 00:00:00' ";
            $_SESSION['c2c_bo_params']['start_date'] = $_POST['start_date'];
        }

        if (isset($_POST['end_date']) && ($_POST['end_date'] != '')) {
            $end_datetime = $_POST['end_date'];
            $end_date_timestamp = strtotime($end_datetime);
            $new_end_datetime = date("Y-m-d H:i", $end_date_timestamp);

            if ($end_datetime != $new_end_datetime) {
                $end_datetime = date("Y-m-d", $end_date_timestamp) . " 23:59:59";
            }
            $_cond1_sql[] = " cb.date_added <= '" . $end_datetime . "' ";
            $_SESSION['c2c_bo_params']['end_date'] = $end_datetime;
        }

        if (isset($_POST['order_status']) && !empty($_POST['order_status'])) {
            foreach ($_POST['order_status'] as $num => $val) {
                $_status_cond[] = $val;
            }
            $_SESSION['c2c_bo_params']['order_status'] = $_POST['order_status'];
            unset($_SESSION['c2c_bo_params']['order_status_any']);
        } else if (isset($_POST['order_status_any']) && ($_POST['order_status_any'] != '')) {
            $_status_cond = array_keys(self::_get_buyback_status());
            $_SESSION['c2c_bo_params']['order_status_any'] = $_POST['order_status_any'];
            unset($_SESSION['c2c_bo_params']['order_status']);
        }

        if ((isset($_POST['search_opt']) && ($_POST['search_opt'] != '')) && (isset($_POST['search_text']) && ($_POST['search_text'] != ''))) {
            if ($_POST['search_opt'] == 'customers_email_address') {
                $_sub_sel_sql = "	SELECT customers_id FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_prepare_input($_POST['search_text']) . "'";
                $_sub_res_sql = tep_db_query($_sub_sel_sql);
                if ($_sub_row = tep_db_fetch_array($_sub_res_sql)) {
                    $_cond1_sql[] = " cb.seller_id = '" . (int) $_sub_row['customers_id'] . "' ";
                } else {
                    $error = 1;
                }
            } else {
                $_cond1_sql[] = " cb." . $_POST['search_opt'] . " = '" . tep_db_prepare_input($_POST['search_text']) . "' ";
            }
            $_SESSION['c2c_bo_params']['search_opt'] = $_POST['search_opt'];
            $_SESSION['c2c_bo_params']['search_text'] = $_POST['search_text'];
        }

        if (isset($_POST['games_list']) && ($_POST['games_list'] != '')) {
            $_cond2_sql[] = " cbp.game_id = '" . (int) $_POST['games_list'] . "' ";
            $_SESSION['c2c_bo_params']['games_list'] = $_POST['games_list'];
        }

        if (isset($_POST['cpt_opt']) && ($_POST['cpt_opt'] != '')) {
            $_cond2_sql[] = " cbp.custom_products_type = '" . (int) $_POST['cpt_opt'] . "' ";
            $_SESSION['c2c_bo_params']['cpt_opt'] = $_POST['cpt_opt'];
        }

        $view_seller_info = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLER_INFO_VIEWING');

        ob_start();
        ?>
        <form name="listing">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                            <?php
                            if (isset($_POST['start_date']) || isset($_POST['end_date'])) {
                                ?>
                                <tr>
                                    <td colspan="2" class="reportRecords">
                                        <?php printf(HEADING_FROM_TO_DATE, (isset($_POST['start_date']) && ($_POST['start_date'] != '') ? $_POST['start_date'] : '*'), (isset($end_datetime) && ($end_datetime != '') ? $end_datetime : date('Y-m-d'))); ?>
                                    </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="reportRecords">[ <a href="<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER); ?>"><?php echo LINK_SEARCH_CRITERIA; ?></a> ]</td>
                </tr>
                <?php
                $_buyback_status_data = self::_get_buyback_status();
                if (!empty($_buyback_status_data)) {
                    foreach ($_buyback_status_data as $_status_id => $_buyback_status) {
                        ?>
                        <tr>
                            <td class="pageHeading" valign="top" colspan="2">
                                <span class="pageHeading"><?php echo $_buyback_status ?></span>
                                <span id="showhidedetails_<?php echo $_status_id ?>"><a href="javascript:;" onclick="showHideDetails_<?php echo $_status_id ?>(this)"><?= TEXT_SHOW_DETAILS ?></a></span>
                            </td>
                        </tr>
                        <tr>
                            <td valign="top">
                                <table border="0" width="100%" cellspacing="1" cellpadding="2" style="border-collapse: collapse;">
                                    <tr>
                                        <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_BO_ID; ?></td>
                                        <td class="reportBoxHeading" valign="top" width="15%"><?php echo SUB_TABLE_GAME_NAME; ?></td>
                                        <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_PRODUCT_TYPE; ?></td>
                                        <?php if ($view_seller_info) { ?>
                                            <td class="reportBoxHeading" valign="top" width="15%"><?php echo SUB_TABLE_SELLER_NAME; ?></td>
                                            <td class="reportBoxHeading" valign="top" width="15%"><?php echo SUB_TABLE_SELLER_EMAIL; ?></td>
                                        <?php } ?>
                                        <td class="reportBoxHeading" valign="top" width="08%"><?php echo SUB_TABLE_BO_DATE; ?></td>
                                        <td class="reportBoxHeading" valign="top" align="center" width="10%"><?php echo SUB_TABLE_BO_TOTAL_PAYABLE; ?></td>
                                        <td class="reportBoxHeading" valign="top" align="center" width="05%"><?php echo SUB_TABLE_ACTION; ?></td>
                                    </tr>
                                    <?php
                                    $_sel_numrows = '0';
                                    if ($error == 0 && (!empty($_cond1_sql) || !empty($_cond2_sql) || !empty($_status_cond))) {
                                        if (empty($_status_cond) || (!empty($_status_cond) && in_array($_status_id, $_status_cond))) {
                                            $_cond1_str = implode(" AND ", $_cond1_sql);
                                            $_cond2_str = implode(" AND ", $_cond2_sql);
                                            $_cond_str = (!empty($_cond1_str) && !empty($_cond2_str) ? $_cond1_str . ' AND ' . $_cond2_str : $_cond1_str . $_cond2_str);
                                            $_status_filt = 1;
                                            if (in_array($_status_id, $_status_cond)) {
                                                $_status_filt = " (cb.status = " . $_status_id . " ) ";
                                            }
                                            $_cond_sql = (!empty($_cond_str) ? $_cond_str . ' AND ' : '') . " ( " . $_status_filt . " ) ";

                                            $_sel_sql = "SELECT cb.c2c_buyback_id, cbp.game_id, cbp.custom_products_type, cbp.product_name, cb.seller_id, cb.orders_read_mode, 
                                                            c.customers_firstname, c.customers_lastname, c.customers_email_address,
                                                            cb.date_added, cb.currency, cbp.purchase_quantity, cbp.after_fee_unit_price,pdm.products_delivery_mode_title, cbp.orders_products_id
                                                        FROM " . TABLE_C2C_BUYBACK . " AS cb
                                                        INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                                                            ON cbp.c2c_buyback_id = cb.c2c_buyback_id
                                                        INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                                            ON c.customers_id = cb.seller_id
                                                       LEFT JOIN " . TABLE_PRODUCTS_DELIVERY_MODE . " AS pdm
                                                            ON cbp.delivery_mode = pdm.products_delivery_mode_id
                                                        WHERE " . $_cond_sql . "
                                                        ORDER BY cb.date_added DESC";

                                            // pagination
                                            $page_split_object = new splitPageResults($_GET['page' . $_status_id], MAX_DISPLAY_SEARCH_RESULTS, $_sel_sql, $_sel_numrows, true);

                                            $_res_sql = tep_db_query($_sel_sql);
                                            if (tep_db_num_rows($_res_sql)) {
                                                $tr_count = 0;
                                                while ($_rows = tep_db_fetch_array($_res_sql)) {
                                                    if ($tr_count % 2 == 0) {
                                                        $tr_class = "reportListingOdd";
                                                    } else {
                                                        $tr_class = "reportListingEven";
                                                    }

                                                    $balance = (int) $_rows['purchase_quantity'] - (int) $_rows['delivered_quantity'];
                                                    $payment_amt = ($_rows['after_fee_unit_price'] * $_rows['delivered_quantity']);

                                                    $js.= "product_ids_" . $_status_id . ".push(" . $_rows['c2c_buyback_id'] . ");";
                                                    $list_colspan_count = "8";
                                                    ?>
                                                    <tbody class="<?php echo $_rows["orders_read_mode"] == "1" ? '' : 'boldText'?>">
                                                    <tr id="<?= $_status_id . '_main_' . $tr_count ?>" class="<?php echo $tr_class; ?>" onmouseover="showOverEffect(this, 'reportListingRowOver', '<?= $_status_id ?>_prod_<?= $tr_count ?>')" onmouseout="showOutEffect(this, '<?php echo $tr_class; ?>', '<?= $_status_id ?>_prod_<?= $tr_count ?>')" onclick="showClicked(this, '<?php echo $tr_class; ?>', '<?= $_status_id ?>_prod_<?= $tr_count ?>')">
                                                        <td class="reportRecords" valign="top"><a href="?selected_box=c2c&action=add_form&id=<?php echo $_rows['c2c_buyback_id']; ?>"><?php echo $_rows['c2c_buyback_id']; ?></a></td>
                                                        <td class="reportRecords" valign="top"><?php echo tep_get_categories_name($_rows['game_id'], 1); ?></td>
                                                        <td class="reportRecords" valign="top"><?php echo tep_get_custom_product_type_name($_rows['custom_products_type']); ?></td>
                                                        <?php if ($view_seller_info) { ?>
                                                            <td class="reportRecords" valign="top"><?php echo ($_rows['customers_firstname'] != '' || $_rows['customers_lastname'] != '' ? '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $_rows['seller_id'] . '&action=edit') . '" target="_blank">' . $_rows['customers_firstname'] . ' ' . $_rows['customers_lastname'] . '</a>' : TEXT_NOT_AVAILABLE); ?></td>
                                                            <td class="reportRecords" valign="top"><?php echo ($_rows['customers_email_address'] != '' ? $_rows['customers_email_address'] : TEXT_NOT_AVAILABLE); ?></td>
                                                        <?php } ?>
                                                        <td class="reportRecords" valign="top"><?php echo $_rows['date_added']; ?></td>
                                                        <td class="reportRecords" valign="top" align="right"><?php echo $cur_obj->format(($_rows['purchase_quantity'] * $_rows['after_fee_unit_price']), false, $_rows['currency']); ?></td>
                                                        <td class="reportRecords" valign="top" align="center"><a href="?selected_box=c2c&action=add_form&id=<?php echo $_rows['c2c_buyback_id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a></td>
                                                    </tr>
                                                    <tr id="<?= $_status_id . '_prod_' . $tr_count ?>" class="<?= $tr_class ?>" onMouseOver="showOverEffect(this, 'reportListingRowOver', '<?= $_status_id ?>_main_<?= $tr_count ?>')" onMouseOut="showOutEffect(this, '<?= $tr_class ?>', '<?= $_status_id ?>_main_<?= $tr_count ?>')" onClick="showClicked(this, '<?= $tr_class ?>', '<?= $_status_id ?>_main_<?= $tr_count ?>')">

                                                        <td colspan="<?= ($list_colspan_count) ?>">
                                                            <table cellspacing="0" cellpadding="2" border="0" width="100%">
                                                                <TBODY class="hide" id="productinfo<?= $_rows['c2c_buyback_id'] ?>">
                                                                    <tr>
                                                                        <td class="ordersRecords" colspan="9" width="100%"><div style="border-bottom: 1px solid #996600;"><?= tep_draw_separator('pixel_trans.gif', '1', '1') ?></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="ordersRecords"><?php echo TABLE_HEADING_PRODUCT_NAME; ?></td>
                                                                        <td width="20%" class="ordersRecords"><?php echo TABLE_HEADING_TRADE_MODE; ?></td>
                                                                        <td width="10%" align="center" class="ordersRecords"><?php echo TABLE_HEADING_PRODUCT_QUANTITY; ?></td>
                                                                        <td width="10%" align="center" class="ordersRecords"><?php echo TABLE_HEADING_PRODUCT_BALANCE; ?></td>
                                                                        <td width="10%" align="right" class="ordersRecords"><?php echo TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT; ?></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td colspan="9"><div style="border-bottom: 1px solid #996600;"><?= tep_draw_separator('pixel_trans.gif', '1', '1') ?></div></td>
                                                                    </tr>
                                                                    <tr class="<?php echo $row_style; ?>">
                                                                        <td class="ordersRecords"><?php echo $_rows['product_name']; ?></td>
                                                                        <?php
                                                                        include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                                                                        $deliver_mode_title = $_rows['products_delivery_mode_title'];
                                                                        $deliver_mode = c2c_buy_order::c2c_order_product_extra_info($_rows['orders_products_id'], 'delivery_mode');
                                                                        if ($deliver_mode && is_array($deliver_mode)) {
                                                                            $deliver_mode_title = isset($deliver_mode['name']) ? $deliver_mode['name'] : "";
                                                                        }
                                                                        ?>
                                                                        <td class="ordersRecords"><?php echo $deliver_mode_title; ?></td>
                                                                        <td align="center" class="ordersRecords"><?php echo $_rows['purchase_quantity']; ?></td>
                                                                        <td align="center" class="ordersRecords"><?php echo $balance; ?></td>

                                                                        <td align="right" class="ordersRecords"><?php echo $cur_obj->format($payment_amt, false, $_rows['currency']); ?></td>
                                                                    </tr>



                                                                </tbody>
                                                            </table>
                                                        </td>

                                                    </tr>
                                                    </tbody>
                                                    <?php
                                                    $tr_count++;
                                                }
                                            } else {
                                                ?>
                                                <tr class="reportListingEven">
                                                    <td class="reportRecords" align="center" colspan="8"><i>Empty</i></td>
                                                </tr>
                                                <?php
                                            }
                                        } else {
                                            ?>
                                            <tr class="reportListingEven">
                                                <td class="reportRecords" align="center" colspan="8"><i>Empty</i></td>
                                            </tr>
                                            <?php
                                        }
                                    } else {
                                        ?>
                                        <tr class="reportListingEven">
                                            <td class="reportRecords" align="center" colspan="8"><i>Empty</i></td>
                                        </tr>
                                    <?php } ?>
                                </table>
                            </td>
                        </tr>
                        <script type="text/javascript">
                            var product_ids_<?= $_status_id ?> = new Array();

                            function showHideDetails_<?= $_status_id ?>(link_obj) {
                            var current_show = '';
                            if (typeof(link_obj.innerText) != 'undefined') {
                            current_show = link_obj.innerText == '<?= TEXT_SHOW_DETAILS ?>' ? true : false;
                            } else {
                            current_show = link_obj.text == '<?= TEXT_SHOW_DETAILS ?>' ? true : false;
                            }

                            var productinfo;

                            if (current_show) {
                            link_obj.innerHTML = '<?= TEXT_HIDE_DETAILS ?>';
                            for (i = 0; i < product_ids_<?= $_status_id ?>.length; i++) {
                            productinfo = document.getElementById('productinfo' + product_ids_<?= $_status_id ?>[i]);
                            productinfo.className = "show";
                            }
                            } else {
                            link_obj.innerHTML = '<?= TEXT_SHOW_DETAILS ?>';

                            for (i = 0; i < product_ids_<?= $_status_id ?>.length; i++) {
                            productinfo = document.getElementById('productinfo' + product_ids_<?= $_status_id ?>[i]);
                            productinfo.className = "hide";
                            }
                            }
                            }
                            <?= $js ?>
                        </script>
                        <?php if ($_sel_numrows) { ?>
                            <tr>
                                <td valign="top" colspan="2">
                                    <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                                        <tr>
                                            <td class="smallText" valign="top" nowrap><?php echo $page_split_object->display_count((int) $_sel_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page' . $_status_id], TEXT_DISPLAY_NUMBER_OF_RECORD) ?></td>
                                            <td class="smallText" align="right"><?php echo $page_split_object->display_links((int) $_sel_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page' . $_status_id], tep_get_all_get_params(array('page' . $_status_id, 'x', 'y', 'cont')) . "cont=1", "page" . $_status_id) ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                            <?php
                        }
                    }
                }
                ?>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addForm($id) {
        global $messageStack;

        if ($id != "") {
            $cur_obj = new currencies();

            $view_seller_info = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLER_INFO_VIEWING');
            $view_seller_order_static = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_VIEW_ORDER_STATISTICS');
            $view_buyer_char = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_VIEW_RSTK_CHAR');
            $view_screenshot = tep_admin_files_actions(FILENAME_BUYBACK_REQUESTS_INFO, 'BUYBACK_ORDER_VIEW_SCREENSHOT');

            $_cb_sel = "SELECT cb.c2c_buyback_id, cb.seller_id, cb.date_added, cb.currency, cb.currency_value, cb.status,
                            cb.orders_tag_ids, cb.locked_by, bs.buyback_status_name, cb.success_rate_flag, o.orders_status, cbp.orders_id, o.customers_id
                        FROM " . TABLE_C2C_BUYBACK . " AS cb
                        LEFT JOIN " . TABLE_BUYBACK_STATUS . " AS bs
                            ON bs.buyback_status_id = cb.status
                        LEFT JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                            ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                        LEFT JOIN ". TABLE_ORDERS ." AS o
                                ON cbp.orders_id = o.orders_id
                        LEFT JOIN " . TABLE_ORDERS_STATUS . " AS os
                                 ON os.orders_status_id = o.orders_status
                        WHERE cb.c2c_buyback_id = '" . (int) $id . "'
                            AND bs.language_id = 1";

            $reverse_seller_permission = tep_admin_files_actions(FILENAME_C2C_BUYBACK_ORDER, 'REVERSE_SELLER_FAULT');
            $cancel_delivery_permission = tep_admin_files_actions(FILENAME_C2C_BUYBACK_ORDER, 'CANCEL_SELLER_DELIVERY');
            $_cb_res = tep_db_query($_cb_sel);
            if (tep_db_num_rows($_cb_res) > 0) {
                tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET orders_read_mode = 1 WHERE c2c_buyback_id = '" . (int) $id . "'");
                $f_buyback_status_data = self::_get_buyback_status();

                $_cb_row = tep_db_fetch_array($_cb_res);
                if($cancel_delivery_permission){
                    $_oph_sel = "SELECT *
                        FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph
                        WHERE oph.buyback_request_group_id = " . (int) $id ;
                    $_oph_res = tep_db_query($_oph_sel);
                }
                if(($_cb_row['status'] == 1) || ($_cb_row['status'] == 5)  || ($_cb_row['status'] == 6) ){ 
                    if($_cb_row['orders_status'] == 2){
                
                    include_once(DIR_WS_CLASSES . 'c2c_order.php');
                    $result = c2c_order::getOrderCancellation($_cb_row['orders_id']); 
                    if($result['result'] > 0){ 
                    ?> 
                        <div id="hiddenCancellation" style="display:none;">1</div> 
                    <?php 
                    } 
                    else{ 
                    ?> 
                        <div id="hiddenCancellation" style="display:none;">0</div> 
                    <?php 
                    } 
                    }
                }   
                
                $_bo_gid = $_cb_row['c2c_buyback_id'];
                $_seller = self::_get_customer_detail($_cb_row['seller_id']);
                $_total_deliver = 0;

                $_lock_status = _verify_locking_status($_bo_gid, TABLE_C2C_BUYBACK);


                //buyback tag
                $_res = self::_order_tag($id);
                $otag = $_res['tag'];
                $tag_str = $_res['tag_str'];

                ob_start();
                ?>
                <script language="javascript">
                    function checkBalance(ref_id, recv_qty, max_qty) {
                    recv_qty = parseInt(recv_qty);
                    max_qty = parseInt(max_qty);

                    if ((document.getElementById('partial_receive_sign_' + ref_id) !== null) && (document.getElementById('partial_receive_' + ref_id) !== null)) {
                    var qty_sign = document.getElementById('partial_receive_sign_' + ref_id).value;
                    var input = trim_str(document.getElementById('partial_receive_' + ref_id).value);

                    if (qty_sign !== '' && input !== '') {
                    var entered_qty = parseInt(qty_sign + '' + input);
                    if (entered_qty >= 0) {
                    if (entered_qty > (max_qty - recv_qty)) {
                    alert('Entered receive quantity exceed balance quantity!\nPlease reduce the quantity!');
                    return false;
                    }
                    } else {
                    if (Math.abs(entered_qty) > recv_qty) {
                    alert('Entered deduct receive quantity exceed received quantity!\nPlease reduce the deduction quantity!');
                    return false;
                    }
                    }

                    return true;
                    }
                    }

                    return false;
                    }

                    function validateForm(ref_id, recv_qty, max_qty) {
                    var res = checkBalance(ref_id, recv_qty, max_qty);
                    if (res === true) {
                    document.getElementById('so-receive').submit();
                    }

                    return res;
                    }
                </script>
                <table cellspacing="2" cellpadding="2" border="0" width="100%">
                    <tr>
                        <td>
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                    <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">&laquo; <a href="<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=search_result'); ?>">Back to Search Criteria</a></td>
                    <tr>
                        <td>
                            <table border="0" cellpadding="2" cellspacing="2" width="100%">
                                <tr>
                                    <td class="main" valign="top" width="195px"><b><?php echo TEXT_BO_NUMBER; ?></b></td>
                                    <td class="main" valign="top" width="5px"><b>:</b></td>
                                    <td class="main" align="left"><b><?php echo $_bo_gid; ?></b></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo TEXT_BO_DATE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_cb_row['date_added']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo TEXT_BO_STATUS; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <?php echo tep_draw_hidden_field('current_status', $_cb_row['status'], 'id="current_status"'); ?>
                                        <?php echo $_cb_row['buyback_status_name']; ?>&nbsp;&nbsp;
                                        <?php echo tep_draw_pull_down_menu($_cb_row['buyback_status_name'] . "_tag_selector", $otag, '', ' id="' . $_cb_row['buyback_status_name'] . '_tag_selector" onChange="orderListsOptions(this, \'' . $_cb_row['status'] . '\', \'' . (int) $languages_id . '\', \'' . $_bo_gid . '\', \'\', \'G2G\');"'); ?>
                                        <small><span class="greenIndicator" id="tag_<?php echo $_bo_gid; ?>">&nbsp;&nbsp;<?php echo $tag_str; ?></span></small>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="main" colspan="3">
                                    <?php echo "<a href='https://dashboard.sendbird.com/34201740-152E-401E-AD8F-5C72EEABA386/group_channels?members_include_in={$_cb_row['seller_id']}%2C{$_cb_row['customers_id']}' target='_blank'>View Chat History</a>" ?>
                                    </td>
                                </tr>
                                <?php if($_cb_row['status'] == 4){ ?>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo TEXT_SELLER_FAULT; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <?php echo tep_draw_hidden_field('current_fault', $_cb_row['success_rate_flag'], 'id="current_fault"');?>
                                        <?php echo ($_cb_row['success_rate_flag'])? 'Yes' : 'No'; ?>&nbsp;&nbsp;
                                        <?php if($reverse_seller_permission){?>
                                            <a  href='#' onclick ="javascript:change_seller_fault(<?php echo $_cb_row['success_rate_flag'];?>,'<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=update&subact=reverse_fault&fault='.$_cb_row['success_rate_flag'].'&id=' . $_bo_gid) ;?>')"><?php echo ($_cb_row['success_rate_flag'])? 'Change to No' : 'Change to Yes'; ?>&nbsp;&nbsp;</a>
                                        <?php } ?>
                                        <small><span class="greenIndicator" id="tag_<?php echo $_bo_gid; ?>">&nbsp;&nbsp;<?php echo $tag_str; ?></span></small>
                                    </td>
                                </tr>
                                <?php } ?>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td><?php echo tep_draw_separator(); ?></td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <?php if ($view_seller_info || $view_seller_order_static) { ?>
                        <tr>
                            <td>
                                <table width="100%" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?php echo SUB_HEADING_SELLER_INFO; ?></td>
                                        <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                                    </tr>
                                    <tr>
                                        <td width="50%" align="left" valign="top">
                                            <?php if ($view_seller_info) { ?>
                                                <table border="0" cellpadding="2" cellspacing="2">
                                                    <tr>
                                                        <td class="main" valign="top" width="195px"><b><?php echo TEXT_SELLER; ?></b></td>
                                                        <td class="main" valign="top" width="5px"><b>:</b></td>
                                                        <td class="main" align="left"><b><?php echo $_seller['seller_name']; ?></b></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_USERNAME; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_username']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_GROUP; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_group_name']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_STATUS; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_status']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_GENDER; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['customers_gender']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_DOB; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_dob']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_EMAIL; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['customers_email_address']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_TELEPHONE; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_telephone']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_MOBILE_PHONE; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['customers_mobile']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><b><?php echo TEXT_SELLER_IM; ?></b></td>
                                                        <td class="main" valign="top"><b>:</b></td>
                                                        <td class="main" align="left"><?php echo $_seller['seller_im']; ?></td>
                                                    </tr>
                                                </table>
                                            <?php } ?>
                                        </td>
                                        <td width="50%" align="right" valign="top">
                                            <?php if ($view_seller_order_static) { ?>
                                                <table width="100%" border="0" cellpadding="2" cellspacing="2">
                                                    <tr>
                                                        <td width="20%" class="main" valign="top"><b><?php echo TEXT_ORDER_STATISTICS; ?></b></td>
                                                        <td width="80%">
                                                            <table width="100%" border="1" cellspacing="0" cellpadding="3">
                                                                <tr>
                                                                    <td width="40%">&nbsp;</td>
                                                                    <td width="25%" class="main" align="center" valign="top"><?php echo SUB_TABLE_HEADING_TOTAL_ORDER; ?></td>
                                                                    <td width="35%" class="main" align="center" valign="top"><?php echo SUB_TABLE_HEADING_TOTAL_AMOUNT . ($cur_obj->currencies[DEFAULT_CURRENCY]['symbol_left'] != '' ? $cur_obj->currencies[DEFAULT_CURRENCY]['symbol_left'] : $cur_obj->currencies[DEFAULT_CURRENCY]['symbol_right']); ?></td>
                                                                </tr>
                                                                <?php
                                                                foreach ($f_buyback_status_data AS $_status_id => $_buyback_status) {
                                                                    $total_count = $one_day_total_count = 0;
                                                                    $total_amount = $one_day_sum_total_amount = 0;
                                                                    $sum_total_amount = 0;

                                                                    $_sql = "   SELECT cbp.purchase_quantity, cbp.after_fee_unit_price_usd, cbp.purchase_quantity, MAX(cbh.date_added) as t_date
                                                                        FROM " . TABLE_C2C_BUYBACK . " AS cb
                                                                        INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " cbp
                                                                            ON cbp.c2c_buyback_id = cb.c2c_buyback_id
                                                                        INNER JOIN " . TABLE_C2C_BUYBACK_HISTORY . " cbh
                                                                            ON cbh.c2c_buyback_id = cb.c2c_buyback_id AND cbh.status = " . $_status_id . "
                                                                        WHERE cb.seller_id = " . $_cb_row['seller_id'] . "
                                                                            AND cb.status = " . $_status_id . "
                                                                        GROUP BY cb.c2c_buyback_id";
                                                                    $_res = tep_db_query($_sql);
                                                                    while ($_row = tep_db_fetch_array($_res)) {
                                                                        $total_amount = ($_row['after_fee_unit_price_usd'] * $_row['purchase_quantity']);
                                                                        if (number_format(tep_day_diff($_row['t_date'], date('Y-m-d H:i:s'))) == 0) {
                                                                            $one_day_total_count++;
                                                                            $one_day_sum_total_amount += $total_amount;
                                                                        }

                                                                        $total_count++;
                                                                        $sum_total_amount += $total_amount;
                                                                    }
                                                                    ?>
                                                                    <tr>
                                                                        <td colspan="3" class="main"><b><?php echo $_buyback_status; ?></b></td>
                                                                    </tr>
                                                                    <?php if ($_buyback_status != 1) { ?>
                                                                        <tr>
                                                                            <td class="main"><?php echo TEXT_BUYBACK_IN_1_DAY; ?></td>
                                                                            <td class="main" align="center" valign="top"><?php echo $one_day_total_count; ?></td>
                                                                            <td class="main" align="right" valign="top"><?php echo $cur_obj->format($one_day_sum_total_amount, false, DEFAULT_CURRENCY); ?></td>
                                                                        </tr>
                                                                    <?php } ?>
                                                                    <tr>
                                                                        <td class="main"><?php echo TEXT_TOTAL_BUYBACK; ?></td>
                                                                        <td class="main" align="center" valign="top"><?php echo $total_count; ?></td>
                                                                        <td class="main" align="right" valign="top"><?php echo $cur_obj->format($sum_total_amount, false, DEFAULT_CURRENCY); ?></td>
                                                                    </tr>
                                                                <?php } ?>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            <?php } ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                        </tr>
                        <tr>
                            <td><?php echo tep_draw_separator(); ?></td>
                        </tr>
                        <tr>
                            <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                        </tr>
                    <?php } ?>
                    <tr>
                        <td class="main">
                            <?php
                            $_unlock_group_arr = array();

                            $_unlock_permission_group = tep_admin_group_unlock_permission();
                            if (is_array($_unlock_permission_group) && count($_unlock_permission_group)) {
                                $_unlock_group_arr = array_keys($_unlock_permission_group);
                            }

                            $_unlock_href = '#';
                            if ($_lock_status && (($_cb_row['locked_by'] == $_SESSION['login_id']) || in_array($_SESSION['login_groups_id'], $_unlock_group_arr))) {
                                $_unlock_href = tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=update&subact=unlock&id=' . $id);
                            }

                            if ($_lock_status) {
                                if ($_unlock_href != '#') {
                                    ?>
                                    <div id="navBtn" style="float:left; margin-top: -18px;">
                                        <ul>
                                            <li id="unlock_btn">
                                                <a href="<?php echo $_unlock_href; ?>" title="Unlocking this order"><?php echo TEXT_BO_UNLOCK; ?></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <?php
                                }
                                echo sprintf(TEXT_LOCKED_BO_SEEN_BY_OWNER, '<b>' . $_lock_status['locking_by'] . '</b>', $_lock_status['locking_datetime'], $_lock_status['locking_from_ip']);
                            } else {
                                ?>
                                <div id="navBtn" style="float:left; margin-top: -18px;">
                                    <ul>
                                        <li id="lock_btn">
                                            <a href="<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'action=update&subact=lock&id=' . $id); ?>" title="Locking this order"><?php echo TEXT_BO_LOCK; ?></a>
                                        </li>
                                    </ul>
                                </div>
                                <?php
                                echo TEXT_BO_NOT_BEEN_LOCKED;
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div id="locking_history_c2c">
                                <div style="float:left;"><a href="javascript:;" onClick="lockingHistory('<?= (int) $id ?>', '1', 'locking_history_c2c');">Show Locking History</a></div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?php
                            $row_count = 1;
                            $payment_total = 0;
                            $sign_arr = array(
                                array('id' => "+", 'text' => "+"),
                            );
                            $_cbp_sql = "SELECT cbp.c2c_buyback_product_id, cbp.c2c_products_listing_id, cbp.product_name, cbp.custom_products_type,
                                                    cbp.purchase_quantity, cbp.after_fee_unit_price, pdm.products_delivery_mode_title, cbp.buyer_character,
                                            cbp.delivered_quantity, cbp.delivery_mode, cbp.total_screenshot, cbp.orders_id, cbp.orders_products_id, cb.date_added,
                                            o.customers_id, o.date_purchased
                                                FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                                                INNER JOIN " . TABLE_C2C_BUYBACK . " AS cb
                                                     ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                                                LEFT JOIN " . TABLE_PRODUCTS_DELIVERY_MODE . " AS pdm
                                                     ON cbp.delivery_mode = pdm.products_delivery_mode_id
                                        INNER JOIN " . TABLE_ORDERS . " AS o
                                            ON o.orders_id = cbp.orders_id
                                                WHERE cbp.c2c_buyback_id = '" . (int) $id . "'";
                            $_cbp_res = tep_db_query($_cbp_sql);
                            while ($_cbp_row = tep_db_fetch_array($_cbp_res)) {
                                $_non_cancel_delivery_sql = "SELECT SUM(delivered_amount) as ongoing_deliveries FROM ". TABLE_ORDERS_PRODUCTS_HISTORY . " WHERE orders_products_id = ".$_cbp_row['orders_products_id']." AND buyback_request_group_id = ". $id . " AND (received <> 2 OR received IS NULL)";
                                $_non_cancel_delivery_res = tep_db_query($_non_cancel_delivery_sql);
                                if($_non_cancel_delivery_row = tep_db_fetch_array($_non_cancel_delivery_res)){
                                    if((int)$_non_cancel_delivery_row['ongoing_deliveries'] != $_cbp_row['delivered_quantity']){
                                        $sign_arr[] = ['id' => '-', 'text' => '-'];
                                    }
                                }

                                $totalSceenShot = $_cbp_row['total_screenshot'];
                                if ($view_screenshot && ($totalSceenShot > 0)) {
                                    $totalnew = 0;
                                    $uploadedProof = self::c2c_buyback_product_extra_info((int)$id, 'so_upload_proof');
                                    if($uploadedProof){
                                    ?>
                                        <table width="100%" border="0" cellspacing="1" cellpadding="3">
                                            <tr>
                                                <td colspan="2"><?php echo tep_draw_separator(); ?></td>
                                            </tr>
                                            <tr>
                                                        <td class="main" valign="top" width="150"><b>Uploaded Proof: </b></td>
                                                        <td class="main">
                                            <?php
                                                foreach($uploadedProof as $proofno => $proofName){
                                                    $sequnce = $proofno+1;
                                                    $pathinfo = pathinfo($proofName);
                                                    $ext = strtolower($pathinfo['extension']);
                                                    $prefix = "i";
                                                    switch($ext){
                                                        case 'mp4':
                                                        case '3gp':
                                                        case 'mov':
                                                        case 'wmv':
                                                        case 'avi':
                                                        case 'flv':
                                                           $prefix = "v";
                                                            break;
                                                    }
                                                    $totalnew++;
                                                echo '<a href="' . tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_g2g_bb_proof&file=' . urlencode($proofName)) . '" target="_blank">'. $prefix . '-' .substr($proofName, 0, 15) .'</a>&nbsp;&nbsp;';
                                                    if($sequnce%8 == 0){
                                                        echo "<br />";
                                                    }
                                                }
                                            ?>
                                                    </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2"><?php echo tep_draw_separator(); ?></td>
                                            </tr>
                                            <tr>
                                                <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                                            </tr>
                                        </table>
                                    <?php
                                    }
                                    if ($totalnew < $totalSceenShot) {
                                        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
                                        $s3_bucket = 'BUCKET_UPLOAD';
                                        $s3_filepath = 'G2G/' . date("Y/m", strtotime($_cbp_row['date_added'])) . "/" . $id . "/";
                                        $aws_obj = new ogm_amazon_ws();
                                        $aws_obj->set_bucket_key($s3_bucket);
                                        $aws_obj->set_filepath($s3_filepath);
                                        $s3_files = $aws_obj->s3_api(array('method' => 'get_object_list', $s3_bucket, array("prefix" => $s3_filepath)));
                                        ?>

                                        <table width="100%" border="0" cellspacing="1" cellpadding="3">
                                            <tr>
                                                <td colspan="2"><?php echo tep_draw_separator(); ?></td>
                                            </tr>
                                            <?php
                                            $num_record = count($s3_files);
                                            $ssLinkOrder = 0;
                                            $screenShotLink = '';
                                            for ($i = 0; $num_record > $i; $i++) {
                                                $filename = str_replace($s3_filepath, '', $s3_files[$i]);
                                                if ($ssLinkOrder % 2) {
                                                    ?>
                                                    <tr>
                                                        <td class="main" valign="top" width="150"><b><?php echo TEXT_SCREEN_SHOT . ' ' . (($i + 1) / 2); ?> : </b></td>
                                                        <td class="main">
                                                            <?php
                                                            echo $screenShotLink;
                                                            echo '<a href="' . tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_g2g_bb_ss&path=' . $s3_filepath . '&file=' . $filename) . '" target="_blank">after_trade.jpg</a>&nbsp;';
                                                            ?>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                } else {
                                                    $screenShotLink = '<a href="' . tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_g2g_bb_ss&path=' . $s3_filepath . '&file=' . $filename) . '" target="_blank">before_trade.jpg</a>&nbsp;';
                                                }
                                                $ssLinkOrder++;
                                            }
                                            ?>
                                            <tr>
                                                <td colspan="2"><?php echo tep_draw_separator(); ?></td>
                                            </tr>
                                            <tr>
                                                <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                                            </tr>
                                        </table>
                                    <?php
                                    }
                                }
                                $row_style = ($row_count % 2) ? 'ordersListingEven' : 'ordersListingOdd';

                                $payment_amt = ($_cbp_row['after_fee_unit_price'] * $_cbp_row['delivered_quantity']);
                                $payment_total += $payment_amt;

                                switch ($_cbp_row['custom_products_type']) {
                                    case 1: // PWL
                                        $balance = $_cbp_row['purchase_quantity'] - $_cbp_row['delivered_quantity'];

                                        include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                                        $m_ext = c2c_buy_order::c2c_order_product_extra_info($_cbp_row['orders_products_id'], 'delivery_info');
                                        $m_lvl = self::c2c_buyback_product_extra_info($_cbp_row['c2c_buyback_product_id']);
                                        ?>
                                        <form name="so-receive" id="so-receive" method="post" action="<?php echo FILENAME_C2C_BUYBACK_ORDER . '?action=update&subact=recv&id=' . $id; ?>">
                                            <table width="100%" border="0" cellspacing="1" cellpadding="2" id="<?php echo $_cbp_row['c2c_buyback_product_id']; ?>">
                                                <tr>
                                                    <td class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_NAME; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_CUSTOMER_ORDER; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_UNIT_PRICE; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_QUANTITY; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_BALANCE; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_REQUEST_LEVEL; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_CURRENT_LEVEL; ?></td>
                                                    <?php if (in_array($_cb_row['status'], array(1)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                        <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_RECEIVE; ?></td>
                                                    <?php } ?>
                                                    <td width="10%" align="right" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT; ?></td>
                                                </tr>
                                                <tr class="<?php echo $row_style; ?>">
                                                    <td class="ordersRecords">
                                                        <?php echo $_cbp_row['product_name']; ?><br />
                                                        <?php if (!empty($m_ext)) { ?>
                                                            <br />
                                                            <?php foreach ($m_ext as $key => $val) { ?>
                                                                <?php echo ucwords(str_replace('_', ' ', $key)); ?> : <?php echo ($key == 'acc_password' ? '******' : $val['value']); ?><br />
                                                            <?php } ?>
                                                            <br />
                                                        <?php } ?>
                                                    </td>
                                                    <td valign="top" align="center" class="ordersRecords"><a href="<?php echo tep_href_link(FILENAME_ORDERS, 'oID=' . $_cbp_row['orders_id'] . '&action=edit', 'NONSSL'); ?>" target="_blank"><?php echo $_cbp_row['orders_id']; ?></a></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_cbp_row['after_fee_unit_price']; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_cbp_row['purchase_quantity']; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $balance; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo (isset($m_lvl['start_lvl']) && isset($m_lvl['end_lvl']) ? $m_lvl['start_lvl'] . ' - ' . $m_lvl['end_lvl'] : '-'); ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo (isset($m_lvl['current_lvl']) ? $m_lvl['current_lvl'] : '-'); ?></td>
                                                    <?php if (in_array($_cb_row['status'], array(1)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                        <td valign="top" align="center" class="ordersRecords">
                                                            <?php
                                                            echo tep_draw_pull_down_menu('partial_receive_sign[' . $_cbp_row['c2c_buyback_product_id'] . ']', $sign_arr, '', 'id="partial_receive_sign_' . $_cbp_row['c2c_buyback_product_id'] . '"') .
                                                            tep_draw_input_field('partial_receive[' . $_cbp_row['c2c_buyback_product_id'] . ']', 0, ' size="5" id="partial_receive_' . $_cbp_row['c2c_buyback_product_id'] . '" onKeyPress="return noEnterKey(event)"');
                                                            ?>
                                                        </td>
                                                    <?php } ?>
                                                    <td valign="top" align="right" class="ordersRecords"><?php echo $cur_obj->format($payment_amt, false, $_cb_row['currency']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="11"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                                </tr>
                                                <tr>
                                                    <td valign="top" colspan="11" align="right" class="ordersRecords"><?php echo TEXT_BO_TOTAL . $cur_obj->format($payment_total, false, $_cb_row['currency']); ?></td>
                                                </tr>
                                                <?php if ((($_cb_row['status'] == 1) || ($_cb_row['status'] == 2)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                    <tr>
                                                        <td valign="top" colspan="11" align="right">
                                                            <?php
                                                            $_deliver = (isset($m_lvl['current_lvl']) ? $m_lvl['current_lvl'] : $_cbp_row['delivered_quantity']);
                                                            $_request = (isset($m_lvl['end_lvl']) ? $m_lvl['end_lvl'] : $_cbp_row['purchase_quantity']);
                                                            ?>
                                                            <input type="button" value="Update" onClick="javascript: return validateForm(<?php echo $_cbp_row['c2c_buyback_product_id']; ?>, <?php echo $_deliver; ?>, <?php echo $_request; ?>);" class="inputButton">
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="11"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                                    </tr>
                                                    <?php
                                                }
                                                $row_count++;
                                                ?>
                                            </table>
                                        </form>
                                        <?php
                                        break;

                                    default:
                                        $balance = (int) $_cbp_row['purchase_quantity'] - (int) $_cbp_row['delivered_quantity'];
                                        include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                                        $deliver_mode_title = $_cbp_row['products_delivery_mode_title'];
                                        $buyer_character = $_cbp_row['buyer_character'];
                                        $deliver_mode = c2c_buy_order::c2c_order_product_extra_info($_cbp_row['orders_products_id'], 'delivery_mode');
                                        if ($deliver_mode && is_array($deliver_mode)) {
                                            $deliver_mode_title = isset($deliver_mode['name']) ? $deliver_mode['name'] : "";
                                            $deliver_mode_attributes = c2c_buy_order::c2c_order_product_extra_info($_cbp_row['orders_products_id'], 'delivery_mode_attributes');
                                            if ($deliver_mode_attributes && is_array($deliver_mode_attributes)) {
                                                $buyer_character = "";
                                                foreach ($deliver_mode_attributes as $deliver_mode_attributes_value) {
                                                    $buyer_character .= isset($deliver_mode_attributes_value['name']) ? $deliver_mode_attributes_value['label'] . ": " . $deliver_mode_attributes_value['name'] . "<br />" : "";
                                                }
                                            }
                                        }
                                        if($buyer_character == ""){
                                            $trade_url = c2c_buy_order::c2c_order_product_extra_info($_cbp_row['orders_products_id'], 'trade_url');
                                            foreach($trade_url as $k => $v){
                                                $buyer_character = isset($v['value']) ? $v['value'] : '';
                                            }
                                        }
                                                                                    

                                        ?>
                                        <form name="so-receive" id="so-receive" method="post" action="<?php echo FILENAME_C2C_BUYBACK_ORDER . '?action=update&subact=recv&id=' . $id; ?>">
                                            <table width="100%" border="0" cellspacing="1" cellpadding="2" id="<?php echo $_cbp_row['c2c_buyback_product_id']; ?>">
                                                <tr>
                                                    <td class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_NAME; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_CUSTOMER_ORDER; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_TRADE_MODE; ?></td>
                                                    <?php if ($view_buyer_char) { ?>
                                                        <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_RSTK_CHARACTER; ?></td>
                                                    <?php } ?>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_UNIT_PRICE; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_QUANTITY; ?></td>
                                                    <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_BALANCE; ?></td>
                                                    <?php if (in_array($_cb_row['status'], array(1)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                        <td width="10%" align="center" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_RECEIVE; ?></td>
                                                    <?php } ?>
                                                    <td width="10%" align="right" class="ordersBoxHeading"><?php echo TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT; ?></td>
                                                </tr>
                                                <tr class="<?php echo $row_style; ?>">
                                                    <td class="ordersRecords"><?php echo $_cbp_row['product_name']; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><a href="<?php echo tep_href_link(FILENAME_ORDERS, 'oID=' . $_cbp_row['orders_id'] . '&action=edit', 'NONSSL'); ?>" target="_blank"><?php echo $_cbp_row['orders_id']; ?></a></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $deliver_mode_title; ?></td>
                                                    <?php if ($view_buyer_char) { ?>
                                                        <td valign="top" align="center" class="ordersRecords"><?php echo $buyer_character; ?></td>
                                                    <?php } ?>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_cbp_row['after_fee_unit_price']; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_cbp_row['purchase_quantity']; ?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $balance; ?></td>
                                                    <?php if (in_array($_cb_row['status'], array(1)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                        <td valign="top" align="center" class="ordersRecords">
                                                            <?php
                                                            if($_cbp_row['custom_products_type'] != 4){
                                                                echo tep_draw_pull_down_menu('partial_receive_sign[' . $_cbp_row['c2c_buyback_product_id'] . ']', $sign_arr, '', 'id="partial_receive_sign_' . $_cbp_row['c2c_buyback_product_id'] . '"') .
                                                                tep_draw_input_field('partial_receive[' . $_cbp_row['c2c_buyback_product_id'] . ']', 0, ' size="5" id="partial_receive_' . $_cbp_row['c2c_buyback_product_id'] . '" onKeyPress="return noEnterKey(event)"');
                                                            }
                                                            ?>
                                                        </td>
                                                    <?php } ?>
                                                    <td valign="top" align="right" class="ordersRecords"><?php echo $cur_obj->format($payment_amt, false, $_cb_row['currency']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="11"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                                </tr>
                                                <tr>
                                                    <td valign="top" colspan="11" align="right" class="ordersRecords"><?php echo TEXT_BO_TOTAL . $cur_obj->format($payment_total, false, $_cb_row['currency']); ?></td>
                                                </tr>
                                                <?php if ((($_cb_row['status'] == 1) || ($_cb_row['status'] == 2)) && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                                    <tr>
                                                        <td valign="top" colspan="11" align="right"><input type="button" value="Update" onClick="javascript: return validateForm(<?php echo $_cbp_row['c2c_buyback_product_id']; ?>, <?php echo (int) $_cbp_row['delivered_quantity']; ?>, <?php echo (int) $_cbp_row['purchase_quantity']; ?>);" class="inputButton"></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="11"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                                    </tr>
                                                    <?php
                                                }
                                                $row_count++;
                                                ?>
                                            </table>
                                            <?php if(isset($_oph_res)){?>
                                                <table width="100%" border="0" cellspacing="1" cellpadding="2" id="301">
                                                <tbody>
                                                <tr>
                                                    <td width="15%" class="ordersBoxHeading">Delivery Date</td>
                                                    <td width="15%" align="center" class="ordersBoxHeading">Delivery Status</td>
                                                    <td width="10%" align="center" class="ordersBoxHeading">Action By</td>
                                                    <td width="15%" align="center" class="ordersBoxHeading">Buyer Comment</td>
                                                    <td width="15%" align="center" class="ordersBoxHeading">Seller/Admin Comment</td>
                                                    <td width="5%" align="center" class="ordersBoxHeading">Delivery Qty</td>
                                                    <td width="10%" align="center" class="ordersBoxHeading">Action</td>
                                                </tr>
                                                <?php while ($_row = tep_db_fetch_array($_oph_res)) {

                                                    switch($_row['received']){
                                                        case NULL:
                                                            $delivery_status = 'Pending Buyer Confirmation';
                                                        break;
                                                        case 0:
                                                            $delivery_status = 'Buyer Not Received';
                                                        break;
                                                        case 1:
                                                            $delivery_status = 'Buyer Received';
                                                        break;
                                                        case 2:
                                                            $delivery_status = 'Delivery Canceled';
                                                        break;

                                                    }
                                                    ?>

                                                <tr class="ordersListingEven">
                                                    <!-- <td class="ordersRecords"><php echo $_cb_row['orders_products_history_id'] ;?></td>  -->
                                                    <td valign="top" align="left" class="ordersRecords"><?php echo $_row['date_added'] ;?></td>   
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $delivery_status ;?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_row['changed_by'] ;?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo ($_row['dispute_comment'])?$_row['dispute_comment']:'-' ;?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo ($_row['seller_comment'])?$_row['seller_comment']:'-' ;?></td>
                                                    <td valign="top" align="center" class="ordersRecords"><?php echo $_row['delivered_amount'] ;?></td>
                                                    <td valign="top" align="center" class="ordersRecords">
                                                    <?php if($_row['received'] != 2  && $_lock_status && $_cb_row['locked_by'] == $_SESSION['login_id']){?>
                                                        <input type="button" value="Cancel" onclick="javascript: return cancel_delivery(<?php echo $_row['orders_products_history_id']?>);" class="inputButton"></td>
                                                <?php } ?> 
                                                              </tr>
                                                <?php }?>
                                                <tr>
                                                    <td colspan="11"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                                </tr>
                                                    </tbody></table> <?php } ?>
                                        </form>
                                        <?php
                                        break;
                                }
                                ?>
                            <?php } ?>
                        </td>
                    </tr>
                    <tr>
                        <td><?php echo tep_draw_separator(); ?></td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td class="main">
                            <table border="1" cellspacing="0" cellpadding="5">
                                <tr>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_DATE_ADDED; ?></b></td>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_CUSTOMER_NOTIFIED; ?></b></td>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_STATUS; ?></b></td>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_COMMENTS; ?></b></td>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_CHANGED_BY; ?></b></td>
                                    <td class="smallText" align="center"><b><?php echo TABLE_HEADING_ACTION; ?></b></td>
                                </tr>
                                <?php
                                $_cbh_sql = "   SELECT cbh.c2c_buyback_history_id, cbh.date_added, cbh.seller_notified, cbh.comments, cbh.set_as_remarks, cbh.changed_by,
                                                    bs.buyback_status_name
                                                FROM " . TABLE_C2C_BUYBACK_HISTORY . " AS cbh
                                                LEFT JOIN " . TABLE_BUYBACK_STATUS . " AS bs
                                                    ON bs.buyback_status_id = cbh.status
                                                        AND bs.language_id = 1
                                                WHERE cbh.c2c_buyback_id = " . $id . "
                                                    ORDER BY cbh.c2c_buyback_history_id";
                                $_cbh_res = tep_db_query($_cbh_sql);
                                if (tep_db_num_rows($_cbh_res)) {
                                    while ($_cbh_row = tep_db_fetch_array($_cbh_res)) {
                                        $result = preg_match('/__{(.*)}__/', $_cbh_row['comments'], $matches);
                                        if(isset($matches[1])){
                                        $result = json_decode($matches[1],1);
                                        $link = '<a href="' . tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_g2g_seller_reject_proof&path=' . $result['path'] ) . '" target="_blank">View Proof</a>&nbsp;';
                                        $_cbh_row['comments'] =  preg_replace('/__{(.*)}__/', $link, $_cbh_row['comments']);
                                    }
                                        ?>
                                        <tr <?php echo ($_cbh_row['set_as_remarks'] == 1 ? 'class="orderRemarkSelectedRow"' : ''); ?>>
                                            <td class="smallText" align="center">&nbsp;<?php echo $_cbh_row['date_added']; ?></td>
                                            <td class="smallText" align="center">&nbsp;<?php echo ($_cbh_row['seller_notified'] ? tep_image(DIR_WS_ICONS . 'tick.gif') : tep_image(DIR_WS_ICONS . 'cross.gif')); ?></td>
                                            <td class="smallText" align="center">&nbsp;<?php echo $_cbh_row['buyback_status_name']; ?></td>
                                            <td class="smallText" align="left">&nbsp;<?php echo tep_not_null($_cbh_row['comments']) ? nl2br($_cbh_row['comments']) : ''; ?></td>
                                            <td class="smallText" align="center">&nbsp;<?php echo $_cbh_row['changed_by']; ?></td>
                                            <td class="smallText" align="center">&nbsp;
                                                <?php if ($_cbh_row['set_as_remarks'] == 0) { ?>
                                                    <a href="<?php echo tep_href_link(FILENAME_C2C_BUYBACK_ORDER, tep_get_all_get_params() . "action=update&subact=set_remark&id={$id}&hid=" . $_cbh_row['c2c_buyback_history_id']); ?>"><?php echo TEXT_ACTION_SET_BUYBACK_REMARK; ?></a>
                                                <?php } ?>
                                            </td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    ?>
                                    <tr><td colspan="6" class="smallText" align="center"><i>Empty</i></td></tr>
                                <?php } ?>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td>
                            <?php if ($_lock_status && ($_cb_row['locked_by'] == $_SESSION['login_id'])) { ?>
                                <form id="so-comment" name="so-comment" method="post" action="<?php echo FILENAME_C2C_BUYBACK_ORDER . '?action=update&subact=comm&id=' . $id; ?>">
                                    <table border="0" cellspacing="1" cellpadding="2">
                                        <tr class="main">
                                            <td>
                                                <b><?php echo TEXT_REMARKS; ?></b><br />
                                                <?php echo tep_draw_textarea_field('f_comment', 'soft', '60', '5', '', ' id="f_comment" '); ?>
                                            </td>
                                            <td valign="bottom"><?php echo tep_draw_checkbox_field('f_set_remark', '1') . '&nbsp;' . TEXT_ACTION_SET_BUYBACK_REMARK ?></td>
                                        </tr>
                                        <tr class="main">
                                            <td colspan="2">
                                                <?php
                                                $_update_order_status[] = array(
                                                    'id' => 0,
                                                    'text' => SELECT_DEFAULT_EMPTY
                                                );
                                                switch ($_cb_row['status']) {
                                                    case 5: // New order
                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 5, 4)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 4,
                                                                'text' => $f_buyback_status_data[4]
                                                            );
                                                        }
                                                        break;
                                                    case 6: // Preparing for delivery
                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 6, 4)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 4,
                                                                'text' => $f_buyback_status_data[4]
                                                            );
                                                        }
                                                        break;
                                                    case 1: // Delivering
                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 1, 2)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 2,
                                                                'text' => $f_buyback_status_data[2]
                                                            );
                                                        }

                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 1, 4)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 4,
                                                                'text' => $f_buyback_status_data[4]
                                                            );
                                                        }
                                                        break;

                                                    case 2: // Delivered

                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 2, 4) && ($_total_deliver == 0)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 4,
                                                                'text' => $f_buyback_status_data[4]
                                                            );
                                                        }
                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 2, 3)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 3,
                                                                'text' => $f_buyback_status_data[3]
                                                            );
                                                        }
                                                    break;

                                                    case 3: // Completed
                                                        if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 3, 1)) {
                                                            $_update_order_status[] = array(
                                                                'id' => 1,
                                                                'text' => $f_buyback_status_data[1]
                                                            );
                                                        }
//                                                         if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 3, 2)) {
//                                                            $_update_order_status[] = array(
//                                                                'id' => 2,
//                                                                'text' => $f_buyback_status_data[2]
//                                                            );
//                                                        }
                                                        break;

                                                    case 4: // Cancel
                                                        if ($_total_deliver == 0) {
                                                            if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 4, 1)) {
                                                                $_update_order_status[] = array(
                                                                    'id' => 1,
                                                                    'text' => $f_buyback_status_data[1]
                                                                );
                                                            }
                                                        }

                                                        if ($_total_deliver > 0) {
                                                            if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 4, 2)) {
                                                                $_update_order_status[] = array(
                                                                    'id' => 2,
                                                                    'text' => $f_buyback_status_data[2]
                                                                );
                                                            }

                                                            if (tep_check_status_update_permission('B', $_SESSION['login_groups_id'], 4, 3)) {
                                                                $_update_order_status[] = array(
                                                                    'id' => 3,
                                                                    'text' => $f_buyback_status_data[3]
                                                                );
                                                            }
                                                        }
                                                        break;
                                                }
                                                ?>
                                                <b><?php echo TEXT_STATUS; ?>: </b>&nbsp;
                                                <?php
                                                echo tep_draw_pull_down_menu("f_status", $_update_order_status, 0, ' id="f_status"');
                                                echo tep_draw_hidden_field('success_rate_flag', '', 'id="success_rate_flag"');
                                                ?>
                                            </td>
                                        </tr>
                                        <tr class="main">
                                            <td><b><?php echo TEXT_SHOW_MESSAGE_TO; ?>: </b>&nbsp;<?php echo tep_draw_checkbox_field('f_notify', '1'); ?></td>
                                            <td align="right"><input type="button" value="Update" onClick="return status_form_submit(this);//javascript: document.getElementById('so-comment').submit();" class="inputButton"></td>
                                        </tr>
                                    </table>
                                </form>
                            <?php } ?>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                </table>
                <?php
                $listing_html = ob_get_contents();
                ob_end_clean();

                return $listing_html;
            } else {
                $messageStack->add_session(ERROR_RECORD_NOT_EXIST, 'error');
            }
        } else {
            $messageStack->add_session(ERROR_RECORD_NOT_EXIST, 'error');
        }
    }

    public function addEntry($id, $subact) {
        global $messageStack;

        include_once(DIR_WS_CLASSES . 'log.php');
        $log_object = new log_files($_SESSION['login_id']);

        switch ($subact) {
            case 'lock':
                $_sql = "   SELECT locking_trans_id
                            FROM " . TABLE_LOCKING . "
                            WHERE locking_trans_id = " . $id . " AND locking_table_name = '" . TABLE_C2C_BUYBACK . "'";
                $_res = tep_db_query($_sql);
                if (tep_db_num_rows($_res) == 0) {
                    $m_attr = array(
                        'locked_by' => $_SESSION['login_id'],
                        'locked_from_ip' => getenv("REMOTE_ADDR"),
                        'locked_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id='" . $id . "'");
                    unset($m_attr);

                    $m_attr = array(
                        'locking_trans_id' => $id,
                        'locking_table_name' => TABLE_C2C_BUYBACK,
                        'locking_by' => $_SESSION['login_id'],
                        'locking_from_ip' => getenv("REMOTE_ADDR"),
                        'locking_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_LOCKING, $m_attr);
                    unset($m_attr);
                    $log_object->insert_orders_log($id, ORDERS_LOG_LOCK_ORDER, FILENAME_C2C_BUYBACK_ORDER);
                }
                break;

            case 'unlock':
                $_sql = "   SELECT locking_trans_id
                            FROM " . TABLE_LOCKING . "
                            WHERE locking_trans_id = " . $id . " AND locking_table_name = '" . TABLE_C2C_BUYBACK . "'";
                $_res = tep_db_query($_sql);
                if (tep_db_num_rows($_res)) {
                    $m_attr = array(
                        'locked_by' => '',
                        'locked_from_ip' => '',
                        'locked_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id='" . $id . "'");
                    unset($m_attr);

                    $_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($id) . "' AND locking_table_name = '" . tep_db_input(TABLE_C2C_BUYBACK) . "'";
                    tep_db_query($_sql);
                    $log_object->insert_orders_log($id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_C2C_BUYBACK_ORDER);
                }
                break;

            case 'recv':
                include_once(DIR_WS_CLASSES . 'edit_order.php');
                include_once(DIR_WS_CLASSES . 'c2c_order.php');

                $_sql = "   SELECT cbp.c2c_buyback_product_id, cbp.product_name, cbp.custom_products_type,
                                cbp.c2c_products_listing_id, cbp.purchase_quantity, cbp.delivered_quantity, cbp.orders_id,
                                cbp.orders_products_id, cb.seller_id, cb.status,op.final_price,op.products_id, op.products_delivered_quantity, op.products_quantity, o.customers_name, o.date_purchased, o.orders_status as order_status, c.customers_email_address
                            FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                            INNER JOIN " . TABLE_C2C_BUYBACK . " AS cb
                                ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                ON op.orders_products_id = cbp.orders_products_id 
                            INNER JOIN " .TABLE_ORDERS ." AS o 
                                ON o.orders_id = op.orders_id
                            INNER JOIN " .TABLE_CUSTOMERS. " AS c  
                                ON c.customers_id = o.customers_id
                            WHERE cbp.c2c_buyback_id = " . $id;
                $_res = tep_db_query($_sql);
                while ($_row = tep_db_fetch_array($_res)) {
                    $f_sign = $_REQUEST['partial_receive_sign'][$_row['c2c_buyback_product_id']];
                    $f_amount = (int) $_REQUEST['partial_receive'][$_row['c2c_buyback_product_id']];
                    if($f_amount <= 0){
                        $messageStack->add_session('Update quantity must be more than 0', 'error');
                        return false;
                    }
                    $_update = false;

                    $m_lvl = self::c2c_buyback_product_extra_info($_row['c2c_buyback_product_id']);
                    if (($_row['custom_products_type'] == 1) && isset($m_lvl['current_lvl'])) {
                        if ($f_sign == '+') {
                            $_cur_lvl = ($m_lvl['current_lvl'] + $f_amount);
                            if ($m_lvl['end_lvl'] >= $_cur_lvl) {
                                $m_lvl['current_lvl'] = $_cur_lvl;
                                $f_amount = ($f_amount / ($m_lvl['end_lvl'] - $m_lvl['start_lvl']));

                                $_delivered = $_row['delivered_quantity'] + $f_amount;
                                $_update = true;
                            }
                        } else if ($f_sign == '-') {
                            $_cur_lvl = $m_lvl['current_lvl'] - $f_amount;
                            if ($_cur_lvl >= $m_lvl['start_lvl']) {
                                $m_lvl['current_lvl'] = $_cur_lvl;
                                $f_amount = ($f_amount / ($m_lvl['end_lvl'] - $m_lvl['start_lvl']));

                                $_delivered = $_row['delivered_quantity'] - $f_amount;
                                $_update = true;
                            }
                        }
                    } else {
                        if ($f_sign == '+') {
                            $_delivered = $_row['delivered_quantity'] + $f_amount;
                            $_update = ($_row['purchase_quantity'] >= $_delivered ? true : false);
                        } else if ($f_sign == '-') {
                            $_delivered = $_row['delivered_quantity'] - $f_amount;
                            $_update = ($_delivered >= 0 ? true : false);
                        }
                    }

                    if (tep_not_empty($_delivered) && $_update) {
                        $qty = round(floatval($f_sign . $f_amount), 2);

                        // PWL
                        if (($_row['custom_products_type'] == 1) && isset($m_lvl['current_lvl'])) {
                            $m_attr = array('extra_info_value' => json_encode($m_lvl));
                            tep_db_perform(TABLE_C2C_BUYBACK_PRODUCT_EXTRA_INFO, $m_attr, 'update', ' c2c_buyback_product_id = ' . $_row['c2c_buyback_product_id']);
                            unset($m_attr);
                        }

                        # update sell-order
                        $m_attr = array('delivered_quantity' => round($_delivered, 2));
                        tep_db_perform(TABLE_C2C_BUYBACK_PRODUCT, $m_attr, 'update', ' c2c_buyback_product_id = ' . $_row['c2c_buyback_product_id']);
                        unset($m_attr);

                        $edit_order_obj = new edit_order($_SESSION['login_id'], $_SESSION['login_email_address'], $_row['orders_id']);
                        $edit_order_obj->buyback_deliver_order(abs($qty), $id, $_row['orders_products_id'], $_SESSION['login_email_address'], true, $messageStack, $f_sign);
                        $m_attr = array(
                            'orders_id' => $_row['orders_id'],
                            'orders_status_id' => 0,
                            'date_added' => 'now()',
                            'customer_notified' => 0,
                            'comments' =>  TEXT_LOG_ITEM_UPDATED . "\n" . $_row['product_name'] . " x " . $qty,
                            'comments_type' => 0,
                            'set_as_order_remarks' => 0,
                            'changed_by' => $_SESSION['login_email_address']
                        );
                        tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr, 'insert');
                        if($f_sign == '+'){
                            // Insert delivery
                            $insert_orders_products_history = "	INSERT INTO " . TABLE_ORDERS_PRODUCTS_HISTORY . " SET
                            buyback_request_group_id = '" . (int) $id . "',
                            orders_id = " . (int) $_row['orders_id'] . ",
                            orders_products_id = '" . (int) $_row['orders_products_id'] . "',
                            date_added = now(),
                            last_updated = now(),
                            received = null,
                            rolled_back = '" . (int) 0 . "',
                            date_confirm_delivered = '".Date('Y-m-d h:i:s', strtotime("+3 days"))."',
                            delivered_amount = '" . tep_db_input($f_amount) . "',
                            delivered_character = '" . tep_db_input($char_name) . "',
                            changed_by = '". tep_db_input($_SESSION['login_email_address'])."';";
                            $result = tep_db_query($insert_orders_products_history);
                            $oph_id = tep_db_insert_id();
                            
                            //insert buyer remark
                            $action = ($_row['custom_products_type'] == 4)? 'delivery_hla': 'delivery';
                            $comment = ['action' => $action, 'translate_key' => 'TEXT_SELLER_DELIVERED', 'params' => ['oph_id' => $oph_id, 'total_delivered_qty' => $_row['products_delivered_quantity'], 'products_quantity' => $_row['products_quantity']]];
                            $m_attr = array(
                                'orders_id' =>  $_row['orders_id'],
                                'orders_status_id' => 0,
                                'date_added' => 'now()',
                                'customer_notified' => 0,
                                'comments' => json_encode($comment),
                                'comments_type' => 5,
                                'set_as_order_remarks' => 0,
                                'changed_by' => $_SESSION['login_email_address']
                            );
                            tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr, 'insert');
                            # remove HLA info
                            if ($_row['custom_products_type'] == 4) {
                                $remove_hla_info_sql = "DELETE FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id = '" . tep_db_input($_row['orders_products_id']) . "' AND (orders_products_extra_info_key LIKE 'hla_info_stage_%')";
                                tep_db_query($remove_hla_info_sql);
                            }
                            
                            $_cpl_sql = "   SELECT c2c_products_listing_id
                            FROM " . TABLE_C2C_PRODUCTS_LISTING . "
                            WHERE c2c_products_listing_id = " . $_row['c2c_products_listing_id'];
                            $_cpl_res = tep_db_query($_cpl_sql);
                            if (tep_db_num_rows($_cpl_res)) {
                                c2c_order::stockMovement($_row['c2c_products_listing_id'], 3, ($f_sign == '+' ? '-' : '+'), $f_amount, $_row['orders_id'], 'c2c_buyback_order');
                            }
                            
                            // if delivery is meet full quantity, move to delivered
                            $cb_sql = "   SELECT cbp.purchase_quantity, cbp.delivered_quantity, cb.status
                            FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                            INNER JOIN " . TABLE_C2C_BUYBACK . " AS cb
                            ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                            WHERE cbp.c2c_buyback_id = " . $id;
                            $cb_res = tep_db_query($cb_sql);
                            if ($cb_row = tep_db_fetch_array($cb_res)) {
                                
                                if ($cb_row['status'] == 1 && $cb_row['purchase_quantity'] == $cb_row['delivered_quantity']) {
                                    //move so to delivering
                                    $m_attr = array('status' => 2);
                                    tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id= '" . (int) $id . "'");
                                    $m_attr = array(
                                        'c2c_buyback_id' => $id,
                                        'c2c_buyback_product_id' => $_row['c2c_buyback_product_id'],
                                        'status' => 2,
                                        'date_added' => 'now()',
                                        'seller_notified' => 0,
                                        'comments' => '',
                                        'set_as_remarks' => 0,
                                        'changed_by' => $_SESSION['login_email_address']
                                    );
                                    tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');
                                }
                            }
                            $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
                            while ($configuration = tep_db_fetch_array($configuration_query)) {
                                define($configuration['cfgKey'], $configuration['cfgValue']);
                            }
                            
                            $store_mail = G2G_STORE_OWNER_EMAIL_ADDRESS;
                            $store_owner = G2G_STORE_OWNER;
                            $email_subject = 'Order Update #'.$_row['orders_id'];
                            $email_template = 'Dear %s, <br /><br />Order Number : %s <br />Order Date : %s <br />Detailed Invoice : %s <br /><br />Status : %s <br /><br />Comment : <br />The following items have been delivered: <br />&raquo %s <br /><br />For any enquiries or assistance, you may use our Online Live Support service or e-mail your enquiries to %s. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping at %s. <br /><br /><br />';
                            $email_content = sprintf($email_template, $_row['customers_name'], $_row['orders_id'], $_row['date_purchased'], HTTPS_G2G_STORE . '/order/buyOrder/order?oid=' . $_row['orders_id'], 'Processing',$_row['product_name'] .' x '. $qty, $store_mail, $store_owner);
                            tep_mail($_row['customers_name'] , $_row['customers_email_address'], $email_subject, $email_content, $store_owner, $store_mail);

                            $m_attr = array(
                                'c2c_buyback_id' => $id,
                                'c2c_buyback_product_id' => $_row['c2c_buyback_product_id'],
                                'status' => 0,
                                'date_added' => 'now()',
                                'seller_notified' => 1,
                                'comments' => TEXT_LOG_ITEM_UPDATED . "\n" . $_row['product_name'] . " x " . $qty,
                                'orders_products_history_id' => $oph_id,
                                'changed_by' => $_SESSION['login_email_address']
                            );
                        } else {
                            $m_attr = array(
                                'c2c_buyback_id' => $id,
                                'c2c_buyback_product_id' => $_row['c2c_buyback_product_id'],
                                'status' => 0,
                                'date_added' => 'now()',
                                'seller_notified' => 0,
                                'comments' => TEXT_LOG_ITEM_DEDUCTED . "\n" . $_row['product_name'] . " x " . $qty,
                                'orders_products_history_id' => $oph_id,
                                'changed_by' => $_SESSION['login_email_address']
                            );
                        }
                      
                        tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');
                    }
                }
                
            break;

            case 'comm':
                $f_comment = (isset($_REQUEST['f_comment']) ? $_REQUEST['f_comment'] : '');
                $f_set_remark = (int) (isset($_REQUEST['f_set_remark']) ? $_REQUEST['f_set_remark'] : '');
                $f_status = (int) (isset($_REQUEST['f_status']) ? $_REQUEST['f_status'] : '');
                $f_notify = (int) (isset($_REQUEST['f_notify']) ? $_REQUEST['f_notify'] : '');
                $success_rate_flag = (int) (isset($_REQUEST['success_rate_flag']) ? $_REQUEST['success_rate_flag'] : '');

                $_sel = "   SELECT cb.status, cbp.c2c_buyback_product_id
                            FROM " . TABLE_C2C_BUYBACK . " AS cb
                            INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                                ON cbp.c2c_buyback_id = cb.c2c_buyback_id
                            WHERE cb.c2c_buyback_id = " . (int) $id;
                $_res = tep_db_query($_sel);

                if ($_row = tep_db_fetch_array($_res)) {
                    if (tep_not_null($f_comment)) {
                        tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $id);

                        $m_attr = array(
                            'c2c_buyback_id' => $id,
                            'c2c_buyback_product_id' => $_row['c2c_buyback_product_id'],
                            'status' => 0,
                            'date_added' => 'now()',
                            'seller_notified' => $f_notify,
                            'comments' => $f_comment,
                            'set_as_remarks' => $f_set_remark,
                            'changed_by' => $_SESSION['login_email_address']
                        );
                        tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');
                        unset($m_attr);
                    }

                    # update order status
                    if ($f_status) {
                        if ($f_status != '4' || ($f_status == "4" && $success_rate_flag >= 0)) {
                            $result = self::_pre_order_rule($id, $_row['status'], $f_status, $success_rate_flag);
                            //recalculate total orders based on scenario
                            if ($result) {
                                $messageStack->add_session($result, 'error');
                            } else {
                                $m_attr = array(
                                    'c2c_buyback_id' => $id,
                                    'c2c_buyback_product_id' => $_row['c2c_buyback_product_id'],
                                    'status' => $f_status,
                                    'date_added' => 'now()',
                                    'seller_notified' => 0,
                                    'comments' => '',
                                    'set_as_remarks' => 0,
                                    'changed_by' => $_SESSION['login_email_address']
                                );

                                if ($f_status == "4" && $success_rate_flag >= 0) {
                                    $change_flag_msg = "Is this cancellation due to Seller's fault? ";
                                    if ($success_rate_flag == "0") {
                                        $change_flag_msg .= ": NO";
                                    } else {
                                        $change_flag_msg .= ": YES";
                                    }
                                    $m_attr['comments'] = $change_flag_msg;
                                }

                                tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');
                                unset($m_attr);
                                $messageStack->add_session(MESSAGE_UPDATE_SUCCESS, 'success');
                            }
                        } else {
                            $messageStack->add_session('Fail to update status', 'error');
                        }
                    }

                    if ($f_status || $f_notify) {
                        self::_send_update_mail($id, 'M', $_row['status'], $f_comment);
                    }
                }
                break;

            case 'set_remark':
                if (isset($_GET['hid']) && !empty($_GET['hid'])) {
                    tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $id);
                    tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 1 WHERE c2c_buyback_history_id = " . (int) $_GET['hid']);
                }
                break;

            case 'reverse_fault':
                if(isset($_GET['id']) && !empty($_GET['id']) && isset($_GET['fault'])){
                    $_sql = "   SELECT c2c_buyback_id, seller_id, success_rate_flag FROM ". TABLE_C2C_BUYBACK . " AS cb   WHERE cb.c2c_buyback_id = " . $id;
                    $_res = tep_db_query($_sql);
                    if ($_row = tep_db_fetch_array($_res)) {
                        $new_success_flag = !$_row['success_rate_flag'];
                        tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET success_rate_flag = ". (int)$new_success_flag ."  WHERE c2c_buyback_id = " . (int) $id);
                        $comment = ($new_success_flag) ? 'Set as seller fault' : 'Set as not seller fault';
                        $m_attr = array(
                            'c2c_buyback_id' => $id,
                            'c2c_buyback_product_id' => 0,
                            'status' => 0,
                            'date_added' => 'now()',
                            'seller_notified' => 0,
                            'comments' => $comment ,
                            'changed_by' => $_SESSION['login_email_address']
                        );
                        tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr, 'insert');  
                        if($new_success_flag){
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so+1 where seller_id = '" . $_row['seller_id'] . "'";
                        } else {
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so-1 where seller_id = '" . $_row['seller_id'] . "'";
                        }
                        tep_db_query($csr_update);
                    }
                }
                break;
            
        }
    }

    public static function _get_buyback_status() {
        $data = array();

        $_sel = "   SELECT buyback_status_id, buyback_status_name
                    FROM " . TABLE_BUYBACK_STATUS . "
                    WHERE language_id = 1
                    ORDER BY buyback_status_sort_order ASC";
        $_res = tep_db_query($_sel);
        while ($_row = tep_db_fetch_array($_res)) {
            $data[$_row['buyback_status_id']] = $_row['buyback_status_name'];
        }

        return $data;
    }

    public static function _get_customer_detail($id) {
        $data = array();

        if (tep_not_null($id)) {
            include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOMERS_INFO_VERIFICATION);

            $_seller_dob = '';

            $_seller_sel_sql = "SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, c.customers_dob, c.customers_email_address,
									c.customers_country_dialing_code_id, c.customers_telephone, c.customers_mobile,
									cc.username, cc.seller_status, cc.seller_group_id, csg.seller_group_name,
									ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_state, ab.entry_country_id AS country_id,
									ct.address_format_id
								FROM " . TABLE_CUSTOMERS . " AS c
								LEFT JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
									ON cc.customers_id = c.customers_id
								LEFT JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
									ON csg.seller_group_id = cc.seller_group_id
								LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
									ON ab.customers_id = c.customers_id
								LEFT JOIN " . TABLE_COUNTRIES . " AS ct
									ON ct.countries_id = ab.entry_country_id
								WHERE c.customers_id = '" . $id . "'";
            $_seller_res_sql = tep_db_query($_seller_sel_sql);
            $_seller_row = tep_db_fetch_array($_seller_res_sql);

            # Address
            $data['seller_address'] = tep_address_format($_seller_row['address_format_id'], $_seller_row, 1, '', '<br />', '', false);

            $user_im = '';
            $im_select_sql = "SELECT imt.instant_message_type_name, ima.instant_message_userid, ima.instant_message_type_id, ima.instant_message_remarks
							FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " AS ima
							LEFT JOIN " . TABLE_INSTANT_MESSAGE_TYPE . " AS imt
								ON (ima.instant_message_type_id = imt.instant_message_type_id)
							WHERE ima.customer_id = '" . (int) $id . "'
								AND ima.instant_message_userid <> ''";
            $im_select_res_sql = tep_db_query($im_select_sql);
            while ($im_select_row = tep_db_fetch_array($im_select_res_sql)) {
                if ($im_select_row['instant_message_type_id'] != 0) {
                    $user_im .= '<b>' . $im_select_row['instant_message_type_name'] . ':</b> ' . $im_select_row['instant_message_userid'] . '<br/>';
                } else {
                    $user_im .= '<b>' . $im_select_row['instant_message_remarks'] . ':</b> ' . $im_select_row['instant_message_userid'] . '<br/>';
                }
            }
            # IM
            $data['seller_im'] = (tep_not_null($user_im) ? $user_im : TEXT_NOT_AVAILABLE);

            # DOB
            if (isset($_seller_row['customers_dob']) && !empty($_seller_row['customers_dob'])) {
                $dob_style = '';
                $_seller_age = tep_calculate_age($_seller_row['customers_dob'], '', 0);

                if (preg_match('/([\d ]+)(yr)/is', $_seller_age, $regs)) {
                    if ((int) trim($regs[1]) < 17) {
                        $dob_style = ' class="redIndicator" ';
                    }
                } else {
                    $dob_style = ' class="redIndicator" ';
                }

                $_seller_dob = '<span ' . $dob_style . '>' . tep_date_short($_seller_row['customers_dob'], PREFERRED_DATE_FORMAT) . ' (' . $_seller_age . ') </span>';
            }

            # Telephone
            $dialing_code = tep_format_telephone($id);
            $_seller_telephone = $dialing_code['country_international_dialing_code'] . ' ' . tep_parse_telephone($_seller_row['customers_telephone'], $_seller_row['customers_country_dialing_code_id'], 'id');
            $_seller_telephone = ($_seller_telephone != ' ' ? '+' . $_seller_telephone : TEXT_NOT_AVAILABLE);

            $data['seller_name'] = (tep_not_null($_seller_row['customers_firstname']) && tep_not_null($_seller_row['customers_lastname']) ? '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $id . '&action=edit') . '" target="_blank">' . $_seller_row['customers_firstname'] . ' ' . $_seller_row['customers_lastname'] . '</a>'. ' ('. $id .')<br />' : TEXT_NOT_AVAILABLE);
            $data['seller_username'] = tep_not_null($_seller_row['username']) ? $_seller_row['username'] : TEXT_NOT_AVAILABLE;
            $data['seller_status'] = tep_not_null($_seller_row['seller_status']) ? ($_seller_row['seller_status'] == 1 ? ACTIVE : NOT_ACTIVE) : TEXT_NOT_AVAILABLE;
            $data['seller_group_name'] = tep_not_null($_seller_row['seller_group_name']) ? $_seller_row['seller_group_name'] : TEXT_NOT_AVAILABLE;
            $data['customers_gender'] = ($_seller_row['customers_gender'] == 'm' ? TEXT_MALE : ($_seller_row['customers_gender'] == 'f' ? TEXT_FEMALE : TEXT_NOT_AVAILABLE));
            $data['seller_dob'] = tep_not_null($_seller_dob) ? $_seller_dob : TEXT_NOT_AVAILABLE;
            $data['customers_email_address'] = tep_not_null($_seller_row['customers_email_address']) ? $_seller_row['customers_email_address'] : TEXT_NOT_AVAILABLE;
            $data['seller_telephone'] = $_seller_telephone;
            $data['customers_mobile'] = (tep_not_null($_seller_row['customers_mobile']) ? $_seller_row['customers_mobile'] : TEXT_NOT_AVAILABLE);
        }

        return $data;
    }

    public static function _add_tag($id, $status_id) {
        $_sel = "SELECT orders_tag_ids FROM " . TABLE_C2C_BUYBACK . " WHERE c2c_buyback_id = " . $id;
        $_res = tep_db_query($_sel);
        if ($_row = tep_db_fetch_array($_res)) {
            $_tags = explode(',', $_row['orders_tag_ids']);

            $_ot_sel = "	SELECT orders_tag_id
                            FROM " . TABLE_ORDERS_TAG . "
                            WHERE FIND_IN_SET(orders_tag_id, '" . $_row["orders_tag_ids"] . "')
                                AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
                                AND filename='" . tep_db_input(FILENAME_BUYBACK_REQUESTS) . "'";
            $_ot_res = tep_db_query($_ot_sel);
            while ($_ot_row = tep_db_fetch_array($_ot_res)) {
                if (!in_array($_ot_row['orders_tag_id'], $_tags)) {
                    $_tags[] = $_ot_row['orders_tag_id'];
                }
            }

            if (!empty($_tags)) {
                tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids = '" . implode(',', $_tags) . "' WHERE c2c_buyback_id = " . tep_db_input($id));
            }
        } else {
            tep_db_query("UPDATE " . TABLE_C2C_BUYBACK . " SET orders_tag_ids = '" . $tag_id . "' WHERE c2c_buyback_id = " . tep_db_input($id));
        }
    }

    public static function c2c_buyback_product_extra_info($opid, $key = 'SO') {
        $result = array();

        $_sql = "SELECT extra_info_value FROM " . TABLE_C2C_BUYBACK_PRODUCT_EXTRA_INFO . " WHERE c2c_buyback_product_id = " . $opid . " AND extra_info_key = '" . $key . "'";
        $_res = tep_db_query($_sql);
        if ($_row = tep_db_fetch_array($_res)) {
            $result = json_decode($_row['extra_info_value'], true);
            if (empty($result)) {
                $result = $_row['extra_info_value'];
            }
        }

        return $result;
    }

    public static function _chat_permission($soid, $opid, $type) {
        //disabled API to old chat 
        return true;
        $response = '';

        $CHAT_CREATE_URL = CHAT_URL . '/api/addChatAccess';
        $CHAT_CHECK_URL = CHAT_URL . '/api/checkChatAccess';
        $CHAT_DELETE_URL = CHAT_URL . '/api/removeChatAccess';

        if (!empty($type)) {
            include_once(DIR_WS_CLASSES . 'curl.php');
            $curl_obj = new curl();

            switch ($type) {
                case 'create':
                    include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                    $g2g_prod_info = c2c_buy_order::c2c_order_product_extra_info($opid, 'listing');
                    if (!empty($g2g_prod_info)) {
                        $op_sql = " SELECT o.orders_id, o.customers_id
                            FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                            INNER JOIN " . TABLE_ORDERS . " AS o
                                ON o.orders_id = op.orders_id
                            WHERE op.orders_products_id = " . $opid;
                        $op_res = tep_db_query($op_sql);
                        if ($op_row = tep_db_fetch_array($op_res)) {
                            $extra = array(
                                array(
                                    'url' => 'db:~:user1_displayname',
                                    'value' => HTTPS_G2G_STORE . '/order/sellOrder/order?oid=' . $soid
                                ),
                                array(
                                    'url' => 'db:~:user2_displayname',
                                    'value' => HTTPS_G2G_STORE . '/order/buyOrder/order?oid=' . $op_row['orders_id']
                                ),
                                array(
                                    'text' => 'Title',
                                    'value' => $g2g_prod_info['products_title']
                                )
                            );

                            $data = array(
                                'ApiKey' => CHAT_API_KEY,
                                'transactionId' => $soid, // sell order id
                                'transactionType' => 5, // site-id
                                'user1Id' => $g2g_prod_info['seller_id'], // seller customer id
                                'user1Displayname' => $soid, // sell order id
                                'user2Id' => $op_row['customers_id'], // buyer customer id
                                'user2Displayname' => $op_row['orders_id'], // customer order id
                                'extraInfo' => json_encode($extra)
                            );
                            $response = $curl_obj->curl_post($CHAT_CREATE_URL, $data);
                        }
                    }
                    break;

                case 'check':
                    $data = array(
                        'apiKey' => CHAT_API_KEY,
                        'transactionId' => $soid,
                        'transactionType' => 5
                    );
                    $response = $curl_obj->curl_post($CHAT_CHECK_URL, $data);
                    break;

                case 'delete':
                    $data = array(
                        'apiKey' => CHAT_API_KEY,
                        'transactionId' => $soid,
                        'transactionType' => 5
                    );
                    $response = $curl_obj->curl_post($CHAT_DELETE_URL, $data);
                    break;
            }
        }

        return $response;
    }

    public static function _create_buyback_order($opid) {
        /*
         * error code
         * 1 : G2G product info in order-product-extra-info not found
         * 2 : Product margin not found
         * 3 : fail to create sell order
         * 4 : sell order already exist
         * 5 : order locked by other
         * 6 : not able to release order lock
         */

        include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);

        $bo_id = false;
        $_err = array();
        $cur_obj = new currencies();
        $lock_obj = new process_locking();

        $scDataRequest = array(
            'order_id' => (string) $opid,
            'activity' => 'P',
        );
        $scData = g2g_serverless::getScAllTransactions($scDataRequest);
        foreach ($scData as $sch_row) {
            if($sch_row['new_amount'] < -0.05){
                $remarks_comment = "Not able to create Sell Order. <br />Purhcased order with negetive Store credit. <br /> RSC balance:".$sch_row['new_amount']." <br /> NRSC balance: " . $sch_row['new_amount'] . " <br />Please refund and do not proceed";
                # Order History comment
                $m_attr = array(
                    'orders_id' => $sch_row['order_id'],
                    'orders_status_id' => 0,
                    'date_added' => 'now()',
                    'customer_notified' => 0,
                    'comments' => $remarks_comment,
                    'comments_type' => 0,
                    'set_as_order_remarks' => 0,
                    'changed_by' => (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : self::CHANGE_BY)
                );
                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr);
                unset($m_attr);
               return array(
                    'errno' => 9,
                    'error' => $remarks_comment
                );
            }
        }
        
        $g2g_prod_info = c2c_buy_order::c2c_order_product_extra_info($opid, 'listing');
        if (!empty($g2g_prod_info)) {
            $_row = self::_retrieve_buyback_id($opid);
            if (empty($_row)) {
                $op_sql = " SELECT op.orders_id, op.products_quantity, op.products_name,
                                o.customers_id, o.date_purchased, op.orders_products_store_price
                            FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                            INNER JOIN " . TABLE_ORDERS . " AS o
                                ON o.orders_id = op.orders_id
                            WHERE op.orders_products_id = " . $opid;
                $op_res = tep_db_query($op_sql);
                $op_row = tep_db_fetch_array($op_res);
                $listing_unit_price = $cur_obj->advance_currency_conversion($g2g_prod_info['products_price'], $g2g_prod_info['products_base_currency'], DEFAULT_CURRENCY, false, 'sell'); // USD
                $order_unit_price = $op_row['orders_products_store_price']; // USD
                $value_difference = number_format($listing_unit_price - $order_unit_price, 10);
                //If listing's unit price smaller than order's unit price then just proceed
                if($value_difference > 0){
                    if ($value_difference > ($order_unit_price*0.05)) {
                        $remarks_comment = "Not able to create Sell Order. <br /> Sold Order's unit price (USD " .$listing_unit_price. ") is higher than Purchase Order's unit price  (USD ".$order_unit_price.") <br />Please refund and do not proceed";
                        # Order History comment
                        $m_attr = array(
                            'orders_id' => $op_row['orders_id'],
                            'orders_status_id' => 0,
                            'date_added' => 'now()',
                            'customer_notified' => 0,
                            'comments' => $remarks_comment,
                            'comments_type' => 0,
                            'set_as_order_remarks' => 0,
                            'changed_by' => (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : self::CHANGE_BY)
                        );
                        tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr);
                        unset($m_attr);
                        include_once(DIR_WS_CLASSES . 'slack_notification.php');
                        $slack = new slack_notification();
                        $text = "Order ID: " . $op_row['orders_id'] . " ~ ".$remarks_comment;
                        $data = json_encode(array(
                            'text' => '[OG Crew] - Prevent Create SO ' . date("F j, Y H:i"),
                            'channel' => G2G_SLACK_WEBHOOK_DEV_DEBUG_CHANNEL,
                            'attachments' => array(
                                array(
                                    'color' => 'warning',
                                    'text' => $text
                                )
                            )
                        ));
                        $slack->send(G2G_SLACK_WEBHOOK_DEV_DEBUG_ID, $data);
                        return array(
                                'errno' => 9,
                                'error' => $remarks_comment
                            );  
                    }   
                }
                # verify lock status
                $tmp_lock = $lock_obj->isLocked($op_row['orders_id'], self::MOVE_ORDER, false);
                $admin_id = (isset($_SESSION['login_id']) && !empty($_SESSION['login_id']) ? $_SESSION['login_id'] : self::IDENTITY);
                $lock_sql = "   SELECT o.orders_locked_by, o.orders_locked_from_ip, o.orders_locked_datetime, a.admin_email_address
                                FROM " . TABLE_ORDERS . " AS o
                                LEFT JOIN " . TABLE_ADMIN . " AS a
                                    ON (o.orders_locked_by = a.admin_id)
                                WHERE orders_id = '" . $op_row['orders_id'] . "'";
                $lock_res = tep_db_query($lock_sql);
                $lock_row = tep_db_fetch_array($lock_res);
                if (!$tmp_lock || ($tmp_lock && ($lock_row['orders_locked_by'] == $admin_id))) {
                    # release lock
                    if ($lock_obj->releaseLocked($op_row['orders_id'], self::MOVE_ORDER, $admin_id)) {
                        tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_id = '" . $op_row['orders_id'] . "'");

                        $margin = 0;
                        # calculate product price with supplier margin
                        $cpm_sql = "SELECT cpm.payout_percentage
                        FROM " . TABLE_C2C_PRODUCT_MARGIN . " AS cpm
                        INNER JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
                            ON cpm.seller_group_id = cc.seller_group_id
                        WHERE cc.customers_id = " . (int) $g2g_prod_info['seller_id'] . "
                            AND cpm.game_id = " . (int) $g2g_prod_info['game_id'] . "
                            AND cpm.custom_products_type_child_id = " . (int) $g2g_prod_info['custom_products_type_child_id'];
                        $cpm_res = tep_db_query($cpm_sql);
                        if ($cpm_row = tep_db_fetch_array($cpm_res)) {
                            $margin = $cpm_row['payout_percentage'];
                        } else {
                            // use default margin
                            $cc_sql = " SELECT configuration_value
                                FROM " . TABLE_C2C_CONFIGURATION . "
                                WHERE configuration_key = 'C2C_DEFAULT_PRODUCT_MARGIN'";
                            $cc_res = tep_db_query($cc_sql);
                            if ($cc_row = tep_db_fetch_array($cc_res)) {
                                $margin = $cc_row['configuration_value'];
                            }
                        }

                        if (!empty($margin)) {
                            // 2nd verify BO not created yet
                            $_row = self::_retrieve_buyback_id($opid);
                            if (empty($_row)) {
                                # create Buyback Order
                                $m_attr = array(
                                    'seller_id' => $g2g_prod_info['seller_id'],
                                    'status' => 5,
                                    'date_added' => 'now()',
                                    'currency' => $g2g_prod_info['products_base_currency'],
                                    'currency_value' => $cur_obj->advance_currency_conversion_rate($g2g_prod_info['products_base_currency'], DEFAULT_CURRENCY, 'buy')
                                );
                                tep_db_perform(TABLE_C2C_BUYBACK, $m_attr);
                                $bo_id = tep_db_insert_id();
                                unset($m_attr);

                                if (!empty($op_row) && !empty($bo_id)) {
                                    //get seller reputation
                                    $csr_sql = "SELECT seller_id
                                                FROM " . TABLE_C2C_SELLER_REPUTATION . " 
                                                WHERE seller_id = " . (int) $g2g_prod_info['seller_id'];
                                    $csr_res = tep_db_query($csr_sql);
                                    //total orders +1 at table c2c_seller_reputation
                                    if ($csr_row = tep_db_fetch_array($csr_res)) {
                                        $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so+1 where seller_id = '" . $csr_row['seller_id'] . "'";
                                        tep_db_query($csr_update);
                                    } else {
                                        $m_attr = array(
                                            'seller_id' => $g2g_prod_info['seller_id'],
                                            'total_so' => 1,
                                            'success_so' => 0
                                        );
                                        tep_db_perform(TABLE_C2C_SELLER_REPUTATION, $m_attr);
                                    }

                                    $char_info = c2c_buy_order::c2c_order_product_extra_info($opid, 'char_name');
                                    $deliver_mode = c2c_buy_order::c2c_order_product_extra_info($opid, 'delivery_mode');

                                    if ($deliver_mode && is_array($deliver_mode)) {
                                        $deliver_mode = isset($deliver_mode['value']) ? $deliver_mode['value'] : "";
                                    }

                                    $purchase_quantity = $op_row['products_quantity'];
                                    $comm = $g2g_prod_info['products_price'] * $margin / 100;
                                    $after_fee_unit_price = $g2g_prod_info['products_price'] - $comm;
                                    // call serverless api here
                                    // Fetch Billing Country
                                    // Fetch Billing Country from orders_products_extra_info
                                    
                                    $op_x_info_sql = "SELECT * FROM ".TABLE_ORDERS_PRODUCTS_EXTRA_INFO." WHERE orders_products_id =  ".$opid." AND orders_products_extra_info_key IN ('seller_info_signup_country_iso_2','seller_info_signup_ip','seller_info_login_ip','seller_info_login_country_iso_2')";
                                    $op_x_info_result = tep_db_query($op_x_info_sql);
                                    $op_extra_info =array();
                                    while ($op_x_info = tep_db_fetch_array($op_x_info_result)) {
                                        $op_extra_info[$op_x_info['orders_products_extra_info_key']] = $op_x_info['orders_products_extra_info_value'];
                                    }

                                    $sign_up_country = '';
                                    if(!empty($op_extra_info['seller_info_signup_country_iso_2'])){
                                        $sign_up_country = $op_extra_info['seller_info_signup_country_iso_2'];
                                    }else if(empty($op_extra_info['seller_info_signup_ip'])){
                                        $ip_country_info = tep_get_ip_country_info($op_extra_info['seller_info_signup_ip']);
                                        $sign_up_country = $ip_country_info['countries_iso_code_2'];
                                    }else{
                                        //orders_products_extra_info country and ip does not exist

                                        // Fetch Signup Country from customer_info
                                        $customer_info_sql = "SELECT * FROM ".TABLE_CUSTOMERS_INFO." WHERE customers_info_id = ".$g2g_prod_info['seller_id']."";
                                        $customer_info_result = tep_db_query($customer_info_sql);
                                        if($customer_info_row = tep_db_fetch_array($customer_info_result)){
                                            $sign_up_country = $customer_info_row['account_created_country'];
                                            if($sign_up_country == null){
                                                //try the ip address
                                                $sign_up_country_ip = $customer_info_row['customers_info_account_created_ip'];
                                                if(!empty($sign_up_country_ip)){
                                                    $ip_country_info = tep_get_ip_country_info($sign_up_country_ip);
                                                    $sign_up_country = $ip_country_info['countries_iso_code_2'];
                                                }

                                            }
                                        }
                                    }

                                    $login_country = '';
                                    if(!empty($op_extra_info['seller_info_login_country_iso_2'])){
                                        $login_country = $op_extra_info['seller_info_login_country_iso_2'];
                                    }else if(!empty($op_extra_info['seller_info_login_ip'])){
                                        $ip_country_info = tep_get_ip_country_info($op_extra_info['seller_info_login_ip']);
                                        $login_country = $ip_country_info['countries_iso_code_2'];
                                    }else{
                                        //orders_products_extra_info country and ip does not exist

                                        // Fetch Login IP from history
                                        $customer_login_sql = "SELECT * FROM ".TABLE_CUSTOMERS_LOGIN_IP_HISTORY." WHERE customers_id = ".$g2g_prod_info['seller_id']." ORDER BY customers_login_date DESC LIMIT 1";
                                        $customer_login_result = tep_db_query($customer_login_sql);
                                        if($customer_login_row = tep_db_fetch_array($customer_login_result)){
                                            $login_country_ip = $customer_login_row['customers_login_ip'];
                                            if(!empty($login_country_ip)){
                                                $ip_country_info = tep_get_ip_country_info($login_country_ip);
                                                $login_country = $ip_country_info['countries_iso_code_2'];
                                            }
                                        }
                                    }

                                    // Fetch Billing Country
                                    $billing_country = "";
                                    $address_book_sql = "SELECT * FROM ".TABLE_ADDRESS_BOOK." where customers_id = ".$g2g_prod_info['seller_id']." ";
                                        $address_book_result_sql = tep_db_query($address_book_sql);
                                        if ($address_book_row = tep_db_fetch_array($address_book_result_sql)) {
                                            $address_book_country_id = $address_book_row['entry_country_id'];
                                            if($address_book_country_id == 0){
                                                $billing_country = "";
                                            }else{
                                                $country_sql = "SELECT countries_iso_code_2 FROM ".TABLE_COUNTRIES." WHERE countries_id = ".$address_book_country_id."";
                                                $country_res = tep_db_query($country_sql);
                                                if($country_row = tep_db_fetch_array($country_res)){
                                                    $billing_country = $country_row['countries_iso_code_2'];
                                                }
                                            }
                                        }

                                    // Fetch Seller Phone Country
                                    $custoners_sql = "SELECT * FROM ".TABLE_CUSTOMERS." WHERE customers_id = ". $g2g_prod_info['seller_id'] ."";
                                    $customers_sql_result = tep_db_query($custoners_sql);
                                    $phone_country = "";
                                    if ($customers_sql_row = tep_db_fetch_array($customers_sql_result)) {
                                        $country_id =$customers_sql_row['customers_country_dialing_code_id'];
                                        // Convert country to iso2
                                        $countries_sql = "SELECT * FROM ".TABLE_COUNTRIES." WHERE countries_id LIKE '".$country_id."' ";
                                        $countries_result = tep_db_query($countries_sql);
                                        if ($countries_row = tep_db_fetch_array($countries_result)) {
                                            $phone_country = $countries_row['countries_iso_code_2'];
                                        }
                                    }

                                    $tax_preview_params = [
                                        "country" => $phone_country,
                                        "user_id" => $g2g_prod_info['seller_id'],
                                        "currency" => $g2g_prod_info['products_base_currency'],
                                        "billing_address" => $billing_country,
                                        "signup_country" => $sign_up_country,
                                        "revenue_type" => "Commission",
                                        "amount" => $comm,
                                        'phone_country' => $phone_country,
                                        'login_country' => $login_country,
                                    ];

                                    $tax_preview = g2g_serverless::getTaxPreview($tax_preview_params);

                                    // deduct the tax further to after_fee_unit_price
                                    // calculate additional extra param
                                    if($tax_preview['tax'] == true){
                                        //only apply tax if tax is enabled
                                        $tax_rate = $tax_preview['tax_rate'];
                                        $comm_tax_amount = $comm * ($tax_rate/100);
                                        $after_fee_unit_price = $after_fee_unit_price - $comm_tax_amount;
                                    }

                                    $after_fee_unit_price_usd = $cur_obj->advance_currency_conversion($after_fee_unit_price, $g2g_prod_info['products_base_currency'], DEFAULT_CURRENCY, false, 'buy');

                                    $m_attr = array(
                                        'c2c_buyback_id' => $bo_id,
                                        'game_id' => $g2g_prod_info['game_id'],
                                        'product_id' => $g2g_prod_info['products_id'],
                                        'product_name' => $g2g_prod_info['products_title'],
                                        'custom_products_type' => $g2g_prod_info['custom_products_type'],
                                        'custom_products_type_child_id' => $g2g_prod_info['custom_products_type_child_id'],
                                        'c2c_products_listing_id' => $g2g_prod_info['c2c_products_listing_id'],
                                        'margin' => $margin,
                                        'purchase_quantity' => $purchase_quantity,
                                        'delivered_quantity' => 0,
                                        'product_unit_price' => $g2g_prod_info['products_price'],
                                        'product_unit_price_usd' => $cur_obj->advance_currency_conversion($g2g_prod_info['products_price'], $g2g_prod_info['products_base_currency'], DEFAULT_CURRENCY, false, 'buy'),
                                        'after_fee_unit_price' => $after_fee_unit_price,
                                        'after_fee_unit_price_usd' => $after_fee_unit_price_usd,
                                        'buyer_character' => (!empty($char_info) ? $char_info : ''),
                                        'delivery_mode' => (!empty($deliver_mode) ? $deliver_mode : ''),
                                        'total_screenshot' => 0,
                                        'orders_id' => $op_row['orders_id'],
                                        'orders_products_id' => $opid
                                    );
                                    tep_db_perform(TABLE_C2C_BUYBACK_PRODUCT, $m_attr);
                                    $new_c2c_buyback_product_id = tep_db_insert_id();
                                    unset($m_attr);

                                    // Save tax information into new_c2c_buyback_product_id
                                    if($tax_preview['tax'] == true){
                                        // save only if tax is enabled
                                        $bp_extra_info = array(
                                            'tax_enabled' => $tax_preview['tax'],
                                            'tax_rate' => $tax_rate,
                                            'tax_amount' => $comm_tax_amount,
                                            'tax_country' => ($tax_preview['country']) ? $tax_preview['country']:"",
                                            'tax_short_title' => ($tax_preview['short_title']) ? $tax_preview['short_title']:"",
                                            'tax_title' => ($tax_preview['tax_title']) ? $tax_preview['tax_title']:"",
                                            'commission_price' => $comm,
                                            'tax_currency' => ($g2g_prod_info['products_base_currency']) ? $g2g_prod_info['products_base_currency']:"",
                                            'tax_label' => "Commission * ".$tax_preview['short_title']." ( ".$tax_rate."% )",
                                            'seller_phone_country_iso2' => $phone_country,
                                            'seller_billing_country_iso2' => $billing_country,
                                            'seller_signup_country_iso2' => $sign_up_country,
                                            'seller_login_country_iso2' => $login_country,
                                        );
                                    }else{
                                        // only save is if tax is enabled and commision price
                                        $bp_extra_info = array(
                                            'tax_enabled' => $tax_preview['tax'],
                                            'commission_price' => $comm,
                                            'seller_phone_country_iso2' => $phone_country,
                                            'seller_billing_country_iso2' => $billing_country,
                                            'seller_signup_country_iso2' => $sign_up_country,
                                            'seller_login_country_iso2' => $login_country,
                                        );
                                    }

                                    foreach($bp_extra_info as $k=>$v){
                                        $bp_extra_info_attr = array(
                                            'c2c_buyback_product_id' => $new_c2c_buyback_product_id,
                                            'extra_info_key' => $k,
                                            'extra_info_value' => $v
                                        );

                                        tep_db_perform(TABLE_C2C_BUYBACK_PRODUCT_EXTRA_INFO, $bp_extra_info_attr);
                                    }

                                    switch ($g2g_prod_info['custom_products_type']) {
                                        case 1: // PWL
                                            $g2g_prod_json = c2c_buy_order::c2c_product_snapshot($op_row['date_purchased'], $op_row['customers_id'], $g2g_prod_info['c2c_products_listing_id']);
                                            if (isset($g2g_prod_json->listing_attributes)) {
                                                $m_lvl = array();
                                                foreach ($g2g_prod_json->listing_attributes as $num => $val) {
                                                    if (($val->key == 'current_lvl') || ($val->key == 'start_lvl')) {
                                                        $m_lvl['current_lvl'] = (int) $val->value;
                                                        $m_lvl['start_lvl'] = (int) $val->value;
                                                    } else if (($val->key == 'desired_lvl') || ($val->key == 'end_lvl')) {
                                                        $m_lvl['end_lvl'] = (int) $val->value;
                                                    }
                                                }

                                                if (!empty($m_lvl)) {
                                                    $m_attr = array(
                                                        'c2c_buyback_product_id' => $new_c2c_buyback_product_id,
                                                        'extra_info_value' => json_encode($m_lvl)
                                                    );
                                                    tep_db_perform(TABLE_C2C_BUYBACK_PRODUCT_EXTRA_INFO, $m_attr);
                                                    unset($m_attr);
                                                }
                                            }
                                            break;
                                    }

                                    # insert notification
                                    $m_attr = array(
                                        'customers_id' => $g2g_prod_info['seller_id'],
                                        'orders_id' => $bo_id,
                                        'orders_type' => 'BO',
                                        'site_id' => 5
                                    );
                                    tep_db_perform(TABLE_ORDERS_NOTIFICATION, $m_attr);
                                    unset($m_attr);
                                    
                                    # insert cronjob
                                    $extra_info_array = array(
                                        'customer_id' => $g2g_prod_info['seller_id'],
                                        'sell_order_id' => $bo_id,
                                    );
                                    $cron_attr = array(
                                        'extra_info' => json_encode($extra_info_array),
                                        'type' => 'create_so',
                                        'flag' => 0,
                                        'response' => 0,
                                        'created_at' => time(),
                                        'updated_at' => time(),
                                    );
                                    tep_db_perform(TABLE_G2G_CRON_PROCESS_QUEUE, $cron_attr);
                                    unset($cron_attr);
                                    
                                    # enable chat and private message permission
                                    self::_chat_permission($bo_id, $opid, 'create');
                                } else {
                                    $_err = array(
                                        'errno' => 3,
                                        'error' => 'Fail to create new sell order'
                                    );
                                }
                            }
                        } else {
                            $_err = array(
                                'errno' => 2,
                                'error' => 'Product margin not found'
                            );
                        }
                    } else {
                        $_err = array(
                            'errno' => 6,
                            'error' => 'Not able to release Order Lock'
                        );
                    }
                } else {
                    $_err = array(
                        'errno' => 5,
                        'error' => 'Not able to create Sell Order' . ($tmp_lock ? ', Buy Order locked by ' . $lock_row['admin_email_address'] : '')
                    );
                }
            } else {
                switch ($_row['status']) {
                    case 4: // cancel
                        $bo_id = $_row['c2c_buyback_id'];

                        # update order status to `pending`
                        $m_attr = array(
                            'status' => 1
                        );

                        //recalculate total orders based on scenario
                        $success_rate_flag = $_row['success_rate_flag'];
                        if ($success_rate_flag == 0) {
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so+1 where seller_id = '" . $g2g_prod_info['seller_id'] . "'";
                            tep_db_query($csr_update);
                            //change success rate flag to 1;
                            $m_attr['success_rate_flag'] = 1;
                        }

                        tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id = '" . $bo_id . "'");

                        # insert notification
                        $on_sel = "SELECT orders_id
                                 FROM " . TABLE_ORDERS_NOTIFICATION . "
                                 WHERE customers_id = " . $g2g_prod_info['seller_id'] . "
                                     AND orders_id = " . $bo_id . "
                                     AND site_id = 5";
                        $on_res = tep_db_query($on_sel);
                        if (!tep_db_num_rows($on_res)) {
                            $m_attr = array(
                                'customers_id' => $g2g_prod_info['seller_id'],
                                'orders_id' => $bo_id,
                                'orders_type' => 'BO',
                                'site_id' => 5
                            );
                            tep_db_perform(TABLE_ORDERS_NOTIFICATION, $m_attr);
                            unset($m_attr);
                        }

                        # enable chat and private message permission
                        self::_chat_permission($bo_id, $opid, 'create');
                        break;

                    default:
                        $_err = array(
                            'errno' => 4,
                            'error' => 'Sell Order already exist'
                        );
                        break;
                }
            }
        } else {
            $_err = array(
                'errno' => 1,
                'error' => 'G2G Product Information not found'
            );
        }

        if (!empty($_err)) {
            return $_err;
        } else {
            if ($bo_id) {
                $_res = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
                while ($_row = tep_db_fetch_array($_res)) {
                    if (!defined($_row['cfgKey'])) {
                        define($_row['cfgKey'], $_row['cfgValue']);
                    }
                }

                $op_sql = "  SELECT orders_id, products_quantity, products_name
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_products_id = " . $opid;
                $op_res = tep_db_query($op_sql);
                $op_row = tep_db_fetch_array($op_res);

                # Buyback History comment
                $m_attr = array(
                    'c2c_buyback_id' => $bo_id,
                    'status' => 5,
                    'date_added' => 'now()',
                    'seller_notified' => 1,
                    'comments' => '',
                    'set_as_remarks' => '0',
                    'changed_by' => 'system'
                );
                tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $m_attr);
                unset($m_attr);

                # Order History comment
                $m_attr = array(
                    'orders_id' => $op_row['orders_id'],
                    'orders_status_id' => 0,
                    'date_added' => 'now()',
                    'customer_notified' => 0,
                    'comments' => '##G2G##' . $bo_id . '## G2G Sell Order Created for REF#' . $g2g_prod_info['c2c_products_listing_id'],
                    'comments_type' => 0,
                    'set_as_order_remarks' => 0,
                    'changed_by' => (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : self::CHANGE_BY)
                );
                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr);
                unset($m_attr);
                                    
                # Send Notification Mail to Buyer
                $cb_sql = " SELECT cb.currency, cbp.purchase_quantity, cbp.after_fee_unit_price, o.customers_id
                        FROM " . TABLE_C2C_BUYBACK . " AS cb
                        INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                            ON cbp.c2c_buyback_id = cb.c2c_buyback_id
                        LEFT JOIN " . TABLE_ORDERS . " AS o
                            ON cbp.orders_id = o.orders_id
                        WHERE cb.c2c_buyback_id = " . $bo_id;
                $cb_res = tep_db_query($cb_sql);
                $cb_row = tep_db_fetch_array($cb_res);

                $buyer_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender
                        FROM " . TABLE_CUSTOMERS . "
                        WHERE customers_id = '" . $cb_row['customers_id'] . "'";
                $buyer_res = tep_db_query($buyer_sql);
                if ($c_row = tep_db_fetch_array($buyer_res)) {
                    $customer_name = $c_row['customers_firstname'] . " " . $c_row['customers_lastname'];
                    $customer_greeting_name = tep_get_email_greeting($c_row['customers_firstname'], $c_row['customers_lastname'], $c_row['customers_gender']);
                    
                    //send email here for new story
                    // $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $c_row['customers_gender']);
                    $mail_prefix = G2G_EMAIL_SUBJECT_PREFIX;
                    $mail_to = G2G_EMAIL_TO;
                    $mail_footer = G2G_EMAIL_FOOTER;
                    $store_mail = G2G_STORE_OWNER_EMAIL_ADDRESS;
                    $store_name = G2G_STORE_NAME;
                    $store_owner = G2G_STORE_OWNER;
                    $pwlTagAdded = false;
                    $hlaTagAdded = false;

                    switch ($g2g_prod_info['custom_products_type']) {
                        case '0': //currency
                        case '5': //item
                            $email_text = $customer_greeting_name .
                                    TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT1 .
                                    TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT2 .
                                    // TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK .
                                    TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE .
                                    TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK .
                                    "\n\n" . 
                                    // sprintf(EMAIL_CONTACT, $mail_to, $store_name) . 
                                    $mail_footer;
                            tep_mail($customer_name, $c_row['customers_email_address'], implode(' ', array($mail_prefix, sprintf(EMAIL_TEXT_SUBJECT, $op_row['orders_id']))), $email_text, $store_owner, $store_mail);
                            $sql_data_array = array(
                                'orders_id' => $op_row['orders_id'],
                                'orders_status_id' => '',
                                'date_added' => 'now()',
                                'customer_notified' => '0',
                                'comments' => 'G2G delivery template e-mail sent',
                                'comments_type' => 1,
                                'set_as_order_remarks' => '0',
                                'changed_by' => (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : self::CHANGE_BY),
                            );
                            tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
                            break;
                        case '1': //pwl
                            //add '[PWL - PWLING UNSUBMITTED]' tag
                            if (!$pwlTagAdded) {
                                $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', " . PWLING_UNSUBMITTED_ORDER_TAG_ID . ", CONCAT_WS(',', orders_tag_ids, " . PWLING_UNSUBMITTED_ORDER_TAG_ID . ")) WHERE orders_id = " . $op_row['orders_id'] . " AND NOT FIND_IN_SET(" . PWLING_UNSUBMITTED_ORDER_TAG_ID . ", orders_tag_ids)";
                                tep_db_query($assign_orders_tag_update_sql);
                                $pwlTagAdded = true;
                            }
                            break;
                        case '4': //hla
                            $email_text = $customer_greeting_name .
                                    TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT1 .
                                    TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT2 .
                                    // TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK .
                                    TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE .
                                    TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK .
                                    "\n\n" . 
                                    // sprintf(EMAIL_CONTACT, $mail_to, $store_name) . 
                                    $mail_footer;
                            tep_mail($customer_name, $c_row['customers_email_address'], implode(' ', array($mail_prefix, sprintf(EMAIL_TEXT_SUBJECT, $op_row['orders_id']))), $email_text, $store_owner, $store_mail);
                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('" . $op_row['orders_id'] . "', '', now(), '0', 'G2G delivery template e-mail sent', 1, '0', '" . (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : self::CHANGE_BY) . "')");
                            //add '[HLA - WOW Account]' tag
                            if (!$hlaTagAdded) {
                                $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', " . HLA_WOW_ACC_ORDER_TAG_ID . ", CONCAT_WS(',', orders_tag_ids, " . HLA_WOW_ACC_ORDER_TAG_ID . ")) WHERE orders_id = " . $op_row['orders_id'] . " AND NOT FIND_IN_SET(" . HLA_WOW_ACC_ORDER_TAG_ID . ", orders_tag_ids)";
                                tep_db_query($assign_orders_tag_update_sql);
                                $hlaTagAdded = true;
                            }
                            break;
                    }
                }
                
                 # Send Notification Mail to Seller
                $c_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender
                        FROM " . TABLE_CUSTOMERS . "
                        WHERE customers_id = '" . $g2g_prod_info['seller_id'] . "'";
                $c_res = tep_db_query($c_sql);
                if ($c_row = tep_db_fetch_array($c_res)) {
                    $customer_name = $c_row['customers_firstname'] . " " . $c_row['customers_lastname'];
                    $customer_greeting_name = tep_get_email_greeting($c_row['customers_firstname'], $c_row['customers_lastname'], $c_row['customers_gender']);

                    if ($c_row['customers_email_address']) {
                        // set application wide parameters
                        // $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
                        // while ($configuration = tep_db_fetch_array($configuration_query)) {
                        //     define($configuration['cfgKey'], $configuration['cfgValue']);
                        // }
                      
                        $mail = $customer_greeting_name .
                                sprintf(EMAIL_G2G_BUYBACK_BODY, EMAIL_SEPARATOR) .
                                sprintf(EMAIL_G2G_BUYBACK_ORDER_NUMBER, $bo_id) . "\n" .
                                sprintf(EMAIL_G2G_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d") . " 00:00:00")) . "\n" .
                                sprintf(EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL, $c_row['customers_email_address']) . "\n" .
                                EMAIL_G2G_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . (isset($op_row['products_name']) ? $op_row['products_name'] : '') . "\n" .
                                EMAIL_SEPARATOR . "\n" .
                                sprintf(EMAIL_G2G_BUYBACK_ORDER_TOTAL, $cur_obj->format(($cb_row['purchase_quantity'] * $cb_row['after_fee_unit_price']), false, $cb_row['currency'])) . "\n\n" .
                                EMAIL_G2G_BUYBACK_STATUS . "\n" .
                                EMAIL_G2G_BUYBACK_ORDER_GUIDE . "\n\n" .
                                // sprintf(EMAIL_G2G_BUYBACK_ORDER_CLOSING, G2G_STORE_NAME) . "\n\n" .
                                G2G_EMAIL_FOOTER;
                        tep_mail($customer_name, $c_row['customers_email_address'], G2G_EMAIL_SUBJECT_PREFIX . " " . sprintf(EMAIL_G2G_BUYBACK_SUBJECT, $bo_id), $mail, G2G_STORE_OWNER, G2G_STORE_OWNER_EMAIL_ADDRESS);
                    }
                }
            }
            return array('errno' => 0, 'result' => $bo_id);
        }
    }

    public static function _order_tag($id, $format = '') {
        $result = array();

        $_cb_sel = "SELECT orders_tag_ids, status FROM " . TABLE_C2C_BUYBACK . " WHERE c2c_buyback_id = '" . (int) $id . "'";
        $_cb_res = tep_db_query($_cb_sel);
        if (tep_db_num_rows($_cb_res) > 0) {
            $_cb_row = tep_db_fetch_array($_cb_res);

            $otag = array(
                array('id' => '', 'text' => 'Order Lists Options ...'),
                array('id' => 'rd', 'text' => '&nbsp;&nbsp;&nbsp;Mark as read'),
                array('id' => 'ur', 'text' => '&nbsp;&nbsp;&nbsp;Mark as unread'),
                array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
            );

            $add_tag = array();
            $del_tag[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
            $sel_tag = explode(',', $_cb_row['orders_tag_ids']);
            $tag_str = '';

            $otag_sel = "   SELECT orders_tag_id, orders_tag_name
                            FROM " . TABLE_ORDERS_TAG . "
                            WHERE FIND_IN_SET('" . $_cb_row['status'] . "', orders_tag_status_ids)
                                AND filename='" . FILENAME_BUYBACK_REQUESTS . "'
                            ORDER BY orders_tag_name";
            $otag_res = tep_db_query($otag_sel);
            while ($otag_row = tep_db_fetch_array($otag_res)) {
                if (in_array($otag_row['orders_tag_id'], $sel_tag)) {
                    $del_tag[] = array('id' => 'rmtag_' . $otag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $otag_row["orders_tag_name"]);
                    $tag_str .= $otag_row['orders_tag_name'] . ', ';
                } else {
                    $add_tag[] = array('id' => 'otag_' . $otag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $otag_row["orders_tag_name"]);
                }
            }
            $add_tag[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
            $otag = array_merge($otag, $add_tag, $del_tag);

            if ($format == 'xml') {
                $result = "<tag_details><order_tags order_id='" . $id . "'><![CDATA[" . (substr($tag_str, -2) == ', ' ? substr($tag_str, 0, -2) : $tag_str) . "]]></order_tags></tag_details>";

                $result .= '<selection>';
                for ($i = 0, $cnt = count($otag); $cnt > $i; $i++) {
                    $result .= "<option index='" . $otag[$i]['id'] . "'><![CDATA[" . $otag[$i]['text'] . "]]></option>";
                }
                $result .= '</selection>';
            } else {
                $result = array(
                    'tag' => $otag,
                    'tag_str' => (substr($tag_str, -2) == ', ' ? substr($tag_str, 0, -2) : $tag_str)
                );
            }
        }

        return $result;
    }

    public static function _pre_order_rule($id, $from_status, $to_status, $success_flag) {
        $result = '';

        $_sel = "   SELECT cb.date_added, cb.success_rate_flag, cb.seller_id, SUM(cbp.delivered_quantity) AS delivered_quantity,
                        cbp.product_id, cbp.orders_id, cbp.orders_products_id, cbp.c2c_products_listing_id, cbp.custom_products_type_child_id, cbp.total_screenshot, 
                        csg.payout_grace_period_offset, o.orders_status, o.date_purchased
                    FROM " . TABLE_C2C_BUYBACK . " AS cb
                    INNER JOIN " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                        ON cbp.c2c_buyback_id = cb.c2c_buyback_id
                    INNER JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
                        ON cc.customers_id = cb.seller_id
                    INNER JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
                        ON csg.seller_group_id = cc.seller_group_id
                    INNER JOIN " . TABLE_ORDERS . " AS o
                        ON o.orders_id = cbp.orders_id
                    WHERE cb.c2c_buyback_id = " . $id . "
                        AND cb.locked_by = " . $_SESSION['login_id'];
        $_res = tep_db_query($_sel);
        if ($_row = tep_db_fetch_array($_res)) {
            if (!tep_check_status_update_permission('B', $_SESSION['login_groups_id'], $from_status, $to_status)) {
                $result = ERROR_TRANS_UPDATE_DENIED;
            } else {
                switch ($from_status) {
                    case 6: // preparing
                        switch ($to_status) {
                            case 1: // delivering
                                break;
                            case 2: // delivered
                             break;
                                
                            break;
                            case 3: // complete
                                break;
                            case 4: //cancelled
                                c2c_order::deleteCancellation($id);
                                break;
                        }
                    case 1: // delivering
                        switch ($to_status) {
                            case 1: // delivering
                                break;
                            case 2: // delivered
                                if ($_row['delivered_quantity'] <= 0) {
                                    $result = ERROR_MIN_DELIVERED_QTY_TO_DELIVERED;
                                }
                            break;
                            case 3: // complete
                                break;
                            case 4: //cancelled
                                c2c_order::deleteCancellation($id);
                                break;
                        }
                        break;

                    case 2: // delivered
                        switch ($to_status) {
                            case 1: // pending

                            case 3: // complete
                                break;
                            case 4: //cancelled
                               c2c_order::deleteCancellation($id);
                                break;
                        }
                        break;

                    case 3: // complete
                        switch ($to_status) {
                            case 1: // pending aka delivering
//                            case 2: // delivered
                                if ($_row['orders_status'] == 2) {    // buy order in processing
                                    # enable chat and private message permission
                                    self::_chat_permission($id, $_row['orders_products_id'], 'create');
                                } else {
//                                    $result = ERROR_BUY_ORDER_STATUS_DENIED;
                                }
                                //removed record from cron_pending_credit
                                //if credited time less than 20mins are not allow to cancel
                                $_cpc_sel = "   SELECT cron_pending_credit_trans_completed_date, DATE_ADD(now(), INTERVAL 20 MINUTE) AS allow_rollback_time, DATE_ADD(cron_pending_credit_trans_completed_date, INTERVAL CAST(cron_pending_credit_mature_period AS UNSIGNED) MINUTE) AS credited_time
                                        FROM " . TABLE_CRON_PENDING_CREDIT . "
                                        WHERE cron_pending_credit_trans_type = 'SO'
                                            AND cron_pending_credit_trans_id = '" . tep_db_input($id) . "'";
                                $_cpc_res = tep_db_query($_cpc_sel);
                                if ($_cpl_row = tep_db_fetch_array($_cpc_res)) {
                                    $allow_rollback = false;
                                    if($_cpl_row['allow_rollback_time'] < $_cpl_row['credited_time']){
                                        $allow_rollback = true;
                                    } else {
                                        if($_row['custom_products_type_child_id'] != "5" && $_row['total_screenshot'] <= 0){
                                            $allow_rollback = true;
                                        }
                                    }

                                    if($allow_rollback === true){
                                        tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $id);
                                        $cbh_attr = array(
                                            'c2c_buyback_id' => $id,
                                            'status' => 0,
                                            'date_added' => 'now()',
                                            'seller_notified' => 0,
                                            'comments' => 'Removed from crediting Seller Credit queue',
                                            'set_as_remarks' => 1,
                                            'changed_by' => $_SESSION['login_email_address']
                                        );
                                        tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                                        tep_db_query("DELETE FROM " . TABLE_CRON_PENDING_CREDIT . " WHERE cron_pending_credit_trans_id = '" . (int) $id . "' AND cron_pending_credit_trans_type = 'SO'");
                                        unset($cbh_attr);
                                    } else {
                                        $result = 'Credit period less than 20 minutes are not allow to rollback from completed';
                                    }
                                } else {
                                    tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $id);
                                        $cbh_attr = array(
                                            'c2c_buyback_id' => $id,
                                            'status' => 0,
                                            'date_added' => 'now()',
                                            'seller_notified' => 0,
                                            'comments' => 'Seller credit already credited, please inform ANB to manual deduct',
                                            'set_as_remarks' => 1,
                                            'changed_by' => $_SESSION['login_email_address']
                                        );
                                    tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                                } 
                                //success order revert +1 at table c2c_seller_reputation
                                $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set success_so = success_so-1 where seller_id = '" . $_row['seller_id'] . "'";
                                break;
                        }
                        break;

                    case 4: // cancel
                        switch ($to_status) {
                            case 1: // delivering
                            case 2: // delivered
                                if ($_row['orders_status'] == 2) {    // buy order in processing
                                    # enable chat and private message permission
                                    self::_chat_permission($id, $_row['orders_products_id'], 'create');

                                    //recalculate total orders based on scenario
                                    $success_rate_flag = $_row['success_rate_flag'];
                                    if ($success_rate_flag == 0) {
                                        $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so+1 where seller_id = '" . $_row['seller_id'] . "'";
                                        //change success rate flag to 1;
                                        $change_success_rate_flag = 1;
                                    }
                                } else {
                                    $result = ERROR_BUY_ORDER_STATUS_DENIED;
                                }
                                c2c_order::deleteCancellation($id);
                                break;

                            case 3: // complete
                                break;
                        }
                        break;
                }

                self::_add_tag($id, $to_status);

                switch ($to_status) {
                    case 3: // complete
                        if ($_row['delivered_quantity'] > 0) {
                            $cron_pending_credit_mature_period = 0;
                            $_cpc_sel = "   SELECT cron_pending_credit_trans_completed_date
                                        FROM " . TABLE_CRON_PENDING_CREDIT . "
                                        WHERE cron_pending_credit_trans_type = 'SO'
                                            AND cron_pending_credit_trans_id = '" . tep_db_input($id) . "'";
                            $_cpc_res = tep_db_query($_cpc_sel);
                            if (!tep_db_num_rows($_cpc_res)) {
                                include_once(DIR_WS_CLASSES . FILENAME_C2C_BUY_ORDER);
                                //check for listing insurance duration
                                $orders_products_extra_info_sql = "	SELECT orders_products_extra_info_value
                                FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
                                WHERE orders_products_id = '" . $_row['orders_products_id'] . "'
                                    AND orders_products_extra_info_key = 'listing'";
                                $orders_products_extra_info_result = tep_db_query($orders_products_extra_info_sql);
                                if ($orders_products_extra_info_row = tep_db_fetch_array($orders_products_extra_info_result)) {
                                    $g2g_prod = tep_array_unserialize($orders_products_extra_info_row['orders_products_extra_info_value']);
                                    if(isset($g2g_prod['listing_insurance'])){
                                        $listing_insurance_row = $g2g_prod['listing_insurance'];
                                    }
                                } 
                                
                                if(isset($listing_insurance_row)){
                                    $cron_pending_credit_mature_period = $listing_insurance_row;
                                } else {
                                    $g2g_prod_json = c2c_buy_order::c2c_product_snapshot($_row['date_purchased'], $_row['seller_id'], $_row['c2c_products_listing_id']);
                                    if (!empty($g2g_prod_json) && isset($g2g_prod_json['product_listing']["listing_insurance"])) {
                                        //use s3 snapshot order details
                                        $cron_pending_credit_mature_period = $g2g_prod_json['product_listing']["listing_insurance"];
                                    } else {
                                        $_cpl_sql = "   SELECT listing_insurance
                                            FROM " . TABLE_C2C_PRODUCTS_LISTING . "
                                            WHERE c2c_products_listing_id = " . $_row['c2c_products_listing_id'];
                                        $_cpl_res = tep_db_query($_cpl_sql);
                                        if ($_cpl_row = tep_db_fetch_array($_cpl_res)) {
                                            //use listing insurance duration value
                                            $cron_pending_credit_mature_period = $_cpl_row['listing_insurance'];
                                        } else {
                                            //hardcode if not able to find any listing insurance
                                            $cron_pending_credit_mature_period = 14;
                                        }
                                    }
                                }

                                $cron_pending_credit_mature_period = $cron_pending_credit_mature_period * 24 * 60;

                                if ($cron_pending_credit_mature_period <= 0) {
                                    //if listing insurance duration smaller than 0, use current default value.
                                    $cron_pending_credit_mature_period = tep_get_products_payment_mature_period($_row['product_id']) + (int) $_row['payout_grace_period_offset'];
                                }

                                $m_attr = array(
                                    'cron_pending_credit_trans_type' => 'SO',
                                    'cron_pending_credit_trans_id' => tep_db_input($id),
                                    'cron_pending_credit_trans_created_date' => $_row['date_added'],
                                    'cron_pending_credit_trans_completed_date' => 'now()',
                                    'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
                                    'cron_pending_credit_trans_status' => $to_status
                                );
                                tep_db_perform(TABLE_CRON_PENDING_CREDIT, $m_attr);
                                unset($m_attr);
                            }

                            tep_db_query("UPDATE " . TABLE_C2C_BUYBACK_HISTORY . " SET set_as_remarks = 0 WHERE c2c_buyback_id = " . (int) $id);
                            $cbh_attr = array(
                                'c2c_buyback_id' => $id,
                                'status' => 0,
                                'date_added' => 'now()',
                                'seller_notified' => 1,
                                'comments' => sprintf(TEXT_COMMENTS_PROCESSING_TO_COMPLETED, ($cron_pending_credit_mature_period + 15)), // +15min cronjob time gap
                                'set_as_remarks' => 1,
                                'changed_by' => $_SESSION['login_email_address']
                            );
                            tep_db_perform(TABLE_C2C_BUYBACK_HISTORY, $cbh_attr);
                            unset($cbh_attr);

                            # disable chat and private message permission
                            self::_chat_permission($id, $_row['orders_products_id'], 'delete');

                            //success order +1 at table c2c_seller_reputation
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set success_so = success_so+1 where seller_id = '" . $_row['seller_id'] . "'";
                        } else {
                            $result = ERROR_MIN_DELIVERED_QTY_TO_COMPLETE;
                        }
                        break;

                    case 4: // cancel
                        //recalculate total orders based on scenario
                        $oph_sql = 'SELECT COUNT(orders_products_history_id) as deliveries FROM '. TABLE_ORDERS_PRODUCTS_HISTORY ." WHERE (received IS NULL OR received != 2) AND buyback_request_group_id =".$id;
                        $oph_res = tep_db_query($oph_sql);
                        if ($oph_count = tep_db_fetch_array($oph_res)) {
                            if($oph_count['deliveries'] > 0){
                                $result = 'Please cancel all deliveries before canceling order';
                                return $result;
                            }
                        }
                        if ($success_flag == "0") {
                            $csr_update = "update " . TABLE_C2C_SELLER_REPUTATION . " set total_so = total_so-1 where seller_id = '" . $_row['seller_id'] . "'";
                            $change_success_rate_flag = 0;
                        }
                        $m_attr = array(
                            'orders_id' => $_row['orders_id'],
                            'orders_status_id' => 0,
                            'date_added' => 'now()',
                            'customer_notified' => 0,
                            'comments' => '##G2G##' . (int) $id . '## - G2G Sell Order CANCELED by ' . $_SESSION['login_email_address'],
                            'comments_type' => 0,
                            'set_as_order_remarks' => 0,
                            'changed_by' => $_SESSION['login_email_address']
                        );
                        tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $m_attr);
                        unset($m_attr);

                        # remove new order notification
                        tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . (int) $id . "' AND orders_type = 'BO' AND site_id = 5");

                        # disable chat and private message permission
                        self::_chat_permission($id, $_row['orders_products_id'], 'delete');
                }

                # update order status
                if (empty($result)) {
                    $m_attr = array('status' => $to_status);
                    if (isset($change_success_rate_flag)) {
                        $m_attr['success_rate_flag'] = $change_success_rate_flag;
                    }
                    tep_db_perform(TABLE_C2C_BUYBACK, $m_attr, 'update', "c2c_buyback_id= '" . (int) $id . "'");
                    if (isset($csr_update)) {
                        tep_db_query($csr_update);
                    }
                    unset($m_attr);
                }
            }
        } else {
            $result = ERROR_ORDER_NOT_LOCK;
        }

        return $result;
    }

    public static function _private_message_permission($soid, $opid, $type) {
        if (!empty($type)) {
            switch ($type) {
                case 'create':
                    $so_sel = "SELECT seller_id FROM " . TABLE_C2C_BUYBACK . " WHERE c2c_buyback_id = " . $soid;
                    $so_res = tep_db_query($so_sel);
                    $so_row = tep_db_fetch_array($so_res);

                    $op_sel = " SELECT op.products_name, o.orders_id, o.customers_id
                            FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                            INNER JOIN " . TABLE_ORDERS . " AS o
                                ON o.orders_id = op.orders_id
                            WHERE op.orders_products_id = " . $opid;
                    $op_res = tep_db_query($op_sel);
                    $op_row = tep_db_fetch_array($op_res);

                    $_sel = "   SELECT transaction_id
                                FROM " . TABLE_C2C_MAILBOX_GROUP . "
                                WHERE transaction_id = " . $soid . "
                                    AND transaction_type = '" . 5 . "'";
                    $_res = tep_db_query($_sel);
                    $m_attr = array(
                        'transaction_id' => $soid,
                        'transaction_type' => 5,
                        'user1_id' => $so_row['seller_id'],
                        'user1_displayname' => $soid,
                        'user2_id' => $op_row['customers_id'],
                        'user2_displayname' => $op_row['orders_id'],
                        'status' => '1',
                        'title' => $op_row['products_name'],
                        'create_date' => 'now()',
                        'last_modify' => 'now()'
                    );
                    if (!tep_db_num_rows($_res)) {
                        tep_db_perform(TABLE_C2C_MAILBOX_GROUP, $m_attr);
                    } else {
                        tep_db_perform(TABLE_C2C_MAILBOX_GROUP, $m_attr, 'update', "transaction_id = '" . $soid . "' AND transaction_type = '" . 5 . "'");
                    }
                    unset($m_attr);
                    break;

                case 'delete':
                    $m_attr = array('status' => '0');
                    tep_db_perform(TABLE_C2C_MAILBOX_GROUP, $m_attr, 'update', "transaction_id = '" . $soid . "' AND transaction_type = '" . 5 . "'");
                    unset($m_attr);
                    break;
            }
        }
    }

    public static function _retrieve_buyback_id($opid) {
        $_sql = "   SELECT cbp.c2c_buyback_id, cb.status, cb.success_rate_flag
                    FROM " . TABLE_C2C_BUYBACK_PRODUCT . " AS cbp
                    INNER JOIN " . TABLE_C2C_BUYBACK . " AS cb
                        ON cb.c2c_buyback_id = cbp.c2c_buyback_id
                    WHERE cbp.orders_products_id = " . $opid;
        $_res = tep_db_query($_sql);
        return tep_db_fetch_array($_res);
    }

    public static function _send_update_mail($id, $type, $from_status, $comm) {
        include_once(DIR_WS_CLASSES . 'currencies.php');
        $currencies = new currencies();

        // set application wide parameters
        $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
        while ($configuration = tep_db_fetch_array($configuration_query)) {
            define($configuration['cfgKey'], $configuration['cfgValue']);
        }

        $_payable_amt = 0;
        $_from_status = '';

        $_sel = "   SELECT cb.status, cb.date_added, cb.currency, cb.currency_value, bs.buyback_status_name,
                        c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_gender
                    FROM " . TABLE_C2C_BUYBACK . " AS cb
                    INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs
                        ON bs.buyback_status_id = cb.status
                    INNER JOIN " . TABLE_CUSTOMERS . " AS c
                        ON c.customers_id = cb.seller_id
                    WHERE cb.c2c_buyback_id = " . (int) $id . "
                        AND bs.language_id = 1";
        $_res = tep_db_query($_sel);
        if ($_row = tep_db_fetch_array($_res)) {
            $_payable_amt = 0;
            $_from_status = $_row['buyback_status_name'];

            $_cbp_sel = "   SELECT delivered_quantity, after_fee_unit_price
                            FROM " . TABLE_C2C_BUYBACK_PRODUCT . "
                            WHERE c2c_buyback_id = " . (int) $id;
            $_cbp_res = tep_db_query($_cbp_sel);
            while ($_cbp_row = tep_db_fetch_array($_cbp_res)) {
                $_payable_amt += ( $_cbp_row['delivered_quantity'] * $_cbp_row['after_fee_unit_price']);
            }

            if ($from_status > 0) {
                $_bs_sel = "SELECT buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE language_id = 1 AND buyback_status_id = " . $from_status;
                $_bs_res = tep_db_query($_bs_sel);
                $_bs_row = tep_db_fetch_array($_bs_res);
                $_from_status = $_bs_row['buyback_status_name'];
            }
        }

        $_payable = $currencies->format($_payable_amt, true, $_row['currency'], $_row['currency_value']);

        //query for admin group
        $display_admin_group = tep_get_admin_group_name($_SESSION['login_email_address']);
        if (tep_not_null($display_admin_group)) {
            $display_admin_group = ' [' . $display_admin_group . ']';
        }

        $notification_select_sql = "SELECT " . ($type == 'M' ? 'status_configuration_manual_notification' : 'status_configuration_auto_notification') . "
                                    FROM " . TABLE_STATUS_CONFIGURATION . "
                                    WHERE status_configuration_trans_type = '" . tep_db_input($type) . "'
                                        AND status_configuration_source_status_id = '" . tep_db_input($from_status) . "'
                                        AND status_configuration_destination_status_id = '" . tep_db_input($_row['status']) . "'";
        $notification_result_sql = tep_db_query($notification_select_sql);

        if ($notification_row = tep_db_fetch_array($notification_result_sql)) {
            $email_to_array = tep_parse_email_string($notification_row[$notification_field]);
            if (count($email_to_array)) {
                $_subject = sprintf(EMAIL_SELL_ORDER_UPDATE_SUBJECT, $id);
                $_mail = sprintf(EMAIL_SELL_ORDER_UPDATE, $id, $_row['date_added'], $_payable, constant('EMAIL_SELL_ORDER_UPDATE_TYPE_' . $type), $_row['buyback_status_name'], $_from_status, date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'] . $display_admin_group, $comm);

                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                    $_name = $email_to_array[$email_to_cnt]['name'];
                    tep_mail($_name, $email_to_array[$email_to_cnt]['email'], G2G_EMAIL_SUBJECT_PREFIX . " " . $_subject, $_mail, G2G_STORE_OWNER, G2G_STORE_OWNER_EMAIL_ADDRESS);
                }
            }
        }
    }

}