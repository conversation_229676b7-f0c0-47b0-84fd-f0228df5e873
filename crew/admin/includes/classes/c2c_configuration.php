<?php
/*
  $Id: c2c_configuration.php,v 1.3 2013/07/10 09:13:18 chingyen Exp $

  Developer: <PERSON> Yen
 */

class c2c_config {

    var $c2c_config_menu = array(
        'C2C_PRODUCT_LISTING',
        'C2C_PRODUCT_CONFIGURATION',
        'C2C_SELLER_PRODUCT_LISTING',
        'C2C_BUYER_ORDER',
        'C2C_MAILBOX',
        'G2G_STORE_INFORMATION'
    );

    function __construct() {

    }

    function menuListing() {
        ob_start();
        ?>
        <form name="menuListing" method="post" action="<?php echo tep_href_link(FILENAME_C2C_CONFIGURATION, 'action=add'); ?>">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td colspan="2">
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                <td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
        <?php
        for ($i = 0, $cnt = count($this->c2c_config_menu); $cnt > $i; $i++) {
            ?>
                    <tr>
                        <td>
                            <?php
                            $c2c_config_sel_sql = "	SELECT c2c_configuration_id, configuration_title, configuration_key,
														configuration_value, configuration_description, use_function,
														set_function
													FROM " . TABLE_C2C_CONFIGURATION . "
													WHERE configuration_group = '" . $this->c2c_config_menu[$i] . "'
													ORDER BY sort_order";
                            $c2c_config_res_sql = tep_db_query($c2c_config_sel_sql);
                            if (tep_db_num_rows($c2c_config_res_sql) > 0) {
                                ?>
                                <table border="0" width="100%" cellspacing="1" cellpadding="1">
                                    <tr>
                                        <td class="pageHeading" valign="top" colspan="3"><?php echo constant('SUB_TITLE_' . $this->c2c_config_menu[$i]); ?></td>
                                    </tr>
                                    <tr class="dataTableHeadingRow">
                                        <td class="cfgBoxHeading"><?php echo TABLE_HEADING_CONFIGURATION_TITLE; ?></td>
                                        <td class="cfgBoxHeading"><?php echo TABLE_HEADING_CONFIGURATION_VALUE; ?></td>
                                        <td class="cfgBoxHeading"><?php echo TABLE_HEADING_CONFIGURATION_DESCRIPTION; ?></td>
                                    </tr>

                                    <?php
                                    $row_count = 0;
                                    while ($c2c_config = tep_db_fetch_array($c2c_config_res_sql)) {
                                        $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                        $cInfo = new objectInfo($c2c_config);

                                        if (tep_not_null($cInfo->use_function)) {
                                            $use_function = $cInfo->use_function;
                                            if (ereg_dep('->', $use_function)) {
                                                $class_method = explode('->', $use_function);
                                                if (!is_object(${$class_method[0]})) {
                                                    include(DIR_WS_CLASSES . $class_method[0] . '.php');
                                                    ${$class_method[0]} = new $class_method[0]();
                                                }
                                                $cfgValue = tep_call_function($class_method[1], $cInfo->configuration_value, ${$class_method[0]});
                                            } else {
                                                $cfgValue = tep_call_function($use_function, $cInfo->configuration_value);
                                            }
                                        } else {
                                            $cfgValue = $cInfo->configuration_value;
                                        }

                                        if ($cInfo->set_function) {
                                            if (strpos($cInfo->set_function, 'tep_cfg_textarea') === 0) {
                                                eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "' . $cInfo->configuration_key . '", "66", "5");');
                                            } else {
                                                eval('$value_field = ' . $cInfo->set_function . '"' . htmlspecialchars($cfgValue) . '", "' . $cInfo->configuration_key . '");');
                                            }
                                        } else {
                                            $value_field = tep_draw_input_field('configuration[' . $cInfo->configuration_key . ']', $cfgValue, 'size="50"');
                                        }
                                        ?>

                                        <tr class="<?php echo $row_style; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style; ?>')" onclick="rowClicked(this, '<?php echo $row_style; ?>')">
                                            <td class="cfgRecords" width="30%" valign="top" nowrap><?php echo $cInfo->configuration_title; ?></td>
                                            <td class="cfgRecords" valign="top"><?php echo $value_field; ?></td>
                                            <td class="cfgRecords" width="30%" valign="top"><?php echo $cInfo->configuration_description; ?></td>
                                        </tr>

                                        <?php
                                        $row_count++;
                                    }
                                    ?>
                                </table>
                                <br />
                <?php
            }
            ?>
                        </td>
                    </tr>
            <?php
        }
        ?>
                <tr>
                    <td class="main" align="right"><?php echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton'); ?></td>
                </tr>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    function addEntry($post_data) {
        global $messageStack;

        for ($i = 0, $cnt = count($this->c2c_config_menu); $cnt > $i; $i++) {
            $c2c_config_sel_sql = "	SELECT configuration_key
									FROM " . TABLE_C2C_CONFIGURATION . "
									WHERE configuration_group = '" . $this->c2c_config_menu[$i] . "'
									ORDER BY sort_order";
            $c2c_config_res_sql = tep_db_query($c2c_config_sel_sql);
            while ($c2c_config = tep_db_fetch_array($c2c_config_res_sql)) {
                $key = $c2c_config['configuration_key'];
                $value = '';

                if (isset($post_data['configuration'][$key])) {
                    $value = $post_data['configuration'][$key];

                    if (is_array($value)) {
                        $value = implode(", ", $value);
                    }
                }

                $configuration_value = tep_db_prepare_input($value);
                $conf_update_sql = "UPDATE " . TABLE_C2C_CONFIGURATION . "
									SET configuration_value = '" . tep_db_input($configuration_value) . "',
										last_modified = now()
									WHERE configuration_key = '" . $key . "'
										AND configuration_value <> '" . tep_db_input($configuration_value) . "'";
                tep_db_query($conf_update_sql);
            }
        }
        $messageStack->add_session(SUCCESS_CONFIGURATION_UPDATED, 'success');

        tep_redirect(tep_href_link(FILENAME_C2C_CONFIGURATION, 'selected_box=c2c'));
    }

}
?>