<?php
/*
  	$Id: c2c_delivery_speed.php,v 1.2 2013/06/07 13:07:43 weichen Exp $
	
	Developer: <PERSON> Yen
*/

class c2c_delivery_speed_config {
	var $c2c_cpt_list = array ( 0, 4, 5 );
	
	function __construct() {}
	
	function menuListing() {
		$cpt = array();
		$listing_html = '';
		
		# Custom Product Type
		$cpt = $this->_get_custom_product_type();
		
		ob_start();
?>
		<form name="menuListing" method="post" action="<?=tep_href_link(FILENAME_C2C_DELIVERY_SPEED, 'action=add_form');?>">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td colspan="2">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE;?></td>
								<td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td width="100" class="main" nowrap><?=TEXT_PRODUCT_TYPE;?></td>
								<td width="100%" class="main" nowrap><?=tep_draw_pull_down_menu('cpt', $cpt, $id, ' id="cpt" onChange="document.menuListing.submit();"');?></td>
							</tr>
							<tr>
								<td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}
	
	function addForm($id = "") {
		$cpt = array();
		$delivery_speed_desc = array();
		
		$js_speed_id = '';
		$js_speed_text = '';
		
		$listing_html = '';
		
		# Custom Product Type
		$cpt = $this->_get_custom_product_type();
		
		# Delivery Speed
		$speed_name_sel_sql = "	SELECT delivery_speed_id, delivery_speed_name 
								FROM " . TABLE_C2C_DELIVERY_SPEED_DESCRIPTION . " 
								WHERE language_id = '1' 
								ORDER BY delivery_speed_id";
		$speed_name_res_sql = tep_db_query($speed_name_sel_sql);
		while ($speed_name_row = tep_db_fetch_array($speed_name_res_sql)) {
			$delivery_speed_desc[] = array ('id' => $speed_name_row['delivery_speed_id'], 
											'text' => $speed_name_row['delivery_speed_name'] );
			$js_speed_id .= '"' . $speed_name_row['delivery_speed_id'] . '",';
			$js_speed_text .= '"' . $speed_name_row['delivery_speed_name'] . '",';
		}
		
		$js_speed_id = substr($js_speed_id, 0, -1);
		$js_speed_text = substr($js_speed_text, 0, -1);
		
		ob_start();
?>
		<script language="javascript">
		<!--
			function check_form() {
				var error = 0;
				var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";
				
				var speed_id = [<?=$js_speed_id;?>];
				var speed_cnt = speed_id.length;
				var speed_text = [<?=$js_speed_text;?>];
				
				for (var i=0; speed_cnt > i; i++) {
					var from_val = document.getElementById('from_' + speed_id[i]).value;
					var to_val = document.getElementById('to_' + speed_id[i]).value;
					
					if ((from_val == '') && (to_val == '')) {
						continue;
					} else {
						if ((from_val == '') && (to_val != '')) {
							error = 1;
							error_message = error_message + "* " + speed_text[i] + " \"From\" must be entered. \n";
						} else if ((from_val != '') && (to_val == '')) {
							error = 1;
							error_message = error_message + "* " + speed_text[i] + " \"To\" must be entered. \n";
						} else {
							if (isNaN(from_val)) {
								error = 1;
								error_message = error_message + "* " + speed_text[i] + " \"From\" must be numeric.\n";
							} else if (isNaN(to_val)) {
								error = 1;
								error_message = error_message + "* " + speed_text[i] + " \"To\" must be numeric.\n";
							} else if (parseInt(from_val) >= parseInt(to_val)) {
								error = 1;
								error_message = error_message + "* " + speed_text[i] + " \"To\" must be greater than \"From\".\n";
							}
						}
					}
				}
				
				if (error == 1) {
					alert(error_message);
					return false;
				} else {
					return true;
				}
			}
		//-->
		</script>
		
		<form name="addForm" method="post" id="addForm" action="<?=tep_href_link(FILENAME_C2C_DELIVERY_SPEED, 'action=add');?>" onSubmit="return check_form();">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td colspan="2">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?=HEADING_TITLE;?></td>
								<td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td width="100" class="main" nowrap><?=TEXT_PRODUCT_TYPE;?></td>
								<td width="100%" class="main" nowrap>
									<?=tep_draw_pull_down_menu('cpt', $cpt, $id, ' id="cpt" onChange="document.addForm.submit();"');?>
									<?=tep_draw_hidden_field('hidden_cpt', $id, ' id="hidden_cpt" ');?>
								</td>
							</tr>
							<tr>
								<td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
							</tr>
						</table>
					</td>
				</tr>
				<?php
					$delivery_speed = array();
					
					$delivery_speed_sel_sql = "	SELECT delivery_speed, from_minute, to_minute 
												FROM " . TABLE_C2C_DELIVERY_SPEED . " 
												WHERE custom_products_type_id = '" . $id . "'";
					$delivery_speed_res_sql = tep_db_query($delivery_speed_sel_sql);
					while ($delivery_speed_row = tep_db_fetch_array($delivery_speed_res_sql)) {
						$delivery_speed[$delivery_speed_row['delivery_speed']] = array(	'from_minute' => $delivery_speed_row['from_minute'], 
																						'to_minute' => $delivery_speed_row['to_minute'] );
					}
				?>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td width="15%" class="main" nowrap><b><?=SUB_TITLE_DELIVERY_SPEED;?></b></td>
								<td width="5%" class="main" nowrap><b><?=SUB_TITLE_FROM;?></b></td>
								<td width="20px" class="main" align="center">&nbsp;</td>
								<td class="main" nowrap><b><?=SUB_TITLE_TO;?></b></td>
							</tr>
							<?php
								for ($i=0, $cnt=count($delivery_speed_desc); $cnt > $i; $i++) {
									$speed_id = $delivery_speed_desc[$i]['id'];
									$from_minute = isset($delivery_speed[$speed_id]['from_minute']) ? $delivery_speed[$speed_id]['from_minute'] : '';
									$to_minute = isset($delivery_speed[$speed_id]['to_minute']) ? $delivery_speed[$speed_id]['to_minute'] : '';
							?>
								<tr>
									<td width="15%" class="main" nowrap><?=$delivery_speed_desc[$i]['text'];?></td>
									<td width="5%" class="main" nowrap><?=tep_draw_input_field('from_' . $speed_id, $from_minute, ' id="' . 'from_' . $speed_id . '" size="15" maxlength="12" ');?> <?=TEXT_MINUTE;?></td>
									<td width="20px" class="main" align="center">~</td>
									<td class="main" nowrap><?=tep_draw_input_field('to_' . $speed_id, $to_minute, ' id="' . 'to_' . $speed_id . '" size="15" maxlength="12" ');?> <?=TEXT_MINUTE;?></td>
								</tr>
							<?php
								}
							?>
							<tr>
								<td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
							</tr>
							<tr>
								<td class="main" align="right" colspan="4">
									<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
								</td>
							</tr>
						</table>
					</tr>
				</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
	
	
	function addEntry($id) {
		global $messageStack;
		
		$error = 0;
		$error_msg = '';
		
		$cpt = array();
		$cpt_list = array();
		$delivery_speed = array();
		
		if (tep_not_null($id)) {
			# Delivery Speed
			$speed_name_sel_sql = "	SELECT delivery_speed_id 
									FROM " . TABLE_C2C_DELIVERY_SPEED_DESCRIPTION . " 
									WHERE language_id = '1'";
			$speed_name_res_sql = tep_db_query($speed_name_sel_sql);
			while ($speed_name_row = tep_db_fetch_array($speed_name_res_sql)) {
				$delivery_speed[] = $speed_name_row['delivery_speed_id'];
			}
			
			# validation
			$speed_val = array();
			
			for ($i=0, $cnt=count($delivery_speed); $cnt > $i; $i++) {
				for ($j=0, $j_cnt=count($delivery_speed); $j_cnt > $j; $j++) {
					if ($i != $j) {
						if ((($_POST['from_' . $i] > $_POST['from_' . $j]) && ($_POST['from_' . $i] < $_POST['to_' . $j])) || 
							(($_POST['to_' . $i] > $_POST['from_' . $j]) && ($_POST['to_' . $i] < $_POST['to_' . $j]))) {
							$error = 1;
							break;
						}
					}
				}
			}
			
			if ($error == 1) {
				$messageStack->add_session(ERROR_SETTING_CONFLICT, 'error');
			} else {
				$delivery_speed_del_sql = "DELETE FROM " . TABLE_C2C_DELIVERY_SPEED . " WHERE custom_products_type_id = '$id'";
				tep_db_query($delivery_speed_del_sql);
				
				for ($i=0, $cnt=count($delivery_speed); $cnt > $i; $i++) {
					$data = array(	'custom_products_type_id' => (int)$id,
									'delivery_speed' => (int)$delivery_speed[$i], 
									'from_minute' => (int)$_POST['from_' . $delivery_speed[$i]], 
									'to_minute' => (int)$_POST['to_' . $delivery_speed[$i]] );
					tep_db_perform(TABLE_C2C_DELIVERY_SPEED, $data, 'insert');
				}
			}
			$messageStack->add_session(SUCCESS_DELIVERY_SPEED_UPDATED, 'success');
		} else {
			$messageStack->add_session(ERROR_INVALID_PRODUCT_TYPE_SELECTED, 'error');
		}
	}
	
	
	function _get_custom_product_type () {
		$cpt_list = tep_get_product_type();
		
		$cpt[] = array(	'id' => '', 
						'text' => SELECT_PRODUCT_TYPE );
		
		for ($i=0, $cnt=count($cpt_list); $cnt > $i; $i++) {
			if (in_array($cpt_list[$i]['id'], $this->c2c_cpt_list)) {
				$cpt[] = array ('id' => $cpt_list[$i]['id'], 
								'text' => $cpt_list[$i]['text'] );
			}
		}
		
		return $cpt;
	}
}
?>