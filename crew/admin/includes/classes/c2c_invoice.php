<?php

class c2c_invoice {
    
    public static function check_g2g_withdraw($trans_id) {
        $statement_select_sql = "SELECT store_account_history_account_type 
                                            FROM " . TABLE_STORE_ACCOUNT_HISTORY . "
                                            WHERE store_account_history_trans_id = '" . $trans_id . "' 
                                            AND store_account_history_trans_type = 'P'";
        $statement_result_sql = tep_db_query($statement_select_sql);
        if ($statement_row = tep_db_fetch_array($statement_result_sql)) {
            //only create g2g invoice queue if is g2g WSC (exclude POWSC)
            if ($statement_row['store_account_history_account_type'] == 'WSC') {
                return true;
            }
        }

        return false;
    }
    
    public static function get_store_payment_datetime($trans_id) {
        $store_payment_history_select_sql = "	SELECT date_added
                                                FROM " . TABLE_STORE_PAYMENTS_HISTORY . "
                                                WHERE store_payments_id = '" . $trans_id . "'
                                                    AND store_payments_status = '3'
                                                ORDER BY date_added DESC";
        $store_payment_history_result_sql = tep_db_query($store_payment_history_select_sql);
        $store_payment_history_row = tep_db_fetch_array($store_payment_history_result_sql);
        if ($store_payment_history_row) {
            $datetime = strtotime($store_payment_history_row['date_added']);
            return $datetime;
        }
        return false;
    }

    public static function create_c2c_invoice_queue($trans_id, $type, $datetime = null) {
        if ($datetime) {
            $time = $datetime;
        } else {
            $time = time();
        }
//insert to temp db to cater API failed to g2g-crew
        $c2c_invoice_queue_api_data_array = array(
            'trans_id' => $trans_id,
            'created_at' => $time,
            'updated_at' => $time,
            'type' => $type,
        );
        tep_db_perform('c2c_invoice_queue_api', $c2c_invoice_queue_api_data_array);

        include_once(DIR_WS_CLASSES . 'curl.php');
        $curl_obj = new curl();
//API G2G-crew
        $G2G_CREW_INVOICE_URL = G2G_API_URL . '/invoice/update-invoice-queue';
        $data = array(
            'merchant' => G2G_API_OGCREW_MERCHANT,
            'signature' => md5($trans_id . "|" . G2G_API_OGCREW_SECRET),
            'tid' => $trans_id,
            'created_at' => $time,
            'type' => $type
        );

        $curl_obj->curl_post($G2G_CREW_INVOICE_URL, $data);
    }

}
