<?php

class c2c_order {

    public function __construct() {
        
    }

    public function postOrderRules($cpl_id, $from_status, $to_status, $amt, $order_id = null, $action_name = "c2c_order") {
        $result = array();

        if (!empty($cpl_id)) {
            $_sql = "SELECT c2c_products_listing_id FROM " . TABLE_C2C_PRODUCTS_LISTING . " WHERE c2c_products_listing_id = " . $cpl_id;
            $_res = tep_db_query($_sql);
            if (tep_db_num_rows($_res)) {
                switch ($from_status) {
                    case 0: // any status
                        switch ($to_status) {
                            case 6: // refund
                                self::stockMovement($cpl_id, 1, '+', $amt, $order_id, $action_name);
                                self::stockMovement($cpl_id, 2, '+', $amt, $order_id, $action_name);
                                break;

                            case 8: // on hold
                                self::stockMovement($cpl_id, 1, '+', $amt, $order_id, $action_name);
                                self::stockMovement($cpl_id, 2, '+', $amt, $order_id, $action_name);
                                break;
                        }
                        break;

                    case 1: // pending
                        switch ($to_status) {
                            case 5: // cancel
                                self::stockMovement($cpl_id, 1, '+', $amt, $order_id, $action_name);
                                break;

                            case 7: // verifying
                                self::stockMovement($cpl_id, 2, '-', $amt, $order_id, $action_name);
                                break;
                        }
                        break;

                    case 2: // processing
                        switch ($to_status) {
                            case 1: // pending
                                self::stockMovement($cpl_id, 2, '+', $amt, $order_id, $action_name);
                                break;
                        }
                        break;

                    case 5: // cancel
                        switch ($to_status) {
                            case 1:
                                self::stockMovement($cpl_id, 1, '-', $amt, $order_id, $action_name);
                                break;
                        }
                        break;

                    case 7: // verifying
                        switch ($to_status) {
                            case 1: // pending
                                self::stockMovement($cpl_id, 2, '+', $amt, $order_id, $action_name);
                                break;

                            case 5: // cancel
                            case 6: // refund
                                self::stockMovement($cpl_id, 1, '+', $amt, $order_id, $action_name);
                                self::stockMovement($cpl_id, 2, '+', $amt, $order_id, $action_name);
                                break;
                        }
                        break;
                }
            }
        }

        return $result;
    }

    public static function stockMovement($cpl_id, $type, $sign, $amt, $order_id = null, $action_name = "c2c_order") {
        /*
         * qty_type :
         * 1 = forecast quantity
         * 2 = available quantity
         * 3 = actual quantity
         */

        $fieldname = '';

        switch ($type) {
            case 1:
                $fieldname = 'forecast_quantity';
                break;
            case 2:
                $fieldname = 'available_quantity';
                break;
            case 3:
                $fieldname = 'actual_quantity';
                break;
        }

        $_sql_listing = "SELECT " . $fieldname . " FROM " . TABLE_C2C_PRODUCTS_LISTING . " WHERE c2c_products_listing_id = " . $cpl_id;
        $_res = tep_db_query($_sql_listing);
        if ($row = tep_db_fetch_array($_res)) {
            $value = $row[$fieldname];
            switch ($sign) {
                case '+': $value = $row[$fieldname] + $amt;
                    break;
                case '-': $value = $row[$fieldname] - $amt;
                    break;
            }

            $_sql = "   UPDATE " . TABLE_C2C_PRODUCTS_LISTING . "
                    SET " . $fieldname . " = " . $value . "
                    WHERE c2c_products_listing_id = " . $cpl_id;
            tep_db_query($_sql);

            //update remaks history

            $data = array(
                'c2c_products_listing_id' => (int) $cpl_id,
                'log_action' => 'EDIT',
                'log_controller_action' => $action_name,
                'remarks_before_changes' => json_encode(array($fieldname => $row[$fieldname])),
                'remarks_after_changes' => json_encode(array($fieldname => $value, 'orders_id' => $order_id)),
                'user_ip' => tep_get_ip_address(),
                'user_role' => 'G2G Crew',
                'remarks_added_date' => "now()",
                'remarks_added_by' => isset($_SESSION['login_email_address'])? $_SESSION['login_email_address'] : 'System'
            );
            tep_db_perform(TABLE_C2C_PRODUCTS_LISTING_REMARKS_HISTORY, $data, 'insert');
            //end update remaks history
        }
    }

    public static function orderSiteID($oid) {
        $result = '';

        $_sel_sql = "	SELECT orders_extra_info_value
                        FROM " . TABLE_ORDERS_EXTRA_INFO . "
                        WHERE orders_id = '" . (int) $oid . "'
                            AND orders_extra_info_key = 'site_id'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $result = $_row_sql['orders_extra_info_value'];
        }

        return $result;
    }
    public static function getOrderCancellation($oid){ 
        $result = []; 
        include_once(DIR_WS_CLASSES . 'curl.php'); 
        $curl_obj = new curl(); 
        $G2G_CREW_ORDER_URL = G2G_API_URL . '/order/order-in-cancellation'; 
        $data = array( 
            'merchant' => G2G_API_OGCREW_MERCHANT, 
            'signature' => md5($oid . "|" . G2G_API_OGCREW_SECRET), 
            'oid' => $oid, 
        ); 
       $response = $curl_obj->curl_get($G2G_CREW_ORDER_URL, $data); 
        if($response){ 
            $result = json_decode($response, true);
        } 
        return $result; 
    } 
    public static function deleteCancellation($so_id){ 
        $result = []; 
        include_once(DIR_WS_CLASSES . 'curl.php'); 
        $curl_obj = new curl(); 
        $G2G_CREW_ORDER_URL = G2G_API_URL . '/order/delete-cancellation'; 
        $data = array( 
            'merchant' => G2G_API_OGCREW_MERCHANT, 
            'signature' => md5($so_id . "|" . G2G_API_OGCREW_SECRET), 
            'oid' => $so_id, 
        ); 
        $response = $curl_obj->curl_post($G2G_CREW_ORDER_URL, $data); 
        if($response){ 
            $result = json_decode($response, true);
        } 
        return $result; 
    } 

}