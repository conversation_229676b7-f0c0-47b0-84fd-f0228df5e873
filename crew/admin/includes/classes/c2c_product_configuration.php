<?php
/*
  	$Id: c2c_product_configuration.php,v 1.5 2014/02/10 04:07:28 wenbin.ng Exp $

	Developer: <PERSON> Yen
*/

class c2c_product_config {
	public $c2c_cpt_list;
	public $c2c_cpct_list;

	function __construct() {
            $this->c2c_cpt_list = explode(',', trim(C2C_PRODUCT_TYPE));
            $this->c2c_cpct_list = explode(',', trim(C2C_PRODUCT_TYPE_CHILD));
        }

	function menuListing() {
		$cpt = array();
		$games_id = array();
		$games_list = array();
		$games_temp = array();
		$seller_group = array();

		$listing_html = '';

		# Game List
		$_def_game_list[] = array (	'id' => '',
									'text' => SELECT_GAME_DEFAULT );
		$games_list = category::get_game_list_by_product_type_child(implode(',', $this->c2c_cpct_list));
		$games_list = array_merge($_def_game_list, $games_list);

		ob_start();
?>
		<form name="menuListing" method="post" action="<?php echo tep_href_link(FILENAME_C2C_PRODUCT_CONFIGURATION, 'action=add_form'); ?>">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td colspan="2">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td width="100" class="main" nowrap><?php echo TEXT_GAMES; ?></td>
								<td width="100%" class="main" nowrap><?php echo tep_draw_pull_down_menu('game_id', $games_list, $id, ' id="game_id" onChange="document.menuListing.submit();"'); ?></td>
							</tr>
							<tr>
								<td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	function addForm($id = "") {
		$cpct = array();
		$cpct_list = array();
		$games_id = array();
		$games_list = array();
		$games_temp = array();
		$seller_group = array();

		$listing_html = '';

		$js_cpct = '';
		$js_seller = '';

		# Game List
		$_def_game_list[] = array (	'id' => '',
									'text' => SELECT_GAME_DEFAULT );
		$games_list = category::get_game_list_by_product_type_child(implode(',', $this->c2c_cpct_list));
		$games_list = array_merge($_def_game_list, $games_list);

		# Custom Product Child Type
		$cpct_list = tep_get_product_child_type();
		for ($i=0, $cnt=count($cpct_list); $cnt > $i; $i++) {
			if (in_array($cpct_list[$i]['id'], $this->c2c_cpct_list)) {
				$db_num_rows = 0;

				if ($id != '') {
					$game_support_cpt_sql = "	SELECT categories_id
												FROM " . TABLE_CATEGORIES . "
												WHERE categories_parent_path LIKE '%_" . $id . "_%'
													AND custom_products_type_child_id = '" . $cpct_list[$i]['id'] . "'";
					$game_support_res_sql = tep_db_query($game_support_cpt_sql);
					$db_num_rows = tep_db_num_rows($game_support_res_sql);
				}

				if ((($id != '') && ($db_num_rows > 0)) || ($id == '')) {
					$cpct[] = array ('id' => $cpct_list[$i]['id'],
									'text' => $cpct_list[$i]['text'] );
					$js_cpct .= '"' . $cpct_list[$i]['id'] . '",';
				}
			}
		}

		# Seller Group
		$seller_group_sel_sql = "	SELECT seller_group_id, seller_group_name
									FROM " . TABLE_C2C_SELLER_GROUPS . "
									ORDER BY sort_order";
		$seller_group_res_sql = tep_db_query($seller_group_sel_sql);
		while ($seller_group_rows = tep_db_fetch_array($seller_group_res_sql)) {
			$seller_group[] = array (	'id' => $seller_group_rows['seller_group_id'],
										'text' => $seller_group_rows['seller_group_name'] );
			$js_seller .= '"' . $seller_group_rows['seller_group_id'] . '",';
		}

		$js_cpct = substr($js_cpct, 0, -1);
		$js_seller = substr($js_seller, 0, -1);

		ob_start();
?>
		<script language="javascript">
		<!--
			jQuery(document).ready(function() {
				jQuery("#cpt_tab > ul").tabs();
				jQuery('.cpt_tab').css({
					border:'1px solid #C9C9C9'
				});
			});


			function check_form() {
				var error = 0;
				var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

				var cpt = [<?php echo $js_cpct; ?>];
				var cpt_cnt = cpt.length;
				var seller = [<?php echo $js_seller; ?>];
				var seller_cnt = seller.length;

				for (i=0; cpt_cnt > i; i++) {
					for (j=0; seller_cnt > j; j++) {
						if (document.getElementById(cpt[i] + '_margin_' + seller[j]).value != '') {
							if (isNaN(document.getElementById(cpt[i] + '_margin_' + seller[j]).value)) {
								i = cpt_cnt + 1;
								j = seller_cnt + 1;

								error = 1;
								error_message = error_message + "* The product margin entry must be numeric or leave blank.\n";
								break;
							}
						}
					}
				}

				if (error == 1) {
					alert(error_message);
					return false;
				} else {
					return true;
				}
			}
		//-->
		</script>

		<form name="addForm" method="post" id="addForm" action="<?php echo tep_href_link(FILENAME_C2C_PRODUCT_CONFIGURATION, 'action=add'); ?>" onSubmit="return check_form();">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td colspan="2">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td width="100" class="main" nowrap><?php echo TEXT_GAMES; ?></td>
								<td width="100%" class="main" nowrap>
									<?php echo tep_draw_pull_down_menu('game_id', $games_list, $id, ' id="game_id" onChange="document.addForm.submit();"'); ?>
									<?php echo tep_draw_hidden_field('hidden_game_id', $id, ' id="hidden_game_id" '); ?>
								</td>
							</tr>
							<tr>
								<td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
							</tr>
						</table>
					</td>
				</tr>
				<?php
					$product_margin = array();

					$prod_margin_sel_sql = "	SELECT seller_group_id, custom_products_type_child_id, payout_percentage
												FROM " . TABLE_C2C_PRODUCT_MARGIN . "
												WHERE game_id = '" . $id . "'";
					$prod_margin_res_sql = tep_db_query($prod_margin_sel_sql);
					while ($prod_margin_row = tep_db_fetch_array($prod_margin_res_sql)) {
						$product_margin[$prod_margin_row['seller_group_id']][$prod_margin_row['custom_products_type_child_id']] = $prod_margin_row['payout_percentage'];
					}
				?>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td>
									<div id="cpt_tab">
										<ul>
											<?php
												for ($i=0, $cnt=count($cpct); $cnt > $i; $i++) {
													if (in_array($cpct[$i]['id'], $this->c2c_cpct_list)) {
											?>
													<li id="cpt_li_<?php echo $cpct[$i]['id']; ?>"><a href="#cpt_tab_<?php echo $cpct[$i]['id']; ?>"><span><?php echo $cpct[$i]['text']; ?></span></a></li>
											<?php
													}
												}
											?>
										</ul>

										<?php
											for ($i=0, $cnt=count($cpct); $cnt > $i; $i++) {
												$min_purchase_amt = '';
												$prod_unit_name = '';

												if (in_array($cpct[$i]['id'], $this->c2c_cpct_list)) {
													$prod_config_sel_sql = "SELECT min_purchase_amount_usd, product_unit_name, product_max_level
																			FROM " . TABLE_C2C_PRODUCT_CONFIGURATION . "
																			WHERE custom_products_type_child_id = '" . $cpct[$i]['id'] . "'
																				AND game_id = '" . $id . "'";
													$prod_config_res_sql = tep_db_query($prod_config_sel_sql);
													$prod_config_row = tep_db_fetch_array($prod_config_res_sql);
													$min_purchase_amt = $prod_config_row['min_purchase_amount_usd'];
													$prod_unit_name = $prod_config_row['product_unit_name'];
													$prod_max_level = $prod_config_row['product_max_level'];
										?>
												<div id="cpt_tab_<?php echo $cpct[$i]['id']; ?>" class="cpt_tab">
													<table border="0" width="100%" cellspacing="0" cellpadding="2">
														<tr>
															<td class="customerFormAreaTitle"><b><?php echo SUB_HEADSING_TITLE_PRODUCT_MARGIN; ?></b></td>
														</tr>
														<tr>
															<td class="formArea">
																<table border="0" width="100%" cellspacing="0" cellpadding="2">
																	<?php
																		if (tep_not_null($seller_group)) {
																			for ($j=0, $seller_cnt=count($seller_group); $seller_cnt > $j; $j++) {
																				$cpct_seller = $cpct[$i]['id'] . '_margin_' . $seller_group[$j]['id'];
																				$margin = isset($product_margin[$seller_group[$j]['id']][$cpct[$i]['id']]) ? $product_margin[$seller_group[$j]['id']][$cpct[$i]['id']] : '';
																	?>
																	<tr>
																		<td width="15%" class="main" nowrap><?php echo $seller_group[$j]['text']; ?></td>
																		<td class="main" nowrap><?php echo tep_draw_input_field($cpct_seller, $margin , ' id="' . $cpct_seller . '" size="10" maxlength="8" '); ?> %</td>
																	</tr>
																	<?php
																			}
																		} else {
																	?>
																	<tr>
																		<td colspan="2" class="main"><?php echo TEXT_NO_RECORD; ?></td>
																	</tr>
																	<?php
																		}
																	?>
																</table>
															</td>
														</tr>
													</table>

													<br />

													<table border="0" width="100%" cellspacing="0" cellpadding="2">
														<tr>
															<td class="customerFormAreaTitle"><b><?php echo SUB_HEADSING_TITLE_PRODUCT_SETTING; ?></b></td>
														</tr>
														<tr>
															<td class="formArea">
																<table border="0" width="100%" cellspacing="0" cellpadding="2">
																	<tr>
																		<td width="15%" class="main" nowrap><?php echo TEXT_MIN_PURCHASE_AMOUNT_USD; ?></td>
																		<td class="main" nowrap><?php echo tep_draw_input_field($cpct[$i]['id'] . '_min_purchase_amt', $min_purchase_amt, ' id="' . $cpct[$i]['id'] . '_min_purchase_amt" size="15" maxlength="20" '); ?></td>
																	</tr>
																	<?php
																		# Currency Only
																		if ($cpct[$i]['id'] == 1) {
																	?>
																	<tr>
																		<td width="15%" class="main" nowrap><?php echo TEXT_PRODUCT_UNIT_NAME; ?></td>
																		<td class="main" nowrap><?php echo tep_draw_input_field($cpct[$i]['id'] . '_prod_unit_name', $prod_unit_name, ' id="' . $cpct[$i]['id'] . '_prod_unit_name" size="32" maxlength="32" '); ?></td>
																	</tr>
																	<?php
																		# HLA Only
																		} else if ($cpct[$i]['id'] == 5) {
																	?>
																	<tr>
																		<td width="15%" class="main" nowrap><?php echo TEXT_PRODUCT_MAX_LEVEL; ?></td>
																		<td class="main" nowrap><?php echo tep_draw_input_field($cpct[$i]['id'] . '_prod_max_level', $prod_max_level, ' id="' . $cpct[$i]['id'] . '_prod_max_level" size="32" maxlength="32" '); ?></td>
																	</tr>
																	<?php
																		}
																	?>
																</table>
															</td>
														</tr>
													</table>
												</div>
										<?php
												}
											}
										?>
									</div>
								</td>
							</tr>
							<tr>
								<td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
							</tr>
							<tr>
								<td class="main" align="right">
									<?php echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
								</td>
							</tr>
						</table>
					</tr>
				</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}


	function addEntry($id) {
		global $messageStack;

		$error = 0;
		$error_msg = '';

		$cpct = array();
		$cpct_list = array();
		$seller_group = array();

		# Default Min Purchase Amount
		$def_min_purchase = '';
		$def_min_purchase_sel_sql = "	SELECT configuration_value FROM " . TABLE_C2C_CONFIGURATION . "
										WHERE configuration_key = 'C2C_DEFAULT_MIN_PURCHASE'";
		$def_min_purchase_res_sql = tep_db_query($def_min_purchase_sel_sql);
		if ($def_min_purchase_row = tep_db_fetch_array($def_min_purchase_res_sql)) {
			$def_min_purchase = $def_min_purchase_row['configuration_value'];
		}

		# Default Product Margin
		$def_product_margin = '';
		$def_product_margin_sel_sql = "	SELECT configuration_value FROM " . TABLE_C2C_CONFIGURATION . "
										WHERE configuration_key = 'C2C_DEFAULT_PRODUCT_MARGIN'";
		$def_product_margin_res_sql = tep_db_query($def_product_margin_sel_sql);
		if ($def_product_margin_row = tep_db_fetch_array($def_product_margin_res_sql)) {
			$def_product_margin = $def_product_margin_row['configuration_value'];
		}

		if (tep_not_null($id)) {
			$prod_margin_del_sql = "DELETE FROM " . TABLE_C2C_PRODUCT_MARGIN . " WHERE game_id = '$id'";
			tep_db_query($prod_margin_del_sql);

			$min_purchase_del_sql = "DELETE FROM " . TABLE_C2C_PRODUCT_CONFIGURATION . " WHERE game_id = '$id'";
			tep_db_query($min_purchase_del_sql);

			# Custom Product Type
			$cpct_list = tep_get_product_child_type();
			for ($i=0, $cnt=count($cpct_list); $cnt > $i; $i++) {
				if (in_array($cpct_list[$i]['id'], $this->c2c_cpct_list)) {
					$cpct[$cpct_list[$i]['id']] = $cpct_list[$i]['cpt_id'];
				}
			}

			# Seller Group
			$seller_group_sel_sql = "	SELECT seller_group_id, seller_group_name
										FROM " . TABLE_C2C_SELLER_GROUPS . "
										ORDER BY sort_order";
			$seller_group_res_sql = tep_db_query($seller_group_sel_sql);
			while ($seller_group_rows = tep_db_fetch_array($seller_group_res_sql)) {
				$seller_group[] = $seller_group_rows['seller_group_id'];
			}

			foreach($cpct as $cpct_id => $cpt_id){
                            $cpct_id = (int)$cpct_id;
                            $cpt_id = (int)$cpt_id;
				# Product Margin by Seller Group
				for ($j=0, $seller_cnt=count($seller_group); $seller_cnt > $j; $j++) {
					$data = array();
					$key = $cpct_id . '_margin_' . $seller_group[$j];

					if (tep_not_null($_POST[$key])) {
						$value = $_POST[$key];
					} else {
						$value = $def_product_margin;
					}

					$data = array(	'seller_group_id' => (int)$seller_group[$j],
									'game_id' => (int)$id,
									'custom_products_type_id' => $cpt_id,
									'custom_products_type_child_id' => $cpct_id,
									'payout_percentage' => $value );
					tep_db_perform(TABLE_C2C_PRODUCT_MARGIN, $data, 'insert');
				}

				# Product Setting
				$data = array();
				$min_key = $cpct_id . '_min_purchase_amt';
				$min_purchase_amount_usd = 0;
				$prod_unit_name = isset($_POST[$cpct_id . '_prod_unit_name']) ? $_POST[$cpct_id . '_prod_unit_name'] : '';
				$prod_max_level = isset($_POST[$cpct_id . '_prod_max_level']) ? $_POST[$cpct_id . '_prod_max_level'] : '';

				if (!isset($_POST[$min_key]) || (0 >= $_POST[$min_key])) {
					$min_purchase_amount_usd = $def_min_purchase;
				} else {
					$min_purchase_amount_usd = $_POST[$min_key];
				}

				$data = array( 	'game_id' => (int)$id,
								'custom_products_type_id' => $cpt_id,
								'custom_products_type_child_id' => $cpct_id,
								'min_purchase_amount_usd' => $min_purchase_amount_usd,
								'product_unit_name' => $prod_unit_name,
								'product_max_level' => $prod_max_level );
				tep_db_perform(TABLE_C2C_PRODUCT_CONFIGURATION, $data, 'insert');
			}

			$messageStack->add_session(SUCCESS_PRODUCT_MARGIN_UPDATED, 'success');
		} else {
			$messageStack->add_session(ERROR_INVALID_GAME_SELECTED, 'error');
		}
	}

	function _gameListComboBox () {
		global $memcache_obj;

		$games_id = array();
		$games_list = array();

		# Game List
	    $cache_key = TABLE_CATEGORIES_STRUCTURES . '/games_id/array/categories_name/';
		$cache_result = $memcache_obj->fetch($cache_key);

		if ($cache_result !== FALSE) {
			$games_list = $cache_result;
		} else {
			$games_id = tep_get_games_id();

			if (!empty($games_id)) {
				$c2c_cpt_list = implode(',', $this->c2c_cpt_list);

				$games_list[] = array ( 'id' => '',
										'text' => SELECT_GAME_DEFAULT );



				for ($i=0, $cnt=count($games_id); $cnt > $i; $i++) {
					$game_support_cpt_sql = "	SELECT categories_id
												FROM " . TABLE_CATEGORIES . "
												WHERE categories_parent_path LIKE '%_" . $games_id[$i] . "_%'
													AND custom_products_type_id IN (" . $c2c_cpt_list . ")";
					$game_support_res_sql = tep_db_query($game_support_cpt_sql);
					if (tep_db_num_rows($game_support_res_sql) > 0) {
						$games_temp[$games_id[$i]] = tep_get_categories_name($games_id[$i]);
					}
				}

				natcasesort($games_temp);

				foreach ($games_temp as $game_id => $game_name) {
					$games_list[] = array ( 'id' => $game_id,
											'text' => $game_name );
				}
			}

			$memcache_obj->store($cache_key, $games_list, 86400);
		}

		return $games_list;
	}
}
?>