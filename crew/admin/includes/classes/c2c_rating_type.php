<?php
class c2c_rating_type {
    public function __construct() {
	}
    
    public function ratingList($cptc = 1){        
        $cptc_select_sql = "SELECT custom_products_type_child_id, custom_products_type_child_name 
                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD ." 
                        WHERE display_status = '1'
                        ORDER BY sort_order";
        $cptc_result_sql = tep_db_query($cptc_select_sql);
        while ($cptc_row = tep_db_fetch_array($cptc_result_sql)) {
            $cptc_dropdown[] = array ('id' => $cptc_row['custom_products_type_child_id'], 
								'text' => $cptc_row['custom_products_type_child_name'] );
        }            
        ob_start();
        ?>        
        <script language="javascript">
		<!--
		function deleteentry(t,id) {
			answer = confirm('Are you sure to delete '+ (trim_str(t) != '' ? '<' + t + '> ' : '') + ' ?')
			if (answer !=0) { 
				jQuery.get("?action=delete&cptc="+id, function(data){
					if (data == "success"){
    		 			jQuery('#row-'+id).fadeOut('slow');
                    }   
				});
			}
		}
		//-->
		</script>
        <form name="menuListing" method="post" action="<?=tep_href_link(FILENAME_C2C_RATING_TYPE);?>">        
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
            <tr>
                <td colspan="2">
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="pageHeading" valign="top"><?=HEADING_TITLE;?></td>
                            <td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="5">
                        <tr>
                            <td width="100" class="main" nowrap><?=TEXT_PRODUCT_TYPE;?></td>
                            <td width="100%" class="main" nowrap>
                            <?php echo tep_draw_pull_down_menu('cptc', $cptc_dropdown, $cptc, ' id="cptc" onChange="document.menuListing.submit();"'); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td align="right" class="reportRecords">
                    [ <a href="?action=addForm&cptc=<?php echo $cptc; ?>"><?php echo LINK_ADD_RATING_TYPE; ?></a> ]                    
                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="1" cellpadding="2">
                        <tr>
                            <td class="reportBoxHeading" valign="top" width="38%"><?php echo SUB_TABLE_ATTRIBUTE; ?></td>
                            <td class="reportBoxHeading" valign="top" width="32%"><?php echo SUB_TABLE_RATING_TYPE; ?></td>
                            <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_DISPLAY_STATUS; ?></td>
                            <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_SORTING; ?></td>
                            <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_ACTION; ?></td>
                        </tr>
                        <?php
                        $rating_type_sql = "select crtd.c2c_rating_type_id, rating_description, rating_type, display_status, sort_order from " . TABLE_C2C_RATING_TYPE ." AS crt 
                        INNER JOIN " . TABLE_C2C_RATING_TYPE_DESCRIPTION . " AS crtd 
                        ON crt.c2c_rating_type_id = crtd.c2c_rating_type_id                         
                        WHERE crtd.language_id = 1 AND crt.custom_products_type_child_id='".$cptc."'
                        ORDER BY sort_order, rating_type";
                        $rating_type_sql = tep_db_query($rating_type_sql);                        
                        while ($cptc_row = tep_db_fetch_array($rating_type_sql)) {
                            $product_display = ((int)$cptc_row['display_status'] == 1 ? 'green' : 'red');
                            $product_display_alt = ((int)$cptc_row['display_status'] == 1 ? TEXT_SHOW : TEXT_HIDE);

                            ($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
                            ?>
                            <tr id="row-<?php echo $cptc_row['c2c_rating_type_id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
                                <td class="reportRecords" valign="top"><?php echo $cptc_row['rating_description'] ?></td>
                                <td class="reportRecords" valign="top"><?php echo $cptc_row['rating_type'] ?></td>
                                <td class="reportRecords" valign="top">
                                <?php
                                	if ($cptc_row['display_status'] == '1') {
                                        echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_C2C_RATING_TYPE, 'action=updateStatus&flag=0&rating_id='.$cptc_row['c2c_rating_type_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                                    } else {
                                        echo '<a href="' . tep_href_link(FILENAME_C2C_RATING_TYPE, 'action=updateStatus&flag=1&rating_id='.$cptc_row['c2c_rating_type_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                    }
                                ?>
                                </td>
                                <td class="reportRecords" valign="top"><?php echo $cptc_row['sort_order'] ?></td>
                                <td class="reportRecords" valign="top">
                                    <a href="<?php echo tep_href_link(FILENAME_C2C_RATING_TYPE, 'action=addForm&type=edit&rating_id='.$cptc_row['c2c_rating_type_id']); ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                    &nbsp;<a href="javascript:void(deleteentry('<?php echo $cptc_row['rating_description']; ?>','<?php echo $cptc_row['c2c_rating_type_id']; ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
                                </td>
                            </tr>
                            <?php
                            $entryCount++;
                        } 
                        ?>
                        
                    </table>
                </td>
            </tr>
        </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
		ob_end_clean();
		
		return $listing_html;
    }
    
    public function addForm($ratingId = '0', $type, $cptc_id = 1){
        $cptc_title = '';
        $rating_type = '1';
        $display_status = '1';
        $sort_order = '0';
        $formTitle = TITLE_ADD_RATING_TYPE;
        $rating_name = array();
        
        if($type == 'edit'){
            $formTitle = TITLE_EDIT_RATING_TYPE;
            $rating_type_sql = "select custom_products_type_child_id, rating_type, display_status, sort_order 
                        FROM " . TABLE_C2C_RATING_TYPE ." WHERE c2c_rating_type_id='".$ratingId."'";                       
            $rating_result_sql = tep_db_query($rating_type_sql);
            $rating_rows = tep_db_fetch_array($rating_result_sql);
            $cptc_id = isset($rating_rows['custom_products_type_child_id']) ? $rating_rows['custom_products_type_child_id'] : $cptc_id;
            $rating_type = isset($rating_rows['rating_type']) ? $rating_rows['rating_type'] : $rating_type;
            $display_status = isset($rating_rows['display_status']) ? $rating_rows['display_status'] : $display_status;
            $sort_order = isset($rating_rows['sort_order']) ? $rating_rows['sort_order'] : $sort_order;
            
            $rating_desc_sql = "SELECT language_id, rating_description FROM ".TABLE_C2C_RATING_TYPE_DESCRIPTION." 
                                WHERE c2c_rating_type_id = '".$ratingId."' 
                                ORDER BY language_id";
            $rating_desc_result_sql = tep_db_query($rating_desc_sql);
            while ($rating_desc_row = tep_db_fetch_array($rating_desc_result_sql)) {
                $rating_name[$rating_desc_row['language_id']] = $rating_desc_row['rating_description'];
            }            
        }

        $cptc_select_sql = "SELECT custom_products_type_child_id, custom_products_type_child_name 
                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD ." 
                        WHERE display_status = '1'
                        ORDER BY sort_order";
        $cptc_result_sql = tep_db_query($cptc_select_sql);
        while ($cptc_row = tep_db_fetch_array($cptc_result_sql)) {
            $cptc_dropdown[] = array ('id' => $cptc_row['custom_products_type_child_id'], 
								'text' => $cptc_row['custom_products_type_child_name'] );
        }            
        
        ob_start();
        ?>

        <script language="javascript">
		<!--
			function check_form() {

				var error = 0;
				var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

				if (jQuery("#rating_name_en").val() == "") {
					error_message = error_message + "* '<?php echo ENTRY_RATING_DESC_EN; ?>' entry must be entered.\n";
					error = 1;
				}
				
				if (error == 1) {
					alert(error_message);
					return false;
				} else {
					return true;
				}
			}
		//-->
		</script>
        <?php echo tep_draw_form('rating_form', FILENAME_C2C_RATING_TYPE, 'action=saveForm', 'post', ' onSubmit="return check_form();" id="rating_form"')?> 
            <?php echo tep_draw_hidden_field('rating_id', $ratingId); ?>
            <table border="0" width="60%" cellspacing="0" cellpadding="4">
                <tr>
                    <td colspan="3">
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo $formTitle; ?></td>
                                <td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="left" width="28%" class="main"><?php echo ENTRY_RATING_DESC_EN; ?></td><td width="1%" align="center">:</td>
                    <td width="71%" align="left" class="main">
                    <?php echo tep_draw_input_field('rating_name_en', (isset($rating_name['1']) ? $rating_name['1'] : '') , ' id="rating_name_en" size="50" maxlength="50" ');?>
                    <span class="fieldRequired">* Required</span>    
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_RATING_DESC_CNZH; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php echo tep_draw_input_field('rating_name_cnZh', (isset($rating_name['2']) ? $rating_name['2'] : '') , ' id="rating_name_en" size="50" maxlength="50" ');?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_RATING_DESC_CNTW; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php echo tep_draw_input_field('rating_name_cnTw', (isset($rating_name['3']) ? $rating_name['3'] : '') , ' id="rating_name_en" size="50" maxlength="50" ');?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_RATING_DESC_IN; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php echo tep_draw_input_field('rating_name_in', (isset($rating_name['4']) ? $rating_name['4'] : '') , ' id="rating_name_en" size="50" maxlength="50" ');?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_CPTC_ID; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php echo tep_draw_pull_down_menu('cptc', $cptc_dropdown, $cptc_id, ' id="cptc"'); ?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_TYPE; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php 
                    $typeAttr = array('0'=>array('id'=>'1',
                                                'text'=>'1 - '.TEXT_PRODUCT_RATING_ATTRIBUTE),
                                      '1'=>array('id'=>'2',
                                                'text'=>'2 - '.TEXT_PRODUCT_LEVEL));

                    echo tep_draw_pull_down_menu('type', $typeAttr, $rating_type, ' id="type"'); ?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_DISPLAY_STATUS; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php 
                    $displayAttr = array('0'=>array('id'=>'0',
                                                'text'=> TEXT_HIDE),
                                      '1'=>array('id'=>'1',
                                                'text'=> TEXT_SHOW));

                    echo tep_draw_pull_down_menu('display', $displayAttr, $display_status, ' id="display"'); ?>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="main"><?php echo ENTRY_SORT_ORDER; ?></td><td align="center" class="main">:</td>
                    <td align="left" class="main">
                    <?php echo tep_draw_input_field('sort_order', $sort_order , ' id="sort_order" size="10" maxlength="10" ');?>
                    </td>
                </tr>
                <tr>
				<td class="main" colspan="3" align="right">
					<?php echo tep_submit_button($type == 'edit' ? BUTTON_UPDATE : BUTTON_SAVE, $type == 'edit' ? ALT_BUTTON_UPDATE : ALT_BUTTON_SAVE, '', 'inputButton')?>
					<?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_C2C_RATING_TYPE,'cptc='.$cptc_id), '', 'inputButton')?>
				</td>
			</tr>
            </table>
            </form>
        <?php
        $listing_html = ob_get_contents();
		ob_end_clean();
		
		return $listing_html;
    } 
    
    public function update($dataArr, $descDataArr, $rating_id){
        tep_db_perform(TABLE_C2C_RATING_TYPE, $dataArr, 'update', "c2c_rating_type_id='" . $rating_id . "'");
        foreach($descDataArr as $key => $val){
            $descArr = array('rating_description'=>$val);
            tep_db_perform(TABLE_C2C_RATING_TYPE_DESCRIPTION, $descArr, 'update', "language_id='" . $key . "' AND c2c_rating_type_id ='".$rating_id."'");
        }
    }
    
    public function save($dataArr, $descDataArr){
        tep_db_perform(TABLE_C2C_RATING_TYPE, $dataArr, 'insert');
        $insert_id = tep_db_insert_id($query);
        
        foreach($descDataArr as $key => $val){
            $descArr = array('c2c_rating_type_id'=>$insert_id,
                            'language_id'=>$key,
                            'rating_description'=>$val
                            );
            
            tep_db_perform(TABLE_C2C_RATING_TYPE_DESCRIPTION, $descArr, 'insert');
        }
    }
    
    public function delete($cptc){
        if (tep_not_null($cptc)) {
            $rating_del_sql = "DELETE FROM " . TABLE_C2C_RATING_TYPE . " WHERE c2c_rating_type_id = '$cptc'";
            $del_rating_res_sql = tep_db_query($rating_del_sql);
            
            $rating_desc_del_sql = "DELETE FROM " . TABLE_C2C_RATING_TYPE_DESCRIPTION . " WHERE c2c_rating_type_id = '$cptc'";
            $del_rating_desc_res_sql = tep_db_query($rating_desc_del_sql);
            
            if ($del_rating_res_sql == 1 && $del_rating_desc_res_sql == 1) {
				return "success";
			}
		} else {
			return "";
		}
    }
    
    public function getCpt($cptc){
        $cpt_select_sql = "SELECT custom_products_type_id FROM ".TABLE_CUSTOM_PRODUCTS_TYPE_CHILD." 
                        WHERE custom_products_type_child_id ='".$cptc."'";
        $cpt_result_sql = tep_db_query($cpt_select_sql);
        $cpt_result_row = tep_db_fetch_array($cpt_result_sql);
        return $cpt_result_row['custom_products_type_id'];
    }
}
?>