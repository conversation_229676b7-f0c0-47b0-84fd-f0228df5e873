<?php
class c2c_seller_group_config {
    public $default_language_id = 1;
    public $language_obj = array();

    public function __construct() {
    }

    public function menuListing() {
        $entryCount = 0;
        $listing_html = '';

        // pagination
        $seller_group_sel_sql = "select seller_group_id from " . TABLE_C2C_SELLER_GROUPS;
        $page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $seller_group_sel_sql, $seller_group_numrows);

        ob_start();
        ?>
        <script language="javascript">
            <!--
            function deleteentry(t,id) {
                answer = confirm('Are you sure to delete '+ (trim_str(t) != '' ? '<' + t + '> ' : '') + ' ?')
                if (answer !=0) { 
                    jQuery.get("?action=delete&id="+id, function(data){
                        if (data == "success")
                            jQuery('#row-'+id).fadeOut('slow');
                    });
                }
            }
            //-->
        </script>

        <form name=listing>
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td colspan="2">
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="reportRecords">&nbsp;</td>
                    <td align="right" class="reportRecords">[ <a href="?action=add_form"><?php echo LINK_ADD_SETTING; ?></a> ]</td>
                </tr>
                <tr>
                    <td valign="top" colspan="2">
                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
                                <td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_GROUP_NAME; ?></td>
                                <td class="reportBoxHeading" align="center" width="30%"><?php echo SUB_TABLE_HEADING_TOTAL_COMPLETED_SALES_AMOUNT; ?></td>
                                <td class="reportBoxHeading" align="center" width="20%"><?php echo SUB_TABLE_HEADING_PAYOUT_GRACE_PERIOD_OFFSET; ?></td>
                                <td class="reportBoxHeading" align="center" width="10%"><?php echo SUB_TABLE_HEADING_SORT_ORDER; ?></td>
                            </tr>
                            <?php
                            $seller_group_sel_sql = "	SELECT seller_group_id, seller_group_name, total_completed_sales_amount, 
																payout_grace_period_offset, sort_order 
															FROM " . TABLE_C2C_SELLER_GROUPS . " 
															ORDER BY sort_order";
                            $seller_group_res_sql = tep_db_query($seller_group_sel_sql);
                            if (tep_db_num_rows($seller_group_res_sql) > 0) {
                                while ($seller_group_rows = tep_db_fetch_array($seller_group_res_sql)) {
                                    ($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
                                    ?>
                                    <tr id="row-<?php echo $seller_group_rows['seller_group_id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
                                        <td class="reportRecords" valign="top" align=center>
                                            <a href="?selected_box=c2c&action=add_form&id=<?php echo $seller_group_rows['seller_group_id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                            <?php
                                            if ($seller_group_rows['seller_group_id'] != '1') {
                                                ?>
                                                <a href="javascript:void(deleteentry('<?php echo $seller_group_rows['seller_group_name']; ?>','<?php echo htmlentities($seller_group_rows['seller_group_id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
                                                <?php
                                            }
                                            ?>
                                        </td>
                                        <td class="reportRecords" valign="top"><?= $seller_group_rows['seller_group_name']; ?></td>
                                        <td class="reportRecords" align="right" valign="top"><?= $seller_group_rows['total_completed_sales_amount']; ?></td>
                                        <td class="reportRecords" align="right" valign="top"><?= $seller_group_rows['payout_grace_period_offset']; ?></td>
                                        <td class="reportRecords" align="right" valign="top"><?= $seller_group_rows['sort_order']; ?></td>
                                    </tr>
                                    <?php
                                    $entryCount++;
                                }
                            } else {
                                ?>
                                <tr class="reportListingEven">
                                    <td class="reportRecords" align="center" colspan="5"><i>Empty</i></td>
                                </tr>
            <?php
        }
        ?>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td valign="top" colspan="2">
                        <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="smallText" valign="top" nowrap><?= $page_split_object->display_count($seller_group_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_GROUP) ?></td>
                                <td class="smallText" align="right"><?= $page_split_object->display_links($seller_group_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1") ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addForm($id = "") {
        $listing_html = '';
        $payout_sign_minus = '';
        $payout_sign_plus = ' selected';
        $countries_array = array();
        $orders_tax_status_active = false;
        $orders_tax_status_inactive = true;

        if ($id) {
            $seller_group_sel_sql = "	SELECT seller_group_name, total_completed_sales_amount, payout_grace_period_offset, sort_order 
										FROM " . TABLE_C2C_SELLER_GROUPS . "
										WHERE seller_group_id = '" . $id . "'";
            $seller_group_res_sql = tep_db_query($seller_group_sel_sql);
            if (tep_db_num_rows($seller_group_res_sql) > 0) {
                $seller_group_rows = tep_db_fetch_array($seller_group_res_sql);

                if ($seller_group_rows['payout_grace_period_offset'] < 0) {
                    $payout_sign_minus = ' selected';
                    $payout_sign_plus = '';
                }
            }
        }

        ob_start();
        ?>
        <script language="javascript">
            <!--
            function check_form() {
                var error = 0;
                var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";
        				
                if (document.menu_form.seller_group_name.value == "") {
                    error_message = error_message + "* 'Group Name' entry must be entered.\n";
                    error = 1;
                }
        				
                if (document.menu_form.total_completed_sales_amount.value == "" || !isFinite(document.menu_form.total_completed_sales_amount.value)) {
                    error_message = error_message + "* 'Total Completed Sales Amount' entry must be entered.\n";
                    error = 1;
                }
        				
                if (document.menu_form.payout_grace_period_offset.value == "" || !isFinite(document.menu_form.payout_grace_period_offset.value)) {
                    error_message = error_message + "* 'Payout Grace Period Offset' entry must be entered.\n";
                    error = 1;
                }
        				
                if (error == 1) {
                    alert(error_message);
                    return false;
                } else {
                    return true;
                }
            }
            //-->
        </script>

        <?= tep_draw_form('menu_form', FILENAME_C2C_SELLER_GROUP, 'action=add', 'post', ' onSubmit="return check_form();" id="menu_form"') ?>
        <table cellspacing="2" cellpadding="2" border="0">
            <tr>
                <td>
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td class="pageHeading" valign="top">
                                <?php
                                if ($id != "") {
                                    echo HEADING_EDIT_MENU;
                                    echo tep_draw_hidden_field('id', $id);
                                } else {
                                    echo HEADING_ADD_MENU;
                                }
                                ?>
                            </td>
                            <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" cellpadding="2" cellspacing="2">
                        <tr>
                            <td class="main" valign=top><?php echo ENTRY_GROUP_NAME; ?></td>
                            <td class="main">
        <?= tep_draw_input_field('seller_group_name', ($seller_group_rows['seller_group_name']) ? $seller_group_rows['seller_group_name'] : '', ' id="seller_group_name" size="40" maxlength="32" '); ?>
                                <span class="fieldRequired">* Required</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="main" valign=top><?php echo ENTRY_TOTAL_COMPLETED_SALES_AMOUNT; ?></td>
                            <td class="main">
        <?= tep_draw_input_field('total_completed_sales_amount', ($seller_group_rows['total_completed_sales_amount']) ? $seller_group_rows['total_completed_sales_amount'] : '0.00', ' id="total_completed_sales_amount" size="20" maxlength="18" '); ?>
        <?= ENTRY_DEF_UPGRADE_REQ; ?>
                                <span class="fieldRequired">* Required</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="main" valign=top><?php echo ENTRY_PAYOUT_GRACE_PERIOD_OFFSET; ?></td>
                            <td class="main">
                                <select name="payout_sign">
                                    <option name="minus" value="-" <?= $payout_sign_minus; ?>>-</option>
                                    <option name="plus" value="+" <?= $payout_sign_plus; ?>>+</option>
                                </select>
        <?php echo tep_draw_input_field('payout_grace_period_offset', ltrim($seller_group_rows['payout_grace_period_offset'], '-'), 'id="payout_grace_period_offset" size="12" maxlength="9"') . '&nbsp;' . TEXT_MINUTE; ?>
                                <span class="fieldRequired">* Required</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="main" valign=top><?php echo ENTRY_SORT_ORDER; ?></td>
                            <td class="main">
        <?= tep_draw_input_field('sort_order', ($seller_group_rows['sort_order']) ? $seller_group_rows['sort_order'] : '50000', ' id="sort_order" size="5" maxlength="5" '); ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
            </tr>
            <tr>
                <td class="main" align="right">
        <?= tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton') ?>
        <?= tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_C2C_SELLER_GROUP), '', 'inputButton') ?>
                </td>
            </tr>
        </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addEntry($id="") {
        global $messageStack;

        $error = 0;
        $error_msg = '';
        $data = array();
        $extra_sel_sql = '';

        if (tep_not_null($_POST['seller_group_name'])) {
            if ($id) {
                $extra_sel_sql = " AND seller_group_id <> '" . $id . "' ";
            }

            $seller_group_sel_sql = "	SELECT seller_group_name 
										FROM " . TABLE_C2C_SELLER_GROUPS . "
										WHERE seller_group_name = '" . $_POST['seller_group_name'] . "'" .
                    $extra_sel_sql;
            $seller_group_res_sql = tep_db_query($seller_group_sel_sql);
            if (tep_db_num_rows($seller_group_res_sql) > 0) {
                $seller_group_rows = tep_db_fetch_array($seller_group_res_sql);

                $error = 1;
                $error_msg = $seller_group_rows['seller_group_name'];
            } else {
                $seller_group_sel_sql = "	SELECT total_completed_sales_amount 
											FROM " . TABLE_C2C_SELLER_GROUPS . "
											WHERE total_completed_sales_amount = '" . $_POST['total_completed_sales_amount'] . "'" .
                        $extra_sel_sql;
                $seller_group_res_sql = tep_db_query($seller_group_sel_sql);
                if (tep_db_num_rows($seller_group_res_sql) > 0) {
                    $seller_group_rows = tep_db_fetch_array($seller_group_res_sql);

                    $error = 2;
                    $error_msg = $seller_group_rows['total_completed_sales_amount'];
                }
            }

            if ($error == 0) {
                $payout_grace_period_offset = $_POST['payout_sign'] . (int) $_POST['payout_grace_period_offset'];

                $data = array('seller_group_name' => tep_db_prepare_input($_POST['seller_group_name']),
                    'total_completed_sales_amount' => ($_POST['total_completed_sales_amount']),
                    'payout_grace_period_offset' => $payout_grace_period_offset,
                    'sort_order' => (int) $_POST['sort_order']);
                if ($id) {
                    $editEntry = true;
                    tep_db_perform(TABLE_C2C_SELLER_GROUPS, $data, 'update', "seller_group_id='" . $id . "'");
                } else {
                    tep_db_perform(TABLE_C2C_SELLER_GROUPS, $data, 'insert');
                }
            } else {
                if ($error == 1) {
                    $messageStack->add_session(sprintf(ERROR_GROUP_NAME_EXIST, $error_msg), 'error');
                } else if ($error == 2) {
                    $messageStack->add_session(sprintf(ERROR_TOTAL_COMPLETED_SALES_AMOUNT_EXIST, $error_msg), 'error');
                }
            }
        }
    }

    public function deleteEntry($id="") {
        if (tep_not_null($id) && $id != 1) {
            $seller_group_del_sql = "DELETE FROM " . TABLE_C2C_SELLER_GROUPS . " WHERE seller_group_id = '$id'";
            $seller_group_res_sql = tep_db_query($seller_group_del_sql);

            if ($seller_group_res_sql == 1) {
                return "success";
            }
        } else {
            return "";
        }
    }

}
?>