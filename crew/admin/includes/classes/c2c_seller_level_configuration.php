<?php

class c2c_seller_level_configuration {

    public function __construct() {
        
    }

    public function levelConfig($cptc = 1) {
        $cptc_select_sql = "SELECT custom_products_type_child_id, custom_products_type_child_name
                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
                        WHERE display_status = '1'
                        ORDER BY sort_order";
        $cptc_result_sql = tep_db_query($cptc_select_sql);
        while ($cptc_row = tep_db_fetch_array($cptc_result_sql)) {
            $cptc_dropdown[] = array('id' => $cptc_row['custom_products_type_child_id'],
                'text' => $cptc_row['custom_products_type_child_name']);
        }

        $config_select_sql = "SELECT min_level, max_level, orders_required, accumulated_orders
                                    FROM " . TABLE_C2C_SELLER_LEVEL_CONFIGURATION . "
                                    WHERE custom_products_type_child_id = '" . $cptc . "'
                                    ORDER BY min_level";
        $config_result_sql = tep_db_query($config_select_sql);
        $configExist = tep_db_num_rows($config_result_sql);

        ob_start();
        ?>

        <script language="javascript">
            <!--
            function deleteentry(id) {
                var e = document.getElementById("cptc");
                var t = e.options[e.selectedIndex].text;
                answer = confirm('Are you sure to delete ' + (trim_str(t) != '' ? '<' + t + '> ' : '') + ' ?')
                if (answer != 0) {
                    jQuery.get("?action=delete&cptc=" + id, function (data) {
                        if (data == "success") {
                            location.reload(true);
                        }
                    });
                }
            }
            //-->
        </script>
        <form name="menuListing" method="post" action="<?php echo tep_href_link(FILENAME_C2C_SELLER_LEVEL_CONFIGURATION); ?>">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                <td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="5">
                            <tr>
                                <td width="100" class="main" nowrap><?php echo TEXT_PRODUCT_TYPE; ?></td>
                                <td width="100%" class="main" nowrap>
                                    <?php
                                    echo '<div style="float:left;">' . tep_draw_pull_down_menu('cptc', $cptc_dropdown, $cptc, ' id="cptc" onChange="document.menuListing.submit();"') . '</div>';
                                    $display = ($configExist == 0) ? 'block' : 'none';
                                    echo '<div id="add-' . $cptc . '" style="padding-left:5px;float:left;display:' . $display . '">[ <a href="?action=addForm&cptc=' . $cptc . '">' . LINK_ADD_LEVEL_CONFIG_SETTING . '</a> ]</div>';
                                    ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="reportRecords">
                        <table border="0" width="80%" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="reportBoxHeading" valign="top" width="20%"><?php echo SUB_TABLE_ACTION; ?></td>
                                <td class="reportBoxHeading" valign="top" width="20%"><?php echo SUB_MIN_LEVEL; ?></td>
                                <td class="reportBoxHeading" valign="top" width="20%"><?php echo SUB_MAX_LEVEL; ?></td>
                                <td class="reportBoxHeading" valign="top" width="20%"><?php echo SUB_ORDER_REQUIRED; ?></td>
                                <td class="reportBoxHeading" valign="top" width="20%"><?php echo SUB_ACCUMULATED_ORDERS; ?></td>
                            </tr>

                            <?php
                            $firstRow = 1;
                            while ($config_row = tep_db_fetch_array($config_result_sql)) {
                                ($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
                                ?>
                                <tr id="row-<?php echo $cptc; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
                                    <?php
                                    if ($firstRow) {
                                        $firstRow = 0;
                                        ?>
                                        <td class="reportRecords" rowspan="<?php echo $configExist; ?>" valign="top">
                                            <a href="<?php echo tep_href_link(FILENAME_C2C_SELLER_LEVEL_CONFIGURATION, 'action=addForm&type=edit&cptc=' . $cptc); ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                            &nbsp;<a href="javascript:void(deleteentry('<?php echo $cptc; ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
                                        </td>
                                        <?php
                                    }
                                    ?>
                                    <td class="reportRecords" valign="top"><?php echo $config_row['min_level'] ?></td>
                                    <td class="reportRecords" valign="top"><?php echo $config_row['max_level'] ?></td>
                                    <td class="reportRecords" valign="top"><?php echo $config_row['orders_required'] ?></td>
                                    <td class="reportRecords" valign="top"><?php echo $config_row['accumulated_orders'] ?></td>
                                </tr>
                                <?php
                                $entryCount++;
                            }
                            ?>
                        </table>
                    </td>
                </tr>
            </table>
            <?php
            $listing_html = ob_get_contents();
            ob_end_clean();

            return $listing_html;
        }

        public function addForm($cptc_id = 1) {
            $data = array();
            $formTitle = TITLE_ADD_SELLER_LEVEL_CONFIGURATION;

            $cptc_select_sql = "SELECT custom_products_type_child_name FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
                WHERE custom_products_type_child_id = '" . $cptc_id . "'";
            $cptc_result_sql = tep_db_query($cptc_select_sql);
            $cptc_row = tep_db_fetch_array($cptc_result_sql);

            $formTitle = TITLE_EDIT_SELLER_LEVEL_CONFIGURATION;
            $config_select_sql = "SELECT c2c_seller_level_configuration_id, min_level, max_level, orders_required, accumulated_orders
                                    FROM " . TABLE_C2C_SELLER_LEVEL_CONFIGURATION . "
                                    WHERE custom_products_type_child_id = '" . $cptc_id . "'
                                    ORDER BY min_level";

            $config_result_sql = tep_db_query($config_select_sql);
            while ($config_row = tep_db_fetch_array($config_result_sql)) {
                $data[$config_row['min_level']] = array('config_id' => $config_row['c2c_seller_level_configuration_id'],
                    'min_level' => $config_row['min_level'],
                    'max_level' => $config_row['max_level'],
                    'orders_required' => $config_row['orders_required'],
                    'accumulated_orders' => $config_row['accumulated_orders'],
                );
            }
            ob_start();
            ?>

            <script language="javascript">
                <!--
                function create_level() {
                    var row = document.getElementById("sample"); // find row to copy
                    var table = document.getElementById("config_tbl").getElementsByTagName("tbody")[0]; // find table to append to
                    var clone = row.cloneNode(true); // copy children too
                    var ele = document.querySelectorAll('tr[id^="row_"]');
                    var cnt = 0;
                    if (ele.length > 0) {
                        var str = ele[ele.length - 1].id.split("_");
                        var cnt = str[1];
                    }
                    var num = (Number(cnt) + 10);
                    var rid = "row_" + num;
                    clone.id = rid; // change id or other attributes/contents
                    table.appendChild(clone); // add new row to end of table
                    document.getElementById(rid).getElementsByTagName("input")[0].setAttribute("id", "from_" + num);
                    document.getElementById(rid).getElementsByTagName("input")[0].setAttribute("name", "from_" + num);
                    document.getElementById(rid).getElementsByTagName("input")[1].setAttribute("id", "to_" + num);
                    document.getElementById(rid).getElementsByTagName("input")[1].setAttribute("name", "to_" + num);
                    document.getElementById(rid).getElementsByTagName("input")[2].setAttribute("id", "order_" + num);
                    document.getElementById(rid).getElementsByTagName("input")[2].setAttribute("name", "order_" + num);
                    document.getElementById(rid).getElementsByTagName("a")[0].setAttribute("href", "javascript:document.getElementById('" + rid + "').remove();");
                    document.getElementById(rid).removeAttribute("style");
                }
                //-->
            </script>
            <?php echo tep_draw_form('config_form', FILENAME_C2C_SELLER_LEVEL_CONFIGURATION, 'action=saveForm', 'post', ' id="config_form"') ?>
            <?php echo tep_draw_hidden_field('cptc', $cptc_id); ?>
            <table id="config_tbl" border="0" width="60%" cellspacing="0" cellpadding="4">
                <tr>
                    <td colspan="4">
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo $formTitle; ?></td>
                                <td class="pageHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="left">
                        <?php echo TEXT_PRODUCT_TYPE . ' : ' . $cptc_row['custom_products_type_child_name']; ?>
                    </td>
                    <td colspan="2" align="right"><a href="javascript:void(create_level());">[Add Level]</a></td>
                </tr>
                <tr>
                    <td class="main reportBoxHeading" width="30%"><?php echo SUB_MIN_LEVEL ?></td>
                    <td class="main reportBoxHeading" width="30%"><?php echo SUB_MAX_LEVEL ?></td>
                    <td class="main reportBoxHeading" width="30%"><?php echo SUB_ORDER_REQUIRED ?></td>
                    <td class="main reportBoxHeading" width="10%">&nbsp;</td>
                </tr>
                <?php if ($data) { ?>
                    <?php foreach ($data as $cnt => $val) { ?>
                        <tr id="row_<?php echo $cnt; ?>">
                            <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('from_' . $cnt, (isset($data[$cnt]['min_level']) ? $data[$cnt]['min_level'] : ''), ' id="from_' . $cnt . '" size="10" maxlength="10" '); ?></td>
                            <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('to_' . $cnt, (isset($data[$cnt]['max_level']) ? $data[$cnt]['max_level'] : ''), ' id="to_' . $cnt . '" size="10" maxlength="10" '); ?></td>
                            <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('order_' . $cnt, (isset($data[$cnt]['orders_required']) ? $data[$cnt]['orders_required'] : ''), ' id="orders_' . $cnt . '" size="10" maxlength="10" '); ?></td>
                            <td class="main" width="20%" align="center"><a href="javascript:document.getElementById('row_<?php echo $cnt; ?>').remove();"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="top"></a></td>
                        </tr>
                    <?php } ?>
                <?php } ?>
                <tr id="sample" style="display: none;">
                    <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('_from', '', ' id="_from" size="10" maxlength="10" '); ?></td>
                    <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('_to', '', ' id="_to" size="10" maxlength="10" '); ?></td>
                    <td class="main" width="20%" align="left"><?php echo tep_draw_input_field('_order', '', ' id="_order" size="10" maxlength="10" '); ?></td>
                    <td class="main" width="20%" align="center"><a href="#"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="top"></a></td>
                </tr>
            </table>
            <?php echo tep_submit_button(BUTTON_SAVE, ALT_BUTTON_SAVE, '', 'inputButton') ?>
            <?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_C2C_SELLER_LEVEL_CONFIGURATION, 'cptc=' . $cptc_id), '', 'inputButton') ?>
            <?php
            $listing_html = ob_get_contents();
            ob_end_clean();

            return $listing_html;
        }

        public function save($orderRequired, $cptc) {
            global $messageStack;

            $cptc_select_sql = "SELECT custom_products_type_id FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " WHERE custom_products_type_child_id = '" . $cptc . "'";
            $cptc_result_sql = tep_db_query($cptc_select_sql);
            $cptc_row = tep_db_fetch_array($cptc_result_sql);
            $cpt = $cptc_row['custom_products_type_id'];
            if ($cpt !== "") {
                tep_db_query("DELETE FROM " . TABLE_C2C_SELLER_LEVEL_CONFIGURATION . " WHERE custom_products_type_child_id = '" . $cptc . "'");

                $accumulate = 0;
                foreach ($orderRequired as $key => $val) {
                    $accumulate += ($val["orders_required"] * ($val["max_level"] - $val["min_level"] + (($val["min_level"] - 1 > 0) ? 1 : 0)));
                    $configArr = array(
                        'custom_product_type' => $cpt,
                        'custom_products_type_child_id' => $cptc,
                        'min_level' => $val["min_level"],
                        'max_level' => $val["max_level"],
                        'orders_required' => $val["orders_required"],
                        'accumulated_orders' => $accumulate
                    );
                    tep_db_perform(TABLE_C2C_SELLER_LEVEL_CONFIGURATION, $configArr, 'insert');
                }
                $messageStack->add_session("Success : configuration save", 'success');
                return "success";
            }
        }

        public function delete($cptc) {
            global $messageStack;
            
            if (tep_not_null($cptc)) {
                $seller_level_del_sql = "DELETE FROM " . TABLE_C2C_SELLER_LEVEL_CONFIGURATION . " WHERE custom_products_type_child_id = '$cptc'";
                $del_seller_level_res_sql = tep_db_query($seller_level_del_sql);
                if ($del_seller_level_res_sql == 1) {
                    $messageStack->add_session("Success : configuration delete", 'success');
                    return "success";
                }
            }
        }

    }
    ?>