<?php
/*
  $Id: c2c_seller_product_listing.php,v 1.25 2015/06/26 06:40:54 wenbin.ng Exp $

  Developer: <PERSON> Yen
 */

class c2c_seller_product_listing {

    var $c2c_cpt_list = array(0, 4, 5);

    public function __construct() {
        
    }

    public function menuListing() {
        if (isset($_SESSION['c2c_prod_list_params'])) {
            unset($_SESSION['c2c_prod_list_params']);
        }

        # Search By
        $search_opt = array(
            array('id' => '',
                'text' => SELECT_DEFAULT_EMPTY),
            array('id' => 'c2c_products_listing_id',
                'text' => SELECT_C2C_PRODUCTS_LISTING_ID),
            array('id' => 'seller_id',
                'text' => SELECT_SELLER_ID),
            array('id' => 'customers_email_address',
                'text' => SELECT_CUSTOMERS_EMAIL_ADDRESS),
        );

        # C2C Customer Product Type
        $cpt_opt = $this->_get_custom_product_type();

        # Game List
        $_def_game_list[] = array('id' => '', 'text' => SELECT_DEFAULT_EMPTY);
        $games_list = category::get_game_list_by_product_type(implode(',', $this->c2c_cpt_list));
        $games_list = array_merge($_def_game_list, $games_list);

        ob_start();
        ?>
        <script language="javascript">
            <!--
            function check_form() {
                var error = 0;
                var error_message = "Errors have occured during the process of your search!\nPlease make the following corrections:\n\n";

                if (document.listing.start_date.value == "" && document.listing.end_date.value == "" && document.listing.search_text.value == "") {
                    error_message = error_message + "* Fill-in at least 'Start Date' and 'End Date', or 'Search By' criteria.\n";
                    error = 1;
                }

                if ((document.listing.start_date.value != "" && document.listing.end_date.value == "") ||
                        (document.listing.start_date.value == "" && document.listing.end_date.value != "")) {
                    error_message = error_message + "* Fill-in 'Start Date' and 'End Date'.\n";
                    error = 1;
                }

                if (document.listing.start_date.value != "" && document.listing.end_date.value != "" && !diffStartAndEndDate(document.listing.start_date.value, document.listing.end_date.value, 31)) {
                    error_message = error_message + "* Date range not more than 31 days.\n";
                    error = 1;
                }

                if (document.listing.search_text.value != "" && document.listing.search_opt.value == "") {
                    error_message = error_message + "* 'Search By' option must be selected.\n";
                    error = 1;
                }

                if (document.listing.search_text.value == "" && document.listing.search_opt.value != "") {
                    var sel = document.listing.search_opt;
                    error_message = error_message + "* 'Search By : " + sel.options[sel.selectedIndex].text + "' entry must be entered.\n";
                    error = 1;
                }

                if (error == 1) {
                    alert(error_message);
                    return false;
                } else {
                    return true;
                }
            }
            //-->
        </script>

        <form name="listing" method="post" id="addForm" action="<?= tep_href_link(FILENAME_C2C_SELLER_PRODUCT_LISTING, 'action=search_result'); ?>" onSubmit="return check_form();">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_SEARCH_CRITERIA; ?></td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="reportRecords">
                        <div>
                            <iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?php echo DIR_WS_INCLUDES; ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td valign="top">
                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                            <tr>
                                <td class="main" width="15%" align="left"><?php echo ENTRY_START_CREATE_DATE; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('start_date', '', 'id="start_date" size="20" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.listing.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.listing.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'; ?></td>
                                <td class="main" width="15%" align="left"><?php echo ENTRY_END_CREATE_DATE; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('end_date', '', 'id="end_date" size="20" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.listing.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.listing.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'; ?></td>
                            </tr>
                            <tr>
                                <td class="main" align="left"><?php echo ENTRY_SEARCH_BY; ?></td>
                                <td class="main" colspan="3">
                                    <?php
                                    echo tep_draw_input_field('search_text', '', ' id="search_text" size="40"');
                                    echo str_repeat('&nbsp', 3);
                                    echo tep_draw_pull_down_menu('search_opt', $search_opt, '', ' id="search_opt"');
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" align="left"><?php echo ENTRY_GAME_CATEGORIES; ?></td>
                                <td class="main" colspan="3"><?php echo tep_draw_pull_down_menu('games_list', $games_list, '', ' id="games_list"'); ?></td>
                            </tr>
                            <tr>
                                <td class="main" align="left"><?php echo ENTRY_PRODUCT_TYPE; ?></td>
                                <td class="main" colspan="3"><?php echo tep_draw_pull_down_menu('cpt_opt', $cpt_opt, '', ' id="cpt_opt"'); ?></td>
                            </tr>
                            <tr>
                                <td colspan="4"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                            </tr>
                            <tr>
                                <td class="main" colspan="4" align="right"><?php echo tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, '', 'inputButton') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function searchResult() {
        $error = 0;
        $tr_class = '';

        $_cond_str = '';
        $_cond_sql = array();
        $_rows = array();

        if (isset($_SESSION['c2c_prod_list_params']) && !empty($_SESSION['c2c_prod_list_params']) && empty($_POST)) {
            foreach ($_SESSION['c2c_prod_list_params'] as $key => $val) {
                $_POST[$key] = $val;
            }
            unset($_SESSION['c2c_prod_list_params']);
        }

        if (isset($_POST['start_date']) && ($_POST['start_date'] != '')) {
            $_cond_sql[] = " cpl.created_date >= '" . $_POST['start_date'] . "' ";
            $_SESSION['c2c_prod_list_params']['start_date'] = $_POST['start_date'];
        }

        if (isset($_POST['end_date']) && ($_POST['end_date'] != '')) {
            $end_datetime = $_POST['end_date'];
            $end_date_timestamp = strtotime($end_datetime);
            $new_end_datetime = date("Y-m-d H:i", $end_date_timestamp);

            if ($end_datetime != $new_end_datetime) {
                $end_datetime = date("Y-m-d", $end_date_timestamp) . " 23:59:59";
            }
            $_cond_sql[] = " cpl.created_date <= '" . $end_datetime . "' ";
            $_SESSION['c2c_prod_list_params']['end_date'] = $_POST['end_date'];
        }

        if ((isset($_POST['search_opt']) && ($_POST['search_opt'] != '')) && (isset($_POST['search_text']) && ($_POST['search_text'] != ''))) {
            if ($_POST['search_opt'] == 'customers_email_address') {
                $_sub_sel_sql = "	SELECT customers_id FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_prepare_input($_POST['search_text']) . "'";
                $_sub_res_sql = tep_db_query($_sub_sel_sql);
                if ($_sub_row = tep_db_fetch_array($_sub_res_sql)) {
                    $_cond_sql[] = " cpl.seller_id = '" . (int) $_sub_row['customers_id'] . "' ";
                } else {
                    $error = 1;
                }
            } else {
                $_cond_sql[] = " cpl." . $_POST['search_opt'] . " = '" . tep_db_prepare_input($_POST['search_text']) . "' ";
            }
            $_SESSION['c2c_prod_list_params']['search_opt'] = $_POST['search_opt'];
            $_SESSION['c2c_prod_list_params']['search_text'] = $_POST['search_text'];
        }

        if (isset($_POST['games_list']) && ($_POST['games_list'] != '')) {
            $_cond_sql[] = " cpl.game_id = '" . (int) $_POST['games_list'] . "' ";
            $_SESSION['c2c_prod_list_params']['games_list'] = $_POST['games_list'];
        }

        if (isset($_POST['cpt_opt']) && ($_POST['cpt_opt'] != '')) {
            $_cond_sql[] = " cpl.custom_products_type = '" . (int) $_POST['cpt_opt'] . "' ";
            $_SESSION['c2c_prod_list_params']['cpt_opt'] = $_POST['cpt_opt'];
        }

        ob_start();
        ?>
        <form name="listing">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td>
                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="reportRecords">[ <a href="<?php echo tep_href_link(FILENAME_C2C_SELLER_PRODUCT_LISTING); ?>"><?php echo LINK_SEARCH_CRITERIA; ?></a> ]</td>
                </tr>
                <tr>
                    <td valign="top">
                        <table border="0" width="100%" cellspacing="1" cellpadding="2" style="border-collapse: collapse;">
                            <tr>
                                <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_PRODUCT_LIST_ID; ?></td>
                                <td class="reportBoxHeading" valign="top" width="08%"><?php echo SUB_TABLE_PRODUCT_CREATED_DATE; ?></td>
                                <td class="reportBoxHeading" valign="top" width="08%"><?php echo SUB_TABLE_PRODUCT_EXPIRY_DATE; ?></td>
                                <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_GAME_NAME; ?></td>
                                <td class="reportBoxHeading" valign="top" width="09%"><?php echo SUB_TABLE_PRODUCT_TYPE; ?></td>
                                <td class="reportBoxHeading" valign="top" width="10%"><?php echo SUB_TABLE_SELLER_NAME; ?></td>
                                <td class="reportBoxHeading" valign="top" width="15%"><?php echo SUB_TABLE_SELLER_EMAIL; ?></td>
                                <td class="reportBoxHeading" valign="top" align="center" width="15%"><?php echo SUB_TABLE_PRODUCT_PRICE_PER_UNIT; ?></td>
                                <td class="reportBoxHeading" valign="top" align="center" width="05%"><?php echo SUB_TABLE_PRODUCT_STATUS; ?></td>
                                <td class="reportBoxHeading" valign="top" align="center" width="05%"><?php echo SUB_TABLE_PRODUCT_DISPLAY; ?></td>
                                <td class="reportBoxHeading" valign="top" align="center" width="05%"><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
                            </tr>
                            <?php
                            if ($error == 0 && !empty($_cond_sql)) {
                                $_cond_str = implode(" AND ", $_cond_sql);
                                $_sel_sql = "	SELECT cpl.c2c_products_listing_id, cpl.seller_id, cpl.game_id, cpl.custom_products_type, cpl.products_price,
														cpl.products_base_currency, cpl.products_status, cpl.products_display, cpl.created_date, cpl.expiry_date,
														c.customers_firstname, c.customers_lastname, c.customers_email_address
													FROM " . TABLE_C2C_PRODUCTS_LISTING . " AS cpl
													LEFT JOIN " . TABLE_CUSTOMERS . " AS c
														ON c.customers_id = cpl.seller_id
													WHERE " . $_cond_str . "
													ORDER BY cpl.created_date ";

                                // pagination
                                $page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $_sel_sql, $_sel_numrows, true);

                                $_res_sql = tep_db_query($_sel_sql);
                                while ($_rows = tep_db_fetch_array($_res_sql)) {
                                    ($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";

                                    $product_status = constant('TEXT_STATUS_' . str_replace('-', '_', $_rows['products_status']));
                                    $product_display = ((int) $_rows['products_display'] == 1 ? 'green' : 'red');
                                    $product_display_alt = ((int) $_rows['products_display'] == 1 ? TEXT_SHOW : TEXT_HIDE);
                                    ?>
                                    <tr class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
                                        <td class="reportRecords" valign="top"><a href="?selected_box=c2c&action=add_form&id=<?php echo $_rows['c2c_products_listing_id']; ?>"><?php echo $_rows['c2c_products_listing_id']; ?></a></td>
                                        <td class="reportRecords" valign="top"><?php echo $_rows['created_date']; ?></td>
                                        <td class="reportRecords" valign="top"><?php echo $_rows['expiry_date']; ?></td>
                                        <td class="reportRecords" valign="top"><?php echo tep_get_categories_name($_rows['game_id'], 1); ?></td>
                                        <td class="reportRecords" valign="top"><?php echo tep_get_custom_product_type_name($_rows['custom_products_type']); ?></td>
                                        <td class="reportRecords" valign="top"><?php echo ($_rows['customers_firstname'] != '' || $_rows['customers_lastname'] != '' ? '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $_rows['seller_id'] . '&action=edit') . '" target="_blank">' . $_rows['customers_firstname'] . ' ' . $_rows['customers_lastname'] . '</a>' : TEXT_NOT_AVAILABLE); ?></td>
                                        <td class="reportRecords" valign="top"><?php echo ($_rows['customers_email_address'] != '' ? $_rows['customers_email_address'] : TEXT_NOT_AVAILABLE); ?></td>
                                        <td class="reportRecords" valign="top" align="right"><?php echo $_rows['products_base_currency'] . ' ' . $_rows['products_price']; ?></td>
                                        <td class="reportRecords" valign="top" align="center"><?php echo $product_status; ?></td>
                                        <td class="reportRecords" valign="top" align="center"><img src="<?php echo DIR_WS_IMAGES . 'icon_status_' . $product_display . '.gif'; ?>" border="0" alt="<?php echo $product_display_alt; ?>" title="<?php echo $product_display_alt; ?>" /></td>
                                        <td class="reportRecords" valign="top" align="center"><a href="?selected_box=c2c&action=add_form&id=<?php echo $_rows['c2c_products_listing_id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a></td>
                                    </tr>
                                    <?php
                                    $entryCount++;
                                }
                            } else {
                                ?>
                                <tr class="reportListingEven">
                                    <td class="reportRecords" align="center" colspan="11"><i>Empty</i></td>
                                </tr>
                                <?php
                            }
                            ?>
                        </table>
                    </td>
                </tr>
                <?php
                if ($error == 0 && !empty($_cond_sql)) {
                    ?>
                    <tr>
                        <td valign="top" colspan="2">
                            <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                                <tr>
                                    <td class="smallText" valign="top" nowrap><?= $page_split_object->display_count($_sel_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_RECORD) ?></td>
                                    <td class="smallText" align="right"><?= $page_split_object->display_links($_sel_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1") ?></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <?php
                }
                ?>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addForm($id) {
        global $messageStack;

        if ($id != "") {
            $_prod_sel_sql = "SELECT `cpl`.*,`cptcl`.`custom_products_type_child_name` FROM " . TABLE_C2C_PRODUCTS_LISTING . " AS cpl
                 LEFT JOIN " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . " AS cptcl
                  ON cpl.`custom_products_type_child_id` = cptcl.`custom_products_type_child_id` 
                  AND `cptcl`.`languages_id` = '1'
                WHERE c2c_products_listing_id = '" . (int) $id . "'";
            $_prod_res_sql = tep_db_query($_prod_sel_sql);
            if (tep_db_num_rows($_prod_res_sql) > 0) {
                $_products_status_opt = array(
                    array('id' => -2, 'text' => TEXT_STATUS__2),
                    array('id' => -1, 'text' => TEXT_STATUS__1),
                    array('id' => 0, 'text' => TEXT_STATUS_0),
                    array('id' => 1, 'text' => TEXT_STATUS_1),
                    array('id' => 2, 'text' => TEXT_STATUS_2)
                );

                // Product List Detail
                $_prod_row = tep_db_fetch_array($_prod_res_sql);

                # Delivery Mode
                $_prod_delivery_mode = TEXT_NOT_AVAILABLE;
                $_tmp = array();

                $delivery_mode = ($_prod_row['delivery_mode'] != '' ? explode(',', $_prod_row['delivery_mode']) : '');
                if (!empty($delivery_mode)) {
                    foreach ($delivery_mode as $val) {
                        if ($val != '') {
                            $_tmp[] = tep_get_products_delivery_mode_title($val);
                        }
                    }

                    $_prod_delivery_mode = (is_array($_tmp) ? implode('<br />', $_tmp) : $_tmp);
                }

                ob_start();
                ?>
                <script language="javascript">
                <!--
                    var ENTRY_SELLER = "<?php echo ENTRY_SELLER; ?>";
                var ENTRY_SELLER_USERNAME = "<?php echo ENTRY_SELLER_USERNAME; ?>";
                var ENTRY_SELLER_STATUS = "<?php echo ENTRY_SELLER_STATUS; ?>";
                var ENTRY_SELLER_GROUP = "<?php echo ENTRY_SELLER_GROUP; ?>";
                var ENTRY_SELLER_GENDER = "<?php echo ENTRY_SELLER_GENDER; ?>";
                var ENTRY_SELLER_DOB = "<?php echo ENTRY_SELLER_DOB; ?>";
                var ENTRY_SELLER_EMAIL = "<?php echo ENTRY_SELLER_EMAIL; ?>";
                var ENTRY_SELLER_TELEPHONE = "<?php echo ENTRY_SELLER_TELEPHONE; ?>";
                var ENTRY_SELLER_MOBILE_PHONE = "<?php echo ENTRY_SELLER_MOBILE_PHONE; ?>";
                var ENTRY_SELLER_IM = "<?php echo ENTRY_SELLER_IM; ?>";

                var ENTRY_DATE = "<?php echo ENTRY_DATE; ?>";
                var ENTRY_LOG_ACTION = "<?php echo ENTRY_LOG_ACTION; ?>";
                var ENTRY_REMARKS_BEFORE = "<?php echo ENTRY_REMARKS_BEFORE; ?>";
                var ENTRY_REMARKS_AFTER = "<?php echo ENTRY_REMARKS_AFTER; ?>";
                var ENTRY_ADDED_BY = "<?php echo ENTRY_ADDED_BY; ?>";
                var ENTRY_USER_ROLE = "<?php echo ENTRY_USER_ROLE; ?>";
                var TEXT_NOT_AVAILABLE = "<?php echo TEXT_NOT_AVAILABLE; ?>";
                var TEXT_ORDER_ID = "<?php echo TEXT_ORDER_ID; ?>";
                var ENTRY_PLI_RESERVED_QUANTITY = "<?php echo ENTRY_PLI_RESERVED_QUANTITY; ?>";
                //-->
                </script>
                <script language="javascript" src="<?php echo DIR_WS_INCLUDES; ?>javascript/c2c_seller_product_listing.js"></script>

                <?= tep_draw_form('menu_form', FILENAME_C2C_SELLER_PRODUCT_LISTING, 'action=add', 'post', ' onSubmit="return check_form();" id="menu_form"') ?>
                <table cellspacing="2" cellpadding="2" border="0" width="100%">
                    <tr>
                        <td>
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                    <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">&laquo; <a href="<?php echo tep_href_link(FILENAME_C2C_SELLER_PRODUCT_LISTING, 'action=search_result'); ?>">Back to Search Criteria</a></td>
                        </td>
                    <tr>
                        <td>
                            <table border="0" cellpadding="2" cellspacing="2" width="100%">
                                <tr>
                                    <td class="pageHeading" valign="top">
                                        <?php echo SUB_HEADING_SELLER_INFO; ?>&nbsp;&nbsp;
                                        <img src="images/icons/expand_arrow.gif" id="seller_detail_ico" border="0" onclick="javascript:get_seller_detail(<?php echo $_prod_row['seller_id']; ?>);" />
                                        <?php echo tep_draw_hidden_field('seller_detail_init', 0, 'id="seller_detail_init"'); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td id="seller_detail" style="display: none;"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td><?php echo tep_draw_separator(); ?></td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td>
                            <table border="0" cellpadding="2" cellspacing="2" width="100%">
                                <tr>
                                    <td class="pageHeading" valign="top" colspan="3"><?php echo SUB_HEADING_PRODUCT_LIST_INFO; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top" width="195px"><b><?php echo ENTRY_PLI_ID; ?></b></td>
                                    <td class="main" valign="top" width="5px"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['c2c_products_listing_id']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_GAME; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo tep_get_categories_name($_prod_row['game_id'], 1); ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_PRODUCT_TYPE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo tep_get_custom_product_type_name($_prod_row['custom_products_type']); ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_SERVICE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['custom_products_type_child_name']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_TITLE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo (!empty($_prod_row['products_title']) ? $_prod_row['products_title'] : TEXT_NOT_AVAILABLE ); ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_DESCRIPTION; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo (!empty($_prod_row['products_description']) ? $_prod_row['products_description'] : TEXT_NOT_AVAILABLE ); ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_STATUS; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <?php
                                        echo tep_draw_pull_down_menu('products_status', $_products_status_opt, $_prod_row['products_status'], ' id="products_status"');
                                        echo '&nbsp;&nbsp;&nbsp;';
                                        echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton');
                                        echo tep_draw_hidden_field('id', $id);
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_DISPLAY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo ($_prod_row['products_display'] == 1 ? TEXT_SHOW : TEXT_HIDE); ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_FORECAST_QUANTITY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['forecast_quantity']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_RESERVED_QUANTITY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <?php
                                        echo $reserved_quantity = $_prod_row['actual_quantity'] - $_prod_row['forecast_quantity'];
                                        if ($reserved_quantity > 0) {
                                            ?>
                                            <img src="images/icons/expand_arrow.gif" id="reserved_qty_listing_ico" border="0" onclick="javascript:get_reserved_qty_listing(<?php echo $id; ?>);" />
                                            <?php echo tep_draw_hidden_field('reserved_qty_listing_init', 0, 'id="reserved_qty_listing_init"'); ?>
                                            <?php
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr id="reserved_qty_listing" style="display: none;">
                                    <td class="main" valign="top">&nbsp;</td>
                                    <td class="main" valign="top">&nbsp;</td>
                                    <td class="main" id="reserved_qty_listing_loading"></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_AVAILABLE_QUANTITY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['available_quantity']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_ACTUAL_QUANTITY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['actual_quantity']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_PRODUCT_PRICE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['products_base_currency'] . ' ' . $_prod_row['products_price']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_PRODUCT_PRICE_NONUSD_CHECKOUT; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">USD <?php echo $_prod_row['listing_usd_price']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_PRODUCT_PRICE_USD_CHECKOUT; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">USD <?php echo $_prod_row['listing_usd_price_2']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_MINIMUM_QUANTITY; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['minimum_quantity']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_BUSINESS_HR; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" valign="top" align="left">
                                        <?php
                                        $businessHrFrom = new DateTime($_prod_row['business_hr_from'], new DateTimeZone('UTC'));
                                        $businessHrFrom->setTimezone(new DateTimeZone('Asia/Kuala_Lumpur')); // +04
                                        $businessHrTo = new DateTime($_prod_row['business_hr_to'], new DateTimeZone('UTC'));
                                        $businessHrTo->setTimezone(new DateTimeZone('Asia/Kuala_Lumpur')); // +04

                                        echo $businessHrFrom->format('H:i:s') . ' -  ' . $businessHrTo->format('H:i:s');
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_ONLINE_HR; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['online_hr'] . ' ' . TEXT_HOUR; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_OFFLINE_HR; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['offline_hr'] . ' ' . TEXT_HOUR; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_NON_BUSINESS_HR; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['non_business_hr'] . ' ' . TEXT_HOUR; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_DELIVERY_MODE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_delivery_mode; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_CREATED_DATE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['created_date']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_LAST_MODIFIED_DATE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['last_modified_date']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_EXPIRY_DATE; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left"><?php echo $_prod_row['expiry_date']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLI_GAME_ACCOUNT_INFO; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <a href="<?php echo HTTP_G2G_CREW_PORTAL . '/listing/index?ListingExtraInfo[listing_id]=' . $_prod_row["c2c_products_listing_id"]; ?>" target="_blank">Link</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="3"><img src="images/pixel_trans.gif" alt="" border="0" height="20" width="1"></td>
                                </tr>
                                <tr>
                                    <td class="pageHeading" valign="top" colspan="3"><?php echo SUB_HEADING_PRODUCT_LIST_MEDIA; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><b><?php echo ENTRY_PLM_MEDIA_URL; ?></b></td>
                                    <td class="main" valign="top"><b>:</b></td>
                                    <td class="main" align="left">
                                        <?php
                                        // Product List Media
                                        $_media_row = array();

                                        $_media_sel_sql = "SELECT value FROM " . TABLE_C2C_PRODUCTS_LISTING_EXTRA_INFO . "
                                                                WHERE c2c_products_listing_id = '" . (int) $id . "' AND `key` = 'media'";
                                        $_media_res_sql = tep_db_query($_media_sel_sql);

                                        if ($_media_row = tep_db_fetch_array($_media_res_sql)) {
                                            if (isset($_media_row['value']) && $_media_row['value'] != '') {
                                                echo '<a href="' . $_media_row['value'] . '" target="_blank">' . LINK_PREVIEW . '</a>';
                                            }
                                        } else {
                                            echo TEXT_NOT_AVAILABLE;
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="3"><img src="images/pixel_trans.gif" alt="" border="0" height="20" width="1"></td>
                                </tr>
                                <?php
                                // HLA Product Detail
                                if ($_prod_row['custom_products_type'] == 4) {
                                    $hla_data = array();
                                    $hla_sel_sql = "SELECT pla.`key`, pla.value, gad.value as name, gasd.value AS label, gal.input_type FROM " . TABLE_C2C_PRODUCTS_LISTING_ATTRIBUTES . " AS pla
                                                        LEFT JOIN " . TABLE_GAME_ATTRIBUTE_LIST . " AS gal
                                                        ON pla.`key` = gal.`key`
                                                        LEFT JOIN " . TABLE_GAME_ATTRIBUTE_DESCRIPTION . " AS gad
                                                        ON pla.value = gad.game_attribute_id AND gad.language_id = '1'
                                                        LEFT JOIN " . TABLE_GAME_ATTRIBUTE_SUPPORTED . " AS gas
                                                        ON pla.`key` = gas.`key` AND gas.game_id ='" . $_prod_row['game_id'] . "' 
                                                        LEFT JOIN " . TABLE_GAME_ATTRIBUTE_SUPPORTED_DESC . " AS gasd
                                                        ON gasd.`game_attribute_supported_id` = gas.`game_attribute_supported_id` AND gasd.language_id ='1' 
                                                        WHERE c2c_products_listing_id ='" . $_prod_row['c2c_products_listing_id'] . "'";

                                    $_res_hla_sql = tep_db_query($hla_sel_sql);
                                    while ($hla_rows = tep_db_fetch_array($_res_hla_sql)) {
                                        $hla_details_label = !empty($hla_rows['label']) ? $hla_rows['label'] : $hla_rows['key'];
                                        switch ($hla_rows['input_type']) {
                                            case "textField":
                                            case "urlTextField":
                                                $hla_details_value = $hla_rows['value'];
                                                break;
                                            default:
                                                $hla_details_value = $hla_rows['name'];
                                        }
                                        $hla_data[] = array("label" => $hla_details_label, "value" => $hla_details_value);
                                    }
                                    ?>
                                    <tr>
                                        <td class="pageHeading" valign="top" colspan="3"><?php echo SUB_HEADING_PRODUCT_LIST_HLA_INFO; ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top"><b><?php echo ENTRY_PLH_HLA_CHAR_NAME; ?></b></td>
                                        <td class="main" valign="top"><b>:</b></td>
                                        <td class="main" align="left"><?php echo isset($_prod_row['products_title']) ? $_prod_row['products_title'] : TEXT_NOT_AVAILABLE; ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top"><b><?php echo ENTRY_PLH_HLA_CHAR_DESC; ?></b></td>
                                        <td class="main" valign="top"><b>:</b></td>
                                        <td class="main" align="left"><?php echo isset($_prod_row['products_description']) ? $_prod_row['products_description'] : TEXT_NOT_AVAILABLE; ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top"><b><?php echo ENTRY_PLH_HLA_CHAR_ATTR; ?></b></td>
                                        <td class="main" valign="top"><b>:</b></td>
                                        <td class="main" valign="top" align="left">
                                            <?php if (!empty($hla_data)) { ?>
                                                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                    <?php foreach ($hla_data as $val) { ?>
                                                        <tr>
                                                            <td class="main" valign="top" width="80px" height="20px"><?php echo $val['label']; ?></td>
                                                            <td class="main" valign="top" width="10px"><b>:</b></td>
                                                            <td class="main" valign="top" align="left"><?php echo empty($val['value']) ? TEXT_NOT_AVAILABLE : $val['value']; ?></td>
                                                        </tr>
                                                    <?php } ?>
                                                </table>
                                            <?php } ?>
                                        </td>
                                    </tr>
                                    <?php
                                    // Game Item Detail
                                } else if ($_prod_row['custom_products_type'] == 5) {
                                    ?>
                                    <tr>
                                        <td class="pageHeading" valign="top" colspan="3"><?php echo SUB_HEADING_PRODUCT_LIST_GAME_ITEM_INFO; ?></td>
                                    </tr>
                                    <?php
                                    $item_data = array();
                                    $item_sel_sql = "SELECT `key`, gad.value FROM " . TABLE_C2C_PRODUCTS_LISTING_ATTRIBUTES . " AS pla
                                                        LEFT JOIN " . TABLE_GAME_ATTRIBUTE_DESCRIPTION . " AS gad
                                                        ON pla.value = gad.game_attribute_id
                                                        WHERE c2c_products_listing_id ='" . $_prod_row['c2c_products_listing_id'] . "'
                                                            AND gad.language_id = '1'";
                                    $_res_hla_sql = tep_db_query($item_sel_sql);
                                    while ($item_rows = tep_db_fetch_array($_res_hla_sql)) {
                                        $item_data[$item_rows['key']] = $item_rows['value'];
                                    }

                                    foreach ($item_data as $key => $val) {
                                        ?>
                                        <tr>
                                            <td class="main" valign="top" width="80px"><b><?php echo $key; ?></b></td>
                                            <td class="main" valign="top" width="10px"><b>:</b></td>
                                            <td class="main" valign="top" align="left"><?php echo empty($val) ? TEXT_NOT_AVAILABLE : $val; ?></td>
                                        </tr>
                                        <?php
                                    }

                                    if (count($item_data) == 0) {
                                        ?>
                                        <tr>
                                            <td class="main" colspan="2"><?php echo TEXT_NOT_AVAILABLE; ?></td>
                                        </tr>
                                        <?php
                                    }
                                }
                                ?>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td><?php echo tep_draw_separator(); ?></td>
                    </tr>
                    <tr>
                        <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                    </tr>
                    <tr>
                        <td>
                            <table border="0" cellpadding="2" cellspacing="2" width="100%">
                                <tr>
                                    <td class="pageHeading" valign="top">
                                        <?php echo SUB_HEADING_PRODUCT_LIST_REMARKS_HISTORY; ?>&nbsp;&nbsp;
                                        <img src="images/icons/expand_arrow.gif" id="product_listing_remark_ico" border="0" onclick="javascript:get_product_listing_remark(<?php echo $id; ?>);" />
                                        <?php echo tep_draw_hidden_field('product_listing_remark_init', 0, 'id="product_listing_remark_init"'); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="main" id="product_listing_remark" style="display: none;"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                </form>
                <?php
                $listing_html = ob_get_contents();
                ob_end_clean();

                return $listing_html;
            } else {
                $messageStack->add_session(ERROR_RECORD_NOT_EXIST, 'error');
            }
        } else {
            $messageStack->add_session(ERROR_RECORD_NOT_EXIST, 'error');
        }
    }

    public function addEntry($id = "") {
        global $messageStack;

        if (tep_not_null($id)) {
            $_products_status_opt = array(
                -2 => TEXT_STATUS__2,
                -1 => TEXT_STATUS__1,
                0 => TEXT_STATUS_0,
                1 => TEXT_STATUS_1,
                2 => TEXT_STATUS_2
            );

            # Remark
            $_prod_sel_sql = "SELECT products_status FROM " . TABLE_C2C_PRODUCTS_LISTING . " WHERE c2c_products_listing_id = '" . (int) $id . "'";
            $_prod_res_sql = tep_db_query($_prod_sel_sql);
            $_prod_row = tep_db_fetch_array($_prod_res_sql);

            $data = array('c2c_products_listing_id' => (int) $id,
                'log_action' => 'EDIT',
                'log_controller_action' => 'admin',
                'remarks_before_changes' => json_encode(array('products_status' => $_prod_row['products_status'])),
                'remarks_after_changes' => json_encode(array('products_status' => $_POST['products_status'])),
                'user_ip' => tep_get_ip_address(),
                'user_role' => 'admin',
                'remarks_added_date' => "now()",
                'remarks_added_by' => 'system');
            tep_db_perform(TABLE_C2C_PRODUCTS_LISTING_REMARKS_HISTORY, $data, 'insert');

            $data = array('products_status' => tep_db_prepare_input($_POST['products_status']));
            tep_db_perform(TABLE_C2C_PRODUCTS_LISTING, $data, 'update', "c2c_products_listing_id='" . (int) $id . "'");

            $messageStack->add_session(SUCCESS_PRODUCT_LIST_UPDATED, 'success');
        } else {
            $messageStack->add_session(ERROR_PRODUCT_LIST_UPDATE_FAIL, 'error');
        }
    }

    public function _get_custom_product_type() {
        $cpt_list = tep_get_product_type();

        $cpt[] = array('id' => '',
            'text' => SELECT_DEFAULT_EMPTY);

        for ($i = 0, $cnt = count($cpt_list); $cnt > $i; $i++) {
            if (in_array($cpt_list[$i]['id'], $this->c2c_cpt_list)) {
                $cpt[] = array('id' => $cpt_list[$i]['id'],
                    'text' => $cpt_list[$i]['text']);
            }
        }

        return $cpt;
    }

    public function _get_customer_detail($id) {
        $data = array();

        if (tep_not_null($id)) {
            include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOMERS_INFO_VERIFICATION);

            $_seller_dob = '';
            $_seller_im = array();

            $_seller_sel_sql = "SELECT c.customers_gender, c.customers_firstname AS firstname, c.customers_lastname AS lastname, c.customers_dob, c.customers_email_address,
									c.customers_country_dialing_code_id, c.customers_telephone, c.customers_msn, c.customers_qq, c.customers_yahoo, c.customers_icq,
									cc.username, cc.seller_status, cc.seller_group_id, csg.seller_group_name,
									ab.entry_street_address AS street_address, ab.entry_suburb AS suburb, ab.entry_postcode AS postcode, ab.entry_city AS city,
                                    ab.entry_state AS state, ab.entry_country_id AS country_id, ab.entry_zone_id AS zone_id,
									ct.address_format_id
								FROM " . TABLE_CUSTOMERS . " AS c
								LEFT JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
									ON cc.customers_id = c.customers_id
								LEFT JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
									ON csg.seller_group_id = cc.seller_group_id
								LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
									ON ab.address_book_id = c.customers_default_address_id
								LEFT JOIN " . TABLE_COUNTRIES . " AS ct
									ON ct.countries_id = ab.entry_country_id
								WHERE c.customers_id = '" . $id . "'";
            $_seller_res_sql = tep_db_query($_seller_sel_sql);
            $_seller_row = tep_db_fetch_array($_seller_res_sql);

            # Address
            $data['seller_address'] = tep_address_format($_seller_row['address_format_id'], $_seller_row, 1, '', '<br />', '', false);

            # IM
            $im_select_sql = "	SELECT imt.instant_message_type_name, ima.instant_message_userid, ima.instant_message_type_id, ima.instant_message_remarks
							FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " AS ima
							LEFT JOIN " . TABLE_INSTANT_MESSAGE_TYPE . " AS imt
								ON (ima.instant_message_type_id = imt.instant_message_type_id)
							WHERE ima.customer_id = '" . $id . "'
								AND ima.instant_message_userid <> ''";

            $im_result_sql = tep_db_query($im_select_sql);
            while ($im_row = tep_db_fetch_array($im_result_sql)) {
                if ($im_row['instant_message_type_id'] != 0) {
                    $_seller_im[] = $im_row['instant_message_type_name'] . ": " . $im_row['instant_message_userid'];
                } else {
                    $_seller_im[] = $im_row['instant_message_remarks'] . ": " . $im_row['instant_message_userid'];
                }
            }

//            (isset($_seller_row['customers_msn']) && !empty($_seller_row['customers_msn'])) ? $_seller_im[] = $_seller_row['customers_msn'] : '';
//            (isset($_seller_row['customers_qq']) && !empty($_seller_row['customers_qq'])) ? $_seller_im[] = $_seller_row['customers_qq'] : '';
//            (isset($_seller_row['customers_yahoo']) && !empty($_seller_row['customers_yahoo'])) ? $_seller_im[] = $_seller_row['customers_yahoo'] : '';
//            (isset($_seller_row['customers_icq']) && !empty($_seller_row['customers_icq'])) ? $_seller_im[] = $_seller_row['customers_icq'] : '';
            $data['seller_im'] = (tep_not_null($_seller_im) ? implode('<br />', $_seller_im) : TEXT_NOT_AVAILABLE);

            # DOB
            if (isset($_seller_row['customers_dob']) && !empty($_seller_row['customers_dob'])) {
                $dob_style = '';
                $_seller_age = tep_calculate_age($_seller_row['customers_dob'], '', 0);

                if (preg_match('/([\d ]+)(yr)/is', $_seller_age, $regs)) {
                    if ((int) trim($regs[1]) < 17) {
                        $dob_style = ' class="redIndicator" ';
                    }
                } else {
                    $dob_style = ' class="redIndicator" ';
                }

                $_seller_dob = '<span ' . $dob_style . '>' . tep_date_short($_seller_row['customers_dob'], PREFERRED_DATE_FORMAT) . ' (' . $_seller_age . ') </span>';
            }

            # Telephone
            $dialing_code = tep_format_telephone($id);
            $_seller_telephone = $dialing_code['country_international_dialing_code'] . ' ' . tep_parse_telephone($_seller_row['customers_telephone'], $_seller_row['customers_country_dialing_code_id'], 'id');
            $_seller_telephone = ($_seller_telephone != ' ' ? '+' . $_seller_telephone : TEXT_NOT_AVAILABLE);

            $data['seller_name'] = (tep_not_null($_seller_row['firstname']) && tep_not_null($_seller_row['lastname']) ? '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $id . '&action=edit') . '" target="_blank">' . $_seller_row['firstname'] . ' ' . $_seller_row['lastname'] . '</a><br />' : TEXT_NOT_AVAILABLE);
            $data['seller_username'] = tep_not_null($_seller_row['username']) ? $_seller_row['username'] : TEXT_NOT_AVAILABLE;
            $data['seller_status'] = tep_not_null($_seller_row['seller_status']) ? ($_seller_row['seller_status'] == 1 ? ACTIVE : NOT_ACTIVE) : TEXT_NOT_AVAILABLE;
            $data['seller_group_name'] = tep_not_null($_seller_row['seller_group_name']) ? $_seller_row['seller_group_name'] : TEXT_NOT_AVAILABLE;
            $data['customers_gender'] = ($_seller_row['customers_gender'] == 'm' ? TEXT_MALE : ($_seller_row['customers_gender'] == 'f' ? TEXT_FEMALE : TEXT_NOT_AVAILABLE));
            $data['seller_dob'] = tep_not_null($_seller_dob) ? $_seller_dob : TEXT_NOT_AVAILABLE;
            $data['customers_email_address'] = tep_not_null($_seller_row['customers_email_address']) ? $_seller_row['customers_email_address'] : TEXT_NOT_AVAILABLE;
            $data['seller_telephone'] = $_seller_telephone;
            $data['customers_mobile'] = (tep_not_null($_seller_row['customers_mobile']) ? $_seller_row['customers_mobile'] : TEXT_NOT_AVAILABLE);
        }

        return $data;
    }

    public function _get_product_listing_remark($id) {
        $data = array();

        if (tep_not_null($id)) {
            $_remarks_sel_sql = "	SELECT c2c_products_listing_id, log_action, log_controller_action, remarks_before_changes, remarks_after_changes, remarks_added_date, remarks_added_by, user_role
									FROM " . TABLE_C2C_PRODUCTS_LISTING_REMARKS_HISTORY . "
									WHERE c2c_products_listing_id = '" . (int) $id . "'
									ORDER BY remarks_added_date DESC";
            $_remarks_res_sql = tep_db_query($_remarks_sel_sql);
            if (tep_db_num_rows($_remarks_res_sql) > 0) {
                while ($_remarks_row = tep_db_fetch_array($_remarks_res_sql)) {
                    $_tmp['tr_class'] = (preg_match('/system/i', $_remarks_row['remarks_added_by']) ? 'orderCommentSystem' : 'orderCommentDelivery');
                    $_tmp['remarks_added_date'] = $_remarks_row['remarks_added_date'];
                    $_tmp['log_action'] = $_remarks_row['log_action'] . "<br />" . $_remarks_row['log_controller_action'];
                    $_tmp['user_role'] = $_remarks_row['user_role'];
                    $_tmp['remarks_before_changes'] = json_decode(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $_remarks_row['remarks_before_changes']), true);
                    $_tmp['remarks_after_changes'] = json_decode(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $_remarks_row['remarks_after_changes']), true);
                    $_tmp['remarks_added_by'] = $_remarks_row['remarks_added_by'];

                    $data[] = $_tmp;
                }
            }
        }

        return $data;
    }

    public function _get_reserved_qty_listing($id) {
        $data = array();

        if (tep_not_null($id)) {
            $id = (int) $id;
            $_prod_sel_sql = "SELECT products_id FROM " . TABLE_C2C_PRODUCTS_LISTING . " WHERE c2c_products_listing_id = '" . $id . "'";
            $_prod_res_sql = tep_db_query($_prod_sel_sql);
            $_prod_row = tep_db_fetch_array($_prod_res_sql);
            if (tep_not_null($_prod_row)) {
//                $c2c_config_sel_sql = "	SELECT configuration_value FROM " . TABLE_C2C_CONFIGURATION . "
//                                                WHERE configuration_key = 'C2C_MAX_PENDING_WITHIN_MIN'";
//                $c2c_config_res_sql = tep_db_query($c2c_config_sel_sql);
//                $_c2c_config_row = tep_db_fetch_array($c2c_config_res_sql);
//                $time = time() - ($_c2c_config_row['configuration_value'] * 60);

                $products_id = $_prod_row['products_id'];
                $time = time() - (2 * 24 * 60 * 60); // 2 days
                $dbTime = date("Y-m-d H:i:s", $time);

                $_listing_sel_sql = "SELECT o.orders_id, op.products_quantity, ope.orders_products_extra_info_value  
                                            FROM  " . TABLE_ORDERS . " AS o 
                                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                            ON op.orders_id = o.orders_id 
                                            INNER JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS ope 
                                            ON ope.orders_products_id = op.orders_products_id 
                                            WHERE o.orders_status = 1  
                                            AND o.date_purchased >= '" . $dbTime . "'
                                            AND op.products_id = '" . $products_id . "' 
                                            AND ope.orders_products_extra_info_key = 'listing' 
                                            ORDER BY orders_id DESC";
                $_listing_res_sql = tep_db_query($_listing_sel_sql);

                while ($_listing_row = tep_db_fetch_array($_listing_res_sql)) {
                    $orders_products_extra_info_value = tep_array_unserialize($_listing_row['orders_products_extra_info_value']);
                    $c2c_products_listing_id = isset($orders_products_extra_info_value['c2c_products_listing_id']) ? $orders_products_extra_info_value['c2c_products_listing_id'] : '';

                    if ($c2c_products_listing_id == $id) {
                        $_tmp['orders_id'] = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $_listing_row['orders_id'] . '&action=edit') . '" target="_blank">' . $_listing_row['orders_id'] . '</a>';
                        $_tmp['products_quantity'] = $_listing_row['products_quantity'];

                        $data[] = $_tmp;
                    }
                }

                return $data;
            }
        }
    }

}