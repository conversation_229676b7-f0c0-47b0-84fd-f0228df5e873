<?php

class categories_discount_rule {

    public $identity, $identity_email, $rule_info;

    public function __construct($identity, $identity_email) {
        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin email
    }

    public function show_rules_list($filename) {
        $sort = 'ASC';
        $sortingDB = 'ASC';
        
        //Sorting
        if (isset($_GET['sort']) AND $_GET['sort'] == 'ASC') {
            $sort = 'DESC';
            $sortingDB = 'ASC';
        }

        if (isset($_GET['sort']) AND $_GET['sort'] == 'DESC') {
            $sort = 'ASC';
            $sortingDB = 'DESC';
        }
        
        if (isset($_REQUEST['page'])) {
            $target = $_SERVER[ 'REQUEST_URI' ];
            $target = preg_replace('/&sort=ASC/',"",$target);
            $target = preg_replace('/&sort=DESC/',"",$target);
            $sorting = "$target&sort=$sort";
        } else {
            $target = $_SERVER[ 'REQUEST_URI' ];
            $target = preg_replace('/\?sort=ASC/',"",$target);
            $target = preg_replace('/\?sort=DESC/',"",$target);
            $sorting = "$target?sort=$sort"; 
        }
        
        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
        $rule_select_sql = "SELECT ofsr.cdrules_id, ofsr.cdrules_title, ofsr.cdrules_date_added, ofsr.cdrules_last_modified
                            FROM " . TABLE_CATEGORY_DISCOUNT_RULES . " AS ofsr 
                            ORDER BY ofsr.cdrules_title " . $sortingDB. " ";

        $show_records = MAX_DISPLAY_SEARCH_RESULTS;
        if ($show_records != "ALL") {
            $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $rule_select_sql, $rule_select_sql_numrows, true);
        }
        $rule_result_sql = tep_db_query($rule_select_sql);

        ob_start();
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr align="right">
                <td>[ <a href="<?php echo tep_href_link($filename, 'action=show_monitor_page') ?>"><?php echo LINK_SHOW_MONITOR_PAGE; ?></a> ] [ <a href="<?php echo tep_href_link($filename, 'action=new_rule') ?>"><?php echo LINK_ADD_CATEGORY_DISCOUNT_RULE; ?></a> ]</td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr>
                            <td width="7%" class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_ID; ?></td>
                            <td class="reportBoxHeading"><a href="<?php echo $sorting ; ?>"><b><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_NAME; ?></b></a></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_SETTING; ?></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_DATE_ADDED; ?></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_ACTION; ?></td>
                        </tr>
                        <?php
                        $row_count = 0;
						$cat_name =  array();
                        while ($rule_row = tep_db_fetch_array($rule_result_sql)) {
                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
							$action_str = ' <a href="' . tep_href_link($filename, 'action=edit_rule&id=' . $rule_row["cdrules_id"]) . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') . '</a>
                                            <a href="javascript:void(confirm_delete(\'' . $rule_row['cdrules_title'] . '\', \'\', \'' . tep_href_link($filename, 'action=delete_rule&rules_id=' . $rule_row["cdrules_id"]) . '\'))">' . tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') . '</a>
                                            <a href="' . tep_href_link($filename, 'action=show_monitor_page&rules_id=' . $rule_row["cdrules_id"]) . '">' . LINK_MONITOR_PAGE_BY_RULE_ID . '</a>';

                            echo '	<tr height="20" class="dataTableRow" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                            <td>' . $rule_row['cdrules_id'] . '</td>
                                            <td valign="top"  style="width:15%">' . $rule_row['cdrules_title'] . '</td>
                                            <td valign="top" style="width:25%" id="rule_row_' . $rule_row["cdrules_id"] . '">' . '<a href="javascript:void(0);" onClick="show_category_discount_rules_by_id(' . $rule_row['cdrules_id'] . ', \'Show setting\', \'Hide setting\');"><span id="toggle_discount_setting_' . $rule_row['cdrules_id'] . '">Show setting</span></a><span id="customer_group_discount_info_' . $rule_row["cdrules_id"] . '"></span></td>';
							
							echo ' 		<td align="center"  style="width:10%" valign="top" >' . $rule_row['cdrules_date_added'] . '</td>
                                            <td align="center"  style="width:10%" valign="top" >' . $action_str . '</td>
                                        </tr>';
                            ?>

                            <?php
                            $row_count++;
							unset($cat_name);
                        }
						
                        ?>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                        <tr>
                            <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($rule_result_sql) > 0 ? "1" : "0", tep_db_num_rows($rule_result_sql), tep_db_num_rows($rule_result_sql)) : $rule_split_object->display_count($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                            <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?php
        $report_section_html = ob_get_contents();
        ob_end_clean();

        return $report_section_html;
    }

    public function add_rule($filename) {
        $report_section_html = '';

        ob_start();
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr>
                <td>
                    <?php echo tep_draw_form('add_rules_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=insert_rule', 'post', '') ?>
                    <table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">

                        <tr>
                            <td class="main" valign="top"><?php echo ENTRY_CATEGORY_DISCOUNT_RULE_NAME; ?></td>
                            <td class="main"><?php echo tep_draw_input_field('rules_name', '', 'size="40" id="rules_name"'); ?></td>
                        </tr>

                         <tr>
                            <td valign="top" class="main">&nbsp;</td>
                            <td>
                                <table width="60%" cellspacing="1" cellpadding="0" border="0">
                                    <tbody><tr>
                                            <td width="30%" class="commonBoxHeading">&nbsp;</td>
                                            <td class="commonBoxHeading" colspan="2">
                                                <table cellspacing="0" cellpadding="2" border="0">
                                                    <tbody><tr>

                                                            <td class="commonBoxHeading" ><?php echo TABLE_HEADING_AUTO_CALCULATE; ?></td>
                                                            <td class="commonBoxHeading" >&nbsp;</td>
                                                        </tr>
                                                    </tbody></table>
                                            </td>
                                        </tr>

                                        <?php
                                        $row_count = 0;

                                        $customers_groups_qty_limit_query = "	SELECT cg.customers_groups_id, cg.customers_groups_name
                                                                                FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                                                                ORDER BY cg.sort_order, cg.customers_groups_name";
                                        $customers_groups_qty_limit_query = tep_db_query($customers_groups_qty_limit_query);
                                        $count_result_qty_limit = tep_db_num_rows($customers_groups_qty_limit_query);
                                        while ($customers_groups = tep_db_fetch_array($customers_groups_qty_limit_query)) {
                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                            $customers_groups_id = $customers_groups['customers_groups_id'];
                                            $out_of_stock_in_percentage = isset($customers_groups['out_of_stock_in_percentage']) ? number_format($customers_groups['out_of_stock_in_percentage'], 2, '.', '') : '0';
                                            ?>

                                            <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">


                                                <td class="main"  align="center">
                                                    <?php
                                                    echo $customers_groups['customers_groups_name'];
                                                    ?>
                                                </td>
                                                <td> 
                                                    Discount: 
													<select name="<?php echo 'customer_discount_wor[' . $customers_groups_id . '][category_discount_sign]'; ?>">
														<option name="minus" value="-" selected="selected" >-</option>
														<option name="plus" value="+" >+</option>
													</select>
													
													<?php echo tep_draw_input_field('customer_discount_wor[' . $customers_groups_id . '][discount]', $out_of_stock_in_percentage, 'size="8" id="customer_discount_' . $customers_groups_id . '" '); ?>
													OP: <?php echo tep_draw_input_field('customer_discount_wor[' . $customers_groups_id . '][wor]', $out_of_stock_in_percentage, 'size="8" id="customer_wor_' . $customers_groups_id . '" '); ?>
                                                </td>


                                            </tr>
                                            <?php
                                            $row_count++;
                                        }
                                        ?>


                                    </tbody></table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="2">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="middle">
                                            <?php echo tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, 'onClick="return form_checking();"', 'inputButton') ?>
                                            <?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton') ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

        </table>


        <script language="javascript">
            function form_checking() {
                var rules_name = document.getElementById('rules_name').value;
                if (rules_name.length > 0) {
                    return true;
                } else {
                    alert('<?php echo ERROR_CATEGORY_DISCOUNT_MISSING_RULE_NAME; ?>');
                    document.getElementById('rules_name').focus();
                    document.getElementById('rules_name').select();
                    return false;
                }


            }

        </script>

        <?php
        $report_section_html = ob_get_contents();
        ob_end_clean();

        return $report_section_html;
    }

    public function edit_rule($filename, $id) {
        $report_section_html = '';
        $rule_select_sql = "SELECT ofsr.cdrules_id, ofsr.cdrules_title
                            FROM " . TABLE_CATEGORY_DISCOUNT_RULES . " AS ofsr 
                            WHERE ofsr.cdrules_id = '" . tep_db_input($id) . "' ";
        $rule_result_sql = tep_db_query($rule_select_sql);

        if ($rule_row = tep_db_fetch_array($rule_result_sql)) {

            $rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.cdgr_discount, ofsrcg.cdgr_wor
                                        FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                        LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.cdgr_customer_group_id
                                        AND ofsrcg.cdrules_id = '" . tep_db_input($id) . "'
                                        ORDER BY cg.sort_order, cg.customers_groups_name";
            $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);

            ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr>
                    <td>
                        <?php echo tep_draw_form('edit_rules_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=update_rule', 'post', '') ?>
                        <table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">

                            <tr>
                                <td class="main" valign="top"><?php echo ENTRY_CATEGORY_DISCOUNT_RULE_NAME; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('rules_name', $rule_row['cdrules_title'], 'size="40" id="rules_name"'); ?></td>
                            </tr>

                            <tr>
                                <td valign="top" class="main">&nbsp;</td>
                                <td>
                                    <table width="60%" cellspacing="1" cellpadding="0" border="0">
                                        <tbody><tr>
                                                <td width="30%" class="commonBoxHeading">&nbsp;</td>
                                                <td class="commonBoxHeading" colspan="2">
                                                    <table cellspacing="0" cellpadding="2" border="0">
                                                        <tbody><tr>

                                                                <td class="commonBoxHeading" ><?php echo TABLE_HEADING_AUTO_CALCULATE; ?></td>
                                                                <td class="commonBoxHeading" >&nbsp;</td>
                                                            </tr>
                                                        </tbody></table>
                                                </td>

                                            </tr>

                                            <?php
                                            $row_count = 0;

                                            while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                $customers_groups_id = $customers_groups['customers_groups_id'];
                                                $cdgr_discount = isset($customers_groups['cdgr_discount']) ? number_format($customers_groups['cdgr_discount'], 2, '.', '') : '0';
                                                $discountSign = ($cdgr_discount[0] == '-') ? '-' : '';
                                                $cdgr_wor = $customers_groups['cdgr_wor'];
                                                $cdgr_discount = substr($cdgr_discount, 1);
                                                ?>

                                                <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                                                    <td class="main"  align="center">
                                                        <?php
                                                        echo $customers_groups['customers_groups_name'];
                                                        ?>
                                                    </td>
													<td> 
                                                    Discount:  
													<select name="<?php echo 'customer_discount_wor[' . $customers_groups_id . '][category_discount_sign]'; ?>">
														<option name="minus" value="-" <? if ($discountSign == '-') echo 'selected="selected"' ?>>-</option>
                                                        <option name="plus" value="+"  <? if ($discountSign == '' AND $cdgr_discount != '0.00') { echo 'selected="selected"'; }?>>+</option>
													</select>
                                                    <?php echo tep_draw_input_field('customer_discount_wor[' . $customers_groups_id . '][discount]', $cdgr_discount, 'size="8" id="customer_discount_' . $customers_groups_id . '" '); ?>
                                                    OP: <?php echo tep_draw_input_field('customer_discount_wor[' . $customers_groups_id . '][wor]', $cdgr_wor, 'size="8" id="customer_wor_' . $customers_groups_id . '" '); ?>
													</td>
                                                </tr>
                                                <?php
                                                $row_count++;
                                            }
                                            ?>

                                        </tbody></table>
                                </td>
                            </tr>


                            <tr>
                                <td colspan="2">
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="middle">
                                                <?php echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_INSERT, 'onClick="return form_checking();"', 'inputButton') ?>
                                                <?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton') ?>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </table>

            <script language="javascript">
                function form_checking() {
                    var rules_name = document.getElementById('rules_name').value;
                    if (rules_name.length > 0) {
                        return true;
                    } else {
                        alert('<?php echo ERROR_CATEGORY_DISCOUNT_MISSING_RULE_NAME; ?>');

                        return false;
                    }


                }

            </script>


            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();
        }
        return $report_section_html;
    }

    public function insert_rule($input_array, &$messageStack) {
        $error = false;
        $rule_name = tep_db_prepare_input($input_array['rules_name']);
        $option_array = $input_array['customer_discount_wor'];

        if (!tep_not_null($rule_name)) {
            $error = true;
            $messageStack->add_session(ERROR_CATEGORY_DISCOUNT_MISSING_RULE_NAME, 'error');
        }

        if (!$error) {
            $rules_data_array = array('cdrules_title' => $rule_name,
                'cdrules_date_added' => 'now()',
                'cdrules_last_modified' => 'now()',
            );
            tep_db_perform(TABLE_CATEGORY_DISCOUNT_RULES, $rules_data_array);

            $cdrules_id = tep_db_insert_id();

           foreach ($option_array as $customer_group_id => $dataVal) {
				$discountRate = $dataVal['discount'];
				$worToken = $dataVal['wor'];
				$category_discount_sign = $dataVal['category_discount_sign'];
				if($category_discount_sign == '-'){
					$discountSign = '-';
				}else{
					$discountSign = '';
				}
			   
				if ($customer_group_id && $cdrules_id) {
					$data_array = array(
						'cdgr_customer_group_id' => (int) $customer_group_id,
						'cdrules_id' => $cdrules_id,
						'cdgr_discount' => $discountSign.''.$discountRate,
						'cdgr_wor' => $worToken,
						'cdgr_updated_datetime' => 'now()',
					);
					tep_db_perform(TABLE_CATEGORY_DISCOUNT_GROUP_RULES, $data_array);
				}
            }
            unset($data_array);
        }

        if (!$error) {
            $messageStack->add_session(MESSAGE_INSERT_SUCCESS, 'success');
            return true;
        } else {
            $messageStack->add_session(MESSAGE_INSERT_ERROR, 'error');
            return false;
        }
    }

    public function update_rule($input_array, &$messageStack) {
        global $memcache_obj;
        
        $error = false;
        $rule_id = tep_db_prepare_input($input_array['id']);
        $rule_name = tep_db_prepare_input($input_array['rules_name']);
        $option_array = $input_array['customer_discount_wor'];

        if (!tep_not_null($rule_name)) {
            $error = true;
            $messageStack->add_session(ERROR_CATEGORY_DISCOUNT_MISSING_RULE_NAME, 'error');
        }

        if (!$error) {
            $exist_rule_name = '';

            $check_exist_rule_select_sql = "	SELECT cdrules_title
                                                FROM " . TABLE_CATEGORY_DISCOUNT_RULES . " 
                                                WHERE   cdrules_id = '" . $rule_id . "'";
            $check_exist_rule_result_sql = tep_db_query($check_exist_rule_select_sql);
            if ($check_exist_rule_result_row = tep_db_fetch_array($check_exist_rule_result_sql)) {
                $exist_rule_name = $check_exist_rule_result_row['cdrules_title'];
            }

            if ($exist_rule_name !== $rule_name) {
                $rule_data_array = array(
                    'cdrules_title' => $rule_name,
                    'cdrules_last_modified' => 'now()',
                );
                tep_db_perform(TABLE_CATEGORY_DISCOUNT_RULES, $rule_data_array, 'update', 'cdrules_id="' . tep_db_input($rule_id) . '"');
            }

            $check_exist_options_result_row_array = array();

            $check_exist_options_select_sql = "SELECT cdgr_customer_group_id, cdgr_discount, cdgr_wor
                                                FROM " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " 
                                                WHERE   cdrules_id = '" . $rule_id . "'";
            $check_exist_options_result_sql = tep_db_query($check_exist_options_select_sql);
            while ($check_exist_options_result_row = tep_db_fetch_array($check_exist_options_result_sql)) {
                $check_exist_options_result_row_array[$check_exist_options_result_row['cdgr_customer_group_id']] = $check_exist_options_result_row['cdgr_discount'];
            }

            if (is_array($option_array)) {
                foreach ($option_array as $customer_group_id => $dataVal) {
					$discountRate = $dataVal['discount'];
					$worToken = $dataVal['wor'];
					$category_discount_sign = $dataVal['category_discount_sign'];
					if($category_discount_sign == '-'){
						$discountSign = '-';
					}else{
						$discountSign = '';
					}
					
					if ($customer_group_id && $rule_id) {
						$insert_new = false;

						if (!isset($check_exist_options_result_row_array[$customer_group_id])) {
							$data_array = array(
								'cdgr_customer_group_id' => (int) $customer_group_id,
								'cdrules_id' => $rule_id,
								'cdgr_discount' => $discountSign.''.$discountRate,
								'cdgr_wor' => $worToken,
								'cdgr_updated_datetime' => 'now()',
							);
							tep_db_perform(TABLE_CATEGORY_DISCOUNT_GROUP_RULES, $data_array);
						} else {
							$rule_option_update_array = array(
								'cdgr_discount' => $discountSign.''.$discountRate,
								'cdgr_wor' => $worToken,
								'cdgr_updated_datetime' => 'now()',
							);
							tep_db_perform(TABLE_CATEGORY_DISCOUNT_GROUP_RULES, $rule_option_update_array, 'update', 'cdrules_id="' . tep_db_input($rule_id) . '" AND cdgr_customer_group_id="' . (int) $customer_group_id . '"');
						
                            //Delete memcache
                            $customer_grp_select_sql = "SELECT customers_groups_id, customers_groups_name
                                                        FROM " . TABLE_CUSTOMERS_GROUPS . "";
                            $customer_grp_result_sql = tep_db_query($customer_grp_select_sql);
                            while ($customer_grp_row = tep_db_fetch_array($customer_grp_result_sql)) {
                                //categories_discount_group_rules/rule_id/xxx/customers_groups_id/xxx
                                $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . '/rule_id/' . $rule_id . '/customers_groups_id/' . $customer_grp_row['customers_groups_id'], 0);
                            }
                        }
						
						# Clear cache
						#key:categories_discount_list/rules_id/xxx
						// $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . '/rules_id/' . $rules_id, 0);
					}
                }
            }
        }

        if (!$error) {
            $messageStack->add_session(MESSAGE_UPDATE_SUCCESS, 'success');
            return $rule_id;
        } else {
            $messageStack->add_session(MESSAGE_UPDATE_ERROR, 'error');
            return false;
        }
    }

    public function delete_rule($input_array, &$messageStack) {
        global $memcache_obj;

        $rules_id = tep_db_prepare_input($input_array['rules_id']);
		
		// Remove the Category Rules
        $rules_delete_sql = "DELETE FROM " . TABLE_CATEGORY_DISCOUNT_RULES . " WHERE cdrules_id = '" . tep_db_input($rules_id) . "'";
        tep_db_query($rules_delete_sql);
		
		$customergroup_delete_sql = "DELETE FROM " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " WHERE cdrules_id = '" . tep_db_input($rules_id) . "'";
        tep_db_query($customergroup_delete_sql);
		
		$categorylist_delete_sql = "DELETE FROM " . TABLE_CATEGORY_DISCOUNT_LIST . " WHERE cdrules_id = '" . tep_db_input($rules_id) . "'";
        tep_db_query($categorylist_delete_sql);
		
		//Delete memcache
        $customer_grp_select_sql = "SELECT customers_groups_id, customers_groups_name
                                    FROM " . TABLE_CUSTOMERS_GROUPS . "";
        $customer_grp_result_sql = tep_db_query($customer_grp_select_sql);
        while ($customer_grp_row = tep_db_fetch_array($customer_grp_result_sql)) {
            //categories_discount_group_rules/rule_id/xxx/customers_groups_id/xxx
            $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . '/rule_id/' . $rules_id . '/customers_groups_id/' . $customer_grp_row['customers_groups_id'], 0);
        }
		
        $messageStack->add_session(MESSAGE_DELETE_SUCCESS, 'success');
        return true;
    }
	
	public function remove_category($input_array, &$messageStack) {
        global $memcache_obj;

        $rules_id = tep_db_prepare_input($input_array['rules_id']);
        $category_id = tep_db_prepare_input($input_array['category_id']);
		
		// Remove the Category From Rules
		$categorylist_delete_sql = "DELETE FROM " . TABLE_CATEGORY_DISCOUNT_LIST . " WHERE cdrules_id = '" . tep_db_input($rules_id) . "' AND cdl_category_id='".$category_id ."'";
        tep_db_query($categorylist_delete_sql);
		
		//Delete memcache
        $customer_grp_select_sql = "SELECT customers_groups_id, customers_groups_name
                                    FROM " . TABLE_CUSTOMERS_GROUPS . "";
        $customer_grp_result_sql = tep_db_query($customer_grp_select_sql);
        while ($customer_grp_row = tep_db_fetch_array($customer_grp_result_sql)) {
            //categories_discount_group_rules/rule_id/xxx/customers_groups_id/xxx
            $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . '/rule_id/' . $rules_id . '/customers_groups_id/' . $customer_grp_row['customers_groups_id'], 0);
        }
		
        $messageStack->add_session(MESSAGE_DELETE_SUCCESS, 'success');
        return true;
    }

    public function get_category_discount_rules_by_id($id) {
		$rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.cdgr_discount, ofsrcg.cdgr_wor
                                        FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                        LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.cdgr_customer_group_id
                                        AND ofsrcg.cdrules_id = '" . tep_db_input($id) . "'
                                        ORDER BY cg.sort_order, cg.customers_groups_name";

        $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);

        while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
            $customers_groups_id = $customers_groups['customers_groups_id'];
            $cdgr_discount = isset($customers_groups['cdgr_discount']) ? number_format($customers_groups['cdgr_discount'], 2, '.', '') : '0';
            $cdgr_wor = $customers_groups['cdgr_wor'];
			ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">

                <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                    <td class="main"  align="center">
                        <?php
                        echo $customers_groups['customers_groups_name'];
                        ?>
                    </td>
					<td> 
						Discount: <?php echo $cdgr_discount; ?>
						OP: <?php echo $cdgr_wor; ?>
					</td>
                </tr>
                <?php
                $row_count++;
            }

            $report_section_html = ob_get_contents();
            ob_end_clean();
            return $report_section_html;
       }

        public function get_rules_name($rules_id) {
            if (!isset($this->rule_info[$rules_id]['cdrules_title'])) {
                $rule_name_sql = "SELECT cdrules_title FROM " . TABLE_CATEGORY_DISCOUNT_RULES . " WHERE cdrules_id = '" . tep_db_input($rules_id) . "'";
                $rule_name_result = tep_db_query($rule_name_sql);
                if ($rule_name_row = tep_db_fetch_array($rule_name_result)) {
                    $this->rule_info[$rules_id]['cdrules_title'] = $rule_name_row['cdrules_title'];
                }
            }

            return isset($this->rule_info[$rules_id]['cdrules_title']) ? $this->rule_info[$rules_id]['cdrules_title'] : '';
        }

        public function show_monitor_page($filename) {
            $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
			$searchFilter = FALSE;
			$filter_category_id = tep_db_prepare_input($_POST['category_id']);
            $sort = 'ASC';
            $sortingDB = 'ASC';
			
			if(isset($_GET['rules_id']) ){
				$rules_id = $_GET['rules_id'];
				$actionRules = "WHERE cdgr.cdrules_id='".$rules_id."' ";
			}else{
				$actionRules = "";
			}
			
			// Search by category id
			if(isset($filter_category_id) AND isset($_GET['rules_id'])){
				$searchFilter = TRUE;
				$queryFilter = "AND cdl_category_id='".$filter_category_id."' ";
				
			}elseif(isset($filter_category_id) AND !isset($_GET['rules_id'])){
				$searchFilter = TRUE;
				$queryFilter = "WHERE cdl_category_id='".$filter_category_id."' ";
			
			}else{
				$searchFilter = FALSE;
				$queryFilter = "";
			}
            
            //Sorting
            if (isset($_GET['sort']) AND $_GET['sort'] == 'ASC') {
                $sort = 'DESC';
                $sortingDB = 'ASC';
            }
            
            if (isset($_GET['sort']) AND $_GET['sort'] == 'DESC') {
                $sort = 'ASC';
                $sortingDB = 'DESC';
            }
            
            $target = $_SERVER[ 'REQUEST_URI' ];
            $target = preg_replace('/&sort=ASC/',"",$target);
            $target = preg_replace('/&sort=DESC/',"",$target);
            $sorting = "$target&sort=$sort"; 
								
								if($searchFilter === TRUE){
									$grp_discount_select_sql = "SELECT * 
																				FROM " . TABLE_CATEGORY_DISCOUNT_LIST . " AS cdl
																				LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS cdgr
																				ON cdl.cdrules_id = cdgr.cdrules_id
																				LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_RULES . " AS cdr
																				ON cdl.cdrules_id = cdr.cdrules_id
																				LEFT JOIN ".TABLE_CUSTOMERS_GROUPS." AS cg
																				ON cdgr.cdgr_customer_group_id = cg.customers_groups_id
																				LEFT JOIN " . TABLE_CATEGORIES . " AS c
																				ON cdl.cdl_category_id=c.categories_id
																				LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
																				ON c.categories_id=cd.categories_id
																				".$actionRules."
																				".$queryFilter."
																				GROUP BY cdl.cdl_category_id
																				ORDER BY cdr.cdrules_title ".$sortingDB." ";
								
								 }else{
									 $grp_discount_select_sql =  "SELECT * 
																				FROM " . TABLE_CATEGORY_DISCOUNT_LIST . " AS cdl
																				LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS cdgr
																				ON cdl.cdrules_id = cdgr.cdrules_id
																				LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_RULES . " AS cdr
																				ON cdl.cdrules_id = cdr.cdrules_id
																				LEFT JOIN ".TABLE_CUSTOMERS_GROUPS." AS cg
																				ON cdgr.cdgr_customer_group_id = cg.customers_groups_id
																				LEFT JOIN " . TABLE_CATEGORIES . " AS c
																				ON cdl.cdl_category_id=c.categories_id
																				LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
																				ON c.categories_id=cd.categories_id
																				".$actionRules."
																				GROUP BY cdl.cdl_category_id
																				ORDER BY cdr.cdrules_title ".$sortingDB." ";

								 }
			
			$show_records = MAX_DISPLAY_SEARCH_RESULTS;
								 
			if ($show_records != "ALL") {
                $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $grp_discount_select_sql, $grp_discount_select_sql_numrows, true);
				
            }
			
			$grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
			
            ob_start();
            ?>
			<!-- Filter block -->
			<?php echo tep_draw_form('filter_category', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_monitor_page', 'post', ''); ?>			
			<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">
				<tbody>
					<tr>
						<td class="main" valign="top" width="12%">Filter by Category ID:</td>
						<td class="main" width="10%"><input type="text" name="category_id" size="20" id="category_id" required></td>
						<td><input type="SUBMIT" value="Filter" class="inputButton" title=" Insert " onmouseover="this.className='inputButtonOver'" onmouseout="this.className='inputButton'"></td>
					 </tr>
				 </tbody>
				 </table>
			</form>
			<!-- End Filter block -->
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr align="right">
                    <td><?php echo tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, ''), '', 'inputButton') ?></td>
                </tr>

                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                            <tr>
                                <td width="10%" class="reportBoxHeading" align="center"><a href="<?php echo $sorting ; ?>"><b><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_NAME; ?></b></a></td>
                                <td width="10%" class="reportBoxHeading" align="center"><?php echo CATEGORY_ID; ?></td>
                                <td width="10%" class="reportBoxHeading" align="center"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_RULE_ACTION; ?></td>
                                <td width="40%" class="reportBoxHeading"><?php echo ENTRY_CATEGORY; ?></td>
                                <td class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_DISCOUNT_WOR; ?></td>
                            </tr>
                            <?php
							 $row_count = 0;
                            while ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
								$row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
								$cust_grp_array = array();
									$cust_grp_discount_select_sql =  "SELECT * 
																					FROM " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS cdgr
																					LEFT JOIN ".TABLE_CUSTOMERS_GROUPS." AS cg
																					ON cdgr.cdgr_customer_group_id = cg.customers_groups_id
																					WHERE cdgr.cdrules_id='".$grp_discount_row["cdrules_id"]."' 
																					ORDER BY cg.sort_order, cg.customers_groups_name ";
									$cust_grp_discount_result_sql = tep_db_query($cust_grp_discount_select_sql);
									while($cust_grp_discount_row = tep_db_fetch_array($cust_grp_discount_result_sql)){
                                        $discountSign = ($cust_grp_discount_row['cdgr_discount'][0] != '-') ? '<b style="color:blue;">'.$cust_grp_discount_row['cdgr_discount'].'</b>' : $cust_grp_discount_row['cdgr_discount'];
										$cust_grp_array[] = $cust_grp_discount_row['customers_groups_name'].'['.$discountSign.','.(int)$cust_grp_discount_row['cdgr_wor'].'] ';
									}
							
								
                                echo '<tr height="20" class="dataTableRow" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                <td align="center">' . $grp_discount_row['cdrules_title'] . '</td>      
                                <td align="center">' . $grp_discount_row['cdl_category_id'] . '</td>      
							   	<td align="center"><a href="javascript:void(confirm_delete(\'Category - ' . $grp_discount_row['cdrules_title'] . '\', \'\', \'' . tep_href_link($filename, 'action=remove_category&rules_id=' . $grp_discount_row["cdrules_id"]) .'&category_id='. $grp_discount_row['cdl_category_id'].'\'))">' . tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') . '</a></td>
							   	<td valign="top"><a href="'.tep_href_link(FILENAME_CATEGORIES, 'cPath=' . $_REQUEST['cPath'] . '&cID=' . $grp_discount_row['cdl_category_id'] . '&action=edit_category').'">'.tep_output_generated_category_path_sq($grp_discount_row['cdl_category_id'], 'category') .'</a></td>';
								?>
							   	<td valign="top">
								<?php 
								foreach($cust_grp_array as $cust_grp_dat): 
									echo $cust_grp_dat;
								endforeach; 
								?>
								</td>
								
								</tr>
							<?php
							 $row_count++;
                            }
                            ?>

                        </table>
                    </td>
                </tr>
				<tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                        <tr>
                            <td class="smallText" valign="top">
							<?php 
							echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($grp_discount_select_sql) > 0 ? "1" : "0", tep_db_num_rows($grp_discount_select_sql), tep_db_num_rows($grp_discount_select_sql)) : $rule_split_object->display_count($grp_discount_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) 
							?></td>
                            <td class="smallText" align="right">
							<?php 
							echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($grp_discount_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') 
							?></td>
                        </tr>
                    </table>
                </td>
            </tr>

            </table>


            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();

            return $report_section_html;
        }
    }
?>