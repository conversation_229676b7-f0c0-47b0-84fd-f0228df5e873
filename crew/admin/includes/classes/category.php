<?

class category {
    var $category_id;
	
	// class constructor
    function category($cid) {
      	$this->category_id = $cid;
	}
	
	// reupdate the categories_parent_path
	function set_categories_parent_path() {
		$parent_path = ($this->category_id > 0 ? '_' . tep_get_particular_cat_path($this->category_id) . '_' : '_');
		
		$this->_update_parent_path($this->category_id, $parent_path);
	}
	
	function _update_parent_path($parent_id, $path) {
		$subcategory_select_sql = "	SELECT categories_id
									FROM categories
									WHERE parent_id = '".tep_db_input($parent_id)."'";
		$subcategory_result_sql = tep_db_query($subcategory_select_sql);
	    while($subcategory_row = tep_db_fetch_array($subcategory_result_sql)) {
	    	$new_path = $path . ($parent_id > 0 ? $parent_id . '_' : '');
	    	
	    	$path_update_sql = "UPDATE categories
	    						SET categories_parent_path = '".tep_db_input(($parent_id > 0 ? $new_path : ''))."'
	    						WHERE categories_id='".tep_db_input($subcategory_row['categories_id'])."'";
	    	tep_db_query($path_update_sql);
	    	
	    	$this->_update_parent_path($subcategory_row['categories_id'], $new_path);
	    }
	}
	
	function get_available_product_child_type() {
		global $memcache_obj;
    	
        #key:categories_product_types/custom_products_type_child_id/array/categories_id/xxx
		$cache_key = CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORIES_PRODUCT_TYPES . '/custom_products_type_child_id/array/categories_id/' . (int)$this->category_id;
    	$cache_result = $memcache_obj->fetch($cache_key);
		if ($cache_result !== FALSE) {
			$available_category_product_type_child_array = $cache_result;
		} else {
			$available_category_product_type_child_array = array();
			$categories_product_types_select_sql = "SELECT custom_products_type_child_id 
													FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
													WHERE categories_id = '". (int)$this->category_id ."'
													ORDER BY custom_products_type_child_id";
			$categories_product_types_result_sql = tep_db_query($categories_product_types_select_sql);
			while($categories_product_types_row = tep_db_fetch_array($categories_product_types_result_sql)){
				$available_category_product_type_child_array[] = $categories_product_types_row['custom_products_type_child_id'];
			}
			$memcache_obj->store($cache_key, $available_category_product_type_child_array, 86400); // cache 24 hours
		}
		return $available_category_product_type_child_array;
	}
	
	function get_categories_service($language_id, $type = '') {
		$categories_services_array = array();
		
		$categories_services_select_sql = "	SELECT categories_services_name, categories_services_url, type 
											FROM " . TABLE_CATEGORIES_SERVICES . " 
											WHERE categories_id = '" . (int)$this->category_id . "' 
												AND language_id = '" . (int)$language_id . "'";
		
		if (tep_not_null($type)) {
			$categories_services_select_sql .= " AND type = '" . (int)$type . "'";
		}
		
		$categories_services_result_sql = tep_db_query($categories_services_select_sql);
		while($categories_services_row = tep_db_fetch_array($categories_services_result_sql)) {
			$categories_services_array[$categories_services_row['type']] = array(	'name' => $categories_services_row['categories_services_name'], 
																					'url' => $categories_services_row['categories_services_url']
																				);
		}
		
		return $categories_services_array;
	}
	
	function category_has_product_type($product_type_id, $customer_group_id=0) {
		$categeries_select_sql = "	SELECT c.categories_parent_path, c.parent_id 
									FROM " . TABLE_CATEGORIES . " AS c ";
		if ($customer_group_id) {
			$categeries_select_sql .= "	INNER JOIN " . TABLE_CATEGORIES_GROUPS . " as cg
											ON cg.categories_id = c.categories_id
												AND cg.groups_id IN ('0', '".$customer_group_id."')";
		}
		$categeries_select_sql .= "	WHERE c.categories_id = '".(int)$this->category_id."'
										AND c.categories_status = '1'";
		$categeries_result_sql = tep_db_query($categeries_select_sql);
		
		if ($categeries_row = tep_db_fetch_array($categeries_result_sql)) {
			if ($categeries_row['parent_id'] == 0) {
				$category_id = $this->category_id;
			} else {
				$cat_parent_path = explode('_',$categeries_row['categories_parent_path']);
				$category_id = $cat_parent_path[1];
			}
			
			$category_has_product_type_select_sql = "	SELECT custom_products_type_id 
														FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
														WHERE categories_id = '".(int)$category_id."'
															AND custom_products_type_id='".(int)$product_type_id."'";
			$category_has_product_type_result_sql = tep_db_query($category_has_product_type_select_sql);
			return (tep_db_num_rows($category_has_product_type_result_sql)>0 ? true : false );
		}
		
		return false;
	}
	
	function delete_cache() {
		global $memcache_obj;
		
                #key:categories/get/categories_url_alias/categories_id/xxx
		$memcache_obj->delete(TABLE_CATEGORIES . '/get/categories_url_alias/categories_id/' . $this->category_id, 0);
		
		$languages = tep_get_languages();
		
		for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
                    #key:categories_description/get/categories_image/categories_id/xxx/language/xxx
                    $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORIES_DESCRIPTION . '/get/categories_image/categories_id/' . $this->category_id . '/language/' . $languages[$i]['id'], 0);
                    
                    #key:categories_description/get/categories_image_title/categories_id/xxx/language/xxx
                    $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORIES_DESCRIPTION . '/get/categories_image_title/categories_id/' . $this->category_id . '/language/' . $languages[$i]['id'], 0);
                    
                    #key:categories_description/get/categories_name/categories_id/xxx/language/xxx
                    $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORIES_DESCRIPTION . '/get/categories_name/categories_id/' . $this->category_id . '/language/' . $languages[$i]['id'], 0);
                    
                    #key:categories_description/get/categories_pin_yin/categories_id/xxx/language/xxx
                    $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CATEGORIES_DESCRIPTION . '/get/categories_pin_yin/categories_id/' . $this->category_id . '/language/' . $languages[$i]['id'], 0);
		}
	}
	
	static public function get_game_list_by_product_type ($product_type) {
		global $memcache_obj;
		
		$games_id = array();
		$_result = array();
		
		# Game List
	    $cache_key = TABLE_CATEGORIES_STRUCTURES . '/games_id/array/categories_name/';
		$cache_result = $memcache_obj->fetch($cache_key);
	    
		if ($cache_result !== FALSE) {
			$_result = $cache_result;
		} else {
			$games_id = tep_get_games_id();
			
			if (!empty($games_id)) {
				for ($i=0, $cnt=count($games_id); $cnt > $i; $i++) {
					$game_support_cpt_sql = "	SELECT categories_id 
												FROM " . TABLE_CATEGORIES . " 
												WHERE categories_parent_path LIKE '%_" . $games_id[$i] . "_%' 
													AND custom_products_type_id IN (" . $product_type . ")";
					$game_support_res_sql = tep_db_query($game_support_cpt_sql);
					if (tep_db_num_rows($game_support_res_sql) > 0) {
						$games_temp[$games_id[$i]] = tep_get_categories_name($games_id[$i]);
					}
				}
				
				natcasesort($games_temp);
				
				foreach ($games_temp as $game_id => $game_name) {
					$_result[] = array( 'id' => $game_id, 
										'text' => $game_name );
				}
			}
			
			$memcache_obj->store($cache_key, $_result, 86400);
		}
		
		return $_result;
	}
        
	static public function get_game_list_by_product_type_child ($product_type_child) {
		global $memcache_obj;
		
		$games_id = array();
		$_result = array();
                $games_temp = array();
		
		# Game List
	    $cache_key = TABLE_CATEGORIES_STRUCTURES . '/games_id/array/cpct/categories_name/';
		$cache_result = $memcache_obj->fetch($cache_key);
	    
		if ($cache_result !== FALSE) {
			$_result = $cache_result;
		} else {
			$games_id = tep_get_games_id();
			
			if (!empty($games_id)) {
				for ($i=0, $cnt=count($games_id); $cnt > $i; $i++) {
					$game_support_cpt_sql = "	SELECT categories_id 
												FROM " . TABLE_CATEGORIES . " 
												WHERE categories_parent_path LIKE '%_" . $games_id[$i] . "_%' 
													AND custom_products_type_child_id IN (" . $product_type_child . ")";
					$game_support_res_sql = tep_db_query($game_support_cpt_sql);
					if (tep_db_num_rows($game_support_res_sql) > 0) {
						$games_temp[$games_id[$i]] = tep_get_categories_name($games_id[$i]);
					}
				}
				
				natcasesort($games_temp);
				
				foreach ($games_temp as $game_id => $game_name) {
					$_result[] = array( 'id' => $game_id, 
										'text' => $game_name );
				}
			}
			
			$memcache_obj->store($cache_key, $_result, 86400);
		}
		
		return $_result;
	}
}
?>