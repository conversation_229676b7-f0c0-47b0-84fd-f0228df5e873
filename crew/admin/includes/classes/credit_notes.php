<?php
if (file_exists(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_CREDIT_NOTES_STATEMENT)) {
	include_once(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_CREDIT_NOTES_STATEMENT);
}

include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'store_credit.php');
include_once(DIR_WS_CLASSES . 'po_suppliers.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

class credit_notes {
    var $identity, $identity_email, $user_roles, $currency_display_decimal;
	var $info;
	
	// class constructor
    function credit_notes($identity, $identity_email) {
      	$this->identity = $identity;	// Admin user
      	$this->identity_email = $identity_email;	// Admin user
      	
      	$this->user_roles = array (	'customers' => "www.offgamers.com",
									'supplier' => "supplier.offgamers.com"
									);
		
		$this->info = array();
		$this->currency_display_decimal = 2;
	}
	
	function search_credit_notes_statement($filename, $session_name) {
		$crnote_statement_html = '';
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
	  								array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50")
								);
		
		ob_start();
?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('crnote_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="0" cellpadding="0">
									<tr>
			    						<td class="main" width="120"><?=ENTRY_SUPPLIER_ID?></td>
			    						<td class="main">
			    							<?=tep_draw_input_field('user_id', $_SESSION[$session_name]["user_id"], ' id="user_id" size="30" ')?>
			    						</td>
			    						<td class="main" colspan="2">&nbsp;</td>
									</tr>
									<tr>
	            						<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
									<tr>
										<td class="main"><?=ENTRY_CRNOTES_START_DATE?></td>
										<td class="main" valign="top" nowrap>
    										<script language="javascript"><!--
  												var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "crnote_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
  												date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION[$session_name]["start_date"]?>';
											//--></script>
    									</td>
    									<td class="main" valign="top" width="120" nowrap><?=ENTRY_CRNOTES_END_DATE?></td>
    									<td class="main" valign="top">
			    							<script language="javascript"><!--
			  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "crnote_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
			  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION[$session_name]["end_date"]?>';
											//--></script>
			    						</td>
									</tr>
									<tr>
	            						<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_TRANSACTION_ID?></td>
			    						<td class="main"><?=tep_draw_input_field('transaction_id', $_SESSION[$session_name]["transaction_id"], ' id="transaction_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
			    						<td class="main" colspan="2">&nbsp;</td>
									</tr>
									<tr>
	            						<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
			    						<td colspan="2">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script language="javascript"><!--
				function form_checking(form_obj, action) {
				    //form_obj.submit();
					return true;
	    		}
	    		
				function resetControls(controlObj) {
					if (trim_str(controlObj.value) != '') {
						document.crnote_criteria.transaction_id.value = '';
						document.crnote_criteria.user_id.value = '';
						document.crnote_criteria.start_date.value = '';
						document.crnote_criteria.end_date.value = '';
						document.crnote_criteria.show_records.selectedIndex = 0;
		    		} else {
		    			controlObj.value = '';
		    		}
				}
	    	//-->
			</script>
<?php
		$crnote_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $crnote_statement_html;
	}
	
	function show_credit_notes_statement($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		if (!$_REQUEST['cont']) {
			$_SESSION[$session_name]["user_id"] = $input_array["user_id"];
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["transaction_id"] = $input_array["transaction_id"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
	  	}
        
	  	$transaction_id_str = (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) ? " scrh.store_credit_note_history_trans_id='" . $_SESSION[$session_name]["transaction_id"] . "' AND scrh.store_credit_note_history_trans_type='PO'" : "1";
	  	
	  	// Get user id
	  	if (tep_not_null($_SESSION[$session_name]["transaction_id"])) {
	  		$user_info_select_sql = "	SELECT scrh.customer_id 
	  									FROM " . TABLE_STORE_CREDIT_NOTE_HISTORY . " AS scrh 
	  									WHERE " . $transaction_id_str . " 
										LIMIT 1";
			$user_info_result_sql = tep_db_query($user_info_select_sql);
			$user_info_row = tep_db_fetch_array($user_info_result_sql);
			
			$_SESSION[$session_name]["user_id"] = $user_info_row['customer_id'];
	  	}
		
	  	$statement_user_str = " scrh.customer_id='" . tep_db_input($_SESSION[$session_name]["user_id"]) . "' ";
	  	
	  	if (tep_not_null($_SESSION[$session_name]["start_date"])) {
			if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
				$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				$start_date_str = " ( scrh.store_credit_note_history_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
                $start_date_str = " ( scrh.store_credit_note_history_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."')";
			}
		} else {
			$start_date_str = " 1 ";
		}
		
		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				$end_date_str = " ( scrh.store_credit_note_history_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
                $end_date_str = " ( scrh.store_credit_note_history_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' )";
			}
		} else {
			$end_date_str = " 1 ";
		}
	    
	  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
	  	
	  	// Need store_account_history_id in ORDER BY to maintain the ordering for those records on the same date and time
	  	$cr_statement_select_sql = "	SELECT scrh.store_credit_note_history_id, DATE_FORMAT(scrh.store_credit_note_history_date, '%Y-%m-%d %H:%i') AS activity_date, 
	  										scrh.store_credit_note_history_trans_type AS trans_type, scrh.store_credit_note_history_trans_id AS trans_id, 
			  								scrh.store_credit_note_history_activity_title AS activity_title, scrh.store_credit_note_history_currency AS currency, 
			  								scrh.store_credit_note_history_debit_amount AS debit_amount, scrh.store_credit_note_history_credit_amount AS credit_amount, 
			  								scrh.store_credit_note_history_after_balance AS after_balance, scrh.store_credit_note_history_added_by AS added_by
										FROM " . TABLE_STORE_CREDIT_NOTE_HISTORY . " AS scrh 
										WHERE " . $transaction_id_str . " 
											AND " . $start_date_str . " 
											AND " . $end_date_str . "
											AND " . $statement_user_str . "
										ORDER BY scrh.store_credit_note_history_date desc, scrh.store_credit_note_history_id DESC";
		
		$show_records = $_SESSION[$session_name]["show_records"];
		
		if ($show_records != "ALL") {
			$cr_statement_split_object = new splitPageResults($_REQUEST['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $cr_statement_select_sql, $cr_statement_select_sql_numrows, true);
		}
		$cr_statement_result_sql = tep_db_query($cr_statement_select_sql);
		
		$user_info_array = $this->_get_user_particulars($_SESSION[$session_name]["user_id"], 'customers');
		
		ob_start();
?>
			<table width="100%" border="0" cellspacing="2" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0">
							<td valign="bottom">
								<? $user_name_link = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $_SESSION[$session_name]["user_id"] . '&action=edit') . '" target="_blank">' . $user_info_array['fname'].' '.$user_info_array['lname'] . '</a>'; ?>
								<?=tep_not_null($user_info_array['email']) ? sprintf(TABLE_SECTION_HEADING_CRNOTES_STAT, $user_name_link, '(ID :'.$_SESSION[$session_name]["user_id"].')') : ''?>
							</td>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
							<tr>
								<td width="12%" class="reportBoxHeading"><?=TABLE_HEADING_CRNOTES_STAT_DATETIME?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_CRNOTES_STAT_ACTIVITY?></td>
							    <td width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_CRNOTES_STAT_DEBIT?></td>
							    <td width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_CRNOTES_STAT_CREDIT?></td>
							    <td width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_CRNOTES_STAT_BALANCE?></td>
							</tr>
<?php
		$row_count = 0;
		while ($cr_statement_row = tep_db_fetch_array($cr_statement_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
	  		$cr_history_id_str = $activity_title = '';

	  		$store_credit_note_history_id = $cr_statement_row['store_credit_note_history_id']; 

            $add_comment_flag = true;
            
            switch($cr_statement_row['trans_type']) {
  				case 'PO':
  					$activity_title = '<a href="'.tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('action','subaction','user_role','user_email','start_date')).'&subaction=edit_po&po_id='.$cr_statement_row['trans_id']).'" target="p_order">'.sprintf(TITLE_TRANS_PURCHASE_ORDER, $cr_statement_row['trans_id']).'</a>';
  					break;
  				default:
  					$activity_title = $cr_statement_row['trans_type'] . ' ' . $cr_statement_row['trans_id'];
  					break;
  			}
  			
  			$comments_select_sql = "SELECT store_credit_note_comments_date_added, store_credit_note_comments_added_by, store_credit_note_comments
		                            FROM ". TABLE_STORE_CREDIT_NOTE_COMMENTS ." 
		                            WHERE store_credit_note_history_id='". $store_credit_note_history_id  ."'
		                            ORDER BY store_credit_note_comments_id";
            $comments_result_sql = tep_db_query($comments_select_sql);
            
	  		if (tep_not_null($cr_statement_row['activity_title'])) {
	  			if (strtolower(trim($cr_statement_row['activity_title'])) == 'manual deduction' ||
	  				strtolower(trim($cr_statement_row['activity_title'])) == 'manual addition'
	  				) {
	  				$cr_history_id_str = sprintf(TEXT_CRNOTES_STAT_TRANS_ID, $cr_statement_row['store_credit_note_history_id']);
	  			}
	  			
	  			$activity_title .= ' ' . $cr_statement_row['activity_title'] . ' (by ' . $cr_statement_row['added_by'] . ')' . (tep_not_null($cr_history_id_str) ? '<br>'.$cr_history_id_str : '');
	  		}
	  		
	  		echo '			<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td valign="top" class="reportRecords" nowrap>'.$cr_statement_row['activity_date'].'</td>
							   	<td valign="top">
							   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
							   			<tr>
            								<td class="reportRecords">'.$activity_title.'</td>
            							</tr>';
			
            while ($comments_row = tep_db_fetch_array($comments_result_sql)) {
			    echo '					<tr>
					    					<td class="reportRecords"><hr>'.$comments_row['store_credit_note_comments_date_added'] . ' (by '. $comments_row['store_credit_note_comments_added_by'] .') '.'<br><div class="paymentRemarkSelectedRow">Comment:<br>'. nl2br($comments_row['store_credit_note_comments']) . '</div></td>
					    				</tr>';
			}
            
            if ($add_comment_flag) {
?>
		    							<tr>
		    								<td class="formAreaTitle">
		    								    <div id="link_<?=$store_credit_note_history_id?>" align="top">
		                        					<a href="javascript:;" onClick="showHideAddComment('box_<?=$store_credit_note_history_id?>', 'link_<?=$store_credit_note_history_id?>', 'show');"><?=TEXT_CRNOTES_STAT_ADD_COMMENT_SHOW?></a>
		                        				</div>
		    								</td>
		    	        				</tr>
		    	        				<tbody id="box_<?=$store_credit_note_history_id?>" class="hide">
		    	        				<tr>
		    								<td>
		    									<?=tep_draw_form('add_comment_form_' . $store_credit_note_history_id, $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=add_comment&cont=1', 'post', '')?>
		    									<?=tep_draw_hidden_field("store_credit_note_history_id", $store_credit_note_history_id) . tep_draw_hidden_field("user_id", $_SESSION[$session_name]["user_id"]) . tep_draw_hidden_field("user_role", 'customers') . tep_draw_hidden_field("activity_title", $cr_statement_row['activity_title'])?>
		    	        						<table border="0" cellspacing="0" cellpadding="2">
		    										<tr>
		    		          							<td class="main" valign="top"><?=ENTRY_CRNOTES_STAT_MANUAL_ADJUST_COMMENT?></td>
		    		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '30', '5', '')?></td>
		    										</tr>
		    										<tr>
		                        	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		                        	      			</tr>
		    										<tr>
		    		          							<td class="main" valign="top">&nbsp;</td>
		    		          							<td class="main"><?=tep_submit_button(BUTTON_ADD_COMMENT, ALT_BUTTON_ADD_COMMENT, '', 'inputButton')?></td>
		    										</tr>
		    	        						</table>
		    	        						</form>
		    								</td>
		    	        				</tr>
		    	        				</tbody>
<?php       }

            echo '		   	    	</table>
            					</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($cr_statement_row['debit_amount']) ? $currencies->format($cr_statement_row['debit_amount'], false, $cr_statement_row['currency']) : TEXT_CRNOTES_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($cr_statement_row['credit_amount']) ? $currencies->format($cr_statement_row['credit_amount'], false, $cr_statement_row['currency']) : TEXT_CRNOTES_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords"><span '.($cr_statement_row['after_balance'] < 0 ? 'class="redIndicatorBold"' : '').'>'.$currencies->format($cr_statement_row['after_balance'], false, $cr_statement_row['currency']).'</a></td>
							  </tr>';

	  		$row_count++;
	  	}
?>
						</table>
					</td>
				</tr>
				<tr>
        			<td>
        				<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($cr_statement_result_sql) > 0 ? "1" : "0", tep_db_num_rows($cr_statement_result_sql), tep_db_num_rows($cr_statement_result_sql)) : $cr_statement_split_object->display_count($cr_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'], $result_display_text)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $cr_statement_split_object->display_links($cr_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
			</table>
    			<script language="javascript"><!--
    				function showHideAddComment(comment_div, comment_link_div, classtype) {
                		DOMCall(comment_div).className = classtype;
                		
                		if (classtype == 'show') {
                			DOMCall(comment_link_div).innerHTML = '<a href="javascript:;" onclick="showHideAddComment(\''+comment_div+'\', \''+comment_link_div+'\', \'hide\');">'+'<?=TEXT_CRNOTES_STAT_ADD_COMMENT_HIDE?>'+'</a>';
                		} else {
                			DOMCall(comment_link_div).innerHTML = '<a href="javascript:;" onclick="showHideAddComment(\''+comment_div+'\', \''+comment_link_div+'\', \'show\');">'+'<?=TEXT_CRNOTES_STAT_ADD_COMMENT_SHOW?>'+'</a>';
                		}
                	}
    	    	//-->
    			</script>
<?php
		$report_section_html = ob_get_contents();
		ob_end_clean() ;
		
		// This should be here to let all the session get updated value
		$search_section_html = $this->search_credit_notes_statement($filename, $session_name);
		
		$manual_adjust_section_html = $this->_manual_adjust_form($filename, $_SESSION[$session_name]["user_id"], 'customers');
		
		return $search_section_html . "\n" . $report_section_html . "\n" . $manual_adjust_section_html;
	}
	
	function _manual_adjust_form($filename, $user_id, $user_role) {
		global $currencies;
		$report_section_html = '';
		
		// Permission for making the manual adjustment
		$manual_deduct_permission = tep_admin_files_actions(FILENAME_CREDIT_NOTES_STATEMENT, 'STORE_CREDIT_NOTES_MANUAL_DEDUCT');
		$manual_add_permission = tep_admin_files_actions(FILENAME_CREDIT_NOTES_STATEMENT, 'STORE_CREDIT_NOTES_MANUAL_ADD');
		
		if ($manual_deduct_permission || $manual_add_permission) {
			$user_store_crnote_array = $this->_get_supplier_store_credit_notes_balance_lists($user_id, $user_role, true);
			
			$default_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
			$all_buy_currencies_array = array_merge($default_array, $currencies->get_currency_set(''));
			
			ob_start();
?>
			<br>
			<table width="100%" border="0" align="center" cellpadding="2" cellspacing="1" valign="middle">
				<tr>
					<td width="47%" valign="top" class="<?=$manual_deduct_permission ? 'formArea' : ''?>">
<?			if ($manual_deduct_permission) { ?>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="formAreaTitle"><?=TABLE_HEADING_CRNOTES_STAT_MANUAL_DEDUCT?></td>
	        				</tr>
	        				<tr>
								<td>
									<?=tep_draw_form('manual_deduct_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_deduct&cont=1', 'post', '')?>
									<?=tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role)?>
	        						<table border="0" cellspacing="0" cellpadding="2">
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_CRNOTES_STAT_CURRENCY_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("cur_acc", $user_store_crnote_array, '', '')?></td>
										</tr>
										<tr>
		          							<td class="main"><?=ENTRY_CRNOTES_STAT_DEDUCT_AMOUNT?></td>
		          							<td class="main">-&nbsp;<?=tep_draw_input_field('deduct_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_CRNOTES_STAT_MANUAL_ADJUST_COMMENT?></td>
		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '50', '5', '')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top">&nbsp;</td>
		          							<td class="main"><?=tep_submit_button(BUTTON_DEDUCT_BALANCE, ALT_BUTTON_DEDUCT_BALANCE, 'onClick="this.disabled=true;document.manual_deduct_form.submit();"', 'inputButton')?></td>
										</tr>
	        						</table>
	        						</form>
								</td>
	        				</tr>
	        			</table>
<?			} ?>
	        		</td>
	        		<td>&nbsp;</td>	
	        		<td width="47%" valign="top" class="<?=$manual_add_permission ? 'formArea' : ''?>">
<?			if ($manual_add_permission) { ?>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="formAreaTitle"><?=TABLE_HEADING_CRNOTES_STAT_MANUAL_ADD?></td>
	        				</tr>
	        				<tr>
								<td>
									<?=tep_draw_form('manual_add_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_add&cont=1', 'post', '')?>
									<?=tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role)?>
	        						<table border="0" cellspacing="0" cellpadding="2">
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_CRNOTES_STAT_CURRENCY_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("cur_acc", $all_buy_currencies_array, '', '')?></td>
										</tr>
										<tr>
		          							<td class="main"><?=ENTRY_CRNOTES_STAT_ADD_AMOUNT?></td>
		          							<td class="main">+&nbsp;<?=tep_draw_input_field('add_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_CRNOTES_STAT_MANUAL_ADJUST_COMMENT?></td>
		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '50', '5', '')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top">&nbsp;</td>
		          							<td class="main"><?=tep_submit_button(BUTTON_ADD_BALANCE, ALT_BUTTON_ADD_BALANCE, 'onClick="this.disabled=true;document.manual_add_form.submit();"', 'inputButton')?></td>
										</tr>
	        						</table>
	        						</form>
								</td>
	        				</tr>
	        			</table>
<?		} ?>
	        		</td>
	        	</tr>
	        </table>
<?php
			$report_section_html = ob_get_contents();
			ob_end_clean();
		}
		return $report_section_html;
	}
	
	function manual_deduct_amount($input_array, &$messageStack) {
	    global $currencies;
		$error = false;
		
		$manual_deduct_permission = tep_admin_files_actions(FILENAME_CREDIT_NOTES_STATEMENT, 'STORE_CREDIT_NOTES_MANUAL_DEDUCT');
		
		if ($manual_deduct_permission) {
			$store_crnote_select_sql = "SELECT store_account_credit_note_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($input_array['user_id']) . "' 
											AND user_role = '" . tep_db_input($input_array['user_role']) . "' 
											AND store_account_balance_currency = '" . tep_db_input($input_array['cur_acc']) . "'";
			$store_crnote_result_sql = tep_db_query($store_crnote_select_sql);
			
			if ($store_crnote_row = tep_db_fetch_array($store_crnote_result_sql)) {
				$deduct_amount = trim($input_array['deduct_amount']);
				if (is_numeric($deduct_amount) && $deduct_amount > 0) {
					// Update live credit balance
					$update_info = array(	array(	'field_name'=> 'store_account_credit_note_amount',
													'operator'=> '-', 
													'value'=> $deduct_amount)
										);
					
					$new_store_cr_balance = $this->_set_credit_note_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
					
					// Insert credit note history
					$credit_note_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
															'store_credit_note_history_date' => 'now()',
															'store_credit_note_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
															'store_credit_note_history_debit_amount' => round($deduct_amount,8),
															'store_credit_note_history_credit_amount' => 'NULL',
															'store_credit_note_history_after_balance' => round($new_store_cr_balance,8),
															'store_credit_note_history_activity_type' => 'MR',
															'store_credit_note_history_activity_title' => 'Manual Deduction',
															'store_credit_note_history_added_by' => $this->identity_email,
															'store_credit_note_history_added_by_role' => 'admin'
															);
					tep_db_perform(TABLE_STORE_CREDIT_NOTE_HISTORY, $credit_note_history_data_array);
					
					$transaction_id = tep_db_insert_id();
					
					$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
					$po_supplier_info = $po_suppliers->get_po_supplier_info($input_array['user_id']);
					
					// Insert comments
					$comment_array = array(	'store_credit_note_history_id' => $transaction_id,
											'comments' => $input_array['comments'],
											'activity_title' => 'Manual Deduction',
											'activity_currency' => $input_array['cur_acc'],
											'activity_amt' => round($deduct_amount,8),
											'balance_amt' => round($new_store_cr_balance,8),
											'supplier_alias' => $po_supplier_info['code'],
											'supplier_fname' => $po_supplier_info['firstname'],
											'supplier_lname' => $po_supplier_info['lastname']
											);
					if (isset($input_array['po_ref_id']) && isset($input_array['po_id'])) {
						$comment_array['po_ref_id'] = $input_array['po_ref_id'];
						$comment_array['po_id'] = $input_array['po_id'];
					}
					$comment_res = $this->add_comment($comment_array, $messageStack);
					
					$transact_date = '';
                    if (tep_not_null($transaction_id)) {
                        $cr_stat_coments_details_select_sql = " SELECT store_credit_note_history_date
                                                                FROM ". TABLE_STORE_CREDIT_NOTE_HISTORY ."
                                                                WHERE store_credit_note_history_id='". $transaction_id ."'";
                        $cr_stat_coments_details_result_sql = tep_db_query($cr_stat_coments_details_select_sql);

                        $cr_stat_coments_details_row = tep_db_fetch_array($cr_stat_coments_details_result_sql);
                        $transact_date = $cr_stat_coments_details_row['store_credit_note_history_date'];
                    }
					
					// send to admin 
					$email = sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_ID, $transaction_id) . "\n" .
					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_DATE, $transact_date) . "\n" .
					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_DEDUCTION, $currencies->format($deduct_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n" .
							 sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_ALIAS, $po_supplier_info['code']) . "\n" .
							 sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_NAME, $po_supplier_info['firstname'].' '.$po_supplier_info['lastname']) . "\n" .
					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_USER, $_SESSION['login_email_address'].' ('.tep_get_admin_group_name($_SESSION['login_email_address']).')') . "\n" .
					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_IP, tep_get_ip_address()) . "\n\n" .
					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_COMMENT, tep_db_prepare_input($input_array['comments']));

                                        $slack = new slack_notification();
                                        $data = json_encode(array(
                                                'text' => implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_DEDUCTION, $transaction_id))),
                                                'attachments' => array(
                                                        array(
                                                                'color' => 'warning',
                                                                'text' => $email
                                                        )
                                                )
                                        ));
                                        $slack->send(SLACK_WEBHOOK_INV_CREDITNOTE, $data);

            		$messageStack->add_session(SUCCESS_CRNOTES_STAT_MANUAL_DEDUCTION, 'success');
            		
				} else {
					$error = true;
					$messageStack->add_session(ERROR_CRNOTES_STAT_INVALID_DEDUCTION_AMOUNT, 'error');
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_CRNOTES_STAT_CURRENCY_ACC_NOT_EXISTS, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function manual_add_amount($input_array, &$messageStack) {
	    global $currencies;
		$error = false;
		
		$manual_add_permission = tep_admin_files_actions(FILENAME_CREDIT_NOTES_STATEMENT, 'STORE_CREDIT_NOTES_MANUAL_ADD');
		
		if ($manual_add_permission) {
			if ($this->_check_store_credit_note_exists($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'])) {
				$add_amount = trim($input_array['add_amount']);
				if (is_numeric($add_amount) && $add_amount > 0) {
					if ($this->_is_within_daily_limit($add_amount / $currencies->get_value($input_array['cur_acc'], ''))) {	// Use SPOT value
						// Update live credit balance
						$update_info = array(	array(	'field_name'=> 'store_account_credit_note_amount',
														'operator'=> '+', 
														'value'=> $add_amount)
											);
						
						$new_store_cr_balance = $this->_set_credit_note_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
						
						// Insert credit note history
						$credit_note_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
																'store_credit_note_history_date' => 'now()',
																'store_credit_note_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
																'store_credit_note_history_debit_amount' => 'NULL',
																'store_credit_note_history_credit_amount' => round($add_amount,8),
																'store_credit_note_history_after_balance' => round($new_store_cr_balance,8),
																'store_credit_note_history_activity_type' => 'MI',
																'store_credit_note_history_activity_title' => 'Manual Addition',
																'store_credit_note_history_added_by' => $this->identity_email,
																'store_credit_note_history_added_by_role' => 'admin'
																);
						tep_db_perform(TABLE_STORE_CREDIT_NOTE_HISTORY, $credit_note_history_data_array);
						
                        $transaction_id = tep_db_insert_id();
						
						$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
						$po_supplier_info = $po_suppliers->get_po_supplier_info($input_array['user_id']);
						
						// Insert comments
						$comment_array = array(	'store_credit_note_history_id' => $transaction_id,
												'comments' => $input_array['comments'],
												'activity_title' => 'Manual Addition',
												'activity_currency' => $input_array['cur_acc'],
												'activity_amt' => round($add_amount,8),
												'balance_amt' => round($new_store_cr_balance,8),
												'supplier_alias' => $po_supplier_info['code'],
												'supplier_fname' => $po_supplier_info['firstname'],
												'supplier_lname' => $po_supplier_info['lastname']
												);
						if (isset($input_array['po_ref_id']) && isset($input_array['po_id'])) {
							$comment_array['po_ref_id'] = $input_array['po_ref_id'];
							$comment_array['po_id'] = $input_array['po_id'];
						}
						$comment_res = $this->add_comment($comment_array, $messageStack);
						
                        $transact_date = '';
                        if (tep_not_null($transaction_id)) {
                            $cr_stat_coments_details_select_sql = " SELECT store_credit_note_history_date
                                                            		FROM ". TABLE_STORE_CREDIT_NOTE_HISTORY ."
                                                           	 		WHERE store_credit_note_history_id='". $transaction_id ."'";
                            $cr_stat_coments_details_result_sql = tep_db_query($cr_stat_coments_details_select_sql);
							
                            $cr_stat_coments_details_row = tep_db_fetch_array($cr_stat_coments_details_result_sql);
                            $transact_date = $cr_stat_coments_details_row['store_credit_note_history_date'];
                        }
    					
    					// send to admin
    					$email = sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_ID, $transaction_id) . "\n" .
    					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_DATE, $transact_date) . "\n" .
    					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_ADDITION, $currencies->format($add_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n" .
								 sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_ALIAS, $po_supplier_info['code']) . "\n" .
								 sprintf(EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_NAME, $po_supplier_info['firstname'].' '.$po_supplier_info['lastname']) . "\n" .
    					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_USER, $_SESSION['login_email_address'].' ('.tep_get_admin_group_name($_SESSION['login_email_address']).')') . "\n" .
    					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_IP, tep_get_ip_address()) . "\n\n" .
    					         sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_COMMENT, tep_db_prepare_input($input_array['comments']));
						
						$slack = new slack_notification();
						$data = json_encode(array(
							'text' => implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_ADDITION, $transaction_id))),
							'attachments' => array(
								array(
									'color' => 'warning',
									'text' => $email
								)
							)
						));
						$slack->send(SLACK_WEBHOOK_INV_CREDITNOTE, $data);
						
						$messageStack->add_session(SUCCESS_CRNOTES_STAT_MANUAL_ADDITION, 'success');
					} else {
						$error = true;
						$messageStack->add_session(ERROR_REACHED_DAILY_CREDIT_LIMIT, 'error');
					}
				} else {
					$error = true;
					$messageStack->add_session(ERROR_CRNOTES_STAT_INVALID_ADDITION_AMOUNT, 'error');
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_CRNOTES_STAT_CURRENCY_ACC_NOT_EXISTS, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function miscellaneous_add_amount($input_array, $action_type, $action_title, &$messageStack) {
		
		$error = false;
		
		if ($this->_check_store_credit_note_exists($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'])) {
			$add_amount = trim($input_array['add_amount']);
			if (is_numeric($add_amount) && $add_amount > 0) {
				// Update live credit balance
				$update_info = array(	array(	'field_name'=> 'store_account_credit_note_amount',
												'operator'=> '+', 
												'value'=> $add_amount)
									);
				
				$new_store_cr_balance = $this->_set_credit_note_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
				
				// Insert credit note history
				$credit_note_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
														'store_credit_note_history_date' => 'now()',
														'store_credit_note_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
														'store_credit_note_history_debit_amount' => 'NULL',
														'store_credit_note_history_credit_amount' => round($add_amount,8),
														'store_credit_note_history_after_balance' => round($new_store_cr_balance,8),
														'store_credit_note_history_activity_type' => tep_db_prepare_input($action_type),
														'store_credit_note_history_activity_title' => tep_db_prepare_input($action_title),
														'store_credit_note_history_added_by' => $this->identity_email,
														'store_credit_note_history_added_by_role' => 'admin'
														);
				if (isset($input_array['trans_type']) && isset($input_array['trans_id'])) {
					$credit_note_history_data_array['store_credit_note_history_trans_type'] = tep_db_prepare_input($input_array['trans_type']);
					$credit_note_history_data_array['store_credit_note_history_trans_id'] = tep_db_prepare_input($input_array['trans_id']);
				}
				tep_db_perform(TABLE_STORE_CREDIT_NOTE_HISTORY, $credit_note_history_data_array);
				$transaction_id = tep_db_insert_id();
				
				$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
				$po_supplier_info = $po_suppliers->get_po_supplier_info($input_array['user_id']);
				
				// Insert comments
				$comment_array = array(	'store_credit_note_history_id' => $transaction_id,
										'comments' => $input_array['comments'],
										'activity_title' => 'Credited Credit Notes',
										'activity_currency' => $input_array['cur_acc'],
										'activity_amt' => round($add_amount,8),
										'balance_amt' => round($new_store_cr_balance,8),
										'supplier_alias' => $po_supplier_info['code'],
										'supplier_fname' => $po_supplier_info['firstname'],
										'supplier_lname' => $po_supplier_info['lastname']
										);
				if (isset($input_array['po_ref_id']) && isset($input_array['po_id'])) {
					$comment_array['po_ref_id'] = $input_array['po_ref_id'];
					$comment_array['po_id'] = $input_array['po_id'];
				}
				$comment_res = $this->add_comment($comment_array, $messageStack);
			} else {
				$error = true;
				$messageStack->add_session(ERROR_STORE_CREDIT_NOTE_AMOUNT, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_STORE_CREDIT_NOTE_CURRENCY_ACC_NOT_EXISTS, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function miscellaneous_deduct_amount($input_array, $action_type, $action_title, &$messageStack) {
		
		$error = false;
		
		if ($this->_check_store_credit_note_exists($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'])) {
			$deduct_amount = trim($input_array['deduct_amount']);
			if (is_numeric($deduct_amount) && $deduct_amount > 0) {
				// Update live credit balance
				$update_info = array(	array(	'field_name'=> 'store_account_credit_note_amount',
												'operator'=> '-', 
												'value'=> $deduct_amount)
									);
				
				$new_store_cr_balance = $this->_set_credit_note_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
				
				if ($new_store_cr_balance !== false) {
					// Insert credit note history
					$credit_note_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
															'store_credit_note_history_date' => 'now()',
															'store_credit_note_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
															'store_credit_note_history_debit_amount' => round($deduct_amount,8),
															'store_credit_note_history_credit_amount' => 'NULL',
															'store_credit_note_history_after_balance' => round($new_store_cr_balance,8),
															'store_credit_note_history_activity_type' => tep_db_prepare_input($action_type),
															'store_credit_note_history_activity_title' => tep_db_prepare_input($action_title),
															'store_credit_note_history_added_by' => $this->identity_email,
															'store_credit_note_history_added_by_role' => 'admin'
															);
					if (isset($input_array['trans_type']) && isset($input_array['trans_id'])) {
						$credit_note_history_data_array['store_credit_note_history_trans_type'] = $input_array['trans_type'];
						$credit_note_history_data_array['store_credit_note_history_trans_id'] = $input_array['trans_id'];
					}
					tep_db_perform(TABLE_STORE_CREDIT_NOTE_HISTORY, $credit_note_history_data_array);
					$transaction_id = tep_db_insert_id();
					
					$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
					$po_supplier_info = $po_suppliers->get_po_supplier_info($input_array['user_id']);
					
					// Insert comments
					$comment_array = array(	'store_credit_note_history_id' => $transaction_id,
											'comments' => $input_array['comments'],
											'activity_title' => 'Debited Credit Notes',
											'activity_currency' => $input_array['cur_acc'],
											'activity_amt' => round($deduct_amount,8),
											'balance_amt' => round($new_store_cr_balance,8),
											'supplier_alias' => $po_supplier_info['code'],
											'supplier_fname' => $po_supplier_info['firstname'],
											'supplier_lname' => $po_supplier_info['lastname']
											);
					if (isset($input_array['po_ref_id']) && isset($input_array['po_id'])) {
						$comment_array['po_ref_id'] = $input_array['po_ref_id'];
						$comment_array['po_id'] = $input_array['po_id'];
					}
					$comment_res = $this->add_comment($comment_array, $messageStack);
				} else {
					$error = true;
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_STORE_CREDIT_NOTE_AMOUNT, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_STORE_CREDIT_NOTE_CURRENCY_ACC_NOT_EXISTS, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function add_comment($input_array, &$messageStack) {
		$error = false;
		
		if (tep_not_null($input_array['comments'])) {
			// Insert account statement comment
			$cr_comment_data_array = array('store_credit_note_history_id' => (int)$input_array['store_credit_note_history_id'],
											'store_credit_note_comments' => tep_db_prepare_input($input_array['comments']),
											'store_credit_note_comments_date_added' => 'now()',
											'store_credit_note_comments_added_by' => $this->identity_email
											);
			tep_db_perform(TABLE_STORE_CREDIT_NOTE_COMMENTS, $cr_comment_data_array);
			
			$messageStack->add_session(SUCCESS_CRNOTES_COMMENTS, 'success');
			
			$po_link = '';
			if (isset($input_array['po_ref_id']) && isset($input_array['po_id'])) {
				$link_str = '<a href="'.tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'po_id='.$input_array['po_id'].'&subaction=edit_po&selected_box=po', 'NONSSL').'" target="_blank">'.$input_array['po_ref_id'].'</a>';
				$po_link = sprintf(EMAIL_CRNOTES_STAT_TEXT_PO_LINK, $link_str) . "\n";
			}
			
			$email = sprintf(EMAIL_CRNOTES_STAT_TEXT_TITLE, $input_array['store_credit_note_history_id']) . "\n" .
						EMAIL_SEPARATOR . "\n\n" .
						sprintf(EMAIL_CRNOTES_STAT_TEXT_TRANSACT_NUMBER, $input_array['store_credit_note_history_id']) . "\n" . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_TRANSACT_DATE, date("Y-m-d H:i:s")) . "\n" .
						sprintf(EMAIL_CRNOTES_STAT_TEXT_SUPPLIER_ALIAS, $input_array['supplier_alias']) . "\n" .
						sprintf(EMAIL_CRNOTES_STAT_TEXT_SUPPLIER_NAME, $input_array['supplier_fname'].' '.$input_array['supplier_lname']) . "\n" .
						sprintf(EMAIL_CRNOTES_STAT_TEXT_ACTIVITY, $input_array['activity_title']) . "\n\n" .
						sprintf(EMAIL_CRNOTES_STAT_TEXT_COMMENTS, $input_array['comments']) . "\n" . 
						$po_link . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_ACTIVITY_CURRENCY, $input_array['activity_currency']) . "\n" . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_ACTIVITY_AMOUNT, $input_array['activity_amt']) . "\n" . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_BALANCE_AMOUNT, $input_array['balance_amt']) . "\n" . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_UPDATE_USER, $_SESSION['login_email_address'].' ('.tep_get_admin_group_name($_SESSION['login_email_address']).')') . "\n" . 
						sprintf(EMAIL_CRNOTES_STAT_TEXT_UPDATE_IP, tep_get_ip_address()) . "\n";

			$slack = new slack_notification();
			$data = json_encode(array(
				'text' => implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_CRNOTES_STAT_UPDATE_SUBJECT, $input_array['store_credit_note_history_id']))),
				'attachments' => array(
					array(
						'color' => 'warning',
						'text' => $email
					)
				)
			));
			$slack->send(SLACK_WEBHOOK_INV_CREDITNOTE, $data);
			
		} else {
			$error = true;
			$messageStack->add_session(WARNING_CRNOTES_STAT_COMMENTS_EMPTY, 'warning');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function _check_store_credit_note_exists($user_id, $user_role, $credit_currency) {
		$store_acc_balance_select_sql = "	SELECT store_account_credit_note_amount 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
												AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
		$store_acc_balance_result_sql = tep_db_query($store_acc_balance_select_sql);
		
		if (tep_db_num_rows($store_acc_balance_result_sql) > 0) {	// Store credit account exists
			return true;
		} else {	// Check if this is a valid customer
			$user_checking_info = $this->_get_user_particulars($user_id, $user_role);
			
			if (is_array($user_checking_info) && count($user_checking_info)) {	// Valid user
				$account_balance_data_array = array('user_id' => $user_id,
													'user_role' => $user_role,
							                        'store_account_balance_currency' => $credit_currency,
							                        'store_account_credit_note_amount' => 0,
							                        'store_account_last_modified' => 'now()'
							                       );
				tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $account_balance_data_array);
				
				return true;
			} else {
				return false;
			}
		}
	}
	
	function _get_supplier_store_credit_notes_balance_lists($user_id, $user_role, $include_option_title=false) {
		$account_array = array();
		
		if ($include_option_title)	$account_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$store_cr_select_sql = "	SELECT sab.store_account_balance_currency, cur.title, cur.symbol_left, cur.symbol_right 
                                    FROM " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
                                    INNER JOIN " . TABLE_CURRENCIES . " AS cur 
                                        ON sab.store_account_balance_currency=cur.code 
                                    WHERE sab.user_id = '" . tep_db_input($user_id) . "' 
                                        AND sab.user_role = '" . tep_db_input($user_role) . "' 
                                        AND sab.store_account_credit_note_amount > 0
                                    ORDER BY cur.code";
		$store_cr_result_sql = tep_db_query($store_cr_select_sql);
		
		while ($store_cr_row = tep_db_fetch_array($store_cr_result_sql)) {
			$account_array[] = array('id' => $store_cr_row['store_account_balance_currency'], 'text' => $store_cr_row['title'] . ' ('.$store_cr_row['symbol_left'].$store_cr_row['symbol_right'].')');
		}
		
		return $account_array;
	}
	
	function _set_credit_note_balance($user_id, $user_role, $credit_currency, $update_info) {
		/*******************************************************************
			operator = +, -, and =
		*******************************************************************/
		$sql_update_array = array();
		
		// Generate the update sql
		for ($update_cnt=0; $update_cnt < count($update_info); $update_cnt++) {
			if ( $update_info[$update_cnt]['field_name'] == 'store_account_credit_note_amount' ) {
				$update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
				switch($update_info[$update_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
		if (count($sql_update_array)) {
			$sql_update_array[] = ' store_account_last_modified = now() ';
			
			$update_sql_str = " SET " . implode(', ', $sql_update_array);
	    	
	    	/*************************************************************************
			 	Lock the TABLE_STORE_ACCOUNT_BALANCE 
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");
	    	
	    	$store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . 
	    										$update_sql_str . " 
												WHERE user_id = '" . tep_db_input($user_id) . "' 
													AND user_role = '" . tep_db_input($user_role) . "' 
													AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			tep_db_query($store_acc_balance_update_sql);
			
			$new_balance_select_sql = "	SELECT store_account_credit_note_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($user_id) . "' 
											AND user_role = '" . tep_db_input($user_role) . "' 
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			$new_balance_result_sql = tep_db_query($new_balance_select_sql);
			$new_balance_row = tep_db_fetch_array($new_balance_result_sql);
			
			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
			********************************************************************/
			
			return $new_balance_row['store_account_credit_note_amount'];
		}
		
		return false;
	}
	
	function _get_user_particulars($user_id, $user_role) {
		$user_info_row = array();
		if ($user_role == 'supplier') {
			$user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_gender AS gender, 'S' as sign_up_from, supplier_disable_withdrawal as disable_withdrawal, '' as flags 
										FROM " . TABLE_SUPPLIER . " 
										WHERE supplier_id = '" . tep_db_input($user_id) . "'";
		} else {
			$user_info_select_sql = "	SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email, customers_gender AS gender , ci.customers_info_account_created_from AS sign_up_from, customers_disable_withdrawal as disable_withdrawal, customers_flag as flags 
										FROM " . TABLE_CUSTOMERS . " AS c 
										LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
											ON c.customers_id=ci.customers_info_id
										WHERE customers_id = '" . tep_db_input($user_id) . "'";
		}
		
		$user_info_result_sql = tep_db_query($user_info_select_sql);
		$user_info_row = tep_db_fetch_array($user_info_result_sql);
		
		if ($user_info_row['sign_up_from'] == '0') {
			$user_info_row['sign_up_from'] = 'C';
		} else if ($user_info_row['sign_up_from'] == '1') {
			$user_info_row['sign_up_from'] = 'CN';
		} else if ($user_info_row['sign_up_from'] == '3') {
			$user_info_row['sign_up_from'] = 'PO';
		}
		
		if ($user_info_row['flags'] != '') {
			$user_flags = explode(',', $user_info_row['flags']);
			$user_info_row['flags'] = $user_flags;
		} else {
			$user_info_row['flags'] = array();
		}
		
		return $user_info_row;
	}
	
	function _is_within_daily_limit($add_amount) {
		$action_allowed = false;
		
		$admin_credit_limit_select_sql = "	SELECT admin_credit_limit_total, admin_credit_limit_max 
											FROM " . TABLE_ADMIN_CREDIT_LIMIT . " 
											WHERE admin_id = " . tep_db_input($this->identity);
		$admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);
		
		if ($admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql)) {
			if ($admin_credit_limit_row['admin_credit_limit_total'] + $add_amount <= $admin_credit_limit_row['admin_credit_limit_max']) {
				$action_allowed = true;
				
				$admin_credit_limit_update_sql = "	UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . " 
													SET admin_credit_limit_total = admin_credit_limit_total + " . $add_amount . " 
													WHERE admin_id = " . tep_db_input($this->identity);
				tep_db_query($admin_credit_limit_update_sql);
			}
		}
		
		return $action_allowed;
	}
}
?>