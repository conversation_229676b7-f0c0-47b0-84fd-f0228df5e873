<?php

class csv_manager {
    var $module, $export_data, $pm_with_conversion;
	
	// class constructor
    function csv_manager($module) {
      	$this->module = $module;
      	$this->export_data = array();
      	$this->conversion_data = array();
      	$this->pm_with_conversion = array('wp', 'mb', 'wu');
	}
	
	function show_converter_form($filename, $session_name) {
		$converter_form_html = '';
		
		$converter_form_template = array(	array ('id' => 'cashu', "text" => "cashU"),
											array ('id' => 'ipay88', "text" => "iPay88"),
											array ('id' => 'moneybookers', "text" => "Moneybookers"),
											array ('id' => 'paypal', "text" => "PayPal"),
											array ('id' => 'webmoney', "text" => "WebMoney"),
											array ('id' => 'wu', "text" => "Western Union"),
											array ('id' => 'worldpay', "text" => "WorldPay"),
											array ('id' => 'co', "text" => "Customer Order Activities")
										);
		
		ob_start();
?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('converter_form', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=convert', 'post', 'enctype="multipart/form-data"')?>
        						<table border="0" cellspacing="0" cellpadding="2">
									<tr>
	          							<td width="31%" class="main"><?=ENTRY_CSV_MANAGER_TEMPLATE?></td>
	          							<td class="main"><?=tep_draw_pull_down_menu("csv_template", $converter_form_template, tep_not_null($_SESSION[$session_name]["csv_template"]) ? $_SESSION[$session_name]["csv_template"] : '', 'id="csv_template" onChange="if (this.options[this.selectedIndex].value == \'co\') expand_and_collapse(\'PM\', \'show\'); else expand_and_collapse(\'PM\', \'hide\');"')?></td>
									</tr>
									<tr>
	            						<td colspan="2"></td>
	          						</tr>
									<tr>
	          							<td class="main"><?=ENTRY_CSV_MANAGER_SOURCE_FILE?></td>
	          							<td class="main"><?=tep_draw_file_field('csv_import', 'size="50"')?></td>
									</tr>
									<tr>
	            						<td colspan="2"></td>
	          						</tr>
	          						<tr>
	            						<td colspan="2">
	            							<div class="font-size:11px;" id="PM_short"></div>
	            							<div id="PM_long" style="display:none;">
	            								<table border="0" width="100%" cellspacing="0" cellpadding="2">
		            								<tr>
					          							<td class="main" width="31%"><?=ENTRY_CSV_MANAGER_WP_SOURCE_FILE?></td>
					          							<td class="main"><?=tep_draw_file_field('wp_csv_import', 'size="50"')?></td>
													</tr>
													<tr>
					          							<td class="main"><?=ENTRY_CSV_MANAGER_MB_SOURCE_FILE?></td>
					          							<td class="main"><?=tep_draw_file_field('mb_csv_import', 'size="50"')?></td>
													</tr>
													<tr>
					          							<td class="main"><?=ENTRY_CSV_MANAGER_WU_SOURCE_FILE?></td>
					          							<td class="main"><?=tep_draw_file_field('wu_csv_import', 'size="50"')?></td>
													</tr>
												</table>
	            							</div>
	            						</td>
	          						</tr>
	          						<tr>
	          							<td></td>
			    						<td>
		  									<?=tep_submit_button(BUTTON_GO, ALT_BUTTON_GO, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script language="javascript"><!--
				function form_checking(form_obj, action) {
				    //form_obj.submit();
					return true;
	    		}
	    	//-->
			</script>
<?
		$converter_form_html = ob_get_contents();
		ob_end_clean() ;
		
		return $converter_form_html;
	}
	
	function do_csv_convert($filename, $session_name, $input_array, &$messageStack) {
		if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
			if ($_FILES['csv_import']["size"] > 0) {
				$import_error = false;
				
				$filename = ($_FILES['csv_import']['tmp_name']);  
			    $handle = fopen($filename, 'r+');
			    
			    $this->read_source_file($handle, $input_array['csv_template']);
			    fclose($handle);
			    
			    if ($this->generate_output_file($handle) === FALSE) {
			    	$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			    }
			} else {
				$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
			}
		} else {
			$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
		}
	}
	
	function read_source_file($handle, $template_id) {
		switch($template_id) {
			case 'worldpay':
				for ($wp_cnt=0; $wp_cnt < 6; $wp_cnt++) {
					$data = fgetcsv($handle, 1024, ',', '"');
				}
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
	    			$payment_status_str = trim($data[3]);
	    			
	    			if ($payment_status_str != 'SETTLED' && $payment_status_str != 'REFUNDED') {
	    				continue;
	    			}
					$date_str = $data[1];
					$gross_amount_str = ($payment_status_str == 'SETTLED' ? ($data[10]+$data[12]) : $data[12]);
					$fees_amount_str = $data[10];
					$net_amount_str = $data[12];
					$order_id_str = $data[8];
					/*
		    		$this->export_data[] = array(	'code' => '3021/200',
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => $net_amount_str,
			    									'credit' => ''
			    								);
			    	*/
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'paypal':
				$data = fgetcsv($handle, 1024, ',', '"');
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					$date_str = $data[2];
					$gross_amount_str = $data[19];
					$fees_amount_str = preg_replace('/[^\d\-\.]/', '', $data[33]);
					$net_amount_str = $data[34];
					$order_id_str = $data[6];
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'cashu':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					$date_str = $data[2];
					$gross_amount_str = $data[4];
					$fees_amount_str = $data[5];
					$net_amount_str = (double)$data[4]-(double)$data[5];
					$order_id_str = $data[7];
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'webmoney':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if ($data[2]!='2') {
						continue;
					}
					$date_str = $data[3];
					$gross_amount_str = $data[4];
					$fees_amount_str = 0;
					$net_amount_str = $data[4];
					$order_id_str = trim(str_replace('Purchase from OffGamers', '', $data[7]));
					
					$date_array = explode('T', $date_str);
					if (tep_not_null($date_array[0])) {
						list($year, $month, $day) = explode('-', $date_array[0]);
						$date_str = sprintf("%02d", $day).'/'.sprintf("%02d", $month).'/'.$year;
					}
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => 'Payment receive from customer',
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'moneybookers':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if ( (trim($data[2]) != 'Receive Money' && trim($data[2]) != 'Receive Money Cancellation' && trim($data[2]) != 'Withdraw') 
						|| (trim($data[2]) != 'Receive Money Cancellation' && trim($data[3]) != 'Fee')) {
						continue;
					}
					
					$data2 = fgetcsv($handle, 1024, ',', '"');
					
					$activity_desc = trim($data[2]);
					$date_str = $data[1];
					$gross_amount_str = $data2[9];
					$fees_amount_str = $data[4];
					$net_amount_str = (double)$data2[9]-(double)$data[4];
					$order_id_str = $data2[8];
					
					if (!tep_not_null($order_id_str) && $activity_desc != 'Withdraw') {
						continue;
					}
					
					if ($activity_desc == 'Receive Money Cancellation') {
						$gross_amount_str = $data2[9];
						$fees_amount_str = $data[5];
						
						$gross_amount_str = '-' . $gross_amount_str;
					} else if ($activity_desc == 'Withdraw') {
						$gross_amount_str = $data2[9];
					}
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'ipay88':
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if ( !is_numeric(trim($data[3])) || trim($data[11]) != 'Success' ) {
						continue;
					}
					
					$activity_desc = 'Payment receive from customer';
					$date_str = $data[1];
					$gross_amount_str = $data[7];
					$fees_amount_str = $data[8];
					$net_amount_str = $data[10];
					$order_id_str = $data[3];
					
					$date_array = explode(' ', $date_str);
					if (tep_not_null($date_array[0])) {
						list($day, $month, $year) = explode('-', $date_array[0]);
						$date_str = sprintf("%02d", $day).'/'.sprintf("%02d", $month).'/'.$year;
					}
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'wu':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					$activity_desc = 'Payment receive from customer';
					$date_str = $data[0];
					$gross_amount_str = $data[3];
					$fees_amount_str = '0.00';
					$net_amount_str = $data[3];
					$order_id_str = $data[1];
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => abs($fees_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc,
			    									'debit' => '',
			    									'credit' => abs($fees_amount_str)
			    								);
				}
				
				break;
			case 'co':
				$this->get_conversion_rate();
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if(trim($data[0])=='Date/Time'){
						break;
					} else {
						continue;
					}
				}
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if (!tep_not_null($data[1])) {
						continue;
					}
					$date_str = $data[0];
					$gross_amount_str = $data[8];
					$fees_amount_str = 0;
					$sc_amount_str = (double)$data[12] + (double)$data[13];
					$net_amount_str = $data[8];
					$order_id_str = $data[2];
					$activity_desc = tep_db_prepare_input(preg_replace('/([^\+\-]+)(.*)/', '$1', preg_quote($data[1])));
					
					if (isset($this->conversion_data[$order_id_str]) && tep_not_null($this->conversion_data[$order_id_str])) {
						$gross_amount_str *= $this->conversion_data[$order_id_str];
						$sc_amount_str *= $this->conversion_data[$order_id_str];
						$net_amount_str *= $this->conversion_data[$order_id_str];
					}
					
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc . ' order amount',
			    									'debit' => $gross_amount_str,
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '4400/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc . ' order amount',
			    									'debit' => '',
			    									'credit' => $gross_amount_str
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3200/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc . ' order amount',
			    									'debit' => abs($sc_amount_str),
			    									'credit' => ''
			    								);
			    	
			    	$this->export_data[] = array(	'code' => '3100/100',
			    									'period' => 16,
		    										'type' => 16,
			    									'date' => $date_str,
			    									'order_id' => $order_id_str,
			    									'remark' => $activity_desc . ' order amount',
			    									'debit' => '',
			    									'credit' => abs($sc_amount_str)
			    								);
				}
				
				break;
		}
	}
	
	function get_conversion_rate() {
		foreach ($this->pm_with_conversion as $pm_code) {
			$filename = $pm_code . '_' . 'csv_import';
			if ( tep_not_null($_FILES[$filename]['tmp_name']) && ($_FILES[$filename]['tmp_name'] != 'none') && is_uploaded_file($_FILES[$filename]['tmp_name']) ) {
				if ($_FILES[$filename]["size"] > 0) {
					$import_error = false;
					
					$filename = ($_FILES[$filename]['tmp_name']);
				    $handle = fopen($filename, 'r+');
				    
				    $this->read_conversion_file($handle, $pm_code);
				    fclose($handle);
				}
			}
		}
	}
	
	function read_conversion_file($handle, $template_id) {
		switch($template_id) {
			case 'wp':
				for ($wp_cnt=0; $wp_cnt < 6; $wp_cnt++) {
					$data = fgetcsv($handle, 1024, ',', '"');
				}
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
	    			$payment_status_str = trim($data[3]);
	    			
	    			if ($payment_status_str != 'SETTLED' && $payment_status_str != 'REFUNDED') {
	    				continue;
	    			}
					
					$order_id_str = $data[8];
					$org_amount_str = (double)$data[5];
					$settled_amount_str = (double)$data[10] + (double)$data[12];
					
					if ($org_amount_str > 0) {
						$this->conversion_data[$order_id_str] = ($settled_amount_str / $org_amount_str);
					}
				}
				break;
			case 'mb':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					if ( trim($data[2]) != 'Receive Money' ) {
						continue;
					}
					
					$data2 = fgetcsv($handle, 1024, ',', '"');
					
					$order_id_str = $data2[8];
					$org_amount_str = (double)$data[9];
					$settled_amount_str = (double)$data[5];
					
					if ($org_amount_str > 0) {
						$this->conversion_data[$order_id_str] = ($settled_amount_str / $org_amount_str);
					}
				}
				
				break;
			case 'wu':
				$data = fgetcsv($handle, 1024, ',', '"');
				
				while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					$order_id_str = $data[1];
					$org_amount_str = (double)$data[1];
					$settled_amount_str = (double)$data[5];
					
					if ($org_amount_str > 0) {
						$this->conversion_data[$order_id_str] = ($settled_amount_str / $org_amount_str);
					}
				}
				
				break;
		}
	}
	
	function generate_output_file() {
		$export_csv_data = '';
		
		if (count($this->export_data)) {
			foreach ($this->export_data as $res) {
				$code_format_str = '"' . str_replace('"', '""', $res['code']) . '"';
				$period_format_str = '"' . str_replace('"', '""', $res["period"]) . '"';
				$type_format_str = '"' . str_replace('"', '""', $res["type"]) . '"';
				$date_format_str = '"' . str_replace('"', '""', $res["date"]) . '"';
				$order_id_format_str = '"' . str_replace('"', '""', $res['order_id']) . '"';
				$remark_format_str = '"' . str_replace('"', '""', $res['remark']) . '"';
				$debit_format_str = '"' . str_replace('"', '""', $res['debit']) . '"';
				$credit_format_str = '"' . str_replace('"', '""', $res['credit']) . '"';
				
				$export_csv_data .= "$code_format_str,$period_format_str,$type_format_str,$date_format_str,$order_id_format_str,,$remark_format_str,,$debit_format_str,$credit_format_str";
				
				$export_csv_data .= "\n";
			}
		}
		
		if (tep_not_null($export_csv_data)) {
			$filename = 'glpost9.csv';
			$mime_type = 'text/x-csv';
			// Download
	        header('Content-Type: ' . $mime_type);
	        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
	        // IE need specific headers
	        if (PMA_USR_BROWSER_AGENT == 'IE') {
	            header('Content-Disposition: inline; filename="' . $filename . '"');
	            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	            header('Pragma: public');
	        } else {
	            header('Content-Disposition: attachment; filename="' . $filename . '"');
	            header('Pragma: no-cache');
	        }
			echo $export_csv_data;
			exit();
		} else {
			return false;
		}
	}
}
?>