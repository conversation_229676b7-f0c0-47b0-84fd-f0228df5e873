<?php
class curl {
    private $error_array = array();
    public $ssl_verification = false;
    public $connect_via_proxy = false;
    public $http_header = array('Expect:');
    public $custom_request_type = false;
    public $http_status = null;
    public $timeout = 120;

    function curl_request($method, $url, $header = array(), $post_data = '', $fail_on_error = true)
    {
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        //reset error status
        $this->error_array = array();
        $this->http_status = null;
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        //fail on http error
        curl_setopt($ch, CURLOPT_FAILONERROR, $fail_on_error);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        if(!empty($post_data)){
            curl_setopt($ch,CURLOPT_POSTFIELDS,$post_data);
        }
        if ($this->ssl_verification) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        } else {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

        if ($this->connect_via_proxy) {
            if (defined("HTTP_PROXY")) {
                // Fix PHP 5.4 empty() not accepting constrain / function
                $proxy = HTTP_PROXY;
                if (!empty($proxy)) {
                    curl_setopt($ch, CURLOPT_PROXY, $proxy);
                }
            }
        }

        $response = curl_exec($ch);
        $this->http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);

        return $response;
    }

    function curl_get($url, $data='', $filename='') {
        $getfields = '';

        if (tep_not_null($data)) {
            if (is_array($data)) {
                while (list($key, $value) = each($data)) {
                    $getfields .= $key . '=' . urlencode($value) . '&';
                }
            } else {
                $getfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        $ch = curl_init($url . '?' . $getfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://my-proxy.offgamers.lan:3128');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);    // false

        $response = curl_exec($ch);

        if($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);

        return $response;
    }

	function curl_post($url, $data, $filename='') {
        $ch = curl_init($url);

        $postfields = '';
        if (is_array($data)) {
            while (list($key, $value) = each($data)) {
                $postfields .= $key . '=' . urlencode($value) . '&';
            }
        } else {
            $postfields = $data;
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        if ($this->custom_request_type) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $this->custom_request_type);
        }

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        if (tep_not_null($filename)) {
      		$fp = fopen ($filename, 'w+');
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }

        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER,  $this->http_header);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://my-proxy.offgamers.lan:3128');
        }

        // Verification of the SSL cert
        if ($this->ssl_verification) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); // true
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2
        } else {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);    // false
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2   // should be false
        }

        $response = curl_exec($ch);

        if($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);
        if (tep_not_null($filename)) {
            fclose($fp);
        }

        return $response;
    }
    
    // curl_g2g_serverless
    function curl_post_serverless($url, $type = "POST", $data = "", $header = null, $retryCount = 0) {

        $ch = curl_init($url);
        $opts = array(
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_POST => true,
        );
        curl_setopt_array($ch, $opts);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        // if(!isset($header['Authorization']) || empty($header['Authorization'])){
        //     $this->g2g_slack('Unable to retrieve serverles token required for calling endpoint'. $url . ' Params: '.json_encode($data). ' Header: '.json_encode($header) );
        //     return false;
        // }
        if ($header) {
            $httpHeader = [];
            foreach ($header as $key => $value) {
                $httpHeader[] = $key . ': ' . $value;
            }
            if (!empty($httpHeader)) {
                curl_setopt($ch, CURLOPT_HTTPHEADER, $httpHeader);
            }
        }

        $retryflag = true;
        $result = null;
        while($retryCount < 3 && $retryflag){
            $retryflag = false;
            try{
                $result = curl_exec($ch);
                $status_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);
                if($status_code >= 500 && $status_code <=599 ){
                    $retryflag = true;
                    throw new Exception('Response code is '.$status_code);
                }
                if($status_code == 403 || $status_code == 400 || $status_code == 401){
                    $retryflag = false;
                    throw new Exception('Response code is '.$status_code);
                }
                $result = json_decode($result, true);
                if($result == null || $result == ''){
                    $retryflag = true;
                    throw new Exception('Request timeout');
                }
                curl_close($ch);
            } catch (Exception $e){
                $this->g2g_slack($e->getMessage(). ' '. 'Endpoint: '. $url . ' Params: '.json_encode($data). ' Header: '.json_encode($header). ' Response:'.$result . ' Retry: '. $retryCount);
                $retryCount++;
                sleep($retryCount);
            }
        }
        return $result;
    }
    
    private function g2g_slack($text){
        
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] - Serverless ' . date("F j, Y H:i"),
            'channel' => G2G_SLACK_WEBHOOK_DEV_DEBUG_CHANNEL,
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $text
                )
            )
        ));
        $slack->send(G2G_SLACK_WEBHOOK_DEV_DEBUG_ID, $data);
    }

    public function get_error() {
        return $this->error_array;
    }

    private function set_error($response, $errorCode, $errorMessage='') {
        $this->error_array = array (
            'response' => $response,
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        );
    }
    
    private function isJson($params) {
        return (json_decode($params, true) !== NULL) ? true : false;
    }
}
?>