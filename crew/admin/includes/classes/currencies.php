<?

/*
  $Id: currencies.php,v 1.23 2016/04/21 09:08:22 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

////
// Class to handle currencies
// TABLES: currencies
class currencies {

    var $currencies;
    var $internal_currencies;
    var $decimal_places;
    var $show_currency_symbol;

    // class constructor
    function currencies() {
        $this->show_currency_symbol = true;
        $this->decimal_places = null;
        $this->currencies = array();
        $this->internal_currencies = array();

        $currencies_query = tep_db_query("select currencies_id, code, title, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, value, buy_value, sell_value, currencies_used_for from " . TABLE_CURRENCIES . " order by title ASC");
        while ($currencies = tep_db_fetch_array($currencies_query)) {
            $this->currencies[$currencies['code']] = array('id' => $currencies['currencies_id'],
                'title' => $currencies['title'],
                'symbol_left' => $currencies['symbol_left'],
                'symbol_right' => $currencies['symbol_right'],
                'decimal_point' => $currencies['decimal_point'],
                'thousands_point' => $currencies['thousands_point'],
                'decimal_places' => $currencies['decimal_places'],
                'value' => $currencies['value'],
                'buy_value' => $currencies['buy_value'],
                'sell_value' => $currencies['sell_value'],
                'use_for' => explode(',', $currencies['currencies_used_for']));

            $this->internal_currencies[$currencies['currencies_id']] = $currencies['code'];
        }
    }

    // class methods
    function format($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = '') {
        if ($calculate_currency_value) {
            $rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);

            // added by subrat
            if (is_null($this->decimal_places))
                $format_string = ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_left'] : '') . number_format($number * $rate, $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_right'] : '');
            else
                $format_string = ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_left'] : '') . number_format(tep_round($number * $rate, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_right'] : '');
            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if ((DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places))
                $format_string = ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_left'] : '') . number_format($number, $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_right'] : '');
            else
                $format_string = ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_left'] : '') . number_format(tep_round($number, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . ($this->show_currency_symbol ? $this->currencies[$currency_type]['symbol_right'] : '');
        }
        return $format_string;
    }

    // class methods
    function format_round_down($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = '') {
        if ($calculate_currency_value) {
            $rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);

            // added by subrat
            if (is_null($this->decimal_places))
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number * $rate, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            else
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number * $rate, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if ((DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format_round_down($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places))
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            else
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int) $this->decimal_places)), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
        }
        return $format_string;
    }

    function custom_format($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $decimal_places = false, $decimal_point = false, $thousand_point = false, $type = '') {
        return number_format($number, $decimal_places !== FALSE ? $decimal_places : $this->currencies[$currency_type]['decimal_places'], $decimal_point !== FALSE ? $decimal_point : $this->currencies[$currency_type]['decimal_point'], $thousand_point !== FALSE ? $thousand_point : $this->currencies[$currency_type]['thousands_point']);
    }

    function _currency_round_down($val, $rounder) {
        if ($rounder != 0) {
            return floor($val * $rounder) / $rounder;
        } else {
            return $val;
        }
    }

    function apply_currency_exchange($number, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = '') {
        $rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);

        return tep_round($number * $rate, is_null($this->decimal_places) ? $this->currencies[$currency_type]['decimal_places'] : $this->decimal_places);
    }

    function advance_currency_conversion($number, $from_currency_type = DEFAULT_CURRENCY, $to_currency_type = DEFAULT_CURRENCY, $round = true, $type = 'sell') {
        $rate = $this->advance_currency_conversion_rate($from_currency_type, $to_currency_type, $type);

        if ($round) {
            return tep_round($number * $rate, is_null($this->decimal_places) ? $this->currencies[$to_currency_type]['decimal_places'] : $this->decimal_places);
        } else {
            return $number * $rate;
        }
    }

    function advance_currency_conversion_rate($from_currency_type = DEFAULT_CURRENCY, $to_currency_type = DEFAULT_CURRENCY, $type = 'sell') {
        if ($from_currency_type == $to_currency_type) {
            return tep_round(1, 8);
        }

        if ($type == 'sell') {
            if ($to_currency_type == DEFAULT_CURRENCY) {
                $rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? (double) ($this->currencies[$to_currency_type]['buy_value'] / $this->currencies[$from_currency_type]['sell_value']) : 0;
            } else {
                $rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['buy_value'] / $this->currencies[$from_currency_type]['value']) : 0;
            }
        } elseif($type == 'spot') {
            $rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['value'] / $this->currencies[$from_currency_type]['value']) : 0;
        } else {
            if ($to_currency_type == DEFAULT_CURRENCY) {
                $rate = ($this->currencies[$from_currency_type]['buy_value'] > 0) ? (double) ($this->currencies[$to_currency_type]['sell_value'] / $this->currencies[$from_currency_type]['buy_value']) : 0;
            } else {
                $rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['sell_value'] / $this->currencies[$from_currency_type]['value']) : 0;
            }
        }

        return tep_round($rate, 8);
    }

    function currency_history_rate_conversion($from_currency, $to_currency, $date) {
        if ($from_currency <> $to_currency) {
            $from = $to = 1;
            $from_rate = $this->get_currency_history_rate($from_currency, $date);
            $to_rate = $this->get_currency_history_rate($to_currency, $date);

            if (isset($from_rate['spot']) && isset($to_rate['spot'])) {
                $from = $from_rate['spot'];
                $to = $to_rate['spot'];
            } else {
                $from = $this->currencies[$from_currency]['value'];
                $to = $this->currencies[$to_currency]['value'];
            }
            return number_format(bcdiv($to, $from, 8), 8, '.', '');
        } else {
            return $from_currency;
        }
    }

    function is_set($code) {
        if (isset($this->currencies[$code]) && tep_not_null($this->currencies[$code])) {
            return true;
        } else {
            return false;
        }
    }

    function get_value($code, $type = '') {
        if (tep_not_null($type)) {
            return $this->currencies[$code][$type . '_value'];
        } else {
            return $this->currencies[$code]['value'];
        }
    }

    function get_decimal_places($code = '') {
        if (!tep_not_null($code))
            $code = DEFAULT_CURRENCY;
        return $this->currencies[$code]['decimal_places'];
    }

    function get_code_by_id($cur_id) {
        return $this->internal_currencies[$cur_id];
    }

    function get_id_by_code($cur_code) {
        return (int) array_search($cur_code, $this->internal_currencies);
    }

    function get_currency_set($usage = '') {
        $currency_set_array = array();

        if (count($this->currencies)) {
            foreach ($this->currencies as $code => $currency_info) {
                if (tep_not_null($usage)) {
                    if (in_array(strtoupper($usage), $currency_info['use_for'])) {
                        $currency_set_array[] = array('id' => $code, 'text' => $currency_info['title'] . ' (' . $currency_info['symbol_left'] . $currency_info['symbol_right'] . ')');
                    }
                } else {
                    $currency_set_array[] = array('id' => $code, 'text' => $currency_info['title'] . ' (' . $currency_info['symbol_left'] . $currency_info['symbol_right'] . ')');
                }
            }
        }

        return $currency_set_array;
    }

    function get_currency_history_rate($code, $date) {
        $result = false;

        $sql = "SELECT buy_value AS buy, spot_value AS spot, sell_value AS sell
                FROM " . TABLE_CURRENCIES_HISTORY . " 
                WHERE date_from <= '$date' 
                    AND code = '$code' 
                ORDER BY version DESC";
        $res = tep_db_query($sql);
        if ($row = tep_db_fetch_array($res)) {
            $result = $row;
        } else {
            $sql = "SELECT buy_value AS buy, spot_value AS spot, sell_value AS sell
                FROM " . TABLE_CURRENCIES_HISTORY . " 
                WHERE code = '$code' 
                ORDER BY version DESC";
            $res = tep_db_query($sql);
            if ($row = tep_db_fetch_array($res)) {
                $result = $row;
            }
        }
        return $result;
    }

    function display_price($products_price, $products_tax, $quantity = 1) {
        return $this->format(tep_add_tax($products_price, $products_tax) * $quantity);
    }

    function set_decimal_places($decimal_place) {
        $decimal_place = (int) $decimal_place;

        if ($decimal_place <= 0)
            $this->decimal_places = null;
        else
            $this->decimal_places = $decimal_place;
    }

    function get_product_prices_info($products_id) {
        $base_price_info_select_sql = "	SELECT products_price, products_base_currency 
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . tep_db_input($products_id) . "'";
        $base_price_info_result_sql = tep_db_query($base_price_info_select_sql);
        $base_price_info_row = tep_db_fetch_array($base_price_info_result_sql);

        $product_price_array = array('price' => $base_price_info_row['products_price'],
            'base_cur' => $base_price_info_row['products_base_currency'],
            'defined_price' => array()
        );

        $defined_prices_select_sql = "	SELECT products_currency_prices_value, products_currency_prices_code 
										FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
										WHERE products_id = '" . tep_db_input($products_id) . "'";
        $defined_prices_result_sql = tep_db_query($defined_prices_select_sql);

        while ($defined_prices_row = tep_db_fetch_array($defined_prices_result_sql)) {
            $product_price_array['defined_price'][$defined_prices_row['products_currency_prices_code']] = $defined_prices_row['products_currency_prices_value'];
        }

        return $product_price_array;
    }

    function set_show_currency_symbol() {
        $this->show_currency_symbol = true;
    }

    function set_hide_currency_symbol() {
        $this->show_currency_symbol = false;
    }

    //Simulate $currencies->format() but don't append currency symbols.
    function do_raw_conversion($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = 'buy') {
        $raw_number = $number;
        if ($calculate_currency_value) {
            if (tep_not_null($currency_value)) {
                $rate = $currency_value;
            } else {
                $rate = $this->get_value($currency_type, $type);
            }

            $raw_number = $number * $rate;
        }
        return $raw_number;
    }

    function parseStringToNumber($currency, $value)
    {
        $value = str_replace($this->currencies[$currency]['symbol_left'], '', $value);
        $value = str_replace($this->currencies[$currency]['symbol_right'], '', $value);
        $value = trim(strip_tags($value));
        $value = str_replace($this->currencies[$currency]['thousands_point'], '', $value);
        return (float) str_replace($this->currencies[$currency]['decimal_point'], '.', $value);
    }

    function get_sc_rebate($customer_id, $products_id, $products_price, $checkout_currency, $quantity = 1, $payment_methods_id = 0) {
        if ($customer_id) {
            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
            $customers_groups_rebate = abs($customers_groups_info_array['cust_group_rebate']);

            $customers_discount_select_sql = "	SELECT customers_discount 
         										FROM " . TABLE_CUSTOMERS . " 
         										WHERE customers_id =  '" . (int) $customer_id . "'";
            $query = tep_db_query($customers_discount_select_sql);
            $query_result = tep_db_fetch_array($query);
            $customer_discount = $query_result['customers_discount'];

            $customer_discount = $customer_discount + $customers_groups_discount;
        } else {
            $customers_groups_info_array = tep_get_customer_group_discount(0, $products_id, 'product');
            $customer_discount = $customers_groups_info_array['cust_group_discount'];
            $customers_groups_rebate = abs($customers_groups_info_array['cust_group_rebate']);
        }

        if ($customer_discount >= 0) {
            $products_price = $products_price + $products_price * abs($customer_discount) / 100;
        } else {
            $products_price = $products_price - $products_price * abs($customer_discount) / 100;
        }

        $cust_group_extra_rebate = 0;
        if ($payment_methods_id > 0 && isset($customers_groups_info_array['cust_group_discount_id']) && (int) $customers_groups_info_array['cust_group_discount_id'] > 0) {
            $customers_groups_extra_op_select_sql = "	SELECT bonus_op 
														FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . "
														WHERE customers_groups_discount_id = '" . (int) $customers_groups_info_array['cust_group_discount_id'] . "'
															AND payment_methods_id = '" . (int) $payment_methods_id . "'
															AND (currency = '" . tep_db_input($checkout_currency) . "'
																OR currency = '*')";
            $customers_groups_extra_op_result_sql = tep_db_query($customers_groups_extra_op_select_sql);
            if ($customers_groups_extra_op_row = tep_db_fetch_array($customers_groups_extra_op_result_sql)) {
                $cust_group_extra_rebate = abs($customers_groups_extra_op_row['bonus_op']);
            }
        }

        $price_convert = number_format(($products_price * $quantity) / $this->currencies[$checkout_currency]['value'], 2, '.', '');

        $rebate_point = floor($price_convert * $customers_groups_rebate);
        $rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
        $rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ') / ' . $this->currencies[$checkout_currency]['value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;

        return array($rebate_point, $rebate_point_extra, $rebate_point_formula);
    }

    function display_product_price_format($products_id, $products_price, $quantity, $currency) {
        global $customer_id;

        $calculate_currency_value = true;
        $rate = 1;

        if (tep_not_null($products_id)) {
            $product_price_array = $this->get_product_prices_info($products_id);
            $product_type = tep_get_custom_product_type($products_id);

            if ($product_type == 3) { // SC Product Type
                $calculate_currency_value = false;
                $store_credit_currency_code = ms_store_credit::getScCurrency($customer_id, false);

                $products_price = $product_price_array['price'];

                if ($store_credit_currency_code != $currency) {
                    $products_price = $this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy');
                }
            } else {
                // Get percentage if price value <>
                if (isset($this->currencies[$product_price_array['base_cur']])) {
                    if ($product_price_array['base_cur'] == $currency) {
                        $calculate_currency_value = false;
                    } else if (isset($product_price_array['defined_price'][$currency])) {
                        $products_price = $product_price_array['defined_price'][$currency];
                        $calculate_currency_value = false;
                    } else {
                        if ($currency == DEFAULT_CURRENCY) {
                            // use spot rate to have better selling price (business decision)
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        } else {
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        }
                    }
                }
            }
        }

        if ($calculate_currency_value) {
            $rate = $this->get_value($currency);
        }

        return number_format($products_price * $quantity * $rate, $this->currencies[$currency]['decimal_places'], $this->currencies[$currency]['decimal_point'], $this->currencies[$currency]['thousands_point']);
    }

}

?>