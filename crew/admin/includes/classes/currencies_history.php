<?php

class currencies_history {

    private $id_mapping_array = array();

    public function getCurrenciesHistoryId($id, $params) {
        if (isset($this->id_mapping_array[$id])) {
            $return_id = $this->id_mapping_array[$id];
        } else {
            $return_id = $this->checkRecordAndUpdateVersion($id, 'id', $params);
            $this->id_mapping_array[$id] = $return_id;
        }

        return $return_id;
    }

    private function checkRecordAndUpdateVersion($id, $pk, $params = array()) {
        # is ID exist

        $currencies_query = tep_db_query("select * from " . TABLE_CURRENCIES_HISTORY . "  WHERE currencies_id = '" . tep_db_input($id) . "' ORDER BY version DESC LIMIT 1");
        $current_data = tep_db_fetch_array($currencies_query);

        if ($current_data) {
            # is the content matched? (currency history 's spot_value, buy_value,sell_value)
            $matched_flag = true;
            foreach ($params as $key => $value) {
                if ($current_data[$key] == $value) {
                    // do nothing
                } else {
                    $matched_flag = false;

                    break;
                }
            }
            if ($matched_flag) {
                return $current_data[$pk];
                # return 'primary_id';
            } else {
                # new_version = current_version + 1;
                # update current id's date_to to current date
                # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
                # return new_primary_id;

                $date_var= date('Y-m-d H:i:s');
                $sql_data_array = array('date_to' => $date_var);
                $update_status = tep_db_perform(TABLE_CURRENCIES_HISTORY, $sql_data_array, 'update', "currencies_id = '" . (int) $id . "' AND $pk ='$current_data[$pk]'");

                if ($update_status) {
                    $sql_data_array = array('currencies_id' => $id, 'date_from' => $date_var, 'date_to' => '2999-12-31 23:59:59', 'version' => $current_data['version'] + 1, 'last_modified' => date('Y-m-d H:i:s'));
                    tep_db_perform(TABLE_CURRENCIES_HISTORY, array_merge($params, $sql_data_array));
                    $primary_id = tep_db_insert_id();
                    return $primary_id;
                }
            }
        } else {
            # new_version = 1;
            # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
            # return new_primary_id

            $sql_data_array = array('currencies_id' => $id, 'date_from' => date('Y-m-d H:i:s'), 'date_to' => '2999-12-31 23:59:59', 'version' => 1, 'last_modified' => date('Y-m-d H:i:s'));
            tep_db_perform(TABLE_CURRENCIES_HISTORY, array_merge($params, $sql_data_array));
            $primary_id = tep_db_insert_id();
            return $primary_id;
        }

        return 0;
    }

}

?>