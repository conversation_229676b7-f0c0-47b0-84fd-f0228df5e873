<?
class custom_product
{
	var $info, $totals, $products, $supplier, $payment, $custom_product_id;
	
    function custom_product($custom_product_id='')
    {
      	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->supplier = array();
		$this->payment = array();
		
		if (tep_not_null($custom_product_id)) {
			$this->custom_product_id = $custom_product_id;
      		$this->query($custom_product_id);
    	}
    }
    
    function query($custom_product_id)
    {
    	$custom_product_select_sql = "SELECT op.*, sta,* FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (op.orders_products_id = sta.orders_products_id) WHERE sta.orders_products_id='" . (int)$custom_product_id . "'" ;
    	$custom_product_result_sql = tep_db_query($custom_product_select_sql);
    	$custom_product_row = tep_db_fetch_array($custom_product_result_sql);
    	
    	$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_gender, s.supplier_dob, s.supplier_date_account_created AS date_created, s.supplier_fax, 
												s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $custom_product_row["suppliers_id"] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
    	
    	$this->info =     array('currency_value' => $custom_product_row["final_price"],
    							'product_name' => $custom_product_row["product_name"],
    							'custom_products_type_id' => $custom_product_row["custom_products_type_id"],
    							'products_categories_id' => $custom_product_row["products_categories_id"]);
    	
    	$this->supplier = array('id' => $order['suppliers_id'],
      							'name' => $order['suppliers_name'],
                              	'street_address' => $order['suppliers_street_address'],
                              	'suburb' => $order['suppliers_suburb'],
                              	'city' => $order['suppliers_city'],
                              	'postcode' => $order['suppliers_postcode'],
                              	'state' => $order['suppliers_state'],
                              	'country' => $order['suppliers_country'],
                              	'format_id' => 1,
                              	'telephone' => $order['suppliers_telephone'],
                              	'fax' => $supplier_personal_row['supplier_fax'],
                              	'email_address' => $order['suppliers_email_address'],
                              	'gender' => $supplier_personal_row['supplier_gender'],
                              	'dob' => $supplier_personal_row['supplier_dob'],
                              	'date_account_created' => $supplier_personal_row['date_created'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment = array(	'paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              	'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
	}

	function get_order_total_payable_amount($custom_product_id='') {
		$payable_price = 0;
		
		if (!tep_not_null($custom_product_id)) {
			$custom_product_id = $this->custom_product_id;
		}
		
		$custom_product_info_select_sql = "SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price FROM " . TABLE_ORDERS_PRODUCTS . " AS op INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta ON (op.orders_products_id = sta.orders_products_id) WHERE op.orders_products_id ='" . (int)$custom_product_id . "'";
		$custom_product_info_result_sql = tep_db_query($custom_product_info_select_sql);
		$custom_product_info_row = tep_db_fetch_array($custom_product_info_result_sql);
		
		$payable_price = $custom_product_info_row["payable_price"];
		
		return $payable_price;
	}
	
	function progress_report_statistic($status_name_array)
	{
		$total_tasks_array = array();
		
		$supplier_id_select_sql = " SELECT s.supplier_id, s.supplier_code, sts.supplier_tasks_allocation_physical_slots 
									FROM " . TABLE_SUPPLIER . " AS s 
									INNER JOIN " . TABLE_SUPPLIER_TASKS_SETTING . " AS sts
										ON (sts.suppliers_id = s.supplier_id AND sts.custom_products_type_id = 1) ";
		
		$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
		
		$progress_report_statistic_html .= '	<table border="0" width="62%" cellspacing="2" cellpadding="2" align="center">
													<tr><td colspan="7" class="ordersBoxHeading" align="center" colspan="2">'.TABLE_HEADING_PROGRESS_REPORT_STATISTIC.'</td></tr>
													<tr>
														<td class="ordersBoxHeading"><b>'.TABLE_HEADING_SUPPLIER.'</b></td>';
		
		for ($status_name_count = 0; $status_name_count < 5; $status_name_count++) {
			$progress_report_statistic_html .= '		<td class="ordersBoxHeading" nowrap align="center"><b>&nbsp;'.$status_name_array[$status_name_count]. (($status_name_count == 4) ? '<br> ('.TEXT_TRANS_NOT_BILLED . ' / ' . TEXT_TRANS_BILLED . ')' : '') .'&nbsp;</b></td>';
		}
		$progress_report_statistic_html .= '			<td class="ordersBoxHeading" nowrap align="center"><b>&nbsp;'.TABLE_HEADING_AVAILABLE_SLOT.'</td>';
		$progress_report_statistic_html .= '		</tr>';
		
		while ($supplier_id_row = tep_db_fetch_array($supplier_id_result_sql)) {
			$supplier_total_tasks = 0;
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
			
			$progress_report_statistic_html .= '	<tr class="'.$row_style.'">
														<td class="main">'.$supplier_id_row['supplier_code'].'</td>';
													
			for ($count_status = 0; $count_status < 5; $count_status++) {
				if ($count_status == 4) {
					$complete_status_unbill_select_sql = "	SELECT COUNT(supplier_tasks_status) AS total_status 
														 	FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
														 	WHERE supplier_tasks_status = '" . (int)$count_status . "' 
														 		AND supplier_tasks_billing_status = 0 
														 		AND suppliers_id ='" . (int)$supplier_id_row['supplier_id'] . "'";
					$complete_status_unbill_result_sql = tep_db_query($complete_status_unbill_select_sql);
					$complete_status_unbill_row = tep_db_fetch_array($complete_status_unbill_result_sql);
					
					$complete_status_bill_select_sql = "	SELECT COUNT(supplier_tasks_status) AS total_status 
														 	FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
														 	WHERE supplier_tasks_status = '" . (int)$count_status . "' 
														 		AND supplier_tasks_billing_status = 1 
														 		AND suppliers_id ='" . (int)$supplier_id_row['supplier_id'] . "'";
					$complete_status_bill_result_sql = tep_db_query($complete_status_bill_select_sql);
					$complete_status_bill_row = tep_db_fetch_array($complete_status_bill_result_sql);
				} else {
					$supplier_tasks_status_select_sql = "	SELECT COUNT(supplier_tasks_status) AS total_status 
														 	FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
														 	WHERE supplier_tasks_status = '" . (int)$count_status . "' 
														 		AND suppliers_id ='" . (int)$supplier_id_row['supplier_id'] . "'";
					$supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
					$supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql);
				}
				
				$progress_report_statistic_html .= ' 	<td class="main" align="center">';
				
				if ($count_status == 4) {
					$total_tasks_array[$count_status]['bill'] += $complete_status_bill_row['total_status'];
					$total_tasks_array[$count_status]['unbill'] += $complete_status_unbill_row['total_status'];
					
					if ($complete_status_unbill_row['total_status'] > 0) {
						$progress_report_statistic_html .=	'<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$count_status.'&sid='.$supplier_id_row['supplier_id'].'&bill=0').'">'.$complete_status_unbill_row['total_status'].'</a>';
					} else {
						$progress_report_statistic_html .=	'<small>'.$complete_status_unbill_row['total_status'].'</small>';
					}
					
					$progress_report_statistic_html .=		' / ';
					
					if ($complete_status_bill_row['total_status'] > 0) {
						$progress_report_statistic_html .=	'<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$count_status.'&sid='.$supplier_id_row['supplier_id'].'&bill=1').'">'.$complete_status_bill_row['total_status'].'</a>';
					} else {
						$progress_report_statistic_html .=	'<small>'.$complete_status_bill_row['total_status'].'</small>';
					}
				} else {
					$total_tasks_array[$count_status] += $supplier_tasks_status_row['total_status'];
					$supplier_total_tasks += $supplier_tasks_status_row['total_status'];
					
					if ((int)$supplier_tasks_status_row['total_status'] > 0) {
						$progress_report_statistic_html .=	'<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$count_status.'&sid='.$supplier_id_row['supplier_id']).'">'.$supplier_tasks_status_row['total_status'].'</a>';
					} else {
						$progress_report_statistic_html .=	'<small>'.$supplier_tasks_status_row['total_status'].'<small>';
					}
				}
				$progress_report_statistic_html .= ' 	</td>';
			}
			
			$progress_report_statistic_html .= ' 		<td class="main" align="center">' . $supplier_total_tasks . '<b> / </b>' . $supplier_id_row['supplier_tasks_allocation_physical_slots'] . '</td>';
			$progress_report_statistic_html .= '	</tr>';
			
			$row_count++;
		}
		
		$progress_report_statistic_html .= '		<tr><td class="main"><b>'.ENTRY_TOTAL.'</b></td>';
		
		foreach ($total_tasks_array as $status_id => $total_status_tasks_count) {
			$progress_report_statistic_html .= '		<td class="main" align="center">';
			if (is_array($total_status_tasks_count)) {
				$progress_report_statistic_html .= '		<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$status_id.'&bill=0').'">'.$total_status_tasks_count['unbill'].'</a>'.' / '.'<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$status_id.'&bill=1').'">'. $total_status_tasks_count['bill'].'</a>';
			} else {
				if ($total_status_tasks_count > 0) {
					$progress_report_statistic_html .= '	<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'action=search&searchby='.$status_id).'">'.$total_status_tasks_count.'</a>';
				} else {
					$progress_report_statistic_html .= '	<small>'.$total_status_tasks_count.'<small>';
				}
			}
			$progress_report_statistic_html .= '		</td>';
		}
		
		$progress_report_statistic_html .= '			<td class="main"></td></tr>';
		$progress_report_statistic_html .= '	</table>';
		
		return $progress_report_statistic_html;
	}
	
	function order_product_bracket_info ($orders_products_id, $level_bar_unit_calculate = false) {
		$tasks_info_array = array();
		
		$orders_products_info_select_sql = "	SELECT ocp.orders_custom_products_value AS task_info, sta.supplier_tasks_status, SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.supplier_tasks_allocation_progress, ocpB.orders_custom_products_value AS bracket_info 
												FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (ocp.orders_products_id=sta.orders_products_id)
												LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
													ON (ocp.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
												WHERE ocp.orders_products_id ='" . tep_db_input($orders_products_id) . "' 
													AND ocp.orders_custom_products_key='power_leveling_info'";
	
		$orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
		
		$orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql);
		
		$total_time = 0;
		$time_pass = 0;
		
		$time_array = explode(':', $orders_products_info_row['hours']);
		
		preg_match('/(?:ETA:)(?:.*?)(\d+)( day)(##)?/', $orders_products_info_row['task_info'], $eta_days_array);
		preg_match('/(?:ETA:)(?:.*?)(\d+)( hour)(##)?/', $orders_products_info_row['task_info'], $eta_hours_array);
		
		if (count($eta_days_array) > 0) {
			$total_time = $eta_days_array[1] * 24;
		}
		
		$total_time += $eta_hours_array[1];
		
		if ($orders_products_info_row['supplier_tasks_status'] == 2 || $orders_products_info_row['supplier_tasks_status'] == 3 || $orders_products_info_row['supplier_tasks_status'] == 4) {
			$time_pass = (int)((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60);
		} else if ($orders_products_info_row['supplier_tasks_status'] == 1) {
			$time_pass = (int)((int)$time_array[0] + ((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60));
		}
		
		if (tep_not_null($orders_products_info_row['bracket_info'])) {
			$bracket_info_array = unserialize($orders_products_info_row['bracket_info']);
			
			preg_match('/(Current '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $current_level_array);
			preg_match('/(Desired '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $desired_level_array);
			
			if ($bracket_info_array['mode'] == 'continuos') {
				$current_level = $current_level_array[2];
				$desired_level = $desired_level_array[2];
			} else if ($bracket_info_array['mode'] == 'discrete') {
				$current_alias = $current_level_array[2];
				$desired_alias = $desired_level_array[2];
				
				for ($track = 0; $track < sizeof($bracket_info_array['range']) ; $track++) {
					if (tep_not_null($bracket_info_array['range'][$track]['alias'])) {
						if ($current_alias == $bracket_info_array['range'][$track]['alias']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['alias']) {
							$end = $track;
						}
					} else {
						if ($current_alias == $bracket_info_array['range'][$track]['level']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['level']) {
							$end = $track;
						}
					}
				}
				$current_level = $start;
				$desired_level = $end;
			}
			
			// Calculate level bar
			if ($level_bar_unit_calculate) {
				$total_bar_unit = $this->calculate_level_bar_unit($current_level, $desired_level, $orders_products_info_row['supplier_tasks_allocation_progress'], $bracket_info_array);
			}
		}
		
		$tasks_info_array = array(	'start_level' => $current_level, 
									'desired_level' => $desired_level, 
									'total_time' => $total_time, 
									'time_pass' => $time_pass, 
									'start_level_display' => $current_level_array[2], 
									'desired_level_display' => $desired_level_array[2],
									'level_label' => $bracket_info_array['bracket_cfg']['pl_level_label'],
									'level_total_unit_bar' => $total_bar_unit);
		
		return $tasks_info_array;
	}
	
	function calculate_level_bar_unit($current_level, $desired_level, $progress_level, $bracket_info_array) {
		if (tep_not_null($desired_level)) {
			$level_left = ((int)$desired_level - (int)$current_level);
			
			if ($level_left != 0) {
				$level_bar_unit = 20 / $level_left;
			}
			
			if ($bracket_info_array['mode'] == 'continuos') {
				$total_bar_unit = ((int)$progress_level - (int)$current_level) * $level_bar_unit;
			} else if ($bracket_info_array['mode'] == 'discrete') {
				for ($track_bar = 0; $track_bar < sizeof($bracket_info_array['range']) ; $track_bar++) {
					if (tep_not_null($bracket_info_array['range'][$track_bar]['alias'])) {
						if ($progress_level == $bracket_info_array['range'][$track_bar]['alias']) {
							$total_bar_unit = ($track_bar - $current_level) * $level_bar_unit;
						}
					} else {
						if ($progress_level == $bracket_info_array['range'][$track_bar]['level']) {
							$total_bar_unit = ($track_bar - $current_level) * $level_bar_unit;
						}
					}
				}
			}
		}
		return $total_bar_unit;
	}
	
	function calculate_level_percentage($orders_products_id) {
		$orders_products_info_select_sql = "	SELECT ocp.orders_custom_products_value AS task_info, sta.supplier_tasks_status, SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.supplier_tasks_allocation_progress, ocpB.orders_custom_products_value AS bracket_info 
												FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (ocp.orders_products_id=sta.orders_products_id)
												LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
													ON (ocp.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
												WHERE ocp.orders_products_id ='" . tep_db_input($orders_products_id) . "' 
													AND ocp.orders_custom_products_key='power_leveling_info'";
													
		$orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
		
		$orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql);
		
		if (tep_not_null($orders_products_info_row['bracket_info'])) {
			$bracket_info_array = unserialize($orders_products_info_row['bracket_info']);
			
			preg_match('/(Current '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $current_level_array);
			preg_match('/(Desired '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $desired_level_array);
			
			if ($bracket_info_array['mode'] == 'continuos') {
				$current_level = $current_level_array[2];
				$desired_level = $desired_level_array[2];
				$progress_level = $orders_products_info_row['supplier_tasks_allocation_progress'];
			} else if ($bracket_info_array['mode'] == 'discrete') {
				$current_alias = $current_level_array[2];
				$desired_alias = $desired_level_array[2];
				
				for ($track = 0; $track < sizeof($bracket_info_array['range']) ; $track++) {
					if (tep_not_null($bracket_info_array['range'][$track]['alias'])) {
						if ($current_alias == $bracket_info_array['range'][$track]['alias']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['alias']) {
							$end = $track;
						}
					} else {
						if ($current_alias == $bracket_info_array['range'][$track]['level']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['level']) {
							$end = $track;
						}
					}
				}
				$current_level = $start;
				$desired_level = $end;
				
				for ($progress_level_count = 0; $progress_level_count < sizeof($bracket_info_array['range']) ; $progress_level_count++) {
					if (tep_not_null($bracket_info_array['range'][$progress_level_count]['alias'])) {
						if ($orders_products_info_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$progress_level_count]['alias']) {
							$progress_level = $progress_level_count;
						}
					} else {
						if ($orders_products_info_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$progress_level_count]['level']) {
							$progress_level = $progress_level_count;
						}
					}
				}
			}
			
			if (tep_not_null($desired_level) && $desired_level > 0) {
				$percentage_result = round((100 / ($desired_level - $current_level)) * ($progress_level - $current_level), 2);
			}
		}
		return $percentage_result;
	}
	
	function get_cp_viewed_date($custom_products_code_id) {
		$log_time = '';
		
		$log_time_select_sql = "	SELECT log_time 
									FROM " . TABLE_CUSTOM_PRODUCTS_CODE_LOG . " 
									WHERE custom_products_code_id = '" . tep_db_input($custom_products_code_id) . "' 
										AND custom_products_code_log_user_role = 'customers' 
										AND log_system_messages REGEXP \"(<b>CD Key View Status</b>: Not Viewed --> Viewed)\" 
									LIMIT 1";
		$log_time_result_sql = tep_db_query($log_time_select_sql);
		if ($log_time_row = tep_db_fetch_array($log_time_result_sql)) {
			$log_time = $log_time_row['log_time'];
		}
		
		return $log_time;
	}
}

?>