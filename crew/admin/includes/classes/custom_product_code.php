<?php
class custom_product_code {
    public  $custom_products_code_array = array();
    private static $to_aws_enabled = 'true';
    private $aws_obj;
    
    public function __construct() {
        global $aws_obj;
        
        if(!is_object($aws_obj)) {
            require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

            $this->aws_obj = new ogm_amazon_ws();
        } else {
            $this->aws_obj = $aws_obj;
        }
        
        $this->aws_obj->set_bucket_key('BUCKET_SECURE');
        $this->aws_obj->set_storage('STORAGE_STANDARD');
    }
    
    public static function useAWS() {
        return self::$to_aws_enabled === 'true';
    }
    
    public function isCodeInAWS($custom_products_code_id, $is_s3) {
        if (!tep_not_null($is_s3)) {
            $is_s3 = $this->getDataByID($custom_products_code_id, 'to_s3');
        }
        
        return (int)$is_s3 == 1;
    }
    
    public function getCode($custom_products_code_id, $is_s3 = NULL, $product_id = 0, $code_add_datetime = NULL, $bucket_key = NULL) {
        if ($this->isCodeInAWS($custom_products_code_id, $is_s3)) {
            $this->setDataByID($custom_products_code_id, $product_id, $code_add_datetime, 1);
            
            return $this->getFromAWS($custom_products_code_id, $bucket_key);
        } else {
            return $this->getFromLocal($custom_products_code_id);
        }
    }
    
    public function getDataByID($custom_products_code_id, $field) {
        if (!isset($this->custom_products_code_array[$custom_products_code_id][$field])) {
            $select_sql = "	SELECT products_id, code_date_added, to_s3
                            FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                            WHERE custom_products_code_id = '" . tep_db_input($custom_products_code_id) . "'";
            $result_sql = tep_db_query($select_sql);
            if ($row = tep_db_fetch_array($result_sql)) {
                $this->custom_products_code_array[$custom_products_code_id] = array(
                    'products_id' => $row['products_id'],
                    'code_date_added' => $row['code_date_added'],
                    'to_s3' => $row['to_s3']
                );
            }
        }
        
        return isset($this->custom_products_code_array[$custom_products_code_id][$field]) ? $this->custom_products_code_array[$custom_products_code_id][$field] : NULL;
    }
    
    private function getFromAWS($custom_products_code_id, $bucket_key) {
        $code_add_datetime = $this->getDataByID($custom_products_code_id, 'code_date_added');
        $product_id = $this->getDataByID($custom_products_code_id, 'products_id');
        
        $this->aws_obj->set_bucket_key($bucket_key);

        if ($this->aws_obj->is_s3_bucket_config_enabled()) {
            $dir = date('Ym', strtotime($code_add_datetime)); 
            $filename = $custom_products_code_id . '.key';
            $filepath = $dir . '/' . $product_id . '/';

            $this->aws_obj->set_filename($filename);
            $this->aws_obj->set_filepath($filepath);
            $theData = $this->aws_obj->get_file();

            if ($theData != '') {
                return $theData->body;
            } else {
                // aws obj will report error
            }

            return FALSE;
        } else {
            return FALSE;
        }
    }
    
    private function getFromLocal($custom_products_code_id) {
        $theData = FALSE;
        
        if (file_exists(DIR_FS_SECURE . $custom_products_code_id . '.key')) {
            $fh = fopen(DIR_FS_SECURE . $custom_products_code_id . '.key', 'r') or die("can't open file");
            $theData = fread($fh, filesize(DIR_FS_SECURE . $custom_products_code_id . '.key'));
            fclose($fh);
        }
        
        return $theData;
    }
    
    public function setDataByID($custom_products_code_id, $product_id, $code_add_datetime, $to_s3) {
        if (tep_not_empty($custom_products_code_id) && tep_not_empty($product_id) && tep_not_empty($code_add_datetime)) {
            $this->custom_products_code_array[$custom_products_code_id] = array(
                'products_id' => $product_id,
                'code_date_added' => $code_add_datetime,
                'to_s3' => $to_s3
            );
        }
    }
    
    public function uploadCode($theData, $custom_products_code_id, $product_id = 0, $code_add_datetime = '', $bucket_key = null, $force_s3 = false) {
        if (self::useAWS() && $this->aws_obj->is_s3_bucket_config_enabled($bucket_key)) {
            $this->setDataByID($custom_products_code_id, $product_id, $code_add_datetime, 1);
            
            if (!$this->uploadToAWS($theData, $custom_products_code_id)) {
                // upload alternative
                if ($force_s3 !== TRUE) {
                    return $this->uploadToLocal($theData, $custom_products_code_id);
                } else {
                    return FALSE;
                }
            }
            
            return TRUE;
        } else if ($force_s3 !== TRUE) {
            return $this->uploadToLocal($theData, $custom_products_code_id);
        }
        
        return FALSE;
    }
    
    public function cleanCode($custom_products_code_id) {
        $this->deleteAtLocal($custom_products_code_id);
    }

    private function uploadToLocal($theData, $custom_products_code_id) {
        $path = DIR_FS_SECURE.$custom_products_code_id.'.key';
        $fh = fopen($path, 'w') or die("can't open file");
        fwrite($fh, $theData);
        fclose($fh);
        @chmod($path, 0600);
        @chown($path, CONFIG_APACHE_USER);
        return TRUE;
    }
    
    private function deleteAtLocal($custom_products_code_id) {
        unlink(DIR_FS_SECURE.$custom_products_code_id.'.key');
    }
    
    private function uploadToAWS($theData, $custom_products_code_id) {
        $code_add_datetime = $this->getDataByID($custom_products_code_id, 'code_date_added');
        $product_id = $this->getDataByID($custom_products_code_id, 'products_id');
        
        $dir = date('Ym', strtotime($code_add_datetime)); 
        $filename = $custom_products_code_id . '.key';
        $filepath = $dir . '/' . $product_id . '/';

        $this->aws_obj->set_filename($filename);
        $this->aws_obj->set_filepath($filepath);
        $this->aws_obj->set_file_content($theData);

        if ($this->aws_obj->save_file()) {
            $update_sql = "	UPDATE " . TABLE_CUSTOM_PRODUCTS_CODE . " 
                                SET to_s3 = 1 
                            WHERE custom_products_code_id = '".tep_db_input($custom_products_code_id)."'";
            tep_db_query($update_sql);

            return TRUE;
        } else {
            // aws obj will report error
        }
        
        return FALSE;
    }
}
?>