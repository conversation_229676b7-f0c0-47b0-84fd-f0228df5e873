<?
require_once('includes/functions/custom_product.php');

class custom_product_payment
{
	var $info, $payment_info, $orders, $supplier;
	var $payment_id;
	
    function custom_product_payment($payment_id='')
    {
      	$this->info = array();
      	$this->orders = array();
      	$this->supplier = array();
		$this->payment_info = array();
					
		if (tep_not_null($payment_id)) {
			$this->payment_id = $payment_id;
      		$this->query($payment_id);
      	}
    }
	
	function set_remarks($remarks) {
		$this->payment_info['remarks'] = $remarks;
	}
	
	function set_show_supplier_remark($show_supplier) {
		$this->payment_info['show_supplier'] = $show_supplier;
	}
	
    function query($payment_id)
    {
    	$payment_info_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_CP_PAYMENTS . " WHERE supplier_cp_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $payment_info_row['suppliers_id'] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
      	$this->info = array('currency' => $payment_info_row['currency'],
                          	'currency_value' => $payment_info_row['currency_value'],
                          	'payments_amount' => $payment_info_row['supplier_cp_payments_amount'],
                          	'payments_tax' => $payment_info_row['supplier_cp_payments_tax'],
                          	'payments_total' => $payment_info_row['supplier_cp_payments_total'],
                          	'payments_date' => $payment_info_row['supplier_cp_payments_date'],
                          	'last_modified' => $payment_info_row['supplier_cp_payments_last_modified'],
                          	'payments_status' => $payment_info_row['supplier_cp_payments_status']
                          	);
		
      	$this->supplier = array('id' => $payment_info_row['suppliers_id'],
      							'firstname' => $payment_info_row['suppliers_firstname'],
                              	'lastname' => $payment_info_row['suppliers_lastname'],
                              	'street_address' => $payment_info_row['suppliers_street_address'],
                              	'suburb' => $payment_info_row['suppliers_suburb'],
                              	'city' => $payment_info_row['suppliers_city'],
                              	'postcode' => $payment_info_row['suppliers_postcode'],
                              	'state' => $payment_info_row['suppliers_state'],
                              	'country' => $payment_info_row['suppliers_country'],
                              	'format_id' => 1,
                              	'email_address' => $payment_info_row['suppliers_email_address'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment_info = array('paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              		'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
	                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
	                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
	                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
	                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
	                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
        
  		$index = 0;
  		
  		$payment_orders_select_sql = "	SELECT scpp.orders_products_id, scpp.supplier_cp_payments_products_paid_amount, scpp.supplier_cp_payments_type, op.orders_products_id, op.orders_id, op.products_name FROM " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (op.orders_products_id = scpp.orders_products_id) WHERE supplier_cp_payments_id ='" . tep_db_input($payment_id) . "'";
  		$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
  		while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
  			$this->orders[$index] = array(  'id' => $payment_orders_row["orders_id"],
  											'orders_products_id' => $payment_orders_row["orders_products_id"],
  											'product_name' => $payment_orders_row["products_name"],
  											'paid_amount' => $payment_orders_row["supplier_cp_payments_products_paid_amount"],
  											'payments_type' => $payment_orders_row["supplier_payments_type"]
  										);
  										
  			$index++;
  			//echo $payment_orders_row["supplier_cp_payments_products_paid_amount"];
  		}
	}
	
	function make_payment($order_product_ids_array)
	{
		global $currencies, $login_email_address;
		
		$result = array();
		$this_payment_amount = 0;
		$success_make_payment = false;
		
		if (is_array($order_product_ids_array) && count($order_product_ids_array)) {	// Check if at least one order is selected
			$processing_order_count_select_sql = "	SELECT COUNT(orders_products_id) AS processing_orders_products, COUNT(DISTINCT suppliers_id) AS total_supplier 
													FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
													WHERE orders_products_id IN ('" .implode("', '", $order_product_ids_array) . "') 
													AND supplier_tasks_status = 4";
			$processing_order_count_result_sql = tep_db_query($processing_order_count_select_sql);
			$processing_order_count_row = tep_db_fetch_array($processing_order_count_result_sql);
			
			if ($processing_order_count_row['processing_orders_products'] != count($order_product_ids_array)) { // Total processing orders product unmatch the total selected orders
				$result = array('text' => ERROR_NON_PROCESSING_ORDERS_PRODUCTS, 'type' => 'error');
			} else if ($processing_order_count_row['total_supplier'] > 1) {	// Those selected orders belong to more than one supplier
				$result = array('text' => ERROR_MULTIPLE_SUPPLIER_PAYMENT, 'type' => 'error');
			} else {
				// Get supplier id first
				$supplier_id_select_sql = "SELECT suppliers_id FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id = '" . tep_db_input($order_product_ids_array[0]) . "'";
				$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
				if ($supplier_id_row = tep_db_fetch_array($supplier_id_result_sql)) {
					$supplier_id = (int)$supplier_id_row['suppliers_id'];
					
					$supplier_address_select_sql = "SELECT s.*, z.zone_name, co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id  
													FROM " . TABLE_SUPPLIER . " AS s
													LEFT JOIN " . TABLE_ZONES . " AS z 
														ON (s.supplier_zone_id = z.zone_id) 
													LEFT JOIN " . TABLE_COUNTRIES . " AS co 
														ON (s.supplier_country_id = co.countries_id) 
													WHERE s.supplier_id = '" . $supplier_id . "'";
					
			      	$supplier_address_result_sql = tep_db_query($supplier_address_select_sql);
			      	if ($supplier_address_row = tep_db_fetch_array($supplier_address_result_sql)) {
						// Create new payment
			      		$payment_data_array = array('suppliers_id' => $supplier_id,
						                            'supplier_cp_payments_date' => 'now()',
						                            'supplier_cp_payments_last_modified' => 'now()',
						                            'supplier_cp_payments_status' => 2,
						                            'suppliers_firstname' => $supplier_address_row['supplier_firstname'],
						                            'suppliers_lastname' => $supplier_address_row['supplier_lastname'],
						                            'suppliers_street_address' => $supplier_address_row['supplier_street_address'],
													'suppliers_suburb' => $supplier_address_row['supplier_suburb'],
													'suppliers_city' => $supplier_address_row['supplier_city'],
													'suppliers_postcode' => $supplier_address_row['supplier_postcode'],
						                            'suppliers_state' => ((tep_not_null($supplier_address_row['supplier_state'])) ? $supplier_address_row['supplier_state'] : $supplier_address_row['zone_name']),
						                            'suppliers_country' => $supplier_address_row['countries_name'],
						                            'suppliers_telephone' => $supplier_address_row['supplier_telephone'],
						                            'suppliers_email_address' => $supplier_address_row['supplier_email_address'],
						                            'currency' => DEFAULT_CURRENCY,
						                            'currency_value' => $currencies->currencies[DEFAULT_CURRENCY]['value']
						                           );
					    tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS, $payment_data_array);
					    $supplier_payments_id = tep_db_insert_id();
					    
					    // Insert payment orders
					    for ($order_cnt=0; $order_cnt < count($order_product_ids_array); $order_cnt++) {
					    	$order_payable_amt = custom_product::get_order_total_payable_amount($order_product_ids_array[$order_cnt]);
					    	$order_paid_amt = $order_payable_amt;
					    	$confirm_pay_amt = $order_payable_amt;
					    	
					    	$supplier_payments_type = 2;
					    	
					    	$payment_order_data_array = array(	'supplier_cp_payments_id' => $supplier_payments_id,
				      											'orders_products_id' => $order_product_ids_array[$order_cnt],
				      											'supplier_cp_payments_products_paid_amount' => $order_paid_amt,
																'supplier_cp_payments_type' => $supplier_payments_type
															  );
					    	
					    	tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS, $payment_order_data_array);
					    	
					    	$completing_orders_products_update_data_array = array(	'supplier_tasks_status' => 5
			          					 								);
			          		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $completing_orders_products_update_data_array, 'update', "orders_products_id = '" . $order_product_ids_array[$order_cnt] . "'");
			          		
					    	tep_update_record_tags(FILENAME_PROGRESS_REPORT, $order_product_ids_array[$order_cnt], 5, "");
					    	
							$supplier_tasks_allocation_history_data_array = array(  'orders_products_id' => $order_product_ids_array[$order_cnt],
				    																'supplier_tasks_status' => 5,
				    																'date_added' => 'now()',
				    																'comments' => "Pay in Payment ID: " . $supplier_payments_id,
				    																'changed_by' => $login_email_address,
				    																'user_role' => 'admin'
				    															);
			    		
			    			tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $supplier_tasks_allocation_history_data_array);
							
							$this_payment_amount += (double)$confirm_pay_amt;
						}
					    
					    // Update payment amount data
					    $payment_update_data_array = array(	'supplier_cp_payments_amount' => $this_payment_amount,
	          					 							'supplier_cp_payments_total' => $this_payment_amount
	          					 							);
	          			tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS, $payment_update_data_array, 'update', "supplier_cp_payments_id = '" . $supplier_payments_id . "'");
	          			
	          			// Insert payment history
					    $payment_history_data_array = array('supplier_cp_payments_id' => $supplier_payments_id,
			      											'supplier_cp_payments_status' => 2,
			      											'date_added' => 'now()',
															'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
								                            'comments' => $this->payment_info['remarks'],
								                            'changed_by' => $login_email_address
							                           	);
					    tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS_HISTORY, $payment_history_data_array);
	          			
	          			$success_make_payment = true;
	          			
	          			// Send email to supplier
						$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
									EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $supplier_payments_id) . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $currencies->format($this_payment_amount)) . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_DATE, strftime(DATE_FORMAT_LONG)) . "\n\n" .
								 	($this->payment_info['show_supplier'] ? sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" : '') .
								 	EMAIL_TEXT_CLOSING . "\n\n" .
								 	EMAIL_FOOTER;
						
						$email_greeting = tep_get_email_greeting($supplier_address_row['supplier_firstname'], $supplier_address_row['supplier_lastname'], $supplier_address_row['supplier_gender']);
						
						$email = $email_greeting . $email;
						tep_mail($supplier_address_row['supplier_firstname'].' '.$supplier_address_row['supplier_lastname'], $supplier_address_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_SUBJECT, $supplier_payments_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			      	}
				}
				
				if ($success_make_payment) {
					$result = array('text' => sprintf(SUCCESS_MAKE_PAYMENT, $supplier_payments_id), 'type' => 'success');
				} else {
					$result = array('text' => ERROR_NEW_PAYMENT_FAILED, 'type' => 'error');
				}
			}
		} else {
			$result = array('text' => ERROR_NO_ORDERS_SELECTED, 'type' => 'error');
		}
		
		return $result;
	}
	
	function reverse_payment()
	{
		global $currencies, $login_email_address, $languages_id;
		
		$result = array();
		$success_reverse_payment = false;
		
		$payment_info_select_sql = "SELECT suppliers_id, supplier_cp_payments_status, supplier_cp_payments_date, supplier_cp_payments_total, currency, currency_value FROM " . TABLE_SUPPLIER_CP_PAYMENTS . " WHERE supplier_cp_payments_id = '" . tep_db_input($this->payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	
		if ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {	// Check if payment is exists
			if ($payment_info_row['supplier_cp_payments_status'] == '1' || $payment_info_row['supplier_cp_payments_status'] == '2') {
				$supplier_id = (int)$payment_info_row['suppliers_id'];
				
				$payment_status_array = array();
				$payment_status_select_sql = "SELECT supplier_payments_status_id, supplier_payments_status_name FROM " . TABLE_SUPPLIER_PAYMENTS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY supplier_payments_status_sort_order";
				$payment_status_result_sql = tep_db_query($payment_status_select_sql);
				while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
					$payment_status_array[$payment_status_row['supplier_payments_status_id']] = $payment_status_row['supplier_payments_status_name'];                       
				}

				$payment_status_update_sql_data = array('supplier_cp_payments_status' => 3,
      					 								'supplier_cp_payments_last_modified' => 'now()',
      					 								);
      			tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS, $payment_status_update_sql_data, 'update', "supplier_cp_payments_id = '" . tep_db_input($this->payment_id) . "'");
      			
				// Insert history
			    $payment_history_data_array = array('supplier_cp_payments_id' => $this->payment_id,
	      											'supplier_cp_payments_status' => 3,
	      											'date_added' => 'now()',
													'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
						                            'comments' => $this->payment_info['remarks'],
						                            'changed_by' => $login_email_address
					                           	);
			    tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS_HISTORY, $payment_history_data_array);
			    
			    $payment_orders_product_select_sql = " SELECT sta.orders_products_id, sta.supplier_tasks_status, scpp.supplier_cp_payments_type FROM " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta ON (sta.orders_products_id=scpp.orders_products_id) WHERE scpp.supplier_cp_payments_id='" . tep_db_input($this->payment_id) . "'";
			    $payment_orders_product_result_sql = tep_db_query($payment_orders_product_select_sql);
				
			    while ($payment_orders_product_row = tep_db_fetch_array($payment_orders_product_result_sql)) {
			    	if ($payment_orders_product_row["supplier_tasks_status"] == 5) {
			    		// Update custom product from Completed to Done
			    		$done_orders_products_update_sql_data = array( 'supplier_tasks_status' => 4);
			    		
			    		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $done_orders_products_update_sql_data, 'update', "orders_products_id = '" . $payment_orders_product_row['orders_products_id'] . "'");
			    		
			    		tep_update_record_tags(FILENAME_PROGRESS_REPORT, $payment_orders_product_row['orders_products_id'], 4, "");
			    		
			    		$supplier_tasks_allocation_history_data_array = array(  'orders_products_id' => $payment_orders_product_row['orders_products_id'],
			    																'supplier_tasks_status' => 4,
			    																'date_added' => 'now()',
			    																'comments' => "Reversed from Payment ID: " . tep_db_input($this->payment_id),
			    																'changed_by' => $login_email_address,
			    																'user_role' => 'admin'
			    															);
			    		
			    		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $supplier_tasks_allocation_history_data_array);
			    	}
			    }
			    
			    $supplier_info_select_sql = "	SELECT s.supplier_firstname, s.supplier_lastname, s.supplier_email_address, s.supplier_gender 
												FROM " . TABLE_SUPPLIER . " AS s
												WHERE s.supplier_id = '" . $supplier_id . "'";
				$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
				$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
				
				// Send email to supplier
				$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
							EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $this->payment_id) . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $currencies->format($payment_info_row['supplier_cp_payments_total'], true, $payment_info_row['currency'], $payment_info_row['currency_value'])) . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_DATE, tep_date_long($payment_info_row["supplier_cp_payments_date"])) . "\n\n" .
						 	sprintf(EMAIL_TEXT_STATUS_UPDATE, $payment_status_array[$payment_info_row['supplier_cp_payments_status']] . ' -> ' . $payment_status_array['3']) . "\n\n" . 
						 	($this->payment_info['show_supplier'] ? sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" : '') .
						 	EMAIL_TEXT_CLOSING . "\n\n" .
						 	EMAIL_FOOTER;
				
				$email_greeting = tep_get_email_greeting($supplier_info_row['supplier_firstname'], $supplier_info_row['supplier_lastname'], $supplier_info_row['supplier_gender']);
				
				$email = $email_greeting . $email;
				tep_mail($supplier_info_row['supplier_firstname'].' '.$supplier_info_row['supplier_lastname'], $supplier_info_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_UPDATE_SUBJECT, $this->payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				
				$result = array('text' => sprintf(SUCCESS_REVERSE_PAYMENT, $this->payment_id), 'type' => 'success');
			} else {
				$result = array('text' => ERROR_NOT_REVERSIBLE_PAYMENT_STATUS, 'type' => 'error');
			}
		} else {
			$result = array('text' => ERROR_PAYMENT_DOES_NOT_EXIST, 'type' => 'error');
		}
		
		return $result;
	}
	
	function update_payment_comment()
	{
		global $currencies, $login_email_address, $languages_id;
		
		$payment_history_data_array = array('supplier_cp_payments_id' => $this->payment_id,
  											'supplier_cp_payments_status' => 0,
  											'date_added' => 'now()',
											'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
				                            'comments' => $this->payment_info['remarks'],
				                            'changed_by' => $login_email_address
			                           	);
	    tep_db_perform(TABLE_SUPPLIER_CP_PAYMENTS_HISTORY, $payment_history_data_array);
	    
	    if ($this->payment_info['show_supplier']) {
			$payment_info_select_sql = "SELECT sps.supplier_payments_status_name, scp.supplier_cp_payments_date, scp.supplier_cp_payments_total, scp.currency, scp.currency_value, s.supplier_firstname, s.supplier_lastname, s.supplier_email_address, s.supplier_gender 
										FROM " . TABLE_SUPPLIER_CP_PAYMENTS . " AS scp 
										INNER JOIN " . TABLE_SUPPLIER_PAYMENTS_STATUS . " AS sps 
											ON (scp.supplier_cp_payments_status=sps.supplier_payments_status_id AND sps.language_id='".tep_db_input($languages_id)."')
										INNER JOIN " . TABLE_SUPPLIER . " AS s 
											ON scp.suppliers_id=s.supplier_id 
										WHERE scp.supplier_cp_payments_id = '" . tep_db_input($this->payment_id) . "'";
			$payment_info_result_sql = tep_db_query($payment_info_select_sql);
			$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
			
			// Send email to supplier
			$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
						EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $this->payment_id) . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $currencies->format($payment_info_row['supplier_cp_payments_total'], true, $payment_info_row['currency'], $payment_info_row['currency_value'])) . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_DATE, tep_date_long($payment_info_row["supplier_cp_payments_date"])) . "\n\n" .
					 	sprintf(EMAIL_TEXT_STATUS_UPDATE, $payment_info_row['supplier_payments_status_name']) . "\n\n" . 
					 	sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" .
					 	EMAIL_TEXT_CLOSING . "\n\n" .
					 	EMAIL_FOOTER;
			
			$email_greeting = tep_get_email_greeting($payment_info_row['supplier_firstname'], $payment_info_row['supplier_lastname'], $payment_info_row['supplier_gender']);
			
			$email = $email_greeting . $email;
			tep_mail($payment_info_row['supplier_firstname'].' '.$payment_info_row['supplier_lastname'], $payment_info_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_UPDATE_SUBJECT, $this->payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
		
		$result = array('text' => sprintf(SUCCESS_UPDATE_PAYMENT, $this->payment_id), 'type' => 'success');
		
		return $result;
	}
	/*
	function get_order_paid_amount($order_id)
	{
		$paid_amount_select_sql = "	SELECT SUM(spo.supplier_payments_orders_paid_amount) AS total_paid 
									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
									INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
										ON (spo.supplier_payments_id=sp.supplier_payments_id AND sp.supplier_payments_status IN (1, 2)) 
									WHERE spo.supplier_order_lists_id = '" . tep_db_input($order_id) . "' ";
		$paid_amount_result_sql = tep_db_query($paid_amount_select_sql);
		$paid_amount_row = tep_db_fetch_array($paid_amount_result_sql);
		
		return $paid_amount_row["total_paid"];
	}
	*/
	
	function get_order_paid_history($orders_products_id, $all=true)
	{
		$payment_history_array = '';
		$order_product_payments_select_sql = "	SELECT scpp.supplier_cp_payments_id, scpp.supplier_cp_payments_products_paid_amount, scpp.supplier_cp_payments_type, scp.currency, scp.currency_value 
										FROM " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp 
										INNER JOIN " . TABLE_SUPPLIER_CP_PAYMENTS . " AS scp 
											ON (scpp.supplier_cp_payments_id=scp.supplier_cp_payments_id) 
										WHERE scpp.orders_products_id = '" . tep_db_input($orders_products_id) . "' " . (!$all ? "AND scp.supplier_cp_payments_status IN (1, 2) " : '') . " 
										ORDER BY scp.supplier_cp_payments_date";
		
		$order_product_payments_result_sql = tep_db_query($order_product_payments_select_sql);
		
		while ($order_product_payments_row = tep_db_fetch_array($order_product_payments_result_sql)) {
			$payment_history_array = array(	'payment_id' => $order_product_payments_row['supplier_cp_payments_id'],
											'paid_amount' => $order_product_payments_row['supplier_cp_payments_products_paid_amount'],
											'paid_currency' => $order_product_payments_row['currency'],
											'paid_currency_value' => $order_product_payments_row['currency_value'],
											'payment_type' => $order_product_payments_row['supplier_cp_payments_type']
											);
		}
		
		return $payment_history_array;
	}
}
?>