<?php

class customers_closure_document {

    public $max_dim, $customer_id;
    private $aws_obj, $upload_obj, $max_upload_dim_w = 800, $max_upload_dim_h = 800;
    private static $aws_enabled = 'true';
    private static $local_setting = array(
        'permissions' => '0775',
        'source_path' => DIR_FS_DATA_LL_SUBMISSION_TEMP, //DIR_FS_DATA_LL_SUBMISSION,
    );
    private static $aws_s3_setting = array(
        'bucket' => 'BUCKET_UPLOAD',
        'acl' => 'ACL_PRIVATE',
        'storage' => 'STORAGE_STANDARD',
        'source_path' => 'user/account_closure',
    );
    private static $image_supported = array(
        'type' => array('gif', 'jpg', 'png'),
        'size' => 512000
    );

    public function __construct($login_user_id = NULL) {
        $this->upload_obj = new upload('', self::$local_setting['source_path'] . '/', self::$local_setting['permissions'], self::$image_supported['type'], '', self::$image_supported['size']);

        if (self::useAWS()) {
            $this->aws_obj = $this->upload_obj->set_amazon_api_transfer(array(
                'bucket' => self::$aws_s3_setting['bucket'],
                'filepath' => self::$aws_s3_setting['source_path'] . '/',
                'storage' => self::$aws_s3_setting['storage'],
                'acl' => self::$aws_s3_setting['acl'],
            ));
        }
    }

    private function getImageResource($filename, $file_content) {
        $return_bool = FALSE;
        $filepath = self::$local_setting['source_path'] . '/' . 'temp_' . $filename;

        if (($fh = @fopen($filepath, "w")) !== false) {
            fwrite($fh, $file_content);
            fclose($fh);

            if ($max_dim = $this->getMaxDim()) {
                $return_bool = resizeImage($filepath, $max_dim);
            } else {
                $return_bool = LoadImage($filepath);
            }

            if (file_exists($filepath)) {
                @unlink($filepath);
            }
        } else {
            $this->reportError(array(
                'error' => 'failed to write:' . $filepath
            ));
        }

        return $return_bool;
    }

    public static function useAWS() {
        return self::$aws_enabled === 'true';
    }

    private function getMaxDim() {
        return tep_not_empty($this->maxdim) ? $this->maxdim : 0;
    }

    public function setMaxDim($num) {
        $this->maxdim = $num;
    }

    public function setMaxUploadDim($width, $height) {
        $this->max_upload_dim_w = $width;
        $this->max_upload_dim_h = $height;
    }

    private function getCustomerID() {
        return (int) $this->customer_id;
    }

    public function setCustomerID($customer_id) {
        if (tep_not_null($customer_id)) {
            $this->customer_id = $customer_id;
        }
    }

    private function getContent($tmp_file_server_path = '') {
        $return_string = '';
        $file_path = $tmp_file_server_path != '' ? $tmp_file_server_path : (isset($this->upload_obj->file['tmp_name']) ? $this->upload_obj->file['tmp_name'] : '');

        $pic_type = strtolower(strrchr($this->upload_obj->file['name'], '.'));
        $pic_name = self::$local_setting['source_path'] . '/original_temp_' . time() . $pic_type;

        move_uploaded_file($file_path, $pic_name);

        $resized_file = self::$local_setting['source_path'] . '/' . $this->max_upload_dim_w . 'x' . $this->max_upload_dim_h . $pic_type;

        if (!$this->imageResize($pic_name, $resized_file, $this->max_upload_dim_w, $this->max_upload_dim_h, 0)) {
            $resized_file = $pic_name;
        }

        if ($resized_file) {
            $return_string = file_get_contents($resized_file);
        }

        unlink($pic_name);
        unlink($resized_file);
        return $return_string;
    }

    private function getFullPath($source_filename_array, $return_array = false) {
        list($customer_id, $filename) = $source_filename_array;
        $ym = substr($filename, 0, 6);
        $return_str = (is_numeric($ym) ? substr($filename, 0, 4) . '/' . substr($filename, 4, 2) : '') . '/' . $customer_id . '_' . $filename;

        return $return_array ? explode('/', $return_str) : $return_str;
    }

    private function imageResize($src, $dst, $width, $height, $crop = 0) {
        if (!list($w, $h) = getimagesize($src))
            return false;

        $type = strtolower(substr(strrchr($src, "."), 1));

        if ($type == 'jpeg')
            $type = 'jpg';

        switch ($type) {
            case 'gif': $img = imagecreatefromgif($src);
                break;
            case 'jpg': $img = imagecreatefromjpeg($src);
                break;
            case 'png': $img = imagecreatefrompng($src);
                break;
            default : return "Unsupported picture type!";
        }

        // resize
        if ($crop) {
            if ($w < $width or $h < $height)
                return false;

            $ratio = max($width / $w, $height / $h);
            $h = $height / $ratio;
            $x = ($w - $width / $ratio) / 2;
            $w = $width / $ratio;
        } else {
            if ($w < $width and $h < $height)
                return false;

            $ratio = min($width / $w, $height / $h);
            $width = $w * $ratio;
            $height = $h * $ratio;
            $x = 0;
        }

        $new = imagecreatetruecolor($width, $height);

        // preserve transparency
        if ($type == "gif" or $type == "png") {
            imagecolortransparent($new, imagecolorallocatealpha($new, 0, 0, 0, 127));
            imagealphablending($new, false);
            imagesavealpha($new, true);
        }

        imagecopyresampled($new, $img, 0, 0, $x, 0, $width, $height, $w, $h);

        switch ($type) {
            case 'gif': imagegif($new, $dst);
                break;
            case 'jpg': imagejpeg($new, $dst);
                break;
            case 'png': imagepng($new, $dst);
                break;
        }
        return true;
    }

//  ======================================  READ  ======================================

    public function readDocument($filename, $log_enabled = true) {
        $return_bool = FALSE;

        if (self::useAWS()) {
            $source_filename_array = explode('_', $filename);
            $server_path_array = $this->getFullPath($source_filename_array, true);
            $source_filename = array_pop($server_path_array);

            $data_string = $this->readFromAWS(self::$aws_s3_setting['source_path'] . '/' . implode('/', $server_path_array) . '/', $source_filename);
        } else {
            $source_filename = $filename;
            $data_string = $this->readFromLocal(self::$local_setting['source_path'] . '/', $filename);
        }

        $return_bool = $this->getImageResource($source_filename, $data_string);

        if ($return_bool === FALSE) {
            $file_path = DIR_FS_ADMIN . 'images/no_image.gif';
            $return_bool = resizeImage($file_path, 200);
        }

        return $return_bool;
    }

    private function readFromLocal($filepath, $filename) {
        $return_string = '';
        $file_path = $filepath . $filename;

        if (file_exists($file_path)) {
            $return_string = file_get_contents($file_path);
        }

        return $return_string;
    }

    private function readFromAWS($filepath, $filename) {
        $this->aws_obj->set_filename($filename);
        $this->aws_obj->set_filepath($filepath);
        $theData = $this->aws_obj->get_file();

        if (isset($theData->status) && $theData->status == 200) {
            return $theData->body;
        } else {
            return '';
        }
    }

//  ======================================  UPLOAD  ======================================

    public function batchUpload($customer_id, $files_info) {
        $return_array = array();

        if (is_array($files_info)) {
            $this->setCustomerID($customer_id);

            foreach ($files_info AS $file_index => $file_array) {
                if (tep_not_null($file_array['name'])) {
                    $return_array = $this->uploadDocument($file_index);
                }
            }
        }

        return $return_array;
    }

    private function uploadDocument($file_index) {
        $return_array = array();
        $customer_id = $this->getCustomerID();

        if (tep_not_empty($customer_id) && $this->validateFile($file_index)) {
            if ($file_content = $this->getContent()) {
                // $this->upload_obj->set_output_messages('session');

                if (self::useAWS()) {
                    $server_path_array = $this->getFullPath(array($customer_id, date("YmdHis")), true);
                    $server_filename = array_pop($server_path_array);
                    $server_path = self::$aws_s3_setting['source_path'] . '/' . implode('/', $server_path_array) . '/';

                    // filename with file extension
                    if ($filename = $this->uploadToAWS($server_path, $server_filename, $file_content)) {
                        $return_array['filename'] = $filename;
                    }
                } else {
                    $server_filename = $customer_id . '_' . date("YmdHis");
                    if ($filename = $this->uploadToLocal(self::$local_setting['source_path'] . '/', $server_filename, $file_content)) {
                        $return_array['filename'] = $filename;
                    }
                }
            }
        }

        return $return_array;
    }

    private function uploadToLocal($filepath, $server_filename, $content, $image_size = 'small') {
        $return_bool = FALSE;
        $this->upload_obj->aws_enabled = FALSE;
        $this->upload_obj->set_destination($filepath);

        if ($this->upload_obj->save_content($content, $server_filename, $image_size)) {
            $return_bool = $this->upload_obj->filename;
        }

        return $return_bool;
    }

    private function uploadToAWS($filepath, $server_filename, $content, $image_size = 'small') {
        $return_bool = FALSE;

        if ($this->aws_obj->is_aws_s3_enabled() && $this->aws_obj->is_s3_bucket_config_enabled(self::$aws_s3_setting['bucket'])) {
            $this->upload_obj->set_amazon_filepath($filepath);

            if ($this->upload_obj->save_content($content, $server_filename, $image_size)) {
                $return_bool = $this->upload_obj->filename;
            } else {
                $this->reportError(array('Failed to upload to AWS S3.', 'bucket' => $this->aws_obj->aws_bucket_key), 'uploadDocument()');
            }
        } else {
            $this->reportError(array('AWS S3 is not enabled or the bucket is not enabled.', 'bucket' => $this->aws_obj->aws_bucket_key), 'uploadDocument()');
        }

        return $return_bool;
    }

//  ======================================  DELETE FILE  ======================================

    private function validateFile($file_index) {
        $this->upload_obj->set_file($file_index);
        // $this->upload_obj->set_output_messages('session');
        return $this->upload_obj->parse();
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Customers Account Closure Document Error - ' . date("F j, Y H:i");
        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>