<?php
/*
  $Id: customers_order_activities.php,v 1.21 2015/07/14 02:29:04 weichen Exp $

  Copyright (c) 2007 Dynamic Podium

  Released under the GNU General Public License
 */


if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOMERS_ORDER_ACTIVITIES)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOMERS_ORDER_ACTIVITIES);
}

class customers_order_activities {

    var $identity, $identity_email;
    var $activity_codes, $activity_codes_from_array, $activity_codes_to_array;
    var $payment_gateways, $payment_gateways_from_array, $payment_gateways_to_array;
    var $currency_display_decimal;

    // class constructor
    function customers_order_activities($identity, $identity_email) {
        global $login_groups_id;

        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user

        $this->activity_codes = array(
            array('id' => 'D', 'text' => 'Delivery +'),
            array('id' => 'RD', 'text' => 'Delivery -'),
            array('id' => 'DE', 'text' => 'Delivery Extra SC +'),
            array('id' => 'RDE', 'text' => 'Delivery Extra SC -'),
            array('id' => 'CD', 'text' => 'Compensation +'),
            array('id' => 'CRD', 'text' => 'Compensation -'),
            array('id' => 'RFD', 'text' => 'Refund +'),
            array('id' => 'RFRD', 'text' => 'Refund -'),
            array('id' => 'RVD', 'text' => 'Reverse +'),
            array('id' => 'RVRD', 'text' => 'Reverse -'),
            array('id' => 'RVDE', 'text' => 'Reverse Extra SC +'),
            array('id' => 'RVRDE', 'text' => 'Reverse Extra SC -')
        ); //end array

        $this->payment_gateways = $this->_get_all_active_receive_payment_methods();

        $this->activity_codes_from_array = array(array('id' => '', "text" => 'Select Activity Codes', "type" => 'optgroup'));
        $this->activity_codes_to_array = array(array('id' => '', "text" => 'Selected Activity Codes', "type" => 'optgroup'));

        $this->payment_gateways_from_array = array(array('id' => '', "text" => 'Select Payment Gateways', "type" => 'optgroup'));
        $this->payment_gateways_to_array = array(array('id' => '', "text" => 'Selected Payment Gateways', "type" => 'optgroup'));

        $this->currency_display_decimal = 2;
    }

    function search_coa($filename, $session_name) {
        global $view_coa_report_permission;

        $coa_html = '';

        foreach ($this->activity_codes as $res) {
            if (isset($_SESSION[$session_name]["activity_codes_to"]) && in_array($res['id'], $_SESSION[$session_name]["activity_codes_to"])) {
                $this->activity_codes_to_array[] = $res;
            } else {
                $this->activity_codes_from_array[] = $res;
            }
        }

        foreach ($this->payment_gateways as $res) {
            if (isset($_SESSION[$session_name]["payment_gateways_to"]) && in_array($res['id'], $_SESSION[$session_name]["payment_gateways_to"])) {
                $this->payment_gateways_to_array[] = $res;
            } else {
                $this->payment_gateways_from_array[] = $res;
            }
        }

        $order_site = array(
            array('id' => '', 'text' => ENTRY_COA_ORDER_SITE_PULLDOWN_DEFAULT),
            array('id' => '1', 'text' => 'OffGamers'),
            array('id' => '2', 'text' => 'OffGamers without SC'),
            array('id' => '3', 'text' => 'OffGamers SC only'),
            array('id' => '4', 'text' => 'G2G')
        );

        ob_start();
        ?>
        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="20">&nbsp;</td>
                            <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                        </tr>
                        <tr>
                            <td width="20">&nbsp;</td>
                            <td>
                                <?= tep_draw_form('coa_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '') ?>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main" valign="top" nowrap><?= ENTRY_COA_START_DATE ?></td>
                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('start_date', $_SESSION[$session_name]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.coa_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.coa_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                        <td class="main" width="8%">&nbsp;</td>
                                        <td class="main" valign="top" nowrap><?= ENTRY_COA_END_DATE ?></td>
                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('end_date', $_SESSION[$session_name]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.coa_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.coa_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td valign="top" class="main"><?= ENTRY_COA_ACTIVITY_CODE ?></td>
                                        <td colspan="4" class="main">
                                            <?= tep_draw_js_select_boxes('activity_codes', $this->activity_codes_from_array, $this->activity_codes_to_array, ' size="10" style="width:25em;"') ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td valign="top" class="main"><?= ENTRY_COA_PAYMENT_GATEWAY ?></td>
                                        <td colspan="4" class="main">
                                            <?= tep_draw_js_select_boxes('payment_gateways', $this->payment_gateways_from_array, $this->payment_gateways_to_array, ' size="10" style="width:25em;"') ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td valign="top" class="main"><?= ENTRY_COA_ORDER_SITE ?></td>
                                        <td colspan="4" class="main">
                                            <?= tep_draw_pull_down_menu('order_site', $order_site, $_SESSION[$session_name]["order_site"], 'id="order_site"') ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5" align="right">
                                            <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking();"', 'inputButton') ?>
                                            <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                        </td>
                                    </tr>
                                </table>
                                </form>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <script language="javascript">
            <!--
                function is_select_box_used(form_name, select_box_name) {
                var select_box_object = document.forms[form_name].elements[select_box_name];
                var select_box_used = false;
                if (select_box_object != null) {
                    if (select_box_object.length > 0) {
                        select_box_used = true;
                    } //end if
                } //end if

                return select_box_used;
            } //end function

            function select_box_data_send(form_name, select_box_name) {
                var select_box_object = document.forms[form_name].elements[select_box_name];
                if (select_box_object != null) {
                    for (x = 0; x < (select_box_object.length); x++) {
                        select_box_object.options[x].selected = true;
                    } //end for
                } //end if
            } //end function

            function form_checking() {
                var form_valid = true;

                if (document.coa_criteria.start_date.value.length < 1
                        || document.coa_criteria.end_date.value.length < 1) {
                    form_valid = false;
                } //end if

                if (form_valid) {
                    select_box_data_send('coa_criteria', 'activity_codes_to[]');
                    select_box_data_send('coa_criteria', 'payment_gateways_to[]');
                        } else {
                    alert('<?= JS_ERROR_COA_SEARCH_CRITERIA_NOT_ENTERED ?>');
                } //end if

                return form_valid;
            } //end function
            //-->
        </script>
        <?
        $coa_html = ob_get_contents();
        ob_end_clean();

        return $coa_html;
    }

    function show_coa($filename, $session_name, $input_array, &$messageStack) {
        global $currencies, $languages_id, $view_coa_report_permission;

        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["start_date"] = $input_array["start_date"];
            $_SESSION[$session_name]["end_date"] = $input_array["end_date"];
            $_SESSION[$session_name]["activity_codes_to"] = $input_array["activity_codes_to"];
            $_SESSION[$session_name]["payment_gateways_to"] = $input_array["payment_gateways_to"];
            $_SESSION[$session_name]["order_site"] = $input_array["order_site"];
        } //end if

        if (tep_not_null($_SESSION[$session_name]["start_date"]) && tep_not_null($_SESSION[$session_name]["end_date"])) {
            if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
                $startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
                list($yr, $mth, $day) = explode('-', $startDateObj[0]);
                list($hr, $min) = explode(':', $startDateObj[1]);
                $start_date_str = " ( sa.sales_activities_date >= '" . date('Y-m-d H:i:s', mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
            } else {
                list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
                $start_date_str = " ( sa.sales_activities_date >= '" . date('Y-m-d H:i:s', mktime(0, 0, 0, $mth, $day, $yr)) . "')";
            } //end if

            if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
                $endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
                list($yr, $mth, $day) = explode('-', $endDateObj[0]);
                list($hr, $min) = explode(':', $endDateObj[1]);
                $end_date_str = " ( sa.sales_activities_date <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 59, $mth, $day, $yr)) . "' )";
            } else {
                list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
                $end_date_str = " ( sa.sales_activities_date <= '" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "')";
            } //end if
            // get activity_codes wanted in search
            $activity_codes_str = "";
            if (tep_not_null($_SESSION[$session_name]["activity_codes_to"])) {
                $total_index = count($_SESSION[$session_name]["activity_codes_to"]);
                for ($current_index = 0; $current_index < ($total_index - 1); $current_index++) {
                    $current_activity_codes_id = $_SESSION[$session_name]["activity_codes_to"][$current_index];
                    $activity_codes_str = $activity_codes_str . "'" . $current_activity_codes_id . "',";
                } //end for
                $current_activity_codes_id = $_SESSION[$session_name]["activity_codes_to"][$total_index - 1];
                $activity_codes_str = $activity_codes_str . "'" . $current_activity_codes_id . "'";
                $activity_codes_str = "sa.sales_activities_code IN (" . $activity_codes_str . ")";
            } else {
                $activity_codes_str = " 1 ";
            } //end if
            // get payment_gateways wanted in search
            $payment_gateways_str = '';
            if (count($_SESSION[$session_name]["payment_gateways_to"])) {
                $actual_selected_payment_method = $_SESSION[$session_name]["payment_gateways_to"];
                if (in_array("OGM_CREDITS", $_SESSION[$session_name]["payment_gateways_to"])) {
                    $search_index_key = array_search('OGM_CREDITS', $actual_selected_payment_method);
                    $actual_selected_payment_method[$search_index_key] = '';
                }

                $payment_gateways_str = "o.payment_methods_parent_id IN ('" . implode("', '", $actual_selected_payment_method) . "')";
            } else {
                $payment_gateways_str = " 1 ";
            } //end if
            // get order_site wanted in search
            $order_type_str = '';
            if (tep_not_null($_SESSION[$session_name]["order_site"])) {
                $order_site_str = '';
                $orders_extra_info_str = '';
                switch ($_SESSION[$session_name]["order_site"]) {
                    case '4':
                        $order_type_str = "oei.orders_extra_info_value = '5'";
                        break;
                    case '3':
                        $order_type_str = "oei.orders_extra_info_value != '5' AND op.parent_orders_products_id = 0 AND op.custom_products_type_id = '3'";
                        break;
                    case '2':
                        $order_type_str = "oei.orders_extra_info_value != '5' AND op.parent_orders_products_id = 0 AND op.custom_products_type_id != '3'";
                        break;
                    default:
                        $order_type_str = "oei.orders_extra_info_value != '5'";
                        break;
                }
                $orders_extra_info_str = "";
                $order_type_str = "AND " . $order_type_str;
            } //end if

            $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;

            $select_sql = "	SELECT sa.sales_activities_id, sa.sales_activities_orders_id, sa.sales_activities_orders_products_id, DATE_FORMAT(sa.sales_activities_date, '%Y-%m-%d %H:%i') AS sales_activities_date, sa.sales_activities_code, sa.sales_activities_quantity, sa.sales_activities_operator, sa.sales_activities_amount, o.currency, o.currency_value, 
                                    pp.settlement_account 
                                FROM " . TABLE_SALES_ACTIVITIES . " AS sa
                                LEFT JOIN " . TABLE_ORDERS . " AS o
                                        ON sa.sales_activities_orders_id = o.orders_id
                                LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei ON sa.sales_activities_orders_id = oei.orders_id 
                                LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON sa.sales_activities_orders_products_id = op.orders_products_id
                                LEFT JOIN " . TABLE_PIPWAVE_PAYMENT . " AS pp ON pp.orders_id = sa.sales_activities_orders_id 
                                WHERE " . $start_date_str . "
                                        AND " . $end_date_str . "
                                        AND " . $activity_codes_str . "
                                        AND	" . $payment_gateways_str . "
                                        AND oei.orders_extra_info_key = 'site_id' " . $order_type_str . "
                                ORDER BY sa.sales_activities_date DESC, sa.sales_activities_orders_id";
            $show_records = $_SESSION[$session_name]["show_records"];
            // echo $select_sql;
            if ($show_records != "ALL") {
                $sc_statement_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $select_sql, $select_sql_numrows, true);
            } //end if

            $result_sql = tep_db_query($select_sql);

            ob_start();
            ?>
            <br>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                            <tr height="20">
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_DATETIME ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_ACTIVITY_CODE ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_ORDER_ID ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_ENTITY ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_ORDER_STATUS ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_CATEGORY_PATH ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_PAYMENT_GATEWAY ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_QUANTITY ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_AMOUNT ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_CURRENCY ?></td>
                                <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_COA_CUSTOMERS_NAME ?></td>
                                <td colspan="6" width="180px" class="reportBoxHeading" nowrap>
                                    <?= TABLE_HEADING_COA_ORDER_AMOUNT_BREAKDOWN ?>
                                </td>
                            </tr>
                            <tr>
                                <td width="90px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_COA_PG ?></td>
                                <td width="90px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_COA_SC ?></td>
                                <td width="90px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_COA_TOTAL_PG ?></td>
                                <td width="90px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_COA_TOTAL_SC ?></td>
                            </tr>
                            <?php
                            $row_count = 0;

                            $payment_methods_obj = new payment_methods('all');
                            $payment_info_array = $payment_methods_obj->payment_methods_array;

                            while ($result_row = tep_db_fetch_array($result_sql)) {
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                $orders_details = $this->_get_orders_details($result_row['sales_activities_orders_id']);
                                $orders_products_details = $this->_get_orders_products_details($result_row['sales_activities_orders_products_id']);
                                $orders_products_is_compensated = $this->_check_orders_products_is_compensate($result_row['sales_activities_orders_products_id']);

                                $sales_activities_date_str = $result_row['sales_activities_date'];

                                $sales_activities_code_str = $result_row['sales_activities_code'];
                                foreach ($this->activity_codes as $activity_codes) {
                                    if (in_array($result_row['sales_activities_code'], $activity_codes)) {
                                        $sales_activities_code_str = $activity_codes['text'];
                                    } //end if
                                } //end foreach

                                $RD_status = $this->_get_RD_status($result_row['sales_activities_orders_id']);
                                $RD_status_str = '';
                                if ($RD_status) {
                                    $RD_status_str = '*';
                                } //end if
                                
                                // Order's Entity ( Settlement Account )
                                $settlement = $result_row["settlement_account"];
                                if (isset($result_row["settlement_account"]) && !empty($result_row["settlement_account"])) {
                                    preg_match('#\[(.*?)\]#', $result_row["settlement_account"], $matches);
                                    $settlement = isset($matches[1]) ? strtoupper($matches[1]) : $result_row["settlement_account"];
                                } else if ($orders_details['payment_methods_parent_id'] == 0 && $orders_details['payment_methods_id'] == 0) {
                                   if($orders_details['site_id'] == 5){
                                        $settlement = "G2GSG";
                                   } else {
                                    $settlement = "OGSG";
                                   }
                                }
                                $sales_activities_orders_settlement_str = $settlement;

                                $sales_activities_orders_status_str = $orders_details['orders_status_name'];
                                $sales_activities_category_path_str = $orders_products_details['products_cat_path'];
                                $sales_activities_products_name_str = $orders_products_details['products_name'];

                                $sales_activities_payment_gateway_str = $payment_info_array[$orders_details['payment_methods_parent_id']]->title . ' - ' . $payment_info_array[$orders_details['payment_methods_id']]->title;
                                $sales_activities_quantity_str = $result_row['sales_activities_quantity'];

                                if ($orders_products_is_compensated['is_compensated'] == true) {
                                    $sales_activities_currency_str = $orders_products_is_compensated['compensate_order_currency'];
                                    $sales_activities_currency_value_str = $orders_products_is_compensated['compensate_order_currency_value'];
                                } else {
                                    $sales_activities_currency_str = $result_row['currency'];
                                    $sales_activities_currency_value_str = $result_row['currency_value'];
                                }

                                $sales_activities_amount_str = $result_row['sales_activities_amount'];
                                $sales_activities_amount_class_name = 'greenIndicator';
                                if ($result_row['sales_activities_operator'] == '-') {
                                    $sales_activities_amount_class_name = 'redIndicator';
                                    $sales_activities_amount_str = '(' . ($sales_activities_amount_str * $sales_activities_currency_value_str) . ')';
                                } //end if

                                $sales_activities_customers_name_str = $orders_details['customers_name'];

                                $sales_activities_delivery_info = $this->_get_sales_activities_delivery_info($result_row['sales_activities_orders_id'], $result_row['sales_activities_id'], $result_row['sales_activities_amount'], $result_row['sales_activities_operator'], $result_row['currency_value']);
                                $orders_total_info = $this->_get_orders_total_info($result_row['sales_activities_orders_id']);

                                $order_obj = new edit_order($this->identity, $this->identity_email, $result_row['sales_activities_orders_id']);
                                $sc_used_info = $order_obj->get_sc_used();
                                $sales_activities_amount_pg_str = '';
                                $sales_activities_amount_sc_str = '';

                                $orders_amount_pg_str = '';
                                $orders_amount_sc_r_str = '';
                                $orders_amount_sc_nr_str = '';

                                if ($result_row['sales_activities_code'] == 'D' || $result_row['sales_activities_code'] == 'RD' || $result_row['sales_activities_code'] == 'RFD' || $result_row['sales_activities_code'] == 'RFRD') {
                                    if ($orders_total_info > 0) {
                                        if ($sales_activities_delivery_info['sales_activities_amount_pg'] > 0) {
                                            $sales_activities_amount_pg_str = number_format($sales_activities_delivery_info['sales_activities_amount_pg'], 4, '.', '');
                                        } //end if

                                        if ($orders_total_info > 0) {
                                            $orders_amount_pg_str = $orders_total_info;
                                        } //end if
                                    } //end if

                                    $sales_activities_amount_sc_str = (isset($sales_activities_delivery_info['sales_activities_amount_sc']) && $sales_activities_delivery_info['sales_activities_amount_sc'] > 0) ? number_format($sales_activities_delivery_info['sales_activities_amount_sc'], 4, '.', '') : '';

                                    $orders_amount_sc_str = (isset($sc_used_info) && $sc_used_info > 0) ? number_format($sc_used_info, 4, '.', '') : '';
                                } //end if
                                ?>
                                <tr height="20" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_date_str ?></td>
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_code_str ?></td>
                                    <td valign="top" align="center" class="reportRecords" nowrap>
                                        <a href="<?= tep_href_link("orders.php", 'oID=' . $result_row['sales_activities_orders_id'] . '&action=edit', 'NONSSL') ?>" target="_new">
                                            <?= $result_row['sales_activities_orders_id'] ?>
                                        </a>
                                        <span class="redIndicator"><?= $RD_status_str ?></span>
                                    </td>
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_orders_settlement_str ?></td>
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_orders_status_str ?></td>
                                    <td valign="top" class="reportRecords"><?= $sales_activities_category_path_str ?> > <?= $sales_activities_products_name_str ?></td>
                                    <td valign="top" class="reportRecords"><?= $sales_activities_payment_gateway_str ?></td>
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_quantity_str ?></td>
                                    <td valign="top" class="reportRecords" nowrap><span class="<?= $sales_activities_amount_class_name ?>"><?= is_numeric($sales_activities_amount_str) ? $sales_activities_amount_str * $sales_activities_currency_value_str : $sales_activities_amount_str ?></span></td>
                                    <td valign="top" class="reportRecords" nowrap><?= $sales_activities_currency_str ?></td>
                                    <td valign="top" class="reportRecords"><?= $sales_activities_customers_name_str ?></td>
                                    <td valign="top" class="reportRecords">
                                        <?= is_numeric($sales_activities_amount_pg_str) ? $sales_activities_amount_pg_str * $sales_activities_currency_value_str : '' ?>
                                    </td>
                                    <td valign="top" class="reportRecords">
                                        <?= is_numeric($sales_activities_amount_sc_str) ? $sales_activities_amount_sc_str * $sales_activities_currency_value_str : '' ?>
                                    </td>
                                    <td valign="top" class="reportRecords">
                                        <?= is_numeric($orders_amount_pg_str) ? $orders_amount_pg_str * $sales_activities_currency_value_str : '' ?>
                                    </td>
                                    <td valign="top" class="reportRecords">
                                        <?= is_numeric($orders_amount_sc_str) ? $orders_amount_sc_str : '' ?>
                                    </td>
                                </tr>
                                <?
                                $row_count++;
                            } //end while

                            unset($orders_id_RD_status);
                            ?>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                            <tr>
                                <td class="smallText" valign="top"><?= $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($result_sql) > 0 ? "1" : "0", tep_db_num_rows($result_sql), tep_db_num_rows($result_sql)) : $sc_statement_split_object->display_count($select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                                <td class="smallText" align="right"><?= $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $sc_statement_split_object->display_links($select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <?
                        echo tep_draw_form('export_csv_form', FILENAME_CUSTOMERS_ORDER_ACTIVITIES, tep_get_all_get_params(array('action')) . 'action=export_csv', 'post', '');
                        ?>
                        <table width="100%" border="0" cellspacing="0" cellpadding="2">
                            <tr>
                                <td align="right">
                                    <? echo tep_submit_button('Export', 'Export as csv file', 'name="btn_export_csv"', 'inputButton'); ?>
                                </td>
                            </tr>
                        </table>
                        </form>
                    </td>
                </tr>
            </table>
            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();
        }
        // This should be here to let all the session get updated value
        $search_section_html = $this->search_coa($filename, $session_name);

        return $search_section_html . "\n" . $report_section_html;
    }

    function export_coa($filename, $session_name) {
        global $currencies, $languages_id, $view_coa_report_permission;

        if (!tep_not_null($_SESSION[$session_name]["start_date"]) || !tep_not_null($_SESSION[$session_name]["end_date"]))
            return "";
        if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
            $startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
            list($yr, $mth, $day) = explode('-', $startDateObj[0]);
            list($hr, $min) = explode(':', $startDateObj[1]);
            $start_date_str = " ( sa.sales_activities_date >= '" . date('Y-m-d H:i:s', mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
        } else {
            list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
            $start_date_str = " ( sa.sales_activities_date >= '" . date('Y-m-d H:i:s', mktime(0, 0, 0, $mth, $day, $yr)) . "' )";
        } //end if

        if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
            $endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
            list($yr, $mth, $day) = explode('-', $endDateObj[0]);
            list($hr, $min) = explode(':', $endDateObj[1]);
            $end_date_str = " ( sa.sales_activities_date <= '" . date('Y-m-d H:i:s', mktime((int) $hr, (int) $min, 59, $mth, $day, $yr)) . "' )";
        } else {
            list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
            $end_date_str = " ( sa.sales_activities_date <= '" . date('Y-m-d H:i:s', mktime(23, 59, 59, $mth, $day, $yr)) . "' )";
        } //end if
        // get activity_codes wanted in search
        $activity_codes_str = "";
        if (tep_not_null($_SESSION[$session_name]["activity_codes_to"])) {
            $total_index = count($_SESSION[$session_name]["activity_codes_to"]);
            for ($current_index = 0; $current_index < ($total_index - 1); $current_index++) {
                $current_activity_codes_id = $_SESSION[$session_name]["activity_codes_to"][$current_index];
                $activity_codes_str = $activity_codes_str . "'" . $current_activity_codes_id . "',";
            } //end for
            $current_activity_codes_id = $_SESSION[$session_name]["activity_codes_to"][$total_index - 1];
            $activity_codes_str = $activity_codes_str . "'" . $current_activity_codes_id . "'";
            $activity_codes_str = "sa.sales_activities_code IN (" . $activity_codes_str . ")";
        } else {
            $activity_codes_str = " 1 ";
        } //end if
        // get payment_gateways wanted in search
        $payment_gateways_str = '';
        if (count($_SESSION[$session_name]["payment_gateways_to"])) {
            $actual_selected_payment_method = $_SESSION[$session_name]["payment_gateways_to"];
            if (in_array("OGM_CREDITS", $_SESSION[$session_name]["payment_gateways_to"])) {
                $search_index_key = array_search('OGM_CREDITS', $actual_selected_payment_method);
                $actual_selected_payment_method[$search_index_key] = '';
            }

            $payment_gateways_str = "o.payment_methods_parent_id IN ('" . implode("', '", $actual_selected_payment_method) . "')";
        } else {
            $payment_gateways_str = " 1 ";
        } //end if
        // get order_site wanted in search
        $order_type_str = '';
        if (tep_not_null($_SESSION[$session_name]["order_site"])) {
            $order_site_str = '';
            $orders_extra_info_str = '';
            switch ($_SESSION[$session_name]["order_site"]) {
                case '4':
                    $order_type_str = "oei.orders_extra_info_value = '5'";
                    break;
                case '3':
                    $order_type_str = "oei.orders_extra_info_value != '5' AND op.parent_orders_products_id = 0 AND op.custom_products_type_id = '3'";
                    break;
                case '2':
                    $order_type_str = "oei.orders_extra_info_value != '5' AND op.parent_orders_products_id = 0 AND op.custom_products_type_id != '3'";
                    break;
                default:
                    $order_type_str = "oei.orders_extra_info_value != '5'";
                    break;
            }
            $orders_extra_info_str = "LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei ON sa.sales_activities_orders_id = oei.orders_id LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON sa.sales_activities_orders_products_id = op.orders_products_id";
            $order_type_str = "AND orders_extra_info_key = 'site_id' AND " . $order_type_str;
        } //end if

        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
        $select_sql = "	SELECT sa.sales_activities_id, sa.sales_activities_orders_id, sa.sales_activities_orders_products_id, DATE_FORMAT(sa.sales_activities_date, '%Y-%m-%d %H:%i') AS sales_activities_date, sa.sales_activities_code, sa.sales_activities_quantity, sa.sales_activities_operator, sa.sales_activities_amount, o.currency, o.currency_value, 
                            pp.settlement_account 
                        FROM " . TABLE_SALES_ACTIVITIES . " AS sa
                        LEFT JOIN " . TABLE_ORDERS . " AS o
                                ON sa.sales_activities_orders_id = o.orders_id
                        " . $orders_extra_info_str . "
                        LEFT JOIN " . TABLE_PIPWAVE_PAYMENT . " AS pp ON pp.orders_id = sa.sales_activities_orders_id 
                        WHERE " . $start_date_str . "
                                AND " . $end_date_str . "
                                AND " . $activity_codes_str . "
                                AND	" . $payment_gateways_str . "
                                " . $order_type_str . "
                        ORDER BY sa.sales_activities_date DESC, sa.sales_activities_orders_id";

        $show_records = $_SESSION[$session_name]["show_records"];
        $result_sql = tep_db_query($select_sql);

        $payment_methods_obj = new payment_methods('all');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        $custom_product_array = array();
        $product_type_select_sql = "SELECT custom_products_type_id, custom_products_type_name
                                    FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                                    WHERE 1";
        $product_type_result_sql = tep_db_query($product_type_select_sql);
        while ($product_type_row = tep_db_fetch_array($product_type_result_sql)) {
            $custom_product_array[$product_type_row['custom_products_type_id']] = $product_type_row['custom_products_type_name'];
        }

        if (tep_db_num_rows($result_sql)) {
            $export_csv_content = '"' . HEADER_FORM_CUSTOMERS_ORDER_ACTIVITIES_TITLE . '"';
            $export_csv_content = $export_csv_content . "\n";
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '"Start Date",":","' . $_SESSION[$session_name]["start_date"] . '"';
            $export_csv_content = $export_csv_content . "\n";
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '"End Date",":","' . $_SESSION[$session_name]["end_date"] . '"';
            $export_csv_content = $export_csv_content . "\n";
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '"' . ENTRY_COA_ACTIVITY_CODE . '",":","' . $_SESSION[$session_name]["activity_codes_to"][0] . '"';
            $export_csv_content = $export_csv_content . "\n";
            for ($index = 1; $index < count($_SESSION[$session_name]["activity_codes_to"]); $index++) {
                $export_csv_content = $export_csv_content . ',,"' . $_SESSION[$session_name]["activity_codes_to"][$index] . '"';
                $export_csv_content = $export_csv_content . "\n";
            } //end for
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '"' . ENTRY_COA_PAYMENT_GATEWAY . '",":","' . $_SESSION[$session_name]["payment_gateways_to"][0] . '"';
            $export_csv_content = $export_csv_content . "\n";
            for ($index = 1; $index < count($_SESSION[$session_name]["payment_gateways_to"]); $index++) {
                $export_csv_content = $export_csv_content . ',,"' . $_SESSION[$session_name]["payment_gateways_to"][$index] . '"';
                $export_csv_content = $export_csv_content . "\n";
            } //end for
            $export_csv_content = $export_csv_content . "\n";
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '' . ',' .
                    '"' . TABLE_HEADING_COA_ORDER_AMOUNT_BREAKDOWN . '",' .
                    '';
            $export_csv_content = $export_csv_content . "\n";

            $export_csv_content = $export_csv_content .
                    '"' . TABLE_HEADING_COA_DATETIME . '",' .
                    '"' . TABLE_HEADING_COA_ACTIVITY_CODE . '",' .
                    '"' . TABLE_HEADING_COA_ORDER_ID . '",' .
                    '' . ',' .
                    '"' . TABLE_HEADING_COA_ENTITY . '",' .
                    '"' . TABLE_HEADING_COA_ORDER_STATUS . '",' .
                    '"' . TABLE_HEADING_COA_CATEGORY_PATH . '",' .
                    '"Product Type",' .
                    '"' . TABLE_HEADING_COA_PAYMENT_GATEWAY . '",' .
                    '"' . TABLE_HEADING_COA_QUANTITY . '",' .
                    '"' . TABLE_HEADING_COA_AMOUNT . '",' .
                    '"' . TABLE_HEADING_COA_CURRENCY . '",' .
                    '"USD Amount",' .
                    '"Remark",' .
                    '"' . TABLE_HEADING_COA_CUSTOMERS_NAME . '",' .
                    '"' . TABLE_HEADING_COA_PG . '",' .
                    '"' . TABLE_HEADING_COA_SC . '",' .
                    '"' . TABLE_HEADING_COA_TOTAL_PG . '",' .
                    '"' . TABLE_HEADING_COA_TOTAL_SC . '"';

            while ($result_row = tep_db_fetch_array($result_sql)) {
                $orders_details = $this->_get_orders_details($result_row['sales_activities_orders_id']);
                $orders_products_details = $this->_get_orders_products_details($result_row['sales_activities_orders_products_id']);
                $orders_products_is_compensated = $this->_check_orders_products_is_compensate($result_row['sales_activities_orders_products_id']);

                $sales_activities_date_str = $result_row['sales_activities_date'];

                $sales_activities_code_str = $result_row['sales_activities_code'];
                foreach ($this->activity_codes as $activity_codes) {
                    if (in_array($result_row['sales_activities_code'], $activity_codes)) {
                        $sales_activities_code_str = $activity_codes['text'];
                    } //end if
                } //end foreach

                $RD_status = $this->_get_RD_status($result_row['sales_activities_orders_id']);
                $RD_status_str = '';
                if ($RD_status) {
                    $RD_status_str = '*';
                } //end if
                
                // Order's Entity ( Settlement Account )
                $settlement = $result_row["settlement_account"];
                if (isset($result_row["settlement_account"]) && !empty($result_row["settlement_account"])) {
                    preg_match('#\[(.*?)\]#', $result_row["settlement_account"], $matches);
                    $settlement = isset($matches[1]) ? strtoupper($matches[1]) : $result_row["settlement_account"];
                } else if ($orders_details['payment_methods_parent_id'] == 0 && $orders_details['payment_methods_id'] == 0) {
                    $settlement = "OGSG";
                }
                $sales_activities_orders_settlement_str = $settlement;

                $sales_activities_orders_status_str = $orders_details['orders_status_name'];
                $sales_activities_category_path_str = $orders_products_details['products_cat_path'];
                $sales_activities_products_name_str = str_replace(array('"', '<br>', '<BR>'), array('""', ''), $orders_products_details['products_name']);
                $sales_activities_payment_gateway_str = $payment_info_array[$orders_details['payment_methods_parent_id']]->title . ' - ' . $payment_info_array[$orders_details['payment_methods_id']]->title;
                $sales_activities_quantity_str = $result_row['sales_activities_quantity'];

                if ($orders_products_is_compensated['is_compensated'] == true) {
                    $sales_activities_currency_str = $orders_products_is_compensated['compensate_order_currency'];
                    $sales_activities_currency_value_str = $orders_products_is_compensated['compensate_order_currency_value'];
                    $sales_activities_remark = $orders_products_is_compensated['remark'];
                } else {
                    $sales_activities_currency_str = $result_row['currency'];
                    $sales_activities_currency_value_str = $result_row['currency_value'];
                    $sales_activities_remark = '';
                }

                $sales_activities_amount_str = $result_row['sales_activities_amount'];
                $sales_activities_amount_class_name = 'greenIndicator';
                if ($result_row['sales_activities_operator'] == '-') {
                    $sales_activities_amount_class_name = 'redIndicator';
                    $sales_activities_amount_str = '(' . ($sales_activities_amount_str * $sales_activities_currency_value_str) . ')';
                } //end if

                $sales_activities_customers_name_str = tep_mb_convert_encoding(str_replace(array('"', '<br>', '<BR>'), array('&quot;', ''), $orders_details['customers_name']), 'EUC-CN', 'UTF-8');

                $sales_activities_delivery_info = $this->_get_sales_activities_delivery_info($result_row['sales_activities_orders_id'], $result_row['sales_activities_id'], $result_row['sales_activities_amount'], $result_row['sales_activities_operator'], $result_row['currency_value']);
                $orders_total_info = $this->_get_orders_total_info($result_row['sales_activities_orders_id']);

                $order_obj = new edit_order($this->identity, $this->identity_email, $result_row['sales_activities_orders_id']);
                $sc_used_info = $order_obj->get_sc_used();

                $sales_activities_amount_pg_str = '';
                $sales_activities_amount_sc_str = '';

                $orders_amount_pg_str = '';
                $orders_amount_sc_r_str = '';
                $orders_amount_sc_nr_str = '';

                if ($result_row['sales_activities_code'] == 'D' || $result_row['sales_activities_code'] == 'RD' || $result_row['sales_activities_code'] == 'RFD' || $result_row['sales_activities_code'] == 'RFRD') {
                    if ($orders_total_info > 0) {
                        if ($sales_activities_delivery_info['sales_activities_amount_pg'] > 0) {
                            $sales_activities_amount_pg_str = number_format($sales_activities_delivery_info['sales_activities_amount_pg'], 4, '.', '');
                        } //end if

                        if ($orders_total_info > 0) {
                            $orders_amount_pg_str = $orders_total_info;
                        } //end if
                    } //end if

                    $sales_activities_amount_sc_str = (isset($sales_activities_delivery_info['sales_activities_amount_sc']) && $sales_activities_delivery_info['sales_activities_amount_sc'] > 0) ? number_format($sales_activities_delivery_info['sales_activities_amount_sc'], 4, '.', '') : '';

                    $orders_amount_sc_str = (isset($sc_used_info) && $sc_used_info > 0) ? number_format($sc_used_info, 4, '.', '') : '';
                } //end if

                $export_csv_content = $export_csv_content .
                        "\n" .
                        '"' . $sales_activities_date_str . '",' .
                        '"' . $sales_activities_code_str . '",' .
                        '"' . $result_row['sales_activities_orders_id'] . '",' .
                        '"' . $RD_status_str . '",' .
                        '"' . $sales_activities_orders_settlement_str . '",' .
                        '"' . $sales_activities_orders_status_str . '",' .
                        '"' . $sales_activities_category_path_str . ' > ' . $sales_activities_products_name_str . '",' .
                        '"' . $custom_product_array[$orders_products_details['custom_products_type_id']] . '",' .
                        '"' . $sales_activities_payment_gateway_str . '",' .
                        '"' . str_replace(",", "", $sales_activities_quantity_str) . '",' .
                        '"' . str_replace(",", "", (is_numeric($sales_activities_amount_str) ? $sales_activities_amount_str * $sales_activities_currency_value_str : $sales_activities_amount_str)) . '",' .
                        '"' . $sales_activities_currency_str . '",' .
                        '"' . $sales_activities_amount_str . '",' .
                        '"' . str_replace(",", "", $sales_activities_remark) . '",' .
                        '"' . $sales_activities_customers_name_str . '",' .
                        '"' . (is_numeric($sales_activities_amount_pg_str) ? $sales_activities_amount_pg_str * $sales_activities_currency_value_str : '') . '",' .
                        '"' . (is_numeric($sales_activities_amount_sc_str) ? $sales_activities_amount_sc_str * $sales_activities_currency_value_str : '') . '",' .
                        '"' . (is_numeric($orders_amount_pg_str) ? $orders_amount_pg_str * $sales_activities_currency_value_str : '') . '",' .
                        '"' . (is_numeric($orders_amount_sc_str) ? $orders_amount_sc_str : '') . '"';
            } //end while

            unset($orders_id_RD_status);
            return $export_csv_content;
        }
        return '';
    }

    function _get_all_active_receive_payment_methods() {
        $payment_module_info = array(array('id' => 'OGM_CREDITS', 'text' => 'Full Store Credit'));

        $payment_methods_obj = new payment_methods('payment_gateways');
        $payment_info_array = $payment_methods_obj->payment_gateways_array;

        foreach ($payment_info_array as $payment_gateways_id => $payment_gateways_data) {
            $payment_module_info[] = array('id' => $payment_gateways_id, 'text' => $payment_gateways_data->title);
        }

        return $payment_module_info;
    }

    function _get_RD_status($orders_id) {
        $undelivery_select_sql = "	SELECT sales_activities_id 
									FROM " . TABLE_SALES_ACTIVITIES . " AS sa
									WHERE sa.sales_activities_code = 'RD'
										AND	sa.sales_activities_orders_id = " . tep_db_input($orders_id) . "
									LIMIT 1";
        $undelivery_result_sql = tep_db_query($undelivery_select_sql);

        $issue_sc_select_sql = "	SELECT store_credit_history_id 
									FROM " . TABLE_STORE_CREDIT_HISTORY . " 
									WHERE store_credit_history_trans_type = 'C' 
										AND	store_credit_history_trans_id = '" . tep_db_input($orders_id) . "' 
										AND store_credit_activity_type = 'R' 
									LIMIT 1";
        $issue_sc_result_sql = tep_db_query($issue_sc_select_sql);

        if (tep_db_num_rows($undelivery_result_sql) + tep_db_num_rows($issue_sc_result_sql) > 0) {
            return true;
        } else {
            return false;
        }
    }

    function _get_orders_details($orders_id) {
        $select_sql = "SELECT o.orders_id as orders_id, os.orders_status_name as orders_status_name, o.payment_methods_parent_id, o.payment_methods_id, o.customers_name as customers_name, o.currency_value as currency_value ,oei.orders_extra_info_value as site_id
						FROM " . TABLE_ORDERS . " AS o
						LEFT JOIN " . TABLE_ORDERS_STATUS . " AS os
                            ON o.orders_status = os.orders_status_id
                        INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei
                            ON oei.orders_id = o.orders_id and oei.orders_extra_info_key = 'site_id'
						WHERE o.orders_id = " . tep_db_input($orders_id);

        $result_sql = tep_db_query($select_sql);
        $result_row = tep_db_fetch_array($result_sql);

        return $result_row;
    }

    function _get_orders_products_details($orders_products_id) {
        $select_sql = " SELECT op.products_name, op.custom_products_type_id, p.products_cat_path, op.products_id 
						FROM " . TABLE_ORDERS_PRODUCTS . " AS op
						LEFT JOIN " . TABLE_PRODUCTS . " AS p
							ON op.products_id = p.products_id
						WHERE op.orders_products_id = " . tep_db_input($orders_products_id);

        $result_sql = tep_db_query($select_sql);
        $result_row = tep_db_fetch_array($result_sql);

        if ($result_row['products_id'] == '-1') {
            $result_row['custom_products_type_id'] = '3';
        }

        return $result_row;
    }

//end function

    function _check_orders_products_is_compensate($orders_products_id) {
        $select_sql = " SELECT ocp.compensate_order_currency, ocp.compensate_order_currency_value, orders_compensate_products_messages, orders_compensate_products_added_by
						FROM " . TABLE_ORDERS_COMPENSATE_PRODUCTS . " AS ocp
						WHERE ocp.orders_products_id = " . tep_db_input($orders_products_id);
        $result_sql = tep_db_query($select_sql);
        if ($result_row = tep_db_fetch_array($result_sql)) {
            $result_row['is_compensated'] = true;
            $result_row['remark'] = 'Added by: ' . $result_row['orders_compensate_products_added_by'] . "\r" . $result_row['orders_compensate_products_messages'];
        } else {
            $result_row = array('is_compensated' => false, 'compensate_order_currency' => '', 'compensate_order_currency_value' => '0');
        }

        return $result_row;
    }

    function _get_orders_total_info($orders_id) {
        // ***** orders_total ***** //
        $order_total_select_sql = "	SELECT ot.value
									FROM " . TABLE_ORDERS_TOTAL . " AS ot
									WHERE ot.orders_id = " . tep_db_input($orders_id) . "
										AND class='ot_total'";
        $order_total_result_sql = tep_db_query($order_total_select_sql);
        $order_total_row = tep_db_fetch_array($order_total_result_sql);

        return $order_total_row['value'];
    }

//end function

    function _get_previous_total_sales_activities_delivered_price($orders_id, $sales_activities_id) {
        $previous_total_sales_activities_delivered_price = 0.0000;

        $select_sql = "	SELECT sales_activities_operator, sa.sales_activities_amount
						FROM " . TABLE_SALES_ACTIVITIES . " AS sa
						WHERE sa.sales_activities_orders_id = " . tep_db_input($orders_id) . "
							AND sa.sales_activities_id < " . tep_db_input($sales_activities_id) . "
							AND (	sa.sales_activities_code = 'D' 
									OR sa.sales_activities_code = 'RD' 
									OR sa.sales_activities_code = 'RFD' 
									OR sa.sales_activities_code = 'RFRD'
								)";
        $result_sql = tep_db_query($select_sql);
        while ($result_row = tep_db_fetch_array($result_sql)) {
            if ($result_row['sales_activities_operator'] == "+") {
                $previous_total_sales_activities_delivered_price = $previous_total_sales_activities_delivered_price + $result_row['sales_activities_amount'];
            } else {
                $previous_total_sales_activities_delivered_price = $previous_total_sales_activities_delivered_price - $result_row['sales_activities_amount'];
            }
        } //end while

        return $previous_total_sales_activities_delivered_price;
    }

//end function

    function _get_sales_activities_delivery_info($orders_id, $sales_activities_id, $sales_activities_amount, $sales_activities_operator = "+", $order_currency_value = "1") {
        global $currencies;
        $sales_activities_delivery_info = array();

        $order_obj = new edit_order($this->identity, $this->identity_email, $orders_id);
        $sc_used_info = $order_obj->get_sc_used_with_currency();
        
        if ($sc_used_info['currency'] != DEFAULT_CURRENCY) {
            $sc_used_info['amount'] = $sc_used_info['amount'] / $order_currency_value;
        }

        if ($sales_activities_operator == "+") {
            $previous_total_sales_activities_delivered_price = $this->_get_previous_total_sales_activities_delivered_price($orders_id, $sales_activities_id);
            $new_total_sales_activities_delivered_price = $previous_total_sales_activities_delivered_price + $sales_activities_amount;
        } else {
            $new_total_sales_activities_delivered_price = $this->_get_previous_total_sales_activities_delivered_price($orders_id, $sales_activities_id);
            $previous_total_sales_activities_delivered_price = $new_total_sales_activities_delivered_price - $sales_activities_amount;
        }

        $sales_activities_delivery_info['sales_activities_amount_sc'] = 0.0000;
        $sales_activities_delivery_info['sales_activities_amount_pg'] = 0.0000;

        $total_sc_used = $sc_used_info['amount'];
        if ($total_sc_used > 0.0000) {
            /*             * **** get payment gateway and/or store credit value ***** */
            if ($previous_total_sales_activities_delivered_price > $total_sc_used) {
                $sales_activities_delivery_info['sales_activities_amount_sc'] = 0.0000;
                $sales_activities_delivery_info['sales_activities_amount_pg'] = $sales_activities_amount;
            } else {
                if ($new_total_sales_activities_delivered_price > $total_sc_used) {
                    $sales_activities_delivery_info['sales_activities_amount_sc'] = $total_sc_used - $previous_total_sales_activities_delivered_price;
                    $sales_activities_delivery_info['sales_activities_amount_pg'] = $new_total_sales_activities_delivered_price - $total_sc_used;
                } else {
                    $sales_activities_delivery_info['sales_activities_amount_sc'] = $sales_activities_amount;
                    $sales_activities_delivery_info['sales_activities_amount_pg'] = 0.0000;
                } //end if
            } //end if
        } else {
            $sales_activities_delivery_info['sales_activities_amount_sc'] = 0.0000;
            $sales_activities_delivery_info['sales_activities_amount_pg'] = $sales_activities_amount;
        } //end if

        return $sales_activities_delivery_info;
    }

//end function
}

//end class
?>
