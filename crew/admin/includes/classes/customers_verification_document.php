<?php

class customers_verification_document {

    public $max_dim, $log_id, $log_users_id, $customer_id;
    private $aws_obj, $upload_obj;
    private static $aws_enabled = 'true';
    /*
     * AWS needs temporary local storage to do editing
     */
    private static $local_setting = array(
        'permissions' => '0775',
        'source_path' => DIR_FS_DATA_LL_SUBMISSION_TEMP, //DIR_FS_DATA_LL_SUBMISSION,
        'editing_path' => DIR_FS_DATA_LL_SUBMISSION_TEMP,
    );
    private static $aws_s3_setting = array(
        'bucket' => 'BUCKET_UPLOAD',
        'acl' => 'ACL_PRIVATE',
        'storage' => 'STORAGE_STANDARD',
        'source_path' => 'user/verification',
        'editing_path' => 'user/verification/tmp',
    );
    private static $image_supported = array(
        'type' => array('gif', 'jpg', 'jpeg', 'png'),
        'size' => 512000
    );
    public static $doc_id_array = array(
        'doc_1' => '001',
        'doc_2' => '002',
        'doc_3' => '003',
        'doc_4' => '004',
        'doc_5' => '005'
    );

    public function __construct($login_user_id = NULL) {
        $this->upload_obj = new upload('', self::$local_setting['source_path'] . '/', self::$local_setting['permissions'], self::$image_supported['type'], '', self::$image_supported['size']);

        if (self::useAWS()) {
            $this->aws_obj = $this->upload_obj->set_amazon_api_transfer(array(
                'bucket' => self::$aws_s3_setting['bucket'],
                'filepath' => self::$aws_s3_setting['source_path'] . '/',
                'storage' => self::$aws_s3_setting['storage'],
                'acl' => self::$aws_s3_setting['acl'],
            ));
        }

        $this->setLoginUserID($login_user_id);
    }

    private function createIfNotExist($customer_id = NULL) {
        $this->setCustomerID($customer_id);

        $check_sql = "  SELECT customers_id
                        FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " 
                        WHERE customers_id = " . $this->getCustomerID();
        $check_query = tep_db_query($check_sql);
        if (!$check = tep_db_fetch_array($check_query)) {
            tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT, array('customers_id' => $this->getCustomerID()));
        }
    }

    private function getFilename($doc_id) {
        $return_array = array();

        if ($log_id = $this->getLogID()) {
            $return_array = $this->getFilenameFromLog($log_id);
        } else if ($customer_id = $this->getCustomerID()) {
            $return_array = $this->getFilenameFromCustomerID($doc_id, $customer_id);
        }

        return $return_array;
    }

    private function getFilenameFromCustomerID($doc_id, $customer_id) {
        $return_array = array();

        $customer_info_select_sql = "	SELECT customers_id, files_" . $doc_id . " as filename
                                        FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . "
                                        WHERE customers_id = " . $customer_id;
        $customers_query = tep_db_query($customer_info_select_sql);
        if ($customers = tep_db_fetch_array($customers_query)) {
            $return_array = array($customers['customers_id'], $doc_id, $customers['filename']);
        }

        return $return_array;
    }

    private function getFilenameFromLog($log_id) {
        $return_array = array();

        $customer_info_select_sql = "	SELECT log_customers_id, log_docs_id, log_filename
                                        FROM " . TABLE_VERIFICATION_DOC_LOG . "
                                        WHERE log_id = " . $log_id;
        $customers_query = tep_db_query($customer_info_select_sql);
        if ($customers = tep_db_fetch_array($customers_query)) {
            $return_array = array($customers['log_customers_id'], $customers['log_docs_id'], $customers['log_filename']);
        }

        return $return_array;
    }

    private function getImageResource($filename, $file_content) {
        $return_bool = FALSE;
        $filepath = self::$local_setting['editing_path'] . '/' . 'temp_' . $filename;

        if (($fh = @fopen($filepath, "w")) !== false) {
            fwrite($fh, $file_content);
            fclose($fh);

            if ($max_dim = $this->getMaxDim()) {
                $return_bool = resizeImage($filepath, $max_dim);
            } else {
                $return_bool = LoadImage($filepath);
            }

            if (file_exists($filepath)) {
                @unlink($filepath);
            }
        } else {
            $this->reportError(array(
                'error' => 'failed to write:' . $filepath
            ));
        }

        return $return_bool;
    }

    public function getLocalEditPath() {
        return self::$local_setting['editing_path'];
    }

    public static function useAWS() {
        return self::$aws_enabled === 'true';
    }

    private function getLogID() {
        return $this->log_id;
    }

    public function setLogID($log_id) {
        if (tep_not_null($log_id)) {
            $this->log_id = $log_id;
        }
    }

    private function getLoginUserID() {
        return $this->log_users_id;
    }

    private function setLoginUserID($user_id) {
        if (tep_not_null($user_id)) {
            $this->log_users_id = $user_id;
        }
    }

    private function getMaxDim() {
        return tep_not_empty($this->maxdim) ? $this->maxdim : 0;
    }

    public function setMaxDim($num) {
        $this->maxdim = $num;
    }

    private function getCustomerID() {
        return (int) $this->customer_id;
    }

    public function setCustomerID($customer_id) {
        if (tep_not_null($customer_id)) {
            $this->customer_id = $customer_id;
        }
    }

    private function getDecryptContent($encrypted_data_string) {
        $return_string = '';

        if (tep_not_empty($encrypted_data_string)) {
            $return_string = tep_decrypt_data($encrypted_data_string);
        }

        return $return_string;
    }

    private function getEncryptContent($tmp_file_server_path = '') {
        $return_string = '';
        $file_path = $tmp_file_server_path != '' ? $tmp_file_server_path : (isset($this->upload_obj->file['tmp_name']) ? $this->upload_obj->file['tmp_name'] : '');

        if ($file_path) {
            $theData = file_get_contents($file_path);
            $return_string = tep_encrypt_data(base64_encode($theData));

            unset($theData);
        }

        return $return_string;
    }

    private function getFullPath($source_filename_array, $return_array = false) {
        list($customer_id, $doc_id, $filename) = $source_filename_array;
        $ym = substr($filename, 0, 6);
        $return_str = $doc_id . '/' . (is_numeric($ym) ? $ym . '/' : '') . $customer_id . '/' . $filename;

        return $return_array ? explode('/', $return_str) : $return_str;
    }

//  ======================================  SAVE  ======================================

    public function saveDocument($doc_id, $temp_filename) {
        $return_bool = FALSE;
        $new_source_filename_array = array($this->getCustomerID(), $doc_id, date("YmdHis") . '.jpg');

        if (self::useAWS()) {
            $return_bool = $this->saveToAWS($new_source_filename_array, $temp_filename);
        } else {
            $return_bool = $this->saveToLocal($new_source_filename_array, $temp_filename);
        }

        if ($return_bool) {
            $new_source_filename = array_pop($new_source_filename_array);

            $this->update_filename($doc_id, $new_source_filename);
            $this->log($doc_id, $new_source_filename, 'MODIFY');
            // delete temp file
            $this->deleteTempFile($temp_filename, '');
        }

        return $return_bool;
    }

    private function saveToAWS($new_source_filename_array, $temp_filename) {
        $source_arr = array(
            'bucket' => self::$aws_s3_setting['bucket'],
            'filename' => self::$aws_s3_setting['editing_path'] . '/' . $temp_filename
        );
        $dest_arr = array(
            'bucket' => self::$aws_s3_setting['bucket'],
            'filename' => self::$aws_s3_setting['source_path'] . '/' . $this->getFullPath($new_source_filename_array),
        );

        return $this->aws_obj->copy_file($source_arr, $dest_arr);
    }

    private function saveToLocal($new_source_filename_array, $temp_filename) {
        return copy(self::$local_setting['editing_path'] . '/' . $temp_filename, self::$local_setting['source_path'] . '/' . implode('_', $new_source_filename_array));
    }

//  ======================================  COPY  ======================================

    public function copyDocumentToTemp($doc_id) {
        $return_bool = FALSE;

        if ($source_filename_array = $this->getFilename($doc_id)) {
            $temp_filename = 'temp_' . implode('_', $source_filename_array);

            if (self::useAWS()) {
                $return_bool = $this->copyToTempAWS($source_filename_array, $temp_filename);
            } else {
                $return_bool = $this->copyToTempLocal($source_filename_array, $temp_filename);
            }

            if ($return_bool) {
                $return_bool = $temp_filename;
            }
        }

        return $return_bool;
    }

    private function copyToTempAWS($source_filename_array, $editing_filename) {
        $source_arr = array(
            'bucket' => self::$aws_s3_setting['bucket'],
            'filename' => self::$aws_s3_setting['source_path'] . '/' . $this->getFullPath($source_filename_array)
        );
        $dest_arr = array(
            'bucket' => self::$aws_s3_setting['bucket'],
            'filename' => self::$aws_s3_setting['editing_path'] . '/' . $editing_filename
        );

        return $this->aws_obj->copy_file($source_arr, $dest_arr);
    }

    private function copyToTempLocal($source_filename_array, $temp_filename) {
        $source_filepath = self::$local_setting['source_path'] . '/' . implode('_', $source_filename_array);
        $temp_filepath = self::$local_setting['editing_path'] . '/' . $temp_filename;

        return copy($source_filepath, $temp_filepath);
    }

//  ======================================  READ  ======================================
    public function readDocumentFullUrl($doc_id, $log_enabled = false) {
        $return_bool = FALSE;
        if ($source_filename_array = $this->getFilename($doc_id)) {
            if (self::useAWS()) {
                $server_path_array = $this->getFullPath($source_filename_array, true);
                $source_filename = array_pop($server_path_array);
                $encrypted_data_string = $this->readFromAWS(self::$aws_s3_setting['source_path'] . '/' . implode('/', $server_path_array) . '/', $source_filename);
            } else {
                $encrypted_data_string = $this->readFromLocal(self::$local_setting['source_path'] . '/', implode('_', $source_filename_array));
            }
            if ($resp_string = $this->getDecryptContent($encrypted_data_string)) {
                if ($log_enabled) {
                    $this->log($doc_id, $source_filename, 'VIEW');
                }
                $return_bool = $resp_string;
            }
        }
        if ($return_bool === FALSE) {
            $file_path = DIR_FS_ADMIN . 'images/no_image.gif';
            $return_bool = resizeImage($file_path, 200);
        }
        return $return_bool;
    }

    public function readDocument($doc_id, $log_enabled = true) {
        $return_bool = FALSE;

        if ($source_filename_array = $this->getFilename($doc_id)) {
            if (self::useAWS()) {
                $server_path_array = $this->getFullPath($source_filename_array, true);
                $source_filename = array_pop($server_path_array);

                $encrypted_data_string = $this->readFromAWS(self::$aws_s3_setting['source_path'] . '/' . implode('/', $server_path_array) . '/', $source_filename);
            } else {
                $encrypted_data_string = $this->readFromLocal(self::$local_setting['source_path'] . '/', implode('_', $source_filename_array));
            }

            if ($resp_string = $this->getDecryptContent($encrypted_data_string)) {
                if ($log_enabled) {
                    $this->log($doc_id, $source_filename, 'VIEW');
                }

                $return_bool = $this->getImageResource($source_filename, $resp_string);
            }
        }

        if ($return_bool === FALSE) {
            $file_path = DIR_FS_ADMIN . 'images/no_image.gif';
            $return_bool = resizeImage($file_path, 200);
        }

        return $return_bool;
    }

    public function readTEMPDocument($editing_filename, $remove_temp_file = FALSE) {
        $return_bool = FALSE;

        if (tep_not_empty($editing_filename)) {
            if (self::useAWS()) {
                $encrypted_data_string = $this->readFromAWS(self::$aws_s3_setting['editing_path'] . '/', $editing_filename);
            } else {
                $encrypted_data_string = $this->readFromLocal(self::$local_setting['editing_path'] . '/', $editing_filename);
            }

            if ($resp_string = $this->getDecryptContent($encrypted_data_string)) {
                $return_bool = $this->getImageResource($editing_filename, $resp_string);
            }
        }

        return $return_bool;
    }

    public function downloadTempDocument($editing_filename) {
        $return_bool = FALSE;

        if (tep_not_empty($editing_filename)) {
            if (self::useAWS()) {
                $encrypted_data_string = $this->readFromAWS(self::$aws_s3_setting['editing_path'] . '/', $editing_filename);
                $this->deleteTempFile($editing_filename);
            } else {
                $encrypted_data_string = $this->readFromLocal(self::$local_setting['editing_path'] . '/', $editing_filename);
            }

            if ($resp_string = $this->getDecryptContent($encrypted_data_string)) {
                $editing_path = self::$local_setting['editing_path'] . '/' . $editing_filename;

                $fh = fopen($editing_path, 'w') or die("can't open file");
                fwrite($fh, $resp_string);
                unset($resp_string);
                fclose($fh);

                $return_bool = TRUE;
            }
        }

        return $return_bool;
    }

    private function readFromLocal($filepath, $filename) {
        $return_string = '';
        $file_path = $filepath . $filename;

        if (file_exists($file_path)) {
            $return_string = file_get_contents($file_path);
        }

        return $return_string;
    }

    private function readFromAWS($filepath, $filename) {
        $this->aws_obj->set_filename($filename);
        $this->aws_obj->set_filepath($filepath);
        $theData = $this->aws_obj->get_file();

        if (isset($theData->status) && $theData->status == 200) {
            return $theData->body;
        } else {
            return '';
        }
    }

//  ======================================  UPLOAD  ======================================

    public function batchUpload($customer_id, $files_info) {
        $return_bool = FALSE;

        if (is_array($files_info)) {
            $return_bool = TRUE;

            $this->setCustomerID($customer_id);
            $this->createIfNotExist();

            foreach ($files_info AS $file_index => $file_array) {
                if (tep_not_null($file_array['name'])) {
                    $return_bool *= $this->uploadDocument($file_index);
                }
            }
        }

        return $return_bool;
    }

    public function uploadTempDocument($filepath, $server_filename) {
        $return_bool = FALSE;

        if ($file_content = $this->getEncryptContent($filepath . $server_filename)) {
            if (self::useAWS()) {
                if ($filename = $this->uploadToAWS(self::$aws_s3_setting['editing_path'] . '/', $server_filename, $file_content, '')) {
                    // delete local file
                    if (file_exists($filepath . $server_filename)) {
                        @unlink($filepath . $server_filename);
                    }

                    $return_bool = TRUE;
                }
            } else {
                if ($filename = $this->uploadToLocal(self::$local_setting['editing_path'] . '/', $server_filename, $file_content, '')) {
                    $return_bool = TRUE;
                }
            }
        }

        return $return_bool;
    }

    private function uploadDocument($file_index) {
        $return_bool = FALSE;
        $customer_id = $this->getCustomerID();

        if (tep_not_empty($customer_id) && isset(self::$doc_id_array[$file_index]) && $this->validateFile($file_index)) {
            $doc_id = self::$doc_id_array[$file_index];

            if ($file_content = $this->getEncryptContent()) {
                $this->upload_obj->set_output_messages('session');

                if (self::useAWS()) {
                    $server_path_array = $this->getFullPath(array($customer_id, $doc_id, date("YmdHis")), true);
                    $server_filename = array_pop($server_path_array);
                    $server_path = self::$aws_s3_setting['source_path'] . '/' . implode('/', $server_path_array) . '/';

                    // filename with file extension
                    if ($filename = $this->uploadToAWS($server_path, $server_filename, $file_content)) {
                        $this->update_filename($doc_id, $filename);
                        $this->log($doc_id, $filename, 'UPLOAD');
                        $return_bool = TRUE;
                    }
                } else {
                    $server_filename = $customer_id . '_' . $doc_id . '_' . date("YmdHis");

                    if ($filename = $this->uploadToLocal(self::$local_setting['source_path'] . '/', $server_filename, $file_content)) {
                        list(,, $filename) = explode('_', $filename);

                        $this->update_filename($doc_id, $filename);
                        $this->log($doc_id, $filename, 'UPLOAD');
                        $return_bool = TRUE;
                    }
                }
            }
        }

        return $return_bool;
    }

    private function uploadToLocal($filepath, $server_filename, $content, $image_size = 'small') {
        $return_bool = FALSE;
        $this->upload_obj->aws_enabled = FALSE;
        $this->upload_obj->set_destination($filepath);

        if ($this->upload_obj->save_content($content, $server_filename, $image_size)) {
            $return_bool = $this->upload_obj->filename;
        }

        return $return_bool;
    }

    private function uploadToAWS($filepath, $server_filename, $content, $image_size = 'small') {
        $return_bool = FALSE;

        if ($this->aws_obj->is_aws_s3_enabled() && $this->aws_obj->is_s3_bucket_config_enabled(self::$aws_s3_setting['bucket'])) {
            $this->upload_obj->set_amazon_filepath($filepath);

            if ($this->upload_obj->save_content($content, $server_filename, $image_size)) {
                $return_bool = $this->upload_obj->filename;
            } else {
                $this->reportError(array('Failed to upload to AWS S3.', 'bucket' => $this->aws_obj->aws_bucket_key), 'uploadDocument()');
            }
        } else {
            $this->reportError(array('AWS S3 is not enabled or the bucket is not enabled.', 'bucket' => $this->aws_obj->aws_bucket_key), 'uploadDocument()');
        }

        return $return_bool;
    }

//  ======================================  DELETE FILE  ======================================

    private function deleteTempFile($filename, $prefix = 'temp') {
        $return_bool = FALSE;
        $filename_array = explode('_', $filename);

        if (isset($filename_array[0]) && ($prefix === '' || $filename_array[0] == $prefix)) {
            if (self::useAWS()) {
                if ($this->aws_obj->delete_file($filename, self::$aws_s3_setting['editing_path'] . '/')) {
                    $return_bool = TRUE;
                }
            } else {
                $filepath = self::$local_setting['editing_path'] . '/' . $filename;

                if (file_exists($filepath)) {
                    @unlink($filepath);
                    $return_bool = TRUE;
                }
            }
        }

        return $return_bool;
    }

    private function validateFile($file_index) {
        $this->upload_obj->set_file($file_index);
        $this->upload_obj->set_output_messages('session');
        return $this->upload_obj->parse();
    }

    private function update_filename($doc_id, $filename) {
        $update_data_array = array(
            'files_' . $doc_id => $filename,
            'files_' . $doc_id . '_locked' => 1
        );
        tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT, $update_data_array, "update", " customers_id=" . $this->getCustomerID());
    }

    private function log($doc_id, $filename, $action = 'UPLOAD') {
        $insert_data_array = array(
            'log_users_id' => $this->getLoginUserID(),
            'log_users_type' => 'admin',
            'log_customers_id' => $this->getCustomerID(),
            'log_docs_id' => $doc_id,
            'log_IP' => tep_get_ip_address(),
            'log_datetime' => 'now()',
            'log_filename' => $filename,
            'log_action' => $action
        );

        return tep_db_perform(TABLE_VERIFICATION_DOC_LOG, $insert_data_array);
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Customers Verification Document Error - ' . date("F j, Y H:i");
        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>