<?php
/*
  	$Id: customers_verification_setting.php,v 1.2 2013/06/07 13:08:14 weichen Exp $
	
	Developer: <PERSON> Yen
*/

class customers_verification_setting {
	var $_list_opt = array ('email' => 'Email', 
							'telephone' => 'Telephone', 
							'files_001' => 'Purchase Authorization Form', 
							'files_002' => 'Utility Bill', 
							'files_003' => 'Photo Identification', 
							'files_004' => 'Front of Credit Card', 
							'files_005' => 'Back of Credit Card' );
	
	public function __construct() {
	}
	
	public function menuListing() {
		$_cpt = array();
		$entryCount = 0;
		$listing_html = '';
		
		$_cpt = tep_get_product_type();
		
		// pagination
		$_pg_sel_sql = "select distinct(countries_id) from " . TABLE_CUSTOMERS_VERIFICATION_SETTING;
		$page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $_pg_sel_sql, $_numrows);
		$_pg_res_sql = tep_db_query($_pg_sel_sql);
		$_numrows = tep_db_num_rows($_pg_res_sql);
		
		ob_start();
?>
		<script language="javascript">
		<!--
		function deleteentry(t,id) {
			answer = confirm('Are you sure to delete '+ (trim_str(t) != '' ? '<' + t + '> ' : '') + ' ?')
			if (answer !=0) { 
				jQuery.get("?action=delete&id="+id, function(data){
					if (data == "success")
						jQuery('tr[id^=row-'+id+'-]').fadeOut('slow');
				});
			}
		}
		//-->
		</script>
		
		<form name=listing>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td colspan="2">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td align="left" class="reportRecords">&nbsp;</td>
					<td align="right" class="reportRecords">[ <a href="?action=add_form"><?php echo LINK_ADD_SETTING; ?></a> ]</td>
				</tr>
				<tr>
					<td valign="top" colspan="2">
						<table border="0" width="100%" cellspacing="1" cellpadding="2">
		 					<tr>
								<?php
									$tbl_width = number_format((55 / count($_cpt)), 2) . '%';
									$tbl_rowspan = count($this->_list_opt);
								?>
		 						<td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
			 					<td class="reportBoxHeading" width="20%"><?php echo SUB_TABLE_HEADING_COUNTRY; ?></td>
								<td class="reportBoxHeading" width="15%" align=center><?php echo SUB_TABLE_HEADING_VERIFICATION_TYPE; ?></td>
								<?php
									foreach ($_cpt as $val) {
								?>
										<td class="reportBoxHeading" width="<?php echo $tbl_width; ?>" align=center><?php echo $val['text']; ?></td>
								<?php
									}
								?>
		 					</tr>
							<?php
								$_country_sel_sql = "	SELECT cvs.countries_id, c.countries_name 
														FROM " . TABLE_CUSTOMERS_VERIFICATION_SETTING . " AS cvs 
														LEFT JOIN " . TABLE_COUNTRIES . " AS c 
															ON c.countries_id = cvs.countries_id 
														GROUP BY cvs.countries_id 
														ORDER BY cvs.countries_id ";
								$_country_res_sql = tep_db_query($_country_sel_sql);
								if (tep_db_num_rows($_country_res_sql) > 0) {
									while ($_country_rows = tep_db_fetch_array($_country_res_sql)) {
										$init = true;
										$result = array();
										($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
										
										if ($_country_rows['countries_name'] == '') {
											$_country_rows['countries_name'] = TEXT_ALL_COUNTRY;
										}
										
										$_sel_sql = "	SELECT * 
														FROM " . TABLE_CUSTOMERS_VERIFICATION_SETTING . " 
														WHERE countries_id = '" . $_country_rows['countries_id'] . "' 
														ORDER BY custom_products_type";
										$_res_sql = tep_db_query($_sel_sql);
										while ($_rows = tep_db_fetch_array($_res_sql)) {
											foreach ($this->_list_opt as $key => $val) {
												$result[$val][$_rows['custom_products_type']] = ($_rows[$key] ? 'x' : '');
											}
										}
										
										foreach ($result as $key => $res) {
											if ($init == true) {
												$init = false;
							?>
											<tr id="row-<?php echo $_country_rows['countries_id']; ?>-" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
												<td class="reportRecords" rowspan="<?php echo $tbl_rowspan; ?>" valign="top" align=center>
													<a href="?selected_box=c2c&action=add_form&id=<?php echo $_country_rows['countries_id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
													<a href="javascript:void(deleteentry('<?php echo $_country_rows['countries_name']; ?>','<?php echo htmlentities($_country_rows['countries_id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
												</td>
												<td class="reportRecords" rowspan="<?php echo $tbl_rowspan; ?>" valign="top"><?php echo $_country_rows['countries_name']; ?></td>
							<?php
											} else {
							?>
											<tr id="row-<?php echo $_country_rows['countries_id'] . '-' . $key; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
							<?php
											}
							?>
												<td class="reportRecords" valign="top"><?php echo ucwords($key); ?></td>
							<?php
												foreach ($_cpt as $cpt_val) {
							?>
												<td class="reportRecords" align=center valign="top"><?php echo $res[$cpt_val['id']]; ?></td>
							<?php
												}
							?>
											</tr>
							<?php
										}
										$entryCount++;
									}
								} else {
							?>
									<tr class="reportListingEven">
										<td class="reportRecords" align="center" colspan="9"><i>Empty</i></td>
									</tr>
							<?php
								}
							?>
						</table>
		 			</td>
		 		</tr>
				<tr>
					<td valign="top" colspan="2">
						<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
							<tr>
								<td class="smallText" valign="top" nowrap><?php echo $page_split_object->display_count($_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_RESULT); ?></td>
								<td class="smallText" align="right"><?php echo $page_split_object->display_links($_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1"); ?></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	public function addForm($id = "") {
		$listing_html = '';
		
		$_cpt = array();
		$_countries = array();
		$result = array();
		
		$_cpt = tep_get_product_type();
		$_countries = array(array (	'id' => '', 
									'text' => LIST_COUNTRY_DEFAULT ), 
							array ( 'id' => '0', 
									'text' => TEXT_ALL_COUNTRY ) );
		$_countries = array_merge($_countries, tep_get_countries());
		
		if ($id != '') {
			$_sel_sql = "	SELECT * 
							FROM " . TABLE_CUSTOMERS_VERIFICATION_SETTING . " 
							WHERE countries_id = '" . $id . "' 
							ORDER BY custom_products_type";
			$_res_sql = tep_db_query($_sel_sql);
			if (tep_db_num_rows($_res_sql) > 0) {
				while ($_rows = tep_db_fetch_array($_res_sql)) {
					foreach ($this->_list_opt as $list_key => $list_val) {
						$result[$list_key][$_rows['custom_products_type']] = $_rows[$list_key];
					}
				}
			}
		}
		
		ob_start();
?>
		<script language="javascript">
		<!--
			function check_form() {
				var error = 0;
				var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";
				
				if (document.menu_form.country.value == "") {
					error_message = error_message + "* 'Country' entry must be selected.\n";
					error = 1;
				}
				
				if (error == 1) {
					alert(error_message);
					return false;
				} else {
					return true;
				}
			}
		//-->
		</script>
		
		<?=tep_draw_form('menu_form', FILENAME_CUSOMTERS_VERIFICATION_SETTING, 'action=add', 'post', ' onSubmit="return check_form();" id="menu_form"')?>
		<table cellspacing="2" cellpadding="2">
			<tr>
				<td>
					<table cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td class="pageHeading" valign="top">
								<?php 
									if ($id != "") { 
										echo HEADING_EDIT_MENU; 
										echo tep_draw_hidden_field('id', $id); 
									} else { 
										echo HEADING_ADD_MENU; 
									} 
								?>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table border="0" cellpadding="2" cellspacing="2">
						<tr>
							<td class="main" valign=top><?php echo ENTRY_COUNTRY; ?></td>
							<td class="main">
								<?php echo tep_draw_pull_down_menu('country', $_countries, $id); ?>
							</td>
						</tr>
						<tr>
							<td class="main" valign=top><?php echo ENTRY_VERIFICATION_TYPE; ?></td>
							<td class="main">
								<table border="1" cellpadding="2" cellspacing="2" style="border-collapse: collapse;">
									<tr>
										<td class="reportBoxHeading">&nbsp;</td>
										<?php
											$tbl_width = number_format((80 / count($_cpt)), 2) . '%';
											foreach ($_cpt as $cpt_val) {
										?>
												<td class="reportBoxHeading" width="<?php echo $tbl_width; ?>" align="center"><?php echo $cpt_val['text']; ?></td>
										<?php
											}
										?>
									</tr>
									<?php
										foreach ($this->_list_opt as $list_key => $list_val) {
									?>
										<tr>
											<td class="reportRecords"><?php echo $list_val; ?></td>
											<?php
												foreach ($_cpt as $cpt_val) {
													$verify_selected = (isset($result[$list_key][$cpt_val['id']]) && ($result[$list_key][$cpt_val['id']] == 1) ? ' checked' : '');
											?>
												<td class="reportRecords" align="center"><?php echo tep_draw_checkbox_field('chkbox_' . $cpt_val['id'] . '[' . $list_key . ']', '', $verify_selected); ?></td>
											<?php
												}
											?>
										</tr>
									<?php
										}
									?>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
			</tr>
			<tr>
				<td class="main" align="right">
					<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
					<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_CUSOMTERS_VERIFICATION_SETTING), '', 'inputButton')?>
				</td>
			</tr>
		</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
	
	
	public function addEntry($id="") {
		global $messageStack;
		
		$error = 0;
		$error_msg = '';
		$data = array();
		$extra_sel_sql = '';
		
		$country_id = (isset($_POST['country']) && ($_POST['country'] != '')) ? (int)$_POST['country'] : '';
		
		if ($country_id !== '') {
			if ($id == '') {
				$_sel_sql = "	SELECT countries_id 
								FROM " . TABLE_CUSTOMERS_VERIFICATION_SETTING . " 
								WHERE countries_id = '" . $country_id . "'";
				$_res_sql = tep_db_query($_sel_sql);
				if (tep_db_num_rows($_res_sql) > 0) {
					if ($country_id > 0) {
						$country_info = tep_get_countries_info($country_id, 'countries_id');
						$error_msg = $country_info['countries_name'];
					} else {
						$error_msg = TEXT_ALL_COUNTRY;
					}
					$error = 1;
				}
			}
			
			if ($error == 0) {
				foreach ($_POST as $key => $val) {
					if (preg_match('/^chkbox_/', $key)) {
						list($chkbox, $cpt) = explode('_', $key);
						
						foreach ($this->_list_opt as $list_key => $list_val) {
							$data[$list_key] = (isset($_POST['chkbox_' . $cpt][$list_key]) && ($_POST['chkbox_' . $cpt][$list_key] == 'on') ? 1 : 0);
						}
						
						if ($id != '') {
							tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_SETTING, $data, 'update', "countries_id = '" . (int)$id . "' AND custom_products_type = '" . (int)$cpt . "'");
						} else {
							$data['countries_id'] = tep_db_prepare_input($_POST['country']);
							$data['custom_products_type'] = (int)$cpt;
							
							tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_SETTING, $data, 'insert');
						}
					}
				}
			} else {
				$messageStack->add_session(sprintf(ERROR_SETTING_EXIST, $error_msg), 'error');
			}
		} else {
			$messageStack->add_session(ERROR_REQUIRED_ENTRY_EMPTY, 'error');
		}
	}
	
	public function deleteEntry($id="") {
		if (tep_not_null($id)) {
			$seller_group_del_sql = "DELETE FROM " . TABLE_CUSTOMERS_VERIFICATION_SETTING . " WHERE countries_id = '$id'";
			$seller_group_res_sql = tep_db_query($seller_group_del_sql);
			
			if ($seller_group_res_sql == 1) {
				return "success";
			}
		} else {
			return "";
		}
	}
}
?>