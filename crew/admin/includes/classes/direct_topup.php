<?

/*
	function listing:
	- get_supported_direct_top_up_product
	- get_game_input
	- add_customers_top_up_info
	- add_top_up
	- do_top_up
	- validate_game_acc
	- check_top_up_status
	- check_payment_status
	- get_credit_balance
	- curl_get
	- curl_post
	- get_signature
	
	- get_product_info
	- verify_signature
	- get_server_list
	- cron_update_server_list
	- cron_check_credit_balance
		- DEFINE MIN_BALANCE
*/

include_once(DIR_WS_CLASSES . 'direct_topup_api_log.php');
include_once(DIR_WS_CLASSES . 'publishers.php');

class direct_topup extends publishers{
	var $connect_via_proxy;
	
	function direct_topup() {
		$this->connect_via_proxy = false;
	}
	
	function get_product_publisher($pass_id) {
		global $memcache_obj;
		
		$publishers_id = 0;
		
		$cache_key = TABLE_TOP_UP_INFO . '/publishers_products/'.(int)$pass_id.'/publisher';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$publishers_id = $cache_result;
		} else {
			$publishers_id_select_sql = "	SELECT pg.publishers_id 
											FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
											INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg 
												ON pp.publishers_games_id = pg.publishers_games_id
											WHERE pp.products_id = '".(int)$pass_id."'";
			$publishers_id_result_sql = tep_db_query($publishers_id_select_sql);
			if ($publishers_id_row = tep_db_fetch_array($publishers_id_result_sql)) {
				$publishers_id = $publishers_id_row['publishers_id'];
			}
			$memcache_obj->store($cache_key, $publishers_id, 86400);
		}
		return $publishers_id;
	}
	
	function get_supported_direct_top_up_product() {
		global $memcache_obj;
		
		$supported_games = array();
		
		$cache_key = TABLE_TOP_UP_INFO . '/supported_direct_top_up_product/products_id';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$supported_games = $cache_result;
		} else {
			$supported_direct_top_up_select_sql = "	SELECT pp.products_id, pg.publishers_id 
													FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
													INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg 
														ON pg.publishers_games_id = pp.publishers_games_id
													GROUP BY pp.products_id";
			$supported_direct_top_up_result_sql = tep_db_query($supported_direct_top_up_select_sql);
			while ($supported_direct_top_up_row = tep_db_fetch_array($supported_direct_top_up_result_sql)) {
				$supported_games[$supported_direct_top_up_row['products_id']] = $supported_direct_top_up_row['publishers_id'];
			}
			$memcache_obj->store($cache_key, $supported_games, 86400);
		}
		return $supported_games;
	}
	
	function get_servers($products_id) {
		global $memcache_obj;
		
		$servers_array = array();
		
        $cache_key = TABLE_PUBLISHERS_GAMES . '/products_id/'.$products_id.'/publishers_server';
        
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$servers_array = $cache_result;
		} else {
			$game_info_select_sql = "	SELECT pg.publishers_server
										FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
										INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg
											ON pp.publishers_games_id = pg.publishers_games_id
										WHERE pp.products_id = '".$products_id."'";
			$game_info_result_sql = tep_db_query($game_info_select_sql);
			if ($game_info_row = tep_db_fetch_array($game_info_result_sql)) {
				$servers_array = json_decode($game_info_row['publishers_server'],true);
				$memcache_obj->store($cache_key, $servers_array, 86400);
			}
		}
		return $servers_array;
	}
	
	function check_is_supported_by_direct_top_up($products_id) {
		global $memcache_obj;
		
		$publishers_id = 0;
		
		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id . '/is_supported_by_direct_top_up/';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$publishers_id = $cache_result;
		} else {
			$product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id 
										FROM " . TABLE_PRODUCTS . " 
										WHERE products_id='" . tep_db_input($products_id) . "'";
			$product_info_result_sql = tep_db_query($product_info_select_sql);
			$product_info_row = tep_db_fetch_array($product_info_result_sql);
			if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
				$publishers_select_sql = "	SELECT pg.publishers_id
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
												ON pg.publishers_games_id = pp.publishers_games_id
											INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
												ON pdi.products_id = pp.products_id 
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
												ON pp.products_id=pb.subproduct_id
											WHERE pb.bundle_id = '".(int)$products_id."'
												AND pdi.products_delivery_mode_id = '6'
											LIMIT 1";
				$publishers_result_sql = tep_db_query($publishers_select_sql);
				if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
					$publishers_id = $publishers_row['publishers_id'];
				}
			} else {
				$publishers_select_sql = "	SELECT pg.publishers_id
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
												ON pg.publishers_games_id = pp.publishers_games_id
											INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
												ON pdi.products_id = pp.products_id
											WHERE pdi.products_id = '".(int)$products_id."'
												AND pdi.products_delivery_mode_id = '6'";
				$publishers_result_sql = tep_db_query($publishers_select_sql);
				if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
					$publishers_id = $publishers_row['publishers_id'];
				}
			}
			$memcache_obj->store($cache_key, $publishers_id, 86400);
		}
		return $publishers_id;
	}
	
	function get_admin_game_input($products_id) {
		global $memcache_obj, $languages_id, $default_languages_id;
		$game_input_array = array();
		
		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id . '/language/' . $languages_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$game_input_array = $cache_result;
		} else {
			$game_input_select_sql = "	SELECT tui.top_up_info_id, tuil.top_up_info_display, tui.top_up_info_key, tui.use_function, tui.set_function 
										FROM " . TABLE_TOP_UP_INFO . " AS tui 
										INNER JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil
											ON tui.top_up_info_id = tuil.top_up_info_id
										WHERE tui.products_id  = '".(int)$products_id."'
											AND tui.top_up_info_type_id = 2
											AND tuil.languages_id = 1 
										ORDER BY tui.sort_order";
			$game_input_result_sql = tep_db_query($game_input_select_sql);
			while ($game_input_row = tep_db_fetch_array($game_input_result_sql)) {
				$game_input_array[$game_input_row['top_up_info_key']] = $game_input_row;
			}
			$memcache_obj->store($cache_key, $game_input_array, 86400);
		}
		return $game_input_array;
	}

    public function get_product_info($pass_id, $info_key = 'GAME')
    {
        $product_info_select_sql = "	SELECT tui.top_up_info_value 
                                        FROM " . TABLE_TOP_UP_INFO . " AS tui  
                                        WHERE tui.products_id = '".(int)$pass_id."'
                                            AND tui.top_up_info_key = '".$info_key."'
                                            AND tui.top_up_info_type_id = '1'";
        $product_info_result_sql = tep_db_query($product_info_select_sql);
        if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            $return_info = $product_info_row['top_up_info_value'];
        }
        return $return_info;
    }
	
	function get_top_up_info($products_id) {
		global $memcache_obj, $languages_id, $default_languages_id;
		$game_input_array = array();

		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$top_up_info_array = $cache_result;
		} else {
			$top_up_info_select_sql = "	SELECT tui.* 
										FROM " . TABLE_TOP_UP_INFO . " AS tui 
										WHERE tui.products_id  = '".(int)$products_id."'
										ORDER BY tui.sort_order";
			$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
			while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
				$top_up_info_array[$top_up_info_row['top_up_info_key']] = $top_up_info_row;
			}
			$memcache_obj->store($cache_key, $top_up_info_array, 86400);
		}
		return $top_up_info_array;
	}
	
	function get_customer_input_value($orders_products_id) {
		$customer_input_value = array();
		$customer_input_value_select_sql = "SELECT ctui.top_up_info_id, ctui.top_up_value 
											FROM " . TABLE_CUSTOMERS_TOP_UP_INFO . " AS ctui 
											WHERE ctui.orders_products_id  = '".(int)$orders_products_id."'";
		$customer_input_value_result_sql = tep_db_query($customer_input_value_select_sql);
		while ($customer_input_value_row = tep_db_fetch_array($customer_input_value_result_sql)) {
			$customer_input_value[$customer_input_value_row['top_up_info_id']] = $customer_input_value_row['top_up_value'];
		}
		return $customer_input_value;
	}
	
	/* function to check OffGamers' orders status */
	function check_top_up_status($publisher_id, $publisher_ref_id) {
		$action = 'check_top_up_status';
		
		$this->publishers($publisher_id);
		$get_publishers_conf_array = $this->get_publishers_conf();
		
		$orders_products_select_sql = "	SELECT orders_products_id, top_up_id
										FROM " . TABLE_ORDERS_TOP_UP . " 
										WHERE publishers_id = '".(int)$publisher_id."'
											AND publishers_ref_id = '".$publisher_ref_id."'";
		$orders_products_result_sql = tep_db_query($orders_products_select_sql);
		if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
			$url = $get_publishers_conf_array['STATUS_URL']['publishers_configuration_value'];
			$param = array(	'action' => $action,
							'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
							'publisher_ref_id' => $publisher_ref_id);
			$param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);
			
			/* Start API Request Log */
			$request_log = 'url: ' . $url . "\n";
			ob_start();
			echo "<pre>";
			print_r($param);
			$request_log .= ob_get_contents();
			ob_end_clean();
			$api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int)$publisher_id, 'publisher_ref_id' => $publisher_ref_id));
			/* End API Request Log */
			
			$curl_response = $this->curl_post($url, $param);
			$curl_response_array = json_decode($curl_response, true);
			
			if (isset($curl_response_array['result_code'])) {
				$result_code = $curl_response_array['result_code'];
				
				$orders_top_up_remark_data_sql = array(	'top_up_id' => $orders_products_row['top_up_id'],
														'data_added' => 'now()');
				if ($result_code == 2000) {
					$top_up_status_data_sql = array();
					switch (strtolower($curl_response_array['top_up_status'])) {
						case 'reloaded':
							$top_up_status_data_sql['top_up_status'] = '3';
							break;
						case 'failed':
							$top_up_status_data_sql['top_up_status'] = '10';
							break;
						case 'not_found':
							$top_up_status_data_sql['top_up_status'] = '11';
							break;
						default:
							$top_up_status_data_sql['top_up_status'] = '1';
							break;
					}
					$top_up_status_data_sql['publishers_response_time'] = 'now()';
					tep_db_perform(TABLE_ORDERS_TOP_UP, $top_up_status_data_sql, 'update', " orders_products_id = '".$orders_products_row['orders_products_id']."' ");
					
					$orders_top_up_remark_data_sql['remark'] = tep_db_prepare_input('Top-up: ' . $curl_response_array['top_up_status']);
				} else if ($result_code == 1000) {
					$orders_top_up_remark_data_sql['remark'] = tep_db_prepare_input('Failed to check top-up status, suspect API not implemented by Publisher.');
				} else {
					$orders_top_up_remark_data_sql['remark'] = tep_db_prepare_input($this->get_result_code_description($result_code));
				}
				tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
			} else {
				$orders_top_up_remark_data_sql = array(	'top_up_id' => $orders_products_row['top_up_id'],
														'data_added' => 'now()',
														'remark' => 'Failed to check top-up status, suspect API not implemented by Publisher.');
				tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
			}
			
			/* Start API Response Log */
			$api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int)$publisher_id, 'publisher_ref_id' => $publisher_ref_id));
			/* End API Response Log */
		}
		
		return $curl_response_array;
	}
	
	/* function to check OffGamers' orders status */
	function get_customer_top_up_status($orders_products_id) {
		$return_status = 0;
		$orders_top_up_status_select_sql = "	SELECT top_up_status  
												FROM " . TABLE_ORDERS_TOP_UP . " AS otu 
												WHERE otu.orders_products_id = '".(int)$orders_products_id."'";
		$orders_top_up_status_result_sql = tep_db_query($orders_top_up_status_select_sql);
		if ($orders_top_up_status_row = tep_db_fetch_array($orders_top_up_status_result_sql)) {
			$return_status = $orders_top_up_status_row['top_up_status'];
		}
		return $return_status;
	}

	/* function to check customer top-up info */
	function get_customer_top_up_info($orders_products_id) {
		$customer_top_up_info_array = array();
		$get_customer_top_up_info_select_sql = "SELECT otu.*  
			    								FROM " . TABLE_ORDERS_TOP_UP . " AS otu 
			    								WHERE orders_products_id = '".(int)$orders_products_id."'";
		$get_customer_top_up_info_result_sql = tep_db_query($get_customer_top_up_info_select_sql);
		if ($get_customer_top_up_info_row = tep_db_fetch_array($get_customer_top_up_info_result_sql)) {
			$customer_top_up_info_array = $get_customer_top_up_info_row;
		}
	 	return $customer_top_up_info_array;
	}
	
	/* function return top-up status defination in array */
	function top_up_status($pass_id = '') {
		$top_up_status_array = array();
		$top_up_status_array[0] = 'Unknown';
		$top_up_status_array[1] = 'Pending';
		$top_up_status_array[3] = 'Reloaded';
		$top_up_status_array[10] = 'Failed';
		$top_up_status_array[11] = 'Not Found';
		
		return (isset($top_up_status_array[(int)$pass_id]) ? $top_up_status_array[(int)$pass_id] : $top_up_status_array[0]);
	}
	
	function curl_get($url) {
		$ch = curl_init($url);
      	
      	$agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
      	
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
		curl_setopt($ch, CURLOPT_TIMEOUT, 120);
		curl_setopt($ch, CURLOPT_USERAGENT, $agent);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
		if ($this->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		
		$response = curl_exec($ch);
		curl_close($ch);
		
		return $response;
	}
	
	function curl_post($url, $data, $filename='') {
		$ch = curl_init($url);
      	
      	$agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
      	
		$postfields = '';
		if (is_array($data)) {
			while (list($key, $value) = each($data)) {
				$postfields .= $key . '=' . urlencode($value) . '&';
			}
      	} else {
      		$postfields = $data;
      	}
      	
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);
      	if (tep_not_null($filename)) {
      		$fp = fopen ($filename, 'w+');
      		curl_setopt($ch, CURLOPT_FILE, $fp);
      	}
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
		curl_setopt($ch, CURLOPT_TIMEOUT, 120);
		curl_setopt($ch, CURLOPT_USERAGENT, $agent);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
		if ($this->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		
		$response = curl_exec($ch);
		curl_close($ch);
		if (tep_not_null($filename)) {
			fclose($fp);
		}
		return $response;
	}
	
	function get_signature($param, $key) {
		$action = array();
		if (isset($param['action'])) {
			$action['action'] = $param['action'];
		}
		unset($param['action']);
		ksort($param);
		reset($param);
		
		$param = array_merge($action, $param);
		
		$signature_array = array();
		foreach ($param as $key_loop => $data_loop) {
			$signature_array[] = $key_loop . '=' . $data_loop;
		}
		$signature_array[] = 'secret_key=' . $key;
		
		return sha1(implode("&",$signature_array));
	}
	
	function void_include_all_classes() {
        global $language;
        
		$module_directory = DIR_FS_CATALOG_MODULES . 'direct_topup/';
		$file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
		$directory_array = array();
		$file_extension = '.php';
		if ($dir = @dir($module_directory)) {
			while ($file = $dir->read()) {
				if (!is_dir($module_directory . $file)) {
					if (substr($file, strrpos($file, '.')) == $file_extension) {
		        		if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file)) {
						    include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file);
						}
					    include_once($module_directory . $file);
		        	}
		      	}
		    }
		    $dir->close();
		}
	}
	
	function void_include_class($publisher_id='') {
		global $memcache_obj, $language;
		
		$module_directory = DIR_FS_CATALOG_MODULES . 'direct_topup/';
		
		if ((int)$publisher_id==0) {
			$class_name = 'offgamers';
		} else {
			$cache_key = TABLE_PUBLISHERS_CONFIGURATION . '/publishers_id/' . $publisher_id . '/key/top_up_mode';
			$cache_result = $memcache_obj->fetch($cache_key);
			
			$class_name = '';
			if ($cache_result !== FALSE) {
				$class_name = $cache_result;
			} else {
				$publishers_configuration_sql = "	SELECT publishers_configuration_value, publishers_configuration_key
													FROM ".TABLE_PUBLISHERS_CONFIGURATION." 
													WHERE publishers_id = '".(int)$publisher_id."'
														AND publishers_configuration_key = 'TOP_UP_MODE'";
				$publishers_configuration_result = tep_db_query($publishers_configuration_sql);
				if ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result)) {
					$class_name = $publishers_configuration_row['publishers_configuration_value'];
				} else {
					$class_name = 'offgamers';
				}
				$memcache_obj->store($cache_key, $class_name, 86400);
			}
		}
			
		if (tep_not_null($class_name)) {
			include_once($module_directory . $class_name . '.php');
			if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class_name . '.php')) {
				include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class_name . '.php');
			}
            else{
                if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/offgamers.php')) {
                    include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/offgamers.php');
                }
            }
		}
		return $class_name;
	}
	
	function get_result_code_description($pass_code) {
		switch ($pass_code) {
			case '2000':
				return 'Success';

			case '1000':
				return 'Unknown action';
			case '1001':
				return 'Incomplete request';
			case '1002':
				return 'Invalid signature';
			case '1003':
				return 'IP access denied';
			case '1004':
				return 'Publisher does not exists';
			case '1005':
				return 'Publisher inactive';
			case '1006':
				return 'Game character doest not exist';
			case '1007':
				return 'Game account doest not exist';
			case '1008':
				return 'Game does not exists';
			case '1009':
				return 'Inactive Game';
			case '1010':
				return 'Server does not exists';
			case '1011':
				return 'Inactive Server';
			case '1012':
				return 'Merchant does not exist';
			case '1013':
				return 'Inactive merchant';
			case '1014':
				return 'Publisher Reference ID expired';
			case '1015':
				return 'Publisher Reference ID does not exists';
			case '1016':
				return 'Duplicate Publisher Reference ID';
			case '1017':
				return 'Server not available';

            case '1200':
                return 'Pending Publishers to Notify Status';

			case '1300':
				return 'Permission denied (Unable to check reference status)';
				
			case '1500':
				return 'Out of credit';
			case '1501':
				return 'Top-up amount less than minimum amount';
			case '1502':
				return 'Top-up amount exceed than maximum amount';
			case '1503':
				return 'Permission denied (Unable to top-up  other transaction)';
			case '1504':
				return 'Invalid top-up amount';
			case '1505':
				return 'Invalid top-up currency';
			case '1506':
				return 'Game account suspended';
			case '1507':
				return 'Game account closed';
			case '1508':
				return 'Exceed player top-up limit';
			case '1509':
				return 'Invalid payment status at OffGamers Side';
            case '1510':
                return 'Hit Minimum Margin Checking, Order Blocked from delivery';

			case '3000':
				return 'Please contact publisher';
				
				break;
			default:
				return $pass_code;
		}
	}
	
	function get_publishers_games_conf($pass_publishers_games_id='', $pass_key='') {
		$publishers_games_configuration_array = array();
		$publishers_games_configuration_select_sql = "	SELECT * 
														FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION  . "
														WHERE publishers_games_id = '".$pass_publishers_games_id."'";
		if (tep_not_null($pass_key)) {
			$publishers_games_configuration_select_sql .= " AND publishers_games_configuration_key = '".tep_db_input($pass_key)."'";
			$publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
			$publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql);
			return $publishers_games_configuration_row['publishers_games_configuration_value'];
		} else {
			$publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
			while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
				$publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row;
			}
			return $publishers_games_configuration_array;
		}
	}
	
	function character_is_sync($products_id) {
		global $memcache_obj;
		
		$cache_key = TABLE_TOP_UP_INFO . '/'.(int)$products_id.'/sync_publisher_character_flag';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$sync_publisher_character_flag = false;
		
		if ($cache_result !== FALSE) {
			$sync_publisher_character_flag = $cache_result;
		} else {
			$is_sync_sql = "SELECT top_up_info_value
							FROM " . TABLE_TOP_UP_INFO . "
							WHERE top_up_info_key = 'sync_publisher_character_flag'
								AND products_id = '".(int)$products_id."'";
			$is_sync_result = tep_db_query($is_sync_sql);
			$is_sync_row = tep_db_fetch_array($is_sync_result);
			$sync_publisher_character_flag = ($is_sync_row['top_up_info_value']==1 ? true : false);
			
			$memcache_obj->store($cache_key, $sync_publisher_character_flag, 86400);
		}
		return $sync_publisher_character_flag;
	}
}
?>