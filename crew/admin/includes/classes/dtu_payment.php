<?php
if (file_exists(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_DTU_PAYMENT)) {
    include_once(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_DTU_PAYMENT);
}

class dtu_payment {

    var $identity, $identity_email;
    var $dtu_items;

    // class constructor
    function dtu_payment($identity, $identity_email) {
        global $languages_id;

        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user

        $this->stock_price_type = array(array('id' => '%', 'text' => '%'), array('id' => '$', 'text' => '$'));

        $this->price_info = array('delivered_price' => array(), 'refund_price' => array(), 'creditnote_price' => array());
        $this->price_info_icons = array('delivered_price' => array('label' => 'Delivered', 'on' => 'icon_status_green.gif', 'off' => 'icon_status_green_light.gif'),
            'refund_price' => array('label' => 'Refunded', 'on' => 'icon_status_blue.gif', 'off' => 'icon_status_blue_light.gif'),
            'creditnote_price' => array('label' => 'Credit Note Issued', 'on' => 'icon_status_red.gif', 'off' => 'icon_status_red_light.gif')
        );

        $this->payment_info = array('payment_pending' => array(), 'payment_processing' => array(), 'payment_complete' => array());
        $this->payment_info_icons = array('payment_pending' => array('label' => 'Payment Pending', 'on' => 'icon_status_red.gif', 'off' => 'icon_status_red_light.gif'),
            'payment_processing' => array('label' => 'Payment in Processing', 'on' => 'icon_status_yellow.gif', 'off' => 'icon_status_yellow_light.gif'),
            'payment_complete' => array('label' => 'Payment Completed', 'on' => 'icon_status_green.gif', 'off' => 'icon_status_green_light.gif')
        );

        $this->contact_person = $identity_email;

        $this->po_delivery_address = array();
        $this->po_company_gst = array();

        $po_company_select_sql = "SELECT * FROM " . TABLE_PO_COMPANY . " ORDER BY po_company_id";
        $po_company_result_sql = tep_db_query($po_company_select_sql);
        while ($po_company_row = tep_db_fetch_array($po_company_result_sql)) {
            $zone_name = '';
            if (tep_not_null($po_company_row["po_company_state"])) {
                $zone_name = $po_company_row["po_company_state"];
            } else {
                $zone_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $po_company_row['po_company_country_id'] . "' and zone_id = '" . (int) $po_company_row['po_company_zone_id'] . "'");
                if ($zone_values = tep_db_fetch_array($zone_query)) {
                    $zone_name = $zone_values['zone_name'];
                }
            }

            $this->po_delivery_address[$po_company_row['po_company_code']] = array(
                'TOP' => array(
                    'name' => $po_company_row['po_company_contact_name'],
                    'company' => $po_company_row['po_company_name'],
                    'street_address' => $po_company_row['po_company_street_address'],
                    'suburb' => $po_company_row['po_company_suburb'],
                    'city' => $po_company_row['po_company_city'],
                    'postcode' => $po_company_row['po_company_postcode'],
                    'state' => $zone_name,
                    'country' => tep_get_country_name($po_company_row['po_company_country_id']),
                    'format_id' => $po_company_row['po_company_format_id'],
                    'telephone' => $po_company_row['po_company_telephone'],
                    'fax' => $po_company_row['po_company_fax']
                ),
                'FOOTER' => array('text' => $po_company_row['po_company_invoice_footer'])
            );

            $this->po_company_gst[$po_company_row['po_company_code']] = $po_company_row['po_company_gst_percentage'];
        }

        $this->unset_po();

        $this->po_status_arr = array('0' => '--');
        $po_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id = " . (int) $languages_id . " ORDER BY purchase_orders_status_sort_order";
        $po_status_result_sql = tep_db_query($po_status_select_sql);
        while ($po_status_row = tep_db_fetch_array($po_status_result_sql)) {
            $this->po_status_arr[$po_status_row['purchase_orders_status_id']] = $po_status_row['purchase_orders_status_name'];
        }
    }

    function unset_po() {
        $this->po_id = '';
        $this->po_zero_total_amt = false;
        $this->po_payment_made = false;
        $this->po_info = array();
        $this->supplier = array();
        $this->billing = array();
        $this->payment = array();
        $this->po_items = array();
        $this->po_total = array();
        $this->po_summary = array();
    }

    function search_dtu_list($input_array) {
        global $languages_id;

        $search_start_date = date('Y-m-d', mktime(0, 0, 0, date('m') - 1, date('d'), date('Y')));
        $search_end_date = date('Y-m-d');

        $show_options = array(array('id' => 'DEFAULT', "text" => "Default"),
            array('id' => '10', "text" => "10"),
            array('id' => '20', "text" => "20"),
            array('id' => '50', "text" => "50"),
            array('id' => 'ALL', "text" => "All")
        );

        $status_options = array();
        $order_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id='" . (int) $languages_id . "' ORDER BY purchase_orders_status_sort_order";
        $order_status_result_sql = tep_db_query($order_status_select_sql);
        while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
            $status_options[$order_status_row["purchase_orders_status_id"]] = $order_status_row["purchase_orders_status_name"];
        }

        $payment_status_options = array();
        $payment_status_select_sql = "SELECT store_payments_status_id, store_payments_status_name FROM " . TABLE_STORE_PAYMENTS_STATUS . " WHERE language_id='" . (int) $languages_id . "' ORDER BY store_payments_status_sort_order";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
            $payment_status_options[$payment_status_row["store_payments_status_id"]] = $payment_status_row["store_payments_status_name"];
        }
        ?>
        <table border="0" cellspacing="2" cellpadding="2" width="100%">
            <tr>
                <td>
                    <?= tep_draw_form('dtu_create_new', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')), 'post', ''); ?>
                    <?= tep_draw_hidden_field('subaction', 'create_blank_dtu', ' id="subaction" '); ?>
                    <?= tep_draw_hidden_field('dtu_list_type', 'create_blank_dtu', ' id="dtu_list_type" '); ?>
                    <?= tep_submit_button('New DTU Payment Request', 'Proceed To Blank DTU Payment Request', 'name="multi_submit_btn" onClick="return dtu_list_search_form_check(\'create_blank_dtu\')"', 'inputButton'); ?>
                    </form>
                    <?= tep_draw_form('dtu_add_cb', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')), 'post', ''); ?>
                    <?= tep_draw_hidden_field('subaction', 'add_dtu_cb', ' id="subaction" '); ?>
                    <?= tep_draw_hidden_field('dtu_list_type', 'add_dtu_cb', ' id="dtu_list_type" '); ?>
                    <?= tep_submit_button('Add DTU CB/DN/FL', 'Proceed To DTU CB/DN/FL', 'name="multi_submit_btn" onClick="return dtu_list_search_form_check(\'add_dtu_cb\')"', 'inputButton'); ?>
                    </form>
                    <?= tep_draw_form('dtu_report', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')), 'post', ''); ?>
                    <?= tep_draw_hidden_field('subaction', 'dtu_report', ' id="subaction" '); ?>
                    <?= tep_draw_hidden_field('dtu_list_type', 'dtu_report', ' id="dtu_list_type" '); ?>
                    <?= tep_submit_button('DTU Report', 'Proceed To DTU Report', 'name="multi_submit_btn" onClick="return dtu_list_search_form_check(\'dtu_report\')"', 'inputButton'); ?>
                    </form>
                </td>
            </tr>
        </table>
        <table border="0" cellspacing="2" cellpadding="2" width="100%">
            <tr>
                <td>
                    <?= tep_draw_form('dtu_list_search', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=show_dtu_list&selected_box=po', 'post'); ?>
                    <table border="0" cellspacing="2" cellpadding="2" width="100%">
                        <tr>
                            <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table border="0" cellspacing="2" cellpadding="0">
                                    <tr>
                                        <td class="main" width="15%" valign="top"><?= ENTRY_HEADING_DTU_LIST_START_DATE ?></td>
                                        <td class="main" width="15%" valign="top" nowrap><?= tep_draw_input_field('start_date', (isset($_SESSION['po_search']['start_date']) ? $_SESSION['po_search']['start_date'] : $search_start_date), 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_list_search.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_list_search.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                        <td class="main" width="5%">&nbsp;</td>
                                        <td class="main" width="12%" valign="top"><?= ENTRY_HEADING_DTU_LIST_END_DATE ?></td>
                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('end_date', (isset($_SESSION['po_search']["end_date"]) ? $_SESSION['po_search']["end_date"] : $search_end_date), 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_list_search.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_list_search.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td class="main" width="15%"><?= ENTRY_HEADING_DTU_LIST_DTU_NO ?></td>
                            <td class="main"><?= tep_draw_input_field('po_ref_id', $_SESSION['po_search']['po_ref_id'], ' size="30" id="po_ref_id"') ?></td>
                        </tr>
                        <tr>
                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td class="main" width="15%" valign="top"><?= ENTRY_HEADING_DTU_LIST_DTU_STATUS ?></td>
                            <td>
                                <table border="0" cellspacing="2" cellpadding="0">
                                    <?
                                    if (count($status_options)) {
                                        echo '<tr><td class="main">' . tep_draw_checkbox_field('order_status_any', '1', isset($_SESSION['po_search']['order_status']) && count($_SESSION['po_search']['order_status']) ? false : true, '', 'id="order_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="' . (count($status_options) * 2 - 1) . '">' . TEXT_DTU_ANY . '</td></tr>';
                                        echo '<tr>';
                                        foreach ($status_options as $id => $title) {
                                            $order_status_display_str = '';
                                            $order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $id . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "' ;";
                                            $order_tag_result_sql = tep_db_query($order_tag_select_sql);

                                            $order_status_display_str .= '<td class="main" valign="top">' .
                                            tep_draw_checkbox_field('order_status[]', $id, isset($_SESSION['po_search']['order_status']) ? (is_array($_SESSION['po_search']["order_status"]) && in_array($id, $_SESSION['po_search']["order_status"]) ? true : false) : (false), '', 'onClick=verify_status_selection();') . '</td><td class="main" valign="top">';
                                            $order_status_display_str .= '<fieldset class="selectedFieldSet"><legend align=left class=SectionHead>' . $title . '</legend><table border="0" cellspacing="0" cellpadding="0"><tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('status_' . $id . '[]', 'no_tag', isset($_SESSION['po_search']['status_' . $id]) ? (is_array($_SESSION['po_search']['status_' . $id]) && in_array('no_tag', $_SESSION['po_search']['status_' . $id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText">No Tag</td></tr>';
                                            while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
                                                $order_status_display_str .= '<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('status_' . $id . '[]', $order_tag_row["orders_tag_id"], isset($_SESSION['po_search']['status_' . $id]) ? (is_array($_SESSION['po_search']['status_' . $id]) && in_array($order_tag_row["orders_tag_id"], $_SESSION['po_search']['status_' . $id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText">' . $order_tag_row["orders_tag_name"] . '</td></tr>';
                                            }
                                            $order_status_display_str .= '</table></fieldset>';
                                            $order_status_display_str .= '</td>';
                                            echo $order_status_display_str;
                                        }
                                        echo '</tr>';
                                    }
                                    ?>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="main" width="15%" valign="top"><?= ENTRY_HEADING_DTU_LIST_PAYMENT_STATUS ?></td>
                            <td>
                                <table border="0" cellspacing="2" cellpadding="0">
                                    <?
                                    if (count($payment_status_options)) {
                                        echo '<tr><td class="main">' . tep_draw_checkbox_field('payment_status_any', '1', isset($_SESSION['po_search']['payment_status']) && count($_SESSION['po_search']['payment_status']) ? false : true, '', 'id="payment_status_any" onClick="set_payment_status_option(this);"') . TEXT_DTU_ANY . '</td></tr>';
                                        foreach ($payment_status_options as $id => $title) {
                                            echo '<tr><td class="main" valign="top">' .
                                            tep_draw_checkbox_field('payment_status[]', $id, isset($_SESSION['po_search']['payment_status']) ? (is_array($_SESSION['po_search']["payment_status"]) && in_array($id, $_SESSION['po_search']["payment_status"]) ? true : false) : (false), '', 'onClick=verify_payment_status_selection();') . $title . '</td></tr>';
                                        }
                                    }
                                    ?>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td class="main" width="15%"><?= ENTRY_HEADING_RECORDS_PER_PAGE ?></td>
                            <td class="main">
                                <?= tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['po_search']["show_records"]) ? $_SESSION['po_search']["show_records"] : '', '') ?>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td colspan="2" align="right">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <?= tep_submit_button('Search', 'Search', 'onClick="return search_form_check();"', 'inputButton'); ?>
                                            &nbsp;&nbsp;
                                            <?= tep_submit_button(IMAGE_BUTTON_RESET, IMAGE_BUTTON_RESET, 'name="reset"', 'inputButton') ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    </form>
                </td>
            </tr>
        </table>
        <script language="javascript">
        <!--
        function search_form_check() {
            var po_ref_id = jQuery('#po_ref_id').val();
            if (po_ref_id != '') {
                return true;
            } else {
                var po_start_date = jQuery('#start_date').val();
                var po_end_date = jQuery('#end_date').val();
                var error = false;
                if (po_start_date == '' || po_end_date == '') {
                    error = true;
                }

                if (error) {
                    alert('Start Date and End Date required!');
                    return false;
                } else {
                    return true;
                }
            }
        }

        function set_status_option(any_status_obj) {
            var multi_status_select = document.dtu_list_search.elements['order_status[]'];
            if (any_status_obj.checked == true) {
                for (i = 0; i < multi_status_select.length; i++) {
                    multi_status_select[i].checked = false;
                    var cur_status_id = multi_status_select[i].value;
                    var multi_tags_select = document.dtu_list_search.elements['status_' + cur_status_id + '[]'];
                    if (typeof (multi_tags_select) != 'undefined') {
                        if (typeof (multi_tags_select.length) != 'undefined') {
                            for (tag_cnt = 0; tag_cnt < multi_tags_select.length; tag_cnt++) {
                                multi_tags_select[tag_cnt].disabled = true;
                                multi_tags_select[tag_cnt].checked = false;
                            }
                        } else {
                            multi_tags_select.disabled = true;
                            multi_tags_select.checked = false;
                        }

                    }
                }
            } else { // force to check if no any order status option is selected
                var selected_count = 0;
                for (i = 0; i < multi_status_select.length; i++) {
                    var cur_status_id = multi_status_select[i].value;
                    var multi_tags_select = document.dtu_list_search.elements['status_' + cur_status_id + '[]'];
                    if (multi_status_select[i].checked == true) {
                        selected_count++;
                        if (typeof (multi_tags_select) != 'undefined') {
                            if (typeof (multi_tags_select.length) != 'undefined') {
                                for (tag_cnt = 0; tag_cnt < multi_tags_select.length; tag_cnt++) {
                                    multi_tags_select[tag_cnt].disabled = false;
                                }
                            } else {
                                multi_tags_select.disabled = false;
                            }
                        }
                    } else {
                        if (typeof (multi_tags_select) != 'undefined') {
                            if (typeof (multi_tags_select.length) != 'undefined') {
                                for (tag_cnt = 0; tag_cnt < multi_tags_select.length; tag_cnt++) {
                                    multi_tags_select[tag_cnt].disabled = true;
                                    multi_tags_select[tag_cnt].checked = false;
                                }
                            } else {
                                multi_tags_select.disabled = true;
                                multi_tags_select.checked = false;
                            }
                        }
                    }
                }
                if (!selected_count) {
                    any_status_obj.checked = true;
                }
            }
        }

        function verify_status_selection() {
            var multi_status_select = document.dtu_list_search.elements['order_status[]'];
            var selected_count = 0;
            for (i = 0; i < multi_status_select.length; i++) {
                var cur_status_id = multi_status_select[i].value;
                var multi_tags_select = document.dtu_list_search.elements['status_' + cur_status_id + '[]'];
                if (multi_status_select[i].checked == true) {
                    selected_count++;
                    if (typeof (multi_tags_select) != 'undefined') {
                        if (typeof (multi_tags_select.length) != 'undefined') {
                            for (tag_cnt = 0; tag_cnt < multi_tags_select.length; tag_cnt++) {
                                multi_tags_select[tag_cnt].disabled = false;
                            }
                        } else {
                            multi_tags_select.disabled = false;
                        }
                    }
                } else {
                    if (typeof (multi_tags_select) != 'undefined') {
                        if (typeof (multi_tags_select.length) != 'undefined') {
                            for (tag_cnt = 0; tag_cnt < multi_tags_select.length; tag_cnt++)            {
                                multi_tags_select[tag_cnt].disabled = true;
                                multi_tags_select[tag_cnt].checked = false;
                            }
                        } else {
                            multi_tags_select.disabled = true;
                            multi_tags_select.checked = false;
                        }
                    }
                }
            }
            if (!selected_count) {
                document.getElementById('order_status_any').checked = true;
            } else {
                document.getElementById('order_status_any').checked = false;
            }
        }

        function set_payment_status_option(any_status_obj) {
            var multi_status_select = document.dtu_list_search.elements['payment_status[]'];
            if (any_status_obj.checked == true) {
                for (i = 0; i < multi_status_select.length; i++) {
                    multi_status_select[i].checked = false;
                }
            } else {
                var selected_count = 0;
                    for (i = 0; i < multi_status_select.length; i++) {
                        if (multi_status_select[i].checked == true) {
                            selected_count++;
                        }
                    }
                if (!selected_count) {
                    any_status_obj.checked = true;
                }
            }
        }

        function verify_payment_status_selection() {
            var multi_status_select = document.dtu_list_search.elements['payment_status[]'];
            var selected_count = 0;
            for (i = 0; i < multi_status_select.length; i++) {
                if (multi_status_select[i].checked == true) {
                    selected_count++;
                }
            }
            if (!selected_count) {
                document.getElementById('payment_status_any').checked = true;
            } else {
                document.getElementById('payment_status_any').checked = false;
            }
        }

        function dtu_list_search_form_check(update_type) {
            jQuery('#subaction').val(update_type);
            switch (update_type) {
                case 'add_dtu_cb':
                    document.dtu_add_cb.submit();
                    break;
                case 'dtu_report':
                    document.dtu_report.submit();
                    break;
                default:
                    document.dtu_create_new.submit();
                    break;
            }
            
            return true;
        }
        //-->
        </script>
        <?
    }

    function show_dtu_list($input_array) {
        global $languages_id, $currencies, $view_payment_details_permission;

        $status_options = array();
        $order_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id='" . (int) $languages_id . "' ORDER BY purchase_orders_status_sort_order";
        $order_status_result_sql = tep_db_query($order_status_select_sql);
        while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
            $status_options[$order_status_row["purchase_orders_status_id"]] = $order_status_row["purchase_orders_status_name"];
        }

        if (!$_REQUEST['cont']) {
            $_SESSION['po_search']["show_records"] = $input_array["show_records"];
            $_SESSION['po_search']["start_date"] = $input_array["start_date"];
            $_SESSION['po_search']["end_date"] = $input_array["end_date"];
            $_SESSION['po_search']["po_ref_id"] = $input_array["po_ref_id"];
            $_SESSION['po_search']["suppID"] = $input_array["suppID"];
            $_SESSION['po_search']["order_status"] = $input_array["order_status"];
            $_SESSION['po_search']["payment_status"] = $input_array["payment_status"];

            if (count($status_options)) {
                foreach ($status_options as $id => $title) {
                    $_SESSION['po_search']["status_" . $id] = $input_array["status_" . $id];
                    $_SESSION['po_search']["page" . $id] = $input_array["page" . $id];
                }
            }
        }
        ?>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
            <tr>
                <td align="right" class="smallText">
                    <?= tep_draw_form('dtu_id_search', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&selected_box=po', 'post', ''); ?>
                    <?= ENTRY_SEARCH_DTU_ID ?>:&nbsp;&nbsp;<?= tep_draw_input_field('po_ref', '', 'size="12" id="po_ref"') ?><?= tep_draw_hidden_field('subaction', 'edit_dtu', 'id="subaction"') ?>
                    </form>
                </td>
            </tr>
        </table>
        <?
        $show_records = $_SESSION['po_search']["show_records"];

        $po_select_str = "SELECT DISTINCT po.purchase_orders_id, po.purchase_orders_ref_id, po.supplier_name, po.supplier_id, 
                            po.payment_type, po.payment_term, po.payment_days_pay_wsc, po.store_payment_account_book_id, po.supplier_email_address, 
                            DATE_FORMAT(po.purchase_orders_issue_date, '%Y-%m-%d') AS purchase_orders_issue_date, 
                            po.last_modified, po.currency, po.confirmed_currency_value, po.purchase_orders_tag_ids, 
                            po.purchase_orders_locked_by, po.purchase_orders_billing_status, po.purchase_orders_paid_status, 
                            po.purchase_orders_verify_mode, po.currency_usd_value, pos.purchase_orders_status_name 
                            FROM " . TABLE_PURCHASE_ORDERS . " AS po 
                            INNER JOIN " . TABLE_PURCHASE_ORDERS_STATUS . " AS pos 
                                ON po.purchase_orders_status = pos.purchase_orders_status_id 
                            LEFT JOIN " . TABLE_CUSTOMERS . " AS c 
                                ON po.supplier_id=c.customers_id ";

        if (tep_not_null($_SESSION['po_search']["payment_status"])) {
            $payment_status_str = "'" . implode("','", $_SESSION['po_search']["payment_status"]) . "'";
            $po_select_str .= " INNER JOIN " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti
                                    ON (spti.store_payments_reimburse_id = po.purchase_orders_id 
                                    AND spti.store_payments_reimburse_table = '" . TABLE_PURCHASE_ORDERS . "')
                                INNER JOIN " . TABLE_STORE_PAYMENTS . " AS sp
                                    ON (spti.store_payments_id = sp.store_payments_id 
                                    AND sp.store_payments_status in (" . $payment_status_str . ")) ";
        }

        if (tep_not_null($_SESSION['po_search']["start_date"])) {
            if (strpos($_SESSION['po_search']["start_date"], ':') !== false) {
                $startDateObj = explode(' ', trim($_SESSION['po_search']["start_date"]));
                list($yr, $mth, $day) = explode('-', $startDateObj[0]);
                list($hr, $min) = explode(':', $startDateObj[1]);
                $start_date_str = " ( po.purchase_orders_issue_date >= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "')";
            } else {
                list($yr, $mth, $day) = explode('-', trim($_SESSION['po_search']["start_date"]));

                $start_date_str = " ( po.purchase_orders_issue_date >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "')";
            }
        } else {
            $start_date_str = " 1 ";
        }

        if (tep_not_null($_SESSION['po_search']["end_date"])) {
            if (strpos($_SESSION['po_search']["end_date"], ':') !== false) {
                $endDateObj = explode(' ', trim($_SESSION['po_search']["end_date"]));
                list($yr, $mth, $day) = explode('-', $endDateObj[0]);
                list($hr, $min) = explode(':', $endDateObj[1]);
                $end_date_str = " ( po.purchase_orders_issue_date <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 59, $mth, $day, $yr)) . "' )";
            } else {
                list($yr, $mth, $day) = explode('-', trim($_SESSION['po_search']["end_date"]));
                $end_date_str = " ( po.purchase_orders_issue_date <= '" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "' )";
            }
        } else {
            $end_date_str = " 1 ";
        }

        $po_ref_id_str = (isset($_SESSION['po_search']["po_ref_id"]) && tep_not_null($_SESSION['po_search']["po_ref_id"])) ? " po.purchase_orders_ref_id='" . $_SESSION['po_search']["po_ref_id"] . "'" : "1";

        $po_supp_id_str = (isset($_SESSION['po_search']["suppID"]) && tep_not_null($_SESSION['po_search']["suppID"])) ? " po.supplier_id='" . $_SESSION['po_search']["suppID"] . "'" : "1";

        $show_order_status = isset($_SESSION['po_search']["order_status"]) ? $_SESSION['po_search']["order_status"] : array();
        if (!count($show_order_status)) {
            $show_order_status = is_array($status_options) ? array_keys($status_options) : array();
        }

        $tag_selection_general = array(array('id' => '', 'text' => 'DTU Payment Request Lists Options ...'),
            array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled'));

        $po_id_list = array();
        $extra_detail = array();
        $payment_detail = array();

        for ($status_count = 0; $status_count < count($show_order_status); $status_count++) {
            $order_status_id = $show_order_status[$status_count];
            $status = preg_replace("/\s/", '_', $status_options[$order_status_id]);
            echo tep_draw_hidden_field($status . '_order_str', '', ' id="' . $status . '_order_str" ');

            $total_amount_array = array();
            $total_upload_array = array();

            $mirror_for_delete_tag = array();
            $status_dependent_tag_array = $tag_selection_general;
            $order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
            $order_tag_result_sql = tep_db_query($order_tag_select_sql);
            while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
                $status_dependent_tag_array[] = array('id' => 'otag_' . $order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $order_tag_row["orders_tag_name"]);
                $mirror_for_delete_tag[] = array('id' => 'rmtag_' . $order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $order_tag_row["orders_tag_name"]);
            }
            $status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
            $status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
            $status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);

            if ($order_status_id == 1) {
                $total_colspan = 7;
            } else if ($order_status_id == 2) {
                $total_colspan = 9;
            } else if ($order_status_id == 3) {
                $total_colspan = 11;
            } else {
                $total_colspan = 7;
            }
            ?>
            <table border="0" width="100%" cellspacing="0" cellpadding="2"><tr><td>
                <table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
                    <tr>
                        <td colspan="12">
                            <span class="pageHeading"><?= $status_options[$order_status_id] ?></span>
                            <?
                            echo '<span id="' . $status . '_nav"></span>';
                            echo '&nbsp;&nbsp;<span id="' . $status . '_tag_nav" class="hide">' . tep_draw_pull_down_menu($status . "_tag_selector", $status_dependent_tag_array, '', ' id="' . $status . '_tag_selector" onChange="orderListsOptions(this, \'' . $order_status_id . '\', \'' . (int) $languages_id . '\', \'\', true);"') . '</span>';
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td width="10%" class="ordersBoxHeading"><?= TABLE_HEADING_DTU_LIST_DTU_NO ?></td>
                        <td width="7%" class="ordersBoxHeading"><?= TABLE_HEADING_DTU_LIST_TERM ?></td>
                        <? if ($order_status_id == 3) { ?>
                            <td width="5%" class="ordersBoxHeading" align="center"><?= TABLE_HEADING_DTU_LIST_DELIVERY_STATUS ?></td>
                        <? } ?>
                        <td width="10%" class="ordersBoxHeading"><?= TABLE_HEADING_DTU_LIST_TAG ?></td>
                        <td width="10%" class="ordersBoxHeading"><?= TABLE_HEADING_DTU_LIST_SUPPLIER ?></td>
                        <? if ($order_status_id == 2 || $order_status_id == 3) { ?>
                            <td width="1%" class="ordersBoxHeading" align="center"><?= TABLE_HEADING_DTU_LIST_PAYMENT_STATUS ?></td>
                        <? } ?>
                        <td width="10%" class="ordersBoxHeading"><?= TABLE_HEADING_DTU_LIST_DATE ?></td>
                        <? if ($order_status_id == 1 || $order_status_id == 4) { ?>
                            <td width="7%" class="ordersBoxHeading" align="right"><?= TABLE_HEADING_DTU_LIST_TOTAL_PURCHASED ?></td>
                        <? } else if ($order_status_id == 2) { ?>
                            <td width="7%" class="ordersBoxHeading" align="right"><?= TABLE_HEADING_DTU_LIST_TOTAL_PURCHASED ?></td>
                            <td width="5%" class="ordersBoxHeading" align="center"><?= TABLE_HEADING_DTU_LIST_LOCKED ?></td>
                        <? } else if ($order_status_id == 3) { ?>
                            <td width="7%" class="ordersBoxHeading" align="right"><?= TABLE_HEADING_DTU_LIST_TOTAL_UPLOADED ?></td>
                            <td width="5%" class="ordersBoxHeading" align="center"><?= TABLE_HEADING_DTU_LIST_VERIFIED ?></td>
                        <? } ?>
                        <td width="2%" class="ordersBoxHeading" align="center"><?= TABLE_HEADING_DTU_LIST_ACTION ?></td>
                    </tr>
                    <?
                    if (isset($_SESSION['po_search']["po_ref_id"]) && tep_not_null($_SESSION['po_search']["po_ref_id"])) {
                        $purchase_orders_select_sql = $po_select_str . ' WHERE ' . $po_ref_id_str . " AND po.purchase_orders_status='" . $order_status_id . "' AND po.purchase_orders_type=1";
                    } else {
                        $purchase_orders_select_sql = $po_select_str . ' WHERE ' . $start_date_str . ' AND ' . $end_date_str . ' AND ' . $po_supp_id_str . " AND po.purchase_orders_status='" . $order_status_id . "' AND po.purchase_orders_type=1 ORDER BY po.purchase_orders_id DESC ";
                    }

                    if ($show_records != "ALL") {
                        $orders_split_object = new splitPageResults($input_array['page' . $order_status_id], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $purchase_orders_select_sql, $orders_select_sql_numrows, true);
                    }

                    $purchase_orders_result_sql = tep_db_query($purchase_orders_select_sql);
                    $row_count = 0;

                    while ($row = tep_db_fetch_array($purchase_orders_result_sql)) {
                        $row_style = ($row_count % 2) ? 'ordersListingEven' : 'ordersListingOdd';
                        $row_count++;

                        $order_locked = false;
                        if (tep_not_null($row['purchase_orders_locked_by'])) {
                            $lock_orders_select_sql = "SELECT a.admin_email_address FROM " . TABLE_ADMIN . " AS a WHERE a.admin_id = '" . $row['purchase_orders_locked_by'] . "'";
                            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                            $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                            if (tep_not_null($lock_orders_row["admin_email_address"])) {
                                $order_locked = true;
                                $lock_owner_username = str_replace(strstr($lock_orders_row["admin_email_address"], '@'), '', $lock_orders_row["admin_email_address"]);
                            }
                        }

                        switch ($row['payment_type']) {
                            case 'g':
                                $pay_term = TEXT_SUPPLIER_CONSIGNMENT;
                                break;
                            case 'c':
                                $pay_term = TEXT_SUPPLIER_PRE_PAYMENT;
                                break;
                            case 'd':
                                $pay_term = TEXT_SUPPLIER_DTU_PAYMENT;
                                break;
                            case 't':
                                $pay_term = $row['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                                break;
                        }

                        $po_purchase_currency = DEFAULT_CURRENCY;
                        $po_purchase_amt = 0;
                        $po_total_select_sql = "SELECT value, currency FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $row['purchase_orders_id'] . "' AND class='po_subtotal'";
                        $po_total_result_sql = tep_db_query($po_total_select_sql);
                        if ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                            $po_purchase_currency = $po_total_row['currency'];
                            $po_purchase_amt = $po_total_row['value'];
                        }
                        //$currencies->set_decimal_places($currencies->currencies[$po_purchase_currency]['decimal_places']);
                        $currencies->set_decimal_places(4);
                        $po_purchase_str = $currencies->format($po_purchase_amt, false, $po_purchase_currency);

                        $tags_str = '';
                        if (tep_not_null($row['purchase_orders_tag_ids'])) {
                            $tags_arr = explode(',', $row['purchase_orders_tag_ids']);
                            foreach ($tags_arr as $tag_id) {
                                $tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $tag_id . "' AND filename='" . FILENAME_DTU_PAYMENT . "'";
                                $tag_result_sql = tep_db_query($tag_select_sql);
                                while ($tag_row = tep_db_fetch_array($tag_result_sql)) {
                                    $tags_str .= ((tep_not_null($tags_str)) ? ',' : '') . $tag_row['orders_tag_name'];
                                }
                            }
                        }

                        $total_upload_amt = 0;
                        if ($order_status_id != 1 && $order_status_id != 4) { // Not in Pending and Canceled status
                            $total_upload_select_sql = "SELECT SUM(products_good_delivered_price) AS total_upload FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " WHERE purchase_orders_id = '" . $row['purchase_orders_id'] . "'";
                            $total_upload_result_sql = tep_db_query($total_upload_select_sql);
                            $total_upload_row = tep_db_fetch_array($total_upload_result_sql);
                            $total_upload_array[$row['currency']] += $total_upload_row['total_upload'];
                            $total_upload_amt = $currencies->currencies[$row['currency']]['symbol_left'] . ' ' . number_format($total_upload_row['total_upload'], 4, $currencies->currencies[$row['currency']]['decimal_point'], $currencies->currencies[$row['currency']]['thousands_point']) . ' ' . $currencies->currencies[$row['currency']]['symbol_right'];
                        }

                        if ($order_status_id == 3) { // in Completed status
                            $price_info_select_sql = "SELECT SUM(products_good_delivered_quantity) AS delivered_price, SUM(products_refund_quantity) AS refund_price, SUM(products_credit_note_quantity) AS cn_price FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " WHERE purchase_orders_id = '" . $row['purchase_orders_id'] . "'";
                            $price_info_result_sql = tep_db_query($price_info_select_sql);
                            $price_info_row = tep_db_fetch_array($price_info_result_sql);
                            $this->price_info['delivered_price'][$row['purchase_orders_id']] += $price_info_row['delivered_price'];
                            $this->price_info['refund_price'][$row['purchase_orders_id']] += $price_info_row['refund_price'];
                            $this->price_info['creditnote_price'][$row['purchase_orders_id']] += $price_info_row['cn_price'];
                        }

                        if ($order_status_id == 2 || $order_status_id == 3) { // in Processing or Completed status
                            $po_paid_select_sql = "SELECT po.purchase_orders_id, po.purchase_orders_paid_status, 
                                                    po.purchase_orders_paid_currency, po.purchase_orders_paid_amount 
                                                    FROM " . TABLE_PURCHASE_ORDERS . " As po 
                                                    WHERE po.purchase_orders_id = '" . $row['purchase_orders_id'] . "'";
                            $po_paid_result_sql = tep_db_query($po_paid_select_sql);
                            $po_paid_row = tep_db_fetch_array($po_paid_result_sql);
                            switch ($po_paid_row['purchase_orders_paid_status']) {
                                case '1':
                                    $po_paid_amount = round($po_paid_row['purchase_orders_paid_amount'], 2);
                                    $po_payable_amount = round($this->get_po_total_payable_amount($row['purchase_orders_id']), 2);

                                    $payment_info_select_sql = "SELECT sp.store_payments_id, sp.store_payments_status, 
                                                                sp.store_payments_request_currency, sp.store_payments_request_amount 
                                                                FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti 
                                                                INNER JOIN " . TABLE_STORE_PAYMENTS . " as sp 
                                                                    ON (spti.store_payments_id = sp.store_payments_id) 
                                                                WHERE spti.store_payments_reimburse_id = '" . $row['purchase_orders_id'] . "' 
                                                                    AND spti.store_payments_reimburse_table = '" . TABLE_PURCHASE_ORDERS . "'";
                                    $payment_info_result_sql = tep_db_query($payment_info_select_sql);
                                    if (tep_db_num_rows($payment_info_result_sql) > 0) {
                                        // get all store payment record status count
                                        $status_array = array('1' => 0, '2' => 0, '3' => 0, '4' => 0);
                                        $running_payment_amount = $po_payable_amount;
                                        while ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {
                                            $status_array[$payment_info_row['store_payments_status']] ++;
                                            switch ($payment_info_row['store_payments_status']) {
                                                case '1':
                                                case '2':
                                                case '3':
                                                    $running_payment_amount -= round($payment_info_row['store_payments_request_amount'], 2);
                                                    break;
                                                case '4':
                                                    $running_payment_amount += round($payment_info_row['store_payments_request_amount'], 2);
                                                    break;
                                            }
                                        }

                                        if ($status_array['4'] > 0 && $running_payment_amount > 0) { // if there are any store payment status is canceled, and still has remaining payable amount
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 0;
                                        }

                                        if ($status_array['1'] > 0) { // if there are any store payment status is pending
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 1;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 0;
                                        } else if ($status_array['2'] > 0) { // if there are any store payment status is processing
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 1;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 0;
                                        } else {
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 1;
                                        }
                                    } else {
                                        if ($po_paid_amount == $po_payable_amount) {
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 1;
                                        } else {
                                            $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                            $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 0;
                                        }
                                    }
                                    break;
                                case '0': // unpaid
                                case '2': // partially paid
                                    $this->payment_info['payment_pending'][$row['purchase_orders_id']] = 0;
                                    $this->payment_info['payment_processing'][$row['purchase_orders_id']] = 0;
                                    $this->payment_info['payment_complete'][$row['purchase_orders_id']] = 0;
                                    break;
                            }
                        }

                        $po_verified = false;
                        if ($row['purchase_orders_verify_mode'] == '1') {
                            $po_verified = true;
                        }

                        $po_id_list[$status]['po_id'][] = $row['purchase_orders_id'];
                        $payment_detail[$status]['order_id'][] = $row['purchase_orders_id'];
                        $total_amount_array[$po_purchase_currency] += $po_purchase_amt; //$row['purchase_orders_total'];
                        ?>
                        <tbody id="read_<?= $row['purchase_orders_id'] ?>">
                            <tr id="<?= $status . '_main_' . $row_count ?>" class="<?= $row_style ?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?= $status . '_sub_' . $row_count ?>##<?= $status . '_sub2_' . $row_count ?>')" onmouseout="showOutEffect(this, '<?= $row_style ?>', '<?= $status . '_sub_' . $row_count ?>##<?= $status . '_sub2_' . $row_count ?>')" onclick="showClicked(this, '<?= $row_style ?>', '<?= $status . '_sub_' . $row_count ?>##<?= $status . '_sub2                            _' . $row_count ?>')">
                                                <td class="ordersRecords"><?= $row['purchase_orders_ref_id'] ?></td>
                                <td class="ordersRecords"><?= $pay_term ?></td>
                                <? if ($order_status_id == 3) { ?>
                                    <td class="ordersRecords" align="center">
                                        <?
                                        foreach ($this->price_info_icons as $sub_status => $icon_info) {
                                            $icon_img_src = DIR_WS_IMAGES . ($this->price_info[$sub_status][$row['purchase_orders_id']] > 0 ? $icon_info['on'] : $icon_info['off']);
                                            echo '&nbsp;' . tep_image($icon_img_src, $icon_info['label'], 10, 10) . '&nbsp;';
                                        }
                                        ?>
                                    </td>
                                <? } ?>
                                <td class="ordersRecords"><span class="greenIndicator" id="tag_<?= $row['purchase_orders_id'] ?>"><?= $tags_str ?></span>&nbsp;</td>
                                <td class="ordersRecords"><?= '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $row['supplier_id'] . '&action=edit&selected_box=customers', 'NONSSL') . '" target="_blank">' . $row['supplier_name'] . '</a>' ?></td>
                                <? if ($order_status_id == 2 || $order_status_id == 3) { ?>
                                    <td class="ordersRecords" align="center">
                                        <?
                                        foreach ($this->payment_info_icons as $sub_status => $icon_info) {
                                            $icon_img_src = DIR_WS_IMAGES . ($this->payment_info[$sub_status][$row['purchase_orders_id']] > 0 ? $icon_info['on'] : $icon_info['off']);
                                            echo '&nbsp;' . tep_image($icon_img_src, $icon_info['label'], 10, 10) . '&nbsp;';
                                        }
                                        ?>
                                    </td>
                                <? } ?>
                                <td class="ordersRecords"><?= $row['purchase_orders_issue_date'] ?></td>
                                <? if ($order_status_id == 1 || $order_status_id == 4) { ?>
                                    <td class="ordersRecords" align="right"><?= $po_purchase_str ?></td>
                                <? } else if ($order_status_id == 2) { ?>
                                    <td class="ordersRecords" align="right"><?= $po_purchase_str ?></td>
                                    <?= '<td class="ordersRecords" align="center">' . ($order_locked == true ? ($row["purchase_orders_locked_by"] == $_SESSION['login_id'] ? '<b>' . $lock_owner_username . '</b>' : $lock_owner_username ) : '&nbsp;') . '</td>'; ?>
                                <? } else if ($order_status_id == 3) { ?>
                                    <td class="ordersRecords" align="right"><?= $total_upload_amt ?></td>
                                    <td class="ordersRecords" align="center"><?= ($po_verified === true ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', 'Verified', 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', 'Un-Verified', 10, 10)) ?></td>
                                <? } ?>
                                <td class="ordersRecords" align="center">
                                    <?= '<a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id=' . $row['purchase_orders_id'] . '&subaction=edit_dtu&selected_box=po', 'NONSSL') . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "14", "13", 'align="top"') . '</a>' ?>
                                </td>
                            </tr>
                        </tbody>
                        <tbody id="<?= $status . "_" . $row['purchase_orders_id'] . '_order_sec' ?>" class="hide">
                            <tr id="<?= $status . '_sub_' . $row_count ?>" class="<?= $row_style ?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?= $status . '_main_' . $row_count ?>##<?= $status . '_sub2_' . $row_count ?>')" onmouseout="showOutEffect(this, '<?= $row_style ?>', '<?= $status . '_main_' . $row_count ?>##<?= $status . '_sub2_' . $row_count ?>')" onclick="showClicked(this, '<?= $row_style ?>', '<?= $status . '_main_' . $row_count ?>##<?= $status . '_sub2_                            ' . $row_count ?>')">
                                                <td class="ordersRecords">&nbsp;</td>
                                <td colspan="<?= ($total_colspan - 2) ?>">
                                    <div id="<?= $status . '_' . $row['purchase_orders_id'] . '_order' ?>"></div>
                                </td>
                                <? if ($order_status_id == 1 || $order_status_id == 2 || $order_status_id == 4) { ?>
                                <td class="ordersRecords">&nbsp;</td>
                                <? } ?>
                            </tr>
                        </tbody>
                        <?
                    }
                    $extra_detail[$status] = $row_count;
                    ?>
            </td></tr>
        <tr>
            <?
            if ($order_status_id == 1 || $order_status_id == 4) {
                $left_colspan = 5;
                $right_colspan = 1;
            } else if ($order_status_id == 2) {
                $left_colspan = 6;
                $right_colspan = 2;
            } else if ($order_status_id == 3) {
                $left_colspan = 7;
                $right_colspan = 3;
            }
            ?>
            <td colspan="<?= $left_colspan ?>">&nbsp;</td>
            <? if ($order_status_id == 1 || $order_status_id == 2 || $order_status_id == 4) { ?>
                <td align="right" class="smallText">
                    <?
                    if (count($total_amount_array)) {
                        ksort($total_amount_array);
                        foreach ($total_amount_array as $cur_code => $total_cur_amount) {
                            echo $currencies->currencies[$cur_code]['symbol_left'] . ' ' . number_format($total_cur_amount, 4, $currencies->currencies[$cur_code]['decimal_point'], $currencies->currencies[$cur_code]['thousands_point']) . ' ' . $currencies->currencies[$cur_code]['symbol_right'] . '<br>';
                        }
                    }
                    ?>
                </td>
            <? } ?>
            <? if ($order_status_id == 2 || $order_status_id == 3) { ?>
                <td align="right" class="smallText">
                    <?
                    if (count($total_upload_array)) {
                        ksort($total_upload_array);
                        foreach ($total_upload_array as $cur_code => $total_cur_amount) {
                            echo $currencies->currencies[$cur_code]['symbol_left'] . ' ' . number_format($total_cur_amount, 4, $currencies->currencies[$cur_code]['decimal_point'], $currencies->currencies[$cur_code]['thousands_point']) . ' ' . $currencies->currencies[$cur_code]['symbol_right'] . '<br>';
                        }
                    }
                    ?>
                </td>
            <? } ?>
            <td colspan="<?= $right_colspan ?>">&nbsp;</td>
        </tr>
        <tr>
            <td colspan="12">
                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                    <tr>
                        <td class="smallText" align="right" valign="top" colspan="2">
                        </td>
                    </tr>
                    <tr>
                        <td class="smallText" valign="top"><?= $show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($purchase_orders_result_sql) > 0 ? "1" : "0", tep_db_num_rows($purchase_orders_result_sql), tep_db_num_rows($purchase_orders_result_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $input_array['page' . $order_status_id], TEXT_DISPLAY_NUMBER_OF_ORDERS) ?></td>
                        <td class="smallText" align="right"><?= $show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $input_array['page' . $order_status_id], tep_get_all_get_params(array('page' . $order_status_id, 'cont', 'criteria_id')) . "cont=1", 'page' . $order_status_id) ?></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <?
            }
            $currencies->set_decimal_places('-1');
        ?>
        </td></tr></table>
        <script language="javascript">
        <!--
            var isProcessing = false;
        <?
        foreach ($payment_detail as $status_name => $res) {
            $order_str = count($res['order_id']) ? implode(',', $res['order_id']) : '';
            ?>
                document.getElementById("<?= $status_name ?>" + "_order_str").value = "<?= $order_str ?>";
            <?
        }

        foreach ($extra_detail as $key => $count) {
            ?>
                var <?= $key ?>_count = <?= $count ?>;
                if (eval(<?= $key ?>_count) > 0) {
                document.getElementById('<?= $key ?>_tag_nav').className = 'show';
                }

                poInfo('<?= $key ?>', 0, false, '<?= (int) $languages_id ?>', '<?= SID ?>');
            <?
        }
        ?>
        //-->
        </script>
        <?
    }
    
    function show_items_dtu_form($input_array) {
        global $currencies;

        if (isset($input_array['dtu_items_top_up_id'])) {
            $this->repopulate_dtu_select_items($input_array);
        } else {
            $products_arr = $input_array['low_stock_batch'];
            if (is_array($products_arr)) {
                foreach ($products_arr as $po_products_id) {
                    $po_products_name = '';
                    $po_products_qty = $po_products_actual_qty = 0;

                    // retrieve product name and quantities
                    $publishers_products_select_sql = "SELECT p.products_quantity, p.products_actual_quantity, 
                                                        pd.products_name 
                                                        FROM " . TABLE_PRODUCTS . " AS p 
                                                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
                                                                ON (p.products_id = pd.products_id) 
                                                        WHERE p.products_id = '" . (int) $po_products_id . "' 
                                                                AND pd.language_id = 1 
                                                                AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    if ($publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
                        $po_products_name = strip_tags($publishers_products_row['products_name']);
                        $po_products_qty = $publishers_products_row['products_quantity'];
                        $po_products_actual_qty = $publishers_products_row['products_actual_quantity'];
                    }

                    // calculate suggested stock quantity
                    $multiplier = 7;
                    $last_n_days = 30;
                    $suggest_qty = $this->calculate_suggest_quantity($po_products_id, $multiplier, $last_n_days);

                    // retrieve product selling price
                    $price_array = $currencies->get_product_prices_info($po_products_id);
                    if ($price_array['base_cur'] == DEFAULT_CURRENCY) {
                        $selling_price = $price_array['price'];
                    } else {
                        if (count($price_array['defined_price']) && isset($price_array['defined_price'][DEFAULT_CURRENCY])) {
                            $selling_price = $price_array['defined_price'][DEFAULT_CURRENCY];
                        } else {
                            $base_rate = 1;
                            if ($currencies->currencies[$price_array['base_cur']]['value'] > 0) {
                                $base_rate = 1 / $currencies->currencies[$price_array['base_cur']]['value'];
                            }
                            $selling_price = $base_rate * $price_array['price'];
                        }
                    }

                    $this->po_items[] = array(
                        'products_id' => $po_products_id,
                        'products_name' => $po_products_name,
                        'products_quantity' => $po_products_qty,
                        'products_actual_quantity' => $po_products_actual_qty,
                        'multiplier' => $multiplier,
                        'last_n_days' => $last_n_days,
                        'suggest_qty' => $suggest_qty,
                        'products_sell_price' => round($selling_price, 2),
                        'stock_price_type' => '$',
                        'products_unit_price' => round($selling_price, 2),
                        'subtotal' => round(($selling_price * $suggest_qty), 2)
                    );
                }
            }
        }

        $this->show_dtu_form($input_array);
    }

    function show_dtu_form($input_array) {
        
        global $currencies, $dtu_publisher;

        $currencies_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
        $currency_list = $currencies->get_currency_set();
        for ($i = 0, $n = sizeof($currency_list); $i < $n; $i++) {
            $currencies_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
        }
        
        if(isset($input_array['dtu_delivery_address'])) {
            $dtu_delivery_address = "'".$input_array['dtu_delivery_address']."'";
        } else {
            $dtu_delivery_address = "''";
        }

        $dtu_publishers_array = $dtu_publisher->get_all_publishers_unlocked();

        // Get Publisher List
        $dtu_publisher_list = array(array('id' => '', 'text' => 'Publisher List...'));
        foreach ($dtu_publishers_array as $publisher) {
            if ($publisher['publishers_status'] > 0) {
                $select_publisher_status = ($publisher['po_supplier_locked_by'] == $_SESSION['login_id'] || empty($publisher['po_supplier_locked_by'])) ? '' : 'disabled' ;
                $locked_by = ($publisher['po_supplier_locked_by'] == $_SESSION['login_id'] || empty($publisher['po_supplier_locked_by'])) ? '' : ' [Locked by:' . $username = substr($publisher['admin_email_address'], 0, strpos($publisher['admin_email_address'], '@')) . ']' ;
                $dtu_publisher_list[] = array(
                    'id' => $publisher['publishers_id'],
                    'param' => $select_publisher_status,
                    'text' => $publisher['publishers_name'] . $locked_by
                );
            }
        }
        ?>
        <?= tep_draw_form('dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=calculate_dtu&selected_box=po', 'post', 'onSubmit="return new_dtu_check_form();"'); ?>
        <?= tep_draw_hidden_field('dtu_list_type', $input_array['dtu_list_type'], 'id="dtu_list_type"') ?>
        <table width="100%" cellspacing="2" cellpadding="2" border="0">
            <tbody>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                                                </tr>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_START_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= tep_draw_input_field('start_date', '', 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_form.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_form.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CONTACT_PERSON ?>:</b></td>
                                                    <td class="main" valign="top"><?= $this->contact_person ?><?= tep_draw_hidden_field('dtu_contact_person', (isset($input_array['dtu_contact_person']) ? $input_array['dtu_contact_person'] : $this->contact_person), 'id="dtu_contact_person"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUGGESTED_SUPPLIER ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                            <tr>
                                                                <td class="main"><?= tep_draw_pull_down_menu('dtu_supplier', $dtu_publisher_list, $input_array['dtu_supplier'], 'id="dtu_supplier" onChange="getSupplierInfo(this, \'\', \'dtu_supplier_address\', \'dtu_supplier_payment_term\', \'dtu_supplier_disbursement\', \'dtu_supplier_delivery\', '.$dtu_delivery_address.', ' . (isset($input_array['BackBtn']) ? "'false'" : "'true'") . ');"', true) ?></td>
                                                                <td class="main"><div id="dtu_payment_supplier_lock_div"></div></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_ADDRESS ?>:</b></td>
                                                    <td class="main"><div id="dtu_supplier_address"></div></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DELIVERY_ADDRESS ?>:</b></td>
                                                    <td class="main"><div id="dtu_supplier_delivery"></div></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_END_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= tep_draw_input_field('end_date', '', 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_form.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_form.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_PAYMENT_TERM ?>:</b></td>
                                                    <td class="main" valign="top"><div id="dtu_supplier_payment_term"></div></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DISBURSEMENT_METHOD ?>:</b></td>
                                                    <td class="main" valign="top"><?= tep_draw_hidden_field('dtu_payment_method_currency', '', 'id="dtu_payment_method_currency"') ?><div id="dtu_supplier_disbursement"></div></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <?= tep_draw_pull_down_menu('dtu_currency', $currencies_array, (isset($input_array['dtu_currency']) ? $input_array['dtu_currency'] : DEFAULT_CURRENCY), 'id="dtu_currency" onChange="getCurrencyRate(this, \'suggest_rate\', \'confirm_rate\');"', true) ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_SUGGEST_RATE ?>:</b></td>
                                                    <td class="main"><?= tep_draw_hidden_field('suggest_rate', $input_array['suggest_rate'], 'id="suggest_rate"') ?><div id="suggest_rate_text"><?= ($input_array['suggest_rate'] ? $input_array['suggest_rate'] : '') ?></div></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_CONFIRM_RATE ?>:</b></td>
                                                    <td class="main"><?= tep_draw_input_field('confirm_rate', $input_array['confirm_rate'], 'maxlength="32" id="confirm_rate" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0)) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" onChange="setDecimalPlaces(this)"') ?></td>
                                                </tr>
                                                <?php
                                                if($input_array['dtu_list_type'] == 'add_dtu_cb') {
                                                    echo '<tr>';
                                                    echo '    <td class="main" valign="top"><b>'. ENTRY_DTU_FORM_REPORT_TOPUP_ID .':</b></td>';
                                                    echo '    <td class="main" valign="top">';
                                                    echo        tep_draw_input_field('dtu_topup_id', '', ' size="20" id="dtu_topup_id"');
                                                    echo '    </td>';
                                                    echo '</tr>';
                                                }
                                                ?>
                                                <tr>
                                                    <td></td>
                                                    <td>
                                                        <div id="div_search_dtu_button"><?= tep_button(BUTTON_SEARCH_DTU, ALT_BUTTON_SEARCH_DTU, '', 'onclick="if(checkDTUform(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier)){searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, \'false\', \'\', \'topup_date\', \'asc\');}"') ?></div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <div id="dtu_list_div">
                            <table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">
                                <tbody>
                                    <tr>
                                        <td class="invoiceBoxHeading" align="center" width="4%"><?= TABLE_HEADING_DTU_SELECT ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_DATE ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_PRODUCT_ID ?></td>
                                        <td class="invoiceBoxHeading" width="18%"><?= TABLE_HEADING_DTU_PRODUCT_NAME ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_ORDER_NO ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_TOP_UP_ID ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="6%"><?= TABLE_HEADING_DTU_QUANTITY ?></td>
                                        <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title"><?=sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>
                                        <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title"><?=sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>
                                        <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_DTU_BILLING_STATUS ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody id="total_row" class="show">
                                <tr class="invoiceListingOdd">
                                    <td width="90%" align="right" class="boldText" colspan="3"><?= TABLE_HEADING_BOTTOM_TOTAL_DTU_SELECT ?>:</td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                    <td width="2%" align="right" class="boldText"><div name="dtu_total_currency" id="dtu_total_currency"><?= (isset($input_array['dtu_currency']) ? $input_array['dtu_currency'] : DEFAULT_CURRENCY) ?></div></td>
                                    <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('dtu_select_total', (isset($input_array['dtu_select_total']) ? $input_array['dtu_select_total'] : '0.0000'), 'id="dtu_select_total"') ?><div name="dtu_select_total_div" id="dtu_select_total_div"><?= $this->monetary_decimal_format($input_array['dtu_select_total']) ?></div></td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right" colspan="2">
                        <?php
                            if($input_array['dtu_list_type'] == 'add_dtu_cb') {
                                // Preview DTU list for change status
                                echo tep_submit_button(BUTTON_STATUS_DTU, ALT_BUTTON_STATUS_DTU, 'name="dtuStatusButton"', 'inputButton');
                            } else {
                                echo tep_submit_button(BUTTON_CALCULATE_DTU, ALT_BUTTON_CALCULATE_DTU, 'name="dtuCalculateButton" id="dtuCalculateButton"', 'inputButton');
                            }
                        ?>
                    </td>
                </tr>
            </tbody>
        </table>
        </form>
        <SCRIPT language="JavaScript" type="text/javascript">
        <!--
        <?
            if (isset($input_array['dtu_supplier'])) {
                echo "getSupplierInfo(document.dtu_form.dtu_supplier, '" . $input_array['dtu_supplier_payment'] . "', 'dtu_supplier_address', 'dtu_supplier_payment_term', 'dtu_supplier_disbursement', 'dtu_supplier_delivery', ".$dtu_delivery_address.", " . (isset($input_array['BackBtn']) ? 'false' : 'true') . ");";
            }

            if (!isset($input_array['BackBtnToList']) && !isset($input_array['preview_error'])) {
                echo "getCurrencyRate(document.dtu_form.dtu_currency, 'suggest_rate', 'confirm_rate');";
            } else {
                echo "searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, 'true', '', 'topup_date', 'asc');";
            }
            
            if (isset($input_array['BackBtnCB'])) {
                echo "searchDTUlist(document.dtu_form.dtu_list_type, document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_currency, 'true', '', 'topup_date', 'asc');";
            }
        ?>
            
            function new_dtu_check_form() {
                if (jQuery('#dtu_supplier').text() == '') {
                    <?= "alert('" . ERROR_DTU_FORM_EMPTY_SUPPLIER . "');" ?>
                    return false;
                }

                return true;
            }
            
            function setDecimalPlaces(sel_obj) {
                sel_obj.value = parseFloat(sel_obj.value).toFixed(8);
            }
            
        //-->
        </SCRIPT>
        <?
    }
    
    function show_dtu_report($input_array) {
        
        global $currencies, $dtu_publisher;

        $currencies_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
        $currency_list = $currencies->get_currency_set();
        for ($i = 0, $n = sizeof($currency_list); $i < $n; $i++) {
            $currencies_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
        }

        $dtu_publishers_array = $dtu_publisher->get_all_publishers();

        // Get Publisher List
        $dtu_publisher_list = array(array('id' => '', 'text' => 'Publisher List...'));
        foreach ($dtu_publishers_array as $publisher) {
            if ($publisher['publishers_status'] > 0) {
                $dtu_publisher_list[] = array(
                    'id' => $publisher['publishers_id'],
                    'text' => $publisher['publishers_name']
                );
            }
        }
        ?>
        <?= tep_draw_form('dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=calculate_dtu&selected_box=po', 'post', 'onSubmit="return new_dtu_check_form();"'); ?>
        <?= tep_draw_hidden_field('dtu_list_type', $input_array['dtu_list_type'], 'id="dtu_list_type"') ?>
        <table width="100%" cellspacing="2" cellpadding="2" border="0">
            <tbody>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="100%" valign="top" colspan="2">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>    
                                <tr>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="15%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_START_DATE_REPORT ?>:</b></td>
                                                    <td width="25%" class="main" valign="top"><?= tep_draw_input_field('start_date', '', 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_form.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_form.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' . TEXT_FIELD_REQUIRED ?></td>
                                                    <td width="15%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_END_DATE_REPORT ?>:</b></td>
                                                    <td class="main" valign="top"><?= tep_draw_input_field('end_date', '', 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.dtu_form.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.dtu_form.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' . TEXT_FIELD_REQUIRED ?></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUGGESTED_SUPPLIER ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <?= tep_draw_pull_down_menu('dtu_supplier', $dtu_publisher_list, $input_array['dtu_supplier'], 'id="dtu_supplier" onChange="getSupplierInfoReport(this, \'dtu_supplier_address\');"', false) ?>
                                                    </td>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_REPORT_PRODUCT_ID ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <?= tep_draw_input_field('dtu_product_id', '', ' size="20" id="dtu_product_id"') ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_ADDRESS ?>:</b></td>
                                                    <td class="main"><div id="dtu_supplier_address"></div></td>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_REPORT_ORDER_NUMBER ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <?= tep_draw_input_field('dtu_order_id', '', ' size="20" id="dtu_order_id"') ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_REPORT_TOPUP_ID ?>:</b></td>
                                                    <td class="main" valign="top">
                                                        <?= tep_draw_input_field('dtu_topup_id', '', ' size="20" id="dtu_topup_id"') ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <div id="div_search_dtu_button"><?= tep_button(BUTTON_SEARCH_DTU, ALT_BUTTON_SEARCH_DTU, '', 'onclick="if(checkDTUReportform(document.dtu_form.start_date, document.dtu_form.end_date)){getDTUList(document.dtu_form.start_date, document.dtu_form.end_date, document.dtu_form.dtu_supplier, document.dtu_form.dtu_product_id, document.dtu_form.dtu_order_id, document.dtu_form.dtu_topup_id,  \'USD\', \'unpaid\', \'\', \'topup_date\', \'asc\');}"') ?></div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <div ID="div_search">
                            <div id="search-tab">
                                <ul>
                                    <li>
                                        <a href="#tab_unpaid"><span>Un-Paid</span></a>
                                    </li>
                                    <li class="ui-tabs-disabled">
                                        <a href="#tab_paid"><span>Paid</span></a>
                                    </li>
                                    <li class="ui-tabs-disabled">	                
                                        <a href="#tab_charge_back"><span>Charge Back</span></a>
                                    </li>
                                    <li class="ui-tabs-disabled">
                                        <a href="#tab_debit_note"><span>Debit Note</span></a>
                                    </li>
                                    <li class="ui-tabs-disabled">	                
                                        <a href="#tab_dtu_issue"><span>DTU Issue</span></a>
                                    </li>
                                </ul>
                                <div id="tab_unpaid" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
                                    <?= TAB_DEFAULT_TEXT ?>
                                </div>
                                <div id="tab_paid" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>
                                <div id="tab_charge_back" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>
                                <div id="tab_debit_note" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>
                                <div id="tab_dtu-issue" style="border-style: solid; border-color: #97a5b0; border-width: 1px;"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody id="total_row" class="show">
                                <tr class="invoiceListingOdd">
                                    <td width="90%" align="right" class="boldText" colspan="3"><?= TABLE_HEADING_BOTTOM_TOTAL_DTU_SELECT ?>:</td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                    <td width="2%" align="right" class="boldText"><div name="dtu_total_currency" id="dtu_total_currency"><?= (isset($input_array['dtu_currency']) ? $input_array['dtu_currency'] : DEFAULT_CURRENCY) ?></div></td>
                                    <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('dtu_select_total', (isset($input_array['dtu_select_total']) ? $input_array['dtu_select_total'] : '0.0000'), 'id="dtu_select_total"') ?><div name="dtu_select_total_div" id="dtu_select_total_div"><?= $this->monetary_decimal_format($input_array['dtu_select_total']) ?></div></td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        </form>
        <SCRIPT language="JavaScript" type="text/javascript">
        <!--   
        function new_dtu_check_form() {
            if (jQuery('#dtu_supplier').text() == '') {
                <?= "alert('" . ERROR_DTU_FORM_EMPTY_SUPPLIER . "');" ?>
                return false;
            }
            return true;
        }   
        //-->
        </SCRIPT>
        <?
    }
    
    function calculate_dtu_form($input_array) {
        
        global $currencies, $po_suppliers;
        
        $dtu_publisher = new publishers($input_array['dtu_supplier']);
        $dtu_supplier_info = $dtu_publisher->get_publishers();
        $dtu_publisher->set_dtu_temp_status_empty();
        $dtu_supplier_id = $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_supplier_id'];

        $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));
        $countries_info = tep_get_countries_info($supplier_info['country_id'], 'countries_id');
        $address_str = tep_address_format($countries_info['address_format_id'], $supplier_info, 1, '', '<br>', 'main');
        $delivery_str = $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['company'];
        
        $dtu_topup_id_array = array();
        $dtu_cb_topup_id_array = array();
        $dtu_dn_topup_id_array = array();
        $products_array = array();
        $cb_products_array = array(); // Charge Back
        $dn_products_array = array(); // Debit Note
        $total_quantity = array();
        $total_cb_quantity = array();
        $total_dn_quantity = array();
        $subtotal_price = array();
        $subtotal_cb_price = array();
        $subtotal_dn_price = array();
        // Group Same products_id
        if (is_array($input_array['dtu_items_top_up_id']) && $input_array['subaction'] != 'create_dtu_product') {
            $comma_list = implode(', ', $input_array['dtu_items_top_up_id']);
            $dtu_item_array = $dtu_publisher->get_dtu_items($comma_list);
            unset($input_array['dtu_select_total']);
            foreach ($input_array['dtu_items_top_up_id'] as $dtu_topup_id) {
                $dtu_publisher->set_dtu_temp_status($dtu_topup_id);
                $dtu_item_prod_id = (isset($dtu_item_array[$dtu_topup_id]['sub_products_id']) && !empty($dtu_item_array[$dtu_topup_id]['sub_products_id'])) ? $dtu_item_array[$dtu_topup_id]['sub_products_id'] : $dtu_item_array[$dtu_topup_id]['products_id'];

                if ($dtu_item_array[$dtu_topup_id]["products_type"] == 3) {
                    // virtual listing
                    $selling_price = $this->getVirtualProductSellPrice($dtu_item_array[$dtu_topup_id]['currency_code'], $dtu_item_array[$dtu_topup_id]['currency_settle_amount'], $currencies, $input_array['dtu_currency']);
                } else {
                    $selling_price = $this->getProductSellPrice($dtu_item_prod_id, $currencies, $input_array['dtu_currency']);
                }
                // DTU to pay
                if ($input_array['dtu_cb_status_'.$dtu_topup_id] == '0' || $input_array['dtu_cb_status_'.$dtu_topup_id] == '1') {
                    $dtu_topup_id_array[$dtu_item_prod_id][] = $dtu_topup_id;
                    $products_array[$dtu_item_prod_id]['products_id'] = $dtu_item_prod_id;
                    $products_array[$dtu_item_prod_id]['main_products_id'] = ($dtu_item_array[$dtu_topup_id]["products_type"] == 3) ? $dtu_item_array[$dtu_topup_id]['products_id'] : '';
                    $products_array[$dtu_item_prod_id]['products_name'] = $dtu_item_array[$dtu_topup_id]['products_name'];
                    $total_quantity[$dtu_item_prod_id] = $total_quantity[$dtu_item_prod_id] + number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                    $products_array[$dtu_item_prod_id]['products_quantity'] = $total_quantity[$dtu_item_prod_id];
                    $products_array[$dtu_item_prod_id]['products_selling_price'] = round($selling_price, 4);
                    $subtotal_price[$dtu_item_prod_id] = $total_quantity[$dtu_item_prod_id] * round($selling_price, 4);
                    $products_array[$dtu_item_prod_id]['products_subtotal_price'] = $subtotal_price[$dtu_item_prod_id];
                    $products_array[$dtu_item_prod_id]['original_products_subtotal_price'] = $subtotal_price[$dtu_item_prod_id];
                    $products_array[$dtu_item_prod_id]['dtu_cb_status'] = $input_array['dtu_cb_status_'.$dtu_topup_id];
                } 
                
                // DTU - Charge Back
                if ($input_array['dtu_cb_status_'.$dtu_topup_id] == '1') {
                    $dtu_cb_topup_id_array[$dtu_item_prod_id][] = $dtu_topup_id;
                    $cb_products_array[$dtu_item_prod_id]['products_id'] = $dtu_item_prod_id;
                    $cb_products_array[$dtu_item_prod_id]['main_products_id'] = ($dtu_item_array[$dtu_topup_id]["products_type"] == 3) ? $dtu_item_array[$dtu_topup_id]['products_id'] : '';
                    $cb_products_array[$dtu_item_prod_id]['products_name'] = $dtu_item_array[$dtu_topup_id]['products_name'];
                    $total_cb_quantity[$dtu_item_prod_id] = $total_cb_quantity[$dtu_item_prod_id] + number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                    $cb_products_array[$dtu_item_prod_id]['products_quantity'] = $total_cb_quantity[$dtu_item_prod_id];
                    $cb_products_array[$dtu_item_prod_id]['products_selling_price'] = round($selling_price, 4);
                    $subtotal_cb_price[$dtu_item_prod_id] = $total_cb_quantity[$dtu_item_prod_id] * round($selling_price, 4);
                    $cb_products_array[$dtu_item_prod_id]['products_subtotal_price'] = $subtotal_cb_price[$dtu_item_prod_id];
                    $cb_products_array[$dtu_item_prod_id]['original_products_subtotal_price'] = $subtotal_cb_price[$dtu_item_prod_id];
                    $cb_products_array[$dtu_item_prod_id]['dtu_cb_status'] = $input_array['dtu_cb_status_'.$dtu_topup_id];
                } 
                
                // DTU - Debit Note
                if ($input_array['dtu_cb_status_'.$dtu_topup_id] == '2') {
                    $dtu_dn_topup_id_array[$dtu_item_prod_id][] = $dtu_topup_id;
                    $dn_products_array[$dtu_item_prod_id]['topid_id'] = $dtu_topup_id;
                    $dn_products_array[$dtu_item_prod_id]['products_id'] = $dtu_item_prod_id;
                    $dn_products_array[$dtu_item_prod_id]['main_products_id'] = ($dtu_item_array[$dtu_topup_id]["products_type"] == 3) ? $dtu_item_array[$dtu_topup_id]['products_id'] : '';
                    $dn_products_array[$dtu_item_prod_id]['products_name'] = $dtu_item_array[$dtu_topup_id]['products_name'];
                    $total_dn_quantity[$dtu_item_prod_id] = $total_dn_quantity[$dtu_item_prod_id] + number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                    $dn_products_array[$dtu_item_prod_id]['products_quantity'] = $total_dn_quantity[$dtu_item_prod_id];
                    $dn_products_array[$dtu_item_prod_id]['products_selling_price'] = round($selling_price, 4);

                    $dn_po_id = $dtu_publisher->get_dn_po_id($dtu_topup_id);
                    $po_price_info = $dtu_publisher->get_dtu_po_final_price($dn_po_id, $dtu_item_prod_id);
                    $round_selling_price = round($po_price_info['products_selling_price'], 4);
                    $round_unit_price_value = round($po_price_info['products_unit_price_value'], 4);
                    if ($po_price_info['products_unit_price_type'] == '%') {
                        $final_po_price = ($round_selling_price - ($round_selling_price * $round_unit_price_value / 100)) * number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                    } else {
                        $final_po_price = ($round_selling_price - $round_unit_price_value) * number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                    }
                    $gst_value = ($po_price_info['purchase_orders_gst_value']) ? $po_price_info['purchase_orders_gst_value'] : 0;
                    $final_po_price = $final_po_price + ($gst_value / 100 * $final_po_price);
                    
                    if ($input_array['dtu_currency'] == DEFAULT_CURRENCY) {
                        if ($po_price_info['currency'] == DEFAULT_CURRENCY) {
                            $final_po_price_dn = $final_po_price;
                        } else {
                            $base_rate = 1;
                            if ($currencies->currencies[$po_price_info['currency']]['value'] > 0) {
                                $base_rate = 1 / $currencies->currencies[$po_price_info['currency']]['value'];
                            }
                            $final_po_price_dn = $base_rate * $final_po_price;
                        }
                    } else {
                        if ($po_price_info['currency'] == $input_array['dtu_currency']) {
                            $final_po_price_dn = $final_po_price;
                        } else {
                            $final_po_price_dn = $currencies->advance_currency_conversion($final_po_price, $po_price_info['currency'], $input_array['dtu_currency'], false);
                        }
                    }

                    $subtotal_dn_price[$dtu_item_prod_id] = $subtotal_dn_price[$dtu_item_prod_id] + $final_po_price_dn;
                    $dn_products_array[$dtu_item_prod_id]['products_unit_price_type'] = $po_price_info['products_unit_price_type'];
                    $dn_products_array[$dtu_item_prod_id]['products_unit_price_value'] = $po_price_info['products_unit_price_value'];
                    $dn_products_array[$dtu_item_prod_id]['products_unit_gst_value'] = $po_price_info['purchase_orders_gst_value'];
                    $dn_products_array[$dtu_item_prod_id]['products_subtotal_price'] = $subtotal_dn_price[$dtu_item_prod_id];
                    $dn_products_array[$dtu_item_prod_id]['original_products_subtotal_price'] = $subtotal_dn_price[$dtu_item_prod_id];
                    $dn_products_array[$dtu_item_prod_id]['dtu_cb_status'] = $input_array['dtu_cb_status_'.$dtu_topup_id];
                }
            }
        } else { // reload products from preview page
            $i = 0;
            foreach ($input_array['dtu_items_prod_id'] as $products_id) {
                foreach ($input_array['dtu_items_topup_id_' . $products_id] as $dtu_topup_id) {
                    $dtu_topup_id_array[$products_id][] = $dtu_topup_id;
                    $dtu_publisher->set_dtu_temp_status($dtu_topup_id);
                }
                $products_array[$i]['products_id'] = $products_id;
                $products_array[$i]['main_products_id'] = $input_array['dtu_items_main_prod_id_' . $products_id];
                $products_array[$i]['products_name'] = $input_array['dtu_items_prod_name_' . $products_id];
                $products_array[$i]['products_quantity'] = $input_array['dtu_item_qty_' . $products_id];
                $products_array[$i]['products_selling_price'] = $input_array['sell_price_' . $products_id];
                $products_array[$i]['products_subtotal_price'] = $input_array['subtotal_' . $products_id];
                $products_array[$i]['original_products_subtotal_price'] = $input_array['original_subtotal_' . $products_id];
                $i++;
            }
            // Charge Back Reload
            if ($input_array['dtu_cb_items_prod_id']) {
                $i = 0;
                foreach ($input_array['dtu_cb_items_prod_id'] as $products_id) {
                    foreach ($input_array['dtu_cb_items_topup_id_' . $products_id] as $dtu_topup_id) {
                        $dtu_cb_topup_id_array[$products_id][] = $dtu_topup_id;
                        $dtu_publisher->set_dtu_temp_status($dtu_topup_id);
                    }
                    $cb_products_array[$i]['products_id'] = $products_id;
                    $cb_products_array[$i]['main_products_id'] = $input_array['dtu_cb_items_main_prod_id_' . $products_id];
                    $cb_products_array[$i]['products_name'] = $input_array['dtu_cb_items_prod_name_' . $products_id];
                    $cb_products_array[$i]['products_quantity'] = $input_array['dtu_cb_item_qty_' . $products_id];
                    $cb_products_array[$i]['products_selling_price'] = $input_array['sell_cb_price_' . $products_id];
                    $cb_products_array[$i]['products_subtotal_price'] = $input_array['subtotal_cb_' . $products_id];
                    $cb_products_array[$i]['original_products_subtotal_price'] = $input_array['original_subtotal_cb_' . $products_id];
                    $cb_products_array[$i]['dtu_cb_status'] = $input_array['dtu_cb_status_cb' . $products_id];
                    $i++;
                }
            }
            // Debit Note Reload
            if ($input_array['dtu_dn_items_prod_id']) {
                $i = 0;
                foreach ($input_array['dtu_dn_items_prod_id'] as $products_id) {
                    foreach ($input_array['dtu_dn_items_topup_id_' . $products_id] as $dtu_topup_id) {
                        $dtu_dn_topup_id_array[$products_id][] = $dtu_topup_id;
                        $dtu_publisher->set_dtu_temp_status($dtu_topup_id);
                    }
                    $dn_products_array[$i]['products_id'] = $products_id;
                    $dn_products_array[$i]['main_products_id'] = $input_array['dtu_dn_items_main_prod_id_' . $products_id];
                    $dn_products_array[$i]['products_name'] = $input_array['dtu_dn_items_prod_name_' . $products_id];
                    $dn_products_array[$i]['products_quantity'] = $input_array['dtu_dn_item_qty_' . $products_id];
                    $dn_products_array[$i]['products_selling_price'] = $input_array['sell_dn_price_' . $products_id];
                    $dn_products_array[$i]['products_subtotal_price'] = $input_array['subtotal_dn_' . $products_id];
                    $dn_products_array[$i]['original_products_subtotal_price'] = $input_array['original_subtotal_dn_' . $products_id];
                    $dn_products_array[$i]['dtu_cb_status'] = $input_array['dtu_cb_status_dn_' . $products_id];
                    $i++;
                }
            }
        }

        // Get CDKey Supplier's payment term info
        switch ($supplier_info['payment_type']) {
            case 'g':
                $dtu_payment = TEXT_SUPPLIER_CONSIGNMENT;
                break;
            case 'c':
                $dtu_payment = TEXT_SUPPLIER_PRE_PAYMENT;
                break;
            case 'd':
                $dtu_payment = TEXT_SUPPLIER_DTU_PAYMENT;
                break;
            case 't':
                $dtu_payment = $supplier_info['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                break;
        }

        $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $dtu_supplier_id, '3');

        ?>
        <?= tep_draw_form('calculate_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=preview_dtu&selected_box=po', 'post', ''); ?>
        <?= tep_draw_hidden_field('dtu_list_type', $input_array['dtu_list_type'], 'id="dtu_list_type"') ?>
        <table width="100%" cellspacing="2" cellpadding="2" border="0">
            <tbody>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_START_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['start_date'] ?><?= tep_draw_hidden_field('start_date', $input_array['start_date'], 'id="start_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CONTACT_PERSON ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['dtu_contact_person'] ?><?= tep_draw_hidden_field('dtu_contact_person', $input_array['dtu_contact_person'], 'id="dtu_contact_person"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUGGESTED_SUPPLIER ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_name'] ?><?= tep_draw_hidden_field('dtu_supplier', $input_array['dtu_supplier'], 'id="dtu_supplier"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $address_str ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DELIVERY_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $delivery_str ?><?= tep_draw_hidden_field('dtu_delivery_address', $input_array['dtu_delivery_address'], 'id="dtu_delivery_address"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_END_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['end_date'] ?><?= tep_draw_hidden_field('end_date', $input_array['end_date'], 'id="end_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_PAYMENT_TERM ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_payment ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DISBURSEMENT_METHOD ?>:</b></td>
                                                    <td class="main" valign="top"><?= tep_draw_hidden_field('dtu_payment_method_currency', $input_array['dtu_payment_method_currency'], 'id="dtu_payment_method_currency"') ?><?= tep_draw_hidden_field('dtu_supplier_payment', $input_array['dtu_supplier_payment'], 'id="dtu_supplier_payment"') ?><?= $pm_obj->payment_accounts[$input_array['dtu_supplier_payment']]['pm_alias'] ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY ?>:</b></td>
                                                    <td class="main" valign="top"><?= $currencies->currencies[$input_array['dtu_currency']]['title'] . ' (' . $currencies->currencies[$input_array['dtu_currency']]['symbol_left'] . $currencies->currencies[$input_array['dtu_currency']]['symbol_right'] . ')' ?><?= tep_draw_hidden_field('dtu_currency', $input_array['dtu_currency'], 'id="dtu_currency"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_SUGGEST_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['suggest_rate'] ?><?= tep_draw_hidden_field('suggest_rate', $input_array['suggest_rate'], 'id="suggest_rate"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_CONFIRM_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['confirm_rate'] ?><?= tep_draw_hidden_field('confirm_rate', $input_array['confirm_rate'], 'id="confirm_rate"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
                </tr>
                <tr>
                    <td>
                        <table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">
                            <tbody>
                                <tr>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_PRODUCT_ID ?></td>
                                    <td class="invoiceBoxHeading" width="30%"><?= TABLE_HEADING_DTU_PRODUCT_NAME ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_QUANTITY ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title"><?=sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title"><?=sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title"><?=sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                </tr>
                                <?
                                if (is_array($products_array) && count($products_array) > 0) {
                                    $rows = 0;
                                    $total = 0;
                                    foreach ($products_array as $product) {
                                        $rows++;
                                        if ($rows % 2 == "1") {
                                            $tr_classname = "reportListingEven";
                                        }
                                        else {
                                            $tr_classname = "reportListingOdd";
                                        }
                                        
                                        $total += $product['products_subtotal_price'];
                                
                                    echo '<tr class="'.$tr_classname.'">';
                                ?>
                                        <td align="center" valign="top" class="invoiceRecordsRightBorder">
                                            <?
                                            foreach ($dtu_topup_id_array[$product['products_id']] as $dtu_topup_id) {
                                                echo tep_draw_hidden_field('dtu_items_topup_id_' . $product['products_id'] . '[]', $dtu_topup_id, 'class="dtu_items_topup_id_' . $product['products_id'] . '"');
                                            }
                                            ?>
                                            <?= tep_draw_hidden_field('dtu_items_prod_id[]', $product['products_id'], 'class="dtu_items_prod_id" id="dtu_items_prod_id_' . $product['products_id'] . '"') ?>
                                            <?= tep_draw_hidden_field('dtu_items_main_prod_id_' . $product['products_id'], $product['main_products_id'], 'id="dtu_items_main_prod_id_' . $product['products_id'] . '"') ?>
                                            <?= $product['products_id'] ?>
                                        </td>
                                        <td nowrap="" valign="top" class="invoiceRecordsRightBorder">
                                            <?= tep_draw_hidden_field('dtu_items_prod_name_' . $product['products_id'], $product['products_name'], 'id="dtu_items_prod_name_' . $product['products_id'] . '"') ?>
                                            <?= $product['products_name'] ?>
                                        </td>
                                        <td valign="top" align="center" class="invoiceRecordsRightBorder">
                                            <?= tep_draw_hidden_field('dtu_item_qty_' . $product['products_id'], $product['products_quantity'], 'id="dtu_item_qty_' . $product['products_id'] . '"') ?>
                                            <div id='dtu_item_qty_<?= $product['products_id'] ?>_div'><?= $product['products_quantity'] ?></div>
                                        </td>
                                        <td valign="top" align="center" class="invoiceRecordsRightBorder">
                                            <?= tep_draw_hidden_field('sell_price_' . $product['products_id'], $product['products_selling_price'], 'id="sell_price_' . $product['products_id'] . '"') ?>
                                            <?= number_format($product['products_selling_price'], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?>
                                        </td>
                                        <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">
                                            <?=tep_draw_pull_down_menu('stock_price_type_' . $product['products_id'], $this->stock_price_type, $input_array['stock_price_type_' . $product['products_id']], 'id="stock_price_type_' . $product['products_id'] . '" onChange="return calculateUnitPrice(\'' . $product['products_id'] . '\', \'\');"')?>
                                            <?=tep_draw_input_field('unit_price_' . $product['products_id'], number_format($input_array['unit_price_' . $products_id], 2, '.', ''), 'size="5" id="unit_price_' . $product['products_id'] . '" onChange="return calculateUnitPrice(\'' . $product['products_id'].'\', \'\');" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0)) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?>
                                        </td>
                                        <td valign="top" align="center  " class="invoiceRecordsRightBorder">
                                            <?= tep_draw_hidden_field('original_subtotal_' . $product['products_id'], round($product['original_products_subtotal_price'], 4), 'id="original_subtotal_' . $product['products_id'] . '"') ?>
                                            <?= tep_draw_hidden_field('subtotal_' . $product['products_id'], round($product['products_subtotal_price'], 4), 'id="subtotal_' . $product['products_id'] . '"') ?>
                                            <?= tep_draw_hidden_field('dtu_cb_status_' . $product['products_id'], $products['dtu_cb_status'], 'id="dtu_cb_status_cb_' . $product['products_id'] . '"') ?>
                                            <div id='subtotal_<?= $product['products_id'] ?>_div'><?= number_format($product['products_subtotal_price'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?></div>
                                        </td>
                                    </tr>
                                <?
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <?
                // START Charge Back Table
                if (is_array($cb_products_array) && count($cb_products_array) > 0) {
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_trans.gif', '1', '10') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_black.gif', '100%', '1') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td valign="top" class="pageHeading"><b>'. TABLE_HEADING_DTU_CB_PRODUCT_DETAILS .'</b></td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>';
                echo '      <table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
                echo '      <tbody>';
                echo '          <tr>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_PRODUCT_ID .'</td>';
                echo '              <td class="invoiceBoxHeading" width="30%">'. TABLE_HEADING_DTU_PRODUCT_NAME .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_QUANTITY .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">'. sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)).'</div></td>';
                echo '              <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title">'. sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)).'</div></td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">'. sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '          </tr>';     
                $rows = 0;
                $total_charge_back = 0;
                foreach ($cb_products_array as $cb_product) {
                    $rows++;
                    if ($rows % 2 == "1") {
                        $tr_classname = "reportListingEven";
                    }
                    else {
                        $tr_classname = "reportListingOdd";
                    }
                    $total_charge_back += $cb_product['products_subtotal_price'];
                    echo '      <tr class="'.$tr_classname.'">';
                    echo '          <td align="center" valign="top" class="invoiceRecordsRightBorder">';                       
                                        foreach ($dtu_cb_topup_id_array[$cb_product['products_id']] as $dtu_topup_id) {
                                            echo tep_draw_hidden_field('dtu_cb_items_topup_id_' . $cb_product['products_id'] . '[]', $dtu_topup_id, 'class="dtu_cb_items_topup_id_' . $cb_product['products_id'] . '"');
                                        }
                                        echo tep_draw_hidden_field('dtu_cb_items_prod_id[]', $cb_product['products_id'], 'class="dtu_cb_items_prod_id" id="dtu_cb_items_prod_id_' . $cb_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('dtu_cb_items_main_prod_id_' . $cb_product['products_id'], $cb_product['main_products_id'], 'id="dtu_cb_items_main_prod_id_' . $cb_product['products_id'] . '"');
                                        echo $cb_product['products_id'];
                    echo '          </td>';
                    echo '          <td nowrap="" valign="top" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('dtu_cb_items_prod_name_' . $cb_product['products_id'], $cb_product['products_name'], 'id="dtu_cb_items_prod_name_' . $cb_product['products_id'] . '"');
                                        echo $cb_product['products_name'];
                    echo '          </td>';
                    echo '          <td valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('dtu_cb_item_qty_' . $cb_product['products_id'], $cb_product['products_quantity'], 'id="dtu_cb_item_qty_' . $cb_product['products_id'] . '"');
                                        echo $cb_product['products_quantity'];
                    echo '          </td>';
                    echo '          <td valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('sell_cb_price_' . $cb_product['products_id'], $cb_product['products_selling_price'], 'id="sell_cb_price_' . $cb_product['products_id'] . '"');
                                        echo number_format($cb_product['products_selling_price'], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']);
                    echo '          </td>';
                    echo '          <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_pull_down_menu('stock_cb_price_type_' . $cb_product['products_id'], $this->stock_price_type, $input_array['stock_price_type_' . $cb_product['products_id']], 'id="stock_cb_price_type_' . $cb_product['products_id'] . '" onChange="return calculateUnitPrice(\'' . $cb_product['products_id'] . '\', \'cb\');"');
                                        echo tep_draw_input_field('unit_cb_price_' . $cb_product['products_id'], number_format($input_array['unit_cb_price_' . $products_id], 2, '.', ''), 'size="5" id="unit_cb_price_' . $cb_product['products_id'] . '" onChange="return calculateUnitPrice(\'' . $cb_product['products_id'].'\', \'cb\');" onKeyUp="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0)) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value),0) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
                    echo '          </td>';
                    echo '          <td valign="top" align="center  " class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('original_subtotal_cb_' . $cb_product['products_id'], $cb_product['original_products_subtotal_price'], 'id="original_subtotal_cb_' . $cb_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('subtotal_cb_' . $cb_product['products_id'], $cb_product['products_subtotal_price'], 'id="subtotal_cb_' . $cb_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('dtu_cb_status_cb_' . $product['products_id'], $cb_product['dtu_cb_status'], 'id="dtu_cb_status_cb_' . $product['products_id'] . '"');
                    echo '              <div id="subtotal_cb_'. $cb_product['products_id'] .'_div">'. number_format($cb_product['products_subtotal_price'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</div>';
                    echo '          </td>';
                    echo '      </tr>';
                }
                echo '          </tbody>';
                echo '      </table>';
                echo '  </td>';
                echo '</tr>';
                }
                // END Charge Back Table
                
                // START Debit Note Table
                if (is_array($dn_products_array) && count($dn_products_array) > 0) {
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_trans.gif', '1', '10') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_black.gif', '100%', '1') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td valign="top" class="pageHeading"><b>'. TABLE_HEADING_DTU_DB_PRODUCT_DETAILS .'</b></td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>';
                echo '      <table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
                echo '      <tbody>';
                echo '          <tr>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_PRODUCT_ID .'</td>';
                echo '              <td class="invoiceBoxHeading" width="30%">'. TABLE_HEADING_DTU_PRODUCT_NAME .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_QUANTITY .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">'. sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)).'</div></td>';
                echo '              <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title">'. sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)).'</div></td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">'. sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '          </tr>';     
                $rows = 0;
                $total_debit_note = 0;
                foreach ($dn_products_array as $dn_product) {
                    $rows++;
                    if ($rows % 2 == "1") {
                        $tr_classname = "reportListingEven";
                    }
                    else {
                        $tr_classname = "reportListingOdd";
                    }
                    $total_debit_note += $dn_product['products_subtotal_price'];
                    echo '      <tr class="'.$tr_classname.'">';
                    echo '          <td align="center" valign="top" class="invoiceRecordsRightBorder">';                       
                                        foreach ($dtu_dn_topup_id_array[$dn_product['products_id']] as $dtu_topup_id) {
                                            echo tep_draw_hidden_field('dtu_dn_items_topup_id_' . $dn_product['products_id'] . '[]', $dtu_topup_id, 'class="dtu_cb_items_topup_id_' . $cb_product['products_id'] . '"');
                                        }
                                        echo tep_draw_hidden_field('dtu_dn_items_prod_id[]', $dn_product['products_id'], 'class="dtu_dn_items_prod_id" id="dtu_dn_items_prod_id_' . $dn_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('dtu_dn_items_main_prod_id_' . $dn_product['products_id'], $dn_product['main_products_id'], 'id="dtu_dn_items_main_prod_id_' . $dn_product['products_id'] . '"');
                                        echo $dn_product['products_id'];
                    echo '          </td>';
                    echo '          <td nowrap="" valign="top" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('dtu_dn_items_prod_name_' . $dn_product['products_id'], $dn_product['products_name'], 'id="dtu_dn_items_prod_name_' . $dn_product['products_id'] . '"');
                                        echo $dn_product['products_name'];
                    echo '          </td>';
                    echo '          <td valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('dtu_dn_item_qty_' . $dn_product['products_id'], $dn_product['products_quantity'], 'id="dtu_dn_item_qty_' . $dn_product['products_id'] . '"');
                                        echo $dn_product['products_quantity'];
                    echo '          </td>';
                    echo '          <td valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('sell_dn_price_' . $dn_product['products_id'], $dn_product['products_selling_price'], 'id="sell_dn_price_' . $dn_product['products_id'] . '"');
                                        echo number_format($dn_product['products_selling_price'], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']);
                    echo '          </td>';
                    echo '          <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">';
                                        echo 'Discount (-)<br />GST (+)';
                    echo '          </td>';
                    echo '          <td valign="top" align="center  " class="invoiceRecordsRightBorder">';
                                        echo tep_draw_hidden_field('original_subtotal_dn_' . $dn_product['products_id'], $dn_product['original_products_subtotal_price'], 'id="original_subtotal_dn_' . $dn_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('subtotal_dn_' . $dn_product['products_id'], $dn_product['products_subtotal_price'], 'id="subtotal_dn_' . $dn_product['products_id'] . '"');
                                        echo tep_draw_hidden_field('dtu_cb_status_dn_' . $product['products_id'], $dn_product['dtu_cb_status'], 'id="dtu_cb_status_dn_' . $product['products_id'] . '"');
                    echo '              <div id="subtotal_dn_'. $dn_product['products_id'] .'_div">'. number_format($dn_product['products_subtotal_price'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</div>';
                    echo '          </td>';
                    echo '      </tr>';
                }
                echo '          </tbody>';
                echo '      </table>';
                echo '  </td>';
                echo '</tr>';
                }
                // END Debit Note Table
                ?>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                        <td valign="top" colspan="2" class="pageHeading"><b>Notes to Supplier</b></td>
                </tr>
                <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                </tr>
                <tr>
                        <td>
                                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                <tbody>
                                        <tr>
                                                <td valign="top">
                                                        <?=tep_draw_textarea_field('notify_supplier_remark', 'soft', '60', '5')?>
                                                </td>
                                        </tr>
                                        <tr>
                                                <td valign="top" class="main"><?=tep_draw_checkbox_field('notify_supplier', '1', $input_array['notify_supplier'] == 'on' ? true : false)?>&nbsp;<b><?=TEXT_NOTIFY_SUPPLIER?></b></td>
                                        </tr>
                                </tbody>
                                </table>
                        </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <!-- Calculate Subtotal -->
                            <tbody id="subtotal_row" class="show">
                                <tr>
                                    <td width="90%" align="right" class="smallText" colspan="3"><?=TABLE_HEADING_BOTTOM_SUBTOTAL?>:</td>
                                    <td width="1%" align="center" class="smallText">&nbsp;</td>
                                    <td width="2%" align="right" class="smallText"><div name="dtu_subtotal_currency" id="po_subtotal_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                    <td width="6%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_subtotal', (isset($input_array['dtu_select_total'])? $input_array['dtu_select_total'] : $total), 'id="dtu_subtotal"')?><div name="dtu_subtotal_div" id="dtu_subtotal_div"><?=number_format((isset($input_array['dtu_select_total'])? $input_array['dtu_select_total'] : $total), 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point'])?></div></td>
                                    <td width="1%" align="center" class="smallText">&nbsp;</td>
                                </tr>
                            </tbody>
                            <!-- Calculate Charge Back -->
                            <?php 
                            if (is_array($cb_products_array) && count($cb_products_array) > 0) {
                                if ($input_array['subaction']=='calculate_dtu') {
                                    $cb_checked = true;
                                } else {
                                    if (isset($input_array['include_cb'])) {
                                        $cb_checked = true;
                                    } else {
                                        $cb_checked = false;
                                        $total_charge_back = 0;
                                    }
                                }
                                echo '<tbody id="subtotal_row" class="show">';
                                echo '    <tr>';
                                echo '        <td width="90%" align="right" class="smallText">'. tep_draw_checkbox_field('include_cb', '1', $cb_checked, '', 'id="include_cb" onChange="refreshCB(this)"') .'</td>';
                                echo '        <td width="1%" align="right" class="smallText" colspan="2" nowrap="nowrap"><div id="cb_title_div">'. TABLE_HEADING_BOTTOM_CHARGE_BACK .':</div></td>';
                                echo '        <td width="1%" align="center" class="smallText">&nbsp;</td>';
                                echo '        <td width="2%" align="right" class="smallText"><div name="dtu_cb_currency" id="dtu_cb_currency">'. (isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY).'</div></td>';
                                echo '        <td width="6%" align="right" class="smallText">'. tep_draw_hidden_field('dtu_cb', (isset($input_array['dtu_cb'])? $input_array['dtu_cb'] : $total_charge_back), 'id="dtu_cb"') .'<div name="dtu_cb_div" id="dtu_cb_div">'. $this->monetary_decimal_format((isset($input_array['dtu_cb'])? $input_array['dtu_cb'] : $total_charge_back)) .'</div></td>';
                                echo '        <td width="1%" align="center" class="smallText">(-)</td>';
                                echo '    </tr>';
                                echo '    <tr class="invoiceListingOdd">';
                                echo '            <td width="90%" align="right" class="boldText" colspan="3">'. TABLE_HEADING_BOTTOM_TOTAL_AFTER_CHARGE_BACK .':</td>';
                                echo '            <td width="1%" align="center" class="boldText">&nbsp;</td>';
                                echo '            <td width="2%" align="right" class="boldText"><div name="dtu_cb_total_currency" id="dtu_cb_total_currency">'. (isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY) .'</div></td>';
                                echo '            <td width="6%" align="right" class="boldText">'. tep_draw_hidden_field('dtu_cb_total', (isset($input_array['dtu_select_total'])? $input_array['dtu_select_total'] : $total) - (isset($input_array['dtu_cb'])? $input_array['dtu_cb'] : $total_charge_back), 'id="dtu_cb_total"') .'<div name="dtu_cb_total_div" id="dtu_cb_total_div">'. number_format((isset($input_array['dtu_select_total'])? $input_array['dtu_select_total'] : $total) - (isset($input_array['dtu_cb'])? $input_array['dtu_cb'] : $total_charge_back), 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</div></td>';
                                echo '            <td width="1%" align="center" class="boldText">&nbsp;</td>';
                                echo '    </tr>';
                                echo '</tbody>';
                            }
                            ?>
                            <!-- Calculate Tax -->
                            <tbody id="gst_row" class="hide">
                                <tr>
                                    <?
                                        if ($input_array['subaction']=='calculate_dtu') {
                                            $gst_checked = true;
                                        } else {
                                            if (isset($input_array['include_gst'])) {
                                                $gst_checked = true;
                                            } else {
                                                $gst_checked = false;
                                                $input_array['dtu_tax'] = 0;
                                            }
                                        }
                                    ?>
                                    <td width="90%" align="right" class="smallText"><?=tep_draw_checkbox_field('include_gst', '1', $gst_checked, '', 'id="include_gst" onChange="refreshGST(this)"')?></td>
                                    <td width="1%" align="right" class="smallText" colspan="2" nowrap="nowrap"><div id="gst_title_div"><?=sprintf(TABLE_HEADING_BOTTOM_GST, '')?>:</div></td>
                                    <td width="1%" align="center" class="smallText">&nbsp;</td>
                                    <td width="2%" align="right" class="smallText"><div name="dtu_gst_currency" id="dtu_gst_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                    <td width="6%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_tax', (isset($input_array['dtu_tax'])? $input_array['dtu_tax'] : ''), 'id="dtu_tax"')?><div name="dtu_tax_div" id="dtu_tax_div"><?=$this->monetary_decimal_format($input_array['dtu_tax'])?></div></td>
                                    <td width="1%" align="center" class="smallText">(+)</td>
                                </tr>
                                <tr class="invoiceListingOdd">
                                        <td width="90%" align="right" class="boldText" colspan="3"><?=TABLE_HEADING_BOTTOM_TOTAL_AFTER_TAX?>:</td>
                                        <td width="1%" align="center" class="boldText">&nbsp;</td>
                                        <td width="2%" align="right" class="boldText"><div name="dtu_tax_total_currency" id="dtu_tax_total_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                        <td width="6%" align="right" class="boldText"><?=tep_draw_hidden_field('dtu_tax_total', '', 'id="dtu_tax_total"')?><div name="dtu_tax_total_div" id="dtu_tax_total_div"><?=$this->monetary_decimal_format($input_array['dtu_tax_total'])?></div></td>
                                        <td width="1%" align="center" class="boldText">&nbsp;</td>
                                </tr>
                            </tbody>
                            <!-- Calculate Debit Note -->
                            <?php 
                            if (is_array($dn_products_array) && count($dn_products_array) > 0) {
                                if ($input_array['subaction']=='calculate_dtu') {
                                    $dn_checked = true;
                                } else {
                                    if (isset($input_array['include_dn'])) {
                                        $dn_checked = true;
                                    } else {
                                        $dn_checked = false;
                                        $total_debit_note = 0;
                                    }
                                }
                                echo '<tbody id="subtotal_row" class="show">';
                                echo '    <tr>';
                                echo '        <td width="90%" align="right" class="smallText">'. tep_draw_checkbox_field('include_dn', '1', $dn_checked, '', 'id="include_dn" onChange="refreshDN(this)"') .'</td>';
                                echo '        <td width="1%" align="right" class="smallText" colspan="2" nowrap="nowrap"><div id="dn_title_div">'. TABLE_HEADING_BOTTOM_DEBIT_NOTE .':</div></td>';
                                echo '        <td width="1%" align="center" class="smallText">&nbsp;</td>';
                                echo '        <td width="2%" align="right" class="smallText"><div name="dtu_dn_currency" id="dtu_dn_currency">'. (isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY).'</div></td>';
                                echo '        <td width="6%" align="right" class="smallText">'. tep_draw_hidden_field('dtu_dn', (isset($input_array['dtu_dn'])? $input_array['dtu_dn'] : $total_debit_note), 'id="dtu_dn"') .'<div name="dtu_dn_div" id="dtu_dn_div">'. number_format((isset($input_array['dtu_dn'])? $input_array['dtu_dn'] : $total_debit_note), 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</div></td>';
                                echo '        <td width="1%" align="center" class="smallText">(-)</td>';
                                echo '    </tr>';
                                echo '</tbody>';
                            }
                            ?>
                            <!-- Calculate Bank Charge -->
                            <?php
                            $bank_charge_curr = (isset($input_array['dtu_pay_bankcharge_currency'])? $input_array['dtu_pay_bankcharge_currency'] : $input_array['dtu_payment_method_currency']);
                            ?>
                            <tbody id="bankcharges_row" class="show">
                                <tr>
                                    <td width="90%" align="right" class="smallText"><?=TABLE_HEADING_BOTTOM_BANK_CHARGES?>:</td>
                                    <td width="2%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_pay_bankcharge_currency', '', 'id="dtu_pay_bankcharge_currency"')?><div name="dtu_pay_bankcharge_currency_div" id="dtu_pay_bankcharge_currency_div"><?=(isset($bank_charge_curr)? $bank_charge_curr : DEFAULT_CURRENCY)?></div></td>
                                    <td width="6%" align="right" class="smallText"><?=tep_draw_input_field('dtu_pay_bankcharge', (isset($input_array['dtu_pay_bankcharge'])? $input_array['dtu_pay_bankcharge'] : '0.00'), 'id="dtu_pay_bankcharge" onChange="return refreshSummaryTotal();" size="7" style="text-align: right;"')?></td>
                                    <td width="1%" align="center" class="smallText">=</td>
                                    <td width="2%" align="center" class="smallText"><div name="dtu_bankcharge_currency" id="dtu_bankcharge_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                    <td width="2%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_bankcharge', (isset($input_array['dtu_bankcharge'])? $input_array['dtu_bankcharge'] : '0.00'), 'id="dtu_bankcharge"')?><div name="dtu_bankcharge_div" id="dtu_bankcharge_div"><?=$this->monetary_decimal_format($input_array['dtu_bankcharge'])?></div></td>
                                    <td width="1%" align="center" class="smallText">(+)</td>
                                </tr>
                            </tbody>
                            <?php
                            $adjustment_curr = (isset($input_array['dtu_pay_adjustment_currency'])? $input_array['dtu_pay_adjustment_currency'] : $input_array['dtu_payment_method_currency']);
                            $adjustment_cal = array(array('id'=>'1', 'text'=>'+'), array('id'=>'2', 'text'=>'-'));
                            ?>
                            <tbody id="adjustment_row" class="show">
                                <tr>
                                    <td width="90%" align="right" class="smallText"><?=TABLE_HEADING_BOTTOM_ADJUSTMENT?>:</td>
                                    <td width="2%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_pay_adjustment_currency', (isset($adjustment_curr)? $adjustment_curr : DEFAULT_CURRENCY), 'id="dtu_pay_adjustment_currency"')?><div name="dtu_pay_adjustment_currency" id="dtu_pay_adjustment_currency"><?=(isset($adjustment_curr)? $adjustment_curr : DEFAULT_CURRENCY)?></div></td>
                                    <td width="6%" align="right" class="smallText"><?=tep_draw_input_field('dtu_pay_adjustment', (isset($input_array['dtu_pay_adjustment'])? $input_array['dtu_pay_adjustment'] : '0.00'), 'id="dtu_pay_adjustment" onChange="return refreshSummaryTotal();" size="7" style="text-align: right;"')?></td>
                                    <td width="1%" align="center" class="smallText">=</td>
                                    <td width="2%" align="center" class="smallText"><div name="dtu_adjustment_currency" id="dtu_adjustment_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                    <td width="2%" align="right" class="smallText"><?=tep_draw_hidden_field('dtu_adjustment', (isset($input_array['dtu_adjustment'])? $input_array['dtu_adjustment'] : '0.00'), 'id="dtu_adjustment"')?><div name="dtu_adjustment_div" id="dtu_adjustment_div"><?=$this->monetary_decimal_format($input_array['dtu_adjustment'])?></div></td>
                                    <td width="1%" align="center" class="smallText"><?=tep_draw_pull_down_menu('adjustment_type', $adjustment_cal, $input_array['adjustment_type'], ' id="adjustment_type" onChange="return refreshSummaryTotal();"')?></td>
                                </tr>
                            </tbody>
                            <!-- Calculate Total Payable -->
                            <tbody id="total_row" class="show">
                                <tr class="invoiceListingOdd">
                                    <td width="90%" align="right" class="boldText" colspan="3"><?=TABLE_HEADING_BOTTOM_TOTAL_PAYABLE_AMOUNT?>:</td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                    <td width="2%" align="right" class="boldText"><div name="dtu_payable_total_currency" id="dtu_payable_total_currency"><?=(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)?></div></td>
                                    <td width="6%" align="right" class="boldText"><?=tep_draw_hidden_field('dtu_payable_total', (isset($input_array['dtu_payable_total'])? $input_array['dtu_payable_total'] : '0.0000'), 'id="dtu_payable_total"')?><div name="dtu_payable_total_div" id="dtu_payable_total_div"><?=$this->monetary_decimal_format($input_array['dtu_payable_total'])?></div></td>
                                    <td width="1%" align="center" class="boldText">&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td align="left"><?= tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtnToList"', 'inputButton') ?></td>
                                    <td align="right"><?=tep_submit_button(BUTTON_PREVIEW_DTU, ALT_BUTTON_PREVIEW_DTU, '', 'inputButton');?></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        </form>
        <SCRIPT language="JavaScript" type="text/javascript">
        <!--
        <?
            if (isset($input_array['dtu_delivery_address'])) {
                echo "getGST(DOMCall('dtu_delivery_address'));";
            }
        ?>
        //-->
        </SCRIPT>
        <?
    }
    
    function preview_dtu_form($input_array) {
        
        global $currencies, $po_suppliers;
        
        $dtu_publisher = new publishers($input_array['dtu_supplier']);
        $dtu_supplier_info = $dtu_publisher->get_publishers();
        $dtu_publisher->set_dtu_temp_status_empty();
        $dtu_supplier_id = $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_supplier_id'];

        $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));
        $supplier_name = $supplier_info['customers_firstname'] . ' ' . $supplier_info['customers_lastname'];
        $countries_info = tep_get_countries_info($supplier_info['country_id'], 'countries_id');
        $address_str = tep_address_format($countries_info['address_format_id'], $supplier_info, 1, '', '<br>', 'main');
        $delivery_str = $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['company'];
        $gst_value = $this->po_company_gst[$input_array['dtu_delivery_address']];

        // Get CDKey Supplier's payment term info
        switch ($supplier_info['payment_type']) {
            case 'g':
                $dtu_payment = TEXT_SUPPLIER_CONSIGNMENT;
                break;
            case 'c':
                $dtu_payment = TEXT_SUPPLIER_PRE_PAYMENT;
                break;
            case 'd':
                $dtu_payment = TEXT_SUPPLIER_DTU_PAYMENT;
                break;
            case 't':
                $dtu_payment = $supplier_info['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                break;
        }

        $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $dtu_supplier_id, '3');

        $po_bank_charge_amt = 0;
        if (isset($input_array['dtu_pay_bankcharge'])) {
            $po_bank_charge_amt = round(($input_array['dtu_pay_bankcharge'] * (1 / $input_array['confirm_rate'])), $currencies->currencies[$input_array['dtu_currency']]['decimal_places']);
        }
        ?>
        <?= tep_draw_form('preview_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=insert_dtu_product&selected_box=po', 'post', ''); ?>
        <?= tep_draw_hidden_field('dtu_list_type', $input_array['dtu_list_type'], 'id="dtu_list_type"') ?>
        <table width="100%" cellspacing="2" cellpadding="2" border="0">
            <tbody>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_START_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['start_date'] ?><?= tep_draw_hidden_field('start_date', $input_array['start_date'], 'id="start_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CONTACT_PERSON ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['dtu_contact_person'] ?><?= tep_draw_hidden_field('dtu_contact_person', $input_array['dtu_contact_person'], 'id="dtu_contact_person"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUGGESTED_SUPPLIER ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_name'] ?><?= tep_draw_hidden_field('dtu_supplier', $input_array['dtu_supplier'], 'id="dtu_supplier"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $address_str ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DELIVERY_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $delivery_str ?><?= tep_draw_hidden_field('dtu_delivery_address', $input_array['dtu_delivery_address'], 'id="dtu_delivery_address"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_END_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['end_date'] ?><?= tep_draw_hidden_field('end_date', $input_array['end_date'], 'id="end_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_PAYMENT_TERM ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_payment ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DISBURSEMENT_METHOD ?>:</b></td>
                                                    <td class="main" valign="top"><?= $pm_obj->payment_accounts[$input_array['dtu_supplier_payment']]['pm_alias'] ?><?= tep_draw_hidden_field('dtu_supplier_payment', $input_array['dtu_supplier_payment'], 'id="dtu_supplier_payment"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY ?>:</b></td>
                                                    <td class="main" valign="top"><?= $currencies->currencies[$input_array['dtu_currency']]['title'] . ' (' . $currencies->currencies[$input_array['dtu_currency']]['symbol_left'] . $currencies->currencies[$input_array['dtu_currency']]['symbol_right'] . ')' ?><?= tep_draw_hidden_field('dtu_currency', $input_array['dtu_currency'], 'id="dtu_currency"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_SUGGEST_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['suggest_rate'] ?><?= tep_draw_hidden_field('suggest_rate', $input_array['suggest_rate'], 'id="suggest_rate"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_CONFIRM_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['confirm_rate'] ?><?= tep_draw_hidden_field('confirm_rate', $input_array['confirm_rate'], 'id="confirm_rate"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
                </tr>
                <tr>
                    <td>
                        <table id="new_po_products" width="100%" cellspacing="0" cellpadding="2" style="border: 1px solid black">
                            <tbody>
                                <tr>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_PRODUCT_ID ?></td>
                                    <td class="invoiceBoxHeading" width="30%"><?= TABLE_HEADING_DTU_PRODUCT_NAME ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_QUANTITY ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title"><?=sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title"><?=sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title"><?=sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY))?></div></td>
                                </tr>
                                <?
                                if (is_array($input_array['dtu_items_prod_id'])) {
                                    foreach ($input_array['dtu_items_prod_id'] as $dtu_products_id) {
                                        $prd_check_search_sql = "SELECT count(products_id) AS prd_cnt FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($dtu_products_id) . "'";
                                        $prd_check_row = tep_db_fetch_array(tep_db_query($prd_check_search_sql));
                                        if ($prd_check_row['prd_cnt'] > 0) {
                                            ?>
                                            <tr>
                                                <td align="center" valign="top" class="invoiceRecordsRightBorder">
                                                    <?
                                                    foreach ($input_array['dtu_items_topup_id_' . $dtu_products_id] as $dtu_topup_id) {
                                                        echo tep_draw_hidden_field('dtu_items_topup_id_' . $dtu_products_id . '[]', $dtu_topup_id);
                                                    }
                                                    ?>
                                                    <?= tep_draw_hidden_field('dtu_items_prod_id[]', $dtu_products_id, 'class="dtu_items_prod_id" id="dtu_items_prod_id_' . $dtu_products_id . '"') ?>
                                                    <?= tep_draw_hidden_field('dtu_items_main_prod_id_' . $dtu_products_id, $input_array['dtu_items_main_prod_id_' . $dtu_products_id], 'id="dtu_items_main_prod_id_' . $dtu_products_id . '"') ?>
                                                    <?= $dtu_products_id ?></td>
                                                <td nowrap="" valign="top" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('dtu_items_prod_name_' . $dtu_products_id, $input_array['dtu_items_prod_name_' . $dtu_products_id], 'id="dtu_items_prod_name_' . $dtu_products_id . '"') ?><?= $input_array['dtu_items_prod_name_' . $dtu_products_id] ?></td>
                                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('dtu_item_qty_' . $dtu_products_id, $input_array['dtu_item_qty_' . $dtu_products_id], 'id="dtu_item_qty_' . $dtu_products_id . '"') ?><?= $input_array['dtu_item_qty_' . $dtu_products_id] ?></td>
                                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('sell_price_' . $dtu_products_id, $input_array['sell_price_' . $dtu_products_id], 'id="sell_price_' . $dtu_products_id . '"') ?><?= number_format($input_array['sell_price_' . $dtu_products_id], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?></td>
                                                <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">
                                                    <?= tep_draw_hidden_field('stock_price_type_' . $dtu_products_id, $input_array['stock_price_type_' . $dtu_products_id], 'id="stock_price_type_' . $dtu_products_id . '"') ?><?= tep_draw_hidden_field('unit_price_' . $dtu_products_id, number_format($input_array['unit_price_' . $dtu_products_id], 6, '.', ''), 'id="unit_price_' . $dtu_products_id . '"') ?>
                                                    <?= $input_array['stock_price_type_' . $dtu_products_id] . " " . $this->monetary_decimal_format($input_array['unit_price_' . $dtu_products_id]) ?>
                                                </td>
                                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('original_subtotal_' . $dtu_products_id, $input_array['original_subtotal_' . $dtu_products_id], 'id="original_subtotal_' . $dtu_products_id . '"') . tep_draw_hidden_field('subtotal_' . $dtu_products_id, $input_array['subtotal_' . $dtu_products_id], 'id="subtotal_' . $dtu_products_id . '"') . tep_draw_hidden_field('dtu_cb_status_' . $dtu_products_id, $dn_products['dtu_cb_status_'.$dtu_products_id], 'id="dtu_cb_status_' . $dtu_products_id . '"') . number_format($input_array['subtotal_' . $dtu_products_id], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?></td>
                                            </tr>
                                            <?
                                        }
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <?php
                // START Charge Back Table
                if (isset($input_array['include_cb']) && ($input_array['include_cb'] == '1' || $input_array['include_cb'] == 'on')) {
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_trans.gif', '1', '10') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_black.gif', '100%', '1') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td valign="top" class="pageHeading"><b>'. TABLE_HEADING_DTU_CB_PRODUCT_DETAILS .'</b></td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>';
                echo '    <table id="new_po_products" width="100%" cellspacing="0" cellpadding="2" style="border: 1px solid black">';
                echo '      <tbody>';
                echo '          <tr>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_PRODUCT_ID .'</td>';
                echo '              <td class="invoiceBoxHeading" width="30%">'. TABLE_HEADING_DTU_PRODUCT_NAME .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_QUANTITY .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">'. sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '              <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title">'. sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">'. sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '          </tr>';
                foreach ($input_array['dtu_cb_items_prod_id'] as $dtu_products_id) {
                    $prd_check_search_sql = "SELECT count(products_id) AS prd_cnt FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($dtu_products_id) . "'";
                    $prd_check_row = tep_db_fetch_array(tep_db_query($prd_check_search_sql));
                    if ($prd_check_row['prd_cnt'] > 0) {
                        echo '  <tr>';
                        echo '      <td align="center" valign="top" class="invoiceRecordsRightBorder">';
                        foreach ($input_array['dtu_cb_items_topup_id_' . $dtu_products_id] as $dtu_topup_id) {
                            echo tep_draw_hidden_field('dtu_cb_items_topup_id_' . $dtu_products_id . '[]', $dtu_topup_id);
                        }
                        echo tep_draw_hidden_field('dtu_cb_items_prod_id[]', $dtu_products_id, 'class="dtu_cb_items_prod_id" id="dtu_cb_items_prod_id_' . $dtu_products_id . '"') . tep_draw_hidden_field('dtu_cb_items_main_prod_id_', $input_array['dtu_cb_items_main_prod_id_' . $dtu_products_id], 'id="dtu_cb_items_main_prod_id_' . $dtu_products_id . '"') . $dtu_products_id .'</td>';

                        echo'       <td nowrap="" valign="top" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('dtu_cb_items_prod_name_' . $dtu_products_id, $input_array['dtu_cb_items_prod_name_' . $dtu_products_id], 'id="dtu_cb_items_prod_name_' . $dtu_products_id . '"') . $input_array['dtu_cb_items_prod_name_' . $dtu_products_id] .'</td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('dtu_cb_item_qty_' . $dtu_products_id, $input_array['dtu_cb_item_qty_' . $dtu_products_id], 'id="dtu_cb_item_qty_' . $dtu_products_id . '"') . $input_array['dtu_cb_item_qty_' . $dtu_products_id] .'</td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('sell_cb_price_' . $dtu_products_id, $input_array['sell_cb_price_' . $dtu_products_id], 'id="sell_cb_price_' . $dtu_products_id . '"') . number_format($input_array['sell_cb_price_' . $dtu_products_id], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</td>';
                        echo '      <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">';
                        echo  tep_draw_hidden_field('stock_cb_price_type_' . $dtu_products_id, $input_array['stock_cb_price_type_' . $dtu_products_id], 'id="stock_cb_price_type_' . $dtu_products_id . '"') . tep_draw_hidden_field('unit_cb_price_' . $dtu_products_id, number_format($input_array['unit_cb_price_' . $dtu_products_id], 6, '.', ''), 'id="unit_cb_price_' . $dtu_products_id . '"');
                        echo $input_array['stock_cb_price_type_' . $dtu_products_id] . " " . $this->monetary_decimal_format($input_array['unit_cb_price_' . $dtu_products_id]);
                        echo '      </td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('original_subtotal_cb_' . $dtu_products_id, $input_array['original_subtotal_cb_' . $dtu_products_id], 'id="original_subtotal_cb_' . $dtu_products_id . '"') . tep_draw_hidden_field('subtotal_cb_' . $dtu_products_id, $input_array['subtotal_cb_' . $dtu_products_id], 'id="subtotal_cb_' . $dtu_products_id . '"') . tep_draw_hidden_field('dtu_cb_status_cb_' . $dtu_products_id, $dn_products['dtu_cb_status_cb_'.$dtu_products_id], 'id="dtu_cb_status_cb_' . $dtu_products_id . '"') . number_format($input_array['subtotal_cb_' . $dtu_products_id], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</td>';
                        echo '  </tr>';
                    }
                }
                echo '          </tbody>';
                echo '      </table>';
                echo '  </td>';
                echo '</tr>';
                } else if (isset($input_array['dtu_cb_items_prod_id']) && count($input_array['dtu_cb_items_prod_id']) > 0) {
                    foreach ($input_array['dtu_cb_items_prod_id'] as $dtu_products_id) {
                        $prd_check_search_sql = "SELECT count(products_id) AS prd_cnt FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($dtu_products_id) . "'";
                        $prd_check_row = tep_db_fetch_array(tep_db_query($prd_check_search_sql));
                        if ($prd_check_row['prd_cnt'] > 0) {
                            foreach ($input_array['dtu_cb_items_topup_id_' . $dtu_products_id] as $dtu_topup_id) {
                                echo tep_draw_hidden_field('dtu_cb_items_topup_id_' . $dtu_products_id . '[]', $dtu_topup_id);
                            }
                            echo tep_draw_hidden_field('dtu_cb_items_prod_id[]', $dtu_products_id, 'class="dtu_cb_items_prod_id" id="dtu_cb_items_prod_id_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_cb_items_main_prod_id_' . $dtu_products_id, $input_array['dtu_cb_items_main_prod_id_' . $dtu_products_id], 'id="dtu_cb_items_main_prod_id_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_cb_items_prod_name_' . $dtu_products_id, $input_array['dtu_cb_items_prod_name_' . $dtu_products_id], 'id="dtu_cb_items_prod_name_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_cb_item_qty_' . $dtu_products_id, $input_array['dtu_cb_item_qty_' . $dtu_products_id], 'id="dtu_cb_item_qty_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('sell_cb_price_' . $dtu_products_id, $input_array['sell_cb_price_' . $dtu_products_id], 'id="sell_cb_price_' . $dtu_products_id . '"');
                            echo  tep_draw_hidden_field('stock_cb_price_type_' . $dtu_products_id, $input_array['stock_cb_price_type_' . $dtu_products_id], 'id="stock_cb_price_type_' . $dtu_products_id . '"'); 
                            echo tep_draw_hidden_field('unit_cb_price_' . $dtu_products_id, number_format($input_array['unit_cb_price_' . $dtu_products_id], 6, '.', ''), 'id="unit_cb_price_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('original_subtotal_cb_' . $dtu_products_id, $input_array['original_subtotal_cb_' . $dtu_products_id], 'id="original_subtotal_cb_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('subtotal_cb_' . $dtu_products_id, $input_array['subtotal_cb_' . $dtu_products_id], 'id="subtotal_cb_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_cb_status_cb_' . $dtu_products_id, $dn_products['dtu_cb_status_cb_'.$dtu_products_id], 'id="dtu_cb_status_cb_' . $dtu_products_id . '"');
                        }
                    }
                }
                ?>
                <?php
                // START Debit Note Table
                if (isset($input_array['include_dn']) && ($input_array['include_dn'] == '1' || $input_array['include_dn'] == 'on')) {
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_trans.gif', '1', '10') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>'. tep_draw_separator('pixel_black.gif', '100%', '1') .'</td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td valign="top" class="pageHeading"><b>'. TABLE_HEADING_DTU_DB_PRODUCT_DETAILS .'</b></td>';
                echo '</tr>';
                echo '<tr>';
                echo '  <td>';
                echo '    <table id="new_po_products" width="100%" cellspacing="0" cellpadding="2" style="border: 1px solid black">';
                echo '      <tbody>';
                echo '          <tr>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_PRODUCT_ID .'</td>';
                echo '              <td class="invoiceBoxHeading" width="30%">'. TABLE_HEADING_DTU_PRODUCT_NAME .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="8%">'. TABLE_HEADING_DTU_QUANTITY .'</td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title">'. sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '              <td width="10%" align="center" class="invoiceBoxHeading"><div id="stock_price_title">'. sprintf(TABLE_HEADING_STOCK_PRICE,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '              <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title">'. sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['dtu_currency'])? $input_array['dtu_currency'] : DEFAULT_CURRENCY)) .'</div></td>';
                echo '          </tr>';
                foreach ($input_array['dtu_dn_items_prod_id'] as $dtu_products_id) {
                    $prd_check_search_sql = "SELECT count(products_id) AS prd_cnt FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($dtu_products_id) . "'";
                    $prd_check_row = tep_db_fetch_array(tep_db_query($prd_check_search_sql));
                    if ($prd_check_row['prd_cnt'] > 0) {
                        echo '  <tr>';
                        echo '      <td align="center" valign="top" class="invoiceRecordsRightBorder">';
                        foreach ($input_array['dtu_dn_items_topup_id_' . $dtu_products_id] as $dtu_topup_id) {
                            echo tep_draw_hidden_field('dtu_dn_items_topup_id_' . $dtu_products_id . '[]', $dtu_topup_id);
                        }
                        echo tep_draw_hidden_field('dtu_dn_items_prod_id[]', $dtu_products_id, 'class="dtu_dn_items_prod_id" id="dtu_dn_items_prod_id_' . $dtu_products_id . '"') . tep_draw_hidden_field('dtu_dn_items_main_prod_id_', $input_array['dtu_dn_items_main_prod_id_' . $dtu_products_id], 'id="dtu_dn_items_main_prod_id_' . $dtu_products_id . '"') . $dtu_products_id .'</td>';
                        echo'       <td nowrap="" valign="top" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('dtu_dn_items_prod_name_' . $dtu_products_id, $input_array['dtu_dn_items_prod_name_' . $dtu_products_id], 'id="dtu_dn_items_prod_name_' . $dtu_products_id . '"') . $input_array['dtu_dn_items_prod_name_' . $dtu_products_id] .'</td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('dtu_dn_item_qty_' . $dtu_products_id, $input_array['dtu_dn_item_qty_' . $dtu_products_id], 'id="dtu_dn_item_qty_' . $dtu_products_id . '"') . $input_array['dtu_dn_item_qty_' . $dtu_products_id] .'</td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('sell_dn_price_' . $dtu_products_id, $input_array['sell_dn_price_' . $dtu_products_id], 'id="sell_dn_price_' . $dtu_products_id . '"') . number_format($input_array['sell_dn_price_' . $dtu_products_id], '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</td>';
                        echo '      <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder">';
                        echo 'Discount (-)<br />GST (-)';
                        echo '      </td>';
                        echo '      <td valign="top" align="center" class="invoiceRecordsRightBorder">'. tep_draw_hidden_field('original_subtotal_dn_' . $dtu_products_id, $input_array['original_subtotal_dn_' . $dtu_products_id], 'id="original_subtotal_dn_' . $dtu_products_id . '"') . tep_draw_hidden_field('subtotal_dn_' . $dtu_products_id, $input_array['subtotal_dn_' . $dtu_products_id], 'id="subtotal_dn_' . $dtu_products_id . '"') . tep_draw_hidden_field('dtu_cb_status_dn_' . $dtu_products_id, $dn_products['dtu_cb_status_dn_'.$dtu_products_id], 'id="dtu_cb_status_dn_' . $dtu_products_id . '"') . number_format($input_array['subtotal_dn_' . $dtu_products_id], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) .'</td>';
                        echo '  </tr>';
                    }
                }
                echo '          </tbody>';
                echo '      </table>';
                echo '  </td>';
                echo '</tr>';
                } else if (isset($input_array['dtu_dn_items_prod_id']) && count($input_array['dtu_dn_items_prod_id']) > 0) {
                    foreach ($input_array['dtu_dn_items_prod_id'] as $dtu_products_id) {
                        $prd_check_search_sql = "SELECT count(products_id) AS prd_cnt FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($dtu_products_id) . "'";
                        $prd_check_row = tep_db_fetch_array(tep_db_query($prd_check_search_sql));
                        if ($prd_check_row['prd_cnt'] > 0) {
                            foreach ($input_array['dtu_dn_items_topup_id_' . $dtu_products_id] as $dtu_topup_id) {
                                echo tep_draw_hidden_field('dtu_dn_items_topup_id_' . $dtu_products_id . '[]', $dtu_topup_id);
                            }
                            echo tep_draw_hidden_field('dtu_dn_items_prod_id[]', $dtu_products_id, 'class="dtu_dn_items_prod_id" id="dtu_dn_items_prod_id_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_dn_items_main_prod_id_' . $dtu_products_id, $input_array['dtu_dn_items_main_prod_id_' . $dtu_products_id], 'id="dtu_dn_items_main_prod_id_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_dn_items_prod_name_' . $dtu_products_id, $input_array['dtu_dn_items_prod_name_' . $dtu_products_id], 'id="dtu_dn_items_prod_name_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_dn_item_qty_' . $dtu_products_id, $input_array['dtu_dn_item_qty_' . $dtu_products_id], 'id="dtu_dn_item_qty_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('sell_dn_price_' . $dtu_products_id, $input_array['sell_dn_price_' . $dtu_products_id], 'id="sell_dn_price_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('original_subtotal_dn_' . $dtu_products_id, $input_array['original_subtotal_dn_' . $dtu_products_id], 'id="original_subtotal_dn_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('subtotal_dn_' . $dtu_products_id, $input_array['subtotal_dn_' . $dtu_products_id], 'id="subtotal_dn_' . $dtu_products_id . '"');
                            echo tep_draw_hidden_field('dtu_cb_status_dn_' . $dtu_products_id, $dn_products['dtu_cb_status_dn_'.$dtu_products_id], 'id="dtu_cb_status_dn_' . $dtu_products_id . '"');
                        }
                    }
                }
                ?>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td valign="top" colspan="2" class="pageHeading"><b>Notes to Supplier</b></td>
                </tr>
                <tr>
                    <td valign="top" class="main">
                        <table width="30%" cellspacing="0" cellpadding="2" style="border: 1px solid black">
                            <tbody>
                                <tr>
                                    <td valign="top" class="main">
                                        <?= tep_draw_hidden_field('notify_supplier_remark', $input_array['notify_supplier_remark'], 'id="notify_supplier_remark"') ?>
                                        <?= $input_array['notify_supplier_remark'] ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <?= tep_draw_hidden_field('notify_supplier', $input_array['notify_supplier'], 'id="notify_supplier"') ?>
                        <?= (isset($input_array['notify_supplier']) ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) . "&nbsp;<b>" . TEXT_NOTIFY_SUPPLIER . "</b>" : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS) . "&nbsp;<b>" . TEXT_NOTIFY_SUPPLIER . "</b>" ) ?>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="90%" align="right" class="smallText"><?= TABLE_HEADING_BOTTOM_SUBTOTAL ?>:</td>
                                    <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                    <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_subtotal', $input_array['dtu_subtotal'], 'id="dtu_subtotal"') ?><?= number_format($input_array['dtu_subtotal'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                    <td width="2%" align="center" class="smallText">&nbsp;</td>
                                </tr>
                                <? if (isset($input_array['include_cb']) && ($input_array['include_cb'] == '1' || $input_array['include_cb'] == 'on')) { ?>
                                    <tr>
                                        <td width="90%" align="right" class="smallText"><?= tep_draw_hidden_field('include_cb', $input_array['include_cb'], 'id="include_cb"') ?><?= sprintf(TABLE_HEADING_BOTTOM_CHARGE_BACK, $this->monetary_decimal_format($gst_value, '%01.1f')) ?>:</td>
                                        <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_cb', $input_array['dtu_cb'], 'id="dtu_cb"') ?><?= number_format($input_array['dtu_cb'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="smallText">(-)</td>
                                    </tr>
                                    <tr class="invoiceListingOdd">
                                        <td width="90%" align="right" class="boldText"><?= TABLE_HEADING_BOTTOM_TOTAL_AFTER_CHARGE_BACK ?>:</td>
                                        <td width="2%" align="right" class="boldText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('dtu_cb_total', $input_array['dtu_cb_total'], 'id="dtu_cb_total"') ?><?= number_format($input_array['dtu_cb_total'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="boldText">&nbsp;</td>
                                    </tr>
                                <? } ?>
                                <? if (isset($input_array['include_gst']) && ($input_array['include_gst'] == '1' || $input_array['include_gst'] == 'on')) { ?>
                                    <tr>
                                        <td width="90%" align="right" class="smallText"><?= tep_draw_hidden_field('include_gst', $input_array['include_gst'], 'id="include_gst"') ?><?= sprintf(TABLE_HEADING_BOTTOM_GST, $this->monetary_decimal_format($gst_value, '%01.1f')) ?>:</td>
                                        <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_tax', $input_array['dtu_tax'], 'id="dtu_tax"') ?><?= number_format($input_array['dtu_tax'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="smallText">(+)</td>
                                    </tr>
                                    <tr class="invoiceListingOdd">
                                        <td width="90%" align="right" class="boldText"><?= TABLE_HEADING_BOTTOM_TOTAL_AFTER_TAX ?>:</td>
                                        <td width="2%" align="right" class="boldText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('dtu_tax_total', $input_array['dtu_tax_total'], 'id="dtu_tax_total"') ?><?= number_format($input_array['dtu_tax_total'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="boldText">&nbsp;</td>
                                    </tr>
                                <? } ?>
                                <? $total_payable_view = 0; ?>
                                <? if (isset($input_array['include_dn']) && ($input_array['include_dn'] == '1' || $input_array['include_dn'] == 'on')) { ?>
                                <? $total_payable_view = 1; ?>
                                    <tr>
                                        <td width="90%" align="right" class="smallText"><?= tep_draw_hidden_field('include_dn', $input_array['include_dn'], 'id="include_cb"') ?><?= sprintf(TABLE_HEADING_BOTTOM_DEBIT_NOTE, $this->monetary_decimal_format($gst_value, '%01.1f')) ?>:</td>
                                        <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_dn', $input_array['dtu_dn'], 'id="dtu_dn"') ?><?= number_format($input_array['dtu_dn'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="smallText">(-)</td>
                                    </tr>
                                <? } ?>
                                <? if (isset($input_array['dtu_bankcharge']) && $input_array['dtu_bankcharge'] > 0) { ?>
                                <? $total_payable_view = 1; ?>
                                    <tr>
                                        <td width="90%" align="right" class="smallText"><?= TABLE_HEADING_BOTTOM_BANK_CHARGES ?>:</td>
                                        <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_bankcharge', $input_array['dtu_bankcharge'], 'id="dtu_bankcharge"') ?><?= number_format($input_array['dtu_bankcharge'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="smallText">(+)<?= tep_draw_hidden_field('dtu_pay_bankcharge', $input_array['dtu_pay_bankcharge'], 'id="dtu_pay_bankcharge"') ?></td>
                                    </tr>
                                <? } ?>
                                <? if (isset($input_array['dtu_adjustment']) && $input_array['dtu_adjustment'] > 0) { ?>
                                <? $total_payable_view = 1; ?>
                                    <tr>
                                        <td width="90%" align="right" class="smallText"><?= TABLE_HEADING_BOTTOM_ADJUSTMENT ?>:</td>
                                        <td width="2%" align="right" class="smallText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_adjustment', $input_array['dtu_adjustment'], 'id="dtu_adjustment"') ?><?= number_format($input_array['dtu_adjustment'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="smallText"><?= ($input_array['adjustment_type']==1) ? '(+)' : '(-)' ; ?><?= tep_draw_hidden_field('dtu_pay_adjustment', $input_array['dtu_pay_adjustment'], 'id="dtu_pay_adjustment"') ?><?= tep_draw_hidden_field('adjustment_type', $input_array['adjustment_type'], 'id="adjustment_type"') ?></td>
                                    </tr>
                                <? } ?>
                                <? if ($total_payable_view > 0) { ?>
                                    <tr class="invoiceListingOdd">
                                        <td width="90%" align="right" class="boldText"><?= TABLE_HEADING_BOTTOM_TOTAL_PAYABLE_AMOUNT ?>:</td>
                                        <td width="2%" align="right" class="boldText"><?= $input_array['dtu_currency'] ?></td>
                                        <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('dtu_payable_total', $input_array['dtu_payable_total'], 'id="dtu_payable_total"') ?><?= number_format($input_array['dtu_payable_total'], 4, $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousand_point']) ?></td>
                                        <td width="2%" align="center" class="boldText">&nbsp;</td>
                                    </tr>
                                <? } ?>    
                                <tr>
                                    <td width="90%" align="right" class="smallText"><?= TABLE_HEADING_BOTTOM_AFTER_CONVERSION_RATE ?>:</td>
                                    <td width="2%" align="right" class="smallText"><?= tep_draw_hidden_field('dtu_payment_method_currency', $input_array['dtu_payment_method_currency'], 'id="dtu_payment_method_currency"') ?><?= $input_array['dtu_payment_method_currency'] ?></td>
                                    <td width="6%" align="right" class="smallText"><?= number_format(($input_array['dtu_tax_total'] * $input_array['confirm_rate']), 4, $currencies->currencies[$input_array['dtu_payment_method_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_payment_method_currency']]['thousand_point']) ?></td>
                                    <td width="2%" align="center" class="smallText">&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td align="left"><?= tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtn"', 'inputButton') ?></td>
                                    <td align="right"><?= tep_submit_button(BUTTON_GENERATE_DTU, ALT_BUTTON_GENERATE_DTU, '', 'inputButton'); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        </form>
        <?
    }
    
    function preview_dtu_cb_form($input_array) {
        
        global $currencies, $po_suppliers;
        
        $dtu_publisher = new publishers($input_array['dtu_supplier']);
        $dtu_supplier_info = $dtu_publisher->get_publishers();
        $dtu_publisher->set_dtu_temp_status_empty($input_array['dtu_supplier'], 'true');
        $dtu_supplier_id = $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_supplier_id'];

        $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));
        $countries_info = tep_get_countries_info($supplier_info['country_id'], 'countries_id');
        $address_str = tep_address_format($countries_info['address_format_id'], $supplier_info, 1, '', '<br>', 'main');
        $delivery_str = $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['company'];
        $gst_value = $this->po_company_gst[$input_array['dtu_delivery_address']];

        // Get CDKey Supplier's payment term info
        switch ($supplier_info['payment_type']) {
            case 'g':
                $dtu_payment = TEXT_SUPPLIER_CONSIGNMENT;
                break;
            case 'c':
                $dtu_payment = TEXT_SUPPLIER_PRE_PAYMENT;
                break;
            case 'd':
                $dtu_payment = TEXT_SUPPLIER_DTU_PAYMENT;
                break;
            case 't':
                $dtu_payment = $supplier_info['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                break;
        }

        $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $dtu_supplier_id, '3');

        $po_bank_charge_amt = 0;
        if (isset($input_array['dtu_pay_bankcharge'])) {
            $po_bank_charge_amt = round(($input_array['dtu_pay_bankcharge'] * (1 / $input_array['confirm_rate'])), $currencies->currencies[$input_array['dtu_currency']]['decimal_places']);
        }
        ?>
        <?= tep_draw_form('preview_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=insert_dtu_product&selected_box=po', 'post', ''); ?>
        <?= tep_draw_hidden_field('dtu_list_type', $input_array['dtu_list_type'], 'id="dtu_list_type"') ?>
        <table width="100%" cellspacing="2" cellpadding="2" border="0">
            <tbody>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_START_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['start_date'] ?><?= tep_draw_hidden_field('start_date', $input_array['start_date'], 'id="start_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CONTACT_PERSON ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['dtu_contact_person'] ?><?= tep_draw_hidden_field('dtu_contact_person', $input_array['dtu_contact_person'], 'id="dtu_contact_person"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUGGESTED_SUPPLIER ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_name'] ?><?= tep_draw_hidden_field('dtu_supplier', $input_array['dtu_supplier'], 'id="dtu_supplier"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $address_str ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DELIVERY_ADDRESS ?>:</b></td>
                                                    <td class="main" valign="top"><?= $delivery_str ?><?= tep_draw_hidden_field('dtu_delivery_address', $input_array['dtu_delivery_address'], 'id="dtu_delivery_address"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="50%" valign="top">
                                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                            <tbody>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_END_DATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['end_date'] ?><?= tep_draw_hidden_field('end_date', $input_array['end_date'], 'id="end_date"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="30%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER_PAYMENT_TERM ?>:</b></td>
                                                    <td class="main" valign="top"><?= $dtu_payment ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_DISBURSEMENT_METHOD ?>:</b></td>
                                                    <td class="main" valign="top"><?= $pm_obj->payment_accounts[$input_array['dtu_supplier_payment']]['pm_name'] ?><?= tep_draw_hidden_field('dtu_supplier_payment', $input_array['dtu_supplier_payment'], 'id="dtu_supplier_payment"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY ?>:</b></td>
                                                    <td class="main" valign="top"><?= $currencies->currencies[$input_array['dtu_currency']]['title'] . ' (' . $currencies->currencies[$input_array['dtu_currency']]['symbol_left'] . $currencies->currencies[$input_array['dtu_currency']]['symbol_right'] . ')' ?><?= tep_draw_hidden_field('dtu_currency', $input_array['dtu_currency'], 'id="dtu_currency"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_SUGGEST_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['suggest_rate'] ?><?= tep_draw_hidden_field('suggest_rate', $input_array['suggest_rate'], 'id="suggest_rate"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= ENTRY_DTU_FORM_CURRENCY_CONFIRM_RATE ?>:</b></td>
                                                    <td class="main" valign="top"><?= $input_array['confirm_rate'] ?><?= tep_draw_hidden_field('confirm_rate', $input_array['confirm_rate'], 'id="confirm_rate"') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
                </tr>
                <tr>
                    <td>
                        <table id="new_dtu_products" border="0" width="100%" cellspacing="1" cellpadding="4">
                            <tbody>
                                <tr>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_DATE ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_PRODUCT_ID ?></td>
                                    <td class="invoiceBoxHeading" width="18%"><?= TABLE_HEADING_DTU_PRODUCT_NAME ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_ORDER_NO ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="8%"><?= TABLE_HEADING_DTU_TOP_UP_ID ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="6%"><?= TABLE_HEADING_DTU_QUANTITY ?></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="sell_price_title"><?=sprintf(TABLE_HEADING_DTU_SRP,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title"><?=sprintf(TABLE_HEADING_DTU_AMOUNT,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>
                                    <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_DTU_BILLING_STATUS ?></td>
                                </tr>
                                <?
                                if (is_array($input_array['dtu_items_top_up_id'])) {
                                    $comma_list = implode(', ', $input_array['dtu_items_top_up_id']);
                                    $dtu_item_array = $dtu_publisher->get_dtu_items($comma_list);
                                    foreach ($input_array['dtu_items_top_up_id'] as $dtu_topup_id) {
                                        $dtu_publisher->set_dtu_temp_status($dtu_topup_id, $input_array['dtu_status_select_'.$dtu_topup_id]);
                                        $dtu_item_prod_id = $dtu_item_array[$dtu_topup_id]['products_id'];

                                        if ($dtu_item_array[$dtu_topup_id]["products_type"] == 3) {
                                            // virtual listing
                                            $selling_price = $this->getVirtualProductSellPrice($dtu_item_array[$dtu_topup_id]['currency_code'], $dtu_item_array[$dtu_topup_id]['currency_settle_amount'], $currencies, $input_array['dtu_currency']);
                                        } else {
                                            $selling_price = $this->getProductSellPrice($dtu_item_prod_id, $currencies, $input_array['dtu_currency']);
                                        }
                                        if ($input_array['dtu_status_select_'.$dtu_topup_id] == '1') {
                                            $dtu_payment_status = 'Charge Back';
                                        } else if ($input_array['dtu_status_select_'.$dtu_topup_id] == '2') {
                                            $dtu_payment_status = 'Debit Note';
                                        } else if ($input_array['dtu_status_select_'.$dtu_topup_id] == '3') {
                                            $dtu_payment_status = 'DTU Issue';
                                        } else if ($input_array['dtu_status_select_'.$dtu_topup_id] == '11') {
                                            $dtu_payment_status = 'Paid';
                                        } else if ($input_array['dtu_status_select_'.$dtu_topup_id] == '12') {
                                            $dtu_payment_status = 'Un-Paid';
                                        } else {
                                            $dtu_withdrawal_status = $dtu_publisher->get_dtu_withdrawal_status($dtu_topup_id);
                                            if ($dtu_withdrawal_status == '0' || $dtu_withdrawal_status == '7') {
                                                $dtu_payment_status = 'Un-Paid';
                                            } else {
                                                $dtu_payment_status = 'Paid';
                                            }
                                        }
                                        $total_quantity = number_format($dtu_item_array[$dtu_topup_id]['products_delivered_quantity'],0);
                                        $subtotal_price = $total_quantity * round($selling_price, 4);
                                        ?>
                                        <tr>
                                            <td align="center" valign="top" class="invoiceRecordsRightBorder">
                                            <?= tep_draw_hidden_field('dtu_items_top_up_id[]', $dtu_topup_id, 'class="dtu_items_top_up_id" id="dtu_items_top_up_id"') ?><?= $dtu_item_array[$dtu_topup_id]['topup_time'] ?></td>
                                            <td align="center" valign="top" class="invoiceRecordsRightBorder"><?= $dtu_item_prod_id ?></td>
                                            <td valign="top" class="invoiceRecordsRightBorder"><?= $dtu_item_array[$dtu_topup_id]['products_name']; ?></td>
                                            <td align="center" valign="top" class="invoiceRecordsRightBorder"><?= $dtu_item_array[$dtu_topup_id]['orders_id']; ?></td>
                                            <td align="center" valign="top" class="invoiceRecordsRightBorder"><?= $dtu_topup_id ?></td>
                                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $total_quantity ?></td>
                                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= number_format($selling_price, '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?></td>
                                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= number_format($subtotal_price, '4', $currencies->currencies[$input_array['dtu_currency']]['decimal_point'], $currencies->currencies[$input_array['dtu_currency']]['thousands_point']) ?></td>
                                            <td align="center" valign="top" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('dtu_status_select_' . $dtu_topup_id, $input_array['dtu_status_select_' . $dtu_topup_id], 'id="dtu_status_select_' . $dtu_topup_id . '"') ?><?= $dtu_payment_status ?></td>
                                        </tr>
                                        <?
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                </tr>
                <tr>
                    <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellspacing="0" cellpadding="2" border="0">
                            <tbody>
                                <tr>
                                    <td align="left">
                                        <?= tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtnCB"', 'inputButton') ?>
                                    </td>
                                    <td align="right">
                                        <?= tep_submit_button(BUTTON_SET_CB_DTU, ALT_BUTTON_SET_CB_DTU, 'name="submitDTUCB"', 'inputButton') ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        </form>
        <?
    }
    
    function printable_dtu_form() {
        global $currencies, $po_suppliers;

        $total_row = 36;
        $tophalf_row = $bottomhalf_row = 16;
        $topblankrow = $bottomblankrow = 1;
        $after_remark_row = 2;

        $supplier_info = $po_suppliers->get_po_supplier_info($this->supplier['supplier_id']);
        $supplier_name = $supplier_info['customers_firstname'] . ' ' . $supplier_info['customers_lastname'];
        $countries_info = tep_get_countries_info($supplier_info['country_id'], 'countries_id');
        $address_str = tep_address_format($countries_info['address_format_id'], $supplier_info, 1, '', '<br>', 'main');
        $addr_arr = explode('<br>', $address_str);

        // Get CDKey Supplier's payment term info
        switch ($this->payment['payment_type']) {
            case 'g':
                $po_payment = TEXT_SUPPLIER_CONSIGNMENT;
                break;
            case 'c':
                $po_payment = TEXT_SUPPLIER_PRE_PAYMENT;
                break;
            case 'd':
                $po_payment = TEXT_SUPPLIER_DTU_PAYMENT;
                break;
            case 't':
                $po_payment = $this->payment['payment_term'] . ' ' . TEXT_SUPPLIER_DAY_TERM;
                break;
        }

        $pm_obj = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $this->supplier['supplier_id'], '3');
        $pm_acct_fields = $pm_obj->get_payment_account_book_detail($this->payment['store_payment_account_book_id']);
        $pma_details = array();
        if (isset($pm_acct_fields[$this->payment['store_payment_account_book_id']]['field'])) {
            $pma_details[] = '<b>Payment send via ' . $pm_acct_fields[$this->payment['store_payment_account_book_id']]['pm_name'] . ' to</b>';
            foreach ($pm_acct_fields[$this->payment['store_payment_account_book_id']]['field'] as $fieldnum => $pma_fields) {
                if (tep_not_null($pma_fields['payment_methods_fields_value'])) {
                    $pma_details[] = $pma_fields['payment_methods_fields_title'] . ': ' . $pma_fields['payment_methods_fields_value'];
                }
            }
        }

        $notify_supplier_remark = $this->get_notify_supplier_remark_array();

        $has_gst = false;
        $total_row_cnt = 1;
        if ($this->payment['gst_value'] > 0) {
            $has_gst = true;
            $total_row_cnt = 3;
        }

        $items_row_cnt = count($this->po_items);
        $remark_row_cnt = count($notify_supplier_remark);
        $pm_row_cnt = count($pma_details);
        if ($items_row_cnt >= $total_row) {
            $tophalf_row = $items_row_cnt;
            $bottomhalf_row = $tophalf_empty_row = $bottomhalf_empty_row = 0;
        } else {
            $tophalf_empty_row = $tophalf_row - $items_row_cnt;
            $bottomhalf_empty_row = $bottomhalf_row - ($remark_row_cnt + $after_remark_row + $pm_row_cnt + $total_row_cnt);
        }
        ?>
        <table align="center" cellspacing="0" cellpadding="0" width="800" border="0">
            <tbody>
                <tr>
                    <td colspan="12" align="left">
                        <table align="left" cellspacing="0" cellpadding="0" width="100%" border="0">
                            <tr>
                                <td align="left"><b><h1>PURCHASE ORDER</h1></b></td>
                                <td align="right"><?= tep_image(DIR_WS_IMAGES . "logo.png", '', '', '', '') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="4%">&nbsp;</td>
                    <td width="10%">&nbsp;</td>
                    <td width="19%">&nbsp;</td>
                    <td width="1%">&nbsp;</td>
                    <td width="1%">&nbsp;</td>
                    <td width="15%">&nbsp;</td>
                    <td width="13%">&nbsp;</td>
                    <td width="1%">&nbsp;</td>
                    <td width="16%">&nbsp;</td>
                    <td width="1%">&nbsp;</td>
                    <td width="9%">&nbsp;</td>
                    <td width="10%">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" align="left" valign="top">To :</td>
                    <td style="border-top: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left" valign="top"><?= (isset($addr_arr[0]) ? $addr_arr[0] : '&nbsp;') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">Ship To :   <?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['company'] ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">Invoice No.</td>
                    <td align="center">:</td>
                    <td colspan="2" align="left"><?= $this->po_info['po_ref_id'] ?></td>
                </tr>
                <tr>
                    <td style="border-left: 1px solid #000000" align="left">&nbsp;</td>
                    <td style="border-right: 1px solid #000000" colspan="2" align="left" valign="top"><?= (isset($addr_arr[1]) ? $addr_arr[1] : '&nbsp;') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['street_address'] ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">Date</td>
                    <td align="center">:</td>
                    <td colspan="2" align="left"><?= date('d M Y', strtotime($this->po_info['po_date'])) ?></td>
                </tr>
                <tr>
                    <td style="border-left: 1px solid #000000" align="left">&nbsp;</td>
                    <td style="border-right: 1px solid #000000" colspan="2" align="left" valign="top"><?= (isset($addr_arr[2]) ? $addr_arr[2] : '&nbsp;') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['suburb'] . ', ' . ($this->po_delivery_address[$this->billing['delivery_location']]['TOP']['format_id'] == '1' ? $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['postcode'] : '') . ' ' . $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['city'] ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td style="border-left: 1px solid #000000" align="left">&nbsp;</td>
                    <td style="border-right: 1px solid #000000" colspan="2" align="left" valign="top"><?= (isset($addr_arr[3]) ? $addr_arr[3] : '&nbsp;') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['country'] . ' ' . ($this->po_delivery_address[$this->billing['delivery_location']]['TOP']['format_id'] == '4' ? $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['postcode'] : '') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">Terms</td>
                    <td align="center">:</td>
                    <td colspan="2" align="left"><?= $po_payment ?></td>
                </tr>
                <tr>
                    <td style="border-left: 1px solid #000000" align="left">&nbsp;</td>
                    <td style="border-right: 1px solid #000000" colspan="2" align="left" valign="top"><?= (isset($addr_arr[4]) ? $addr_arr[4] : '&nbsp;') ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tel : <?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['telephone'] ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">Contact Person</td>
                    <td align="center">:</td>
                    <td colspan="2" align="left"><?= $this->po_info['po_contact_info'] ?></td>
                </tr>
                <tr>
                    <td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left">&nbsp;</td>
                    <td style="border-bottom: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left" valign="top">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fax : <?= $this->po_delivery_address[$this->billing['delivery_location']]['TOP']['fax'] ?></td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="center">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                    <td align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12">&nbsp;</td>
                </tr>
                <!-- Print Column Title Row //-->
                <tr>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" height="17" align="center"><?= TABLE_HEADING_DTU_PRINT_NO ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center"><?= TABLE_HEADING_DTU_PRINT_PRODUCT_ID ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="4" align="center"><?= TABLE_HEADING_DTU_PRINT_PARTICULARS ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="2" align="center"><?= TABLE_HEADING_DTU_PRINT_QUANTITY ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="2" align="center"><?= TABLE_HEADING_DTU_PRINT_UNIT_PRICE ?><br><?= '(' . $currencies->currencies[$this->po_info['currency']]['symbol_left'] . $currencies->currencies[$this->po_info['currency']]['symbol_right'] . ')' ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center"><?= TABLE_HEADING_DTU_PRINT_DISCOUNT ?></td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center"><?= TABLE_HEADING_DTU_PRINT_TOTAL ?><br><?= '(' . $currencies->currencies[$this->po_info['currency']]['symbol_left'] . $currencies->currencies[$this->po_info['currency']]['symbol_right'] . ')' ?></td>
                </tr>
                <!-- Print Top Blank Row //-->
                <?
                for ($t = 0; $t < $topblankrow; $t++) {
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center">&nbsp;</td>
                    </tr>
                <? } ?>
                <!-- Print Product Items Row //-->
                <?
                for ($p = 0; $p < $items_row_cnt; $p++) {
                    $item_data = $this->po_items[$p];
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center" valign="top"><?= $p + 1 ?></td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top"><?= $item_data['products_id'] ?></td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left" valign="top"><?= $item_data['products_name'] ?></td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top"><?= $item_data['suggest_qty'] ?></td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top"><?= ($item_data['products_unit_price_type'] == '$' ? $this->monetary_decimal_format($item_data['products_unit_price']) : number_format($item_data['products_sell_price'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point'])) ?></td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top"><?= ($item_data['products_unit_price_type'] == '%' ? number_format($item_data['products_unit_price_value'], 2, '.', '') . '%' : '&nbsp;') ?></td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign="top"><?= number_format($item_data['subtotal'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                    </tr>
                <? } ?>
                <!-- Print Top Half Blank Row before Print Remark //-->
                <? for ($e1_i = 0; $e1_i < $tophalf_empty_row; $e1_i++) { ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center">&nbsp;</td>
                    </tr>
                <? } ?>
                <!-- Print Remark Row //-->
                <?
                if ($remark_row_cnt > 0) {
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left" valign="top"><u><b>Notes:</b></u></td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                    </tr>
                    <?
                    for ($r = 0; $r < $remark_row_cnt; $r++) {
                        $remark_data = nl2br($notify_supplier_remark[$r]);
                        ?>
                        <tr>
                            <td style="border-left: 1px solid #000000" height="17" align="center" valign="top">&nbsp;</td>
                            <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                            <td style="border-left: 1px solid #000000" colspan="4" align="left" valign="top"><?= $remark_data ?></td>
                            <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                            <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                            <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                            <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        </tr>
                        <?
                    }
                }
                for ($ar = 0; $ar < $after_remark_row; $ar++) {
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                    </tr>
                    <?
                }
                for ($pma = 0; $pma < $pm_row_cnt; $pma++) {
                    $pma_data = nl2br($pma_details[$pma]);
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left" valign="top"><?= $pma_data ?></td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign="top">&nbsp;</td>
                    </tr>
                    <?
                }
                ?>
                <!-- Print Bottom Half Blank Row after Print Remark //-->
                <? for ($e2_i = 0; $e2_i < $bottomhalf_empty_row; $e2_i++) { ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center">&nbsp;</td>
                    </tr>
                <? } ?>
                <!-- Print Bottom Blank Row //-->
                <?
                for ($b = 0; $b < $bottomblankrow; $b++) {
                    ?>
                    <tr>
                        <td style="border-left: 1px solid #000000" height="17" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" colspan="2" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000" align="center">&nbsp;</td>
                        <td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center">&nbsp;</td>
                    </tr>
                <? } ?>
                    <tr>
                        <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="11" height="17" align="right">SUB-TOTAL&nbsp;<?= $this->po_info['currency'] ?>:</td>
                        <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center"><?= number_format($this->po_total['po_subtotal']['value'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                    </tr>
                <tr>
                    <td colspan="12" height="17" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" height="17" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td height="19" colspan="2">Prepared by</td>
                    <td colspan="8">&nbsp;</td>
                    <td colspan="2">Checked and Approved by</td>
                </tr>
                <tr>
                    <td colspan="12" height="19" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" height="17" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td height="17" colspan="2">____________________</td>
                    <td colspan="8">&nbsp;</td>
                    <td colspan="2">____________________</td>
                </tr>
                <tr>
                    <td height="19" colspan="2">Date :</td>
                    <td colspan="8">&nbsp;</td>
                    <td colspan="2">Date :</td>
                </tr>
                <tr>
                    <td colspan="12" height="19" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" height="19" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="12" height="17" align="justify">For Internal Use:</td>
                </tr>
                <tr>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="3" height="17" align="center">Faxed/Emailed Recipient</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="4" align="center">Payee Details &amp; Computation Checked</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="3" align="center">Payment Made</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="center">Recorded </td>
                </tr>
                <tr>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="3" height="17" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000" colspan="3" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="3" height="51" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="4" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan="3" align="left">&nbsp;</td>
                    <td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan="2" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" height="17" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="12" align="center"><b><font size="1" color="#0000FF"><a href="http://www.offgamers.com/"><?= $this->po_delivery_address[$this->billing['delivery_location']]['FOOTER']['text'] ?></a></font></b></td>
                </tr>
            </tbody>
        </table>
        <?
    }

    function load_po($input_array, &$messageStack) {
        global $languages_id, $currencies;

        if ((isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) ||
                (isset($input_array['po_ref']) && tep_not_null($input_array['po_ref']))) {
            $this->unset_po();

            if (isset($input_array['po_id'])) {
                $po_filter_id = $input_array['po_id'];
                $po_filter_by = "purchase_orders_id='" . tep_db_input($input_array['po_id']) . "'";
            } else if (isset($input_array['po_ref'])) {
                $po_filter_id = $input_array['po_ref'];
                $po_filter_by = "purchase_orders_ref_id='" . tep_db_input(urldecode($input_array['po_ref'])) . "'";
            }

            $po_select_sql = "SELECT * FROM " . TABLE_PURCHASE_ORDERS . " WHERE " . $po_filter_by;
            $po_result_sql = tep_db_query($po_select_sql);
            if ($po_row = tep_db_fetch_array($po_result_sql)) {
                $this->po_id = $po_row['purchase_orders_id'];
                $this->po_zero_total_amt = false;
                $this->po_payment_made = false;

                $this->po_info = array(
                    'po_id' => $po_row['purchase_orders_id'],
                    'po_ref_id' => $po_row['purchase_orders_ref_id'],
                    'po_date' => $po_row['purchase_orders_issue_date'],
                    'po_contact_info' => $po_row['purchase_orders_contact_info'],
                    'po_finish_date' => $po_row['purchase_orders_date_finished'],
                    'currency' => $po_row['currency'],
                    'suggested_currency_value' => $po_row['suggested_currency_value'],
                    'confirmed_currency_value' => $po_row['confirmed_currency_value'],
                    'currency_usd_value' => $po_row['currency_usd_value'],
                    'po_status' => $po_row['purchase_orders_status'],
                    'po_status_name' => $this->po_status_arr[$po_row['purchase_orders_status']],
                    'po_paid_status' => $po_row['purchase_orders_paid_status'],
                    'po_billing_status' => $po_row['purchase_orders_billing_status'],
                    'purchase_orders_tag_ids' => $po_row['purchase_orders_tag_ids'],
                    'purchase_orders_locked_by' => $po_row['purchase_orders_locked_by'],
                    'purchase_orders_verify_mode' => $po_row['purchase_orders_verify_mode']
                );

                $this->supplier = array(
                    'supplier_id' => $po_row['supplier_id'],
                    'supplier_name' => $po_row['supplier_name'],
                    'supplier_company' => $po_row['supplier_company'],
                    'supplier_street_address' => $po_row['supplier_street_address'],
                    'supplier_suburb' => $po_row['supplier_suburb'],
                    'supplier_city' => $po_row['supplier_city'],
                    'supplier_postcode' => $po_row['supplier_postcode'],
                    'supplier_state' => $po_row['supplier_state'],
                    'supplier_country' => $po_row['supplier_country'],
                    'supplier_telephone_country' => $po_row['supplier_telephone_country'],
                    'supplier_country_international_dialing_code' => $po_row['supplier_country_international_dialing_code'],
                    'supplier_telephone' => $po_row['supplier_telephone'],
                    'supplier_email_address' => $po_row['supplier_email_address'],
                    'supplier_address_format_id' => $po_row['supplier_address_format_id']
                );

                $this->billing = array(
                    'delivery_location' => $po_row['delivery_location'],
                    'delivery_name' => $po_row['delivery_name'],
                    'delivery_company' => $po_row['delivery_company'],
                    'delivery_street_address' => $po_row['delivery_street_address'],
                    'delivery_suburb' => $po_row['delivery_suburb'],
                    'delivery_city' => $po_row['delivery_city'],
                    'delivery_postcode' => $po_row['delivery_postcode'],
                    'delivery_state' => $po_row['delivery_state'],
                    'delivery_country' => $po_row['delivery_country'],
                    'delivery_address_format_id' => $po_row['delivery_address_format_id']
                );

                $this->payment = array(
                    'payment_type' => $po_row['payment_type'],
                    'payment_term' => $po_row['payment_term'],
                    'payment_days_pay_wsc' => $po_row['payment_days_pay_wsc'],
                    'store_payment_account_book_id' => $po_row['store_payment_account_book_id'],
                    'bankcharges_included' => $po_row['purchase_orders_bankcharges_included'],
                    'bankcharges_refunded' => $po_row['purchase_orders_bankcharges_refunded'],
                    'gst_currency' => $po_row['purchase_orders_gst_currency'],
                    'gst_value' => $po_row['purchase_orders_gst_value'],
                    'gst_amount' => $po_row['purchase_orders_gst_amount'],
                    'gst_refunded' => $po_row['purchase_orders_gst_refunded']
                );

                $po_total_select_sql = "SELECT * FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . $this->po_id . "' ORDER BY sort_order";
                $po_total_result_sql = tep_db_query($po_total_select_sql);
                while ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                    $this->po_total[$po_total_row['class']] = array(
                        'purchase_orders_total_id' => $po_total_row['purchase_orders_total_id'],
                        'title' => $po_total_row['title'],
                        'usd_value' => $po_total_row['usd_value'],
                        'value' => $po_total_row['value'],
                        'currency' => $po_total_row['currency']
                    );
                }

                $total_purchase_amount = $total_delivered_amount = $total_canceled_amount = $total_refunded_amount = $total_creditnote_amount = $total_balance_amount = 0;

                $po_products_select_sql = "SELECT * FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " WHERE purchase_orders_id='" . $this->po_id . "' ORDER BY purchase_orders_products_id";
                $po_products_result_sql = tep_db_query($po_products_select_sql);
                while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
                    $po_products_qty = $po_products_actual_qty = 0;
                    // retrieve product name and quantities
                    $publishers_products_select_sql = "	SELECT p.products_quantity, p.products_actual_quantity, pd.products_name 
														FROM " . TABLE_PRODUCTS . " AS p 
														LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON (p.products_id = pd.products_id) 
														WHERE p.products_id = '" . (int) $po_products_row['products_id'] . "' 
															AND pd.language_id = " . (int) $languages_id . " 
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    if ($publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
                        $po_products_qty = $publishers_products_row['products_quantity'];
                        $po_products_actual_qty = $publishers_products_row['products_actual_quantity'];
                    }

                    $total_purchase_amount += ($po_products_row['products_unit_price'] * $po_products_row['products_quantity']);
                    $total_delivered_amount += ($po_products_row['products_unit_price'] * $po_products_row['products_good_delivered_quantity']);
                    $total_canceled_amount += ($po_products_row['products_unit_price'] * $po_products_row['products_canceled_quantity']);
                    $total_refunded_amount += ($po_products_row['products_unit_price'] * $po_products_row['products_refund_quantity']);
                    $total_creditnote_amount += ($po_products_row['products_unit_price'] * $po_products_row['products_credit_note_quantity']);

                    $this->po_items[] = array(
                        'id' => $po_products_row['purchase_orders_products_id'],
                        'products_id' => $po_products_row['products_id'],
                        'products_name' => $po_products_row['products_name'],
                        'products_model' => $po_products_row['products_model'],
                        'products_quantity' => $po_products_qty,
                        'products_actual_quantity' => $po_products_actual_qty,
                        'multiplier' => '',
                        'last_n_days' => '',
                        'stock_price_type' => '$',
                        'products_tax' => $po_products_row['products_tax'],
                        'suggest_qty' => $po_products_row['products_quantity'],
                        'products_unit_price_type' => $po_products_row['products_unit_price_type'],
                        'products_unit_price_value' => $po_products_row['products_unit_price_value'],
                        'products_sell_price' => $po_products_row['products_selling_price'],
                        'products_unit_price' => $po_products_row['products_unit_price'],
                        'subtotal' => $po_products_row['products_subtotal'],
                        'products_usd_unit_price' => $po_products_row['products_usd_unit_price'],
                        'products_usd_selling_price' => $po_products_row['products_usd_selling_price'],
                        'products_usd_subtotal' => $po_products_row['products_usd_subtotal'],
                        'products_delivered_quantity' => $po_products_row['products_delivered_quantity'],
                        'products_good_delivered_quantity' => $po_products_row['products_good_delivered_quantity'],
                        'products_good_delivered_price' => $po_products_row['products_good_delivered_price'],
                        'products_good_delivered_usd_price' => $po_products_row['products_good_delivered_usd_price'],
                        'products_canceled_quantity' => $po_products_row['products_canceled_quantity'],
                        'products_canceled_price' => $po_products_row['products_canceled_price'],
                        'products_canceled_usd_price' => $po_products_row['products_canceled_usd_price'],
                        'products_refund_quantity' => $po_products_row['products_refund_quantity'],
                        'products_refund_price' => $po_products_row['products_refund_price'],
                        'products_refund_usd_price' => $po_products_row['products_refund_usd_price'],
                        'products_credit_note_quantity' => $po_products_row['products_credit_note_quantity'],
                        'products_credit_note_price' => $po_products_row['products_credit_note_price'],
                        'products_credit_note_usd_price' => $po_products_row['products_credit_note_usd_price'],
                        'custom_products_type_id' => $po_products_row['custom_products_type_id'],
                        'products_categories_id' => $po_products_row['products_categories_id']
                    );
                }

                $total_balance_amount = $total_purchase_amount - ($total_delivered_amount + $total_canceled_amount + $total_refunded_amount + $total_creditnote_amount);

                $this->po_summary = array(
                    'total_purchased_amount' => $total_purchase_amount,
                    'total_delivered_amount' => $total_delivered_amount,
                    'total_canceled_amount' => $total_canceled_amount,
                    'total_refunded_amount' => $total_refunded_amount,
                    'total_creditnote_amount' => $total_creditnote_amount,
                    'total_remaining_amount' => $total_balance_amount
                );

                return true;
            } else {
                $messageStack->add_session(sprintf(ERROR_PO_SEARCH_INVALID_ID, $po_filter_id), 'error');
                return false;
            }
        } else {
            $messageStack->add_session(ERROR_PO_LOAD_EMPTY_ID, 'error');
            return false;
        }
    }

    function show_edit_dtu_form($input_array) {
        global $currencies, $languages_id, $allow_verify_dtu_permission;
        
        $currencies->set_decimal_places('-1');
        $tag_selection_general = array(array('id' => '', 'text' => 'DTU Payment Request Lists Options ...'),
            array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
        );

        if (tep_not_null($this->po_id)) {
            $status = preg_replace("/\s/", '_', $this->po_info['po_status']);
            echo tep_draw_hidden_field($status . '_order_str', '', ' id="' . $status . '_order_str" ');

            $mirror_for_delete_tag = array();
            $status_dependent_tag_array = $tag_selection_general;
            $po_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('" . $this->po_info['po_status'] . "', orders_tag_status_ids) AND filename='" . FILENAME_DTU_PAYMENT . "';";
            $po_tag_result_sql = tep_db_query($po_tag_select_sql);
            while ($po_tag_row = tep_db_fetch_array($po_tag_result_sql)) {
                $status_dependent_tag_array[] = array('id' => 'otag_' . $po_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $po_tag_row["orders_tag_name"]);
                $mirror_for_delete_tag[] = array('id' => 'rmtag_' . $po_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;' . $po_tag_row["orders_tag_name"]);
            }
            $status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
            $status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
            $status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);

            $this->po_zero_total_amt = false;
            $zero_po_amt_select_sql = "SELECT value 
                                        FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " 
                                        WHERE purchase_orders_id = '" . $this->po_id . "'
                                            AND class = 'po_total'
                                            AND value = 0";
            $zero_po_amt_result_sql = tep_db_query($zero_po_amt_select_sql);
            if (tep_db_num_rows($zero_po_amt_result_sql) > 0) {
                $this->po_zero_total_amt = true;
            }

            $this->po_payment_made = false;
            if ($this->po_zero_total_amt == false) {
                $po_payment_array = array();
                $po_payment_select_sql = "SELECT spti.store_payments_id, sp.store_payments_status, sps.store_payments_status_name 
                                            FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti
                                            INNER JOIN " . TABLE_STORE_PAYMENTS . " AS sp
                                                ON (spti.store_payments_id = sp.store_payments_id)
                                            INNER JOIN " . TABLE_STORE_PAYMENTS_STATUS . " AS sps
                                                ON (sps.store_payments_status_id = sp.store_payments_status 
                                                    AND sps.language_id = '" . $languages_id . "')
                                            WHERE spti.store_payments_reimburse_id = '" . $this->po_id . "'
                                                AND spti.store_payments_reimburse_table = '" . TABLE_PURCHASE_ORDERS . "'";
                $po_payment_result_sql = tep_db_query($po_payment_select_sql);
                while ($po_payment_row = tep_db_fetch_array($po_payment_result_sql)) {
                    if ($po_payment_row['store_payments_status'] != '4')
                        $this->po_payment_made = true;
                    $po_payment_array[] = $po_payment_row;
                }
            }

            // Payment methods
            $store_payment_account_book_sql = " SELECT payment_methods_alias 
                                                FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " 
                                                WHERE store_payment_account_book_id = '" . $this->payment['store_payment_account_book_id'] . "'";
            $store_payment_account_book_result = tep_db_query($store_payment_account_book_sql);
            $store_payment_account_book = tep_db_fetch_array($store_payment_account_book_result);

            $po_used_credit_note_amt = 0;
            $has_credit_note_select_sql = "SELECT value, currency 
                                            FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " 
                                            WHERE purchase_orders_id = '" . $this->po_id . "'
                                                AND class = 'po_credit_note'";
            $has_credit_note_result_sql = tep_db_query($has_credit_note_select_sql);
            if (tep_db_num_rows($has_credit_note_result_sql) > 0) {
                $has_credit_note_row = tep_db_fetch_array($has_credit_note_result_sql);
                $po_used_credit_note_amt = $currencies->format($has_credit_note_row['value'], false, $has_credit_note_row['currency']);
            }
            ?>
            <table width="100%" cellspacing="2" cellpadding="2" border="0">
                <tbody>
                    <tr>
                        <td align="right" class="smallText">
                            <?= tep_draw_form('dtu_id_search', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction', 'po_id')) . '&selected_box=po', 'post', ''); ?>
                            <?= ENTRY_SEARCH_DTU_ID ?>:&nbsp;&nbsp;<?= tep_draw_input_field('po_id', '', 'size="12" id="po_id"') ?><?= tep_draw_hidden_field('subaction', 'search_dtu', 'id="subaction"') ?>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                <tbody>
                                    <tr>
                                        <td valign="top">
                                            <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td width="15%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_DTU_REF_ID ?>:</b></td>
                                                        <td class="main" valign="top"><?= $this->po_info['po_ref_id'] . ($this->po_info['po_status'] == '3' ? ($this->po_info['purchase_orders_verify_mode'] ? TEXT_VERIFIED : TEXT_UNVERIFIED) : '') ?><?= tep_draw_hidden_field('po_id', $this->po_info['po_id'], 'id="po_id"') ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td width="15%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_ISSUE_DATE ?>:</b></td>
                                                        <td class="main" valign="top"><?= $this->po_info['po_date'] ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td width="15%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_DTU_STATUS ?>:</b></td>
                                                        <td class="main" valign="top">
                                                            <table border="0" cellspacing="2" cellpadding="0">
                                                                <tr>
                                                                    <td class="main" valign="top">
                                                                        <?= $this->po_status_arr[$this->po_info['po_status']] ?>
                                                                    </td>
                                                                    <td align="right" class="main">
                                                                        <?= '&nbsp;&nbsp;' . tep_draw_pull_down_menu($this->po_status_arr[$this->po_info['po_status']] . "_tag_selector", $status_dependent_tag_array, '', ' id="' . $this->po_status_arr[$this->po_info['po_status']] . '_tag_selector" onChange="orderListsOptions(this, \'' . $this->po_info['po_status'] . '\', \'' . (int) $languages_id . '\', \'' . $this->po_info['po_id'] . '\');"') ?>
                                                                    </td>
                                                                    <td class="smallText" valign="top" nowrap>
                                                                        <?
                                                                        $tags_str = '';
                                                                        if (tep_not_null($this->po_info['purchase_orders_tag_ids'])) {
                                                                            $po_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '" . $this->po_info['purchase_orders_tag_ids'] . "') AND filename='" . FILENAME_DTU_PAYMENT . "';";
                                                                            $po_assigned_tag_result_sql = tep_db_query($po_assigned_tag_select_sql);
                                                                            while ($po_assigned_tag_row = tep_db_fetch_array($po_assigned_tag_result_sql)) {
                                                                                $tags_str .= $po_assigned_tag_row["orders_tag_name"] . ', ';
                                                                            }
                                                                            if (substr($tags_str, -2) == ', ')
                                                                                $tags_str = substr($tags_str, 0, -2);
                                                                        }
                                                                        echo '<span class="greenIndicator" id="tag_' . $this->po_info['po_id'] . '">&nbsp;&nbsp;' . $tags_str . '</span>';
                                                                        ?>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td align="right" valign="top" nowrap>
                                            <?
                                            if ($allow_verify_dtu_permission) {
                                                if ($this->po_info['po_status'] == 2 || $this->po_info['po_status'] == 3) {
                                                    echo tep_draw_form('po_verify_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params() . 'subaction=verify_po&po_id=' . $this->po_info['po_id'] . '&v_mode=' . ($this->po_info['purchase_orders_verify_mode'] ? '0' : '1'), 'POST', '');
                                                    if ($this->po_info['purchase_orders_verify_mode']) {
                                                        echo tep_submit_button(BUTTON_UNVERIFY, ALT_BUTTON_UNVERIFY, '', 'inputButton');
                                                    } else {
                                                        echo tep_submit_button(BUTTON_VERIFY, ALT_BUTTON_VERIFY, '', 'inputButton');
                                                    }
                                                    echo '</form>';
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                    </tr>
                    <tr>
                        <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PAYMENT_INFO ?></b></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td valign="top">
                            <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                <tbody>
                                    <tr>
                                        <td width="20%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_PAYMENT_TERM ?>:</b></td>
                                        <td class="main" valign="top">
                                        <?php 
                                        
                                        switch($this->payment['payment_type']) {
                                            case 'g':
                                                echo TEXT_SUPPLIER_CONSIGNMENT;
                                                break;
                                            case 'c':
                                                echo TEXT_SUPPLIER_PRE_PAYMENT;
                                                break;
                                            case 'd':
                                                echo TEXT_SUPPLIER_DTU_PAYMENT;
                                                break;
                                            default :
                                                echo TEXT_SUPPLIER_DAY_TERM;
                                                break;
                                        }
                                        ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="20%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_BILLING_STATUS ?>:</b></td>
                                        <td class="main" valign="top">
                                            <?php
                                            if ($this->po_info['po_billing_status'] == '1') {
                                                echo TEXT_PAYMENT_BILLED . str_repeat("&nbsp;", 8);
                                                if ($this->po_info['po_status'] == 2 && $this->po_info['po_billing_status'] == '1' && $this->po_info['po_paid_status'] != '1') {
                                                    echo tep_draw_form('dtu_debit_payment', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=debit_payment&selected_box=po', 'post', '');
                                                    echo tep_submit_button(BUTTON_DEBIT_PAYMENT, ALT_BUTTON_DEBIT_PAYMENT, '', 'redButton');
                                                    echo "</form>";
                                                }
                                            } else {
                                                echo TEXT_PAYMENT_PENDING_BILLING . str_repeat("&nbsp;", 8);
                                                if ($this->po_info['po_status'] == 2 && $this->payment['payment_type'] == 'd' && ($this->po_info['po_billing_status'] != '-1' && $this->po_info['po_billing_status'] != '1')) {
                                                    echo tep_draw_form('dtu_make_payment', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . '&subaction=make_payment&selected_box=po', 'post', '');
                                                    echo tep_submit_button(BUTTON_MAKE_PAYMENT, ALT_BUTTON_MAKE_PAYMENT, '', 'inputButton');
                                                    echo "</form>";
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="20%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_WITHDRAW_STATUS ?>:</b></td>
                                        <td class="main" valign="top">
                                            <?
                                            switch ($this->po_info['po_paid_status']) {
                                                case '0':
                                                    echo TEXT_PAYMENT_PENDING_WITHDRAW;
                                                    break;
                                                case '1':
                                                    echo TEXT_PAYMENT_FULLY_WITHDRAW;
                                                    break;
                                                case '2':
                                                    echo TEXT_PAYMENT_PARTIAL_WITHDRAW;
                                                    break;
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="20%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_PAYMENT_TRANS ?>:</b></td>
                                        <td class="main" valign="top">
                                            <?
                                            if ($this->po_zero_total_amt == true && $po_used_credit_note_amt === 0) {
                                                echo TEXT_PAYMENT_NO_PAYMENT;
                                            } else {
                                                echo ($po_used_credit_note_amt !== 0 ? "Credit Note: " . $po_used_credit_note_amt . "<br>" : "");
                                                if ($this->po_zero_total_amt == false) {
                                                    if ($this->po_payment_made) {
                                                        foreach ($po_payment_array as $payment_row) {
                                                            echo '<a href="' . tep_href_link(FILENAME_PAYMENT, 'payID=' . $payment_row['store_payments_id'] . '&action=edit', 'NONSSL') . '" target="payment">Payment ' . $payment_row['store_payments_id'] . '</a>' . ' (' . $payment_row['store_payments_status_name'] . ')<br>';
                                                        }
                                                    } else {
                                                        echo TEXT_PAYMENT_PENDING_PAYMENT;
                                                    }
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="20%" class="main" valign="top"><b><?=ENTRY_DTU_FORM_DISBURSEMENT_METHOD?>:</b></td>
                                        <td class="main" valign="top"><?= $store_payment_account_book['payment_methods_alias'] ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                    </tr>
                    <tr>
                        <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_SUPPLIER_INFO ?></b></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                <tbody>
                                    <tr>
                                        <td valign="top" width="50%">
                                            <table width="100%" cellspacing="0" cellpadding="2" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td width="20%" class="main" valign="top"><b><?= ENTRY_DTU_FORM_SUPPLIER ?>:</b></td>
                                                        <td class="main" valign="top"><?= '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $this->supplier['supplier_id'] . '&action=edit&selected_box=customers', 'NONSSL') . '" target="_blank">' . $this->supplier['supplier_name'] . '</a>' ?></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td valign="top">
                                            <div id="po_stat_div"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                    </tr>
                    <?= $this->draw_status_history_table() ?>
                    <tr>
                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
                    </tr>
                    <?
                    switch ($this->po_info['po_status']) {
                        case '1':
                            $this->approve_dtu_form($input_array);
                            break;
                        case '2':
                            $this->process_dtu_form($input_array);
                            break;
                        case '3':
                            $this->completed_dtu_form($input_array);
                            break;
                        case '4':
                            $this->canceled_dtu_form($input_array);
                            break;
                    }
                    ?>
                    <tr>
                        <td>
                        </td>
                    </tr>
                </tbody>
            </table>
            <SCRIPT language="JavaScript" type="text/javascript">
            <!--    
            function get_cancel_comment(form_name) {
                var user_comment = prompt('Please insert the reason to Cancel this DTU Request:', '');
                if (user_comment != null) {
                    document.getElementById('cancel_comments').value = user_comment;
                    return true;
                } else {
                    return false;
                }
            }

            function get_confirm(form_name, price_diff_class) {
                var text = 'Please Confirm to Proceed';
                var price_higher_before = false;
                jQuery("." + price_diff_class).each(function () {
                    if (this.value == "1") {
                        price_higher_before = true;
                        text = 'NOTE: Cost of Some Product(s) higher than Previous Purchase<br>Please Confirm to Proceed';
                    }
                });
                jquery_confirm_box(text, 4, 1, 'Confirm');
                jQuery('#jconfirm_submit').click(function () {
                    jQuery('#' + form_name).submit();
                    return true;
                });
                jQuery("jconfirm_cancel").click(function () {
                    return false;
                });
            }

            function po_check_form() {
                return true;
            }
            //-->
            </SCRIPT>
            <?
        }
    }

    function approve_dtu_form($input_array) {
        global $currencies;
        ?>
        <tr>
            <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tbody>
                        <tr>
                            <td class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_NAME ?></td>
                            <td width="8%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_DTU_PENDING ?></td>
                            <td width="8%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_SELLING_PRICE, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="8%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_STOCK_PRICE, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="2%" align="center" class="invoiceBoxHeading"></td>
                            <td width="6%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_REQUEST_QTY ?></td>
                            <td width="6%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_SUBTOTAL, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="1%" align="center" class="invoiceBoxHeading"></td>
                        </tr>
                    </tbody>
                    <div id="main_items" name="main_items">
                        <?
                        $rows = 0;
                        for ($p = 0, $n = sizeof($this->po_items); $p < $n; $p++) {
                            $item_data = $this->po_items[$p];

                            $rows++;
                            if ($rows % 2 == "1") {
                                $tr_classname = "reportListingEven";
                            } else {
                                $tr_classname = "reportListingOdd";
                            }

                            $prod_cat_id = $item_data['products_categories_id'];
                            $products_cat_select_sql = "SELECT pc.categories_id 
                                                        FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                        WHERE pc.products_id = '" . (int) $item_data['products_id'] . "'";
                            $products_cat_result_sql = tep_db_query($products_cat_select_sql);
                            if ($products_cat_row = tep_db_fetch_array($products_cat_result_sql)) {
                                $prod_cat_id = $products_cat_row['categories_id'];
                            }

                            $pending_po = $this->check_product_in_pending_processing_po($item_data['products_id']);

                            $price_indicator_class = ($item_data['products_unit_price'] >= $item_data['products_sell_price']) ? "redIndicator" : "blackIndicator";

                            $previous_po = $this->get_previous_purchase_price($item_data['products_id'], $this->po_info['po_id']);
                            if (is_array($previous_po)) {
                                if (round($item_data['products_usd_unit_price'], 2) > round($previous_po['products_usd_unit_price'], 2)) {
                                    $icon_img = "up.gif";
                                    $icon_alt = TEXT_PRICE_UP;
                                    $price_diff = "1";
                                } else if (round($item_data['products_usd_unit_price'], 2) == round($previous_po['products_usd_unit_price'], 2)) {
                                    $icon_img = "equal.gif";
                                    $icon_alt = TEXT_PRICE_EQUAL;
                                    $price_diff = "0";
                                } else if (round($item_data['products_usd_unit_price'], 2) < round($previous_po['products_usd_unit_price'], 2)) {
                                    $icon_img = "down.gif";
                                    $icon_alt = TEXT_PRICE_DOWN;
                                    $price_diff = "-1";
                                }
                            } else {
                                $icon_img = "error.gif";
                                $icon_alt = TEXT_NO_PRICE;
                                $price_diff = "";
                            }

                            echo '		<tbody>';
                            echo '			<tr id="1_main_' . $rows . '" class="' . $tr_classname . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $tr_classname . '\')" onclick="rowClicked(this, \'' . $tr_classname . '\')">' . "\n";
                            ?>
                            <td nowrap="" valign="top" class="invoiceRecordsRightBorder"><?= '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($prod_cat_id) . '&pID=' . $item_data["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $item_data["products_id"] . '">' . $item_data['products_name'] . '</a>' ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $pending_po ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= number_format($item_data['products_sell_price'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                            <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder"><span class="<?= $price_indicator_class ?>"><?= $this->monetary_decimal_format($item_data['products_unit_price']) ?></span></td>
                            <td align="center" class="invoiceRecordsRightBorder"><?= tep_draw_hidden_field('price_diff[]', $price_diff, 'class="price_diff"') ?><?= tep_image(DIR_WS_ICONS . $icon_img, $icon_alt) ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $item_data['suggest_qty'] ?></td>
                            <td valign="top" align="right" class="invoiceRecordsRightBorder"><span class="<?= $price_indicator_class ?>"><?= number_format($item_data['subtotal'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></span></td>
                            <td valign="top" align="center"><?= '<span id="' . $rows . '_nav"><a href="javascript:void(0);" onClick="get_product_last_purchase_price(\'' . $rows . '\', \'' . $item_data["products_id"] . '\', \'' . $this->po_info['po_id'] . '\')">' . tep_image(DIR_WS_ICONS . "att.gif", "View Last 4x Cot Price", "14", "13", 'align="top"') . '</a></span>' ?></td>
                            </tr>
                            </tbody>
                            <tbody id="<?= '1_sub_' . $rows . "_" . $item_data['products_id'] . '_cost' ?>" class="hide">
                                <tr id="<?= '1_sub_' . $rows ?>" class="<?= $tr_classname ?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?= '1_main_' . $rows ?>##<?= '1_sub2_' . $rows ?>')" onmouseout="showOutEffect(this, '<?= $tr_classname ?>', '<?= '1_main_' . $rows ?>##<?= '1_sub2_' . $rows ?>')" onclick="showClicked(this, '<?= $tr_classname ?>', '<?= '1_main_' . $rows ?>##<?= '1_sub2_' . $rows ?>')">
                                    <td align="right" colspan="10">
                                        <div id="<?= $item_data['products_id'] . '_cost' ?>"></div>
                                    </td>
                                </tr>
                            </tbody>
                            <?
                        }
                        ?>
                    </div>
                </table>
            </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <?= $this->draw_purchase_orders_total_table() ?>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
        </tr>
        <tr>
            <td>
                <?= tep_draw_form('approve_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=approve_dtu&cont=1', 'post', ' id="approve_dtu_form" onSubmit="return po_check_form();"'); ?>
                <?= tep_draw_hidden_field('po_id', $this->po_info['po_id'], 'id="po_id"') ?>
                <?= tep_draw_hidden_field('action_button', '', 'id="action_button"') ?>
                    <table width="100%" cellspacing="0" cellpadding="2" border="0">
                        <tr>
                            <td align="left">
                                <?php
                                echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtn"', 'inputButton');
                                if ($this->po_info['po_status'] == '1') {
                                    echo '&nbsp;&nbsp;' . tep_submit_button(' Print ', ' Print ', ' onClick="window.open(\'' . tep_href_link(FILENAME_PRINTABLE_DTU_REQUEST, tep_get_all_get_params(array('subaction')) . '&subaction=print_po') . '\', \'_blank\');return false;" name="PrintBtn"', 'inputButton');
                                }
                                ?>
                            </td>
                            <td align="right">
                                <?php
                                if ($this->po_info['po_status'] == '1') {
                                    echo tep_draw_hidden_field('cancel_comments', '', 'id="cancel_comments"');
                                    echo tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="CancelBtn" onClick="document.approve_dtu_form.action_button.value=\'CancelBtn\'; return get_cancel_comment(\'approve_dtu_form\');"', 'redButton') . '&nbsp;&nbsp;' . tep_submit_button(' Approve ', ' Approve ', 'name="ApproveBtn" onClick="document.approve_dtu_form.action_button.value=\'ApproveBtn\'; return get_confirm(\'approve_dtu_form\', \'price_diff\');"', 'greenButton');
                                }
                                ?>
                            </td>
                        </tr>
                    </table>
                </form>
            </td>
        </tr>
    <?
    }

    function process_dtu_form($input_array) {
        global $currencies, $languages_id, $allow_process_dtu_permission, $allow_dtu_cancel_pending_receive_permission, $login_groups_id;

        $acct_book_array = $this->get_supplier_account_book_info($this->supplier['supplier_id'], $this->payment['store_payment_account_book_id']);
        $payment_currency = $acct_book_array['pm_currency'];

        $po_wsc_in_cronjob_select_sql = "SELECT payment_type, purchase_orders_billing_status FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . (int) $this->po_info['po_id'] . "'";
        $po_wsc_in_cronjob_result_sql = tep_db_query($po_wsc_in_cronjob_select_sql);
        $po_wsc_in_cronjob_row = tep_db_fetch_array($po_wsc_in_cronjob_result_sql);
        ?>
        <tr>
            <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
        </tr>
        <tr>
            <td>
                <div id="lockingBtn">
                    <?
                    $lock_orders_select_sql = "SELECT po.purchase_orders_locked_by, po.purchase_orders_locked_from_ip, 
                                                po.purchase_orders_locked_datetime, a.admin_email_address 
                                                FROM " . TABLE_PURCHASE_ORDERS . " AS po 
                                                LEFT JOIN " . TABLE_ADMIN . " AS a 
                                                        ON po.purchase_orders_locked_by = a.admin_id 
                                                WHERE purchase_orders_id = '" . (int) $this->po_info['po_id'] . "'";
                    $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                    $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                    if (tep_not_null($lock_orders_row["purchase_orders_locked_by"])) {
                        if ($lock_orders_row["purchase_orders_locked_by"] == $_SESSION['login_id']) {
                            // This person is the owner who lock this order
                            $editable_view = true;
                            echo '<div id="navBtn" style="float:left;">
                                        <ul>
                                            <li id="unlock_btn"><a href="javascript:;" onClick="doPOLocked(\'' . $_SESSION['login_id'] . '\', \'' . (int) $this->po_info['po_id'] . '\', \'' . (int) $languages_id . '\', \'ul\', \'lockingBtn\');" title="Unlocking this order">UNLOCK</a></li>
                                        </ul>
                                    </div>
                                    <div style="width:90%;">' .
                                        sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . '
                                    </div>';
                        } else {
                            // Order has been locked by other person
                            $admin_group_to_contact = tep_admin_group_unlock_dtu_po_permission();
                            if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                                $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                                $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                            } else {
                                $contact_admin_group_msg = '';
                                $contact_admin_group_id_array = array();
                            }

                            if (in_array($login_groups_id, $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                                echo '<div id="navBtn">
                                        <ul>
                                            <li id="unlock_btn"><a href="javascript:;" onClick="doPOLocked(\'' . $_SESSION['login_id'] . '\', \'' . (int) $this->po_info['po_id'] . '\', \'' . (int) $languages_id . '\', \'ulo\', \'lockingBtn\');" title="Unlocking this order">UNLOCK</a></li>
                                        </ul>
                                    </div>
                                    <div style="float:right; width:90%;">' .
                                        sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, tep_not_null($lock_orders_row["admin_email_address"]) ? $lock_orders_row["admin_email_address"] : ($lock_orders_row["purchase_orders_locked_by"] == 0 ? 'System' : $lock_orders_row["purchase_orders_locked_by"]), $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"]) . '
                                    </div>';
                            } else {
                                echo '<div style="float:right; width:98%;">' .
                                        sprintf(TEXT_ORDER_LOCKED_BY_OTHER, tep_not_null($lock_orders_row["admin_email_address"]) ? $lock_orders_row["admin_email_address"] : ($lock_orders_row["purchase_orders_locked_by"] == 0 ? 'System' : $lock_orders_row["purchase_orders_locked_by"]), $lock_orders_row["purchase_orders_locked_datetime"], $lock_orders_row["purchase_orders_locked_from_ip"], $contact_admin_group_msg) . '
                                    </div>';
                            }
                        }
                    } else {
                        // No body lock this order. Show locked button
                        echo '<div id="navBtn" style="">
                                <ul>
                                    <li id="lock_btn"><a href="javascript:;" onClick="doPOLocked(\'' . $_SESSION['login_id'] . '\', \'' . (int) $this->po_info['po_id'] . '\', \'' . (int) $languages_id . '\', \'l\', \'lockingBtn\', \'' . date("Y-m-d H:i:s") . '\');" title="Locking this order">LOCK</a></li>
                                </ul>
                            </div>
                            <div style="float:right; width:90%; text-align: justify;">' . TEXT_ORDER_NOT_BEEN_LOCKED . '</div>';
                    }
                    ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="locking_history">
                    <div style="float:left;"><a href="javascript:;" onClick="lockingHistory('<?= (int) $this->po_info['po_id'] ?>', '1', 'locking_history');">Show Locking History</a></div>
                </div>
            </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tbody>
                        <tr>
                            <td class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_NAME ?></td>
                            <td width="15%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_DTU_PENDING ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_STOCK_PRICE, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_REQUEST_QTY ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_SUBTOTAL, '(' . $this->po_info['currency'] . ')') ?></td>
                        </tr>
                        <div id="main_items" name="main_items">
                            <?
                            $rows = 0;
                            for ($p = 0, $n = sizeof($this->po_items); $p < $n; $p++) {
                                $item_data = $this->po_items[$p];

                                $rows++;
                                if ($rows % 2 == "1") {
                                    $tr_classname = "reportListingEven";
                                } else {
                                    $tr_classname = "reportListingOdd";
                                }

                                $prod_cat_id = $item_data['products_categories_id'];
                                $products_cat_select_sql = "SELECT pc.categories_id 
                                                            FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                            WHERE pc.products_id = '" . (int) $item_data['products_id'] . "'";
                                $products_cat_result_sql = tep_db_query($products_cat_select_sql);
                                if ($products_cat_row = tep_db_fetch_array($products_cat_result_sql)) {
                                    $prod_cat_id = $products_cat_row['categories_id'];
                                }

                                $pending_po = $this->check_product_in_pending_processing_po($item_data['products_id']);

                                $pending_recv_qty = $item_data['suggest_qty'] - ($item_data['products_good_delivered_quantity'] + $item_data['products_canceled_quantity'] + $item_data['products_refund_quantity'] + $item_data['products_credit_note_quantity']);
                                $pending_indicator_class = ($pending_recv_qty == 0) ? "greenIndicator" : "redIndicator";

                                if ($allow_process_dtu_permission) {
                                    if (($this->po_info['purchase_orders_locked_by'] == $_SESSION['login_id']) && ($pending_recv_qty > 0)) {
                                        if ($po_wsc_in_cronjob_row['payment_type'] != 'c' || ($po_wsc_in_cronjob_row['payment_type'] == 'c' && $po_wsc_in_cronjob_row['purchase_orders_billing_status'] != '-1')) {
                                            $pending_recv_qty = '<a href="' . tep_href_link(FILENAME_CDKEY, '&po_id=' . $this->po_info['po_id'] . '&filterby_p=' . $item_data["products_id"] . '&selected_box=po#tab-po-upload') . '" target="_blank" class="highlightLink">' . $pending_recv_qty . '</a>';
                                        }
                                    }
                                }

                                echo '<tr id="1_main_' . $rows . '" class="' . $tr_classname . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $tr_classname . '\')" onclick="rowClicked(this, \'' . $tr_classname . '\')">' . "\n";
                                ?>
                                <td nowrap="" valign="top" class="invoiceRecordsRightBorder"><?= '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($prod_cat_id) . '&pID=' . $item_data["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $item_data["products_id"] . '">' . $item_data['products_name'] . '</a>' ?></td>
                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $pending_po ?></td>
                                <td nowrap="" valign="top" align="center" class="invoiceRecordsRightBorder"><?= $this->monetary_decimal_format($item_data['products_unit_price']) ?></td>
                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $item_data['suggest_qty'] ?></td>
                                <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= number_format($item_data['subtotal'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                                </tr>
                                <?
                            }
                            ?>
                        </div>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <?= $this->draw_purchase_orders_total_table() ?>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
        </tr>
        <tr>
            <td>
                <?= tep_draw_form('refund_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=refund_dtu&cont=1', 'post', 'onSubmit="return po_check_form();"'); ?>
                <?= tep_draw_hidden_field('po_id', $this->po_info['po_id'], 'id="po_id"') ?>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tr>
                        <td align="left">
                            <? 
                            echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtn"', 'inputButton');
                            echo '&nbsp;&nbsp;' . tep_submit_button(' Print ', ' Print ', ' onClick="window.open(\''.tep_href_link(FILENAME_PRINTABLE_DTU_REQUEST, tep_get_all_get_params(array('subaction')).'&subaction=print_po').'\', \'_blank\');return false;" name="PrintBtn"', 'inputButton');
                            ?>
                        </td>
                        <td align="right">
                            <?php
                            echo tep_draw_hidden_field('cancel_comments', '', 'id="cancel_comments"');
                            if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '2', '3') && $this->po_info['purchase_orders_locked_by'] == $_SESSION['login_id']) {
                                echo tep_submit_button(BUTTON_COMPLETE, ALT_BUTTON_COMPLETE, 'name="CompleteBtn"', 'inputButton') . '<br>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '<br>';
                            }
                            if ($this->po_info['po_paid_status'] == '2' && $this->po_zero_total_amt == false && $this->po_payment_made) {
                                if ($this->po_payment_made == true) {
                                    // when partial payment, PO not zero amount and has payment made
                                    echo '<span class="main">' . WARNING_TEXT_DTU_FORM_DTU_PARTIAL_PAID . '</span>';
                                } else {
                                    if ($this->po_info['po_billing_status'] == '0') {
                                        if ($allow_dtu_cancel_pending_receive_permission && $this->po_info['purchase_orders_locked_by'] == $_SESSION['login_id']) {
                                            echo tep_submit_button(BUTTON_CANCEL_PENDING_RECEIVE, ALT_BUTTON_CANCEL_PENDING_RECEIVE, 'name="CancelPendingReceiveBtn" onClick="return get_refund_remark(this,\'' . $payment_currency . '\');"', 'inputButton');
                                        }
                                    }
                                }
                            } else {
                                if ($this->po_info['po_billing_status'] == '0') {
                                    if ($allow_dtu_cancel_pending_receive_permission && $this->po_info['purchase_orders_locked_by'] == $_SESSION['login_id']) {
                                        echo tep_submit_button(BUTTON_CANCEL_PENDING_RECEIVE, ALT_BUTTON_CANCEL_PENDING_RECEIVE, 'name="CancelPendingReceiveBtn" onClick="return get_refund_remark(this,\'' . $payment_currency . '\');"', 'inputButton');
                                    }
                                }
                            }
                            ?>
                        </td>
                    </tr>
                </table>
                </form>
            </td>
        </tr>
        <?
    }

    function completed_dtu_form($input_array) {
        global $currencies, $languages_id;
        ?>
        <tr>
            <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tbody>
                        <tr>
                            <td class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_NAME ?></td>
                            <td width="15%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_DTU_PENDING ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_STOCK_PRICE, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_REQUEST_QTY ?></td>
                            <td width="10%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_SUBTOTAL, '(' . $this->po_info['currency'] . ')') ?></td>
                        </tr>
                    <div id="main_items" name="main_items">
                        <?
                        $rows = 0;
                        for ($p = 0, $n = sizeof($this->po_items); $p < $n; $p++) {
                            $item_data = $this->po_items[$p];

                            $rows++;
                            if ($rows % 2 == "1") {
                                $tr_classname = "reportListingEven";
                            } else {
                                $tr_classname = "reportListingOdd";
                            }

                            $prod_cat_id = $item_data['products_categories_id'];
                            $products_cat_select_sql = "SELECT pc.categories_id 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
										WHERE pc.products_id = '" . (int) $item_data['products_id'] . "'";
                            $products_cat_result_sql = tep_db_query($products_cat_select_sql);
                            if ($products_cat_row = tep_db_fetch_array($products_cat_result_sql)) {
                                $prod_cat_id = $products_cat_row['categories_id'];
                            }

                            $pending_po = $this->check_product_in_pending_processing_po($item_data['products_id']);

                            echo '			<tr id="1_main_' . $rows . '" class="' . $tr_classname . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $tr_classname . '\')" onclick="rowClicked(this, \'' . $tr_classname . '\')">' . "\n";
                            ?>
                            <td nowrap="" valign="top" class="invoiceRecordsRightBorder"><?= '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($prod_cat_id) . '&pID=' . $item_data["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $item_data["products_id"] . '">' . $item_data['products_name'] . '</a>' ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $pending_po ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $this->monetary_decimal_format($item_data['products_unit_price']) ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $item_data['suggest_qty'] ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= number_format($item_data['subtotal'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                            </tr>
                            <?
                        }
                        ?>
                    </div>
                </tbody>
            </table>
        </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <?= $this->draw_purchase_orders_total_table() ?>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
        </tr>
        <tr>
            <td>
                <?= tep_draw_form('completed_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=rollback&cont=1', 'post', ' id="completed_dtu_form" onSubmit="return po_check_form();"'); ?>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tr>
                        <td align="left">
                            <?=tep_draw_hidden_field('po_id', $this->po_info['po_id'], 'id="po_id"')?>
                            <?
                            echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtn"', 'inputButton');
                            echo '&nbsp;&nbsp;' . tep_submit_button(' Print ', ' Print ', ' onClick="window.open(\''.tep_href_link(FILENAME_PRINTABLE_DTU_REQUEST, tep_get_all_get_params(array('subaction')).'&subaction=print_po').'\', \'_blank\');return false;" name="PrintBtn"', 'inputButton');
                            ?>
                        </td>
                        <td align="right">
                            <?
                            if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '3', '2')) {
                                if ($this->po_info['po_paid_status']!='1') {
                                    echo tep_submit_button(BUTTON_ROLLBACK_TO_PROCESS, ALT_ROLLBACK_TO_PROCESS, 'name="RollbackProcessBtn"', 'inputButton');
                                }
                            }
                            ?>
                        </td>
                    </tr>
                </table>
            </form>
        </td>
        </tr>
        <?
    }

    function canceled_dtu_form($input_array) {
        global $currencies, $languages_id;
        ?>
        <tr>
            <td valign="top" class="pageHeading"><b><?= TABLE_HEADING_DTU_PRODUCT_DETAILS ?></b></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tbody>
                        <tr>
                            <td class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_NAME ?></td>
                            <td width="8%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_DTU_PENDING ?></td>
                            <td width="8%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_STOCK_PRICE, '(' . $this->po_info['currency'] . ')') ?></td>
                            <td width="6%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_REQUEST_QTY ?></td>
                            <td width="6%" align="center" class="invoiceBoxHeading"><?= sprintf(TABLE_HEADING_SUBTOTAL, '(' . $this->po_info['currency'] . ')') ?></td>
                        </tr>
                    <div id="main_items" name="main_items">
                        <?
                        $rows = 0;
                        for ($p = 0, $n = sizeof($this->po_items); $p < $n; $p++) {
                            $item_data = $this->po_items[$p];

                            $rows++;
                            if ($rows % 2 == "1") {
                                $tr_classname = "reportListingEven";
                            } else {
                                $tr_classname = "reportListingOdd";
                            }

                            $prod_cat_id = $item_data['products_categories_id'];
                            $products_cat_select_sql = "SELECT pc.categories_id 
                                                        FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                        WHERE pc.products_id = '" . (int) $item_data['products_id'] . "'";
                            $products_cat_result_sql = tep_db_query($products_cat_select_sql);
                            if ($products_cat_row = tep_db_fetch_array($products_cat_result_sql)) {
                                $prod_cat_id = $products_cat_row['categories_id'];
                            }

                            $pending_po = $this->check_product_in_pending_processing_po($item_data['products_id']);

                            echo '<tr id="1_main_' . $rows . '" class="' . $tr_classname . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $tr_classname . '\')" onclick="rowClicked(this, \'' . $tr_classname . '\')">' . "\n";
                            ?>
                            <td nowrap="" valign="top" class="invoiceRecordsRightBorder"><?= '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($prod_cat_id) . '&pID=' . $item_data["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $item_data["products_id"] . '">' . $item_data['products_name'] . '</a>' ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $pending_po ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $this->monetary_decimal_format($item_data['products_unit_price']) ?></td>
                            <td valign="top" align="center" class="invoiceRecordsRightBorder"><?= $item_data['suggest_qty'] ?></td>
                            <td valign="top" align="right" class="invoiceRecordsRightBorder"><?= number_format($item_data['subtotal'], $currencies->currencies[$this->po_info['currency']]['decimal_places'], $currencies->currencies[$this->po_info['currency']]['decimal_point'], $currencies->currencies[$this->po_info['currency']]['thousands_point']) ?></td>
                            </tr>
                            <?
                        }
                        ?>
                    </div>
                </tbody>
            </table>
        </td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <?= $this->draw_purchase_orders_total_table() ?>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_black.gif', '100%', '1') ?></td>
        </tr>
        <tr>
            <td>
                <?= tep_draw_form('canceled_dtu_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'cont=1', 'post', ' id="canceled_dtu_form" onSubmit="return po_check_form();"'); ?>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tr>
                        <td align="left">
                            <?= tep_draw_hidden_field('po_id', $this->po_info['po_id'], 'id="po_id"') ?>
                            <?
                            echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, 'name="BackBtn"', 'inputButton');
                            echo '&nbsp;&nbsp;' . tep_submit_button(' Print ', ' Print ', ' onClick="window.open(\''.tep_href_link(FILENAME_PRINTABLE_DTU_REQUEST, tep_get_all_get_params(array('subaction')).'&subaction=print_po').'\', \'_blank\');return false;" name="PrintBtn"', 'inputButton');
                            ?>
                        </td>
                        <td align="right">
                        </td>
                    </tr>
                </table>
            </form>
        </td>
        </tr>
        <?
    }

    function draw_purchase_orders_total_table() {
        global $currencies, $languages_id;

        $currencies->set_decimal_places('-1');
        ?>		
        <tr>
            <td>
                <table width="100%" cellspacing="0" cellpadding="2" border="0">
                    <tbody>
                        <? foreach ($this->po_total as $po_total_class => $po_total_row) { ?>
                            <tr>
                                <td width="90%" align="right" class="main"><?= $po_total_row['title'] ?></td>
                                <td align="right" class="main"><?= $currencies->format($po_total_row['value'], false, $po_total_row['currency']) ?></td>
                            </tr>
                        <? } ?>
                        <tr>
                            <td width="90%" align="right" class="smallText"><i><?= TEXT_REMAINING_AMOUNT ?>:</i></td>
                            <td align="right" class="smallText"><i><?= $currencies->format($this->po_summary['total_remaining_amount'], false, $this->po_info['currency']) ?></i></td>
                        </tr>
                        <tr>
                            <td width="90%" align="right" class="smallText" colspan="2">
                                <?= '<i>(' . $currencies->format('1', false, $this->po_info['currency']) . '=' . $currencies->currencies[$this->payment['store_payment_currency']]['symbol_left'] . $this->po_info['confirmed_currency_value'] . $currencies->currencies[$this->payment['store_payment_currency']]['symbol_right'] . ')</i><br>' ?>
                                <?= '<i>(' . $currencies->format('1', false, $this->payment['store_payment_currency']) . '=' . $currencies->currencies[$this->po_info['currency']]['symbol_left'] . (tep_round(1 / $this->po_info['confirmed_currency_value'], 6)) . $currencies->currencies[$this->po_info['currency']]['symbol_right'] . ')</i>' ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <?
    }

    function draw_status_history_table() {
        global $currencies, $languages_id, $allow_view_dtu_remark_permission;

        $order_comment_style_array = array("orderCommentSystem", "orderCommentCrew", "orderCommentDelivery");
        if ($allow_view_dtu_remark_permission) {
            ?>
            <tr>
                <td>
                    <?= tep_draw_form('dtu_comment_form', FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction')) . 'subaction=add_remark', 'post', 'id="dtu_comment_form"'); ?>
                    <a href="javascript:;" onclick="toggle_po_comment(this, 'o_comment_')">Hide All</a> |
                    <a href="javascript:;" onclick="toggle_po_comment(this, 'o_comment_0_')">Hide System</a> |
                    <a href="javascript:;" onclick="toggle_po_comment(this, 'o_comment_1_')">Hide Crew</a> |
                    <a href="javascript:;" onclick="toggle_po_comment(this, 'o_comment_2_')">Hide Qty Update</a>
                </td>
            </tr>
            <tr>
                <td class="main">
                    <div id="order_comment_history_box" class="show">
                        <table border="1" cellspacing="0" cellpadding="5">
                            <tr>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_DATE_ADDED ?></b></td>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_NOTIFY_SUPPLIER ?></b></td>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_STATUS ?></b></td>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_COMMENTS ?></b></td>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_MODIFIED_BY ?></b></td>
                                <td class="smallText" align="center"><b><?= STATUS_TABLE_HEADING_ACTION ?></b></td>
                            </tr>
                            <?
                            $set_as_remark_array = array();
                            $po_history_query = tep_db_query("select purchase_orders_status_history_id, purchase_orders_status_id, date_added, comments, comments_type, changed_by, set_as_po_remarks, supplier_notified from " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . " where purchase_orders_id = '" . tep_db_input($this->po_info['po_id']) . "' order by purchase_orders_status_history_id");
                            if (tep_db_num_rows($po_history_query)) {
                                $order_comment_count = 0;
                                while ($po_history = tep_db_fetch_array($po_history_query)) {
                                    $comment_row_style = $po_history["set_as_po_remarks"] == 1 ? 'class="orderRemarkSelectedRow"' : 'class="' . $order_comment_style_array[$po_history["comments_type"]] . '"';
                                    $formatted_date_comment_added = $po_history["date_added"];
                                    echo '	<tbody id="o_comment_' . $po_history["comments_type"] . '_' . $po_history["purchase_orders_status_history_id"] . '">
								<tr ' . $comment_row_style . '>
									<td class="smallText" align="center">' . (tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--') . '</td>';
                                    echo '			<td class="smallText" align="center">';
                                    if ($po_history['supplier_notified'] == '1') {
                                        echo tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) . "</td>\n";
                                    } else {
                                        echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS) . "</td>\n";
                                    }

                                    $payment_query = tep_db_query("select payment_methods_id from " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " where store_payment_account_book_id = '" . tep_db_input($this->payment['store_payment_account_book_id']) . "'");
                                    $payment_row = tep_db_fetch_array($payment_query);
                                    $refund_order_pm_array = rawurlencode(tep_array_serialize(array($payment_row['payment_methods_id'])));

                                    echo ' 			<td class="smallText">' . $this->po_status_arr[$po_history['purchase_orders_status_id']] . '</td>
									<td class="smallText">' . nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $po_history['comments'])) . '&nbsp;</td>
									<td class="smallText">' . nl2br(tep_db_output($po_history['changed_by'])) . '&nbsp;</td>
									<td class="smallText"><div id="set_remark_' . $po_history["purchase_orders_status_history_id"] . '">' . ($po_history["set_as_po_remarks"] == 1 ? '&nbsp;' : '<a href="javascript:;" onClick="setAsPORemark(\'' . $po_history["purchase_orders_status_history_id"] . '\', \'' . (int) $this->po_info['po_id'] . '\', \'' . (int) $languages_id . '\', \'order_comment_history_box\');">' . TEXT_SET_AS_REMARK . '</a>') . '</div></td>
								</tr>
							</tbody>' . "\n";
                                    $set_as_remark_array[] = "set_remark_" . $po_history["purchase_orders_status_history_id"];
                                    $order_comment_count++;

                                    echo tep_draw_hidden_field('hidden_o_comment_' . $po_history["comments_type"] . '_' . $po_history["purchase_orders_status_history_id"], 'o_comment_' . $po_history["comments_type"] . '_' . $po_history["purchase_orders_status_history_id"], 'id="hidden_o_comment_' . $po_history["comments_type"] . '_' . $po_history["purchase_orders_status_history_id"] . '" class="o_comment_' . $po_history["comments_type"] . '_"');
                                }
                            } else {
                                echo '  		<tr>' . "\n" .
                                '          	<td class="smallText" colspan="6">' . TEXT_NO_PO_HISTORY . '</td>' . "\n" .
                                '          </tr>' . "\n";
                            }
                            ?>
                            <tr>
                                <td class="smallText" colspan="6">
                                    <?= tep_draw_separator('pixel_trans.gif', '1', '30') ?>
                                    <?= tep_submit_button(BUTTON_ADD_REMARK, ALT_BUTTON_ADD_REMARK, 'name="AddRemarkBtn" onClick="get_crew_remark();"', 'inputButton') ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </form>
                <script>
                <!--
                var set_as_remark_array = new Array();
                <?
                if (count($set_as_remark_array)) {
                    foreach ($set_as_remark_array as $div_id) {
                        ?>                                                                                                                                                        set_as_remark_array.push('<?= $div_id ?>');
                        <?
                    }
                }

                echo 'initSetRemarkView(1);' . "\n";
                ?>
                //-->
                </script>
            </td>
            </tr>
            <?php
        }
    }

    function check_product_in_pending_processing_po($products_id) {
        $pending_n_processing_status = "'1,2'";
        $pending_po_select_sql = "SELECT DISTINCT po.purchase_orders_id 
									FROM " . TABLE_PURCHASE_ORDERS . " AS po 
									INNER JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop 
										ON (pop.purchase_orders_id = po.purchase_orders_id AND pop.products_id = '" . tep_db_input($products_id) . "') 
									WHERE FIND_IN_SET(po.purchase_orders_status , " . $pending_n_processing_status . ")";
        $pending_po_result_sql = tep_db_query($pending_po_select_sql);
        $pending_po_row = tep_db_num_rows($pending_po_result_sql);

        if ($pending_po_row === false) {
            $pending_po_row = 0;
        }

        return $pending_po_row;
    }

    function get_previous_purchase_price($products_id, $exclude_po_id = '') {
        $previous_po = array('products_usd_unit_price' => 0);

        $ex_po_id_str = "1";
        if (tep_not_null($exclude_po_id)) {
            $ex_po_id_str = " po.purchase_orders_id <> '" . tep_db_input($exclude_po_id) . "'";
        }

        $previous_po_select_sql = "	SELECT pop.products_usd_unit_price
                                    FROM " . TABLE_PURCHASE_ORDERS . " AS po
                                    INNER JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop
                                        ON (po.purchase_orders_id = pop.purchase_orders_id AND pop.products_id = '" . tep_db_input($products_id) . "')
                                    WHERE " . $ex_po_id_str . "
                                        AND po.purchase_orders_type = 1
                                        AND po.purchase_orders_status = 3
                                    ORDER BY po.purchase_orders_id DESC LIMIT 1";
        $previous_po_result_sql = tep_db_query($previous_po_select_sql);
        if ($previous_po_row = tep_db_fetch_array($previous_po_result_sql)) {
            $previous_po = $previous_po_row;
        }

        return $previous_po;
    }

    function insert_new_po($input_array, &$messageStack) {
        global $currencies, $po_suppliers, $allow_create_dtu_permission;
        $po_payment_remark = '';

        if ($allow_create_dtu_permission) {
            $dtu_publisher = new publishers($input_array['dtu_supplier']);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_supplier_id'];
            
            $supplier_info = $po_suppliers->get_po_supplier_info(tep_db_input($dtu_supplier_id));

            $rate_to_usd = 1 / $currencies->get_value($input_array['dtu_currency'], 'sell');

            $state_id = '';
            if (tep_not_null($this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['state'])) {
                $zone_select_sql = "SELECT z.zone_id 
                                    FROM " . TABLE_ZONES . " AS z
                                    INNER JOIN " . TABLE_COUNTRIES . " AS c
                                        ON (c.countries_id=z.zone_country_id AND c.countries_name='" . $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['country'] . "')
                                    WHERE z.zone_code='" . $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['state'] . "'";
                $zone_result_sql = tep_db_query($zone_select_sql);
                if ($zone_row = tep_db_fetch_array($zone_result_sql)) {
                    $state_id = $zone_row['zone_id'];
                }
            }

            $gst_value = $this->po_company_gst[$input_array['dtu_delivery_address']];
            $gst_show = false;

            // Bank Charge follow Disbursement Currency
            $payment_currency = '';
            $pm_select_sql = "SELECT pab.store_payment_account_book_id, pm.payment_methods_send_currency
                                FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
                                INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                    ON pab.payment_methods_id=pm.payment_methods_id
                                WHERE pab.store_payment_account_book_id = '" . tep_db_input($input_array['dtu_supplier_payment']) . "'";
            $pm_result_sql = tep_db_query($pm_select_sql);
            if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
                if ($pm_row['payment_methods_send_currency'] < 1) {
                    $payment_currency = $input_array['dtu_payment_method_currency'];
                } else {
                    $payment_currency = $currencies->get_code_by_id($pm_row['payment_methods_send_currency']);
                }
            }

            // create purchase order record
            $purchase_orders_data = array(
                'supplier_id' => tep_db_prepare_input($dtu_supplier_id),
                'supplier_name' => $supplier_info['name'],
                'supplier_company' => $supplier_info['company'],
                'supplier_street_address' => $supplier_info['street_address'],
                'supplier_suburb' => $supplier_info['suburb'],
                'supplier_city' => $supplier_info['city'],
                'supplier_postcode' => $supplier_info['postcode'],
                'supplier_state' => $supplier_info['state'],
                'supplier_country' => tep_get_country_name($supplier_info['country_id']),
                'supplier_telephone_country' => tep_get_country_name($supplier_info['country_id']),
                'supplier_country_international_dialing_code' => $supplier_info['customers_country_dialing_code_id'],
                'supplier_telephone' => $supplier_info['state'],
                'supplier_email_address' => $supplier_info['state'],
                'supplier_address_format_id' => $supplier_info['format_id'],
                'delivery_location' => tep_db_prepare_input($input_array['dtu_delivery_address']),
                'delivery_name' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['name'],
                'delivery_company' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['company'],
                'delivery_street_address' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['street_address'],
                'delivery_suburb' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['suburb'],
                'delivery_city' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['city'],
                'delivery_postcode' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['postcode'],
                'delivery_state' => $state_id,
                'delivery_country' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['country'],
                'delivery_address_format_id' => $this->po_delivery_address[$input_array['dtu_delivery_address']]['TOP']['format_id'],
                'payment_type' => $supplier_info['payment_type'],
                'payment_term' => $supplier_info['payment_term'],
                'payment_days_pay_wsc' => $supplier_info['po_days_pay_wsc'],
                'store_payment_account_book_id' => tep_db_prepare_input($input_array['dtu_supplier_payment']),
                'purchase_orders_contact_info' => tep_db_prepare_input($input_array['dtu_contact_person']),
                'purchase_orders_issue_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                'purchase_orders_status' => '1',
                'purchase_orders_type' => '1',
                'purchase_orders_paid_currency' => tep_db_prepare_input($payment_currency),
                'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                'suggested_currency_value' => tep_db_prepare_input($input_array['suggest_rate']),
                'confirmed_currency_value' => tep_db_prepare_input($input_array['confirm_rate']),
                'currency_usd_value' => tep_db_prepare_input($rate_to_usd),
                'last_modified' => 'now()'
            );

            // if bank charges involve, keep entered value
            if (isset($input_array['dtu_pay_bankcharge']) && $input_array['dtu_pay_bankcharge'] > 0) {
                // $purchase_orders_data['purchase_orders_paid_currency'] = tep_db_prepare_input($payment_currency);
                $purchase_orders_data['purchase_orders_bankcharges_included'] = tep_db_prepare_input($input_array['dtu_pay_bankcharge']);
            }

            tep_db_perform(TABLE_PURCHASE_ORDERS, $purchase_orders_data);
            $insert_id = tep_db_insert_id();

            // update purchase order table with PO Reference Code
            $new_running_count = $supplier_info['po_ref_counter'] + 1;
            $po_suppliers->update_po_supplier_po_reference_counter($dtu_supplier_id, $new_running_count);

            $po_ref_id = $supplier_info['code'] . '-' . $supplier_info['po_ref_year'] . '-' . sprintf("%04s", $new_running_count);
            $update_po_data = array('purchase_orders_ref_id' => $po_ref_id);
            tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $insert_id . "'");

            // Calculate PO total add bank charges, if any exist.
            $po_total_amount = 0;

            // create purchase order products record
            foreach ($input_array['dtu_items_prod_id'] as $po_products_id) {
                // Check for virtual products main product_id
                $po_main_products_id = isset($input_array['dtu_items_main_prod_id_' . $po_products_id]) ? $input_array['dtu_items_main_prod_id_' . $po_products_id] : '';
                $qry_po_products_id = $po_products_id;
                if (tep_not_null($po_main_products_id)) {
                    $qry_po_products_id = $po_main_products_id;
                }

                $products_select_sql = "SELECT p.products_id, pd.products_name, p.products_model, 
                                            p.products_tax_class_id, p.custom_products_type_id, p.products_main_cat_id 
                                        FROM " . TABLE_PRODUCTS . " p
                                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
                                            ON p.products_id=pd.products_id
                                        WHERE p.products_id = '" . (int) $qry_po_products_id . "'
                                        AND pd.language_id = '1'";
                $products_query = tep_db_query($products_select_sql);
                if ($products_row = tep_db_fetch_array($products_query)) {
                    // calculate purchase product unit price in PO currency
                    $stock_price_type = $input_array['stock_price_type_' . $po_products_id];
                    if ($stock_price_type == '$') {
                        $unit_price = tep_db_prepare_input($input_array['unit_price_' . $po_products_id]);
                    } else if ($stock_price_type == '%') {
                        $unit_price = tep_db_prepare_input($input_array['sell_price_' . $po_products_id] * ( 1 - ($input_array['unit_price_' . $po_products_id] / 100)));
                    }

                    // calculate subtotal amount
                    $subtotal = round(($unit_price * $input_array['dtu_item_qty_' . $po_products_id]), 8);
                    $subtotal_usd = round(($unit_price * $input_array['dtu_item_qty_' . $po_products_id] * $rate_to_usd), 8);
                    $po_total_amount += $subtotal;

                    // create purchase order product record
                    $po_products_data = array(
                        'purchase_orders_id' => $insert_id,
                        'products_id' => $po_products_id,
                        'products_model' => $products_row['products_model'],
                        'products_name' => tep_db_prepare_input($input_array['dtu_items_prod_name_' . $po_products_id]),
                        'products_unit_price_type' => tep_db_prepare_input($input_array['stock_price_type_' . $po_products_id]),
                        'products_unit_price_value' => tep_db_prepare_input($input_array['unit_price_' . $po_products_id]),
                        'products_unit_price' => $unit_price,
                        'products_selling_price' => tep_db_prepare_input($input_array['sell_price_' . $po_products_id]),
                        'products_subtotal' => tep_db_prepare_input($subtotal),
                        'products_usd_selling_price' => ($input_array['sell_price_' . $po_products_id] * $rate_to_usd),
                        'products_usd_unit_price' => ($unit_price * $rate_to_usd),
                        'products_usd_subtotal' => tep_db_prepare_input($subtotal_usd),
                        'products_tax' => tep_get_tax_rate($products_row['products_tax_class_id']),
                        'products_quantity' => tep_db_prepare_input($input_array['dtu_item_qty_' . $po_products_id]),
                        'custom_products_type_id' => $products_row['custom_products_type_id'],
                        'products_categories_id' => $products_row['products_main_cat_id']
                    );
                    tep_db_perform(TABLE_PURCHASE_ORDERS_PRODUCTS, $po_products_data);
                    
                    // Update table: orders_top_up_withdrawal
                    foreach ($input_array['dtu_items_topup_id_' . $po_products_id] as $dtu_topup_id) {
                        $dtu_products_data = array(
                            'purchase_orders_id' => $insert_id,
                            'top_up_withdrawal_ref_id' => $po_ref_id,
                            'top_up_withdrawal_status' => '1',
                            'top_up_withdrawal_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                            'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                        );
                        tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $dtu_products_data, 'update', "top_up_id = '" . (int) $dtu_topup_id . "'");
                        
                        $dtu_item_data = array(
                            'top_up_id' => (int) $dtu_topup_id,
                            'purchase_orders_id' => (int) $insert_id,
                            'purchase_orders_ref_id' => $po_ref_id,
                            'top_up_withdrawal_status' => '1',
                            'top_up_withdrawal_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                            'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                        );
                        tep_db_perform(TABLE_ORDERS_TOP_UP_PURCHASE_ORDERS, $dtu_item_data);
                    }
                }
            }
            
            // Save Charge Back DTU
            $topup_id_cb = array();
            $po_subtotal_cb = 0;
            if (isset($input_array['include_cb']) && ($input_array['include_cb'] == '1' || $input_array['include_cb'] == 'on')) {
                
                foreach ($input_array['dtu_cb_items_prod_id'] as $po_cb_products_id) {
                    // Check for virtual products main product_id
                    $po_main_products_id = isset($input_array['dtu_cb_items_main_prod_id_' . $po_products_id]) ? $input_array['dtu_items_main_prod_id_' . $po_products_id] : '';
                    $qry_po_cb_products_id = $po_products_id;
                    if (tep_not_null($po_main_products_id)) {
                        $qry_po_cb_products_id = $po_main_products_id;
                    }

                    $products_select_sql = "SELECT p.products_id
                                            FROM " . TABLE_PRODUCTS . " p
                                            WHERE p.products_id = '" . (int) $qry_po_cb_products_id . "'";
                    $products_query = tep_db_query($products_select_sql);
                    if ($products_row = tep_db_fetch_array($products_query)) {
                        // calculate purchase product unit price in PO currency
                        $stock_price_type = $input_array['stock_cb_price_type_' . $po_cb_products_id];
                        if ($stock_price_type == '$') {
                            $unit_price = tep_db_prepare_input($input_array['unit_cb_price_' . $po_cb_products_id]);
                        } else if ($stock_price_type == '%') {
                            $unit_price = tep_db_prepare_input($input_array['sell_cb_price_' . $po_cb_products_id] * ( 1 - ($input_array['unit_cb_price_' . $po_cb_products_id] / 100)));
                        }
                        
                        // calculate subtotal amount
                        $subtotal = round(($unit_price * $input_array['dtu_cb_item_qty_' . $po_cb_products_id]), 8);
                        $subtotal_usd = round(($unit_price * $input_array['dtu_cb_item_qty_' . $po_cb_products_id] * $rate_to_usd), 8);
                        $po_subtotal_cb += $subtotal;
                        
                        // Update table: orders_top_up_withdrawal
                        foreach ($input_array['dtu_cb_items_topup_id_' . $po_cb_products_id] as $dtu_topup_id) {
                            $topup_id_cb[] = $dtu_topup_id;
                            $dtu_products_data = array(
                                'top_up_cb_deduction_po_id' => $insert_id,
                                'top_up_cb_deduction_status' => '1',
                                'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $dtu_products_data, 'update', "top_up_id = '" . (int) $dtu_topup_id . "'");

                            $dtu_item_data = array(
                                'top_up_id' => (int) $dtu_topup_id,
                                'purchase_orders_id' => (int) $insert_id,
                                'purchase_orders_ref_id' => $po_ref_id,
                                'top_up_cb_status' => '1',
                                'top_up_cb_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                                'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_CB_PURCHASE_ORDERS, $dtu_item_data);
                        }
                    }
                }
            }
            
            // Save Debit Note DTU
            $topup_id_dn = array();
            $po_subtotal_dn = 0;
            if (isset($input_array['include_dn']) && ($input_array['include_dn'] == '1' || $input_array['include_dn'] == 'on')) {
                
                foreach ($input_array['dtu_dn_items_prod_id'] as $po_dn_products_id) {
                    // Check for virtual products main product_id
                    $po_main_products_id = isset($input_array['dtu_dn_items_main_prod_id_' . $po_products_id]) ? $input_array['dtu_items_main_prod_id_' . $po_products_id] : '';
                    $qry_po_dn_products_id = $po_products_id;
                    if (tep_not_null($po_main_products_id)) {
                        $qry_po_dn_products_id = $po_main_products_id;
                    }

                    $products_select_sql = "SELECT p.products_id
                                            FROM " . TABLE_PRODUCTS . " p
                                            WHERE p.products_id = '" . (int) $qry_po_dn_products_id . "'";
                    $products_query = tep_db_query($products_select_sql);
                    if ($products_row = tep_db_fetch_array($products_query)) {
                        // Get Subtotal Debit Note products
                        $dn_subtotal = $input_array['subtotal_dn_' . $po_dn_products_id];
                        
                        // calculate subtotal amount
                        $subtotal = round($dn_subtotal, 8);
                        $subtotal_usd = round(($dn_subtotal * $rate_to_usd), 8);
                        $po_subtotal_dn += $subtotal;
                        
                        // Update table: orders_top_up_withdrawal
                        foreach ($input_array['dtu_dn_items_topup_id_' . $po_dn_products_id] as $dtu_topup_id) {
                            $topup_id_dn[] = $dtu_topup_id;
                            $dtu_products_data = array(
                                'top_up_cb_deduction_po_id' => $insert_id,
                                'top_up_cb_deduction_status' => '1',
                                'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $dtu_products_data, 'update', "top_up_id = '" . (int) $dtu_topup_id . "'");

                            $dtu_item_data = array(
                                'top_up_id' => (int) $dtu_topup_id,
                                'purchase_orders_id' => (int) $insert_id,
                                'purchase_orders_ref_id' => $po_ref_id,
                                'top_up_cb_status' => '2',
                                'top_up_cb_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                                'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_CB_PURCHASE_ORDERS, $dtu_item_data);
                        }
                    }
                }
            }
            
            $po_subtotal = $po_total_after_cb = round($po_total_amount, 8);
            
            // if Charge Back involve, calculate from sub-total
            if ($po_subtotal_cb > 0) {
                $po_total_after_cb = $po_subtotal - $po_subtotal_cb;
            }

            $po_total_after_tax = $po_total_after_cb;
            
            // if GST involve, calculate from sub-total-cb
            $po_gst_amt = 0;
            if (isset($input_array['include_gst']) && ($input_array['include_gst'] == '1' || $input_array['include_gst'] == 'on')) {
                $po_gst_amt = $po_total_after_cb * ($gst_value / 100);
                $po_total_after_tax = $po_total_after_cb + $po_gst_amt;
                $gst_show = true;
            }

            $po_total_after_rebate = $po_total_after_tax;
            
            if ($po_subtotal_dn > 0) {
                $po_total_after_rebate = $po_total_after_tax - $po_subtotal_dn;
            }

            $po_payable_total = $po_total_after_rebate;
            
            // add bank charges
            $po_bankcharge = 0;
            if (isset($input_array['dtu_pay_bankcharge']) && $input_array['dtu_pay_bankcharge'] > 0) {
                $po_bankcharge = $input_array['dtu_pay_bankcharge'] * (1 / $input_array['confirm_rate']);
                $po_payable_total = $po_total_after_rebate + $po_bankcharge;
            }
            
            // add adjustment
            $po_adjustment = 0;
            $po_adjustment_class = 'po_adjustment_plus';
            $po_adjustment_text = '+ Adjustment Amount : ';
            if (isset($input_array['dtu_pay_adjustment']) && $input_array['dtu_pay_adjustment'] > 0) {
                $po_adjustment = $input_array['dtu_pay_adjustment'] * (1 / $input_array['confirm_rate']);
                if ($input_array['adjustment_type'] == 1) {
                    $po_payable_total = $po_payable_total + $po_adjustment;
                } else {
                    $po_adjustment_class = 'po_adjustment_minus';
                    $po_adjustment_text = '- Adjustment Amount : ';
                    $po_payable_total = $po_payable_total - $po_adjustment;
                }
            }

            // Subtotal record
            $po_total_subtotal_data = array(
                'purchase_orders_id' => $insert_id,
                'title' => 'Sub-Total:',
                'usd_value' => round($po_subtotal * $rate_to_usd, 4),
                'value' => round($po_subtotal, 4),
                'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                'class' => 'po_subtotal',
                'sort_order' => '1',
            );
            tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_subtotal_data);
            if ($po_subtotal_cb > 0) {
                // Charge Back record
                $po_total_cb_data = array(
                    'purchase_orders_id' => $insert_id,
                    'title' => 'Charge Back Sub-Total:',
                    'usd_value' => round($po_subtotal_cb * $rate_to_usd, 4),
                    'value' => round($po_subtotal_cb, 4),
                    'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'class' => 'po_charge_back',
                    'sort_order' => '20',
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_cb_data);
            }
            if ($gst_show) {
                // GST record
                $po_total_gst_data = array(
                    'purchase_orders_id' => $insert_id,
                    'title' => sprintf('GST %s&percnt;:', $this->monetary_decimal_format($gst_value, '%01.1f')),
                    'usd_value' => round($po_gst_amt * $rate_to_usd, 4),
                    'value' => round($po_gst_amt, 4),
                    'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'class' => 'po_gst',
                    'sort_order' => '50',
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_gst_data);
            }
            if ($po_subtotal_dn > 0) {
                // Debit Note record
                $po_total_dn_data = array(
                    'purchase_orders_id' => $insert_id,
                    'title' => 'Debit Note Sub-Total:',
                    'usd_value' => round($po_subtotal_dn * $rate_to_usd, 4),
                    'value' => round($po_subtotal_dn, 4),
                    'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'class' => 'po_debit_note',
                    'sort_order' => '200',
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_dn_data);
            }
            if ($po_bankcharge > 0) {
                // Bank Charge record
                $po_total_bankcharge_data = array(
                    'purchase_orders_id' => $insert_id,
                    'title' => 'Bank Charges:',
                    'usd_value' => round($po_bankcharge * $rate_to_usd, 4),
                    'value' => round($po_bankcharge, 4),
                    'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'class' => 'po_bank_charge',
                    'sort_order' => '700',
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_bankcharge_data);
            }
            if ($po_adjustment > 0) {
                // Adjustment amt
                $po_total_adjustment_data = array(
                    'purchase_orders_id' => $insert_id,
                    'title' => 'Adjustment:',
                    'usd_value' => round($po_adjustment * $rate_to_usd, 4),
                    'value' => round($po_adjustment, 4),
                    'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'class' => $po_adjustment_class,
                    'sort_order' => '750',
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_adjustment_data);
            }
            // Total record
            $po_total_total_data = array(
                'purchase_orders_id' => $insert_id,
                'title' => 'Total:',
                'usd_value' => round($po_payable_total * $rate_to_usd, 4),
                'value' => round($po_payable_total, 4),
                'currency' => tep_db_prepare_input($input_array['dtu_currency']),
                'class' => 'po_total',
                'sort_order' => '900',
            );
            tep_db_perform(TABLE_PURCHASE_ORDERS_TOTAL, $po_total_total_data);

            // if GST involve, keep entered value
            if ($gst_show) {
                $po_update_paid_data = array(
                    'purchase_orders_gst_currency' => tep_db_prepare_input($input_array['dtu_currency']),
                    'purchase_orders_gst_value' => round($gst_value, 8),
                    'purchase_orders_gst_amount' => round($po_gst_amt, 8)
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS, $po_update_paid_data, 'update', "purchase_orders_id = '" . (int) $insert_id . "'");
            }

            // payment remark to be printed on printable PO
            $keep_decimal_places = $currencies->decimal_places;

            if ($gst_show || $po_bankcharge > 0 || $po_subtotal_cb > 0 || $po_subtotal_dn > 0) {
                $po_payment_remark .= "  Total Order : " . $currencies->format($po_subtotal, true, $payment_currency, $input_array['confirm_rate']) . "<br />";
            }
            if ($po_subtotal_cb > 0) {
                $po_payment_remark .= "- Charge Back : " . $currencies->format($po_subtotal_cb, true, $payment_currency, $input_array['confirm_rate']) . "<br />--------------------------------------------------<br />";
                $po_payment_remark .= "  Total Payment : " . $currencies->format($po_total_after_cb, true, $payment_currency, $input_array['confirm_rate']) . "<br />";
            }
            if ($gst_show) {
                $po_payment_remark .= "+ GST : " . $currencies->format($po_gst_amt, true, $payment_currency, $input_array['confirm_rate']) . "<br />--------------------------------------------------<br />";
                $po_payment_remark .= "  Total Payment to Make : " . $currencies->format($po_total_after_tax, true, $payment_currency, $input_array['confirm_rate']) . "<br />";
            }
            if ($po_subtotal_dn > 0) {
                $po_payment_remark .= "- Debit Note : " . $currencies->format($po_subtotal_dn, true, $payment_currency, $input_array['confirm_rate']) . "<br />";
            }
            if ($po_bankcharge > 0) {
                $po_payment_remark .= "+ Recipient Bank Charge : " . $currencies->format($input_array['dtu_pay_bankcharge'], false, $payment_currency) . "<br />";
            }
            if ($po_adjustment > 0) {
                $po_payment_remark .= $po_adjustment_text . $currencies->format($input_array['dtu_pay_adjustment'], false, $payment_currency) . "<br />";
            }
            $po_payment_remark .= "--------------------------------------------------" . "<br />" .
                    "  Total to Pay : " . $currencies->format($po_payable_total, true, $payment_currency, $input_array['confirm_rate']) . "<br />" .
                    "--------------------------------------------------" . "<br /><br />";
            if ($po_subtotal_cb > 0) {
                $po_payment_remark .= "Top-up ID in Charge Back: <br />";
                $i = 0;
                $topup_id_cb_count = count($topup_id_cb);
                foreach ($topup_id_cb as $top_up_id) {
                    $po_payment_remark .= $top_up_id;
                    if ($i < 3 && $i < ($topup_id_cb_count-1)) {
                        $po_payment_remark .= ", ";
                        $i++;
                    } else {
                        $po_payment_remark .= "<br />";
                        $i = 0;
                    }
                }
                $po_payment_remark .= "<br /><br />";
            }
            if ($po_subtotal_dn > 0) {
                $po_payment_remark .= "Top-up ID in Debit Note: <br />";
                $i = 0;
                $topup_id_dn_count = count($topup_id_dn);
                foreach ($topup_id_dn as $top_up_id) {
                    $po_payment_remark .= $top_up_id;
                    if ($i < 3 && $i < ($topup_id_dn_count-1)) {
                        $po_payment_remark .= ", ";
                        $i++;
                    } else {
                        $po_payment_remark .= "<br />";
                        $i = 0;
                    }
                }
                $po_payment_remark .= "<br /><br />";
            }
            if ($po_subtotal_cb > 0 || $po_subtotal_dn > 0) {
                $po_payment_remark .= "--------------------------------------------------<br /><br />";
            }

            $currencies->decimal_places = $keep_decimal_places;

            $po_status = '1'; // Pending status

            if (round($po_payable_total, 2) == 0) {
                // set PO to billed and paid, since fully covered by credit note
                $update_status_data = array(
                    'purchase_orders_billing_status' => '1',
                    'purchase_orders_paid_status' => '1'
                );
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_status_data, 'update', "purchase_orders_id = '" . (int) $insert_id . "'");
            }

            // Add PO Status Remark History
            $sql_data_array = array('purchase_orders_id' => $insert_id,
                'purchase_orders_status_id' => $po_status,
                'date_added' => 'now()',
                'comments' => $po_remark,
                'changed_by' => $_SESSION['login_email_address']);
            tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
            tep_update_purchase_orders_status_counter($sql_data_array);

            // Add Payment Remark with notifying Supplier flagged
            $po_payment_remark_array = array('purchase_orders_id' => $insert_id,
                'date_added' => 'now()',
                'comments' => tep_db_prepare_input($po_payment_remark),
                'changed_by' => $_SESSION['login_email_address'],
                'supplier_notified' => '1'
            );
            tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $po_payment_remark_array);
            tep_update_purchase_orders_status_counter($notify_array);

            // Add Notify Supplier Remark if exist
            if (isset($input_array['notify_supplier_remark']) && tep_not_null($input_array['notify_supplier_remark'])) {
                $notify_array = array('purchase_orders_id' => $insert_id,
                    'date_added' => 'now()',
                    'comments' => tep_db_prepare_input($input_array['notify_supplier_remark']),
                    'changed_by' => $_SESSION['login_email_address']);
                if (isset($input_array['notify_supplier']) && tep_not_null($input_array['notify_supplier'])) {
                    $notify_array['supplier_notified'] = '1';
                }
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $notify_array);
                tep_update_purchase_orders_status_counter($notify_array);
            }
            
            // unlocking the supplier after dtu po created
            $po_suppliers->unlock_po_supplier($dtu_supplier_id);

            // send notification email
            $new_po_email_contents = sprintf(EMAIL_PO_NEW_PO_NOTIFICATION_CONTENT, $insert_id, $po_ref_id, date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'] . ' (' . tep_get_admin_group_name($_SESSION['login_email_address']) . ')');

            $email_to_array = tep_parse_email_string(PO_NEW_PO_NOTIFICATION_EMAIL);
            for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'New PO Generated')), $new_po_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }

            $messageStack->add_session(sprintf(SUCCESS_PO_FORM_ADDED, '[' . $po_ref_id . ']'), 'success');
        }
    }
    
    function insert_new_cb($input_array, &$messageStack) {
        global $allow_create_dtu_permission, $po_suppliers;

        if ($allow_create_dtu_permission) {
            $dtu_publisher = new publishers($input_array['dtu_supplier']);
            $dtu_supplier_info = $dtu_publisher->get_publishers();
            $dtu_supplier_id = $dtu_supplier_info[$input_array['dtu_supplier']]['publishers_supplier_id'];
            
            foreach ($input_array['dtu_items_top_up_id'] as $dtu_topup_id) {
                // Update table: orders_top_up_withdrawal
                if ($input_array['dtu_status_select_'.$dtu_topup_id] == '11') {
                    $top_up_status_col = "top_up_withdrawal_status";
                    $top_up_date_col = "top_up_withdrawal_date";
                    $top_up_status_val = "11";
                } else if ($input_array['dtu_status_select_'.$dtu_topup_id] == '12') {
                    $top_up_status_col = "top_up_withdrawal_status";
                    $top_up_date_col = "top_up_withdrawal_date";
                    $top_up_status_val = "0";
                } else {
                    $top_up_status_col = "top_up_cb_status";
                    $top_up_date_col = "top_up_cb_date";
                    $top_up_status_val = $input_array['dtu_status_select_'.$dtu_topup_id];
                    
                    // Update table: orders_top_up_cb
                    $dtu_cb_data = array(
                        'top_up_id' => (int) $dtu_topup_id,
                        'top_up_cb_status' => tep_db_prepare_input($input_array['dtu_status_select_'.$dtu_topup_id]),
                        'top_up_cb_date' => tep_db_prepare_input(date("Y-m-d H:i:s")),
                        'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                    );
                    tep_db_perform(TABLE_ORDERS_TOP_UP_CB, $dtu_cb_data);
                }
                $dtu_item_data = array(
                    $top_up_status_col => tep_db_prepare_input($top_up_status_val),
                    $top_up_date_col => tep_db_prepare_input(date("Y-m-d H:i:s")),
                    'changed_by' => tep_db_prepare_input($input_array['dtu_contact_person'])
                );
                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $dtu_item_data, 'update', "top_up_id = '" . (int) $dtu_topup_id . "'");
            }
            
            // unlocking the supplier after dtu po created
            $po_suppliers->unlock_po_supplier($dtu_supplier_id);
            
            $messageStack->add_session(SUCCESS_CB_FORM_ADDED, 'success');
        }
    }

    function calculate_suggest_quantity($products_id, $multiplier = 7, $last_n_days = 30) {
        $avg_qty = $suggest_qty = 0;

        $avg_start_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") - $last_n_days, date("Y")));
        $avg_end_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") + 1, date("Y")));

        $avg_qty_select_sql = "	SELECT SUM(sales_activities_quantity) AS total_qty 
								FROM " . TABLE_SALES_ACTIVITIES . " 
								WHERE sales_activities_products_id = '" . tep_db_input($products_id) . "' 
								AND sales_activities_date >= '" . tep_db_input($avg_start_date) . "' 
								AND sales_activities_date <= '" . tep_db_input($avg_end_date) . "' 
								AND sales_activities_code = 'D' 
								AND sales_activities_operator = '+' 
								GROUP BY sales_activities_products_id";
        $avg_qty_result_sql = tep_db_query($avg_qty_select_sql);
        if ($avg_qty_row = tep_db_fetch_array($avg_qty_result_sql)) {
            $avg_qty = (($last_n_days > 0) ? ($avg_qty_row['total_qty'] / $last_n_days) : 0);
        }
        $suggest_qty = round($multiplier * $avg_qty, 0);

        return $suggest_qty;
    }

    function cancel_dtu($input_array, &$messageStack) {
        global $currencies;

        if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '1', '4')) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                $status_remark = 'DTU Payment Request cancelled with reason:<br>' . $input_array['cancel_comments'];

                // update DTU Request status to cancel
                $update_po_data = array('purchase_orders_status' => '4', 'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");
                
                // update DTU Items status to cancel
                $update_dtu_item = array(
                    'purchase_orders_id' => '0',
                    'top_up_withdrawal_ref_id' => '',
                    'top_up_withdrawal_status' => '7',
                    'top_up_withdrawal_temp_status' => '0',
                    'changed_by' => $_SESSION['login_email_address']
                );
                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");
                
                // update DTU CB/DN Items status to cancel
                $update_dtu_item = array(
                    'top_up_cb_deduction_po_id' => '0',
                    'top_up_cb_deduction_status' => '0'
                );
                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "top_up_cb_deduction_po_id = '" . (int) $input_array['po_id'] . "'");
                
                $update_dtu_item_po = array(
                    'top_up_withdrawal_status' => '7',
                    'changed_by' => $_SESSION['login_email_address']
                );
                tep_db_perform(TABLE_ORDERS_TOP_UP_PURCHASE_ORDERS, $update_dtu_item_po, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");

                // insert DTU Request status history
                $sql_data_array = array('purchase_orders_id' => $input_array['po_id'],
                    'purchase_orders_status_id' => '4',
                    'date_added' => 'now()',
                    'comments' => $status_remark,
                    'comments_type' => '1',
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);

                // re-update purchase orders tags
                tep_update_record_tags(FILENAME_DTU_PAYMENT, (int) $input_array['po_id'], 4, '');

                // email status completed notification
                tep_status_update_notification('PO', $input_array['po_id'], $_SESSION['login_email_address'], '1', '3', 'S', $status_remark);

                return true;
            } else {
                $messageStack->add_session(ERROR_DTU_CANCELLATION_FAILED, 'Error');
                return false;
            }
        } else {
            $messageStack->add_session(sprintf(ERROR_DTU_STATUS_CHANGE_FAILED, 'Pending', 'Cancel'), 'Error');
            return false;
        }
    }

    function approve_dtu($input_array, &$messageStack) {
        if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '1', '2')) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                $status_remark = 'DTU Payment Request approved to process';

                $update_po_data = array('purchase_orders_status' => '2', 'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");
                
                $update_dtu_item = array('top_up_withdrawal_status' => '3', 'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");
                tep_db_perform(TABLE_ORDERS_TOP_UP_PURCHASE_ORDERS, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");

                $sql_data_array = array('purchase_orders_id' => $input_array['po_id'],
                    'purchase_orders_status_id' => '2',
                    'date_added' => 'now()',
                    'comments' => $status_remark,
                    'comments_type' => '1',
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);

                // re-update purchase orders tags
                tep_update_record_tags(FILENAME_DTU_PAYMENT, (int) $input_array['po_id'], 2, '');

                // email status completed notification
                tep_status_update_notification('PO', $input_array['po_id'], $_SESSION['login_email_address'], '1', '2', 'S', $status_remark);

                $this->make_dtu_pre_payment($input_array['po_id'], $messageStack);

                return true;
            } else {
                $messageStack->add_session(ERROR_DTU_APPROVE_FAILED, 'Error');
                return false;
            }
        } else {
            $messageStack->add_session(sprintf(ERROR_DTU_STATUS_CHANGE_FAILED, 'Pending', 'Approve'), 'Error');
            return false;
        }
    }

    function complete_dtu($input_array, &$messageStack, &$po_status) {

        if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '2', '3')) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                // Force delivery to the DTU Payment Request
                $po_prod_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_unit_price, 
                                            pod.products_usd_unit_price, pod.products_quantity
                                        FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
                                        WHERE pod.purchase_orders_id='".tep_db_input($input_array['po_id'])."'";
                $po_prod_result_sql = tep_db_query($po_prod_select_sql);
                while ($po_prod_row = tep_db_fetch_array($po_prod_result_sql)) {
                    $new_good_deliver_qty = (int)$po_prod_row['products_quantity'];
                    $prod_unit_price = $po_prod_row['products_unit_price'];
                    $prod_usd_unit_price = $po_prod_row['products_usd_unit_price'];
                    $new_good_deliver_price = $new_good_deliver_qty * $prod_unit_price;
                    $new_good_deliver_usd_price = $new_good_deliver_qty * $prod_usd_unit_price;
                    
                    $force_delivered = array(
                        'products_delivered_quantity' => (int)$new_good_deliver_qty,
                        'products_good_delivered_quantity' => (int)$new_good_deliver_qty,
                        'products_good_delivered_price' => $new_good_deliver_price,
                        'products_good_delivered_usd_price' => $new_good_deliver_usd_price
                    );
                    
                    tep_db_perform(TABLE_PURCHASE_ORDERS_PRODUCTS, $force_delivered, 'update', "purchase_orders_products_id = '" . (int)$po_prod_row['purchase_orders_products_id'] . "'");
                }
                    
                $po_status = '3';
                $status_remark = 'DTU Payment Request completed';

                $update_po_data = array('purchase_orders_status' => $po_status, 'purchase_orders_date_finished' => 'now()', 'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");

                $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $input_array['po_id'] . "'";
                tep_db_query($unlock_orders_update_sql);

                $sql_data_array = array(
                    'purchase_orders_id' => $input_array['po_id'],
                    'purchase_orders_status_id' => $po_status,
                    'date_added' => 'now()',
                    'comments' => $status_remark,
                    'comments_type' => '1',
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);

                // add record for cronjob to auto credit WSC for supplier once payment term matured
                $this->load_po($input_array, $messageStack);
                if ($this->payment['payment_type'] == 'd' && $this->po_info['po_billing_status'] != '1') { // DTU Payment and not yet billed
                    
                    $mature_period = $this->payment['payment_days_pay_wsc'] * 24 * 60; // in minutes
                    tep_insert_cron_pending_credit('PO', $input_array['po_id'], $this->po_info['po_date'], $mature_period, '3');

                    $pay_req_date = date("Y-m-d H:i:s", strtotime('+' . $this->payment['payment_days_pay_wsc'] . ' day'));
                    // send notification email
                    $new_po_email_contents = sprintf(EMAIL_PO_WSC_AUTO_CREDITED_NOTIFICATION_CONTENT, $this->po_id, $this->po_info['po_ref_id'], date("Y-m-d H:i:s"), $this->payment['payment_days_pay_wsc'] . ' Days', $pay_req_date, date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'] . ' (' . tep_get_admin_group_name($_SESSION['login_email_address']) . ')');

                    $email_to_array = tep_parse_email_string(PO_WSC_AUTO_CREDIT_NOTIFICATION_EMAIL);
                    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                        tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'PO WSC Auto Credited')), $new_po_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }
                }

                // re-update purchase orders tags
                tep_update_record_tags(FILENAME_DTU_PAYMENT, (int) $input_array['po_id'], (int) $po_status, '');

                // email status completed notification
                tep_status_update_notification('PO', $input_array['po_id'], $_SESSION['login_email_address'], '2', '3', 'S', $status_remark);
                return true;
            } else {
                $messageStack->add_session(ERROR_DTU_COMPLETE_FAILED, 'Error');
                return false;
            }
        } else {
            $messageStack->add_session(sprintf(ERROR_DTU_STATUS_CHANGE_FAILED, 'Processing', 'Complete'), 'error');
            return false;
        }
    }

    function rollback_complete_dtu($input_array, &$messageStack) {

        if (tep_check_status_update_permission('PO', $_SESSION['login_groups_id'], '3', '2')) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                $po_status = '2';

                $update_po_data = array('purchase_orders_status' => $po_status, 'purchase_orders_date_finished' => 'NULL', 'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");

                $sql_data_array = array('purchase_orders_id' => $input_array['po_id'],
                    'purchase_orders_status_id' => $po_status,
                    'date_added' => 'now()',
                    'comments_type' => '1',
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);

                // re-update purchase orders tags
                tep_update_record_tags(FILENAME_DTU_PAYMENT, (int) $input_array['po_id'], (int) $po_status, '');

                return true;
            } else {
                $messageStack->add_session(ERROR_DTU_ROLLBACK_COMPLETE_FAILED, 'Error');
                return false;
            }
        } else {
            $messageStack->add_session(sprintf(ERROR_DTU_STATUS_CHANGE_FAILED, 'Completed', 'Processing'), 'error');
            return false;
        }
    }

    function verifying_po($input_array, &$messageStack) {
        global $allow_verify_dtu_permission;

        if ($allow_verify_dtu_permission) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                $update_po_data = array('purchase_orders_verify_mode' => ($input_array['v_mode'] ? $input_array['v_mode'] : '0'), 'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_po_data, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");

                $sql_data_array = array('purchase_orders_id' => $input_array['po_id'],
                    'date_added' => 'now()',
                    'comments' => 'Mark purchase order as ' . ($input_array['v_mode'] ? 'verified' : 'unverified'),
                    'comments_type' => '1',
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);
                return true;
            } else {
                $messageStack->add_session(ERROR_PO_VERIFY_FAILED, 'Error');
                return false;
            }
        }
    }

    function dtu_add_remark($input_array, &$messageStack) {
        global $allow_add_dtu_remark_permission;

        if ($allow_add_dtu_remark_permission) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                $notify_supplier = (isset($input_array['notify_supplier'])) ? '1' : '0';
                $set_as_important = (isset($input_array['set_important'])) ? '1' : '0';

                $sql_data_array = array('purchase_orders_id' => tep_db_prepare_input($input_array['po_id']),
                    'date_added' => 'now()',
                    'comments' => tep_db_prepare_input($input_array['crew_comments']),
                    'comments_type' => '1',
                    'set_as_po_remarks' => $set_as_important,
                    'supplier_notified' => $notify_supplier,
                    'changed_by' => $_SESSION['login_email_address']);
                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                tep_update_purchase_orders_status_counter($sql_data_array);
                return true;
            } else {
                $messageStack->add_session(ERROR_DTU_ADD_REMARK_FAILED, 'Error');
                return false;
            }
        }
    }

    function get_notify_supplier_remark_array($po_id = '') {
        $remark_str = array();

        if (!tep_not_null($po_id) && tep_not_null($this->po_id)) {
            $po_id = $this->po_id;

            $remark_select_sql = "	SELECT date_added, comments 
									FROM " . TABLE_PURCHASE_ORDERS_STATUS_HISTORY . "
									WHERE supplier_notified = '1'
                                        AND purchase_orders_id = '" . $po_id . "'
									ORDER BY purchase_orders_status_history_id";
            $remark_result_sql = tep_db_query($remark_select_sql);
            while ($remark_row = tep_db_fetch_array($remark_result_sql)) {
                $remark_str[] = $remark_row['comments'];
            }
        }

        return $remark_str;
    }

    function make_dtu_pre_payment($po_id, &$messageStack) {
        $load_po_success = false;

        if ($this->po_id != $po_id) {
            $load_po_success = $this->load_po(array('po_id' => $po_id), $messageStack);
        }

        if ($load_po_success) {
            // add record for cronjob to auto credit WSC for supplier for pre-payment term
            if ($this->payment['payment_type'] == 'd' && $this->po_info['po_billing_status'] != '1') { // pre-payment or cash term and not yet billed
                $mature_period = 0;
                tep_insert_cron_pending_credit('PO', $po_id, $this->po_info['po_date'], $mature_period, '2');

                $update_data = array('purchase_orders_billing_status' => '-1',
                    'last_modified' => 'now()');
                tep_db_perform(TABLE_PURCHASE_ORDERS, $update_data, 'update', "purchase_orders_id = '" . (int) $po_id . "'");

                $messageStack->add_session(SUCCESS_CREDIT_WSC, 'success');
                return true;
            } else {
                if ($this->payment['payment_type'] != 'd') {
                    $messageStack->add_session(ERROR_DTU_CREDIT_WSC_PAYMENT_TYPE_FAILED, 'Error');
                    return false;
                } else {
                    return true;
                }
            }
        } else {
            return false;
        }
    }

    function debit_dtu_pre_payment($po_id, &$messageStack) {
        global $po_suppliers;

        $load_po_success = false;

        if ($this->po_id != $po_id) {
            $load_po_success = $this->load_po(array('po_id' => $po_id), $messageStack);
        }

        if ($load_po_success) {
            if ($this->po_info['po_billing_status'] == '1') {
                $proceed_debit = false;
                if ($this->po_info['po_paid_status'] == '0') {
                    $proceed_debit = true;  // no payment made as yet.
                } else if ($this->po_info['po_paid_status'] == '1') {
                    $proceed_debit = false;  // payment has been made.
                } else if ($this->po_info['po_paid_status'] == '2') {
                    $proceed_debit = true;  // partial payment, possible only credit notes used.
                    $po_payment_select_sql = "SELECT spti.store_payments_id 
                                                FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti
                                                INNER JOIN " . TABLE_STORE_PAYMENTS . " AS sp
                                                    ON (spti.store_payments_id = sp.store_payments_id AND sp.store_payments_status <> '4')
                                                WHERE spti.store_payments_reimburse_id = '" . $this->po_id . "'
                                                AND spti.store_payments_reimburse_table = '" . TABLE_PURCHASE_ORDERS . "'";
                    $po_payment_result_sql = tep_db_query($po_payment_select_sql);
                    if (tep_db_num_rows($po_payment_result_sql) > 0) {
                        $proceed_debit = false;  // found some store payment records, payment has been made.
                    }
                }

                if ($proceed_debit) {
                    $payment_info = $this->get_supplier_account_book_info($this->supplier['supplier_id'], $this->payment['store_payment_account_book_id']);
                    $po_wsc_amount = round($po_suppliers->get_po_supplier_available_amount($this->supplier['supplier_id'], $payment_info['pm_currency']), 2);

                    // get last credited wsc amount to debit from history table
                    $wsc_to_debit = 0;
                    $to_debit_amt_select_sql = "SELECT store_account_history_credit_amount 
                                                FROM " . TABLE_STORE_ACCOUNT_HISTORY . " 
                                                WHERE store_account_history_trans_id='" . tep_db_input($po_id) . "' 
                                                    AND store_account_history_trans_type = 'PO'
                                                    AND store_account_history_account_type = 'POWSC'
                                                    AND store_account_history_currency = '" . tep_db_input($payment_info['pm_currency']) . "'
                                                    AND store_account_history_credit_amount IS NOT NULL
                                                ORDER BY store_account_history_id DESC
                                                LIMIT 1";
                    $to_debit_amt_result_sql = tep_db_query($to_debit_amt_select_sql);
                    if ($to_debit_amt_row = tep_db_fetch_array($to_debit_amt_result_sql)) {
                        //$wsc_to_debit = round($to_debit_amt_row['store_account_history_credit_amount'] * $this->po_info['confirmed_currency_value'], 2);		// take subtotal amount cause need to deduct credit note amount if not required full amount
                        $wsc_to_debit = $to_debit_amt_row['store_account_history_credit_amount'];
                    }

                    if ($wsc_to_debit <= $po_wsc_amount) {
                        $update_info = array(array('field_name' => 'store_account_po_wsc',
                                'operator' => '-',
                                'value' => $wsc_to_debit)
                        );
                        $new_store_acc_balance = $po_suppliers->_set_po_supplier_po_wsc_balance($this->supplier['supplier_id'], 'customers', $payment_info['pm_currency'], $update_info);

                        // Insert account statement history
                        $account_balance_history_data_array = array('user_id' => tep_db_prepare_input($this->supplier['supplier_id']),
                            'user_role' => 'customers',
                            'store_account_history_date' => 'now()',
                            'store_account_history_currency' => tep_db_prepare_input($payment_info['pm_currency']),
                            'store_account_history_account_type' => 'POWSC',
                            'store_account_history_debit_amount' => (double) $wsc_to_debit,
                            'store_account_history_credit_amount' => 'NULL',
                            'store_account_history_after_balance' => (double) $new_store_acc_balance,
                            'store_account_history_trans_type' => 'PO',
                            'store_account_history_trans_id' => $po_id,
                            'store_account_history_added_by' => $this->identity_email,
                            'store_account_history_added_by_role' => 'admin'
                        );
                        tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);

                        $update_data = array('purchase_orders_billing_status' => '0',
                            'last_modified' => 'now()');
                        tep_db_perform(TABLE_PURCHASE_ORDERS, $update_data, 'update', "purchase_orders_id = '" . (int) $po_id . "'");

                        $trans_history_data_array = array('purchase_orders_id' => $po_id,
                            'date_added' => 'now()',
                            'comments' => 'Debited the PO WSC',
                            'changed_by' => 'system'
                        );
                        tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $trans_history_data_array);
                        tep_update_purchase_orders_status_counter($trans_history_data_array);

                        $messageStack->add_session(SUCCESS_DEBIT_WSC, 'success');
                        return true;
                    } else {
                        $messageStack->add_session(ERROR_PO_DEBIT_WSC_FUND_FAILED, 'error');
                        return false;
                    }
                } else {
                    $messageStack->add_session(ERROR_PO_DEBIT_WSC_PAYMENT_FOUND_FAILED, 'error');
                    return false;
                }
            } else {
                $messageStack->add_session(ERROR_PO_DEBIT_WSC_PAYMENT_TYPE_FAILED, 'error');
                return false;
            }
        } else {
            return false;
        }
    }

    function refund_unpaid($input_array, &$messageStack) {
        global $allow_dtu_cancel_pending_receive_permission, $currencies, $po_suppliers;

        $close_po_status_as = '4'; // default to PO Canceled
        // assume PO is cancel in the first place, will be re-update to completed in later stage below if there are stock delivered
        $po_data_array = array('purchase_orders_status' => $close_po_status_as);
        tep_db_perform(TABLE_PURCHASE_ORDERS, $po_data_array, 'update', "purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'");

        $total_unpaid_refund = $total_delivered_amt_in_paid_currency = 0;
        $total_delivered_qty = 0;
        $total_gst_refund = $total_gst_delivered = 0;
        $refund_str = "The following items have been canceled:\n";

        if ($allow_dtu_cancel_pending_receive_permission) {
            if (isset($input_array['po_id']) && tep_not_null($input_array['po_id'])) {
                
                // Force delivery to the DTU Payment Request
                $po_prod_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_unit_price, 
                                            pod.products_usd_unit_price, pod.products_quantity
                                        FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
                                        WHERE pod.purchase_orders_id='".tep_db_input($input_array['po_id'])."'";
                $po_prod_result_sql = tep_db_query($po_prod_select_sql);
                while ($po_prod_row = tep_db_fetch_array($po_prod_result_sql)) {
                    $force_delivered = array(
                        'products_delivered_quantity' => 0,
                        'products_good_delivered_quantity' => 0,
                        'products_good_delivered_price' => 0.00000000,
                        'products_good_delivered_usd_price' => 0.00000000
                    );

                    tep_db_perform(TABLE_PURCHASE_ORDERS_PRODUCTS, $force_delivered, 'update', "purchase_orders_products_id = '" . (int)$po_prod_row['purchase_orders_products_id'] . "'");
                }
                
                $po_select_sql = "SELECT purchase_orders_ref_id, payment_type, payment_term, purchase_orders_issue_date, 
                                    purchase_orders_paid_status, purchase_orders_billing_status, supplier_id, 
                                    store_payment_account_book_id, confirmed_currency_value,purchase_orders_gst_currency, 
                                    purchase_orders_gst_value, purchase_orders_gst_amount, purchase_orders_gst_refunded 
                                FROM " . TABLE_PURCHASE_ORDERS . " 
                                WHERE purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'";
                $po_result_sql = tep_db_query($po_select_sql);
                if ($po_row = tep_db_fetch_array($po_result_sql)) {
                    if ($po_row['payment_type'] == 'd') { // DTU Payment PO are allow to perform cancel pending receive.
                        if ($po_row['purchase_orders_billing_status'] == 0 && $po_row['purchase_orders_paid_status'] != '1') {
                            // If unbilled and not fully paid
                            $acct_book_array = $this->get_supplier_account_book_info($po_row['supplier_id'], $po_row['store_payment_account_book_id']);

                            $po_prods_select_sql = "SELECT purchase_orders_products_id, products_unit_price, products_usd_unit_price, 
                                                    products_quantity, products_delivered_quantity, products_name, 
                                                    products_good_delivered_quantity, products_good_delivered_price 
                                                    FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                                    WHERE purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'";
                            $po_prods_result_sql = tep_db_query($po_prods_select_sql);
                            while ($po_prods_row = tep_db_fetch_array($po_prods_result_sql)) {                                
                                $balance_qty = $po_prods_row['products_quantity'] - $po_prods_row['products_delivered_quantity'];
                                $delivered_amt = $po_prods_row['products_good_delivered_price'];
                                $total_delivered_qty += $po_prods_row['products_good_delivered_quantity'];

                                if ($balance_qty > 0) {
                                    $refund_price = round($balance_qty * $po_prods_row['products_unit_price'], 8);
                                    $refund_usd_price = round($balance_qty * $po_prods_row['products_usd_unit_price'], 8);
                                    $total_unpaid_refund += $refund_price;

                                    // if involve GST, calculated the GST amount to be refunded
                                    if ($po_row['purchase_orders_gst_value'] > 0) {
                                        $total_gst_refund = $total_gst_refund + ($refund_price * ($po_row['purchase_orders_gst_value'] / 100));
                                    }

                                    $refund_str .= "&raquo; " . $po_prods_row["products_name"] . "\tx " . $balance_qty . "\n";

                                    $update_data = array('products_delivered_quantity' => $po_prods_row['products_delivered_quantity'] + $balance_qty,
                                        'products_canceled_quantity' => $balance_qty,
                                        'products_canceled_price' => $refund_price,
                                        'products_canceled_usd_price' => $refund_usd_price);
                                    tep_db_perform(TABLE_PURCHASE_ORDERS_PRODUCTS, $update_data, 'update', "purchase_orders_products_id = '" . (int) $po_prods_row['purchase_orders_products_id'] . "'");
                                }

                                if ($delivered_amt > 0) {
                                    $total_delivered_amt_in_paid_currency += $delivered_amt * $po_row['confirmed_currency_value'];

                                    // if involve GST, calculated the GST amount to be refunded
                                    if ($po_row['purchase_orders_gst_value'] > 0) {
                                        $total_gst_delivered = $total_gst_delivered + ($delivered_amt * ($po_row['purchase_orders_gst_value'] / 100));
                                    }
                                }
                            }

                            // include GST refunded amount
                            if ($total_gst_refund > 0) {
                                $gst_refunded_in_paid_currency = ($total_gst_refund * $po_row['confirmed_currency_value']);
                                $total_unpaid_refund = $total_unpaid_refund + $gst_refunded_in_paid_currency;
                            }

                            // include GST delivered amount
                            if ($total_gst_delivered > 0) {
                                $gst_delivered_in_paid_currency = ($total_gst_delivered * $po_row['confirmed_currency_value']);
                                $total_delivered_amt_in_paid_currency = $total_delivered_amt_in_paid_currency + $gst_delivered_in_paid_currency;
                            }

                            if ($total_delivered_amt_in_paid_currency > 0) {
                                if (($po_row['payment_type'] == 'd') && $po_row['purchase_orders_billing_status'] != '1') { // DTU Payment PO and not yet billed
                                    // IMPORTANT: have to update the PO status before insert cron task due to pre-payment PO payable amount calculation logic
                                    $close_po_status_as = '3'; // some items has been received, need to pay for it
                                    $po_data_array = array('purchase_orders_status' => $close_po_status_as);
                                    tep_db_perform(TABLE_PURCHASE_ORDERS, $po_data_array, 'update', "purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'");

                                    if ($po_row['payment_type'] == 'd' || $po_row['payment_type'] == 'c') {
                                        $po_row['payment_days_pay_wsc'] = 0;
                                    }
                                    $mature_period = $po_row['payment_days_pay_wsc'] * 24 * 60; // in minutes
                                    tep_insert_cron_pending_credit('PO', $input_array['po_id'], $po_row['purchase_orders_issue_date'], $mature_period, '3');

                                    $pay_req_date = date("Y-m-d H:i:s", strtotime('+' . $po_row['payment_days_pay_wsc'] . ' day'));
                                    // send notification email
                                    $new_po_email_contents = sprintf(EMAIL_PO_WSC_AUTO_CREDITED_NOTIFICATION_CONTENT, $input_array['po_id'], $po_row['purchase_orders_ref_id'], date("Y-m-d H:i:s"), $po_row['payment_days_pay_wsc'] . ' Days', $pay_req_date, date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'] . ' (' . tep_get_admin_group_name($_SESSION['login_email_address']) . ')');

                                    $email_to_array = tep_parse_email_string(PO_WSC_AUTO_CREDIT_NOTIFICATION_EMAIL);
                                    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                        tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'PO WSC Auto Credited')), $new_po_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                    }
                                }
                            } else if ($total_delivered_qty > 0) {
                                $close_po_status_as = '3'; // some items has been received, need to pay for it
                                $po_data_array = array('purchase_orders_status' => $close_po_status_as);
                                tep_db_perform(TABLE_PURCHASE_ORDERS, $po_data_array, 'update', "purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'");
                            }

                            if ($total_unpaid_refund > 0) {
                                $po_data_array = array(
                                    'purchase_orders_date_finished' => 'now()',
                                    'last_modified' => 'now()',
                                    'purchase_orders_locked_by' => 'NULL',
                                    'purchase_orders_locked_from_ip' => 'NULL',
                                    'purchase_orders_locked_datetime' => 'NULL'
                                );
                                // GST refunded
                                if ($total_gst_refund > 0) {
                                    $po_data_array['purchase_orders_gst_refunded'] = $po_row['purchase_orders_gst_refunded'] + $total_gst_refund;
                                    $refund_str .= "\n" . "Inclusive GST refunded : " . $currencies->format($gst_refunded_in_paid_currency, false, $acct_book_array['pm_currency']);
                                }
                                // bank charges refunded
                                if (isset($input_array['bankcharges_refund']) && $input_array['bankcharges_refund'] > 0) {
                                    $po_data_array['purchase_orders_bankcharges_refunded'] = $input_array['bankcharges_refund'];
                                    $refund_str .= "\n" . "Bank charges refunded : " . $currencies->format($input_array['bankcharges_refund'], false, $acct_book_array['pm_currency']);
                                }
                                tep_db_perform(TABLE_PURCHASE_ORDERS, $po_data_array, 'update', "purchase_orders_id = '" . tep_db_input($input_array['po_id']) . "'");
                                // update DTU Items status to cancel
                                $update_dtu_item = array(
                                    'purchase_orders_id' => '0',
                                    'top_up_withdrawal_ref_id' => '',
                                    'top_up_withdrawal_status' => '7',
                                    'top_up_withdrawal_temp_status' => '0',
                                    'changed_by' => $_SESSION['login_email_address']
                                );
                                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $input_array['po_id'] . "'");
                                // update DTU CB Items status
                                $update_dtu_item = array(
                                    'top_up_cb_deduction_po_id' => '0',
                                    'top_up_cb_deduction_status' => '0'
                                );
                                tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "top_up_cb_deduction_po_id = '" . (int) $input_array['po_id'] . "'");

                                $refund_str .= ((isset($input_array['cancel_comments']) && tep_not_null($input_array['cancel_comments'])) ? "\n\n" . tep_db_prepare_input($input_array['cancel_comments']) : '');
                                $sql_data_array = array('purchase_orders_id' => tep_db_prepare_input($input_array['po_id']),
                                    'purchase_orders_status_id' => $close_po_status_as,
                                    'date_added' => 'now()',
                                    'comments' => $refund_str,
                                    'comments_type' => '1',
                                    'changed_by' => $_SESSION['login_email_address']);
                                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                                tep_update_purchase_orders_status_counter($sql_data_array);

                                // re-update purchase orders tags
                                tep_update_record_tags(FILENAME_DTU_PAYMENT, (int) $input_array['po_id'], (int) $close_po_status_as, '');
                            }

                            $unlock_orders_update_sql = "UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_locked_by = NULL, purchase_orders_locked_from_ip = NULL, purchase_orders_locked_datetime = NULL WHERE purchase_orders_id = '" . $input_array['po_id'] . "'";
                            tep_db_query($unlock_orders_update_sql);
                        } else {
                            $messageStack->add_session(ERROR_DTU_CANCEL_PENDING_RECEIVE_PAID_STATUS_FAILED, 'Error');
                            return false;
                        }
                    } else {
                        $messageStack->add_session(ERROR_DTU_CANCEL_PENDING_RECEIVE_FAILED, 'Error');
                        return false;
                    }
                }
            } else {
                $messageStack->add_session(ERROR_DTU_CANCEL_PENDING_RECEIVE_FAILED, 'Error');
                return false;
            }
        }
    }

    function get_po_total_payable_amount($po_id = '', $full_amount = true) {
        $total_payable_amount = 0;
        $total_charge_back = 0;
        $total_debit_note = 0;
        $total_adjustment_plus = 0;
        $total_adjustment_minus = 0;

        if (tep_not_null($po_id)) {
            $po_select_sql = "	SELECT payment_type, currency, confirmed_currency_value, purchase_orders_paid_amount, purchase_orders_paid_currency, 
                                    purchase_orders_bankcharges_included, purchase_orders_bankcharges_refunded, 
                                    purchase_orders_gst_currency, purchase_orders_gst_value, purchase_orders_gst_amount, 
                                    purchase_orders_status 
                                FROM " . TABLE_PURCHASE_ORDERS . " 
                                WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
            $po_result_sql = tep_db_query($po_select_sql);
            $po_row = tep_db_fetch_array($po_result_sql);
            $payment_type = $po_row['payment_type'];
            $pay_rate = $po_row['confirmed_currency_value'];
            $paid_amount = $po_row['purchase_orders_paid_amount'];

            if ($payment_type == 'c' || $payment_type == 'd') {
                $has_prod_delivered = false;
                $raw_delivered_amount = 0;
                $raw_charge_back = 0;
                $raw_debit_note = 0;

                if ($po_row['purchase_orders_status'] == '3') { // when PO is completed, get delivered amount instead
                    $po_products_select_sql = "SELECT purchase_orders_products_id, products_good_delivered_quantity, products_good_delivered_price 
                                                FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                                WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
                    $po_products_result_sql = tep_db_query($po_products_select_sql);

                    while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
                        if ($po_products_row['products_good_delivered_quantity'] > 0) {
                            $has_prod_delivered = true;
                            $total_payable_amount += $po_products_row['products_good_delivered_price'];
                        }
                    }
                }

                if ($has_prod_delivered == true) {
                    $raw_delivered_amount = $total_payable_amount;
                    $total_payable_amount = $total_payable_amount * $pay_rate;
                } else {
                    $po_total_select_sql = "SELECT value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . tep_db_input($po_id) . "' AND class='po_subtotal'";
                    $po_total_result_sql = tep_db_query($po_total_select_sql);
                    if ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                        $total_payable_amount = $po_total_row['value'] * $pay_rate;  // take subtotal amount cause need to deduct credit note amount if not required full amount
                        $raw_delivered_amount = $po_total_row['value'];
                    }
                }
                
                // Get Charge Back & Debit Note value for DTU Payment Request
                if ($payment_type == 'd') {
                    $po_total_select_sql = "SELECT value, class FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . tep_db_input($po_id) . "'";
                    $po_total_result_sql = tep_db_query($po_total_select_sql);
                    while ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                        switch ($po_total_row['class']) {
                            case 'po_charge_back':
                                $raw_charge_back = $po_total_row['value'];
                                $total_charge_back = $po_total_row['value'] * $pay_rate;
                                break;
                            case 'po_debit_note':
                                $raw_debit_note = $po_total_row['value'];
                                $total_debit_note = $po_total_row['value'] * $pay_rate;
                                break;
                            case 'po_adjustment_plus':
                                $total_adjustment_plus = $po_total_row['value'] * $pay_rate;
                                break;
                            case 'po_adjustment_minus':
                                $total_adjustment_minus = $po_total_row['value'] * $pay_rate;
                                break;
                        }
                    }
                }
                
                // deduct charge back
                if ($total_charge_back > 0) {
                    $total_payable_amount = $total_payable_amount - $total_charge_back;
                    $raw_delivered_amount = $raw_delivered_amount - $raw_charge_back;
                }

                // add GST amount in paid currency
                if ($po_row['purchase_orders_gst_value'] > 0) {
                    $total_payable_amount = $total_payable_amount + (($raw_delivered_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $pay_rate);
                }
            } else if ($payment_type == 't' || $payment_type == 'g') {
                $raw_delivered_amount = 0;
                $po_products_select_sql = "SELECT purchase_orders_products_id, products_quantity, products_unit_price, products_good_delivered_price 
                                            FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
                                            WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
                $po_products_result_sql = tep_db_query($po_products_select_sql);
                while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
                    // for term payment, use delivered price
                    $total_payable_amount += $po_products_row['products_good_delivered_price'] * $pay_rate;
                    $raw_delivered_amount += $po_products_row['products_good_delivered_price'];
                }

                // add GST amount in paid currency
                if ($po_row['purchase_orders_gst_value'] > 0) {
                    $total_payable_amount = $total_payable_amount + (($raw_delivered_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $pay_rate);
                }
            }
            
            // deduct debit note
            if ($total_debit_note > 0) {
                $total_payable_amount = $total_payable_amount - $total_debit_note;
            }

            // add bank charges, if any
            if ($po_row['purchase_orders_bankcharges_included'] > 0 || $po_row['purchase_orders_bankcharges_refunded'] > 0) {
                $total_payable_amount = $total_payable_amount + ($po_row['purchase_orders_bankcharges_included'] - $po_row['purchase_orders_bankcharges_refunded']);
            }
            
            // Adjustment value +
            if ($total_adjustment_plus > 0) {
                $total_payable_amount = $total_payable_amount + $total_adjustment_plus;
            }

            // Adjustment value -
            if ($total_adjustment_minus > 0) {
                $total_payable_amount = $total_payable_amount - $total_adjustment_minus;
            }

            if (!$full_amount) {
                // deduct previously paid + credit note used amount
                $total_payable_amount = $total_payable_amount - $paid_amount;
            }
        }

        return $total_payable_amount;
    }

    function get_supplier_account_book_info($user_id, $acct_book_id = '') {
        global $currencies;
        $acct_book_arr = array();

        if (tep_not_null($user_id) && tep_not_null($acct_book_id)) {
            $acct_book_select_sql = "SELECT spab.payment_methods_id, pm.payment_methods_send_currency, pm.payment_methods_send_status, pm.payment_methods_send_mode_name 
										FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
										INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
											ON (pm.payment_methods_id = spab.payment_methods_id)
										WHERE spab.store_payment_account_book_id = '" . tep_db_input($acct_book_id) . "'
										AND spab.user_id = '" . tep_db_input($user_id) . "'
										AND spab.user_role = 'customers'";
            $acct_book_result_sql = tep_db_query($acct_book_select_sql);
            if ($acct_book_row = tep_db_fetch_array($acct_book_result_sql)) {
                $acct_book_arr = array('pm_id' => $acct_book_row['payment_methods_id'],
                    'pm_status' => $acct_book_row['payment_methods_send_status'],
                    'pm_name' => $acct_book_row['payment_methods_send_mode_name'],
                    'pm_currency' => $currencies->get_code_by_id($acct_book_row['payment_methods_send_currency']));
            }
        }

        return $acct_book_arr;
    }

    function get_po_ref_num_by_id($po_id) {
        $po_ref_id = '';

        if (tep_not_null($po_id)) {
            $po_ref_select_sql = "SELECT purchase_orders_ref_id FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
            $po_ref_result_sql = tep_db_query($po_ref_select_sql);
            if ($po_ref_row = tep_db_fetch_array($po_ref_result_sql)) {
                $po_ref_id = $po_ref_row['purchase_orders_ref_id'];
            }
        }

        return $po_ref_id;
    }
    
    function get_po_payment_type($po_id) {
        $po_payment_type = '';

        if (tep_not_null($po_id)) {
            $po_payment_type_select_sql = "SELECT payment_type FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($po_id) . "'";
            $po_payment_type_result_sql = tep_db_query($po_payment_type_select_sql);
            if ($po_payment_type_row = tep_db_fetch_array($po_payment_type_result_sql)) {
                $po_payment_type = $po_payment_type_row['payment_type'];
            }
        }

        return $po_payment_type;
    }

    function update_dtu_print_status($po_id) {
        if (tep_not_null($po_id)) {
            $po_data_array = array('purchase_orders_last_printed_by' => $_SESSION['login_id'],
                'purchase_orders_last_printed_from_ip' => tep_get_ip_address(),
                'purchase_orders_last_printed' => 'now()');
            tep_db_perform(TABLE_PURCHASE_ORDERS, $po_data_array, 'update', "purchase_orders_id = '" . tep_db_input($po_id) . "'");
        }
    }

    function monetary_decimal_format($value, $format = '%01.4f') {
        $trimmed_value = rtrim($value, '\0'); // remove trailing zeroes
        $final_value = sprintf($format, $trimmed_value); // format to minimum 2 decimal point

        if (strlen($trimmed_value) > strlen($final_value)) {
            // if string length of trimmed version longer than final version, take the trimmed version result
            return $trimmed_value;
        } else {
            // if string lenght of trimmed version shorter than final version, take final version result
            return $final_value;
        }
    }
    
    function is_dtu_payment($po_id) {
        
        $total_po = 0;
        if (tep_not_null($po_id)) {
            $po_ref_select_sql = "SELECT count(purchase_orders_id) AS total_po FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($po_id) . "' AND purchase_orders_type=1";
            $po_ref_result_sql = tep_db_query($po_ref_select_sql);
            if ($po_ref_row = tep_db_fetch_array($po_ref_result_sql)) {
                $total_po = $po_ref_row['total_po'];
            }
        }

        return $total_po;
    }
    
    function getProductSellPrice($products_id, $currencies, $sell_curr) {
        
        $price_array = $currencies->get_product_prices_info($products_id);
        if ($sell_curr == DEFAULT_CURRENCY) {
            if ($price_array['base_cur'] == DEFAULT_CURRENCY) {
                $selling_price = $price_array['price'];
            } else {
                if (count($price_array['defined_price']) && isset($price_array['defined_price'][DEFAULT_CURRENCY])) {
                    $selling_price = $price_array['defined_price'][DEFAULT_CURRENCY];
                } else {
                    $base_rate = 1;
                    if ($currencies->currencies[$price_array['base_cur']]['value'] > 0) {
                        $base_rate = 1 / $currencies->currencies[$price_array['base_cur']]['value'];
                    }
                    $selling_price = $base_rate * $price_array['price'];
                }
            }
        } else {
            $price_array = $currencies->get_product_prices_info($products_id);
            if ($price_array['base_cur'] == $sell_curr) {
                $selling_price = $price_array['price'];
            } else {
                if (count($price_array['defined_price']) && isset($price_array['defined_price'][$sell_curr])) {
                    $selling_price = $price_array['defined_price'][$sell_curr];
                } else {
                    $selling_price = $currencies->advance_currency_conversion($price_array['price'], $price_array['base_cur'], $sell_curr, false);
                }
            }
        }

        return $selling_price;
    }

    function getVirtualProductSellPrice($cur_code, $price, $currencies, $sell_curr) {
        if ($sell_curr == DEFAULT_CURRENCY) {
            if ($cur_code == DEFAULT_CURRENCY) {
                $selling_price = $price;
            } else {
                $base_rate = 1;
                if ($currencies->currencies[$cur_code]['value'] > 0) {
                    $base_rate = 1 / $currencies->currencies[$cur_code]['value'];
                }
                $selling_price = $base_rate * $price;
            }
        } else {
            if ($cur_code == $sell_curr) {
                $selling_price = $price;
            } else {
                $selling_price = $currencies->advance_currency_conversion($price, $cur_code, $sell_curr, false);
            }
        }
        return $selling_price;
    }

}
?>