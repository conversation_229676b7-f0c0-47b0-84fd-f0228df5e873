<?
class export_product {
	var $mode, $fp, $current_path, $filename;
	var $main_cat_id;
	var $delivery_minutes, $delivery_method;
	var $game_title;
	var $save_file_location;
	var $export_xml_data;
	var $export_html_data;
	var $tpl;
	var $game_qty_conversion;
	var $game_product_division;
	var $currency;

	function export_product($main_cat_id, $tpl) {
		switch ($tpl) {
			case '1':
				$this->main_cat_id = $main_cat_id;
				$this->set_game_title($main_cat_id);
				$this->tpl = $tpl;

				break;
			case '2':
				$this->main_cat_id = $main_cat_id;
				$this->tpl = $tpl;

				break;
			case '3':
				$this->main_cat_id = $main_cat_id;
				$this->tpl = $tpl;
				break;
			case '4':
				$this->tpl = $tpl;
				break;
		}

		$this->export_xml_data = '';
		$this->export_html_data = '';
		$this->delivery_minutes = "1440";
		$this->delivery_method = "EMAIL";
		$this->currency = 'CNY'; // default currency

		$this->game_qty_conversion = array ('1802' => 1000000,
											'1877' => 1000000,
											'1958' => 1000,
											'1660' => 1000000,
											'1809' => 1000000,
											'1401' => 1000,
											'1834' => 1000000,
											'2919' => 1000000,
											'1773' => 1000000,
											'1816' => 1000000,
											'1736' => 1000000,
											'3051' => 1000000,
											'2923' => 1000000,
											'2845' => 1000000,
											'3167' => 1000000,
											'1735' => 1000000,
											'3232' => 1000000,
											'4204' => 1000,
											'4422' => 1000,
											'4542' => 1000000,
											'1964' => 1000000,
											'1695' => 1000000,
											'1454' => 1000,
											'4354' => 1000000,
											'3082' => 1000000,
											'3083' => 1000000,
											'3248' => 1000000,
											'3406' => 1000,
											'1807' => 1000000,
											'3821' => 1000000,
											'3844' => 1000000,
											'3832' => 1000000,
											'2244' => 1000000,
											'4784' => 1000000,
											'4786' => 1000000,
											'3407' => 1000,
											'4357' => 1000);

		$this->game_product_division = array (	'1802' => 50000000,
												'1877' => 5000000,
												'1964' => 10000000,
												'1735' => 5000000,
												'1695' => 5000000,
												'1454' => 100000,
												'1958' => 500000,
												'1660' => 10000000,
												'1496' => 1000,
												'3050' => 100,
												'1809' => 1000000,
												'1401' => 100000,
												'1982' => 500000,
												'1834' => 10000000,
												'3083' => 5000000,
												'3082' => 10000000,
												'2919' => 100000000,
												'1807' => 10000000,
												'2244' => 5000000,
												'1629' => 10000000,
												'1816' => 10000000,
												'2217' => 10000000,
												'2844' => 1000,
												'1736' => 10000000,
												'2845' => 1000000,
												'2923' => 1000000,
												'2921' => 10000000,
												'2060' => 100);
	}

	function set_save_location($dir) {
		$this->save_file_location = $dir;
	}

	function set_game_title() {
		switch($this->main_cat_id) {
			case '194':
				$this->game_title = 'World of Warcraft (US)';
				break;
			case '195':
				$this->game_title = 'World of Warcraft (EU)';
				break;
		}
	}

	function get_data_xml($languages_id, $messageStack='') {

		$line_break = "\n";
		$export_xml_product = '';

		$this->template($languages_id, $export_data_competitor);

		$this->export_xml_data = $export_data_competitor . $line_break . $line_break . $export_xml_product;

		if (is_object($messageStack)) {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'error');
		}
	}

	function get_data_html($languages_id, $messageStack='') {
		$this->template($languages_id, $export_data_competitor);

		$this->export_html_data = $export_data_competitor;

		if (is_object($messageStack)) {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'error');
		}
	}

	function template($languages_id,&$export_data_competitor){
		global $memcache_obj;

		$this->load_definition();
		$line_break = "\n";

		switch ($this->tpl) {
			case '1':
				$cat_array = array();

				tep_get_subcategories($cat_array, $this->main_cat_id, '');
				$this->cat_array = $cat_array;

				if ($cat_array) {
					$stock_query_select_sql = "	SELECT p.products_id, p.products_cat_path, p.products_quantity, p.products_status, p.products_price, p.products_base_currency, pd.products_name, pc.categories_id
													FROM " . TABLE_PRODUCTS . " AS p
						  							LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
						  								ON (p.products_id=pd.products_id AND pd.language_id = '" . $languages_id . "')
						  							LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
						  								ON p.products_id=pc.products_id
						  							WHERE p.products_status = 1
						  								AND p.products_quantity>0
						  								AND p.products_skip_inventory=0
						  								AND p.products_bundle=''
						  								AND p.products_bundle_dynamic=''
						  								AND pc.categories_id IN (" . implode(", ", $this->cat_array) . ")
						  								AND pc.products_is_link=0";

					$stock__result_sql = tep_db_query($stock_query_select_sql);

					//xml content
					$export_data_competitor = XML_HEADING_TITLE. $line_break;
					$export_data_competitor .= XML_SPARTER_BEGIN. $line_break;
					$export_data_competitor .= XML_XMLNS_XSI. $line_break;
				 	$export_data_competitor .= XML_XSI. $line_break;
					$export_data_competitor .= XML_CONTENT. $line_break;
					$export_data_competitor .= $line_break;

					$export_data_competitor .= '<'.XML_OFFER;
					$export_data_competitor .= XML_SERVICE.' = "'.$this->game_title;
					$export_data_competitor .= '">'.$line_break;

					while ($stock_row = tep_db_fetch_array($stock__result_sql)) {
						$product_name = $stock_row['products_name'];
						$product_name = preg_replace('/([^a-z]+)/is', '', $product_name);

						$products_quantity = $stock_row['products_quantity'];
						$currencyCode = $stock_row['products_base_currency'];
						$unitPrice = $stock_row['products_price'];

						$cat_path = split_dep(" > ", $stock_row['products_cat_path']);
						$cat_path_rev = array_reverse($cat_path);

						$xml_realm = $cat_path_rev[1];
						$xml_faction = $cat_path_rev[0];

						$export_data_competitor .= XML_OFFER_S.$line_break;

						$export_data_competitor .= XML_SERVER_S.$xml_realm;
						$export_data_competitor .= XML_SERVER_E.$line_break;

						$export_data_competitor .= XML_MARKET_S.$xml_faction;
						$export_data_competitor .= XML_MARKET_E. $line_break;

						$export_data_competitor .= XML_ITEM_S.$product_name;
						$export_data_competitor .= XML_ITEM_E. $line_break;

						$export_data_competitor .= XML_AVAILABLEQUANTITY_S.$products_quantity;
						$export_data_competitor .= XML_AVAILABLEQUANTITY_E. $line_break;

						$export_data_competitor .= XML_DELIVERYMINUTES_S . $this->delivery_minutes;
						$export_data_competitor .= XML_DELIVERYMINUTES_E . $line_break;

						$export_data_competitor .= XML_DELIVERYMETHOD_S . $this->delivery_method;
						$export_data_competitor .= XML_DELIVERYMETHOD_E. $line_break;

						$export_data_competitor .= XML_CURRENCYCODE_S.$currencyCode;
						$export_data_competitor .= XML_CURRENCYCODE_E. $line_break;

						$export_data_competitor .= XML_UNITPRICE_S.$unitPrice;
						$export_data_competitor .= XML_UNITPRICE_E. $line_break;

						$export_data_competitor .= XML_OFFER_E. $line_break;
					}

					$export_data_competitor .= '</'.XML_OFFER.'>';
					$export_data_competitor .= XML_END. $line_break;
				}
				break;

			case '2':
				// set application wide parameters
				$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
				while ($configuration = tep_db_fetch_array($configuration_query)) {
					define($configuration['cfgKey'], $configuration['cfgValue']);
				}

				$shopdesc = "Buy Game Currencies, Power Leveling, CD Keys / Time Cards / Game Points";

				$telephone = "+603 9222 6654";
				$address = "Room 813, Hollywood Plaza, 610 Nathan Road, Kowloon, Hong Kong SAR";
				$country = "Hong Kong";
				$ebay_profile = "OffGamers";

				//xml content
				$export_data_competitor = XML_MMO_HEADING_TITLE. $line_break;
				$export_data_competitor .= XML_MMO_SHOP_S. $line_break;
				$export_data_competitor .= XML_MMO_SHOPINFO_S. $line_break;
				$export_data_competitor .= XML_MMO_SHOPNAME_S.XML_CDATA_S. STORE_NAME .XML_CDATA_E;
				$export_data_competitor .= XML_MMO_SHOPNAME_E. $line_break;
				$export_data_competitor .= XML_MMO_FREETEXT_S.XML_CDATA_S. $shopdesc .XML_CDATA_E;
				$export_data_competitor .= XML_MMO_FREETEXT_E. $line_break;
				$export_data_competitor .= XML_MMO_COMPANYNAME_S.XML_CDATA_S. STORE_NAME .XML_CDATA_E;
				$export_data_competitor .= XML_MMO_COMPANYNAME_E. $line_break;
				$export_data_competitor .= XML_MMO_SHOPURL_S. XML_CDATA_S . HTTP_CATALOG_SERVER . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_SHOPURL_E. $line_break;
				$export_data_competitor .= XML_MMO_CONTACTEMAIL_S. XML_CDATA_S .STORE_OWNER_EMAIL_ADDRESS . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_CONTACTEMAIL_E. $line_break;
				$export_data_competitor .= XML_MMO_TELEPHONE_S. XML_CDATA_S . $telephone . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_TELEPHONE_E. $line_break;
				$export_data_competitor .= XML_MMO_SHOPADDR_S. XML_CDATA_S . $address . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_SHOPADDR_E. $line_break;
				$export_data_competitor .= XML_MMO_COUNTRY_S. XML_CDATA_S . $country . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_COUNTRY_E. $line_break;

				/* Payment Info */
				$pm_title_array = tep_get_enabled_pm_title();
				$count_pm_title_array = sizeof($pm_title_array);
				$pm_title_cnt = 1;

				$export_data_competitor .= XML_MMO_PAYMENT_OPT_S. $line_break;
				foreach ($pm_title_array as $pm_title) {
					$export_data_competitor .= XML_MMO_PAYMENTINFO_S . XML_CDATA_S . $pm_title['text'] . XML_CDATA_E . XML_MMO_PAYMENTINFO_E . $line_break;
				}
				$export_data_competitor .= XML_MMO_PAYMENT_OPT_E. $line_break;

				/*
				$export_data_competitor .= XML_MMO_PAYMENT_OPT_S. XML_CDATA_S;
				foreach ($pm_title_array as $pm_title) {
					$export_data_competitor .= $pm_title['text'];
					if ($pm_title_cnt != $count_pm_title_array) { $export_data_competitor .= ', '; }
					$pm_title_cnt ++;
				}
				$export_data_competitor .= XML_CDATA_E . XML_MMO_PAYMENT_OPT_E. $line_break;
				*/
				/* Payment Info EOF */

				$export_data_competitor .= XML_MMO_PAYPALADDR_S. XML_CDATA_S . MODULE_PAYMENT_PAYPALIPN_ID . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_PAYPALADDR_E. $line_break;
				$export_data_competitor .= XML_MMO_EBAYNAME_S. XML_CDATA_S . $ebay_profile . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_EBAYNAME_E. $line_break;
				$export_data_competitor .= XML_MMO_CURRENCY_S. XML_CDATA_S . DEFAULT_CURRENCY . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_CURRENCY_E. $line_break;

				$export_data_competitor .= XML_MMO_LIVE_HELP_S. XML_CDATA_S . 'yes' . XML_CDATA_E;
				$export_data_competitor .= XML_MMO_LIVE_HELP_E. $line_break;

				$export_data_competitor .= XML_MMO_SHOPINFO_E.$line_break;

				foreach ($this->main_cat_id as $count_id => $cat_id) {
					$game_product_division_ON = false;

					if ($count_id > 0)
					{
						$cat_array = array();
						tep_get_subcategories($cat_array, $cat_id['id'], '');

						$this->cat_array = $cat_array;

						if ($cat_array) {
							$stock_query_select_sql = "		SELECT c.categories_id, c.categories_url_alias, c.categories_parent_path, p.products_id, p.products_cat_path, p.products_quantity, p.products_status, p.products_price, p.products_base_currency, pd.products_name, pc.categories_id
															FROM " . TABLE_PRODUCTS . " AS p
								  							LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
								  								ON (p.products_id=pd.products_id AND pd.language_id = '" . $languages_id . "')
								  							LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
								  								ON p.products_id=pc.products_id
								  							LEFT JOIN " . TABLE_CATEGORIES . " AS c
								  								ON pc.categories_id=c.categories_id
								  							LEFT JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
								  								ON c.categories_id=cg.categories_id
								  							WHERE p.products_status = 1
								  								AND cg.groups_id IN (0,2)
								  								AND p.products_skip_inventory=0
								  								AND p.products_bundle=''
								  								AND p.products_bundle_dynamic=''
								  								AND p.custom_products_type_id=0
								  								AND pc.categories_id IN (" . implode(", ", $cat_array) . ")
								  								AND pc.products_is_link=0";
							$stock__result_sql = tep_db_query($stock_query_select_sql);
							$num = tep_db_num_rows($stock__result_sql);

							if ($num != 0) {
								$store_data_array_game = array();
								$store_data_array_server = array();

								if (isset($this->game_product_division[$cat_id['id']])) {
									$game_product_division_ON = true;
								}

								$game_cnt = $server_cnt = 0;
								while ($stock_row = tep_db_fetch_array($stock__result_sql)) {
									$store_data_array_faction = array();
									$parent_id_arr = array();
									$parent_id_available = array();

									$cat_path = split_dep(" > ", $stock_row['products_cat_path']);
									$cat_path_last = end($cat_path);

									$product_name_split = split_dep(" ",$stock_row['products_name']);
									$product_name = end($product_name_split);
									$product_name = preg_replace(array('/\(/', '/\)/'), "", $product_name);//filtering the currency

									tep_get_parent_categories($parent_id_arr, $stock_row['categories_id'], true);
									$splice_parent_id_arr = $parent_id_arr;
									unset($splice_parent_id_arr[count($splice_parent_id_arr)-1]);	// Remove last element
									$count_splice_parent_id_arr = count($splice_parent_id_arr);

									for ($parent_id_cnt = 0; $parent_id_cnt < $count_splice_parent_id_arr; $parent_id_cnt++) {
										$main_cat_id = $parent_id_arr[$parent_id_cnt + 1];
										$current_cat_id = $splice_parent_id_arr[$parent_id_cnt];
										if (!$this->cat_id_display_available($main_cat_id, $current_cat_id, 1, 0)) {
											$parent_id_available[] = $current_cat_id;
										}
									}

									if (sizeof($parent_id_available) > 0) {
										$parent_id_available = array_reverse($parent_id_available);
										$cat_id_path = $main_cat_id."-".implode('-', $parent_id_available)."-".$stock_row['categories_id'];
									} else {
										$cat_id_path = $main_cat_id."-".$stock_row['categories_id'];
									}

									$url = HTTP_CATALOG_SERVER.$stock_row['categories_url_alias']."-c-".$cat_id_path.".ogm?tpl=0";

									$game_name = $cat_id['text'];

									$category_parent_path_arr = array();
									$product_path_arr = array();
									$start_to_get_the_rest_of_the_value = false;

									$cat_path_arr = array();
									$product_path = tep_get_product_path($stock_row['products_id']);
									$product_path_arr = explode(">", $product_path);

									$category_parent_path = substr($stock_row['categories_parent_path'], 1, -1);
									$category_parent_path_arr = explode("_", $category_parent_path);
									$category_parent_path_arr[count($category_parent_path_arr)] = $stock_row['categories_id'];

									$total_of_category_id = sizeof ($category_parent_path_arr);

									for ($category_parent_path_cnt = 0; $category_parent_path_cnt < $total_of_category_id; $category_parent_path_cnt++) {
										if ($start_to_get_the_rest_of_the_value) {
											//if ($this->validate_category_path_id($category_parent_path_arr[$category_parent_path_cnt])) {
												$cat_path_arr[] = $category_parent_path_arr[$category_parent_path_cnt];
											//}
										} else {
											if ($this->get_started_category_path_id($category_parent_path_arr[$category_parent_path_cnt])) {
												$start_to_get_the_rest_of_the_value = true;
											}
										}
									}

									$cat_path_count = count($cat_path_arr);
									switch ($cat_path_count) {
										case 1:
											$game_server = trim($this->get_category_name($cat_path_arr[0]));
											$game_faction = "Default";
											break;
										default:
											$game_server = trim($this->get_category_name($cat_path_arr[$cat_path_count-2]));
											$game_faction = trim($this->get_category_name($cat_path_arr[$cat_path_count-1]));
											break;
									}

									$product_query_select_sql = "	SELECT pb.bundle_id
																	FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb
																	INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
																		ON pc.products_id = pb.bundle_id
																		AND pc.categories_id = '" . $stock_row['categories_id'] . "'
																		AND pc.products_is_link = '0'
																	WHERE pb.subproduct_id = '".$stock_row['products_id']."'";
									$product_result_sql = tep_db_query($product_query_select_sql);
									$product_result_num = tep_db_num_rows($product_result_sql);

									$buddle_id_array = array();

									if ($product_result_num != 0) {
										$store_product_info_arr = array();
										$lowest_qty = array();
										$new_division_price = 0;
										$mmo_mininum_qty = 0;

										if (!in_array($game_server,$store_data_array_server)) {
											if (!empty($store_data_array_server)) {
												if (end($store_data_array_server)!=$game_server) {
													$export_data_competitor .= XML_MMO_SERVER_E.$line_break;
												}
											}

											if (!in_array($game_name,$store_data_array_game)) {
												$store_data_array_game[sizeof($store_data_array_game)] = $game_name ;
												$game_name = $this->safe_quote_string($game_name);
												$export_data_competitor .= XML_MMO_GAME_S."name=\"".$game_name."\" currency=\"".$product_name."\">".$line_break;
												$game_cnt ++;
											}

											$store_data_array_server[sizeof($store_data_array_server)] = $game_server ;

											$export_data_competitor .= XML_MMO_SERVER_S."name=\"".htmlspecialchars($game_server)."\">".$line_break;
											$server_cnt ++;
										}

										if ($game_faction=="Default") {
											$export_data_competitor .= XML_MMO_FACTION_S."name=\"".$game_faction."\" url=\"".$url."\">".$line_break;
										} else {
											if(!in_array($game_faction,$store_data_array_faction)) {
												$store_data_array_faction[sizeof($store_data_array_faction)] = $game_faction ;
												$export_data_competitor .= XML_MMO_FACTION_S."name=\"".$game_faction."\" url=\"".$url."\">".$line_break;
											}
										}

										while ($product_row = tep_db_fetch_array($product_result_sql)) {
											$buddle_id_array[sizeof($buddle_id_array)] = $product_row['bundle_id'];
										}

										$find_product_display_sql = "	SELECT pb.subproduct_qty, p.products_cat_path, p.products_price, pd.products_name
																		FROM " . TABLE_PRODUCTS . " AS p
																		LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																			ON (pd.products_id = p.products_id AND pd.language_id = 1)
																		LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
																			ON (p.products_id = pb.bundle_id)
																		WHERE p.products_status = 1
																		AND p.products_skip_inventory = 0
																		AND p.products_bundle = 'yes'
																		AND p.products_bundle_dynamic = ''
																		AND p.products_id IN (".implode(",",$buddle_id_array).")";
										$find_product_display_result_sql = tep_db_query($find_product_display_sql);

										while ($product_display_row = tep_db_fetch_array($find_product_display_result_sql)) {
											$qty = $this->convert_to_real_qty($cat_id['id'], $product_display_row['subproduct_qty']);

											$export_data_competitor .= XML_MMO_PRICE_S."quantity=\"".$qty."\"><![CDATA[".round($product_display_row['products_price'],2);
											$export_data_competitor .= "]]>".XML_MMO_PRICE_E. $line_break;

											if ($game_product_division_ON) {
												$store_product_info_arr[$stock_row['categories_id']]['qty'][$qty]['price'] = round($product_display_row['products_price'],2);
											}
										}

										// Create new row of qty and price - (If our product qty didnt match with the mmobux)
										if ($game_product_division_ON) {
											asort($store_product_info_arr[$stock_row['categories_id']]['qty']);
											$lowest_qty = array_keys($store_product_info_arr[$stock_row['categories_id']]['qty']);
											$mmo_mininum_qty = $this->game_product_division[$cat_id['id']];
											if ($lowest_qty[0] > $mmo_mininum_qty) {
												$new_division_price = ($mmo_mininum_qty / $lowest_qty[0]) * $store_product_info_arr[$stock_row['categories_id']]['qty'][$lowest_qty[0]]['price'];
												$export_data_competitor .= XML_MMO_PRICE_S."quantity=\"".$mmo_mininum_qty."\"><![CDATA[".round($new_division_price,2);
												$export_data_competitor .= "]]>".XML_MMO_PRICE_E. $line_break;
											}
										}
									} else {
										break 1;
									}
									$export_data_competitor .= XML_MMO_FACTION_E.$line_break;
									unset($store_data_array_faction);
								}//END While

								if ($server_cnt > 0) {
									$export_data_competitor .= XML_MMO_SERVER_E.$line_break;
								}
								if ($game_cnt > 0) {
									$export_data_competitor .= XML_MMO_GAME_E.$line_break;
								}
							}
							unset($store_data_array_game);
						}//END IF($cat_array)
					}//END Foreach
				}//END $count_id

				$export_data_competitor .= XML_MMO_SHOP_E.$line_break;
				unset($store_data_array_game);
				unset($store_data_array_server);
				unset($store_data_array_faction);
			break;

			case '3':
				require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
				require_once(DIR_WS_CLASSES . 'currencies.php');
				include_once(DIR_WS_CLASSES . 'vip_order.php');

				$currencies = new currencies();

				$buyback_game_array = $this->main_cat_id;

				for ($game_cnt=0; $game_cnt < count($buyback_game_array); $game_cnt++) {
					$result_array = array();

					$game_id = $buyback_game_array[$game_cnt]['id'];
					$game_title = $buyback_game_array[$game_cnt]['text'];

					if ($game_id) {
						$buybackSupplierObj = new buyback_supplier($game_id);
						$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
						$buybackSupplierObj->calculate_offer_price();
						$buybackSupplierObj->get_buyback_product_server_full_info();

						foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
							$product_display_name = tep_display_category_path($products_arr['cat_path']." > ".$products_arr['product_name'], $products_arr['cat_id'], 'catalog', false);

							if ((float)$products_arr['avg_offer_price'] > 0) {
								if ((int)$products_arr['is_buyback'] && $products_arr['max_qty'] > 0) {
									if ($game_id == '1496') {
										$products_arr['avg_offer_price'] = $products_arr['avg_offer_price'] * 100;
										$products_arr['max_qty'] = $products_arr['max_qty'] / 100;
									}

									$unit_price = number_format($currencies->do_raw_conversion($products_arr['avg_offer_price'], true, $this->currency), $currencies->decimal_places, $currencies->currencies[$this->currency]['decimal_point'], $currencies->currencies[$this->currency]['thousands_point']);
									$result_array[] = $product_display_name.','.$unit_price.','.$products_arr['max_qty'];

									switch ($languages_id) {
										case '1':
											$instant_trade = 'Instant trade';
											break;
										case '2':
											$instant_trade = '&#21450;&#26102;&#20132;&#26131;';
											break;
										default:
											$instant_trade = 'Instant trade';
											break;

									}

									$export_data_competitor .= '<tr>';
									$export_data_competitor .= '<td width="20%">'.$game_title.'</td>';
									$export_data_competitor .= '<td width="20%">'.$product_display_name.'</td>';
									$export_data_competitor .= '<td width="20%">'.$unit_price.'</td>';
									$export_data_competitor .= '<td width="20%">'.$products_arr['min_qty'].'/'.$products_arr['max_qty'].'</td>';
									$export_data_competitor .= '<td width="20%">'.$instant_trade.'</td>';
									$export_data_competitor .= '</tr>';
								}
							}
						}
					}

					if (count($result_array) > 0) {
						$cache_key = '/buyback_price/mode/1/game_id/'.$game_id;
						$memcache_obj->delete($cache_key, 0);
						$memcache_obj->store($cache_key, implode('|', $result_array), 1800); // Cache for 30 mins
					}
				}
				break;

			case '4':
				// set application wide parameters
				$conf_select_sql = 'SELECT configuration_key AS cfgKey, configuration_value AS cfgValue
									FROM ' . TABLE_CONFIGURATION . '
									WHERE configuration_key IN (\'STORE_NAME\', \'STORE_OWNER_EMAIL_ADDRESS\', \'AWS_S3_ENABLED\')';
				$configuration_query = tep_db_query($conf_select_sql);

				while ($configuration = tep_db_fetch_array($configuration_query)) {
					define($configuration['cfgKey'], $configuration['cfgValue']);
				}

				$shopdesc = 'Buy Game Currencies, Power Leveling, CD Keys / Time Cards / Game Points';
				$telephone = '+603 9222 6654';
				$address = 'Room 813, Hollywood Plaza, 610 Nathan Road, Kowloon, Hong Kong SAR';
				$country = 'Hong Kong';
				$policy = 'Any form of CD Key, Time Card, Expansion, Game Points, Pre-paid Card, Trial/Beta/Item Code viewed from your account history is neither refundable nor exchangeable.';

				//xml content
				$export_data_competitor = XML_CDKEY_HEADING_TITLE. $line_break;
				$export_data_competitor .= XML_CDKEY_SHOP_S. $line_break;
				$export_data_competitor .= '<lastBuildDate>'.date("D, j M Y H:i:s O").'</lastBuildDate>';
				$export_data_competitor .= XML_CDKEY_SHOPINFO_S. $line_break;
				$export_data_competitor .= XML_CDKEY_SHOPNAME_S.XML_CDATA_CDKEY_S. STORE_NAME .XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_SHOPNAME_E. $line_break;
				$export_data_competitor .= XML_CDKEY_FREETEXT_S.XML_CDATA_CDKEY_S. $shopdesc .XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_FREETEXT_E. $line_break;
				$export_data_competitor .= XML_CDKEY_COMPANYNAME_S.XML_CDATA_CDKEY_S. STORE_NAME .XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_COMPANYNAME_E. $line_break;
				$export_data_competitor .= XML_CDKEY_SHOPURL_S. XML_CDATA_CDKEY_S . HTTP_CATALOG_SERVER . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_SHOPURL_E. $line_break;
				$export_data_competitor .= XML_CDKEY_CONTACTEMAIL_S. XML_CDATA_CDKEY_S .STORE_OWNER_EMAIL_ADDRESS . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_CONTACTEMAIL_E. $line_break;
				$export_data_competitor .= XML_CDKEY_TELEPHONE_S. XML_CDATA_CDKEY_S . $telephone . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_TELEPHONE_E. $line_break;
				$export_data_competitor .= XML_CDKEY_SHOPADDR_S. XML_CDATA_CDKEY_S . $address . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_SHOPADDR_E. $line_break;
				$export_data_competitor .= XML_CDKEY_COUNTRY_S. XML_CDATA_CDKEY_S . $country . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_COUNTRY_E. $line_break;
				$export_data_competitor .= XML_CDKEY_POLICY_S. XML_CDATA_CDKEY_S . $policy . XML_CDATA_CDKEY_E;
				$export_data_competitor .= XML_CDKEY_POLICY_E. $line_break;
				$export_data_competitor .= XML_CDKEY_SHOPINFO_E.$line_break;

				$categories_games_select_sql = "SELECT categories_structures_value
												FROM " . TABLE_CATEGORIES_STRUCTURES . "
												WHERE categories_structures_key = 'games'";
				$categories_games_result_sql = tep_db_query($categories_games_select_sql);
				$categories_games_row = tep_db_fetch_array($categories_games_result_sql);

				if (tep_not_null($categories_games_row['categories_structures_value'])) {
					$categories_games_array = explode(',', $categories_games_row['categories_structures_value']);
				} else {
					$categories_games_array = array();
				}

				if (count($categories_games_array)) {
					$categories_games_str = implode("','", $categories_games_array);
					$selected_categories_select_sql = "	SELECT c.categories_id, c.categories_parent_path, cd.categories_name
														FROM " . TABLE_CATEGORIES. " AS c
														INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION. " AS cd
															ON c.categories_id = cd.categories_id
														WHERE c.categories_id IN ('" . $categories_games_str . "')
															AND cd.language_id='1'
															AND c.categories_status = '1'
														ORDER BY c.sort_order, cd.categories_name";
					$selected_categories_result_sql = tep_db_query($selected_categories_select_sql);

					while ($selected_categories_row = tep_db_fetch_array($selected_categories_result_sql)) {
						$parent_path_str = $selected_categories_row['categories_parent_path'];

						if (tep_not_null($parent_path_str)) {
							$parent_path_str .= $selected_categories_row['categories_id'] . '_';
						} else {
							if ($selected_categories_row['categories_id'] > 0) {
								$parent_path_str = '_'.$selected_categories_row['categories_id'] . '_';
							}
						}

						$parent_path_str = preg_replace("/_/u", "\_", $parent_path_str);

						$cdkey_info_sql = "	SELECT p.products_id, p.products_base_currency, p.products_price, p.products_url_alias,
												pd.products_name, pd.products_image, pd.products_description
											FROM " . TABLE_PRODUCTS . " AS p
		  									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
		  										ON (p.products_id=pd.products_id AND pd.language_id = '1')
		  									INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
		  										ON p.products_id=pc.products_id
		  									INNER JOIN " . TABLE_CATEGORIES . " AS c
		  										ON pc.categories_id=c.categories_id
		  									WHERE c.categories_status = '1'
		  										AND p.products_status = '1'
		  										AND p.products_display = '1'
												AND c.categories_parent_path LIKE '".$parent_path_str."%'
												AND c.custom_products_type_id = '2'";
						$cdkey_info_result = tep_db_query($cdkey_info_sql);
						$cdkey_num = tep_db_num_rows($cdkey_info_result);

						if ($cdkey_num != 0) {
							//game name
							$export_data_competitor .= XML_CDKEY_GAME_NAME_S. $line_break;

							$export_data_competitor .= XML_CDKEY_G_NAME_S . XML_CDATA_CDKEY_S . strip_tags($selected_categories_row['categories_name']) . XML_CDATA_CDKEY_E;
							$export_data_competitor .= XML_CDKEY_G_NAME_E . $line_break;

							$export_data_competitor .= XML_CDKEY_PRODUCT_ID_S. XML_CDATA_CDKEY_S .$selected_categories_row['categories_id']. XML_CDATA_CDKEY_E;
							$export_data_competitor .= XML_CDKEY_PRODUCT_ID_E.$line_break;

							while ($cdkey_info_row = tep_db_fetch_array($cdkey_info_result)) {
								//-cp- is for the specific product page whereas -c- is for product type page
								$url = HTTP_CATALOG_SERVER.$cdkey_info_row['products_url_alias']."-cp-".$cdkey_info_row['products_id'].".ogm";

								list($product_desc, $useless_part) = explode('Return Policy', stripcslashes(strip_tags($cdkey_info_row['products_description'])));
								list($product_desc, $useless_part) = explode('Website:', $product_desc);

								if (tep_not_null($cdkey_info_row['products_image'])) { //small img url
									$img_url = DIR_WS_CATALOG_IMAGES."products/".$cdkey_info_row['products_image'];
								}

								$export_data_competitor .= XML_CDKEY_PRODUCT_S. $line_break;

								// Product ID
								$export_data_competitor .= XML_CDKEY_PRODUCT_ID_S. XML_CDATA_CDKEY_S .$cdkey_info_row['products_id']. XML_CDATA_CDKEY_E;
								$export_data_competitor .= XML_CDKEY_PRODUCT_ID_E.$line_break;

								// Price currency
								if ((tep_not_null($cdkey_info_row['products_base_currency'])) && (tep_not_null($cdkey_info_row['products_price']))) {
									$export_data_competitor .= XML_CDKEY_PRICE_CURRENCY_S. "currency=\"" . $cdkey_info_row['products_base_currency'] . "\">". XML_CDATA_CDKEY_S .$cdkey_info_row['products_price']. XML_CDATA_CDKEY_E;
									$export_data_competitor .= XML_CDKEY_PRICE_CURRENCY_E.$line_break;
								}

								// Product name
								if (tep_not_null($cdkey_info_row['products_name'])) {
									$export_data_competitor .= XML_CDKEY_PRODUCT_NAME_S. XML_CDATA_CDKEY_S . strip_tags($cdkey_info_row['products_name']) . XML_CDATA_CDKEY_E;
									$export_data_competitor .= XML_CDKEY_PRODUCT_NAME_E.$line_break;
								}

								// Product link
								if (tep_not_null($url)) {
									$export_data_competitor .= XML_CDKEY_PRODUCT_LINK_S. XML_CDATA_CDKEY_S . $url . XML_CDATA_CDKEY_E;
									$export_data_competitor .= XML_CDKEY_PRODUCT_LINK_E.$line_break;
								}

								// Product image
								if (tep_not_null($cdkey_info_row['products_image'])) {
									$export_data_competitor .= XML_CDKEY_PRODUCT_IMG_URL_S. XML_CDATA_CDKEY_S . $img_url . XML_CDATA_CDKEY_E;
									$export_data_competitor .= XML_CDKEY_PRODUCT_IMG_URL_E.$line_break;
								}

								if (tep_not_null($cdkey_info_row['products_description'])) {
									$export_data_competitor .= XML_CDKEY_PRODUCT_DESCRIPTION_S. XML_CDATA_CDKEY_S . $product_desc . XML_CDATA_CDKEY_E;
									$export_data_competitor .= XML_CDKEY_PRODUCT_DESCRIPTION_E.$line_break;
								}
								$export_data_competitor .= XML_CDKEY_PRODUCT_E.$line_break;
							}

							$export_data_competitor .= XML_CDKEY_GAME_NAME_E . $line_break;
						}
					}
				}
				$export_data_competitor .= XML_CDKEY_SHOP_E.$line_break;
			break;
		}
	}

	function download($mode=1) {
		global $memcache_obj;

		switch($mode) {
			case '0':	// Save to specified location
				if ($this->tpl == '1') {
					$filename = 'sparter_'.$this->main_cat_id.'.xml';
				} else {
					$filename = 'mmobux.xml';
				}

				require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
				$aws_obj = new ogm_amazon_ws();

				$aws_obj->set_bucket_key('BUCKET_DATA');
				$aws_obj->set_filepath('xml_price/');
				$aws_obj->set_storage('STORAGE_STANDARD');
				$aws_enabled = $aws_obj->is_aws_s3_enabled();

				$encoded_data = base64_encode($this->export_xml_data);

				if ($aws_enabled) {
					$aws_obj->set_filename($filename);
					$aws_obj->set_file_content($encoded_data);
					$create_status = $aws_obj->save_file();

					if ($create_status == 0) {
						report_error('Function[download][' . $filename . '] Failed to write');
					}
				} else {
					if (!$handle = fopen($this->save_file_location . '/' . $filename, 'w')) {
				         exit;
				    }

				    // Write to our opened file.
				    if (fwrite($handle, $encoded_data) === FALSE) {
				    	fclose($handle);
				        exit;
				    }

					fclose($handle);
				}

				unset($aws_obj);
				break;
			case '1':
				$filename = 'sparter_'.$main_cat_id.'.xml';
				$mime_type = 'text/x-xml';
				// Download
		        header('Content-Type: ' . $mime_type);

		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT'.'');
		        // IE need specific headers

		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            Header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
		        echo $export_xml_data;

		    	break;
		    case '2':
		    	$cache_key = '/buyback_price/mode/1/game_id/all';
				$memcache_obj->delete($cache_key, 0);
				$memcache_obj->store($cache_key, $this->export_html_data, 1800); // Cache for 30 mins

		    	break;
		    case '3':
				$filename = 'cdkey.xml';

				require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
				$aws_obj = new ogm_amazon_ws();

				$aws_obj->set_bucket_key('BUCKET_DATA');
				$aws_obj->set_filepath('xml_price/');
				$aws_obj->set_storage('STORAGE_STANDARD');
				$aws_enabled = $aws_obj->is_aws_s3_enabled();

				$encoded_data = base64_encode($this->export_xml_data);

				if ($aws_enabled) {
					$aws_obj->set_filename($filename);
					$aws_obj->set_file_content($encoded_data);
					$create_status = $aws_obj->save_file();

					if ($create_status == 0) {
						report_error('Function[download][' . $filename . '] Failed to write');
					}
				} else {
					if (!$handle = fopen($this->save_file_location . '/' . $filename, 'w')) {
				         exit;
				    }

				    if (fwrite($handle, $encoded_data) === FALSE) {
				    	fclose($handle);
				        exit;
				    }
					fclose($handle);
				}

				unset($aws_obj);
				break;
	    }
	}

	function load_definition() {
		//sparter
		define ('XML_HEADING_TITLE','<?xml version="1.0" encoding="UTF-8"?>');
		define ('XML_SPARTER_BEGIN','<sparter xmlns="http://sparter.com"');
		define ('XML_XMLNS_XSI','xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"');
		define ('XML_XSI','xsi:schemaLocation="http://sparter.com sparter.xsd">');
		define ('XML_CONTENT','<!--  contents -->');
		define ('XML_OFFER','offerUpload ');
		define ('XML_SERVICE','service');
		define ('XML_OFFER_S','<offer>');
		define ('XML_OFFER_E','</offer>');
		define ('XML_SERVER_S','<server>');
		define ('XML_SERVER_E','</server>');

		define ('XML_MARKET_S','<market>');
		define ('XML_MARKET_E','</market>');
		define ('XML_ITEM_S','<item>');
		define ('XML_ITEM_E','</item>');
		define ('XML_AVAILABLEQUANTITY_S','<availableQuantity>');
		define ('XML_AVAILABLEQUANTITY_E','</availableQuantity>');

		define ('XML_DELIVERYMINUTES_S','<deliveryMinutes>');
		define ('XML_DELIVERYMINUTES_E','</deliveryMinutes>');
		define ('XML_DELIVERYMETHOD_S','<deliveryMethod>');
		define ('XML_DELIVERYMETHOD_E','</deliveryMethod>');
		define ('XML_CURRENCYCODE_S','<currencyCode>');
		define ('XML_CURRENCYCODE_E','</currencyCode>');
		define ('XML_UNITPRICE_S','<unitPrice>');
		define ('XML_UNITPRICE_E','</unitPrice>');
		define ('XML_END','</sparter>');

		//mmobux
		define ('XML_MMO_HEADING_TITLE','<?xml version="1.0" encoding="UTF-8"?>');
		define ('XML_MMO_SHOP_S','<shop>');
		define ('XML_MMO_SHOP_E','</shop>');
		define ('XML_MMO_SHOPINFO_S','<shop_info>');
		define ('XML_MMO_SHOPINFO_E','</shop_info>');
		define ('XML_MMO_SHOPNAME_S','<shop_name>');
		define ('XML_MMO_SHOPNAME_E','</shop_name>');
		define ('XML_MMO_FREETEXT_S','<free_text>');
		define ('XML_MMO_FREETEXT_E','</free_text>');
		define ('XML_MMO_COMPANYNAME_S','<company_name>');
		define ('XML_MMO_COMPANYNAME_E','</company_name>');
		define ('XML_MMO_SHOPURL_S','<shop_url>');
		define ('XML_MMO_SHOPURL_E','</shop_url>');
		define ('XML_MMO_CONTACTEMAIL_S','<contact_email>');
		define ('XML_MMO_CONTACTEMAIL_E','</contact_email>');
		define ('XML_MMO_TELEPHONE_S','<telephone>');
		define ('XML_MMO_TELEPHONE_E','</telephone>');
		define ('XML_MMO_SHOPADDR_S','<shop_address>');
		define ('XML_MMO_SHOPADDR_E','</shop_address>');
		define ('XML_MMO_COUNTRY_S','<country_name>');
		define ('XML_MMO_COUNTRY_E','</country_name>');

		define ('XML_MMO_PAYMENT_OPT_S','<payment_options>');
		define ('XML_MMO_PAYMENT_OPT_E','</payment_options>');

		define ('XML_MMO_PAYMENTINFO_S','<payment_info>');
		define ('XML_MMO_PAYMENTINFO_E','</payment_info>');
		define ('XML_MMO_PAYOPTION_S','<pay_option>');
		define ('XML_MMO_PAYOPTION_E','</pay_option>');
		define ('XML_MMO_LIVE_HELP_S','<live_help>');
		define ('XML_MMO_LIVE_HELP_E','</live_help>');
		define ('XML_MMO_PAYPALADDR_S','<paypal_address>');
		define ('XML_MMO_PAYPALADDR_E','</paypal_address>');
		define ('XML_MMO_EBAYNAME_S','<ebay_name>');
		define ('XML_MMO_EBAYNAME_E','</ebay_name>');
		define ('XML_MMO_CURRENCY_S','<currency>');
		define ('XML_MMO_CURRENCY_E','</currency>');

		define ('XML_MMO_GAME_S','<game ');
		define ('XML_MMO_GAME_E','</game>');
		define ('XML_MMO_SERVER_S','<server ');
		define ('XML_MMO_SERVER_E','</server>');
		define ('XML_MMO_FACTION_S','<faction ');
		define ('XML_MMO_FACTION_E','</faction>');
		define ('XML_MMO_PRICE_S','<price ');
		define ('XML_MMO_PRICE_E','</price>');

		define ('XML_CDATA_S','<![CDATA[');
		define ('XML_CDATA_E',']]>');

		//cdkey
		define ('XML_CDKEY_HEADING_TITLE','<?xml version="1.0" encoding="UTF-8"?>');
		define ('XML_CDKEY_SHOP_S','<shop>');
		define ('XML_CDKEY_SHOP_E','</shop>');
		define ('XML_CDKEY_SHOPINFO_S','<shop_info>');
		define ('XML_CDKEY_SHOPINFO_E','</shop_info>');
		define ('XML_CDKEY_SHOPNAME_S','<shop_name>');
		define ('XML_CDKEY_SHOPNAME_E','</shop_name>');
		define ('XML_CDKEY_FREETEXT_S','<free_text>');
		define ('XML_CDKEY_FREETEXT_E','</free_text>');
		define ('XML_CDKEY_COMPANYNAME_S','<company_name>');
		define ('XML_CDKEY_COMPANYNAME_E','</company_name>');
		define ('XML_CDKEY_SHOPURL_S','<shop_url>');
		define ('XML_CDKEY_SHOPURL_E','</shop_url>');
		define ('XML_CDKEY_CONTACTEMAIL_S','<contact_email>');
		define ('XML_CDKEY_CONTACTEMAIL_E','</contact_email>');
		define ('XML_CDKEY_TELEPHONE_S','<telephone>');
		define ('XML_CDKEY_TELEPHONE_E','</telephone>');
		define ('XML_CDKEY_SHOPADDR_S','<shop_address>');
		define ('XML_CDKEY_SHOPADDR_E','</shop_address>');
		define ('XML_CDKEY_COUNTRY_S','<country_name>');
		define ('XML_CDKEY_COUNTRY_E','</country_name>');
		define ('XML_CDKEY_POLICY_S','<policy>');
		define ('XML_CDKEY_POLICY_E','</policy>');
		define ('XML_CDKEY_GAME_NAME_S','<game>');
		define ('XML_CDKEY_GAME_NAME_E','</game>');
		define ('XML_CDKEY_G_NAME_S','<name>');
		define ('XML_CDKEY_G_NAME_E','</name>');
		define ('XML_CDKEY_PRODUCT_S','<product>');
		define ('XML_CDKEY_PRODUCT_E','</product>');
		define ('XML_CDKEY_PRICE_CURRENCY_S','<price ');
		define ('XML_CDKEY_PRICE_CURRENCY_E','</price>');
		define ('XML_CDKEY_PRODUCT_ID_S','<id>');
		define ('XML_CDKEY_PRODUCT_ID_E','</id>');
		define ('XML_CDKEY_PRODUCT_NAME_S','<name>');
		define ('XML_CDKEY_PRODUCT_NAME_E','</name>');
		define ('XML_CDKEY_PRODUCT_LINK_S','<product_link>');
		define ('XML_CDKEY_PRODUCT_LINK_E','</product_link>');
		define ('XML_CDKEY_PRODUCT_IMG_URL_S','<img_url>');
		define ('XML_CDKEY_PRODUCT_IMG_URL_E','</img_url>');
		define ('XML_CDKEY_PRODUCT_DESCRIPTION_S','<product_description>');
		define ('XML_CDKEY_PRODUCT_DESCRIPTION_E','</product_description>');
		define ('XML_CDATA_CDKEY_S','<![CDATA[');
		define ('XML_CDATA_CDKEY_E',']]>');

	}

	function safe_quote_string($string) {
		$result_string = preg_replace('/&/', '&amp;', $string);
		$result_string = preg_replace('/-/', ' ', $result_string);
		$result_string = preg_replace('/\s\s+/', ' ', $result_string);

		return $result_string;
	}

	function convert_to_real_qty ($cat_id ,$qty) {
		return isset($this->game_qty_conversion[$cat_id]) ? $qty * $this->game_qty_conversion[$cat_id] : $qty;
	}

	function cat_id_display_available($parent_cat_id, $match_current_cat_id, $loop_num, $custom_products_type_id) {
		global $languages_id, $customers_groups_id;

		if ($loop_num) {
			$sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
										FROM " . TABLE_CATEGORIES . " AS c
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											ON c.categories_id=cd.categories_id
										WHERE c.parent_id = '" . $parent_cat_id . "'
											AND cd.language_id = '" . (int)$languages_id . "'
											AND c.custom_products_type_id = 0
											AND c.categories_status = 1
										ORDER BY c.sort_order, cd.categories_name";
			$sub_category_result_sql = tep_db_query($sub_category_select_sql);
			while ($sub_category_row = tep_db_fetch_array($sub_category_result_sql)) {
				if ($loop_num == 1) {
					$permitted_cat_select_sql = "	SELECT linkid
				  									FROM " . TABLE_CATEGORIES_GROUPS . "
				  									WHERE categories_id = '" . $sub_category_row["categories_id"] . "'
				  										AND ((groups_id = '".(int)$customers_groups_id."') OR (groups_id=0)) ";
					$permitted_cat_result_sql = tep_db_query($permitted_cat_select_sql);
					$permitted_cat_row = tep_db_fetch_array($permitted_cat_result_sql);
					if (tep_db_num_rows($permitted_cat_result_sql)) {
						if($sub_category_row["categories_id"] == $match_current_cat_id) {
							return true;
						}
					}
				} else {
					$this->cat_id_display_available($sub_category_row["categories_id"], $match_current_cat_id, $loop_num-1, $custom_products_type_id);
				}
			}
		} else return;
	}

	function get_started_category_path_id ($category_parent_id) {
		$validate_category_select_sql = "	SELECT categories_id
		  									FROM " . TABLE_CATEGORIES . "
		  									WHERE categories_id = '".$category_parent_id."'
		  									AND custom_products_type_id != '999'";
		$validate_category_result_sql = tep_db_query($validate_category_select_sql);
		if ($validate_category_row = tep_db_fetch_array($validate_category_result_sql)) {
			return true;
		} else {
			return false;
		}
	}

	function get_category_name ($category_id) {
		$category_name_select_sql = "	SELECT c.categories_id, cd.categories_name
										FROM " . TABLE_CATEGORIES . " AS c
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											ON c.categories_id=cd.categories_id
										WHERE c.categories_id = '" . $category_id . "'";
		$category_name_result_sql = tep_db_query($category_name_select_sql);
		if ($category_name_row = tep_db_fetch_array($category_name_result_sql)) {
			return $category_name_row['categories_name'];
		}
	}

	function report_error ($message) {
            //
	}
}
?>