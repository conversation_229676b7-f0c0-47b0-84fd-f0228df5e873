<?php

require_once(DIR_WS_CLASSES . 'page_template.php');
class game_database_config {
	var $config_def_option, $config_set_lang, $config_show_note, $cftype_options;
	var $default_language_id, $language_obj;
	
	function game_database_config() {
		$this->language_obj = array();
		
		$this->config_def_option = 'esrb';
		$this->config_show_note = 'esrb';
		$this->config_set_lang = '';
		
		$this->config_type_options = array (
			array ('id' => 'esrb', 'text' => 'ESRB'),
			array ('id' => 'genre', 'text' => 'Genre'),
			array ('id' => 'platform', 'text' => 'Platform'),
			array ('id' => 'language', 'text' => 'Language'),
			array ('id' => 'region', 'text' => 'Region'),
            array ('id' => 'blog', 'text' => 'Game Blog')
		);
		
		$game_lang_select_sql = "SELECT code, languages_id, name FROM " . TABLE_LANGUAGES . " ORDER BY sort_order ASC";
		$game_lang_result_sql = tep_db_query($game_lang_select_sql);
		while ($lang_rows = tep_db_fetch_array($game_lang_result_sql)) {
			$defaultflaq = '';
			$row_obj = array();
			
			if (DEFAULT_LANGUAGE == $lang_rows['code']) {
				$defaultflaq = 'default';
				$this->default_language_id = $lang_rows['languages_id'];
			}
			
			$row_obj = array (	'code' => $lang_rows['code'],
								'languages_id' => $lang_rows['languages_id'],
								'name' => $lang_rows['name'],
								'default' => $defaultflaq );
			
			$this->language_obj[] = $row_obj; 
		}
	}
	
	function menuListing() {
		$listing_html = '';
		$listing_obj = array();
		
		if ($_GET['config_type']) {
			$cftype = $_GET['config_type'];
		} else {
			$cftype = $this->config_def_option;
		}
		
		$cftable = strtoupper($cftype);
		$MAIN_TABLE = 'TABLE_GAME_' . $cftable;
		$DESC_TABLE = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';
		
		$id_desc_select_sql = "	SELECT t1.game_" . $cftype . "_id AS id, 
									t2.game_" . $cftype . "_description AS description " .
								($cftype == $this->config_show_note ? ', t2.note AS note ' : '' ) . " 
								FROM " . constant($MAIN_TABLE) . " AS t1 
								INNER JOIN " . constant($DESC_TABLE) . " AS t2 
									ON t1.game_" . $cftype . "_id = t2.game_" . $cftype . "_id
								WHERE t2.language_id = '" . $this->default_language_id . "' 
								ORDER BY sort_order";
		$id_desc_result_sql = tep_db_query($id_desc_select_sql);
		while ($id_desc_rows = tep_db_fetch_array($id_desc_result_sql)) {
			$row_obj = array();
			
			if ($cftype == $this->config_show_note) {
				$row_obj = array (	'id' => $id_desc_rows['id'],
									'description' => $id_desc_rows['description'],
									'note' => $id_desc_rows['note'] );
			} else {
				$row_obj = array (	'id' => $id_desc_rows['id'],
									'description' => $id_desc_rows['description'] );
			}
			
			$listing_obj[] = $row_obj;
		}
		
		ob_start();
?>
		<script language="javascript">
		<!--
		function deleteentry(s,t,id) {
			answer = confirm('Are you sure to delete '+ (trim_str(s) != '' ? "<" + s + "> " : '') + '"' + t + '"' + ' record?')
			if (answer !=0) { 
				jQuery.get("?action=delete&id="+id+"&config_type="+strtolower(str_replace(' ', '_', s)), function(data){
					if (data == "success")
			 			jQuery('#row-'+id).fadeOut('slow');
				});
			}
		}
		//-->
		</script>
		
		<form name=listing>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class=main align="left">
									<?php echo ENTRY_CONFIGURATION_TYPE . '&nbsp;' . tep_draw_pull_down_menu("config_type", $this->config_type_options, ($cftype) ? $cftype : '', ' onchange="document.location.href=\'?selected_box=infolinks&config_type=\'+ escape(document.listing.config_type.value)"'); ?>
								</td>
								<td align="right">[ <a href="?selected_box=infolinks&action=add_form&config_type=<?php echo urlencode($cftype); ?>" ><?php echo LINK_ADD_SETTING; ?></a> ]</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign="top">
						<table border="0" width="100%" cellspacing="1" cellpadding="2">
		 					<tr>
			 					<td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_DESCRIPTION; ?></td>
		 						<td class="reportBoxHeading" width="5%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
		 					</tr>
							<?php
							if (tep_not_null($listing_obj)) {
								$entryCount = 0;
								
								foreach ($listing_obj as $rows) {
									$entryCount++;
									
									($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
							?>
								<tr id="row-<?php echo $rows['id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
									<td class="reportRecords" valign="top">
										<?php 
											if ($cftype == $this->config_show_note) {
												echo '<b>(' . $rows['description'] . ')</b> ' . $rows['note'];
											} else {
												echo $rows['description'];
											}
										?>
									</td>
									<td class="reportRecords" valign="top" align=center>
										<a href="?selected_box=infolinks&action=add_form&id=<?php echo $rows['id']; ?>&config_type=<?php echo $cftype; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
										<a href="javascript:void(deleteentry('<?php echo strtoupper($cftype); ?>','<?php echo htmlentities ($rows['description']); ?>','<?php echo htmlentities ($rows['id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
									</td>
								</tr>
							<?php
								}
							} else {
							?>
								<tr class="reportListingEven">
									<td class="reportRecords" align="center" colspan="3"><i>Empty</i></td>
								</tr>
							<?php
							}
							?>
						</table>
		 			</td>
		 		</tr>
			</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	function addForm($id = "") {
		$listing_html = '';
		
		if ($_GET['config_type']) {
			$cftype = $_GET['config_type'];
		} else {
			$cftype = $this->config_def_option;
		}
		
		if ($id) {
			$cftable = strtoupper($cftype);
			$MAIN_TABLE = 'TABLE_GAME_' . $cftable;
			$DESC_TABLE = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';
			
			$id_select_sql = "	SELECT game_" . $cftype . "_id AS id, sort_order 
								FROM " . constant($MAIN_TABLE) . " 
								WHERE game_" . $cftype . "_id = '$id'";
			$id_result_sql = tep_db_query($id_select_sql);
	 		$id_row = tep_db_fetch_array($id_result_sql);

	 		foreach ($this->language_obj as $language_rows) {
				$language_id = $language_rows['languages_id'];
				
				$desc_select_sql = "SELECT game_" . $cftype . "_description AS description " .
									($cftype == $this->config_show_note ? ', note ' : '' ) . " 
									FROM " . constant($DESC_TABLE) . " 
									WHERE game_" . $cftype . "_id = '$id' 
										AND language_id = '$language_id'";
				$desc_result_sql = tep_db_query($desc_select_sql);
				if ($desc_rows = tep_db_fetch_array($desc_result_sql)) {
					if ($cftype == $this->config_show_note) {
						$id_row['note'] = $desc_rows['description'];
						$id_row["description_{$language_id}"] = $desc_rows['note'];
					} else {
						$id_row["description_{$language_id}"] = $desc_rows['description'];
					}
				}
	 		}
		}
		
		ob_start();
?>
		<script language="javascript">
		<!--
		function check_form() {
			var error = 0;
			var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";
			
			if (document.menu_form.note) {
				if (document.menu_form.note.value == "") {
					error_message = error_message + "* The 'Note' entry must be entered.\n";
					error = 1;
				}	
			}
			
			if (document.menu_form.description_<?php echo $this->default_language_id; ?>.value == "") {
				error_message = error_message + "* The 'Description' entry must be entered.\n";
				error = 1;
			}
			
			if (validateInteger(document.menu_form.sort_order.value) == false) {
				error_message = error_message + "* The 'Sort Order' value is invalid.\n";
				error = 1;
			}
			
			var max = document.menu_form.default_setting.length;
			for (var idx = 0; max > idx; idx++) {
				if (eval('document.menu_form.default_setting[' + idx + '].checked') == true) {
					if (eval('document.menu_form.game_lang_' + idx + '.checked') == false) {
						error_message = error_message + "* Default Language's checkbox is not checked.\n";
						error = 1;
					}
   				}
			}
			
			if (error == 1) {
				alert(error_message);
				return false;
			}
			else {
				return true;
			}
		}
		//-->
		</script>
		
		<?=tep_draw_form('menu_form', 'game_database_config.php', 'selected_box=infolinks&action=add', 'post', ' onSubmit="return check_form();" id="menu_form"')?>
		<table cellspacing="2" cellpadding="2" border="0">
			<tr>
				<td>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td class="pageHeading" valign="top"><?php if ($id != "") { echo HEADING_EDIT_MENU; } else { echo HEADING_ADD_MENU; } ?></td>
							<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table border="0" cellpadding="2" cellspacing="2">
						<tr>
							<td class="main" valign=top><?php echo ENTRY_CONFIGURATION_TYPE; ?></td>
							<td class="main">
								<?php 
									foreach ($this->config_type_options as $opt => $options) {
										if ($options['id'] == $cftype) {
											echo $options['text'];
											break;
										}
									}
									echo tep_draw_hidden_field('config_type', $cftype); 
								?>
							</td>
						</tr>
						<?php
							if ($cftype == $this->config_show_note) {
						?>
						<tr id="config-title-row">
							<td class="main" valign=top><?php echo ENTRY_DESCRIPTION; ?></td>
							<td class="main">
								<?php 
									echo tep_draw_input_field('note', tep_not_null($id_row['note']) ? $id_row['note'] : '' , ' id="note" size="5" maxlength="5" '); 
									print " <span class=\"fieldRequired\">* Required</span>";
								?>
							<td>
						</tr>
						<?php
							}
						?>
						<tr>
							<td class="main" valign=top><?php echo ($cftype == $this->config_show_note ? ENTRY_NOTE : ENTRY_DESCRIPTION); ?></td>
							<td class="main">
								<?php
								if ($id) {
									echo tep_draw_hidden_field('id', $id);
								}
								?>
								<table cellpadding="1" cellspacing="0" border="0">
								<?php
									foreach ($this->language_obj as $language_row) {
										$count_languages_id = $language_row['languages_id'];
								?>
									<tr>
										<td class="main"><b><?php echo $language_row['name']; ?></b></td>
										<td class="main"><?=tep_draw_input_field('description_' . $count_languages_id, $id_row["description_{$count_languages_id}"], ' id="description_' . $count_languages_id.'" size="60" ')?></td>
								<?php
										if ($language_row['default'] == "default"){
											print "<td><span class=\"fieldRequired\">* Required</span></td>";
										}
								?>
									</tr>
								<?php
									}
								?>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
						</tr>
						<tr>
							<td class="main"><?php echo ENTRY_SORT_ORDER; ?></td>
							<td class="main">
								<?=tep_draw_input_field('sort_order', ($id_row['sort_order']) ? $id_row['sort_order'] : '0' , ' id="sort_order" size="5" maxlength="5" ')?>
							</td>
						</tr>
						<tr>
							<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
			</tr>
			<?php
				if (tep_not_null($this->config_set_lang) && ($cftype == $this->config_set_lang)) {
					$data = array();
					$lang_data = array();
					$default_setting = '';
					
					$game_lang_desc_select_sql = "	SELECT game_language_id AS id, game_language_description AS description 
													FROM " . TABLE_GAME_LANGUAGE_DESCRIPTION . " 
													WHERE language_id = '" . $this->default_language_id . "'";
					$game_lang_desc_result_sql = tep_db_query($game_lang_desc_select_sql);
					while ($game_lang_desc_rows = tep_db_fetch_array($game_lang_desc_result_sql)) {
						$data[] = array ( 'id' => $game_lang_desc_rows['id'], 'text' => $game_lang_desc_rows['description'] );
					}
					
					if (tep_not_null($id)) {
						$region_lang_select_sql = "	SELECT game_language_id, default_setting 
													FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " 
													WHERE game_" . $cftype . "_id = '" . $id . "'";
						$region_lang_result_sql = tep_db_query($region_lang_select_sql);
						while ($region_lang_rows = tep_db_fetch_array($region_lang_result_sql)) {
							$lang_data[] = $region_lang_rows['game_language_id'];
							
							if ($region_lang_rows['default_setting'] == 1) {
								$default_setting = $region_lang_rows['game_language_id'];
							}
						}
					}
			?>
			<tr>
				<td class="main" valign=top>
					<table border="0" cellpadding="2" cellspacing="2">
						<tr>
							<td class="main" valign="top"><?php echo ENTRY_LANGUAGE_SUPPORT; ?></td>
							<td class="main" valign="top">
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td class="main" valign="top" width="150"><b><?php echo ENTRY_LANGUAGE; ?></b></td>
										<td class="main" valign="top"><b><?php echo ENTRY_DEFAULT; ?></b></td>
									</tr>
									<?php
										foreach ($data as $num => $array) {
									?>
										<tr>
											<td class="main" valign="top">
												<?php echo tep_draw_checkbox_field('game_language_id[]" id="game_lang_' . $num, $array['id'], in_array($array['id'], $lang_data) ? true : false); ?>
												<label for="game_lang_<?php echo $num; ?>"><?php echo $array['text']; ?></label>
											</td>
											<td class="main" valign="top" align="center">
												<?php echo tep_draw_radio_field('default_setting', $array['id'], ($default_setting == $array['id'] ? true : false)); ?>
											</td>
										</tr>
									<?php
										}
									?>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<?php
				}
			?>
			<tr>
				<td class="main" align="right">
					<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
					<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, '?selected_box=infolinks&config_type=' . $cftype, '', 'inputButton')?>
				</td>
			</tr>
		</table>
		</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
	
	
	function addEntry($id="") {
		global $messageStack;
		
		$error = 0;
		$error_msg = '';
		$editEntry = false;
		
		$note = tep_not_null($_POST['note']) ? tep_db_prepare_input($_POST['note']) : '';
		$def_desc = tep_not_null($_POST["description_{$this->default_language_id}"]) ? tep_db_prepare_input($_POST["description_{$this->default_language_id}"]) : '';
		
		$cftype = $_POST['config_type'];
		$cftable = strtoupper($cftype);
		$MAIN_TABLE = 'TABLE_GAME_' . $cftable;
		$DESC_TABLE = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';
		
		$game_desc_select_sql = "	SELECT game_" . $cftype . "_description 
									FROM " . constant($DESC_TABLE) . " 
									WHERE game_" . $cftype . "_description = '" . 
										($cftype == $this->config_show_note ? $note : $def_desc) . "' " .
										(tep_not_null($id) ? " AND game_" . $cftype . "_id != '{$id}'" : "");
		$game_desc_result_sql = tep_db_query($game_desc_select_sql);
		if (tep_db_num_rows($game_desc_result_sql) > 0) {
			$error = 1;
			$error_msg = ($cftype == $this->config_show_note ? $note : $def_desc);
		}
		
		if ($error == 0) {
			$data = array ( 'sort_order' => (int)$_POST['sort_order'] );
			
			if ($id) {
				$editEntry = true;
				tep_db_perform(constant($MAIN_TABLE), $data, 'update', "game_" . $cftype . "_id='" . $id . "'");
				
				$game_desc_delete_sql = "DELETE FROM " . constant($DESC_TABLE) . " WHERE game_" . $cftype . "_id = '$id'";
				tep_db_query($game_desc_delete_sql);
			} else {
				if($cftype == 'blog'){
					$pageTemplate = new page_template();
					$data['custom_url'] = $pageTemplate->format_url($_POST["description_1"]);
				}
				tep_db_perform(constant($MAIN_TABLE), $data, 'insert');
				$id = tep_db_insert_id();
			}
			
			if (tep_not_null($id)) {
				foreach ($this->language_obj as $language_rows) { 
					$data = array();
					$description = '';
					$language_id = $language_rows["languages_id"];
					
					if (tep_not_null($_POST["description_{$language_id}"])) {
						$description = tep_db_prepare_input($_POST["description_{$language_id}"]);
					} else {
						$description = tep_db_prepare_input($def_desc);
					}
					
					if ($cftype == $this->config_show_note && tep_not_null($note)) {
						$data = array (	'game_' . $cftype . '_id' => $id, 
										'language_id' => tep_db_prepare_input($language_rows['languages_id']), 
										'game_' . $cftype . '_description' => $note, 
										'note' => $description );
					} else {
						$data = array (	'game_' . $cftype . '_id' => $id, 
										'language_id' => tep_db_prepare_input($language_rows['languages_id']), 
										'game_' . $cftype . '_description' => $description );
					}
					
					tep_db_perform(constant($DESC_TABLE), $data, 'insert');
				}
				
				// region to language setting
				if ($cftype == $this->config_set_lang) {
					$game_lang_data = array();
					$game_lang_post = tep_not_null($_POST['game_language_id']) ? $_POST['game_language_id'] : array();
					
					if ($editEntry) {
						$region_lang_select_sql = "	SELECT game_language_id 
													FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " 
													WHERE game_region_id = '" . $id . "'";
						$region_lang_result_sql = tep_db_query($region_lang_select_sql);
						while ($region_lang_rows = tep_db_fetch_array($region_lang_result_sql)) {
							$game_lang_data[] = $region_lang_rows['game_language_id'];
						}
						
						// remove unchecked option
						$array_diff = array();
						$array_diff = array_diff($game_lang_data, $game_lang_post);
						if (tep_not_null($array_diff)) {
							foreach ($array_diff as $num => $game_lang_id) {
								$game_lang_select_sql = "	SELECT game_region_to_language_id 
															FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " 
															WHERE game_region_id = '" . $id . "' 
																AND game_language_id = '" . $game_lang_id . "'";
								$game_lang_result_sql = tep_db_query($game_lang_select_sql);
								if ($game_lang_rows = tep_db_fetch_array($game_lang_result_sql)) {
									$region_lang_delete_sql = "DELETE FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " WHERE game_region_to_language_id = '" . $game_lang_rows['game_region_to_language_id'] . "'";
									tep_db_query($region_lang_delete_sql);
									
									$region_lang_cat_delete_sql = "DELETE FROM " . TABLE_GAME_REGION_LANGUAGE_TO_CATEGORIES . " WHERE game_region_to_language_id = '" . $game_lang_rows['game_region_to_language_id'] . "'";
									tep_db_query($region_lang_cat_delete_sql);
								}
							}
						}
					}
					
					// insert new checked option
					$array_diff = array();
					$array_diff = array_diff($game_lang_post, $game_lang_data);
					if (tep_not_null($array_diff)) {
						foreach ($array_diff as $num => $game_lang_id) {
							$sql_data = array();
							$sql_data = array (	'game_region_id' => $id, 
												'game_language_id' => $game_lang_id );
							tep_db_perform(TABLE_GAME_REGION_TO_LANGUAGE, $sql_data);
						}
					}
					
					// set `default_setting`
					$default_setting = (int)$_POST['default_setting'];
					if ($default_setting) {
						$data = array();
						$data = array ( 'default_setting' => 0 );
						tep_db_perform(TABLE_GAME_REGION_TO_LANGUAGE, $data, 'update', "game_region_id = '{$id}'");
						
						$data = array();
						$data = array ( 'default_setting' => 1 );
						tep_db_perform(TABLE_GAME_REGION_TO_LANGUAGE, $data, 'update', "game_region_id = '{$id}' AND game_language_id = '" . $default_setting . "'");
					}
				}
			}
		} else {
			$messageStack->add_session(sprintf(ERROR_TITLE_EXIST, $error_msg), 'error');
		}
	}
	
	function deleteEntry($id='', $cftype='') {
		if ($id && tep_not_null($cftype)) {
			$main_result_sql = 0;
			$desc_result_sql = 0;
			
			$cftable = strtoupper($cftype);
			
			$MAIN_TABLE = 'TABLE_GAME_' . $cftable;
			$DESC_TABLE = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';
			$TO_CAT_TABLE = 'TABLE_GAME_' . $cftable . '_TO_CATEGORIES';
			
			$id_delete_sql = "DELETE FROM " . constant($MAIN_TABLE) . " WHERE game_" . $cftype . "_id = '$id'";
			$main_result_sql = tep_db_query($id_delete_sql);
			
			$desc_delete_sql = "DELETE FROM " . constant($DESC_TABLE) . " WHERE game_" . $cftype . "_id = '$id'";
			$desc_result_sql = tep_db_query($desc_delete_sql);
			
			if ($cftype == 'region' || $cftype == 'language') {
				$region_lang_select_sql = "	SELECT game_region_to_language_id 
											FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " 
											WHERE game_" . $cftype . "_id = '{$id}'";
				$region_lang_result_sql = tep_db_query($region_lang_select_sql);
				while ($rows = tep_db_fetch_array($region_lang_result_sql)) {
					$region_lang_cat_delete_sql = "	DELETE FROM " . TABLE_GAME_REGION_LANGUAGE_TO_CATEGORIES . " 
													WHERE game_region_to_language_id = '" . $rows['game_region_to_language_id'] . "'";
					tep_db_query($region_lang_cat_delete_sql);
				}
				
				$region_lang_delete_sql = "DELETE FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " WHERE game_" . $cftype . "_id = '{$id}'";
				$main_result_sql = tep_db_query($region_lang_delete_sql);
			} else {
				$game_cat_delete_sql = "DELETE FROM " . constant($TO_CAT_TABLE) . " WHERE game_" . $cftype . "_id = '$id'";
				tep_db_query($game_cat_delete_sql);
			}
			
			if ($main_result_sql == 1 && $desc_result_sql == 1) {
				return 'success';
			}
		} else {
			return '';
		}
	}	
}
?>