<?php

class gst_configuration {

    public $default_language_id = 1;
    public $language_obj = array();
    public $log_users_id;
    public $order_tax_id;

    var $message_location;

    private static $aws_s3_setting = array(
        'bucket' => 'BUCKET_IMAGE',
        'acl' => 'ACL_PUBLIC',
        'storage' => 'STORAGE_STANDARD',
        'source_path' => 'gallery/tax'
    );

    private static $image_supported = array(
        'type' => array('gif', 'jpg', 'png')
    );

    public function __construct($login_user_id = NULL) {
        $language_select_sql = "SELECT code, languages_id, name FROM " . TABLE_LANGUAGES . " ORDER BY sort_order ASC";
        $language_result_sql = tep_db_query($language_select_sql);
        while ($language_rows = tep_db_fetch_array($language_result_sql)) {
            $defaultflaq = '';
            $row_obj = array();

            if (DEFAULT_LANGUAGE == $language_rows['code']) {
                $defaultflaq = 'default';
                $this->default_language_id = $language_rows['languages_id'];
            }

            $row_obj = array(
                'code' => $language_rows['code'],
                'languages_id' => $language_rows['languages_id'],
                'name' => $language_rows['name'],
                'default' => $defaultflaq);

            $this->language_obj[] = $row_obj;
        }

        $this->setLoginUserID($login_user_id);
    }

    public function menuListing() {
        $entryCount = 0;
        $listing_html = '';

        $tax_status_array = array(
            '1' => array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT),
            '0' => array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
        );

        // pagination
        $orders_tax_select_sql = "select orders_tax_id from " . TABLE_ORDERS_TAX_CONFIGURATION . " order by country_code";
        $page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $orders_tax_select_sql, $orders_tax_numrows);

        ob_start();
        ?>
        <script language="javascript">
            <!--
            function deleteentry(s, t, id) {
                answer = confirm('Are you sure to delete ' + (trim_str(s) != '' ? "<" + s + "> " : '') + '"' + t + '"' + ' record?')
                if (answer != 0) {
                    jQuery.get("?action=delete&id=" + id, function (data) {
                        if (data == "success")
                            jQuery('#row-' + id).fadeOut('slow');
                    });
                }
            }
            //-->
        </script>

           <form name=listing>
                 <table border="0" width="100%" cellspacing="0" cellpadding="2">
                    <tr>
                        <td colspan="2">
                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
                                    <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="reportRecords"><?php echo HEADING_GST_STATUS; ?><a href="<?php echo tep_href_link(FILENAME_MODULES, 'set=ordertotal&module=ot_gst'); ?>"><?php echo (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst') ? 'ON' : 'OFF'); ?></a></td>
                        <td align="right" class="reportRecords">[ <a href="?action=add_form"><?php echo LINK_ADD_SETTING; ?></a> ]</td>
                    </tr>
                    <tr>
                        <td valign="top" colspan="2">
                            <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                <tr>
                                    <td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_IP_COUNTRY; ?></td>
                                    <td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_TAX_TITLE; ?></td>
                                    <td class="reportBoxHeading" align=center><?php echo SUB_TABLE_HEADING_TAX; ?></td>
                                    <td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_PROVIDE_INVOICE_STATUS; ?></td>
                                    <td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_STATUS; ?></td>
                                    <td class="reportBoxHeading" width="10%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
                                </tr>
                                <?php
                                $orders_tax_result_sql = tep_db_query($orders_tax_select_sql);
                                if (tep_db_num_rows($orders_tax_result_sql) > 0) {
                                    while ($orders_tax_rows = tep_db_fetch_array($orders_tax_result_sql)) {
                                        $orders_tax_config_select_sql = "	SELECT otc.orders_tax_id, otcd.orders_tax_title, c.countries_name,
                                                                            otc.orders_tax_percentage, otc.orders_tax_status,
                                                                            otc.orders_provide_invoice_status
																			FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
																			INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd 
																				ON otcd.orders_tax_id = otc.orders_tax_id 
																			LEFT JOIN " . TABLE_COUNTRIES . " AS c 
																				ON c.countries_iso_code_2 = otc.country_code 
																			WHERE otcd.language_id = '" . $this->default_language_id . "'
																				AND otc.orders_tax_id = '" . $orders_tax_rows['orders_tax_id'] . "'";
                                        $orders_tax_config_result_sql = tep_db_query($orders_tax_config_select_sql);

                                        if ($orders_tax_config_rows = tep_db_fetch_array($orders_tax_config_result_sql)) {
                                            ($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
                                            ?>
                                            <tr id="row-<?php echo $orders_tax_config_rows['orders_tax_id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
                                                <td class="reportRecords" valign="top"><?= $orders_tax_config_rows['countries_name']; ?></td>
                                                <td class="reportRecords" valign="top"><?= $orders_tax_config_rows['orders_tax_title']; ?></td>
                                                <td class="reportRecords" valign="top" align="center"><?= $orders_tax_config_rows['orders_tax_percentage']; ?></td>
                                                <td class="reportRecords" valign="top" align="center">&nbsp;&nbsp;
                                                    <?php
                                                    foreach ($tax_status_array as $status_id => $img_res) {
                                                        if ((int) $orders_tax_config_rows['orders_provide_invoice_status'] == (int) $status_id) {
                                                            echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
                                                        } else {
                                                            echo '<a href="' . tep_href_link(FILENAME_GST_CONFIGURATION, 'action=set_orders_provide_invoice_status&flag=' . (int) $status_id . '&id=' . $orders_tax_config_rows['orders_tax_id'] . '&page=' . $_GET["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
                                                        }
                                                    }
                                                    ?>
                                                </td>
                                                <td class="reportRecords" valign="top" align="center">&nbsp;&nbsp;
                                                    <?php
                                                    foreach ($tax_status_array as $status_id => $img_res) {
                                                        if ((int) $orders_tax_config_rows['orders_tax_status'] == (int) $status_id) {
                                                            echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
                                                        } else {
                                                            echo '<a href="' . tep_href_link(FILENAME_GST_CONFIGURATION, 'action=set_orders_tax_status&flag=' . (int) $status_id . '&id=' . $orders_tax_config_rows['orders_tax_id'] . '&page=' . $_GET["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
                                                        }
                                                    }
                                                    ?>
                                                </td>
                                                <td class="reportRecords" valign="top" align=center>
                                                    <a href="?selected_box=localization&action=add_form&id=<?php echo $orders_tax_config_rows['orders_tax_id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                                    <a href="javascript:void(deleteentry('<?php echo $orders_tax_config_rows['countries_name']; ?>','<?php echo htmlentities($orders_tax_config_rows['orders_tax_title']); ?>','<?php echo htmlentities($orders_tax_config_rows['orders_tax_id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
                                                </td>
                                            </tr>
                                            <?php
                                            $entryCount++;
                                        }
                                    }
                                } else {
                                    ?>
                                    <tr class="reportListingEven">
                                        <td class="reportRecords" align="center" colspan="6"><i>Empty</i></td>
                                    </tr>
                                    <?php
                                }
                                ?>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td valign="top" colspan="2">
                            <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                                <tr>
                                    <td class="smallText" valign="top" nowrap><?= $page_split_object->display_count($orders_tax_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_TAX) ?></td>
                                    <td class="smallText" align="right"><?= $page_split_object->display_links($orders_tax_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1") ?></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addForm($id = "") {
        $listing_html = '';
        $countries_array = array();
        $orders_tax_status_active = false;
        $orders_tax_status_inactive = true;
        $orders_provide_invoice_status_active = false;
        $orders_provide_invoice_status_inactive = true;
        $edit_mode = false;
        $tax_percentage = 0;

        if ($id) {
            $edit_mode = true;
            $orders_tax_config_select_sql = "	SELECT otc.orders_tax_id, c.countries_id, otc.orders_tax_percentage,
                                                otc.orders_tax_status, otc.start_datetime, otc.address_1, otc.address_2,
                                                otc.address_3, otc.contact, otc.website, otc.tax_registration_name, otc.tax_invoice_title,
                                                otc.gst_registration_no, otc.company_name, otc.company_logo, otc.orders_provide_invoice_status
												FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
												LEFT JOIN " . TABLE_COUNTRIES . " AS c 
													ON c.countries_iso_code_2 = otc.country_code 
												WHERE otc.orders_tax_id = '" . $id . "'";
            $orders_tax_config_result_sql = tep_db_query($orders_tax_config_select_sql);
            $orders_tax_config_row = tep_db_fetch_array($orders_tax_config_result_sql);

            // Orders Tax Status
            if ($orders_tax_config_row['orders_tax_status'] == true) {
                $orders_tax_status_active = true;
                $orders_tax_status_inactive = false;
            }

            // Orders Provide Invoice Status
            if ($orders_tax_config_row['orders_provide_invoice_status'] == true) {
                $orders_provide_invoice_status_active = true;
                $orders_provide_invoice_status_inactive = false;
            }

            $tax_percentage = $orders_tax_config_row['orders_tax_percentage'];

            foreach ($this->language_obj as $language_rows) {
                $language_id = $language_rows['languages_id'];

                $orders_tax_config_desc_select_sql = "	SELECT orders_tax_title, orders_tax_title_short, orders_tax_message
	 													FROM " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " 
	 													WHERE language_id = '" . $language_id . "'
	 														AND orders_tax_id = '" . $id . "'";
                $orders_tax_config_desc_result_sql = tep_db_query($orders_tax_config_desc_select_sql);
                if ($orders_tax_config_desc_rows = tep_db_fetch_array($orders_tax_config_desc_result_sql)) {
                    $orders_tax_config_row["description_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_title'];
                    $orders_tax_config_row["description_short_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_title_short'];
                    $orders_tax_config_row["message_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_message'];
                }
            }
        }

        ob_start();
        ?>
            <script language="javascript">
                <!--
                function check_form() {
                    var error = 0;
                    var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";
                    var old_tax_percentage = <?php echo $tax_percentage ?>;

                    if (document.menu_form.description_<?php echo $this->default_language_id; ?>.value == "") {
                        error_message = error_message + "* The 'Tax Title (long)' entry must be entered.\n";
                        error = 1;
                    }

                    if (document.menu_form.description_short_<?php echo $this->default_language_id; ?>.value == "") {
                        error_message = error_message + "* The 'Tax Title (short)' entry must be entered.\n";
                        error = 1;
                    }

                    if (document.menu_form.message_<?php echo $this->default_language_id; ?>.value == "") {
                        error_message = error_message + "* The 'Tax message' entry must be entered.\n";
                        error = 1;
                    }

                    if (document.menu_form.orders_tax_percentage) {
                        if (document.menu_form.orders_tax_percentage.value == "" || document.menu_form.orders_tax_percentage.value < 0) {
                            error_message = error_message + "* The 'Tax' entry must be entered and more than 0 value.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.start_datetime) {
                        if (!validateDate(document.menu_form.start_datetime.value)) {
                            error_message = error_message + "* The 'Start date' is not a valid date format as requested.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.company_name) {
                        if (document.menu_form.company_name.value == "") {
                            error_message = error_message + "* The 'Company Name' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.address_1) {
                        if (document.menu_form.address_1.value == "") {
                            error_message = error_message + "* The 'Address Line 1' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.contact) {
                        if (document.menu_form.contact.value == "") {
                            error_message = error_message + "* The 'Contact' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.website) {
                        if (document.menu_form.website.value == "") {
                            error_message = error_message + "* The 'Website' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.tax_invoice_title) {
                        if (document.menu_form.tax_invoice_title.value == "") {
                            error_message = error_message + "* The 'Tax Invoice Title' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.tax_registration_name) {
                        if (document.menu_form.tax_registration_name.value == "") {
                            error_message = error_message + "* The 'Tax Registration Name' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    if (document.menu_form.gst_registration_no) {
                        if (document.menu_form.gst_registration_no.value == "") {
                            error_message = error_message + "* The 'Tax Registration No' entry must be entered.\n";
                            error = 1;
                        }
                    }

                    <?php if (!$edit_mode) { ?>
                        if (document.menu_form.company_logo) {
                            if (document.menu_form.company_logo.value == "") {
                                error_message = error_message + "* The 'Company Logo' entry must be uploaded.\n";
                                error = 1;
                            }
                        }
                    <?php } ?>

                    if (error == 1) {
                        alert(error_message);
                        return false;
                    } else {
                        if (old_tax_percentage != document.menu_form.orders_tax_percentage.value) {
                            var change_message = confirm("Confirm Tax Message has been changed?\n");
                            if (!change_message) {
                                return false;
                            }
                        }
                        return true;
                    }
                }
                //-->
            </script>

            <?= tep_draw_form('menu_form', FILENAME_GST_CONFIGURATION, 'action=add', 'post', ' onSubmit="return check_form();" id="menu_form" enctype="multipart/form-data"') ?>
            <table cellspacing="2" cellpadding="2" border="0">
                <tr>
                    <td>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <td class="pageHeading" valign="top">
                                    <?php
                                    if ($id != "") {
                                        echo HEADING_EDIT_MENU;
                                        echo tep_draw_hidden_field('id', $id);
                                    } else {
                                        echo HEADING_ADD_MENU;
                                    }
                                    ?>
                                </td>
                                <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table border="0" cellpadding="2" cellspacing="2">
                            <tr>
                                <td class="main" valign=top><?php echo ENTRY_IP_COUNTRY; ?></td>
                                <td class="main">
                                    <?= tep_draw_pull_down_menu('countries_id', tep_get_countries(), $orders_tax_config_row['countries_id']); ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top><?php echo ENTRY_TAX_TITLE; ?></td>
                                <td class="main">
                                    <div id="languages_tab">
                                        <ul>
                                            <?php
                                            for ($i = 0, $n = sizeof($this->language_obj); $i < $n; $i++) {
                                                echo '<li id="languages_li_' . $i . '"><a href="#languages_tab_' . $i . '"><span>' . $this->language_obj[$i]['name'] . '</span></a></li>';
                                            }
                                            ?>
                                        </ul>
                                        <?php
                                        for ($i = 0, $n = sizeof($this->language_obj); $i < $n; $i++) {
                                            $lang_id = $this->language_obj[$i]['languages_id'];
                                            ?>
                                            <div id="languages_tab_<?= $i ?>" class="languages_tab">
                                                <table border="0" width="90%" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <td class="main header" colspan="2"><b>Tax Title</b></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top">Short</td>
                                                        <td class="main">
                                                            <?= tep_draw_input_field('orders_tax_title_short_' . $lang_id, $orders_tax_config_row["description_short_{$lang_id}"], ' id="description_short_' . $lang_id . '" size="30" ') ?><br/>
                                                            <?php echo ($this->default_language_id == $lang_id ? '<span class="fieldRequired">* Required</span>' : ''); ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top">Long</td>
                                                        <td class="main">
                                                            <?php echo tep_draw_input_field('orders_tax_title_' . $lang_id, $orders_tax_config_row["description_{$lang_id}"], ' id="description_' . $lang_id . '" size="30" ') ?><br/>
                                                            <?php echo ($this->default_language_id == $lang_id ? '<span class="fieldRequired">* Required</span>' : ''); ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top">Message</td>
                                                        <td class="main">
                                                            <?php echo tep_draw_textarea_field('orders_tax_message_' . $lang_id, 'soft', '30', '5', $orders_tax_config_row["message_{$lang_id}"], ' id="message_' . $lang_id . '"') ?><br/>
                                                            <?php echo ($this->default_language_id == $lang_id ? '<span class="fieldRequired">* Required</span>' : ''); ?>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div><!--languages_tab-->
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top><?php echo ENTRY_TAX; ?></td>
                                <td class="main">
                                    <?= tep_draw_input_field('orders_tax_percentage', ($orders_tax_config_row['orders_tax_percentage']) ? $orders_tax_config_row['orders_tax_percentage'] : '0', ' id="sort_order" size="5" maxlength="5" ') . ENTRY_TAX_PERCENTAGE; ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Start Date<br/><small>(YYYY-MM-DD /<br/>
                                        YYYY-MM-DD HH:MM)</small></td>
                                <td class="main">
                                    <?php echo tep_draw_input_field('start_datetime', ($orders_tax_config_row['start_datetime']) ? date_format(date_create($orders_tax_config_row['start_datetime']), 'Y-m-d H:i') : '', ' id="start_datetime" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.menu_form.start_datetime); }"'); ?>
                                    <a href="javascript:void(0)" onclick="if (self.gfPop)
                                                        gfPop.fPopCalendar(document.menu_form.start_datetime);
                                                    return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Company Name</td>
                                <td class="main">
                                    <?= tep_draw_input_field('company_name', ($orders_tax_config_row['company_name']) ? $orders_tax_config_row['company_name'] : '', ' id="company_name" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Address Line 1</td>
                                <td class="main">
                                    <?= tep_draw_input_field('address_1', ($orders_tax_config_row['address_1']) ? $orders_tax_config_row['address_1'] : '', ' id="address_1" size="30" maxlength="64" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Address Line 2</td>
                                <td class="main">
                                    <?= tep_draw_input_field('address_2', ($orders_tax_config_row['address_2']) ? $orders_tax_config_row['address_2'] : '', ' id="address_2" size="30" maxlength="64" '); ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Address Line 3</td>
                                <td class="main">
                                    <?= tep_draw_input_field('address_3', ($orders_tax_config_row['address_3']) ? $orders_tax_config_row['address_3'] : '', ' id="address_3" size="30" maxlength="64" '); ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Contact</td>
                                <td class="main">
                                    <?= tep_draw_input_field('contact', ($orders_tax_config_row['contact']) ? $orders_tax_config_row['contact'] : '', ' id="contact" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Website</td>
                                <td class="main">
                                    <?= tep_draw_input_field('website', ($orders_tax_config_row['website']) ? $orders_tax_config_row['website'] : '', ' id="address_3" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Tax Invoice Title</td>
                                <td class="main">
                                    <?= tep_draw_input_field('tax_invoice_title', ($orders_tax_config_row['tax_invoice_title']) ? $orders_tax_config_row['tax_invoice_title'] : '', ' id="tax_invoice_title" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Tax Registration Name</td>
                                <td class="main">
                                    <?= tep_draw_input_field('tax_registration_name', ($orders_tax_config_row['tax_registration_name']) ? $orders_tax_config_row['tax_registration_name'] : '', ' id="tax_registration_name" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Tax Registration No</td>
                                <td class="main">
                                    <?= tep_draw_input_field('gst_registration_no', ($orders_tax_config_row['gst_registration_no']) ? $orders_tax_config_row['gst_registration_no'] : '', ' id="gst_registration_no" size="30" maxlength="32" '); ?>
                                    <span class="fieldRequired">* Required</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top>Company Logo</td>
                                <td class="main">
                                    <?php echo tep_draw_input_field('company_logo', '', 'size="40"', false, 'file'); ?>
                                    <?php if (!$edit_mode) { ?><span class="fieldRequired">* Required</span><?php } ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top><?php echo ENTRY_PROVIDE_INVOICE_STATUS; ?></td>
                                <td class="main">
                                    <?php echo tep_draw_radio_field('orders_provide_invoice_status', '1', $orders_provide_invoice_status_active, '', 'id="orders_provide_invoice_status_active"') . '&nbsp;' . TEXT_ACTIVE . '&nbsp;' . tep_draw_radio_field('orders_provide_invoice_status', '0', $orders_provide_invoice_status_inactive, '', 'id="orders_provide_invoice_status_inactive"') . '&nbsp;' . TEXT_INACTIVE; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="main" valign=top><?php echo ENTRY_STATUS; ?></td>
                                <td class="main">
                                    <?php echo tep_draw_radio_field('orders_tax_status', '1', $orders_tax_status_active, '', 'id="orders_tax_status_active"') . '&nbsp;' . TEXT_ACTIVE . '&nbsp;' . tep_draw_radio_field('orders_tax_status', '0', $orders_tax_status_inactive, '', 'id="orders_tax_status_inactive"') . '&nbsp;' . TEXT_INACTIVE; ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                </tr>
                <tr>
                    <td class="main" align="right">
                        <?= tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton') ?>
                        <?= tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_GST_CONFIGURATION), '', 'inputButton') ?>
                    </td>
                </tr>
                <tr>
                    <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                </tr>
            </table>
        </form>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    public function addEntry($id = "") {
        global $messageStack;

        $error = 0;
        $error_msg = '';
        $data = array();
        $previousLogo = '';

        // country code
        $countries_code_select_sql = "SELECT countries_iso_code_2 FROM " . TABLE_COUNTRIES . " WHERE countries_id  = '" . tep_db_prepare_input($_POST['countries_id']) . "'";
        $countries_code_result_sql = tep_db_query($countries_code_select_sql);
        $countries_code_row = tep_db_fetch_array($countries_code_result_sql);

        $default_orders_tax_title = tep_not_null($_POST["orders_tax_title_{$this->default_language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_{$this->default_language_id}"]) : '';
        $default_orders_tax_title_short = tep_not_null($_POST["orders_tax_title_short_{$this->default_language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_short_{$this->default_language_id}"]) : '';
        $default_orders_tax_message = tep_not_null($_POST["orders_tax_message_{$this->default_language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_message_{$this->default_language_id}"]) : '';

        if (tep_not_null($countries_code_row['countries_iso_code_2'])) {
            $gst_select_sql = "	SELECT c.countries_name, otcd.orders_tax_title 
								FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
								INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd 
									ON otcd.orders_tax_id = otc.orders_tax_id 
								LEFT JOIN " . TABLE_COUNTRIES . " AS c 
									ON c.countries_iso_code_2 = otc.country_code 
								WHERE otc.country_code = '" . $countries_code_row['countries_iso_code_2'] . "'" .
                    (tep_not_null($id) ? " AND otc.orders_tax_id != '{$id}'" : "");
            $gst_result_sql = tep_db_query($gst_select_sql);

            if ($rows = tep_db_fetch_array($gst_result_sql)) {
                $error = 1;
                $error_msg = $rows['countries_name'];
            }

            if ($error == 0) {
                $data = array(
                    'country_code' => $countries_code_row['countries_iso_code_2'],
                    'orders_tax_percentage' => tep_not_null($_POST['orders_tax_percentage']) ? tep_db_prepare_input($_POST['orders_tax_percentage']) : 0,
                    'orders_tax_status' => tep_not_null($_POST['orders_tax_status']) ? tep_db_prepare_input($_POST['orders_tax_status']) : 0,
                    'orders_provide_invoice_status' => tep_not_null($_POST['orders_provide_invoice_status']) ? tep_db_prepare_input($_POST['orders_provide_invoice_status']) : 0,
                    'start_datetime' => tep_not_null($_POST['start_datetime']) ? tep_db_prepare_input($_POST['start_datetime']) : 0,
                    'address_1' => tep_not_null($_POST['address_1']) ? tep_db_prepare_input($_POST['address_1']) : '',
                    'address_2' => tep_not_null($_POST['address_2']) ? tep_db_prepare_input($_POST['address_2']) : '',
                    'address_3' => tep_not_null($_POST['address_3']) ? tep_db_prepare_input($_POST['address_3']) : '',
                    'contact' => tep_not_null($_POST['contact']) ? tep_db_prepare_input($_POST['contact']) : '',
                    'website' => tep_not_null($_POST['website']) ? tep_db_prepare_input($_POST['website']) : '',
                    'tax_invoice_title' => tep_not_null($_POST['tax_invoice_title']) ? tep_db_prepare_input($_POST['tax_invoice_title']) : '',
                    'tax_registration_name' => tep_not_null($_POST['tax_registration_name']) ? tep_db_prepare_input($_POST['tax_registration_name']) : '',
                    'gst_registration_no' => tep_not_null($_POST['gst_registration_no']) ? tep_db_prepare_input($_POST['gst_registration_no']) : '',
                    'company_name' => tep_not_null($_POST['company_name']) ? tep_db_prepare_input($_POST['company_name']) : ''
                );
                    // 'company_logo' => tep_not_null($_POST['company_logo']) ? tep_db_prepare_input($_POST['company_logo']) : '' );
                $remarks_string = '';
                
                if ($id) {
                    //log changes start
                    $remarks_string .= 'Changes made from : ';

                    $orders_tax_config_select_sql = "	SELECT otc.orders_tax_id, c.countries_id, c.countries_name, 
                                                        otc.orders_tax_percentage, otc.orders_tax_status, otc.address_1,
                                                        otc.address_2, otc.address_3, otc.contact, otc.website, otc.tax_invoice_title,
                                                        otc.tax_registration_name, otc.gst_registration_no, otc.company_name, otc.company_logo,
                                                        otc.orders_provide_invoice_status
												        FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
												        LEFT JOIN " . TABLE_COUNTRIES . " AS c 
													       ON c.countries_iso_code_2 = otc.country_code 
												        WHERE otc.orders_tax_id = '" . $id . "'";
                    $orders_tax_config_result_sql = tep_db_query($orders_tax_config_select_sql);
                    $orders_tax_config_row = tep_db_fetch_array($orders_tax_config_result_sql);

                    foreach ($data as $dataKey => $dataValue) {
                        if (isset($orders_tax_config_row[$dataKey]) && $orders_tax_config_row[$dataKey] != $dataValue) {
                            $remarks_string .= '<b>' . $dataKey . '</b>: ' . $orders_tax_config_row[$dataKey] . ' --> ' . $dataValue . ' , <br/>';
                        }
                    }

                    foreach ($this->language_obj as $language_rows) {
                        $language_id = $language_rows['languages_id'];
                        $dataTitle = tep_not_null($_POST["orders_tax_title_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_{$language_id}"]) : $default_orders_tax_title;
                        $dataTitleShort = tep_not_null($_POST["orders_tax_title_short_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_short_{$language_id}"]) : $default_orders_tax_title_short;
                        $dataMessage = tep_not_null($_POST["orders_tax_message_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_message_{$language_id}"]) : $default_orders_tax_message;

                        $orders_tax_config_desc_select_sql = "	SELECT orders_tax_title, orders_tax_title_short 
                                                                FROM " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " 
                                                                WHERE language_id = '" . $language_id . "'
                                                                        AND orders_tax_id = '" . $id . "'";
                        $orders_tax_config_desc_result_sql = tep_db_query($orders_tax_config_desc_select_sql);

                        if ($orders_tax_config_desc_rows = tep_db_fetch_array($orders_tax_config_desc_result_sql)) {
                            $orders_tax_config_row["description_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_title'];
                            $orders_tax_config_row["description_short_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_title_short'];
                            $orders_tax_config_row["message_{$language_id}"] = $orders_tax_config_desc_rows['orders_tax_message'];

                            if ($orders_tax_config_row["description_{$language_id}"] != $dataTitle) {
                                $remarks_string .= '<b>orders_tax_title</b> : ' . $orders_tax_config_row["description_{$language_id}"] . ' --> ' . $dataTitle . ' , <br/>';
                            }

                            if ($orders_tax_config_row["description_short_{$language_id}"] != $dataTitleShort) {
                                $remarks_string .= '<b>orders_tax_title_short</b> : ' . $orders_tax_config_row["description_short_{$language_id}"] . ' --> ' . $dataTitleShort . ' , <br/>';
                            }

                            if ($orders_tax_config_row["message_{$language_id}"] != $dataMessage) {
                                $remarks_string .= '<b>orders_tax_message</b> : ' . $orders_tax_config_row["message_{$language_id}"] . ' --> ' . $dataMessage . ' , <br/>';
                            }
                        }
                    }
                    //log changes end

                    $editEntry = true;
                    tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION, $data, 'update', "orders_tax_id='" . $id . "'");

                    $orders_tax_config_desc_delete_sql = "DELETE FROM " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " WHERE orders_tax_id = '$id'";
                    tep_db_query($orders_tax_config_desc_delete_sql);

                    $previousLogo = $orders_tax_config_row['company_logo'];

                    $log_action = 'MODIFY';
                } else {
                    tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION, $data, 'insert');
                    $id = tep_db_insert_id();
                    $log_action = 'CREATE';
                }

                if (tep_not_null($id)) {
                    $this->setOrderTaxID($id);

                    foreach ($this->language_obj as $language_rows) {
                        $data = array();
                        $orders_tax_title = '';
                        $language_id = $language_rows["languages_id"];

                        $data = array(
                            'orders_tax_id' => $id,
                            'language_id' => tep_db_prepare_input($language_rows['languages_id']),
                            'orders_tax_title' => tep_not_null($_POST["orders_tax_title_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_{$language_id}"]) : $default_orders_tax_title,
                            'orders_tax_title_short' => tep_not_null($_POST["orders_tax_title_short_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_title_short_{$language_id}"]) : $default_orders_tax_title_short,
                            'orders_tax_message' => tep_not_null($_POST["orders_tax_message_{$language_id}"]) ? tep_db_prepare_input($_POST["orders_tax_message_{$language_id}"]) : $default_orders_tax_message
                        );

                        tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION, $data, 'insert');
                    }


                    if (tep_not_null($_FILES['company_logo']['name'])) {
                        $uploadResult = $this->uploadDocument('company_logo', $previousLogo);

                        if ($uploadResult) {
                            $remarks_string .= '<b>company_logo</b> : ' . $previousLogo . ' --> ' . $uploadResult . ' , <br/>';
                        }
                    }

                    $this->log($id, $log_action, $remarks_string);

                    $messageStack->add_session(SUCCESS_TAX_CONFIG_SAVED_SUCCESSFULLY, 'success');
                }
            } else {
                $messageStack->add_session(sprintf(ERROR_TITLE_EXIST, $error_msg), 'error');
            }
        }
    }

    public function deleteEntry($id = "") {
        if (tep_not_null($id)) {
            $orders_tax_config_delete_sql = "DELETE FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " WHERE orders_tax_id = '$id'";
            $orders_tax_config_result_sql = tep_db_query($orders_tax_config_delete_sql);

            $orders_tax_config_desc_delete_sql = "DELETE FROM " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " WHERE orders_tax_id = '$id'";
            $orders_tax_config_desc_result_sql = tep_db_query($orders_tax_config_desc_delete_sql);

            if (($orders_tax_config_result_sql == 1) && ($orders_tax_config_desc_result_sql == 1)) {
                $this->log($id, 'DELETE', '');
                return "success";
            }
        } else {
            return "";
        }
    }

    private function log($orders_tax_id, $action = 'CREATE', $remarks) {//add changes
        $insert_data_array = array(
            'log_users_id' => $this->getLoginUserID(),
            'log_orders_tax_id' => $orders_tax_id,
            'log_remarks' => $remarks,
            'log_IP' => tep_get_ip_address(),
            'log_datetime' => 'now()',
            'log_action' => $action
        );

        return tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION_LOG, $insert_data_array);
    }

    private function getOrderTaxID() {
        return (int) $this->order_tax_id;
    }

    public function setOrderTaxID($order_tax_id) {
        if (tep_not_null($order_tax_id)) {
            $this->order_tax_id = $order_tax_id;
        }
    }

    private function getLoginUserID() {
        return $this->log_users_id;
    }

    private function setLoginUserID($user_id) {
        if (tep_not_null($user_id)) {
            $this->log_users_id = $user_id;
        }
    }

    private function uploadDocument($file_index, $previous_logo = '') {
        $return = '';

        if ($validated = $this->validateImageFile($file_index)) {

            require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

            $aws_obj = new ogm_amazon_ws();
            $aws_obj->set_bucket_key(self::$aws_s3_setting['bucket']);
            $aws_obj->set_storage(self::$aws_s3_setting['storage']);
            $aws_obj->set_acl(self::$aws_s3_setting['acl']);

            if ($aws_obj->is_aws_s3_enabled() && $aws_obj->is_s3_bucket_config_enabled(self::$aws_s3_setting['bucket'])) {
                $path_parts = pathinfo($_FILES[$file_index]['name']);
                $ext = strtolower($path_parts['extension']);
                $server_filename = date("YmdHis") . '.' . $ext;

                $server_path = self::$aws_s3_setting['source_path'] . '/';

                $content = file_get_contents($_FILES[$file_index]['tmp_name']);

                $aws_obj->set_filename($server_filename);
                $aws_obj->set_filepath($server_path);
                $aws_obj->set_file_content($content);

                if ($aws_obj->save_file()) {
                    $filename = $aws_obj->aws_filename;

                    if ($previous_logo != '') {
                        //delete previous image                    
                        $aws_obj->delete_file('', $previous_logo, self::$aws_s3_setting['bucket']);
                    }

                    $newFilename = $server_path . $filename;
                    $this->update_filename($newFilename);
                    $return = $newFilename;
                } else {
                    // aws obj will report error
                }
            } else {
                $this->reportError(array('AWS S3 is not enabled or the bucket is not enabled.', 'bucket' => $aws_obj->aws_bucket_key), 'uploadDocument()');
            }
        }

        return $return;
    }

    private function validateImageFile($file_index) {
        global $messageStack;
        $return_bool = FALSE;

        if (isset($_FILES[$file_index])) {
            $file = $_FILES[$file_index];
            if (tep_not_null($file['tmp_name']) && ($file['tmp_name'] != 'none') && is_uploaded_file($file['tmp_name'])) {

                if (sizeof(self::$image_supported['type']) > 0) {
                    $path_parts = pathinfo($file['name']);
                    $ext = strtolower($path_parts['extension']);

                    if (!in_array($ext, self::$image_supported['type'])) {
                        if ($this->message_location == 'direct') {
                            $messageStack->add(ERROR_FILETYPE_NOT_ALLOWED, 'error');
                        } else {
                            $messageStack->add_session(ERROR_FILETYPE_NOT_ALLOWED, 'error');
                        }
                    } else {
                        $return_bool = TRUE;
                    }
                } else {
                    $return_bool = TRUE;
                }
            }
        } else {
            if ($this->message_location == 'direct') {
                $messageStack->add(WARNING_NO_FILE_UPLOADED, 'warning');
            } else {
                $messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
            }
        }

        return $return_bool;
    }

    private function update_filename($filename) {

        $update_data_array = array(
            'company_logo' => $filename
        );

        tep_db_perform(TABLE_ORDERS_TAX_CONFIGURATION, $update_data_array, "update", " orders_tax_id=" . $this->getOrderTaxID());
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Tax Configuration Error - ' . date("F j, Y H:i");

        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}
?>