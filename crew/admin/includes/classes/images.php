<?
////////////////////////IMAGE TEXT CLASS//////////////////////////
class imagetext{
	var $handle;
	var $dx;
	var $dy;
	
	function imagetext($fontfile, $fontsize, $fontcolor, $textstr, $angle=0, $bgcolor="") {
		$text=stripslashes($textstr);
		// font color
		$r = hexdec(substr($fontcolor, 0, 2));
		$g = hexdec(substr($fontcolor, 2, 2));
		$b = hexdec(substr($fontcolor, 4, 2));
		
		// bg color
		if ($bgcolor && $fontcolor!=$bgcolor){
			$rb = hexdec(substr($bgcolor, 0, 2));
			$gb = hexdec(substr($bgcolor, 2, 2));
			$bb = hexdec(substr($bgcolor, 4, 2));
		}else{
			$r>128?$rb=0:$rb=255;
			$g>128?$gb=0:$gb=255;
			$b>128?$bb=0:$bb=255;
		}
		
		//$fontsize = $fontsize/96*72;
		$size = imagettfbbox($fontsize,$angle,$fontfile,$text);
		$factor = $fontsize/2;
		$this->dx = $factor + max($size[2], $size[4]) - min($size[0], $size[6]); // extreme x values
		$this->dy = $factor + max($size[1], $size[3]) - min($size[5], $size[7]); // extreme y values
		
		$this->handle = imagecreate($this->dx,$this->dy);
		$transcolor = ImageColorAllocate ($this->handle, $rb, $gb, $bb);
		imagefill($this->handle,0,0,$transcolor);
		$textcolor = ImageColorAllocate($this->handle, $r, $g, $b);
		$offsety = $this->dy-($factor);
		$offsetx = $factor/2+($factor*$angle)/60;
		
		ImageTTFText($this->handle, $fontsize, $angle, $offsetx, $offsety, $textcolor, $fontfile, $text);
		imagecolortransparent($this->handle, $transcolor);
	}
	
	function preview() {
		header("Content-type: image/png");
		imagepng($this->handle);
	}
}

////////////////////////MAIN IMAGE CLASS//////////////////////////
class imageobject{
	var $handle;
	var $type="jpg";
	var $height=0;
	var $width=0;
	var $string;// for img height/width tags
	// output message
	var $message;
	// current
	var $directory;
	var $filename;
	var $newfilename;
	//output
	var $resample=false;
	var $quality="95";
	var $output="jpg";// alternatives png8 or png
	// textobject
	var $message_location;
	
	function imageobject($directory,$filename,$type="jpg"){
		$this->directory = $directory;
		$this->filename = $filename;
		$this->newfilename = '';
		$this->type = $type;
		$this->set_output_messages('direct');
		
		if (file_exists($directory.$filename)){
			$this->filesize = ceil(filesize($directory.$filename)/1024);
			$size = GetImageSize($directory.$filename);
			if ($size) {
				$this->handle = $this->getHandle($directory.$filename,$size[2]);
			}
			$this->width = $size[0];
			$this->height = $size[1];
			$this->string = $size[3];
		}
	}
	
	function getHandle($name,&$type){
		switch ($type){
			case 1:
				$im = imagecreatefromgif($name);
				$this->type= "gif";
				break;
			case 2:
                ini_set('gd.jpeg_ignore_warning', 1);
				$im = imagecreatefromjpeg($name);
				break;
			case 3:
				$im = imagecreatefrompng($name);
				$this->type= "png";
				break;
		}
		return $im;
	}
	
    function uniqueName() {
		$add="";
		$fileparts = split_dep("\.",$this->filename);
		$nonchr = array("__","0","1","2","3","4","5","6","7","8","9");
		$desc = str_replace($nonchr,"",$fileparts[0]);
		$name = $desc."__".date("YmdHms");
		
		if (file_exists($this->directory.$name.".".$this->type)){
			$add = 1;
			while(file_exists($this->directory.$name.$add.".".$this->type)) $add++;
		}
		return $imgnew.$name.$add.".".$this->type;
    }
	
	function createUnique($imgnew) {
		$this->type = substr($this->output,0,3);
		
		if ($this->newfilename == '') {
			$unique_str = $this->uniqueName();
		} else {
			$unique_str = $this->newfilename.".".$this->type;
		}
		
		switch ($this->type) {
			case "png":
				imagepng($imgnew,$this->directory.$unique_str);
				break;
			default:
				imagejpeg($imgnew,$this->directory.$unique_str,$this->quality);
				break;
		}
		
		imagedestroy($this->handle);
		$newobject = new imageobject($this->directory,$unique_str,$this->type);
		return $newobject;
	}

    function createImage($new_w,$new_h) {
		if (function_exists("imagecreatetruecolor") && $this->output!="png8") {
			return imagecreatetruecolor($new_w,$new_h);
		} else {
			return imagecreate($new_w,$new_h);
		}
    }
	
    function copyhandle(&$dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h) {
		if ($this->output=="png8" && $this->type="jpg"){
			imagecopyresized($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);
			$this->resample==true;
		}
		
		if (function_exists("imagecopyresampled") && $this->resample==true)
			imagecopyresampled($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);
		else
			imagecopy($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $src_w, $src_h);
    }

    function copycreatehandle(&$src_im, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h) {
		$dst_im = $this->createImage($dst_w,$dst_h);
		$this->copyhandle($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);
		return $dst_im;
    }
	
	function hex2rgb($c) {
		if(!$c) return false;
		$c = trim($c);
		$out = false;
		
		if (preg_match("/^[0-9ABCDEFabcdef\#]+$/i", $c)) {
			$c = str_replace('#','', $c);
			$l = strlen($c) == 3 ? 1 : (strlen($c) == 6 ? 2 : false);
		
			if ($l) {
				unset($out);
				$out['r'] = hexdec(substr($c, 0,1*$l));
				$out['g'] = hexdec(substr($c, 1*$l,1*$l));
				$out['b'] = hexdec(substr($c, 2*$l,1*$l));
			} else {
				$out = false;
			}
		} else {
			$out = false;
		}
		
		return $out;
	} 

    function saveAlpha(&$handle) {
		ImageAlphaBlending($handle, true);
		imagesavealpha($handle,false);
		imagesavealpha($handle,true);
    }

    function getHexColor($xpos,$ypos) {
		$color = imagecolorat($this->handle, $xpos, $ypos);
		$colorrgb = imagecolorsforindex($this->handle,$color);
		
		if ($colorrgb["red"]>0)	$hred = dechex($colorrgb["red"]); else $hred = "00";
		if (strlen($hred)<2)	$hred = "0".$hred;
		
		if ($colorrgb["green"]>0)	$hgreen = dechex($colorrgb["green"]); else $hgreen = "00";
		if (strlen($hgreen)<2)	$hgreen = "0".$hgreen;
		
		if ($colorrgb["blue"]>0)	$hblue = dechex($colorrgb["blue"]); else $hblue = "00";
		if (strlen($hblue)<2)	$hblue = "0".$hblue;
		
		return strtoupper($hred.$hgreen.$hblue);
    }
    
    function rotateImage($degrees) {
		if ($degrees == 180){
			$dst_img = @imagerotate($this->handle, $degrees, 0);
		}else{
			$width = $this->width;
			$height = $this->height;
			if ($width > $height) {
				$size = $width;
			} else {	
				$size = $height;
			}
			$dst_img = $this->createImage($size, $size);
			$this->copyhandle($dst_img, $this->handle, 0, 0, 0, 0, $width, $height,$width, $height);
			$dst_img  = @imagerotate($dst_img, $degrees, 0);
			$this->handle = $dst_img;
			$dst_img = $this->createImage($height, $width);
			if ((($degrees == 90) && ($width > $height)) || (($degrees == 270) && ($width < $height)))
				$this->copyhandle($dst_img, $this->handle, 0, 0, 0, 0, $size, $size, $size, $size);
			if ((($degrees == 270) && ($width > $height)) || (($degrees == 90) && ($width < $height)))
				$this->copyhandle($dst_img, $this->handle, 0, 0, $size - $height, $size - $width, $size, $size, $size, $size);
		}
		
		return $this->createUnique($dst_img);
    }

    function resizeImage($scale=false,$newwidth=0,$newheight=0) {
		$new_w = $this->width;
		$new_h = $this->height;
		$aspect_ratio = (int) $new_h / $new_w;
		if ($scale) $new_w = $new_w * $scale;
		if ($newwidth>0) $new_w = $newwidth;
		if ($newheight>0){
			$new_h = $newheight;
			$new_w = (int) $new_h / $aspect_ratio;
		}else{
			$new_h = abs($new_w * $aspect_ratio);
		}
		$dst_img = $this->copycreatehandle($this->handle, 0, 0, 0, 0, $new_w, $new_h, $this->width,$this->height);
		
		return $this->createUnique($dst_img);
    }

    function cropImage($top,$right,$bottom,$left) {
		$new_w = $right - $left;
		$new_h = $bottom - $top;

		$dst_img = $this->copycreatehandle($this->handle, 0, 0, $left, $top, $new_w, $new_h, $new_w, $new_h);
		return $this->createUnique($dst_img);
    }

	function mirrorImage() {
		$imgnew = $this->createImage($this->width,$this->height);
		// horizontal
		for ($i=0;$i<$this->height;$i++){
			// vertical
			for ($j=0;$j<$this->width;$j++){
				$color = imagecolorat($this->handle, $j, $i);
				imagesetpixel($imgnew,$this->width-$j-1,$i,$color);
			}
		}
		return $this->createUnique($imgnew);
	}
	
	function writeText($xpos=0, $ypos=0, $textstring, $fontsize, $truetype, $fontcolor, $fontangle) {
		$fontbgcolor= $this->getHexColor($xpos,$ypos);
		$textimage = new imagetext($truetype,$fontsize,$fontcolor,$textstring,$fontangle,$fontbgcolor);
		$this->saveAlpha($this->handle);
		$this->copyhandle($this->handle, $textimage->handle, $xpos, $ypos, 0, 0, $textimage->dx, $textimage->dy,$textimage->dx, $textimage->dy);
		
		return $this->createUnique($this->handle);
	}
	
	function mergeImage($dir, $srcimage, $srcx, $srcy, $opacity=100) {
		$newimage = new imageobject($dir, $srcimage);
		$this->saveAlpha($this->handle);
		if ($opacity<100)
			@ImageCopyMerge($this->handle,$newimage->handle,$srcx,$srcy,0,0,$newimage->width,$newimage->height,$opacity);
		else
			$this->copyhandle($this->handle,$newimage->handle,$srcx,$srcy,0,0,$newimage->width,$newimage->height,$newimage->width,$newimage->height);
		
		return $this->createUnique($this->handle);
	}
	
	function mergeColor($top,$right,$bottom,$left,$color) {
		$new_w = $right - $left;
		$new_h = $bottom - $top;
		$rgb = $this->hex2rgb($color);
		$dst_img = $this->copycreatehandle($this->handle, 0, 0, 0, 0, $this->width, $this->height, $this->width, $this->height);//$this->copycreatehandle($this->handle, 0, 0, $left, $top, $new_w, $new_h, $new_w, $new_h);
		
		$newimage = ImageCreate($new_w, $new_h);
		$r = $rgb['r'];
		$g = $rgb['g'];
		$b = $rgb['b'];
		$mergecolor = ImageColorAllocate($newimage, $rgb['r'], $rgb['g'], $rgb['b']);
		ImageCopyMerge($this->handle,$newimage,$left,$top,0,0,$new_w,$new_h,100);
		
		return $this->createUnique($this->handle);
	}
		
	function getNamedColors($color="") {
		ob_start();
?>
		<option value="F0F8FF" style="background-color:#F0F8FF;">Aliceblue
		<option value="FAEBD7" style="background-color:#FAEBD7;">Antiquewhite
		<option value="00FFFF" style="background-color:#00FFFF;">Aqua
		<option value="7FFFD4" style="background-color:#7FFFD4;">Aquamarine
		<option value="F0FFFF" style="background-color:#F0FFFF;">Azure
		<option value="F5F5DC" style="background-color:#F5F5DC;">Beige
		<option value="FFE4C4" style="background-color:#FFE4C4;">Bisque
		<option value="000000" style="background-color:black;color:white;" <? if ($color=="black") echo "selected"; ?>>Black
		<option value="FFEBCD" style="background-color:#FFEBCD;">Blanchedalmond
		<option value="0000FF" style="background-color:#0000FF;color:white;">Blue
		<option value="8A2BE2" style="background-color:#8A2BE2;">Blueviolet
		<option value="A52A2A" style="background-color:#A52A2A;">Brown
		<option value="DEB887" style="background-color:#DEB887;">Burlywood
		<option value="5F9EA0" style="background-color:#5F9EA0;">Cadetblue
		<option value="7FFF00" style="background-color:#7FFF00;">Chartreuse
		<option value="D2691E" style="background-color:#D2691E;">Chocolate
		<option value="FF7F50" style="background-color:#FF7F50;">Coral
		<option value="6495ED" style="background-color:#6495ED;">Cornflowerblue
		<option value="FFF8DC" style="background-color:#FFF8DC;">Cornsilk
		<option value="DC143C" style="background-color:#DC143C;">Crimson
		<option value="00FFFF" style="background-color:#00FFFF;">Cyan
		<option value="00008B" style="background-color:#00008B;color:white;">Darkblue
		<option value="008B8B" style="background-color:#008B8B;">Darkcyan
		<option value="B8860B" style="background-color:#B8860B;">Darkgoldenrod
		<option value="A9A9A9" style="background-color:#A9A9A9;">Darkgray
		<option value="006400" style="background-color:#006400;">Darkgreen
		<option value="BDB76B" style="background-color:#BDB76B;">Darkkhaki
		<option value="8B008B" style="background-color:#8B008B;">Darkmagenta
		<option value="556B2F" style="background-color:#556B2F;">Darkolivegreen
		<option value="FF8C00" style="background-color:#FF8C00;">Darkorange
		<option value="9932CC" style="background-color:#9932CC;">Darkorchid
		<option value="8B0000" style="background-color:#8B0000;">Darkred
		<option value="E9967A" style="background-color:#E9967A;">Darksalmon
		<option value="8FBC8F" style="background-color:#8FBC8F;">Darkseagreen
		<option value="483D8B" style="background-color:#483D8B;">Darkslateblue
		<option value="2F4F4F" style="background-color:#2F4F4F;">Darkslategray
		<option value="00CED1" style="background-color:#00CED1;">Darkturquoise
		<option value="9400D3" style="background-color:#9400D3;">Darkviolet
		<option value="FF1493" style="background-color:#FF1493;">Deeppink
		<option value="00BFFF" style="background-color:#00BFFF;">Deepskyblue
		<option value="696969" style="background-color:#696969;">Dimgray
		<option value="1E90FF" style="background-color:#1E90FF;">Dodgerblue
		<option value="B22222" style="background-color:#B22222;">Firebrick
		<option value="FFFAF0" style="background-color:#FFFAF0;">Floralwhite
		<option value="228B22" style="background-color:#228B22;">Forestgreen
		<option value="FF00FF" style="background-color:#FF00FF;">Fuchsia
		<option value="DCDCDC" style="background-color:#DCDCDC;">Gainsboro
		<option value="F8F8FF" style="background-color:#F8F8FF;">Ghostwhite
		<option value="FFD700" style="background-color:#FFD700;">Gold
		<option value="DAA520" style="background-color:#DAA520;">Goldenrod
		<option value="808080" style="background-color:#808080;">Gray
		<option value="008000" style="background-color:#008000;">Green
		<option value="ADFF2F" style="background-color:#ADFF2F;">Greenyellow
		<option value="F0FFF0" style="background-color:#F0FFF0;">Honeydew
		<option value="FF69B4" style="background-color:#FF69B4;" <? if ($color=="pink") echo "selected"; ?>>Hotpink
		<option value="CD5C5C" style="background-color:#CD5C5C;">Indianred
		<option value="4B0082" style="background-color:#4B0082;">Indigo
		<option value="FFFFF0" style="background-color:#FFFFF0;">Ivory
		<option value="F0E68C" style="background-color:#F0E68C;">Khaki
		<option value="E6E6FA" style="background-color:#E6E6FA;">Lavender
		<option value="FFF0F5" style="background-color:#FFF0F5;">Lavenderblush
		<option value="7CFC00" style="background-color:#7CFC00;">Lawngreen
		<option value="FFFACD" style="background-color:#FFFACD;">Lemonchiffon
		<option value="ADD8E6" style="background-color:#ADD8E6;">Lightblue
		<option value="F08080" style="background-color:#F08080;">Lightcoral
		<option value="E0FFFF" style="background-color:#E0FFFF;">Lightcyan
		<option value="FAFAD2" style="background-color:#FAFAD2;">Lightgoldenrodyellow
		<option value="90EE90" style="background-color:#90EE90;">Lightgreen
		<option value="D3D3D3" style="background-color:#D3D3D3;">Lightgrey
		<option value="FFB6C1" style="background-color:#FFB6C1;">Lightpink
		<option value="FFA07A" style="background-color:#FFA07A;">Lightsalmon
		<option value="20B2AA" style="background-color:#20B2AA;">Lightseagreen
		<option value="87CEFA" style="background-color:#87CEFA;">Lightskyblue
		<option value="778899" style="background-color:#778899;">Lightslategray
		<option value="B0C4DE" style="background-color:#B0C4DE;">Lightsteelblue
		<option value="FFFFE0" style="background-color:#FFFFE0;">Lightyellow
		<option value="00FF00" style="background-color:#00FF00;">Lime
		<option value="32CD32" style="background-color:#32CD32;">Limegreen
		<option value="FAF0E6" style="background-color:#FAF0E6;">Linen
		<option value="FF00FF" style="background-color:#FF00FF;">Magenta
		<option value="800000" style="background-color:#800000;color:white;">Maroon
		<option value="66CDAA" style="background-color:#66CDAA;">Mediumauqamarine
		<option value="0000CD" style="background-color:#0000CD;">Mediumblue
		<option value="BA55D3" style="background-color:#BA55D3;">Mediumorchid
		<option value="9370D8" style="background-color:#9370D8;">Mediumpurple
		<option value="3CB371" style="background-color:#3CB371;">Mediumseagreen
		<option value="7B68EE" style="background-color:#7B68EE;">Mediumslateblue
		<option value="00FA9A" style="background-color:#00FA9A;">Mediumspringgreen
		<option value="48D1CC" style="background-color:#48D1CC;">Mediumturquoise
		<option value="C71585" style="background-color:#C71585;">Mediumvioletred
		<option value="191970" style="background-color:#191970;color:white;">Midnightblue
		<option value="F5FFFA" style="background-color:#F5FFFA;">Mintcream
		<option value="FFE4E1" style="background-color:#FFE4E1;">Mistyrose
		<option value="FFE4B5" style="background-color:#FFE4B5;">Moccasin
		<option value="FFDEAD" style="background-color:#FFDEAD;">Navajowhite
		<option value="000080" style="background-color:#000080;color:white;">Navy
		<option value="FDF5E6" style="background-color:#FDF5E6;">Oldlace
		<option value="808000" style="background-color:#808000;">Olive
		<option value="688E23" style="background-color:#688E23;">Olivedrab
		<option value="FFA500" style="background-color:#FFA500;">Orange
		<option value="FF4500" style="background-color:#FF4500;">Orangered
		<option value="DA70D6" style="background-color:#DA70D6;">Orchid
		<option value="EEE8AA" style="background-color:#EEE8AA;">Palegoldenrod
		<option value="98FB98" style="background-color:#98FB98;">Palegreen
		<option value="AFEEEE" style="background-color:#AFEEEE;">Paleturquoise
		<option value="D87093" style="background-color:#D87093;">Palevioletred
		<option value="FFEFD5" style="background-color:#FFEFD5;">Papayawhip
		<option value="FFDAB9" style="background-color:#FFDAB9;">Peachpuff
		<option value="CD853F" style="background-color:#CD853F;">Peru
		<option value="FFC0CB" style="background-color:#FFC0CB;">Pink
		<option value="DDA0DD" style="background-color:#DDA0DD;">Plum
		<option value="B0E0E6" style="background-color:#B0E0E6;">Powderblue
		<option value="800080" style="background-color:#800080;">Purple
		<option value="FF0000" style="background-color:#FF0000;">Red
		<option value="BC8F8F" style="background-color:#BC8F8F;">Rosybrown
		<option value="4169E1" style="background-color:#4169E1;">Royalblue
		<option value="8B4513" style="background-color:#8B4513;">Saddlebrown
		<option value="FA8072" style="background-color:#FA8072;">Salmon
		<option value="F4A460" style="background-color:#F4A460;">Sandybrown
		<option value="2E8B57" style="background-color:#2E8B57;">Seagreen
		<option value="FFF5EE" style="background-color:#FFF5EE;">Seashell
		<option value="A0522D" style="background-color:#A0522D;">Sienna
		<option value="C0C0C0" style="background-color:#C0C0C0;">Silver
		<option value="87CEEB" style="background-color:#87CEEB;">Skyblue
		<option value="6A5ACD" style="background-color:#6A5ACD;">Slateblue
		<option value="708090" style="background-color:#708090;">Slategray
		<option value="FFFAFA" style="background-color:#FFFAFA;">Snow
		<option value="00FF7F" style="background-color:#00FF7F;">Springgreen
		<option value="4682B4" style="background-color:#4682B4;">Steelblue
		<option value="D2B48C" style="background-color:#D2B48C;">Tan
		<option value="008080" style="background-color:#008080;">Teal
		<option value="D8BFD8" style="background-color:#D8BFD8;">Thistle
		<option value="FF6347" style="background-color:#FF6347;">Tomato
		<option value="40E0D0" style="background-color:#40E0D0;">Turquoise
		<option value="EE82EE" style="background-color:#EE82EE;">Violet
		<option value="F5DEB3" style="background-color:#F5DEB3;">Wheat
		<option value="FFFFFF" style="background-color:#FFFFFF;">White
		<option value="F5F5F5" style="background-color:#F5F5F5;">Whitesmoke
		<option value="FFFF00" style="background-color:#FFFF00;">Yellow
		<option value="9ACD32" style="background-color:#9ACD32;">YellowGreen
		</option>
<?
		$color_options = ob_get_contents();
		ob_end_clean();
		return $color_options;
	}
	
	function writeOptions($min,$max,$sel=0) {
		$retval = '';
		for($i=$min;$i<=$max;$i++) {
			$retval .= "<option value=".$i;
			if ($i==$sel) $retval .= " selected";
				$retval .= ">".$i;
		}
		return $retval."</option>";
	}
	
	function setDirectory($newDirectory) {
		$this->directory = $newDirectory;
	}
	
	function setNewfilename($newFilename) {
		$this->newfilename = $newFilename;
	}
	
	function set_output_messages($location) {
    	switch ($location) {
        	case 'session':
          		$this->message_location = 'session';
          		break;
        	case 'direct':
        	default:
          		$this->message_location = 'direct';
          		break;
      	}
	}
}
?>