<?
/*
  	$Id: log.php,v 1.46 2014/04/17 10:25:25 june.see Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class log_files {
	var $identity;
	var $table = TABLE_LOG_TABLE;
	
	function log_files($identity)
	{
		$this->identity = $identity;
	}
	
	function insert_log($prod_id, $field_name, $from_val='', $to_val='', $admin_msg='', $user_msg='', $admin_email='', $notification = true)
	{
		$sql = sprintf(	'	INSERT INTO %s (log_admin_id, log_ip, log_time, log_products_id, log_system_messages, log_user_messages, log_field_name, log_from_value, log_to_value) ' . '
							VALUES ("%s", "%s", NOW(), %d, "%s", "%s", "%s", "%s", "%s")',
                     		$this->table, $this->identity, tep_db_input(getenv("REMOTE_ADDR")), $prod_id, tep_db_input($admin_msg),
                     		tep_db_input($user_msg), tep_db_input($field_name), tep_db_input($from_val), tep_db_input($to_val));
		tep_db_query($sql);
		
		if ($notification == true) {
			if ($field_name == 'products_price' || $field_name == 'products_other_price') {
				$products_price_select = "	SELECT products_cat_path, products_price, products_base_currency
		                          			FROM " . TABLE_PRODUCTS . "
								  			WHERE products_id = " . tep_db_input($prod_id) . "";
				$products_price_result = tep_db_query($products_price_select);
				
				if ($products_price_row = tep_db_fetch_array($products_price_result)) {
					$currencies = new currencies(); //to use the class function 
					
					$email_with_usd_conversion = false;
					$product_USD_selling_price = '';
					$other_price_array = array();
					
					$products_cat_path = $products_price_row['products_cat_path'];
					$products_price = $products_price_row['products_price'];
					$products_base_currency = $products_price_row['products_base_currency'];
					
					if ($products_base_currency == 'USD') {
						$product_USD_selling_price = $products_price;
					}
					
					if ($field_name == 'products_price'){
						$email_title = 'Product Price Changes';
						$products_price = preg_replace('/[^\d\.]/', '', $to_val);
					} else if ($field_name == 'products_other_price') {
						$email_title = 'Product Other Price Changes';
						$products_price = $products_price_row['products_price'];
					}
					
					$products_other_price_select = "	SELECT products_currency_prices_code, products_currency_prices_value
			                          					FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
									  					WHERE products_id = " . tep_db_input($prod_id) . "";
					$products_other_price_result = tep_db_query($products_other_price_select);
					
					while ($products_other_price_row = tep_db_fetch_array($products_other_price_result)) { //get other prices
						$other_price_array[] = $products_other_price_row['products_currency_prices_code'].' '.$products_other_price_row['products_currency_prices_value'];
						
						if ($products_other_price_row['products_currency_prices_code'] == 'USD') {
							$product_USD_selling_price = $products_other_price_row['products_currency_prices_value'];
						}
					}
					
					if (count($other_price_array) != 0) {
						$email_content_other_price = implode(', ', $other_price_array);
					} else {
						$email_content_other_price = 'n/a';
					}
					
					if (tep_not_null($product_USD_selling_price)) {	// Either has USD price from Base or Other Price
						$email_with_usd_conversion = false;
					} else {
						$email_with_usd_conversion = true;
						$product_USD_selling_price = $currencies->advance_currency_conversion($products_price, $products_base_currency, DEFAULT_CURRENCY);
					}
					
					$categories_id = tep_get_actual_product_cat_id($prod_id);
					$product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($categories_id) . '&pID=' . $prod_id . '&selected_box=catalog') . '"  target="_blank">'.$prod_id.'</a>';
			
					if ($email_with_usd_conversion){
						$product_price_changes_email_contents = sprintf(EMAIL_PRODUCT_PRICE_CHANGES_NOTIFICATION_CONTENT_WITHOUT_USD, $product_id_link, $products_cat_path, strip_tags(tep_get_products_name($prod_id)), $products_base_currency, $products_price, $product_USD_selling_price, $email_content_other_price, $from_val, $to_val, date("Y-m-d H:i:s"), tep_get_ip_address(), $admin_email , tep_get_admin_group_name($admin_email));
					} else {
						$product_price_changes_email_contents = sprintf(EMAIL_PRODUCT_PRICE_CHANGES_NOTIFICATION_CONTENT, $product_id_link, $products_cat_path, strip_tags(tep_get_products_name($prod_id)), $products_base_currency, $products_price, $email_content_other_price, $from_val, $to_val, date("Y-m-d H:i:s"), tep_get_ip_address(), $admin_email , tep_get_admin_group_name($admin_email));
					}
					
					$custom_product_type_id = tep_get_custom_product_type($prod_id);
					$cp_notification_email_array = tep_get_custom_products_notification_email($custom_product_type_id);
					
					if (isset($cp_notification_email_array['custom_product_price_email']) && tep_not_null($cp_notification_email_array['custom_product_price_email'])) {
						$cat_cpp_cfg_array = tep_get_cfg_setting($prod_id, 'product', $cp_notification_email_array['custom_product_price_email']);
						$email_cpp_to_array = tep_parse_email_string($cat_cpp_cfg_array[$cp_notification_email_array['custom_product_price_email']]);
						
						for ($email_to_cnt=0; $email_to_cnt < count($email_cpp_to_array); $email_to_cnt++) {
			    			tep_mail($email_cpp_to_array[$email_to_cnt]['name'], $email_cpp_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_title)), $product_price_changes_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			    		}
			    	}
				}
		    } else if ($field_name == 'products_status' || $field_name == 'products_display') {
		    	$custom_product_type_id = tep_get_custom_product_type($prod_id);
				$cp_notification_email_array = tep_get_custom_products_notification_email($custom_product_type_id);
				
				if (isset($cp_notification_email_array['custom_products_change_status_email']) && tep_not_null($cp_notification_email_array['custom_products_change_status_email'])) {
					$cat_cpp_cfg_array = tep_get_cfg_setting($prod_id, 'product', $cp_notification_email_array['custom_products_change_status_email']);
					$email_cpp_to_array = tep_parse_email_string($cat_cpp_cfg_array[$cp_notification_email_array['custom_products_change_status_email']]);
					
					$products_info_select = "	SELECT p.products_quantity, p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, pc.categories_id 
			                          			FROM " . TABLE_PRODUCTS . " AS p 
			                          			INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
      												ON (p.products_id=pc.products_id AND pc.products_is_link=0)
									  			WHERE p.products_id = " . tep_db_input($prod_id) . "";
					$products_info_result = tep_db_query($products_info_select);
					
					if ($products_info_row = tep_db_fetch_array($products_info_result)) {
						$prod_type = '';
						
						if ($products_info_row['products_bundle'] == 'yes') {
							$products_info_select = "	SELECT products_id
					                          			FROM " . TABLE_PRODUCTS_FOLLOW_PRICE . "
											  			WHERE products_id = " . tep_db_input($prod_id) . "";
							$products_info_result = tep_db_query($products_info_select);
							
							if (tep_db_num_rows($products_info_result)) {
								$prod_type = 'Static with Follow Product';
							} else {
								$prod_type = 'Static';
							}
						} else if ($products_info_row['products_bundle_dynamic'] == 'yes') {
							$prod_type = 'Dynamic Bundle';
						} else {
							$get_product_type_arr = tep_get_product_type();
					
							if (count($get_product_type_arr) > 0) {
								foreach ($get_product_type_arr as $product_type_arr) {
									if ($product_type_arr['id'] == $products_info_row['custom_products_type_id']) {
										if ($products_info_row['custom_products_type_id'] == 0) {
											$prod_type = 'Currency Product';
										} else {
											$prod_type = $product_type_arr['text'];
										}
										break;
									}
								}
							}
						}
						
						$product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($products_info_row['categories_id']) . '&pID=' . $prod_id . '&selected_box=catalog') . '"  target="_blank">'.$prod_id.'</a>';
						
						switch ($field_name) {
							case 'products_status':
								if ($to_val == 1) {
									$update_desc = 'Inactive -> Active';
								} else {
									$update_desc = 'Active -> Inactive';
								}
								$email_title = 'Product Status Changed';
								$product_status_changes_email_contents = sprintf(EMAIL_PRODUCT_STATUS_NOTIFICATION_CONTENT, $product_id_link, $prod_type, strip_tags(tep_get_products_name($prod_id)), $update_desc, date("Y-m-d H:i:s"), tep_get_ip_address(), $admin_email , tep_get_admin_group_name($admin_email));
								
								break;
							case 'products_display':
								if ($to_val == 1) {
									$update_desc = 'Hide -> Show';
								} else {
									$update_desc = 'Show -> Hide';
								}
								$email_title = 'Product Display Status Changed';
								$product_status_changes_email_contents = sprintf(EMAIL_PRODUCT_DISPLAY_STATUS_NOTIFICATION_CONTENT, $product_id_link, $prod_type, strip_tags(tep_get_products_name($prod_id)), $update_desc, date("Y-m-d H:i:s"), tep_get_ip_address(), $admin_email , tep_get_admin_group_name($admin_email));
								
								break;
						}
						
						for ($email_to_cnt=0; $email_to_cnt < count($email_cpp_to_array); $email_to_cnt++) {
			    			tep_mail($email_cpp_to_array[$email_to_cnt]['name'], $email_cpp_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_title)), $product_status_changes_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			    		}
					}
		    	}
		    }
		}
	}
	
	function insert_orders_log($order_id, $orders_log_system_msg='', $orders_log_filename='')
	{
		$sql = sprintf(	'	INSERT INTO ' . TABLE_ORDERS_LOG_TABLE . ' (orders_log_admin_id, orders_log_ip, orders_log_time, orders_log_orders_id, orders_log_system_messages, orders_log_filename) ' . '
							VALUES ("%s", "%s", NOW(), "%s", "%s", "%s")',
                     		$this->identity, tep_db_input(getenv("REMOTE_ADDR")), $order_id, tep_db_input($orders_log_system_msg), tep_db_input($orders_log_filename));
		tep_db_query($sql);
	}
        
        function insert_customer_history_log($customers_id, $customer_remarks='')
	{
                $remarks_added_by = ($this->identity == 0) ? 'system' : $this->identity;		
                $sql = 'INSERT INTO ' . TABLE_CUSTOMERS_REMARKS_HISTORY . ' (customers_id, date_remarks_added, remarks, remarks_added_by) ' . '
                        VALUES ("'.$customers_id.'",NOW(), "'.tep_db_input($customer_remarks).'", "' . $remarks_added_by . '")';
		tep_db_query($sql);
	}
	
	function insert_cdkey_history_log($user_role, $cp_id, $sys_msg='', $user_msg='')
	{
		$sql = sprintf(	'	INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE_LOG . ' (custom_products_code_log_user, custom_products_code_log_user_role, log_ip, log_time, custom_products_code_id, log_system_messages, log_user_messages) ' . '
							VALUES ("%d", "%s", "%s", NOW(), "%d", "%s", "%s")',
                     		$this->identity, tep_db_input($user_role), tep_db_input(getenv("REMOTE_ADDR")), tep_db_input($cp_id), tep_db_input($sys_msg), tep_db_input($user_msg));
		tep_db_query($sql);
	}	


	// function for system log
	function insert_system_log($system_log_action, $table_name, $entry_id, $field_name, $from_value, $to_value)
	{
	    $sql_data_array = array('system_log_ip' => tep_db_prepare_input(getenv("REMOTE_ADDR")),
	        	              	'system_log_time' => 'now()',
	           	              	'system_log_action' => tep_db_prepare_input($system_log_action),
	       	                  	'system_log_admin_id' => $this->identity,
	          	              	'table_name' => tep_db_prepare_input($table_name),
	           	              	'entry_id' => tep_db_prepare_input($entry_id));
	    if(tep_not_null($field_name)) $sql_data_array['field_name'] = tep_db_prepare_input($field_name);
	    if(tep_not_null($from_value)) $sql_data_array['from_value'] = tep_db_prepare_input($from_value);
	    if(tep_not_null($to_value))  $sql_data_array['to_value'] = tep_db_prepare_input($to_value);
		tep_db_perform(TABLE_SYSTEM_LOG, $sql_data_array);
	}
	// end function

	// function for system log all changes loop
	function insert_all_system_log($system_log_action, $table_name, $entry_id, $changes_array)
	{
		if (count($changes_array)) {
			for ($i=0; $i < count($changes_array); $i++) {
				if (count($changes_array[$i])) {
					foreach($changes_array[$i] as $field => $res) {
						$this->insert_system_log($system_log_action, $table_name, $entry_id, $res['text'], $res['from'], $res['to']);
					}
				}
			}
		}
	}
	// end function

	function set_log_table($table_name) {
		/*
		if (tep_not_null($table_name)) {
			$this->table = $table_name;
		}*/
		return;
	}
	
	function detect_changes ($old_data, $new_data)
	{
		$changes_array = array();
		if (count($old_data) && count($new_data)) {
			foreach ($old_data as $key => $value) {
				if (strcmp($new_data[$key], $value) !== 0) {
					$changes_array[$key] = array('from'=> $value, 'to'=> $new_data[$key]);
				}
			}
		}	
		return $changes_array;
	}
	
	function construct_log_message($changes_array)
	{
		$message_str = array();
		if (count($changes_array)) {
			foreach ($changes_array as $key => $changes) {
				$readable_array = $this->get_readable_log_input($key, $changes['from'], $changes['to']);
				if (count($readable_array)) {
					$message_str[] = $readable_array;
				}
			}
		}
		
		return $message_str;
	}
	
	function construct_customers_info_flag_log_message($old_str, $new_str)
	{	
		$changes_array = array();
		
		$old_info_flag_array = explode("##", $old_str);
		$new_info_flag_array = explode("##", $new_str);
		
		$result_flag = array_diff($old_info_flag_array, $new_info_flag_array);
		$result_unflag = array_diff($new_info_flag_array, $old_info_flag_array);
		
		$result_flag = array_filter($result_flag, 'filter_empty_val');
		$result_unflag = array_filter($result_unflag, 'filter_empty_val');
		
		if (count($result_flag)) {
			foreach ($result_flag as $res) {
				$changes_array[$res . '_flag'] = array('from'=> 'flagged', 'to'=> 'unflagged');
			}
		}
		
		if (count($result_unflag)) {
			foreach ($result_unflag as $res) {
				$changes_array[$res . '_flag'] = array('from'=> 'unflagged', 'to'=> 'flagged');
			}
		}
		
		return $changes_array;
	}
	
	function get_readable_log_input($field_name, $old_val, $new_val) {
		$plain_result = false;
		
		$result = array();
		$old_val = trim($old_val);
		$new_val = trim($new_val);
		switch($field_name) {
			case 'customers_gender_flag':
				$text = 'Customers Gender Flag';
				$old_string = $old_val;
				$new_string = $new_val;
			
				break;
			case 'customers_firstname_flag':
				$text = 'Customers First Name Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_lastname_flag':
				$text = 'Customers Last Name Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_dob_flag':
				$text = 'Customers Date of Birth Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_email_address_flag':
				$text = 'Customers E-Mail Address Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_email_address_flag':
				$text = 'Customers E-Mail Address Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_street_address_flag':
				$text = 'Customers Street Address Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_suburb_flag':
				$text = 'Customers Suburb Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_postcode_flag':
				$text = 'Customers Post Code Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_city_flag':
				$text = 'Customers City Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_state_flag':
				$text = 'Customers State Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'entry_country_id_flag':
				$text = 'Customers Country Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_country_dialing_code_id_flag':
				$text = 'Location Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				break;
			case 'customers_telephone_flag':
				$text = 'Customers Telephone Number Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_fax_flag':
				$text = 'Customers Fax Number Flag';
				$old_string = $old_val;
				$new_string = $new_val;
				
				break;
			case 'customers_flag':
				$old_string = $new_string = '';
				
				if (function_exists('tep_get_user_flags')) {
					$user_flags_array = tep_get_user_flags();
				} else {
					include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
					$user_flags_array = tep_get_user_flags();
				}
				
				$changes_array = array();
				
				$old_array = tep_not_null($old_val) ? explode(',', $old_val) : array();
				$new_array = tep_not_null($new_val) ? explode(',', $new_val) : array();
				
				$flagged_array = array_diff($new_array, $old_array);
				$unflagged_array = array_diff($old_array, $new_array);
				
				if (count($flagged_array)) {	// From Off->On
					foreach ($flagged_array as $flag_id) {
						$flag_label = $user_flags_array[$flag_id]['user_flags_name'] . (strpos($user_flags_array[$flag_id]['user_flags_name'], str_replace(':', '', ENTRY_CUSTOMERS_FLAG)) !== FALSE ? ':' : ' ' . ENTRY_CUSTOMERS_FLAG);
						$changes_array[$flag_id] = '<span class="'.$user_flags_array[$flag_id]['user_flags_css_style'].'">'.$flag_label.'</span> Off --> On' . (tep_not_null($user_flags_array[$flag_id]['user_flags_description']) ? ' (<span class="redIndicator">'.$user_flags_array[$flag_id]['user_flags_description'].'</span>)' : '');
					}
				}
				
				if (count($unflagged_array)) {	// From On->Off
					foreach ($unflagged_array as $flag_id) {
						$flag_label = $user_flags_array[$flag_id]['user_flags_name'] . (strpos($user_flags_array[$flag_id]['user_flags_name'], str_replace(':', '', ENTRY_CUSTOMERS_FLAG)) !== FALSE ? ':' : ' ' . ENTRY_CUSTOMERS_FLAG);
						$changes_array[$flag_id] = '<span class="'.$user_flags_array[$flag_id]['user_flags_css_style'].'">'.$flag_label.'</span> On --> Off';
					}
				}
				
				ksort($changes_array);
				reset($changes_array);
				
				$text = implode("\n", $changes_array);
				$plain_result = true;
				
				break;
			case 'customers_newsletter':
				$old_string = '';
				
				$old_array = tep_not_null($old_val) ? explode(',', $old_val) : array();
				$new_array = tep_not_null($new_val) ? explode(',', $new_val) : array();
				
				$new_subscribe_array = array_diff($new_array, $old_array);
				$unsubscribe_array = array_diff($old_array, $new_array);
				
				if (count($new_subscribe_array)) {
					$new_subscribe_str = '';
					
					$newsletter_group_select_sql = "SELECT newsletters_groups_name 
													FROM " . TABLE_NEWSLETTERS_GROUPS . " 
													WHERE module = 'newsletter' 
														AND newsletters_groups_id IN ('" . implode("', '", $new_subscribe_array) . "') 
													ORDER BY newsletters_groups_sort_order";
					$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
					
					while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
						$new_subscribe_str .= $newsletter_group_row['newsletters_groups_name'] . "\n";
					}
					
					if (tep_not_null($new_subscribe_str))	$old_string .= "\n" . '<b><i>' . ENTRY_NEWSLETTER_YES . "</i></b>\n" . $new_subscribe_str;
				}
				
				if (substr($old_string, -1) == "\n")	$old_string = substr($old_string, 0, -1);
				
				if (count($unsubscribe_array)) {
					$unsubscribe_str = '';
					
					$newsletter_group_select_sql = "SELECT newsletters_groups_name 
													FROM " . TABLE_NEWSLETTERS_GROUPS . " 
													WHERE module = 'newsletter' 
														AND newsletters_groups_id IN ('" . implode("', '", $unsubscribe_array) . "') 
													ORDER BY newsletters_groups_sort_order";
					$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
					
					while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
						$unsubscribe_str .= $newsletter_group_row['newsletters_groups_name'] . "\n";
					}
					
					if (tep_not_null($unsubscribe_str))	$old_string .= "\n" . '<b><i>' . ENTRY_NEWSLETTER_NO . "</i></b>\n" . $unsubscribe_str;
				}
				
				if (substr($old_string, -1) == "\n")	$old_string = substr($old_string, 0, -1);
				
				$text = 'Newsletter';
				
				break;
			case 'customers_groups_id':
				if ((int)$old_val > 0) {
					$cust_grp_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id = '" . (int)$old_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$old_string = $cust_grp_row["customers_groups_name"];
					} else {
						$old_string = "Customer group not found!";
					}
				}
				
				if ((int)$new_val > 0) {
					$cust_grp_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id = '" . (int)$new_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$new_string = $cust_grp_row["customers_groups_name"];
					} else {
						$new_string = "Customer group not found!";
					}
				}
				$text = 'Customer Group Status';
				
				break;
            case 'customers_aft_groups_id':
				if ((int)$old_val > 0) {
					$cust_grp_select_sql = "SELECT customers_aft_groups_name FROM " . TABLE_CUSTOMERS_AFT_GROUPS . " WHERE customers_aft_groups_id = '" . (int)$old_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$old_string = $cust_grp_row["customers_aft_groups_name"];
					} else {
						$old_string = "AFT group not found!";
					}
				}
				
				if ((int)$new_val > 0) {
					$cust_grp_select_sql = "SELECT customers_aft_groups_name FROM " . TABLE_CUSTOMERS_AFT_GROUPS . " WHERE customers_aft_groups_id = '" . (int)$new_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$new_string = $cust_grp_row["customers_aft_groups_name"];
					} else {
						$new_string = "AFT group not found!";
					}
				}
				$text = 'AFT Group Level';
				
				break;
			case 'customers_gender':
				$old_string = ($old_val == 'm') ? 'Male' : 'Female';
				$new_string = ($new_val == 'm') ? 'Male' : 'Female';
				$text = 'Gender';
				
				break;
			case 'customers_dob':
				$old_string = tep_date_short($old_val);
				$new_string = tep_date_short($new_val);
				$text = 'Date of Birth';
				
				break;
			case 'customers_phone_verified':
				$old_string = ((int)$old_val == '1') ? 'Verify' : 'Unverify';
				$new_string = ((int)$new_val == '1') ? 'Verify' : 'Unverify';
				$text = 'Phone Verification';
				
				break;
			case 'entry_country_id':
				$old_string = tep_get_country_name($old_val);
				$new_string = tep_get_country_name($new_val);
				$text = 'Country';
				
				break;
			case 'entry_zone_id':
				return;
				break;
			case 'customers_status':
				$old_string = ($old_val == 0) ? 'Not Active' : 'Active';
				$new_string = ($new_val == 0) ? 'Not Active' : 'Active';
				$text = 'Customers Status';
				
				break;
			case 'account_activated':
				$old_string = ($old_val == 0) ? 'Not Activated' : 'Activated';
				$new_string = ($new_val == 0) ? 'Not Activated' : 'Activated';
				$text = 'Account Activation';
				
				break;
			case 'customers_country_dialing_code_id':
				$old_string = tep_get_country_name($old_val);
				$new_string = tep_get_country_name($new_val);
				$text = 'Location';
				
				break;
			case 'custom_products_code_viewed':
				$old_string = ((int)$old_val == '1') ? 'Viewed' : 'Not Viewed';
				$new_string = ((int)$new_val == '1') ? 'Viewed' : 'Not Viewed';
				$text = 'CD Key View Status';
				
				break;
			case 'customers_disable_withdrawal':
			case 'supplier_disable_withdrawal':
				$old_string = ((int)$old_val == '1') ? 'Disabled' : 'Enabled';
				$new_string = ((int)$new_val == '1') ? 'Disabled' : 'Enabled';
				$text = 'Disable Withdrawal';

				break;
			case 'custom_products_code_status_id':
				$code_status_array = array(
                    '-1' => 'Disabled',
                    '-2' => 'On Hold',
                    '-4' => 'G2G',
                    '-5' => 'Reserved',
                    '0'  => 'Sold',
                    '1'  => 'Actual'
                );
				$old_string = $code_status_array[(int)$old_val];
				$new_string = $code_status_array[(int)$new_val];
				$text = 'CD Key Status';
				
				break;
			case 'customers_reserve_amount':
				$old_string = $old_val;
				$new_string = $new_val;
				$text = 'Reserve Amount';
				break;
			case 'vip_supplier_groups_id':
				if ((int)$old_val > 0  && $old_val != "") {
					$vip_status_select_sql = "SELECT vip_supplier_groups_name FROM " . TABLE_VIP_SUPPLIER_GROUPS . " WHERE vip_supplier_groups_id = '" . (int)$old_val . "'";
					$vip_status_result_sql = tep_db_query($vip_status_select_sql);
					if ($vip_status_row = tep_db_fetch_array($vip_status_result_sql)) {
						$old_string = $vip_status_row["vip_supplier_groups_name"];
					} else {
						$old_string = "VIP Status not found!";
					}
				} else {
					$old_string = "EMPTY";
				}
				
				if ((int)$new_val > 0  && $new_val != "") {
					$vip_status_select_sql = "SELECT vip_supplier_groups_name FROM " . TABLE_VIP_SUPPLIER_GROUPS . " WHERE vip_supplier_groups_id = '" . (int)$new_val . "'";
					$vip_status_result_sql = tep_db_query($vip_status_select_sql);
					if ($vip_status_row = tep_db_fetch_array($vip_status_result_sql)) {
						$new_string = $vip_status_row["vip_supplier_groups_name"];
					} else {
						$new_string = "VIP Status not found!";
					}
				} else {
					$new_string = "EMPTY";
				}
				$text = 'VIP Status';
				
				break;
			case 'vip_rank_id':
				if ((int)$old_val > 0 && $old_val != "") {
					$vip_rank_select_sql = "SELECT vip_rank_name FROM " . TABLE_VIP_RANK . " WHERE vip_rank_id = '" . (int)$old_val . "'";
					$vip_rank_result_sql = tep_db_query($vip_rank_select_sql);
					if ($vip_rank_row = tep_db_fetch_array($vip_rank_result_sql)) {
						$old_string = $vip_rank_row["vip_rank_name"];
					} else {
						$old_string = "VIP Rank not found!";
					}
				} else {
					$old_string = "EMPTY";
				}
				
				if ((int)$new_val > 0 && $new_val != "") {
					$vip_rank_select_sql = "SELECT vip_rank_name FROM " . TABLE_VIP_RANK . " WHERE vip_rank_id = '" . (int)$new_val . "'";
					$vip_rank_result_sql = tep_db_query($vip_rank_select_sql);
					if ($vip_rank_row = tep_db_fetch_array($vip_rank_result_sql)) {
						$new_string = $vip_rank_row["vip_rank_name"];
					} else {
						$new_string = "VIP Rank not found!";
					}
				} else {
					$new_string = "EMPTY";
				}
				$text = 'VIP Rank';
				
				break;
			case 'admin_groups_id':
				$old_group_select_sql = "	SELECT admin_groups_name 
											FROM " . TABLE_ADMIN_GROUPS . " 
											WHERE admin_groups_id='" . $old_val . "'";
				$old_group_result_sql = tep_db_query($old_group_select_sql);
				$old_group_result_row = tep_db_fetch_array($old_group_result_sql);

				$new_group_select_sql = "	SELECT admin_groups_name 
											FROM " . TABLE_ADMIN_GROUPS . " 
											WHERE admin_groups_id='" . $new_val . "'";
				$new_group_result_sql = tep_db_query($new_group_select_sql);
				$new_group_result_row = tep_db_fetch_array($new_group_result_sql);

					
				$old_string = ($old_val == 0) ? 'Unassigned' : $old_group_result_row['admin_groups_name'];
				$new_string = ($new_val == 0) ? 'Unassigned' : $new_group_result_row['admin_groups_name'];
				$text = $field_name;
				
				break;
			case 'admin_groups_authorized':
				$old_val!='' ? $old_array = explode(",", $old_val) : $old_array = array();
				$new_val!='' ? $new_array = explode(",", $new_val) : $new_array = array();
				$removed_array = array_diff($old_array, $new_array);
				$added_array = array_diff($new_array, $old_array);
				$diff = "";
				
				$admin_groups_select_sql = "SELECT admin_groups_id, admin_groups_name 
    			                    		FROM " . TABLE_ADMIN_GROUPS;
				$admin_groups_result_sql = tep_db_query($admin_groups_select_sql);
				$admin_groups_array = array();
				while ($admin_groups_row = tep_db_fetch_array($admin_groups_result_sql)) {
					$admin_groups_array[$admin_groups_row['admin_groups_id']] = $admin_groups_row['admin_groups_name'];
				}

				if (count($added_array)>0){
					$diff .= "<b><i>Added:</i></b><br>";
					foreach ($added_array as $id){
						 $diff .= $admin_groups_array[$id] . "<br>";
					}
				}
				if (count($removed_array)>0){
					$diff .= "<b><i>Removed:</i></b><br>";
					foreach ($removed_array as $id){
							 $diff .= $admin_groups_array[$id] . "<br>";
					}
				}

				$old_string = 'n/a';
				$new_string = $diff;
				$text = $field_name;
				
				break;
			default:
				$display_label = array(	'customers_firstname' => 'First Name', 'customers_lastname' => 'Last Name', 'customers_email_address' => 'E-Mail Address', 
										'customers_telephone' => 'Telephone Number', 'customers_fax' => 'Fax Number', 'customers_mobile' => 'Mobile Number', 'customers_discount' => 'Customer Discount Rate',
										'customers_phone_verified' => 'Phone Verification', 'customers_phone_verified_datetime' => 'Phone Verification Date',
										'entry_street_address' => 'Street Address', 'entry_postcode' => 'Post Code', 'entry_city' => 'City', 'entry_company' => 'Company',
										'entry_suburb' => 'Suburb', 'entry_state' => 'State', 'customers_msn' => 'MSN', 'customers_qq' => 'QQ', 'customers_icq' => 'ICQ', 'customers_yahoo' => 'YAHOO', 'vip_buyback_cummulative_point' => 'VIP Cummulative Point',
										'custom_products_code_products_id' => 'CD Key Product', 'custom_products_code_file_name' => 'File Name', 'custom_products_code_remarks' => 'Remarks', 'custom_products_code_uploaded_by' => 'Uploaded By',
                                                                                'verified_seller' => 'Verified Seller', 'seller_product_listing_limit' => 'seller_product_listing_limit'
										);
				
				$old_string = (trim($old_val) != '') ? $old_val : "EMPTY";
				$new_string = (trim($new_val) != '') ? $new_val : "EMPTY";
				$text = tep_not_null($display_label[$field_name]) ? $display_label[$field_name] : $field_name;
				
				break;
		}
		
		$result[$field_name] = array('text' => $text, 'from' => $old_string, 'to' => $new_string, 'plain_result' => ($plain_result ? '1' : '0') );
		return $result;
	}
	
	function contruct_changes_string($customer_changes_array, $all_customers_info_changes_made, $customer_verified_array = '') {
		if (gettype($customer_changes_array) == "array" && sizeof($customer_changes_array) > 0) {
			foreach ($customer_changes_array as $field => $res) {
				if (tep_not_null($all_customers_info_changes_made)) {
	        		if (!preg_match('/(##)?('.$field.')(##)?/', $all_customers_info_changes_made)) {
	        			$all_customers_info_changes_made .= "##" . $field;
	        		}
	        	} else {
	        		$all_customers_info_changes_made .= $field;	
	        	}
			}
		}
		
		if (gettype($customer_verified_array) == "array" && tep_not_null($customer_verified_array)) {
			foreach ($customer_verified_array as $field => $res) {
				$all_customers_info_changes_made = preg_replace('/(##)?('.$field.')(##)?/', '$1'.'$3', $all_customers_info_changes_made);
			}
		}
		
		while (strstr($all_customers_info_changes_made, '####')) $all_customers_info_changes_made = str_replace('####', '##', $all_customers_info_changes_made);
		
		if (preg_match('/^##/', $all_customers_info_changes_made)) {
			$all_customers_info_changes_made = substr($all_customers_info_changes_made, 2);
	    }
	    
	    if (preg_match('/##$/', $all_customers_info_changes_made)) {
	    	$all_customers_info_changes_made = substr($all_customers_info_changes_made, 0, -2);
	    }
	    
		return $all_customers_info_changes_made;	
	}
	
	function draw_inputs($form_name="log_form", $input_array=array())
	{
		echo '	<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="2" cellpadding="0">';
		foreach ($input_array as $key => $resources) {
			$input_str = "";
			switch ($resources["type"]) {
				case "date":
					if ($resources["calendar"] == 'PopCal') {
						$input_str = '	<tr>
	            							<td class="main" width="12%">' . $resources["title"] . '<br><small>(' . strtoupper($resources["format"]) . ')</small></td>
	            							<td class="main" align="laft">'.tep_draw_input_field($key, "$resources[default_value]", 'id="'.$key.'" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.log_input.'.$key.'); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.log_input.'.$key.');return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' . 
	            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].'</td>
	          							</tr>';
					} else {
						$input_str = '	<script language="javascript"><!--
	  										var ' . "date_$key" . ' = new ctlSpiffyCalendarBox("' . "date_$key" . '", "' . $form_name . '", "' . $key . '", "' . "btnDate_$key". '", "", scBTNMODE_CUSTOMBLUE);
											//--></script>';
						$input_str .= '	<tr>
	            							<td class="main" width="12%">' . $resources["title"] . '<br><small>(' . strtoupper($resources["format"]) . ')</small></td>
	            							<td class="main" align="laft"><script language="javascript">' . "date_$key" . '.writeControl(); ' . "date_$key" . '.dateFormat="' . $resources["format"] . '"; document.getElementById(\''.$key.'\').value=\''.$resources["default_value"].'\';</script>' .
	            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].'</td>
	          							</tr>';
	          		}
	          		$input_str .= '	<tr>
	            						<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
	          						</tr>';
					break;
				case "date_range":
					$input_str = '	<tr>
										<td class="main" width="12%">' . $resources["title"]["date_range"] . '<br><small>(' . strtoupper($resources["format"]) . ')</small></td>
            							<td class="main">&nbsp;'.
            							$resources["title"]["start_date"] . 
										'&nbsp;&nbsp;&nbsp;' . 
            							tep_draw_input_field("start_date", $resources[default_value]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.log_input.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.log_input.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' . 
            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].
										'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . 
            							$resources["title"]["end_date"] . 
										'&nbsp;&nbsp;&nbsp;' . 
            							tep_draw_input_field("end_date", $resources[default_value]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.log_input.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.log_input.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' . 
            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].'
            							</td>
          							</tr>';
	          		$input_str .= '	<tr>
	            						<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
	          						</tr>';
					break;
				case "text":
					$input_str = '	<tr>
										<td class="main">' . $resources["title"] . '</td>
										<td class="main">' . tep_draw_input_field("$key", "$resources[default_value]", "$resources[params]") . ($resources["required"] ? '<sup><span style="color:red;">*</span></sup>' : '');
					
				 	if (isset($resources["lookup"])) {
				 		if (isset($resources["lookup"]["link"])){
					 		$input_str .= '&nbsp;<a href="javascript:openDGDialog(\''. tep_href_link($resources["lookup"]["file"], $resources["lookup"]["params"]) . '\', 600, 250, \'\');">' . $resources["lookup"]["link"] . '</a>';
					 	}
				 	}
				 	
				 	if (isset($resources["extra_info"])) {
				 		$input_str .= $resources["extra_info"];
					}
					
					$input_str .= '		</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "select":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>
							            <td class="main">' . tep_draw_pull_down_menu("$key", $resources["source"], $resources["default_value"], '') . 
							            ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "multi_select":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>
							            <td class="main">' . tep_draw_pull_down_menu("$key", $resources["source"], $resources["default_value"], 'multiple="multiple" size="' . $resources["size"].'" ' . $resources["params"]) . 
							            ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .='	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "checkbox":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_checkbox) {
							$input_str .= tep_draw_checkbox_field($ind_checkbox["id"], (isset($ind_checkbox["value"]) ? $ind_checkbox["value"] : $ind_checkbox["text"]), ( $ind_checkbox["checked"] ? true : false), "", $ind_checkbox["params"]) . '&nbsp;' . $ind_checkbox["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					} else if ($resources["format"] == "vertical") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_checkbox) {
							$input_str .= (tep_not_null($resources["spacer"]) ? tep_draw_separator('pixel_trans.gif', $resources["spacer"], '1') : '') . tep_draw_checkbox_field($resources["use_key"] ? $key : $ind_checkbox["id"], (isset($ind_checkbox["value"]) ? $ind_checkbox["value"] : $ind_checkbox["text"]), ( $ind_checkbox["checked"] ? true : false), "", $ind_checkbox["params"]) . '&nbsp;' . $ind_checkbox["text"] . '<br>';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "radio":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_radio) {
							$input_str .= tep_draw_radio_field($ind_radio["name"], (isset($ind_radio["value"]) ? $ind_radio["value"] : $ind_radio["text"]), ( $ind_radio["checked"] ? true : false), "", $ind_radio["params"]) . '&nbsp;' . $ind_radio["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
			}
			echo $input_str;
		}
		echo '			</table>
					</td>
				</tr>';
	}
}
?>