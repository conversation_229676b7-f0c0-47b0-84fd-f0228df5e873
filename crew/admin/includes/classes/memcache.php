<?
class OGM_Cache_MemCache extends OGM_Cache_Abstract {
    // Memcache object
    public $connection;
	
    function __construct() {
        $this->connection = new MemCached;
        //$this->addServer('localhost');
        $this->addServer(CFG_MEMCACHE_SERVER);
    }
	
    function store($key, $data, $ttl) {
        return $this->connection->set($key,$data,$ttl);
    }
	
	function replace($key, $data, $ttl) {
        $result = $this->connection->replace($key, $data, $ttl);
		
		if ($result == false) {
		    $result = $this->store($key, $data, $ttl);
		}
		
		return $result;
    }
    
    function fetch($key) {
        return $this->connection->get($key);
    }
	
    function delete($key, $ttl=0) {
        return $this->connection->delete($key, $ttl);
    }
    
    function confirmDelete($key, $ttl=0) {
        $return_bool = null;
        $cache_result = $this->fetch($key);

        if ($cache_result !== FALSE) {
            $return_bool = $this->delete($key, 0);
        } else {
            $this->delete($key, 0);
        }
        
        return $return_bool;
    }
    
    function addServer($host, $port=11211, $weight=10) {
        $this->connection->addServer($host, $port, $weight);
    }
    
    function getStats() {
        return $this->printDetails($this->connection->getStats());
    }
    
    function doFlush() {
        $this->connection->flush();
    }
    
    function printDetails($status) {
    	$entry_css_class = 'e';
    	$text_css_class = 'v';
		echo "<table border='0' width='600' cellpadding='3'>";
        echo "<tr><td class='".$entry_css_class."'>Memcache Server version:</td><td class='".$text_css_class."'> ".$status ["version"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Process id of this server process </td><td class='".$text_css_class."'>".$status ["pid"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Number of seconds this server has been running </td><td class='".$text_css_class."'>".$status ["uptime"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Accumulated user time for this process </td><td class='".$text_css_class."'>".$status ["rusage_user"]." seconds</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Accumulated system time for this process </td><td class='".$text_css_class."'>".$status ["rusage_system"]." seconds</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Total number of items stored by this server ever since it started </td><td class='".$text_css_class."'>".$status ["total_items"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Number of open connections </td><td class='".$text_css_class."'>".$status ["curr_connections"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Total number of connections opened since the server started running </td><td class='".$text_css_class."'>".$status ["total_connections"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Number of connection structures allocated by the server </td><td class='".$text_css_class."'>".$status ["connection_structures"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Cumulative number of retrieval requests </td><td class='".$text_css_class."'>".$status ["cmd_get"]."</td></tr>";
        echo "<tr><td class='".$entry_css_class."'> Cumulative number of storage requests </td><td class='".$text_css_class."'>".$status ["cmd_set"]."</td></tr>";

        $percCacheHit=(real)$status ["cmd_get"] > 0 ? ((real)$status ["get_hits"]/ (real)$status ["cmd_get"] *100) : 0;
        $percCacheHit=round($percCacheHit,3);
        $percCacheMiss=100-$percCacheHit;

        echo "<tr><td class='".$entry_css_class."'>Number of keys that have been requested and found present </td><td class='".$text_css_class."'>".$status ["get_hits"]." ($percCacheHit%)</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Number of items that have been requested and not found </td><td class='".$text_css_class."'>".$status ["get_misses"]."($percCacheMiss%)</td></tr>";

        $MBRead= (real)$status["bytes_read"]/(1024*1024);

        echo "<tr><td class='".$entry_css_class."'>Total number of bytes read by this server from network </td><td class='".$text_css_class."'>".$MBRead." Mega Bytes</td></tr>";
        $MBWrite=(real) $status["bytes_written"]/(1024*1024) ;
        echo "<tr><td class='".$entry_css_class."'>Total number of bytes sent by this server to network </td><td class='".$text_css_class."'>".$MBWrite." Mega Bytes</td></tr>";
        $MBSize=(real) $status["limit_maxbytes"]/(1024*1024) ;
        echo "<tr><td class='".$entry_css_class."'>Number of bytes this server is allowed to use for storage.</td><td class='".$text_css_class."'>".$MBSize." Mega Bytes</td></tr>";
        echo "<tr><td class='".$entry_css_class."'>Number of valid items removed from cache to free memory for new items.</td><td class='".$text_css_class."'>".$status ["evictions"]."</td></tr>";
		echo "</table>";
    }
}
?>