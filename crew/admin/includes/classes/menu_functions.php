<?
/*
		$Id: menu_functions.php,v 1.2 2008/01/17 08:25:52 edwin.wang Exp $
	
	osCommerce, Open Source E-Commerce Solutions
	http://www.oscommerce.com
	
	Copyright (c) 2003 osCommerce
	
	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: cms_menu
class Menu {
	// class constructor
	function Menu() 
	{
			global $language_obj;
			$language_obj = array();
			
		$sql = "SELECT * 
				FROM ".TABLE_LANGUAGES." 
				ORDER BY sort_order ASC";
			$result_sql = tep_db_query($sql);
		while ($language_rows = tep_db_fetch_array($result_sql)) {
			$defaultflaq = (DEFAULT_LANGUAGE == $language_rows['code']) ? "default" : "";
				
			$row_obj = array();
					$row_obj = array( 'code' => $language_rows['code'],
			'languages_id' => $language_rows['languages_id'],
			'name' => $language_rows['name'],
			'default' => $defaultflaq);

			$language_obj[] = $row_obj; 
		}
	}

	// class methods
	function addEntry($id="")
	{
		Global $language_obj;

		$Menu_obj = array(
		'cms_menu_type' => ($_POST['menu_type']) ? $_POST['menu_type'] : '',
		'cms_menu_content_type' => ($_POST['menu_content_type']) ? tep_db_prepare_input($_POST['menu_content_type']) : '',
		'cms_menu_sort_order' => ($_POST['menu_sorting']) ? tep_db_prepare_input($_POST['menu_sorting']) : '',
		'cms_menu_right_navigation' => ($_POST['right_navigation']) ? tep_db_prepare_input($_POST['right_navigation']) : '',
		'cms_menu_status' => ($_POST['menu_status']) ? tep_db_prepare_input($_POST['menu_status']) : '');

		if ($_POST['base_filename']) {
			$base_filename_array = array();
			$base_filename_array = explode("\r\n",$_POST['base_filename']);
			
			// Filter uniqueness
			foreach ($base_filename_array as $base_filename_entry) {
				if (empty($flag["$base_filename_entry"])) {
					$flag["$base_filename_entry"] = 1;
					$base_filename_array_new [] = $base_filename_entry;
				}
			}

			$base_filename_array = $base_filename_array_new;
		}

		if ($_POST['menu_content_type'] == "url") {
			$Menu_obj['cms_menu_url'] = ($_POST['menu_url']) ? tep_db_prepare_input($_POST['menu_url']) : '';
		}
		else {
			if ($_POST['menu_seo_alias']) {
				include_once(DIR_WS_CLASSES . 'seo.php');
				$seo_url = new seo_url();
				$menu_seo_alias = $seo_url->tep_translate_special_character($_POST['menu_seo_alias']);
				$menu_seo_alias = $seo_url->tep_validate_special_characters($menu_seo_alias);
			}
							
			$Menu_obj['cms_menu_seo_alias'] = ($menu_seo_alias) ? $menu_seo_alias : '';
		}

		if ($id) {
			tep_db_perform(TABLE_CMS_MENU, $Menu_obj, 'update', "cms_menu_id='".$id."'");

			foreach ($language_obj as $language_rows) {
				$language_id = $language_rows["languages_id"];
				$sql = "DELETE 
								FROM ".TABLE_CMS_MENU_VALUE." 
								WHERE cms_menu_id = '$id' 
								AND languages_id = '$language_id'";
				$result_sql = tep_db_query($sql);
			}

			$sql = "DELETE 
							FROM ".TABLE_CMS_MENU_TAB_PAGE." 
							WHERE cms_menu_id = '$id'";
			$result_sql = tep_db_query($sql);
			
			$menu_id = $id;
		}
		else {
			tep_db_perform(TABLE_CMS_MENU, $Menu_obj, 'insert');
			$menu_id = tep_db_insert_id();
		}

		$variable_array = array ('menu_title','menu_content','menu_submenu_script');

		foreach ($variable_array as $variable_name) {
			foreach ($language_obj as $language_rows) {
				$language_id = $language_rows["languages_id"];
				$value = tep_db_prepare_input($_POST["{$variable_name}_{$language_id}"]);

				$Menu_data_array = array(
					'cms_menu_id' => tep_db_prepare_input($menu_id),
					'languages_id' => tep_db_prepare_input($language_rows['languages_id']),
					'cms_menu_lang_setting_key' => tep_db_prepare_input($variable_name),
					'cms_menu_lang_setting_key_value' => $value);
					
				tep_db_perform(TABLE_CMS_MENU_VALUE, $Menu_data_array, 'insert');
			}
		}
		
		if ($base_filename_array) {
			foreach ($base_filename_array as $base_filename_array_entry) {
				if ($base_filename_array_entry) {
					$Menu_data_array = array(
						'cms_menu_id' => tep_db_prepare_input($menu_id),
						'cms_linked_filename' => tep_db_prepare_input($base_filename_array_entry));
	
					tep_db_perform(TABLE_CMS_MENU_TAB_PAGE, $Menu_data_array, 'insert');
				}
			}
		}
	}
	
	function changeStatus($id="")
	{
		if ($id) {
			$sql = "SELECT * 
					FROM ".TABLE_CMS_MENU." 
					WHERE cms_menu_id = '$id'";
			$result_sql = tep_db_query($sql);
	 		$rows = tep_db_fetch_array($result_sql);
	 		
	 		$menu_status = $rows['cms_menu_status'];
	 		
	 		if ($menu_status == "1")
	 			 $Menu_obj = array('cms_menu_status' => 0);
			else
	 			 $Menu_obj = array('cms_menu_status' => 1);

			tep_db_perform(TABLE_CMS_MENU, $Menu_obj, 'update', "cms_menu_id='".$id."'");

				if ($menu_status == "1")
					 return "<a href=\"javascript:change_status('$id')\"><img src=\"images/icon_status_green_light.gif\" border=0></a> <img src=\"images/icon_status_red.gif\" border=0>";
			 	else
					 return "<img src=\"images/icon_status_green.gif\" border=0> <a href=\"javascript:change_status('$id')\"><img src=\"images/icon_status_red_light.gif\" border=0></a>";
		}
		else {
			return "";
		}
	}

	function deleteEntry($id="")
	{
		GLOBAL $language_obj;

		if ($id) {
			foreach ($language_obj as $language_rows) {
				$language_id = $language_rows["languages_id"];
				$sql1 = "	DELETE 
							FROM ".TABLE_CMS_MENU_VALUE." 
							WHERE cms_menu_id = '$id' 
								AND languages_id = '$language_id'";
				$result_sql1 = tep_db_query($sql1);
			}

			$sql = "DELETE 
					FROM ".TABLE_CMS_MENU." 
					WHERE cms_menu_id = '$id'";
			$result_sql = tep_db_query($sql);

			$sql = "DELETE 
					FROM ".TABLE_CMS_MENU_TAB_PAGE." 
					WHERE cms_menu_id = '$id'";
			$result_sql = tep_db_query($sql);

			if ($result_sql == 1)
				return "success";
		}
		else {
			return "";
		}
	}	
}
?>