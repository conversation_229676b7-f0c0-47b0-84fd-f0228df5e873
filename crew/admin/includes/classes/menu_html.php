<?php
/*
	$Id: menu_html.php,v 1.8 2013/01/09 04:58:35 chingyen Exp $
	
	osCommerce, Open Source E-Commerce Solutions
	http://www.oscommerce.com
	
	Copyright (c) 2003 osCommerce
	
	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: Menu Management

/*
Menu Type
-----------
1. Navigation Tab
2. Footer SEO Link
3. Copyright Link
4. Header Nav Tab
5. Header Product Type Tab

*/

class Menu_HTML {
	// class constructor
	function Menu_HTML()
	{
		global $language_obj;
		$language_obj = array();

		$sql = "SELECT * 
				FROM ".TABLE_LANGUAGES." 
				ORDER BY sort_order ASC";
		$result_sql = tep_db_query($sql);
		while ($language_rows = tep_db_fetch_array($result_sql)) {
			$defaultflaq = (DEFAULT_LANGUAGE == $language_rows['code']) ? "default" : "";
			
			$row_obj = array();
			$row_obj = array( 'code' => $language_rows['code'],
			'languages_id' => $language_rows['languages_id'],
			'name' => $language_rows['name'],
			'default' => $defaultflaq);

			$language_obj[] = $row_obj; 
		}
	}
	
	function menuListing() {
		global $language_obj;

		if ($_GET['menu_type']) {
			$menu_type = $_GET['menu_type'];
		}
		else {
			$sql = "SELECT cms_menu_type 
					FROM ".TABLE_CMS_MENU." 
					ORDER BY cms_menu_sort_order ASC";
			$result_sql = tep_db_query($sql);
			$rows = tep_db_fetch_array($result_sql);

			$menu_type = $rows['cms_menu_type'];
		}

		$listing_html = '';

		$listing_obj = array();

		$sql = "SELECT * 
				FROM ".TABLE_CMS_MENU." 
				WHERE cms_menu_type='$menu_type' 
				ORDER BY cms_menu_sort_order ASC";
		$result_sql = tep_db_query($sql);

		while ($rows = tep_db_fetch_array($result_sql)) {

	 		foreach ($language_obj as $language_rows) {
				if ($language_rows['default'] == "default") {
					$language_id = $language_rows['languages_id'];

					$sql1 = "SELECT * 
							FROM ".TABLE_CMS_MENU_VALUE." 
							WHERE cms_menu_id = '".$rows['cms_menu_id']."' 
								AND languages_id = '$language_id' 
								AND cms_menu_lang_setting_key='menu_title'";
					$result_sql1 = tep_db_query($sql1);
					$entry = tep_db_fetch_array($result_sql1);
					$menu_title = $entry['cms_menu_lang_setting_key_value'];
				}
	 		}

			$row_obj = array();
			$row_obj = array( 'id' => $rows['cms_menu_id'],
			'title' => $menu_title,
			'type' => $rows['cms_menu_content_type'],
			'status' => $rows['cms_menu_status'],
			'sorting' => $rows['cms_menu_sort_order']);

			$listing_obj[] = $row_obj;
		}

		$menu_type_options = array (
			array ('id' => 1, 'text' => LIST_MENU_TYPE_1),
			array ('id' => 2, 'text' => LIST_MENU_TYPE_2),
			array ('id' => 3, 'text' => LIST_MENU_TYPE_3),
			array ('id' => 4, 'text' => LIST_MENU_TYPE_4),
			array ('id' => 5, 'text' => LIST_MENU_TYPE_5)
		);

		ob_start();
?>
<script language="javascript">
<!--
jQuery.noConflict();

function change_status(id) {
	jQuery.get("?action=changestatus&id="+id, function(data){
		if (data)
			jQuery('#status-'+id).html(data);
	});
}

function deleteentry(s,t,id) {
	answer = confirm('Are you sure to delete '+ (trim_str(s) != '' ? "<" + s + "> " : '') + t + ' record?')
	if (answer !=0) { 
		jQuery.get("?action=delete&id="+id, function(data){
			if (data == "success")
	 			jQuery('#row-'+id).fadeOut('slow');
		});
	}
}

//-->
</script>
			<form name=listing>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td align="left">[ <?php  ?><a href="?selected_box=infolinks&action=add_form&menu_type=<?php echo urlencode($menu_type); ?>" ><?php echo LINK_ADD_MENU; ?></a> ]</td>
								<td class=main align="right">
									<?php echo ENTRY_MENU_TYPE; ?> : 
									<?=tep_draw_pull_down_menu("menu_type", $menu_type_options, ($menu_type) ? $menu_type : '', ' onchange="document.location.href=\'?selected_box=infolinks&menu_type=\'+ escape(document.listing.menu_type.value)"')?>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign="top">
						<table border="0" width="100%" cellspacing="1" cellpadding="2">
		 					<tr>
			 					<td class="reportBoxHeading"><?php echo SUB_TABLE_HEADING_MENU_TITLE; ?></td>
			 					<td class="reportBoxHeading" align=center><?php echo SUB_TABLE_HEADING_CONTENT_TYPE; ?></td>
								<td class="reportBoxHeading" align=center><?php echo SUB_TABLE_HEADING_SORT_ORDER; ?></td>
								<td class="reportBoxHeading" align=center><?php echo SUB_TABLE_HEADING_STATUS; ?></td>
		 						<td class="reportBoxHeading" width="5%" align=center><?php echo SUB_TABLE_HEADING_ACTION; ?></td>
		 					</tr>
<?php
$entryCount = 0;

foreach ($listing_obj as $rows) {
	$entryCount++;
	
	($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
?>
							<tr id="row-<?php echo $rows['id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
								<td class="reportRecords" valign="top"><?php echo $rows['title']; ?></td>
								<td class="reportRecords" valign="top" align=center><?php echo $rows['type']; ?></td>
								<td class="reportRecords" valign="top" align=center><?php echo $rows['sorting']; ?></td>
								<td class="reportRecords" valign="top">
									<div style="text-align: center;" id="status-<?php echo $rows['id']; ?>">
								<?php
									if ($rows['status'] == "1")	{
								?>
										<img src="images/icon_status_green.gif" border=0> <a href="javascript:change_status('<?php echo $rows['id']; ?>')"><img src="images/icon_status_red_light.gif" border=0></a>
								<?php
									}
									else {
								?>
										<a href="javascript:change_status('<?php echo $rows['id']; ?>')"><img src="images/icon_status_green_light.gif" border=0></a> <img src="images/icon_status_red.gif" border=0>
								<?php
									}
								?>
									</div>
								</td>
								<td class="reportRecords" valign="top" align=center>
									<a href="?selected_box=infolinks&action=add_form&id=<?php echo $rows['id']; ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                                                        <a href="javascript:void(deleteentry('Menu','<?php echo addslashes(htmlentities ($rows['title'])); ?>','<?php echo htmlentities ($rows['id']); ?>'))"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
								</td>
							</tr>
<?php
}
?>
						</table>
			 			</td>
			 		</tr>
				</table>
				</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	function addForm($id = "") {
		global $language_obj;

		$listing_html = '';

 		foreach ($language_obj as $language_rows) {
			if ($language_rows['default'] == "default")
				$default_language_id = $language_rows['languages_id'];
				
			$javascript_elements[] = "menu_content_".$language_rows['languages_id'];
		}
		
		$printelements = implode (",",$javascript_elements);

		if ($id) {
			$sql = "SELECT * 
					FROM ".TABLE_CMS_MENU." 
					WHERE cms_menu_id = '$id'";
			$result_sql = tep_db_query($sql);
	 		$rows = tep_db_fetch_array($result_sql);

	 		foreach ($language_obj as $language_rows) {
				$language_id = $language_rows['languages_id'];

				$sql1 = "SELECT * 
						FROM ".TABLE_CMS_MENU_VALUE." 
						WHERE cms_menu_id = '$id' 
							AND languages_id = '$language_id'";
				$result_sql1 = tep_db_query($sql1);
				while ($entry = tep_db_fetch_array($result_sql1)) {
					$rows[$entry['cms_menu_lang_setting_key']."_{$language_id}"] = $entry['cms_menu_lang_setting_key_value'];
				}
	 		}

			$sql2 = "SELECT cms_linked_filename  
					FROM ".TABLE_CMS_MENU_TAB_PAGE." 
					WHERE cms_menu_id='$id' 
					ORDER BY cms_linked_filename ASC";
			$result_sql2 = tep_db_query($sql2);
			while ($rows2 = tep_db_fetch_array($result_sql2)) {
				if ($rows2['cms_linked_filename'])
					$base_filename .= $rows2['cms_linked_filename']."\r\n";	
			}
		}

		if ($_GET['menu_type']) {
			$rows['cms_menu_type'] = $_GET['menu_type'];
		}

		$menu_type_options = array (
			array ('id' => '', 'text' => ''),
			array ('id' => 1, 'text' => LIST_MENU_TYPE_1),
			array ('id' => 2, 'text' => LIST_MENU_TYPE_2),
			array ('id' => 3, 'text' => LIST_MENU_TYPE_3),
			array ('id' => 4, 'text' => LIST_MENU_TYPE_4),
			array ('id' => 5, 'text' => LIST_MENU_TYPE_5)
		);

		$menu_content_type_options = array 	(	
			array ('id' => 'url', "text" => "URL"),
			array ('id' => 'content', "text" => "Content")
		);

		ob_start();
?>
<script language="JavaScript" src="includes/javascript/tiny_mce/tiny_mce.js"></script>
<script language="javascript" type="text/javascript">
	tinyMCE.init({
//		mode : "exact",
		mode : "none",
		elements : "<?php echo $printelements; ?>",
		theme : "advanced",
		skin : "o2k7",
		verify_html : false,
		relative_urls : false,
		convert_urls : false,
		forced_root_block : false,
		force_p_newlines : false,
		verify_css_classes : false,
		nowrap : true,
		cleanup : false,
		fix_table_elements : false,

		plugins : "inlinepopups,safari,style,layer,table,advhr,advimage,advlink,insertdatetime,preview,searchreplace,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras",
		theme_advanced_buttons1_add : "fontselect,fontsizeselect",
		theme_advanced_buttons2_add_before: "cut,copy,paste,pastetext,separator",
		theme_advanced_buttons2_add : "preview,separator,forecolor,backcolor,separator,visualchars,iespell,advhr,separator,fullscreen",
		theme_advanced_buttons3_add_before : "tablecontrols,separator",
		theme_advanced_buttons3_add : "insertlayer,moveforward,movebackward,absolute,separator,styleprops",
		theme_advanced_buttons4 : "",
		theme_advanced_toolbar_location : "top",
		theme_advanced_toolbar_align : "left",
		theme_advanced_statusbar_location : "bottom",
		theme_advanced_resizing : true,

//		content_css : "/images/css/wow_stylesheet.php",
//		extended_valid_elements : "hr[class|width|size|noshade],font[face|size|color|style],span[class|align|style]",
		nonbreaking_force_tab : true,
		apply_source_formatting : true,
		relative_urls : false,
		remove_script_host : false
	});
	
</script>
<script language="javascript">
<!--
jQuery.noConflict();

function display_content_type() {
	if (jQuery("#menu_content_type").val() == "url") {
		jQuery("#content_type_content").hide();
		jQuery("#content_type_url").fadeIn('fast');
	}
	else {
		jQuery("#content_type_url").hide();
		jQuery("#content_type_content").fadeIn('fast');
	}
}

function check_form() {
	var error = 0;
	var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

	if (document.menu_form.menu_type.value == "") {
		error_message = error_message + "* The 'Menu Type' value is invalid.\n";
		error = 1;
	}

	if (document.menu_form.menu_title_<?php echo $default_language_id; ?>.value == "") {
		error_message = error_message + "* The 'Menu Title' entry must be entered.\n";
		error = 1;
	}
	
	if (document.menu_form.menu_content_type.value == "url") {
		if (document.menu_form.menu_url.value == "") {
			error_message = error_message + "* The 'URL' entry must be entered.\n";
			error = 1;
		}
	}

	if (validateInteger(document.menu_form.menu_sorting.value) == false) {
		error_message = error_message + "* The 'Menu Sorting' value is invalid.\n";
		error = 1;
	}

	if (error == 1) {
		alert(error_message);
		return false;
	}
	else {
		return true;
	}
}

jQuery(document).ready(function() {
	display_content_type();

<?php
if ($id) {
?>
	var seourl = jQuery("#menu_seo_alias").val();
	jQuery("#dis_seo_url").html(seourl);
	
	jQuery("#menu_seo_alias").keyup(function (){
		var seourl = jQuery("#menu_seo_alias").val();
		var str = filter_special_char(seourl);
		var seourl = str.toLowerCase(); //to lowercase
		
		jQuery("#dis_seo_url").html(seourl);
	});
<?php
}
?>
});

//-->
</script>
<?=tep_draw_form('menu_form', 'menu_management.php', 'selected_box=infolinks&action=add', 'post', ' onSubmit="return check_form();" id="menu_form"')?>
<table cellspacing="2" cellpadding="2" border="0">
<tbody><tr>
		<td>
			<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tbody><tr>
						<td class="pageHeading" valign="top"><?php if ($id != "") { echo HEADING_EDIT_MENU; } else { echo HEADING_ADD_MENU; } ?></td>
						<td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
					</tr>
			</tbody></table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" cellpadding="2" cellspacing="2">
				<tbody>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_MENU_TYPE; ?></td>
					<td class="main">
						<?=tep_draw_pull_down_menu("menu_type", $menu_type_options, tep_not_null($rows['cms_menu_type']) ? $rows['cms_menu_type'] : '', ' id="menu_type"')?>
					</td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_MENU_TITLE; ?></td>
					<td class="main">
					 <table cellpadding=1 cellspacing=0 border=0>
<?php
foreach ($language_obj as $language_row) {
$count_languages_id = $language_row['languages_id'];
?>
					 <tr>
						<td class="main"><b><?php echo $language_row['name']; ?></b></td><td class="main"><?=tep_draw_input_field('menu_title_'.$count_languages_id, $rows["menu_title_{$count_languages_id}"], ' id="menu_title_'.$count_languages_id.'" size="40" ')?></td>
<?php
	if ($language_row['default'] == "default"){
		print "						<td><span class=\"fieldRequired\">* Required</span></td></tr>\n";
	}
	else {
		print "						</tr>\n";
	}
}
?>
					 </table>
					</td>
				</tr>
				<tr>
					<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_CONTENT_TYPE; ?></td>
					<td class="main">
						<?=tep_draw_pull_down_menu("menu_content_type", $menu_content_type_options, $rows['cms_menu_content_type'] , ' id="menu_content_type" onchange="display_content_type()"')?>
						<br><br>
						<div id="content_type_url" style="padding: 10px; background-color: FFFFCC; display: visible; width: 100%">
							<table cellpadding=3 cellspacing=0 border=0>
								<tr>
									<td class="main"><?php echo ENTRY_URL; ?> :</td>
									<td class="main"><?=tep_draw_input_field('menu_url', $rows['cms_menu_url'], ' id="menu_url" size="40" ')?> <span class="fieldRequired">* Required</span></td>
								</tr>
							</table>
							<table cellpadding=3 cellspacing=0 border=0 id="div_base_filename">
								<tr>
									<td class="main" valign=top><?php echo ENTRY_BASE_FILE_NAME; ?> :</td>
									<td class="main"><?=tep_draw_textarea_field('base_filename' , 'soft', '40', '6', $base_filename , ' id="base_filename"')?></textarea></td>
								</tr>
							</table>
						</div>
						<div id="content_type_content" style="padding: 10px; background-color: FFFFCC; width: 100%">
						<table cellpadding=3 cellspacing=0 border=0 width=100%>
							<tr>
								<td class="main">
<?php
foreach ($language_obj as $language_row) {

if ($firstlanguagetextarea)
	print "<br><br>";
else
	$firstlanguagetextarea = 1;

$count_languages_id = $language_row['languages_id'];

?>
								<table cellpadding=0 border=0 width=100%>
									<tr>
										<td><b><?php echo $language_row['name']; ?></b></td>
										<td align=right>
											<div id="menu_content_<?=$language_row['languages_id']?>_start">
												<a href="javascript:starteditor('menu_content_<?=$language_row['languages_id']?>')">HTML Editor</a>
											</div>
											<div style="display: none" id="menu_content_<?=$language_row['languages_id']?>_stop">
												<a href="javascript:stopeditor('menu_content_<?=$language_row['languages_id']?>')">Text Editor</a>
											</div>
										</td>
									</tr>
								</table>
								<?=tep_draw_textarea_field('menu_content_'.$language_row['languages_id'] , 'soft', '40', '6', htmlspecialchars($rows["menu_content_{$count_languages_id}"]) , ' style="width: 100%" id="menu_content_'.$language_row['languages_id'].'"')?>
<?php
}
?>

								</td>
							</tr>
							<tr>
								<td>
									<table cellpadding=3 cellspacing=0 border=0>
										<tr>
											<td class="main"><?php echo ENTRY_RIGHT_PANEL; ?> :</td>
<?php
if ($rows['cms_menu_right_navigation'] == "0") {
?>
					<td class="main"><?=tep_draw_selection_field('right_navigation', 'radio', '1', false)?> <?php echo ENTRY_VALUE_DISPLAY; ?> <?=tep_draw_selection_field('right_navigation', 'radio', '0', true)?> <?php echo ENTRY_VALUE_NODISPLAY; ?></td>
<?php
}
else {
?>
					<td class="main"><?=tep_draw_selection_field('right_navigation', 'radio', '1', true)?> <?php echo ENTRY_VALUE_DISPLAY; ?> <?=tep_draw_selection_field('right_navigation', 'radio', '0', false)?> <?php echo ENTRY_VALUE_NODISPLAY; ?></td>
<?php
}
?>
										</tr>
										<tr>
											<td class="main"><?php echo ENTRY_SEO_URL_ALIAS; ?> :</td>
											<td class="main"><?=tep_draw_input_field('menu_seo_alias', $rows['cms_menu_seo_alias'], ' id="menu_seo_alias" size="43" ')?></td>
										</tr>
<?php
if ($id) {
?>
										<tr>
											<td class="main"><?php echo ENTRY_SEO_URL; ?> :</td>
											<td class="main"><span id="dis_seo_url"></span>-m-<?php echo $rows['cms_menu_id']; ?>.ogm</td>
										</tr>
<?php
	if ($rows['cms_menu_seo_alias']) {
?>
										<tr>
											<td class="main"><?php echo ENTRY_CURRENT_SEO_URL; ?> :</td>
											<td class="main"><?php echo $rows['cms_menu_seo_alias']; ?>-m-<?php echo $rows['cms_menu_id']; ?>.ogm</td>
										</tr>
<?php
	}
}
?>
									</table>
								</td>
							</tr>
						</table>
						</div>
					</td>
				</tr>
				<tr>
					<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
				</tr>
				<tr>
					<td class="main"><?php echo ENTRY_STATUS; ?></td>
<?php
if ($rows['cms_menu_status'] != "1") {
?>
					<td class="main"><?=tep_draw_selection_field('menu_status', 'radio', '1', false)?> <?php echo ENTRY_VALUE_ACTIVE; ?> <?=tep_draw_selection_field('menu_status', 'radio', '0', true)?> <?php echo ENTRY_VALUE_INACTIVE; ?></td>
<?php
}
else {
?>
					<td class="main"><?=tep_draw_selection_field('menu_status', 'radio', '1', true)?> <?php echo ENTRY_VALUE_ACTIVE; ?> <?=tep_draw_selection_field('menu_status', 'radio', '0', false)?> <?php echo ENTRY_VALUE_INACTIVE; ?></td>
<?php
}
?>
				</tr>
				<tr>
					<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
				</tr>
<!--
				<tr>
					<td class="main" valign=top>Sub Menu Script</td>
					<td class="main">
<?php
foreach ($language_obj as $language_row) {

$count_languages_id = $language_row['languages_id'];
?>
						<b><?php echo $language_row['name']; ?></b><br>
						<?=tep_draw_textarea_field('menu_submenu_script_'.$language_row['languages_id'] , 'soft', '60', '4', $rows["menu_submenu_script_{$count_languages_id}"] , ' id="menu_submenu_script_'.$language_row['languages_id'].'"')?><br><br>
<?php
}
?>
					</td>
				</tr>
				<tr>
					<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
				</tr>
-->
				<tr>
					<td class="main"><?php echo ENTRY_SORT_ORDER; ?></td>
					<td class="main">
					<?=tep_draw_input_field('menu_sorting', ($rows['cms_menu_sort_order']) ? $rows['cms_menu_sort_order'] : '0' , ' id="menu_sorting" size="5" ')?>
				</tr>
				<tr>
					<td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="1" width="1"></td>
				</tr>
			</tbody></table>
		</td>
	</tr>
	<tr>
		<td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
	</tr>
	<tr>
		<td class="main" align="right">
			<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
			<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, '?selected_box=infolinks', '', 'inputButton')?>
		</td>
	</tr>
</tbody>
<tbody><tr>
</table>
<?php
if ($id) {
?>
<input type=hidden name=id value="<?php echo $id; ?>">
<?php
}
?>
</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
}
?>