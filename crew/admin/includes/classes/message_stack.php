<?
/*
  	$Id: message_stack.php,v 1.3 2005/06/16 08:43:42 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
	
  	Example usage:
	
  	$messageStack = new messageStack();
  	$messageStack->add('Error: Error 1', 'error');
  	$messageStack->add('Error: Error 2', 'warning');
  	if ($messageStack->size > 0) echo $messageStack->output();
*/

class messageStack extends tableBlock {
	var $size = 0;
	public $errors = array(), $identifier = '';

    function messageStack() {
        $this->loadMessageStack();
	}

	function loadMessageStack(){
        global $memcache_obj;
        if(!empty($this->identifier)){
            $cache_key = '/messageToStack/'.session_id().'/'.$this->identifier;
            $cache_result = $memcache_obj->fetch($cache_key);
            if ($cache_result !== FALSE) {
                $_stored_message = $cache_result;
                $memcache_obj->delete($cache_key);
            } else {
                $_stored_message = array();
            }

            foreach($_stored_message as &$message){
                $this->add($message['text'], $message['type']);
            }
        }
        else{
            if (isset($_SESSION['messageToStack'])) {
                for ($i = 0, $n = sizeof($_SESSION['messageToStack']); $i < $n; $i++) {
                    $this->add($_SESSION['messageToStack'][$i]['text'], $_SESSION['messageToStack'][$i]['type']);
                }
                unset($_SESSION['messageToStack']);
            }
        }
    }
	
    function add($message, $type = 'error') {
      	if ($type == 'error') {
        	$this->errors[] = array('params' => 'class="messageStackError"', 'text' => tep_image(DIR_WS_ICONS . 'error.gif', ICON_ERROR) . '&nbsp;' . $message);
      	} else if ($type == 'warning') {
        	$this->errors[] = array('params' => 'class="messageStackWarning"', 'text' => tep_image(DIR_WS_ICONS . 'warning.gif', ICON_WARNING) . '&nbsp;' . $message);
      	} else if ($type == 'success') {
        	$this->errors[] = array('params' => 'class="messageStackSuccess"', 'text' => tep_image(DIR_WS_ICONS . 'success.gif', ICON_SUCCESS) . '&nbsp;' . $message);
      	} else {
        	$this->errors[] = array('params' => 'class="messageStackError"', 'text' => $message);
      	}
		
      	$this->size++;
	}
	
    function add_session($message, $type = 'error') {
        global $memcache_obj;

        $_message = array('text' => $message, 'type' => $type);

      	if(!empty($this->identifier)){
      	    $cache_key = '/messageToStack/'.session_id().'/'.$this->identifier;
            $cache_result = $memcache_obj->fetch($cache_key);
            if ($cache_result !== FALSE) {
                $_stored_message = $cache_result;
            } else {
                $_stored_message = array();
            }
            $_stored_message[] = $_message;
            $memcache_obj->store($cache_key, $_stored_message, 86400);
        }
        else{
            if (!isset($_SESSION['messageToStack'])) {
                $_SESSION['messageToStack'] = array();
            }
            $_SESSION['messageToStack'][] = $_message;
        }
	}
	
    function reset() {
      	$this->errors = array();
      	$this->size = 0;
    }
	
    function output() {
      	$this->table_data_parameters = 'class="messageBox"';
      	return $this->tableBlock($this->errors);
	}
}
?>