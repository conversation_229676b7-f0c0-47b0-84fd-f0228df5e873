<?php

class mgc_sales_report {

    private $api_credential_key = MGC_API_CREDENTIAL_CODE;
    private $api_credential_secret = MGC_API_CREDENTIAL_KEY;
    private $cat_id = MGC_CATEGORY_ID;
    private $mgc_url = HTTPS_MGC_SERVER;
    public $start_date, $end_date, $order_status;

    public function __construct() {

    }

    public function searchForm() {
        $read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

        // order status
        $order_status[] = array('id' => '', 'text' => "All");
        $sql = 'SELECT orders_status_id, orders_status_name FROM ' . TABLE_ORDERS_STATUS;
        $res = tep_db_query($sql, 'read_db_link');
        while ($row = tep_db_fetch_array($res)) {
            $order_status[] = array('id' => $row["orders_status_id"], 'text' => $row["orders_status_name"]);
        }

        ob_start();
        ?>
        <script language="javascript">
                    <!--
                                                                            function check_form() {
                        var error = 0;
                        var error_message = "Errors have occured during the process of your search!\nPlease make the following corrections:\n\n";

                        if (document.menu_form.f_start.value == "") {
                            error_message = error_message + "* 'Start Date' entry must be entered.\n";
                            error = 1;
                        }

                        if (document.menu_form.f_end.value == "") {
                            error_message = error_message + "* 'End Date' entry must be entered.\n";
                            error = 1;
                        }

                        if (!validStartAndEndDate(document.menu_form.f_start.value, document.menu_form.f_end.value)) {
                            error_message = error_message + "* 'End Date' must be greater than or equal to 'Start Date'.\n";
                            error = 1;
                        }

                        if (!validStartEndDateRange(document.menu_form.f_start.value, document.menu_form.f_end.value, 31)) {
                            error_message += "* 'Start Date' to 'End Date' must be within 31 days.\n";
                            error = 1;
                        }

                        if (error == 1) {
                            alert(error_message);
                            return false;
                        } else {
                            return true;
                        }
                    }
                    //-->
                                                                        </script>

            <?= tep_draw_form('menu_form', FILENAME_MGC_SALES_REPORT, 'action=report', 'post', ' onSubmit="return check_form();" id="menu_form"') ?>
            <table border="0" cellpadding="2" cellspacing="2" width="100%">
                <!-- calendar -->
                <tr>
                    <td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                </tr>
                <tr>
                    <td class="main" valign="top" width="10%">Order Create Start Date<br /><small>(YYYY-MM-DD)</small></td>
                    <td class="main" align="left">
                        <?= tep_draw_input_field('f_start', "", 'id="f_start" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.menu_form.f_start); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.menu_form.f_start);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" border="0" alt=""></a>' ?>
                    </td>
                </tr>
                <tr>
                    <td class="main" valign="top">Order Create End Date<br /><small>(YYYY-MM-DD)</small></td>
                    <td class="main" align="left">
                        <?= tep_draw_input_field('f_end', "", 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.menu_form.f_end); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.menu_form.f_end);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" border="0" alt=""></a>' ?>
                    </td>
                </tr>
                <!-- order status -->
                <tr>
                    <td class="main" valign="top">Order Status</small></td>
                    <td class="main" align="left"><?= tep_draw_pull_down_menu("f_status", $order_status, "", ' id="f_status"') ?></td>
                </tr>
                <tr>
                    <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td class="main" align="left">
                        <?php echo tep_submit_button("Report", "Report", ' name="f_action" ', 'inputButton'); ?>&nbsp;&nbsp;&nbsp;
                        <?php echo tep_submit_button("Export", "Export", ' name="f_action" ', 'inputButton'); ?>
                    </td>
                </tr>
            </table>
        </form>
        <?php
        $html = ob_get_contents();
        ob_end_clean();

        return $html;
    }

    public function searchResult($f_action) {
        $read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

        $order_status = array();
        $cpin = array();
        $report = array();
        $trans = array();

        // product id
        $_data = array();
        $sql = 'SELECT products_id FROM ' . TABLE_PRODUCTS . ' WHERE products_cat_id_path like "_' . $this->cat_id . '_%"';
        $res = tep_db_query($sql, 'read_db_link');
        while ($row = tep_db_fetch_array($res)) {
            $_data[] = $row["products_id"];
        }
        $prod_id = implode(",", $_data);

        if (!empty($prod_id)) {
            // order status
            $sql = 'SELECT orders_status_id, orders_status_name FROM ' . TABLE_ORDERS_STATUS;
            $res = tep_db_query($sql, 'read_db_link');
            while ($row = tep_db_fetch_array($res)) {
                $order_status[$row["orders_status_id"]] = $row["orders_status_name"];
            }

            $sql = 'SELECT o.orders_id, o.date_purchased, o.orders_status, o.currency, o.currency_value,
                        o.payment_methods_parent_id, o.payment_methods_id, 
                        op.orders_products_id, op.products_id, op.final_price, op.products_bundle_id 
                    FROM ' . TABLE_ORDERS_PRODUCTS . ' AS op
                    INNER JOIN ' . TABLE_ORDERS . ' AS o
                        ON o.orders_id = op.orders_id
                    WHERE o.date_purchased >= "' . $this->start_date . '"
                        AND o.date_purchased <= "' . $this->end_date . '" ' .
                    (!empty($this->order_status) ? 'AND o.orders_status = ' . $this->order_status : '') . '
                        AND op.products_id IN (' . $prod_id . ')
                    ORDER BY o.orders_id';
            $res = tep_db_query($sql, 'read_db_link');
            while ($row = tep_db_fetch_array($res)) {
                if ($row["payment_methods_id"] == 0) {
                    $row["pm"] = "Store Credit";
                } else {
                    $_sql = 'SELECT pm1.payment_methods_title 
                            FROM ' . TABLE_PAYMENT_METHODS . ' AS pm1
                            WHERE pm1.payment_methods_id = ' . $row["payment_methods_parent_id"];
                    $_res = tep_db_query($_sql, 'read_db_link');
                    $_row = tep_db_fetch_array($_res);
                    $row["pm"] = $_row["payment_methods_title"];

                    $_sql = 'SELECT pm2.payment_methods_title
                            FROM ' . TABLE_PAYMENT_METHODS . ' AS pm2
                            WHERE pm2.payment_methods_id = ' . $row["payment_methods_id"];
                    $_res = tep_db_query($_sql, 'read_db_link');
                    $_row = tep_db_fetch_array($_res);
                    $row["pm"] .= ' - ' . $_row["payment_methods_title"];
                }

                $_sql = "SELECT cpc.custom_products_code_id, cpc.file_type, cpc.to_s3, cpc.code_date_added,
                            cpc.custom_products_code_viewed 
                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                        WHERE cpc.orders_products_id = " . $row["orders_products_id"] . "
                            AND cpc.products_id = " . $row["products_id"];
                $_res = tep_db_query($_sql, 'read_db_link');
                while ($_row = tep_db_fetch_array($_res)) {
                    // get CDK serial
                    $cdk = "";
                    $cpc_obj = new custom_product_code();
                    $cpc_res = $cpc_obj->getCode($_row["custom_products_code_id"], $_row['to_s3'], $row['products_id'], $_row['code_date_added']);
                    if ($cpc_res !== FALSE) {
                        $_data = tep_decrypt_data($cpc_res);

                        $arr = explode("<br>", $_data);
                        foreach ($arr as $num => $val) {
                            preg_match("/Serial/", $val, $match);
                            if ($match) {
                                $cdk = str_replace("Serial: ", "", $val);
                                break;
                            }
                        }
                    }

                    // get unit final price
                    if ($row["products_bundle_id"] != 0) {
                        $qty = 0;
                        $bundle_qty = 0;
                        $_sql2 = "SELECT op.products_quantity, op.final_price
                                FROM orders_products AS op
                                WHERE op.orders_id = " . $row["orders_id"] . "
                                    AND op.products_id = " . $row["products_bundle_id"] . "
                                    AND op.products_bundle_id = 0";
                        $_res2 = tep_db_query($_sql2, 'read_db_link');
                        $_row2 = tep_db_fetch_array($_res2);
                        $bundle_qty = $_row2["products_quantity"];

                        $_sql3 = "SELECT op.products_quantity
                                FROM orders_products AS op
                                WHERE op.orders_id = " . $row["orders_id"] . "
                                    AND op.products_bundle_id = " . $row["products_bundle_id"];
                        $_res3 = tep_db_query($_sql3, 'read_db_link');
                        while ($_row3 = tep_db_fetch_array($_res3)) {
                            $qty += $_row3["products_quantity"] / $bundle_qty;
                        }
                        $row["final_price"] = $_row2["final_price"] / $qty;

                        if (isset($report[$row["orders_id"]])) {
                            $report[$row["orders_id"]]["order_unit_price"] = ($row["final_price"] * $row["currency_value"]);
                            $report[$row["orders_id"]]["order_unit_price_usd"] = $row["final_price"];
                        }
                    }

                    if (!isset($report[$row["orders_id"]])) {
                        $report[$row["orders_id"]] = array(
                            "order_date" => $row["date_purchased"],
                            "order_status" => $order_status[$row["orders_status"]],
                            "order_currency" => $row["currency"],
                            "order_unit_price" => ($row["final_price"] * $row["currency_value"]),
                            "order_unit_price_usd" => $row["final_price"],
                            "order_pg" => $row["pm"],
                            "order_serial" => array()
                        );
                    }

                    if (!empty($cdk)) {
                        if ($_row["custom_products_code_viewed"] == 1) {
                            $cpin[] = $cdk;
                        }
                        $report[$row["orders_id"]]["order_serial"][$cdk] = array();
                    }
                }
            }

            if (!empty($cpin)) {
                $chunk = array_chunk($cpin, 100, true);
                $_cnt = count($chunk);

                for ($i = 0; $_cnt > $i; $i++) {
                    $req = array(
                        "api_key" => $this->api_credential_key,
                        "timestamp" => time(),
                        "data" => array("pin" => $chunk[$i])
                    );
                    $body = json_encode($req);

                    // MGC gift card redemption status
                    $curl_obj = new curl();
                    $curl_obj->http_header = array(
                        'Content-Type:application/json',
                        'signature:' . hash_hmac('sha256', $body, $this->api_credential_secret)
                    );
                    $curl_obj->custom_request_type = "POST";
                    $curl_res = $curl_obj->curl_post($this->mgc_url, $body);
                    $c_data = json_decode($curl_res, true);
                    if (isset($c_data["status"]) && ($c_data["status"] == 200)) {
                        if (isset($c_data["response"]["pin"])) {
                            $trans = array_merge($trans, $c_data["response"]["pin"]);
                        }
                    } else {
                        if (isset($c_data["message"])) {
                            $messageStack->add_session($c_data["message"], 'error');
                        } else {
                            $messageStack->add_session($curl_obj->get_error(), 'error');
                        }
                    }
                    sleep(1);
                }
            }
        }

        switch ($f_action) {
            case "export":
                return $this->_searchExport($report, $trans);
                break;
            default:
                return $this->_searchResult($report, $trans);
                break;
        }
    }

    private function _searchResult($report, $trans) {
        ob_start();
        ?>
        <table border="0" width="100%" cellspacing="1" cellpadding="2">
            <tr>
                <td class="ordersBoxHeading" nowrap>Pin Sold Date & Time</td>
                <td class="ordersBoxHeading" nowrap>Redeemed Date & Time</td>
                <td class="ordersBoxHeading" nowrap>Serial</td>
                <td class="ordersBoxHeading" nowrap>Currency</td>
                <td class="ordersBoxHeading" nowrap>Card Value</td>
                <td class="ordersBoxHeading" nowrap>Redeemed Value</td>
                <td class="ordersBoxHeading" nowrap>Pin Status</td>
                <td class="ordersBoxHeading" nowrap>Order ID</td>
                <td class="ordersBoxHeading" nowrap>Order Status</td>
                <td class="ordersBoxHeading" nowrap>Order Currency</td>
                <td class="ordersBoxHeading" nowrap>Order Amount</td>
                <td class="ordersBoxHeading" nowrap>Order Amount in USD</td>
                <td class="ordersBoxHeading" nowrap>PG</td>
            </tr>
            <?php
            if (!empty($report)) {
                $row_style = 'ordersListingOdd';
                foreach ($report as $order_id => $row) {
                    foreach ($report[$order_id]["order_serial"] as $cdk => $info) {
                        if (!empty($cdk)) {
                            $_cnt = (isset($trans[$cdk]) ? count($trans[$cdk]) : 1);
                            $_cnt = ($_cnt > 0 ? $_cnt : 1);
                            for ($i = 0; $_cnt > $i; $i++) {
                                ?>
                                <tr class="<?= $row_style; ?>">
                                    <td class="ordersRecords" nowrap><?= $row["order_date"]; ?></td>
                                    <td class="ordersRecords" nowrap><?= (isset($trans[$cdk][$i]["redeem_date"]) ? $trans[$cdk][$i]["redeem_date"] : ""); ?></td>
                                    <td class="ordersRecords" nowrap><?= $cdk; ?></td>
                                    <td class="ordersRecords" nowrap><?= (isset($trans[$cdk][$i]["currency"]) ? $trans[$cdk][$i]["currency"] : ""); ?></td>
                                    <td class="ordersRecords" nowrap><?= (isset($trans[$cdk][$i]["card_value"]) ? $trans[$cdk][$i]["card_value"] : ""); ?></td>
                                    <td class="ordersRecords" nowrap><?= (isset($trans[$cdk][$i]["redeem_value"]) ? $trans[$cdk][$i]["redeem_value"] : ""); ?></td>
                                    <td class="ordersRecords" nowrap><?= (isset($trans[$cdk][$i]["status"]) ? $trans[$cdk][$i]["status"] : ""); ?></td>
                                    <td class="ordersRecords" nowrap><?= $order_id; ?></td>
                                    <td class="ordersRecords" nowrap><?= $row["order_status"]; ?></td>
                                    <td class="ordersRecords" nowrap><?= $row["order_currency"]; ?></td>
                                    <td class="ordersRecords" nowrap><?= $row["order_unit_price"]; ?></td>
                                    <td class="ordersRecords" nowrap><?= $row["order_unit_price_usd"]; ?></td>
                                    <td class="ordersRecords" nowrap><?= $row["order_pg"]; ?></td>
                                </tr>
                                <?php
                                $row_style = ($row_style == 'ordersListingEven' ? 'ordersListingOdd' : 'ordersListingEven');
                            }
                        }
                    }
                }
            } else {
                ?>
                <tr><td colspan="16" class="ordersRecords" nowrap style="text-align: center;">- No Result Found -</td></tr>
                <?php
            }
            ?>
        </table>
        <?php
        $html = ob_get_contents();
        ob_end_clean();

        return $html;
    }

    private function _searchExport($report, $trans) {
        if (!empty($report)) {
            $filename = "download/mgc_sales_report_" . date("YmdHis") . ".csv";
            $fp = fopen($filename, "a+");

            fwrite($fp, "Pin Sold Date & Time|Redeemed Date & Time|Serial|Currency|Card Value|Redeemed Value|Pin Status|Order ID|Order Status|Order Currency|Order Amount|Order Amount in USD|PG" . "\n");

            foreach ($report as $order_id => $row) {
                foreach ($report[$order_id]["order_serial"] as $cdk => $info) {
                    if (!empty($cdk)) {
                        $_cnt = (isset($trans[$cdk]) ? count($trans[$cdk]) : 1);
                        for ($i = 0; $_cnt > $i; $i++) {
                            fwrite($fp, $row["order_date"] . "|" .
                                    (isset($trans[$cdk][$i]["redeem_date"]) ? $trans[$cdk][$i]["redeem_date"] : "") . "|" .
                                    $cdk . "|" .
                                    (isset($trans[$cdk][$i]["currency"]) ? $trans[$cdk][$i]["currency"] : "") . "|" .
                                    (isset($trans[$cdk][$i]["card_value"]) ? $trans[$cdk][$i]["card_value"] : "") . "|" .
                                    (isset($trans[$cdk][$i]["redeem_value"]) ? $trans[$cdk][$i]["redeem_value"] : "") . "|" .
                                    (isset($trans[$cdk][$i]["status"]) ? $trans[$cdk][$i]["status"] : "") . "|" .
                                    $order_id . "|" .
                                    $row["order_status"] . "|" .
                                    $row["order_currency"] . "|" .
                                    $row["order_unit_price"] . "|" .
                                    $row["order_unit_price_usd"] . "|" .
                                    $row["order_pg"] . "\n");
                        }
                    }
                }
            }

            fclose($fp);

            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename=' . $filename);
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($filename));
            readfile($filename);
            exit;
        }
    }

}
