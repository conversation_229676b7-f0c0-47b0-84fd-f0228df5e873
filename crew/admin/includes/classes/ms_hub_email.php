<?php

require('includes/configure.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');
include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

class ms_hub_email
{
    var $awsObj;

    public function __construct()
    {
        $awsParams = array(
            'key' => AWS_SQS_KEY,
            'secret' => AWS_SQS_SECRET,
        );

        $this->awsObj = new ogm_amazon_ws();
        $this->awsObj->init_sqs(AWS_SQS_PDF_QUEUE_URL, $awsParams);
    }

    public function setSqsEmail($data)
    {
        $error = false;
        $start_time = time();
        $start_time_m = microtime(true);

        // Use retry method
        $i = 0;
        $result = false;
        while ($i++ < 3) {
            try {
                $this->awsObj->sendMessage(json_encode(['data' => $data]));
            } catch (Exception $e) {
                $slack = new slack_notification();
                $message = "Error Message : " . (($e->getMessage()) ? $e->getMessage() : 'Empty Response From Server') . "\n";
                $message .= "Params : " . json_encode($data) . "\n";
                $message .= "Request Timestamp : " . date('Y-m-d H:i:s', $start_time) . "\n";
                $message .= "Total Time(ms) : " . (int)((microtime(true) - $start_time_m) * 1000);

                $slackData = json_encode(array(
                    'text' => '[OG Crew] SQS Email - ' . date("F j, Y H:i"),
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => $message,
                        )
                    )
                ));
                $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $slackData);
                $error = true;
            }

            // If response not false break the loop
            if (!$error) {
                break;
            }
            // Delays the program execution
            if ($i < 3) {
                sleep($i);
            }
        }

        return $error;
    }
}
