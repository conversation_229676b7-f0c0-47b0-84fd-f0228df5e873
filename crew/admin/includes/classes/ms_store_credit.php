<?php
if (file_exists(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_STORE_CREDIT)) {
    include_once(DIR_WS_LANGUAGES . (isset($_SESSION['language']) ? $_SESSION['language'] : 'english') . '/' . FILENAME_STORE_CREDIT);
}

include_once(DIR_WS_CLASSES . 'slack_notification.php');
include_once('includes/configure.php');

class ms_store_credit
{

    var $identity, $identity_email;

    // class constructor
    public function __construct($identity, $identity_email)
    {
        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user
    }

    private static function msApi($data, $method, $cid = '')
    {
        include_once(DIR_WS_CLASSES . 'curl.php');
        $result = false;

        if ($cid) {
            $url = MS_API_SC_URL . '/store-credits/' . $cid;
        } else {
            $url = MS_API_SC_URL . '/store-credits';
        }

        $timeNow = time();
        $header = array('Content-Type:application/json');

        $data['source'] = MS_API_SC_MERCHANT;
        $data['signature'] = md5(MS_API_SC_MERCHANT . $timeNow . '|' . MS_API_SC_SECRET);
        $data['time'] = $timeNow;

        $data = json_encode($data);

        $start_time = time();
        $start_time_m = microtime(true);

        $curl_obj = new curl();
        $curl_obj->connect_via_proxy = false;

        // Use retry method for curl to microservice
        $i = 0;
        $result = false;
        while ($i++ < 3) {
            try {
                $curl_resp = $curl_obj->curl_request($method, $url, $header, $data);
                $result = json_decode($curl_resp, true);

                if (json_last_error() != JSON_ERROR_NONE) {
                    throw new Exception(json_last_error_msg());
                } elseif (empty($result) || !isset($result['status']) || !isset($result['result'])) {
                    throw new Exception('Error Connecting to store credit micro-service');
                }

            } catch (Exception $e) {
                $slack = new slack_notification();
                $message = "Error Message : " . (($e->getMessage()) ? $e->getMessage() : 'Empty Response From Server') . "\n";
                $message .= "Fail Count : " . ($i) . "\n";
                $message .= "Endpoints : " . $url . "\n";
                $message .= "Response Body : " . json_encode($result) . "\n";
                $message .= "Params : " . $data . "\n";
                $message .= "Request Timestamp : " . date('Y-m-d H:i:s', $start_time) . "\n";
                $message .= "Total Time(ms) : " . (int)((microtime(true) - $start_time_m) * 1000);

                $slackData = json_encode(array(
                    'text' => '[OG Crew] MS SC Error - ' . date("F j, Y H:i"),
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => $message,
                        )
                    )
                ));
                $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $slackData);
                $result = false;
            }

            // If response not false break the loop
            if ($result) {
                break;
            }
            // Delays the program execution
            if ($i < 3) {
                sleep($i);
            }
        }

        unset($curl_obj);

        return $result;
    }

    private static function getUserParticulars($cid)
    {
        $user_info_select_sql = "   SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email, customers_gender AS gender,
                                        ci.customers_info_account_created_from AS sign_up_from, c.customers_disable_withdrawal as disable_withdrawal, c.customers_id
                                    FROM " . TABLE_CUSTOMERS . " AS c
                                    LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
                                        ON c.customers_id=ci.customers_info_id
                                    WHERE customers_id = '" . tep_db_input($cid) . "'";
        $user_info_result_sql = tep_db_query($user_info_select_sql);

        return $user_info_row = tep_db_fetch_array($user_info_result_sql);
    }

    // Get customer sc account status
    public static function getScAccountStatus($result)
    {
        if (isset($result['status']) && $result['status'] && $result['result']['Items']['user_status'] == 'ACTIVE') {
            return true;
        } elseif (isset($result['error']) && $result['error']['code'] == 14) {
            return true;
        } else {
            return false;
        }
    }

    // Get store credit transaction from the new Store Credit Microservice
    public static function getScTransactions($data)
    {
        include_once(DIR_WS_CLASSES . 'currencies.php');
        $currencies = new currencies();

        $data['list_type'] = 'TRANSACTION';

        if (isset($data['order_id']) && !empty($data['order_id'])) {
            $data['orders_id'] = $data['order_id'];
            unset($data['order_id']);
        }

        if (isset($data['activity']) && ($data['activity'] == 'P')) {
            $ot_sc_amount_select_sql = "SELECT `value`
                FROM " . TABLE_ORDERS_TOTAL . " 
                WHERE orders_id='" . tep_db_input($data['orders_id']) . "'
                    AND class='ot_gv'";

            $ot_sc_amount = tep_db_query($ot_sc_amount_select_sql);
            if ($ot_sc_amount_row = tep_db_fetch_array($ot_sc_amount)) {
                $ot_gv = tep_not_null($ot_sc_amount_row['value']) ? $ot_sc_amount_row['value'] : 0;
                if ($ot_gv > 0) {
                    $result = self::msApi($data, 'GET');
                    if ($result['status'] && !empty($result['result']['Items'])) {
                        return $result['result'];
                    } else {
                        // return false;
                        $sc_select_sql = "  SELECT store_credit_account_type, store_credit_history_debit_amount, store_credit_history_currency_id,
                                store_credit_history_date, customer_id, store_credit_history_trans_id, store_credit_history_r_after_balance, store_credit_history_nr_after_balance,
                                store_credit_history_added_by_role
                                FROM " . TABLE_STORE_CREDIT_HISTORY . "
                                WHERE store_credit_history_trans_id='" . tep_db_input($data['orders_id']) . "'
                                    AND store_credit_activity_type= '" . $data['activity'] . "'";
                        $sc_result_sql = tep_db_query($sc_select_sql);

                        $sc_used_array = [];
                        while ($sc_row = tep_db_fetch_array($sc_result_sql)) {
                            $sc_used_info =
                                array(
                                    'activity' => 'P',
                                    'activity_description' => 'Purchase',
                                    'activity_title' => 'P',
                                    'allow_negative' => 0,
                                    'brand' => 'OG',
                                    'created_date' => $sc_row['store_credit_history_date'],
                                    'free_conversion' => 0,
                                    'new_amount' => $sc_row['store_credit_history_r_after_balance'] + $sc_row['store_credit_history_nr_after_balance'],
                                    'new_currency' => $currencies->get_code_by_id($sc_row['store_credit_history_currency_id']),
                                    'order_id' => isset($data['orders_id']) ? (string)$data['orders_id'] : '',
                                    'param_1' => null,
                                    'param_2' => null,
                                    'param_3' => null,
                                    'previous_amount' => '',
                                    'previous_currency' => "",
                                    'request_id' => '',
                                    'requesting_id' => $sc_row['customer_id'],
                                    'requesting_role' => $sc_row['store_credit_history_added_by_role'],
                                    'transaction_amount' => $sc_row['store_credit_history_debit_amount'],
                                    'transaction_conversion_rate' => "",
                                    'transaction_currency' => $currencies->get_code_by_id($sc_row['store_credit_history_currency_id']),
                                    'transaction_id' => $sc_row['store_credit_history_trans_id'],
                                    'transaction_type' => "SUBTRACT_CREDIT",
                                    'user_id' => $sc_row['customer_id'],
                                    'user_role' => 'customer'
                                );
                            $sc_used_array['Items'][] = $sc_used_info;

                        }

                        return $sc_used_array;
                    }
                }
            }
        } else {
            $result = self::msApi($data, 'GET');

            if ($result['status']) {
                return $result['result'];
            } else {
                return false;
            }
        }
    }

    // Get all store credit transaction from the new Store Credit Microservice
    public static function getScAllTransactions($data)
    {
        $allListArray = array();
        $data['page'] = 1;
        $data['limit'] = 200;

        $arrayResult = self::getScTransactions($data);

        if ($arrayResult) {
            $allListArray = $arrayResult['Items'];

            if (isset($arrayResult['Page']) && $arrayResult['Page']['total_page'] > 1) {
                for ($i = 2; $i <= $arrayResult['Page']['total_page']; $i++) {
                    $data['page'] = $i;
                    $arrayResult = self::getScTransactions($data);
                    $allListArray = array_push($allListArray, $arrayResult['Items']);
                }
            }
        }

        return $allListArray;
    }

    // Get store credit balance from the new Store Credit Microservice
    public static function getScBalance($cid, $returnResponse = false)
    {
        $data['checking_type'] = 'BALANCE';
        $result = self::msApi($data, 'GET', $cid);

        if ($result['status']) {
            return array(
                'amount' => $result['result']['Items']['balance'],
                'currency' => $result['result']['Items']['currency'],
                'status' => self::getScAccountStatus($result)
            );
        } else {
            if ($returnResponse) {
                return $result;
            }

            return false;
        }
    }

    public static function getScCurrency($cid, $checkout_currency = DEFAULT_CURRENCY, $return_id = true)
    {
        global $currencies;

        if ($balanceArray = self::getScBalance($cid)) {
            if ($return_id) {
                return $currencies->get_id_by_code($balanceArray['currency']);
            } else {
                return $balanceArray['currency'];
            }
        } else {
            // use selected currency if no record found.
            if ($return_id) {
                return array_search($checkout_currency, $currencies->internal_currencies);
            } else {
                return $checkout_currency;
            }
        }
    }

    // Save store credit balance to the new Store Credit Microservice
    public static function setScTransaction($data, $operation)
    {
        $data['transaction_type'] = ($operation) ? $operation : 'ADD_CREDIT';
        $data['allow_negative'] = ($data['transaction_type'] == 'DEDUCT_CREDIT') ? 1 : 0;

        $result = self::msApi($data, 'POST');

        if ($result['status']) {
            return $result['result'];
        } else {
            return false;
        }
    }

    // Save store credit balance to the new Store Credit Microservice
    public static function setAdvanceScTransaction($cid, $data, $operation)
    {
        $result = self::setScTransaction($data, $operation);

        if ($result) {
            return array(
                'transaction_id' => $result['request_id'],
                'amount' => $result['amount'],
                'currency' => $result['currency']
            );
        } else {
            return false;
        }
    }

    public static function getScPromotionPercentage($customer_id)
    {
        $percentage = 0;

        if ($customer_id) {
            $customers_groups_select_sql = "SELECT customers_groups_id 
                                            FROM " . TABLE_CUSTOMERS . " 
                                            WHERE customers_id =  '" . tep_db_input($customer_id) . "'";
            $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
            $customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);

            $extra_sc_select_sql = "SELECT customers_groups_extra_sc
                                    FROM " . TABLE_CUSTOMERS_GROUPS . "
                                    WHERE customers_groups_id = '" . tep_db_input($customers_groups_row['customers_groups_id']) . "'";
            $extra_sc_result_sql = tep_db_query($extra_sc_select_sql);
            $extra_sc_row = tep_db_fetch_array($extra_sc_result_sql);

            if ($extra_sc_row['customers_groups_extra_sc'] > 0) {
                $percentage = $extra_sc_row['customers_groups_extra_sc'];
            }
        }

        return $percentage;
    }

    public function issueStoreCredit($cid, $act_type, $trans_array, $admin_msg, &$messageStack, $fullRefund = false)
    {
        global $language, $currencies;
        $error = false;

        $default_currency_id = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        $return_result = array();

        $sc_spended_amount_array = array(
            'SC' => 0,
            'RP' => 0,
            'NRP' => 0,
            'currency_id' => $default_currency_id,
            'currency_code' => DEFAULT_CURRENCY
        );

        $sc_issue_amount = 0;
        $payment_refund_amount = $non_sc_payment_refund_amount = $sc_payment_refund_amount = 0;
        $add_sc_amount = 0;
        $add_non_sc_amount = 0;

        $ot_subtotal = 0;
        $ot_sc = 0;
        $ot_coupon = 0;
        $ot_surcharge = 0;
        $ot_gst = 0;
        $refund_gst = false;
        $refund_gst_amount = 0;
        $refund_surcharge = false;
        $refund_surcharge_amount = 0;

        $display_admin_group = tep_get_admin_group_name($this->identity_email);
        if (tep_not_null($display_admin_group)) {
            $display_admin_group = ' [' . $display_admin_group . ']';
        }

        // If not found treat as reversible
        $sc_type = 'R';

        $payment_methods_obj = new payment_methods($trans_array['payment_methods_id']);
        $payment_methods_obj = $payment_methods_obj->payment_method_array;

        if (isset($payment_methods_obj) && (int)$payment_methods_obj->payment_methods_id > 0) {
            if ($payment_methods_obj->confirm_complete_days > 0) {
                ;
            } else {
                $sc_type = 'NR';
            }
        }

        // Will create store credit account if not exists
        $pay_currency_id = (isset($trans_array['pay_currency'])) ? array_search($trans_array['pay_currency'], $currencies->internal_currencies) : 0;

        if (isset($trans_array['add_sc_amount'])) {
            $add_sc_amount = trim($trans_array['add_sc_amount']);
        }

        if (isset($trans_array['add_non_sc_amount'])) {
            $add_non_sc_amount = trim($trans_array['add_non_sc_amount']);
        } else {
            $add_non_sc_amount = trim($trans_array['add_amount']);
        }

        if ((is_numeric($add_non_sc_amount) && $add_non_sc_amount > 0) || (is_numeric($add_sc_amount) && $add_sc_amount > 0)) {
            $ot_amount_select_sql = "   SELECT ot_subtotal.value AS ot_subtotal_value, ot_gv.value AS ot_gv_value,
                                            ot_coupon.value AS ot_coupon_value, ot_surcharge.value AS ot_surcharge_value,
                                            ot_gst.value AS ot_gst_value
                                        FROM " . TABLE_ORDERS_TOTAL . " AS ot_subtotal 
                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv
                                            ON ot_gv.orders_id = ot_subtotal.orders_id
                                                AND ot_gv.class='ot_gv'
                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_coupon
                                            ON ot_coupon.orders_id = ot_subtotal.orders_id
                                                AND ot_coupon.class='ot_coupon'
                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_surcharge
                                            ON ot_surcharge.orders_id = ot_subtotal.orders_id
                                                AND ot_surcharge.class='ot_surcharge'
                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gst
                                            ON ot_gst.orders_id = ot_subtotal.orders_id
                                                AND ot_gst.class='ot_gst'
                                        WHERE ot_subtotal.orders_id='" . tep_db_input($trans_array['id']) . "'
                                            AND ot_subtotal.class='ot_subtotal'";
            $ot_amount_result_sql = tep_db_query($ot_amount_select_sql);
            if ($ot_amount_row = tep_db_fetch_array($ot_amount_result_sql)) {
                $ot_subtotal = tep_not_null($ot_amount_row['ot_subtotal_value']) ? $ot_amount_row['ot_subtotal_value'] : 0;
                $ot_sc = tep_not_null($ot_amount_row['ot_gv_value']) ? $ot_amount_row['ot_gv_value'] : 0;
                $ot_coupon = tep_not_null($ot_amount_row['ot_coupon_value']) ? $ot_amount_row['ot_coupon_value'] : 0;
                $ot_surcharge = tep_not_null($ot_amount_row['ot_surcharge_value']) ? $ot_amount_row['ot_surcharge_value'] : 0;
                if (tep_not_null($ot_amount_row['ot_gst_value'])) {
                    $refund_gst = true;
                    $ot_gst = $ot_amount_row['ot_gst_value'];
                }
            }

            if ($add_non_sc_amount > 0) {
                $add_amount = $add_non_sc_amount;
                $exch_rate = 1;
                $used_sc = false;

                // Prepare query data for MS SC API
                $scDataRequest = array(
                    'order_id' => (string)$trans_array['id'],
                    'activity' => 'P',
                );
                // get all transaction related to orders_id and activity
                $scArrayList = self::getScAllTransactions($scDataRequest);

                foreach ($scArrayList as $scList) {
                    $used_sc = true;
                    $sc_spended_amount_array['SC'] += $scList['transaction_amount'];
                    $sc_spended_amount_array['currency_id'] = $currencies->get_id_by_code($scList['transaction_currency']);
                    $sc_spended_amount_array['currency_code'] = $scList['transaction_currency'];
                }

                $payment_amount_select_sql = "  SELECT value, text
                                                FROM " . TABLE_ORDERS_TOTAL . "
                                                WHERE orders_id='" . tep_db_input($trans_array['id']) . "'
                                                    AND class='ot_total'";
                $payment_amount_result_sql = tep_db_query($payment_amount_select_sql);
                $payment_amount_row = tep_db_fetch_array($payment_amount_result_sql);

                // if this order is having payment surcharge included, minus it out
                if ($ot_surcharge > 0) {
                    $payment_amount_row['value'] = $payment_amount_row['value'] - $ot_surcharge;
                    $trans_array['order_total_amount'] - $trans_array['order_total_amount'] - $ot_surcharge;
                }

                if ($payment_amount_row['value'] > 0) {
                    // exclude GST
                    $payment_amount_row['value'] = $payment_amount_row['value'] - $ot_gst;

                    $sc_products_amount_select_sql = "  SELECT SUM(final_price * products_quantity) AS sc_total_amount
                                                        FROM " . TABLE_ORDERS_PRODUCTS . "
                                                        WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                            AND custom_products_type_id = 3
                                                            AND orders_products_is_compensate = 0";
                    $sc_products_amount_result_sql = tep_db_query($sc_products_amount_select_sql);
                    $sc_products_amount_row = tep_db_fetch_array($sc_products_amount_result_sql);
                    if ($sc_products_amount_row['sc_total_amount'] > 0) {
                        $payment_amount_row['value'] = $payment_amount_row['value'] - number_format($sc_products_amount_row['sc_total_amount'], 2, '.', '');
                    }

                    // Refund to payment gateway
                    if (isset($trans_array['payment_refund']) && $trans_array['payment_refund'] === true) {
                        if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                            $sc_spended_amount_array[$sc_type . 'P'] = $payment_amount_row['value'];
                        } else {
                            $sc_spended_amount_array[$sc_type . 'P'] = ($payment_amount_row['value'] * $trans_array['currency_value']);
                        }
                    } else {
                        if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                            $sc_spended_amount_array['SC'] += $payment_amount_row['value'];
                        } else {
                            $sc_spended_amount_array['SC'] += ($payment_amount_row['value'] * $trans_array['currency_value']);
                        }
                    }
                }

                if ($sc_spended_amount_array['currency_id'] != $default_currency_id) {
                    $exch_rate = $trans_array['currency_value'];
                }
                $add_amount = number_format(($add_amount * $exch_rate), 4, '.', '');

                // when getting total previously refunded value, we need to exclude out store credit products from the order.
                // store credit products only refund to payment gateway.
                $latest_total_refunded_value_select_sql = " SELECT SUM(products_canceled_price) AS new_total_refund
                                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                                WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                                    AND custom_products_type_id <> 3
                                                                    AND orders_products_is_compensate = 0";
                $latest_total_refunded_value_result_sql = tep_db_query($latest_total_refunded_value_select_sql);
                $latest_total_refunded_value_row = tep_db_fetch_array($latest_total_refunded_value_result_sql);

                $latest_total_refunded_value_row['new_total_refund'] = number_format(($latest_total_refunded_value_row['new_total_refund'] * $exch_rate), 4, '.', '');
                $total_used_sc_amount = number_format($sc_spended_amount_array['SC'] + $sc_spended_amount_array['RP'] + $sc_spended_amount_array['NRP'], 4, '.', '');
                $total_aaccumulated_value = 0;

                $compensate_sc = (isset($trans_array['compensate_sc']) && $trans_array['compensate_sc'] === true) ? true : false;

                if (!$compensate_sc) {
                    if ($add_amount > $latest_total_refunded_value_row['new_total_refund']) {
                        $add_amount -= ($add_amount - $latest_total_refunded_value_row['new_total_refund']);
                    }
                }

                if (($latest_total_refunded_value_row['new_total_refund'] >= $add_amount) || $compensate_sc) {
                    $before_refund_total_value = $latest_total_refunded_value_row['new_total_refund'] - $add_amount;

                    if ($add_amount > 0) {
                        if ($compensate_sc) {
                            $sc_issue_amount = $add_amount;
                        } else {
                            if ($latest_total_refunded_value_row['new_total_refund'] > $total_used_sc_amount) {
                                $add_amount = $total_used_sc_amount - $before_refund_total_value;
                            }

                            $so_far_refunded_amount = 0;

                            if ($sc_spended_amount_array['RP'] > 0) {
                                $total_aaccumulated_value += $sc_spended_amount_array['RP'];

                                if ($before_refund_total_value < $total_aaccumulated_value) {
                                    if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value)) {
                                        $payment_refund_amount = $total_aaccumulated_value - $before_refund_total_value;
                                        $add_amount -= $payment_refund_amount;
                                        $so_far_refunded_amount += $payment_refund_amount;
                                    } else {
                                        $payment_refund_amount = $add_amount;
                                        $add_amount = 0;
                                        $so_far_refunded_amount += $payment_refund_amount;
                                    }
                                }
                            }

                            if ($add_amount > 0) {
                                $total_aaccumulated_value += $sc_spended_amount_array['SC'];

                                if ($before_refund_total_value < $total_aaccumulated_value) {
                                    if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount)) {
                                        $sc_issue_amount = $total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount;
                                        $add_amount -= $sc_issue_amount;
                                        $so_far_refunded_amount += $sc_issue_amount;
                                    } else {
                                        $sc_issue_amount = $add_amount;
                                        $add_amount = 0;
                                        $so_far_refunded_amount += $sc_issue_amount;
                                    }
                                }
                            }

                            if ($add_amount > 0) {
                                $total_aaccumulated_value += $sc_spended_amount_array['NRP'];

                                if ($sc_spended_amount_array['NRP'] > 0) {
                                    if ($before_refund_total_value < $total_aaccumulated_value) {
                                        if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount)) {
                                            $payment_refund_amount = $total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount;
                                            $add_amount -= $payment_refund_amount;
                                            $so_far_refunded_amount += $payment_refund_amount;
                                        } else {
                                            $payment_refund_amount = $add_amount;
                                            $add_amount = 0;
                                            $so_far_refunded_amount += $payment_refund_amount;
                                        }
                                    }
                                }
                            }
                        }

                        // Update live credit balance
                        if ($sc_issue_amount > 0) {
                            // compose email content
                            $trans_info_select_sql = "  SELECT o.date_purchased, o.currency, o.currency_value, c.customers_email_address
                                                        FROM " . TABLE_ORDERS . " AS o
                                                        INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                                            ON o.customers_id=c.customers_id
                                                        WHERE o.orders_id = '" . tep_db_input($trans_array['id']) . "'";
                            $trans_info_result_sql = tep_db_query($trans_info_select_sql);
                            $trans_info_row = tep_db_fetch_array($trans_info_result_sql);

                            $sc_notification_email_contents = "Order ID: %d\nOrder Date: %s\nOrder Amount: %s\nCustomer E-mail: %s\n\nStore Credit Transaction ID: SC%d\nIssue Amount: %s\n\nUpdate Date: %s\nUpdate IP: %s\nUpdate User: %s\n\nUpdate Comment:\n%s";

                            if ($pay_currency_id > 0 && $pay_currency_id != $sc_spended_amount_array['currency_id']) {
                                $sc_issue_currency_id = $pay_currency_id;
                                $sc_issue_amount = $currencies->apply_currency_exchange($sc_issue_amount, $currencies->get_code_by_id($pay_currency_id), $trans_array['currency_value']);
                                $sc_issue_total_amount = $currencies->apply_currency_exchange($total_aaccumulated_value, $currencies->get_code_by_id($pay_currency_id), $trans_array['currency_value']);
                            } else {
                                $sc_issue_currency_id = $sc_spended_amount_array['currency_id'];
                                $sc_issue_total_amount = $total_aaccumulated_value;
                            }

                            $sc_issue_currency = $currencies->get_code_by_id($sc_issue_currency_id);

                            // prepare data for Store Credit MicroService
                            $sc_balance_history_data_array = array(
                                'orders_id' => $trans_array['id'],
                                'reference_id' => $trans_array['reference_id'],
                                'customers_id' => $cid,
                                'customers_role' => 'customers',
                                'requesting_id' => $this->identity_email,
                                'requesting_role' => 'admin',
                                'amount' => $sc_issue_amount,
                                'total_amount' => ($compensate_sc) ? $sc_issue_amount : $sc_issue_total_amount,
                                'currency' => $sc_issue_currency,
                                'activity' => $act_type,
                                'message' => $admin_msg,
                                'show_customer' => '1',
                            );

                            // Add Store Credit
                            $insert_sc_trans = self::setAdvanceScTransaction($cid, $sc_balance_history_data_array, 'ADD_CREDIT');

                            if ($insert_sc_trans) {
                                $return_result[] = array('sc_type' => 'R', 'sc_trans_id' => $insert_sc_trans['transaction_id'], 'sc_amount' => (double) $insert_sc_trans['amount'], 'sc_currency' => $insert_sc_trans['currency']);

                                $sc_notification_email_contents = sprintf($sc_notification_email_contents, $trans_array['id'], $trans_info_row['date_purchased'], strip_tags($payment_amount_row['text']), $trans_info_row['customers_email_address'], $insert_sc_trans['transaction_id'], $currencies->format($insert_sc_trans['amount'], false, $insert_sc_trans['currency']), date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $this->identity_email . $display_admin_group, $admin_msg);

                                $email_to_array = tep_parse_email_string(ISSUE_STORE_CREDIT_NOTIFY_EMAIL);
                                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                    tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf('Issue Reversible Store Credit from Order #%d', $trans_array['id']))), $sc_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                }
                            } else {
                                $error = true;
                            }
                        }

                        if ($payment_refund_amount > 0) {
                            // if store credit is in used during order made, the refund amount will be converted to
                            // match the store credit currency. We need to convert it back to USD.
                            if ($used_sc) {
                                $non_sc_payment_refund_amount = round($payment_refund_amount * (1 / $trans_array['currency_value']), 6);
                                // need to have more than 2 decimal places for amount accuracy
                            } else {
                                $non_sc_payment_refund_amount = $payment_refund_amount;
                            }

                            if ($fullRefund == true) {
                                if ($refund_gst && ($non_sc_payment_refund_amount > 0)) {
                                    $refund_gst_amount = ($non_sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon)) * $ot_gst;
                                    $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $refund_gst_amount;
                                }
                                $refund_surcharge = true;
                                $refund_surcharge_amount = $ot_surcharge;
                                $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $ot_surcharge;
                            } else {
                                /* -- GST :: refund with GST (if any) -- */
                                if ($refund_gst && ($non_sc_payment_refund_amount > 0)) {
                                    $refund_gst_amount = ($non_sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon + $ot_surcharge)) * $ot_gst;
                                    $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $refund_gst_amount;
                                }
                            }
                        }
                    } else {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            }

            if ($add_sc_amount > 0) {
                $sc_spended_amount_array = array(
                    'SC' => 0,
                    'RP' => 0,
                    'NRP' => 0,
                    'currency_id' => $default_currency_id,
                    'currency_code' => DEFAULT_CURRENCY
                );

                if (isset($trans_array['pay_currency'])) {
                    $sc_spended_amount_array['currency_id'] = array_search($trans_array['pay_currency'], $currencies->internal_currencies);
                    $sc_spended_amount_array['currency_code'] = $trans_array['pay_currency'];
                }
                $add_amount = $add_sc_amount;
                $exch_rate = 1;

                $payment_amount_select_sql = "  SELECT value, text
                                                FROM " . TABLE_ORDERS_TOTAL . "
                                                WHERE orders_id='" . tep_db_input($trans_array['id']) . "'
                                                    AND class='ot_total'";
                $payment_amount_result_sql = tep_db_query($payment_amount_select_sql);
                $payment_amount_row = tep_db_fetch_array($payment_amount_result_sql);

                if ($payment_amount_row['value'] > 0) {
                    // Refund to payment gateway
                    if (isset($trans_array['payment_refund']) && $trans_array['payment_refund'] === true) {
                        if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                            $sc_spended_amount_array[$sc_type . 'P'] = $payment_amount_row['value'];
                        } else {
                            $sc_spended_amount_array[$sc_type . 'P'] = ($payment_amount_row['value'] * $trans_array['currency_value']);
                        }
                    } else {
                        if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                            $sc_spended_amount_array['SC'] += $payment_amount_row['value'];
                        } else {
                            $sc_spended_amount_array['SC'] += ($payment_amount_row['value'] * $trans_array['currency_value']);
                        }
                    }
                }

                if ($sc_spended_amount_array['currency_id'] != $default_currency_id) {
                    $exch_rate = $trans_array['currency_value'];
                }
                $add_amount = number_format(($add_amount * $exch_rate), 2, '.', '');

                $latest_total_refunded_value_select_sql = " SELECT SUM(products_canceled_price) AS new_total_refund
                                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                                WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                                        AND custom_products_type_id = 3
                                                                        AND orders_products_is_compensate = 0";
                $latest_total_refunded_value_result_sql = tep_db_query($latest_total_refunded_value_select_sql);
                $latest_total_refunded_value_row = tep_db_fetch_array($latest_total_refunded_value_result_sql);

                $latest_total_refunded_value_row['new_total_refund'] = number_format(($latest_total_refunded_value_row['new_total_refund'] * $exch_rate), 2, '.', '');
                $total_used_sc_amount = number_format($sc_spended_amount_array['RP'] + $sc_spended_amount_array['NRP'], 2, '.', '');
                $total_aaccumulated_value = 0;

                if ($add_amount > $latest_total_refunded_value_row['new_total_refund']) {
                    $add_amount -= ($add_amount - $latest_total_refunded_value_row['new_total_refund']);
                }

                if (($latest_total_refunded_value_row['new_total_refund'] >= $add_amount)) {
                    $before_refund_total_value = $latest_total_refunded_value_row['new_total_refund'] - $add_amount;

                    if ($add_amount > 0) {
                        if ($latest_total_refunded_value_row['new_total_refund'] > $total_used_sc_amount) {
                            $add_amount = $total_used_sc_amount - $before_refund_total_value;
                        }

                        if ($sc_spended_amount_array['RP'] > 0) {
                            $total_aaccumulated_value += $sc_spended_amount_array['RP'];
                            if ($before_refund_total_value < $total_aaccumulated_value) {
                                if ($add_amount > $sc_spended_amount_array['RP']) {
                                    $payment_refund_amount = $sc_spended_amount_array['RP'];
                                    $add_amount -= $sc_spended_amount_array['RP'];
                                } else {
                                    $payment_refund_amount = $add_amount;
                                    $add_amount = 0;
                                }
                            }
                        }

                        if ($add_amount > 0) {
                            $total_aaccumulated_value += $sc_spended_amount_array['NRP'];
                            if ($sc_spended_amount_array['NRP'] > 0) {
                                if ($before_refund_total_value < $total_aaccumulated_value) {
                                    if ($add_amount > $sc_spended_amount_array['NRP']) {
                                        $payment_refund_amount = $sc_spended_amount_array['NRP'];
                                        $add_amount -= $sc_spended_amount_array['NRP'];
                                    } else {
                                        $payment_refund_amount = $add_amount;
                                        $add_amount = 0;
                                    }
                                }
                            }
                        }

                        if ($payment_refund_amount > 0) {
                            // reverse the payment gateway refund amount back to USD by using order currency value
                            $sc_payment_refund_amount = round($payment_refund_amount * (1 / $trans_array['currency_value']), 6);

                            if ($fullRefund == true) {
                               if ($refund_gst && ($sc_payment_refund_amount > 0)) {
                                    $refund_gst_amount = ( $sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon) ) * $ot_gst;
                                    $sc_payment_refund_amount = $sc_payment_refund_amount + $refund_gst_amount;
                                }
                                $refund_surcharge = true;
                                $refund_surcharge_amount = $ot_surcharge;
                                $sc_payment_refund_amount = $sc_payment_refund_amount + $ot_surcharge;
                            } else {
                                /* -- GST :: refund with GST (if any) -- */
                                if ($refund_gst && ($sc_payment_refund_amount > 0)) {
                                    $refund_gst_amount = ($sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon + $ot_surcharge)) * $ot_gst;
                                    $sc_payment_refund_amount = $sc_payment_refund_amount + $refund_gst_amount;
                                }
                            }
                        }
                    } else {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            }

            // Combine both SC and non SC refund amount to crete only 1 refund transaction.
            if ($non_sc_payment_refund_amount > 0 || $sc_payment_refund_amount > 0) {
                $payment_refund_amount = $non_sc_payment_refund_amount + $sc_payment_refund_amount;
                /* ***********************************************************************
                  1. Check this user own that order
                 * *********************************************************************** */
                $order_checking_select_sql = "  SELECT orders_id
                                                FROM " . TABLE_ORDERS . "
                                                WHERE orders_id = '" . tep_db_prepare_input($trans_array['id']) . "'
                                                    AND customers_id = '" . tep_db_prepare_input($cid) . "'";
                $order_checking_result_sql = tep_db_query($order_checking_select_sql);

                if ($order_checking_row = tep_db_fetch_array($order_checking_result_sql)) {
                    $user_info_array = self::getUserParticulars($cid);

                    $refund_sql_data_array = array(
                        'user_id' => tep_db_prepare_input($cid),
                        'user_firstname' => $user_info_array['fname'],
                        'user_lastname' => $user_info_array['lname'],
                        'user_email_address' => $user_info_array['email'],
                        'store_refund_date' => 'now()',
                        'store_refund_trans_id' => tep_db_prepare_input($trans_array['id']),
                        'store_refund_status' => '1',
                        'store_refund_trans_total_amount' => (double)$trans_array['order_total_amount'],
                        'store_refund_amount' => (double)$payment_refund_amount,
                        'store_refund_payments_methods_name' => tep_db_prepare_input($trans_array['payment_method'])
                    );
                    tep_db_perform(TABLE_STORE_REFUND, $refund_sql_data_array);
                    $insert_refund_id = tep_db_insert_id();

                    if ($insert_refund_id > 0) {
                        /* -- GST :: record refund GST amount -- */
                        if (($refund_gst && ($refund_gst_amount > 0)) || ($refund_surcharge && ($refund_surcharge_amount > 0))) {
                            $refund_gst_amount_sql_data_array = array(
                                'store_refund_id' => $insert_refund_id,
                                'store_refund_gst_amount' => $refund_gst_amount,
                                'store_refund_surcharge_amount' => $refund_surcharge_amount,
                            );
                            tep_db_perform(TABLE_STORE_REFUND_INFO, $refund_gst_amount_sql_data_array);
                        }

                        // Insert refund history
                        $refund_history_sql_data_array = array(
                            'store_refund_id' => $insert_refund_id,
                            'store_refund_status' => '1',
                            'date_added' => 'now()',
                            'payee_notified' => '1',
                            'comments' => tep_db_prepare_input($admin_msg),
                            'changed_by' => $this->identity_email,
                            'changed_by_role' => 'admin'
                        );
                        tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);

                        $messageStack->add_session(SUCCESS_ORDER_PAYMENT_REFUNDED, 'success');

                        $return_result[] = array('sc_type' => $sc_type . 'P', 'sc_trans_id' => $insert_refund_id, 'sc_amount' => (double)$payment_refund_amount);
                    } else {
                        // Refund failed
                        $error = true;
                        $messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
                    }
                } else {
                    // Refund failed
                    $error = true;
                    $messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
                }
            }
        } else {
            $error = true;
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    public function setMiscAmount($input_array, $action_message, $action_desc, &$messageStack = array())
    {
        global $currencies;

        $return_result = array();
        $return_msg = array();
        $error = false;

        if (!isset($input_array['currency_id'])) {
            $input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        }

        $sc_amount = trim($input_array['amount']);
        if (is_numeric($sc_amount) && $sc_amount > 0) {
            // Update live credit balance
            $trans_array = array(
                'orders_id' => $input_array['id'],
                'reference_id' => $input_array['reference_id'],
                'customers_id' => $input_array['user_id'],
                'customers_role' => 'customers',
                'requesting_id' => $this->identity_email,
                'requesting_role' => 'admin',
                'amount' => floatval($sc_amount),
                'total_amount' => floatval(trim($input_array['total_amount'])),
                'currency' => $currencies->get_code_by_id($input_array['currency_id']),
                'activity' => $input_array['act_type'],
                'message' => $action_desc,
                'show_customer' => $input_array['show_desc'],
            );

            // Add Store Credit
            $sc_transaction = self::setAdvanceScTransaction($input_array['user_id'], $trans_array, $input_array['operation']);

            if ($sc_transaction) {
                $return_result = array('sc_trans_id' => $sc_transaction['transaction_id'], 'sc_amount' => (double) $sc_transaction['amount'], 'sc_currency' => $sc_transaction['currency']);
                $return_msg[] = SUCCESS_SC_STAT_SC_CREDITED;
            } else {
                $error = true;
                $return_msg[] = ($input_array['operation'] == 'ADD_CREDIT') ? ERROR_SC_STAT_INVALID_ADDITION_AMOUNT : ERROR_SC_STAT_INVALID_DEDUCTION_AMOUNT;
            }
        } else {
            $error = true;
            $return_msg[] = ($input_array['operation'] == 'ADD_CREDIT') ? ERROR_SC_STAT_INVALID_ADDITION_AMOUNT : ERROR_SC_STAT_INVALID_DEDUCTION_AMOUNT;
        }

        if (is_object($messageStack)) {
            $key = $error ? 'error' : 'success';

            foreach ($return_msg as $idx => $msg) {
                $messageStack->add_session($msg, $key);
            }
        } else {
            if ($error && is_array($messageStack)) {
                $messageStack = array_merge($messageStack, $return_msg);
            }
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    public function redeemOnStoreCredit($input_array)
    {
        global $currencies;

        $redeem_amount = trim($input_array['sc_redeem_amt']);

        if (is_numeric($redeem_amount) && $redeem_amount > 0) {
            $update_info = array(
                'orders_id' => (string)$input_array['sp_redeem_id'],
                'reference_id' => (string)$input_array['sp_redeem_id'],
                'customers_id' => $input_array['user_id'],
                'customers_role' => 'customers',
                'requesting_id' => $this->identity_email,
                'requesting_role' => 'admin',
                'amount' => floatval($redeem_amount),
                'total_amount' => floatval($redeem_amount),
                'currency' => $currencies->get_code_by_id($input_array['currency_id']),
                'activity' => 'D',
                'message' => $input_array['comments'],
                'show_customer' => $input_array['show_comments'],
            );

            $sc_transaction = self::setScTransaction($update_info, 'ADD_CREDIT');

            if ($sc_transaction['request_id']) {
                $sp_redeem_sql_data_array = array(
                    'store_points_redeem_status' => 3,
                    'store_points_paid_currency' => $sc_transaction['currency'],
                    'store_points_paid_currency_amount' => $sc_transaction['amount'],
                    'store_points_redeem_reference' => $sc_transaction['request_id'],
                    'store_points_redeem_last_modified' => 'now()'
                );

                tep_db_perform(TABLE_STORE_POINTS_REDEEM, $sp_redeem_sql_data_array, 'update', " store_points_redeem_id = '" . (int)$input_array['sp_redeem_id'] . "'");

                $sp_redeem_history_data_array = array(
                    'store_points_redeem_id' => $input_array['sp_redeem_id'],
                    'store_points_redeem_status' => 3,
                    'date_added' => 'now()',
                    'payee_notified' => 0,
                    'comments' => 'Store Credit Redeemed: ' . $currencies->format($sc_transaction['amount'], false, $sc_transaction['currency']) . "\n" . 'Reference id: SC-' . $sc_transaction['request_id'],
                    'changed_by' => $this->identity_email,
                    'changed_by_role' => 'admin'
                );

                tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sp_redeem_history_data_array);
            } else {
                return false;
            }
        } else {
            return false;
        }

        return true;
    }
}
