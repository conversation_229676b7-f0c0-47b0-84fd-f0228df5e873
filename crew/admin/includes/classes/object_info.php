<?php
/*
  $Id: object_info.php,v 1.5 2009/03/18 05:26:52 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/


class objectInfo {
// class constructor
	function objectInfo($object_array, $format=true) {
		if (is_array($object_array)) {
			reset($object_array);
		}
		
		while (list($key, $value) = each($object_array)) {
			if ($format) {
				$this->$key = tep_db_prepare_input($value);
			} else {
				$this->$key = $value;
			}
      	}
	}
}
?>
