<?php

class ogc_redeem_status {

    public function __construct() {
        
    }

    public function searchForm($serial = "") {
        $row = array();
        $gcArray = array();

        if ($serial) {
            $sel_sql = "SELECT customers_id, redeem_ip, redeem_date, transaction_id
                        FROM " . TABLE_GIFT_CARD_REDEMPTION . "
                        WHERE serial_number = '" . $serial . "'";
            $res_sql = tep_db_query($sel_sql);
            if (tep_db_num_rows($res_sql) > 0) {
                while ($row = tep_db_fetch_array($res_sql)) {
                    $gcArray[] = $row;
                }
            }
        }

        ob_start();
        ?>
        <script language="javascript">
            <!--
                                            function check_form() {
                var error = 0;
                var error_message = "Errors have occured during the process of your search!\nPlease make the following corrections:\n\n";

                if (document.menu_form.serial.value == "") {
                    error_message = error_message + "* 'Serial' entry must be entered.\n";
                    error = 1;
                }

                if (error == 1) {
                    alert(error_message);
                    return false;
                } else {
                    return true;
                }
            }
            //-->
        </script>

        <?= tep_draw_form('menu_form', FILENAME_OGC_REDEEM_STATUS, 'action=search', 'post', ' onSubmit="return check_form();" id="menu_form"') ?>
        <table cellspacing="2" cellpadding="2" border="0">
            <tr>
                <td>
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td class="pageHeading" valign="top">
                                <?php echo HEADING_TITLE; ?>
                            </td>
                            <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" cellpadding="2" cellspacing="2">
                        <tr>
                            <td class="main" valign="top"><?php echo ENTRY_SERIAL; ?></td>
                            <td class="main">
                                <?php echo tep_draw_input_field('serial', '', ' id="serial" size="40" maxlength="32" '); ?>
                                <span class="fieldRequired">* Required</span>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
            </tr>
            <tr>
                <td class="main" align="right">
                    <?php echo tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, '', 'inputButton'); ?>
                </td>
            </tr>
        </table>
        </form>
        <?php if ($serial) { ?>
            <table cellspacing="2" cellpadding="2" border="0">
                <tr>
                    <td><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                </tr>
                <tr>
                    <td>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <td class="main" valign="top" colspan="2"><b><?php echo HEADING_SEARCH_RESULT; ?></b></td>
                            </tr>
                            <?php if ($gcArray) {
                                foreach ($gcArray as $gcInfo) { ?>
                                <tr>
                                    <td colspan="2"><img src="images/pixel_trans.gif" alt="" border="0" height="10" width="1"></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><?php echo TEXT_CUSTOMER_ID; ?></td>
                                    <td class="main" valign="top">: <a href="<?php echo tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $gcInfo['customers_id'] . '&action=edit'); ?>" target="_blank"><?php echo $gcInfo['customers_id']; ?></a></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><?php echo TEXT_REDEEM_IP; ?></td>
                                    <td class="main" valign="top">: <?php echo $gcInfo['redeem_ip']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><?php echo TEXT_REDEEM_DATE; ?></td>
                                    <td class="main" valign="top">: <?php echo $gcInfo['redeem_date']; ?></td>
                                </tr>
                                <tr>
                                    <td class="main" valign="top"><?php echo TEXT_REDEEM_TRANS_ID; ?></td>
                                    <td class="main" valign="top">: <?php echo (!empty($gcInfo['transaction_id'])) ? $gcInfo['transaction_id'] : 'Not Availbale'; ?></td>
                                </tr>
                            <?php } 
                            } else { ?>
                                <tr>
                                    <td class="main" valign="top" colspan="2"><?php printf(ERROR_RECORD_NOT_FOUND, $serial); ?></td>
                                </tr>
                            <?php } ?>
                        </table>
                    </td>
                </tr>
            </table>
        <?php } ?>
        <?php
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

}
?>