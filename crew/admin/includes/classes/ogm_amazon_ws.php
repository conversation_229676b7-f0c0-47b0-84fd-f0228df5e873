<?php

require_once DIR_WS_INCLUDES . 'addon/amazonaws_sdk/sdk.class.php';

class ogm_amazon_ws {

    public $aws_bucket, $default_cache_control_max_age = '2592000', $aws_bucket_key;
    public $aws_filepath;
    public $aws_storage;
    public $aws_acl;
    public $aws_headers = array();
    public $aws_filename;
    public $aws_file, $aws_file_content, $aws_file_content_type;
    public $s3, $ses, $cloudfront;
    public static $aws_s3_enabled = 'false'; //AWS_S3_ENABLED; // Needed to transfer to AWS, true: yes
    public static $aws_ses_enabled = 'false'; // Needed to transfer to AWS, true: yes
    public static $buckets_array = array(
        'BUCKET_IMAGE' => array('bucket' => AWS_S3_BUCKET_IMAGE_NAME, 'domain' => AWS_S3_BUCKET_IMAGE_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'true'),
        'BUCKET_NEWSLETTER' => array('bucket' => AWS_S3_BUCKET_NEWSLETTER_NAME, 'domain' => AWS_S3_BUCKET_NEWSLETTER_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'false'),
        'BUCKET_SECURE' => array('bucket' => AWS_S3_BUCKET_SECURE_NAME, 'domain' => AWS_S3_BUCKET_SECURE_DOMAIN, 'enabled' => 'false', 'enabled_cf' => 'false'),
        'BUCKET_STATIC' => array('bucket' => AWS_S3_BUCKET_STATIC_NAME, 'domain' => AWS_S3_BUCKET_STATIC_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'true'),
        'BUCKET_UPLOAD' => array('bucket' => AWS_S3_BUCKET_UPLOAD_NAME, 'domain' => AWS_S3_BUCKET_UPLOAD_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'false'),
        'BUCKET_DATA' => array('bucket' => AWS_S3_BUCKET_DATA_NAME, 'domain' => AWS_S3_BUCKET_DATA_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'false'),
        'BUCKET_SHASSO_STATIC' => array('bucket' => AWS_S3_BUCKET_SHASSO_STATIC_NAME, 'domain' => AWS_S3_BUCKET_SHASSO_STATIC_DOMAIN, 'enabled' => 'true', 'enabled_cf' => 'true'),
    );
    private $proxy_mode = FALSE; // Need to change to false for beta and live server
    private $ses_send_quota_array = array();
    private $letter_to, $letter_from, $letter_source, $letter_destination = null, $letter_option = null, $letter_compiled_message = null, $letter_replyto = null;
    private $cf_distribution_list, $cf_distribution_id_array;

    public function __construct($config = null) {
        if (tep_not_null($config)) {
            // use external config setting
        } else {
            if (defined('AWS_S3_ENABLED') !== TRUE) {
                $configuration_sql = '  SELECT configuration_key as cfgKey, configuration_value as cfgValue
                                        FROM ' . TABLE_CONFIGURATION . '
                                        WHERE configuration_key IN ("AWS_S3_ENABLED", "AWS_SES_ENABLED", "AWS_S3_BUCKET_SECURE_ENABLED")';
                $configuration_query = tep_db_query($configuration_sql);
                while ($configuration = tep_db_fetch_array($configuration_query)) {
                    define($configuration['cfgKey'], $configuration['cfgValue']);
                }
            }

            $config = array(
                'aws_s3_enabled' => AWS_S3_ENABLED,
                'aws_ses_enabled' => AWS_SES_ENABLED,
                'aws_s3_bucket_secure_enabled' => AWS_S3_BUCKET_SECURE_ENABLED,
            );
        }

        $this->set_aws_api_config($config);
        $this->s3 = new AmazonS3();
        $this->ses = new AmazonSES();

        if ($this->proxy_mode) {
            $this->proxy_enabled();
        }
    }

    public function init_cloudfront() {
        $this->cloudfront = new AmazonCloudFront();
        if ($this->proxy_mode) {
            $this->cloudfront->set_proxy('proxy://my-proxy.offgamers.lan:3128');
        }
    }

    public function init_sqs($queueURL, $params = null) {
        $this->sqs = new AmazonSQS($params);
        $this->queueURL = $queueURL;
        if ($this->proxy_mode) {
            $this->sqs->set_proxy('proxy://my-proxy.offgamers.lan:3128');
        }
    }

    /*
     * 	Format: `proxy://user:pass@hostname:port`
     */

    private function proxy_enabled() {
        $this->s3->set_proxy('proxy://my-proxy.offgamers.lan:3128');
        $this->ses->set_proxy('proxy://my-proxy.offgamers.lan:3128');
    }

    private function load_memcache() {
        global $memcache_obj;

        if (!isset($memcache_obj)) {
            require_once(DIR_WS_CLASSES . 'cache_abstract.php');
            require_once(DIR_WS_CLASSES . 'memcache.php');
            $memcache_obj = new OGM_Cache_MemCache();
        }

        return $memcache_obj;
    }

// -------------------------------------------------------------------------------------------------------------------------------------------------- S3
    /*
     * 	acl - _string_ (Optional) [Allowed values: `AmazonS3::ACL_PRIVATE`, `AmazonS3::ACL_PUBLIC`, `AmazonS3::ACL_OPEN`, `AmazonS3::ACL_AUTH_READ`, `AmazonS3::ACL_OWNER_READ`, `AmazonS3::ACL_OWNER_FULL_CONTROL`]. The default value is <ACL_PRIVATE>
     */
    public function set_acl($acl) {
        $this->aws_acl = constant('AmazonS3::' . $acl);
    }

    public function set_headers($headers) {
        $this->aws_headers = $headers;
    }

    private function set_aws_api_config($config) {
        if (tep_not_null($config)) {
            // One shot initialize
            if ($config['aws_s3_enabled']) {
                self::$aws_s3_enabled = $config['aws_s3_enabled'];
            }

            if ($config['aws_ses_enabled']) {
                self::$aws_ses_enabled = $config['aws_ses_enabled'];
            }

            if ($config['aws_s3_bucket_secure_enabled']) {
                self::$buckets_array['BUCKET_SECURE']['enabled'] = $config['aws_s3_bucket_secure_enabled'];
            }
        }
    }

    public function set_bucket_key($bucket_key) {
        if (tep_not_null($bucket_key)) {
            $this->aws_bucket_key = $bucket_key;
            $this->aws_bucket = self::$buckets_array[$bucket_key]['bucket'];
        }
    }

    public function set_file($file) {
        $this->aws_file = $file;
    }

    public function set_file_content($file_content) {
        $this->aws_file_content = $file_content;
    }

    /*
     * 	contentType - _string_ (Optional)
     */

    public function set_file_content_type($file_content_type = null) {
        if (tep_not_null($file_content_type)) {
            $this->aws_file_content_type = $file_content_type;
        } else if (tep_not_null($this->aws_filename)) {
            $extension = explode('.', $this->aws_filename);
            $extension = array_pop($extension);
            $mime_type = CFMimeTypes::get_mimetype($extension);
            $this->aws_file_content_type = $mime_type;
        }
    }

    public function set_filename($filename) {
        if (!is_null($filename)) {
            $this->aws_filename = $filename;
        }
    }

    public function set_filepath($filepath) {
        if (tep_not_null($filepath)) {
            $this->aws_filepath = $filepath;
        }
    }

    /*
     * 	storage - _string_ (Optional) [Allowed values: `AmazonS3::STORAGE_STANDARD`, `AmazonS3::STORAGE_REDUCED`]. The default value is <STORAGE_STANDARD>.
     */

    public function set_storage($storage) {
        if ($storage == 'STORAGE_STANDARD') {
            $storage = 'STORAGE_STANDARD';
        }
        $this->aws_storage = constant('AmazonS3::' . $storage);
    }

    private function get_aws_bucket($bucket_key = null) {
        $this->set_bucket_key($bucket_key);
        return $this->aws_bucket;
    }

    private function get_aws_filename($filename = null, $filepath = null) {
        $this->set_filename($filename);
        $this->set_filepath($filepath);
        return $this->aws_filepath . $this->aws_filename;
    }

    public static function is_aws_s3_enabled() {
        return strtolower(self::$aws_s3_enabled) == 'true';
    }

    public function is_s3_bucket_config_enabled($bucket_key = null, $enabled_type = 'enabled') {
        $this->set_bucket_key($bucket_key);
        $bucket_key = $this->aws_bucket_key;

        if (tep_not_null($bucket_key) && isset(self::$buckets_array[$bucket_key][$enabled_type])) {
            return self::$buckets_array[$bucket_key][$enabled_type] == 'true';
        }

        return false;
    }

    public function get_image_info($pro_img) {
        $return_array = array();

        // Force all $pro_img == null to return as empty string, it helps to overwrite the $this->aws_filename.
        // This will prevent it from using last define filename to get the current image exist status when current filename is null.
        if (is_null($pro_img)) {
            $pro_img = '';
        }

        if (self::is_aws_s3_enabled()) {
            $this->set_filename($pro_img);

            if ($this->is_image_exists()) {
                $return_array['src'] = $this->get_image_url_by_instance();
            }
        }

        return $return_array;
    }

    public function get_image_url_by_instance($default = '') {
        return $this->get_image_url($this->aws_filename, $this->aws_filepath, $this->aws_bucket_key, $default);
    }

    public function get_image_url($filename, $filepath, $bucket_key, $default = '') {
        $return_string = '';

        if (self::is_aws_s3_enabled()) {
            $domain = self::$buckets_array[$bucket_key]['domain'];
            if (!tep_not_null($domain)) {
                $domain = self::$buckets_array[$bucket_key]['bucket'] . ".s3.amazonaws.com/";
            }
            $return_string = "http://" . $domain . $filepath . $filename;
        } else {
            $return_string = $default;
        }

        return $return_string;
    }

    private function get_opts_array() {
        $return_array = array();

        if (isset($this->aws_file['tmp_name'])) {
            $return_array['fileUpload'] = $this->aws_file['tmp_name'];
        } else if (tep_not_null($this->aws_file_content)) {
            $return_array['body'] = $this->aws_file_content;
        }

        if (tep_not_null($this->aws_storage)) {
            $return_array['storage'] = $this->aws_storage;
        }

        if (tep_not_null($this->aws_acl)) {
            $return_array['acl'] = $this->aws_acl;
        }

        if (tep_not_null($this->aws_file_content_type)) {
            $return_array['contentType'] = $this->aws_file_content_type;
        }

        if (tep_not_null($this->aws_headers)) {
            $return_array['headers'] = $this->aws_headers;
        }

        return $return_array;
    }

    public function is_image_exists($filename = null, $filepath = null, $bucket = null, $cache = true) {
        $memcache_obj = $this->load_memcache();
        $return_status = false;

        if (self::is_aws_s3_enabled()) {
            $bucket = $this->get_aws_bucket($bucket);
            $file_name = $this->get_aws_filename($filename, $filepath);

            if (tep_not_null($file_name)) {
                $cache_key = $bucket . '/' . $file_name . '/boolean';
                $cache_result = $memcache_obj->fetch($cache_key);

                if ($cache_result !== FALSE) {
                    $return_status = $cache_result;
                } else {
                    try {
                        if ($this->s3->if_object_exists($bucket, $file_name) === true) {
                            $return_status = true;
                        }
                    } catch (Exception $e) {
                        $this->report_error("Method [is_image_exists] : " . $e->getMessage());
                    }

                    if ($cache) {
                        $memcache_obj->store($cache_key, $return_status, 2592000); // Cache for 30 days
                    }
                }

                unset($cache_key, $cache_result);
            }

            unset($bucket, $file_name);
        }

        return $return_status;
    }

    public function copy_file($source_arr, $dest_arr) {
        $return_status = false;

        if (self::is_aws_s3_enabled()) {
            if (isset($source_arr['bucket']) && in_array($source_arr['bucket'], array_keys(self::$buckets_array))) {
                $source_arr['bucket'] = self::$buckets_array[$source_arr['bucket']]['bucket'];
            }

            if (isset($dest_arr['bucket']) && in_array($dest_arr['bucket'], array_keys(self::$buckets_array))) {
                $dest_arr['bucket'] = self::$buckets_array[$dest_arr['bucket']]['bucket'];
            }

            try {
                $result = $this->s3->copy_object($source_arr, $dest_arr);
                $return_status = (int) $result->status;
                unset($result);

                if ($return_status >= 300) {
                    $this->report_error("Method [copy_file][" . $return_status . "] :<br>Source : " . http_build_query($source_arr, null, '&') . "<br>Dest : " . http_build_query($dest_arr, null, '&'));
                }
            } catch (Exception $e) {
                $this->report_error("Method [copy_file] : " . $e->getMessage());
            }
        }

        return $return_status;
    }

    public function get_file($filename = null, $filepath = null, $bucket = null, $cache = false) { // Get Object using API
        $memcache_obj = $this->load_memcache();
        $return_string = '';

        if (self::is_aws_s3_enabled()) {
            $bucket = $this->get_aws_bucket($bucket);
            $file_name = $this->get_aws_filename($filename, $filepath);
            $opt_array = $this->get_opts_array();

            if (tep_not_null($file_name)) {
                $cache_key = $bucket . '/' . $file_name;
                $cache_result = $memcache_obj->fetch($cache_key);

                if ($cache_result !== FALSE) {
                    $return_status = $cache_result;
                } else {
                    try {
                        $return_string = $this->s3->get_object($bucket, $file_name, $opt_array);
                    } catch (Exception $e) {
                        $this->report_error("Method [get_file][" . $this->aws_bucket_key . "] : " . $e->getMessage());
                    }

                    if ($cache) {
                        $memcache_obj->store($cache_key, $return_string, $cache);
                    }
                }

                unset($opt_array, $cache_key, $cache_result);
            }

            unset($bucket, $file_name);
        }

        return $return_string;
    }

    public function get_object_url($bucket = null, $file_name = null, $preauth = 0, $opt_array = array()) { // Get Object using API
        $return_string = '';

        if (self::is_aws_s3_enabled()) {
            if (tep_not_null($file_name)) {
                try {
                    $return_string = $this->s3->get_object_url($bucket, $file_name, $preauth, $opt_array);
                } catch (Exception $e) {
                    $this->report_error("Method [get_object_url][" . $this->aws_bucket_key . "] : " . $e->getMessage());
                }
            }

            unset($bucket, $file_name);
        }

        return $return_string;
    }

    public function save_file() { // Move the file to Amazon S3 by API
        $return_status = false;

        if (self::is_aws_s3_enabled() && $this->is_s3_bucket_config_enabled()) {
            $this->set_file_content_type();

            $bucket = $this->get_aws_bucket();
            $file_name = $this->get_aws_filename();
            $opt_array = $this->get_opts_array();

            try {
                $result = $this->s3->create_object($bucket, $file_name, $opt_array);
                $return_status = (int) $result->status;

                if ($return_status >= 300) {
                    $this->report_error("Method [save_file][" . $this->aws_bucket_key . "][" . $return_status . "] :<br>Return Status:" . var_export($result, true));
                    $return_status = false;
                } else {
                    $this->capture_changed_object();
                    $this->clear_memcache();
                    $return_status = true;
                }

                unset($result);
            } catch (Exception $e) {
                $this->report_error("Method [save_file] : " . $e->getMessage());
            }

            unset($bucket, $file_name, $opt_array);
        }

        return $return_status;
    }

    public function move_file($source_arr, $dest_arr) {
        $memcache_obj = $this->load_memcache();
        $return_status = false;

        if (self::is_aws_s3_enabled()) {
            if (isset($source_arr['bucket']) && in_array($source_arr['bucket'], array_keys(self::$buckets_array))) {
                $source_arr['bucket'] = self::$buckets_array[$source_arr['bucket']]['bucket'];
                $this->clear_memcache($source_arr['filename'], '', $source_arr['bucket']);
            }

            if (isset($dest_arr['bucket']) && in_array($dest_arr['bucket'], array_keys(self::$buckets_array))) {
                $dest_arr['bucket'] = self::$buckets_array[$dest_arr['bucket']]['bucket'];
            }

            try {
                $result = $this->s3->copy_object($source_arr, $dest_arr);
                $return_status = (int) $result->status;
                unset($result);

                if ($return_status >= 300) {
                    $this->report_error("Method [move_file_1][" . $return_status . "] :<br>Source : " . http_build_query($source_arr, null, '&') . "<br>Dest : " . http_build_query($dest_arr, null, '&'));
                } else {
                    $result = $this->s3->delete_object($source_arr['bucket'], $source_arr['filename']);
                    $return_status = (int) $result->status;
                    unset($result);

                    if ($return_status >= 300) {
                        $this->report_error("Method [move_file_2][" . $return_status . "] :<br>Source : " . http_build_query($source_arr, null, '&'));
                    }
                }
            } catch (Exception $e) {
                $this->report_error("Method [move_file] : " . $e->getMessage());
            }
        }

        return $return_status;
    }

    public function delete_file($filename = null, $filepath = null, $bucket = null) {
        $memcache_obj = $this->load_memcache();
        $return_status = false;

        if (self::is_aws_s3_enabled()) {
            $bucket = $this->get_aws_bucket($bucket);
            $file_name = $this->get_aws_filename($filename, $filepath);

            try {
                $result = $this->s3->delete_object($bucket, $file_name);
                $this->clear_memcache();
                $return_status = (int) $result->status;

                if ($return_status >= 300) {
                    $this->report_error("Method [delete_file][" . $return_status . "] :<br>Source : " . $bucket . $file_name);
                }
            } catch (Exception $e) {
                $this->report_error("Method [delete_file] : " . $e->getMessage());
            }

            unset($bucket, $file_name, $result);
        }

        return $return_status;
    }

    public function s3_api(/* polymorphic */) {
        $args = func_get_args();
        $result_return = '';

        if (self::is_aws_s3_enabled()) {
            if (isset($args[0][0])) {
                if (in_array($args[0][0], array_keys(self::$buckets_array))) {
                    $args[0][0] = self::$buckets_array[$args[0][0]]['bucket'];
                }
            }

            if (isset($args[0]['method']) && method_exists($this->s3, $args[0]['method'])) {
                $method = $args[0]['method'];
                unset($args[0]['method']);

                try {
                    $result_return = call_user_func_array(array($this->s3, $method), $args[0]);
                    if ((int) $result_return->status >= 300) {
                        $this->report_error("Method [s3_api][" . $result_return->status . "] :<br>Args : " . http_build_query($args[0], null, '&'));
                    }
                } catch (Exception $e) {
                    $this->report_error("Method [s3_api] : " . $e->getMessage());
                }
            }
        }

        return $result_return;
    }

    private function clear_memcache($filename = null, $filepath = null, $bucket = null) {
        $memcache_obj = $this->load_memcache();

        $bucket = $this->get_aws_bucket($bucket);
        $file_name = $this->get_aws_filename($filename, $filepath);

        $memcache_obj->delete($bucket . '/' . $file_name, 0);
        $memcache_obj->delete($bucket . '/' . $file_name . '/boolean', 0);

        unset($bucket, $file_name);
    }

// ------------------------------------------------------------------------------------------------------------------------------------------------- SQS

    public function sendMessage($message_body = '', $opt = null) {
        $message_body = (is_array($message_body) ? json_encode($message_body) : $message_body);
        $this->sqs->send_message($this->queueURL, $message_body, $opt);
    }

// ------------------------------------------------------------------------------------------------------------------------------------------------- cloudfront
    public function get_distribution_id_by_bucket($bucket_key) {
        $domain = self::$buckets_array[$bucket_key]['bucket'];

        if (!isset($this->cf_distribution_id_array[$bucket_key])) {
            $d_list_arr = $this->get_distribution_list();

            foreach ($d_list_arr as $cf_obj) {
                if (isset($cf_obj->S3Origin)) {
                    $s3_arr = (array) $cf_obj->S3Origin;
                    $s3_domain = $s3_arr['DNSName'];

                    if (stristr($s3_domain, $domain) !== FALSE) {
                        $this->cf_distribution_id_array[$bucket_key] = (string) $cf_obj->Id;
                        break;
                    }
                }
            }

            unset($d_list_arr);
        }

        return isset($this->cf_distribution_id_array[$bucket_key]) ? $this->cf_distribution_id_array[$bucket_key] : '';
    }

    public function get_distribution_list() {

        if (empty($this->cf_distribution_list)) {
            $xml_resp = $this->cloudfront->list_distributions(); //array('MaxItems' => 1)

            if (isset($xml_resp->body->DistributionSummary)) {
                $temp = (array) $xml_resp->body;
                $resp_arr = $temp['DistributionSummary'];

                if (is_array($resp_arr)) {
                    $this->cf_distribution_list = $resp_arr;
                } else {
                    $this->cf_distribution_list[0] = $resp_arr;
                }

                unset($temp, $resp_arr);
            }
        }

        return $this->cf_distribution_list;
    }

    public function list_invalidations($distribution_id) {
        return $this->cloudfront->list_invalidations($distribution_id);
    }

    public function clear_cloudfront_cache($distribution_id, $paths_array) {
        $return_status = true;
        $caller_reference = 'cf_' . time();

        try {
            $result = $this->cloudfront->create_invalidation($distribution_id, $caller_reference, $paths_array);
            $return_status = (int) $result->status;

            if ($return_status >= 300) {
                $return_status = false;
                $this->report_error("Method [clear_cloudfront_cache][" . $return_status . "] :<br>distribution_id : " . $distribution_id . '<br>result:' . http_build_query($result, null, '&'));
            }
        } catch (Exception $e) {
            $return_status = false;
            $this->report_error("Method [clear_cloudfront_cache] : " . $e->getMessage());
        }

        return $return_status;
    }

    private function capture_changed_object() {
        if ($this->is_s3_bucket_config_enabled($this->aws_bucket_key, 'enabled_cf')) {
            $check_sql = "	SELECT cf_id
                            FROM " . TABLE_CRON_AWS_CF . "
                            WHERE bucket_key = '" . $this->aws_bucket_key . "'
                                AND filepath = '" . $this->aws_filepath . "'
                                AND filename = '" . $this->aws_filename . "'";
            $check_result = tep_db_query($check_sql);
            if (!tep_db_num_rows($check_result)) {
                $record_arr = array(
                    'bucket_key' => $this->aws_bucket_key,
                    'filepath' => $this->aws_filepath,
                    'filename' => $this->aws_filename,
                    'created_datetime' => 'now()',
                    'status' => 'P'
                );
                tep_db_perform(TABLE_CRON_AWS_CF, $record_arr);
            }
        }
    }

// ------------------------------------------------------------------------------------------------------------------------------------------------- SES
    /*
     * $letter['envelope']['to'] : (Required)
     * $letter['envelope']['to']['name'] : (Required)
     * $letter['envelope']['from'] : (Required)
     * $letter['envelope']['from']['name'] : (Required)
     * $letter['envelope']['cc'] : (Optional)
     * $letter['envelope']['bcc'] : (Optional)
     * $letter['envelope']['replyto'] : (Optional)
     * $letter['message']['subject'] : (Required)
     * $letter['message']['body'] : (Required)
     */
    public function set_ses_letter_info($letter) {
        if (tep_not_null($letter)) {
            if (EMAIL_LINEFEED == 'CRLF') {
                $lf = "\r\n";
            } else {
                $lf = "\n";
            }

            $this->letter_from = $letter['envelope']['from']['address'];
            $this->letter_to = $letter['envelope']['to']['address'];
            $this->letter_source = (tep_not_null($letter['envelope']['from']['name']) ? '"' . $letter['envelope']['from']['name'] . '" <' . $this->letter_from . '>' : $this->letter_from);

            $this->letter_destination = array();
            $this->letter_destination['ToAddresses'] = array((tep_not_null($letter['envelope']['to']['name']) ? '"' . utf8_encode($letter['envelope']['to']['name']) . '" <' . $this->letter_to . '>' : $this->letter_to));

            if (isset($letter['envelope']['cc']) && tep_not_null($letter['envelope']['cc'])) {
                $this->letter_destination['CcAddresses'] = array($letter['envelope']['cc']);
            }

            if (isset($letter['envelope']['bcc']) && tep_not_null($letter['envelope']['bcc'])) {
                $this->letter_destination['BccAddresses'] = array($letter['envelope']['bcc']);
            }

            if (isset($letter['envelope']['replyto'])) {
                $this->set_ses_reply_to_email($letter['envelope']['replyto']);
            } else {
                $this->set_ses_reply_to_email($this->letter_from);
            }

            $this->letter_option = array('ReplyToAddresses' => $this->letter_replyto);

            $this->letter_compiled_message = array();
            $this->letter_compiled_message['Subject']['Data'] = $letter['message']['subject'];
            //$this->letter_compiled_message['Body.Text.Data'] = tep_convert_linefeeds(array("\r\n", "\n", "\r"), $lf, $letter['message']['body']);
            $this->letter_compiled_message['Body']['Html']['Data'] = tep_convert_linefeeds(array("\r\n", "\n", "\r"), '<br>', $letter['message']['body']);
        }
    }

    public function set_ses_reply_to_email($replyto_address) {
        if (tep_not_null($replyto_address)) {
            $this->letter_replyto = $replyto_address;
        }
    }

    public function set_ses_status_locked() {
        $memcache_obj = $this->load_memcache();
        $mm_status = $this->send_mail_mc_locked_status();

        if ($mm_status['status'] != 'LOCKED') {
            $get_remaining_time = 3600; //(int)$mm_status['expired_ts'] - time();
//			if ($get_remaining_time > 0) {
            $mc_array = array('expired_ts' => $mm_status['expired_ts'], 'duration' => $get_remaining_time, 'status' => 'LOCKED');
            $memcache_obj->store($mm_status['key'], $mc_array, $get_remaining_time);
            $this->report_error("Status [LOCKED][DURATION] : " . $get_remaining_time);
            unset($mc_array);
//			}
        } else {
            // Memcache has no record or has been locked.
        }

        unset($mm_status);
    }

    public function get_ses_send_quota() {
        $return_array = array();

        if (tep_not_null($this->ses_send_quota_array)) {
            $return_array = $this->ses_send_quota_array;
        } else {
            $api_return = array();

            try {
                $api_return = $this->ses->get_send_quota();
                $api_return = (array) $api_return->body->GetSendQuotaResult;
            } catch (Exception $e) {
                $this->report_error("Method [get_ses_send_quota] : " . $e->getMessage());
            }

            if (tep_not_null($api_return)) {
                $return_array['max'] = (int) $api_return['Max24HourSend'];
                $return_array['sent'] = (int) $api_return['SentLast24Hours'];
                $return_array['rate'] = (int) $api_return['MaxSendRate']; // How many email sent per second
                $this->ses_send_quota_array = $return_array;
            }

            unset($api_return);
        }

        return $return_array;
    }

    /*
     * 	OUTPUT
     * return 0  : Checked SES and Found Sent records but cannot get last 24 first attempt timestamp
     * return -1 : Checked SES and Found NO send records
     * return >0 : Checked SES and Found Sent records and Got the last 24 first attempt timestamp
     */

    public function get_timestamp_for_first24_attempts() {
        $data_array = array();
        $return_int = 0;

        try {
            $statistics_obj = $this->ses->get_send_statistics();
            $data_array = (array) $statistics_obj->body->GetSendStatisticsResult->SendDataPoints;
        } catch (Exception $e) {
            $this->report_error("Method [get_timestamp_for_first24_attempts] : " . $e->getMessage());
        }

        if (isset($data_array['member'])) {
            $sent_quota = $this->get_ses_send_quota();

            if (isset($sent_quota['sent'])) { // When sent quota has value
                $ttl_sent = $sent_quota['sent'];

                if ($ttl_sent > 0) {
                    $stt_array = array();
                    $estimate_range = time() - 86400; // Estimate Passed 24 hours from now

                    foreach ($data_array['member'] as $counter => $rdata) {
                        $arr_key = strtotime($rdata->Timestamp);
                        if ($arr_key > $estimate_range) {
                            $stt_array[$arr_key]['da'] = (int) $rdata->DeliveryAttempts;
                        }
                    }

                    krsort($stt_array);

                    foreach ($stt_array as $ts => $stt) {
                        $ttl_sent -= $stt['da'];
                        if ($ttl_sent <= 0) {
                            $return_int = (int) $ts;
                            break;
                        }
                    }

                    unset($stt_array);
                } else {
                    $return_int = -1;
                }
            }
            unset($sent_quota);
        }

        unset($statistics_obj, $data_array);
        return $return_int;
    }

    public function get_expired_timestamp() {
        $return_int = 0;
        $last_attempts_ts = $this->get_timestamp_for_first24_attempts();

        switch ($last_attempts_ts) {
            case 0 :
                // Check records again after 1 hour
                $expired_timestamp = time() + 3600; // +1hour interval = 3600 seconds
                break;
            case -1 :
                $expired_timestamp = time() + 86400; // +24hours = 86400 seconds
                break;
            default :
                $expired_timestamp = $last_attempts_ts + 86400; // +24hours = 86400 seconds
                break;
        }

        if ($expired_timestamp > time()) {
            $return_int = (int) $expired_timestamp;
        }

        return $return_int;
    }

    public static function is_aws_ses_enabled() {
        return strtolower(self::$aws_ses_enabled) == 'true';
    }

    private function is_filtered_allow_subject() {
        $return_bool = true;
        $exclude_fromaddress_pattern_array = array('<EMAIL>');
        $exclude_toaddress_pattern_array = array('<EMAIL>');
        $exclude_toaddress_domain_pattern_array = array();
        $exclude_subject_pattern_array = array('[cronjob]', 'DEBUG E-MAIL', ' Before Process E-MAIL', 'Top-up Report', 'Top-up Notification', 'Direct Top-up',
            'Paid amount less than total need to pay',
            'Bibit Payment Method Match Failed',
            'SC search empty query',
            '[URGENT] checkout amount not tally with order products',
            'Turn off publisher due to data mismatched',
            'MayBank Before Process curl E-MAIL',
            'Cron update order status',
            'KuaiQian CallBack',
            'KuaiQian Process',
            'KuaiQian Before Process3',
            'MayBank CallBack E-MAIL',
            'Mobile Money DEBUG',
            'Missing private/public key',
            'Admin Account Updated',
            'New Admin Member',
            'Manual Stock Addition Notification',
            'Manual Stock Deduction Notification',
            'Discount Coupons Request Approved',
            'Discount Coupons Request Cancelled',
            'Inactive Admin Members',
            'Low Stock Warning:',
            'Cancellation of Redeemed Gift Voucher',
            'Receiving Payment Method Changes Notification',
            'Low Stock Warning:');

        if (tep_not_null($this->letter_to)) {
            $return_bool = $this->check_not_bounce_address(trim($this->letter_to));
        } else {
            $return_bool = false; // Just in case ToAddress is missing
        }

//		if ($return_bool && tep_not_null($_SERVER['REQUEST_URI'])) {
//			if (stripos($_SERVER['REQUEST_URI'], '/supplier_xmlhttp.php') !== FALSE) {	// case-insensitive
//				$return_bool = false;
//			}
//		}

        if ($return_bool) {
            if (tep_not_null($this->letter_from)) {
                if (in_array(trim($this->letter_from), $exclude_fromaddress_pattern_array)) { // case-insensitive
                    $return_bool = false;
                }
            } else {
                $return_bool = false; // Just in case FromAddress is missing
            }
        }

        if ($return_bool) {
            if (in_array(trim($this->letter_to), $exclude_toaddress_pattern_array)) { // case-insensitive
                $return_bool = false;
            } else if (in_array(trim(substr(strrchr($this->letter_to, "@"), 1)), $exclude_toaddress_domain_pattern_array)) { // case-insensitive
                $return_bool = false;
            }
        }

        if ($return_bool && isset($this->letter_compiled_message['Subject']['Data'])) {
            foreach ($exclude_subject_pattern_array as $str) {
                if (stripos($this->letter_compiled_message['Subject']['Data'], $str) !== FALSE) { // case-insensitive
                    $return_bool = false;
                    break;
                }
            }
        }

        unset($exclude_toaddress_pattern_array, $exclude_fromaddress_pattern_array, $exclude_subject_pattern_array);

        return $return_bool;
    }

    public function send_mail_by_ses($letter = null) {
        $return_bool = false;

        if (self::is_aws_ses_enabled()) {
            $mm_status = $this->send_mail_mc_locked_status();

            if ($mm_status['status'] != 'LOCKED') { // Allow to send if status is ''
                $this->set_ses_letter_info($letter);

                try {
                    $return_obj = $this->ses->send_email($this->letter_source, $this->letter_destination, $this->letter_compiled_message, $this->letter_option);

                    if ($return_obj->status != 200) {
                        if (isset($return_obj->body->Error->Code)) {
                            if (strtolower($return_obj->body->Error->Code) == 'throttling') {
                                $this->set_ses_status_locked();
                                $this->report_error("STATUS[FAILED:THROTTLING] : " . http_build_query((array) $return_obj->body->Error, null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . htmlspecialchars((string) $this->letter_destination['ToAddresses']));
                            } else if (strtolower($return_obj->body->Error->Message) == 'address blacklisted') {
                                $this->log_send_failed(http_build_query((array) $return_obj->body->Error, null, '&'));
                            } else {
                                $this->log_send_failed(http_build_query((array) $return_obj->body->Error, null, '&'));
                                $this->report_error("STATUS[FAILED:KNOWN CODE] : " . ' <br> ' . http_build_query((array) $return_obj->body->Error, null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . (is_array($this->letter_destination['ToAddresses']) ? http_build_query($this->letter_destination['ToAddresses']) : htmlspecialchars((string) $this->letter_destination['ToAddresses'])));
                            }
                        } else {
                            $this->report_error("STATUS[FAILED:UNKNOWN] : " . ' <br> ' . http_build_query((array) $return_obj->body->Error, null, '&') . '<br>Source : ' . htmlspecialchars((string) $this->letter_source) . '<br>To : ' . (is_array($this->letter_destination['ToAddresses']) ? http_build_query($this->letter_destination['ToAddresses']) : htmlspecialchars((string) $this->letter_destination['ToAddresses'])));
                        }
                    } else {
                        $return_bool = true;
                    }

                    unset($return_obj);
                } catch (Exception $e) {
                    $this->report_error("STATUS[FAILED] : " . $e->getMessage());
                }
            }

            unset($mm_status);
        }

        return $return_bool;
    }

    public function send_mail_by_ses_controller($letter = null) {
        $memcache_obj = $this->load_memcache();
        $return_bool = false;

        if (self::is_aws_ses_enabled()) {
            $this->set_ses_letter_info($letter);

            if ($this->is_filtered_allow_subject()) {
                $mm_status = $this->send_mail_mc_locked_status();

                if (tep_not_null($mm_status['status'])) {
                    if ($mm_status['status'] != 'LOCKED') { // Status return 'LOCKED'
                        $return_bool = true;
                    }
                } else {
//					$get_expired_ts = $this->get_expired_timestamp();
//
//					if ($get_expired_ts > 0) {
//						$memcache_duration = $get_expired_ts - time();
//						$mc_array = array('expired_ts' => $get_expired_ts, 'duration' => $memcache_duration, 'status' => 'UNLOCKED');
//						$memcache_obj->store($mm_status['key'], $mc_array, $memcache_duration);
//						$this->report_error ("Status[UNLOCKED][DURATION] : " . $memcache_duration);
//						unset($mc_array);
//					}

                    $return_bool = true;
                }

                unset($mm_status);
            }
        }

        return $return_bool;
    }

    /*
     * 	OUTPUT
     * status : ''         : Memcache has no record
     * status : 'LOCKED'   : Memcache has recorded Locked when received ThrottlingException from SES response
     * status : 'UNLOCKED' : Memcache has recorded Unlocked when newly created.
     */

    private function send_mail_mc_locked_status() {
        $memcache_obj = $this->load_memcache();
        $return_array = array();
        $cache_key = 'ogm_amazon_ws/send_mail_controller/array';
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $return_array['status'] = '';
        }

        $return_array['key'] = $cache_key;

        return $return_array;
    }

    public function verify_email_address($email_address) {
        try {
            $this->ses->verify_email_address($email_address);
            $this->report_error("Method [verify_email_address][DONE] : " . $email_address);
        } catch (Exception $e) {
            $this->report_error("Method [verify_email_address] : " . $e->getMessage());
        }
    }

    public function check_not_bounce_address($email_address) {
        $return_bool = true;

        $str = explode(" ", $email_address);
        $email_address = trim(array_pop($str), "<>");

        $check_exist_sql = "SELECT email
                            FROM " . TABLE_LOG_SES_BOUNCE . "
                            WHERE email = '" . $email_address . "'";
        $check_exist_result_sql = tep_db_query($check_exist_sql);
        if (tep_db_num_rows($check_exist_result_sql)) {
            $return_bool = false;
        }

        return $return_bool;
    }

    private function log_send_failed($error_message) {
        $error_string = 'Error MSG : ' . $error_message .
                '<br>Subject : ' . htmlspecialchars($this->letter_compiled_message['Subject']['Data']) .
                '<br>Request : ' . getenv('REQUEST_URI');

        if ($this->check_not_bounce_address($this->letter_to)) {
            $str = explode(" ", $this->letter_to);
            $email_address = trim(array_pop($str), "<>");

            $query_array = array('email' => $email_address,
                'error_string' => tep_db_prepare_input($error_string),
                'created_datetime' => 'now()'
            );
            tep_db_perform(TABLE_LOG_SES_BOUNCE, $query_array);
            unset($query_array);
        }
    }

    private function report_error($message) {
        $subject_ext = '';
        if (tep_not_null($this->aws_filename)) {
            $subject_ext = ' - ' . $this->aws_filename;
        } else if (isset($this->letter_compiled_message['Subject']['Data'])) {
            $subject_ext = ' - ' . $this->letter_to;
            $message .= '<br>Subject: ' . htmlspecialchars($this->letter_compiled_message['Subject']['Data']);
        }

        $serverName = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-';
        $subject = '[OFFGAMERS] AWS Reporting' . $subject_ext . ' from ' . $serverName;

        @tep_mail('OffGamers', '<EMAIL>', $subject, $message . "<br>Request : " . getenv('REQUEST_URI') . ' [ ' . date("F j, Y H:i") . ' ]', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

    public function __destruct() {
        unset($this->s3, $this->ses);
    }

}

?>