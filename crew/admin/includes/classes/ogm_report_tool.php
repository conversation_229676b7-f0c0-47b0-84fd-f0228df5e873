<?php

class ogm_report_tool {

    public function log_cdkey_delivered($cdkey_id_arr, $order_id, $orders_products_id, $products_id, $total_item_amount, $process_purchase_quantity) {

        if ($cdkey_id_arr) {
            $cd_key_id_str = "'" . implode("','", $cdkey_id_arr) . "'";
            $delivered_products_cd_key_array = array();
            $delivered_softpin_qty = 0;

            $cdkey_confirmed_delivered_select_sql = "   SELECT cpc.purchase_orders_id,cpc.custom_products_code_id,cpc.remarks, 
                                                        lar.custom_products_code_id AS api_cdk
                                                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                                                        LEFT JOIN " . TABLE_LOG_API_RESTOCK . " AS lar
                                                            ON cpc.custom_products_code_id = lar.custom_products_code_id                
                                                        WHERE cpc.custom_products_code_id IN (" . $cd_key_id_str . ")";
            $cdkey_confirmed_delivered_result_sql = tep_db_query($cdkey_confirmed_delivered_select_sql);
            while ($cdkey_confirm_delivered = tep_db_fetch_array($cdkey_confirmed_delivered_result_sql)) {
                // if (strpos($cdkey_confirm_delivered['remarks'], '_API') === false) {
                if ($cdkey_confirm_delivered['api_cdk'] != $cdkey_confirm_delivered['custom_products_code_id']) {
                    $delivered_softpin_qty ++;
                    $delivered_products_cd_key_array['SOFTPIN']['po_id'][$cdkey_confirm_delivered['purchase_orders_id']]['cdkey_id'][] = $cdkey_confirm_delivered['custom_products_code_id'];
                // } else if (strpos($cdkey_confirm_delivered['remarks'], '_API') >= 0) {
                } else if ($cdkey_confirm_delivered['api_cdk'] == $cdkey_confirm_delivered['custom_products_code_id']) {
                    $delivered_softpin_qty ++;
                    $delivered_products_cd_key_array['API']['custom_products_code_id'][] = $cdkey_confirm_delivered['custom_products_code_id'];
                }
            }

            if ($delivered_products_cd_key_array) {
                $delivered_products_cd_key_array['final_price'] = $total_item_amount > 0 ? $total_item_amount / $process_purchase_quantity : 0;

                $log_data = array(
                    'orders_id' => $order_id,
                    'orders_products_id' => $orders_products_id,
                    'products_id' => $products_id,
                    'products_quantity' => $delivered_softpin_qty,
                    'extra_info' => json_encode($delivered_products_cd_key_array),
                    'log_date_time' => 'now()'
                );

                tep_db_perform(TABLE_LOG_DELIVERED_PRODUCTS, $log_data);
            }
        }
    }

    public function log_cdkey_delivered_released($order_id, $products_id, $orders_products_id, $total_item_amount, $process_purchase_quantity, $refund = false) {
        if ($orders_products_id) {
            $released_products_cd_key_array = array();
            $released_softpin_qty = 0;
            // Get all the released key
           // $refund_qry = ($refund) ? '' : ', 1';
           // $delivered_check_sql = "SELECT t.log_date_time "
           //         . "FROM log_delivered_products AS t "
           //         . "WHERE orders_id = " . $order_id . " "
           //         . "ORDER BY id DESC LIMIT 1" . $refund_qry . "";
           // $delivered_check_result = tep_db_query($delivered_check_sql);
           // $delivered_check = tep_db_fetch_array($delivered_check_result);
           // $log_date_time = ($delivered_check) ? $delivered_check['log_date_time']: '0000-00-00 00:00:00' ;
            
            $cdkey_released_sql = " SELECT lrp.id, lrp.custom_products_code_id, cpc.purchase_orders_id, cpc.remarks,
                                        lar.custom_products_code_id AS api_cdk
                                    FROM " . TABLE_LOG_RELEASED_PRODUCTS . " AS lrp
                                    INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc
                                        ON cpc.custom_products_code_id = lrp.custom_products_code_id
                                    LEFT JOIN " . TABLE_LOG_API_RESTOCK . " AS lar
                                        ON cpc.custom_products_code_id = lar.custom_products_code_id
                                    WHERE lrp.orders_id = " . $order_id . "
                                        AND lrp.orders_products_id = " . $orders_products_id . "
                                        AND lrp.products_id = " . $products_id . "
                                        AND lrp.log_delivered_released_status = 0";
            $cdkey_released_result = tep_db_query($cdkey_released_sql);
            while ($cdkey_released = tep_db_fetch_array($cdkey_released_result)) {
                if ($cdkey_released['api_cdk'] != $cdkey_released['custom_products_code_id']) {
                    $released_products_cd_key_array['SOFTPIN']['po_id'][$cdkey_released['purchase_orders_id']]['cdkey_id'][] = $cdkey_released['custom_products_code_id'];
                    $released_softpin_qty ++;
                } else if ($cdkey_released['api_cdk'] == $cdkey_released['custom_products_code_id']) {
                    $released_products_cd_key_array['API']['custom_products_code_id'][] = $cdkey_released['custom_products_code_id'];
                    $released_softpin_qty ++;
                }  
                $sql_data_array = array('log_delivered_released_status' => 1);
                tep_db_perform(TABLE_LOG_RELEASED_PRODUCTS, $sql_data_array, 'update', " id='" . (int) $cdkey_released['id'] . "'");
            }
            
            if ($released_products_cd_key_array) {
                $released_products_cd_key_array['final_price'] = $total_item_amount > 0 ? $total_item_amount / $process_purchase_quantity : 0;

                $log_data = array(
                    'orders_id' => $order_id,
                    'orders_products_id' => $orders_products_id,
                    'products_id' => $products_id,
                    'released_quantity' => $released_softpin_qty,
                    'extra_info' => json_encode($released_products_cd_key_array),
                    'log_date_time' => 'now()'
                );

                tep_db_perform(TABLE_LOG_DELIVERED_RELEASED, $log_data);
            }
        }
    }

    public function log_cdkey_released($orders_id, $products_id, $orders_products_id, $cdkey_id_arr) {
        if ($cdkey_id_arr) {

            foreach ($cdkey_id_arr as $cdkeys) {
                $log_data = array(
                    'orders_id' => $orders_id,
                    'orders_products_id' => $orders_products_id,
                    'products_id' => $products_id,
                    'custom_products_code_id' => $cdkeys,
                    'log_date_time' => 'now()'
                );

                tep_db_perform(TABLE_LOG_RELEASED_PRODUCTS, $log_data);
            }
        }
    }

    public function log_order_refunded($orders_id, $orders_products_id, $products_canceled_quantity, $refund_type, $refund_id) {
        /*
         * Steps:
         * 1. get latest value from orders_products
         *    - orders_products_id
         *    - products_id
         * 2. store data in TABLE_LOG_REFUNDED_PRODUCTS
         */

        $orders_products_sql = "SELECT products_id
                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                WHERE orders_id = " . $orders_id . "
                                AND orders_products_id = " . $orders_products_id;

        $orders_products_result_sql = tep_db_query($orders_products_sql);

        while ($orders_products = tep_db_fetch_array($orders_products_result_sql)) {
            $log_data = array(
                'orders_id' => $orders_id,
                'orders_products_id' => $orders_products_id,
                'products_id' => $orders_products['products_id'],
                'refund_type' => $refund_type,
                'refund_id' => $refund_id,
                'products_canceled_quantity' => $products_canceled_quantity,
                'log_date_time' => 'now()'
            );

            tep_db_perform(TABLE_LOG_REFUNDED_PRODUCTS, $log_data);
        }
    }

    public function log_top_up_reloaded($top_up_id, $order_id, $orders_products_id, $products_id, $total_item_amount, $quantity) {
        if ($top_up_id) {

            $delivered_softpin_qty = 1;
            // check topup exist?
            $top_up_select_sql = "  SELECT top_up_id
                                    FROM " . TABLE_ORDERS_TOP_UP . " 
                                    WHERE top_up_id = '" . (int) $top_up_id . "'";
            $top_up_result_sql = tep_db_query($top_up_select_sql);
            if ($topup_result = tep_db_fetch_array($top_up_result_sql)) {
                $delivered_top_up_array['DTU']['top_up_id'][] = $topup_result['top_up_id'];
            }
            if ($delivered_top_up_array) {
                $delivered_softpin_qty = $quantity;
                $delivered_top_up_array['final_price'] = $total_item_amount;

                $log_data = array(
                    'orders_id' => $order_id,
                    'orders_products_id' => $orders_products_id,
                    'products_id' => $products_id,
                    'products_quantity' => $delivered_softpin_qty,
                    'extra_info' => json_encode($delivered_top_up_array),
                    'log_date_time' => 'now()'
                );

                tep_db_perform(TABLE_LOG_DELIVERED_PRODUCTS, $log_data);
            }
        }
    }


    public function log_physical_goods_delivered($orders_products_id, $order_id, $orders_products_id, $products_id, $total_item_amount, $quantity) {
        $delivered_top_up_array = array(
            'final_price' => $total_item_amount,
            'PHYSICAL_GOODS' => [
                'id' => [
                    $products_id
                ]
            ],
        );
        $log_data = array(
            'orders_id' => $order_id,
            'orders_products_id' => $orders_products_id,
            'products_id' => $products_id,
            'products_quantity' => $quantity,
            'extra_info' => json_encode($delivered_top_up_array),
            'log_date_time' => 'now()'
        );

        tep_db_perform(TABLE_LOG_DELIVERED_PRODUCTS, $log_data);
    }
}

?>