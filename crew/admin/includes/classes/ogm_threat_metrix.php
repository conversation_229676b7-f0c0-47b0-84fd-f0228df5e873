<?php

/*
  $Id: ogm_threat_metrix.php,v 1.15 2016/02/02 08:49:07 weesiong Exp $

  Developer: <PERSON>
  Copyright (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

class ogm_threat_metrix {

    private $org_id = TM_ORG_ID;
    private $api_key = TM_API_Key;
    private $tm_params;               // TM API Query Params
    private $order_id, $extended_order_ids, $transaction_type;  // OGM Order Type [CO/BO]
    private $device_id;
    private $event_type = 'PAYMENT';
    private $session_type = 'session-policy';
    private $threat_metrix_switch = TM_SERVICE_ENABLED;
    private $extended_variables_array = array();
    private $missing_fields = array();
    private $query_url = "https://h-api.online-metrix.net/api/session-query";
    private $servic_mode = TM_SERVICE_MODE;
    private $trans_id_table_array = null;
    private $max_duration = 144000;  // 100 days
    // Declare the database tables fields used to map with the API response data. MAPPING LIKE [ API_FIELD => DB_FIELD ]
    private $db_tables = array(TABLE_API_TM_BROWSER => array('browser_language' => 'browser_language',
            'browser_string' => 'browser_string',
            'enabled_js' => 'enabled_js',
            'enabled_fl' => 'enabled_fl',
            'enabled_ck' => 'enabled_ck',
            'enabled_im' => 'enabled_im',
            'css_image_loaded' => 'css_image_loaded',
            'flash_version' => 'flash_version',
            'flash_lang' => 'flash_lang',
            'flash_os' => 'flash_os',
            'headers_name_value_hash' => 'headers_name_value_hash',
            'headers_order_string_hash' => 'headers_order_string_hash',
            'http_os_signature' => 'http_os_signature',
            'http_referer' => 'http_referer',
            'plugin_adobe_acrobat' => 'plugin_adobe_acrobat',
            'plugin_flash' => 'plugin_flash',
            'plugin_hash' => 'plugin_hash',
            'plugin_silverlight' => 'plugin_silverlight'
        ),
        TABLE_API_TM_DEVICE => array('device_id' => 'device_id',
            'device_result' => 'device_result',
            'os' => 'os',
            'screen_res' => 'screen_res',
            'local_time_offset' => 'local_time_offset',
            'local_time_offset_range' => 'local_time_offset_range',
            'time_zone' => 'time_zone',
            'device_score' => 'device_score',
            'device_attributes' => 'device_attributes',
            'device_activities' => 'device_activities',
            'device_assert_history' => 'device_assert_history',
            'device_last_update' => 'device_last_update',
            'device_worst_score' => 'device_worst_score',
            'profiling_datetime' => 'profiling_datetime',
            'device_first_seen' => 'device_first_seen',
            'device_last_event' => 'device_last_event',
            'device_match_result' => 'device_match_result',
            'offset_measure_time' => 'offset_measure_time',
            'os_anomaly' => 'os_anomaly',
            'os_fonts_hash' => 'os_fonts_hash',
            'os_fonts_number' => 'os_fonts_number'
        ),
        TABLE_API_TM_PROXY_IP => array('proxy_ip' => 'proxy_ip',
            'proxy_ip_score' => 'proxy_ip_score',
            'proxy_ip_attributes' => 'proxy_ip_attributes',
            'proxy_ip_activities' => 'proxy_ip_activities',
            'proxy_ip_assert_history' => 'proxy_ip_assert_history',
            'proxy_ip_last_update' => 'proxy_ip_last_update',
            'proxy_ip_worst_score' => 'proxy_ip_worst_score',
            'proxy_ip_city' => 'proxy_ip_city',
            'proxy_ip_geo' => 'proxy_ip_geo',
            'proxy_ip_isp' => 'proxy_ip_isp',
            'proxy_ip_latitude' => 'proxy_ip_latitude',
            'proxy_ip_longitude' => 'proxy_ip_longitude',
            'proxy_type' => 'proxy_type',
            'proxy_ip_first_seen' => 'proxy_ip_first_seen',
            'proxy_ip_last_event' => 'proxy_ip_last_event',
            'proxy_ip_longitude' => 'proxy_ip_longitude',
            'proxy_ip_organization' => 'proxy_ip_organization',
            'proxy_ip_region' => 'proxy_ip_region',
            'proxy_ip_result' => 'proxy_ip_result'
        ),
        TABLE_API_TM_RISK_SUMMARY_N_POLICY => array('summary_risk_score' => 'summary_risk_score',
            'policy_score' => 'policy_score',
            'reason_code' => 'reason_code'
        ),
        TABLE_API_TM_TRUE_IP => array('true_ip' => 'true_ip',
            'true_ip_activities' => 'true_ip_activities',
            'true_ip_attributes' => 'true_ip_attributes',
            'true_ip_city' => 'true_ip_city',
            'true_ip_geo' => 'true_ip_geo',
            'true_ip_isp' => 'true_ip_isp',
            'true_ip_last_update' => 'true_ip_last_update',
            'true_ip_latitude' => 'true_ip_latitude',
            'true_ip_longitude' => 'true_ip_longitude',
            'true_ip_worst_score' => 'true_ip_worst_score',
            'true_ip_score' => 'true_ip_score',
            'true_ip_first_seen' => 'true_ip_first_seen',
            'true_ip_last_event' => 'true_ip_last_event',
            'true_ip_longitude' => 'true_ip_longitude',
            'true_ip_organization' => 'true_ip_organization',
            'true_ip_region' => 'true_ip_region',
            'true_ip_result' => 'true_ip_result'
        ),
        TABLE_API_TM_TRANSACTION_IDENTIFIER => array('transaction_id' => 'transaction_id',
            'request_result' => 'request_result',
            'request_id' => 'request_id',
            'device_id' => 'device_id',
            'local_attrib_1' => 'customers_id',
            'local_attrib_2' => 'transaction_type'
        ),
        TABLE_API_TM_FUZZY_DEVICE => array('fuzzy_device_id' => 'fuzzy_device_id',
            'fuzzy_device_first_seen' => 'fuzzy_device_first_seen',
            'fuzzy_device_id_confidence' => 'fuzzy_device_id_confidence',
            'fuzzy_device_last_event' => 'fuzzy_device_last_event',
            'fuzzy_device_last_update' => 'fuzzy_device_last_update',
            'fuzzy_device_match_result' => 'fuzzy_device_match_result',
            'fuzzy_device_result' => 'fuzzy_device_result',
            'fuzzy_device_score' => 'fuzzy_device_score',
            'fuzzy_device_worst_score' => 'fuzzy_device_worst_score'
        ),
        'NOT_REQUIRED' => array('org_id' => 'org_id',
            'service_type' => 'service_type',
            'session_id' => 'session_id',
            'event_type' => 'event_type',
        ),
        'EXTENDED_VARIABLES_ARRAY' => array('local_attrib_3' => 'extended_order_ids')
    );

    public function __construct($order_id = '', $order_type = 'CO') {
        // Switch is not in use currently
        if ($this->threat_metrix_switch == 'true') {
            $this->threat_metrix_switch = 1; // Turn ON
        } else {
            $this->threat_metrix_switch = 0; // Turn OFF
        }

        $this->set_order_id($order_id);
        $this->set_transaction_type($order_type);
    }

    public function execute_stored_query_call($orders_id, $customer_id = 0) {
        if ($this->threat_metrix_switch) {
            $this->set_order_id($orders_id);
            $pass_params = array();

            $get_query_select_sql = "SELECT transaction_type, extra_info FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " WHERE orders_id = '" . $this->order_id . "'";
            $get_query_result_sql = tep_db_query($get_query_select_sql);
            if ($get_query_row = tep_db_fetch_array($get_query_result_sql)) {
                $extra_info_array = json_decode($get_query_row['extra_info'], true);

                $pass_params = array(
                    'orders_id' => $this->order_id,
                    'transaction_type' => $get_query_row['transaction_type'],
                    'TM_session_id_created_timestamp' => $extra_info_array['timestamp'],
                    'session_id' => $extra_info_array['session_id'],
                    'customers_id' => $extra_info_array['customer_id'],
                    'customers_login_ip' => $extra_info_array['customers_login_ip'],
                    'customers_login_timestamp' => $extra_info_array['timestamp'],
                    'customers_city' => $extra_info_array['customer_info']['city'],
                    'customers_country' => $extra_info_array['customer_info']['country_ISO'],
                );

                if ($this->execution_rules($customer_id)) {
                    $this->execute_query_call($pass_params);
                }

                $delete_expired_records_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                                                            WHERE created_datetime < DATE_SUB(NOW(),INTERVAL 1 DAY)";
                //tep_db_query($delete_expired_records_sql);

                $delete_order_record_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                                                            WHERE orders_id = '" . $this->order_id . "'";
                //tep_db_query($delete_order_record_sql);

                unset($extra_info_array, $pass_params);
            }
        }
    }

    private function execute_query_call($ext_params = '') {
        if (!tep_not_null($ext_params)) {
            return;
        }
        $session_timestamp = $ext_params['TM_session_id_created_timestamp'];
        $this->tm_params = http_build_query($this->generate_post_params($ext_params), null, '&');

        // Checking purpose
        $checking_str = '<br>CREATE DT: ' . date("d/m/y : H:i:s", $session_timestamp)
                . '<br>Call DT: ' . date("d/m/y : H:i:s")
                . '<br>Total Time Spend In Seconds: ' . (time() - $session_timestamp)
                . '<br><br>URL: ' . $this->query_url
                . '<br><br>Params: ' . $this->tm_params;

        $options = array(CURLOPT_URL => $this->query_url,
            CURLOPT_VERBOSE => TRUE,
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_FOLLOWLOCATION => TRUE,
            CURLOPT_SSL_VERIFYHOST => 1,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $this->tm_params,
            CURLOPT_USERAGENT => "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)"
        );
        if (WWW_USE_PROXY_SERVER == 'true') {
            $options[CURLOPT_PROXY] = 'http://my-proxy.offgamers.lan:3128';
        }

        try {
            $ch = curl_init();
            curl_setopt_array($ch, $options);
            $response = curl_exec($ch);
            if ($response !== false) {
                $api_array = $this->decode_string_to_array($response);
                $this->device_id = isset($api_array['device_id']) ? $api_array['device_id'] : '';
                $mapped_array = $this->map_db_fields_with_api_return_fields($api_array);
                $content_extended_array = $this->extend_data_content($mapped_array, $ext_params);
                $db_insert_array = $this->duplicate_for_grouped_orders($content_extended_array);
                $this->insert_api_data_into_db($db_insert_array);
                unset($api_array, $mapped_array, $content_extended_array, $db_insert_array);
            } else {
                $this->report_error('CURL no response. ' . $checking_str);
            }
        } catch (Exception $e) {
            $this->report_error('CURL Error Found: ' . $e . '<br>' . $checking_str);
        }

        curl_close($ch);
    }

    private function generate_post_params($ext_params) {
        $return_array['org_id'] = $this->org_id;
        $return_array['api_key'] = $this->api_key;
        $return_array['service_type'] = $this->session_type;
        $return_array['event_type'] = $this->event_type;

        $return_array['transaction_id'] = $ext_params['orders_id'];
        $return_array['session_id'] = $ext_params['session_id'];
        $return_array['account_address_city'] = $ext_params['customers_city'];
        $return_array['account_address_country'] = $ext_params['customers_country'];
        $return_array['local_attrib_1'] = $ext_params['customers_id'];
        $return_array['local_attrib_2'] = $ext_params['transaction_type'];

        if (tep_not_null($this->extended_order_ids)) {
            $return_array['local_attrib_3'] = $this->extended_order_ids;
        }

        if ($this->servic_mode == 'Live') {
            $return_array['local_attrib_5'] = 'live';
        }

        // http_build_query will encode all the params
        return $return_array;
    }

    private function execution_rules($customers_id) {
        $fire_list = array(1, 2, 12);
        $return_bool = TRUE;

        if ($customers_id) {
            $customers_groups_id_select_sql = "	SELECT customers_aft_groups_id
                                                FROM " . TABLE_CUSTOMERS . "
                                                WHERE customers_id = '" . $customers_id . "'";
            $customers_groups_id_result_sql = tep_db_query($customers_groups_id_select_sql);
            if ($customers_groups_id_row = tep_db_fetch_array($customers_groups_id_result_sql)) {
                if (!in_array((int) $customers_groups_id_row['customers_aft_groups_id'], $fire_list)) {
                    $return_bool = FALSE;
                }
            }
        }

        return $return_bool;
    }

    // decode TM's API returns string and convert into API_ARRAY with format {API_ARRAY[API PARAMETER] = API VALUE}
    private function decode_string_to_array($api_string) {
        if (!tep_not_null($api_string))
            return '';

        $return_array = array();

        $encoded_string = urldecode($api_string);
        $encoded_array = explode("&", $encoded_string);

        foreach ($encoded_array AS $pnv) {
            list($p, $v) = explode("=", $pnv, 2);
            $return_array[str_replace(" ", "", $p)] = $v; // remove space in between parameter and save into return_array.
        }

        return $return_array;
    }

    // DB table fields Mapping and return as format {MAPPED_ARRAY[DB_TABLES][DB PARAMETER] = API VALUE}
    private function map_db_fields_with_api_return_fields($api_array) {
        $return_array = array();
        $this->missing_fields = $api_array;

        if (tep_not_null($api_array)) {
            foreach ($this->db_tables AS $table_name => $table_fields_array) {
                $db_table_not_required_insert = true;

                foreach ($table_fields_array AS $api_field => $db_field) {
                    if (isset($api_array[$api_field])) {
                        $return_array[$table_name][$db_field] = $api_array[$api_field];
                        $db_table_not_required_insert = false;
                        unset($this->missing_fields[$api_field]);
                    } else {
                        $return_array[$table_name][$db_field] = '';
                    }
                }

                if ($db_table_not_required_insert) {
                    unset($return_array[$table_name]);
                }
            }

            if (isset($return_array['EXTENDED_VARIABLES_ARRAY'])) {
                $this->extended_variables_array = $return_array['EXTENDED_VARIABLES_ARRAY'];
                unset($return_array['EXTENDED_VARIABLES_ARRAY']);
            }

            unset($return_array['NOT_REQUIRED'], $api_array);
        }

        return $return_array;
    }

    private function extend_data_content($mapped_array, $ext_params = '') {
        $return_array = array();
        if (tep_not_null($mapped_array)) {
            $return_array = $mapped_array;

            $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['create_datetime'] = 'now()';
            $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['query_string'] = $this->query_url . '?' . $this->tm_params;

            if (tep_not_null($ext_params)) {
                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['customers_login_ip'] = $ext_params['customers_login_ip'];
                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['customers_login_date'] = date("Y-m-d : H:i:s", $ext_params['customers_login_timestamp']);
            }

            if (tep_not_null($this->missing_fields)) {
                // TM 2.1 has changed unknown Session Handling - We make it remain unchange by overwrite the request result to previous value
                if (isset($this->missing_fields['unknown_session'])) {
                    if (strtolower($this->missing_fields['unknown_session']) == 'yes') {
                        $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['request_result'] = 'fail_unknown_session';
                    }
                }

                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['missing_field_bk'] = http_build_query($this->missing_fields, null, '&');
            }

            unset($mapped_array);
        }
        return $return_array;
    }

    // Duplicate the TM API return record for BO. Return format as {DB_ARRAY[BO_IDS][DB_TABLES][API PARAMETER] = API VALUE}
    private function duplicate_for_grouped_orders($extended_array) {
        $return_array = array();
        $order_ids_array = array();

        if (tep_not_null($extended_array)) {
            $return_array[$this->order_id] = $extended_array;

            if (isset($this->extended_variables_array['extended_order_ids'])) {
                $order_ids_array = explode(",", $this->extended_variables_array['extended_order_ids']);
                foreach ($order_ids_array as $order_id) {
                    $order_id = trim($order_id);

                    $extended_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['transaction_id'] = $order_id;
                    $return_array[$order_id] = $extended_array;
                }
            }
        }

        return $return_array;
    }

    // Insert proccessed API_ARRAY and save into db tables.
    private function insert_api_data_into_db($insert_arrays) {
        foreach ($insert_arrays as $order_id => $insert_array) {
            $tm_query_ID = 0;

            tep_db_perform(TABLE_API_TM_TRANSACTION_IDENTIFIER, $insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]);
            $tm_query_ID = tep_db_insert_id();

            unset($insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]); // Removed TABLE_API_TM_TRANSACTION_IDENTIFIER records from the array to prevent duplicate records.

            if (tep_not_null($insert_array)) {
                foreach ($insert_array as $table_name => $table_field) {
                    $temp_query_array = $table_field;
                    $temp_query_array['api_tm_query_id'] = $tm_query_ID;
                    tep_db_perform($table_name, $temp_query_array);

                    unset($temp_query_array);
                }
            }
        }
    }

    public function set_session_type($session_type) {
        $this->session_type = $session_type;
    }

    public function set_event_type($event_type) {
        $this->event_type = $event_type;
    }

    public function set_order_id($order_id) {
        if (tep_not_null($order_id)) {
            $this->order_id = $order_id;
        }
    }

    public function set_order_ids($order_ids_array) {
        if (is_array($order_ids_array) && count($order_ids_array)) {
            $this->order_id = array_shift($order_ids_array);
            $this->extended_order_ids = implode(",", $order_ids_array);
        }
    }

    public function set_transaction_type($transaction_type) {
        if (tep_not_null($transaction_type)) {
            $this->transaction_type = strtoupper($transaction_type);
        }
    }

    private function get_unique_customers_by_login_ip($ip) {
        $login_ip_shared_customers = array();

        // Customers who are using the SAME IP.
        if (!empty($ip)) {
            $transaction3_select_sql = "SELECT DISTINCT customers_id
                                        FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
                                        WHERE customers_id > 0
                                            AND customers_login_ip = '" . $ip . "'";
            $transaction3_result_sql = tep_db_query($transaction3_select_sql);
            while ($transaction3_row = tep_db_fetch_array($transaction3_result_sql)) {
                $login_ip_shared_customers[] = $transaction3_row['customers_id'];
            }
        }

        return $login_ip_shared_customers;
    }

    private function get_unique_customers_by_device($device_id, $last_datetime = '', $minute_interval = '') {
        $device_id_shared_customers = array();
        $sql_extension = '';

        if (tep_not_null($last_datetime) && tep_not_null($minute_interval) && (int) $minute_interval < $this->max_duration) {
            $sql_extension = " 	AND create_datetime <= '" . $last_datetime . "'
								AND create_datetime >= DATE_SUB('" . $last_datetime . "',INTERVAL " . $minute_interval . " MINUTE)";
        } else {
            $sql_extension = " 	AND create_datetime <= now()
								AND create_datetime >= DATE_SUB(now(),INTERVAL 1 YEAR)";
        }

        // Customers who are using the SAME device ID.
        $transaction_select_sql = "	SELECT DISTINCT customers_id
									FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
									WHERE customers_id != ''
										AND device_id = '" . $device_id . "'" .
                $sql_extension;
        $transaction_result_sql = tep_db_query($transaction_select_sql);
        while ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
            $device_id_shared_customers[] = $transaction_row['customers_id'];
        }

        return $device_id_shared_customers;
    }

    private function get_unique_devices_by_customer($customers_id, $last_datetime = '', $minute_interval = '') {
        $account_shared_devices = array();
        $sql_extension = '';

        if (tep_not_null($last_datetime) && tep_not_null($minute_interval) && (int) $minute_interval <= $this->max_duration) {
            $sql_extension = " 	AND create_datetime <= '" . $last_datetime . "'
								AND create_datetime >= DATE_SUB('" . $last_datetime . "',INTERVAL " . $minute_interval . " MINUTE)";
        } else {
            $sql_extension = " 	AND create_datetime <= now()
								AND create_datetime >= DATE_SUB(now(),INTERVAL 1 YEAR)";
        }

        // Get Devices which are using the SAME customers ID.
        $transaction_select_sql = "	SELECT DISTINCT device_id
									FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
									WHERE device_id != ''
										AND customers_id = '" . $customers_id . "'" .
                $sql_extension;
        $transaction_result_sql = tep_db_query($transaction_select_sql);
        while ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
            $account_shared_devices[] = $transaction_row['device_id'];
        }

        return $account_shared_devices;
    }

    public function get_db_tbl_transaction_id_array() {
        $return_array = array();

        if (!tep_not_null($this->order_id) || !tep_not_null($this->transaction_type)) {
            $this->report_error('[get_db_tbl_transaction_id_array] => order_id empty.');
        } else if (is_array($this->trans_id_table_array) && $this->trans_id_table_array['transaction_id'] == $this->order_id) {
            $return_array = $this->trans_id_table_array;
        } else {
            $transaction_select_sql = "	SELECT api_tm_query_id, transaction_id, customers_id, request_result, device_id, create_datetime
										FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
										WHERE transaction_id = '" . $this->order_id . "'
											AND transaction_type = '" . $this->transaction_type . "'";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_array = $transaction_row;
            }
        }

        $this->trans_id_table_array = $return_array;

        return $return_array;
    }

    private static function sort_compare($a, $b) {
        if ($a["customers_login_ts"] == $b["customers_login_ts"]) {
            return 0;
        }
        return ($a["customers_login_ts"] > $b["customers_login_ts"]) ? -1 : 1;
    }

    public function get_total_unique_customers_by_device($duration) {
        $return_int = 0;

        if ((int) $duration > $this->max_duration || !tep_not_null($duration)) {
            $this->report_error('[get_total_unique_customers_by_device] => Duration > ' . $this->max_duration . ' or 0 (max minute).');
        } else if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if (isset($this->trans_id_table_array['device_id']) && $this->trans_id_table_array['device_id']) {
                $return_int = count($this->get_unique_customers_by_device($this->trans_id_table_array['device_id'], $this->trans_id_table_array['create_datetime'], $duration));
            }
        } else {
            $this->report_error('[get_total_unique_customers_by_device] => data not found.<br>tran obj: ' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_int;
    }

    public function get_device_verification_result() {
        $return_string = '';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if ($this->trans_id_table_array['request_result'] == 'success') {
                $return_string = 'success';
            } else {
                $return_string = 'failed';
            }
        } else {
            $this->report_error('[get_device_verification_result] => TM records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    //Normalized Score : 0 = Good, 99 = Bad
    //TM : -100 = Bad, 100 = Good
    public function get_summary_score() {
        $return_string = '0';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            $transaction_select_sql = "	SELECT api_tm_query_id, policy_score FROM " . TABLE_API_TM_RISK_SUMMARY_N_POLICY . "
                                            WHERE api_tm_query_id = '" . $this->trans_id_table_array['api_tm_query_id'] . "' ";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_string = abs(round(($transaction_row['policy_score'] - 100) * -0.5));
            }
        } else {
            $this->report_error('[get_summary_score] => TM records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    public function get_order_tm_info($order_id = '', $transaction_type = 'CO') {
        $temp_processing_array = array();
        $return_array = array('device_id' => 'NA',
            'query_id' => '',
            'transaction_identifier' => '',
            'request_result' => 0,
            'true_ip' => 'NA',
            'true_ip_isp' => 'NA',
            'true_ip_country' => 'NA',
            'true_ip_organization' => 'NA',
            'true_ip_city' => 'NA',
            'true_ip_attributes' => 'NA',
            'proxy_ip' => 'NA',
            'proxy_detected' => 'NA',
            'proxy_type' => 'NA',
            'proxy_ip_sharing' => array(),
            'proxy_ip_city' => 'NA',
            'proxy_ip_country' => 'NA',
            'proxy_ip_isp' => 'NA',
            'proxy_ip_organization' => 'NA',
            'proxy_ip_attributes' => 'NA',
            'list_of_accounts_involved' => '',
            'number_of_devices_exist' => 'NA',
            'device_first_seen' => 'NA');

        if (tep_not_null($order_id) && tep_not_null($transaction_type)) {
            $transaction_select_sql = "	SELECT api_tm_query_id, transaction_id, request_result, device_id, customers_id
										FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
										WHERE transaction_id = " . (int) $order_id . "
											AND transaction_type = '" . $transaction_type . "'";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $query_id = $return_array['query_id'] = $transaction_row['api_tm_query_id'];
                $device_id = $return_array['device_id'] = $transaction_row['device_id'];
                $customers_id = $transaction_row['customers_id'];

                $return_array['transaction_identifier'] = $transaction_row['transaction_id'];
                $return_array['request_result'] = ($transaction_row['request_result'] == 'success') ? 1 : 0;

                // Check Proxy IP
                $proxy_ip_select_sql = "	SELECT proxy_ip, proxy_type, proxy_ip_city, proxy_ip_geo, proxy_ip_isp, proxy_ip_organization, proxy_ip_attributes
											FROM " . TABLE_API_TM_PROXY_IP . "
											WHERE api_tm_query_id = " . $query_id;
                $proxy_ip_result_sql = tep_db_query($proxy_ip_select_sql);
                if ($proxy_ip_row = tep_db_fetch_array($proxy_ip_result_sql)) {
                    $return_array['proxy_detected'] = 1;
                    $return_array['proxy_type'] = $proxy_ip_row['proxy_type'];

                    $return_array['proxy_ip'] = $proxy_ip_row['proxy_ip'];
                    $return_array['proxy_ip_sharing'] = $this->get_unique_customers_by_login_ip($proxy_ip_row['proxy_ip']);
                    $return_array['proxy_ip_city'] = $proxy_ip_row['proxy_ip_city'];
                    $return_array['proxy_ip_country'] = $proxy_ip_row['proxy_ip_geo'];
                    $return_array['proxy_ip_isp'] = $proxy_ip_row['proxy_ip_isp'];
                    $return_array['proxy_ip_organization'] = $proxy_ip_row['proxy_ip_organization'];
                    if (tep_not_null($proxy_ip_row['proxy_ip_attributes'])) {
                        $return_array['proxy_ip_attributes'] = $proxy_ip_row['proxy_ip_attributes'];
                    }
                }

                // Check True IP
                $true_ip_select_sql = "	SELECT true_ip, true_ip_isp, true_ip_geo, true_ip_organization, true_ip_city, true_ip_attributes
										FROM " . TABLE_API_TM_TRUE_IP . "
										WHERE api_tm_query_id = " . $query_id;
                $true_ip_result_sql = tep_db_query($true_ip_select_sql);
                if ($true_ip_row = tep_db_fetch_array($true_ip_result_sql)) {
                    $return_array['proxy_detected'] = 0;

                    $return_array['true_ip'] = $true_ip_row['true_ip'];
                    $return_array['true_ip_isp'] = $true_ip_row['true_ip_isp'];
                    $return_array['true_ip_country'] = $true_ip_row['true_ip_geo'];
                    $return_array['true_ip_organization'] = $true_ip_row['true_ip_organization'];
                    $return_array['true_ip_city'] = $true_ip_row['true_ip_city'];
                    if (tep_not_null($true_ip_row['true_ip_attributes'])) {
                        $return_array['true_ip_attributes'] = $true_ip_row['true_ip_attributes'];
                    }
                }

                //Device Details
                $device_select_sql = " SELECT device_first_seen FROM " . TABLE_API_TM_DEVICE . " WHERE api_tm_query_id = " . $query_id;
                $device_result_sql = tep_db_query($device_select_sql);
                if ($device_row = tep_db_fetch_array($device_result_sql)) {
                    if (!empty($device_row['device_first_seen'])) {
                        $return_array['device_first_seen'] = date('d/m/Y', strtotime($device_row['device_first_seen']));
                    }
                }

                if (tep_not_null($device_id)) {
                    // Customers who are using the SAME device ID but DIFFERENT customer id.
                    $return_array['list_of_accounts_involved'] = $this->get_unique_customers_by_device($device_id);
                }

                if (tep_not_null($customers_id)) {
                    // Customers who are using the SAME customer id but DIFFERENT device ID.
                    $return_array['number_of_devices_exist'] = count($this->get_unique_devices_by_customer($customers_id));
                }
            }
        }

        return $return_array;
    }

    public function get_buyback_order_tm_info($buyback_order_id = '', $order_id = '', $transaction_type = 'BO') {
        $return_array = $this->get_order_tm_info($buyback_order_id, $transaction_type);
        $return_array['device_id_match'] = 'NA';

        if (tep_not_null($return_array['device_id'])) {
            $device_id = $return_array['device_id'];

            $transaction4_select_sql = "SELECT customers_id, transaction_id, transaction_type
										FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
										WHERE device_id = '" . $device_id . "'
											AND transaction_id = " . $order_id . "
											AND transaction_type = 'CO'";
            $transaction4_result_sql = tep_db_query($transaction4_select_sql);
            if ($transaction4_row = tep_db_fetch_array($transaction4_result_sql)) {
                $return_array['device_id_match'] = 1;
            } else {
                $return_array['device_id_match'] = 0;
            }
        }

        return $return_array;
    }

    public function get_customer_history_info($customer_id = '') {
        $return_array = array();
        $temp_array = array();

        if (isset($_REQUEST['year']) && (int) $_REQUEST['year'] > 0) {
            $yr = (int) $_REQUEST['year'];
        } else {
            $yr = date('Y');
        }

        if (isset($_REQUEST['month']) && (int) $_REQUEST['month'] > 0) {
            $mth = (int) $_REQUEST['month'];
        } else {
            $mth = date('m');
        }

        $start_date = date('Y-m-01 00:00:00', mktime(0, 0, 0, $mth, 1, $yr));
        $end_date = date('Y-m-t 23:59:59', mktime(0, 0, 0, $mth, 1, $yr));

        for ($count_month = 0; $count_month < 6; $count_month++) {
            if ($mth != date("m") - $count_month) {
                $return_array['monthly_filter_link'] .= "<a href='" . tep_href_link(FILENAME_CUSTOMERS, tep_get_all_get_params(array('page', 'action', 'month', 'year', 'cID', 'cont', 'show_records')) . 'action=device_id_history&cID=' . (int) $customer_id . '&month=' . date("m", mktime(0, 0, 0, date("m") - $count_month, 1)) . '&year=' . date("Y", mktime(0, 0, 0, date("m") - $count_month, 1))) . "'>";
            }

            $return_array['monthly_filter_link'] .= date("M", mktime(0, 0, 0, date("m") - $count_month, 1));

            if ($mth != date("m") - $count_month) {
                $return_array['monthly_filter_link'] .= "</a>";
            }

            $return_array['monthly_filter_link'] .= "&nbsp;";
        }

        if (tep_not_null($customer_id)) {
            $transaction_identifier_select_sql = "	select api_tm_query_id, customers_login_ip, customers_login_date, device_id, create_datetime
													FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
													WHERE customers_id ='" . (int) $customer_id . "'
														AND create_datetime >= '" . $start_date . "'
														AND create_datetime <= '" . $end_date . "'";

            $transaction_identifier_result_sql = tep_db_query($transaction_identifier_select_sql);
            while ($transaction_identifier_row = tep_db_fetch_array($transaction_identifier_result_sql)) {
                $order_ip = '';
                $device_id_shared_customers = array();
                $login_ip_shared_customers = array();

                // Check True IP
                $true_ip_select_sql = "	SELECT true_ip
										FROM " . TABLE_API_TM_TRUE_IP . "
										WHERE api_tm_query_id = " . $transaction_identifier_row['api_tm_query_id'];
                $true_ip_result_sql = tep_db_query($true_ip_select_sql);
                if ($true_ip_row = tep_db_fetch_array($true_ip_result_sql)) {
                    $order_ip = $true_ip_row['true_ip'];
                } else {
                    $proxy_ip_select_sql = "SELECT proxy_ip
											FROM " . TABLE_API_TM_PROXY_IP . "
											WHERE api_tm_query_id = " . $transaction_identifier_row['api_tm_query_id'];
                    $proxy_ip_result_sql = tep_db_query($proxy_ip_select_sql);
                    if ($proxy_ip_row = tep_db_fetch_array($proxy_ip_result_sql)) {
                        $order_ip = $proxy_ip_row['proxy_ip'];
                    }
                }

                if (tep_not_null($transaction_identifier_row['device_id'])) {
                    // Customers who are using the SAME device ID.
                    $device_id_shared_customers = $this->get_unique_customers_by_device($transaction_identifier_row['device_id']);
                }

                if (tep_not_null($transaction_identifier_row['customers_login_ip'])) {
                    // Customers who are using the SAME IP.
                    $login_ip_shared_customers = $this->get_unique_customers_by_login_ip($transaction_identifier_row['customers_login_ip']);
                }

                $temp_array[] = array(
                    'id' => $transaction_identifier_row['api_tm_query_id'],
                    'customers_login_ip' => $transaction_identifier_row['customers_login_ip'],
                    'customers_login_date' => $transaction_identifier_row['customers_login_date'],
                    'customers_login_ts' => strtotime($transaction_identifier_row['customers_login_date']),
                    'request_date' => $transaction_identifier_row['create_datetime'],
                    'true_ip' => $order_ip,
                    'device_id' => $transaction_identifier_row['device_id'],
                    'device_id_sharing' => array('customers' => $device_id_shared_customers,
                        'ttl' => count($device_id_shared_customers)
                    ),
                    'ip_sharing' => array('customers' => $login_ip_shared_customers,
                        'ttl' => count($login_ip_shared_customers)
                    )
                );

                unset($device_id_shared_customers, $login_ip_shared_customers);
            }

            $transaction_identifier_select_sql = "	select customers_login_ip, customers_login_date
													FROM " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . "
													WHERE customers_id ='" . (int) $customer_id . "'
														AND customers_login_date >= '" . $start_date . "'
														AND customers_login_date <= '" . $end_date . "'";

            $transaction_identifier_result_sql = tep_db_query($transaction_identifier_select_sql);
            while ($transaction_identifier_row = tep_db_fetch_array($transaction_identifier_result_sql)) {
                $temp_array[] = array(
                    'id' => 0,
                    'customers_login_ip' => $transaction_identifier_row['customers_login_ip'],
                    'customers_login_date' => $transaction_identifier_row['customers_login_date'],
                    'customers_login_ts' => strtotime($transaction_identifier_row['customers_login_date']),
                    'request_date' => '',
                    'true_ip' => '',
                    'device_id' => '',
                    'device_id_sharing' => array('customers' => array(), 'ttl' => ''),
                    'ip_sharing' => array('customers' => array(), 'ttl' => '')
                );
            }

            if (count($temp_array)) {
                usort($temp_array, "ogm_threat_metrix::sort_compare");
                $return_array['datas'] = $temp_array;
                unset($temp_array);
            }
        }

        return $return_array;
    }

    public function get_device_info_by_query_id($query_id) {
        $return_array = array('transaction_identifier' => array('transaction_id' => 'NA',
                'request_id' => 'NA',
                'create_datetime' => 'NA',
                'request_result' => 0),
            'proxy_ip' => array('proxy_ip' => 'NA',
                'proxy_ip_sharing' => array(),
                'proxy_ip_city' => 'NA',
                'proxy_ip_geo' => 'NA',
                'proxy_ip_isp' => 'NA',
                'proxy_ip_latitude' => 'NA',
                'proxy_ip_longitude' => 'NA'),
            'true_ip' => array('true_ip' => 'NA',
                'true_ip_sharing' => array(),
                'true_ip_city' => 'NA',
                'true_ip_geo' => 'NA',
                'true_ip_isp' => 'NA',
                'true_ip_latitude' => 'NA',
                'true_ip_longitude' => 'NA'),
            'device' => array('device_id' => 'NA',
                'os' => 'NA',
                'screen_res' => 'NA',
                'local_time_offset' => 'NA',
                'device_last_update' => 'NA',
                'profiling_datetime' => 'NA',
                'os_fonts_number' => 'NA',
                'local_time_offset' => 'NA',
                'local_time_offset_range' => 'NA',
                'time_zone' => 'NA',
                'device_first_seen' => 'NA',
                'setting_country' => 'NA',
                'physical_country' => 'NA',
                'physical_region' => 'NA',
                'pc_remote' => 'NA',
                'is_mobile_device' => 'NA',
                'mobile_forwarder' => 'NA',
                'mobile_type' => 'NA',
                'device_voice' => 'NA',),
            'fuzzy_device' => array('fuzzy_device_id' => 'NA'),
            'browser' => array('browser_language' => 'NA',
                'browser_string' => 'NA',
                'enabled_js' => 'NA',
                'enabled_fl' => 'NA',
                'enabled_ck' => 'NA',
                'enabled_im' => 'NA',
                'css_image_loaded' => 'NA',
                'flash_version' => 'NA'),
            'persona' => array(
                'country' => 'NA',
                'region' => 'NA',
                'network' => 'NA',
                'cards' => 'NA',
                'devices' => 'NA',
                'emails' => 'NA',
                'orders_14_days' => 'NA',
                'orders_6_hours' => 'NA',),
        );

        if (tep_not_null($query_id)) {
            $transaction_select_sql = "	SELECT transaction_id, request_id, create_datetime, request_result
										FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
										WHERE api_tm_query_id = " . (int) $query_id;
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $device_id = $transaction_row['device_id'];
                $customers_id = $transaction_row['customers_id'];

                $return_array['transaction_identifier']['transaction_id'] = $transaction_row['transaction_id'];
                $return_array['transaction_identifier']['request_id'] = $transaction_row['request_id'];
                $return_array['transaction_identifier']['create_datetime'] = $transaction_row['create_datetime'];
                $return_array['transaction_identifier']['request_result'] = ($transaction_row['request_result'] == 'success') ? 1 : 0;

                // Check Proxy IP
                $proxy_ip_select_sql = "	SELECT proxy_ip, proxy_ip_city, proxy_ip_geo, proxy_ip_isp, proxy_ip_latitude, proxy_ip_longitude
											FROM " . TABLE_API_TM_PROXY_IP . "
											WHERE api_tm_query_id = " . $query_id;
                $proxy_ip_result_sql = tep_db_query($proxy_ip_select_sql);
                if ($proxy_ip_row = tep_db_fetch_array($proxy_ip_result_sql)) {
                    $return_array['proxy_ip']['proxy_ip'] = $proxy_ip_row['proxy_ip'];
                    $return_array['proxy_ip']['proxy_ip_sharing'] = $this->get_unique_customers_by_login_ip($proxy_ip_row['proxy_ip']);
                    $return_array['proxy_ip']['proxy_ip_city'] = $proxy_ip_row['proxy_ip_city'];
                    $return_array['proxy_ip']['proxy_ip_geo'] = $proxy_ip_row['proxy_ip_geo'];
                    $return_array['proxy_ip']['proxy_ip_geo_info_array'] = tep_get_countries_info($proxy_ip_row['proxy_ip_geo'], 'countries_iso_code_2');
                    $return_array['proxy_ip']['proxy_ip_isp'] = $proxy_ip_row['proxy_ip_isp'];
                    $return_array['proxy_ip']['proxy_ip_latitude'] = $proxy_ip_row['proxy_ip_latitude'];
                    $return_array['proxy_ip']['proxy_ip_longitude'] = $proxy_ip_row['proxy_ip_longitude'];
                }

                // Check True IP
                $true_ip_select_sql = "	SELECT true_ip, true_ip_city, true_ip_isp, true_ip_geo, true_ip_latitude, true_ip_longitude
										FROM " . TABLE_API_TM_TRUE_IP . "
										WHERE api_tm_query_id = " . $query_id;
                $true_ip_result_sql = tep_db_query($true_ip_select_sql);
                if ($true_ip_row = tep_db_fetch_array($true_ip_result_sql)) {
                    $return_array['true_ip']['true_ip'] = $true_ip_row['true_ip'];
                    $return_array['true_ip']['true_ip_sharing'] = $this->get_unique_customers_by_login_ip($true_ip_row['true_ip']);
                    $return_array['true_ip']['true_ip_city'] = $true_ip_row['true_ip_city'];
                    $return_array['true_ip']['true_ip_geo'] = $true_ip_row['true_ip_geo'];
                    $return_array['true_ip']['true_ip_isp'] = $true_ip_row['true_ip_isp'];
                    $return_array['true_ip']['true_ip_latitude'] = $true_ip_row['true_ip_latitude'] == '' ? $return_array['true_ip']['true_ip_latitude'] : $true_ip_row['true_ip_latitude'];
                    $return_array['true_ip']['true_ip_longitude'] = $true_ip_row['true_ip_longitude'] == '' ? $return_array['true_ip']['true_ip_longitude'] : $true_ip_row['true_ip_longitude'];
                }

                // Check Device
                $device_select_sql = "	SELECT device_id, os, screen_res,
											local_time_offset, local_time_offset_range, time_zone,
											device_last_update, profiling_datetime, os_fonts_number
										FROM " . TABLE_API_TM_DEVICE . "
										WHERE api_tm_query_id = " . $query_id;
                $device_result_sql = tep_db_query($device_select_sql);
                if ($device_row = tep_db_fetch_array($device_result_sql)) {
                    $return_array['device']['device_id'] = $device_row['device_id'];
                    $return_array['device']['os'] = $device_row['os'];
                    $return_array['device']['screen_res'] = $device_row['screen_res'];
                    $return_array['device']['os_fonts_number'] = $device_row['os_fonts_number'];
                    $return_array['device']['local_time_offset'] = $device_row['local_time_offset'];
                    $return_array['device']['local_time_offset_range'] = $device_row['local_time_offset_range'];
                    $return_array['device']['time_zone'] = $device_row['time_zone'];

                    $return_array['device']['device_last_update'] = $device_row['device_last_update'];
                    $return_array['device']['profiling_datetime'] = ($device_row['profiling_datetime'] > 0) ? date("Y-m-d H:i:s", $device_row['profiling_datetime']) : '';
                }

                // Check Fuzzy Device
                $fuzzy_device_select_sql = "	SELECT fuzzy_device_id
												FROM " . TABLE_API_TM_FUZZY_DEVICE . "
												WHERE api_tm_query_id = " . $query_id;
                $fuzzy_device_result_sql = tep_db_query($fuzzy_device_select_sql);
                if ($fuzzy_device_row = tep_db_fetch_array($fuzzy_device_result_sql)) {
                    $return_array['fuzzy_device']['fuzzy_device_id'] = $fuzzy_device_row['fuzzy_device_id'];
                }

                // Check Browser
                $browser_select_sql = "	SELECT browser_language, browser_string, enabled_js, enabled_fl, enabled_ck, enabled_im,
											css_image_loaded, flash_version
										FROM " . TABLE_API_TM_BROWSER . "
										WHERE api_tm_query_id = " . $query_id;
                $browser_result_sql = tep_db_query($browser_select_sql);
                if ($browser_row = tep_db_fetch_array($browser_result_sql)) {
                    $return_array['browser']['browser_language'] = $browser_row['browser_language'];
                    $return_array['browser']['browser_string'] = $browser_row['browser_string'];
                    $return_array['browser']['enabled_js'] = ucwords($browser_row['enabled_js']);
                    $return_array['browser']['enabled_fl'] = ucwords($browser_row['enabled_fl']);
                    $return_array['browser']['enabled_ck'] = ucwords($browser_row['enabled_ck']);
                    $return_array['browser']['enabled_im'] = ucwords($browser_row['enabled_im']);
                    $return_array['browser']['css_image_loaded'] = ucwords($browser_row['css_image_loaded']);
                    $return_array['browser']['flash_version'] = $browser_row['flash_version'];
                }
            }
        }

        return $return_array;
    }

    private function report_error($message) {
        $subject = '[OFFGAMERS] Order ID: ' . $this->order_id;
        @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>