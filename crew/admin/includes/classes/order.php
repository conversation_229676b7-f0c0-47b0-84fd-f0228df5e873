<?php
/*
	$Id: order.php,v 1.71 2015/06/26 06:42:05 wenbin.ng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
require_once(DIR_WS_CLASSES . 'c2c_order.php');
include_once(DIR_WS_CLASSES . 'currencies.php');

class order
{
	var $info, $totals, $order_totals, $price_info, $price_info_icons, $products, $compensate_products, $customer, $delivery;
	var $order_id, $total_undeliver_qty;

    function order($order_id)
    {
      	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->compensate_products = array();
      	$this->customer = array();
      	$this->delivery = array();
      	$this->total_undeliver_qty = 0;
      	$this->total_processed_qty = 0;
		$this->price_info = array('delivered_price' => 0, 'canceled_price' => 0, 'reversed_price' => 0);
		$this->price_info_icons = array('delivered_price' => array('label' => 'Delivered', 'on' => 'icon_status_green.gif', 'off' => 'icon_status_green_light.gif'),
										'canceled_price' => array('label' => 'Refunded', 'on' => 'icon_status_blue.gif', 'off' => 'icon_status_blue_light.gif'),
										'reversed_price' => array('label' => 'Reversed', 'on' => 'icon_status_red.gif', 'off' => 'icon_status_red_light.gif')
									);
		$this->cb_status = array('1' => 'WIN', '2' => 'LOST', '3' => 'RESOLVED');

		$this->order_id = $order_id;

      	$this->query($order_id);
    }

	function get_cdkey_identifier($products_id, $orders_products_id)
	{
		$cdkey_identifier_arr = array();

		$sql = "SELECT orders_custom_products_id, orders_custom_products_value
				FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . "
				WHERE products_id = '". tep_db_input($products_id) ."'
					AND orders_products_id = '" . tep_db_input($orders_products_id) . "'
					AND orders_custom_products_key = 'cd_key_id'";
		$result = tep_db_query($sql);
		$ids_arr = array();
		while ($row = tep_db_fetch_array($result)) {
			$ids_arr[$row['orders_custom_products_id']] = $row['orders_custom_products_value'];
		}

		unset($row);

		foreach ($ids_arr as $orders_custom_products_id => $order_cp_value) {
			if (tep_not_null($order_cp_value)) {
				$sql = "SELECT custom_products_code_id, file_name, file_type, custom_products_code_viewed
						FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
						WHERE custom_products_code_id in (".$order_cp_value.")
						ORDER BY code_date_added asc, custom_products_code_id asc";
				$result = tep_db_query($sql);
				while ($row = tep_db_fetch_array($result)) {
					$cdkey_identifier_arr[$row['custom_products_code_id']] = array(	'key_identifier' => $row['custom_products_code_id'],
																					'file_name' => $row['file_name'],
																					'file_type' => $row['file_type'],
																					'is_viewed' => $row['custom_products_code_viewed'],
																					'orders_custom_products_id' => $orders_custom_products_id);
				}
			}
		}
		return $cdkey_identifier_arr;
	}

	function get_store_credit_currency_identifier($products_id, $orders_products_id)
	{
    global $currencies;
		if(!isset($currencies)){
			$currencies = new currencies();
		}

		$sc_currency_identifier_arr = array();

    $sql = "SELECT ocp.orders_custom_products_id, ocp.orders_custom_products_value, o.orders_id, o.currency
        FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " ocp
        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON ocp.orders_products_id = op.orders_products_id
        INNER JOIN " . TABLE_ORDERS . " AS o ON op.orders_id=o.orders_id 
        WHERE ocp.orders_products_id = '" . tep_db_input($orders_products_id) . "'
          AND ocp.orders_custom_products_key = 'store_credit_currency'";
    $result = tep_db_query($sql);
    $ids_arr = array();
    if ($row = tep_db_fetch_array($result)) {
      if (c2c_order::orderSiteID($row['orders_id']) == 5) {
        $sc_currency_identifier_arr = array(
          'currency_id' => $currencies->get_id_by_code($row['currency']),
          'orders_custom_products_id' => $row['orders_custom_products_id']
        );
      }
      else{
        $sc_currency_identifier_arr = array(
          'currency_id' => $row['orders_custom_products_value'],
          'orders_custom_products_id' => $row['orders_custom_products_id']
        );
      }
    }

		return $sc_currency_identifier_arr;
	}

    function query($order_id)
    {
    	global $currencies, $languages_id;

    	$customer_grp_discount_array = array();

      	//begin PayPal_Shopping_Cart_IPN
		$order_query = tep_db_query("SELECT o.*,oei.orders_extra_info_value as site_id, coun.countries_international_dialing_code, coun.countries_name FROM " . TABLE_ORDERS . " AS o INNER JOIN ". TABLE_ORDERS_EXTRA_INFO ." as oei on o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id' LEFT JOIN " . TABLE_COUNTRIES . " AS coun ON (coun.countries_name = o.customers_country) WHERE o.orders_id = '" . (int)$order_id . "'");
		//end PayPal_Shopping_Cart_IPN

      	$order = tep_db_fetch_array($order_query);
      	$totals_query = tep_db_query("select title, text, class, value from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' order by sort_order");
      	while ($totals = tep_db_fetch_array($totals_query)) {
        	$this->totals[] = array('title' => $totals['title'],
                                	'text' => $totals['text'],
                                	'value' => $totals['value'],
                                	'class' => $totals['class']
                                	);

			$this->order_totals[$totals['class']] = array(	'title' => $totals['title'],
						                                	'text' => $totals['text'],
						                                	'value' => $totals['value']
														);
      	}

		$customer_personal_select_sql = "	SELECT c.customers_gender, civ.info_verified, c.customers_dob, c.customers_groups_id, c.customers_status, c.customers_flag, c.customers_discount, c.affiliate_ref_id, c.customers_email_address, ci.customers_info_date_account_created AS date_created
											FROM " . TABLE_CUSTOMERS . " AS c
											LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
												ON c.customers_id = ci.customers_info_id
											LEFT JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
												ON c.customers_id = civ.customers_id AND civ.customers_info_value = c.customers_email_address AND civ.info_verification_type = 'email'
											LEFT JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg
												ON c.customers_groups_id = cg.customers_groups_id
											WHERE c.customers_id = '" . $order["customers_id"] . "'";
		$customer_personal_result_sql = tep_db_query($customer_personal_select_sql);
		$customer_personal_row = tep_db_fetch_array($customer_personal_result_sql);

//		$grp_discount_select_sql = "SELECT gd.categories_id, gd.customers_groups_discount
//									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd
//									INNER JOIN " . TABLE_CATEGORIES . " AS c
//										ON gd.categories_id=c.categories_id
//									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
//										ON (c.categories_id=cd.categories_id AND cd.language_id = '" . (int)$languages_id . "')
//									WHERE gd.customers_groups_id = '" . $customer_personal_row["customers_groups_id"] . "'
//									ORDER BY c.sort_order, cd.categories_name";
//		$grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
//		while ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
//			$customer_grp_discount_array[] = array(	'cat_id' => $grp_discount_row['categories_id'],
//													'discount' => $grp_discount_row['customers_groups_discount']);
//		}

      	$this->info = array('currency' => $order['currency'],
                          	'currency_value' => $order['currency_value'],
                          	'payment_method' => $order['payment_method'],
                          	'payment_methods_id' => $order['payment_methods_id'],
                          	'payment_methods_parent_id' => $order['payment_methods_parent_id'],
                          	'cc_type' => $order['cc_type'],
                          	'cc_owner' => $order['cc_owner'],
                          	'cc_number' => $order['cc_number'],
                          	'cc_expires' => $order['cc_expires'],
                          	'pm_2CO_cc_owner_firstname' => $order['pm_2CO_cc_owner_firstname'],
                          	'pm_2CO_cc_owner_lastname' => $order['pm_2CO_cc_owner_lastname'],
                          	'date_purchased' => $order['date_purchased'],
                          	'orders_status' => $order['orders_status'],
                          	'cb_status' => $order['orders_cb_status'],
						  	'paypal_ipn_id' => $order['paypal_ipn_id'],
						  	'remote_addr' => $order['remote_addr'],
                          	'last_modified' => $order['last_modified'],
                          	'follow_up' => $order['orders_follow_up_datetime'],
                          	'normal_purchase_fully_delivered' => 1,
                          	'orders_aft_executed' => $order['orders_aft_executed'],
                          	'orders_rebated' => $order['orders_rebated'],
				          	'total_value' => $this->order_totals['ot_total']['value'],
							'orders_status_id' => $order['orders_status'],
							'site_id' => $order['site_id']
                          	);

      	$this->customer = array('id' => $order['customers_id'],
      							'name' => $order['customers_name'],
                              	'company' => $order['customers_company'],
                              	'street_address' => $order['customers_street_address'],
                              	'suburb' => $order['customers_suburb'],
                              	'city' => $order['customers_city'],
                              	'postcode' => $order['customers_postcode'],
                              	'state' => $order['customers_state'],
                              	'country' => $order['customers_country'],
                              	'format_id' => $order['customers_address_format_id'],
                              	'telephone_country' => (tep_not_null($order['customers_telephone_country']) ? $order['customers_telephone_country'] : $order['countries_name']),
								'order_country_code' => (tep_not_null($order['customers_country_international_dialing_code']) ? $order['customers_country_international_dialing_code'] : $order['countries_international_dialing_code']),
                              	'telephone' => $order['customers_telephone'],
                              	'email_address' => $order['customers_email_address'],
                              	'profile_email_address' => $customer_personal_row['customers_email_address'],
                              	'email_verified' => $customer_personal_row['info_verified'],
                              	'gender' => $customer_personal_row['customers_gender'],
                              	'dob' => $customer_personal_row['customers_dob'],
                              	'ref_id' => $customer_personal_row['affiliate_ref_id'],
                              	'customers_info_date_account_created' => $customer_personal_row['date_created'],
                              	'customers_discount' => $customer_personal_row['customers_discount'],
                              	//'customers_groups_discount' => $customer_grp_discount_array,
                              	'account_status' => $customer_personal_row['customers_status'],
                              	'customers_flag' => explode(',', $customer_personal_row['customers_flag'])
                              	);

      	$this->delivery = array('name' => $order['delivery_name'],
                              	'company' => $order['delivery_company'],
                              	'street_address' => $order['delivery_street_address'],
                              	'suburb' => $order['delivery_suburb'],
                              	'city' => $order['delivery_city'],
                              	'postcode' => $order['delivery_postcode'],
                              	'state' => $order['delivery_state'],
                              	'country' => $order['delivery_country'],
                              	'format_id' => $order['delivery_address_format_id']);

      	$this->billing = array(	'name' => $order['billing_name'],
                             	'company' => $order['billing_company'],
                             	'street_address' => $order['billing_street_address'],
                             	'suburb' => $order['billing_suburb'],
                             	'city' => $order['billing_city'],
                             	'postcode' => $order['billing_postcode'],
                             	'state' => $order['billing_state'],
                             	'country' => $order['billing_country'],
                             	'format_id' => $order['billing_address_format_id']);

      	$index = 0;

      	$orders_products_select_sql = "	SELECT *, IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
      													NULL,
      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
      												) AS purchase_eta
      									FROM " . TABLE_ORDERS_PRODUCTS . "
      									WHERE orders_id = '" . (int)$order_id . "'
      										AND parent_orders_products_id < 1
      										AND orders_products_is_compensate = 0";
      	$orders_products_query = tep_db_query($orders_products_select_sql);
      	while ($orders_products = tep_db_fetch_array($orders_products_query)) {
            $products_price = $orders_products['products_price'];
            $final_price = $orders_products['final_price'];
                $this->total_processed_qty += $orders_products['products_delivered_quantity'];
        	$this->products[$index] = array('order_products_id' => $orders_products['orders_products_id'],
											'qty' => $orders_products['products_quantity'],
                                        	'name' => $orders_products['products_name'],
                                        	'id' => $orders_products['products_id'],
                                        	'model' => $orders_products['products_model'],
                                        	'tax' => $orders_products['products_tax'],
                                        	'price' => $products_price,
                                        	'final_price' => $final_price,
                                        	'pre_order' => $orders_products['products_pre_order'],
                                        	'custom_products_type_id' => $orders_products['custom_products_type_id'],
                                        	'delivered_qty' => $orders_products['products_delivered_quantity'],
                                        	'purchase_eta' => $orders_products['purchase_eta'],
                                        	'org_purchase_eta' => $orders_products['orders_products_purchase_eta'],
                                        	'op_rebate' => (int)$orders_products['op_rebate'],
                                        	'op_rebate_delivered' => $orders_products['op_rebate_delivered'],
                                            'store_price' => $orders_products['orders_products_store_price']
                                        	);
			if ($orders_products['custom_products_type_id'] == '2') {
				$delivery_mode_sql = "	SELECT orders_products_extra_info_value
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
										WHERE orders_products_id = '".$orders_products['orders_products_id']."'
											AND orders_products_extra_info_key = 'delivery_mode'";
				$delivery_mode_result = tep_db_query($delivery_mode_sql);
				if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result)) {
					$this->products[$index]['custom_content']['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
				}
			}

			$this->products[$index]['price_info'] = array(	'delivered_price' => $orders_products['products_good_delivered_price'],
															'canceled_price' => $orders_products['products_canceled_price'],
															'reversed_price' => $orders_products['products_reversed_price']);
			$this->products[$index]['qty_info'] = array(	'delivered_quantity' => $orders_products['products_good_delivered_quantity'],
															'canceled_quantity' => $orders_products['products_canceled_quantity'],
															'reversed_quantity' => $orders_products['products_reversed_quantity']
														);

			$this->price_info['delivered_price'] += $orders_products['products_good_delivered_price'];
			$this->price_info['canceled_price'] += $orders_products['products_canceled_price'];
			$this->price_info['reversed_price'] += $orders_products['products_reversed_price'];

			if ($cdkey_info = $this->get_cdkey_identifier($orders_products['products_id'], $orders_products['orders_products_id'])) {
				$this->products[$index]['cdkey_info'] = $cdkey_info;
			}

			if ($sc_currency_info = $this->get_store_credit_currency_identifier($orders_products['products_id'], $orders_products['orders_products_id'])) {
				$this->products[$index]['sc_currency_info'] = $sc_currency_info;
			}

        	$subindex = 0;
        	$attributes_query = tep_db_query("select products_options, products_options_values, options_values_price, price_prefix from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$order_id . "' and orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'");
        	if (tep_db_num_rows($attributes_query)) {
          		while ($attributes = tep_db_fetch_array($attributes_query)) {
            		$this->products[$index]['attributes'][$subindex] = array(	'option' => $attributes['products_options'],
                                                                     			'value' => $attributes['products_options_values'],
                                                                     			'prefix' => $attributes['price_prefix'],
                                                                     			'price' => $attributes['options_values_price']);

            		$subindex++;
          		}
        	}

        	$product_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $orders_products["products_id"] . "'");
			$product = tep_db_fetch_array($product_query);
			if ($product["products_bundle_dynamic"] == "yes") {
				$b_index = 0;
				$product_bundle_dynamic_select_sql = "	SELECT orders_products_id, products_id, products_model, products_name, products_quantity, products_delivered_quantity, products_categories_id, orders_products_purchase_eta,
															products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity,
															IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
		      													NULL,
		      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
		      												) AS purchase_eta
														FROM " . TABLE_ORDERS_PRODUCTS . "
														WHERE orders_id = '" . (int)$order_id . "'
															AND parent_orders_products_id = '" . $orders_products["orders_products_id"] . "'
															AND orders_products_is_compensate=0";
				$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);

				while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
                                        $this->total_processed_qty += $product_bundle['products_delivered_quantity'];
					$this->products[$index]['bundle'][$b_index] = array('id' => $product_bundle['products_id'],
																		'order_products_id' => $product_bundle['orders_products_id'],
																		'qty' => $product_bundle['products_quantity'],
	                                        							'name' => $product_bundle['products_name'],
	                                        							'model' => $product_bundle['products_model'],
	                                        							'products_categories_id' => $product_bundle['products_categories_id'],
	                                        							'delivered_qty' => $product_bundle['products_delivered_quantity'],
	                                        							'purchase_eta' => $product_bundle['purchase_eta'],
	                                        							'org_purchase_eta' => $product_bundle['orders_products_purchase_eta']
	                                        							);
					if ($orders_products['custom_products_type_id'] == '2') {
						$delivery_mode_sql = "	SELECT orders_products_extra_info_value
												FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
												WHERE orders_products_id = '".$product_bundle['orders_products_id']."'
													AND orders_products_extra_info_key = 'delivery_mode'";
						$delivery_mode_result = tep_db_query($delivery_mode_sql);
						if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result)) {
							$this->products[$index]['bundle'][$b_index]['custom_content']['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
						}
					}
					$this->products[$index]['bundle'][$b_index]['qty_info'] = array(	'delivered_quantity' => $product_bundle['products_good_delivered_quantity'],
																						'canceled_quantity' => $product_bundle['products_canceled_quantity'],
																						'reversed_quantity' => $product_bundle['products_reversed_quantity']
																					);
					if ($product_bundle['products_delivered_quantity'] < $product_bundle['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;

					$b_index++;
				}
			} else if ($product["products_bundle"] == "yes") {
				$s_index = 0;
				$static_bundle_select_sql = "	SELECT orders_products_id, products_id, products_name, products_model, orders_products_store_price, products_price, products_tax, products_quantity, final_price, products_categories_id, products_delivered_quantity, products_categories_id, orders_products_purchase_eta,
													products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity,
													IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
      													NULL,
      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
      												) AS purchase_eta
												FROM " . TABLE_ORDERS_PRODUCTS . "
												WHERE orders_id = '" . (int)$order_id . "'
													AND parent_orders_products_id = '" . $orders_products["orders_products_id"] . "'
													AND orders_products_is_compensate = 0";
				$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
				while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
                                        $this->total_processed_qty += $static_bundle['products_delivered_quantity'];
					$this->products[$index]['static'][$s_index] = array('id' => $static_bundle['products_id'],
																		'order_products_id' => $static_bundle['orders_products_id'],
																		'qty' => $static_bundle['products_quantity'],
	                                        							'name' => $static_bundle['products_name'],
	                                        							'model' => $static_bundle['products_model'],
	                                        							'tax' => $static_bundle['products_tax'],
	                                        							'store_price' => $static_bundle['orders_products_store_price'],
	                                        							'price' => $static_bundle['products_price'],
	                                        							'final_price' => $static_bundle['final_price'],
	                                        							'products_categories_id' => $static_bundle['products_categories_id'],
	                                        							'delivered_qty' => $static_bundle['products_delivered_quantity'],
	                                        							'purchase_eta' => $static_bundle['purchase_eta'],
	                                        							'org_purchase_eta' => $static_bundle['orders_products_purchase_eta']
	                                        							);

					if ($orders_products['custom_products_type_id'] == '2') {
						$delivery_mode_sql = "	SELECT orders_products_extra_info_value
												FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
												WHERE orders_products_id = '".$static_bundle['orders_products_id']."'
													AND orders_products_extra_info_key = 'delivery_mode'";
						$delivery_mode_result = tep_db_query($delivery_mode_sql);
						if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result)) {
							$this->products[$index]['static'][$s_index]['custom_content']['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
						}
					}

					$this->products[$index]['static'][$s_index]['qty_info'] = array(	'delivered_quantity' => $static_bundle['products_good_delivered_quantity'],
																						'canceled_quantity' => $static_bundle['products_canceled_quantity'],
																						'reversed_quantity' => $static_bundle['products_reversed_quantity']
																					);
					if ($static_bundle['products_delivered_quantity'] < $static_bundle['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;

					$subproduct_type_select_sql = "	SELECT custom_products_type_id
								    				FROM " . TABLE_PRODUCTS . "
								    				WHERE products_id = '" . tep_db_input($static_bundle["products_id"]) . "'";
					$subproduct_type_result_sql = tep_db_query($subproduct_type_select_sql);

					while ($subproduct_type_row = tep_db_fetch_array($subproduct_type_result_sql)) {
						if ($subproduct_type_row['custom_products_type_id']==2) {  // CD Key product
							if ($cdkey_info = $this->get_cdkey_identifier($static_bundle['products_id'], $static_bundle['orders_products_id'])) {
								$this->products[$index]['static'][$s_index]['cdkey_info'] = $cdkey_info;
							}
						} else if ($subproduct_type_row['custom_products_type_id']==3) {  // SC product
							if ($sc_currency_info = $this->get_store_credit_currency_identifier($static_bundle['products_id'], $static_bundle['orders_products_id'])) {
								$this->products[$index]['static'][$s_index]['sc_currency_info'] = $sc_currency_info;
							}
						}
					}

					$this->total_undeliver_qty += $static_bundle['products_quantity'] - $static_bundle['products_delivered_quantity'];

					$s_index++;
				}
			} else {
				if ($orders_products['products_delivered_quantity'] < $orders_products['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;
			}

        	$index++;
      	}
	}

	function format_character_name($matches) {
		// as usual: $matches[0] is the complete match
		// $matches[1] the match for the first subpattern
		// enclosed in '(...)' and so on

		$formated_string = preg_replace('/([^A-Z]+)/s', "<span style='color:#0A246A;'>\\1</span>", preg_quote($matches[1], "/"));
		$formated_string = tep_db_prepare_input($formated_string);

		return "<span style='color:#019858;'>".$formated_string."</span>";
	}

	function get_product_order_info() {
		$total_ordered_product = count($this->products);
		$styling_keys_array = array('char_name', 'char_account_name', 'char_account_pwd', 'char_wow_account');

		for ($prod_cnt=0; $prod_cnt < $total_ordered_product; $prod_cnt++) {
			$this->products[$prod_cnt]['extra_info'] = array();

			$extra_info_select_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
										WHERE orders_products_id = '" . tep_db_input($this->products[$prod_cnt]['order_products_id']) . "'";
			$extra_info_result_sql = tep_db_query($extra_info_select_sql);

			while ($extra_info_row = tep_db_fetch_array($extra_info_result_sql)) {
				$value = $extra_info_row['orders_products_extra_info_value'];
                              
                                if($extra_info_row['orders_products_extra_info_key'] == "delivery_mode"){
                                    $deliver_mode = tep_array_unserialize($extra_info_row['orders_products_extra_info_value']);
                                            if ($deliver_mode && is_array($deliver_mode)) {
                                                $value = isset($deliver_mode['value']) ? $deliver_mode['value'] : "";
                                            } 
                                } else  if($extra_info_row['orders_products_extra_info_key'] == "delivery_mode_attributes"){
                                    $deliver_mode_attributes = tep_array_unserialize($extra_info_row['orders_products_extra_info_value']);
                                            if ($deliver_mode_attributes && is_array($deliver_mode_attributes)) {
                                                $buyer_character = "";
                                                foreach ($deliver_mode_attributes as $deliver_mode_attributes_value) {
                                                    $buyer_character .= isset($deliver_mode_attributes_value['name']) ? $deliver_mode_attributes_value['label'] . ": <span style='color: red; font-size: 15px;'><b>".$deliver_mode_attributes_value['name'] . "</b></span><br />" : "";
                                                }
                                                $value = $buyer_character;
                                                $extra_info_row['orders_products_extra_info_key'] = "char_name";
                                            } 
                                } else if (in_array($extra_info_row['orders_products_extra_info_key'], $styling_keys_array)) {
					$value = preg_replace_callback(	'/([^a-z]+)/s',
              										array($this, 'format_character_name'),
              										$value);

					if (tep_not_null($value))	$value = '<span style="color: red; font-size: 15px;"><b>'.$value.'</b></span>';
				}
                                
				$this->products[$prod_cnt]['extra_info'][$extra_info_row['orders_products_extra_info_key']] = $value;
			}
		}
	}

	function get_compensate_products() {
		$index = 0;
		$this->info['compensation_fully_delivered'] = 1;

		$compensate_product_select_sql = "	SELECT *,
												IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
  													NULL,
  													(TO_DAYS(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
  												) AS purchase_eta
											FROM " . TABLE_ORDERS_PRODUCTS . "
											WHERE orders_id = '" . (int)$this->order_id . "'
												AND orders_products_is_compensate=1
											ORDER BY orders_products_id";
		$compensate_product_result_sql = tep_db_query($compensate_product_select_sql);
      	while ($compensate_product_row = tep_db_fetch_array($compensate_product_result_sql)) {
        	$this->compensate_products[$index] = array(	'order_products_id' => $compensate_product_row['orders_products_id'],
														'qty' => $compensate_product_row['products_quantity'],
			                                        	'name' => $compensate_product_row['products_name'],
            			                            	'id' => $compensate_product_row['products_id'],
                        			                	'price' => $compensate_product_row['products_price'],
            			                            	'final_price' => $compensate_product_row['final_price'],
                        			                	'pre_order' => $compensate_product_row['products_pre_order'],
                                    			    	'custom_products_type_id' => $compensate_product_row['custom_products_type_id'],
			                                        	'delivered_qty' => $compensate_product_row['products_delivered_quantity'],
            			                            	'purchase_eta' => $compensate_product_row['purchase_eta'],
                        			                	'org_purchase_eta' => $compensate_product_row['orders_products_purchase_eta']
                                    		    	);

			$this->compensate_products[$index]['price_info'] = array(	'delivered_price' => $compensate_product_row['products_good_delivered_price'],
																		'canceled_price' => $compensate_product_row['products_canceled_price'],
																		'reversed_price' => $compensate_product_row['products_reversed_price']);

			if ($compensate_product_row['products_delivered_quantity'] < $compensate_product_row['products_quantity'])	$this->info['compensation_fully_delivered'] = 0;

			if ($cdkey_info = $this->get_cdkey_identifier($compensate_product_row['products_id'], $compensate_product_row['orders_products_id'])) {
				$this->compensate_products[$index]['cdkey_info'] = $cdkey_info;
			}

			$compensation_select_sql = "SELECT compensate_for_orders_products_id, compensate_entered_currency, compensate_entered_currency_value, compensate_order_currency, compensate_order_currency_value, compensate_accident_amount, compensate_non_accident_amount, compensate_supplier_amount, compensate_by_supplier_id,
											orders_compensate_products_added_by, orders_compensate_products_messages
										FROM " . TABLE_ORDERS_COMPENSATE_PRODUCTS . "
										WHERE orders_products_id = '" . tep_db_input($compensate_product_row['orders_products_id']) . "'";
			$compensation_result_sql = tep_db_query($compensation_select_sql);
	      	if ($compensation_row = tep_db_fetch_array($compensation_result_sql)) {
	      		$this->compensate_products[$index]['compensate'] = array(	'for_product' => $compensation_row['compensate_for_orders_products_id'],
																			'input_currency' => $compensation_row['compensate_entered_currency'],
																			'input_currency_value' => $compensation_row['compensate_entered_currency_value'],
																			'output_currency' => $compensation_row['compensate_order_currency'],
																			'output_currency_value' => $compensation_row['compensate_order_currency_value'],
																			'accident_amount' => $compensation_row['compensate_accident_amount'],
																			'non_accident_amount' => $compensation_row['compensate_non_accident_amount'],
																			'supplier_amount' => $compensation_row['compensate_supplier_amount'],
																			'supplier_id' => $compensation_row['compensate_by_supplier_id'],
																			'added_by' => $compensation_row['orders_compensate_products_added_by'],
																			'comments' => $compensation_row['orders_compensate_products_messages']
																			);

		    }

			$index++;
		}
	}

	function get_products_ordered()
	{
		global $languages_id, $currencies;
		$products_ordered = '';
		for ($i=0, $n=sizeof($this->products); $i<$n; $i++) {
		    $products_ordered_attributes = '';
		    if (isset($this->products[$i]['attributes'])) {
		      	for ($j=0, $n2=sizeof($this->products[$i]['attributes']); $j<$n2; $j++) {
		      		if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $this->products[$i]['id'] . "'
					                                and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $this->products[$i]['id'] . "' and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);

		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}

			$cat_path = tep_output_generated_category_path($this->products[$i]['id'], 'product');
	      	$product_email_display_str = $this->products[$i]['name'] . (tep_not_null($this->products[$i]['model']) ? ' (' . $this->products[$i]['model'] . ')' : '');

		    $products_ordered .= $this->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($this->products[$i]['final_price'], $this->products[$i]['tax'], $this->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}

		return $products_ordered;
	}

	function get_payments_breakdown()
	{
		$payments_breakdown = '';
		for ($i=0, $n=sizeof($this->totals); $i<$n; $i++) {
			$payments_breakdown .= strip_tags($this->totals[$i]['title']) . ' ' . strip_tags($this->totals[$i]['text']) . "\n";
		}

		return $payments_breakdown;
	}

	function do_payment_action($fn_name, $language)
	{
		$selected_payment_module_obj = '';

		$selected_payment_module_obj = new payment_methods($this->info['payment_methods_id']);
		if (is_object($selected_payment_module_obj)) {
			$selected_payment_module_obj = $selected_payment_module_obj->payment_method_array;
			if (method_exists($selected_payment_module_obj, $fn_name)) eval('$selected_payment_module_obj->$fn_name($this->order_id);');	// Use single quote
		}
	}

	function update_order_status($orders_status, $orders_status_history_data_sql_array, $deduct_stock_for_automated_payment = false) {
    	$orders_status_update_sql_data = array(	'orders_status' => tep_db_input($orders_status),
				 								'last_modified' => 'now()'
				 								);

		if (!isset($orders_status_history_data_sql_array['data']['orders_id']) || (int)$orders_status_history_data_sql_array['data']['orders_id']==0) $orders_status_history_data_sql_array['data']['orders_id'] = $this->order_id;

		if ($this->info['orders_aft_executed'] != '-1') {
			$orders_status_update_sql_data['orders_aft_executed'] = 0;
		}

		tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . (int)$this->order_id . "'");
		tep_update_orders_status_counter($orders_status_history_data_sql_array['data']);

		if ($orders_status_history_data_sql_array['action'] == 'insert') {
			$orders_status_history_data_sql_array['data']['date_added'] = 'now()';
			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_sql_array['data']);
		} else if ($orders_status_history_data_sql_array['action'] == 'update') {
			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_sql_array['data'], 'update', "orders_id = '" . (int)$this->order_id . "'");
		}

		if ($deduct_stock_for_automated_payment) {
			tep_deduct_stock_for_automated_payment($this->order_id, $this->products, $this->info['orders_status_id'], $orders_status);
		}

		if ($orders_status == 7) { // order in verifying
			$aft_obj = new anti_fraud($this->order_id);
			$aft_obj->execute_aft_script();
		}
    }
    
    function update_affiliate_details($status, $affiliateRemarks){
        $order_id = (int)$this->order_id;
        //API G2G-crew
        include_once(DIR_WS_CLASSES . 'curl.php');
        $curl_obj = new curl();
        $G2G_CREW_AFFILIATE_URL = G2G_API_URL . '/affiliate/update-details';
        $data = array(
            'merchant' => G2G_API_OGCREW_MERCHANT,
            'signature' => md5($order_id . "|" . G2G_API_OGCREW_SECRET),
            'status' => $status,
            'oid' => $order_id,
            'user' => $_SESSION['login_email_address'],
            'remarks' => $affiliateRemarks
        );
        $response = $curl_obj->curl_post($G2G_CREW_AFFILIATE_URL, $data);
        if($response){
            $result = json_decode($response, true);
            if(isset($result['status']) && $result['status'] == true && isset($result['result'])){    
                return TRUE;
            } 
        }
        return FALSE;
    }

	public static function getOrderCancellationRefund($oid){ 
        $result = []; 
        include_once(DIR_WS_CLASSES . 'curl.php'); 
        $curl_obj = new curl(); 
        $G2G_CREW_ORDER_URL = G2G_API_URL . '/order/order-cancellation-refund'; 
        $data = array( 
            'merchant' => G2G_API_OGCREW_MERCHANT, 
            'signature' => md5($oid . "|" . G2G_API_OGCREW_SECRET), 
            'order_id' => $oid, 
        ); 
        $response = $curl_obj->curl_get($G2G_CREW_ORDER_URL, $data); 
        if($response){ 
            $result = json_decode($response, true);
        } 
        return $result; 
    } 
}
?>