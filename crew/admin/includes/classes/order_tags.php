<?
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ORDER_TAGS)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ORDER_TAGS);
}

class order_tags {

    var $status_options;
    var $supplier_status_options;
    var $buyback_status_options;
    var $lowstock_status_options;
    var $po_status_options;
    var $filename_array;

    // class constructor
    function order_tags() {
        $this->filename_array = array();
        $this->status_options = array();
        $this->supplier_status_options = array();
        $this->buyback_status_options = array();
        $this->lowstock_status_options = array();
        $this->po_status_options = array();

        // tagging system supportable files
        $this->filename_array[] = array('id' => FILENAME_BUYBACK_REQUESTS, 'text' => 'Buyback');
        $this->filename_array[] = array('id' => FILENAME_STATS_ORDERS_TRACKING, 'text' => 'Order Lists');
        $this->filename_array[] = array('id' => FILENAME_PRODUCTS_LOW_STOCK, 'text' => 'Products Low Stock');
        $this->filename_array[] = array('id' => FILENAME_PROGRESS_REPORT, 'text' => 'Progress Report');
        $this->filename_array[] = array('id' => FILENAME_EDIT_PURCHASE_ORDERS, 'text' => 'Purchase Order Lists');

        // customers orders status array
        $order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . (int) $_SESSION['languages_id'] . "' ORDER BY orders_status_sort_order";
        $order_status_result_sql = tep_db_query($order_status_select_sql);
        while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
            $this->status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
        }

        // progress report status array
        $supplier_tasks_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE language_id='" . (int) $_SESSION['languages_id'] . "' ORDER BY supplier_tasks_status_sort_order";
        $supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
        while ($supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql)) {
            $this->supplier_status_options[$supplier_tasks_status_row["supplier_tasks_status_id"]] = $supplier_tasks_status_row["supplier_tasks_status_name"];
        }

        // buyback status array
        $buyback_status_select_sql = "SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE language_id='" . (int) $_SESSION['languages_id'] . "' ORDER BY buyback_status_sort_order";
        $buyback_status_result_sql = tep_db_query($buyback_status_select_sql);
        while ($buyback_status_row = tep_db_fetch_array($buyback_status_result_sql)) {
            $this->buyback_status_options[$buyback_status_row["buyback_status_id"]] = $buyback_status_row["buyback_status_name"];
        }

        // hardcode product low stock warning report page status. There is only 1 pending status.
        $this->lowstock_status_options[1] = 'Pending';

        // purchase orders status array
        $po_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id='" . (int) $_SESSION['languages_id'] . "' ORDER BY purchase_orders_status_sort_order";
        $po_status_result_sql = tep_db_query($po_status_select_sql);
        while ($po_status_row = tep_db_fetch_array($po_status_result_sql)) {
            $this->po_status_options[$po_status_row["purchase_orders_status_id"]] = $po_status_row["purchase_orders_status_name"];
        }
    }

    function check_duplicate_order_tag($filename_new, $trans_array, &$messageStack) {
        $error = false;
        $submitted_order_status = $trans_array["order_status"][$filename_new];

        for ($i = 0; $i < count($submitted_order_status); $i++) {
            $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($trans_array["tag_name"]) . "' AND filename='" . tep_db_input($filename_new) . "' AND FIND_IN_SET('" . $submitted_order_status[$i] . "', orders_tag_status_ids) " . ($trans_array["action"] == "update_tags" ? " AND orders_tag_id <> '" . (int) $trans_array["tagID"] . "'" : '') . ";";
            $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
            if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                $error = true;
                switch ($filename_new) {
                    case FILENAME_STATS_ORDERS_TRACKING:
                        $status_str = $this->status_options[$submitted_order_status[$i]];
                        break;
                    case FILENAME_PROGRESS_REPORT:
                        $status_str = $this->supplier_status_options[$submitted_order_status[$i]];
                        break;
                    case FILENAME_BUYBACK_REQUESTS:
                        $status_str = $this->buyback_status_options[$submitted_order_status[$i]];
                        break;
                    case FILENAME_PRODUCTS_LOW_STOCK:
                        $status_str = $this->lowstock_status_options[$submitted_order_status[$i]];
                        break;
                    case FILENAME_EDIT_PURCHASE_ORDERS:
                        $status_str = $this->po_status_options[$submitted_order_status[$i]];
                        break;
                }
                $messageStack->add('This tag name had been used for ' . $status_str . ' status!', 'error');
            }
        }

        return $error;
    }

    function get_all_tags_by_filename($filename, $status) {
        $tags_array = array();

        $tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE filename='" . tep_db_input($filename) . "' AND FIND_IN_SET('" . $status . "', orders_tag_status_ids)";
        $tag_result_sql = tep_db_query($tag_select_sql);
        while ($tag_row = tep_db_fetch_array($tag_result_sql)) {
            $tags_array[$tag_row['orders_tag_id']] = $tag_row['orders_tag_name'];
        }

        return $tags_array;
    }

    function get_tags_name_from_id($orders_tag_id_list, $filename, $status) {
        $tags_name = array();
        $tags_id_array = explode(',', $orders_tag_id_list);

        $tags_array = $this->get_all_tags_by_filename($filename, $status);
        foreach ($tags_id_array as $tag_id) {
            $tags_name[] = $tags_array[$tag_id];
        }

        return implode(',', $tags_name);
    }

    function update_order_tag($filename_new, $trans_array, &$messageStack) {
        $check_success = true;
        $order_status_str = '';

        // status checkbox data checking
        if (is_array($trans_array["order_status"]) && count($trans_array["order_status"][$filename_new]) > 0) {
            foreach ($trans_array["order_status"][$filename_new] as $key => $value) {
                if ($value == 'on') {
                    $trans_array["order_status"][$filename_new][$key] = 0;
                }
            }
            $order_status_str = implode(',', $trans_array["order_status"][$filename_new]);
        } else {
            $check_success = false;
            $messageStack->add('No order status is selected!', 'error');
        }

        if ($check_success) {
            // obtain old filename before data updating
            if ($trans_array['action'] == "update_tags") {
                $filename_select_sql = "SELECT filename FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id ='" . (int) $trans_array["tagID"] . "'";
                $filename_result_sql = tep_db_query($filename_select_sql);
                $filename_row = tep_db_fetch_array($filename_result_sql);
                $filename_old = $filename_row['filename'];
            }

            $order_tags_sql_data_array = array('orders_tag_name' => tep_db_prepare_input($trans_array["tag_name"]),
                'orders_tag_status_ids' => tep_db_prepare_input($order_status_str),
                'filename' => tep_db_prepare_input($filename_new)
            );
            if ($trans_array['action'] == "insert_tags") {
                tep_db_perform(TABLE_ORDERS_TAG, $order_tags_sql_data_array);
            } else {
                tep_db_perform(TABLE_ORDERS_TAG, $order_tags_sql_data_array, 'update', ' orders_tag_id="' . (int) $trans_array["tagID"] . '"');
            }

            if ($trans_array['action'] == "update_tags") {
                if ($filename_new == $filename_old) {
                    if ($filename_new == FILENAME_STATS_ORDERS_TRACKING) {
                        $unassign_orders_tag_select_sql = "SELECT orders_id, orders_tag_ids FROM " . TABLE_ORDERS . " WHERE orders_status NOT IN (" . $order_status_str . ") AND FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                        while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . $unassign_orders_tag_row["orders_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_PROGRESS_REPORT) {
                        $unassign_orders_tag_select_sql = "SELECT orders_products_id, orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE supplier_tasks_status NOT IN (" . $order_status_str . ") AND FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                        while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_products_id='" . $unassign_orders_tag_row["orders_products_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_BUYBACK_REQUESTS) {
                        $unassign_orders_tag_select_sql = "SELECT buyback_request_group_id, orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_status_id NOT IN (" . $order_status_str . ") AND FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                        while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . $unassign_orders_tag_row["buyback_request_group_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_PRODUCTS_LOW_STOCK) {
                        $unassign_orders_tag_select_sql = "SELECT products_id, low_stock_tag_ids FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE low_stock_status NOT IN (" . $order_status_str . ") AND FIND_IN_SET('" . (int) $trans_array["tagID"] . "', low_stock_tag_ids)";
                        $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                        while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["low_stock_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids='" . $new_tag_string . "' WHERE products_id='" . $unassign_orders_tag_row["products_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_EDIT_PURCHASE_ORDERS) {
                        $unassign_orders_tag_select_sql = "SELECT purchase_orders_id, purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_status NOT IN (" . $order_status_str . ") AND FIND_IN_SET('" . (int) $trans_array["tagID"] . "', purchase_orders_tag_ids)";
                        $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                        while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["purchase_orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . $unassign_orders_tag_row["purchase_orders_id"] . "'");
                        }
                    }
                } else {
                    if ($filename_new == FILENAME_STATS_ORDERS_TRACKING) {
                        $unassign_orders_tags_select_sql = "SELECT orders_products_id, orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tags_result_sql = tep_db_query($unassign_orders_tags_select_sql);
                        while ($unassign_orders_tags_row = tep_db_fetch_array($unassign_orders_tags_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tags_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_products_id='" . $unassign_orders_tags_row["orders_products_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_PROGRESS_REPORT) {
                        $unassign_orders_tags_select_sql = "SELECT orders_id, orders_tag_ids FROM " . TABLE_ORDERS . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tags_result_sql = tep_db_query($unassign_orders_tags_select_sql);
                        while ($unassign_orders_tags_row = tep_db_fetch_array($unassign_orders_tags_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tags_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . $unassign_orders_tags_row["orders_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_BUYBACK_REQUESTS) {
                        $unassign_orders_tags_select_sql = "SELECT buyback_request_group_id, orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                        $unassign_orders_tags_result_sql = tep_db_query($unassign_orders_tags_select_sql);
                        while ($unassign_orders_tags_row = tep_db_fetch_array($unassign_orders_tags_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tags_row["orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . $unassign_orders_tags_row["buyback_request_group_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_PRODUCTS_LOW_STOCK) {
                        $unassign_orders_tags_select_sql = "SELECT products_id, low_stock_tag_ids FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', low_stock_tag_ids)";
                        $unassign_orders_tags_result_sql = tep_db_query($unassign_orders_tags_select_sql);
                        while ($unassign_orders_tags_row = tep_db_fetch_array($unassign_orders_tags_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tags_row["low_stock_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids='" . $new_tag_string . "' WHERE products_id='" . $unassign_orders_tags_row["products_id"] . "'");
                        }
                    } else if ($filename_new == FILENAME_EDIT_PURCHASE_ORDERS) {
                        $unassign_orders_tags_select_sql = "SELECT purchase_orders_id, purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', purchase_orders_tag_ids)";
                        $unassign_orders_tags_result_sql = tep_db_query($unassign_orders_tags_select_sql);
                        while ($unassign_orders_tags_row = tep_db_fetch_array($unassign_orders_tags_result_sql)) {
                            $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                            $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tags_row["purchase_orders_tag_ids"]);
                            if (substr($new_tag_string, 0, 1) == ',')
                                $new_tag_string = substr($new_tag_string, 1);
                            if (substr($new_tag_string, -1) == ',')
                                $new_tag_string = substr($new_tag_string, 0, -1);

                            tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . $unassign_orders_tags_row["purchase_orders_id"] . "'");
                        }
                    }
                }
            }
        }

        return $check_success;
    }

    function delete_order_tag($trans_array, &$messageStack) {
        if (tep_not_null($trans_array["tagID"])) {
            $delete_filename_select_sql = "SELECT filename FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $trans_array["tagID"] . "'";
            $delete_filename_result_sql = tep_db_query($delete_filename_select_sql);
            $delete_filename_row = tep_db_fetch_array($delete_filename_result_sql);

            $tags_delete_sql = "DELETE FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $trans_array["tagID"] . "'";
            tep_db_query($tags_delete_sql);

            if ($delete_filename_row['filename'] == FILENAME_STATS_ORDERS_TRACKING) {
                $unassign_orders_tag_select_sql = "SELECT orders_id, orders_tag_ids FROM " . TABLE_ORDERS . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . $unassign_orders_tag_row["orders_id"] . "'");
                }
            } else if ($delete_filename_row['filename'] == FILENAME_PROGRESS_REPORT) {
                $unassign_orders_tag_select_sql = "SELECT orders_products_id, orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_products_id='" . $unassign_orders_tag_row["orders_products_id"] . "'");
                }
            } else if ($delete_filename_row['filename'] == FILENAME_BUYBACK_REQUESTS) {
                $unassign_orders_tag_select_sql = "SELECT buyback_request_group_id, orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . $unassign_orders_tag_row["buyback_request_group_id"] . "'");
                }
            } else if ($delete_filename_row['filename'] == FILENAME_PRODUCTS_LOW_STOCK) {
                $unassign_orders_tag_select_sql = "SELECT products_id, low_stock_tag_ids FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', low_stock_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["low_stock_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids='" . $new_tag_string . "' WHERE products_id='" . $unassign_orders_tag_row["products_id"] . "'");
                }
            } else if ($delete_filename_row['filename'] == FILENAME_EDIT_PURCHASE_ORDERS) {
                $unassign_orders_tag_select_sql = "SELECT purchase_orders_id, purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE FIND_IN_SET('" . (int) $trans_array["tagID"] . "', purchase_orders_tag_ids)";
                $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                    $TagRemovePattern = "/(,)?" . (int) $trans_array["tagID"] . "(,)?/is";
                    $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["purchase_orders_tag_ids"]);
                    if (substr($new_tag_string, 0, 1) == ',')
                        $new_tag_string = substr($new_tag_string, 1);
                    if (substr($new_tag_string, -1) == ',')
                        $new_tag_string = substr($new_tag_string, 0, -1);

                    tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . $unassign_orders_tag_row["purchase_orders_id"] . "'");
                }
            }
        }
    }

    function search_order_tags() {
        ob_start();
        echo tep_draw_form('filter_tags_order_status', FILENAME_ORDERS_TAGS, '', 'post');
        ?>
        <table border="0" cellspacing="0" cellpadding="0" width="100%">
            <tr>
                <td>
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="smallText" align="left"><?= HEADING_TITLE_FILENAME ?></td>
                            <td colspan="2"><?= tep_draw_pull_down_menu('filename_search', array_merge(array(array('id' => 'all', 'text' => TEXT_ALL_FILES)), $this->filename_array), $_SESSION["tag_filename_search"], ' id="cmbCurrentFilename" onChange="update_order_status_option(this, \'cmbCurrentFilename\', \'cmbDesiredOrderStatus\')" ') ?></td>
                        </tr>
                        <tr>
                            <td class="smallText" colspan="3"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td class="smallText" align="left">
                                <div id="order_status_title_div" class=<?= tep_not_null($_SESSION["tag_filename_search"]) ? (($_SESSION["tag_filename_search"] == 'all') ? "hide" : "show") : "hide" ?>>
                                    <?= HEADING_TITLE_SEARCH ?>
                                </div>
                            </td>
                            <td class="smallText" align="left">
                                <div id="order_status_div" class=<?= tep_not_null($_SESSION["tag_filename_search"]) ? (($_SESSION["tag_filename_search"] == 'all') ? "hide" : "show") : "hide" ?>>
                                    <?= tep_draw_pull_down_menu('order_status_search', array_merge(array(array('id' => 'all', 'text' => TEXT_ALL_ORDER_STATUS)), ($_SESSION["tag_filename_search"] == FILENAME_STATS_ORDERS_TRACKING) ? tep_get_orders_status() : ($_SESSION["tag_filename_search"] == FILENAME_PROGRESS_REPORT) ? tep_get_supplier_tasks_status() : ($_SESSION["tag_filename_search"] == FILENAME_BUYBACK_REQUESTS) ? tep_get_buyback_status() : ($_SESSION["tag_filename_search"] == FILENAME_EDIT_PURCHASE_ORDERS) ? tep_get_purchase_orders_status() : tep_get_products_low_stock_status()), $_SESSION["tag_order_status_search"], ' id="cmbDesiredOrderStatus" ') ?>
                                </div>
                            </td>
                            <td align="right">&nbsp;<?= tep_submit_button('Go', 'Go', 'onClick="this.form.submit();"') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        </form>
        <script language="javascript">
            function appendToSelect(select, value, content) {
                var opt = document.createElement('option');
                opt.value = value;
                opt.text = content;
                select.options.add(opt);
            }

            function clearOptionList(obj) {
                while (obj.options.length > 0) {
                    obj.remove(0);
                }
            }

            function refreshOrderStatus(filenameSelected) {
                if (filenameSelected.value == '<?= FILENAME_PROGRESS_REPORT ?>') {
                    document.getElementById('task_status_span').className = 'show';
                    document.getElementById('order_status_span').className = 'hide';
                    document.getElementById('buyback_status_span').className = 'hide';
                    document.getElementById('lowstock_status_span').className = 'hide';
                    document.getElementById('po_status_span').className = 'hide';
                } else if (filenameSelected.value == '<?= FILENAME_STATS_ORDERS_TRACKING ?>') {
                    document.getElementById('order_status_span').className = 'show';
                    document.getElementById('task_status_span').className = 'hide';
                    document.getElementById('buyback_status_span').className = 'hide';
                    document.getElementById('lowstock_status_span').className = 'hide';
                    document.getElementById('po_status_span').className = 'hide';
                } else if (filenameSelected.value == '<?= FILENAME_BUYBACK_REQUESTS ?>') {
                    document.getElementById('buyback_status_span').className = 'show';
                    document.getElementById('order_status_span').className = 'hide';
                    document.getElementById('task_status_span').className = 'hide';
                    document.getElementById('lowstock_status_span').className = 'hide';
                    document.getElementById('po_status_span').className = 'hide';
                } else if (filenameSelected.value == '<?= FILENAME_PRODUCTS_LOW_STOCK ?>') {
                    document.getElementById('lowstock_status_span').className = 'show';
                    document.getElementById('buyback_status_span').className = 'hide';
                    document.getElementById('order_status_span').className = 'hide';
                    document.getElementById('task_status_span').className = 'hide';
                    document.getElementById('po_status_span').className = 'hide';
                } else if (filenameSelected.value == '<?= FILENAME_EDIT_PURCHASE_ORDERS ?>') {
                    document.getElementById('po_status_span').className = 'show';
                    document.getElementById('lowstock_status_span').className = 'hide';
                    document.getElementById('buyback_status_span').className = 'hide';
                    document.getElementById('order_status_span').className = 'hide';
                    document.getElementById('task_status_span').className = 'hide';
                }
            }

            function update_order_status_option(sel1, selObj1, selObj2) {
                var filename_select_obj = sel1.value;
                var filename_obj = DOMCall(selObj1);
                var order_status_obj = DOMCall(selObj2);

                clearOptionList(order_status_obj);

                var optionsStatus = Array();

        <? $orderStatus = array(); ?>

                if (filename_select_obj == '<?= FILENAME_STATS_ORDERS_TRACKING ?>') {
                    document.getElementById('order_status_div').className = 'show';
                    document.getElementById('order_status_title_div').className = 'show';
                    appendToSelect(order_status_obj, 'all', '<?= TEXT_ALL_ORDER_STATUS ?>');
        <?
        $orderStatus = tep_get_orders_status();
        for ($count = 0; $count < count($orderStatus); $count++) {
            ?>
                        appendToSelect(order_status_obj, '<?= $orderStatus[$count]["id"] ?>', '<?= $orderStatus[$count]["text"] ?>');
        <? } ?>
                } else if (filename_select_obj == '<?= FILENAME_PROGRESS_REPORT ?>') {
                    document.getElementById('order_status_div').className = 'show';
                    document.getElementById('order_status_title_div').className = 'show';
                    appendToSelect(order_status_obj, 'all', '<?= TEXT_ALL_ORDER_STATUS ?>');
        <?
        $orderStatus = tep_get_supplier_tasks_status();
        for ($count = 0; $count < count($orderStatus); $count++) {
            ?>
                        appendToSelect(order_status_obj, '<?= $orderStatus[$count]["id"] ?>', '<?= $orderStatus[$count]["text"] ?>');
        <? } ?>
                } else if (filename_select_obj == '<?= FILENAME_BUYBACK_REQUESTS ?>') {
                    document.getElementById('order_status_div').className = 'show';
                    document.getElementById('order_status_title_div').className = 'show';
                    appendToSelect(order_status_obj, 'all', '<?= TEXT_ALL_ORDER_STATUS ?>');
        <?
        $orderStatus = tep_get_buyback_status();
        for ($count = 0; $count < count($orderStatus); $count++) {
            ?>
                        appendToSelect(order_status_obj, '<?= $orderStatus[$count]["id"] ?>', '<?= $orderStatus[$count]["text"] ?>');
        <? } ?>
                } else if (filename_select_obj == '<?= FILENAME_PRODUCTS_LOW_STOCK ?>') {
                    document.getElementById('order_status_div').className = 'show';
                    document.getElementById('order_status_title_div').className = 'show';
                    appendToSelect(order_status_obj, 'all', '<?= TEXT_ALL_ORDER_STATUS ?>');
        <?
        $orderStatus = tep_get_products_low_stock_status();
        for ($count = 0; $count < count($orderStatus); $count++) {
            ?>
                        appendToSelect(order_status_obj, '<?= $orderStatus[$count]["id"] ?>', '<?= $orderStatus[$count]["text"] ?>');
        <? } ?>
                } else if (filename_select_obj == '<?= FILENAME_EDIT_PURCHASE_ORDERS ?>') {
                    document.getElementById('order_status_div').className = 'show';
                    document.getElementById('order_status_title_div').className = 'show';
                    appendToSelect(order_status_obj, 'all', '<?= TEXT_ALL_ORDER_STATUS ?>');
        <?
        $orderStatus = tep_get_purchase_orders_status();
        for ($count = 0; $count < count($orderStatus); $count++) {
            ?>
                        appendToSelect(order_status_obj, '<?= $orderStatus[$count]["id"] ?>', '<?= $orderStatus[$count]["text"] ?>');
        <? } ?>
                } else {
                    document.getElementById('order_status_div').className = 'hide';
                    document.getElementById('order_status_title_div').className = 'hide';
                }
            }
        </script>
        <?
        $order_tags_html = ob_get_contents();
        ob_end_clean();

        return $order_tags_html;
    }

    function order_tags_form($trans_array) {
        $selected_order_status = array();

        ob_start();
        echo tep_draw_form('order_tags', FILENAME_ORDERS_TAGS, tep_get_all_get_params(array('action')) . 'action=' . ($trans_array['action'] == "new_tags" ? 'insert_tags' : 'update_tags'), 'post', 'onSubmit="return order_tags_form_checking(this.filename.value);"');

        if ($trans_array["tagID"]) {
            $order_tag_select_sql = "SELECT * FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . $trans_array["tagID"] . "'";
            $order_tag_result_sql = tep_db_query($order_tag_select_sql);
            $order_tag_row = tep_db_fetch_array($order_tag_result_sql);
            $selected_order_status = explode(',', $order_tag_row["orders_tag_status_ids"]);

            echo tep_draw_hidden_field("tagID", $trans_array["tagID"]);
        }

        echo tep_draw_hidden_field('from_action', $trans_array['action']);
        ?>
        <tr>
            <td width="100%">
                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                    <tr>
                        <td class="main"><?= ENTRY_FILENAME ?></td>
                        <td class="main"><?= tep_draw_pull_down_menu('filename', $this->filename_array, $order_tag_row["filename"], 'onChange="refreshOrderStatus(this);"') ?></td>
                    </tr>
                    <tr>
                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td class="main" width="15%" valign="top"><?= ENTRY_TAG_NAME ?></td>
                        <td class="main" valign="top"><?= tep_draw_input_field('tag_name', $order_tag_row["orders_tag_name"], 'size="40" id="tag_name"') ?></td>
                    </tr>
                    <tr>
                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td class="main"><?= ENTRY_ORDER_STATUS ?></td>
                        <td class="main" id="order_status_checkbox">
                            <span id="order_status_span" class="<?= (tep_not_null($order_tag_row['filename']) ? (($order_tag_row['filename'] == FILENAME_STATS_ORDERS_TRACKING) ? show : hide) : (($trans_array['filename'] == FILENAME_STATS_ORDERS_TRACKING) ? show : hide)) ?>" >
                                <?
                                foreach ($this->status_options as $id => $title) {
                                    echo tep_draw_checkbox_field('order_status[' . FILENAME_STATS_ORDERS_TRACKING . '][]', $id, (!$error) ? ($order_tag_row['filename'] == FILENAME_STATS_ORDERS_TRACKING) ? (in_array($id, $selected_order_status) ? true : false) : (is_array($submitted_order_status) && in_array($id, $submitted_order_status) ? true : false)  : false) . "&nbsp;" . $title . "&nbsp;";
                                }
                                ?>
                            </span>
                            <span id="task_status_span" class="<?= (tep_not_null($order_tag_row['filename']) ? (($order_tag_row['filename'] == FILENAME_PROGRESS_REPORT) ? show : hide) : (($trans_array['filename'] == FILENAME_PROGRESS_REPORT) ? show : hide)) ?>" >
                                <?
                                foreach ($this->supplier_status_options as $id => $title) {
                                    echo tep_draw_checkbox_field('order_status[' . FILENAME_PROGRESS_REPORT . '][]', $id, (!$error) ? ($order_tag_row['filename'] == FILENAME_PROGRESS_REPORT) ? (in_array($id, $selected_order_status) ? true : false) : (is_array($submitted_order_status) && in_array($id, $submitted_order_status) ? true : false)  : false) . "&nbsp;" . $title . "&nbsp;";
                                }
                                ?>
                            </span>
                            <span id="buyback_status_span" class="<?= (tep_not_null($order_tag_row['filename']) ? (($order_tag_row['filename'] == FILENAME_BUYBACK_REQUESTS) ? show : hide) : (($trans_array['filename'] == FILENAME_BUYBACK_REQUESTS || $trans_array['filename'] == '') ? show : hide)) ?>" >
                                <?
                                foreach ($this->buyback_status_options as $id => $title) {
                                    echo tep_draw_checkbox_field('order_status[' . FILENAME_BUYBACK_REQUESTS . '][]', $id, (!$error) ? ($order_tag_row['filename'] == FILENAME_BUYBACK_REQUESTS) ? (in_array($id, $selected_order_status) ? true : false) : (is_array($submitted_order_status) && in_array($id, $submitted_order_status) ? true : false)  : false) . "&nbsp;" . $title . "&nbsp;";
                                }
                                ?>
                            </span>
                            <span id="lowstock_status_span" class="<?= (tep_not_null($order_tag_row['filename']) ? (($order_tag_row['filename'] == FILENAME_PRODUCTS_LOW_STOCK) ? show : hide) : (($trans_array['filename'] == FILENAME_PRODUCTS_LOW_STOCK) ? show : hide)) ?>" >
                                <?
                                foreach ($this->lowstock_status_options as $id => $title) {
                                    echo tep_draw_checkbox_field('order_status[' . FILENAME_PRODUCTS_LOW_STOCK . '][]', $id, (!$error) ? ($order_tag_row['filename'] == FILENAME_PRODUCTS_LOW_STOCK) ? (in_array($id, $selected_order_status) ? true : false) : (is_array($submitted_order_status) && in_array($id, $submitted_order_status) ? true : false)  : false) . "&nbsp;" . $title . "&nbsp;";
                                }
                                ?>
                            </span>
                            <span id="po_status_span" class="<?= (tep_not_null($order_tag_row['filename']) ? (($order_tag_row['filename'] == FILENAME_EDIT_PURCHASE_ORDERS) ? show : hide) : (($trans_array['filename'] == FILENAME_EDIT_PURCHASE_ORDERS) ? show : hide)) ?>" >
                                <?
                                foreach ($this->po_status_options as $id => $title) {
                                    echo tep_draw_checkbox_field('order_status[' . FILENAME_EDIT_PURCHASE_ORDERS . '][]', $id, (!$error) ? ($order_tag_row['filename'] == FILENAME_EDIT_PURCHASE_ORDERS) ? (in_array($id, $selected_order_status) ? true : false) : (is_array($submitted_order_status) && in_array($id, $submitted_order_status) ? true : false)  : false) . "&nbsp;" . $title . "&nbsp;";
                                }
                                ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                    </tr>
                    <tr>
                        <td colspan="2" align="right">
                            <?= ($trans_array['action'] == "new_tags" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_ORDERS_TAGS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>' ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        </form>
        <script type="text/javascript">
            function order_tags_form_checking(filenames) {
                if (trim_str(document.getElementById('tag_name').value) == "") {
                    alert('Please enter the name for this tag!');
                    document.getElementById('tag_name').value = '';
                    document.getElementById('tag_name').focus();
                    return false;
                }

                var at_least_one_status = jQuery("#order_status_checkbox [name='order_status[" + filenames + "][]']").is(":checked");

                if (!at_least_one_status) {
                    alert('Please select at least one order status for this tag!');
                    return false;
                }

                return true;
            }
        </script>
        <?
        $order_tags_form_html = ob_get_contents();
        ob_end_clean();

        return $order_tags_form_html;
    }

    function show_order_tags($trans_array) {
        $order_tags_list_html = '';

        $where_str = " 1 ";
        if (tep_not_null($_SESSION["tag_order_status_search"]) && (int) $_SESSION["tag_order_status_search"] >= 0 && $_SESSION["tag_order_status_search"] != 'all') {
            $where_str = " FIND_IN_SET('" . $_SESSION["tag_order_status_search"] . "', orders_tag_status_ids) ";
        }

        if (tep_not_null($_SESSION["tag_filename_search"]) && $_SESSION["tag_filename_search"] != 'all') {
            $where_str .= " AND filename ='" . tep_db_input($_SESSION["tag_filename_search"]) . "'";
        }

        $order_tags_select_sql = "select * from " . TABLE_ORDERS_TAG . " where " . $where_str . " order by filename";
        $page_object = new splitPageResults($trans_array['page'], MAX_DISPLAY_SEARCH_RESULTS, $order_tags_select_sql, $order_tags_select_sql_numrows);
        $order_tags_result_sql = tep_db_query($order_tags_select_sql);

        ob_start();
        ?>
        <tr>
            <td valign="top">
                <table border="0" width="55%" cellspacing="1" cellpadding="2">
                    <tr>
                        <td class="reportBoxHeading" width="5%"><?= TABLE_HEADING_ORDER_TAG_ACTION ?></td>
                        <td class="reportBoxHeading"><?= TABLE_HEADING_ORDER_TAG_NAME ?></td>
                        <td class="reportBoxHeading"><?= TABLE_HEADING_ORDER_STATUS ?></td>
                        <td class="reportBoxHeading"><?= TABLE_HEADING_FILENAME ?></td>
                    </tr>
                    <?
                    if (tep_db_num_rows($order_tags_result_sql)) {
                        $row_count = 0;
                        while ($order_tags_row = tep_db_fetch_array($order_tags_result_sql)) {
                            $tag_filename = '';
                            foreach ($this->filename_array as $tag_array) {
                                if ($order_tags_row["filename"] == $tag_array['id']) {
                                    $tag_filename = $tag_array['text'];
                                }
                            }

                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                            ?>
                            <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                <td class="reportRecords" valign="top">
                                    <a href="<?= tep_href_link(FILENAME_ORDERS_TAGS, 'action=edit_tags&tagID=' . $order_tags_row["orders_tag_id"]) ?>"><?= tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') ?></a>
                                    <a href="javascript:void(confirm_delete('', 'this order tag', '<?= tep_href_link(FILENAME_ORDERS_TAGS, 'action=delete_tags&tagID=' . $order_tags_row["orders_tag_id"]) ?>'))"><?= tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') ?></a>
                                </td>
                                <td class="reportRecords" valign="top"><?= $order_tags_row["orders_tag_name"] ?></td>
                                <td class="reportRecords" valign="top">
                                    <?
                                    $order_status_str = '';

                                    if ($order_tags_row["filename"] == FILENAME_STATS_ORDERS_TRACKING) {
                                        $order_status_select_sql = "SELECT orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE FIND_IN_SET(orders_status_id, '" . $order_tags_row["orders_tag_status_ids"] . "') AND language_id ='" . (int) $_SESSION['languages_id'] . "'";
                                        $order_status_result_sql = tep_db_query($order_status_select_sql);
                                        while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
                                            $order_status_str .= $order_status_row["orders_status_name"] . ', ';
                                        }
                                    } else if ($order_tags_row["filename"] == FILENAME_PROGRESS_REPORT) {
                                        $supplier_tasks_status_select_sql = "SELECT supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE FIND_IN_SET(supplier_tasks_status_id, '" . $order_tags_row["orders_tag_status_ids"] . "') AND language_id ='" . (int) $_SESSION['languages_id'] . "'";
                                        $supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
                                        while ($supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql)) {
                                            $order_status_str .= $supplier_tasks_status_row["supplier_tasks_status_name"] . ', ';
                                        }
                                    } else if ($order_tags_row["filename"] == FILENAME_BUYBACK_REQUESTS) {
                                        $buyback_status_select_sql = "SELECT buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE FIND_IN_SET(buyback_status_id, '" . $order_tags_row["orders_tag_status_ids"] . "') AND language_id ='" . (int) $_SESSION['languages_id'] . "'";
                                        $buyback_status_result_sql = tep_db_query($buyback_status_select_sql);
                                        while ($buyback_status_row = tep_db_fetch_array($buyback_status_result_sql)) {
                                            $order_status_str .= $buyback_status_row["buyback_status_name"] . ', ';
                                        }
                                    } else if ($order_tags_row["filename"] == FILENAME_PRODUCTS_LOW_STOCK) {
                                        $order_status_str = 'Pending';
                                    } else if ($order_tags_row["filename"] == FILENAME_EDIT_PURCHASE_ORDERS) {
                                        $po_status_select_sql = "SELECT purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE FIND_IN_SET(purchase_orders_status_id, '" . $order_tags_row["orders_tag_status_ids"] . "') AND language_id ='" . (int) $_SESSION['languages_id'] . "'";
                                        $po_status_result_sql = tep_db_query($po_status_select_sql);
                                        while ($po_status_row = tep_db_fetch_array($po_status_result_sql)) {
                                            $order_status_str .= $po_status_row["purchase_orders_status_name"] . ', ';
                                        }
                                    }
                                    if (substr($order_status_str, -2) == ', ')
                                        $order_status_str = substr($order_status_str, 0, -2);
                                    echo $order_status_str;
                                    ?>
                                </td>
                                <td class="reportRecords" valign="top"><?= $tag_filename ?></td>
                            </tr>
                            <?
                            $row_count++;
                        }
                    }
                    ?>
                    <tr>
                        <td colspan="4">
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="smallText" valign="top"><?= $page_object->display_count($order_tags_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $trans_array['page'], TEXT_DISPLAY_NUMBER_OF_ORDERS_TAGS) ?></td>
                                    <td class="smallText" align="right"><?= $page_object->display_links($order_tags_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $trans_array['page'], tep_get_all_get_params(array('page')), 'page') ?></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <?
        $order_tags_list_html = ob_get_contents();
        ob_end_clean();

        return $order_tags_list_html;
    }

}
?>