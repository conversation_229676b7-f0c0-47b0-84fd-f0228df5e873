<?
/*
  $Id: orders_matching.php,v 1.29 2015/03/30 09:37:17 weesiong Exp $

  Copyright (c) 2007 Dynamic Podium

  Released under the GNU General Public License
 */

class orders_matching {

    var $identity, $identity_email;
    var $orders_status, $orders_status_from_array, $orders_status_to_array;
    var $csv_file_data;
    var $crew_database_data;
    var $combined_data;
    var $order_amounts_data;
    var $payment_method;
    var $store_start_date;
    var $store_end_date;
    var $payment_gateways_id, $payment_methods_id;
    var $report_type_id;
    var $orders_total_summary = array('ot_subtotal' => 0, 'ot_surcharge' => 0, 'ot_gv' => 0, 'ot_coupon' => 0, 'ot_total' => 0);
    var $report_type_values = array(
        array('id' => '1', 'text' => 'Order Matching'),
        array('id' => '2', 'text' => 'Order Delivery Matching')
    );

    // private $aws_obj;

    // class constructor
    function orders_matching($identity, $identity_email) {
        global $login_groups_id;
        // global $aws_obj;

        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user
        $this->csv_file_data = array();
        $this->crew_database_data = array();
        $this->combined_data = array();
        $this->order_amounts_data = array();

        // AWS S3
        // if(!is_object($aws_obj)) {
        //     require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

        //     $this->aws_obj = new ogm_amazon_ws();
        // } else {
        //     $this->aws_obj = $aws_obj;
        // }
        
        // $this->aws_obj->set_bucket_key('BUCKET_SECURE');
        // $this->aws_obj->set_storage('STORAGE_STANDARD');
    }

    function search_orders_matching($filename, $session_name) {
        $orders_matching_html = '';

        ob_start();
        ?>
        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="20">&nbsp;</td>
                            <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                        </tr>
                        <tr>
                            <td width="20">&nbsp;</td>
                            <td>
                                <?= tep_draw_form('orders_matching_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', 'enctype="multipart/form-data"') ?>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main"><?= ENTRY_ORDERS_MATCHING_REPORT_TYPE ?></td>
                                        <td colspan="4" class="main"><?= tep_draw_pull_down_menu('report_type', $this->report_type_values, $this->report_type_id) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?= ENTRY_CSV_SOURCE_FILE ?></td>
                                        <td colspan="4" class="main"><?= tep_draw_file_field('csv_import', 'size="50"') ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><?= ENTRY_ORDERS_MATCHING_START_LAST_VERIFIED_DATE ?></td>
                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('start_date', $_SESSION[$session_name]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.orders_matching_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.orders_matching_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                        <td class="main" width="8%">&nbsp;</td>
                                        <td class="main" valign="top" nowrap><?= ENTRY_ORDERS_MATCHING_END_LAST_VERIFIED_DATE ?></td>
                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('end_date', $_SESSION[$session_name]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.orders_matching_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.orders_matching_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"></td>
                                    </tr>
                                    <tr>
                                        <td valign="top" class="main"><?= ENTRY_ORDERS_MATCHING_PAYMENT_GATEWAY ?></td>
                                        <td colspan="4" class="main">
                                            <?
                                            $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id 
									FROM " . TABLE_PAYMENT_METHODS . "
									WHERE payment_methods_receive_status = '1' 
									ORDER BY payment_methods_sort_order";
                                            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                                            $payment_methods_array = array();
                                            $payment_gateways_array = array();
                                            while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                                                if ($payment_methods_row['payment_methods_parent_id'] > 0) {
                                                    $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                                } else {
                                                    $payment_gateways_array[$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                                }
                                            }
                                            ?>
                                            <ul class="myTree">
                                                <?
                                                foreach ($payment_gateways_array as $payment_gateways_id => $payment_gateways_title) {
                                                    ?>
                                                    <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_gateways_id ?>">
                                                        <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                        <span class="textHolder payment_gateways">
                                                            <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>">
                                                            <?= $payment_gateways_title ?>
                                                        </span>
                                                        <?
                                                        if (isset($payment_methods_array[$payment_gateways_id]) && count($payment_methods_array[$payment_gateways_id])) {
                                                            ?>
                                                            <ul style="display: none;">
                                                                <?
                                                                $pg_pm_id = implode(',', array_keys($payment_methods_array[$payment_gateways_id]));
                                                                asort($payment_methods_array[$payment_gateways_id]);
                                                                foreach ($payment_methods_array[$payment_gateways_id] as $payment_methods_id => $payment_methods_title) {
                                                                    ?>
                                                                    <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_methods_id ?>">&nbsp;&nbsp;
                                                                        <span class="textHolder payment_methods">
                                                                            <input type="checkbox" name="payment_methods_id[]" value="<?= $payment_methods_id ?>">
                                                                            <?= $payment_methods_title ?>
                                                                        </span>
                                                                    </li>
                                                                    <script type="text/javascript">
                                                                        jQuery("li#<?= $payment_methods_id ?> span.payment_methods input[type='checkbox']").click(function () {
                                                                            var count_unchecked = 0;
                                                                            const pg_pm_id = [<?=$pg_pm_id?>];
                                                                            
                                                                            pg_pm_id.forEach(element => {
                                                                                if (jQuery("li#<?= $payment_gateways_id ?> li#"+element+" span.payment_methods input[type='checkbox']").attr("checked") === false) {
                                                                                    count_unchecked++;
                                                                                }
                                                                            });
                                                                            
                                                                            if (count_unchecked == 0) {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                                                            } else {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", '');
                                                                            }
                                                                        });
                                                                    </script>
                                                                    <?
                                                                }
                                                                ?>
                                                            </ul>
                                                            <?
                                                        }
                                                        ?>
                                                    </li>
                                                    <script>
                                                        jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").click(function () {
                                                            if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                                            } else {
                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                                            }
                                                        });

                                                        jQuery(document).ready(function () {
                                                            if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                                            } else {
                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                                            }
                                                        });

                                                    </script>
                                                    <?
                                                }
                                                ?>	
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5" align="right">
        <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking();"', 'inputButton') ?>
                                            <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                        </td>
                                    </tr>
                                </table>
                                </form>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <script language="javascript">
            <!--
        jQuery(document).ready(function () {
                tree = jQuery('#myTree');
                jQuery('img.expandImage', tree.get(0)).click(
                        function () {
                            if (this.src.indexOf('spacer') == -1) {
                                subbranch = jQuery('ul', this.parentNode).eq(0);
                                if (subbranch.css('display') == 'none') {
                                    subbranch.show();
                                    this.src = 'images/icon-collapse-small.gif';
                                } else {
                                    subbranch.hide();
                                    this.src = 'images/icon-expand-small.gif';
                                }
                            }
                        }
                );
            });

            function select_box_data_send(form_name, select_box_name) {
                var select_box_object = document.forms[form_name].elements[select_box_name];
                if (select_box_object != null) {
                    for (x = 0; x < (select_box_object.length); x++) {
                        select_box_object.options[x].selected = true;
                    } //end for
                } //end if
            } //end function

            function form_checking() {
                var form_valid = true;

                if (form_valid
                        && document.orders_matching_criteria.csv_import.value.length < 1) {
                    form_valid = false;
                }

                if (form_valid
                        && (document.orders_matching_criteria.start_date.value.length < 1
                                || document.orders_matching_criteria.end_date.value.length < 1)) {
                    form_valid = false;
                }

                if (!form_valid) {
                    alert('<?= JS_ERROR_ORDERS_MATCHING_SEARCH_CRITERIA_NOT_ENTERED ?>');
                } else {
                    select_box_data_send('orders_matching_criteria', 'payment_gateways_to[]');
                }

                return form_valid;
            }
            //-->
        </script>
        <?
        $orders_matching_html = ob_get_contents();
        ob_end_clean();

        return $orders_matching_html;
    }

    function show_activity_matching($filename, $session_name, $input_array, &$messageStack) {
        global $currencies, $languages_id;
        
        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["report_type"] = $input_array["report_type"];
            $_SESSION[$session_name]["start_date"] = $input_array["start_date"];
            $_SESSION[$session_name]["end_date"] = $input_array["end_date"];
            $_SESSION[$session_name]["payment_gateways_id"] = isset($input_array["payment_gateways_id"]) ? $input_array["payment_gateways_id"] : array();
            $_SESSION[$session_name]["payment_methods_id"] = isset($input_array["payment_methods_id"]) ? $input_array["payment_methods_id"] : array();
        }

        $this->payment_gateways_id = $_SESSION[$session_name]["payment_gateways_id"];
        $this->payment_methods_id = $_SESSION[$session_name]["payment_methods_id"];
        $this->report_type_id = $input_array["report_type"];

        if (!isset($_SESSION[$session_name]["combined_data"])) {
            // Prepare temp files
            $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
            $temp_target = 'download/' . $temp_filename;
            // $temp_target = 'download/orders_matching/';
            // Do checking if temp file exist for this user?
            // $aws_obj_status = $aws_obj->get_file($temp_filename, $temp_target);
            // if($aws_obj_status->isOK()) {
            if(file_exists($temp_target)) {
                // removed prev file
                unlink($temp_target);
                // $aws_status = $aws_obj->delete_file($temp_filename, $temp_target);
            }
            // Store file upload temp
            move_uploaded_file($_FILES['csv_import']['tmp_name'], $temp_target);
            // $this->aws_obj->set_filename($temp_filename);
            // $this->aws_obj->set_filepath($temp_target);
            // $this->aws_obj->set_file($_FILES['csv_import']);
            // $this->aws_obj->set_headers(
            //     array(
            //         'Cache-Control' => 'max-age=' . $aws_obj->default_cache_control_max_age,
            //         'Expires'       => gmdate('D, d M Y H:i:s \G\M\T', strtotime('+ 24 hours')),
            //     )
            // );
            // $this->aws_obj->save_file();

            // Store upload file settings
            $_FILES['csv_import']['name'] = $temp_filename;
            $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
            // $_FILES['csv_import']['tmp_name'] = $this->aws_obj->get_object_url($this->aws_obj->get_aws_bucket(), $temp_target.$temp_filename);
            $_SESSION[$session_name]["combined_data"] = $_FILES['csv_import'];
        } else {
            if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name'])) {
                // Prepare temp files
                $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
                $temp_target = 'download/' . $temp_filename;
                // $temp_target = 'download/orders_matching/';
                // Do checking if temp file exist for this user?
                // $aws_obj_status = $aws_obj->get_file($temp_filename, $temp_target);
                // if($aws_obj_status->isOK()) {
                if(file_exists($temp_target)) {
                    // removed prev file
                    unlink($temp_target);
                    // $aws_status = $aws_obj->delete_file($temp_filename, $temp_target);
                }
                // Store file upload temp
                move_uploaded_file($_FILES['csv_import']['tmp_name'], $temp_target);
                // $this->aws_obj->set_filename($temp_filename);
                // $this->aws_obj->set_filepath($temp_target);
                // $this->aws_obj->set_file($_FILES['csv_import']);
                // $this->aws_obj->set_headers(
                //     array(
                //         'Cache-Control' => 'max-age=' . $aws_obj->default_cache_control_max_age,
                //         'Expires'       => gmdate('D, d M Y H:i:s \G\M\T', strtotime('+ 24 hours')),
                //     )
                // );
                // $this->aws_obj->save_file();

                // Store upload file settings
                $_FILES['csv_import']['name'] = $temp_filename;
                $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
                // $_FILES['csv_import']['tmp_name'] = $this->aws_obj->get_object_url($this->aws_obj->get_aws_bucket(), $temp_target.$temp_filename);
                $_SESSION[$session_name]["combined_data"] = $_FILES['csv_import'];
            } else {
                $_FILES['csv_import'] = $_SESSION[$session_name]["combined_data"];
                // Prepare temp files
                $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
                $temp_target = 'download/' . $temp_filename;
                // $temp_target = 'download/orders_matching/';
                $_FILES['csv_import']['name'] = $temp_filename;
                $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
                // $_FILES['csv_import']['tmp_name'] = $this->aws_obj->get_object_url($this->aws_obj->get_aws_bucket(), $temp_target.$temp_filename);
            }
        }

        // if (!isset($_SESSION[$session_name]["combined_data"])) {
        //     $this->load_csv_file_data($input_array, $messageStack);
        //     $this->load_crew_database_activities_data($input_array);
        //     $this->compare_activities_data();
        //     $_SESSION[$session_name]["combined_data"] = $this->combined_data;
        // } else {
        //     if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name'])) {
        //         $this->load_csv_file_data($input_array, $messageStack);
        //         $this->load_crew_database_activities_data($input_array);
        //         $this->compare_activities_data();
        //         $_SESSION[$session_name]["combined_data"] = $this->combined_data;
        //     } else {
        //         $this->combined_data = $_SESSION[$session_name]["combined_data"];
        //     }
        // }
        
        $this->load_csv_file_data($input_array, $messageStack);
        $this->load_crew_database_activities_data($input_array);
        $this->compare_activities_data();

        $paging_array_data = $this->paging_array_data($this->combined_data, 500, $input_array);

        $csv_filename = $this->_generate_activities_csv_file();

        ob_start();
        ?>
        <br>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr height="20">
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_DATE ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_ID ?></td>
                            <td colspan="3" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_PAYMENT_GATEWAY ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_PG_SURCHARGE ?></td>
                            <td colspan="3" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_DELIVERIES ?></td>
                            <td rowspan="2" width="50px" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_PREVIOUS_DELIVERIES ?></td>
                            <td colspan="3" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVERIES ?></td>
                            <td colspan="3" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_REFUND ?></td>
                            <td colspan="3" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CHARGE_BACK ?></td>
                            <td rowspan="2" width="50px" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_TOTAL ?></td>
                            <td rowspan="2" width="1px" class="reportBoxHeading"></td>
                            <td colspan="8" align="center" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_INFO ?></td>
                            <td rowspan="2" width="1px" class="reportBoxHeading"></td>
                            <td rowspan="2" width="50px" class="reportBoxHeading"><?= TABLE_HEADING_ACTIVITIES_MATCHING_COMPENSATED_DELIVERIES ?></td>
                        </tr>
                        <tr>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_PG_CODE ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENCY ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_PG_AMOUNT ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_PG ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_SC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_DC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_PG ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_SC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_DC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_PG ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_SC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_DC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CB_RESOLVE ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CB_WIN ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CB_LOST ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_PG_CODE ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_USD_RATE ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_CURRENCY ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_TOTAL ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_SURCHARGE ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_GV ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_DC ?></td>
                            <td width="50px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ACTIVITIES_MATCHING_OT_SUBTOTAL ?></td>
                        </tr>
                        <?
                        $row_count = 0;
                        foreach ($paging_array_data['display_data'] as $orders_id => $orders_details) {
                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                            $orders_id_str = $orders_id;
                            $orders_date_str = $orders_details['order_date'];
                            $pg_code_str = $orders_details['pg_code'];
                            $currency_str = $orders_details['currency'];
                            $pg_amount_str = $orders_details['pg_amount'];
                            $pg_surcharge_str = $orders_details['pg_surcharge'];
                            $order_usd_rate_str = $orders_details['usd_rate'];

                            $current_deliver_pg_amt_str = $orders_details['current_deliver_pg_amt'];
                            $current_deliver_sc_amt_str = $orders_details['current_deliver_sc_amt'];
                            $current_deliver_dc_amt_str = $orders_details['current_deliver_dc_amt'];
                            $previous_deliver_amt_str = $orders_details['previous_deliver_amt'];
                            $balance_deliver_pg_amt_str = $orders_details['balance_deliver_pg_amt'];
                            $balance_deliver_sc_amt_str = $orders_details['balance_deliver_sc_amt'];
                            $balance_deliver_dc_amt_str = $orders_details['balance_deliver_dc_amt'];

                            $current_refund_pg_amt_str = $orders_details['current_refund_pg_amt'];
                            $current_refund_sc_amt_str = $orders_details['current_refund_sc_amt'];
                            $current_refund_dc_amt_str = $orders_details['current_refund_dc_amt'];
                            $current_cb_resolve_amt_str = $orders_details['current_cb_resolve_amt'];
                            $current_cb_win_amt_str = $orders_details['current_cb_win_amt'];
                            $current_cb_lost_amt_str = $orders_details['current_cb_lost_amt'];
                            $database_total_str = $orders_details['database_total'];

                            $order_pg_code_str = $orders_details['order_pg_code'];
                            $order_currency_str = $orders_details['order_currency'];
                            $csv_file_pg_amt_str = $orders_details['csv_file_pg_amt'];
                            $csv_file_pg_surcharge_amt_str = $orders_details['csv_file_pg_surcharge_amt'];
                            $csv_file_sc_amt_str = $orders_details['csv_file_sc_amt'];
                            $csv_file_dc_amt_str = $orders_details['csv_file_dc_amt'];
                            $csv_file_total_str = $orders_details['csv_file_total'];
                            $compensated_total_str = $orders_details['compensated_amt'];
                            ?>
                            <tr height="20" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                <td valign="top" class="reportRecords" nowrap><?= $orders_date_str ?></td>
                                <td valign="top" align="left" class="reportRecords" nowrap>
                            <?
                            if (tep_not_null($orders_details['crew_database_orders_id']) > 0) {
                            ?>
                                    <a href="<?= tep_href_link("orders.php", 'oID=' . $orders_id_str . '&action=edit', 'NONSSL') ?>" target="_new">
                                        <?= $orders_id_str ?>
                                    </a>
                            <?
                            } else {
                                echo $orders_id_str;
                            }
                            if ($this->get_refund_as_store_credit($orders_id) > 0) {
                                echo '<span class="redIndicator"> *</span>';
                            }
                            ?>
                                </td>
                                <td valign="top" class="reportRecords"><?= $pg_code_str ?></td>
                                <td valign="top" class="reportRecords"><?= $currency_str ?></td>
                                <td valign="top" class="reportRecords"><?= $pg_amount_str ?></td>
                                <td valign="top" class="reportRecords"><?= $pg_surcharge_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_deliver_pg_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_deliver_sc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_deliver_dc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $previous_deliver_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $balance_deliver_pg_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $balance_deliver_sc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $balance_deliver_dc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_refund_pg_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_refund_sc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_refund_dc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_cb_resolve_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_cb_win_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $current_cb_lost_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $database_total_str ?></td>
                                <td valign="top" class="reportRecords"></td>
                                <td valign="top" class="reportRecords"><?= $order_pg_code_str ?></td>
                                <td valign="top" class="reportRecords"><?= $order_usd_rate_str ?></td>
                                <td valign="top" class="reportRecords"><?= $order_currency_str ?></td>
                                <td valign="top" class="reportRecords"><?= $csv_file_pg_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $csv_file_pg_surcharge_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $csv_file_sc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $csv_file_dc_amt_str ?></td>
                                <td valign="top" class="reportRecords"><?= $csv_file_total_str ?></td>
                                <td valign="top" class="reportRecords"></td>
                                <td valign="top" class="reportRecords"><?= $compensated_total_str ?></td>
                            </tr>
                            <?
                            $row_count++;
                        }
                        ?>
                    </table>
                </td>
            </tr>
            <tr>
                <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>
            <tr>
                <td><?= $paging_array_data['display_info'] ?></td>
            </tr>
            <tr>
                <td>
                    <?
                    echo tep_draw_form('export_csv_form', FILENAME_ORDERS_MATCHING, tep_get_all_get_params(array('action')) . 'action=export_csv', 'post', '');
                    echo tep_draw_hidden_field("filename", $csv_filename) . "\n";
                    ?>
                    <table width="100%" border="0" cellspacing="0" cellpadding="2">
                        <tr>
                            <td align="right">
                                <? echo tep_submit_button('Export All', 'Export as csv file', 'name="btn_export_csv"', 'inputButton'); ?>
                            </td>
                        </tr>
                    </table>
                    </form>
                </td>
            </tr>
        </table>
        <?
        $report_section_html = ob_get_contents();
        ob_end_clean();

        // This should be here to let all the session get updated value
        $search_section_html = $this->search_orders_matching($filename, $session_name);

        return $search_section_html . "\n" . $report_section_html;
    }

    function load_gateway_csv_file_data($input_array, &$messageStack) {
        if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name'])) {
            if ($_FILES['csv_import']["size"] > 0) {
                list($start_date_year, $start_date_month, $start_date_day) = explode('-', trim($input_array["start_date"]));
                $start_date_timestamp = mktime(0, 0, 0, $start_date_month, $start_date_day, $start_date_year);

                list($end_date_year, $end_date_month, $end_date_day) = explode('-', trim($input_array["end_date"]));
                $end_date_timestamp = mktime(0, 0, 0, $end_date_month, $end_date_day, $end_date_year);

                $filename = ($_FILES['csv_import']['tmp_name']);
                $handle = fopen($filename, 'r+');

                while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
                    if (trim($data[0]) == 'Date') {
                        break;
                    }
                }

                while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
                    if (!tep_not_null($data[0])) {
                        continue;
                    }

                    $record_date = trim($data[0]);
                    $orders_id = trim($data[1]);
                    $order_amount_received = (float) trim($data[2]);
                    $pg_code = trim($data[3]);
                    $currency = trim($data[4]);
                    $record_status = trim($data[5]);

                    $this->csv_file_data[$orders_id] = array('orders_id' => $orders_id,
                        'record_date' => $record_date,
                        'order_amount_received' => number_format($order_amount_received, 4, '.', ''),
                        'payment_method' => str_replace(" ", "", strtolower($pg_code)),
                        'currency' => $currency,
                        'record_status' => $record_status
                    );
                }
                fclose($handle);
            } else {
                $messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
            }
        } else {
            $messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
        }
    }

    function load_crew_database_activities_data($input_array) {
        global $languages_id;
        $payment_method_where_str = ' 1 ';

        if (tep_not_null($input_array["start_date"])) {
            if (strpos($input_array["start_date"], ':') === false) {
                $input_array["start_date"] = $input_array["start_date"] . ' 00:00';
            }
            $startDateObj = explode(' ', trim($input_array["start_date"]));
            list($yr, $mth, $day) = explode('-', $startDateObj[0]);
            list($hr, $min) = explode(':', $startDateObj[1]);
            $this->store_start_date = date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr));
            $start_date_str = " ( sa.sales_activities_date >= '" . $this->store_start_date . "' )";
            $start_order_date_str = " ( o.date_purchased >= '" . $this->store_start_date . "' ) ";
        } else {
            $start_date_str = " 1 ";
            $start_order_date_str = " 1 ";
        }

        if (tep_not_null($input_array["end_date"])) {
            if (strpos($input_array["end_date"], ':') === FALSE) {
                $input_array["end_date"] = $input_array["end_date"] . ' 23:59';
            }
            $endDateObj = explode(' ', trim($input_array["end_date"]));
            list($yr, $mth, $day) = explode('-', $endDateObj[0]);
            list($hr, $min) = explode(':', $endDateObj[1]);
            $this->store_end_date = date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 59, $mth, $day, $yr));
            $end_date_str = " ( sa.sales_activities_date <= '" . $this->store_end_date . "' )";
            $end_order_date_str = " ( o.date_purchased <= '" . $this->store_end_date . "' ) ";
        } else {
            $end_date_str = " 1 ";
            $end_order_date_str = " 1 ";
        }

        $payment_gateways_where_str = 0;
        if (isset($input_array['payment_gateways_id']) && count($input_array['payment_gateways_id'])) {
            $payment_gateways_where_str = " o.payment_methods_parent_id IN ('" . implode("', '", $input_array["payment_gateways_id"]) . "')";
        }

        $payment_methods_where_str = 0;
        if (isset($input_array['payment_methods_id']) && count($input_array['payment_methods_id'])) {
            $payment_methods_where_str = " o.payment_methods_id IN ('" . implode("', '", $input_array["payment_methods_id"]) . "')";
        }

        // Start get all orders from within search criteria regardless of orders activities
        $all_orders_array = array();
        $all_orders_select_sql = "SELECT o.orders_id, o.date_purchased, o.currency, o.currency_value
									FROM " . TABLE_ORDERS . " AS o
									WHERE " . $start_order_date_str . "
									AND " . $end_order_date_str;
        if ($payment_gateways_where_str != '0' || $payment_methods_where_str != '0') {
            $all_orders_select_sql .= " AND (
							" . $payment_gateways_where_str . " 
								OR " . $payment_methods_where_str . " 
							)";
        }
        $all_orders_select_sql .= " ORDER BY o.orders_id";
        $all_orders_result_sql = tep_db_query($all_orders_select_sql, 'read_db_link');
        while ($all_orders_result_row = tep_db_fetch_array($all_orders_result_sql)) {
            $all_orders_array[$all_orders_result_row['orders_id']] = $all_orders_result_row;
        }
        unset($all_orders_result_row);
        // End Get all orders from within search criteria regardless of orders activities
        // Start get all orders activities within search criteria
        $current_select_sql = "	SELECT sa.sales_activities_id, sa.sales_activities_orders_id, sa.sales_activities_orders_products_id, DATE_FORMAT(sa.sales_activities_date, '%Y-%m-%d %H:%i') AS sales_activities_date, sa.sales_activities_code, sa.sales_activities_quantity, sa.sales_activities_operator, sa.sales_activities_amount, o.currency, o.currency_value, o.date_purchased
								FROM " . TABLE_SALES_ACTIVITIES . " AS sa
								LEFT JOIN " . TABLE_ORDERS . " AS o
									ON sa.sales_activities_orders_id = o.orders_id
								WHERE " . $start_date_str . "
									AND " . $end_date_str;
        if ($payment_gateways_where_str != '0' || $payment_methods_where_str != '0') {
            $current_select_sql .="	AND (
							" . $payment_gateways_where_str . " 
								OR " . $payment_methods_where_str . " 
							)";
        }
        $current_select_sql .=" ORDER BY sa.sales_activities_orders_id, sa.sales_activities_date";
        $current_result_sql = tep_db_query($current_select_sql, 'read_db_link');
        // End get all orders activities within search criteria

        $orders_info = array();
        $orders_amount_info = array();
        $current_info = array();
        $previous_info = array();

        $temp_order_id = 0;
        while ($current_result_row = tep_db_fetch_array($current_result_sql)) {
            // Get Order Info
            $orders_id = $current_result_row['sales_activities_orders_id'];
            if ($temp_order_id > 0 && $temp_order_id != $orders_id) {
                $this->crew_database_data[$temp_order_id] = $current_info;
                $current_info = array();
                $temp_order_id = $orders_id;
            } else {
                $temp_order_id = $orders_id;
            }
            $current_info[] = array('orders_id' => $orders_id,
                'order_date' => tep_date_short($current_result_row['date_purchased'], 'Y-m-d'),
                'sales_activities_id' => $current_result_row['sales_activities_id'],
                'sales_activities_orders_products_id' => $current_result_row['sales_activities_orders_products_id'],
                'sales_activities_date' => $current_result_row['sales_activities_date'],
                'sales_activities_code' => $current_result_row['sales_activities_code'],
                'sales_activities_quantity' => $current_result_row['sales_activities_quantity'],
                'sales_activities_operator' => $current_result_row['sales_activities_operator'],
                'sales_activities_amount' => $current_result_row['sales_activities_amount'],
                'currency' => $current_result_row['currency'],
                'currency_value' => $current_result_row['currency_value'],
            );
        }
        if (!isset($this->crew_database_data[$temp_order_id])) {
            $this->crew_database_data[$temp_order_id] = $current_info;
        }

        // compare both orders array with orders_activities array to find the differences
        // manually add in the missing orders into main orders array, to simulate undeliver amounts
        $no_activity_orders = array_diff_assoc($all_orders_array, $this->crew_database_data);
        if (count($no_activity_orders) > 0) {
            foreach ($no_activity_orders as $orders_id => $orders_array) {
                $this->crew_database_data[$orders_id] = array(
                    'orders_id' => $orders_array['orders_id'],
                    'order_date' => $orders_array['date_purchased'],
                    'sales_activities_id' => '',
                    'sales_activities_orders_products_id' => '',
                    'sales_activities_date' => '',
                    'sales_activities_code' => '',
                    'sales_activities_quantity' => '0',
                    'sales_activities_operator' => '+',
                    'sales_activities_amount' => '0',
                    'currency' => $orders_array['currency'],
                    'currency_value' => $orders_array['currency_value']
                );
            }
        }

        // Start find previous orders activities
        foreach ($this->crew_database_data as $orders_id => $orders_array) {
            $previous_select_sql = "SELECT sa.sales_activities_id, sa.sales_activities_orders_id, sa.sales_activities_orders_products_id, DATE_FORMAT(sa.sales_activities_date, '%Y-%m-%d %H:%i') AS sales_activities_date, sa.sales_activities_code, sa.sales_activities_quantity, sa.sales_activities_operator, sa.sales_activities_amount, o.currency, o.currency_value
									FROM " . TABLE_SALES_ACTIVITIES . " AS sa
									LEFT JOIN " . TABLE_ORDERS . " AS o
										ON sa.sales_activities_orders_id = o.orders_id
									WHERE ( sa.sales_activities_date < '" . $this->store_start_date . "' )
									AND sa.sales_activities_orders_id = '" . $orders_id . "'";
            $previous_result_sql = tep_db_query($previous_select_sql, 'read_db_link');
            $temp_array = array();
            while ($previous_result_row = tep_db_fetch_array($previous_result_sql)) {
                $temp_array[] = $previous_result_row;
            }
            $previous_info[$orders_id] = $temp_array;

            // get the current order's credits data			
            $orders_amounts = $this->get_order_amounts_usage($orders_id);
            $this->order_amounts_data[$orders_id]['order'] = $orders_amounts;
        }
        // End find previous orders activities
        // initialised various amounts working arrays
        $previous_amounts_array = $current_amounts_array = $balance_amounts_array = $compensated_amounts_array = array();
        foreach ($this->crew_database_data as $orders_id => $orders_array) {
            $previous_amounts_array[$orders_id] = array(
                'deliver' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'refund' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'chargeback' => array('resolve' => 0, 'win' => 0, 'loss' => 0),
                'total' => 0
            );
            $current_amounts_array[$orders_id] = array(
                'deliver' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'refund' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'chargeback' => array('resolve' => 0, 'win' => 0, 'loss' => 0),
                'total' => 0
            );
            $balance_amounts_array[$orders_id] = array(
                'deliver' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'refund' => array('payment_gateway' => 0, 'store_credit' => 0, 'discount_coupon' => 0),
                'chargeback' => array('resolve' => 0, 'win' => 0, 'loss' => 0),
                'total' => 0
            );
            $compensated_amounts_array[$orders_id] = array(
                'total' => 0
            );
        }

        // calculate and distribute previously delivered order amounts
        foreach ($previous_info as $orders_id => $previous_arrays) {
            $previous_total = 0;
            foreach ($previous_arrays as $index => $previous_array) {
                switch (trim($previous_array['sales_activities_code'])) {
                    case "D": // delivery +
                        $this_amount = $previous_array['sales_activities_amount'];
                        $activity_mode = "deliver";
                        $operator = "+";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $this_amount, $previous_total);
                        $previous_total = $previous_total + $this_amount;
                        break;
                    case "RD": // delivery -
                        $this_amount = $previous_array['sales_activities_amount'];
                        $previous_total = $previous_total - $this_amount;
                        $activity_mode = "deliver";
                        $operator = "-";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $this_amount, $previous_total);
                        break;
                    case "RFD": // refund +
                        $this_amount = $previous_array['sales_activities_amount'];
                        $activity_mode = "refund";
                        $operator = "+";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $this_amount, $previous_total);
                        $previous_total = $previous_total + $this_amount;
                        break;
                    case "RFRD": // refund -
                        $this_amount = $previous_array['sales_activities_amount'];
                        $previous_total = $previous_total - $this_amount;
                        $activity_mode = "refund";
                        $operator = "-";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $this_amount, $previous_total);
                        break;
                    case "RVD": // reverse +
                        $previous_total = $previous_total - $previous_array['sales_activities_amount'];
                        $activity_mode = "chargeback";
                        $operator = "+";
                        $cb_status = $this->get_order_chargeback_type($orders_id);
                        switch ($cb_status) {
                            case 1 : // chargeback win
                                $account_type = array(0 => array("type" => "win", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            case 2 : // chargeback loss
                                $account_type = array(0 => array("type" => "loss", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            case 3 : // chargeback resolved
                                $account_type = array(0 => array("type" => "resolve", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            default :
                                $account_type = array(0 => array("type" => "resolve", "amt" => $previous_array['sales_activities_amount']));
                                break;
                        }
                        break;
                    case "RVRD": // reverse -
                        $previous_total = $previous_total + $previous_array['sales_activities_amount'];
                        $activity_mode = "chargeback";
                        $operator = "-";
                        $cb_status = $this->get_order_chargeback_type($orders_id);
                        switch ($cb_status) {
                            case 1 : // chargeback win
                                $account_type = array(0 => array("type" => "win", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            case 2 : // chargeback loss
                                $account_type = array(0 => array("type" => "loss", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            case 3 : // chargeback resolved
                                $account_type = array(0 => array("type" => "resolve", "amt" => $previous_array['sales_activities_amount']));
                                break;
                            default :
                                $account_type = array(0 => array("type" => "resolve", "amt" => $previous_array['sales_activities_amount']));
                                break;
                        }
                        break;
                    case "CD": // compensation +
                        $compensated_amounts_array[$orders_id]['total'] = $compensated_amounts_array[$orders_id]['total'] + $previous_array['sales_activities_amount'];
                        break;
                    case "CRD": // compensation -
                        $compensated_amounts_array[$orders_id]['total'] = $compensated_amounts_array[$orders_id]['total'] - $previous_array['sales_activities_amount'];
                        break;
                    default:
                        break;
                }
                foreach ($account_type as $idx => $pair) {
                    $this_type = $pair['type'];
                    $this_value = $pair['amt'];
                    eval('$previous_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] = $previous_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] ' . $operator . ' ' . $this_value . ';');
                }
            }
            $previous_amounts_array[$orders_id]['total'] = $previous_total;
            $this->order_amounts_data[$orders_id]['previous'] = $previous_amounts_array[$orders_id];
            $this->order_amounts_data[$orders_id]['compensated'] = $compensated_amounts_array[$orders_id];
        }

        // calculate and distribute current deliver amounts		
        foreach ($this->crew_database_data as $orders_id => $orders_arrays) {
            $current_total = (isset($this->order_amounts_data[$orders_id]['previous']['total'])) ? $this->order_amounts_data[$orders_id]['previous']['total'] : 0;
            foreach ($orders_arrays as $index => $orders_array) {
                $sales_activities_code = isset($orders_array['sales_activities_code']) ? $orders_array['sales_activities_code'] : '';
                switch (trim($sales_activities_code)) {
                    case "D": // delivery +
                        $current_amount = $orders_array['sales_activities_amount'];
                        $activity_mode = "deliver";
                        $operator = "+";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $current_amount, $current_total);
                        $current_total = $current_total + $current_amount;
                        break;
                    case "RD": // delivery -
                        $current_amount = $orders_array['sales_activities_amount'];
                        $current_total = $current_total - $current_amount;
                        $activity_mode = "deliver";
                        $operator = "-";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $current_amount, $current_total);
                        break;
                    case "RFD": // refund +
                        $current_amount = $orders_array['sales_activities_amount'];
                        $activity_mode = "refund";
                        $operator = "+";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $current_amount, $current_total);
                        $current_total = $current_total + $current_amount;
                        break;
                    case "RFRD": // refund -
                        $current_amount = $orders_array['sales_activities_amount'];
                        $current_total = $current_total - $current_amount;
                        $activity_mode = "refund";
                        $operator = "-";
                        $account_type = $this->get_order_amount_used_account_type_array($orders_id, $current_amount, $current_total);
                        break;
                    case "RVD": // reverse +
                        $current_total = $current_total - $orders_array['sales_activities_amount'];
                        $activity_mode = "chargeback";
                        $operator = "+";
                        $cb_status = $this->get_order_chargeback_type($orders_id);
                        switch ($cb_status) {
                            case 1 : // chargeback win
                                $account_type = array(0 => array("type" => "win", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            case 2 : // chargeback loss
                                $account_type = array(0 => array("type" => "loss", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            case 3 : // chargeback resolved
                                $account_type = array(0 => array("type" => "resolve", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            default :
                                $account_type = array(0 => array("type" => "resolve", "amt" => $orders_array['sales_activities_amount']));
                                break;
                        }
                        break;
                    case "RVRD": // reverse -
                        $current_total = $current_total + $orders_array['sales_activities_amount'];
                        $activity_mode = "chargeback";
                        $operator = "-";
                        $cb_status = $this->get_order_chargeback_type($orders_id);
                        switch ($cb_status) {
                            case 1 : // chargeback win
                                $account_type = array(0 => array("type" => "win", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            case 2 : // chargeback loss
                                $account_type = array(0 => array("type" => "loss", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            case 3 : // chargeback resolved
                                $account_type = array(0 => array("type" => "resolve", "amt" => $orders_array['sales_activities_amount']));
                                break;
                            default :
                                $account_type = array(0 => array("type" => "resolve", "amt" => $orders_array['sales_activities_amount']));
                                break;
                        }
                        break;
                    case "CD": // compensation +
                        $compensated_amounts_array[$orders_id]['total'] = $compensated_amounts_array[$orders_id]['total'] + $orders_array['sales_activities_amount'];
                        break;
                    case "CRD": // compensation -
                        $compensated_amounts_array[$orders_id]['total'] = $compensated_amounts_array[$orders_id]['total'] - $orders_array['sales_activities_amount'];
                        break;
                    default:
                        break;
                }

                if (isset($account_type)) {
                    foreach ($account_type as $idx => $pair) {
                        $this_type = $pair['type'];
                        $this_value = $pair['amt'];
                        eval('$current_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] = $current_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] ' . $operator . ' ' . $this_value . ';');
                    }
                    unset($account_type);
                }
            }
            $current_amounts_array[$orders_id]['total'] = $current_total;
            $this->order_amounts_data[$orders_id]['current'] = $current_amounts_array[$orders_id];
            $this->order_amounts_data[$orders_id]['compensated'] = $compensated_amounts_array[$orders_id];
        }

        // calculate and distribute balance amounts
        foreach ($this->crew_database_data as $orders_id => $orders_arrays) {
            $orders_total_summary = $this->get_orders_total_summary($orders_id);
            $order_amount = $orders_total_summary['ot_subtotal'];
            $total_delivered = $this->order_amounts_data[$orders_id]['current']['total'];
            if ($total_delivered < $order_amount) {
                $balance_amount = $order_amount - $total_delivered;
                $activity_mode = 'deliver';
                $operator = '+';
                $account_type = $this->get_order_amount_used_account_type_array($orders_id, $balance_amount, $total_delivered);

                foreach ($account_type as $idx => $pair) {
                    $this_type = $pair['type'];
                    $this_value = $pair['amt'];
                    eval('$balance_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] = $balance_amounts_array[' . $orders_id . '][' . $activity_mode . '][' . $this_type . '] ' . $operator . ' ' . $this_value . ';');
                }
            }
            $this->order_amounts_data[$orders_id]['balance'] = $balance_amounts_array[$orders_id];
        }
    }

    function get_order_amount_used_account_type_array($orders_id, $current_amount, $previous_total = 0) {
        $accounts_type = array();
        $running_amt = 0;
        foreach ($this->order_amounts_data[$orders_id]['order'] as $type => $values) {
            $balance_amount = $values['weight'] - ($previous_total + $running_amt);
            if (number_format($current_amount, 4, '.', '') <= number_format($balance_amount, 4, '.', '')) {
                switch ($type) {
                    case "RP" :
                    case "NRP" :
                        $accounts_type[] = array("type" => "payment_gateway", "amt" => $current_amount);
                        $running_amt = $running_amt + $current_amount;
                        break;
                    case "RSC" :
                    case "NRSC" :
                        $accounts_type[] = array("type" => "store_credit", "amt" => $current_amount);
                        $running_amt = $running_amt + $current_amount;
                        break;
                    case "DC" :
                        $accounts_type[] = array("type" => "discount_coupon", "amt" => $current_amount);
                        $running_amt = $running_amt + $current_amount;
                        break;
                    default :
                        break;
                }
                break; // since current_amount already less than ramaining amount, we take full current_amount then break the looping process
            } else if ((number_format($current_amount, 4, '.', '') > number_format($balance_amount, 4, '.', '')) && ($balance_amount >= 0) && ($values['amt'] > 0)) {
                switch ($type) {
                    case "RP" :
                    case "NRP" :
                        $accounts_type[] = array("type" => "payment_gateway", "amt" => $balance_amount);
                        $running_amt = $running_amt + $balance_amount;
                        break;
                    case "RSC" :
                    case "NRSC" :
                        $accounts_type[] = array("type" => "store_credit", "amt" => $balance_amount);
                        $running_amt = $running_amt + $balance_amount;
                        break;
                    case "DC" :
                        $accounts_type[] = array("type" => "discount_coupon", "amt" => $balance_amount);
                        $running_amt = $running_amt + $balance_amount;
                        break;
                    default :
                        break;
                }
                $current_amount = $current_amount - $balance_amount;
            }
        }
        return $accounts_type;
    }

    function get_order_chargeback_type($orders_id) {
        $chargeback_type_select_sql = "SELECT orders_cb_status
										FROM " . TABLE_ORDERS . "
										WHERE orders_id='" . $orders_id . "'";
        $chargeback_type_result_sql = tep_db_query($chargeback_type_select_sql);
        if ($chargeback_type_row = tep_db_fetch_array($chargeback_type_result_sql)) {
            return $chargeback_type_row['orders_cb_status'];
        }

        return 0;
    }

    function get_order_amounts_usage($orders_id) {
        $amounts_usage_array = array('RP' => array('amt' => 0, 'weight' => 0),
            'RSC' => array('amt' => 0, 'weight' => 0),
            'NRP' => array('amt' => 0, 'weight' => 0),
            'NRSC' => array('amt' => 0, 'weight' => 0),
            'SC' => array('amt' => 0, 'weight' => 0),
            'DC' => array('amt' => 0, 'weight' => 0)
        );

        if (tep_not_null($orders_id)) {
            $edit_order_obj = new edit_order($this->identity, $this->identity_email, $orders_id);

            $order_select_sql = "SELECT orders_id, payment_methods_parent_id, payment_methods_id
									FROM " . TABLE_ORDERS . "
									WHERE orders_id='" . $orders_id . "'";
            $order_result_sql = tep_db_query($order_select_sql);

            if ($order_row = tep_db_fetch_array($order_result_sql)) {
                $payment_methods_obj = new payment_methods($order_row['payment_methods_id']);
                // get payment method configuration setting for confirm_complete
                // first by payment_methods_id, then by payment_methods_parent_id
                $pg_confirm_complete = $payment_methods_obj->payment_method_array->confirm_complete_days;

                // get orders total summary info
                $orders_total_summary = $this->get_orders_total_summary($orders_id);
                if ($pg_confirm_complete > 0) {  // PG is RP setting
                    $amounts_usage_array['RP']['amt'] = $orders_total_summary['ot_total'] - $orders_total_summary['ot_surcharge'];
                } else { // PG is NRP setting
                    $amounts_usage_array['NRP']['amt'] = $orders_total_summary['ot_total'] - $orders_total_summary['ot_surcharge'];
                }
            }

            // get store credit used for orders
            if ($order_sc_used = $edit_order_obj->get_sc_used()) {
                $amounts_usage_array['SC']['amt'] = $order_sc_used;
            }

            $amounts_usage_array['DC']['amt'] = $orders_total_summary['ot_coupon'];
            $running_amt = 0;
            foreach ($amounts_usage_array as $idx => $values) {
                $running_amt = $running_amt + $values['amt'];
                $amounts_usage_array[$idx]['weight'] = $running_amt;
            }
        }

        return $amounts_usage_array;
    }

    function compare_activities_data() {
        global $currencies;

        ksort($this->csv_file_data);
        ksort($this->order_amounts_data);

        foreach ($this->csv_file_data as $orders_id => $orders_details) {
            $order_found = false;
            if (is_array($this->order_amounts_data[$orders_id])) {
                $compare_orders_id = $orders_id;
                $order_found = true;
            }

            $orders_info = $this->get_orders_info($orders_id);
            $orders_total_summary = $this->get_orders_total_summary($orders_id);
            $payment_methods_obj = new payment_methods($orders_info['payment_methods_id']);
            if ($payment_methods_obj->get_code() == '') {
                $payment_methods_obj = new payment_methods($orders_info['payment_methods_parent_id']);
            }

            $exchange_rate = 1;
            if ($this->csv_file_data[$orders_id]['currency'] == $orders_info['currency']) {
                $exchange_rate = $orders_info['currency_value'];
            } else {
                if ($orders_total_summary['ot_total'] > 0) {
                    $exchange_rate = $this->csv_file_data[$orders_id]['order_amount_received'] / $orders_total_summary['ot_total'];
                    if ($exchange_rate == 0)
                        $exchange_rate = 1;
                }
            }

            $usd_rate = 0;
            if ($orders_info['currency'] == DEFAULT_CURRENCY) {
                $usd_rate = 1;
            } else {
                $usd_rate = (1 / $orders_info['currency_value']);
            }

            $currency = $this->csv_file_data[$orders_id]['currency'];
            //$csv_file_total = $this->csv_file_data[$orders_id]['order_amount_received'] + ($orders_total_summary['ot_gv']/$exchange_rate) + ($orders_total_summary['ot_coupon']/$exchange_rate) - ($orders_total_summary['ot_surcharge']/$exchange_rate);
            $csv_file_total = $this->csv_file_data[$orders_id]['order_amount_received'];

            $zero_value = number_format(0, $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']);

            if ($order_found) {
                $has_chargeback = (($this->order_amounts_data[$orders_id]['current']['chargeback']['resolve'] > 0) ||
                        ($this->order_amounts_data[$orders_id]['current']['chargeback']['win'] > 0) ||
                        ($this->order_amounts_data[$orders_id]['current']['chargeback']['loss'] > 0)) ? true : false;

                $for_database_total = ((
                        $this->order_amounts_data[$orders_id]['current']['deliver']['payment_gateway'] +
                        $this->order_amounts_data[$orders_id]['current']['deliver']['store_credit'] +
                        $this->order_amounts_data[$orders_id]['current']['deliver']['discount_coupon'] +
                        $this->order_amounts_data[$orders_id]['previous']['total'] +
                        $this->order_amounts_data[$orders_id]['balance']['deliver']['payment_gateway'] +
                        $this->order_amounts_data[$orders_id]['balance']['deliver']['store_credit'] +
                        $this->order_amounts_data[$orders_id]['balance']['deliver']['discount_coupon'] +
                        $this->order_amounts_data[$orders_id]['current']['refund']['payment_gateway'] +
                        $this->order_amounts_data[$orders_id]['current']['refund']['store_credit'] +
                        $this->order_amounts_data[$orders_id]['current']['refund']['discount_coupon']
                        ) - (
                        $this->order_amounts_data[$orders_id]['current']['chargeback']['resolve'] +
                        $this->order_amounts_data[$orders_id]['current']['chargeback']['win'] +
                        $this->order_amounts_data[$orders_id]['current']['chargeback']['loss']
                        ));
                $this->combined_data[$orders_id] = array('order_date' => strval(date('Y-m-d', strtotime($orders_info['date_purchased']))),
                    'pg_code' => $this->csv_file_data[$orders_id]['payment_method'],
                    'order_id' => $orders_id,
                    'currency' => $currency,
                    'pg_amount' => number_format($csv_file_total, $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'pg_surcharge' => number_format(($orders_total_summary['ot_surcharge'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_pg_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['current']['deliver']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_sc_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['current']['deliver']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_dc_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['current']['deliver']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'previous_deliver_amt' => number_format(($this->order_amounts_data[$orders_id]['previous']['total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_pg_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['balance']['deliver']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_sc_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['balance']['deliver']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_dc_amt' => ($has_chargeback) ? $zero_value : number_format(($this->order_amounts_data[$orders_id]['balance']['deliver']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_pg_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['refund']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_sc_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['refund']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_dc_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['refund']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_resolve_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['chargeback']['resolve'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_win_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['chargeback']['win'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_lost_amt' => number_format(($this->order_amounts_data[$orders_id]['current']['chargeback']['loss'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'database_total' => number_format(($for_database_total * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'order_pg_code' => $payment_methods_obj->get_code(),
                    'usd_rate' => round($usd_rate, 8),
                    'order_currency' => $orders_info['currency'],
                    'csv_file_pg_amt' => number_format(($orders_total_summary['ot_total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_pg_surcharge_amt' => number_format(($orders_total_summary['ot_surcharge'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_sc_amt' => number_format(($orders_total_summary['ot_gv'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_dc_amt' => number_format(($orders_total_summary['ot_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_total' => number_format(($orders_total_summary['ot_subtotal'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'compensated_amt' => number_format(($this->order_amounts_data[$orders_id]['compensated']['total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point'])
                );
            } else {
                $this->combined_data[$orders_id] = array('order_date' => $this->csv_file_data[$orders_id]['record_date'],
                    'order_id' => $orders_id,
                    'pg_code' => $this->csv_file_data[$orders_id]['payment_method'],
                    'currency' => $this->csv_file_data[$orders_id]['currency'],
                    'pg_amount' => number_format($csv_file_total, $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'pg_surcharge' => '',
                    'current_deliver_pg_amt' => '',
                    'current_deliver_sc_amt' => '',
                    'current_deliver_dc_amt' => '',
                    'previous_deliver_amt' => '',
                    'balance_deliver_pg_amt' => '',
                    'balance_deliver_sc_amt' => '',
                    'balance_deliver_dc_amt' => '',
                    'current_refund_pg_amt' => '',
                    'current_refund_sc_amt' => '',
                    'current_refund_dc_amt' => '',
                    'current_cb_resolve_amt' => '',
                    'current_cb_win_amt' => '',
                    'current_cb_lost_amt' => '',
                    'database_total' => '',
                    'order_pg_code' => '',
                    'usd_rate' => '',
                    'order_currency' => '',
                    'csv_file_pg_amt' => '',
                    'csv_file_pg_surcharge_amt' => '',
                    'csv_file_sc_amt' => '',
                    'csv_file_dc_amt' => '',
                    'csv_file_total' => '',
                    'compensated_amt' => ''
                );
            }
        }

        foreach ($this->order_amounts_data as $orders_id => $orders_details) {
            if (!is_array($this->csv_file_data[$orders_id])) {
                $orders_info = $this->get_orders_info($orders_id);
                $orders_total_summary = $this->get_orders_total_summary($orders_id);
                $payment_methods_obj = new payment_methods($orders_info['payment_methods_id']);
                if ($payment_methods_obj->get_code() == '') {
                    $payment_methods_obj = new payment_methods($orders_info['payment_methods_parent_id']);
                }
                $currency = $orders_info['currency'];
                $exchange_rate = $orders_info['currency_value'];

                $usd_rate = 0;
                if ($currency == DEFAULT_CURRENCY) {
                    $usd_rate = 1;
                } else {
                    $usd_rate = (1 / $exchange_rate);
                }

                $zero_value = number_format(0, $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']);
                $for_database_total = ((
                        $orders_details['current']['deliver']['payment_gateway'] +
                        $orders_details['current']['deliver']['store_credit'] +
                        $orders_details['current']['deliver']['discount_coupon'] +
                        $orders_details['previous']['total'] +
                        $orders_details['balance']['deliver']['payment_gateway'] +
                        $orders_details['balance']['deliver']['store_credit'] +
                        $orders_details['balance']['deliver']['discount_coupon'] +
                        $orders_details['current']['refund']['payment_gateway'] +
                        $orders_details['current']['refund']['store_credit'] +
                        $orders_details['current']['refund']['discount_coupon']
                        ) - (
                        $orders_details['current']['chargeback']['resolve'] +
                        $orders_details['current']['chargeback']['win'] +
                        $orders_details['current']['chargeback']['loss']
                        ));

                $has_chargeback = (($orders_details['current']['chargeback']['resolve'] > 0) ||
                        ($orders_details['current']['chargeback']['win'] > 0) ||
                        ($orders_details['current']['chargeback']['loss'] > 0)) ? true : false;

                $this->combined_data[$orders_id] = array('order_date' => strval(date('Y-m-d', strtotime($orders_info['date_purchased']))),
                    'order_id' => $orders_id,
                    'pg_code' => '',
                    'currency' => '',
                    'pg_amount' => '',
                    'pg_surcharge' => number_format(($orders_total_summary['ot_surcharge'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_pg_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['current']['deliver']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_sc_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['current']['deliver']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_deliver_dc_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['current']['deliver']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'previous_deliver_amt' => number_format(($orders_details['previous']['total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_pg_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['balance']['deliver']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_sc_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['balance']['deliver']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'balance_deliver_dc_amt' => ($has_chargeback) ? $zero_value : number_format(($orders_details['balance']['deliver']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_pg_amt' => number_format(($orders_details['current']['refund']['payment_gateway'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_sc_amt' => number_format(($orders_details['current']['refund']['store_credit'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_refund_dc_amt' => number_format(($orders_details['current']['refund']['discount_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_resolve_amt' => number_format(($orders_details['current']['chargeback']['resolve'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_win_amt' => number_format(($orders_details['current']['chargeback']['win'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'current_cb_lost_amt' => number_format(($orders_details['current']['chargeback']['loss'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'database_total' => number_format(($for_database_total * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'order_pg_code' => $payment_methods_obj->get_code(),
                    'usd_rate' => round($usd_rate, 8),
                    'order_currency' => $orders_info['currency'],
                    'csv_file_pg_amt' => number_format(($orders_total_summary['ot_total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_pg_surcharge_amt' => number_format(($orders_total_summary['ot_surcharge'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_sc_amt' => number_format(($orders_total_summary['ot_gv'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_dc_amt' => number_format(($orders_total_summary['ot_coupon'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'csv_file_total' => number_format(($orders_total_summary['ot_subtotal'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']),
                    'compensated_amt' => number_format(($orders_details['compensated']['total'] * $exchange_rate), $currencies->currencies[$currency]['decimal_places'], $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point'])
                );
            }
        }
    }

    function show_orders_matching($filename, $session_name, $input_array, &$messageStack) {
        global $currencies, $languages_id;

        if ($input_array["report_type"] == '2') {
            return $this->show_activity_matching($filename, $session_name, $input_array, $messageStack);
        }

        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["report_type"] = $input_array["report_type"];
            $_SESSION[$session_name]["start_date"] = $input_array["start_date"];
            $_SESSION[$session_name]["end_date"] = $input_array["end_date"];
            $_SESSION[$session_name]["payment_gateways_id"] = isset($input_array["payment_gateways_id"]) ? $input_array["payment_gateways_id"] : array();
            $_SESSION[$session_name]["payment_methods_id"] = isset($input_array["payment_methods_id"]) ? $input_array["payment_methods_id"] : array();
        }

        $this->payment_gateways_id = $_SESSION[$session_name]["payment_gateways_id"];
        $this->payment_methods_id = $_SESSION[$session_name]["payment_methods_id"];
        $this->report_type_id = $_SESSION[$session_name]["report_type"];

        if (!isset($_SESSION[$session_name]["combined_data"])) {
            // Prepare temp files
            $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
            $temp_target = 'download/' . $temp_filename;
            // Do checking if temp file exist for this user?
            if(file_exists($temp_target)) {
                // removed prev file
                unlink($temp_target);
            }
            // Store file upload temp
            move_uploaded_file($_FILES['csv_import']['tmp_name'], $temp_target);
            // Store upload file settings
            $_FILES['csv_import']['name'] = $temp_filename;
            $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
            $_SESSION[$session_name]["combined_data"] = $_FILES['csv_import'];
        } else {
            if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name'])) {
                // Prepare temp files
                $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
                $temp_target = 'download/' . $temp_filename;
                // Do checking if temp file exist for this user?
                if(file_exists($temp_target)) {
                    // removed prev file
                    unlink($temp_target);
                }
                // Store file upload temp
                move_uploaded_file($_FILES['csv_import']['tmp_name'], $temp_target);
                // Store upload file settings
                $_FILES['csv_import']['name'] = $temp_filename;
                $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
                $_SESSION[$session_name]["combined_data"] = $_FILES['csv_import'];
            } else {
                $_FILES['csv_import'] = $_SESSION[$session_name]["combined_data"];
                // Prepare temp files
                $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
                $temp_target = 'download/' . $temp_filename;
                $_FILES['csv_import']['name'] = $temp_filename;
                $_FILES['csv_import']['tmp_name'] = DIR_FS_DOCUMENT_ROOT.$temp_target;
            }
        }

        // if (!isset($_SESSION[$session_name]["combined_data"])) {
        //     $this->load_csv_file_data($input_array, $messageStack);
        //     $this->load_crew_database_data($input_array);
        //     $this->compare_data();
        //     $_SESSION[$session_name]["combined_data"] = $this->combined_data;
        // } else {
        //     if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name'])) {
        //         $this->load_csv_file_data($input_array, $messageStack);
        //         $this->load_crew_database_data($input_array);
        //         $this->compare_data();
        //         $_SESSION[$session_name]["combined_data"] = $this->combined_data;
        //     } else {
        //         $this->combined_data = $_SESSION[$session_name]["combined_data"];
        //     }
        // }

        $this->load_csv_file_data($input_array, $messageStack);
        $this->load_crew_database_data($input_array);
        $this->compare_data();

        $paging_array_data = $this->paging_array_data($this->combined_data, 50, $input_array);

        $csv_filename = $this->_generate_csv_file();

        ob_start();
        ?>
        <br>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
        <? if (count($paging_array_data['display_data'])) { ?>
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="smallText" valign="middle">
                            <tr>
                                <td colspan="2" class="main"><b>Result Codes' Reference :</b></td>
                            </tr>
                            <tr>
                                <td width="50" align="right" class="main"><span class="greenIndicator"><b>01</b></span> : </td>
                                <td align="left" class="main">Payment Gateway Matched</td>
                            </tr>
                            <tr>
                                <td align="right" class="main"><span class="flagOrange"><b>02</b></span> : </td>
                                <td align="left" class="main">Amount Matched</td>
                            </tr>
                            <tr>
                                <td align="right" class="main"><span class="orangeIndicator"><b>03</b></span> : </td>
                                <td align="left" class="main">Status Matched</td>
                            </tr>
                            <tr>
                                <td align="right" class="main"><span class="redIndicator"><b>04</b></span> : </td>
                                <td align="left" class="main">Date in Range</td>
                            </tr>
                        </table>
                    </td>
                </tr>
        <? } ?>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr height="20">
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_ID ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_DATETIME ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_RECEIVED ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_PG_CODE ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_CURRENCY ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_DATETIME ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_CUSTOMER_ID ?></td>
                            <td colspan="3" width="280px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_BREAKDOWN ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_PG_CODE ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_CURRENCY ?></td>
                            <td rowspan="2" class="reportBoxHeading"><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_STATUS ?></td>
                            <td colspan="4" width="120px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES ?></td>
                        </tr>
                        <tr>
                            <td width="70px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_PG ?></td>
                            <td width="70px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_SC ?></td>
                            <td width="70px" class="reportBoxHeading" nowrap><?= TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_SUBTOTAL ?></td>
                            <td width="30px" class="reportBoxHeading" nowrap><span class="greenIndicator"><b><?= TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_01 ?></b></span></td>
                            <td width="30px" class="reportBoxHeading" nowrap><span class="flagOrange"><b><?= TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_02 ?></b></span></td>
                            <td width="30px" class="reportBoxHeading" nowrap><span class="orangeIndicator"><b><?= TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_03 ?></b></span></td>
                            <td width="30px" class="reportBoxHeading" nowrap><span class="redIndicator"><b><?= TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_04 ?></b></span></td>
                        </tr>
        <?
        $row_count = 0;

        foreach ($paging_array_data['display_data'] as $orders_id => $orders_details) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
            $orders_id_str = $orders_id;
            $record_date_str = $orders_details['csv_file_record_date'];
            $order_amount_received_str = $orders_details['csv_file_order_amount_received'];
            $pg_code_str = $orders_details['csv_file_pg_code'];
            $currency_str = $orders_details['csv_file_currency'];

            $crew_database_record_date_str = $orders_details['crew_database_record_date'];
            $order_amount_pg_str = $orders_details['crew_database_pg_value'];
            $order_amount_rsc_str = $orders_details['crew_database_rsc_value'];
            $order_amount_nrsc_str = $orders_details['crew_database_nrsc_value'];
            $order_amount_sc_str = $orders_details['crew_database_sc_value'];
            $order_amount_sub_total_str = $orders_details['crew_database_sub_total_value'];
            $order_amount_customers_id = $orders_details['crew_database_customers_id_value'];
            $order_amount_total_str = $orders_details['crew_database_total_value'];
            $crew_database_pg_code_str = $orders_details['crew_database_pg_code'];
            $crew_database_currency_str = $orders_details['crew_database_currency'];

            $record_status_str = $orders_details['crew_database_record_status'];
            $result_code_01_str = $orders_details['result_code_01'];
            $result_code_02_str = $orders_details['result_code_02'];
            $result_code_03_str = $orders_details['result_code_03'];
            $result_code_04_str = $orders_details['result_code_04'];
            ?>
                            <tr height="20" class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                <td valign="top" align="center" class="reportRecords" nowrap>
                            <?
                            if (strlen($orders_details['crew_database_orders_id']) > 0) {
                                ?>
                                        <a href="<?= tep_href_link("orders.php", 'oID=' . $orders_id_str . '&action=edit', 'NONSSL') ?>" target="_new">
                                        <?= $orders_id_str ?>
                                        </a>
                                        <?
                                    } else {
                                        echo $orders_id_str;
                                    }
                                    ?>
                                </td>
                                <td valign="top" class="reportRecords" nowrap><?= $record_date_str ?></td>
                                <td valign="top" align="right" class="reportRecords" nowrap><?= $order_amount_received_str ?></td>
                                <td valign="top" class="reportRecords"><?= $pg_code_str ?></td>
                                <td valign="top" class="reportRecords"><?= $currency_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $crew_database_record_date_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $order_amount_customers_id ?></td>
                                <td valign="top" class="reportRecords"><?= $order_amount_pg_str ?></td>
                                <td valign="top" class="reportRecords"><?= $order_amount_sc_str ?></td>
                                <td valign="top" class="reportRecords"><?= $order_amount_sub_total_str ?></td>
                                <td valign="top" class="reportRecords"><?= $crew_database_pg_code_str ?></td>
                                <td valign="top" class="reportRecords"><?= $crew_database_currency_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $record_status_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $result_code_01_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $result_code_02_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $result_code_03_str ?></td>
                                <td valign="top" class="reportRecords" nowrap><?= $result_code_04_str ?></td>
                            </tr>
            <?
            $row_count++;
        }
        ?>
                    </table>
                </td>
            </tr>
            <tr>
                <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>
            <tr>
                <td><?= $paging_array_data['display_info'] ?></td>
            </tr>
            <tr>
                <td>
        <?
        echo tep_draw_form('export_csv_form', FILENAME_ORDERS_MATCHING, tep_get_all_get_params(array('action')) . 'action=export_csv', 'post', '');
        echo tep_draw_hidden_field("filename", $csv_filename) . "\n";
        ?>
                    <table width="100%" border="0" cellspacing="0" cellpadding="2">
                        <tr>
                            <td align="right">
        <? echo tep_submit_button('Export All', 'Export as csv file', 'name="btn_export_csv"', 'inputButton'); ?>
                            </td>
                        </tr>
                    </table>
                    </form>
                </td>
            </tr>
        </table>
        <?
        $report_section_html = ob_get_contents();
        ob_end_clean();

        // This should be here to let all the session get updated value
        $search_section_html = $this->search_orders_matching($filename, $session_name);

        return $search_section_html . "\n" . $report_section_html;
    }

    function load_csv_file_data($input_array, &$messageStack) {
        // echo $_FILES['csv_import']['tmp_name'];
        // var_dump(is_uploaded_file($_FILES['csv_import']['tmp_name']));
        if (tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none')) {
            if (file_exists($_FILES['csv_import']['tmp_name'])) {
                if ($_FILES['csv_import']["size"] > 0) {
                    list($start_date_year, $start_date_month, $start_date_day) = explode('-', trim($input_array["start_date"]));
                    $start_date_timestamp = mktime(0, 0, 0, $start_date_month, $start_date_day, $start_date_year);

                    list($end_date_year, $end_date_month, $end_date_day) = explode('-', trim($input_array["end_date"]));
                    $end_date_timestamp = mktime(0, 0, 0, $end_date_month, $end_date_day, $end_date_year);

                    $filename = ($_FILES['csv_import']['tmp_name']);
                    $handle = fopen($filename, 'r+');

                    while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
                        if (strtoupper(trim($data[0])) == 'DATE') {
                            break;
                        }
                    }

                    if ($input_array['report_type'] == '2') {
                        while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
                            if (!tep_not_null($data[0])) {
                                continue;
                            }

                            $record_date = trim($data[0]);
                            $orders_id = $data[1];
                            $order_amount_received = (float) $data[2];
                            $currency = trim($data[3]);
                            $pg_code = $data[4];
                            $record_status = '';

                            $this->csv_file_data[$orders_id] = array('orders_id' => $orders_id,
                                'record_date' => $record_date,
                                'order_amount_received' => number_format($order_amount_received, 4, '.', ''),
                                'payment_method' => str_replace(" ", "", strtolower($pg_code)),
                                'currency' => strtoupper($currency),
                                'record_status' => $record_status
                            );
                        }
                    } else {
                        while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
                            if (!tep_not_null($data[0])) {
                                continue;
                            }

                            $record_date = trim($data[0]);
                            $orders_id = $data[1];
                            $order_amount_received = (float) $data[2];
                            $pg_code = $data[3];
                            $currency = trim($data[4]);
                            $record_status = $data[5];

                            $this->csv_file_data[$orders_id] = array('orders_id' => $orders_id,
                                'record_date' => $record_date,
                                'order_amount_received' => number_format($order_amount_received, 4, '.', ''),
                                'payment_method' => str_replace(" ", "", strtolower($pg_code)),
                                'currency' => strtoupper($currency),
                                'record_status' => $record_status
                            );
                        }
                    }
                    fclose($handle);
                } else {
                    $messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
                }
            } else {
                $messageStack->add_session(WARNING_MISSING_FILE_UPLOADED, 'error');
            }
        } else {
            $messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
        }
    }

    function _generate_activities_csv_file() {
        $export_csv_data = '';
        $filename = '';

        if (count($this->combined_data)) {
            $export_csv_data = "Activities Start Date(YYYY-MM-DD) : " . $_SESSION['orders_matching_inputs']["start_date"] . ",,Activities End Date(YYYY-MM-DD) : " . $_SESSION['orders_matching_inputs']["end_date"];
            $export_csv_data .= "\n";
            $export_csv_data .= "\n";
            $export_csv_data .= ",,Payment Gateway,,,,Current Deliveries,,,Previous,Undelivered,,,Refund,,,Charge Back,,,,Order Info,,,,,,,,Compensated";
            $export_csv_data .= "\n";
            $export_csv_data .= "Order Date,Order ID,PG Code,Currency,PG Amount,PG Surcharge,Total PG Deliver,Total Store Credit,Total Discount Coupon,Deliveries,PG,Store Credit,Discount Coupon,in PG,Issued SC,in Discount Coupon,Resolve,Win,Loss,Total,Order PG Code,USD Rate,Order Currency,Amount,Surcharge,Store Credit,Discount Coupon,Total,Deliveries";
            $export_csv_data .= "\n";

            foreach ($this->combined_data as $orders_id => $orders_details) {
                $orders_date_str = $orders_details['order_date'];
                $orders_id_str = $orders_id;
                if ($this->get_refund_as_store_credit($orders_id) > 0) {
                    $orders_id_str = $orders_id_str . ' *';
                }
                $pg_code_str = $orders_details['pg_code'];
                $currency_str = $orders_details['currency'];
                $pg_amount_str = $orders_details['pg_amount'];
                $pg_surcharge_str = $orders_details['pg_surcharge'];
                $usd_rate_str = $orders_details['usd_rate'];

                $current_deliver_pg_amt_str = $orders_details['current_deliver_pg_amt'];
                $current_deliver_sc_amt_str = $orders_details['current_deliver_sc_amt'];
                $current_deliver_dc_amt_str = $orders_details['current_deliver_dc_amt'];
                $previous_deliver_amt_str = $orders_details['previous_deliver_amt'];
                $balance_deliver_pg_amt_str = $orders_details['balance_deliver_pg_amt'];
                $balance_deliver_sc_amt_str = $orders_details['balance_deliver_sc_amt'];
                $balance_deliver_dc_amt_str = $orders_details['balance_deliver_dc_amt'];
                $current_refund_pg_amt_str = $orders_details['current_refund_pg_amt'];
                $current_refund_sc_amt_str = $orders_details['current_refund_sc_amt'];
                $current_refund_dc_amt_str = $orders_details['current_refund_dc_amt'];
                $current_cb_resolve_amt_str = $orders_details['current_cb_resolve_amt'];
                $current_cb_win_amt_str = $orders_details['current_cb_win_amt'];
                $current_cb_lost_amt_str = $orders_details['current_cb_lost_amt'];
                $database_total_str = $orders_details['database_total'];

                $order_pg_code_str = $orders_details['order_pg_code'];
                $order_currency_str = $orders_details['order_currency'];
                $csv_file_pg_amt_str = $orders_details['csv_file_pg_amt'];
                $csv_file_pg_surcharge_amt_str = $orders_details['csv_file_pg_surcharge_amt'];
                $csv_file_sc_amt_str = $orders_details['csv_file_sc_amt'];
                $csv_file_dc_amt_str = $orders_details['csv_file_dc_amt'];
                $csv_file_total_str = $orders_details['csv_file_total'];
                $compensated_total_str = $orders_details['compensated_amt'];

                $export_csv_data .= "\"$orders_date_str\",\"$orders_id_str\",\"$pg_code_str\",\"$currency_str\",\"$pg_amount_str\",\"$pg_surcharge_str\",\"$current_deliver_pg_amt_str\",\"$current_deliver_sc_amt_str\",\"$current_deliver_dc_amt_str\",\"$previous_deliver_amt_str\",\"$balance_deliver_pg_amt_str\",\"$balance_deliver_sc_amt_str\",\"$balance_deliver_dc_amt_str\",\"$current_refund_pg_amt_str\",\"$current_refund_sc_amt_str\",\"$current_refund_dc_amt_str\",\"$current_cb_resolve_amt_str\",\"$current_cb_win_amt_str\",\"$current_cb_lost_amt_str\",\"$database_total_str\",\"$order_pg_code_str\",\"$usd_rate_str\",\"$order_currency_str\",\"$csv_file_pg_amt_str\",\"$csv_file_pg_surcharge_amt_str\",\"$csv_file_sc_amt_str\",\"$csv_file_dc_amt_str\",\"$csv_file_total_str\",\"$compensated_total_str\"";
                $export_csv_data .= "\n";
            }
        }

        if (tep_not_null($export_csv_data)) {
            $filename = 'orders_matching_' . date('YmdHis') . '.csv';
            $file_location = 'download/' . $filename;
            if (!$handle = fopen($file_location, 'w')) {
                exit;
            }

            // Write to our opened file.
            if (fwrite($handle, $export_csv_data) === FALSE) {
                fclose($handle);
                exit;
            }

            fclose($handle);
            return $filename;
        }
    }

    function _generate_csv_file() {
        $export_csv_data = '';
        $filename = '';

        if (count($this->combined_data)) {
            $export_csv_data = "Start Last Verified Date(YYYY-MM-DD) : " . $_SESSION['orders_matching_inputs']["start_date"] . ",,End Last Verified Date(YYYY-MM-DD) : " . $_SESSION['orders_matching_inputs']["end_date"];
            $export_csv_data .= "\n";
            $export_csv_data .= "\n";
            $export_csv_data .= "Result Codes' Reference :";
            $export_csv_data .= "\n";
            $export_csv_data .= "01 : Payment Gateway Matched";
            $export_csv_data .= "\n";
            $export_csv_data .= "02 : Amount Matched";
            $export_csv_data .= "\n";
            $export_csv_data .= "03 : Status Matched";
            $export_csv_data .= "\n";
            $export_csv_data .= "04 : Date in Range";
            $export_csv_data .= "\n";
            $export_csv_data .= ",,,,,,Order Amount Breakdown,,,,,,,Result Codes";
            $export_csv_data .= "\n";
            $export_csv_data .= "Order ID,Date/Time,Order Amount Received,PG Code,Currency,Date/Time,Customer ID,PG,SC,Sub-Total,PG Code,Currency,Order Status,01,02,03,04";
            $export_csv_data .= "\n";

            foreach ($this->combined_data as $orders_id => $orders_details) {
                $orders_id_str = $orders_id;
                $record_date_str = $orders_details['csv_file_record_date'];
                $order_amount_received_str = $orders_details['csv_file_order_amount_received'];
                $pg_code_str = $orders_details['csv_file_pg_code'];
                $currency_str = $orders_details['csv_file_currency'];

                $crew_database_record_date_str = $orders_details['crew_database_record_date'];
                $order_amount_pg_str = $orders_details['crew_database_pg_value'];
                $order_amount_sc_str = $orders_details['crew_database_sc_value'];
                $order_amount_customers_id_str = $orders_details['crew_database_customers_id_value'];
                $order_amount_sub_total_str = $orders_details['crew_database_sub_total_value'];
                $order_amount_total_str = $orders_details['crew_database_total_value'];
                $crew_database_pg_code_str = $orders_details['crew_database_pg_code'];
                $crew_database_currency_str = $orders_details['crew_database_currency'];

                $record_status_str = $orders_details['crew_database_current_status'];
                $result_code_01_str = $orders_details['result_code_01'];
                $result_code_02_str = $orders_details['result_code_02'];
                $result_code_03_str = $orders_details['result_code_03'];
                $result_code_04_str = $orders_details['result_code_04'];

                $export_csv_data .= "$orders_id_str,$record_date_str,$order_amount_received_str,$pg_code_str,$currency_str,$crew_database_record_date_str,$order_amount_customers_id_str,$order_amount_pg_str,$order_amount_sc_str,$order_amount_sub_total_str,$crew_database_pg_code_str,$crew_database_currency_str,$record_status_str,$result_code_01_str,$result_code_02_str,$result_code_03_str,$result_code_04_str";
                $export_csv_data .= "\n";
            }
        }

        if (tep_not_null($export_csv_data)) {
            $filename = 'orders_matching_' . date('YmdHis') . '.csv';
            $file_location = 'download/' . $filename;
            if (!$handle = fopen($file_location, 'w')) {
                exit;
            }

            // Write to our opened file.
            if (fwrite($handle, $export_csv_data) === FALSE) {
                fclose($handle);
                exit;
            }

            fclose($handle);
            return $filename;
        }
    }

    function get_orders_info($orders_id) {
        $orders_info = array();

        $orders_select_sql = "	SELECT o.orders_id, o.customers_id,  o.payment_method, o.payment_methods_id, o.payment_methods_parent_id, o.currency, o.currency_value, o.orders_status, o.date_purchased 
                                FROM " . TABLE_ORDERS . " AS o
								WHERE o.orders_id = " . tep_db_input($orders_id);
        $orders_result_sql = tep_db_query($orders_select_sql, 'read_db_link');
        $orders_result_row = tep_db_fetch_array($orders_result_sql);

        return $orders_result_row;
    }

    function get_refund_as_store_credit($orders_id, $return_total = true) {
        $store_credit_history_select_sql = "SELECT store_credit_history_id
											FROM " . TABLE_STORE_CREDIT_HISTORY . "
											WHERE store_credit_history_trans_type='C'
											AND store_credit_activity_type='R'
											AND store_credit_history_trans_id = '" . tep_db_input($orders_id) . "'";
        $store_credit_history_result_sql = tep_db_query($store_credit_history_select_sql, 'read_db_link');

        if ($return_total) {
            $store_credit_history_result_row = tep_db_num_rows($store_credit_history_result_sql);
        } else {
            $store_credit_history_result_row = tep_db_fetch_array($store_credit_history_result_sql);
        }

        return $store_credit_history_result_row;
    }

    function get_orders_amount_info($orders_id, $orders_info) {
        $orders_amount_info = array();
        $orders_total_select_sql = "SELECT ot.value AS pg_value, ot.text
									FROM " . TABLE_ORDERS_TOTAL . " AS ot
									WHERE ot.orders_id = " . tep_db_input($orders_id) . "
										AND ot.class = 'ot_total'";
        $orders_total_result_sql = tep_db_query($orders_total_select_sql, 'read_db_link');
        $orders_total_result_row = tep_db_fetch_array($orders_total_result_sql);

        $orders_sub_total_select_sql = "SELECT ot.value AS sub_total, ot.text
        FROM " . TABLE_ORDERS_TOTAL . " AS ot
        WHERE ot.orders_id = " . tep_db_input($orders_id) . "
            AND ot.class = 'ot_subtotal'";
        $orders_sub_total_result_sql = tep_db_query($orders_sub_total_select_sql, 'read_db_link');
        $orders_sub_total_result_row = tep_db_fetch_array($orders_sub_total_result_sql);

        $orders_sc_total_select_sql = "SELECT ot.value AS ot_gv, ot.text
        FROM " . TABLE_ORDERS_TOTAL . " AS ot
        WHERE ot.orders_id = " . tep_db_input($orders_id) . "
            AND ot.class = 'ot_gv'";
        $orders_sc_total_result_sql = tep_db_query($orders_sc_total_select_sql, 'read_db_link');
        $orders_sc_total_result_row = tep_db_fetch_array($orders_sc_total_result_sql);
        if($orders_sc_total_result_row){
            $sc_checkout_amount = $orders_sc_total_result_row['ot_gv'];
        }
        $orders_amount_info['pg_value'] = $orders_total_result_row['pg_value'] * $orders_info['currency_value'];
        $orders_amount_info['sub_total'] = ($orders_sub_total_result_row['sub_total'] + $sc_checkout_amount) * $orders_info['currency_value'];
        $orders_amount_info['paid_amount'] = strip_tags($orders_total_result_row['text']);

        $orders_amount_info['payment_method_type'] = $this->payment_method[strtolower($orders_info['payment_method'])]['type'];
        $orders_amount_info['rp_value'] = ($orders_amount_info['payment_method_type'] == 'RP') ? $orders_amount_info['pg_value'] : 0.0000;
        $orders_amount_info['nrp_value'] = ($orders_amount_info['payment_method_type'] == 'NRP') ? $orders_amount_info['pg_value'] : 0.0000;

        $edit_order_obj = new edit_order($this->identity, $this->identity_email, $orders_id);
        $sc_used = $edit_order_obj->get_sc_used();

        //$orders_amount_info['rsc_value'] = (isset($sc_used['R']) && is_numeric($sc_used['R'])) ? $sc_used['R'] * $orders_info['currency_value'] : 0.0000;
        //$orders_amount_info['nrsc_value'] = (isset($sc_used['NR']) && is_numeric($sc_used['NR'])) ? $sc_used['NR'] * $orders_info['currency_value'] : 0.0000;
        $orders_amount_info['sc_value'] = $sc_used;
        // $orders_amount_info['nrsc_value'] = (isset($sc_used['NR']) && is_numeric($sc_used['NR'])) ? $sc_used['NR'] : 0.0000;
        $orders_amount_info['total_value'] = $orders_amount_info['rp_value'] + $orders_amount_info['nrp_value'] + $orders_amount_info['rsc_value'] + $orders_amount_info['nrsc_value'];

        return $orders_amount_info;
    }

    function get_orders_total_summary($orders_id) {
        $orders_total_summary = $this->orders_total_summary;

        $orders_total_select_sql = "SELECT ot.value, ot.class
									FROM " . TABLE_ORDERS_TOTAL . " AS ot
									WHERE ot.orders_id = " . tep_db_input($orders_id);
        $orders_total_result_sql = tep_db_query($orders_total_select_sql, 'read_db_link');
        while ($orders_total_result_row = tep_db_fetch_array($orders_total_result_sql)) {
            $orders_total_summary[$orders_total_result_row['class']] = $orders_total_result_row['value'];
        }
        return $orders_total_summary;
    }

    function get_last_orders_status($orders_id, $start_date_str, $end_date_str) {
        $valid_order = false;
        $verifying_in_range = false;
        $processing_in_range = false;

        $last_orders_status = array();

        $verifying_status_select_sql = "SELECT osh.date_added 
										FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
										WHERE osh.orders_id = " . tep_db_input($orders_id) . "
											AND osh.orders_status_id = 7
										ORDER BY osh.date_added DESC
										LIMIT 1";
        $verifying_status_result_sql = tep_db_query($verifying_status_select_sql, 'read_db_link');

        if ($verifying_status_row = tep_db_fetch_array($verifying_status_result_sql)) {
            if (tep_day_diff($start_date_str, $verifying_status_row['date_added'], 'sec') !== FALSE && tep_day_diff($verifying_status_row['date_added'], $end_date_str, 'sec') !== FALSE) {
                $verifying_in_range = true;
            }
        }

        $processing_status_select_sql = "	SELECT osh.date_added 
											FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
											WHERE osh.orders_id = " . tep_db_input($orders_id) . "
												AND osh.orders_status_id = 2
											ORDER BY osh.date_added DESC
											LIMIT 1";
        $processing_status_result_sql = tep_db_query($processing_status_select_sql, 'read_db_link');

        if ($processing_status_row = tep_db_fetch_array($processing_status_result_sql)) {
            if (tep_day_diff($start_date_str, $processing_status_row['date_added'], 'sec') !== FALSE && tep_day_diff($processing_status_row['date_added'], $end_date_str, 'sec') !== FALSE) {
                $processing_in_range = true;
            }
        }

        if (tep_not_null($verifying_status_row['date_added']) && tep_not_null($processing_status_row['date_added'])) {
            if (tep_day_diff($verifying_status_row['date_added'], $processing_status_row['date_added'], 'sec') !== FALSE) {
                if ($verifying_in_range) {
                    $last_orders_status['date'] = $verifying_status_row['date_added'];
                    $last_orders_status['status_id'] = '7';
                    $valid_order = true;
                }
            } else {
                if ($processing_in_range) {
                    $last_orders_status['date'] = $processing_status_row['date_added'];
                    $last_orders_status['status_id'] = '2';
                    $valid_order = true;
                }
            }
        } else if (tep_not_null($verifying_status_row['date_added'])) {
            if ($verifying_in_range) {
                $last_orders_status['date'] = $verifying_status_row['date_added'];
                $last_orders_status['status_id'] = '7';
                $valid_order = true;
            }
        } else if (tep_not_null($processing_status_row['date_added'])) {
            if ($processing_in_range) {
                $last_orders_status['date'] = $processing_status_row['date_added'];
                $last_orders_status['status_id'] = '2';
                $valid_order = true;
            }
        }

        return $valid_order ? $last_orders_status : $valid_order;
    }

    function load_crew_database_data($input_array) {
        global $languages_id;
        $table_join_sql = '';
        $payment_method_where_str = ' 1 ';

        if (tep_not_null($input_array["start_date"])) {
            if (strpos($input_array["start_date"], ':') === FALSE) {
                $input_array["start_date"] = $input_array["start_date"] . ' 00:00';
            }
            $startDateObj = explode(' ', trim($input_array["start_date"]));
            list($yr, $mth, $day) = explode('-', $startDateObj[0]);
            list($hr, $min) = explode(':', $startDateObj[1]);
            $this->store_start_date = date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr));
            $start_date_str = " ( osh.date_added >= '" . $this->store_start_date . "' )";
        } else {
            $start_date_str = " 1 ";
        }

        if (tep_not_null($input_array["end_date"])) {
            if (strpos($input_array["end_date"], ':') === FALSE) {
                $input_array["end_date"] = $input_array["end_date"] . ' 23:59';
            }
            $endDateObj = explode(' ', trim($input_array["end_date"]));
            list($yr, $mth, $day) = explode('-', $endDateObj[0]);
            list($hr, $min) = explode(':', $endDateObj[1]);
            $this->store_end_date = date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 59, $mth, $day, $yr));
            $end_date_str = " ( osh.date_added <= '" . $this->store_end_date . "' )";
        } else {
            $end_date_str = " 1 ";
        }

        $payment_gateways_where_str = 0;
        if (isset($input_array['payment_gateways_id']) && count($input_array['payment_gateways_id'])) {
            $table_join_sql .= ' INNER JOIN ' . TABLE_ORDERS . ' AS o ON osh.orders_id=o.orders_id ';
            $payment_gateways_where_str = " o.payment_methods_parent_id IN ('" . implode("', '", $input_array["payment_gateways_id"]) . "')";
        }

        $payment_methods_where_str = 0;
        if (isset($input_array['payment_methods_id']) && count($input_array['payment_methods_id'])) {
            if ($payment_gateways_where_str == '0') {
                $table_join_sql .= ' INNER JOIN ' . TABLE_ORDERS . ' AS o ON osh.orders_id=o.orders_id ';
            }
            $payment_methods_where_str = " o.payment_methods_id IN ('" . implode("', '", $input_array["payment_methods_id"]) . "')";
        }

        $orders_status_history_select_sql = "	SELECT DISTINCT osh.orders_id 
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh " .
                $table_join_sql . "
												WHERE " . $start_date_str . "
													AND " . $end_date_str;
        if ($payment_gateways_where_str != '0' || $payment_methods_where_str != '0') {
            $orders_status_history_select_sql .="	AND (
														" . $payment_gateways_where_str . " 
														OR " . $payment_methods_where_str . " 
													)";
        }
        $orders_status_history_select_sql .= "		AND (osh.orders_status_id = 7 OR osh.orders_status_id = 2)";
        $orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql, 'read_db_link');

        $orders_info = array();
        $orders_amount_info = array();
        $last_orders_status = array();

        $payment_methods_obj = new payment_methods('all');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        while ($orders_status_history_result_row = tep_db_fetch_array($orders_status_history_result_sql)) {
            $orders_id = $orders_status_history_result_row['orders_id'];
            $last_orders_status = $this->get_last_orders_status($orders_id, $input_array["start_date"] . ':00', $input_array["end_date"] . ':59');

            if ($last_orders_status !== FALSE) {
                $orders_info = $this->get_orders_info($orders_id);
                $orders_amount_info = $this->get_orders_amount_info($orders_id, $orders_info);
                $this->crew_database_data[$orders_id] = array('orders_id' => $orders_id,
                    'record_date' => tep_date_short($last_orders_status['date'], 'Y-m-d'),
                    'pg_value' => number_format($orders_amount_info['pg_value'], 4, '.', ''),
                    'paid_amount' => $orders_amount_info['paid_amount'],
                    'sc_value' => number_format($orders_amount_info['sc_value'], 4, '.', ''),
                    'sub_total' =>  number_format($orders_amount_info['sub_total'], 4, '.', ''),
                    'customers_id' => $orders_info['customers_id'],
                    // 'nrsc_value' => number_format($orders_amount_info['nrsc_value'], 4, '.', ''),
                    'total_value' => number_format($orders_amount_info['total_value'], 4, '.', ''),
                    'payment_method' => str_replace(" ", "", strtolower($payment_info_array[(isset($input_array['payment_gateways_id']) && count($input_array['payment_gateways_id']) && in_array($orders_info['payment_methods_parent_id'], $input_array['payment_gateways_id']) ? $orders_info['payment_methods_parent_id'] : $orders_info['payment_methods_id'])]->title)),
                    'payment_methods_id' => $orders_info['payment_methods_id'],
                    'payment_methods_parent_id' => $orders_info['payment_methods_parent_id'],
                    'currency' => $orders_info['currency'],
                    'record_status' => tep_get_orders_status_name($last_orders_status['status_id'], $languages_id),
                    'current_status' => tep_get_orders_status_name($orders_info['orders_status'], $languages_id)
                );
            }
        }
    }

    function compare_data() {
        global $currencies;

        ksort($this->csv_file_data);
        ksort($this->crew_database_data);

        $payment_methods_obj = new payment_methods('all');
        $payment_info_array = $payment_methods_obj->payment_methods_array;

        foreach ($this->csv_file_data as $orders_id => $orders_details) {
            $result_code_04 = 0;
            $order_found = false;
            if (is_array($this->crew_database_data[$orders_id])) {
                $compare_orders_id = $this->crew_database_data[$orders_id]['orders_id'];
                $compare_date = $this->crew_database_data[$orders_id]['record_date'];
                $compare_payment_method = $this->crew_database_data[$orders_id]['payment_method'];
                $compare_currency = $this->crew_database_data[$orders_id]['currency'];
                $compare_record_status = $this->crew_database_data[$orders_id]['record_status'];
                $compare_current_status = $this->crew_database_data[$orders_id]['current_status'];
                //04 : 	Date in Range
                $result_code_04 = 1;
                $order_found = true;
            } else {
                $order_select_sql = "	SELECT DISTINCT osh.orders_id, osh.date_added, o.payment_method, o.payment_methods_id, o.payment_methods_parent_id, 
												o.currency, osh.orders_status_id, o.orders_status as current_status, 
												(osh.date_added >= '" . $this->store_start_date . "' AND osh.date_added <= '" . $this->store_end_date . "') as date_in_range
										FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
										INNER JOIN " . TABLE_ORDERS . " AS o ON osh.orders_id=o.orders_id
										WHERE o.orders_id='" . (int) $this->csv_file_data[$orders_id]['orders_id'] . "'
										GROUP BY osh.orders_status_id
				 						ORDER BY osh.date_added DESC";
                $order_result_sql = tep_db_query($order_select_sql);
                $order_temp_array = array();
                while ($order_row = tep_db_fetch_array($order_result_sql)) {
                    $order_temp_array[$order_row['orders_status_id']] = $order_row;
                }

                if (count($order_temp_array) > 0) {
                    $order_found = true;
                    $order_row = array();
                    if (isset($order_temp_array[7])) {
                        $order_row = $order_temp_array[7];
                    } elseif (isset($order_temp_array[2])) {
                        $order_row = $order_temp_array[2];
                    } else {
                        $order_row = $order_temp_array[1];
                    }

                    $compare_orders_id = $order_row['orders_id'];
                    $compare_date = $order_row['date_added'];
                    $compare_payment_method = strtolower(str_replace(" ", "", $payment_info_array[$order_row['payment_methods_id']]->title));
                    if (in_array($order_row['payment_methods_parent_id'], $this->payment_gateways_id)) {
                        $compare_payment_method = strtolower(str_replace(" ", "", $payment_info_array[$order_row['payment_methods_parent_id']]->title));
                    } else {
                        $compare_payment_method = strtolower(str_replace(" ", "", $payment_info_array[$order_row['payment_methods_id']]->title));
                    }

                    $compare_currency = $order_row['currency'];
                    $compare_record_status = tep_get_orders_status_name($order_row['orders_status_id']);
                    $compare_current_status = tep_get_orders_status_name($order_row['current_status']);
                    //04 : 	Date in Range
                    if ($order_row['date_in_range']) {
                        $result_code_04 = 1;
                    }
                }
            }

            if ($order_found) {
                $orders_info = $this->get_orders_info($orders_id);
                $orders_amount_info = $this->get_orders_amount_info($orders_id, $orders_info);

                $compare_pg_value = $orders_amount_info['pg_value'];
                $compare_rsc_value = $orders_amount_info['rsc_value'];
                $compare_sc_value = $orders_amount_info['sc_value'];
                $compare_customers_id_value = $orders_info['customers_id'];
                $compare_sub_total_value = $orders_amount_info['sub_total'];
                $compare_nrsc_value = $orders_amount_info['nrsc_value'];
                $compare_total_value = $orders_amount_info['total_value'];
                $compare_paid_amount = $orders_amount_info['paid_amount'];

                //01 : 	Payment Gateway Matched
                $result_code_01 = 0;
                if (strtolower(str_replace(" ", "", $this->csv_file_data[$orders_id]['payment_method'])) == strtolower(str_replace(" ", "", $compare_payment_method))) {
                    $result_code_01 = 1;
                }

                //02 : 	Amount Matched
                $result_code_02 = 0;
                //check the payment_amount
                $pg_amt = number_format($this->csv_file_data[$orders_id]['order_amount_received'], $currencies->currencies[$orders_details['currency']]['decimal_places'], $currencies->currencies[$orders_details['currency']]['decimal_point'], $currencies->currencies[$orders_details['currency']]['thousands_point']);
                if ($currencies->currencies[$orders_details['currency']]['symbol_left'] . $pg_amt . $currencies->currencies[$orders_details['currency']]['symbol_right'] == $compare_paid_amount) {
                    $result_code_02 = 1;
                }

                //03 : 	Status Matched
                $result_code_03 = 0;
                if (strtolower(str_replace(" ", "", $this->csv_file_data[$orders_id]['record_status'])) == strtolower(str_replace(" ", "", $compare_record_status))) {
                    $result_code_03 = 1;
                }

                $this->combined_data[$orders_id] = array('csv_file_orders_id' => $this->csv_file_data[$orders_id]['orders_id'],
                    'csv_file_record_date' => strval($this->csv_file_data[$orders_id]['record_date']),
                    'csv_file_order_amount_received' => $this->csv_file_data[$orders_id]['order_amount_received'],
                    'csv_file_pg_code' => $this->csv_file_data[$orders_id]['payment_method'],
                    'csv_file_currency' => $this->csv_file_data[$orders_id]['currency'],
                    'csv_file_record_status' => $this->csv_file_data[$orders_id]['record_status'],
                    'crew_database_orders_id' => $compare_orders_id,
                    'crew_database_record_date' => tep_date_short($compare_date, 'Y-m-d'),
                    'crew_database_pg_value' => number_format($compare_pg_value, 4, '.', ''),
                    'crew_database_rsc_value' => number_format($compare_rsc_value, 4, '.', ''),
                    'crew_database_nrsc_value' => number_format($compare_nrsc_value, 4, '.', ''),
                    'crew_database_sc_value' => number_format($compare_sc_value, 4, '.', ''),
                    'crew_database_customers_id_value' => $compare_customers_id_value,
                    'crew_database_sub_total_value' => number_format($compare_sub_total_value, 4, '.', ''),
                    'crew_database_total_value' => number_format($compare_total_value, 4, '.', ''),
                    'crew_database_pg_code' => $compare_payment_method,
                    'crew_database_currency' => $compare_currency,
                    'crew_database_record_status' => $compare_record_status,
                    'crew_database_current_status' => $compare_current_status,
                    'result_code_01' => $result_code_01,
                    'result_code_02' => $result_code_02,
                    'result_code_03' => $result_code_03,
                    'result_code_04' => $result_code_04
                );
            } else {
                $this->combined_data[$orders_id] = array('csv_file_orders_id' => $this->csv_file_data[$orders_id]['orders_id'],
                    'csv_file_record_date' => strval($this->csv_file_data[$orders_id]['record_date']),
                    'csv_file_order_amount_received' => $this->csv_file_data[$orders_id]['order_amount_received'],
                    'csv_file_pg_code' => $this->csv_file_data[$orders_id]['payment_method'],
                    'csv_file_currency' => $this->csv_file_data[$orders_id]['currency'],
                    'csv_file_record_status' => $this->csv_file_data[$orders_id]['record_status'],
                    'crew_database_orders_id' => '',
                    'crew_database_record_date' => '',
                    'crew_database_pg_value' => '',
                    'crew_database_rsc_value' => '',
                    'crew_database_nrsc_value' => '',
                    'crew_database_total_value' => '',
                    'crew_database_pg_code' => '',
                    'crew_database_currency' => '',
                    'crew_database_record_status' => '',
                    'crew_database_current_status' => '',
                    'result_code_01' => 0,
                    'result_code_02' => 0,
                    'result_code_03' => 0,
                    'result_code_04' => 0
                );
            }
        }

        foreach ($this->crew_database_data as $orders_id => $orders_details) {
            if (!is_array($this->csv_file_data[$orders_id])) {
                $this->combined_data[$orders_id] = array('csv_file_orders_id' => '',
                    'csv_file_record_date' => '',
                    'csv_file_order_amount_received' => '',
                    'csv_file_pg_code' => '',
                    'csv_file_currency' => '',
                    'csv_file_record_status' => '',
                    'crew_database_orders_id' => $this->crew_database_data[$orders_id]['orders_id'],
                    'crew_database_record_date' => strval($this->crew_database_data[$orders_id]['record_date']),
                    'crew_database_pg_value' => $this->crew_database_data[$orders_id]['pg_value'],
                    'crew_database_sub_total' => $this->crew_database_data[$orders_id]['sub_total'],
                    'crew_database_customers_id' => $this->crew_database_data[$orders_id]['customers_id'],
                    'crew_database_rsc_value' => $this->crew_database_data[$orders_id]['rsc_value'],
                    'crew_database_nrsc_value' => $this->crew_database_data[$orders_id]['nrsc_value'],
                    'crew_database_sc_value' => $this->crew_database_data[$orders_id]['sc_value'],
                    'crew_database_total_value' => $this->crew_database_data[$orders_id]['total_value'],
                    'crew_database_pg_code' => $this->crew_database_data[$orders_id]['payment_method'],
                    'crew_database_currency' => $this->crew_database_data[$orders_id]['currency'],
                    'crew_database_record_status' => $this->crew_database_data[$orders_id]['record_status'],
                    'crew_database_current_status' => $this->crew_database_data[$orders_id]['current_status'],
                    'result_code_01' => 0,
                    'result_code_02' => 0,
                    'result_code_03' => 0,
                    'result_code_04' => 1
                );
            }
        }
    }

    function paging_array_data($combined_data, $max_record_in_a_page, $input_array) {
        global $PHP_SELF;

        $display_data = array();
        $total_record = count($combined_data);
        $total_page = ($total_record >= $max_record_in_a_page) ? $total_record / $max_record_in_a_page : 1;
        $total_page = (intval($total_page) < $total_page) ? intval($total_page) + 1 : $total_page;

        $current_page_number = 1;
        $current_page_record_start_number = 0;
        if ($total_record > 0) {
            $current_page_record_start_number = 1;
        }
        $current_page_record_end_number = ($total_record > $max_record_in_a_page) ? $max_record_in_a_page : $total_record;

        if (tep_not_null($input_array['page'])) {
            if ($input_array['page'] != 'all') {
                $current_page_number = intval($input_array['page']);
                $current_page_record_start_number = intval(($current_page_number * $max_record_in_a_page) - $max_record_in_a_page + 1);
                $current_page_record_end_number = (intval($current_page_number * $max_record_in_a_page) < $total_record) ? intval($current_page_number * $max_record_in_a_page) : $total_record;
            } else {
                if ($input_array['page'] == 'all') {
                    $current_page_number = $input_array['page'];
                    $current_page_record_start_number = 1;
                    $current_page_record_end_number = $total_record;
                }
            }
        }

        if (count($combined_data)) {
            $current_row_number = 1;
            if ($this->report_type_id == '1') {
                foreach ($combined_data as $orders_id => $orders_details) {
                    if ($current_row_number >= $current_page_record_start_number && $current_row_number <= $current_page_record_end_number) {
                        $display_data[$orders_id] = array('csv_file_orders_id' => $orders_details['csv_file_orders_id'],
                            'csv_file_record_date' => $orders_details['csv_file_record_date'],
                            'csv_file_order_amount_received' => $orders_details['csv_file_order_amount_received'],
                            'csv_file_pg_code' => $orders_details['csv_file_pg_code'],
                            'csv_file_currency' => $orders_details['csv_file_currency'],
                            'csv_file_record_status' => $orders_details['csv_file_record_status'],
                            'crew_database_orders_id' => $orders_details['crew_database_orders_id'],
                            'crew_database_record_date' => $orders_details['crew_database_record_date'],
                            'crew_database_pg_value' => $orders_details['crew_database_pg_value'],
                            'crew_database_sc_value' => $orders_details['crew_database_sc_value'],
                            'crew_database_customers_id_value' => $orders_details['crew_database_customers_id_value'],
                            'crew_database_sub_total_value' => $orders_details['crew_database_sub_total_value'],
                            'crew_database_nrsc_value' => $orders_details['crew_database_nrsc_value'],
                            'crew_database_total_value' => $orders_details['crew_database_total_value'],
                            'crew_database_pg_code' => $orders_details['crew_database_pg_code'],
                            'crew_database_currency' => $orders_details['crew_database_currency'],
                            'crew_database_record_status' => $orders_details['crew_database_current_status'],
                            'result_code_01' => $orders_details['result_code_01'],
                            'result_code_02' => $orders_details['result_code_02'],
                            'result_code_03' => $orders_details['result_code_03'],
                            'result_code_04' => $orders_details['result_code_04'],
                        );
                    }
                    $current_row_number++;
                }
            } else if ($this->report_type_id == '2') {
                $input_array['action'] = "show_report";
                foreach ($combined_data as $orders_id => $orders_details) {
                    if ($current_row_number >= $current_page_record_start_number && $current_row_number <= $current_page_record_end_number) {
                        $display_data[$orders_id] = array('order_date' => $orders_details['order_date'],
                            'order_id' => $orders_details['order_id'],
                            'pg_code' => $orders_details['pg_code'],
                            'currency' => $orders_details['currency'],
                            'pg_amount' => $orders_details['pg_amount'],
                            'pg_surcharge' => $orders_details['pg_surcharge'],
                            'current_deliver_pg_amt' => $orders_details['current_deliver_pg_amt'],
                            'current_deliver_sc_amt' => $orders_details['current_deliver_sc_amt'],
                            'current_deliver_dc_amt' => $orders_details['current_deliver_dc_amt'],
                            'previous_deliver_amt' => $orders_details['previous_deliver_amt'],
                            'balance_deliver_pg_amt' => $orders_details['balance_deliver_pg_amt'],
                            'balance_deliver_sc_amt' => $orders_details['balance_deliver_sc_amt'],
                            'balance_deliver_dc_amt' => $orders_details['balance_deliver_dc_amt'],
                            'current_refund_pg_amt' => $orders_details['current_refund_pg_amt'],
                            'current_refund_sc_amt' => $orders_details['current_refund_sc_amt'],
                            'current_refund_dc_amt' => $orders_details['current_refund_dc_amt'],
                            'current_cb_resolve_amt' => $orders_details['current_cb_resolve_amt'],
                            'current_cb_win_amt' => $orders_details['current_cb_win_amt'],
                            'current_cb_lost_amt' => $orders_details['current_cb_lost_amt'],
                            'database_total' => $orders_details['database_total'],
                            'order_pg_code' => $orders_details['order_pg_code'],
                            'usd_rate' => $orders_details['usd_rate'],
                            'order_currency' => $orders_details['order_currency'],
                            'csv_file_pg_amt' => $orders_details['csv_file_pg_amt'],
                            'csv_file_pg_surcharge_amt' => $orders_details['csv_file_pg_surcharge_amt'],
                            'csv_file_sc_amt' => $orders_details['csv_file_sc_amt'],
                            'csv_file_dc_amt' => $orders_details['csv_file_dc_amt'],
                            'csv_file_total' => $orders_details['csv_file_total'],
                            'compensated_amt' => $orders_details['compensated_amt']
                        );
                    }
                    $current_row_number++;
                }
            }
        }

        $url = tep_href_link(basename($PHP_SELF));

        $query_string = '';
        if (isset($input_array['page']))
            unset($input_array['page']);
        if (isset($input_array['osCAdminID']))
            unset($input_array['osCAdminID']);

        foreach ($input_array as $key => $value) {
            $query_string .= $key . '=' . $value . '&';
        }

        if (tep_not_null($query_string)) {
            $query_string = '?' . substr($query_string, 0, -1);
        }

        $previous_page_url = '';
        if (intval($current_page_number) - 1 >= 1) {
            $previous_page_url = $url . $query_string . '&page=' . (intval($current_page_number) - 1);
        } else {
            $previous_page_url = '';
        }

        $next_page_url = '';
        if (intval($current_page_number) + 1 <= $total_page) {
            $next_page_url = $url . $query_string . '&page=' . (intval($current_page_number) + 1);
        } else {
            $next_page_url = '';
        }

        $display_info = '<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle"><tr><td class="smallText" valign="top">Displaying <b>' . $current_page_record_start_number . '</b> to <b>' . $current_page_record_end_number . '</b> (of <b>' . $total_record . '</b> records)</td><td class="smallText" align="right">';

        if ($total_page > 1) {
            $display_info .= '<form name="pages" action="' . tep_href_link(basename($PHP_SELF)) . '" method="get">';

            if ($previous_page_url == '' || $current_page_number == 'all') {
                $display_info .= '&lt;&lt;';
            } else {
                $display_info .= '<a href="' . $previous_page_url . '" class="splitPageLink">&lt;&lt;</a>&nbsp;&nbsp;';
            }

            $display_info .= 'Page&nbsp;<select name="page" onChange="this.form.submit();">';

            for ($page_number = 1; $page_number <= $total_page; $page_number++) {
                if ($page_number == intval($current_page_number)) {
                    $display_info .= '<option value="' . $page_number . '" SELECTED>' . $page_number . '</option>';
                } else {
                    $display_info .= '<option value="' . $page_number . '">' . $page_number . '</option>';
                }
            }

            $display_info .= '</select> of ' . $total_page . '&nbsp;&nbsp;';

            if ($next_page_url == '' || $current_page_number == 'all') {
                $display_info .= '&gt;&gt;';
            } else {
                $display_info .= '<a href="' . $next_page_url . '" class="splitPageLink">&gt;&gt;</a>&nbsp;&nbsp;';
            }

            $display_info .= '<input type="hidden" name="report_type" value="' . $input_array['report_type'] . '">';
            $display_info .= '<input type="hidden" name="start_date" value="' . $input_array['start_date'] . '">';
            $display_info .= '<input type="hidden" name="end_date" value="' . $input_array['end_date'] . '">';
            $display_info .= '<input type="hidden" name="action" value="show_report"><input type="hidden" name="cont" value="1"></form>';
        }

        $display_info .= '</td></tr></table>';

        $paging_array_data = array();
        $paging_array_data['total_record'] = $total_record;
        $paging_array_data['total_page'] = $total_page;
        $paging_array_data['current_page_number'] = $current_page_number;
        $paging_array_data['current_page_record_start_number'] = $current_page_record_start_number;
        $paging_array_data['current_page_record_end_number'] = $current_page_record_end_number;
        $paging_array_data['display_data'] = $display_data;
        $paging_array_data['previous_page_url'] = $previous_page_url;
        $paging_array_data['next_page_url'] = $next_page_url;
        $paging_array_data['display_info'] = $display_info;

        return $paging_array_data;
    }
}
?>