<?php

class out_of_stock_rule {

    public $identity, $identity_email, $rule_info;

    public function __construct($identity, $identity_email) {
        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin email
    }

    public function show_rules_list($filename) {
        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
        $rule_select_sql = "SELECT ofsr.rules_id, ofsr.rules_name, ofsr.date_added, ofsr.last_modified
                            FROM " . TABLE_OUT_OF_STOCK_RULES . " AS ofsr 
                            ORDER BY ofsr.rules_id DESC";

        $show_records = MAX_DISPLAY_SEARCH_RESULTS;
        if ($show_records != "ALL") {
            $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $rule_select_sql, $rule_select_sql_numrows, true);
        }
        $rule_result_sql = tep_db_query($rule_select_sql);

        ob_start();
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr align="right">
                <td>[ <a href="<?php echo tep_href_link($filename, 'action=show_monitor_page') ?>"><?php echo LINK_SHOW_MONITOR_PAGE; ?></a> ] [ <a href="<?php echo tep_href_link($filename, 'action=new_rule') ?>"><?php echo LINK_ADD_OUT_OF_STOCK_RULE; ?></a> ]</td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr>
                            <td width="7%" class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_ID; ?></td>
                            <td class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_NAME; ?></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_SETTING; ?></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_DATE_ADDED; ?></td>
                            <td width="15%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_ACTION; ?></td>
                        </tr>
                        <?php
                        $row_count = 0;
                        while ($rule_row = tep_db_fetch_array($rule_result_sql)) {
                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                            $action_str = ' <a href="' . tep_href_link($filename, 'action=edit_rule&id=' . $rule_row["rules_id"]) . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') . '</a>
                                            <a href="javascript:void(confirm_delete(\'Out of Stock Rule - ' . $rule_row['rules_id'] . '\', \'\', \'' . tep_href_link($filename, 'action=delete_rule&rules_id=' . $rule_row["rules_id"]) . '\'))">' . tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') . '</a>
                                            <a href="' . tep_href_link($filename, 'action=show_monitor_page&rules_id=' . $rule_row["rules_id"]) . '">' . LINK_MONITOR_PAGE_BY_RULE_ID . '</a>';

                            echo '	<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                            <td>' . $rule_row['rules_id'] . '</td>
                                            <td valign="top"><a target="_blank" href="' . tep_href_link($filename, 'action=show_rules_supported_page&rules_id=' . $rule_row['rules_id']) . '">' . $rule_row['rules_name'] . '</a></td>
                                            <td valign="top" style="width:35%" id="rule_row_' . $rule_row["rules_id"] . '">' . '<a href="javascript:void(0);" onClick="show_out_of_stock_rules_by_id(' . $rule_row['rules_id'] . ', \'Show setting\', \'Hide setting\');"><span id="toggle_discount_setting_' . $rule_row['rules_id'] . '">Show setting</span></a><span id="customer_group_discount_info_' . $rule_row["rules_id"] . '"></span></td>
                                            <td align="center" valign="top" class="reportRecords">' . $rule_row['date_added'] . '</td>
                                            <td align="center" valign="top" class="reportRecords">' . $action_str . '</td>
                                        </tr>';
                            ?>

                            <?php
                            $row_count++;
                        }
                        ?>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                        <tr>
                            <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($rule_result_sql) > 0 ? "1" : "0", tep_db_num_rows($rule_result_sql), tep_db_num_rows($rule_result_sql)) : $rule_split_object->display_count($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                            <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?php
        $report_section_html = ob_get_contents();
        ob_end_clean();

        return $report_section_html;
    }

    public function add_rule($filename) {
        $report_section_html = '';

        ob_start();
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr>
                <td>
                    <?php echo tep_draw_form('add_rules_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=insert_rule', 'post', '') ?>
                    <table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">

                        <tr>
                            <td class="main" valign="top"><?php echo ENTRY_OUT_OF_STOCK_RULE_NAME; ?></td>
                            <td class="main"><?php echo tep_draw_input_field('rules_name', '', 'size="40" id="rules_name"'); ?></td>
                        </tr>

                        <tr>
                            <td valign="top" class="main">&nbsp;</td>
                            <td>
                                <table width="60%" cellspacing="1" cellpadding="0" border="0">
                                    <tbody><tr>
                                            <td width="30%" class="commonBoxHeading">&nbsp;</td>
                                            <td class="commonBoxHeading" colspan="2">
                                                <table cellspacing="0" cellpadding="2" border="0">
                                                    <tbody><tr>

                                                            <td class="commonBoxHeading" ><?php echo TABLE_HEADING_AUTO_CALCULATE; ?></td>
                                                            <td class="commonBoxHeading" >&nbsp;</td>
                                                        </tr>
                                                    </tbody></table>
                                            </td>
                                        </tr>

                                        <?php
                                        $row_count = 0;

                                        $customers_groups_qty_limit_query = "	SELECT cg.customers_groups_id, cg.customers_groups_name
                                                                                FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                                                                ORDER BY cg.sort_order, cg.customers_groups_name";
                                        $customers_groups_qty_limit_query = tep_db_query($customers_groups_qty_limit_query);
                                        $count_result_qty_limit = tep_db_num_rows($customers_groups_qty_limit_query);
                                        while ($customers_groups = tep_db_fetch_array($customers_groups_qty_limit_query)) {
                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                            $customers_groups_id = $customers_groups['customers_groups_id'];
                                            $out_of_stock_in_percentage = isset($customers_groups['out_of_stock_in_percentage']) ? number_format($customers_groups['out_of_stock_in_percentage'], 2, '.', '') : '0';
                                            ?>

                                            <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">


                                                <td class="main"  align="center">
                                                    <?php
                                                    echo $customers_groups['customers_groups_name'];
                                                    ?>
                                                </td>
                                                <td> 
                                                    <?php echo tep_draw_input_field('customer_group_out_of_stock_in_percentage[' . $customers_groups_id . ']', $out_of_stock_in_percentage, 'size="8" id="customer_group_out_of_stock_in_percentage_' . $customers_groups_id . '" '); ?>
                                                </td>


                                            </tr>
                                            <?php
                                            $row_count++;
                                        }
                                        ?>


                                    </tbody></table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="2">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="middle">
                                            <?php echo tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, 'onClick="return form_checking();"', 'inputButton') ?>
                                            <?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton') ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

        </table>


        <script language="javascript">
            function form_checking() {
                var rules_name = document.getElementById('rules_name').value;
                if (rules_name.length > 0) {
                    return true;
                } else {
                    alert('<?php echo ERROR_OUT_OF_STOCK_MISSING_RULE_NAME; ?>');
                    document.getElementById('rules_name').focus();
                    document.getElementById('rules_name').select();
                    return false;
                }


            }

        </script>

        <?php
        $report_section_html = ob_get_contents();
        ob_end_clean();

        return $report_section_html;
    }

    public function edit_rule($filename, $id) {
        $report_section_html = '';
        $rule_select_sql = "SELECT ofsr.rules_id, ofsr.rules_name
                            FROM " . TABLE_OUT_OF_STOCK_RULES . " AS ofsr 
                            WHERE ofsr.rules_id = '" . tep_db_input($id) . "' ";
        $rule_result_sql = tep_db_query($rule_select_sql);

        if ($rule_row = tep_db_fetch_array($rule_result_sql)) {

            $rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.out_of_stock_in_percentage
                                        FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                        LEFT JOIN " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.customers_groups_id
                                        AND ofsrcg.out_of_stock_rules_id = '" . tep_db_input($id) . "'
                                        ORDER BY cg.sort_order, cg.customers_groups_name";
            $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);

            ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr>
                    <td>
                        <?php echo tep_draw_form('edit_rules_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=update_rule', 'post', '') ?>
                        <table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">

                            <tr>
                                <td class="main" valign="top"><?php echo ENTRY_OUT_OF_STOCK_RULE_NAME; ?></td>
                                <td class="main"><?php echo tep_draw_input_field('rules_name', $rule_row['rules_name'], 'size="40" id="rules_name"'); ?></td>
                            </tr>

                            <tr>
                                <td valign="top" class="main">&nbsp;</td>
                                <td>
                                    <table width="60%" cellspacing="1" cellpadding="0" border="0">
                                        <tbody><tr>
                                                <td width="30%" class="commonBoxHeading">&nbsp;</td>
                                                <td class="commonBoxHeading" colspan="2">
                                                    <table cellspacing="0" cellpadding="2" border="0">
                                                        <tbody><tr>

                                                                <td class="commonBoxHeading" ><?php echo TABLE_HEADING_AUTO_CALCULATE; ?></td>
                                                                <td class="commonBoxHeading" >&nbsp;</td>
                                                            </tr>
                                                        </tbody></table>
                                                </td>

                                            </tr>

                                            <?php
                                            $row_count = 0;

                                            while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                $customers_groups_id = $customers_groups['customers_groups_id'];
                                                $out_of_stock_in_percentage = isset($customers_groups['out_of_stock_in_percentage']) ? number_format($customers_groups['out_of_stock_in_percentage'], 2, '.', '') : '0';
                                                ?>

                                                <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                                                    <td class="main"  align="center">
                                                        <?php
                                                        echo $customers_groups['customers_groups_name'];
                                                        ?>
                                                    </td>
                                                    <td> 
                                                        <?php echo tep_draw_input_field('customer_group_out_of_stock_in_percentage[' . $customers_groups_id . ']', $out_of_stock_in_percentage, 'size="8" id="customer_group_out_of_stock_in_percentage_' . $customers_groups_id . '" '); ?>
                                                    </td>
                                                </tr>
                                                <?php
                                                $row_count++;
                                            }
                                            ?>

                                        </tbody></table>
                                </td>
                            </tr>


                            <tr>
                                <td colspan="2">
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="middle">
                                                <?php echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_INSERT, 'onClick="return form_checking();"', 'inputButton') ?>
                                                <?php echo tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton') ?>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </table>

            <script language="javascript">
                function form_checking() {
                    var rules_name = document.getElementById('rules_name').value;
                    if (rules_name.length > 0) {
                        return true;
                    } else {
                        alert('<?php echo ERROR_OUT_OF_STOCK_MISSING_RULE_NAME; ?>');

                        return false;
                    }


                }

            </script>


            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();
        }
        return $report_section_html;
    }

    public function insert_rule($input_array, &$messageStack) {
        $error = false;
        $rule_name = tep_db_prepare_input($input_array['rules_name']);
        $option_array = $input_array['customer_group_out_of_stock_in_percentage'];

        if (!tep_not_null($rule_name)) {
            $error = true;
            $messageStack->add_session(ERROR_OUT_OF_STOCK_MISSING_RULE_NAME, 'error');
        }

        if (!$error) {
            $rules_data_array = array('rules_name' => $rule_name,
                'date_added' => 'now()',
                'last_modified' => 'now()',
            );
            tep_db_perform(TABLE_OUT_OF_STOCK_RULES, $rules_data_array);

            $out_of_stock_rules_id = tep_db_insert_id();

            foreach ($option_array as $customer_group_id => $qty_limit_value) {
                if ($customer_group_id && $out_of_stock_rules_id) {
                    $data_array = array('customers_groups_id' => (int) $customer_group_id,
                        'out_of_stock_rules_id' => $out_of_stock_rules_id,
                        'out_of_stock_in_percentage' => $qty_limit_value,
                    );
                    tep_db_perform(TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES, $data_array);
                }
            }
            unset($data_array);
        }

        if (!$error) {
            $messageStack->add_session(MESSAGE_INSERT_SUCCESS, 'success');
            return true;
        } else {
            $messageStack->add_session(MESSAGE_INSERT_ERROR, 'error');
            return false;
        }
    }

    public function update_rule($input_array, &$messageStack) {
        $error = false;
        $rule_id = tep_db_prepare_input($input_array['id']);
        $rule_name = tep_db_prepare_input($input_array['rules_name']);
        $option_array = $input_array['customer_group_out_of_stock_in_percentage'];

        if (!tep_not_null($rule_name)) {
            $error = true;
            $messageStack->add_session(ERROR_OUT_OF_STOCK_MISSING_RULE_NAME, 'error');
        }

        if (!$error) {
            $exist_rule_name = '';

            $check_exist_rule_select_sql = "	SELECT rules_name
                                                FROM " . TABLE_OUT_OF_STOCK_RULES . " 
                                                WHERE   rules_id = '" . $rule_id . "'";
            $check_exist_rule_result_sql = tep_db_query($check_exist_rule_select_sql);
            if ($check_exist_rule_result_row = tep_db_fetch_array($check_exist_rule_result_sql)) {
                $exist_rule_name = $check_exist_rule_result_row['rules_name'];
            }

            if ($exist_rule_name !== $rule_name) {
                $rule_data_array = array(
                    'rules_name' => $rule_name,
                    'last_modified' => 'now()',
                );
                tep_db_perform(TABLE_OUT_OF_STOCK_RULES, $rule_data_array, 'update', 'rules_id="' . tep_db_input($rule_id) . '"');
            }

            $check_exist_options_result_row_array = array();

            $check_exist_options_select_sql = "	SELECT customers_groups_id, out_of_stock_in_percentage
                                                FROM " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " 
                                                WHERE   out_of_stock_rules_id = '" . $rule_id . "'";
            $check_exist_options_result_sql = tep_db_query($check_exist_options_select_sql);
            while ($check_exist_options_result_row = tep_db_fetch_array($check_exist_options_result_sql)) {
                $check_exist_options_result_row_array[$check_exist_options_result_row['customers_groups_id']] = $check_exist_options_result_row['out_of_stock_in_percentage'];
            }

            if (is_array($option_array)) {
                foreach ($option_array as $customer_group_id => $qty_limit_value) {
                    if ($customer_group_id && $rule_id) {
                        $insert_new = false;

                        if (!isset($check_exist_options_result_row_array[$customer_group_id])) {
                            $data_array = array(
                                'customers_groups_id' => (int) $customer_group_id,
                                'out_of_stock_rules_id' => $rule_id,
                                'out_of_stock_in_percentage' => $qty_limit_value,
                            );
                            tep_db_perform(TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES, $data_array);
                        } else {
                            $rule_option_update_array = array(
                                'out_of_stock_in_percentage' => $qty_limit_value,
                            );

                            if (bccomp($check_exist_options_result_row_array[$customer_group_id], $qty_limit_value, 4) !== 0) {
                                tep_db_perform(TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES, $rule_option_update_array, 'update', 'out_of_stock_rules_id="' . tep_db_input($rule_id) . '" AND customers_groups_id="' . (int) $customer_group_id . '"');
                            }
                        }
                    }
                }
            }
        }

        if (!$error) {
            $messageStack->add_session(MESSAGE_UPDATE_SUCCESS, 'success');
            return $rule_id;
        } else {
            $messageStack->add_session(MESSAGE_UPDATE_ERROR, 'error');
            return false;
        }
    }

    public function delete_rule($input_array, &$messageStack) {
        global $memcache_obj;

        $rules_id = tep_db_prepare_input($input_array['rules_id']);

        $outstock_delete_sql = "DELETE FROM " . TABLE_OUT_OF_STOCK_RULES . " WHERE rules_id = '" . tep_db_input($rules_id) . "'";
        tep_db_query($outstock_delete_sql);

        $outstock_prod_delete_sql = "DELETE FROM " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " WHERE out_of_stock_rules_id = '" . tep_db_input($rules_id) . "'";
        tep_db_query($outstock_prod_delete_sql);

        $prod_extra_info_select_sql = "	SELECT products_id
                                               FROM " . TABLE_PRODUCTS_EXTRA_INFO . "   
					       WHERE products_extra_info_key = 'out_of_stock_rules_id' AND products_extra_info_value = '" . tep_db_input($rules_id) . "'";

        $prod_extra_info_result_sql = tep_db_query($prod_extra_info_select_sql);

        while ($prod_extra_info_result_row = tep_db_fetch_array($prod_extra_info_result_sql)) {
            $products_id = (int) $prod_extra_info_result_row['products_id'];

            $cg_purchase_control_select_sql = " SELECT purchase_limit, customers_groups_id
                                                FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " 
                                                WHERE  products_id = '" . tep_db_input($products_id) . "'";
            $cg_purchase_control_result_query = tep_db_query($cg_purchase_control_select_sql);
            while ($cg_purchase_control_result = tep_db_fetch_array($cg_purchase_control_result_query)) {
                # Clear cache
                #key:customers_groups_purchase_control/products_id/xxx/customers_groups_id/xxx
                $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . '/products_id/' . $products_id . '/customers_groups_id/' . $cg_purchase_control_result['customers_groups_id'], 0);
            }

            $cg_purchase_control_delete_sql = "DELETE FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " WHERE  products_id = '" . $products_id . "'";
            tep_db_query($cg_purchase_control_delete_sql);

            $prod_extra_info_rule_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_extra_info_key = 'out_of_stock_rules_id' AND products_id = '" . $products_id . "'";
            tep_db_query($prod_extra_info_rule_delete_sql);

            $prod_extra_info_stock_qty_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_extra_info_key = 'stock_quantity' AND products_id = '" . $products_id . "'";
            tep_db_query($prod_extra_info_stock_qty_delete_sql);
        }

        $messageStack->add_session(MESSAGE_DELETE_SUCCESS, 'success');
        return true;
    }

    public function remove_product_from_out_of_stock_rule($input_array, &$messageStack, $display_msg = true) {
        global $memcache_obj, $login_id;

        $rules_id = tep_db_prepare_input($input_array['rules_id']);
        $products_id = tep_db_prepare_input($input_array['products_id']);
        $stock_qty = 0;

        $cg_purchase_control_select_sql = " SELECT purchase_limit, customers_groups_id
                                            FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " 
                                            WHERE  products_id = '" . tep_db_input($products_id) . "'";
        $cg_purchase_control_result_query = tep_db_query($cg_purchase_control_select_sql);
        while ($cg_purchase_control_result = tep_db_fetch_array($cg_purchase_control_result_query)) {
            # Clear cache
            #key:customers_groups_purchase_control/products_id/xxx/customers_groups_id/xxx
            $memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . '/products_id/' . $products_id . '/customers_groups_id/' . $cg_purchase_control_result['customers_groups_id'], 0);
        }

        $prod_extra_info_qty_sql = "SELECT products_extra_info_value 
                                    FROM " . TABLE_PRODUCTS_EXTRA_INFO . "
                                    WHERE products_extra_info_key = 'stock_quantity' AND  products_id = '" . (int) $products_id . "'";
        $purchase_control_result_query = tep_db_query($prod_extra_info_qty_sql);
        if ($purchase_control_result = tep_db_fetch_array($purchase_control_result_query)) {
            $stock_qty = $purchase_control_result['products_extra_info_value'];
        }

        $cg_purchase_control_delete_sql = "DELETE FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " WHERE  products_id = '" . (int) $products_id . "'";
        tep_db_query($cg_purchase_control_delete_sql);

        $prod_extra_info_rule_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_extra_info_key = 'out_of_stock_rules_id' AND  products_id = '" . (int) $products_id . "'";
        tep_db_query($prod_extra_info_rule_delete_sql);

        $prod_extra_info_qty_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_extra_info_key = 'stock_quantity' AND  products_id = '" . (int) $products_id . "'";
        tep_db_query($prod_extra_info_qty_delete_sql);

        require_once(DIR_WS_CLASSES . 'log.php');
        $log_object = new log_files($login_id);
        $log_object->insert_log($products_id, 'out_of_stock_rules_id', $rules_id, '-', LOG_PRODUCT_OUT_OF_STOCK_RULE_UPDATE, 'Rules removed.', $_SESSION['login_email_address']);
        $log_object->insert_log($products_id, 'stock_quantity', $stock_qty, '-', LOG_PRODUCT_STOCK_QUANTITY_UPDATE, 'Rules removed.', $_SESSION['login_email_address']);
        unset($log_object);

        if ($display_msg) {
            $messageStack->add_session(MESSAGE_DELETE_SUCCESS, 'success');
        }

        return true;
    }

    public function get_out_of_stock_rules_by_id($id) {
        $rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.out_of_stock_in_percentage
								FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                                                LEFT JOIN " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.customers_groups_id
								AND ofsrcg.out_of_stock_rules_id = '" . tep_db_input($id) . "'
								ORDER BY cg.sort_order, cg.customers_groups_name";

        $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);

        while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
            $customers_groups_id = $customers_groups['customers_groups_id'];
            $out_of_stock_in_percentage = isset($customers_groups['out_of_stock_in_percentage']) ? number_format($customers_groups['out_of_stock_in_percentage'], 2, '.', '') : '0';
            ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">

                <tr height="40px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                    <td class="main"  align="center">
                        <?php
                        echo $customers_groups['customers_groups_name'];
                        ?>
                    </td>
                    <td> 
                        <?php echo $out_of_stock_in_percentage; ?>
                    </td>
                </tr>
                <?php
                $row_count++;
            }

            $report_section_html = ob_get_contents();
            ob_end_clean();
            return $report_section_html;
        }

        public function get_out_of_stock_rules() {
            $out_of_stock_rules_select_sql = "SELECT ofsr.rules_id, ofsr.rules_name
							FROM " . TABLE_OUT_OF_STOCK_RULES . " AS ofsr
							WHERE 1
							ORDER BY  ofsr.rules_name ASC";

            $out_of_stock_rules_result_sql = tep_db_query($out_of_stock_rules_select_sql);
            $out_of_stock_rules_set_array = array();
            while ($out_of_stock_rules = tep_db_fetch_array($out_of_stock_rules_result_sql)) {
                $out_of_stock_rules_set_array[] = array('id' => $out_of_stock_rules['rules_id'], 'text' => $out_of_stock_rules['rules_name']);
            }

            return $out_of_stock_rules_set_array;
        }

        public function get_rules_name($rules_id) {
            if (!isset($this->rule_info[$rules_id]['rules_name'])) {
                $rule_name_sql = "SELECT rules_name FROM " . TABLE_OUT_OF_STOCK_RULES . " WHERE rules_id = '" . tep_db_input($rules_id) . "'";
                $rule_name_result = tep_db_query($rule_name_sql);
                if ($rule_name_row = tep_db_fetch_array($rule_name_result)) {
                    $this->rule_info[$rules_id]['rules_name'] = $rule_name_row['rules_name'];
                }
            }

            return isset($this->rule_info[$rules_id]['rules_name']) ? $this->rule_info[$rules_id]['rules_name'] : '';
        }

        public function show_monitor_page($filename) {
            $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
            $rules_id = isset($_GET['rules_id']) ? $_GET['rules_id'] : 0;
            $customers_groups_purchase_select_sql = "SELECT cgpc.customers_groups_id, cgpc.products_id,  
                                                     pei.products_extra_info_value as rules_id, pei2.products_extra_info_value as stock_quantity
                                                    FROM  " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " AS cgpc
                                                    INNER JOIN " . TABLE_PRODUCTS . " AS p ON cgpc.products_id = p.products_id
                                                    INNER JOIN " . TABLE_PRODUCTS_EXTRA_INFO . "  AS pei ON cgpc.products_id = pei.products_id
                                                        AND pei.products_extra_info_key = 'out_of_stock_rules_id'
                                                    INNER JOIN " . TABLE_PRODUCTS_EXTRA_INFO . " AS pei2 ON cgpc.products_id = pei2.products_id
                                                        AND pei2.products_extra_info_key = 'stock_quantity'
                                                    INNER JOIN " . TABLE_CUSTOMERS_GROUPS . "  AS cg ON cgpc.customers_groups_id = cg.customers_groups_id 
                                                    WHERE cgpc.out_of_stock_flag = 1 AND products_quantity > 0";
            if ($rules_id === 0) {
                //
            } else if ($rules_id) {
                $customers_groups_purchase_select_sql.= " AND pei.products_extra_info_value ='" . tep_db_prepare_input($rules_id) . "'";
            }
            $customers_groups_purchase_select_sql.= " GROUP BY cgpc.products_id ORDER BY cgpc.out_of_stock_datetime DESC";

            $show_records = MAX_DISPLAY_SEARCH_RESULTS_RULE_MONITOR_PAGE;

            if ($show_records != "ALL") {
                $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $customers_groups_purchase_select_sql, $customers_groups_purchase_select_sql_numrows, true);
            }
            $customers_groups_purchase_result_sql = tep_db_query($customers_groups_purchase_select_sql);

            ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr align="right">
                    <td><?php echo tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, ''), '', 'inputButton') ?></td>
                </tr>

                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                            <tr>
                                <td width="10%" class="reportBoxHeading"><?php echo TABLE_HEADING_PRODUCT_ID; ?></td>
                                <td width="20%" class="reportBoxHeading"><?php echo TABLE_HEADING_PRODUCT_NAME; ?></td>
                                <td class="reportBoxHeading"><?php echo TABLE_HEADING_CUSTOMER_GROUP; ?></td>
                                <td width="10%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_CUSTOMER_CURRENT_AVAILABLE_QTY; ?></td>
                                <td width="10%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_NAME; ?></td>
                            </tr>
                            <?php
                            $row_count = 0;
                            while ($rule_row = tep_db_fetch_array($customers_groups_purchase_result_sql)) {
                                $prod_id = $rule_row['products_id'];
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                $prod_url = tep_href_link(FILENAME_CATEGORIES, 'cPath=' . tep_get_product_cPath($prod_id) . '&pID=' . $prod_id . '&action=new_product');
                                $rule_url = tep_href_link(FILENAME_OUT_OF_STOCK_RULE, 'action=edit_rule&id=' . $rule_row['rules_id']);


                                echo '			<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                                                <td><a target="_blank" href="' . $prod_url . '">' . $prod_id . '</a></td>      
							   	<td valign="top">' . tep_get_products_name($prod_id, 1) . '</td>
								<td valign="top" class="reportRecords">' . $this->get_customer_group_name_with_purchase_limit($prod_id) . '</td>
								<td align="center" valign="top" class="reportRecords">' . $rule_row['stock_quantity'] . '</td>   
                                                                <td align="center" valign="top" class="reportRecords"><a target="_blank" href="' . $rule_url . '">' . $this->get_rules_name($rule_row['rules_id']) . '</a></td> 
							  </tr>';
                                ?>


                                <?php
                                $row_count++;
                            }
                            ?>

                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                            <tr>
                                <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($customers_groups_purchase_result_sql) > 0 ? "1" : "0", tep_db_num_rows($customers_groups_purchase_result_sql), tep_db_num_rows($customers_groups_purchase_result_sql)) : $rule_split_object->display_count($customers_groups_purchase_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                                <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($customers_groups_purchase_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </table>


            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();

            return $report_section_html;
        }

        public function get_customer_group_name_with_purchase_limit($prod_id) {
            $return = '';
            $select_sql = " SELECT cgpc.purchase_limit, cg.customers_groups_name
                        FROM  " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " AS cgpc
                        INNER JOIN " . TABLE_CUSTOMERS_GROUPS . "  AS cg ON cgpc.customers_groups_id = cg.customers_groups_id 
                        WHERE cgpc.out_of_stock_flag =1 AND cgpc.products_id = '" . tep_db_input($prod_id) . "'
                        ORDER BY cg.sort_order, cg.customers_groups_name ASC";

            $result_sql = tep_db_query($select_sql);
            while ($row = tep_db_fetch_array($result_sql)) {
                $return.= $row['customers_groups_name'] . '(' . $row['purchase_limit'] . '), ';
            }
            return rtrim($return, ', ');
        }

        public function show_rules_supported_page($filename, $rules_id) {
            $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;

            $customers_groups_purchase_select_sql = "SELECT pei.products_id, 
                                                        oss.rules_id, oss.rules_name,  pd.products_name
                                                    FROM " . TABLE_PRODUCTS_EXTRA_INFO . "  AS pei
                                                    INNER JOIN " . TABLE_OUT_OF_STOCK_RULES . "  AS oss ON oss.rules_id = pei.products_extra_info_value  AND pei.products_extra_info_key = 'out_of_stock_rules_id'
                                                    INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . "  AS pd ON pd.products_id = pei.products_id AND pd.language_id =1 AND oss.rules_id =  '" . tep_db_input($rules_id) . "'";


            $show_records = MAX_DISPLAY_SEARCH_RESULTS_RULE_MONITOR_PAGE;

            if ($show_records != "ALL") {
                $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $customers_groups_purchase_select_sql, $customers_groups_purchase_select_sql_numrows, true);
            }
            $customers_groups_purchase_result_sql = tep_db_query($customers_groups_purchase_select_sql);

            ob_start();
            ?>
            <table width="100%" border="0" cellspacing="2" cellpadding="2">
                <tr align="right">
                    <td><?php echo tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, ''), '', 'inputButton'); ?></td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                            <tr>
                                <td width="12%" class="reportBoxHeading"><?php echo TABLE_HEADING_PRODUCT_ID; ?></td>
                                <td class="reportBoxHeading"><?php echo TABLE_HEADING_PRODUCT_NAME; ?></td>
                                <td class="reportBoxHeading"><?php echo TABLE_HEADING_OUT_OF_STOCK_RULE_ACTION; ?></td>
                            </tr>
                            <?php
                            $row_count = 0;
                            while ($rule_row = tep_db_fetch_array($customers_groups_purchase_result_sql)) {
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                $prod_url = tep_href_link(FILENAME_CATEGORIES, 'cPath=' . tep_get_product_cPath($rule_row['products_id']) . '&pID=' . $rule_row['products_id'] . '&action=new_product');
                                $remove_url = tep_href_link($filename, 'action=remove_product_from_out_of_stock_rule&rules_id=' . $rule_row['rules_id'] . '&products_id=' . $rule_row['products_id']);


                                echo '			<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
								 
                                                                <td><a target="_blank" href="' . $prod_url . '">' . $rule_row['products_id'] . '</a></td>      
							   	<td valign="top">' . $rule_row['products_name'] . '</td>
                                                                <td><a href="javascript:void(confirm_delete(\'Product ID - ' . $rule_row['products_id'] . ' With Rule ID - ' . $rule_row['rules_id'] . ' \', \'\', \'' . $remove_url . '\'))">' . TABLE_HEADING_REMOVE_PRODUCT . '</a></td>  
							  </tr>';
                                ?>

                                <tr>
                                    <td colspan="3" id="customer_group_discount_info1_<?php echo $rule_row["rules_id"]; ?>">
                                    </td>
                                </tr>
                                <?php
                                $row_count++;
                            }
                            ?>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                            <tr>
                                <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($customers_groups_purchase_result_sql) > 0 ? "1" : "0", tep_db_num_rows($customers_groups_purchase_result_sql), tep_db_num_rows($customers_groups_purchase_result_sql)) : $rule_split_object->display_count($customers_groups_purchase_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                                <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($customers_groups_purchase_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </table>

            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();

            return $report_section_html;
        }

    }
?>