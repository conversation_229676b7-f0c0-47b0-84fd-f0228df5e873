<?php

include_once('includes/classes/pinyin.php');

class page_template {
    const DEFAULT_LANGUAGES_ID = 1;
    
    public  $data,
            $id,
            $cPath_array,
            $language_id,
            $game_templates_key,
            $template_id,
            
            $game_detail_options_array = array(),
            $game_value_array = array(),
            $game_detail_value_array = array(),
            $game_info_array = array(),
            $category_setting_array = array(),
            $language_list_array = array();
    
	public $game_db_config = array ( 'esrb', 'genre', 'platform', 'language', 'region' );
    // array index is the template id type
    public $game_templates_array = array(
        0 => array('id' => 'GAMES_LANDING', 'text' => 'CATEGORY'),
        1 => array('id' => 'PRODUCT_LANDING', 'text' => 'PRODUCT'),
        2 => array('id' => 'CATEGORY_LANDING', 'text' => 'GAME')
    );
    
    public function __construct($id = '', $game_templates_key = '', $cPath_array = array()) {
        $this->id = $id;
        $this->game_templates_key = $game_templates_key;
        $this->cPath_array = $cPath_array;
//        $this->language_id = $language_id;
	}
    
    public function getCategoryDescription($categories_id, $language_id = 1) {
        $return_str = '';

        $categories_select_sql = "	SELECT categories_name
									FROM " . TABLE_CATEGORIES_DESCRIPTION . "
									WHERE categories_id = '" . (int)$categories_id . "'
										AND categories_name <> ''
										AND (IF (language_id = '" . (int)$language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
																									FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																									WHERE categories_id = '" . (int)$categories_id . "'
																										AND language_id = '" . (int)$language_id . "'
																										AND categories_name <> ''), 0, language_id = '" . (int)$_SESSION['default_languages_id'] . "')))";
        $categories_result_sql = tep_db_query($categories_select_sql);
        if ($categories_row = tep_db_fetch_array($categories_result_sql)) {
            $return_str = $categories_row['categories_name'];
        }
        
        return $return_str;
    }
    
    public function getCategoryList($games_array = array()) {
        $categories_structure_select_sql = "SELECT categories_structures_value 
                                            FROM " . TABLE_CATEGORIES_STRUCTURES . "
                                            WHERE categories_structures_key = 'games'";
        $categories_structure_result_sql = tep_db_query($categories_structure_select_sql);
        $categories_structure_row = tep_db_fetch_array($categories_structure_result_sql);
        if(tep_not_null($categories_structure_row)){
            $categories_select_sql = "	SELECT c.categories_id, cd.categories_name 
                                        FROM " . TABLE_CATEGORIES . " AS c 
                                        INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
                                            ON (c.categories_id = cd.categories_id AND cd.language_id = '1')
                                        WHERE c.categories_id IN (" . $categories_structure_row['categories_structures_value'] . ") 
                                        ORDER BY cd.categories_name, c.sort_order";
            $categories_result_sql = tep_db_query($categories_select_sql);
            while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                $games_array[] = array(
                    'id' => $categories_row['categories_id'], 
                    'text' => $categories_row['categories_name']
                );
            }
        }
        
        return $games_array;
    }
    
    public function getTemplateCategoryList($games_array = array()) {
        $categories_id_array = array();
        
        $select_sql = "	SELECT id 
                        FROM frontend_template 
                        WHERE id_type = 0";
        $result_sql = tep_db_query($select_sql);
        while ($row = tep_db_fetch_array($result_sql)) {
            $categories_id_array[] = $row['id'];
        }
        
        if(tep_not_null($categories_id_array)){
            $categories_select_sql = "	SELECT c.categories_id, cd.categories_name 
                                        FROM " . TABLE_CATEGORIES . " AS c 
                                        INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
                                            ON (c.categories_id = cd.categories_id AND cd.language_id = '1')
                                        WHERE c.categories_id IN (" . implode(',', $categories_id_array) . ") 
                                        ORDER BY c.sort_order, cd.categories_name";
            $categories_result_sql = tep_db_query($categories_select_sql);
            while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                $games_array[] = array(
                    'id' => $categories_row['categories_id'], 
                    'text' => $categories_row['categories_name']
                );
            }
        }
        
        return $games_array;
    }
    
    public function getTagInfo($tag_id, $language_id = 0) {
        $return_str = '';
        
        $sql = "	SELECT tag_title
                    FROM categories_tag_description 
                    WHERE tag_id = " . $tag_id . "
                        AND language_id = 1";
        $result_sql = tep_db_query($sql);
        if ($rows = tep_db_fetch_array($result_sql)) {
            $return_str = $rows['tag_title'];
        }
        
        return $return_str;
    }
    
    public function getCategoryTag($tpl_key = '', $games_array = array()) {
        $tpl_key = $tpl_key !== '' ? $tpl_key : $this->game_templates_key;
        
        switch ($tpl_key) {
            case 0: // edit Category tpl
                $sql = "	SELECT (COUNT(parent.tag_key) - 1) AS depth, node.tag_id, node.tag_key
                            FROM categories_tag AS node,
                                categories_tag AS parent
                            WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                                AND node.tag_id > 1
                            GROUP BY node.tag_key
                            ORDER BY node.tag_lft";
                $result_sql = tep_db_query($sql);
                while ($rows = tep_db_fetch_array($result_sql)) {
                    $rows['tag_title'] = $this->getTagInfo($rows['tag_id'], 1);
                    $games_array[] = $rows;
                }
                break;
        }
        
        return $games_array;
    }
    
    public function getGameDetailOptions($type = '') {
        if ($this->game_detail_options_array == array()) {
            foreach ($this->game_db_config as $cftype) {
                $cftable = strtoupper($cftype);
                $main_table = 'TABLE_GAME_' . $cftable;
                $desc_table = 'TABLE_GAME_' . $cftable . '_DESCRIPTION';

                $game_config_select_sql = "	SELECT t2.game_{$cftype}_id AS id, t2.game_{$cftype}_description AS description
                                            FROM " . constant($main_table) . " AS t1 
                                            LEFT JOIN " . constant($desc_table) . " AS t2 
                                                ON t2.game_{$cftype}_id = t1.game_{$cftype}_id 
                                            WHERE t2.language_id = '" . self::DEFAULT_LANGUAGES_ID . "' 
                                            ORDER BY t1.sort_order";
                $game_config_result_sql = tep_db_query($game_config_select_sql);
                while ($game_config_rows = tep_db_fetch_array($game_config_result_sql)) {
                    $this->game_detail_options_array[$cftype][] = array (	
                        'id' => $game_config_rows['id'], 
                        'text' => $game_config_rows['description']
                    );
                }
            }
        }
        
        return isset($this->game_detail_options_array[$type]) ? $this->game_detail_options_array[$type] : $this->game_detail_options_array;
    }
    
    public function getGameValues($key, $default = '') {
        if ($this->game_value_array == array()) {
            $select_game_sql = "	SELECT *
                                    FROM frontend_template 
                                    WHERE tpl_id = '" . $this->getTemplateID() . "'";
            $select_game_sql_res = tep_db_query($select_game_sql);
            $this->game_value_array = tep_db_fetch_array($select_game_sql_res);
        }
        
        return isset($this->game_value_array[$key]) ? $this->game_value_array[$key] : $default;
    }

    public function getGameBlogUrl($key){
        $select_game_blog_sql = "    SELECT custom_url
                                FROM game_blog
                                WHERE game_blog_id = '" . $key . "'";
        $get_game_blog_result_sql = tep_db_query($select_game_blog_sql);
        $results = tep_db_fetch_array($get_game_blog_result_sql);

        return isset($results['custom_url']) ? $results['custom_url'] : '';

    }
    
    public function getGameDetailValues($key, $language_id = '', $default = '') {
        $language_id = $language_id !== '' ? $language_id : $this->language_id;
        
        if (!isset($this->game_detail_value_array[$language_id])) {
            $select_game_details_sql = "	SELECT *
                                            FROM frontend_template_lang 
                                            WHERE tpl_id = '" . $this->getTemplateID() . "' 
                                                AND language_id = '" . $language_id . "'";
            $select_game_details_sql_res = tep_db_query($select_game_details_sql);
            $this->game_detail_value_array[$language_id] = tep_db_fetch_array($select_game_details_sql_res);
        }
        
        return isset($this->game_detail_value_array[$language_id][$key]) ? $this->game_detail_value_array[$language_id][$key] : $default;
    }
    
    public function getGameInfo($key, $default = '') {
        if (!isset($this->game_info_array[$key])) {
            $select_sql = "	SELECT game_info_id 
                            FROM frontend_template_to_game_info 
                            WHERE tpl_id = '" . $this->getTemplateID() . "'
                                AND game_info_type = '" . $key . "'";
            $result_sql = tep_db_query($select_sql);
            while ($rows = tep_db_fetch_array($result_sql)) {
                $this->game_info_array[$key][] = $rows['game_info_id'];
            }
        }
        
        return isset($this->game_info_array[$key]) ? $this->game_info_array[$key] : $default;
    }
    
    public function getGameKeyword($language_id = '', $default = '') {
        $language_id = $language_id !== '' ? $language_id : $this->language_id;
        
        $get_game_details_sql = "	SELECT game_keyword
                                    FROM ".TABLE_CATEGORIES_GAME_DETAILS." 
                                    WHERE categories_id = '" . $this->id . "' 
                                        AND language_id = '" . $language_id . "'";
        $get_game_details_result_sql = tep_db_query($get_game_details_sql);
        $game_detail = tep_db_fetch_array($get_game_details_result_sql);

        return isset($game_detail['game_keyword']) ? $game_detail['game_keyword'] : $default;
    }
    
    public function getGameList($tpl_key = '', $games_array = array()) {
        $tpl_key = $tpl_key !== '' ? $tpl_key : $this->game_templates_key;
        
        switch ($tpl_key) {
            case 0: // edit Category tpl
                $games_array = $this->getAllGames($games_array);
                break;
            case 1: // edit Product tpl
                $games_array = $this->getGamesByCPath($games_array);
                break;
            case 2: // edit Game tpl
                $games_array = $this->getTemplateCategoryList($games_array);
                break;
            
        }
        
        return $games_array;
    }
    
    public function getSelectedGameList($tpl_key = '', $games_array = array()) {
        $tpl_key = $tpl_key !== '' ? $tpl_key : $this->game_templates_key;
        
        switch ($tpl_key) {
            case 0: // edit Category tpl
                $games_array = $this->getSelectedGames($games_array);
                break;
            case 1: // edit Product tpl
                $games_array = $this->getSelectedGameBlog($games_array);
                break;
            case 2: // edit Game tpl
                $games_array = $this->getSelectedCategories($games_array);
                break;
            
        }
        
        return $games_array;
    }
    
    private function getSelectedGameBlog($return_array = array()) {
        $select_sql = "	SELECT game_blog_id
                        FROM products_to_game_blog
                        WHERE products_id = '" . $this->id . "'";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = $rows['game_blog_id'];
        }
        
        return $return_array;
    }

    private function getSelectedGames($return_array = array()) {
        $select_sql = "	SELECT game_blog_id
                        FROM game_blog_categories
                        WHERE categories_id = '" . $this->id . "'";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = $rows['game_blog_id'];
        }
        
        return $return_array;
    }

    private function getSelectedCategories($return_array = array()) {
        $select_sql = "	SELECT categories_id
                        FROM game_blog_categories
                        WHERE game_blog_id = '" . $this->id . "'";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = $rows['categories_id'];
        }
        
        return $return_array;
    }
    
    public function getSelectedCategoryTag($return_array = array()) {
        $select_sql = "	SELECT tag_id
                        FROM categories_tagmap
                        WHERE game_id = '" . $this->id . "'";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = $rows['tag_id'];
        }
        
        return $return_array;
    }
    
    private function getAllGames($return_array = array()) {
        $select_sql = "	SELECT gbd.* 
                        FROM game_blog gb
                        LEFT JOIN game_blog_description gbd
                            ON (gbd.game_blog_id = gb.game_blog_id AND gbd.language_id = 1)
                        ORDER BY gb.sort_order ";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = array(
                'id' => $rows['game_blog_id'],
                'text' => $rows['game_blog_description']
            );
        }
        
        return $return_array;
    }
    
    private function getGamesByCPath($return_array = array()) {
        $select_sql = "	SELECT gbd.* 
                        FROM game_blog gb
                        LEFT JOIN game_blog_description gbd
                            ON (gbd.game_blog_id = gb.game_blog_id AND gbd.language_id = 1)
                        WHERE gb.game_blog_id IN (select game_blog_id FROM game_blog_categories WHERE categories_id IN ('" . implode("', '", $this->cPath_array) . "'))
                        ORDER BY gb.sort_order ";
        $result_sql = tep_db_query($select_sql);
        while ($rows = tep_db_fetch_array($result_sql)) {
            $return_array[] = array(
                'id' => $rows['game_blog_id'],
                'text' => $rows['game_blog_description']
            );
        }
        
        return $return_array;
    }

    private function getGameDescription($id, $language_id = 1) {
        $return_str = '';
        
        $select_sql = "	SELECT game_blog_description
                        FROM game_blog_description
                        WHERE game_blog_id = '" . $id . "'
                            AND game_blog_description <> ''
                            AND (IF (language_id = '" . $language_id . "', 1, IF(( SELECT COUNT(game_blog_id) > 0
                                                                                    FROM game_blog_description
                                                                                    WHERE game_blog_id = '" . $id . "'
                                                                                        AND language_id = '" . $language_id . "'
                                                                                        AND game_blog_description <> ''), 0, language_id = '" . (int)$_SESSION['default_languages_id'] . "')))";
        $result_sql = tep_db_query($select_sql);
        if ($rows = tep_db_fetch_array($result_sql)) {
            $return_str = $rows['game_blog_description'];
        }
        
        return $return_str;
    }
    
    public function getGalleryImageInfo($idx, $key, $language_id = '') {
        $return_str = array();
        $language_id = $language_id !== '' ? $language_id : $this->language_id;
        
        if ($gallery_info = $this->getGameDetailValues('gallery_info', $language_id, '')) {
            $return_str = json_decode($gallery_info, true);
        }
        
        return isset($return_str[$idx][$key]) ? $return_str[$idx][$key] : '';
    }
    
    public function getRelatedLinkInfo($language_id = '') {
        $return_array = array();
        $language_id = $language_id !== '' ? $language_id : $this->language_id;
        
        if ($related_link_info = $this->getGameDetailValues('related_link_info', $language_id, '')) {
            $return_array = json_decode($related_link_info, true);
        }
        
        return $return_array;
    }

    public function getLanguageList($category_id) {
        if (!isset($this->language_list_array[$category_id])) {
            $this->language_list_array[$category_id] = array();
            
            $languages_select_query = "	SELECT languages_id, name 
                                        FROM " . TABLE_LANGUAGES . "
                                        WHERE languages_id IN (SELECT language_id FROM ".TABLE_CATEGORIES_DESCRIPTION." WHERE categories_id = ". $category_id .") 
                                        ORDER BY sort_order";
            $languages_select_result = tep_db_query($languages_select_query);
            while ($languages_select_row = tep_db_fetch_array($languages_select_result)) {
                $this->language_list_array[$category_id][] = array(
                    "id" => $languages_select_row["languages_id"],
                    "text" => $languages_select_row["name"]
                );
            }
        }
        
        return $this->language_list_array[$category_id];
    }
    
    public function getTemplateKey($id) {
        $return_int = false;
        
        foreach ($this->game_templates_array as $key => $tpl) {
            if ($tpl['id'] == $id) {
                $return_int = $key;
                break;
            }
        }
        
        return $return_int;
    }
    
    private function getTemplateID($id = '', $key = '') {
        $return_int = 0;
        $id = $id !== '' ? $id : $this->id;
        $key = $key !== '' ? $key : $this->game_templates_key;
        
        if (!isset($this->template_id[$id.$key])) {
            $select_sql = "	SELECT tpl_id 
                            FROM frontend_template 
                            WHERE id = '" . $id . "'
                                AND id_type = '" . $key . "'";
            $result_sql = tep_db_query($select_sql);
            if ($rows = tep_db_fetch_array($result_sql)) {
                $this->template_id[$id.$key] = $rows['tpl_id'];
            } else if ($id) {
                $insert_array = array(
                    'id' => $id, 
                    'id_type' => $key, 
                );
                tep_db_perform('frontend_template', $insert_array);
                $this->template_id[$id.$key] = tep_db_insert_id();
            }
        }
        
        return $this->template_id[$id.$key];
    }
    
    public function getTemplateLabelByKey($key) {
        return isset($this->game_templates_array[$key]) ? $this->game_templates_array[$key]['text'] : $this->game_templates_array[0]['text'];
    }
    
    public function getTemplateList($return_array = array()) {
        // Product template land from catalog page
        unset($this->game_templates_array[1]);
        
        foreach ($this->game_templates_array as $idx => $info_array) {
            $return_array[] = array(
                'id' => $idx,
                'text' => $info_array['text']
            );
        }
        
        return $return_array;
    }
    
    public function getTitle($id, $language_id = 1, $tpl_key = '') {
        $return_str = '';
        $tpl_key = $tpl_key !== '' ? $tpl_key : $this->game_templates_key;
        
        switch ($tpl_key) {
            case 0: // edit Category tpl
                $return_str = $this->getCategoryDescription($id, $language_id);
                break;
            case 2: // edit Game tpl
                $return_str = $this->getGameDescription($id, $language_id);
                break;
            
        }
        
        return $return_str;
    }
    
    public function keyword_generator($sentence) {
        $keyword = '';
        $words = explode(' ', $sentence);
        for ($word_counter = 0, $max_word = count($words)-1; $word_counter < $max_word; $word_counter++) {
            $keyword .= substr($words[$word_counter], 0, 1); 
        }
        $keyword .= ' ' . trim($words[count($words) - 1], '.?![](){}*');

        return trim($keyword);
    }
    
    public function saveTemplates($data) {
        if ($data) {
            if (isset($this->game_templates_array[$this->game_templates_key])) {
                if (isset($data["game"]["blog"]["custom_url"])) {
                    $selectCustomUrlSql = "SELECT custom_url FROM game_blog WHERE custom_url = '" . $this->format_url($data["game"]["blog"]["custom_url"]) . "' AND game_blog_id != " . $this->id;
                    $customUrlSql = tep_db_query($selectCustomUrlSql);
                    $customUrl = tep_db_fetch_array($customUrlSql);

                    if($customUrl['custom_url'] === $this->format_url($data["game"]["blog"]["custom_url"])){
                        return 'duplicated_custom_url';
                    } else {
                        $update_sql = "UPDATE game_blog SET custom_url = '" . $this->format_url($data["game"]["blog"]["custom_url"]) . "' WHERE game_blog_id = " . $this->id;
                        tep_db_query($update_sql);
                    }
                }

                $game_array = tep_db_prepare_input($data["game"]);
                $game_details_array = tep_db_prepare_input($data["game_details"]);

                $this->data['tpl_id'] = $this->getTemplateID();
                
//                if ($this->game_templates_key == 1) {
//                    if (isset($game_array['tpl_status'])) {
//                        $this->data['frontend_template']['tpl_status'] = 1;
//                    } else {
//                        $this->data['frontend_template']['tpl_status'] = 0;
//                    }
//                } else {
//                    $this->data['frontend_template']['tpl_status'] = 1;
//                }
                
                if (isset($game_array['extra_setting']['af_notice_option'])) {
                    $this->data['frontend_template']['af_notice_enable_status'] = $game_array['extra_setting']['af_notice_option'];
                }
                
                if (isset($game_array['background_setting']['url'])) {
                    $this->data['frontend_template']['background_source'] = $game_array['background_setting']['url'];
                }

                if (isset($game_array['background_setting']['color'])) {
                    $this->data['frontend_template']['background_color'] = $game_array['background_setting']['color'];
                }

                if (isset($game_array['logo'])) {
                    $this->data['frontend_template']['logo_source'] = $game_array['logo'];
                }
                
                if (isset($game_array['game_region'])) {
                    foreach ($game_array['game_region'] as $platform) {
                        $this->data['frontend_template_to_game_info']['game_region'][] = $platform;
                    }
                } else {
                    $this->data['frontend_template_to_game_info']['game_region'] = '';
                }
                
                if (isset($game_array['game_language'])) {
                    foreach ($game_array['game_language'] as $platform) {
                        $this->data['frontend_template_to_game_info']['game_language'][] = $platform;
                    }
                } else {
                    $this->data['frontend_template_to_game_info']['game_language'] = '';
                }
                
                if (isset($game_array['game_platform'])) {
                    foreach ($game_array['game_platform'] as $platform) {
                        $this->data['frontend_template_to_game_info']['game_platform'][] = $platform;
                    }
                } else {
                    $this->data['frontend_template_to_game_info']['game_platform'] = '';
                }

                if (isset($game_array['game_genre'])) {
                    foreach ($game_array['game_genre'] as $genre) {
                        $this->data['frontend_template_to_game_info']['game_genre'][] = $genre;
                    }
                } else {
                    $this->data['frontend_template_to_game_info']['game_genre'] = '';
                }

                if (!empty($game_details_array) && is_array($game_details_array)) {
                    foreach ($game_details_array as $lang_id => $game_detail) {
                        if (isset($game_detail['description'])) {
                            $this->data['frontend_template_lang'][$lang_id]['description'] = $game_detail['description'];
                        }

                        if (isset($game_detail['notice'])) {
                            $this->data['frontend_template_lang'][$lang_id]['notice'] = $game_detail['notice'];
                        }

                        if (isset($game_detail['system_requirements'])) {
                            $this->data['frontend_template_lang'][$lang_id]['system_requirements'] = $game_detail['system_requirements'];
                        }

                        if (isset($game_detail['remark'])) {
                            $this->data['frontend_template_lang'][$lang_id]['remark'] = $game_detail['remark'];
                        }

                        if (isset($game_detail['gallery']['image_source'])) {
                            $gallery_array = array();

                            for ($i = 0; $i < count($game_detail['gallery']['image_source']); $i++) {
                                if ($game_detail['gallery']['image_source'][$i]) {
                                    $gallery_array[] = array(
                                        'source' => $game_detail['gallery']['image_source'][$i],
                                        'link' => $game_detail['gallery']['link_url'][$i]
                                    );
                                }
                            }
                            
                            $this->data['frontend_template_lang'][$lang_id]['gallery_info'] = json_encode($gallery_array);
                        }

                        if (isset($game_detail['game_title'])) {
                            $this->data['frontend_template_lang'][$lang_id]['game_title'] = $game_detail['game_title'];
                        }

                        if (isset($game_detail['game_publisher'])) {
                            $this->data['frontend_template_lang'][$lang_id]['game_publisher'] = $game_detail['game_publisher'];
                        }

                        if (isset($game_detail['game_developer'])) {
                            $this->data['frontend_template_lang'][$lang_id]['game_developer'] = $game_detail['game_developer'];
                        }

                        if (isset($game_detail['game_release_date'])) {
                            $this->data['frontend_template_lang'][$lang_id]['game_release_date'] = $game_detail['game_release_date'];
                        }
                        
                        if (isset($game_detail['keyword'])) {
                            $this->data['categories_game_details'][$lang_id]['game_keyword'] = $game_detail['keyword'];
                            $this->data['frontend_template_lang'][$lang_id]['game_keyword'] = $game_detail['keyword'];
                        }

                        if (isset($game_detail['related_link']['label'])) {
                            $related_link_array = array();
    
                            for ($i = 0; $i < count($game_detail['related_link']['label']); $i++) {
                                if ($game_detail['related_link']['label'][$i]) {
                                    $related_link_array[] = array(
                                        'label' => $game_detail['related_link']['label'][$i],
                                        'link' => $game_detail['related_link']['link'][$i]
                                    );
                                }
                            }
                            
                            $this->data['frontend_template_lang'][$lang_id]['related_link_info'] = json_encode($related_link_array);
                        }
                    }
                }
                
                if ($tpl_id = $this->data['tpl_id']) {
                    if ($this->data['frontend_template']) {
                        tep_db_perform('frontend_template', $this->data['frontend_template'], 'update', "tpl_id = " . $tpl_id);
                    }
                    
                    if ($this->data['frontend_template_lang']) {
                        foreach ($this->data['frontend_template_lang'] as $lang_id => $lang_array) {
                             $select_sql = " SELECT tpl_id 
                                             FROM frontend_template_lang 
                                             WHERE tpl_id = " . $tpl_id . " 
                                                AND language_id = " . $lang_id;
                            $result_sql = tep_db_query($select_sql);
                            if (!tep_db_num_rows($result_sql)) {
                                $lang_array['tpl_id'] = $tpl_id;
                                $lang_array['language_id'] = $lang_id;
                                
                                tep_db_perform('frontend_template_lang', $lang_array);
                            } else {
                                tep_db_perform('frontend_template_lang', $lang_array, 'update', "tpl_id = " . $tpl_id . " and language_id = " . $lang_id);
                            }
                        }
                    }
                    
                    // search keywords
                    if ($this->data['categories_game_details']) {
                        if ($this->game_templates_key === 0) {
                            foreach ($this->data['categories_game_details'] as $lang_id => $lang_array) {
                                $select_game_details_sql = "SELECT categories_id 
                                                            FROM " . TABLE_CATEGORIES_GAME_DETAILS . " 
                                                            WHERE categories_id > '0' 
                                                                AND categories_id = '" . $this->id . "' 
                                                                AND language_id = " . $lang_id;
                                $sql_select_res = tep_db_query($select_game_details_sql);
                                if (!tep_db_num_rows($sql_select_res)) {
                                    $lang_array['categories_id'] = $this->id;
                                    $lang_array['language_id'] = $lang_id;
                                    tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $lang_array);
                                } else {
                                    tep_db_perform(TABLE_CATEGORIES_GAME_DETAILS, $lang_array, 'update', "categories_id = '" . $this->id . "' AND language_id = " . $lang_id);
                                }
                            }

                            tep_update_categories_keywords($this->id);
                        }
                        
                        $this->update_search_keywords($tpl_id, $this->id, $this->game_templates_key);
                    }

                    if ($this->data['frontend_template_to_game_info']) {
                        foreach ($this->data['frontend_template_to_game_info'] as $game_info_type => $game_info_array) {
                            $delete_sql = "DELETE FROM frontend_template_to_game_info WHERE tpl_id = " . $tpl_id . " AND game_info_type = '" . $game_info_type . "'";
                            tep_db_query($delete_sql);
                            
                            if ($game_info_array) {
                                $insert_sql = "INSERT INTO frontend_template_to_game_info (tpl_id, game_info_type, game_info_id) VALUES ('" . $tpl_id . "','" . $game_info_type . "','" . implode("'),('" . $tpl_id . "','" . $game_info_type . "','", $game_info_array) . "')";
                                tep_db_query($insert_sql);
                            }
                        }
                    }

                    if ($this->game_templates_key === 0) {
                        $delete_sql = "DELETE FROM game_blog_categories WHERE categories_id = '" . $this->id . "'";
                        tep_db_query($delete_sql);
                        
                        if (isset($game_array['supported_category_id'])) {
                            $insert_sql = "INSERT INTO game_blog_categories (categories_id, game_blog_id) VALUES ('" . $this->id . "','" . implode("'),('" . $this->id . "','", $game_array['supported_category_id']) . "')";
                            tep_db_query($insert_sql);
                        }
                        
                        $delete_sql = "DELETE FROM categories_tagmap WHERE game_id = '" . $this->id . "'";
                        tep_db_query($delete_sql);
                        
                        if (isset($game_array['category_tag_id'])) {
                            $insert_sql = "INSERT INTO categories_tagmap (game_id, tag_id) VALUES ('" . $this->id . "','" . implode("'),('" . $this->id . "','", $game_array['category_tag_id']) . "')";
                            tep_db_query($insert_sql);
                        }
                    } else if ($this->game_templates_key === 1) {
                        $delete_sql = "DELETE FROM products_to_game_blog WHERE products_id = '" . $this->id . "'";
                        tep_db_query($delete_sql);

                        if (isset($game_array['supported_product_id'])) {
                            $insert_sql = "INSERT INTO products_to_game_blog (products_id, game_blog_id) VALUES ('" . $this->id . "','" . implode("'),('" . $this->id . "','", $game_array['supported_product_id']) . "')";
                            tep_db_query($insert_sql);
                        }
                    } else if ($this->game_templates_key === 2) {
                        $delete_sql = "DELETE FROM game_blog_categories WHERE game_blog_id = '" . $this->id . "'";
                        tep_db_query($delete_sql);
                        
                        if (isset($game_array['supported_game_id'])) {
                            $insert_sql = "INSERT INTO game_blog_categories (game_blog_id, categories_id) VALUES ('" . $this->id . "','" . implode("'),('" . $this->id . "','", $game_array['supported_game_id']) . "')";
                            tep_db_query($insert_sql);
                        }
                    }
                }
            }
        }
        
        return true;
    }
    
    public function format_url($str){
        $RegExp1 = "/[\x{4e00}-\x{9fff}]+/u";
        $RegExp2 = "/[^\a-z]/i";
        $RegExp3 = "@%[\dA-F]{2}@";
        $RegExp4 = "/\$|,|@|#|~|`|\%|\*|\^|\&|\(|\)|\+|\=|\[|\]|\[|\}|\{|\;|\:|\"|\<|\>|\?|\||\|\!|\$|\./";
        $RegExp5 = "/'|\_|-{1,}| {1,}|\/{1,}/";
        $RegExp6 = "/\ {1,}/";

        //find all chinese characters
        preg_match_all($RegExp1, $str, $content);
        $contentArr = $content[0];
        $i = 0;

        if(isset($content[0]) && $content[0] !== false){
            foreach($contentArr as $contentObj){
                $pinyin = new pinyin();
                //chinese characters
                if($i > 0){
                    $pinyin = '-' . $pinyin->getPinyin($contentObj);
                } else {
                    $pinyin = $pinyin->getPinyin($contentObj);
                }
                $pinyin = str_replace(' ', '-', $pinyin);
                $str = preg_replace($RegExp1, $pinyin, $str,1);
                $i ++;
            }
        }
        // $str = preg_replace($testRegex, ' ', $str);
        $str = preg_replace($RegExp2, ' ', $str);
        $str = preg_replace($RegExp3, ' ', $str);
        $str = preg_replace($RegExp4, ' ', $str);
        $str = preg_replace($RegExp5, ' ', $str);
        $str = trim($str);
        $str = preg_replace($RegExp6, '-', $str);
        // var_dump($str);exit; 
        
        return strtolower($str);
    }

    private function update_search_keywords($tpl_id, $id, $game_templates_key) {
        if (in_array($game_templates_key, array(0, 2))) {
            $valid_id_array = array();
            
            if ($game_templates_key == 0) {
                $category_id_array_select_sql = "	SELECT categories_structures_value
                                                    FROM " . TABLE_CATEGORIES_STRUCTURES . "
                                                    WHERE categories_structures_key = 'games'";
                $category_id_array_result_sql = tep_db_query($category_id_array_select_sql);
                if ($category_id_array_row = tep_db_fetch_array($category_id_array_result_sql)) {
                    $valid_id_array = explode(',', $category_id_array_row['categories_structures_value']);
                }
            } else if ($game_templates_key == 2) {
                $category_id_array_select_sql = "	SELECT game_blog_id
                                                    FROM game_blog";
                $category_id_array_result_sql = tep_db_query($category_id_array_select_sql);
                while ($category_id_array_row = tep_db_fetch_array($category_id_array_result_sql)) {
                    $valid_id_array[] = $category_id_array_row['game_blog_id'];
                }
            }
            
            // Remove all search value of this Categories (Regardless of Game or not)
            $categories_search_delete_sql = "	DELETE FROM search_keywords
                                                WHERE id = '" . $id . "'
                                                    AND id_type = " . $game_templates_key;
            tep_db_query($categories_search_delete_sql);

            if (in_array($id, $valid_id_array)) { // If is GAME type
                $all_languages = tep_get_languages();
                
                foreach ($all_languages as $languages) {
                    $name = $this->getTitle($id, $languages['id']);
                    
                    $game_keyword_select_sql = "SELECT game_keyword
                                                FROM frontend_template_lang
                                                WHERE tpl_id = '" . $tpl_id . "'
                                                    AND language_id = " . (int)$languages['id'];
                    $game_keyword_result_sql = tep_db_query($game_keyword_select_sql);
                    $game_keyword_row = tep_db_fetch_array($game_keyword_result_sql);

                    $keywords_data_array = array(
                        'search_value' => tep_db_prepare_input($name . (tep_not_empty($game_keyword_row['game_keyword']) ? ', ' . $game_keyword_row['game_keyword'] : '')),
                        'tpl_id' => $tpl_id,
                        'id' => $id,
                        'id_type' => $game_templates_key,
                        'language_id' => (int)$languages['id']
                    );
                    
                    tep_db_perform('search_keywords', $keywords_data_array);
                }
            }
        }
    }
    
    private function toHierarchy($collection) {
        $l = 0;
        $trees = array();

        if (count($collection) > 0) {
            // Node Stack. Used to help building the hierarchy
            $stack = array();

            foreach ($collection as $node) {
                $item = $node;
                $item['children'] = array();

                // Number of stack items
                $l = count($stack);

                // Check if we're dealing with different levels
                while($l > 0 && $stack[$l - 1]['depth'] >= $item['depth']) {
                    array_pop($stack);
                    $l--;
                }

                // Stack is empty (we are inspecting the root)
                if ($l == 0) {
                    // Assigning the root node
                    $i = count($trees);
                    $trees[$i] = $item;
                    $stack[] = & $trees[$i];
                } else {
                    // Add node to parent
                    $i = count($stack[$l - 1]['children']);
                    $stack[$l - 1]['children'][$i] = $item;
                    $stack[] = & $stack[$l - 1]['children'][$i];
                }
            }
        }

        return $trees;
    }
}
?>