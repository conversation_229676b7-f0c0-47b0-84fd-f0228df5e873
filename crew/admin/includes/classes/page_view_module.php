<?
class page_view_module {
	var $domxml_object, $files_name_array;
	var $tags_names, $tags_configurations;
	
	function page_view_module() {
		require_once('ogm_xml_to_ary.php');
		$this->xml_array_obj = new ogm_xml_to_ary(realpath(DIR_FS_CATALOG . "cache/page_view_module.xml"));
		$this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();
		$this->set_tags();
	}
	
    function set_tags(){
    	$this->tags_names = array();
    	$this->tags_configurations = array();
    	
    	foreach ($this->xml_array['page_view_module']['_c']['tags']['_c'] as $tags => $tag_datas) {
    		$this->files_name_array[$tags] = array();
    		$this->tags_names[$tags] = $tag_datas['_c']['display']['_v'];
    		foreach ($tag_datas['_c']['configurations']['_c'] as $tag_configurations_key => $tag_configurations_data) {
    			$this->tags_configurations[$tags][$tag_configurations_key] = $tag_configurations_data['_v'];
    		}
    	}
    	return $this->tags_names;
    }
    
    function get_tags_names() {
    	return $this->tags_names;
    }
}
?>