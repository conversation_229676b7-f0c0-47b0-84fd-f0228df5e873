<?php

if (!defined('SYSTEM_DEFAULT_LANGUAGE_ID')) {
    define('SYSTEM_DEFAULT_LANGUAGE_ID', 1);
}

class payment_methods {

    var $payment_methods_id;
    var $payment_methods_parent_id;
    var $payment_methods_filename;
    var $payment_methods_class_name;
    var $payment_methods_title;
    var $payment_methods_send_mode_name;
    var $payment_methods_sort_order;
    var $payment_methods_legend_color;
    var $payment_methods_receive_status;
    var $payment_methods_receive_status_mode;
    var $payment_methods_code;
    var $payment_methods_description_title;
    var $payment_methods_instance_key_info;
    var $payment_methods_types_id;
    var $payment_methods_send_mass_payment;
    var $payment_method_array;  // for selected payment method
    var $payment_methods_array;  // for all payment methods
    var $payment_gateways_array; // for all payment gateways
    var $payment_methods_size;
    // SEND PAYMENT
    var $payment_methods_send_status;
    var $payment_methods_send_status_mode;
    var $payment_methods_send_status_message;
    // SEND CODE
    var $payment_methods_send_code;

    // class constructor
    function payment_methods($pmID = '') {
        if (is_numeric($pmID) && (int) $pmID > 0) {
            $payment_methods_select_sql = "	SELECT pm.payment_methods_code, pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id,
												pm.payment_methods_logo, pm.payment_methods_types_id, pm.payment_methods_receive_status,
												ppm.payment_methods_filename as payment_methods_parent_filename, pm.payment_methods_send_status,
												pm.payment_methods_send_status_mode, pm.payment_methods_send_mode_name, pm.payment_methods_title,
												pm.payment_methods_legend_color, pm.payment_methods_sort_order, pm.payment_methods_send_currency,
												pm.payment_methods_receive_status_mode, pm.payment_methods_send_mass_payment, pm.payment_methods_send_available_sites,
                                                                                                pm.payment_methods_receive_featured_status
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											LEFT JOIN " . TABLE_PAYMENT_METHODS . " as ppm
												ON pm.payment_methods_parent_id = ppm.payment_methods_id
											LEFT JOIN " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " as pmsd
												ON pm.payment_methods_id = pmsd.payment_methods_id
													AND pmsd.payment_methods_mode = 'RECEIVE'
													AND pmsd.payment_methods_status = '-1'
													AND pmsd.languages_id = '1'
													AND pm.payment_methods_id = pmsd.payment_methods_id
											WHERE pm.payment_methods_id = '" . (int) $pmID . "'";
            // TODO : don't hardcode languages_id = '1' in future.
            // TODO : don't hardcode payment_methods_status = '-1' in future.

            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {

                $pm_desc_title_select_sql = "	SELECT payment_methods_description_title
												FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
												WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
													AND languages_id = '1'";
                $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
                $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

                $payment_methods_status_description_select_sql = "	SELECT pmsd.payment_methods_mode, pmsd.payment_methods_status_message
																	FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " as pmsd
																	WHERE pmsd.payment_methods_id = '" . (int) $pmID . "'
																		AND pmsd.languages_id = '" . SYSTEM_DEFAULT_LANGUAGE_ID . "'";
                $payment_methods_status_description_result_sql = tep_db_query($payment_methods_status_description_select_sql);
                while ($payment_methods_status_description_row = tep_db_fetch_array($payment_methods_status_description_result_sql)) {
                    switch ($payment_methods_status_description_row['payment_methods_mode']) {
                        case 'RECEIVE':
                            $payment_methods_row['payment_methods_status_message'] = $payment_methods_status_description_row['payment_methods_status_message'];
                            break;
                        case 'SEND':
                            $payment_methods_row['payment_methods_send_status_message'] = $payment_methods_status_description_row['payment_methods_status_message'];
                            break;
                        case 'SEND_CODE':
                            $payment_methods_row['payment_methods_send_code'] = $payment_methods_status_description_row['payment_methods_status_message'];
                            break;
                    }
                }
                $this->payment_methods_id = $payment_methods_row['payment_methods_id'];

                if ($payment_methods_row['payment_methods_parent_id'] > 0) {
                    $filename = $payment_methods_row['payment_methods_parent_filename'];
                } else {
                    $filename = $payment_methods_row['payment_methods_filename'];
                }

                $module_class = substr($filename, 0, strrpos($filename, '.'));

                if (!tep_class_exists($module_class)) {
                    if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php')) {
                        include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php');
                    }
                    if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php')) {
                        include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php');
                    }
                }

                $this->payment_method_array = array();

                if (tep_class_exists($module_class)) {
                    eval('$pm_module = new ' . $module_class . '(' . $payment_methods_row['payment_methods_id'] . ');');
                    $this->payment_method_array = $pm_module;
                    $this->payment_methods_class_name = $module_class;
                }
                $this->payment_methods_receive_status_mode = $payment_methods_row['payment_methods_receive_status_mode'];
                $this->payment_methods_description_title = $pm_desc_title_row['payment_methods_description_title'];

                $this->payment_methods_code = $payment_methods_row['payment_methods_code'];
                $this->payment_methods_parent_id = $payment_methods_row['payment_methods_parent_id'];
                $this->payment_methods_title = $payment_methods_row['payment_methods_title'];
                $this->payment_methods_send_mode_name = $payment_methods_row['payment_methods_send_mode_name'];
                $this->payment_methods_sort_order = $payment_methods_row['payment_methods_sort_order'];
                $this->payment_methods_legend_color = $payment_methods_row['payment_methods_legend_color'];
                $this->payment_methods_receive_status = $payment_methods_row['payment_methods_receive_status'];

                $this->payment_methods_filename = $filename;
                $this->payment_methods_status_message = $payment_methods_row['payment_methods_status_message'];  // for now support -1 only
                $this->payment_methods_logo = $payment_methods_row['payment_methods_logo'];
                $this->payment_methods_types_id = (int) $payment_methods_row['payment_methods_types_id'];

                // SEND PAYMENT
                $this->payment_methods_send_status = $payment_methods_row['payment_methods_send_status'];
                $this->payment_methods_send_status_mode = $payment_methods_row['payment_methods_send_status_mode'];
                $this->payment_methods_send_status_message = isset($payment_methods_row['payment_methods_send_status_message']) ? $payment_methods_row['payment_methods_send_status_message'] : '';
                $this->payment_methods_send_code = isset($payment_methods_row['payment_methods_send_code']) ? $payment_methods_row['payment_methods_send_code'] : '';
                $this->payment_methods_send_currency = $payment_methods_row['payment_methods_send_currency'];
                $this->payment_methods_send_mass_payment = $payment_methods_row['payment_methods_send_mass_payment'];
                $this->payment_methods_send_available_sites = $payment_methods_row['payment_methods_send_available_sites'];
                $this->payment_methods_receive_featured_status = $payment_methods_row['payment_methods_receive_featured_status'];

                $pm_types_select_sql = "SELECT pmt.payment_methods_types_name, pmt.payment_methods_types_id
										FROM " . TABLE_PAYMENT_METHODS_TYPES . " as pmt
										WHERE pmt.payment_methods_types_id = '" . (int) $payment_methods_row['payment_methods_types_id'] . "'";
                $pm_types_result_sql = tep_db_query($pm_types_select_sql);
                $pm_types_row = tep_db_fetch_array($pm_types_result_sql);

                $this->payment_methods_types_name = $pm_types_row['payment_methods_types_name'];
            }
        } else {
            switch ($pmID) {
                case 'count_payment_methods':
                    $payment_methods_select_sql = "	SELECT count(pm.payment_methods_id) as total
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";
                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                    $payment_methods_row = tep_db_fetch_array($payment_methods_result_sql);
                    $this->payment_methods_size = (int) $payment_methods_row['total'];
                    break;

                case 'payment_gateways':
                    $payment_gateway_file_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_title
														FROM " . TABLE_PAYMENT_METHODS . " as pm
														WHERE pm.payment_methods_parent_id = '0'
															AND pm.payment_methods_receive_status = '1'
															AND pm.payment_methods_filename IS NOT NULL";

                    $payment_gateway_file_result_sql = tep_db_query($payment_gateway_file_select_sql);

                    $this->payment_gateways_array = array();
                    while ($payment_gateway_file_row = tep_db_fetch_array($payment_gateway_file_result_sql)) {

                        $module_class = substr($payment_gateway_file_row['payment_methods_filename'], 0, strrpos($payment_gateway_file_row['payment_methods_filename'], '.'));

                        if (!tep_class_exists($module_class)) {
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php');
                            }
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php');
                            }
                        }

                        eval('$pm_module = new ' . $module_class . '(' . $payment_gateway_file_row['payment_methods_id'] . ');');

                        $this->payment_gateways_array[$payment_gateway_file_row['payment_methods_id']] = $pm_module;
                    }
                    break;

                case 'payment_methods':

                    $filename_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_parent_id = '0'
												AND pm.payment_methods_filename IS NOT NULL";

                    $filename_result_sql = tep_db_query($filename_select_sql);

                    $filename_result_array = array();
                    while ($filename_row = tep_db_fetch_array($filename_result_sql)) {
                        $filename_result_array[$filename_row['payment_methods_id']] = $filename_row['payment_methods_filename'];
                    }

                    $payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";

                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

                    $this->payment_methods_array = array();

                    while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {

                        $filename = $filename_result_array[$payment_methods_row['payment_methods_parent_id']];
                        $module_class = substr($filename, 0, strrpos($filename, '.'));

                        $this->payment_methods_array[$payment_methods_row['payment_methods_id']] = array();

                        if (!tep_class_exists($module_class)) {
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php');
                            }
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php');
                            }
                        }

                        eval('$pm_module = new ' . $module_class . '(' . $payment_methods_row['payment_methods_id'] . ');');

                        $this->payment_methods_array[$payment_methods_row['payment_methods_id']] = $pm_module;
                    }

                    $this->payment_methods_size = count($this->payment_methods_array);
                    break;

                case 'all':

                    $this->payment_methods_array = array();

                    $payment_gateway_file_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_title
														FROM " . TABLE_PAYMENT_METHODS . " as pm
														WHERE pm.payment_methods_parent_id = '0'
															AND pm.payment_methods_receive_status = '1'
															AND pm.payment_methods_filename IS NOT NULL";
                    $payment_gateway_file_result_sql = tep_db_query($payment_gateway_file_select_sql);

                    $filename_result_array = array();
                    while ($payment_gateway_file_row = tep_db_fetch_array($payment_gateway_file_result_sql)) {

                        $module_class = substr($payment_gateway_file_row['payment_methods_filename'], 0, strrpos($payment_gateway_file_row['payment_methods_filename'], '.'));

                        if (!tep_class_exists($module_class)) {
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php');
                            }
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php');
                            }
                        }

                        eval('$pm_module = new ' . $module_class . '(' . $payment_gateway_file_row['payment_methods_id'] . ');');

                        $this->payment_methods_array[$payment_gateway_file_row['payment_methods_id']] = $pm_module;
                        $filename_result_array[$payment_gateway_file_row['payment_methods_id']] = $payment_gateway_file_row['payment_methods_filename'];
                    }

                    $payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";
                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

                    while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                        $filename = isset($filename_result_array[$payment_methods_row['payment_methods_parent_id']]) ? $filename_result_array[$payment_methods_row['payment_methods_parent_id']] : '';
                        $module_class = !empty($filename) ? substr($filename, 0, strrpos($filename, '.')) : '';
                        $this->payment_methods_array[$payment_methods_row['payment_methods_id']] = array();
                        if (!tep_class_exists($module_class)) {
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '.php');
                            }
                            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php')) {
                                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_class . '/admin/languages/english/' . $module_class . '.lng.php');
                            }
                        }
                        
                        if ($module_class) {
                            eval('$pm_module = new ' . $module_class . '(' . $payment_methods_row['payment_methods_id'] . ');');
                            $this->payment_methods_array[$payment_methods_row['payment_methods_id']] = $pm_module;
                        }
                    }
                    break;
            }
        }
    }

    function get_pm_display_title($pm_id = '', $pg_id = '') {
        if ($pm_id == '') {
            if ((int) $this->payment_methods_parent_id == 0)
                return $this->payment_methods_title;

            $parent_select_sql = "	SELECT payment_methods_title, payment_methods_send_mode_name
									FROM " . TABLE_PAYMENT_METHODS . "
									WHERE payment_methods_id = '" . $this->payment_methods_parent_id . "'";
            $parent_result_sql = tep_db_query($parent_select_sql);
            if ($parent_row = tep_db_fetch_array($parent_result_sql)) {
                $title = array();
                if (tep_not_null($this->payment_methods_title))
                    $title[] = $this->payment_methods_title;
                if (tep_not_null($this->payment_methods_send_mode_name))
                    $title[] = $this->payment_methods_send_mode_name;
                return $parent_row['payment_methods_title'] . " > " . implode(" / ", $title);
            }
        } else {
            $display_pm_title = '';
            $pm_title_select_sql = "SELECT payment_methods_title, payment_methods_send_mode_name
									FROM " . TABLE_PAYMENT_METHODS . " as pm
									WHERE pm.payment_methods_id IN ('" . $pm_id . "','" . $pg_id . "')
									ORDER BY pm.payment_methods_id";
            $pm_title_result_sql = tep_db_query($pm_title_select_sql);
            while ($pm_title_row = tep_db_fetch_array($pm_title_result_sql)) {
                if ($display_pm_title != '') {
                    $display_pm_title .= ' - ';
                }
                $display_pm_title .= $pm_title_row['payment_methods_title'];
            }
            return $display_pm_title;
        }
    }

    function get_all_pm_display_title() {
        $pm_title_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_title, pm.payment_methods_parent_id
								FROM " . TABLE_PAYMENT_METHODS . " as pm
								ORDER BY pm.payment_methods_id";
//								WHERE pm.payment_methods_receive_status = '1'
        $pm_title_result_sql = tep_db_query($pm_title_select_sql);
        $pm_title_array = array();
        $pg_title_array = array();
        while ($pm_title_row = tep_db_fetch_array($pm_title_result_sql)) {
            if ($pm_title_row['payment_methods_parent_id'] > 0) {
                $pm_title_array[$pm_title_row['payment_methods_parent_id']][$pm_title_row['payment_methods_id']] = $pm_title_row['payment_methods_title'];
            } else {
                $pg_title_array[$pm_title_row['payment_methods_id']] = $pm_title_row['payment_methods_title'];
            }
        }
        $all_pg_title_array = array();
        foreach ($pg_title_array as $pg_id_loop => $pg_value_loop) {
            $all_pg_title_array[$pg_id_loop] = $pg_value_loop;
            if (isset($pm_title_array[$pg_id_loop]) && count($pm_title_array[$pg_id_loop])) {
                foreach ($pm_title_array[$pg_id_loop] as $pm_id_loop => $pm_value_loop) {
                    $all_pg_title_array[$pm_id_loop] = $pg_value_loop . ' - ' . $pm_value_loop;
                }
            }
        }
        return $all_pg_title_array;
    }

    function get_payment_method_configuration_key($language_id = 1) {
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_title, pci.payment_configuration_info_key, pcid.payment_configuration_info_value, pci.payment_configuration_info_description, pci.use_function, pci.set_function
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
													WHERE pci.payment_methods_id = '" . (int) ((int) $this->payment_methods_parent_id > 0 ? $this->payment_methods_parent_id : $this->payment_methods_id) . "'
														AND pcid.languages_id = '" . (int) $language_id . "'
													ORDER BY pci.payment_configuration_info_sort_order ";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        $payment_configuration_info_array = array();
        while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
            $payment_configuration_info_array[$payment_configuration_info_row['payment_configuration_info_key']] = array('payment_configuration_info_title' => $payment_configuration_info_row['payment_configuration_info_title'],
                'payment_configuration_info_value' => $payment_configuration_info_row['payment_configuration_info_value'],
                'payment_configuration_info_description' => $payment_configuration_info_row['payment_configuration_info_description'],
                'use_function' => $payment_configuration_info_row['use_function'],
                'set_function' => $payment_configuration_info_row['set_function']);
        }
        return $payment_configuration_info_array;
    }

    function set_instance_key_info() {
        if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $this->payment_methods_filename)) {
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $this->payment_methods_filename);
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $this->payment_methods_class_name . '/admin/languages/english/' . $this->payment_methods_class_name . '.lng.php');

            if (tep_class_exists($this->payment_methods_class_name)) {
                $module = new $this->payment_methods_class_name();
                $this->payment_methods_instance_key_info = $module->merchant_information_keys();
            }
        }
    }

    function get_payment_fees($pmID) {
        if (!tep_not_null($pmID))
            return array();

        $payment_fees_array = array();
        $payment_fees_currency_array = array();

        // get customer groups name
        $customer_groups = array();
        $cust_grp_select_sql = "SELECT customers_groups_id, customers_groups_name
								FROM " . TABLE_CUSTOMERS_GROUPS . "
								ORDER BY sort_order";
        $cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
        while ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
            $customer_groups[$cust_grp_row['customers_groups_id']] = $cust_grp_row['customers_groups_name'];
        }

        foreach ($customer_groups as $cust_grp_id => $cust_grp_name) {
            $payment_fees_array[$pmID][$cust_grp_id] = array();

            // first find all existing payment methods currency code from payment_fees table
            $payment_fees_currency_select_sql = "SELECT DISTINCT payment_methods_currency_code
												FROM " . TABLE_PAYMENT_FEES . "
												WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
												AND payment_methods_mode = 'RECEIVE'";
            $payment_fees_currency_result_sql = tep_db_query($payment_fees_currency_select_sql);
            while ($payment_fees_currency_row = tep_db_fetch_array($payment_fees_currency_result_sql)) {
                $payment_fees_currency_array[] = $payment_fees_currency_row['payment_methods_currency_code'];
                $payment_fees_array[$pmID][$cust_grp_id][$payment_fees_currency_row['payment_methods_currency_code']] = array();
            }

            // second find the missing currencies from payment_methods_instance table
            $currency_list = implode("','", $payment_fees_currency_array);
            $payment_methods_instance_select_sql = "SELECT DISTINCT currency_code
													FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
													WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
													AND currency_code NOT IN ('" . $currency_list . "')";
            $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
            if (tep_db_num_rows($payment_methods_instance_result_sql) > 0) {
                while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                    $payment_fees_array[$pmID][$cust_grp_id][$payment_methods_instance_row['currency_code']] = array();
                }
            }
        }

        // now payment_fees_array should have the complete currency list, next to populate the fees setting data
        $payment_fees_select_sql = "	SELECT payment_fees_id, payment_methods_id, payment_methods_currency_code, payment_methods_mode, payment_fees_operator, payment_fees_min, payment_fees_cost_value,
												payment_fees_cost_percent, payment_fees_cost_percent_min, payment_fees_cost_percent_max, currency_code,
												payment_fees_customers_groups_id, payment_fees_follow_group
										FROM " . TABLE_PAYMENT_FEES . "
										WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
										AND payment_methods_mode = 'RECEIVE'
										ORDER BY payment_fees_customers_groups_id, payment_methods_currency_code";
        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
        while ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql)) {
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']] = array();
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_id'] = $payment_fees_row['payment_fees_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_id'] = $payment_fees_row['payment_methods_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_currency_code'] = $payment_fees_row['payment_methods_currency_code'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_mode'] = $payment_fees_row['payment_methods_mode'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_operator'] = $payment_fees_row['payment_fees_operator'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_min'] = ((float) $payment_fees_row['payment_fees_min'] > 0) ? $payment_fees_row['payment_fees_min'] : '';
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_value'] = $payment_fees_row['payment_fees_cost_value'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent'] = $payment_fees_row['payment_fees_cost_percent'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent_min'] = $payment_fees_row['payment_fees_cost_percent_min'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent_max'] = $payment_fees_row['payment_fees_cost_percent_max'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_customers_groups_id'] = $payment_fees_row['payment_fees_customers_groups_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_customers_groups_name'] = $customer_groups[$payment_fees_row['payment_fees_customers_groups_id']];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_follow_group'] = $payment_fees_row['payment_fees_follow_group'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['currency_code'] = $payment_fees_row['currency_code'];
        }

        return $payment_fees_array;
    }

    function get_payment_fees_groups_been_followed($pmID, $surcharge_type = 'instant_purchase') {
        if (!tep_not_null($pmID))
            return array();

        if ($surcharge_type == 'sc_topup') {
            $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
        } else {
            $payment_fees_table_name = TABLE_PAYMENT_FEES;
        }

        $groups_array = array();

        $groups_select_sql = "	SELECT DISTINCT cg.customers_groups_id, cg.customers_groups_name
								FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg
								INNER JOIN " . $payment_fees_table_name . " AS pf
									ON cg.customers_groups_id=pf.payment_fees_customers_groups_id
								WHERE pf.payment_methods_id = '" . tep_db_input($pmID) . "'
									AND pf.payment_methods_mode = 'RECEIVE'
									AND pf.payment_fees_follow_group = '0'";
        $groups_result_sql = tep_db_query($groups_select_sql);

        while ($groups_row = tep_db_fetch_array($groups_result_sql)) {
            $groups_array[$groups_row['customers_groups_id']] = $groups_row['customers_groups_name'];
        }

        return $groups_array;
    }

    function get_payment_fees_following_group($pmID, $cust_grp_id, $surcharge_type = 'instant_purchase') {
        if (!tep_not_null($pmID) || !tep_not_null($cust_grp_id))
            return array('payment_fees_follow_group' => 0);

        if ($surcharge_type == 'sc_topup') {
            $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
        } else {
            $payment_fees_table_name = TABLE_PAYMENT_FEES;
        }

        $payment_fees_row = array();

        $payment_fees_select_sql = "	SELECT DISTINCT payment_fees_follow_group
										FROM " . $payment_fees_table_name . "
										WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
											AND payment_methods_mode = 'RECEIVE'
											AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'";
        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
        if ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql)) {
            return $payment_fees_row;
        }

        return $payment_fees_row;
    }

    function is_payment_fees_customer_group_has_setting($pmID, $cust_grp_id, $surcharge_type = 'instant_purchase') {
        if (!tep_not_null($pmID) || !tep_not_null($cust_grp_id))
            return false;
        $payment_fees_info = array();

        $payment_fees_info = $this->get_payment_fees_following_group($pmID, $cust_grp_id, $surcharge_type);

        if (count($payment_fees_info) > 0) {
            if ($payment_fees_info['payment_fees_follow_group'] == '0') {
                return true;
            } else {
                return false;
            }
        } else {
            return 2;
        }
    }

    function get_payment_fees_customers_groups($pmID, $surcharge_type = 'instant_purchase') {
        $cust_grp_array = array();

        $cust_grp_select_sql = "SELECT customers_groups_id, customers_groups_legend_color, customers_groups_name
								FROM " . TABLE_CUSTOMERS_GROUPS . "
								ORDER BY customers_groups_id";
        $cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
        while ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
            $color_row = $this->get_payment_fees_following_group($pmID, $cust_grp_row['customers_groups_id'], $surcharge_type);
            if ($color_row['payment_fees_follow_group'] > 0) {
                $color_select_sql = "SELECT customers_groups_legend_color FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id='" . tep_db_input($color_row['payment_fees_follow_group']) . "'";
                $color_data = tep_db_fetch_array(tep_db_query($color_select_sql));
                $legend_color = $color_data['customers_groups_legend_color'];
            } else {
                $legend_color = $cust_grp_row['customers_groups_legend_color'];
            }
            $has_setting = $this->is_payment_fees_customer_group_has_setting($pmID, $cust_grp_row['customers_groups_id'], $surcharge_type);
            $cust_grp_array[] = array('id' => $cust_grp_row['customers_groups_id'], 'name' => $cust_grp_row['customers_groups_name'], 'legend_color' => $legend_color, 'has_setting' => ($has_setting == 2 ? true : $has_setting));
        }

        return $cust_grp_array;
    }

    function get_payment_fees_by_customer_group($pmID, $cust_grp_id, &$fees_found = 0, $surcharge_type = 'instant_purchase') {
        if (!tep_not_null($pmID) || !tep_not_null($cust_grp_id))
            return array();

        if ($surcharge_type == 'sc_topup') {
            $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
        } else {
            $payment_fees_table_name = TABLE_PAYMENT_FEES;
        }

        $payment_fees_array = array();
        $payment_fees_currency_array = array();

        $check_parent_search_sql = "SELECT payment_methods_parent_id
									FROM " . TABLE_PAYMENT_METHODS . "
									WHERE payment_methods_id='" . tep_db_input($pmID) . "'";
        $check_parent_result_sql = tep_db_query($check_parent_search_sql);
        if ($check_parent_row = tep_db_fetch_array($check_parent_result_sql)) {
            if ($check_parent_row['payment_methods_parent_id'] > 0) {
                $fees_found = 2; // follow parent settings
            }
        }

        // get customer groups name
        $customer_groups = array();
        $cust_grp_select_sql = "SELECT * FROM " . TABLE_CUSTOMERS_GROUPS . "
								WHERE customers_groups_id = '" . tep_db_input($cust_grp_id) . "'
								ORDER BY customers_groups_id";
        $cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
        while ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
            $customer_groups[$cust_grp_row['customers_groups_id']] = $cust_grp_row['customers_groups_name'];
        }

        foreach ($customer_groups as $cust_grp_id => $cust_grp_name) {
            $payment_fees_array[$pmID][$cust_grp_id] = array();

            // first find all existing payment methods currency code from payment_fees table
            $payment_fees_currency_select_sql = "SELECT DISTINCT payment_methods_currency_code
												FROM " . $payment_fees_table_name . "
												WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
												AND payment_methods_mode = 'RECEIVE'
												AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'
												ORDER BY payment_methods_currency_code";
            $payment_fees_currency_result_sql = tep_db_query($payment_fees_currency_select_sql);
            while ($payment_fees_currency_row = tep_db_fetch_array($payment_fees_currency_result_sql)) {
                $payment_fees_currency_array[] = $payment_fees_currency_row['payment_methods_currency_code'];
                $payment_fees_array[$pmID][$cust_grp_id][$payment_fees_currency_row['payment_methods_currency_code']] = array();
            }

            // second find the missing currencies from payment_methods_instance table
            $currency_list = implode("','", $payment_fees_currency_array);
            $payment_methods_instance_select_sql = "SELECT DISTINCT currency_code
													FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
													WHERE payment_methods_id = '" . tep_db_input($pmID) . "'";
            $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
            if (tep_db_num_rows($payment_methods_instance_result_sql) > 0) {
                $pmi_select_sql = "	SELECT DISTINCT currency_code
									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
									WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
									AND currency_code NOT IN ('" . $currency_list . "')";
                $pmi_result_sql = tep_db_query($pmi_select_sql);
                while ($pmi_row = tep_db_fetch_array($pmi_result_sql)) {
                    $payment_fees_array[$pmID][$cust_grp_id][$pmi_row['currency_code']] = array();
                }
            } else {
                // No record in payment_methods_instance table, search from parent
                $parent_pm_instance_select_sql = "	SELECT DISTINCT pmi.currency_code
													FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " AS pmi
													WHERE pmi.payment_methods_id = (
														SELECT pm.payment_methods_parent_id
														FROM " . TABLE_PAYMENT_METHODS . " AS pm
														WHERE pm.payment_methods_id='" . tep_db_input($pmID) . "')";
                $parent_pm_instance_result_sql = tep_db_query($parent_pm_instance_select_sql);
                if (tep_db_num_rows($parent_pm_instance_result_sql) > 0) {
                    while ($parent_pm_instance_row = tep_db_fetch_array($parent_pm_instance_result_sql)) {
                        $payment_fees_array[$pmID][$cust_grp_id][$parent_pm_instance_row['currency_code']] = array();
                    }
                }
            }
        }

        // now payment_fees_array should have the complete currency list, next to populate the fees setting data
        $payment_fees_select_sql = "	SELECT payment_fees_id, payment_methods_id, payment_methods_currency_code, payment_methods_mode, payment_fees_operator, payment_fees_min, payment_fees_cost_value,
												payment_fees_cost_percent, payment_fees_cost_percent_min, payment_fees_cost_percent_max, currency_code,
												payment_fees_customers_groups_id, payment_fees_follow_group
										FROM " . $payment_fees_table_name . "
										WHERE payment_methods_id = '" . tep_db_input($pmID) . "'
										AND payment_methods_mode = 'RECEIVE'
										AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'
										ORDER BY payment_fees_customers_groups_id, payment_methods_currency_code";
        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
        if (tep_db_num_rows($payment_fees_result_sql) > 0) {
            $fees_found = 1;
        }
        while ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql)) {
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']] = array();
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_id'] = $payment_fees_row['payment_fees_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_id'] = $payment_fees_row['payment_methods_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_currency_code'] = $payment_fees_row['payment_methods_currency_code'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_methods_mode'] = $payment_fees_row['payment_methods_mode'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_operator'] = $payment_fees_row['payment_fees_operator'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_min'] = ((float) $payment_fees_row['payment_fees_min'] > 0) ? $payment_fees_row['payment_fees_min'] : '';
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_value'] = $payment_fees_row['payment_fees_cost_value'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent'] = $payment_fees_row['payment_fees_cost_percent'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent_min'] = $payment_fees_row['payment_fees_cost_percent_min'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_cost_percent_max'] = $payment_fees_row['payment_fees_cost_percent_max'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_customers_groups_id'] = $payment_fees_row['payment_fees_customers_groups_id'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_customers_groups_name'] = $customer_groups[$payment_fees_row['payment_fees_customers_groups_id']];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['payment_fees_follow_group'] = $payment_fees_row['payment_fees_follow_group'];
            $payment_fees_array[$pmID][$payment_fees_row['payment_fees_customers_groups_id']][$payment_fees_row['payment_methods_currency_code']]['currency_code'] = $payment_fees_row['currency_code'];
        }

        return $payment_fees_array;
    }

    function check_payment_fees_fields(&$fields_array, &$error_message) {
        if (count($fields_array) <= 0)
            return false;
        $error_flag = false;
        $fees_id_array = array();

        foreach ($fields_array as $key => $value) {
            list($field_name, $cgrp_id, $curr_code, $fees_id) = explode('-', $key);
            if (!array_key_exists($fees_id, $fees_id_array)) {
                $fees_id_array[$fees_id] = $cgrp_id . '-' . $curr_code;
            }

            switch ($field_name) {
                case "payment_fees_min":
                    if (trim($value) == '') {
                        $fields_array[$key] = 0;
                    } else if (is_numeric($value)) {
                        $fields_array[$key] = tep_round($value, 2);
                    } else {
                        $error_flag = true;
                        $error_message[] = "Apply to Order Amount for " . $curr_code . " must be in numerical value.";
                    }
                    break;
                case "payment_fees_cost_value":
                    if (trim($value) == '') {
                        $fields_array[$key] = 0;
                    } else if (is_numeric($value)) {
                        $fields_array[$key] = tep_round($value, 2);
                    } else {
                        $error_flag = true;
                        $error_message[] = "Transaction Fees for " . $curr_code . " must be in numerical value.";
                    }
                    break;
                case "payment_fees_cost_percent":
                    if (trim($value) == '') {
                        $fields_array[$key] = 0;
                    } else if (is_numeric($value)) {
                        $fields_array[$key] = tep_round($value, 2);
                    } else {
                        $error_flag = true;
                        $error_message[] = "Transaction Fees Percentage for " . $curr_code . " must be in numerical value.";
                    }
                    break;
                case "payment_fees_cost_percent_min":
                    if (trim($value) == '') {
                        $fields_array[$key] = 0;
                    } else if (is_numeric($value)) {
                        $fields_array[$key] = tep_round($value, 2);
                    } else {
                        $error_flag = true;
                        $error_message[] = "Mininum Fees for " . $curr_code . " must be in numerical value.";
                    }
                    break;
                case "payment_fees_cost_percent_max":
                    if (trim($value) == '') {
                        $fields_array[$key] = 0;
                    } else if (is_numeric($value)) {
                        $fields_array[$key] = tep_round($value, 2);
                    } else {
                        $error_flag = true;
                        $error_message[] = "Maximum Fees for " . $curr_code . " must be in numerical value.";
                    }
                    break;
            }
        }

        foreach ($fees_id_array as $fees_id => $combine_keys) {
            list($cgrp_id, $curr_code) = explode('-', $combine_keys);
            $key1 = 'payment_fees_cost_percent_min' . '-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id;
            $key2 = 'payment_fees_cost_percent_max' . '-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id;
            if (tep_not_null($fields_array[$key1]) && tep_not_null($fields_array[$key2])) {
                if ($fields_array[$key1] > $fields_array[$key2]) {
                    $error_flag = true;
                    $error_message[] = '1st set of Minimum Fees can not be greater than Maximum Fees for ' . $curr_code . '.';
                }
            }

            $key3 = 'payment_fees_cost_percent_min' . '-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id;
            $key4 = 'payment_fees_cost_percent_max' . '-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id;
            if (tep_not_null($fields_array[$key3]) && tep_not_null($fields_array[$key4])) {
                if ($fields_array[$key3] > $fields_array[$key4]) {
                    $error_flag = true;
                    $error_message[] = '2nd set of Minimum Fees can not be greater than Maximum Fees for ' . $curr_code . '.';
                }
            }
        }

        if ($error_flag) {
            return false;
        } else {
            return true;
        }
    }

    function get_payment_methods_types($mode = 'RECEIVE') {
        $payment_methods_types_select_sql = "	SELECT payment_methods_types_id, payment_methods_types_name
												FROM " . TABLE_PAYMENT_METHODS_TYPES . "
												WHERE payment_methods_types_mode = '" . tep_db_input($mode) . "'
												ORDER BY payment_methods_types_sort_order";
        $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
        $payment_methods_types_array = array();
        while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
            $payment_methods_types_array[$payment_methods_types_row['payment_methods_types_id']] = $payment_methods_types_row['payment_methods_types_name'];
        }
        return $payment_methods_types_array;
    }

    function get_payment_methods_send_available_sites() {
        $send_sites_select_sql = "SELECT site_id, site_name
									FROM " . TABLE_SITE_CODE . "
									WHERE site_id IN (0,3)";
        $send_sites_result_sql = tep_db_query($send_sites_select_sql);
        $availale_sites_array = array();
        while ($send_sites_row = tep_db_fetch_array($send_sites_result_sql)) {
            $availale_sites_array[$send_sites_row['site_id']] = $send_sites_row['site_name'];
        }
        return $availale_sites_array;
    }

    function set_payment_methods_filename() {
        $payment_methods_filename_select_sql = "SELECT payment_methods_filename
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_id = '" . (int) ((int) $this->payment_methods_parent_id > 0 ? $this->payment_methods_parent_id : $this->payment_methods_id) . "'";
        $payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
        if ($payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql)) {
            $this->payment_methods_filename = $payment_methods_filename_row['payment_methods_filename'];
        } else {
            $this->payment_methods_filename = '';
        }
    }

    public static function get_payment_methods_filename($payment_methods_id = '') {
        global $memcache_obj;

        $cache_key = TABLE_PAYMENT_METHODS . '/payment_methods_id/' . (int) $payment_methods_id . '/payment_methods_filename';
        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $payment_methods_filename = $cache_result;
        } else {
            $payment_methods_filename_select_sql = "SELECT payment_methods_filename
													FROM " . TABLE_PAYMENT_METHODS . "
													WHERE payment_methods_id = '" . (int) $payment_methods_id . "'";
            $payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
            $payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);
            $payment_methods_filename = $payment_methods_filename_row['payment_methods_filename'];
            $memcache_obj->store($cache_key, $payment_methods_filename, 86400);
        }
        return $payment_methods_filename;
    }

    function get_number_of_child() {
        $payment_methods_select_sql = "	SELECT pm.payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . " as pm
										WHERE pm.payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        return tep_db_num_rows($payment_methods_result_sql);
    }

    function get_payment_methods_class_name() {
        return $this->payment_methods_class_name;
    }

    function get_status_message() {
        return $this->payment_methods_status_message;
    }

    function get_types_id() {
        return $this->payment_methods_types_id;
    }

    function get_types_name() {
        return $this->payment_methods_types_name;
    }

    function get_logo() {
        return $this->payment_methods_logo;
    }

    public static function get_tax_option() {
        $tax = array();
        $tax_sel_sql = "SELECT otc.country_code, otc.orders_tax_percentage, otcd.orders_tax_title, c.countries_name 
                        FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
                        INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd 
                            ON otcd.orders_tax_id = otc.orders_tax_id 
                        LEFT JOIN " . TABLE_COUNTRIES . " AS c 
                            ON c.countries_iso_code_2 = otc.country_code 
                        WHERE otcd.language_id = '1'
                        ORDER BY otc.country_code";
        $tax_res_sql = tep_db_query($tax_sel_sql);
        if (tep_db_num_rows($tax_res_sql)) {
            $tax[] = PULL_DOWN_DEFAULT;
            while ($tax_row = tep_db_fetch_array($tax_res_sql)) {
                $tax[$tax_row["country_code"]] = $tax_row["countries_name"] . " " . $tax_row["orders_tax_title"] . " " . $tax_row["orders_tax_percentage"] . "%";
            }
        }

        return $tax;
    }

    function get_title() {
        return $this->payment_methods_title;
    }

    function get_display_title() {
        return $this->payment_methods_description_title;
    }

    function get_send_title() {
        return $this->payment_methods_send_mode_name;
    }

    function get_sort_order() {
        return $this->payment_methods_sort_order;
    }

    function get_code() {
        return $this->payment_methods_code;
    }

    function get_legend_color() {
        return $this->payment_methods_legend_color;
    }

    function get_receive_flag() {
        return $this->payment_methods_receive_status;
    }

    function get_receive_status() {
        return $this->payment_methods_receive_status_mode;
    }

    function get_parent_id() {
        return $this->payment_methods_parent_id;
    }

    function get_filename() {
        return $this->payment_methods_filename;
    }

    function get_instance_key_info() {
        return $this->payment_methods_instance_key_info;
    }

    function get_send_status_message() {
        return $this->payment_methods_send_status_message;
    }
    
    function get_send_code() {
        return $this->payment_methods_send_code;
    }

    // SEND PAYMENT
    function get_send_status() {
        return $this->payment_methods_send_status_mode;
    }

    function get_send_flag() {
        return $this->payment_methods_send_status;
    }

    function get_send_payment_info() {
        $send_payment_info_array = array();
        $send_payment_info_select_sql = "	SELECT pmf.*
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
											WHERE pmf.payment_methods_id = '" . (int) $this->payment_methods_id . "'
												AND pmf.payment_methods_mode  = 'SEND'
											ORDER BY payment_methods_fields_sort_order";
        $send_payment_info_result_sql = tep_db_query($send_payment_info_select_sql);
        while ($send_payment_info_row = tep_db_fetch_array($send_payment_info_result_sql)) {
            $send_payment_info_array[$send_payment_info_row['payment_methods_fields_id']] = $send_payment_info_row;
        }
        return $send_payment_info_array;
    }

}

?>