<?php
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PAYMENT_MODULE_INFO)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PAYMENT_MODULE_INFO);
}

class payment_module_info {
	var $payment_accounts, $identity, $identity_email, $max_entry, $user_input_field_type, $change_log;
	var $user_id, $user_role, $avail_sites;
	
	// class constructor
	function payment_module_info($identity, $identity_email, $customers_id, $avail_sites = '0') {
		$this->payment_accounts = array();

		if (tep_not_null($customers_id)) {
			$this->payment_accounts = $this->_get_current_payment_accounts($customers_id, 'customers', $avail_sites);
		}

		$this->identity = $identity;	// Admin user
		$this->identity_email = $identity_email;	// Admin user

		$this->user_id = $customers_id;
		$this->user_role = 'customers';
		$this->avail_sites = $avail_sites;

		$this->user_input_field_type = array(1, 2, 3, 4, 5);
        $this->change_log = '';
		$this->max_entry = MAX_PO_SUPPLIER_PAYMENT_BOOK_ENTRIES;
	}
    
    function _get_current_payment_accounts($user_id, $user_role, $avail_sites = '0') {
		$payment_accounts = array();

		$payment_acc_select_sql = "	SELECT pab.*, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_available_sites
										FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
										INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
												ON pab.payment_methods_id=pm.payment_methods_id
										WHERE pab.user_id = '" . tep_db_input($user_id) . "'
												AND pab.user_role = '" . tep_db_input($user_role) . "'
										ORDER BY pab.payment_methods_alias";
		$payment_acc_result_sql = tep_db_query($payment_acc_select_sql);

		while ($payment_acc_row = tep_db_fetch_array($payment_acc_result_sql)) {
			if (($avail_sites == 'ALL') || (strstr($payment_acc_row['payment_methods_send_available_sites'], $avail_sites) !== false)) {
				$payment_accounts[$payment_acc_row['store_payment_account_book_id']] = array(	'pm_id' => $payment_acc_row['payment_methods_id'],
																								'pm_alias' => $payment_acc_row['payment_methods_alias'],
																								'pm_name' => $payment_acc_row['payment_methods_send_mode_name'],
																								'pm_currency' => $payment_acc_row['payment_methods_send_currency'],
																								'pm_avail_sites' => $payment_acc_row['payment_methods_send_available_sites']);
				
				$payment_acc_field_select_sql = "	SELECT pabd.payment_methods_fields_id, pabd.payment_methods_fields_value, pmf.payment_methods_fields_title, pmf.payment_methods_fields_type  
														FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS pabd
														INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
															ON pabd.payment_methods_fields_id=pmf.payment_methods_fields_id
														WHERE pabd.store_payment_account_book_id = '" . tep_db_input($payment_acc_row['store_payment_account_book_id']) . "'
														AND pmf.payment_methods_fields_type <> 7
														ORDER BY pmf.payment_methods_fields_sort_order";
				$payment_acc_field_result_sql = tep_db_query($payment_acc_field_select_sql);
				
				while ($payment_acc_field_row = tep_db_fetch_array($payment_acc_field_result_sql)) {
					$payment_accounts[$payment_acc_row['store_payment_account_book_id']]['field'][] = $payment_acc_field_row;
				}
			}
		}

		return $payment_accounts;
    }
	
    function get_existing_payment_account_selection($include_option_title=false) {
		$pm_array = array();
		
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		if (isset($this->payment_accounts) && count($this->payment_accounts)) {
			foreach ($this->payment_accounts as $pm_book_id => $pm_book_info) {
				$pm_array[] = array('id' => $pm_book_id, 'text' => $pm_book_info['pm_alias']);
			}
		}
		
		return $pm_array;
    }
	
    function list_payment_account_book($filename, $avail_sites = '0') {
		$payment_list_content = '';
		
		$payment_list_content = '
								<table border="1" cellspacing="0" cellpadding="5" width="80%">
									<tr>
										<td class="invoiceBoxHeading">' . TABLE_HEADING_PAYMENT_ACCOUNT . '</td>
										<td class="invoiceBoxHeading">' . TABLE_HEADING_PAYMENT_INFO . '</td>
										<td class="invoiceBoxHeading">' . TABLE_HEADING_PAYMENT_ACTION . '</td>
									</tr>
								';
		
		if (!count($this->payment_accounts)) {
			$payment_list_content .= '<tr><td class="main" colspan="3">'.TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT.'</td></tr>';
		} else {
			foreach ($this->payment_accounts as $book_id => $book_info) {
				$pma_details = '';
				
				// listed payment account details
				if (tep_not_null($this->payment_accounts[$book_id]['field'])) {
					foreach ($this->payment_accounts[$book_id]['field'] as $field_num => $pma_fields) {
						if (in_array($pma_fields['payment_methods_fields_type'], $this->user_input_field_type)) {
							$pma_details .= $pma_fields['payment_methods_fields_title'] .': '. $pma_fields['payment_methods_fields_value'] .'<br>';
						}
					}
				}
				
				$invalid_site = false;
				if (($avail_sites != 'ALL') && (strstr($book_info['pm_avail_sites'], $avail_sites) === false)) {
					$invalid_site = true;
				}
				
				$safe_pm_title = htmlspecialchars(addslashes($book_info['pm_alias']), ENT_QUOTES);
				$payment_list_content .= '
										<tr>
											<td class="main">'.$book_info['pm_alias'].'</td>
											<td class="main">
												<b>'.$book_info['pm_name'].'</b><br>'. $pma_details .
												($invalid_site ? '<br><b><span class="errorText">This disbursement method has been set to inactive.</span></b>' : '') .
											'</td>
											<td class="main"> 
												<div>' .
													($invalid_site ? '' : '<a href="javascript:void(0);" onclick="editPOPMBook(\'pm_book_details\', \''.$book_id.'\', \''.$this->user_id.'\')">Edit</a>&nbsp;&nbsp;|&nbsp;&nbsp;') . 
													'<a href="javascript:void(0);" onclick="confirm_delete(\''.$safe_pm_title.'\', \''.HEADING_TITLE.'\', \''.tep_href_link($filename, tep_get_all_get_params(array('action','subaction')).'action=delete_pm&subaction=confirm_delete_pm&book_id='.$book_id).'\')">Delete</a>
												</div> 
											</td>
										</tr>';
			}
		}
		
		$payment_list_content .= '</table>';
		
		$payment_list_html = '
								<table border="0" cellspacing="0" cellpadding="2" width="80%">
									<tr>
										<td>'.$payment_list_content.'</td>
									</tr>';
		
		if ($this->max_entry < 0 || count($this->payment_accounts) < $this->max_entry) {
			$payment_list_html .= '
									<tr>
										<td>'.tep_button(BUTTON_ADD_PM, ALT_BUTTON_ADD_PM, '', ' onClick="addPOPMBook(\'pm_book_details\', \''.$this->user_id.'\'); return false;"', 'inputButton').'</td>
									</tr>';
		}
		else if (count($this->payment_accounts) == $this->max_entry) {
			$payment_list_html .= '
                                    <tr>
			                            <td>
											<table border="0" cellspacing="1" cellpadding="2" width="80%">
												<tr>
													<td>'. sprintf(TEXT_PM_REACHED_MAX, $this->max_entry) .'</td>
												</tr>
											</table>
										</td>
									</tr>';
		}
		
		$payment_list_html .= '
									<tr>
										<td><div id="pm_book_details"></div></td>
									</tr>';
		
		$payment_list_html .= '</table>';
		
		return $payment_list_html;
    }
	
    function new_payment_account() {
		global $currencies, $currency;
		
		$currency_drop_down_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
		$currency_list = $currencies->get_currency_set();
		for ($i=0, $n=sizeof($currency_list); $i<$n; $i++) {
			$currency_drop_down_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
		}
		
		$payment_list_html = '';
		
		if ($this->max_entry > 0) {
			if ($this_action == 'add' && count($this->payment_accounts) >= $this->max_entry) {
				$payment_list_html = '
									<tr>
										<td class="main" width="20%"></td>
										<td class="main"></td>
									</tr>';
			}
		}
		
		if (!tep_not_null($payment_list_html)) {
			$payment_list_html = '
									<tr>
										<td class="main" width="20%">'.ENTRY_FORM_PM_ALIAS.'</td>
										<td class="main">'.tep_draw_input_field('pm_alias', '', 'id="pm_alias" size="30"').'</td>
									</tr>
									<tr>
										<td class="main" width="20%">'.ENTRY_FORM_PM_SELECT_CURRENCY.'</td>
										<td class="main">'.tep_draw_pull_down_menu('pm_currency', $currency_drop_down_array, '', 'id="pm_currency" onChange="getPOPMLists(this, \'pm_id\', \'pm_field_div\', \'\', \'\', \''.$this->user_id.'\');"').'<span style="font-size:10px;margin-left:10px;">'.ENTRY_FORM_PM_SELECT_CURRENCY_INFO.'</span></td>
									</tr>
									<tr>
										<td class="main" width="20%">'.ENTRY_FORM_PM_SELECT_PM.'</td>
										<td class="main">'.tep_draw_pull_down_menu('pm_id', array(), '', 'id="pm_id" onChange="getPOPMOptions(this, \'pm_field_div\', \'\', \''.$this->user_id.'\');"').'</td>
									</tr>
									<tr>
										<td class="inputLabel" colspan="2">
											<div id="pm_field_div"></div>
										</td>
									</tr>
									<script>
										jQuery("#pm_id").ready(function (){';
			$payment_list_html .= 'getPOPMLists(document.getElementById("pm_currency"), "pm_id", "pm_field_div", "");';
			$payment_list_html .= '									});
									</script>';
		}
		
		return $payment_list_html;
    }
	
    function edit_payment_account($book_id) {
		global $currencies, $currency;
		
		$currency_drop_down_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
		$currency_list = $currencies->get_currency_set();
		for ($i=0, $n=sizeof($currency_list); $i<$n; $i++) {
			$currency_drop_down_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
		}
		
		$payment_list_html = '';
		
		if (!tep_not_null($payment_list_html)) {
			$payment_list_html = tep_draw_hidden_field("book_id", $book_id) . 
								'<tr>
									<td class="main" width="20%">'.ENTRY_FORM_PM_ALIAS.'</td>
									<td class="main">'.tep_draw_input_field('pm_alias', $this->payment_accounts[$book_id]['pm_alias'], 'id="pm_alias" size="30"').'</td>
								</tr>
								<tr>
									<td class="main" width="20%">'.ENTRY_FORM_PM_SELECT_CURRENCY.'</td>
									<td class="main">'.tep_draw_pull_down_menu('pm_currency', $currency_drop_down_array, $currencies->get_code_by_id($this->payment_accounts[$book_id]['pm_currency']), 'id="pm_currency" onChange="getPOPMLists(this, \'pm_id\', \'pm_field_div\', \''.$this->payment_accounts[$book_id]['pm_id'].'\', \''.$book_id.'\');"').'</td>
								</tr>
								<tr class="inputBoxContents">
									<td class="main" width="20%">'.ENTRY_FORM_PM_SELECT_PM.'</td>
									<td class="main">'.tep_draw_pull_down_menu('pm_id', $this->_get_all_active_send_payment_methods(true, $this->payment_accounts[$book_id]['pm_currency']), $this->payment_accounts[$book_id]['pm_id'], 'id="pm_id" onChange="getPOPMOptions(this, \'pm_field_div\', \''.$book_id.'\', \''.$this->user_id.'\');"').'</td>
								</tr>
								<tr>
									<td class="inputLabel" colspan="2">
										<div id="pm_field_div"></div>
									</td>
								</tr>
								<script>
									jQuery("#pm_id").ready(function (){';
			$payment_list_html .= 'getPOPMOptions(document.getElementById("pm_id"), "pm_field_div", "'.$book_id.'", "'.$this->user_id.'");';
			$payment_list_html .= '									});
								</script>';
		}
		
		return $payment_list_html;
    }
	
    function insert_payment_account($input_array, &$messageStack) {
		
		if ($this->max_entry > 0 && count($this->payment_accounts) >= $this->max_entry) {	// Exceed maximum allowed payment account entry
			$messageStack->add_session(ERROR_PM_ACCOUNT_BOOK_FULL, 'error');
			return true;
		}
		
		$error = false;
		
		$account_book_data_array = array();
		$account_book_details_data_array = array();
		
		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_types_id 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."'
											AND payment_methods_send_status = 1 
											AND payment_methods_send_status_mode = 1";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		
		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			
			$account_book_data_array = array(	'user_id' => $this->user_id,
												'user_role' => $this->user_role,
												'payment_methods_id' => $input_array['pm_id'],
												'payment_methods_alias' => tep_db_input($input_array['pm_alias'])
											);
			
			$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
											WHERE payment_methods_id = '" . tep_db_input($input_array['pm_id']) . "'
												AND payment_methods_mode = 'SEND'
												AND payment_methods_fields_status = 1";
			$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
			
			while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
				if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
					$error = true;
					$messageStack->add_session(sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
				}
				
				$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
															'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
														);
			}
		} else {
			$error = true;
		}
		
		if (!$error) {
			if (count($account_book_data_array)) {
				tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $account_book_data_array);
				$payment_account_book_id = tep_db_insert_id();
				
				for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
					$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $payment_account_book_id;
					tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
				}
			}
			return true;
		} else {
			return false;
		}
    }
	
    function update_payment_account($input_array, &$messageStack) {
		$error = false;
		$account_book_details_data_array = array();
        $payment_input_title_array = array();
        $this->change_log = '';
        
		if (isset($input_array['book_id'])) {
			$payment_method_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."'
												AND payment_methods_send_status = 1 
												AND payment_methods_send_status_mode = 1";
			$payment_method_result_sql = tep_db_query($payment_method_select_sql);
			
			if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
				$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required
												FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
												WHERE payment_methods_id = '" . tep_db_input($payment_method_row['payment_methods_id']) . "'
													AND payment_methods_mode = 'SEND'
													AND payment_methods_fields_status = 1";
				$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
				
				while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
                    $payment_input_title_array[$payment_fields_row['payment_methods_fields_id']] = $payment_fields_row['payment_methods_fields_title'];
                    
					if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
						$error = true;
						$messageStack->add_session(sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
					}
					
					$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
																'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
															);
				}
			} else {
				$error = true;
			}
			
			if (!$error) {
				$account_book_update_sql = "UPDATE " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " SET payment_methods_id='".tep_db_input($input_array['pm_id'])."', payment_methods_alias = '".tep_db_input($input_array['pm_alias'])."' WHERE store_payment_account_book_id = '".tep_db_input($input_array['book_id'])."'";
				tep_db_query($account_book_update_sql);
                
                $old_acc_book_info = array();
                $old_account_book_select_sql = "SELECT payment_methods_fields_id, payment_methods_fields_value 
                                                FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " 
                                                WHERE store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'";
				$old_account_book_result_sql = tep_db_query($old_account_book_select_sql);
                while ($old_account_book_row = tep_db_fetch_array($old_account_book_result_sql)) {
                    $old_acc_book_info[$old_account_book_row['payment_methods_fields_id']] = $old_account_book_row['payment_methods_fields_value'];
                }
                
				$account_book_field_delete_sql = "DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " WHERE store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'";
				tep_db_query($account_book_field_delete_sql);

				for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
                    $pm_field_id = $account_book_details_data_array[$acc_book_detail_cnt]['payment_methods_fields_id'];
                    if ($account_book_details_data_array[$acc_book_detail_cnt]['payment_methods_fields_value'] != $old_acc_book_info[$pm_field_id]) {
                        $this->change_log .= $payment_input_title_array[$pm_field_id] . ': ' . $old_acc_book_info[$pm_field_id] .' --> ' . $account_book_details_data_array[$acc_book_detail_cnt]['payment_methods_fields_value'] . "\n";
                    }
                    
					$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $input_array['book_id'];
					tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
				}
				return true;
			} else {
				return false;
			}
		} else {
			return $this->insert_payment_account($input_array, $messageStack);
		}
    }
	
    function delete_payment_account($pm_book_id, &$messageStack) {
		if (isset($pm_book_id)) {
			$account_book_delete_sql = "DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " WHERE store_payment_account_book_id = '" . tep_db_input($pm_book_id) . "'";
			tep_db_query($account_book_delete_sql);
			
			$account_book_field_delete_sql = "DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " WHERE store_payment_account_book_id = '" . tep_db_input($pm_book_id) . "'";
			tep_db_query($account_book_field_delete_sql);
			
			$messageStack->add_session(SUCCESS_PM_ACCOUNT_BOOK_DELETED, 'success');
			
			return true;
		} else {
			$messageStack->add_session(ERROR_PM_ACCOUNT_BOOK_DELETE, 'error');
			return false;
		}
    }
	
    function _get_all_active_send_payment_methods($include_option_title=false, $currency_id='') {
		$pm_array = array();
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_send_mode_name, payment_methods_send_available_sites
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_send_status = 1
												AND payment_methods_send_status_mode = 1 
												AND payment_methods_parent_id <> '0' 
												" . (tep_not_null($currency_id) ? "AND payment_methods_send_currency = '" . tep_db_input($currency_id) . "'" : "") . "
										ORDER BY payment_methods_sort_order";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
            if ($this->avail_sites > 0) {
                if (strstr($payment_method_row['payment_methods_send_available_sites'], $this->avail_sites) !== false) {
                    $pm_array[] = array('id' => $payment_method_row['payment_methods_id'], 'text' => $payment_method_row['payment_methods_send_mode_name']);
                }
            } else {
                $pm_array[] = array('id' => $payment_method_row['payment_methods_id'], 'text' => $payment_method_row['payment_methods_send_mode_name']);
            }
		}
		
		return $pm_array;
    }
	
    function get_payment_account_book_id($payment_methods_id, $currency_id) {
        // Get Payment Method Info
        $payment_book_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, 
                                    pm.payment_methods_send_currency, spab.payment_methods_alias, 
                                    pm.payment_methods_types_id, spab.store_payment_account_book_id 
                                    FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
                                        ON spab.payment_methods_id=pm.payment_methods_id 
                                    INNER JOIN " . TABLE_CURRENCIES . " AS c 
                                        ON (pm.payment_methods_send_currency = c.currencies_id 
                                        AND c.code = '" . tep_db_input($currency_id) . "')
                                    WHERE pm.payment_methods_id = '".tep_db_input($payment_methods_id)."' 
                                        AND spab.user_id = '".tep_db_input($this->user_id)."' 
                                        AND spab.user_role = 'customers' 
                                        AND pm.payment_methods_send_status = 1 
                                        AND pm.payment_methods_send_status_mode = 1";
        $payment_book_result_sql = tep_db_query($payment_book_select_sql);
        $payment_book_row = tep_db_fetch_array($payment_book_result_sql);
        
        return $payment_book_row['store_payment_account_book_id'];
    }
            
	function get_payment_account_book_detail($acct_book_id) {
		$acct_book_info_arr = array();
		
		if (tep_not_null($acct_book_id)) {
			$payment_acc_select_sql = "	SELECT pab.*, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency 
											FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab 
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
												ON pab.payment_methods_id=pm.payment_methods_id 
											WHERE pab.user_id = '" . tep_db_input($this->user_id) . "' 
												AND pab.user_role = '" . tep_db_input($this->user_role) . "' 
												AND pab.store_payment_account_book_id = '" . tep_db_input($acct_book_id) . "' 
											ORDER BY pab.payment_methods_alias";
			$payment_acc_result_sql = tep_db_query($payment_acc_select_sql);
			if ($payment_acc_row = tep_db_fetch_array($payment_acc_result_sql)) {
				$acct_book_info_arr[$payment_acc_row['store_payment_account_book_id']] = array(	'pm_id' => $payment_acc_row['payment_methods_id'],
																								'pm_alias' => $payment_acc_row['payment_methods_alias'],
																								'pm_name' => $payment_acc_row['payment_methods_send_mode_name'],
																								'pm_currency' => $payment_acc_row['payment_methods_send_currency']);
				
				$payment_acc_field_select_sql = "	SELECT pabd.payment_methods_fields_id, pabd.payment_methods_fields_value, pmf.payment_methods_fields_title, pmf.payment_methods_fields_type  
														FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS pabd
														INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
															ON pabd.payment_methods_fields_id=pmf.payment_methods_fields_id
														WHERE pabd.store_payment_account_book_id = '" . tep_db_input($payment_acc_row['store_payment_account_book_id']) . "'
														AND pmf.payment_methods_fields_type <> 7
														ORDER BY pmf.payment_methods_fields_sort_order";
				$payment_acc_field_result_sql = tep_db_query($payment_acc_field_select_sql);
				
				while ($payment_acc_field_row = tep_db_fetch_array($payment_acc_field_result_sql)) {
					$acct_book_info_arr[$payment_acc_row['store_payment_account_book_id']]['field'][] = $payment_acc_field_row;
				}
			}
		}
		
		return $acct_book_info_arr;
	}
}
?>