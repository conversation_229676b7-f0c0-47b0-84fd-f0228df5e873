<?php
include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'po_suppliers.php');
include_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
include_once(DIR_WS_CLASSES . 'dtu_payment.php');
include_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
include_once(DIR_WS_CLASSES . 'consignment_payment.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');
include_once(DIR_WS_CLASSES . 'c2c_invoice.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');
require_once(DIR_WS_CLASSES . 'g2g_serverless.php');

class payments {
    var $identity, $identity_email, $user_roles, $currency_display_decimal;
	var $info, $payment_info, $beneficiary, $ot;
	
	// class constructor
    function payments($identity, $identity_email) {
      	$this->identity = $identity;	// Admin user
      	$this->identity_email = $identity_email;	// Admin user
      	
      	$this->user_roles = array (	'customers' => "www.offgamers.com",
									'supplier' => "supplier.offgamers.com"
									);
		
		$this->info = array();
      	$this->payment_info = array();
      	$this->beneficiary = array();
      	$this->trans_info = array();
		
		$this->currency_display_decimal = 2;
	}
	
	function search_acc_statement($filename, $session_name) {
		$acc_statement_html = '';
		
		$user_role_options = array 	(	array ('id' => 'customers', "text" => "www.offgamers.com"),
										array ('id' => 'supplier', "text" => "supplier.offgamers.com")
									);
                
                $order_type = array(
                                        array ('id' => 'PO', "text" => "OffGamers Purchase Order (POWSC)"),
									);
						
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
	  								array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
								);
		
		ob_start();
		?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('payments_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="0" cellpadding="0">
									<tr>
	          							<td class="main"><?=ENTRY_USER_ROLE?></td>
	          							<td class="main"><?=tep_draw_pull_down_menu("user_role", $user_role_options, tep_not_null($_SESSION[$session_name]["user_role"]) ? $_SESSION[$session_name]["user_role"] : '', 'id="user_role"')?></td>
			    						<td class="main">&nbsp;</td>
			    						<td class="main"><?=ENTRY_USER_EMAIL?></td>
			    						<td class="main">&nbsp;</td>
			    						<td class="main">
			    							<?=tep_draw_input_field('user_email', $_SESSION[$session_name]["user_email"], ' id="user_email" size="30" ')?>
			    							<a href="javascript:;" onClick="searchCustomersPopUp('user_email', payments_criteria.user_role.value == 'supplier' ? 'supplier_email_address' : 'customers_email_address')"><?=tep_image(DIR_WS_ICONS . 'search.jpg', IMAGE_ICON_SEARCH, 16, 16)?></a>
			    						</td>
			    						<td class="main" colspan="3">&nbsp;</td>
									</tr>
									<tr>
	            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
									<tr>
										<td class="main"><?=ENTRY_ORDER_START_DATE?></td>
										<td class="main" valign="top" nowrap>
    										<script language="javascript"><!--
  												var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "payments_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
  												date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION[$session_name]["start_date"]?>';
											//--></script>
    									</td>
    									<td class="main" width="10%">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
    									<td class="main">&nbsp;</td>
    									<td class="main" valign="top" colspan="4">
			    							<script language="javascript"><!--
			  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "payments_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
			  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION[$session_name]["end_date"]?>';
											//--></script>
			    						</td>
									</tr>
									<tr>
	            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
	          							<td class="main"><?=ENTRY_PAYMENT_ID?></td>
	          							<td class="main"><?=tep_draw_input_field('payment_id', $_SESSION[$session_name]["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
			    						<td class="main">&nbsp;</td>
									<td class="main"><?=ENTRY_TRANSACTION_ID?></td>
			    						<td class="main">&nbsp;</td>
			    						<td class="main"><?=tep_draw_input_field('transaction_id', $_SESSION[$session_name]["transaction_id"], ' id="transaction_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
									</tr>
									<tr>
	            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
                                                                <tr>
                                                                    <td class="main" valign="middle"><?=ENTRY_ORDER_ID?></td>
                                                                    <td>
                                                                        <table>
                                                                            <tr>
                                                                                <td class="main">
                                                                                    <?=tep_draw_pull_down_menu("order_type", $order_type, tep_not_null($_SESSION[$session_name]["order_type"]) ? $_SESSION[$session_name]["order_type"] : '', 'id="order_type"')?>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="main">
                                                                                    <?=tep_draw_input_field('order_id', $_SESSION[$session_name]["order_id"], ' id="order_id" size="15" onKeyUP="resetControls(this);" onBlur="resetControls(this);" onKeyPress="return noEnterKey(event)"')?>
                                                                                </td>
                                                                             </tr>
                                                                        </table>
                                                                    </td>
	          						</tr>
                                                                <tr>
                                                                    <td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                                                                </tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
						    			<td class="main">&nbsp;</td>
			    						<td colspan="6">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script language="javascript"><!--
				function form_checking(form_obj, action) {
				    //form_obj.submit();
					return true;
	    		}
	    		
				function resetControls(controlObj) {
					if (trim_str(controlObj.value) != '') {
						if (controlObj.id == 'payment_id') {
							document.payments_criteria.order_id.value = '';
							document.payments_criteria.transaction_id.value = '';
						} else if (controlObj.id == 'order_id') {
							document.payments_criteria.payment_id.value = '';
							document.payments_criteria.transaction_id.value = '';
						} else if (controlObj.id == 'transaction_id') {
							document.payments_criteria.payment_id.value = '';
							document.payments_criteria.order_id.value = '';
						}
						
						document.payments_criteria.user_email.value = '';
						document.payments_criteria.start_date.value = '';
						document.payments_criteria.end_date.value = '';
						document.payments_criteria.show_records.selectedIndex = 0;
		    		} else {
		    			controlObj.value = '';
		    		}
				}
	    	//-->
			</script>
		<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $acc_statement_html;
	}
	
	function show_acc_statement($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		if (!$_REQUEST['cont']) {
			$_SESSION[$session_name]["user_role"] = $input_array["user_role"];
			$_SESSION[$session_name]["user_email"] = $input_array["user_email"];
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["payment_id"] = $input_array["payment_id"];
			$_SESSION[$session_name]["transaction_id"] = $input_array["transaction_id"];
			$_SESSION[$session_name]["order_type"] = $input_array["order_type"];
			$_SESSION[$session_name]["order_id"] = $input_array["order_id"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
	  	}
        
	  	$payment_id_str = (isset($_SESSION[$session_name]["payment_id"]) && tep_not_null($_SESSION[$session_name]["payment_id"])) ? " sah.store_account_history_trans_id='" . $_SESSION[$session_name]["payment_id"] . "' AND sah.store_account_history_trans_type='P'" : "1";
		$order_id_str = (isset($_SESSION[$session_name]["order_id"]) && tep_not_null($_SESSION[$session_name]["order_id"])) ? " sah.store_account_history_trans_id = '".$_SESSION[$session_name]["order_id"]."' AND sah.store_account_history_trans_type = '".$_SESSION[$session_name]["order_type"]."'" : "1";
		$transaction_id_str = (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) ? " sah.store_account_history_id = '" . $_SESSION[$session_name]["transaction_id"] . "' AND (LOWER(store_account_history_activity_title) = 'manual deduction' OR LOWER(store_account_history_activity_title) = 'manual addition') " : "1";
	  	
	  	// Get user id
	  	if (tep_not_null($_SESSION[$session_name]["payment_id"]) || tep_not_null($_SESSION[$session_name]["order_id"]) || tep_not_null($_SESSION[$session_name]["transaction_id"])) {
	  		$user_info_select_sql = "	SELECT sah.user_id, sah.user_role 
	  									FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sah 
	  									WHERE " . $order_id_str . " 
											AND " . $payment_id_str . " 
											AND " . $transaction_id_str . " 
										LIMIT 1";
			$user_info_result_sql = tep_db_query($user_info_select_sql);
			$user_info_row = tep_db_fetch_array($user_info_result_sql);
			
			$_SESSION[$session_name]["user_id"] = $user_info_row['user_id'];
			$_SESSION[$session_name]["user_role"] = $user_info_row['user_role'];
	  	} else {
	  		if ($_SESSION[$session_name]["user_role"] == 'customers') {
	  			$user_info_select_sql = "	SELECT customers_id 
	  										FROM " . TABLE_CUSTOMERS . " 
		  									WHERE customers_email_address = '".tep_db_input($_SESSION[$session_name]["user_email"])."'";
				$user_info_result_sql = tep_db_query($user_info_select_sql);
				$user_info_row = tep_db_fetch_array($user_info_result_sql);
				
				$_SESSION[$session_name]["user_id"] = $user_info_row['customers_id'];
	  		} else if ($_SESSION[$session_name]["user_role"] == 'supplier') {
	  			$user_info_select_sql = "	SELECT supplier_id 
	  										FROM " . TABLE_SUPPLIER . " 
		  									WHERE supplier_email_address = '".tep_db_input($_SESSION[$session_name]["user_email"])."'";
				$user_info_result_sql = tep_db_query($user_info_select_sql);
				$user_info_row = tep_db_fetch_array($user_info_result_sql);
				
				$_SESSION[$session_name]["user_id"] = $user_info_row['supplier_id'];
	  		}
	  	}
	  	
	  	if (tep_not_null($_SESSION[$session_name]["start_date"])) {
			if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
				$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				$start_date_str = " ( sah.store_account_history_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
				$start_date_str = " ( DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		} else {
			$start_date_str = " 1 ";
		}
		
		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				$end_date_str = " ( sah.store_account_history_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
				$end_date_str = " ( DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		} else {
			$end_date_str = " 1 ";
		}
		
	  	$statement_user_str = " sah.user_id='" . tep_db_input($_SESSION[$session_name]["user_id"]) . "' and sah.user_role='".tep_db_input($_SESSION[$session_name]["user_role"])."' ";
	    
	  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
	  	
	  	// Need store_account_history_id in ORDER BY to maintain the ordering for those records on the same date and time
	  	$acc_statement_select_sql = "	SELECT sah.store_account_history_id, DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d %H:%i') AS activity_date, sah.store_account_history_trans_type AS trans_type, sah.store_account_history_trans_id AS trans_id, sah.store_account_history_activity_desc_show, 
			  								sah.store_account_history_activity_title AS activity_title, sah.store_account_history_activity_desc AS activity_desc, sah.store_account_transaction_reserved AS trans_reserved,
			  								sah.store_account_history_currency AS currency, sah.store_account_history_debit_amount AS debit_amount, sah.store_account_history_credit_amount AS credit_amount, sah.store_account_history_after_balance AS after_balance, sah.store_account_history_added_by AS added_by
										FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sah 
										WHERE " . $order_id_str . " 
											AND " . $payment_id_str . " 
											AND " . $transaction_id_str . " 
											AND " . $start_date_str . " 
											AND " . $end_date_str . "
											AND " . $statement_user_str . "
										ORDER BY sah.store_account_history_date desc, store_account_history_id DESC";
		
		$show_records = $_SESSION[$session_name]["show_records"];
		
		if ($show_records != "ALL") {
			$acc_statement_split_object = new splitPageResults($_REQUEST['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $acc_statement_select_sql, $acc_statement_select_sql_numrows, true);
		}
		$acc_statement_result_sql = tep_db_query($acc_statement_select_sql);
		
		//if (!tep_not_null($_SESSION[$session_name]["user_email"])) {
			$user_info_array = $this->_get_user_particulars($_SESSION[$session_name]["user_id"], $_SESSION[$session_name]["user_role"]);
		/*} else {
			$user_info_array = array('email' => $_SESSION[$session_name]["user_email"]);
		}*/
		
		$fixed_reserve_amount = $this->_get_general_reserve_amount($_SESSION[$session_name]["user_id"], $_SESSION[$session_name]["user_role"]);
		$dynamic_reserved_amount_array = $this->_get_dynamic_reserve_amount($_SESSION[$session_name]["user_id"], $_SESSION[$session_name]["user_role"]);
		
		ob_start();
		?>
			<table width="100%" border="0" cellspacing="2" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0">
							<td valign="bottom">
								<?=tep_not_null($user_info_array['email']) ? sprintf(TABLE_SECTION_HEADING_ACC_STAT, $user_info_array['fname'].' '.$user_info_array['lname'], $user_info_array['email'], $this->user_roles[$_SESSION[$session_name]["user_role"]]) : ''?>
							</td>
							<td align="right">
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td class="main"><b><?=TEXT_ACC_STAT_DISABLE_WITHDRAWAL?>:</b></td>
										<td align="right" class="main"><?=($user_info_array['disable_withdrawal'] == '1' ? '<span class="redIndicator">ON</span>' : '<span class="greenIndicator">OFF</span>' )?></td>
									</tr>
									<tr>
										<td class="main"><b><?=TEXT_ACC_STAT_FIXED_RESERVED?>:</b></td>
										<td align="right" class="main"><?=$currencies->format($fixed_reserve_amount)?></td>
									</tr>
									<tr>
										<td valign="top" class="main"><b><?=TEXT_ACC_STAT_DYNAMIC_RESERVED?>:</b></td>
										<td align="right" class="main">
										<?
											if (is_array($dynamic_reserved_amount_array) && count($dynamic_reserved_amount_array)) {
												foreach ($dynamic_reserved_amount_array as $cur_code => $reserve_amt) {
													echo $currencies->format($reserve_amt, false, $cur_code).'<br>';
												}
											}
										?>
										</td>
									</tr>
								</table>
							</td>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
							<tr>
								<td rowspan="2" width="12%" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_DATETIME?></td>
								<td rowspan="2" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_ACTIVITY?></td>
								<td width="6%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_CUSTOMER_ORDER?></td>
								<td width="6%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_CUSTOMER_ORDER_STATUS?></td>
								<td width="6%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_CUSTOMER_AGING?></td>
								<td rowspan="2" width="15%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_RESERVE?></td>
								<td rowspan="2" width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_PAYMENT_STATUS?></td>
							    <td rowspan="2" width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_DEBIT?></td>
							    <td rowspan="2" width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_CREDIT?></td>
							    <td rowspan="2" width="10%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_BALANCE?></td>
							</tr>
							<tr>
								<td colspan="3" align="center" class="reportBoxHeading"><?=sprintf(LINK_ACC_AGING_STATISTIC, "getAgingStats();")?></td>
							</tr>
		<?
		$row_count = 0;
		while ($acc_statement_row = tep_db_fetch_array($acc_statement_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;

	  		$acc_history_id_str = $activity_title = '';

	  		$store_account_history_id = $acc_statement_row['store_account_history_id']; 

            $add_comment_flag = false;
            $is_buyback_order = false;
            
            switch($acc_statement_row['trans_type']) {
  				case 'P':
  					$activity_title = '<a href="'.tep_href_link(FILENAME_PAYMENT, 'payID='.$acc_statement_row['trans_id'].'&action=edit', 'NONSSL').'" target="payment">'.sprintf(TITLE_TRANS_PAYMENT, $acc_statement_row['trans_id']).'</a>';
  					break;
  				case 'S':
  					$verify_status_select_sql = "	SELECT supplier_orders_verify_mode 
  													FROM " . TABLE_SUPPLIER_ORDER_LISTS . " 
  													WHERE supplier_order_lists_id = '".tep_db_input($acc_statement_row['trans_id'])."'";
  					$verify_status_result_sql = tep_db_query($verify_status_select_sql);
		  			$verify_status_row = tep_db_fetch_array($verify_status_result_sql);
		  			
  					$activity_title = '<a href="'.tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$acc_statement_row['trans_id'].'&action=edit', 'NONSSL').'" target="s_order">'.sprintf(TITLE_TRANS_SUPPLIER_ORDER, $acc_statement_row['trans_id']) . '</a>&nbsp;' . ($verify_status_row['supplier_orders_verify_mode'] ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', TEXT_ACC_STAT_TRANS_VERIFIED, 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', TEXT_ACC_STAT_TRANS_NOT_VERIFIED, 10, 10));
  					break;
  				case 'PWL':
  					list($oID, $pSeq) = explode('-', $acc_statement_row['trans_id']);
  					
  					$pwl_orders_select_sql = "	SELECT op.orders_products_id, sta.supplier_tasks_verify_mode 
												FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (op.orders_products_id=sta.orders_products_id) 
												WHERE op.orders_id = '".tep_db_input($oID)."' 
													AND op.custom_products_type_id = 1 
													AND ocp.orders_custom_products_number = '".tep_db_input($pSeq)."'";
					$pwl_orders_result_sql = tep_db_query($pwl_orders_select_sql);
					$pwl_orders_row = tep_db_fetch_array($pwl_orders_result_sql);
					
  					$activity_title = '<a href="'.tep_href_link(FILENAME_PROGRESS_REPORT, 'orders_product_id='.$pwl_orders_row['orders_products_id'].'&action=report').'" target="pwl_order">'.sprintf(TITLE_TRANS_PWL_ORDER, $acc_statement_row['trans_id']) . '</a>&nbsp;' . ($pwl_orders_row['supplier_tasks_verify_mode'] ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', TEXT_ACC_STAT_TRANS_VERIFIED, 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', TEXT_ACC_STAT_TRANS_NOT_VERIFIED, 10, 10));
  					break;
  				case 'B':
  					$verify_status_select_sql = "	SELECT buyback_request_group_verify_mode 
  													FROM " . TABLE_BUYBACK_REQUEST_GROUP . " 
  													WHERE buyback_request_group_id = '".tep_db_input($acc_statement_row['trans_id'])."'";
  					$verify_status_result_sql = tep_db_query($verify_status_select_sql);
		  			$verify_status_row = tep_db_fetch_array($verify_status_result_sql);
		  			
  					$activity_title = '<a href="'.tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$acc_statement_row['trans_id'], 'NONSSL').'" target="b_order">'.sprintf(TITLE_TRANS_BUYBACK_ORDER, $acc_statement_row['trans_id']) . '</a>&nbsp;' . ($verify_status_row['buyback_request_group_verify_mode'] ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', TEXT_ACC_STAT_TRANS_VERIFIED, 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', TEXT_ACC_STAT_TRANS_NOT_VERIFIED, 10, 10));
  					$is_buyback_order = true;
  					break;
  				case 'A':
  					$activity_title = TITLE_TRANS_ADJUSTMENT;
  					break;
				case 'PO':
                                        $is_dtu_po = dtu_payment::is_dtu_payment($acc_statement_row['trans_id']);
                                        $is_api_po = api_replenish_payment::is_po_payment($acc_statement_row['trans_id']);
                    $is_cdk_po = consignment_payment::is_cdk_payment($acc_statement_row['trans_id']);
                                        if($is_dtu_po > 0) {
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 'user_role', 'user_email', 'start_date')) . '&subaction=edit_dtu&po_id=' . $acc_statement_row['trans_id']) . '" target="p_order">' . sprintf(TITLE_TRANS_DTU_PAYMENT_REQUEST, dtu_payment::get_po_ref_num_by_id($acc_statement_row['trans_id'])) . '</a>';
                                        } else if($is_api_po > 0) {
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 'user_role', 'user_email', 'start_date')) . '&subaction=edit_po&po_id=' . $acc_statement_row['trans_id']) . '" target="p_order">' . sprintf(TITLE_TRANS_API_PAYMENT_REQUEST, api_replenish_payment::get_po_ref_num_by_id($acc_statement_row['trans_id'])) . '</a>';
                    } else if($is_cdk_po > 0) {
                        $activity_title = '<a href="' . tep_href_link(FILENAME_CDK_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 'user_role', 'user_email', 'start_date')) . '&subaction=edit_cdk&po_id=' . $acc_statement_row['trans_id']) . '" target="p_order">' . sprintf(TITLE_TRANS_CDK_PAYMENT_REQUEST, consignment_payment::get_po_ref_num_by_id($acc_statement_row['trans_id'])) . '</a>';
                                        } else {
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('action', 'subaction', 'user_role', 'user_email', 'start_date')) . '&subaction=edit_po&po_id=' . $acc_statement_row['trans_id']) . '" target="p_order">' . sprintf(TITLE_TRANS_PURCHASE_ORDER, edit_purchase_orders::get_po_ref_num_by_id($acc_statement_row['trans_id'])) . '</a>';
                                        }
  					break;
				case 'SO':
					if(is_numeric($acc_statement_row['trans_id']) === true){
						$activity_title = 'G2G Sell Order (#<a href="' . tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'selected_box=c2c&action=add_form&id=' . $acc_statement_row['trans_id'], 'NONSSL') . '" target="_blank">' . $acc_statement_row['trans_id'] . '</a>)';
					} else {
						$activity_title = 'G2G Sell Order (#<a href="' .CREW2_PATH_G2G . '/orders/sold-order/details/'.$acc_statement_row['trans_id'].'" target="_blank">' . $acc_statement_row['trans_id'] . '</a>)';
					}
  					$is_buyback_order = true;
  					break;
  			case 'AF':
           	$activity_title = 'G2G Affiliate: Order (#<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $acc_statement_row['trans_id'] . "&action=edit", 'NONSSL') . '" target="_blank">' . $acc_statement_row['trans_id'] . '</a>)';
   					break;
  				default:
  					$activity_title = $acc_statement_row['trans_type'] . ' ' . $acc_statement_row['trans_id'];
  					break;
  			}
  			
  			$comments_select_sql = "SELECT store_account_comments_date_added, store_account_comments_added_by, store_account_comments, store_account_comments_notified
		                            FROM ". TABLE_STORE_ACCOUNT_COMMENTS ." 
		                            WHERE store_account_history_id='". $store_account_history_id  ."'
		                            ORDER BY store_account_comments_id";
            $comments_result_sql = tep_db_query($comments_select_sql);
            
	  		if (tep_not_null($acc_statement_row['activity_title'])) {
	  			if (strtolower(trim($acc_statement_row['activity_title'])) == 'manual deduction' ||
	  				strtolower(trim($acc_statement_row['activity_title'])) == 'manual addition' ||
	  				strtolower(trim($acc_statement_row['activity_title'])) == 'compensate'
	  				) {
	  				$acc_history_id_str = sprintf(TEXT_ACC_STAT_TRANS_ID, $acc_statement_row['store_account_history_id']);
	  			}
	  			
	  			$activity_title .= ' ' . $acc_statement_row['activity_title'] . ' (by ' . $acc_statement_row['added_by'] . ')' . (($acc_statement_row['store_account_history_activity_desc_show']) ? tep_image(DIR_WS_ICONS . 'tick.gif', TEXT_ACC_STAT_MANUAL_ACTIVITY_SHOW) : tep_image(DIR_WS_ICONS . 'cross.gif', TEXT_ACC_STAT_MANUAL_ACTIVITY_HIDE)) . (tep_not_null($acc_history_id_str) ? '<br>'.$acc_history_id_str : '') . (tep_not_null($acc_statement_row['activity_desc']) ? '<br><div class="paymentRemarkSelectedRow">Comment:<br>' . nl2br($acc_statement_row['activity_desc']) . '</div>' : '');
				
	  			if ($_SESSION[$session_name]["user_role"] == 'supplier' || $user_info_array['sign_up_from'] == 'CN') {
	  			    $add_comment_flag = true;
    	  		}
	  		}
	  		
	  		if ($acc_statement_row['trans_type'] == 'P') {
	  			$payment_info_select_sql = "SELECT sp.store_payments_status, sps.store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS . " AS sp 
	  										INNER JOIN " . TABLE_STORE_PAYMENTS_STATUS . " sps 
	  											ON (sp.store_payments_status=sps.store_payments_status_id AND sps.language_id = '".$languages_id."') 
	  										WHERE sp.store_payments_id='".tep_db_input($acc_statement_row['trans_id'])."'";
	  			$payment_info_result_sql = tep_db_query($payment_info_select_sql);
	  			$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
	  			
	  			if ($payment_info_row['store_payments_status'] == '3' || $payment_info_row['store_payments_status'] == '4') {
	  				$status_update_date_select_sql = "	SELECT date_added 
	  													FROM " . TABLE_STORE_PAYMENTS_HISTORY . " 
	  													WHERE store_payments_id = '".tep_db_input($acc_statement_row['trans_id'])."' 
	  														AND store_payments_status = '".$payment_info_row['store_payments_status']."' 
	  													ORDER BY date_added DESC 
	  													LIMIT 1";
	  				$status_update_date_result_sql = tep_db_query($status_update_date_select_sql);
		  			$status_update_date_row = tep_db_fetch_array($status_update_date_result_sql);
		  			
	  				$payment_status = sprintf(TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE, $payment_info_row['store_payments_status_name'], $status_update_date_row['date_added']);
	  			} else {
	  				$payment_status = $payment_info_row['store_payments_status_name'];
	  			}
	  			
	  			$reserve_link = TEXT_ACC_STAT_NOT_APPLICABLE;
	  		} else {
	  			$payment_status = TEXT_ACC_STAT_NOT_APPLICABLE;
	  			
	  			if (tep_not_null($acc_statement_row['trans_type']) && $acc_statement_row['credit_amount'] > 0) {
	  				if ($acc_statement_row['trans_reserved'] == '1') {	// Currently is reserved
	  					$reserve_link = sprintf(LINK_ACC_STAT_LIFT_RESERVE, "reserveTrans('".$acc_statement_row['trans_id']."', '".$acc_statement_row['trans_type']."', '".$_SESSION[$session_name]["user_id"]."', '".$_SESSION[$session_name]["user_role"]."', '0', 'reserve_div_".$row_count."');");
	  				} else {	// Currently is not reserved
	  					$reserve_link = sprintf(LINK_ACC_STAT_RESERVE, "reserveTrans('".$acc_statement_row['trans_id']."', '".$acc_statement_row['trans_type']."', '".$_SESSION[$session_name]["user_id"]."', '".$_SESSION[$session_name]["user_role"]."', '1', 'reserve_div_".$row_count."');");
	  				}
	  			} else {
	  				$reserve_link = TEXT_ACC_STAT_NOT_APPLICABLE;
	  			}
	  		}

	  		echo '			<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td valign="top" class="reportRecords" nowrap>'.$acc_statement_row['activity_date'].'</td>
							   	<td valign="top">
							   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
							   			<tr>
            								<td class="reportRecords">'.$activity_title.'</td>
            							</tr>';
			
            while ($comments_row = tep_db_fetch_array($comments_result_sql)) {
			    echo '					<tr>
					    					<td class="reportRecords"><hr>'.$comments_row['store_account_comments_date_added'] . ' (by '. $comments_row['store_account_comments_added_by'] .') '. (($comments_row['store_account_comments_notified']) ? tep_image(DIR_WS_ICONS . 'tick.gif', TEXT_ACC_STAT_NOTIFIED) : tep_image(DIR_WS_ICONS . 'cross.gif', TEXT_ACC_STAT_NOT_NOTIFY)) .'<br><div class="paymentRemarkSelectedRow">Comment:<br>'. nl2br($comments_row['store_account_comments']) . '</div></td>
					    				</tr>';
			}
            
            if ($add_comment_flag) {
		?>
		    							<tr>
		    								<td class="formAreaTitle">
		    								    <div id="link_<?=$store_account_history_id?>" align="top">
		                        					<a href="javascript:;" onClick="showHideAddComment('box_<?=$store_account_history_id?>', 'link_<?=$store_account_history_id?>', 'show');"><?=TEXT_ACC_STAT_ADD_COMMENT_SHOW?></a>
		                        				</div>
		    								</td>
		    	        				</tr>
		    	        				<tbody id="box_<?=$store_account_history_id?>" class="hide">
		    	        				<tr>
		    								<td>
		    									<?=tep_draw_form('add_comment_form_' . $store_account_history_id, $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=add_comment&cont=1', 'post', '')?>
		    									<?=tep_draw_hidden_field("store_account_history_id", $store_account_history_id) . tep_draw_hidden_field("user_id", $_SESSION[$session_name]["user_id"]) . tep_draw_hidden_field("user_role", $_SESSION[$session_name]["user_role"]) . tep_draw_hidden_field("user_id", $_SESSION[$session_name]["user_id"]) . tep_draw_hidden_field("activity_title", $acc_statement_row['activity_title'])?>
		    	        						<table border="0" cellspacing="0" cellpadding="2">
		    										<tr>
		    		          							<td class="main" valign="top"><?=ENTRY_ACC_STAT_MANUAL_ADJUST_COMMENT?></td>
		    		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '30', '5', '')?></td>
		    										</tr>
		    										<tr>
		    		          							<td class="main" valign="top">&nbsp;</td>
		    		          							<td class="main"><?=ENTRY_ACC_STAT_SUPPLIER_NOTIFY . tep_draw_checkbox_field('notify', '1')?></td>
		    										</tr>
		    										<tr>
		                        	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		                        	      			</tr>
		    										<tr>
		    		          							<td class="main" valign="top">&nbsp;</td>
		    		          							<td class="main"><?=tep_submit_button(BUTTON_ADD_COMMENT, ALT_BUTTON_ADD_COMMENT, '', 'inputButton')?></td>
		    										</tr>
		    	        						</table>
		    	        						</form>
		    								</td>
		    	        				</tr>
		    	        				</tbody>
		<?          }

            echo '		   	    	</table>
            					</td>
            					<td align="right" valign="top" class="reportRecords">'.($is_buyback_order ? '<div id="cor_store_'.$acc_statement_row['trans_id'].'"></div><div class="bo_id" id="'.$acc_statement_row['trans_id'].'"></div>' : '').'</td>
            					<td align="center" valign="top" class="reportRecords">'.($is_buyback_order ? '<div id="cor_status_'.$acc_statement_row['trans_id'].'"></div>' : '').'</td>
            					<td align="center" valign="top" class="reportRecords">'.($is_buyback_order ? '<div id="cust_aging_'.$acc_statement_row['trans_id'].'"></div>' : '').'</td>
							   	<td align="center" valign="top" class="reportRecords">'.($acc_statement_row['credit_amount'] > 0 ? '<div id="reserve_div_'.$row_count.'">'.$reserve_link.'</div>' : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="center" valign="top" class="reportRecords">'.$payment_status.'</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($acc_statement_row['debit_amount']) ? $currencies->format($acc_statement_row['debit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($acc_statement_row['credit_amount']) ? $currencies->format($acc_statement_row['credit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords"><span '.($acc_statement_row['after_balance'] < 0 ? 'class="redIndicatorBold"' : '').'>'.$currencies->format($acc_statement_row['after_balance'], false, $acc_statement_row['currency']).'</a></td>
							  </tr>';

	  		$row_count++;
	  	}
		?>
						</table>
					</td>
				</tr>
				<tr>
        			<td>
        				<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($acc_statement_result_sql) > 0 ? "1" : "0", tep_db_num_rows($acc_statement_result_sql), tep_db_num_rows($acc_statement_result_sql)) : $acc_statement_split_object->display_count($acc_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'], $result_display_text)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $acc_statement_split_object->display_links($acc_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
			</table>
    			<script language="javascript"><!--
    				function showHideAddComment(comment_div, comment_link_div, classtype) {
                		DOMCall(comment_div).className = classtype;
                		
                		if (classtype == 'show') {
                			DOMCall(comment_link_div).innerHTML = '<a href="javascript:;" onclick="showHideAddComment(\''+comment_div+'\', \''+comment_link_div+'\', \'hide\');">'+'<?=TEXT_ACC_STAT_ADD_COMMENT_HIDE?>'+'</a>';
                		} else {
                			DOMCall(comment_link_div).innerHTML = '<a href="javascript:;" onclick="showHideAddComment(\''+comment_div+'\', \''+comment_link_div+'\', \'show\');">'+'<?=TEXT_ACC_STAT_ADD_COMMENT_SHOW?>'+'</a>';
                		}
                	}
    	    	//-->
    			</script>
		<?
		$report_section_html = ob_get_contents();
		ob_end_clean() ;
		
		// This should be here to let all the session get updated value
		$search_section_html = $this->search_acc_statement($filename, $session_name);
		
		$manual_adjust_section_html = $this->_manual_adjust_form($filename, $_SESSION[$session_name]["user_id"], $_SESSION[$session_name]["user_role"]);
		
		return $search_section_html . "\n" . $report_section_html . "\n" . $manual_adjust_section_html;
	}
	
	function _manual_adjust_form($filename, $user_id, $user_role) {
		$report_section_html = '';
		
		// Permission for making the manual adjustment
		$manual_deduct_permission = tep_admin_files_actions(FILENAME_ACCOUNT_STATEMENT, 'STORE_ACC_MANUAL_DEDUCT');
		$manual_add_permission = tep_admin_files_actions(FILENAME_ACCOUNT_STATEMENT, 'STORE_ACC_MANUAL_ADD');
		
		if ($manual_deduct_permission || $manual_add_permission) {
			$user_store_account_array = $this->_get_user_store_account_balance_lists($user_id, $user_role, true);
			
			$wsc_account_options = array(	array ('id' => 'wsc', 'text' => 'Normal WSC'),
											array ('id' => 'po_wsc', 'text' => 'PO WSC')
										);
			
			ob_start();
		?>
			<br>
			<table width="100%" border="0" align="center" cellpadding="2" cellspacing="1" valign="middle">
				<tr>
					<td width="47%" valign="top" class="<?=$manual_deduct_permission ? 'formArea' : ''?>">
		<?			if ($manual_deduct_permission) { ?>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="formAreaTitle"><?=TABLE_HEADING_ACC_STAT_MANUAL_DEDUCT?></td>
	        				</tr>
	        				<tr>
								<td>
									<?=tep_draw_form('manual_deduct_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_deduct&cont=1', 'post', '')?>
									<?=tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role)?>
	        						<table border="0" cellspacing="0" cellpadding="2">
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_ACC_STAT_CURRENCY_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("cur_acc", $user_store_account_array, '', '')?></td>
										</tr>
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_ACC_STAT_WSC_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("wsc_acc", $wsc_account_options, '', '')?></td>
										</tr>
										<tr>
		          							<td class="main"><?=ENTRY_ACC_STAT_DEDUCT_AMOUNT?></td>
		          							<td class="main">-&nbsp;<?=tep_draw_input_field('deduct_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_ACC_STAT_MANUAL_ADJUST_COMMENT?></td>
		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '50', '5', '')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_ACC_STAT_COMMENT_SHOW_SUPPLIER?></td>
		          							<td class="main"><?=tep_draw_checkbox_field('show_comments', '1')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top">&nbsp;</td>
		          							<td class="main"><?=tep_submit_button(BUTTON_DEDUCT_BALANCE, ALT_BUTTON_DEDUCT_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton')?></td>
										</tr>
	        						</table>
	        						</form>
								</td>
	        				</tr>
	        			</table>
		<?			} ?>
	        		</td>
	        		<td>&nbsp;</td>	
	        		<td width="47%" valign="top" class="<?=$manual_add_permission ? 'formArea' : ''?>">
		<?			if ($manual_add_permission) { ?>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="formAreaTitle"><?=TABLE_HEADING_ACC_STAT_MANUAL_ADD?></td>
	        				</tr>
	        				<tr>
								<td>
									<?=tep_draw_form('manual_add_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_add&cont=1', 'post', '')?>
									<?=tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role)?>
	        						<table border="0" cellspacing="0" cellpadding="2">
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_ACC_STAT_CURRENCY_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("cur_acc", $user_store_account_array, '', '')?></td>
										</tr>
	        							<tr>
		          							<td width="30%" class="main"><?=ENTRY_ACC_STAT_WSC_ACCOUNT?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("wsc_acc", $wsc_account_options, '', '')?></td>
										</tr>
										<tr>
		          							<td class="main"><?=ENTRY_ACC_STAT_ADD_AMOUNT?></td>
		          							<td class="main">+&nbsp;<?=tep_draw_input_field('add_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_ACC_STAT_MANUAL_ADJUST_COMMENT?></td>
		          							<td class="main"><?=tep_draw_textarea_field('comments', 'soft', '50', '5', '')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top"><?=ENTRY_ACC_STAT_COMMENT_SHOW_SUPPLIER?></td>
		          							<td class="main"><?=tep_draw_checkbox_field('show_comments', '1')?></td>
										</tr>
										<tr>
		          							<td class="main" valign="top">&nbsp;</td>
		          							<td class="main"><?=tep_submit_button(BUTTON_ADD_BALANCE, ALT_BUTTON_ADD_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton')?></td>
										</tr>
	        						</table>
	        						</form>
								</td>
	        				</tr>
	        			</table>
		<?			} ?>
	        		</td>
	        	</tr>
	        </table>
		<?
			$report_section_html = ob_get_contents();
			ob_end_clean();
		}
		return $report_section_html;
	}
	
	function manual_deduct_amount($input_array, &$messageStack) {
	    global $currencies;
		$error = false;
		
		$manual_deduct_permission = tep_admin_files_actions(FILENAME_ACCOUNT_STATEMENT, 'STORE_ACC_MANUAL_DEDUCT');
		
		if ($manual_deduct_permission) {
			$store_acc_balance_select_sql = "	SELECT store_account_balance_amount 
												FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
												WHERE user_id = '" . tep_db_input($input_array['user_id']) . "' 
													AND user_role = '" . tep_db_input($input_array['user_role']) . "' 
													AND store_account_balance_currency = '" . tep_db_input($input_array['cur_acc']) . "'";
			$store_acc_balance_result_sql = tep_db_query($store_acc_balance_select_sql);
			
			if ($store_acc_balance_row = tep_db_fetch_array($store_acc_balance_result_sql)) {
				$deduct_amount = trim($input_array['deduct_amount']);
				if (is_numeric($deduct_amount) && $deduct_amount > 0) {
					// Update live credit balance
					if ($input_array['wsc_acc'] == 'wsc') {
						$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
														'operator'=> '-', 
														'value'=> $deduct_amount)
											);
						
						$new_store_acc_balance = $this->_set_store_acc_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
					} else if ($input_array['wsc_acc'] == 'po_wsc') {
						$po_suppliers_obj = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
						
						// Update live credit balance
						$update_powsc_info = array(array(	'field_name' => 'store_account_po_wsc',
															'operator' => '-',
															'value' => $deduct_amount)
						);
						$new_store_acc_balance = $po_suppliers_obj->_set_po_supplier_po_wsc_balance($input_array['user_id'], 'customers', $input_array['cur_acc'], $update_powsc_info);
					}
					
					// Insert account statement history
					$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($input_array['user_id']),
																'user_role' => tep_db_prepare_input($input_array['user_role']),
										                        'store_account_history_date' => 'now()',
										                        'store_account_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
										                        'store_account_history_debit_amount' => (double)$deduct_amount,
										                        'store_account_history_credit_amount' => 'NULL',
										                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
										                        'store_account_history_activity_title' => LOG_ACC_STAT_MANUAL_DEDUCTION,
										                        'store_account_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
										                        'store_account_history_activity_desc_show' => (int)$input_array['show_comments'],
										                        'store_account_history_added_by' => $this->identity_email,
										                        'store_account_history_added_by_role' => 'admin'
										                       );
					if ($input_array['wsc_acc'] == 'po_wsc') {
						$account_balance_history_data_array['store_account_history_account_type'] = 'POWSC';
					}
					tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
					
					$transaction_id = tep_db_insert_id();
					$transact_date = '';
                    if (tep_not_null($transaction_id)) {
                        $acc_stat_coments_details_select_sql = "SELECT store_account_history_date
                                                        FROM ". TABLE_STORE_ACCOUNT_HISTORY ."
                                                        WHERE store_account_history_id='". $transaction_id ."'";
                        $acc_stat_coments_details_result_sql = tep_db_query($acc_stat_coments_details_select_sql);

                        $acc_stat_coments_details_row = tep_db_fetch_array($acc_stat_coments_details_result_sql);
                        $transact_date = $acc_stat_coments_details_row['store_account_history_date'];
                    }
					
                    if ($input_array['user_role'] == 'supplier' || $user_info_array['sign_up_from'] == 'CN') {
                        // send email to supplier
                        $email_supplier = sprintf(EMAIL_ACC_STAT_TEXT_TITLE, $transaction_id) . "\n" .
                                          EMAIL_SEPARATOR . "\n\n" .
                                          sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_NUMBER, $transaction_id) . "\n" . 
                                          sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_DATE, date("Y-m-d H:i:s")) . "\n" .
                                          sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_DEDUCTION, $currencies->format($deduct_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n\n" .
                                          (($input_array['show_comments']) ?  sprintf(EMAIL_ACC_STAT_TEXT_COMMENTS, $input_array['comments']) . "\n" : '') . 
                                          EMAIL_TEXT_CLOSING . "\n\n" .
                    				      EMAIL_FOOTER;

                        $user_info_array = $this->_get_user_particulars($input_array['user_id'], $input_array['user_role']);
                        $email_greeting = tep_get_email_greeting($user_info_array['fname'], $user_info_array['lname'], $user_info_array['gender']);
                		$email_supplier = $email_greeting . $email_supplier;
				        @tep_mail($user_info_array['fname'] .' '. $user_info_array['lname'], $user_info_array['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_DEDUCTION, $transaction_id))), $email_supplier, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    				}
					
					// send to admin 
					$email = sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_ID, $transaction_id) . "\n" .
					         sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_DATE, $transact_date) . "\n" .
					         sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_DEDUCTION, $currencies->format($deduct_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n" .
					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_USER, $this->identity_email) . "\n" .
					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_IP, getenv("REMOTE_ADDR")) . "\n\n" .
					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_COMMENT, tep_db_prepare_input($input_array['comments']));
					
					$email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
        			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
        				@tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_DEDUCTION, $transaction_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        			}
            		$messageStack->add_session(SUCCESS_ACC_STAT_MANUAL_DEDUCTION, 'success');
            		
				} else {
					$error = true;
					$messageStack->add_session(ERROR_ACC_STAT_INVALID_DEDUCTION_AMOUNT, 'error');
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_ACC_STAT_CURRENCY_ACC_NOT_EXISTS, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function manual_add_amount($input_array, &$messageStack) {
	    global $currencies;
		$error = false;
		
		$manual_add_permission = tep_admin_files_actions(FILENAME_ACCOUNT_STATEMENT, 'STORE_ACC_MANUAL_ADD');
		
		if ($manual_add_permission) {
			$store_acc_balance_select_sql = "	SELECT store_account_balance_amount 
												FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
												WHERE user_id = '" . tep_db_input($input_array['user_id']) . "' 
													AND user_role = '" . tep_db_input($input_array['user_role']) . "' 
													AND store_account_balance_currency = '" . tep_db_input($input_array['cur_acc']) . "'";
			$store_acc_balance_result_sql = tep_db_query($store_acc_balance_select_sql);
			
			if ($store_acc_balance_row = tep_db_fetch_array($store_acc_balance_result_sql)) {
				$add_amount = trim($input_array['add_amount']);
				if (is_numeric($add_amount) && $add_amount > 0) {
					if ($this->_is_within_daily_limit($add_amount / $currencies->get_value($input_array['cur_acc'], ''))) {	// Use SPOT value
						// Update live credit balance
						if ($input_array['wsc_acc'] == 'wsc') {
							$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
															'operator'=> '+', 
															'value'=> $add_amount)
												);
							
							$new_store_acc_balance = $this->_set_store_acc_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
						} else if ($input_array['wsc_acc'] == 'po_wsc') {
							$po_suppliers_obj = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
							
							// Update live credit balance
							$update_powsc_info = array(array(	'field_name' => 'store_account_po_wsc',
																'operator' => '+',
																'value' => $add_amount)
							);
							$new_store_acc_balance = $po_suppliers_obj->_set_po_supplier_po_wsc_balance($input_array['user_id'], 'customers', $input_array['cur_acc'], $update_powsc_info);
						}
						
						// Insert account statement history
						$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($input_array['user_id']),
																	'user_role' => tep_db_prepare_input($input_array['user_role']),
											                        'store_account_history_date' => 'now()',
											                        'store_account_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
											                        'store_account_history_debit_amount' => 'NULL',
											                        'store_account_history_credit_amount' => (double)$add_amount,
											                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
											                        'store_account_history_activity_title' => LOG_ACC_STAT_MANUAL_ADDITION,
											                        'store_account_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
											                        'store_account_history_activity_desc_show' => (int)$input_array['show_comments'],
											                        'store_account_history_added_by' => $this->identity_email,
											                        'store_account_history_added_by_role' => 'admin'
											                       );
						if ($input_array['wsc_acc'] == 'po_wsc') {
							$account_balance_history_data_array['store_account_history_account_type'] = 'POWSC';
						}
						tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);

                        $transaction_id = tep_db_insert_id();
						
                        $transact_date = '';
                        if (tep_not_null($transaction_id)) {
                            $acc_stat_coments_details_select_sql = "SELECT store_account_history_date
                                                            		FROM ". TABLE_STORE_ACCOUNT_HISTORY ."
                                                           	 		WHERE store_account_history_id='". $transaction_id ."'";
                            $acc_stat_coments_details_result_sql = tep_db_query($acc_stat_coments_details_select_sql);
							
                            $acc_stat_coments_details_row = tep_db_fetch_array($acc_stat_coments_details_result_sql);
                            $transact_date = $acc_stat_coments_details_row['store_account_history_date'];
                        }
	                    
	                    if ($input_array['user_role'] == 'supplier' || $user_info_array['sign_up_from'] == 'CN') {
	                        // send email to supplier
	                        $email_supplier = sprintf(EMAIL_ACC_STAT_TEXT_TITLE, $transaction_id) . "\n" .
	                                          EMAIL_SEPARATOR . "\n\n" .
	                                          sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_NUMBER, $transaction_id) . "\n" . 
	                                          sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_DATE, date("Y-m-d H:i:s")) . "\n" .
	                                          sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_ADDITION, $currencies->format($add_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n\n" .
	                                          (($input_array['show_comments']) ? sprintf(EMAIL_ACC_STAT_TEXT_COMMENTS, $input_array['comments']) . "\n" : '') . 
	                                          EMAIL_TEXT_CLOSING . "\n\n" .
	                    				      EMAIL_FOOTER;
							
	                        $user_info_array = $this->_get_user_particulars($input_array['user_id'], $input_array['user_role']);
	                        $email_greeting = tep_get_email_greeting($user_info_array['fname'], $user_info_array['lname'], $user_info_array['gender']);
	                		$email_supplier = $email_greeting . $email_supplier;
					        @tep_mail($user_info_array['fname'] .' '. $user_info_array['lname'], $user_info_array['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_ADDITION, $transaction_id))), $email_supplier, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	    				}
    					
    					$email = sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_ID, $transaction_id) . "\n" .
    					         sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_DATE, $transact_date) . "\n" .
    					         sprintf(EMAIL_ACC_STAT_NOTIFY_TRANSACT_AMOUNT, LOG_ACC_STAT_MANUAL_ADDITION, $currencies->format($add_amount, false, tep_db_prepare_input($input_array['cur_acc']))) . "\n" .
    					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_USER, $this->identity_email) . "\n" .
    					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_IP, getenv("REMOTE_ADDR")) . "\n\n" .
    					         sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_COMMENT, tep_db_prepare_input($input_array['comments']));
						
                        $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
    					for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
    					    @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_NOTIFY_UPDATE_SUBJECT, LOG_ACC_STAT_MANUAL_ADDITION, $transaction_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            			}
						
						$messageStack->add_session(SUCCESS_ACC_STAT_MANUAL_ADDITION, 'success');
					} else {
						$error = true;
						$messageStack->add_session(ERROR_REACHED_DAILY_CREDIT_LIMIT, 'error');
					}
				} else {
					$error = true;
					$messageStack->add_session(ERROR_ACC_STAT_INVALID_ADDITION_AMOUNT, 'error');
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_ACC_STAT_CURRENCY_ACC_NOT_EXISTS, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function miscellaneous_add_amount($input_array, $action_message, $action_desc, &$messageStack) {
		$error = false;
		
		if ($this->_check_store_acc_exists($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'])) {
			$add_amount = trim($input_array['add_amount']);
			if (is_numeric($add_amount) && $add_amount > 0) {
				// Update live credit balance
				$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
												'operator'=> '+', 
												'value'=> $add_amount)
									);
				
				$new_store_acc_balance = $this->_set_store_acc_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
				if ($new_store_acc_balance !== false) {
					// Insert account statement history
					$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($input_array['user_id']),
																'user_role' => tep_db_prepare_input($input_array['user_role']),
										                        'store_account_history_date' => 'now()',
										                        'store_account_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
										                        'store_account_history_debit_amount' => 'NULL',
										                        'store_account_history_credit_amount' => (double)$add_amount,
										                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
										                        'store_account_history_activity_title' => tep_db_prepare_input($action_message),
										                        'store_account_history_activity_desc' => tep_db_prepare_input($action_desc),
										                        'store_account_history_added_by' => $this->identity_email,
										                        'store_account_history_added_by_role' => 'admin'
										                       );
					tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
				} else {
					$error = true;
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_STORE_BALANCE_AMOUNT, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_STORE_BALANCE_CURRENCY_ACC_NOT_EXISTS, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function miscellaneous_deduct_amount($input_array, $action_message, $action_desc, &$messageStack) {
		$error = false;
		
		if ($this->_check_store_acc_exists($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'])) {
			$deduct_amount = trim($input_array['deduct_amount']);
			if (is_numeric($deduct_amount) && $deduct_amount > 0) {
				// Update live credit balance
				$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
												'operator'=> '-', 
												'value'=> $deduct_amount)
									);
				
				$new_store_acc_balance = $this->_set_store_acc_balance($input_array['user_id'], $input_array['user_role'], $input_array['cur_acc'], $update_info);
				
				if ($new_store_acc_balance !== false) {
					// Insert account statement history
					$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($input_array['user_id']),
																'user_role' => tep_db_prepare_input($input_array['user_role']),
										                        'store_account_history_date' => 'now()',
										                        'store_account_history_currency' => tep_db_prepare_input($input_array['cur_acc']),
										                        'store_account_history_debit_amount' => (double)$deduct_amount,
										                        'store_account_history_credit_amount' => 'NULL',
										                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
										                        'store_account_history_activity_title' => tep_db_prepare_input($action_message),
										                        'store_account_history_activity_desc' => tep_db_prepare_input($action_desc),
										                        'store_account_history_added_by' => $this->identity_email,
										                        'store_account_history_added_by_role' => 'admin'
										                       );
					
					if (isset($input_array['trans_type']) && isset($input_array['trans_id'])) {
						$account_balance_history_data_array['store_account_history_trans_type'] = $input_array['trans_type'];
						$account_balance_history_data_array['store_account_history_trans_id'] = $input_array['trans_id'];
					}
					
					tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
				} else {
					$error = true;
				}
			} else {
				$error = true;
				$messageStack->add_session(ERROR_STORE_BALANCE_AMOUNT, 'error');
			}
		} else {
			$error = true;
			$messageStack->add_session(ERROR_STORE_BALANCE_CURRENCY_ACC_NOT_EXISTS, 'error');
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function search_payment_list($filename, $session_name) {
		$payment_list_html = '';
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
	  								array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
								);
		$status_options = $this->_get_payment_status();
		
		$payment_options = $this->_get_all_active_send_payment_methods(false);
		
		$date_type_array = array();
		
		$date_type_array[] = array ('id' => 0, 'text' => TEXT_ENTRY_DATE);
		
		$total_status_entry = count($status_options);
		for ($status_cnt=0; $status_cnt < $total_status_entry; $status_cnt++) {
			$date_type_array[] = array ('id' => $status_options[$status_cnt]['id'], 'text' => sprintf(TEXT_LAST_STATUS_DATE, $status_options[$status_cnt]['text']));
		}
		ob_start();
		?>
	  	<table width="100%" border="0" cellpadding="2" cellspacing="0">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
						</tr>
						<tr>
        					<td>
        						<?=tep_draw_form('payment_list_criteria', $filename, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main"><?=ENTRY_PAYMENT_START_DATE?></td>
										<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION[$session_name]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.payment_list_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.payment_list_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
    									<td class="main" width="8%">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=ENTRY_PAYMENT_END_DATE?></td>
    									<td class="main">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION[$session_name]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.payment_list_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.payment_list_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
			    						<td valign="top"><?=tep_draw_separator('pixel_trans.gif', '40', '1') . tep_draw_pull_down_menu('date_type', $date_type_array, $_SESSION[$session_name]["date_type"], ' id="date_type" ')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
	          							<td class="main"><?=ENTRY_PAYMENT_ID?></td>
	          							<td class="main" colspan="6"><?=tep_draw_input_field('payment_id', $_SESSION[$session_name]["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td valign="top" class="main"><?=ENTRY_CURRENT_PAYMENT_STATUS?></td>
						    			<td class="main" colspan="6">
						    				<table border="0" cellspacing="2" cellpadding="0">
						    			<?
						    				if (count($status_options)) {
					    						echo '<tr><td class="main">'.tep_draw_checkbox_field('payment_status_any', '1', isset($_SESSION[$session_name]) ? (count($_SESSION[$session_name]["payment_status"]) ? false : true) : false, '', 'id="payment_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>';
					    						echo '<tr>';
					    						for ($status_cnt=0; $status_cnt < count($status_options); $status_cnt++) {
					    							$order_status_display_str = '';
													$id = $status_options[$status_cnt]['id'];
							    					$title = $status_options[$status_cnt]['text'];
							    					
						    						$order_status_display_str .=
						    							'	<td class="main">'.
						    									tep_draw_checkbox_field('payment_status[]', $id, isset($_SESSION[$session_name]) ? (is_array($_SESSION[$session_name]["payment_status"]) && in_array($id, $_SESSION[$session_name]["payment_status"]) ? true : false) : ( $id=="2" ? true : false), '', 'onClick=verify_status_selection(this);') . '
						    								</td>
						    								<td class="main">'.$title.'</td>';
					    							echo $order_status_display_str;
						    					}
						    					echo '</tr>';
						    				}
						    			?>
						    				</table>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td valign="top" class="main"><?=ENTRY_PAYMENT_METHOD?></td>
						    			<td class="main">
						    			<?
						    				echo tep_draw_checkbox_field('payment_method_any', '1', isset($_SESSION[$session_name]) ? (count($_SESSION[$session_name]["payment_method"]) ? false : true) : false, '', 'id="payment_method_any" onClick="set_status_option(this);"') . "&nbsp;" . TEXT_ANY . '<br>';
						    				for ($pm_cnt=0; $pm_cnt < count($payment_options); $pm_cnt++) {
						    					$id = $payment_options[$pm_cnt]['id'];
						    					$title = $payment_options[$pm_cnt]['text'];
						    					echo tep_draw_checkbox_field('payment_method[]', $id, isset($_SESSION[$session_name]) ? (is_array($_SESSION[$session_name]["payment_method"]) && in_array($id, $_SESSION[$session_name]["payment_method"]) ? true : false) : ( $id=="1" || $id=="2" ? true : false), '', 'onClick=verify_status_selection(this);') . "&nbsp;" . $title . '<br>';
						    				}
						    			?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
						    			<td class="main">&nbsp;</td>
			    						<td colspan="4">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
		<script language="javascript"><!--
			function form_checking(form_obj, action) {
			    //form_obj.submit();
				return true;
    		}
    		
			function resetControls(controlObj) {
				if (trim_str(controlObj.value) != '') {
					document.payment_list_criteria.start_date.value = '';
					document.payment_list_criteria.end_date.value = '';
					document.payment_list_criteria.show_records.selectedIndex = 0;
					/*
					selected_count = 0;
					var multi_status_select = document.payment_list_criteria.elements['payment_status[]'];
					for (i=0;i<multi_status_select.length;i++) {
						if (i == 1) {	// Only check the Processing
							selected_count++;
							multi_status_select[i].checked = true;
						} else {
							multi_status_select[i].checked = false;
						}
					}
					
					if (!selected_count) {
						document.getElementById('payment_status_any').checked = true;
					} else {
						document.getElementById('payment_status_any').checked = false;
					}*/
	    		} else {
	    			controlObj.value = '';
	    		}
			}
			
			function set_status_option(any_status_obj) {
				if (any_status_obj.id == "payment_status_any") {
    				var multi_status_select = document.payment_list_criteria.elements['payment_status[]'];
    		    } else {
    		    	var multi_status_select = document.payment_list_criteria.elements['payment_method[]'];
    		    }
    			if (any_status_obj.checked == true) {
					for (i=0;i<multi_status_select.length;i++) {
						multi_status_select[i].checked = false;
					}
    			} else {	// force to check if no any order status option is selected
    				var selected_count = 0;
    				for (i=0;i<multi_status_select.length;i++) {
    					if (multi_status_select[i].checked == true) {
							selected_count++;
						}
					}
					if (!selected_count) {
						any_status_obj.checked = true;
					}
    			}
    		}
			
    		function verify_status_selection(any_status_obj) {
    			if (any_status_obj.name == "payment_status[]") {
    				var multi_status_select = document.payment_list_criteria.elements['payment_status[]'];
    				var selection_id = 'payment_status_any';
    		    } else {
    		    	var multi_status_select = document.payment_list_criteria.elements['payment_method[]'];	
    		    	var selection_id = 'payment_method_any';
    		    }
    			var selected_count = 0;
				for (i=0;i<multi_status_select.length;i++) {
					if (multi_status_select[i].checked == true) {
						selected_count++;
					}
				}
				if (!selected_count) {
					document.getElementById(selection_id).checked = true;
				} else {
					document.getElementById(selection_id).checked = false;
				}
    		}
    		
    		set_status_option(document.getElementById('payment_status_any'));
    	//-->
		</script>
		<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $acc_statement_html;
	}
	
	function show_payment_list($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		$pm_scope_js_array = array();
		$sc_scope_js_array = array();
		$pay_scope_js_array = array();
		$result_display_criteria = array();
		$g2g_fraud_list = array();
		
		$payment_manual_unlock_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_UNLOCK_PAYMENT');
		$payment_manual_complete_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_COMPLETE_PAYMENT');
		
		if (!$_REQUEST['cont']) {
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["date_type"] = $input_array["date_type"];
			$_SESSION[$session_name]["payment_id"] = $input_array["payment_id"];
			$_SESSION[$session_name]["payment_status"] = $input_array["payment_status"];
			$_SESSION[$session_name]["payment_method"] = $input_array["payment_method"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
	  	}
	  	
	  	$search_by_status_date = false;
	  	
	  	$group_by_str = $having_str = '';
	  	
	  	$payment_id_str = (isset($_SESSION[$session_name]["payment_id"]) && tep_not_null($_SESSION[$session_name]["payment_id"])) ? " sp.store_payments_id='" . $_SESSION[$session_name]["payment_id"] . "'" : "1";
	  	
	  	if (tep_not_null($_SESSION[$session_name]["start_date"])) {
	  		$result_display_criteria['date']['from'] = $_SESSION[$session_name]["start_date"];
	  		
	  		if ((int)$_SESSION[$session_name]["date_type"] < 1) {
				if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
					$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$start_date_str = " ( sp.store_payments_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
					$start_date_str = " ( sp.store_payments_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."')";
				}
			} else if ((int)$_SESSION[$session_name]["date_type"] > 0) {
				$search_by_status_date = true;
				$start_date_str = " 1 ";
				
				if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
					$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$having_str .= " HAVING latest_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' ";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
					$having_str .= " HAVING latest_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."'";
				}
			}
		} else {
			$start_date_str = " 1 ";
		}
		
		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			$result_display_criteria['date']['to'] = $_SESSION[$session_name]["end_date"];
			
			if ((int)$_SESSION[$session_name]["date_type"] < 1) {
				if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
					$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					$end_date_str = " ( sp.store_payments_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
					$end_date_str = " ( sp.store_payments_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."')";
				}
			} else if ((int)$_SESSION[$session_name]["date_type"] > 0) {
				$search_by_status_date = true;
				$end_date_str = " 1 ";
				
				if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
					$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					if (tep_not_null($having_str)) {
						$having_str .= " AND latest_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' ";
					} else {
						$having_str .= " HAVING (latest_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
					}
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
					if (tep_not_null($having_str)) {
						$having_str .= " AND latest_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."'";
					} else {
						$having_str .= " HAVING (latest_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."')";
					}
				}
			}
		} else {
			$end_date_str = " 1 ";
		}

		if (isset($_SESSION[$session_name]["payment_status"]) && is_array($_SESSION[$session_name]["payment_status"]) && count($_SESSION[$session_name]["payment_status"])) {
			$payment_status_str = " sp.store_payments_status IN ('" . implode("', '", $_SESSION[$session_name]["payment_status"]) . "') ";
		} else {
			$payment_status_str = " 1 ";
		}

		if ($search_by_status_date) {
			$sql_select_str = "SELECT sp.*, MAX( date_added ) AS latest_date FROM " . TABLE_STORE_PAYMENTS . " AS sp INNER JOIN " . TABLE_STORE_PAYMENTS_HISTORY . " AS sph ON (sph.store_payments_status = '" . (int)$_SESSION[$session_name]["date_type"] . "' AND sp.store_payments_id = sph.store_payments_id) ";
			$group_by_str = " GROUP BY store_payments_id ";
		} else {
			$sql_select_str = "SELECT * FROM " . TABLE_STORE_PAYMENTS . " AS sp ";
		}

		$sql_where_str = " WHERE 1 ";
	  	$sql_where_str .= " and $payment_id_str and $start_date_str and $end_date_str and $payment_status_str ";

	  	$sql_order_by_str = " order by sp.store_payments_date DESC ";

		$show_payment_method = isset($_SESSION[$session_name]["payment_method"]) ? $_SESSION[$session_name]["payment_method"] : array();
		if (!count($show_payment_method)) {
			$payment_options = $this->_get_all_active_send_payment_methods(false);

			for ($pm_cnt=0; $pm_cnt < count($payment_options); $pm_cnt++) {
				$show_payment_method[] = $payment_options[$pm_cnt]['id'];
			}
		}

		$show_records = $_SESSION[$session_name]["show_records"];

		$result_display_text = TEXT_DISPLAY_NUMBER_OF_PAYMENTS;

	  	for ($pm_cnt=0; $pm_cnt < count($show_payment_method); $pm_cnt++) {


	  		$show_currency_ex_colum = false;
	  		$payment_reference_input = false;

	  		$total_colspan = 12;

	  		$pm_id = $show_payment_method[$pm_cnt];

	  		// Check permission to process payment methods
	  		$pm_allow_process = $this->_is_allow_to_send_payment_methods($pm_id);
	  		// Check permission to update payment methods
	  		$pm_allow_update = $this->_is_allow_to_update_payment_methods($pm_id);
	  		// Check permission to cancel payment methods
	  		$pm_allow_cancel = $this->_is_allow_to_cancel_payment_methods($pm_id);

	  		$payment_select_sql = $sql_select_str . $sql_where_str . " and sp.store_payments_methods_id = '" . tep_db_input($pm_id) . "' " . $group_by_str . $having_str . $sql_order_by_str;

	  		if ($show_records != "ALL") {
				$payment_split_object = new splitPageResults($_REQUEST['page'.$pm_id], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $payment_select_sql, $payment_select_sql_numrows, true);
			}

			$payment_result_sql = tep_db_query($payment_select_sql);

			if ($this->_is_payment_methods_type_mode_system_define($pm_id) == '1') {
				$is_system_defined_method = true;
				$payment_method_info_select_sql = "	SELECT pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_mass_payment AS mass_payment, '' AS code, pmt.payment_methods_types_system_define, pg.payment_methods_filename
													FROM " . TABLE_PAYMENT_METHODS . " AS pm
													INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
														ON pm.payment_methods_types_id=pmt.payment_methods_types_id
													INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg
														ON pm.payment_methods_parent_id = pg.payment_methods_id
													WHERE pm.payment_methods_id = '" . tep_db_input($pm_id) . "' ";
			} else {
				$is_system_defined_method = false;
				$payment_method_info_select_sql = "	SELECT pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_mass_payment AS mass_payment, cur.code, pmt.payment_methods_types_system_define, pg.payment_methods_filename
													FROM " . TABLE_PAYMENT_METHODS . " AS pm
													INNER JOIN " . TABLE_CURRENCIES . " AS cur
														ON pm.payment_methods_send_currency=cur.currencies_id
													INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
														ON pm.payment_methods_types_id=pmt.payment_methods_types_id
													INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg
														ON pm.payment_methods_parent_id = pg.payment_methods_id
													WHERE pm.payment_methods_id = '" . tep_db_input($pm_id) . "' ";
			}
			$payment_method_info_result_sql = tep_db_query($payment_method_info_select_sql);
  			$payment_method_info_row = tep_db_fetch_array($payment_method_info_result_sql);

  			$payment_input_fields_array = $this->_get_payment_methods_fields($pm_id, array('1'));	// Just look for those input fields

  			ob_start();

  			$form_name = 'pm_'.$pm_id.'_lists_form';
  			$form_param = tep_get_all_get_params(array('action')) . 'action=batch_action';
			?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
          			<td>
          			<?
          				echo tep_draw_form($form_name, $filename, $form_param, 'post', 'enctype="multipart/form-data"');
          				echo tep_draw_hidden_field('pm_id', $pm_id);
          				echo tep_draw_hidden_field('system_defined_'.$pm_id, (($is_system_defined_method)?'yes':'no'), 'id=system_defined_'.$pm_id);
          			?>
          				<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td>
									<span class="pageHeading"><?=$payment_method_info_row['payment_methods_send_mode_name']?></span>
			<?
									if ($payment_method_info_row['payment_methods_filename'] == 'paypal.php' && $payment_method_info_row['mass_payment']) {
										require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPAL);
						  				$paypal_get_balance_obj = new paypal($pm_id);
										$portal = '';
										$g2g_pm_array = explode(',',G2G_PAYPAL_DISBURSE_PAYMENT_METHODS);
										if (in_array($pm_id, $g2g_pm_array )){
											$portal = 'G2G';
										}
										  $paypal_get_balance_obj->get_merchant_outgoing_account($payment_method_info_row['code'], $portal);
										  $balance_array = $paypal_get_balance_obj->get_balance('&ReturnAllCurrencies=1');
						  				$display_date = '-';
						  				if (isset($balance_array['TIMESTAMP'])) {
						  					$display_date = urldecode(preg_replace("/([A-Z]+)/", " ",$balance_array['TIMESTAMP']));
						  					preg_match_all("/([0-9]{4})\-([0-9]{2})\-([0-9]{2}) ([0-9]{1,2}):([0-9]{1,2})/", $display_date, $display_array);
						  					if (count($display_array) == 6) {
						  						$display_date = date("Y-m-d H:i", mktime($display_array[4][0]+8, $display_array[5][0], 0, $display_array[2][0], $display_array[3][0], $display_array[1][0]));
						  					}
						  				}
						  				echo "<BR>Balance: ";
						  				if ($balance_array['ACK']=='Success') {
						  					$display_array = array();
						  					foreach ($balance_array as $balance_key_loop => $balance_data_loop) {
							  					if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $balance_key_loop, $matched_key) && count($matched_key)==3) {
							  						$display_array[$matched_key[1]][$matched_key[2]] = urldecode($balance_data_loop);
												}
											}
											if (isset($display_array['AMT']) && count($display_array['AMT'])) {
												foreach ($display_array['AMT'] as $display_key_loop => $display_data_loop) {
													if ($payment_method_info_row['code'] == $display_array['CURRENCYCODE'][$display_key_loop]) {
														echo $currencies->format($display_data_loop, false, $display_array['CURRENCYCODE'][$display_key_loop]);
													}
												}
											}
											echo " at: ".$display_date."<BR>";
						  				} else if ($balance_array['ACK']=='SuccessWithWarning') {
						  					$display_array = array();
						  					foreach ($balance_array as $balance_key_loop => $balance_data_loop) {
							  					if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $balance_key_loop, $matched_key) && count($matched_key)==3) {
							  						$display_array[$matched_key[1]][$matched_key[2]] = urldecode($balance_data_loop);
												}
											}
											if (isset($display_array['AMT']) && count($display_array['AMT'])) {
												foreach ($display_array['AMT'] as $display_key_loop => $display_data_loop) {
													if ($payment_method_info_row['code'] == $display_array['CURRENCYCODE'][$display_key_loop]) {
														echo $currencies->format($display_data_loop, false, $display_array['CURRENCYCODE'][$display_key_loop]);
													}
												}
											}
											echo " at: ".$display_date."<BR>";
						  				} else {
						  					echo "Failed (Correlation ID: " . $balance_array['CORRELATIONID'].")";
						  				}
						  			} else if ($payment_method_info_row['payment_methods_filename'] == 'paypalEC.php' && $payment_method_info_row['mass_payment']) {
										require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPALEC);
						  				$paypal_get_balance_obj = new paypalEC($pm_id);
						  				$paypal_get_balance_obj->get_merchant_account($payment_method_info_row['code']);
						  				$balance_array = $paypal_get_balance_obj->getBalance('&ReturnAllCurrencies=1');
						  				$display_date = '-';
						  				if (isset($balance_array['TIMESTAMP'])) {
						  					$display_date = urldecode(preg_replace("/([A-Z]+)/", " ",$balance_array['TIMESTAMP']));
						  					preg_match_all("/([0-9]{4})\-([0-9]{2})\-([0-9]{2}) ([0-9]{1,2}):([0-9]{1,2})/", $display_date, $display_array);
						  					if (count($display_array) == 6) {
						  						$display_date = date("Y-m-d H:i", mktime($display_array[4][0]+8, $display_array[5][0], 0, $display_array[2][0], $display_array[3][0], $display_array[1][0]));
						  					}
						  				}
						  				echo "<BR>Balance: ";
						  				if ($balance_array['ACK']=='Success') {
						  					$display_array = array();
						  					foreach ($balance_array as $balance_key_loop => $balance_data_loop) {
							  					if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $balance_key_loop, $matched_key) && count($matched_key)==3) {
							  						$display_array[$matched_key[1]][$matched_key[2]] = urldecode($balance_data_loop);
												}
											}
											if (isset($display_array['AMT']) && count($display_array['AMT'])) {
												foreach ($display_array['AMT'] as $display_key_loop => $display_data_loop) {
													if ($payment_method_info_row['code'] == $display_array['CURRENCYCODE'][$display_key_loop]) {
														echo $currencies->format($display_data_loop, false, $display_array['CURRENCYCODE'][$display_key_loop]);
													}
												}
											}
											echo " at: ".$display_date."<BR>";
						  				} else if ($balance_array['ACK']=='SuccessWithWarning') {
						  					$display_array = array();
						  					foreach ($balance_array as $balance_key_loop => $balance_data_loop) {
							  					if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $balance_key_loop, $matched_key) && count($matched_key)==3) {
							  						$display_array[$matched_key[1]][$matched_key[2]] = urldecode($balance_data_loop);
												}
											}
											if (isset($display_array['AMT']) && count($display_array['AMT'])) {
												foreach ($display_array['AMT'] as $display_key_loop => $display_data_loop) {
													if ($payment_method_info_row['code'] == $display_array['CURRENCYCODE'][$display_key_loop]) {
														echo $currencies->format($display_data_loop, false, $display_array['CURRENCYCODE'][$display_key_loop]);
													}
												}
											}
											echo " at: ".$display_date."<BR>";
						  				} else {
						  					echo "Failed (Correlation ID: " . $balance_array['CORRELATIONID'].")";
						  				}
						  			} else if ($payment_method_info_row['payment_methods_filename'] == 'pipwavePG.php' && $payment_method_info_row['mass_payment']) {
                                                                                //disbursement-g2g
										require_once(DIR_FS_CATALOG_MODULES . 'payment/pipwavePG.php');
						  				$pipwave_get_balance_obj = new pipwavePG($pm_id);
						  				$pipwave_get_balance_obj->get_merchant_outgoing_account($payment_method_info_row['code']);
						  				$balance_amount = $pipwave_get_balance_obj->getBalance($payment_method_info_row['code']);
						  				echo "<BR>Balance: " . $balance_amount;
                                                                        }
			?>
								</td>
								<td class="main" align="right">
								<?
									if (isset($result_display_criteria['date'])) {
										if (count($result_display_criteria['date']) > 1) {
											echo implode(' - ', $result_display_criteria['date']);
										} else {
											if (isset($result_display_criteria['date']['from']))	echo 'From ' . $result_display_criteria['date']['from'];
											if (isset($result_display_criteria['date']['to']))	echo 'Till ' . $result_display_criteria['date']['to'];
										}
									}
								?>
								</td>
							</tr>
						</table>
          				<table border="0" width="100%" cellspacing="1" cellpadding="1">
							<tr>
								<td width="8%" class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_ID?></td>
								<td class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_USER?></td>
								<td class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_USER_EMAIL?></td>
								<td class="reportBoxHeading" nowrap>Fraud</td>
								<td class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_SUPPLIER_AGING?></td>
								<td class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_BAD_ORDERS_RATIO?></td>
								<td width="15%" align="center" class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_ACC_STAT?></td>
								<!--DYNAMIC_COLUMNS-->
			<?
				if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
					foreach ($payment_input_fields_array as $field_id => $field_info) {
						echo '<td align="left" class="reportBoxHeading" nowrap>'.$field_info['payment_methods_fields_title'].'</td>';
					}
					
					$total_colspan += count($payment_input_fields_array);
				}
			?>
								<!--DYNAMIC_COLUMNS-->
								<!--SYSTEM_EXCHANGE_HEADER-->
								<td width="15%" align="right" class="reportBoxHeading" colspan="2" nowrap><?=TABLE_HEADING_PAYMENT_REQUEST_AMOUNT?></td>
								<td width="12%" align="right" class="reportBoxHeading" nowrap><?=TABLE_HEADING_PAYMENT_EX_RATE?></td>
								<!--SYSTEM_EXCHANGE_HEADER-->
								<? if ($is_system_defined_method) { ?>
								<td width="15%" align="right" class="reportBoxHeading" colspan="2" nowrap><?=TABLE_HEADING_PAYMENT_PAYOUT_AMOUNT?></td>
								<? } else { 
									if ($payment_method_info_row['payment_methods_filename']=='paypal.php') {?>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_AMOUNT, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_FEE, $payment_method_info_row['code'])?></td>
									    <td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_PAYOUT_TAX_CURRENCY, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_NET_PAYABLE_AMOUNT, $payment_method_info_row['code'])?></td>
								<? } else if ($payment_method_info_row['payment_methods_filename']=='paypalEC.php') {?>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_AMOUNT, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_FEE, $payment_method_info_row['code'])?></td>
									    <td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_PAYOUT_TAX_CURRENCY, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_NET_PAYABLE_AMOUNT, $payment_method_info_row['code'])?></td>
								<? } else if ($payment_method_info_row['payment_methods_filename']=='pipwavePG.php') {?>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_AMOUNT, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_WIDTHDRAW_FEE, $payment_method_info_row['code'])?></td>
									    <td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_PAYOUT_TAX_CURRENCY, $payment_method_info_row['code'])?></td>
										<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_NET_PAYABLE_AMOUNT, $payment_method_info_row['code'])?></td>
								<?} else { ?>
									<td width="10%" align="center" class="reportBoxHeading"><?=sprintf(TABLE_HEADING_PAYMENT_PAYOUT_CURRENCY, $payment_method_info_row['code'])?></td>
								<?	}
								 } ?>
							    <td width="18%" class="reportBoxHeading" align="left" nowrap><?=TABLE_HEADING_PAYMENT_REMARK?></td>
							    <td width="13%" class="reportBoxHeading" align="center" nowrap><?=TABLE_HEADING_PAYMENT_REFERENCE?></td>
							    <td width="5%" class="reportBoxHeading" nowrap>&nbsp;</td>
							    <td width="10%" class="reportBoxHeading" align="center" nowrap><?=TABLE_HEADING_PAYMENT_ACTION?></td>
							    <td width="1%" class="reportBoxHeading">
							    	<?
							    	if ($is_system_defined_method) {
							    		echo tep_draw_checkbox_field('select_all_'.$pm_id, '', false, '', 'id="select_all_'.$pm_id.'" title="Select or deselect all payment records" onClick="javascript:void(setActiveCheckboxes(\''.$form_name.'\',\'select_all_'.$pm_id.'\',\'payments_batch\')); update_selected_sc_price(this.form, \''.$pm_id.'\', \'payments_batch\'); "');
							    	} else {
							    		echo tep_draw_checkbox_field('select_all_'.$pm_id, '', false, '', 'id="select_all_'.$pm_id.'" title="Select or deselect all payment records" onClick="javascript:void(setActiveCheckboxes(\''.$form_name.'\',\'select_all_'.$pm_id.'\',\'payments_batch\')); update_selected_price(this.form, \''.$pm_id.'\', \'payments_batch\'); "');
							    	}
							    	?>
							    </td>
							    <td width="1%" class="reportBoxHeading"></td>

							</tr>
							<tbody>
			<?
			$row_count = 0;
			$store_payment_id_list = [];
			// get all the store_payment_id
			while ($payment_row = tep_db_fetch_array($payment_result_sql)) {
				$store_payment_id_list[] = $payment_row['store_payments_id'];
			}

			$store_credit_extra_info_list = [];

			if (!empty($store_payment_id_list)) {
				//refresh the result pointer
				tep_db_data_seek($payment_result_sql, 0);

				// get all store_payments_extra_info based on ids
				$payment_select_extra_info_sql = "SELECT * FROM ".TABLE_STORE_PAYMENTS_EXTRA_INFO." WHERE store_payments_id IN (".implode(',',$store_payment_id_list).")";
				$payment_select_extra_info_result = tep_db_query($payment_select_extra_info_sql);
				//put all store_payments_extra_info into an array list
				while($row = tep_db_fetch_array($payment_select_extra_info_result)){
					$store_credit_extra_info_list[$row['store_payments_id']][$row['store_payments_extra_info_key']] = $row['store_payments_extra_info_value'];
				}	
			}

			while ($payment_row = tep_db_fetch_array($payment_result_sql)) {
				$store_credit_extra_info = isset($store_credit_extra_info_list[$payment_row['store_payments_id']]) ? $store_credit_extra_info_list[$payment_row['store_payments_id']]:[];
				$tax_amount = 0;
				if(isset($store_credit_extra_info['tax_enabled']) && $store_credit_extra_info['tax_enabled']){
					$tax_amount = $store_credit_extra_info['tax_amount'];
				}
				$rounded_tax_amount = $request_amount = number_format($tax_amount, $this->currency_display_decimal, '.', '');

				if(!isset($g2g_fraud_list[$payment_row['user_id']])){
					$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:blue'>No checking on old seller</span>";
					$g2g_fraud_select_sql = "	SELECT opt_status, aft_status, fraud_status
												FROM " . TABLE_G2G_FRAUD . "
												WHERE customers_id = '" . $payment_row['user_id'] . "' ";
					$g2g_fraud_result_sql = tep_db_query($g2g_fraud_select_sql);
					$g2g_fraud_result_row = tep_db_fetch_array($g2g_fraud_result_sql);
					if($g2g_fraud_result_row){
						if($g2g_fraud_result_row['fraud_status'] == '3'){
							$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:green'>No fraud</span>";
						} else {
							$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:orange'>Pending Review</span>";
							switch($g2g_fraud_result_row['opt_status']){
								case '1':
								case '3':
									switch($g2g_fraud_result_row['aft_status']){
										case '1':
										case '3':
											$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:green'>Approved</span>";
										break;
										case '2':
											$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:red'>Rejected</span>";
										break;
									}
									break;
								case '2':
									$g2g_fraud_list[$payment_row['user_id']] = "<span style='color:red'>Rejected</span>";
									break;
							}
						}
					} 
				}
				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
				$tr_is_lock = false;
				$td_style = '';
				
				if ($payment_row['store_payments_lock'] && !$payment_manual_unlock_payment_permission) {
					if ($payment_row['store_payments_status'] == 2) {
						$tr_is_lock = true;
						$td_style = ' style="color:#A0A0A0;" ';
					}
				}
				
				$this_payment_need_need_ex_rate = false;
				
				$payment_id = $payment_row['store_payments_id'];
				$live_exchange_rate = false;
				$payment_batch_available = true;
				
				$payment_details_array = $this->get_payment_details($payment_id);	// Just look for those input fields
				$user_info_array = $this->_get_user_particulars($payment_row['user_id'], $payment_row['user_role']);

				// check the current store_payment record payment method fields value has successful pass payment				
				if (in_array((int)$payment_row['store_payments_status'], array(1,2))) {
						$pm_validated = $this->_compare_pass_completed_withdraw_method($payment_row['user_id'],$pm_id,$payment_input_fields_array,$payment_details_array);
				} else {
						$pm_validated = true;
				}
				$payment_validated_style = ($pm_validated === true) ? '' : ($pm_validated === 2 ? 'class="orangeIndicator"' : 'class="redIndicator"');
				
				$this->get_payment_aging_info($payment_id, true, $supplier_aging_str, $supplier_cb_ratio_str);

				// Email must be use the user's latest email address
				$acc_stat_link = sprintf(LINK_PAYMENT_ACCOUNT_STATEMENT, tep_href_link(FILENAME_ACCOUNT_STATEMENT, 'action=show_report&user_role='.urlencode($payment_row['user_role']).'&user_email='.urlencode($user_info_array['email']).'&start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL'), $td_style);
				
				if ($payment_row['user_role'] == 'supplier') {
					$user_name_link = '<a href="'.tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=edit_supplier&sID='.$payment_row['user_id']).'" target="_blank" '.$td_style.'>'.$payment_row['user_firstname'] . ' ' . $payment_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
				} else {
					$user_name_link = '<a href="'.tep_href_link(FILENAME_CUSTOMERS, 'cID='.$payment_row['user_id'].'&action=edit').'" target="_blank" '.$td_style.'>'.$payment_row['user_firstname'] . ' ' . $payment_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
				}
				
				if ($is_system_defined_method) {
					$show_currency_ex_colum = true;
					$this_payment_need_need_ex_rate = true;
				} else {
					if ($payment_row['store_payments_request_currency'] != $payment_row['store_payments_paid_currency']) {
						$show_currency_ex_colum = true;
						$this_payment_need_need_ex_rate = true;
					}
				}
				
				//$request_amount = number_format(tep_round($payment_row['store_payments_after_fees_amount'], $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
				$request_amount = $payment_row['store_payments_after_fees_amount'];
                                
				if ($payment_row['store_payments_status'] == '2') {	// Only editable when it is Processing status
					// style - red colour
					if (tep_not_null($payment_row['store_payments_paid_currency_value'])) {
						$rate_input_style = '';
						$stored_currency_rate = $payment_row['store_payments_paid_currency_value'];
					} else {
						$live_exchange_rate = true;
						$rate_input_style = 'class="redInputBox"';
						$stored_currency_rate = $currencies->advance_currency_conversion_rate($payment_row['store_payments_request_currency'], $payment_row['store_payments_paid_currency']);
					}
					$exchange_rate_str = tep_draw_input_field('exchange_rate['.$payment_id.']', $stored_currency_rate, ' id="exchange_rate_'.$payment_id.'" size="12" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; } refresh_payout_price(this, \''.$payment_id.'\', \''.$request_amount.'\', \''.$this->currency_display_decimal.'\'); " onBlur="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value)) ) { this.value = \'\'; } refresh_payout_price(this, \''.$payment_id.'\', \''.$request_amount.'\', \''.$this->currency_display_decimal.'\');" onKeyPress="return noEnterKey(event)" ' . $rate_input_style);
					
					if ($payment_method_info_row['payment_methods_filename']=='paypal.php' && $payment_method_info_row['mass_payment']) {
						$payment_reference_str = $payment_row['store_payments_reference'];
					} else if ($payment_method_info_row['payment_methods_filename']=='paypalEC.php' && $payment_method_info_row['mass_payment']) { 
						$payment_reference_str = $payment_row['store_payments_reference'];
					} else if ($payment_method_info_row['payment_methods_filename']=='pipwavePG.php' && $payment_method_info_row['mass_payment']) { 
						$payment_reference_str = tep_draw_input_field('payment_reference['.$payment_id.']', $payment_row['store_payments_reference'], ' id="payment_reference_'.$payment_id.'" size="16" onKeyPress="return noEnterKey(event)" ');
					} else {
						$payment_reference_str = tep_draw_input_field('payment_reference['.$payment_id.']', $payment_row['store_payments_reference'], ' id="payment_reference_'.$payment_id.'" size="16" onKeyPress="return noEnterKey(event)" ');
					}
					
					$payment_reference_input = true;
				} else {
					if (tep_not_null($payment_row['store_payments_paid_currency_value'])) {
						$exchange_rate_str = $payment_row['store_payments_paid_currency_value'];
					} else {
						$live_exchange_rate = true;
						$exchange_rate_str = $currencies->advance_currency_conversion_rate($payment_row['store_payments_request_currency'], $payment_row['store_payments_paid_currency']);
					}
					
					$stored_currency_rate = $exchange_rate_str;
					if ($this->_is_payment_methods_type_mode_system_define($pm_id) && tep_not_null($payment_row['store_payments_reference'])) {
						$is_g2g = c2c_invoice::check_g2g_withdraw($payment_id);
                        if($is_g2g == true){
                            $payment_reference_str = '<a href="' . CREW2_PATH_G2G . '/store-credit/report?request_id=' . $payment_row['store_payments_reference'] .'" target="_blank">' .sprintf(TITLE_TRANS_STORE_CREDIT, $payment_row['store_payments_reference']).'</a>' ;                                                                                   
						}
                        else{
							$payment_reference_str = "<a href=\"" . CREW2_PATH . '/store-credit/index?transaction_id=' . $payment_row['store_payments_reference'] .'&start_date=' . date('Y-m-d', strtotime($payment_row['store_payments_date'])) . "\" target=\"_blank\">" . sprintf(TITLE_TRANS_STORE_CREDIT, $payment_row['store_payments_reference']) . "</a>";
						}
					} else {
						$payment_reference_str = $payment_row['store_payments_reference'];
					}
				}
				
				$actual_payout_amount = $request_amount * $stored_currency_rate;
				
				$rounded_actual_payout_amount = number_format(tep_round($actual_payout_amount, $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
				$pay_scope_js_array[$payment_id]['payout_amount'] = $rounded_actual_payout_amount;
				
				$actual_payment_style = ($payment_row['store_payments_request_currency'] != $payment_row['store_payments_paid_currency'] && $live_exchange_rate) ? 'class="redIndicator"' : '';
				
				switch($payment_row['store_payments_status']) {
					case '1':	// Pending
						if ($payment_row['user_role'] == 'customers' && $this->_is_payment_methods_type_mode_system_define($pm_id)) {
							$action_button_html = tep_button('Issue NRSC', 'Process this payment', '', ' name="ProcessBtn_'.$payment_id.'" onClick="updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'1\', \'3\', \''.$pm_id.'\');" ', 'inputButton') . '&nbsp;';
						} else {
							$action_button_html = tep_button('Process', 'Process this payment', '', ' name="ProcessBtn_'.$payment_id.'" onClick="updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'1\', \'2\', \''.$pm_id.'\');" ', 'inputButton') . '&nbsp;';
						}
						
						// Check for allow user to process payment methods
						if ($pm_allow_process == false) {
							$action_button_html = 'Access Denied';
						}
						
						break;
					case '2':	// Processing
						if ($payment_method_info_row['payment_methods_filename']=='paypal.php' && $payment_method_info_row['mass_payment']) {
							if ( $payment_row['store_payments_lock']) {
								$action_button_html = 'Processing&nbsp;';
							} else {
								$action_button_html = tep_button('Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						
							if ($payment_manual_complete_payment_permission) {
								$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						} else if ($payment_method_info_row['payment_methods_filename']=='paypalEC.php' && $payment_method_info_row['mass_payment']) {
							if ( $payment_row['store_payments_lock']) {
								$action_button_html = 'Processing&nbsp;';
							} else {
								$action_button_html = tep_button('PaypalEC Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						
							if ($payment_manual_complete_payment_permission) {
								$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						} else if ($payment_method_info_row['payment_methods_filename']=='pipwavePG.php' && $payment_method_info_row['mass_payment']) {
                                                        //disbursement-g2g - page list button
							if ( $payment_row['store_payments_lock']) {
								$action_button_html = 'Processing&nbsp;';
							} else {
								$action_button_html = tep_button('Payment API Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						
							if ($payment_manual_complete_payment_permission) {
								$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
							}
						} else {
							$action_button_html = tep_button('Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$payment_id.'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updatePayment(\''.$filename.'\', this, \''.$payment_id.'\', \'2\', \'3\', \''.$pm_id.'\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
						}
						
						// Check for allow user to process payment methods
						if ($pm_allow_process == false) {
							$action_button_html = 'Access Denied';
						}
						
						break;
					case '3':	// Completed
						$action_button_html = 'Completed';
						break;
					case '4':	// Canceled
						$action_button_html = 'Canceled';
						
						break;
					default:
						$action_button_html = '';
						
						break;
				}
				
				// Check allow user to process payment methods - Link to edit payment
				if ($pm_allow_process || $pm_allow_update || $pm_allow_cancel) {
					$edit_payment_link = '<a href="' . tep_href_link($filename, 'payID='.$payment_id.'&action=edit', 'NONSSL') . '" '.$td_style.'>'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>';
					$checkbox_html = tep_draw_checkbox_field('payments_batch[]', $payment_id, false, '', 'id="'.$payment_id.'" onClick="update_selected_sc_price(this.form, \''.$pm_id.'\', \'payments_batch\');" '.(!$payment_batch_available || $tr_is_lock ? ' ' : ''));
				} else {
					$edit_payment_link = '';
					$checkbox_html = '';
				}
				
				if ($tr_is_lock) {
					echo '	<tr height="20" style="background-color:rgb(240, 240, 240);">';
				} else {
					echo ' 	<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">';
				}
				echo ' 			<td valign="top" class="reportRecords" nowrap '.$td_style.'>'.$payment_id.'</td>
								<td valign="top" class="reportRecords" nowrap '.$td_style.'>'.$user_name_link.'</td>
								<td valign="top" class="reportRecords" nowrap '.$td_style.'>'.$user_info_array['email'].'</td>
								<td valign="top" class="reportRecords" nowrap '.$td_style.'><a href="'.tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_fraud&customer_id='.$payment_row['user_id']) .'" target="_blank"><b>'.$g2g_fraud_list[$payment_row['user_id']].'</b></a></td>
								<td align="center" valign="top" class="reportRecords" nowrap>'.$supplier_aging_str.'</td>
								<td align="center" valign="top" class="reportRecords" nowrap>'.$supplier_cb_ratio_str.'</td>
								<td align="center" valign="top" class="reportRecords" '.$td_style.'>'.$acc_stat_link.'</td>';
				echo '			<!--DYNAMIC_COLUMNS-->';
				if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
					foreach ($payment_input_fields_array as $field_id => $field_info) {
						echo '	<td align="left" valign="top" class="reportRecords" '.$td_style.' nowrap><span '.$payment_validated_style.'>'.nl2br($payment_details_array[$field_id]['payment_methods_fields_value']).'</span></td>';
					}
				}
				echo '			<!--DYNAMIC_COLUMNS-->';
				
				echo '			<!--SYSTEM_EXCHANGE_CONTENT-->
								<td width="5%" align="center" valign="top" '.$td_style.' class="reportRecords" nowrap>'.$payment_row['store_payments_request_currency'] .'</td>
								<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.$request_amount.'</td>
								<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.($this_payment_need_need_ex_rate ? $exchange_rate_str : TEXT_PAYMENT_NOT_APPLICABLE).'</td>
								<!--SYSTEM_EXCHANGE_CONTENT-->';
				if ($is_system_defined_method) {
					echo '			<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.$payment_row['store_payments_paid_currency'].'</td>';
				} else if ($payment_method_info_row['payment_methods_filename']=='paypal.php') {
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_request_amount']*$stored_currency_rate : $payment_row['store_payments_request_amount']), $this->currency_display_decimal, '.', '').'</td>';
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_fees']*$stored_currency_rate : $payment_row['store_payments_fees']), $this->currency_display_decimal, '.', '').'</td>';
					echo '  <td align="right" valign="top" class="reportRecords" '.$td_style.'><span '.$actual_payment_style.' id="span_tax_'.$payment_id.'">'.$rounded_tax_amount.'</span></td>';
				} else if ($payment_method_info_row['payment_methods_filename']=='paypalEC.php') {
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_request_amount']*$stored_currency_rate : $payment_row['store_payments_request_amount']), $this->currency_display_decimal, '.', '').'</td>';
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_fees']*$stored_currency_rate : $payment_row['store_payments_fees']), $this->currency_display_decimal, '.', '').'</td>';
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'><span '.$actual_payment_style.' id="span_tax_'.$payment_id.'">'.$rounded_tax_amount.'</span></td>';
				} else if ($payment_method_info_row['payment_methods_filename']=='pipwavePG.php') {
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_request_amount']*$stored_currency_rate : $payment_row['store_payments_request_amount']), $this->currency_display_decimal, '.', '').'</td>';
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'>'.number_format(($this_payment_need_need_ex_rate ? $payment_row['store_payments_fees']*$stored_currency_rate : $payment_row['store_payments_fees']), $this->currency_display_decimal, '.', '').'</td>';
					echo '	<td align="right" valign="top" class="reportRecords" '.$td_style.'><span '.$actual_payment_style.' id="span_tax_'.$payment_id.'">'.$rounded_tax_amount.'</span></td>';
				}
				echo '			<td align="right" valign="top" class="reportRecords" '.$td_style.'><span '.$actual_payment_style.' id="span_payout_'.$payment_id.'">'.$rounded_actual_payout_amount.'</span></td>';
				if(in_array($payment_row['store_payments_methods_id'],explode(',',G2G_PAYPAL_DISBURSE_PAYMENT_METHODS))){
					echo '			<td align="left" valign="top" class="reportRecords" '.$td_style.' nowrap>'.sprintf(TEXT_G2G_PAYMENT_REMARKS, $payment_id).'</td>';}
					else {
						$is_g2g = c2c_invoice::check_g2g_withdraw($payment_id);
                        if($is_g2g == true){
                            echo '	<td align="left" valign="top" class="reportRecords" '.$td_style.' nowrap>'.sprintf(TEXT_G2G_PAYMENT_REMARKS, $payment_id).'</td>';
						}
                        else{
					echo '	<td align="left" valign="top" class="reportRecords" '.$td_style.' nowrap>'.sprintf(TEXT_PAYMENT_REMARKS, $payment_id).'</td>';
					}
					}
					echo '
								<td align="center" valign="top" class="reportRecords" '.$td_style.'>'.$payment_reference_str.'</td>
								<td align="center" valign="top" class="reportRecords" '.$td_style.'>
									'.$edit_payment_link.'
								</td>
								<td align="center" valign="top" class="reportRecords" '.$td_style.' nowrap>'.
									$action_button_html . '
								</td>';
				if ($is_system_defined_method) {
				echo '			<td class="reportRecords" valign="top" '.$td_style.'>'.$checkbox_html.'</td>';
				} else {
				echo '			<td class="reportRecords" valign="top" '.$td_style.'>'.tep_draw_checkbox_field('payments_batch[]', $payment_id, false, '', 'id="'.$payment_id.'" onClick="update_selected_price(this.form, \''.$pm_id.'\', \'payments_batch\');" '.(!$payment_batch_available || $tr_is_lock ? ' ' : '')).'</td>';
				}
				echo '			<td class="reportRecords" valign="top" align="center" '.$td_style.'>'.( $payment_row['store_payments_status'] != 3 && $payment_row['store_payments_lock'] ? tep_image(DIR_WS_IMAGES . 'icons/locked.gif', 'Locked', '', '', ' pid="'.$payment_id.'" class="img_lock" ') : '').'</td>';
				echo '		</tr>';
				
				if ($is_system_defined_method) {
					$sc_scope_js_array[$payment_id]['currency_code'] = $payment_row['store_payments_paid_currency'];
					$sc_scope_js_array[$payment_id]['symbol_left'] = $currencies->currencies[$payment_row['store_payments_paid_currency']]['symbol_left'];
					$sc_scope_js_array[$payment_id]['symbol_right'] = $currencies->currencies[$payment_row['store_payments_paid_currency']]['symbol_right'];
					$sc_scope_js_array[$payment_id]['currency_ex'] = $show_currency_ex_colum;
				}
				$row_count++;
			}
			
			if ($show_currency_ex_colum)	$total_colspan += 3;
			
			$batch_file_input = '';
			$batch_action_array = array(array('id' => '', 'text' => 'With selected:'));
			if ($show_currency_ex_colum)	$batch_action_array[] = array('id' => 'ExRate', 'text' => 'Update Rate');
			$batch_action_array[] = array('id' => 'Process', 'text' => 'Process Payment');
			$batch_action_array[] = array('id' => 'Complete', 'text' => 'Complete Payment');
                        //disbursement-g2g - enhance dropdown button
			if ($payment_method_info_row['mass_payment'] == '1') {
				if ($pm_id == '3') {	// ICBC Bank
					$batch_file_input = tep_draw_file_field('csv_import', 'size="50"') . "&nbsp;&nbsp;";
					$batch_action_array[] = array('id' => 'MassPayCSV', 'text' => 'Export to Kuai Qian Batch Payment xls');
					$batch_action_array[] = array('id' => 'ImportMassPayCSV', 'text' => 'Batch Complete from Kuai Qian Imported xls');
				} else if ($payment_method_info_row['payment_methods_filename'] == 'paypal.php') {
					$batch_file_input = tep_draw_file_field('csv_import', 'size="50"') . "&nbsp;&nbsp;";
					$batch_action_array[] = array('id' => 'MassPayCSV', 'text' => 'Export to PayPal Mass Payment tab-delimited file');
					//$batch_action_array[] = array('id' => 'ImportMassPayCSV', 'text' => 'Batch Complete from PayPal Imported xls');
				} else if ($payment_method_info_row['payment_methods_filename'] == 'paypalEC.php') {
					$batch_file_input = tep_draw_file_field('csv_import', 'size="50"') . "&nbsp;&nbsp;";
					$batch_action_array[] = array('id' => 'MassPayCSV', 'text' => 'Export to PayPalEC Mass Payment tab-delimited file');
					//$batch_action_array[] = array('id' => 'ImportMassPayCSV', 'text' => 'Batch Complete from PayPal Imported xls');
				} else if ($payment_method_info_row['payment_methods_filename'] == 'pipwavePG.php') {
					
					//$batch_action_array[] = array('id' => 'ImportMassPayCSV', 'text' => 'Batch Complete from PayPal Imported xls');
				} else {
					$batch_file_input = tep_draw_file_field('csv_import', 'size="50"') . "&nbsp;&nbsp;";
					$batch_action_array[] = array('id' => 'MassPayCSV', 'text' => 'Export to Alipay Batch Payment xls');
					$batch_action_array[] = array('id' => 'ImportMassPayCSV', 'text' => 'Batch Complete from Alipay Imported xls');
				}
			}
			
			echo '			<tr>
								<td align="left" class="ordersRecords" colspan="3">&nbsp;</td>
								<td align="right" colspan="'.($total_colspan-3).'">
									<table border="0" width="100%" cellspacing="0" cellpadding="2">
										<tr>
											<td width="100px" align="right" class="ordersRecords">'.TEXT_TOTAL_SELECTED_AMOUNT.'</td>
											<td width="100px" class="ordersRecords"><span id="total_sel_amt_'.$pm_id.'" class="redIndicator"></span></td>
											<td width="20px" align="right" class="ordersRecords">'.TEXT_TOTAL_SELECTED_RECORDS.'</td>
											<td width="20px" class="ordersRecords"><span id="total_sel_rec_'.$pm_id.'" class="redIndicator"></span></td>
											<td align="right" nowrap>';
			
			if (count($batch_action_array) > 1) {
				echo	$batch_file_input . tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;' . tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction(this.form, \''.$order_status_id.'\', \'orders_batch\')"', 'inputButton');
			}
			
			echo '							</td>
										</tr>
									</table>
								</td>
							</tr>';
			?>
							</tbody>
						</table>
						</form>
					</td>
				</tr>
				<tr>
        			<td colspan="<?=$total_colspan?>">
        				<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($payment_result_sql) > 0 ? "1" : "0", tep_db_num_rows($payment_result_sql), tep_db_num_rows($payment_result_sql)) : $payment_split_object->display_count($payment_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'.$pm_id], $result_display_text)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $payment_split_object->display_links($payment_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'.$pm_id], tep_get_all_get_params(array('page'.$pm_id, 'cont'))."cont=1", 'page'.$pm_id)?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
			</table>
			<?
			if ($payment_manual_unlock_payment_permission) {
			?>
			<script>
				jQuery("img.img_lock").click(function() {
					var pid = jQuery(this).attr('pid');
					jquery_confirm_box("Are you sure to unlock this payment'?",2 , 0 , "Confirm");
					jQuery('#jconfirm_submit').click(function(){
						jquery_confirm_box('<h1>Unlocking...</h1>', 0, 0);
						jQuery.ajax({
							type: "GET",
							dataType: 'xml',
							url: "payment_xmlhttp.php?action=unlock_store_payment&spID=" + pid,
							timeout: 10000,
						    error: function(){
						        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
						    },
							success: function(xml){
								if (jQuery(xml).find('result').text() == '1') {
									location.href = "<?=tep_href_link($filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report&cont=1')?>";
								}
							}
						});
					});
				});
			</script>
			<?
		}
		?>
			<br>
		<?
			$report_section_html = ob_get_contents();
			ob_end_clean();
			
			if (!$show_currency_ex_colum) {
				$report_section_html = preg_replace("/(<!--SYSTEM_EXCHANGE_HEADER-->)(.*?)(<!--SYSTEM_EXCHANGE_HEADER-->)/is", '', $report_section_html);
				$report_section_html = preg_replace("/(<!--SYSTEM_EXCHANGE_CONTENT-->)(.*?)(<!--SYSTEM_EXCHANGE_CONTENT-->)/is", '', $report_section_html);
			}
			
			if (!$is_system_defined_method) {
				$pm_scope_js_array[$pm_id]['currency_code'] = $payment_method_info_row['code'];
				$pm_scope_js_array[$pm_id]['symbol_left'] = $currencies->currencies[$payment_method_info_row['code']]['symbol_left'];
				$pm_scope_js_array[$pm_id]['symbol_right'] = $currencies->currencies[$payment_method_info_row['code']]['symbol_right'];
				$pm_scope_js_array[$pm_id]['currency_ex'] = $show_currency_ex_colum;
			}
			
			$parsed_report_section_html .= $report_section_html;
	  	}
	  	
	  	ob_start();
		?>
	  	<script language="javascript">
		<!--
			var currencyInfoArray = new Array();
			var storecreditInfoArray = new Array();
			var paymentAmountArray = new Array();
		<?
			foreach ($pm_scope_js_array as $id => $pm_info) {
				echo "currencyInfoArray[".$id."] = new Array();\n";
				
				if (isset($pm_info['currency_code'])) {
					echo "currencyInfoArray[".$id."]['currency_code'] = '" . $pm_info['currency_code'] . "';\n";
				}
				
				if (isset($pm_info['currency_ex']))	{
					echo "currencyInfoArray[".$id."]['currency_ex'] = '" . $pm_info['currency_ex'] . "';\n";
				}
				
				if (isset($pm_info['symbol_left']))	{
					echo "currencyInfoArray[".$id."]['symbol_left'] = '" . $pm_info['symbol_left'] . "';\n";
				}
				
				if (isset($pm_info['symbol_right']))	{
					echo "currencyInfoArray[".$id."]['symbol_right'] = '" . $pm_info['symbol_right'] . "';\n";
				}
			}
			
			foreach ($sc_scope_js_array as $id => $pm_info) {
				echo "storecreditInfoArray[".$id."] = new Array();\n";
				
				if (isset($pm_info['currency_code'])) {
					echo "storecreditInfoArray[".$id."]['currency_code'] = '" . $pm_info['currency_code'] . "';\n";
				}
				
				if (isset($pm_info['currency_ex']))	{
					echo "storecreditInfoArray[".$id."]['currency_ex'] = '" . $pm_info['currency_ex'] . "';\n";
				}
				
				if (isset($pm_info['symbol_left']))	{
					echo "storecreditInfoArray[".$id."]['symbol_left'] = '" . $pm_info['symbol_left'] . "';\n";
				}
				
				if (isset($pm_info['symbol_right']))	{
					echo "storecreditInfoArray[".$id."]['symbol_right'] = '" . $pm_info['symbol_right'] . "';\n";
				}
			}
			
			foreach ($pay_scope_js_array as $id => $pay_info) {
				if (isset($pay_info['payout_amount']))	echo "paymentAmountArray['".$id."'] = ".$pay_info['payout_amount'].";\n";
			}
		?>
			function update_selected_price(frmObj, pmID, checkName) {
				var total_sel_amount = 0;
				var any_box_selected = 0;
				var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
		 						? frmObj.elements[checkName+'[]']
		 						: "";
				var elts_cnt  = (typeof(elts.length) != 'undefined')
      							? elts.length
      							: 0;
      			
      			if (elts_cnt) {
			        for (var i=0; i < elts_cnt; i++) {
			            e = elts[i];
			            
			            if (e.type=='checkbox' && e.checked) {
			            	any_box_selected++;
			            	if (paymentAmountArray[e.value] != null) {
				            	total_sel_amount += paymentAmountArray[e.value];
			            	}
						}
			        } // end for
			    } else if (elts != '') {
			    	e = elts;
			        if (e.type=='checkbox' && e.checked) {
			        	any_box_selected++;
			        	if (paymentAmountArray[e.value] != null) {
			        		total_sel_amount += paymentAmountArray[e.value];
			            }
					}
			    }
			    
			    var total_amt_span_obj = DOMCall('total_sel_amt_' + pmID);
			    var total_sel_span_obj = DOMCall('total_sel_rec_' + pmID);
			    
			    if (any_box_selected > 0) {
					total_amt_span_obj.innerHTML = currency(total_sel_amount, currencyInfoArray[pmID]['symbol_left'], currencyInfoArray[pmID]['symbol_right'], 2);
					total_sel_span_obj.innerHTML = any_box_selected;
				} else {
					total_amt_span_obj.innerHTML = '';
					total_sel_span_obj.innerHTML = '';
				}
			}
			
			function update_selected_sc_price(frmObj, pmID, checkName) {
				var total_sel_amount = new Array();
				var currencies_info = new Array();
				var any_box_selected = 0;
				var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
		 						? frmObj.elements[checkName+'[]']
		 						: "";
				var elts_cnt  = (typeof(elts.length) != 'undefined')
      							? elts.length
      							: 0;
      			var sel_curr = '';
      			
      			if (elts_cnt) {
			        for (var i=0; i < elts_cnt; i++) {
			            e = elts[i];
			            
			            if (e.type=='checkbox' && e.checked) {
			            	any_box_selected++;
			            	if (paymentAmountArray[e.value] != null) {
			            		sel_curr = storecreditInfoArray[e.value]['currency_code'];
			            		if (typeof(total_sel_amount[sel_curr]) == 'undefined') {
			            			total_sel_amount[sel_curr] = 0;
			            		}
				            	total_sel_amount[sel_curr] += paymentAmountArray[e.value];
				            	
				            	if (typeof(currencies_info[sel_curr]) == 'undefined') {
				            		currencies_info[sel_curr] = new Array();
				            		currencies_info[sel_curr]['symbol_left'] = storecreditInfoArray[e.value]['symbol_left'];
				            		currencies_info[sel_curr]['symbol_right'] = storecreditInfoArray[e.value]['symbol_right'];
				            	}
			            	}
						}
			        } // end for
			    } else if (elts != '') {
			    	e = elts;
			        if (e.type=='checkbox' && e.checked) {
			        	any_box_selected++;
			        	if (paymentAmountArray[e.value] != null) {
			        		sel_curr = storecreditInfoArray[e.value]['currency_code'];
		            		if (typeof(total_sel_amount[sel_curr]) == 'undefined') {
		            			total_sel_amount[sel_curr] = 0;
		            		}
			        		total_sel_amount[sel_curr] += paymentAmountArray[e.value];
			        		
			            	if (typeof(currencies_info[sel_curr]) == 'undefined') {
			            		currencies_info[sel_curr] = new Array();
			            		currencies_info[sel_curr]['symbol_left'] = storecreditInfoArray[e.value]['symbol_left'];
			            		currencies_info[sel_curr]['symbol_right'] = storecreditInfoArray[e.value]['symbol_right'];
			            	}
			            }
					}
			    }
			    
			    var total_amt_span_obj = DOMCall('total_sel_amt_' + pmID);
			    var total_sel_span_obj = DOMCall('total_sel_rec_' + pmID);
			    
			    var total_str = '';
			    for (sel_curr in total_sel_amount) {
			    	total_str = total_str + currency(total_sel_amount[sel_curr], currencies_info[sel_curr]['symbol_left'], currencies_info[sel_curr]['symbol_right'], 2)+'<br>';
			    }
			    
			    if (any_box_selected > 0) {
					total_amt_span_obj.innerHTML = total_str;
					total_sel_span_obj.innerHTML = any_box_selected;
				} else {
					total_amt_span_obj.innerHTML = '';
					total_sel_span_obj.innerHTML = '';
				}
			}
			
			function refresh_payout_price(exRateObj, pmID, reqAmt, dec) {
				var payout_span_obj = DOMCall('span_payout_' + pmID);
				var latest_rate = exRateObj != null ? exRateObj.value : 0;
				
				if (payout_span_obj != null) {
					payout_span_obj.innerHTML = replace(currency_display(latest_rate * parseFloat(reqAmt), dec), ',', '');
				}
			}
			
			function confirmBatchAction(frmObj, frmStatus, checkName) {
				if (trim_str(frmObj.batch_action.value) == '') {
					alert('Please select your batch action!');
					return false;
				} else {
					var confirmation = confirm('Confirm to proceed with batch update?');
					
					if (confirmation == false) {
						frmObj.batch_action.selectedIndex = 0;
						return false;
					} else {
						return true;
					}
				}
			}
		//-->
		</script>
		<?
		$parsed_report_section_html .= "\n" . ob_get_contents();
		ob_end_clean();
		
	  	return $parsed_report_section_html;
	}
	
	function get_payment_aging_info($payment_id='', $for_pending_only=true, &$payment_aging_str, &$payment_cb_ratio_str) {
		$payment_aging_str = '';
		$payment_cb_ratio_str = '';
		
		if (tep_not_null($payment_id)) {
			$payment_select_sql = "	SELECT * FROM " . TABLE_STORE_PAYMENTS . "
									WHERE store_payments_id='" . tep_db_input($payment_id) . "'";
			$payment_result_sql = tep_db_query($payment_select_sql);
			
			if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
				if (($for_pending_only && $payment_row['store_payments_status'] == '1' && $payment_row['user_role'] == 'customers') || 
					(!$for_pending_only && $payment_row['user_role'] == 'customers')) {
					$buyback_search_sql = "	SELECT date_added 
											FROM " . TABLE_C2C_BUYBACK . "
											WHERE seller_id = " . tep_db_input($payment_row['user_id']) . "
												AND status = 3
											ORDER BY date_added
											LIMIT 1";
					$buyback_result_sql = tep_db_query($buyback_search_sql);
					if ($buyback_row = tep_db_fetch_array($buyback_result_sql)) {
						$payment_aging_str = floor((strtotime($payment_row['store_payments_date']) - strtotime($buyback_row['date_added'])) / (60*60*24*30));
						$payment_aging_str = ($payment_aging_str < 6) ? '<font color="red">'. $payment_aging_str . '</font>' : $payment_aging_str;
					} else {
						$payment_aging_str = '<font color="red">No Aging Info</font>';
					}
					
					$cb_count = $completed_count = $on_hold_count = 0;
					$cb_status_search_sql = "	SELECT count(orders_id) as cb_count 
												FROM " . TABLE_ORDERS . "
												WHERE customers_id = '" . tep_db_input($payment_row['user_id']) . "'
													AND orders_status = '3'
													AND orders_cb_status IS NOT NULL";
					$cb_status_result_sql = tep_db_query($cb_status_search_sql);
					if ($cb_status_row = tep_db_fetch_array($cb_status_result_sql)) {
						$cb_count = $cb_status_row['cb_count'];
					}
					
					$completed_search_sql = "	SELECT count(orders_id) as completed_count
												FROM " . TABLE_ORDERS . "
												WHERE customers_id = '" . tep_db_input($payment_row['user_id']) . "'
													AND orders_status = '3'";
					$complete_result_sql = tep_db_query($completed_search_sql);
					if ($complete_row = tep_db_fetch_array($complete_result_sql)) {
						$completed_count = $complete_row['completed_count'];
					}
					
					$on_hold_search_sql = "	SELECT count(orders_id) as on_hold_count
											FROM " . TABLE_ORDERS . "
											WHERE customers_id = '" . tep_db_input($payment_row['user_id']) . "'
												AND orders_status = '8'";
					$on_hold_result_sql = tep_db_query($on_hold_search_sql);
					if ($on_hold_row = tep_db_fetch_array($on_hold_result_sql)) {
						$on_hold_count = $on_hold_row['on_hold_count'];
					}
					
					$payment_cb_ratio_str = ($cb_count+$on_hold_count).'/'.($completed_count+$on_hold_count);
				} else if ($for_pending_only && $payment_row['store_payments_status'] != '1' && $payment_row['user_role'] == 'customers') {
					$payment_aging_str = '<div id="supp_aging_'.$payment_id.'">'.sprintf(LINK_PAYMENT_AGING_STATISTIC, "getSuppAgingStats('".$payment_id."');").'</div>';
					$payment_cb_ratio_str = '<div id="cb_order_'.$payment_id.'"></div>';
				} else if ($payment_row['user_role'] == 'supplier') {
					$payment_aging_str = 'No Aging Info';
					$payment_cb_ratio_str = '';
				}
			}
		}
	}

	function _compare_pass_completed_withdraw_method($user_id, $payment_method_id, $payment_input_fields_array, $payment_details_array) {
		/******************************************************
			Output:
				- true  : found pass success completed payment method transaction.
				- false : not found any pass success completed payment method transaction.
				- 2     : new payment withdraw request; no pass completed records found from sql.
		******************************************************/
		$completed_payments_array = array();
		$pass_pm_details_array = array();
		$source_pm_fields = array();
		$target_pm_fields = array();
		$field_array = array();
		
		// get the required payment method fields
		if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
			foreach ($payment_input_fields_array as $field_id => $field_info) {
				$source_pm_fields[$field_id] = strtolower(trim($payment_details_array[$field_id]['payment_methods_fields_value']));
				$field_array[] = $field_id;
			}
		} else {
			return false;
		}

		// SQL: 1. get all pass completed payment ID that using this criteria
		//         a. same user ID
		//         b. same payment method ID
		//         c. payment status is COMPLETED
		$store_payment_select_sql = " SELECT sp.store_payments_id 
	                                  FROM " . TABLE_STORE_PAYMENTS . " AS sp
	                                  WHERE sp.store_payments_status = 3
	                                  	AND sp.store_payments_methods_id = '" . tep_db_input($payment_method_id) . "'
	                                  	AND sp.user_id = '" . tep_db_input($user_id) . "'";
		$store_payment_result_sql = tep_db_query($store_payment_select_sql);
		
		while ($store_payment_row = tep_db_fetch_array($store_payment_result_sql)) {
			$completed_payments_array[] = $store_payment_row['store_payments_id'];
		}
		
		if (is_array($completed_payments_array) && count($completed_payments_array)) {
			// SQL: 1. get all pass completed payment method fields that using this criteria :
			//         a. pass completed payment id
			//         b. payment_methods_fields_id (this is aim to filter out the non-input fields)
			$payment_details_select_sql = " SELECT spd.store_payments_id,
                                           		spd.payment_methods_fields_id, 
                                           		spd.payment_methods_fields_title,
                                           		spd.payment_methods_fields_value
	                                    	FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd
		                                	WHERE spd.store_payments_id IN ( " . implode(",",$completed_payments_array) . " )
    		                            		AND spd.payment_methods_fields_id IN ( " . implode(",",$field_array) . " )
        		                        	ORDER BY spd.store_payments_id DESC, 
                                             	spd.payment_methods_fields_sort_order ASC";
			$payment_details_result_sql = tep_db_query($payment_details_select_sql);
			
			while ($payment_details_row = tep_db_fetch_array($payment_details_result_sql)) {
				$pass_pm_details_array[$payment_details_row['store_payments_id']][$payment_details_row['payment_methods_fields_id']] = $payment_details_row;
			}
		}
		
		// steps to convert record-based payment method field info into array-based for later easy comparison
		if (is_array($pass_pm_details_array) && count($pass_pm_details_array)) {
			$current_py_id = 0;
			$temp_array = array();
			foreach($pass_pm_details_array as $py_id => $pm_detail_row) {
				if ($current_py_id != 0 && $py_id != $current_py_id) {
					$target_pm_fields[$current_py_id] = $temp_array;
					$temp_array = array();
					foreach($pm_detail_row as $pm_field_id => $pm_fields_array) {
							$temp_array[$pm_field_id] = strtolower(trim($pm_fields_array['payment_methods_fields_value']));
					}
					$current_py_id = $py_id;
				} else {
					foreach($pm_detail_row as $pm_field_id => $pm_fields_array) {
							$temp_array[$pm_field_id] = strtolower(trim($pm_fields_array['payment_methods_fields_value']));
					}
					if ($current_py_id === 0) { $current_py_id = $py_id; }
				}
			}
			
			if (count($temp_array)) {
				$target_pm_fields[$current_py_id] = $temp_array;
			}
		}
		
		// compare pass completed payment method fields info with currently transacted info
		// by selected payment method id.
		if (is_array($target_pm_fields) && count($target_pm_fields)) {
			foreach($target_pm_fields as $pm_id => $target_field) {
				$diff_result = array_diff_assoc($source_pm_fields, $target_field);
				if (count($diff_result) === 0) {
					return true;
					break;
				}
			}
		}
		
		// when no matching completed payment method fields info, check any previously
		// completed with any payment method id.
		$completed_payment_select_sql = " 	SELECT sp.store_payments_id 
		                                	FROM " . TABLE_STORE_PAYMENTS . " AS sp
											WHERE sp.store_payments_status = 3
												AND sp.user_id = '" . tep_db_input($user_id) . "'";
		$completed_payment_result_sql = tep_db_query($completed_payment_select_sql);

		if (tep_db_num_rows($completed_payment_result_sql) > 0) {
			// return red color if found any completed payments other than selected payment method id
			return false;
		} else {
			// return orange color when nothing found
			return 2;
		}
	}

	function _payment_completion_process($pay_id, $payment_reference, $latest_ex_rate, &$messageStack) {
		global $currencies;
		
		$payment_current_info_select_sql = "SELECT store_payments_status, store_payments_request_currency, store_payments_after_fees_amount, store_payments_paid_currency, store_payments_methods_id 
											FROM " . TABLE_STORE_PAYMENTS . " 
											WHERE store_payments_id = '" . tep_db_input($pay_id) . "'";
		$payment_current_info_result_sql = tep_db_query($payment_current_info_select_sql);
		
		if ($payment_current_info_row = tep_db_fetch_array($payment_current_info_result_sql)) {
			if ($payment_current_info_row['store_payments_status'] == 2) {	// Only "Processing" payment can be updated
				// Insert exchange rate, update paid amount
				$payment_update_sql_data_array = array(	'store_payments_status' => 3,
														'store_payments_reference' => $payment_reference,
														'store_payments_last_modified' => 'now()' );
				
				if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
					if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
						$latest_ex_rate = (double)$latest_ex_rate;
						$actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];
						
						$payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
						$payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;
						
						$exRate_calculation_str = 	$currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' . 
													$latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
					} else {
						$processing_error = true;
						$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id), 'warning');
					}
				} else {
					$actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
					$payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;
					
					$exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
				}
	    		
				if (!$processing_error) {
					tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($pay_id) . "'");
					
          			$payment_received_by_str = '';
					
          			$estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
          															FROM " . TABLE_PAYMENT_METHODS . "
          															WHERE payment_methods_id = '".tep_db_input($payment_current_info_row['store_payments_methods_id'])."'";
					$estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
					
					if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
						$payment_received_by_timestamp = mktime(date("H"), (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));
						$payment_notice_due_timestamp = mktime(date("H")+24, (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));	// Hard code 24 for now..Need global configuration
						
						$payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
						$payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
                                                
                                                $is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                if($is_g2g == true){
                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                }
                                                else{
                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                }
//						$payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_3, $payment_received_by_date, $payment_notice_due_date);
					}
					
          			// Insert payment history
          			$payment_history_sql_data_array = array('store_payments_id' => $pay_id,
		    		           								'store_payments_status' => 3,
			    	 	          							'date_added' => 'now()',
			    	 	          							'payee_notified' => '1',
			    	 	          							'comments' => $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '') . $payment_received_by_str,
			    	 	          							'changed_by' => $this->identity_email,
			    	 	          							'changed_by_role' => 'admin'
							        	    	       							);
					tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
					
                                        if($is_g2g == true){
                                           $withdraw_datetime = c2c_invoice::get_store_payment_datetime($pay_id); 
                                        }
                                        
					$messageStack->add_session(sprintf(SUCCESS_PAYMENT_UPDATE, $pay_id), 'success');
					
					// Email to beneficiary
					$email_comments_str = $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '');
					
                                      //  $is_g2g = c2c_invoice::check_g2g_withdraw($payment_id);
                                      //  if ($is_g2g == true) {
                                       //     $withdraw_datetime = c2c_invoice::get_store_payment_datetime($payment_id); 
                                            if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                                                c2c_invoice::create_c2c_invoice_queue($pay_id, 'withdrawal', $withdraw_datetime);
                                            }
                                       // }
                                        $this->send_payment_status_email($pay_id, $email_comments_str);
				}
			} else {
				$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id), 'warning');
			}
		}
	}
	
	function do_batch_action($filename, $input_array, &$messageStack) {
		global $currencies, $messageToStack;
		
		$action_res_array = array('code' => 0);
		
		switch($input_array["batch_action"]) {
    		case "ExRate":
    			if (is_array($input_array["payments_batch"]) && count($input_array["payments_batch"])) {
    				foreach($input_array["payments_batch"] as $pay_id) {
    					if (isset($input_array["exchange_rate"][$pay_id])) {
    						// Only "Processing" payment can be update exchange rate
    						$currency_update_sql = "UPDATE " . TABLE_STORE_PAYMENTS . "
    												SET store_payments_paid_currency_value='" . tep_db_input($input_array["exchange_rate"][$pay_id]) . "'
    												WHERE store_payments_id = '" . tep_db_input($pay_id) . "' 
    													AND store_payments_status = 2 
    													AND store_payments_request_currency <> store_payments_paid_currency";
    						tep_db_query($currency_update_sql);
    					}
					}
					
					$messageStack->add_session(SUCCESS_PAYMENT_BATCH_ACTION_EXRATE, 'success');
					$action_res_array['code'] = '1';
    			}
    			
    			break;
			case "Process":
				$auto_complete_sc_withdraw_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_AUTO_CREDIT_WSC_TO_NRSC');
    			if (is_array($input_array["payments_batch"]) && count($input_array["payments_batch"])) {
    				foreach($input_array["payments_batch"] as $pay_id) {
    					$payment_current_info_select_sql = "SELECT store_payments_status, store_payments_request_currency, store_payments_paid_currency, store_payments_after_fees_amount, user_id, user_role, store_payments_methods_id, store_payments_paid_currency_value 
															FROM " . TABLE_STORE_PAYMENTS . " 
															WHERE store_payments_id = '" . tep_db_input($pay_id) . "'";
						$payment_current_info_result_sql = tep_db_query($payment_current_info_select_sql);
						
						if ($payment_current_info_row = tep_db_fetch_array($payment_current_info_result_sql)) {
							if ($payment_current_info_row['store_payments_status'] == 1) {	// Only "Pending" payment can be updated
								// check if payment method is system define, means withdraw into store credit
								if ($auto_complete_sc_withdraw_permission && $this->_is_payment_methods_type_mode_system_define($payment_current_info_row['store_payments_methods_id'])) {
									$processing_error = false;
									$user_inactive_flag = false;
									$user_account_id = $payment_current_info_row['user_id'];
									$user_role = $payment_current_info_row['user_role'];
									$third_user_info = $this->_get_withdraw_into_third_party_account_info($user_account_id, $user_role, $payment_current_info_row['store_payments_methods_id']);
									
									if ($third_user_info === false) {
										$processing_error = true;
										$user_inactive_flag = true;
										$res_message = sprintf(ERROR_PAYMENT_TRANSACTION_CUSTOMER_INACTIVE, $pay_id);
										$messageStack->add_session($res_message, 'warning');
									} else {
										if (is_array($third_user_info)) {
											$user_id = $third_user_info['user_id'];
											$user_role = $third_user_info['user_role'];
											$user_email = $third_user_info['user_email'];
										} else {
											$user_id = $payment_current_info_row['user_id'];
											$user_role = $payment_current_info_row['user_role'];
											$user_email = '';
										}
										
										if ($user_info_row = $this->_get_user_particulars($user_id, $user_role)) {
											if (!in_array('4', $user_info_row['flags'])) {
												$request_currency = $payment_current_info_row['store_payments_request_currency'];
												$request_currency_id = array_search($request_currency, $currencies->internal_currencies);
												$paid_currency = $payment_current_info_row['store_payments_paid_currency'];
												$paid_currency_id = array_search($paid_currency, $currencies->internal_currencies);
												$after_fees_amount = $payment_current_info_row['store_payments_after_fees_amount'];
												
												if ($user_role == 'supplier') {
													// since is from supplier, do not auto complete, but change status to processing
													$processing_error = true;
												} else if (($user_info_row['disable_withdrawal'] == '0') && ($this->_check_store_acc_got_balance($user_id, $user_role, $paid_currency, $store_balance_amount, $total_reserve_amount) === true)) {
													// Manual issue NR store credit
													$sc_object = new ms_store_credit($this->identity, $this->identity_email);
													
													$gv_user_id = $user_id;
													if ($user_role == 'supplier' && tep_not_null($user_email)) {
														// if user Id is taken from supplier table, we need to search through customer table with email to get the customer Id to get the correct store credit account
														$user_info_select_sql = "	SELECT customers_id, customers_email_address
																					FROM " . TABLE_CUSTOMERS . "
																					WHERE customers_email_address = '" . tep_db_input($user_email) . "'";
														$user_info_result_sql = tep_db_query($user_info_select_sql);
														if ($user_info_row = tep_db_fetch_array($user_info_result_sql)) {
															$gv_user_id = $user_info_row['customers_id'];
														}
													}

													$is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                    if($is_g2g == true){
                                                        $scArrayResult = g2g_serverless::getScBalance($gv_user_id);
                                                    }
                                                    else{
                                                        $scArrayResult = ms_store_credit::getScBalance($gv_user_id);
                                                    }

                                                    if ($scArrayResult) {
                                                        $to_customers_sc_currency_id = $currencies->get_id_by_code($scArrayResult['currency']);
                                                    } else { // if customer have no store credit account, use purchase currency
                                                        $to_customers_sc_currency_id = $paid_currency;
                                                    }
													
													if ($request_currency_id == $to_customers_sc_currency_id) {
														$req_ex_rate = 1;
														$paid_currency_id = $to_customers_sc_currency_id;
														$paid_currency = $currencies->get_code_by_id($paid_currency_id);
													} else {
														$req_ex_rate = $payment_current_info_row['store_payments_paid_currency_value'];
														if (is_null($req_ex_rate)) { // means withdraw into USD
															$req_ex_rate = 1;
															$paid_currency_id = $to_customers_sc_currency_id;
															$paid_currency = $currencies->get_code_by_id($paid_currency_id);
														}
													}
													
													$converted_from_qty = round($after_fees_amount * $req_ex_rate, 2);
													$sc_deliver_qty = $currencies->advance_currency_conversion($converted_from_qty, $paid_currency, $currencies->get_code_by_id($to_customers_sc_currency_id), true, 'sell');
													
													$sc_deliver_str = "Store credit for Payment ".$pay_id;

													$trans_array = array(
                                                        'orders_id' => (string) $pay_id,
                                                        'reference_id' => (string) $pay_id,
                                                        'customers_id' => $gv_user_id,
                                                        'customers_role' => $user_role,
                                                        'requesting_id' => $_SESSION['login_email_address'],
                                                        'requesting_role' => 'admin',
                                                        'amount' => floatval($sc_deliver_qty),
                                                        'total_amount' => floatval($sc_deliver_qty),
                                                        'currency' => $currencies->get_code_by_id($to_customers_sc_currency_id),
                                                        'activity' => LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW,
                                                        'message' => $sc_deliver_str,
                                                        'show_customer' => '1',
                                                    );

                                                    // Add Store Credit
                                                    $is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                    if($is_g2g == true){
                                                        
                                                        $trans_array_g2g = array(
															'request_id' => 'disbursement-co-' . $gv_user_id . '-' . $pay_id ,
                                                            'order_id' => (string)$pay_id,
                                                            'user_id' => $gv_user_id,
                                                            'user_role' => $user_role,
                                                            'requesting_id' => $_SESSION['login_email_address'],
                                                            'requesting_role' => 'admin',
                                                            'amount' => floatval($sc_deliver_qty),
                                                            'currency' => $currencies->get_code_by_id($to_customers_sc_currency_id),
                                                            'activity' => LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW,
                                                            'param_1' => 'show_customer',
                                                        );   
    
                                                        $sc_delivery_result = g2g_serverless::setScTransaction($trans_array_g2g, 'ADD_CREDIT');
                                                    }
                                                    else{
                                                        $sc_og_delivery_result = ms_store_credit::setScTransaction($trans_array, 'ADD_CREDIT');
                                                        $sc_delivery_result = $sc_og_delivery_result['request_id'];
                                                    }
                                                    
													if ($sc_delivery_result) {
														$payment_reference = tep_db_prepare_input($sc_delivery_result);
														$payment_reference_str = sprintf(TITLE_TRANS_STORE_CREDIT, $payment_reference);
													} else {
														$processing_error = true;
														if (count($messageToStack) > 0) {
															$err_msg = array();
															for ($err_i = 0, $err_n = sizeof($messageToStack); $err_i < $err_n; $err_i++) {
																$err_msg[] = $messageToStack[$err_i]['text'];
															}
															$res_message = implode("\n", $err_msg);
														}
													}
												} else {
													$processing_error = true;
													
													if ($user_info_row['disable_withdrawal'] == '1') {
														$res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id)."\n\r".ERROR_PAYMENT_TRANSACTION_DISABLED_WITHDRAWAL;
													} else {
														$res_message = sprintf(ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE, $pay_id)."\n\r"."\n\r".'Total available balance = '.$paid_currency.$store_balance_amount."\n\r";
													}
													$messageStack->add_session($res_message, 'warning');
												}
											} else {
												$processing_error = true;
												$flags_array = tep_get_user_flags();
												
												$res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id);
												foreach($user_info_row['flags'] as $flag) {
													switch ($flag) {
														case '4':
															$res_message .= "\n\rThis account has been flag as " . $flags_array[$flag]['user_flags_name'] . ".";
															break;
													}
												}
												$messageStack->add_session($res_message, 'warning');
											}
										} else {
											$processing_error = true;
											$res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id)."\n\r".sprintf(ERROR_PAYMENT_TRANSACTION_NO_ACCOUNT, $user_id.($user_email == '' ? '' : ' ('.$user_email.')'));
											$messageStack->add_session($res_message, 'warning');
										}
									}
									
									if (!$processing_error) {
										$payment_update_sql_data_array = array(	'store_payments_status' => '3',
																				'store_payments_paid_amount' => $converted_from_qty,
																				'store_payments_reference' => $payment_reference,
																				'store_payments_last_modified' => 'now()' );
										
										tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($pay_id) . "'");
					          			
					          			$payment_received_by_str = '';
					          			
					          			$estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
					          															FROM " . TABLE_PAYMENT_METHODS . "
					          															WHERE payment_methods_id = '".tep_db_input($payment_current_info_row['store_payments_methods_id'])."'";
					          			$estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
										
										if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
											$payment_received_by_timestamp = mktime(date("H"), (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));
											$payment_notice_due_timestamp = mktime(date("H")+24, (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));	// Hard code 24 for now..Need global configuration
											
											$payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
											$payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
											
                                                                                        $is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                                                        if($is_g2g == true){
                                                                                            $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                        }
                                                                                        else{
                                                                                            $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                        }
//											$payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_4, $payment_received_by_date, $payment_notice_due_date);
										}
										
					          			// Insert payment history
					          			$payment_history_sql_data_array = array('store_payments_id' => $pay_id,
							    		           								'store_payments_status' => '3',
								    	 	          							'date_added' => 'now()',
								    	 	          							'payee_notified' => '1',
								    	 	          							'comments' => $sc_deliver_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '') . $payment_received_by_str,
								    	 	          							'changed_by' => $this->identity_email,
								    	 	          							'changed_by_role' => 'admin'
							        	    	       							);
										tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
										
                                                                                if($is_g2g == true){
                                                                                    $withdraw_datetime = c2c_invoice::get_store_payment_datetime($pay_id); 
                                                                                }
                                        
										// Email to beneficiary
										$email_comments_str = $sc_deliver_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '');
                                                                                //Issue NRSC, from pending -> completed
                                                                                //if move completed status only
                                                                                //Insert to temp db and fire API to g2g lan-api to create g2g-invoice queue
                                                                               // $is_g2g = c2c_invoice::check_g2g_withdraw($payment_id);
                                                                                //if($is_g2g == true){
                                                                                    $withdraw_datetime = c2c_invoice::get_store_payment_datetime($pay_id); 
                                                                                    if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                                                                                        c2c_invoice::create_c2c_invoice_queue($pay_id, 'withdrawal', $withdraw_datetime);
                                                                                    }
                                                                                //}
										$this->send_payment_status_email($pay_id, $email_comments_str);
									}									
								} else { // normal gateway
									$user_id = $payment_current_info_row['user_id'];
									$user_role = $payment_current_info_row['user_role'];
									$user_info_row = $this->_get_user_particulars($user_id, $user_role);
									$paid_currency = $payment_current_info_row['store_payments_request_currency'];
									
									if (($user_info_row['disable_withdrawal'] == '0') && ($this->_check_store_acc_got_balance($user_id, $user_role, $paid_currency, $store_balance_amount, $total_reserve_amount) === true)) {
			    						$payment_update_sql_data_array = array(	'store_payments_status' => 2,
																				'store_payments_last_modified' => 'now()' );
										
										// Get latest exchange rate and automatically save it
										//if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
										//	$payment_update_sql_data_array['store_payments_paid_currency_value'] = $currencies->advance_currency_conversion_rate($payment_current_info_row['store_payments_request_currency'], $payment_current_info_row['store_payments_paid_currency']);
										//}
										
	          							tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($pay_id) . "'");
				          				
	          							// Insert payment history
	          							$payment_history_sql_data_array = array('store_payments_id' => $pay_id,
						    		    	       								'store_payments_status' => 2,
								 	 	    	      							'date_added' => 'now()',
	   	 	          															'payee_notified' => '1',
	   	 	          															'changed_by' => $this->identity_email,
							    	 	          								'changed_by_role' => 'admin'
			    	    	       												);
										tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
										
										$messageStack->add_session(sprintf(SUCCESS_PAYMENT_UPDATE, $pay_id), 'success');
										
										// Email to beneficiary
										//$this->send_payment_status_email($pay_id, '');
									} else {
										if ($user_info_row['disable_withdrawal'] == '1') {
											$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id)."\n\r".ERROR_PAYMENT_TRANSACTION_DISABLED_WITHDRAWAL, 'warning');
										} else {
  											$messageStack->add_session(sprintf(ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE, $pay_id)."\n\r"."\n\r".'Total available balance = '.$paid_currency.$store_balance_amount."\n\r", 'warning');
										}
									}
								}
   							} else {
	    						$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id), 'warning');
	    					}
	    				}
					}
					
					$action_res_array['code'] = '1';
    			}
    			
    			break;
    		case "Complete":
    			if (is_array($input_array["payments_batch"]) && count($input_array["payments_batch"])) {
    				
    				$paypal_store_payment_array = array();
    				$explude_store_payment_array = array();
    				$store_payment_pm_select_sql = "SELECT sp.store_payments_id, pg.payment_methods_filename, pm.payment_methods_id, pm.payment_methods_send_mass_payment 
													FROM " . TABLE_STORE_PAYMENTS . " AS sp 
													LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
														ON sp.store_payments_methods_id = pm.payment_methods_id
													LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
														ON pg.payment_methods_id = pm.payment_methods_parent_id
													WHERE store_payments_id IN ('" . implode("','", (isset($input_array["payments_batch"]) && count($input_array["payments_batch"]) ? $input_array["payments_batch"] : array())) . "')";
					$store_payment_pm_result_sql = tep_db_query($store_payment_pm_select_sql);
					while ($store_payment_pm_row = tep_db_fetch_array($store_payment_pm_result_sql)) {
						switch ($store_payment_pm_row['payment_methods_filename']) {
							case 'paypal.php':
								if ($store_payment_pm_row['payment_methods_send_mass_payment']==1) {
									$paypal_store_payment_array[$store_payment_pm_row['payment_methods_id']][] = $store_payment_pm_row['store_payments_id'];
									$explude_store_payment_array[] = $store_payment_pm_row['store_payments_id'];
								}
								break;
							case 'paypalEC.php':
								if ($store_payment_pm_row['payment_methods_send_mass_payment']==1) {
									$paypalEC_store_payment_array[$store_payment_pm_row['payment_methods_id']][] = $store_payment_pm_row['store_payments_id'];
									$explude_store_payment_array[] = $store_payment_pm_row['store_payments_id'];
								}
								break;
                                                        //disbursement-g2g
                                                        case 'pipwavePG.php':
                                                        if ($store_payment_pm_row['payment_methods_send_mass_payment']==1) {
                                                                $pipwavePG_store_payment_array[$store_payment_pm_row['payment_methods_id']][] = $store_payment_pm_row['store_payments_id'];
                                                                $explude_store_payment_array[] = $store_payment_pm_row['store_payments_id'];
                                                        }
                                                        break;
						}
					}
    				
    				// Paypal
    				if (count($paypal_store_payment_array)) {
						require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPAL);
						foreach ($paypal_store_payment_array as $paypal_payment_methods_id_loop => $paypal_payment_methods_data_loop) {
							$paypal_obj = new paypal($paypal_payment_methods_id_loop);
							$paypal_obj->mass_pay($paypal_payment_methods_data_loop);
						}
					}
    				// PaypalEC
    				if (count($paypalEC_store_payment_array)) {
						require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPALEC);
						foreach ($paypalEC_store_payment_array as $paypal_payment_methods_id_loop => $paypal_payment_methods_data_loop) {
							$paypal_obj = new paypal($paypal_payment_methods_id_loop);
							$paypal_obj->massPay($paypal_payment_methods_data_loop);
						}
					}
                                //disbursement-g2g
                                if (count($pipwavePG_store_payment_array)) {
                                                $pipwavePG_result_array = array();
						require_once(DIR_FS_CATALOG_MODULES . 'payment/pipwavePG.php');
						foreach ($pipwavePG_store_payment_array as $pipwavePG_payment_methods_id_loop => $pipwavePG_payment_methods_data_loop) {
							$pipwave_obj = new pipwavePG($pipwavePG_payment_methods_id_loop);
							$pipwavePG_result_array = $pipwave_obj->massPay($pipwavePG_payment_methods_data_loop);
                                                         foreach ($pipwavePG_result_array as $result_data_loop_pm_id => $result_data_loop) {
                                                            if ($result_data_loop != 1) {
                                                                $processing_error = true;
                                                                $messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $result_data_loop_pm_id) . ": " . $result_data_loop, 'warning');
                                                            }
                                                        }
						}
					}        
    				
    				// Others from paypal
    				foreach($input_array["payments_batch"] as $pay_id) {
    					if (!in_array($pay_id, $explude_store_payment_array)) {
	    					if ((isset($input_array["payment_reference"][$pay_id]) && tep_not_null($input_array["payment_reference"][$pay_id]))) {	// Only complete a payment if there is payment reference
	    						$processing_error = false;
								$exRate_calculation_str = '';
								$payment_reference = tep_db_prepare_input((isset($input_array["payment_reference"][$pay_id])?$input_array["payment_reference"][$pay_id]:''));
								$latest_ex_rate = tep_db_prepare_input($input_array["exchange_rate"][$pay_id]);
								
	    						$payment_current_info_select_sql = "SELECT store_payments_status, store_payments_request_currency, store_payments_after_fees_amount, store_payments_paid_currency, store_payments_methods_id 
																	FROM " . TABLE_STORE_PAYMENTS . " 
																	WHERE store_payments_id = '" . tep_db_input($pay_id) . "'";
								$payment_current_info_result_sql = tep_db_query($payment_current_info_select_sql);
								
								if ($payment_current_info_row = tep_db_fetch_array($payment_current_info_result_sql)) {
		    						if ($payment_current_info_row['store_payments_status'] == 2) {	// Only "Processing" payment can be updated
		    							// Insert exchange rate, update paid amount
										$payment_update_sql_data_array = array(	'store_payments_status' => 3,
																				'store_payments_reference' => $payment_reference,
																				'store_payments_last_modified' => 'now()' );
										
										if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
											if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
												$latest_ex_rate = (double)$latest_ex_rate;
												$actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];
												
												$payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
												$payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;
												
												$exRate_calculation_str = 	$currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' . 
																			$latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
											} else {
												$processing_error = true;
												$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id), 'warning');
											}
										} else {
											$actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
											$payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;
											
											$exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
										}
		    							
		    							if (!$processing_error) {
											tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($pay_id) . "'");
						          			
						          			$payment_received_by_str = '';
						          			
						          			$estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
						          															FROM " . TABLE_PAYMENT_METHODS . "
						          															WHERE payment_methods_id = '".tep_db_input($payment_current_info_row['store_payments_methods_id'])."'";
						          			$estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
											
											if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
												$payment_received_by_timestamp = mktime(date("H"), (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));
												$payment_notice_due_timestamp = mktime(date("H")+24, (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));	// Hard code 24 for now..Need global configuration
												
												$payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
												$payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
												
                                                                                                $is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                                                                if($is_g2g == true){
                                                                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                                }
                                                                                                else{
                                                                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                                }
//												$payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_5, $payment_received_by_date, $payment_notice_due_date);
											}
											
						          			// Insert payment history
						          			$payment_history_sql_data_array = array('store_payments_id' => $pay_id,
								    		           								'store_payments_status' => 3,
									    	 	          							'date_added' => 'now()',
									    	 	          							'payee_notified' => '1',
									    	 	          							'comments' => $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '') . $payment_received_by_str,
									    	 	          							'changed_by' => $this->identity_email,
									    	 	          							'changed_by_role' => 'admin'
								        	    	       							);
											tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
											
                                        
											$messageStack->add_session(sprintf(SUCCESS_PAYMENT_UPDATE, $pay_id), 'success');
											
											// Email to beneficiary
											$email_comments_str = $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '');
                                                                                        //if move completed status only
                                                                                        //Insert to temp db and fire API to g2g lan-api to create g2g-invoice queue
                                                                                        
                                                                                        $is_g2g = c2c_invoice::check_g2g_withdraw($pay_id);
                                                                                        if($is_g2g == true){
                                                                                            $withdraw_datetime = c2c_invoice::get_store_payment_datetime($pay_id); 
                                                                                            if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                                                                                                c2c_invoice::create_c2c_invoice_queue($pay_id, 'withdrawal', $withdraw_datetime);
                                                                                            }
                                                                                         }
											$this->send_payment_status_email($pay_id, $email_comments_str);
										}
		    						} else {
		    							$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id), 'warning');
		    						}
		    					}
	    					} else {
	    						$messageStack->add_session(sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $pay_id)."\n\r".ERROR_PAYMENT_TRANSACTION_MISSING_PAYMENT_REFERENCE, 'warning');
	    					}
	    				} 
					}
					
					$action_res_array['code'] = '1';
    			}
    			
    			break;
    		case "MassPayCSV":
				$this->_prepare_mass_payment_csv($input_array["pm_id"], $input_array["payments_batch"], $messageStack);
    			
    			break;
    		case "ImportMassPayCSV":
				$this->_import_mass_payment_csv($input_array["pm_id"], $input_array, $messageStack);
    			
    			break;
		}
		
		return $action_res_array;
	}
	
	function edit_payment($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id, $payment_manual_complete_payment_permission;
		
		$edit_payment_html = '';
		$this->_get_payment_info($input_array['payID']);
		
		// Check for user is allow to process this payment
		$pm_allow_process = $this->_is_allow_to_send_payment_methods($this->payment_info["payment_methods_id"]);
		// Check for user is allow to update this payment
		$pm_allow_update = $this->_is_allow_to_update_payment_methods($this->payment_info["payment_methods_id"]);
		// Check for user is allow to cancel this payment
		$pm_allow_cancel = $this->_is_allow_to_cancel_payment_methods($this->payment_info["payment_methods_id"]);
		
		$payment_status_array = array();
		$payment_status_select_sql = "	SELECT store_payments_status_id, store_payments_status_name 
										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
										WHERE language_id = '" . (int)$languages_id . "' 
										ORDER BY store_payments_status_sort_order";
		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
		while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
			$payment_status_array[$payment_status_row['store_payments_status_id']] = $payment_status_row['store_payments_status_name'];
		}
		
		ob_start();
  		
  		$form_name = 'edit_payment_form';
  		$form_param = tep_get_all_get_params(array('action')) . 'action=update_payment';
  		$form_cancel_param = tep_get_all_get_params(array('action')) . 'action=cancel_payment';
		?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
			<tr>
				<td width="100%">
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
    						<td valign="top" width="80%">
    							<table width="100%" border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" width="15%"><b><?=ENTRY_PAYMENT_ID?></b></td>
										<td class="main"><b><?=$input_array['payID']?></b></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_PAYMENT_STATUS?></b></td>
										<td class="main"><?=$this->info['status_name']?></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_PAYMENT_DATE_TIME?></b></td>
										<td class="main"><?=tep_datetime_short($this->info["payments_date"], PREFERRED_DATE_TIME_FORMAT)?></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
										<td class="main"><?=tep_datetime_short($this->info["last_modified"], PREFERRED_DATE_TIME_FORMAT)?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
			<tr>
				<td><?=tep_draw_separator()?></td>
			</tr>
			<tr>
				<td>
					<table width="100%" border="0" cellspacing="0" cellpadding="2">
  						<tr>
        					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_BENEFICIARY_INFO?></b></td>
      					</tr>
      					<tr>
							<td valign="top">
								<table width="100%" border="0" cellspacing="0" cellpadding="2">
      								<tr>
      									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_BENEFICIARY?></b></td>
										<td class="main">
										<?	echo tep_output_string_protected($this->beneficiary['firstname'] . ' ' . $this->beneficiary['lastname']) . '&nbsp;['. $this->beneficiary['role'] . ']'; ?>
										</td>
      								</tr>
      								<tr>
      									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_BENEFICIARY_EMAIL?></b></td>
										<td class="main"><?=$this->beneficiary['email_address']?></td>
      								</tr>
      								<tr>
      									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_TELEPHONE?></b></td>
										<td class="main"><?=$this->beneficiary['telephone']?></td>
      								</tr>
      								<tr>
      									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_MOBILE?></b></td>
										<td class="main"><?=$this->beneficiary['mobile']?></td>
      								</tr>
      							</table>
      						</td>
      				</table>
      			</td>
      		</tr>
      		<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
			<tr>
				<td><?=tep_draw_separator()?></td>
			</tr>
      		<tr>
				<td>
					<table width="100%" border="0" cellspacing="0" cellpadding="2">
  						<tr>
        					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_INFO?></b></td>
      					</tr>
      					<tr>
    						<td valign="top">
    							<table width="100%" border="0" cellspacing="0" cellpadding="2">
	              					<tr>
    									<td valign="top">
    										<table width="100%" border="0" cellspacing="0" cellpadding="2">
    											<tr>
	                								<td width="20%" class="main" valign="top"><b><?=ENTRY_PAYMENT_METHOD?></b></td>
	                								<td class="main"><?=$this->payment_info["payment_methods_alias"]?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_PAYMENT_REFERENCE?></b></td>
	                								<td class="main"><?=$this->payment_info["reference"]?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_PAYMENT_WITHDRAW_AMOUNT?></b></td>
	                								<td class="main"><?=$currencies->format($this->payment_info["request_amount"], false, $this->payment_info["request_currency"])?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_PAYMENT_PAY_AMOUNT?></b></td>
	                								<td class="main"><?=$currencies->format($this->payment_info["paid_amount"], false, $this->payment_info["paid_currency"])?></td>
	              								</tr>
												<? if (count($this->trans_info['PO']) > 0) { ?>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_PAYMENT_PO_NUMBER?></b></td>
	                								<td class="main">
														<?
														foreach ($this->trans_info['PO'] as $po_info) {
                                                                                                                    $is_dtu_po = dtu_payment::is_dtu_payment($po_info['trans_id']);
                                                                                                                    $is_api_po = api_replenish_payment::is_po_payment($po_info['trans_id']);
                                                            $is_cdk_po = consignment_payment::is_cdk_payment($po_info['trans_id']);
                                                                                                                    if($is_dtu_po > 0) {
                                                                                                                        echo '<a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.$po_info['trans_id'].'&subaction=edit_dtu&selected_box=po', 'NONSSL') . '" target="_blank">'.$po_info['trans_text'].'</a><br />';
                                                                                                                    } else if($is_api_po > 0) {
                                                                                                                        echo '<a href="' . tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'po_id='.$po_info['trans_id'].'&subaction=edit_po&selected_box=po', 'NONSSL') . '" target="_blank">'.$po_info['trans_text'].'</a><br />';
                                                            } else if($is_cdk_po > 0) {
                                                                echo '<a href="' . tep_href_link(FILENAME_CDK_PAYMENT, 'po_id='.$po_info['trans_id'].'&subaction=edit_cdk&selected_box=po', 'NONSSL') . '" target="_blank">'.$po_info['trans_text'].'</a><br />';
                                                                                                                    } else {
                                                                                                                        echo '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'po_id='.$po_info['trans_id'].'&subaction=edit_po&selected_box=po', 'NONSSL') . '" target="_blank">'.$po_info['trans_text'].'</a><br />';
                                                                                                                    }
														}
														?>
													</td>
	              								</tr>
												<? } ?>
	              							</table>
	              						</td>
	              					</tr>
	              				</table>
	              			</td>
	              		</tr>
      				</table>
      			</td>
      		</tr>
      		<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
			<tr>
    			<td><?=tep_draw_separator()?></td>
  			</tr>
  			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
  			<tr>
		  		<td class="main">
					<table border="1" cellspacing="0" cellpadding="5">
  						<tr>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_BENEFICIARY_NOTIFIED?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_CHANGED_BY?></b></td>
  						</tr>
		<?
		$payment_history_select_sql = "	SELECT * 
									FROM " . TABLE_STORE_PAYMENTS_HISTORY . " 
									WHERE store_payments_id = '" . $input_array['payID'] . "' 
									ORDER BY date_added";
		$payment_history_result_sql = tep_db_query($payment_history_select_sql);
		while ($payment_history_row = tep_db_fetch_array($payment_history_result_sql)) {
			$formatted_date_comment_added = tep_datetime_short($payment_history_row["date_added"], PREFERRED_DATE_TIME_FORMAT);
			$img_str = ($payment_history_row['payee_notified'] == '1') ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
			?>
							<tr>
	    						<td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
	    						<td class="smallText" align="center"><?=$img_str?></td>
	    						<td class="smallText" align="center"><?=isset($payment_status_array[$payment_history_row['store_payments_status']]) ? $payment_status_array[$payment_history_row['store_payments_status']] : '--'?></td>
	    						<td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $payment_history_row['comments']))?>&nbsp;</td>
	    						<td class="smallText" align="center"><?=nl2br(tep_db_output($payment_history_row['changed_by']))?>&nbsp;</td>
	  						</tr>
		<?	} ?>
					</table>
  				</td>
		  	</tr>
		  	<tr>
		  		<td>
		<?	echo tep_draw_form($form_name, $filename, $form_param, 'post', '');
			echo tep_draw_hidden_field('status_DB_prev', $this->info['status']);
			
			if (isset($_SESSION[$session_name])) {
				$back_btn_url = tep_href_link($filename, 'action=show_report&cont=1');
			} else {
				$back_btn_url = tep_href_link($filename);
			}
		?>
		  			<table border="0" cellspacing="0" cellpadding="2" width="100%">
		  				<tr>
		  					<td class="main" colspan="4"><b><? if($pm_allow_update) {echo ENTRY_PAYMENT_REMARK;} ?></b></td>
		  				</tr>	
		  				<tr>
		  					<td class="main" colspan="3"><? if($pm_allow_update) {echo tep_draw_textarea_field('admin_comment', 'soft', '60', '5');} ?></td>
		  					<td class="main">&nbsp;</td>
		  				</tr>
		  				<tr>
		  					<td width="135px" class="main"><b><? if($pm_allow_update) {echo ENTRY_PAYMENT_NOTIFY_BENEFICIARY;} ?></b></td>
		  					<td class="main"><? if($pm_allow_update) {echo tep_draw_checkbox_field('notify', '1', false, '', 'id=notify');} ?></td>
		  					<td align="right" class="main">
		  						<?
		  						if ($pm_allow_update) {
		  							echo tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="PaymentUpdateBtn"', 'inputButton');
		  						}
	  							?>
		  					</td>
		  					<td align="left" class="main" width="100%">&nbsp;&nbsp;
							<?
							if ($pm_allow_process) {
								switch($this->info['status']) {
									case '1':	// Pending
										if ($this->beneficiary['role'] == 'customers' && $this->_is_payment_methods_type_mode_system_define($this->payment_info['payment_methods_id'])) {
											//$action_button_html = tep_button('Issue NRSC', 'Process this payment', '', ' name="ProcessBtn_'.$input_array['payID'].'" onClick="updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'1\', \'3\', \''.$this->payment_info['payment_methods_id'].'\');" ', 'greenButton').'&nbsp;&nbsp;';
											$action_button_html = '&nbsp;';
										} else {
											$action_button_html = tep_button('Process', 'Process this payment', '', ' name="ProcessBtn_'.$input_array['payID'].'" onClick="updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'1\', \'2\', \''.$this->payment_info['payment_methods_id'].'\');" ', 'greenButton').'&nbsp;&nbsp;';
										}
										
										break;
									case '2':	// Processing
										if ($this->_is_payment_methods_type_mode_system_define($this->payment_info['payment_methods_id']) == '1') {
											$payment_method_info_select_sql = "	SELECT pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_mass_payment AS mass_payment, '' AS code, pmt.payment_methods_types_system_define, pg.payment_methods_filename
																				FROM " . TABLE_PAYMENT_METHODS . " AS pm 
																				INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
																					ON pm.payment_methods_types_id=pmt.payment_methods_types_id 
																				INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
																					ON pm.payment_methods_parent_id = pg.payment_methods_id
																				WHERE pm.payment_methods_id = '" . tep_db_input($this->payment_info['payment_methods_id']) . "' ";
										} else {
											$payment_method_info_select_sql = "	SELECT pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_mass_payment AS mass_payment, cur.code, pmt.payment_methods_types_system_define, pg.payment_methods_filename 
																				FROM " . TABLE_PAYMENT_METHODS . " AS pm 
																				INNER JOIN " . TABLE_CURRENCIES . " AS cur 
																					ON pm.payment_methods_send_currency=cur.currencies_id 
																				INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
																					ON pm.payment_methods_types_id=pmt.payment_methods_types_id 
																				INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
																					ON pm.payment_methods_parent_id = pg.payment_methods_id
																				WHERE pm.payment_methods_id = '" . tep_db_input($this->payment_info['payment_methods_id']) . "' ";
										}
										
										$payment_method_info_result_sql = tep_db_query($payment_method_info_select_sql);
										$payment_method_info_row = tep_db_fetch_array($payment_method_info_result_sql);
										if ($payment_method_info_row['payment_methods_filename']=='paypal.php' && $payment_method_info_row['mass_payment']) {
											if ($this->payment_info['store_payments_lock']) {
												$action_button_html = '&nbsp;Processing&nbsp;';
											} else {
												$action_button_html = tep_button('Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\'); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
										
											if ($payment_manual_complete_payment_permission) {
												$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\', 1); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
										} else if ($payment_method_info_row['payment_methods_filename']=='paypalEC.php' && $payment_method_info_row['mass_payment']) { 
											if ($this->payment_info['store_payments_lock']) {
												$action_button_html = '&nbsp;Processing&nbsp;';
											} else {
												$action_button_html = tep_button('PaypalEC Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\'); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
						
											if ($payment_manual_complete_payment_permission) {
												$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\', 1); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
										} else if ($payment_method_info_row['payment_methods_filename']=='pipwavePG.php' && $payment_method_info_row['mass_payment']) { 
                                                                                        //disbursement-g2g - edit page button
											if ($this->payment_info['store_payments_lock']) {
												$action_button_html = '&nbsp;Processing&nbsp;';
											} else {
												$action_button_html = tep_button('Payment API Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\'); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
						
											if ($payment_manual_complete_payment_permission) {
												$action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\', 1); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
											}
										} else {
											$action_button_html = tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$input_array['payID'].'" onClick=" if (confirm(\''.JS_ALERT_PAYMENT_CONFIRM_UPDATE.'\') != \'0\') { updateEditPayment(\''.FILENAME_PAYMENT.'\', this, \''.$input_array['payID'].'\', \'2\', \'3\', \''.$this->payment_info['payment_methods_id'].'\'); } else { return false; }" ', 'greenButton').'&nbsp;&nbsp;';
										}
										
										break;
								}
								echo $action_button_html;
		  					}
		  					if ($pm_allow_cancel) {
		  						if ($this->info['status'] == '1' || $this->info['status'] == '2') {
		  							echo tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="PaymentCancelBtn" onClick="if (confirm(\'Are you sure to cancel this payment?\') != \'0\') { return true; } else { return false; }"', 'redButton');
		  						}
		  					}
		  					?>
		  					</td>
		  				</tr>
		  				<tr>
		  					<td class="main" colspan="4">&nbsp;</td>
		  				</tr>
		  				<tr>
		  					<td align="left" class="main" colspan="5">
		  						<?=tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, $back_btn_url, '', 'inputButton')?>
		  					</td>
		  				</tr>
		  			</table>
		  		</form>
		  		</td>
		  	</tr>
		</table>
		<?
		$edit_payment_html .= "\n" . ob_get_contents();
		ob_end_clean();
		
	  	return $edit_payment_html;
	}
	
	function update_payment($filename, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		if (isset($input_array['payID'])) {
			$payment_select_sql = "	SELECT store_payments_id, store_payments_status 
									FROM " . TABLE_STORE_PAYMENTS . "
									WHERE store_payments_id='".tep_db_input($input_array['payID'])."' ";
			$payment_result_sql = tep_db_query($payment_select_sql);
			
			if ($payment_row = tep_db_fetch_array($payment_result_sql)) {	// This payment exists
				if ($input_array["status_DB_prev"] != $payment_row['store_payments_status']) {
	    			$messageStack->add_session(WARNING_PAYMENT_UPDATED_BY_SOMEONE, 'warning');
	    		} else {
					if (isset($input_array['PaymentUpdateBtn'])) {	// Update payment remarks
						if (tep_not_null($input_array['admin_comment'])) {
							$payment_history_data_array = array('store_payments_id' => $input_array['payID'],
					  											'store_payments_status' => 0,
					  											'date_added' => 'now()',
																'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
									                            'comments' => tep_db_prepare_input($input_array['admin_comment']),
									                            'changed_by' => $this->identity_email,
									                            'changed_by_role' => 'admin'
								                           	);
						    tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_data_array);
						    
						    if (isset($input_array['notify']) && $input_array['notify'] > 0) {
						    	$this->send_payment_status_email($input_array['payID'], $input_array['admin_comment']);
						    	
						    	// update payment read
                    			$update_payment_read_sql = "UPDATE " . TABLE_STORE_PAYMENTS . " 
                    			                            SET store_payments_read_mode = 0 
                    			                            WHERE store_payments_id = '" . $input_array['payID'] . "'";
                    			tep_db_query($update_payment_read_sql);
							}
							
							$messageStack->add_session(sprintf(SUCCESS_PAYMENT_UPDATE, $input_array['payID']), 'success');
						}
					} else if (isset($input_array['PaymentCancelBtn'])) {	// Cancel this payment
						if ($payment_row['store_payments_status'] == '1' ||
							$payment_row['store_payments_status'] == '2') {
							$this->_get_payment_info($input_array['payID']);
					    	
							// check transaction table payment
							$pay_trans_select_sql = "SELECT DISTINCT store_payments_reimburse_table FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " WHERE store_payments_id='" . tep_db_input($input_array['payID']) . "'";
							$pay_trans_result_sql = tep_db_query($pay_trans_select_sql);
							if (tep_db_num_rows($pay_trans_result_sql) > 0) {
								$pay_trans_row = tep_db_fetch_array($pay_trans_result_sql);
								// If this payment is for Purchase Orders, cancel paid status for related PO
								if ($pay_trans_row['store_payments_reimburse_table'] == TABLE_PURCHASE_ORDERS) {
									$po_suppliers_obj = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
									
									// Update live credit balance
									$update_powsc_info = array(array(	'field_name' => 'store_account_po_wsc',
																		'operator' => '+',
																		'value' => $this->payment_info["request_amount"])
									);
									$new_powsc_balance = $po_suppliers_obj->_set_po_supplier_po_wsc_balance($this->beneficiary['id'], 'customers', $this->payment_info["request_currency"], $update_powsc_info);
									
									// update PO status
									$po_suppliers_obj->cancel_po_payment($input_array['payID'], $messageStack);
									
									$action_result = true;
								}
							} else {
								$cancel_info = array(	'user_id' => $this->beneficiary['id'],
														'user_role' => $this->beneficiary['role'],
														'cur_acc' => $this->payment_info["request_currency"],
														'add_amount' => $this->payment_info["request_amount"],
														);
								$action_result = $this->miscellaneous_add_amount($cancel_info, sprintf(LOG_PAYMENT_CANCELLATION, $input_array['payID']), (isset($input_array['notify']) && $input_array['notify'] > 0 ? $input_array['admin_comment'] : ''), $messageStack);
							}
							
							// If success, add payment history and update payment status
							if ($action_result) {
								// Cancel this payment
								$payment_status_update_data_array = array(	'store_payments_status' => 4,
																			'store_payments_last_modified' => 'now()' );
								if (isset($input_array['notify']) && $input_array['notify'] > 0) {
								    $payment_status_update_data_array['store_payments_read_mode'] = '0';
								}
			          			tep_db_perform(TABLE_STORE_PAYMENTS, $payment_status_update_data_array, 'update', "store_payments_id = '" . tep_db_input($input_array['payID']) . "'");
			          			
			          			// Insert payment history
			          			$payment_history_data_array = array('store_payments_id' => $input_array['payID'],
						  											'store_payments_status' => 4,
						  											'date_added' => 'now()',
																	'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
										                            'comments' => tep_db_prepare_input($input_array['admin_comment']),
										                            'changed_by' => $this->identity_email,
										                            'changed_by_role' => 'admin'
									                           	);
							    tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_data_array);
                                                            
							    if (isset($input_array['notify']) && $input_array['notify'] > 0) {
							    	$this->send_payment_status_email($input_array['payID'], $input_array['admin_comment']);
								}
								
								$messageStack->add_session(sprintf(SUCCESS_PAYMENT_CANCELLATION, $input_array['payID']), 'success');
							}
						}
					}
				}
			}
		}
	}
	
	function _get_payment_info($payment_id) {
		global $languages_id;
		
		$payment_info_select_sql = "SELECT * 
									FROM " . TABLE_STORE_PAYMENTS . "
									WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
		
		if ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {
			$payment_status_select_sql = "	SELECT store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
	  										WHERE store_payments_status_id = '" . tep_db_input($payment_info_row['store_payments_status']) . "' 
	  											AND language_id='" . $languages_id  . "'";
	  		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
	  		$payment_status_row = tep_db_fetch_array($payment_status_result_sql);
			
			$payment_trans_select_sql = "	SELECT store_payments_reimburse_id, store_payments_reimburse_table 
	  										FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " 
	  										WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
	  		$payment_trans_result_sql = tep_db_query($payment_trans_select_sql);
	  		while ($payment_trans_row = tep_db_fetch_array($payment_trans_result_sql)) {
				if ($payment_trans_row['store_payments_reimburse_table'] == TABLE_PURCHASE_ORDERS) {
					if (!isset($this->trans_info['PO'])) { $this->trans_info['PO'] = array(); }
					
					$po_ref_select_sql = "SELECT purchase_orders_id, purchase_orders_ref_id FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id='".$payment_trans_row['store_payments_reimburse_id']."'";
					$po_ref_result_sql = tep_db_query($po_ref_select_sql);
					if ($po_ref_row = tep_db_fetch_array($po_ref_result_sql)) {
						$this->trans_info['PO'][] = array('trans_id' => $po_ref_row['purchase_orders_id'], 'trans_text' => $po_ref_row['purchase_orders_ref_id']);
					}
				}
			}
	  		
			$this->info = array('pay_id' => $payment_id,
								'payments_date' => $payment_info_row['store_payments_date'],
								'status' => $payment_info_row['store_payments_status'],
								'status_name' => $payment_status_row['store_payments_status_name'],
								'last_modified' => $payment_info_row['store_payments_last_modified']
								);
			
			$this->beneficiary = array(	'id' => $payment_info_row['user_id'],
	      								'role' => $payment_info_row['user_role'],
	      								'firstname' => $payment_info_row['user_firstname'],
	      								'lastname' => $payment_info_row['user_lastname'],
	      								'email_address' => $payment_info_row['user_email_address'],
	      								'telephone' => $payment_info_row['user_country_international_dialing_code'].$payment_info_row['user_telephone'],
	      								'mobile' => $payment_info_row['user_mobile']
	      								);
	      	
	      	$this->payment_info = array('request_currency' => $payment_info_row['store_payments_request_currency'],
	      								'request_amount' => $payment_info_row['store_payments_request_amount'],
	      								'fees' => $payment_info_row['store_payments_fees'],
	      								'after_fees_amount' => $payment_info_row['store_payments_after_fees_amount'],
	      								'paid_currency' => $payment_info_row['store_payments_paid_currency'],
	      								'exchange_rate' => $payment_info_row['store_payments_paid_currency_value'],
	      								'paid_amount' => $payment_info_row['store_payments_paid_amount'],
	      								'reference' => $payment_info_row['store_payments_reference'],
	      								'payment_methods_id' => $payment_info_row['store_payments_methods_id'],
                                        'payment_methods_name' => $payment_info_row['store_payments_methods_name'],
	      								'account_book_id' => $payment_info_row['store_payment_account_book_id'],
	      								'payment_methods_alias' => $payment_info_row['user_payment_methods_alias'],
	      								'fees_calculation' => $payment_info_row['store_payments_fees_calculation'],
										'store_payments_lock' => $payment_info_row['store_payments_lock']
	      								);
		}
	}
	
	function get_user_account_balance($user_id, $user_role) {
		$account_array = array();
		
		$store_account_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($user_id) . "' 
											AND user_role = '" . tep_db_input($user_role) . "' 
										ORDER BY store_account_balance_currency";
		$store_account_result_sql = tep_db_query($store_account_select_sql);
		
		while ($store_account_row = tep_db_fetch_array($store_account_result_sql)) {
			$account_array[] = array('id' => $store_account_row['store_account_balance_currency'], 'text' => $store_account_row['store_account_balance_amount']);
		}
		
		return $account_array;
	}
	
	function _get_user_store_account_balance_lists($user_id, $user_role, $include_option_title=false) {
		$account_array = array();
		
		if ($include_option_title)	$account_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$store_account_select_sql = "	SELECT sab.store_account_balance_currency, cur.title 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
										INNER JOIN " . TABLE_CURRENCIES . " AS cur 
											ON sab.store_account_balance_currency=cur.code 
										WHERE sab.user_id = '" . tep_db_input($user_id) . "' 
											AND sab.user_role = '" . tep_db_input($user_role) . "' 
										ORDER BY cur.code";
		$store_account_result_sql = tep_db_query($store_account_select_sql);
		
		while ($store_account_row = tep_db_fetch_array($store_account_result_sql)) {
			$account_array[] = array('id' => $store_account_row['store_account_balance_currency'], 'text' => $store_account_row['title'] . ' ('.$store_account_row['store_account_balance_currency'].')');
		}
		
		return $account_array;
	}
	
	function _get_payment_status() {
		global $languages_id;
		
		$status_array = array();
		
  		$payment_status_select_sql = "	SELECT store_payments_status_id, store_payments_status_name 
  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
  										WHERE language_id='" . $languages_id  . "' 
  										ORDER BY store_payments_status_sort_order";
  		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
  		while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
  			$status_array[] = array('id' => $payment_status_row["store_payments_status_id"], 'text' => $payment_status_row["store_payments_status_name"]);
  		}
		
		return $status_array;
	}
	
	function _get_all_active_send_payment_methods($include_option_title=false) {
	    global $login_groups_id;

		$pm_array = array();
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$parent_payment_method_array = array();
		$parent_payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_filename 
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_parent_id = '0' 
												ORDER BY payment_methods_sort_order";
		//AND FIND_IN_SET( '". $login_groups_id ."', payment_methods_admin_groups_id ) 
		//payment_methods_send_status = 1 AND 
		$parent_payment_method_result_sql = tep_db_query($parent_payment_method_select_sql);
		while ($parent_payment_method_row = tep_db_fetch_array($parent_payment_method_result_sql)) {
			$parent_payment_method_array[$parent_payment_method_row['payment_methods_id']] = $parent_payment_method_row['payment_methods_title'];
		}
		
		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_send_mode_name, payment_methods_parent_id 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_send_status = 1
											AND payment_methods_parent_id <> '0' 
											AND FIND_IN_SET( '". $login_groups_id ."', payment_methods_admin_groups_id ) 
										ORDER BY payment_methods_sort_order";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			if (in_array($payment_method_row['payment_methods_parent_id'], array_keys($parent_payment_method_array))) {
                            # Solved PHP Warning:  Illegal string offset 'payment_methods_filename' in /var/www/html/crew.offgamers.biz/admin/includes/classes/payments.php on line 3293
                            $pm_array[] = array(
                                'id' => $payment_method_row['payment_methods_id'], 
                                'text' => $parent_payment_method_array[$payment_method_row['payment_methods_parent_id']] . ' > ' .$payment_method_row['payment_methods_send_mode_name'],
                                'filename' => isset($parent_payment_method_array[$payment_method_row['payment_methods_parent_id']]['payment_methods_filename']) ? $parent_payment_method_array[$payment_method_row['payment_methods_parent_id']]['payment_methods_filename'] : ''
                            );
			}
		}
		
		return $pm_array;
	}
	
	// Allow to process payment
	function _is_allow_to_send_payment_methods($pm_id) {
	    global $login_groups_id;
		
		$payment_method_select_sql = "	SELECT payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
											AND payment_methods_send_status = 1
											AND payment_methods_parent_id <> '0' 
											AND FIND_IN_SET( '". $login_groups_id ."', payment_methods_payment_admin_groups_id ) 
		";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
	
	// Allow to update payment
	function _is_allow_to_update_payment_methods($pm_id) {
	    global $login_groups_id;
		
		$payment_method_select_sql = "	SELECT payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
											AND payment_methods_send_status = 1
											AND payment_methods_parent_id <> '0' 
											AND FIND_IN_SET( '". $login_groups_id ."', payment_methods_update_admin_groups_id ) 
		";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			return true;
		} else {
			return false;
		}
	}

	// Allow to cancel payment
	function _is_allow_to_cancel_payment_methods($pm_id) {
	    global $login_groups_id;
		
		$payment_method_select_sql = "	SELECT payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
											AND payment_methods_send_status = 1
											AND payment_methods_parent_id <> '0' 
											AND FIND_IN_SET( '". $login_groups_id ."', payment_methods_cancel_admin_groups_id ) 
		";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
	
	function _get_all_active_receive_payment_methods() {
		global $language;
		
		$payment_module_info = array();
		$payment_methods_obj = new payment_methods('payment_gateways');
		$payment_info_array = $payment_methods_obj->payment_gateways_array;
		
		foreach ($payment_info_array as $payment_gateways_id => $payment_gateways_data) {
			$payment_module_info[]= array('id' => $payment_gateways_id, 'text' => $payment_gateways_data->title);
		}
		
		return $payment_module_info;
	}
	
	function _get_payment_methods_fields($payment_method_id, $field_mode) {
		/******************************************************
			$field_mode:
				- 1: Input Field
				- 2: Output Info
		******************************************************/
		$pm_field_array = array();
		$search_field_array = array();
		
		if (is_array($field_mode) && count($field_mode)) {
			if (in_array('1', $field_mode)) {
				$search_field_array[] = '1'; // Text Box
				$search_field_array[] = '2'; // Text Area
				$search_field_array[] = '3'; // Dropdown Menu
				$search_field_array[] = '4'; // Radio Button
				$search_field_array[] = '5'; // Date Selection
			}
			
			if (in_array('2', $field_mode)) {
				$search_field_array[] = '6'; // Display Label
				$search_field_array[] = '7'; // Information Text
			}
			
			$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_type 
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
											WHERE payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
												AND payment_methods_mode = 'SEND' 
												AND payment_methods_fields_status = 1 
												AND payment_methods_fields_type IN ('" . implode("', '", $search_field_array) . "') 
											ORDER BY payment_methods_fields_sort_order";
			$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
			
			while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
				$pm_field_array[$payment_fields_row['payment_methods_fields_id']] = $payment_fields_row;
			}
		}
		
		return $pm_field_array;
	}
	
	function _is_payment_methods_type_mode_system_define($payment_methods_id) {
		$payment_method_type_select_sql = " SELECT pmt.payment_methods_types_system_define
											FROM . " . TABLE_PAYMENT_METHODS_TYPES . " as pmt
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
												ON (pmt.payment_methods_types_id=pm.payment_methods_types_id AND pm.payment_methods_id = '" . tep_db_input($payment_methods_id) . "')";
		$payment_method_type_result_sql = tep_db_query($payment_method_type_select_sql);
		
		if ($payment_method_type_row = tep_db_fetch_array($payment_method_type_result_sql)) {
			return $payment_method_type_row['payment_methods_types_system_define'];
		}
		
		return 0;
	}
	
	function _get_withdraw_into_third_party_account_info($user_account_id, $user_role, $payments_methods_id, $payment_account_book_id=null) {
		$user_array = false;
		
		if ($user_array === false) {
			if ($user_role == 'customers') {
				// no payment option email address found, default to use login user id
				$customer_select_sql = "SELECT customers_id, customers_status, customers_email_address FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . tep_db_input($user_account_id) . "'";
				$customer_result_sql = tep_db_query($customer_select_sql);
				if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
					if ($customer_row['customers_status'] == '1') {
						$user_array = array('user_id' => $customer_row['customers_id'], 'user_role' => 'customers', 'user_email' => $customer_row['customers_email_address']);
					}
				}
			}
		}
		
		return $user_array;
	}

	function _get_withdraw_sc_supplier_info($payment_account_book_id) {
		$user_array = false;
		$pm_fields_array = array();
		// get user_id (customer)
		$supplier_account_sql = "SELECT spabd.payment_methods_fields_value
								FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd
								INNER JOIN payment_methods_fields AS pmf
									ON spabd.payment_methods_fields_id = pmf.payment_methods_fields_id
									AND pmf.payment_methods_fields_title = 'Customer ID'
								WHERE store_payment_account_book_id = '" . tep_db_input($payment_account_book_id) . "'";
		$supplier_account_result = tep_db_query($supplier_account_sql);
		if ($supplier_account = tep_db_fetch_array($supplier_account_result)) {
			if (is_numeric($supplier_account['payment_methods_fields_value'])) {
				$customer_select_sql = "SELECT customers_id, customers_status, customers_email_address
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . $supplier_account['payment_methods_fields_value'] . "'";
				$customer_result_sql = tep_db_query($customer_select_sql);
				if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
					if ($customer_row['customers_status'] == '1') {
						$user_array = array('user_id' => $customer_row['customers_id'], 'user_role' => 'customers', 'user_email' => $customer_row['customers_email_address']);
					}
				}
			}
		}
		return $user_array;
	}
	
	function get_payment_details($payment_id) {
		$pm_detail_array = array();
		
		$payment_details_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_value 
										FROM " . TABLE_STORE_PAYMENTS_DETAILS . " 
										WHERE store_payments_id = '" . tep_db_input($payment_id) . "' 
										ORDER BY payment_methods_fields_sort_order";
		$payment_details_result_sql = tep_db_query($payment_details_select_sql);
		
		while ($payment_details_row = tep_db_fetch_array($payment_details_result_sql)) {
			$pm_detail_array[$payment_details_row['payment_methods_fields_id']] = $payment_details_row;
		}
		
		return $pm_detail_array;
	}
	
	function send_payment_status_email($payID, $comment='') {
		global $currencies;
		
		$this->_get_payment_info($payID);
		
		$user_particulars_array = $this->_get_user_particulars($this->beneficiary['id'], $this->beneficiary['role']);

		// Send email to payee
		if ($user_particulars_array['sign_up_from'] == 'CN') {
		    require(DIR_WS_LANGUAGES . 'email_contents_chinese.php');
		    
		    $email_payment_subject_message = array_search($this->info['status'], $PAYMENT_SUBJECT_MESSAGE_ARRAY);
		    $email_payment_status = array_search($this->info['status'], $PAYMENT_STATUS_ARRAY);
		    
		    $email = tep_mb_convert_encoding(tep_get_email_greeting_chinese (tep_mb_convert_encoding($user_particulars_array['fname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), tep_mb_convert_encoding($user_particulars_array['lname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $user_particulars_array['gender']), CHARSET_CHINESE, EMAIL_CHARSET_CHINESE) . "\n".
		                EMAIL_PAYMENT_TEXT_TITLE_CHINESE . "\n\n" .
    					EMAIL_PAYMENT_TEXT_SUMMARY_TITLE_CHINESE . "\n" . EMAIL_PAYMENT_SEPARATOR_CHINESE . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER_CHINESE, $payID) . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAID_AMOUNT_CHINESE, $currencies->format($this->payment_info["paid_amount"], false, $this->payment_info["paid_currency"])) . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_DATE_CHINESE, tep_date_long($this->info["payments_date"])) . "\n\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_STATUS_UPDATE_CHINESE, $email_payment_status) . "\n\n" . 
    				 	sprintf(EMAIL_PAYMENT_TEXT_COMMENTS_CHINESE, tep_db_prepare_input($comment)) . "\n" .
    				 	EMAIL_PAYMENT_TEXT_PROCESS_GUIDE_CHINESE . "\n\n" .
    				 	EMAIL_FOOTER_CHINESE;
    	        	    
		    @tep_mail(tep_mb_convert_encoding($user_particulars_array['fname'] . ' ' . $user_particulars_array['lname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $this->beneficiary['email_address'],  sprintf(EMAIL_PAYMENT_UPDATE_SUBJECT_CHINESE, $payID, $email_payment_subject_message), $email, LOCAL_STORE_NAME, LOCAL_STORE_EMAIL_ADDRESS, EMAIL_CHARSET_CHINESE);
		} else {
            $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
            while ($configuration = tep_db_fetch_array($configuration_query)) {
                define($configuration['cfgKey'], $configuration['cfgValue']);
            }
            
    		$email = 	EMAIL_PAYMENT_TEXT_TITLE . "\n\n" .
    					EMAIL_PAYMENT_TEXT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER, $payID) . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAID_AMOUNT, $currencies->format($this->payment_info["paid_amount"], false, $this->payment_info["paid_currency"])) . "\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_DATE, tep_date_long($this->info["payments_date"])) . "\n\n" .
    				 	sprintf(EMAIL_PAYMENT_TEXT_STATUS_UPDATE, $this->info['status_name']) . "\n\n" . 
    				 	sprintf(EMAIL_PAYMENT_TEXT_COMMENTS, tep_db_prepare_input($comment)) . "\n\n" .
						sprintf(EMAIL_G2G_BUYBACK_ORDER_CLOSING, G2G_STORE_NAME) . "\n\n" .
						G2G_EMAIL_FOOTER;
    		
    		$email_greeting = tep_get_email_greeting($user_particulars_array['fname'], $user_particulars_array['lname'], $user_particulars_array['gender']);
    		
    		$email = $email_greeting . $email;  
    		@tep_mail($user_particulars_array['fname'].' '.$user_particulars_array['lname'], $this->beneficiary['email_address'], G2G_EMAIL_SUBJECT_PREFIX . " " . sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $payID), $email, STORE_OWNER, G2G_STORE_OWNER_EMAIL_ADDRESS);
    	}
	}
	
	function _check_store_acc_exists($user_id, $user_role, $credit_currency) {
		$store_acc_balance_select_sql = "	SELECT store_account_balance_amount 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
												AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
		$store_acc_balance_result_sql = tep_db_query($store_acc_balance_select_sql);
		
		if (tep_db_num_rows($store_acc_balance_result_sql) > 0) {	// Store credit account exists
			return true;
		} else {	// Check if this is a valid customer
			$user_checking_info = $this->_get_user_particulars($user_id, $user_role);
			
			if (is_array($user_checking_info) && count($user_checking_info)) {	// Valid user
				$account_balance_data_array = array('user_id' => $user_id,
													'user_role' => $user_role,
							                        'store_account_balance_currency' => $credit_currency,
							                        'store_account_balance_amount' => 0,
							                        'store_account_last_modified' => 'now()'
							                       );
				tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $account_balance_data_array);
				
				return true;
			} else {
				return false;
			}
		}
	}
	
	function _set_store_acc_balance($user_id, $user_role, $credit_currency, $update_info) {
		/*******************************************************************
			operator = +, -, and =
		*******************************************************************/
		$sql_update_array = array();
		
		// Generate the update sql
		for ($update_cnt=0; $update_cnt < count($update_info); $update_cnt++) {
			if ( ($update_info[$update_cnt]['field_name'] == 'store_account_balance_amount' || $update_info[$update_cnt]['field_name'] == 'store_account_reserve_amount') ) {
				$update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
				switch($update_info[$update_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
		if (count($sql_update_array)) {
			$sql_update_array[] = ' store_account_last_modified = now() ';
			
			$update_sql_str = " SET " . implode(', ', $sql_update_array);
	    	
	    	/*************************************************************************
			 	Lock the TABLE_STORE_ACCOUNT_BALANCE 
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");
	    	
	    	$store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . 
	    										$update_sql_str . " 
												WHERE user_id = '" . tep_db_input($user_id) . "' 
													AND user_role = '" . tep_db_input($user_role) . "' 
													AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			tep_db_query($store_acc_balance_update_sql);
			
			$new_balance_select_sql = "	SELECT store_account_balance_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($user_id) . "' 
											AND user_role = '" . tep_db_input($user_role) . "' 
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			$new_balance_result_sql = tep_db_query($new_balance_select_sql);
			$new_balance_row = tep_db_fetch_array($new_balance_result_sql);
			
			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
			********************************************************************/
			
			return $new_balance_row['store_account_balance_amount'];
		}
		
		return false;
	}
	
	function _get_user_particulars($user_id, $user_role) {
		$user_info_row = array();
		if ($user_role == 'supplier') {
			$user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_gender AS gender, 'S' as sign_up_from, supplier_disable_withdrawal as disable_withdrawal, '' as flags 
										FROM " . TABLE_SUPPLIER . " 
										WHERE supplier_id = '" . tep_db_input($user_id) . "'";
		} else {
			$user_info_select_sql = "	SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email, customers_gender AS gender , ci.customers_info_account_created_from AS sign_up_from, customers_disable_withdrawal as disable_withdrawal, customers_flag as flags 
										FROM " . TABLE_CUSTOMERS . " AS c 
										LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
											ON c.customers_id=ci.customers_info_id
										WHERE customers_id = '" . tep_db_input($user_id) . "'";
		}
		
		$user_info_result_sql = tep_db_query($user_info_select_sql);
		$user_info_row = tep_db_fetch_array($user_info_result_sql);
		
		if ($user_info_row['sign_up_from'] == '0') {
			$user_info_row['sign_up_from'] = 'C';
		} else if ($user_info_row['sign_up_from'] == '1') {
			$user_info_row['sign_up_from'] = 'CN';
		} else if ($user_info_row['sign_up_from'] == '3') {
			$user_info_row['sign_up_from'] = 'PO';
		}
		
		if ($user_info_row['flags'] != '') {
			$user_flags = explode(',', $user_info_row['flags']);
			$user_info_row['flags'] = $user_flags;
		} else {
			$user_info_row['flags'] = array();
		}
		
		return $user_info_row;
	}
	
	function _get_general_reserve_amount($user_id, $user_role) {
		switch($user_role) {
			case "supplier":
				$reserve_amt_select_sql = "	SELECT supplier_reserve_amount 
											FROM " . TABLE_SUPPLIER . " 
											WHERE supplier_id = '".tep_db_input($user_id)."' ";
				$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
				$reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql);
				
				return $reserve_amt_row['supplier_reserve_amount'];
				
				break;
		    case "customers":
				$reserve_amt_select_sql = "	SELECT customers_reserve_amount 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '".tep_db_input($user_id)."' ";
				$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
				$reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql);
				
				return $reserve_amt_row['customers_reserve_amount'];
				
				break;
			default:
				return 0;
				
				break;
		}
	}
	
	function _get_dynamic_reserve_amount($user_id, $user_role, $credit_currency='') {
		$reserve_array = array();
		
		if (tep_not_null($credit_currency)) {
			$dynamic_reserve_select_sql = "	SELECT store_account_reserve_amount, store_account_balance_currency 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '".tep_db_input($user_id)."' 
												AND user_role = '".tep_db_input($user_role)."' 
												AND store_account_balance_currency = '".tep_db_input($credit_currency)."'";
			$dynamic_reserve_result_sql = tep_db_query($dynamic_reserve_select_sql);
			$dynamic_reserve_row = tep_db_fetch_array($dynamic_reserve_result_sql);
			
			$reserve_array[$dynamic_reserve_row['store_account_balance_currency']] = $dynamic_reserve_row['store_account_reserve_amount'];
		} else {
			$dynamic_reserve_select_sql = "	SELECT store_account_reserve_amount, store_account_balance_currency 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '".tep_db_input($user_id)."' 
												AND user_role = '".tep_db_input($user_role)."'";
			$dynamic_reserve_result_sql = tep_db_query($dynamic_reserve_select_sql);
			
			while ($dynamic_reserve_row = tep_db_fetch_array($dynamic_reserve_result_sql)) {
				$reserve_array[$dynamic_reserve_row['store_account_balance_currency']] = $dynamic_reserve_row['store_account_reserve_amount'];
			}
		}
		
		return $reserve_array;
	}
	
	function _get_other_currency_acc_eligible_balance($user_id, $user_role, $request_currency) {
		global $currencies;
		
		$eligible_balance = 0;
		
		$other_balance_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount - store_account_reserve_amount AS balance 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($user_id) . "' 
											AND user_role = '" . tep_db_input($user_role) . "' 
											AND store_account_balance_currency <> '" . tep_db_input($request_currency) . "'";
		$other_balance_result_sql = tep_db_query($other_balance_select_sql);
		
		while ($other_balance_row = tep_db_fetch_array($other_balance_result_sql)) {
			if ($other_balance_row['balance'] > 0) {
				$eligible_balance += $currencies->advance_currency_conversion($other_balance_row['balance'], $other_balance_row['store_account_balance_currency'], $request_currency, true, 'sell');
			}
		}
		
		return $eligible_balance;
	}
	
	function _check_store_acc_got_balance($user_id, $user_role, $request_currency, &$store_balance_amount, &$total_reserve_amount) {
		global $currencies;
		
		$store_balance_amount = 0;
		$user_credit_info_select_sql = "SELECT store_account_balance_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '".tep_db_input($user_id)."'
										AND user_role = '".tep_db_input($user_role)."'
										AND store_account_balance_currency = '".tep_db_input($request_currency)."'";
		$user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);
		if ($user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
			$store_balance_amount = (double)$user_credit_info_row['store_account_balance_amount'];
		}
		
		$general_reserve_amount = $this->_get_general_reserve_amount($user_id, $user_role);
		$converted_general_reserve_amount = $currencies->apply_currency_exchange($general_reserve_amount, $request_currency, '', 'sell');
		/*
		$other_eligible_balance = $this->_get_other_currency_acc_eligible_balance($user_id, $user_role, $request_currency);
		
		if ($other_eligible_balance >= $converted_general_reserve_amount) {
			$converted_general_reserve_amount = 0;
		} else {
			$converted_general_reserve_amount = $converted_general_reserve_amount - $other_eligible_balance;
		}
		*/
		$dynamic_reserved_amount_array = $this->_get_dynamic_reserve_amount($user_id, $user_role, $request_currency);
		
		$available_balance_all_currency = $this->_get_available_balance_all_currency($user_id,$request_currency);
		$store_balance_amount = $total_available_balance = $available_balance_all_currency - $converted_general_reserve_amount;
		if ( $total_available_balance >= 0) {
		
			return true;
		} else {
			return false;
		}
	}
	
	function is_utf8_3bytes($string) {
		return preg_match('%^(?:
			[\xE1-\xEC\xEE\xEF][\x80-\xBF]{2}  # straight 3-byte
		)*$%xs', $string);
	}
    
	function _import_mass_payment_csv($payment_method_id, $input_array, &$messageStack) {
		global $currencies;
		
		$success_array = array();
		$failure_array = array();
		$unknown_status_array = array();
		
		// Get the dynamic payment method fields ID array
		$field_array = array();
		$payment_input_fields_array = $this->_get_payment_methods_fields($payment_method_id, array('1'));	// Just look for those input fields
		foreach ($payment_input_fields_array as $pm_field_id => $pm_field_array) {
			$field_array[] = $pm_field_id;
		}
		
		if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
			if ($_FILES['csv_import']["size"] > 0) {
				$import_error = false;
				$filename = ($_FILES['csv_import']['tmp_name']);
			    $handle = fopen($filename, 'r+');
				switch ($payment_method_id) {
					case "3":	// ICBC
						// create success transaction array and failed transaction array
						$city_str = tep_mb_convert_encoding('ï¿½ï¿½ï¿½ï¿½', 'UTF-8', 'EUC-CN');
						$success_status = tep_mb_convert_encoding('ï¿½ï¿½ï¿½×³É¹ï¿½', 'UTF-8', 'EUC-CN');
						$success_apply_status = tep_mb_convert_encoding('ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½É¹ï¿½', 'UTF-8', 'EUC-CN');
						$failure_status = tep_mb_convert_encoding('ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½', 'UTF-8', 'EUC-CN');
						$failure_apply_status = tep_mb_convert_encoding('ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½', 'UTF-8', 'EUC-CN');

				    	while (($data = fgetcsv($handle, 4096, ',', '"')) !== FALSE) {
			    			$data0 = tep_mb_convert_encoding($data[0], 'EUC-CN', 'UTF-8');
			    			$data13 = tep_mb_convert_encoding($data[14], 'EUC-CN', 'UTF-8');
			    			if ($new_data == $city_str) { echo "match city string<br>"; }else{ "not match city string<br>"; }

				    		if ((trim($data[0]) == 'ï¿½ï¿½ï¿½ï¿½' || $data0 == $city_str) || trim($data[1]) == '') {	// Assume this row is useless
	    						continue;
			    			}
	    					if ((trim($data[14]) == 'ï¿½ï¿½ï¿½×³É¹ï¿½' || trim($data[14]) == 'ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½É¹ï¿½') ||
	    					    ($data13 == $success_status || $data13 == $success_apply_status)) {
	    						$success_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'received_amt'=>$data[8],'charge_bearer'=>$data[9],'refunded'=>$data[10],'payment_id'=>$data[11],'order_no'=>$data[12],'payment_reference'=>$data[13],'status'=>$data[14],'created_date'=>$data[15],'modified_date'=>$data[16],'temp1'=>$data[17]);
			    			} else if ((trim($data[14]) == 'ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½' || trim($data[14]) == 'ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½') ||
			    			           ($data13 == $failure_status || $data13 == $failure_apply_status)) {
			    				$failure_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'received_amt'=>$data[8],'charge_bearer'=>$data[9],'refunded'=>$data[10],'payment_id'=>$data[11],'order_no'=>$data[12],'payment_reference'=>$data[13],'status'=>$data[14],'created_date'=>$data[15],'modified_date'=>$data[16],'temp1'=>$data[17]);
	    					} else {
	    						$unknown_status_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'received_amt'=>$data[8],'charge_bearer'=>$data[9],'refunded'=>$data[10],'payment_id'=>$data[11],'order_no'=>$data[12],'payment_reference'=>$data[13],'status'=>$data[14],'created_date'=>$data[15],'modified_date'=>$data[16],'temp1'=>$data[17]);
	    					}
			    			
				    		/*if (trim($data[0]) == 'ï¿½ï¿½ï¿½ï¿½' || trim($data[1]) == '') {	// Assume this row is useless
	    						continue;
			    			}
	    					if (trim($data[13]) == 'ï¿½ï¿½ï¿½×³É¹ï¿½' || trim($data[13]) == 'ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½É¹ï¿½') {
	    						$success_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'charge_bearer'=>$data[8],'refunded'=>$data[9],'payment_id'=>$data[10],'order_no'=>$data[11],'payment_reference'=>$data[12],'status'=>$data[13],'created_date'=>$data[14],'modified_date'=>$data[15],'temp1'=>$data[16]);
			    			} else if (trim($data[13]) == 'ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½' || trim($data[13]) == 'ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ê§ï¿½ï¿½') {
			    				$failure_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'charge_bearer'=>$data[8],'refunded'=>$data[9],'payment_id'=>$data[10],'order_no'=>$data[11],'payment_reference'=>$data[12],'status'=>$data[13],'created_date'=>$data[14],'modified_date'=>$data[15],'temp1'=>$data[16]);
	    					} else {
	    						$unknown_status_array[] = array('city'=>$data[0],'bank'=>$data[1],'branch'=>$data[2],'customer_name'=>$data[3],'customer_account'=>$data[4],'transact_amt'=>$data[5],'charge_fees'=>$data[6],'payment_amt'=>$data[7],'charge_bearer'=>$data[8],'refunded'=>$data[9],'payment_id'=>$data[10],'order_no'=>$data[11],'payment_reference'=>$data[12],'status'=>$data[13],'created_date'=>$data[14],'modified_date'=>$data[15],'temp1'=>$data[16]);
	    					}*/
						}
						
						// If got successful transaction, process it
						if (count($success_array) > 0) {
							$success_checking_array = $failed_checking_array = array();
							// Check all payment method fields value against database value
							// Success on SQL checking, raw data assign to $success_checking_array
							// Failure on SQL checking, raw data assign to $failed_checking_array
							foreach($success_array as $idx => $rec) {
								$pm_value_array = array($rec['customer_name'], preg_replace('/\s/', '', $rec['customer_account']));
								$to_check_count = count($pm_value_array); // dynamic fields counting
								list(,,$store_payment_id) = explode(" ", $rec['payment_id']);

								$payment_details_select_sql = " SELECT spd.store_payments_id,
                    						                       		spd.payment_methods_fields_id, 
                        	        	    				       		spd.payment_methods_fields_title,
				            	        	                       		spd.payment_methods_fields_value
	            					                        	FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd
		                            					    	WHERE spd.store_payments_id = '" . tep_db_input($store_payment_id) . "' 
					    		                            		AND spd.payment_methods_fields_id IN ('" . implode("', '", $field_array) . "')";
								$payment_details_result_sql = tep_db_query($payment_details_select_sql);
								$row_match = 0;
								while ($payment_details_row = tep_db_fetch_array($payment_details_result_sql)) {
									if (mb_detect_encoding($rec['customer_name']) == 'ASCII' && mb_detect_encoding($payment_details_row['payment_methods_fields_value']) == 'ASCII') {
										if (strcmp($payment_details_row['payment_methods_fields_value'], $rec['customer_name']) === 0) $row_match++;
									} else {
										$encoded_text_str = tep_mb_convert_encoding($rec['customer_name'], 'UTF-8', 'EUC-CN');
										$encoded_db_str   = tep_mb_convert_encoding($payment_details_row['payment_methods_fields_value'], 'EUC-CN', 'UTF-8');
										if (strcmp($encoded_text_str, $encoded_db_str) === 0) $row_match++;
									}

									if (mb_detect_encoding($rec['customer_account']) == 'ASCII' && mb_detect_encoding($payment_details_row['payment_methods_fields_value']) == 'ASCII') {
										$current_pm_fields_value = preg_replace('/\s/', '', $payment_details_row['payment_methods_fields_value']);  // remove whitespaces
										if (strcmp($current_pm_fields_value, $rec['customer_account']) === 0) $row_match++;
									} else {
										$current_pm_fields_value = mb_ereg_replace('/\s/', '', $payment_details_row['payment_methods_fields_value']); // remove whitespaces
										// step to convert multi-bytes digit to ascii digit
										$converted_str = '';
										$digit_array = array();
										if ($this->is_utf8_3bytes($current_pm_fields_value)) {
											for ($i=0; $i < strlen($current_pm_fields_value); $i=$i+3) {
												$this_bytes = substr($current_pm_fields_value,$i,3);
												if (bin2hex(substr($this_bytes,0,1))=='ef' && bin2hex(substr($this_bytes,1,1))=='bc') {
													switch(bin2hex(substr($this_bytes,2,1))) {
														case '90' : $digit_array[] = '0'; break;
														case '91' : $digit_array[] = '1'; break;
														case '92' : $digit_array[] = '2'; break;
														case '93' : $digit_array[] = '3'; break;
														case '94' : $digit_array[] = '4'; break;
														case '95' : $digit_array[] = '5'; break;
														case '96' : $digit_array[] = '6'; break;
														case '97' : $digit_array[] = '7'; break;
														case '98' : $digit_array[] = '8'; break;
														case '99' : $digit_array[] = '9'; break;
														default : $digit_array[] = $this_bytes; break;
													}
												} else {
													$digit_array[] = $this_bytes;
												}
											}
											$converted_str = implode('',$digit_array);
										} else {
											$converted_str = $current_pm_fields_value;
										}

										$encoded_text_str = $rec['customer_account']; //tep_mb_convert_encoding($rec['customer_account'], 'UTF-8', 'EUC-CN');
										$encoded_db_str   = tep_mb_convert_encoding($converted_str, 'UTF-8', 'EUC-CN');
										if (strcmp($encoded_text_str, $encoded_db_str) === 0) $row_match++;
									}
								}

								if ($to_check_count == $row_match) {
									$success_checking_array[$store_payment_id] = $rec;
								} else {
									$failed_checking_array[$store_payment_id] = $rec;
								}
							}

							// Using $success_checking_array data to get amount from store_payment record, which that having:
							// 1. correct store payment ID
							// 2. status is in processing
							// Success on SQL checking, raw data is used to check amount in database:
							//    - Success on amount checking, raw data assign to $pass_price_chekcing_array
							//    - failure on amount checking, raw data assign to $fail_price_checking_array
							// Failure on SQL checking due to status, raw data assign to $invalid_status_array
							$invalid_status_array = $pass_price_checking_array = $fail_price_checking_array = array();
							foreach($success_checking_array as $sp_id => $rec) {
								$amount_to_check = (double)$rec['payment_amt'];
								
								$store_payment_select_sql = " SELECT sp.store_payments_id, 
                    						                       		sp.store_payments_after_fees_amount, 
                        	        	    				       		sp.store_payments_request_currency, 
				            	        	                       		sp.store_payments_paid_currency, 
				            	        	                       		sp.store_payments_paid_currency_value 
	            					                        	FROM " . TABLE_STORE_PAYMENTS . " AS sp 
		                            					    	WHERE sp.store_payments_id = '" . tep_db_input($sp_id) . "' 
					    		                            		AND sp.store_payments_status = '2'";
								$store_payment_result_sql = tep_db_query($store_payment_select_sql);
								
								if ($store_payment_row = tep_db_fetch_array($store_payment_result_sql)) {
									$requested_pay_amount = 0;
									if (trim($store_payment_row['store_payments_request_currency']) == trim($store_payment_row['store_payments_paid_currency'])) {
										$requested_pay_amount = (double)$store_payment_row['store_payments_after_fees_amount'];
									} else {
										$requested_pay_amount = (double)$store_payment_row['store_payments_after_fees_amount'] * (double)$store_payment_row['store_payments_paid_currency_value'];
									}

									if (number_format($amount_to_check,2,'.','') == number_format($requested_pay_amount,2,'.','')) {
										$rec['ex_rate'] = $store_payment_row['store_payments_paid_currency_value'];
										$pass_price_checking_array[$sp_id] = $rec;
									} else {
										$fail_price_checking_array[$sp_id] = $rec;
									}
								} else {
									$invalid_status_array[$sp_id] = $rec;
								}
							}

							// Using $pass_price_checking_array data to update database record
							foreach ($pass_price_checking_array as $pay_id => $rec) {
								$payment_reference = $rec['payment_reference'];
								$latest_ex_rate = $rec['ex_rate'];
								$this->_payment_completion_process($pay_id, $payment_reference, $latest_ex_rate, $messageStack);
							}
						}
						
						// Combine all failure data into a single array for error reporting
						if (count($failure_array) > 0) {
							foreach($failure_array as $idx => $rec) {
								list(,,$failed_pay_id) = explode(" ", $rec['payment_id']);
								$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_TRANSACTION, $failed_pay_id), 'warning');
							}
							unset($failed_pay_id);
						}
						if (count($failed_checking_array) > 0) {
							foreach($failed_checking_array as $failed_pay_id => $rec) {
								$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_PAYMENT_METHOD_FIELDS, $failed_pay_id), 'warning');
							}
							unset($failed_pay_id);
						}
						if (count($fail_price_checking_array) > 0) {
							foreach($fail_price_checking_array as $failed_pay_id => $rec) {
								$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_AMOUNT_CHECKING, $failed_pay_id), 'warning');
							}
							unset($failed_pay_id);
						}
						if (count($invalid_status_array) > 0) {
							foreach($invalid_status_array as $failed_pay_id => $rec) {
								$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_STATUS, $failed_pay_id), 'warning');
							}
							unset($failed_pay_id);
						}
						if (count($unknown_status_array) > 0) {
							foreach($unknown_status_array as $idx => $rec) {
								list(,,$failed_pay_id) = explode(" ", $rec['payment_id']);
								$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_UNKNOWN_STATUS, $failed_pay_id), 'warning');
							}
							unset($failed_pay_id);
						}
						break;

					default:	// Alipay
						require_once 'Spreadsheet/Excel/Reader.php';
						$xls_Object = new Spreadsheet_Excel_Reader();
						$xls_Object->setUTFEncoder('mb');
						$xls_Object->setOutputEncoding('UTF-8');

						if ($xls_Object->read($filename) === false) {
							$messageStack->add_session($xls_Object->error_message, 'warning');
						} else {
							$success_status = '成功';
							$failure_status = '失败';
							// create success transaction array and failed transaction array
							for ($i = 4; $i <= $xls_Object->sheets[0]['numRows']; $i++) {
                                                            if (isset($xls_Object->sheets[0]['cells'][$i][7])) {
								$input_status = tep_mb_convert_encoding(trim($xls_Object->sheets[0]['cells'][$i][7]), 'UTF-8', null);
                                                                $failure_reason = isset($xls_Object->sheets[0]['cells'][$i][8]) ? $xls_Object->sheets[0]['cells'][$i][8] : '';
								
                                                                if ($input_status == $success_status) {
									$success_array[] = array('idx' => $xls_Object->sheets[0]['cells'][$i][1], 'customer_email' => $xls_Object->sheets[0]['cells'][$i][2], 'customer_name' => $xls_Object->sheets[0]['cells'][$i][3], 'payment_amt' => $xls_Object->sheets[0]['cells'][$i][4], 'payment_id' => $xls_Object->sheets[0]['cells'][$i][5], 'payment_reference' => $xls_Object->sheets[0]['cells'][$i][6], 'status' => $xls_Object->sheets[0]['cells'][$i][7], 'failure_reason' => $failure_reason);
								} else if ($input_status == $failure_status) {
									$failure_array[] = array('idx' => $xls_Object->sheets[0]['cells'][$i][1], 'customer_email' => $xls_Object->sheets[0]['cells'][$i][2], 'customer_name' => $xls_Object->sheets[0]['cells'][$i][3], 'payment_amt' => $xls_Object->sheets[0]['cells'][$i][4], 'payment_id' => $xls_Object->sheets[0]['cells'][$i][5], 'payment_reference' => $xls_Object->sheets[0]['cells'][$i][6], 'status' => $xls_Object->sheets[0]['cells'][$i][7], 'failure_reason' => $failure_reason);
								} else {
									$unknown_status_array[] = array('idx' => $xls_Object->sheets[0]['cells'][$i][1], 'customer_email' => $xls_Object->sheets[0]['cells'][$i][2], 'customer_name' => $xls_Object->sheets[0]['cells'][$i][3], 'payment_amt' => $xls_Object->sheets[0]['cells'][$i][4], 'payment_id' => $xls_Object->sheets[0]['cells'][$i][5], 'payment_reference' => $xls_Object->sheets[0]['cells'][$i][6], 'status' => $xls_Object->sheets[0]['cells'][$i][7], 'failure_reason' => $failure_reason);
								}
                                                            }
							}
						
							// If got successful transaction, process it
							if (count($success_array) > 0) {
								$success_checking_array = $failed_checking_array = array();
								// Check all payment method fields value against database value
								// Success on SQL checking, raw data assign to $success_checking_array
								// Failure on SQL checking, raw data assign to $failed_checking_array
								foreach($success_array as $idx => $rec) {
									$pm_value_array = array($rec['customer_email'],$rec['customer_name']);
									$to_check_count = count($pm_value_array); // dynamic fields counting
									list(,,$store_payment_id) = explode(" ", $rec['payment_id']);

									$payment_details_select_sql = " SELECT spd.store_payments_id,
                    						                       		spd.payment_methods_fields_id, 
                        	        	    				       		spd.payment_methods_fields_title,
				            	        	                       		spd.payment_methods_fields_value
	            					                        	FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd
		                            					    	WHERE spd.store_payments_id = '" . tep_db_input($store_payment_id) . "' 
					    		                            		AND spd.payment_methods_fields_id IN ( " . tep_db_input(implode(",",$field_array)) . " )
					        		                        	ORDER BY spd.store_payments_id DESC, 
                    					                         	spd.payment_methods_fields_sort_order ASC";
									$payment_details_result_sql = tep_db_query($payment_details_select_sql);
									$row_match = 0;
									while ($payment_details_row = tep_db_fetch_array($payment_details_result_sql)) {
										if (mb_detect_encoding($rec['customer_email']) == 'ASCII' && mb_detect_encoding($payment_details_row['payment_methods_fields_value']) == 'ASCII') {
											if (strcmp($payment_details_row['payment_methods_fields_value'], $rec['customer_email']) === 0) $row_match++;
										} else {
											$encoded_text_str = tep_mb_convert_encoding($rec['customer_email'], 'EUC-CN', 'UTF-8');
											$encoded_db_str   = tep_mb_convert_encoding($payment_details_row['payment_methods_fields_value'], 'EUC-CN', 'UTF-8');
											if (strcmp($encoded_text_str, $encoded_db_str) === 0) $row_match++;
										}

										if (mb_detect_encoding($rec['customer_name']) == 'ASCII' && mb_detect_encoding($payment_details_row['payment_methods_fields_value']) == 'ASCII') {
											if (strcmp($payment_details_row['payment_methods_fields_value'], $rec['customer_name']) === 0) $row_match++;
										} else {
											$encoded_text_str = tep_mb_convert_encoding($rec['customer_name'], 'EUC-CN', 'UTF-8');
											$encoded_db_str   = tep_mb_convert_encoding($payment_details_row['payment_methods_fields_value'], 'EUC-CN', 'UTF-8');
											if (strcmp($encoded_text_str, $encoded_db_str) === 0) $row_match++;
										}
									}

									if ($to_check_count == $row_match) {
										$success_checking_array[$store_payment_id] = $rec;
									} else {
										$failed_checking_array[$store_payment_id] = $rec;
									}
								}

								// Using $success_checking_array data to get amount from store_payment record, which that having:
								// 1. correct store payment ID
								// 2. status is in processing
								// Success on SQL checking, raw data is used to check amount in database:
								//    - Success on amount checking, raw data assign to $pass_price_chekcing_array
								//    - failure on amount checking, raw data assign to $fail_price_checking_array
								// Failure on SQL checking due to status, raw data assign to $invalid_status_array
								$invalid_status_array = $pass_price_checking_array = $fail_price_checking_array = array();
								foreach($success_checking_array as $sp_id => $rec) {
									$amount_to_check = (double)$rec['payment_amt'];
									
									$store_payment_select_sql = " SELECT sp.store_payments_id, 
                    						                       		sp.store_payments_after_fees_amount, 
                        	        	    				       		sp.store_payments_request_currency, 
				            	        	                       		sp.store_payments_paid_currency, 
				            	        	                       		sp.store_payments_paid_currency_value 
	            					                        	FROM " . TABLE_STORE_PAYMENTS . " AS sp 
		                            					    	WHERE sp.store_payments_id = '" . tep_db_input($sp_id) . "' 
					    		                            		AND sp.store_payments_status = '2'";
									$store_payment_result_sql = tep_db_query($store_payment_select_sql);

									if ($store_payment_row = tep_db_fetch_array($store_payment_result_sql)) {
										$requested_pay_amount = 0;
										if (trim($store_payment_row['store_payments_request_currency']) == trim($store_payment_row['store_payments_paid_currency'])) {
											$requested_pay_amount = (double)$store_payment_row['store_payments_after_fees_amount'];
										} else {
											$requested_pay_amount = (double)$store_payment_row['store_payments_after_fees_amount'] * (double)$store_payment_row['store_payments_paid_currency_value'];
										}

										if (number_format($amount_to_check,2,'.','') == number_format($requested_pay_amount,2,'.','')) {
											$rec['ex_rate'] = $store_payment_row['store_payments_paid_currency_value'];
											$pass_price_checking_array[$sp_id] = $rec;
										} else {
											$fail_price_checking_array[$sp_id] = $rec;
										}
									} else {
										$invalid_status_array[$sp_id] = $rec;
									}
								}

								// Using $pass_price_checking_array data to update database record
								foreach ($pass_price_checking_array as $pay_id => $rec) {
									$payment_reference = $rec['payment_reference'];
									$latest_ex_rate = $rec['ex_rate'];
									$this->_payment_completion_process($pay_id, $payment_reference, $latest_ex_rate, $messageStack);
								}
							}

							// Combine all failure data into a single array for error reporting
							if (count($failure_array) > 0) {
								foreach($failure_array as $idx => $rec) {
									list(,,$failed_pay_id) = explode(" ", $rec['payment_id']);
									$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_TRANSACTION, $failed_pay_id), 'warning');
								}
								unset($failed_pay_id);
							}
							if (count($failed_checking_array) > 0) {
								foreach($failed_checking_array as $failed_pay_id => $rec) {
									$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_PAYMENT_METHOD_FIELDS, $failed_pay_id), 'warning');
								}
								unset($failed_pay_id);
							}
							if (count($fail_price_checking_array) > 0) {
								foreach($fail_price_checking_array as $failed_pay_id => $rec) {
									$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_AMOUNT_CHECKING, $failed_pay_id), 'warning');
								}
								unset($failed_pay_id);
							}
							if (count($invalid_status_array) > 0) {
								foreach($invalid_status_array as $failed_pay_id => $rec) {
									$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_STATUS, $failed_pay_id), 'warning');
								}
								unset($failed_pay_id);
							}
							if (count($unknown_status_array) > 0) {
								foreach($unknown_status_array as $idx => $rec) {
									list(,,$failed_pay_id) = explode(" ", $rec['payment_id']);
									$messageStack->add_session(sprintf(WARNING_MASS_PAYMENT_IMPORT_FAILED_UNKNOWN_STATUS, $failed_pay_id), 'warning');
								}
								unset($failed_pay_id);
							}
						}
						break;
	    		}
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_IMPORT, 'error');
				return false;
			}
		} else {
			$messageStack->add_session(ERROR_PAYMENT_BATCH_MASS_PAYMENT_MISSING_FILE, 'error');
			return false;
		}
	}
	
	function _prepare_mass_payment_csv($payment_method_id, $pay_id_array, &$messageStack) {
		global $currencies;
		require_once 'Spreadsheet/Excel/Writer.php';
		
		if (file_exists(DIR_WS_LANGUAGES . 'export_chinese.php')) {
			include_once(DIR_WS_LANGUAGES . 'export_chinese.php');
		}
		
		$total_payment_count = $total_payment_amount = 0;
		$body_column_count = 0;
		
		// Check for mass payment option is on for this payment method
		$mass_pay_select_sql = "SELECT pm.payment_methods_send_mass_payment, pg.payment_methods_filename  
								FROM " . TABLE_PAYMENT_METHODS . " AS pm
								LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pg
									ON pm.payment_methods_parent_id = pg.payment_methods_id
								WHERE pm.payment_methods_id = '".tep_db_input($payment_method_id)."' 
									AND pm.payment_methods_send_mass_payment = 1";
		$mass_pay_result_sql = tep_db_query($mass_pay_select_sql);
		
		if ($mass_pay_row = tep_db_fetch_array($mass_pay_result_sql)) {
			// We give the path to our file here, blank for direct output
			$workbook = new Spreadsheet_Excel_Writer('');
			$workbook->setVersion(8);
			$worksheet =& $workbook->addWorksheet('Mass Payment Excel');
			$worksheet->setInputEncoding('utf-8');
			
			// Future development: get the template id. Now assume is AliPay template by default
			if (is_array($pay_id_array)) {
				switch ($payment_method_id) {
					case "3":	// ICBC
						$body_row = 1;
						// Reverse the ordering as Alipay will stack the records
						for ($payIDCnt=count($pay_id_array)-1; $payIDCnt >= 0; $payIDCnt--) {
							$payID = $pay_id_array[$payIDCnt];
							
							$csv_content_array = array();
							
							// Grab processing payments only
							$payment_select_sql = "	SELECT store_payments_after_fees_amount, store_payments_request_currency, store_payments_paid_currency, store_payments_paid_currency_value 
													FROM " . TABLE_STORE_PAYMENTS . " 
													WHERE store_payments_id = '" . tep_db_input($payID) . "' 
														AND store_payments_methods_id = '" . tep_db_input($payment_method_id) . "' 
														AND store_payments_status = 2";
							$payment_result_sql = tep_db_query($payment_select_sql);
							
							if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
								$csv_content_array[] = T_2_CSV_BODY_PAYEE_BANK;
								
								$payment_details_array = $this->get_payment_details($payID);
								$payment_input_fields_array = $this->_get_payment_methods_fields($payment_method_id, array('1'));	// Just look for those input fields
								if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
									foreach ($payment_input_fields_array as $field_id => $field_info) {
										$csv_content_array[] = $payment_details_array[$field_id]['payment_methods_fields_value'];
									}
								}
								
								//$request_amount = number_format(tep_round($payment_row['store_payments_after_fees_amount'], $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
								$request_amount = $payment_row['store_payments_after_fees_amount'];
								
								// style - red colour
								if (tep_not_null($payment_row['store_payments_paid_currency_value'])) {
									$stored_currency_rate = $payment_row['store_payments_paid_currency_value'];
								} else {
									$stored_currency_rate = $currencies->advance_currency_conversion_rate($payment_row['store_payments_request_currency'], $payment_row['store_payments_paid_currency']);
								}
								
								$actual_payout_amount = $request_amount * $stored_currency_rate;
								$rounded_actual_payout_amount = number_format(tep_round($actual_payout_amount, 2), 2, '.', '');	// Kuai Qian max is 2 Decimals
								
								$csv_content_array[] = $rounded_actual_payout_amount;
								$is_g2g = c2c_invoice::check_g2g_withdraw($payID);
								if($is_g2g == true){
									$csv_content_array[] = strtoupper(sprintf(TEXT_G2G_PAYMENT_REMARKS, $payID));
								}
								else{
								$csv_content_array[] = strtoupper(sprintf(TEXT_PAYMENT_REMARKS, $payID));
								}	
								
								for ($excelCnt=0; $excelCnt < count($csv_content_array); $excelCnt++) {
									if ($excelCnt == 0) {
										//$worksheet->write($body_row, $excelCnt, tep_mb_convert_encoding($csv_content_array[$excelCnt], "GB2312", "UTF-8"));
										$worksheet->write($body_row, $excelCnt, $csv_content_array[$excelCnt]);
									} else {
										if (is_numeric($csv_content_array[$excelCnt]) && strlen($csv_content_array[$excelCnt]) > 15) {
											//$worksheet->writeString($body_row, $excelCnt, tep_mb_convert_encoding($csv_content_array[$excelCnt], "GB2312", "UTF-8"));
											$worksheet->writeString($body_row, $excelCnt, html_entity_decode($csv_content_array[$excelCnt], ENT_QUOTES, "utf-8"));
										} else {
											//$worksheet->write($body_row, $excelCnt, tep_mb_convert_encoding($csv_content_array[$excelCnt], "GB2312", "UTF-8"));
											$worksheet->write($body_row, $excelCnt, html_entity_decode($csv_content_array[$excelCnt], ENT_QUOTES, "utf-8"));
										}
									}
								}
								
								$body_row++;
								$body_column_count++;
							}
						}
						
						$worksheet->write(0, 0, T_2_CSV_HEADER_BANK_NAME);
						$worksheet->write(0, 1, T_2_CSV_HEADER_PAYEE_NAME);
						$worksheet->write(0, 2, T_2_CSV_HEADER_PAYEE_ACCOUNT_NO);
						$worksheet->write(0, 3, T_2_CSV_HEADER_PAY_AMOUNT);
						$worksheet->write(0, 4, T_2_CSV_HEADER_PAYMENT_NOTE);
						$worksheet->write(0, 5, T_2_CSV_HEADER_PAYMENT_GATEWAY_ID);
						$worksheet->write(0, 5, T_2_CSV_HEADER_PAYMENT_FEES);
						$worksheet->write(0, 5, T_2_CSV_HEADER_PAYMENT_STATUS);
						$worksheet->write(0, 5, T_2_CSV_HEADER_PAYMENT_DATE);
						
						break;
					default:
						switch ($mass_pay_row['payment_methods_filename']) {
							case 'paypal.php':
									$file_content = '';
									for ($payIDCnt=count($pay_id_array)-1; $payIDCnt >= 0; $payIDCnt--) {
										$payID = $pay_id_array[$payIDCnt];

										$csv_content_array = array();
										// Grab processing payments only
										$payment_select_sql = "	SELECT store_payments_after_fees_amount, store_payments_request_currency, store_payments_paid_currency, store_payments_paid_currency_value
																FROM " . TABLE_STORE_PAYMENTS . "
																WHERE store_payments_id = '" . tep_db_input($payID) . "'
																	AND store_payments_methods_id = '" . tep_db_input($payment_method_id) . "'
																	AND store_payments_status = 2
																	AND store_payments_lock = 0";
										$payment_result_sql = tep_db_query($payment_select_sql);
										$payment_select_extra_info_sql = "SELECT * FROM ".TABLE_STORE_PAYMENTS_EXTRA_INFO." WHERE store_payments_id = ".tep_db_input($payID);
										$payment_select_extra_info_result = tep_db_query($payment_select_extra_info_sql);
										$store_credit_extra_info = [];

										while($row = tep_db_fetch_array($payment_method_info_result_sql)){
											$store_credit_extra_info[$row['store_payments_extra_info_key']] = $row['store_payments_extra_info_value'];
										}
										$tax_amount = 0;
										if(isset($store_credit_extra_info['tax_enabled']) && $store_credit_extra_info['tax_enabled']){
											$tax_amount = $store_credit_extra_info['tax_amount'];
										}
										$rounded_tax_amount = $request_amount = number_format($tax_amount, $this->currency_display_decimal, '.', '');

										if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
											// select receiver email
											$receiver_email_sql = "	SELECT spd.payment_methods_fields_value
																	FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd
																	INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
																		ON spd.payment_methods_fields_id = pmf.payment_methods_fields_id
																	WHERE pmf.payment_methods_fields_system_type = 'MODULE_PAYPAL_SEND_EMAIL'
																		AND spd.store_payments_id = '".tep_db_input($payID)."'";
											$receiver_email_result = tep_db_query($receiver_email_sql);
											$receiver_email_row = tep_db_fetch_array($receiver_email_result);
											if (tep_not_null($receiver_email_row['payment_methods_fields_value'])) {
												$request_amount = number_format(tep_round($payment_row['store_payments_after_fees_amount'], $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
												$file_content .= $receiver_email_row['payment_methods_fields_value'] . "\t" . $request_amount . "\t" . $payment_row['store_payments_request_currency'] . "\t" . $payID . "\tOffGamers Payment ".$payID."\n";
											}
										}
									}
									
									$filename = 'mass_payment_'.date('YmdHis').'.txt';
									$handle = fopen($filename, "w");
									fwrite($handle, $file_content);
									header("Content-type: application/text");
							        header("Content-Disposition: attachment; filename=\"$filename\"");
							        header("Expires: 0");
							        header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
							        header("Pragma: public");
									echo $file_content;
									fclose($handle); // this removes the file
									unlink($filename);

									exit();
								break;
							case 'paypalEC.php':
									$file_content = '';
									for ($payIDCnt=count($pay_id_array)-1; $payIDCnt >= 0; $payIDCnt--) {
										$payID = $pay_id_array[$payIDCnt];

										$csv_content_array = array();
										// Grab processing payments only
										$payment_select_sql = "	SELECT store_payments_after_fees_amount, store_payments_request_currency, store_payments_paid_currency, store_payments_paid_currency_value
																FROM " . TABLE_STORE_PAYMENTS . "
																WHERE store_payments_id = '" . tep_db_input($payID) . "'
																	AND store_payments_methods_id = '" . tep_db_input($payment_method_id) . "'
																	AND store_payments_status = 2
																	AND store_payments_lock = 0";
										$payment_result_sql = tep_db_query($payment_select_sql);
										$payment_select_extra_info_sql = "SELECT * FROM ".TABLE_STORE_PAYMENTS_EXTRA_INFO." WHERE store_payments_id = ".tep_db_input($payID);
										$payment_select_extra_info_result = tep_db_query($payment_select_extra_info_sql);
										$store_credit_extra_info = [];

										while($row = tep_db_fetch_array($payment_method_info_result_sql)){
											$store_credit_extra_info[$row['store_payments_extra_info_key']] = $row['store_payments_extra_info_value'];
										}
										$tax_amount = 0;
										if(isset($store_credit_extra_info['tax_enabled']) && $store_credit_extra_info['tax_enabled']){
											$tax_amount = $store_credit_extra_info['tax_amount'];
										}
										$rounded_tax_amount = $request_amount = number_format($tax_amount, $this->currency_display_decimal, '.', '');
										if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
											// select receiver email
											$receiver_email_sql = "	SELECT spd.payment_methods_fields_value
																	FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd
																	INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
																		ON spd.payment_methods_fields_id = pmf.payment_methods_fields_id
																	WHERE pmf.payment_methods_fields_system_type = 'MODULE_PAYPALEC_SEND_EMAIL'
																		AND spd.store_payments_id = '".tep_db_input($payID)."'";
											$receiver_email_result = tep_db_query($receiver_email_sql);
											$receiver_email_row = tep_db_fetch_array($receiver_email_result);
											if (tep_not_null($receiver_email_row['payment_methods_fields_value'])) {
												$request_amount = number_format(tep_round($payment_row['store_payments_after_fees_amount'], $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
												$file_content .= $receiver_email_row['payment_methods_fields_value'] . "\t" . $request_amount . "\t" . $payment_row['store_payments_request_currency'] . "\t" . $payID . "\tOffGamers Payment ".$payID."\n";
											}
										}
									}

									$filename = 'mass_payment_'.date('YmdHis').'.txt';
									$handle = fopen($filename, "w");
									fwrite($handle, $file_content);
									header("Content-type: application/text");
							        header("Content-Disposition: attachment; filename=\"$filename\"");
							        header("Expires: 0");
							        header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
							        header("Pragma: public");
									echo $file_content;
									fclose($handle); // this removes the file
									unlink($filename);
									
									exit();
								break;
							default:
								$body_row = 3;
								// Reverse the ordering as Alipay will stack the records
								for ($payIDCnt=count($pay_id_array)-1; $payIDCnt >= 0; $payIDCnt--) {
									$payID = $pay_id_array[$payIDCnt];

									$csv_content_array = array();
									// Grab processing payments only
									$payment_select_sql = "	SELECT store_payments_after_fees_amount, store_payments_request_currency, store_payments_paid_currency, store_payments_paid_currency_value
															FROM " . TABLE_STORE_PAYMENTS . "
															WHERE store_payments_id = '" . tep_db_input($payID) . "'
																AND store_payments_methods_id = '" . tep_db_input($payment_method_id) . "'
																AND store_payments_status = 2";
									$payment_select_extra_info_sql = "SELECT * FROM ".TABLE_STORE_PAYMENTS_EXTRA_INFO." WHERE store_payments_id = ".tep_db_input($payID);
									$payment_select_extra_info_result = tep_db_query($payment_select_extra_info_sql);
									$store_credit_extra_info = [];

									while($row = tep_db_fetch_array($payment_method_info_result_sql)){
										$store_credit_extra_info[$row['store_payments_extra_info_key']] = $row['store_payments_extra_info_value'];
									}
									$tax_amount = 0;
									if(isset($store_credit_extra_info['tax_enabled']) && $store_credit_extra_info['tax_enabled']){
										$tax_amount = $store_credit_extra_info['tax_amount'];
									}
									$payment_result_sql = tep_db_query($payment_select_sql);

									if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
										$csv_content_array[] = $payID;

										$payment_details_array = $this->get_payment_details($payID);
										$payment_input_fields_array = $this->_get_payment_methods_fields($payment_method_id, array('1'));	// Just look for those input fields
										if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
											foreach ($payment_input_fields_array as $field_id => $field_info) {
												$csv_content_array[] = $payment_details_array[$field_id]['payment_methods_fields_value'];
											}
										}
										
										//$request_amount = number_format(tep_round($payment_row['store_payments_after_fees_amount'], $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
										$request_amount = $payment_row['store_payments_after_fees_amount'];
										
										// style - red colour
										if (tep_not_null($payment_row['store_payments_paid_currency_value'])) {
											$stored_currency_rate = $payment_row['store_payments_paid_currency_value'];
										} else {
											$stored_currency_rate = $currencies->advance_currency_conversion_rate($payment_row['store_payments_request_currency'], $payment_row['store_payments_paid_currency']);
										}
										
										$actual_payout_amount = $request_amount * $stored_currency_rate;
										$rounded_actual_payout_amount = number_format($actual_payout_amount, $this->currency_display_decimal, '.', '');
										
										$rounded_tax_amount = number_format($tax_amount, $this->currency_display_decimal, '.', '');
										$csv_content_array[] = $rounded_actual_payout_amount;
										$is_g2g = c2c_invoice::check_g2g_withdraw($payID);
										if($is_g2g == true){
											$csv_content_array[] = strtoupper(sprintf(TEXT_G2G_PAYMENT_REMARKS, $payID));
										}
										else{
										$csv_content_array[] = strtoupper(sprintf(TEXT_PAYMENT_REMARKS, $payID));
										}
										
										$total_payment_count++;
										$total_payment_amount += $rounded_actual_payout_amount;
										
										for ($excelCnt=0; $excelCnt < count($csv_content_array); $excelCnt++) {
											//$worksheet->write($body_row, $excelCnt, tep_mb_convert_encoding($csv_content_array[$excelCnt], "GB2312", "UTF-8"));
											$worksheet->write($body_row, $excelCnt, html_entity_decode($csv_content_array[$excelCnt], ENT_QUOTES, "utf-8"));
										}
										
										$body_row++;
										$body_column_count++;
									}
								}
								
								$worksheet->write(0, 0, T_1_CSV_HEADER_BATCH_ID);
								$worksheet->write(0, 1, T_1_CSV_HEADER_PAYMENT_DATE);
								$worksheet->write(0, 2, T_1_CSV_HEADER_PAYER_EMAIL);
								$worksheet->write(0, 3, T_1_CSV_HEADER_PAYER_ACCOUNT_NAME);
								$worksheet->write(0, 4, T_1_CSV_HEADER_TOTAL_PAY_AMOUNT);
								$worksheet->write(0, 5, T_1_CSV_HEADER_TOTAL_PAYMENT_TRANSACTION);
								
								$worksheet->write(1, 0, date('Ymd'));
								$worksheet->write(1, 1, date('Ymd'));
								$worksheet->write(1, 2, T_1_CSV_HEADER_PAYER_EMAIL_VALUE);
								$worksheet->write(1, 3, T_1_CSV_HEADER_PAYER_ACCOUNT_NAME_VALUE);
								$worksheet->write(1, 4, $total_payment_amount);
								$worksheet->write(1, 5, $total_payment_count);
								
								$worksheet->write(2, 0, T_1_CSV_HEADER_PAYMENT_ID);
								$worksheet->write(2, 1, T_1_CSV_HEADER_PAYEE_EMAIL);
								$worksheet->write(2, 2, T_1_CSV_HEADER_PAYEE_NAME);
								$worksheet->write(2, 3, T_1_CSV_HEADER_PAY_AMOUNT);
								$worksheet->write(2, 4, T_1_CSV_HEADER_PAYMENT_NOTE);
							break;
						}
						break;
				}
			}
			
			if ($body_column_count) {
				$filename = 'mass_payment_'.date('YmdHis').'.xls';
				
				$workbook->send($filename);
				
				// We still need to explicitly close the workbook
				$workbook->close();
				
				exit();
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				return false;
			}
		} else {
			$messageStack->add_session(ERROR_PAYMENT_BATCH_MASS_PAYMENT_NOT_SUPPORTED, 'error');
			return false;
		}
	}
	
	function add_comment($input_array, &$messageStack) {
	    $error = false;

        if (tep_not_null($input_array['comments'])) {
            // Insert account statement comment
        	$account_comment_data_array = array('store_account_history_id' => (int)$input_array['store_account_history_id'],
        						                'store_account_comments' => tep_db_prepare_input($input_array['comments']),
        						                'store_account_comments_date_added' => 'now()',
        						                'store_account_comments_notified' => $input_array['notify'],
        						                'store_account_comments_added_by' => $this->identity_email
        						                );
    		tep_db_perform(TABLE_STORE_ACCOUNT_COMMENTS, $account_comment_data_array);

            $messageStack->add_session(SUCCESS_ACC_COMMENTS, 'success');

            $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
            while ($configuration = tep_db_fetch_array($configuration_query)) {
                define($configuration['cfgKey'], $configuration['cfgValue']);
            }
			
            $email = sprintf(EMAIL_ACC_STAT_TEXT_TITLE, $input_array['store_account_history_id']) . "\n" .
                     EMAIL_SEPARATOR . "\n\n" .
                     sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_NUMBER, $input_array['store_account_history_id']) . "\n" . 
                     sprintf(EMAIL_ACC_STAT_TEXT_TRANSACT_DATE, date("Y-m-d H:i:s")) . "\n" .
                     sprintf(EMAIL_ACC_STAT_TEXT_ACTIVITY, $input_array['activity_title']) . "\n\n" .
                     sprintf(EMAIL_ACC_STAT_TEXT_COMMENTS, $input_array['comments']) . "\n" . 
					 sprintf(EMAIL_G2G_BUYBACK_CLOSE, G2G_EMAIL_TO) . "\n\n" .
					 sprintf(EMAIL_G2G_BUYBACK_ORDER_CLOSING, G2G_STORE_NAME) . "\n\n" .
					 G2G_EMAIL_FOOTER;

            // send email to notify supplier
            if (isset($input_array['notify']) && tep_not_null($input_array['user_id']) && tep_not_null($input_array['user_role'])) {
                $user_info_array = $this->_get_user_particulars($input_array['user_id'], $input_array['user_role']);

                $email_greeting = tep_get_email_greeting($user_info_array['fname'], $user_info_array['lname'], $user_info_array['gender']);

        		$email = $email_greeting . $email;

        		@tep_mail($user_info_array['fname'].' '.$user_info_array['lname'], $user_info_array['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_UPDATE_SUBJECT, $input_array['store_account_history_id']))), $email, "<EMAIL>", "<EMAIL>");

                $messageStack->add_session(SUCCESS_ACC_COMMENTS_EMAIL, 'success');
            }

            $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				@tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_ACC_STAT_UPDATE_SUBJECT, $input_array['store_account_history_id']))), $email, STORE_OWNER, "<EMAIL>");
			}
        } else {
            $error = true;
		    $messageStack->add_session(ERROR_ACC_STAT_COMMENTS_EMPTY, 'error');
        }

	    if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	// Refund module
	function search_refund_list($filename, $session_name) {
		$payment_list_html = '';
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
	  								array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50")
								);
		$status_options = $this->_get_payment_status();
		
		$payment_options = $this->_get_all_active_receive_payment_methods(false);
		
		$date_type_array = array();
		
		$date_type_array[] = array ('id' => 0, 'text' => TEXT_ENTRY_DATE);
		
		$total_status_entry = count($status_options);
		for ($status_cnt=0; $status_cnt < $total_status_entry; $status_cnt++) {
			$date_type_array[] = array ('id' => $status_options[$status_cnt]['id'], 'text' => sprintf(TEXT_LAST_STATUS_DATE, $status_options[$status_cnt]['text']));
		}
		ob_start();
		?>
	  	<table width="100%" border="0" cellpadding="2" cellspacing="0">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
						</tr>
						<tr>
        					<td>
        						<?=tep_draw_form('refund_list_criteria', $filename, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main"><?=ENTRY_REFUND_START_DATE?></td>
										<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION[$session_name]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.refund_list_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.refund_list_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
    									<td class="main" width="8%">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=ENTRY_REFUND_END_DATE?></td>
    									<td class="main">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION[$session_name]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.refund_list_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.refund_list_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
			    						<td valign="top"><?=tep_draw_separator('pixel_trans.gif', '40', '1') . tep_draw_pull_down_menu('date_type', $date_type_array, $_SESSION[$session_name]["date_type"], ' id="date_type" ')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
	          							<td class="main"><?=ENTRY_REFUND_ID?></td>
	          							<td class="main" colspan="6"><?=tep_draw_input_field('refund_id', $_SESSION[$session_name]["refund_id"], ' id="refund_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td valign="top" class="main"><?=ENTRY_REFUND_STATUS?></td>
						    			<td class="main" colspan="6">
						    				<table border="0" cellspacing="2" cellpadding="0">
						    			<?
						    				if (count($status_options)) {
					    						echo '<tr><td class="main">'.tep_draw_checkbox_field('refund_status_any', '1', isset($_SESSION[$session_name]) ? (count($_SESSION[$session_name]["refund_status"]) ? false : true) : false, '', 'id="refund_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>';
					    						echo '<tr>';
					    						for ($status_cnt=0; $status_cnt < count($status_options); $status_cnt++) {
					    							$status_display_str = '';
													$id = $status_options[$status_cnt]['id'];
							    					$title = $status_options[$status_cnt]['text'];
							    					
						    						$status_display_str .=
						    							'	<td class="main">'.
						    									tep_draw_checkbox_field('refund_status[]', $id, isset($_SESSION[$session_name]) ? (is_array($_SESSION[$session_name]["refund_status"]) && in_array($id, $_SESSION[$session_name]["refund_status"]) ? true : false) : ( $id=="2" ? true : false), '', 'onClick=verify_status_selection();') . '
						    								</td>
						    								<td class="main">'.$title.'</td>';
					    							echo $status_display_str;
						    					}
						    					echo '</tr>';
						    				}
						    			?>
						    				</table>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td valign="top" class="main"><?=ENTRY_REFUND_CHECKOUT_METHOD?></td>
						    			<td class="main">
						    			<?
							    			if (count($payment_options)) {
					    						echo tep_draw_checkbox_field('payment_method_any', '1', isset($_SESSION[$session_name]) && count($_SESSION[$session_name]["payment_method"]) ? false : true, '', 'id="payment_method_any" onClick="set_payment_option(this);"') . '&nbsp;' . TEXT_ANY . '<br>';
					    					}
						    				for ($pm_cnt=0; $pm_cnt < count($payment_options); $pm_cnt++) {
						    					$id = $payment_options[$pm_cnt]['id'];
						    					$title = $payment_options[$pm_cnt]['text'];
						    					echo tep_draw_checkbox_field('payment_method[]', $id, isset($_SESSION[$session_name]) ? (is_array($_SESSION[$session_name]["payment_method"]) && in_array($id, $_SESSION[$session_name]["payment_method"]) ? true : false) : false, '', 'onClick=verify_payment_selection();') . "&nbsp;" . $title . '<br>';
						    				}
						    			?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
						    			<td class="main">&nbsp;</td>
			    						<td colspan="4">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
		<script language="javascript"><!--
			function form_checking(form_obj, action) {
			    //form_obj.submit();
				return true;
    		}
    		
			function resetControls(controlObj) {
				if (trim_str(controlObj.value) != '') {
					document.refund_list_criteria.start_date.value = '';
					document.refund_list_criteria.end_date.value = '';
					document.refund_list_criteria.show_records.selectedIndex = 0;
					document.refund_list_criteria.payment_method_any.checked = true;
					set_payment_option(document.refund_list_criteria.payment_method_any);
	    		} else {
	    			controlObj.value = '';
	    		}
			}
			
			function set_payment_option(any_pay_obj) {
    			var multi_pay_select = document.refund_list_criteria.elements['payment_method[]'];
    			if (any_pay_obj.checked == true) {
					for (i=0;i<multi_pay_select.length;i++) {
						multi_pay_select[i].checked = false;
					}
    			} else {	// force to check if no any payment option is selected
    				var selected_count = 0;
    				for (i=0;i<multi_pay_select.length;i++) {
						if (multi_pay_select[i].checked == true) {
							selected_count++;
						}
					}
					if (!selected_count) {
						any_pay_obj.checked = true;
					}
    			}
    		}
    		
    		function verify_payment_selection() {
    			var multi_pay_select = document.refund_list_criteria.elements['payment_method[]'];
    			var selected_count = 0;
				for (i=0;i<multi_pay_select.length;i++) {
					if (multi_pay_select[i].checked == true) {
						selected_count++;
					}
				}
				if (!selected_count) {
					document.getElementById('payment_method_any').checked = true;
				} else {
					document.getElementById('payment_method_any').checked = false;
				}
    		}
    		
			function set_status_option(any_status_obj) {
    			var multi_status_select = document.refund_list_criteria.elements['refund_status[]'];
    			if (any_status_obj.checked == true) {
					for (i=0;i<multi_status_select.length;i++) {
						multi_status_select[i].checked = false;
					}
    			} else {	// force to check if no any order status option is selected
    				var selected_count = 0;
    				for (i=0;i<multi_status_select.length;i++) {
    					if (multi_status_select[i].checked == true) {
							selected_count++;
						}
					}
					if (!selected_count) {
						any_status_obj.checked = true;
					}
    			}
    		}
    		
    		function verify_status_selection() {
    			var multi_status_select = document.refund_list_criteria.elements['refund_status[]'];
    			var selected_count = 0;
				for (i=0;i<multi_status_select.length;i++) {
					if (multi_status_select[i].checked == true) {
						selected_count++;
					}
				}
				if (!selected_count) {
					document.getElementById('refund_status_any').checked = true;
				} else {
					document.getElementById('refund_status_any').checked = false;
				}
    		}
    		
    		set_status_option(document.getElementById('refund_status_any'));
    	//-->
		</script>
		<?
		$refund_list_html = ob_get_contents();
		ob_end_clean() ;
		
		return $refund_list_html;
	}
	
	function show_refund_list($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id, $paypal_manual_refund_permission;
		
		$pm_scope_js_array = array();
		$pay_scope_js_array = array();
		
		if (!$_REQUEST['cont']) {
			if (isset($_REQUEST["serialise"]) && $_REQUEST["serialise"] == '1') {	// serialized status
				if (isset($_REQUEST["payment_methods_parent_id"])) {
					$input_array["payment_method"] = tep_array_unserialize(rawurldecode($_REQUEST["payment_methods_parent_id"]));
				}
			}
			
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["date_type"] = $input_array["date_type"];
			$_SESSION[$session_name]["refund_id"] = $input_array["refund_id"];
			$_SESSION[$session_name]["refund_status"] = $input_array["refund_status"];
			$_SESSION[$session_name]["payment_method"] = $input_array["payment_method"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
		}

		$group_by_str = $having_str = '';
		
		$refund_id_str = (isset($_SESSION[$session_name]["refund_id"]) && tep_not_null($_SESSION[$session_name]["refund_id"])) ? " sr.store_refund_id='" . $_SESSION[$session_name]["refund_id"] . "'" : "1";
		
		$start_date_str = $end_date_str = " 1 ";
		$having_str = '';
		if (tep_not_null($_SESSION[$session_name]["start_date"])) {
			if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
				$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				if ((int)$_SESSION[$session_name]["date_type"] > 0) {
					$having_str .= " HAVING latest_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' ";
				} else {
					$start_date_str = " ( sr.store_refund_date  >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
				}
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
				if ((int)$_SESSION[$session_name]["date_type"] > 0) {
					$having_str .= " HAVING latest_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."' ";
				} else {
					$start_date_str = " ( sr.store_refund_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."' )";
				}
			}
		}
		
		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				if ((int)$_SESSION[$session_name]["date_type"] > 0) {
					if (tep_not_null($having_str)) {
						$having_str .= " AND latest_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' ";
					} else {
						$having_str .= " HAVING latest_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' ";
					}
				} else {
					$end_date_str = " ( sr.store_refund_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
				}
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
				if ((int)$_SESSION[$session_name]["date_type"] > 0) {
					if (tep_not_null($having_str)) {
						$having_str .= " AND latest_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' ";
					} else {
						$having_str .= " HAVING latest_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' ";
					}
				} else {
					$end_date_str = " ( sr.store_refund_date <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' )";
				}
			}
		}
		
		if (isset($_SESSION[$session_name]["refund_status"]) && is_array($_SESSION[$session_name]["refund_status"]) && count($_SESSION[$session_name]["refund_status"])) {
			$refund_status_str = " sr.store_refund_status IN ('" . implode("', '", $_SESSION[$session_name]["refund_status"]) . "') ";
		} else {
			$refund_status_str = " 1 ";
		}
		
		if ((int)$_SESSION[$session_name]["date_type"] > 0) {
			$sql_select_str = "SELECT sr.*, o.currency, o.currency_value, o.payment_method, o.payment_methods_parent_id, o.payment_methods_id, MAX(date_added) AS latest_date FROM " . TABLE_STORE_REFUND . " AS sr INNER JOIN " . TABLE_ORDERS . " AS o ON sr.store_refund_trans_id=o.orders_id INNER JOIN " . TABLE_STORE_REFUND_HISTORY . " AS srh ON (srh.store_refund_status = '" . (int)$_SESSION[$session_name]["date_type"] . "' AND sr.store_refund_id = srh.store_refund_id) ";
			$group_by_str = " GROUP BY sr.store_refund_id ";
		} else {
			$sql_select_str = "SELECT sr.*, o.currency, o.currency_value, o.payment_method, o.payment_methods_parent_id, o.payment_methods_id FROM " . TABLE_STORE_REFUND . " AS sr INNER JOIN " . TABLE_ORDERS . " AS o ON sr.store_refund_trans_id=o.orders_id ";
			$group_by_str = "";
		}
		
		$sql_where_str = "INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei ON o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id' AND oei.orders_extra_info_value != '5' WHERE 1 ";
		$sql_where_str .= " and $refund_id_str and $start_date_str and $end_date_str and $refund_status_str ";
		
		$sql_order_by_str = " order by sr.store_refund_id DESC ";
		
		$show_payment_method = isset($_SESSION[$session_name]["payment_method"]) ? $_SESSION[$session_name]["payment_method"] : array();
		if (count($show_payment_method)) {

		} else {
			$payment_options = $this->_get_all_active_receive_payment_methods(false);

			for ($pm_cnt=0; $pm_cnt < count($payment_options); $pm_cnt++) {
				$show_payment_method[] = $payment_options[$pm_cnt]['id'];
			}
			$show_payment_method[] = 0; //Add '0' for Store Credit
		}

		$show_records = $_SESSION[$session_name]["show_records"];
		
		$result_display_text = TEXT_DISPLAY_NUMBER_OF_PAYMENTS;
		
		$payment_methods_filename_array = $this->_get_payment_method();
		
		$payment_gateway_array = array();
		$payment_gateway_select_sql = "	SELECT pg_id, pm_id, pg_display_name
										FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . "
										GROUP BY pg_id, pm_id";
		$payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
		while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
			$payment_gateway_array[$payment_gateway_row['pg_id'].$payment_gateway_row['pm_id']] = $payment_gateway_row['pg_display_name'];
		}

		for ($pm_cnt = 0; $pm_cnt < count($show_payment_method); $pm_cnt++) {
			$payment_reference_input = false;
			$total_pm_payout = 0;
			
			$total_colspan = 11;
			
			$pm_id = $show_payment_method[$pm_cnt];
			if ($pm_id == 0){
				$trans_select_sql = $sql_select_str . $sql_where_str . " and sr.store_refund_payments_methods_id = '" . tep_db_input($pm_id) . "' and sr.store_refund_payments_methods_id = '" . tep_db_input($pm_id) . "' " . $group_by_str . $having_str . $sql_order_by_str;
			} else {
				$trans_select_sql = $sql_select_str . $sql_where_str . " and o.payment_methods_parent_id = '" . tep_db_input($pm_id) . "' and sr.store_refund_payments_methods_id IS NULL or sr.store_refund_payments_methods_id = '" . tep_db_input($pm_id) . "' " . $group_by_str . $having_str . $sql_order_by_str;
			}

			if ($show_records != "ALL") {
				$trans_split_object = new splitPageResults($_REQUEST['page'.$pm_cnt], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $trans_select_sql, $trans_select_sql_numrows, true);
			}
			$trans_result_sql = tep_db_query($trans_select_sql);
			ob_start();

			$form_name = 'refund_'.$pm_cnt.'_lists_form';
			$form_param = tep_get_all_get_params(array('action')) . 'action=batch_action';
		?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
		  			<td>
		  			<?
		  				echo tep_draw_form($form_name, $filename, $form_param, 'post', '');
		  			?>
		  				<table border="0" width="100%" cellspacing="1" cellpadding="1">
							<tr>
								<td colspan="14">
									<span class="pageHeading">
		<?
			if ($pm_id != 0){
				$payment_methods_obj = new payment_methods($pm_id);
				$payment_methods_obj = $payment_methods_obj->payment_method_array;
				echo $payment_methods_obj->title;
			} else {
				echo "Store Credit";
			}
		?>
									</span>
								</td>
							</tr>
							<tr>
								<td width="8%" class="reportBoxHeading" nowrap><?=TABLE_HEADING_REFUND_ID?></td>
								<td width="8%" class="reportBoxHeading" nowrap><?=TABLE_HEADING_REFUND_ORDER_ID?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_REFUND_USER?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_REFUND_USER_EMAIL?></td>
								<td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_PAYMENT_ORG_REF?></td>
								<td width="10%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_BENEFICIARY_INFO?></td>
								<td width="10%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_PAYMENT_METHOD?></td>
								<td width="10%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_PAYMENT_ENTITY?></td>
								<td width="10%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_PAYMENT_CURRENCY?></td>
								<td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_ORDER_TOTAL_AMOUNT?></td>
							    <td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_REFUND_AMOUNT?></td>
							    <td width="13%" class="reportBoxHeading" align="center"><?=TABLE_HEADING_REFUND_REFERENCE?></td>
							    <td width="5%" class="reportBoxHeading">&nbsp;</td>
							    <td width="10%" class="reportBoxHeading" align="center"><?=TABLE_HEADING_REFUND_ACTION?></td>
							    <td width="1%" class="reportBoxHeading"><?=tep_draw_checkbox_field('select_all_'.$pm_cnt, '', false, '', 'id="select_all_'.$pm_cnt.'" title="Select or deselect all refund records" onClick="javascript:void(setActiveCheckboxes(\''.$form_name.'\',\'select_all_'.$pm_cnt.'\',\'refund_batch\')); update_selected_price(this.form, \''.$pm_cnt.'\', \'refund_batch\'); "')?></td>
							</tr>
							<tbody>
		<?
			$row_count = 0;
			while ($trans_row = tep_db_fetch_array($trans_result_sql)) {
				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
				$refund_id = $trans_row['store_refund_id'];	// Group by trans id
				$order_id = $trans_row['store_refund_trans_id'];	// Group by trans id
				$order_total_amount = $trans_row['store_refund_trans_total_amount'];
				$refund_amount = $trans_row['store_refund_amount'];
				
				/*-- GST : refund GST amount --*/
				$refund_gst_amount = $refund_surcharge_amount = '';
				$refund_gst_select_sql = "	SELECT store_refund_gst_amount, store_refund_surcharge_amount
											FROM " . TABLE_STORE_REFUND_INFO . " 
											WHERE store_refund_id = '" . $refund_id . "'";
				$refund_gst_result_sql = tep_db_query($refund_gst_select_sql);
				if ($refund_gst_row = tep_db_fetch_array($refund_gst_result_sql)) {
					$refund_gst_amount = $refund_gst_row['store_refund_gst_amount'];
					$refund_surcharge_amount = $refund_gst_row['store_refund_surcharge_amount'];
				}
				
				$store_refund_is_processed = $trans_row['store_refund_is_processed'];
				
				$payment_batch_available = false;
				
				$user_info_array = $this->_get_user_particulars($trans_row['user_id'], 'customers');
				$might_be_rollback = $this->_check_refund_rollback($trans_row['store_refund_trans_id'], $trans_row['store_refund_date']);
				
				$order_id_link = '<a href="'.tep_href_link(FILENAME_ORDERS, 'oID='.$order_id.'&action=edit').'" target="_blank">'.$order_id.'</a>';
				$user_name_link = '<a href="'.tep_href_link(FILENAME_CUSTOMERS, 'cID='.$trans_row['user_id'].'&action=edit').'" target="_blank">'.$trans_row['user_firstname'] . ' ' . $trans_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
				
				$original_payment_gateway_trans_id = $this->_get_payment_gateway_trans_id($order_id, $pm_id);
				if (isset($payment_methods_filename_array[$trans_row['payment_methods_parent_id']]) && $payment_methods_filename_array[$trans_row['payment_methods_parent_id']] == 'paypalEC.php') {
					$payment_required_fields = $this->_get_required_receive_payment_info($order_id, $pm_id, 'paypalEC.php');
				} else {
					$payment_required_fields = $this->_get_required_receive_payment_info($order_id, $pm_id);
				}	
				
				if ($trans_row['store_refund_status'] == '2' && !$store_refund_is_processed) {
					$payment_reference_str = tep_draw_input_field('payment_reference['.$refund_id.']', $trans_row['store_refund_payments_reference'], ' id="payment_reference_'.$refund_id.'" size="16" onKeyPress="return noEnterKey(event)" ');
					$payment_reference_input = true;
				} else {
					$payment_reference_str = $trans_row['store_refund_payments_reference'];
				}
				
				$pay_scope_js_array[$refund_id]['payout_amount'] = $refund_amount;
				$total_pm_payout += $refund_amount;

                                list($action_button_html, $payment_batch_available) = $this->_get_action_button($order_id, $refund_id, $filename, $pm_cnt, $trans_row, $payment_methods_filename_array, $payment_batch_available);
				
				if ($might_be_rollback || $currencies->apply_currency_exchange($refund_amount, $trans_row['currency'], $trans_row['currency_value']) < $currencies->apply_currency_exchange($order_total_amount, $trans_row['currency'], $trans_row['currency_value'])) {
					$custom_style_css = 'class="redIndicator"';
				} else {
					$custom_style_css = '';
				}

				// Payment methods name
				$payment_methods_name = $payment_gateway_array[$trans_row['payment_methods_parent_id'].$trans_row['payment_methods_id']] . ' - ';
				if ($trans_row['payment_method']) {
					$payment_methods_name .= $trans_row['payment_method'];
				} else {
					$pm_child_obj = new payment_methods($trans_row['payment_methods_id']);
					$pm_child_obj = $pm_child_obj->payment_method_array;
					$payment_methods_name .= $pm_child_obj->title;
					unset($pm_child_obj);
				}
                                
				$this->_get_refund_info($refund_id);
				if ($order_total_amount >= $this->ot['ot_total']['value']) {
					$order_total_amount = $this->ot['ot_total']['text'];
				} else {
					$order_total_amount = $currencies->apply_currency_exchange($order_total_amount, $trans_row['currency'], $trans_row['currency_value']);
				}

				if ($refund_amount >= $this->ot['ot_total']['value']) {
					$refund_amount = $this->ot['ot_total']['text'];
				} else {
					$refund_amount = $currencies->apply_currency_exchange($refund_amount, $trans_row['currency'], $trans_row['currency_value']);
				}
				
				if ($refund_gst_amount >= $this->ot['ot_gst']['value']) {
					$refund_gst_amount = $this->ot['ot_gst']['text'];
				} else {
					$refund_gst_amount = $currencies->apply_currency_exchange($refund_gst_amount, $trans_row['currency'], $trans_row['currency_value']);
				}

				if ($refund_surcharge_amount >= $this->ot['ot_surcharge']['value']) {
					$refund_surcharge_amount = $this->ot['ot_surcharge']['text'];
				} else {
					$refund_surcharge_amount = $currencies->apply_currency_exchange($refund_surcharge_amount, $trans_row['currency'], $trans_row['currency_value']);
				}

				echo '		<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$refund_id.'</span></td>
								<td valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$order_id_link.'</span></td>
								<td valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$user_name_link.'</span></td>
								<td valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$user_info_array['email'].'</span></td>
								<td align="center" valign="top" class="reportRecords"><span '.$custom_style_css.'>'.$original_payment_gateway_trans_id.'</span></td>
								<td align="left" valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>';
				
									for ($field_cnt=0; $field_cnt < count($payment_required_fields); $field_cnt++) {
										//echo $payment_required_fields[$field_cnt]['title'] . ': ' . $payment_required_fields[$field_cnt]['value'] . '<br>';
										echo $payment_required_fields[$field_cnt]['value'] . '<br>';
									}

				echo '				</span>
								</td>
								<td align="left" valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$payment_methods_name.'</span></td>
								<td align="left" valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$this->_get_settlement_account($order_id).'</span></td>
								<td align="left" valign="top" class="reportRecords" nowrap><span '.$custom_style_css.'>'.$trans_row['currency'].'</span></td>
								<td align="right" valign="top" class="reportRecords"><span '.$custom_style_css.'>'.$currencies->custom_format($order_total_amount, false, $trans_row['currency']).'</span></td>
								<td align="right" valign="top" class="reportRecords"><span '.$custom_style_css.'>'.$currencies->custom_format($refund_amount, false, $trans_row['currency']).((tep_not_null($refund_gst_amount) && $refund_gst_amount > 0) ? '<br />' . sprintf(TEXT_GST, $currencies->custom_format($refund_gst_amount, false, $trans_row['currency'])) : '') . ((tep_not_null($refund_surcharge_amount) && $refund_surcharge_amount > 0) ? '<br />' . sprintf(TEXT_SURCHARGE, $currencies->custom_format($refund_surcharge_amount, false, $trans_row['currency'])) : '') . '</span></td>
								<td align="center" valign="top" class="reportRecords"><span '.$custom_style_css.'>'.$payment_reference_str.'</span></td>
								<td align="center" valign="top" class="reportRecords">
									<span '.$custom_style_css.'>
									<a href="' . tep_href_link($filename, 'refID='.$refund_id.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>
									</span>
								</td>
								<td align="center" valign="top" class="reportRecords" nowrap>'.
									$action_button_html . '
								</td>
								<td class="reportRecords" valign="top">'.tep_draw_checkbox_field('refund_batch[]', $refund_id, false, '', 'id="'.$refund_id.'" onClick="update_selected_price(this.form, \''.$pm_cnt.'\', \'refund_batch\');" '.(!$payment_batch_available ? ' DISABLED ' : '')).'</td>
						  	</tr>';
				
				$row_count++;
			}
			
			$batch_action_array = array(array('id' => '', 'text' => 'With selected:'));
			$batch_action_array[] = array('id' => 'PayRef', 'text' => 'Update Payment Reference');
			
			//if ($payment_methods_obj->payment_methods_id =='25' || $payment_methods_obj->payment_methods_parent_id =='25') {
			if ($payment_methods_obj->filename == 'paypal.php') {
				$batch_action_array[] = array('id' => 'ApiRefund', 'text' => 'Paypal Refund API');
			} else if ($payment_methods_obj->filename == 'paypalEC.php') {
				$batch_action_array[] = array('id' => 'PaypalECApiRefund', 'text' => 'PaypalEC Refund API');
			} else if ($payment_methods_obj->filename == 'alipay.php') {
				$batch_action_array[] = array('id' => 'alipay_batch_refund', 'text' => 'Alipay Refund API');
			} else if ($payment_methods_obj->filename == 'global_collect.php') {
				$batch_action_array[] = array('id' => 'global_collect_batch_refund', 'text' => 'Global Collect Refund API');
			} else if ($payment_methods_obj->filename == 'bibit.php') {
				$batch_action_array[] = array('id' => 'worldpay_batch_refund', 'text' => 'WorldPay Refund API');
			}
			
			echo '			<tr>
								<td align="left" class="ordersRecords" colspan="3">'.TEXT_TOTAL_OVERALL_AMOUNT.' <b>'.$currencies->format($total_pm_payout, false).'</b></td>
								<td align="right" colspan="'.($total_colspan-3).'">
									<table border="0" cellspacing="0" cellpadding="2">
										<tr>
											<td width="100px" align="right" class="ordersRecords">'.TEXT_TOTAL_SELECTED_AMOUNT.'</td>
											<td width="100px" class="ordersRecords"><span id="total_sel_amt_'.$pm_cnt.'" class="redIndicator"></span></td>
											<td width="20px" align="right" class="ordersRecords">'.TEXT_TOTAL_SELECTED_RECORDS.'</td>
											<td width="20px" class="ordersRecords"><span id="total_sel_rec_'.$pm_cnt.'" class="redIndicator"></span></td>
											<td width="230px" align="right" nowrap>';
			
			if (count($batch_action_array) > 1) {
				echo	tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;' . tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction(this.form)"', 'inputButton');
			}
			
			echo '							</td>
										</tr>
									</table>
								</td>
							</tr>';
		?>
							</tbody>
						</table>
						</form>
					</td>
				</tr>
				<tr>
					<td colspan="<?=$total_colspan?>">
						<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
		  					<tr>
		    					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($trans_result_sql) > 0 ? "1" : "0", tep_db_num_rows($trans_result_sql), tep_db_num_rows($trans_result_sql)) : $trans_split_object->display_count($trans_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'.$pm_cnt], $result_display_text)?></td>
		    					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_PAYMENTS, '1', '1') : $trans_split_object->display_links($trans_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'.$pm_cnt], tep_get_all_get_params(array('page'.$pm_cnt, 'cont'))."cont=1", 'page'.$pm_cnt)?></td>
		  					</tr>
						</table>
					</td>
				</tr>
			</table><br>
		<?
			$report_section_html = ob_get_contents();
			ob_end_clean();
			
			$pm_scope_js_array[$pm_cnt]['currency_code'] = DEFAULT_CURRENCY;
			$pm_scope_js_array[$pm_cnt]['symbol_left'] = $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'];
			$pm_scope_js_array[$pm_cnt]['symbol_right'] = $currencies->currencies[DEFAULT_CURRENCY]['symbol_right'];
			
			$parsed_report_section_html .= $report_section_html;
		}
		
		ob_start();
		?>
		<script language="javascript">
			<!--
			var currencyInfoArray = new Array();
			var paymentAmountArray = new Array();
			<?
				foreach ($pm_scope_js_array as $id => $pm_info) {
					echo "currencyInfoArray[".$id."] = new Array();\n";
					
					if (isset($pm_info['currency_code'])) {
						echo "currencyInfoArray[".$id."]['currency_code'] = '" . $pm_info['currency_code'] . "';\n";
					}
					
					if (isset($pm_info['symbol_left']))	{
						echo "currencyInfoArray[".$id."]['symbol_left'] = '" . $pm_info['symbol_left'] . "';\n";
					}
					
					if (isset($pm_info['symbol_right']))	{
						echo "currencyInfoArray[".$id."]['symbol_right'] = '" . $pm_info['symbol_right'] . "';\n";
					}
				}
				
				foreach ($pay_scope_js_array as $id => $pay_info) {
					if (isset($pay_info['payout_amount']))	echo "paymentAmountArray['".$id."'] = ".$pay_info['payout_amount'].";\n";
				}
			?>
			function update_selected_price(frmObj, pmID, checkName) {
				var total_sel_amount = 0;
				var any_box_selected = 0;
				var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
		 						? frmObj.elements[checkName+'[]']
		 						: "";
				var elts_cnt  = (typeof(elts.length) != 'undefined')
								? elts.length
								: 0;
				
				if (elts_cnt) {
			        for (var i=0; i < elts_cnt; i++) {
			            e = elts[i];
			            
			            if (e.type=='checkbox' && e.checked) {
			            	any_box_selected++;
			            	if (paymentAmountArray[e.value] != null) {
				            	total_sel_amount += paymentAmountArray[e.value];
			            	}
						}
			        } // end for
			    } else if (elts != '') {
			    	e = elts;
			        if (e.type=='checkbox' && e.checked) {
			        	any_box_selected++;
			        	if (paymentAmountArray[e.value] != null) {
			        		total_sel_amount += paymentAmountArray[e.value];
			            }
					}
			    }
			    
			    var total_amt_span_obj = DOMCall('total_sel_amt_' + pmID);
			    var total_sel_span_obj = DOMCall('total_sel_rec_' + pmID);
			    
			    if (any_box_selected > 0) {
					total_amt_span_obj.innerHTML = currency(total_sel_amount, currencyInfoArray[pmID]['symbol_left'], currencyInfoArray[pmID]['symbol_right'], 2);
					total_sel_span_obj.innerHTML = any_box_selected;
				} else {
					total_amt_span_obj.innerHTML = '';
					total_sel_span_obj.innerHTML = '';
				}
			}
			
			function refresh_payout_price(exRateObj, pmID, reqAmt, dec) {
				var payout_span_obj = DOMCall('span_payout_' + pmID);
				var latest_rate = exRateObj != null ? exRateObj.value : 0;
				
				if (payout_span_obj != null) {
					payout_span_obj.innerHTML = replace(currency_display(latest_rate * parseFloat(reqAmt), dec), ',', '');
				}
			}
			
			function confirmBatchAction(frmObj) {
				if (trim_str(frmObj.batch_action.value) == '') {
					alert('Please select your batch action!');
					return false;
				} else {
					return true;
				}
			}
			//-->
		</script>
		<?
		$parsed_report_section_html .= "\n" . ob_get_contents();
		ob_end_clean();
		
	  	return $parsed_report_section_html;
	}
	
	function issue_refund($user_id, $trans_array, $admin_msg, &$messageStack) {
		/*************************************************************************
		 	1. Check this user own that order
		*************************************************************************/
		$order_checking_select_sql = "	SELECT orders_id 
										FROM " . TABLE_ORDERS . "
										WHERE orders_id = '" . tep_db_prepare_input($trans_array['id']) . "' 
											AND customers_id = '" . tep_db_prepare_input($user_id) . "'";
		$order_checking_result_sql = tep_db_query($order_checking_select_sql);
		
		if ($order_checking_row = tep_db_fetch_array($order_checking_result_sql)) {
			$user_info_array = $this->_get_user_particulars($user_id, 'customers');
			
			$refund_sql_data_array = array(	'user_id' => tep_db_prepare_input($user_id),
	 	          							'user_firstname' => $user_info_array['fname'],
	 	          							'user_lastname' => $user_info_array['lname'],
	 	          							'user_email_address' => $user_info_array['email'],
	 	          							'store_refund_date' => 'now()',
	 	          							'store_refund_trans_id' => tep_db_prepare_input($trans_array['id']),
	 	          							'store_refund_status' => '1',
	 	          							'store_refund_trans_total_amount' => (double)$trans_array['order_total_amount'],
	  	         							'store_refund_amount' => (double)$trans_array['amount'],
	  	         							'store_refund_payments_methods_name' => tep_db_prepare_input($trans_array['payment_method']),
											'store_refund_payments_methods_id' => tep_db_prepare_input($trans_array['payment_methods_parent_id']),
											'store_refund_checkout_payments_methods_name' => tep_db_prepare_input($trans_array['payment_method']),
											'store_refund_checkout_payments_methods_id' => tep_db_prepare_input($trans_array['payment_methods_parent_id']),
			    	       				);

			tep_db_perform(TABLE_STORE_REFUND, $refund_sql_data_array);
			$insert_refund_id = tep_db_insert_id();
			
			if ($insert_refund_id > 0) {
				// Insert refund history
				// TODO: need to check whether notify option is checked
				$refund_history_sql_data_array = array(	'store_refund_id' => $insert_refund_id,
				           								'store_refund_status' => '1',
		    	 	          							'date_added' => 'now()',
		    	 	          							'payee_notified' => '1',
		    	 	          							'comments' => tep_db_prepare_input($admin_msg),
		    	 	          							'changed_by' => $this->identity_email,
		    	 	          							'changed_by_role' => 'admin'
		    	    	       							);
				tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
				/*
				// E-mail the beneficiary
				$this->send_payment_status_email($insert_payment_id);
				*/
				$messageStack->add_session(SUCCESS_ORDER_PAYMENT_REFUNDED, 'success');
				
				return $insert_refund_id;
			} else {
				// Refund failed
				$messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
				return false;
			}
		} else {
			// Refund failed
			$messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
			return false;
		}
	}
	
	function do_refund_batch_action($filename, $input_array, &$messageStack) {
		global $currencies;
		
		$action_res_array = array('code' => 0);
		
		switch($input_array["batch_action"]) {
			case 'global_collect_batch_refund':
    			if (is_array($input_array["refund_batch"]) && count($input_array["refund_batch"])) {
    				foreach($input_array["refund_batch"] as $refund_id) {
    					$processing_error = false;
    					$memo = '';
    					
    					$refund_info_select_sql = "	SELECT o.payment_methods_id, sr.store_refund_trans_id, sr.store_refund_status, sr.store_refund_trans_total_amount, 
    													sr.store_refund_amount, sr.store_refund_payments_methods_name, o.payment_methods_parent_id, 
    													gc.global_collect_status_id, o.currency, o.currency_value 
    												FROM " . TABLE_STORE_REFUND . " AS sr
    												INNER JOIN " . TABLE_ORDERS . " AS o
    													ON o.orders_id = sr.store_refund_trans_id
    												INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc 
    													ON o.orders_id = gc.global_collect_orders_id 
    												WHERE store_refund_id = '" . tep_db_input($refund_id) . "'";
    					$refund_info_result_sql = tep_db_query($refund_info_select_sql);
    					if ($refund_info_row = tep_db_fetch_array($refund_info_result_sql)) {
    						
    						$pm_select_sql = "	SELECT payment_methods_filename
    											FROM " . TABLE_PAYMENT_METHODS . " AS pm 
    											WHERE payment_methods_id = '".$refund_info_row['payment_methods_parent_id']."'";
    						$pm_result_sql = tep_db_query($pm_select_sql);
    						$pm_row = tep_db_fetch_array($pm_result_sql);
    						if (strtolower($pm_row['payment_methods_filename']) == 'global_collect.php') {
    							
    							require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_GLOBAL_COLLECT);
								
								$global_collect_obj = new global_collect($refund_info_row['payment_methods_id']);
								
								$global_collect_refund_returned_array = array();
								
								if ($refund_info_row['global_collect_status_id'] < 900 ) {
									if ($refund_info_row['global_collect_status_id'] < 800 || ($currencies->apply_currency_exchange($refund_info_row['store_refund_amount'], $refund_info_row['currency'], $refund_info_row['currency_value']) == $currencies->apply_currency_exchange($refund_info_row['store_refund_trans_total_amount'], $refund_info_row['currency'], $refund_info_row['currency_value']))) {
										$global_collect_refund_returned_array = $global_collect_obj->do_cancel_payment($refund_info_row['store_refund_trans_id'], $refund_id);
									}
								} else {
									$global_collect_refund_returned_array = $global_collect_obj->do_refund($refund_info_row['store_refund_trans_id'], $refund_id);
								}
								
								if (isset($global_collect_refund_returned_array['RESULT']) && strtoupper($global_collect_refund_returned_array['RESULT']) == 'OK') {
									if ($refund_info_row['global_collect_status_id'] < 900 ) {
		          						$comments = "Global Collect Cancel Payment Submitted, waiting response from IPN<br><br>";
		          					} else {
		          						$comments = "Global Collect Refund Submitted, waiting response from IPN<br><br>";
		          					}
		          					$comments .= "Response:<br>Request ID: " . $global_collect_refund_returned_array['REQUESTID'];
		          					
				          			$refund_history_sql_data_array = array(	'store_refund_id' => $refund_id,
						    		           								/*'store_refund_status' => $to_status,*/
							    	 	          							'date_added' => 'now()',
							    	 	          							'payee_notified' => '1',
							    	 	          							'comments' => $comments,
							    	 	          							'changed_by' => $_SESSION['login_email_address'],
							    	 	          							'changed_by_role' => 'admin'
						        	    	       							);
									tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
									
		          					$refund_update_sql_data_array = array(	'store_refund_is_processed' => 1);
									tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . (int)$refund_id . "'");
								} else {
									if (isset($global_collect_refund_returned_array['CODE']) && isset($global_collect_refund_returned_array['MESSAGE'])) { 
										if ($refund_info_row['global_collect_status_id'] < 900 ) {
			          						$comments = "Global Collect Cancel Payment Submitted, waiting response from IPN<br><br>";
			          					} else {
			          						$comments = "Global Collect Refund Submitted, waiting response from IPN<br><br>";
			          					}
										$comments .= "Response:<br>";
										if (isset($global_collect_refund_returned_array['CODE'])) {
											$comments .= "Code: " . $global_collect_refund_returned_array['CODE'] . "<br>";
										}
										if (isset($global_collect_refund_returned_array['MESSAGE'])) {
											$comments .= "Message: " . $global_collect_refund_returned_array['MESSAGE'] . "<br>";
										}
					          			$refund_history_sql_data_array = array(	'store_refund_id' => $refund_id,
							    		           								/*'store_refund_status' => $to_status,*/
								    	 	          							'date_added' => 'now()',
								    	 	          							'payee_notified' => '1',
								    	 	          							'comments' => $comments,
								    	 	          							'changed_by' => $_SESSION['login_email_address'],
								    	 	          							'changed_by_role' => 'admin'
							        	    	       							);
										tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
									}
									$processing_error = true;
								}
							} else {
								$processing_error = true;
							}
    					} else {
    						$processing_error = true;
    					}
    					
    					if ($processing_error) {
    						$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']), 'error');
    					}
    				}
    			}
    			break;
    		case 'alipay_batch_refund':
    			if (is_array($input_array["refund_batch"]) && count($input_array["refund_batch"])) {
    				foreach($input_array["refund_batch"] as $refund_id) {
    					$processing_error = false;
    					$memo = '';
    					
    					$refund_info_select_sql = "	SELECT sr.store_refund_trans_id, sr.store_refund_status, sr.store_refund_trans_total_amount, 
    													sr.store_refund_amount, sr.store_refund_payments_methods_name, o.payment_methods_parent_id 
    												FROM " . TABLE_STORE_REFUND . " AS sr
    												INNER JOIN " . TABLE_ORDERS . " AS o
    													ON o.orders_id = sr.store_refund_trans_id
    												WHERE store_refund_id = '" . tep_db_input($refund_id) . "'";
    					$refund_info_result_sql = tep_db_query($refund_info_select_sql);
    					if ($refund_info_row = tep_db_fetch_array($refund_info_result_sql)) {

    						$pm_select_sql = "	SELECT payment_methods_filename
    											FROM " . TABLE_PAYMENT_METHODS . " AS pm 
    											WHERE payment_methods_id = '".$refund_info_row['payment_methods_parent_id']."'";
    						$pm_result_sql = tep_db_query($pm_select_sql);
    						$pm_row = tep_db_fetch_array($pm_result_sql);
    						if (strtolower($pm_row['payment_methods_filename']) == 'alipay.php') {
    							
    							require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_ALIPAY);
    							
    							$alipay_seq_select_sql = "	SELECT alipay_refund_seq
															FROM " . TABLE_ALIPAY_REFUND_SEQ;
								$alipay_seq_result_sql = tep_db_query($alipay_seq_select_sql);
								$alipay_seq_row = tep_db_fetch_array($alipay_seq_result_sql);
								$alipay_seq = (int)$alipay_seq_row['alipay_refund_seq']+1;
								
								tep_db_query("DELETE FROM " . TABLE_ALIPAY_REFUND_SEQ . " WHERE 1;");
								tep_db_perform(TABLE_ALIPAY_REFUND_SEQ, array('alipay_refund_seq' => $alipay_seq));
								
								$alipay_sql_str = date("Ymd") . str_repeat("0",(3 - strlen($alipay_seq))) . $alipay_seq;
								
								$alipay_info_select_sql = "	SELECT alipay_trade_no 
															FROM " . TABLE_ALIPAY . " 
															WHERE alipay_orders_id = '" . tep_db_input($refund_info_row['store_refund_trans_id']) . "'";
								$alipay_info_result_sql = tep_db_query($alipay_info_select_sql);
								$alipay_info_row = tep_db_fetch_array($alipay_info_result_sql);
								
								$transaction_id = $alipay_info_row['alipay_trade_no'];
								$transaction_user_id = $alipay_info_row['alipay_buyer_id'];
								
								$refund_type = 'Full';
								$memo = '';
								
								$currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_info_row['store_refund_trans_id']) . "'";
								$currency_result_sql = tep_db_query($currency_select_sql);
								$currency_row = tep_db_fetch_array($currency_result_sql);

                                                                $this->_get_refund_info($refund_id);
                                                                if ($refund_info_row['store_refund_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_refund_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }
								
                                                                if ($refund_info_row['store_refund_trans_total_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_total_order_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }

								if ($formatted_refund_amount < $formatted_total_order_amount) {
									$refund_type = 'Partial';
								}

								//====================pipwave / crew refund API ====================
								$pipwave = new pipwave($refund_info_row['store_refund_trans_id']);
								if(isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
									$result = $pipwave->refundAPI($formatted_refund_amount);
									if(isset($result['status']) && $result['status'] == 200) {

									} else {
										$processing_error = true;
									}
								} else {
									//crew proceed own refund API for non pipwave checkout
									$alipay_obj = new alipay();
									$alipay_obj->get_merchant_account($currency_row['currency']);

									$alipay_parameter = array(	"service" => "refund_fastpay_by_platform_nopwd",
										"partner" => $alipay_obj->partner_id,
										"notify_url" => tep_catalog_href_link(FILENAME_ALIPAY_IPN),
										"_input_charset" => $alipay_obj->input_charset,
										"batch_no" => $alipay_sql_str,
										"refund_date" => date("Y-m-d H:i:s"),
										"batch_num" => "1",
										"detail_data"=> $transaction_id . "^".$formatted_refund_amount."^" . $refund_id,
										"return_type" => "xml"
									);

									require_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/alipay_service.php');

									$alipay_service_obj = new alipay_service($alipay_parameter, $alipay_obj->security_code, $alipay_obj->sign_type);
									$alipay_link = $alipay_service_obj->create_url();
									$response = $alipay_obj->curl_connect($alipay_link);

									require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
									$xml_array_obj = new ogm_xml_to_ary($response, 'content');
									$xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

									if (!isset($xml_data_array['alipay']['_c']['is_success']['_v']) || strtolower($xml_data_array['alipay']['_c']['is_success']['_v']) != 't') {
										$processing_error = true;
									}
								}

								//====================pipwave / crew refund API ====================
							} else {
								$processing_error = true;
							}
    					} else {
    						$processing_error = true;
    					}
    					
    					if ($processing_error) {
							if(isset($result['status']) && $result['message']) {
								$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $result['status'], $result['message']), 'error');
							} else {
								$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']), 'error');
							}
    					} else { // Insert exchange rate, update paid amount
							
							$refund_update_sql_data_array = array(	'store_refund_is_processed' => 1,
																	'store_refund_payments_reference' => $alipay_sql_str, 
																	'store_refund_last_modified' => 'now()' );
							tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
							
			          		// Insert refund payment history
		          			$refund_history_sql_data_array = array(	'store_refund_id' => $refund_id,
				    		           								/*'store_refund_status' => 3,*/
					    	 	          							'date_added' => 'now()',
					    	 	          							'payee_notified' => '1',
					    	 	          							'comments' => $comments,
					    	 	          							'changed_by' => $_SESSION['login_email_address'],
					    	 	          							'changed_by_role' => 'admin'
				        	    	       							);
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
    					}
    				}
    			}
    			break;
    		case 'PayRef':
    			if (is_array($input_array["refund_batch"]) && count($input_array["refund_batch"])) {
    				foreach($input_array["refund_batch"] as $refund_id) {
    					if (isset($input_array["payment_reference"][$refund_id])) {
    						$reference_update_sql = "	UPDATE " . TABLE_STORE_REFUND . "
    													SET store_refund_payments_reference='" . tep_db_input($input_array["payment_reference"][$refund_id]) . "'
    													WHERE store_refund_id = '" . tep_db_input($refund_id) . "'";
    						tep_db_query($reference_update_sql);
    					}
					}
					
					$messageStack->add_session(SUCCESS_REFUND_BATCH_ACTION_PAYREF, 'success');
					$action_res_array['code'] = '1';
    			}
    			
    			break;
    		case 'ApiRefund':
    			if (is_array($input_array["refund_batch"]) && count($input_array["refund_batch"])) {
    				require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPAL);
					
    				foreach($input_array["refund_batch"] as $refund_id) {
    					$processing_error = false;
    					$memo = '';
    					
    					$refund_info_select_sql = "	SELECT sr.store_refund_trans_id, sr.store_refund_status, sr.store_refund_trans_total_amount, sr.store_refund_amount, sr.store_refund_payments_methods_name,
														o.payment_methods_id, o.payment_methods_parent_id
    												FROM " . TABLE_STORE_REFUND . " AS sr 
													INNER JOIN " . TABLE_ORDERS . " AS o
    													ON o.orders_id = sr.store_refund_trans_id
    												WHERE sr.store_refund_id = '" . tep_db_input($refund_id) . "'";
    					$refund_info_result_sql = tep_db_query($refund_info_select_sql);
    					if ($refund_info_row = tep_db_fetch_array($refund_info_result_sql)) {
							$payment_methods_filename_select_sql = "	SELECT payment_methods_filename 
																		FROM " . TABLE_PAYMENT_METHODS . "
																		WHERE payment_methods_id = '".$refund_info_row['payment_methods_parent_id']."'";
							$payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
							$payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);
							
    						if ($payment_methods_filename_row['payment_methods_filename'] == 'paypal.php') {

	    						$transaction_id = $this->_get_payment_gateway_trans_id($refund_info_row['store_refund_trans_id'], '25');
								$refund_type = 'Full';
								
								$nvpStr = '&TRANSACTIONID=' . $transaction_id . '&NOTE=' . $memo;
								
								$paypal = new paypal($refund_info_row['payment_methods_id']);
								
								$currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_info_row['store_refund_trans_id']) . "'";
								$currency_result_sql = tep_db_query($currency_select_sql);
								$currency_row = tep_db_fetch_array($currency_result_sql);

                                                                $this->_get_refund_info($refund_id);
                                                                if ($refund_info_row['store_refund_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_refund_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }

                                                                if ($refund_info_row['store_refund_trans_total_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_total_order_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }

								if ($formatted_refund_amount < $formatted_total_order_amount) {
									$refund_type = 'Partial';
									$nvpStr .= '&AMT=' . $formatted_refund_amount . '&CURRENCYCODE=' . $currency_row['currency'];
								}
								
								$nvpStr .= '&REFUNDTYPE=' . $refund_type;
								
								$res_array = $paypal->hash_call("RefundTransaction", $nvpStr, $currency_row['currency']);
								
								$ack = strtoupper($res_array['ACK']);
								
								if ($ack != 'SUCCESS') {
									$processing_error = true;
								}
							} else {
								$processing_error = true;
							}
    					} else {
    						$processing_error = true;
    					}
    					
    					if ($processing_error) {
    						$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']), 'error');
    					} else { // Insert exchange rate, update paid amount
							$refund_update_sql_data_array = array(	'store_refund_status' => 3,
																	'store_refund_payments_reference' => $res_array['REFUNDTRANSACTIONID'],
																	'store_refund_last_modified' => 'now()' );
							
							tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
		          			
							$comments = "Paypal Refund Transaction ID: " . $res_array['REFUNDTRANSACTIONID'] . "\n" .
										"Gross Refund Amount: " . $res_array['GROSSREFUNDAMT'] . "\n" .
										"Fee Refund Amount: " . $res_array['FEEREFUNDAMT'] . "\n" .
										"Net Refund Amount: " . $res_array['NETREFUNDAMT'];
							
							$orders_status_history_sql_data_array = array(	'orders_id' => $refund_info_row['store_refund_trans_id'],
																			'orders_status_id' => 0,
																			'date_added' => 'now()',
																			'customer_notified' => 0,
																			'comments' => $comments,
																			'comments_type' => 1,
																			'set_as_order_remarks' => 0,
																			'changed_by' => $_SESSION['login_email_address']
																		);
							tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data_array);
			          		
		          			// Insert refund payment history
		          			$refund_history_sql_data_array = array(	'store_refund_id' => $refund_id,
				    		           								'store_refund_status' => 3,
					    	 	          							'date_added' => 'now()',
					    	 	          							'payee_notified' => '1',
					    	 	          							'comments' => $comments,
					    	 	          							'changed_by' => $_SESSION['login_email_address'],
					    	 	          							'changed_by_role' => 'admin'
				        	    	       							);
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
    					}
    				}
    			}
    			break;
    		case 'PaypalECApiRefund':
    			if (is_array($input_array["refund_batch"]) && count($input_array["refund_batch"])) {		
					require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPALEC);
    				foreach($input_array["refund_batch"] as $refund_id) {
    					$processing_error = false;
    					$memo = '';
    					
    					$refund_info_select_sql = "	SELECT sr.store_refund_trans_id, sr.store_refund_status, sr.store_refund_trans_total_amount, sr.store_refund_amount, sr.store_refund_payments_methods_name,
														o.payment_methods_id, o.payment_methods_parent_id
    												FROM " . TABLE_STORE_REFUND . " AS sr 
													INNER JOIN " . TABLE_ORDERS . " AS o
    													ON o.orders_id = sr.store_refund_trans_id
    												WHERE sr.store_refund_id = '" . tep_db_input($refund_id) . "'";
    					$refund_info_result_sql = tep_db_query($refund_info_select_sql);
    					if ($refund_info_row = tep_db_fetch_array($refund_info_result_sql)) {
							$payment_methods_filename_select_sql = "	SELECT payment_methods_filename 
																		FROM " . TABLE_PAYMENT_METHODS . "
																		WHERE payment_methods_id = '".$refund_info_row['payment_methods_parent_id']."'";
							$payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
							$payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);
							
    						if ($payment_methods_filename_row['payment_methods_filename'] == 'paypalEC.php') {
                                                            
	    						$transaction_id = $this->_get_payment_gateway_trans_id($refund_info_row['store_refund_trans_id'], 'paypalEC.php');
								$paypalEC = new paypalEC($refund_info_row['payment_methods_id']);
								
								$currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_info_row['store_refund_trans_id']) . "'";
								$currency_result_sql = tep_db_query($currency_select_sql);
								$currency_row = tep_db_fetch_array($currency_result_sql);
								$paypalEC->get_merchant_account($currency_row['currency']);

                                                                $this->_get_refund_info($refund_id);
                                                                if ($refund_info_row['store_refund_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_refund_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }

                                                                if ($refund_info_row['store_refund_trans_total_amount'] >= $this->ot['ot_total']['value']) {
                                                                    $formatted_total_order_amount = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                                }

								//====================pipwave / crew refund API ====================
								$pipwave = new pipwave($refund_info_row['store_refund_trans_id']);
								if(isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
									$result = $pipwave->refundAPI($formatted_refund_amount);
									if(isset($result['pg_raw_data']) && !empty($result['pg_raw_data'])) {
										$rawData = json_decode($result['pg_raw_data'], true);

										// no need to create functions for multiple format for now, improve when we have sample with full data
										// just compute for REFUNDTRANSACTIONID for now
										$refund_transaction_id = null;
										if(isset($rawData['result']['id'])){
											$refund_transaction_id = $rawData['result']['id'];
										}else if(isset($rawData['RefundTransactionID'])){
											$refund_transaction_id = $rawData['RefundTransactionID'];
										}else if(isset($rawData['RefundInfo']['encryptedRefundTransactionId'])){
											$refund_transaction_id = $rawData['RefundInfo']['encryptedRefundTransactionId'];
										}

										$res_array = array (
											'REFUNDTRANSACTIONID' => $refund_transaction_id,
											'GROSSREFUNDAMT' => (isset($rawData['GrossRefundAmount']['value'])?$rawData['GrossRefundAmount']['value']:$rawData['RefundInfo']['refundGrossAmount']),
											'FEEREFUNDAMT' => (isset($rawData['FeeRefundAmount']['value'])?$rawData['FeeRefundAmount']['value']:$rawData['RefundInfo']['refundFeeAmount']),
											'NETREFUNDAMT' => (isset($rawData['NetRefundAmount']['value'])?$rawData['NetRefundAmount']['value']:$rawData['RefundInfo']['refundNetAmount']),
										);
									}
									if(isset($result['status']) && $result['status'] == 200) {

									} else {
										$processing_error = true;
									}
								} else {
									if ($formatted_refund_amount < $formatted_total_order_amount) {
										$res_array = $paypalEC->refundAPI('Partial', $transaction_id, $memo, $formatted_refund_amount, $currency_row['currency']);
									} else {
										$res_array = $paypalEC->refundAPI('Full', $transaction_id, $memo);
									}
									$ack = strtoupper($res_array['ACK']);

									if ($ack != 'SUCCESS') {
										$processing_error = true;
									}
								}
								//====================pipwave / crew refund API ====================

							} else {
								$processing_error = true;
							}
    					} else {
    						$processing_error = true;
    					}
    					
    					if ($processing_error) {
							if(isset($result['status']) && $result['message']) {
								$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $result['status'], $result['message']), 'error');
							} else {
								$messageStack->add_session(sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']), 'error');
							}
    					} else { // Insert exchange rate, update paid amount
							$refund_update_sql_data_array = array(	'store_refund_status' => 3,
																	'store_refund_payments_reference' => $res_array['REFUNDTRANSACTIONID'],
																	'store_refund_last_modified' => 'now()' );
							
							tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
		          			
							$comments = "Paypal Refund Transaction ID: " . $res_array['REFUNDTRANSACTIONID'] . "\n" .
										"Gross Refund Amount: " . $res_array['GROSSREFUNDAMT'] . "\n" .
										"Fee Refund Amount: " . $res_array['FEEREFUNDAMT'] . "\n" .
										"Net Refund Amount: " . $res_array['NETREFUNDAMT'];
							
							$orders_status_history_sql_data_array = array(	'orders_id' => $refund_info_row['store_refund_trans_id'],
																			'orders_status_id' => 0,
																			'date_added' => 'now()',
																			'customer_notified' => 0,
																			'comments' => $comments,
																			'comments_type' => 1,
																			'set_as_order_remarks' => 0,
																			'changed_by' => $_SESSION['login_email_address']
																		);
							tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data_array);
			          		
		          			// Insert refund payment history
		          			$refund_history_sql_data_array = array(	'store_refund_id' => $refund_id,
				    		           								'store_refund_status' => 3,
					    	 	          							'date_added' => 'now()',
					    	 	          							'payee_notified' => '1',
					    	 	          							'comments' => $comments,
					    	 	          							'changed_by' => $_SESSION['login_email_address'],
					    	 	          							'changed_by_role' => 'admin'
				        	    	       							);
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
    					}
    				}
    			}
    			break;
		}
		
		return $action_res_array;
	}
	
	function edit_refund($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id, $rollback_complete_to_cancel_permission;
		
		$edit_refund_html = '';
		$this->_get_refund_info($input_array['refID']);
		
		$payment_status_array = array();
		$payment_status_select_sql = "	SELECT store_payments_status_id, store_payments_status_name 
                                                FROM " . TABLE_STORE_PAYMENTS_STATUS . "
                                                WHERE language_id = '" . (int)$languages_id . "'
                                                ORDER BY store_payments_status_sort_order";
		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
		while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
			$payment_status_array[$payment_status_row['store_payments_status_id']] = $payment_status_row['store_payments_status_name'];
		}
		$payment_methods_filename_select_sql = "	SELECT payment_methods_filename 
                                                                FROM " . TABLE_PAYMENT_METHODS . "
                                                                WHERE payment_methods_id = '".$this->payment_info['payment_methods_parent_id']."'";
		$payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
		$payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);
		if (isset($payment_methods_filename_row['payment_methods_filename']) && $payment_methods_filename_row['payment_methods_filename'] == 'paypalEC.php') {
			$payment_required_fields = $this->_get_required_receive_payment_info($this->payment_info['order_id'], $this->payment_info['payment_methods_parent_id'], 'paypalEC.php');
		} else {
			$payment_required_fields = $this->_get_required_receive_payment_info($this->payment_info['order_id'], $this->payment_info['payment_methods_parent_id']);
		}
                
		ob_start();
  		
  		$form_name = 'edit_refund_form';
  		$form_param = tep_get_all_get_params(array('action')) . 'action=update_refund';
  		$form_cancel_param = tep_get_all_get_params(array('action')) . 'action=cancel_refund';
		?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
                    <!--Refund Ticket Info-->
                    <tr>
                        <td width="100%">
                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td valign="top" width="80%">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main" width="15%"><b><?=ENTRY_REFUND_ID?></b></td>
                                                <td class="main"><b><?=$input_array['refID']?></b></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><b><?=ENTRY_REFUND_STATUS?></b></td>
                                                <td class="main"><?=$this->info['status_name']?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><b><?=ENTRY_REFUND_DATE_TIME?></b></td>
                                                <td class="main"><?=tep_datetime_short($this->info["refund_date"], PREFERRED_DATE_TIME_FORMAT)?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><b><?=ENTRY_REFUND_DATE_LAST_MODIFIED?></b></td>
                                                <td class="main"><?=tep_datetime_short($this->info["last_modified"], PREFERRED_DATE_TIME_FORMAT)?></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator()?></td>
                    </tr>
                    <!--Beneficiary Info-->
                    <tr>
                        <td>
                            <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="pageHeading" valign="top"><b><?=TABLE_SECTION_HEADING_REFUND_BENEFICIARY_INFO?></b></td>
                                </tr>
                                <tr>
                                    <td valign="top">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_REFUND_BENEFICIARY?></b></td>
                                                <td class="main">
                                                <?	echo tep_output_string_protected($this->beneficiary['firstname'] . ' ' . $this->beneficiary['lastname']); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_REFUND_BENEFICIARY_EMAIL?></b></td>
                                                <td class="main"><?=$this->beneficiary['email_address']?></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
      			</td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator()?></td>
                    </tr>
                    <!--Refund Payment Info-->
                    <tr>
                        <td>
                            <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="pageHeading" valign="top"><b><?=TABLE_SECTION_HEADING_REFUND_INFO?></b></td>
                                </tr>
                                <tr>
                                    <td valign="top">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td valign="top">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="2">
                                                        <tr>
                                                            <td width="20%" class="main" valign="top"><b><?=ENTRY_REFUND_ORDER_ID?></b></td>
                                                            <td class="main"><?=$this->payment_info["order_id"]?></td>
                                                        </tr>
                                                        <tr>
                                                            <td width="20%" class="main" valign="top"><b><?=ENTRY_REFUND_PAYMENT_ORG_REF?></b></td>
                                                            <td class="main"><?=$this->_get_payment_gateway_trans_id($this->payment_info["order_id"], $this->payment_info["payment_methods_parent_id"])?></td>
                                                        </tr>
                                                        <tr>
                                                            <td width="20%" class="main" valign="top"><b><?=ENTRY_REFUND_CHECKOUT_METHOD?></b></td>
                                                            <td class="main"><?= $this->payment_info["checkout_payment_methods_name"] ?: $this->payment_info["payment_methods_name"]; ?></td>
                                                        </tr>
                                                        <?= tep_draw_form('update_refund_method', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=update_refund', 'post', '') ?>
                                                        <tr>
                                                            <td width="20%" class="main" valign="top"><b><?= ENTRY_REFUND_PAYMENT_METHOD ?></b></td>
                                                            <?
															if ($this->info['status'] == '2') {
                                                                $refund_method_array = array( array(
                                                                        'id' => "0",
                                                                        'text' => "Store Credit"
                                                                )
                                                                );
                                                                $extra_payment_method = array();
                                                                if (empty($this->payment_info["checkout_payment_methods_name"])) {
                                                                    $extra_payment_method['id'] = $this->payment_info["payment_methods_parent_id"];
                                                                    $extra_payment_method['text'] = $this->payment_info["store_refund_payments_methods_name"];
																	$selected_value = $this->payment_info["payment_methods_parent_id"];
                                                                } else {
                                                                    $extra_payment_method['id'] = $this->payment_info["payment_methods_parent_id"];
                                                                    $extra_payment_method['text'] = $this->payment_info["checkout_payment_methods_name"];
																	$selected_value = $this->payment_info["refund_payment_methods_id"];
                                                                }
                                                                $refund_method_array[] = $extra_payment_method;
                                                                ?>
                                                                <td class="main">
                                                                    <?= tep_draw_pull_down_menu("refund_payment_method_id", $refund_method_array, tep_not_null($selected_value) ? $selected_value : '', 'id="refund_payment_method"') ?>
                                                                    <?= tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="RefundPaymentMethodUpdateBtn"', 'inputButton') ?>
                                                                </td>
                                                            <? } else { ?>
                                                                <td class="main"><?= $this->payment_info["payment_methods_name"] ?></td>
                                                            <?php }
																echo tep_draw_hidden_field('checkout_payment_methods_id', $this->payment_info["payment_methods_parent_id"]);
															?>
                                                        </tr>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?=ENTRY_REFUND_PAYMENT_REFERENCE?></b></td>
                                                            <td class="main"><?=$this->payment_info["reference"]?></td>
                                                        </tr>
<?		if ($this->payment_info["payment_methods_parent_id"] == '25' || $this->payment_info["payment_methods_parent_id"] == '354') { ?>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?=ENTRY_REFUND_PAYPAL_TRANS_ID?></b></td>
                                                            <td class="main"><?=$this->payment_info["reference"]?></td>
                                                        </tr>
<?		} ?>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?=ENTRY_REFUND_ORDER_TOTAL_AMOUNT?></b></td>
                                                            <td class="main">
                                                                <?php
                                                                if ($this->payment_info["order_total_amount"] >= $this->ot['ot_total']['value']) {
                                                                    $_total = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $_total = $currencies->apply_currency_exchange($this->payment_info["order_total_amount"], $this->payment_info['output_currency'], $this->payment_info['output_currency_value']);
                                                                }
                                                                echo $currencies->format($_total, false, $this->payment_info["output_currency"])
                                                                ?>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?=ENTRY_REFUND_AVAILABLE_TOTAL_REFUND_AMOUNT?></b></td>
                                                            <td class="main">
                                                                <?php
                                                                if ($this->payment_info["store_refund_amount"] >= $_total = $this->ot['ot_total']['value']) {
                                                                    $_refund = $this->ot['ot_total']['text'];
                                                                } else {
																	$refund_amount = $currencies->apply_currency_exchange($this->payment_info["store_refund_amount"], $this->payment_info['output_currency'], $this->payment_info
																	['output_currency_value']);
                                                                    $_refund = $refund_amount + $this->payment_info["fees_bear_by_customer"];
                                                                }

                                                                if ($this->payment_info["refund_gst_amount"] >= $this->ot['ot_gst']['value']) {
                                                                    $_tax = $this->ot['ot_gst']['text'];
                                                                } else {
                                                                    $_tax = $currencies->apply_currency_exchange($this->payment_info["refund_gst_amount"], $this->payment_info['output_currency'], $this->payment_info['output_currency_value']);
                                                                }

                                                                if ($this->payment_info["refund_surcharge_amount"] >= $this->ot['ot_surcharge']['value']) {
                                                                    $_surcharge = $this->ot['ot_surcharge']['text'];
                                                                } else {
                                                                    $_surcharge = $currencies->apply_currency_exchange($this->payment_info["refund_surcharge_amount"], $this->payment_info['output_currency'], $this->payment_info['output_currency_value']);
                                                                }

                                                                echo $currencies->format($_refund, false, $this->payment_info["output_currency"]) . ' ' .
                                                                    sprintf(TEXT_GST, $currencies->format($_tax, false, $this->payment_info["output_currency"])). ' ' .
                                                                    sprintf(TEXT_SURCHARGE, $currencies->format($_surcharge, false, $this->payment_info["output_currency"]))
                                                                ?>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?= ENTRY_REFUND_FEES_BEAR_BY_CUSTOMER ?></b></td>
                                                            <? if ( $this->info['status'] == '2' ) { ?>
                                                                <td class="main">
                                                                    <?= tep_draw_input_field('refund_fees_bear_by_customer', tep_not_null($this->payment_info["fees_bear_by_customer"]) ? $this->payment_info["fees_bear_by_customer"] : '', ' id="refund_fees_bear_by_customer" size="30" ') ?>
                                                                    <?= tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="RefundAmountUpdateBtn"', 'inputButton');?>
                                                                </td>
															<? } else if ($this->info['status'] != '2' && tep_not_null($this->payment_info["fees_bear_by_customer"])) { ?>
                                                                <td class="main">
																<?php
                                                                echo $currencies->format($this->payment_info["fees_bear_by_customer"], false, $this->payment_info["output_currency"]);
                                                                ?>
																</td>
                                                            <? } else { ?>
                                                                <td class="main">-</td>
                                                            <? } ?>
                                                            <?php
                                                                if ($this->payment_info["store_refund_amount"] >= $_total = $this->ot['ot_total']['value']) {
                                                                    $_refund = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $_refund = $currencies->apply_currency_exchange($this->payment_info["store_refund_amount"], $this->payment_info['output_currency'], $this->payment_info
																	['output_currency_value']);
                                                                }
																
																if ($_total == $this->ot['ot_total']['text']){
																	$currency_convert = 1;
																}else{
																	$currency_convert = $_total / $this->ot['ot_total']['text'];
																}

                                                                echo tep_draw_hidden_field('refund_amount', $_refund) . tep_draw_hidden_field('fees_bear_by_customer', $this->payment_info["fees_bear_by_customer"]) . tep_draw_hidden_field('currency_convert', $currency_convert) ;
                                                            ?>
                                                        </tr>
                                                        <tr>
                                                            <td class="main" valign="top"><b><?=ENTRY_REFUND_AMOUNT?></b></td>
                                                            <td class="main">
                                                                <?php
                                                                if ($this->payment_info["store_refund_amount"] >= $_total = $this->ot['ot_total']['value']) {
                                                                    $_refund = $this->ot['ot_total']['text'];
                                                                } else {
                                                                    $_refund = $currencies->apply_currency_exchange($this->payment_info["store_refund_amount"], $this->payment_info['output_currency'], $this->payment_info['output_currency_value']);
                                                                }

                                                                echo $currencies->format($_refund, false, $this->payment_info["output_currency"]);
                                                                ?>
                                                            </td>
                                                        </tr>
                                                            <?
                                                            if (count($payment_required_fields)) {
                                                                    echo '<tr><td class="main" valign="top">&nbsp;</td></tr>';
                                                            }
                                                            for ($field_cnt=0; $field_cnt < count($payment_required_fields); $field_cnt++) {
    echo '								<tr>
                                                                    <td class="main" valign="top"><b>'.$payment_required_fields[$field_cnt]['title'].'</b></td>
                                                                    <td class="main">'.$payment_required_fields[$field_cnt]['value'].'</td>
                                                            </tr>';
                                                            }
                                                            ?>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator()?></td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                    </tr>
                    <!--Remarks-->
                    <tr>
                        <td class="main">
                            <table border="1" cellspacing="0" cellpadding="5">
                                <tr>
                                    <td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
                                    <td class="smallText" align="center"><b><?=TABLE_HEADING_BENEFICIARY_NOTIFIED?></b></td>
                                    <td class="smallText" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
                                    <td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
                                    <td class="smallText" align="center"><b><?=TABLE_HEADING_CHANGED_BY?></b></td>
                                </tr>
                                <?
                                    $refund_history_select_sql = "	SELECT *
                                                                        FROM " . TABLE_STORE_REFUND_HISTORY . "
                                                                        WHERE store_refund_id = '" . $input_array['refID'] . "'
                                                                        ORDER BY date_added";
                                    $refund_history_result_sql = tep_db_query($refund_history_select_sql);
                                    while ($refund_history_row = tep_db_fetch_array($refund_history_result_sql)) {
                                        $formatted_date_comment_added = tep_datetime_short($refund_history_row["date_added"], PREFERRED_DATE_TIME_FORMAT);
                                        $img_str = ($refund_history_row['payee_notified'] == '1') ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
                                ?>
                                <tr>
                                    <td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
                                    <td class="smallText" align="center"><?=$img_str?></td>
                                    <td class="smallText" align="center"><?=isset($payment_status_array[$refund_history_row['store_refund_status']]) ? $payment_status_array[$refund_history_row['store_refund_status']] : '--'?></td>
                                    <td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $refund_history_row['comments']))?>&nbsp;</td>
                                    <td class="smallText" align="center"><?=nl2br(tep_db_output($refund_history_row['changed_by']))?>&nbsp;</td>
                                </tr>
                                <?	} ?>
                            </table>
                        </td>
                    </tr>
                    <!--Refund Action-->
                    <tr>
                        <td>
                        <?	echo tep_draw_form($form_name, $filename, $form_param, 'post', '');
                                echo tep_draw_hidden_field('status_DB_prev', $this->info['status']);

                                if (isset($_SESSION[$session_name])) {
                                        $back_btn_url = tep_href_link($filename, 'action=show_report&cont=1');
                                } else {
                                        $back_btn_url = tep_href_link($filename);
                                }

                                $trans_row = $this->payment_info;
                                $trans_row["store_refund_status"] = $this->info["status"];
                                $payment_methods_filename_array = $this->_get_payment_method();

                                // action
                                list($action_button_html, $payment_batch_available) = $this->_get_action_button($this->payment_info['order_id'], $this->info['refund_id'], $filename, 1, $trans_row, $payment_methods_filename_array, 0, 1);
                        ?>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main" colspan="4">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <?php if ($this->info['status'] == '2') { ?>
                                            <td class="main" colspan="2"><b>Payment Reference</b></td>
                                            <td class="main" colspan="2"><b><?=ENTRY_REFUND_REMARK?></b></td>
                                        <?php } else { ?>
                                            <td class="main" colspan="4"><b><?=ENTRY_REFUND_REMARK?></b></td>
                                        <?php } ?>
                                    </tr>
                                    <tr>
                                        <?php if ($this->info['status'] == '2') { ?>
                                            <td class="main" colspan="2"><?=tep_draw_textarea_field('payment_reference_' . $this->info['refund_id'], 'soft', '60', '5', '', ' id="payment_reference_' . $this->info['refund_id'] . '" maxlength=32')?></td>
                                            <td class="main" colspan="2"><?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5')?></td>
                                        <?php } else { ?>
                                            <td class="main" colspan="4"><?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5')?></td>
                                        <?php } ?>
                                    </tr>
                                    <tr>
                                        <?php if ($this->info['status'] == '2') { ?>
                                            <td align="right" class="main" colspan="2">
                                                <?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="PaymentReferenceUpdateBtn"', 'inputButton')?>
                                            </td>
                                            <td class="main">
                                                <b><?=ENTRY_REFUND_NOTIFY_BENEFICIARY?></b>
                                                <?=tep_draw_checkbox_field('notify', '1', false, '', 'id=notify')?>
                                            </td>
                                            <td align="right" class="main">
                                                <?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="RefundUpdateBtn"', 'inputButton')?>
                                            </td>
                                        <?php } else { ?>
                                            <td class="main" colspan="3">
                                                <b><?=ENTRY_REFUND_NOTIFY_BENEFICIARY?></b>
                                                <?=tep_draw_checkbox_field('notify', '1', false, '', 'id=notify')?>
                                            </td>
                                            <td align="right" class="main">
                                                <?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="RefundUpdateBtn"', 'inputButton')?>
                                            </td>
                                        <?php } ?>
                                    </tr>
                                    <tr>
                                        <td class="main" colspan="4">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td class="main" colspan="2">
                                        <? if ($this->info['status'] == '1' || $this->info['status'] == '2') { ?>
                                            <fieldset class="selectedNuetralBgFieldSet">
                                                <legend align="left" class="SectionHead">Refund Update</legend>
                                                <?php
                                                    echo $action_button_html;
                                                    echo '&nbsp;&nbsp;&nbsp;';
                                                    echo tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="RefundCancelBtn" style="padding: 5px 10px;" onClick="if (confirm(\'Are you sure to cancel this refund payment?\') != \'0\') { return true; } else { return false; }"', 'redButton');
                                                ?>
                                            </fieldset>
                                        <?php } else if (($this->info['status'] == 3) && $rollback_complete_to_cancel_permission) { ?>
                                            <fieldset class="selectedAlertBgFieldSet">
                                                <legend align="left" class="SectionHead">Rollback</legend>
                                                <?php
                                                    echo tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="RefundCancelBtn" style="padding: 5px 10px;" onClick="if (confirm(\'Are you sure to cancel this refund payment?\') != \'0\') { return true; } else { return false; }"', 'greenButton');
                                                    echo '&nbsp;&nbsp;&nbsp;';
                                                ?>
                                            </fieldset>
                                        <?php } ?>
                                        </td>
                                        <td class="main" colspan="2">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td class="main" colspan="4">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td align="left" class="main" colspan="4">
                                            <?=tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, $back_btn_url, '', 'inputButton')?>
                                        </td>
                                    </tr>
                                </table>
                            </form>
                        </td>
                    </tr>
		</table>

                <style>
                    .inputButton, .inputButtonOver { padding : 5px 10px !important; }
                </style>
		<?
		$edit_payment_html .= "\n" . ob_get_contents();
		ob_end_clean();

	  	return $edit_payment_html;
	}

	function update_refund($filename, $input_array, &$messageStack) {
		global $currencies, $languages_id;
                global $rollback_complete_to_cancel_permission;
		
		if (isset($input_array['refID'])) {
                    $refund_select_sql = "	SELECT store_refund_id, store_refund_status, store_refund_payments_methods_name, store_refund_checkout_payments_methods_name, store_refund_customer_bear_fees
                                            FROM " . TABLE_STORE_REFUND . "
                                            WHERE store_refund_id='".tep_db_input($input_array['refID'])."' ";
                    $refund_result_sql = tep_db_query($refund_select_sql);

                    if ($refund_row = tep_db_fetch_array($refund_result_sql)) {	// This payment exists
                            if ($input_array["status_DB_prev"] != $refund_row['store_refund_status']) {
                                $messageStack->add_session(WARNING_REFUND_UPDATED_BY_SOMEONE, 'warning');
                    } else {
                        if (isset($input_array['RefundUpdateBtn'])) {	// Update refund payment remarks
                            if (tep_not_null($input_array['admin_comment'])) {
                                    $refund_history_data_array = array(	'store_refund_id' => $input_array['refID'],
                                                                            'store_refund_status' => 0,
                                                                            'date_added' => 'now()',
                                                                            'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
                                                                            'comments' => tep_db_prepare_input($input_array['admin_comment']),
                                                                            'changed_by' => $this->identity_email,
                                                                            'changed_by_role' => 'admin'
                                                                            );
                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_data_array);
                                $messageStack->add_session(sprintf(SUCCESS_REFUND_PAYMENT_UPDATE, $input_array['refID']), 'success');
                            }
                        } else if (isset($input_array['RefundCancelBtn'])) {	// Cancel this refund payment
                            if ($refund_row['store_refund_status'] == '1' ||
                                $refund_row['store_refund_status'] == '2' ||
                                ($refund_row['store_refund_status'] == 3 && $rollback_complete_to_cancel_permission)) {
                                $refund_status_update_data_array = array(	'store_refund_status' => 4,
                                                                                'store_refund_last_modified' => 'now()' );
                                tep_db_perform(TABLE_STORE_REFUND, $refund_status_update_data_array, 'update', "store_refund_id = '" . tep_db_input($input_array['refID']) . "'");

                                // Insert refund payment history
                                $refund_history_data_array = array(	'store_refund_id' => $input_array['refID'],
                                                                        'store_refund_status' => 4,
                                                                        'date_added' => 'now()',
                                                                        'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
                                                                        'comments' => tep_db_prepare_input($input_array['admin_comment']),
                                                                        'changed_by' => $this->identity_email,
                                                                        'changed_by_role' => 'admin'
                                                                                );
                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_data_array);
                                $messageStack->add_session(sprintf(SUCCESS_REFUND_PAYMENT_CANCELLATION, $input_array['refID']), 'success');
                            }
                        } else if (isset($input_array['PaymentReferenceUpdateBtn'])) {    // Update Payment Reference
                            if (isset($input_array["payment_reference_" . $input_array['refID']])) {
                                $sql = "SELECT store_refund_payments_reference FROM " . TABLE_STORE_REFUND . " WHERE store_refund_id = " . tep_db_input($input_array['refID']);
                                $res = tep_db_query($sql);
                                $row = tep_db_fetch_array($res);

                                $reference_update_sql = "UPDATE " . TABLE_STORE_REFUND . "
                                                        SET store_refund_payments_reference='" . tep_db_input($input_array["payment_reference_" . $input_array['refID']]) . "'
                                                        WHERE store_refund_id = '" . tep_db_input($input_array['refID']) . "'";
                                tep_db_query($reference_update_sql);

                                // Insert refund payment history
                                $refund_history_data_array = array(	'store_refund_id' => $input_array['refID'],
                                                                        'store_refund_status' => 0,
                                                                        'date_added' => 'now()',
                                                                        'payee_notified' => 0,
                                                                        'comments' => "Changes from \n" .
                                                                                        "<b>Payment Reference:</b> " . $row["store_refund_payments_reference"] . "\n" .
                                                                                        "\n" .
                                                                                        "Changes made \n" .
                                                                                        "<b>Payment Reference:</b> " . tep_db_prepare_input($input_array["payment_reference_" . $input_array['refID']]),
                                                                        'changed_by' => $this->identity_email,
                                                                        'changed_by_role' => 'admin'
                                                                    );
                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_data_array);
                                $messageStack->add_session("Payment Reference has been successfully updated.", 'success');
                            }
                        } else if (isset($input_array['RefundPaymentMethodUpdateBtn'])) {
                            $payment_checkout_method = !empty($refund_row["store_refund_checkout_payments_methods_name"]) ? $refund_row["store_refund_checkout_payments_methods_name"] : $refund_row["store_refund_payments_methods_name"];

                            if (isset($input_array["refund_payment_method_id"])) {
                                if ($refund_row['store_refund_payments_methods_name'] != $input_array["refund_payment_method_id"]) {
									if ($input_array["refund_payment_method_id"] == 0) {
										$refund_payment_method = "Store Credit";
									} else {
										$refund_payment_method_select_sql =  "	SELECT payment_methods_send_mode_name 
																	FROM " . TABLE_PAYMENT_METHODS . "
																	WHERE payment_methods_id = " . tep_db_input($input_array["refund_payment_method_id"]) . ";";
										$refund_payment_method_result_sql = tep_db_query($refund_payment_method_select_sql);
										if ($refund_payment_method_row = tep_db_fetch_array($refund_payment_method_result_sql)) {
											$refund_payment_method = $refund_payment_method_row['payment_methods_send_mode_name'];
										} else {
											$messageStack->add_session("Refund Method Not Found IN DB", 'warning');
										}
									}
									
									if ($refund_payment_method) {
										$refund_method_update_sql = "UPDATE " . TABLE_STORE_REFUND . "
														SET store_refund_payments_methods_id ='" . tep_db_input($input_array["refund_payment_method_id"]) . "',
														store_refund_payments_methods_name = '" . tep_db_input($refund_payment_method) . "',
														store_refund_checkout_payments_methods_id = '" . tep_db_input($input_array['checkout_payment_methods_id']) . "',
														store_refund_checkout_payments_methods_name = '" . tep_db_input($payment_checkout_method) . "'
														WHERE store_refund_id = '" . tep_db_input($input_array['refID']) . "'";
										tep_db_query($refund_method_update_sql);

										// Insert refund payment method changed history
										$refund_history_data_array = array(	'store_refund_id' => $input_array['refID'],
											'store_refund_status' => 2,
											'date_added' => 'now()',
											'payee_notified' => 0,
											'comments' => "Changes from \n" .
												"<b>Refund Payment Method:</b> " . $refund_row['store_refund_payments_methods_name'] . "\n" .
												"\n" .
												"Changes made \n" .
												"<b>Refund Payment Method:</b> " . tep_db_prepare_input($refund_payment_method),
											'changed_by' => $this->identity_email,
											'changed_by_role' => 'admin'
										);
										tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_data_array);

										$messageStack->add_session("Refund Method Has Been Changed", 'success');
									}
                                } else {
                                    $messageStack->add_session("No Changes Made For Refund Method", 'warning');
                                }
                            }
                    } else if (isset($input_array['RefundAmountUpdateBtn'])) {
                        if (isset($input_array['refund_fees_bear_by_customer']) && $input_array['refund_fees_bear_by_customer'] >= 0) {
                            if ($refund_row['store_refund_customer_bear_fees'] != $input_array['refund_fees_bear_by_customer']) {
								
                                $available_refund = $input_array['refund_amount'] + $input_array['fees_bear_by_customer'];
								$check_available_refund = round($available_refund , 2 );
								if ( $check_available_refund >= $input_array['refund_fees_bear_by_customer']){

									$total_store_refund_amount = $available_refund - $input_array['refund_fees_bear_by_customer'];
									$total_store_refund_amount = round(($total_store_refund_amount * $input_array['currency_convert']) , 8 );

									if ($total_store_refund_amount != 0) {
										$refund_method_update_sql = "UPDATE " . TABLE_STORE_REFUND . "
													SET store_refund_customer_bear_fees ='" . tep_db_input($input_array['refund_fees_bear_by_customer']) . "',
													store_refund_amount = '". tep_db_input($total_store_refund_amount) ."'
													WHERE store_refund_id = '" . tep_db_input($input_array['refID']) . "'";
										tep_db_query($refund_method_update_sql);
		
										// Insert refund fees bear by customer history
										$refund_history_data_array = array(	'store_refund_id' => $input_array['refID'],
											'store_refund_status' => 2,
											'date_added' => 'now()',
											'payee_notified' => 0,
											'comments' => "Changes from \n" .
												"<b>Fees Bear by Customer:</b> " . $refund_row["store_refund_customer_bear_fees"] . "\n" .
												"\n" .
												"Changes made \n" .
												"<b>Fees Bear by Customer:</b> " . tep_db_prepare_input($input_array["refund_fees_bear_by_customer"]),
											'changed_by' => $this->identity_email,
											'changed_by_role' => 'admin'
										);
										tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_data_array);
		
										$messageStack->add_session("Fees Bear by Customer Had Been Updated", 'success');
									} else {
										$messageStack->add_session("Total Refund Amount Can't Be 0", 'warning');
									}
									
								} else {
									$messageStack->add_session("Refund Amount Input Is Higher Than Available Refund Value", 'warning');
								}
                            } else {
                                $messageStack->add_session("No Changes Made For Fees Bear by Customer", 'warning');
                            }
                        }
                    }
                }
            }
        }
	}
	
	function _check_refund_rollback($order_id, $since_refund_date) {
		$refund_rollback_order_select_sql = "	SELECT orders_status_history_id 
                                                        FROM " . TABLE_ORDERS_STATUS_HISTORY . "
                                                        WHERE orders_id = '" . tep_db_input($order_id) . "'
                                                                AND date_added > '" . tep_db_input($since_refund_date) . "'
                                                                AND comments LIKE 'The following items have been refunded:%'
                                                                AND comments REGEXP \"(x -)[0-9]+\"
                                                        LIMIT 1";
		$refund_rollback_order_result_sql = tep_db_query($refund_rollback_order_select_sql);
		
		if (tep_db_num_rows($refund_rollback_order_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
	
	function _get_payment_gateway_trans_id($order_id, $payment_methods_parent_id) {
		$pipwave = new pipwave($order_id);
		$payment_gateway_trans_id = '';
		if(isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
			$pgInfo = $pipwave->pipwavePaymentMapper('pg_id', $payment_methods_parent_id);
			$pgRawData = array();
			if(isset($pipwave->pg_raw_data)) {
				$pgRawData = json_decode($pipwave->pg_raw_data,1);
			}
			if(isset($pgInfo['pg_code'])) {
				switch ($pgInfo['pg_code']) {
					case 'paypal':
					case 'paypalEC':
						$payment_gateway_trans_id = (isset($pgRawData['parent_txn_id']) ? $pgRawData['parent_txn_id'] : (isset($pgRawData['txn_id']) ? $pgRawData['txn_id'] : ''));
						break;
					case 'moneybookers':
						$payment_gateway_trans_id = (isset($pgRawData['mb_transaction_id']) ? $pgRawData['mb_transaction_id'] : '');
						break;
					case 'alipay':
						$payment_gateway_trans_id = (isset($pgRawData['trade_no']) ? $pgRawData['trade_no'] : '');
						break;
					case 'bibit':
						$payment_gateway_trans_id = (isset($pgRawData['transaction_id']) ? $pgRawData['transaction_id'] : '');
						break;
				}
			}
		} else {
			switch($payment_methods_parent_id) {
				case '25':
					$info_select_sql = "SELECT txn_id
									FROM " . TABLE_PAYPAL . "
									WHERE invoice = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['txn_id'];
					}
					break;
				//paypal ec
				case '354':
					$info_select_sql = "SELECT txn_id
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['txn_id'];
					}
					break;
				case '44':
					$info_select_sql = "SELECT mb_mb_trans_id
									FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
									WHERE mb_trans_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['mb_mb_trans_id'];
					}

					break;
				case '19':
					$info_select_sql = "SELECT egold_payment_batch_num
									FROM " . TABLE_PAYMENT_EGOLD . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['egold_payment_batch_num'];
					}

					break;
				case '17': // worldpay
					$info_select_sql = "SELECT transaction_id
									FROM " . TABLE_PAYMENT_EXTRA_INFO . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['transaction_id'];
					}
					break;
				case 'mazooma.php': // mazooma
					$info_select_sql = "SELECT mazooma_transaction_number
									FROM " . TABLE_MAZOOMA . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['transaction_id'];
					}
					break;
				case 'alipay.php': // alipay
					$info_select_sql = "SELECT alipay_trade_no
									FROM " . TABLE_ALIPAY . "
									WHERE alipay_orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['alipay_trade_no'];
					}
					break;
				case 'paypalEC.php': // paypalEC
					$info_select_sql = "SELECT txn_id
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$payment_gateway_trans_id = $info_row['txn_id'];
					}
					break;
				default:

					break;
			}
		}

		return $payment_gateway_trans_id;
	}
	
	function _get_required_receive_payment_info($order_id, $payment_methods_parent_id, $pass_filename='') {
		$info_array = array();

		$pipwave = new pipwave($order_id);
		$info_array = array();
		if(isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
			$pgInfo = $pipwave->pipwavePaymentMapper('pg_id', $payment_methods_parent_id);
			$pgRawData = array();
			if(isset($pipwave->pg_raw_data)) {
				$pgRawData = json_decode($pipwave->pg_raw_data,1);
			}
			if(isset($pgInfo['pg_code'])) {
				switch ($pgInfo['pg_code']) {
					case 'paypal':
					case 'paypalEC':
						$info_array[] = array(
							'key' => 'paypal_email',
							'title'=> 'PayPal E-mail Address',
							'value'=> $pgRawData['payer_email']
						);
					break;
					case 'moneybookers':
						$info_array[] = array(	'key' => 'mb_email',
							'title'=> 'Moneybookers E-mail Address',
							'value'=> $pgRawData['pay_from_email']
						);
						break;
				}
			}
		} else {
			if (tep_not_null($pass_filename)) {
				$payment_methods_parent_id = $pass_filename;
			}
			switch($payment_methods_parent_id) {
				case '25':
					$info_select_sql = "SELECT payer_email
									FROM " . TABLE_PAYPAL . "
									WHERE invoice = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$info_array[] = array(	'key' => 'paypal_email',
							'title'=> 'PayPal E-mail Address',
							'value'=> $info_row['payer_email']
						);
					}

					break;
				case 'paypalEC.php': //paypalEC
					$info_select_sql = "SELECT payer_email
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$info_array[] = array(	'key' => 'paypal_email',
							'title'=> 'PayPalEC E-mail Address',
							'value'=> $info_row['payer_email']
						);
					}

					break;
				case '44':
					$info_select_sql = "SELECT mb_payer_email
									FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
									WHERE mb_trans_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$info_array[] = array(	'key' => 'mb_email',
							'title'=> 'Moneybookers E-mail Address',
							'value'=> $info_row['mb_payer_email']
						);
					}

					break;
				case '19':
					$info_select_sql = "SELECT egold_payer_account
									FROM " . TABLE_PAYMENT_EGOLD . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$info_array[] = array(	'key' => 'egold_account',
							'title'=> 'e-gold Account Number',
							'value'=> $info_row['egold_payer_account']
						);
					}
					break;

				case 'mazooma.php': // mazooma
					$info_select_sql = "SELECT mazooma_user_id
									FROM " . TABLE_MAZOOMA . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
					$info_result_sql = tep_db_query($info_select_sql);

					if ($info_row = tep_db_fetch_array($info_result_sql)) {
						$info_array[] = array(	'key' => 'mazooma_user_id',
							'title'=> 'User ID with Mazooma',
							'value'=> $info_row['mazooma_user_id']
						);
					}
					break;

				default:

					break;
			}
		}
		
		return $info_array;
	}
	
	function _get_refund_info($refund_id) {
		global $languages_id;
		$refund_info_select_sql = "	SELECT sr.*, o.currency, o.currency_value, o.payment_methods_id, o.payment_methods_parent_id 
									FROM " . TABLE_STORE_REFUND . " as sr 
									INNER JOIN " . TABLE_ORDERS . " AS o 
										ON sr.store_refund_trans_id = o.orders_id 
									WHERE sr.store_refund_id = '" . tep_db_input($refund_id) . "'";
		$refund_info_result_sql = tep_db_query($refund_info_select_sql);
		
		if ($refund_info_row = tep_db_fetch_array($refund_info_result_sql)) {
			$refund_status_select_sql = "	SELECT store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
	  										WHERE store_payments_status_id = '" . tep_db_input($refund_info_row['store_refund_status']) . "' 
	  											AND language_id='" . $languages_id  . "'";
	  		$refund_status_result_sql = tep_db_query($refund_status_select_sql);
	  		$refund_status_row = tep_db_fetch_array($refund_status_result_sql);
	  		
	  		$refund_gst_select_sql = "	SELECT store_refund_gst_amount,  store_refund_surcharge_amount
										FROM " . TABLE_STORE_REFUND_INFO . " 
										WHERE store_refund_id = '" . tep_db_input($refund_id) . "'";
			$refund_gst_result_sql = tep_db_query($refund_gst_select_sql);
			$refund_gst_row = tep_db_fetch_array($refund_gst_result_sql);

			$this->info = array('refund_id' => $refund_id,
								'refund_date' => $refund_info_row['store_refund_date'],
								'status' => $refund_info_row['store_refund_status'],
								'status_name' => $refund_status_row['store_payments_status_name'],
								'last_modified' => $refund_info_row['store_refund_last_modified']
								);
			
			$this->beneficiary = array(	'id' => $refund_info_row['user_id'],
	      								'firstname' => $refund_info_row['user_firstname'],
	      								'lastname' => $refund_info_row['user_lastname'],
	      								'email_address' => $refund_info_row['user_email_address']
	      								);
	      	
	      	$this->payment_info = array('order_id' => $refund_info_row['store_refund_trans_id'],
	      								'output_currency' => $refund_info_row['currency'],
	      								'output_currency_value' => $refund_info_row['currency_value'],
	      								'order_total_amount' => $refund_info_row['store_refund_trans_total_amount'],
	      								'store_refund_amount' => $refund_info_row['store_refund_amount'],
	      								'refund_gst_amount' => $refund_gst_row['store_refund_gst_amount'],
	      								'refund_surcharge_amount' => $refund_gst_row['store_refund_surcharge_amount'],
	      								'reference' => $refund_info_row['store_refund_payments_reference'],
	      								'payment_methods_id' => $refund_info_row['payment_methods_id'],
	      								'payment_methods_parent_id' => $refund_info_row['payment_methods_parent_id'],
	      								'store_refund_payments_methods_name' => $refund_info_row['store_refund_payments_methods_name'],
                                        'checkout_payment_methods_name' => $refund_info_row['store_refund_checkout_payments_methods_name'],
										'refund_payment_methods_id' => $refund_info_row['store_refund_payments_methods_id'],
                                        'fees_bear_by_customer' => $refund_info_row['store_refund_customer_bear_fees'],
	      								);

                        $ot_sql = "SELECT text, value, class FROM " . TABLE_ORDERS_TOTAL . "
                                WHERE orders_id = " . $refund_info_row["store_refund_trans_id"];
                        $ot_res = tep_db_query($ot_sql);
			while ($ot_row = tep_db_fetch_array($ot_res)) {
                            $this->ot[$ot_row['class']] = array(
                                    'text' => preg_replace("/[^0-9.]/", "", str_replace(',', '', preg_replace('~&#([0-9]+);~e', '', strip_tags($ot_row['text'])))),
                                    'value' => $ot_row['value']
                                    );
                        }
		}
	}
	
	function _is_within_daily_limit($add_amount) {
		$action_allowed = false;
		
		$admin_credit_limit_select_sql = "	SELECT admin_credit_limit_total, admin_credit_limit_max 
											FROM " . TABLE_ADMIN_CREDIT_LIMIT . " 
											WHERE admin_id = " . tep_db_input($this->identity);
		$admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);
		
		if ($admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql)) {
			if ($admin_credit_limit_row['admin_credit_limit_total'] + $add_amount <= $admin_credit_limit_row['admin_credit_limit_max']) {
				$action_allowed = true;
				
				$admin_credit_limit_update_sql = "	UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . " 
													SET admin_credit_limit_total = admin_credit_limit_total + " . $add_amount . " 
													WHERE admin_id = " . tep_db_input($this->identity);
				tep_db_query($admin_credit_limit_update_sql);
			}
		}
		
		return $action_allowed;
	}
	function _get_available_balance_all_currency($user_id,$request_currency) {
	    //total current balance - total dynamic reserve
	    global $currencies;

	    $user_reserve_sql  = "SELECT store_account_balance_currency, store_account_balance_amount, store_account_reserve_amount
	    FROM store_account_balance
	    WHERE user_id = ".tep_db_input($user_id)." AND user_role = 'customers'
	    ORDER BY store_account_balance_currency";

	    $result_user_reserve = tep_db_query($user_reserve_sql);
	    $sum_all_currency_reserve_amount = 0;
	    $sum_all_currency_current_amount = 0;
	    while ($new_values = tep_db_fetch_array($result_user_reserve)){
	      $sum_all_currency_current_amount += $currencies->advance_currency_conversion($new_values['store_account_balance_amount'],$new_values['store_account_balance_currency'],$request_currency);
	      $sum_all_currency_reserve_amount += $currencies->advance_currency_conversion($new_values['store_account_reserve_amount'],$new_values['store_account_balance_currency'],$request_currency);
	    }

	    return $sum_all_currency_current_amount - $sum_all_currency_reserve_amount ;
	}

	function _get_settlement_account($orders_id) {
		$select_sql = " SELECT settlement_account
                                FROM pipwave_payment
                                WHERE orders_id = '" . tep_db_input($orders_id) . "'";
		$result_sql = tep_db_query($select_sql);
		if ($result_row = tep_db_fetch_array($result_sql)) {
			preg_match("/\[([^\]]*)\]/", $result_row['settlement_account'], $matches);
			return ( (isset($matches[1]) && !empty($matches[1])) ? $matches[1] : $result_row['settlement_account']);
		} else {
			$select_sql = " SELECT invoice_id
                                        FROM c2c_invoice
                                        WHERE orders_id = '" . tep_db_input($orders_id) . "'";
			$result_sql = tep_db_query($select_sql);
			if ($result_row = tep_db_fetch_array($result_sql)) {
				return 'G2G';
			} else {
				$g2g_select_sql = " SELECT orders_extra_info_value
                                                    FROM orders_extra_info
                                                    WHERE orders_id = '" . tep_db_input($orders_id) . "'
                                                        AND orders_extra_info_key = 'use_g2g_pg' ";
				$g2g_result_sql = tep_db_query($g2g_select_sql);
	
				if ($g2g_row = tep_db_fetch_array($g2g_result_sql)) {
					return 'G2G';
				}
			}
		}
	
		return 'OG';
	}

        function _get_action_button($order_id, $refund_id, $filename, $pm_cnt, $trans_row, $payment_methods_filename_array, $payment_batch_available, $refresh = false) {
            global $currencies, $paypal_manual_refund_permission;

            $action_button_html = "";

            switch ($trans_row['store_refund_status']) {
                case '1': // Pending
                    $store_refund_is_processed = 0;

                    switch ((isset($payment_methods_filename_array[$trans_row['payment_methods_parent_id']]) ? $payment_methods_filename_array[$trans_row['payment_methods_parent_id']] : '')) {
                        case "global_collect.php":
                            $global_collect_select_sql = "SELECT global_collect_status_id
                                                        FROM " . TABLE_GLOBAL_COLLECT . "
                                                        WHERE global_collect_orders_id = '" . (int) $order_id . "'";
                            $global_collect_result_sql = tep_db_query($global_collect_select_sql);
                            $global_collect_row = tep_db_fetch_array($global_collect_result_sql);

                            // do checking
                            if (($global_collect_row['global_collect_status_id'] >= 800 && $global_collect_row['global_collect_status_id'] < 900) && ($currencies->apply_currency_exchange($refund_amount, $trans_row['currency'], $trans_row['currency_value']) < $currencies->apply_currency_exchange($order_total_amount, $trans_row['currency'], $trans_row['currency_value']))) {
                                $store_refund_is_processed = 1;
                            }
                            break;
                            
                        default:
                            break;
                    }

                    $action_button_html = tep_button('Process', 'Process this refund payment', '', ' name="ProcessBtn_' . $refund_id . '" onClick="updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'1\', \'2\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\');" ' . ($store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                    break;

                case '2': // Processing
                    $pwUrl = '';
                    $pipwave = new pipwave($order_id);
                    if (isset($pipwave->pw_id) && !empty($pipwave->pw_id)) {
                        $pwUrl = PIPWAVE_PAYMENT_TRANSACTION_REPORT_URL . '?pw_id=' . $pipwave->pw_id;
                    }

					if ($trans_row['store_refund_payments_methods_name'] == 'Store Credit') {
						$action_button_html = tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
					} else {
						switch ((isset($payment_methods_filename_array[$trans_row['payment_methods_parent_id']]) ? $payment_methods_filename_array[$trans_row['payment_methods_parent_id']] : '')) {
							case 'paypal.php':
								if (!empty($pwUrl)) {
									$action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								} else {
									$action_button_html = tep_button('PayPal Refund API', 'Complete this refund payment using PayPal Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								}
	
								if ($paypal_manual_refund_permission) {
									$action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton');
								}
								break;
								
							case 'paypalEC.php':
								if (!empty($pwUrl)) {
									$action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								} else {
									$action_button_html = tep_button('PayPalEC Refund API', 'Complete this refund payment using PayPalEC Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								}
	
								if ($paypal_manual_refund_permission) {
									$action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton');
								}
								break;
								
							case 'alipay.php':
								if (!empty($pwUrl)) {
									$action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
								} else {
									$action_button_html = tep_button('Alipay Refund API', 'Alipay Refund API', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
								}
	
								$action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton');
								break;
								
							case 'global_collect.php':
								$global_collect_select_sql = "SELECT global_collect_status_id
															FROM " . TABLE_GLOBAL_COLLECT . "
															WHERE global_collect_orders_id = '" . (int) $order_id . "'";
								$global_collect_result_sql = tep_db_query($global_collect_select_sql);
								$global_collect_row = tep_db_fetch_array($global_collect_result_sql);
	
								// do checking
								if (($global_collect_row['global_collect_status_id'] >= 800 && $global_collect_row['global_collect_status_id'] < 900) && ($currencies->apply_currency_exchange($refund_amount, $trans_row['currency'], $trans_row['currency_value']) < $currencies->apply_currency_exchange($order_total_amount, $trans_row['currency'], $trans_row['currency_value']))) {
									$store_refund_is_processed = 1;
								}
	
								$action_button_html = tep_button('Refund API', 'Complete this refund payment using ' . $trans_row['store_refund_payments_methods_name'] . ' Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
								$action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton');
								break;
	
							case 'bibit.php':
								$bibit_select_sql = "SELECT bibit_status, bibit_payment_method
													FROM " . TABLE_BIBIT . "
													WHERE orders_id = '" . (int) $order_id . "'";
								$bibit_result_sql = tep_db_query($bibit_select_sql);
								$bibit_row = tep_db_fetch_array($bibit_result_sql);
	
								if ($bibit_row['bibit_status'] == 'CAPTURED' && in_array($bibit_row['bibit_payment_method'], array('VISA-SSL', 'ECMC-SSL'))) { // Only Captured status and Visa/Master card can be refunded via API
									$action_button_html = tep_button('Refund API', 'Complete this refund payment using ' . $trans_row['store_refund_payments_methods_name'] . ' Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
								}
	
								$action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
								break;
	
							default:
								if (!empty($pwUrl)) {
									$action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								}
	
								$action_button_html .= tep_button('Complete', 'Complete this refund payment', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\', \'' . $refresh . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								break;
						}
					}
                    

                    $payment_batch_available = true;
                    break;
                    
                case '3': // Completed
                    $action_button_html = 'Completed';
                    break;

                case '4': // Canceled
                    $action_button_html = 'Canceled';
                    break;

                default:
                    $action_button_html = '';
                    break;
            }

            return array($action_button_html, $payment_batch_available);
        }

        function _get_payment_method() {
            $payment_methods_filename_array = array();
            $payment_methods_filename_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename
                                                    FROM " . TABLE_PAYMENT_METHODS . " AS pm
                                                    WHERE NOT ISNULL( pm.payment_methods_filename ) ";
            $payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
            while ($payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql)) {
                $payment_methods_filename_array[$payment_methods_filename_row['payment_methods_id']] = $payment_methods_filename_row['payment_methods_filename'];
            }

            return $payment_methods_filename_array;
        }
}
?>