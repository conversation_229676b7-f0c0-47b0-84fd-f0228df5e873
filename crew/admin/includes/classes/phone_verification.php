<?php

class phone_verification {

    public $service_provider = TELEPHONE_VERIFICATION_SERVICES;

    public function __construct() {
        if ($this->is_telesign_enabled()) {
            require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/telesign/classes/telesign_class.php');
            $this->service = new Telesign();
            $this->service->setCustomerID(TELESIGN_CUSTOMER_ID);
            $this->service->setAuthenticationID(TELESIGN_AUTHENTICATION_ID);
        } else if ($this->is_maxmind_enabled()) {
            require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/classes/ogm_maxmind.php');
            $this->service = new ogm_maxmind();
        } else if ($this->is_neutrino_enabled()) {
            require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/neutrino/classes/ogm_neutrino.php');
            $this->service = new ogm_neutrino();
        } else {
            require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/twilio/classes/ogm_twilio.php');
            $this->service = new ogm_twilio();
        }
    }

    public function is_telesign_enabled() {
        return $this->service_provider == 'telesign';
    }

    public function is_maxmind_enabled() {
        return ($this->service_provider == 'maxmind' && MAXMIND_PHONE_VERIFICATION_ENABLED == 'true');
    }

    public function is_neutrino_enabled() {
        return $this->service_provider == 'neutrino';
    }

    public function is_twilio_enabled() {
        return $this->service_provider == 'twilio';
    }

    public function get_call_button($order_id = null, $btn_type = '') {
        if (tep_not_null($order_id)) {
            if ($btn_type == 'gray') {
                echo tep_image_button2('gray_short', 'javascript:void(0)', IMAGE_BUTTON_TEXT_ME_NOW, '', ' id="button_text_me_now" name="NonJSUpdate" style="float:left;" onclick="confirm_code(\'' . $order_id . '\',\'SMS\');"');
            } else {
                echo tep_div_button(1, '&nbsp; ' . IMAGE_BUTTON_TEXT_ME_NOW . ' &nbsp; ', 'phone_verify_form', 'id="button_text_me_now" name="NonJSUpdate" style="float:left;"', 'green_button', false, 'confirm_code(\'' . $order_id . '\',\'SMS\');');
            }
        } else {
            echo tep_div_button(1, '&nbsp; ' . IMAGE_BUTTON_TEXT_ME_NOW . ' &nbsp; ', 'phone_verify_form', 'id="button_text_me_now" name="NonJSUpdate" style="float:left;"', 'green_button', false, 'confirm_code(\'SMS\');');
        }
    }

    public function get_call_language($verify_info) {
        $call_language = '';

        $call_language_select_sql = "	SELECT call_language
										FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
										WHERE customers_info_value ='" . tep_db_prepare_input($verify_info) . "'
											AND info_verification_type = 'telephone'";
        $call_language_result_sql = tep_db_query($call_language_select_sql);

        if ($call_language_row = tep_db_fetch_array($call_language_result_sql)) {
            $call_language = $call_language_row['call_language'];
        }
        return $call_language;
    }

    public function get_language_name($language) {
        $return_string = '';
        if ($this->is_telesign_enabled()) {
            $language_tmp_array = $this->get_supported_languages();
            if (isset($language_tmp_array[$language])) {
                $return_string = $language_tmp_array[$language];
            }
        }
        return $return_string;
    }

    public function get_languages_tab() {
        $return_string = '';

        if ($this->is_telesign_enabled()) {
            $language_tmp_array = $this->get_supported_languages();
            if (tep_not_null($language_tmp_array)) {
                foreach ($language_tmp_array as $language_id => $language_name) {
                    $language_array[] = array('id' => $language_id,
                        'text' => $language_name);
                }
            }
            $return_string = '<tr>
								<td class="inputLabel" width="20%" align="left">' . TEXT_CALL_LANGUAGE . '</td>
					   			<td align="left" class="inputField">' .
                    tep_draw_pull_down_menu("call_language", $language_array, "english", "id=call_language") . '
								</td>
							</tr>';
        }

        return $return_string;
    }

    private function get_phone_info($country, $country_code, $telephone_number) {
        $return_array = FALSE;

        try {
            $this->set_country_code($country_code);

            if ($this->is_telesign_enabled()) {
                $this->service->requestPhoneID($telephone_number);
                $return_array = $this->service->phoneinfo;
            } else if (is_object($this->service)) {
                $return_array = $this->service->telephone_type_request($country, $telephone_number);
            }
        } catch (Exception $e) {
            $this->report_error("Method [" . $this->service_provider . ": requestPhoneID][" . $country_code . '-' . $telephone_number . "] : " . $e->getMessage());
        }

        return $return_array;
    }

    public function get_phone_type($customers_id, $country, $customers_country_code, $customers_telephone) {
        $return_int = null;

        $telephone_info_select_sql = "	SELECT customers_telephone_type, provider
										FROM " . TABLE_MAXMIND_TELEPHONE_IDENTIFICATION . "
										WHERE customers_country_international_dialing_code = '" . tep_db_input($customers_country_code) . "'
											AND customers_telephone ='" . tep_db_input($customers_telephone) . "'
											AND customers_id ='" . tep_db_input($customers_id) . "'";
        $telephone_info_result_sql = tep_db_query($telephone_info_select_sql);
        if ($telephone_info_row = tep_db_fetch_array($telephone_info_result_sql)) {
            if ($telephone_info_row['provider'] == 'telesign') {
                $return_int = $telephone_info_row['customers_telephone_type'] - 300; // Telesign code start with 301 and its 301 = maxmind's 1
            } else {
                $return_int = $telephone_info_row['customers_telephone_type'];
            }
        } else {
            if ($this->request_phoneID($customers_id, $country, $customers_country_code, $customers_telephone)) {
                $telephone_info_select_sql = "	SELECT customers_telephone_type, provider
                                                FROM " . TABLE_MAXMIND_TELEPHONE_IDENTIFICATION . "
                                                WHERE customers_country_international_dialing_code = '" . tep_db_input($customers_country_code) . "'
                                                    AND customers_telephone ='" . tep_db_input($customers_telephone) . "'
                                                    AND customers_id ='" . tep_db_input($customers_id) . "'";
                $telephone_info_result_sql = tep_db_query($telephone_info_select_sql);
                if ($telephone_info_row = tep_db_fetch_array($telephone_info_result_sql)) {
                    if ($telephone_info_row['provider'] == 'telesign') {
                        $return_int = $telephone_info_row['customers_telephone_type'] - 300; // Telesign code start with 301 and its 301 = maxmind's 1
                    } else {
                        $return_int = $telephone_info_row['customers_telephone_type'];
                    }
                }
            }
        }

        return $return_int;
    }

    public function get_service_provider_name($customers_id = null, $customers_country_code = null, $customers_telephone = null) {
        if (tep_not_null($customers_id) && tep_not_null($customers_country_code) && tep_not_null($customers_telephone)) {
            $telephone_info_select_sql = "	SELECT provider
											FROM " . TABLE_MAXMIND_TELEPHONE_IDENTIFICATION . "
											WHERE customers_country_international_dialing_code = '" . tep_db_input($customers_country_code) . "'
												AND customers_telephone ='" . tep_db_input($customers_telephone) . "'
												AND customers_id ='" . tep_db_input($customers_id) . "'";
            $telephone_info_result_sql = tep_db_query($telephone_info_select_sql);
            if ($telephone_info_row = tep_db_fetch_array($telephone_info_result_sql)) {
                return $telephone_info_row['provider'];
            } else {
                return $this->get_service_provider_name();
            }
        } else {
            if ($this->is_telesign_enabled()) {
                return 'Telesign';
            } else if ($this->is_maxmind_enabled()) {
                return 'MaxMind';
            } else if (is_object($this->service)) {
                return $this->service->provider_name;
            }
        }
    }

    // double check on this
    public function get_service_provider_status() {
        if ($this->is_telesign_enabled()) {
            return true;
        } else if ($this->is_maxmind_enabled()) {
            return true;
        } else if ($this->is_neutrino_enabled()) {
            return true;
        } else if ($this->is_twilio_enabled()) {
            return true;
        } else {
            return false; //all phone verification services are off
        }
    }

    public function set_call_language($verify_info, $call_language) {
        if (tep_not_null($verify_info) && tep_not_null($call_language)) {
            $sql_data_array = array('call_language' => tep_db_prepare_input($call_language));

            tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array, 'update', "customers_info_value = '" . tep_db_prepare_input($verify_info) . "' AND info_verification_type = 'telephone'");
        }
    }

    public function get_country_code() {
        return $this->service->getCountryCode();
    }

    // only for telesign
    public function get_request_error() {
        return $this->service->getErrors();
    }

    public function get_request_type() {
        return $this->service->flag;
    }

    public function get_supported_languages() {
        return Telesign::$suportedLanguages;
    }

    public function set_challenge_code($code) {
        $this->service->setPin($code);
    }

    public function set_country_code($country_code) {
        $this->service->setCountryCode($country_code);
    }

    public function set_debug_mode($on) {
        return $this->service->setDebugging($on);
    }

    public function set_language($language) {
        $this->service->setLanguage($language);
    }

    public function set_phonecall_delay($second) {
        $this->service->setDelay($second);
    }

    public function set_redial_count($count) {
        $this->service->setRedialCount($count);
    }

    public function request_call($customer_id, $telephone_number, $country_code = null, $code = null, $languages = null) {
        global $languages_id;
        $request_status = false;

        if ($this->is_telesign_enabled()) {
            $this->set_challenge_code($code);
            $this->set_language($languages);
            $this->set_country_code($country_code);
            try {
                $this->service->requestCall($telephone_number);
                $errors = $this->get_request_error();
                if (tep_not_null($errors[0]['code']) && $errors[0]['code'] !== '0') {
                    $request_status = false;
                    $this->report_error("Method [request_call]<br><br>Error Code: " . $errors[0]['code'] . "<br>Error Message: " . $errors[0]['message'] . "<br>Customer ID: " . $customer_id);
                } else {
                    $request_status = true;
                }
            } catch (Exception $e) {
                $this->report_error("Method [request_call] : " . $e->getMessage());
            }
        }

        return $request_status;
    }

    public function request_sms($customer_id, $telephone_number, $country_code = null, $code = null, $languages = null, $message = null) {
        $request_status = false;

        if ($this->is_telesign_enabled()) {
            $this->set_challenge_code($code);
            $this->set_language($languages);
            $this->set_country_code($country_code);
            if (tep_not_null($message)) {
                $this->service->setMessageText($message, $code); //$code will be display as the position of %s inside message Exp:"Your code is %s,TQ"
            }
            try {
                $this->service->requestSms($telephone_number);
                $errors = $this->get_request_error();
                if (tep_not_null($errors[0]['code']) && $errors[0]['code'] !== '0') {
                    $this->report_error("Method [request_sms]<br><br>Error Code: " . $errors[0]['code'] . "<br>Error Message: " . $errors[0]['message'] . "<br>Customer ID: " . $customer_id);
                    $request_status = false;
                } else {
                    $request_status = true;
                }
            } catch (Exception $e) {
                $this->report_error("Method [request_sms] : " . $e->getMessage());
            }
        }

        return $request_status;
    }

    public function request_phoneID($customers_id, $country, $country_code, $telephone_number) {
        $request_status = FALSE;
        $phone_info = $this->get_phone_info($country, $country_code, $telephone_number);

        if ($phone_info !== FALSE) {
            $telesign_phone_data_sql_array = array(
                'customers_id' => tep_db_prepare_input($customers_id),
                'customers_country_international_dialing_code' => tep_db_prepare_input($this->get_country_code()),
                'customers_telephone' => tep_db_prepare_input($telephone_number),
                'maxmind_telephone_identification_id' => isset($phone_info["REFERENCEID"]) ? tep_db_prepare_input($phone_info["REFERENCEID"]) : '',
                'requested_date' => 'now()',
                'provider' => $this->service_provider
            );

            if (tep_not_null($phone_info['APIERROR^CODE']) && $phone_info['APIERROR^CODE'] != '0') {
                $telesign_phone_data_sql_array['error'] = tep_db_prepare_input($phone_info["APIERROR^CODE"] . '-' . $phone_info["APIERROR^MESSAGE"]);
                $this->report_error("Method[" . $this->service_provider . ": request_phoneID] :<br><br>Error Code: " . $phone_info['APIERROR^CODE'] . "<br>Error Message: " . $phone_info['APIERROR^MESSAGE'] . "<br>Customer ID: " . $customers_id);
            } else if (isset($phone_info['error']) && !empty($phone_info['error'])) {
                $telesign_phone_data_sql_array['error'] = $phone_info['error'];
                $this->report_error("Method [" . $this->service_provider . ": requestPhoneID]<br><br>Error : " . $phone_info['error'] . "<br>Customer ID: " . $customers_id);
            } else {
                $request_status = TRUE;
                $telesign_phone_data_sql_array['customers_telephone_type'] = tep_db_prepare_input((int) $phone_info["TYPEOFPHONE"]);
                $telesign_phone_data_sql_array['city'] = (isset($phone_info["CITY"]) ? (tep_not_null($phone_info["CITY"]) ? tep_db_prepare_input($phone_info["CITY"]) : 'NULL') : 'NULL');
                $telesign_phone_data_sql_array['state'] = (isset($phone_info["STATE"]) ? (tep_not_null($phone_info["STATE"]) ? tep_db_prepare_input($phone_info["STATE"]) : 'NULL') : 'NULL');
                $telesign_phone_data_sql_array['postcode'] = (isset($phone_info["ZIP"]) ? (tep_not_null($phone_info["ZIP"]) ? tep_db_prepare_input($phone_info["ZIP"]) : 'NULL') : 'NULL');
                $telesign_phone_data_sql_array['countries_name'] = (isset($phone_info["COUNTRYNAME"]) ? (tep_not_null($phone_info["COUNTRYNAME"]) ? tep_db_prepare_input($phone_info["COUNTRYNAME"]) : 'NULL') : 'NULL');
                $telesign_phone_data_sql_array['latitude'] = (isset($phone_info["LATITUDE"]) ? (tep_not_null($phone_info["LATITUDE"]) ? tep_db_prepare_input((float) $phone_info["LATITUDE"]) : 'NULL') : 'NULL');
                $telesign_phone_data_sql_array['longitude'] = (isset($phone_info["LONGITUDE"]) ? (tep_not_null($phone_info["LONGITUDE"]) ? tep_db_prepare_input((float) $phone_info["LONGITUDE"]) : 'NULL') : 'NULL');
            }

            $customers_id_select_sql = "	SELECT customers_id
                                            FROM " . TABLE_MAXMIND_TELEPHONE_IDENTIFICATION . "
                                            WHERE customers_id = '" . (int) $customers_id . "'
                                                AND customers_country_international_dialing_code ='" . tep_db_input($this->get_country_code()) . "'
                                                AND customers_telephone ='" . tep_db_input($telephone_number) . "'";
            $customers_id_result_sql = tep_db_query($customers_id_select_sql);
            if (tep_db_num_rows($customers_id_result_sql) < 1) {
                if (!tep_db_perform(TABLE_MAXMIND_TELEPHONE_IDENTIFICATION, $telesign_phone_data_sql_array)) {
                    $request_status = FALSE;
                }
            } else {
                if (!tep_db_perform(TABLE_MAXMIND_TELEPHONE_IDENTIFICATION, $telesign_phone_data_sql_array, 'update', "customers_id = '" . (int) $customers_id . "' AND customers_country_international_dialing_code ='" . tep_db_input($this->get_country_code()) . "' AND customers_telephone ='" . tep_db_input($telephone_number) . "'")) {
                    $request_status = FALSE;
                }
            }

            unset($telesign_phone_data_sql_array);
        }

        unset($phone_info);

        return $request_status;
    }

    public function get_customer_phone_verify_codes() {
        require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/telesign/classes/telesign_class.php');
        require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/classes/ogm_maxmind.php');
        require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/neutrino/classes/ogm_neutrino.php');
        require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/twilio/classes/ogm_twilio.php');

        $obj_telesign = new Telesign();
        $obj_maxmind = new ogm_maxmind();
        $obj_neutrino = new ogm_neutrino();
        $obj_twilio = new ogm_twilio();

        return array('neutrino' => $obj_neutrino->phone_type, 'telesign' => $obj_telesign->phone_type, 'maxmind' => $obj_maxmind->phone_type, 'twilio' => $obj_twilio->phone_type, 'nexmo' => array(
                "mobile" => 2,
        ));
    }

    public function report_error($message, $extend_subject = '') {
        $serverName = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-';
        $subject = '[OFFGAMERS] Phone verification Reporting' . $extend_subject . ' from ' . $serverName;

        @tep_mail('OffGamers', '<EMAIL>', $subject, $message . "<br>Request : " . getenv('REQUEST_URI') . ' [ ' . date("F j, Y H:i") . ' ]', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>