<?php

class pipwave {

    private $key, $secret, $paymentUrl, $oid;
    public $orderSite, $checkoutSite, $ogPgCode, $ogPmId;
    //notification data
    public $pw_id, $pipwave_score, $pg_raw_data, $rules_action, $payment_method_code, $reversible_payment, $require_capture, $mobile_number_verification, $transaction_status, $risk_service_type;
    protected $orders_id, $status, $api_key, $notification_id, $notification_date, $amount, $tax_exempted_amount;
    protected $processing_fee_amount, $tax_amount, $total_amount, $final_amount, $currency_code, $type, $subscription_token;
    protected $charge_index, $mobile_number, $pg_status, $pg_reason;
    protected $pg_date, $extra_param1, $extra_param2, $extra_param3;

    public function pipwave($oid = null, $site_id = null) {
        $this->paymentUrl = PIPWAVE_PAYMENT_API_URL;
        $this->nameCheckUrl = PIPWAVE_MERCHANT_AML_API_URL;
        if(isset($site_id)){
            $this->orderSite = $site_id;
        }
        if($oid) {
            $this->checkoutSite = $this->orderCheckoutType($oid);
            $this->orderSite = $this->orderSiteID($oid);
            $this->init($oid);
            $this->oid = $oid;
        }
       
        if ($this->orderSite == '5') {
            $this->key = PIPWAVE_G2G_MERCHANT_API_KEY;
            $this->secret = PIPWAVE_G2G_MERCHANT_API_SECRET;
        } else {
            $this->key = PIPWAVE_MERCHANT_API_KEY;
            $this->secret = PIPWAVE_MERCHANT_API_SECRET;
        }
        

    }

    protected function init($oid) {
        $_sel_sql = "	SELECT *
                        FROM " . TABLE_PIPWAVE_PAYMENT . "
                        WHERE orders_id = '" . (int) $oid . "'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            foreach($_row_sql as $key => $val) {
                $this->$key = $val;
            }
        }
    }

    protected function orderSiteID($oid) {
        $result = '';

        $_sel_sql = "	SELECT orders_extra_info_value
                        FROM " . TABLE_ORDERS_EXTRA_INFO . "
                        WHERE orders_id = '" . (int) $oid . "'
                            AND orders_extra_info_key = 'site_id'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $result = $_row_sql['orders_extra_info_value'];
        }

        return $result;
    }

    public function orderCheckoutType($oid) {
        $result = '';

        $_sel_sql = "	SELECT orders_extra_info_value
                        FROM " . TABLE_ORDERS_EXTRA_INFO . "
                        WHERE orders_id = '" . (int) $oid . "'
                            AND orders_extra_info_key = 'checkoutSite'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $result = $_row_sql['orders_extra_info_value'];
        }

        return $result;
    }

    public function refundAPI($refundAmount, $extraParam=null) {
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'refund',
                'api_key' => $this->key,
                'op' => 'submit',
                'txn_id' => (string)$this->oid,
                'refund_amount' => $refundAmount,
                'api_secret' => $this->secret
            );
            if($this->pw_id) {
                $requestData['pw_id'] = $this->pw_id;
            }
            if($extraParam) {
                $requestData['extra_param'] = $extraParam;
            }
            $requestData['signature'] = $this->generateSign($requestData);
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'refund');

            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    public function lastNameCheck($customer_id){
        $time = time();
        $data = [
            'action' => 'get-latest-user-name-screen-result',
            'user_id' => $customer_id,
            'timestamp' => $time,
            'api_key' => $this->key,
            'api_secret' => $this->secret,
        ];
        $data['signature'] = $this->generateSign($data);
        unset($requestData['api_secret']);
        $json_data = json_encode($data);
        $result = $this->connectCurl($json_data, PIPWAVE_MERCHANT_AML_API_URL);

        return $result;
    }

    public function newNameCheck($customer_id){
        $customer_log_select_sql = "SELECT customers_firstname, customers_lastname, customers_gender,customers_dob, customers_telephone,customers_country_dialing_code_id
        FROM " . TABLE_CUSTOMERS . " WHERE customers_id='" . (int) $customer_id . "'";
        $customer_log_result_sql = tep_db_query($customer_log_select_sql);
        $customer_old_log_row = tep_db_fetch_array($customer_log_result_sql);

        $country_select_sql = "SELECT countries_international_dialing_code, countries_iso_code_2 
        FROM " . TABLE_COUNTRIES . " WHERE countries_id='" . (int) $customer_old_log_row['customers_country_dialing_code_id'] . "'";
        $country_result_sql = tep_db_query($country_select_sql);
        $country_row = tep_db_fetch_array($country_result_sql);
      
        $time = time();
        $data = [
            'action' => 'user-screening',
            'user_id' => (string) $customer_id,
            'timestamp' => $time,
            'api_key' => $this->key,
            'api_secret' => $this->secret,
            'firstname' => $customer_old_log_row['customers_firstname'],
            'lastname' => $customer_old_log_row['customers_lastname'],
        ];
        if ( $customer_old_log_row['customers_dob'] ) {
            $data['dob'] = $customer_old_log_row['customers_dob'];
        }
        $data['signature'] = $this->generateSign($data);
        if($country_row){
            $data['phone'] = [
                "intl_num"=> $country_row['countries_international_dialing_code']."-".$customer_old_log_row['customers_telephone'],
                "dialing_code"=> $country_row['countries_international_dialing_code'],
                "country_iso2"=> $country_row['countries_iso_code_2'],
                "local_num" => $customer_old_log_row['customers_telephone'],
            ];
        }
        unset($requestData['api_secret']);
        $json_data = json_encode($data);
        $result = $this->connectCurl($json_data,PIPWAVE_MERCHANT_AML_API_URL);   
        $this->savenameScreenResult($customer_id, $result);
        return $result;
    }

    public function requeryAPI() {
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'requery',
                'api_key' => $this->key,
                'txn_id' => (string)$this->oid,
                'api_secret' => $this->secret,
            );
            if($this->pw_id) {
                $requestData['pw_id'] = $this->pw_id;
            }
            $requestData['signature'] = $this->generateSign($requestData);
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'requery');

            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    public function tagChargebackAPI($stage, $reason="unauthorized") {
        //stage = “dispute”, “chargeback” or “chargeback reversed”.
        //reason = “unauthorized”, “non_receipt”, “not_as_described” or “others”
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'tag-chargeback',
                'api_key' => $this->key,
                'api_secret' => $this->secret,
                'txn_id' => (string)$this->oid,
                'stage' => $stage,
            );
            if($this->pw_id) {
                $requestData['pw_id'] = $this->pw_id;
            }
            $requestData['signature'] = $this->generateSign($requestData);
            if($reason) {
                $requestData['reason'] = $reason;
            }
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'tag-chargeback');

            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    public function confirmPaymentAPI($reason=null) {
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'confirm-payment',
                'api_key' => $this->key,
                'api_secret' => $this->secret,
                'txn_id' => (string)$this->oid,
            );
            if($this->pw_id) {
                $requestData['pw_id'] = $this->pw_id;
                $requestData['ref_tag'] = $this->pw_id;
            }
            if($reason) {
                $requestData['reason'] = $reason;
            }
            $requestData['signature'] = $this->generateSign($requestData);
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'confirm-payment');

            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    public function captureAPI($amount=null) {
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'capture-payment',
                'api_key' => $this->key,
                'txn_id' => (string)$this->oid,
                'api_secret' => $this->secret,
                'capture_amount'=>$amount
            );
            if($this->pw_id) {
                $requestData['pw_id'] = $this->pw_id;
            }
            $requestData['signature'] = $this->generateSign($requestData);
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'capture-payment');
            if(isset($result['status']) && $result['status']==200) {
                $pipwave_payment_data_array = array(
                    'require_capture' => 0,
                );
                tep_db_perform(TABLE_PIPWAVE_PAYMENT, $pipwave_payment_data_array, 'update', 'orders_id="' . tep_db_input($this->oid) . '"');
            }
            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    public function acceptOrder() {
        if($this->oid) {
            $timestamp = time();
            $requestData = array(
                'timestamp' => $timestamp,
                'action' => 'accept-order',
                'api_key' => $this->key,
                'txn_id' => (string)$this->oid,
                'api_secret' => $this->secret,
            );
            
            $requestData['signature'] = $this->generateSign($requestData);
            unset($requestData['api_secret']);
            $json_data = json_encode($requestData);
            $result = $this->connectCurl($json_data);
            $this->saveHistory($result, 'accept-order');
            
            return $result;
        } else {
            return array('status' => 'error', 'message'=>'OrderId/PwId not found');
        }
    }

    
    private function generateSign($array) {
        ksort($array);
        $s = '';
        foreach($array as $key => $value) {
            $s .= $key . ':' . $value;
        }
        return sha1($s);
    }

    private function connectCurl($data, $url = null) {

        if(is_null($url)){
            $url = $this->paymentUrl;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('x-api-key:' . $this->key));
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $apiResult = curl_exec($ch);
        $error = curl_error($ch);
        return ($apiResult) ? json_decode($apiResult, true) : array();
    }

    public function paymentStatusMapper($code) {
        $status = '';
        switch($code) {
            case 0:
                $status = 'Pending';
                break;
            case 1:
                $status = 'Failed';
                break;
            case 2:
                $status = 'Cancelled';
                break;
            case 5:
                $status = 'Processing';
                break;
            case 10:
                $status = 'Paid';
                break;
            case 13:
                $status = 'Disputed';
                break;
            case 15:
                $status = 'Chargeback';
                break;
            case 20:
                $status = 'Full refund';
                break;
            case 25:
                $status = 'Partial refund';
                break;
        }

        return $status;
    }
    public function paymentSubStatusMapper($code) {
        $status = '';
        switch($code) {
            case 501:
                $status = 'Required capture';
                break;
            case 502:
                $status = 'Payment confirmed';
                break;
        }

        return $status;
    }

    public function apiStatusMapper($code) {
        $status = '';
        switch($code) {
            case 200:
                $status = 'OK';
                break;
            case 400:
                $status = 'Failed to fulfill request, see message';
                break;
            case 401:
                $status = 'Invalid API signature';
                break;
            case 403:
                $status = 'Invalid API key';
                break;
            case 404:
                $status = 'Invalid action';
                break;
            case 500:
                $status = 'Generic server error, see message';
                break;
            case 1001:
                $status = 'Currency not offered by Merchant';
                break;
            case 1002:
                $status = 'Transaction ID (txn_id) duplicate';
                break;
            case 2001:
                $status = 'Unsupported payment method, see message';
                break;
            case 2002:
                $status = 'Token has expired';
                break;
            case 3001:
                $status = 'Notification with warning (see pw_reason data)';
                break;
            case 4001:
                $status = 'Payment is already fully refunded';
                break;
            case 4002:
                $status = 'Payment not finalized yet';
                break;
            case 4003:
                $status = 'Refunded amount is more than current amount';
                break;
            case 4004:
                $status = 'Refund not supported';
                break;
            case 4005:
                $status = 'Partial refund not supported';
                break;
            case 4006:
                $status = 'The transaction already has a reversal in process. You must wait for the process to complete first.';
                break;
            case 4501:
                $status = 'No disputed chargeback transaction to reversed';
                break;
            case 9001:
                $status = 'Parameter error, see message';
                break;
        }

        return $status;
    }

    public function pipwavePaymentMapper($site = 'pipwave', $val) {
        switch($site) {
            case 'pipwave':
                $condition = 'pipwave_payment_code';
                break;
            case 'pm_id':
                $condition = 'pm_id';
                break;
            case 'pg_id':
                $condition = 'pg_id';
                break;
        }
        $result = array();
        $_sel_sql = "	SELECT *
                        FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . "
                        WHERE ".$condition." = '" . $val . "'
                            AND site_id = '".$this->orderSite."'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $result = $_row_sql;
        }
        return $result;
    }

    public function generateRuleResultUrl($ruleId) {
        $ruleUrl = '';
        if($this->oid && $this->pw_id) {
            $time = time();
            $hash = hash('sha256', $this->oid . $this->pw_id . $ruleId . $time . $this->key .  $this->secret);
            $getArr = array(
                'rule_id' => $ruleId,
                'pw_id' => $this->pw_id,
                'txn_id' => $this->oid,
                'merchant_key' => $this->key,
                'timestamp' => $time,
                'sign' => $hash
            );
            $ruleUrl = PIPWAVE_RULE_DIRECT_VIEW_URL . '?' . http_build_query($getArr);
        }
        return $ruleUrl;
    }

    private function saveHistory($apiResponse, $method=null) {
        $apiStatus = '';
        $txnStatus = '';
        if (isset($apiResponse['status'])) {
            $apiStatus = $this->apiStatusMapper($apiResponse['status']);
            if($apiResponse['status'] == 200) {
                if(isset($apiResponse['transaction_status'])) {
                    $txnStatus = $this->paymentStatusMapper($apiResponse['transaction_status']);
                }
            }
        } else {
            $apiStatus = 'API FAILED';
        }

        $pipwave_payment_history_data_array = array(
            'orders_id' => $this->oid,
            'status' => ($method) ? $method . ' - ' . $apiStatus : $apiStatus,
            'message' =>($apiResponse['message']) ?  $apiResponse['message'] : $txnStatus,
            'notification_date' => 'now()'
        );
        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
        $pipwave_payment_history_data_array['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
        tep_db_perform(TABLE_PIPWAVE_PAYMENT_HISTORY, $pipwave_payment_history_data_array);
    }

    public function saveNameScreenResult($customers_id, $result ){
        $time = time();
        if ( $result['status'] == 200 ) {
            if ( isset($result['result'][0]['matches']) && !empty($result['result'][0]['matches']) ) {
                //save to customer remark
                $remark = "<b>Name Verification Matches</b> :";
                foreach ( $result['result'][0]['matches'] as $matches ) {
                    $remark .= $matches['name'] . ' - ' . $matches['reason_listed'] .' ';
                }


                //save to customer setting
                $name_verification_customers_setting = [
                    "name_verification_last_name_check_called" => date("Y-m-d H:i:s", $time),
                    "name_verification_match_count"            => count($result['result'][0]['matches']),
                    "name_verification_id_of_name_check"       => $result['result'][0]['aml_id'],
                    "name_verification_match_signature"        => self::generateNameVerificationMatchSignature($result['result'][0]['matches']),
                    'name_verification_source'                 => "api"
                ];
            } else {
                $remark                              = "<b>No Verification Matches Found</b>";
                // not found
                $name_verification_customers_setting = [
                    "name_verification_last_name_check_called" => date("Y-m-d H:i:s", $time),
                    "name_verification_match_count"            => 0,
                    "name_verification_id_of_name_check"       => $result['result'][0]['aml_id'],
                    "name_verification_match_signature"        => self::generateNameVerificationMatchSignature([]),
                    'name_verification_source'                 => "api"
                ];
            }

            if (tep_not_null($remark)) {
                // $cust_remarks_data_array = array(            REQUEST BY AFT TO REMOVE
                //     'customers_id' => (int) $customers_id,
                //     'date_remarks_added' => 'now()',
                //     'remarks' => $remark,
                //     'remarks_added_by' => 'system'
                // );
                // tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $cust_remarks_data_array);
            }

            foreach ( $name_verification_customers_setting as $key => $value ) {
                $customer_setting_sql = "SELECT customers_setting_value FROM " . TABLE_CUSTOMERS_SETTING . " 
                WHERE customers_id = '" . tep_db_input($customers_id) . "' 
                AND customers_setting_key = '" . $key . "'";

                $customer_setting = tep_db_query($customer_setting_sql);
                if ($customer_setting_row = tep_db_fetch_array($customer_setting)) {
                    $query = " 	UPDATE " . TABLE_CUSTOMERS_SETTING . " 
                                SET customers_setting_value = '" . $value . "',
                                    updated_datetime = '" . date("Y-m-d H:i:s", $time) . "'
                                WHERE customers_id = '" . tep_db_input($customers_id) . "' 
                                    AND customers_setting_key = '" . $key ."'";
                    tep_db_query($query); 
                } else {
                    tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_SETTING . " (customers_id, customers_setting_key, customers_setting_value, created_datetime, updated_datetime) VALUES 
                    ('" . $customers_id  . "', '" .  $key ."', '" . $value. "', '" . date("Y-m-d H:i:s", $time) ."', '" . date("Y-m-d H:i:s", $time) . "')");

                }
              
            
            }
            return $name_verification_customers_setting;
        }
    }

    public static function generateNameVerificationMatchSignature($matches){
        if(!empty($matches)){
            $name_array=[];
            foreach($matches as $match){
                $name_array[]=$match['name'].'-'.$match['reason_listed'];
            }
            sort($name_array);
            $verification_string = implode(",",$name_array);
            return md5($verification_string);

        }else{
            return "";
        }
    }

    public static function get_paypal_pg_raw_data_capture($pgRawData){

        if(isset($pgRawData['id'])) {
            $pgRawData['txn_id'] = $pgRawData['id'];
        }
        if(isset($pgRawData['PaymentInfo']['ParentTransactionID'])) { // no data yet
            $pgRawData['parent_txn_id'] = $pgRawData['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($pgRawData['payer']['name']['given_name'])) {
            $pgRawData['first_name'] = $pgRawData['payer']['name']['given_name'];
        }
        if(isset($pgRawData['payer']['name']['surname'])) {
            $pgRawData['last_name'] = $pgRawData['payer']['name']['surname'];
        }
        if(isset($pgRawData['payer']['address']['country_code'])) {
            $pgRawData['residence_country'] = $pgRawData['payer']['address']['country_code'];
        }
        if(isset($pgRawData['payer']['email_address'])) {
            $pgRawData['payer_email'] = $pgRawData['payer']['email_address'];
        }
        if(isset($pgRawData['payer']['payer_id'])) {
            $pgRawData['payer_id'] = $pgRawData['payer']['payer_id'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Name'])) { // no data yet
            $pgRawData['address_name'] = $pgRawData['PayerInfo']['Address']['Name'];
        }else{
            if(isset($pgRawData['payer']['name']['given_name']) && isset($pgRawData['payer']['name']['surname']) ){
                $pgRawData['address_name'] = $pgRawData['payer']['name']['given_name']." ".$pgRawData['payer']['name']['surname'];
            }
        }

        if(isset($pgRawData['PayerInfo']['Address']['AddressStatus'])) { // no data yet
            $pgRawData['address_status'] = $pgRawData['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($pgRawData['payer']['address']['address_line_1']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['address_line_1'])) {
            $pgRawData['address_street'] = (isset($pgRawData['payer']['address']['address_line_1'])) ? $pgRawData['payer']['address']['address_line_1'] : $pgRawData['purchase_units'][0]['shipping']['address']['address_line_1'];
        }
        if(isset($pgRawData['payer']['address']['admin_area_1']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['admin_area_1'])) {
            $pgRawData['address_city'] = (isset($pgRawData['payer']['address']['admin_area_1'])) ? $pgRawData['payer']['address']['admin_area_1'] : $pgRawData['purchase_units'][0]['shipping']['address']['admin_area_1'];
        }
        if(isset($pgRawData['payer']['address']['admin_area_2']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['admin_area_2']) ) {
            $pgRawData['address_state'] = (isset($pgRawData['payer']['address']['admin_area_2'])) ? $pgRawData['payer']['address']['admin_area_2'] : $pgRawData['purchase_units'][0]['shipping']['address']['admin_area_2'];
        }
        if(isset($pgRawData['payer']['address']['postal_code']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['postal_code']) ) {
            $pgRawData['address_zip'] = (isset($pgRawData['payer']['address']['postal_code']) ? $pgRawData['payer']['address']['postal_code'] : $pgRawData['purchase_units'][0]['shipping']['address']['postal_code'] );
        }
        if(isset($pgRawData['payer']['address']['country_code']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['country_code']) ) {
            $pgRawData['address_country'] = (isset($pgRawData['payer']['address']['country_code'])) ? $pgRawData['payer']['address']['country_code'] : $pgRawData['purchase_units'][0]['shipping']['address']['country_code'] ;
        }
        if(isset($pgRawData['PayerInfo']['PayerStatus'])) { // no data
            $pgRawData['payer_status'] = $pgRawData['PayerInfo']['PayerStatus'];
        }
        if(isset($pgRawData['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'])) {
            $pgRawData['pending_reason'] = $pgRawData['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'];
        }
        if(isset($pgRawData['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'])) {
            $pgRawData['protection_eligibility'] = $pgRawData['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'];
        }

        return $pgRawData;

    }

    public static function get_paypal_pg_raw_data_requery($pgRawData){

        $requery = array();

        if(isset($pgRawData['requery'])){
            $requery = $pgRawData['requery'];
        }

        if(isset($requery['id'])) {
            $pgRawData['txn_id'] = $requery['id'];
        }
        if(isset($requery['PaymentInfo']['ParentTransactionID'])) { // no data yet
            $pgRawData['parent_txn_id'] = $requery['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($requery['payer']['name']['given_name'])) {
            $pgRawData['first_name'] = $requery['payer']['name']['given_name'];
        }
        if(isset($requery['payer']['name']['surname'])) {
            $pgRawData['last_name'] = $requery['payer']['name']['surname'];
        }
        if(isset($requery['payer']['address']['country_code'])) {
            $pgRawData['residence_country'] = $requery['payer']['address']['country_code'];
        }
        if(isset($requery['payer']['email_address'])) {
            $pgRawData['payer_email'] = $requery['payer']['email_address'];
        }
        if(isset($requery['payer']['payer_id'])) {
            $pgRawData['payer_id'] = $requery['payer']['payer_id'];
        }
        if(isset($requery['PayerInfo']['Address']['Name'])) { // no data yet
            $pgRawData['address_name'] = $requery['PayerInfo']['Address']['Name'];
        }else{
            if(isset($requery['payer']['name']['given_name']) && isset($requery['payer']['name']['surname']) ){
                $pgRawData['address_name'] = $requery['payer']['name']['given_name']." ".$requery['payer']['name']['surname'];
            }
        }

        if(isset($requery['PayerInfo']['Address']['AddressStatus'])) { // no data yet
            $pgRawData['address_status'] = $requery['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($requery['payer']['address']['address_line_1']) || isset($requery['purchase_units'][0]['shipping']['address']['address_line_1'])) {
            $pgRawData['address_street'] = (isset($requery['payer']['address']['address_line_1'])) ? $requery['payer']['address']['address_line_1'] : $requery['purchase_units'][0]['shipping']['address']['address_line_1'];
        }
        if(isset($requery['payer']['address']['admin_area_1']) || isset($requery['purchase_units'][0]['shipping']['address']['admin_area_1'])) {
            $pgRawData['address_city'] = (isset($requery['payer']['address']['admin_area_1'])) ? $requery['payer']['address']['admin_area_1'] : $requery['purchase_units'][0]['shipping']['address']['admin_area_1'];
        }
        if(isset($requery['payer']['address']['admin_area_2']) || isset($requery['purchase_units'][0]['shipping']['address']['admin_area_2']) ) {
            $pgRawData['address_state'] = (isset($requery['payer']['address']['admin_area_2'])) ? $requery['payer']['address']['admin_area_2'] : $requery['purchase_units'][0]['shipping']['address']['admin_area_2'];
        }
        if(isset($requery['payer']['address']['postal_code']) || isset($requery['purchase_units'][0]['shipping']['address']['postal_code']) ) {
            $pgRawData['address_zip'] = (isset($requery['payer']['address']['postal_code']) ? $requery['payer']['address']['postal_code'] : $requery['purchase_units'][0]['shipping']['address']['postal_code'] );
        }
        if(isset($requery['payer']['address']['country_code']) || isset($requery['purchase_units'][0]['shipping']['address']['country_code']) ) {
            $pgRawData['address_country'] = (isset($requery['payer']['address']['country_code'])) ? $requery['payer']['address']['country_code'] : $requery['purchase_units'][0]['shipping']['address']['country_code'] ;
        }
        if(isset($requery['PayerInfo']['PayerStatus'])) { // no data
            $pgRawData['payer_status'] = $requery['PayerInfo']['PayerStatus'];
        }
        if(isset($requery['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'])) {
            $pgRawData['pending_reason'] = $requery['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'];
        }
        if(isset($requery['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'])) {
            $pgRawData['protection_eligibility'] = $requery['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'];
        }

        return $pgRawData;

    }

    public static function get_paypal_pg_raw_data_old($pgRawData){
        return $pgRawData;
    }

    public static function get_paypal_pg_raw_data_legacy($pgRawData){

        if(isset($pgRawData['PaymentInfo']['TransactionID'])) {
            $pgRawData['txn_id'] = $pgRawData['PaymentInfo']['TransactionID'];
        }
        if(isset($pgRawData['PaymentInfo']['ParentTransactionID'])) {
            $pgRawData['parent_txn_id'] = $pgRawData['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($pgRawData['PayerInfo']['PayerName']['FirstName'])) {
            $pgRawData['first_name'] = $pgRawData['PayerInfo']['PayerName']['FirstName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerName']['LastName'])) {
            $pgRawData['last_name'] = $pgRawData['PayerInfo']['PayerName']['LastName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerCountry'])) {
            $pgRawData['residence_country'] = $pgRawData['PayerInfo']['PayerCountry'];
        }
        if(isset($pgRawData['PayerInfo']['Payer'])) {
            $pgRawData['payer_email'] = $pgRawData['PayerInfo']['Payer'];
        }
        if(isset($pgRawData['PayerInfo']['PayerID'])) {
            $pgRawData['payer_id'] = $pgRawData['PayerInfo']['PayerID'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Name'])) {
            $pgRawData['address_name'] = $pgRawData['PayerInfo']['Address']['Name'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['AddressStatus'])) {
            $pgRawData['address_status'] = $pgRawData['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Street1'])) {
            $pgRawData['address_street'] = $pgRawData['PayerInfo']['Address']['Street1'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['CityName'])) {
            $pgRawData['address_city'] = $pgRawData['PayerInfo']['Address']['CityName'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['StateOrProvince'])) {
            $pgRawData['address_state'] = $pgRawData['PayerInfo']['Address']['StateOrProvince'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['PostalCode'])) {
            $pgRawData['address_zip'] = $pgRawData['PayerInfo']['Address']['PostalCode'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['CountryName'])) {
            $pgRawData['address_country'] = $pgRawData['PayerInfo']['Address']['CountryName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerStatus'])) {
            $pgRawData['payer_status'] = $pgRawData['PayerInfo']['PayerStatus'];
        }
        if(isset($pgRawData['PaymentInfo']['PendingReason'])) {
            $pgRawData['pending_reason'] = $pgRawData['PaymentInfo']['PendingReason'];
        }
        if(isset($pgRawData['PaymentInfo']['ProtectionEligibility'])) {
            $pgRawData['protection_eligibility'] = $pgRawData['PaymentInfo']['ProtectionEligibility'];
        }

        return $pgRawData;
    }
}
