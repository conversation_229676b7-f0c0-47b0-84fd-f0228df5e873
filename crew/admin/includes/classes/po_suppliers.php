<?php
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_SUPPLIERS_LIST)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_SUPPLIERS_LIST);
}

class po_suppliers {
	var $identity, $identity_email;
	var $login_site;
	
	// class constructor
    function po_suppliers($identity, $identity_email) {
		
		$this->identity = $identity;	// Admin user
      	$this->identity_email = $identity_email;	// Admin user
      	
      	$this->login_site = '3';
	}
	
	function update_po_supplier_status($po_supplier_id, $to_status) {
		$supplier_status_update_sql = "	UPDATE " . TABLE_PO_SUPPLIERS . " 
										SET po_supplier_status = '".(int)$to_status."' 
										WHERE po_suppliers_id='" . (int)$po_supplier_id . "'";
		tep_db_query($supplier_status_update_sql);
	}
        
    function update_po_supplier_company_code($input_array) {
        $po_supplier_id = tep_db_prepare_input($input_array['po_supplier_id']);
        $po_supplier_company_code = tep_db_prepare_input($input_array['po_supplier_company_code']);

        $supplier_company_update_sql = "UPDATE " . TABLE_PO_SUPPLIERS . " 
                                SET po_supplier_company_code = '".$po_supplier_company_code."'
                                WHERE po_suppliers_id = '" . (int)$po_supplier_id . "'";
        tep_db_query($supplier_company_update_sql);
    }
	
	function update_po_supplier_agreement_terms($input_array) {
		$po_supplier_id = tep_db_prepare_input($input_array['po_supplier_id']);
		$agreement_start_date = tep_db_prepare_input($input_array['po_supplier_agreement_start_date']);
		$agreement_end_date = tep_db_prepare_input($input_array['po_supplier_agreement_end_date']);
		$agreement_discount_terms = tep_db_prepare_input($input_array['po_supplier_agreement_discount_terms']);
		
		$supplier_agreement_update_sql = "	UPDATE " . TABLE_PO_SUPPLIERS . " 
											SET po_supplier_agreement_start_date = '".$agreement_start_date."', 
												po_supplier_agreement_end_date = '".$agreement_end_date."', 
												po_supplier_agreement_discount_terms = '".$agreement_discount_terms."'
											WHERE po_suppliers_id='" . (int)$po_supplier_id . "'";
		tep_db_query($supplier_agreement_update_sql);
	}
	
	function update_po_supplier_po_reference_counter($po_supplier_id, $new_counter) {
		$supplier_po_ref_update_sql = "	UPDATE " . TABLE_PO_SUPPLIERS . " 
										SET po_supplier_po_ref_counter = '".(int)$new_counter."' 
										WHERE po_suppliers_id='" . (int)$po_supplier_id . "'";
		tep_db_query($supplier_po_ref_update_sql);
	}
	
	function batch_update_po_supplier_status($input_array, $to_status) {
		for ($i=0; $i < count($input_array["po_supplier_batch"]); $i++) {
			if (isset($input_array["po_supplier_batch"][$i])) {
				$supplier_status_update_sql = "	UPDATE " . TABLE_PO_SUPPLIERS . " 
  												SET po_supplier_status = '".$to_status."' 
  												WHERE po_suppliers_id = '" . (int)$input_array["po_supplier_batch"][$i] . "'";
				tep_db_query($supplier_status_update_sql);
				
				// add action remark
				$remarks_data_array = array('customers_id' => $input_array["po_supplier_batch"][$i],
											'date_remarks_added' => 'now()',
											'remarks' => 'Change of Status',
											'remarks_added_by' => $_SESSION['login_id']);
				tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
			}
		}
	}
	
    function insert_new_po_supplier($input_array, &$messageStack) {
	if (!isset($input_array['po_supplier_po_ref_counter']) || $input_array['po_supplier_po_ref_counter']=='') {
            $input_array['po_supplier_po_ref_counter'] = '0';
        }
			
	$customers_data_array = array(
            'customers_groups_id' => '0',
            'customers_firstname' => tep_db_prepare_input($input_array['po_supplier_firstname']),
            'customers_lastname' => tep_db_prepare_input($input_array['po_supplier_lastname']),
            'customers_email_address' => tep_db_prepare_input($input_array['po_supplier_email_address']),
            'customers_telephone' => tep_db_prepare_input($input_array['po_supplier_telephone']),
            'customers_fax' => tep_db_prepare_input($input_array['po_supplier_fax']),
            'customers_qq' => tep_db_prepare_input($input_array['po_supplier_qq']),
            'customers_msn' => tep_db_prepare_input($input_array['po_supplier_msn']),
            'customers_status' => 0,
            'customers_login_sites' => $this->login_site
	);
		
	$po_data_array = array(
            'po_supplier_code' => tep_db_prepare_input($input_array['po_supplier_code']),
            'po_supplier_po_ref_year' => tep_db_prepare_input($input_array['po_supplier_po_ref_year']),
            'po_supplier_po_ref_counter' => tep_db_prepare_input($input_array['po_supplier_po_ref_counter']),
            'po_payment_type' => tep_db_prepare_input($input_array['po_payment_type']),
            'po_payment_term' => tep_db_prepare_input($input_array['po_payment_term']),
            'po_days_pay_wsc' => tep_db_prepare_input($input_array['po_days_pay_wsc']),
            'po_supplier_company_code' => tep_db_prepare_input($input_array['po_supplier_company_code']),
            'po_supplier_status' => 0
	);
		
	if (isset($input_array['agreement_start_date']) && tep_not_null($input_array['agreement_start_date'])) {
            $po_data_array['po_supplier_agreement_start_date'] = tep_db_prepare_input($input_array['agreement_start_date']);
	}
		
	if (isset($input_array['agreement_end_date']) && tep_not_null($input_array['agreement_end_date'])) {
            $po_data_array['po_supplier_agreement_end_date'] = tep_db_prepare_input($input_array['agreement_end_date']);
	}
		
	if (isset($input_array['agreement_discount_terms']) && tep_not_null($input_array['agreement_discount_terms'])) {
            $po_data_array['po_supplier_agreement_discount_terms'] = tep_db_prepare_input($input_array['agreement_discount_terms']);
	}
		
	$address_data_array = array(
            'entry_firstname' => tep_db_prepare_input($input_array['po_supplier_firstname']),
            'entry_lastname' => tep_db_prepare_input($input_array['po_supplier_lastname']),
            'entry_street_address' => tep_db_prepare_input($input_array['po_supplier_street_address']),
            'entry_suburb' => tep_db_prepare_input($input_array['po_supplier_suburb']),
            'entry_city' => tep_db_prepare_input($input_array['po_supplier_city']),
            'entry_postcode' => tep_db_prepare_input($input_array['po_supplier_postcode']),
            'entry_country_id' => tep_db_prepare_input($input_array['po_supplier_country_id'])
	);
		
	$cinfo_data_array = array(
            'customers_info_date_account_created' => 'now()',
            'customers_info_account_created_from' => (int)$this->login_site,
            'customers_info_account_created_ip' => tep_get_ip_address()
	);
		
	if (tep_not_null($input_array['po_supplier_remark'])) {
            $remarks_data_array = array(
                'date_remarks_added' => 'now()',
    		'remarks' => tep_db_prepare_input($input_array['po_supplier_remark']),
             	'remarks_added_by' => $_SESSION['login_id']);
	}
        
        if (ACCOUNT_STATE == 'true') {
            if ($po_supplier_zone_id > 0) {
                $address_data_array['entry_zone_id'] = tep_db_prepare_input($input_array['po_supplier_zone_id']);
                $address_data_array['entry_state'] = '';
            } else {
                $address_data_array['entry_zone_id'] = '0';
                $address_data_array['entry_state'] = tep_db_prepare_input($input_array['po_supplier_state']);
            }
        }
		
        // insert po supplier profile
        tep_db_perform(TABLE_CUSTOMERS, $customers_data_array);
        $po_supplier_id = tep_db_insert_id();

        // insert po supplier info
        $cinfo_data_array['customers_info_id'] = $po_supplier_id;
        tep_db_perform(TABLE_CUSTOMERS_INFO, $cinfo_data_array);

        // insert po supplier data
        $po_data_array['po_suppliers_id'] = $po_supplier_id;
        tep_db_perform(TABLE_PO_SUPPLIERS, $po_data_array);

        // insert po supplier address
        $address_data_array['customers_id'] = $po_supplier_id;
        tep_db_perform(TABLE_ADDRESS_BOOK, $address_data_array);
        $po_address_id = tep_db_insert_id();
		
        // insert po supplier remark
        if (tep_not_null($po_supplier_remark)) {
                $remarks_data_array['customers_id'] = $po_supplier_id;
                tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
        }

        // update address id in customers table
        $sql_data_array = array('customers_default_address_id' => $po_address_id);
        tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int)$po_supplier_id . "'");

        // insert disbursement payment info
        $pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $po_supplier_id);
        $subaction_res = $pm_object->insert_payment_account($input_array, $messageStack);

        return $po_supplier_id;
    }
	
    function get_po_supplier_payment_info($po_supplier_id) {
	$po_supplier_select_sql = "SELECT s.customers_id, s.customers_gender, s.customers_firstname, s.customers_lastname, 
				ps.po_supplier_code, ps.po_supplier_po_ref_year, ps.po_supplier_po_ref_counter, ps.po_payment_type, 
                                ps.po_payment_term, ps.po_days_pay_wsc, ps.po_supplier_status, ps.po_supplier_agreement_start_date, 
                                ps.po_supplier_agreement_end_date, ps.po_supplier_agreement_discount_terms, spab.store_payment_account_book_id,
                                ps.po_supplier_company_code
                            FROM " . TABLE_CUSTOMERS . " AS s 
                            LEFT JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
                                ON s.customers_id = ps.po_suppliers_id
                            LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
                                ON (spab.user_id = s.customers_id AND spab.user_role='customers' AND spab.store_payment_account_book_primary=1)
                            WHERE FIND_IN_SET( ".$this->login_site.", customers_login_sites) 
                            AND s.customers_id = '" . tep_db_input($po_supplier_id) . "'
                            ORDER BY s.customers_id";
	$po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
	$po_supplier_row = tep_db_fetch_array($po_supplier_result_sql);
	$sInfo = new objectInfo($po_supplier_row, false);
		
	return $sInfo;
    }
	
	function get_po_supplier_info($po_supplier_id) {
		$sInfo = array();
		
		$po_supplier_select_sql = "	SELECT s.customers_id, s.customers_firstname, s.customers_lastname, s.customers_email_address, 
										s.customers_country_dialing_code_id, s.customers_telephone, 
										ps.po_supplier_code, ps.po_supplier_po_ref_year, ps.po_supplier_po_ref_counter, ps.po_payment_type, ps.po_payment_term, ps.po_days_pay_wsc, 
										ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, 
										ab.entry_state, ab.entry_country_id, ab.entry_zone_id, c.address_format_id, ps.po_supplier_company_code 
								    FROM " . TABLE_CUSTOMERS . " AS s 
									INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
										ON (s.customers_id = ps.po_suppliers_id)
									INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab 
										ON (ab.address_book_id = s.customers_default_address_id)
									INNER JOIN " . TABLE_COUNTRIES . " AS c
										ON (c.countries_id = ab.entry_country_id)
									WHERE FIND_IN_SET( ".$this->login_site.", customers_login_sites) 
									AND s.customers_id = '" . tep_db_input($po_supplier_id) . "'
									ORDER BY s.customers_id";
		$po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
		if ($po_supplier_row = tep_db_fetch_array($po_supplier_result_sql)) {
			$sInfo = array(	'id' => $po_supplier_row['customers_id'],
							'code' => $po_supplier_row['po_supplier_code'],
							'po_ref_year' => $po_supplier_row['po_supplier_po_ref_year'],
							'po_ref_counter' => $po_supplier_row['po_supplier_po_ref_counter'],
							'name' => $po_supplier_row['customers_firstname'].' '.$po_supplier_row['customers_lastname'],
							'firstname' => $po_supplier_row['customers_firstname'],
							'lastname' => $po_supplier_row['customers_lastname'],
							'company' => '',
							'street_address' => $po_supplier_row['entry_street_address'],
							'suburb' => $po_supplier_row['entry_suburb'],
							'city' => $po_supplier_row['entry_city'],
							'postcode' => $po_supplier_row['entry_postcode'],
							'state' => $po_supplier_row['entry_state'],
							'country_id' => $po_supplier_row['entry_country_id'],
							'zone_id' => $po_supplier_row['entry_zone_id'],
							'format_id' => $po_supplier_row['address_format_id'],
							'customers_email_address' => $po_supplier_row['customers_email_address'],
							'customers_country_dialing_code_id' => $po_supplier_row['customers_country_dialing_code_id'],
							'customers_telephone' => $po_supplier_row['customers_telephone'],
							'payment_type' => $po_supplier_row['po_payment_type'],
							'payment_term' => $po_supplier_row['po_payment_term'],
							'po_days_pay_wsc' => $po_supplier_row['po_days_pay_wsc'],
                                                        'po_supplier_company_code' => $po_supplier_row['po_supplier_company_code']
						);
		}
		
		return $sInfo;
	}
	
	function get_active_po_supplier_list() {
		$po_supplier_list = array(array('id' => '', 'text' => 'Supplier List...'));
		
		$po_supplier_select_sql = "SELECT s.customers_id, s.customers_firstname, s.customers_lastname, ps.po_supplier_code 
								    FROM " . TABLE_CUSTOMERS . " AS s 
									INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
										ON (s.customers_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
									ORDER BY s.customers_lastname, s.customers_firstname
									";
		$po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
		while ($po_supplier_row = tep_db_fetch_array($po_supplier_result_sql)) {
			$po_supplier_list[] = array('id' => $po_supplier_row['customers_id'], 'text' => '[' . $po_supplier_row['po_supplier_code'] . ']-' . $po_supplier_row['customers_firstname'] . ' ' . $po_supplier_row['customers_lastname']);
		}
		
		return $po_supplier_list;
	}
	
    function get_po_supplier_consignment() {
        $po_supplier_list = array();

        $po_supplier_select_sql = " SELECT s.customers_id, s.customers_firstname, s.customers_lastname, ps.po_supplier_code, 
                                           ps.po_supplier_locked_by, ps.po_supplier_status, a.admin_email_address 
                                    FROM " . TABLE_CUSTOMERS . " AS s 
                                    INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
                                        ON (s.customers_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
                                        AND ps.po_payment_type = 'g'
                                    LEFT JOIN " . TABLE_ADMIN . " AS a
                                        ON ps.po_supplier_locked_by = a.admin_id
                                    ORDER BY s.customers_lastname, s.customers_firstname
        ";
        $po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
        while ($po_supplier_row = tep_db_fetch_array($po_supplier_result_sql)) {
            $po_supplier_list[$po_supplier_row['customers_id']] = $po_supplier_row;
        }

        return $po_supplier_list;
    }
    
    function get_po_supplier_consignment_currency($po_supplier_id) {
        $po_currency_list = array(array('id' => '', 'text' => 'Currency List...'));

        if (tep_not_null($po_supplier_id)) {
            $po_currency_select_sql = " SELECT c.code, c.title, c.symbol_left, c.symbol_right 
                                        FROM " . TABLE_CURRENCIES . " AS c 
                                        LEFT JOIN " . TABLE_PURCHASE_ORDERS . " AS po
                                            ON (po.supplier_id = '".tep_db_input($po_supplier_id)."')
                                        INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
                                            ON (po.store_payment_account_book_id = spab.store_payment_account_book_id)
                                        INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                            ON (pm.payment_methods_id = spab.payment_methods_id)
                                        INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
                                            ON (po.supplier_id = ps.po_suppliers_id AND ps.po_supplier_status = '1')
                                        WHERE c.currencies_id = pm.payment_methods_send_currency
                                        GROUP BY c.code
                                        ORDER BY c.title
            ";
            $po_currency_result_sql = tep_db_query($po_currency_select_sql);
            while ($po_currency_row = tep_db_fetch_array($po_currency_result_sql)) {
                $po_currency_list[] = array(
                    'id' => $po_currency_row['code'], 
                    'text' => $po_currency_row['title'] . " (" . $po_currency_row['symbol_left'] . $po_currency_row['symbol_right'] . ")");
            }
        }

        return $po_currency_list;
    }
	
	function get_po_supplier_lock_status($po_supplier_id, $locked_by_id) {
		$supplier_lock = array();
		
		$supplier_locked_select_sql = "	SELECT po_suppliers_id, po_supplier_locked_by 
										FROM " . TABLE_PO_SUPPLIERS . " 
										WHERE po_suppliers_id = '".tep_db_input($po_supplier_id)."'
										AND (po_supplier_locked_by IS NULL OR po_supplier_locked_by = '".$locked_by_id."')";
		$supplier_locked_result_sql = tep_db_query($supplier_locked_select_sql);
		if ($supplier_locked_row = tep_db_fetch_array($supplier_locked_result_sql)) {
			$supplier_lock = $supplier_locked_row;
		}
		
		return $supplier_lock;
	}
        
        function get_po_supplier_lock_validation($po_supplier_id, $locked_by_id) {
            $supplier_is_locked = '';
            $supplier_locked_select_sql = "SELECT po_suppliers_id, po_supplier_locked_by 
                                           FROM " . TABLE_PO_SUPPLIERS . " 
                                           WHERE po_suppliers_id = '".tep_db_input($po_supplier_id)."'";
            $supplier_locked_result_sql = tep_db_query($supplier_locked_select_sql);
            if ($supplier_locked_row = tep_db_fetch_array($supplier_locked_result_sql)) {
                $supplier_is_locked = $supplier_locked_row['po_supplier_locked_by'];
            }
            
            return $supplier_is_locked;
        }
	
	function lock_po_supplier($po_supplier_id, $locked_by_id) {
		$lock_supplier_update_sql = "UPDATE " . TABLE_PO_SUPPLIERS . " SET po_supplier_locked_by = '" . tep_db_input($locked_by_id) . "', po_supplier_locked_from_ip='" . tep_get_ip_address() . "', po_supplier_locked_datetime=now() WHERE po_suppliers_id = '" . tep_db_input($po_supplier_id) . "'";
		tep_db_query($lock_supplier_update_sql);
		
		return true;
	}
	
	function unlock_po_supplier($po_supplier_id) {
		$unlock_supplier_update_sql = "UPDATE " . TABLE_PO_SUPPLIERS . " SET po_supplier_locked_by = NULL, po_supplier_locked_from_ip = NULL, po_supplier_locked_datetime = NULL WHERE po_suppliers_id = '" . tep_db_input($po_supplier_id) . "'";
		tep_db_query($unlock_supplier_update_sql);
		
		return true;
	}
	
	function get_po_supplier_with_wsc() {
		$po_supplier_list = array(array('id' => '', 'text' => 'Supplier List...'));
		$po_supp_id = array();
		
		$po_supplier_select_sql = "SELECT s.customers_id, s.customers_firstname, s.customers_lastname, sab.store_account_po_wsc 
								    FROM " . TABLE_CUSTOMERS . " AS s 
									INNER JOIN " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
										ON (s.customers_id = sab.user_id AND sab.user_role='customers' AND sab.store_account_po_wsc > 0) 
									INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
										ON (s.customers_id = ps.po_suppliers_id 
											AND ps.po_supplier_status='1'
											AND (ps.po_supplier_locked_by IS NULL OR ps.po_supplier_locked_by='".$_SESSION['login_id']."'))
									ORDER BY s.customers_lastname, s.customers_firstname
									";
		$po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
		while ($po_supplier_row = tep_db_fetch_array($po_supplier_result_sql)) {
			if (round($po_supplier_row['store_account_po_wsc'], 2) > 0 && !in_array($po_supplier_row['customers_id'], $po_supp_id)) {
				$po_supplier_list[] = array('id' => $po_supplier_row['customers_id'], 'text' => $po_supplier_row['customers_firstname'] . ' ' . $po_supplier_row['customers_lastname']);
				$po_supp_id[] = $po_supplier_row['customers_id'];
			}
		}
		
		return $po_supplier_list;
	}
	
	function get_po_supplier_wsc_currency($po_supplier_id) {
		$po_currency_list = array(array('id' => '', 'text' => 'Currency List...'));
		
		if (tep_not_null($po_supplier_id)) {
			$po_currency_select_sql = "SELECT c.code, c.title, c.symbol_left, c.symbol_right 
									    FROM " . TABLE_CURRENCIES . " AS c 
										INNER JOIN " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
											ON (c.code = sab.store_account_balance_currency AND sab.store_account_po_wsc > 0 AND sab.user_role='customers' AND sab.user_id = '".tep_db_input($po_supplier_id)."') 
										INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
											ON (sab.user_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
										ORDER BY c.title
										";
			$po_currency_result_sql = tep_db_query($po_currency_select_sql);
			while ($po_currency_row = tep_db_fetch_array($po_currency_result_sql)) {
				$po_currency_list[] = array('id' => $po_currency_row['code'], 'text' => $po_currency_row['title'] . " (" . $po_currency_row['symbol_left'] . $po_currency_row['symbol_right'] . ")");
			}
		}
		
		return $po_currency_list;
	}
	
	function get_po_supplier_credit_note_by_currency($po_supplier_id, $po_currency) {
		$po_credit_note = array('amount' => '0.00', 'currency' => '');
		
		if (tep_not_null($po_supplier_id)) {
			$po_creditnote_select_sql = "SELECT sab.store_account_credit_note_amount, sab.store_account_balance_currency 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
										WHERE sab.store_account_balance_currency = '".tep_db_input($po_currency)."' 
										AND sab.store_account_credit_note_amount > 0 
										AND sab.user_role='customers' 
										AND sab.user_id = '".tep_db_input($po_supplier_id)."'";
			$po_creditnote_result_sql = tep_db_query($po_creditnote_select_sql);
			if ($po_creditnote_row = tep_db_fetch_array($po_creditnote_result_sql)) {
				$po_credit_note['amount'] = $po_creditnote_row['store_account_credit_note_amount'];
				$po_credit_note['currency'] = $po_creditnote_row['store_account_balance_currency'];
			}
		}
		
		return $po_credit_note;
	}
	
	function get_po_supplier_disburse_method($po_supplier_id, $curr_code) {
		$po_pm_list = array(array('id' => '', 'text' => 'Disbursement List...'));
		
		if (tep_not_null($po_supplier_id) && tep_not_null($curr_code)) {
			$po_pm_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_available_sites 
									FROM " . TABLE_PAYMENT_METHODS . " AS pm 
									INNER JOIN " . TABLE_CURRENCIES . " AS c 
										ON (pm.payment_methods_send_currency = c.currencies_id AND c.code = '" . tep_db_input($curr_code) . "') 
									INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
										ON (pm.payment_methods_id = spab.payment_methods_id AND spab.user_role='customers' AND spab.user_id = '".tep_db_input($po_supplier_id)."') 
									INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
										ON (spab.user_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
									WHERE pm.payment_methods_send_status = '1'
									ORDER BY pm.payment_methods_send_mode_name
									";
			$po_pm_result_sql = tep_db_query($po_pm_select_sql);
			while ($po_pm_row = tep_db_fetch_array($po_pm_result_sql)) {
				if (strstr($po_pm_row['payment_methods_send_available_sites'], '3') !== false) {
					$po_pm_list[] = array('id' => $po_pm_row['payment_methods_id'], 'text' => $po_pm_row['payment_methods_send_mode_name']);
				}
			}
		}
		
		return $po_pm_list;
	}

	function get_po_supplier_disburse_method_withdrawal($po_supplier_id, $curr_code) {
		$pm_id_array = array();
		$po_pm_list = array(array('id' => '', 'text' => 'Disbursement List...'));
		
		if (tep_not_null($po_supplier_id) && tep_not_null($curr_code)) {
			$po_pm_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_available_sites, 
									spab.store_payment_account_book_id, spab.payment_methods_alias 
									FROM " . TABLE_PAYMENT_METHODS . " AS pm 
									INNER JOIN " . TABLE_CURRENCIES . " AS c 
										ON (pm.payment_methods_send_currency = c.currencies_id AND c.code = '" . tep_db_input($curr_code) . "') 
									INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
										ON (pm.payment_methods_id = spab.payment_methods_id AND spab.user_role='customers' AND spab.user_id = '".tep_db_input($po_supplier_id)."') 
									INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
										ON (spab.user_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
									WHERE pm.payment_methods_send_status = '1'
									ORDER BY pm.payment_methods_send_mode_name
									";
			$po_pm_result_sql = tep_db_query($po_pm_select_sql);
			while ($po_pm_row = tep_db_fetch_array($po_pm_result_sql)) {
				if (strstr($po_pm_row['payment_methods_send_available_sites'], '3') !== false) {
					$po_pm_list[] = array('id' => $po_pm_row['store_payment_account_book_id'], 'text' => $po_pm_row['payment_methods_alias']);
					$pm_id_array[] = $po_pm_row['store_payment_account_book_id'];
				}
			}

			// add in disbursement method NRSC
			$po_nrsc_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_available_sites,
							spab.store_payment_account_book_id, spab.payment_methods_alias, po.purchase_orders_paid_currency
							FROM " . TABLE_PURCHASE_ORDERS . " AS po 
							INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
								ON po.store_payment_account_book_id = spab.store_payment_account_book_id 
							INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
								ON spab.payment_methods_id = pm.payment_methods_id
							WHERE po.supplier_id = '".tep_db_input($po_supplier_id)."' 
								AND po.purchase_orders_billing_status = '1' 
								AND po.purchase_orders_paid_status <> '1' 
								AND po.purchase_orders_paid_currency = '" . tep_db_input($curr_code) . "'
							GROUP BY po.store_payment_account_book_id";
			$po_nrsc_result = tep_db_query($po_nrsc_sql);
			while ($po_nrsc_row = tep_db_fetch_array($po_nrsc_result)) {
				$gv_curr_code = '';
				// Check if SC used
				$acct_book_select_sql = "SELECT spab.payment_methods_id, pm.payment_methods_send_currency, pm.payment_methods_send_status
		                                 FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
		                                 INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
		                                    ON (pm.payment_methods_id = spab.payment_methods_id)
		                                 WHERE spab.store_payment_account_book_id = '" . tep_db_input($po_nrsc_row['store_payment_account_book_id']) . "'
		                                 AND spab.user_id = '" . tep_db_input($po_supplier_id) . "'
		                                 AND spab.user_role = 'customers'";
		        $acct_book_result_sql = tep_db_query($acct_book_select_sql);

		        if ($acct_book_row = tep_db_fetch_array($acct_book_result_sql)) {
		        	if ($acct_book_row['payment_methods_send_currency'] < 1) {
		                $gv_curr_code = $po_nrsc_row['purchase_orders_paid_currency'];
		        	}
		        }

		        // Check if SC currncies = withdrawal currencies
		        if ($gv_curr_code === $curr_code) {
		        	if (!in_array($po_nrsc_row['store_payment_account_book_id'], $pm_id_array)) {
						$po_pm_list[] = array('id' => $po_nrsc_row['store_payment_account_book_id'], 'text' => $po_nrsc_row['payment_methods_alias']);
					}
		        }
			}
		}
		
		return $po_pm_list;
	}
	
	function get_po_supplier_available_amount($po_supplier_id, $curr_code) {
		$wsc_amt = 0;
		
		if (tep_not_null($po_supplier_id) && tep_not_null($curr_code)) {
			$wsc_select_sql = "SELECT sab.store_account_po_wsc 
								FROM " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
								INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
									ON (sab.user_id = ps.po_suppliers_id AND ps.po_supplier_status='1')
								WHERE sab.store_account_balance_currency = '" . tep_db_input($curr_code) . "'
								AND sab.user_role='customers' 
								AND sab.user_id = '" . tep_db_input($po_supplier_id) . "'";
			$wsc_result_sql = tep_db_query($wsc_select_sql);
			if ($wsc_row = tep_db_fetch_array($wsc_result_sql)) {
				$wsc_amt = $wsc_row['store_account_po_wsc'];
			}
		}
		
		return ($wsc_amt > 0 ? $wsc_amt : 0);
	}
	
	function get_po_supplier_pay_for_po($po_supplier_id, $curr_code, $pm_id) {
		global $currencies;
		$po_list = array();
		
		if (tep_not_null($po_supplier_id) && tep_not_null($pm_id)) {
			$po_select_sql = "SELECT po.purchase_orders_id, po.purchase_orders_ref_id, po.confirmed_currency_value, po.payment_type, po.payment_term, 
								po.purchase_orders_paid_status, po.purchase_orders_paid_amount, po.purchase_orders_type, spab.payment_methods_id,
								po.purchase_orders_bankcharges_included, po.purchase_orders_bankcharges_refunded, spab.store_payment_account_book_id,
								po.purchase_orders_gst_currency, po.purchase_orders_gst_value, po.purchase_orders_gst_amount, 
								po.purchase_orders_status, po.purchase_orders_paid_currency
								FROM " . TABLE_PURCHASE_ORDERS . " AS po 
								INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
									ON (po.store_payment_account_book_id = spab.store_payment_account_book_id 
										AND spab.user_role = 'customers' 
										AND spab.user_id = po.supplier_id
										AND spab.store_payment_account_book_id = '" . tep_db_input($pm_id) . "') 
								WHERE po.supplier_id = '" . tep_db_input($po_supplier_id) . "'
								AND po.purchase_orders_billing_status = '1'
								AND po.purchase_orders_paid_status <> '1'
								AND po.purchase_orders_paid_currency = '" . tep_db_input($curr_code) . "'
								ORDER BY po.payment_type, po.purchase_orders_id
								";
			$po_result_sql = tep_db_query($po_select_sql);
			while ($po_row = tep_db_fetch_array($po_result_sql)) {
				$partial_paid = 0;
				if ($po_row['purchase_orders_paid_status'] == '2') {
					$partial_paid = $po_row['purchase_orders_paid_amount'];
				}
				
				$po_amount = 0;
                $cb_amount = 0;
                $dn_amount = 0;
                $adjustment_amount_plus = 0;
                $adjustment_amount_minus = 0;
				$payment_type_text = '';

				// Get Charge Back & Debit Note value
				$raw_charge_back = 0;
				$raw_debit_note = 0;
                $po_total_select_sql = "SELECT value, class FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='" . tep_db_input($po_row['purchase_orders_id']) . "'";
                $po_total_result_sql = tep_db_query($po_total_select_sql);
                while ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
                    switch ($po_total_row['class']) {
                        case 'po_charge_back':
                            $raw_charge_back = $po_total_row['value'];
                            $cb_amount = $po_total_row['value'] * $po_row['confirmed_currency_value'];
                            break;
                        case 'po_debit_note':
                            $raw_debit_note = $po_total_row['value'];
                            $dn_amount = $po_total_row['value'] * $po_row['confirmed_currency_value'];
                            break;
                        case 'po_adjustment_plus':
                            $adjustment_amount_plus = $po_total_row['value'] * $po_row['confirmed_currency_value'];
                            break;
                        case 'po_adjustment_minus':
                            $adjustment_amount_minus = $po_total_row['value'] * $po_row['confirmed_currency_value'];
                            break;
                    }
                }

				if ($po_row['payment_type'] == 'c' || $po_row['payment_type'] == 'd' || $po_row['purchase_orders_type'] == 2 || $po_row['purchase_orders_type'] == 3) {

					$payment_type_text = ' ['.TEXT_SUPPLIER_PRE_PAYMENT.']';
                    if ($po_row['payment_type'] == 'd') {
                        $payment_type_text = ' ['.TEXT_SUPPLIER_DTU_PAYMENT.']';
                    } else if ($po_row['payment_type'] == 'g') {
                        $payment_type_text = ' ['.TEXT_SUPPLIER_CONSIGNMENT.']';
                    }

                    if ($po_row['purchase_orders_type'] == 2) {
                    	$payment_type_text = ' ['.TEXT_SUPPLIER_API_PAYMENT.']';
                    } elseif ($po_row['purchase_orders_type'] == 3) {
                    	$payment_type_text = ' ['.TEXT_SUPPLIER_CONSIGNMENT_PAYMENT.']';
                    }

					$has_prod_delivered = false;
					$raw_po_amount = 0;
					
					if ($po_row['purchase_orders_status'] == '3') {	// when PO is completed, get delivered amount instead
						$po_products_select_sql = "SELECT purchase_orders_products_id, products_good_delivered_quantity, products_good_delivered_price 
													FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . "
													WHERE purchase_orders_id = '" . tep_db_input($po_row['purchase_orders_id']) . "'";
						$po_products_result_sql = tep_db_query($po_products_select_sql);

						while ($po_products_row = tep_db_fetch_array($po_products_result_sql)) {
							if ($po_products_row['products_good_delivered_quantity'] > 0) {
								$has_prod_delivered = true;
								$po_amount += $po_products_row['products_good_delivered_price'];
							}
						}
					}
					
					if ($has_prod_delivered == true) {
						$raw_po_amount = $po_amount;
						$po_amount = $po_amount * $po_row['confirmed_currency_value'];
					} else {
						$po_total_select_sql = "SELECT value FROM " . TABLE_PURCHASE_ORDERS_TOTAL . " WHERE purchase_orders_id='".tep_db_input($po_row['purchase_orders_id'])."' AND class='po_subtotal'";
						$po_total_result_sql = tep_db_query($po_total_select_sql);
						if ($po_total_row = tep_db_fetch_array($po_total_result_sql)) {
							$raw_po_amount = $po_total_row['value'];
							$po_amount = $po_total_row['value'] * $po_row['confirmed_currency_value'];		
                            // take subtotal amount cause need to deduct credit note amount if not required full amount
						}
					}
                    
                    // deduct charge back
                    if ($cb_amount > 0) {
                        $po_amount = $po_amount - $cb_amount;
                        $raw_po_amount = $raw_po_amount - $raw_charge_back;
                    }
					
					// add GST amount in paid currency
					if ($po_row['purchase_orders_gst_value'] > 0) {
						$po_amount = $po_amount + (($raw_po_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $po_row['confirmed_currency_value']);
					}
				} else if ($po_row['payment_type'] == 't' || $po_row['payment_type'] == 'g') {
					if ($po_row['payment_type'] == 't') {
						$payment_type_text = ' ['.$po_row['payment_term'].' '.TEXT_SUPPLIER_DAY_TERM.']';
					} else if ($po_row['payment_type'] == 'g') {
						$payment_type_text = ' ['.TEXT_SUPPLIER_CONSIGNMENT.']';
					}
					$po_amount_select_sql = "SELECT products_good_delivered_price, purchase_orders_products_id 
												FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " 
												WHERE purchase_orders_id = '" . $po_row['purchase_orders_id'] . "'";
					$po_amount_result_sql = tep_db_query($po_amount_select_sql);
					while ($po_amount_row = tep_db_fetch_array($po_amount_result_sql)) {
						$po_amount += $po_amount_row['products_good_delivered_price'];
					}
					$raw_po_amount = $po_amount;
					$po_amount = $po_amount * $po_row['confirmed_currency_value'];
					
					// add GST amount in paid currency
					if ($po_row['purchase_orders_gst_value'] > 0) {
						$po_amount = $po_amount + (($raw_po_amount * ($po_row['purchase_orders_gst_value'] / 100)) * $po_row['confirmed_currency_value']);
					}
				}
                                
                // deduct debit note
                if ($dn_amount > 0) {
                    $po_amount = $po_amount - $dn_amount;
                }
				
				// add bank charges, if any
				if ($po_row['purchase_orders_bankcharges_included'] > 0 || $po_row['purchase_orders_bankcharges_refunded'] > 0) {
					$po_amount = $po_amount + $po_row['purchase_orders_bankcharges_included'] - $po_row['purchase_orders_bankcharges_refunded'];
				}
                                
                // Adjustment +
                if ($adjustment_amount_plus > 0) {
                    $po_amount = $po_amount + $adjustment_amount_plus;
                }
                
                //Adjustment -
                if ($adjustment_amount_minus > 0) {
                    $po_amount = $po_amount - $adjustment_amount_minus;
                }

                $po_amount_payable = $po_amount - $partial_paid;
				
				$po_list[] = array('id' => $po_row['purchase_orders_id'], 'text' => $po_row['purchase_orders_ref_id'].$payment_type_text, 'amount' => ($po_amount_payable), 'payment_type' => $po_row['payment_type']);
			}
		}
		
		return $po_list;
	}
	
	function _get_general_reserve_amount($user_id) {
		$cust_reserved_amt = 0;
		
		$reserve_amt_select_sql = "	SELECT customers_reserve_amount 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '".tep_db_input($user_id)."' ";
		$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
		if ($reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql)) {
			$cust_reserved_amt = $reserve_amt_row['customers_reserve_amount'];
		}
		
		return $cust_reserved_amt;
	}
	
	function _set_po_supplier_po_wsc_balance($user_id, $user_role, $credit_currency, $update_info) {
		/*******************************************************************
			operator = +, -, and =
		*******************************************************************/
		$sql_update_array = array();
		
		// Generate the update sql
		for ($update_cnt=0; $update_cnt < count($update_info); $update_cnt++) {
			if ($update_info[$update_cnt]['field_name'] == 'store_account_po_wsc') {
				$update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
				switch($update_info[$update_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
		if (count($sql_update_array)) {
			$sql_update_array[] = ' store_account_last_modified = now() ';
			
			$update_sql_str = " SET " . implode(', ', $sql_update_array);
			
	    	/*************************************************************************
			 	Lock the TABLE_STORE_ACCOUNT_BALANCE
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");
			
	    	$store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE .
	    										$update_sql_str . "
												WHERE user_id = '" . tep_db_input($user_id) . "'
													AND user_role = '" . tep_db_input($user_role) . "'
													AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			tep_db_query($store_acc_balance_update_sql);
			
			$new_balance_select_sql = "	SELECT store_account_po_wsc
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '" . tep_db_input($user_id) . "'
											AND user_role = '" . tep_db_input($user_role) . "'
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			$new_balance_result_sql = tep_db_query($new_balance_select_sql);
			$new_balance_row = tep_db_fetch_array($new_balance_result_sql);
			
			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
			********************************************************************/
			
			return $new_balance_row['store_account_po_wsc'];
		}
		
		return false;
	}
	
	function alloc_po_payment_part($input_array, &$messageStack) {
            global $currencies;

            include_once(DIR_WS_CLASSES . 'dtu_payment.php');
            $edit_dtu_obj = new dtu_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
            
            include_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
            $edit_api_obj = new api_replenish_payment($_SESSION['login_id'], $_SESSION['login_email_address']);

	        include_once(DIR_WS_CLASSES . 'consignment_payment.php');
	        $edit_cdk_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);

            $withdraw_fees = 0;
            $amount_bucket = array();
            $pay_amount_batch = array();
		
            if (isset($input_array['confirm_po_payment_supplier']) && !isset($input_array['po_payment_supplier'])) { $input_array['po_payment_supplier'] = $input_array['confirm_po_payment_supplier']; }
            if (isset($input_array['confirm_po_payment_currency']) && !isset($input_array['po_payment_currency'])) { $input_array['po_payment_currency'] = $input_array['confirm_po_payment_currency']; }
            if (isset($input_array['confirm_po_payment_method']) && !isset($input_array['po_payment_method'])) { $input_array['po_payment_method'] = $input_array['confirm_po_payment_method']; }
            if (isset($input_array['confirm_pay_po_batch']) && !isset($input_array['pay_po_batch'])) { $input_array['pay_po_batch'] = $input_array['confirm_pay_po_batch']; }
            if (isset($input_array['confirm_withdraw_total']) && !isset($input_array['withdraw_total'])) { $input_array['withdraw_total'] = $input_array['confirm_withdraw_total']; }
            if (isset($input_array['pay_po_str_list'])) { $input_array['pay_po_batch'] = explode(',', $input_array['pay_po_str_list']); }
		
            if ((isset($input_array['po_payment_supplier']) && tep_not_null($input_array['po_payment_supplier'])) &&
                (isset($input_array['po_payment_currency']) && tep_not_null($input_array['po_payment_currency'])) &&
                (isset($input_array['po_payment_method']) && tep_not_null($input_array['po_payment_method'])) &&
                (isset($input_array['pay_po_batch']) && tep_not_null($input_array['pay_po_batch']))) {

            	// Changes to the data confirm_po_payment_method = store_payment_account_book_id from the view
            	// Changes to the data po_payment_method = store_payment_account_book_id from the view
            	$po_store_payment_account_book_id = $input_array['po_payment_method'];

                $user_credit_info_select_sql = "SELECT store_account_po_wsc 
                                                FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
                                                WHERE user_id = '".tep_db_input($input_array['po_payment_supplier'])."' 
                                                    AND user_role = 'customers' 
                                                    AND store_account_balance_currency = '".tep_db_input($input_array['po_payment_currency'])."'";
                $user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);
                if ($user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
                    $available_balance = $user_credit_info_row['store_account_po_wsc'];
                    if ($available_balance < 0) { $available_balance = 0; }

                    // check if any incomplete mandatory data
                    $mandatory_data_completed_flag = true;
                    $check_incomplete_field_select_sql = "  SELECT pmf.payment_methods_fields_id, spabd.payment_methods_fields_value, spab.payment_methods_id 
                                                            FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
                                                            INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
                                                                ON (pmf.payment_methods_id = spab.payment_methods_id
                                                                    AND spab.user_id = '".tep_db_input($input_array['po_payment_supplier'])."'
                                                                    AND spab.user_role = 'customers')
                                                            LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd 
                                                                ON (spabd.store_payment_account_book_id = spab.store_payment_account_book_id
                                                                    AND spabd.payment_methods_fields_id = pmf.payment_methods_fields_id) 
                                                            WHERE spab.store_payment_account_book_id = '".tep_db_input($po_store_payment_account_book_id)."'
                                                                AND pmf.payment_methods_fields_required = '1'
                                                                AND pmf.payment_methods_fields_status = '1'";
                    $check_incomplete_field_result_sql = tep_db_query($check_incomplete_field_select_sql);
                    while ($check_incomplete_field_row = tep_db_fetch_array($check_incomplete_field_result_sql)) {
                        if (!tep_not_null($check_incomplete_field_row['payment_methods_fields_value'])) {
                            $mandatory_data_completed_flag = false;
                        } else {
                        	$po_payent_methods_id = $check_incomplete_field_row['payment_methods_id'];
                        }
                    }

                    if (!$mandatory_data_completed_flag) {
                        $pay_amount_batch['error'] = ERROR_PO_PAYMENT_INCOMPLETE_FIELD_INFO;
                    } else {
                        if ($available_balance > 0 && $input_array['withdraw_total'] <= $available_balance) {
                            if ($input_array['withdraw_total'] > 0) {
                                $payment_fee_select_sql = " SELECT * 
                                                            FROM " . TABLE_PAYMENT_FEES . "	
                                                            WHERE payment_methods_id = '".tep_db_input($po_payent_methods_id)."' 
                                                            AND payment_methods_mode = 'SEND'";
                                $payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
                                $payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);

                                $converted_max_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_max'], $input_array['po_payment_currency'], '', 'sell');
                                if ($converted_max_withdraw >= 0) {
                                    if ($converted_max_withdraw > 0 && $input_array['withdraw_total'] > $converted_max_withdraw) {	// Over limit
                                        // when over maximum limit, break the total withdrawal amount into small buckets
                                        $running_amount = 0;
                                        $po_list = array(); $amt_list = array(); 
                                        $amt_list_split = array(); $rm_poid_array = array(); $add_poid_array = array();

                                        foreach ($input_array['pay_po_batch'] as $po_id) {
                                            // GET Payment Po payment type
                                            $payment_type = $edit_dtu_obj->get_po_payment_type($po_id);

                                            if ($payment_type=='d') {
                                                $po_payable_amt = $edit_dtu_obj->get_po_total_payable_amount($po_id, false);
                                            } else {
                                                $po_payable_amt = edit_purchase_orders::get_po_total_payable_amount($po_id, false);
                                            }

                                            // Check API Replenish
                                            $po_type = $edit_api_obj->get_po_payment_type($po_id);
                                            
                                            if ($po_type == 2) {
                                                $po_payable_amt = $edit_api_obj->get_po_total_payable_amount($po_id, false);
                                            }
                                            
	                                        // Check CDK PO Consignment Payment
	                                        $po_cdk_type = $edit_cdk_obj->get_po_payment_type($po_id);

	                                        if ($po_cdk_type == 3) {
	                                            $po_payable_amt = $edit_cdk_obj->get_po_total_payable_amount($po_id, false);
	                                        }
                                        
                                            if (round($po_payable_amt,8) == round($converted_max_withdraw,8)) {
                                                $amount_bucket[] = array('trans_amt' => $converted_max_withdraw, 'po_list' => array($po_id), 'amt_list' => array($po_id => $converted_max_withdraw));
                                            } else if (round($po_payable_amt,8) > round($converted_max_withdraw,8)) {
                                                $running_amount += $po_payable_amt;
                                                $this_po_running_amount = $po_payable_amt;
                                                while ($this_po_running_amount > $converted_max_withdraw) {
                                                    $amount_bucket[] = array(
                                                        'trans_amt' => $converted_max_withdraw, 
                                                        'po_list' => array($po_id), 
                                                        'amt_list' => array($po_id => $converted_max_withdraw)
                                                    );
                                                    $running_amount -= $converted_max_withdraw;
                                                    $this_po_running_amount -= $converted_max_withdraw;
                                                }
                                                if ($running_amount > 0) {
                                                    $po_list[] = $po_id;
                                                    $amt_list[$po_id] = $this_po_running_amount;
                                                }
                                            } else {
                                            	$running_amount += $po_payable_amt;
                                                $this_po_running_amount = $po_payable_amt;
                                                if ($running_amount > 0) {
                                                    $po_list[] = $po_id;
                                                    $amt_list[$po_id] = $this_po_running_amount;
                                                }
                                                // if (($running_amount + $po_payable_amt) <= $converted_max_withdraw) {
                                                //     $running_amount += round($po_payable_amt,8);
                                                //     $po_list[] = $po_id;
                                                //     $amt_list[$po_id] = $po_payable_amt;
                                                // } else {
                                                //     $amount_bucket[] = array(
                                                //         'trans_amt' => $running_amount, 
                                                //         'po_list' => $po_list, 
                                                //         'amt_list' => $amt_list
                                                //     );
                                                //     $running_amount = $po_payable_amt;
                                                //     $po_list = array($po_id);
                                                //     $amt_list = array($po_id => $po_payable_amt);
                                                // }
                                            }
                                        }
                                        if ($running_amount > 0) {
                                        	while ($running_amount >= $converted_max_withdraw) {
                                        		$deduct_amt_list = $converted_max_withdraw;
                                        		foreach ($po_list as $po_id) {
                                        			$amt_list_total = $amt_list[$po_id];
                                        			$deduct_amt_list = $deduct_amt_list - $amt_list_total;
                                        			if ($amt_list_total > 0) {
                                        				$add_poid_array[] = $po_id;
                                        				if ($deduct_amt_list >= 0) {
	                                        				$amt_list_split[$po_id] = $amt_list_total;
	                                        				$amt_list[$po_id] = ($amt_list_total > 0) ? $amt_list[$po_id] - $amt_list_split[$po_id] : 0;
	                                        			} else {
	                                        				$amt_list_split[$po_id] = $amt_list_total - abs($deduct_amt_list);
	                                        				$amt_list[$po_id] = $amt_list[$po_id] - $amt_list_split[$po_id];
	                                        				break;
	                                        			}
                                        			}
                                        		}
                                        		$amount_bucket[] = array(
                                                    'trans_amt' => $converted_max_withdraw, 
                                                    'po_list' => $add_poid_array, 
                                                    'amt_list' => $amt_list_split
                                                );
                                                $running_amount -= $converted_max_withdraw;
                                                unset($add_poid_array);
                                        	}
                                        	if ($running_amount > 0) {
                                        		foreach ($po_list as $po_id) {
                                        			if ($amt_list[$po_id] > 0) {
	                                        			$amt_list_split[$po_id] = $amt_list[$po_id];
                                        			} else {
                                        				// Array to remove PO id from payment list
                                        				$rm_poid_array[] = $po_id;
                                        			}
                                        		}
                                        		foreach ($rm_poid_array as $rm_poid) {
                                        			// unset po_id from po_list
                                        			$indexCompleted = array_search($rm_poid, $po_list);
													unset($po_list[$indexCompleted]);

													// unset po_id from amt_list_split
													unset($amt_list_split[$rm_poid]);
                                        		}
                                        		$amount_bucket[] = array('trans_amt' => $running_amount, 'po_list' => $po_list, 'amt_list' => $amt_list_split);
                                        	}
                                        }
                                    } else {
                                        $amt_list = array();
                                        foreach ($input_array['pay_po_batch'] as $po_id) {
                                            // GET Payment Po payment type
                                            $payment_type = $edit_dtu_obj->get_po_payment_type($po_id);

                                            if ($payment_type=='d') {
                                                $po_payable_amt = $edit_dtu_obj->get_po_total_payable_amount($po_id, false);
                                            } else {
                                                $po_payable_amt = edit_purchase_orders::get_po_total_payable_amount($po_id, false);
                                            }
                                            
                                            // Check if API Replenish
                                            $po_type = $edit_api_obj->get_po_payment_type($po_id);
                                            
                                            if ($po_type == 2) {
                                                $po_payable_amt = $edit_api_obj->get_po_total_payable_amount($po_id, false);
                                            }
                                            
	                                        // Check CDK PO Consignment Payment
	                                        $po_cdk_type = $edit_cdk_obj->get_po_payment_type($po_id);

	                                        if ($po_cdk_type == 3) {
	                                            $po_payable_amt = $edit_cdk_obj->get_po_total_payable_amount($po_id, false);
	                                        }

	                                        $amt_list[$po_id] = $po_payable_amt;
                                        }
                                        $amount_bucket[] = array('trans_amt' => $input_array['withdraw_total'], 'po_list' => $input_array['pay_po_batch'], 'amt_list' => $amt_list);
                                    }
                                }

                                // for each withdrawal buckets, get its chargable payment fees
                                foreach ($amount_bucket as $this_pay_amount) {
                                    $fees_amount = 0;

                                    $po_ref_list = array();
                                    foreach ($this_pay_amount['po_list'] as $this_po_id) {
                                        $po_ref_list[] = edit_purchase_orders::get_po_ref_num_by_id($this_po_id);
                                    }
                                    $pay_amount_batch[] = array(
                                        'pay_amount' => round($this_pay_amount['trans_amt'],$currencies->currencies[$input_array['po_payment_currency']]['decimal_places']), 
                                        'fees' => round($fees_amount,$currencies->currencies[$input_array['po_payment_currency']]['decimal_places']), 
                                        'po_list' => $this_pay_amount['po_list'],
                                        'amt_list' => $this_pay_amount['amt_list'], 
                                        'po_ref_list' => $po_ref_list
                                    );
                                }
                            } else {
                                $messageStack->add_session(ERROR_PO_PAYMENT_NO_WITHDRAW_AMOUNT, 'error');
                            }
                        } else {
                            $messageStack->add_session(ERROR_PO_PAYMENT_NO_AVAILABLE_FUND, 'error');
                        }
                    }
                }
            }
		
            return $pay_amount_batch;
	}
	
	function insert_po_payment($input_array, &$messageStack) {
            global $currencies;
		
            $pay_amount_batch = $this->alloc_po_payment_part($input_array, $messageStack);
		
            if (!isset($pay_amount_batch['error'])) {
                $general_reserve_amount = $total_reserve_amount = $available_balance = 0;
                $po_supplier_id = $input_array['confirm_po_payment_supplier'];
                $po_pay_currency = $input_array['confirm_po_payment_currency'];
                // Changes to the data confirm_po_payment_method = store_payment_account_book_id from the view
                $po_store_payment_account_book_id = $input_array['confirm_po_payment_method'];
                // $po_pay_method_id = $input_array['confirm_po_payment_method'];

                // Get User Info
                $user_info_array = $this->get_po_supplier_info($po_supplier_id);

                // Get User Store Account Balance
                $user_credit_info_select_sql = "SELECT store_account_po_wsc 
                                                FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
                                                WHERE user_id = '".tep_db_input($po_supplier_id)."' 
                                                    AND user_role = 'customers' 
                                                    AND store_account_balance_currency = '".tep_db_input($po_pay_currency)."'";
                $user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);
                if ($user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
                    $available_balance = $user_credit_info_row['store_account_po_wsc'];
                    if ($available_balance <  0) { $available_balance = 0; }
                }

                // Check for NRSC PO System payment methods
                $po_nrsc_status = 0;
				$po_nrsc_sql = "SELECT pm.payment_methods_id, pm.payment_methods_types_id, pm.payment_methods_send_available_sites 
								FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
								INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
									ON spab.payment_methods_id=pm.payment_methods_id 
								WHERE spab.store_payment_account_book_id = '".tep_db_input($po_store_payment_account_book_id)."' ";
				$po_nrsc_result = tep_db_query($po_nrsc_sql);
				if ($po_nrsc_row = tep_db_fetch_array($po_nrsc_result)) {
					if ((int) $po_nrsc_row['payment_methods_types_id'] == 6 && (int) $po_nrsc_row['payment_methods_send_available_sites'] == 3) {
						$po_nrsc_status = 1;
						$po_currency_id = $currencies->get_id_by_code($po_pay_currency);
						$po_send_currency = "ON (c.currencies_id =  '" . tep_db_input($po_currency_id) . "' AND c.code = '" . tep_db_input($po_pay_currency) . "')";
					} else {
						$po_send_currency = "ON (pm.payment_methods_send_currency = c.currencies_id AND c.code = '" . tep_db_input($po_pay_currency) . "')";
					}
				}

                // Get Payment Method Info
                $payment_book_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, 
                                            pm.payment_methods_send_currency, spab.payment_methods_alias, 
                                            pm.payment_methods_types_id, spab.store_payment_account_book_id 
                                            FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
                                            INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
                                                ON spab.payment_methods_id=pm.payment_methods_id 
                                            INNER JOIN " . TABLE_CURRENCIES . " AS c 
                                                " . $po_send_currency . "
                                            WHERE spab.store_payment_account_book_id = '".tep_db_input($po_store_payment_account_book_id)."'
                                                AND spab.user_id = '".tep_db_input($po_supplier_id)."' 
                                                AND spab.user_role = 'customers' 
                                                AND pm.payment_methods_send_status = 1 
                                                AND pm.payment_methods_send_status_mode = 1";
                $payment_book_result_sql = tep_db_query($payment_book_select_sql);
                $payment_book_row = tep_db_fetch_array($payment_book_result_sql);

                // update po_pay_method_id
                $po_pay_method_id = $payment_book_row['payment_methods_id'];
                // Set checking for 
                $payment_methods_send_currency = ($po_nrsc_status > 0) ? $po_currency_id : $payment_book_row['payment_methods_send_currency'];

                // Get Payment Method Fields Info
                $payment_fields_array = array();
                $payment_fields_select_sql = "	SELECT pmf.payment_methods_fields_id, pmf.payment_methods_fields_title, 
                                                pmf.payment_methods_fields_sort_order, spabd.payment_methods_fields_value 
                                                FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
                                                LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd 
                                                    ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id 
                                                    AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_row['store_payment_account_book_id'])."')
                                                WHERE pmf.payment_methods_id = '" . tep_db_input($po_pay_method_id) . "' 
                                                    AND pmf.payment_methods_mode = 'SEND' 
                                                    AND pmf.payment_methods_fields_status = 1 
                                                ORDER BY pmf.payment_methods_fields_sort_order";
                $payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
                while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
                        $payment_fields_array[] = $payment_fields_row;
                }

                // Get Payment Fees Info
                $payment_fee_select_sql = " SELECT * 
                                            FROM " . TABLE_PAYMENT_FEES . "	
                                            WHERE payment_methods_id = '".tep_db_input($po_pay_method_id)."' 
                                            AND payment_methods_mode = 'SEND'";
                $payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
                $payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);

                foreach ($pay_amount_batch as $payment_batch) {
                    $fees_calculation_text = '';
                    $after_fees_amount = $payment_batch['pay_amount'] - $payment_batch['fees'];

                    /**********************************************************************
                            Capture the withdraw calculation and store it for reference
                    **********************************************************************/
                    $fees_calculation_text .= 	ENTRY_PO_PAYMENT_WITHDRAW_CURRENT_BALANCE . ' ' . $currencies->format($user_credit_info_row['store_account_po_wsc'], false, $po_pay_currency) . "\n" . 
                                                ENTRY_PO_PAYMENT_WITHDRAW_AVAILABLE_BALANCE . ' ' . $currencies->format($available_balance, false, $po_pay_currency) . "\n" .
                                                ENTRY_PO_PAYMENT_WITHDRAW_AMOUNT . ' ' . $currencies->format($payment_batch['pay_amount'], false, $po_pay_currency) . "\n" .
                                                ENTRY_PO_PAYMENT_WITHDRAW_PAYMENT_FOR . ' ' . implode(",", $payment_batch['po_ref_list']) . "\n" .
                                                ENTRY_PO_PAYMENT_WITHDRAW_PAYMENT_ACCOUNT . ' ' . $payment_book_row['payment_methods_alias'] . "\n\n";

                    $fees_calculation_text .= 	ENTRY_PO_PAYMENT_WITHDRAW_MIN_AMT . ' ' . ($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min'], true, $po_pay_currency) : TEXT_NO_WITHDRAW_LIMIT) . "\n" .
                                                ENTRY_PO_PAYMENT_WITHDRAW_MAX_AMT . ' ' . ($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max'], true, $po_pay_currency) : TEXT_NO_WITHDRAW_LIMIT) . "\n";

                    if ($payment_batch['fees'] > 0) {
                        $fees_calculation_text .= ENTRY_PO_PAYMENT_WITHDRAW_FEES . $payment_batch['fees'] . "\n";
                    }

                    $fees_calculation_text .= "\n" . ENTRY_PO_PAYMENT_WITHDRAW_FINAL_AMOUNT . $currencies->format($after_fees_amount, false, $po_pay_currency) . "\n\n";
                    /**********************************************************************
                            End of capture the withdraw calculation and store it for reference
                    **********************************************************************/

                    $payment_sql_data_array = array(	
                        'user_id' => tep_db_prepare_input($po_supplier_id),
                        'user_role' => 'customers',
                        'user_firstname' => $user_info_array['firstname'],
                        'user_lastname' => $user_info_array['lastname'],
                        'user_email_address' => '',
                        'user_country_international_dialing_code' => $user_info_array['customers_country_dialing_code_id'],
                        'user_telephone' => $user_info_array['customers_telephone'],
                        'user_mobile' => '',
                        'store_payments_date' => 'now()',
                        'store_payments_status' => '1',
                        'store_payments_request_currency' => tep_db_prepare_input($po_pay_currency),
                        'store_payments_request_amount' => (double)$payment_batch['pay_amount'],
                        'store_payments_fees' => (double)$payment_batch['fees'],
                        'store_payments_after_fees_amount' => $after_fees_amount,
                        'store_payments_paid_currency' => tep_db_prepare_input($po_pay_currency),
                        'store_payments_methods_id' => tep_db_prepare_input($po_pay_method_id),
                        'store_payments_methods_name' => $payment_book_row['payment_methods_send_mode_name'],
                        'store_payment_account_book_id' => $payment_book_row['store_payment_account_book_id'],
                        'user_payment_methods_alias' => $payment_book_row['payment_methods_alias']
                    );

                    if ($po_pay_currency != $currencies->get_code_by_id($payment_methods_send_currency)) {
                        $payment_sql_data_array['store_payments_paid_currency_value'] = $currencies->advance_currency_conversion_rate($po_pay_currency, $currencies->get_code_by_id($payment_methods_send_currency));
                    }

                    tep_db_perform(TABLE_STORE_PAYMENTS, $payment_sql_data_array);
                    $insert_payment_id = tep_db_insert_id();

                    if ($insert_payment_id > 0) {
                        // Update live credit balance
                        $update_info = array(array(	
                            'field_name'=> 'store_account_po_wsc',
                            'operator'=> '-', 
                            'value'=> $payment_batch['pay_amount']
                            )
                        );

                        $new_store_acc_balance = $this->_set_po_supplier_po_wsc_balance($po_supplier_id, 'customers', $po_pay_currency, $update_info);

                        // Insert account statement history
                        $account_balance_history_data_array = array(
                            'user_id' => tep_db_prepare_input($po_supplier_id),
                            'user_role' => 'customers',
                            'store_account_history_date' => 'now()',
                            'store_account_history_currency' => tep_db_prepare_input($po_pay_currency),
                            'store_account_history_account_type' => 'POWSC',
                            'store_account_history_debit_amount' => (double)$payment_batch['pay_amount'],
                            'store_account_history_credit_amount' => 'NULL',
                            'store_account_history_after_balance' => (double)$new_store_acc_balance,
                            'store_account_history_trans_type' => 'P',
                            'store_account_history_trans_id' => $insert_payment_id,
                            'store_account_history_added_by' => $this->identity_email,
                            'store_account_history_added_by_role' => 'admin'
                        );
                        tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);

                        // Insert payment details
                        foreach ($payment_fields_array as $payment_fields_row) {
                            $payment_details_sql_data_array = array(
                                'store_payments_id' => $insert_payment_id,
                                'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
                                'payment_methods_fields_title' => $payment_fields_row['payment_methods_fields_title'],
                                'payment_methods_fields_value' => $payment_fields_row['payment_methods_fields_value'],
                                'payment_methods_fields_sort_order' => $payment_fields_row['payment_methods_fields_sort_order']
                            );
                            tep_db_perform(TABLE_STORE_PAYMENTS_DETAILS, $payment_details_sql_data_array);

                            $fees_calculation_text .= $payment_fields_row['payment_methods_fields_title'] . ' ' . $payment_fields_row['payment_methods_fields_value'] . "\n";
                        }

                        // Insert payment history
                        $comments_contents_str = tep_db_prepare_input(sprintf(COMMENT_WITHDRAW_FUNDS_REQUESTED, $payment_book_row['payment_methods_alias'], $payment_book_row['payment_methods_send_mode_name'])) . "\n\n" . tep_db_prepare_input($fees_calculation_text);
                        $payment_history_sql_data_array = array(
                            'store_payments_id' => $insert_payment_id,
                            'store_payments_status' => '1',
                            'date_added' => 'now()',
                            'payee_notified' => '0',
                            'comments' => $comments_contents_str,
                            'changed_by' => $this->identity_email,
                            'changed_by_role' => 'admin'
                        );
                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);

                        // Update withdraw fees calculation history
                        $payment_update_array = array('store_payments_fees_calculation' => tep_db_prepare_input($fees_calculation_text));
                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_array, 'update', 'store_payments_id="'.tep_db_input($insert_payment_id).'"');

                        foreach ($payment_batch['po_list'] as $pay_po_id) {
                            $to_pay_amount = round($payment_batch['amt_list'][$pay_po_id], $currencies->currencies[$po_pay_currency]['decimal_places']);
                            $po_payment_data = array(
                                'store_payments_id' => $insert_payment_id,
                                'store_payments_reimburse_id' => $pay_po_id,
                                'store_payments_reimburse_table' => TABLE_PURCHASE_ORDERS,
                                'store_payments_reimburse_amount' => $to_pay_amount,
                                'store_payments_reimburse_currency' => tep_db_input($po_pay_currency)
                            );
                            tep_db_perform(TABLE_STORE_PAYMENTS_TRANSACTION_INFO, $po_payment_data);

                            $po_update_sql = "  update " . TABLE_PURCHASE_ORDERS . " 
                                                set purchase_orders_paid_amount = purchase_orders_paid_amount + ".$to_pay_amount.", 
                                                    purchase_orders_paid_currency = '".tep_db_input($po_pay_currency)."', 
                                                    purchase_orders_paid_status = '1',
                                                    last_modified = now()
                                                where purchase_orders_id = '".tep_db_input($pay_po_id)."'";
                            $po_update_result = tep_db_query($po_update_sql);

                            $sql_data_array = array(
                                'purchase_orders_id' => tep_db_input($pay_po_id), 
                                'date_added' => 'now()', 
                                'comments' => sprintf(COMMENT_PO_PAYMENT_WITHDRAW_SUCCESS, $insert_payment_id, $po_pay_currency.$to_pay_amount),
                                'comments_type' => '1',
                                'changed_by' => $_SESSION['login_email_address']);
                            tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                            tep_update_purchase_orders_status_counter($sql_data_array);
                        }

                        // check for consignment PO payment, auto complete the payment transaction
//                    if (isset($input_array['po_payment_type']) && $input_array['po_payment_type'] == 'g') {
//                        // auto set the store payment to processing
//                        $payment_update_sql_data_array = array(	
//                            'store_payments_status' => '2',
//                            'store_payments_last_modified' => 'now()' 
//                        );
//                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($insert_payment_id) . "'");
//
//                        // Insert payment history
//                        $payment_history_sql_data_array = array(
//                            'store_payments_id' => $insert_payment_id,
//                            'store_payments_status' => '2',
//                            'date_added' => 'now()',
//                            'payee_notified' => '0',
//                            'changed_by' => $_SESSION['login_email_address'],
//                            'changed_by_role' => 'admin'
//                        );
//                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
//
//                        // auto set the store payment to completed
//                        $payment_update_sql_data_array = array(	
//                            'store_payments_status' => '3',
//                            'store_payments_reference' => '',
//                            'store_payments_last_modified' => 'now()',
//                            'store_payments_paid_amount' => $after_fees_amount
//                        );
//                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($insert_payment_id) . "'");
//
//                        // Insert payment history
//                        $payment_history_sql_data_array = array(
//                            'store_payments_id' => $insert_payment_id,
//                            'store_payments_status' => '3',
//                            'date_added' => 'now()',
//                            'payee_notified' => '0',
//                            'comments' => 'Consignment Payment',
//                            'changed_by' => $_SESSION['login_email_address'],
//                            'changed_by_role' => 'admin'
//                        );
//                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
//                    }
                        }
                    }

                // unlocking the cdk supplier after payment disbursed
                $this->unlock_po_supplier($po_supplier_id);

                $messageStack->add_session(SUCCESS_PO_PAYMENT_WITHDRAW_SUCCESS, 'success');
            } else {
                // Withdraw failed
                $messageStack->add_session($pay_amount_batch['error'], 'error');
                $messageStack->add_session(ERROR_PO_PAYMENT_WITHDRAW_NOT_SUCCESS, 'error');
            }
	}
	
	function cancel_po_payment($store_payments_id, &$messageStack) {
		if (isset($store_payments_id) && tep_not_null($store_payments_id)) {
			$pay_trans_select_sql = "SELECT store_payments_id, store_payments_reimburse_id, store_payments_reimburse_table, 
										store_payments_reimburse_amount, store_payments_reimburse_currency 
                                    FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . "
                                    WHERE store_payments_id = '" . tep_db_input($store_payments_id) . "'";
			$pay_trans_result_sql = tep_db_query($pay_trans_select_sql);
			while ($pay_trans_row = tep_db_fetch_array($pay_trans_result_sql)) {
				
				$reimburse_table = $pay_trans_row['store_payments_reimburse_table'];
				if (isset($reimburse_table) && tep_not_null($reimburse_table) && $reimburse_table == TABLE_PURCHASE_ORDERS) {
					$po_select_sql = "SELECT purchase_orders_id, purchase_orders_paid_amount, purchase_orders_paid_currency 
										FROM " . TABLE_PURCHASE_ORDERS . "
										WHERE purchase_orders_id = '".tep_db_input($pay_trans_row['store_payments_reimburse_id'])."'";
					$po_result_sql = tep_db_query($po_select_sql);
					while ($po_row = tep_db_fetch_array($po_result_sql)) {
						$remain_amt = $po_row['purchase_orders_paid_amount'] - $pay_trans_row['store_payments_reimburse_amount'];
						if ($remain_amt < 0) { $remain_amt = 0; }
						
						$po_update_data = array('purchase_orders_paid_amount' => $remain_amt, 'purchase_orders_paid_status' => '0', 'last_modified' => 'now()');
						if ($remain_amt > 0) {
							$po_update_data['purchase_orders_paid_status'] = '2'; // PARTIAL PAYMENT
						}
						tep_db_perform(TABLE_PURCHASE_ORDERS, $po_update_data, 'update', 'purchase_orders_id="'.tep_db_input($po_row['purchase_orders_id']).'"');
						
						$sql_data_array = array(
							'purchase_orders_id' => tep_db_input($po_row['purchase_orders_id']), 
							'date_added' => 'now()', 
							'comments' => sprintf(COMMENT_PO_PAYMENT_WITHDRAW_CANCEL, $store_payments_id, $pay_trans_row['store_payments_reimburse_currency'].$pay_trans_row['store_payments_reimburse_amount']),
							'comments_type' => '1',
							'changed_by' => $_SESSION['login_email_address']);
						tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
						tep_update_purchase_orders_status_counter($sql_data_array);
					}
				}
			}
		} else {
			$messageStack->add_session(ERROR_PO_PAYMENT_MISSING_STORE_PAYMENT_ID, 'error');
		}
	}
	
	function calculate_payment_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
		global $currencies;
		
		$trans_fee_array = array('cost' => 0, 'percent' => 0);
		
		$payment_fee_select_sql = "	SELECT * 
									FROM " . TABLE_PAYMENT_FEES . "	
									WHERE payment_methods_id = '".tep_db_input($payment_method_id)."' 
										AND payment_methods_mode = 'SEND'";
		$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
		if (!tep_db_num_rows($payment_fee_result_sql)) {
			$payment_fee_select_sql = "	SELECT pf.* 
										FROM " . TABLE_PAYMENT_FEES . "	AS pf 
										INNER JOIN " . TABLE_PAYMENT_METHODS. " AS pm 
											ON pf.payment_methods_id = pm.payment_methods_parent_id
										WHERE pm.payment_methods_id = '".tep_db_input($payment_method_id)."' 
											AND pf.payment_methods_mode = 'SEND'";
			$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
		}
		$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
		
		if ($payment_fee_row['payment_fees_cost_value'] > 0) 	$trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];
		
		if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
			$percent_fees_not_in_range = false;
			
			$percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;
			
			if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
				$w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
				if ($percent_fees < $w_currency_min_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
					$percent_fees_not_in_range = true;
				}
			}
			
			if ($payment_fee_row['payment_fees_cost_percent_max'] > 0)	{
				$w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');
				
				if ($percent_fees > $w_currency_max_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
					$percent_fees_not_in_range = true;
				}
			}
			
			if ($percent_fees_not_in_range) {
				$trans_fee_array['cost'] += $percent_fees;
			} else {
				$trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
			}
		}
		
		return $trans_fee_array;
	}
	
	function check_disbursement_in_usage_in_po($supplier_id, $book_id) {
		$pending_payment_po_found = false;
		
		$po_select_sql = "SELECT purchase_orders_id 
							FROM " . TABLE_PURCHASE_ORDERS . " 
							WHERE store_payment_account_book_id = '".tep_db_input($book_id)."' 
							AND supplier_id = '".tep_db_input($supplier_id)."' 
							AND (((purchase_orders_status = 2 OR purchase_orders_status = 3) AND purchase_orders_paid_status <> 1) 
								OR (purchase_orders_status = 1))";
		$po_result_sql = tep_db_query($po_select_sql);
		if (tep_db_num_rows($po_result_sql) > 0) {
			$pending_payment_po_found = true;
		}
		
		return $pending_payment_po_found;
	}
	
	function get_po_supplier_stock_statistic($po_supplier_id) {
		global $currencies;
		
		$zero_amt = $currencies->format(0, false, DEFAULT_CURRENCY);
		$stat_data = array(
							'stock_pending_payment' => $zero_amt,
							'payment_pending_stock' => $zero_amt,
							'net_outstanding' => $zero_amt,
							'po_pending_payment' => $zero_amt,
							'supplier_credit' => $zero_amt
						);
		
		// (AMT A) get stock received, but pending payment, usually for X-day Term
		$stock_received_amt = 0;
		$stock_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_good_delivered_usd_price 
								FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
								INNER JOIN " . TABLE_PURCHASE_ORDERS . " AS po 
									ON (pod.purchase_orders_id = po.purchase_orders_id 
										AND (po.purchase_orders_status = 2 OR po.purchase_orders_status = 3) 
										AND po.purchase_orders_billing_status = 0 
										AND po.supplier_id = '" . tep_db_input($po_supplier_id) . "')
								WHERE pod.products_delivered_quantity > 0";
		$stock_result_sql = tep_db_query($stock_select_sql);
		if (tep_db_num_rows($stock_result_sql) > 0) {
			while ($stock_row = tep_db_fetch_array($stock_result_sql)) {
				$stock_received_amt += $stock_row['products_good_delivered_usd_price'];
			}
			
			$stat_data['stock_pending_payment'] = $currencies->format($stock_received_amt, false, DEFAULT_CURRENCY);
		}
		
		// (AMT B) get payment made, but pending stock receive, usually for pre-payment
		// since inclusive credit note amount, use products_delivered_quantity
		$paid_amt = 0;
		$paid_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_quantity, pod.products_usd_unit_price 
								FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
								INNER JOIN " . TABLE_PURCHASE_ORDERS . " AS po 
									ON (pod.purchase_orders_id = po.purchase_orders_id 
										AND (po.purchase_orders_status = 2 OR po.purchase_orders_status = 3) 
										AND po.purchase_orders_paid_status = 1 
										AND po.supplier_id = '" . tep_db_input($po_supplier_id) . "')
								WHERE pod.products_delivered_quantity = 0";
		$paid_result_sql = tep_db_query($paid_select_sql);
		if (tep_db_num_rows($paid_result_sql) > 0) {
			while ($paid_row = tep_db_fetch_array($paid_result_sql)) {
				$paid_amt += ($paid_row['products_quantity'] * $paid_row['products_usd_unit_price']);
			}
			
			$stat_data['payment_pending_stock'] = $currencies->format($paid_amt, false, DEFAULT_CURRENCY);
		}
		
		// get net outstanding difference between (AMT A) and (AMT B)
		$net_outstanding = round($stock_received_amt,2) - round($paid_amt,2);
		$stat_data['net_outstanding'] = $currencies->format($net_outstanding, false, DEFAULT_CURRENCY);
		
		// get total PO pending payment, after deduct credit note.
		$pending_pay_amt = 0;
		$pending_pay_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_quantity, pod.products_usd_unit_price, pod.products_good_delivered_quantity 
								FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
								INNER JOIN " . TABLE_PURCHASE_ORDERS . " AS po 
									ON (pod.purchase_orders_id = po.purchase_orders_id 
										AND (po.purchase_orders_status = 2 OR po.purchase_orders_status = 3) 
										AND po.purchase_orders_billing_status = 0 
										AND po.supplier_id = '" . tep_db_input($po_supplier_id) . "')
								";
		$pending_pay_result_sql = tep_db_query($pending_pay_select_sql);
		if (tep_db_num_rows($pending_pay_result_sql) > 0) {
			while ($pending_pay_row = tep_db_fetch_array($pending_pay_result_sql)) {
				$pending_pay_amt += (($pending_pay_row['products_quantity'] - $pending_pay_row['products_good_delivered_quantity']) * $pending_pay_row['products_usd_unit_price']);
			}
			
			$stat_data['po_pending_payment'] = $currencies->format($pending_pay_amt, false, DEFAULT_CURRENCY);
		}
		
		// get supplier credit note amount
		$credit_note_amt = 0;
		$credit_note_select_sql = "SELECT pod.purchase_orders_products_id, pod.products_credit_note_usd_price 
									FROM " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pod 
									INNER JOIN " . TABLE_PURCHASE_ORDERS . " AS po 
									ON (pod.purchase_orders_id = po.purchase_orders_id 
										AND (po.purchase_orders_status = 2 OR po.purchase_orders_status = 3) 
										AND po.supplier_id = '" . tep_db_input($po_supplier_id) . "')
									WHERE pod.products_credit_note_quantity > 0";
		$credit_note_result_sql = tep_db_query($credit_note_select_sql);
		if (tep_db_num_rows($credit_note_result_sql) > 0) {
			while ($credit_note_row = tep_db_fetch_array($credit_note_result_sql)) {
				$credit_note_amt += $credit_note_row['products_credit_note_usd_price'];
			}
			
			$stat_data['supplier_credit'] = $currencies->format($credit_note_amt, false, DEFAULT_CURRENCY);
		}
		
		return $stat_data;
	}
	
	function get_po_supplier_provision_payments() {
		global $currencies;
		
                include_once(DIR_WS_CLASSES . 'dtu_payment.php');
                $edit_dtu_obj = new dtu_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
                
                include_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
                $edit_api_obj = new api_replenish_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
                
        include_once(DIR_WS_CLASSES . 'consignment_payment.php');
        $edit_consignment_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
                
		$provision_array = array();
		$provision_total_array = array();
		
		$cron_select_sql = "SELECT cron_pending_credit_trans_id, cron_pending_credit_trans_completed_date, cron_pending_credit_mature_period 
							FROM " . TABLE_CRON_PENDING_CREDIT . "
							WHERE cron_pending_credit_trans_type = 'PO'";
		$cron_result_sql = tep_db_query($cron_select_sql);
		while ($cron_row = tep_db_fetch_array($cron_result_sql)) {
			$created_time = strtotime($cron_row['cron_pending_credit_trans_completed_date']);
			$matured_time = mktime(date('H',$created_time), date('i',$created_time)+$cron_row['cron_pending_credit_mature_period'], date('s',$created_time), date('n',$created_time), date('j',$created_time), date('Y',$created_time));
			$mature_year = date('Y', $matured_time);
			$week_of_year = date('W', $matured_time);
			
			$po_link_str = $cron_row['cron_pending_credit_trans_id'];
			$supp_link_str = '';
			$po_select_sql = "SELECT purchase_orders_id, purchase_orders_ref_id, purchase_orders_issue_date, supplier_id, 
                                            supplier_name, store_payment_account_book_id, payment_type, purchase_orders_type
                                            FROM " . TABLE_PURCHASE_ORDERS . "
                                            WHERE purchase_orders_id = '".$cron_row['cron_pending_credit_trans_id']."'";

			$po_result_sql = tep_db_query($po_select_sql);
			if ($po_row = tep_db_fetch_array($po_result_sql)) {
				
                            $acct_book_arr = edit_purchase_orders::get_supplier_account_book_info($po_row['supplier_id'], $po_row['store_payment_account_book_id']);
                if ($po_row['payment_type']=='d' && $po_row['purchase_orders_type']!=2 && $po_row['purchase_orders_type']!=3) {
                                $po_payable_amt = $edit_dtu_obj->get_po_total_payable_amount($po_row['purchase_orders_id'], false);
                    $po_link_str = '<a href="'.tep_href_link(FILENAME_DTU_PAYMENT, 'po_id='.$po_row['purchase_orders_id'].'&subaction=edit_dtu&selected_box=po', 'NONSSL').'" target="_blank">'.$po_row['purchase_orders_ref_id'].'</a>';
                            } else if ($po_row['purchase_orders_type']==2) {
                                $po_payable_amt = $edit_api_obj->get_po_total_payable_amount($po_row['purchase_orders_id'], false);
                    $po_link_str = '<a href="'.tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'po_id='.$po_row['purchase_orders_id'].'&subaction=edit_po&selected_box=po', 'NONSSL').'" target="_blank">'.$po_row['purchase_orders_ref_id'].'</a>';
                } else if ($po_row['purchase_orders_type']==3) {
                    $po_payable_amt = $edit_consignment_obj->get_po_total_payable_amount($po_row['purchase_orders_id'], false);
                    $po_link_str = '<a href="'.tep_href_link(FILENAME_CDK_PAYMENT, 'po_id='.$po_row['purchase_orders_id'].'&subaction=edit_po&selected_box=po', 'NONSSL').'" target="_blank">'.$po_row['purchase_orders_ref_id'].'</a>';
                            } else {
                                $po_payable_amt = edit_purchase_orders::get_po_total_payable_amount($po_row['purchase_orders_id'], false);
                    $po_link_str = '<a href="'.tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'po_id='.$po_row['purchase_orders_id'].'&subaction=edit_po&selected_box=po', 'NONSSL').'" target="_blank">'.$po_row['purchase_orders_ref_id'].'</a>';
                            }
				
                $supp_link_str = '<a href="'.tep_href_link(FILENAME_CUSTOMERS, 'cID='.$po_row['supplier_id'].'&action=edit&selected_box=customers', 'NONSSL').'" target="_blank">'.$po_row['supplier_name'].'</a>';

                            $provision_array[$mature_year][$week_of_year][$cron_row['cron_pending_credit_trans_id']] = array(
                                'po_ref' => $po_link_str,
                                'po_date' => $po_row['purchase_orders_issue_date'],
                                'supp_link' => $supp_link_str,
                                'matured_date' => date('Y-m-d H:i:s', $matured_time),
                                'pay_method' => $acct_book_arr['pm_name'],
                                'pay_currency' => $acct_book_arr['pm_currency'],
                                'pay_amount' => $currencies->format($po_payable_amt, false, $acct_book_arr['pm_currency'])
                            );
				if (isset($provision_total_array[$acct_book_arr['pm_currency']])) {
					$provision_total_array[$acct_book_arr['pm_currency']] += $po_payable_amt;
				} else {
					$provision_total_array[$acct_book_arr['pm_currency']] = $po_payable_amt;
				}
			}
		}
		
		echo '<table width="100%" cellpadding="2" cellspacing="2" border="0"><tbody>';
        echo '  <tr>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PO_REF_ID.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PO_DATE.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PAYMENT_PROVISION_DATE.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PO_SUPPLIER.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PAYMENT_METHOD.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PAYMENT_CURRENCY.'</td>
					<td class="ordersBoxHeading">'.TABLE_HEADING_PROVISION_PAYMENT_AMOUNT.'</td>
				</tr>';
		foreach ($provision_array as $year_no => $year_array) {
			foreach ($year_array as $week_no => $week_array) {
				//$week_dates = $this->returnWeekDateRange($year_no, $week_no+1);
				//echo '<tr><td colspan="6" class="main">Week '.$week_no.' of Year '.$year_no.' ('.$week_dates[1].' - '.$week_dates[2].')</td></tr>';
				
				foreach ($week_array as $po_id => $po_array) {
                    echo '  <tr>
								<td class="main">'.$po_array['po_ref'].'</td>
								<td class="main">'.date('Y-m-d', strtotime($po_array['po_date'])).'</td>
								<td class="main">'.date('Y-m-d', strtotime($po_array['matured_date'])).'</td>
								<td class="main">'.$po_array['supp_link'].'</td>
								<td class="main">'.$po_array['pay_method'].'</td>
								<td class="main">'.$po_array['pay_currency'].'</td>
								<td class="main">'.$po_array['pay_amount'].'</td>
							</tr>';
				}
			}
		}
		echo '<tr><td colspan="6" align="right" vlign="top" class="ordersBoxHeading">'.TABLE_FOOTER_PROVISION_PAYMENT_TOTAL.'</td><td class="ordersBoxHeading">';
		foreach ($provision_total_array as $curr => $total_amt) {
			echo $currencies->format($total_amt, false, $curr)."<br/>";
		}
		echo '</td></tr>';
		echo '</tbody></table>';
	}
	
	// Return the range of days spanning a given week
	// Author: jaaristizabal <<EMAIL>>
	function returnWeekDateRange($year, $week)
	{  
		// Determine the opening day of the year
		$year = date('Y',mktime(0, 0, 0, 1, 1, $year));
		// 0 for Sunday through 6 for Saturday
		$diaInicial = date('w',mktime(0, 0, 0, 1, 1, $year)); 
		// Establish baseline data for calculation
		$finSemana = 7-$diaInicial; 
		$diaAno = $diaAno+$finSemana; 
		
		$diaIni = date('Y-m-d',mktime(0, 0, 0, 1, $diaIni+1, $year));
		$diaFin = date('Y-m-d',mktime(0, 0, 0, 1, $finSemana, $year));
		
		// Make the cycle until desired week
		if($week<54)
		{
			for($sem=2;$sem<=$week;$sem++)
			{
				$diaIni = $diaAno + 1;
				$diaFin = $diaIni + 6;
				$diaAno = $diaFin;
				
				$diaIni = date('Y-m-d',mktime(0, 0, 0, 1, $diaIni, $year));
				$diaFin = date('Y-m-d',mktime(0, 0, 0, 1, $diaFin, $year));
			}
		}
		
		$fechas = array($year, $diaIni, $diaFin);
		return $fechas;
	} // End of funtion returnWeekDateRange
}
?>