<?php

class polling {
    var $identity, $identity_email;
	
	// class constructor
    function polling($identity, $identity_email) {
      	$this->identity = $identity;	// Admin user
      	$this->identity_email = $identity_email;	// Admin user
	}
	
	function show_poll_list($filename) {
	  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
	  	
	  	$poll_select_sql = "SELECT pq.polls_questions_id, pq.polls_questions, pq.polls_questions_start_date, pq.polls_questions_end_date, pq.polls_questions_status
							FROM " . TABLE_POLLS_QUESTIONS . " AS pq 
							WHERE 1
							ORDER BY pq.polls_questions_status DESC, pq.polls_questions_start_date desc";
		
		$show_records = MAX_DISPLAY_SEARCH_RESULTS;
		
		if ($show_records != "ALL") {
			$poll_split_object = new splitPageResults($_REQUEST['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $poll_select_sql, $poll_select_sql_numrows, true);
		}
		$poll_result_sql = tep_db_query($poll_select_sql);
		
		ob_start();
?>
			<table width="100%" border="0" cellspacing="2" cellpadding="2">
				<tr>
					<td>
						<table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
							<tr>
								<td width="12%" class="reportBoxHeading"><?=TABLE_HEADING_POLLING_ID?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_POLLING_QUESTION?></td>
								<td width="15%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_POLLING_START?></td>
								<td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_POLLING_END?></td>
							    <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_POLLING_STATUS?></td>
							    <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_POLLING_ACTION?></td>
							</tr>
<?
		$row_count = 0;
		while ($poll_row = tep_db_fetch_array($poll_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			if ((int)$poll_row['polls_questions_status'] == 1) {
				$status_str = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif') . '&nbsp;<a href="javascript:;" onClick="confirm_action(\'Are you sure to deactivate this poll?\', \''. tep_href_link($filename, 'action=poll_flag&flag=0&id=' . $poll_row['polls_questions_id']) .'\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif'). '</a>';
		    } else {
		    	$status_str = '<a href="javascript:;" onClick="confirm_action(\'Are you sure to activate this poll?\', \''. tep_href_link($filename, 'action=poll_flag&flag=1&id=' . $poll_row['polls_questions_id']) .'\')">'. tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif') . '</a>&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
		    }
		    
		    $action_str = '	<a href="'.tep_href_link($filename, 'action=edit_poll&id='.$poll_row["polls_questions_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>
							<a href="'.tep_href_link($filename, 'action=view_poll_stat&id='.$poll_row["polls_questions_id"]).'">'.tep_image(DIR_WS_ICONS."statistics.gif", "Poll Statistic", "", "", 'align="top"').'</a>
							<a href="javascript:void(confirm_delete(\'Poll Question - '.$poll_row['polls_questions_id'].'\', \'\', \''.tep_href_link($filename, 'action=delete_poll&id='.$poll_row["polls_questions_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a>';
			
	  		echo '			<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td valign="top" class="reportRecords" nowrap>'.$poll_row['polls_questions_id'].'</td>
							   	<td valign="top">'.$poll_row['polls_questions'].'</td>
								<td align="center" valign="top" class="reportRecords">'.$poll_row['polls_questions_start_date'].'</td>
								<td align="center" valign="top" class="reportRecords">'.$poll_row['polls_questions_end_date'].'</td>
								<td align="center" valign="top" class="reportRecords">'.$status_str.'</td>
								<td align="center" valign="top" class="reportRecords">'.$action_str.'</td>
							  </tr>';
	  		$row_count++;
	  	}
?>
						</table>
					</td>
				</tr>
				<tr>
        			<td>
        				<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($poll_result_sql) > 0 ? "1" : "0", tep_db_num_rows($poll_result_sql), tep_db_num_rows($poll_result_sql)) : $poll_split_object->display_count($poll_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'], $result_display_text)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $poll_split_object->display_links($poll_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
				<tr>
        			<td>[ <a href="<?=tep_href_link($filename, 'action=new_poll')?>"><?=LINK_ADD_POLL?></a> ]</td>
        		</tr>
			</table>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean() ;
		
		return $report_section_html;
	}
	
	function add_poll($filename) {
		$report_section_html = '';
		$product_type_array = tep_get_product_type();
		
		ob_start();
?>
		<table width="100%" border="0" cellspacing="2" cellpadding="2">
			<tr>
				<td>
					<?=tep_draw_form('add_poll_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=insert_poll', 'post', '')?>
					<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_START_DATE?></td>
							<td class="main">
								<?=tep_draw_input_field('start_date', '', 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.add_poll_form.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.add_poll_form.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_END_DATE?></td>
							<td class="main">
								<?=tep_draw_input_field('end_date', '', 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.add_poll_form.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.add_poll_form.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_QUESTION?></td>
							<td class="main"><?=tep_draw_textarea_field('question', 'soft', 70, 5, '', '')?></td>
						</tr>
						<tr>
							<td colspan="2">&nbsp;</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_OPTION?></td>
							<td class="main">
							<?	echo 	tep_draw_input_field('options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('options[]', '', 'size="72"');
							?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_SHOW_IN?></td>
							<td class="main">
							<?
								for ($p_cnt=0; $p_cnt < count($product_type_array); $p_cnt++) {
									echo tep_draw_checkbox_field('product_type[]', (string)$product_type_array[$p_cnt]['id'], false) . $product_type_array[$p_cnt]['text'] . '<br>';
								}
							?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_WHO_CAN_VOTE?></td>
							<td class="main">
								<? echo tep_draw_radio_field('allow_anonymous', '0', true) . ENTRY_POLLING_MEMBER_ONLY .
										"<br />" .
										tep_draw_radio_field('allow_anonymous', '1', false)	. ENTRY_POLLING_ANONYMOUS_ALLOWED;
								?>
							</td>
						</tr>
						<tr>
		  					<td colspan="2">
		  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
		  							<tr>
		  								<td align="right">
  											<?=tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, 'onClick="return form_checking();"', 'inputButton')?>
  											<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton')?>
		  								</td>
		  							</tr>
		  						</table>
		  					</td>
		  				</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
			</tr>
		</table>
		<script language="javascript">
		<!--
			function form_checking() {
				var start_date = document.getElementById('start_date').value;
				if(start_date.length > 0) {
     				if (!validateDate(start_date)) {
     					alert('<?=ERROR_POLLING_INVALID_START_DATE ?>');
						document.getElementById('start_date').focus();
						document.getElementById('start_date').select();
						return false;
     				}
   				} else {
   					alert('<?=ERROR_POLLING_MISSING_START_DATE ?>');
					document.getElementById('start_date').focus();
					document.getElementById('start_date').select();
					return false;
   				}
   				
   				var end_date = document.getElementById('end_date').value;
				if(end_date.length > 0) {
     				if (!validateDate(end_date)) {
     					alert('<?=ERROR_POLLING_INVALID_END_DATE ?>');
						document.getElementById('end_date').focus();
						document.getElementById('end_date').select();
						return false;
     				}
   				} else {
   					alert('<?=ERROR_POLLING_MISSING_END_DATE ?>');
					document.getElementById('end_date').focus();
					document.getElementById('end_date').select();
					return false;
   				}
   				
   				if (start_date.length > 0 && end_date.length > 0) {
   					if (!validStartAndEndDate(start_date, end_date)) {
   						alert('<?=ERROR_POLLING_INVALID_DATES ?>');
						document.getElementById('start_date').focus();
						document.getElementById('start_date').select();
						return false;
   					}
   				}
   				
   				var question = document.forms['add_poll_form'].elements['question'].value;
   				if(question.length<=0){
					alert('<?=ERROR_POLLING_MISSING_QUESTION ?>');
					document.forms['add_poll_form'].elements['question'].focus();
					document.forms['add_poll_form'].elements['question'].select();
  					return false;
   				}
   				
   				var options_arr = document.forms['add_poll_form'].elements['options[]'];
   				var options_exist = false;
   				for (i=0; i<options_arr.length; i++){
   					if(options_arr[i].value.length>=1){
   						options_exist = true;
   						break;
   					}
   				}
   				if (!options_exist){
   					alert('<?=ERROR_POLLING_MISSING_OPTION ?>');
					document.forms['add_poll_form'].elements['options[]'][0].focus();
					document.forms['add_poll_form'].elements['options[]'][0].select();
  					return false;
   				}
   				
   				var product_type_arr = document.forms['add_poll_form'].elements['product_type[]'];
   				var product_type_exist = false;
   				for (i=0; i<product_type_arr.length; i++){
   					if(product_type_arr[i].checked==true){
   						product_type_exist = true;
   						break;
   					}
   				}
   				if (!product_type_exist){
   					alert('<?=ERROR_POLLING_MISSING_TYPE ?>');
					document.forms['add_poll_form'].elements['product_type[]'][0].focus();
					document.forms['add_poll_form'].elements['product_type[]'][0].select();
  					return false;
   				}
   				
			}
		//-->
		</script>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean();
		
		return $report_section_html;
	}
	
	function edit_poll($filename, $id) {
		$report_section_html = '';
		$product_type_array = tep_get_product_type();
		
		$poll_select_sql = "SELECT pq.polls_questions_id, pq.polls_questions, pq.polls_questions_start_date, pq.polls_questions_end_date, pq.polls_questions_custom_products_type, pq.polls_questions_allow_anonymous 
							FROM " . TABLE_POLLS_QUESTIONS . " AS pq 
							WHERE pq.polls_questions_id='" . tep_db_input($id) . "'";
		$poll_result_sql = tep_db_query($poll_select_sql);
		
		if ($poll_row = tep_db_fetch_array($poll_result_sql)) {
			$poll_option_select_sql = "	SELECT polls_questions_options_id, polls_questions_options_value 
										FROM " . TABLE_POLLS_QUESTIONS_OPTIONS . " 
										WHERE polls_questions_id = '" . tep_db_input($id) . "'
										ORDER BY polls_questions_options_id";
			$poll_option_result_sql = tep_db_query($poll_option_select_sql);
			
			$non_editable = $this->_is_answered($id);
			
			ob_start();
?>
		<table width="100%" border="0" cellspacing="2" cellpadding="2">
			<tr>
				<td>
					<?=tep_draw_form('edit_poll_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=update_poll', 'post', '')?>
					<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2" class="smallText" valign="middle">
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_START_DATE?></td>
							<td class="main">
								<?=tep_draw_input_field('start_date', substr($poll_row['polls_questions_start_date'], 0, 10), 'id="start_date" size="16" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.edit_poll_form.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.edit_poll_form.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_END_DATE?></td>
							<td class="main">
								<?=tep_draw_input_field('end_date', substr($poll_row['polls_questions_end_date'], 0, 10), 'id="end_date" size="16" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.edit_poll_form.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.edit_poll_form.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_QUESTION?></td>
							<td class="main">
							<?
								if ($non_editable) {
									echo tep_draw_textarea_field('question', 'soft', 70, 5, $poll_row['polls_questions'], 'disabled="disabled"');
								} else {
									echo tep_draw_textarea_field('question', 'soft', 70, 5, $poll_row['polls_questions'], '');
								}
							?>
							</td>
						</tr>
						<tr>
							<td colspan="2">&nbsp;</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_OPTION?></td>
							<td class="main">
							<?
								while ($poll_option_row = tep_db_fetch_array($poll_option_result_sql)) {
									if ($non_editable) {
										echo tep_draw_input_field('options['.$poll_option_row['polls_questions_options_id'].']', $poll_option_row['polls_questions_options_value'], 'size="72" disabled="disabled"') . '<br><br>';
									} else {
										echo tep_draw_input_field('options['.$poll_option_row['polls_questions_options_id'].']', $poll_option_row['polls_questions_options_value'], 'size="72"') . '<br><br>';
									}
								}
								
								echo	tep_draw_input_field('new_options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('new_options[]', '', 'size="72"') . '<br><br>' .
										tep_draw_input_field('new_options[]', '', 'size="72"');
							?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_SHOW_IN?></td>
							<td class="main">
							<?
								$selected_product_type = explode(',', $poll_row['polls_questions_custom_products_type']);
								
								for ($p_cnt=0; $p_cnt < count($product_type_array); $p_cnt++) {
									echo tep_draw_checkbox_field('product_type[]', (string)$product_type_array[$p_cnt]['id'], in_array($product_type_array[$p_cnt]['id'], $selected_product_type) ? true : false) . $product_type_array[$p_cnt]['text'] . '<br>';
								}
							?>
							</td>
						</tr>
						<tr>
							<td width="15%" valign="top" class="main"><?=ENTRY_POLLING_WHO_CAN_VOTE?></td>
							<td class="main">
								<? 
									$selected_allow_anonymous = $poll_row['polls_questions_allow_anonymous'];
									if ($non_editable) {
										echo tep_draw_radio_field('allow_anonymous', '0', ($selected_allow_anonymous==0), '', 'disabled=disabled') . ENTRY_POLLING_MEMBER_ONLY .
											"<br />" .
											tep_draw_radio_field('allow_anonymous', '1', ($selected_allow_anonymous==1), '', 'disabled=disabled') . ENTRY_POLLING_ANONYMOUS_ALLOWED;
									} else {
										echo tep_draw_radio_field('allow_anonymous', '0', ($selected_allow_anonymous==0), '', '') . ENTRY_POLLING_MEMBER_ONLY .
											"<br />" .
											tep_draw_radio_field('allow_anonymous', '1', ($selected_allow_anonymous==1), '', '') . ENTRY_POLLING_ANONYMOUS_ALLOWED;
									}
								?>
							</td>
						</tr>
						<tr>
		  					<td colspan="2">
		  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
		  							<tr>
		  								<td align="right">
  											<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'onClick="return form_checking();"', 'inputButton')?>
  											<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link($filename, ''), '', 'inputButton')?>
		  								</td>
		  							</tr>
		  						</table>
		  					</td>
		  				</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
			</tr>
		</table>
		<script language="javascript">
		<!--
			function form_checking() {
				var start_date = document.getElementById('start_date').value;
				if(start_date.length > 0) {
     				if (!validateDate(start_date)) {
     					alert('<?=ERROR_POLLING_INVALID_START_DATE ?>');
						document.getElementById('start_date').focus();
						document.getElementById('start_date').select();
						return false;
     				}
   				} else {
   					alert('<?=ERROR_POLLING_MISSING_START_DATE ?>');
					document.getElementById('start_date').focus();
					document.getElementById('start_date').select();
					return false;
   				}
   				
   				var end_date = document.getElementById('end_date').value;
				if(end_date.length > 0) {
     				if (!validateDate(end_date)) {
     					alert('<?=ERROR_POLLING_INVALID_END_DATE ?>');
						document.getElementById('end_date').focus();
						document.getElementById('end_date').select();
						return false;
     				}
   				} else {
   					alert('<?=ERROR_POLLING_MISSING_END_DATE ?>');
					document.getElementById('end_date').focus();
					document.getElementById('end_date').select();
					return false;
   				}
   				
   				if (start_date.length > 0 && end_date.length > 0) {
   					if (!validStartAndEndDate(start_date, end_date)) {
   						alert('<?=ERROR_POLLING_INVALID_DATES ?>');
						document.getElementById('start_date').focus();
						document.getElementById('start_date').select();
						return false;
   					}
   				}
   				
				var question = document.forms['edit_poll_form'].elements['question'].value;
   				if(question.length<=0){
					alert('<?=ERROR_POLLING_MISSING_QUESTION ?>');
					document.forms['edit_poll_form'].elements['question'].focus();
					document.forms['edit_poll_form'].elements['question'].select();
  					return false;
   				}

				<?
				if (!$non_editable) {
				?>
   				var options_exist = false;
   				var first_opt_index = -1;
   				for(i=0; i<document.forms['edit_poll_form'].length; i++){
   					if(document.forms['edit_poll_form'].elements[i].name.indexOf("options")!=-1){
   						if(first_opt_index == -1){
   							first_opt_index = i;
   						}
   						if(document.forms['edit_poll_form'].elements[i].value.length>=1){
   							options_exist = true;
   							break;
   						}
   					}
   				}
   				if (!options_exist){
   					alert('<?=ERROR_POLLING_MISSING_OPTION ?>');
					document.forms['edit_poll_form'].elements[first_opt_index].focus();
					document.forms['edit_poll_form'].elements[first_opt_index].select();
  					return false;
   				}
   				<?
   				} else {
   				?>
   				//not editable
   				<?
   				}
   				?>
   				
  				var product_type_arr = document.forms['edit_poll_form'].elements['product_type[]'];
   				var product_type_exist = false;
   				for (i=0; i<product_type_arr.length; i++){
   					if(product_type_arr[i].checked==true){
   						product_type_exist = true;
   						break;
   					}
   				}
   				if (!product_type_exist){
   					alert('<?=ERROR_POLLING_MISSING_TYPE ?>');
					document.forms['edit_poll_form'].elements['product_type[]'][0].focus();
					document.forms['edit_poll_form'].elements['product_type[]'][0].select();
  					return false;
   				}
   				
			}
		//-->
		</script>
<?
			$report_section_html = ob_get_contents();
			ob_end_clean();
		}
		return $report_section_html;
	}
	
	function show_result($filename, $id) {
		$poll_info = $this->_get_polls_info($id);
		$total_option = count($poll_info['option']);
		$total_vote = 0;
		$option_vote = array();
		for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
			$count = $this->_get_answered_count($poll_info['option'][$opt_cnt]['id']);
			$option_vote[] = $count;
			$total_vote += (int)$count;
		}
		$report_section_html = '';
		$report_section_html .= '<table width="100%" border="0" cellspacing="2" cellpadding="2">';
		$report_section_html .= '	<tr>';
		$report_section_html .= '		<td colspan="3" class="dataTableHeading">';
		$report_section_html .= '			' . $poll_info['question'] . '<br /><br />';
		$report_section_html .= '		</td>';
		$report_section_html .= '	</tr>';
		$report_section_html .= '	<tr>';
		$report_section_html .= '		<td width="20">';
		$report_section_html .= '			&nbsp;';
		$report_section_html .= '		</td>';
		$report_section_html .= '		<td valign="top" width="200">';
		$report_section_html .= '			<table width="200" border="0" cellspacing="0" cellpadding="0">';
		$max_vote = max($option_vote);
		for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
			$option_question = $poll_info['option'][$opt_cnt]['value'];
			$option_count = $this->_get_answered_count($poll_info['option'][$opt_cnt]['id']);
			$report_section_html .= '				<tr>';
			$report_section_html .= '					<td class="dataTableContent" style="font-size:12px;">';
			$report_section_html .= '						' . $option_question . '';
			$report_section_html .= '					</td>';
			$report_section_html .= '				</tr>';
			$report_section_html .= '				<tr>';
			$report_section_html .= '					<td class="dataTableContent" style="height:16px; color:#000000; font-size:12px; background-color:#BBC664; background-image:url(\'' . DIR_WS_IMAGES . '/poll.gif\'); background-position: ' . ($total_vote>0?(round(200*$option_count/$total_vote)):0) . 'px 0px; background-repeat: no-repeat;">';
			
			if ($total_vote>0){
				$report_section_html .= '&nbsp;&nbsp;&nbsp;' . (round(100*$option_count/$total_vote, 2)) . '%';
			}
			$report_section_html .= '					</td>';
			$report_section_html .= '				</tr>';
			$report_section_html .= '				<tr>';
			$report_section_html .= '					<td style="color:#999999; font-size:12px;">';
			$report_section_html .= ' 						' . $option_count . ' ' . TEXT_INFO_VOTES;
			$report_section_html .= '					</td>';
			$report_section_html .= '				</tr>';
			$report_section_html .= '				<tr>';
			$report_section_html .= '					<td>';
			$report_section_html .= '						&nbsp;';
			$report_section_html .= '					</td>';
			$report_section_html .= '				</tr>';
		}
		$report_section_html .= '				<tr>';
		$report_section_html .= '					<td class="dataTableContent" style="font-size:12px;">';
		$report_section_html .= '						Total vote: ' . $total_vote . '';
		$report_section_html .= '					</td>';
		$report_section_html .= '				</tr>';
		$report_section_html .= '				<tr>';
		$report_section_html .= '					<td class="dataTableContent" style="font-size:12px;">';
		$report_section_html .= '						<span style="color:#0000BB; cursor: pointer;" onclick="showPollsComments('.$id.')">View Comments</span>';
		$report_section_html .= '					</td>';
		$report_section_html .= '				</tr>';
		$report_section_html .= '			</table>';
		$report_section_html .= '		</td>';
		$report_section_html .= '		<td valign="top">';
		$report_section_html .= '			<div id="polls_comments" name="polls_comments"></div>';
		$report_section_html .= '		</td>';
		$report_section_html .= '	</tr>';
		$report_section_html .= '	<tr>';
		$report_section_html .= '		<td colspan="3">';
		$report_section_html .= '			<table border="0" width="100%" cellspacing="0" cellpadding="0">';
		$report_section_html .= '				<tr>';
		$report_section_html .= '					<td align="right">';
		$report_section_html .= '						' . tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, ''), '', 'inputButton');
		$report_section_html .= '					</td>';
		$report_section_html .= '				</tr>';
		$report_section_html .= '			</table>';
		$report_section_html .= '		</td>';
		$report_section_html .= '	</tr>';
		$report_section_html .= '</table>';
		if(isset($_GET['cid'])){
			$report_section_html .= "<script language='JavaScript'>";
			$report_section_html .= "showPollsComments(".$id.");";
			$report_section_html .= "</script>";
		}
		return $report_section_html;
	}
	
	function display_comments($filename, $pid){
		$res_arr = $this->_get_polls_comments($pid);
		$comment_count = count($res_arr);
		echo "<table border='0' width='100%'>";
		echo "<tr>";
		echo "<td align='center' class='reportBoxHeading'>" . TABLE_HEADING_POLLING_DATE . "</td>";
		echo "<td align='center' class='reportBoxHeading'>" . TABLE_HEADING_POLLING_USER . "</td>";
		echo "<td align='center' class='reportBoxHeading'>" . TABLE_HEADING_POLLING_TYPE . "</td>";
		echo "<td align='center' class='reportBoxHeading'>" . TABLE_HEADING_POLLING_COMMENT . "</td>";
		echo "<td align='center' class='reportBoxHeading'>" . TABLE_HEADING_POLLING_ACTION . "</td>";
		echo "</tr>";
		if ($comment_count>=1){
			for ($i=0; $i < $comment_count; $i++) {
				$row_style = ($i%2) ? 'reportListingEven' : 'reportListingOdd' ;
				if(is_numeric($res_arr[$i]['customers_id'])){
					$cus_arr = $this->_get_customers_email((int)$res_arr[$i]['customers_id']);
					if($cus_arr['email']==''){
						$customer = $cus_arr['id'];
						$visitor_type = "Unknown ID";
					} else {
						$cus_email = $cus_arr['email'];
						$customer = "<a href='mailto:".$cus_email."'>".$cus_email."</a>";
						$visitor_type = "Member";
					}
				} else {
					$cus_del = strpos($res_arr[$i]['customers_id'], '(');
					if ($cus_del>0) {
						$cus_email = substr($res_arr[$i]['customers_id'], 0, $cus_del);
						$del_date = substr($res_arr[$i]['customers_id'], $cus_del);
						$customer = "<a href='mailto:".$cus_email."'>".$cus_email."</a>";
						$visitor_type = "Deleted " . $del_date;
					} else {
						$customer = "<a href='mailto:".$res_arr[$i]['customers_id']."'>".$res_arr[$i]['customers_id']."</a>";
						$visitor_type = "Guest";
					}
				}
				echo "<tr class='".$row_style."'>";
				echo "<td valign='top' class='reportRecords'>".$res_arr[$i]['polls_comments_date']."</td>";
				echo "<td valign='top' class='reportRecords'>".$customer."</td>";
				echo "<td valign='top' class='reportRecords'>".$visitor_type."</td>";
				echo "<td valign='top' class='reportRecords'>".$res_arr[$i]['polls_comments']."</td>";
				echo "<td align='center' valign='top' class='reportRecords'>";
				echo "<a href='javascript:void(confirm_delete(\"Poll Comment - ".$i."\", \"\", \"".tep_href_link($filename, 'action=view_poll_stat&subaction=delete_comment&id='.$pid.'&cid='.$res_arr[$i]['polls_comments_id'])."\"))'>".tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')."</a>";
				echo"</td>";
				echo "</tr>";
			}
				echo "<tr>";
				echo "<td colspan='5' class='reportRecords'><hr />" . $comment_count . " " . TEXT_INFO_COMMENTS . ".</td>";
				echo "</tr>";
		} else {
				echo "<tr>";
				echo "<td colspan='5' class='reportRecords'><hr />No comment found.</td>";
				echo "</tr>";
		}
		echo "</table>";
		
	}
	
	function _get_customers_email($id) {
		$customers_email_array = array();
		
		$customers_email_select_sql = "	SELECT customers_email_address 
								FROM " . TABLE_CUSTOMERS . "
								WHERE customers_id = '".tep_db_input($id)."'";
		$customers_email_result_sql = tep_db_query($customers_email_select_sql);
		
		if ($customers_email_row = tep_db_fetch_array($customers_email_result_sql)) {
			$customers_email_array = array('id' => $id,
								'email' => $customers_email_row['customers_email_address']
								);
		} else {
			$customers_email_array = array('id' => $id,
								'email' => ''
								);
		}
		
		return $customers_email_array;
	}

	function _get_polls_comments($id) {
		$poll_comments_array = array();
		
		$pool_comments_select_sql = "	SELECT *   
								FROM " . TABLE_POLLS_COMMENTS . "
								WHERE polls_questions_id = '".tep_db_input($id)."'";
		$pool_comments_result_sql = tep_db_query($pool_comments_select_sql);
		while ($pool_comments_row = tep_db_fetch_array($pool_comments_result_sql)) {
			$poll_comments_array[] = array('polls_comments_id' => $pool_comments_row['polls_comments_id'],
								'polls_comments' => $pool_comments_row['polls_comments'],
								'polls_comments_date' => $pool_comments_row['polls_comments_date'],
								'customers_id' => $pool_comments_row['customers_id']
								);
		}
		return $poll_comments_array;
	}
	
	function _get_polls_info($id) {
		$poll_array = array();
		
		$poll_select_sql = "	SELECT polls_questions  
								FROM " . TABLE_POLLS_QUESTIONS . "
								WHERE polls_questions_id = '".tep_db_input($id)."'";
		$poll_result_sql = tep_db_query($poll_select_sql);
		
		if ($poll_row = tep_db_fetch_array($poll_result_sql)) {
			$option_array = array();
			$option_select_sql = "	SELECT polls_questions_options_id, polls_questions_options_value
									FROM " . TABLE_POLLS_QUESTIONS_OPTIONS . "
									WHERE polls_questions_id='".tep_db_input($id)."' ORDER BY polls_questions_options_id";
			$option_result_sql = tep_db_query($option_select_sql);
			
			while ($option_row = tep_db_fetch_array($option_result_sql)) {
				$option_array[] = array('id' => $option_row['polls_questions_options_id'],
										'value' => $option_row['polls_questions_options_value']
										);
			}
			
			$poll_array = array('id' => $id,
								'question' => $poll_row['polls_questions'],
								'option' => $option_array
								);
		}
		
		return $poll_array;
	}
	
	function _get_answered_count($option) {
		$vote_count_select_sql = "	SELECT COUNT(customers_id) AS total_answer 
									FROM " . TABLE_POLLS_QUESTIONS_ANSWERS . "
									WHERE polls_questions_options_id = '".tep_db_input($option)."'";
		$vote_count_result_sql = tep_db_query($vote_count_select_sql);
		$vote_count_row = tep_db_fetch_array($vote_count_result_sql);
		
		return (int)$vote_count_row['total_answer'];
	}
	
	function _is_answered($id) {
		$vote_count_select_sql = "	SELECT COUNT(customers_id) AS total_answer 
									FROM " . TABLE_POLLS_QUESTIONS_ANSWERS . "
									WHERE polls_questions_id = '".tep_db_input($id)."'";
		$vote_count_result_sql = tep_db_query($vote_count_select_sql);
		$vote_count_row = tep_db_fetch_array($vote_count_result_sql);
		return (int)$vote_count_row['total_answer'] > 0 ? true : false;
	}
	
	function insert_poll($input_array, &$messageStack) {
		$error = false;
		
		$start_date = tep_db_prepare_input($input_array['start_date']);
		$end_date = tep_db_prepare_input($input_array['end_date']);
		$question = tep_db_prepare_input($input_array['question']);
		$option_array = $input_array['options'];
		$product_type_array = isset($input_array['product_type']) ? $input_array['product_type'] : array();
		$allow_anonymous = tep_db_prepare_input($input_array['allow_anonymous']);
		
		if (!tep_not_null($start_date)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_START_DATE, 'error');
		}
		
		if (!tep_not_null($end_date)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_END_DATE, 'error');
		}
		
		if (!tep_not_null($question)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_QUESTION, 'error');
		}
		
		if (!count($option_array)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_OPTION, 'error');
		}
		
		if (!$error) {
			// Insert account statement history
			$poll_data_array = array(	'polls_questions' => $question,
										'polls_questions_start_date' => $start_date,
				                        'polls_questions_end_date' => $end_date,
				                        'polls_questions_created_date' => 'now()',
				                        'polls_questions_added_by' => $this->identity_email,
				                        'polls_questions_custom_products_type' => implode(',', $product_type_array),
				                        'polls_questions_allow_anonymous' => $allow_anonymous
				                 	);
			tep_db_perform(TABLE_POLLS_QUESTIONS, $poll_data_array);
			$polls_questions_id = tep_db_insert_id();
			
			for ($opt_cnt=0; $opt_cnt < count($option_array); $opt_cnt++) {
				if (tep_not_null($option_array[$opt_cnt])) {
					$poll_option_data_array = array('polls_questions_id' => $polls_questions_id,
													'polls_questions_options_value' => tep_db_prepare_input($option_array[$opt_cnt])
						                 			);
					tep_db_perform(TABLE_POLLS_QUESTIONS_OPTIONS, $poll_option_data_array);
				}
			}
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function update_poll($input_array, &$messageStack) {
		$error = false;
		
		$question_id = tep_db_prepare_input($input_array['id']);
		$start_date = tep_db_prepare_input($input_array['start_date']);
		$end_date = tep_db_prepare_input($input_array['end_date']);
		$question = tep_db_prepare_input($input_array['question']);
		$option_array = $input_array['options'];
		$new_option_array = $input_array['new_options'];
		$product_type_array = isset($input_array['product_type']) ? $input_array['product_type'] : array();
		$allow_anonymous = tep_db_prepare_input($input_array['allow_anonymous']);
		
		$non_editable = $this->_is_answered($question_id);
		
		if (!tep_not_null($start_date)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_START_DATE, 'error');
		}
		
		if (!tep_not_null($end_date)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_END_DATE, 'error');
		}
		
		if (!$non_editable && !tep_not_null($question)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_QUESTION, 'error');
		}
		
		if (!$non_editable && !count($option_array)) {
			$error = true;
			$messageStack->add_session(ERROR_POLLING_MISSING_OPTION, 'error');
		}
		
		if (!$error) {
			// Insert account statement history
			$poll_data_array = array(	'polls_questions_start_date' => $start_date,
				                        'polls_questions_end_date' => $end_date,
				                        'polls_questions_last_modified' => 'now()',
				                        'polls_questions_custom_products_type' => implode(',', $product_type_array),
				                        'polls_questions_allow_anonymous' => $allow_anonymous
				                 	);
			if (!$non_editable) {
				$poll_data_array['polls_questions'] = $question;
			}
			
			tep_db_perform(TABLE_POLLS_QUESTIONS, $poll_data_array, 'update', 'polls_questions_id="'.tep_db_input($question_id).'"');
			
			if (!$non_editable && is_array($option_array)) {
				foreach ($option_array as $opt_id => $opt_val) {
					if (tep_not_null($opt_val)) {
						$poll_option_data_array = array('polls_questions_options_value' => tep_db_prepare_input($opt_val) );
						tep_db_perform(TABLE_POLLS_QUESTIONS_OPTIONS, $poll_option_data_array, 'update', 'polls_questions_options_id="'.tep_db_input($opt_id).'"');
					} else {	// Delete this option
						$poll_option_deletet_sql = "DELETE FROM " . TABLE_POLLS_QUESTIONS_OPTIONS . " WHERE polls_questions_options_id='".tep_db_input($opt_id)."'";
						tep_db_query($poll_option_deletet_sql);
					}
				}
			}
			
			for ($opt_cnt=0; $opt_cnt < count($new_option_array); $opt_cnt++) {
				if (tep_not_null($new_option_array[$opt_cnt])) {
					$poll_option_data_array = array('polls_questions_id' => $question_id,
													'polls_questions_options_value' => tep_db_prepare_input($new_option_array[$opt_cnt])
						                 			);
					tep_db_perform(TABLE_POLLS_QUESTIONS_OPTIONS, $poll_option_data_array);
				}
			}
		}
		
		if (!$error) {
			return true;
		} else {
			return false;
		}
	}
	
	function delete_poll($input_array, &$messageStack) {
	    $poll_id = tep_db_prepare_input($input_array['id']);
	    
	    // Delete poll question
	    $poll_deletet_sql = "DELETE FROM " . TABLE_POLLS_QUESTIONS . " WHERE polls_questions_id='".tep_db_input($poll_id)."'";
		tep_db_query($poll_deletet_sql);
    	
    	// Delete poll answers
	    $poll_answer_deletet_sql = "	DELETE " . TABLE_POLLS_QUESTIONS_ANSWERS . " 
										FROM " . TABLE_POLLS_QUESTIONS_ANSWERS . ", " . TABLE_POLLS_QUESTIONS_OPTIONS . "
										WHERE " . TABLE_POLLS_QUESTIONS_ANSWERS . ".polls_questions_options_id=" . TABLE_POLLS_QUESTIONS_OPTIONS . ".polls_questions_options_id 
											AND " . TABLE_POLLS_QUESTIONS_OPTIONS . ".polls_questions_id = '" . tep_db_input($poll_id) . "'";
		tep_db_query($poll_answer_deletet_sql);
		
    	// Delete poll options
	    $poll_option_deletet_sql = "DELETE FROM " . TABLE_POLLS_QUESTIONS_OPTIONS . " WHERE polls_questions_id='".tep_db_input($poll_id)."'";
		tep_db_query($poll_option_deletet_sql);
		
    	// Delete poll comments
	    $poll_comments_deletet_sql = "DELETE FROM " . TABLE_POLLS_COMMENTS . " WHERE polls_questions_id='".tep_db_input($poll_id)."'";
		tep_db_query($poll_comments_deletet_sql);

		return true;
	}
	
	function delete_comment($input_array, &$messageStack) {
	    $comment_id = tep_db_prepare_input($input_array['cid']);
	    
	    $comment_deletet_sql = "DELETE FROM " . TABLE_POLLS_COMMENTS . " WHERE polls_comments_id='".tep_db_input($comment_id)."'";
		tep_db_query($comment_deletet_sql);
    	
		return true;
	}

	function set_poll_status($input_array, &$messageStack) {
	    $poll_id = tep_db_prepare_input($input_array['id']);
	    $poll_status = tep_db_prepare_input($input_array['flag']);
		
		// Insert account statement history
		$poll_data_array = array(	'polls_questions_status' => $poll_status,
									'polls_questions_last_modified' => 'now()'
			                 	);
		tep_db_perform(TABLE_POLLS_QUESTIONS, $poll_data_array, 'update', 'polls_questions_id="'.tep_db_input($poll_id).'"');
		
		return true;
	}
}
?>