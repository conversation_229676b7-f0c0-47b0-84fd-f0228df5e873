<?php
/*
  	$Id: price_automation.php,v 1.5 2010/03/25 06:57:57 boonhock Exp $

  	Developer: CacPhy Foong
  	Copyright (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/

class price_automation {
    var $pa_cate_id;
    var $pa_prod_id;
    var $pa_category_cat_path = '';
    //var $pa_error_msg_arr = array();            //The complain queue for messageStack. Otherwise always assign messages as fatal.
    var $pa_preferred_margin = '35'; 			// default value 35%
    var $backorder_days = array();
    var $completed_delivered_qty = array();
    var $current_selling_price = array();
    var $game_product_id = array();
    var $backorder_oldest_order_day_arr = array();
    var $alert_setting_backorder_days_arr = array();
    // Buyback defination
    var $buyback_current_price = array();
    var $buyback_game_info_arr = array();
    var $pa_bb_total_competitors = 0;
    var $pa_bb_total_products = 0;
    var $pa_bb_competitors_array = array();
    var $pa_bb_products_array = array();
    var $pa_bb_competitors_price_array = array();
    var $pa_bb_preferred_rank = '';
     // Selling defination
    var $pa_selling_total_competitors = 0;
    var $pa_selling_total_products = 0;
    var $pa_selling_competitors_array = array();
    var $pa_selling_products_array = array();
    var $pa_selling_competitors_price_array = array();
    var $pa_selling_preferred_rank = '';
    var $bundle_product = array();
    var $package_quantity = array();
    
    //Public Constructor
    function price_automation($cate_id, $prod_id=0) {
        $this->pa_cate_id = $cate_id;
        $this->pa_prod_id = $prod_id;
        //$this->pa_category_cat_path = tep_output_generated_category_path_sq($this->pa_cate_id);
        //$this->set_products();
    }
    
    function pa_get_data_csv() {
		$line_break = "\n";
		$empty_columns = str_repeat(',', ($this->pa_bb_total_competitors*2));
		$export_csv_data = '';
		$export_csv_product = '';
		$export_csv_product_info = '';
		
		$export_csv_competitor = CSV_HEADING_BUYING_PRICE_IMPORT_CURRENCY .','.DEFAULT_CURRENCY. $line_break;
		$export_csv_competitor .= CSV_HEADING_COMPETITOR_ID.','.TABLE_HEADING_COMPETITOR_CODE;
		
		foreach ($this->pa_bb_competitors_array as $competitors_id => $competitors_arr) {
			$export_csv_competitor .= $line_break;
			$export_csv_competitor .= $competitors_id . ',' . $this->pa_bb_competitors_array[$competitors_id]['code'];
			$export_csv_competitor .= $empty_columns;
		}
		
		$export_csv_product .= CSV_HEADING_COMPETITOR_PRODUCT_ID. ',' . TABLE_HEADING_PRODUCT;
		
		for ($i=0; $i < $this->pa_bb_total_competitors; $i++) {
			$export_csv_product .= ',' . CSV_HEADING_COMPETITOR_PRODUCT_PRICE . ',' . CSV_HEADING_COMPETITOR_PRODUCT_STATUS;
		}
		
		if (sizeof($this->products_arr) > 0) {
			foreach ($this->products_arr as $product_id => $product_name) {
				$export_csv_product_info .= $product_id . ',' . $product_name['game_path'] . $line_break;
			}
		}
		
		$export_csv_data = $export_csv_competitor . $line_break . '###' . $empty_columns . $line_break . $export_csv_product . $line_break . $export_csv_product_info;
		
		return $export_csv_data;
	}
	
	function set_products() {
		global $languages_id;
		
		if ($this->pa_cate_id > 0) {
			//get the whole category
			$cat_parent_path = tep_get_categories_parent_path($this->pa_cate_id);
			if (tep_not_null($cat_parent_path)) {
				$cat_parent_array = explode("_",$cat_parent_path);
				$top_parent_id = $cat_parent_array[1];
				
				$cat_parent_path .= $this->pa_cate_id."_";
			} else {
				$cat_parent_path = "_".$this->pa_cate_id."_";
				$top_parent_id = (int)$this->pa_cate_id;
			}
			
			$products_select_product = "SELECT p.products_cat_path, p.products_id, abp.buyback_price, p.products_cat_id_path 
			           					FROM " . TABLE_PRODUCTS . " AS p
			           					LEFT JOIN " . TABLE_AUTOMATE_BUYBACK_PRICE . " AS abp 
											ON p.products_id = abp.products_id 
			           					WHERE 1 ";
			if ((int)$this->pa_cate_id>0) {
				$cat_parent_path = str_replace('_', '\_', $cat_parent_path);
				$products_select_product .= " AND p.products_cat_id_path LIKE '".$cat_parent_path."%' ";
			}
			$products_select_product .= " 	AND p.custom_products_type_id=0
			           						AND p.products_bundle=''
											AND p.products_bundle_dynamic=''
			           					ORDER BY p.products_cat_path";
		
			$products_result = tep_db_query($products_select_product);
			while ($products_row = tep_db_fetch_array($products_result)) {
				$product_cat_path_array = explode("_",$products_row['products_cat_id_path']);
				$products_row['categories_id'] = $product_cat_path_array[count($product_cat_path_array)-2];	// Last element is '_'
				
				$game_name = tep_display_category_path($products_row['products_cat_path'], $products_row['categories_id']);

				$this->products_arr[$products_row['products_id']] = array(	"game_name" => $game_name, 
																			"game_price" => $products_row['buyback_price'],
																			"categories_id" => $products_row['categories_id'],
																			"game_path" => $products_row['products_cat_path']);
			}
		}
		
	}
	
	function is_buyback_game() {
		$buyback_cat_select_sql = "	SELECT categories_id 
									FROM " . TABLE_CATEGORIES . " 
									WHERE categories_buyback_main_cat = 1 
										AND categories_id = '" . tep_db_input($this->pa_cate_id) . "'";
		$buyback_cat_result_sql = tep_db_query($buyback_cat_select_sql);
		
		if (tep_db_num_rows($buyback_cat_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
	
	function pa_bb_assign_competitor($competitor_id, $competitor_code) {
		$this->pa_bb_competitors_array[$competitor_id] = array('code' => $competitor_code);
		$this->pa_bb_total_competitors = count($this->pa_bb_competitors_array);
	}
	
	function pa_selling_assign_competitor($competitor_id, $competitor_code) {
		$this->pa_selling_competitors_array[$competitor_id] = array('code' => $competitor_code);
		$this->pa_selling_total_competitors = count($this->pa_selling_competitors_array);
	}
	
	function pa_bb_assign_competitors_price($products_id, $competitor_id, $price, $is_buyback) {
		$this->pa_bb_competitors_price_array[$products_id][$competitor_id] = array('price' => $price, 'is_buyback' => $is_buyback);
	}

	function pa_selling_assign_competitors_price($products_id, $competitor_id, $price, $is_buyback) {
		$this->pa_selling_competitors_price_array[$products_id][$competitor_id] = array('price' => $price, 'is_buyback' => $is_buyback);
	}
	
	function set_competitor_highest_price($products_id, $highest_price, $full_highest_price, $type='buyback') {
		if ($type == 'buyback') {
			$this->pa_bb_competitors_price_array[$products_id]['highest_price'] = $highest_price;
			$this->pa_bb_competitors_price_array[$products_id]['full_highest_price'] = $full_highest_price;
		} else {
			$this->pa_selling_competitors_price_array[$products_id]['highest_price'] = $highest_price;
			$this->pa_selling_competitors_price_array[$products_id]['full_highest_price'] = $full_highest_price;
		}
	}
	
	function pa_highest_competitor_price($products_id, $type='buyback', $is_buyback = 0) {
		$result = 0;
		
		if ($type == 'buyback') {
			if (isset($this->pa_bb_competitors_price_array[$products_id])) {
				return $is_buyback == 0 
						? $this->pa_bb_competitors_price_array[$products_id]['full_highest_price'] 
						: $this->pa_bb_competitors_price_array[$products_id]['highest_price'];
			}
		} else {
			if (isset($this->pa_selling_competitors_price_array[$products_id])) {
				return $is_buyback == 0 
						? $this->pa_selling_competitors_price_array[$products_id]['full_highest_price'] 
						: $this->pa_selling_competitors_price_array[$products_id]['highest_price'];
			}
		}
		return $result;
	}
	
	function array_average ($price_value) {
	  //variable and initializations
	  $the_result = 0.0;
	  $the_array_sum = array_sum($price_value); //sum the elements
	  $number_of_elements = count($price_value); //count the number of elements
	
	  //calculate the result
	  $the_result = $the_array_sum / $number_of_elements;
	
	  //return the value
	  return $the_result;
	}
	
	function get_backorder () {
		if(count($this->pa_bb_products_array) > 0) {
			$considered_order_status_array = array(7, 2); // 7 = Verifying ; 2 = Processing ;
			/*
			$backorder_select_sql = "	SELECT op.products_id, products_quantity - products_delivered_quantity AS undeliver_processing_qty, 
											( TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()) ) AS date_today, 
											( TO_DAYS(o.date_purchased)*24*3600 + TIME_TO_SEC(o.date_purchased) ) AS date_purchased, 
											( TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR)) ) AS date_eta, 
											TO_DAYS(NOW()) - TO_DAYS(o.date_purchased) AS day_past
										FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
										INNER JOIN " . TABLE_ORDERS . " AS o 
											ON op.orders_id = o.orders_id 
										WHERE o.date_purchased >= DATE_SUB(NOW(), INTERVAL 3 MONTH) 
											AND op.products_id IN ('".implode("', '", array_keys($this->pa_bb_products_array))."') 
											AND o.orders_status IN ('".implode("', '", $considered_order_status_array)."') 
											AND (op.orders_products_purchase_eta IS NOT NULL AND op.orders_products_purchase_eta != -999) 
										";
			
			*/
			$backorder_select_sql = "	SELECT op.products_id, products_quantity - products_delivered_quantity AS undeliver_processing_qty, 
											( TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()) ) AS date_today, 
											( TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR)) ) AS date_eta 
										FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
										INNER JOIN " . TABLE_ORDERS . " AS o 
											ON op.orders_id = o.orders_id 
										WHERE op.products_id IN ('".implode("', '", array_keys($this->pa_bb_products_array))."') 
											AND o.orders_status IN ('".implode("', '", $considered_order_status_array)."') 
											AND (op.orders_products_purchase_eta IS NOT NULL AND op.orders_products_purchase_eta != -999) 
										";
			//echo $backorder_select_sql;
			$backorder_result_sql = tep_db_query($backorder_select_sql);
			while ($backorder_row = tep_db_fetch_array($backorder_result_sql)) {
				if ($backorder_row['date_today'] > $backorder_row['date_eta']) {
					$bo_date = $backorder_row['date_today'] - $backorder_row['date_eta']; // 63379973779 63380060179 $backorder_row['date_today']
					$this->assign_backorder_days($backorder_row['products_id'], $bo_date, $backorder_row['undeliver_processing_qty']);
				}
			}
		}
	}
	
	function assign_backorder_days ($product_id, $bo_date, $undeliver_processing_qty) {
		$this->backorder_days[$product_id][0] += 86400 > $bo_date ? $undeliver_processing_qty : 0 ; 					     //   86400 secs = 1 day
		$this->backorder_days[$product_id][1] += 172800 > $bo_date && 86400 < $bo_date ? $undeliver_processing_qty : 0 ;     //  172800 secs = 2 days
		$this->backorder_days[$product_id][2] += 259200 > $bo_date && 172800 < $bo_date ? $undeliver_processing_qty : 0 ;    //  259200 secs = 3 days
		$this->backorder_days[$product_id][3] += 345600 > $bo_date && 259200 < $bo_date ? $undeliver_processing_qty : 0 ;    //  345600 secs = 4 days
		$this->backorder_days[$product_id][4] += 432000 > $bo_date && 345600 < $bo_date ? $undeliver_processing_qty : 0 ;    //  432000 secs = 5 days
		$this->backorder_days[$product_id][5] += 1209600 > $bo_date && 432000 < $bo_date ? $undeliver_processing_qty : 0 ;   // 1209600 secs = 14 days or 2 weeks
		$this->backorder_days[$product_id][6] += 7776000 > $bo_date && 1209600 < $bo_date ? $undeliver_processing_qty : 0 ; // 2592000 secs = 30 days or 1 month ; 2592000 x 3 months = 7776000 secs
		
	}
	
	function get_bundle_product ($product_id_arr) {
		$product_bundle_select_sql = "	SELECT pb.subproduct_id, pb.bundle_id, pb.subproduct_qty, p.products_price 
										FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
										LEFT JOIN " . TABLE_PRODUCTS . " AS p 
											ON (pb.bundle_id = p.products_id) 
										WHERE pb.subproduct_id IN (" . implode(", ", $product_id_arr) . ") 
										ORDER BY pb.subproduct_qty";
		$product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
		while ($product_bundle_row = tep_db_fetch_array($product_bundle_result_sql)) {
			$this->assign_bundle_product($product_bundle_row['subproduct_id'], $product_bundle_row['bundle_id'], $product_bundle_row['subproduct_qty'], $product_bundle_row['products_price']);
		}
	}
	
	function assign_bundle_product ($product_id, $bundle_id, $subproduct_qty, $products_price) {
		//$this->bundle_product[$product_id][$subproduct_qty] = array( 'bundle_id' => $bundle_id, 'subproduct_qty' => $subproduct_qty, 'products_price' => $products_price );
		if (!in_array($subproduct_qty, $this->package_quantity)) {
			$this->package_quantity[] = $subproduct_qty;
		}
		
	}
	
	function get_completed_delivered_qty () {
		if(count($this->pa_bb_products_array) > 0) {
			$completed_delivered_qty_select_sql = "	SELECT sa.sales_activities_quantity, sa.sales_activities_products_id,
													( TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()) ) AS date_today,
													( TO_DAYS(sa.sales_activities_date)*24*3600 + TIME_TO_SEC(sa.sales_activities_date) ) AS sales_activities_date 
													FROM " . TABLE_SALES_ACTIVITIES . " AS sa 
													WHERE sa.sales_activities_products_id IN ('".implode("', '", array_keys($this->pa_bb_products_array))."') 
														AND sa.sales_activities_code = 'D' 
														AND sa.sales_activities_operator = '+' 
														AND ( sa.sales_activities_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH) )
													";
			$completed_delivered_qty_result_sql = tep_db_query($completed_delivered_qty_select_sql);
			while ($completed_delivered_qty_row = tep_db_fetch_array($completed_delivered_qty_result_sql)) {
				$sales_activities_date = $completed_delivered_qty_row['date_today'] - $completed_delivered_qty_row['sales_activities_date'];
				$this->assign_completed_delivered_qty($completed_delivered_qty_row['sales_activities_products_id'], $sales_activities_date, $completed_delivered_qty_row['sales_activities_quantity']);
			}
		}
	}
	
	function assign_completed_delivered_qty ($product_id, $sales_activities_date, $sales_activities_quantity) {
		$this->completed_delivered_qty[$product_id][0] += 86400 > $sales_activities_date ? $sales_activities_quantity : 0 ; 									//  86400 secs = 1 day
		$this->completed_delivered_qty[$product_id][1] += 172800 > $sales_activities_date && 86400 < $sales_activities_date ? $sales_activities_quantity : 0 ;  // 172800 secs = 2 days
		$this->completed_delivered_qty[$product_id][2] += 259200 > $sales_activities_date && 172800 < $sales_activities_date ? $sales_activities_quantity : 0 ; // 259200 secs = 3 days
		$this->completed_delivered_qty[$product_id][3] += 345600 > $sales_activities_date && 259200 < $sales_activities_date ? $sales_activities_quantity : 0 ; // 345600 secs = 4 days
		$this->completed_delivered_qty[$product_id][4] += 432000 > $sales_activities_date && 345600 < $sales_activities_date ? $sales_activities_quantity : 0 ; // 432000 secs = 5 days
		$this->completed_delivered_qty[$product_id][5] += 1209600 > $sales_activities_date && 432000 < $sales_activities_date ? $sales_activities_quantity : 0 ; // 1209600 secs = 14 days or 2 weeks
		$this->completed_delivered_qty[$product_id][6] += 7776000 > $sales_activities_date && 1209600 < $sales_activities_date ? $sales_activities_quantity : 0 ; // 2592000 secs = 30 days or 1 month ; 2592000 x 3 months = 7776000 secs
	}
	
	function get_current_selling_price () {
		if(count($this->pa_bb_products_array) > 0) {
			$current_selling_price_select_sql = "	SELECT p.products_id, p.products_price 
													FROM " . TABLE_PRODUCTS . " AS p 
													WHERE p.products_id IN ('".implode("', '", array_keys($this->pa_bb_products_array))."') 
													";
			$current_selling_price_result_sql = tep_db_query($current_selling_price_select_sql);
			while ($current_selling_price_row = tep_db_fetch_array($current_selling_price_result_sql)) {
				$this->current_selling_price[$current_selling_price_row['products_id']] = $current_selling_price_row['products_price'];
			}
			return $current_selling_price_row;
		} else {
			return false;
		}
	}
	
	function pa_get_bb_data_csv ($buyback_suggester_price_array, $buyback_overwrite_price_array) {
		$line_break = "\n";
		$export_csv_data = '';
		
		$export_csv_data = CSV_HEADING_BUYBACK_PRODUCT_ID .','.CSV_HEADING_BUYBACK_PRODUCT_NAME.','.CSV_HEADING_BUYBACK_SUGGESTED_PRICE.','.CSV_HEADING_BUYBACK_OVERWRITE_PRICE;
		
		foreach ($buyback_suggester_price_array as $game_id => $suggested_price) {
			$product_path = $this->tep_get_product_path($game_id);
			$export_csv_data .= $line_break;
			$export_csv_data .= $game_id . ',' . $product_path . ',' . $suggested_price . ',' . $buyback_overwrite_price_array[$game_id];
		}
		return $export_csv_data;
	}
	
	function pa_get_selling_data_csv ($selling_suggester_price_array, $selling_overwrite_price_array) {
		$line_break = "\n";
		$export_csv_data = '';
		
		$export_csv_data = CSV_HEADING_BUYBACK_PRODUCT_ID .','.CSV_HEADING_BUYBACK_PRODUCT_NAME.','.CSV_HEADING_BUYBACK_SUGGESTED_PRICE.','.CSV_HEADING_BUYBACK_OVERWRITE_PRICE;
		
		foreach ($selling_suggester_price_array as $game_id => $suggested_price) {
			$product_path = $this->tep_get_product_path($game_id);
			$export_csv_data .= $line_break;
			$export_csv_data .= $game_id . ',' . $product_path . ',' . $suggested_price . ',' . $selling_overwrite_price_array[$game_id];
		}
		return $export_csv_data;
	}
	
	function tep_get_product_path($id) {
		$product_path_select_sql = "SELECT p.products_cat_path 
									FROM " . TABLE_PRODUCTS . " AS p 
									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
										ON p.products_id=pd.products_id 
									WHERE p.products_id = '".tep_db_input($id)."'";
		$product_path_result_sql = tep_db_query($product_path_select_sql);
		
		if ($product_path_row = tep_db_fetch_array($product_path_result_sql)) {
			return $product_path_row['products_cat_path'];
		} else {
			return '';
		}
	}
	
	function get_buyback_current_price () {
		$buyback_current_price_sql = "	SELECT products_id, buyback_price 
										FROM " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
										WHERE products_id IN(".implode("," , $this->game_product_id).")";
		$buyback_current_price_result_sql = tep_db_query($buyback_current_price_sql);
		while ($buyback_current_price_row = tep_db_fetch_array($buyback_current_price_result_sql)) {
			$this->buyback_current_price[$buyback_current_price_row['products_id']] = $buyback_current_price_row['buyback_price'];
		}
		return $this->buyback_current_price;
	}
	
	function pa_assign_backorder_oldest_order_day () {
		if (sizeof($this->game_product_id) > 0) {
			$backorder_oldest_order_day_select_sql = "	SELECT op.products_id, 
															MAX(TO_DAYS(NOW()) - TO_DAYS(o.date_purchased)) as oldest_order_day 
														FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
														INNER JOIN " . TABLE_ORDERS . " AS o 
															ON op.orders_id = o.orders_id 
														WHERE op.products_id IN(".implode("," , $this->game_product_id).") 
															AND o.orders_status IN (2,7) 
														GROUP BY op.products_id";
			
			$backorder_oldest_order_day_result_sql = tep_db_query($backorder_oldest_order_day_select_sql);
			while ($backorder_oldest_order_day_row = tep_db_fetch_array($backorder_oldest_order_day_result_sql)) {
				$this->backorder_oldest_order_day_arr[$backorder_oldest_order_day_row['products_id']] = $backorder_oldest_order_day_row['oldest_order_day'];
			}
		}
	}
	
	function bo_alert_color_setting ($backorder_days = 0) {
		$total_backorder_color = sizeof($this->alert_setting_backorder_days_arr);
		$backorder_cnt = 0;
		$last_record = $total_backorder_color - 1;
		
		if ($total_backorder_color != 0) {
			for ($backorder_cnt = 0; $backorder_cnt < $total_backorder_color; $backorder_cnt++) {
				if ($backorder_cnt != $last_record) {
					if ($this->alert_setting_backorder_days_arr[$backorder_cnt]['bo_days'] > $backorder_days) {
						return $this->alert_setting_backorder_days_arr[$backorder_cnt]['bo_color'];
					}
				} else {
					return $this->alert_setting_backorder_days_arr[$backorder_cnt]['bo_color'];
				}
			}
		}
	}
	
}
?>