<?
class product {
	var $product_id;
	
	function product($pd_id) {
      	$this->product_id = $pd_id;
	}
	
	function get_delivery_mode($custom_products_type_id) {
		global $memcache_obj;
		$cache_key = TABLE_PRODUCTS_DELIVERY_MODE . '/products_delivery_mode_id/array/custom_products_type_id/' . (int)$custom_products_type_id;
    	$cache_result = $memcache_obj->fetch($cache_key);
		if ($cache_result !== FALSE) {
			$products_delivery_mode_array = $cache_result;
		} else {
			$products_delivery_mode_array = array();
			$products_delivery_mode_sql = "	SELECT products_delivery_mode_id, products_delivery_mode_title
											FROM ".TABLE_PRODUCTS_DELIVERY_MODE."
											WHERE FIND_IN_SET('".(int)$custom_products_type_id."',custom_products_type) 
												OR custom_products_type IS NULL
											ORDER BY products_delivery_mode_title";
			$products_delivery_mode_result = tep_db_query($products_delivery_mode_sql);
			while ($products_delivery_mode_row = tep_db_fetch_array($products_delivery_mode_result)) {
				$products_delivery_mode_array[$products_delivery_mode_row['products_delivery_mode_id']] = $products_delivery_mode_row['products_delivery_mode_title'];
			}
			$memcache_obj->store($cache_key, $products_delivery_mode_array, 86400); // cache 24 hours
		}
		
		return $products_delivery_mode_array;
	}
	
	function get_other_currencies_price() {
		global $currencies;
		
		$pd_curr_prices_arr = array();
		
		$products_currency_prices_select_sql = "	SELECT products_currency_prices_code, products_currency_prices_value 
													FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . " 
													WHERE products_id = '" . (int)$this->product_id . "'";
		$products_currency_prices_result_sql = tep_db_query($products_currency_prices_select_sql);
		while($products_currency_prices_row = tep_db_fetch_array($products_currency_prices_result_sql)) {
			$pd_curr_prices_arr[] = array('currency_code' => $products_currency_prices_row['products_currency_prices_code'], 'price' => $currencies->format($products_currency_prices_row['products_currency_prices_value'], false, $products_currency_prices_row['products_currency_prices_code']));
		}
		
		return $pd_curr_prices_arr;
	}
	
	function delete_cache() {
		global $memcache_obj;
		
		$languages = tep_get_languages(); 
		
		for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
			$memcache_obj->delete(TABLE_PRODUCTS_DESCRIPTION . '/get/products_image/products_id/' . $this->product_id . '/language/' . $languages[$i]['id'], 0);
		}
	}
	
	function get_product_delivery_mode($products_id) {
		global $memcache_obj;
		
		$cache_key = TABLE_PRODUCTS_DELIVERY_INFO . '/products_delivery_mode_id/array/products_id/' . $products_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$products_delivery_mode_array = array();
		
		if ($cache_result !== FALSE) {
			$products_delivery_mode_array = $cache_result;
		} else {
			$product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id 
										FROM " . TABLE_PRODUCTS . " 
										WHERE products_id='" . tep_db_input($products_id) . "'";
			$product_info_result_sql = tep_db_query($product_info_select_sql);
			$product_info_row = tep_db_fetch_array($product_info_result_sql);
			
			if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
				// Assume package ONLY consists of ONE product type
				$product_delivery_mode_found = false;
				$custom_products_type_id = $product_info_row["custom_products_type_id"];
				$sub_product_select_sql = "	SELECT p.products_id, p.custom_products_type_id 
							    			FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												ON p.products_id=pb.subproduct_id 
							    			WHERE pb.bundle_id = '" . (int)$products_id . "'";
				$sub_product_result_sql = tep_db_query($sub_product_select_sql);
				while ($sub_product_row = tep_db_fetch_array($sub_product_result_sql)) {
					$custom_products_type_id = $sub_product_row['custom_products_type_id'];
					
					$sub_product_delivery_mode_array = product::get_product_delivery_mode($sub_product_row['products_id']);
					
					if (count($products_delivery_mode_array)) {
						$products_delivery_mode_array = array_intersect($products_delivery_mode_array, $sub_product_delivery_mode_array);
					} else {
						$products_delivery_mode_array = $sub_product_delivery_mode_array;
					}
				}
				
				if (!$product_delivery_mode_found && count($products_delivery_mode_array)==0) {
					if ($custom_products_type_id==2) {
						$products_delivery_mode_array[] = 5; // force all to 5 if empty
					} else if ($custom_products_type_id==0) {
						$products_delivery_mode_array[] = 1; // force all to 5 if empty
					}
				}
			} else {
				$product_delivery_mode_select_sql = "	SELECT pdi.products_delivery_mode_id
														FROM " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
														WHERE pdi.products_id = '".(int)$products_id."'";
				$product_delivery_mode_result_sql = tep_db_query($product_delivery_mode_select_sql);
				while ($product_delivery_mode_row = tep_db_fetch_array($product_delivery_mode_result_sql)) {
					$products_delivery_mode_array[] = $product_delivery_mode_row['products_delivery_mode_id'];
				}
				
				if (count($products_delivery_mode_array)==0) {
					if ($product_info_row["custom_products_type_id"]==2) {
						$products_delivery_mode_array[] = 5; // force all to 5 if empty
					} else if ($product_info_row["custom_products_type_id"]==0) {
						$products_delivery_mode_array[] = 1; // force all to 5 if empty
					}
				}
			}
			
			$memcache_obj->store($cache_key, $products_delivery_mode_array, 86400); // 1 day
		}
		return $products_delivery_mode_array;
	}
}
?>