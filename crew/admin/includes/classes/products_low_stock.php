<?
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCTS_LOW_STOCK)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCTS_LOW_STOCK);
}

class products_low_stock {
	
	// class constructor
    function products_low_stock() {
	}
	
	function add_low_stock_warning($trans_array, &$messageStack) {
		$error = false;
		
		if (isset($trans_array['products_id']) && tep_not_null($trans_array['products_id'])) {
			$check_warning_select_sql = "SELECT products_id FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE products_id = '" . tep_db_input($trans_array['products_id']) . "'";
			$check_warning_result_sql = tep_db_query($check_warning_select_sql);
			if (tep_db_num_rows($check_warning_result_sql) <= 0) {
				$data_array = array (	'products_id' => $trans_array['products_id'],
										'custom_products_type_id' => $trans_array['custom_products_type_id'],
										'low_stock_date' => 'now()',
										'low_stock_tag_ids' => '',
										'low_stock_status' => 1
									);
				tep_db_perform(TABLE_PRODUCTS_LOW_STOCK, $data_array);
	      		$messageStack->add('Warning record successfully added.', 'success');
			}
		} else {
			$error = true;
			$messageStack->add('Failure on adding warning record.', 'Error');
		}
		
		return $error;
	}
	
	function delete_low_stock_warning($trans_array, &$messageStack) {
		$error = false;
		
		if (isset($trans_array['pid']) && tep_not_null($trans_array['pid'])) {
	      	$delete_query = tep_db_query("DELETE FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE products_id = '" . tep_db_input($trans_array['pid']) . "'");
      		$messageStack->add('Warning record successfully deleted.', 'success');
		} else {
			$error = true;
			$messageStack->add('Failure on deleting warning record.', 'Error');
		}
		
		return $error;
	}
	
	function get_low_stock_tags() {
		$status_dependent_tag_array = array ( array('id' => '', 'text' => 'Order Lists Options ...'),
											 array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
										   );
		
		$lowstock_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('1', orders_tag_status_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."'";
		$lowstock_tag_result_sql = tep_db_query($lowstock_tag_select_sql);
		while ($lowstock_tag_row = tep_db_fetch_array($lowstock_tag_result_sql)) {
			$status_dependent_tag_array[] = array('id' => 'otag_'.$lowstock_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$lowstock_tag_row["orders_tag_name"]);
			$mirror_for_delete_tag[] = array('id' => 'rmtag_'.$lowstock_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$lowstock_tag_row["orders_tag_name"]);
		}
		$status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
		$status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
		if (count($mirror_for_delete_tag)) {
			$status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);
		}
		
		return $status_dependent_tag_array;
	}
	
	function show_low_stock_warning($trans_array) {
		global $messageStack;
		
		$low_stock_list_html = '';
		$tags_obj = new order_tags();
		
		$status_dependent_tag_array = $this->get_low_stock_tags();
		
		$warning_list = array('1' => array('prd_id'=>array()));
		
		$warning_query_raw = "select products_id, custom_products_type_id, low_stock_date, low_stock_tag_ids from " . TABLE_PRODUCTS_LOW_STOCK . " where custom_products_type_id='2' order by low_stock_date";
		$warning_split = new splitPageResults($trans_array['page'], MAX_DISPLAY_SEARCH_RESULTS, $warning_query_raw, $warning_query_numrows);
		$warning_split->show_all = false;
		$warning_query = tep_db_query($warning_query_raw);
		$rows = 0;
		
		ob_start();
?>
			<table width="100%" align="center" border="0" cellpadding="5" cellspacing="2" class="main">
				<tr>
					<td>
						<?=tep_draw_form('low_stock_form', FILENAME_EDIT_PURCHASE_ORDERS, tep_get_all_get_params(array('subaction')), 'post', ''); ?>
						<?=tep_draw_hidden_field('subaction', 'navigate', ' id="subaction" ');?>
						<?=tep_submit_button('New PO', 'Proceed To Blank Purchase Order', 'name="multi_submit_btn" onClick="return low_stock_form_check(\'create_blank_po\')"', 'inputButton');?>
					</td>
					<td>
						<? 
						echo tep_draw_hidden_field('1_prd_str', '', ' id="1_prd_str" ');
						echo '<span id="1_tag_nav">'.tep_draw_pull_down_menu("1_tag_selector", $status_dependent_tag_array, '', ' id="1_tag_selector" onChange="orderListsOptions(this, \'1\', \''.(int)$_SESSION['languages_id'].'\', \'\', true);"').'</span>';
						?>
					</td>
					<td colspan="5"></td>
				</tr>
				<tr>
					<td class="reportBoxHeading"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_PRODUCT_NAME; ?></font></td>
					<td class="reportBoxHeading"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_TAG; ?></font></td>
					<td class="reportBoxHeading"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_WARNING_SINCE; ?></font></td>	
					<td class="reportBoxHeading" align="right"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_ACTUAL_QTY; ?></font></td>
					<td class="reportBoxHeading" align="right"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_AVAIL_QTY; ?></font></td>
					<td class="reportBoxHeading" align="right"><font size="1"><?php echo TABLE_HEADING_LOW_STOCK_REORDER_LEVEL; ?></font></td>
					<td class="reportBoxHeading" align="center"><?=tep_draw_checkbox_field('select_all', '', false, '', 'id="select_all" title="Select or deselect all products" onclick="javascript:void(setCheckboxes(\'low_stock_form\',\'select_all\',\'low_stock_batch\'));"')?></font></td>
				</tr>
<?
		while ($warning_row = tep_db_fetch_array($warning_query)) {
			$product_qty_query = tep_db_query("SELECT p.products_actual_quantity, p.products_quantity, p.products_quantity_order, pc.categories_id, p.products_status FROM " . TABLE_PRODUCTS . " AS p LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc ON p.products_id=pc.products_id WHERE p.products_id = '" . tep_db_input($warning_row['products_id']) . "' AND pc.products_is_link=0");
			$product_qty_row = tep_db_fetch_array($product_qty_query);
			
			$cat_cfg_array = tep_get_cfg_setting($products[$i]['id'], 'product');
			$stock_reorder = $product_qty_row['products_quantity_order'];
			$warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];
			
			if ($product_qty_row['products_status'] == '0' || $product_qty_row['products_quantity'] > $warning_stock) {
				// automatic remove the low stock warning when actual quantity is greater than reorder level quantity
				$this->delete_low_stock_warning(array('pid' => $warning_row['products_id']), $messageStack);
				continue;
			}
			
			$rows++;
			if ($rows % 2 == "1") {
				$tr_classname = "reportListingEven";
			}
			else {
				$tr_classname = "reportListingOdd";
			}
			
			$warning_list['1']['prd_id'][] = $warning_row['products_id'];
			
			$tags_name_str = $tags_obj->get_tags_name_from_id($warning_row['low_stock_tag_ids'], FILENAME_PRODUCTS_LOW_STOCK, '1');
			
			$product_desc_query = tep_db_query("SELECT products_name, products_description FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE language_id='" . (int)$_SESSION['languages_id'] . "' AND products_id='" . tep_db_input($warning_row['products_id']) . "'");
			$product_desc_row = tep_db_fetch_array($product_desc_query);
			
			echo '				<tr id="1_main_'.$rows.'" class="'.$tr_classname.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$tr_classname.'\')" onclick="rowClicked(this, \''.$tr_classname.'\')">' . "\n";
?>
					<td class="dataTableContent"><?php echo '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($product_qty_row['categories_id']) . '&pID=' . $warning_row['products_id'] . '&action=new_product') . '" target="_blank">' . strip_tags($product_desc_row['products_name']) . '</a>'; ?></td>
					<td class="dataTableContent"><span class="greenIndicator" id="tag_<?=$warning_row['products_id']?>"><?php echo $tags_name_str; ?></span></td>
					<td class="dataTableContent"><?php echo $warning_row['low_stock_date']; ?></td>
					<td class="dataTableContent" align="right"><?php echo $product_qty_row['products_actual_quantity']; ?></td>
					<td class="dataTableContent" align="right"><?php echo $product_qty_row['products_quantity']; ?></td>
					<td class="dataTableContent" align="right"><?php echo $warning_stock; ?></td>
					<td class="dataTableContent" align="center" width="5%"><?=tep_draw_checkbox_field('low_stock_batch[]', $warning_row['products_id'], false)?></td>
				</tr>
<?
		}
?>
				<tr>
					<td colspan="10">
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td>&nbsp;<?=tep_button('Refresh', 'Refresh', '', 'onclick="javascript:window.location.href=\''.tep_href_link(basename($_SERVER['PHP_SELF']), tep_get_all_get_params(array('subaction'))).'\';"', 'inputButton')?>&nbsp;</td>
								<td align="right">&nbsp;<?=tep_submit_button('Issue PO', 'Issue Purchase Order', 'name="multi_submit_btn" onClick="return low_stock_form_check(\'create_po\')"', 'inputButton');?>&nbsp;</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10">
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_count($warning_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $trans_array['page'], TEXT_DISPLAY_NUMBER_OF_LOW_STOCK_WARNINGS); } ?>&nbsp;</td>
								<td align="right" class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_links($warning_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $trans_array['page'], tep_get_all_get_params(array('page', 'action'))); } ?>&nbsp;</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
			<script language="javascript">
				<!--
				<?	foreach ($warning_list as $status_name => $res) {
						$prd_str = count($res['prd_id']) ? implode(',', $res['prd_id']) : '';
				?>
						document.getElementById('<?=$status_name?>'+'_prd_str').value = "<?=$prd_str?>";
				<?
					}
				?>
				
				function low_stock_form_check(update_type) {
					jQuery('#subaction').val(update_type);
					document.low_stock_form.submit();
					return true;
				}
				//-->
			</script>
<?
		$low_stock_list_html = ob_get_contents();
		ob_end_clean() ;
		
		return $low_stock_list_html;
	}
}
?>