<?php

class products_payment_restrictions_rules {

    public $identity, $identity_email, $rule_info;

    public function __construct($identity, $identity_email) {
        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin email
    }

    public function show_rules_list($filename) {
        $sort = 'ASC';
        $sortingDB = 'ASC';
        
        //Sorting
        if (isset($_GET['sort']) AND $_GET['sort'] == 'ASC') {
            $sort = 'DESC';
            $sortingDB = 'ASC';
        }

        if (isset($_GET['sort']) AND $_GET['sort'] == 'DESC') {
            $sort = 'ASC';
            $sortingDB = 'DESC';
        }
        
        if (isset($_REQUEST['page'])) {
            $target = $_SERVER[ 'REQUEST_URI' ];
            $target = preg_replace('/&sort=ASC/',"",$target);
            $target = preg_replace('/&sort=DESC/',"",$target);
            $sorting = "$target&sort=$sort";
        } else {
            $target = $_SERVER[ 'REQUEST_URI' ];
            $target = preg_replace('/\?sort=ASC/',"",$target);
            $target = preg_replace('/\?sort=DESC/',"",$target);
            $sorting = "$target?sort=$sort"; 
        }
        
        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
        $rule_select_sql = "SELECT ppmr.products_id, pd.products_name, p.products_cat_path, ppmr.restriction_mode, ppmr.restriction_info, ppmr.updated_date, ppmr.changed_by
                            FROM " . TABLE_PRODUCTS_PAYMENT_METHODS_RESTRICTIONS . " AS ppmr
                            INNER JOIN " . TABLE_PRODUCTS . " AS p
                                ON ppmr.products_id = p.products_id
                            LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                ON p.products_id = pd.products_id AND (pd.language_id = 1 OR pd.language_id IS NULL)
                            WHERE ppmr.restriction_info != ''
                            ORDER BY pd.products_name " . $sortingDB. " ";

        $show_records = MAX_DISPLAY_SEARCH_RESULTS;
        if ($show_records != "ALL") {
            $rule_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $rule_select_sql, $rule_select_sql_numrows, true);
        }
        $rule_result_sql = tep_db_query($rule_select_sql);

        ob_start();
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr>
                            <td width="7%" class="reportBoxHeading"><?php echo TABLE_HEADING_PRODUCT_ID; ?></td>
                            <td width="22%" class="reportBoxHeading"><a href="<?php echo $sorting ; ?>"><b><?php echo TABLE_HEADING_PRODUCT_NAME; ?></b></a></td>
                            <td width="22%" class="reportBoxHeading"><?php echo TABLE_HEADING_CATEGORY_PATH; ?></td>
                            <td width="8%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_RESTRICTION_MODE; ?></td>
                            <td width="17%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_RESTRICTION_INFO; ?></td>
                            <td width="12%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_UPDATE_DATE; ?></td>
                            <td width="12%" align="center" class="reportBoxHeading"><?php echo TABLE_HEADING_CHANGED_BY; ?></td>
                        </tr>
                        <?php
                        $row_count = 0;
						$cat_name =  array();
                        while ($rule_row = tep_db_fetch_array($rule_result_sql)) {
                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                            $product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path(tep_get_actual_product_cat_id($rule_row['products_id'])) . '&pID=' . $rule_row['products_id'] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-size:12px; font-weight:bold;" title="'.TABLE_HEADING_PRODUCT_ID.' '.$rule_row['products_id'].'">' . $rule_row['products_id'] . '</a>';

                            // List restrictions detail info
                            $restrictions_text = '';
                            $restriction_array = explode(",", $rule_row['restriction_info']);
                            foreach ($restriction_array as $pipwave_mapper_id) {
                                if ($pipwave_mapper_id == '0') {
                                    $restrictions_text .= 'OffGamers : Store Credit';
                                } else {
                                    $pm_sql = "SELECT pm_display_name, pg_display_name FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE id = '" . (int) $pipwave_mapper_id . "'";
                                    $pm_result = tep_db_query($pm_sql);
                                    if ($pm_row = tep_db_fetch_array($pm_result)) {
                                        $restrictions_text .= $pm_row['pg_display_name'] . " : " . $pm_row['pm_display_name'];
                                    }
                                }
                                $restrictions_text .= '<br>';
                            }

                            echo '<tr height="20" class="dataTableRow" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">';
                            echo '  <td align="center" valign="top">' . $product_id_link . '</td>';
                            echo '  <td valign="top">' . $rule_row['products_name'] . '</td>';
                            echo '  <td valign="top">' . $rule_row['products_cat_path'] . '</td>';
                            echo '  <td align="center" valign="top">' . $rule_row['restriction_mode'] . '</td>';
                            echo '  <td valign="top">' . $restrictions_text . '</td>';
                            echo '  <td align="center" valign="top">' . $rule_row['updated_date'] . '</td>';
                            echo '  <td align="center" valign="top">' . $rule_row['changed_by'] . '</td>';
                            echo '</tr>';
                            ?>

                            <?php
                            $row_count++;
							unset($cat_name);
                        }
						
                        ?>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
                        <tr>
                            <td class="smallText" valign="top"><?php echo $show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($rule_result_sql) > 0 ? "1" : "0", tep_db_num_rows($rule_result_sql), tep_db_num_rows($rule_result_sql)) : $rule_split_object->display_count($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], $result_display_text) ?></td>
                            <td class="smallText" align="right"><?php echo $show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $rule_split_object->display_links($rule_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . "cont=1", 'page') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?php
        $report_section_html = ob_get_contents();
        ob_end_clean();

        return $report_section_html;
    }
}
?>