<?php
require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

class products_promotion {

    function products_promotion() {
        //
    }

    /* This method use to check the product id is single product OR static product */

    function check_single_product($pid) {
        $bundle_select_sql = " SELECT bundle_id 
						       FROM " . TABLE_PRODUCTS_BUNDLES . "
						       WHERE subproduct_id = " . (int) $pid . "
                               LIMIT 1";
        $bundle_result_sql = tep_db_query($bundle_select_sql);
        $db_row = (tep_db_num_rows($bundle_result_sql)) ? true : false;
        return $db_row;
    }

    function get_promotion_product($product_id) {
        $return_array = array();

        $product_select_sql = "	SELECT *
								FROM " . TABLE_PRODUCTS_PROMOTION . "
								WHERE products_id = " . (int) $product_id;
        $product_result_sql = tep_db_query($product_select_sql);
        if ($product_row = tep_db_fetch_array($product_result_sql)) {
            $return_array = $product_row;
        }

        return $return_array;
    }

    function get_promotion_product_description($product_id) {
        $return_array = array();

        $product_description_select_sql = "	SELECT promotion_image, promotion_image_title, language_id
											FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " 
											WHERE products_id = " . (int) $product_id;
        $product_description_result_sql = tep_db_query($product_description_select_sql);
        while ($product_description_row = tep_db_fetch_array($product_description_result_sql)) {
            $return_array[$product_description_row['language_id']]["promotion_image"] = $product_description_row['promotion_image'];
            $return_array[$product_description_row['language_id']]["promotion_image_title"] = $product_description_row['promotion_image_title'];
        }

        return $return_array;
    }

    /* This method check whether the product id exist in Product Promotion table */

    function check_product_id_exist($product_id) {
        $id_select_sql = " 	SELECT products_id 
							FROM " . TABLE_PRODUCTS_PROMOTION . " 
							WHERE products_id = " . (int) $product_id;
        $id_result_sql = tep_db_query($id_select_sql);
        $db_row = (tep_db_num_rows($id_result_sql)) ? true : false;
        return $db_row;
    }

    /* This method check whether the product id exist in Product Promotion Description table */

    function check_product_id_desc_exist($product_id, $lng_id) {
        $id_select_sql = " 	SELECT products_id 
							FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " 
							WHERE products_id = " . (int) $product_id . " AND language_id = " . (int) $lng_id;
        $id_result_sql = tep_db_query($id_select_sql);
        $db_row = (tep_db_num_rows($id_result_sql)) ? true : false;
        return $db_row;
    }

    /*     * ****************************************************************************************
     * This method process add OR edit promotion product  
     * Param: 
     * $promotion_product_data_array - Store promotion product info for Product Promotion table
     * $languages - 1 for english, 2 for chinese simplify, 3 for chinese traditional
     * $image_files - Image file to upload
     * $action - new OR edit
     * ***************************************************************************************** */

    function process_promotion_product($promotion_product_data_array, $languages, $image_files, $action) {
        global $memcache_obj, $messageStack;

        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key('BUCKET_STATIC');
        $aws_obj->set_acl('ACL_PUBLIC');
        $aws_obj->set_storage('STORAGE_STANDARD');
        $aws_obj->set_filepath('images/products/');

        $static_product_list_array = array();
        $main_product_id = $promotion_product_data_array['products_id'];

        // Insert single product OR main product into products promotion table
        if ($action == 'edit') {
            // Update Edited Promotion Product
            tep_db_perform(TABLE_PRODUCTS_PROMOTION, $promotion_product_data_array, 'update', "products_id = " . $main_product_id);
        } else {
            // Insert single product into products promotion table
            tep_db_perform(TABLE_PRODUCTS_PROMOTION, $promotion_product_data_array);
        }

        // Parse static product string to array
        if (tep_not_null($_POST['static_product_list'])) {
            $static_product_list_array = explode(',', $_POST['static_product_list']);
        }

        // Insert ALL static product into products promotion table
        if (tep_not_empty($static_product_list_array)) {
            foreach ($static_product_list_array as $static_product_id) {
                if ((int) $static_product_id) {
                    // Check Selected Static Product Exist in product promotion table
                    if ($this->check_product_id_exist($static_product_id)) {
                        $promotion_product_data_array['products_id'] = $static_product_id;
                        tep_db_perform(TABLE_PRODUCTS_PROMOTION, $promotion_product_data_array, 'update', "products_id = " . $static_product_id);
                    } else {
                        $promotion_product_data_array['products_id'] = $static_product_id;
                        tep_db_perform(TABLE_PRODUCTS_PROMOTION, $promotion_product_data_array);
                    }
                }
            }
        }

        // Loop Language Tabs
        for ($i = 0; $i < sizeof($languages); $i++) {
            $product_image = '';
            $lng_id = $languages[$i]['id'];

            $promotion_product_description_data_array = array('promotion_image_title' => tep_db_prepare_input($_POST['promotions_image_title'][$lng_id]),
                'products_id' => (int) $main_product_id,
                'language_id' => (int) $lng_id
            );

            // Having Image File to Upload
            if (tep_not_null($image_files[$i])) {
                $filename = strtolower($image_files[$i]);
                $exts = split_dep("[/\\.]", $filename);
                $n = count($exts) - 1;
                $exts = $exts[$n];
                
                $promo_image_basename = ($lng_id > 1) ? 'promo_' . $promotion_product_description_data_array['products_id'] . '_' . $lng_id . '_' . time() : 'promo_' . $promotion_product_description_data_array['products_id'] . '_' . time();
                $product_image = $promo_image_basename . '.' . $exts;
                $product_image_ext = array('jpg', 'bmp', 'gif');

                if ($action == 'edit') {
                    // Remove Old Main Product Image
                    if ($aws_obj->is_aws_s3_enabled()) {
                        // Remove from S3
                        $aws_product_image = '';
                        $promo_products_description_image_select_sql = "SELECT promotion_image FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " 
					        		WHERE products_id = '" . (int) $main_product_id . "'
                                                                AND language_id = '" . (int) $lng_id . "'";

                        $promo_products_description_image_select_sql = tep_db_query($promo_products_description_image_select_sql);
                        $products_description_image_row = tep_db_fetch_array($promo_products_description_image_select_sql);

                        if (tep_not_null($products_description_image_row['promotion_image'])) {
                            $aws_product_image = $products_description_image_row['promotion_image'];
                        }

                        if ($aws_product_image != '') {
                            $aws_status = $aws_obj->delete_file($aws_product_image);

                            if ($aws_status >= 300) {
                                $messageStack->add_session('Cannot delete previous image please try again', 'error');
                                tep_redirect(tep_href_link(FILENAME_PRODUCT_PROMOTIONS, tep_get_all_get_params(array('page', 'action', 'id')) . 'page=' . $_GET['page'] . '&action=edit_product_promotion&id=' . $main_product_id));
                            }
                        }
                    } else {
                        // Remove from local
                        if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $product_image)) {
                            $oldPermission = @umask(0);
                            @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $product_image, 0777);
                            @umask($oldPermission);
                            @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $product_image);
                        }
                    }
                }

                // Upload Main Product Promotion Image - Upload to S3 OR local handle by "upload" class
                $product_promotion_image = new upload('products_image_' . $lng_id, '', '', $product_image_ext);
                $product_promotion_image->set_filename($product_image);
                $product_promotion_image->set_destination(DIR_FS_CATALOG_IMAGES . 'products/');
                $product_promotion_image->set_amazon_api_transfer(array(
                    'bucket' => 'BUCKET_STATIC',
                    'filepath' => 'images/products/',
                    'storage' => 'STORAGE_STANDARD',
                    'acl' => 'ACL_PUBLIC',
                    'headers' => array(
                        'Cache-Control' => 'max-age=' . $aws_obj->default_cache_control_max_age
                    )
                ));

                if ($product_promotion_image->parse() && $product_promotion_image->save($promo_image_basename, 'small')) {
                    $promotion_product_description_data_array['promotion_image'] = tep_db_prepare_input($product_image);

                    // Insert OR Update Single Product from products promotion description table - with Image
                    if ($action == 'add') {
                        tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                    } else {
                        tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array, 'update', "products_id = " . $main_product_id . ' AND language_id = ' . $lng_id);
                    }

                    // Loop ALL static product
                    if (tep_not_empty($static_product_list_array)) {
                        foreach ($static_product_list_array as $static_product_id) {
                            if ((int) $static_product_id) {
                                $static_product_image_name = $product_image;
                                $image_info_array = $aws_obj->get_image_info($product_image);

                                if ($action == 'edit') {
                                    // Remove Old Static Product Image
                                    if ($aws_obj->is_aws_s3_enabled()) {
                                        // Remove from S3
                                        if ($aws_obj->is_image_exists($static_product_image_name, 'images/products/', 'BUCKET_STATIC')) {
                                            $aws_obj->delete_file($static_product_image_name, 'images/products/', 'BUCKET_STATIC');
                                        }
                                    } else {
                                        // Remove from local
                                        if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name)) {
                                            $oldPermission = @umask(0);
                                            @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name, 0777);
                                            @umask($oldPermission);
                                            @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name);
                                        }
                                    }
                                }

                                // Copy Main Product Image for each static product
                                if (tep_not_null($image_info_array)) {
                                    // Copy S3 Image
                                    $bucket_name = $aws_obj->aws_bucket;
                                    $source_arr = array('bucket' => $bucket_name, 'filename' => $aws_obj->aws_filepath . $product_image);
                                    $dest_arr = array('bucket' => $bucket_name, 'filename' => $aws_obj->aws_filepath . $static_product_image_name);
                                    $copy_opt_arr = array('storage' => $aws_obj->aws_storage, 'acl' => $aws_obj->aws_acl);

                                    $clon_result = $aws_obj->s3_api(array('method' => 'copy_object', $source_arr, $dest_arr, $copy_opt_arr));
                                } else if (tep_not_null($product_image) && file_exists(DIR_FS_CATALOG_IMAGES . 'products/' . $product_image)) {
                                    // Copy Local Image
                                    $file = DIR_FS_CATALOG_IMAGES . 'products/' . $product_image;
                                    $newfile = DIR_FS_CATALOG_IMAGES . 'products/' . $static_product_image_name;
                                    copy($file, $newfile);
                                }

                                if ($action == 'add') {
                                    // Insert Static Product into products promotion description table - with Image
                                    $promotion_product_description_data_array['products_id'] = $static_product_id;
                                    $promotion_product_description_data_array['promotion_image'] = tep_db_prepare_input($static_product_image_name);
                                    tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                                } else {
                                    // Check Selected Static Product Exist in product promotion description table
                                    $promotion_product_description_data_array['products_id'] = $static_product_id;
                                    $promotion_product_description_data_array['promotion_image'] = tep_db_prepare_input($static_product_image_name);

                                    if ($this->check_product_id_desc_exist($static_product_id, $lng_id)) {
                                        // Update Static Product
                                        tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array, 'update', "products_id = " . $promotion_product_description_data_array['products_id'] . ' AND language_id = ' . $lng_id);
                                    } else {
                                        // Insert Static Product
                                        tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                                    }
                                }
                                $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $static_product_id;
                                $memcache_obj->delete($cache_key, 0);
                            }
                        }
                    }
                }
            } else {
                // No Image File to Upload
                if ($action == 'add') {
                    // Insert single product into products_promotion_description table - Without Image
                    tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                } else {
                    // Update Edited Promotion Product Description - Without Image
                    tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array, 'update', 'products_id = ' . $main_product_id . ' AND language_id = ' . $lng_id);
                }

                // Loop ALL static product
                if (tep_not_empty($static_product_list_array)) {
                    foreach ($static_product_list_array as $static_product_id) {
                        if ((int) $static_product_id) {
                            if ($action == 'add') {
                                // Add each static product into Product Promotion Description table 
                                $promotion_product_description_data_array['products_id'] = $static_product_id;
                                tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                            } else {
                                // Get Single Product's Promotion Image File Name
                                $single_product_description_array = $this->get_promotion_product_description($main_product_id);
                                $single_product_image_array = $aws_obj->get_image_info($single_product_description_array[$lng_id]['promotion_image']);

                                // Get Static Product's Promotion Image File Name
                                $image_ext = explode('.', $single_product_description_array[$lng_id]['promotion_image']);
                                $static_product_image_name = $product_image = 'promo_' . $static_product_id . '_' . time() . '_' . $lng_id . '.' . $image_ext[1];
                                $promotion_product_description_data_array['promotion_image'] = $static_product_image_name;

                                // Check Whether Single Product Image Exist In S3
                                if (tep_not_null($single_product_image_array)) {
                                    // Remove from S3
                                    if ($aws_obj->is_image_exists($static_product_image_name, 'images/products/', 'BUCKET_STATIC')) {
                                        $aws_obj->delete_file($static_product_image_name, 'images/products/', 'BUCKET_STATIC');
                                    }

                                    // Copy single product image file from S3
                                    $bucket_name = $aws_obj->aws_bucket;
                                    $source_arr = array('bucket' => $bucket_name, 'filename' => $aws_obj->aws_filepath . $single_product_description_array[$lng_id]['promotion_image']);
                                    $dest_arr = array('bucket' => $bucket_name, 'filename' => $aws_obj->aws_filepath . $static_product_image_name);
                                    $copy_opt_arr = array('storage' => $aws_obj->aws_storage, 'acl' => $aws_obj->aws_acl);

                                    $clon_result = $aws_obj->s3_api(array('method' => 'copy_object', $source_arr, $dest_arr, $copy_opt_arr));

                                    // Check Whether Single Product Image Exist In Local
                                } else if (tep_not_null($single_product_description_array[$lng_id]['promotion_image']) && file_exists(DIR_FS_CATALOG_IMAGES . 'products/' . $single_product_description_array[$lng_id]['promotion_image'])) {
                                    // Remove from local
                                    if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name)) {
                                        $oldPermission = @umask(0);
                                        @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name, 0777);
                                        @umask($oldPermission);
                                        @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name);
                                    }

                                    // Copy & Rename from Local Image
                                    $file = DIR_FS_CATALOG_IMAGES . 'products/' . $single_product_description_array[$lng_id]['promotion_image'];
                                    $newfile = DIR_FS_CATALOG_IMAGES . 'products/' . $static_product_image_name;
                                    copy($file, $newfile);

                                    // Single Image File Not Exist, Remove Static Product's Image
                                } else {
                                    if ($aws_obj->is_aws_s3_enabled()) {
                                        // Remove from S3
                                        if ($aws_obj->is_image_exists($static_product_image_name, 'images/products/', 'BUCKET_STATIC')) {
                                            $aws_obj->delete_file($static_product_image_name, 'images/products/', 'BUCKET_STATIC');
                                        }
                                    } else {
                                        // Remove from local
                                        if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name)) {
                                            $oldPermission = @umask(0);
                                            @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name, 0777);
                                            @umask($oldPermission);
                                            @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $static_product_image_name);
                                        }
                                    }
                                }

                                // Check Selected Static Product Exist in product promotion description table
                                $promotion_product_description_data_array['products_id'] = $static_product_id;
                                if ($this->check_product_id_desc_exist($static_product_id, $lng_id)) {
                                    // Update Static Product
                                    tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array, 'update', "products_id = " . $promotion_product_description_data_array['products_id'] . ' AND language_id = ' . $lng_id);
                                } else {
                                    // Insert Static Product
                                    tep_db_perform(TABLE_PRODUCTS_PROMOTION_DESCRIPTION, $promotion_product_description_data_array);
                                }
                            }
                            $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $static_product_id;
                            $memcache_obj->delete($cache_key, 0);
                        }
                    }
                }
            }
        }
        unset($static_product_list_array, $promotion_product_data_array, $promotion_product_description_data_array);
        return "success";
    }

    function addEditProduct($action) {
        global $memcache_obj;

        $image_files = array();
        $products_id = (int) $_POST['product_id'];
        $start_date_time = tep_db_prepare_input($_POST['start_date']);
        $end_date_time = tep_db_prepare_input($_POST['end_date']);
        $only_promo_box = ($_POST['only_promo_box'] == 'on') ? 1 : 0;
        $limited_stock = ($_POST['limited_stock'] == 'on') ? 1 : 0;
        $limited_stock_quatity = (int) $_POST['limited_stock_quatity'];

        // Validate Product ID
        if (tep_not_null($products_id)) {
            // Check Product ID Exist In Category
            $categories_select_sql = "	SELECT products_id 
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = " . $products_id;
            $categories_result_sql = tep_db_query($categories_select_sql);
            if (tep_db_num_rows($categories_result_sql)) {
                // Check Product ID Exist In Product Promotion
                if ($this->check_product_id_exist($products_id)) {
                    if ($action == 'add') {
                        return TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_EXIST;
                    }
                } else {
                    if ($action == 'edit') {
                        return TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_MISSING;
                    }
                }
            } else {
                return TEXT_PRODUCT_PROMOTION_ERROR_INVALID_PRODUCT_ID;
            }
        } else {
            return TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_EMPTY;
        }

        // Validate Start Date
        if (tep_not_null($start_date_time)) {
            // Validate End Date
            if (tep_not_null($end_date_time)) {
                $arr_end_date = explode(' ', $end_date_time);

                if (!tep_not_null($arr_end_date[1])) {
                    // Add time
                    $end_date_time = $arr_end_date[0] . ' 23:59:59';
                } else {
                    if (strlen($arr_end_date[1]) == 5) {
                        // Add Seconds
                        $end_date_time .= ':59';
                    }
                }

                // Validate Date Range
                if ($start_date_time > $end_date_time) {
                    return TEXT_PRODUCT_PROMOTION_ERROR_INVALID_DATE_RANGE;
                }
            }

            if (!tep_not_null($end_date_time)) {
                $end_date_time = '0000-00-00 00:00:00';
            }
        } else {
            return TEXT_PRODUCT_PROMOTION_ERROR_START_DATE_EMPTY;
        }

        // Validate Limited Stock Quantity
        if (tep_not_null($limited_stock_quatity)) {
            if (strlen(abs($limited_stock_quatity)) > 5) {
                return TEXT_PRODUCT_PROMOTION_ERROR_LIMITED_QUANTITY_OUT_OF_RANGE;
            }
        }

        // validate image file type
        $languages = tep_get_languages();

        // Insert into products_promotion table
        $promotion_product_data_array = array('products_id' => $products_id,
            'promotion_start_date' => $start_date_time,
            'promotion_end_date' => $end_date_time,
            'promotion_box_only' => $only_promo_box,
            'promotion_selling_status' => (int) $_POST['promotion_status'],
            'promotion_limited_stock' => $limited_stock,
            'promotion_limited_stock_qty' => $limited_stock_quatity
        );

        for ($i = 0; $i < sizeof($languages); $i++) {
            $lng_id = $languages[$i]['id'];
            $image_files[$i] = $_FILES['products_image_' . $lng_id]['name'];
        }

        $return_val = $this->process_promotion_product($promotion_product_data_array, $languages, $image_files, $action);
        $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $products_id;
        $memcache_obj->delete($cache_key, 0);

        return $return_val;
    }

    function productPromotion($action, $result, $id) {

        $languages = tep_get_languages();
        $promotion_status_array = array(0 => false, 1 => false, 2 => false);

        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key('BUCKET_STATIC');
        $aws_obj->set_filepath('images/products/');

        if ($action == 'new') {
            $form_action = 'add_new_product';
            $form_submit_btn_label = BUTTON_INSERT;
            $page_title = HEADING_PRODUCT_PROMOTION_NEW_PROMOTION;
            $start_date = $_SESSION['promotion_lists_param']['start_date'];
            $end_date = $_SESSION['promotion_lists_param']['end_date'];
            $promotion_box_only = $_SESSION['promotion_lists_param']['only_promo_box'];
            $promotion_limited_stock = $_SESSION['promotion_lists_param']['limited_stock'];
            $promotion_limited_stock_qty = $_SESSION['promotion_lists_param']['limited_stock_quatity'];
            if (tep_not_null($_SESSION['promotion_lists_param']['promotion_status'])) {
                $promotion_status_array[$_SESSION['promotion_lists_param']['promotion_status']] = true;
            } else {
                $promotion_status_array[0] = true;
            }
        } else if ($action == 'edit') {
            $product_info_arr = $this->get_promotion_product($id);
            $product_description_info_arr = $this->get_promotion_product_description($id);

            $form_action = 'update_product';
            $form_submit_btn_label = BUTTON_UPDATE;
            $page_title = HEADING_PRODUCT_PROMOTION_EDIT_PROMOTION;
            $start_date = $product_info_arr["promotion_start_date"] != '0000-00-00 00:00:00' ? substr($product_info_arr["promotion_start_date"], 0, 16) : '';
            $end_date = $product_info_arr["promotion_end_date"] != '0000-00-00 00:00:00' ? substr($product_info_arr["promotion_end_date"], 0, 16) : '';
            $promotion_box_only = $product_info_arr["promotion_box_only"];
            $promotion_limited_stock = $product_info_arr["promotion_limited_stock"];
            $promotion_limited_stock_qty = $product_info_arr["promotion_limited_stock_qty"];
            $promotion_status_array[$product_info_arr["promotion_selling_status"]] = true;

            $category_id = tep_get_actual_product_cat_id($id);
            $product_name = strip_tags(tep_get_products_name($id));
            $category_name = strip_tags(tep_output_generated_category_path_sq($category_id));
        }

        ob_start();
        ?>
        <script type="text/javascript">
            function getReturnedValue(received_val) {
                // assign openDGDialog return value to product_id field
                document.getElementById('product_id').value = received_val;
            }

            function getReturnedStaticProduct(received_val) {
                // assign openDGDialog return value to static_product field by join all selected static product to string
                document.promotion_form.static_product_list.value = received_val.join();
            }
        </script>

        <div><iframe width="188px" height="166px" name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
            <tr>
                <td>
        <?
        echo tep_draw_form('promotion_form', FILENAME_PRODUCT_PROMOTIONS, 'action=' . $form_action . '&page=' . $_GET['page'], 'post', 'enctype="multipart/form-data"');
        ?>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="pageHeading" valign="top"><?= $page_title ?></td>
                            <td><?= tep_draw_separator('pixel_trans.gif', '2', '40') ?></td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
        <?
        if ($action == 'new') {
            echo '<td width="160px">' . LABEL_PRODUCT_PROMOTION_PRODUCT_ID . '</td>' .
            '<td class="main" align="left">' .
            tep_draw_input_field('product_id', $_SESSION['promotion_lists_param']['product_id'], ' id="product_id" size="15" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"') .
            '&nbsp;<a href="javascript:openDGDialog(\'' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'fname=' . FILENAME_ORDERS) . '\', 600, 250, \'\');">' . TEXT_PRODUCT_PROMOTION_LIST . '</a>&nbsp;&nbsp;&nbsp;<span class="fieldRequired">* ' . TEXT_PRODUCT_PROMOTION_REQUIRED . '</span>' .
            '&nbsp;&nbsp;<a style="text-decoration:none" href="javascript:show_static_product(\'new\');">' . tep_button(BUTTON_PRODUCT_PROMOTION_SHOW_STATIC_PRODUCT, BUTTON_PRODUCT_PROMOTION_SHOW_STATIC_PRODUCT, '', 'onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true) . '</a>' .
            '</td>';
        } else {
            // Get Product Path
            echo '<td class="main" align="left">' . $category_name . ' > ' . $product_name . '&nbsp;(' . $id . ')';

            if ($this->check_single_product($id)) {
                echo '&nbsp;&nbsp;<a style="text-decoration:none" href="javascript:show_static_product(\'edit\');">' . tep_button(BUTTON_PRODUCT_PROMOTION_SHOW_STATIC_PRODUCT, BUTTON_PRODUCT_PROMOTION_SHOW_STATIC_PRODUCT, '', 'onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true) . '</a>';
            }

            echo '</td><td>' . tep_draw_hidden_field('product_id', $id, ' id="product_id"') . '</td>';
        }
        ?>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <div id="languages_tab">
                                    <ul>
        <?
        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
            echo '<li id="languages_li_' . $i . '"><a href="#languages_tab_' . $i . '"><span>' . $languages[$i]['name'] . '</span></a></li>';
        }
        ?>
                                    </ul>
                                        <?
                                        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                                            $lng_id = $languages[$i]['id'];
                                            $promo_image = '';
                                            $promo_image_title = array();

                                            if ($action == 'edit') {
                                                $promo_image_title[$lng_id] = $product_description_info_arr[$lng_id]['promotion_image_title'];
                                                $image_info_array = $aws_obj->get_image_info($product_description_info_arr[$lng_id]['promotion_image']);

                                                if (tep_not_null($image_info_array)) {
                                                    $promo_image = $image_info_array['src'];
                                                } else if (tep_not_null($product_description_info_arr[$lng_id]['promotion_image']) && file_exists(DIR_FS_CATALOG_IMAGES . 'products/' . $product_description_info_arr[$lng_id]['promotion_image'])) {
                                                    $promo_image = DIR_WS_CATALOG_IMAGES . 'products/' . $product_description_info_arr[$lng_id]['promotion_image'];
                                                }

                                                if (tep_not_null($promo_image)) {
                                                    $promo_image = tep_image($promo_image, '', '60', '30');
                                                } else {
                                                    $promo_image = '';
                                                }

                                                unset($image_info_array);
                                            } else {
                                                $promo_image_title[$lng_id] = $_SESSION['promotion_lists_param']['promotions_image_title' . $lng_id];
                                            }
                                            ?>
                                        <div id="languages_tab_<?= $i ?>" class="languages_tab">
                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td colspan="3"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="1%"></td>
                                                    <td width="20%" class="main"><?= LABEL_PRODUCT_PROMOTION_IMAGE ?></td>
                                                    <td class="main" valign="top"><?= tep_draw_file_field('products_image_' . $lng_id, 'id="products_image" size="40"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="1%"></td>
                                                    <td width="18%" valign="top"></td>
                                                    <td class="main" valign="top" align="left"><?= $promo_image ?></td>
                                                </tr>
                                                <tr>
                                                    <td width="1%"></td>
                                                    <td width="20%" valign="top" class="main"><?= LABEL_PRODUCT_PROMOTION_IMAGE_TITLE ?></td>
                                                    <td class="main"><?= tep_draw_input_field('promotions_image_title[' . $lng_id . ']', $promo_image_title[$lng_id], 'id="promotions_image_title[' . $lng_id . ']" maxlength="64"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                            </table>
                                        </div>
            <?
        }
        ?>
                                </div>
                                <script language="javascript">
                                    jQuery("#languages_tab > ul").tabs();
                                    jQuery('.languages_tab').css({
                                        border: '1px solid #C9C9C9'
                                    });
                                </script>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <table>
                                    <tr>
                                        <td class="main" width="143px"><?= LABEL_PRODUCT_PROMOTION_START_DATE ?></td>
                                        <td class="main"><?= tep_draw_input_field('start_date', $start_date, 'id="start_date" size="16" maxlength="16" onblur=""') . '<a href="javascript:void(0)" onclick="gfPop.fPopCalendar(document.promotion_form.start_date);" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                        <td class="main">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                                        <td class="main" width="120px"><?= LABEL_PRODUCT_PROMOTION_END_DATE ?></td>
                                        <td class="main"><?= tep_draw_input_field('end_date', $end_date, 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.promotion_form.end_date); }"') . '<a href="javascript:void(0)" onclick="gfPop.fPopCalendar(document.promotion_form.end_date);" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                    </tr>
                                    <tr>
                                        <td width="143px"></td>
                                        <td class="smallText"><span class="fieldRequired">* <?= TEXT_PRODUCT_PROMOTION_REQUIRED ?></span></td>
                                        <td></td>
                                        <td width="120px"></td>
                                        <td class="smallText" align='left'><span class="fieldRequired">* <?= TEXT_PRODUCT_PROMOTION_OPTIONAL ?></span></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <table>
                                    <tr>
                                        <td class="main" width="143px"><?= TEXT_PRODUCT_PROMOTION_BOX ?></td>
                                        <td class="main"><?= tep_draw_checkbox_field('only_promo_box', '', $promotion_box_only) ?></td>
                                        <td class="main"><?= LABEL_PRODUCT_PROMOTION_ONLY_IN_PROMO_BOX ?></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <table>
                                        </td>
                                        </tr>
                                    </table>
                            </td>
                        </tr>
                        <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td class="main" width="143px"><?= TEXT_PRODUCT_PROMOTION_STATUS ?></td>
                                                    <td class="main"><?= tep_draw_checkbox_field('limited_stock', '', $promotion_limited_stock, '', 'id="limited_stock"') ?></td>
                                                    <td class="main"><?= LABEL_PRODUCT_PROMOTION_LIMITED_STOCK ?></td>
                                                    <td class="main">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?= TEXT_PRODUCT_PROMOTION_LIMITED_STOCK_QUANTITY ?></td>
                                                    <td class="main"><?= tep_draw_input_field('limited_stock_quatity', $promotion_limited_stock_qty, 'size="5" maxlength="6" id="limited_stock_quatity"', false, 'text', true) ?></td>
                                                    <td class="main"><?= TEXT_PRODUCT_PROMOTION_LIMITED_STOCK_COMMENT ?></td>
                                                </tr>
                                                <table>
                                                    </td>
                                                    </tr>
                                                </table>
                                        </td>
                                    </tr>
                                    <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
                                    <tr>
                                        <td>
                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td>
                                                        <table>
                                                            <tr>
                                                                <td class="main" width="143px"></td>
                                                                <td class="main">
                                                                    <fieldset class="selectedFieldSet" style="width:290">
                                                                        <legend align=center class=SectionHead><?= TEXT_PRODUCT_PROMOTION_LOW_PRIORITY_THAN_LIMITED_STOCK ?></legend>
                                                                        <table border="0" cellspacing="0" cellpadding="0">
                                                                            <tr>
                                                                                <td class="smallText" valign="top"><?= tep_draw_radio_field(promotion_status, '0', $promotion_status_array[0], '') ?></td>
                                                                                <td class="smallText"><?= LABEL_PRODUCT_PROMOTION_NO_STATUS ?></td>
                                                                                <td>&nbsp;&nbsp;&nbsp;</td>
                                                                                <td class="smallText" valign="top"><?= tep_draw_radio_field(promotion_status, '1', $promotion_status_array[1], '') ?></td>
                                                                                <td class="smallText"><?= LABEL_PRODUCT_PROMOTION_FAST_SELLING ?></td>
                                                                                <td>&nbsp;&nbsp;&nbsp;</td>
                                                                                <td class="smallText" valign="top"><?= tep_draw_radio_field(promotion_status, '2', $promotion_status_array[2], '') ?></td>
                                                                                <td class="smallText"><?= LABEL_PRODUCT_PROMOTION_PRICE_SLASH; ?></td>
                                                                            </tr>
                                                                        </table>
                                                                    </fieldset>
                                                                </td>
                                                            </tr>
                                                            <tr><td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
                                                            <tr><td colspan="6"></td></tr>
                                                            <tr><td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <input type="hidden" id="static_product_list" name="static_product_list" value="">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2" class="pageHeading" align="right">
        <?
        echo '<a style="text-decoration:none" href="javascript:validate_form();">' . tep_button($form_submit_btn_label, $form_submit_btn_label, '', 'onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true) . '</a>' .
        '&nbsp;' .
        '<a style="text-decoration:none" href="' . tep_href_link(FILENAME_PRODUCT_PROMOTIONS, 'page=' . $_GET['page'] . '&action=""') . '">' . tep_button(BUTTON_CANCEL, BUTTON_CANCEL, '', 'onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true) . '</a>';
        ?>
                                                    </td>
                                                </tr>
                                            </table>
                                            </form>
                                            <script language="javascript">
                                                /* This function display window popup for static product list */
                                                function show_static_product(sel_action) {
                                                    if (jQuery('#product_id').val() == null || jQuery('#product_id').val() == "") {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_PRODUCT_ID_MISSING ?>');
                                                    } else {
                                                        var display_html = '<div id="methods_div" style=\"width: 700px; height: 250px; overflow-y: scroll;\">';
                                                        display_html += '	<div id="payment_method_list">Loading...</div>';
                                                        display_html += '</div>';

                                                        jQuery.ajax({
                                                            type: "post",
                                                            url: '<?= FILENAME_PRODUCT_PROMOTIONS ?>',
                                                            data: 'action=get_static_products&id=' + jQuery('#product_id').val() + '&sid=' + jQuery('#static_product_list').val() + '&sel_action=' + sel_action,
                                                            dataType: 'html',
                                                            success: function (result) {
                                                                jQuery("#payment_method_list").html(result);
                                                            }
                                                        });

                                                        jquery_confirm_box(display_html, 2, 0, '<?= TEXT_PRODUCT_PROMOTION_TITLE ?>', 0, '800');

                                                        jQuery("#jconfirm_submit").click(function () {
                                                            var selected_methods = '';
                                                            jQuery("#static_product:checked").each(function () {
                                                                if (selected_methods == '') {
                                                                    selected_methods += jQuery(this).val();
                                                                } else {
                                                                    selected_methods += ',' + jQuery(this).val();
                                                                }
                                                            });
                                                            document.promotion_form.static_product_list.value = selected_methods;
                                                        });
                                                    }
                                                }

                                                function validate_form() {
                                                    var param = '';
                                                    var path = "<?= tep_href_link(FILENAME_PRODUCT_PROMOTIONS) ?>";

                                                    // Action 1 - New Promotion Product
                                                    // Action 2 - Edit Promotion Product
                                                    if (jQuery('#product_id').val() == null || jQuery('#product_id').val() == "") {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_PRODUCT_ID_MISSING ?>');
                                                    } else if (jQuery('#start_date').val() == null || jQuery('#start_date').val() == "") {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_START_DATE_MISSING ?>');
                                                    } else if ((jQuery('#start_date').val() != null && jQuery('#end_date').val() != "") && jQuery('#start_date').val() > jQuery('#end_date').val()) {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_INVALID_DATE_RANGE ?>');
                                                    } else if ((jQuery('#limited_stock_quatity').val() != null && jQuery('#limited_stock_quatity').val() != "") && jQuery('#limited_stock_quatity').val().replace("-", '').length > 5) {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_LIMITED_QUANTITY_OUT_OF_RANGE ?>');
                                                    } else if (jQuery('#limited_stock').attr('checked') == true && (jQuery('#limited_stock_quatity').val() == null || jQuery('#limited_stock_quatity').val() == "")) {
                                                        alert('<?= TEXT_PRODUCT_PROMOTION_ERROR_JS_LIMITED_QUANTITY_MISSING ?>');
                                                    } else {
                                                        document.promotion_form.submit();
                                                    }
                                                }
                                            </script>
                                        </td>
                                    </tr>
                                </table>
        <?
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    function menuListing() {
        unset($_SESSION['promotion_lists_param']);

        $listing_html = '';
        $listing_arr = array();

        $proc_id_select_sql = "	SELECT products_id, promotion_start_date, promotion_end_date 
								FROM " . TABLE_PRODUCTS_PROMOTION; 
        $products_split = new splitPageResults($_GET['page'], MAX_DISPLAY_PRODUCT_PROMOTION_SEARCH_RESULTS_PROMO_PRODUCT, $proc_id_select_sql, $product_promotion_numrows, true);
        $proc_id_result_sql = tep_db_query($proc_id_select_sql);

        while ($proc_id_rows = tep_db_fetch_array($proc_id_result_sql)) {
            $category_id = strip_tags(tep_get_actual_product_cat_id($proc_id_rows['products_id']));
            $product_name = strip_tags(tep_get_products_name($proc_id_rows['products_id']));
            $product_status = tep_get_product_status($proc_id_rows['products_id']);
            if ($product_status != '1')
                $product_name = '<span class="redIndicator">' . $product_name . '</span>';
            $category_name = strip_tags(tep_output_generated_category_path_sq($category_id));
            $row_arr = array('product_name' => $product_name,
                'id' => $proc_id_rows['products_id'],
                'category_name' => $category_name,
                'promotion_start_date' => $proc_id_rows['promotion_start_date'],
                'promotion_end_date' => $proc_id_rows['promotion_end_date']);
            $listing_arr[] = $row_arr;
        }

        sort($listing_arr);
        ob_start();
        ?>
                                <script language="javascript">
                                    function cleanup_confirmation(bthObj) {
                                        var box_content = '<br />Are you sure you want to delete all expired promotion products?<br />';

                                        jquery_confirm_box(box_content, 2, 0, '');
                                        jQuery('#jconfirm_submit').click(function () {
                                            bthObj.form.submit();
                                        });

                                        jQuery('#jconfirm_cancel').click(function () {
                                            return false;
                                        });

                                        return true;
                                    }

                                    function deleteentry(s, id) {
                                        var answer = confirm('<?= TEXT_PRODUCT_PROMOTION_JS_DELETE_CONFIRMATION ?>' + (trim_str(s) != '' ? "<" + s + "> " : '') + ' ?');
                                        if (answer != 0) {
                                            jQuery.get("?action=delete&id=" + id, function (data) {
                                                if (data == "success")
                                                    jQuery('#row-' + id).fadeOut('slow');
                                                location.reload(true);
                                            });
                                        }
                                    }
                                </script>
        <?= tep_draw_form('menu_listing', FILENAME_PRODUCT_PROMOTIONS . '?page=' . $_GET['page'] . '', 'post', ''); ?>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td>
                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td class="pageHeading" valign="top"><?= HEADING_PRODUCT_PROMOTION_TITLE ?></td>
                                                    <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td valign="top"><?= tep_button(BUTTON_PRODUCT_PROMOTION_NEW_PROMO, BUTTON_PRODUCT_PROMOTION_NEW_PROMO, tep_href_link(FILENAME_PRODUCT_PROMOTIONS, 'page=' . $_GET['page'] . '&action=new_product_promotion'), 'onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'"', 'inputButton', true); ?></td>
                                                    <td align="right"><input type="submit" name="cleanup_expired_promotion" value="<?= BUTTON_PRODUCT_PROMOTION_CLEANUP ?>" class="inputButton" onClick="return cleanup_confirmation(this);"></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr><td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td></tr>
                                    <tr>
                                        <td valign="top">
                                            <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                <tr>
                                                    <td class="reportBoxHeading"><?= TABLE_HEADING_PRODUCT_PROMOTION_PRODUCT ?></td>
                                                    <td class="reportBoxHeading"><?= TABLE_HEADING_PRODUCT_PROMOTION_CATEGORY ?></td>
                                                    <td class="reportBoxHeading"><?= TABLE_HEADING_PRODUCT_PROMOTION_START_DATE ?></td>
                                                    <td class="reportBoxHeading"><?= TABLE_HEADING_PRODUCT_PROMOTION_END_DATE ?></td>
                                                    <td class="reportBoxHeading" width="5%" align=center><?= TABLE_HEADING_PRODUCT_PROMOTION_ACTION ?></td>
                                                </tr>
        <?
        if (tep_not_null($listing_arr)) {
            $entryCount = 0;

            foreach ($listing_arr as $rows) {
                $entryCount++;
                $tr_class = ($entryCount % 2 == 0) ? "reportListingOdd" : "reportListingEven";
                $expired_style = ($rows['promotion_end_date'] != '0000-00-00 00:00:00' && $rows['promotion_end_date'] < date("Y-m-d H:i:s", time())) ? 'color:red;' : '';
                ?>
                                                        <tr id="row-<?= $rows['id'] ?>" class="<?= $tr_class ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $tr_class ?>')" onclick="rowClicked(this, '<?= $tr_class ?>')">
                                                            <td class="reportRecords" valign="top"><?= $rows['product_name'] . ' (' . $rows['id'] . ')' ?></td>
                                                            <td class="reportRecords" valign="top"><?= $rows['category_name'] ?></td>
                                                            <td class="reportRecords" valign="top"><?= $rows['promotion_start_date'] ?></td>
                                                            <td class="reportRecords" valign="top" style="<?= $expired_style ?>"><?= $rows['promotion_end_date'] ?></td>
                                                            <td class="reportRecords" valign="top" align=center>
                                                                <a href="<?= tep_href_link(FILENAME_PRODUCT_PROMOTIONS, 'page=' . $_GET['page'] . '&action=edit_product_promotion' . '&id=' . $rows['id']) ?>"><img src="images/icons/edit.gif" border="0" alt="Edit" title=" Edit " align="top"></a>
                                                                <a href="javascript:deleteentry('<?php echo htmlentities(addslashes(strip_tags($rows['product_name'])), ENT_QUOTES); ?>', '<?php echo $rows['id']; ?>')"><img src="images/icons/delete.gif" border="0" alt="Delete" title=" Delete " align="top"></a>
                                                            </td>
                                                        </tr>
                <?
            }
        } else {
            ?>
                                                    <tr class="reportListingEven">
                                                        <td class="reportRecords" align="center" colspan="5"><i><?= TEXT_PRODUCT_PROMOTION_EMPTY ?></i></td>
                                                    </tr>
            <?
        }
        ?>
                                                <tr>
                                                    <td colspan="5">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td class="smallText" valign="top"><?= $products_split->display_count($product_promotion_numrows, MAX_DISPLAY_PRODUCT_PROMOTION_SEARCH_RESULTS_PROMO_PRODUCT, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS) ?></td>
                                                                <td class="smallText" align="right"><?= $products_split->display_links($product_promotion_numrows, MAX_DISPLAY_PRODUCT_PROMOTION_SEARCH_RESULTS_PROMO_PRODUCT, MAX_DISPLAY_PAGE_LINKS, $_GET['page']) ?>&nbsp;</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                </form>
        <?
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

    function delExpProductPromotion() {
        global $memcache_obj;

        $aws_obj = new ogm_amazon_ws();

        $id_select_sql = " 	SELECT products_id
							FROM products_promotion 
							WHERE promotion_end_date < NOW()
							AND promotion_end_date != '0000-00-00 00:00:00'";
        $id_result_sql = tep_db_query($id_select_sql);
        while ($id_row = tep_db_fetch_array($id_result_sql)) {

            // Remove Expired Product Promotion Images
            $product_promotion_select_sql = "	SELECT promotion_image 
												FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . "
												WHERE products_id = '" . $id_row['products_id'] . "'";
            $proc_id_result_sql = tep_db_query($product_promotion_select_sql);

            while ($proc_id_rows = tep_db_fetch_array($proc_id_result_sql)) {
                if ($aws_obj->is_aws_s3_enabled()) {
                    // Remove from S3
                    if ($aws_obj->is_image_exists($proc_id_rows['promotion_image'], 'images/products/', 'BUCKET_STATIC')) {
                        $aws_obj->delete_file($proc_id_rows['promotion_image'], 'images/products/', 'BUCKET_STATIC');
                    }
                } else {
                    // Remove from local
                    if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image'])) {
                        $oldPermission = @umask(0);
                        @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image'], 0777);
                        @umask($oldPermission);
                        @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image']);
                    }
                }
            }

            $delete_promo_desc_sql = " DELETE FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " WHERE products_id = " . $id_row['products_id'];
            $delete_promo_desc_result_sql = tep_db_query($delete_promo_desc_sql);
            $delete_promo_sql = " DELETE FROM " . TABLE_PRODUCTS_PROMOTION . " WHERE products_id = " . $id_row['products_id'];
            $delete_promo_result_sql = tep_db_query($delete_promo_sql);

            $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $id_row['products_id'];
            $memcache_obj->delete($cache_key, 0);
        }
        return;
    }

    function deleteEntry($pid = '') {
        if (tep_not_null($pid)) {
            global $memcache_obj;
            $main_result_sql = 0;
            $desc_result_sql = 0;
            $aws_obj = new ogm_amazon_ws();

            // Remove Product Promotion Images
            $product_promotion_select_sql = "	SELECT promotion_image 
												FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . "
												WHERE products_id = '" . $pid . "'";
            $proc_id_result_sql = tep_db_query($product_promotion_select_sql);

            while ($proc_id_rows = tep_db_fetch_array($proc_id_result_sql)) {
                if ($aws_obj->is_aws_s3_enabled()) {
                    // Remove from S3
                    if ($aws_obj->is_image_exists($proc_id_rows['promotion_image'], 'images/products/', 'BUCKET_STATIC')) {
                        $aws_obj->delete_file($proc_id_rows['promotion_image'], 'images/products/', 'BUCKET_STATIC');
                    }
                } else {
                    // Remove from local
                    if (file_exists(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image'])) {
                        $oldPermission = @umask(0);
                        @chmod(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image'], 0777);
                        @umask($oldPermission);
                        @unlink(DIR_FS_CATALOG_IMAGES . "products/" . $proc_id_rows['promotion_image']);
                    }
                }
            }

            $id_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PROMOTION . " WHERE products_id = " . $pid;
            $main_result_sql = tep_db_query($id_delete_sql);
            $desc_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " WHERE products_id = " . $pid;
            $desc_result_sql = tep_db_query($desc_delete_sql);

            if ($main_result_sql == 1 && $desc_result_sql == 1) {
                $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $pid;
                $memcache_obj->delete($cache_key, 0);
                return TEXT_PRODUCT_PROMOTION_ICON_SUCCESS;
            }
            return;
        }
    }

}
?>