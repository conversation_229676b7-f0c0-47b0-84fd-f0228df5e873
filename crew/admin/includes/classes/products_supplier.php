<?php

class products_supplier {
	var $supplier_id, $products_supplier, $products_rss_link;
	
	function products_supplier($supplier_id) {
		$this->products_supplier = array();
		
		$this->supplier_id = $supplier_id;
		
		$this->query();
	}
	
	function query() {
		$products_supplier_array = array();
		
		$products_supplier_select_sql = "SELECT * FROM " . TABLE_PRODUCTS_SUPPLIER . " WHERE supplier_id = '" . (int)$this->supplier_id . "'";
		$products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
		if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
			foreach ($products_supplier_row as $key => $val) {
				$products_supplier_array[$key] = $val;
			}
		}
		
		$this->products_supplier = $products_supplier_array;
	}
	
	function get_products_to_supplier_id($products_id) {
		$products_to_supplier_id = '';
		
		$products_id_select_sql = "	SELECT products_to_supplier_id FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " 
									WHERE supplier_id = '" . $this->supplier_id . "' 
										AND products_id = '" . $products_id . "'";
		$products_id_result_sql = tep_db_query($products_id_select_sql);
		if ($products_id_row = tep_db_fetch_array($products_id_result_sql)) {
			$products_to_supplier_id = $products_id_row['products_to_supplier_id'];
		}
		
		return $products_to_supplier_id;
	}
	
	function get_products_rss_link() {
		$products_info_link_array = array();
		
		$products_info_link_select_sql = "	SELECT pts.products_id, prl.url, pts.folder_name 
											FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " AS pts 
											INNER JOIN " . TABLE_PRODUCTS_RSS_LINK . " AS prl 
												ON pts.products_to_supplier_id = prl.products_to_supplier_id 
											WHERE pts.supplier_id = '" . (int)$this->supplier_id . "' 
											ORDER BY pts.products_id, prl.url";
		$products_info_link_result_sql = tep_db_query($products_info_link_select_sql);
		while ($products_info_link_row = tep_db_fetch_array($products_info_link_result_sql)) {
			$products_info_link_array[$products_info_link_row['products_id']][] = array('url' => $products_info_link_row['url'],
																				 		'folder_name' => $products_info_link_row['folder_name']);
		}
		
		$this->products_rss_link = $products_info_link_array;
	}
	
	function update_products_info($products_links) {
		if (tep_not_null($products_links)) {
			foreach ($products_links as $products_id => $products_info_arr) {
				$products_to_supplier_id = $this->get_products_to_supplier_id($products_id);
				
				if (tep_not_null($products_to_supplier_id)) {
					foreach ($products_info_arr as $cnt => $products_info) {
						$url = tep_db_prepare_input($products_info['url']);
						$folder_name = $products_info['folder_name'];
						
						$products_to_supplier_sql_data = array ( 'folder_name' => $folder_name);
						tep_db_perform(TABLE_PRODUCTS_TO_SUPPLIER, $products_to_supplier_sql_data, 'update', 'products_to_supplier_id = "'. tep_db_input($products_to_supplier_id) . '"');
						
						$products_info_link_sql_data = array();
						
						$products_info_link_select_sql = "	SELECT products_rss_link_id 
															FROM " . TABLE_PRODUCTS_RSS_LINK . " 
															WHERE products_to_supplier_id = '" . $products_to_supplier_id . "' 
																AND url = '" . tep_db_input($url) . "'";
						$products_info_link_result_sql = tep_db_query($products_info_link_select_sql);
						if (tep_db_num_rows($products_info_link_result_sql) == 0) {
							$products_info_link_sql_data = array (	'products_to_supplier_id' => $products_to_supplier_id, 
																	'url' => $url);
							tep_db_perform(TABLE_PRODUCTS_RSS_LINK, $products_info_link_sql_data);
						}
					}
				} else {
					$products_to_supplier_sql_data = array();
					$products_to_supplier_sql_data = array ('supplier_id' => $this->supplier_id, 
															'products_id' => $products_id);
					tep_db_perform(TABLE_PRODUCTS_TO_SUPPLIER, $products_to_supplier_sql_data);
					$products_to_supplier_id = tep_db_insert_id();
					
					if (tep_not_null($products_to_supplier_id)) {
						foreach ($products_links[$products_id] as $cnt => $products_info) {
							$url = tep_db_prepare_input($products_info['url']);
							$folder_name = $products_info['folder_name'];
							
							$products_to_supplier_sql_data = array ( 'folder_name' => $folder_name);
							tep_db_perform(TABLE_PRODUCTS_TO_SUPPLIER, $products_to_supplier_sql_data, 'update', 'products_to_supplier_id = "'. $products_to_supplier_id . '"');
						
							$products_info_link_select_sql = "	SELECT products_rss_link_id FROM " . TABLE_PRODUCTS_RSS_LINK . " 
																WHERE products_to_supplier_id = '" . $products_to_supplier_id . "' 
																	AND url = '" . tep_db_input($url) . "'";
							$products_info_link_result_sql = tep_db_query($products_info_link_select_sql);
							if (tep_db_num_rows($products_info_link_result_sql) == 0) {
								$products_info_link_sql_data = array (	'products_to_supplier_id' => $products_to_supplier_id, 
																		'url' => $url);
								tep_db_perform(TABLE_PRODUCTS_RSS_LINK, $products_info_link_sql_data);
							}
						}
					}
				}
			}
		}
	}
	
	function remove_rss_link($products_to_supplier_id, $url) {
		if (tep_not_null($products_to_supplier_id)) {
			$products_info_remove_sql = "	DELETE FROM " . TABLE_PRODUCTS_RSS_LINK . " 
											WHERE products_to_supplier_id = '" . (int)$products_to_supplier_id . "' 
												AND url = '" . tep_db_prepare_input($url) . "'";
			tep_db_query($products_info_remove_sql);
			
			// remove products if rss_link is empty
			$products_info_select_sql = "SELECT products_rss_link_id FROM " . TABLE_PRODUCTS_RSS_LINK . " WHERE products_to_supplier_id = '" . (int)$products_to_supplier_id . "'";
			$products_info_result_sql = tep_db_query($products_info_select_sql);
			if (tep_db_num_rows($products_info_result_sql) == 0) {
				$products_info_remove_sql = "	DELETE FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " 
												WHERE products_to_supplier_id = '" . (int)$products_to_supplier_id . "' 
													AND supplier_id = '" . (int)$this->supplier_id . "'";
				tep_db_query($products_info_remove_sql);
			}
		}
	}
	
	function remove_products_rss_link($products_id = '') {
		$products_id_remove_sql = '';
		
		if (tep_not_null($products_id)) {
			$products_id_remove_sql = " AND pts.products_id = '" . (int)$products_id . "' ";
		}
		
		$products_info_remove_sql = "DELETE pts.*, prl.* 
									FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " AS pts, " . 
									TABLE_PRODUCTS_RSS_LINK . " AS prl 
									WHERE pts.supplier_id = '" . (int)$this->supplier_id . "' " .
										$products_id_remove_sql . " 
										AND pts.products_to_supplier_id = prl.products_to_supplier_id";
		tep_db_query($products_info_remove_sql);
	}
	
	function remove_products_supplier() {
		$products_supplier_remove_sql = "	DELETE FROM " . TABLE_PRODUCTS_SUPPLIER . " 
											WHERE supplier_id = '" . (int)$this->supplier_id . "'";
		tep_db_query($products_supplier_remove_sql);
		
		$this->remove_products_rss_link();
	}
	
	function construct_products_array($products_info) {
		$products_links = array();
		
		if (tep_not_null($products_info)) {
			for ($i=0, $total = count($products_info['products_id']); $total > $i; $i++) {
				if (tep_not_null($products_info['products_id'][$i]) && tep_not_null($products_info['url'][$i])) {
					$products_links[$products_info['products_id'][$i]][] = array('url' => $products_info['url'][$i],
																				 'folder_name' => $products_info['folder_name'][$i]);
				}
			}
		}
		
		return $products_links;
	}
}
?>