<?php

class publishers {

    var $pass_id, $publishers_id, $dtu_report_paging, $dtu_list_limit, $dtu_list_paging;

    function publishers($pass_id = '') {
        $this->publishers_id = (int) $pass_id;
        $this->dtu_report_paging = 200;
        $this->dtu_list_paging = 200;
        $this->dtu_list_limit = 1000;
    }

    function get_publishers() {
        $publishers_array = array();
        $publishers_select_sql = "	SELECT * 
									FROM " . TABLE_PUBLISHERS . "
									WHERE publishers_id = '" . $this->publishers_id . "'";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_id']] = $publishers_row;
        }
        return $publishers_array;
    }

    function get_all_publishers() {
        $publishers_array = array();
        $publishers_select_sql = "SELECT * FROM " . TABLE_PUBLISHERS;
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_id']] = $publishers_row;
        }
        return $publishers_array;
    }

    function get_all_publishers_unlocked() {
        $publishers_array = array();
        $publishers_select_sql = "  SELECT p.publishers_id, p.publishers_name, p.publishers_status, 
                                    ps.po_supplier_locked_by, a.admin_email_address 
                                    FROM " . TABLE_PUBLISHERS . " AS p
                                    INNER JOIN " . TABLE_PO_SUPPLIERS . " AS ps 
                                        ON (p.publishers_supplier_id = ps.po_suppliers_id 
                                        AND ps.po_supplier_status='1')
                                    LEFT JOIN " . TABLE_ADMIN . " AS a
                                        ON ps.po_supplier_locked_by = a.admin_id
                                    ";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_array[$publishers_row['publishers_id']] = $publishers_row;
        }
        return $publishers_array;
    }

    function set_publishers_conf($pass_key, $pass_value = '') {
        $publishers_configuration_array = array();
        $publishers_configuration_array['publishers_configuration_value'] = tep_db_prepare_input($pass_value);
        $publishers_configuration_array['last_modified'] = 'now()';
        if (isset($_SESSION['login_email_address']))
            $publishers_configuration_array['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
        tep_db_perform(TABLE_PUBLISHERS_CONFIGURATION, $publishers_configuration_array, 'update', " publishers_id = '" . $this->publishers_id . "' AND publishers_configuration_key = '" . tep_db_input($pass_key) . "'");
    }

    function get_publishers_conf($pass_key = '') {
        $publishers_configuration_array = array();
        $publishers_configuration_select_sql = "SELECT * 
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . $this->publishers_id . "'";
        if (tep_not_null($pass_key)) {
            $publishers_configuration_select_sql .= " AND publishers_configuration_key = '" . tep_db_input($pass_key) . "'";
            $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
            $publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql);
            return $publishers_configuration_row['publishers_configuration_value'];
        } else {
            $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
            while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
                $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row;
            }
            return $publishers_configuration_array;
        }
    }

    function activate_publishers() {
        $publishers_data_sql = array("publishers_status" => 1,
            "last_modified" => 'now()');
        if (isset($_SESSION['login_email_address']))
            $publishers_data_sql['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
        tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '" . $this->publishers_id . "' ");
    }

    function deactivate_publishers() {
        $publishers_data_sql = array("publishers_status" => 0,
            "last_modified" => 'now()');
        if (isset($_SESSION['login_email_address']))
            $publishers_data_sql['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
        tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '" . $this->publishers_id . "' ");
    }

    function set_publishers_game($pass_id, $pass_game) {
        $update_publishers_game_data_sql = array('publishers_game' => tep_db_prepare_input($pass_game));
        $servers_select_sql = "	SELECT publishers_id 
								FROM " . TABLE_PUBLISHERS_GAMES . "
								WHERE publishers_id = '" . $this->publishers_id . "'
									AND products_id = '" . (int) $pass_id . "'";
        $servers_result_sql = tep_db_query($servers_select_sql);
        if (tep_db_num_rows($servers_result_sql)) {
            tep_db_perform(TABLE_PUBLISHERS_GAMES, $update_publishers_game_data_sql, 'update', " publishers_id = '" . $this->publishers_id . "' AND products_id = '" . (int) $pass_id . "' ");
        } else {
            $update_publishers_game_data_sql['publishers_id'] = $this->publishers_id;
            $update_publishers_game_data_sql['products_id'] = (int) $pass_id;
            tep_db_perform(TABLE_PUBLISHERS_GAMES, $update_publishers_game_data_sql);
        }
    }

    function get_publishers_game($publishers_id = '') {
        $publishers_games_array = array();
        $servers_select_sql_where = '';

        if (tep_not_null($publishers_id)) {
            $servers_select_sql_where = "WHERE publishers_id = '" . $this->publishers_id . "'";
        }
        // Checking
        $servers_select_sql = "	SELECT publishers_games_id, publishers_id, publishers_game
								FROM " . TABLE_PUBLISHERS_GAMES .
                $servers_select_sql_where;

        $servers_result_sql = tep_db_query($servers_select_sql);
        while ($servers_row = tep_db_fetch_array($servers_result_sql)) {
            $publishers_games_array[$servers_row['publishers_id']][$servers_row['publishers_games_id']] = $servers_row['publishers_game'];
        }

        return $publishers_games_array;
    }

    function set_publishers_game_server($publishers_games_id, $publishers_server) {
        if ((int) $publishers_games_id) {
            $game_array = array('publishers_server' => json_encode($publishers_server));

            $servers_select_sql = "	SELECT publishers_games_id, publishers_id
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE publishers_games_id = '" . tep_db_input($publishers_games_id) . "'";
            $servers_result_sql = tep_db_query($servers_select_sql);
            if ($row = tep_db_fetch_array($servers_result_sql)) {
                tep_db_perform(TABLE_PUBLISHERS_GAMES, $game_array, 'update', " publishers_games_id = " . $publishers_games_id);
            }

            unset($game_array);
        }
    }

    function get_publishers_game_server($pass_match, $pass_type = 'products_id') {
        $servers_array = array();
        if ($pass_type == 'products_id') {
            $servers_select_sql = "	SELECT publishers_servers 
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE products_id = '" . (int) $pass_match . "'";
        } else {
            $servers_select_sql = "	SELECT publishers_servers 
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE game = '" . tep_db_input($pass_match) . "'";
        }
        $servers_result_sql = tep_db_query($servers_select_sql);
        if ($servers_row = tep_db_fetch_array($servers_result_sql) && tep_not_null($servers_row['publishers_servers'])) {
            $servers_array = json_decode($servers_row['publishers_servers']);
        }
        return $servers_array;
    }
    
    function set_dtu_temp_status($dtu_topup_id, $set_cb = '') {
        if ($set_cb) {
            $update_col = 'top_up_cb_temp_status';
            $temp_value = (string) $set_cb;
        } else {
            $update_col = 'top_up_withdrawal_temp_status';
            $temp_value = '1';
        }
        
        $check_array = array($update_col => $temp_value);
        tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $check_array, 'update', "top_up_id = " . $dtu_topup_id);
    }
    
    function set_dtu_temp_status_empty($publisher_id = '', $set_cb = '') {
        $update_col = ($set_cb) ? 'top_up_cb_temp_status' : 'top_up_withdrawal_temp_status';
        if ($publisher_id) {
            $where_col = "publishers_id = '" . $publisher_id . "'";
        } else {
            $where_col = "publishers_id = '" . $this->publishers_id . "'";
        }
        $check_array = array($update_col => '0');
        tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $check_array, 'update', $where_col);
    }
            
    function get_dtu_withdrawal_status($topup_id) {
        $topup_withdrawal_sql = "SELECT otw.top_up_id, otw.top_up_withdrawal_status
                                FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw
                                WHERE otw.top_up_id = '" . (int)$topup_id . "'";
        $topup_withdrawal_result = tep_db_query($topup_withdrawal_sql);
        if ($topup_withdrawal_row = tep_db_fetch_array($topup_withdrawal_result)) {
            $topup_withdrawal_status = $topup_withdrawal_row['top_up_withdrawal_status'];
        }
        return $topup_withdrawal_status;
        
    }
    
    function get_dtu_po_final_price($po_id, $products_id) {
        $dtu_price_sql = "SELECT po.purchase_orders_id, po.purchase_orders_gst_value, pop.products_id, po.currency,
                          pop.products_unit_price_type, pop.products_unit_price_value, pop.products_selling_price 
                          FROM " . TABLE_PURCHASE_ORDERS . " AS po
                          LEFT JOIN " . TABLE_PURCHASE_ORDERS_PRODUCTS . " AS pop
                            ON po.purchase_orders_id = pop.purchase_orders_id 
                            AND pop.products_id = '" . (int)$products_id . "' 
                          WHERE po.purchase_orders_id = '" . (int)$po_id . "'";
        
        $dtu_price_result = tep_db_query($dtu_price_sql);
        if ($dtu_price_row = tep_db_fetch_array($dtu_price_result)) {
            return $dtu_price_row;
        } else {
            return false;
        }
        
    }
    
    function get_dn_po_id($topup_id) {
        $dn_po_sql = "SELECT top_up_id, purchase_orders_id
                          FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . "
                          WHERE top_up_id = '" . (int)$topup_id . "'";
        $dn_po_result = tep_db_query($dn_po_sql);
        if ($dn_po_row = tep_db_fetch_array($dn_po_result)) {
            return $dn_po_row['purchase_orders_id'];
        } else {
            return false;
        }
    }
            
    function get_dtu_items($comma_list) {
        $top_up_array = array();
        $select_qry = "op.products_delivered_quantity, op.products_id, otu.top_up_id, op.orders_id, p.products_type,
                        IF(p.products_type = 3, op.products_name, pd.products_name) AS products_name,
                        otu.currency_code, otu.currency_settle_amount,
                        DATE_FORMAT(otu.top_up_timestamp, '%d/%m/%Y') as topup_time, op.final_price,
                        opei.orders_products_extra_info_value as sub_products_id,
                        otw.purchase_orders_id, otw.top_up_withdrawal_ref_id as withdrawal_ref, 
                        otw.top_up_withdrawal_status as withdrawal_status, otw.top_up_withdrawal_temp_status as withdrawal_temp_status, 
                        otw.top_up_cb_status as cb_status, otw.top_up_cb_temp_status as cb_temp_status,
                        otw.top_up_cb_deduction_po_id as cb_deduction_po_id, otw.top_up_cb_deduction_status as cb_deduction_status,
                        DATE_FORMAT(otw.top_up_withdrawal_date, '%d/%m/%Y') as withdrawal_date";
        
        $top_up_sql = "SELECT " . $select_qry . "
                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                        INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
                            ON otu.orders_products_id = op.orders_products_id 
                        LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
                            ON opei.orders_products_id = op.orders_products_id
                            AND opei.orders_products_extra_info_key = 'sub_products_id'
                        INNER JOIN " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw
                            ON op.orders_products_id = otw.orders_products_id
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON op.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        LEFT JOIN " . TABLE_PRODUCTS . " AS p
                            ON op.products_id = p.products_id
                        WHERE 
                            otu.top_up_id IN (" . $comma_list . ")";
        
        $top_up_result = tep_db_query($top_up_sql);
        
        while ($top_up_row = tep_db_fetch_array($top_up_result)) {
            $top_up_array[$top_up_row['top_up_id']] = $top_up_row;
        }
        
        return $top_up_array;
    }
            
    function get_dtu_list($dtu_list_type, $tp_id, $start_date, $end_date, $page, $sort_by, $sort_option) {
        $top_up_array = array();
        $list_limit = $this->dtu_list_limit;
        // How many items to list per page
        $limit = $this->dtu_list_paging;
        // Calculate the offset for the query
        $offset = ($page - 1) * $limit;
        
        for($i = 0; $i < 2; $i++) {
            if($dtu_list_type == 'add_dtu_cb' && $i == 0) {
                continue;
            }
            $withdrawal_status = '';
            $date_range = '';
            if($i == 0) {
                $withdrawal_status = "AND (otw.top_up_withdrawal_status = '0' OR otw.top_up_withdrawal_status = '7' OR otw.top_up_cb_status = '1' OR otw.top_up_cb_status = '2' OR otw.top_up_cb_status = '3')";
                $date_range = "AND otu.top_up_timestamp < '" . $start_date . " 00:00:00'";
            } else {
                $date_range = "AND otu.top_up_timestamp >= '" . $start_date . " 00:00:00' AND otu.top_up_timestamp <= '" . $end_date . " 23:59:59'";
            }
            
            $top_up_result = $this->dtu_list_query('list', $tp_id, $date_range, $withdrawal_status, $list_limit, $limit, $offset, $sort_by, $sort_option);
            
            $list_limit = $list_limit - tep_db_num_rows($top_up_result);
            
            while ($top_up_row = tep_db_fetch_array($top_up_result)) {
                $top_up_array[$top_up_row['top_up_id']] = $top_up_row;
            }
        }
        return $top_up_array;
    }
    
    function get_dtu_paging($dtu_list_type, $tp_id, $start_date, $end_date) {
        $pages = array();
        $list_limit = $this->dtu_list_limit;
        
        $pages['total'] = 0;
        $pages['pages'] = 0;
        
        for($i = 0; $i < 2; $i++) {
            if($dtu_list_type == 'add_dtu_cb' && $i == 0) {
                continue;
            }
            $withdrawal_status = '';
            $date_range = '';
            if($i == 0) {
                $withdrawal_status = "AND (otw.top_up_withdrawal_status = '0' OR otw.top_up_withdrawal_status = '7' OR otw.top_up_cb_status = '1' OR otw.top_up_cb_status = '2' OR otw.top_up_cb_status = '3')";
                $date_range = "AND otu.top_up_timestamp < '" . $start_date . " 00:00:00'";
            } else {
                $date_range = "AND otu.top_up_timestamp >= '" . $start_date . " 00:00:00' AND otu.top_up_timestamp <= '" . $end_date . " 23:59:59'";
            }
            
            $top_up_result = $this->dtu_list_query('pages', $tp_id, $date_range, $withdrawal_status, $list_limit, '', '', '', '');
            
            $list_limit = $list_limit - tep_db_num_rows($top_up_result);
            
            if ($top_up_row = tep_db_fetch_array($top_up_result)) {
                // How many pages will there be
                $pages['total'] = $pages['total'] + $top_up_row['total_dtu'];
            }
        }
        
        $pages['pages'] = ceil($pages['total'] / $this->dtu_list_paging);
        
        return $pages;
    }
    
    function dtu_list_query($query_type, $tp_id, $date_range, $withdrawal_status, $list_limit, $limit, $offset, $sort_by, $sort_option) {
        $topup_qry = (empty($tp_id)) ? '' : 'AND otu.top_up_id = ' . (int)$tp_id;
        if ($query_type == 'pages') {
            $select_qry = "count(op.products_id) AS total_dtu";
            $limit_qry = "LIMIT " . $list_limit;
            $offset_qry = "";
            $order_by_qry = "";
        } else {
            switch ($sort_by) {
                case 'topup_date':
                    $sort_by_qry = "otw.top_up_withdrawal_date";
                    break;
                case 'prod_id':
                    $sort_by_qry = "op.products_id";
                    break;
                case 'prod_name':
                    $sort_by_qry = "pd.products_name";
                    break;
                case 'order_num':
                    $sort_by_qry = "op.orders_id";
                    break;
                case 'topup_id':
                    $sort_by_qry = "otu.top_up_id";
                    break;
            }
            $select_qry = "op.products_delivered_quantity, op.products_id, otu.top_up_id, op.orders_id, 
                            DATE_FORMAT(otu.top_up_timestamp, '%d/%m/%Y') as topup_time, op.final_price,
                            opei.orders_products_extra_info_value as sub_products_id,
                            otw.purchase_orders_id, otw.top_up_withdrawal_ref_id as withdrawal_ref, 
                            otw.top_up_withdrawal_status as withdrawal_status, otw.top_up_withdrawal_temp_status as withdrawal_temp_status, 
                            otw.top_up_cb_status as cb_status, otw.top_up_cb_temp_status as cb_temp_status,
                            otw.top_up_cb_deduction_po_id as cb_deduction_po_id, otw.top_up_cb_deduction_status as cb_deduction_status,
                            DATE_FORMAT(otw.top_up_withdrawal_date, '%d/%m/%Y') as withdrawal_date";
            $limit_qry = "LIMIT " . $limit;
            $offset_qry = "OFFSET " . $offset;
            $order_by_qry = "ORDER BY " . $sort_by_qry . " " . $sort_option;
        }
        
        $top_up_sql = "SELECT " . $select_qry . "
                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                        INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
                            ON otu.orders_products_id = op.orders_products_id 
                            " . $topup_qry . "
                        LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
                            ON opei.orders_products_id = op.orders_products_id
                            AND opei.orders_products_extra_info_key = 'sub_products_id'
                        INNER JOIN " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw
                            ON op.orders_products_id = otw.orders_products_id
                            " . $withdrawal_status . "
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON op.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        WHERE otu.publishers_id = '" . $this->publishers_id . "' 
                            " . $date_range . "
                            AND otu.top_up_status = '3'
                        " . $order_by_qry . "
                        " . $limit_qry . "
                        " . $offset_qry;
        $top_up_result = tep_db_query($top_up_sql);
        
        return $top_up_result;
    }
            
    function get_dtu_list_report($dtu_list_type, $product_id, $order_id, $topup_id, $start_date, $end_date, $page, $sort_by, $sort_option) {
        $top_up_array = array();
        $sort_array = array('sort_by' => $sort_by, 'sort_option' => $sort_option);
        
        // How many items to list per page
        $limit = $this->dtu_report_paging;
        
        // Calculate the offset for the query
        $offset = ($page - 1) * $limit;
        
        $withdrawal_status = $this->withdrawal_status_query($dtu_list_type);
        
        $top_up_result = $this->dtu_reports_query('list', $product_id, $order_id, $topup_id, $start_date, $end_date, $withdrawal_status, $limit, $offset, $sort_array);
        
        while ($top_up_row = tep_db_fetch_array($top_up_result)) {
            $top_up_array[$top_up_row['top_up_id']] = $top_up_row;
        }
        
        return $top_up_array;
    }
    
    function get_dtu_paging_report($dtu_list_type, $product_id, $order_id, $topup_id, $start_date, $end_date) {
        $pages = array();
        
        $withdrawal_status = $this->withdrawal_status_query($dtu_list_type);
        
        $top_up_result = $this->dtu_reports_query('pages', $product_id, $order_id, $topup_id, $start_date, $end_date, $withdrawal_status, '', '');
        
        if ($top_up_row = tep_db_fetch_array($top_up_result)) {
            // How many pages will there be
            $pages['total'] = $top_up_row['total_dtu'];
            $pages['pages'] = ceil($top_up_row['total_dtu'] / $this->dtu_report_paging);
        }
        
        return $pages;
    }
    
    function withdrawal_status_query($dtu_list_type) {
        switch ($dtu_list_type) {
            case 'unpaid':
                $withdrawal_status = "AND (otw.top_up_withdrawal_status = '0' OR otw.top_up_withdrawal_status = '7') AND otw.top_up_cb_status = '0'";
                break;
            case 'paid':
                $withdrawal_status = "AND (otw.top_up_withdrawal_status = '3' OR otw.top_up_withdrawal_status = '11') AND otw.top_up_cb_status = '0'";
                break;
            case 'charge_back':
                $withdrawal_status = "AND (otw.top_up_cb_status = '1')";
                break;
            case 'debit_note':
                $withdrawal_status = "AND (otw.top_up_cb_status = '2')";
                break;
            case 'dtu_issue':
                $withdrawal_status = "AND (otw.top_up_cb_status = '3')";
                break;
            default :
                $withdrawal_status = '';
                break;
        }
        
        return $withdrawal_status;
    }

    function dtu_reports_query($query_type, $product_id, $order_id, $topup_id, $start_date, $end_date, $withdrawal_status, $limit, $offset, $sort_array = array()) {
        $sort_option = $sort_array['sort_option'];
        if ($query_type == 'pages') {
            $select_qry = "count(op.products_id) AS total_dtu";
            $order_by_qry = "";
            $limit_qry = "";
            $offset_qry = "";
        } else {
            switch ($sort_array['sort_by']) {
                case 'topup_date':
                    $sort_by_qry = "otu.top_up_timestamp";
                    break;
                case 'prod_id':
                    $sort_by_qry = "op.products_id";
                    break;
                case 'pub_name':
                    $sort_by_qry = "pu.publishers_name";
                    break;
                case 'prod_name':
                    $sort_by_qry = "pd.products_name";
                    break;
                case 'order_num':
                    $sort_by_qry = "op.orders_id";
                    break;
                case 'topup_id':
                    $sort_by_qry = "otu.top_up_id";
                    break;
            }
            $select_qry = "op.products_delivered_quantity, op.products_id, otu.top_up_id, op.orders_id, pd.products_name,
                        DATE_FORMAT(otu.top_up_timestamp, '%d/%m/%Y') as topup_time, op.final_price,
                        opei.orders_products_extra_info_value as sub_products_id,
                        otw.purchase_orders_id, otw.top_up_withdrawal_ref_id as withdrawal_ref, 
                        otw.top_up_withdrawal_status as withdrawal_status, otw.top_up_withdrawal_temp_status as withdrawal_temp_status, 
                        otw.top_up_cb_status as cb_status, otw.top_up_cb_temp_status as cb_temp_status,
                        otw.top_up_cb_deduction_po_id as cb_deduction_po_id, otw.top_up_cb_deduction_status as cb_deduction_status,
                        DATE_FORMAT(otw.top_up_withdrawal_date, '%d/%m/%Y') as withdrawal_date, pu.publishers_id, pu.publishers_name";
            $order_by_qry = "ORDER BY " . $sort_by_qry . " " . $sort_option;
            $limit_qry = "LIMIT " . $limit;
            $offset_qry = "OFFSET " . $offset;
        }
        
        if ($this->publishers_id == 0) {
            $publisher_qry = "";
        } else {
            $publisher_qry = "otu.publishers_id = '" . $this->publishers_id . "' AND";
        }
        
        $topup_qry = (empty($topup_id)) ? '' : 'AND otu.top_up_id = ' . (int)$topup_id;
        $product_qry = (empty($product_id)) ? '' : 'AND op.products_id = ' . (int)$product_id;
        $order_qry = (empty($order_id)) ? '' : 'AND op.orders_id = ' . (int)$order_id;
        
        $top_up_sql = "SELECT " . $select_qry . "
                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                        INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
                            ON otu.orders_products_id = op.orders_products_id
                            " . $topup_qry . "
                        LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
                            ON opei.orders_products_id = op.orders_products_id
                            AND opei.orders_products_extra_info_key = 'sub_products_id'
                        INNER JOIN " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " AS otw
                            ON op.orders_products_id = otw.orders_products_id
                            " . $withdrawal_status . "
                        INNER JOIN " . TABLE_PUBLISHERS . " AS pu
                            ON otw.publishers_id = pu.publishers_id
                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                            ON op.products_id = pd.products_id
                            AND pd.language_id = 1
                            AND pd.products_name <> ''
                        WHERE 
                            " . $publisher_qry . "
                            otu.top_up_timestamp >= '" . $start_date . " 00:00:00' AND otu.top_up_timestamp <= '" . $end_date . " 23:59:59'
                            " . $order_qry . "
                            " . $product_qry . "
                            AND otu.top_up_status = '3'
                        " . $order_by_qry . "
                        " . $limit_qry . "
                        " . $offset_qry;
        $top_up_result = tep_db_query($top_up_sql);
        
        return $top_up_result;
    }

    function validate_active_publisher () {
        $publishers_select_sql = "	SELECT publishers_id
									FROM " . TABLE_PUBLISHERS . "
                                    WHERE publishers_id = '" . $this->publishers_id . "'
                                        AND publishers_status = 1";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        if (tep_db_num_rows($publishers_result_sql) > 0) {
            return true;
        }

        return false;
    }
}

?>