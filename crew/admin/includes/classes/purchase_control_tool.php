<?php
include_once(DIR_WS_LANGUAGES . "english/out_of_stock_rule.php");

class purchase_control_tool {

    public function get_out_of_stock_rules() {
        $out_of_stock_rules_select_sql = "  SELECT ofsr.rules_id, ofsr.rules_name
                                            FROM " . TABLE_OUT_OF_STOCK_RULES . " AS ofsr
                                            WHERE 1
                                            ORDER BY  ofsr.rules_name ASC";
        $out_of_stock_rules_result_sql = tep_db_query($out_of_stock_rules_select_sql);
        $out_of_stock_rules_set_array = array();
        while ($out_of_stock_rules = tep_db_fetch_array($out_of_stock_rules_result_sql)) {
            $out_of_stock_rules_set_array[] = array('id' => $out_of_stock_rules['rules_id'], 'text' => $out_of_stock_rules['rules_name']);
        }

        return $out_of_stock_rules_set_array;
    }

    public function get_out_of_stock_rules_by_id($id) {
        $rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.out_of_stock_in_percentage
                                FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                LEFT JOIN " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.customers_groups_id
                                AND ofsrcg.out_of_stock_rules_id = '" . tep_db_input($id) . "'
                                ORDER BY cg.sort_order, cg.customers_groups_name";
        $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);
        ob_start();
        ?>
        <?php
        echo '<table width="100%" border="0" cellspacing="1" cellpadding="1">';
        while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
            $customers_groups_id = $customers_groups['customers_groups_id'];
            $out_of_stock_in_percentage = isset($customers_groups['out_of_stock_in_percentage']) ? number_format($customers_groups['out_of_stock_in_percentage'], 2, '.', '') : '0';
            ?>
            <tr height="25px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                <td class="main"  align="center">
                    <?php
                    echo $customers_groups['customers_groups_name'];
                    ?>
                </td>
                <td class="main" align="center"> 
                    <?php echo $out_of_stock_in_percentage; ?>
                </td>
            </tr>
            <?php
            $row_count++;
        }
        echo '</table>';
        $report_section_html = ob_get_contents();
        ob_end_clean();
        return $report_section_html;
    }

    public function recalculate_by_pid($rule_id, $pid, &$messageStack, $is_ajax = false) {
        $error = true;

        if ($rule_id && $pid) {
            $stock_quantity = 0;

            $products_extra_info_sql = "SELECT products_extra_info_value 
                                        FROM " . TABLE_PRODUCTS_EXTRA_INFO . " 
                                        WHERE products_id= '" . (int) $pid . "' AND products_extra_info_key = 'stock_quantity'";
            $products_extra_info_query = tep_db_query($products_extra_info_sql);
            if ($products_extra_info = tep_db_fetch_array($products_extra_info_query)) {
                $stock_quantity = $products_extra_info['products_extra_info_value'];
            }

            $error = $this->process_recalculation($rule_id, $pid, $stock_quantity);
        } else {
            if ($is_ajax) {
                echo ERROR_OUT_OF_STOCK_MISSING_RULE_OR_PRODUCT_ID;
            } else {
                $messageStack->add_session(ERROR_OUT_OF_STOCK_MISSING_RULE_OR_PRODUCT_ID, 'error');
            }
        }

        if ($error) {
            if ($is_ajax) {
                echo ERROR_OUT_OF_STOCK_RECALCULATE_ERROR;
            } else {
                $messageStack->add_session(ERROR_OUT_OF_STOCK_RECALCULATE_ERROR, 'error');
            }
        } else {
//            $product_stock_select_sql = "SELECT products_quantity, products_actual_quantity FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int) $pid . "'";
//            $product_stock_result_sql = tep_db_query($product_stock_select_sql);
//
//            if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
//                $prod_available_qty = $product_stock_row["products_quantity"];
//            }
//
//            $stock_quantity_data_array = array('products_extra_info_value' => $prod_available_qty);
//            tep_db_perform(TABLE_PRODUCTS_EXTRA_INFO, $stock_quantity_data_array, 'update', "products_id = '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'");

            if ($is_ajax) {
                echo MESSAGE_OUT_OF_STOCK_RECALCULATE_SUCCESS;
            } else {
                $messageStack->add_session(MESSAGE_OUT_OF_STOCK_RECALCULATE_SUCCESS, 'success');
            }
        }
    }

    public function recalculate_by_rule_id($rule_id, &$messageStack) {
        $error = null;

        if ($rule_id) {
            $products_extra_info_prod_id_sql = "SELECT products_id
                                                FROM " . TABLE_PRODUCTS_EXTRA_INFO . "  
                                                WHERE products_extra_info_key = 'out_of_stock_rules_id' AND  products_extra_info_value = '" . (int) $rule_id . "' ";
            $products_extra_info_prod_id_query = tep_db_query($products_extra_info_prod_id_sql);
            if (tep_db_num_rows($products_extra_info_prod_id_query)) {
                while ($products_extra_info_prod_id = tep_db_fetch_array($products_extra_info_prod_id_query)) {
                    $stock_quantity = 0;
                    $pid = $products_extra_info_prod_id['products_id'];

                    $products_extra_info_query = tep_db_query("SELECT products_extra_info_value from " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_id= '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'");
                    if ($products_extra_info = tep_db_fetch_array($products_extra_info_query)) {
                        $stock_quantity = $products_extra_info['products_extra_info_value'];
                    }

                    $error = $this->process_recalculation($rule_id, $pid, $stock_quantity);
                }
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_OUT_OF_STOCK_MISSING_RULE_ID, 'error');
        }

        if (!is_null($error)) {
            if ($error) {
                $messageStack->add_session(ERROR_OUT_OF_STOCK_RECALCULATE_ERROR, 'error');
            } else {
                $messageStack->add_session(MESSAGE_OUT_OF_STOCK_RECALCULATE_SUCCESS, 'success');
            }
        }
    }

    private function process_recalculation($rule_id, $pid, $stock_quantity) {
        $error = false;

        $rule_cg_select_sql = " SELECT ofsrcg.out_of_stock_in_percentage, ofsrcg.customers_groups_id
                                FROM " . TABLE_OUT_OF_STOCK_CUSTOMERS_GROUPS_RULES . " AS ofsrcg 
                                WHERE ofsrcg.out_of_stock_rules_id = '" . tep_db_input($rule_id) . "'";
        $rule_cg_result_query = tep_db_query($rule_cg_select_sql);
        while ($customers_groups = tep_db_fetch_array($rule_cg_result_query)) {
            $customers_groups_id = $customers_groups['customers_groups_id'];
            $out_of_stock_in_percentage = $customers_groups['out_of_stock_in_percentage'];

            $sql_data_array = array(
                'purchase_limit' => tep_db_prepare_input(round(bcmul($stock_quantity, $out_of_stock_in_percentage, 2) / 100)),
                'out_of_stock_flag' => 0
            );

            $cg_purchase_control_select_sql = " SELECT cgpc.products_id, cgpc.customers_groups_id
                                                FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " AS cgpc
                                                WHERE cgpc.customers_groups_id = '" . (int) $customers_groups_id . "' AND  cgpc.products_id = '" . (int) $pid . "'";
            $cg_purchase_control_result_query = tep_db_query($cg_purchase_control_select_sql);
            if ($cg_purchase_control_result = tep_db_fetch_array($cg_purchase_control_result_query)) {
                if (!tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL, $sql_data_array, 'update', "customers_groups_id = '" . (int) $customers_groups_id . "' AND products_id = '" . (int) $pid . "'")) {
                    $error = true;
                }
            } else {
                $sql_data_array['customers_groups_id'] = $customers_groups_id;
                $sql_data_array['products_id'] = $pid;

                if (!tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL, $sql_data_array)) {
                    $error = true;
                }
            }
            
            # process update out of stock flag
            $this->updateOutOfStockFlag($pid);
        }

        return $error;
    }

    public function recalculate_out_of_stock_qty_by_upload_qty($pid, $upload_qty, &$messageStack) {
        $out_of_stock_rules_id = 0;
        $new_available_qty = 0;
        
        $products_extra_info_query = tep_db_query("SELECT products_extra_info_value from " . TABLE_PRODUCTS_EXTRA_INFO . " WHERE products_id= '" . (int) $pid . "' AND products_extra_info_key='out_of_stock_rules_id'");
        if ($products_extra_info = tep_db_fetch_array($products_extra_info_query)) {
            $out_of_stock_rules_id = $products_extra_info['products_extra_info_value'];
        }

        if ($out_of_stock_rules_id) {
            $products_qty_query = tep_db_query("SELECT products_quantity from " . TABLE_PRODUCTS . " WHERE products_id= '" . (int) $pid . "'");
            if ($products_qty_row = tep_db_fetch_array($products_qty_query)) {
                $new_available_qty = $products_qty_row['products_quantity'];
            }

            $new_available_qty = bcadd($new_available_qty, $upload_qty, 0);
            $sql_data_array = array('products_extra_info_value' => $new_available_qty);

            if (tep_db_perform(TABLE_PRODUCTS_EXTRA_INFO, $sql_data_array, 'update', "products_id = '" . (int) $pid . "' AND products_extra_info_key='stock_quantity'")) {
                $this->recalculate_by_pid($out_of_stock_rules_id, $pid, $messageStack);
            }
        }
        
        return $new_available_qty;
    }
    
    public function updateOutOfStockFlag($products_id, $new_product_quantity = null) {
        global $memcache_obj;
        
        if (is_null($new_product_quantity)) { 
            # Used to proess out of stock rules
            $product_stock_select_sql = "   SELECT products_quantity, products_actual_quantity
                                            FROM " . TABLE_PRODUCTS . "
                                            WHERE products_id = '" . tep_db_input($products_id) . "'";
            $product_stock_result_sql = tep_db_query($product_stock_select_sql);
            if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
                $new_product_quantity = $product_stock_row['products_quantity'];
            } else {
                $new_product_quantity = 0;
            }
        }

        # Out of Stock Rules
        if (!is_null($new_product_quantity)) {
            $cg_purchase_control_select_sql = " SELECT purchase_limit, customers_groups_id
                                                FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . " 
                                                WHERE  products_id = '" . tep_db_input($products_id) . "'";
            $cg_purchase_control_result_query = tep_db_query($cg_purchase_control_select_sql);
            while ($cg_purchase_control_result = tep_db_fetch_array($cg_purchase_control_result_query)) {
                if ($new_product_quantity <= $cg_purchase_control_result['purchase_limit']) {
                    $sql_data_array = array(
                        'out_of_stock_flag' => 1,
                        'out_of_stock_datetime' => 'now()'
                    );
                } else {
                   $sql_data_array = array('out_of_stock_flag' => 0);
                }

                tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL, $sql_data_array, 'update', "customers_groups_id = '" . (int) $cg_purchase_control_result['customers_groups_id'] . "' AND products_id = '" . (int) $products_id . "'");

                # Clear cache
                #key:customers_groups_purchase_control/products_id/xxx/customers_groups_id/xxx
		$memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_CUSTOMERS_GROUPS_PURCHASE_CONTROL . '/products_id/' . $products_id . '/customers_groups_id/' . $cg_purchase_control_result['customers_groups_id'], 0);
            }
        }
    }
	
	
	public function get_category_discount_rules_by_id($id) {
        $rule_cg_select_sql = "	SELECT cg.customers_groups_name, cg.customers_groups_id, ofsrcg.cdgr_discount, ofsrcg.cdgr_wor
                                        FROM " . TABLE_CUSTOMERS_GROUPS . " AS cg 
                                        LEFT JOIN " . TABLE_CATEGORY_DISCOUNT_GROUP_RULES . " AS ofsrcg ON cg.customers_groups_id=ofsrcg.cdgr_customer_group_id
                                        AND ofsrcg.cdrules_id = '" . tep_db_input($id) . "'
                                        ORDER BY cg.sort_order, cg.customers_groups_name";
        $rule_cg_result_sql = tep_db_query($rule_cg_select_sql);
        ob_start();
 
        echo '<table width="100%" border="0" cellspacing="1" cellpadding="1">';
        while ($customers_groups = tep_db_fetch_array($rule_cg_result_sql)) {
            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
            $customers_groups_id = $customers_groups['customers_groups_id'];
            $cdgr_discount = isset($customers_groups['cdgr_discount']) ? number_format($customers_groups['cdgr_discount'], 2, '.', '') : '0';
            $cdgr_wor = $customers_groups['cdgr_wor'];
            ?>
            <tr height="25px" class="<?php echo $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $row_style ?>')" onclick="rowClicked(this, '<?php echo $row_style ?>')">
                <td class="main"  align="center">
                    <?php
                    echo $customers_groups['customers_groups_name'];
                    ?>
                </td>
                <td class="main" align="center"> 
                    Discount: <?php echo $cdgr_discount; ?>
						OP: <?php echo $cdgr_wor; ?>
                </td>
            </tr>
            <?php
            $row_count++;
        }
        echo '</table>';
        $report_section_html = ob_get_contents();
        ob_end_clean();
        return $report_section_html;
    }
	
	
}
?>