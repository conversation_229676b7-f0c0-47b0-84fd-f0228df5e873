<?php

/*******************************************************************************
	RC4Crypt 3.0
	
	RC4 encryption / decryption class file
	@package RC4Crypt
********************************************************************************/

class rc4crypt {
	/*********************************************************
		Encrypts variable ($data), with key ($pwd)
	 	
		@param string $pwd maximum 256 characters in length
	 	@param string $data value to be encrypted
	**********************************************************/
	function encrypt($pwd, $data, $decrypt=0)
	{
		$key[] = '';
		$box[] = '';
		$cipher = '';
		
		$pwd_length = strlen($pwd);
		$data_length = strlen($data);
		
		for ($i=0; $i < 256; $i++) {
			$key[$i] = ord($pwd[$i % $pwd_length]);
			$box[$i] = $i;
		}
		
		for ($j = $i = 0; $i < 256; $i++) {
			$j = ($j + $box[$i] + $key[$i]) % 256;
			$tmp = $box[$i];
			$box[$i] = $box[$j];
			$box[$j] = $tmp;
		}
		
		if ($decrypt) {
			for ($a = $j = $i = 0; $i < $data_length; $i += 2) {
				$a = ($a + 1) % 256;
				$j = ($j + $box[$a]) % 256;
				
				$k = $box[(($box[$a] + $box[$j]) % 256)];
				
				$cipher .= chr(hexdec($data[$i] . $data[$i+1]) ^ $k);
			}
		} else {
			for ($a = $j = $i = 0; $i < $data_length; $i++) {
				$a = ($a + 1) % 256;
				$j = ($j + $box[$a]) % 256;
				
				$k = $box[(($box[$a] + $box[$j]) % 256)];
				//$cipher .= chr(ord($data[$i]) ^ $k);
				$cipher .= sprintf("%02X", ord($data[$i]) ^ $k);	// output in hexadecimal form
			}
		}
		return $cipher;
	}
	
	/********************************************************
	 	Decrypts variable ($data), using key ($pwd)
	 	
	 	@param string $pwd maximum 256 characters in length
	 	@param string $data value to be decrypted
	********************************************************/
	function decrypt ($pwd, $data)
	{
		return rc4crypt::encrypt($pwd, $data, 1);
	}
}
?>