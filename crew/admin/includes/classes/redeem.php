<?php

include_once(DIR_WS_CLASSES . 'store_point.php');

class redeem {
    var $identity, $identity_email, $user_roles, $currency_display_decimal;
	var $info, $redeem_info, $beneficiary;
	
	// class constructor
    function redeem($identity, $identity_email) {
      	$this->identity = $identity;	// Admin user
      	$this->identity_email = $identity_email;	// Admin user
      	
      	$this->user_roles = array (	'customers' => 'Customer',
									'supplier' => 'Supplier'
									);
		
		$this->info = array();
      	$this->redeem_info = array();
      	$this->beneficiary = array();
      	
		$this->currency_display_decimal = 2;
	}
	
	function search_acc_statement($filename, $session_name) {
		$acc_statement_html = '';
		
		$user_role_options = array 	(	array ('id' => 'customers', 'text' => 'Customer'),
										array ('id' => 'supplier', 'text' => 'Supplier')
									);
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
	  								array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
								);
		
		ob_start();
?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('payments_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '')?>
	        						<table border="0" cellspacing="0" cellpadding="0">
										<tr>
		          							<td class="main"><?=ENTRY_USER_ROLE?></td>
		          							<td class="main"><?=tep_draw_pull_down_menu("user_role", $user_role_options, tep_not_null($_SESSION[$session_name]["user_role"]) ? $_SESSION[$session_name]["user_role"] : '', 'id="user_role"')?></td>
				    						<td class="main">&nbsp;</td>
				    						<td class="main"><?=ENTRY_USER_EMAIL?></td>
				    						<td class="main">&nbsp;</td>
				    						<td class="main">
				    							<?=tep_draw_input_field('user_email', $_SESSION[$session_name]["user_email"], ' id="user_email" size="30" ')?>
				    							<a href="javascript:;" onClick="searchCustomersPopUp('user_email', payments_criteria.user_role.value == 'supplier' ? 'supplier_email_address' : 'customers_email_address')"><?=tep_image(DIR_WS_ICONS . 'search.jpg', IMAGE_ICON_SEARCH, 16, 16)?></a>
				    						</td>
				    						<td class="main" colspan="3">&nbsp;</td>
										</tr>
										<tr>
		            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
										<tr>
											<td class="main"><?=ENTRY_ORDER_START_DATE?></td>
											<td class="main" valign="top" nowrap>
	    										<script language="javascript"><!--
	  												var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "payments_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
	  												date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION[$session_name]["start_date"]?>';
												//--></script>
	    									</td>
	    									<td class="main" width="10%">&nbsp;</td>
	    									<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
	    									<td class="main">&nbsp;</td>
	    									<td class="main" valign="top" colspan="4">
				    							<script language="javascript"><!--
				  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "payments_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
				  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION[$session_name]["end_date"]?>';
												//--></script>
				    						</td>
										</tr>
										<tr>
		            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
		          							<td class="main"><?=ENTRY_PAYMENT_ID?></td>
		          							<td class="main"><?=tep_draw_input_field('payment_id', $_SESSION[$session_name]["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
				    						<td class="main">&nbsp;</td>
				    						<td class="main"><?=ENTRY_ORDER_ID?></td>
				    						<td class="main">&nbsp;</td>
				    						<td class="main"><?=tep_draw_input_field('order_id', $_SESSION[$session_name]["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											<td class="main"><?=ENTRY_TRANSACTION_ID?></td>
				    						<td class="main">&nbsp;</td>
				    						<td class="main"><?=tep_draw_input_field('transaction_id', $_SESSION[$session_name]["transaction_id"], ' id="transaction_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
										</tr>
										<tr>
		            						<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
											<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
							    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
							    			<td class="main">&nbsp;</td>
				    						<td colspan="6">
			  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
			  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
			  								</td>
										</tr>
		        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script language="javascript"><!--
				function form_checking(form_obj, action) {
				    //form_obj.submit();
					return true;
	    		}
	    		
				function resetControls(controlObj) {
					if (trim_str(controlObj.value) != '') {
						if (controlObj.id == 'payment_id') {
							document.payments_criteria.order_id.value = '';
							document.payments_criteria.transaction_id.value = '';
						} else if (controlObj.id == 'order_id') {
							document.payments_criteria.payment_id.value = '';
							document.payments_criteria.transaction_id.value = '';
						} else if (controlObj.id == 'transaction_id') {
							document.payments_criteria.payment_id.value = '';
							document.payments_criteria.order_id.value = '';
						}
						
						document.payments_criteria.user_email.value = '';
						document.payments_criteria.start_date.value = '';
						document.payments_criteria.end_date.value = '';
						document.payments_criteria.show_records.selectedIndex = 0;
						
		    		} else {
		    			controlObj.value = '';
		    		}
				}
	    	//-->
			</script>
<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $acc_statement_html;
	}
	
	function search_redeem_list($filename, $session_name) {
		$payment_list_html = '';
		
	  	$show_options = array 	(	array ('id' => 'DEFAULT', 'text' => 'Default'),
	  								array ('id' => '10', 'text' => '10'),
									array ('id' => '20', 'text' => '20'),
									array ('id' => '50', 'text' => '50'),
									array ('id' => 'ALL', 'text' => TEXT_ALL_PAGES)
								);
		
		$status_options = $this->_get_redeem_status();
		
		$date_type_array = array();
		
		$date_type_array[] = array ('id' => 0, 'text' => TEXT_ENTRY_DATE);
		
		$total_status_entry = count($status_options);
		
		for ($status_cnt=0; $status_cnt < $total_status_entry; $status_cnt++) {
			$date_type_array[] = array ('id' => $status_options[$status_cnt]['id'], 'text' => sprintf(TEXT_LAST_STATUS_DATE, $status_options[$status_cnt]['text']));
		}
		
		ob_start();
?>
	  	<table width="100%" border="0" cellpadding="2" cellspacing="0">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
						</tr>
						<tr>
        					<td>
        						<?=tep_draw_form('redeem_list_criteria', $filename, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main"><?=ENTRY_REDEEM_START_DATE?></td>
										<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION[$session_name]["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.redeem_list_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.redeem_list_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
    									<td class="main" width="8%">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=ENTRY_REDEEM_END_DATE?></td>
    									<td class="main">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION[$session_name]["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.redeem_list_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.redeem_list_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
    									<td valign="top"><?=tep_draw_separator('pixel_trans.gif', '40', '1') . tep_draw_pull_down_menu('date_type', $date_type_array, $_SESSION[$session_name]["date_type"], ' id="date_type" ')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
	          							<td class="main"><?=ENTRY_REDEEM_ID?></td>
	          							<td class="main" colspan="6"><?=tep_draw_input_field('redeem_id', $_SESSION[$session_name]['redeem_id'], ' id="redeem_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td valign="top" class="main"><?=ENTRY_CURRENT_REDEEM_STATUS?></td>
						    			<td class="main" colspan="6">
						    				<table border="0" cellspacing="2" cellpadding="0">
						    			<?
						    				if (count($status_options)) {
					    						echo '	<tr><td class="main">'.tep_draw_checkbox_field('redeem_status_any', '1', isset($_SESSION[$session_name]) ? (count($_SESSION[$session_name]['redeem_status']) ? false : true) : false, '', 'id="redeem_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>
					    								<tr>';
					    						for ($status_cnt=0; $status_cnt < count($status_options); $status_cnt++) {
					    							$order_status_display_str = '';
													$id = $status_options[$status_cnt]['id'];
							    					$title = $status_options[$status_cnt]['text'];
							    					
						    						$order_status_display_str .=
						    							'	<td class="main">'.
						    									tep_draw_checkbox_field('redeem_status[]', $id, isset($_SESSION[$session_name]) ? (is_array($_SESSION[$session_name]['redeem_status']) && in_array($id, $_SESSION[$session_name]['redeem_status']) ? true : false) : ( $id=="2" ? true : false), '', ' onClick="verify_status_selection();"') . '
						    								</td>
						    								<td class="main">'.$title.'</td>';
					    							echo $order_status_display_str;
						    					}
						    					echo '</tr>';
						    				}
						    			?>
						    				</table>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu('show_records', $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '')?></td>
						    			<td class="main">&nbsp;</td>
			    						<td colspan="4">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
		<script language="javascript"><!--
			function form_checking(form_obj, action) {
			    //form_obj.submit();
				return true;
    		}
    		
			function resetControls(controlObj) {
				if (trim_str(controlObj.value) != '') {
					if (controlObj.id == 'redeem_id') {
						jQuery('#start_date').val('');
						jQuery('#end_date').val('');
						
						jQuery('#redeem_status_any').attr('checked', true);
						
						var multi_status_select = document.redeem_list_criteria.elements['redeem_status[]'];
		    			
						for (i = 0; i < multi_status_select.length; i++) {
							multi_status_select[i].checked = false;
						}
					}
	    		} else {
	    			controlObj.value = '';
	    		}
			}
			
			function set_status_option(any_status_obj) {
    			var multi_status_select = document.redeem_list_criteria.elements['redeem_status[]'];
    			if (any_status_obj.checked == true) {
					for (i = 0; i < multi_status_select.length; i++) {
						multi_status_select[i].checked = false;
					}
    			} else {	// force to check if no any order status option is selected
    				var selected_count = 0;
    				
    				for (i = 0; i < multi_status_select.length; i++) {
    					if (multi_status_select[i].checked == true) {
							selected_count++;
						}
					}
					if (!selected_count) {
						any_status_obj.checked = true;
					}
    			}
    		}
			
    		function verify_status_selection() {
    			var multi_status_select = document.redeem_list_criteria.elements['redeem_status[]'];
    			var selected_count = 0;
    			
				for (i = 0; i < multi_status_select.length; i++) {
					if (multi_status_select[i].checked == true) {
						selected_count++;
					}
				}
				if (!selected_count) {
					jQuery('#redeem_status_any').attr('checked', true);
				} else {
					jQuery('#redeem_status_any').attr('checked', false);
				}
    		}
    		
    		set_status_option(jQuery('#redeem_status_any'));
    	//-->
		</script>
<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $acc_statement_html;
	}
	
	function show_redeem_list($filename, $session_name, $input_array, &$messageStack) {
		global $languages_id, $currencies;
		
		$pm_scope_js_array = array();
		$pay_scope_js_array = array();
		$result_display_criteria = array();
		
		if (!$_REQUEST['cont']) {
			$_SESSION[$session_name]['start_date'] = $input_array['start_date'];
			$_SESSION[$session_name]['end_date'] = $input_array['end_date'];
			$_SESSION[$session_name]['date_type'] = $input_array['date_type'];
			$_SESSION[$session_name]['redeem_id'] = $input_array['redeem_id'];
			$_SESSION[$session_name]['redeem_status'] = $input_array['redeem_status'];
			$_SESSION[$session_name]['show_records'] = $input_array['show_records'];
	  	}
	  	
	  	$search_by_status_date = false;
	  	
	  	$group_by_str = $having_str = '';
	  	
	  	$start_date_str = " 1 ";
	  	$end_date_str = " 1 ";
	  	$redeem_status_str = " 1 ";
	  	
	  	$sql_order_by_str = " ORDER BY sp.store_points_redeem_date DESC ";
	  	
	  	if (tep_not_null($_SESSION[$session_name]['redeem_id'])) {
	  		$sql_where_str = " WHERE sp.store_points_redeem_id = '" . (int)$_SESSION[$session_name]['redeem_id'] . "'";
	  		$sql_select_str = "SELECT * FROM " . TABLE_STORE_POINTS_REDEEM . " AS sp ";
	  	} else {
	  		if (tep_not_null($_SESSION[$session_name]['start_date'])) {
		  		$result_display_criteria['date']['from'] = $_SESSION[$session_name]['start_date'];
		  		
		  		if ((int)$_SESSION[$session_name]['date_type'] < 1) {
					if (strpos($_SESSION[$session_name]['start_date'], ':') !== false) {
						$startDateObj = explode(' ', trim($_SESSION[$session_name]['start_date']));
						list($yr, $mth, $day) = explode('-', $startDateObj[0]);
						list($hr, $min) = explode(':', $startDateObj[1]);
						
						$start_date_str = " ( sp.store_points_redeem_date >= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
						
						$start_date_str = " ( DATE_FORMAT(sp.store_points_redeem_date, '%Y-%m-%d') >= DATE_FORMAT('".date('Y-m-d', mktime(0, 0, 0, $mth, $day, $yr))."','%Y-%m-%d') )";
					}
				} else if ((int)$_SESSION[$session_name]['date_type'] > 0) {
					$search_by_status_date = true;
					
					if (strpos($_SESSION[$session_name]['start_date'], ':') !== false) {
						$startDateObj = explode(' ', trim($_SESSION[$session_name]['start_date']));
						list($yr, $mth, $day) = explode('-', $startDateObj[0]);
						list($hr, $min) = explode(':', $startDateObj[1]);
						
						$having_str .= " HAVING latest_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' ";
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]['start_date']));
						
						$having_str .= " HAVING DATE_FORMAT(latest_date, '%Y-%m-%d') >= DATE_FORMAT('".date('Y-m-d', mktime(0, 0, 0, $mth, $day, $yr))."','%Y-%m-%d') ";
					}
				}
			}
			
			if (tep_not_null($_SESSION[$session_name]['end_date'])) {
				$result_display_criteria['date']['to'] = $_SESSION[$session_name]['end_date'];
				
				if ((int)$_SESSION[$session_name]['date_type'] < 1) {
					if (strpos($_SESSION[$session_name]['end_date'], ':') !== false) {
						$endDateObj = explode(' ', trim($_SESSION[$session_name]['end_date']));
						list($yr, $mth, $day) = explode('-', $endDateObj[0]);
						list($hr, $min) = explode(':', $endDateObj[1]);
						
						$end_date_str = " ( sp.store_points_redeem_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
						$end_date_str = " ( DATE_FORMAT(sp.store_points_redeem_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0, 0, 0, $mth, $day, $yr))."','%Y-%m-%d') )";
					}
				} else if ((int)$_SESSION[$session_name]['date_type'] > 0) {
					$search_by_status_date = true;
					
					if (strpos($_SESSION[$session_name]['end_date'], ':') !== false) {
						$endDateObj = explode(' ', trim($_SESSION[$session_name]['end_date']));
						list($yr, $mth, $day) = explode('-', $endDateObj[0]);
						list($hr, $min) = explode(':', $endDateObj[1]);
						
						if (tep_not_null($having_str)) {
							$having_str .= " AND latest_date <= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' ";
						} else {
							$having_str .= " HAVING (latest_date <= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
						}
					} else {
						list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]['end_date']));
						
						if (tep_not_null($having_str)) {
							$having_str .= " AND DATE_FORMAT(latest_date, '%Y-%m-%d') <= DATE_FORMAT('".date('Y-m-d', mktime(0, 0, 0, $mth, $day, $yr))."','%Y-%m-%d') ";
						} else {
							$having_str .= " HAVING (DATE_FORMAT(latest_date, '%Y-%m-%d') <= DATE_FORMAT('".date('Y-m-d', mktime(0, 0 ,0, $mth, $day, $yr))."','%Y-%m-%d') )";
						}
					}
				}
			}
	  		
	  		if ($search_by_status_date) {
				$sql_select_str = "	SELECT sp.*, MAX( date_added ) AS latest_date 
									FROM " . TABLE_STORE_POINTS_REDEEM . " AS sp 
									INNER JOIN " . TABLE_STORE_POINTS_REDEEM_HISTORY . " AS sph 
										ON (sph.store_points_redeem_status = '" . (int)$_SESSION[$session_name]['date_type'] . "' AND sp.store_points_redeem_id = sph.store_points_redeem_id) ";
				$group_by_str = " GROUP BY store_points_redeem_id ";
			} else {
				$sql_select_str = "SELECT * FROM " . TABLE_STORE_POINTS_REDEEM . " AS sp ";
			}
	  		
	  		if (is_array($_SESSION[$session_name]['redeem_status']) && sizeof($_SESSION[$session_name]['redeem_status']) > 0) {
	  			if ($search_by_status_date) {
	  				$redeem_status_str = " sph.store_points_redeem_status IN (" . implode(',', $_SESSION[$session_name]['redeem_status']) . ")";
	  			} else {
	  				$redeem_status_str = " sp.store_points_redeem_status IN (" . implode(',', $_SESSION[$session_name]['redeem_status']) . ")";
	  			}
	  		}
	  		
	  		$sql_where_str .= " WHERE " . $start_date_str . " AND " . $end_date_str . " AND " . $redeem_status_str;
	  	}
	  	
	  	ob_start();
	  	
	  	$form_name = 'sp_lists_form';
  		$form_param = tep_get_all_get_params(array('action')) . 'action=batch_action';
	
	  	$store_points_redeem_select_sql = $sql_select_str . $sql_where_str . $group_by_str . $having_str . $sql_order_by_str;

        $show_records = $_SESSION[$session_name]['show_records'];
	  	if ($show_records != 'ALL') {
			$store_points_redeem_split_object = new splitPageResults($_REQUEST['page'], ($show_records == '' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $store_points_redeem_select_sql, $store_points_redeem_select_sql_numrows, true);
		}
	  	
		$store_points_redeem_result_sql = tep_db_query($store_points_redeem_select_sql);

		$result_display_text = TEXT_DISPLAY_NUMBER_OF_REDEEMS;
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="0">
			<tr>
				<td>
					<?=tep_draw_form($form_name, $filename, $form_param, 'post', 'enctype="multipart/form-data"')?>
						<table border="0" width="100%" cellspacing="1" cellpadding="1">
							<tr>
								<td width="8%" class="reportBoxHeading" nowrap><?=TABLE_HEADING_REDEEM_ID?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_REDEEM_DATE?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_REDEEM_USER?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_REDEEM_SP_STATEMENT?></td>
								<td width="12%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_REDEEM_TOTAL_POINTS_REDEEM?></td>
								<td width="12%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_REDEEM_AMOUNT?></td>
								<td width="13%" class="reportBoxHeading" align="center"><?=TABLE_HEADING_REDEEM_REFERENCE?></td>
								<td width="5%" class="reportBoxHeading">&nbsp;</td>
								<td width="10%" class="reportBoxHeading" align="center"><?=TABLE_HEADING_REDEEM_ACTION?></td>
								<td width="1%" class="reportBoxHeading"><?=tep_draw_checkbox_field('select_all_'.$redeem_id, '', false, '', 'id="select_all_'.$redeem_id.'" title="Select or deselect all redeem records" onClick="javascript:void(setActiveCheckboxes(\''.$form_name.'\',\'select_all_'.$redeem_id.'\',\'redeems_batch\')); "')?></td>
							</tr>
							<tbody>
<?
		$row_count = 0;
		
		while ($store_points_redeem_row = tep_db_fetch_array($store_points_redeem_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd';
			$redeem_id = $store_points_redeem_row['store_points_redeem_id'];
			$user_info_array = $this->_get_user_particulars($store_points_redeem_row['user_id'], $store_points_redeem_row['user_role']);
			$user_name_link = '<a href="'.tep_href_link(FILENAME_CUSTOMERS, 'cID='.$store_points_redeem_row['user_id'].'&action=edit').'" target="_blank">'.$store_points_redeem_row['user_firstname'] . ' ' . $store_points_redeem_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
			$sp_stat_link = sprintf(LINK_REDEEM_SP_STATEMENT, tep_href_link(FILENAME_STORE_POINT, 'action=show_report&customer_id='.urlencode($store_points_redeem_row['user_id']).'&start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL'));
			
			$redeem_batch_available = false;
			
			$actual_payout_amount = $store_points_redeem_row['store_points_request_currency_amount'];
			$rounded_actual_payout_amount = number_format(tep_round($actual_payout_amount, $this->currency_display_decimal), $this->currency_display_decimal, '.', '');
			
			switch($store_points_redeem_row['store_points_redeem_status']) {
				case '1':	// Pending
					$action_button_html = tep_button('Process', 'Process this payment', '', ' name="ProcessBtn_'.$redeem_id.'" onClick="updateRedeem(\''.$filename.'\', this, \''.$redeem_id.'\', \'1\', \'2\');" ', 'inputButton') . '&nbsp;';
					$redeem_batch_available = true;
					
					break;
				case '2':	// Processing
					$action_button_html = tep_button('Complete', 'Complete this payment', '', ' name="CompleteBtn_'.$redeem_id.'" onClick=" if (confirm(\''.JS_ALERT_REDEEM_CONFIRM_UPDATE.'\') != \'0\') { updateRedeem(\''.$filename.'\', this, \''.$redeem_id.'\', \'2\', \'3\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
					$redeem_batch_available = true;
					
					break;
				case '3':	// Completed
					$action_button_html = 'Completed';
					
					break;
				case '4':	// Canceled
					$action_button_html = 'Canceled';
					
					break;
				default:
					$action_button_html = '';
					
					break;
			}
			
			echo ' 				<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
									<td class="reportRecords" nowrap>' . $store_points_redeem_row['store_points_redeem_id'] . '</td>
									<td class="reportRecords" nowrap>' . $store_points_redeem_row['store_points_redeem_date'] . '</td>
									<td class="reportRecords" nowrap>' . $user_name_link . '</td>
									<td class="reportRecords" nowrap>' . $sp_stat_link . '</td>
									<td class="reportRecords" nowrap align="right">' . (int)$store_points_redeem_row['store_points_redeem_amount'] . '</td>
									<td class="reportRecords" nowrap align="right">' . $currencies->format($rounded_actual_payout_amount, false, $store_points_redeem_row['store_points_paid_currency']) . '</td>
									<td class="reportRecords" nowrap align="center">' . $store_points_redeem_row['store_points_redeem_reference'] . '</td>
									<td align="center" valign="top" class="reportRecords">
										<a href="' . tep_href_link($filename, 'redeemID='.$redeem_id.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS.'edit.gif', 'Edit', '14', '13', 'align="top"').'</a>
									</td>
									<td class="reportRecords" nowrap align="center">' . $action_button_html . '</td>
									<td class="reportRecords" valign="top">'.tep_draw_checkbox_field('redeems_batch[]', $redeem_id, false, '', 'id="'.$redeem_id.'"'.(!$redeem_batch_available ? ' DISABLED ' : '')).'</td>
								</tr>';
			
			$row_count++;
		}
		
		$batch_action_array = array(array('id' => '', 'text' => 'With selected:'));
		$batch_action_array[] = array('id' => 'Process', 'text' => 'Process Payment');
		$batch_action_array[] = array('id' => 'Complete', 'text' => 'Complete Payment');
		
		if (count($batch_action_array) > 1) {
			echo '				<tr>
									<td align="right" colspan="9">' . tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;' . tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction(this.form)"', 'inputButton') . '</td>
								</tr>';
		}
?>
							</tbody>
						</table>
					</form>
				</td>
			</tr>
			<tr>
				<td colspan="<?=$total_colspan?>">
					<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
	  					<tr>
	    					<td class="smallText" valign="top"><?=$show_records == 'ALL' ? sprintf($result_display_text, tep_db_num_rows($store_points_redeem_result_sql) > 0 ? "1" : "0", tep_db_num_rows($store_points_redeem_result_sql), tep_db_num_rows($store_points_redeem_result_sql)) : $store_points_redeem_split_object->display_count($store_points_redeem_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'], $result_display_text)?></td>
	    					<td class="smallText" align="right"><?=$show_records == 'ALL' ? sprintf(TEXT_DISPLAY_NUMBER_OF_REDEEMS, '1', '1') : $store_points_redeem_split_object->display_links($store_points_redeem_select_sql_numrows, ($show_records == '' || $show_records == 'DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont')) . 'cont=1', 'page')?></td>
	  					</tr>
					</table>
				</td>
			</tr>
		</table>
<?
	  	$parsed_report_section_html = ob_get_contents();
	  	
		ob_end_clean();
		ob_start();
?>
		<script language="javascript">
		<!--
			function confirmBatchAction(frmObj) {
				if (trim_str(frmObj.batch_action.value) == '') {
					alert('Please select your batch action!');
					return false;
				} else {
					var confirmation = confirm('Confirm to proceed with batch update?');
					
					if (confirmation == false) {
						frmObj.batch_action.selectedIndex = 0;
						return false;
					} else {
						return true;
					}
				}
			}
		//-->
		</script>
<?
	  	return $parsed_report_section_html;
	}
	
	
	function do_batch_action($filename, $input_array, &$messageStack) {
		global $currencies;
		
		$action_res_array = array('code' => 0);
		
		switch($input_array['batch_action']) {
    		case 'Process':
    			if (is_array($input_array['redeems_batch']) && count($input_array['redeems_batch'])) {
    				foreach($input_array['redeems_batch'] as $redeem_id) {
    					$redeem_current_info_select_sql = "	SELECT * 
															FROM " . TABLE_STORE_POINTS_REDEEM . " 
															WHERE store_points_redeem_id = '" . tep_db_input($redeem_id) . "'";
						$redeem_current_info_result_sql = tep_db_query($redeem_current_info_select_sql);
						if ($redeem_current_info_row = tep_db_fetch_array($redeem_current_info_result_sql)) {
							if ($redeem_current_info_row['store_points_redeem_status'] == 1) {	// Only 'Pending' redeem can be updated
								$user_id = $redeem_current_info_row['user_id'];
								$user_role = $redeem_current_info_row['user_role'];
								$user_info_row = $this->_get_user_particulars($user_id, $user_role);
								
								$redeem_update_sql_data_array = array(	'store_points_redeem_status' => 2,
																		'store_points_redeem_last_modified' => 'now()'
																	);
								
								tep_db_perform(TABLE_STORE_POINTS_REDEEM, $redeem_update_sql_data_array, 'update', "store_points_redeem_id = '" . tep_db_input($redeem_id) . "'");
								
								$redeem_history_sql_data_array = array(	'store_points_redeem_id' => $redeem_id,
					    												'store_points_redeem_status' => 2,
						    											'date_added' => 'now()',
						    											'payee_notified' => '1',
						    											'changed_by' => $_SESSION['login_email_address'],
						    											'changed_by_role' => 'admin'
				        											);
								tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $redeem_history_sql_data_array);
								
								$messageStack->add_session(sprintf(SUCCESS_REDEEM_UPDATE, $redeem_id), 'success');
								
								//Email to beneficiary
								//$this->send_redeem_status_email($redeem_id, '');
							} else {
								$messageStack->add_session(sprintf(WARNING_REDEEM_TRANSACTION_NOT_UPDATED, $redeem_id), 'warning');
							}
						}
    				}
    				
    				$action_res_array['code'] = '1';
    			}
				
    			break;
    		case 'Complete':
    			if (is_array($input_array['redeems_batch']) && count($input_array['redeems_batch'])) {
    				$xmlhttp_sc_object = new ms_store_credit($this->identity, $this->identity_email);
    				
    				foreach($input_array['redeems_batch'] as $redeem_id) {
    					$user_id_select_sql = "	SELECT spr.*, c.currencies_id 
												FROM " . TABLE_STORE_POINTS_REDEEM . " AS spr 
												INNER JOIN " . TABLE_CURRENCIES . " AS c 
													ON (c.code = spr.store_points_paid_currency) 
												WHERE spr.store_points_redeem_id = '" . tep_db_input($redeem_id) . "'";
    					$user_id_result_sql = tep_db_query($user_id_select_sql);
    					if ($redeem_current_info_row = tep_db_fetch_array($user_id_result_sql)) {
							if ($redeem_current_info_row['store_points_redeem_status'] == 2) {	// Only 'Processing' redeem can be updated
								$store_points_redeem_id = $redeem_current_info_row['store_points_redeem_id'];
								$user_id = $redeem_current_info_row['user_id'];
								$store_points_redeem_amount = $redeem_current_info_row['store_points_redeem_amount'];
								$store_points_request_currency = $redeem_current_info_row['store_points_request_currency'];
								$store_points_request_currency_amount = $redeem_current_info_row['store_points_request_currency_amount'];
								$store_points_paid_currency = $redeem_current_info_row['store_points_paid_currency'];
								$store_points_paid_currency_amount = $redeem_current_info_row['store_points_paid_currency_amount'];
								$redeem_to_currency_id = $redeem_current_info_row['currencies_id'];
								
								if ($store_points_request_currency == $store_points_paid_currency) {
									if ((double)$store_points_request_currency_amount > (double)$store_points_paid_currency_amount) {
										// get SC balance and currency
										$sc_currency_row = $xmlhttp_sc_object->getScBalance($user_id);
										
										if ($sc_currency_row) {
											$sc_currency_id = $currencies->get_id_by_code($sc_currency_row['currency']);
											$sc_currency_code = $sc_currency_row['currency'];
										} else {
											$sc_currency_id = $redeem_current_info_row['currencies_id'];
											$sc_currency_code = $currencies->get_code_by_id($redeem_current_info_row['currencies_id']);
										}

										$sc_redeem_amt = (double)$store_points_request_currency_amount;
											
										if ($redeem_to_currency_id != $sc_currency_id) {
											$exchange_rate = $currencies->advance_currency_conversion_rate('USD', $sc_currency_code, 'sell');
											$request_currency_amount = (($store_points_redeem_amount / 10000) * $exchange_rate);
											$sc_redeem_amt = $request_currency_amount;
											$redeem_to_currency_id = $sc_currency_id;
											
											$data_array_sql = array();
											
											$data_array_sql['store_points_request_currency'] = $sc_currency_code;
											$data_array_sql['store_points_request_currency_amount'] = $request_currency_amount;
											$data_array_sql['store_points_paid_currency'] = $sc_currency_code;
											$data_array_sql['store_points_exchange_rate'] = $exchange_rate;
											
											tep_db_perform(TABLE_STORE_POINTS_REDEEM, $data_array_sql, 'update', " store_points_redeem_id = '" . (int)$store_points_redeem_id . "'");
											
											unset($data_array_sql);
										}
										
										$trans_array = array();
										
										$trans_array['sp_redeem_id'] = $store_points_redeem_id;
										$trans_array['user_id'] = $user_id;
										$trans_array['sc_redeem_paid_currency'] = $sc_currency_code;
										$trans_array['sc_redeem_amt'] = $sc_redeem_amt;
										$trans_array['currency_id'] = $redeem_to_currency_id;
										
										if ($xmlhttp_sc_object->redeemOnStoreCredit($trans_array)) {
											$messageStack->add_session(sprintf(SUCCESS_REDEEM_UPDATE, $redeem_id), 'success');
											//Email to beneficiary
											$this->send_redeem_status_email($redeem_id, '');
										} else {
											$messageStack->add_session(sprintf(WARNING_REDEEM_TRANSACTION_NOT_UPDATED, $redeem_id .  'dfdf'), 'warning');
										}
										
										unset($trans_array);
									} else {
										$messageStack->add_session(sprintf(WARNING_REDEEM_TRANSACTION_NOT_UPDATED, $redeem_id .  'dfdf'), 'warning');
									}
								} else {
									; // TO BE CONTINUE
								}
							} else {
								$messageStack->add_session(sprintf(WARNING_REDEEM_TRANSACTION_NOT_UPDATED, $redeem_id .  'dfdf'), 'warning');
							}
						}
    				}
    				
    				$action_res_array['code'] = '1';
    			}
    			
    			break;
		}
		
		return $action_res_array;
	}
	
	function edit_redeem($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		$edit_redeem_html = '';
		$this->_get_redeem_info($input_array['redeemID']);
		
		$redeem_status_array = array();
		$payment_status_select_sql = "	SELECT store_payments_status_id, store_payments_status_name 
										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
										WHERE language_id = '" . (int)$languages_id . "' 
										ORDER BY store_payments_status_sort_order";
		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
		while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
			$redeem_status_array[$payment_status_row['store_payments_status_id']] = $payment_status_row['store_payments_status_name'];
		}
		
		ob_start();
  		
  		$form_name = 'edit_redeem_form';
  		$form_param = tep_get_all_get_params(array('action')) . 'action=update_redeem';
  		$form_cancel_param = tep_get_all_get_params(array('action')) . 'action=cancel_redeem';
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
			<tr>
				<td width="100%">
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
    						<td valign="top" width="80%">
    							<table width="100%" border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" width="15%"><b><?=ENTRY_REDEEM_ID?></b></td>
										<td class="main"><b><?=$input_array['redeemID']?></b></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_REDEEM_STATUS?></b></td>
										<td class="main"><?=$this->info['status_name']?></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_REDEEM_DATE_TIME?></b></td>
										<td class="main"><?=tep_datetime_short($this->info['redeem_date'], PREFERRED_DATE_TIME_FORMAT)?></td>
									</tr>
									<tr>
										<td class="main"><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
										<td class="main"><?=tep_datetime_short($this->info['last_modified'], PREFERRED_DATE_TIME_FORMAT)?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
      		<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
			<tr>
				<td><?=tep_draw_separator()?></td>
			</tr>
      		<tr>
				<td>
					<table width="100%" border="0" cellspacing="0" cellpadding="2">
  						<tr>
        					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_REDEEM_INFO?></b></td>
      					</tr>
      					<tr>
    						<td valign="top">
    							<table width="100%" border="0" cellspacing="0" cellpadding="2">
	              					<tr>
    									<td valign="top">
    										<table width="100%" border="0" cellspacing="0" cellpadding="2">
    											<tr>
	                								<td width="20%" class="main" valign="top"><b><?=ENTRY_REDEEM_REFERENCE?></b></td>
	                								<td class="main"><?=$this->redeem_info['redeem_reference']?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_POINTS_REDEEM_AMOUNT?></b></td>
	                								<td class="main"><?=$this->redeem_info['redeem_amount']?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_REDEEM_SC_REQUEST_AMOUNT?></b></td>
	                								<td class="main"><?=$this->redeem_info['redeem_request_currency_amount']?></td>
	              								</tr>
	              								<tr>
	                								<td class="main" valign="top"><b><?=ENTRY_REDEEM_SC_PAID_AMOUNT?></b></td>
	                								<td class="main"><?=$this->redeem_info['redeem_paid_currency_amount']?></td>
	              								</tr>
	              							</table>
	              						</td>
	              					</tr>
	              				</table>
	              			</td>
	              		</tr>
      				</table>
      			</td>
      		</tr>
      		<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
			<tr>
    			<td><?=tep_draw_separator()?></td>
  			</tr>
  			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			</tr>
  			<tr>
		  		<td class="main">
					<table border="1" cellspacing="0" cellpadding="5">
  						<tr>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_BENEFICIARY_NOTIFIED?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
    						<td class="smallText" align="center"><b><?=TABLE_HEADING_CHANGED_BY?></b></td>
  						</tr>
<?
	$store_points_redeem_history_select_sql = "	SELECT * 
												FROM " . TABLE_STORE_POINTS_REDEEM_HISTORY . " 
												WHERE store_points_redeem_id = '" . $input_array['redeemID'] . "' 
												ORDER BY date_added";
	$store_points_redeem_history_result_sql = tep_db_query($store_points_redeem_history_select_sql);
	while ($store_points_redeem_history_row = tep_db_fetch_array($store_points_redeem_history_result_sql)) {
		$formatted_date_comment_added = tep_datetime_short($store_points_redeem_history_row['date_added'], PREFERRED_DATE_TIME_FORMAT);
		$img_str = ($store_points_redeem_history_row['payee_notified'] == '1') ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
?>
						<tr>
    						<td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
    						<td class="smallText" align="center"><?=$img_str?></td>
    						<td class="smallText" align="center"><?=isset($redeem_status_array[$store_points_redeem_history_row['store_points_redeem_status']]) ? $redeem_status_array[$store_points_redeem_history_row['store_points_redeem_status']] : '--'?></td>
    						<td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $store_points_redeem_history_row['comments']))?>&nbsp;</td>
    						<td class="smallText" align="center"><?=nl2br(tep_db_output($store_points_redeem_history_row['changed_by']))?>&nbsp;</td>
  						</tr>
<?	} ?>
					</table>
  				</td>
		  	</tr>
		  	<tr>
		  		<td>
<?	echo tep_draw_form($form_name, $filename, $form_param, 'post', '');
	echo tep_draw_hidden_field('status_DB_prev', $this->info['status']);
	
	if (isset($_SESSION[$session_name])) {
		$back_btn_url = tep_href_link($filename, 'action=show_report&cont=1');
	} else {
		$back_btn_url = tep_href_link($filename);
	}
?>
		  			<table border="0" cellspacing="0" cellpadding="2">
		  				<tr>
		  					<td class="main" colspan="4"><b><?=ENTRY_REDEEM_REMARK?></b></td>
		  				</tr>	
		  				<tr>
		  					<td class="main" colspan="3"><?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5')?></td>
		  					<td class="main">&nbsp;</td>
		  				</tr>
		  				<tr>
		  					<td width="135px" class="main"><b><?=ENTRY_REDEEM_NOTIFY_BENEFICIARY?></b></td>
		  					<td class="main"><?=tep_draw_checkbox_field('notify', '1', false, '', 'id=notify')?></td>
		  					<td align="right" class="main">
		  						<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="RedeemUpdateBtn"', 'inputButton')?>
		  					</td>
		  					<td align="right" class="main">&nbsp;&nbsp;
		  					<?
		  						if ($this->info['status'] == '1' || $this->info['status'] == '2') {
		  							echo tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="RedeemCancelBtn" onClick="if (confirm(\'Are you sure to cancel this redeem?\') != \'0\') { return true; } else { return false; }"', 'inputButton');
		  						}
		  					?>
		  					</td>
		  				</tr>
		  				<tr>
		  					<td class="main" colspan="4">&nbsp;</td>
		  				</tr>
		  				<tr>
		  					<td align="left" class="main" colspan="4">
		  						<?=tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, $back_btn_url, '', 'inputButton')?>
		  					</td>
		  				</tr>
		  			</table>
		  		</form>
		  		</td>
		  	</tr>
		</table>
<?
		$edit_redeem_html .= "\n" . ob_get_contents();
		ob_end_clean();
		
	  	return $edit_redeem_html;
	}
	
	function update_redeem($filename, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		if (isset($input_array['redeemID'])) {
			$store_points_redeem_status_select_sql = "	SELECT store_points_redeem_status 
														FROM " . TABLE_STORE_POINTS_REDEEM . " 
														WHERE store_points_redeem_id = '" . (int)$input_array['redeemID'] . "'";
			$store_points_redeem_status_result_sql = tep_db_query($store_points_redeem_status_select_sql);
			if ($store_points_redeem_status_row = tep_db_fetch_array($store_points_redeem_status_result_sql)) {
				if ($input_array['status_DB_prev'] != $store_points_redeem_status_row['store_points_redeem_status']) {
	    			$messageStack->add_session(WARNING_REDEEM_UPDATED_BY_SOMEONE, 'warning');
	    		} else {
	    			if (isset($input_array['RedeemUpdateBtn'])) {
	    				if (tep_not_null($input_array['admin_comment'])) {
	    					$sp_redeem_history_data_sql = array (	'store_points_redeem_id' => $input_array['redeemID'],
	    															'store_points_redeem_status' => 4,
	    															'date_added' => 'now()',
	    															'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
	    															'comments' => tep_db_prepare_input($input_array['admin_comment']),
	    															'changed_by' => $this->identity_email,
									                            	'changed_by_role' => 'admin'
									                            );
							tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sp_redeem_history_data_sql);
	    				}
	    			} else if (isset($input_array['RedeemCancelBtn'])) {
	    				if ($store_points_redeem_status_row['store_points_redeem_status'] == '1' || $store_points_redeem_status_row['store_points_redeem_status'] == '2') {
	    					$this->_get_redeem_info($input_array['redeemID']);
	    					
	    					$sp_redeem_data_sql = array(	'store_points_redeem_status' => 4,
	    													'store_points_redeem_last_modified' => 'now()'
	    												);
	    					tep_db_perform(TABLE_STORE_POINTS_REDEEM, $sp_redeem_data_sql, 'update', "store_points_redeem_id = '" . (int)$input_array['redeemID'] . "'");
	    					
	    					$sp_redeem_history_data_sql = array('store_points_redeem_id' => $input_array['redeemID'],
	    														'store_points_redeem_status' => 4,
	    														'date_added' => 'now()',
	    														'payee_notified' => isset($input_array['notify']) && $input_array['notify'] > 0 ? '1' : '0',
	    														'comments' => tep_db_prepare_input($input_array['admin_comment']),
	    														'changed_by' => $this->identity_email,
									                            'changed_by_role' => 'admin'
									                            );
	    					tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sp_redeem_history_data_sql);
	    					
	    					$update_info = array(	array(	'field_name' => 'sp_amount',
															'operator' => '+',
															'value' => $this->redeem_info['redeem_amount'])
													);
							
	    					$store_point_obj = new store_point($this->identity, $this->identity_email);
	    					$new_sp_balance = $store_point_obj->_set_store_point_balance($this->beneficiary['id'], $update_info);
	    					
							$sp_balance_history_data_array = array(	'customer_id' => tep_db_prepare_input($this->beneficiary['id']),
											                        'store_points_history_date' => 'now()',
											                        'store_points_history_debit_amount' => 'NULL',
										    	                    'store_points_history_credit_amount' => (double)$this->redeem_info['redeem_amount'],
																	'store_points_history_after_balance' => (double)$new_sp_balance['sp_amount'],
										                    	    'store_points_history_trans_id' => $input_array['redeemID'],
										                    	    'store_points_history_activity_type' => LOG_SP_ACTIVITY_TYPE_CANCEL,
										                    	    'store_points_history_activity_title' => sprintf(LOG_REDEEM_CANCELLATION, $input_array['redeemID']),
																	'store_points_history_activity_desc' => tep_db_prepare_input($input_array['admin_comment']),
																	'store_points_history_activity_desc_show' => 0,
										                        	'store_points_history_added_by' => $this->identity_email,
										                        	'store_points_history_added_by_role' => 'admin'
											                       );
							tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_balance_history_data_array);
	    				}
	    			}
	    		}
			}
		}
	}
	
	function _get_redeem_info($redeem_id) {
		global $languages_id, $currencies;
		
		$redeem_info_select_sql = "	SELECT * 
									FROM " . TABLE_STORE_POINTS_REDEEM . " 
									WHERE store_points_redeem_id = '" . (int)$redeem_id . "'";
		$redeem_info_result_sql = tep_db_query($redeem_info_select_sql);
		if ($redeem_info_row = tep_db_fetch_array($redeem_info_result_sql)) {
			$redeem_status_select_sql = "	SELECT store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
	  										WHERE store_payments_status_id = '" . tep_db_input($redeem_info_row['store_points_redeem_status']) . "' 
	  											AND language_id = '" . (int)$languages_id  . "'";
	  		$redeem_status_result_sql = tep_db_query($redeem_status_select_sql);
	  		$redeem_status_row = tep_db_fetch_array($redeem_status_result_sql);
	  		
	  		$this->info = array('redeem_id' => $redeem_id,
								'redeem_date' => tep_datetime_short($redeem_info_row['store_points_redeem_date'], PREFERRED_DATE_TIME_FORMAT),
								'status' => $redeem_info_row['store_points_redeem_status'],
								'status_name' => $redeem_status_row['store_payments_status_name'],
								'last_modified' => tep_datetime_short($redeem_info_row['store_points_redeem_last_modified'], PREFERRED_DATE_TIME_FORMAT)
								);
			
			$this->beneficiary = array(	'id' => $redeem_info_row['user_id'],
	      								'role' => $redeem_info_row['user_role'],
	      								'firstname' => $redeem_info_row['user_firstname'],
	      								'lastname' => $redeem_info_row['user_lastname'],
	      								'email_address' => $redeem_info_row['user_email_address'],
	      								'telephone' => $redeem_info_row['user_country_international_dialing_code'].$redeem_info_row['user_telephone'],
	      								'mobile' => $redeem_info_row['user_mobile']
	      								);
			
			$this->redeem_info = array(	'redeem_reference' => (tep_not_null($redeem_info_row['store_points_redeem_reference']) ? $redeem_info_row['store_points_redeem_reference'] : TEXT_NOT_AVAILABLE),
										'redeem_amount' => (int)$redeem_info_row['store_points_redeem_amount'],
										'redeem_request_currency' => $redeem_info_row['store_points_request_currency'],
										'redeem_request_currency_amount' => $currencies->format($redeem_info_row['store_points_request_currency_amount'], false, $redeem_info_row['store_points_request_currency']),
										'redeem_paid_currency' => $redeem_info_row['store_points_paid_currency'],
										'redeem_paid_currency_amount' => $currencies->format($redeem_info_row['store_points_paid_currency_amount'], false, $redeem_info_row['store_points_paid_currency'])
										);
		}
	}
	
	function _get_redeem_status() {
		global $languages_id;
		
		$status_array = array();
		
  		$payment_status_select_sql = "	SELECT store_payments_status_id, store_payments_status_name 
  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
  										WHERE language_id = '" . (int)$languages_id  . "' 
  										ORDER BY store_payments_status_sort_order";
  		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
  		while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
  			$status_array[] = array('id' => $payment_status_row["store_payments_status_id"], 'text' => $payment_status_row["store_payments_status_name"]);
  		}
		
		return $status_array;
	}
	
	function send_redeem_status_email($redeemID, $comment = '') {
		global $currencies;
		
		$this->_get_redeem_info($redeemID);
		
		$user_particulars_array = $this->_get_user_particulars($this->beneficiary['id'], $this->beneficiary['role']);
		
		// Send email to payee
		
		$email = 	EMAIL_REDEEM_TEXT_TITLE . "\n\n" .
					EMAIL_REDEEM_TEXT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
				 	sprintf(EMAIL_REDEEM_TEXT_REDEEM_NUMBER, $redeemID) . "\n" .
				 	sprintf(EMAIL_REDEEM_TEXT_PAID_AMOUNT, $this->redeem_info['redeem_paid_currency_amount']) . ' (' . number_format($this->redeem_info['redeem_amount'], 0, '.', ',') . ' WOR)' . "\n\n" .
				 	sprintf(EMAIL_REDEEM_TEXT_COMMENTS, tep_db_prepare_input($comment)) . "\n\n" .
				 	EMAIL_TEXT_CLOSING . "\n\n" .
				 	EMAIL_FOOTER;
		
		$email_greeting = tep_get_email_greeting($user_particulars_array['fname'], $user_particulars_array['lname'], $user_particulars_array['gender']);
		$email = $email_greeting . $email;
		
		@tep_mail($user_particulars_array['fname'].' '.$user_particulars_array['lname'], $this->beneficiary['email_address'], sprintf(EMAIL_REDEEM_REDEEM_UPDATE_SUBJECT, $redeemID), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	
	function _get_user_particulars($user_id, $user_role) {
		$user_info_row = array();
		if ($user_role == 'supplier') {
			$user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_gender AS gender, 'S' as sign_up_from 
										FROM " . TABLE_SUPPLIER . " 
										WHERE supplier_id = '" . tep_db_input($user_id) . "'";
		} else {
			$user_info_select_sql = "	SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email, customers_gender AS gender, ci.customers_info_account_created_from AS sign_up_from 
										FROM " . TABLE_CUSTOMERS . " AS c 
										LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
											ON c.customers_id=ci.customers_info_id
										WHERE customers_id = '" . tep_db_input($user_id) . "'";
		}
		
		$user_info_result_sql = tep_db_query($user_info_select_sql);
		$user_info_row = tep_db_fetch_array($user_info_result_sql);
		
		if ($user_info_row['sign_up_from'] == '0') {
			$user_info_row['sign_up_from'] = 'C';
		} else if ($user_info_row['sign_up_from'] == '1') {
			$user_info_row['sign_up_from'] = 'CN';
		}
		
		return $user_info_row;
	}
	
	function _check_store_point_got_balance($user_id) {
		$redeem_current_info_select_sql = "	SELECT * 
											FROM " . TABLE_STORE_POINTS_REDEEM . " 
											WHERE store_points_redeem_id = '" . tep_db_input($redeem_id) . "'";
		$redeem_current_info_result_sql = tep_db_query($redeem_current_info_select_sql);
		if ($redeem_current_info_row = tep_db_fetch_array($redeem_current_info_result_sql)) {
			if ((double)$sp_amount_row['sp_amount'] > 0) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
}
?>