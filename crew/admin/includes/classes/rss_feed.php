<?php

include_once(DIR_FS_CATALOG . 'includes/classes/localization.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');
include_once(DIR_WS_CLASSES . 'rss_generator.php');

class rss_feed {

    private $localization_obj;
    private $zone_id;
    private $zone_info_array = array();
    public $category_array = array();
    private $customer_group_id = 1;
    public $category_id;
    public $language_id;
    public $front_end_host = HTTPS_CATALOG_SERVER;
    public $xml_title = '';
    public $game_region_description_array = array();
    public $map_category_array = array();
    public $map_category_region_array = array();

    public function __construct() {
        ;
    }
    
    public function generate_feed($service) {
        if ($this->map_category_array) {
            $game_region_to_category_array = $this->map_category_array;
        } else {
            $game_region_array = $this->getRegionsId($this->map_category_region_array);
            $game_region_to_category_array = $this->getRegionToCategories($game_region_array);
        }
        
        $product_data = $this->getAllActiveProductCategoryIDByGameID($game_region_to_category_array);
        $feed_obj = new rss_generator("Offgamers", HTTPS_CATALOG_SERVER, $this->xml_title);
        
        if ($service == 'price_panda') {
             //nothing now
            
        } else if ($service == 'allcdkey') {
            //$this->localization_obj = new localization($this->country);
            //$this->zone_id = $this->localization_obj->get_zone_id(3);
            //$this->zone_info_array = $this->get_zone_info_by_id($zone_id);
            //$this->category_array = $this->getZoneCategoriesID();
            //$customer_zone_category_ids_array = $this->filterCategoryIDByGroupID($this->category_array);
            //$product_data=$this->getAllActiveProductCategoryIDByGameID($customer_zone_category_ids_array);

            $feed_obj->fieldmap("products_id", "products_id");
            $feed_obj->fieldmap("game_name", "game_name");
            $feed_obj->fieldmap("link", "link");
            $feed_obj->fieldmap("formatted_price", "formatted_price");
            $feed_obj->fieldmap("availability", "availability");
        } else if ($service == 'criteo') {
            $feed_obj->fieldmap("products_id", "products_id");
            $feed_obj->fieldmap("game_name", "game_name");
            $feed_obj->fieldmap("link", "link");
            $feed_obj->fieldmap("formatted_price", "formatted_price");
            $feed_obj->fieldmap("availability", "availability");
        }
      
        $feed_obj->generateRssFile($product_data, $service . '.xml', $service);
    }

    private function getRegionsId($region_info = array()) {
        $return_array = array();
        
        if($region_info) {
            $comma_separated = implode("','", $region_info);
            $comma_separated = "'" . $comma_separated . "'";
            $sql = "SELECT game_region_id FROM " . TABLE_GAME_REGION_DESCRIPTION . " WHERE  game_region_description IN (" . $comma_separated . ") AND language_id=1";
        } else {
            $sql = "SELECT game_region_id FROM " . TABLE_GAME_REGION_DESCRIPTION . " WHERE language_id=1";
        }
        
        $sql_result = tep_db_query($sql);
        while ($dataset = tep_db_fetch_array($sql_result)) {
            $return_array[] = $dataset['game_region_id'];
        }
        return $return_array;
    }

    private function getRegionToCategories($game_region_id = array()) {
        $return_array = array();

        $sql = "SELECT ft.id AS categories_id, fttgi.game_info_id AS game_region_id
        FROM  frontend_template ft
        INNER JOIN frontend_template_to_game_info fttgi ON ft.tpl_id = fttgi.tpl_id
        WHERE fttgi.game_info_type =  'game_region'
        AND ft.tpl_status =1
        AND fttgi.game_info_id IN (" . implode(',', $game_region_id) . ") ";

        $sql_result = tep_db_query($sql);
        while ($dataset = tep_db_fetch_array($sql_result)) {
            $return_array[$dataset['game_region_id']][] = $dataset['categories_id'];
        }

        return $return_array;
    }

    public function getSingleProductStockStatus($product_info, $purchase_qty = NULL) {
        $status = '';

        if ($product_info) {
            switch ($product_info["products_purchase_mode"]) {
                case '1':
                    $status = 'In stock';
                    break;
                case '2':
                    $status = 'In stock';
                    break;
                case '3':
                    $status = 'Out of stock';
                    break;
                case '4':
                    $status = 'Auto';

                    if ($this->notNull($purchase_qty)) {
                        if ($this->notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_out_of_stock_level"] < $purchase_qty) { // If there is setting for out of stock level 
                            $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                            $status = 'Out of stock';
                        } else if ($this->notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_pre_order_level"] < $purchase_qty) {
                            #Test on Pre-order
                            $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                            $status = 'In stock';
                        } else {
                            if ($product_info["products_pre_order_level"]) {
                                $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                            } else if ($product_info["products_out_of_stock_level"]) {
                                $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                            } else {
                                $available_stock = $product_info['products_quantity'];
                            }

                            $status = 'In stock';
                        }
                    }
                    break;
            }
        }

        return $status;
    }

    public function get_zone_info_by_id($zones_id) {
        $zone_setting_result = array();
        require_once(DIR_WS_CLASSES . 'json.php');
        $json = new Services_JSON();
        $zone_info_select_sql = "	SELECT geo_zone_info
									FROM " . TABLE_ZONES_INFO . "
										WHERE geo_zone_id = '" . tep_db_input($zones_id) . "'";
        $zones_info_result = tep_db_query($zone_info_select_sql);
        $zones_info_row = tep_db_fetch_array($zones_info_result);

        $zone_setting_result = $json->decode($zones_info_row['geo_zone_info']);

        if (isset($zone_setting_result->zone_categories_id)) {
            $_all_game = array();
            foreach ($zone_setting_result->zone_categories_id as $categories_id) {
                $categories_select_sql = "	SELECT c.categories_id
                                    FROM " . TABLE_CATEGORIES . " AS c
                                    WHERE c.categories_id=" . $categories_id . " AND c.categories_status = 1";
                $categories_result_sql = tep_db_query($categories_select_sql);
                if (tep_db_num_rows($categories_result_sql) > 0) {
                    $_all_game[] = $categories_id;
                }
            }
            $zone_setting_result->zone_categories_id = $_all_game;
            unset($_all_game);
        }

        if (isset($zone_setting_result->zone_currency_id)) {
            require_once('../' . DIR_WS_CLASSES . 'currencies.php');
            $currencies = new currencies();

            $zone_setting_result->zone_currency_id = array_intersect($zone_setting_result->zone_currency_id, $currencies->internal_currencies);

            if ($currencies->is_set($zone_setting_result->zone_default_currency_id) !== TRUE) {
                $zone_setting_result->zone_default_currency_id = DEFAULT_CURRENCY;
            }

            unset($currencies);
        }

        return $zone_setting_result;
    }

    public function getZoneCategoriesID($type = 'zone_categories_id') {
        $return_array = array();
        // 1: Games & Product
        $zone_game_obj = $this->getZoneInfoByType(1);
        if (is_object($zone_game_obj) && is_array($zone_game_obj->$type) && !empty($zone_game_obj->$type)) {
            $return_array = $zone_game_obj->$type;
        }
        unset($zone_game_obj);
        return $return_array;
    }

    private function getZoneInfoByType($zone_type) {
        if (!isset($this->zone_info_array[$zone_type])) {
            $zones_id = $this->localization_obj->get_zone_id($zone_type);
            $this->zone_info_array[$zone_type] = new stdClass();
            $this->zone_info_array[$zone_type]->zone_id = $zones_id;
            $this->zone_info_array[$zone_type] = $this->get_zone_info_by_id($zones_id);
        }

        return $this->zone_info_array[$zone_type];
    }

    private function filterCategoryIDByGroupID($game_ids_array, $customers_groups_id = NULL) {
        $return_array = array();
        $customers_groups_id = $this->notNull($customers_groups_id) ? $customers_groups_id : $this->customer_group_id;
        if ($game_ids_array) {
            if ($this->countDimension($game_ids_array) > 1) {
                foreach ($game_ids_array as $idx => $game_ids) {
                    $return_array[$idx] = $this->filterCategoryIDByGroupID($game_ids, $customers_groups_id);
                }
            } else {
                $group_game_ids_array = $this->getCustomerGroupCategoryIDs($customers_groups_id);
                $return_array = array_intersect($game_ids_array, $group_game_ids_array);
            }
        }

        return $return_array;
    }

    public function getCustomerGroupCategoryIDs($customers_groups_id) {
        $result_array = array();
        $result_array = $this->getGameIDsByGroupID($customers_groups_id);
        return $result_array;
    }

    public function getGameIDsByGroupID($group_id) {
        $return_array = array();
        $sql = "SELECT categories_id FROM " . TABLE_CATEGORIES_GROUPS . " WHERE groups_id = $group_id";
        $sql_result = tep_db_query($sql);
        while ($dataset = tep_db_fetch_array($sql_result)) {
            $return_array[$dataset['categories_id']] = $dataset['categories_id'];
        }
        $sql1 = "SELECT categories_id FROM " . TABLE_CATEGORIES_GROUPS . " WHERE groups_id = 0";
        $sql_result1 = tep_db_query($sql1);
        while ($dataset = tep_db_fetch_array($sql_result1)) {
            $return_array[$dataset['categories_id']] = $dataset['categories_id'];
        }
        return $return_array;
    }

    private function getAllActiveProductCategoryIDByGameID($game_info_array) {
        $product_types_id = 2;
        $return_array = array();
        $game_id_region = 0;
        foreach ($game_info_array as $game_id_key => $game_id_value) {
            $game_id_region = (string) $game_id_key;
            
            if (is_array($game_id_value)) {
                foreach ($game_id_value as $game_id_key1 => $game_id_value1) {
                    $category_info_array = $this->getCategoryInfoByGameID($game_id_value1, $product_types_id);
                    if ($product_array = $this->getAllProductByGameID($game_id_value1, $category_info_array, $game_id_region)) {
                        $return_array[$game_id_value1] = $product_array;
                    }
                }
            } else {
                $category_info_array = $this->getCategoryInfoByGameID($game_id_value, $product_types_id);
                if ($product_array = $this->getAllProductByGameID($game_id_value, $category_info_array)) {
                    $return_array[$game_id_value] = $product_array;
                }
            }
        }
        return $return_array;
    }

    public function getCategoryInfoByGameID($game_id, $product_types_id) {
        $return_array = array();

        $result_array = $this->getSubcategoriesByCPathFromDB($game_id, $product_types_id, 1);

        foreach ($result_array as $subcategory_array) {
            $subcategory_array['cpath'] = $subcategory_array['categories_parent_path'] ? substr($subcategory_array['categories_parent_path'], 1) . $subcategory_array['categories_id'] : $subcategory_array['categories_id'];
            $return_array = $this->getSubcategoriesByCPath($subcategory_array, $return_array);
        }

        return $return_array;
    }

    public function getAllProductByGameID($game_id, $category_info_array, $game_region_id = NULL) {
        $return_array = array();

        $return_array = $this->getCategories($game_id, $category_info_array, $game_region_id);

        return $return_array;
    }

    private function getCategories($game_id, $category_info_array, $game_region_id = NULL) {
        $return_array = array();
        foreach ($category_info_array as $category_info) {

            $category_info['game_id'] = $game_id;
            $return_array = $this->getAllProductsByCategoryID($category_info['categories_id'], 1, 1, $return_array, $game_region_id);
        }

        //ksort($return_array);
        return $return_array;
    }

    public function getAllProductsByCategoryID($category_id, $status = '', $display_status = '', & $return_array = array(), $game_region_id = NULL) {
        $sql = "SELECT p.products_id,pd.products_name AS game_name,p.products_url_alias AS link, p.products_base_currency AS currency_by_region,
                    p.products_price AS formatted_price,
                    p.products_sort_order,p.products_purchase_mode,p.products_quantity,p.products_out_of_stock_level,p.products_pre_order_level FROM " . TABLE_PRODUCTS . "  p
                    INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " ptc ON p.products_id = ptc.products_id
                    INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd ON pd.products_id = p.products_id
                    WHERE ptc.categories_id =$category_id
                    AND p.products_display =$status
                    AND p.products_status =$display_status AND  pd.language_id =1";

        $sql_result = tep_db_query($sql);
        while ($dataset = tep_db_fetch_array($sql_result)) {
            $dataset['link'] = $this->front_end_host . 'buynow/' . $dataset['link'] . '.html';
            $return_array[$dataset['products_id']] = $dataset;
            $products_info['products_purchase_mode'] = $dataset['products_purchase_mode'];
            $products_info['products_quantity'] = $dataset['products_quantity'];
            $products_info['products_out_of_stock_level'] = $dataset['products_out_of_stock_level'];
            $products_info['products_pre_order_level'] = $dataset['products_pre_order_level'];
            $return_array[$dataset['products_id']]['availability'] = $this->getSingleProductStockStatus($products_info, 1);
        }

        return $return_array;
    }

    public function getSubcategoriesByCPathFromDB($game_id, $custom_products_type_id, $status = '') {
        $return_array = array();
        $category_path = '\_' . str_replace('_', '\_', $this->getCPathByCategoryID($game_id)) . '\_%';
        $cat_sql = "SELECT categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id, sort_order
                    FROM " . TABLE_CATEGORIES . "
                    WHERE categories_parent_path LIKE '" . $category_path . "'
                    AND custom_products_type_id =$custom_products_type_id AND
                    categories_status =$status";

        $cat_sql_result = tep_db_query($cat_sql);
        if ($dataset = tep_db_fetch_array($cat_sql_result)) {
            $return_array[] = $dataset;
        }
        return $return_array;
    }

    public function getCPathByCategoryID($categories_id) {
        $return_string = '';
        $sql = "SELECT categories_parent_path,categories_id FROM " . TABLE_CATEGORIES . " WHERE categories_id=$categories_id";
        $sql_result = tep_db_query($sql);
        if ($result = tep_db_fetch_array($sql_result)) {
            $return_string = trim($result['categories_parent_path'] . $result['categories_id'], '_');
        }

        return $return_string;
    }

    public function getSubcategoriesByCPath($category_info_array, $return_array = array()) {
        $return_array[] = $category_info_array;
        $forth_layer_id_array = $this->getSubcategoriesByCPathFromDB1($category_info_array['cpath'], 1);
        foreach ($forth_layer_id_array as $category_info) {
            $return_array[] = $category_info;
        }

        return $return_array;
    }

    public function getSubcategoriesByCPathFromDB1($cPath, $status = '') {
        $return_array = array();
        $category_path = '\_' . str_replace('_', '\_', $cPath) . '\_%';
        $sql = "SELECT categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id, sort_order
                    FROM " . TABLE_CATEGORIES . "
                    WHERE categories_parent_path LIKE '" . $category_path . "' AND categories_status =$status";
        $sql_result = tep_db_query($sql);
        if ($dataset = tep_db_fetch_array($sql_result)) {
            $return_array[] = $dataset;
        }

        return $return_array;
    }

    public function getRegionsArray($region_info = array('Europe (EU)', 'United Kingdom (UK)', 'Global', 'United State (US)')) {
        $return_array = array();
        $comma_separated = implode("','", $region_info);
        $comma_separated = "'" . $comma_separated . "'";
        $sql = "SELECT game_region_id,game_region_description FROM " . TABLE_GAME_REGION_DESCRIPTION . " WHERE  game_region_description IN (" . $comma_separated . ") AND language_id=1";
        $sql_result = tep_db_query($sql);
        while ($dataset = tep_db_fetch_array($sql_result)) {
            $return_array[$dataset['game_region_id']] = $dataset['game_region_description'];
        }
        return $return_array;
    }

    public function notNull($value) {
        if (is_array($value)) {
            if (sizeof($value) > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            if (($value != '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function countDimension($array) {
        if (is_array(reset($array))) {
            $return = $this->countDimension(reset($array)) + 1;
        } else {
            $return = 1;
        }

        return $return;
    }

}