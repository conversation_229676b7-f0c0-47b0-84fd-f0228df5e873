<?php

if (class_exists('currencies') != TRUE) {
    include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'currencies.php';
}

if (class_exists('ogm_amazon_ws') != TRUE) {
    include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'ogm_amazon_ws.php';
}

class rss_generator {
    public $feed_store_name = '';
    public $feed_store_url = '';
    public $feed_title = 'Buy Gift Cards, Game Cards and CD Keys - OffGamers Online Game Store';
    public $feed_description = '';
    private $feed_customFields = array();
    private $queryMap = array();

    public function __construct($feed_store_name, $feed_store_url, $feed_title) {
        $this->feed_store_name = $feed_store_name;
        $this->feed_store_url = $feed_store_url;
        $this->feed_title = $feed_title;
    }

    public function prepare($s) {
        $str = (strpos($s, '£') == false) ? mb_convert_encoding($s, "UTF-8", mb_detect_encoding($s)) : $s;
        return htmlspecialchars($str, ENT_QUOTES);
    }

    public function map($map) {
        $this->queryMap = array_merge($this->queryMap, $map);
    }

    public function fieldmap($q_field, $i_field) {
        $this->queryMap[$q_field] = $i_field;
    }

    public function custom($field, $value) {
        $this->feed_customFields[$field] = $value;
    }

    private function allCDKeyItemGenerator($recordset) {
        $items = '';
        $currency_obj = new currencies();
        $productsId = '';
        $currencyByRegionId = 'USD';

        foreach ($recordset as $game_id => $record) {
            foreach ($record as $row => $currentrow) {

                $current = '';
                foreach ($currentrow as $field => $value) {
                    if ($field == 'products_id') {
                        $productsId = $value;
                    }

                    if ($field == 'currency_by_region') {
                        
                        if (isset($currency_obj->currencies[$value])) {
                            $currencyByRegionId = $value;
                        }
                       

                    }

                    if ($field == 'formatted_price') {
                        $nonformatted_value = $currency_obj->display_product_price_format($productsId, $value, 1, $currencyByRegionId);
                        $value = html_entity_decode($currency_obj->format($nonformatted_value, false, $currencyByRegionId), ENT_NOQUOTES, 'UTF-8');
                        $current.=" <currency>" . $currencyByRegionId . "</currency>\n";
                        $current.=" <price>" . $nonformatted_value . "</price>\n";
                      
                    }

                    if (array_key_exists($field, $this->queryMap)) {
                        $current.=" <{$this->queryMap[$field]}>" . $this->prepare($value) . "</{$this->queryMap[$field]}>\n";
                    }
                    
                    
                }

                if (!empty($current))
                    $items .= " <item>\n" . $current . " </item>\n";
            }
        }

        return $items;
    }

    private function pricePandaItemGenerator($recordset) {
        $items = '';
        while ($record = tep_db_fetch_array($recordset)) {
            $current = '';
            foreach ($record as $field => $value) {
                if (array_key_exists($field, $this->queryMap)) {
                    $queryValue = $this->prepare($value);

                    if ($this->queryMap[$field] != 'PRODUCTS_ID') {

                        $current.=" <{$this->queryMap[$field]}>" . $queryValue . "</{$this->queryMap[$field]}>\n";
                    } else {
                        $current.= '<PRODUCT NUM="' . $this->prepare($value) . '">' . $current . "\n";
                    }
                }
            }
            if (!empty($current))
                $items .= $current . '</PRODUCT>' . "\n";
        }
        return $items;
    }

    function generate($recordset, $feed_type) {
        // Generate head 
        $feed = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        foreach ($this->feed_customFields as $field => $value) {
            $feed .= '<' . $field . '>' . $this->prepare($value) . '</' . $field . '>' . "\n";
        }
        // Generate items 
        if ($feed_type == 'pricepanda') {
            $feed .= '<STORE URL="' . $this->prepare($this->feed_store_url) . '"  DATE="' . date('d/m/Y') . '" TIME="' . date('H:i:s') . '" NAME="' . $this->prepare($this->feed_store_name) . '">' . "\n";
            $feed .= '<PRODUCTS>' . "\n";
            $feed .= $this->pricePandaItemGenerator($recordset);
            $feed .= '</PRODUCTS>' . "\n";
            $feed .= '</STORE>';
        } else if ($feed_type == 'allcdkey' || $feed_type == 'criteo') {
            $feed .= '<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" >' . "\n";
            $feed .= '<channel>' . "\n";
            $feed .= '<title>' . $this->feed_title . '</title>' . "\n";
            $feed .= $this->allCDKeyItemGenerator($recordset);
            $feed .= '</channel>' . "\n";
            $feed .= '</rss>' . "\n";
        }

        return $feed;
    }

    public function cData($str) {
        return '<![CDATA[ ' . $str . ' ]]>';
    }

    public function generateRssFile($recordset, $filename, $feed_type) {
        $bool = false;
        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key('BUCKET_STATIC');
        $aws_obj->set_filepath('product_feeds_xml/');
//        $aws_obj->set_acl('ACL_PUBLIC');
        $aws_obj->set_storage('STORAGE_STANDARD');
        $aws_enabled = $aws_obj->is_aws_s3_enabled();
        $xml_data = $this->generate($recordset, $feed_type);
       
        if ($aws_enabled) {
            $aws_obj->set_filename($filename);
            $aws_obj->set_file_content($xml_data);
            $create_status = $aws_obj->save_file();
            if ($create_status == 0) {
                $this->report_error('Function[generateRssFile][' . $filename . '] Failed to write');
            } else if ($create_status == 1) {
                $bool = true;
                //echo 'Success Uploaded';
            }
        } else {
            $dirname = dirname(__FILE__) . '/data_feeds_xml';
            if (!is_dir($dirname)) {
                mkdir($dirname);
            }
            $fw = file_put_contents($dirname . '/' . $filename, $this->generate($recordset, $feed_type));
            if ($fw) {
                $bool = true;
            }
        }
        
        return $bool;
    }

    private function report_error($message) {
        include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'slack_notification.php';
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] Cron GraphicMail Error Report - ' . date('YmdHis'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $message
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

}