<?php

/*
  $Id: sales_report.php,v 1.94 2015/09/04 08:33:09 jeeva.kasin<PERSON>an Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
 */

// make a connection to the database... now
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

class sales_report {

    var $mode, $globalStartDate, $startDate, $endDate, $info, $previous, $next, $startDates, $endDates, $size;
    var $order_amount_fileds_str, $orders_amt_breakdown_array, $order_amount_status_str, $orders_default_site;

    function sales_report($mode, $startDate = "", $endDate = "", $filter = "", $filter2 = "", $filter3 = "", $orders_site = "1") {
        // startDate and endDate have to be a unix timestamp. Use mktime !
        // if set then both have to be valid startDate and endDate
        $this->mode = $mode;
        $this->previous = '';
        $this->next = '';
        $this->filter = '';
        $this->sql_filter = '';
        $this->order_amount_fileds_str = '';
        $this->info = array(array());
        $this->filter_sql = array();
        $this->orders_default_site = $orders_site;

        $this->order_amount_status_str = " SUM(op.final_price*op.products_quantity) AS total_purchase_amount, SUM(op.products_good_delivered_price) AS total_delivered_amount, SUM(op.products_canceled_price) AS total_canceled_amount, SUM(op.products_reversed_price) AS total_reversed_amount ";

        $this->orders_amt_breakdown_array = array('2' => array('0' => array('id' => 'UD', 'text' => 'Undelivered'),
                '1' => array('id' => 'D', 'text' => 'Delivered'),
                '2' => array('id' => 'C', 'text' => 'Refunded')
            ),
            '3' => array('0' => array('id' => 'D', 'text' => 'Delivered'),
                '1' => array('id' => 'C', 'text' => 'Refunded'),
                '2' => array('id' => 'RV-WIN', 'text' => 'Reversed WIN'),
                '3' => array('id' => 'RV-LOST', 'text' => 'Reversed LOST'),
                '4' => array('id' => 'RV-RESOLVED', 'text' => 'Reversed RESOLVED')
            )
        );

        // get date of first sale
        $first_query = tep_db_query("select UNIX_TIMESTAMP(min(date_purchased)) as first FROM " . TABLE_ORDERS);
        $first = tep_db_fetch_array($first_query);
        $this->globalStartDate = mktime(0, 0, 0, date("m", $first['first']), date("d", $first['first']), date("Y", $first['first']));

        // get all possible status for filter
        $tmp_query = tep_db_query("SELECT * FROM " . TABLE_ORDERS_STATUS . " ORDER BY orders_status_sort_order", 'read_db_link');
        $i = 0;
        while ($status = tep_db_fetch_array($tmp_query)) {
            $tmp[$i]['index'] = $status['orders_status_id'];
            $tmp[$i]['value'] = $status['orders_status_name'];
            $i++;
        }
        $this->status_available = $tmp;
        $this->status_available_size = $i;

        if ($endDate == "" or $startDate == "") {
            // set startDate to nothing
            $dateGiven = false;
            $startDate = 0;

            // endDate is today
            $this->endDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
        } else {
            $dateGiven = true;
            if ($endDate > mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                $this->endDate = mktime(0, 0, 0, date("m"), date("d") + 1, date("Y"));
            } else {
                // set endDate to the given Date with "round" on days
                $this->endDate = mktime(0, 0, 0, date("m", $endDate), date("d", $endDate) + 1, date("Y", $endDate));
            }
        }

        switch ($this->mode) {
            // hourly
            case '1':
                if ($dateGiven) {
                    // "round" to midnight
                    $this->startDate = mktime(0, 0, 0, date("m", $startDate), date("d", $startDate), date("Y", $startDate));
                    $this->endDate = mktime(0, 0, 0, date("m", $startDate), date("d", $startDate) + 1, date("Y", $startDate));

                    // size to number of hours
                    $this->size = 24;
                } else {
                    // startDate to start of this day
                    $this->startDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
                    $this->endDate = mktime(date("G") + 1, 0, 0, date("m"), date("d"), date("Y"));

                    // size to number of hours
                    $this->size = date("G") + 1;

                    if ($this->startDate < $this->globalStartDate) {
                        $this->startDate = $this->globalStartDate;
                    }
                }

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime($i, 0, 0, date("m", $this->startDate), date("d", $this->startDate), date("Y", $this->startDate));
                    $this->endDates[$i] = mktime($i + 1, 0, 0, date("m", $this->startDate), date("d", $this->startDate), date("Y", $this->startDate));
                }
                break;

            // day
            case '2':
                if ($dateGiven) {
                    // "round" to day
                    $this->startDate = mktime(0, 0, 0, date("m", $startDate), date("d", $startDate), date("Y", $startDate));
                    //$this->endDate = mktime(0, 0, 0, date("m", $endDate), date("d", $endDate), date("Y", $endDate));
                    // size to number of days
                    $this->size = ($this->endDate - $this->startDate) / (60 * 60 * 24);
                    $this->size = ceil($this->size);
                } else {
                    // startDate to start of this week
                    $this->startDate = mktime(0, 0, 0, date("m"), date("d") - date("w"), date("Y"));
                    $this->endDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));

                    // size to number of days
                    $this->size = date("w") + 1;

                    if ($this->startDate < $this->globalStartDate) {
                        $this->startDate = $this->globalStartDate;
                    }
                }

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i, date("Y", $this->startDate));
                    $this->endDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1), date("Y", $this->startDate));
                }
                break;

            // week
            case '3':
                if ($dateGiven) {
                    $this->startDate = mktime(0, 0, 0, date("m", $startDate), date("d", $startDate) - date("w", $startDate), date("Y", $startDate));
                } else {
                    // startDate to beginning of first week of this month
                    $firstDayOfMonth = mktime(0, 0, 0, date("m"), 1, date("Y"));
                    $this->startDate = mktime(0, 0, 0, date("m"), 1 - date("w", $firstDayOfMonth), date("Y"));
                }

                if ($this->startDate < $this->globalStartDate) {
                    $this->startDate = $this->globalStartDate;
                }

                // size to the number of weeks in this month till endDate
                $this->size = ceil((($this->endDate - $this->startDate + 1) / (60 * 60 * 24)) / 7);
                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i * 7, date("Y", $this->startDate));
                    $this->endDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1) * 7, date("Y", $this->startDate));
                }
                break;

            // month
            case '4':
                if ($dateGiven) {
                    $this->startDate = mktime(0, 0, 0, date("m", $startDate), 1, date("Y", $startDate));
                    // size to number of days
                } else {
                    // startDate to first day of the first month of this year
                    $this->startDate = mktime(0, 0, 0, 1, 1, date("Y"));
                    // size to number of months in this year
                }

                if ($this->startDate < $this->globalStartDate) {
                    $this->startDate = mktime(0, 0, 0, date("m", $this->globalStartDate), 1, date("Y", $this->globalStartDate));
                }

                $this->size = (date("Y", $this->endDate) - date("Y", $this->startDate)) * 12 + (date("m", $this->endDate) - date("m", $this->startDate)) + 1;
                $tmpMonth = date("m", $this->startDate);
                $tmpYear = date("Y", $this->startDate);

                for ($i = 0; $i < $this->size; $i++) {
                    // the first of the $tmpMonth + $i
                    $this->startDates[$i] = mktime(0, 0, 0, $tmpMonth + $i, 1, $tmpYear);
                    // the first of the $tmpMonth + $i + 1 month
                    $this->endDates[$i] = mktime(0, 0, 0, $tmpMonth + $i + 1, 1, $tmpYear);
                }
                break;

            // year
            case '5':
                if ($dateGiven) {
                    $this->startDate = mktime(0, 0, 0, 1, 1, date("Y", $startDate));
                    $this->endDate = mktime(0, 0, 0, 1, 1, date("Y", $endDate) + 1);
                } else {
                    // startDate to first of current year - $max_years
                    $this->startDate = mktime(0, 0, 0, 1, 1, date("Y") - 5 + 1);
                    // endDate to today
                    $this->endDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
                }

                if ($this->startDate < $this->globalStartDate) {
                    $this->startDate = $this->globalStartDate;
                }

                $this->size = date("Y", $this->endDate) - date("Y", $this->startDate) + 1;
                $tmpYear = date("Y", $this->startDate);

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, 1, 1, $tmpYear + $i);
                    $this->endDates[$i] = mktime(0, 0, 0, 1, 1, $tmpYear + $i + 1);
                }
                break;
        }

        if (($this->mode < 3) or ($this->mode == 4)) {
            // set previous to start - diff
            $tmpDiff = $this->endDate - $this->startDate;

            if ($this->size == 0) {
                $tmpUnit = 0;
            } else {
                $tmpUnit = $tmpDiff / $this->size;
            }
            //echo $tmpDiff . " " . $tmpUnit . '-' . $this->size . "<br>";

            switch ($this->mode) {
                // hourly
                case '1':
                    $tmp1 = 24 * 60 * 60;
                    break;

                // daily
                case '2':
                    $tmp1 = 7 * 24 * 60 * 60;
                    break;

                // weekly
                case '3':
                    $tmp1 = 30 * 24 * 60 * 60;
                    break;

                // monthly
                case '4':
                    $tmp1 = 365 * 24 * 60 * 60;
                    break;
            }
            $tmp = ceil($tmpDiff / $tmp1);

            if ($tmp > 1) {
                //$tmpShift = ($tmp * $tmpDiff) + $tmpUnit;
                $tmpShift = $tmp1 + $tmpUnit;
            } else {
                $tmpShift = $tmp1 + $tmpUnit;
            }

            $tmpStart = $this->startDate - $tmpShift + $tmpUnit;
            $tmpEnd = $this->startDate - $tmpUnit;

            if ($tmpStart >= $this->globalStartDate or $this->mode == 4) {
                //echo strftime("%T %x", $tmpStart). " - " . strftime("%T %x", $tmpEnd) . "<br>";
                $this->previous = "report=" . $this->mode . "&startDate=" . $tmpStart . "&endDate=" . $tmpEnd;
            }

            $tmpStart = $this->endDate;
            $tmpEnd = $this->endDate + $tmpShift - 2 * $tmpUnit;

            if ($tmpEnd < mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                //echo strftime("%T %x", $tmpStart). " - " . strftime("%T %x", $tmpEnd);
                $this->next = "report=" . $this->mode . "&startDate=" . $tmpStart . "&endDate=" . $tmpEnd;
            } else {
                if ($tmpEnd - $tmpDiff < mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                    $tmpEnd = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
                    //echo strftime("%T %x", $tmpStart). " - " . strftime("%T %x", $tmpEnd);
                    $this->next = "report=" . $this->mode . "&startDate=" . $tmpStart . "&endDate=" . $tmpEnd;
                }
            }
        }

        // handle filters
        // submit the filters that way:
        // 01001 means use filter for status 2 and 5 set.
        if (strlen($filter) > 0) {
            $tmp = $tmp1 = '';

            for ($i = 0; $i < $this->status_available_size; $i++) {
                if (substr($filter, $i, 1) == "0") {
                    $tmp1 .= "0";

                    if (strlen($tmp) == 0) {
                        $tmp = "o.orders_status = " . $this->status_available[$i]['index'];
                    } else {
                        $tmp .= " OR o.orders_status = " . $this->status_available[$i]['index'];
                    }
                } else {
                    $tmp1 .= "1";
                }
            }

            $this->filter_sql[] = '(' . $tmp . ')';
        }

        if (count($this->filter_sql)) {
            $this->sql_filter = implode(" AND ", $this->filter_sql);
        }

        if (strlen($filter2) > 0)
            $this->set_order_amount_status(2, $filter2);
        if (strlen($filter3) > 0)
            $this->set_order_amount_status(3, $filter3);

        $this->filter_link = "report=" . $this->mode . "&order_site=" . $this->orders_default_site . "&startDate=" . $startDate . "&endDate=" . $endDate;

        $this->query();
    }

    function query() {
        if (count($this->order_amount_status_array)) {
            $sales_sql = "SELECT o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str;
        } else {
            $sales_sql = "SELECT SUM(op.final_price*op.products_quantity) as value, AVG(op.final_price*op.products_quantity) as avg, COUNT(DISTINCT o.orders_id) as count ";
        }

        $ogc_pid = array();
        $ogc_products_id_query = "select products_id from products_to_categories where categories_id IN (SELECT categories_id FROM categories where categories_parent_path LIKE '%\_".OGC_CATEGORIES_ID."\_%')";
        $ogc_res = tep_db_query($ogc_products_id_query, 'read_db_link');
        while ($ogc_row = tep_db_fetch_array($ogc_res)) {
            $ogc_pid[] = $ogc_row["products_id"];
        }

        $sales_sql .= "	, oei.orders_extra_info_value, oss.latest_date
                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                        INNER JOIN " . TABLE_ORDERS . " AS o
                            ON op.orders_id = o.orders_id
                        LEFT JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss
                            ON oss.orders_id = o.orders_id
                            AND oss.orders_status_id = 7
                        LEFT JOIN orders_extra_info AS oei 
                            ON o.orders_id = oei.orders_id 
                            AND oei.orders_extra_info_key = 'site_id'
                        WHERE op.products_bundle_id=0 
                            AND op.orders_products_is_compensate=0
                            AND op.custom_products_type_id != 3
                            AND op.products_id NOT IN (" . implode(",", $ogc_pid) .")
                            " . ($this->sql_filter ? " AND " . $this->sql_filter : "");

        for ($i = 0; $i < $this->size; $i++) {
            if (count($this->order_amount_status_array)) {
                $report_select_sql = $sales_sql . " AND oss.latest_date >= '" . tep_db_input(date("Y-m-d H:i:s", $this->startDates[$i])) . "' AND oss.latest_date < '" . tep_db_input(date("Y-m-d H:i:s", $this->endDates[$i])) . "'";
                $report_select_sql .= " GROUP BY o.orders_id";
                $report_result_sql = tep_db_query($report_select_sql, 'read_db_link');
                
                $this->info[$i]['count'] = 0;
                $this->info[$i]['sum'] = 0;

                while ($report = tep_db_fetch_array($report_result_sql)) {
                    if ($this->orders_default_site == '1') {
                        $site_id_conditions = ($report['orders_extra_info_value'] != 5 || empty($report['orders_extra_info_value']));
                    } else {
                        $site_id_conditions = ($report['orders_extra_info_value'] == 5);
                    }

                    if ($site_id_conditions) {
                        $sales_amount = $this->get_sales_amount($report);
                        
                        if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                            $this->info[$i]['sum'] += $sales_amount;
                            $this->info[$i]['count']++;
                        }
                    }
                }
                
                if ($this->info[$i]['count'])
                    $this->info[$i]['avg'] = $this->info[$i]['sum'] / $this->info[$i]['count'];
            } else {
                $report_query = tep_db_query($sales_sql . " AND oss.latest_date >= '" . tep_db_input(date("Y-m-d H:i:s", $this->startDates[$i])) . "' AND oss.latest_date < '" . tep_db_input(date("Y-m-d H:i:s", $this->endDates[$i])) . "'");
                $report = tep_db_fetch_array($report_query, 'read_db_link');

                $this->info[$i]['sum'] = $report['value'];
                $this->info[$i]['avg'] = $report['avg'];
                $this->info[$i]['count'] = $report['count'];
            }

            switch ($this->mode) {
                // hourly
                case '1':
                    $this->info[$i]['text'] = strftime("%H", $this->startDates[$i]) . " - " . strftime("%H", $this->endDates[$i]);
                    $this->info[$i]['link'] = "";
                    break;

                // daily
                case '2':
                    $this->info[$i]['text'] = strftime("%x", $this->startDates[$i]);
                    $this->info[$i]['link'] = "report=1&order_site=" . $this->orders_default_site . "&startDate=" . $this->startDates[$i] . "&endDate=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) + 1, date("Y", $this->endDates[$i]));
                    break;

                // weekly
                case '3':
                    $this->info[$i]['text'] = strftime("%x", $this->startDates[$i]) . " - " . strftime("%x", mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i])));
                    $this->info[$i]['link'] = "report=2&order_site=" . $this->orders_default_site . "&startDate=" . $this->startDates[$i] . "&endDate=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i]));
                    break;

                // monthly
                case '4':
                    $this->info[$i]['text'] = strftime("%b %y", $this->startDates[$i]);
                    $this->info[$i]['link'] = "report=3&order_site=" . $this->orders_default_site . "&startDate=" . $this->startDates[$i] . "&endDate=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i]));
                    break;

                // yearly
                case '5':
                    $this->info[$i]['text'] = date("Y", $this->startDates[$i]);
                    $this->info[$i]['link'] = "report=4&order_site=" . $this->orders_default_site . "&startDate=" . $this->startDates[$i] . "&endDate=" . mktime(0, 0, 0, date("m", $this->endDates[$i]) - 1, date("d", $this->endDates[$i]), date("Y", $this->endDates[$i]));
                    break;
            }
        }
    }

    function set_order_amount_status($status_id, $amt_status_str) {
        $considered_fields = array();

        if (tep_not_null($amt_status_str)) {
            for ($sub_cnt = 0; $sub_cnt < count($this->orders_amt_breakdown_array[$status_id]); $sub_cnt++) {
                if (substr($amt_status_str, $sub_cnt, 1) == "0") {
                    $this->order_amount_status_array[$status_id][] = $this->orders_amt_breakdown_array[$status_id][$sub_cnt]['id'];
                }
            }
        }
    }

    function get_sales_amount($query_result) {
        $value = 0;
        if ($query_result['orders_status'] == 2 || $query_result['orders_status'] == 3) {
            if (count($this->order_amount_status_array[$query_result['orders_status']]) == count($this->orders_amt_breakdown_array[$query_result['orders_status']])) { // All sub status checkboxes are checked
                $value = $query_result['total_purchase_amount'];
            } else {
                for ($sub_cnt = 0; $sub_cnt < count($this->order_amount_status_array[$query_result['orders_status']]); $sub_cnt++) {
                    switch ($this->order_amount_status_array[$query_result['orders_status']][$sub_cnt]) {
                        case 'UD': // Completed order does not has Undelivered part
                            if ($query_result['orders_status'] == 2)
                                $value += ( $query_result['total_purchase_amount'] - ($query_result['total_delivered_amount'] + $query_result['total_canceled_amount'] + $query_result['total_reversed_amount']) );
                            break;
                        case 'D':
                            $value += $query_result['total_delivered_amount'];
                            break;
                        case 'C':
                            $value += $query_result['total_canceled_amount'];
                            break;
                        case 'RV-WIN':
                            if ($query_result['orders_cb_status'] == '1')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        case 'RV-LOST':
                            if ($query_result['orders_cb_status'] == '2')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        case 'RV-RESOLVED':
                            if ($query_result['orders_cb_status'] == '3')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            $value = $query_result['total_purchase_amount'];
        }

        return $value;
    }

}

class sales_report_new {

    var $mode, $startDate, $endDate, $size, $sql_filter, $info, $order, $summary, $totalSales;
    var $selStartDateStr, $selEndDateStr, $sales_period;
    var $parantChild;
    var $extra_joining_str, $sort_by, $sort_order, $order_site;
    var $csv_data;
    var $order_amount_status_array, $order_amount_status_str, $order_qty_fileds_str, $orders_amt_breakdown_array;
    var $sub_qty, $pg_is_any;
    var $export_report, $export_format, $export_payment_method, $export_payment_parent_method;
    var $date_type;
    var $g2g_region_layer;

    function sales_report_new($mode, $startDate = "", $endDate = "", $sales_period = 'month', $date_formatted = 0, $export_report = '') {
        $this->mode = $mode;
        $this->sql_filter = "";
        $this->info = array();
        $this->sub_qty = array();
        $this->sales_text = array();
        $this->order = array(array());
        $this->summary = array();
        $this->parantChild = array(); // define parent-child relationship, used for expand/collapse in category report
        $this->totalSales = 0;
        $this->prod_qty_type = array(); // for mode = 7
        $this->order_amount_status_array = array();
        $this->extra_joining_str = '';
        $this->pg_is_any = false;
        $this->date_type = '';
        $this->g2g_region_layer = false;

        $this->order_amount_status_str = " SUM(op.final_price*op.products_quantity) AS total_purchase_amount, SUM(op.products_good_delivered_price) AS total_delivered_amount, SUM(op.products_canceled_price) AS total_canceled_amount, SUM(op.products_reversed_price) AS total_reversed_amount ";
        $this->order_qty_fileds_str = " op.products_quantity AS total_purchase_qty, op.products_good_delivered_quantity AS total_delivered_qty, op.products_canceled_quantity AS total_canceled_qty, op.products_reversed_quantity AS total_reversed_qty ";

        $this->csv_data = array();

        $this->orders_amt_breakdown_array = array('2' => array('UD' => 'Undelivered',
                'D' => 'Delivered',
                'C' => 'Refunded'
            ),
            '3' => array('D' => 'Delivered',
                'C' => 'Refunded',
                'RV-WIN' => 'Reversed WIN',
                'RV-LOST' => 'Reversed LOST',
                'RV-RESOLVED' => 'Reversed RESOLVED'
            )
        );

        $this->sales_period = $sales_period;
        $this->export_report = $export_report;
        $this->export_format = '';
        $this->export_payment_method = array();
        $this->export_payment_parent_method = array();

        if (!$date_formatted) {
            $start_date_array = explode("-", $startDate);
            $end_date_array = explode("-", $endDate);

            $start_date_stamp = mktime(0, 0, 0, (int) $start_date_array["1"], (int) $start_date_array["2"], (int) $start_date_array["0"]);
            $end_date_stamp = mktime(0, 0, 0, (int) $end_date_array["1"], (int) $end_date_array["2"], (int) $end_date_array["0"]);
        } else {
            $start_date_stamp = $startDate;
            $end_date_stamp = $endDate;
        }

        switch ($this->mode) {
            case "1": //Hourly
                $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp), date("Y", $start_date_stamp));
                $this->endDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp) + 1, date("Y", $start_date_stamp));
                // size to number of hours
                $this->size = 24;

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime($i, 0, 0, date("m", $this->startDate), date("d", $this->startDate), date("Y", $this->startDate));
                    $this->endDates[$i] = mktime($i + 1, 0, 0, date("m", $this->startDate), date("d", $this->startDate), date("Y", $this->startDate));
                }
                break;

            case "2": //Daily
                $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp), date("Y", $start_date_stamp));
                $this->endDate = mktime(0, 0, 0, date("m", $end_date_stamp), date("d", $end_date_stamp), date("Y", $end_date_stamp));
                // size to number of days
                $this->size = ($this->endDate - $this->startDate) / (24 * 60 * 60) + 1;
                $this->size = floor($this->size);

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i, date("Y", $this->startDate));
                    $this->endDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1), date("Y", $this->startDate));
                }
                break;

            case "3": //Weekly
                if (date("w", $start_date_stamp) == 1) {
                    $day_adjust = 0;
                } else if (date("w", $start_date_stamp) < 1) {
                    $day_adjust = 1;
                } else {
                    $day_adjust = (7 - date("w", $start_date_stamp)) + 1;
                }

                // set start date to the first coming Monday
                $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp) + $day_adjust, date("Y", $start_date_stamp));
                $this->endDate = mktime(0, 0, 0, date("m", $end_date_stamp), date("d", $end_date_stamp), date("Y", $end_date_stamp));
                $this->size = ceil((($this->endDate - $this->startDate + 1) / (60 * 60 * 24)) / 7);

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i * 7, date("Y", $this->startDate));
                    $this->endDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1) * 7, date("Y", $this->startDate));
                }
                break;

            case "4": //Monthly
                $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), 1, date("Y", $start_date_stamp));
                $this->endDate = mktime(0, 0, 0, date("m", $end_date_stamp), date("d", $end_date_stamp), date("Y", $end_date_stamp));

                $this->size = (date("Y", $this->endDate) - date("Y", $this->startDate)) * 12 + (date("m", $this->endDate) - date("m", $this->startDate)) + 1;

                $start_month = date("m", $this->startDate);
                $start_year = date("Y", $this->startDate);

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, $start_month + $i, 1, $start_year);
                    $this->endDates[$i] = mktime(0, 0, 0, $start_month + ($i + 1), 1, $start_year);
                }
                break;

            case "5": //Yearly
                $this->startDate = mktime(0, 0, 0, 1, 1, date("Y", $start_date_stamp));
                $this->endDate = mktime(0, 0, 0, 1, 1, date("Y", $end_date_stamp));
                $this->size = date("Y", $this->endDate) - date("Y", $this->startDate) + 1;

                $start_year = date("Y", $this->startDate);

                for ($i = 0; $i < $this->size; $i++) {
                    $this->startDates[$i] = mktime(0, 0, 0, 1, 1, $start_year + $i);
                    $this->endDates[$i] = mktime(0, 0, 0, 1, 1, $start_year + $i + 1);
                }
                break;

            case "6": # Category
            case "8": # Country 
            case "9": # Customer
            case "10": # New Sign Up
                if ($startDate) {
                    $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp), date("Y", $start_date_stamp));
                    if ($this->mode != 10) {
                        $this->selStartDateStr = " o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDate) . "' ";
                    } else {
                        $this->selStartDateStr = " customers_info_date_account_created >= '" . date("Y-m-d H:i:s", $this->startDate) . "' ";
                    }
                } else {
                    $this->selStartDateStr = " 1 ";
                }

                # endDate = 23:59:59, use "<=" 
                if ($endDate) {
                    $this->endDate = mktime(0, 0, -1, date("m", $end_date_stamp), date("d", $end_date_stamp) + 1, date("Y", $end_date_stamp));

                    if ($this->endDate > mktime(0, 0, 0, date('m'), date('d'), date('Y'))) {
                        $this->endDate = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));
                    }

                    if ($this->mode != 10) {
                        $this->selEndDateStr = " o.date_purchased <= '" . date("Y-m-d H:i:s", $this->endDate) . "' ";
                    } else {
                        $this->selEndDateStr = " customers_info_date_account_created <= '" . date("Y-m-d H:i:s", $this->endDate) . "' ";
                    }
                } else {
                    $this->selEndDateStr = " 1 ";
                }

                if ($this->sales_period == 'day') {
                    $this->size = ceil(($this->endDate - $this->startDate) / (24 * 60 * 60));

                    for ($i = 0; $i < $this->size; $i++) {
                        $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i, date("Y", $this->startDate));
                        $this->endDates[$i] = mktime(0, 0, -1, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1), date("Y", $this->startDate));
                    }
                } else {
                    $this->size = (date("Y", $this->endDate) - date("Y", $this->startDate)) * 12 + (date("m", $this->endDate) - date("m", $this->startDate)) + 1;

                    $start_month = date("m", $this->startDate);
                    $start_year = date("Y", $this->startDate);

                    for ($i = 0; $i < $this->size; $i++) {
                        $this->startDates[$i] = mktime(0, 0, 0, $start_month + $i, 1, $start_year);
                        $this->endDates[$i] = mktime(0, 0, -1, $start_month + ($i + 1), 1, $start_year);
                    }
                }
                break;

            case "7":
                if ($startDate) {
                    $this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), $this->sales_period == 'day' ? date("d", $start_date_stamp) : 1, date("Y", $start_date_stamp));
                } else {
                    $this->startDate = mktime(0, 0, 0, date("m"), 1, date("Y"));
                }

                # endDate = 23:59:59, use "<=" 
                if ($endDate) {
                    $this->endDate = mktime(0, 0, -1, date("m", $end_date_stamp), date("d", $end_date_stamp) + 1, date("Y", $end_date_stamp));
                } else {
                    $this->endDate = mktime(0, 0, -1, date("m"), date("d") + 1, date("Y"));
                }

                $this->selStartDateStr = " o.date_purchased >= '" . date('Y-m-d H:i:s', $this->startDate) . "' ";
                $this->selEndDateStr = " o.date_purchased <= '" . date('Y-m-d H:i:s', $this->endDate) . "' ";

                if ($this->sales_period == 'day') {
                    $this->size = ($this->endDate - $this->startDate) / (24 * 60 * 60);
                    $this->size = ceil($this->size);

                    for ($i = 0; $i < $this->size; $i++) {
                        $this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i, date("Y", $this->startDate));
                        $this->endDates[$i] = mktime(0, 0, -1, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1), date("Y", $this->startDate));
                    }
                } else {
                    $this->size = (date("Y", $this->endDate) - date("Y", $this->startDate)) * 12 + (date("m", $this->endDate) - date("m", $this->startDate)) + 1;

                    $start_month = date("m", $this->startDate);
                    $start_year = date("Y", $this->startDate);

                    for ($i = 0; $i < $this->size; $i++) {
                        $this->startDates[$i] = mktime(0, 0, 0, $start_month + $i, 1, $start_year);
                        $this->endDates[$i] = mktime(0, 0, -1, $start_month + ($i + 1), 1, $start_year);
                    }
                }
                break;
        }
    }

    function set_products_qty_type($qty_type_array) {
        if (is_array($qty_type_array) && count($qty_type_array)) {
            $this->prod_qty_type = $qty_type_array;
        }
    }

    function set_order_amount_status($status_id, $amt_status_array) {
        if (is_array($amt_status_array) && count($amt_status_array)) {
            $this->order_amount_status_array[$status_id] = $amt_status_array;
        }
    }

    function set_sorting_info($sort_by, $sort_order) {
        $this->sort_by = $sort_by;
        $this->sort_order = $sort_order;
    }

    function reportQuery($filter = "") {
        global $languages_id;

        if (strpos($this->sql_filter, 'ot_gv.') !== FALSE || $this->pg_is_any) {
            $this->extra_joining_str .= " LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv 
                                        ON o.orders_id=ot_gv.orders_id AND ot_gv.class='ot_gv' ";
        }

        switch ($this->mode) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                if (count($this->order_amount_status_array)) {
                    $orig_sales_sql = "SELECT o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str;
                    $orig_sales_per_order_sql = "SELECT o.orders_id, " . $this->order_amount_status_str;
                } else {
                    $orig_sales_sql = "SELECT SUM(op.final_price*op.products_quantity) as value, AVG(op.final_price*op.products_quantity) as avg, COUNT(DISTINCT o.orders_id) as count ";
                    $orig_sales_per_order_sql = "SELECT o.orders_id, SUM(op.final_price*op.products_quantity) AS value ";
                }

                $payment_method_sel = array();
                $payment_parent_method_sel = array();
                $cpt_array = array();
                $orig_sql_filter = $this->sql_filter;
                $orig_extra_joining_str = $this->extra_joining_str;

                if ($this->mode == 2 && tep_not_null($this->export_report)) {
                    /* -- payment method -- */
                    $payment_methods_select_sql = "	SELECT pm1.payment_methods_id, pm1.payment_methods_parent_id 
                                                        FROM " . TABLE_PAYMENT_METHODS . " AS pm1 
                                                        LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pm2 
                                                                ON pm2.payment_methods_id = pm1.payment_methods_parent_id 
                                                        WHERE pm1.payment_methods_receive_status = '1' 
                                                                AND pm1.payment_methods_parent_id > 0 
                                                        ORDER BY pm1.payment_methods_sort_order";
                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql, 'read_db_link');

                    $sess_pg_id = (isset($_SESSION['sales_param']["payment_gateways_id"]) ? $_SESSION['sales_param']["payment_gateways_id"] : (isset($filter["payment_gateways_id"]) ? $filter["payment_gateways_id"] : ''));
                    $sess_pm_id = (isset($_SESSION['sales_param']['payment_methods_id']) ? $_SESSION['sales_param']['payment_methods_id'] : (isset($filter['payment_methods_id']) ? $filter['payment_methods_id'] : ''));
                    if (is_array($sess_pg_id) && in_array("any", $sess_pg_id)) {
                        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                            if (tep_not_null($this->export_format) && $this->export_format == 'export_by_order') {
                                $pg_id = $payment_methods_row['payment_methods_parent_id'];

                                if (!in_array($pg_id, $payment_parent_method_sel)) {
                                    $payment_parent_method_sel[] = $pg_id;
                                }

                                if (!isset($payment_method_sel[$pg_id]) || !tep_not_null($payment_method_sel[$pg_id]) || !in_array($payment_methods_row['payment_methods_id'], $payment_method_sel[$pg_id])) {
                                    $payment_method_sel[$pg_id][] = $payment_methods_row['payment_methods_id'];
                                }
                            } else {
                                $payment_method_sel[] = array('sql' => " AND ( o.payment_methods_parent_id = '" . $payment_methods_row['payment_methods_parent_id'] . "' 
                                                                        AND o.payment_methods_id = '" . $payment_methods_row['payment_methods_id'] . "' ) ",
                                    'payment_methods_parent_id' => $payment_methods_row['payment_methods_parent_id'],
                                    'payment_methods_id' => $payment_methods_row['payment_methods_id']);
                            }
                        }
                    } else {
                        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                            $pg_id = $payment_methods_row['payment_methods_parent_id'];

                            if (isset($sess_pg_id) && count($sess_pg_id) && in_array($pg_id, $sess_pg_id)) {
                                if (tep_not_null($this->export_format) && $this->export_format == 'export_by_order') {
                                    if (!in_array($pg_id, $payment_parent_method_sel)) {
                                        $payment_parent_method_sel[] = $pg_id;
                                    }

                                    if (empty($payment_method_sel) || !tep_not_null($payment_method_sel[$pg_id]) || !in_array($payment_methods_row['payment_methods_id'], $payment_method_sel[$pg_id])) {
                                        $payment_method_sel[$pg_id][] = $payment_methods_row['payment_methods_id'];
                                    }
                                } else {
                                    $payment_method_sel[] = array('sql' => "	AND ( o.payment_methods_parent_id = '" . $payment_methods_row['payment_methods_parent_id'] . "' 
                                                                                AND o.payment_methods_id = '" . $payment_methods_row['payment_methods_id'] . "' ) ",
                                        'payment_methods_parent_id' => $payment_methods_row['payment_methods_parent_id'],
                                        'payment_methods_id' => $payment_methods_row['payment_methods_id']);
                                }
                            } else if (isset($sess_pm_id) && count($sess_pm_id) && in_array($payment_methods_row['payment_methods_id'], $sess_pm_id)) {
                                if (tep_not_null($this->export_format) && $this->export_format == 'export_by_order') {
                                    if (!in_array($pg_id, $payment_parent_method_sel)) {
                                        $payment_parent_method_sel[] = $pg_id;
                                    }

                                    if (!tep_not_null($payment_method_sel[$pg_id]) || !in_array($payment_methods_row['payment_methods_id'], $payment_method_sel[$pg_id])) {
                                        $payment_method_sel[$pg_id][] = $payment_methods_row['payment_methods_id'];
                                    }
                                } else {
                                    $payment_method_sel[] = array('sql' => " AND ( o.payment_methods_id = '" . $payment_methods_row['payment_methods_id'] . "' ) ",
                                        'payment_methods_parent_id' => $payment_methods_row['payment_methods_parent_id'],
                                        'payment_methods_id' => $payment_methods_row['payment_methods_id']);
                                }
                            }
                        }
                    }

                    if ((is_array($sess_pg_id)) && (in_array("any", $sess_pg_id) || in_array(0, $sess_pg_id))) {
                        if (tep_not_null($this->export_format) && $this->export_format == 'export_by_order') {
                            $payment_method_sel[] = 0;
                            $payment_parent_method_sel[] = 0;
                        } else {
                            $payment_method_sel[] = array('sql' => "	AND ( o.payment_methods_parent_id = '0' 
                                                                    AND o.payment_methods_id = '0' ) ",
                                'payment_methods_parent_id' => 0,
                                'payment_methods_id' => 0
                            );
                        }
                    }

                    /* -- custom_products_type -- */
                    $cpt_sel_sql = "SELECT custom_products_type_id FROM " . TABLE_CUSTOM_PRODUCTS_TYPE;
                    $cpt_res_sql = tep_db_query($cpt_sel_sql, 'read_db_link');
                    while ($cpt_row = tep_db_fetch_array($cpt_res_sql)) {
                        $cpt_array[] = $cpt_row['custom_products_type_id'];
                    }
                } else {
                    $cpt_array[] = '';
                    $payment_method_sel[] = '';
                }

                if (!tep_not_null($this->export_format) || (tep_not_null($this->export_format) && $this->export_format == 'export_by_payment_gateway')) {
                    if ($this->mode == 2 && tep_not_null($this->export_report)) {
                        $cpt_array[] = 'gift_card';
                    }

                    for ($pg_cnt = 0, $pg_total = count($payment_method_sel); $pg_total > $pg_cnt; $pg_cnt++) {
                        $this->sql_filter = $orig_sql_filter;
                        $this->extra_joining_str = $orig_extra_joining_str;

                        foreach ($cpt_array as $cpt_cnt => $cpt_id) {
                            if ($this->mode == 2 && tep_not_null($this->export_report)) {
                                if (($cpt_id == 2) || ($cpt_id == 'gift_card')) {
                                    $this->extra_joining_str = $orig_extra_joining_str . " LEFT JOIN " . TABLE_PRODUCTS . " AS exp ON exp.products_id = op.products_id ";

                                    if ($cpt_id == 2) {
                                        $this->sql_filter = $orig_sql_filter . $payment_method_sel[$pg_cnt]['sql'] . " AND op.custom_products_type_id = '" . $cpt_id . "' 
															AND exp.products_flag_id != 4";
                                    } else {
                                        $this->sql_filter = $orig_sql_filter . $payment_method_sel[$pg_cnt]['sql'] . " AND op.custom_products_type_id = '2' 
															AND exp.products_flag_id = 4";
                                    }
                                } else {
                                    $this->sql_filter = $orig_sql_filter . $payment_method_sel[$pg_cnt]['sql'] . " AND op.custom_products_type_id = '" . $cpt_id . "'";
                                }

                                if (($payment_method_sel[$pg_cnt]['payment_methods_parent_id'] == 0) &&
                                        (strpos($this->extra_joining_str, 'ot_gv') === FALSE)) {
                                    $this->extra_joining_str .= " LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv 
                                                                ON o.orders_id=ot_gv.orders_id AND ot_gv.class='ot_gv' ";
                                }
                            }

                            for ($i = 0; $i < $this->size; $i++) {
                                $date_type_extra_join_str = '';

                                # DATE TYPE filtering
                                if (tep_not_null($this->date_type) && ($this->date_type > 1)) {
                                    $date_type_extra_join_str = "	LEFT JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss 
                                                                    ON oss.orders_id = o.orders_id ";
                                    $date_range_where_sql = " oss.first_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND oss.first_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' AND oss.orders_status_id = '" . $this->date_type . "'";
                                } else { // First Pending from `orders`
                                    $date_range_where_sql = " o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND o.date_purchased < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
                                }

                                $sales_sql = $orig_sales_sql . " FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                INNER JOIN " . TABLE_ORDERS . " AS o 
                                                                    ON op.orders_id = o.orders_id " . $this->extra_joining_str . $date_type_extra_join_str . "
                                                                WHERE op.parent_orders_products_id=0 
                                                                    AND op.orders_products_is_compensate=0 " .
                                        ($this->sql_filter ? " AND " . $this->sql_filter : "");

                                $sales_per_order_sql = $orig_sales_per_order_sql . " FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                                    INNER JOIN " . TABLE_ORDERS . " AS o 
                                                                                            ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
                                                                                    WHERE op.parent_orders_products_id=0 
                                                                                    AND op.orders_products_is_compensate=0 " .
                                        ($orig_sql_filter ? " AND " . $orig_sql_filter : "");

                                $report_select_sql = $sales_sql . " AND " . $date_range_where_sql;

                                if (count($this->order_amount_status_array)) {
                                    $report_select_sql .= " GROUP BY o.orders_id";
                                    $report_result_sql = tep_db_query($report_select_sql, 'read_db_link');

                                    $this->info[$i]['count'] = 0;
                                    $this->info[$i]['sum'] = 0;
                                    $this->info[$i]['orders_rate'] = 0;
                                    $this->info[$i]['sales_qty'] = 0;
                                    $this->info[$i]['ot_gv'] = 0;
                                    $this->info[$i]['ot_coupon'] = 0;
                                    $this->info[$i]['ot_ogc'] = 0;

                                    while ($report = tep_db_fetch_array($report_result_sql)) {
                                        $sales_amount = $this->get_sales_amount($report);

                                        if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                                            if (!tep_not_null($this->export_report) || ($this->mode == 2 && tep_not_null($this->export_report) && $payment_method_sel[$pg_cnt]['payment_methods_parent_id'] == 0)) {
                                                $this->info[$i]['sum'] += $sales_amount;
                                            }
                                            $this->info[$i]['count']++;

                                            if ($this->mode == 2) {
                                                if ((is_array($this->order[$i]['order_id']) && !in_array($report["orders_id"], $this->order[$i]['order_id'])) || !is_array($this->order[$i]['order_id'])) {
                                                    $this->order[$i]['order_id'][] = $report["orders_id"];
                                                    $this->order[$i]['order_amount'][] = $sales_amount;
                                                }
                                            } else {
                                                $this->order[$i]['order_id'][] = $report["orders_id"];
                                                $this->order[$i]['order_amount'][] = $sales_amount;
                                            }

                                            if ($this->mode == 2 && tep_not_null($this->export_report)) {
                                                $ot_subtotal_value = 0;
                                                $ot_gv_value = 0;
                                                $ot_coupon_value = 0;
                                                $ot_ogc_value = 0;
                                                $ot_subtotal = 0;
                                                $ot_gv = 0;
                                                $ot_coupon = 0;
                                                $ot_ogc = 0;

                                                # Orders Total: ot_subtotal, ot_coupon, ot_gv 
                                                $sales_ot_select_sql = "SELECT ot_subtotal.value AS ot_subtotal_value, 
                                                                            ot_gv.value AS ot_gv_value, ot_coupon.value AS ot_coupon_value, 
                                                                            ot_ogc.value AS ot_ogc_value, 
                                                                            (SELECT ((IF(ot_subtotal_value > 0, ot_subtotal_value, 0)) + (IF(ot_gv_value > 0, ot_gv_value, 0)) + (IF(ot_coupon_value > 0, ot_coupon_value, 0)))) AS ot_sum_value 
                                                                        FROM " . TABLE_ORDERS_TOTAL . " AS ot_subtotal 
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv 
                                                                            ON ot_gv.orders_id = ot_subtotal.orders_id 
                                                                                    AND ot_gv.class='ot_gv' 
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_coupon 
                                                                            ON ot_coupon.orders_id = ot_subtotal.orders_id 
                                                                                    AND ot_coupon.class='ot_coupon' 
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_ogc 
                                                                            ON ot_ogc.orders_id = ot_subtotal.orders_id 
                                                                                    AND ot_ogc.class='ot_ogc' 
                                                                        WHERE ot_subtotal.orders_id='" . $report['orders_id'] . "' 
                                                                            AND ot_subtotal.class='ot_subtotal'";
                                                $sales_ot_result = tep_db_query($sales_ot_select_sql, 'read_db_link');
                                                if ($sales_ot_row = tep_db_fetch_array($sales_ot_result)) {
                                                    $ot_subtotal_value = tep_not_null($sales_ot_row['ot_subtotal_value']) ? $sales_ot_row['ot_subtotal_value'] : 0;
                                                    $ot_gv_value = tep_not_null($sales_ot_row['ot_gv_value']) ? $sales_ot_row['ot_gv_value'] : 0;
                                                    $ot_coupon_value = tep_not_null($sales_ot_row['ot_coupon_value']) ? $sales_ot_row['ot_coupon_value'] : 0;
                                                    $ot_ogc_value = tep_not_null($sales_ot_row['ot_ogc_value']) ? $sales_ot_row['ot_ogc_value'] : 0;
                                                }

                                                $sales_per_order_select_sql = $sales_per_order_sql . " AND o.orders_id = '" . $report['orders_id'] . "' GROUP BY o.orders_id";
                                                $sales_per_order_result_sql = tep_db_query($sales_per_order_select_sql, 'read_db_link');
                                                if ($sales_per_order_row = tep_db_fetch_array($sales_per_order_result_sql)) {
                                                    if (($sales_amount > 0) && ($sales_per_order_row['total_purchase_amount'] > 0)) {
                                                        $this->info[$i]['orders_rate'] += $sales_amount / $sales_per_order_row['total_purchase_amount'];

                                                        if ($ot_gv_value > 0) {
                                                            $ot_gv = ($ot_gv_value / $ot_subtotal_value) * $sales_amount;
                                                            $this->info[$i]['ot_gv'] += $ot_gv;
                                                        }

                                                        if ($ot_coupon_value > 0) {
                                                            $ot_coupon = ($ot_coupon_value / $ot_subtotal_value) * $sales_amount;
                                                            $this->info[$i]['ot_coupon'] += $ot_coupon;
                                                        }

                                                        if ($ot_ogc_value > 0) {
                                                            $ot_ogc = ($ot_ogc_value / $ot_subtotal_value) * $sales_amount;
                                                            $this->info[$i]['ot_ogc'] += $ot_ogc;
                                                        }

                                                        if ($ot_subtotal_value > 0) {
                                                            $ot_subtotal = $sales_amount - ($ot_gv + $ot_coupon + $ot_ogc);
                                                            $this->info[$i]['sum'] += $ot_subtotal;
                                                        }
                                                    }
                                                }


                                                /* -- product quantity sold -- */
                                                $prod_qty_sold_select_sql = "SELECT p.products_bundle, o.orders_status, o.orders_cb_status, op.products_id, op.orders_products_id, 
                                                                            " . $this->order_qty_fileds_str . "
                                                                            FROM " . TABLE_ORDERS . " AS o
                                                                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                                    ON op.orders_id = o.orders_id
                                                                            INNER JOIN " . TABLE_PRODUCTS . " AS p
                                                                                    ON op.products_id = p.products_id " .
                                                        $this->extra_joining_str . " WHERE o.orders_id = '" . $report['orders_id'] . "' " .
                                                        ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
                                                                            AND op.orders_products_is_compensate=0
                                                                            AND op.parent_orders_products_id = 0";
                                                $prod_qty_sold_result_sql = tep_db_query($prod_qty_sold_select_sql, 'read_db_link');
                                                while ($prod_qty_sold_row = tep_db_fetch_array($prod_qty_sold_result_sql)) {
                                                    if ($prod_qty_sold_row['products_bundle'] == 'yes') {
                                                        // GET SubProduct 'QTY SOLD'
                                                        $sub_prod_qty_sold_select_sql = "SELECT o.orders_status, o.orders_cb_status, " . $this->order_qty_fileds_str . "
                                                                                        FROM " . TABLE_ORDERS . " AS o
                                                                                        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                                                ON o.orders_id = op.orders_id " . $this->extra_joining_str . "
                                                                                        WHERE op.parent_orders_products_id = '" . $prod_qty_sold_row["orders_products_id"] . "'";
                                                        $sub_prod_qty_sold_result_sql = tep_db_query($sub_prod_qty_sold_select_sql, 'read_db_link');
                                                        while ($sub_prod_qty_sold_row = tep_db_fetch_array($sub_prod_qty_sold_result_sql)) {
                                                            $sub_sales_qty = $this->get_sales_qty($sub_prod_qty_sold_row);
                                                            $this->info[$i]['sales_qty'] += $sub_sales_qty;
                                                        }
                                                    } else {
                                                        $sales_qty = $this->get_sales_qty($prod_qty_sold_row);

                                                        if ($sales_qty) {
                                                            $this->info[$i]['sales_qty'] += $sales_qty;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if ($this->info[$i]['count']) {
                                        $this->info[$i]['avg'] = $this->info[$i]['sum'] / $this->info[$i]['count'];
                                    }
                                } else {
                                    $report_result_sql = tep_db_query($report_select_sql, 'read_db_link');
                                    $report = tep_db_fetch_array($report_result_sql);

                                    if ($this->mode == 2 && tep_not_null($this->export_report)) {
                                        $this->info[$i]['sum'] = 0;
                                        $this->info[$i]['orders_rate'] = 0;
                                        $this->info[$i]['sales_qty'] = 0;
                                        $this->info[$i]['ot_gv'] = 0;
                                        $this->info[$i]['ot_coupon'] = 0;
                                        $this->info[$i]['ot_ogc'] = 0;

                                        $sales_orders_id_sql = "SELECT DISTINCT o.orders_id 
                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                INNER JOIN " . TABLE_ORDERS . " AS o
                                                                        ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
                                                                WHERE op.parent_orders_products_id=0
                                                                        AND op.orders_products_is_compensate=0 " .
                                                ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
                                                                        AND " . $date_range_where_sql;
                                        $sales_orders_id_result = tep_db_query($sales_orders_id_sql, 'read_db_link');
                                        while ($sales_orders_id_row = tep_db_fetch_array($sales_orders_id_result)) {
                                            $ot_subtotal_value = 0;
                                            $ot_gv_value = 0;
                                            $ot_coupon_value = 0;
                                            $ot_ogc_value = 0;
                                            $ot_subtotal = 0;
                                            $ot_gv = 0;
                                            $ot_coupon = 0;
                                            $ot_ogc = 0;

                                            if ($payment_method_sel[$pg_cnt]['payment_methods_parent_id'] != 0) { # FULL STORE CREDIT
                                                # Orders Total: ot_subtotal, ot_coupon, ot_gv 
                                                $sales_ot_select_sql = "SELECT ot_subtotal.value AS ot_subtotal_value, 
                                                                                ot_gv.value AS ot_gv_value, ot_coupon.value AS ot_coupon_value,
                                                                                (SELECT ((IF(ot_subtotal_value > 0, ot_subtotal_value, 0)) + (IF(ot_gv_value > 0, ot_gv_value, 0)) + (IF(ot_coupon_value > 0, ot_coupon_value, 0)))) AS ot_sum_value
                                                                        FROM " . TABLE_ORDERS_TOTAL . " AS ot_subtotal
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv
                                                                                ON ot_gv.orders_id = ot_subtotal.orders_id
                                                                                        AND ot_gv.class='ot_gv'
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_coupon
                                                                                ON ot_coupon.orders_id = ot_subtotal.orders_id
                                                                                        AND ot_coupon.class='ot_coupon'
                                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_ogc
                                                                                ON ot_ogc.orders_id = ot_subtotal.orders_id
                                                                                        AND ot_ogc.class='ot_ogc'
                                                                        WHERE ot_subtotal.orders_id='" . $sales_orders_id_row['orders_id'] . "'
                                                                                AND ot_subtotal.class='ot_subtotal'";
                                                $sales_ot_result = tep_db_query($sales_ot_select_sql, 'read_db_link');
                                                if ($sales_ot_row = tep_db_fetch_array($sales_ot_result)) {
                                                    $ot_subtotal_value = tep_not_null($sales_ot_row['ot_subtotal_value']) ? $sales_ot_row['ot_subtotal_value'] : 0;
                                                    $ot_gv_value = tep_not_null($sales_ot_row['ot_gv_value']) ? $sales_ot_row['ot_gv_value'] : 0;
                                                    $ot_coupon_value = tep_not_null($sales_ot_row['ot_coupon_value']) ? $sales_ot_row['ot_coupon_value'] : 0;
                                                    $ot_ogc_value = tep_not_null($sales_ot_row['ot_ogc_value']) ? $sales_ot_row['ot_ogc_value'] : 0;
                                                }
                                            }

                                            $_cond_sql = '';
                                            $_join_sql = '';

                                            if ($cpt_id == 2) {
                                                $_cond_sql = " AND p.products_flag_id != 4 ";
                                                $_join_sql = " LEFT JOIN " . TABLE_PRODUCTS . " AS p ON p.products_id = op1.products_id ";
                                            } else if ($cpt_id == 'gift_card') {
                                                $_cond_sql = " AND p.products_flag_id = 4 ";
                                                $_join_sql = " LEFT JOIN " . TABLE_PRODUCTS . " AS p ON p.products_id = op1.products_id ";
                                            }

                                            $sales_per_order_select_sql = "	SELECT SUM(op.final_price*op.products_quantity) AS total_purchase_amount, 
																			(	SELECT SUM(op1.final_price*op1.products_quantity) 
																				FROM " . TABLE_ORDERS_PRODUCTS . " AS op1 " .
                                                    $_join_sql . " 
																				WHERE op1.orders_id = o.orders_id 
																					AND op1.parent_orders_products_id=0 
																					AND op1.orders_products_is_compensate=0 
																					AND op1.custom_products_type_id = '" . ($cpt_id == 'gift_card' ? 2 : $cpt_id) . "' " .
                                                    $_cond_sql . " 
																			) AS sales_value
																			FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
																			INNER JOIN " . TABLE_ORDERS . " AS o 
																				ON op.orders_id = o.orders_id 
																			WHERE op.parent_orders_products_id=0 
																				AND op.orders_products_is_compensate=0 " .
                                                    ($orig_sql_filter ? " AND " . $orig_sql_filter : "") . "
																				AND o.orders_id = '" . $sales_orders_id_row['orders_id'] . "' 
																			GROUP BY o.orders_id";
                                            $sales_per_order_result_sql = tep_db_query($sales_per_order_select_sql, 'read_db_link');
                                            if ($sales_per_order_row = tep_db_fetch_array($sales_per_order_result_sql)) {
                                                if (($sales_per_order_row['sales_value'] > 0) && ($sales_per_order_row['total_purchase_amount'] > 0)) {
                                                    $this->info[$i]['orders_rate'] += $sales_per_order_row['sales_value'] / $sales_per_order_row['total_purchase_amount'];

                                                    if ($payment_method_sel[$pg_cnt]['payment_methods_parent_id'] != 0) { # Discount and Partial SC for PG
                                                        if ($ot_gv_value > 0) {
                                                            $ot_gv = ($ot_gv_value / $ot_subtotal_value) * $sales_per_order_row['sales_value'];
                                                            $this->info[$i]['ot_gv'] += $ot_gv;
                                                        }

                                                        if ($ot_coupon_value > 0) {
                                                            $ot_coupon = ($ot_coupon_value / $ot_subtotal_value) * $sales_per_order_row['sales_value'];
                                                            $this->info[$i]['ot_coupon'] += $ot_coupon;
                                                        }

                                                        if ($ot_ogc_value > 0) {
                                                            $ot_ogc = ($ot_ogc_value / $ot_subtotal_value) * $sales_per_order_row['sales_value'];
                                                            $this->info[$i]['ot_ogc'] += $ot_ogc;
                                                        }

                                                        if ($ot_subtotal_value > 0) {
                                                            $ot_subtotal = $ot_subtotal_value - ($ot_gv + $ot_coupon + $ot_ogc);
                                                            $this->info[$i]['sum'] += $ot_subtotal;
                                                        }
                                                    }
                                                }
                                            }


                                            if ($_join_sql != '') {
                                                $_join_sql = " LEFT JOIN " . TABLE_PRODUCTS . " AS p ON p.products_id = op.products_id ";
                                            }

                                            /* -- product quantity sold -- */
                                            $prod_qty_sold_select_sql = "	SELECT IF((SELECT COUNT(op1.orders_products_id) 
																				FROM " . TABLE_ORDERS_PRODUCTS . " AS op1 
																				WHERE op1.parent_orders_products_id = op.orders_products_id) > 0, 
																				(SELECT SUM(op1.products_quantity) 
																				FROM " . TABLE_ORDERS_PRODUCTS . " AS op1 
																				WHERE op1.parent_orders_products_id = op.orders_products_id), 
																				SUM(op.products_quantity)) AS total_prod_qty 
																			FROM " . TABLE_ORDERS_PRODUCTS . " AS op " .
                                                    $_join_sql . " 
																			WHERE op.orders_id = '" . $sales_orders_id_row['orders_id'] . "' 
																				AND op.orders_products_is_compensate=0 
																				AND op.parent_orders_products_id = 0 
																				AND op.custom_products_type_id = '" . ($cpt_id == 'gift_card' ? 2 : $cpt_id) . "' " .
                                                    $_cond_sql . " 
																			GROUP BY op.orders_products_id";
                                            $prod_qty_sold_result_sql = tep_db_query($prod_qty_sold_select_sql, 'read_db_link');
                                            while ($prod_qty_sold_row = tep_db_fetch_array($prod_qty_sold_result_sql)) {
                                                $this->info[$i]['sales_qty'] += $prod_qty_sold_row['total_prod_qty'];
                                            }
                                        }
                                    }

                                    if (!tep_not_null($this->export_report) || (tep_not_null($this->export_report) && $payment_method_sel[$pg_cnt]['payment_methods_parent_id'] == 0)) {
                                        $this->info[$i]['sum'] = tep_not_null($report['value']) ? $report['value'] : 0;
                                    }
                                    $this->info[$i]['avg'] = tep_not_null($report['avg']) ? $report['avg'] : 0;
                                    $this->info[$i]['count'] = tep_not_null($report['count']) ? $report['count'] : 0;
                                }

                                if ($this->mode == 2 && tep_not_null($this->export_report)) {
                                    $payment_methods_parent_id = $payment_method_sel[$pg_cnt]['payment_methods_parent_id'];
                                    $payment_methods_id = $payment_method_sel[$pg_cnt]['payment_methods_id'];

                                    $this->csv_data[$payment_methods_parent_id][$payment_methods_id][$cpt_id][$this->startDates[$i]] = array('order' => $this->info[$i]['orders_rate'],
                                        'sales_qty' => $this->info[$i]['sales_qty'],
                                        'sales' => $this->info[$i]['sum'],
                                        'sc' => $this->info[$i]['ot_gv'],
                                        'dc' => $this->info[$i]['ot_coupon'],
                                        'ogc' => $this->info[$i]['ot_ogc']);
                                }

                                switch ($this->mode) {
                                    case '1': // Hourly
                                        $this->info[$i]['text'] = strftime("%H", $this->startDates[$i]) . " - " . strftime("%H", $this->endDates[$i]);
                                        $this->info[$i]['link'] = "";

                                        if (!count($this->order_amount_status_array)) {
                                            $sales_per_order_select_sql = $sales_per_order_sql . " AND o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND o.date_purchased < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' GROUP BY o.orders_id ";
                                            $sales_per_order_result_sql = tep_db_query($sales_per_order_select_sql, 'read_db_link');

                                            while ($sales_per_order_row = tep_db_fetch_array($sales_per_order_result_sql)) {
                                                if (is_array($this->order[$i]['order_id'])) {
                                                    if (!in_array($sales_per_order_row["orders_id"], $this->order[$i]['order_id'])) {
                                                        $this->order[$i]['order_id'][] = $sales_per_order_row["orders_id"];
                                                        $this->order[$i]['order_amount'][] = $sales_per_order_row["value"];
                                                    }
                                                } else {
                                                    $this->order[$i]['order_id'][] = $sales_per_order_row["orders_id"];
                                                    $this->order[$i]['order_amount'][] = $sales_per_order_row["value"];
                                                }
                                            }
                                        }
                                        break;

                                    case '2': // Daily
                                        $this->info[$i]['text'] = date("Y-m-d", $this->startDates[$i]) . " (" . date('D', $this->startDates[$i]) . ")";
                                        $this->info[$i]['link'] = "report=1&cont=1&action=show_report&start_date=" . $this->startDates[$i] . "&end_date=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) + 1, date("Y", $this->endDates[$i]));
                                        break;

                                    case '3': // Weekly
                                        $this->info[$i]['text'] = date("Y-m-d", $this->startDates[$i]) . TEXT_RANGE_OPERATOR . date("Y-m-d", mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i])));
                                        $this->info[$i]['link'] = "report=2&cont=1&action=show_report&start_date=" . $this->startDates[$i] . "&end_date=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i]));
                                        break;

                                    case '4': // Monthly
                                        $this->info[$i]['text'] = date("M Y", $this->startDates[$i]);
                                        $this->info[$i]['link'] = "report=3&cont=1&action=show_report&start_date=" . $this->startDates[$i] . "&end_date=" . mktime(0, 0, 0, date("m", $this->endDates[$i]), date("d", $this->endDates[$i]) - 1, date("Y", $this->endDates[$i]));
                                        break;

                                    case '5': // Yearly
                                        $this->info[$i]['text'] = date("Y", $this->startDates[$i]);
                                        $this->info[$i]['link'] = "report=4&cont=1&action=show_report&start_date=" . $this->startDates[$i] . "&end_date=" . mktime(0, 0, 0, date("m", $this->endDates[$i]) - 1, date("d", $this->endDates[$i]), date("Y", $this->endDates[$i]));
                                        break;
                                }
                            }
                        }
                    }
                } else if ($this->mode == 2 && $this->export_format == 'export_by_order') {
                    $this->export_payment_method = $payment_method_sel;
                    $this->export_payment_parent_method = $payment_parent_method_sel;
                }
                break;

            case 6:
                $level = 0;
                $from_cat_id = isset($_SESSION['sales_param']["cat_id"]) && $_SESSION['sales_param']["cat_id"] >= 0 ? $_SESSION['sales_param']["cat_id"] : 0;
                $this->getCatTree($from_cat_id, (int) $languages_id, $level);
                $cat_array = $this->cat_array;
                unset($this->cat_array);
                $this->info = $cat_array;

                $uncategorise_total = 0;

                if (count($this->order_amount_status_array)) {
                    $prod_total_price_select_sql = "SELECT o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str . " 
													FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
													LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
														ON op.products_id = pc.products_id 
													INNER JOIN " . TABLE_ORDERS . " AS o 
														ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
													WHERE pc.categories_id IS NULL 
														AND op.parent_orders_products_id=0 
														AND op.orders_products_is_compensate=0 
														AND $this->selStartDateStr 
														AND $this->selEndDateStr " .
                            ($this->sql_filter ? " AND " . $this->sql_filter : "") . "
													GROUP BY o.orders_id ";
                    $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
                    while ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                        $sales_amount = $this->get_sales_amount($prod_total_price_row);

                        if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                            $uncategorise_total += $sales_amount;
                        }
                    }
                } else {
                    $prod_total_price_select_sql = "SELECT SUM(op.final_price*op.products_quantity) AS toal_prod_sales 
													FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
													LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
														ON op.products_id = pc.products_id 
													INNER JOIN " . TABLE_ORDERS . " AS o 
														ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
													WHERE pc.categories_id IS NULL 
														AND op.parent_orders_products_id=0 
														AND op.orders_products_is_compensate=0 
														AND $this->selStartDateStr 
														AND $this->selEndDateStr " .
                            ($this->sql_filter ? " AND " . $this->sql_filter : "");
                    $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
                    if ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                        $uncategorise_total = $prod_total_price_row["toal_prod_sales"];
                    }
                }

                if ($uncategorise_total) {
                    $this->info[] = array('id' => -1,
                        'name' => "<i>Uncategories</i>",
                        'subtotal' => $uncategorise_total,
                        'level' => 0
                    );
                }
                break;

            case 7: # Category (Sales + Stock)
                $level = 0;
                $from_cat_id = isset($_SESSION['sales_param']["cat_id"]) && $_SESSION['sales_param']["cat_id"] >= 0 ? $_SESSION['sales_param']["cat_id"] : 0;
                $cat_array = $this->categoryGetCatTree($from_cat_id, (int) $languages_id, $level);
                $this->info = $cat_array;
                break;

            case 8: # Country
                $tmp_country_array = array();

                $country_sales_select_str = "SELECT o.orders_id, o.customers_id ";

                if (count($this->order_amount_status_array) && tep_not_null($this->get_sales_stat_select_sql())) {
                    $country_sales_select_str .= ', ' . $this->get_sales_stat_select_sql();
                } else {
                    $country_sales_select_str .= ", op.final_price*op.products_quantity AS value ";
                }

                /* -- report date range -- */
                if ($this->sales_period == 'day') {
                    $date_range_where_sql = " AND $this->selStartDateStr AND $this->selEndDateStr ";
                } else {
                    $date_range_where_sql = " AND o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "'
											AND o.date_purchased <= '" . date("Y-m-d H:i:s", $this->endDates[count($this->endDates) - 1]) . "' ";
                }

                /* -- retrieve date purchased -- */
                $country_sales_select_str .= ", o.customers_telephone_country, o.date_purchased";
                $country_sales_select_sql = $country_sales_select_str . "
                                            FROM " . TABLE_ORDERS . " AS o 
                                            INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                ON o.orders_id=op.orders_id " . $this->extra_joining_str . " 
                                            LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei 
                                                ON oei.orders_id = o.orders_id 
                                                AND oei.orders_extra_info_key = 'site_id' 
                                            WHERE op.parent_orders_products_id=0 
                                                AND op.orders_products_is_compensate=0
                                                AND op.custom_products_type_id <> 3
                                            " . $date_range_where_sql . "
                                            " . ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
                                                AND oei.orders_extra_info_value " . ($this->order_site == 5 ? " = 5" : " <> 5");

                $country_sales_result_sql = tep_db_query($country_sales_select_sql, 'read_db_link');

                $tmp_country_value = array();  # array collect sales value by country name and purchase date

                while ($country_sales_row = tep_db_fetch_array($country_sales_result_sql)) {
                    if ($country_sales_row['value'] > 0) {
                        $country_name = !empty($country_sales_row['customers_telephone_country']) ? $country_sales_row['customers_telephone_country'] : 'Uncategories';

                        if (isset($tmp_country_array[$country_name])) {
                            $tmp_country_array[$country_name]['orders_id'][$country_sales_row['orders_id']] = $country_sales_row['orders_id'];

                            $tmp_country_array[$country_name]['customers_id'][$country_sales_row['customers_id']] = $country_sales_row['customers_id'];

                            $tmp_country_array[$country_name]['sum'] += $country_sales_row['value'];
                        } else {
                            $tmp_country_array[$country_name] = array('country_name' => $country_name,
                                'text' => ($country_name == 'Uncategories' ? "<i>Uncategories</i>" : $country_name));
                            $tmp_country_array[$country_name]['orders_id'] = array($country_sales_row['orders_id'] => $country_sales_row['orders_id']);
                            $tmp_country_array[$country_name]['customers_id'] = array($country_sales_row['customers_id'] => $country_sales_row['customers_id']);
                            $tmp_country_array[$country_name]['sum'] = $country_sales_row['value'];
                        }

                        /* -- collecting sales value by country name and purchase date -- */
                        foreach ($country_sales_row as $key => $value) {
                            if ($key == 'country_name') {
                                $country_name = $value;
                            } else if ($key == 'date_purchased') {
                                list($date, $time) = explode(' ', $value);
                                list($yrs, $mth, $day) = explode('-', $date);
                                $date_purchased = mktime(0, 0, 0, $mth, $day, $yrs);
                            } else if ($key == 'value') {
                                $subvalue = $value;
                            }
                        }
                        $tmp_country_value[$country_name][$date_purchased] += $subvalue;
                    }
                }

                /* -- grouping sales value -- */
                $subtotal = [];
                foreach ($tmp_country_value as $country_name => $values) {
                    $subtotal = $this->subtotal_calculation($subtotal, $values);
                    $tmp_country_array[$country_name]['subtotal'] = $subtotal;
                    unset($subtotal);
                }

                // Sort according user's sorting preference
                $sorted_result = $this->sort_country_result($tmp_country_array);

                if (is_array($sorted_result) && count($sorted_result)) {
                    foreach ($sorted_result as $sort_key => $res_info) {
                        $this->info = array_merge($this->info, $res_info);
                    }
                }
                break;

            /* -- Report Type: Customer Discount Group -- */
            case 9:
                $report_info = array();
                $subtotal = array();
                $cust_gid_array = array();
                $case_select_sql_str = '';

                if (count($this->order_amount_status_array) && tep_not_null($this->get_sales_stat_select_sql())) {
                    $case_select_sql_str = ', ' . $this->get_sales_stat_select_sql();
                }

                if (!tep_not_null($case_select_sql_str)) {
                    $case_select_sql_str = ', op.final_price*op.products_quantity AS value ';
                }

                $customer_sales_select_str = "SELECT DATE_FORMAT(o.date_purchased, '%Y-%m-%d') AS date_purchased, o.customers_id, o.orders_id, o.customers_groups_id " .
                        $case_select_sql_str;

                if ($this->sales_period == 'day') {
                    $date_range_where_sql = " AND $this->selStartDateStr AND $this->selEndDateStr ";
                } else {
                    $date_range_where_sql = " AND o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "'
											AND o.date_purchased <= '" . date("Y-m-d H:i:s", $this->endDates[count($this->endDates) - 1]) . "' ";
                }

                /* -- retrieve all customer group, except "Guest" -- */
                $sql = "SELECT customers_groups_id, customers_groups_name 
						FROM " . TABLE_CUSTOMERS_GROUPS . " 
						WHERE customers_groups_id != '1' " .
                        (tep_not_null($this->sort_by) && ($this->sort_by == 'sort_order') ? " ORDER BY sort_order " . $this->sort_order . ", customers_groups_name " . $this->sort_order : '');

                $result_sql = tep_db_query($sql, 'read_db_link');
                while ($rows = tep_db_fetch_array($result_sql)) {
                    $cust_gid = $rows['customers_groups_id'];
                    $report_info[$cust_gid]['customers_groups_name'] = $rows['customers_groups_name'];
                    $report_info[$cust_gid]['customers_groups_id'] = $cust_gid;

                    $cust_array[$cust_gid] = array();
                    $order_array[$cust_gid] = array();
                    $cust_gid_array[] = $cust_gid;

                    $customer_sales_from_str = "FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON o.orders_id=op.orders_id " . $this->extra_joining_str . " 
												WHERE op.parent_orders_products_id=0 
													AND op.orders_products_is_compensate=0 
                                                    AND op.custom_products_type_id <> 3 " .
                            $date_range_where_sql .
                            ($this->sql_filter ? " AND " . $this->sql_filter : "") .
                            " AND o.customers_groups_id = '" . $cust_gid . "'";
                    $customer_sales_query_sql = $customer_sales_select_str . ' ' . $customer_sales_from_str;
                    $customer_sales_result_sql = tep_db_query($customer_sales_query_sql, 'read_db_link');
                    while ($entries = tep_db_fetch_array($customer_sales_result_sql)) {
                        $value = $entries['value'];

                        list($yrs, $mth, $day) = explode('-', $entries['date_purchased']);
                        $date = mktime(0, 0, 0, $mth, $day, $yrs);

                        for ($i = 0; $this->size > $i; $i++) {
                            if (($date >= $this->startDates[$i]) && ($date <= $this->endDates[$i])) {
                                $date_purchased = $this->startDates[$i];
                                break;
                            }
                        }

                        if ($value > 0) {
                            if (!in_array($entries['orders_id'], $order_array[$cust_gid])) {
                                $order_array[$cust_gid][] = $entries['orders_id'];
                            }

                            if (!in_array($entries['customers_id'], $cust_array[$cust_gid])) {
                                $cust_array[$cust_gid][] = $entries['customers_id'];
                            }
                        }

                        $report_info[$cust_gid]['subtotal'][$date_purchased] += $value;
                        $report_info[$cust_gid]['sum'] += $value;
                        $subtotal[$date_purchased] += $value;
                    }

                    $report_info[$cust_gid]['total_customer'] = (int) count($cust_array[$cust_gid]);
                    $report_info[$cust_gid]['count'] += (int) count($order_array[$cust_gid]);
                    unset($cust_array[$cust_gid]);
                    unset($order_array[$cust_gid]);

                    if (($report_info[$cust_gid]['sum'] > 0) && ($report_info[$cust_gid]['count'] > 0)) {
                        $report_info[$cust_gid]['avg'] = $report_info[$cust_gid]['sum'] / $report_info[$cust_gid]['count'];
                    } else {
                        $report_info[$cust_gid]['avg'] = 0;
                    }
                }


                for ($i = 0; count($this->startDates) > $i; $i++) {
                    $date_purchased = $this->startDates[$i];

                    for ($j = 0, $cnt = count($cust_gid_array); $cnt > $j; $j++) {
                        $cust_gid = $cust_gid_array[$j];

                        if (($subtotal[$date_purchased] > 0) && ($report_info[$cust_gid]['subtotal'][$date_purchased])) {
                            $report_info[$cust_gid]['sales_percentage'][$date_purchased] = round((($report_info[$cust_gid]['subtotal'][$date_purchased] / $subtotal[$date_purchased]) * 100), 2);
                        } else {
                            $report_info[$cust_gid]['sales_percentage'][$date_purchased] = 0;
                        }
                    }
                }

                // Sort according user's sorting preference
                $sorted_result = $this->sort_country_result($report_info);

                if (is_array($sorted_result) && count($sorted_result)) {
                    foreach ($sorted_result as $sort_key => $res_info) {
                        $this->info = array_merge($this->info, $res_info);
                    }
                }

                $this->subtotal = $subtotal;
                unset($report_info);
                break;

            /* -- Report Type: New Sign Up -- */
            case 10:
                $new_sign_up = array();
                $tmp_new_sign_up = array();
                $tmp_new_sign_up_array = array();
                $case_select_sql_str = '';

                if (count($this->order_amount_status_array) && tep_not_null($this->get_sales_stat_select_sql())) {
                    $case_select_sql_str = ', ' . $this->get_sales_stat_select_sql();
                } else {
                    $case_select_sql_str = ', SUM(op.final_price*op.products_quantity) AS value ';
                }


                # total new sign up
                if ($this->sales_period == 'day') {
                    $new_date_range_where_sql = " $this->selStartDateStr AND $this->selEndDateStr ";
                    $total_new_sign_up_group_by_sql = " GROUP BY day(customers_info_date_account_created), customer_info_selected_country";
                } else {
                    $new_date_range_where_sql = " customers_info_date_account_created >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "'
												  AND customers_info_date_account_created <= '" . date("Y-m-d H:i:s", $this->endDates[count($this->endDates) - 1]) . "' ";
                    $total_new_sign_up_group_by_sql = " GROUP BY month(customers_info_date_account_created), customer_info_selected_country";
                }

                $total_new_sign_up_query_sql = "SELECT customer_info_selected_country, COUNT(customers_info_date_account_created) AS new_sign_up, date_format(customers_info_date_account_created, '%Y-%m-%d') as sign_up_date 
												FROM " . TABLE_CUSTOMERS_INFO . " 
												WHERE " . $new_date_range_where_sql . $total_new_sign_up_group_by_sql;
                $total_new_sign_up_result_sql = tep_db_query($total_new_sign_up_query_sql, 'read_db_link');

                while ($total_new_sign_up_row = tep_db_fetch_array($total_new_sign_up_result_sql)) {
                    if ($total_new_sign_up_row['customer_info_selected_country'] > 0) {
                        $country_info = tep_get_countries_info($total_new_sign_up_row['customer_info_selected_country'], 'countries_id');
                    } else {
                        $country_info = array('countries_name' => '<i>Uncategories</i>');
                    }

                    list($yrs, $mth, $day) = explode('-', $total_new_sign_up_row['sign_up_date']);
                    $create_date = mktime(0, 0, 0, $mth, $day, $yrs);
                    $tmp_new_sign_up[$country_info['countries_name']][$create_date]['new_sign_up'] = $total_new_sign_up_row['new_sign_up'];
                    $new_sign_up[$country_info['countries_name']]['total_sign_up'] += $total_new_sign_up_row['new_sign_up'];
                }

                # total new sign up with purchase
                if ($this->sales_period == 'day') {
                    $date_range_where_sql = " AND $this->selStartDateStr AND $this->selEndDateStr ";
                } else {
                    $date_range_where_sql = " AND ci.customers_info_date_account_created >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "'
											  AND ci.customers_info_date_account_created <= '" . date("Y-m-d H:i:s", $this->endDates[count($this->endDates) - 1]) . "' ";
                }

                $new_sign_up_query_sql = "	SELECT o.customers_id, COUNT(DISTINCT(o.orders_id)) AS orders, ci.customer_info_selected_country, ci.customers_info_date_account_created " . $case_select_sql_str . "
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON o.orders_id=op.orders_id 
											LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
												ON o.customers_id=ci.customers_info_id " . $this->extra_joining_str . " 
											WHERE op.parent_orders_products_id=0 
												AND op.orders_products_is_compensate=0 
                                                AND op.custom_products_type_id <> 3" .
                        $date_range_where_sql .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "") . "
												AND (DATE_ADD(ci.customers_info_date_account_created, INTERVAL 1 DAY) >= o.date_purchased) 
											GROUP BY o.customers_id";
                $new_sign_up_result_sql = tep_db_query($new_sign_up_query_sql, 'read_db_link');

                while ($new_sign_up_row = tep_db_fetch_array($new_sign_up_result_sql)) {
                    list($date, $time) = explode(' ', $new_sign_up_row['customers_info_date_account_created']);
                    list($yrs, $mth, $day) = explode('-', $date);
                    $create_date = mktime(0, 0, 0, $mth, $day, $yrs);

                    if ($new_sign_up_row['value'] > 0) {
                        if ($new_sign_up_row['customer_info_selected_country'] > 0) {
                            $country_info = tep_get_countries_info($new_sign_up_row['customer_info_selected_country'], 'countries_id');
                        } else {
                            $country_info = array('countries_name' => 'Uncategories');
                        }

                        $tmp_new_sign_up[$country_info['countries_name']][$create_date]['total_customer'] += 1;
                        $tmp_new_sign_up[$country_info['countries_name']][$create_date]['count'] += $new_sign_up_row['orders'];
                        $tmp_new_sign_up[$country_info['countries_name']][$create_date]['subtotal'] += $new_sign_up_row['value'];
                    }
                }

                unset($tmp_new_sign_up_array);

                if (tep_not_null($tmp_new_sign_up)) {
                    foreach ($tmp_new_sign_up as $country_id => $country_data) {
                        foreach ($country_data as $key => $value) {
                            for ($i = 0; $this->size > $i; $i++) {
                                if (($key >= $this->startDates[$i]) && ($this->endDates[$i] >= $key)) {
                                    # '0' array key, only 1 customer group "New Sign Up"
                                    $new_sign_up[$country_id]['count'] += $value['count'];
                                    $new_sign_up[$country_id]['total_customer'] += $value['total_customer'];
                                    $new_sign_up[$country_id]['sum'] += $value['subtotal'];
                                    if (isset($tmp_new_sign_up[$country_id][$key]['new_sign_up']))
                                        $new_sign_up[$country_id]['new_sign_up'][$this->startDates[$i]] = $tmp_new_sign_up[$country_id][$key]['new_sign_up'];
                                    $new_sign_up[$country_id]['with_purchase'][$this->startDates[$i]] += $value['total_customer'];
                                    $new_sign_up[$country_id]['subtotal'][$this->startDates[$i]] += $value['subtotal'];
                                    $new_sign_up[$country_id]['count_by_date'][$this->startDates[$i]] += $value['count'];
                                }
                            }
                        }
                    }
                    unset($tmp_new_sign_up);
                }

                ksort($new_sign_up);
                $this->info = array_merge($this->info, $new_sign_up);
                break;
        }
    }

    function sort_country_result(&$result_array) {
        $tmp_sorted_array = array();
        $sort_flag = SORT_NUMERIC;
        $uncategories_arr = array();

        if (is_array($result_array) && count($result_array)) {
            if (tep_not_null($this->sort_by)) {
                foreach ($result_array as $key => $res_info) {
                    if (!isset($result_array[$key]['count'])) {
                        $result_array[$key]['count'] = count($res_info['orders_id']);
                        $result_array[$key]['total_customer'] = count($res_info['customers_id']);
                        $result_array[$key]['avg'] = $result_array[$key]['count'] > 0 ? $res_info['sum'] / $result_array[$key]['count'] : 0;
                    }

                    if ($key == 'Uncategories') {
                        $uncategories_arr = array($result_array[$key]);
                    } else {
                        if (isset($tmp_sorted_array[$result_array[$key][$this->sort_by]])) {
                            $tmp_sorted_array[(string) $result_array[$key][$this->sort_by]][] = $result_array[$key];
                        } else {
                            $tmp_sorted_array[(string) $result_array[$key][$this->sort_by]] = array($result_array[$key]);
                        }
                    }
                }

                if ($this->sort_by == 'country_name')
                    $sort_flag = SORT_STRING;
            } else {
                $tmp_sorted_array = $result_array;
            }
        }

        if ($this->sort_order == 'DESC') { // Descending
            krsort($tmp_sorted_array, $sort_flag);
        } else { // Ascending
            ksort($tmp_sorted_array, $sort_flag);
        }

        /* -- move "Uncategories" to end of array -- */
        if (tep_not_null($uncategories_arr)) {
            $tmp_sorted_array['Uncategories'] = $uncategories_arr;
        }

        return $tmp_sorted_array;
    }

    function getCatTree($cat_id, $languages_id = '', $level = '') {
        $p_rank = tep_check_cat_tree_permissions(FILENAME_SALES_REPORT, $cat_id);

        if ($p_rank > 0) {
            if ($cat_id > 0) {
                $cat_select_sql = "	SELECT c.custom_products_type_id, c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.categories_id = '" . $cat_id . "' AND cd.language_id = '" . $languages_id . "'";
                $cat_result_sql = tep_db_query($cat_select_sql, 'read_db_link');
                $cat_row = tep_db_fetch_array($cat_result_sql);
            } else {
                $cat_row = array('parent_id' => -1, 'categories_name' => TEXT_TOP);
            }

            if (!tep_not_null($_SESSION['sales_param']["pro_type"]) || $this->cpt_filter($_SESSION['sales_param']["pro_type"], $cat_row['custom_products_type_id'], $level)) {
                $array_index = count($this->cat_array);
                $name = "<div valign='top'>" . str_repeat("&nbsp;", $level * 3) . "<span id='cell_" . $cat_id . "' stlye='display: block; width: 100%'>##</span>" . "&nbsp;" . strip_tags($cat_row['categories_name']) . "</div>";

                $this->cat_array[$array_index] = array('id' => $cat_id,
                    'name' => $name,
                    'subtotal' => 0,
                    'products' => array("purchased" => array()),
                    'level' => $level
                );

                $this->parantChild[$cat_row["parent_id"]][] = $cat_id;
            }

            $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name 
										FROM " . TABLE_CATEGORIES . " AS c 
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
											ON c.categories_id = cd.categories_id 
										WHERE c.parent_id = '" . $cat_id . "' AND cd.language_id = '" . $languages_id . "' 
										ORDER BY c.sort_order, cd.categories_name";
            $sub_category_result_sql = tep_db_query($sub_category_select_sql, 'read_db_link');

            $total_cat = 0;
            while ($sub_category_row = tep_db_fetch_array($sub_category_result_sql)) {
                $total_cat += $this->getCatTree($sub_category_row['categories_id'], $languages_id, $level + 1);
            }

            $total_prod = 0;

            if (count($this->order_amount_status_array)) {
                $prod_total_price_select_sql = "SELECT o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str . " 
												FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
						  	                    INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
						  	                    	ON op.products_id = pc.products_id
						  	                    INNER JOIN " . TABLE_ORDERS . " AS o 
						  	                    	ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
						  	                    WHERE pc.categories_id = '" . $cat_id . "' 
						  	                    	AND pc.products_is_link=0 
						  	                    	AND op.parent_orders_products_id=0 
						  	                    	AND op.orders_products_is_compensate=0 
						  	                    	AND $this->selStartDateStr 
						  	                    	AND $this->selEndDateStr 
						  	                    	" . ($this->sql_filter ? " AND " . $this->sql_filter : "");
                $prod_total_price_select_sql .= " GROUP BY o.orders_id";
                $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');

                while ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                    $sales_amount = $this->get_sales_amount($prod_total_price_row);
                    if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                        $total_prod += $sales_amount;
                    }
                }
            } else {
                $prod_total_price_select_sql = "SELECT SUM(op.final_price*op.products_quantity) AS toal_prod_sales 
		       									FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
						  	                    INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
						  	                    	ON op.products_id = pc.products_id
						  	                    INNER JOIN " . TABLE_ORDERS . " AS o 
						  	                    	ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
						  	                    WHERE pc.categories_id = '" . $cat_id . "' 
						  	                    	AND pc.products_is_link=0 
						  	                    	AND op.parent_orders_products_id=0 
						  	                    	AND op.orders_products_is_compensate=0 
						  	                    	AND $this->selStartDateStr 
						  	                    	AND $this->selEndDateStr " .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "");
                $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
                if ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                    $total_prod = $prod_total_price_row["toal_prod_sales"];
                }
            }

            $total = $total_cat + $total_prod;
            $this->cat_array[$array_index]["subtotal"] = $total;

            return $total;
        }
    }

    function displayCatTree($level, $currencies) {
        for ($i = 0; $i < count($this->info); $i++) { // each row in the report
            $expand_collapse_txt = (is_array($this->parantChild[$this->info[$i]["id"]]) ? ( ($this->info[$i]["level"] >= $level - 1) ? "<a href=\"javascript:;\" onClick=\"expandCollapse('" . $this->info[$i]["id"] . "', 'show')\">" . tep_image(DIR_WS_ICONS . "expand.gif", '', 10, 10, 'border=0') . "</a>" : "<a href=\"javascript:;\" onClick=\"expandCollapse('" . $this->info[$i]["id"] . "', 'hide')\">" . tep_image(DIR_WS_ICONS . "collapse.gif", '', 10, 10, 'border=0') . "</a>") : tep_image(DIR_WS_IMAGES . "pixel_trans.gif", '', 10, 10, 'border=0'));

            $display_name = preg_replace("/([^#]*?)(##)([^#]+)(<\/div>.*)/is", "\$1" . $expand_collapse_txt . "\$3\$4", $this->info[$i]["name"]);

            echo '	<tbody id="row_' . $this->info[$i]["id"] . '" class="' . ($this->info[$i]["level"] < $level ? "show" : "hide") . '">
					<tr class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')"> 
						<td class="reportRecords">&nbsp;</td>
						<td class="reportRecords" valign="center">' . $display_name . '</td>
						<td class="reportRecords" align="right">' . $currencies->format($this->info[$i]["subtotal"]) . '</td>
					</tr>
					</tbody>';

            if ($this->info[$i]["level"] == "0") {
                // only need to added up first level sales amount
                $this->totalSales += $this->info[$i]["subtotal"];
            }
        }

        return $this->csv_data;
    }

    function get_sales_amount($query_result) {
        $value = 0;
        if ($query_result['orders_status'] == 2 || $query_result['orders_status'] == 3) {
            if (count($this->order_amount_status_array[$query_result['orders_status']]) == count($this->orders_amt_breakdown_array[$query_result['orders_status']])) { // All sub status checkboxes are checked
                $value = $query_result['total_purchase_amount'];
            } else {
                for ($sub_cnt = 0; $sub_cnt < count($this->order_amount_status_array[$query_result['orders_status']]); $sub_cnt++) {
                    switch ($this->order_amount_status_array[$query_result['orders_status']][$sub_cnt]) {
                        case 'UD': // Completed order does not has Undelivered part
                            if ($query_result['orders_status'] == 2)
                                $value += ( $query_result['total_purchase_amount'] - ($query_result['total_delivered_amount'] + $query_result['total_canceled_amount'] + $query_result['total_reversed_amount']) );
                            break;
                        case 'D':
                            $value += $query_result['total_delivered_amount'];
                            break;
                        case 'C':
                            $value += $query_result['total_canceled_amount'];
                            break;
                        case 'RV-WIN':
                            if ($query_result['orders_cb_status'] == '1')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        case 'RV-LOST':
                            if ($query_result['orders_cb_status'] == '2')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        case 'RV-RESOLVED':
                            if ($query_result['orders_cb_status'] == '3')
                                $value += $query_result['total_reversed_amount'];
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            $value = $query_result['total_purchase_amount'];
        }

        return $value;
    }

    function get_sales_qty($query_result) {
        $value = 0;
        if ($query_result['orders_status'] == 2 || $query_result['orders_status'] == 3) {
            if (count($this->order_amount_status_array[$query_result['orders_status']]) == count($this->orders_amt_breakdown_array[$query_result['orders_status']])) { // All sub status checkboxes are checked
                $value = $query_result['total_purchase_qty'];
            } else {
                if ($query_result['products_bundle'] != 'yes') {
                    for ($sub_cnt = 0; $sub_cnt < count($this->order_amount_status_array[$query_result['orders_status']]); $sub_cnt++) {
                        switch ($this->order_amount_status_array[$query_result['orders_status']][$sub_cnt]) {
                            case 'UD': // Completed order does not has Undelivered part
                                if ($query_result['orders_status'] == 2)
                                    $value += ( $query_result['total_purchase_qty'] - ($query_result['total_delivered_qty'] + $query_result['total_canceled_qty'] + $query_result['total_reversed_qty']) );
                                break;
                            case 'D':
                                $value += $query_result['total_delivered_qty'];
                                break;
                            case 'C':
                                $value += $query_result['total_canceled_qty'];
                                break;
                            case 'RV-WIN':
                                if ($query_result['orders_cb_status'] == '1')
                                    $value += $query_result['total_reversed_qty'];
                                break;
                            case 'RV-LOST':
                                if ($query_result['orders_cb_status'] == '2')
                                    $value += $query_result['total_reversed_qty'];
                                break;
                            case 'RV-RESOLVED':
                                if ($query_result['orders_cb_status'] == '3')
                                    $value += $query_result['total_reversed_qty'];
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        } else {
            $value = $query_result['total_purchase_qty'];
        }
        return $value;
    }

    function get_sales_stat_select_sql() {
        $case_statement_array = array();
        $country_sales_select_str = '';

        if (count($this->order_amount_status_array)) {
            foreach ($this->order_amount_status_array as $status_id => $sub_status) {
                if (count($sub_status) == count($this->orders_amt_breakdown_array[$status_id])) { // All sub status checkboxes are checked
                    continue;
                } else {
                    $sum_select_sql_array = array();
                    for ($sub_cnt = 0; $sub_cnt < count($sub_status); $sub_cnt++) {
                        switch ($sub_status[$sub_cnt]) {
                            case 'UD': // Completed order does not has Undelivered part
                                if ($status_id == 2)
                                    $sum_select_sql_array[] = "( (op.final_price*op.products_quantity)-products_good_delivered_price-products_canceled_price-products_reversed_price )";
                                break;
                            case 'D':
                                $sum_select_sql_array[] = "(products_good_delivered_price)";
                                break;
                            case 'C':
                                $sum_select_sql_array[] = "(products_canceled_price)";
                                break;
                            case 'RV-WIN':
                                $sum_select_sql_array[] = "(IF(o.orders_cb_status=1, products_reversed_price, 0))";
                                break;
                            case 'RV-LOST':
                                $sum_select_sql_array[] = "(IF(o.orders_cb_status=2, products_reversed_price, 0))";
                                break;
                            case 'RV-RESOLVED':
                                $sum_select_sql_array[] = "(IF(o.orders_cb_status=3, products_reversed_price, 0))";
                                break;
                            default:
                                break;
                        }
                    }

                    if (count($sum_select_sql_array)) {
                        $case_statement_array['sum'][] = " WHEN " . $status_id . " THEN " . implode('+', $sum_select_sql_array) . " ";
                    }
                }
            }

            if (count($case_statement_array)) {
                $case_statement_array['sum'][] = " ELSE op.final_price*op.products_quantity";

                $country_sales_select_str = " 	CASE o.orders_status " . implode("\n", $case_statement_array['sum']) . " END AS value";

                # sum sales during mysql data retrieve {start}
                if ($this->mode == 10) {
                    $country_sales_select_str = " 	SUM(CASE o.orders_status " . implode("\n", $case_statement_array['sum']) . " END) AS value";
                }
                # sum sales during mysql data retrieve {end}
            }
        }

        return $country_sales_select_str;
    }

    function getPaymentMethods() {
        $payment_methods_obj = new payment_methods('payment_gateways');
        $payment_method = array();

        foreach ($payment_methods_obj->payment_gateways_array as $payment_gateways_id => $payment_gateways_data) {
            $payment_method[$payment_gateways_id] = $payment_gateways_data->title;
        }
        $payment_method[SYSTEM_PAYMENT_STORE_CREDITS] = 'Store Credits';

        return $payment_method;
    }

    function getSummaryText() {
        switch ($this->mode) {
            case "1":
                $this->summary["mode_summary"] = AVERAGE_HOURLY_TOTAL;
                $this->summary["parent_mode_summary"] = TODAY_TO_DATE;
                $this->summary["report_title"] = REPORT_TYPE_HOURLY . ' ' . HEADING_TITLE;
                $this->summary["date_heading"] = date('l jS \of F Y', $this->startDate);
                $this->summary["first_column_title"] = TABLE_HEADING_HOUR;
                break;

            case "2":
                $this->summary["mode_summary"] = AVERAGE_DAILY_TOTAL;
                $this->summary["parent_mode_summary"] = WEEK_TO_DATE;
                $this->summary["report_title"] = REPORT_TYPE_DAILY . ' ' . HEADING_TITLE;
                $this->summary["date_heading"] = "";
                $this->summary["first_column_title"] = TABLE_HEADING_DATE;
                break;

            case "3":
                $this->summary["mode_summary"] = AVERAGE_WEEKLY_TOTAL;
                $this->summary["parent_mode_summary"] = MONTH_TO_DATE;
                $this->summary["report_title"] = REPORT_TYPE_WEEKLY . ' ' . HEADING_TITLE;
                $this->summary["date_heading"] = "";
                $this->summary["first_column_title"] = TABLE_HEADING_WEEKLY;
                break;

            case "4":
                $this->summary["mode_summary"] = AVERAGE_MONTHLY_TOTAL;
                $this->summary["parent_mode_summary"] = YEAR_TO_DATE;
                $this->summary["report_title"] = REPORT_TYPE_MONTHLY . ' ' . HEADING_TITLE;
                $this->summary["date_heading"] = "";
                $this->summary["first_column_title"] = TABLE_HEADING_MONTH;
                break;

            case "5":
                $this->summary["mode_summary"] = AVERAGE_YEARLY_TOTAL;
                $this->summary["parent_mode_summary"] = YEARLY_TOTAL;
                $this->summary["report_title"] = REPORT_TYPE_YEARLY . ' ' . HEADING_TITLE;
                $this->summary["date_heading"] = "";
                $this->summary["first_column_title"] = TABLE_HEADING_YEAR;
                break;

            case "6":
                $this->summary["mode_summary"] = "";
                $this->summary["parent_mode_summary"] = TOTAL_SALES;
                $this->summary["report_title"] = REPORT_TYPE_CATEGORY . ' ' . HEADING_TITLE;

                if ($this->startDate && $this->endDate) {
                    $this->summary["date_heading"] = "From " . date("Y-m-d", $this->startDate) . " to " . date("Y-m-d", $this->endDate);
                } else if ($this->startDate) {
                    $this->summary["date_heading"] = "After " . date("Y-m-d", $this->startDate);
                } else if ($this->endDate) {
                    $this->summary["date_heading"] = "Before " . date("Y-m-d", $this->endDate);
                }
                $this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
                break;

            case "7":
                $this->summary["mode_summary"] = $this->sales_period == 'day' ? AVERAGE_DAILY_TOTAL : AVERAGE_MONTHLY_TOTAL;
                $this->summary["parent_mode_summary"] = TOTAL_SALES;
                $this->summary["report_title"] = REPORT_TYPE_CATEGORY_WITH_STOCK . ' ' . HEADING_TITLE;
                $this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
                $this->summary["type"] = TABLE_HEADING_TYPE;
                $this->summary["second_column_title"] = TABLE_HEADING_AVAILABLE_QTY;
                $this->summary["third_column_title"] = TABLE_HEADING_ACTUAL_QTY;
                $this->summary["sales_category_title"] = $this->sales_period == 'day' ? TABLE_HEADING_DAILY_SALES : TABLE_HEADING_MONTHLY_SALES;
                break;

            case "8": # country
            case "9": # customer group
            case "10": # new sign up
                $this->summary["mode_summary"] = $this->sales_period == 'day' ? AVERAGE_DAILY_TOTAL : AVERAGE_MONTHLY_TOTAL;
                $this->summary["parent_mode_summary"] = TOTAL_SALES;
                $this->summary["second_column_title"] = TABLE_HEADING_CUSTOMERS_COUNT;
                $this->summary["third_column_title"] = TABLE_HEADING_ORDERS;
                $this->summary["forth_column_title"] = TABLE_HEADING_SALES_PER_ORDER;
                $this->summary["sales_category_title"] = $this->sales_period == 'day' ? TABLE_HEADING_DAILY_SALES : TABLE_HEADING_MONTHLY_SALES;

                if ($this->mode == '8')
                    $this->summary["date_heading"] = TEXT_COUNTRY_DEFINITION;

                if ($this->sales_period == 'day') {
                    $startDate = $this->startDate;
                    $endDate = $this->endDate;
                } else {
                    $startDate = $this->startDates[0];
                    $endDate = $this->endDates[count($this->endDates) - 1];

                    if (date('Y-m-d', $endDate) > date('Y-m-d'))
                        $endDate = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));
                }

                if (!empty($startDate) && !empty($endDate)) {
                    $this->summary["date_heading"] .= "From " . date("Y-m-d", $startDate) . " to " . date("Y-m-d", $endDate);
                } else if (!empty($startDate)) {
                    $this->summary["date_heading"] .= "After " . date("Y-m-d", $startDate);
                } else if (!empty($endDate)) {
                    $this->summary["date_heading"] .= "Before " . date("Y-m-d", $endDate);
                }

                $this->summary["date_heading"] .= '<br />Exclude SC Top Up Sales';

                if ($this->mode == '8') {
                    $this->summary["first_column_title"] = TABLE_HEADING_COUNTRY;
                    $this->summary["report_title"] = REPORT_TYPE_COUNTRY . ' ' . HEADING_TITLE;
                } else if ($this->mode == '9') {
                    $this->summary["first_column_title"] = TABLE_HEADING_CUSTOMER;
                    $this->summary["report_title"] = REPORT_TYPE_CUSTOMER . ' ' . HEADING_TITLE;
                } else if ($this->mode == '10') {
                    $this->summary["first_column_title"] = TABLE_HEADING_NEW_SIGN_UP;
                    $this->summary["second_column_title"] = TABLE_HEADING_NEW_SIGN_UP_TOTAL;
                    $this->summary["after_second_column_title"] = TABLE_HEADING_NEW_SIGN_UP_PURCHASE;
                    $this->summary["report_title"] = REPORT_TYPE_NEW_SIGN_UP . ' ' . HEADING_TITLE;
                }
                break;
        }
    }

    function cpt_filter($cpt_id, $current_cpt_id, $level) {
        if (is_numeric($current_cpt_id)) {
            switch ($cpt_id) {
                case '0':
                    $verified = $current_cpt_id == $cpt_id || $current_cpt_id == '999' ? true : false;
                    if ($verified) {
                        return true;
                    } else {
                        return false;
                    }
                    break;

                default:
                    $verified = $current_cpt_id == $cpt_id || $current_cpt_id == '999' ? true : false;
                    if ($verified) {
                        return true;
                    } else {
                        return false;
                    }
                    break;
            }
        } else {
            return true;
        }
    }

    function dig_out_bundle_prod($bundle_product_id) {
        global $languages_id;

        $sub_product_info = array();

        $products_select_sql = "SELECT p.custom_products_type_id, p.products_id, pd.products_name, p.products_quantity, p.products_actual_quantity, pb.subproduct_qty, pb.subproduct_id 
                              FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
                              INNER JOIN " . TABLE_PRODUCTS . " AS p 
                              ON pb.subproduct_id=p.products_id 
                              INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
                              ON pb.subproduct_id=pd.products_id 
                              WHERE pb.bundle_id='" . $bundle_product_id . "' 
                              AND p.products_status = '1'
                              AND pd.language_id='" . (int) $languages_id . "'";

        $products_result_sql = tep_db_query($products_select_sql, 'read_db_link');
        while ($products_row = tep_db_fetch_array($products_result_sql)) {
            $sub_product_info[$products_row['products_id']] = array('name' => strip_tags($products_row["products_name"]),
                'available_qty' => number_format($products_row["products_quantity"], 0, '.', ','),
                'actual_qty' => number_format($products_row["products_actual_quantity"], 0, '.', ','),
            );
        }

        return $sub_product_info;
    }

    function bundle_sales_amount($orders_product_id, $i) {
        $total_prod = 0;

        $date_range_where_sql = " o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND " .
                " o.date_purchased < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";

        $prod_total_price_select_sql = "SELECT op.orders_products_id, o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str . " 
                                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                        INNER JOIN " . TABLE_ORDERS . " AS o 
                                        ON op.orders_id = o.orders_id " . $this->extra_joining_str . "
                                        WHERE " . $date_range_where_sql . " 
                                        AND op.parent_orders_products_id=0 
                                        AND op.orders_products_is_compensate=0 
                                        AND op.orders_products_id = '" . $orders_product_id . "'
                                       " . ($this->sql_filter ? " AND " . $this->sql_filter : "");
        $prod_total_price_select_sql .= " GROUP BY o.orders_id";

        $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
        while ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
            $sales_amount = $this->get_sales_amount($prod_total_price_row);
            if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                $total_prod += $sales_amount;
            }
        }
        return $total_prod;
    }

    function tep_get_custom_product_type($products_id) {
        $custom_type = '';

        $product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id 
									FROM " . TABLE_PRODUCTS . " 
									WHERE products_id='" . tep_db_input($products_id) . "'";
        $product_info_result_sql = tep_db_query($product_info_select_sql, 'read_db_link');

        if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            if ($product_info_row['products_bundle'] != 'yes' && $product_info_row['products_bundle_dynamic'] != 'yes') {
                $custom_type = $product_info_row['custom_products_type_id'];
            } else {
                // Assume package ONLY consists of ONE product type
                $package_type_select_sql = "SELECT p.custom_products_type_id 
							    			FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												ON p.products_id=pb.subproduct_id 
							    			WHERE pb.bundle_id = '" . tep_db_input($products_id) . "'
											LIMIT 1";
                $package_type_result_sql = tep_db_query($package_type_select_sql, 'read_db_link');
                if ($package_type_row = tep_db_fetch_array($package_type_result_sql)) {
                    $custom_type = $package_type_row['custom_products_type_id'];
                }
            }
        }
        return $custom_type;
    }

    function generate_csv_file($report_csv_data, $report_type = '') {
        $cpt_array = array();

        /* -- custom_products_type -- */
        $cpt_sel_sql = "SELECT custom_products_type_id, custom_products_type_name 
                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                        ORDER BY custom_products_type_id ASC";
        $cpt_res_sql = tep_db_query($cpt_sel_sql, 'read_db_link');
        while ($cpt_row = tep_db_fetch_array($cpt_res_sql)) {
            $cpt_array[$cpt_row['custom_products_type_id']] = $cpt_row['custom_products_type_name'];
        }

        /* -- Gift Card product, based on products_flag -- */
        $cpt_array['gift_card'] = EXPORT_TEXT_GIFT_CARD;
        
        switch ($report_type) {
            case 2: # Daily Report
                if (tep_not_null($this->export_format) && ($this->export_format == 'export_by_order')) {
                    $cpt_array['multi_game_card'] = EXPORT_TEXT_MULTI_GAME_CARD;
                    $currencies = new currencies();

                    $cvs_header = array('"' . EXPORT_TEXT_ORDER_ID . '"', '"' . EXPORT_TEXT_ORDER_DATE . '"',
                        '"' . EXPORT_TEXT_PG . '"', '"' . EXPORT_TEXT_SITE . '"', '"' . EXPORT_TEXT_ENTITY . '"', '"' . EXPORT_TEXT_PG_TXN_ID . '"',
                        '"' . EXPORT_TEXT_INV_NO . '"', '"' . EXPORT_TEXT_INV_TYPE . '"', 
                        '"' . EXPORT_TEXT_CURRENCY . '"', '"' . EXPORT_TEXT_SUBTOTAL_ORIGINAL_CURRENCY . '"', 
                        '"' . EXPORT_TEXT_GST . '"', '"' . EXPORT_TEXT_SURCHARGE . '"', 
                        '"' . EXPORT_TEXT_DC . '"', '"' . EXPORT_TEXT_OGC . '"', 
                        '"' . EXPORT_TEXT_SC . '"', '"' . EXPORT_TEXT_PG_AMOUNT . '"', 
                        '"' . EXPORT_TEXT_ORDER_RATE . '"', '"' . EXPORT_TEXT_SUBTOTAL_USD . '"', 
                        '"' . EXPORT_TEXT_GST . '"', '"' . EXPORT_TEXT_SURCHARGE . '"', 
                        '"' . EXPORT_TEXT_DC . '"', '"' . EXPORT_TEXT_OGC . '"', 
                        '"' . EXPORT_TEXT_SC . '"', '"' . EXPORT_TEXT_PG_AMOUNT . '"', 
                        '"' . EXPORT_TEXT_GST_TAX_COUNTRY . '"', '"' . EXPORT_TEXT_GST_PERCENTAGE . '"');

                    echo '"From ' . date('d-m-Y', $this->startDates[0]) . ' to ' . date('d-m-Y', $this->endDates[($this->size - 1)]) . '"' . str_repeat(',', count($cvs_header)) . ',"' . EXPORT_TEXT_PRODUCT_CATEGORIES_USD . '"' .
                            str_repeat(',', count($cpt_array)) . ',"Store Credit Actual Delivery"' . "\n";
                    echo implode(',', $cvs_header) . ',';

                    /* -- custom_products_type -- */
                    if (tep_not_null($cpt_array)) {
                        foreach ($cpt_array as $cpt_id => $cpt_name) {
                            echo ',"' . $cpt_name . '"';
                        }
                    }
                    echo "\n";

                    // make a connection to the database... now
                    $og_db = tep_db_connect(DB_OG_RR_SERVER, DB_OG_RR_SERVER_USERNAME, DB_OG_RR_SERVER_PASSWORD, DB_OG_RR_DATABASE, 'og_db') or die('Unable to connect to alt database server!');

                    $payment_gateway_select_sql = "(SELECT payment_methods_id, payment_methods_title, payment_methods_filename, payment_methods_sort_order 
                                                    FROM " . TABLE_PAYMENT_METHODS . " 
                                                    WHERE payment_methods_receive_status = '1' 
                                                            AND payment_methods_parent_id = 0
                                                            AND payment_methods_id IN (" . implode(',', $this->export_payment_parent_method) . ") ) 
                                                    UNION 
                                                    (SELECT 0 AS payment_methods_id, '" . EXPORT_TEXT_FULL_STORE_CREDIT . "' AS payment_methods_title, 
                                                            '" . SYSTEM_PAYMENT_STORE_CREDITS . "' AS payment_methods_filename, 0 AS payment_methods_sort_order) 
                                                    ORDER BY payment_methods_sort_order";
                    $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql, 'read_db_link');
                    while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
                        $payment_methods_sql = '';

                        $pg_id = $payment_gateway_row['payment_methods_id'];
                        $payment_methods_sql = ' o.payment_methods_parent_id = "' . $pg_id . '" AND ';

                        if (isset($this->export_payment_method[$pg_id]) && tep_not_null($this->export_payment_method[$pg_id])) {
                            $payment_methods_sql .= ' o.payment_methods_id IN ( ' . implode(',', $this->export_payment_method[$pg_id]) . ' ) AND ';
                        }

                        $total_arr = array('subtotal' => 0, 'coupon' => 0, 'sc' => 0, 'surcharge' => 0, 'total' => 0, 'usd_subtotal' => 0, 'usd_coupon' => 0, 'usd_sc' => 0, 'usd_surcharge' => 0, 'usd_total' => 0);
                        $data_arr = array();

                        # DATE TYPE filtering
                        $date_type_extra_join_str = '';
                        if (tep_not_null($this->date_type) && ($this->date_type > 1)) {
                            $date_type_extra_join_str = "	LEFT JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss 
                                                            ON oss.orders_id = o.orders_id ";
                            $date_range_where_sql = " oss.first_date >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "' AND oss.first_date < '" . date("Y-m-d H:i:s", $this->endDates[($this->size - 1)]) . "' AND oss.orders_status_id = '" . $this->date_type . "'";
                        } else {
                            $date_range_where_sql = "	o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[0]) . "' 
                                                        AND o.date_purchased < '" . date("Y-m-d H:i:s", $this->endDates[($this->size - 1)]) . "' ";
                        }
                        $this->extra_joining_str = $date_type_extra_join_str;

                        $orders_search_sql = "	SELECT o.orders_id, o.date_purchased, o.currency, o.currency_value, o.payment_methods_id, op.products_model, o.payment_methods_parent_id,
                                                    (IF( o.payment_method <> '', o.payment_method , '" . EXPORT_TEXT_FULL_STORE_CREDIT . "' )) AS payment_method, 
                                                    o.orders_status, o.orders_cb_status " . (tep_not_null($this->order_amount_status_str) ? ', ' . $this->order_amount_status_str : '') . ", 
                                                    pp.settlement_account 
                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                LEFT JOIN " . TABLE_ORDERS . " AS o 
                                                        ON op.orders_id = o.orders_id " .
                                                $date_type_extra_join_str . " 
                                                LEFT JOIN " . TABLE_PIPWAVE_PAYMENT . " AS pp 
                                                    ON pp.orders_id = o.orders_id 
                                                WHERE " . $payment_methods_sql . " " .
                                (tep_not_null($this->sql_filter) ? ' ( ' . $this->sql_filter . ' ) AND ' : '') .
                                $date_range_where_sql . " 
                                                            AND op.parent_orders_products_id=0
                                                            AND op.orders_products_is_compensate=0
                                                        GROUP BY o.orders_id";
                        $orders_result_sql = tep_db_query($orders_search_sql, 'read_db_link');
                        while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
                            $orders_id = $orders_row['orders_id'];

                            $sales_amount = $this->get_sales_amount($orders_row);
                            if ($sales_amount >= 0.0001) { // Only count as an order if there is amount for the sub status
                                $data_arr[$orders_id] = array_merge($orders_row, $total_arr);

                                $data_arr[$orders_id]['usd_sc'] = 0;
                                $data_arr[$orders_id]['sc'] = 0;
                                $data_arr[$orders_id]['usd_coupon'] = 0;
                                $data_arr[$orders_id]['coupon'] = 0;
                                $data_arr[$orders_id]['usd_surcharge'] = 0;
                                $data_arr[$orders_id]['surcharge'] = 0;
                                $data_arr[$orders_id]['usd_gst'] = 0;
                                $data_arr[$orders_id]['gst'] = 0;
                                $data_arr[$orders_id]['usd_ogc'] = 0;
                                $data_arr[$orders_id]['ogc'] = 0;
                                $data_arr[$orders_id]['usd_subtotal'] = 0;
                                $data_arr[$orders_id]['subtotal'] = 0;
                                $data_arr[$orders_id]['usd_total'] = 0;
                                $data_arr[$orders_id]['total'] = 0;
                                $data_arr[$orders_id]['ip_country'] = '';
                                $data_arr[$orders_id]['gst_percentage'] = '';

                                # Orders Total: ot_subtotal, ot_coupon, ot_gv, ot_surcharge 
                                $sales_ot_select_sql = "SELECT ot_subtotal.value AS ot_subtotal_value, ot_gv.value AS ot_gv_value, 
                                                            ot_coupon.value AS ot_coupon_value, ot_surcharge.value AS ot_surcharge_value,
                                                            ot_gst.value AS ot_gst_value, ot_ogc.value AS ot_ogc_value,
                                                            oei_ip_country.orders_extra_info_value AS ip_country,
                                                            IF(oei_ip_country.orders_extra_info_value <> '', (SELECT c.countries_iso_code_2 FROM " . TABLE_COUNTRIES . " AS c WHERE c.countries_id = oei_ip_country.orders_extra_info_value), '') AS ip_country,
                                                            oei_gst_tax_percentage.orders_extra_info_value AS gst_tax_percentage, oei_site_id.orders_extra_info_value AS site_id,
                                                            oei_use_g2g_pg.orders_extra_info_value AS use_g2g_pg, c2c_invoice.invoice_number AS invoice_number
                                                        FROM " . TABLE_ORDERS_TOTAL . " AS ot_subtotal
                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv
                                                            ON ot_gv.orders_id = ot_subtotal.orders_id
                                                                AND ot_gv.class='ot_gv'
                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_coupon
                                                            ON ot_coupon.orders_id = ot_subtotal.orders_id
                                                                AND ot_coupon.class='ot_coupon'
                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_surcharge
                                                            ON ot_surcharge.orders_id = ot_subtotal.orders_id
                                                                AND ot_surcharge.class='ot_surcharge'
                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gst
                                                            ON ot_gst.orders_id = ot_subtotal.orders_id
                                                                AND ot_gst.class='ot_gst'
                                                        LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_ogc
                                                            ON ot_ogc.orders_id = ot_subtotal.orders_id
                                                                AND ot_ogc.class='ot_ogc'
                                                        LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei_ip_country
                                                            ON oei_ip_country.orders_id = ot_subtotal.orders_id
                                                                AND oei_ip_country.orders_extra_info_key='ip_country'
                                                        LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei_gst_tax_percentage
                                                            ON oei_gst_tax_percentage.orders_id = ot_subtotal.orders_id
                                                                AND oei_gst_tax_percentage.orders_extra_info_key='gst_tax_percentage'
                                                        LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei_site_id
                                                            ON oei_site_id.orders_id = ot_subtotal.orders_id 
                                                                AND oei_site_id.orders_extra_info_key='site_id'
                                                        LEFT JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei_use_g2g_pg
                                                            ON oei_use_g2g_pg.orders_id = ot_subtotal.orders_id 
                                                                AND oei_use_g2g_pg.orders_extra_info_key='use_g2g_pg'
                                                        LEFT JOIN " . TABLE_C2C_INVOICE . " AS c2c_invoice
                                                            ON c2c_invoice.orders_id = ot_subtotal.orders_id
                                                        WHERE ot_subtotal.orders_id='" . $orders_id . "'
                                                            AND ot_subtotal.class='ot_subtotal'";
                                $sales_ot_result = tep_db_query($sales_ot_select_sql, 'read_db_link');
                                if ($sales_ot_row = tep_db_fetch_array($sales_ot_result)) {
                                    $ot_subtotal_value = tep_not_null($sales_ot_row['ot_subtotal_value']) ? $sales_ot_row['ot_subtotal_value'] : 0;
                                    $ot_coupon_value = tep_not_null($sales_ot_row['ot_coupon_value']) ? $sales_ot_row['ot_coupon_value'] : 0;
                                    $ot_gv_value = tep_not_null($sales_ot_row['ot_gv_value']) ? $sales_ot_row['ot_gv_value'] : 0;
                                    $ot_surcharge_value = tep_not_null($sales_ot_row['ot_surcharge_value']) ? $sales_ot_row['ot_surcharge_value'] : 0;
                                    $ot_gst_value = tep_not_null($sales_ot_row['ot_gst_value']) ? $sales_ot_row['ot_gst_value'] : 0;
                                    $ot_ogc_value = tep_not_null($sales_ot_row['ot_ogc_value']) ? $sales_ot_row['ot_ogc_value'] : 0;

                                    if ($ot_gv_value > 0) {
                                        $data_arr[$orders_id]['usd_sc'] = $ot_gv_value;
                                        $data_arr[$orders_id]['sc'] = number_format(round($ot_gv_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    if ($ot_coupon_value > 0) {
                                        $data_arr[$orders_id]['usd_coupon'] = $ot_coupon_value;
                                        $data_arr[$orders_id]['coupon'] = number_format(round($ot_coupon_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    if ($ot_surcharge_value > 0) {
                                        $data_arr[$orders_id]['usd_surcharge'] = $ot_surcharge_value;
                                        $data_arr[$orders_id]['surcharge'] = number_format(round($ot_surcharge_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    if ($ot_gst_value > 0) {
                                        $data_arr[$orders_id]['usd_gst'] = $ot_gst_value;
                                        $data_arr[$orders_id]['gst'] = number_format(round($ot_gst_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    if ($ot_ogc_value > 0) {
                                        $data_arr[$orders_id]['usd_ogc'] = $ot_ogc_value;
                                        $data_arr[$orders_id]['ogc'] = number_format(round($ot_ogc_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    if ($ot_subtotal_value > 0) {
                                        $data_arr[$orders_id]['usd_subtotal'] = $ot_subtotal_value;
                                        $data_arr[$orders_id]['subtotal'] = number_format(round($ot_subtotal_value * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');

                                        $ot_total = $ot_subtotal_value - ($ot_gv_value + $ot_coupon_value + $ot_ogc_value) + $ot_surcharge_value + $ot_gst_value;
                                        $data_arr[$orders_id]['usd_total'] = round($ot_total, 8);
                                        $data_arr[$orders_id]['total'] = number_format(round($ot_total * $orders_row['currency_value'], 2), $currencies->currencies[$orders_row['currency']]['decimal_places'], $currencies->currencies[$orders_row['currency']]['decimal_point'], '');
                                    }

                                    $data_arr[$orders_id]['gst_percentage'] = $sales_ot_row['gst_tax_percentage'];
                                    $data_arr[$orders_id]['ip_country'] = $sales_ot_row['ip_country'];
                                    $data_arr[$orders_id]['site_id'] = ($sales_ot_row['site_id'] == '0') ? 'OG' : 'G2G';
                                    $data_arr[$orders_id]['use_g2g_pg'] = ($sales_ot_row['use_g2g_pg'] == '1') ? 'G2G' : '';
                                    $data_arr[$orders_id]['invoice_number'] = $sales_ot_row['invoice_number'];
                                    $data_arr[$orders_id]['payment_methods_id'] = $orders_row['payment_methods_id'];
                                    $data_arr[$orders_id]['payment_methods_parent_id'] = $orders_row['payment_methods_parent_id'];
                                }
                            }

                            # Purchase Amount by Product Type
                            if (isset($data_arr[$orders_id]) && tep_not_null($cpt_array) && tep_not_null($data_arr[$orders_id])) {
                                foreach ($cpt_array as $cpt_id => $cpt_name) {
                                    if (is_numeric($cpt_id)) {
                                        # CDK
                                        if ($cpt_id == 2) {
                                            # Gift Card
                                            $prod_cat_sql = "	SELECT SUM(op.final_price * op.products_quantity) AS purchase_amount 
                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                LEFT JOIN " . TABLE_PRODUCTS . " AS p
                                                                    ON p.products_id = op.products_id
                                                                WHERE op.orders_id = '" . $orders_id . "'
                                                                    AND op.custom_products_type_id = '" . $cpt_id . "'
                                                                    AND op.orders_products_is_compensate = 0
                                                                    AND op.products_bundle_id = 0
                                                                    AND p.products_flag_id = '4'";
                                            $prod_res_sql = tep_db_query($prod_cat_sql, 'read_db_link');
                                            if ($prod_row = tep_db_fetch_array($prod_res_sql)) {
                                                $data_arr[$orders_id]['gift_card'] = tep_not_null($prod_row['purchase_amount']) ? $prod_row['purchase_amount'] : 0;
                                            }
                                            
                                            # Multi Game Card
                                            $parent_opid = 0;
                                            $prod_cat_sql = "	SELECT SUM(op.final_price * op.products_quantity) AS purchase_amount, op.parent_orders_products_id 
                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                WHERE op.orders_id = '" . $orders_id . "' 
                                                                        AND op.custom_products_type_id = '" . $cpt_id . "' 
                                                                        AND op.orders_products_is_compensate = 0
                                                                        AND op.products_model LIKE 'MGC_%'";
                                            $prod_res_sql = tep_db_query($prod_cat_sql, 'read_db_link');
                                            if ($prod_row = tep_db_fetch_array($prod_res_sql)) {
                                                if ($prod_row['parent_orders_products_id']) {
                                                    # bundle product
                                                    $ttl_value_in_amt = 0;
                                                    $mgc_value_in_amt = 0;
                                                    $ttl_purchase_amt = 0;
                                                    $parent_opid = $prod_row['parent_orders_products_id'];
                                                    
                                                    $prod_cat_sql2 = "	SELECT SUM(op.final_price * op.products_quantity) AS purchase_amount 
                                                                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                        WHERE op.orders_products_id = " . $parent_opid;
                                                    $prod_res_sql2 = tep_db_query($prod_cat_sql2, 'read_db_link');
                                                    if ($prod_row2 = tep_db_fetch_array($prod_res_sql2)) {
                                                        $ttl_purchase_amt = $prod_row2['purchase_amount'];
                                                    }
                                                    
                                                    $prod_cat_sql2 = "	SELECT op.products_quantity, op.orders_products_store_price, IF(op.products_model LIKE 'MGC_%', 'yes', 'no') as 'is_mgc_product' 
                                                                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                        WHERE op.orders_id = '" . $orders_id . "'
                                                                            AND op.parent_orders_products_id = '" . $parent_opid . "'";
                                                    $prod_res_sql2 = tep_db_query($prod_cat_sql2, 'read_db_link');
                                                    while ($prod_row2 = tep_db_fetch_array($prod_res_sql2)) {
                                                        if ($prod_row2['is_mgc_product'] === 'yes') {
                                                            $mgc_value_in_amt += $prod_row2['products_quantity'] * $prod_row2['orders_products_store_price'];
                                                        }
                                                        
                                                        $ttl_value_in_amt += $prod_row2['products_quantity'] * $prod_row2['orders_products_store_price'];
                                                    }
                                                    
                                                    $data_arr[$orders_id]['multi_game_card'] = $ttl_value_in_amt > 0 ? ((double) ($mgc_value_in_amt / $ttl_value_in_amt) * $ttl_purchase_amt) : 0;
                                                } else {
                                                    # single product
                                                    $data_arr[$orders_id]['multi_game_card'] = tep_not_null($prod_row['purchase_amount']) ? $prod_row['purchase_amount'] : 0;
                                                }
                                            }
                                            
                                            # CDK
                                            $prod_cat_sql = "	SELECT SUM(IF(op.orders_products_id != " . $parent_opid . " && (op.products_model = '' OR op.products_model IS NULL OR op.products_model NOT LIKE  'MGC_%'), op.final_price * op.products_quantity, 0 )) AS purchase_amount 
                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                                LEFT JOIN " . TABLE_PRODUCTS . " AS p
                                                                    ON p.products_id = op.products_id 
                                                                WHERE op.orders_id = '" . $orders_id . "'
                                                                    AND op.custom_products_type_id = '" . $cpt_id . "'
                                                                    AND op.orders_products_is_compensate = 0
                                                                    AND op.products_bundle_id = 0
                                                                    AND p.products_flag_id != '4'";
                                        } else {
                                            $prod_cat_sql = "	SELECT SUM(op.final_price * op.products_quantity) AS purchase_amount 
                                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                                WHERE op.orders_id = '" . $orders_id . "'
                                                                    AND op.custom_products_type_id = '" . $cpt_id . "'
                                                                    AND op.orders_products_is_compensate = 0
                                                                    AND op.products_bundle_id = 0";
                                        }

                                        $prod_res_sql = tep_db_query($prod_cat_sql, 'read_db_link');
                                        if ($prod_row = tep_db_fetch_array($prod_res_sql)) {
                                            $data_arr[$orders_id][$cpt_id] = tep_not_null($prod_row['purchase_amount']) ? $prod_row['purchase_amount'] : 0;
                                        }

                                        // store credit actual delivery
                                        if ($cpt_id == 3) {
                                            $data_arr[$orders_id]['actual_deliver_currency'] = array();
                                            $data_arr[$orders_id]['actual_deliver_amount'] = array();

                                            $_sql = "SELECT transaction_type, transaction_amount, transaction_currency, created_date
                                                    FROM store_credit_transaction 
                                                    WHERE order_id = '" . $orders_id . "' 
                                                        AND activity IN ('X', 'C', 'R', 'B', 'D', 'S', 'XS', 'RP') 
                                                    ORDER BY transaction_id ASC";
                                            $_res = tep_db_query($_sql, 'og_db');
                                            while ($_row = tep_db_fetch_array($_res)) {
                                                switch ($_row['transaction_type']) {
                                                    case 'ADD_CREDIT':
                                                        $data_arr[$orders_id]['actual_deliver_currency'][] = $_row['transaction_currency'];
                                                        $data_arr[$orders_id]['actual_deliver_amount'][] = '+' . $_row['transaction_amount'];
                                                        break;

                                                    case 'SUBTRACT_CREDIT':
                                                        $data_arr[$orders_id]['actual_deliver_currency'][] = $_row['transaction_currency'];
                                                        $data_arr[$orders_id]['actual_deliver_amount'][] = '-' . $_row['transaction_amount'];
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // PG Transaction ID
                            if (isset($data_arr[$orders_id]) && tep_not_null($data_arr[$orders_id])) {
                                $data_arr[$orders_id]['pg_txn_id'] = '';
                                $pg_txn_sql = " SELECT pp.pg_txn_id
                                                FROM " . TABLE_PIPWAVE_PAYMENT . " AS pp
                                                WHERE pp.orders_id = '" . $orders_id . "'";
                                $pg_txn_result = tep_db_query($pg_txn_sql, 'read_db_link');
                                if ($pg_txn_row = tep_db_fetch_array($pg_txn_result)) {
                                    $data_arr[$orders_id]['pg_txn_id'] = $pg_txn_row['pg_txn_id'];
                                }
                            }
                        }

                        if (tep_not_null($data_arr)) {
                            if ($payment_gateway_row['payment_methods_title'] != EXPORT_TEXT_FULL_STORE_CREDIT) {
                                echo '"' . $payment_gateway_row['payment_methods_title'] . '"' . "\n";
                            }

                            foreach ($data_arr as $orders_id => $data_row) {
                                $payment_method_title = '';
                                // Get payment methods parent title
                                $pm_parent_title_sql = "SELECT payment_methods_title FROM " . TABLE_PAYMENT_METHODS . " WHERE payment_methods_id = '" . $data_row['payment_methods_parent_id'] . "'";
                                $pm_parent_title_result = tep_db_query($pm_parent_title_sql, 'read_db_link');
                                if ($pm_parent_title = tep_db_fetch_array($pm_parent_title_result)) {
                                    $payment_method_title .= $pm_parent_title['payment_methods_title'] . ' - ';
                                }
                                // Get payment methods title
                                $pm_title_sql = "SELECT payment_methods_title FROM " . TABLE_PAYMENT_METHODS . " WHERE payment_methods_id = '" . $data_row['payment_methods_id'] . "'";
                                $pm_title_result = tep_db_query($pm_title_sql, 'read_db_link');
                                if ($pm_title = tep_db_fetch_array($pm_title_result)) {
                                    $payment_method_title .= $pm_title['payment_methods_title'];
                                }
                                // Set default Full Store Credit
                                if (empty($payment_method_title)) {
                                    $payment_method_title = $data_row['payment_method'];
                                }
                                
                                // Order's Entity ( Settlement Account )
                                $settlement = $data_row["settlement_account"];
                                if (isset($data_row["settlement_account"]) && !empty($data_row["settlement_account"])) {
                                    preg_match('#\[(.*?)\]#', $data_row["settlement_account"], $matches);
                                    $settlement = isset($matches[1]) ? strtoupper($matches[1]) : $data_row["settlement_account"];
                                } else if ($data_row['payment_methods_parent_id'] == 0 && $data_row['payment_methods_id'] == 0) {
                                    $settlement = "OGSG";
                                }

                                echo '"' . $data_row['orders_id'] . '","' . $data_row['date_purchased'] . '","' . strip_tags($payment_method_title) . '","' . $data_row['site_id'] . '","' . $settlement . '","' . $data_row['pg_txn_id'] . '","' . $data_row['invoice_number'] . '","' . $data_row['use_g2g_pg'] . '","' . $data_row['currency'] . '",';
                                echo '"' . $data_row['subtotal'] . '","' . $data_row['gst'] . '","' . $data_row['surcharge'] . '","' . $data_row['coupon'] . '","' . $data_row['ogc'] . '","' . $data_row['sc'] . '","' . $data_row['total'] . '","' . $data_row['currency_value'] . '",';
                                echo '"' . $data_row['usd_subtotal'] . '","' . $data_row['usd_gst'] . '","' . $data_row['usd_surcharge'] . '","' . $data_row['usd_coupon'] . '","' . $data_row['usd_ogc'] . '","' . $data_row['usd_sc'] . '","' . $data_row['usd_total'] . '",';
                                echo '"' . $data_row['ip_country'] . '","' . $data_row['gst_percentage'] . '",';

                                if (tep_not_null($cpt_array)) {
                                    foreach ($cpt_array as $cpt_id => $cpt_name) {
                                        echo ',"' . $data_row[$cpt_id] . '"';
                                    }
                                }

                                // store credit actual delivery
                                if (!empty($data_row['actual_deliver_amount'])) {
                                    $_space = str_repeat(",", 26) . str_repeat(",", count($cpt_array));
                                    foreach ($data_row['actual_deliver_amount'] as $_num => $_val) {
                                        if ($_num == 0) {
                                            echo ',"","' . $data_row['actual_deliver_currency'][$_num] . '","' . $_val . '"';
                                        } else {
                                            echo "\n" . $_space . ',"","' . $data_row['actual_deliver_currency'][$_num] . '","' . $_val . '"';
                                        }
                                    }
                                }

                                echo "\n";
                            }
                            echo "\n\n";
                        }

                        unset($data_arr);
                    }
                } else {
                    echo '"' . STORE_NAME . '"' . "\n";
                    echo '"' . REPORT_TYPE_DAILY . ' ' . HEADING_TITLE . '"' . "\n";
                    echo '"From ' . date('d-m-Y', $this->startDates[0]) . ' to ' . date('d-m-Y', $this->endDates[($this->size - 1)]) . '"' . "\n";

                    echo '"' . TABLE_HEADING_CATEGORY . '",,';
                    for ($i = $this->size - 1; $i >= 0; $i--) {
                        $startDates = $this->startDates[$i];
                        echo '"' . EXPORT_TEXT_ORDER . '","' . EXPORT_TEXT_QTY . '","#' . EXPORT_TEXT_PG . ' ' . date('Y-m-d', $startDates) . '","#' . EXPORT_TEXT_PARTIAL_GIFT_CARD . ' ' . date('Y-m-d', $startDates) . '","#' . EXPORT_TEXT_PARTIAL_STORE_CREDIT . ' ' . date('Y-m-d', $startDates) . '","#' . EXPORT_TEXT_DISCOUNT . ' ' . date('Y-m-d', $startDates) . '",,,';
                    }
                    echo '"' . EXPORT_TEXT_ORDER . '","' . EXPORT_TEXT_QTY . '","' . EXPORT_TEXT_TOTAL . ' ' . EXPORT_TEXT_PG . '","' . EXPORT_TEXT_TOTAL . ' ' . EXPORT_TEXT_PARTIAL_GIFT_CARD . '","' . EXPORT_TEXT_TOTAL . ' ' . EXPORT_TEXT_PARTIAL_STORE_CREDIT . '","' . EXPORT_TEXT_TOTAL . ' ' . EXPORT_TEXT_DISCOUNT . '"' . "\n";

                    $csv_data = array();
                    $btm_subtotal = array();
                    $btm_total_sales = array();

                    /* -- payment method -- */
                    $payment_gateway_select_sql = "(SELECT payment_methods_id, payment_methods_title, payment_methods_filename, payment_methods_sort_order 
													FROM " . TABLE_PAYMENT_METHODS . " 
													WHERE payment_methods_receive_status = '1' 
														AND payment_methods_parent_id = 0) 
													UNION 
													(SELECT 0 AS payment_methods_id, '" . EXPORT_TEXT_FULL_STORE_CREDIT . "' AS payment_methods_title, 
														'" . SYSTEM_PAYMENT_STORE_CREDITS . "' AS payment_methods_filename, 0 AS payment_methods_sort_order) 
													ORDER BY payment_methods_sort_order";
                    $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql, 'read_db_link');
                    while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
                        $payment_methods_parent_id = $payment_gateway_row['payment_methods_id'];

                        if (tep_not_null($this->csv_data[$payment_methods_parent_id])) {
                            if ((is_array($_SESSION['sales_param']["payment_gateways_id"]) && (in_array("any", $_SESSION['sales_param']["payment_gateways_id"]) ||
                                    in_array($payment_methods_parent_id, $_SESSION['sales_param']["payment_gateways_id"]) ||
                                    in_array(SYSTEM_PAYMENT_STORE_CREDITS, $_SESSION['sales_param']["payment_gateways_id"]))) ||
                                    tep_not_null($_SESSION['sales_param']['payment_methods_id']) ||
                                    preg_match('/^EXPORT_TEXT_/', $payment_methods_parent_id)) {
                                echo '"' . $payment_gateway_row['payment_methods_title'] . '"' . "\n";

                                if ($payment_methods_parent_id != '0' && !preg_match('/^EXPORT_TEXT_/', $payment_methods_parent_id)) {
                                    $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title 
                                                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                                                        WHERE payment_methods_receive_status = '1' 
                                                                                AND payment_methods_parent_id = '" . $payment_methods_parent_id . "' 
                                                                        ORDER BY payment_methods_sort_order";
                                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql, 'read_db_link');
                                    while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                                        $payment_methods_id = $payment_methods_row['payment_methods_id'];

                                        if ((is_array($_SESSION['sales_param']["payment_gateways_id"]) && (in_array("any", $_SESSION['sales_param']["payment_gateways_id"]) ||
                                                in_array($payment_methods_parent_id, $_SESSION['sales_param']["payment_gateways_id"]))) ||
                                                (is_array($_SESSION['sales_param']['payment_methods_id']) && in_array($payment_methods_id, $_SESSION['sales_param']['payment_methods_id']))) {
                                            if ($payment_gateway_row['payment_methods_filename'] == 'offline.php') {
                                                if (tep_not_null($this->csv_data[$payment_methods_parent_id][$payment_methods_id])) {
                                                    echo '"' . $payment_methods_row['payment_methods_title'] . '"' . "\n";
                                                    $csv_report_data = $this->csv_data[$payment_methods_parent_id][$payment_methods_id];

                                                    foreach ($cpt_array as $cpt_id => $cpt_name) {
                                                        echo '"' . $cpt_name . '",,';

                                                        $horizontal_order = 0;
                                                        $horizontal_qty = 0;
                                                        $horizontal_sales = 0;
                                                        $horizontal_sc = 0;
                                                        $horizontal_dc = 0;
                                                        $horizontal_ogc = 0;

                                                        for ($i = $this->size - 1; $i >= 0; $i--) {
                                                            $startDates = $this->startDates[$i];
                                                            $report_order = $csv_report_data[$cpt_id][$startDates]['order'];
                                                            $report_qty = $csv_report_data[$cpt_id][$startDates]['sales_qty'];
                                                            $report_sales = $csv_report_data[$cpt_id][$startDates]['sales'];
                                                            $report_sc = $csv_report_data[$cpt_id][$startDates]['sc'];
                                                            $report_dc = $csv_report_data[$cpt_id][$startDates]['dc'];
                                                            $report_ogc = $csv_report_data[$cpt_id][$startDates]['ogc'];

                                                            echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                                            $horizontal_order += $report_order;
                                                            $horizontal_qty += $report_qty;
                                                            $horizontal_sales += $report_sales;
                                                            $horizontal_sc += $report_sc;
                                                            $horizontal_dc += $report_dc;
                                                            $horizontal_ogc += $report_ogc;

                                                            $csv_data[$payment_methods_id][$startDates]['order'] += $report_order;
                                                            $csv_data[$payment_methods_id][$startDates]['sales_qty'] += $report_qty;
                                                            $csv_data[$payment_methods_id][$startDates]['sales'] += $report_sales;
                                                            $csv_data[$payment_methods_id][$startDates]['sc'] += $report_sc;
                                                            $csv_data[$payment_methods_id][$startDates]['dc'] += $report_dc;
                                                            $csv_data[$payment_methods_id][$startDates]['ogc'] += $report_ogc;

                                                            $btm_subtotal[$startDates][$cpt_id]['order'] += $report_order;
                                                            $btm_subtotal[$startDates][$cpt_id]['sales_qty'] += $report_qty;
                                                            $btm_subtotal[$startDates][$cpt_id]['sales'] += $report_sales;
                                                            $btm_subtotal[$startDates][$cpt_id]['sc'] += $report_sc;
                                                            $btm_subtotal[$startDates][$cpt_id]['dc'] += $report_dc;
                                                            $btm_subtotal[$startDates][$cpt_id]['ogc'] += $report_ogc;
                                                        }
                                                        echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"' . "\n";
                                                    }

                                                    $horizontal_order = 0;
                                                    $horizontal_qty = 0;
                                                    $horizontal_sales = 0;
                                                    $horizontal_sc = 0;
                                                    $horizontal_dc = 0;
                                                    $horizontal_ogc = 0;

                                                    echo ',,';
                                                    for ($i = $this->size - 1; $i >= 0; $i--) {
                                                        $startDates = $this->startDates[$i];
                                                        $report_order = $csv_data[$payment_methods_id][$startDates]['order'];
                                                        $report_qty = $csv_data[$payment_methods_id][$startDates]['sales_qty'];
                                                        $report_sales = $csv_data[$payment_methods_id][$startDates]['sales'];
                                                        $report_sc = $csv_data[$payment_methods_id][$startDates]['sc'];
                                                        $report_dc = $csv_data[$payment_methods_id][$startDates]['dc'];
                                                        $report_ogc = $csv_data[$payment_methods_id][$startDates]['ogc'];

                                                        echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                                        $horizontal_order += $report_order;
                                                        $horizontal_qty += $report_qty;
                                                        $horizontal_sales += $report_sales;
                                                        $horizontal_sc += $report_sc;
                                                        $horizontal_dc += $report_dc;
                                                        $horizontal_ogc += $report_ogc;

                                                        $btm_total_sales[$startDates]['order'] += $report_order;
                                                        $btm_total_sales[$startDates]['sales_qty'] += $report_qty;
                                                        $btm_total_sales[$startDates]['sales'] += $report_sales;
                                                        $btm_total_sales[$startDates]['sc'] += $report_sc;
                                                        $btm_total_sales[$startDates]['dc'] += $report_dc;
                                                        $btm_total_sales[$startDates]['ogc'] += $report_ogc;
                                                    }
                                                    echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"' . "\n\n";
                                                }
                                            } else {
                                                if (tep_not_null($this->csv_data[$payment_methods_parent_id][$payment_methods_id])) {
                                                    $csv_report_data = $this->csv_data[$payment_methods_parent_id][$payment_methods_id];
                                                    foreach ($cpt_array as $cpt_id => $cpt_name) {
                                                        for ($i = $this->size - 1; $i >= 0; $i--) {
                                                            $startDates = $this->startDates[$i];
                                                            $report_order = $csv_report_data[$cpt_id][$startDates]['order'];
                                                            $report_qty = $csv_report_data[$cpt_id][$startDates]['sales_qty'];
                                                            $report_sales = $csv_report_data[$cpt_id][$startDates]['sales'];
                                                            $report_sc = $csv_report_data[$cpt_id][$startDates]['sc'];
                                                            $report_dc = $csv_report_data[$cpt_id][$startDates]['dc'];
                                                            $report_ogc = $csv_report_data[$cpt_id][$startDates]['ogc'];

                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['order'] += $report_order;
                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sales_qty'] += $report_qty;
                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sales'] += $report_sales;
                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sc'] += $report_sc;
                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['dc'] += $report_dc;
                                                            $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['ogc'] += $report_ogc;

                                                            $csv_data[$payment_methods_parent_id][$startDates]['order'] += $report_order;
                                                            $csv_data[$payment_methods_parent_id][$startDates]['sales_qty'] += $report_qty;
                                                            $csv_data[$payment_methods_parent_id][$startDates]['sales'] += $report_sales;
                                                            $csv_data[$payment_methods_parent_id][$startDates]['sc'] += $report_sc;
                                                            $csv_data[$payment_methods_parent_id][$startDates]['dc'] += $report_dc;
                                                            $csv_data[$payment_methods_parent_id][$startDates]['ogc'] += $report_ogc;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $payment_methods_id = $payment_gateway_row['payment_methods_id'];
                                    if (tep_not_null($this->csv_data[$payment_methods_parent_id][$payment_methods_id])) {
                                        $csv_report_data = $this->csv_data[$payment_methods_parent_id][$payment_methods_id];
                                        foreach ($cpt_array as $cpt_id => $cpt_name) {
                                            echo '"' . $cpt_name . '",,';

                                            if ($payment_methods_parent_id == 0 && !preg_match('/^EXPORT_TEXT_/', $payment_methods_parent_id)) { # Full Store Credit
                                                $horizontal_order = 0;
                                                $horizontal_qty = 0;
                                                $horizontal_sales = 0;
                                                $horizontal_sc = 0;
                                                $horizontal_dc = 0;
                                                $horizontal_ogc = 0;

                                                for ($i = $this->size - 1; $i >= 0; $i--) {
                                                    $startDates = $this->startDates[$i];
                                                    $report_order = $csv_report_data[$cpt_id][$startDates]['order'];
                                                    $report_qty = $csv_report_data[$cpt_id][$startDates]['sales_qty'];
                                                    $report_sales = (($payment_methods_parent_id == 0) && ($payment_methods_id == 0) ? 0 : $csv_report_data[$cpt_id][$startDates]['sales']);
                                                    $report_sc = $csv_report_data[$cpt_id][$startDates]['sc'];
                                                    $report_dc = $csv_report_data[$cpt_id][$startDates]['dc'];
                                                    $report_ogc = $csv_report_data[$cpt_id][$startDates]['ogc'];

                                                    echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                                    $horizontal_order += $report_order;
                                                    $horizontal_qty += $report_qty;
                                                    $horizontal_sales += $report_sales;
                                                    $horizontal_sc += $report_sc;
                                                    $horizontal_dc += $report_dc;
                                                    $horizontal_ogc += $report_ogc;

                                                    $csv_data[$payment_methods_id][$startDates]['order'] += $report_order;
                                                    $csv_data[$payment_methods_id][$startDates]['sales_qty'] += $report_qty;
                                                    $csv_data[$payment_methods_id][$startDates]['sales'] += $report_sales;
                                                    $csv_data[$payment_methods_id][$startDates]['sc'] += $report_sc;
                                                    $csv_data[$payment_methods_id][$startDates]['dc'] += $report_dc;
                                                    $csv_data[$payment_methods_id][$startDates]['ogc'] += $report_ogc;

                                                    $btm_subtotal[$startDates][$cpt_id]['order'] += $report_order;
                                                    $btm_subtotal[$startDates][$cpt_id]['sales_qty'] += $report_qty;
                                                    $btm_subtotal[$startDates][$cpt_id]['sales'] += $report_sales;
                                                    $btm_subtotal[$startDates][$cpt_id]['sc'] += $report_sc;
                                                    $btm_subtotal[$startDates][$cpt_id]['dc'] += $report_dc;
                                                    $btm_subtotal[$startDates][$cpt_id]['ogc'] += $report_ogc;
                                                }
                                                echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"' . "\n";
                                            }
                                        }

                                        if ($payment_methods_parent_id == 0 && !preg_match('/^EXPORT_TEXT_/', $payment_methods_parent_id)) { # Full Store Credit
                                            $horizontal_order = 0;
                                            $horizontal_qty = 0;
                                            $horizontal_sales = 0;
                                            $horizontal_sc = 0;
                                            $horizontal_dc = 0;
                                            $horizontal_ogc = 0;

                                            echo ',,';
                                            for ($i = $this->size - 1; $i >= 0; $i--) {
                                                $startDates = $this->startDates[$i];
                                                $report_order = $csv_data[$payment_methods_id][$startDates]['order'];
                                                $report_qty = $csv_data[$payment_methods_id][$startDates]['sales_qty'];
                                                $report_sales = $csv_data[$payment_methods_id][$startDates]['sales'];
                                                $report_sc = $csv_data[$payment_methods_id][$startDates]['sc'];
                                                $report_dc = $csv_data[$payment_methods_id][$startDates]['dc'];
                                                $report_ogc = $csv_data[$payment_methods_id][$startDates]['ogc'];

                                                echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                                $horizontal_order += $report_order;
                                                $horizontal_qty += $report_qty;
                                                $horizontal_sales += $report_sales;
                                                $horizontal_sc += $report_sc;
                                                $horizontal_dc += $report_dc;
                                                $horizontal_ogc += $report_ogc;

                                                $btm_total_sales[$startDates]['order'] += $report_order;
                                                $btm_total_sales[$startDates]['sales_qty'] += $report_qty;
                                                $btm_total_sales[$startDates]['sales'] += $report_sales;
                                                $btm_total_sales[$startDates]['sc'] += $report_sc;
                                                $btm_total_sales[$startDates]['dc'] += $report_dc;
                                                $btm_total_sales[$startDates]['ogc'] += $report_ogc;
                                            }
                                            echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"';
                                        }
                                        echo "\n";
                                    }
                                }
                            }
                        }

                        if (($payment_gateway_row['payment_methods_filename'] != 'offline.php') &&
                                ($payment_methods_parent_id != 0) &&
                                (is_array($_SESSION['sales_param']["payment_gateways_id"]) && (in_array("any", $_SESSION['sales_param']["payment_gateways_id"]) || in_array($payment_methods_parent_id, $_SESSION['sales_param']["payment_gateways_id"])))) {
                            foreach ($cpt_array as $cpt_id => $cpt_name) {
                                echo '"' . $cpt_name . '",,';

                                $horizontal_order = 0;
                                $horizontal_qty = 0;
                                $horizontal_sales = 0;
                                $horizontal_sc = 0;
                                $horizontal_dc = 0;
                                $horizontal_ogc = 0;

                                for ($i = $this->size - 1; $i >= 0; $i--) {
                                    $startDates = $this->startDates[$i];
                                    $report_order = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['order'];
                                    $report_qty = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sales_qty'];
                                    $report_sales = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sales'];
                                    $report_sc = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['sc'];
                                    $report_dc = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['dc'];
                                    $report_ogc = $csv_data[$payment_methods_parent_id][$cpt_id][$startDates]['ogc'];

                                    echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                    $horizontal_order += $report_order;
                                    $horizontal_qty += $report_qty;
                                    $horizontal_sales += $report_sales;
                                    $horizontal_sc += $report_sc;
                                    $horizontal_dc += $report_dc;
                                    $horizontal_ogc += $report_ogc;

                                    $btm_subtotal[$startDates][$cpt_id]['order'] += $report_order;
                                    $btm_subtotal[$startDates][$cpt_id]['sales_qty'] += $report_qty;
                                    $btm_subtotal[$startDates][$cpt_id]['sales'] += $report_sales;
                                    $btm_subtotal[$startDates][$cpt_id]['sc'] += $report_sc;
                                    $btm_subtotal[$startDates][$cpt_id]['dc'] += $report_dc;
                                    $btm_subtotal[$startDates][$cpt_id]['ogc'] += $report_ogc;
                                }
                                echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"' . "\n";
                            }

                            $horizontal_order = 0;
                            $horizontal_qty = 0;
                            $horizontal_sales = 0;
                            $horizontal_sc = 0;
                            $horizontal_dc = 0;
                            $horizontal_ogc = 0;

                            echo ',,';
                            for ($i = $this->size - 1; $i >= 0; $i--) {
                                $startDates = $this->startDates[$i];
                                $report_order = $csv_data[$payment_methods_parent_id][$startDates]['order'];
                                $report_qty = $csv_data[$payment_methods_parent_id][$startDates]['sales_qty'];
                                $report_sales = $csv_data[$payment_methods_parent_id][$startDates]['sales'];
                                $report_sc = $csv_data[$payment_methods_parent_id][$startDates]['sc'];
                                $report_dc = $csv_data[$payment_methods_parent_id][$startDates]['dc'];
                                $report_ogc = $csv_data[$payment_methods_parent_id][$startDates]['ogc'];

                                echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '",,,';

                                $horizontal_order += $report_order;
                                $horizontal_qty += $report_qty;
                                $horizontal_sales += $report_sales;
                                $horizontal_sc += $report_sc;
                                $horizontal_dc += $report_dc;
                                $horizontal_ogc += $report_ogc;

                                $btm_total_sales[$startDates]['order'] += $report_order;
                                $btm_total_sales[$startDates]['sales_qty'] += $report_qty;
                                $btm_total_sales[$startDates]['sales'] += $report_sales;
                                $btm_total_sales[$startDates]['sc'] += $report_sc;
                                $btm_total_sales[$startDates]['dc'] += $report_dc;
                                $btm_total_sales[$startDates]['ogc'] += $report_ogc;
                            }
                            echo '"' . $horizontal_order . '","' . $horizontal_qty . '","' . $horizontal_sales . '","' . $horizontal_ogc . '","' . $horizontal_sc . '","' . $horizontal_dc . '"' . "\n\n";
                        }
                    }

                    /* -- Sub-Total -- */
                    $horizontal_order = array();
                    $horizontal_qty = array();
                    $horizontal_sales = array();
                    $horizontal_sc = array();
                    $horizontal_dc = array();
                    $horizontal_ogc = array();

                    echo '"' . EXPORT_TEXT_SUB_TOTAL . '"' . "\n";

                    foreach ($cpt_array as $cpt_id => $cpt_name) {
                        echo '"' . $cpt_name . '",,';
                        for ($i = $this->size - 1; $i >= 0; $i--) {
                            $startDates = $this->startDates[$i];

                            $report_order = $btm_subtotal[$startDates][$cpt_id]['order'];
                            $report_qty = $btm_subtotal[$startDates][$cpt_id]['sales_qty'];
                            $report_sales = $btm_subtotal[$startDates][$cpt_id]['sales'];
                            $report_sc = $btm_subtotal[$startDates][$cpt_id]['sc'];
                            $report_dc = $btm_subtotal[$startDates][$cpt_id]['dc'];
                            $report_ogc = $btm_subtotal[$startDates][$cpt_id]['ogc'];

                            echo '"' . $report_order . '","' . $report_qty . '","' . $report_sales . '","' . $report_ogc . '","' . $report_sc . '","' . $report_dc . '"' . str_repeat(',', 3);

                            $horizontal_order[$cpt_id] += $report_order;
                            $horizontal_qty[$cpt_id] += $report_qty;
                            $horizontal_sales[$cpt_id] += $report_sales;
                            $horizontal_sc[$cpt_id] += $report_sc;
                            $horizontal_dc[$cpt_id] += $report_dc;
                            $horizontal_ogc[$cpt_id] += $report_ogc;
                        }
                        echo '"' . $horizontal_order[$cpt_id] . '","' . $horizontal_qty[$cpt_id] . '","' . $horizontal_sales[$cpt_id] . '","' . $horizontal_ogc[$cpt_id] . '","' . $horizontal_sc[$cpt_id] . '","' . $horizontal_dc[$cpt_id] . '"' . "\n";
                    }
                    echo "\n\n";

                    /* -- Total of Sales -- */
                    $show_total_order = 0;
                    $show_total_sales = 0;
                    $show_total_avg = 0;

                    echo '"' . TABLE_HEADING_SALES . '",,';
                    for ($i = $this->size - 1; $i >= 0; $i--) {
                        $startDates = $this->startDates[$i];
                        $report_order = $btm_total_sales[$startDates]['order'];
                        $report_sales = $btm_total_sales[$startDates]['sales'] + $btm_total_sales[$startDates]['sc'] + $btm_total_sales[$startDates]['dc'];

                        echo '"' . $report_order . '"' . str_repeat(',', 5) . '"' . $report_sales . '"' . str_repeat(',', 3);

                        $show_total_order += $report_order;
                        $show_total_sales += $report_sales;

                        if ($report_order > 0 && $report_sales > 0) {
                            $btm_total_sales[$startDates]['avg'] = ($report_sales / $report_order);
                            $show_total_avg += ($report_sales / $report_order);
                        } else {
                            $btm_total_sales[$startDates]['avg'] = 0;
                        }
                    }
                    echo '"' . $show_total_order . '"' . str_repeat(',', 5) . '"' . $show_total_sales . '"' . "\n";

                    echo '"' . TABLE_HEADING_SALES_PER_ORDER . '",,';
                    for ($i = $this->size - 1; $i >= 0; $i--) {
                        $startDates = $this->startDates[$i];
                        echo str_repeat(',', 5) . '"' . $btm_total_sales[$startDates]['avg'] . '"' . str_repeat(',', 3);
                    }
                    echo str_repeat(',', 5);
                    if ($show_total_sales > 0 && $show_total_order > 0) {
                        echo '"' . ($show_total_avg / $this->size) . '"';
                    } else {
                        echo '"0.00"';
                    }
                    echo "\n";

                    echo '"' . AVERAGE_DAILY_TOTAL . '",,';
                    echo str_repeat(',,,,,,,,', $this->size) . '"' . $this->size . '"' . str_repeat(',', 5) . '"' . ($show_total_sales / $this->size) . '"';

                    unset($this->csv_data);
                }
                break;

            default:
                $export_csv_data = '';
                $filename = '';

                if (!is_dir("download/")) {
                    mkdir("download/", 0755);
                }

                if (count($report_csv_data)) {
                    foreach ($report_csv_data as $res) {
                        $tmp_cvs_data_array = array();

                        for ($i = 0; $i < count($res); $i++) {
                            $tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
                        }

                        $export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
                    }
                }

                if (tep_not_null($export_csv_data)) {
                    $filename = 'sales_report_' . date('YmdHis') . '.csv';
                    $file_location = 'download/' . $filename;

                    if (!$handle = fopen($file_location, 'w')) {
                        exit;
                    }

                    // Write to our opened file.
                    if (fwrite($handle, $export_csv_data) === FALSE) {
                        fclose($handle);
                        exit;
                    }

                    fclose($handle);
                    return $filename;
                }
                break;
        }
    }

    function clean_up_csv($checktime = 0, $type = '') {
        $dir = "download/";

        //$counter = '1';
        $time_now = time();
        $checktime *= 60;
        $time_different_between = "";

        if (is_dir($dir)) {
            if ($dh = opendir($dir)) {
                while (($file = readdir($dh)) !== false) {
                    $time_different_between = "";
                    $file_type = substr(strrchr($file, '.'), 1);
                    if ($type == $file_type) {
                        $time_different_between = $time_now - fileatime($dir . $file);
                        if ($checktime > $time_different_between) {
                            
                        } else {
                            @unlink($dir . $file);
                        }
                    } else {
                        ; //File Type not match;
                    }
                    //$counter++;
                }
                closedir($dh);
            }
        }
    }

    /* -- sales amount within date range -- */

    function subtotal_calculation($subtotal, $values) {
        foreach ($values as $key => $value) {
            for ($i = 0; count($this->startDates) > $i; $i++) {
                if (($key >= $this->startDates[$i]) && ($key <= $this->endDates[$i])) {
                    $subtotal[$this->startDates[$i]] += $value;
                }
            }
        }

        return $subtotal;
    }

    /* -- getCatTree for mode 7: Category (Stock + Sales) -- */

    function categoryGetCatTree($cat_id, $languages_id = '', $level = '') {
        $cat_array = array();
        $p_rank = tep_check_cat_tree_permissions(FILENAME_SALES_REPORT, $cat_id);

        if ($p_rank > 0) {
            /* -- TOP or highest categories: -- */
            if (0 >= $cat_id) {
                $cat_row = array('parent_id' => -1, 'categories_name' => TEXT_TOP);
            } else {
                $cat_select_sql = " SELECT c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.categories_id = '" . $cat_id . "'";
                $cat_result_sql = tep_db_query($cat_select_sql, 'read_db_link');
                $cat_row = tep_db_fetch_array($cat_result_sql);
            }

            if (!tep_not_null($_REQUEST['export_report'])) {
                $name = str_repeat('&nbsp;', $level * 3) . '<span id="cell_' . $cat_id . '">##</span>' . '&nbsp;' . strip_tags($cat_row['categories_name']);
            } else {
                $name = strip_tags($cat_row['categories_name']);
            }

            $cat_array[$cat_id] = array('id' => $cat_id,
                'name' => $name,
                'subtotal' => array(),
                'products' => array("purchased" => array()),
                'level' => $level
            );

            $this->parantChild[$cat_row['parent_id']][] = $cat_id;

            /* -- identify product and product bundle from highest category (except TOP category) -- */
            if ($cat_id > 0) {
                $cat_row['parent_id'] = $cat_id;
                $cat_array = $this->productPriceSalesQuantity($cat_id, $cat_row, $cat_array, $languages_id);
                $cat_array = $this->productAndBundle($cat_row, $cat_array, $languages_id);
            }

            $level++;
            $parent[] = $cat_id;
            $parent_id = array();

            do {
                do {
                    $cat_id = array_pop($parent);

                    $cat_select_sql = " SELECT c1.categories_id as parent_id, c2.categories_id as child_id, c1.categories_parent_path, c1.custom_products_type_id, cd.categories_name 
										FROM " . TABLE_CATEGORIES . " AS c1
										LEFT JOIN " . TABLE_CATEGORIES . " AS c2
											ON c2.parent_id = c1.categories_id
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											ON c1.categories_id = cd.categories_id 
										WHERE c1.parent_id = '" . $cat_id . "'
											AND cd.language_id = '" . $languages_id . "'
										ORDER BY c1.sort_order, cd.categories_name";

                    $cat_result_sql = tep_db_query($cat_select_sql, 'read_db_link');
                    while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                        if (!tep_not_null($_SESSION['sales_param']["pro_type"]) || $this->cpt_filter($_SESSION['sales_param']["pro_type"], $cat_row['custom_products_type_id'], $level)) {
                            $new_parent_id = false;

                            if (!isset($cat_array[$cat_row['parent_id']])) {
                                if (!tep_not_null($_REQUEST['export_report'])) {
                                    $name = str_repeat('&nbsp;', $level * 3) . '<span id="cell_' . $cat_row['parent_id'] . '">##</span>' . '&nbsp;' . strip_tags($cat_row['categories_name']);
                                } else {
                                    $name = strip_tags($cat_row['categories_name']);
                                }

                                $this->parantChild[$cat_id][] = $cat_row['parent_id'];

                                $cat_array[$cat_row['parent_id']] = array('id' => $cat_row['parent_id'],
                                    'name' => $name,
                                    'subtotal' => array(),
                                    'products' => array("purchased" => array()),
                                    'level' => $level
                                );

                                /* -- collect categories_parent_path -- */
                                if (!empty($cat_row['categories_parent_path'])) {
                                    $categories_parent_path = explode('_', $cat_row['categories_parent_path']);

                                    foreach ($categories_parent_path as $key => $value) {
                                        if (!empty($value))
                                            $cat_array[$cat_row['parent_id']]['categories_parent_path'][] = $value;
                                    }
                                }

                                $new_parent_id = true;
                            }

                            if ($cat_row['child_id'] != NULL) {
                                if (!in_array($cat_row['parent_id'], $parent_id)) {
                                    $parent_id[] = $cat_row['parent_id'];
                                    $cat_array = $this->productPriceSalesQuantity($cat_id, $cat_row, $cat_array, $languages_id);
                                }
                            }
                            /* -- lowest level category price, sales, quantity calculation -- */ else {
                                $cat_array = $this->productPriceSalesQuantity($cat_id, $cat_row, $cat_array, $languages_id);
                            }

                            if ($new_parent_id === true) {
                                $cat_array = $this->productAndBundle($cat_row, $cat_array, $languages_id);
                            }
                        }
                    }
                } while (!empty($parent));

                $level++;

                if (!empty($parent_id))
                    $parent = $parent_id;

                $parent_id = array();
            } while (!empty($parent));


            /* -- calculate subtotal -- */
            if (tep_not_null($cat_array)) {
                foreach ($cat_array as $info => $info_array) {
                    if (tep_not_null($info_array['categories_parent_path'])) {
                        foreach ($info_array['categories_parent_path'] as $num => $cat_parent_id) {
                            foreach ($info_array['subtotal'] as $date => $subtotal) {
                                if (isset($cat_array[$cat_parent_id])) {
                                    $cat_array[$cat_parent_id]['subtotal'][$date] += $subtotal;
                                }

                                if ($num == 0) {
                                    $this->totalSales += $subtotal;
                                    if (isset($cat_array[0])) {
                                        $cat_array[0]['subtotal'][$date] += $subtotal;
                                    }
                                }
                            }
                        }
                    } else {
                        foreach ($info_array['subtotal'] as $date => $subtotal) {
                            $this->totalSales += $subtotal;
                        }
                    }
                }
            }
        }

        return $cat_array;
    }

    /* -- lowest level category price, sales, quantity calculation -- */

    function productPriceSalesQuantity($cat_id, $cat_row, $cat_array, $languages_id) {
        for ($i = 0; $i < $this->size; $i++) {
            $total_prod = 0;
            $date_range_where_sql = " o.date_purchased >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND" .
                    " o.date_purchased <= '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";

            if (count($this->order_amount_status_array)) {
                /* -- product total price -- */
                $prod_total_price_select_sql = "SELECT op.orders_products_id, o.orders_id, o.orders_status, o.orders_cb_status, " . $this->order_amount_status_str . " 
    											FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
    												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
    												ON op.products_id = pc.products_id 
    												INNER JOIN " . TABLE_ORDERS . " AS o 
    												ON op.orders_id = o.orders_id " . $this->extra_joining_str . " 
												WHERE " . $date_range_where_sql . " 
													AND pc.categories_id = '" . $cat_row['parent_id'] . "' 
													AND pc.products_is_link=0 
													AND op.parent_orders_products_id=0 
													AND op.orders_products_is_compensate=0 " .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "") . "
												GROUP BY o.orders_id";
                $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
                while ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                    $sales_amount = $this->get_sales_amount($prod_total_price_row);
                    $total_prod += $sales_amount;
                }

                /* -- product quantity sold -- */
                if($this->g2g_region_layer == true){
                    //For G2G region sales
                    $prod_qty_sold_select_sql = "SELECT p.products_bundle, o.orders_status, o.orders_cb_status, op.products_id, op.products_bundle_id, op.orders_products_id, opei.orders_products_extra_info_value, 
                                                " . $this->order_qty_fileds_str . " 
                                                FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
                                                    INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                                    ON pc.products_id = op.products_id 
                                                    INNER JOIN " . TABLE_PRODUCTS . " AS p 
                                                    ON pc.products_id = p.products_id 
                                                    INNER JOIN " . TABLE_ORDERS . " AS o 
                                                    ON op.orders_id = o.orders_id " . $this->extra_joining_str . " 
                                                    LEFT JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei 
                                                    ON op.orders_products_id = opei.orders_products_id AND opei.orders_products_extra_info_key = 'region'
                                                WHERE pc.categories_id = '" . $cat_row['parent_id'] . "' 
                                                    AND $date_range_where_sql 
                                                    AND op.parent_orders_products_id = 0 
                                                    AND pc.products_is_link=0 " .
                            ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
                                                    AND op.orders_products_is_compensate=0";
                } else {
                $prod_qty_sold_select_sql = "SELECT p.products_bundle, o.orders_status, o.orders_cb_status, op.products_id, op.products_bundle_id, op.orders_products_id, 
											" . $this->order_qty_fileds_str . " 
											FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON pc.products_id = op.products_id 
												INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON pc.products_id = p.products_id 
												INNER JOIN " . TABLE_ORDERS . " AS o 
												ON op.orders_id = o.orders_id " . $this->extra_joining_str . " 
											WHERE pc.categories_id = '" . $cat_row['parent_id'] . "' 
												AND $date_range_where_sql 
												AND op.parent_orders_products_id = 0 
												AND pc.products_is_link=0 " .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
												AND op.orders_products_is_compensate=0";
                }
                $prod_qty_sold_result_sql = tep_db_query($prod_qty_sold_select_sql, 'read_db_link');
                while ($prod_qty_sold_row = tep_db_fetch_array($prod_qty_sold_result_sql)) {
                    $bundle_product_qty = 0;
                    $calc_sub_bundle_qty = 0;

                    //get G2G region by game
                    if($this->g2g_region_layer == true && tep_not_null($prod_qty_sold_row['orders_products_extra_info_value'])){
                        $region_data = json_decode($prod_qty_sold_row['orders_products_extra_info_value'], true);
                        if(tep_not_null($region_data)){
                            $region_id = isset($region_data['id']) ? $region_data['id'] : 'unknown';
                            $region_value = isset($region_data['value']) ? $region_data['value'] : 'unknown';
                            $cat_array[$cat_row['parent_id']]["products"]["region"][$region_id] = $region_value;
                        }
                    }

                    if ($prod_qty_sold_row['products_bundle'] == 'yes') {
                        // GET SubProduct 'QTY SOLD'
                        $sub_prod_qty_sold_select_sql = "SELECT op.parent_orders_products_id, o.orders_status, o.orders_cb_status, op.products_id, op.products_name, op.products_bundle_id, op.orders_products_id, " . $this->order_qty_fileds_str . "
														FROM " . TABLE_ORDERS . " AS o 
															INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
															ON o.orders_id = op.orders_id " . $this->extra_joining_str . " 
														WHERE op.parent_orders_products_id = '" . $prod_qty_sold_row["orders_products_id"] . "'";
                        $sub_prod_qty_sold_result_sql = tep_db_query($sub_prod_qty_sold_select_sql, 'read_db_link');
                        while ($sub_prod_qty_sold_row = tep_db_fetch_array($sub_prod_qty_sold_result_sql)) {
                            $sub_sales_qty = $this->get_sales_qty($sub_prod_qty_sold_row);

                            if ($sub_sales_qty) {
                                $cat_array[$cat_row['parent_id']]["sub_products"]["qty_sold"][$this->startDates[$i]][$sub_prod_qty_sold_row["products_bundle_id"]][$sub_prod_qty_sold_row["products_id"]] += $sub_sales_qty;
                            }

                            if (!in_array($sub_prod_qty_sold_row["products_id"], $cat_array[$cat_row['parent_id']]["products"]["purchased"])) {
                                $cat_array[$cat_row['parent_id']]["products"]["purchased"][] = $sub_prod_qty_sold_row["products_id"];
                            }

                            /* For calculate the bundle qty */
                            if ($sub_prod_qty_sold_row['total_purchase_qty'] != 0 || $prod_qty_sold_row['total_purchase_qty'] != 0) {
                                $get_bundle_qty_based_one_this = $sub_prod_qty_sold_row['total_purchase_qty'] / $prod_qty_sold_row['total_purchase_qty'];
                            }

                            if ($sub_sales_qty != 0) {
                                $calc_sub_bundle_qty = ceil($sub_sales_qty / $get_bundle_qty_based_one_this);
                            }

                            $bundle_product_qty = $calc_sub_bundle_qty > $bundle_product_qty ? $calc_sub_bundle_qty : $bundle_product_qty;

                            if (!tep_not_null($cat_array[$cat_row['parent_id']]['products']['products_bundle_name'][$sub_prod_qty_sold_row['products_bundle_id']][$sub_prod_qty_sold_row['products_id']])) {
                                $cat_array[$cat_row['parent_id']]['products']['products_bundle_name'][$sub_prod_qty_sold_row['products_bundle_id']][$sub_prod_qty_sold_row['products_id']] = '(' . $sub_prod_qty_sold_row['products_id'] . ') ' . $sub_prod_qty_sold_row['products_name'];
                            }
                        }

                        $cat_array[$cat_row['parent_id']]["products"]["qty_sold"][$this->startDates[$i]][$prod_qty_sold_row["products_id"]] += $bundle_product_qty;
                    } else {
                        $sales_qty = $this->get_sales_qty($prod_qty_sold_row);
                        if ($sales_qty) { // Only count as an order if there is qty for the sub status
                            $cat_array[$cat_row['parent_id']]["products"]["qty_sold"][$this->startDates[$i]][$prod_qty_sold_row["products_id"]] += $sales_qty;
                            //For G2G region sales
                            if(isset($region_id)){
                                $cat_array[$cat_row['parent_id']]["products"]["region_qty_sold"][$this->startDates[$i]][$region_id] += $sales_qty;
                            }
                        }
                    }

                    if (!in_array($prod_qty_sold_row["products_id"], $cat_array[$cat_row['parent_id']]["products"]["purchased"])) {
                        $cat_array[$cat_row['parent_id']]["products"]["purchased"][] = $prod_qty_sold_row["products_id"];
                    }

                    $bundle_sales_amt = $this->bundle_sales_amount($prod_qty_sold_row['orders_products_id'], $i); // Get CDKEY BUNDLE amount
                    # record products bundle sales amt
                    $cat_array[$cat_row['parent_id']]["products"]["bundle_sales_amt"][$this->startDates[$i]][$prod_qty_sold_row["products_id"]] += $bundle_sales_amt;
                    //For G2G region sales
                    if(isset($region_id)){
                        $cat_array[$cat_row['parent_id']]["products"]["region_bundle_sales_amt"][$this->startDates[$i]][$region_id] += $bundle_sales_amt;
                    }
                }
            } else {
                $prod_total_price_select_sql = "SELECT SUM(op.final_price*op.products_quantity) AS toal_prod_sales 
												FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
													INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
													ON op.products_id = pc.products_id
													INNER JOIN " . TABLE_ORDERS . " AS o 
													ON op.orders_id = o.orders_id " . $this->extra_joining_str . " 
												WHERE " . $date_range_where_sql . " 
													AND pc.categories_id = '" . $cat_row['parent_id'] . "' 
													AND pc.products_is_link=0 
													AND op.parent_orders_products_id=0 
													AND op.orders_products_is_compensate=0 " .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "");
                $prod_total_price_result_sql = tep_db_query($prod_total_price_select_sql, 'read_db_link');
                if ($prod_total_price_row = tep_db_fetch_array($prod_total_price_result_sql)) {
                    $total_prod = $prod_total_price_row["toal_prod_sales"];
                }

                $prod_qty_sold_select_sql = "SELECT op.final_price, op.products_quantity, op.products_bundle_id, op.parent_orders_products_id, p.products_id, SUM( op.products_quantity ) AS total_prod_qty 
											FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON pc.products_id = op.products_id 
												INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON pc.products_id = p.products_id 
												INNER JOIN " . TABLE_ORDERS . " AS o 
												ON op.orders_id = o.orders_id " . $this->extra_joining_str . " 
											WHERE pc.categories_id = '" . $cat_id . "' 
												AND $date_range_where_sql 
												AND pc.products_is_link=0 " .
                        ($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
												AND op.orders_products_is_compensate=0 
											GROUP BY op.products_id, op.parent_orders_products_id";

                $prod_qty_sold_result_sql = tep_db_query($prod_qty_sold_select_sql, 'read_db_link');

                while ($prod_qty_sold_row = tep_db_fetch_array($prod_qty_sold_result_sql)) {
                    if ($prod_qty_sold_row['parent_orders_products_id'] != 0) {
                        //$cat_array[$array_index]["sub_products"]["qty_sold"][$this->startDates[$i]][$prod_qty_sold_row["products_bundle_id"]][$prod_qty_sold_row["products_id"]] += $prod_qty_sold_row['total_prod_qty'];
                        $cat_array[$cat_row['parent_id']]["sub_products"]["qty_sold"][$this->startDates[$i]][$prod_qty_sold_row["products_bundle_id"]][$prod_qty_sold_row["products_id"]] += $prod_qty_sold_row['total_prod_qty'];

                        if (!in_array($prod_qty_sold_row["products_id"], $cat_array[$cat_row['parent_id']]["products"]["purchased"])) {
                            $cat_array[$cat_row['parent_id']]["products"]["purchased"][] = $prod_qty_sold_row["products_id"];
                        }
                    } else {
                        $cat_array[$cat_row['parent_id']]["products"]["qty_sold"][$this->startDates[$i]][$prod_qty_sold_row["products_id"]] = $prod_qty_sold_row["total_prod_qty"];

                        if (!in_array($prod_qty_sold_row["products_id"], $cat_array[$cat_row['parent_id']]["products"]["purchased"])) {
                            $cat_array[$cat_row['parent_id']]["products"]["purchased"][] = $prod_qty_sold_row["products_id"];
                        }

                        $cat_array[$cat_row['parent_id']]["products"]["bundle_sales_amt"][$this->startDates[$i]][$prod_qty_sold_row["products_id"]] += ($prod_qty_sold_row['final_price'] * $prod_qty_sold_row['total_prod_qty']);
                    }
                }
            }

            $cat_array[$cat_row['parent_id']]['subtotal'][$this->startDates[$i]] += $total_prod;
        }

        return $cat_array;
    }

    /* -- product and bundle name, quantity, actual quantity for mode 7: Category (Stock + Sales) -- */

    function productAndBundle($cat_row, $cat_array, $languages_id) {
        $products_name_select_sql = "SELECT p.products_id, pd.products_name, p.products_bundle, p.products_quantity, p.products_actual_quantity 
									FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
										ON p2c.products_id=p.products_id 
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
										ON p2c.products_id=pd.products_id 
									WHERE p2c.categories_id='" . $cat_row['parent_id'] . "' 
										AND p2c.products_is_link=0 
										AND pd.language_id='" . (int) $languages_id . "' 
										AND IF(p.products_status=0, p.products_id IN ('" . (is_array($cat_array[$cat_row['parent_id']]['products']['purchased']) ? implode("', '", $cat_array[$cat_row['parent_id']]['products']['purchased']) : '') . "'), 1) 
									ORDER BY p.products_sort_order , pd.products_name";
        $products_name_result_sql = tep_db_query($products_name_select_sql, 'read_db_link');
        while ($result_row = tep_db_fetch_array($products_name_result_sql)) {
            $cat_array[$cat_row['parent_id']]['products']['products_name'][$result_row['products_id']] = $result_row['products_name'];
            $cat_array[$cat_row['parent_id']]['products']['products_bundle'][$result_row['products_id']] = $result_row['products_bundle'];

            if ($result_row['products_bundle'] != 'yes') {
                $cat_array[$cat_row['parent_id']]['products']['products_quantity'][$result_row['products_id']] = $result_row['products_quantity'];
                $cat_array[$cat_row['parent_id']]['products']['products_actual_quantity'][$result_row['products_id']] = $result_row['products_actual_quantity'];
            } else {
                $products_bundle_sql = "SELECT p.products_id, pd.products_name, p.products_quantity, p.products_actual_quantity 
										FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON pb.subproduct_id=p.products_id 
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON pb.subproduct_id=pd.products_id 
										WHERE pb.bundle_id='" . $result_row['products_id'] . "' 
											AND p.products_status = '1' 
											AND pd.language_id='" . (int) $languages_id . "'";
                $products_bundle_result_sql = tep_db_query($products_bundle_sql, 'read_db_link');
                while ($products_bundle_row = tep_db_fetch_array($products_bundle_result_sql)) {
                    $cat_array[$cat_row['parent_id']]['products']['products_bundle_name'][$result_row['products_id']][$products_bundle_row['products_id']] = strip_tags($products_bundle_row['products_name']);
                    $cat_array[$cat_row['parent_id']]['products']['products_quantity'][$result_row['products_id']][$products_bundle_row['products_id']] = $products_bundle_row['products_quantity'];
                    $cat_array[$cat_row['parent_id']]['products']['products_actual_quantity'][$result_row['products_id']][$products_bundle_row['products_id']] = $products_bundle_row['products_actual_quantity'];
                }
            }
        }

        return $cat_array;
    }

    /* -- displayCatTree for mode 7: Category (Stock + Sales) -- */

    function categoryDisplayCatTree($level, $currencies) {
        global $languages_id;

        $this->call_array_child($currencies, key($this->parantChild), $level);

        /* -- total quantity sold -- */
        echo '<tr><td class="reportRecords" colspan="' . (2 + count($this->prod_qty_type)) . '">&nbsp;</td>';

        for ($i = ($this->size - 1); $i >= 0; $i--) {
            echo '<td align="right" class="reportRecords">' . number_format($this->total_amount[$this->startDates[$i]], 0, '.', '') . '</td><td class="reportRecords">&nbsp;</td>';
        }
    }

    /* -- call cat_array child for categorydisplayCatTree -- */

    function call_array_child($currencies, $key, $level) {
        foreach ($this->parantChild[$key] as $key_idx => $value) {
            $product_info_table = '';
            $available_quantity_text = '';
            $actual_quantity_text = '';
            $expand_collapse_txt = (is_array($this->parantChild[$value]) ? ( ($this->info[$value]['level'] >= $level - 1) ? "<a href=\"javascript:;\" onClick=\"expandCollapse('" . $this->info[$value]['id'] . "', 'show')\">" . tep_image(DIR_WS_ICONS . "expand.gif", '', 10, 10, 'border=0') . "</a>" : "<a href=\"javascript:;\" onClick=\"expandCollapse('" . $this->info[$value]["id"] . "', 'hide')\">" . tep_image(DIR_WS_ICONS . "collapse.gif", '', 10, 10, 'border=0') . "</a>") : tep_image(DIR_WS_IMAGES . "pixel_trans.gif", '', 10, 10, 'border=0'));
            $display_name = str_replace('##', $expand_collapse_txt, $this->info[$value]['name']);

            /* -- product name, available quantity, actual quantity -- */
            if (isset($this->info[$value]['products']['products_name'])) {
                foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                    $display_name .= '<br />' . str_repeat('&nbsp;', $this->info[$value]['level'] * 3 + 3) . '&raquo;&nbsp;';

                    if ($this->info[$value]['products']['products_bundle'][$products_key] == 'yes')
                        $display_name .= '<b>';

                    $display_name .= (!empty($products_name) ? strip_tags($products_name) : 'Unknown');

                    if (isset($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                        $display_name .= '</b>';
                        $available_quantity_text .= '<br />';
                        $actual_quantity_text .= '<br />';

                        foreach ($this->info[$value]['products']['products_bundle_name'][$products_key] as $sub_key => $sub_value) {
                            $display_name .= '<br />' . str_repeat('&nbsp;', $this->info[$value]['level'] * 3 + 6) . '&raquo;&raquo;&nbsp;' . '(' . $sub_key . ') ' . $sub_value;
                            $available_quantity_text .= '<br />' . (tep_not_null($this->info[$value]['products']['products_quantity'][$products_key][$sub_key]) ? $this->info[$value]['products']['products_quantity'][$products_key][$sub_key] : '0');
                            $actual_quantity_text .= '<br />' . (tep_not_null($this->info[$value]['products']['products_actual_quantity'][$products_key][$sub_key]) ? $this->info[$value]['products']['products_actual_quantity'][$products_key][$sub_key] : '0');
                        }
                    } else {
                        $display_name .= '</b>';
                        $available_quantity_text .= '<br />' . (tep_not_null($this->info[$value]['products']['products_quantity'][$products_key]) ? $this->info[$value]['products']['products_quantity'][$products_key] : '0');
                        $actual_quantity_text .= '<br />' . (tep_not_null($this->info[$value]['products']['products_actual_quantity'][$products_key]) ? $this->info[$value]['products']['products_actual_quantity'][$products_key] : '0');
                    }
                    //For G2G region sales
                    if(tep_not_null($this->info[$value]['products']['region'])){
                        foreach($this->info[$value]['products']['region'] as $g2gregion_id => $g2gRegion){
                            $display_name .= '<br />' . str_repeat('&nbsp;', $this->info[$value]['level'] * 3 + 6) . '&raquo;&nbsp;<b><i>' . $g2gRegion . '</i></b>';
                        }
                    }
                }
            }

            $product_info_table = '<tbody id="row_' . $this->info[$value]['id'] . '" class="' . ( $this->info[$value]['level'] < $level ? "show" : "hide") . '">
                                   <tr class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')"> 
                                   <td class="reportRecords">&nbsp;</td>
                                   <td class="reportRecords" valign="top" nowrap>' . $display_name . '</td>' .
                    (in_array('available_qty', $this->prod_qty_type) ? '<td width="5%" align="right" valign="top" class="reportRecords" valign="center" nowrap>' . $available_quantity_text . '</td>' : '') .
                    (in_array('actual_qty', $this->prod_qty_type) ? '<td width="5%" align="right" valign="top" class="reportRecords" valign="center" nowrap>' . $actual_quantity_text . '</td>' : '');


            /* -- quantity sold and subtotal -- */
            for ($i = ($this->size - 1); $i >= 0; $i--) {
                /* -- quantity sold -- */
                $quantity_sold = '';
                $quantity_sold_total = '';

                if (isset($this->info[$value]['products']['qty_sold'][$this->startDates[$i]])) {
                    foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                        $sub_quantity_sold = '';

                        if (isset($this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key])) {
                            /* -- bundle product -- */
                            if (is_array($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key])) {
                                foreach ($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key] as $sub_key => $sub_value) {
                                    $quantity_sold_total += $sub_value;
                                    $sub_quantity_sold .= '<br />' . $sub_value;
                                }
                                $quantity_sold .= '<br /><b><em>' . (tep_not_null($this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key]) ? $this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key] : '0') . '</em></b>' . $sub_quantity_sold;
                            } else {
                                $quantity_sold_total += $this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key];
                                $quantity_sold .= '<br />' . (tep_not_null($this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key]) ? $this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key] : '0');
                            }
                        } else {
                            if ($this->info[$value]['products']['products_bundle'][$products_key] == 'yes') {
                                $quantity_sold .= '<br /><em><b>0</b></em>';
                                if (is_array($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                                    foreach ($this->info[$value]['products']['products_bundle_name'][$products_key] as $sub_key => $sub_value) {
                                        $quantity_sold .= '<br />0';
                                    }
                                }
                            } else {
                                $quantity_sold .= '<br />0';
                            }
                        }
                    }
                    //For G2G region sales
                    if(tep_not_null($this->info[$value]['products']['region'])){
                        foreach($this->info[$value]['products']['region'] as $g2gregion_id => $g2gRegion){
                            $quantity_sold .= '<br /><b><i>' . (tep_not_null($this->info[$value]['products']['region_qty_sold'][$this->startDates[$i]][$g2gregion_id]) ? $this->info[$value]['products']['region_qty_sold'][$this->startDates[$i]][$g2gregion_id] : '0') . '</i></b>';
                        }
                    }

                } else {
                    if (is_array($this->info[$value]['products']['products_name'])) {
                        foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                            if ($this->info[$value]['products']['products_bundle'][$products_key] == 'yes') {
                                if (is_array($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                                    $quantity_sold .= '<br /><em><b>0</b></em>';
                                    foreach ($this->info[$value]['products']['products_bundle_name'][$products_key] as $sub_key => $sub_value) {
                                        $quantity_sold .= '<br />0';
                                    }
                                } else {
                                    $quantity_sold .= '<br /><em><b>0</b></em>';
                                }
                            } else {
                                $quantity_sold .= '<br />0';
                            }
                        }
                    }
                    //For G2G region sales
                    if(tep_not_null($this->info[$value]['products']['region'])){
                        foreach($this->info[$value]['products']['region'] as $g2gregion_id => $g2gRegion){
                            $quantity_sold .= '<br />0';
                        }
                    }
                }

                $this->total_amount[$this->startDates[$i]] += $quantity_sold_total;


                /* -- subtotal sales comparison -- */
                $color_style = '';
                unset($next);

                $current = (isset($this->info[$value]['subtotal'][$this->startDates[$i]]) ? $this->info[$value]['subtotal'][$this->startDates[$i]] : 0);
                if (($i - 1) >= 0) {
                    $next = (isset($this->info[$value]['subtotal'][$this->startDates[($i - 1)]]) ? $this->info[$value]['subtotal'][$this->startDates[($i - 1)]] : 0);
                }

                if (isset($next)) {
                    if ($current < $next) {
                        $color_style = 'redIndicator';
                    } else if ($current > $next) {
                        $color_style = 'greenIndicator';
                    }
                }

                $subtotal_text = '<span class="' . $color_style . '">' . $currencies->format($current) . '</span>';

                if (is_array($this->info[$value]['products']['products_name'])) {
                    foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                        if (isset($this->info[$value]['products']['bundle_sales_amt'][$this->startDates[$i]][$products_key])) {
                            /* -- bundle product subtotal -- */
                            if (isset($this->info[$value]['products']['bundle_sales_amt'][$this->startDates[$i]][$products_key])) {
                                $subtotal_text .= '<br />' . $currencies->format($this->info[$value]['products']['bundle_sales_amt'][$this->startDates[$i]][$products_key]);
                            } else {
                                $subtotal_text .= '<br />' . $currencies->format(0);
                            }
                        } else {
                            $subtotal_text .= '<br />' . $currencies->format(0);
                        }

                        if (isset($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                            $subtotal_text .= str_repeat('<br />', count($this->info[$value]['products']['products_bundle_name'][$products_key]));
                        }
                        //For G2G region sales
                        if(tep_not_null($this->info[$value]['products']['region'])){
                            foreach($this->info[$value]['products']['region'] as $g2gregion_id => $g2gRegion){
                                $subtotal_text .= '<br /><b><i>' . (tep_not_null($this->info[$value]['products']['region_bundle_sales_amt'][$this->startDates[$i]][$g2gregion_id]) ? $currencies->format($this->info[$value]['products']['region_bundle_sales_amt'][$this->startDates[$i]][$g2gregion_id]) : '0') . '</i></b>';
                            }
                        }
                    }
                }

                $product_info_table .= '<td class="reportRecords" align="right" valign="top" nowrap><b>' . $quantity_sold_total . '</b>' . $quantity_sold . '</td>';
                $product_info_table .= '<td class="reportRecords" align="right" valign="top" nowrap>' . $subtotal_text . '</td>';
            }

            $product_info_table .= '</tr></tbody>';
            print $product_info_table;

            if (isset($this->parantChild[$value]))
                $this->call_array_child($currencies, $value, $level);
        }
    }

    /* -- displayCatTree for mode 7: Category (Stock + Sales) -- */

    function categoryExportCatTree($level, $currencies, $fp) {
        if (tep_not_null($_SESSION['export_array_child_parent_name']))
            unset($_SESSION['export_array_child_parent_name']);

        $this->export_array_child($currencies, key($this->parantChild), $level, $fp);

        /* -- total quantity sold -- */
        fwrite($fp, str_repeat('"",', (2 + count($this->prod_qty_type))));

        $content = '';
        for ($i = ($this->size - 1); $i >= 0; $i--) {
            $content .= '"' . (number_format($this->total_amount[$this->startDates[$i]], 0, '.', '')) . '","",';
        }
        fwrite($fp, $content . "\n");
        unset($_SESSION['export_array_child_parent_name']);
    }

    /* -- export cat_array child for categorydisplayCatTree -- */

    function export_array_child($currencies, $key, $level, $fp) {
        foreach ($this->parantChild[$key] as $key_idx => $value) {
            $quantity_sold_total = 0;

            # category
            $category_name = trim(str_replace('##', '', strip_tags($this->info[$value]['name'])));

            # parent name
            if (!eregi_dep(TEXT_TOP, $category_name))
                $_SESSION['export_array_child_parent_name'][$this->info[$value]['level']] = $category_name;

            if (tep_not_null($_SESSION['export_array_child_parent_name'])) {
                $parent = array();

                for ($i = 0; $this->info[$value]['level'] >= $i; $i++) {
                    if (tep_not_null($_SESSION['export_array_child_parent_name'][$i]) && ($_SESSION['export_array_child_parent_name'][$i] != $category_name)) {
                        $parent[] = $_SESSION['export_array_child_parent_name'][$i];
                    }
                }

                if (tep_not_null($parent))
                    $parent_name = implode(' > ', $parent);
            }

            $category_name = (tep_not_null($parent_name) ? $parent_name . ' > ' . $category_name : $category_name);
            $category = '"' . $category_name . '",'; # categories
            $category .= '"C",'; # type
            $category .= (in_array('available_qty', $this->prod_qty_type) ? '"",' : ''); # available quantity
            $category .= (in_array('actual_qty', $this->prod_qty_type) ? '"",' : '');  # actual quantity

            /* -- quantity sold -- */
            if (isset($this->info[$value]['products']['products_name'])) {
                for ($i = ($this->size - 1); $i >= 0; $i--) {
                    foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                        if (!is_array($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key])) {
                            $quantity_sold_total += $this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key];
                        } else {
                            foreach ($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key] as $sub_key => $sub_value) {
                                $quantity_sold_total += $sub_value;
                            }
                        }
                    }

                    $this->total_amount[$this->startDates[$i]] += $quantity_sold_total;
                    $category .= '"' . $quantity_sold_total . '",';
                    $category .= '"' . ($currencies->format(isset($this->info[$value]['subtotal'][$this->startDates[$i]]) ? $this->info[$value]['subtotal'][$this->startDates[$i]] : '0')) . '",';

                    $quantity_sold_total = 0;
                }
            } else {
                for ($i = ($this->size - 1); $i >= 0; $i--) {
                    $category .= '"",';
                    $category .= '"' . ($currencies->format(isset($this->info[$value]['subtotal'][$this->startDates[$i]]) ? $this->info[$value]['subtotal'][$this->startDates[$i]] : '0')) . '",';
                }
            }
            fwrite($fp, $category . "\n");


            /* -- product name, available quantity, actual quantity -- */
            if (isset($this->info[$value]['products']['products_name'])) {
                # product
                foreach ($this->info[$value]['products']['products_name'] as $products_key => $products_name) {
                    $product_name = $category_name . ' > ' . (tep_not_null($products_name) ? strip_tags($products_name) : 'Unknown');
                    $child_name = $product_name;
                    $product = '"' . $product_name . '",'; # categories
                    $product .= '"P",';      # type
                    $product .= '"' . $products_key . '",'; # product id

                    if (isset($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                        $product .= (in_array('available_qty', $this->prod_qty_type) ? '"",' : ''); # available quantity
                        $product .= (in_array('actual_qty', $this->prod_qty_type) ? '"",' : ''); # actual quantity
                    } else {
                        $product .= (in_array('available_qty', $this->prod_qty_type) ? '"' . $this->info[$value]['products']['products_quantity'][$products_key] . '",' : '');  # available quantity
                        $product .= (in_array('actual_qty', $this->prod_qty_type) ? '"' . $this->info[$value]['products']['products_actual_quantity'][$products_key] . '",' : ''); # actual quantity
                    }

                    /* -- quantity sold -- */
                    for ($i = ($this->size - 1); $i >= 0; $i--) {
                        $product .= '"' . (isset($this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key]) ? $this->info[$value]['products']['qty_sold'][$this->startDates[$i]][$products_key] : '0') . '",';
                        $product .= '"' . ($currencies->format(isset($this->info[$value]['products']['bundle_sales_amt'][$this->startDates[$i]][$products_key]) ? $this->info[$value]['products']['bundle_sales_amt'][$this->startDates[$i]][$products_key] : '0')) . '",';
                    }
                    fwrite($fp, $product . "\n");


                    # bundle product
                    if (isset($this->info[$value]['products']['products_bundle_name'][$products_key])) {
                        foreach ($this->info[$value]['products']['products_bundle_name'][$products_key] as $sub_key => $sub_value) {
                            $bundle_name = str_repeat(' ', $this->info[$value]['level'] * 3 + 3) . $child_name . ' > ' . strip_tags($sub_value);
                            $bundle = '"' . $bundle_name . '",'; # categories
                            $bundle .= '"P",'; // type
                            $bundle .= '"' . $sub_key . '",'; // Product ID
                            $bundle .= (in_array('available_qty', $this->prod_qty_type) ? '"' . $this->info[$value]['products']['products_quantity'][$products_key][$sub_key] . '",' : '');  # available quantity
                            $bundle .= (in_array('actual_qty', $this->prod_qty_type) ? '"' . $this->info[$value]['products']['products_actual_quantity'][$products_key][$sub_key] . '",' : ''); # actual quantity

                            /* -- quantity sold -- */
                            for ($i = ($this->size - 1); $i >= 0; $i--) {
                                if (is_array($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key])) {
                                    foreach ($this->info[$value]['sub_products']['qty_sold'][$this->startDates[$i]][$products_key] as $bundle_sub_key => $bundle_sub_value) {
                                        $bundle .= '"' . (tep_not_null($bundle_sub_value) ? $bundle_sub_value : '0') . '","",';
                                    }
                                } else {
                                    $bundle .= '"","",';
                                }
                            }
                        }
                        fwrite($fp, $bundle . "\n");
                    }
                }
            }

            if (isset($this->parantChild[$value])) {
                $this->export_array_child($currencies, $value, $level, $fp);
            }
        }
    }
}

?>