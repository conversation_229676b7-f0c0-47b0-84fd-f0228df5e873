<?
/*************************************
	SEO URL Redirection 
*************************************/
class seo_url {
	var $get_data_array = array();
	var $parameters = '';
	var $if_seo_url = '';
	var $redirect_to_index = '';
	var $server_type = '';
	
	function seo_url($page = '', $parameters = '') {
		if (tep_not_null($page) && tep_not_null($parameters)) {
			$this->if_seo_url = false;
		    $this->redirect_to_index = 1; //redirect to index page
		    
		    if (ENABLE_SSL_CATALOG == 'true') {
	        	$this->server_type = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG;
	      	} else {
	        	$this->server_type = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
	      	}
		    
		    if (tep_not_null($page)) {
				$key_value_pair = array();
		    	$form_array = array();
		    	$get_array = array();
		    	$cat_name_array = array();
				
		    	$parameters = ((substr($parameters, -1, 1) == "&") ? substr($parameters, 0, -1) : $parameters);
		    	$query_string_array = split_dep("[&=]", $parameters); //put parameter in array
		    	for($i=0; $i < count($query_string_array) -1; $i+=2) {
		    		$key_value_pair[$query_string_array[$i]] = $query_string_array[$i+1];
		    		//if ($query_string_array[$i] != "cPath") {
		    			$get_array[$query_string_array[$i]] = $query_string_array[$i+1];
		    		//}
		    	}
				
				if(isset($key_value_pair['cPath'])) {
					if (tep_not_null($key_value_pair['cPath']) && $key_value_pair['cPath'] > 0) {
						$cat_id_arary = explode("_", $key_value_pair['cPath']); //category id
		
						for($i=0; $i < sizeof($cat_id_arary); $i++) {
							$cat_name_select_sql = "SELECT categories_url_alias
													FROM ". TABLE_CATEGORIES ."
													WHERE categories_id='".$cat_id_arary[$i]."' ";
		
							$cat_name_result_sql = tep_db_query($cat_name_select_sql);
							$cat_name_row = tep_db_fetch_array($cat_name_result_sql);
							$cat_name_array[] = $cat_name_row['categories_url_alias']; //category url alias
		
						}
						$cpath = str_replace('_', '-', $key_value_pair['cPath']); // change '_' to '-'
					}
				}
				
				
				switch ($page) {
					case FILENAME_DEFAULT:
					case FILENAME_CATALOG_CUSTOM_PRODUCT_INFO:
					case FILENAME_CATALOG_PRODUCT_INFO:
						$do_product_name = true;
						$get_data_array = array();
						
						if (count($get_array)) { //GET data
							foreach ($get_array as $key => $value) {
								if ($page != FILENAME_DEFAULT) {
									if ($key != "products_id" && $key != 'cPath') {
										$get_data_array[] = $key."=".$value;
									}
								} else {
									if ($key != 'cPath') {
										$get_data_array[] = $key."=".$value;
									}
								}
							}
						}
						
						if ($page == FILENAME_DEFAULT) { //index
							$divider = FILENAME_URL_INDEX; //divider
							$do_product_name = false;
						} else if ($page == FILENAME_CATALOG_PRODUCT_INFO) { //product info
							$divider = FILENAME_URL_PRODUCT_INFO; //divider
						} else if($page == FILENAME_CATALOG_CUSTOM_PRODUCT_INFO) { //custom product info
							$divider = FILENAME_URL_CUSTOM_PRODUCT_INFO; //divider
						}
						
						if ($do_product_name == true) {
							$pro_name_select_sql = "SELECT p.products_url_alias, p2c.categories_id
													FROM ". TABLE_PRODUCTS ." AS p
													INNER JOIN ". TABLE_PRODUCTS_TO_CATEGORIES ." AS p2c
														ON (p.products_id=p2c.products_id)
													WHERE p.products_id='". $key_value_pair['products_id'] ."'";
							
							$pro_name_result_sql = tep_db_query($pro_name_select_sql);
							$pro_name_row = tep_db_fetch_array($pro_name_result_sql);
							$pro_name_array[] =$pro_name_row['products_url_alias']; //products url alias
							
							tep_get_parent_categories($parent_categories_array, $pro_name_row['categories_id']);
							$pro_cat_id_array = (is_array($parent_categories_array) ? array_reverse($parent_categories_array) : array());
	                        $pro_cat_id_array = ((count($pro_cat_id_array)) ? array_merge($pro_cat_id_array, array($pro_name_row['categories_id'])) : array($pro_name_row['categories_id']));
	                        $pro_cat_name_array = array();
	                        
							for($i=0; $i < sizeof($pro_cat_id_array); $i++) {
								$pro_cat_name_select_sql = "SELECT categories_url_alias
															FROM ". TABLE_CATEGORIES ."
															WHERE categories_id='". $pro_cat_id_array[$i] ."' ";
								$pro_cat_name_result_sql = tep_db_query($pro_cat_name_select_sql);
								
								$pro_cat_name_row = tep_db_fetch_array($pro_cat_name_result_sql);
								$pro_cat_name_array[] = $pro_cat_name_row['categories_url_alias']; //category url alias
							}
							
							if ($key_value_pair['products_id']) {
								$new_path = array_merge($pro_cat_name_array, $pro_name_array);
								$this->parameters = implode("/", $new_path) . "-". $divider . "-" . $key_value_pair['products_id'];
								$this->redirect_to_index = 0;
							}
							
							$this->if_seo_url = true;
						} else {
							if ($key_value_pair['cPath']) {
								$this->parameters = ((count($cat_name_array) > 1) ? implode("/", $cat_name_array) : $cat_name_array[0]) . "-". $divider . "-" . $cpath;							
								$this->redirect_to_index = 0;
								$this->if_seo_url = true;
							}
						}
						
						if (count($get_data_array))	$this->get_data_array = implode("&", $get_data_array);
						
						break;
					case FILENAME_CATALOG_NEWS:
						$do_news_parameters = false;
						if (isset($key_value_pair['cPath'])) {
							$cpath = str_replace('_', '-', $key_value_pair['cPath']); // change '_' to '-'
							$divider = FILENAME_URL_NEWS_ALL; //divider
							
							if ($key_value_pair['cPath'] == NULL) {
								unset($cpath);
								$cpath = "0"; //if empty, assgin cPath to '0'
							}
							$do_news_parameters = true;
						} else {
							$divider = FILENAME_URL_NEWS; //divider
						}
						
						$this_site_id = 0;
						if (defined('SITE_ID')) {
							$this_site_id = SITE_ID;
						}
						$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
						
						$news_id_where_str = (tep_not_null($key_value_pair['news_id'])) ? "ln.news_id='". $key_value_pair['news_id'] ."'" : " 1 ";
						$news_name_select_sql = "	SELECT ln.latest_news_url_alias, lngd.news_groups_name FROM ". TABLE_LATEST_NEWS ." AS ln
													INNER JOIN ". TABLE_LATEST_NEWS_GROUPS_DESCRIPTION ." AS lngd
														ON (ln.news_groups_id=lngd.news_groups_id)
													WHERE ln.language='1'
														AND $news_display_sites_where_str 
														AND lngd.news_groups_id='". $key_value_pair['news_type'] ."'
														AND lngd.language_id = '1'
														AND ". $news_id_where_str;
						$news_name_result_sql = tep_db_query($news_name_select_sql);
						$news_name_row = tep_db_fetch_array($news_name_result_sql);
						$info_news_group_name_array[] = $news_name_row['news_groups_name']; //news group name
						
						if ($do_news_parameters == false) {
							$info_url_alias = $news_name_row['latest_news_url_alias']; //news url alias
						}
						
						if ($key_value_pair['news_type']) {
							$new_path = array_merge($cat_name_array, array($info_url_alias));
							
							$this->parameters = implode("/", $new_path) . (($new_path) ? '-' : '') . $divider;
							$this->parameters .= (($do_news_parameters == true) ? $cpath : '') . (($key_value_pair['news_id']) ? "-". $key_value_pair['news_id'] : '') ."-".$key_value_pair['news_type'] . (($key_value_pair['page']) ? "-". $key_value_pair['page'] : (tep_not_null($cpath) ? '-1' : ''));
							$this->redirect_to_index = 0;
						}
						$this->if_seo_url = true;
						
						break;
					case FILENAME_CATALOG_INFOLINKS:
						$divider = FILENAME_URL_INFO; //divider
						$content_id_where_str = (tep_not_null($key_value_pair['content_id'])) ? "ic.infolinks_contents_id='". $key_value_pair['content_id'] ."'" : ' 1 ';
						
						$inforlinks_name_select_sql = " SELECT i.infolinks_url_alias, ic.infolinks_contents_page
														FROM ". TABLE_INFOLINKS ." AS i
														INNER JOIN ". TABLE_INFOLINKS_CONTENTS ." AS ic
															ON (i.infolinks_id=ic.infolinks_id)
														WHERE i.infolinks_id='". $key_value_pair['id'] ."'
															AND ". $content_id_where_str;
						
						$inforlinks_name_restul_sql = tep_db_query($inforlinks_name_select_sql);
						$inforlinks_name_row = tep_db_fetch_array($inforlinks_name_restul_sql);
						
						$infolinks_contents_page = $inforlinks_name_row['infolinks_contents_page']; //infolinks page no.
						$infolinks_url_alias = $inforlinks_name_row['infolinks_url_alias']; //infolinks url alias
						
						if ($key_value_pair['id']) {
							$this->parameters = $infolinks_url_alias ."-". $divider ."-". $key_value_pair['id'] . (($key_value_pair['content_id'])? "-". $key_value_pair['content_id'] : '');
							$this->redirect_to_index = 0;
						}
						
						$this->if_seo_url = true;
						
						break;
					default:
						
						$this->parameters = $parameters;
						break;
				}
			}
		}
		
	}
	
	// Translate special characters to actual characters
	function tep_translate_special_character($character){ 
	$special_char = array (	'a' => array(chr(192), //?
										chr(193), //?
										chr(194), //?
										chr(195), //?
										chr(196), //?
										chr(197), //?
										chr(198), //?
										chr(224), //?
										chr(225), //?
										chr(226), //?
										chr(227), //?
										chr(228), //?
										chr(229), //?
										chr(230) //?
										),
							'b' => array(chr(223) //?
										),
							'c' => array(chr(199), //?
										chr(231) //?
										),
							'e' => array(chr(200), //?
										chr(201), //?
										chr(202), //?
										chr(203), //?
										chr(232), //?
										chr(233), //?
										chr(234), //?
										chr(235) //?
										),
							'i' => array(chr(204), //?
										chr(205), //?
										chr(206), //?
										chr(207), //?
										chr(236), //?
										chr(237), //?
										chr(238), //?
										chr(239) //?
										),
							'n' => array(chr(209), //?
										chr(241) //?
										),
							'o' => array(chr(210), //?
										chr(211), //?
										chr(212), //?
										chr(213), //?
										chr(214), //?
										chr(216), //?
										chr(242), //?
										chr(243), //?
										chr(244), //?
										chr(245), //?
										chr(246), //?
										chr(248) //?
										),
							's' => array(chr(138), //?
										chr(154) //?
										),
							'u' => array(chr(217), //?
										chr(218), //?
										chr(219), //?
										chr(220), //?
										chr(249), //?
										chr(250), //?
										chr(251), //?
										chr(252) //?
										),
							'y' => array(chr(159), //?
										chr(221), //?
										chr(255) //�
										),
							'z' => array(chr(142), //?
										chr(158) //?
										)
							);
	
		foreach ($special_char as $key => $value) {
			foreach ($value as $key2 => $value2) {
				$character = str_replace($value2, $key, $character);
			}
		}
		return $character;
	}
	
	//validate special characters
	function tep_validate_special_characters($string) {	
		if (!preg_match('/^[a-zA-Z0-9]{1,}$/', $string)) {
			$space = array("/", "_", "'", "~", ",", ":", " ", "-");
			$string = str_replace($space, '-', $string); // replace to space
			preg_match_all('/([a-zA-Z0-9-])/i', $string, $matches);
			$string = strtolower(implode('', $matches[1]));
			
			while (strstr($string, '--')) {
				$string = str_replace('--', '-', $string); // '--' -> '-'
			}
			$string = ((substr($string, 0, 1) == "-") ? substr($string, 1) : $string); //trim "-" at first character
			$string = ((substr($string, -1, 1) == "-") ? substr($string, 0, -1) : $string); //trim "-" at last character
		}
		
		return $string;
	}
}

?>