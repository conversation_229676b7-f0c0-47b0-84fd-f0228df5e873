<?
/*
		$Id: seo_functions.php,v 1.5 2009/05/04 10:32:19 weesiong Exp $
	
	osCommerce, Open Source E-Commerce Solutions
	http://www.oscommerce.com
	
	Copyright (c) 2003 osCommerce
	
	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: seo_meta_tag
class SEO {
	var $baseurl, $robot;
	
	// class constructor
	function SEO() 
	{
		$this->baseurl = array('1' =>	"index.php",
								'2' =>	"product_info.php",
								'3' =>	"custom_product_info.php",
								'4' =>	"content.php",
								'5' =>	"cms_content.php",
								'6' =>	"news.php");
		
		$this->robot = array(	'1' => "Index, Follow",
								'2' => "Index, Not Follow",
								'3' => "No Index, Follow",
								'4' => "No Index, Not Follow"
								);
	}
	
	// class methods
	function addEntry($id="")
	{
		$seo_meta_baseurl = $this->baseurl[$_POST['seo_meta_page_type']];

		$query_string_array = array();

		for ($i=1 ; $i <= 10 ; $i++) {
			$temp_key = urlencode($_POST['seo_meta_query_string_key_'.$i]);
			$temp_value = urlencode($_POST['seo_meta_query_string_value_'.$i]);
			
			if (tep_not_null($temp_key)) {
				$query_string_array[] = "$temp_key=$temp_value";
			}
		}
		
		sort($query_string_array);
		
		$seo_meta_query_string = implode("&", $query_string_array);
		
		if ($_POST['seo_meta_title_overwrite'] == "1")
			$seo_meta_title_overwrite = "1";
		else
			$seo_meta_title_overwrite = "0";

		if ($_POST['seo_meta_description_overwrite'] == "1")
			$seo_meta_description_overwrite = "1";
		else
			$seo_meta_description_overwrite = "0";

		if ($_POST['seo_meta_keywords_overwrite'] == "1")
			$seo_meta_keywords_overwrite = "1";
		else
			$seo_meta_keywords_overwrite = "0";

		// 1 - Index, Follow
		// 2 - Index, Not Follow
		// 3 - No Index, Follow
		// 4 - No Index, Not Follow

		if ($_POST['seo_meta_index'] == 1 && $_POST['seo_meta_follow'] == 1)
			$seo_meta_robots = 1;
		else if ($_POST['seo_meta_index'] == 1 && $_POST['seo_meta_follow'] == 0)
			$seo_meta_robots = 2;
		else if ($_POST['seo_meta_index'] == 0 && $_POST['seo_meta_follow'] == 1)
			$seo_meta_robots = 3;
		else if ($_POST['seo_meta_index'] == 0 && $_POST['seo_meta_follow'] == 0)
			$seo_meta_robots = 4;

		$seo_data_array = array('language_id' => (int)$_POST['language_id'],
								'seo_meta_page_type' => ($_POST['seo_meta_page_type']) ? $_POST['seo_meta_page_type'] : '',
								'seo_meta_baseurl' => ($seo_meta_baseurl) ? tep_db_prepare_input($seo_meta_baseurl) : '',
								'seo_meta_query_string' => ($seo_meta_query_string) ? tep_db_prepare_input($seo_meta_query_string) : '',
								'seo_meta_title' => ($_POST['seo_meta_title']) ? tep_db_prepare_input($_POST['seo_meta_title']) : '',
								'seo_meta_description' => ($_POST['seo_meta_description']) ? tep_db_prepare_input($_POST['seo_meta_description']) : '',
								'seo_meta_keywords' => ($_POST['seo_meta_keywords']) ? tep_db_prepare_input($_POST['seo_meta_keywords']) : '',
								'seo_meta_title_overwrite' => $seo_meta_title_overwrite,
								'seo_meta_description_overwrite' => $seo_meta_description_overwrite,
								'seo_meta_keywords_overwrite' => $seo_meta_keywords_overwrite,
								'seo_meta_robots' => $seo_meta_robots);

		if ($id) {
			tep_db_perform(TABLE_SEO_META_TAG, $seo_data_array, 'update', "seo_meta_id='".$id."'");
		}
		else {
			tep_db_perform(TABLE_SEO_META_TAG, $seo_data_array);
		}
	}
	
	function deleteEntry($id="")
	{
		if ($id) {
			$sql = "DELETE 
					FROM ".TABLE_SEO_META_TAG." 
					WHERE seo_meta_id = '".tep_db_input($id)."'";
			$result_sql = tep_db_query($sql);
			
			return ($result_sql == 1 ? "success" : '');
		}
		else {
			return '';
		}
	}

	function export($page_type='', $search_keyword='')
	{
		$search_keyword = tep_db_prepare_input($search_keyword);

		if ($page_type) {
			$search_where_statement = ' 1 ';
			if ($search_keyword) {
				$search_where_statement = " (seo_meta_query_string LIKE '%".tep_db_input($search_keyword)."%' OR seo_meta_title LIKE '%".tep_db_input($search_keyword)."%' OR seo_meta_description LIKE '%".tep_db_input($search_keyword)."%')";
			}

			$listing_html = '';
	
			$listing_obj = array();
	
			$seo_meta_select_sql = "SELECT seo_meta_id, seo_meta_query_string, seo_meta_title, seo_meta_description, seo_meta_keywords,seo_meta_robots 
									FROM ".TABLE_SEO_META_TAG." 
									WHERE seo_meta_page_type='".tep_db_input($page_type)."' 
										AND " . $search_where_statement . " 
									ORDER BY seo_meta_title ASC";
			$seo_meta_result_sql = tep_db_query($seo_meta_select_sql);
	
			while ($seo_meta_rows = tep_db_fetch_array($seo_meta_result_sql)) {
				$print_robot = $this->robot[$seo_meta_rows['seo_meta_robots']];
				
				if ($page_type == "1") {
					$query_string_array = explode('&', $seo_meta_rows['seo_meta_query_string']);
					foreach ($query_string_array as $query_string_row) {
						list($query_string_key,$query_string_value) = explode('=', $query_string_row);
						
						if ($query_string_key == "cPath") {
							$cPath = $query_string_value;
							
							$cPath_array = array();
							$cPath_array = explode('_', $cPath);
							
							$category_string = "";
							
							foreach ($cPath_array as $cPath_entry) {
								$categories_sql = "	SELECT cd.categories_name 
													FROM " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd 
													WHERE c.categories_id = cd.categories_id
														AND c.categories_id = '$cPath_entry'
														AND c.categories_status = '1'";
								$categories_result = tep_db_query($categories_sql);
								$categories_result_rows = tep_db_fetch_array($categories_result);
								
								if ($category_string)
									$category_string .= " ".$categories_result_rows['categories_name'];
								else
									$category_string .= $categories_result_rows['categories_name'];
							}
						}
					}
				}

       			$export_csv_data .= '"' . str_replace('"','""',$seo_meta_rows['seo_meta_query_string']) . '","' . str_replace('"','""', $category_string) . '","' . str_replace('"', '""', $seo_meta_rows['seo_meta_title']) . '","' . str_replace('"', '""', $seo_meta_rows['seo_meta_description']) . '","' . str_replace('"', '""', $seo_meta_rows['seo_meta_keywords']) . '","' . str_replace('"', '""', $print_robot) . "\"\n";
			}
			
			if (tep_not_null($export_csv_data)) {
				$export_csv_data = '"Query String","Categories Name","Meta Title","Meta Description","Meta Keyword","Robot"'."\n" . $export_csv_data;

				$filename = 'seo_meta_tag_'.date('YmdHis').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
}
?>