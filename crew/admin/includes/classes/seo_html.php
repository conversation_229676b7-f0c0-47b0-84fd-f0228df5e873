<?php
/*
	$Id: seo_html.php,v 1.6 2011/07/20 09:40:04 weichen Exp $
	
	osCommerce, Open Source E-Commerce Solutions
	http://www.oscommerce.com
	
	Copyright (c) 2003 osCommerce
	
	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: seo_meta_tag


class SEO_HTML {
	// class constructor
	function SEO_HTML()
	{

	}
	
	function seoListing() {

		if ($_GET['search_keyword']) {
			$search_keyword = tep_db_prepare_input($_GET['search_keyword']);
		}

		if ($_GET['page_type']) {
			$page_type = $_GET['page_type'];
			$_SESSION['seo_page_type'] = $page_type; 
		}
		else if(isset($_SESSION['seo_page_type'])) {
			$page_type = $_SESSION['seo_page_type'];
		}

		$search_where_statement = ' 1 ';
		if ($search_keyword) {
			$search_where_statement = " (seo_meta_query_string LIKE '%".tep_db_input($search_keyword)."%' OR seo_meta_title LIKE '%".tep_db_input($search_keyword)."%' OR seo_meta_description LIKE '%".tep_db_input($search_keyword)."%')";
		}

		$listing_html = '';

		$listing_obj = array();

		$seo_meta_select_sql = "SELECT seo.seo_meta_id, seo.seo_meta_query_string, seo.seo_meta_title, seo.seo_meta_description, lang.name
								FROM ".TABLE_SEO_META_TAG." seo 
								LEFT JOIN ".TABLE_LANGUAGES." lang 
									ON seo.language_id=lang.languages_id 
								WHERE seo.seo_meta_page_type='".tep_db_input($page_type)."' 
									AND " . $search_where_statement . " 
								ORDER BY seo_meta_title ASC";
		
		if ($show_records != "ALL") {
			$template_split_object = new splitPageResults($_GET['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $seo_meta_select_sql, $seo_meta_select_sql_numrows, true);
		}

		$seo_meta_result_sql = tep_db_query($seo_meta_select_sql);

		while ($seo_meta_rows = tep_db_fetch_array($seo_meta_result_sql)) {
			$listing_obj[] = array( 'id' => $seo_meta_rows['seo_meta_id'],
									'language' => $seo_meta_rows['name'],
									'title' => $seo_meta_rows['seo_meta_title'],
									'query_string' => $seo_meta_rows['seo_meta_query_string'],
									'description' => $seo_meta_rows['seo_meta_description']);
		}

		$page_type_options = array (
									array ('id' => '', 'text' => PULL_DOWN_DEFAULT),
									array ('id' => 1, 'text' => LIST_PAGE_TYPE_1),
									array ('id' => 2, 'text' => LIST_PAGE_TYPE_2),
									array ('id' => 3, 'text' => LIST_PAGE_TYPE_3),
									array ('id' => 4, 'text' => LIST_PAGE_TYPE_4),
									array ('id' => 5, 'text' => LIST_PAGE_TYPE_5),
									array ('id' => 6, 'text' => LIST_PAGE_TYPE_6)
								);
		
		ob_start();
?>
<script language="javascript">
<!--
jQuery.noConflict();

function deleteentry(s,t,id) {
	answer = confirm('Are you sure to delete '+ (trim_str(s) != '' ? "<" + s + "> " : '') + t + ' record?')
	if (answer !=0) { 
		jQuery.get("<?=tep_href_link(FILENAME_SEO_META_TAG, 'action=delete', 'NONSSL')?>&id="+id, function(data){
			if (data == "success")
	 			jQuery('#row-'+id).fadeOut('slow');
		});
	}
}

//-->
</script>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td align="left">[ <?php  ?><a href="<?=tep_href_link(FILENAME_SEO_META_TAG, 'selected_box=infolinks&action=add_form&page_type='.urlencode($page_type) , 'NONSSL')?>" ><?php echo LINK_ADD_META_TAG; ?></a> ]</td>
								<td align=center>
									<?=tep_draw_form('seo_search_form', '', '', 'get')?>
									<?=tep_draw_input_field('search_keyword', $search_keyword, ' id="search_keyword" size="20" ')?>
									<?=tep_submit_button(BUTTON_SEARCH, BUTTON_SEARCH, '', 'inputButton')?>
									<?=tep_draw_hidden_field('page_type', $page_type)?>
									</form>
								</td>
								<td class=main align="right">
									<?=tep_draw_form('seo_page_type_form', '' , '', 'get')?>
									<?php echo ENTRY_PAGE_TYPE; ?> : 
									<?=tep_draw_pull_down_menu("page_type", $page_type_options, ($page_type) ? $page_type : '', ' onchange="document.seo_page_type_form.submit()"')?>
									</form>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign="top">
						<table border="0" width="100%" cellspacing="1" cellpadding="2">
		 					<tr>
			 					<td class="reportBoxHeading"><?php echo BOX_LOCALIZATION_LANGUAGES; ?></td>
			 					<td class="reportBoxHeading"><?php echo TABLE_HEADING_QUERY_STRING; ?></td>
			 					<td class="reportBoxHeading" align=center><?php echo TABLE_HEADING_META_TITLE; ?></td>
								<td class="reportBoxHeading" align=center><?php echo TABLE_HEADING_META_DESCRIPTION; ?></td>
								<td class="reportBoxHeading" align=center><?php echo TABLE_HEADING_ACTION; ?></td>
		 					</tr>
<?php
$entryCount = 0;

foreach ($listing_obj as $rows) {
	$entryCount++;
	
	($entryCount % 2 == 0) ? $tr_class = "reportListingOdd" : $tr_class = "reportListingEven";
?>
							<tr id="row-<?php echo $rows['id']; ?>" class="<?php echo $tr_class; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?php echo $tr_class; ?>')" onclick="rowClicked(this, '<?php echo $tr_class; ?>')">
								<td class="reportRecords" valign="top"><?php echo $rows['language']; ?></td>
								<td class="reportRecords" valign="top"><?php echo $rows['query_string']; ?></td>
								<td class="reportRecords" valign="top" align=center>
									<?php echo tep_draw_input_field('seo_meta_title'.$rows['id'], $rows['title'], ' size="30"'); ?>
								</td>
								<td class="reportRecords" valign="top" align=center>
									<?php echo tep_draw_input_field('seo_meta_description'.$rows['id'], $rows['description'], ' size="30"'); ?>
								</td>
								<td class="reportRecords" valign="top" align=center>
									<a href="<?=tep_href_link(FILENAME_SEO_META_TAG, 'selected_box=infolinks&action=add_form&id=' . $rows['id'].'&page_type=' . $page_type, 'NONSSL')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
									<a href="javascript:void(deleteentry('Page','<?php echo htmlentities ($rows['title']); ?>','<?php echo htmlentities ($rows['id']); ?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
								</td>
							</tr>
<?php
}
?>
						</table>
			 			</td>
			 		</tr>
				</table>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_RECORDS, tep_db_num_rows($seo_meta_select_sql) > 0 ? "1" : "0", tep_db_num_rows($seo_meta_select_sql), tep_db_num_rows($seo_meta_select_sql)) : $template_split_object->display_count($seo_meta_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_GET['page'], TEXT_DISPLAY_NUMBER_OF_RECORDS)?></td>
						<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $template_split_object->display_links($seo_meta_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
					</tr>
				</table>
				<br>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td align="right">
							<?=tep_draw_form('seo_export', FILENAME_SEO_META_TAG, 'action=export', 'post')?>
							<?=tep_submit_button(BUTTON_SEO_META_EXPORT_CSV, BUTTON_SEO_META_EXPORT_CSV, '', 'inputButton')?>
							<?=tep_draw_hidden_field('page_type', $page_type)?>
							<?=tep_draw_hidden_field('search_keyword', $search_keyword)?>
							</form>
						</td>
					</tr>
				</table>

<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;

		return $listing_html;
	}

	function addForm($id = "") {
		$listing_html = '';

		if ($id) {
			$seo_meta_sql = "	SELECT *
								FROM ".TABLE_SEO_META_TAG." 
								$search_where_statement
								WHERE seo_meta_id = '".tep_db_input($id)."'";
			$seo_meta_result_sql = tep_db_query($seo_meta_sql);
	 		$seo_meta_rows = tep_db_fetch_array($seo_meta_result_sql);
		}

		if ($_GET['page_type']) {
			$seo_meta_rows['seo_meta_page_type'] = $_GET['page_type'];
		}
		
		$page_type_options = array (
			array ('id' => 1, 'text' => LIST_PAGE_TYPE_1),
			array ('id' => 2, 'text' => LIST_PAGE_TYPE_2),
			array ('id' => 3, 'text' => LIST_PAGE_TYPE_3),
			array ('id' => 4, 'text' => LIST_PAGE_TYPE_4),
			array ('id' => 5, 'text' => LIST_PAGE_TYPE_5),
			array ('id' => 6, 'text' => LIST_PAGE_TYPE_6)
		);
		
		$seo_languages_sql = "	SELECT languages_id, name 
								FROM ". TABLE_LANGUAGES ." 
								ORDER BY sort_order"; //$search_where_statement
		$seo_languages_result_sql = tep_db_query($seo_languages_sql);
 		while ($seo_languages_rows = tep_db_fetch_array($seo_languages_result_sql)) {
            $seo_languages_array[] = array('id' => $seo_languages_rows['languages_id'], 'text' => $seo_languages_rows['name']);
        }
		ob_start();
?>
<script language="javascript">
<!--
jQuery.noConflict();

function select_overwrite(textfield,checkbox) {
	if (jQuery("#"+checkbox).attr('checked') == true)
		jQuery("#"+textfield).removeAttr('disabled');
	else
		jQuery("#"+textfield).attr('disabled',true);
}

function check_form() {
	var error = 0;
	var error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

	if (error == 1) {
		alert(error_message);
		return false;
	}
	else {
		jQuery("#seo_meta_title").removeAttr('disabled');
		jQuery("#seo_meta_description").removeAttr('disabled');
		jQuery("#seo_meta_keywords").removeAttr('disabled');
		return true;
	}
}

function add_query_string_row () {
	var query_string_row_count = parseFloat(jQuery("#query_string_row_count").val()) + 1;

	jQuery("#query_string_row_"+query_string_row_count).show();
	jQuery("#query_string_row_count").val(query_string_row_count);
}

jQuery(document).ready(function() {
<?php
$query_sring_count = 0;
if ($seo_meta_rows['seo_meta_query_string']) {
	$query_string_array = array();
	$query_string_array = explode('&', $seo_meta_rows['seo_meta_query_string']);

	foreach ($query_string_array as $query_string_row) {
		$query_sring_count++;
		list($query_string_key,$query_string_value) = explode('=', $query_string_row);
?>
	jQuery("#seo_meta_query_string_key_<?=$query_sring_count ?>").val('<?=$query_string_key ?>');
	jQuery("#seo_meta_query_string_value_<?=$query_sring_count ?>").val('<?=$query_string_value ?>');
	jQuery("#query_string_row_<?=$query_sring_count ?>").show();
<?php
	}
}

for ($temp_count = 1 ; $temp_count <= 3 ; $temp_count++) {
	$query_sring_count++; 
?>
	jQuery("#query_string_row_<?=$query_sring_count ?>").show();
<?php
} 
?>
	jQuery("#query_string_row_count").val('<?=$query_sring_count ?>');
});

//-->
</script>
<?=tep_draw_form('seo_form', FILENAME_SEO_META_TAG , 'selected_box=infolinks&action=add', 'post', ' onSubmit="return check_form();"')?>
<table cellspacing="2" cellpadding="2" border="0">
<tbody><tr>
		<td>
			<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tbody><tr>
						<td class="pageHeading" valign="top"><?php if ($id != "") { echo HEADING_EDIT_META_TAG; } else { echo HEADING_ADD_META_TAG; } ?></td>
						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
					</tr>
			</tbody></table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" cellpadding="2" cellspacing="2">
				<tbody>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_PAGE_TYPE; ?></td>
					<td class="main">
						<?=tep_draw_pull_down_menu("seo_meta_page_type", $page_type_options, tep_not_null($seo_meta_rows['seo_meta_page_type']) ? $seo_meta_rows['seo_meta_page_type'] : '', ' id="seo_meta_page_type"')?>
					</td>
				</tr>
				<tr>
					<td class="main" valign=top><?=ENTRY_LANGUAGE ?></td>
					<td class="main">
						<?=tep_draw_pull_down_menu("language_id", $seo_languages_array, tep_not_null($seo_meta_rows['language_id']) ? $seo_meta_rows['language_id'] : '1', ' id="language_id"')?>
					</td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_QUERY_STRING; ?></td>
					<td class="main">
					<div id="query_string_div">
					<div>
						<div style="float: left; width:90px;"><b>Key</b></div>
						<div style="float: left"><b>Value</b></div>
					</div>
					<div style="clear: both;"></div>
		<?
				for ($fieldcount = 1 ; $fieldcount <= 10 ; $fieldcount++) {
		?>
						<div id="query_string_row_<?=$fieldcount ?>" style="padding: 3px 0px; display: none">
							<?=tep_draw_input_field('seo_meta_query_string_key_'.$fieldcount, '' , 'size="10" id="seo_meta_query_string_key_'.$fieldcount.'"')?> :
							<?=tep_draw_input_field('seo_meta_query_string_value_'.$fieldcount, '' , 'size="30" id="seo_meta_query_string_value_'.$fieldcount.'"')?>
						</div>
		<?
				}
		?>
					 </div>
					 <input type=hidden name="query_string_row_count" value="" id="query_string_row_count">
					 <b><a href="javascript:add_query_string_row()">[+] <?=TEXT_ADD_ROW ?></a></b>
					</td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1') ?></td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_META_TITLE; ?></td>
					<td class="main"><?=tep_draw_input_field('seo_meta_title', $seo_meta_rows['seo_meta_title'] , 'size="50" id="seo_meta_title"')?> <?=tep_draw_selection_field('seo_meta_title_overwrite', 'checkbox', '1', ($seo_meta_rows['seo_meta_title_overwrite'] == "1")? true : false, '' , ' id="seo_meta_title_overwrite" onclick="javacript:select_overwrite(\'seo_meta_title\',\'seo_meta_title_overwrite\')"')?> Overwrite</td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_META_DESCRIPTION; ?></td>
					<td class="main"><?=tep_draw_input_field('seo_meta_description', $seo_meta_rows['seo_meta_description'] , 'size="50" id="seo_meta_description"')?> <?=tep_draw_selection_field('seo_meta_description_overwrite', 'checkbox', '1', ($seo_meta_rows['seo_meta_description_overwrite'] == "1")? true : false, '' , ' id="seo_meta_description_overwrite" onclick="javacript:select_overwrite(\'seo_meta_description\',\'seo_meta_description_overwrite\')"')?> Overwrite</td>
				</tr>
				<tr>
					<td class="main" valign=top><?php echo ENTRY_KEYWORDS; ?></td>
					<td class="main"><?=tep_draw_input_field('seo_meta_keywords', $seo_meta_rows['seo_meta_keywords'] , 'size="50" id="seo_meta_keywords"')?> <?=tep_draw_selection_field('seo_meta_keywords_overwrite', 'checkbox', '1', ($seo_meta_rows['seo_meta_keywords_overwrite'] == "1")? true : false , '' , ' id="seo_meta_keywords_overwrite" onclick="javacript:select_overwrite(\'seo_meta_keywords\',\'seo_meta_keywords_overwrite\')"')?> Overwrite</td>
				</tr>

				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1') ?></td>
				</tr>
				<tr>
					<td class="main"><?php echo ENTRY_ROBOT; ?></td>
					<td class="main">
<?php
// 1 - Index, Follow
// 2 - Index, Not Follow
// 3 - No Index, Follow
// 4 - No Index, Not Follow

if ($seo_meta_rows['seo_meta_robots'] == "2") {
?>
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '1', true)?> <?php echo LIST_INDEX; ?> 
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '0', false)?> <?php echo LIST_NO_INDEX; ?><br>
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '1', false)?> <?php echo LIST_FOLLOW; ?> 
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '0', true)?> <?php echo LIST_NOT_FOLLOW; ?>
<?php
}
else if ($seo_meta_rows['seo_meta_robots'] == "3") {
?>
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '1', false)?> <?php echo LIST_INDEX; ?> 
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '0', true)?> <?php echo LIST_NO_INDEX; ?><br>
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '1', true)?> <?php echo LIST_FOLLOW; ?> 
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '0', false)?> <?php echo LIST_NOT_FOLLOW; ?>
<?php
}
else if ($seo_meta_rows['seo_meta_robots'] == "4") {
?>
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '1', false)?> <?php echo LIST_INDEX; ?> 
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '0', true)?> <?php echo LIST_NO_INDEX; ?><br>
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '1', false)?> <?php echo LIST_FOLLOW; ?> 
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '0', true)?> <?php echo LIST_NOT_FOLLOW; ?>
<?php
}
else {
?>
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '1', true)?> <?php echo LIST_INDEX; ?> 
					<?=tep_draw_selection_field('seo_meta_index', 'radio', '0', false)?> <?php echo LIST_NO_INDEX; ?><br>
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '1', true)?> <?php echo LIST_FOLLOW; ?> 
					<?=tep_draw_selection_field('seo_meta_follow', 'radio', '0', false)?> <?php echo LIST_NOT_FOLLOW; ?>
<?php
}

?>
						</td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1') ?></td>
				</tr>
			</tbody></table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
	</tr>
	<tr>
		<td class="main" align="right">
			<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?>
			<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, '?selected_box=infolinks', '', 'inputButton')?>
		</td>
	</tr>
</tbody>
<tbody><tr>
</table>
<script language="javascript">

<?php
if (empty($id)) {
?>
	jQuery("#seo_meta_title_overwrite").attr('checked',true);
	jQuery("#seo_meta_description_overwrite").attr('checked',true);
	jQuery("#seo_meta_keywords_overwrite").attr('checked',true);
<?php
}
?>

<?php
if ($seo_meta_rows['seo_meta_title_overwrite'] == "0") {
?>
	jQuery("#seo_meta_title").attr('disabled',true);
<?
}
if ($seo_meta_rows['seo_meta_description_overwrite'] == "0") {
?>
 	jQuery("#seo_meta_description").attr('disabled',true);
<?
}
if ($seo_meta_rows['seo_meta_keywords_overwrite'] == "0") {
?>
	jQuery("#seo_meta_keywords").attr('disabled',true);
<?
}

?>
</script>
<?
if ($id) {
?>
<input type="hidden" name="id" value="<?php echo $id; ?>">
<?php
}
?>
<input type="hidden" name="page_type" value="<?php echo $_GET['page_type']; ?>">
</form>
<?php
		$listing_html = ob_get_contents();
		ob_end_clean() ;
		
		return $listing_html;
	}
}
?>