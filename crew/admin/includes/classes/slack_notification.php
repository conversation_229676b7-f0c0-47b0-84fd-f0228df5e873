<?php

include_once(DIR_FS_ADMIN . 'includes/configure.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class slack_notification
{
    public $curl_obj, $base_path;

    public function __construct()
    {
        $this->curl_obj = new curl();
        $this->curl_obj->connect_via_proxy = true;
        if (defined("SLACK_WEBHOOK_BASE_PATH")) {
            // Fix PHP 5.4 empty() not accepting constrain / function
            $this->base_path = SLACK_WEBHOOK_BASE_PATH;
        } else {
            $this->base_path = 'https://hooks.slack.com/services';
        }
    }

    public function send($url, $body)
    {
        $url = $this->base_path . '/' . $url;
        $header = array('Content-Type:application/json');
        $this->curl_obj->curl_request('POST', $url, $header, $body);
    }

    public function debugNotification($error, $functions)
    {
        $data = json_encode(array(
            'text' => 'Debug OG Crew',
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => "\n Error : " . $error . " \n `Functions` : " . $functions
                )
            )
        ));

        $this->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }
}
