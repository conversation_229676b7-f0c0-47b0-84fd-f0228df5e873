<?php
class stock_report {
	var $mode, $startDate, $endDate, $products_qty_type, $sub_header_type, $sql_filter, $info;
	var $cat_array;
	var $sql_sort;
	var $total_price_decimal;
	var $csv_data, $report_header;
	var $report_legend;

	function stock_report($mode, $startDate='', $endDate='') {
		$this->mode = $mode;
		$this->sql_filter = '';
      	$this->info = array();
      	$this->csv_data = array();
		$this->sub_header_type = array();
		$this->report_legend = array();
		$this->total_price_decimal = 6;
		
		$startDate = trim($startDate);
		$endDate = trim($endDate);
		
		switch ($this->mode) {
			case "movement":
			case "outstanding":
				$history_start_date_fieldname = $this->mode == "movement" ? 'stock_history_date' : 'outstanding_payment_history_date';
				
				if (strpos($startDate, ':') !== false) { // User specify the start time as well
					$startDateObj = explode(' ', $startDate);
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$start_date_str = $history_start_date_fieldname . " >= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' ";
				} else {
					list($yr, $mth, $day) = explode('-', $startDate);
					$start_date_str = $history_start_date_fieldname." >= '".date('Y-m-d H:i:s', mktime(0,0,0,$mth,$day,$yr))."' ";
				}
				
				if (strpos($endDate, ':') !== false) { // User specify the end time as well
					$endDateObj = explode(' ', $endDate);
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					$end_date_str = $history_start_date_fieldname." <= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' ";
				} else {
					list($yr, $mth, $day) = explode('-', $endDate);
					$end_date_str = $history_start_date_fieldname." <= '".date('Y-m-d H:i:s', mktime(23,59,59,$mth,$day,$yr))."' ";
				}
				
				if ($this->mode == "movement") {
					$history_date_check_select_sql = "	SELECT DISTINCT(stock_history_date) AS history_date 
														FROM " . TABLE_STOCK_HISTORY . " 
														WHERE " . $start_date_str . "
															AND " . $end_date_str . " 
														ORDER BY stock_history_date desc";
				} else {
					$history_date_check_select_sql = "	SELECT DISTINCT(outstanding_payment_history_date) AS history_date 
														FROM " . TABLE_OUTSTANDING_PAYMENT_HISTORY . " 
														WHERE " . $start_date_str . "
															AND " . $end_date_str . " 
														ORDER BY outstanding_payment_history_date desc";
					
					$this->set_sub_header_type(array('B', 'PP', 'S'));
				}
				
				if (tep_not_null($history_date_check_select_sql)) {
					$history_date_check_result_sql = tep_db_query($history_date_check_select_sql, 'read_db_link');
					while ($history_date_check_row = tep_db_fetch_array($history_date_check_result_sql)) {
						$this->startDates[] = $history_date_check_row['history_date'];
					}
				}
			break;
		}
	}
 
	function set_main_category_id($main_cat_id, $icl_subcat) {
		$this->cat_array = array();
		
		$p_rank = tep_check_cat_tree_permissions(FILENAME_STOCK_REPORT, $main_cat_id);
		
		if ($p_rank == 1) {
			$this->cat_array = array($main_cat_id);
		}
		
  		if ($icl_subcat) {
  			if ($p_rank > 0) {
  				$this->cat_array = tep_get_eligible_categories(FILENAME_STOCK_REPORT, $this->cat_array, $main_cat_id, false);
  			}
  		}
 	} 
	
	function set_main_category_id_mov($main_cat_id_mov, $icl_subcat) {
	    $this->cat_array = array();
	    
	    foreach ($main_cat_id_mov as $cat_id){
			$temp_cat_array = array();
			
			$p_rank = tep_check_cat_tree_permissions(FILENAME_STOCK_REPORT, $cat_id);
			
			if ($p_rank == 1) {
				$temp_cat_array = array($cat_id);
			}
			
	  		if ($icl_subcat) {
	  			if ($p_rank > 0) { 
	  				$temp_cat_array = tep_get_eligible_categories(FILENAME_STOCK_REPORT, $temp_cat_array, $cat_id, false);
	  			}
	  		}
	  		
			$this->cat_array = array_merge($this->cat_array, $temp_cat_array);
		}

	}
	
	function set_products_qty_type($qty_type_array) {
		if (is_array($qty_type_array) && count($qty_type_array)) {
			$this->products_qty_type = $qty_type_array;
		}
	}
	
	function set_sub_header_type($sub_header_type_array) {
		if (is_array($sub_header_type_array) && count($sub_header_type_array)) {
			$this->sub_header_type = $sub_header_type_array;
		}
	}
	
	function set_sorting_info($sorting_array) {
		for ($i=0; $i < count($sorting_array); $i++) {
			if ($i == 0)	$this->sql_sort = " ORDER BY ";
			if (tep_not_null($sorting_array[$i])) {
				$this->sql_sort .= $sorting_array[$i][0] . ' ' . $sorting_array[$i][1] . ', ';
			}
		}
		
		if (substr(trim($this->sql_sort), -1) == ',')	$this->sql_sort = substr(trim($this->sql_sort), 0, -1);
	}
	
	function reportQuery() {
		global $languages_id;
		
		switch ($this->mode) {
			case 'real_time':
				$sel_cat_sql_array = array();
				
				$stock_query_str = "select p.products_id, p.products_cat_path, p.products_quantity, p.products_actual_quantity, p.products_quantity_fifo_cost, p.products_actual_quantity_fifo_cost, p.products_status, p.products_main_cat_id, pd.products_name, pd.products_location, p.products_cat_id_path from products AS p 
									left join products_description as pd on p.products_id=pd.products_id where 1 ";
				
				if (isset($_REQUEST["cat_id"]) && tep_not_null($_REQUEST["cat_id"])) {
		       		$parent_path_str = tep_get_categories_parent_path((int)$_SESSION['stock_param']["cat_id"]);
	        		$top_parent_id = 0;
					
					if (tep_not_null($parent_path_str)) {
						$parent_path_str .= (int)$_SESSION['stock_param']["cat_id"] . '_';
						$parent_path_array = explode("_",$parent_path_str);
						$top_parent_id = $parent_path_array[1];
					} else {
						if ((int)$_SESSION['stock_param']["cat_id"] > 0) {
							$parent_path_str = '_'.(int)$_SESSION['stock_param']["cat_id"] . '_';
							$top_parent_id = (int)$_SESSION['stock_param']["cat_id"];
						}
					}
					
					$parent_path_str = preg_replace("/_/u", "\_", $parent_path_str);
					
					$stock_query_str .= "	and (p.products_main_cat_id = '".$top_parent_id."')
											and (p.products_cat_id_path LIKE '".$parent_path_str."%')
										";
				} else if (isset($_REQUEST["_dualselect_sel_category_id"]) && tep_not_null($_REQUEST["_dualselect_sel_category_id"])) {
					foreach ($_SESSION['stock_param']["category_id_to"] as $sel_cat_id) {
		 				if ($sel_cat_id > 0) {
		 					$parent_path_str = '\_'.$sel_cat_id . '\_';
							
							$sel_cat_sql_array[] =  " p.products_cat_id_path like '".$parent_path_str ."%' ";
		        	    }
		 			}
		 			$stock_query_str .= " and (" . implode(" or ", $sel_cat_sql_array) . ")";
				}
				
				$stock_query_str .= "	and p.products_skip_inventory=0 
										and p.products_bundle='' 
										and p.products_bundle_dynamic='' 
										and ". ($this->sql_filter ? $this->sql_filter : "1") ." 
										and pd.language_id='" . $languages_id . "'";
				
				$_SESSION['ori_stock_select_sql'] = $stock_query_str. $this->sql_sort;
				
				break;
			
			case 'movement':
			  	if (isset($_REQUEST["cat_id"]) &&  tep_not_null($_REQUEST["cat_id"])){ 
			  		for ($i=0; $i < count($this->startDates); $i++) {
				  		$stock_history_products_select_sql = "	SELECT pc.products_id, pd.products_name, pc.categories_id, sh.products_actual_quantity, sh.products_quantity, sh.products_quantity_fifo_cost, sh.products_actual_quantity_fifo_cost, sh.stock_history_date, p.products_cat_path 
				  												FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
																LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																	ON pc.products_id=pd.products_id
																LEFT JOIN " . TABLE_PRODUCTS . " AS p
				  													ON pc.products_id=p.products_id	
																LEFT JOIN " . TABLE_STOCK_HISTORY . " AS sh
				  													ON pc.products_id=sh.products_id
				  												WHERE sh.stock_history_date = '". $this->startDates[$i] ."'
								  									AND ". ($this->sql_filter ? $this->sql_filter : "1") ." 
								  									AND pc.categories_id IN (" . implode(",", $this->cat_array) . ") 
								  									AND pc.products_is_link=0 
								  									AND pd.language_id='" . $languages_id . "' 
								  								ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
						$stock_history_products_result_sql = tep_db_query($stock_history_products_select_sql, 'read_db_link');
					  	while ($stock_history_products_row = tep_db_fetch_array($stock_history_products_result_sql)) {
		 					$prod_cat_path = $stock_history_products_row['products_cat_path'] ." > ". $stock_history_products_row['products_name'];
		 					$prod_cat_path_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($stock_history_products_row["categories_id"]) . '&pID=' . $stock_history_products_row["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;">' . strip_tags($prod_cat_path) . '</a>';
		 					
		 					if (!isset($this->info[$stock_history_products_row["products_id"]]))  {
			 					$this->info[$stock_history_products_row["products_id"]] = array(	'id' => $stock_history_products_row["products_id"],
														 											'name' => $prod_cat_path,
														 											'name_link' => $prod_cat_path_link,
														 											'history' => array()
																								);
							}
							
		 					$this->info[$stock_history_products_row["products_id"]]['history'][$stock_history_products_row['stock_history_date']] = array(	'products_quantity' => $stock_history_products_row['products_quantity'],
		 																																					'products_actual_quantity' => $stock_history_products_row['products_actual_quantity'],
																																							'products_quantity_fifo_cost' => $stock_history_products_row['products_quantity_fifo_cost'],
																																							'products_actual_quantity_fifo_cost' => $stock_history_products_row['products_actual_quantity_fifo_cost']
																	  																					);
				 		}
			    	}
				} else if (isset($_REQUEST["_dualselect_sel_category_id"]) && tep_not_null($_REQUEST["_dualselect_sel_category_id"])) {
				  	for ($i=0; $i < count($this->startDates); $i++) {
				  		$stock_history_products_select_sql = "	SELECT pc.products_id, pd.products_name, pc.categories_id, sh.products_actual_quantity, sh.products_quantity, sh.products_quantity_fifo_cost, sh.products_actual_quantity_fifo_cost, sh.stock_history_date, p.products_cat_path 
				  												FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
																LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																	ON pc.products_id=pd.products_id
																LEFT JOIN " . TABLE_PRODUCTS . " AS p
				  													ON pc.products_id=p.products_id	
																LEFT JOIN " . TABLE_STOCK_HISTORY . " AS sh
				  													ON pc.products_id=sh.products_id
				  												WHERE sh.stock_history_date = '". $this->startDates[$i] ."'
								  									AND ". ($this->sql_filter ? $this->sql_filter : "1") ." 
								  									AND pc.categories_id IN ('" . implode("','", $this->cat_array) . "') 
								  									AND pc.products_is_link=0 
								  									AND pd.language_id='" . $languages_id . "' 
								  								ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
						$stock_history_products_result_sql = tep_db_query($stock_history_products_select_sql, 'read_db_link');
					  	while ($stock_history_products_row = tep_db_fetch_array($stock_history_products_result_sql)) {
		 					$prod_cat_path = $stock_history_products_row['products_cat_path'] ." > ". $stock_history_products_row['products_name'];
		 					$prod_cat_path_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($stock_history_products_row["categories_id"]) . '&pID=' . $stock_history_products_row["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;">' . strip_tags($prod_cat_path) . '</a>';
		 					
		 					if (!isset($this->info[$stock_history_products_row["products_id"]]))  {
			 					$this->info[$stock_history_products_row["products_id"]] = array(	'id' => $stock_history_products_row["products_id"],
														 											'name' => $prod_cat_path,
														 											'name_link' => $prod_cat_path_link,
														 											'history' => array()
																								);
							}
							
		 					$this->info[$stock_history_products_row["products_id"]]['history'][$stock_history_products_row['stock_history_date']] = array(	'products_quantity' => $stock_history_products_row['products_quantity'],
																																							'products_actual_quantity' => $stock_history_products_row['products_actual_quantity'],
																																							'products_quantity_fifo_cost' => $stock_history_products_row['products_quantity_fifo_cost'],
																																							'products_actual_quantity_fifo_cost' => $stock_history_products_row['products_actual_quantity_fifo_cost']
																						  																	);
				 		}
				    }
				}
				break;
				
			case "outstanding":
				for ($i=0; $i < count($this->startDates); $i++) {
			  		$outstanding_payment_select_sql = "	SELECT oph.*, 
			  												s.supplier_firstname, s.supplier_lastname, s.supplier_email_address, 
			  												c.customers_firstname, c.customers_lastname, c.customers_email_address 
		  												FROM " . TABLE_OUTSTANDING_PAYMENT_HISTORY . " AS oph 
														LEFT JOIN " . TABLE_SUPPLIER . " AS s 
															ON (oph.user_id=s.supplier_id AND oph.payment_category IN ('S', 'PP')) 
														LEFT JOIN " . TABLE_CUSTOMERS . " AS c 
		  													ON (oph.user_id=c.customers_id AND oph.payment_category='B') 
		  												WHERE oph.outstanding_payment_history_date = '". $this->startDates[$i] ."' 
						  									AND ". ($this->sql_filter ? $this->sql_filter : "1") ." 
						  								ORDER BY c.customers_firstname, c.customers_lastname, s.supplier_firstname, s.supplier_lastname";
					$outstanding_payment_result_sql = tep_db_query($outstanding_payment_select_sql, 'read_db_link');
					
			  		while ($outstanding_payment_row = tep_db_fetch_array($outstanding_payment_result_sql)) {
				  		// Do a grouping here to distinguish customer or supplier since there might be same user id (customer id and supplier id)
				  		if ($outstanding_payment_row['payment_category'] == 'B') {	// Only customer submit buyback order
				  			$array_ref_id = $outstanding_payment_row['user_id'] . '_B';
				  			$name = is_null($outstanding_payment_row['customers_email_address']) ? $outstanding_payment_row['user_firstname'] . $outstanding_payment_row['user_lastname'] : $outstanding_payment_row['customers_firstname'] . $outstanding_payment_row['customers_lastname'];
				  			$email = is_null($outstanding_payment_row['customers_email_address']) ? $outstanding_payment_row['user_email_address'] : $outstanding_payment_row['customers_email_address'];
				  			
				  		} else {
				  			$array_ref_id = $outstanding_payment_row['user_id'] . '_S';
				  			$name = is_null($outstanding_payment_row['supplier_email_address']) ? $outstanding_payment_row['user_firstname'] . $outstanding_payment_row['user_lastname'] : $outstanding_payment_row['supplier_firstname'] . $outstanding_payment_row['supplier_lastname'];
				  			$email = is_null($outstanding_payment_row['supplier_email_address']) ? $outstanding_payment_row['user_email_address'] : $outstanding_payment_row['supplier_email_address'];
				  		}
			  		
	 					if (!isset($this->info[$array_ref_id]))  {
		 					$this->info[$array_ref_id] = array(	'id' => $outstanding_payment_row['user_id'],
		 														'id_type' => $outstanding_payment_row['payment_category'] == 'B' ? 'customer' : 'supplier',
													 			'name' => tep_not_null($name) ? $name : ($outstanding_payment_row['payment_category'] == 'B' ? sprintf(TEXT_DISPLAY_CUSTOMER, $outstanding_payment_row['user_id']) : sprintf(TEXT_DISPLAY_SUPPLIER, $outstanding_payment_row['user_id'])),
													 			'email' => $email,
													 			'history' => array()
																);
						}
						
	 					$this->info[$array_ref_id]['history'][$outstanding_payment_row['outstanding_payment_history_date']][$outstanding_payment_row['payment_category']] = array(	'gross' => $outstanding_payment_row['outstanding_payment_gross_amount'],
																																													'outstanding_amount' => $outstanding_payment_row['outstanding_payment_amount']);
			 		}
			    }
			    
				break;
	    }
	}
	
	function displayCatTree($currencies, $show_records='ALL') {
		$report_html = '';
		
		switch ($this->mode) {
			case 'real_time':
				$view_location_permission = tep_admin_files_actions(FILENAME_STOCK_REPORT, 'VIEW_PRODUCT_LOCATION');
				$view_fifo_permission = tep_admin_files_actions(FILENAME_STOCK_REPORT, 'VIEW_FIFO_INFO');
				$stock_column_allignment = $view_fifo_permission ? 'right' : 'center';	// positioning the stock column when show/hide the FIFO info
				$total_available_qty = 0;
				$total_actual_qty = 0;
				
				$report_html .= '<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="1" cellpadding="2">
               								<tr>';
				$report_html .= '				<td class="reportBoxHeading">'.TABLE_HEADING_PRODUCTS_PATH.'</td>';
                $report_html .= '				<td class="reportBoxHeading">'.TABLE_HEADING_PRODUCTS.'</td>';
                $report_html .= '				<td class="reportBoxHeading" align="center">'.TABLE_HEADING_PRODUCTS_STATUS.'</td>';
                
				$this->report_header['HEADER'][] = TABLE_HEADING_PRODUCTS_PATH;
				$this->report_header['HEADER'][] = TABLE_HEADING_PRODUCTS;
				$this->report_header['HEADER'][] = TABLE_HEADING_PRODUCTS_STATUS;

           		if ($view_location_permission) {
           			 $report_html .= '<td class="reportBoxHeading">' . TABLE_HEADING_PRODUCTS_LOCATION . '</td>';
			  		$this->report_header['HEADER'][] = TABLE_HEADING_PRODUCTS_LOCATION;	
				}
				
        		$report_html .= '<td class="reportBoxHeading" align="center">'.TABLE_HEADING_VERIFYING_QTY.'</td>';
        		$report_html .= '<td class="reportBoxHeading" align="center">'.TABLE_HEADING_PENDING_PROCESSING_QTY.'</td>';
        		$report_html .= '<td class="reportBoxHeading" align="center">'.TABLE_HEADING_PURCHASING_QTY.'</td>';
        		
				$this->report_header['HEADER'][] = TABLE_HEADING_VERIFYING_QTY;
				$this->report_header['HEADER'][] = TABLE_HEADING_PENDING_PROCESSING_QTY;
				$this->report_header['HEADER'][] = TABLE_HEADING_PURCHASING_QTY;

				if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"])) {
                	$report_html .= '<td class="reportBoxHeading" align="'.$stock_column_allignment.'">' . ($view_fifo_permission ? TABLE_HEADING_QTY_LEFT_WITH_FIFO : TABLE_HEADING_QTY_LEFT) . '</td>';
					$this->report_header['HEADER'][] =	strip_tags(($view_fifo_permission ? TABLE_HEADING_QTY_LEFT_WITH_FIFO : TABLE_HEADING_QTY_LEFT));
				}
				
				if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"])) {
					$report_html .= '<td class="reportBoxHeading" align="'.$stock_column_allignment.'">' . ($view_fifo_permission ? TABLE_HEADING_ACTUAL_QTY_WITH_FIFO : TABLE_HEADING_ACTUAL_QTY) . '</td>';
					$this->report_header['HEADER'][] =	strip_tags(($view_fifo_permission ? TABLE_HEADING_ACTUAL_QTY_WITH_FIFO : TABLE_HEADING_ACTUAL_QTY));
				}
			   	$report_html .= '</tr>';
			   	
				$stock_select_sql = $_SESSION['ori_stock_select_sql'];
				//$show_records = $_REQUEST["show_records"];
				
				if ($show_records != "ALL") {
					$stock_split_object = new splitPageResults($_REQUEST["page"], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $stock_select_sql, $stock_sql_numrows);
				}
			
				$stock_query = tep_db_query($stock_select_sql, 'read_db_link');	
   				
				$row_count = 0;
				$considered_order_status_array = array(7, 2);
				$csv_data_size = count($this->csv_data);
							
				while ($stock_row = tep_db_fetch_array($stock_query)) {
				
					$product_cat_path_array = explode("_",$stock_row['products_cat_id_path']);
					$stock_row['categories_id'] = $product_cat_path_array[count($product_cat_path_array)-2];	// Last element is '_'
				
					$product_name_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($stock_row["categories_id"]) . '&pID=' . $stock_row["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;">' . strip_tags($stock_row["products_name"]) . '</a>';
					$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
					$total_available_qty += (int) $stock_row["products_quantity"];
					$total_actual_qty += (int) $stock_row["products_actual_quantity"];
				
					$total_undelivered_processing_qty = 0;
					$order_status_quantity_array = array();
				
					$order_status_quantity_info_select_sql = "	SELECT o.orders_status, SUM(products_quantity - products_delivered_quantity) AS total_undeliver_processing_qty, 
																	MAX(TO_DAYS(NOW()) - TO_DAYS(o.date_purchased)) as oldest_order_day 
																FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
																INNER JOIN " . TABLE_ORDERS . " AS o 
																	ON op.orders_id = o.orders_id 
																WHERE op.products_id = '".$stock_row['products_id']."' 
																	AND o.orders_status IN ('".implode("', '", $considered_order_status_array)."') 
																GROUP BY op.products_id, o.orders_status";
					$order_status_quantity_info_result_sql = tep_db_query($order_status_quantity_info_select_sql, 'read_db_link');
					while ($order_status_quantity_info_row = tep_db_fetch_array($order_status_quantity_info_result_sql)) {
						$order_status_quantity_array[$order_status_quantity_info_row['orders_status']] = $order_status_quantity_info_row;
					}
					
					$floating_available_qty = tep_calculate_floating_available_qty($stock_row['products_id']);
		   								
					$report_html .=	'<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">';											
					$report_html .=	'<td class="reportRecords">'.$stock_row["products_cat_path"].'</td>';
					$report_html .= '<td class="reportRecords">'.$product_name_link.'</td>';
					$report_html .= '<td class="reportRecords" align="center">';
					
					$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_cat_path"];
					$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_name"];	
				
					if ($stock_row["products_status"] == "1") {
						$report_html .=	tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
					
					} else {
						$report_html .= tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
					}
					
					$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_status"];
												
					$report_html .='</td>';
												
					if ($view_location_permission) {
						$report_html .= '<td class="reportRecords">' . $stock_row["products_location"] . '</td>';
						$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_location"];
					}
					
					for ($osq_ant=0; $osq_ant < count($considered_order_status_array); $osq_ant++) {
					 	$cur_order_status_id = $considered_order_status_array[$osq_ant];
						$report_html .= '<td class="reportRecords" align="center">';
						if ($order_status_quantity_array[$cur_order_status_id]['total_undeliver_processing_qty'] > 0) {
							$total_undelivered_processing_qty += $order_status_quantity_array[$cur_order_status_id]['total_undeliver_processing_qty'];
							
							$report_html .= "\n" .tep_draw_form('orders_'.$cur_order_status_id . '_' . $stock_row["products_id"] . '_form', FILENAME_STATS_ORDERS_TRACKING, 'selected_box=sales&action=show_report&subaction=sl_status', 'post', 'target="_blank"') . "\n";
							$report_html .= tep_draw_hidden_field('order_status', tep_array_serialize(array($cur_order_status_id))) . "\n";
							$report_html .= tep_draw_hidden_field('product_id', $stock_row["products_id"]) . "\n";
							$report_html .= tep_draw_hidden_field('include_subcategory', 1) . "\n";
							$report_html .= tep_draw_hidden_field('show_records', ($cur_order_status_id==2 ? 'ALL' : 50)) . "\n";
							$report_html .= '<a href="javascript: document.orders_'.$cur_order_status_id . '_' . $stock_row["products_id"] . '_form' . '.submit();">';
							$report_html .= (int)$order_status_quantity_array[$cur_order_status_id]['total_undeliver_processing_qty'];
							$report_html .= '</a>';
							$report_html .= '</form>';
							$report_html .= ' ('.$order_status_quantity_array[$cur_order_status_id]['oldest_order_day'].')';
						
							$this->csv_data[$csv_data_size + $row_count][] = (int)$order_status_quantity_array[$cur_order_status_id]['total_undeliver_processing_qty'].'('.$order_status_quantity_array[$cur_order_status_id]['oldest_order_day'].')';	
							//$this->csv_data[$csv_data_size + $row_count][] = $order_status_quantity_array[$cur_order_status_id]['oldest_order_day'];											
						} else {
							$report_html .= '&nbsp;';
							$this->csv_data[$csv_data_size + $row_count][] = '';
						}
						$report_html .= '</td>';
					}
					
					$report_html .= '	<td class="reportRecords" align="center" nowrap>' . ($total_undelivered_processing_qty-$floating_available_qty) . '</td>';
					$this->csv_data[$csv_data_size + $row_count][] = ($total_undelivered_processing_qty-$floating_available_qty);
					
					if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"])) {
						$report_html .= '<td class="reportRecords" align="'.$stock_column_allignment.'" nowrap>' . $stock_row["products_quantity"] . ($view_fifo_permission && tep_not_null($stock_row['products_quantity_fifo_cost']) ? ' ('.$stock_row['products_quantity_fifo_cost'].')' : '&nbsp;') . '</td>';
						$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_quantity"] . ($view_fifo_permission && tep_not_null($stock_row['products_quantity_fifo_cost']) ? ' ('.$stock_row['products_quantity_fifo_cost'].')' : '');
					}
					
					if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"])) {
						$report_html .= '<td class="reportRecords" align="'.$stock_column_allignment.'" nowrap>' . $stock_row["products_actual_quantity"] . ($view_fifo_permission && tep_not_null($stock_row['products_actual_quantity_fifo_cost']) ? ' ('.$stock_row['products_actual_quantity_fifo_cost'].')' : '&nbsp;') . '</td>';
						$this->csv_data[$csv_data_size + $row_count][] = $stock_row["products_actual_quantity"] . ($view_fifo_permission && tep_not_null($stock_row['products_actual_quantity_fifo_cost']) ? ' ('.$stock_row['products_actual_quantity_fifo_cost'].')' : '');
					}
					$report_html .=	'</tr>';
					$row_count++;
   				}

    			if ($show_records == "ALL" || $_REQUEST["page"] == 'all' || $stock_sql_numrows <= (int)$show_records) {
    				$report_html .= '<tr>
										<td class="reportRecords" colspan="'.($view_location_permission ? "5" : "4").'">&nbsp;</td>
										<td></td>
										<td class="reportRecords" align="right">'.TABLE_FOOTER_TOTAL.'</td>';
					if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"])&& !in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"])){
						$report_html .= '<td class="reportRecords" align="right">'.(is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_available_qty : '&nbsp;').'</td>';
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = TABLE_FOOTER_TOTAL;
						$this->csv_data[$csv_data_size + $row_count][] = (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_available_qty : '');
					}
					
					if (is_array($_SESSION['stock_param']["products_qty_type"]) && !in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"])){
						$report_html .= '<td class="reportRecords" align="right">'.(is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_actual_qty : '&nbsp;').'</td>';
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = TABLE_FOOTER_TOTAL;
						$this->csv_data[$csv_data_size + $row_count][] = (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_actual_qty : '');
					}
					
					if (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"])&& in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"])){
						$report_html .= '<td class="reportRecords" align="right">'.(is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_available_qty : '&nbsp;').'</td>';
						$report_html .= '<td class="reportRecords" align="right">'.(is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_actual_qty : '&nbsp;').'</td>';
						$report_html .= '</tr>';
						
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = "";
						$this->csv_data[$csv_data_size + $row_count][] = TABLE_FOOTER_TOTAL;
						$this->csv_data[$csv_data_size + $row_count][] = (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_available_qty : '');
						$this->csv_data[$csv_data_size + $row_count][] = (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"]) ? $total_actual_qty : '');
					}
				}
				
				$report_html .= ' 		</table>
			   						</td>
			   					</tr>
			   					<tr>
			   						<td>
			   							<table border="0" width="100%" cellspacing="1" cellpadding="2">
			   								<tr>
												<td class="smallText" valign="top">'.($show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_PRODUCTS, tep_db_num_rows($stock_query) > 0 ? "1" : "0", tep_db_num_rows($stock_query), tep_db_num_rows($stock_query)) : $stock_split_object->display_count($stock_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS)).'</td>
												<td class="smallText" align="right">'.($show_records == "ALL" ? "Page 1 of 1" : $stock_split_object->display_links($stock_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'show_records', 'x', 'y', 'cID', 'cont'))."cont=1&show_records=$show_records")).'</td>';	
				$report_html .= '			</tr>
										</table>
									</td>
								</tr>';
				break;
			
			case 'movement':
				$view_fifo_permission = tep_admin_files_actions(FILENAME_STOCK_REPORT, 'VIEW_FIFO_INFO');
				$stock_column_allignment = $view_fifo_permission ? 'right' : 'center';	// positioning the stock column when show/hide the FIFO info
				
				$total_info_array = array();
				
				if ($this->info) {
					$report_html .= '<table border="0" width="100%" cellspacing="1" cellpadding="2">
										<tr>
											<td class="reportBoxHeading" rowspan="2" width="100%" nowrap>'. TABLE_HEADING_PRODUCTS .'</td>';
					$this->report_header['HEADER'][] = '';
					$this->report_header['SUB_HEADER_1'][] = TABLE_HEADING_PRODUCTS;
					
					for ($i=0; $i < count($this->startDates); $i++) {
						$report_html .= '	<td class="reportBoxHeading" colspan="'. count($this->products_qty_type) .'" align="center">' . (strtotime($this->startDates[$i]) === -1 ? $this->startDates[$i] : date('Y-m-d', strtotime($this->startDates[$i]))) . '</td>';
						$this->report_header['HEADER'][] = (strtotime($this->startDates[$i]) === -1 ? $this->startDates[$i] : date('Y-m-d', strtotime($this->startDates[$i])));
						
						if ($view_fifo_permission) {
							for ($empty_cnt=0; $empty_cnt < (count($this->products_qty_type)-1)*2 + 1; $empty_cnt++) {
								$this->report_header['HEADER'][] = '';
							}
						} else {
							if (count($this->products_qty_type) > 1) {
								$this->report_header['HEADER'][] = '';
							}
						}
					}
					
					$report_html .= '	</tr>
										<tr>';
					
					for ($i=0; $i < count($this->startDates); $i++) {
						if (is_array($this->products_qty_type) && in_array("available_qty", $this->products_qty_type)) {
							$report_html .= '	<td class="reportBoxHeading" width="10px" align="'.$stock_column_allignment.'" nowrap>'. ($view_fifo_permission ? TABLE_HEADING_QTY_LEFT_WITH_FIFO : TABLE_HEADING_QTY_LEFT) .'</td>';
							$this->report_header['SUB_HEADER_1'][] = TABLE_HEADING_QTY_LEFT;
							if ($view_fifo_permission)	$this->report_header['SUB_HEADER_1'][] = '';
						}
						
						if (is_array($this->products_qty_type) && in_array("actual_qty", $this->products_qty_type)) {
							$report_html .= '	<td class="reportBoxHeading" width="10px" align="'.$stock_column_allignment.'" nowrap>'. ($view_fifo_permission ? TABLE_HEADING_ACTUAL_QTY_WITH_FIFO : TABLE_HEADING_ACTUAL_QTY) .'</td>';
							$this->report_header['SUB_HEADER_1'][] = TABLE_HEADING_ACTUAL_QTY;
							if ($view_fifo_permission)	$this->report_header['SUB_HEADER_1'][] = '';
						}
					}
					
					$report_html .= '		</tr>';
					$prod_cnt = 0;
					$csv_data_size = count($this->csv_data);
					
					foreach ($this->info as $pid => $pInfo) {
						$row_style = ($prod_cnt%2) ? 'reportListingEven' : 'reportListingOdd' ;
						$report_html .= '	<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
												<td class="reportRecords">'.$pInfo['name'].'</td>';
						
						$this->csv_data[$csv_data_size + $prod_cnt][] = $pInfo['name'];
						
						for ($sDateCnt=0; $sDateCnt < count($this->startDates); $sDateCnt++) {
							$date_history_info_array = $pInfo['history'][$this->startDates[$sDateCnt]];
							
							if (is_array($this->products_qty_type) && in_array("available_qty", $this->products_qty_type)) {
								$available_qty_cost = ($view_fifo_permission && tep_not_null($date_history_info_array['products_quantity_fifo_cost'])) ? number_format($date_history_info_array['products_quantity']*$date_history_info_array['products_quantity_fifo_cost'], $this->total_price_decimal, '.', ',') : '';
								
								$report_html .= '		<td class="reportRecords" align="'.$stock_column_allignment.'" nowrap>'. $date_history_info_array['products_quantity'] . ($view_fifo_permission && tep_not_null($date_history_info_array['products_quantity_fifo_cost']) ? ' ('.$date_history_info_array['products_quantity_fifo_cost'].') = '.$available_qty_cost : '&nbsp;') . '</td>';
								$total_info_array[$this->startDates[$sDateCnt]]['products_quantity'] += $date_history_info_array['products_quantity'];
								if ($view_fifo_permission) {
									$this->csv_data[$csv_data_size + $prod_cnt][] = $date_history_info_array['products_quantity'] . (tep_not_null($date_history_info_array['products_quantity_fifo_cost']) ? ' x '. $date_history_info_array['products_quantity_fifo_cost'] : '');
									$this->csv_data[$csv_data_size + $prod_cnt][] = tep_not_null($date_history_info_array['products_quantity_fifo_cost']) ? $available_qty_cost : '';
								} else {
									$this->csv_data[$csv_data_size + $prod_cnt][] = $date_history_info_array['products_quantity'];
								}
							}
							
							if (is_array($this->products_qty_type) && in_array("actual_qty", $this->products_qty_type)) {
								$actual_qty_cost = ($view_fifo_permission && tep_not_null($date_history_info_array['products_actual_quantity_fifo_cost'])) ? number_format($date_history_info_array['products_actual_quantity'] * $date_history_info_array['products_actual_quantity_fifo_cost'], $this->total_price_decimal, '.', ',') : '';
								
								$report_html .= '		<td class="reportRecords" align="'.$stock_column_allignment.'" nowrap>'. $date_history_info_array['products_actual_quantity'] . ($view_fifo_permission && tep_not_null($date_history_info_array['products_actual_quantity_fifo_cost']) ? ' ('.$date_history_info_array['products_actual_quantity_fifo_cost'].') = '.$actual_qty_cost : '&nbsp;') .'</td>';
								$total_info_array[$this->startDates[$sDateCnt]]['products_actual_quantity'] += $date_history_info_array['products_actual_quantity'];
								
								if ($view_fifo_permission) {
									$this->csv_data[$csv_data_size + $prod_cnt][] = $date_history_info_array['products_actual_quantity'] . (tep_not_null($date_history_info_array['products_actual_quantity_fifo_cost']) ? ' x '.$date_history_info_array['products_actual_quantity_fifo_cost'] : '');
									$this->csv_data[$csv_data_size + $prod_cnt][] = tep_not_null($date_history_info_array['products_actual_quantity_fifo_cost']) ? $actual_qty_cost : '';
								} else {
									$this->csv_data[$csv_data_size + $prod_cnt][] = $date_history_info_array['products_actual_quantity'];
								}
							}
						}
						$report_html .= '	</tr>';
						
						$prod_cnt++;
					}
					
					$report_html .= '		<tr>';
					$report_html .= '			<td class="reportRecords" align="right">'. TABLE_FOOTER_TOTAL .'</td>';
					
					$csv_data_size = count($this->csv_data);
					$this->csv_data[$csv_data_size][] = TABLE_FOOTER_TOTAL;
					
					foreach ($total_info_array as $total_info) {
						if (is_array($this->products_qty_type) && in_array("available_qty", $this->products_qty_type)) {
							$report_html .= '	<td class="reportRecords" align="center">'. $total_info['products_quantity'] .'</td>';
							$this->csv_data[$csv_data_size][] = $total_info['products_quantity'];
							if ($view_fifo_permission)	$this->csv_data[$csv_data_size][] = '';
						}
						
						if (is_array($this->products_qty_type) && in_array("actual_qty", $this->products_qty_type)) {
							$report_html .= '	<td class="reportRecords" align="center">'. $total_info['products_actual_quantity'] .'</td>';
							$this->csv_data[$csv_data_size][] = $total_info['products_actual_quantity'];
							if ($view_fifo_permission)	$this->csv_data[$csv_data_size][] = '';
						}
					}
					$report_html .= '		</tr>';
					$report_html .= '	</table>';
				}
				break;
				
			case "outstanding":
				$total_info_array = array();
				
				if ($this->info) {
					$report_html .= '	<table border="0" width="100%" cellspacing="1" cellpadding="2">
											<tr>
												<td class="reportBoxHeading" rowspan="2" width="100%" nowrap>'. TABLE_HEADING_OUTSTANDING_PAYMENT_USERS .'</td>';
					$this->report_header['HEADER'][] = '';
					$this->report_header['SUB_HEADER_1'][] = TABLE_HEADING_OUTSTANDING_PAYMENT_USERS;
					
					for ($i=0; $i < count($this->startDates); $i++) {
						$report_html .= '		<td class="reportBoxHeading" colspan="'. count($this->sub_header_type) .'" align="center">' . (strtotime($this->startDates[$i]) === -1 ? $this->startDates[$i] : date('Y-m-d', strtotime($this->startDates[$i]))) . '</td>';
						$this->report_header['HEADER'][] = (strtotime($this->startDates[$i]) === -1 ? $this->startDates[$i] : date('Y-m-d', strtotime($this->startDates[$i])));
						
						for ($sub_header_cnt=1; $sub_header_cnt < count($this->sub_header_type); $sub_header_cnt++) {
							$this->report_header['HEADER'][] = '';
						}
					}
					
					$report_html .= '		</tr>
											<tr>';
											
					for ($i=0; $i < count($this->startDates); $i++) {
						for ($sub_header_cnt=0; $sub_header_cnt < count($this->sub_header_type); $sub_header_cnt++) {
							$report_html .= '	<td class="reportBoxHeading" width="70px" align="center" nowrap>'. $this->sub_header_type[$sub_header_cnt] .'</td>';
							$this->report_header['SUB_HEADER_1'][] = $this->sub_header_type[$sub_header_cnt];
						}
					}
					$report_html .= '		</tr>';
					
					$row_cnt = 0;
					$csv_data_size = count($this->csv_data);
					foreach ($this->info as $user_ref => $paymentInfo) {
						$row_style = ($row_cnt%2) ? 'reportListingEven' : 'reportListingOdd' ;
						
						$report_html .= '			<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
														<td class="reportRecords">'.$paymentInfo['name'] . (tep_not_null($paymentInfo['email']) ? ' ('.$paymentInfo['email'].')' : '').'</td>';
						
						$this->csv_data[$csv_data_size + $row_cnt][] = $paymentInfo['name'] . (tep_not_null($paymentInfo['email']) ? ' ('.$paymentInfo['email'].')' : '');
						
						for ($sDateCnt=0; $sDateCnt < count($this->startDates); $sDateCnt++) {
							$date_history_info_array = $paymentInfo['history'][$this->startDates[$sDateCnt]];
							
							for ($sub_header_cnt=0; $sub_header_cnt < count($this->sub_header_type); $sub_header_cnt++) {
								$payment_type_info =  isset($date_history_info_array[$this->sub_header_type[$sub_header_cnt]]) ? $date_history_info_array[$this->sub_header_type[$sub_header_cnt]] : array();
								
								$outstanding_amt = isset($payment_type_info['outstanding_amount']) ? $currencies->format($payment_type_info['outstanding_amount']) : '';
								$gross_amt = $this->sub_header_type[$sub_header_cnt] == 'PP' && tep_not_null($payment_type_info['outstanding_amount']['gross']) ? ' ('.$payment_type_info['gross'].')' : '';
								
								if (isset($total_info_array[$this->startDates[$sDateCnt]]) && is_array($total_info_array[$this->startDates[$sDateCnt]])) {
									$total_info_array[$this->startDates[$sDateCnt]][$this->sub_header_type[$sub_header_cnt]] += $payment_type_info['outstanding_amount'];
								} else {
									$total_info_array[$this->startDates[$sDateCnt]] = array();
									$total_info_array[$this->startDates[$sDateCnt]][$this->sub_header_type[$sub_header_cnt]] = $payment_type_info['outstanding_amount'];
								}
								
								$report_html .= '		<td class="reportRecords" align="right" nowrap>'. (tep_not_null($outstanding_amt) ? $outstanding_amt : '&nbsp;') . $gross_amt . '</td>';
								$this->csv_data[$csv_data_size + $row_cnt][] = $outstanding_amt . $gross_amt;
							}
						}
						$report_html .= '			</tr>';
						
						$row_cnt++;
					}
					
					$report_html .= '				<tr>
														<td class="reportRecords" align="right">'. TABLE_FOOTER_TOTAL .'</td>';
					
					$csv_data_size = count($this->csv_data);
					$this->csv_data[$csv_data_size][] = TABLE_FOOTER_TOTAL;
					
					foreach ($total_info_array as $total_info) {
						for ($sub_header_cnt=0; $sub_header_cnt < count($this->sub_header_type); $sub_header_cnt++) {
							$report_html .= '			<td class="reportRecords" align="right" nowrap>'. $currencies->format($total_info[$this->sub_header_type[$sub_header_cnt]]) .'</td>';
							$this->csv_data[$csv_data_size][] = $currencies->format($total_info[$this->sub_header_type[$sub_header_cnt]]);
						}
					}
					$report_html .= '				</tr>
												</table>';
				}
				
				break;
		}
		
		return $report_html;
	}
	
	function get_csv_data($mode) {
		switch ($mode) {
			case 'real_time':
				;
				break;
		}
	}
	
	function display_report_legend() {
		$legend_html = '';
		
		if (is_array($this->report_legend) && count($this->report_legend)) {
			$legend_html .= '<table border="0" cellspacing="1" cellpadding="0">';
			
			$legend_cnt = 0;
			foreach ($this->report_legend as $legend_key => $info) {
				if ($pm_cnt == 0) {
					$legend_html .= '<tr>';
				} else if ($pm_cnt%5 == 0) {
					$legend_html .= '</tr>
									<tr>';
				}
				
				$legend_html .= '		<td class="smallText">' . $legend_key . ':</td>
										<td class="smallText" NOWRAP>&nbsp;'.$info.'&nbsp;&nbsp;</td>';
				$pm_cnt++;
			}
			
			$legend_html .= '		</tr>
								</table>';
		}
		
		echo $legend_html;
	}
	
	function getSummaryText() {
		switch($this->mode) {
			case "real_time":
				$this->summary["report_title"] = HEADING_TITLE;
				break;			
			case "movement":
				$this->summary["report_title"] = HEADING_MOVEMENT_TITLE;
				break;
			case "outstanding":
				$this->summary["report_title"] = HEADING_OUTSTANDING_PAYMENT_TITLE;
				$this->report_legend = array(	'B' => 'Customer Buyback',
												'PP' => 'Powerleveling',
												'S' => 'Supplier Order');
				break;
			default:
				$this->summary["report_title"] = HEADING_TITLE;
				break;
		}
	}
}
?>