<?php
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_STORE_CREDIT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_STORE_CREDIT);
}

class store_credit {

    var $identity, $identity_email;
    var $credit_accounts_type, $credit_accounts, $currency_display_decimal;
    var $store_credit_data;
    var $store_opening_data;
    var $store_closing_data;
    var $store_currency_count;
    var $store_credit_data_filename;

    // class constructor
    function store_credit($identity, $identity_email) {
        $this->credit_accounts = array();
        $this->store_credit_data = array();
        $this->store_opening_data = array();
        $this->store_closing_data = array();
        $this->store_currency_count = array();
        $this->store_credit_data_filename = '';
        $this->refund_product_info = array();

        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user

        $this->credit_accounts_type = array('R' => 'Reversible',
            'NR' => 'Non-Reversible',
            'W' => 'Withdrawable'
        );

        $this->credit_accounts_type_short = array('R' => 'RSC',
            'NR' => 'NRSC',
            'W' => 'WSC',
            'POWSC' => 'POW'
        );

        $this->credit_accounts_type_selection = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
            array('id' => 'R', "text" => 'Reversible'),
            array('id' => 'NR', "text" => 'Non-Reversible')
        );

        $this->credit_credits_type_selection = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
            array('id' => 'R', "text" => 'RSC'),
            array('id' => 'NR', "text" => 'NRSC'),
            array('id' => 'W', "text" => 'WSC'),
            array('id' => 'POWSC', "text" => 'PO WSC'),
        );

        $this->credit_accounts_activities_selection = array(array('id' => '', 'text' => 'All Activities'),
            array('id' => 'X', "text" => 'Cancel'),
            array('id' => 'C', "text" => 'Compensate'),
            array('id' => 'MI', "text" => 'Manual Addition'),
            array('id' => 'MR', "text" => 'Manual Deduction'),
            array('id' => 'P', "text" => 'Purchase'),
            array('id' => 'R', "text" => 'Refund'),
            array('id' => 'B', "text" => 'Bonus'),
            array('id' => 'D', "text" => 'Redeem'),
            array('id' => 'S', "text" => 'Store Credit Top Up'),
            array('id' => 'XS', "text" => 'Extra Store Credit')
        );

        $this->currency_display_decimal = 2;
    }

    function get_sc_promotion_percentage($customer_id) {
        $percentage = 0;

        if ($customer_id) {
            $customers_groups_select_sql = "SELECT customers_groups_id 
                                            FROM " . TABLE_CUSTOMERS . " 
                                            WHERE customers_id =  '" . tep_db_input($customer_id) . "'";
            $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
            $customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);

            $extra_sc_select_sql = "SELECT customers_groups_extra_sc
                                    FROM " . TABLE_CUSTOMERS_GROUPS . "
                                    WHERE customers_groups_id = '" . tep_db_input($customers_groups_row['customers_groups_id']) . "'";
            $extra_sc_result_sql = tep_db_query($extra_sc_select_sql);
            $extra_sc_row = tep_db_fetch_array($extra_sc_result_sql);

            if ($extra_sc_row['customers_groups_extra_sc'] > 0) {
                $percentage = $extra_sc_row['customers_groups_extra_sc'];
            }
        }

        return $percentage;
    }

    function search_sc_statement($filename, $session_name) {
        global $view_sc_flow_report_permission;

        $search_type = tep_not_null($_SESSION[$session_name]["search_type"]) ? $_SESSION[$session_name]["search_type"] : (tep_not_null($_REQUEST['search_type']) ? $_REQUEST['search_type'] : (tep_not_null($_SESSION[$session_name]["transaction_id"]) ? '1' : '2'));

        $sc_statement_html = '';

        $report_type_array = array(array('id' => '1', "text" => REPORT_SC_STAT_TYPE_INDIVIDUAL));
        if ($view_sc_flow_report_permission)
            $report_type_array[] = array('id' => '2', "text" => REPORT_SC_STAT_TYPE_SC_FLOW);

        $show_options = array(array('id' => 'DEFAULT', "text" => "Default"),
            array('id' => '10', "text" => "10"),
            array('id' => '20', "text" => "20"),
            array('id' => '50', "text" => "50")
        );

        ob_start();
        ?>
        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="20%">&nbsp;</td>
                            <td>
                                <?= tep_draw_form('sc_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '') ?>
                                <?= tep_draw_hidden_field('search_type', (tep_not_null($search_type) ? $search_type : '1')) ?>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main"><?= ENTRY_SC_STAT_REPORT_TYPE ?></td>
                                        <td class="main"><?= tep_draw_pull_down_menu("report", $report_type_array, $_SESSION[$session_name]["report"], ' id="report" onChange="swap_fields(this.value)"') ?></td>
                                        <td class="main" colspan="4">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                </table>

                                <div ID="search-tab">
                                    <ul>
                                        <?php
                                        if ($search_type == '1')
                                            echo '<li class="ui-tabs-selected">';
                                        else
                                            echo '<li class="">';
                                        ?>
                                        <a href="#tab-quick" onClick="javascript:document.sc_criteria.search_type.value = '1';"><span><?= ENTRY_SC_STAT_QUICK_SEARCH ?></span></a></li>
                                        <?php
                                        if ($search_type == '2')
                                            echo '<li class="ui-tabs-selected">';
                                        else
                                            echo '<li class="">';
                                        ?>
                                        <a href="#tab-advanced" onClick="javascript:document.sc_criteria.search_type.value = '2';"><span><?= ENTRY_SC_STAT_ADVANCED_SEARCH ?></span></a></li>
                                    </ul>
                                    <div id="tab-quick" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_TRANSACTION_ID ?></td>
                                                <td class="main"><?= tep_draw_input_field('transaction_id', $_SESSION[$session_name]["transaction_id"], ' id="transaction_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"') ?></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main">&nbsp;</td>
                                                <td class="smallText" valign="top" align="left">
                                                    <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton') ?>
                                                    <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div id="tab-advanced" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_STAT_USER_ID ?></td>
                                                <td class="main" valign="top" colspan="6">
                                                    <?= tep_draw_input_field('customer_id', $_SESSION[$session_name]["user_id"], ' id="customer_id" size="30" ') ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_STAT_START_DATE ?></td>
                                                <td class="main" valign="top" nowrap>
                                                    <script language="javascript"><!--
                                                    var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "sc_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
                                                        date_start_date.writeControl();
                                                        date_start_date.dateFormat = "yyyy-MM-dd";
                                                        document.getElementById('start_date').value = '<?= $_SESSION[$session_name]["start_date"] ?>';
                                                        //--></script>
                                                </td>
                                                <td class="main" width="10%">&nbsp;</td>
                                                <td class="main" valign="top" nowrap><?= ENTRY_SC_STAT_END_DATE ?></td>
                                                <td class="main">&nbsp;</td>
                                                <td class="main" valign="top" colspan="4">
                                                    <script language="javascript"><!--
                                                    var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "sc_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
                                                        date_end_date.writeControl();
                                                        date_end_date.dateFormat = "yyyy-MM-dd";
                                                        document.getElementById('end_date').value = '<?= $_SESSION[$session_name]["end_date"] ?>';
                                                        //--></script>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_TYPE ?></td>
                                                <td class="main"><?= tep_draw_pull_down_menu("sc_type", $this->credit_credits_type_selection, tep_not_null($_SESSION[$session_name]["sc_type"]) ? $_SESSION[$session_name]["sc_type"] : '', 'id="sc_type" onChange="credit_type_check(this.value)"') ?></td>
                                                <td class="main">&nbsp;</td>
                                                <td class="main"><?= ENTRY_SC_ACTIVITY ?></td>
                                                <td class="main">&nbsp;</td>
                                                <td class="main"><?= tep_draw_pull_down_menu("sc_activity", $this->credit_accounts_activities_selection, tep_not_null($_SESSION[$session_name]["sc_activity"]) ? $_SESSION[$session_name]["sc_activity"] : '', 'id="sc_activity"') ?></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_RECORDS_PER_PAGE ?></td>
                                                <td class="main"><?= tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '') ?></td>
                                                <td class="main">&nbsp;</td>
                                                <td colspan="3">
                                                    <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton') ?>
                                                    <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                                    <?= tep_submit_button('Export All as CSV', 'Export as csv file', 'id="doexport" onClick="return form_checking(this.form, \'do_export\');"', 'inputButton'); ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                </form>
                            </td>
                            <td width="20%">&nbsp;</td>
                        </tr>
                        <tr>
                            <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <script language="javascript"><!--
                function form_checking(form_obj, action) {
                var error_message = '<?= JS_ERROR ?>';
                var form_error = false;

                var sel_report = DOMCall('report').value;
                var search_type = form_obj.search_type.value;

                if (search_type == '1') {					// Quick Search
                    // transaction ID is mandatory field
                    if (trim_str(DOMCall('transaction_id').value) == '') {
                        error_message += '* <?= JS_ERROR_SC_STAT_EMPTY_TRANSACTION_ID ?>' + "\n";
                        form_error = true;
                    }
                } else if (search_type == '2') {			// Advanced Search
                    if (sel_report == '1') {				// Report Type by Customer
                        // customer email is mandatory field
                        // change customer email to customer id
                        if (trim_str(DOMCall('customer_id').value) == '') {
                            error_message += '* <?= JS_ERROR_SC_STAT_EMPTY_USER_ID ?>' + "\n";
                            form_error = true;
                        }
                    } else if (sel_report == '2') {			// Report Type by Movement
                        // start date, end date and credit type are mandatory fields
                        var start_date = DOMCall('start_date').value;
                        if (start_date.length > 0) {
                            if (!validateDate(start_date)) {
                                error_message += '* Start date is not a valid date format as requested!' + "\n";
                                form_error = true;
                            }
                        } else {
                            error_message += '* Please enter start date!' + "\n";
                            form_error = true;
                        }

                        var end_date = DOMCall('end_date').value;
                        if (end_date.length > 0) {
                            if (!validateDate(end_date)) {
                                error_message += '* End date is not a valid date format as requested!' + "\n";
                                form_error = true;
                            }
                        } else {
                            error_message += '* Please enter end date!' + "\n";
                            form_error = true;
                        }

                        if (start_date.length > 0 && end_date.length > 0) {
                            if (!validStartAndEndDate(start_date, end_date)) {
                                error_message += '* Start Date is greater than End Date!' + "\n";
                                form_error = true;
                            }
                            if (!validStartAndEndDateRange(start_date, end_date, 100)) {
                                error_message += '* Start Date to End Date must be within 100 days!' + "\n";
                                form_error = true;
                            }
                        }

                        var sc_type = DOMCall('sc_type').value;
                        if (sc_type.length <= 0) {
                            error_message += '* Please select a Store Credit Type!' + "\n";
                            form_error = true;
                        }
                    }
                }

                if (form_error) {
                    alert(error_message);
                } else {
                    if (action == 'do_search') {
                        form_obj.action = "<?= tep_href_link($filename, 'action=show_report'); ?>";
                    } else if (action == 'do_export') {
                        form_obj.action = "<?= tep_href_link($filename, 'action=export_report'); ?>";
                    }
                }

                return !form_error;
            }

            function validStartAndEndDateRange(start_date, end_date, range) {
                var MinMilli = 1000 * 60;
                var HrMilli = MinMilli * 60;
                var DyMilli = HrMilli * 24;
                var RangeMilli = DyMilli * range;

                if (start_date.indexOf(':') > 1) {
                    var dateTimeArray = start_date.split(' ');
                    var startDate = dateTimeArray[0].split('-');
                    var startTime = dateTimeArray[1].split(':');
                    var startDateObj = new Date(startDate[0], startDate[1] - 1, startDate[2], startTime[0], startTime[1], '00');
                } else {
                    var startDate = start_date.split('-');
                    var startDateObj = new Date(startDate[0], startDate[1] - 1, startDate[2]);
                }

                if (end_date.indexOf(':') > 1) {
                    var dateTimeArray = end_date.split(' ');
                    var endDate = dateTimeArray[0].split('-');
                    var endTime = dateTimeArray[1].split(':');
                    var endDateObj = new Date(endDate[0], endDate[1] - 1, endDate[2], endTime[0], endTime[1], '00');
                } else {
                    var endDate = end_date.split('-');
                    var endDateObj = new Date(endDate[0], endDate[1] - 1, endDate[2]);
                }

                if (endDateObj.getTime() - startDateObj.getTime() > RangeMilli) {
                    return false;
                } else {
                    return true;
                }
            }

            function resetControls(controlObj) {
                if (trim_str(controlObj.value) != '') {
                    //document.sc_criteria.user_email.value = '';
                    //document.sc_criteria.start_date.value = '';
                    //document.sc_criteria.end_date.value = '';
                    //document.sc_criteria.sc_type.selectedIndex = 0;
                    //document.sc_criteria.sc_activity.selectedIndex = 0;
                    //document.sc_criteria.show_records.selectedIndex = 0;
                } else {
                    controlObj.value = '';
                }
            }

            function swap_fields(selected) {
                if (selected == '1') {
                    //DOMCall('email_div').className = 'show_row';
                    //DOMCall('custom_section_tbody').className = 'show';
                    DOMCall('doexport').disabled = true;
                    DOMCall('customer_id').disabled = false;
                } else if (selected == '2') {
                    //DOMCall('email_div').className = 'hide_row';
                    //DOMCall('custom_section_tbody').className = 'hide';
                    DOMCall('doexport').disabled = false;
                    DOMCall('customer_id').disabled = true;
                } else {
                    //DOMCall('custom_section_tbody').className = 'hide';
                    DOMCall('doexport').disabled = true;
                    DOMCall('customer_id').disabled = false;
                }

                credit_type_check(document.getElementById('sc_type').value);
            }

            function credit_type_check(selected) {
                if (selected == 'W') {
                    DOMCall('sc_activity').disabled = true;
                } else {
                    DOMCall('sc_activity').disabled = false;
                }
            }

            function init() {
                swap_fields(document.getElementById('report').value);
            }
            init();
        //-->
        </script>
        <?php
        $sc_statement_html = ob_get_contents();
        ob_end_clean();

        return $sc_statement_html;
    }

    function show_sc_statement($filename, $session_name, $input_array, &$messageStack) {
        global $currencies, $languages_id, $view_sc_flow_report_permission;

        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["report"] = (isset($input_array["report"]) ? $input_array["report"] : '1');
            $_SESSION[$session_name]["customer_id"] = $input_array["customer_id"];
            $_SESSION[$session_name]["start_date"] = $input_array["start_date"];
            $_SESSION[$session_name]["end_date"] = $input_array["end_date"];
            $_SESSION[$session_name]["transaction_id"] = $input_array["transaction_id"];
            $_SESSION[$session_name]["sc_type"] = $input_array["sc_type"];
            $_SESSION[$session_name]["sc_activity"] = $input_array["sc_activity"];
            $_SESSION[$session_name]["show_records"] = $input_array["show_records"];
            $_SESSION[$session_name]["search_type"] = $input_array["search_type"];

            // if cache data not exist, query from database and write to cache
            $this->_load_database_data($session_name, $input_array, $messageStack);
            $_SESSION[$session_name]["store_credit_data"] = $this->store_credit_data_filename;
        } else if ($_REQUEST['cont'] == '2') {
            if (!tep_not_null($_SESSION[$session_name]["report"])) {
                $_SESSION[$session_name]["report"] = '1';
            }
            if (!tep_not_null($_SESSION[$session_name]["customer_id"])) {
                $_SESSION[$session_name]["customer_id"] = $input_array["user_id"];
            }
            if (!tep_not_null($_SESSION[$session_name]["sc_type"])) {
                $_SESSION[$session_name]["sc_type"] = $input_array["sc_type"];
            }
            $_SESSION[$session_name]["search_type"] = '2';
            $this->_load_database_data($session_name, $_SESSION[$session_name], $messageStack);
        } else {
            // if cache data exist, read from cache
            $this->store_credit_data_filename = $_SESSION[$session_name]["store_credit_data"];
            $this->_load_cache_data($this->store_credit_data_filename, $session_name);
        }

        $rec_per_page = ($_SESSION[$session_name]["show_records"] == 'DEFAULT') ? MAX_DISPLAY_SEARCH_RESULTS : $_SESSION[$session_name]["show_records"];

        $paging_array_data = $this->paging_array_data($this->store_credit_data, $rec_per_page, $input_array);
        $display_data = $paging_array_data['display_data'];

        ob_start();

        switch ($_SESSION[$session_name]["report"]) {
            case "1": // By customer
                if (count($display_data) > 0) {
                    $data_arr_by_date = current($display_data);
                    $data_arr = current($data_arr_by_date);
                }
                if (isset($data_arr['customer_id'])) {
                    $_SESSION[$session_name]["user_id"] = $data_arr['customer_id'];
                } else if (isset($_SESSION[$session_name]["user_id"])) {
                    ; // Still need this line else New Customer with no SC Records will failed
                } else {
                    unset($_SESSION[$session_name]["user_id"]);
                }

                $user_info_array = $this->_get_user_particulars($_SESSION[$session_name]["user_id"]);

                $credit_reserved_amount_array = $this->_get_credit_reserve_amount($_SESSION[$session_name]["user_id"]);
                ?>
                <table width="100%" border="0" cellspacing="2" cellpadding="2">
                    <tr>
                        <td>
                            <table border="0" width="100%" align="center" cellpadding="0" cellspacing="0">
                                <td valign="bottom">
                                    <?php $user_name_link = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $_SESSION[$session_name]["user_id"] . '&action=edit') . '" target="_blank">' . $user_info_array['fname'] . ' ' . $user_info_array['lname'] . '</a>'; ?>
                                    <?= tep_not_null($user_info_array['email']) ? sprintf(TABLE_SECTION_HEADING_SC_STAT, $user_name_link, $_SESSION[$session_name]["user_id"]) : '' ?>
                                </td>
                                <td align="right">
                                    <table border="0" cellpadding="0" cellspacing="0">
                                        <tr>
                                            <td class="main" align="right"><b><?= TEXT_SC_STAT_DISABLE_WITHDRAWAL ?>:</b></td>
                                            <td align="right" class="main">&nbsp;<?= ($user_info_array['disable_withdrawal'] == '1' ? '<span class="redIndicator">ON</span>' : '<span class="greenIndicator">OFF</span>' ) ?></td>
                                        </tr>
                                        <? $currencies->set_show_currency_symbol(); ?>
                                        <tr>
                                            <td class="main" align="right"><b><?= TEXT_SC_STAT_REVERSIBLE_RESERVED ?>:</b></td>
                                            <td align="right" class="main">&nbsp;<?= ($user_info_array['sc_currency_id'] == '' ? '0.00' : $currencies->format($credit_reserved_amount_array['sc_reverse_reserve_amt'], false, $currencies->get_code_by_id($user_info_array['sc_currency_id']))) ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" align="right"><b><?= TEXT_SC_STAT_IRREVERSIBLE_RESERVED ?>:</b></td>
                                            <td align="right" class="main">&nbsp;<?= ($user_info_array['sc_currency_id'] == '' ? '0.00' : $currencies->format($credit_reserved_amount_array['sc_irreverse_reserve_amt'], false, $currencies->get_code_by_id($user_info_array['sc_currency_id']))) ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" align="right"><b><?= TEXT_SC_STAT_WITHDRAWABLE_RESERVED ?>:</b></td>
                                            <td align="right" class="main">&nbsp;<?= $currencies->format($credit_reserved_amount_array['store_account_reserve_amt']) ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                                <tr>
                                    <td width="12%" class="reportBoxHeading" rowspan="2"><?= TABLE_HEADING_SC_STAT_DATETIME ?></td>
                                    <td width="35" class="reportBoxHeading" rowspan="2"><?= TABLE_HEADING_SC_STAT_ACTIVITY ?></td>
                                    <td width="8%" align="center" class="reportBoxHeading" rowspan="2"><?= TABLE_HEADING_SC_STAT_CURRENCY ?></td>
                                    <td width="15%" class="reportBoxHeading" align="center" colspan="3"><?= TABLE_HEADING_SC_STAT_RSC ?></td>
                                    <td width="15%" class="reportBoxHeading" align="center" colspan="3"><?= TABLE_HEADING_SC_STAT_NRSC ?></td>
                                    <td width="15%" class="reportBoxHeading" align="center" colspan="3"><?= TABLE_HEADING_SC_STAT_WSC ?></td>
                                </tr>
                                <tr>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_MINUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_PLUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_BALANCE ?></td>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_MINUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_PLUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_BALANCE ?></td>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_MINUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SC_STAT_PLUS ?></td>
                                    <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_BALANCE ?></td>
                                </tr>
                                <?php
                                $currencies->set_hide_currency_symbol();
                                $row_count = 0;
                                //while ($sc_statement_row = tep_db_fetch_array($sc_statement_result_sql)) {
                                if (count($display_data) > 0) {
                                    foreach ($display_data as $idx_activity_date => $sc_statement_row_by_date) {
                                        foreach ($sc_statement_row_by_date as $idx_history_id => $sc_statement_row) {
                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                            $activity_info = '';
                                            $reserve_link = ''; //TEXT_SC_STAT_NOT_APPLICABLE;

                                            if (tep_not_null($sc_statement_row['trans_type']) && ($sc_statement_row['trans_type'] != 'C')) {
                                                if ($sc_statement_row['credit_amount'] > 0) {
                                                    if ($sc_statement_row['trans_reserved'] == '1') { // Currently is reserved
                                                        $reserve_link = sprintf(LINK_SC_STAT_LIFT_RESERVE, "reserveSCTrans('" . $sc_statement_row['history_id'] . "', '" . $_SESSION[$session_name]["user_id"] . "', '0', 'reserve_div_" . $row_count . "', '" . $sc_statement_row['account_type'] . "');");
                                                    } else { // Currently is not reserved
                                                        $reserve_link = sprintf(LINK_SC_STAT_RESERVE, "reserveSCTrans('" . $sc_statement_row['history_id'] . "', '" . $_SESSION[$session_name]["user_id"] . "', '1', 'reserve_div_" . $row_count . "', '" . $sc_statement_row['account_type'] . "');");
                                                    }
                                                }
                                            }

                                            $sc_history_id_str = $activity_title = $activity_type = $sc_sales_id_str = '';
                                            if (tep_not_null($sc_statement_row['activity_title'])) {
                                                if (strtolower(trim($sc_statement_row['activity_title'])) == 'manual deduction' ||
                                                        strtolower(trim($sc_statement_row['activity_title'])) == 'manual addition') {
                                                    $sc_history_id_str = sprintf(TEXT_SC_STAT_TRANS_ID, $sc_statement_row['history_id']);
                                                } else if (strtolower(trim($sc_statement_row['activity_title'])) == 'store credit sales') {
                                                    $sc_sales_id_str = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_CUSTOMER_ORDER, $sc_statement_row['trans_id']) . '</a>';
                                                } else if (strtolower(trim($sc_statement_row['activity_title'])) == 'offgamers payment') {
                                                    $sc_statement_row['activity_title'] = sprintf(TITLE_TRANS_PAYMENT_WITHDRAW, $sc_statement_row['history_id']);
                                                }
                                                $activity_title = (tep_not_null($sc_sales_id_str) ? $sc_sales_id_str . ' - ' : '') . $sc_statement_row['activity_title'] . (($sc_statement_row['added_by_role'] == 'admin') ? ' (by ' . $sc_statement_row['added_by'] . ')' : '') . (($sc_statement_row['activity_desc_show']) ? tep_image(DIR_WS_ICONS . 'tick.gif', TEXT_SC_STAT_MANUAL_ACTIVITY_SHOW) : tep_image(DIR_WS_ICONS . 'cross.gif', TEXT_SC_STAT_MANUAL_ACTIVITY_HIDE)) . (tep_not_null($sc_history_id_str) ? '<br>' . $sc_history_id_str : '') . (tep_not_null($sc_statement_row['activity_desc']) ? '<br><div class="paymentRemarkSelectedRow">Comment:<br>' . nl2br($sc_statement_row['activity_desc']) . '</div>' : '');
                                            } else {
                                                if ($sc_statement_row['account_type'] == 'WSC' || $sc_statement_row['account_type'] == 'POWSC') {
                                                    // Store Credit Type = WSC or PO WSC
                                                    switch ($sc_statement_row['trans_type']) {
                                                        case 'A':
                                                            $activity_title = TEXT_SC_STAT_ACTIVITY_AFFILIATE . ' ' . $sc_statement_row['trans_id'];
                                                            break;
                                                        case 'B':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id=' . $sc_statement_row['trans_id'], 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_BUYBACK . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                            break;
                                                        case 'P':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_PAYMENT, 'payID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PAYMENT . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                            break;
                                                        case 'PWL':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PWL . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                            break;
                                                        case 'PO':
                                                            require_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
                                                            require_once(DIR_WS_CLASSES . 'dtu_payment.php');
                                                            require_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
                                                            require_once(DIR_WS_CLASSES . 'consignment_payment.php');
                                                            $is_dtu_po = dtu_payment::is_dtu_payment($sc_statement_row['trans_id']);
                                                            $is_api_po = api_replenish_payment::is_po_payment($sc_statement_row['trans_id']);
                                                            $is_cdk_po = consignment_payment::is_cdk_payment($sc_statement_row['trans_id']);
                                                            if($is_dtu_po > 0) {
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'po_id=' . $sc_statement_row['trans_id'] . '&subaction=edit_dtu', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_DTU . ' ' . dtu_payment::get_po_ref_num_by_id($sc_statement_row['trans_id']) . '</a>';
                                                            } else if($is_api_po > 0) {
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'po_id=' . $sc_statement_row['trans_id'] . '&subaction=edit_po', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_API . ' ' . api_replenish_payment::get_po_ref_num_by_id($sc_statement_row['trans_id']) . '</a>';
                                                            } else if($is_cdk_po > 0) {
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_CDK_PAYMENT, 'po_id=' . $sc_statement_row['trans_id'] . '&subaction=edit_cdk', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_CDK . ' ' . consignment_payment::get_po_ref_num_by_id($sc_statement_row['trans_id']) . '</a>';
                                                            } else {
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'po_id=' . $sc_statement_row['trans_id'] . '&subaction=edit_po', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PO . ' ' . edit_purchase_orders::get_po_ref_num_by_id($sc_statement_row['trans_id']) . '</a>';
                                                            }
                                                            break;
                                                        case 'SO':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'selected_box=c2c&action=add_form&id=' . $sc_statement_row['trans_id'], 'NONSSL') . '" target="_blank">' . TEXT_SC_STAT_ACTIVITY_G2G_SELL_ORDER . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                            break;
                                                        default:
                                                            $activity_title = $sc_statement_row['activity_title'];
                                                            break;
                                                    }
                                                } else {
                                                    switch ($sc_statement_row['trans_type']) {
                                                        case 'C':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_CUSTOMER_ORDER, $sc_statement_row['trans_id']) . '</a>';
                                                            break;
                                                        default:
                                                            $activity_title = $sc_statement_row['trans_type'] . ' ' . $sc_statement_row['trans_id'];
                                                            break;
                                                    }

                                                    switch ($sc_statement_row['activity_type']) {
                                                        case 'C':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_COMPENSATE;
                                                            break;
                                                        case 'P':
                                                        case 'RP':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_PURCHASE;
                                                            break;
                                                        case 'R':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_REFUND;
                                                            break;
                                                        case 'X':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_CANCEL;
                                                            break;
                                                        case 'D':
                                                            $activity_title = '<a href="' . tep_href_link(FILENAME_REDEEM, 'redeemID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="redeem">' . sprintf(TITLE_TRANS_REDEEM_RECORD, $sc_statement_row['trans_id']) . '</a>';
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_REDEEM;
                                                            $activity_info = '<br>' . sprintf(TEXT_SC_STAT_TRANS_ID, $sc_statement_row['history_id']);
                                                            break;
                                                        case 'PW':
                                                            $activity_title = sprintf(TITLE_TRANS_PAYMENT_WITHDRAW, $sc_statement_row['history_id']);
                                                            break;
                                                        case 'S':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_SC;
                                                            if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                                $activity_type .= TEXT_DEDUCT;
                                                            }
                                                            break;
                                                        case 'XS':
                                                            $activity_type = TEXT_SC_STAT_ACTIVITY_EXTRA_SC;
                                                            if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                                $activity_type .= TEXT_DEDUCT;
                                                            }
                                                            break;
                                                    }
                                                }

                                                if ($sc_statement_row['activity_type'] == 'XS') {
                                                    if (tep_not_null($activity_type))
                                                        $activity_title = $activity_type . ' - ' . $activity_title;
                                                } else {
                                                    if (tep_not_null($activity_type))
                                                        $activity_title .= ' - ' . $activity_type;
                                                }
                                                $activity_title = $activity_title . (($sc_statement_row['added_by_role'] == 'admin') ? ' (by ' . $sc_statement_row['added_by'] . ')' : '') . (($sc_statement_row['activity_desc_show']) ? tep_image(DIR_WS_ICONS . 'tick.gif', TEXT_SC_STAT_MANUAL_ACTIVITY_SHOW) : tep_image(DIR_WS_ICONS . 'cross.gif', TEXT_SC_STAT_MANUAL_ACTIVITY_HIDE)) . $activity_info;
                                                if (tep_not_null($reserve_link))
                                                    $activity_title .= '&nbsp;&nbsp;' . '<div id="reserve_div_' . $row_count . '">' . $reserve_link . '</div>';
                                                if (tep_not_null($sc_statement_row['activity_desc']))
                                                    $activity_title .= '<br><div class="paymentRemarkSelectedRow">Comment:<br>' . nl2br($sc_statement_row['activity_desc']) . '</div>';
                                            }

                                            echo '	<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
									<td valign="top" class="reportRecords" nowrap>' . $sc_statement_row['activity_date'] . '</td>
									<td valign="top" class="reportRecords">' . $activity_title . '</td>
									<td align="center" valign="top" class="reportRecords" nowrap>' . $sc_statement_row['currency'] . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'R' ? (tep_not_null($sc_statement_row['debit_amount']) ? ($sc_statement_row['debit_amount'] >= 0 ? $currencies->format($sc_statement_row['debit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['debit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'R' ? (tep_not_null($sc_statement_row['credit_amount']) ? ($sc_statement_row['credit_amount'] >= 0 ? $currencies->format($sc_statement_row['credit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['credit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'R' ? (tep_not_null($sc_statement_row['r_after_balance']) ? ($sc_statement_row['r_after_balance'] >= 0 ? $currencies->format($sc_statement_row['r_after_balance']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['r_after_balance']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'NR' ? (tep_not_null($sc_statement_row['debit_amount']) ? ($sc_statement_row['debit_amount'] >= 0 ? $currencies->format($sc_statement_row['debit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['debit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'NR' ? (tep_not_null($sc_statement_row['credit_amount']) ? ($sc_statement_row['credit_amount'] >= 0 ? $currencies->format($sc_statement_row['credit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['credit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'NR' ? (tep_not_null($sc_statement_row['nr_after_balance']) ? ($sc_statement_row['nr_after_balance'] >= 0 ? $currencies->format($sc_statement_row['nr_after_balance']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['nr_after_balance']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'W' ? (tep_not_null($sc_statement_row['debit_amount']) ? ($sc_statement_row['debit_amount'] >= 0 ? $currencies->format($sc_statement_row['debit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['debit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'W' ? (tep_not_null($sc_statement_row['credit_amount']) ? ($sc_statement_row['credit_amount'] >= 0 ? $currencies->format($sc_statement_row['credit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['credit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									<td align="right" valign="top" class="reportRecords" nowrap>' . ($sc_statement_row['account_type'] == 'W' ? (tep_not_null($sc_statement_row['r_after_balance']) ? ($sc_statement_row['r_after_balance'] >= 0 ? $currencies->format($sc_statement_row['r_after_balance']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['r_after_balance']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
									</tr>';

                                            $row_count++;
                                        }
                                    }
                                }
                                ?>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td><?= $paging_array_data['display_info'] ?></td>
                    </tr>
                </table>
                <?php
                $currencies->set_show_currency_symbol();
                break;
            case "2":
                if ($view_sc_flow_report_permission) {
                    ?>
                    <table width="100%" border="0" cellspacing="2" cellpadding="2">
                        <tr>
                            <td>
                                <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                                    <tr>
                                        <td width="5%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_TRANS_ID ?></td>
                                        <td width="9%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_DATETIME ?></td>
                                        <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_ACTIVITY ?></td>
                                        <td width="12%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_ADDED_BY ?></td>
                                        <td width="12%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_CUST_ID ?></td>
                                        <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_PG ?></td>
                                        <td width="18%" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_COMMENT ?></td>
                                        <td width="6%" align="center" class="reportBoxHeading"><?= TABLE_HEADING_SC_STAT_CURRENCY ?></td>
                                        <td width="4%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_DEBIT ?></td>
                                        <td width="4%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_CREDIT ?></td>
                                        <td width="4%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SC_STAT_BALANCE . " (" . $this->credit_accounts_type_short[($_SESSION[$session_name]["search_type"] == '1' ? $_SESSION[$session_name]["type_from_transaction"] : ($_SESSION[$session_name]["sc_type"] ? $_SESSION[$session_name]["sc_type"] : $_SESSION[$session_name]["type_from_transaction"]))] . ")" ?></td>
                                    </tr>
                                    <?php
                                    if (($_SESSION[$session_name]["search_type"] == '2') && (($paging_array_data['current_page_number'] == 1) || (strtoupper($_REQUEST['page']) == 'ALL'))) {
                                        // Display first row of Closing Balance as at end_date + 1day
                                        echo $this->_display_store_credits_daily_history($this->store_closing_data, 'C', $_SESSION[$session_name]["sc_type"]);
                                    }

                                    $currencies->set_hide_currency_symbol();
                                    $row_count = 0;
                                    //while ($sc_statement_row = tep_db_fetch_array($sc_statement_result_sql)) {
                                    if (count($display_data) > 0) {

                                        $payment_gateway_array = array();
                                        $payment_gateway_select_sql = "	SELECT payment_methods_id, payment_methods_title
													FROM " . TABLE_PAYMENT_METHODS . "
													WHERE payment_methods_parent_id = '0' ";
                                        $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
                                        while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
                                            $payment_gateway_array[$payment_gateway_row['payment_methods_id']] = $payment_gateway_row['payment_methods_title'];
                                        }

                                        foreach ($display_data as $idx_activity_date => $sc_statement_row_by_date) {
                                            foreach ($sc_statement_row_by_date as $idx_history_id => $sc_statement_row) {

                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                                $activity_title = $activity_type = '';

                                                $sc_history_id_str = sprintf(TEXT_SC_STAT_TRANS_ID, $sc_statement_row['history_id']);
                                                $sc_history_by_str = (($sc_statement_row['added_by_role'] == 'admin') ? $sc_statement_row['added_by'] : $sc_statement_row['customer_id']);

                                                if (tep_not_null($sc_statement_row['activity_title'])) {
                                                    $activity_title = $sc_statement_row['activity_title'];
                                                } else {
                                                    if ($_SESSION[$session_name]["sc_type"] == 'W') {
                                                        // Store Credit Type = WSC
                                                        switch ($sc_statement_row['trans_type']) {
                                                            case 'B':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id=' . $sc_statement_row['trans_id'], 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_BUYBACK . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                                break;
                                                            case 'P':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_PAYMENT, 'payID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PAYMENT . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                                break;
                                                            case 'PWL':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PWL . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                                break;
                                                            case 'SO':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'selected_box=c2c&action=add_form&id=' . $sc_statement_row['trans_id'], 'NONSSL') . '" target="_blank">' . TEXT_SC_STAT_ACTIVITY_G2G_SELL_ORDER . ' ' . $sc_statement_row['trans_id'] . '</a>';
                                                                break;
                                                            default:
                                                                $activity_title = $sc_statement_row['activity_title'];
                                                                break;
                                                        }
                                                    } else if ($_SESSION[$session_name]["sc_type"] == 'POWSC') {
                                                        switch ($sc_statement_row['trans_type']) {
                                                            case 'PO':
                                                                require_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'po_id=' . $sc_statement_row['trans_id'] . '&subaction=edit_po', 'NONSSL') . '" target="payment">' . TEXT_SC_STAT_ACTIVITY_PO . ' ' . edit_purchase_orders::get_po_ref_num_by_id($sc_statement_row['trans_id']) . '</a>';
                                                                break;
                                                            default:
                                                                $activity_title = $sc_statement_row['activity_title'];
                                                                break;
                                                        }
                                                    } else {
                                                        // Store Credit Type = RSC and NRSC
                                                        switch ($sc_statement_row['trans_type']) {
                                                            case 'C':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_CUSTOMER_ORDER, $sc_statement_row['trans_id']) . '</a>';
                                                                break;
                                                            default:
                                                                $activity_title = $sc_statement_row['trans_type'] . ' ' . $sc_statement_row['trans_id'];
                                                                break;
                                                        }

                                                        switch ($sc_statement_row['activity_type']) {
                                                            case 'C':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_COMPENSATE;
                                                                break;
                                                            case 'P':
                                                            case 'RP':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_PURCHASE;
                                                                break;
                                                            case 'R':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_REFUND;
                                                                break;
                                                            case 'D':
                                                                $activity_title = '<a href="' . tep_href_link(FILENAME_REDEEM, 'redeemID=' . $sc_statement_row['trans_id'] . '&action=edit', 'NONSSL') . '" target="redeem">' . sprintf(TITLE_TRANS_REDEEM_RECORD, $sc_statement_row['trans_id']) . '</a>';
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_REDEEM;
                                                                $activity_info = '<br>' . sprintf(TEXT_SC_STAT_TRANS_ID, $sc_statement_row['history_id']);
                                                                break;
                                                            case 'S':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_SC;
                                                                if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                                    $activity_type .= TEXT_DEDUCT;
                                                                }
                                                                break;
                                                            case 'X':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_CANCEL;
                                                                break;
                                                            case 'XS':
                                                                $activity_type = TEXT_SC_STAT_ACTIVITY_EXTRA_SC;
                                                                if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                                    $activity_type .= TEXT_DEDUCT;
                                                                }
                                                                break;
                                                        }

                                                        if ($sc_statement_row['activity_type'] == 'XS') {
                                                            if (tep_not_null($activity_type))
                                                                $activity_title = $activity_type . ' - ' . $activity_title;
                                                        } else {
                                                            if (tep_not_null($activity_type))
                                                                $activity_title .= ' - ' . $activity_type;
                                                        }
                                                    }
                                                }

                                                $reserve_link = TEXT_SC_STAT_NOT_APPLICABLE;

                                                if (tep_not_null($sc_statement_row['trans_type']) && ($sc_statement_row['trans_type'] != 'C')) {
                                                    if ($sc_statement_row['credit_amount'] > 0) {
                                                        if ($sc_statement_row['trans_reserved'] == '1') { // Currently is reserved
                                                            $reserve_link = sprintf(LINK_SC_STAT_LIFT_RESERVE, "reserveSCTrans('" . $sc_statement_row['history_id'] . "', '" . $_SESSION[$session_name]["user_id"] . "', '0', 'reserve_div_" . $row_count . "', '" . $sc_statement_row['account_type'] . "');");
                                                        } else { // Currently is not reserved
                                                            $reserve_link = sprintf(LINK_SC_STAT_RESERVE, "reserveSCTrans('" . $sc_statement_row['history_id'] . "', '" . $_SESSION[$session_name]["user_id"] . "', '1', 'reserve_div_" . $row_count . "', '" . $sc_statement_row['account_type'] . "');");
                                                        }
                                                    }
                                                }

                                                //						if ($_SESSION[$session_name]["sc_type"] != 'W') {
                                                //							$customer_select_sql = "SELECT customers_email_address
                                                //													FROM ".TABLE_CUSTOMERS."
                                                //													WHERE customers_id = '".tep_db_input($sc_statement_row['customer_id'])."'
                                                //													LIMIT 1";
                                                //						} else {
                                                //							$customer_select_sql = "SELECT supplier_email_address as customers_email_address
                                                //													FROM ".TABLE_SUPPLIER."
                                                //													WHERE supplier_id = '".tep_db_input($sc_statement_row['customer_id'])."'
                                                //													LIMIT 1";
                                                //						}
                                                //						$customer_result_sql = tep_db_query($customer_select_sql);
                                                //						$customer_row = tep_db_fetch_array($customer_result_sql);

                                                list($rec_date, $rec_time) = explode(' ', $sc_statement_row['activity_date']);
                                                list($rec_year, $rec_month, $rec_day) = explode('-', $rec_date);

                                                $payment_gateway = '';
                                                if (!tep_not_null($sc_statement_row['activity_title']) && isset($sc_statement_row['trans_type']) && strtolower($sc_statement_row['trans_type']) == 'c' && isset($sc_statement_row['trans_id']) && $sc_statement_row['trans_id'] > 0) {
                                                    $payment_gateway_select_sql = "	SELECT payment_methods_parent_id
                                                                FROM " . TABLE_ORDERS . "
                                                                WHERE orders_id = '" . $sc_statement_row['trans_id'] . "'";
                                                    $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
                                                    $payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql);
                                                    if ($payment_gateway_row['payment_methods_parent_id'] > 0) {
                                                        $payment_gateway = (isset($payment_gateway_array[$payment_gateway_row['payment_methods_parent_id']]) ? $payment_gateway_array[$payment_gateway_row['payment_methods_parent_id']] : '');
                                                    }
                                                }

                                                echo '	<tr height="20" class="' . $row_style . '" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'' . $row_style . '\')" onclick="rowClicked(this, \'' . $row_style . '\')">
                                    <td valign="top" class="reportRecords" nowrap>' . $sc_statement_row['history_id'] . '</td>
                                    <td valign="top" class="reportRecords" nowrap>' . $sc_statement_row['activity_date'] . '</td>
                                    <td valign="top" class="reportRecords" nowrap>' . $activity_title . '</td>
                                    <td align="left" valign="top" class="reportRecords" nowrap>' . $sc_history_by_str . '</td>
                                    <td align="left" valign="top" class="reportRecords" nowrap><a href="' . tep_href_link(FILENAME_STORE_CREDIT, 'action=show_report&report=1&search_type=2&customer_id=' . $sc_statement_row['customer_id'] . '&sc_type=&start_date=' . (date('Y-m-d', mktime(0, 0, 0, $rec_month, $rec_day, $rec_year))), 'SSL') . '" target="_blank">' . $sc_statement_row['customer_id'] . '</a></td>
                                    <td align="center" valign="top" class="reportRecords" nowrap>' . $payment_gateway . '</td>
                                    <td align="left" valign="top" class="reportRecords">' . nl2br($sc_statement_row['activity_desc']) . '</td>
                                    <td align="center" valign="top" class="reportRecords" nowrap>' . $sc_statement_row['currency'] . '</td>
                                    <td align="right" valign="top" class="reportRecords" nowrap>' . (tep_not_null($sc_statement_row['debit_amount']) ? ($sc_statement_row['debit_amount'] >= 0 ? $currencies->format($sc_statement_row['debit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['debit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
                                    <td align="right" valign="top" class="reportRecords" nowrap>' . (tep_not_null($sc_statement_row['credit_amount']) ? ($sc_statement_row['credit_amount'] >= 0 ? $currencies->format($sc_statement_row['credit_amount']) : '<span class="redIndicator">' . $currencies->format($sc_statement_row['credit_amount']) . '</span>') : TEXT_SC_STAT_NOT_APPLICABLE) . '</td>
                                    <td align="right" valign="top" class="reportRecords" nowrap><span' . ($sc_statement_row['running_balance']['untally'] === 1 ? ' class="redIndicator"' : '') . '>' . $currencies->format($sc_statement_row['running_balance']['amt']) . '</span></td>
                                    </tr>';
                                                $row_count++;
                                            }
                                        }
                                    }
                                    if (($_SESSION[$session_name]["search_type"] == '2') && ((strtoupper($_REQUEST['page']) == 'ALL') || ($paging_array_data['current_page_number'] == $paging_array_data['total_page']))) {
                                        // Display last row of Closing Balance as at start_date
                                        echo $this->_display_store_credits_daily_history($this->store_opening_data, 'O', $_SESSION[$session_name]["sc_type"]);
                                    }
                                    ?>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><?= $paging_array_data['display_info'] ?></td>
                        </tr>
                    </table>
                    <?php
                }
                $currencies->set_show_currency_symbol();
                break;
            default:
                break;
        }

        $report_section_html = ob_get_contents();
        ob_end_clean();

        // This should be here to let all the session get updated value
        $search_section_html = $this->search_sc_statement($filename, $session_name);

        $manual_adjust_section_html = ($_SESSION[$session_name]["report"] == '1') ? $this->_manual_adjust_form($filename, $_SESSION[$session_name]["user_id"]) : '';

        return $search_section_html . "\n" . $report_section_html . "\n" . $manual_adjust_section_html;
    }

    function _get_store_credits_daily_history($store_date = '', $sc_type = '', $user_id = 'system', $user_role = 'system', $export = false) {
        global $currencies;
        $currencies->set_hide_currency_symbol();
        if (!isset($store_date))
            return false;

        $history_array = array();
        if ($store_date) {
            $sc_type_str = (isset($sc_type) && tep_not_null($sc_type)) ? " store_credit_daily_history_credit_type = '" . tep_db_input($sc_type) . "'" : "1";

            $sc_daily_history_select_sql = "SELECT store_credit_daily_history_date,
												store_credit_daily_history_currency,
												store_credit_daily_history_credit_type,
												store_credit_daily_history_amount,
												store_credit_daily_history_reserved_amount,
												0 as untally
											FROM " . TABLE_STORE_CREDIT_DAILY_HISTORY . "
											WHERE store_credit_daily_history_date >= '" . tep_db_input($store_date) . "'
												AND store_credit_daily_history_date < DATE_ADD('" . tep_db_input($store_date) . "', INTERVAL 24 HOUR)
												AND user_id = '" . tep_db_input($user_id) . "'
												AND user_role = '" . tep_db_input($user_role) . "'
												AND " . $sc_type_str . "";
            $sc_daily_history_result_sql = tep_db_query($sc_daily_history_select_sql);

            while ($sc_daily_history_row = tep_db_fetch_array($sc_daily_history_result_sql)) {
                $history_array[$sc_daily_history_row['store_credit_daily_history_credit_type']][$sc_daily_history_row['store_credit_daily_history_date']][$sc_daily_history_row['store_credit_daily_history_currency']] = $sc_daily_history_row;
            }

            $currencies->set_show_currency_symbol();
            return $history_array;
        }
    }

    function _display_store_credits_daily_history($data, $balance_type, $sc_type, $export = false) {
        global $currencies;
        $currencies->set_hide_currency_symbol();

        if (isset($data) && is_array($data)) {
            if ($export === true) {
                $daily_history_row = '';
                foreach ($data as $type => $date_list) {
                    foreach ($date_list as $date => $currency_row) {
                        $currency_str = $balance_str = '';

                        if ($balance_type == 'C') {
                            $balance_title = 'Closing Balance';
                        } else if ($balance_type == 'O') {
                            $balance_title = 'Opening Balance';
                        } else {
                            $balance_title = 'Balance';
                        }

                        $daily_history_row = '"",' .
                                '"' . $balance_title . ' as at ' . $date . '",' .
                                '"",' .
                                '"",' .
                                '"",' .
                                '"",' .
                                '"",' .
                                '"",' .
                                '"",' .
                                '"",';

                        foreach ($currency_row as $currency => $history_data) {
                            $currency_str .= '"' . $currency . '",';
                            $balance_str .= '"' . $currencies->format($history_data['store_credit_daily_history_amount']) . '",';
                        }
                        $daily_history_row .= $currency_str . "\n";
                        $daily_history_row .= '"","","","","","","","","","",' . $balance_str . "\n\n";
                    }
                }
            } else {
                $daily_history_row = '';
                foreach ($data as $type => $date_list) {
                    foreach ($date_list as $date => $currency_row) {
                        $currency_str = $balance_str = '';
                        foreach ($currency_row as $currency => $history_data) {
                            $span_str = '<span';
                            if ($balance_type == 'C' && $history_data['untally'] === 1) {
                                $span_str .= ' class="redIndicator"';
                            }
                            $span_str .= '>';
                            $currency_str .= $currency . '<br>';
                            $balance_str .= $span_str . $currencies->format($history_data['store_credit_daily_history_amount']) . '</span><br>';
                        }

                        if ($balance_type == 'C') {
                            $balance_title = 'Closing Balance';
                            $report_type = 'C';
                        } else if ($balance_type == 'O') {
                            $balance_title = 'Opening Balance';
                            $report_type = 'O';
                        } else {
                            $balance_title = 'Balance';
                            $report_type = 'O';
                        }

                        $daily_history_row .= '
										<tr height="20" class="reportListingSummary" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingSummary\')" onclick="rowClicked(this, \'reportListingSummary\')">
	  										<td valign="top" class="reportRecords" nowrap></td>
											<td valign="middle" class="reportRecords" colspan="6" nowrap><span class="boldText">' . $balance_title . ' as at ' . $date . '</span><br><a href="' . tep_href_link(FILENAME_STORE_CREDIT, 'action=export_summary&report=2&sc_type=' . $sc_type . '&report_type=' . $report_type . '&report_date=' . $date) . '" target="_blank">' . TEXT_SC_STAT_EXPORT_CLOSING_BALANCE . '</a></td>
											<td align="center" valign="top" class="reportRecords" nowrap>' . $currency_str . '</td>
											<td valign="top" class="reportRecords" nowrap colspan="2"></td>
											<td align="right" valign="top" class="reportRecords" nowrap>' . $balance_str . '</td>
										</tr>';
                    }
                }
            }
        } else {
            $daily_history_row = '';
        }

        $currencies->set_show_currency_symbol();
        return $daily_history_row;
    }

    function _load_database_data($session_name, $input_array, &$messageStack) {
        global $currencies;
        unset($_SESSION[$session_name]["type_from_transaction"]); // used when search by transaction ID
        // unset transaction ID if user want to perform advanced search
        $search_type = tep_not_null($_SESSION[$session_name]["search_type"]) ? $_SESSION[$session_name]["search_type"] : (tep_not_null($_REQUEST['search_type']) ? $_REQUEST['search_type'] : (tep_not_null($_SESSION[$session_name]["transaction_id"]) ? '1' : '2'));

        if ($search_type == '1') {  // quick search
            $search_as_report_type_1 = false;
            $filter_transaction_id = false;
            $filter_customer_email = false;
            $filter_sc_type = false;
            $filter_sc_activity = false;
            $filter_start_date = false;
            $filter_end_date = false;

            if (tep_not_null($_SESSION[$session_name]["transaction_id"])) {
                // If report type == 2 and transaction ID is set, do report type 1 combined sql search
                if ($_SESSION[$session_name]["report"] == '2') {
                    $search_as_report_type_1 = true;
                }
                $filter_transaction_id = true;
            }
        } else if ($search_type == '2') {  // advanced search
            if ($_SESSION[$session_name]["report"] == '1') {
                
            } else if ($_SESSION[$session_name]["report"] == '2') {
                
            }
            $filter_customer_email = true;
            $filter_sc_type = true;
            $filter_sc_activity = true;
            $filter_start_date = true;
            $filter_end_date = true;
            $filter_transaction_id = false;
        }

        // Get Store Credit Type
        $sc_type_str = $sc_type_str1 = $sc_type_str2 = "1";
        if ($filter_sc_type) {
            if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
                $sc_type_str1 = (isset($_SESSION[$session_name]["sc_type"]) && tep_not_null($_SESSION[$session_name]["sc_type"])) ? (($_SESSION[$session_name]["sc_type"] == 'W') ? " 1 " : " sch.store_credit_account_type = '" . tep_db_input($_SESSION[$session_name]["sc_type"]) . "'") : "1";
                $sc_type_str2 = "1";
            } else {
                switch ($_SESSION[$session_name]["sc_type"]) {
                    case 'POWSC':
                        $sc_type_str = " sch.store_account_history_account_type = 'POWSC'";

                        break;
                    case 'W':
                        $sc_type_str = " sch.store_account_history_account_type = 'WSC'";

                        break;
                    default:
                        if (tep_not_null($_SESSION[$session_name]["sc_type"])) {
                            $sc_type_str = " sch.store_credit_account_type = '" . tep_db_input($_SESSION[$session_name]["sc_type"]) . "'";
                        } else {
                            $sc_type_str = ' 1 ';
                        }

                        break;
                }
            }
        }

        // Get Activity Type
        $sc_activity_str = $sc_activity_str1 = $sc_activity_str2 = "1";
        if ($filter_sc_activity) {
            if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
                if ($_SESSION[$session_name]["sc_type"] != 'W' && $_SESSION[$session_name]["sc_type"] != 'POWSC' && isset($_SESSION[$session_name]["sc_activity"]) && tep_not_null($_SESSION[$session_name]["sc_activity"])) {
                    if ($_SESSION[$session_name]["sc_activity"] == LOG_SC_ACTIVITY_TYPE_PURCHASE) {
                        $sc_activity_str1 = " sch.store_credit_activity_type IN ('" . $_SESSION[$session_name]["sc_activity"] . "', '" . LOG_SC_ACTIVITY_TYPE_REPURCHASE . "') ";
                    } else {
                        $sc_activity_str1 = " sch.store_credit_activity_type = '" . $_SESSION[$session_name]["sc_activity"] . "'";
                    }
                } else {
                    $sc_activity_str1 = "1";
                }
                $sc_activity_str2 = "1";
            } else {
                if ($_SESSION[$session_name]["sc_type"] != 'W' && $_SESSION[$session_name]["sc_type"] != 'POWSC' && isset($_SESSION[$session_name]["sc_activity"]) && tep_not_null($_SESSION[$session_name]["sc_activity"])) {
                    if ($_SESSION[$session_name]["sc_activity"] == LOG_SC_ACTIVITY_TYPE_PURCHASE) {
                        $sc_activity_str = " sch.store_credit_activity_type IN ('" . $_SESSION[$session_name]["sc_activity"] . "', '" . LOG_SC_ACTIVITY_TYPE_REPURCHASE . "') ";
                    } else {
                        $sc_activity_str = " sch.store_credit_activity_type = '" . $_SESSION[$session_name]["sc_activity"] . "' ";
                    }
                } else {
                    $sc_activity_str = "1";
                }
            }
        }

        // Get Transaction ID
        $transaction_id_str = $transaction_id_str1 = $transaction_id_str2 = "1";
        if ($filter_transaction_id) {
            if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
                $transaction_id_str1 = (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) ? " sch.store_credit_history_id = '" . $_SESSION[$session_name]["transaction_id"] . "'" : "1";
                $transaction_id_str2 = (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) ? " sah.store_account_history_id = '" . $_SESSION[$session_name]["transaction_id"] . "'" : "1";
            } else {
                $transaction_id_str = (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) ? (($_SESSION[$session_name]["sc_type"] == 'W') ? " sch.store_account_history_id = '" : " sch.store_credit_history_id = '") . $_SESSION[$session_name]["transaction_id"] . "'" : "1";
            }
        }

        // Get user id
        $no_customer_found = false;
        $statement_user_str = $statement_user_str1 = $statement_user_str2 = " 1 ";
        if ($filter_customer_email || $filter_transaction_id) {
            if (($search_type == '1' && tep_not_null($_SESSION[$session_name]["transaction_id"])) || $search_as_report_type_1) {
                $user_info_select_sql = "	SELECT customer_id AS customers_id
	  										FROM " . TABLE_STORE_CREDIT_HISTORY . " AS sch
	  										WHERE " . $transaction_id_str1 . "
											LIMIT 1";
                $user_info_result_sql = tep_db_query($user_info_select_sql);
                $user_info_row = tep_db_fetch_array($user_info_result_sql);
            } else if (tep_not_null($_SESSION[$session_name]["customer_id"])) {
                $user_info_row = array('customers_id' => $_SESSION[$session_name]["customer_id"]);
            }
            if (is_array($user_info_row)) {
                $_SESSION[$session_name]["user_id"] = $user_info_row['customers_id'];
                $statement_user_str1 = " sch.customer_id ='" . tep_db_input($_SESSION[$session_name]["user_id"]) . "' ";
                $statement_user_str2 = " sah.user_id ='" . tep_db_input($_SESSION[$session_name]["user_id"]) . "' ";
            } else {
                $no_customer_found = tep_not_null($_SESSION[$session_name]["customer_id"]) ? true : false;
            }
        }

        // Get Start Date
        $start_date_str = $start_date_str1 = $start_date_str2 = " 1 ";
        if ($filter_start_date) {
            if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
                if (tep_not_null($_SESSION[$session_name]["start_date"])) {
                    if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
                        $startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
                        list($yr, $mth, $day) = explode('-', $startDateObj[0]);
                        list($hr, $min) = explode(':', $startDateObj[1]);
                        $start_date_str1 = " ( sch.store_credit_history_date >= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                        $start_date_str2 = " ( sah.store_account_history_date >= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                    } else {
                        list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
                        $start_date_str1 = " ( sch.store_credit_history_date >= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                        $start_date_str2 = " ( sah.store_account_history_date >= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                    }
                    $opening_balance_date = date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr));
                } else {
                    $start_date_str1 = " ( sch.store_credit_history_date >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d') - 100, date('Y'))) . "' )";
                    $start_date_str2 = " ( sah.store_account_history_date >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d') - 100, date('Y'))) . "' )";
                    $opening_balance_date = date("Y-m-d H:i:s", mktime(0, 0, 0, date('m'), date('d') - 100, date('Y')));
                }
            } else {
                if (tep_not_null($_SESSION[$session_name]["start_date"])) {
                    if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
                        $startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
                        list($yr, $mth, $day) = explode('-', $startDateObj[0]);
                        list($hr, $min) = explode(':', $startDateObj[1]);
                        $start_date_str = " ( " . (($_SESSION[$session_name]["sc_type"] == 'W' || $_SESSION[$session_name]["sc_type"] == 'POWSC') ? "sch.store_account_history_date" : "sch.store_credit_history_date") . " >= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                    } else {
                        list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
                        $start_date_str = " ( " . (($_SESSION[$session_name]["sc_type"] == 'W' || $_SESSION[$session_name]["sc_type"] == 'POWSC') ? "sch.store_account_history_date" : "sch.store_credit_history_date") . " >= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                    }
                    $opening_balance_date = date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr));
                } else {
                    $start_date_str = " 1 ";
                    $opening_balance_date = '';
                }
            }
        }

        // Get End Date
        $end_date_str = $end_date_str1 = $end_date_str2 = " 1 ";
        if ($filter_end_date) {
            if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
                if (tep_not_null($_SESSION[$session_name]["end_date"])) {
                    if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
                        $endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
                        list($yr, $mth, $day) = explode('-', $endDateObj[0]);
                        list($hr, $min) = explode(':', $endDateObj[1]);
                        $end_date_str1 = " ( sch.store_credit_history_date <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                        $end_date_str2 = " ( sah.store_account_history_date <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                    } else {
                        list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
                        $end_date_str1 = " ( sch.store_credit_history_date <= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                        $end_date_str2 = " ( sah.store_account_history_date <= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                    }
                    $closing_balance_date = date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day + 1, $yr));
                } else {
                    $end_date_str1 = " ( sch.store_credit_history_date <= '" . date("Y-m-d H:i:s") . "' )";
                    $end_date_str2 = " ( sah.store_account_history_date <= '" . date("Y-m-d H:i:s") . "' )";
                    $closing_balance_date = date("Y-m-d H:i:s");
                }
            } else {
                if (tep_not_null($_SESSION[$session_name]["end_date"])) {
                    if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
                        $endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
                        list($yr, $mth, $day) = explode('-', $endDateObj[0]);
                        list($hr, $min) = explode(':', $endDateObj[1]);
                        $end_date_str = " ( " . (($_SESSION[$session_name]["sc_type"] == 'W' || $_SESSION[$session_name]["sc_type"] == 'POWSC') ? "sch.store_account_history_date" : "sch.store_credit_history_date") . " <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "' )";
                    } else {
                        list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
                        $end_date_str = " ( " . (($_SESSION[$session_name]["sc_type"] == 'W' || $_SESSION[$session_name]["sc_type"] == 'POWSC') ? "sch.store_account_history_date" : "sch.store_credit_history_date") . " <= DATE_FORMAT('" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "','%Y-%m-%d %H:%i:%s') )";
                    }
                    $closing_balance_date = date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day + 1, $yr));
                } else {
                    $end_date_str = " 1 ";
                    $closing_balance_date = '';
                }
            }
        }

        $result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;

        if ($_SESSION[$session_name]["report"] == '1') {
            // getting opening balance amount from opening date, store credit type
            $opening_data = $this->_get_store_credits_daily_history($opening_balance_date);
            // getting closing balance amount from closing date, store credit type
            $closing_data = $this->_get_store_credits_daily_history($closing_balance_date);

            $opening_figure = $currency_count = array();
            if (isset($opening_data) && is_array($opening_data)) {
                foreach ($opening_data as $store_credit_type => $date_list) {
                    foreach ($date_list as $date => $currency_list) {
                        foreach ($currency_list as $currency => $data) {
                            $opening_figure[$store_credit_type][$currency] = (double) $data['store_credit_daily_history_amount'];
                            $currency_count[$store_credit_type][$currency] = 0;
                        }
                    }
                }
            }
        } else {
            // getting opening balance amount from opening date, store credit type
            $opening_data = $this->_get_store_credits_daily_history($opening_balance_date, $this->credit_accounts_type_short[$_SESSION[$session_name]["sc_type"]]);
            // getting closing balance amount from closing date, store credit type
            $closing_data = $this->_get_store_credits_daily_history($closing_balance_date, $this->credit_accounts_type_short[$_SESSION[$session_name]["sc_type"]]);

            $opening_figure = $currency_count = array();
            if (isset($opening_data) && is_array($opening_data)) {
                foreach ($opening_data as $credit_tpye => $date_list) {
                    foreach ($date_list as $date => $currency_list) {
                        foreach ($currency_list as $currency => $data) {
                            $opening_figure[$currency] = (double) $data['store_credit_daily_history_amount'];
                            $currency_count[$currency] = 0;
                        }
                    }
                }
            }
        }

        if ($_SESSION[$session_name]["report"] == '1' || $search_as_report_type_1) {
            $sc_statement_select_sql = "SELECT sch.store_credit_history_id as history_id, DATE_FORMAT(sch.store_credit_history_date, '%Y-%m-%d %H:%i') AS activity_date, sch.store_credit_history_trans_type AS trans_type, sch.store_credit_history_trans_id AS trans_id, sch.store_credit_transaction_reserved AS trans_reserved,
			  							sch.store_credit_activity_type AS activity_type, sch.store_credit_history_activity_title AS activity_title, sch.store_credit_history_activity_desc AS activity_desc, sch.store_credit_history_activity_desc_show AS activity_desc_show,
			  							sch.store_credit_account_type AS account_type, sch.store_credit_history_debit_amount AS debit_amount, sch.store_credit_history_credit_amount AS credit_amount,
			  							sch.store_credit_history_r_after_balance AS r_after_balance, sch.store_credit_history_nr_after_balance AS nr_after_balance, sch.store_credit_history_added_by AS added_by, sch.store_credit_history_added_by_role AS added_by_role, sch.store_credit_history_admin_messages AS admin_message, sch.customer_id AS customer_id, CAST(sch.store_credit_history_currency_id AS CHAR) AS currency,
                                        cust.customers_email_address AS customers_email_address
										FROM " . TABLE_STORE_CREDIT_HISTORY . " AS sch
                                        LEFT JOIN " . TABLE_CUSTOMERS . " AS cust ON sch.customer_id = cust.customers_id
										WHERE " . $statement_user_str1 . "
										AND " . $transaction_id_str1 . "
										AND " . $start_date_str1 . "
										AND " . $end_date_str1 . "
										AND " . $sc_type_str1 . "
										AND " . $sc_activity_str1;
            if ($sc_activity_str1 == "1") {
                $sc_statement_select_sql .= "
										UNION
		  								SELECT sah.store_account_history_id as history_id, DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d %H:%i') AS activity_date, sah.store_account_history_trans_type AS trans_type, sah.store_account_history_trans_id AS trans_id, sah.store_account_transaction_reserved AS trans_reserved,
			  							'' AS activity_type, sah.store_account_history_activity_title AS activity_title, sah.store_account_history_activity_desc AS activity_desc, sah.store_account_history_activity_desc_show AS activity_desc_show,
			  							sah.store_account_history_account_type AS account_type, sah.store_account_history_debit_amount AS debit_amount, sah.store_account_history_credit_amount AS credit_amount,
			  							sah.store_account_history_after_balance AS r_after_balance, 0 AS nr_after_balance, sah.store_account_history_added_by AS added_by, sah.store_account_history_added_by_role AS added_by_role, sah.store_account_history_admin_messages AS admin_message, sah.user_id AS customer_id, sah.store_account_history_currency as currency,
                                        cust.customers_email_address AS customers_email_address
										FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sah
                                        LEFT JOIN " . TABLE_CUSTOMERS . " AS cust ON sah.user_id = cust.customers_id
										WHERE " . $statement_user_str2 . "
										AND " . $transaction_id_str2 . "
										AND " . $start_date_str2 . "
										AND " . $end_date_str2 . "
										AND " . $sc_type_str2 . "
										AND " . $sc_activity_str2;
            }
            $sc_statement_select_sql .= "
										ORDER BY activity_date asc, history_id asc";
        } else if ($_SESSION[$session_name]["report"] == '2') {
            if ($_SESSION[$session_name]["sc_type"] == 'W') {
                // Need store_credit_history_added_by in ORDER BY to maintain the ordering for those records on the same date and time
                $sc_statement_select_sql = "SELECT sch.store_account_history_id as history_id, DATE_FORMAT(sch.store_account_history_date, '%Y-%m-%d %H:%i') AS activity_date, sch.store_account_history_trans_type AS trans_type, sch.store_account_history_trans_id AS trans_id, sch.store_account_transaction_reserved AS trans_reserved,
				  							'' AS activity_type, sch.store_account_history_activity_title AS activity_title, sch.store_account_history_activity_desc AS activity_desc, sch.store_account_history_activity_desc_show AS activity_desc_show,
				  							sch.store_account_history_account_type AS account_type, sch.store_account_history_debit_amount AS debit_amount, sch.store_account_history_credit_amount AS credit_amount,
				  							sch.store_account_history_after_balance AS r_after_balance, 0 AS nr_after_balance, sch.store_account_history_added_by AS added_by, sch.store_account_history_added_by_role AS added_by_role, sch.store_account_history_admin_messages AS admin_message, sch.user_id AS customer_id, sch.store_account_history_currency as currency,
                                            cust.customers_email_address AS customers_email_address
											FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sch
                                            LEFT JOIN " . TABLE_CUSTOMERS . " AS cust ON sch.user_id = cust.customers_id
											WHERE " . $statement_user_str . "
											AND " . $transaction_id_str . "
											AND " . $start_date_str . "
											AND " . $end_date_str . "
											AND " . $sc_type_str . "
											AND " . $sc_activity_str . "
											ORDER BY sch.store_account_history_date asc, store_account_history_id asc";
            } else if ($_SESSION[$session_name]["sc_type"] == 'POWSC') {
                // Need store_credit_history_added_by in ORDER BY to maintain the ordering for those records on the same date and time
                $sc_statement_select_sql = "SELECT sch.store_account_history_id as history_id, DATE_FORMAT(sch.store_account_history_date, '%Y-%m-%d %H:%i') AS activity_date, sch.store_account_history_trans_type AS trans_type, sch.store_account_history_trans_id AS trans_id, sch.store_account_transaction_reserved AS trans_reserved,
				  							'' AS activity_type, sch.store_account_history_activity_title AS activity_title, sch.store_account_history_activity_desc AS activity_desc, sch.store_account_history_activity_desc_show AS activity_desc_show,
				  							sch.store_account_history_account_type AS account_type, sch.store_account_history_debit_amount AS debit_amount, sch.store_account_history_credit_amount AS credit_amount,
				  							sch.store_account_history_after_balance AS r_after_balance, 0 AS nr_after_balance, sch.store_account_history_added_by AS added_by, sch.store_account_history_added_by_role AS added_by_role, sch.store_account_history_admin_messages AS admin_message, sch.user_id AS customer_id, sch.store_account_history_currency as currency,
                                            cust.customers_email_address AS customers_email_address
											FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sch
                                            LEFT JOIN " . TABLE_CUSTOMERS . " AS cust ON sch.user_id = cust.customers_id
											WHERE " . $statement_user_str . "
											AND " . $transaction_id_str . "
											AND " . $start_date_str . "
											AND " . $end_date_str . "
											AND " . $sc_type_str . "
											AND " . $sc_activity_str . "
											ORDER BY sch.store_account_history_date asc, store_account_history_id asc";
            } else {
                // Need store_credit_history_added_by in ORDER BY to maintain the ordering for those records on the same date and time
                $sc_statement_select_sql = "SELECT sch.store_credit_history_id as history_id, DATE_FORMAT(sch.store_credit_history_date, '%Y-%m-%d %H:%i') AS activity_date, sch.store_credit_history_trans_type AS trans_type, sch.store_credit_history_trans_id AS trans_id, sch.store_credit_transaction_reserved AS trans_reserved,
				  							sch.store_credit_activity_type AS activity_type, sch.store_credit_history_activity_title AS activity_title, sch.store_credit_history_activity_desc AS activity_desc, sch.store_credit_history_activity_desc_show AS activity_desc_show,
				  							sch.store_credit_account_type AS account_type, sch.store_credit_history_debit_amount AS debit_amount, sch.store_credit_history_credit_amount AS credit_amount,
				  							sch.store_credit_history_r_after_balance AS r_after_balance, sch.store_credit_history_nr_after_balance AS nr_after_balance, sch.store_credit_history_added_by AS added_by, sch.store_credit_history_added_by_role AS added_by_role, sch.store_credit_history_admin_messages AS admin_message, sch.customer_id AS customer_id, sch.store_credit_history_currency_id as currency,
                                            cust.customers_email_address AS customers_email_address
											FROM " . TABLE_STORE_CREDIT_HISTORY . " AS sch
                                            LEFT JOIN " . TABLE_CUSTOMERS . " AS cust ON sch.customer_id = cust.customers_id
											WHERE " . $statement_user_str . "
											AND " . $transaction_id_str . "
											AND " . $start_date_str . "
											AND " . $end_date_str . "
											AND " . $sc_type_str . "
											AND " . $sc_activity_str . "
											ORDER BY sch.store_credit_history_date asc, store_credit_history_id asc";
            }
        }
        $show_records = $_SESSION[$session_name]["show_records"];

        if (!$no_customer_found) {
            $sc_statement_result_sql = tep_db_query($sc_statement_select_sql);

            $sc_statement_array = array();
            if ($_SESSION[$session_name]["report"] == '1') {
                while ($sc_statement_row = tep_db_fetch_array($sc_statement_result_sql)) {
                    // keep the last transaction record for each currency, later used to find untally closing balance
                    $this_currency_code = is_numeric($sc_statement_row['currency']) ? $currencies->get_code_by_id($sc_statement_row['currency']) : $sc_statement_row['currency'];
                    if (tep_not_null($sc_statement_row['debit_amount'])) {
                        if (isset($opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code])) {
                            $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] = (double) $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] - (double) $sc_statement_row['debit_amount'];
                        } else {
                            $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] = 0 - (double) $sc_statement_row['debit_amount'];
                        }
                        $sc_statement_row['running_balance'] = array('amt' => (double) $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code], 'cnt' => 0, 'untally' => 0);
                    }
                    if (tep_not_null($sc_statement_row['credit_amount'])) {
                        if (isset($opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code])) {
                            $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] = (double) $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] + (double) $sc_statement_row['credit_amount'];
                        } else {
                            $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code] = (double) $sc_statement_row['credit_amount'];
                        }
                        $sc_statement_row['running_balance'] = array('amt' => (double) $opening_figure[$this->credit_accounts_type_short[$sc_statement_row['account_type']]][$this_currency_code], 'cnt' => 0, 'untally' => 0);
                    }
                    $sort_idx = $sc_statement_row['activity_date'] . "_" . $sc_statement_row['history_id'];
                    $sc_statement_array[$sort_idx][$sc_statement_row['history_id']] = $sc_statement_row;
                }
                krsort($sc_statement_array);
            } else {
                while ($sc_statement_row = tep_db_fetch_array($sc_statement_result_sql)) {
                    $sort_idx = $sc_statement_row['activity_date'] . "_" . $sc_statement_row['history_id'];
                    $sc_statement_array[$sort_idx][$sc_statement_row['history_id']] = $sc_statement_row;
                    // keep the last transaction record for each currency, later used to find untally closing balance
                    $this_currency_code = is_numeric($sc_statement_row['currency']) ? $currencies->get_code_by_id($sc_statement_row['currency']) : $sc_statement_row['currency'];
                    $currency_count[$this_currency_code] = array('date' => $sc_statement_row['activity_date'], 'id' => $sc_statement_row['history_id']);
                    if (tep_not_null($sc_statement_row['debit_amount'])) {
                        if (isset($opening_figure[$this_currency_code])) {
                            $opening_figure[$this_currency_code] = (double) $opening_figure[$this_currency_code] - (double) $sc_statement_row['debit_amount'];
                        } else {
                            $opening_figure[$this_currency_code] = 0 - (double) $sc_statement_row['debit_amount'];
                        }
                        $sc_statement_array[$sort_idx][$sc_statement_row['history_id']]['running_balance'] = array('amt' => (double) $opening_figure[$this_currency_code], 'cnt' => $currency_count[$this_currency_code]['id'], 'untally' => 0);
                    }
                    if (tep_not_null($sc_statement_row['credit_amount'])) {
                        if (isset($opening_figure[$this_currency_code])) {
                            $opening_figure[$this_currency_code] = (double) $opening_figure[$this_currency_code] + (double) $sc_statement_row['credit_amount'];
                        } else {
                            $opening_figure[$this_currency_code] = (double) $sc_statement_row['credit_amount'];
                        }
                        $sc_statement_array[$sort_idx][$sc_statement_row['history_id']]['running_balance'] = array('amt' => (double) $opening_figure[$this_currency_code], 'cnt' => $currency_count[$this_currency_code]['id'], 'untally' => 0);
                    }
                }
                krsort($sc_statement_array);

                // Find the untally balance between last transaction and closing balance
                if (isset($closing_data) && is_array($closing_data)) {
                    foreach ($closing_data as $credit_type => $date_list) {
                        foreach ($date_list as $date => $currency_row) {
                            foreach ($currency_row as $currency => $history_data) {
                                if (isset($currency_count[$currency])) {
                                    $idx_date = $currency_count[$currency]['date'];
                                    $idx_id = $currency_count[$currency]['id'];
                                    $sort_idx = $idx_date . "_" . $idx_id;
                                    if (isset($sc_statement_array[$sort_idx][$idx_id]['running_balance']['amt']) && round(bcmul($history_data['store_credit_daily_history_amount'], 1, 4), 2) != round(bcmul($sc_statement_array[$sort_idx][$idx_id]['running_balance']['amt'], 1, 4), 2)) {
                                        $sc_statement_array[$sort_idx][$idx_id]['running_balance']['untally'] = 1;
                                        $closing_data[$credit_type][$date][$currency]['untally'] = 1;
                                    }
                                } else {
                                    $closing_data[$credit_type][$date][$currency]['untally'] = 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($search_type == '1' || $search_as_report_type_1 === true) {
            if (count($sc_statement_array) > 0) {
                foreach ($sc_statement_array as $key1 => $values) {
                    foreach ($values as $key2 => $value) {
                        $_SESSION[$session_name]["type_from_transaction"] = $value['account_type'];
                    }
                }
            }
        } else {
            unset($_SESSION[$session_name]["type_from_transaction"]);
        }

        if (!$no_customer_found) {
            $this->store_credit_data = $sc_statement_array;
            $this->store_opening_data = $opening_data;
            $this->store_closing_data = $closing_data;
            $this->store_currency_count = $currency_count;
        } else {
            $this->store_credit_data = array();
            $this->store_opening_data = array();
            $this->store_closing_data = array();
            $this->store_currency_count = array();
            $error = true;
            $messageStack->add(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
        }

        // write records data into session cache file
        $this->store_credit_data_filename = $session_name . '_' . date('YmdHis') . '.cache';
        $this->_write_cache_data($this->store_credit_data_filename, $session_name);
    }

    function _load_cache_data($cache_filename, $session_name) {
        $file_location = 'download/' . $cache_filename;
        if (!$handle = fopen($file_location, 'r+')) {
            exit;
        }

        if ($contents = fread($handle, filesize($file_location))) {
            $combined_data = unserialize($contents);
            $this->store_credit_data = $combined_data['store_credit_data'];
            $this->store_opening_data = $combined_data['store_opening_data'];
            $this->store_closing_data = $combined_data['store_closing_data'];
            $this->store_currency_count = $combined_data['store_currency_count'];
            $_SESSION[$session_name]["user_id"] = $combined_data['session_user_id'];
        }
        fclose($handle);

        if ($contents === false) {
            return false;
        } else {
            return true;
        }
    }

    function _write_cache_data($cache_filename, $session_name) {
        if (tep_not_null($this->store_credit_data)) {
            $file_location = 'download/' . $cache_filename;
            if (!$handle = fopen($file_location, 'w')) {
                exit;
            }

            $combined_data = array('store_credit_data' => $this->store_credit_data,
                'store_opening_data' => $this->store_opening_data,
                'store_closing_data' => $this->store_closing_data,
                'store_currency_count' => $this->store_currency_count,
                'session_user_id' => $_SESSION[$session_name]["user_id"]
            );
            $cache_data = serialize($combined_data);
            // Write to our opened file.
            if (fwrite($handle, $cache_data) === FALSE) {
                fclose($handle);
                exit;
            }

            fclose($handle);
            return true;
        } else {
            return false;
        }
    }

    function paging_array_data($combined_data, $max_record_in_a_page, $input_array) {
        global $PHP_SELF, $currencies;

        if ($max_record_in_a_page == 0) {
            $max_record_in_a_page = MAX_DISPLAY_SEARCH_RESULTS;
        }

        $display_data = array();
        $total_record = count($combined_data);
        $total_page = ($total_record >= $max_record_in_a_page) ? $total_record / $max_record_in_a_page : 1;
        $total_page = (intval($total_page) < $total_page) ? intval($total_page) + 1 : $total_page;

        $current_page_number = 1;
        $current_page_record_start_number = 0;
        if ($total_record > 0) {
            $current_page_record_start_number = 1;
        }
        $current_page_record_end_number = ($total_record > $max_record_in_a_page) ? $max_record_in_a_page : $total_record;

        if (tep_not_null($input_array['page'])) {
            if ($input_array['page'] != 'all') {
                $current_page_number = intval($input_array['page']);
                $current_page_record_start_number = intval(($current_page_number * $max_record_in_a_page) - $max_record_in_a_page + 1);
                $current_page_record_end_number = (intval($current_page_number * $max_record_in_a_page) < $total_record) ? intval($current_page_number * $max_record_in_a_page) : $total_record;
            } else {
                if ($input_array['page'] == 'all') {
                    $current_page_number = $input_array['page'];
                    $current_page_record_start_number = 1;
                    $current_page_record_end_number = $total_record;
                }
            }
        }

        if (count($combined_data)) {
            $current_row_number = 1;
            foreach ($combined_data as $idx_activity_date => $sc_details_by_date) {
                foreach ($sc_details_by_date as $idx_history_id => $sc_details) {
                    if ($current_row_number >= $current_page_record_start_number && $current_row_number <= $current_page_record_end_number) {
                        $display_data[$idx_activity_date][$idx_history_id] = array('history_id' => $sc_details['history_id'],
                            'activity_date' => $sc_details['activity_date'],
                            'trans_type' => $sc_details['trans_type'],
                            'trans_id' => $sc_details['trans_id'],
                            'trans_reserved' => $sc_details['trans_reserved'],
                            'activity_type' => $sc_details['activity_type'],
                            'activity_title' => $sc_details['activity_title'],
                            'activity_desc' => $sc_details['activity_desc'],
                            'activity_desc_show' => $sc_details['activity_desc_show'],
                            'account_type' => $sc_details['account_type'],
                            'debit_amount' => $sc_details['debit_amount'],
                            'credit_amount' => $sc_details['credit_amount'],
                            'r_after_balance' => $sc_details['r_after_balance'],
                            'nr_after_balance' => $sc_details['nr_after_balance'],
                            'added_by' => $sc_details['added_by'],
                            'added_by_role' => $sc_details['added_by_role'],
                            'admin_messages' => $sc_details['admin_messages'],
                            'customer_id' => $sc_details['customer_id'],
                            'currency' => is_numeric($sc_details['currency']) ? $currencies->get_code_by_id((int) $sc_details['currency']) : $sc_details['currency'],
                            'running_balance' => $sc_details['running_balance'],
                        );
                    }
                    $current_row_number++;
                }
            }
        }

        $url = tep_href_link(basename($PHP_SELF));

        $query_string = '';
        if (isset($input_array['page']))
            unset($input_array['page']);
        if (isset($input_array['osCAdminID']))
            unset($input_array['osCAdminID']);

        foreach ($input_array as $key => $value) {
            $query_string .= $key . '=' . $value . '&';
        }

        if (tep_not_null($query_string)) {
            $query_string = '?' . substr($query_string, 0, -1);
        }

        $previous_page_url = '';
        if (intval($current_page_number) - 1 >= 1) {
            $previous_page_url = $url . $query_string . '&page=' . (intval($current_page_number) - 1);
        } else {
            $previous_page_url = '';
        }

        $next_page_url = '';
        if (intval($current_page_number) + 1 <= $total_page) {
            $next_page_url = $url . $query_string . '&page=' . (intval($current_page_number) + 1);
        } else {
            $next_page_url = '';
        }

        $display_info = '<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle"><tr><td class="smallText" valign="top">Displaying <b>' . $current_page_record_start_number . '</b> to <b>' . $current_page_record_end_number . '</b> (of <b>' . $total_record . '</b> records)</td><td class="smallText" align="right">';

        if ($total_page > 1) {
            $display_info .= '<form name="pages" action="' . tep_href_link(basename($PHP_SELF)) . '" method="get">';

            if ($previous_page_url == '' || $current_page_number == 'all') {
                $display_info .= '&lt;&lt;';
            } else {
                $display_info .= '<a href="' . $previous_page_url . '" class="splitPageLink">&lt;&lt;</a>&nbsp;&nbsp;';
            }

            $display_info .= 'Page&nbsp;<select name="page" onChange="this.form.submit();">';

            for ($page_number = 1; $page_number <= $total_page; $page_number++) {
                if ($page_number == intval($current_page_number)) {
                    $display_info .= '<option value="' . $page_number . '" SELECTED>' . $page_number . '</option>';
                } else {
                    $display_info .= '<option value="' . $page_number . '">' . $page_number . '</option>';
                }
            }
            $display_info .= '<option value="all" ' . ($current_page_number == 'all' ? 'SELECTED' : '') . '>All</option>';

            $display_info .= '</select> of ' . $total_page . '&nbsp;&nbsp;';

            if ($next_page_url == '' || $current_page_number == 'all') {
                $display_info .= '&gt;&gt;';
            } else {
                $display_info .= '<a href="' . $next_page_url . '" class="splitPageLink">&gt;&gt;</a>&nbsp;&nbsp;';
            }

            $display_info .= '<input type="hidden" name="action" value="show_report"><input type="hidden" name="cont" value="1"></form>';
        }

        $display_info .= '</td></tr></table>';

        $paging_array_data = array();
        $paging_array_data['total_record'] = $total_record;
        $paging_array_data['total_page'] = $total_page;
        $paging_array_data['current_page_number'] = $current_page_number;
        $paging_array_data['current_page_record_start_number'] = $current_page_record_start_number;
        $paging_array_data['current_page_record_end_number'] = $current_page_record_end_number;
        $paging_array_data['display_data'] = $display_data;
        $paging_array_data['previous_page_url'] = $previous_page_url;
        $paging_array_data['next_page_url'] = $next_page_url;
        $paging_array_data['display_info'] = $display_info;

        return $paging_array_data;
    }

    function export_detail_daily_history($filename, $session_name, $input_array) {
        global $currencies, $languages_id, $view_sc_flow_report_permission;
        $currencies->set_hide_currency_symbol();

        //$csv_file_array = array(); // array of currency based daily history csv filenames
        $export_zip_content = array();

        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["report"] = $input_array["report"];
            $_SESSION[$session_name]["report_date"] = $input_array["report_date"];
            $_SESSION[$session_name]["sc_type"] = $input_array["sc_type"];
            $_SESSION[$session_name]["report_type"] = $input_array["report_type"];
        }
        $sc_type = $input_array["sc_type"];
        $report_type = $input_array["report_type"];

        switch ($input_array["report"]) {
            case "2":
                if ($view_sc_flow_report_permission) {
                    $sc_type_str = (isset($sc_type) && tep_not_null($sc_type)) ? " store_credit_daily_history_credit_type = '" . tep_db_input($this->credit_accounts_type_short[$sc_type]) . "'" : "1";
                    // retrieve daily history records based on date and store credit type
                    $sc_daily_history_select_sql = "SELECT store_credit_daily_history_date,
													store_credit_daily_history_currency,
													store_credit_daily_history_credit_type,
													store_credit_daily_history_amount,
													store_credit_daily_history_reserved_amount,
													user_id
													FROM " . TABLE_STORE_CREDIT_DAILY_HISTORY . "
													WHERE store_credit_daily_history_date >= '" . tep_db_input($input_array["report_date"]) . "'
														AND store_credit_daily_history_date < DATE_ADD('" . tep_db_input($input_array["report_date"]) . "', INTERVAL 1 HOUR)
														AND " . $sc_type_str . "
														AND (store_credit_daily_history_amount <> 0
															OR store_credit_daily_history_reserved_amount <> 0)
														AND user_id <> 'system'
													ORDER BY store_credit_daily_history_currency, user_id";
                    $sc_daily_history_result_sql = tep_db_query($sc_daily_history_select_sql);
                    while ($sc_daily_history_row = tep_db_fetch_array($sc_daily_history_result_sql)) {
                        $history_array[$sc_daily_history_row['store_credit_daily_history_currency']][] = $sc_daily_history_row;
                    }

                    if (is_array($history_array)) {
                        $history_keys = array_keys($history_array);
                        $start_key = 0;
                        while ($data_array = array_shift($history_array)) {
                            $currency = $history_keys[$start_key];
                            $zip_content = array();
                            $zip_content[] = '"' . TABLE_HEADING_SC_STAT_DATETIME . '",' .
                                    '"' . TABLE_HEADING_SC_STAT_CUST_ID . '",' .
                                    '"' . TABLE_HEADING_SC_STAT_CURRENCY . '",' .
                                    '"' . TABLE_HEADING_SC_STAT_BALANCE . ' (' . $this->credit_accounts_type_short[$sc_type] . ')",' .
                                    '"' . TABLE_HEADING_SC_STAT_RESERVE . ' (' . $this->credit_accounts_type_short[$sc_type] . ')"';
                            if (is_array($data_array)) {
                                while ($value = array_shift($data_array)) {
                                    $zip_content[] = '"' . $value['store_credit_daily_history_date'] . '",' .
                                            '"' . $value['user_id'] . '",' .
                                            '"' . (is_numeric($value['store_credit_daily_history_currency']) ? $currencies->get_code_by_id((int) $value['store_credit_daily_history_currency']) : $value['store_credit_daily_history_currency']) . '",' .
                                            '"' . $value['store_credit_daily_history_amount'] . '",' .
                                            '"' . $value['store_credit_daily_history_reserved_amount'] . '"';
                                }
                            }
                            $export_zip_content[$currency] = $zip_content;
                            unset($zip_content);
                            unset($data_array);
                            $start_key++;
                        }
                    }
                }
                break;
            default :
                break;
        }

        $currencies->set_show_currency_symbol();
        return $export_zip_content;
    }

    function export_store_credit_movement($filename, $session_name, $input_array, &$messageStack) {
        global $currencies, $languages_id, $view_sc_flow_report_permission;
        $currencies->set_hide_currency_symbol();

        if (!$_REQUEST['cont']) {
            $_SESSION[$session_name]["report"] = (isset($input_array["report"]) ? $input_array["report"] : '1');
            $_SESSION[$session_name]["customer_id"] = $input_array["customer_id"];
            $_SESSION[$session_name]["start_date"] = $input_array["start_date"];
            $_SESSION[$session_name]["end_date"] = $input_array["end_date"];
            $_SESSION[$session_name]["transaction_id"] = $input_array["transaction_id"];
            $_SESSION[$session_name]["sc_type"] = $input_array["sc_type"];
            $_SESSION[$session_name]["sc_activity"] = $input_array["sc_activity"];
            $_SESSION[$session_name]["show_records"] = $input_array["show_records"];

            // if cache data not exist, query from database and write to cache
            $this->_load_database_data($session_name, $input_array, $messageStack);
            $_SESSION[$session_name]["store_credit_data"] = $this->store_credit_data_filename;
        } else {
            // if cache data exist, read from cache
            $this->store_credit_data_filename = $_SESSION[$session_name]["store_credit_data"];
            $this->_load_cache_data($this->store_credit_data_filename, $session_name);
        }

        $export_csv_content = '';

        switch ($_SESSION[$session_name]["report"]) {
            case "2":
                if ($view_sc_flow_report_permission) {
                    if (count($this->store_credit_data) > 0) {
                        $export_csv_content = '"' . HEADER_FORM_SC_STAT_TITLE . '"';
                        $export_csv_content = $export_csv_content . "\n";
                        $export_csv_content = $export_csv_content . "\n";

                        $export_csv_content = $export_csv_content . '"' . ENTRY_SC_STAT_REPORT_TYPE . '",":","' . ($_SESSION[$session_name]["report"] == "1" ? REPORT_SC_STAT_TYPE_INDIVIDUAL : REPORT_SC_STAT_TYPE_SC_FLOW) . '"';
                        $export_csv_content = $export_csv_content . "\n";

                        if (isset($_SESSION[$session_name]["transaction_id"]) && tep_not_null($_SESSION[$session_name]["transaction_id"])) {
                            $export_csv_content = $export_csv_content . '"' . ENTRY_SC_TRANSACTION_ID . '",":","' . $_SESSION[$session_name]["transaction_id"] . '"';
                            $export_csv_content = $export_csv_content . "\n";
                        } else {
                            $export_csv_content = $export_csv_content . '"Customer ID",":","' . $_SESSION[$session_name]["customer_id"] . '"';
                            $export_csv_content = $export_csv_content . "\n";

                            $export_csv_content = $export_csv_content . '"Start Date",":","' . $_SESSION[$session_name]["start_date"] . '"';
                            $export_csv_content = $export_csv_content . "\n";

                            $export_csv_content = $export_csv_content . '"End Date",":","' . $_SESSION[$session_name]["end_date"] . '"';
                            $export_csv_content = $export_csv_content . "\n";
                        }

                        if (isset($_SESSION[$session_name]["sc_type"]) && tep_not_null($_SESSION[$session_name]["sc_type"])) {
                            $export_csv_content = $export_csv_content . '"' . ENTRY_SC_TYPE . '",":","' . $this->credit_accounts_type[$_SESSION[$session_name]["sc_type"]] . '"';
                            $export_csv_content = $export_csv_content . "\n";
                        } else {
                            $export_csv_content = $export_csv_content . '"' . ENTRY_SC_TYPE . '",":","Please Select"';
                            $export_csv_content = $export_csv_content . "\n";
                        }

                        if (isset($_SESSION[$session_name]["sc_activity"]) && tep_not_null($_SESSION[$session_name]["sc_activity"])) {
                            if (count($this->credit_accounts_activities_selection) > 0) {
                                foreach ($this->credit_accounts_activities_selection as $key => $item) {
                                    if ($item['id'] == $_SESSION[$session_name]["sc_activity"]) {
                                        $export_csv_content = $export_csv_content . '"' . ENTRY_SC_ACTIVITY . '",":","' . $item['text'] . '"';
                                        $export_csv_content = $export_csv_content . "\n";
                                    }
                                }
                            }
                        } else {
                            $export_csv_content = $export_csv_content . '"' . ENTRY_SC_ACTIVITY . '",":","All Activities"';
                            $export_csv_content = $export_csv_content . "\n";
                        }

                        $payment_gateway_array = array();
                        $payment_gateway_select_sql = "	SELECT payment_methods_id, payment_methods_title
														FROM " . TABLE_PAYMENT_METHODS . "
														WHERE payment_methods_parent_id = '0' ";
                        $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
                        while ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
                            $payment_gateway_array[$payment_gateway_row['payment_methods_id']] = $payment_gateway_row['payment_methods_title'];
                        }

                        $export_csv_content = $export_csv_content .
                                '"' . TABLE_HEADING_SC_STAT_TRANS_ID . '",' .
                                '"' . TABLE_HEADING_SC_STAT_DATETIME . '",' .
                                '"' . TABLE_HEADING_SC_STAT_ACTIVITY . '",' .
                                '"' . TABLE_HEADING_SC_STAT_ADDED_BY . '",' .
                                '"' . TABLE_HEADING_SC_STAT_CUST_ID . '",' .
                                '"' . TABLE_HEADING_SC_STAT_PG . '",' .
                                '"' . TABLE_HEADING_SC_STAT_COMMENT . '",' .
                                '"' . TABLE_HEADING_SC_STAT_CURRENCY . '",' .
                                '"' . TABLE_HEADING_SC_STAT_DEBIT . '",' .
                                '"' . TABLE_HEADING_SC_STAT_CREDIT . '",' .
                                '"' . TABLE_HEADING_SC_STAT_USD_EQUIV . '",' .
                                '"' . TABLE_HEADING_SC_STAT_BALANCE . ' (' . $this->credit_accounts_type_short[$_SESSION[$session_name]["sc_type"]] . ')",' .
                                '"' . TABLE_HEADING_SC_STAT_CUST_BALANCE . '",' . 
                                '"' . TABLE_HEADING_SC_STAT_CUST_EMAIL . '"';
                        $export_csv_content = $export_csv_content . "\n";

                        // Display first row for current store credit values
                        $export_csv_content = $export_csv_content . "\n" . $this->_display_store_credits_daily_history($this->store_closing_data, 'C', $_SESSION[$session_name]["sc_type"], true);

                        $row_count = 0;
                        if (count($this->store_credit_data) > 0) {
                            foreach ($this->store_credit_data as $idx_activity_date => $sc_statement_row_by_date) {
                                foreach ($sc_statement_row_by_date as $idx_history_id => $sc_statement_row) {
                                    $sc_history_id_str = sprintf(TEXT_SC_STAT_TRANS_ID, $sc_statement_row['history_id']);
                                    $sc_history_by_str = (($sc_statement_row['added_by_role'] == 'admin') ? $sc_statement_row['added_by'] : $sc_statement_row['customer_id']);
                                    $order_currency_value = 0;
                                    $payment_gateway = '';
                                    $order_info_row = array();

                                    if (tep_not_null($sc_statement_row['activity_title'])) {
                                        $activity_title = $sc_statement_row['activity_title'];
                                    } else {
                                        if ($_SESSION[$session_name]["sc_type"] == 'W' || $_SESSION[$session_name]["sc_type"] == 'POWSC') {
                                            // Store Credit Type = WSC
                                            switch ($sc_statement_row['trans_type']) {
                                                case 'A':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_AFFILIATE . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                case 'B':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_BUYBACK . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                case 'P':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_PAYMENT . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                case 'PWL':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_PWL . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                case 'PO':
                                                    require_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_PO . ' ' . edit_purchase_orders::get_po_ref_num_by_id($sc_statement_row['trans_id']);
                                                    break;
                                                case 'SO':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_G2G_SELL_ORDER . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                case 'AF':
                                                    $activity_title = TEXT_SC_STAT_ACTIVITY_G2G_AFFILIATE . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                                default:
                                                    $activity_title = $sc_statement_row['activity_title'];
                                                    break;
                                            }
                                        } else {
                                            // Store Credit Type = RSC and NRSC
                                            switch ($sc_statement_row['trans_type']) {
                                                case 'C':
                                                    $activity_title = sprintf(TITLE_TRANS_CUSTOMER_ORDER, $sc_statement_row['trans_id']);
                                                    $order_info_row = $this->_get_orders_particulars($sc_statement_row['trans_id']);
                                                    $order_currency_value = $order_info_row['currency_value'];
                                                    $payment_gateway = (isset($payment_gateway_array[$order_info_row['payment_methods_parent_id']]) ? $payment_gateway_array[$order_info_row['payment_methods_parent_id']] : '');

                                                    break;
                                                default:
                                                    $activity_title = $sc_statement_row['trans_type'] . ' ' . $sc_statement_row['trans_id'];
                                                    break;
                                            }

                                            switch ($sc_statement_row['activity_type']) {
                                                case 'C':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_COMPENSATE;
                                                    break;
                                                case 'P':
                                                case 'RP':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_PURCHASE;
                                                    break;
                                                case 'R':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_REFUND;
                                                    break;
                                                case 'D':
                                                    $activity_title = sprintf(TITLE_TRANS_REDEEM_RECORD, $sc_statement_row['trans_id']);
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_REDEEM;
                                                    break;
                                                case 'S':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_SC;
                                                    if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                        $activity_type .= TEXT_DEDUCT;
                                                    }
                                                    break;
                                                case 'X':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_CANCEL;
                                                    break;
                                                case 'XS':
                                                    $activity_type = TEXT_SC_STAT_ACTIVITY_EXTRA_SC;
                                                    if (tep_not_null($sc_statement_row['debit_amount']) && ($sc_statement_row['debit_amount'] >= 0)) {
                                                        $activity_type .= TEXT_DEDUCT;
                                                    }
                                                    break;
                                            }

                                            if ($sc_statement_row['activity_type'] == 'XS') {
                                                if (tep_not_null($activity_type))
                                                    $activity_title = $activity_type . ' - ' . $activity_title;
                                            } else {
                                                if (tep_not_null($activity_type))
                                                    $activity_title .= ' - ' . $activity_type;
                                            }
                                        }
                                    }

                                    if (is_numeric($sc_statement_row['debit_amount'])) {
                                        $this_amount = $sc_statement_row['debit_amount'];
                                    } else if (is_numeric($sc_statement_row['credit_amount'])) {
                                        $this_amount = $sc_statement_row['credit_amount'];
                                    } else {
                                        $this_amount = 0;
                                    }

                                    if ($sc_statement_row['activity_type'] == 'S') {
                                        $order_product_info_row = $this->_get_orders_product_particulars($sc_statement_row['trans_id']);

                                        $this_amount = $this_amount * $order_product_info_row['final_price'] * $order_currency_value;
                                    }

                                    $reserve_link = TEXT_SC_STAT_NOT_APPLICABLE;

                                    $export_csv_content = $export_csv_content .
                                            "\n" .
                                            '"' . $sc_statement_row['history_id'] . '",' .
                                            '"' . $sc_statement_row['activity_date'] . '",' .
                                            '"' . $activity_title . '",' .
                                            '"' . $sc_history_by_str . '",' .
                                            '"' . $sc_statement_row['customer_id'] . '",' .
                                            '"' . $payment_gateway . '",' .
                                            '"' . tep_mb_convert_encoding(str_replace(array('"', '<br>', '<BR>'), array('""', ''), $sc_statement_row['activity_desc']), 'EUC-CN', 'UTF-8') . '",' .
                                            '"' . (is_numeric($sc_statement_row['currency']) ? $currencies->get_code_by_id((int) $sc_statement_row['currency']) : $sc_statement_row['currency']) . '",' .
                                            '"' . (is_numeric($sc_statement_row['debit_amount']) ? $sc_statement_row['debit_amount'] : '') . '",' .
                                            '"' . (is_numeric($sc_statement_row['credit_amount']) ? $sc_statement_row['credit_amount'] : '') . '",' .
                                            '"' . (($order_currency_value == 0) ? '' : ((($this_amount / $order_currency_value) > 0) ? round($this_amount / $order_currency_value, 4) : '')) . '",' .
                                            '"' . (is_numeric($sc_statement_row['running_balance']['amt']) ? round($sc_statement_row['running_balance']['amt'], 4) : '') . '",' .
                                            '"' . (isset($sc_statement_row['r_after_balance']) && is_numeric($sc_statement_row['r_after_balance']) ? $sc_statement_row['r_after_balance'] : '') . '",' .
                                            '"' . (isset($sc_statement_row['customers_email_address']) ? $sc_statement_row['customers_email_address'] : '') . '"';
                                    $row_count++;
                                }
                            }
                        }
                        // Display last row for store credit daily history
                        $export_csv_content = $export_csv_content . "\n" . $this->_display_store_credits_daily_history($this->store_opening_data, 'O', $_SESSION[$session_name]["sc_type"], true);
                        $export_csv_content = $export_csv_content . "\n";
                    }
                }
                break;
            default:
                break;
        }

        $currencies->set_show_currency_symbol();
        return $export_csv_content;
    }

    function _get_orders_particulars($order_id) {
        $order_info_select_sql = "	SELECT currency, currency_value, date_purchased, orders_status, payment_methods_parent_id
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        return $order_info_row;
    }

    function _get_orders_product_particulars($order_id) {
        $order_product_select_sql = "	SELECT final_price
                                        FROM " . TABLE_ORDERS_PRODUCTS . "
                                        WHERE orders_id = '" . tep_db_input($order_id) . "'
                                            AND custom_products_type_id = 3";
        $order_product_result_sql = tep_db_query($order_product_select_sql);
        $order_product_row = tep_db_fetch_array($order_product_result_sql);

        return $order_product_row;
    }

    function _manual_adjust_form($filename, $user_id) {
        global $currencies;
        $report_section_html = '';

        // Permission for making the manual adjustment
        $manual_deduct_permission = tep_admin_files_actions(FILENAME_STORE_CREDIT, 'STORE_CREDIT_MANUAL_DEDUCT');
        $manual_add_permission = tep_admin_files_actions(FILENAME_STORE_CREDIT, 'STORE_CREDIT_MANUAL_ADD');

        $currency_selection_array = tep_get_currencies_list();

        if ($manual_deduct_permission || $manual_add_permission) {
            $user_data_array = $this->_get_user_particulars($user_id);
            ob_start();
            ?>
            <br>
            <table width="100%" border="0" align="center" cellpadding="2" cellspacing="1" valign="middle">
                <tr>
                    <td width="47%" valign="top" class="<?= $manual_deduct_permission ? 'formArea' : '' ?>">
                        <? if ($manual_deduct_permission) { ?>
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="formAreaTitle"><?= TABLE_HEADING_SC_STAT_MANUAL_DEDUCT ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?= tep_draw_form('manual_deduct_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_deduct&cont=2', 'post', '') ?>
                                        <?= tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role) . tep_draw_hidden_field("currency_id", $user_data_array['sc_currency_id']) ?>
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td width="30%" class="main"><?= ENTRY_SC_STAT_SC_TYPE ?></td>
                                                <td class="main"><?= tep_draw_pull_down_menu("manual_sc_type", $this->credit_accounts_type_selection, '', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_STAT_DEDUCT_AMOUNT ?></td>
                                                <td class="main">-&nbsp;<?= ($user_data_array['sc_currency_id'] == '' ? tep_draw_pull_down_menu("currency_id", $currency_selection_array) : $currencies->get_code_by_id($user_data_array['sc_currency_id'])) . ' ' . tep_draw_input_field('deduct_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SC_STAT_MANUAL_ADJUST_COMMENT ?></td>
                                                <td class="main"><?= tep_draw_textarea_field('comments', 'soft', '50', '5', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SC_STAT_NOTIFY_USERS ?></td>
                                                <td class="main"><?= tep_draw_checkbox_field('show_comments', '1') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top">&nbsp;</td>
                                                <td class="main"><?= tep_submit_button(BUTTON_DEDUCT_BALANCE, ALT_BUTTON_DEDUCT_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton') ?></td>
                                            </tr>
                                        </table>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        <? } ?>
                    </td>
                    <td>&nbsp;</td>
                    <td width="47%" valign="top" class="<?= $manual_add_permission ? 'formArea' : '' ?>">
                        <? if ($manual_add_permission) { ?>
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="formAreaTitle"><?= TABLE_HEADING_SC_STAT_MANUAL_ADD ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?= tep_draw_form('manual_add_form', $filename, tep_get_all_get_params(array('subaction', 'cont')) . 'subaction=manual_add&cont=2', 'post', '') ?>
                                        <?= tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role) . tep_draw_hidden_field("currency_id", $user_data_array['sc_currency_id']) ?>
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td width="30%" class="main"><?= ENTRY_SC_STAT_SC_TYPE ?></td>
                                                <td class="main"><?= tep_draw_pull_down_menu("manual_sc_type", $this->credit_accounts_type_selection, '', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SC_STAT_ADD_AMOUNT ?></td>
                                                <td class="main">+&nbsp;<?= ($user_data_array['sc_currency_id'] == '' ? tep_draw_pull_down_menu("currency_id", $currency_selection_array) : $currencies->get_code_by_id($user_data_array['sc_currency_id'])) . ' ' . tep_draw_input_field('add_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SC_STAT_MANUAL_ADJUST_COMMENT ?></td>
                                                <td class="main"><?= tep_draw_textarea_field('comments', 'soft', '50', '5', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SC_STAT_NOTIFY_USERS ?></td>
                                                <td class="main"><?= tep_draw_checkbox_field('show_comments', '1') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top">&nbsp;</td>
                                                <td class="main"><?= tep_submit_button(BUTTON_ADD_BALANCE, ALT_BUTTON_ADD_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton') ?></td>
                                            </tr>
                                        </table>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        <? } ?>
                    </td>
                </tr>
            </table>
            <?php
            $report_section_html = ob_get_contents();
            ob_end_clean();
        }
        return $report_section_html;
    }

    function manual_deduct_amount($input_array, &$messageStack) {
        global $currencies;
        $error = false;

        $manual_deduct_permission = tep_admin_files_actions(FILENAME_STORE_CREDIT, 'STORE_CREDIT_MANUAL_DEDUCT');
        if (!isset($input_array['currency_id'])) {
            $input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        }

        if ($manual_deduct_permission) {
            if (isset($this->credit_accounts_type[$input_array['manual_sc_type']])) {
                // Will create store credit account if not exists
                if ($this->_check_credits_account_exists($input_array['user_id'], $input_array['currency_id'])) {
                    // check store credit currency differences, to prevent customer change currency while crew holding old currency value
                    $user_info_row = $this->_get_user_particulars($input_array['user_id']);
                    if ($user_info_row['sc_currency_id'] == $input_array['currency_id']) {
                        $deduct_amount = trim($input_array['deduct_amount']);
                        if (is_numeric($deduct_amount) && $deduct_amount > 0) {
                            // Update live credit balance
                            $update_info = array(array('field_name' => $input_array['manual_sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                                    'operator' => '-',
                                    'value' => $deduct_amount,
                                    'currency_id' => $input_array['currency_id'])
                            );

                            $new_sc_balance = $this->_set_store_credit_balance($input_array['user_id'], $update_info);

                            // Insert store credit history
                            $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                                'store_credit_account_type' => tep_db_prepare_input($input_array['manual_sc_type']),
                                'store_credit_history_date' => 'now()',
                                'store_credit_history_currency_id' => (int) $new_sc_balance['currency_id'],
                                'store_credit_history_debit_amount' => (double) $deduct_amount,
                                'store_credit_history_credit_amount' => 'NULL',
                                'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                                'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                                'store_credit_activity_type' => (isset($input_array['act_type'])) ? $input_array['act_type'] : LOG_SC_ACTIVITY_TYPE_MANUAL_RECLAIM,
                                'store_credit_history_activity_title' => (isset($input_array['act_type_title'])) ? $input_array['act_type_title'] : LOG_SC_STAT_MANUAL_DEDUCTION,
                                'store_credit_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
                                'store_credit_history_activity_desc_show' => (int) $input_array['show_comments'],
                                'store_credit_history_added_by' => $this->identity_email,
                                'store_credit_history_added_by_role' => 'admin'
                            );
                            tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                            $sc_transaction_id = tep_db_insert_id();

                            $return_result = array('sc_type' => $input_array['manual_sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double) $deduct_amount, 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['currency_id']));

                            $messageStack->add_session((isset($input_array['act_type']) ? SUCCESS_SC_STAT_SC_DEBITED : SUCCESS_SC_STAT_MANUAL_DEDUCTION), 'success');

                            // Notification to admin
                            $user_info_array = $this->_get_user_particulars($input_array['user_id']);

                            $display_admin_group = tep_get_admin_group_name($this->identity_email);
                            if (tep_not_null($display_admin_group)) {
                                $display_admin_group = ' [' . $display_admin_group . ']';
                            }


                            $sc_notification_email_contents = sprintf(EMAIL_SC_STAT_MANUAL_ACTION_CONTENT, '<a href="' . tep_href_link(FILENAME_STORE_CREDIT, 'transaction_id=' . $sc_transaction_id . '&action=show_report&report=1', 'SSL') . '">SC-' . $sc_transaction_id . '</a>', $this->credit_accounts_type[$input_array['manual_sc_type']], LOG_SC_STAT_MANUAL_DEDUCTION, $currencies->format($deduct_amount, false, $currencies->get_code_by_id((int) $input_array['currency_id'])), $user_info_array['email'], date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $this->identity_email . $display_admin_group, tep_db_prepare_input($input_array['comments'])
                            );

                            $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
                            for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SC_STAT_MANUAL_UPDATE_SUBJECT, LOG_SC_STAT_MANUAL_DEDUCTION, $sc_transaction_id))), $sc_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        } else {
                            $error = true;
                            $messageStack->add_session(ERROR_SC_STAT_INVALID_DEDUCTION_AMOUNT, 'error');
                        }
                    } else {
                        $error = true;
                        $messageStack->add_session(sprintf(ERROR_SC_STAT_SC_CURRENCY_CHANGED, $currencies->get_code_by_id($input_array['currency_id']), $currencies->get_code_by_id($user_info_row['sc_currency_id'])), 'error');
                    }
                } else {
                    $error = true;
                    $messageStack->add_session(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_SC_STAT_SC_TYPE_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    function redeem_on_store_credit($input_array) {
        global $currencies;
        $error = false;

        if (isset($this->credit_accounts_type[$input_array['manual_sc_type']])) {
            if ($this->_check_credits_account_exists($input_array['user_id'], $input_array['currency_id'])) {
                $redeem_amount = trim($input_array['sc_redeem_amt']);

                if (is_numeric($redeem_amount) && $redeem_amount > 0) {
                    $update_info = array(array('field_name' => $input_array['manual_sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                            'operator' => '+',
                            'value' => $redeem_amount,
                            'currency_id' => $input_array['currency_id'])
                    );

                    $user_info_array = $this->_get_user_particulars($input_array['user_id']);
                    $new_sc_balance = $this->_set_store_credit_balance($input_array['user_id'], $update_info);

                    $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                        'store_credit_account_type' => tep_db_prepare_input($input_array['manual_sc_type']),
                        'store_credit_history_date' => 'now()',
                        'store_credit_history_currency_id' => (int) $new_sc_balance['currency_id'],
                        'store_credit_history_debit_amount' => 'NULL',
                        'store_credit_history_credit_amount' => (double) $redeem_amount,
                        'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                        'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                        'store_credit_activity_type' => 'D',
                        'store_credit_history_trans_id' => $input_array['sp_redeem_id'],
                        'store_credit_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
                        'store_credit_history_activity_desc_show' => (int) $input_array['show_comments'],
                        'store_credit_history_added_by' => $user_info_array['email'],
                        'store_credit_history_added_by_role' => 'customers'
                    );
                    tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                    $sc_transaction_id = tep_db_insert_id();

                    $sp_redeem_sql_data_array = array('store_points_redeem_status' => 3,
                        'store_points_paid_currency_amount' => $redeem_amount,
                        'store_points_redeem_reference' => $sc_transaction_id,
                        'store_points_redeem_last_modified' => 'now()'
                    );

                    tep_db_perform(TABLE_STORE_POINTS_REDEEM, $sp_redeem_sql_data_array, 'update', " store_points_redeem_id = '" . (int) $input_array['sp_redeem_id'] . "'");

                    $sp_redeem_history_data_array = array('store_points_redeem_id' => $input_array['sp_redeem_id'],
                        'store_points_redeem_status' => 3,
                        'date_added' => 'now()',
                        'payee_notified' => 0,
                        'comments' => 'Store Credit Redeemed: ' . $currencies->format($redeem_amount, false, $input_array['sc_redeem_paid_currency']) . "\n" . 'Reference id: NRSC-' . $sc_transaction_id,
                        'changed_by' => $this->identity_email,
                        'changed_by_role' => 'admin'
                    );

                    tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sp_redeem_history_data_array);
                    //$messageStack->add_session(SUCCESS_SC_STAT_REDEEM, 'success');
                } else {
                    $error = true;
                    //$messageStack->add_session(ERROR_SC_STAT_INVALID_ADDITION_AMOUNT, 'error');
                }
            } else {
                $error = true;
                //$messageStack->add_session(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            //$messageStack->add_session(ERROR_SC_STAT_SC_TYPE_NOT_EXISTS, 'error');
        }
    }

    function manual_add_amount($input_array, &$messageStack) {
        global $currencies;
        $error = false;

        if (isset($input_array['auto_permission_granted']) && $input_array['auto_permission_granted'] == true) {
            $manual_add_permission = $input_array['auto_permission_granted'];
        } else {
            $manual_add_permission = tep_admin_files_actions(FILENAME_STORE_CREDIT, 'STORE_CREDIT_MANUAL_ADD');
        }
        if (!isset($input_array['currency_id'])) {
            $input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        }

        if ($manual_add_permission) {
            if (isset($this->credit_accounts_type[$input_array['manual_sc_type']])) {
                // Will create store credit account if not exists
                if ($this->_check_credits_account_exists($input_array['user_id'], $input_array['currency_id'])) {
                    // check store credit currency differences, to prevent customer change currency while crew holding old currency value
                    if ($input_array['user_role'] == 'supplier') {
                        $user_info_row = $this->_get_supplier_particulars($input_array['user_id']);
                    } else {
                        $user_info_row = $this->_get_user_particulars($input_array['user_id']);
                    }
                    if ($user_info_row['sc_currency_id'] != '' && $user_info_row['sc_currency_id'] == $input_array['currency_id']) {
                        $add_amount = trim($input_array['add_amount']);
                        if (is_numeric($add_amount) && $add_amount > 0) {
                            $add_amount_in_usd = $currencies->advance_currency_conversion($add_amount, $currencies->get_code_by_id($input_array['currency_id']), DEFAULT_CURRENCY, true, 'sell');
                            if ($this->_is_within_daily_limit($add_amount_in_usd)) {
                                // Update live credit balance
                                $update_info = array(array(
                                    'field_name' => $input_array['manual_sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                                    'operator' => '+',
                                    'value' => $add_amount,
                                    'currency_id' => $input_array['currency_id'])
                                );

                                $new_sc_balance = $this->_set_store_credit_balance($input_array['user_id'], $update_info);

                                // Insert store credit history
                                $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                                    'store_credit_account_type' => tep_db_prepare_input($input_array['manual_sc_type']),
                                    'store_credit_history_date' => 'now()',
                                    'store_credit_history_currency_id' => (int) $new_sc_balance['currency_id'],
                                    'store_credit_history_debit_amount' => 'NULL',
                                    'store_credit_history_credit_amount' => (double) $add_amount,
                                    'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                                    'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                                    'store_credit_activity_type' => (isset($input_array['act_type'])) ? $input_array['act_type'] : LOG_SC_ACTIVITY_TYPE_MANUAL_ISSUE,
                                    'store_credit_history_activity_title' => (isset($input_array['act_type_title'])) ? $input_array['act_type_title'] : LOG_SC_STAT_MANUAL_ADDITION,
                                    'store_credit_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
                                    'store_credit_history_activity_desc_show' => (int) $input_array['show_comments'],
                                    'store_credit_history_added_by' => $this->identity_email,
                                    'store_credit_history_added_by_role' => 'admin'
                                );
                                if ((isset($input_array['type']) && tep_not_null($input_array['type'])) && (isset($input_array['id']) && tep_not_null($input_array['id']))) {
                                    $sc_balance_history_data_array['store_credit_history_trans_type'] = tep_db_prepare_input($input_array['type']);
                                    $sc_balance_history_data_array['store_credit_history_trans_id'] = tep_db_prepare_input($input_array['id']);
                                }
                                tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                                $sc_transaction_id = tep_db_insert_id();

                                $return_result = array('sc_type' => $input_array['manual_sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double) $add_amount, 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['currency_id']));

                                $messageStack->add_session((isset($input_array['act_type']) ? SUCCESS_SC_STAT_SC_CREDITED : SUCCESS_SC_STAT_MANUAL_ADDITION), 'success');

                                // Notification to admin
                                $user_info_array = $this->_get_user_particulars($input_array['user_id']);

                                $display_admin_group = tep_get_admin_group_name($this->identity_email);
                                if (tep_not_null($display_admin_group)) {
                                    $display_admin_group = ' [' . $display_admin_group . ']';
                                }

                                if (isset($input_array['act_type']) && $input_array['act_type'] == LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW) {
                                    $email_subject = implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SC_STAT_MANUAL_UPDATE_SUBJECT, LOG_SC_STAT_DISBURSEMENT, $sc_transaction_id)));
                                } else {
                                    $email_subject = implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SC_STAT_MANUAL_UPDATE_SUBJECT, LOG_SC_STAT_MANUAL_ADDITION, $sc_transaction_id)));
                                }

                                $sc_notification_email_contents = sprintf(EMAIL_SC_STAT_MANUAL_ACTION_CONTENT, '<a href="' . tep_href_link(FILENAME_STORE_CREDIT, 'transaction_id=' . $sc_transaction_id . '&action=show_report&report=1', 'SSL') . '">SC-' . $sc_transaction_id . '</a>', $this->credit_accounts_type[$input_array['manual_sc_type']], LOG_SC_STAT_MANUAL_ADDITION, $currencies->format($add_amount, false, $currencies->get_code_by_id((int) $input_array['currency_id'])), $user_info_array['email'], date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $this->identity_email . $display_admin_group, tep_db_prepare_input($input_array['comments'])
                                );

                                $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
                                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                    @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $email_subject, $sc_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                }
                            } else {
                                $error = true;
                                $messageStack->add_session(ERROR_REACHED_DAILY_CREDIT_LIMIT, 'error');
                            }
                        } else {
                            $error = true;
                            $messageStack->add_session(ERROR_SC_STAT_INVALID_ADDITION_AMOUNT, 'error');
                        }
                    } else {
                        $error = true;
                        $messageStack->add_session(sprintf(ERROR_SC_STAT_SC_CURRENCY_CHANGED, $currencies->get_code_by_id($input_array['currency_id']), $currencies->get_code_by_id($user_info_row['sc_currency_id'])), 'error');
                    }
                } else {
                    $error = true;
                    $messageStack->add_session(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_SC_STAT_SC_TYPE_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    function miscellaneous_deduct_amount($input_array, $action_message, $action_desc, &$messageStack = array()) {
        global $currencies;

        $return_result = array();
        $return_msg = array();
        $error = false;

        if (!isset($input_array['currency_id'])) {
            $input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        }

        if (isset($this->credit_accounts_type[$input_array['sc_type']])) {
            // Will create store credit account if not exists
            if ($this->_check_credits_account_exists($input_array['user_id'], $input_array['currency_id'])) {
                $deduct_amount = trim($input_array['deduct_amount']);
                if (is_numeric($deduct_amount) && $deduct_amount > 0) {
                    // Update live credit balance
                    $update_info = array(array('field_name' => $input_array['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                            'operator' => '-',
                            'value' => $deduct_amount,
                            'currency_id' => $input_array['currency_id'])
                    );

                    $new_sc_balance = $this->_set_store_credit_balance($input_array['user_id'], $update_info);

                    // Insert store credit history
                    $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                        'store_credit_account_type' => tep_db_prepare_input($input_array['sc_type']),
                        'store_credit_history_date' => 'now()',
                        'store_credit_history_currency_id' => (int) $new_sc_balance['currency_id'],
                        'store_credit_history_debit_amount' => (double) $deduct_amount,
                        'store_credit_history_credit_amount' => 'NULL',
                        'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                        'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                        'store_credit_history_trans_type' => tep_db_prepare_input($input_array['type']),
                        'store_credit_history_trans_id' => tep_db_prepare_input($input_array['id']),
                        'store_credit_activity_type' => tep_db_prepare_input($input_array['act_type']),
                        'store_credit_history_activity_title' => $action_message,
                        'store_credit_history_activity_desc' => tep_db_prepare_input($action_desc),
                        'store_credit_history_activity_desc_show' => (int) $input_array['show_desc'],
                        'store_credit_history_added_by' => $this->identity_email,
                        'store_credit_history_added_by_role' => 'admin'
                    );
                    tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                    $sc_transaction_id = tep_db_insert_id();

                    $return_result = array('sc_type' => $input_array['sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double) $deduct_amount, 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['currency_id']));

                    $return_msg[] = SUCCESS_SC_STAT_SC_DEBITED;
                } else {
                    $error = true;
                    $return_msg[] = ERROR_SC_STAT_INVALID_ADDITION_AMOUNT;
                }
            } else {
                $error = true;
                $return_msg[] = ERROR_SC_STAT_USER_NOT_EXISTS;
            }
        } else {
            $error = true;
            $return_msg[] = ERROR_SC_STAT_SC_TYPE_NOT_EXISTS;
        }

        if (is_object($messageStack)) {
            $key = $error ? 'error' : 'success';

            foreach ($return_msg as $idx => $msg) {
                $messageStack->add_session($msg, $key);
            }
        } else if ($error && is_array($messageStack)) {
            $messageStack = array_merge($messageStack, $return_msg);
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    function miscellaneous_add_amount($input_array, $action_message, $action_desc, &$messageStack = array()) {
        global $currencies;

        $return_result = array();
        $return_msg = array();
        $error = false;

        if (!isset($input_array['currency_id'])) {
            $input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        }

        if (isset($this->credit_accounts_type[$input_array['sc_type']])) {
            // Will create store credit account if not exists
            if ($this->_check_credits_account_exists($input_array['user_id'], $input_array['currency_id'])) {
                $add_amount = trim($input_array['add_amount']);
                if (is_numeric($add_amount) && $add_amount > 0) {
                    // Update live credit balance
                    $update_info = array(array('field_name' => $input_array['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
                            'operator' => '+',
                            'value' => $add_amount,
                            'currency_id' => $input_array['currency_id'])
                    );

                    $new_sc_balance = $this->_set_store_credit_balance($input_array['user_id'], $update_info);

                    // Insert store credit history
                    $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                        'store_credit_account_type' => tep_db_prepare_input($input_array['sc_type']),
                        'store_credit_history_date' => 'now()',
                        'store_credit_history_currency_id' => (int) $new_sc_balance['currency_id'],
                        'store_credit_history_debit_amount' => 'NULL',
                        'store_credit_history_credit_amount' => (double) $add_amount,
                        'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                        'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                        'store_credit_history_trans_type' => tep_db_prepare_input($input_array['type']),
                        'store_credit_history_trans_id' => tep_db_prepare_input($input_array['id']),
                        'store_credit_activity_type' => tep_db_prepare_input($input_array['act_type']),
                        'store_credit_history_activity_title' => $action_message,
                        'store_credit_history_activity_desc' => tep_db_prepare_input($action_desc),
                        'store_credit_history_activity_desc_show' => (int) $input_array['show_desc'],
                        'store_credit_history_added_by' => $this->identity_email,
                        'store_credit_history_added_by_role' => 'admin'
                    );
                    tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                    $sc_transaction_id = tep_db_insert_id();

                    $return_result = array('sc_type' => $input_array['sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double) $add_amount, 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['currency_id']));

                    $return_msg[] = SUCCESS_SC_STAT_SC_CREDITED;
                } else {
                    $error = true;
                    $return_msg[] = ERROR_SC_STAT_INVALID_ADDITION_AMOUNT;
                }
            } else {
                $error = true;
                $return_msg[] = ERROR_SC_STAT_USER_NOT_EXISTS;
            }
        } else {
            $error = true;
            $return_msg[] = ERROR_SC_STAT_SC_TYPE_NOT_EXISTS;
        }

        if (is_object($messageStack)) {
            $key = $error ? 'error' : 'success';

            foreach ($return_msg as $idx => $msg) {
                $messageStack->add_session($msg, $key);
            }
        } else if ($error && is_array($messageStack)) {
            $messageStack = array_merge($messageStack, $return_msg);
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    function issue_store_credit($user_id, $act_type, $trans_array, $admin_msg, &$messageStack, $fullRefund = false) {
        global $language, $currencies;
        $error = false;

        $default_currency_id = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
        $return_result = array();
        $sc_spended_amount_array = array(
            'RP' => 0,
            'R' => 0,
            'NRP' => 0,
            'NR' => 0,
            'currency_id' => $default_currency_id,
            'currency_code' => DEFAULT_CURRENCY
        );

        $sc_reverse_issue_amount = $sc_irreverse_issue_amount = 0;
        $payment_refund_amount = $non_sc_payment_refund_amount = $sc_payment_refund_amount = 0;
        $add_sc_amount = 0;
        $add_non_sc_amount = 0;

        $ot_subtotal = 0;
        $ot_sc = 0;
        $ot_coupon = 0;
        $ot_surcharge = 0;
        $ot_gst = 0;
        $refund_gst = false;
        $refund_gst_amount = 0;
        $refund_surcharge = false;
        $refund_surcharge_amount = 0;        

        $display_admin_group = tep_get_admin_group_name($this->identity_email);
        if (tep_not_null($display_admin_group)) {
            $display_admin_group = ' [' . $display_admin_group . ']';
        }

        // If not found treat as reversible
        $sc_type = 'R';

        $payment_methods_obj = new payment_methods($trans_array['payment_methods_id']);
        $payment_methods_obj = $payment_methods_obj->payment_method_array;

        if (isset($payment_methods_obj) && (int) $payment_methods_obj->payment_methods_id > 0) {
            if ($payment_methods_obj->confirm_complete_days > 0) {
                ;
            } else {
                $sc_type = 'NR';
            }
        }

        if (isset($this->credit_accounts_type[$sc_type])) {
            // Will create store credit account if not exists
            $pay_currency_id = (isset($trans_array['pay_currency'])) ? array_search($trans_array['pay_currency'], $currencies->internal_currencies) : 0;
            if ($this->_check_credits_account_exists($user_id, $pay_currency_id)) {
                if (isset($trans_array['add_sc_amount'])) {
                    $add_sc_amount = trim($trans_array['add_sc_amount']);
                }

                if (isset($trans_array['add_non_sc_amount'])) {
                    $add_non_sc_amount = trim($trans_array['add_non_sc_amount']);
                } else {
                    $add_non_sc_amount = trim($trans_array['add_amount']);
                }

                //if (is_numeric($add_amount) && $add_amount > 0) {
                if ((is_numeric($add_non_sc_amount) && $add_non_sc_amount > 0) || (is_numeric($add_sc_amount) && $add_sc_amount > 0)) {
                    $ot_amount_select_sql = "   SELECT ot_subtotal.value AS ot_subtotal_value, ot_gv.value AS ot_gv_value,
                                                    ot_coupon.value AS ot_coupon_value, ot_surcharge.value AS ot_surcharge_value,
                                                    ot_gst.value AS ot_gst_value
                                                FROM " . TABLE_ORDERS_TOTAL . " AS ot_subtotal 
                                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gv
                                                    ON ot_gv.orders_id = ot_subtotal.orders_id
                                                        AND ot_gv.class='ot_gv'
                                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_coupon
                                                    ON ot_coupon.orders_id = ot_subtotal.orders_id
                                                        AND ot_coupon.class='ot_coupon'
                                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_surcharge
                                                    ON ot_surcharge.orders_id = ot_subtotal.orders_id
                                                        AND ot_surcharge.class='ot_surcharge'
                                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot_gst
                                                    ON ot_gst.orders_id = ot_subtotal.orders_id
                                                        AND ot_gst.class='ot_gst'
                                                WHERE ot_subtotal.orders_id='" . tep_db_input($trans_array['id']) . "'
                                                    AND ot_subtotal.class='ot_subtotal'";
                    $ot_amount_result_sql = tep_db_query($ot_amount_select_sql);
                    if ($ot_amount_row = tep_db_fetch_array($ot_amount_result_sql)) {
                        $ot_subtotal = tep_not_null($ot_amount_row['ot_subtotal_value']) ? $ot_amount_row['ot_subtotal_value'] : 0;
                        $ot_sc = tep_not_null($ot_amount_row['ot_gv_value']) ? $ot_amount_row['ot_gv_value'] : 0;
                        $ot_coupon = tep_not_null($ot_amount_row['ot_coupon_value']) ? $ot_amount_row['ot_coupon_value'] : 0;
                        $ot_surcharge = tep_not_null($ot_amount_row['ot_surcharge_value']) ? $ot_amount_row['ot_surcharge_value'] : 0;
                        if (tep_not_null($ot_amount_row['ot_gst_value'])) {
                            $refund_gst = true;
                            $ot_gst = $ot_amount_row['ot_gst_value'];
                        }
                    }

                    if ($add_non_sc_amount > 0) {
                        $add_amount = $add_non_sc_amount;
                        $exch_rate = 1;
                        $used_sc = false;

                        // TODO: Need to consider Pending order placed by SC then get modified
                        $sc_select_sql = "  SELECT store_credit_account_type, store_credit_history_debit_amount, store_credit_history_currency_id
                                            FROM " . TABLE_STORE_CREDIT_HISTORY . "
                                            WHERE store_credit_history_trans_type='C'
                                                AND store_credit_history_trans_id='" . tep_db_input($trans_array['id']) . "'
                                                AND store_credit_activity_type='P'";
                        $sc_result_sql = tep_db_query($sc_select_sql);
                        while ($sc_row = tep_db_fetch_array($sc_result_sql)) {
                            $used_sc = true;
                            $sc_spended_amount_array[$sc_row['store_credit_account_type']] = $sc_row['store_credit_history_debit_amount'];
                            $sc_spended_amount_array['currency_id'] = $sc_row['store_credit_history_currency_id'];
                            $sc_spended_amount_array['currency_code'] = $currencies->get_code_by_id($sc_row['store_credit_history_currency_id']);
                        }

                        $payment_amount_select_sql = "	SELECT value, text
                                                        FROM " . TABLE_ORDERS_TOTAL . "
                                                        WHERE orders_id='" . tep_db_input($trans_array['id']) . "'
                                                            AND class='ot_total'";
                        $payment_amount_result_sql = tep_db_query($payment_amount_select_sql);
                        $payment_amount_row = tep_db_fetch_array($payment_amount_result_sql);

                        // if this order is having payment surcharge included, minus it out
						// $surcharge_amount_select_sql = "SELECT value, text
						// 								FROM " . TABLE_ORDERS_TOTAL . "
						// 								WHERE orders_id='" . tep_db_input($trans_array['id']) . "'
						// 									AND class='ot_surcharge'";
						// $surcharge_amount_result_sql = tep_db_query($surcharge_amount_select_sql);
						// if ($surcharge_amount_row = tep_db_fetch_array($surcharge_amount_result_sql)) {
                        if ($ot_surcharge > 0) {
                            $payment_amount_row['value'] = $payment_amount_row['value'] - $ot_surcharge;
                            $trans_array['order_total_amount'] - $trans_array['order_total_amount'] - $ot_surcharge;                       
                        }

                        if ($payment_amount_row['value'] > 0) {
                            // exclude GST
                            $payment_amount_row['value'] = $payment_amount_row['value'] - $ot_gst;

                            $sc_products_amount_select_sql = "	SELECT SUM(final_price * products_quantity) AS sc_total_amount
                                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                                WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                                    AND custom_products_type_id = 3
                                                                    AND orders_products_is_compensate = 0";
                            $sc_products_amount_result_sql = tep_db_query($sc_products_amount_select_sql);
                            $sc_products_amount_row = tep_db_fetch_array($sc_products_amount_result_sql);
                            if ($sc_products_amount_row['sc_total_amount'] > 0) {
                                $payment_amount_row['value'] = $payment_amount_row['value'] - number_format($sc_products_amount_row['sc_total_amount'], 2, '.', '');
                            }

                            if (isset($trans_array['payment_refund']) && $trans_array['payment_refund'] === true) { // Refund to payment gateway
                                if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                                    $sc_spended_amount_array[$sc_type . 'P'] = $payment_amount_row['value'];
                                } else {
                                    $sc_spended_amount_array[$sc_type . 'P'] = ($payment_amount_row['value'] * $trans_array['currency_value']);
                                }
                            } else {
                                if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                                    $sc_spended_amount_array[$sc_type] += $payment_amount_row['value'];
                                } else {
                                    $sc_spended_amount_array[$sc_type] += ($payment_amount_row['value'] * $trans_array['currency_value']);
                                }
                            }
                        }

                        if ($sc_spended_amount_array['currency_id'] != $default_currency_id) {
                            $exch_rate = $trans_array['currency_value'];
                        }
                        $add_amount = number_format(($add_amount * $exch_rate), 4, '.', '');

                        // when getting total previously refunded value, we need to exclude out store credit products from the order.
                        // store credit products only refund to payment gateway.
                        $latest_total_refunded_value_select_sql = "	SELECT SUM(products_canceled_price) AS new_total_refund
                                                                        FROM " . TABLE_ORDERS_PRODUCTS . "
                                                                        WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                                            AND custom_products_type_id <> 3
                                                                            AND orders_products_is_compensate = 0";
                        $latest_total_refunded_value_result_sql = tep_db_query($latest_total_refunded_value_select_sql);
                        $latest_total_refunded_value_row = tep_db_fetch_array($latest_total_refunded_value_result_sql);

                        $latest_total_refunded_value_row['new_total_refund'] = number_format(($latest_total_refunded_value_row['new_total_refund'] * $exch_rate), 4, '.', '');
                        $total_used_sc_amount = number_format($sc_spended_amount_array['RP'] + $sc_spended_amount_array['R'] + $sc_spended_amount_array['NRP'] + $sc_spended_amount_array['NR'], 4, '.', '');
                        $total_aaccumulated_value = 0;

                        $compensate_sc = (isset($trans_array['compensate_sc']) && $trans_array['compensate_sc'] === true) ? true : false;

                        if (!$compensate_sc) {
                            if ($add_amount > $latest_total_refunded_value_row['new_total_refund']) {
                                $add_amount -= ($add_amount - $latest_total_refunded_value_row['new_total_refund']);
                            }
                        }

                        if (( $latest_total_refunded_value_row['new_total_refund'] >= $add_amount ) || $compensate_sc) {
                            $before_refund_total_value = $latest_total_refunded_value_row['new_total_refund'] - $add_amount;

                            if ($add_amount > 0) {
                                if ($compensate_sc) {
                                    if ($sc_type == 'R') {
                                        $sc_reverse_issue_amount = $add_amount;
                                    } else if ($sc_type == 'NR') {
                                        $sc_irreverse_issue_amount = $add_amount;
                                    }
                                } else {
                                    if ($latest_total_refunded_value_row['new_total_refund'] > $total_used_sc_amount) {
                                        $add_amount = $total_used_sc_amount - $before_refund_total_value;
                                    }

                                    $so_far_refunded_amount = 0;

                                    if ($sc_spended_amount_array['RP'] > 0) {
                                        $total_aaccumulated_value += $sc_spended_amount_array['RP'];

                                        if ($before_refund_total_value < $total_aaccumulated_value) {
                                            if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value)) {
                                                $payment_refund_amount = $total_aaccumulated_value - $before_refund_total_value;
                                                $add_amount -= $payment_refund_amount;
                                                $so_far_refunded_amount += $payment_refund_amount;
                                            } else {
                                                $payment_refund_amount = $add_amount;
                                                $add_amount = 0;
                                                $so_far_refunded_amount += $payment_refund_amount;
                                            }
                                        }
                                    }

                                    if ($add_amount > 0) {
                                        $total_aaccumulated_value += $sc_spended_amount_array['R'];

                                        if ($before_refund_total_value < $total_aaccumulated_value) {
                                            if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount)) {
                                                $sc_reverse_issue_amount = $total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount;
                                                $add_amount -= $sc_reverse_issue_amount;
                                                $so_far_refunded_amount += $sc_reverse_issue_amount;
                                            } else {
                                                $sc_reverse_issue_amount = $add_amount;
                                                $add_amount = 0;
                                                $so_far_refunded_amount += $sc_reverse_issue_amount;
                                            }
                                        }
                                    }

                                    if ($add_amount > 0) {
                                        $total_aaccumulated_value += $sc_spended_amount_array['NRP'];

                                        if ($sc_spended_amount_array['NRP'] > 0) {
                                            if ($before_refund_total_value < $total_aaccumulated_value) {
                                                if ($add_amount > ($total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount)) {
                                                    $payment_refund_amount = $total_aaccumulated_value - $before_refund_total_value - $so_far_refunded_amount;
                                                    $add_amount -= $payment_refund_amount;
                                                    $so_far_refunded_amount += $payment_refund_amount;
                                                } else {
                                                    $payment_refund_amount = $add_amount;
                                                    $add_amount = 0;
                                                    $so_far_refunded_amount += $payment_refund_amount;
                                                }
                                            }
                                        }
                                    }

                                    if ($add_amount > 0) {
                                        if ($add_amount > $sc_spended_amount_array['NR']) {
                                            $sc_irreverse_issue_amount = $sc_spended_amount_array['NR'];
                                        } else {
                                            $sc_irreverse_issue_amount = $add_amount;
                                        }
                                    }
                                }

                                if ($sc_reverse_issue_amount > 0 || $sc_irreverse_issue_amount > 0) {
                                    $trans_info_select_sql = "	SELECT o.date_purchased, o.currency, o.currency_value, c.customers_email_address
																FROM " . TABLE_ORDERS . " AS o
																INNER JOIN " . TABLE_CUSTOMERS . " AS c
																	ON o.customers_id=c.customers_id
																WHERE o.orders_id = '" . tep_db_input($trans_array['id']) . "'";
                                    $trans_info_result_sql = tep_db_query($trans_info_select_sql);
                                    $trans_info_row = tep_db_fetch_array($trans_info_result_sql);

                                    $sc_notification_email_contents = "Order ID: %d\nOrder Date: %s\nOrder Amount: %s\nCustomer E-mail: %s\n\nStore Credit Transaction ID: SC%d\nIssue Amount: %s\n\nUpdate Date: %s\nUpdate IP: %s\nUpdate User: %s\n\nUpdate Comment:\n%s";
                                }

                                // Update live credit balance
                                if ($sc_reverse_issue_amount > 0) {
                                    if ($pay_currency_id > 0 && $pay_currency_id != $sc_spended_amount_array['currency_id']) {
                                        $sc_issue_currency_id = $pay_currency_id;
                                        $sc_reverse_issue_amount = $currencies->apply_currency_exchange($sc_reverse_issue_amount, $currencies->get_code_by_id($pay_currency_id), $trans_array['currency_value']);
                                    } else {
                                        $sc_issue_currency_id = $sc_spended_amount_array['currency_id'];
                                    }
                                    $sc_issue_currency = $currencies->get_code_by_id($sc_issue_currency_id);
                                    $update_info = array(array('field_name' => 'sc_reversible_amount',
                                            'operator' => '+',
                                            'value' => $sc_reverse_issue_amount,
                                            'currency_id' => $sc_issue_currency_id)
                                    );

                                    $new_sc_balance = $this->_set_store_credit_balance($user_id, $update_info);

                                    if ($new_sc_balance['currency_id'] != $sc_issue_currency_id) {
                                        $sc_issue_currency = $currencies->get_code_by_id($new_sc_balance['currency_id']);
                                        $sc_reverse_issue_amount = $currencies->advance_currency_conversion($sc_reverse_issue_amount, $currencies->get_code_by_id($sc_issue_currency_id), $sc_issue_currency, true, 'sell');
                                    }

                                    // Insert store credit history
                                    $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($user_id),
                                        'store_credit_account_type' => 'R',
                                        'store_credit_history_date' => 'now()',
                                        'store_credit_history_currency_id' => $new_sc_balance['currency_id'],
                                        'store_credit_history_debit_amount' => 'NULL',
                                        'store_credit_history_credit_amount' => (double) $sc_reverse_issue_amount,
                                        'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                                        'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                                        'store_credit_history_trans_type' => $trans_array['type'],
                                        'store_credit_history_trans_id' => $trans_array['id'],
                                        'store_credit_activity_type' => $act_type,
                                        'store_credit_history_activity_desc' => tep_db_prepare_input($admin_msg),
                                        'store_credit_history_activity_desc_show' => 1,
                                        'store_credit_history_added_by' => $this->identity_email,
                                        'store_credit_history_added_by_role' => 'admin'
                                    );
                                    tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                                    $insert_sc_trans_id = tep_db_insert_id();

                                    $return_result[] = array('sc_type' => 'R', 'sc_trans_id' => $insert_sc_trans_id, 'sc_amount' => (double) $sc_reverse_issue_amount, 'sc_currency' => $sc_issue_currency);

                                    $sc_notification_email_contents = sprintf($sc_notification_email_contents, $trans_array['id'], $trans_info_row['date_purchased'], strip_tags($payment_amount_row['text']), $trans_info_row['customers_email_address'], $insert_sc_trans_id, $currencies->format($sc_reverse_issue_amount, false, $currencies->get_code_by_id($new_sc_balance['currency_id'])), date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $this->identity_email . $display_admin_group, $admin_msg);

                                    $email_to_array = tep_parse_email_string(ISSUE_STORE_CREDIT_NOTIFY_EMAIL);
                                    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                        tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf('Issue Reversible Store Credit from Order #%d', $trans_array['id']))), $sc_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                    }
                                }

                                if ($sc_irreverse_issue_amount > 0) {
                                    if ($pay_currency_id > 0 && $pay_currency_id != $sc_spended_amount_array['currency_id']) {
                                        $sc_issue_currency_id = $pay_currency_id;
                                        $sc_irreverse_issue_amount = $currencies->apply_currency_exchange($sc_irreverse_issue_amount, $currencies->get_code_by_id($pay_currency_id), $trans_array['currency_value']);
                                    } else {
                                        $sc_issue_currency_id = $sc_spended_amount_array['currency_id'];
                                    }
                                    $sc_issue_currency = $currencies->get_code_by_id($sc_issue_currency_id);
                                    $update_info = array(array('field_name' => 'sc_irreversible_amount',
                                            'operator' => '+',
                                            'value' => $sc_irreverse_issue_amount,
                                            'currency_id' => $sc_issue_currency_id)
                                    );

                                    $new_sc_balance = $this->_set_store_credit_balance($user_id, $update_info);

                                    if ($new_sc_balance['currency_id'] != $sc_issue_currency_id) {
                                        $sc_issue_currency = $currencies->get_code_by_id($new_sc_balance['currency_id']);
                                        $sc_irreverse_issue_amount = $currencies->advance_currency_conversion($sc_irreverse_issue_amount, $currencies->get_code_by_id($sc_issue_currency_id), $sc_issue_currency, true, 'sell');
                                    }

                                    // Insert store credit history
                                    $sc_balance_history_data_array = array('customer_id' => tep_db_prepare_input($user_id),
                                        'store_credit_account_type' => 'NR',
                                        'store_credit_history_date' => 'now()',
                                        'store_credit_history_currency_id' => $new_sc_balance['currency_id'],
                                        'store_credit_history_debit_amount' => 'NULL',
                                        'store_credit_history_credit_amount' => (double) $sc_irreverse_issue_amount,
                                        'store_credit_history_r_after_balance' => (double) $new_sc_balance['sc_reverse'],
                                        'store_credit_history_nr_after_balance' => (double) $new_sc_balance['sc_irreverse'],
                                        'store_credit_history_trans_type' => $trans_array['type'],
                                        'store_credit_history_trans_id' => $trans_array['id'],
                                        'store_credit_activity_type' => $act_type,
                                        'store_credit_history_activity_desc' => tep_db_prepare_input($admin_msg),
                                        'store_credit_history_activity_desc_show' => 1,
                                        'store_credit_history_added_by' => $this->identity_email,
                                        'store_credit_history_added_by_role' => 'admin'
                                    );
                                    tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
                                    $insert_sc_trans_id = tep_db_insert_id();

                                    $return_result[] = array('sc_type' => 'NR', 'sc_trans_id' => $insert_sc_trans_id, 'sc_amount' => (double) $sc_irreverse_issue_amount, 'sc_currency' => $sc_issue_currency);

                                    $sc_notification_email_contents = sprintf($sc_notification_email_contents, $trans_array['id'], $trans_info_row['date_purchased'], strip_tags($payment_amount_row['text']), $trans_info_row['customers_email_address'], $insert_sc_trans_id, $currencies->format($sc_irreverse_issue_amount, false, $currencies->get_code_by_id($new_sc_balance['currency_id'])), date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $this->identity_email . $display_admin_group, $admin_msg);

                                    $email_to_array = tep_parse_email_string(ISSUE_STORE_CREDIT_NOTIFY_EMAIL);
                                    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                        tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf('Issue Non-Reversible Store Credit from Order #%d', $trans_array['id']))), $sc_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                    }
                                }

                                if ($payment_refund_amount > 0) {
                                    // if store credit is in used during order made, the refund amount will be converted to
                                    // match the store credit currency. We need to convert it back to USD.
                                    if ($used_sc) {
                                        //$non_sc_payment_refund_amount = $currencies->apply_currency_exchange($payment_refund_amount,DEFAULT_CURRENCY,(1/$trans_array['currency_value']));
                                        $non_sc_payment_refund_amount = round($payment_refund_amount * (1 / $trans_array['currency_value']), 6); // need to have more than 2 decimal places for amount accuracy
                                    } else {
                                        $non_sc_payment_refund_amount = $payment_refund_amount;
                                    }

                                    if ($fullRefund == true) {
                                        if ($refund_gst && ($non_sc_payment_refund_amount > 0)) {
                                            $refund_gst_amount = ( $non_sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon) ) * $ot_gst;
                                            $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $refund_gst_amount;
                                        }
                                        $refund_surcharge = true;
                                        $refund_surcharge_amount = $ot_surcharge;
                                        $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $ot_surcharge;
                                    } else {
                                        /* -- GST :: refund with GST (if any) -- */
                                        if ($refund_gst && ($non_sc_payment_refund_amount > 0)) {
                                            $refund_gst_amount = ( $non_sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon + $ot_surcharge) ) * $ot_gst;
                                            $non_sc_payment_refund_amount = $non_sc_payment_refund_amount + $refund_gst_amount;
                                        }
                                    }
                                }
                            } else {
                                $error = true;
                            }
                        } else {
                            $error = true;
                        }
                    }

                    if ($add_sc_amount > 0) {
                        $sc_spended_amount_array = array('RP' => 0,
                            'R' => 0,
                            'NRP' => 0,
                            'NR' => 0,
                            'currency_id' => $default_currency_id,
                            'currency_code' => DEFAULT_CURRENCY
                        );

                        if (isset($trans_array['pay_currency'])) {
                            $sc_spended_amount_array['currency_id'] = array_search($trans_array['pay_currency'], $currencies->internal_currencies);
                            $sc_spended_amount_array['currency_code'] = $trans_array['pay_currency'];
                        }
                        $add_amount = $add_sc_amount;
                        $exch_rate = 1;

                        $payment_amount_select_sql = "	SELECT value, text
                                                        FROM " . TABLE_ORDERS_TOTAL . "
                                                        WHERE orders_id='" . tep_db_input($trans_array['id']) . "'
                                                            AND class='ot_total'";
                        $payment_amount_result_sql = tep_db_query($payment_amount_select_sql);
                        $payment_amount_row = tep_db_fetch_array($payment_amount_result_sql);

                        if ($payment_amount_row['value'] > 0) {
                            if (isset($trans_array['payment_refund']) && $trans_array['payment_refund'] === true) { // Refund to payment gateway
                                if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                                    $sc_spended_amount_array[$sc_type . 'P'] = $payment_amount_row['value'];
                                } else {
                                    $sc_spended_amount_array[$sc_type . 'P'] = ($payment_amount_row['value'] * $trans_array['currency_value']);
                                }
                            } else {
                                if ($sc_spended_amount_array['currency_id'] == $default_currency_id) {
                                    $sc_spended_amount_array[$sc_type] += $payment_amount_row['value'];
                                } else {
                                    $sc_spended_amount_array[$sc_type] += ($payment_amount_row['value'] * $trans_array['currency_value']);
                                }
                            }
                        }

                        if ($sc_spended_amount_array['currency_id'] != $default_currency_id) {
                            $exch_rate = $trans_array['currency_value'];
                        }
                        $add_amount = number_format(($add_amount * $exch_rate), 2, '.', '');

                        $latest_total_refunded_value_select_sql = "	SELECT SUM(products_canceled_price) AS new_total_refund
                                                                        FROM " . TABLE_ORDERS_PRODUCTS . "
                                                                        WHERE orders_id = '" . tep_db_input($trans_array['id']) . "'
                                                                                AND custom_products_type_id = 3
                                                                                AND orders_products_is_compensate = 0";
                        $latest_total_refunded_value_result_sql = tep_db_query($latest_total_refunded_value_select_sql);
                        $latest_total_refunded_value_row = tep_db_fetch_array($latest_total_refunded_value_result_sql);

                        $latest_total_refunded_value_row['new_total_refund'] = number_format(($latest_total_refunded_value_row['new_total_refund'] * $exch_rate), 2, '.', '');
                        $total_used_sc_amount = number_format($sc_spended_amount_array['RP'] + $sc_spended_amount_array['NRP'], 2, '.', '');
                        $total_aaccumulated_value = 0;

                        if ($add_amount > $latest_total_refunded_value_row['new_total_refund']) {
                            $add_amount -= ($add_amount - $latest_total_refunded_value_row['new_total_refund']);
                        }

                        if (($latest_total_refunded_value_row['new_total_refund'] >= $add_amount)) {
                            $before_refund_total_value = $latest_total_refunded_value_row['new_total_refund'] - $add_amount;

                            if ($add_amount > 0) {
                                if ($latest_total_refunded_value_row['new_total_refund'] > $total_used_sc_amount) {
                                    $add_amount = $total_used_sc_amount - $before_refund_total_value;
                                }

                                if ($sc_spended_amount_array['RP'] > 0) {
                                    $total_aaccumulated_value += $sc_spended_amount_array['RP'];
                                    if ($before_refund_total_value < $total_aaccumulated_value) {
                                        if ($add_amount > $sc_spended_amount_array['RP']) {
                                            $payment_refund_amount = $sc_spended_amount_array['RP'];
                                            $add_amount -= $sc_spended_amount_array['RP'];
                                        } else {
                                            $payment_refund_amount = $add_amount;
                                            $add_amount = 0;
                                        }
                                    }
                                }

                                if ($add_amount > 0) {
                                    $total_aaccumulated_value += $sc_spended_amount_array['NRP'];
                                    if ($sc_spended_amount_array['NRP'] > 0) {
                                        if ($before_refund_total_value < $total_aaccumulated_value) {
                                            if ($add_amount > $sc_spended_amount_array['NRP']) {
                                                $payment_refund_amount = $sc_spended_amount_array['NRP'];
                                                $add_amount -= $sc_spended_amount_array['NRP'];
                                            } else {
                                                $payment_refund_amount = $add_amount;
                                                $add_amount = 0;
                                            }
                                        }
                                    }
                                }

                                if ($payment_refund_amount > 0) {
                                    // reverse the payment gateway refund amount back to USD by using order currency value
                                    //$sc_payment_refund_amount = $currencies->apply_currency_exchange($payment_refund_amount,DEFAULT_CURRENCY,(1/$trans_array['currency_value']));
                                    $sc_payment_refund_amount = round($payment_refund_amount * (1 / $trans_array['currency_value']), 6);

                                    /* -- GST :: refund with GST (if any) -- */
                                    if ($refund_gst && ($sc_payment_refund_amount > 0)) {
                                        $refund_gst_amount = ( $sc_payment_refund_amount / ($ot_subtotal - $ot_sc - $ot_coupon + $ot_surcharge) ) * $ot_gst;
                                        $sc_payment_refund_amount = $sc_payment_refund_amount + $refund_gst_amount;
                                    }
                                }
                            } else {
                                $error = true;
                            }
                        } else {
                            $error = true;
                        }
                    }

                    // Combine both SC and non SC refund amount to crete only 1 refund transaction.
                    if ($non_sc_payment_refund_amount > 0 || $sc_payment_refund_amount > 0) {
                        $payment_refund_amount = $non_sc_payment_refund_amount + $sc_payment_refund_amount;
                        /*                         * ***********************************************************************
                          1. Check this user own that order
                         * *********************************************************************** */
                        $order_checking_select_sql = "	SELECT orders_id
                                                        FROM " . TABLE_ORDERS . "
                                                        WHERE orders_id = '" . tep_db_prepare_input($trans_array['id']) . "'
                                                            AND customers_id = '" . tep_db_prepare_input($user_id) . "'";
                        $order_checking_result_sql = tep_db_query($order_checking_select_sql);

                        if ($order_checking_row = tep_db_fetch_array($order_checking_result_sql)) {
                            $user_info_array = $this->_get_user_particulars($user_id);

                            $refund_sql_data_array = array('user_id' => tep_db_prepare_input($user_id),
                                'user_firstname' => $user_info_array['fname'],
                                'user_lastname' => $user_info_array['lname'],
                                'user_email_address' => $user_info_array['email'],
                                'store_refund_date' => 'now()',
                                'store_refund_trans_id' => tep_db_prepare_input($trans_array['id']),
                                'store_refund_status' => '1',
                                'store_refund_trans_total_amount' => (double) $trans_array['order_total_amount'],
                                'store_refund_amount' => (double) $payment_refund_amount,
                                'store_refund_payments_methods_name' => tep_db_prepare_input($trans_array['payment_method'])
                            );
                            tep_db_perform(TABLE_STORE_REFUND, $refund_sql_data_array);
                            $insert_refund_id = tep_db_insert_id();

                            if ($insert_refund_id > 0) {
                                /* -- GST :: record refund GST amount -- */
                                /* -- Surcharge :: record refund surcharge amount -- */
                                if (($refund_gst && ($refund_gst_amount > 0)) || ($refund_surcharge && ($refund_surcharge_amount > 0))) {
                                    $refund_gst_amount_sql_data_array = array('store_refund_id' => $insert_refund_id,
                                        'store_refund_gst_amount' => $refund_gst_amount,
                                        'store_refund_surcharge_amount' => $refund_surcharge_amount,
                                            );
                                    tep_db_perform(TABLE_STORE_REFUND_INFO, $refund_gst_amount_sql_data_array);
                                }

                                // Insert refund history
                                // TODO: need to check whether notify option is checked
                                $refund_history_sql_data_array = array('store_refund_id' => $insert_refund_id,
                                    'store_refund_status' => '1',
                                    'date_added' => 'now()',
                                    'payee_notified' => '1',
                                    'comments' => tep_db_prepare_input($admin_msg),
                                    'changed_by' => $this->identity_email,
                                    'changed_by_role' => 'admin'
                                );
                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                /*
                                  // E-mail the beneficiary
                                  $this->send_payment_status_email($insert_payment_id);
                                 */
                                $messageStack->add_session(SUCCESS_ORDER_PAYMENT_REFUNDED, 'success');

                                $return_result[] = array('sc_type' => $sc_type . 'P', 'sc_trans_id' => $insert_refund_id, 'sc_amount' => (double) $payment_refund_amount);
                            } else {
                                // Refund failed
                                $error = true;
                                $messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
                            }
                        } else {
                            // Refund failed
                            $error = true;
                            $messageStack->add_session(ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS, 'error');
                        }
                    }
                } else {
                    $error = true;
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_SC_STAT_SC_TYPE_NOT_EXISTS, 'error');
        }

        if (!$error) {
            return $return_result;
        } else {
            return false;
        }
    }

    function _get_user_particulars($user_id) {
        $user_info_select_sql = "	SELECT customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email, customers_gender AS gender , ci.customers_info_account_created_from AS sign_up_from, c.customers_disable_withdrawal as disable_withdrawal, c.customers_id
									FROM " . TABLE_CUSTOMERS . " AS c
									LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
										ON c.customers_id=ci.customers_info_id
									WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $user_info_result_sql = tep_db_query($user_info_select_sql);
        $user_info_row = tep_db_fetch_array($user_info_result_sql);

        if ($user_info_row['sign_up_from'] == '0') {
            $user_info_row['sign_up_from'] = 'C';
        } else if ($user_info_row['sign_up_from'] == '1') {
            $user_info_row['sign_up_from'] = 'CN';
        } else if ($user_info_row['sign_up_from'] == '2') {
            $user_info_row['sign_up_from'] = 'A';
        }

        $sc_info_select_sql = "	SELECT DISTINCT sc_currency_id, sc_conversion_date
								FROM " . TABLE_COUPON_GV_CUSTOMER . "
								WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $sc_info_result_sql = tep_db_query($sc_info_select_sql);
        if ($sc_info_row = tep_db_fetch_array($sc_info_result_sql)) {
            $user_info_row['sc_currency_id'] = $sc_info_row['sc_currency_id'];
            $user_info_row['sc_conversion_date'] = $sc_info_row['sc_conversion_date'];
        } else {
            $user_info_row['sc_currency_id'] = '';
            $user_info_row['sc_conversion_date'] = '';
        }

        return $user_info_row;
    }

    function _get_supplier_particulars($user_id) {
        // Cannot direct MAP Supplier ID = Customer ID
        $user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_gender AS gender , 'CN' AS sign_up_from, supplier_disable_withdrawal as disable_withdrawal, '' as sc_currency_id, '' as sc_conversion_date
									FROM " . TABLE_SUPPLIER . "
									WHERE supplier_id = '" . tep_db_input($user_id) . "'";
        $user_info_result_sql = tep_db_query($user_info_select_sql);
        $user_info_row = tep_db_fetch_array($user_info_result_sql);

        return $user_info_row;
    }

    function _get_credit_reserve_amount($user_id) {
        $reserve_array = array();

        $credit_acc_select_sql = "	SELECT sc_reversible_reserve_amount, sc_irreversible_reserve_amount
									FROM " . TABLE_COUPON_GV_CUSTOMER . "
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);

        if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
            $reserve_array = array('sc_reverse_reserve_amt' => $credit_acc_row['sc_reversible_reserve_amount'],
                'sc_irreverse_reserve_amt' => $credit_acc_row['sc_irreversible_reserve_amount']
            );
        }

        $acc_balance_select_sql = "	SELECT store_account_reserve_amount
									FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
									WHERE user_id = '" . tep_db_input($user_id) . "'";
        $acc_balance_result_sql = tep_db_query($acc_balance_select_sql);

        if ($acc_balance_row = tep_db_fetch_array($acc_balance_result_sql)) {
            $reserve_array['store_account_reserve_amt'] = $acc_balance_row['store_account_reserve_amount'];
        }

        return $reserve_array;
    }

    function _check_credits_account_exists($user_id, $new_sc_currency_id = 0) {
        global $currencies;

        $credit_acc_select_sql = "	SELECT customer_id
									FROM " . TABLE_COUPON_GV_CUSTOMER . "
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);

        if (tep_db_num_rows($credit_acc_result_sql) > 0) { // Store credit account exists
            return true;
        } else { // Check if this is a valid customer
            $customer_select_sql = "SELECT customers_id
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($user_id) . "'";
            $customer_result_sql = tep_db_query($customer_select_sql);

            $new_sc_currency_id = ($new_sc_currency_id == 0) ? array_search(DEFAULT_CURRENCY, $currencies->internal_currencies) : $new_sc_currency_id;

            if (tep_db_num_rows($customer_result_sql) > 0) { // Valid customer
                $sc_balance_data_array = array('customer_id' => $user_id,
                    'sc_reversible_amount' => 0,
                    'sc_reversible_reserve_amount' => 0,
                    'sc_irreversible_amount' => 0,
                    'sc_irreversible_reserve_amount' => 0,
                    'sc_currency_id' => $new_sc_currency_id,
                    'sc_last_modified' => 'now()'
                );
                tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $sc_balance_data_array);

                return true;
            } else {
                return false;
            }
        }
    }

    function check_credits_account_exists($user_id, $new_sc_currency_id = 0) {
        return $this->_check_credits_account_exists($user_id, $new_sc_currency_id);
    }

    function set_refund_product($refunded_product) {
        if (is_array($refunded_product))
            $this->refund_product_info = $refunded_product;
    }

    // Sets the available & actual qty of a product
    function _set_store_credit_balance($user_id, $sc_array) {
        global $log_object, $currencies;
        /*         * *****************************************************************
          operator = + (add credit), - (deduct credit), = (assign new credit)
         * ***************************************************************** */
        $sql_update_array = array();

        $credit_acc_select_sql = "	SELECT DISTINCT sc_currency_id
									FROM " . TABLE_COUPON_GV_CUSTOMER . "
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
        $credit_acc_row = tep_db_fetch_array($credit_acc_result_sql);

        // Generate the update sql
        for ($sc_cnt = 0; $sc_cnt < count($sc_array); $sc_cnt++) {
            if (($sc_array[$sc_cnt]['field_name'] == 'sc_reversible_amount' || $sc_array[$sc_cnt]['field_name'] == 'sc_irreversible_amount')) {
                $sc_array[$sc_cnt]['operator'] = trim($sc_array[$sc_cnt]['operator']);
                if ($credit_acc_row['sc_currency_id'] != $sc_array[$sc_cnt]['currency_id']) {
                    //$sc_array[$sc_cnt]['value'] = $this->get_store_credit_conversion($sc_array[$sc_cnt]['value'], $currencies->get_code_by_id($sc_array[$sc_cnt]['currency_id']), $currencies->get_code_by_id($credit_acc_row['sc_currency_id']));
                    $sc_array[$sc_cnt]['value'] = $currencies->advance_currency_conversion($sc_array[$sc_cnt]['value'], $currencies->get_code_by_id($sc_array[$sc_cnt]['currency_id']), $currencies->get_code_by_id($credit_acc_row['sc_currency_id']), true, 'sell');
                }
                switch ($sc_array[$sc_cnt]['operator']) {
                    case '+':
                        $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' + ' . tep_db_input($sc_array[$sc_cnt]['value']);
                        break;
                    case '-':
                        $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' - ' . tep_db_input($sc_array[$sc_cnt]['value']);
                        break;
                    case '=':
                        $sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . tep_db_input($sc_array[$sc_cnt]['value']);
                        break;
                    default:
                        break;
                }
            }
        }

        if (count($sql_update_array)) {
            $sql_update_array[] = ' sc_last_modified = now() ';

            $update_sql_str = " SET " . implode(', ', $sql_update_array);

            /*             * ***********************************************************************
              Lock the TABLE_COUPON_GV_CUSTOMER
              REMEMBER: Need to lock all the tables involved in this block.
             * *********************************************************************** */
            tep_db_query("LOCK TABLES " . TABLE_COUPON_GV_CUSTOMER . " WRITE;");

            $sc_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER .
                    $update_sql_str . "
								WHERE customer_id = '" . tep_db_input($user_id) . "'";
            tep_db_query($sc_update_sql);

            tep_db_query("UNLOCK TABLES;");
            /*             * ******************************************************************
              End of locking the TABLE_COUPON_GV_CUSTOMER table.
             * ****************************************************************** */

            return $this->_get_current_credits_balance($user_id);
        }
    }

    function _get_current_credits_balance($user_id) {
        $credit_accounts = array();

        $credit_acc_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount, sc_currency_id
									FROM " . TABLE_COUPON_GV_CUSTOMER . "
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
        if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
            $credit_accounts = array('sc_reverse' => $credit_acc_row['sc_reversible_amount'],
                'sc_irreverse' => $credit_acc_row['sc_irreversible_amount'],
                'currency_id' => $credit_acc_row['sc_currency_id']
            );
        }

        return $credit_accounts;
    }

    function get_current_credits_balance($user_id) {
        $credit_accounts = array();

        $credit_acc_select_sql = "	SELECT sc_reversible_amount, sc_reversible_reserve_amount, sc_irreversible_amount, sc_irreversible_reserve_amount, sc_currency_id
									FROM " . TABLE_COUPON_GV_CUSTOMER . "
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
        $credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
        if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
            $credit_accounts = array('R' => $credit_acc_row['sc_reversible_amount'],
                'R_reserve' => $credit_acc_row['sc_reversible_reserve_amount'],
                'NR' => $credit_acc_row['sc_irreversible_amount'],
                'NR_reserve' => $credit_acc_row['sc_irreversible_reserve_amount'],
                'currency_id' => $credit_acc_row['sc_currency_id']
            );
        }

        return $credit_accounts;
    }

    function get_credit_accounts() {
        return $this->credit_accounts;
    }

    function _is_within_daily_limit($add_amount) {
        $action_allowed = false;

        $admin_credit_limit_select_sql = "	SELECT admin_credit_limit_total, admin_credit_limit_max
											FROM " . TABLE_ADMIN_CREDIT_LIMIT . "
											WHERE admin_id = " . tep_db_input($this->identity);
        $admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);

        if ($admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql)) {
            if ($admin_credit_limit_row['admin_credit_limit_total'] + $add_amount <= $admin_credit_limit_row['admin_credit_limit_max']) {
                $action_allowed = true;

                $admin_credit_limit_update_sql = "	UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . "
													SET admin_credit_limit_total = admin_credit_limit_total + " . $add_amount . "
													WHERE admin_id = " . tep_db_input($this->identity);
                tep_db_query($admin_credit_limit_update_sql);
            }
        }

        return $action_allowed;
    }

    function _get_customers_id_by_email($customers_email) {
        $user_info_select_sql = "SELECT customers_id
									FROM " . TABLE_CUSTOMERS . "
  									WHERE customers_email_address = '" . tep_db_input($customers_email) . "'";
        $user_info_result_sql = tep_db_query($user_info_select_sql);
        if ($user_info_row = tep_db_fetch_array($user_info_result_sql)) {
            return $user_info_row['customers_id'];
        }
        return 0;
    }

    function get_store_credit_conversion($sc_amount = 0, $sc_from_currency = DEFAULT_CURRENCY, $sc_to_currency = DEFAULT_CURRENCY) {
        global $currencies;
        if (trim($sc_from_currency) == trim($sc_to_currency))
            return $sc_amount;

        $return_value = $currencies->advance_currency_conversion($sc_amount, $sc_from_currency, $sc_to_currency, true, 'sell');

        return tep_round($return_value, 4);
    }

    function get_store_credits_in_new_currency($customers_id, $new_currency_code = DEFAULT_CURRENCY) {
        global $currencies;

        $new_store_credit_row = array('R' => 0,
            'R_reserve' => 0,
            'NR' => 0,
            'NR_reserve' => 0
        );

        $store_credit_row = $this->get_current_credits_balance($customers_id);

        if (count($store_credit_row) > 0) {
            $current_store_credit_currency = $store_credit_row['currency_id'];
            $from_currency_code = $currencies->get_code_by_id($current_store_credit_currency);

            // if current store credit currency same as profile currency, just return the store credit amount
            if ($from_currency_code == $new_currency_code) {
                return $store_credit_row;
            }

            $new_store_credit_row['sc_reverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse'], $from_currency_code, $new_currency_code, true, 'sell');
            $new_store_credit_row['sc_reverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse_reserve_amt'], $from_currency_code, $new_currency_code, true, 'sell');
            $new_store_credit_row['sc_irreverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse'], $from_currency_code, $new_currency_code, true, 'sell');
            $new_store_credit_row['sc_irreverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse_reserve_amt'], $from_currency_code, $new_currency_code, true, 'sell');
        }

        return $new_store_credit_row;
    }

}
?>