<?php
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_STORE_POINT)) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_STORE_POINT);
}

class store_point {

    var $identity, $identity_email;
    var $point_accounts, $currency_display_decimal;
    var $store_point_data;
    var $store_opening_data;
    var $store_closing_data;
    var $store_currency_count;
    var $store_point_data_filename;
    var $max_record_per_file = 20000;

    // class constructor
    function store_point($identity, $identity_email) {
        $this->point_accounts = array();
        $this->store_point_data = array();
        $this->store_opening_data = array();
        $this->store_closing_data = array();
        $this->store_currency_count = array();
        $this->store_point_data_filename = '';

        $this->identity = $identity; // Admin user
        $this->identity_email = $identity_email; // Admin user

        $this->point_accounts_activities_selection = array(
            array('id' => '', 'text' => 'All Activities'),
            array('id' => 'X', "text" => 'Cancel'),
            array('id' => 'C', "text" => 'Compensate'),
            array('id' => 'MI', "text" => 'Manual Addition'),
            array('id' => 'MR', "text" => 'Manual Deduction'),
            array('id' => 'P', "text" => 'Purchase'),
            array('id' => 'PD', "text" => 'Order Rollback'),
            array('id' => 'R', "text" => 'Refund'),
            array('id' => 'B', "text" => 'Bonus')
        );

        $this->currency_display_decimal = 2;
    }

    function search_sp_statement($filename, $session_name) {
        global $view_sp_flow_report_permission;

        $search_type = tep_not_null($_SESSION[$session_name]['search_type']) ? $_SESSION[$session_name]['search_type'] : (tep_not_null($_REQUEST['search_type']) ? $_REQUEST['search_type'] : (tep_not_null($_SESSION[$session_name]["transaction_id"]) ? '1' : '2'));

        $sp_statement_html = '';

        $report_type_array = array(
            array('id' => '1', "text" => REPORT_SP_STAT_TYPE_INDIVIDUAL)
        );
        if ($view_sp_flow_report_permission) {
            $report_type_array[] = array('id' => '2', "text" => REPORT_SP_STAT_TYPE_SP_FLOW);
        }
        ?>
        <table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="20%">&nbsp;</td>
                            <td>
                                <?= tep_draw_form('sp_criteria', $filename, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '') ?>
                                <?= tep_draw_hidden_field('search_type', (tep_not_null($search_type) ? $search_type : '1')) ?>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main"><?= ENTRY_SP_STAT_REPORT_TYPE ?></td>
                                        <td class="main"><?= tep_draw_pull_down_menu("report", $report_type_array, $_SESSION[$session_name]["report"], ' id="report" onChange="swap_fields(this.value)"') ?></td>
                                        <td class="main" colspan="4">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                </table>

                                <div ID="search-tab">
                                    <ul>
                                        <?php
                                        if ($search_type == '1')
                                            echo '<li class="ui-tabs-selected">';
                                        else
                                            echo '<li class="">';
                                        ?>
                                        <a href="#tab-quick" onClick="javascript:document.sp_criteria.search_type.value = '1';"><span><?= ENTRY_SP_STAT_QUICK_SEARCH ?></span></a></li>
                                        <?php
                                        if ($search_type == '2')
                                            echo '<li class="ui-tabs-selected">';
                                        else
                                            echo '<li class="">';
                                        ?>
                                        <a href="#tab-advanced" onClick="javascript:document.sp_criteria.search_type.value = '2';"><span><?= ENTRY_SP_STAT_ADVANCED_SEARCH ?></span></a></li>
                                    </ul>
                                    <div id="tab-quick" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_TRANSACTION_ID ?></td>
                                                <td class="main"><?= tep_draw_input_field('transaction_id', $_SESSION[$session_name]["transaction_id"], ' id="transaction_id" size="15" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onkeypress="return noEnterKey(event)"') ?></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main">&nbsp;</td>
                                                <td class="smallText" valign="top" align="left">
                                                    <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton') ?>
                                                    <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div id="tab-advanced" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_STAT_USER_ID ?></td>
                                                <td class="main" valign="top" colspan="5">
                                                    <?= tep_draw_input_field('customer_id', $_SESSION[$session_name]["customer_id"], ' id="customer_id" size="30" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onkeypress="return noEnterKey(event)"', true) ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_STAT_START_DATE ?></td>
                                                <td class="main" valign="top" nowrap>
                                                    <script language="javascript">
                                                        var date_start_date = new ctlSpiffyCalendarBox('date_start_date', 'sp_criteria', 'start_date', 'btnDate_start_date', '', scBTNMODE_CUSTOMBLUE);
                                                        date_start_date.writeControl();
                                                        date_start_date.dateFormat = "yyyy-MM-dd";
                                                        document.getElementById('start_date').value = '<?= $_SESSION[$session_name]["start_date"] ?>';
                                                    </script>
                                                </td>
                                                <td class="main" width="10%">&nbsp;</td>
                                                <td class="main" valign="top" nowrap><?= ENTRY_SP_STAT_END_DATE ?></td>
                                                <td class="main">&nbsp;</td>
                                                <td class="main" valign="top">
                                                    <script language="javascript">
                                                        var date_end_date = new ctlSpiffyCalendarBox('date_end_date', 'sp_criteria', 'end_date', 'btnDate_end_date', '', scBTNMODE_CUSTOMBLUE);
                                                        date_end_date.writeControl();
                                                        date_end_date.dateFormat = "yyyy-MM-dd";
                                                        document.getElementById('end_date').value = '<?= $_SESSION[$session_name]["end_date"] ?>';
                                                    </script>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_ACTIVITY ?></td>
                                                <td class="main"><?= tep_draw_pull_down_menu('sp_activity', $this->point_accounts_activities_selection, tep_not_null($_SESSION[$session_name]['sp_activity']) ? $_SESSION[$session_name]['sp_activity'] : '', 'id="sp_activity"') ?></td>
                                                <td class="main" colspan="4">&nbsp;</td>
                                            </tr>
                                            <tr>
                                                <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main">&nbsp;</td>
                                                <td class="main">&nbsp;</td>
                                                <td class="main">&nbsp;</td>
                                                <td colspan="3">
                                                    <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton') ?>
                                                    <?= tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton') ?>
                                                    <?= tep_submit_button('Export All as CSV', 'Export as csv file', 'id="doexport" onClick="return form_checking(this.form, \'do_export\');"', 'inputButton'); ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                </form>
                            </td>
                            <td width="20%">&nbsp;</td>
                        </tr>
                        <tr>
                            <td colspan="6"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <script language="javascript">
            function form_checking(form_obj, action) {
                var error_message = '<?= JS_ERROR ?>';
                var form_error = false;
                var sel_report = DOMCall('report').value;
                var search_type = form_obj.search_type.value;
                if (search_type == '1') { // Quick Search
                    // transaction ID is mandatory field
                    if (trim_str(DOMCall('transaction_id').value) == '') {
                        error_message += '* <?= JS_ERROR_SP_STAT_EMPTY_TRANSACTION_ID ?>' + "\n";
                        form_error = true;
                    }
                } else if (search_type == '2') { // Advanced Search
                    if (sel_report == '1') { // Report Type by Customer
                        // customer email is mandatory field
                        if (trim_str(DOMCall('customer_id').value) == '') {
                            error_message += '* <?= JS_ERROR_SP_STAT_EMPTY_ID ?>' + "\n";
                            form_error = true;
                        }

                        var start_date = DOMCall('start_date').value;
                        var end_date = DOMCall('end_date').value;

                        if (start_date.length > 0 || end_date.length > 0) {
                            if (validateDate(start_date) && validateDate(end_date)) {
                                if (!validStartAndEndDate(start_date, end_date)) {
                                    error_message += '* Start Date is greater then End Date' + "\n";
                                    form_error = true;
                                }
                            } else {
                                if (!validateDate(start_date) && start_date.length > 0 ) {
                                    error_message += '* Start Date is not a valid date' + "\n";
                                    form_error = true;
                                }
                                if (!validateDate(end_date) && end_date.length > 0) {
                                    error_message += '* End Date is not a valid date' + "\n";
                                    form_error = true;
                                }
                            }
                        }
                    } else if (sel_report == '2') { // Report Type by Movement
                        // start date, end date and credit type are mandatory fields
                        var start_date = DOMCall('start_date').value;
                        if (start_date.length > 0) {
                            if (!validateDate(start_date)) {
                                error_message += '* Start date is not a valid date format as requested!' + "\n";
                                form_error = true;
                            }
                        } else {
                            error_message += '* Please enter start date!' + "\n";
                            form_error = true;
                        }

                        var end_date = DOMCall('end_date').value;
                        if (end_date.length > 0) {
                            if (!validateDate(end_date)) {
                                error_message += '* End date is not a valid date format as requested!' + "\n";
                                form_error = true;
                            }
                        } else {
                            error_message += '* Please enter end date!' + "\n";
                            form_error = true;
                        }

                        if (start_date.length > 0 && end_date.length > 0) {
                            if (!validStartAndEndDate(start_date, end_date)) {
                                error_message += '* Start Date is greater than End Date!' + "\n";
                                form_error = true;
                            }
                            if (!validStartAndEndDateRange(start_date, end_date, 100)) {
                                error_message += '* Start Date to End Date must be within 100 days!' + "\n";
                                form_error = true;
                            }
                        }
                    }
                }

                if (form_error) {
                    alert(error_message);
                } else {
                    if (action == 'do_search') {
                        form_obj.action = "<?= tep_href_link($filename, 'action=show_report'); ?>";
                    } else if (action == 'do_export') {
                        form_obj.action = "<?= tep_href_link($filename, 'action=export_report'); ?>";
                    }
                }

                return !form_error;
            }

            function validStartAndEndDateRange(start_date, end_date, range) {
                var MinMilli = 1000 * 60;
                var HrMilli = MinMilli * 60;
                var DyMilli = HrMilli * 24;
                var RangeMilli = DyMilli * range;
                if (start_date.indexOf(':') > 1) {
                    var dateTimeArray = start_date.split(' ');
                    var startDate = dateTimeArray[0].split('-');
                    var startTime = dateTimeArray[1].split(':');
                    var startDateObj = new Date(startDate[0], startDate[1] - 1, startDate[2], startTime[0], startTime[1], '00');
                } else {
                    var startDate = start_date.split('-');
                    var startDateObj = new Date(startDate[0], startDate[1] - 1, startDate[2]);
                }

                if (end_date.indexOf(':') > 1) {
                    var dateTimeArray = end_date.split(' ');
                    var endDate = dateTimeArray[0].split('-');
                    var endTime = dateTimeArray[1].split(':');
                    var endDateObj = new Date(endDate[0], endDate[1] - 1, endDate[2], endTime[0], endTime[1], '00');
                } else {
                    var endDate = end_date.split('-');
                    var endDateObj = new Date(endDate[0], endDate[1] - 1, endDate[2]);
                }

                if (endDateObj.getTime() - startDateObj.getTime() > RangeMilli) {
                    return false;
                } else {
                    return true;
                }
            }

            function resetControls(controlObj) {
                if (trim_str(controlObj.value) != '') {
                } else {
                    controlObj.value = '';
                }
            }

            function swap_fields(selected) {
                if (selected == '2') {
                    DOMCall('customer_id').value = "";
                    DOMCall('customer_id').disabled = true;
                    DOMCall('doexport').disabled = false;
                } else {
                    DOMCall('customer_id').disabled = false;
                    DOMCall('doexport').disabled = true;
                }
            }

            function init() {
                swap_fields(document.getElementById('report').value);
            }
            init();
        </script>
        <?
    }

    function _get_store_points_daily_history($store_date = '', $user_id = 'system', $user_role = 'system') {
        if (!isset($store_date))
            return false;

        $history_array = array();
        if ($store_date) {
            $sp_daily_history_select_sql = "SELECT store_credit_daily_history_date, store_credit_daily_history_amount, 0 as untally
                                            FROM " . TABLE_STORE_CREDIT_DAILY_HISTORY . "
                                            WHERE store_credit_daily_history_date >= '" . tep_db_input($store_date) . "'
                                                AND store_credit_daily_history_date < DATE_ADD('" . tep_db_input($store_date) . "', INTERVAL 24 HOUR)
                                                AND user_id = '" . tep_db_input($user_id) . "'
                                                AND user_role = '" . tep_db_input($user_role) . "'
                                                AND store_credit_daily_history_credit_type = 'OP'";
            $sp_daily_history_result_sql = tep_db_query($sp_daily_history_select_sql);
            while ($sp_daily_history_row = tep_db_fetch_array($sp_daily_history_result_sql)) {
                $history_array[$sp_daily_history_row['store_credit_daily_history_date']] = $sp_daily_history_row;
            }

            return $history_array;
        }
    }

    function _display_store_points_daily_history($data, $balance_type) {
        if (isset($data) && is_array($data)) {
            $daily_history_row = '';

            foreach ($data as $date_list => $history_data) {
                $balance_str = '';
                $span_str = '<span';

                if ($balance_type == 'C' && $history_data['untally'] === 1) {
                    $span_str .= ' class="redIndicator"';
                }

                $span_str .= '>';
                $balance_str .= $span_str . number_format($history_data['store_credit_daily_history_amount'], 0, '.', '') . '</span><br>';
                $date = $history_data['store_credit_daily_history_date'];

                if ($balance_type == 'C') {
                    $balance_title = 'Closing Balance';
                    $report_type = 'C';
                } else if ($balance_type == 'O') {
                    $balance_title = 'Opening Balance';
                    $report_type = 'O';
                } else {
                    $balance_title = 'Balance';
                    $report_type = 'O';
                }

                $daily_history_row .= '<tr height="20" class="reportListingSummary" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingSummary\')" onclick="rowClicked(this, \'reportListingSummary\')">
                                            <td valign="top" class="reportRecords" nowrap></td>
                                            <td valign="middle" class="reportRecords" colspan="6" nowrap><span class="boldText">' . $balance_title . ' as at ' . $date . '</span></td>
                                            <td valign="top" class="reportRecords" nowrap colspan="2"></td>
                                            <td align="right" valign="top" class="reportRecords" nowrap>' . $balance_str . '</td>
                                        </tr>';
            }
        } else {
            $daily_history_row = '';
        }

        return $daily_history_row;
    }

    function search($input) {
        $data = array(
            "user" => array(),
            "row" => array(),
            "balance" => array()
        );

        if (tep_not_null($input['search_type'])) {
            $opening = $closing = 0.00;
            $sql = "";

            switch ($input['search_type']) {
                // Quick Search
                case 1:
                    if (tep_not_null($input["transaction_id"])) {
                        $sql = "SELECT * FROM " . TABLE_STORE_POINTS_HISTORY . " WHERE store_points_history_id = " . $input["transaction_id"];
                        $_sql = "SELECT COUNT(*) AS cnt FROM " . TABLE_STORE_POINTS_HISTORY . " WHERE store_points_history_id = " . $input["transaction_id"];
                    }
                    break;

                // Advance Search
                case 2:
                    $cond = array();
                    if (tep_not_null($input["customer_id"])) {
                        $cond[] = "customer_id = " . $input["customer_id"];
                    }
                    if (tep_not_null($input["start_date"])) {
                        $cond[] = "store_points_history_date >= '" . $input["start_date"] . " 00:00:00'";
                    }
                    if (tep_not_null($input["end_date"])) {
                        $cond[] = "store_points_history_date < '" . date("Y-m-d", strtotime($input["end_date"] . " +1 day")) . " 00:00:00'";
                    }
                    if (tep_not_null($input["sp_activity"])) {
                        $cond[] = "store_points_history_activity_type = '" . $input["sp_activity"] . "'";
                    }

                    switch ($input['report']) {
                        // Customer
                        case 1:
                            $sql = "SELECT * FROM " . TABLE_STORE_POINTS_HISTORY . "
                                    WHERE " . implode(" AND ", $cond) . " 
                                    ORDER BY store_points_history_date ASC, store_points_history_id ASC";
                            $_sql = "SELECT COUNT(*) AS cnt FROM " . TABLE_STORE_POINTS_HISTORY . "
                                    WHERE " . implode(" AND ", $cond);
                            break;

                        // Store Credit Movement
                        case 2:
                            // check record exist
                            $this->store_point_data_filename = "store_point_" . $this->identity . '_' . date("YmdHis", strtotime($input["start_date"])) . "_" . date("YmdHis", strtotime($input["end_date"])) . "_" . (tep_not_null($input["sp_activity"]) ? $input["sp_activity"] : "ALL") . "_";
                            $files = preg_grep('~^' . $this->store_point_data_filename . '.*\.cache~', scandir("download/"));
                            if (!tep_not_empty($files)) {
                                $sql = "SELECT * FROM " . TABLE_STORE_POINTS_HISTORY . "
                                        WHERE " . implode(" AND ", $cond) . " 
                                        ORDER BY store_points_history_date ASC, store_points_history_id ASC";
                                $_sql = "SELECT COUNT(*) AS cnt FROM " . TABLE_STORE_POINTS_HISTORY . "
                                        WHERE " . implode(" AND ", $cond);
                                $start_date = $input["start_date"] . " 00:00:00";
                                $close_date = date("Y-m-d", strtotime($input["end_date"] . " +1 day")) . " 00:00:00";

                                $data["balance"]["opening"] = $this->_get_store_points_daily_history($start_date);
                                if (!tep_not_null($input["sp_activity"])) {
                                    $data["balance"]["closing"] = $this->_get_store_points_daily_history($close_date);
                                }

                                if (tep_not_empty($data["balance"]["opening"])) {
                                    foreach ($data["balance"]["opening"] as $_date => $_val) {
                                        $opening = (double) $_val['store_credit_daily_history_amount'];
                                    }
                                }
                            }
                            break;
                    }
                    break;
            }

            if (tep_not_null($sql)) {
                $_res = tep_db_query($_sql);
                $_row = tep_db_fetch_array($_res);
                if (isset($_row["cnt"]) && ($_row["cnt"] > 0)) {
                    $_cnt = ($this->max_record_per_file > $_row["cnt"] ? 1 : ceil($_row["cnt"] / $this->max_record_per_file));

                    for ($i = 0; $_cnt > $i; $i++) {
                        $q = $sql . " LIMIT " . ($i > 0 ? ($i * $this->max_record_per_file) : 0) . ", " . $this->max_record_per_file;
                        $res = tep_db_query($q);
                        while ($row = tep_db_fetch_array($res)) {
                            if ($_REQUEST["report"] == 1 && (!tep_not_empty($data["user"]))) {
                                $data["user"] = $this->_get_user_particulars($row["customer_id"]);
                            } else if ($_REQUEST["report"] == 2) {
                                $opening = $opening - (double) $row['store_points_history_debit_amount'];
                                $opening = $opening + (double) $row['store_points_history_credit_amount'];
                            }

                            $activity_title = $payment_gateway = $activity_type = '';

                            if (tep_not_null($row["store_points_history_activity_title"])) {
                                $activity_title = $row["store_points_history_activity_title"];
                            } else {
                                switch ($row["store_points_history_trans_type"]) {
                                    case 'C':
                                        $activity_title = $row["store_points_history_trans_id"];
                                        $_sql = "SELECT pm.payment_methods_title
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                                                        ON o.payment_methods_parent_id=pm.payment_methods_id
                                                    WHERE o.orders_id='" . tep_db_input($row["store_points_history_trans_id"]) . "'";
                                        $_res = tep_db_query($_sql);
                                        if ($_row = tep_db_fetch_array($_res)) {
                                            $payment_gateway = $_row['payment_methods_title'];
                                        } else {
                                            $payment_gateway = 'OffGamers Store Credit';
                                        }
                                        break;

                                    default:
                                        $activity_title = $row["store_points_history_trans_type"] . ' ' . $row["store_points_history_trans_id"];
                                        break;
                                }

                                switch ($row["store_points_history_activity_type"]) {
                                    case 'X':
                                        $activity_type = TEXT_SP_STAT_ACTIVITY_CANCEL;
                                        break;
                                    case 'D':
                                        $activity_type = TEXT_SP_STAT_ACTIVITY_REDEEM;
                                        break;
                                    case 'P':
                                        $activity_title = sprintf(TITLE_TRANS_CUSTOMER_ORDER, $row["store_points_history_trans_id"]);
                                        break;
                                    case 'B':
                                        $activity_title = sprintf(TITLE_TRANS_CUSTOMER_ORDER, $row["store_points_history_trans_id"]);
                                        $activity_type = TEXT_SP_STAT_ACTIVITY_BONUS;
                                        break;
                                }

                                if (tep_not_null($activity_type))
                                    $activity_title .= ' - ' . $activity_type;
                            }

                            $data["row"][$row["store_points_history_id"]] = array(
                                "history_id" => $row["store_points_history_id"],
                                "activity_date" => $row["store_points_history_date"],
                                "trans_type" => $row["store_points_history_trans_type"],
                                "trans_id" => $row["store_points_history_trans_id"],
                                "activity_type" => $row["store_points_history_activity_type"],
                                "activity_type_text" => $activity_type,
                                "activity_title" => $row["store_points_history_activity_title"],
                                "activity_title_text" => $activity_title,
                                "activity_desc" => $row["store_points_history_activity_desc"],
                                "activity_desc_show" => $row["store_points_history_activity_desc_show"],
                                "debit_amount" => (double) $row["store_points_history_debit_amount"],
                                "credit_amount" => (double) $row["store_points_history_credit_amount"],
                                "after_balance" => (double) $row["store_points_history_after_balance"],
                                "added_by" => $row["store_points_history_added_by"],
                                "added_by_role" => $row["store_points_history_added_by_role"],
                                "admin_message" => $row["store_points_history_admin_messages"],
                                "customer_id" => $row["customer_id"],
                                "payment_gateway" => $payment_gateway,
                                "running_balance" => array(
                                    "amt" => $opening,
                                    "cnt" => $row["store_points_history_id"],
                                    "untally" => 0
                                )
                            );
                        }
                        krsort($data["row"]);

                        // Advance Search + Store Credit Movement
                        if ($input['search_type'] == 2 && $input['report'] == 2) {
                            $last = false;
                            $fp = fopen("download/" . $this->store_point_data_filename . $i . ".cache", "a+");

                            if (($i + 1) == $_cnt) {
                                $last = true;
                            }

                            foreach ($data["row"] as $val) {
                                if ($last && isset($data["balance"]["closing"])) {
                                    foreach ($data["balance"]["closing"] as $_idx => $_val) {
                                        if ((double) $_val['store_credit_daily_history_amount'] <> $val["running_balance"]["amt"]) {
                                            $val["running_balance"]["untally"] = 1;
                                            $data["balance"]["closing"][$_idx]["untally"] = 1;
                                        }
                                    }
                                    fwrite($fp, json_encode(array("closing" => $data["balance"]["closing"])) . "\n");
                                    $last = false;
                                }
                                fwrite($fp, json_encode($val) . "\n");
                            }

                            if ($i == 0) {
                                fwrite($fp, json_encode(array("opening" => $data["balance"]["opening"])) . "\n");
                            }
                            fclose($fp);
                            $data["row"] = array();
                            gc_collect_cycles();
                        }
                    }
                }
            }
        }

        // Advance Search + Store Credit Movement
        if ($input['search_type'] == 2 && $input['report'] == 2) {
            switch ($input["action"]) {
                case "export_report":
                    $this->_export_movement($input);
                    break;

                default:
                    $this->_load_output_movement(false, $input);
            }
        } else {
            if ($_REQUEST["report"] == 1) {
                // By Customer
                $this->_load_output_customer($data, $input);
                if (tep_not_null($input["customer_id"])) {
                    $this->_manual_adjust_form(FILENAME_STORE_POINT, $input["customer_id"]);
                }
            } else if ($_REQUEST["report"] == 2) {
                // Quick Search + Store Credit Movement
                if ($input['search_type'] == 1) {
                    // Store Credit Movement
                    $this->_load_output_movement($data, $input);
                }
            }
        }
    }

    function _load_output_customer($data, $input) {
        ?>
        <table border="0" cellspacing="2" cellpadding="2" width="100%">
            <tr>
                <td>
                    <table border="0" width="100%" align="center" cellpadding="0" cellspacing="0">
                        <td valign="bottom">
                            <? $user_name_link = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $data["user"]["customers_id"] . '&action=edit') . '" target="_blank">' . $data["user"]['fname'] . ' ' . $data["user"]['lname'] . '</a>'; ?>
                            <?= tep_not_null($data["user"]["customers_id"]) ? sprintf(TABLE_SECTION_HEADING_SP_STAT, $user_name_link, $data["user"]["customers_id"]) : '' ?>
                        </td>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr>
                            <td width="12%" class="reportBoxHeading" rowspan="2"><?= TABLE_HEADING_SP_STAT_DATETIME ?></td>
                            <td width="35" class="reportBoxHeading" rowspan="2"><?= TABLE_HEADING_SP_STAT_ACTIVITY ?></td>
                            <td width="15%" class="reportBoxHeading" align="center" colspan="3"><?= TABLE_HEADING_SP_STAT_OP ?></td>
                        </tr>
                        <tr>
                            <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SP_STAT_MINUS ?></td>
                            <td width="5%" class="reportBoxHeading" align="center"><?= TABLE_HEADING_SP_STAT_PLUS ?></td>
                            <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SP_STAT_BALANCE ?></td>
                        </tr>
                        <?
                        if ($data["row"]) {
                            $row_count = 0;
                            foreach ($data["row"] as $id => $val) {
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                $sp_history_id_str = $activity_title = $activity_type = '';

                                if (tep_not_null($val['activity_title'])) {
                                    if (strtolower(trim($val['activity_title'])) == 'manual deduction' || strtolower(trim($val['activity_title'])) == 'manual addition') {
                                        $sp_history_id_str = sprintf(TEXT_SP_STAT_TRANS_ID, $val['history_id']);
                                    }
                                    $activity_title = $val['activity_title'] . (($val['added_by_role'] == 'admin') ? ' (by ' . $val['added_by'] . ')' : '') . ' <b>' . (($val['activity_desc_show']) ? '<span title="' . TEXT_SP_STAT_MANUAL_ACTIVITY_SHOW . '" style="cursor: default; color: green;">&#10004;</span>' : '<span title="' . TEXT_SP_STAT_MANUAL_ACTIVITY_HIDE . '" style="cursor: default; color: red;">&#10006;</span>') . '</b>' . (tep_not_null($sp_history_id_str) ? '<br>' . $sp_history_id_str : '') . (tep_not_null($val['activity_desc']) ? '<br><div class="paymentRemarkSelectedRow">Comment:<br>' . nl2br($val['activity_desc']) . '</div>' : '');
                                } else {
                                    switch ($val['trans_type']) {
                                        case 'C':
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_CUSTOMER_ORDER, $val['trans_id']) . '</a>';
                                            $activity_type = TEXT_SP_STAT_ACTIVITY_PURCHASE;
                                            break;
                                        default:
                                            $activity_title = $val['trans_type'] . ' ' . $val['trans_id'];
                                            break;
                                    }

                                    switch ($val['activity_type']) {
                                        case 'D':
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_REDEEM, 'redeemID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_REDEEM_RECORD, $val['trans_id']) . '</a>';
                                            $activity_type = TEXT_SP_STAT_ACTIVITY_REDEEM;
                                            break;
                                        case 'X':
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_REDEEM, 'redeemID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_REDEEM_RECORD, $val['trans_id']) . '</a>';
                                            $activity_type = TEXT_SP_STAT_ACTIVITY_CANCEL;
                                            break;
                                        case 'PD':
                                            $activity_title = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . sprintf(TITLE_TRANS_CUSTOMER_ORDER, $val['trans_id']) . '</a>';
                                            $activity_type = TEXT_SP_STAT_ACTIVITY_ORDER_ROLLBACK;
                                            break;
                                        case 'B':
                                            $activity_type = TEXT_SP_STAT_ACTIVITY_BONUS;
                                            break;
                                    }

                                    if (tep_not_null($activity_type))
                                        $activity_title .= ' - ' . $activity_type;

                                    $activity_title = $activity_title . (($val['added_by_role'] == 'admin') ? ' (by ' . $val['added_by'] . ')' : '') . '&nbsp;<b>' . (($val['activity_desc_show']) ? '<span title="' . TEXT_SP_STAT_MANUAL_ACTIVITY_SHOW . '" style="cursor: default; color: green;">&#10004;</span>' : '<span title="' . TEXT_SP_STAT_MANUAL_ACTIVITY_HIDE . '" style="cursor: default; color: red;">&#10006;</span>') . '</b>';
                                    if (tep_not_null($val['activity_desc']))
                                        $activity_title .= '<br><div class="paymentRemarkSelectedRow">Comment:<br>' . nl2br($val['activity_desc']) . '</div>';
                                }
                                ?>

                                <tr height="20" class="<?= $row_style; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style; ?>')" onclick="rowClicked(this, '<?= $row_style; ?>')">
                                    <td valign="top" class="reportRecords" nowrap><?= $val['activity_date']; ?></td>
                                    <td valign="top" class="reportRecords"><?= $activity_title; ?></td>
                                    <td align="right" valign="top" class="reportRecords" nowrap>
                                        <?php
                                        if (tep_not_null($val['debit_amount'])) {
                                            if ($val['debit_amount'] >= 0) {
                                                echo (int) $val['debit_amount'];
                                            } else {
                                                echo '<span class="redIndicator">' . (int) $val['debit_amount'] . '</span>';
                                            }
                                        } else {
                                            echo TEXT_SP_STAT_NOT_APPLICABLE;
                                        }
                                        ?>
                                    </td>
                                    <td align="right" valign="top" class="reportRecords" nowrap>
                                        <?php
                                        if (tep_not_null($val['credit_amount'])) {
                                            if ($val['credit_amount'] >= 0) {
                                                echo (int) $val['credit_amount'];
                                            } else {
                                                echo '<span class="redIndicator">' . (int) $val['credit_amount'] . '</span>';
                                            }
                                        } else {
                                            echo TEXT_SP_STAT_NOT_APPLICABLE;
                                        }
                                        ?>
                                    </td>
                                    <td align="right" valign="top" class="reportRecords" nowrap>
                                        <?php
                                        if (tep_not_null($val['after_balance'])) {
                                            if ($val['after_balance'] >= 0) {
                                                echo number_format($val['after_balance'], 0, '.', '');
                                            } else {
                                                echo '<span class="redIndicator">' . number_format($val['after_balance'], 0, '.', '') . '</span>';
                                            }
                                        } else {
                                            echo TEXT_SP_STAT_NOT_APPLICABLE;
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php
                                $row_count++;
                            }
                        }
                        ?>
                    </table>
                </td>
            </tr>
        </table>
        <?php
    }

    function _load_output_movement($data, $input) {
        ?>
        <table width="100%" border="0" cellspacing="2" cellpadding="2">
            <tr>
                <td>
                    <table width="100%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
                        <tr>
                            <td width="6%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_TRANS_ID ?></td>
                            <td width="10%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_DATETIME ?></td>
                            <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_ACTIVITY ?></td>
                            <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_PAYMENT_GATEWAY ?></td>
                            <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_ADDED_BY ?></td>
                            <td width="13%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_CUST_ID ?></td>
                            <td width="17%" class="reportBoxHeading"><?= TABLE_HEADING_SP_STAT_COMMENT ?></td>
                            <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SP_STAT_DEBIT ?></td>
                            <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SP_STAT_CREDIT ?></td>
                            <td width="5%" class="reportBoxHeading" align="right"><?= TABLE_HEADING_SP_STAT_BALANCE ?></td>
                        </tr>
                        <?php
                        $cnt = 0;
                        if ($input['search_type'] == 2 && $input['report'] == 2) {
                            // check record exist
                            $files = preg_grep('~^' . $this->store_point_data_filename . '.*\.cache$~', scandir("download/"));
                            if (tep_not_empty($files)) {
                                $cnt = count($files) - 1;
                            }
                        }

                        do {
                            if ($input['search_type'] == 2 && $input['report'] == 2) {
                                if (file_exists("download/" . $this->store_point_data_filename . $cnt . ".cache")) {
                                    $fp = fopen("download/" . $this->store_point_data_filename . $cnt . ".cache", "r");
                                    while (!feof($fp)) {
                                        $line = fgets($fp);
                                        $_data = (json_decode($line, true));
                                        if (!empty($_data)) {
                                            if (isset($_data["closing"])) {
                                                $data["balance"]["closing"] = $_data["closing"];
                                            } else if (isset($_data["opening"])) {
                                                $data["balance"]["opening"] = $_data["opening"];
                                            } else {
                                                $data["row"][$_data["history_id"]] = $_data;
                                            }
                                        }
                                    }
                                    fclose($fp);
                                    unset($fp, $_data);
                                    gc_collect_cycles();
                                }
                            }

                            if ($data["row"] || $data["balance"]) {
                                if ($input["search_type"] == 2 && isset($data["balance"]["closing"])) {
                                    echo $this->_display_store_points_daily_history($data["balance"]["closing"], 'C');
                                }

                                $row_count = 0;
                                foreach ($data["row"] as $id => $val) {
                                    $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                    switch ($val['activity_type']) {
                                        case 'P':
                                            $val['activity_title_text'] = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . $val['activity_title_text'] . '</a>';
                                            break;
                                        case 'B':
                                            $val['activity_title_text'] = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $val['trans_id'] . '&action=edit', 'NONSSL') . '" target="payment">' . $val['activity_title_text'] . '</a> - ' . $val['activity_type_text'];
                                            break;
                                    }

                                    list($rec_date, $rec_time) = explode(' ', $val['activity_date']);
                                    list($rec_year, $rec_month, $rec_day) = explode('-', $rec_date);
                                    ?>
                                    <tr height="20" class="<?= $row_style; ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style; ?>')" onclick="rowClicked(this, '<?= $row_style; ?>')">
                                        <td valign="top" class="reportRecords" nowrap><?= $val['history_id']; ?></td>
                                        <td valign="top" class="reportRecords" nowrap><?= $val['activity_date']; ?></td>
                                        <td valign="top" class="reportRecords" nowrap><?= $val['activity_title_text']; ?></td>
                                        <td valign="top" class="reportRecords" nowrap><?= $val['payment_gateway']; ?></td>
                                        <td align="left" valign="top" class="reportRecords" nowrap><?= ($val['added_by_role'] == 'admin' ? $val['added_by'] : $val['customer_id']); ?></td>
                                        <td align="left" valign="top" class="reportRecords" nowrap><a href="<?= tep_href_link(FILENAME_STORE_POINT, 'action=show_report&report=1&search_type=2&customer_id=' . $val['customer_id'] . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, $rec_month, $rec_day, $rec_year))), 'SSL'); ?>" target="_blank"><?= $val['customer_id']; ?></a></td>
                                        <td align="left" valign="top" class="reportRecords"><?= nl2br($val['activity_desc']); ?></td>
                                        <td align="right" valign="top" class="reportRecords" nowrap><?= (tep_not_null($val['debit_amount']) ? ((int) $val['debit_amount'] >= 0 ? (int) $val['debit_amount'] : '<span class="redIndicator">' . (int) $val['debit_amount'] . '</span>') : TEXT_SP_STAT_NOT_APPLICABLE); ?></td>
                                        <td align="right" valign="top" class="reportRecords" nowrap><?= (tep_not_null($val['credit_amount']) ? ((int) $val['credit_amount'] >= 0 ? (int) $val['credit_amount'] : '<span class="redIndicator">' . (int) $val['credit_amount'] . '</span>') : TEXT_SP_STAT_NOT_APPLICABLE); ?></td>
                                        <td align="right" valign="top" class="reportRecords" nowrap><span <?= ($val['running_balance']['untally'] === 1 ? ' class="redIndicator"' : ''); ?>><?= number_format($val['running_balance']['amt'], 0, '.', ''); ?></span></td>
                                    </tr>
                                    <?php
                                    $row_count++;
                                }

                                if ($input["search_type"] == 2 && isset($data["balance"]["opening"])) {
                                    echo $this->_display_store_points_daily_history($data["balance"]["opening"], 'O');
                                }
                            }

                            if ($input['search_type'] == 2 && $input['report'] == 2) {
                                $data = array();
                                gc_collect_cycles();
                            }

                            --$cnt;
                        } while ($cnt >= 0);
                        ?>
                    </table>
                </td>
            </tr>
        </table>
        <?php
    }

    function _export_movement($input) {
        $filename = "download/store_point_" . $this->identity . "_" . date("YmdHis") . ".csv";
        $fn = fopen($filename, "a+");

        // title
        fwrite($fn, '"Report Type :","Store Point Movement"' . "\n");
        fwrite($fn, '"Start Date :","' . $input["start_date"] . '"' . "\n");
        fwrite($fn, '"End Date :","' . $input["end_date"] . '"' . "\n");
        fwrite($fn, '"Show :","Store Point Movement"' . "\n");
        if ($input["sp_activity"]) {
            foreach ($this->point_accounts_activities_selection as $key => $item) {
                if ($item['id'] == $input["sp_activity"]) {
                    fwrite($fn, '"Show :","' . $item['text'] . '"' . "\n");
                }
            }
        } else {
            fwrite($fn, '"Show :","All Activities"' . "\n");
        }
        fwrite($fn, "\n");

        // header
        fwrite($fn, '"' . TABLE_HEADING_SP_STAT_TRANS_ID . '",' .
                '"' . TABLE_HEADING_SP_STAT_DATETIME . '",' .
                '"' . TABLE_HEADING_SP_STAT_ACTIVITY . '",' .
                '"' . TABLE_HEADING_SP_STAT_PAYMENT_GATEWAY . '",' .
                '"' . TABLE_HEADING_SP_STAT_ADDED_BY . '",' .
                '"' . TABLE_HEADING_SP_STAT_CUST_ID . '",' .
                '"' . TABLE_HEADING_SP_STAT_COMMENT . '",' .
                '"' . TABLE_HEADING_SP_STAT_DEBIT . '",' .
                '"' . TABLE_HEADING_SP_STAT_CREDIT . '",' .
                '"' . TABLE_HEADING_SP_STAT_BALANCE . '"' . "\n");


        // read result
        $cnt = 0;

        // check record exist
        $files = preg_grep('~^' . $this->store_point_data_filename . '.*\.cache$~', scandir("download/"));
        if (tep_not_empty($files)) {
            $cnt = count($files) - 1;
        }

        do {
            if (file_exists("download/" . $this->store_point_data_filename . $cnt . ".cache")) {
                $fp = fopen("download/" . $this->store_point_data_filename . $cnt . ".cache", "r");
                while (!feof($fp)) {
                    $line = fgets($fp);
                    $_data = (json_decode($line, true));
                    if (!empty($_data)) {
                        if (isset($_data["closing"])) {
                            foreach ($_data["closing"] as $_idx => $_val) {
                                fwrite($fn, '"","Closing Balance as at ' . $_val["store_credit_daily_history_date"] . '","","","","","","","","' . $_val["store_credit_daily_history_amount"] . '"' . "\n");
                            }
                            fwrite($fn, "\n");
                        } else if (isset($_data["opening"])) {
                            fwrite($fn, "\n");
                            foreach ($_data["opening"] as $_idx => $_val) {
                                fwrite($fn, '"","Opening Balance as at ' . $_val["store_credit_daily_history_date"] . '","","","","","","","","' . $_val["store_credit_daily_history_amount"] . '"' . "\n");
                            }
                        } else {
                            fwrite($fn, '"' . $_data["history_id"] . '",' .
                                    '"' . $_data['activity_date'] . '",' .
                                    '"' . $_data['activity_title_text'] . ($_data['activity_type_text'] ? ' - ' . $_data['activity_type_text'] : '') . '",' .
                                    '"' . $_data['payment_gateway'] . '",' .
                                    '"' . ($_data['added_by_role'] == 'admin' ? $_data['added_by'] : $_data['customer_id']) . '",' .
                                    '"' . $_data['customer_id'] . '",' .
                                    '"' . nl2br($_data['activity_desc']) . '",' .
                                    '"' . $_data['debit_amount'] . '",' .
                                    '"' . $_data['credit_amount'] . '",' .
                                    '"' . $_data['running_balance']['amt'] . '"' .
                                    "\n");
                        }
                    }
                }
                fclose($fp);
                unset($fp, $_data);
                gc_collect_cycles();
            }

            --$cnt;
        } while ($cnt >= 0);

        // end
        fclose($fn);

        // Download
        header('Pragma: no-cache');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Cache-Control: private', false);
        header('Content-Transfer-Encoding: binary');
        header('Content-Type: text/x-csv');
        header('Content-Encoding: utf-8');
        header('Content-Language: zh, en');
        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
        header('Content-Length: ' . filesize($filename));
        @readfile($filename);
        exit();
    }

    function _manual_adjust_form($filename, $user_id) {
        // Permission for making the manual adjustment
        $manual_deduct_permission = tep_admin_files_actions(FILENAME_STORE_POINT, 'STORE_POINT_MANUAL_DEDUCT');
        $manual_add_permission = tep_admin_files_actions(FILENAME_STORE_POINT, 'STORE_POINT_MANUAL_ADD');

        if ($manual_deduct_permission || $manual_add_permission) {
            ?>
            <br>
            <table width="100%" border="0" align="center" cellpadding="2" cellspacing="1" valign="middle">
                <tr>
                    <td width="47%" valign="top" class="<?= $manual_deduct_permission ? 'formArea' : '' ?>">
                        <? if ($manual_deduct_permission) { ?>
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="formAreaTitle"><?= TABLE_HEADING_SP_STAT_MANUAL_DEDUCT ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?= tep_draw_form('manual_deduct_form', $filename, tep_get_all_get_params(array('subaction')) . 'subaction=manual_deduct', 'post', '') ?>
                                        <?= tep_draw_hidden_field("user_id", $user_id) ?>
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_STAT_DEDUCT_AMOUNT ?></td>
                                                <td class="main">-&nbsp;<?= tep_draw_input_field('deduct_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" ') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SP_STAT_MANUAL_ADJUST_COMMENT ?></td>
                                                <td class="main"><?= tep_draw_textarea_field('comments', 'soft', '50', '5', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SP_STAT_NOTIFY_USERS ?></td>
                                                <td class="main"><?= tep_draw_checkbox_field('show_comments', '1') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top">&nbsp;</td>
                                                <td class="main"><?= tep_submit_button(BUTTON_DEDUCT_BALANCE, ALT_BUTTON_DEDUCT_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton') ?></td>
                                            </tr>
                                        </table>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        <? } ?>
                    </td>
                    <td>&nbsp;</td>
                    <td width="47%" valign="top" class="<?= $manual_add_permission ? 'formArea' : '' ?>">
                        <? if ($manual_add_permission) { ?>
                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                <tr>
                                    <td class="formAreaTitle"><?= TABLE_HEADING_SP_STAT_MANUAL_ADD ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?= tep_draw_form('manual_add_form', $filename, tep_get_all_get_params(array('subaction')) . 'subaction=manual_add', 'post', '') ?>
                                        <?= tep_draw_hidden_field("user_id", $user_id) . tep_draw_hidden_field("user_role", $user_role) ?>
                                        <table border="0" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><?= ENTRY_SP_STAT_ADD_AMOUNT ?></td>
                                                <td class="main">+&nbsp;<?= tep_draw_input_field('add_amount', '', 'id="deduct_amount" onkeyup="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" ') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SP_STAT_MANUAL_ADJUST_COMMENT ?></td>
                                                <td class="main"><?= tep_draw_textarea_field('comments', 'soft', '50', '5', '') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top"><?= ENTRY_SP_STAT_NOTIFY_USERS ?></td>
                                                <td class="main"><?= tep_draw_checkbox_field('show_comments', '1') ?></td>
                                            </tr>
                                            <tr>
                                                <td class="main" valign="top">&nbsp;</td>
                                                <td class="main"><?= tep_submit_button(BUTTON_ADD_BALANCE, ALT_BUTTON_ADD_BALANCE, 'onClick="this.disabled=true; this.form.submit(); return true;"', 'inputButton') ?></td>
                                            </tr>
                                        </table>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        <? } ?>
                    </td>
                </tr>
            </table>
            <?
        }
    }

    function manual_deduct_amount($input_array, &$messageStack) {
        $error = false;

        $manual_deduct_permission = tep_admin_files_actions(FILENAME_STORE_POINT, 'STORE_POINT_MANUAL_DEDUCT');

        if ($manual_deduct_permission) {
            // Will create point credit account if not exists
            if ($this->_check_points_account_exists($input_array['user_id'])) {
                $deduct_amount = trim($input_array['deduct_amount']);
                if (is_numeric($deduct_amount) && $deduct_amount > 0) {
                    // Update live credit balance
                    $update_info = array(array('field_name' => 'sp_amount',
                            'operator' => '-',
                            'value' => $deduct_amount)
                    );

                    $new_sp_balance = $this->_set_store_point_balance($input_array['user_id'], $update_info);

                    // Insert store point history
                    $sp_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                        'store_points_history_date' => 'now()',
                        'store_points_history_debit_amount' => (double) $deduct_amount,
                        'store_points_history_credit_amount' => 'NULL',
                        'store_points_history_after_balance' => (double) $new_sp_balance['sp_amount'],
                        'store_points_history_activity_type' => LOG_SP_ACTIVITY_TYPE_MANUAL_RECLAIM,
                        'store_points_history_activity_title' => LOG_SP_STAT_MANUAL_DEDUCTION,
                        'store_points_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
                        'store_points_history_activity_desc_show' => (int) $input_array['show_comments'],
                        'store_points_history_added_by' => $this->identity_email,
                        'store_points_history_added_by_role' => 'admin'
                    );
                    tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_balance_history_data_array);
                    $sp_transaction_id = tep_db_insert_id();

                    $messageStack->add_session(SUCCESS_SP_STAT_MANUAL_DEDUCTION, 'success');

                    // Notification to admin
                    $user_info_array = $this->_get_user_particulars($input_array['user_id']);

                    $display_admin_group = tep_get_admin_group_name($this->identity_email);
                    if (tep_not_null($display_admin_group)) {
                        $display_admin_group = ' [' . $display_admin_group . ']';
                    }

                    $sp_notification_email_contents = sprintf(EMAIL_SP_STAT_MANUAL_ACTION_CONTENT, $sp_transaction_id, LOG_SP_STAT_MANUAL_DEDUCTION, $deduct_amount, $input_array['user_id'], $user_info_array['email'], date('Y-m-d H:i:s'), getenv('REMOTE_ADDR'), $this->identity_email . $display_admin_group, tep_db_prepare_input($input_array['comments'])
                    );

                    $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
                    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                        @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SP_STAT_MANUAL_UPDATE_SUBJECT, LOG_SP_STAT_MANUAL_DEDUCTION, $sp_transaction_id))), $sp_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }

                    // send admin slack notifications
                    $this->sendNotificationBySlack('deduct', $sp_notification_email_contents);
                } else {
                    $error = true;
                    $messageStack->add_session(ERROR_SP_STAT_INVALID_DEDUCTION_AMOUNT, 'error');
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_SP_STAT_USER_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
        }

        if (!$error) {
            return true;
        } else {
            return false;
        }
    }

    function manual_add_amount($input_array, &$messageStack) {
        $error = false;

        $manual_add_permission = tep_admin_files_actions(FILENAME_STORE_POINT, 'STORE_POINT_MANUAL_ADD');

        if ($manual_add_permission) {
            // Will create store credit account if not exists
            if ($this->_check_points_account_exists($input_array['user_id'])) {
                $add_amount = trim($input_array['add_amount']);
                if (is_numeric($add_amount) && $add_amount > 0) {
                    if ($this->_is_within_daily_limit($add_amount / 10000)) {
                        // Update live credit balance
                        $update_info = array(array('field_name' => 'sp_amount',
                                'operator' => '+',
                                'value' => $add_amount)
                        );

                        $new_sp_balance = $this->_set_store_point_balance($input_array['user_id'], $update_info);

                        // Insert store point history
                        $sp_balance_history_data_array = array('customer_id' => tep_db_prepare_input($input_array['user_id']),
                            'store_points_history_date' => 'now()',
                            'store_points_history_debit_amount' => 'NULL',
                            'store_points_history_credit_amount' => (double) $add_amount,
                            'store_points_history_after_balance' => (double) $new_sp_balance['sp_amount'],
                            'store_points_history_activity_type' => LOG_SP_ACTIVITY_TYPE_MANUAL_ISSUE,
                            'store_points_history_activity_title' => LOG_SP_STAT_MANUAL_ADDITION,
                            'store_points_history_activity_desc' => tep_db_prepare_input($input_array['comments']),
                            'store_points_history_activity_desc_show' => (int) $input_array['show_comments'],
                            'store_points_history_added_by' => $this->identity_email,
                            'store_points_history_added_by_role' => 'admin'
                        );
                        tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_balance_history_data_array);
                        $sp_transaction_id = tep_db_insert_id();

                        $messageStack->add_session(SUCCESS_SP_STAT_MANUAL_ADDITION, 'success');

                        // Notification to admin
                        $user_info_array = $this->_get_user_particulars($input_array['user_id']);

                        $display_admin_group = tep_get_admin_group_name($this->identity_email);
                        if (tep_not_null($display_admin_group)) {
                            $display_admin_group = ' [' . $display_admin_group . ']';
                        }

                        $sp_notification_email_contents = sprintf(EMAIL_SP_STAT_MANUAL_ACTION_CONTENT, $sp_transaction_id, LOG_SP_STAT_MANUAL_ADDITION, $add_amount, $input_array['user_id'], $user_info_array['email'], date('Y-m-d H:i:s'), getenv('REMOTE_ADDR'), $this->identity_email . $display_admin_group, tep_db_prepare_input($input_array['comments'])
                        );

                        $email_to_array = tep_parse_email_string(ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL);
                        for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                            @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SP_STAT_MANUAL_UPDATE_SUBJECT, LOG_SP_STAT_MANUAL_ADDITION, $sp_transaction_id))), $sp_notification_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }

                        // send admin slack notifications
                        $this->sendNotificationBySlack('add', $sp_notification_email_contents);
                    } else {
                        $error = true;
                        $messageStack->add_session(ERROR_REACHED_DAILY_CREDIT_LIMIT, 'error');
                    }
                } else {
                    $error = true;
                    $messageStack->add_session(ERROR_SP_STAT_INVALID_ADDITION_AMOUNT, 'error');
                }
            } else {
                $error = true;
                $messageStack->add_session(ERROR_SP_STAT_USER_NOT_EXISTS, 'error');
            }
        } else {
            $error = true;
            $messageStack->add_session(ERROR_PERFORMED_ACTION_DENIED, 'error');
        }

        if (!$error) {
            return true;
        } else {
            return false;
        }
    }

    function _get_user_particulars($user_id) {
        $user_info_select_sql = "SELECT customers_id, customers_firstname AS fname, customers_lastname AS lname, customers_email_address AS email
                                FROM " . TABLE_CUSTOMERS . " AS c
                                WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $user_info_result_sql = tep_db_query($user_info_select_sql);
        $user_info_row = tep_db_fetch_array($user_info_result_sql);
        return $user_info_row;
    }

    function _check_points_account_exists($user_id) {
        $point_acc_select_sql = "SELECT customers_id
                                FROM " . TABLE_STORE_POINTS . "
                                WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $point_acc_result_sql = tep_db_query($point_acc_select_sql);

        if (tep_db_num_rows($point_acc_result_sql) > 0) { // Store points account exists
            return true;
        } else { // Check if this is a valid customer
            $customer_select_sql = "SELECT customers_id
                                    FROM " . TABLE_CUSTOMERS . "
                                    WHERE customers_id = '" . tep_db_input($user_id) . "'";
            $customer_result_sql = tep_db_query($customer_select_sql);

            if (tep_db_num_rows($customer_result_sql) > 0) { // Valid customer
                $sp_balance_data_array = array('customers_id' => $user_id,
                    'sp_amount' => 0,
                    'sp_last_modified' => 'now()'
                );
                tep_db_perform(TABLE_STORE_POINTS, $sp_balance_data_array);

                return true;
            } else {
                return false;
            }
        }
    }

    // Sets the available & actual qty of a product
    function _set_store_point_balance($user_id, $sp_array) {
        global $log_object;
        /*         * *****************************************************************
          operator = + (add credit), - (deduct credit), = (assign new credit)
         * ***************************************************************** */
        $sql_update_array = array();

        // Generate the update sql
        for ($sp_cnt = 0; $sp_cnt < count($sp_array); $sp_cnt++) {
            if (($sp_array[$sp_cnt]['field_name'] == 'sp_amount')) {
                $sp_array[$sp_cnt]['operator'] = trim($sp_array[$sp_cnt]['operator']);
                switch ($sp_array[$sp_cnt]['operator']) {
                    case '+':
                        $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . $sp_array[$sp_cnt]['field_name'] . ' + ' . tep_db_input($sp_array[$sp_cnt]['value']);
                        break;
                    case '-':
                        $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . $sp_array[$sp_cnt]['field_name'] . ' - ' . tep_db_input($sp_array[$sp_cnt]['value']);
                        break;
                    case '=':
                        $sql_update_array[] = $sp_array[$sp_cnt]['field_name'] . ' = ' . tep_db_input($sp_array[$sp_cnt]['value']);
                        break;
                    default:
                        break;
                }
            }
        }

        if (count($sql_update_array)) {
            $sql_update_array[] = ' sp_last_modified = now() ';

            $update_sql_str = " SET " . implode(', ', $sql_update_array);

            /*             * ***********************************************************************
              Lock the TABLE_STORE_POINTS
              REMEMBER: Need to lock all the tables involved in this block.
             * *********************************************************************** */
            tep_db_query("LOCK TABLES " . TABLE_STORE_POINTS . " WRITE;");

            $sp_update_sql = "	UPDATE " . TABLE_STORE_POINTS .
                    $update_sql_str . "
								WHERE customers_id = '" . tep_db_input($user_id) . "'";
            tep_db_query($sp_update_sql);

            tep_db_query("UNLOCK TABLES;");
            /*             * ******************************************************************
              End of locking the TABLE_STORE_POINTS table.
             * ****************************************************************** */

            return $this->_get_current_points_balance($user_id);
        }
    }

    function _get_current_points_balance($user_id) {
        $point_accounts = array('sp_amount' => 0);

        $point_acc_select_sql = "SELECT sp_amount
                                FROM " . TABLE_STORE_POINTS . "
                                WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $point_acc_result_sql = tep_db_query($point_acc_select_sql);
        if ($point_acc_row = tep_db_fetch_array($point_acc_result_sql)) {
            $point_accounts = array('sp_amount' => $point_acc_row['sp_amount']);
        }

        return $point_accounts;
    }

    function get_current_points_balance($user_id) {
        $point_accounts = array('sp_amount' => 0);

        $point_acc_select_sql = "SELECT sp_amount
                                FROM " . TABLE_STORE_POINTS . "
                                WHERE customers_id = '" . tep_db_input($user_id) . "'";
        $point_acc_result_sql = tep_db_query($point_acc_select_sql);
        if ($point_acc_row = tep_db_fetch_array($point_acc_result_sql)) {
            $point_accounts = array('sp_amount' => $point_acc_row['sp_amount']);
        }

        return $point_accounts;
    }

    function _is_within_daily_limit($add_amount) {
        $action_allowed = false;

        $admin_credit_limit_select_sql = "SELECT admin_credit_limit_total, admin_credit_limit_max
                                            FROM " . TABLE_ADMIN_CREDIT_LIMIT . "
                                            WHERE admin_id = " . tep_db_input($this->identity);
        $admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);
        if ($admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql)) {
            print "<pre>";
            print_r($admin_credit_limit_row);
            print "</pre>";
            if ($admin_credit_limit_row['admin_credit_limit_total'] + $add_amount <= $admin_credit_limit_row['admin_credit_limit_max']) {
                $action_allowed = true;

                $admin_credit_limit_update_sql = "UPDATE " . TABLE_ADMIN_CREDIT_LIMIT . "
                                                SET admin_credit_limit_total = admin_credit_limit_total + " . $add_amount . "
                                                WHERE admin_id = " . tep_db_input($this->identity);
                tep_db_query($admin_credit_limit_update_sql);
            }
        }

        return $action_allowed;
    }

    function reset_deliver_store_points($orders_id) {
        $orders_select_sql = "	SELECT o.customers_id, o.orders_rebated , oei.orders_extra_info_value
								FROM " . TABLE_ORDERS . " As o INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei on o.orders_id = oei.orders_id  AND oei.orders_extra_info_key = 'site_id'
								WHERE o.orders_id = '" . (int) $orders_id . "'";
        $orders_result_sql = tep_db_query($orders_select_sql);
        if ($orders_row = tep_db_fetch_array($orders_result_sql)) {
            if ($orders_row['orders_rebated'] == 1) {
                $total_delivered_op = 0;

                $orders_products_select_sql = "	SELECT orders_products_id, op_rebate_delivered
												FROM " . TABLE_ORDERS_PRODUCTS . " 
												WHERE orders_id = '" . (int) $orders_id . "'
													AND parent_orders_products_id < 1 
													AND orders_products_is_compensate = 0";
                $orders_products_result_sql = tep_db_query($orders_products_select_sql);
                while ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
                    $total_delivered_op += $orders_products_row['op_rebate_delivered'];

                    $sql_data_array = array('op_rebate_delivered' => 0);
                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array, 'update', " orders_products_id = '" . (int) $orders_products_row['orders_products_id'] . "'");
                }

                $update_info = array(array('field_name' => 'sp_amount',
                        'operator' => '-',
                        'value' => $total_delivered_op)
                );
                if ($orders_row['orders_extra_info_value'] == 5) {
                    g2g_serverless::adjustG2gTokenAmount($orders_row['customers_id'], LOG_SP_ACTIVITY_TYPE_PURCHASE_DEDUCT, $total_delivered_op, '', $orders_id, true);
                    $new_sp_balance = g2g_serverless::getG2gTokenAmount($orders_row['customers_id']);
                } else {
                    $new_sp_balance = $this->_set_store_point_balance($orders_row['customers_id'], $update_info);
                    $sp_balance_history_data_array = array('customer_id' => tep_db_prepare_input($orders_row['customers_id']),
                        'store_points_history_date' => 'now()',
                        'store_points_history_debit_amount' => (double) $total_delivered_op,
                        'store_points_history_credit_amount' => 'NULL',
                        'store_points_history_after_balance' => (double) $new_sp_balance['sp_amount'],
                        'store_points_history_trans_id' => $orders_id,
                        'store_points_history_activity_type' => LOG_SP_ACTIVITY_TYPE_PURCHASE_DEDUCT,
                        'store_points_history_activity_desc_show' => 1,
                        'store_points_history_added_by' => 'system',
                        'store_points_history_added_by_role' => 'admin'
                    );
                    tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_balance_history_data_array);
                }

                $orders_data_array = array('orders_rebated' => 0);
                tep_db_perform(TABLE_ORDERS, $orders_data_array, 'update', " orders_id = '" . (int) $orders_id . "'");
            }
        }
    }

    function sendNotificationBySlack($action = 'add', $message = '')
    {
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $slackData = json_encode(array(
            'text' => '[OG Crew] Manual OP ' . (($action == 'add') ? 'Addition' : 'Deduction') . ' - ' . date("F j, Y H:i"),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $message,
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_ANB, $slackData);
    }

}
?>