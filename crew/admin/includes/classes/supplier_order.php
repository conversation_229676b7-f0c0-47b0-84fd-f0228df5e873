<?
/*
	$Id: supplier_order.php,v 1.11 2007/05/02 08:34:54 sunny Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class supplier_order
{
	var $info, $totals, $products, $supplier, $payment, $order_id;
	
    function supplier_order($order_id)
    {
      	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->supplier = array();
		$this->payment = array();
		
		$this->order_id = $order_id;
		
      	$this->query($order_id);
    }
	
    function query($order_id)
    {
		$order_query = tep_db_query("SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "'");
      	$order = tep_db_fetch_array($order_query);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_groups_id, s.supplier_gender, s.supplier_dob, s.supplier_date_account_created AS date_created, s.supplier_fax, s.supplier_qq, s.supplier_msn, 
												s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, 
												sg.supplier_groups_name, sg.supplier_groups_id 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $order["suppliers_id"] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
		$supplier_pricing_setting_array = tep_get_supplier_pricing_setting($supplier_personal_row['supplier_groups_id'], $order['products_purchases_lists_id']);
		if (!isset($supplier_pricing_setting_array['rstk_char_set']))	$supplier_pricing_setting_array['rstk_char_set'] = '';
		
      	$this->info = array('currency' => $order['currency'],
                          	'currency_value' => $order['currency_value'],
                          	'date_submitted' => $order['supplier_order_lists_date'],
                          	'orders_status' => $order['supplier_order_lists_status'],
                          	'billing_status' => $order['supplier_order_lists_billing_status'],
                          	'verified_status' => $order['supplier_orders_verify_mode'],
                          	'list_name' => $order['products_purchases_lists_name'],
                          	'remote_addr' => $order['remote_addr'],
                          	'last_modified' => $order['supplier_order_lists_last_modified'],
                          	'products_purchases_lists_id' => $order['products_purchases_lists_id']);
		
      	$this->supplier = array('id' => $order['suppliers_id'],
      							'name' => $order['suppliers_name'],
                              	'street_address' => $order['suppliers_street_address'],
                              	'suburb' => $order['suppliers_suburb'],
                              	'city' => $order['suppliers_city'],
                              	'postcode' => $order['suppliers_postcode'],
                              	'state' => $order['suppliers_state'],
                              	'country' => $order['suppliers_country'],
                              	'format_id' => 1,
                              	'telephone' => $order['suppliers_telephone'],
                              	'fax' => $supplier_personal_row['supplier_fax'],
                              	'qq' => $supplier_personal_row['supplier_qq'],
                              	'msn' => $supplier_personal_row['supplier_msn'],
                              	'email_address' => $order['suppliers_email_address'],
                              	'gender' => $supplier_personal_row['supplier_gender'],
                              	'dob' => $supplier_personal_row['supplier_dob'],
                              	'date_account_created' => $supplier_personal_row['date_created'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code'],
                              	'supplier_groups_id' => $supplier_personal_row['supplier_groups_id']);
		
		$this->payment = array(	'paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              	'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
        
		// Grab the first list products
      	$index = 0;
      	
      	$prod_sort_by = isset($_SESSION['sup_order_lists_param']["sort_by"]) ? $_SESSION['sup_order_lists_param']["sort_by"] : 'sort_faction_servers';
      	$prod_sort_order = isset($_SESSION['sup_order_lists_param']["sort_order"]) ? $_SESSION['sup_order_lists_param']["sort_order"] : 'ASC';
      	
      	if ($prod_sort_by == 'sort_servers') {
	      	$unique_product_select_sql = "	SELECT DISTINCT solp.products_id AS pid, p.products_cat_path 
	      									FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
	      									LEFT JOIN " . TABLE_PRODUCTS . " AS p 
	      										ON solp.products_id=p.products_id 
	      									WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "'
	      									ORDER BY products_cat_path " . $prod_sort_order;
	   	} else {
	   		$unique_product_select_sql = "	SELECT DISTINCT solp.products_id AS pid, 
	      										IF(LOCATE('-', REVERSE(products_cat_path)), REVERSE(TRIM(LEFT(REVERSE(products_cat_path), CAST(LOCATE('-', REVERSE(products_cat_path)) AS UNSIGNED) -1 ))), products_cat_path) AS fraction,
	      										p.products_cat_path 
	      									FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
	      									LEFT JOIN " . TABLE_PRODUCTS . " AS p 
	      										ON solp.products_id=p.products_id 
	      									WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "'
	      									ORDER BY fraction " . $prod_sort_order . ", products_cat_path " . $prod_sort_order;
	    }
	    
      	$unique_product_result_sql = tep_db_query($unique_product_select_sql);
      	while ($unique_product_row = tep_db_fetch_array($unique_product_result_sql)) {
      		$first_orders_products_select_sql = "	SELECT solp.*, rci.restock_character AS real_time_rstk_char 
      												FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
      												LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
      													ON (solp.products_id=rci.products_id AND rci.restock_character_sets_id='".tep_db_input($supplier_pricing_setting_array['rstk_char_set'])."') 
      												WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
      													AND solp.products_id = '" . tep_db_input($unique_product_row["pid"]) . "' 
      													AND solp.supplier_order_lists_type=1";
      		$first_orders_products_result_sql = tep_db_query($first_orders_products_select_sql);
        	$first_orders_products_row = tep_db_fetch_array($first_orders_products_result_sql);
        	
        	$confirmed_orders_products_query = tep_db_query("SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "' AND products_id = '" . tep_db_input($unique_product_row["pid"]) . "' AND supplier_order_lists_type=2");
        	$confirmed_orders_row = tep_db_fetch_array($confirmed_orders_products_query);
        	
        	$payable_amount = 0;
        	if (tep_db_num_rows($confirmed_orders_products_query)) {
        		$payable_amount = $this->get_payable_amount($confirmed_orders_row["products_received_quantity"], $confirmed_orders_row["first_max_quantity"], $confirmed_orders_row["first_max_unit_price"], $confirmed_orders_row["second_max_quantity"], $confirmed_orders_row["second_max_unit_price"]);
        	}
        	
        	$this->products[$index] = array('id' => $unique_product_row['pid'],
											'first_list' => $first_orders_products_row,
	                                       	'confirm_list' => $confirmed_orders_row,
	                                       	'payable_amount' => $payable_amount,
	                                       	'sort_str' => ($prod_sort_by == 'sort_servers') ? $unique_product_row['products_cat_path'] : $unique_product_row['fraction'] . $unique_product_row['products_cat_path']
	                                       	);
			
        	$index++;
      	}
	}
	
	function get_order_total_payable_amount($order_id='') {
		if (!tep_not_null($order_id)) {
			$order_id = $this->order_id;
		}
		
		$total_payable_amount = 0;
		
		$distinct_product_select_sql = "SELECT DISTINCT solp.products_id 
										FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
										WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
											AND solp.supplier_order_lists_type=2";
		$distinct_product_result_sql = tep_db_query($distinct_product_select_sql);
		
		while ($distinct_product_row = tep_db_fetch_array($distinct_product_result_sql)) {
			$payable_amount_select_sql = "	SELECT (IF(solp.products_received_quantity > solp.first_max_quantity, IF(solp.products_received_quantity > solp.first_max_quantity+solp.second_max_quantity, solp.first_max_quantity*solp.first_max_unit_price + solp.second_max_quantity*solp.second_max_unit_price, solp.first_max_quantity*solp.first_max_unit_price + (solp.products_received_quantity-solp.first_max_quantity)*solp.second_max_unit_price), solp.products_received_quantity*solp.first_max_unit_price)) AS total_amount 
											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
											WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
												AND solp.supplier_order_lists_type=2 
												AND solp.products_id = '" . tep_db_input($distinct_product_row['products_id']) . "' 
											GROUP BY solp.products_id 
											LIMIT 1	";
			$payable_amount_result_sql = tep_db_query($payable_amount_select_sql);
			
			while ($payable_amount_row = tep_db_fetch_array($payable_amount_result_sql)) {
				$total_payable_amount += $payable_amount_row["total_amount"];
			}
		}
		
		return $total_payable_amount;
	}
	
	function get_payable_amount($received_qty, $first_max, $first_max_unit_price, $second_max, $second_max_unit_price)
	{
		$payable_amount = 0;
		$received_qty = (int)$received_qty;
		
		if ($received_qty > $first_max) {
			if ($received_qty > $first_max + $second_max) {
				$payable_amount = $first_max * $first_max_unit_price + $second_max * $second_max_unit_price;
			} else {
				$payable_amount = $first_max * $first_max_unit_price + ($received_qty - $first_max) * $second_max_unit_price;
			}
		} else {
			$payable_amount = $received_qty * $first_max_unit_price;
		}
		
		return $payable_amount;
	}
	
	function get_products_ordered()
	{
		global $currencies;
		
		$products_ordered = '<table border="0" cellspacing="2" cellpadding="2">'.
						  	'	<tr>'.
						  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">Product</td>'.
						  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">First List Selling Quantity</td>'.
						  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">Confirmation List Selling Quantity</td>'.
						  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;" align="center">Received Quantity</td>'.
						  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;" align="center">Payable Amount</td>'.
						  	'	</tr>';
		for ($i=0; $i < count($this->products); $i++) {
			if (is_array($this->products[$i]['confirm_list']) && count($this->products[$i]['confirm_list'])) {
				$suggested_quantity = (int)$this->products[$i]['first_list']['products_quantity'];
				$selling_quantity = (int)$this->products[$i]['confirm_list']['products_quantity'];
				$received_quantity = (int)$this->products[$i]['confirm_list']['products_received_quantity'];
				$alert_style = $received_quantity < $selling_quantity ? '#ff0000' : '#000000';
				
			    $products_ordered .='	<tr>'.
							  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;">'.$this->products[$i]['confirm_list']['products_display_name'].'</td>'.
							  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="center">'.$suggested_quantity.'</td>'.
							  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="center">'.$selling_quantity.'</td>'.
							  		'		<td style="background-color:#DFDFDF; color:'.$alert_style.'; font-size:11px;" align="center">'.$received_quantity.'</td>'.
							  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="right">'.$currencies->format($this->products[$i]['payable_amount']).'</td>'.
							  		'	</tr>';
			}
		}
		$products_ordered .= '</table>';
		
		return $products_ordered;
	}
}
?>