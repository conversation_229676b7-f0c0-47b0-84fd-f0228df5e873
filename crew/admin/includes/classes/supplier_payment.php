<?
/*
	$Id: supplier_payment.php,v 1.4 2006/08/08 04:37:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

/**************************************************************************
	NOTE: require(DIR_WS_CLASSES . 'supplier_order.php');
**************************************************************************/
class supplier_payment
{
	var $info, $payment_info, $orders, $supplier;
	var $payment_id;
	
    function supplier_payment($payment_id='')
    {
      	$this->info = array();
      	$this->orders = array();
      	$this->supplier = array();
		$this->payment_info = array();
					
		if (tep_not_null($payment_id)) {
			$this->payment_id = $payment_id;
      		$this->query($payment_id);
      	}
    }
	
	function set_remarks($remarks) {
		$this->payment_info['remarks'] = $remarks;
	}
	
	function set_show_supplier_remark($show_supplier) {
		$this->payment_info['show_supplier'] = $show_supplier;
	}
	
    function query($payment_id)
    {
    	$payment_info_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_PAYMENTS . " WHERE supplier_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $payment_info_row['suppliers_id'] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
      	$this->info = array('currency' => $payment_info_row['currency'],
                          	'currency_value' => $payment_info_row['currency_value'],
                          	'payments_amount' => $payment_info_row['supplier_payments_amount'],
                          	'payments_tax' => $payment_info_row['supplier_payments_tax'],
                          	'payments_total' => $payment_info_row['supplier_payments_total'],
                          	'payments_date' => $payment_info_row['supplier_payments_date'],
                          	'last_modified' => $payment_info_row['supplier_payments_last_modified'],
                          	'payments_status' => $payment_info_row['supplier_payments_status']
                          	);
		
      	$this->supplier = array('id' => $payment_info_row['suppliers_id'],
      							'firstname' => $payment_info_row['suppliers_firstname'],
                              	'lastname' => $payment_info_row['suppliers_lastname'],
                              	'street_address' => $payment_info_row['suppliers_street_address'],
                              	'suburb' => $payment_info_row['suppliers_suburb'],
                              	'city' => $payment_info_row['suppliers_city'],
                              	'postcode' => $payment_info_row['suppliers_postcode'],
                              	'state' => $payment_info_row['suppliers_state'],
                              	'country' => $payment_info_row['suppliers_country'],
                              	'format_id' => 1,
                              	'email_address' => $payment_info_row['suppliers_email_address'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment_info = array('paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              		'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
	                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
	                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
	                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
	                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
	                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
        
		// Grab the payment's orders
      	$index = 0;
      	
      	$payment_orders_select_sql = "	SELECT spo.supplier_order_lists_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sol.products_purchases_lists_name, sol.supplier_order_lists_date, sol.currency, sol.currency_value 
    									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
    									INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
    										ON spo.supplier_order_lists_id=sol.supplier_order_lists_id 
    									WHERE spo.supplier_payments_id = '" . tep_db_input($payment_id) . "'
    									ORDER BY spo.supplier_order_lists_id";
    	$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
    	
		while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
			$order_payable_amount = supplier_order::get_order_total_payable_amount($payment_orders_row['supplier_order_lists_id']);
			
			$this->orders[$index] = array(	'id' => $payment_orders_row['supplier_order_lists_id'],
											'list_name' => $payment_orders_row['products_purchases_lists_name'],
											'date_submitted' => $payment_orders_row['supplier_order_lists_date'],
											'currency' => $payment_orders_row['currency'],
                          					'currency_value' => $payment_orders_row['currency_value'],
											'payable_amount' => $order_payable_amount,
											'paid_amount' => $payment_orders_row['supplier_payments_orders_paid_amount'],
											'paid_currency' => $payment_info_row['currency'],
                          					'paid_currency_value' => $payment_info_row['currency_value'],
                          					'payments_type' => $payment_orders_row['supplier_payments_type']
	                                       	);
			
        	$index++;
  		}
	}
	
	function make_payment($order_ids_array, $orders_pay_array)
	{
		global $currencies, $login_email_address;
		
		$result = array();
		$this_payment_amount = 0;
		$success_make_payment = false;
		
		if (is_array($order_ids_array) && count($order_ids_array)) {	// Check if at least one order is selected
			$processing_order_count_select_sql = "	SELECT COUNT(supplier_order_lists_id) AS processing_orders, COUNT(DISTINCT suppliers_id) AS total_supplier 
													FROM " . TABLE_SUPPLIER_ORDER_LISTS . " 
													WHERE supplier_order_lists_id IN ('" .implode("', '", $order_ids_array) . "') 
													AND supplier_order_lists_status = 2";
			$processing_order_count_result_sql = tep_db_query($processing_order_count_select_sql);
			$processing_order_count_row = tep_db_fetch_array($processing_order_count_result_sql);
			
			if ($processing_order_count_row['processing_orders'] != count($order_ids_array)) { // Total processing orders unmatch the total selected orders
				$result = array('text' => ERROR_NON_PROCESSING_ORDERS, 'type' => 'error');
			} else if ($processing_order_count_row['total_supplier'] > 1) {	// Those selected orders belong to more than one supplier
				$result = array('text' => ERROR_MULTIPLE_SUPPLIER_PAYMENT, 'type' => 'error');
			} else {
				// Get supplier id first
				$supplier_id_select_sql = "SELECT suppliers_id FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_ids_array[0]) . "'";
				$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
				if ($supplier_id_row = tep_db_fetch_array($supplier_id_result_sql)) {
					$supplier_id = (int)$supplier_id_row['suppliers_id'];
					
					$supplier_address_select_sql = "SELECT s.*, z.zone_name, co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id  
													FROM " . TABLE_SUPPLIER . " AS s
													LEFT JOIN " . TABLE_ZONES . " AS z 
														ON (s.supplier_zone_id = z.zone_id) 
													LEFT JOIN " . TABLE_COUNTRIES . " AS co 
														ON (s.supplier_country_id = co.countries_id) 
													WHERE s.supplier_id = '" . $supplier_id . "'";
					
			      	$supplier_address_result_sql = tep_db_query($supplier_address_select_sql);
			      	if ($supplier_address_row = tep_db_fetch_array($supplier_address_result_sql)) {
						// Create new payment
			      		$payment_data_array = array('suppliers_id' => $supplier_id,
						                            'supplier_payments_date' => 'now()',
						                            'supplier_payments_last_modified' => 'now()',
						                            'supplier_payments_status' => 2,
						                            'suppliers_firstname' => $supplier_address_row['supplier_firstname'],
						                            'suppliers_lastname' => $supplier_address_row['supplier_lastname'],
						                            'suppliers_street_address' => $supplier_address_row['supplier_street_address'],
													'suppliers_suburb' => $supplier_address_row['supplier_suburb'],
													'suppliers_city' => $supplier_address_row['supplier_city'],
													'suppliers_postcode' => $supplier_address_row['supplier_postcode'],
						                            'suppliers_state' => ((tep_not_null($supplier_address_row['supplier_state'])) ? $supplier_address_row['supplier_state'] : $supplier_address_row['zone_name']),
						                            'suppliers_country' => $supplier_address_row['countries_name'],
						                            'suppliers_telephone' => $supplier_address_row['supplier_telephone'],
						                            'suppliers_email_address' => $supplier_address_row['supplier_email_address'],
						                            'currency' => DEFAULT_CURRENCY,
						                            'currency_value' => $currencies->currencies[DEFAULT_CURRENCY]['value']
						                           );
					    tep_db_perform(TABLE_SUPPLIER_PAYMENTS, $payment_data_array);
					    $supplier_payments_id = tep_db_insert_id();
					    
					    // Insert payment orders
					    for ($order_cnt=0; $order_cnt < count($order_ids_array); $order_cnt++) {
					    	$confirm_pay_amt = 0;
					    	$confirm_pay_remaining = false;
					    	
					    	$order_payable_amt = supplier_order::get_order_total_payable_amount($order_ids_array[$order_cnt]);
					    	$order_paid_amt = $this->get_order_paid_amount($order_ids_array[$order_cnt]);
					    	
					    	$order_unpaid_amt = (double)$order_payable_amt - (double)$order_paid_amt;
					    	
					    	if (isset($orders_pay_array[$order_ids_array[$order_cnt]]) && is_numeric($orders_pay_array[$order_ids_array[$order_cnt]])) {
					    		$desire_pay_amt = (double)$orders_pay_array[$order_ids_array[$order_cnt]];
					    		if ($desire_pay_amt > 0 && $desire_pay_amt < $order_unpaid_amt) {	// Satisfy partial pay amount
					    			$confirm_pay_amt = $desire_pay_amt;
					    		} else {	// Considered pay the whole remaining amt
					    			$confirm_pay_amt = $order_unpaid_amt;
					    			$confirm_pay_remaining = true;
					    		}
					    	} else {	// Definitely pay the whole remaining amt 
					    		$confirm_pay_amt = $order_unpaid_amt;
					    		$confirm_pay_remaining = true;
					    	}
					    	
					    	$supplier_payments_type = ($confirm_pay_amt < (double)$order_payable_amt) ? '1' : '2';
					    	
						    $payment_order_data_array = array(	'supplier_payments_id' => $supplier_payments_id,
				      											'supplier_order_lists_id' => $order_ids_array[$order_cnt],
				      											'supplier_payments_orders_paid_amount' => $confirm_pay_amt,
																'supplier_payments_type' => $supplier_payments_type
								                           	);
						    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_ORDERS, $payment_order_data_array);
						    
						    // Update supplier orders from Processing to Completed (Just for pay whole remaining amt payment orders)
						    // Inserting order history
						    if ($confirm_pay_remaining) {
							    $completing_orders_update_data_array = array(	'supplier_order_lists_status' => 3,
			          					 										'supplier_order_lists_last_modified' => 'now()',
			          					 									);
			          			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $completing_orders_update_data_array, 'update', "supplier_order_lists_id = '" . $order_ids_array[$order_cnt] . "'");
			          			
			          			$order_list_history_data_array = array(	'supplier_order_lists_id' => $order_ids_array[$order_cnt],
												                        'supplier_order_lists_status' => 3,
												                        'date_added' => 'now()',
												                        'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
												                        'comments' => $this->payment_info['remarks'],
												                        'changed_by' => $login_email_address
												                       );
								tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
						    } else {
						    	// Just insert comment, no status changes
						    	$order_list_history_data_array = array(	'supplier_order_lists_id' => $order_ids_array[$order_cnt],
												                        'supplier_order_lists_status' => 0,
												                        'date_added' => 'now()',
												                        'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
												                        'comments' => $this->payment_info['remarks'],
												                        'changed_by' => $login_email_address
												                       );
								tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
						    }
						    
							$this_payment_amount += (double)$confirm_pay_amt;
						}
					    
						// Update payment amount data
					    $payment_update_data_array = array(	'supplier_payments_amount' => $this_payment_amount,
	          					 							'supplier_payments_total' => $this_payment_amount
	          					 							);
	          			tep_db_perform(TABLE_SUPPLIER_PAYMENTS, $payment_update_data_array, 'update', "supplier_payments_id = '" . $supplier_payments_id . "'");
					    
					    // Insert payment history
					    $payment_history_data_array = array('supplier_payments_id' => $supplier_payments_id,
			      											'supplier_payments_status' => 2,
			      											'date_added' => 'now()',
															'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
								                            'comments' => $this->payment_info['remarks'],
								                            'changed_by' => $login_email_address
							                           	);
					    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_HISTORY, $payment_history_data_array);
					    
						$success_make_payment = true;
						
						// Send email to supplier
						$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
									EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $supplier_payments_id) . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $currencies->format($this_payment_amount)) . "\n" .
								 	sprintf(EMAIL_TEXT_PAYMENT_DATE, strftime(DATE_FORMAT_LONG)) . "\n\n" .
								 	($this->payment_info['show_supplier'] ? sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" : '') .
								 	EMAIL_TEXT_CLOSING . "\n\n" .
								 	EMAIL_FOOTER;
						
						$email_greeting = tep_get_email_greeting($supplier_address_row['supplier_firstname'], $supplier_address_row['supplier_lastname'], $supplier_address_row['supplier_gender']);
						
						$email = $email_greeting . $email;
						tep_mail($supplier_address_row['supplier_firstname'].' '.$supplier_address_row['supplier_lastname'], $supplier_address_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_SUBJECT, $supplier_payments_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			      	}
				}
				
				if ($success_make_payment) {
					$result = array('text' => sprintf(SUCCESS_MAKE_PAYMENT, $supplier_payments_id), 'type' => 'success');
				} else {
					$result = array('text' => ERROR_NEW_PAYMENT_FAILED, 'type' => 'error');
				}
			}
		} else {
			$result = array('text' => ERROR_NO_ORDERS_SELECTED, 'type' => 'error');
		}
		
		return $result;
	}
	
	function reverse_payment()
	{
		global $currencies, $login_email_address, $languages_id;
		
		$result = array();
		$success_reverse_payment = false;
		
		$payment_info_select_sql = "SELECT suppliers_id, supplier_payments_status, supplier_payments_date, supplier_payments_total, currency, currency_value FROM " . TABLE_SUPPLIER_PAYMENTS . " WHERE supplier_payments_id = '" . tep_db_input($this->payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	
		if ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {	// Check if payment is exists
			if ($payment_info_row['supplier_payments_status'] == '1' || $payment_info_row['supplier_payments_status'] == '2') {
				$supplier_id = (int)$payment_info_row['suppliers_id'];
				
				$payment_status_array = array();
				$payment_status_select_sql = "SELECT supplier_payments_status_id, supplier_payments_status_name FROM " . TABLE_SUPPLIER_PAYMENTS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY supplier_payments_status_sort_order";
				$payment_status_result_sql = tep_db_query($payment_status_select_sql);
				while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
					$payment_status_array[$payment_status_row['supplier_payments_status_id']] = $payment_status_row['supplier_payments_status_name'];                       
				}

				$payment_status_update_sql_data = array('supplier_payments_status' => 3,
      					 								'supplier_payments_last_modified' => 'now()',
      					 								);
      			tep_db_perform(TABLE_SUPPLIER_PAYMENTS, $payment_status_update_sql_data, 'update', "supplier_payments_id = '" . tep_db_input($this->payment_id) . "'");
      			
				// Insert history
				$reverse_date = date("Y-m-d H:i:s");
			    $payment_history_data_array = array('supplier_payments_id' => $this->payment_id,
	      											'supplier_payments_status' => 3,
	      											'date_added' => $reverse_date,
													'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
						                            'comments' => $this->payment_info['remarks'],
						                            'changed_by' => $login_email_address
					                           	);
			    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_HISTORY, $payment_history_data_array);
			    
			    $payment_orders_select_sql = "	SELECT spo.supplier_order_lists_id, spo.supplier_payments_type, sol.supplier_order_lists_status 
	        									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
	        									INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
	        										ON spo.supplier_order_lists_id=sol.supplier_order_lists_id 
	        									WHERE spo.supplier_payments_id = '" . tep_db_input($this->payment_id) . "'";
	        	$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
			    
			    while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
			    	if ($payment_orders_row['supplier_order_lists_status'] == 3) {	// This order is Completed order
			    		// Update supplier orders from Completed to Processing
					    $processing_orders_update_sql_data = array(	'supplier_order_lists_status' => 2,
		      					 									'supplier_order_lists_last_modified' => 'now()',
		      					 									);
		      			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $processing_orders_update_sql_data, 'update', "supplier_order_lists_id = '" . $payment_orders_row['supplier_order_lists_id'] . "'");
		      			
		      			// Inserting order history (Completed -> Processing)
		      			$order_list_history_data_array = array(	'supplier_order_lists_id' => $payment_orders_row['supplier_order_lists_id'],
										                        'supplier_order_lists_status' => 2,
										                        'date_added' => 'now()',
										                        'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
										                        'comments' => $this->payment_info['remarks'],
										                        'changed_by' => $login_email_address
										                       );
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
			    	} else {	// This is partial paid order
			    		// Inserting order history (Updating comment)
		      			$order_list_history_data_array = array(	'supplier_order_lists_id' => $payment_orders_row['supplier_order_lists_id'],
										                        'supplier_order_lists_status' => 0,
										                        'date_added' => 'now()',
										                        'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
										                        'comments' => $this->payment_info['remarks'],
										                        'changed_by' => $login_email_address
										                       );
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
			    	}
			    }
			    
				$supplier_info_select_sql = "	SELECT s.supplier_firstname, s.supplier_lastname, s.supplier_email_address, s.supplier_gender 
												FROM " . TABLE_SUPPLIER . " AS s
												WHERE s.supplier_id = '" . $supplier_id . "'";
				$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
				$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
				
				// Send email to supplier
				$reverse_amount = $currencies->format($payment_info_row['supplier_payments_total'], true, $payment_info_row['currency'], $payment_info_row['currency_value']);
				
				$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
							EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $this->payment_id) . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $reverse_amount) . "\n" .
						 	sprintf(EMAIL_TEXT_PAYMENT_DATE, tep_date_long($payment_info_row["supplier_payments_date"])) . "\n\n" .
						 	sprintf(EMAIL_TEXT_STATUS_UPDATE, $payment_status_array[$payment_info_row['supplier_payments_status']] . ' -> ' . $payment_status_array['3']) . "\n\n" . 
						 	($this->payment_info['show_supplier'] ? sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" : '') .
						 	EMAIL_TEXT_CLOSING . "\n\n" .
						 	EMAIL_FOOTER;
				
				$email_greeting = tep_get_email_greeting($supplier_info_row['supplier_firstname'], $supplier_info_row['supplier_lastname'], $supplier_info_row['supplier_gender']);
				
				$email = $email_greeting . $email;
				tep_mail($supplier_info_row['supplier_firstname'].' '.$supplier_info_row['supplier_lastname'], $supplier_info_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_UPDATE_SUBJECT, $this->payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				
				$result = array('text' => sprintf(SUCCESS_REVERSE_PAYMENT, $this->payment_id), 'type' => 'success');
				
				/*****************************************************************
					Send notification e-mail on reversing supplier payment
				*****************************************************************/
				$admin_email_to_array = tep_parse_email_string(REVERSE_SUPPLIER_PAYMENT_EMAIL);
        		$reverse_payment_notification_email = sprintf(EMAIL_TEXT_REVERSE_SUPPLIER_PAYMENT_INFO, $reverse_date, $this->payment_info['remarks'], $login_email_address . '('.$_SERVER['REMOTE_ADDR'].')', $this->payment_id, $this->supplier['firstname'] . ' ' . $this->supplier['lastname'], $reverse_amount);
				
        		for ($admin_email_to_cnt=0; $admin_email_to_cnt < count($admin_email_to_array); $admin_email_to_cnt++) {
        			tep_mail($admin_email_to_array[$admin_email_to_cnt]['name'], $admin_email_to_array[$admin_email_to_cnt]['email'], sprintf(EMAIL_REVERSE_SUPPLIER_PAYMENT_NOTIFICATION_SUBJECT, $this->payment_id), $reverse_payment_notification_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        		}
			} else {
				$result = array('text' => ERROR_NOT_REVERSIBLE_PAYMENT_STATUS, 'type' => 'error');
			}
		} else {
			$result = array('text' => ERROR_PAYMENT_DOES_NOT_EXIST, 'type' => 'error');
		}
		
		return $result;
	}
	
	function update_payment_comment()
	{
		global $currencies, $login_email_address, $languages_id;
		
		$payment_history_data_array = array('supplier_payments_id' => $this->payment_id,
  											'supplier_payments_status' => 0,
  											'date_added' => 'now()',
											'supplier_notified' => $this->payment_info['show_supplier'] ? '1' : '0',
				                            'comments' => $this->payment_info['remarks'],
				                            'changed_by' => $login_email_address
			                           	);
	    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_HISTORY, $payment_history_data_array);
	    
	    if ($this->payment_info['show_supplier']) {
			$payment_info_select_sql = "SELECT sps.supplier_payments_status_name, sp.supplier_payments_date, sp.supplier_payments_total, sp.currency, sp.currency_value, s.supplier_firstname, s.supplier_lastname, s.supplier_email_address, s.supplier_gender 
										FROM " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
										INNER JOIN " . TABLE_SUPPLIER_PAYMENTS_STATUS . " AS sps 
											ON (sp.supplier_payments_status=sps.supplier_payments_status_id AND sps.language_id='".tep_db_input($languages_id)."')
										INNER JOIN " . TABLE_SUPPLIER . " AS s 
											ON sp.suppliers_id=s.supplier_id 
										WHERE sp.supplier_payments_id = '" . tep_db_input($this->payment_id) . "'";
			$payment_info_result_sql = tep_db_query($payment_info_select_sql);
			$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
			
			// Send email to supplier
			$email = 	EMAIL_TEXT_PAYMENT_TITLE . "\n\n" .
						EMAIL_TEXT_PAYMENT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_NUMBER, $this->payment_id) . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_AMOUNT, $currencies->format($payment_info_row['supplier_payments_total'], true, $payment_info_row['currency'], $payment_info_row['currency_value'])) . "\n" .
					 	sprintf(EMAIL_TEXT_PAYMENT_DATE, tep_date_long($payment_info_row["supplier_payments_date"])) . "\n\n" .
					 	sprintf(EMAIL_TEXT_STATUS_UPDATE, $payment_info_row['supplier_payments_status_name']) . "\n\n" . 
					 	sprintf(EMAIL_TEXT_PAYMENT_COMMENTS, $this->payment_info['remarks']) . "\n\n" .
					 	EMAIL_TEXT_CLOSING . "\n\n" .
					 	EMAIL_FOOTER;
			
			$email_greeting = tep_get_email_greeting($payment_info_row['supplier_firstname'], $payment_info_row['supplier_lastname'], $payment_info_row['supplier_gender']);
			
			$email = $email_greeting . $email;
			tep_mail($payment_info_row['supplier_firstname'].' '.$payment_info_row['supplier_lastname'], $payment_info_row['supplier_email_address'], sprintf(EMAIL_PAYMENT_UPDATE_SUBJECT, $this->payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
		
		$result = array('text' => sprintf(SUCCESS_UPDATE_PAYMENT, $this->payment_id), 'type' => 'success');
		
		return $result;
	}
	
	function get_order_paid_amount($order_id)
	{
		$paid_amount_select_sql = "	SELECT SUM(spo.supplier_payments_orders_paid_amount) AS total_paid 
									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
									INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
										ON (spo.supplier_payments_id=sp.supplier_payments_id AND sp.supplier_payments_status IN (1, 2)) 
									WHERE spo.supplier_order_lists_id = '" . tep_db_input($order_id) . "' ";
		$paid_amount_result_sql = tep_db_query($paid_amount_select_sql);
		$paid_amount_row = tep_db_fetch_array($paid_amount_result_sql);
		
		return $paid_amount_row["total_paid"];
	}
	
	function get_order_paid_history($order_id, $all=true)
	{
		$payment_history_array = array();
		$order_payments_select_sql = "	SELECT spo.supplier_payments_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sp.currency, sp.currency_value 
										FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
										INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
											ON (spo.supplier_payments_id=sp.supplier_payments_id) 
										WHERE spo.supplier_order_lists_id = '" . tep_db_input($order_id) . "' " . (!$all ? "AND sp.supplier_payments_status IN (1, 2) " : '') . " 
										ORDER BY sp.supplier_payments_date";
		$order_payments_result_sql = tep_db_query($order_payments_select_sql);
		
		while ($order_payments_row = tep_db_fetch_array($order_payments_result_sql)) {
			$payment_history_array[] = array(	'payment_id' => $order_payments_row['supplier_payments_id'],
												'paid_amount' => $order_payments_row['supplier_payments_orders_paid_amount'],
												'paid_currency' => $order_payments_row['currency'],
												'paid_currency_value' => $order_payments_row['currency_value'],
												'payment_type' => $order_payments_row['supplier_payments_type']
												);
		}
		
		return $payment_history_array;
	}
}
?>