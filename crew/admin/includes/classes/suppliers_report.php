<?php
/*
  	$Id: suppliers_report.php,v 1.9 2007/03/02 12:55:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

/****************************************************************************************
	Mode: 	1 - Total
			2 - Supplier Groups
			3 - Suppliers
			4 - Total By Source
****************************************************************************************/

class suppliers_report
{
	var $mode, $startDate, $endDate, $size, $sql_filter, $info, $report_stat, $subtitle, $order, $summary, $queryItemCnt, $totalSales;
	var $selStartDateStr, $selEndDateStr;
	var $parantChild;
	var $sort_by, $sort_order;
	var $cat_id, $grp_id_array, $order_status_array, $buyback_from_array;
	var $buyback_price_info_permission;
	
	function suppliers_report($mode, $startDate='', $endDate='', $date_formatted=0) {
		$this->mode = $mode;
		$this->sql_filter = '';
      	$this->info = array();
      	$this->report_stat = array();
      	$this->subtitle = array();
      	$this->order = array(array());
      	$this->summary = array();
      	$this->parantChild = array();	// define parent-child relationship, used for expand/collapse in category report
      	$this->totalSales = 0;
      	$this->prod_qty_type = array();	// for mode = 7
      	
      	$this->queryItemCnt = "SELECT o.orders_id, op.products_id AS pid, op.orders_products_id, op.products_name AS pname, SUM(op.products_quantity) AS prod_qty, SUM(op.final_price * op.products_quantity) AS prod_sum, op.products_tax AS prod_tax FROM " . TABLE_ORDERS . " AS o, " . TABLE_ORDERS_PRODUCTS . " AS op WHERE o.orders_id = op.orders_id";
      	
      	$this->buyback_price_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_REPORT, 'SUPPLIER_REPORT_BUYBACK_PRICE_INFO');
      	
      	if (!$date_formatted) {
            if ($startDate) {
			$start_date_array = explode("-", $startDate);
                        $start_date_stamp = mktime(0, 0, 0, $start_date_array["1"], $start_date_array["2"], $start_date_array["0"]);
            }
            
            if ($endDate) {
			$end_date_array = explode("-", $endDate);
			$end_date_stamp = mktime(0, 0, 0, $end_date_array["1"], $end_date_array["2"], $end_date_array["0"]);
            }
        } else {
                $start_date_stamp = $startDate;
                $end_date_stamp = $endDate;
        }
		
		switch ($this->mode) {
			case 1:	// Total
			case 2:	// Supplier Groups
			case 3:	// Suppliers
			case 4: // Total By Source
				if ($startDate) {
					$this->startDate = mktime(0, 0, 0, date("m", $start_date_stamp), date("d", $start_date_stamp), date("Y", $start_date_stamp));
				} else {
					$this->startDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
				}
				
				if ($endDate) {
					$this->endDate = mktime(0, 0, 0, date("m", $end_date_stamp), date("d", $end_date_stamp), date("Y", $end_date_stamp));
				} else {
					$this->endDate = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
				}
				
				$this->selStartDateStr = " DATE_FORMAT(o.date_purchased, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", $this->startDate)."','%Y-%m-%d') ";
				$this->selEndDateStr = " DATE_FORMAT(o.date_purchased, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", $this->endDate)."','%Y-%m-%d') ";
				
				$this->size = ($this->endDate - $this->startDate) / (24 * 60 * 60) + 1;
				
				for ($i = 0; $i < $this->size; $i++) {
            		$this->startDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + $i, date("Y", $this->startDate));
            		$this->endDates[$i] = mktime(0, 0, 0, date("m", $this->startDate), date("d", $this->startDate) + ($i + 1), date("Y", $this->startDate));
          		}
				break;
		}
	}
	
	function set_cat_id($cat_id) {
		$this->cat_id = $cat_id;
	}
	
	function set_grp_array($grp_array) {
		if (is_array($grp_array) && count($grp_array)) {
			$this->grp_id_array = $grp_array;
		} else {
			$this->grp_id_array = array();;
		}
	}
	
	function set_order_status_array($status_array) {
		if (is_array($status_array) && count($status_array)) {
			$this->order_status_array = $status_array;
		} else {
			$this->order_status_array = array();;
		}
	}
	
	function set_buyback_from_array($buyback_from_array) {
		if (is_array($buyback_from_array) && count($buyback_from_array)) {
			$this->buyback_from_array = $buyback_from_array;
		} else {
			$this->buyback_from_array = array();;
		}
	}
	
	function set_sorting_info($sort_by, $sort_order) {
		$this->sort_by = $sort_by;
		$this->sort_order = $sort_order;
	}
	
	function reportQuery() {
		global $languages_id;
		
		$category_array = tep_get_eligible_categories(FILENAME_SUPPLIERS_REPORT, $category_array, $this->cat_id, true);
		
		$total_amount_select_str = ($this->buyback_price_info_permission) ? ", SUM( (IF(solp2.products_received_quantity > solp2.first_max_quantity, IF(solp2.products_received_quantity > solp2.first_max_quantity+solp2.second_max_quantity, solp2.first_max_quantity*solp2.first_max_unit_price + solp2.second_max_quantity*solp2.second_max_unit_price, solp2.first_max_quantity*solp2.first_max_unit_price + (solp2.products_received_quantity-solp2.first_max_quantity)*solp2.second_max_unit_price), solp2.products_received_quantity*solp2.first_max_unit_price)) ) AS total_amount " : '';
		switch ($this->mode) {
			case 1:
				$products_select_sql = "SELECT p2c.categories_id, p.products_id, p.products_cat_path, pd.products_name 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p2c.products_id=pd.products_id 
										WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
				$products_result_sql = tep_db_query($products_select_sql);
				while ($products_row = tep_db_fetch_array($products_result_sql)) {
					$list_info = array();
					for ($i=0; $i < $this->size; $i++) {
						// Using LEFT JOIN since the product might not exists in both first and confirmation lists
			      		$date_range_where_sql = " sol.supplier_order_lists_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND sol.supplier_order_lists_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
			      		
			      		$supplier_orders_products_select_sql = "SELECT SUM(solp1.products_quantity) AS first_list_qty, AVG(solp1.first_max_unit_price) AS first_list_up, SUM(solp2.products_quantity) AS sec_list_qty, AVG(solp2.first_max_unit_price) AS sec_list_up, SUM(solp2.products_received_quantity) AS recv_qty " . $total_amount_select_str . " 
																FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
																LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp1 
																	ON (sol.supplier_order_lists_id=solp1.supplier_order_lists_id AND solp1.supplier_order_lists_type=1 AND solp1.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
																	ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2 AND solp2.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																WHERE $date_range_where_sql " . 
																	($this->sql_filter ? " AND " . $this->sql_filter : "");
						$supplier_orders_products_result_sql = tep_db_query($supplier_orders_products_select_sql);
						
						if ($supplier_orders_products_row = tep_db_fetch_array($supplier_orders_products_result_sql)) {
							$list_info[$this->startDates[$i]] = $supplier_orders_products_row;
						}
			        }
			        
			        $product_display_name = tep_display_category_path($products_row['products_cat_path']." > ".$products_row['products_name'], $products_row['categories_id'], 'catalog', false);
			        $this->info[] = array(	'id' => $products_row["products_id"],
      			   			 				'name' => $product_display_name,
				    	  		            'list_info' => $list_info
										 );
				}
				
				for ($i=0; $i < $this->size; $i++) {
		      		$date_range_where_sql = " sol.supplier_order_lists_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND sol.supplier_order_lists_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
		      		
		      		$server_status_stat_select_sql = "	SELECT solp2.products_purchase_status AS server_status, SUM(solp2.products_quantity) AS sec_list_qty, SUM(solp2.products_received_quantity) AS recv_qty, SUM(solp2.first_max_quantity + solp2.second_max_quantity) AS request_qty 
														FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
														INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
															ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2) 
														INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
															ON (solp2.products_id=p2c.products_id) 
														WHERE $date_range_where_sql AND p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 " . 
															($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
														GROUP BY solp2.products_purchase_status";
					$server_status_stat_result_sql = tep_db_query($server_status_stat_select_sql);
					while ($server_status_stat_row = tep_db_fetch_array($server_status_stat_result_sql)) {
						if (tep_not_null($server_status_stat_row["server_status"])) {
							$this->report_stat[$this->startDates[$i]][$server_status_stat_row["server_status"]] = $server_status_stat_row;
						} else {
							$this->report_stat[$this->startDates[$i]]['no_server_status'] = $server_status_stat_row;
						}
					}
		        }
		    	break;
		    case 2:
			case 3:
				if ($this->mode == 2) {
					$extra_group_by_field = ' sg.supplier_groups_id';
					$extra_group_by_field_result_name = 'supplier_groups_id';
					
					$extra_look_up_table = " INNER JOIN " . TABLE_SUPPLIER . " AS s ON sol.suppliers_id=s.supplier_id INNER JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg ON (s.supplier_groups_id=sg.supplier_groups_id AND sg.supplier_groups_id IN ('" . implode("', '", $this->grp_id_array) . "') )";
					
					$supplier_group_select_sql = "SELECT supplier_groups_id, supplier_groups_name FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id IN ('" . implode("', '", $this->grp_id_array) . "') ORDER BY supplier_groups_name";
					$supplier_group_result_sql = tep_db_query($supplier_group_select_sql);
					while ($supplier_group_row = tep_db_fetch_array($supplier_group_result_sql)) {
						$this->subtitle[] = array('id' => $supplier_group_row['supplier_groups_id'], 'text' => $supplier_group_row['supplier_groups_name'], 'alt' => $supplier_group_row['supplier_groups_name']);
					}
				} else {
					$extra_group_by_field = ' sol.suppliers_id';
					$extra_group_by_field_result_name = 'suppliers_id';
					
					$supplier_select_sql = "SELECT s.supplier_id, s.supplier_code, s.supplier_firstname, s.supplier_lastname 
											FROM " . TABLE_SUPPLIER . " AS s 
											INNER JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id=sg.supplier_groups_id 
											ORDER BY sg.supplier_groups_name, s.supplier_code";
					$supplier_result_sql = tep_db_query($supplier_select_sql);
					while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
						$this->subtitle[] = array('id' => $supplier_row['supplier_id'], 'text' => $supplier_row['supplier_code'], 'alt' => $supplier_row['supplier_firstname'] . ' ' . $supplier_row['supplier_lastname']);
					}
				}
				
				$products_select_sql = "SELECT p2c.categories_id, p.products_id, p.products_cat_path, pd.products_name 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p2c.products_id=pd.products_id 
										WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
				$products_result_sql = tep_db_query($products_select_sql);
				while ($products_row = tep_db_fetch_array($products_result_sql)) {
					$list_info = array();
					for ($i=0; $i < $this->size; $i++) {
						// Using LEFT JOIN since the product might not exists in both first and confirmation lists
			      		//$date_range_where_sql = " DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('" . date("Y-m-d H:i:s", $this->startDates[$i]) . "','%Y-%m-%d %H:%i:%s') AND DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') < DATE_FORMAT('" . date("Y-m-d H:i:s", $this->endDates[$i]) . "','%Y-%m-%d %H:%i:%s') ";
			      		$date_range_where_sql = " sol.supplier_order_lists_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND sol.supplier_order_lists_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
			      		
			      		$supplier_orders_products_select_sql = "SELECT SUM(solp1.products_quantity) AS first_list_qty, AVG(solp1.first_max_unit_price) AS first_list_up, SUM(solp2.products_quantity) AS sec_list_qty, AVG(solp2.first_max_unit_price) AS sec_list_up, SUM(solp2.products_received_quantity) AS recv_qty " . (tep_not_null($extra_group_by_field) ? ', ' . $extra_group_by_field : '' ) . $total_amount_select_str . " 
																FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol " . 
																$extra_look_up_table . " 
																LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp1 
																	ON (sol.supplier_order_lists_id=solp1.supplier_order_lists_id AND solp1.supplier_order_lists_type=1 AND solp1.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
																	ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2 AND solp2.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																WHERE $date_range_where_sql " .
																	($this->sql_filter ? " AND " . $this->sql_filter : "") . 
																(tep_not_null($extra_group_by_field) ? 'GROUP BY ' . $extra_group_by_field : '' );
						$supplier_orders_products_result_sql = tep_db_query($supplier_orders_products_select_sql);
						
						while ($supplier_orders_products_row = tep_db_fetch_array($supplier_orders_products_result_sql)) {
							$list_info[$this->startDates[$i]][$supplier_orders_products_row[$extra_group_by_field_result_name]] = $supplier_orders_products_row;
						}
			        }
			        
			        $product_display_name = tep_display_category_path($products_row['products_cat_path']." > ".$products_row['products_name'], $products_row['categories_id'], 'catalog', false);
			        $this->info[] = array(	'id' => $products_row["products_id"],
      			   			 				'name' => $product_display_name,
				    	  		            'list_info' => $list_info
										 );
				}
				
				for ($i=0; $i < $this->size; $i++) {
		      		//$date_range_where_sql = " DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('" . date("Y-m-d H:i:s", $this->startDates[$i]) . "','%Y-%m-%d %H:%i:%s') AND DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') < DATE_FORMAT('" . date("Y-m-d H:i:s", $this->endDates[$i]) . "','%Y-%m-%d %H:%i:%s') ";
		      		$date_range_where_sql = " sol.supplier_order_lists_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND sol.supplier_order_lists_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
		      		
		      		$server_status_stat_select_sql = "	SELECT solp2.products_purchase_status AS server_status, SUM(solp2.products_quantity) AS sec_list_qty, SUM(solp2.products_received_quantity) AS recv_qty, SUM(solp2.first_max_quantity + solp2.second_max_quantity) AS request_qty " . (tep_not_null($extra_group_by_field) ? ', ' . $extra_group_by_field : '' ) . " 
														FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol " . 
														$extra_look_up_table . " 
														INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
															ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2) 
														INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
															ON (solp2.products_id=p2c.products_id) 
														WHERE $date_range_where_sql AND p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 " . 
															($this->sql_filter ? " AND " . $this->sql_filter : "") . " 
														GROUP BY solp2.products_purchase_status" . (tep_not_null($extra_group_by_field) ? ', ' . $extra_group_by_field : '' );
					$server_status_stat_result_sql = tep_db_query($server_status_stat_select_sql);
					
					while ($server_status_stat_row = tep_db_fetch_array($server_status_stat_result_sql)) {
						if (tep_not_null($server_status_stat_row["server_status"])) {
							$this->report_stat[$this->startDates[$i]][$server_status_stat_row[$extra_group_by_field_result_name]][$server_status_stat_row["server_status"]] = $server_status_stat_row;
						} else {
							$this->report_stat[$this->startDates[$i]][$server_status_stat_row[$extra_group_by_field_result_name]]['no_server_status'] = $server_status_stat_row;
						}
					}
		        }
		        
		    	break;
			case 4:
				$products_select_sql = "SELECT p2c.categories_id, p.products_id, p.products_cat_path, pd.products_name 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p2c.products_id=pd.products_id 
										WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
				$products_result_sql = tep_db_query($products_select_sql);
				while ($products_row = tep_db_fetch_array($products_result_sql)) {
					$list_info = array();
					
					for ($i=0; $i < $this->size; $i++) {
						// Using LEFT JOIN since the product might not exists in both first and confirmation lists
			      		$total_send_qty = $total_recv_qty = $total_unit_price = $total_amount = '';
			      		$total_source_has_value = 0;
			      		
			      		if (in_array('sup', $this->buyback_from_array)) {
			      			$date_range_where_sql = " sol.supplier_order_lists_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND sol.supplier_order_lists_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
			      			
				      		$supplier_orders_products_select_sql = "SELECT SUM(solp2.products_quantity) AS send_qty, AVG(solp2.first_max_unit_price) AS unit_price, SUM(solp2.products_received_quantity) AS recv_qty " . $total_amount_select_str . " 
																	FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
																	LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
																		ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2 AND solp2.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																	WHERE $date_range_where_sql " . 
																		($this->sql_filter ? " AND " . $this->sql_filter : "");
							$supplier_orders_products_result_sql = tep_db_query($supplier_orders_products_select_sql);
							
							if ($supplier_orders_products_row = tep_db_fetch_array($supplier_orders_products_result_sql)) {
								$list_info[$this->startDates[$i]]['sup'] = $supplier_orders_products_row;
								
								if ($supplier_orders_products_row['recv_qty'] > 0)	$total_source_has_value++;
								$total_send_qty += $supplier_orders_products_row['send_qty'];
								$total_recv_qty += $supplier_orders_products_row['recv_qty'];
								$total_unit_price += $supplier_orders_products_row['unit_price'];
								$total_amount += $supplier_orders_products_row['total_amount'];
							}
						}
						
						if (in_array('us', $this->buyback_from_array)) {	// Website Buyback
							$date_range_where_sql = " brg.buyback_request_group_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND brg.buyback_request_group_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
							
							$buyback_total_amount_select_str = ($this->buyback_price_info_permission) ? ", SUM( (IF(br.buyback_quantity_received > br.buyback_request_quantity, br.buyback_unit_price*br.buyback_request_quantity, br.buyback_unit_price*br.buyback_quantity_received)) ) AS total_amount " : '';
							
				      		$website_buyback_products_select_sql = "SELECT SUM(br.buyback_request_quantity) AS send_qty, AVG(br.buyback_unit_price) AS unit_price, SUM(br.buyback_quantity_received) AS recv_qty " . $buyback_total_amount_select_str . " 
																	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																	LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																		ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																	WHERE buyback_request_group_site_id = 0 
																		AND $date_range_where_sql " . 
																		(count($this->order_status_array) ? " AND brg.buyback_status_id IN ('" . implode("', '", $this->order_status_array) . "') " : "");
							$website_buyback_products_result_sql = tep_db_query($website_buyback_products_select_sql);
							
							if ($website_buyback_products_row = tep_db_fetch_array($website_buyback_products_result_sql)) {
								$list_info[$this->startDates[$i]]['us'] = $website_buyback_products_row;
								
								if ($website_buyback_products_row['recv_qty'] > 0)	$total_source_has_value++;
								$total_send_qty += $website_buyback_products_row['send_qty'];
								$total_recv_qty += $website_buyback_products_row['recv_qty'];
								$total_unit_price += $website_buyback_products_row['unit_price'];
								$total_amount += $website_buyback_products_row['total_amount'];
							}
						}
						
						if (in_array('cn', $this->buyback_from_array)) {	// China Buyback
							$date_range_where_sql = " brg.buyback_request_group_date >= '" . date("Y-m-d H:i:s", $this->startDates[$i]) . "' AND brg.buyback_request_group_date < '" . date("Y-m-d H:i:s", $this->endDates[$i]) . "' ";
							
							$buyback_total_amount_select_str = ($this->buyback_price_info_permission) ? ", SUM( (IF(br.buyback_quantity_received > br.buyback_quantity_confirmed, br.buyback_unit_price*br.buyback_quantity_confirmed, br.buyback_unit_price*br.buyback_quantity_received)) ) AS total_amount " : '';
							
				      		$cn_buyback_products_select_sql = "	SELECT SUM(br.buyback_quantity_confirmed) AS send_qty, AVG(br.buyback_unit_price) AS unit_price, SUM(br.buyback_quantity_received) AS recv_qty " . $buyback_total_amount_select_str . " 
																FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																	ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id = '" . tep_db_input($products_row["products_id"]) . "') 
																WHERE buyback_request_group_site_id = 1 
																	AND $date_range_where_sql " . 
																	(count($this->order_status_array) ? " AND brg.buyback_status_id IN ('" . implode("', '", $this->order_status_array) . "') " : "");
							$cn_buyback_products_result_sql = tep_db_query($cn_buyback_products_select_sql);
							
							if ($cn_buyback_products_row = tep_db_fetch_array($cn_buyback_products_result_sql)) {
								$list_info[$this->startDates[$i]]['cn'] = $cn_buyback_products_row;
								
								if ($cn_buyback_products_row['recv_qty'] > 0)	$total_source_has_value++;
								$total_send_qty += $cn_buyback_products_row['send_qty'];
								$total_recv_qty += $cn_buyback_products_row['recv_qty'];
								$total_unit_price += $cn_buyback_products_row['unit_price'];
								$total_amount += $cn_buyback_products_row['total_amount'];
							}
						}
						
						$list_info[$this->startDates[$i]]['send_qty'] = $total_send_qty;
						$list_info[$this->startDates[$i]]['recv_qty'] = $total_recv_qty;
						$list_info[$this->startDates[$i]]['unit_price'] = $total_source_has_value > 0 ? ($total_unit_price / $total_source_has_value) : $total_unit_price;
						$list_info[$this->startDates[$i]]['total_amount'] = $total_amount;
			        }
			        
			        $product_display_name = tep_display_category_path($products_row['products_cat_path']." > ".$products_row['products_name'], $products_row['categories_id'], 'catalog', false);
			        $this->info[] = array(	'id' => $products_row["products_id"],
      			   			 				'name' => $product_display_name,
				    	  		            'list_info' => $list_info
										 );
				}
				
		    	break;
	    }
	}
	
	function getSummaryText() {
		$this->summary['legend'] = array(	'urgent' => 	array('name' => 'Urgent', 'colour' => '#DE2738'), 
											'important' => 	array('name' => 'Important', 'colour' => '#FFF4B4'), 
											'normal' => 	array('name' => 'Normal', 'colour' => '#52C903')
										);
		switch($this->mode) {
			case 1:
				$this->summary["mode_summary"] = "";
				$this->summary["parent_mode_summary"] = TOTAL_SALES;
				$this->summary["report_title"] = HEADING_TITLE . ' (Group By ' . REPORT_TYPE_TOTAL . ')';
				if ($this->startDate && $this->endDate) {
					$this->summary["date_heading"] = "From ".date("Y-m-d",$this->startDate)." to ".date("Y-m-d",$this->endDate);
				} else if ($this->startDate) {
					$this->summary["date_heading"] = "After ".date("Y-m-d",$this->startDate);
				} else if ($this->endDate) {
					$this->summary["date_heading"] = "Before ".date("Y-m-d",$this->endDate);
				}
				$this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
				if ($this->buyback_price_info_permission)		$this->summary["second_column_title"] = 'Avg. Buyback Price<br>(%s)';
				
				break;
			case 2:
				$this->summary["mode_summary"] = "";
				$this->summary["parent_mode_summary"] = TOTAL_SALES;
				$this->summary["report_title"] = HEADING_TITLE . ' (Group By ' . REPORT_TYPE_SUPPLIER_GROUPS . ')';
				if ($this->startDate && $this->endDate) {
					$this->summary["date_heading"] = "From ".date("Y-m-d",$this->startDate)." to ".date("Y-m-d",$this->endDate);
				} else if ($this->startDate) {
					$this->summary["date_heading"] = "After ".date("Y-m-d",$this->startDate);
				} else if ($this->endDate) {
					$this->summary["date_heading"] = "Before ".date("Y-m-d",$this->endDate);
				}
				$this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
				if ($this->buyback_price_info_permission)		$this->summary["second_column_title"] = 'Avg. Buyback Price<br>(%s)';
				
				break;
			case 3:
				$this->summary["mode_summary"] = "";
				$this->summary["parent_mode_summary"] = TOTAL_SALES;
				$this->summary["report_title"] = HEADING_TITLE . ' (Group By ' . REPORT_TYPE_SUPPLIERS . ')';
				if ($this->startDate && $this->endDate) {
					$this->summary["date_heading"] = "From ".date("Y-m-d",$this->startDate)." to ".date("Y-m-d",$this->endDate);
				} else if ($this->startDate) {
					$this->summary["date_heading"] = "After ".date("Y-m-d",$this->startDate);
				} else if ($this->endDate) {
					$this->summary["date_heading"] = "Before ".date("Y-m-d",$this->endDate);
				}
				$this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
				if ($this->buyback_price_info_permission)		$this->summary["second_column_title"] = 'Avg. Buyback Price<br>(%s)';
				
				break;
			case 4:
				$this->summary["mode_summary"] = "";
				$this->summary["parent_mode_summary"] = TOTAL_SALES;
				$this->summary["report_title"] = HEADING_TITLE . ' (Group By ' . REPORT_TYPE_TOTAL_BY_SOURCE . ')';
				if ($this->startDate && $this->endDate) {
					$this->summary["date_heading"] = "From ".date("Y-m-d",$this->startDate)." to ".date("Y-m-d",$this->endDate);
				} else if ($this->startDate) {
					$this->summary["date_heading"] = "After ".date("Y-m-d",$this->startDate);
				} else if ($this->endDate) {
					$this->summary["date_heading"] = "Before ".date("Y-m-d",$this->endDate);
				}
				$this->summary["first_column_title"] = TABLE_HEADING_CATEGORY;
				if ($this->buyback_price_info_permission)		$this->summary["second_column_title"] = 'Avg. Buyback Price<br>(%s)';
				
				break;
		}
	}
	
	function set_products_qty_type($qty_type_array) {
		if (is_array($qty_type_array) && count($qty_type_array)) {
			$this->prod_qty_type = $qty_type_array;
		}
	}
}
?>