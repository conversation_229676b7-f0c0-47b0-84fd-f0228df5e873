<?
class user_agent_detector {
	var $browser;
    var $os;
    var $useragent;	

    function user_agent_detector($pass_ua="") {
    	$this->useragent = $pass_ua;
        //$this->useragent = $this->set_useragent($pass_ua);
        $this->initialize();
    }

    function initialize() {
        $this->set_browser();
        $this->set_os();
    }

    function display($opentag = '', $closetag = '', $separator = ' ') {
        echo $opentag . 'Browser: ' . $this->browser . $separator . 'Operating System: ' . $this->os . $closetag;
    }

    function error() {
        echo 'We were unable to detect your browser and operating system';
    }

    function get_browser() {
        return $this->browser;
    }

    function get_os() {
        return $this->os;
    }

    function get_ua() {
        return $this->useragent;
    }
    
    function set_useragent($pass_ua="") {
    	/*
        $useragent = null;
 		if (trim($pass_ua)=="") {
	        if (isset($_SERVER['HTTP_USER_AGENT'])) {
	            $useragent = $_SERVER['HTTP_USER_AGENT'];
	        } else {
	            $useragent = getenv('HTTP_USER_AGENT');
	        }
 		} else {
 			$useragent = $pass_ua;
 		}
       	return !is_null($useragent) ? $useragent : false;*/
    }

    function set_browser() {
        $this->browser = $this->get_browser_name();
    }

	function get_browser_name() {
        $useragent = $this->useragent;

        if (strpos($useragent, 'MSIE') !== false && strpos($useragent, 'Opera') === false && strpos($useragent, 'Netscape') === false) {
            if (preg_match('/MSIE ([0-9]{1}\.[0-9]{1,2})/', $useragent, $matches)) {
                return 'Internet Explorer ' . $matches[1];
            }
        } elseif (strpos($useragent, 'Gecko')) {
            if (preg_match('/Firefox\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Mozilla Firefox ' . $matches[1];
            }

            if (preg_match('/Netscape\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Netscape ' . $matches[1];
            }
 
            if (preg_match('/Safari\/([0-9]{2,3}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Safari ' . $matches[1];
            }
 
            if (preg_match('/Galeon\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Galeon ' . $matches[1];
            }
 
            if (preg_match('/Konqueror\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Konqueror ' . $matches[1];
            }       
            return 'Gecko based';                   
        } elseif (strpos($useragent, 'Opera') !== false) {
            if (preg_match('/Opera[\/ ]([0-9]{1}\.[0-9]{1}([0-9])?)/', $useragent, $matches)) {
                return 'Opera ' . $matches[1];
            }
        } elseif (strpos($useragent, 'Lynx') !== false) {
            if (preg_match('/Lynx\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Lynx ' . $matches[1];
            }
        } elseif (strpos($useragent, 'Netscape') !== false) {
            if (preg_match('/Netscape\/([0-9]{1}\.[0-9]{1}(\.[0-9])?)/', $useragent, $matches)) {
                return 'Netscape ' . $matches[1];
            }
        } else {
            return false;
        }
    }

    function set_os() {
        $this->os = $this->get_os_name();
    }

    function get_os_name() {       
        $useragent = strtolower($this->useragent);

        if (strpos($useragent, 'windows nt 5.1') !== false) {
            return 'Windows XP';           
        } elseif (strpos($useragent, 'windows 98') !== false) {
            return 'Windows 98';
        } elseif (strpos($useragent, 'windows nt 5.0') !== false) {
            return 'Windows 2000';
        } elseif (strpos($useragent, 'windows nt 5.2') !== false) {
            return 'Windows 2003 server';
        } elseif (strpos($useragent, 'windows nt 6.0') !== false) {
            return 'Windows Vista';
        } elseif (strpos($useragent, 'windows nt') !== false) {
            return 'Windows NT';
        } elseif (strpos($useragent, 'win 9x 4.90') !== false && strpos($useragent, 'win me')) {
            return 'Windows ME';
        } elseif (strpos($useragent, 'win ce') !== false) {
            return 'Windows CE';
        } elseif (strpos($useragent, 'win 9x 4.90') !== false) {
            return 'Windows ME';
        } elseif (strpos($useragent, 'android') !== false) {
            return 'Android';
        } elseif (strpos($useragent, 'iphone') !== false) {
            return 'Iphone';
        } elseif (strpos($useragent, 'mac os x') !== false) {
            return 'Mac OS X';
        } elseif (strpos($useragent, 'macintosh') !== false) {
            return 'Macintosh';
        } elseif (strpos($useragent, 'linux') !== false) {
            return 'Linux';
        } elseif (strpos($useragent, 'freebsd') !== false) {
            return 'Free BSD';
        } elseif (strpos($useragent, 'webos') !== false) {
            return 'Web OS';
        } elseif (strpos($useragent, 'symbian') !== false) {
            return 'Symbian';
        } else {
            return false;
        }
    }
}
?>